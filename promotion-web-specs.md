# Promotion Web API Specifications

## Project Overview

This document specifies the promotion web system for a new project stack migration:
- **Current Stack**: Node.js/GORM (ORM)
- **Target Stack**: Go + Gin + SQLx + PostgreSQL

The promotion web system is a comprehensive promotion management platform with web interface and advanced features including user tracking, return systems, and credit locking mechanisms.

## Architecture Overview

### Tech Stack
- **Language**: Go 1.21+
- **HTTP Framework**: Gin
- **Database**: PostgreSQL 15+
- **Database Library**: SQLx (raw SQL with struct scanning)
- **Authentication**: JWT tokens
- **File Storage**: AWS S3 or local storage
- **Validation**: Go Playground validator
- **Documentation**: Swagger/OpenAPI

### Database Schema

The promotion web system uses the following core tables:

#### Core Tables
```sql
-- Promotion web types
CREATE TABLE promotion_web_type (
    id BIGSERIAL PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    label_th VARCHAR(255),
    label_en VARCHAR(255)
);

-- Promotion web statuses
CREATE TABLE promotion_web_status (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    label_th VARCHAR(255),
    label_en VARCHAR(255)
);

-- Main promotion web table
CREATE TABLE promotion_web (
    id BIGSERIAL PRIMARY KEY,
    promotion_web_type_id BIGINT REFERENCES promotion_web_type(id),
    promotion_web_status_id BIGINT REFERENCES promotion_web_status(id),
    condition_detail TEXT,
    image_url VARCHAR(255),
    name VARCHAR(255) NOT NULL,
    short_description TEXT,
    description TEXT,
    promotion_web_date_type_id BIGINT,
    start_date TIMESTAMP,
    end_date TIMESTAMP,
    free_bonus_amount DECIMAL(15,2) DEFAULT 0,
    privilege_per_day BIGINT DEFAULT 0,
    able_withdraw_morethan DECIMAL(15,2) DEFAULT 0,
    promotion_web_bonus_condition_id BIGINT,
    bonus_condition_amount DECIMAL(15,2) DEFAULT 0,
    promotion_web_bonus_type_id BIGINT,
    bonus_type_amount DECIMAL(15,2) DEFAULT 0,
    bonus_type_amount_max DECIMAL(15,2) DEFAULT 0,
    able_withdraw_pertime DECIMAL(15,2) DEFAULT 0,
    promotion_web_turnover_type_id BIGINT,
    turnover_amount DECIMAL(15,2) DEFAULT 0,
    monday BOOLEAN DEFAULT FALSE,
    tuesday BOOLEAN DEFAULT FALSE,
    wednesday BOOLEAN DEFAULT FALSE,
    thursday BOOLEAN DEFAULT FALSE,
    friday BOOLEAN DEFAULT FALSE,
    saturday BOOLEAN DEFAULT FALSE,
    sunday BOOLEAN DEFAULT FALSE,
    time_start TIME,
    time_end TIME,
    hidden_url_link VARCHAR(255),
    priority_order INT DEFAULT 0,
    created_by_admin_id BIGINT,
    updated_by_admin_id BIGINT,
    canceled_by_admin_id BIGINT,
    deleted_by_admin_id BIGINT,
    canceled_at TIMESTAMP,
    deleted_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- User promotion web tracking
CREATE TABLE promotion_web_user (
    id BIGSERIAL PRIMARY KEY,
    promotion_web_id BIGINT REFERENCES promotion_web(id),
    user_id BIGINT REFERENCES users(id),
    promotion_web_user_status_id BIGINT,
    total_amount DECIMAL(15,2) DEFAULT 0,
    total_deposit_amount DECIMAL(15,2) DEFAULT 0,
    canceled_by_admin_id BIGINT,
    approve_credit_by_admin_id BIGINT,
    approve_credit_at TIMESTAMP,
    canceled_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

#### Supporting Tables
```sql
-- Bonus conditions
CREATE TABLE promotion_web_bonus_condition (
    id BIGSERIAL PRIMARY KEY,
    syntax VARCHAR(10), -- '>=' or '<='
    name VARCHAR(255),
    label_th VARCHAR(255),
    label_en VARCHAR(255)
);

-- Bonus types
CREATE TABLE promotion_web_bonus_type (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255), -- 'PERCENT' or 'FIXED_RATE'
    label_th VARCHAR(255),
    label_en VARCHAR(255)
);

-- Turnover types
CREATE TABLE promotion_web_turnover_type (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255), -- 'ALL', 'SPORT', 'CASINO', 'SLOT', etc.
    label_th VARCHAR(255),
    label_en VARCHAR(255)
);

-- Lock credit system
CREATE TABLE lock_credit_promotion (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id),
    promotion_id BIGINT REFERENCES promotion_web(id),
    promotion_web_user_id BIGINT REFERENCES promotion_web_user(id),
    bonus_amount DECIMAL(15,2) NOT NULL,
    is_locked BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Withdraw lock tracking
CREATE TABLE lock_credit_withdraw (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id),
    ref_id BIGINT,
    detail TEXT,
    user_withdraw_lock_credit_type_id BIGINT,
    credit_more_than DECIMAL(15,2),
    allow_withdraw_amount DECIMAL(15,2),
    withdraw_amount DECIMAL(15,2),
    pull_credit_amount DECIMAL(15,2),
    is_locked BOOLEAN DEFAULT TRUE,
    is_pull_credit BOOLEAN DEFAULT FALSE,
    approved_by_id BIGINT,
    approved_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

## API Endpoints

### Admin Authentication System

**Note**: In the new project, there is **no separate admin table**. Instead, users are assigned admin roles through a role-based system:

```sql
-- Users table with role system
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255),
    role VARCHAR(50) DEFAULT 'USER', -- 'USER', 'ADMIN', 'SUPER_ADMIN'
    -- other user fields...
);

-- Role permissions
CREATE TABLE user_roles (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id),
    role_name VARCHAR(255), -- 'promotion', 'promotion_edit', 'promotion_view', etc.
    granted_by_user_id BIGINT REFERENCES users(id),
    created_at TIMESTAMP DEFAULT NOW()
);
```

### Authentication Middleware

```go
// JWT middleware checks user role
func AuthorizeAdmin(c *gin.Context) {
    // Extract JWT token
    // Verify user has admin role or specific permissions
    // Set adminId and permissions in context
}

func CheckPermission(permissions []string) gin.HandlerFunc {
    return func(c *gin.Context) {
        // Check if user has required permissions
    }
}
```

### Core API Routes

#### 1. Promotion Management (Admin)

**Base Path**: `/api/v1/promotion-web`

```go
// GET /promotion-web/list - Get promotion list with pagination
type GetPromotionListRequest struct {
    Page                 int    `form:"page,default=1"`
    Limit                int    `form:"limit,default=10"`
    StartDate            string `form:"startDate" time_format:"2006-01-02"`
    EndDate              string `form:"endDate" time_format:"2006-01-02"`
    Search               string `form:"search"`
    PromotionWebStatusID *int64 `form:"promotionWebStatusId"`
}

type GetPromotionListResponse struct {
    ID                     int64     `json:"id"`
    PromotionWebTypeID     int64     `json:"promotionWebTypeId"`
    PromotionWebTypeTh     string    `json:"promotionWebTypeTh"`
    PromotionWebStatusID   int64     `json:"promotionWebStatusId"`
    PromotionWebStatusTh   string    `json:"promotionWebStatusTh"`
    Name                   string    `json:"name"`
    StartDate              string    `json:"startDate"`
    EndDate                string    `json:"endDate"`
    TimeStart              string    `json:"timeStart"`
    TimeEnd                string    `json:"timeEnd"`
    CreatedByAdminID       int64     `json:"createdByAdminId"`
    CreatedByAdminName     string    `json:"createdByAdminName"`
    UpdatedByAdminID       *int64    `json:"updatedByAdminId"`
    UpdatedByAdminName     string    `json:"updatedByAdminName"`
    HiddenURLLink          string    `json:"hiddenUrlLink"`
    UpdatedAt              time.Time `json:"updatedAt"`
}

// POST /promotion-web - Create promotion
type CreatePromotionRequest struct {
    PromotionWebTypeID           int64   `json:"promotionWebTypeId" binding:"required"`
    PromotionWebStatusID         int64   `json:"promotionWebStatusId" binding:"required"`
    ConditionDetail              string  `json:"conditionDetail"`
    ImageURL                     string  `json:"imageUrl"`
    Name                         string  `json:"name" binding:"required"`
    ShortDescription             string  `json:"shortDescription" binding:"required"`
    Description                  string  `json:"description" binding:"required"`
    PromotionWebDateTypeID       int64   `json:"promotionWebDateTypeId" binding:"required"`
    StartDate                    *string `json:"startDate"`
    EndDate                      *string `json:"endDate"`
    FreeBonusAmount              float64 `json:"freeBonusAmount"`
    PrivilegePerDay              int64   `json:"privilegePerDay"`
    AbleWithdrawMorethan         float64 `json:"ableWithdrawMorethan"`
    PromotionWebBonusConditionID *int64  `json:"promotionWebBonusConditionId"`
    BonusConditionAmount         float64 `json:"bonusConditionAmount"`
    PromotionWebBonusTypeID      *int64  `json:"promotionWebBonusTypeId"`
    BonusTypeAmount              float64 `json:"bonusTypeAmount"`
    BonusTypeAmountMax           float64 `json:"bonusTypeAmountMax"`
    AbleWithdrawPertime          float64 `json:"ableWithdrawPertime"`
    PromotionWebTurnoverTypeID   *int64  `json:"promotionWebTurnoverTypeId"`
    TurnoverAmount               float64 `json:"turnoverAmount"`
    Monday                       bool    `json:"monday"`
    Tuesday                      bool    `json:"tuesday"`
    Wednesday                    bool    `json:"wednesday"`
    Thursday                     bool    `json:"thursday"`
    Friday                       bool    `json:"friday"`
    Saturday                     bool    `json:"saturday"`
    Sunday                       bool    `json:"sunday"`
    TimeStart                    *string `json:"timeStart"`
    TimeEnd                      *string `json:"timeEnd"`
    HiddenURLLink                string  `json:"hiddenUrlLink"`
}

// GET /promotion-web/{id} - Get promotion by ID
type GetPromotionResponse struct {
    ID                               int64   `json:"id"`
    PromotionWebTypeID               int64   `json:"promotionWebTypeId"`
    PromotionWebTypeTh               string  `json:"promotionWebTypeTh"`
    PromotionWebStatusID             int64   `json:"promotionWebStatusId"`
    PromotionWebStatusTh             string  `json:"promotionWebStatusTh"`
    ConditionDetail                  string  `json:"conditionDetail"`
    ImageURL                         string  `json:"imageUrl"`
    Name                             string  `json:"name"`
    ShortDescription                 string  `json:"shortDescription"`
    Description                      string  `json:"description"`
    PromotionWebDateTypeID           int64   `json:"promotionWebDateTypeId"`
    StartDate                        string  `json:"startDate"`
    EndDate                          string  `json:"endDate"`
    FreeBonusAmount                  float64 `json:"freeBonusAmount"`
    PrivilegePerDay                  int64   `json:"privilegePerDay"`
    AbleWithdrawMorethan             float64 `json:"ableWithdrawMorethan"`
    PromotionWebBonusConditionID     int64   `json:"promotionWebBonusConditionId"`
    PromotionWebBonusConditionTh     string  `json:"promotionWebBonusConditionTh"`
    PromotionWebBonusConditionSyntax string  `json:"promotionWebBonusConditionSyntax"`
    BonusConditionAmount             float64 `json:"bonusConditionAmount"`
    PromotionWebBonusTypeID          int64   `json:"promotionWebBonusTypeId"`
    PromotionWebBonusTypeTh          string  `json:"promotionWebBonusTypeTh"`
    BonusTypeAmount                  float64 `json:"bonusTypeAmount"`
    BonusTypeAmountMax               float64 `json:"bonusTypeAmountMax"`
    AbleWithdrawPertime              float64 `json:"ableWithdrawPertime"`
    PromotionWebTurnoverTypeID       int64   `json:"promotionWebTurnoverTypeId"`
    PromotionWebTurnoverTypeTh       string  `json:"promotionWebTurnoverTypeTh"`
    TurnoverAmount                   float64 `json:"turnoverAmount"`
    Monday                           bool    `json:"monday"`
    Tuesday                          bool    `json:"tuesday"`
    Wednesday                        bool    `json:"wednesday"`
    Thursday                         bool    `json:"thursday"`
    Friday                           bool    `json:"friday"`
    Saturday                         bool    `json:"saturday"`
    Sunday                           bool    `json:"sunday"`
    TimeStart                        string  `json:"timeStart"`
    TimeEnd                          string  `json:"timeEnd"`
}

// PUT /promotion-web/{id} - Update promotion
// PUT /promotion-web/cancel/{id} - Cancel promotion
// DELETE /promotion-web/{id} - Delete promotion
// PUT /promotion-web/sort-priority-order - Sort promotions
// POST /promotion-web/upload/cover - Upload cover image
```

#### 2. Promotion Options (Admin)

```go
// GET /promotion-web/option/type - Get promotion types
type PromotionWebTypeResponse struct {
    ID      int64  `json:"id"`
    Name    string `json:"name"`
    LabelTh string `json:"labelTh"`
    LabelEn string `json:"labelEn"`
}

// GET /promotion-web/option/status - Get promotion statuses
// GET /promotion-web/option/bonus-condition - Get bonus conditions
// GET /promotion-web/option/bonus-type - Get bonus types
// GET /promotion-web/option/turnover-type - Get turnover types
// GET /promotion-web/option/date-type - Get date types
// GET /promotion-web/option/user-status - Get user status options
```

#### 3. User Promotion Management (Admin)

```go
// GET /promotion-web/user/list - Get user promotion list
type GetUserPromotionListRequest struct {
    PromotionWebID           *int64 `form:"promotionWebId"`
    Page                     int    `form:"page,default=1"`
    Limit                    int    `form:"limit,default=10"`
    StartDate                string `form:"startDate" time_format:"2006-01-02"`
    EndDate                  string `form:"endDate" time_format:"2006-01-02"`
    PromotionWebUserStatusID *int64 `form:"promotionWebUserStatusId"`
    Search                   string `form:"search"`
    TypeList                 string `form:"typeList"` // "CANCELED" or "OTHER"
}

type GetUserPromotionListResponse struct {
    ID                       int64      `json:"id"`
    PromotionWebID           int64      `json:"promotionWebId"`
    PromotionName            string     `json:"promotionName"`
    UserID                   int64      `json:"userId"`
    MemberCode               string     `json:"memberCode"`
    FullName                 string     `json:"fullName"`
    Phone                    string     `json:"phone"`
    PromotionWebUserStatusID int64      `json:"promotionWebUserStatusId"`
    PromotionWebUserStatusTh string     `json:"promotionWebUserStatusTh"`
    TotalAmount              float64    `json:"totalAmount"`
    IsLocked                 bool       `json:"isLocked"`
    AbleWithdrawPertime      float64    `json:"ableWithdrawPertime"`
    AbleWithdrawMorethan     float64    `json:"ableWithdrawMorethan"`
    CreatedAt                time.Time  `json:"createdAt"`
    CanceledByAdminID        *int64     `json:"canceledByAdminId"`
    CanceledByAdminName      *string    `json:"canceledByAdminName"`
    CanceledAt               *time.Time `json:"canceledAt"`
}

// GET /promotion-web/user/{userId} - Get user promotions by user ID
// PUT /promotion-web/user/cancel/{id} - Cancel user promotion
// GET /promotion-web/user/list-collected - Get collected promotions by user
// GET /promotion-web/user/turnover-win-lose-summary/{id} - Get user win/lose summary
```

#### 4. Public/User API

```go
// GET /promotion-web/public/show - Get public promotions
type ShowPromotionForUserResponse struct {
    ID                      int64  `json:"id"`
    PromotionWebTypeID      int64  `json:"promotionWebTypeId"`
    PromotionWebTypeTh      string `json:"promotionWebTypeTh"`
    PromotionWebTypeEn      string `json:"promotionWebTypeEn"`
    PromotionWebStatusID    int64  `json:"promotionWebStatusId"`
    ConditonDetail          string `json:"conditonDetail"`
    ImageURL                string `json:"imageUrl"`
    Name                    string `json:"name"`
    ShortDescription        string `json:"shortDescription"`
    Description             string `json:"description"`
    PromotionWebDateTypeID  int64  `json:"promotionWebDateTypeId"`
    StartDate               string `json:"startDate"`
    EndDate                 string `json:"endDate"`
    Monday                  bool   `json:"monday"`
    Tuesday                 bool   `json:"tuesday"`
    Wednesday               bool   `json:"wednesday"`
    Thursday                bool   `json:"thursday"`
    Friday                  bool   `json:"friday"`
    Saturday                bool   `json:"saturday"`
    Sunday                  bool   `json:"sunday"`
    TimeStart               string `json:"timeStart"`
    TimeEnd                 string `json:"timeEnd"`
    HiddenURLLink           string `json:"hiddenUrlLink"`
    UserStatusWithPromotion string `json:"userStatusWithPromotion"` // "NOT_AVAILABLE", "AVAILABLE", "ON_PROCESS"
}

// GET /promotion-web/public/show/{id} - Get public promotion by ID
// POST /promotion-web/user - Collect promotion (authenticated users)
// GET /promotion-web/user/show - Get promotions for authenticated user
// GET /promotion-web/user/show/{id} - Get specific promotion for user
// GET /promotion-web/user/locked-credit - Check locked credit status
// GET /promotion-web/user/link/{hiddenUrlLink} - Access promotion via hidden link
```

#### 5. Lock Credit System

```go
// GET /lock-credit/withdraw-list - Get locked credit withdraw list (Admin)
type GetLockCreditWithdrawListRequest struct {
    UserID    int64  `form:"userId"`
    Search    string `form:"search"`
    StartDate string `form:"startDate" time_format:"2006-01-02"`
    EndDate   string `form:"endDate" time_format:"2006-01-02"`
    Page      int    `form:"page,default=1"`
    Limit     int    `form:"limit,default=10"`
}

type GetLockCreditWithdrawListResponse struct {
    ID                           int64      `json:"id"`
    UserID                       int64      `json:"userId"`
    MemberCode                   string     `json:"memberCode"`
    Fullname                     string     `json:"fullname"`
    Phone                        string     `json:"phone"`
    RefID                        int64      `json:"refId"`
    Detail                       string     `json:"detail"`
    UserWithdrawLockCreditTypeID int64      `json:"userWithdrawLockCreditTypeId"`
    UserWithdrawLockCreditTypeTh string     `json:"userWithdrawLockCreditTypeTh"`
    CreditMoreThan               float64    `json:"creditMoreThan"`
    AllowWithdrawAmount          float64    `json:"allowWithdrawAmount"`
    WithdrawAmount               float64    `json:"withdrawAmount"`
    PullCreditAmount             float64    `json:"pullCreditAmount"`
    IsLocked                     bool       `json:"isLocked"`
    IsPullCredit                 bool       `json:"isPullCredit"`
    CreatedAt                    time.Time  `json:"createdAt"`
    ApprovedAt                   *time.Time `json:"approvedAt"`
    ApprovedByID                 *int64     `json:"approvedById"`
    ApprovedByName               *string    `json:"apporvedByName"`
}

// PUT /lock-credit/withdraw-unlock/{id} - Unlock credit withdraw
// PUT /promotion-web/unlock-user-credit - Unlock user credit (Admin)
// GET /user/lock-credit/withdraw-check - Check locked credit for user
```

#### 6. Reports & Analytics

```go
// GET /promotion-web/summary - Get promotion summary
type PromotionSummaryRequest struct {
    StartDate string `form:"startDate" time_format:"2006-01-02"`
    EndDate   string `form:"endDate" time_format:"2006-01-02"`
}

type PromotionSummaryResponse struct {
    TotalBonusAmount float64 `json:"totalBonusAmount"`
}

// GET /promotion-web/user/summary - Get user promotion summary
type UserPromotionSummaryRequest struct {
    PromotionWebID *int64 `form:"promotionWebId"`
    StartDate      string `form:"startDate" time_format:"2006-01-02"`
    EndDate        string `form:"endDate" time_format:"2006-01-02"`
}

type UserPromotionSummaryResponse struct {
    TotalBonusAmount  float64 `json:"totalBonusAmount"`
    TotalCreditAmount float64 `json:"totalCreditAmount"`
}

// GET /promotion-web/history-report - Get promotion history report
// GET /promotion-web/slide-list - Get active promotion slides
```

## Business Logic Constants

### Promotion Types
```go
const (
    PROMOTION_WEB_TYPE_NEW_MEMBER_FREE         = 1 // New member free bonus
    PROMOTION_WEB_TYPE_NEW_MEMBER_CONDITION    = 2 // New member with condition
    PROMOTION_WEB_TYPE_DEPOSIT_MINIMUM_PER_DAY = 3 // Minimum deposit per day
    PROMOTION_WEB_TYPE_FIRST_DEPOSIT           = 4 // First deposit bonus
    PROMOTION_WEB_TYPE_DEPOSIT_PER_DAY         = 5 // Deposit per day
    PROMOTION_WEB_TYPE_DEPOSIT_BY_TIME         = 6 // Deposit by time period
    PROMOTION_WEB_TYPE_FIRST_DEPOSIT_OF_DAY    = 7 // First deposit of day
)
```

### Promotion Status
```go
const (
    PROMOTION_WEB_STATUS_DISABLE_WEB = 1 // Disabled
    PROMOTION_WEB_STATUS_ACTIVE      = 2 // Active
    PROMOTION_WEB_STATUS_CANCELED    = 3 // Canceled
    PROMOTION_WEB_STATUS_ONLY_SHOW   = 4 // Show only (not claimable)
    PROMOTION_WEB_STATUS_ONLY_URL    = 5 // URL only access
)
```

### User Status
```go
const (
    PROMOTION_WEB_USER_STATUS_ON_PROCESS  = 1 // Processing
    PROMOTION_WEB_USER_STATUS_SUCCESS     = 2 // Completed
    PROMOTION_WEB_USER_STATUS_CANCELED    = 3 // Canceled
    PROMOTION_WEB_USER_STATUS_ON_WITHDRAW = 4 // Withdrawing
)
```

### Bonus Types
```go
const (
    PROMOTION_WEB_BONUS_TYPE_PERCENT    = 1 // Percentage bonus
    PROMOTION_WEB_BONUS_TYPE_FIXED_RATE = 2 // Fixed amount bonus
)
```

### Turnover Types
```go
const (
    PROMOTION_WEB_TURN_OVER_TYPE_ALL       = 1 // All games
    PROMOTION_WEB_TURN_OVER_TYPE_SPORT     = 2 // Sports betting
    PROMOTION_WEB_TURN_OVER_TYPE_CASINO    = 3 // Casino games
    PROMOTION_WEB_TURN_OVER_TYPE_SLOT      = 4 // Slot games
    PROMOTION_WEB_TURN_OVER_TYPE_LOTTERY   = 6 // Lottery
    // Note: P2P (5) and FINANCIAL (7) are excluded due to credit management issues
)
```

## Service Layer Architecture

### Repository Pattern with SQLx

```go
type PromotionWebRepository interface {
    // Basic CRUD
    GetPromotionList(ctx context.Context, req GetPromotionListRequest) ([]GetPromotionListResponse, int64, error)
    CreatePromotion(ctx context.Context, req CreatePromotionRequest) (int64, error)
    GetPromotionByID(ctx context.Context, id int64) (*GetPromotionResponse, error)
    UpdatePromotion(ctx context.Context, req UpdatePromotionRequest) error
    DeletePromotion(ctx context.Context, id int64, adminID int64) error
    
    // User promotions
    GetUserPromotionList(ctx context.Context, req GetUserPromotionListRequest) ([]GetUserPromotionListResponse, int64, error)
    CreateUserPromotion(ctx context.Context, userID, promotionID int64) (int64, error)
    CancelUserPromotion(ctx context.Context, id int64, adminID int64) error
    
    // Options
    GetPromotionTypes(ctx context.Context) ([]PromotionWebTypeResponse, error)
    GetPromotionStatuses(ctx context.Context) ([]PromotionWebStatusResponse, error)
    
    // Lock credit system
    CreateLockCredit(ctx context.Context, req LockCreditPromotionCreateRequest) (int64, error)
    UpdateLockCredit(ctx context.Context, req LockCreditPromotionUpdateRequest) error
    CheckLockedCredit(ctx context.Context, userID int64) (bool, error)
}

// Implementation with SQLx
type promotionWebRepository struct {
    db *sqlx.DB
}

func (r *promotionWebRepository) GetPromotionList(ctx context.Context, req GetPromotionListRequest) ([]GetPromotionListResponse, int64, error) {
    query := `
        SELECT pw.id, pw.name, pw.start_date, pw.end_date,
               pwt.label_th as promotion_web_type_th,
               pws.label_th as promotion_web_status_th,
               u.username as created_by_admin_name
        FROM promotion_web pw
        LEFT JOIN promotion_web_type pwt ON pw.promotion_web_type_id = pwt.id
        LEFT JOIN promotion_web_status pws ON pw.promotion_web_status_id = pws.id
        LEFT JOIN users u ON pw.created_by_admin_id = u.id
        WHERE pw.deleted_at IS NULL
    `
    
    args := []interface{}{}
    if req.Search != "" {
        query += " AND pw.name ILIKE $" + strconv.Itoa(len(args)+1)
        args = append(args, "%"+req.Search+"%")
    }
    
    if req.PromotionWebStatusID != nil {
        query += " AND pw.promotion_web_status_id = $" + strconv.Itoa(len(args)+1)
        args = append(args, *req.PromotionWebStatusID)
    }
    
    // Count total
    countQuery := "SELECT COUNT(*) FROM (" + query + ") as count_query"
    var total int64
    err := r.db.GetContext(ctx, &total, countQuery, args...)
    if err != nil {
        return nil, 0, err
    }
    
    // Add pagination
    query += " ORDER BY pw.created_at DESC LIMIT $" + strconv.Itoa(len(args)+1) + " OFFSET $" + strconv.Itoa(len(args)+2)
    args = append(args, req.Limit, (req.Page-1)*req.Limit)
    
    var promotions []GetPromotionListResponse
    err = r.db.SelectContext(ctx, &promotions, query, args...)
    if err != nil {
        return nil, 0, err
    }
    
    return promotions, total, nil
}
```

### Service Layer

```go
type PromotionWebService interface {
    GetPromotionList(ctx context.Context, req GetPromotionListRequest) (*PaginatedResponse, error)
    CreatePromotion(ctx context.Context, req CreatePromotionRequest, adminID int64) (int64, error)
    UpdatePromotion(ctx context.Context, req UpdatePromotionRequest, adminID int64) error
    
    // User operations
    CollectPromotion(ctx context.Context, userID, promotionID int64) (int64, error)
    GetUserPromotions(ctx context.Context, userID int64) ([]ShowPromotionForUserResponse, error)
    
    // Validation and business logic
    ValidatePromotionEligibility(ctx context.Context, userID, promotionID int64) error
    ProcessPromotionBonus(ctx context.Context, userPromotionID int64) error
    CheckTurnoverRequirements(ctx context.Context, userID int64, promotionID int64) (bool, error)
}

type promotionWebService struct {
    repo PromotionWebRepository
    db   *sqlx.DB
}

func (s *promotionWebService) ValidatePromotionEligibility(ctx context.Context, userID, promotionID int64) error {
    // Check if user already has this promotion
    // Check if promotion is active and within time limits
    // Check if user meets requirements (first deposit, etc.)
    // Check daily limits
    // Validate against promotion conditions
    return nil
}
```

## Handler Layer (Gin Controllers)

```go
type PromotionWebHandler struct {
    service PromotionWebService
}

// @Summary Get promotion list
// @Description Get paginated list of promotions for admin
// @Tags Promotion Web - Admin
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Param search query string false "Search term"
// @Success 200 {object} PaginatedResponse
// @Failure 400 {object} ErrorResponse
// @Router /promotion-web/list [get]
func (h *PromotionWebHandler) GetPromotionList(c *gin.Context) {
    var req GetPromotionListRequest
    if err := c.ShouldBindQuery(&req); err != nil {
        c.JSON(400, ErrorResponse{Message: err.Error()})
        return
    }
    
    adminID := c.GetInt64("adminId") // From JWT middleware
    
    result, err := h.service.GetPromotionList(c.Request.Context(), req)
    if err != nil {
        c.JSON(500, ErrorResponse{Message: err.Error()})
        return
    }
    
    c.JSON(200, result)
}

// @Summary Create promotion
// @Description Create new promotion (admin only)
// @Tags Promotion Web - Admin
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param request body CreatePromotionRequest true "Promotion details"
// @Success 200 {object} map[string]int64
// @Failure 400 {object} ErrorResponse
// @Router /promotion-web [post]
func (h *PromotionWebHandler) CreatePromotion(c *gin.Context) {
    var req CreatePromotionRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(400, ErrorResponse{Message: err.Error()})
        return
    }
    
    adminID := c.GetInt64("adminId")
    
    id, err := h.service.CreatePromotion(c.Request.Context(), req, adminID)
    if err != nil {
        c.JSON(500, ErrorResponse{Message: err.Error()})
        return
    }
    
    c.JSON(200, map[string]int64{"id": id})
}

// User endpoints
func (h *PromotionWebHandler) CollectPromotion(c *gin.Context) {
    var req struct {
        PromotionWebID int64 `json:"promotionWebId" binding:"required"`
    }
    
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(400, ErrorResponse{Message: err.Error()})
        return
    }
    
    userID := c.GetInt64("userId") // From JWT middleware
    
    id, err := h.service.CollectPromotion(c.Request.Context(), userID, req.PromotionWebID)
    if err != nil {
        c.JSON(500, ErrorResponse{Message: err.Error()})
        return
    }
    
    c.JSON(200, map[string]int64{"id": id})
}
```

## Router Configuration

```go
func SetupPromotionWebRoutes(r *gin.Engine, db *sqlx.DB, handler *PromotionWebHandler) {
    api := r.Group("/api/v1")
    
    // Admin routes (require admin role)
    admin := api.Group("/promotion-web", AuthorizeAdmin, CheckPermission("promotion"))
    {
        admin.GET("/list", CheckPermission("promotion_view"), handler.GetPromotionList)
        admin.POST("", CheckPermission("promotion_create"), handler.CreatePromotion)
        admin.GET("/:id", CheckPermission("promotion_view"), handler.GetPromotionByID)
        admin.PUT("/:id", CheckPermission("promotion_edit"), handler.UpdatePromotion)
        admin.DELETE("/:id", CheckPermission("promotion_delete"), handler.DeletePromotion)
        admin.PUT("/cancel/:id", CheckPermission("promotion_cancel"), handler.CancelPromotion)
        
        // Options
        options := admin.Group("/option")
        {
            options.GET("/type", handler.GetPromotionTypes)
            options.GET("/status", handler.GetPromotionStatuses)
            options.GET("/bonus-condition", handler.GetBonusConditions)
            options.GET("/bonus-type", handler.GetBonusTypes)
            options.GET("/turnover-type", handler.GetTurnoverTypes)
        }
        
        // User management
        userMgmt := admin.Group("/user")
        {
            userMgmt.GET("/list", CheckPermission("promotion_view"), handler.GetUserPromotionList)
            userMgmt.GET("/:userId", CheckPermission("promotion_view"), handler.GetUserPromotionsByUserID)
            userMgmt.PUT("/cancel/:id", CheckPermission("promotion_cancel"), handler.CancelUserPromotion)
        }
        
        // File upload
        admin.POST("/upload/cover", handler.UploadPromotionCover)
    }
    
    // User routes (authenticated users)
    user := api.Group("/promotion-web/user", AuthorizeUser)
    {
        user.POST("", handler.CollectPromotion)
        user.GET("/show", handler.GetUserPromotions)
        user.GET("/show/:id", handler.GetUserPromotionByID)
        user.GET("/locked-credit", handler.CheckLockedCredit)
        user.GET("/link/:hiddenUrlLink", handler.GetPromotionByHiddenLink)
    }
    
    // Public routes (no authentication)
    public := api.Group("/promotion-web/public")
    {
        public.GET("/show", handler.GetPublicPromotions)
        public.GET("/show/:id", handler.GetPublicPromotionByID)
    }
    
    // Lock credit management
    lockCredit := api.Group("/lock-credit", AuthorizeAdmin)
    {
        lockCredit.GET("/withdraw-list", CheckPermission("promotion_view"), handler.GetLockCreditWithdrawList)
        lockCredit.PUT("/withdraw-unlock/:id", CheckPermission("promotion_approve"), handler.UnlockCreditWithdraw)
    }
    
    userLockCredit := api.Group("/user/lock-credit", AuthorizeUser)
    {
        userLockCredit.GET("/withdraw-check", handler.CheckLockCreditWithdraw)
    }
}
```

## Migration Strategy

### Database Migration

1. **Create promotion web tables in PostgreSQL**
2. **Migrate data from current system** (if applicable)
3. **Create indexes for performance**:
   ```sql
   CREATE INDEX idx_promotion_web_status ON promotion_web(promotion_web_status_id);
   CREATE INDEX idx_promotion_web_type ON promotion_web(promotion_web_type_id);
   CREATE INDEX idx_promotion_web_date_range ON promotion_web(start_date, end_date);
   CREATE INDEX idx_promotion_web_user_user_id ON promotion_web_user(user_id);
   CREATE INDEX idx_promotion_web_user_promotion_id ON promotion_web_user(promotion_web_id);
   CREATE INDEX idx_lock_credit_user_id ON lock_credit_promotion(user_id);
   ```

### API Migration

1. **Implement repository layer with SQLx**
2. **Create service layer with business logic**
3. **Build Gin handlers with proper validation**
4. **Set up middleware for authentication and permissions**
5. **Add comprehensive error handling**
6. **Implement file upload for promotion covers**

### Testing Strategy

1. **Unit tests for service layer business logic**
2. **Integration tests for repository layer**
3. **API tests for all endpoints**
4. **Load testing for high-traffic endpoints**

## Key Features

### 1. Comprehensive Promotion Management
- Multiple promotion types (new member, deposit bonuses, time-based)
- Flexible bonus calculations (percentage/fixed)
- Date and time-based scheduling
- Hidden URL access for special promotions

### 2. User Tracking & Management
- Track user promotion collection
- Monitor promotion status lifecycle
- Lock credit system for bonus management
- Withdrawal restrictions and approvals

### 3. Advanced Business Logic
- Eligibility validation based on user history
- Turnover requirements tracking
- Automatic promotion expiration
- Bonus condition validation

### 4. Admin Controls
- Role-based permission system
- Comprehensive reporting and analytics
- Bulk operations for user promotions
- Audit trail for all admin actions

### 5. API Design
- RESTful endpoints
- Comprehensive request/response models
- Swagger documentation
- Proper error handling and validation

This specification provides a complete blueprint for implementing the promotion web system in the new Go/Gin/SQLx/PostgreSQL stack while maintaining compatibility with the existing business logic and user experience.