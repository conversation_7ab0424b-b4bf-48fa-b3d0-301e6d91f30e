package main

import (
	docs "cybergame-api/docs"
	handler "cybergame-api/handler"
	"cybergame-api/middleware"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	handlerv2 "cybergame-api/handler/v2"
	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
	swaggerfiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// @title CyberGame API
// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization

func main() {

	if err := godotenv.Load(); err != nil {
		log.Println("Load the ENV locally instead.")
	}

	// rdb := connectRedis()

	initTimeZone()
	db := initDatabase()

	gin.SetMode(os.Getenv("GIN_MODE"))
	r := gin.Default()

	// Register the middleware
	r.Use(middleware.CORSMiddleware())

	path := "/api"
	docs.SwaggerInfo.BasePath = path

	apiRoute := r.Group(path)
	// Middleware to set default headers
	apiRoute.Use(func(c *gin.Context) {
		c.Writer.Header().Set("Api-Version", "cybergame-api-250609a")
		// check env API_VERSION exists
		if version, exists := os.LookupEnv("VERSION"); exists {
			c.Writer.Header().Set("Env-Version", version)
		}
		c.Next()
	})
	apiRoute.GET("/ping", func(c *gin.Context) {
		pingExample(c)
	})
	// Register the handler
	handler.AuthController(apiRoute, db)
	handler.GormController(apiRoute, db)
	handler.AdminController(apiRoute, db)
	handler.PermissionController(apiRoute, db)
	handler.GroupController(apiRoute, db)
	handler.AccountingController(apiRoute, db)
	handler.UserController(apiRoute, db)
	handler.BankingController(apiRoute, db)
	handler.ScammerController(apiRoute, db)
	handler.RecommendController(apiRoute, db)
	handler.MenuController(apiRoute, db)
	handler.TransactionController(apiRoute, db)
	handler.FileController(apiRoute, db)
	handler.AffiliateController(apiRoute, db)
	handler.CronController(apiRoute, db)
	handler.AccountMoveController(apiRoute, db)
	handler.PromotionReturnController(apiRoute, db)
	handler.WebController(apiRoute, db)
	handler.AllianceController(apiRoute, db)
	handler.TurnoverController(apiRoute, db)
	handler.AdminActionController(apiRoute, db)
	handler.IssueReportController(apiRoute, db)
	handler.AccountingReportController(apiRoute, db)
	handler.PromotionWebController(apiRoute, db)
	handler.UserCreditController(apiRoute, db)
	handler.MigratorController(apiRoute, db)
	handler.MarketingController(apiRoute, db)
	handler.ActivityDailyController(apiRoute, db)
	handler.RenewalWebController(apiRoute, db)
	handler.WebPopupController(apiRoute, db)
	handler.CouponCashController(apiRoute, db)
	handler.TelegramController(apiRoute, db)
	handler.PromotionReturnTurnController(apiRoute, db)
	handler.AgentController(apiRoute, db)
	// handler.LineNotifyController(apiRoute, db)
	// handler.SettingwebController(apiRoute, db)
	handler.NotificationController(apiRoute, db)
	handler.ConfigurationController(apiRoute, db)
	handler.ActivityLuckyWheelController(apiRoute, db)
	handler.DownlineController(apiRoute, db)
	handler.AllianceWhiteController(apiRoute, db)
	handler.PaymentGatewayController(apiRoute, db)
	handler.GameOrderController(apiRoute, db)
	handler.EditSectionController(apiRoute, db)
	handler.CategoryGameSettingController(apiRoute, db)
	handler.SendSmsController(apiRoute, db)
	handler.UserTierController(apiRoute, db)
	handler.ActivityDailyV2Controller(apiRoute, db)
	handler.PgHardController(apiRoute, db)
	handler.AgentCtwController(apiRoute, db)
	handler.AgentCblottoController(apiRoute, db)
	handler.UserSalepageController(apiRoute, db)
	handler.DashboardController(apiRoute, db)

	handlerv2.QuestController(apiRoute, db)
	handlerv2.DailyQuestController(apiRoute, db)
	handlerv2.UserQuestPlayLogController(apiRoute, db)
	handlerv2.ActivityDailyAutoCheckinV2Controller(apiRoute, db)
	handlerv2.RankingV2Controller(apiRoute, db)

	frontPath := "/api/v1"
	frontRoute := r.Group(frontPath)
	handler.GameController(frontRoute, db)

	// Secure the route
	swaggerPath := os.Getenv("SECURE_SWAGGER_PATH")
	if swaggerPath != "" {
		r.GET(fmt.Sprintf("/%s/*any", swaggerPath), ginSwagger.WrapHandler(swaggerfiles.Handler))
	} else if strings.ToLower(swaggerPath) != "disabled" {
		r.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerfiles.Handler))
	}

	port := fmt.Sprintf(":%s", os.Getenv("PORT"))
	err := r.Run(port)
	if err != nil {
		panic(err)
	}
}

type ping struct {
	Message string `json:"message" example:"pong" `
}

// @BasePath /ping
// @Summary ping example
// @Schemes
// @Description do ping
// @Tags Test
// @Accept json
// @Produce json
// @Success 200 {object} ping
// @Router /ping [get]
func pingExample(c *gin.Context) {
	c.JSON(200, ping{Message: "pong"})
}

func initTimeZone() {

	ict, err := time.LoadLocation(os.Getenv("TZ"))
	if err != nil {
		panic(err)
	}

	time.Local = ict

	println("Time now", time.Now().Format("2006-01-02 15:04:05"))
}

func initDatabase() *gorm.DB {

	dsn := fmt.Sprintf("%v:%v@tcp(%v:%v)/%v?parseTime=true",
		os.Getenv("DB_USER"),
		os.Getenv("DB_PASS"),
		os.Getenv("DB_HOST"),
		os.Getenv("DB_PORT"),
		os.Getenv("DB_NAME"),
	)

	ginMode := os.Getenv("GIN_MODE")

	if ginMode == "release" {
		db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
			Logger: logger.Default.LogMode(logger.Silent),
		})
		if err != nil {
			panic(err)
		}

		println("Database is connected release mode")
		return db
	} else {
		db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
			Logger: logger.Default.LogMode(logger.Info),
			// NamingStrategy: schema.NamingStrategy{
			// 	SingularTable: true,
			// },
		})
		if err != nil {
			panic(err)
		}
		println("Database is connected dev mode")
		return db
	}
}

// func connectRedis() *redis.Client {

// 	toStr, err := strconv.Atoi(os.Getenv("REDIS_DB"))
// 	if err != nil {
// 		panic(err)
// 	}

// 	return redis.NewClient(&redis.Options{
// 		Addr:     os.Getenv("REDIS_HOST"),
// 		Password: os.Getenv("REDIS_PASS"),
// 		DB:       toStr,
// 	})
// }
