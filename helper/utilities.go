package helper

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/md5"
	"encoding/base64"
	"encoding/gob"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"log"
	"math/rand"
	"regexp"
	"strconv"
	"strings"
)

func StripAllButAlphaNumeric(str string) string {
	var nonAlphanumericRegex = regexp.MustCompile(`[^a-zA-Z0-9 ]+`)
	return nonAlphanumericRegex.ReplaceAllString(str, "")
}
func StripAllButNumbers(str string) string {
	var nonAlphanumericRegex = regexp.MustCompile(`[^0-9]+`)
	return nonAlphanumericRegex.ReplaceAllString(str, "")
}

func StructJson(data interface{}) string {
	jsonString, err := json.Marshal(data)
	if err != nil {
		return ""
	}
	return string(jsonString)
}

func EndsWith(str string, suffix string) bool {
	return len(str) >= len(suffix) && str[len(str)-len(suffix):] == suffix
}

func RandStrings(length int) string {
	const letterBytes = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
	b := make([]byte, length)
	for i := range b {
		b[i] = letterBytes[rand.Intn(len(letterBytes))]
	}
	return string(b)
}

func Md5Compare(a, b []byte) bool {
	a = append(a, b...)
	c := 0
	for _, x := range a {
		c ^= int(x)
	}
	return c == 0
}

type S struct {
	K1 string
	K2 int
}

func Md5Hash(s []S) []byte {
	var b bytes.Buffer
	// Error return value of `(*encoding/gob.Encoder).Encode` is not checked (errcheck)
	err := gob.NewEncoder(&b).Encode(s)
	if err != nil {
		return nil
	}
	return b.Bytes()
}

func GetMD5Hash(text string) string {
	hash := md5.Sum([]byte(text))
	return hex.EncodeToString(hash[:])
}

func EncryptAES(key string, plaintext string) string {

	// Encrypt the overall data (AES): Encryption involves parameters:
	// method(encryption method): AES-128-CBC aesKey (encryption key): needs to be given by the developer
	// iv (initialization direction, fixed value): mYxiZZhwtvkYAxOV
	keyByte := []byte(key)

	c, err := aes.NewCipher(keyByte)
	if err != nil {
		return ""
	}

	out := make([]byte, len(plaintext))

	c.Encrypt(out, []byte(plaintext))

	return hex.EncodeToString(out)
}

func EncryptAES128CBC(aesKey, plaintext, ivHex string) (string, error) {

	// Create a 16-byte key using SHA-256 (use the first 16 bytes)
	// hash := sha256.Sum256([]byte(key))
	// aesKey := hash[:16]

	block, err := aes.NewCipher([]byte(aesKey))
	if err != nil {
		log.Println("Error creating AES cipher:", err)
		return "", err
	}

	plaintextBytes := []byte(plaintext)

	// Padding the plaintext to be a multiple of the block size
	padding := aes.BlockSize - len(plaintextBytes)%aes.BlockSize
	padtext := plaintextBytes
	for i := 0; i < padding; i++ {
		padtext = append(padtext, byte(padding))
	}

	// Decode the IV from hex
	// iv, err := hex.DecodeString(ivHex)
	// if err != nil {
	// 	log.Println("Error decoding IV:", err)
	// 	return "", err
	// }
	iv := []byte(ivHex)

	// fmt.Println("aes.BlockSize", aes.BlockSize)
	// fmt.Println("len(iv)", len(iv))
	// fmt.Println("len(plaintextBytes)", len(plaintextBytes))
	// fmt.Println("len(padtext)", len(padtext))

	if len(iv) != aes.BlockSize {
		return "", fmt.Errorf("IV must be %d bytes long", aes.BlockSize)
	}

	// Encrypt using AES-128-CBC
	ciphertext := make([]byte, len(padtext))
	mode := cipher.NewCBCEncrypter(block, iv)
	mode.CryptBlocks(ciphertext, padtext)

	// return hex.EncodeToString(ciphertext), nil
	ciphertextBase64 := base64.StdEncoding.EncodeToString(ciphertext)
	return ciphertextBase64, nil
}

func DecryptAES128CBC(aesKey, ciphertextBase64, ivHex string) (string, error) {

	// Create a 16-byte key using SHA-256 (use the first 16 bytes)
	// hash := sha256.Sum256([]byte(key))
	// aesKey := hash[:16]

	block, err := aes.NewCipher([]byte(aesKey))
	if err != nil {
		log.Println("Error creating AES cipher:", err)
		return "", err
	}

	// ciphertext, err := hex.DecodeString(ciphertextHex)
	ciphertext, err := base64.StdEncoding.DecodeString(ciphertextBase64)
	if err != nil {
		log.Println("Error decoding ciphertext:", err)
		return "", err
	}

	// iv, err := hex.DecodeString(ivHex)
	// if err != nil {
	// 	return "", err
	// }
	iv := []byte(ivHex)

	if len(ciphertext)%aes.BlockSize != 0 {
		return "", fmt.Errorf("ciphertext is not a multiple of the block size")
	}

	mode := cipher.NewCBCDecrypter(block, iv)
	plaintext := make([]byte, len(ciphertext))
	mode.CryptBlocks(plaintext, ciphertext)

	// Unpad plaintext
	padding := plaintext[len(plaintext)-1]
	if int(padding) > len(plaintext) || int(padding) > aes.BlockSize {
		return "", fmt.Errorf("invalid padding")
	}
	plaintext = plaintext[:len(plaintext)-int(padding)]

	return string(plaintext), nil
}

func SplitToInt64(str string, delimit string) []int64 {
	tmp := strings.Split(str, delimit)
	values := make([]int64, 0, len(tmp))
	for _, raw := range tmp {
		v, err := strconv.ParseInt(raw, 10, 64)
		if err != nil {
			// log.Print(err)
			continue
		}
		values = append(values, v)
	}
	return (values)
}

func MapIdsToInt64Array(m map[int64]int64) []int64 {
	var a []int64
	for _, v := range m {
		a = append(a, v)
	}
	return a
}

func MapIdsToStringArray(m map[string]string) []string {
	var a []string
	for _, v := range m {
		a = append(a, v)
	}
	return a
}

func StringInArray(str string, arr []string) bool {
	for _, v := range arr {
		if v == str {
			return true
		}
	}
	return false
}
