package helper

import (
	"encoding/base64"
	"fmt"
	"strings"
	"unicode"
)

func Encode(val string) string {
	r1 := encode(val)
	r2 := encode(r1)
	return r2
}

func Decode(val string) (*string, error) {

	r1, err := decode(val)
	if err != nil {
		return nil, err
	}

	r2, err := decode(r1)
	if err != nil {
		return nil, err
	}

	return &r2, nil
}

func encode(val string) string {

	origin := base64.StdEncoding.EncodeToString([]byte(val))
	count := strings.Count(origin, "=")
	origin = strings.Replace(origin, "=", "", -1)
	round := strings.Map(func(r rune) rune {
		if unicode.IsUpper(r) {
			return unicode.ToLower(r)
		}
		return unicode.ToUpper(r)
	}, origin)

	if count == 1 {
		return fmt.Sprintf("%sv", round)
	} else if count == 2 {
		return fmt.Sprintf("%si", round)
	} else {
		return fmt.Sprintf("%sq", round)
	}
}

func decode(val string) (string, error) {

	last := val[len(val)-1:]
	val = val[:len(val)-1]

	text := strings.Map(func(r rune) rune {
		if unicode.IsLower(r) {
			return unicode.ToUpper(r)
		}
		return unicode.ToLower(r)
	}, val)

	if last == "v" {
		text = fmt.Sprintf("%s=", text)
	}
	if last == "i" {
		text = fmt.Sprintf("%s==", text)
	}

	origin, err := base64.StdEncoding.DecodeString(text)
	if err != nil {
		return "", err
	}

	return string(origin), nil
}
