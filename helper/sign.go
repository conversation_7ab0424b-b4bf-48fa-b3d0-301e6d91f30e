package helper

import (
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"strings"
	"time"
)

func CreateSign(agent_key string, data string, timeNow time.Time) string {

	timeStamp := fmt.Sprintf("%d", timeNow.Unix())
	apiKey := agent_key

	word := strings.ToLower(data) + timeStamp + strings.ToLower(apiKey)
	hasher := sha256.New()

	if _, err := hasher.Write([]byte(word)); err != nil {
		return ""
	}

	hash := hasher.Sum(nil)

	return hex.EncodeToString(hash)
}

func HashSHA256(word string) string {

	hasher := sha256.New()
	if _, err := hasher.Write([]byte(word)); err != nil {
		return ""
	}
	hash := hasher.Sum(nil)
	return hex.EncodeToString(hash)
}
