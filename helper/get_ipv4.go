package helper

import (
	"math/big"
	"net"
)

func GetIPv4(ip string) string {

	if ip == "::1" || ip == "127.0.0.1" {
		return "127.0.0.1"
	}

	ipv6Bytes := net.ParseIP(ip).To16()
	ipv6BigInt := big.NewInt(0)

	for _, byteValue := range ipv6Bytes {
		ipv6BigInt.SetBytes([]byte{byteValue})
	}

	ipv4BigInt := big.NewInt(int64(ipv6Bytes[12])<<24 | int64(ipv6Bytes[13])<<16 | int64(ipv6Bytes[14])<<8 | int64(ipv6Bytes[15]))
	ipv4Bytes := ipv4BigInt.Bytes()

	if len(ipv4Bytes) < 4 {
		ipv4Bytes = append(make([]byte, 4-len(ipv4Bytes)), ipv4Bytes...)
	}

	ipv4Address := net.IPv4(ipv4Bytes[0], ipv4Bytes[1], ipv4Bytes[2], ipv4Bytes[3]).String()

	return ipv4Address
}
