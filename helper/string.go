package helper

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"
)

func FindString(search string, text string) bool {

	re := regexp.MustCompile(search)
	match := re.FindString(text)

	if match != "" {
		return true
	} else {
		return false
	}
}

var matchFirstCap = regexp.MustCompile("(.)([A-Z][a-z]+)")
var matchAllCap = regexp.MustCompile("([a-z0-9])([A-Z])")

func ToSnakeCase(str string) string {
	snake := matchFirstCap.ReplaceAllString(str, "${1}_${2}")
	snake = matchAllCap.ReplaceAllString(snake, "${1}_${2}")
	return strings.ToLower(snake)
}

func ToSnake(str string) string {
	snake := matchFirstCap.ReplaceAllString(str, "${1}_${2}")
	snake = matchAllCap.ReplaceAllString(snake, "${1}_${2}")
	snake = strings.ReplaceAll(snake, " ", "_")
	snake = strings.ReplaceAll(snake, "__", "_")
	return strings.ToLower(snake)
}

func Format(n int64) string {
	in := strconv.FormatInt(n, 10)
	numOfDigits := len(in)
	if n < 0 {
		numOfDigits-- // First character is the - sign (not a digit)
	}
	numOfCommas := (numOfDigits - 1) / 3

	out := make([]byte, len(in)+numOfCommas)
	if n < 0 {
		in, out[0] = in[1:], '-'
	}

	for i, j, k := len(in)-1, len(out)-1, 0; ; i, j = i-1, j-1 {
		out[j] = in[i]
		if i == 0 {
			return string(out)
		}
		if k++; k == 3 {
			j, k = j-1, 0
			out[j] = ','
		}
	}
}

func CensorPhone(phone string) string {

	// test string len - to len 10
	// fmt.Println("0", helper.CensorPhone(""))
	// fmt.Println("1", helper.CensorPhone("1"))
	// fmt.Println("2", helper.CensorPhone("12"))
	// fmt.Println("3", helper.CensorPhone("123"))
	// fmt.Println("4", helper.CensorPhone("1234"))
	// fmt.Println("5", helper.CensorPhone("12345"))
	// fmt.Println("6", helper.CensorPhone("123456"))
	// fmt.Println("7", helper.CensorPhone("1234567"))
	// fmt.Println("8", helper.CensorPhone("12345678"))
	// fmt.Println("9", helper.CensorPhone("123456789"))
	// fmt.Println("10", helper.CensorPhone("1234567890"))
	// fmt.Println("11", helper.CensorPhone("12345678901"))

	// censor 088xxx0000
	if len(phone) <= 4 {
		// return all digit with xxxx prefix until 10 digit
		return "080xxx" + fmt.Sprintf("%04s", phone)
	}
	return phone[:3] + "xxx" + phone[len(phone)-4:]
}
