package helper

import "strings"

func IsAgentmobile(useragent string) bool {

	if len(useragent) == 0 {
		return false
	}

	// the list below is taken from
	// https://github.com/bcit-ci/CodeIgniter/blob/develop/system/libraries/User_agent.php
	mobiles := []string{
		"Mobile Explorer", "Palm", "Motorola", "Nokia", "Palm", "Apple iPhone", "iPad", "Apple iPod Touch", "Sony Ericsson", "Sony Ericsson", "BlackBerry", "O2 Cocoon", "Treo", "LG", "Amoi", "XDA", "MDA", "Vario", "HTC", "Samsung",
		"Sharp", "Siemens", "Alcatel", "BenQ", "HP iPaq", "Motorola", "PlayStation Portable", "PlayStation 3", "PlayStation Vita", "Danger Hiptop", "NEC", "Panasonic", "Philips", "Sagem", "Sanyo", "SPV", "ZTE", "Sendo",
		"Nintendo DSi", "Nintendo DS", "Nintendo 3DS", "Nintendo Wii", "Open Web", "OpenWeb", "Android", "Symbian", "SymbianOS", "Palm", "Symbian S60", "Windows CE", "Obigo", "Netfront Browser", "Openwave Browser",
		"Mobile Explorer", "Opera Mini", "Opera Mobile", "Firefox Mobile", "Digital Paths", "AvantGo", "Xiino", "Novarra Transcoder", "Vodafone", "NTT DoCoMo", "O2", "mobile", "wireless", "j2me", "midp", "cldc",
		"up.link", "up.browser", "smartphone", "cellphone", "Generic Mobile", "iPhone",
	}

	for _, device := range mobiles {
		if strings.Contains(useragent, device) {
			return true
		}
	}
	return false
}

func GetAgentBrowser(useragent string) string {

	var result string
	// Windows Swaggers
	// INPUT : Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/118.0.2088.69
	// Client Mac Chrome
	// Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
	// Client Mac Safari
	// Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15
	// Iphone
	// Mozilla/5.0 (iPhone; CPU iPhone OS 17_0_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0.1 Mobile/15E148 Safari/604.1
	if len(useragent) > 0 {
		agentList := make(map[string]string)
		for _, agent := range strings.Split(useragent, " ") {
			if strings.Contains(agent, "Edg/") {
				agentList["Edg"] = agent
			} else if strings.Contains(agent, "Chrome/") {
				agentList["Chrome"] = agent
			} else if strings.Contains(agent, "Firefox/") {
				agentList["Firefox"] = agent
			} else if strings.Contains(agent, "Safari/") {
				agentList["Safari"] = agent
			}
		}
		// How to get real browser name from user agent?
		// Piority: Edg > Chrome > Firefox > Safari
		if len(agentList) > 0 {
			if _, ok := agentList["Edg"]; ok {
				result = agentList["Edg"]
			} else if _, ok := agentList["Chrome"]; ok {
				result = agentList["Chrome"]
			} else if _, ok := agentList["Firefox"]; ok {
				result = agentList["Firefox"]
			} else if _, ok := agentList["Safari"]; ok {
				result = agentList["Safari"]
			} else {
				result = "Unknown"
			}
		}
	}

	return result
}
