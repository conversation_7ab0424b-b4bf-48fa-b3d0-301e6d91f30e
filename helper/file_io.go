package helper

import (
	"io"
	"os"
)

func ReadJsonFile(path string) ([]byte, error) {

	jsonData, _ := os.Open(path)

	byteValue, err := io.ReadAll(jsonData)
	if err != nil {
		return nil, err
	}

	// var gameList []model.GameDetailBng
	// if err := json.Unmarshal(byteValue, &gameList); err != nil {
	// 	return "", err
	// }

	// var response []model.GameDetail

	// for i := 0; i < len(gameList); i++ {
	// 	response = append(response, model.GameDetail{
	// 		GameName: gameList[i].GameName,
	// 		GameCode: gameList[i].GameCode,
	// 		ImageUrl: gameList[i].ImageIcon,
	// 	})
	// }

	return byteValue, nil
}
