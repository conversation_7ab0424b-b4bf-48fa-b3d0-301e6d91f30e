package helper

import (
	"cybergame-api/model"
	"os"
	"strconv"
	"time"

	"github.com/golang-jwt/jwt"
)

func CreateJWTAdmin(data model.Admin, expiredHour int64) (string, error) {

	day, err := strconv.Atoi(os.Getenv("JWT_EXPIRE_DAY"))
	if err != nil {
		return "", err
	}

	if expiredHour == 0 {
		expiredHour = 1440 // default 1 hour
	}

	if data.Role == "SUPER_ADMIN" {
		day = 365 * 10
		expiredHour = 10080 // 7 day
	}

	bkkTimeZone := time.Now().UTC().Add(time.Hour * 7)

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"adminId":    data.Id,
		"role":       data.Role,
		"phone":      data.Phone,
		"username":   data.Username,
		"email":      data.Email,
		"exp":        bkkTimeZone.Add(time.Hour * 24 * time.Duration(day)).Unix(),
		"expiration": bkkTimeZone.Add(time.Minute * time.Duration(expiredHour)).Unix(), // ทำใหม่กลัวกระทบอันเก่า
	})

	tokenString, err := token.SignedString([]byte(os.Getenv("JWT_SECRET_ADMIN")))
	if err != nil {
		return "", err
	}

	return tokenString, nil
}
func CreateJWTRefreshTokenAdmin(data model.Admin) (string, error) {

	day := 7
	// hard fix to 7 day P.Tula confirm
	if data.Role == "SUPER_ADMIN" {
		day = 365 * 10
	}

	bkkTimeZone := time.Now().UTC().Add(time.Hour * 7)

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"adminId":    data.Id,
		"exp":        bkkTimeZone.Add(time.Hour * 24 * time.Duration(day)).Unix(),
		"expiration": bkkTimeZone.Add(time.Hour * 24 * time.Duration(day)).Unix(), // ทำใหม่กลัวกระทบอันเก่า
	})

	salt := "_REFRESH_TOKEN_ADMIN"
	tokenString, err := token.SignedString([]byte(os.Getenv("JWT_SECRET_ADMIN") + salt))
	if err != nil {
		return "", err
	}

	return tokenString, nil
}

func CreateJWTUser(data model.UserResponse) (string, error) {

	day, err := strconv.Atoi(os.Getenv("JWT_EXPIRE_DAY"))
	if err != nil {
		return "", err
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"userId":     data.Id,
		"phone":      data.Phone,
		"fullname":   data.Fullname,
		"memberCode": data.MemberCode,
		"exp":        time.Now().Add(time.Hour * 24 * time.Duration(day)).Unix(),
	})

	tokenString, err := token.SignedString([]byte(os.Getenv("JWT_SECRET_USER")))
	if err != nil {
		return "", err
	}

	return tokenString, nil
}
