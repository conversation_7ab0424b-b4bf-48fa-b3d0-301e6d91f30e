package helper

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"errors"
	"fmt"
	"io"
	"os"
)

func deriveKey(operatorID string) []byte {
	hash := sha256.Sum256([]byte(operatorID))
	return hash[:]
}

// 4bHD2yHvx5fxaXkUo4/CgDjKo2z89BOsCa9/oW+xpg==
// ลอง แล้ว agent ไม่สามารถเข้าได้
func EncryptPgHard(userToken string) (string, error) {
	operatorID := os.Getenv("PG_HARD_OPERATOR_ID")
	if operatorID == "" {
		fmt.Println("PG_HARD_OPERATOR_ID is not set")
		return "", errors.New("PG_HARD_OPERATOR_ID is not set")
	}

	key := deriveKey(operatorID) // Derive a 32-byte key from operatorID
	plaintext := []byte(userToken)

	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}

	ciphertext := make([]byte, aes.BlockSize+len(plaintext))
	iv := ciphertext[:aes.BlockSize]

	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		return "", err
	}

	stream := cipher.NewCFBEncrypter(block, iv)
	stream.XORKeyStream(ciphertext[aes.BlockSize:], plaintext)

	// Return the encoded string
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// 4bHD2yHvx5fxaXkUo4/CgDjKo2z89BOsCa9/oW+xpg==
// ลอง แล้ว agent ไม่สามารถเข้าได้
func DecryptPgHard(encryptedToken string) (string, error) {
	operatorID := os.Getenv("PG_HARD_OPERATOR_ID")
	if operatorID == "" {
		fmt.Println("PG_HARD_OPERATOR_ID is not set")
		return "", errors.New("PG_HARD_OPERATOR_ID is not set")
	}

	key := deriveKey(operatorID) // Derive a 32-byte key from operatorID

	ciphertext, err := base64.StdEncoding.DecodeString(encryptedToken)
	if err != nil {
		return "", err
	}

	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}

	if len(ciphertext) < aes.BlockSize {
		return "", errors.New("ciphertext too short")
	}

	iv := ciphertext[:aes.BlockSize]
	ciphertext = ciphertext[aes.BlockSize:]

	stream := cipher.NewCFBDecrypter(block, iv)
	stream.XORKeyStream(ciphertext, ciphertext)

	// Return the decoded string
	return string(ciphertext), nil
}
