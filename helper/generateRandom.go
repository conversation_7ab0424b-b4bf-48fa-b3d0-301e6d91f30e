package helper

import (
	"crypto/rand"
	"encoding/base64"
	"strings"

	"github.com/dchest/uniuri"
	"github.com/google/uuid"
)

func GenerateRandomBase64(length int) string {
	randomBytes := make([]byte, length)
	rand.Read(randomBytes)
	return base64.URLEncoding.EncodeToString(randomBytes)
}

func GenerateRandomUid(length int) string {
	u := uuid.New()
	randomString := u.String()
	// Truncate to desired length
	if length < len(randomString) {
		randomString = randomString[:length]
	}
	return randomString
}
func GenerateRandomStringAndNumber(length int) string {
	return uniuri.NewLen(length)
}
func GenerateRandomUuid(length int) string {
	u := uuid.New()
	randomString := u.String()
	// replace - with empty string
	randomString = strings.Replace(randomString, "-", "", -1)
	// Truncate to desired length
	if length < len(randomString) {
		randomString = randomString[:length]
	}
	return randomString
}
