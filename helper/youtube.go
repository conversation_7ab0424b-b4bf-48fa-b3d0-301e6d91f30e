package helper

import (
	"errors"
	"regexp"

	"github.com/gocolly/colly"
)

type Youtube struct {
	Title       string
	Description string
}

func GetYoutubeId(youtubeUrl string) (string, error) {

	youtubeId := ""

	regex := regexp.MustCompile(`(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([a-zA-Z0-9_-]{11})`)
	match := regex.FindStringSubmatch(youtubeUrl)
	if len(match) > 1 {
		youtubeId = match[1]
	}

	if youtubeId == "" {
		return "", errors.New("Youtube Url ไม่ถูกต้อง")
	}

	return youtubeId, nil
}

func GetYoutubeContent(youtubeUrl string) (*Youtube, error) {

	youtube := &Youtube{}

	c := colly.NewCollector()

	c.OnHTML("meta[itemprop]", func(e *colly.HTMLElement) {
		property := e.Attr("itemprop")
		content := e.Attr("content")

		switch property {
		case "name":
			youtube.Title = content
		case "description":
			youtube.Description = content
		}
	})

	err := c.Visit(youtubeUrl)
	if err != nil {
		return nil, err
	}

	return youtube, nil
}
