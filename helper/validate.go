package helper

import "strings"

// PhoneValidate is a function to validate phone number
func CheckPhone(phone string) bool {

	if len(phone) == 0 {
		return false
	}

	phoneLength := 10

	if len(phone) < phoneLength || len(phone) > phoneLength {
		return false
	}

	if phone[0] != '0' {
		return false
	}

	return true
}

// EmailValidate is a function to validate email
func CheckEmail(email string) bool {

	if len(email) == 0 {
		return false
	}

	if !strings.Contains(email, "@") {
		return false
	}

	if !strings.Contains(email, ".") {
		return false
	}

	return true
}
