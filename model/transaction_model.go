package model

import "time"

type TransactionDetail struct {
	ID              int64  `json:"id"`
	SlipURL         string `json:"slipUrl"`
	Amount          int64  `json:"amount"`
	Type            string `json:"type"`
	Status          string `json:"status"`
	UserID          int64  `json:"-"`
	DepositedAt     string `json:"-"`
	TransactionDate string `json:"transactionDate"`
	CreatedAt       string `json:"-"`
}

type TransactionQuery struct {
	Page   int   `form:"page"`
	Limit  int   `form:"limit"`
	UserId int64 `json:"-"`
}

type TransactionDepositBody struct {
	QrData        string `json:"qrData" binding:"required"`
	SlipURL       string `json:"slipUrl"`
	Amount        int64  `json:"amount" binding:"required"`
	Type          string `json:"-"`
	StatusId      int64  `json:"-"`
	UserID        int64  `json:"-"`
	BankID        int64  `json:"bankId"`
	BankAccountID int64  `json:"-"`
	DepositedAt   string `json:"depositedAt" binding:"required" example:"2023-12-13 11:22"`
}
type CreateDepositFromUserUploadRequest struct {
	UserId      int64     `json:"-"`
	Amount      float64   `json:"amount" binding:"required"`
	SlipImgUrl  string    `json:"slipImgUrl" binding:"required"`
	BankId      int64     `json:"bankId" binding:"required"`
	DepositedAt time.Time `json:"depositedAt" binding:"required" example:"2021-01-01T00:00:00Z"`
}
type CreateDepositConfirmedFromUserRequest struct {
	Amount      float64   `json:"amount" binding:"required"`
	RawQrCode   string    `json:"rawQrCode" binding:"required"`
	UserId      int64     `json:"-"`
	BankId      int64     `json:"bankId" binding:"required"`
	DepositedAt time.Time `json:"depositedAt" binding:"required" example:"2021-01-01T00:00:00Z"`
}
type CreateDepositConfirmedFromUserBody struct {
	Id            int64  `json:"id"`
	RawQrCode     string `json:"rawQrCode"`
	SlipUrl       string `json:"slipUrl"`
	Amount        int64  `json:"amount"` // ทำไมไม่เป็น Float
	StatusId      int64  `json:"statusId"`
	UserId        int64  `json:"userId"`
	BankId        int64  `json:"bankId"`
	BankAccountId int64  `json:"bankAccountId"`
	DepositedAt   string `json:"depositedAt"`
}

type ValidateQrDataWithBankTransactionBody struct {
	TransferDate    time.Time `json:"transferDate"`
	TransferAmount  float64   `json:"transferAmount"`
	FromBankAccount string    `json:"fromBankAccount"`
	ToBankAccountId int64     `json:"toBankAccountId"`
	FromBankId      *int64    `json:"bankid"`
}

type FindPossibleOwnerByConfirmDeposit struct {
	Id                  int64 `json:"id"`
	StatementId         int64 `json:"statement_id"`
	TransactionStatusId int64 `json:"transaction_status_id"`
}

type TransactionWithdrawBody struct {
	Amount   int64  `json:"amount" binding:"required"`
	Type     string `json:"-"`
	StatusId int64  `json:"-"`
	UserID   int64  `json:"-"`
}

type TrasactionListResponse struct {
	List  []TransactionDetail `json:"list"`
	Total int64               `json:"total"`
}

type TransactionBankList struct {
	ID            int64  `json:"id"`
	AccountName   string `json:"name"`
	BankName      string `json:"bankName"`
	AccountNumber string `json:"accountNumber"`
}

type TransactionBankResponse struct {
	Result []TransactionBankList `json:"result"`
}

type TransactionAgentRequest struct {
	UserId     int64   `json:"userId"`
	PlayerName string  `json:"PlayerName"`
	Amount     float64 `json:"Amount"`
	RefId      *int64  `json:"RefId"`
	Remark     string  `json:"Remark"`
}

type TransactionAgent struct {
	AgentName         string        `json:"AgentName"`
	PlayerName        string        `json:"PlayerName"`
	Amount            float64       `json:"Amount"`
	Timestamp         int           `json:"Timestamp"`
	Sign              string        `json:"Sign"`
	RefId             *int64        `json:"RefId"`
	Remark            string        `json:"Remark"`
	AvailableProducts []interface{} `json:"AvailableProducts"`
}

type TransactionDataForAgent struct {
	ID         int64
	MemberCode string
}

type TransactionVerifyQrData struct {
	ActionCode            string      `json:"actionCode"`
	RawQrBarcode          string      `json:"rawQrBarcode"`
	KPayQrPaymentInfo     interface{} `json:"kPayQrPaymentInfo"`
	PromptPayTransferInfo interface{} `json:"promptPayTransferInfo"`
	TransferInfo          interface{} `json:"transferInfo"`
	BillPaymentInfo       interface{} `json:"billPaymentInfo"`
	VerifySlipInfo        struct {
		SlipType                 string `json:"slipType"`
		TransType                string `json:"transType"`
		TransferType             string `json:"transferType"`
		TransRef                 string `json:"transRef"`
		TransAmount              int64  `json:"transAmount"`
		TransFeeAmount           int64  `json:"transFeeAmount"`
		TransDate                int64  `json:"transDate"`
		TransTime                string `json:"transTime"`
		FromAccountNo            string `json:"fromAccountNo"`
		FromAccountNameTh        string `json:"fromAccountNameTh"`
		FromAccountNameEn        string `json:"fromAccountNameEn"`
		FromBankName             string `json:"fromBankName"`
		FromBankId               string `json:"fromBankId"`
		ToAccountNo              string `json:"toAccountNo"`
		ToAccountNameEn          string `json:"toAccountNameEn"`
		ToAccountNameTh          string `json:"toAccountNameTh"`
		ToBankName               string `json:"toBankName"`
		ToBankId                 string `json:"toBankId"`
		ToMerchantName           string `json:"toMerchantName"`
		BillReference1           string `json:"billReference1"`
		BillReference2           string `json:"billReference2"`
		BillReference3           string `json:"billReference3"`
		NumberOfRef              int64  `json:"numberOfRef"`
		Reference1Name           string `json:"reference1Name"`
		Reference2Name           string `json:"reference2Name"`
		Reference3Name           string `json:"reference3Name"`
		KShopReference           string `json:"kShopReference"`
		KShopMerchantName        string `json:"kShopMerchantName"`
		KShopMerchantAccountName string `json:"kShopMerchantAccountName"`
		MerchantCode             string `json:"merchantCode"`
		MerchantAlias            string `json:"merchantAlias"`
		MerchantCategory         string `json:"merchantCategory"`
		SlipBankCode             string `json:"slipBankCode"`
		SwiftCode                string `json:"swiftCode"`
		InterfundStatus          string `json:"interfundStatus"`
		InterfundStatusDesc      string `json:"interfundStatusDesc"`
		ForeignExchangeAmount    int64  `json:"foreignExchangeAmount"`
		CurrencyCode             string `json:"currencyCode"`
		CurrencyExponent         int64  `json:"currencyExponent"`
		IsForeignPayment         string `json:"isForeignPayment"`
		Info1                    string `json:"info1"`
		Info2                    string `json:"info2"`
		Info3                    string `json:"info3"`
	} `json:"verifySlipInfo"`
	AtmCardlessInfo    interface{} `json:"atmCardlessInfo"`
	AuthenticationInfo interface{} `json:"authenticationInfo"`
	WebsiteUrlInfo     interface{} `json:"websiteUrlInfo"`
	UpiPaymentInfo     interface{} `json:"upiPaymentInfo"`
	FreeText           string      `json:"freeText"`
}

type TransactionStatement struct {
	ID int64 `json:"id"`
}

// {
// 	"status": "00000",
// 	"referenceNo": "A6588070db2d14691",
// 	"transactionDateTime": "2024-04-01T15:21:35+07:00",
// 	"effectiveDate": "2024-04-01T15:21:35+07:00",
// 	"fromAccount": "XXX-X-XX131-3",
// 	"fromAccountName": "นาย สหัสวรรษ ชาวเหนือ",
// 	"fromBank": "006",
// 	"fromBankName": "กรุงไทย",
// 	"fromBankIconUrl": "next-static-content-bucket/images/transfer/banks/006v202208021500.png",
// 	"toAccount": "XXX-X-XX008-5",
// 	"toAccountName": "นาย เมธา ศรีคุณารักษ์",
// 	"receivingBank": "014",
// 	"receivingBankName": "ไทยพาณิชย์",
// 	"receivingBankIconUrl": "next-static-content-bucket/images/transfer/banks/014v201910242016.png",
// 	"amount": 1,
// 	"currency": "THB"
//   }

type TransactionVerifyQrDataKTB struct {
	Status               string  `json:"status"`
	ReferenceNo          string  `json:"referenceNo"`
	TransactionDateTime  string  `json:"transactionDateTime"`
	EffectiveDate        string  `json:"effectiveDate"`
	FromAccount          string  `json:"fromAccount"`
	FromAccountName      string  `json:"fromAccountName"`
	FromBank             string  `json:"fromBank"`
	FromBankName         string  `json:"fromBankName"`
	FromBankIconUrl      string  `json:"fromBankIconUrl"`
	ToAccount            string  `json:"toAccount"`
	ToAccountName        string  `json:"toAccountName"`
	ReceivingBank        string  `json:"receivingBank"`
	ReceivingBankName    string  `json:"receivingBankName"`
	ReceivingBankIconUrl string  `json:"receivingBankIconUrl"`
	Amount               float64 `json:"amount"`
	Currency             string  `json:"currency"`
}

type UserFirstDeposit struct {
	Id         int64
	UserId     int64
	TransferAt time.Time
	Amount     float64
	Remark     string
	CreatedAt  time.Time
}
type UserFirstDepositCreateBody struct {
	Id         int64     `json:"id"`
	UserId     int64     `json:"userId"`
	TransferAt time.Time `json:"transferAt"`
	Amount     float64   `json:"amount"`
	Remark     string    `json:"remark"`
}
type UserFirstDepositCreateRequest struct {
	Id            int64     `json:"id"`
	UserId        int64     `json:"userId"`
	TransactionId *int64    `json:"transactionId"`
	TransferAt    time.Time `json:"transferAt"`
	Amount        float64   `json:"amount"`
	Remark        string    `json:"remark"`
}
