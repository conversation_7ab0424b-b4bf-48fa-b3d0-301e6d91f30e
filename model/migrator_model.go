package model

import (
	"time"

	"gorm.io/gorm"
)

const (
	MIGRATOR_STATUS_PENDING = 0
	MIGRATOR_STATUS_SUCCESS = 1
	MIGRATOR_STATUS_ERROR   = 2
)

type MigratorUser struct {
	Id               int64          `json:"id"`
	MigratorStatusId int64          `json:"migratorStatusId"`
	MemberCode       string         `json:"memberCode"`
	Ref              string         `json:"ref"`
	RefBy            int64          `json:"refBy"`
	Username         string         `json:"username"`
	Password         string         `json:"password"`
	Phone            string         `json:"phone"`
	UserStatusId     int64          `json:"userStatusId"`
	UserTypeId       int64          `json:"userTypeId"`
	Firstname        string         `json:"firstname"`
	Lastname         string         `json:"lastname"`
	Fullname         string         `json:"fullname"`
	Credit           float64        `json:"credit"`
	Ip               string         `json:"ip"`
	BankId           int64          `json:"bankId"`
	BankAccount      string         `json:"bankAccount"`
	ChannelId        int64          `json:"channelId"`
	TrueWallet       string         `json:"trueWallet"`
	Contact          string         `json:"contact"`
	Note             string         `json:"note"`
	Course           string         `json:"course"`
	LineId           string         `json:"lineId"`
	Encrypt          string         `json:"encrypt"`
	IpRegistered     string         `json:"ipRegistered"`
	VerifiedAt       *time.Time     `json:"verifiedAt"`
	CreatedBy        int64          `json:"createdBy"`
	IsResetPassword  bool           `json:"isResetPassword"`
	LogedinAt        *time.Time     `json:"logedinAt"`
	CreatedAt        time.Time      `json:"createdAt"`
	UpdatedAt        *time.Time     `json:"updatedAt"`
	DeletedAt        gorm.DeletedAt `json:"deletedAt"`
}
type MigratorUserListRequest struct {
	Search  string `form:"search"`
	Page    int    `form:"page" default:"1" min:"1"`
	Limit   int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol string `form:"sortCol"`
	SortAsc string `form:"sortAsc"`
}

type MigratorUserCreateBody struct {
	Id           int64     `json:"id"`
	MemberCode   string    `json:"memberCode"`
	RefBy        int64     `json:"refBy"`
	Username     string    `json:"username"`
	Phone        string    `json:"phone"`
	Password     string    `json:"password"`
	Fullname     string    `json:"fullname"`
	Firstname    string    `json:"firstname"`
	Lastname     string    `json:"lastname"`
	UserStatusId int64     `json:"userStatusId"`
	UserTypeId   int64     `json:"userTypeId"`
	BankAccount  string    `json:"bankAccount"`
	BankId       int64     `json:"bankId"`
	ChannelId    int64     `json:"channelId"`
	LineId       string    `json:"lineId"`
	Encrypt      string    `json:"encrypt"`
	VerifiedAt   time.Time `json:"verifiedAt"`
	LastActionAt time.Time `json:"lastActionAt"`
	CreatedAt    time.Time `json:"createdAt"`
	UpdatedAt    time.Time `json:"updatedAt"`
}

type MigratorPartner struct {
	Id               int64      `json:"id"`
	MigratorStatusId int64      `json:"migratorStatusId"`
	SiteId           int64      `json:"siteId"`
	MpartUserIdMk    int64      `json:"mpartUserIdMk"`
	MpartMemId       int64      `json:"mpartMemId"`
	MpartMemCode     string     `json:"mpartMemCode"`
	MpartCodename    string     `json:"mpartCodename"`
	MpartComSport    float64    `json:"mpartComSport"`
	MpartComCasino   float64    `json:"mpartComCasino"`
	MpartComGame     float64    `json:"mpartComGame"`
	MpartAgent       int64      `json:"mpartAgent"`
	MpartSport       int64      `json:"mpartSport"`
	MpartCasino      int64      `json:"mpartCasino"`
	MpartGame        int64      `json:"mpartGame"`
	MpartBonusFree   int64      `json:"mpartBonusFree"`
	MpartBonus       int64      `json:"mpartBonus"`
	MpartTranDay     int64      `json:"mpartTranDay"`
	MpartCreate      *time.Time `json:"mpartCreate"`
	MpartUpdate      *time.Time `json:"mpartUpdate"`
}
type MigratorPartnerListRequest struct {
	Search  string `form:"search"`
	Page    int    `form:"page" default:"1" min:"1"`
	Limit   int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol string `form:"sortCol"`
	SortAsc string `form:"sortAsc"`
}

type MigratorResponse struct {
	WorkName     string `json:"workName"`
	DataCount    int64  `json:"dataCount"`
	SuccessCount int64  `json:"successCount"`
	ErrorCount   int64  `json:"errorCount"`
}
type UserHasRefBy struct {
	Id         int64 `json:"id"`
	RefBy      int64 `json:"refBy"`
	UserTypeId int64 `json:"userTypeId"`
}
type UserHasRefByListRequest struct {
	Search  string `form:"search"`
	Page    int    `form:"page" default:"1" min:"1"`
	Limit   int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol string `form:"sortCol"`
	SortAsc string `form:"sortAsc"`
}

type MigrateBankTransaction struct {
	Id           int64     `json:"id"`
	UserId       int64     `json:"userId"`
	TransferAt   time.Time `json:"transferAt"`
	ConfirmedAt  time.Time `json:"confirmedAt"`
	CreditAmount float64   `json:"creditAmount"`
}

type MigratorOldUserFirstDepResponse struct {
	WorkName     string                   `json:"workName"`
	DataCount    int64                    `json:"dataCount"`
	SuccessCount int64                    `json:"successCount"`
	ErrorCount   int64                    `json:"errorCount"`
	Data         []MigratorOldUserDeposit `json:"data"`
}
type MigratorOldUserListRequest struct {
	FromUserId int64  `form:"fromUserId" validate:"required"`
	ToUserId   int64  `form:"toUserId" validate:"required"`
	Page       int    `form:"page" default:"1" min:"1"`
	Limit      int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol    string `form:"sortCol"`
	SortAsc    string `form:"sortAsc"`
}
type MigratorOldUser struct {
	Id         int64  `json:"id"`
	MemberCode string `json:"memberCode"`
	Phone      string `json:"phone"`
}
type MigratorOldUserDeposit struct {
	UserId          int64 `json:"userId"`
	DepositCount    int64 `json:"depositCount"`
	HasFirstDeposit bool  `json:"hasFirstDeposit"`
	FirstDepositId  int64 `json:"firstDepositId"`
	NewDepositId    int64 `json:"newDepositId"`
}

type MigratorOldUserFirstDepositCreateBody struct {
	Id                  int64     `json:"id"`
	UserId              int64     `json:"userId"`
	TransactionTypeId   int64     `json:"transactionTypeId"`
	TransferAt          time.Time `json:"transferAt"`
	ConfirmedAt         time.Time `json:"confirmedAt"`
	CreditAmount        float64   `json:"creditAmount"`
	TransactionStatusId int64     `json:"transactionStatusId"`
	IsFirstDeposit      int64     `json:"isFirstDeposit"`
	DepositChannel      string    `json:"depositChannel"`
	CreatedByAdminId    int64     `json:"createdByAdminId"`
	CreatedAt           time.Time `json:"createdAt"`
	// DeletedAt           time.Time `json:"deletedAt"`
}

type MigratorBcelUser struct {
	Id               int64          `json:"id"`
	MigratorStatusId int64          `json:"migratorStatusId"`
	MemberCode       string         `json:"memberCode"`
	Ref              string         `json:"ref"`
	RefBy            int64          `json:"refBy"`
	Username         string         `json:"username"`
	Password         string         `json:"password"`
	ImiPassword      string         `json:"imiPassword"`
	Phone            string         `json:"phone"`
	UserStatusId     int64          `json:"userStatusId"`
	UserTypeId       int64          `json:"userTypeId"`
	Firstname        string         `json:"firstname"`
	Lastname         string         `json:"lastname"`
	Fullname         string         `json:"fullname"`
	Credit           float64        `json:"credit"`
	Ip               string         `json:"ip"`
	BankId           int64          `json:"bankId"`
	BankCode         string         `json:"bankCode"`
	BankAccount      string         `json:"bankAccount"`
	ChannelId        int64          `json:"channelId"`
	TrueWallet       string         `json:"trueWallet"`
	Contact          string         `json:"contact"`
	Note             string         `json:"note"`
	Course           string         `json:"course"`
	LineId           string         `json:"lineId"`
	Encrypt          string         `json:"encrypt"`
	IpRegistered     string         `json:"ipRegistered"`
	VerifiedAt       *time.Time     `json:"verifiedAt"`
	CreatedBy        int64          `json:"createdBy"`
	IsResetPassword  bool           `json:"isResetPassword"`
	LogedinAt        *time.Time     `json:"logedinAt"`
	CreatedAt        time.Time      `json:"createdAt"`
	UpdatedAt        *time.Time     `json:"updatedAt"`
	DeletedAt        gorm.DeletedAt `json:"deletedAt"`
}
