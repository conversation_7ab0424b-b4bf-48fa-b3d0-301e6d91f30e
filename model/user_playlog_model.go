package model

import (
	"time"
)

const (
	USER_PLAYLOG_STATUS_PENDING = 1
	USER_PLAYLOG_STATUS_SUCCESS = 2
	USER_PLAYLOG_STATUS_FAIL    = 3
)

type Playlog struct {
	Id                   int64
	Player               string
	UserId               int64
	TurnSport            float64
	TurnCasino           float64
	TurnGame             float64
	TurnLottery          float64
	TurnP2p              float64
	TurnFinancial        float64
	WinLoseSport         float64
	WinLoseCasino        float64
	WinLoseGame          float64
	WinLoseLottery       float64
	WinLoseP2p           float64
	WinLoseFinancial     float64
	ValidAmountSport     float64
	ValidAmountCasino    float64
	ValidAmountGame      float64
	ValidAmountLottery   float64
	ValidAmountP2p       float64
	ValidAmountFinancial float64
	TurnTotal            float64
	WinLoseTotal         float64
	ValidAmountTotal     float64
	Date                 string
	CreatedAt            time.Time
}

type PlaylogTotalAmount struct {
	Id                        int64   `json:"id"`
	UserId                    int64   `json:"userId"`
	TotalLossAmount           float64 `json:"totalLossAmount"`
	TotalLossSport            float64 `json:"totalLossSport"`
	TotalLossCasino           float64 `json:"totalLossCasino"`
	TotalLossGame             float64 `json:"totalLossGame"`
	TotalLossLottery          float64 `json:"totalLossLottery"`
	TotalLossP2p              float64 `json:"totalLossP2p"`
	TotalLossFinancial        float64 `json:"totalLossFinancial"`
	TotalTurnAmount           float64 `json:"totalTurnAmount"`
	TotalTurnSport            float64 `json:"totalTurnSport"`
	TotalTurnCasino           float64 `json:"totalTurnCasino"`
	TotalTurnGame             float64 `json:"totalTurnGame"`
	TotalTurnLottery          float64 `json:"totalTurnLottery"`
	TotalTurnP2p              float64 `json:"totalTurnP2p"`
	TotalTurnFinancial        float64 `json:"totalTurnFinancial"`
	TotalValidAmount          float64 `json:"totalValidAmount"`
	TotalValidAmountSport     float64 `json:"totalValidAmountSport"`
	TotalValidAmountCasino    float64 `json:"totalValidAmountCasino"`
	TotalValidAmountGame      float64 `json:"totalValidAmountGame"`
	TotalValidAmountLottery   float64 `json:"totalValidAmountLottery"`
	TotalValidAmountP2p       float64 `json:"totalValidAmountP2p"`
	TotalValidAmountFinancial float64 `json:"totalValidAmountFinancial"`
}

type UserPlaylogStatus struct {
	Id             int64      `json:"id"`
	StatementDate  string     `json:"statementDate"`
	Path           string     `json:"path"`
	Page           int        `json:"page"`
	StatusId       int64      `json:"statusId"`
	OutMessage     string     `json:"outMessage"`
	OutTotal       int64      `json:"outTotal"`
	OutJsonError   string     `json:"outJsonError"`
	OutJsonSummary string     `json:"outJsonSummary"`
	OutTargetUrl   string     `json:"outTargetUrl"`
	CreatedAt      time.Time  `json:"createdAt"`
	UpdatedAt      *time.Time `json:"updatedAt"`
}
type UserPlaylogStatusResponse struct {
	Id             int64      `json:"id"`
	StatementDate  string     `json:"statementDate"`
	Path           string     `json:"path"`
	Page           int        `json:"page"`
	StatusId       int64      `json:"statusId"`
	StatusName     string     `json:"statusName"`
	OutMessage     string     `json:"outMessage"`
	OutTotal       int64      `json:"outTotal"`
	OutJsonError   string     `json:"outJsonError"`
	OutJsonSummary string     `json:"outJsonSummary"`
	OutTargetUrl   string     `json:"outTargetUrl"`
	CreatedAt      time.Time  `json:"createdAt"`
	UpdatedAt      *time.Time `json:"updatedAt"`
}
type UserPlaylogStatusCreateBody struct {
	Id             int64  `json:"id"`
	StatementDate  string `json:"statementDate"`
	Path           string `json:"path"`
	Page           int    `json:"page"`
	StatusId       int64  `json:"statusId"`
	OutMessage     string `json:"outMessage"`
	OutTotal       int64  `json:"outTotal"`
	OutJsonError   string `json:"outJsonError"`
	OutJsonSummary string `json:"outJsonSummary"`
	OutTargetUrl   string `json:"outTargetUrl"`
}
type UserPlaylogStatusUpdateBody struct {
	StatusId       int64  `json:"statusId"`
	OutMessage     string `json:"outMessage"`
	OutTotal       int64  `json:"outTotal"`
	OutJsonError   string `json:"outJsonError"`
	OutJsonSummary string `json:"outJsonSummary"`
	OutTargetUrl   string `json:"outTargetUrl"`
}

type UserPlaylog struct {
	Id                   int64     `json:"id"`
	UserId               int64     `json:"userId"`
	StatementDate        string    `json:"statementDate"`
	DailyKey             string    `json:"dailyKey"`
	TurnSport            float64   `json:"turnSport"`
	ValidAmountSport     float64   `json:"validAmountSport"`
	WinLoseSport         float64   `json:"winLoseSport"`
	TurnCasino           float64   `json:"turnCasino"`
	WinLoseCasino        float64   `json:"winLoseCasino"`
	ValidAmountCasino    float64   `json:"validAmountCasino"`
	TurnGame             float64   `json:"turnGame"`
	WinLoseGame          float64   `json:"winLoseGame"`
	ValidAmountGame      float64   `json:"validAmountGame"`
	TurnLottery          float64   `json:"turnLottery"`
	WinLoseLottery       float64   `json:"winLoseLottery"`
	ValidAmountLottery   float64   `json:"validAmountLottery"`
	TurnP2p              float64   `json:"turnP2p"`
	WinLoseP2p           float64   `json:"winLoseP2p"`
	ValidAmountP2p       float64   `json:"validAmountP2p"`
	TurnFinancial        float64   `json:"turnFinancial"`
	WinLoseFinancial     float64   `json:"winLoseFinancial"`
	ValidAmountFinancial float64   `json:"validAmountFinancial"`
	TurnTotal            float64   `json:"turnTotal"`
	WinLoseTotal         float64   `json:"winLoseTotal"`
	ValidAmountTotal     float64   `json:"validAmountTotal"`
	CreatedAt            time.Time `json:"createdAt"`
}
type UserPlaylogListRequest struct {
	UserId   *int64 `form:"userId"`
	FromDate string `form:"fromDate"`
	ToDate   string `form:"toDate"`
	Page     int    `form:"page" default:"1" min:"1"`
	Limit    int    `form:"limit" default:"10" min:"0" max:"100"`
	SortCol  string `form:"sortCol"`
	SortAsc  string `form:"sortAsc"`
}
type UserPlaylogResponse struct {
	Id                   int64     `json:"id"`
	UserId               int64     `json:"userId"`
	MemberCode           string    `json:"memberCode"`
	StatementDate        string    `json:"statementDate"`
	TurnSport            float64   `json:"turnSport"`
	ValidAmountSport     float64   `json:"validAmountSport"`
	WinLoseSport         float64   `json:"winLoseSport"`
	TurnCasino           float64   `json:"turnCasino"`
	WinLoseCasino        float64   `json:"winLoseCasino"`
	ValidAmountCasino    float64   `json:"validAmountCasino"`
	TurnGame             float64   `json:"turnGame"`
	WinLoseGame          float64   `json:"winLoseGame"`
	ValidAmountGame      float64   `json:"validAmountGame"`
	TurnLottery          float64   `json:"turnLottery"`
	WinLoseLottery       float64   `json:"winLoseLottery"`
	ValidAmountLottery   float64   `json:"validAmountLottery"`
	TurnP2p              float64   `json:"turnP2p"`
	WinLoseP2p           float64   `json:"winLoseP2p"`
	ValidAmountP2p       float64   `json:"validAmountP2p"`
	TurnFinancial        float64   `json:"turnFinancial"`
	WinLoseFinancial     float64   `json:"winLoseFinancial"`
	ValidAmountFinancial float64   `json:"validAmountFinancial"`
	TurnTotal            float64   `json:"turnTotal"`
	WinLoseTotal         float64   `json:"winLoseTotal"`
	ValidAmountTotal     float64   `json:"validAmountTotal"`
	CreatedAt            time.Time `json:"createdAt"`
}
type UserPlaylogTotalResponse struct {
	UserId               int64   `json:"userId"`
	RefBy                int64   `json:"refBy"`
	TurnSport            float64 `json:"turnSport"`
	ValidAmountSport     float64 `json:"validAmountSport"`
	WinLoseSport         float64 `json:"winLoseSport"`
	TurnCasino           float64 `json:"turnCasino"`
	WinLoseCasino        float64 `json:"winLoseCasino"`
	ValidAmountCasino    float64 `json:"validAmountCasino"`
	TurnGame             float64 `json:"turnGame"`
	WinLoseGame          float64 `json:"winLoseGame"`
	ValidAmountGame      float64 `json:"validAmountGame"`
	TurnLottery          float64 `json:"turnLottery"`
	WinLoseLottery       float64 `json:"winLoseLottery"`
	ValidAmountLottery   float64 `json:"validAmountLottery"`
	TurnP2p              float64 `json:"turnP2p"`
	WinLoseP2p           float64 `json:"winLoseP2p"`
	ValidAmountP2p       float64 `json:"validAmountP2p"`
	TurnFinancial        float64 `json:"turnFinancial"`
	WinLoseFinancial     float64 `json:"winLoseFinancial"`
	ValidAmountFinancial float64 `json:"validAmountFinancial"`
	TurnTotal            float64 `json:"turnTotal"`
	WinLoseTotal         float64 `json:"winLoseTotal"`
	ValidAmountTotal     float64 `json:"validAmountTotal"`
}
type UserPlaylogCreateBody struct {
	Id                   int64   `json:"id"`
	UserId               int64   `json:"userId"`
	MemberCode           string  `json:"-" gorm:"-"`
	DailyKey             string  `json:"dailyKey"`
	StatementDate        string  `json:"statementDate"`
	TurnSport            float64 `json:"turnSport"`
	ValidAmountSport     float64 `json:"validAmountSport"`
	WinLoseSport         float64 `json:"winLoseSport"`
	TurnCasino           float64 `json:"turnCasino"`
	WinLoseCasino        float64 `json:"winLoseCasino"`
	ValidAmountCasino    float64 `json:"validAmountCasino"`
	TurnGame             float64 `json:"turnGame"`
	WinLoseGame          float64 `json:"winLoseGame"`
	ValidAmountGame      float64 `json:"validAmountGame"`
	TurnLottery          float64 `json:"turnLottery"`
	WinLoseLottery       float64 `json:"winLoseLottery"`
	ValidAmountLottery   float64 `json:"validAmountLottery"`
	TurnP2p              float64 `json:"turnP2p"`
	WinLoseP2p           float64 `json:"winLoseP2p"`
	ValidAmountP2p       float64 `json:"validAmountP2p"`
	TurnFinancial        float64 `json:"turnFinancial"`
	WinLoseFinancial     float64 `json:"winLoseFinancial"`
	ValidAmountFinancial float64 `json:"validAmountFinancial"`
	TurnTotal            float64 `json:"turnTotal"`
	WinLoseTotal         float64 `json:"winLoseTotal"`
	ValidAmountTotal     float64 `json:"validAmountTotal"`
}

type UserAffiliateIncome struct {
	Id                  int64     `json:"id"`
	UserId              int64     `json:"userId"`
	StatementDate       string    `json:"statementDate"`
	StatusId            int64     `json:"statusId"`
	DailyKey            string    `json:"dailyKey"`
	TurnSport           float64   `json:"turnSport"`
	PercentSport        float64   `json:"percentSport"`
	CommissionSport     float64   `json:"commissionSport"`
	TurnCasino          float64   `json:"turnCasino"`
	PercentCasino       float64   `json:"percentCasino"`
	CommissionCasino    float64   `json:"commissionCasino"`
	TurnGame            float64   `json:"turnGame"`
	PercentGame         float64   `json:"percentGame"`
	CommissionGame      float64   `json:"commissionGame"`
	TurnLottery         float64   `json:"turnLottery"`
	PercentLottery      float64   `json:"percentLottery"`
	CommissionLottery   float64   `json:"commissionLottery"`
	TurnP2p             float64   `json:"turnP2p"`
	PercentP2p          float64   `json:"percentP2p"`
	CommissionP2p       float64   `json:"commissionP2p"`
	TurnFinancial       float64   `json:"turnFinancial"`
	PercentFinancial    float64   `json:"percentFinancial"`
	CommissionFinancial float64   `json:"commissionFinancial"`
	TurnTotal           float64   `json:"turnTotal"`
	CommissionTotal     float64   `json:"commissionTotal"`
	TakeAt              time.Time `json:"takeAt"`
	TakenPrice          float64   `json:"takenPrice"`
	CreatedAt           time.Time `json:"createdAt"`
}
type UserAffiliateIncomeCreateBody struct {
	Id                   int64   `json:"id"`
	UserId               int64   `json:"userId"`
	StatementDate        string  `json:"statementDate"`
	StatusId             int64   `json:"statusId"`
	DailyKey             string  `json:"dailyKey"`
	TurnSport            float64 `json:"turnSport"`
	PercentSport         float64 `json:"percentSport"`
	PercentSport1        float64 `json:"percentSport1"`
	PercentSport2        float64 `json:"percentSport2"`
	CommissionSport      float64 `json:"commissionSport"`
	CommissionSport1     float64 `json:"commissionSport1"`
	CommissionSport2     float64 `json:"commissionSport2"`
	TurnCasino           float64 `json:"turnCasino"`
	PercentCasino        float64 `json:"percentCasino"`
	PercentCasino1       float64 `json:"percentCasino1"`
	PercentCasino2       float64 `json:"percentCasino2"`
	CommissionCasino     float64 `json:"commissionCasino"`
	CommissionCasino1    float64 `json:"commissionCasino1"`
	CommissionCasino2    float64 `json:"commissionCasino2"`
	TurnGame             float64 `json:"turnGame"`
	PercentGame          float64 `json:"percentGame"`
	PercentGame1         float64 `json:"percentGame1"`
	PercentGame2         float64 `json:"percentGame2"`
	CommissionGame       float64 `json:"commissionGame"`
	CommissionGame1      float64 `json:"commissionGame1"`
	CommissionGame2      float64 `json:"commissionGame2"`
	TurnLottery          float64 `json:"turnLottery"`
	PercentLottery       float64 `json:"percentLottery"`
	PercentLottery1      float64 `json:"percentLottery1"`
	PercentLottery2      float64 `json:"percentLottery2"`
	CommissionLottery    float64 `json:"commissionLottery"`
	CommissionLottery1   float64 `json:"commissionLottery1"`
	CommissionLottery2   float64 `json:"commissionLottery2"`
	TurnP2p              float64 `json:"turnP2p"`
	PercentP2p           float64 `json:"percentP2p"`
	PercentP2p1          float64 `json:"percentP2p1"`
	PercentP2p2          float64 `json:"percentP2p2"`
	CommissionP2p        float64 `json:"commissionP2p"`
	CommissionP2p1       float64 `json:"commissionP2p1"`
	CommissionP2p2       float64 `json:"commissionP2p2"`
	TurnFinancial        float64 `json:"turnFinancial"`
	PercentFinancial     float64 `json:"percentFinancial"`
	PercentFinancial1    float64 `json:"percentFinancial1"`
	PercentFinancial2    float64 `json:"percentFinancial2"`
	CommissionFinancial  float64 `json:"commissionFinancial"`
	CommissionFinancial1 float64 `json:"commissionFinancial1"`
	CommissionFinancial2 float64 `json:"commissionFinancial2"`
	TurnTotal            float64 `json:"turnTotal"`
	CommissionTotal      float64 `json:"commissionTotal"`
}
type UserAffiliateIncomeTotalListRequest struct {
	LimitAmount   float64 `form:"limitAmount"`
	RefBy         *int64  `form:"refBy"`
	UserId        *int64  `form:"userId"`
	StatementDate string  `form:"statementDate"`
}

type UserAffiliateIncomeTotalResponse struct {
	DailyKey            string                                `json:"dailyKey"`
	RefBy               int64                                 `json:"refBy"`
	CommissionSport     float64                               `json:"commissionSport"`
	CommissionCasino    float64                               `json:"commissionCasino"`
	CommissionGame      float64                               `json:"commissionGame"`
	CommissionLottery   float64                               `json:"commissionLottery"`
	CommissionP2p       float64                               `json:"commissionP2p"`
	CommissionFinancial float64                               `json:"commissionFinancial"`
	CommissionTotal     float64                               `json:"commissionTotal"`
	UserList            []UserAffiliateIncomeDownlineResponse `json:"userList"`
}
type UserAffiliateIncomeDownlineResponse struct {
	Level               int64   `json:"level"`
	RefBy               int64   `json:"refBy"`
	UserId              int64   `json:"userId"`
	CommissionSport     float64 `json:"commissionSport"`
	CommissionCasino    float64 `json:"commissionCasino"`
	CommissionGame      float64 `json:"commissionGame"`
	CommissionLottery   float64 `json:"commissionLottery"`
	CommissionP2p       float64 `json:"commissionP2p"`
	CommissionFinancial float64 `json:"commissionFinancial"`
	CommissionTotal     float64 `json:"commissionTotal"`
}

type UserTodayPlaylog struct {
	Id                   int64     `json:"id"`
	UserId               int64     `json:"userId"`
	DailyKey             string    `json:"dailyKey"`
	StatementDate        string    `json:"statementDate"`
	TurnSport            float64   `json:"turnSport"`
	ValidAmountSport     float64   `json:"validAmountSport"`
	WinLoseSport         float64   `json:"winLoseSport"`
	TurnCasino           float64   `json:"turnCasino"`
	WinLoseCasino        float64   `json:"winLoseCasino"`
	ValidAmountCasino    float64   `json:"validAmountCasino"`
	TurnGame             float64   `json:"turnGame"`
	WinLoseGame          float64   `json:"winLoseGame"`
	ValidAmountGame      float64   `json:"validAmountGame"`
	TurnLottery          float64   `json:"turnLottery"`
	WinLoseLottery       float64   `json:"winLoseLottery"`
	ValidAmountLottery   float64   `json:"validAmountLottery"`
	TurnP2p              float64   `json:"turnP2p"`
	WinLoseP2p           float64   `json:"winLoseP2p"`
	ValidAmountP2p       float64   `json:"validAmountP2p"`
	TurnFinancial        float64   `json:"turnFinancial"`
	WinLoseFinancial     float64   `json:"winLoseFinancial"`
	ValidAmountFinancial float64   `json:"validAmountFinancial"`
	TurnTotal            float64   `json:"turnTotal"`
	WinLoseTotal         float64   `json:"winLoseTotal"`
	ValidAmountTotal     float64   `json:"validAmountTotal"`
	CreatedAt            time.Time `json:"createdAt"`
}
type UserTodayPlaylogResponse struct {
	Id                   int64     `json:"id"`
	UserId               int64     `json:"userId"`
	DailyKey             string    `json:"dailyKey"`
	StatementDate        string    `json:"statementDate"`
	TurnSport            float64   `json:"turnSport"`
	ValidAmountSport     float64   `json:"validAmountSport"`
	WinLoseSport         float64   `json:"winLoseSport"`
	TurnCasino           float64   `json:"turnCasino"`
	WinLoseCasino        float64   `json:"winLoseCasino"`
	ValidAmountCasino    float64   `json:"validAmountCasino"`
	TurnGame             float64   `json:"turnGame"`
	WinLoseGame          float64   `json:"winLoseGame"`
	ValidAmountGame      float64   `json:"validAmountGame"`
	TurnLottery          float64   `json:"turnLottery"`
	WinLoseLottery       float64   `json:"winLoseLottery"`
	ValidAmountLottery   float64   `json:"validAmountLottery"`
	TurnP2p              float64   `json:"turnP2p"`
	WinLoseP2p           float64   `json:"winLoseP2p"`
	ValidAmountP2p       float64   `json:"validAmountP2p"`
	TurnFinancial        float64   `json:"turnFinancial"`
	WinLoseFinancial     float64   `json:"winLoseFinancial"`
	ValidAmountFinancial float64   `json:"validAmountFinancial"`
	TurnTotal            float64   `json:"turnTotal"`
	WinLoseTotal         float64   `json:"winLoseTotal"`
	ValidAmountTotal     float64   `json:"validAmountTotal"`
	CreatedAt            time.Time `json:"createdAt"`
}
type UserTodayPlaylogCreateBody struct {
	Id                   int64   `json:"id"`
	UserId               int64   `json:"userId"`
	DailyKey             string  `json:"dailyKey"`
	MemberCode           string  `json:"-" gorm:"-"`
	StatementDate        string  `json:"statementDate"`
	TurnSport            float64 `json:"turnSport"`
	ValidAmountSport     float64 `json:"validAmountSport"`
	WinLoseSport         float64 `json:"winLoseSport"`
	TurnCasino           float64 `json:"turnCasino"`
	WinLoseCasino        float64 `json:"winLoseCasino"`
	ValidAmountCasino    float64 `json:"validAmountCasino"`
	TurnGame             float64 `json:"turnGame"`
	WinLoseGame          float64 `json:"winLoseGame"`
	ValidAmountGame      float64 `json:"validAmountGame"`
	TurnLottery          float64 `json:"turnLottery"`
	WinLoseLottery       float64 `json:"winLoseLottery"`
	ValidAmountLottery   float64 `json:"validAmountLottery"`
	TurnP2p              float64 `json:"turnP2p"`
	WinLoseP2p           float64 `json:"winLoseP2p"`
	ValidAmountP2p       float64 `json:"validAmountP2p"`
	TurnFinancial        float64 `json:"turnFinancial"`
	WinLoseFinancial     float64 `json:"winLoseFinancial"`
	ValidAmountFinancial float64 `json:"validAmountFinancial"`
	TurnTotal            float64 `json:"turnTotal"`
	WinLoseTotal         float64 `json:"winLoseTotal"`
	ValidAmountTotal     float64 `json:"validAmountTotal"`
}
type UserTodayPlaylogUpdateBody struct {
	Id                   int64    `json:"id"`
	DailyKey             string   `json:"dailyKey"`
	TurnSport            *float64 `json:"turnSport"`
	ValidAmountSport     *float64 `json:"validAmountSport"`
	WinLoseSport         *float64 `json:"winLoseSport"`
	TurnCasino           *float64 `json:"turnCasino"`
	WinLoseCasino        *float64 `json:"winLoseCasino"`
	ValidAmountCasino    *float64 `json:"validAmountCasino"`
	TurnGame             *float64 `json:"turnGame"`
	WinLoseGame          *float64 `json:"winLoseGame"`
	ValidAmountGame      *float64 `json:"validAmountGame"`
	TurnLottery          *float64 `json:"turnLottery"`
	WinLoseLottery       *float64 `json:"winLoseLottery"`
	ValidAmountLottery   *float64 `json:"validAmountLottery"`
	TurnP2p              *float64 `json:"turnP2p"`
	WinLoseP2p           *float64 `json:"winLoseP2p"`
	ValidAmountP2p       *float64 `json:"validAmountP2p"`
	TurnFinancial        *float64 `json:"turnFinancial"`
	WinLoseFinancial     *float64 `json:"winLoseFinancial"`
	ValidAmountFinancial *float64 `json:"validAmountFinancial"`
	TurnTotal            *float64 `json:"turnTotal"`
	WinLoseTotal         *float64 `json:"winLoseTotal"`
	ValidAmountTotal     *float64 `json:"validAmountTotal"`
}
type UserTodayPlaylogListRequest struct {
	UserId             *int64 `form:"userId"`
	StatementDate      string `form:"statementDate"`
	StatementDateStart string `form:"statementDateStart"`
	StatementDateEnd   string `form:"statementDateEnd"`
	Page               int    `form:"page" default:"1" min:"1"`
	Limit              int    `form:"limit" default:"10" min:"0" max:"100"`
	SortCol            string `form:"sortCol"`
	SortAsc            string `form:"sortAsc"`
}
type UserTodaySumPlaylogReponse struct {
	UserId                  int64   `json:"userId"`
	StatementDate           string  `json:"statementDate"`
	SumTurnSport            float64 `json:"turnSport"`
	SumTurnCasino           float64 `json:"turnCasino"`
	SumTurnGame             float64 `json:"turnGame"`
	SumTurnLottery          float64 `json:"turnLottery"`
	SumTurnP2p              float64 `json:"turnP2p"`
	SumTurnFinancial        float64 `json:"turnFinancial"`
	SumWinLoseSport         float64 `json:"winLoseSport"`
	SumWinLoseCasino        float64 `json:"winLoseCasino"`
	SumWinLoseGame          float64 `json:"winLoseGame"`
	SumWinLoseLottery       float64 `json:"winLoseLottery"`
	SumWinLoseP2p           float64 `json:"winLoseP2p"`
	SumWinLoseFinancial     float64 `json:"winLoseFinancial"`
	SumValidAmountSport     float64 `json:"validAmountSport"`
	SumValidAmountCasino    float64 `json:"validAmountCasino"`
	SumValidAmountGame      float64 `json:"validAmountGame"`
	SumValidAmountLottery   float64 `json:"validAmountLottery"`
	SumValidAmountP2p       float64 `json:"validAmountP2p"`
	SumValidAmountFinancial float64 `json:"validAmountFinancial"`
	SumTurnTotal            float64 `json:"turnTotal"`
	SumWinLoseTotal         float64 `json:"winLoseTotal"`
	SumValidAmountTotal     float64 `json:"validAmountTotal"`
}

type UserPlaylogSummary struct {
	Id               int64      `json:"id"`
	UserId           int64      `json:"userId"`
	PlaylogStartDate string     `json:"playlogStartDate"`
	TotalTurn        float64    `json:"totalTurn"`
	TotalWinLoss     float64    `json:"totalWinLoss"`
	CreatedAt        time.Time  `json:"createdAt"`
	UpdatedAt        *time.Time `json:"updatedAt"`
}
type UserPlaylogSummaryListRequest struct {
	UserId  *int64 `form:"userId"`
	Page    int    `form:"page" default:"1" min:"1"`
	Limit   int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol string `form:"sortCol"`
	SortAsc string `form:"sortAsc"`
}
type UserPlaylogSummaryResponse struct {
	Id               int64      `json:"id"`
	UserId           int64      `json:"userId"`
	MemberCode       string     `json:"memberCode"`
	PlaylogStartDate string     `json:"playlogStartDate"`
	TotalTurn        float64    `json:"totalTurn"`
	TotalWinLoss     float64    `json:"totalWinLoss"`
	CreatedAt        time.Time  `json:"createdAt"`
	UpdatedAt        *time.Time `json:"updatedAt"`
}
type UserPlaylogSummaryCreateBody struct {
	Id               int64     `json:"id"`
	UserId           int64     `json:"userId"`
	PlaylogStartDate string    `json:"playlogStartDate"`
	TotalTurn        float64   `json:"totalTurn"`
	TotalWinLoss     float64   `json:"totalWinLoss"`
	UpdatedAt        time.Time `json:"updatedAt"`
}
type UserPlaylogSummaryUpdateBody struct {
	TotalTurn    *float64  `json:"totalTurn"`
	TotalWinLoss *float64  `json:"totalWinLoss"`
	UpdatedAt    time.Time `json:"updatedAt"`
}

type ReportPlayLogStatusRequest struct {
	Page    int    `form:"page" default:"1" min:"1"`
	Limit   int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol string `form:"sortCol"`
	SortAsc string `form:"sortAsc"`
}
type ReportPlayLogStatusResponse struct {
	StatementDate             string `json:"statementDate"`
	PlaylogSportStatus        string `json:"playlogSportStatus"`
	PlaylogGameStatus         string `json:"playlogGameStatus"`
	PlaylogCasinoStatus       string `json:"playlogCasinoStatus"`
	PlaylogLotteryStatus      string `json:"playlogLotteryStatus"`
	PlaylogP2pStatus          string `json:"playlogP2pStatus"`
	PlaylogFinancialStatus    string `json:"playlogFinancialStatus"`
	CutDailyAffiliateStatus   string `json:"cutDailyAffiliateStatus"`
	CutDailyAllianceStatus    string `json:"cutDailyAllianceStatus"`
	PromotionReturnLossStatus string `json:"promotionReturnLossStatus"`
}
type ReportPlayLogRerunRequest struct {
	StatementDate string `form:"statementDate" validate:"required"`
}
type ReportPlayLogResponseRequest struct {
	StatementDate string `form:"statementDate" validate:"required"`
	Page          int    `form:"page" default:"1" min:"1"`
	Limit         int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol       string `form:"sortCol"`
	SortAsc       string `form:"sortAsc"`
}
type ReportPlayLogResponse struct {
	UserId               int64   `json:"userId"`
	MemberCode           string  `json:"memberCode"`
	TurnSport            float64 `json:"turnSport"`
	WinLoseSport         float64 `json:"winLoseSport"`
	ValidAmountSport     float64 `json:"validAmountSport"`
	TurnCasino           float64 `json:"turnCasino"`
	WinLoseCasino        float64 `json:"winLoseCasino"`
	ValidAmountCasino    float64 `json:"validAmountCasino"`
	TurnGame             float64 `json:"turnGame"`
	WinLoseGame          float64 `json:"winLoseGame"`
	ValidAmountGame      float64 `json:"validAmountGame"`
	TurnLottery          float64 `json:"turnLottery"`
	WinLoseLottery       float64 `json:"winLoseLottery"`
	ValidAmountLottery   float64 `json:"validAmountLottery"`
	TurnP2p              float64 `json:"turnP2p"`
	WinLoseP2p           float64 `json:"winLoseP2p"`
	ValidAmountP2p       float64 `json:"validAmountP2p"`
	TurnFinancial        float64 `json:"turnFinancial"`
	WinLoseFinancial     float64 `json:"winLoseFinancial"`
	ValidAmountFinancial float64 `json:"validAmountFinancial"`
	TurnTotal            float64 `json:"turnTotal"`
	WinLoseTotal         float64 `json:"winLoseTotal"`
	ValidAmountTotal     float64 `json:"validAmountTotal"`
	StatementDate        string  `json:"statementDate"`
}
type ReportPlayLogSummaryResponse struct {
	TurnSport            float64 `json:"turnSport"`
	WinLoseSport         float64 `json:"winLoseSport"`
	ValidAmountSport     float64 `json:"validAmountSport"`
	TurnCasino           float64 `json:"turnCasino"`
	WinLoseCasino        float64 `json:"winLoseCasino"`
	ValidAmountCasino    float64 `json:"validAmountCasino"`
	TurnGame             float64 `json:"turnGame"`
	WinLoseGame          float64 `json:"winLoseGame"`
	ValidAmountGame      float64 `json:"validAmountGame"`
	TurnLottery          float64 `json:"turnLottery"`
	WinLoseLottery       float64 `json:"winLoseLottery"`
	ValidAmountLottery   float64 `json:"validAmountLottery"`
	TurnP2p              float64 `json:"turnP2p"`
	WinLoseP2p           float64 `json:"winLoseP2p"`
	ValidAmountP2p       float64 `json:"validAmountP2p"`
	TurnFinancial        float64 `json:"turnFinancial"`
	WinLoseFinancial     float64 `json:"winLoseFinancial"`
	ValidAmountFinancial float64 `json:"validAmountFinancial"`
	TurnTotal            float64 `json:"turnTotal"`
	WinLoseTotal         float64 `json:"winLoseTotal"`
	ValidAmountTotal     float64 `json:"validAmountTotal"`
}

type AgentCallbackPlayLogListRequest struct {
	FromDate            string `form:"fromDate"`
	ToDate              string `form:"toDate"`
	AgentGameProviderId int64  `form:"agentGameProviderId"`
	Page                int    `form:"page" default:"1" min:"1"`
	Limit               int    `form:"limit" default:"10" min:"1" max:"100"`
	UserId              *int64 `form:"userId"`
}

type AgentCtwCallbackPlayLogListResponse struct {
	Id             int64     `json:"id"`
	UserId         int64     `json:"userId"`
	MemberCode     string    `json:"memberCode"`
	Payoff         float64   `json:"payoff"`
	BetAmount      float64   `json:"betAmount"`
	WinloseAmount  float64   `json:"winloseAmount"`
	Balance        float64   `json:"balance"`
	BeforeBalance  float64   `json:"beforeBalance"`
	AfterBalance   float64   `json:"afterBalance"`
	TransactionId  string    `json:"transactionId"`
	RoundId        string    `json:"roundId"`
	GameId         string    `json:"gameId"`
	CallbackReason string    `json:"callbackReason"`
	CreatedAt      time.Time `json:"createdAt"`
}
type AgentPgHardCallbackPlayLogListResponse struct {
	Id            int64     `json:"id"`
	UserId        int64     `json:"userId"`
	MemberCode    string    `json:"memberCode"`
	Payoff        float64   `json:"payoff"`
	BetAmount     float64   `json:"betAmount"`
	WinloseAmount float64   `json:"winloseAmount"`
	Balance       float64   `json:"balance"`
	BeforeBalance float64   `json:"beforeBalance"`
	AfterBalance  float64   `json:"afterBalance"`
	TransactionId string    `json:"transactionId"`
	RoundId       string    `json:"roundId"`
	GameId        string    `json:"gameId"`
	GameStringId  string    `json:"gameStringId"`
	GameName      string    `json:"gameName"`
	Remark        string    `json:"remark"`
	IsSuccess     int       `json:"isSuccess"`
	CreatedAt     time.Time `json:"createdAt"`
}

type AgcWinloseReportRequest struct {
	StartDate     string `json:"StartDate"`
	EndDate       string `json:"EndDate"`
	PageSize      int    `json:"PageSize"`
	PageIndex     int    `json:"PageIndex"`
	MemberName    string `json:"MemberName"`
	AgentName     string `json:"AgentName"`
	PlayerName    string `json:"PlayerName"`
	Products      []int  `json:"Products"`
	Currency      string `json:"Currency"`
	AgentCurrency bool   `json:"AgentCurrency"`
	TimeStamp     int64  `json:"TimeStamp"`
	Sign          string `json:"sign"`
}

type AgcWinloseReportResponse struct {
	Result    AgcWinloseReportResult `json:"Result"`
	TargetUrl *string                `json:"TargetUrl"`
	Success   bool                   `json:"Success"`
	Error     interface{}            `json:"Error"`
	Message   interface{}            `json:"Message"`
}

type AgcWinloseReportResult struct {
	Total   int64                    `json:"Total"`
	Records []AgcWinloseReportRecord `json:"Records"`
	// Summary       AgcWinloseReportSummary   `json:"Summary"`
	// ProcessStatus []AgcWinloseProcessStatus `json:"ProcessStatus"`
}

type AgcWinloseReportRecord struct {
	Details   []AgcWinloseReportDetail `json:"Details"`
	PlayerId  int64                    `json:"PlayerId"`
	MemberId  int64                    `json:"MemberId"`
	UserName  string                   `json:"UserName"`
	FullName  string                   `json:"FullName"`
	TurnOver  float64                  `json:"TurnOver"`
	GrossComm float64                  `json:"GrossComm"`
	Company   float64                  `json:"Company"`
	Role      int                      `json:"Role"`
	Loyalty   float64                  `json:"Loyalty"`
	VipInfo   interface{}              `json:"VipInfo"`
}

type AgcWinloseReportDetail struct {
	Role        int     `json:"Role"`
	Win         float64 `json:"Win"`
	Comm        float64 `json:"Comm"`
	ValidAmount float64 `json:"ValidAmount"`
	Loyalty     float64 `json:"Loyalty"`
	Total       float64 `json:"Total"`
}

type AgcWinloseReportSummary struct {
	ValidAmounts map[string]float64             `json:"ValidAmounts"`
	Comms        map[string]float64             `json:"Comms"`
	Wins         map[string]float64             `json:"Wins"`
	Details      map[string]AgcWinloseDetailMap `json:"Details"`
	Win          float64                        `json:"Win"`
	Comm         float64                        `json:"Comm"`
	TurnOver     float64                        `json:"TurnOver"`
	GrossComm    float64                        `json:"GrossComm"`
	PayOut       float64                        `json:"PayOut"`
	Company      float64                        `json:"Company"`
	ValidAmount  float64                        `json:"ValidAmount"`
	Loyalty      float64                        `json:"Loyalty"`
}

type AgcWinloseDetailMap struct {
	Role        int     `json:"Role"`
	Win         float64 `json:"Win"`
	Comm        float64 `json:"Comm"`
	ValidAmount float64 `json:"ValidAmount"`
	Loyalty     float64 `json:"Loyalty"`
	Total       float64 `json:"Total"`
}

type AgcWinloseProcessStatus struct {
	Date   string `json:"Date"` // or use time.Time if you're parsing to time
	Status bool   `json:"Status"`
}

// tickets/fetchpaging
type AgcTicketsFetchPagingRequest struct {
	FromDate string `json:"FromDate"`
	ToDate   string `json:"ToDate"`
	// Metadata  string `json:"Metadata"`
	PageSize  int    `json:"PageSize"`
	Partner   string `json:"Partner"`
	TimeStamp int64  `json:"TimeStamp"`
	Sign      string `json:"sign"`
}

type AgcTicketReportResponse struct {
	Result    AgcTicketsFetchPagingResult `json:"Result"`
	TargetUrl *string                     `json:"TargetUrl"`
	Success   bool                        `json:"Success"`
	Error     AgcError                    `json:"Error"`
	Message   string                      `json:"Message"`
}

type AgcTicketsFetchPagingResult struct {
	Tickets  []interface{}            `json:"Tickets"`
	Metadata AgcTicketsPagingMetadata `json:"Metadata"`
}

type AgcTicketsPagingMetadata struct {
	FirstSortOffset *string `json:"FirstSortOffset"`
	LastSortOffset  *string `json:"LastSortOffset"`
	PageIndex       int     `json:"PageIndex"`
	Direction       int     `json:"Direction"`
}

type AgcError struct {
	Code    int    `json:"Code"`
	Message string `json:"Message"`
}
