package model

import "time"

// 2024/11/28 15:56:17 SendTelegramMessage.response_data {"ok":false,"error_code":400,"description":"Bad Request: chat not found"}
type TelegramErrorResponse struct {
	Ok          bool   `json:"ok"`
	ErrorCode   int64  `json:"error_code"`
	Description string `json:"description"`
}

// {
// 	"update_id": 902863961,
// 	"message": {
// 		"message_id": 20,
// 		"from": {
// 			"id": 1745326599,
// 			"is_bot": false,
// 			"first_name": "tanaka",
// 			"last_name": "kyonkun",
// 			"username": "Tanaka012",
// 			"language_code": "en"
// 		},
// 		"chat": {
// 			"id": -4155155843,
// 			"title": "POC NOTI",
// 			"type": "group",
// 			"all_members_are_administrators": true
// 		},
// 		"date": 1710750888,
// 		"text": "/showmethemoney",
// 		"entities": [
// 			{
// 				"offset": 0,
// 				"length": 15,
// 				"type": "bot_command"
// 			}
// 		]
// 	}
// }

type TelegramGetUpdatesResponse struct {
	Ok     bool                 `json:"ok"`
	Result []TelegramGetUpdates `json:"result"`
}
type TelegramGetUpdates struct {
	UpdateId int64 `json:"update_id"`
	Message  struct {
		MessageId int64 `json:"message_id"`
		From      struct {
			Id         int64  `json:"id"`
			IsBot      bool   `json:"is_bot"`
			FirstName  string `json:"first_name"`
			LastName   string `json:"last_name"`
			Username   string `json:"username"`
			LanguageCd string `json:"language_code"`
		} `json:"from"`
		Chat struct {
			Id                 int64  `json:"id"`
			Title              string `json:"title"`
			Type               string `json:"type"`
			AllMembersAreAdmin bool   `json:"all_members_are_administrators"`
		} `json:"chat"`
		Date     int64  `json:"date"`
		Text     string `json:"text"`
		Entities []struct {
			Offset int64  `json:"offset"`
			Length int64  `json:"length"`
			Type   string `json:"type"`
		} `json:"entities"`
	} `json:"message"`
}

type TelegramSendMessageResponse struct {
	Ok     bool `json:"ok"`
	Result struct {
		MessageId int64 `json:"message_id"`
		From      struct {
			Id        int64  `json:"id"`
			IsBot     bool   `json:"is_bot"`
			FirstName string `json:"first_name"`
			Username  string `json:"username"`
		} `json:"from"`
		Chat struct {
			Id                 int64  `json:"id"`
			Title              string `json:"title"`
			Type               string `json:"type"`
			AllMembersAreAdmin bool   `json:"all_members_are_administrators"`
		} `json:"chat"`
		Date int64  `json:"date"`
		Text string `json:"text"`
	} `json:"result"`
}

type Telegram struct {
	Id        int64      `json:"id"`
	SortOrder int64      `json:"sortOrder"`
	ImgPath   string     `json:"imgPath"`
	IsShow    bool       `json:"isShow"`
	CreatedAt time.Time  `json:"createdAt"`
	UpdatedAt *time.Time `json:"updatedAt"`
}
type TelegramListRequest struct {
	IsShow  *bool  `form:"isShow"`
	Page    int    `form:"page" default:"1" min:"1"`
	Limit   int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol string `form:"sortCol"`
	SortAsc string `form:"sortAsc"`
}
type TelegramResponse struct {
	Id        int64      `json:"id"`
	SortOrder int64      `json:"sortOrder"`
	ImgPath   string     `json:"imgPath"`
	IsShow    bool       `json:"isShow"`
	CreatedAt time.Time  `json:"createdAt"`
	UpdatedAt *time.Time `json:"updatedAt"`
}
type TelegramCreateRequest struct {
	ImgPath string `form:"imgPath" binding:"required"`
	IsShow  *bool  `form:"isShow"`
}
type TelegramCreateBody struct {
	Id      int64  `json:"id"`
	ImgPath string `json:"imgPath"`
	IsShow  *bool  `json:"isShow"`
}
type TelegramUpdateRequest struct {
	IsShow *bool `form:"isShow"`
}
type TelegramUpdateBody struct {
	IsShow *bool `json:"isShow"`
}

type TelegramAccessTokenUpdateRequest struct {
	AccessToken string `form:"accessToken" binding:"required"`
}
type TelegramAccessTokenResponse struct {
	AccessToken string `json:"accessToken"`
}
type TelegramChatTokenResponse struct {
	AccessToken string `json:"accessToken"`
	ChatId      int64  `json:"chatId"`
}
