package model

const (
	USERTIER_TYPE_ENV      = "env"
	USERTIER_TYPE_DEPOSIT  = "deposit"
	USERTIER_TYPE_TURNOVER = "turnover"
)

type UserTierSetting struct {
	Id        int64
	Type      string
	SortOrder int
	Name      string
	FromValue int64
	ToValue   int64
	CreatedAt string
	UpdatedAt string
}
type UserTierSettingItem struct {
	Id        int64  `json:"id"`
	Type      string `json:"type"`
	SortOrder int    `json:"sortOrder"`
	Name      string `json:"name"`
	FromValue int64  `json:"fromValue"`
	ToValue   int64  `json:"toValue"`
	CreatedAt string `json:"created_at"`
	UpdatedAt string `json:"updated_at"`
}
type UserListTierSettingResponse struct {
	IsEnvEnabled      bool `json:"isEnvEnabled"`
	IsDepositEnabled  bool `json:"isDepositEnabled"`
	IsTurnOverEnabled bool `json:"isTurnOverEnabled"`
}
type UserTierSettingResponse struct {
	Type      string                `json:"type"`
	IsEnabled bool                  `json:"isEnabled"`
	List      []UserTierSettingItem `json:"list"`
}
type UserTierSettingListRequest struct {
	Type  string `form:"type" binding:"required"`
	Page  int    `form:"page" binding:"required"`
	Limit int    `form:"limit" binding:"required"`
}

type UserTierSettingUpdateItem struct {
	SortOrder int    `form:"sortOrder" binding:"required"`
	Name      string `form:"name" binding:"required"`
	FromValue int64  `form:"fromValue" binding:"required"`
	ToValue   int64  `form:"toValue" binding:"required"`
}
type UserTierByDepositSettingUpdateRequest struct {
	IsEnabled bool                        `form:"isEnabled"`
	List      []UserTierSettingUpdateItem `form:"list"`
}
type UserTierByTurnOverSettingUpdateRequest struct {
	IsEnabled bool                        `form:"isEnabled"`
	List      []UserTierSettingUpdateItem `form:"list"`
}
type UserTierSettingCreateBody struct {
	Id        int64
	Type      string
	SortOrder int
	Name      string
	FromValue int64
	ToValue   int64
}
type UserTierSettingUpdateBody struct {
	Type      *string
	SortOrder *int
	Name      *string
	FromValue *int64
	ToValue   *int64
}

type UserTierData struct {
	Id           int64
	UserId       int64
	TotalDeposit float64
	TotalTurn    float64
	CreatedAt    string
	UpdatedAt    string
}
type UserTierDataCreateBody struct {
	Id           int64
	UserId       int64
	TotalDeposit float64
	TotalTurn    float64
}
type UserTierDataUpdateRequest struct {
	UserId         int64   `form:"userId" binding:"required"`
	DepositAmount  float64 `form:"depositAmount"`
	TurnOverAmount float64 `form:"turnOverAmount"`
}
