package model

import (
	"time"
)

type EditSection struct {
	ID              int64     `json:"id"`
	SectionDeposit  string    `json:"sectionDeposit"`
	SectionWithdraw string    `json:"sectionWithdraw"`
	CreatedAt       time.Time `json:"createdAt"`
	CreatedById     int64     `json:"createdById"`
	UpdatedAt       time.Time `json:"updatedAt"`
	UpdatedBy       int64     `json:"updatedBy"`
}

type GetEditSectionResponse struct {
	SectionDeposit  string `json:"sectionDeposit"`
	SectionWithdraw string `json:"sectionWithdraw"`
}

type CreateEditSectionBody struct {
	SectionDeposit  string `json:"sectionDeposit"`
	SectionWithdraw string `json:"sectionWithdraw"`
	CreatedById     int64  `json:"-"`
}

type UpdateEditSectionBody struct {
	SectionDeposit  *string `json:"sectionDeposit"`
	SectionWithdraw *string `json:"sectionWithdraw"`
	UpdatedById     int64   `json:"-"`
}
