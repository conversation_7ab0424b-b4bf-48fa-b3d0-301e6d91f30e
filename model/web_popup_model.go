package model

import (
	"time"
)

type WebPopup struct {
	Id        int64      `json:"id"`
	SortOrder int64      `json:"sortOrder"`
	ImgPath   string     `json:"imgPath"`
	IsShow    bool       `json:"isShow"`
	CreatedAt time.Time  `json:"createdAt"`
	UpdatedAt *time.Time `json:"updatedAt"`
	DeleteAt  *time.Time `json:"deleteAt"`
}
type WebPopupListRequest struct {
	IsShow  *bool  `form:"isShow"`
	Page    int    `form:"page" default:"1" min:"1"`
	Limit   int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol string `form:"sortCol"`
	SortAsc string `form:"sortAsc"`
}
type WebPopupResponse struct {
	Id        int64      `json:"id"`
	SortOrder int64      `json:"sortOrder"`
	ImgPath   string     `json:"imgPath"`
	IsShow    bool       `json:"isShow"`
	CreatedAt time.Time  `json:"createdAt"`
	UpdatedAt *time.Time `json:"updatedAt"`
}
type WebPopupCreateRequest struct {
	ImgPath string `form:"imgPath" binding:"required"`
	IsShow  *bool  `form:"isShow"`
}
type WebPopupCreateBody struct {
	Id      int64  `json:"id"`
	ImgPath string `json:"imgPath"`
	IsShow  bool   `json:"isShow"`
}
type WebPopupUpdateRequest struct {
	IsShow *bool `form:"isShow"`
}
type WebPopupUpdateBody struct {
	IsShow *bool `json:"isShow"`
}
type SortOrder struct {
	Id        int64 `json:"id"`
	SortOrder int   `json:"sortOrder"`
}

type CloudFlareUploadCreateBody struct {
	FileTypeId        int64     `json:"fileTypeId"`
	ImageId           string    `json:"imageId"`
	Filename          string    `json:"filename"`
	Uploaded          time.Time `json:"uploaded"`
	RequireSignedURLs bool      `json:"requireSignedURLs"`
	FileUrl           string    `json:"fileUrl"`
	ImageDimensions   string    `json:"imageDimensions"`
	Kilobytes         int64     `json:"kilobytes"`
}

type CloudFlareUploadResponse struct {
	Result   CloudFlareResult `json:"result"`
	Success  bool             `json:"success"`
	Errors   []interface{}    `json:"errors"`
	Messages []interface{}    `json:"messages"`
}

type CloudFlareResult struct {
	Id                string    `json:"id"`
	Filename          string    `json:"filename"`
	Uploaded          time.Time `json:"uploaded"`
	RequireSignedURLs bool      `json:"requireSignedURLs"`
	Variants          []string  `json:"variants"`
}

type CloudFlareDeleteResponse struct {
	Result  interface{} `json:"result"`
	Success bool        `json:"success"`
	Errors  []struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
	} `json:"errors"`
	Messages []interface{} `json:"messages"`
}

// "result": {
//     "images": [
//       {
//         "id": "cbgame/db_maga89_net/banking/upload/deposit-slip/20241122_23620",
//         "filename": "line_oa_chat_241122_230411.jpeg",
//         "uploaded": "2024-11-22T16:06:15.805Z",
//         "requireSignedURLs": false,
//         "variants": [
//           "https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/db_maga89_net/banking/upload/deposit-slip/20241122_23620/public"
//         ]
//       }
//     ]
//   },
//   "success": true,
//   "errors": [],
//   "messages": []
// }

type GetAllUploadsResponse struct {
	Result   GetAllUploadsResult `json:"result"` // Result is now an object, not an array
	Success  bool                `json:"success"`
	Errors   []interface{}       `json:"errors"`
	Messages []interface{}       `json:"messages"`
}

type GetAllUploadsResult struct {
	Images []GetAllUploadsResponseResult `json:"images"` // Images is an array
}

type GetAllUploadsResponseResult struct {
	Id                string   `json:"id"`
	Filename          string   `json:"filename"`
	Uploaded          string   `json:"uploaded"` // Assuming uploaded is a string in the JSON
	RequireSignedURLs bool     `json:"requireSignedURLs"`
	Variants          []string `json:"variants"`
}
