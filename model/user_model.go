package model

import (
	"time"

	"gorm.io/gorm"
)

const (
	USER_STATUS_ACTIVE   int64 = 1
	USER_STATUS_DEACTIVE int64 = 2
	USER_TYPE_NONE       int64 = 1
	USER_TYPE_AFFILIATE  int64 = 2
	USER_TYPE_ALLIANCE   int64 = 3
)

type User struct {
	Id              int64          `json:"id"`
	MemberCode      *string        `json:"memberCode"`
	Username        *string        `json:"username"`
	LoginType       string         `json:"loginType"`
	Phone           string         `json:"phone"`
	Password        string         `json:"password"`
	UserStatusId    int64          `json:"userStatusId"`
	Fullname        string         `json:"fullname"`
	Firstname       string         `json:"firstname"`
	Lastname        string         `json:"lastname"`
	BankCode        string         `json:"bankCode" gorm:"-"`
	BankAccount     string         `json:"bankAccount"`
	BankId          int64          `json:"bankId"`
	LineId          string         `json:"lineId"`
	Encrypt         string         `json:"-"`
	ChannelId       int64          `json:"channelId"`
	TrueWallet      string         `json:"trueWallet"`
	Contact         string         `json:"contact"`
	Note            string         `json:"note"`
	Course          string         `json:"course"`
	Credit          float64        `json:"credit"`
	CreatedBy       int64          `json:"createdBy"`
	Ip              string         `json:"ip"`
	IsResetPassword bool           `json:"is_reset_password"`
	IpRegistered    string         `json:"ipRegistered"`
	CreatedAt       time.Time      `json:"createdAt"`
	UpdatedAt       time.Time      `json:"updatedAt"`
	DeletedAt       gorm.DeletedAt `json:"deletedAt"`
	LogedinAt       *time.Time     `json:"logedinAt" gorm:"default:CURRENT_TIMESTAMP"`
}
type UserResponse struct {
	Id              int64          `json:"id"`
	MemberCode      *string        `json:"memberCode"`
	Username        *string        `json:"username"`
	Phone           string         `json:"phone"`
	Password        string         `json:"password"`
	UserStatusId    int64          `json:"userStatusId"`
	Status          string         `json:"status"`
	Fullname        string         `json:"fullname"`
	Firstname       string         `json:"firstname"`
	Lastname        string         `json:"lastname"`
	BankCode        string         `json:"bankCode" gorm:"-"`
	BankAccount     string         `json:"bankAccount"`
	BankId          int64          `json:"bankId"`
	LineId          string         `json:"lineId"`
	Encrypt         string         `json:"-"`
	ChannelId       int64          `json:"channelId"`
	UserTypeId      int64          `json:"userTypeId"`
	TrueWallet      string         `json:"trueWallet"`
	Contact         string         `json:"contact"`
	Note            string         `json:"note"`
	Course          string         `json:"course"`
	Credit          float64        `json:"credit"`
	CreatedBy       int64          `json:"createdBy"`
	Ip              string         `json:"ip"`
	IsResetPassword bool           `json:"is_reset_password"`
	IpRegistered    string         `json:"ipRegistered"`
	CreatedAt       time.Time      `json:"createdAt"`
	UpdatedAt       time.Time      `json:"updatedAt"`
	DeletedAt       gorm.DeletedAt `json:"deletedAt"`
	LogedinAt       *time.Time     `json:"logedinAt" gorm:"default:CURRENT_TIMESTAMP"`
	LoginType       string         `json:"loginType"`
}

type UserFormCreate struct {
	Id           int64
	Username     string
	Phone        string
	Password     string
	Fullname     string
	Firstname    string
	Lastname     string
	UserStatusId int64
	BankAccount  string
	BankId       int64
	ChannelId    int64
	LineId       string
	Encrypt      string
	VerifiedAt   *time.Time
	CreatedBy    int64
	UpdatedAt    *time.Time
}

type UserRegisterForm struct {
	Id              int64   `json:"id"`
	MemberCode      *string `json:"memberCode"`
	RefBy           *int64  `json:"refBy"`
	Username        string  `json:"username"`
	LoginType       string  `json:"loginType"`
	Phone           string  `json:"phone"`
	Password        string  `json:"password" gorm:"default:NULL"`
	Fullname        string  `json:"fullname" gorm:"default:NULL"`
	Firstname       string  `json:"firstname" gorm:"default:NULL"`
	Lastname        string  `json:"lastname" gorm:"default:NULL"`
	BankAccount     string  `json:"bankAccount" gorm:"default:NULL"`
	BankId          int64   `json:"bankId" gorm:"default:NULL"`
	LineId          string  `json:"lineId" binding:"required,max=255"`
	ChannelId       int64   `json:"channelId" gorm:"default:NULL"`
	TrueWallet      string  `json:"trueWallet" gorm:"default:NULL"`
	Contact         string  `json:"contact" gorm:"default:NULL"`
	Note            string  `json:"note" gorm:"default:NULL"`
	Course          string  `json:"course" gorm:"default:NULL"`
	Encrypt         string  `json:"-"`
	Credit          float64 `json:"credit"`
	Ip              string  `json:"ip" gorm:"default:NULL"`
	IsResetPassword bool    `json:"isResetPassword"`
	IpRegistered    string  `json:"ipRegistered" gorm:"default:NULL"`
}

type UserRegisterCheckRequest struct {
	Phone        string `json:"phone" validate:"required,number,min=10,max=10" example:"**********"`
	CaptchaId    string `json:"captchaId" validate:"required"`
	CaptchaValue string `json:"captchaValue" validate:"required"`
}
type UserRegisterCheckResponse struct {
	Phone         string `json:"phone"`
	IsCanRegister bool   `json:"isCanRegister"`
}

type UserSendOtpBody struct {
	Phone string `json:"phone" validate:"required,number,min=10,max=10" example:"**********"`
}

type UserOtpResponse struct {
	UserId int64  `json:"userId,omitempty"`
	OtpId  string `json:"otpId"`
	OtpRef string `json:"otpRef"`
}

type UserOtp struct {
	Id         int64
	Phone      string
	Code       string
	Ref        string
	Type       string
	AntsOtpId  string
	UserId     int64
	LocalPin   string
	CreatedAt  time.Time
	VerifiedAt *time.Time
	ExpiredAt  *time.Time
}

type UserOtpBody struct {
	Phone     string
	Ref       string
	Type      string
	AntsOtpId string
	UserId    int64
	LocalPin  string
	ExpiredAt time.Time
}

type UserVerifyOtpBody struct {
	Phone string `json:"phone" validate:"required,number,min=10,max=10" example:"**********"`
	OtpId string `json:"otpId" validate:"required" example:"0c342e74-a9c7-4bbd-8da4-b36a49bbc0d6"`
	Code  string `json:"code" validate:"required,number,min=6,max=10" example:"123456"`
}

type UserResendOtpBody struct {
	Phone string `json:"phone" validate:"required,number,min=10,max=10" example:"**********"`
	OtpId string `json:"otpId" validate:"required" example:"0c342e74-a9c7-4bbd-8da4-b36a49bbc0d6"`
}

type UserVerifyOtpResponse struct {
	Token string `json:"token"`
}

type UserCreateRequest struct {
	Phone         string  `json:"phone" binding:"required,min=10,max=12" example:"**********"`
	Password      string  `json:"password" binding:"required,min=4,max=15,containsany=**********"`
	Fullname      string  `json:"fullname"`
	BankAccount   string  `json:"bankAccount" binding:"required,max=18"`
	BankId        int64   `json:"bankId" binding:"required"`
	LineId        string  `json:"lineId"`
	ChannelId     *int64  `json:"channelId"`
	RefMemberCode *string `json:"refMemberCode"`
	AdminId       int64   `json:"-"`
}

type UserRegister struct {
	OtpId        string `json:"otpId"`
	Code         string `json:"code"`
	Phone        string `json:"phone" binding:"required,min=10,max=12" example:"**********"`
	Password     string `json:"password" binding:"required,min=4,max=255,containsany=**********"`
	Fullname     string `json:"fullname"`
	BankAccount  string `json:"bankAccount" binding:"required,max=18"`
	BankId       int64  `json:"bankId" binding:"required"`
	LineId       string `json:"lineId"`
	ChannelId    *int64 `json:"channelId"`
	IpRegistered string `json:"ipRegistered"`
	RefBy        string `json:"refBy"`
	RefCode      string `json:"refCode"`
	SaleCode     string `json:"saleCode"`
}

type UserLogin struct {
	Phone    string `json:"phone" validate:"required,min=8,max=30"`
	Password string `json:"password" validate:"required,min=4,max=30"`
	Ip       string `json:"ip"`
}

type UserLoginUpdate struct {
	IP        string    `json:"ip"`
	LogedinAt time.Time `json:"logedinAt"`
}

type UserLoginResponse struct {
	GameProvider string `json:"gameProvider"`
	Token        string `json:"token"`
	GameToken    string `json:"gameToken"`
}

type UserListQuery struct {
	Page                   int    `form:"page" validate:"min=1"`
	Limit                  int    `form:"limit" validate:"min=1,max=100"`
	NonMember3             bool   `form:"nonMember" default:"false"`
	UserCategory           string `form:"userCategory"`
	Search                 string `form:"search"`
	From                   string `form:"from" time_format:"2006-01-02 15:04"`
	To                     string `form:"to" time_format:"2006-01-02 15:04"`
	DepositRankId          *int64 `form:"depositRankId"`
	DepositTotalAmountMin  *int64 `form:"depositTotalAmountMin"`
	DepositTotalAmountMax  *int64 `form:"depositTotalAmountMax"`
	TurnOverRankId         *int64 `form:"turnOverRankId"`
	TurnOverTotalAmountMin *int64 `form:"turnOverTotalAmountMin"`
	TurnOverTotalAmountMax *int64 `form:"turnOverTotalAmountMax"`
}
type UserListQueryForExcel struct {
	NonMember3   bool   `form:"nonMember" default:"false"`
	UserCategory string `form:"userCategory"`
	Search       string `form:"search"`
	From         string `form:"from" time_format:"2006-01-02 15:04"`
	To           string `form:"to" time_format:"2006-01-02 15:04"`
}

type UpdateUserRequest struct {
	Fullname      string  `json:"fullname" validate:"required,max=255"`
	Firstname     string  `json:"-"`
	Lastname      string  `json:"-"`
	MemberCode    string  `json:"memberCode" validate:"max=255"`
	Password      string  `json:"password" validate:"min=4,max=255,containsany=**********"`
	Phone         string  `json:"phone" validate:"required,min=10,max=12"`
	Username      string  `json:"-"`
	RefMemberCode *string `json:"refMemberCode"`
	BankAccount   string  `json:"bankAccount" validate:"max=18"`
	BankId        int64   `json:"bankId"`
	ChannelId     int64   `json:"channelId"`
	TrueWallet    string  `json:"trueWallet" validate:"max=20"`
	Contact       string  `json:"contact" validate:"max=255"`
	Note          string  `json:"note" validate:"max=255"`
	Course        string  `json:"course" validate:"max=50"`
	LineId        string  `json:"lineId"`
	Encrypt       string  `json:"-"`
	Ip            string  `json:"-"`
	IpAddress     string  `json:"ipAddress"`
}

type UpdateUserBody struct {
	Fullname    string  `json:"fullname" validate:"required,max=255"`
	Firstname   string  `json:"-"`
	Lastname    string  `json:"-"`
	MemberCode  string  `json:"memberCode" validate:"max=255"`
	Password    string  `json:"password" validate:"min=4,max=255,containsany=**********"`
	Phone       string  `json:"phone" validate:"required,min=10,max=12"`
	Username    string  `json:"-"`
	BankAccount string  `json:"bankAccount" validate:"max=18"`
	BankId      int64   `json:"bankId"`
	ChannelId   int64   `json:"channelId"`
	TrueWallet  string  `json:"trueWallet" validate:"max=20"`
	Contact     string  `json:"contact" validate:"max=255"`
	Note        string  `json:"note" validate:"max=255"`
	Course      string  `json:"course" validate:"max=50"`
	LineId      string  `json:"lineId"`
	Encrypt     *string `json:"-"`
	Ip          string  `json:"-"`
}

type UserBody struct {
	Fullname string `json:"fullname" validate:"required,min=8,max=30"`
	// Phone         string   `json:"phone" validate:"required,number,min=10,max=12"`
	Email         string   `json:"email" validate:"required,email"`
	GroupId       *int64   `json:"groupId"`
	Status        string   `json:"status" validate:"required"`
	PermissionIds *[]int64 `json:"permissionIds"`
}

type UserList struct {
	Id                  int64      `json:"id"`
	UserTypeId          int64      `json:"userTypeId"`
	Phone               string     `json:"phone"`
	LoginType           string     `json:"loginType"`
	TotalDepositAmount  float64    `json:"totalDepositAmount"`
	DepositRank         string     `json:"depositRank"`
	TotalTurnOverAmount float64    `json:"totalTurnOverAmount"`
	TurnOverRank        string     `json:"turnOverRank"`
	MemberCode          string     `json:"memberCode"`
	RefBy               string     `json:"refBy"`
	RefUrl              string     `json:"refUrl"`
	Fullname            string     `json:"fullname"`
	Type                string     `json:"type"`
	BankId              int64      `json:"bankId"`
	BankName            string     `json:"bankName"`
	BankAccount         string     `json:"bankAccount"`
	ChannelId           int64      `json:"channelId"`
	Channel             string     `json:"channel"`
	Credit              float64    `json:"credit"`
	Note                string     `json:"note"`
	Ip                  string     `json:"ip"`
	IpRegistered        string     `json:"ipRegistered"`
	CreatedBy           string     `json:"createdBy"`
	CreatedAt           *time.Time `json:"createdAt"`
	UpdatedAt           *time.Time `json:"updatedAt"`
	LogedinAt           *time.Time `json:"logedinAt" gorm:"default:CURRENT_TIMESTAMP"`
	AccumulatedAmount   float64    `json:"accumulatedAmount"`
	MaximumTimePerDay   float64    `json:"maximumTimePerDay"`
}
type UserListForExcel struct {
	Id                int64     `json:"id"`
	Phone             string    `json:"phone"`
	LoginType         string    `json:"loginType"`
	MemberCode        string    `json:"memberCode"`
	RefBy             string    `json:"refBy"`
	RefUrl            string    `json:"refUrl"`
	Fullname          string    `json:"fullname"`
	Type              string    `json:"type"`
	BankId            int64     `json:"bankId"`
	BankName          string    `json:"bankName"`
	BankAccount       string    `json:"bankAccount"`
	ChannelId         int64     `json:"channelId"`
	Channel           string    `json:"channel"`
	Credit            float64   `json:"credit"`
	Note              string    `json:"note"`
	Ip                string    `json:"ip"`
	IpRegistered      string    `json:"ipRegistered"`
	CreatedBy         string    `json:"createdBy"`
	CreatedAt         time.Time `json:"createdAt"`
	UpdatedAt         time.Time `json:"updatedAt"`
	LogedinAt         time.Time `json:"logedinAt" gorm:"default:CURRENT_TIMESTAMP"`
	AccumulatedAmount float64   `json:"accumulatedAmount"`
	MaximumTimePerDay float64   `json:"maximumTimePerDay"`
}

type UserDetail struct {
	Id           int64   `json:"id"`
	MemberCode   string  `json:"memberCode"`
	Username     string  `json:"username"`
	Phone        string  `json:"phone"`
	Fullname     string  `json:"fullname"`
	BankName     string  `json:"bankName"`
	BankAccount  string  `json:"bankAccount"`
	BankId       int64   `json:"bankId"`
	GameToken    string  `json:"gameToken"`
	ChannelId    int64   `json:"channelId"`
	Channel      string  `json:"channel"`
	TrueWallet   string  `json:"trueWallet"`
	Contact      string  `json:"contact"`
	Note         string  `json:"note"`
	Course       string  `json:"course"`
	Type         string  `json:"type"`
	Credit       float64 `json:"credit"`
	Ref          string  `json:"ref"`
	RefCode      string  `json:"refCode"`
	Encrypt      string  `json:"-"`
	IpRegistered string  `json:"-"`
	LoginType    string  `json:"loginType"`
}

type UserDetailResponse struct {
	Id               int64   `json:"id"`
	MemberCode       string  `json:"memberCode"`
	Username         string  `json:"username"`
	Phone            string  `json:"phone"`
	Fullname         string  `json:"fullname"`
	BankName         string  `json:"bankName"`
	BankAccount      string  `json:"bankAccount"`
	BankId           int64   `json:"bankId"`
	GameToken        string  `json:"gameToken"`
	ChannelId        int64   `json:"channelId"`
	Channel          string  `json:"channel"`
	TrueWallet       string  `json:"trueWallet"`
	Contact          string  `json:"contact"`
	Note             string  `json:"note"`
	Course           string  `json:"course"`
	Type             string  `json:"type"`
	Credit           float64 `json:"credit"`
	Ref              string  `json:"ref"`
	RefCode          string  `json:"refCode"`
	Encrypt          string  `json:"-"`
	IpRegistered     string  `json:"-"`
	TotalTurn        float64 `json:"totalTurn"`
	TotalWinLoss     float64 `json:"totalWinLoss"`
	TotalValidAmount float64 `json:"totalValidAmount"`
}

type UserInfo struct {
	Id              int64   `json:"id"`
	RefBy           *int64  `json:"refBy"`
	RefByMemberCode string  `json:"refByMemberCode"`
	MemberCode      string  `json:"memberCode"`
	Username        string  `json:"username"`
	Phone           string  `json:"phone"`
	Fullname        string  `json:"fullname"`
	BankName        string  `json:"bankName"`
	BankAccount     string  `json:"bankAccount"`
	BankId          int64   `json:"bankId"`
	GameToken       string  `json:"gameToken"`
	ChannelId       int64   `json:"channelId"`
	Channel         string  `json:"channel"`
	TrueWallet      string  `json:"trueWallet"`
	Contact         string  `json:"contact"`
	Note            string  `json:"note"`
	Course          string  `json:"course"`
	Type            string  `json:"type"`
	Credit          float64 `json:"credit"`
	Ref             string  `json:"ref"`
	RefCode         string  `json:"refCode"`
	Encrypt         string  `json:"-"`
	IpRegistered    string  `json:"-"`
}

type UserRefreshCreditRequest struct {
	Id int64 `uri:"id" binding:"required"`
}

type UserRefreshCreditResponse struct {
	Id         int64   `json:"id"`
	MemberCode string  `json:"memberCode"`
	Credit     float64 `json:"credit"`
}

type UserMe struct {
	Id          int64   `json:"id"`
	Credit      float64 `json:"credit"`
	Ref         string  `json:"ref"`
	RefCode     string  `json:"refCode"`
	Phone       string  `json:"phone"`
	BankName    string  `json:"bankname"`
	BankCode    string  `json:"bankCode"`
	BankAccount string  `json:"bankAccount"`
	MemberCode  string  `json:"memberCode"`
	Fullname    string  `json:"fullname"`
	Type        string  `json:"type"`
	LoginType   string  `json:"loginType"`
	Encrypt     string  `json:"-"`
}

type UserUpdatePassword struct {
	UserId      int64  `json:"-"`
	OldPassword string `json:"oldPassword" validate:"required,min=8,max=30,containsany=**********"`
	NewPassword string `json:"newPassword" validate:"required,min=8,max=30,containsany=**********"`
	Encrypt     string `json:"-"`
	Ip          string `json:"-"`
}

type UserUpdatePasswordForFront struct {
	UserId   int64  `json:"-"`
	OtpId    string `json:"otpId" binding:"required"`
	Code     string `json:"code" binding:"required"`
	Password string `json:"password" validate:"required,min=4,max=30,containsany=**********"`
	Encrypt  string `json:"-"`
	Ip       string `json:"-"`
}

type UserByPhone struct {
	Id         int64
	Phone      string
	VerifiedAt *time.Time
}

type UserLoginLog struct {
	Id        int64     `json:"id"`
	UserId    int64     `json:"userId"`
	Ip        string    `json:"ip"`
	CreatedAt time.Time `json:"createdAt"`
}

type UserUpdateLogs struct {
	UserId            int64   `json:"userId"`
	Description       string  `json:"description"`
	CreatedByUsername string  `json:"createdByUsername"`
	Ip                string  `json:"ip"`
	JsonResult        *string `json:"jsonResult"`
}

type UserUpdateLogResponse struct {
	Id                int64      `json:"id"`
	UserId            int64      `json:"userId"`
	Description       string     `json:"description"`
	CreatedByUsername string     `json:"createdByUsername"`
	Ip                string     `json:"ip"`
	CreatedAt         *time.Time `json:"createdAt"`
}

type UserUpdateQuery struct {
	Page   int    `form:"page" validate:"min=1"`
	Limit  int    `form:"limit" validate:"min=1,max=100"`
	Search string `form:"search"`
	From   string `form:"from" time_format:"2006-01-02 15:04"`
	To     string `form:"to" time_format:"2006-01-02 15:04"`
}

type UserOTP struct {
	Id         int64
	Code       string
	Ref        string
	Type       string
	UserId     int64
	CreatedAt  time.Time
	VerifiedAt *time.Time
	ExpiredAt  time.Time
}

type UserSendOTP struct {
	Phone string `json:"phone" validate:"required,number,min=10,max=12"`
}

type UserVerifyOTP struct {
	UserId int64  `json:"-"`
	Phone  string `json:"phone" validate:"required,number,min=10,max=12"`
	Code   string `json:"code" validate:"required,number,min=6,max=6"`
}

type UserVerifyOTPForget struct {
	UserId   int64  `json:"-"`
	Phone    string `json:"phone" validate:"required,number,min=10,max=12"`
	Code     string `json:"code" validate:"required,number,min=6,max=6"`
	Password string `json:"password" validate:"required,min=4,max=30"`
}

type FrontUserUpdate struct {
	Username  string  `json:"-"`
	Password  string  `json:"password" gorm:"default:NULL" validate:"min=4,max=30,containsany=**********"`
	Fullname  string  `json:"fullname" gorm:"default:NULL" validate:"max=30"`
	Firstname *string `json:"-"`
	Lastname  *string `json:"-"`
	BankId    int64   `json:"bankId" binding:"required"`
	// Bankname    string    `json:"bankname" gorm:"default:NULL" validate:"max=50"`
	// BankCode    string    `json:"bankCode" gorm:"default:NULL" validate:"max=10"`
	BankAccount string    `json:"bankAccount" gorm:"default:NULL" validate:"max=18"`
	ChannelId   int64     `json:"channelId" gorm:"default:NULL"`
	TrueWallet  string    `json:"trueWallet" gorm:"default:NULL"`
	VerifiedAt  time.Time `json:"-" gorm:"default:CURRENT_TIMESTAMP"`
	Ip          string    `json:"ip" validate:"max=20"`
}

type UserGenMemberCode struct {
	Fullname     string
	Encrypt      string
	Phone        string
	IpRegistered string
}

type UserUpdateCredit struct {
	Status bool    `json:"status"`
	Credit float64 `json:"credit"`
}

type UserUpdateBankTransaction struct {
	TransactionStatusId int64
	BeforeAmount        float64
	AfterAmount         float64
}

type UserBank struct {
	Id   int64  `json:"id"`
	Name string `json:"name"`
	Code string `json:"code"`
}

type UserBankListResponse struct {
	List []UserBank `json:"list"`
}

type ResponseGameCredit struct {
	Success      bool    `json:"success"`
	UserId       int64   `json:"userId"`
	BeforeAmount float64 `json:"beforeAmount"`
	AfterAmount  float64 `json:"afterAmount"`
}

type GetUserByMemberCode struct {
	Id          int64   `json:"id"`
	MemberCode  *string `json:"memberCode"`
	Fullname    string  `json:"fullname"`
	Username    string  `json:"username"`
	BankName    string  `json:"bankName"`
	BankCode    string  `json:"bankCode"`
	BankAccount string  `json:"bankAccount"`
	Credit      float64 `json:"credit"`
}

type UserUpdateMemberAndRef struct {
	MemberCode string
	Encrypt    string
}

type UserGetDataByRef struct {
	UserId     int64
	UserTypeId int64
}

type UserMemberList struct {
	Id         int64
	MemberCode string
}
type UserRefNo struct {
	Id    int64
	RefBy int64
}

type GetUserLogListRequest struct {
	UserId int64 `form:"userId"`
	Page   int   `form:"page" default:"1" min:"1"`
	Limit  int   `form:"limit" default:"10" min:"1" max:"100"`
}

type UserChannel struct {
	Id    int64  `json:"id"`
	Title string `json:"title"`
}

type InactiveUserResponse struct {
	Id             int64     `json:"id"`
	Username       string    `json:"username"`
	Credit         float64   `json:"credit"`
	LastActionDays int64     `json:"lastActionDays"`
	LastActionAt   time.Time `json:"lastActionAt"`
}
type InactiveUserListRequest struct {
	DateType string `form:"dateType" validate:"required"`
	Username string `form:"username"`
	Page     int    `form:"page" default:"1" min:"1"`
	Limit    int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol  string `form:"sortCol"`
	SortAsc  string `form:"sortAsc"`
}
type InactiveUserListForExcelRequest struct {
	DateType string `form:"dateType" validate:"required"`
	Username string `form:"username"`
	SortCol  string `form:"sortCol"`
	SortAsc  string `form:"sortAsc"`
}
type InactiveUserRemoveListRequest struct {
	AdminId     int64   `json:"-"`
	DateType    string  `form:"dateType" validate:"required"`
	SelectedIds []int64 `json:"selectedIds" validate:"required"`
}

type CreateUserMemberCode struct {
	Id int64 `uri:"id" binding:"required"`
}

type UserAllianceInfo struct {
	Id         int64  `json:"id"`
	RefBy      *int64 `json:"refBy"`
	MemberCode string `json:"memberCode"`
	UserTypeId int64  `json:"userTypeId"`
	Alias      string `json:"alias"`
	RefCode    string `json:"refCode"`
}

type GetUserForGenmemberByUserId struct {
	Id         int64  `json:"id"`
	RefBy      *int64 `json:"refBy"`
	MemberCode string `json:"memberCode"`
	UserTypeId int64  `json:"userTypeId"`
}

type MoveUserRefbyRequest struct {
	UserId             int64  `json:"userId"`
	NewRefByMemberCode string `json:"newRefByMemberCode"`
}
type MoveAffiliateUserMemberBody struct {
	MemberId           int64  `json:"memberId"`
	MemberCode         string `json:"memberCode"`
	OldRefBy           int64  `json:"oldRefBy"`
	OldRefByMemberCode string `json:"oldRefByMemberCode"`
	NewRefBy           int64  `json:"newRefBy"`
	NewRefByMemberCode string `json:"newRefByMemberCode"`
}

type MoveAllianceUserMemberBody struct {
	MemberId           int64  `json:"memberId"`
	MemberCode         string `json:"memberCode"`
	OldRefBy           int64  `json:"oldRefBy"`
	OldRefByMemberCode string `json:"oldRefByMemberCode"`
	NewRefBy           int64  `json:"newRefBy"`
	NewRefByMemberCode string `json:"newRefByMemberCode"`
}

type CheckMissingAffiliateReponse struct {
	UserId       int64  `json:"userId"`
	MemberCode   string `json:"memberCode"`
	UserTypeId   int64  `json:"userTypeId"`
	DepositCount int64  `json:"depositCount"`
}

type UserWithdrawSettingBody struct {
	Id                int64      `json:"id"`
	UserId            int64      `json:"userId"`
	AccumulatedAmount float64    `json:"accumulatedAmount"`
	MaximumTimePerDay float64    `json:"maximumTimePerDay"`
	UpdatedByAdminId  *int64     `json:"updatedByAdminId"`
	CreatedAt         time.Time  `json:"createdAt"`
	UpdatedAt         *time.Time `json:"updatedAt"`
	DeletedAt         *time.Time `json:"deletedAt"`
}

type UserWithdrawSettingCreateRequest struct {
	Id                int64   `json:"-"`
	UserId            int64   `json:"userId"`
	AccumulatedAmount float64 `json:"accumulatedAmount"`
	MaximumTimePerDay float64 `json:"maximumTimePerDay"`
}

type UserWithdrawSettingUpdateByUserIdRequest struct {
	UserId            int64    `json:"-"`
	AccumulatedAmount *float64 `json:"accumulatedAmount"`
	MaximumTimePerDay *float64 `json:"maximumTimePerDay"`
	UpdatedByAdminId  *int64   `json:"-"`
}

type UserWithdrawSettingGetByUserIdResponse struct {
	Id                int64   `json:"id"`
	UserId            int64   `json:"userId"`
	AccumulatedAmount float64 `json:"accumulatedAmount"`
	MaximumTimePerDay float64 `json:"maximumTimePerDay"`
	UpdatedByAdminId  *int64  `json:"updatedByAdminId"`
}

type UserWithdrawSettingGetByUserIdRequest struct {
	UserId int64 `uri:"userId" binding:"required"`
}
type UserWithdrawSettingGetByRequest struct {
	Id int64 `uri:"id" binding:"required"`
}

type UserWithdrawConfig struct {
	Id          int64   `json:"id"`
	ConfigKey   string  `json:"configKey"`
	ConfigValue float64 `json:"configValue"`
}

type UserLineLoginRequest struct {
	AccessToken  string `form:"accessToken"`
	RefreshToken string `form:"refreshToken"`
	Uuid         string `form:"uuid"`
	Image        string `form:"image"`
	Name         string `form:"name"`
	Ip           string `form:"Ip"`
}

type LineLoginLog struct {
	Id           int64  `json:"id"`
	AccessToken  string `json:"accessToken"`
	RefreshToken string `json:"refreshToken"`
	Uuid         string `json:"uuid"`
	ImageUrl     string `json:"imageUrl"`
	DisplayName  string `json:"displayName"`
	CreatedAt    string `json:"createdAt"`
	UpdatedAt    string `json:"updatedAt"`
}
type LineLoginLogCreateBody struct {
	Id           int64  `json:"id"`
	AccessToken  string `json:"accessToken"`
	RefreshToken string `json:"refreshToken"`
	Uuid         string `json:"uuid"`
	ImageUrl     string `json:"imageUrl"`
	DisplayName  string `json:"displayName"`
}

// type UserRegister struct {
// 	OtpId        string `json:"otpId"`
// 	Code         string `json:"code"`
// 	Phone        string `json:"phone" binding:"required,min=10,max=12" example:"**********"`
// 	Password     string `json:"password" binding:"required,min=4,max=255,containsany=**********"`
// 	Fullname     string `json:"fullname" binding:"required,max=255"`
// 	BankAccount  string `json:"bankAccount" binding:"required,max=18"`
// 	BankId       int64  `json:"bankId" binding:"required"`
// 	LineId       string `json:"lineId"`
// 	ChannelId    *int64 `json:"channelId"`
// 	IpRegistered string `json:"-"`
// 	RefBy        string `json:"refBy"`
// }

type UserLineRegisterRequest struct {
	AccessToken  string `form:"accessToken"`
	RefreshToken string `form:"refreshToken"`
	Uuid         string `form:"uuid"`
	Image        string `form:"image"`
	Name         string `form:"name"`
	Phone        string `form:"phone"`
	Fullname     string `form:"fullname"`
	BankAccount  string `form:"bankAccount" binding:"required,max=18"`
	BankId       int64  `form:"bankId" binding:"required"`
	ChannelId    *int64 `form:"channelId"`
	RefBy        string `form:"refBy"`
	RefCode      string `form:"refCode"`
	Ip           string `form:"Ip"`
}

type UserChannelSummaryGraphRequest struct {
	DateType string `form:"dateType"`
	FromDate string `form:"fromDate"`
	ToDate   string `form:"toDate"`
}
type UserChannelTotalCount struct {
	OfDate       string `json:"ofDate"`
	ChannelId    int64  `json:"channelId"`
	ChannelName  string `json:"channelName"`
	ChannelColor string `json:"channelColor"`
	UserCount    int64  `json:"userCount"`
}
type UserChannelGraphChannel struct {
	ChannelId     int64   `json:"channelId"`
	ChannelName   string  `json:"channelName"`
	UserCountList []int64 `json:"userCountList"`
	ChannelColor  string  `json:"channelColor"`
}
type UserChannelSummaryGraphResponse struct {
	DateList    []string                  `json:"dateList"`
	ChannelList []UserChannelGraphChannel `json:"channelList"`
}

type UserNewMemberCountResponse struct {
	UserWithMemberCode    int64 `json:"userWithMemberCode"`
	UserWithoutMemberCode int64 `json:"userWithoutMemberCode"`
	Total                 int64 `json:"total"`
}

type EncodeRefByRequest struct {
	RefBy int64 `form:"refBy"`
}
type DecodeRefByRequest struct {
	RefBy string `form:"refBy"`
}

type VerifyOtpRetry struct {
	RetryCount   int64     `json:"retryCount"`
	Phone        string    `json:"phone"`
	ExpiredAt    time.Time `json:"expiredAt"`
	ClearCacheAt time.Time `json:"clearCacheAt"`
}

type UserRawResponse struct {
	Id         int64  `json:"id"`
	MemberCode string `json:"memberCode"`
	Username   string `json:"username"`
	Phone      string `json:"phone"`
	Fullname   string `json:"fullname"`
	Firstname  string `json:"firstname"`
	Lastname   string `json:"lastname"`
}
