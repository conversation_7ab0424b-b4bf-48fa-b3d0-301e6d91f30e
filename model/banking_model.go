package model

import (
	"time"

	"gorm.io/gorm"
)

// transaction type
const (
	TRANSACTION_TYPE_DEPOSIT      int64 = 1
	TRANSACTION_TYPE_WITHDRAW     int64 = 2
	TRANSACTION_TYPE_BONUS        int64 = 3
	TRANSACTION_TYPE_CREDITBACK   int64 = 4
	TRANSACTION_TYPE_CREDITCANCEL int64 = 5
)

// transaction status
const (
	// เขียนรายละเอียกให้ พี่ mink แล้วครับ
	TRANS_STATUS_PENDING                   int64 = 1 //pending
	TRANS_STATUS_DEPOSIT_PENDING_CREDIT    int64 = 2 // pending_credit
	TRANS_STATUS_DEPOSIT_PENDING_SLIP      int64 = 3 //slip
	TRANS_STATUS_DEPOSIT_PENDING_MULTIUSER int64 = 4 //multiuser
	TRANS_STATUS_DEPOSIT_CREDIT_APPROVED   int64 = 5 //finished
	TRANS_STATUS_DEPOSIT_CREDIT_REJECTED   int64 = 6 //canceled

	TRANS_STATUS_WITHDRAW_PENDING     int64 = 7  // ค่า default
	TRANS_STATUS_WITHDRAW_OVER_BUDGET int64 = 8  // credit เกินการตั้งค่าของระบบ
	TRANS_STATUS_WITHDRAW_APPROVED    int64 = 9  // เกมยืนยัน
	TRANS_STATUS_WITHDRAW_REJECTED    int64 = 10 // เกมปฏิเสธ
	TRANS_STATUS_WITHDRAW_FAILED      int64 = 11 // ถอนไม่สำเร็จเกิดจาก fastbank
	TRANS_STATUS_WITHDRAW_SUCCESS     int64 = 12 // ถอนสำเร็จ
	TRANS_STATUS_WITHDRAW_OVER_MAX    int64 = 13 // ถอนเกิน max amount ที่ตั้งค่าไว้ //สมารถ auto
	TRANS_STATUS_WITHDRAW_CANCELED    int64 = 14 // cancel

	TRANS_STATUS_DEPOSIT_IGNORE  int64 = 15 // เพิกเฉย
	TRANS_STATUS_WITHDRAW_UNSURE int64 = 16 // ไม่แน่ใจ ว่าผ่านหรือไม่

	// [2024-04-26] เพิ่มสถานะเมื่อ รายการอยู่ในระหว่างการโอนจากธนาคารภายนอก *ไม่สามารถยกเลิกได้*
	TRANS_STATUS_WITHDRAW_TRASNFERING int64 = 17 // credit เกินการตั้งค่าของระบบ
	// ** ไปเพิ่มใน REPO ด้วยนะครับ
)

// statement type
const (
	STATEMENT_TYPE_TRANSFER_IN  int64 = 1 // transfer_in
	STATEMENT_TYPE_TRANSFER_OUT int64 = 2 // transfer_out
)

// statement status
const (
	STATEMENT_STATUS_PENDING   int64 = 1 // pending
	STATEMENT_STATUS_CONFIRMED int64 = 2 // confirmed
	STATEMENT_STATUS_IGNORED   int64 = 3 // ignored
)

// ใช้ query ดูว่าได้ record แบบไหนมา เอาไปทำ withdraw
const (
	AUTO_WITHDRAW              string = "AUTO_WITHDRAW"
	CLOSE_AUTO_WITHDRAW        string = "CLOSE_AUTO_WITHDRAW"
	OUT_OF_CONFIG_AMOUNT       string = "OUT_OF_CONFIG_AMOUNT"
	CONFIG_WEB_MANUAL_WITHDRAW int64  = 1
	CONFIG_WEB_AUTO_WITHDRAW   int64  = 2

	USER_WITHDRAW_SETTING_PASS_AUTO string = "USER_WITHDRAW_SETTING_PASS_AUTO"
	USER_WITHDRAW_SETTING_UNPASS    string = "USER_WITHDRAW_SETTING_UNPASS"
)

type GetPossibleOwnersRequest struct {
	FromBankId        int64  `form:"fromBankId"`
	ToBankId          int64  `form:"toBankId"`
	FromAccountNumber string `form:"fromAccountNumber"`
}

type StatementTypeResponse struct {
	Id      int64  `json:"id"`
	Name    string `json:"name"`
	LabelTh string `json:"labelTh"`
	LabelEn string `json:"labelEn"`
}

type TransactionTypeResponse struct {
	Id      int64  `json:"id"`
	Name    string `json:"name"`
	LabelTh string `json:"labelTh"`
	LabelEn string `json:"labelEn"`
}
type GetByIdRequest struct {
	Id int64 `uri:"id" binding:"required"`
}
type GetByIdUserRequest struct {
	Id     int64 `uri:"id" binding:"required"`
	UserId int64 `json:"userId" validate:"required"`
}
type SimpleOption struct {
	Key  string `json:"key"`
	Name string `json:"name"`
}
type OptionValueString struct {
	Value string `json:"value"`
	Label string `json:"label"`
}
type OptionValueInt struct {
	Value int64  `json:"value"`
	Label string `json:"label"`
}
type BankStatement struct {
	Id                int64          `json:"id" gorm:"primaryKey"`
	AccountId         int64          `json:"accountId"`
	ExternalId        int64          `json:"externalId"`
	Amount            float64        `json:"amount" sql:"type:decimal(14,2);"`
	Detail            string         `json:"detail"`
	BankId            int64          `json:"bankId"`
	StatementTypeId   int64          `json:"statementTypeId"`
	FromBankId        int64          `json:"fromBankId"`
	FromBankCode      string         `json:"fromBankCode"`
	FromAccountNumber string         `json:"fromAccountNumber"`
	FromBankName      string         `json:"fromBankName"`
	FromBankIconUrl   string         `json:"fromBankIconUrl"`
	TransferAt        time.Time      `json:"transferAt"`
	StatementStatusId int64          `json:"statementStatusId"`
	CreatedAt         time.Time      `json:"createAt"`
	UpdatedAt         *time.Time     `json:"updateAt"`
	DeletedAt         gorm.DeletedAt `json:"deleteAt"`
}

type SimpleListRequest struct {
	Search  string `form:"search" extensions:"x-order:1"`
	Page    int    `form:"page" extensions:"x-order:2" default:"1" min:"1"`
	Limit   int    `form:"limit" extensions:"x-order:3" default:"10" min:"1" max:"100"`
	SortCol string `form:"sortCol" extensions:"x-order:4"`
	SortAsc string `form:"sortAsc" extensions:"x-order:5"`
}

type BankStatementSummary struct {
	TotalPendingStatementCount int64 `json:"totalPendingStatementCount"`
	TotalPendingDepositCount   int64 `json:"totalPendingDepositCount"`
	TotalPendingWithdrawCount  int64 `json:"totalPendingWithdrawCount"`
}

type BankStatementListRequest struct {
	AccountId         string `form:"accountId" extensions:"x-order:1"`
	StatementTypeId   *int64 `form:"statementTypeId" extensions:"x-order:2"`
	FromTransferDate  string `form:"fromTransferDate" extensions:"x-order:3"`
	ToTransferDate    string `form:"toTransferDate" extensions:"x-order:4"`
	Search            string `form:"search" extensions:"x-order:5"`
	SimilarId         int64  `form:"similarId" extensions:"x-order:6"`
	StatementStatusId *int64 `form:"statementStatusId" extensions:"x-order:7"`
	Page              int    `form:"page" extensions:"x-order:7" default:"1" min:"1"`
	Limit             int    `form:"limit" extensions:"x-order:8" default:"10" min:"1" max:"100"`
	SortCol           string `form:"sortCol" extensions:"x-order:9"`
	SortAsc           string `form:"sortAsc" extensions:"x-order:10"`
}

type BankStatementCreateBody struct {
	Id                int64     `json:"id"`
	AccountId         int64     `json:"accountId"`
	ExternalId        *int64    `json:"externalId"`
	Amount            float64   `json:"amount" sql:"type:decimal(14,2);"`
	Detail            string    `json:"detail"`
	FromBankId        int64     `json:"fromBankId"`
	FromAccountNumber string    `json:"fromAccountNumber"`
	StatementTypeId   int64     `json:"statementTypeId"`
	TransferAt        time.Time `json:"transferAt"`
	StatementStatusId int64     `json:"statementStatusId"`
}

type BankStatementMatchRequest struct {
	StatementId        int64     `json:"statementId" validate:"required"`
	UserId             int64     `json:"userId" validate:"required"`
	ConfirmedAt        time.Time `json:"-"`
	ConfirmedByAdminId *int64    `json:"-"`
}

type BankStatementUpdateBody struct {
	Id                int64 `json:"id" validate:"required"`
	StatementStatusId int64 `json:"statementStatusId" validate:"required"`
}

type BankStatementResponse struct {
	Id                int64          `json:"id" gorm:"primaryKey"`
	AccountId         int64          `json:"accountId"`
	ExternalId        int64          `json:"externalId"`
	AccountName       string         `json:"accountName"`
	AccountNumber     string         `json:"accountNumber"`
	BankName          string         `json:"bankName"`
	Amount            float64        `json:"amount" sql:"type:decimal(14,2);"`
	Detail            string         `json:"detail"`
	FromBankId        int64          `json:"fromBankId"`
	FromBankName      string         `json:"fromBankName"`
	FromBankIconUrl   string         `json:"fromBankIconUrl"`
	StatementTypeId   int64          `json:"statementTypeId"`
	TransferAt        time.Time      `json:"transferAt"`
	StatementStatusId int64          `json:"statementStatusId"`
	CreatedAt         time.Time      `json:"createAt"`
	UpdatedAt         *time.Time     `json:"updateAt"`
	DeletedAt         gorm.DeletedAt `json:"deleteAt"`
}

type BankTransaction struct {
	Id int64 `json:"id" gorm:"primaryKey"`

	StatementId int64 `json:"statementId"`

	MemberCode        string `json:"memberCode"`
	UserId            int64  `json:"userId"`
	TransactionTypeId int64  `json:"transactionTypeId"`
	PromotionId       int64  `json:"promotionId"`

	FromAccountId     int64  `json:"fromAccountId"`
	FromBankId        int64  `json:"fromBankId"`
	FromBankCode      string `json:"fromBankCode"`
	FromBankName      string `json:"fromBankName"`
	FromAccountName   string `json:"fromAccountName"`
	FromAccountNumber string `json:"fromAccountNumber"`

	ToAccountId     int64  `json:"toAccountId"`
	ToBankId        int64  `json:"toBankId"`
	ToBankName      string `json:"toBankName"`
	ToBankCode      string `json:"toBankCode"`
	ToAccountName   string `json:"toAccountName"`
	ToAccountNumber string `json:"toAccountNumber"`

	CreditAmount   float64 `json:"creditAmount" sql:"type:decimal(14,2);"`
	CreditBack     float64 `json:"creditBack" sql:"type:decimal(14,2);"`
	DepositChannel string  `json:"depositChannel"`
	SlipImgUrl     string  `json:"slipImgUrl"`
	OverAmount     float64 `json:"overAmount" sql:"type:decimal(14,2);"`
	BonusAmount    float64 `json:"bonusAmount" sql:"type:decimal(14,2);"`
	BonusReason    string  `json:"bonusReason"`
	BeforeAmount   float64 `json:"beforeAmount" sql:"type:decimal(14,2);"`
	AfterAmount    float64 `json:"afterAmount" sql:"type:decimal(14,2);"`

	TransferAt          *time.Time     `json:"transferAt"`
	CreatedByAdminId    int64          `json:"createdByAdminId"`
	CreatedByUsername   string         `json:"createdByUsername"`
	CancelRemark        string         `json:"cancelRemark"`
	CanceledAt          *time.Time     `json:"canceledAt"`
	CanceledByAdminId   int64          `json:"canceledByAdminId"`
	CanceledByUsername  string         `json:"canceledByUsername"`
	ConfirmedAt         *time.Time     `json:"confirmedAt"`
	ConfirmedByAdminId  int64          `json:"confirmedByAdminId"`
	ConfirmedByUsername string         `json:"confirmedByUsername"`
	TransactionStatusId int64          `json:"transactionStatusId"`
	IsAutoCredit        bool           `json:"isAutoCredit"`
	CreatedAt           time.Time      `json:"createAt"`
	UpdatedAt           *time.Time     `json:"updateAt"`
	DeletedAt           gorm.DeletedAt `json:"deleteAt"`
}

type BankTransactionGetRequest struct {
	Id int64 `uri:"id" binding:"required"`
}

type BankTransactionListRequest struct {
	MemberCode       string `form:"memberCode" extensions:"x-order:2"`
	UserId           string `form:"userId" extensions:"x-order:3"`
	FromTransferDate string `form:"fromTransferDate" extensions:"x-order:4"`
	ToTransferDate   string `form:"toTransferDate" extensions:"x-order:5"`
	TransactionType  string `form:"TransactionType"`
	TransferStatus   string `form:"transferStatus" extensions:"x-order:7"`
	Search           string `form:"search" extensions:"x-order:8"`
	Page             int    `form:"page" extensions:"x-order:9" default:"1" min:"1"`
	Limit            int    `form:"limit" extensions:"x-order:10" default:"10" min:"1" max:"100"`
	SortCol          string `form:"sortCol" extensions:"x-order:11"`
	SortAsc          string `form:"sortAsc" extensions:"x-order:12"`
}

type BankTransactionCreateBody struct {
	Id                  int64      `json:"-"`
	MemberCode          string     `json:"memberCode" validate:"required"`
	UserId              int64      `json:"-"`
	StatementId         *int64     `json:"statementId"`
	TransactionTypeId   int64      `json:"transactionTypeId" validate:"required" example:"1"`
	PromotionId         *int64     `json:"promotionId"`
	FromAccountId       *int64     `json:"fromAccountId"`
	FromBankId          *int64     `json:"-"`
	FromAccountName     *string    `json:"-"`
	FromAccountNumber   *string    `json:"-"`
	ToAccountId         *int64     `json:"toAccountId"`
	ToBankId            *int64     `json:"-"`
	ToAccountName       *string    `json:"-"`
	ToAccountNumber     *string    `json:"-"`
	CreditAmount        float64    `json:"creditAmount"`
	CurrencyAmount      *float64   `json:"currencyAmount"`
	CreditBack          float64    `json:"-"`
	DepositChannel      string     `json:"depositChannel"`
	OverAmount          float64    `json:"overAmount"`
	BonusAmount         float64    `json:"bonusAmount"`
	BonusReason         string     `json:"bonusReason"`
	BeforeAmount        float64    `json:"-"`
	AfterAmount         float64    `json:"-"`
	TransferAt          *time.Time `json:"transferAt" example:"2023-05-31T22:33:44+07:00"`
	SlipImgUrl          string     `json:"slipImgUrl"`
	CreatedByAdminId    int64      `json:"-"`
	TransactionStatusId int64      `json:"-"`
	IsAutoCredit        bool       `json:"isAutoCredit"`
}
type BankTransactionNoOwnerCreateBody struct {
	Id                  int64      `json:"-"`
	TransferAt          *time.Time `json:"transferAt" example:"2023-05-31T22:33:44+07:00"`
	CreatedByAdminId    int64      `json:"-"`
	TransactionTypeId   int64      `json:"transactionTypeId"`
	FromAccountNumber   string     `json:"fromAccountNumber"`
	FromBankId          int64      `json:"fromBankId"`
	CreditAmount        float64    `json:"creditAmount" validate:"required"`
	DepositChannel      string     `json:"depositChannel"`
	OverAmount          float64    `json:"overAmount"`
	IsAutoCredit        bool       `json:"isAutoCredit"`
	ToAccountId         *int64     `json:"toAccountId"`
	PromotionId         *int64     `json:"promotionId"`
	StatementId         int64      `json:"statementId"`
	ToBankId            *int64     `json:"-"`
	ToAccountName       *string    `json:"-"`
	ToAccountNumber     *string    `json:"-"`
	TransactionStatusId int64      `json:"-"`
}

type BankTransactionUpdateOwnerBody struct {
	Id                  int64   `json:"-"`
	MemberCode          string  `json:"memberCode" validate:"required"`
	UserId              int64   `json:"userId"`
	FromAccountId       *int64  `json:"fromAccountId"`
	FromBankId          *int64  `json:"fromBankId"`
	FromAccountName     *string `json:"fromAccountName"`
	FromAccountNumber   *string `json:"fromAccountNumber"`
	PromotionId         *int64  `json:"promotionId"`
	TransactionStatusId int64   `json:"transactionStatusId"`
	CreatedByAdminId    int64   `json:"-"`
	CreatedByUsername   string  `json:"-"`
	TransactionTypeId   int64   `json:"transactionTypeId"`
}

type BonusTransactionCreateBody struct {
	MemberCode          string    `json:"memberCode" validate:"required"`
	UserId              int64     `json:"-"`
	TransactionTypeId   int64     `json:"-"`
	ToAccountId         int64     `json:"-"`
	ToBankId            int64     `json:"-"`
	ToAccountName       string    `json:"-"`
	ToAccountNumber     string    `json:"-"`
	BonusAmount         float64   `json:"bonusAmount" validate:"required"`
	BonusReason         string    `json:"bonusReason"`
	BeforeAmount        float64   `json:"-"`
	AfterAmount         float64   `json:"-"`
	TransferAt          time.Time `json:"transferAt" validate:"required" example:"2023-05-31T22:33:44+07:00"`
	CreatedByAdminId    int64     `json:"-"`
	TransactionStatusId int64     `json:"-"`
}

type BankTransactionUpdateRequest struct {
	IsAutoCredit      *bool  `json:"isAutoCredit"`
	UpdatedByUserId   int64  `json:"-"`
	UpdatedByUsername string `json:"-"`
}

type BankTransactionUpdateBody struct {
	IsAutoCredit bool   `json:"isAutoCredit"`
	Status       string `json:"status"`
}

type BankTransactionResponse struct {
	Id                  int64          `json:"id" gorm:"primaryKey"`
	UserId              int64          `json:"userId"`
	MemberCode          string         `json:"memberCode"`
	UserUsername        string         `json:"userUsername"`
	UserFullname        string         `json:"userFullname"`
	TransactionTypeId   int64          `json:"transactionTypeId"`
	PromotionId         int64          `json:"promotionId"`
	FromAccountId       int64          `json:"fromAccountId"`
	FromBankId          int64          `json:"fromBankId"`
	FromBankName        string         `json:"fromBankName"`
	FromAccountName     string         `json:"fromAccountName"`
	FromAccountNumber   string         `json:"fromAccountNumber"`
	ToAccountId         int64          `json:"toAccountId"`
	ToBankId            int64          `json:"toBankId"`
	ToBankName          string         `json:"toBankName"`
	ToAccountName       string         `json:"toAccountName"`
	ToAccountNumber     string         `json:"toAccountNumber"`
	CreditAmount        float64        `json:"creditAmount" sql:"type:decimal(14,2);"`
	CreditBack          float64        `json:"creditBack" sql:"type:decimal(14,2);"`
	DepositChannel      string         `json:"depositChannel"`
	OverAmount          float64        `json:"overAmount" sql:"type:decimal(14,2);"`
	BonusAmount         float64        `json:"bonusAmount" sql:"type:decimal(14,2);"`
	BonusReason         string         `json:"bonusReason"`
	BeforeAmount        float64        `json:"beforeAmount" sql:"type:decimal(14,2);"`
	AfterAmount         float64        `json:"afterAmount" sql:"type:decimal(14,2);"`
	TransferAt          *time.Time     `json:"transferAt"`
	CreatedByAdminId    int64          `json:"createdByAdminId"`
	CreatedByUsername   string         `json:"createdByUsername"`
	CancelRemark        string         `json:"cancelRemark"`
	CanceledAt          *time.Time     `json:"canceledAt"`
	CanceledByAdminId   int64          `json:"canceledByAdminId"`
	CanceledByUsername  string         `json:"canceledByUsername"`
	ConfirmedAt         *time.Time     `json:"confirmedAt"`
	ConfirmedByAdminId  int64          `json:"confirmedByAdminId"`
	ConfirmedByUsername string         `json:"confirmedByUsername"`
	TransactionStatusId int64          `json:"transactionStatusId"`
	IsAutoCredit        bool           `json:"isAutoCredit"`
	CreatedAt           time.Time      `json:"createAt"`
	UpdatedAt           *time.Time     `json:"updateAt"`
	DeletedAt           gorm.DeletedAt `json:"deleteAt"`
}

type BankTransactionStatusCount struct {
	TransactionStatusId int64 `json:"transactionStatusId"`
	Count               int64 `json:"count"`
}

type BankDepositTransStatusCounts struct {
	AllCount            int64 `json:"allCount"`
	PendingAllCount     int64 `json:"pendingAllCount"`
	PendingCount        int64 `json:"pendingCount"`
	PendingCreditCount  int64 `json:"pendingCreditCount"`
	CreditApprovedCount int64 `json:"creditApprovedCount"`
	CreditRejected      int64 `json:"creditRejected"`
}
type BankWithdrawTransStatusCounts struct {
	AllCount             int64 `json:"allCount"`
	PendingAllCount      int64 `json:"pendingAllCount"`
	PendingCreditCount   int64 `json:"pendingCreditCount"`
	PendingTransferCount int64 `json:"pendingTransferCount"`
	SuccessCount         int64 `json:"successCount"`
	FailedCount          int64 `json:"failedCount"`
	CanceledCount        int64 `json:"canceledCount"`
	TransferIngCount     int64 `json:"transferIngCount"`
}

type PendingDepositTransactionListRequest struct {
	FromTransferDate string `form:"fromTransferDate" extensions:"x-order:3"`
	ToTransferDate   string `form:"toTransferDate" extensions:"x-order:4"`
	Search           string `form:"search" extensions:"x-order:5"`
	Page             int    `form:"page" extensions:"x-order:6" default:"1" min:"1"`
	Limit            int    `form:"limit" extensions:"x-order:7" default:"10" min:"1" max:"100"`
	SortCol          string `form:"sortCol" extensions:"x-order:8"`
	SortAsc          string `form:"sortAsc" extensions:"x-order:9"`
}

type PendingWithdrawTransactionListRequest struct {
	FromTransferDate string `form:"fromTransferDate" extensions:"x-order:3"`
	ToTransferDate   string `form:"toTransferDate" extensions:"x-order:4"`
	Search           string `form:"search" extensions:"x-order:5"`
	Page             int    `form:"page" extensions:"x-order:6" default:"1" min:"1"`
	Limit            int    `form:"limit" extensions:"x-order:7" default:"10" min:"1" max:"100"`
	SortCol          string `form:"sortCol" extensions:"x-order:8"`
	SortAsc          string `form:"sortAsc" extensions:"x-order:9"`
}

type FinishedTransactionListRequest struct {
	FromTransferDate  string `form:"fromTransferDate" extensions:"x-order:1"`
	ToTransferDate    string `form:"toTransferDate" extensions:"x-order:2"`
	AccountId         string `form:"accountId" extensions:"x-order:3"`
	TransactionTypeId *int64 `form:"transactionTypeId" extensions:"x-order:4"`
	Search            string `form:"search" extensions:"x-order:5"`
	Page              int    `form:"page" extensions:"x-order:6" default:"1" min:"1"`
	Limit             int    `form:"limit" extensions:"x-order:7" default:"10" min:"1" max:"100"`
	SortCol           string `form:"sortCol" extensions:"x-order:8"`
	SortAsc           string `form:"sortAsc" extensions:"x-order:9"`
}

type RemovedTransactionListRequest struct {
	FromTransferDate  string `form:"fromTransferDate" extensions:"x-order:1"`
	ToTransferDate    string `form:"toTransferDate" extensions:"x-order:2"`
	AccountId         string `form:"accountId" extensions:"x-order:3"`
	TransactionTypeId *int64 `form:"transactionTypeId" extensions:"x-order:4"`
	Search            string `form:"search" extensions:"x-order:5"`
	Page              int    `form:"page" extensions:"x-order:6" default:"1" min:"1"`
	Limit             int    `form:"limit" extensions:"x-order:7" default:"10" min:"1" max:"100"`
	SortCol           string `form:"sortCol" extensions:"x-order:8"`
	SortAsc           string `form:"sortAsc" extensions:"x-order:9"`
}

type BankTransactionCancelBody struct {
	TransactionStatusId int64     `json:"-"`
	CancelRemark        string    `json:"cancelRemark" validate:"required"`
	CanceledAt          time.Time `json:"-"`
	CanceledByAdminId   int64     `json:"-"`
}

type BankConfirmDepositRequest struct {
	TransferAt         *time.Time `json:"transferAt"`
	SlipUrl            *string    `json:"slipUrl"`
	BonusAmount        *float64   `json:"bonusAmount"`
	ConfirmedAt        time.Time  `json:"-"`
	ConfirmedByAdminId *int64     `json:"-"`
}

type BankConfirmCreditWithdrawRequest struct {
	FromAccountId      *int64    `json:"fromAccountId"`
	CreditAmount       *float64  `json:"creditAmount"`
	ConfirmedAt        time.Time `json:"-"`
	ConfirmedByAdminId *int64    `json:"-"`
}
type BankConfirmTransferWithdrawRequest struct {
	FromAccountId      *int64    `json:"fromAccountId"`
	ConfirmedAt        time.Time `json:"-"`
	ConfirmedByAdminId *int64    `json:"-"`
}

type BankDepositTransactionConfirmBody struct {
	TransferAt          time.Time `json:"transferAt"`
	BonusAmount         float64   `json:"bonusAmount"`
	TransactionStatusId int64     `json:"transactionStatusId"`
	ConfirmedAt         time.Time `json:"confirmedAt"`
	ConfirmedByAdminId  *int64    `json:"confirmedByAdminId"`
	AutoProcessTimer    *string   `json:"autoProcessTimer"`
}

type BankWithdrawTransactionConfirmBody struct {
	FromAccountId       *int64    `json:"fromAccountId"`
	TransferAt          time.Time `json:"transferAt"`
	CreditAmount        float64   `json:"creditAmount"`
	TransactionStatusId int64     `json:"transactionStatusId"`
	ConfirmedAt         time.Time `json:"confirmedAt"`
	ConfirmedByAdminId  *int64    `json:"confirmedByAdminId"`
}

type CreateBankTransactionActionBody struct {
	Id                 int64      `json:"id"`
	ActionKey          string     `json:"actionKey"`
	TransactionId      int64      `json:"transactionId"`
	UserId             int64      `json:"userId"`
	TransactionTypeId  int64      `json:"transactionTypeId"`
	FromAccountId      int64      `json:"fromAccountId"`
	ToAccountId        int64      `json:"toAccountId"`
	JsonBefore         string     `json:"jsonBefore"`
	TransferAt         *time.Time `json:"transferAt"`
	SlipUrl            string     `json:"slipUrl"`
	BonusAmount        float64    `json:"bonusAmount"`
	CreditAmount       float64    `json:"creditAmount"`
	ConfirmedAt        time.Time  `json:"confirmedAt"`
	ConfirmedByAdminId *int64     `json:"confirmedByAdminId"`
}

type CreateBankStatementActionBody struct {
	StatementId        int64     `json:"statementId"`
	UserId             int64     `json:"userId"`
	ActionType         string    `json:"actionType"`
	AccountId          int64     `json:"accountId"`
	JsonBefore         string    `json:"jsonBefore"`
	ConfirmedAt        time.Time `json:"confirmedAt"`
	ConfirmedByAdminId *int64    `json:"confirmedByAdminId"`
}
type BankTransactionRemoveBody struct {
	Status string `json:"-" validate:"required"`
}
type BankAutoWithdrawCondition struct {
	TransId         int64   `json:"TransId"`
	TransStatusId   int64   `json:"TransStatusId"`
	UserId          int64   `json:"UserId"`
	FromAccountId   int64   `json:"toAccountId"`
	CreditAmount    float64 `json:"CreditAmount"`
	MinCreditAmount float64 `json:"minCreditAmount"`
	MaxCreditAmount float64 `json:"maxCreditAmount"`
	// AutoWithdrawCreditFlag  string  `json:"autoWithdrawCreditFlag"`
	// AutoWithdrawConfirmFlag string  `json:"autoWithdrawConfirmFlag"`
}

type Member struct {
	Id           int64   `json:"id"`
	RefBy        *int64  `json:"refBy"`
	MemberCode   string  `json:"memberCode"`
	UserTypeId   int64   `json:"userTypeId"`
	UserTypeName string  `json:"userTypeName"`
	Username     string  `json:"username"`
	Phone        string  `json:"phone"`
	Fullname     string  `json:"fullname"`
	Credit       float64 `json:"credit"`
	BankAccount  string  `json:"bankAccount"`
	BankId       int64   `json:"bankId"`
	BankName     string  `json:"bankName"`
}

type MemberForDropdown struct {
	Id          int64  `json:"id"`
	MemberCode  string `json:"memberCode"`
	BankId      int64  `json:"bankId"`
	BankName    string `json:"bankName"`
	BankCode    string `json:"bankCode"`
	BankAccount string `json:"bankAccount"`
	Fullname    string `json:"fullname"`
}

type MemberForDropdownResponse struct {
	Result []MemberForDropdown `json:"result"`
}

type MemberListRequest struct {
	Search  string `form:"search" extensions:"x-order:1"`
	Page    int    `form:"page" extensions:"x-order:7" default:"1" min:"1"`
	Limit   int    `form:"limit" extensions:"x-order:8" default:"10" min:"1" max:"100"`
	SortCol string `form:"sortCol" extensions:"x-order:9"`
	SortAsc string `form:"sortAsc" extensions:"x-order:10"`
}
type MemberPossibleListRequest struct {
	UnknownStatementId int64   `form:"unknownStatementId" extensions:"x-order:1"`
	UserAccountNumber  *string `form:"userAccountNumber" extensions:"x-order:2"`
	UserBankCode       *string `form:"userBankCode" extensions:"x-order:3"`
	Page               int     `form:"page" extensions:"x-order:7" default:"1" min:"1"`
	Limit              int     `form:"limit" extensions:"x-order:8" default:"10" min:"1" max:"100"`
	SortCol            string  `form:"sortCol" extensions:"x-order:9"`
	SortAsc            string  `form:"sortAsc" extensions:"x-order:10"`
}
type MemberTransaction struct {
	Id                  int64          `json:"id" gorm:"primaryKey"`
	UserId              int64          `json:"userId"`
	MemberCode          string         `json:"memberCode"`
	UserUsername        string         `json:"userUsername"`
	UserFullname        string         `json:"userFullname"`
	TransactionTypeId   int64          `json:"transactionTypeId"`
	TransactionTypeTh   string         `json:"transactionTypeTh"`
	TransactionTypeEn   string         `json:"transactionTypeEn"`
	PromotionId         int64          `json:"promotionId"`
	FromAccountId       int64          `json:"fromAccountId"`
	FromBankId          int64          `json:"fromBankId"`
	FromBankName        string         `json:"fromBankName"`
	FromAccountName     string         `json:"fromAccountName"`
	FromAccountNumber   string         `json:"fromAccountNumber"`
	ToAccountId         int64          `json:"toAccountId"`
	ToBankId            int64          `json:"toBankId"`
	ToBankName          string         `json:"toBankName"`
	ToAccountName       string         `json:"toAccountName"`
	ToAccountNumber     string         `json:"toAccountNumber"`
	CreditAmount        float64        `json:"creditAmount" sql:"type:decimal(14,2);"`
	CreditBack          float64        `json:"creditBack" sql:"type:decimal(14,2);"`
	DepositChannel      string         `json:"depositChannel"`
	OverAmount          float64        `json:"overAmount" sql:"type:decimal(14,2);"`
	BonusAmount         float64        `json:"bonusAmount" sql:"type:decimal(14,2);"`
	BonusReason         string         `json:"bonusReason"`
	BeforeAmount        float64        `json:"beforeAmount" sql:"type:decimal(14,2);"`
	AfterAmount         float64        `json:"afterAmount" sql:"type:decimal(14,2);"`
	TransferAt          time.Time      `json:"transferAt"`
	CreatedByAdminId    int64          `json:"createdByAdminId"`
	CreatedByUsername   string         `json:"createdByUsername"`
	CancelRemark        string         `json:"cancelRemark"`
	CanceledAt          time.Time      `json:"canceledAt"`
	CanceledByAdminId   int64          `json:"canceledByAdminId"`
	CanceledByUsername  string         `json:"canceledByUsername"`
	ConfirmedAt         *time.Time     `json:"confirmedAt"`
	ConfirmedByAdminId  int64          `json:"confirmedByAdminId"`
	ConfirmedByUsername string         `json:"confirmedByUsername"`
	Status              string         `json:"status"`
	IsAutoCredit        bool           `json:"isAutoCredit"`
	CreatedAt           time.Time      `json:"createAt"`
	UpdatedAt           *time.Time     `json:"updateAt"`
	DeletedAt           gorm.DeletedAt `json:"deleteAt"`
}
type MemberTransactionListRequest struct {
	UserId            string `form:"userId" extensions:"x-order:1"`
	FromTransferDate  string `form:"fromTransferDate" extensions:"x-order:2"`
	ToTransferDate    string `form:"toTransferDate" extensions:"x-order:3"`
	TransactionTypeId *int64 `form:"transactionTypeId"`
	Search            string `form:"search" extensions:"x-order:5"`
	Page              int    `form:"page" extensions:"x-order:6" default:"1" min:"1"`
	Limit             int    `form:"limit" extensions:"x-order:7" default:"10" min:"1" max:"100"`
	SortCol           string `form:"sortCol" extensions:"x-order:8"`
	SortAsc           string `form:"sortAsc" extensions:"x-order:9"`
}
type MemberTransactionSummary struct {
	TotalDepositAmount  float64 `json:"totalDepositAmount"`
	TotalWithdrawAmount float64 `json:"totalWithdrawAmount"`
	TotalBonusAmount    float64 `json:"totalBonusAmount"`
}

type MemberStatementType struct {
	Id        int64          `json:"id" gorm:"primaryKey"`
	Code      string         `json:"code"`
	Name      string         `json:"name"`
	CreatedAt time.Time      `json:"createAt"`
	UpdatedAt *time.Time     `json:"updateAt"`
	DeletedAt gorm.DeletedAt `json:"deleteAt"`
}
type MemberStatement struct {
	Id              int64          `json:"id" gorm:"primaryKey"`
	UserId          int64          `json:"userId"`
	StatementTypeId int64          `json:"statementTypeId"`
	TransferAt      time.Time      `json:"transferAt"`
	Info            string         `json:"info"`
	BeforeBalance   float64        `json:"beforeBalance" sql:"type:decimal(14,2);"`
	Amount          float64        `json:"amount" sql:"type:decimal(14,2);"`
	AfterBalance    float64        `json:"afterBalance" sql:"type:decimal(14,2);"`
	CreatedAt       time.Time      `json:"createAt"`
	UpdatedAt       *time.Time     `json:"updateAt"`
	DeletedAt       gorm.DeletedAt `json:"deleteAt"`
}
type MemberStatementCreateRequest struct {
	UserId int64   `json:"userId"`
	Amount float64 `json:"amount"`
}
type MemberStatementCreateBody struct {
	Id              int64 `json:"id"`
	UserId          int64 `json:"userId"`
	StatementTypeId int64 `json:"statementTypeId"`
	// TransferAt      time.Time `json:"transferAt"`
	Info string `json:"info"`
	// BeforeBalance   float64   `json:"beforeBalance" sql:"type:decimal(14,2);"`
	Amount float64 `json:"amount" sql:"type:decimal(14,2);"`
	// AfterBalance    float64   `json:"afterBalance" sql:"type:decimal(14,2);"`
}

type CreateUserStatement struct {
	Id              int64     `json:"id"`
	UserId          int64     `json:"userId"`
	StatementId     int64     `json:"statementId"`
	StatementTypeId int64     `json:"statementTypeId"`
	TransferAt      time.Time `json:"transferAt"`
	Info            string    `json:"info"`
	BeforeBalance   float64   `json:"beforeBalance" sql:"type:decimal(14,2);"`
	Amount          float64   `json:"amount" sql:"type:decimal(14,2);"`
	AfterBalance    float64   `json:"afterBalance" sql:"type:decimal(14,2);"`
}
type MemberStatementListRequest struct {
	UserId           string `form:"userId" extensions:"x-order:1"`
	StatementTypeId  *int64 `form:"statementTypeId" extensions:"x-order:2"`
	FromTransferDate string `form:"fromTransferDate" extensions:"x-order:3"`
	ToTransferDate   string `form:"toTransferDate" extensions:"x-order:4"`
	Search           string `form:"search" extensions:"x-order:5"`
	Page             int    `form:"page" extensions:"x-order:6" default:"1" min:"1"`
	Limit            int    `form:"limit" extensions:"x-order:7" default:"10" min:"1" max:"100"`
	SortCol          string `form:"sortCol" extensions:"x-order:8"`
	SortAsc          string `form:"sortAsc" extensions:"x-order:9"`
}
type MemberStatementResponse struct {
	Id                int64          `json:"id" gorm:"primaryKey"`
	UserId            int64          `json:"userId"`
	MemberCode        string         `json:"memberCode"`
	UserUsername      string         `json:"userUsername"`
	UserFullname      string         `json:"userFullname"`
	StatementTypeId   int64          `json:"statementTypeId"`
	StatementTypeName string         `json:"statementTypeName"`
	TransferAt        time.Time      `json:"transferAt"`
	Info              string         `json:"info"`
	BeforeBalance     float64        `json:"beforeBalance" sql:"type:decimal(14,2);"`
	Amount            float64        `json:"amount" sql:"type:decimal(14,2);"`
	AfterBalance      float64        `json:"afterBalance" sql:"type:decimal(14,2);"`
	CreatedAt         time.Time      `json:"createAt"`
	UpdatedAt         *time.Time     `json:"updateAt"`
	DeletedAt         gorm.DeletedAt `json:"deleteAt"`
}

type GetBankTransactionDepositCountRequest struct {
	FromTransferDate  string `form:"fromTransferDate"`
	ToTransferDate    string `form:"toTransferDate"`
	TransactionTypeId *int64 `form:"transactionTypeId"`
	Search            string `form:"search"`
	TransactionStatus string `form:"transactionStatus"`
}
type GetBankTransactionDepositListRequest struct {
	FromTransferDate  string `form:"fromTransferDate"`
	ToTransferDate    string `form:"toTransferDate"`
	TransactionTypeId *int64 `form:"transactionTypeId"`
	ActionByAdminId   *int64 `form:"actionByAdminId"`
	ActionByAdminType string `form:"actionByAdminType"`
	Search            string `form:"search"`
	Page              int    `form:"page" default:"1" min:"1"`
	Limit             int    `form:"limit" default:"10" min:"1" max:"100"`
	TransactionStatus string `form:"transactionStatus"`
}

type GetBankTransactionWithdrawCountRequest struct {
	FromTransferDate  string `form:"fromTransferDate"`
	ToTransferDate    string `form:"toTransferDate"`
	TransactionTypeId *int64 `form:"transactionTypeId"`
	Search            string `form:"search"`
	TransactionStatus string `form:"transactionStatus"`
}
type GetBankTransactionWithdrawListRequest struct {
	FromTransferDate  string `form:"fromTransferDate"`
	ToTransferDate    string `form:"toTransferDate"`
	TransactionTypeId *int64 `form:"transactionTypeId"`
	ActionByAdminId   *int64 `form:"actionByAdminId"`
	ActionByAdminType string `form:"actionByAdminType"`
	Search            string `form:"search"`
	Page              int    `form:"page" default:"1" min:"1"`
	Limit             int    `form:"limit" default:"10" min:"1" max:"100"`
	TransactionStatus string `form:"transactionStatus"`
}
type GetBankTransactionSuccessListRequest struct {
	TransferDate      string `form:"transferDate"`
	TransactionTypeId *int64 `form:"transactionTypeId"`
	BankAccountId     *int64 `form:"bankAccountId"`
	OrderBy           string `form:"orderBy"`
	Page              int    `form:"page" default:"1" min:"1"`
	Limit             int    `form:"limit" default:"10" min:"1" max:"100"`
}

type GetBankTransactionDepositListResponse struct {
	Id                  int64     `json:"id" gorm:"primaryKey"`
	StatementId         int64     `json:"statementId"`
	PromotionId         int64     `json:"promotionId"`
	CreditAmount        float64   `json:"creditAmount" sql:"type:decimal(14,2);"`
	CreditBack          float64   `json:"creditBack" sql:"type:decimal(14,2);"`
	AfterAmount         float64   `json:"afterAmount" sql:"type:decimal(14,2);"`
	BeforeAmount        float64   `json:"beforeAmount" sql:"type:decimal(14,2);"`
	DepositChannel      string    `json:"depositChannel"`
	OverAmount          float64   `json:"overAmount" sql:"type:decimal(14,2);"`
	BonusAmount         float64   `json:"bonusAmount" sql:"type:decimal(14,2);"`
	FromAccountId       int64     `json:"fromAccountId"`
	FromBankId          int64     `json:"fromBankId"`
	FromBankName        string    `json:"fromBankName"`
	FromAccountName     string    `json:"fromAccountName"`
	FromAccountName2    string    `json:"-"`
	FromAccountNumber   string    `json:"fromAccountNumber"`
	FromAccountNumber2  string    `json:"-"`
	ConfirmedByUsername string    `json:"confirmedByUsername"`
	CreatedByUsername   string    `json:"createdByUsername"`
	IsAutoCredit        bool      `json:"isAutoCredit"`
	TransferAt          time.Time `json:"transferAt"`
	// UserId use to join with Users table
	UserId         int64  `json:"userId"`
	UserMemberCode string `json:"userMemberCode"`
	UserFullname   string `json:"userFullname"`
	Username       string `json:"username"`
	// TransactionTypeId use to join with transaction_type table
	TransactionTypeId int64  `json:"transactionTypeId"`
	TransactionTypeTh string `json:"transactionTypeTh"`
	TransactionTypeEn string `json:"transactionTypeEn"`
	// ToAccountId use to join with account table
	ToAccountId      int64  `json:"toAccountId"`
	ToBankId         int64  `json:"toBankId"`
	ToBankName       string `json:"toBankName"`
	ToAccountName    string `json:"toAccountName"`
	ToAccountName2   string `json:"-"`
	ToAccountNumber  string `json:"toAccountNumber"`
	ToAccountNumber2 string `json:"-"`
	//transaction_status table use to join with transaction_status table
	TransactionStatusId int64  `json:"transactionStatusId"`
	TransactionStatusTh string `json:"transactionStatusTh"`
	TransactionStatusEn string `json:"transactionStatusEn"`
	FromBankIconUrl     string `json:"fromBankIconUrl"`
	ToBankIconUrl       string `json:"toBankIconUrl"`
	// statement
	Detail         string     `json:"detail"`
	Status         string     `json:"status"`
	CancelRemark   string     `json:"cancelRemark"`
	SlipImgUrl     string     `json:"slipImgUrl"`
	CurrencyAmount float64    `json:"currencyAmount"`
	ConfirmedAt    *time.Time `json:"confirmedAt"`
}
type GetBankTransactionWithdrawListResponse struct {
	Id                  int64     `json:"id" gorm:"primaryKey"`
	StatementId         int64     `json:"statementId"`
	PromotionId         int64     `json:"promotionId"`
	CreditAmount        float64   `json:"creditAmount" sql:"type:decimal(14,2);"`
	CreditBack          float64   `json:"creditBack" sql:"type:decimal(14,2);"`
	CreditBackReason    string    `json:"creditBackReason"`
	DepositChannel      string    `json:"depositChannel"`
	SlipImgUrl          string    `json:"slipImgUrl"`
	OverAmount          float64   `json:"overAmount" sql:"type:decimal(14,2);"`
	BonusAmount         float64   `json:"bonusAmount" sql:"type:decimal(14,2);"`
	FromAccountId       int64     `json:"fromAccountId"`
	FromBankId          int64     `json:"fromBankId"`
	FromBankName        string    `json:"fromBankName"`
	FromAccountNumber   string    `json:"fromAccountNumber"`
	FromAccountName2    string    `json:"-"`
	FromAccountNumber2  string    `json:"-"`
	ConfirmedByUsername string    `json:"confirmedByUsername"`
	CreatedByUsername   string    `json:"createdByUsername"`
	IsAutoCredit        bool      `json:"isAutoCredit"`
	TransferAt          time.Time `json:"transferAt"`
	// UserId use to join with Users table
	UserId         int64  `json:"userId"`
	UserMemberCode string `json:"userMemberCode"`
	UserFullname   string `json:"userFullname"`
	Username       string `json:"username"`
	// TransactionTypeId use to join with transaction_type table
	TransactionTypeId int64  `json:"transactionTypeId"`
	TransactionTypeTh string `json:"transactionTypeTh"`
	TransactionTypeEn string `json:"transactionTypeEn"`
	// ToAccountId use to join with account table
	ToAccountId     int64  `json:"toAccountId"`
	ToBankId        int64  `json:"toBankId"`
	ToBankName      string `json:"toBankName"`
	ToAccountName   string `json:"toAccountName"`
	ToAccountNumber string `json:"toAccountNumber"`
	//transaction_status table use to join with transaction_status table
	TransactionStatusId int64      `json:"transactionStatusId"`
	TransactionStatusTh string     `json:"transactionStatusTh"`
	TransactionStatusEn string     `json:"transactionStatusEn"`
	ConfirmedAt         *time.Time `json:"confirmedAt"`
	ConfirmedByAdminId  int64      `json:"confirmedByAdminId"`
	FromBankIconUrl     string     `json:"fromBankIconUrl"`
	ToBankIconUrl       string     `json:"toBankIconUrl"`
	Status              string     `json:"status"`
	CancelRemark        string     `json:"cancelRemark"`
	CanceledAt          *time.Time `json:"canceledAt"`
	AfterAmount         float64    `json:"afterAmount"`
	IsAllowAuto         bool       `json:"isAllowAuto"`
	CreatedAt           time.Time  `json:"createdAt"`
	PaymentType         string     `json:"paymentType"`
	IsPayment           bool       `json:"isPayment"`
}

type GetBankTransactionSuccessListResponse struct {
	Id              int64   `json:"id" gorm:"primaryKey"`
	StatementId     int64   `json:"statementId"`
	UserMemberCode  string  `json:"userMemberCode"`
	PromotionId     int64   `json:"promotionId"`
	Promotion       string  `json:"promotion"`
	PromotionDetail string  `json:"promotionDetail"`
	CreditBack      float64 `json:"creditBack" sql:"type:decimal(14,2);"`
	CreditAmount    float64 `json:"creditAmount" sql:"type:decimal(14,2);"`
	BonusAmount     float64 `json:"bonusAmount" sql:"type:decimal(14,2);"`
	AfterAmount     float64 `json:"afterAmount" sql:"type:decimal(14,2);"`

	ConfirmedByUsername string    `json:"confirmedByUsername"`
	CreatedByUsername   string    `json:"createdByUsername"`
	TransferAt          time.Time `json:"transferAt"`
	CreateAt            time.Time `json:"createAt"`
	// UserId use to join with Users table
	UserId       int64  `json:"userId"`
	UserFullname string `json:"userFullname"`
	Username     string `json:"username"`
	// TransactionTypeId use to join with transaction_type table
	TransactionTypeId int64  `json:"transactionTypeId"`
	TransactionTypeTh string `json:"transactionTypeTh"`
	TransactionTypeEn string `json:"transactionTypeEn"`

	IsAutoCredit     bool   `json:"isAutoCredit"`
	AutoProcessTimer string `json:"autoProcessTimer"`
}

type QrCodeResponse struct {
	Type   string `json:"type"`
	Symbol []struct {
		Seq   int    `json:"seq"`
		Data  string `json:"data"`
		Error string `json:"error"`
	} `json:"symbol"`
}

type GetUserTransactionListRequest struct {
	UserId            int64  `json:"-"`
	FromTransferDate  string `form:"fromTransferDate"`
	ToTransferDate    string `form:"toTransferDate"`
	TransactionTypeId *int64 `form:"transactionTypeId"`
	Page              int    `form:"page" default:"1" min:"1"`
	Limit             int    `form:"limit" default:"10" min:"1" max:"100"`
}

type GetUserTransactionListBody struct {
	Id                  int64     `json:"id" gorm:"primaryKey"`
	StatementId         int64     `json:"statementId"`
	CreditAmount        float64   `json:"creditAmount" sql:"type:decimal(14,2);"`
	BeforeAmount        float64   `json:"beforeAmount" sql:"type:decimal(14,2);"`
	AfterAmount         float64   `json:"afterAmount" sql:"type:decimal(14,2);"`
	TransferAt          time.Time `json:"transferAt"`
	TransactionStatusId int64     `json:"transactionStatusId"`
	TransactionStatusTh string    `json:"transactionStatusTh"`
	TransactionTypeId   int64     `json:"transactionTypeId"`
	TransactionTypeTh   string    `json:"transactionTypeTh"`
}

type GetUserTransactionListResponse struct {
	Id                    int64     `json:"id" gorm:"primaryKey"`
	StatementId           int64     `json:"statementId"`
	CreditAmount          float64   `json:"creditAmount" sql:"type:decimal(14,2);"`
	BeforeAmount          float64   `json:"beforeAmount" sql:"type:decimal(14,2);"`
	AfterAmount           float64   `json:"afterAmount" sql:"type:decimal(14,2);"`
	TransferAt            time.Time `json:"transferAt"`
	TransactionStatusId   int64     `json:"transactionStatusId"`
	TransactionStatusName string    `json:"transactionStatusName"`
	TransactionTypeId     int64     `json:"transactionTypeId"`
	TransactionTypeTh     string    `json:"transactionTypeTh"`
}

type UserCreateWithdrawTransactionRequest struct {
	UserId   int64     `json:"-"`
	Amount   float64   `form:"amount"`
	ActionAt time.Time `form:"actionAt" json:"-"`
}
type UserCreatePaygateLuckyWithdrawRequest struct {
	UserId      int64   `form:"userId"`
	RefId       *int64  `form:"refId"`
	BankCode    string  `form:"bankCode"`
	AccountNo   string  `form:"accountNo"`
	Accountname string  `form:"accountname"`
	Amount      float64 `form:"amount"`
}

type UserBankDetailBody struct {
	Id          int64   `json:"id"`
	MemberCode  string  `json:"memberCode"`
	Phone       string  `json:"phone"`
	Status      string  `json:"status"`
	Fullname    string  `json:"fullname"`
	Credit      float64 `json:"credit"`
	BankAccount string  `json:"bankAccount"`
	BankId      int64   `json:"bankId"`
	BankName    string  `json:"bankName"`
	BankCode    string  `json:"bankCode"`
}

type WithdrawTransferFastBankBody struct {
	AccountFrom string `json:"accountFrom"`
	AccountTo   string `json:"accountTo"`
	Amount      string `json:"amount"`
	BankCode    string `json:"bankCode"`
	Pin         string `json:"pin"`
}

type WithdrawTransferFastBankResponse struct {
	Status struct {
		Code        int    `json:"code"`
		Header      string `json:"header"`
		Description string `json:"description"`
	} `json:"status"`
}

type WithdrawTransferFastBankBadResponse struct {
	TimeStamp    string `json:"timeStamp"`
	ErrorMessage string `json:"errorMessage"`
}

type WithdrawTransferFastBankBackup struct {
	Data struct {
		Note                string  `json:"note"`
		QrData              string  `json:"qrData"`
		ToAccountName       string  `json:"toAccountName"`
		Amount              int     `json:"amount"`
		IsExistingFavorite  bool    `json:"isExistingFavorite"`
		FromAccountName     string  `json:"fromAccountName"`
		TransactionDateTime string  `json:"transactionDateTime"`
		Fee                 float64 `json:"fee"`
		ToAccountNo         string  `json:"toAccountNo"`
		FromAccountNo       string  `json:"fromAccountNo"`
		TransactionRefNo    string  `json:"transactionRefNo"`
	} `json:"data"`
	Status struct {
		Code        int    `json:"code"`
		Header      string `json:"header"`
		Description string `json:"description"`
	} `json:"status"`
}

type CreateAutoWithdrawRequest struct {
	TransactionId      int64     `json:"transactionId"`
	ConfirmedAt        time.Time `json:"-"`
	ConfirmedByAdminId *int64    `json:"-"`
}
type CreateSuccessTransferWithdrawRequest struct {
	TransactionId      int64     `json:"transactionId"`
	ConfirmedAt        time.Time `json:"-"`
	ConfirmedByAdminId *int64    `json:"-"`
}
type CreateIgnoredTransacionRequest struct {
	TransactionId      int64     `json:"transactionId"`
	CancelRemark       string    `json:"cancelRemark"`
	ConfirmedAt        time.Time `json:"-"`
	ConfirmedByAdminId *int64    `json:"-"`
}

type UpdateConfirmAutoWithdrawBody struct {
	TransactionStatusId *int64     `json:"transactionStatusId"`
	ConfirmedAt         time.Time  `json:"confirmedAt"`
	ConfirmedByAdminId  *int64     `json:"confirmedByAdminId"`
	FromAccountId       *int64     `json:"fromAccountId"`
	FromBankId          *int64     `json:"fromBankId"`
	FromAccountName     *string    `json:"fromAccountName"`
	FromAccountNumber   *string    `json:"fromAccountNumber"`
	SlipImgUrl          *string    `json:"slipImgUrl"`
	TransferAt          *time.Time `json:"transferAt"`
}
type UpdateConfirmTransactionRetry struct {
	ConfirmedAt        time.Time `json:"confirmedAt"`
	ConfirmedByAdminId *int64    `json:"confirmedByAdminId"`
}

type CreateTransWithdrawWithSelectedAccountRequest struct {
	TransactionId      int64     `json:"transactionId"`
	BankAccountId      int64     `json:"bankAccountId"`
	ConfirmedAt        time.Time `json:"-"`
	ConfirmedByAdminId *int64    `json:"-"`
}

type CreateTransWithdrawWithManualAccountRequest struct {
	TransactionId      int64     `form:"transactionId" validate:"required"`
	BankId             *int64    `form:"bankId"`
	AccountNumber      *string   `form:"accountNumber"`
	AccountName        *string   `form:"accountName"`
	SlipImgUrl         *string   `form:"slipImgUrl"`
	ConfirmedAt        time.Time `json:"-"`
	ConfirmedByAdminId *int64    `json:"-"`
}
type CreateTransWithdrawWithExternalAccountRequest struct {
	TransactionId      int64     `json:"transactionId"`
	ConfirmedAt        time.Time `json:"-"`
	ConfirmedByAdminId *int64    `json:"-"`
}

type CancelWithdrawCreditRequest struct {
	TransactionId       int64     `json:"transactionId"`
	CancelRemark        string    `json:"cancelRemark"`
	ConfirmedAt         time.Time `json:"-"`
	ConfirmedByAdminId  *int64    `json:"-"`
	TransactionStatusId int64     `json:"-"`
}

type WithdrawCheckTransferingRequest struct {
	TransactionId int64 `json:"transactionId"`
	ConfirmedBy   int64 `json:"-"`
}
type WithdrawCheckTransferingResponse struct {
	TransactionId int64  `json:"transactionId"`
	Status        string `json:"status"`
}

type UpdateUserTransactionStatusRequest struct {
	Id                  int64      `json:"id"`
	CancelRemark        *string    `json:"cancelRemark"`
	CanceledAt          time.Time  `json:"-"`
	CanceledByAdminId   int64      `json:"-"`
	ConfirmedAt         *time.Time `json:"confirmedAt"`
	ConfirmedByAdminId  *int64     `json:"confirmedByAdminId"`
	TransactionStatusId int64      `json:"-"`
}

type CreateDepositRecordRequest struct {
	UserId            int64     `json:"-"`
	MemberCode        string    `json:"memberCode"`
	CreditAmount      float64   `json:"creditAmount"`
	BonusAmount       float64   `json:"bonusAmount"`
	BonusReason       string    `json:"bonusReason"`
	DepositChannel    string    `json:"depositChannel"`
	FromAccountNumber string    `json:"fromAccountNumber"`
	ToAccountId       int64     `json:"toAccountId"`
	TransferAt        time.Time `json:"transferAt"`
	SlipImgUrl        string    `json:"slipImgUrl"`
	Auto              bool      `json:"auto"`
	CreateByAdminId   int64     `json:"-"`
	CreateAt          time.Time `json:"-"`
}

type CreateDepositRecordBody struct {
	Id                  int64     `json:"id"`
	MemberCode          string    `json:"memberCode"`
	UserId              int64     `json:"userId"`
	TransactionTypeId   int64     `json:"transactionTypeId"`
	FromAccountNumber   string    `json:"fromAccountNumber"`
	ToAccountId         int64     `json:"toAccountId"`
	ToBankId            int64     `json:"toBankId"`
	ToAccountNumber     string    `json:"toAccountNumber"`
	CreditAmount        float64   `json:"creditAmount"`
	BonusAmount         float64   `json:"bonusAmount"`
	DepositChannel      string    `json:"depositChannel"`
	TransferAt          time.Time `json:"transferAt"`
	TransactionStatusId int64     `json:"transactionStatusId"`
	CreateByUserId      int64     `json:"-"`
	CreateByUsername    string    `json:"-"`
}

type UpdateDepositRecordAutoBy struct {
	Id                  int64     `json:"id"`
	TransactionStatusId int64     `json:"transactionStatusId"`
	ConfirmedAt         time.Time `json:"confirmedAt"`
	ConfirmedByAdminId  *int64    `json:"confirmedByAdminId"`
}

type CreateFreeBonusRequest struct {
	MemberCode         string    `json:"memberCode"`
	BonusAmount        float64   `json:"bonusAmount"`
	BonusReason        string    `json:"bonusReason"`
	TransferAt         time.Time `json:"transferAt"`
	SlipImgUrl         string    `json:"slipImgUrl"`
	ConfirmedAt        time.Time `json:"-"`
	ConfirmedByAdminId *int64    `json:"-"`
}

type PriorityValidation struct {
	Status        string
	BankAccountId int64
	ValidateLog   []interface{}
}

type CreateWithdrawRecordRequest struct {
	MemberCode         string    `json:"memberCode"`
	ToBankId           int64     `json:"toBankId"`
	Amount             float64   `json:"amount"`
	ConfirmedAt        time.Time `json:"-"`
	ConfirmedByAdminId *int64    `json:"-"`
}
type CreateWithdrawPullCreditBackRequest struct {
	MemberCode         string    `json:"memberCode"`
	Amount             float64   `json:"amount"`
	Remark             string    `json:"remark"`
	ConfirmedAt        time.Time `json:"-"`
	ConfirmedByAdminId *int64    `json:"-"`
}
type CreateUserCancelCreditRequest struct {
	MemberCode         string    `json:"memberCode"`
	Amount             float64   `json:"amount"`
	Remark             string    `json:"remark"`
	ConfirmedAt        time.Time `json:"-"`
	ConfirmedByAdminId *int64    `json:"-"`
}

type WebScoket struct {
	UserID      int64   `json:"userId"`
	MemberCode  string  `json:"memberCode"`
	PhoneNumber string  `json:"phoneNumber"`
	FullName    string  `json:"fullName"`
	Amount      float64 `json:"amount"`
	AlertType   string  `json:"alertType"`
	BankAccount string  `json:"bankAccount"`
}
type WebScoketWithdraw struct {
	UserID  string  `json:"userId"`
	Amount  float64 `json:"amount"`
	Message string  `json:"message"`
}
type WebSocketUpdateAdminDashboardPayload struct {
	Id            int64      `json:"id"`
	Status        string     `json:"status"`
	AdminFullname string     `json:"adminFullname"`
	TransferAt    *time.Time `json:"transferAt"`
}

type RemovedTransactionId struct {
	Id int64 `uri:"id" binding:"required"`
}

//	type RemovedTransactionRequest struct {
//		RemovedAt         time.Time `json:"-"`
//		RemovedByUserId   int64     `json:"-"`
//		RemovedByUsername string    `json:"-"`
//		DeletedAt         time.Time `json:"deleteAt"`
//	}
type RemovedTransactionBody struct {
	Id        int64     `json:"id"`
	DeletedAt time.Time `json:"deleteAt"`
}

type RemovedSuccessTransactionList struct {
	Id                int64     `json:"id" gorm:"primaryKey"`
	StatementId       int64     `json:"statementId"`
	UserMemberCode    string    `json:"userMemberCode"`
	FromAccountNumber string    `json:"fromAccountNumber"`
	FromBankId        int64     `json:"fromBankId"`
	FromBankName      string    `json:"fromBankName"`
	FromBankIconUrl   string    `json:"fromBankIconUrl"`
	ToAccountNumber   string    `json:"toAccountNumber"`
	ToBankId          int64     `json:"toBankId"`
	ToBankName        string    `json:"toBankName"`
	ToBankIconUrl     string    `json:"toBankIconUrl"`
	TransferAt        time.Time `json:"transferAt"`
	CreditAmount      float64   `json:"creditAmount" sql:"type:decimal(14,2);"`
	BonusAmount       float64   `json:"bonusAmount" sql:"type:decimal(14,2);"`
	CreatedByUsername string    `json:"createdByUsername"`
	CreateAt          time.Time `json:"createAt"`
	TransactionTypeId int64     `json:"transactionTypeId"`
	TransactionTypeTh string    `json:"transactionTypeTh"`
	TransactionTypeEn string    `json:"transactionTypeEn"`
}

type RequestRemovedSuccessTransactionList struct {
	Page  int `form:"page" extensions:"x-order:2" default:"1" min:"1"`
	Limit int `form:"limit" extensions:"x-order:3" default:"10" min:"1" max:"100"`
}

type AgentTransferUpdator struct {
	TransId             int64   `json:"transId"`
	TransactionStatusId int64   `json:"transactionStatusId"`
	BeforeAmount        float64 `json:"beforeAmount"`
	AfterAmount         float64 `json:"afterAmount"`
}

type BankTransactionLogCreateRequest struct {
	Id          int64   `json:"-"`
	JsonRequest *string `json:"jsonRequest"`
	JsonPayload *string `json:"jsonPayload"`
	LogType     *string `json:"logType"`
	Status      *string `json:"status"`
}
type BankTransactionLogCreate struct {
	Id          int64  `json:"id"`
	JsonRequest string `json:"jsonRequest"`
	JsonPayload string `json:"jsonPayload"`
	LogType     string `json:"logType"`
	Status      string `json:"status"`
}

type BankTransactionLogBody struct {
	JsonPayload string `json:"jsonPayload"`
	Status      string `json:"status"`
}

type CreateFristTimeDepositRecordRequest struct {
	UserId            int64     `json:"userId"`
	CreditAmount      float64   `json:"creditAmount"`
	PromotionWebId    *int64    `json:"promotionWebId"`
	BonusAmount       float64   `json:"bonusAmount"`
	BonusReason       string    `json:"bonusReason"`
	DepositChannel    string    `json:"depositChannel"`
	FromAccountNumber string    `json:"fromAccountNumber"`
	ToAccountId       int64     `json:"toAccountId"`
	TransferAt        time.Time `json:"transferAt"`
	Auto              bool      `json:"auto"`
	CreateByUserId    int64     `json:"-"`
	CreateAt          time.Time `json:"-"`
}

type BankTransactionStatementResponse struct {
	Id                  int64      `json:"id" gorm:"primaryKey"`
	UserId              int64      `json:"userId"`
	StatementId         int64      `json:"statementId"`
	ExternalStatementId int64      `json:"externalStatementId"`
	TransactionTypeId   int64      `json:"transactionTypeId"`
	TransactionStatusId int64      `json:"transactionStatusId"`
	IsAutoCredit        bool       `json:"isAutoCredit"`
	CreatedAt           time.Time  `json:"createAt"`
	UpdatedAt           *time.Time `json:"updateAt"`
}

type CheckDuplicateWithdrawRequest struct {
	UserId int64   `json:"userId"`
	Amount float64 `json:"amount"`
}

// [2023/12/4] //ไม่ต้อง response ให้user เพราะ ต้องสามารถสร้างรายการได้ตลอด
type FastbankWithdrawTransactionResponse struct {
	Id      int64  `json:"id"`
	Message string `json:"message"`
	Status  int64  `json:"status"`
}

type UpdateConfirmedByAdminIdRequest struct {
	ConfirmAdminId *int64  `json:"confirmAdminId"`
	AccountId      *int64  `json:"accountId"`
	Detail         *string `json:"detail"`
}

type CheckUserDuplicateWithdrawProcessingRequest struct {
	UserId int64 `json:"userId"`
}

type CheckUserDuplicateWithdrawProcessingResponse struct {
	Id           int64     `json:"id"`
	CreditAmount float64   `json:"creditAmount"`
	CreatedAt    time.Time `json:"createdAt"`
}
type CheckUserDuplicateWithdrawResponse struct {
	Id           int64     `json:"id"`
	CreditAmount float64   `json:"creditAmount"`
	CreatedAt    time.Time `json:"createdAt"`
	Avaliable    bool      `json:"avaliable"`
}

type CreateWithdrawConfirmBody struct {
	Id         int64  `json:"id"`
	ConfirmKey string `json:"confirmKey"`
	UserId     int64  `json:"userId"`
}

type CheckWithdrawConfirmBody struct {
	CreatedAt time.Time `form:"createdAt"`
	UserId    int64     `form:"userId"`
}

type WithdrawConfirm struct {
	Id                int64     `json:"id"`
	ConfirmKey        string    `json:"confirmKey"`
	UserId            int64     `json:"userId"`
	BankTransactionId int64     `json:"bankTransactionId"`
	CreatedAt         time.Time `json:"createdAt"`
}

type GetBankTransactionFirstTimeDepositRequest struct {
	DateType         string `form:"dateType" default:"daily"`
	MemberCode       string `form:"memberCode"`
	FromTransferDate string `form:"fromTransferDate" extensions:"x-order:4"`
	ToTransferDate   string `form:"toTransferDate" extensions:"x-order:5"`
	Search           string `form:"search" extensions:"x-order:8"`
	Page             int    `form:"page" extensions:"x-order:9" default:"1" min:"1"`
	Limit            int    `form:"limit" extensions:"x-order:10" default:"10" min:"1" max:"100"`
	SortCol          string `form:"sortCol" extensions:"x-order:11"`
	SortAsc          string `form:"sortAsc" extensions:"x-order:12"`
}

type GetBankTransactionFirstTimeDepositResponse struct {
	// Id             int64     `json:"id" gorm:"primaryKey"`
	UserId         int64     `json:"userId"`
	UserMemberCode string    `json:"userMemberCode"`
	CreditAmount   float64   `json:"creditAmount" sql:"type:decimal(14,2);"`
	TransferAt     time.Time `json:"transferAt"`
	RegisterAt     time.Time `json:"registerAt"`
}

type TransactionWithdrawToCheckUserConfigResponse struct {
	CreditAmountSum float64 `json:"creditAmountSum" sql:"type:decimal(14,2);"`
}
type TransactionWithdrawToCheckUserConfigRequest struct {
	FromTransferDate string `form:"fromTransferDate"`
	ToTransferDate   string `form:"toTransferDate"`
	UserId           int64  `form:"userId"`
}

type TransactionWithdrawToCheckUserConfigListResponse struct {
	Id           int64   `json:"id" gorm:"primaryKey"`
	CreditAmount float64 `json:"creditAmount" sql:"type:decimal(14,2);"`
}

type RetryDepositAgentRequest struct {
	RefId              int64     `json:"refId" binding:"required"`
	ConfirmedAt        time.Time `json:"-"`
	ConfirmedByAdminId *int64    `json:"-"`
}

type CheckAdminDuplicateWithdrawList struct {
	Id           int64     `json:"id"`
	UserId       int64     `json:"userId"`
	MemberCode   string    `json:"memberCode"`
	CreditAmount float64   `json:"creditAmount"`
	TransferAt   time.Time `json:"transferAt"`
}
type CheckAdminDuplicateWithdrawListRequest struct {
	UserId       int64   `form:"userId" binding:"required"`
	CreditAmount float64 `form:"creditAmount" binding:"required"`
}

type BankTransactionGraph2Request struct {
	DateType string `form:"dateType"`
	FromDate string `form:"fromDate"`
	ToDate   string `form:"toDate"`
}
type BankTransGraphItem struct {
	OfDate             string  `json:"ofDate"`
	DepositAmount      float64 `json:"depositAmount"`
	FirstDepositAmount float64 `json:"firstDepositAmount"`
	WithdrawAmount     float64 `json:"withdrawAmount"`
	BonusAmount        float64 `json:"bonusAmount"`
}
type UserTransGraphItem struct {
	OfDate                string `json:"ofDate"`
	ActiveUserCount       int64  `json:"activeUserCount"`
	NewUserCount          int64  `json:"newUserCount"`
	FirstDepositUserCount int64  `json:"firstDepositUserCount"`
}
type BankTransactionGraph2Response struct {
	BankTransGraphList []BankTransGraphItem `json:"bankTransGraphList"`
	UserTransGraphList []UserTransGraphItem `json:"userTransGraphList"`
}

type BankTransactionLog struct {
	Id          int64          `json:"id" gorm:"primaryKey"`
	JsonRequest string         `json:"jsonRequest"`
	JsonPayload string         `json:"jsonPayload"`
	LogType     string         `json:"logType"`
	Status      string         `json:"status"`
	CreatedAt   time.Time      `json:"createAt"`
	UpdatedAt   *time.Time     `json:"updateAt"`
	DeletedAt   gorm.DeletedAt `json:"deleteAt"`
}

type BankTransactionExternalDetail struct {
	Id                int64     `json:"id" `
	BankTransactionId int64     `json:"bankTransactionId"`
	Detail            string    `json:"detail"`
	ErrorCode         int64     `json:"errorCode"`
	CreatedAt         time.Time `json:"createAt"`
}

type BankTransactionExternalDetailCreateRequest struct {
	Id                int64  `json:"-" `
	BankTransactionId int64  `json:"bankTransactionId"`
	Detail            string `json:"detail"`
	ErrorCode         int64  `json:"errorCode"`
}

type BankTransactionExternalDetailGetByBankTransactionIdRequest struct {
	Id int64 `uri:"id" binding:"required"`
}

type BankTransactionExternalDetailGetByBankTransactionIdResponse struct {
	Id                int64     `json:"id" `
	BankTransactionId int64     `json:"bankTransactionId"`
	Detail            string    `json:"detail"`
	ErrorCode         int64     `json:"errorCode"`
	CreatedAt         time.Time `json:"createAt"`
}

// CREATE TABLE IF NOT EXISTS `bank_transaction_slip` (
//     `id` INT(11) NOT NULL AUTO_INCREMENT,
//     `user_id` INT(11) NOT NULL,
//     `status` INT DEFAULT 1, -- 1: รอตรวจสอบ, 2: ผ่าน, 3: ไม่ผ่าน
//     `transaction_id` BIGINT DEFAULT NULL,
//     `raw_qr_code` VARCHAR(255) DEFAULT NULL,
//     `from_account_number` VARCHAR(255) DEFAULT NULL,
//     `from_account_name` VARCHAR(255) DEFAULT NULL,
//     `from_bank_name` VARCHAR(255) DEFAULT NULL,
//     `to_account_number` VARCHAR(255) DEFAULT NULL,
//     `to_account_name` VARCHAR(255) DEFAULT NULL,
//     `amount` DECIMAL(10,2) DEFAULT NULL,
//     `transaction_date` DATETIME DEFAULT NULL,
//     `remark` TEXT DEFAULT NULL,
//     `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
//     `updated_at` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
//     PRIMARY KEY (`id`),
//     UNIQUE KEY `raw_qr_code` (`raw_qr_code`)
// );

type BankTransactionSlipCreateRequest struct {
	Id                int64      `json:"id" `
	UserId            int64      `json:"userId"`
	Status            int        `json:"status"`
	TransactionId     int64      `json:"transactionId"`
	RawQrCode         *string    `json:"rawQrCode"`
	FromAccountNumber string     `json:"fromAccountNumber"`
	FromAccountName   string     `json:"fromAccountName"`
	FromBankName      string     `json:"fromBankName"`
	ToAccountNumber   string     `json:"toAccountNumber"`
	ToAccountName     string     `json:"toAccountName"`
	Amount            float64    `json:"amount"`
	TransactionDate   time.Time  `json:"transactionDate"`
	Remark            string     `json:"remark"`
	CreatedAt         time.Time  `json:"createAt"`
	UpdatedAt         *time.Time `json:"updateAt"`
}

type BankTransactionSlipUpdateRequest struct {
	Id                int64      `json:"id" `
	UserId            *int64     `json:"userId"`
	Status            *int       `json:"status"`
	TransactionId     *int64     `json:"transactionId"`
	RawQrCode         *string    `json:"rawQrCode"`
	FromAccountNumber *string    `json:"fromAccountNumber"`
	FromAccountName   *string    `json:"fromAccountName"`
	FromBankName      *string    `json:"fromBankName"`
	ToAccountNumber   *string    `json:"toAccountNumber"`
	ToAccountName     *string    `json:"toAccountName"`
	Amount            *float64   `json:"amount"`
	TransactionDate   *time.Time `json:"transactionDate"`
	Remark            *string    `json:"remark"`
	CreatedAt         *time.Time `json:"createAt"`
	UpdatedAt         *time.Time `json:"updateAt"`
}

type CheckScammerSlipRequest struct {
	UserId int64 `json:"userId"`
}

type CheckScammerSlipResponse struct {
	CountTodaySent int64 `json:"countTodaySent"`
}

type ClearAllTurnOver struct {
	UserId  int64 `json:"userId"`
	AdminId int64 `json:"-"`
}
type EmptyUserTurnListRequest struct {
	UserId  []int64 `json:"userId"`
	AdminId int64   `json:"-"`
}

type CreateDepositLaosBankRecordRequest struct {
	UserId       int64   `json:"-"`
	CreditAmount float64 `json:"creditAmount"`
	ToAccountId  int64   `json:"toAccountId"`
}

type AccountInfoFastbankRequest struct {
	BankCode string `json:"BankCode"`
	BankNo   string `json:"BankNo"`
}

// v2
type AccountInfoFastbankResponse struct {
	BankAccountNo     string `json:"bankAccountNo"`
	BankAccountName   string `json:"bankAccountName"`
	BankAccountNameEN string `json:"bankAccountNameEN"`
	BankCode          string `json:"bankCode"`
}

// old v1
// type AccountInfoFastbankResponse struct {
// 	Data         *AccountInfoFastbankData `json:"data"`
// 	ResponseMesg string                   `json:"responseMesg"`
// 	ResponseCode string                   `json:"responseCode"`
// }

// type AccountInfoFastbankData struct {
// 	BankAccountName    string `json:"bankAccountName"`
// 	BankTransferNameEN string `json:"bankTransferNameEN"`
// 	BankNo             string `json:"bankNo"`
// 	BankTransferNameTH string `json:"bankTransferNameTH"`
// 	BankCode           string `json:"bankCode"`
// }

// responseData {"timeStamp":"2024-07-18T07:22:52.718Z","errorMessage":"bankAccount not found"}

type AccountInfoFastbankDataError2 struct {
	Timestamp    int64  `json:"timestamp"`
	ErrorMessage string `json:"errorMessage"`
}
type AccountInfoFastbankDataError struct {
	Timestamp int64  `json:"timestamp"`
	Status    int64  `json:"status"`
	Error     string `json:"error"`
	Path      string `json:"path"`
}

// {
// 	"timestamp": *************,
// 	"status": 500,
// 	"error": "Internal Server Error",
// 	"path": "/api/v2/statement/accountInfo"
//   }

type GetSmsModeDepositListRequest struct {
	DateType   string `form:"dateType" default:"daily"`
	MemberCode string `form:"memberCode"`
	FromDate   string `form:"fromDate"`
	Status     string `form:"status"`
	ToDate     string `form:"toDate"`
	Search     string `form:"search"`
	Page       int    `form:"page" default:"1" min:"1"`
	Limit      int    `form:"limit" default:"10" min:"1" max:"100"`
}

type GetSmsModeDepositListResponse struct {
	Id       int64  `json:"id" gorm:"primaryKey"`
	UserId   int64  `json:"userId"`
	Username string `json:"username"` // ผู้ใช้งาน
	FullName string `json:"fullName"` // ผู้ใช้งาน
	// RefId             int64      `json:"refId"`
	MemberCode string `json:"memberCode"` // รหัสสมาชิก
	// FromBankId        int64      `json:"fromBankId"`
	// FromBankName      string     `json:"fromBankName"`
	// AccountFrom       string     `json:"accountFrom"`
	// BankAccountId     int64      `json:"bankAccountId"`
	// BankAccountName   string     `json:"bankAccountName"`
	// BankAccountNo     string     `json:"bankAccountNo"`
	// BankCode          string     `json:"bankCode"`
	OrderNo        string  `json:"orderNo"`        // refno
	Detail         string  `json:"detail"`         // โอนจากธนาคาร ไป ธนาคาร
	Amount         float64 `json:"amount"`         // จำนวนแจ้ง
	TransferAmount float64 `json:"transferAmount"` // จำนวนเงินที่โอน
	// TransactionNo     string     `json:"transactionNo"`
	TransactionDate      time.Time  `json:"transactionDate"`   // วันและเวลาแจ้งฝาก
	TransactionStatus    string     `json:"transactionStatus"` // สถานะ
	PaymentAt            *time.Time `json:"paymentAt"`         // วันและเวลาที่โอนจริง
	Remark               string     `json:"remark"`            // หมายเหตุ
	ConfirmedByAdminName string     `json:"confirmedByAdminName"`
	CreatedAt            time.Time  `json:"createdAt"`
	UpdatedAt            *time.Time `json:"updatedAt"`
}

type AdminConfirmDepositSmsModeRequest struct {
	PaygateSmsModeDepositId int64  `json:"paygateSmsModeDepositId"`
	CreateByAdminId         *int64 `json:"-"`
}

type GetSmsModeDepositByIdReponse struct {
	Id                int64      `json:"id" gorm:"primaryKey"`
	UserId            int64      `json:"userId"`
	Username          string     `json:"username"` // ผู้ใช้งาน
	FullName          string     `json:"fullName"` // ผู้ใช้งาน
	RefId             int64      `json:"refId"`
	MemberCode        string     `json:"memberCode"` // รหัสสมาชิก
	FromBankId        int64      `json:"fromBankId"`
	FromBankName      string     `json:"fromBankName"`
	AccountFrom       string     `json:"accountFrom"`
	BankAccountId     int64      `json:"bankAccountId"`
	BankAccountName   string     `json:"bankAccountName"`
	BankAccountNo     string     `json:"bankAccountNo"`
	BankCode          string     `json:"bankCode"`
	OrderNo           string     `json:"orderNo"`        // refno
	Detail            string     `json:"detail"`         // โอนจากธนาคาร ไป ธนาคาร
	Amount            float64    `json:"amount"`         // จำนวนแจ้ง
	TransferAmount    float64    `json:"transferAmount"` // จำนวนเงินที่โอน
	TransactionNo     string     `json:"transactionNo"`
	TransactionDate   time.Time  `json:"transactionDate"`   // วันและเวลาแจ้งฝาก
	TransactionStatus string     `json:"transactionStatus"` // สถานะ
	PaymentAt         *time.Time `json:"paymentAt"`         // วันและเวลาที่โอนจริง
	Remark            string     `json:"remark"`            // หมายเหตุ
	CreatedAt         time.Time  `json:"createdAt"`
	UpdatedAt         *time.Time `json:"updatedAt"`
}

type AdminConfirmCanceledSmsModeRequest struct {
	PaygateSmsModeCanceledId int64  `json:"paygateSmsModeCanceledId"`
	CreateByAdminId          *int64 `json:"-"`
}

type BotCreateWithdrawPullCreditBackRequest struct {
	DateFrom        *time.Time `json:"dateFrom"`
	DateTo          *time.Time `json:"dateTo"`
	Limit           int        `json:"limit"`
	Page            int        `json:"page"`
	CreateByAdminId int64      `json:"-"`
}
type BotCreateWithdrawPullCreditBackGetUser struct {
	UserId     int64  `json:"userId"`
	MemberCode string `json:"memberCode"`
}

type BotCreateWithdrawPullCreditBackResponse struct {
	Total                                 int                                     `json:"total"`
	CountError                            int                                     `json:"countError"`
	CountSuccess                          int                                     `json:"countSuccess"`
	BotCreateWithdrawPullCreditBackDetail []BotCreateWithdrawPullCreditBackDetail `json:"botCreateWithdrawPullCreditBackDetail"`
}
type BotCreateWithdrawPullCreditBackDetail struct {
	MemberCode string `json:"memberCode"`
	Message    string `json:"message"`
}
