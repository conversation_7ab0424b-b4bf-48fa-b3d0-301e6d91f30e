package model

import "time"

const (
	ACTIVITY_DAILY_V2_TOTAL_CONDITION_NO_CONDITION    = 1
	ACTIVITY_DAILY_V2_TOTAL_CONDITION_MIN_DEPOSIT     = 2
	ACTIVITY_DAILY_V2_TOTAL_CONDITION_OVERALL_DEPOSIT = 3
)

type ActivityDailyV2Total struct {
	Id                int64      `json:"id"`
	TotalAbleReviceNo int64      `json:"totalAbleReviceNo"`
	ChangeCountTime   int64      `json:"changeCountTime"`
	CreatedAt         time.Time  `json:"createdAt"`
	UpdatedAt         *time.Time `json:"updatedAt"`
	UpdatedById       *int64     `json:"updatedById"`
}

type CreateActivityDailyV2Request struct {
	TotalAbleReviceNo               int64                   `json:"totalAbleReviceNo"`
	ActivityDailyV2TotalConditionId *int64                  `json:"activityDailyV2TotalConditionId"`
	ConditionAmount                 *float64                `json:"conditionAmount"`
	ActivityDailyV2Detail           []ActivityDailyV2Detail `json:"activityDailyV2Detail"`
	IsActive                        *bool                   `json:"isActive"`
	UpdatedAt                       time.Time               `json:"-"`
	UpdatedById                     int64                   `json:"-"`
}

type CreateActivityDailyV2TotalBody struct {
	TotalAbleReviceNo               int64      `json:"totalAbleReviceNo"`
	ChangeCountTime                 int64      `json:"changeCountTime"`
	ActivityDailyV2TotalConditionId *int64     `json:"activityDailyV2TotalConditionId"`
	ConditionAmount                 *float64   `json:"conditionAmount"`
	IsActive                        *bool      `json:"isActive"`
	UpdatedAt                       *time.Time `json:"updatedAt"`
	UpdatedById                     *int64     `json:"updatedById"`
}

type GetActivityDailyV2TotalChangeCountTime struct {
	ChangeCountTime int64 `json:"changeCountTime"`
}

type ActivityDailyV2 struct {
	Id              int64     `json:"id"`
	ChangeCountTime int64     `json:"changeCountTime"`
	NoNumber        int64     `json:"noNumber"`
	CreditAmount    float64   `json:"creditAmount"`
	IsHiglight      int       `json:"isHiglight"`
	CreatedAt       time.Time `json:"createdAt"`
	CreatedById     int64     `json:"createdById"`
}

type CreateActivityDailyV2Body struct {
	ChangeCountTime int64     `json:"changeCountTime"`
	NoNumber        int64     `json:"noNumber"`
	CreditAmount    float64   `json:"creditAmount"`
	IsHiglight      int       `json:"isHiglight"`
	CreatedAt       time.Time `json:"createdAt"`
	CreatedById     int64     `json:"createdById"`
}

type ActivityDailyV2Detail struct {
	Id           int64   `json:"-"`
	NoNumber     int64   `json:"noNumber"`
	CreditAmount float64 `json:"creditAmount"`
	IsHiglight   int     `json:"isHiglight"`
}

type GetActivityDailyV2TotalResponse struct {
	TotalAbleReviceNo                 int64                   `json:"totalAbleReviceNo"`
	ConditionAmount                   float64                 `json:"conditionAmount"`
	ActivityDailyV2TotalConditionId   int64                   `json:"activityDailyV2TotalConditionId"`
	ActivityDailyV2TotalConditionName string                  `json:"activityDailyV2TotalConditionName"`
	IsActive                          bool                    `json:"isActive"`
	ActivityDailyV2Detail             []ActivityDailyV2Detail `json:"activityDailyV2Detail"`
}

type WebActivityDailyV2Detail struct {
	NoNumber     int64   `json:"noNumber"`
	CreditAmount float64 `json:"creditAmount"`
	IsHiglight   int     `json:"isHiglight"`
}
type WebActivityDailyV2ReviceDetail struct {
	NoNumber     int64   `json:"noNumber"`
	CreditAmount float64 `json:"creditAmount"`
	IsHiglight   int     `json:"isHiglight"`
}

type UserReviceActivityDailyV2Request struct {
	UserId int64 `json:"-"`
}

type ActivityDailyV2UserNextNo struct {
	Id              int64   `json:"id"`
	ChangeCountTime int64   `json:"changeCountTime"`
	NoNumber        int64   `json:"noNumber"`
	CreditAmount    float64 `json:"creditAmount"`
}

type CreateActivityDailyV2User struct {
	Id                int64     `json:"id"`
	ActivityDailyV2Id int64     `json:"activityDailyV2Id"`
	ChangeCountTime   int64     `json:"changeCountTime"`
	NoNumber          int64     `json:"noNumber"`
	UserId            int64     `json:"userId"`
	CreditAmount      float64   `json:"creditAmount"`
	ReceivedAt        time.Time `json:"receivedAt"`
	DateReceived      string    `json:"dateReceived"`
}

type WebGetActivityDailyV2ListResponse struct {
	NoNumber       int64   `json:"noNumber"`
	CreditAmount   float64 `json:"creditAmount"`
	IsHiglight     int     `json:"isHiglight"`
	AlreadyReviced bool    `json:"alreadyReviced"`
	TodayAvailable bool    `json:"todayAvailable"`
}
type WebGetActivityDailyV2Response struct {
	ConditionAmount                   float64                             `json:"conditionAmount"`
	ActivityDailyV2TotalConditionId   int64                               `json:"activityDailyV2TotalConditionId"`
	ActivityDailyV2TotalConditionName string                              `json:"activityDailyV2TotalConditionName"`
	PercentCal                        int                                 `json:"percentCal"`
	LastHighlight                     int64                               `json:"lastHighlight"`
	NexHighlight                      int64                               `json:"nexHighlight"`
	WebGetActivityDailyV2ListResponse []WebGetActivityDailyV2ListResponse `json:"activityDailyV2List"`
}

type GetTurnoverUserActivityDailyV2Request struct {
	DateType   string `form:"dateType" default:"daily"`
	FromDate   string `form:"fromDate"`
	ToDate     string `form:"toDate"`
	MemberCode string `form:"memberCode"`
	Page       int    `form:"page" default:"1" min:"1"`
	Limit      int    `form:"limit" default:"10" min:"1" max:"100"`
}

type GetTurnoverUserActivityDailyV2Response struct {
	Id              int64      `json:"id"`
	UserId          int64      `json:"userId"`
	MemberCode      string     `json:"memberCode"`      // รหัสสมาชิก
	Fullname        string     `json:"fullname"`        // ชื่อ-นามสกุล
	Username        string     `json:"username"`        // เบอร์โทรศัพท์
	Description     string     `json:"description"`     // รายละเอียด
	AmountCondition float64    `json:"amountCondition"` // จำนวนเงื่อนไข
	TidturnPercent  int64      `json:"tidturnPercent"`  // เปอร์เซ็นต์การติดเทิร์น
	TypeId          int64      `json:"typeId"`
	TypeName        string     `json:"typeName"`
	RefTypeId       int64      `json:"refTypeId"`
	BonusAmount     float64    `json:"bonusAmount"` // เครดิตรางวัล
	StatusId        int64      `json:"statusId"`
	StatusName      string     `json:"statusName"`      // สถานะเทิร์น
	StartTurnAmount float64    `json:"startTurnAmount"` // จำนวนเทิร์น
	StartTurnAt     *time.Time `json:"startTurnAt"`
	TotalTurnAmount float64    `json:"totalTurnAmount"`
	EndTurnAt       *time.Time `json:"endTurnAt"`
	CreatedAt       time.Time  `json:"createdAt"` // วันเวลา
	UpdatedAt       *time.Time `json:"updatedAt"`
}

type GetActivityDailyV2IsActive struct {
	IsActive bool `json:"isActive"`
}

type GetActivityDailyV2Total struct {
	TotalAbleReviceNo                 int64   `json:"totalAbleReviceNo"`
	ConditionAmount                   float64 `json:"conditionAmount"`
	ActivityDailyV2TotalConditionId   int64   `json:"activityDailyV2TotalConditionId"`
	ActivityDailyV2TotalConditionName string  `json:"activityDailyV2TotalConditionName"`
	ChangeCountTime                   int64   `json:"changeCountTime"`
}

type GetUserTodayDepositForActivityDaily struct {
	Id           int64     `json:"id"`
	UserId       int64     `json:"userId"`
	CreditAmount float64   `json:"creditAmount"`
	TransferAt   time.Time `json:"transferAt"`
}
