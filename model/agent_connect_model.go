package model

const (
	AGENT_PRODUCT_SPORT     = 1
	AGENT_PRODUCT_CASINO    = 2
	AGENT_PRODUCT_GAME      = 4
	AGENT_PRODUCT_LOTTERY   = 3
	AGENT_PRODUCT_P2P       = 6
	AGENT_PRODUCT_FINANCIAL = 7
)
const (
	AGENT_AMB_PRODUCT_GAME   = 1
	AGENT_AMB_PRODUCT_CASINO = 2
	AGENT_AMB_PRODUCT_SPORT  = 3
)

type AgcRegister struct {
	Username  string `json:"Username" validate:"required"`
	Agentname string `json:"Agentname" validate:"required"`
	Fullname  string `json:"Fullname" validate:"required"`
	Password  string `json:"Password" validate:"required"`
	Currency  string `json:"Currency" validate:"required"`
	Dob       string `json:"Dob"`
	Gender    int    `json:"Gender" validate:"required"`
	Email     string `json:"Email"`
	Mobile    string `json:"Mobile" validate:"required"`
	Ip        string `json:"Ip" validate:"required"`
	Timestamp int64  `json:"Timestamp" validate:"required"`
	Sign      string `json:"Sign" validate:"required"`
	//CommFollowUpline int    `json:"CommFollowUpline"`
	//PTFollowUpline   int    `json:"PTFollowUpline"`
}

type AgcLogin struct {
	Username  string `json:"Username" validate:"required"`
	Partner   string `json:"Partner" validate:"required"`
	Timestamp int64  `json:"Timestamp" validate:"required"`
	Sign      string `json:"Sign" validate:"required"`
	Domain    string `json:"Domain" validate:"required"`
	Lang      string `json:"Lang" validate:"required"`
	IsMobile  bool   `json:"IsMobile" validate:"required"`
	Ip        string `json:"Ip" validate:"required"`
}

type AgcChangePassword struct {
	PlayerName  string `json:"PlayerName" validate:"required"`
	Partner     string `json:"Partner" validate:"required"`
	NewPassword string `json:"NewPassword" validate:"required"`
	Timestamp   int64  `json:"Timestamp" validate:"required"`
	Sign        string `json:"Sign" validate:"required"`
}

type AgcDeposit struct {
	Agentname     string  `json:"Agentname" validate:"required"`
	PlayerName    string  `json:"PlayerName" validate:"required"`
	Amount        float64 `json:"Amount" validate:"required"`
	Timestamp     int64   `json:"TimeStamp" validate:"required"`
	Sign          string  `json:"Sign" validate:"required"`
	TransactionId string  `json:"TransactionId" validate:"required"`
}

type AgcWithdraw struct {
	Agentname     string  `json:"Agentname" validate:"required"`
	PlayerName    string  `json:"PlayerName" validate:"required"`
	Amount        float64 `json:"Amount" validate:"required"`
	TimeStamp     int64   `json:"TimeStamp" validate:"required"`
	Sign          string  `json:"Sign" validate:"required"`
	TransactionId string  `json:"TransactionId" validate:"required"`
}

type AgcLoginResponse struct {
	Success bool   `json:"Success"`
	Token   string `json:"Token"`
}

type AgcChangePasswordResponse struct {
	ErrorCode int  `json:"ErrorCode"`
	Status    bool `json:"Status"`
}

type AgcPlayQuery struct {
	Token string `form:"token" binding:"required"`
}

type AgcPlayBody struct {
	Vendor       string `json:"vendor"`
	Browser      string `json:"browser" example:""`
	GameCode     string `json:"gameCode"`
	Lang         string `json:"lang" binding:"oneof=en-us th-th" example:"th-th or en-us"`
	CategoryName string `json:"categoryName" binding:"required" example:""`
	GameToken    string `json:"gameToken" binding:"required" example:""`
	Ip           string `json:"-"`
	UserId       int64  `json:"-"`
}
type AgcPlayHtmlRequest struct {
	Vendor       string `form:"vendor"`
	Browser      string `form:"browser" example:""`
	Lang         string `form:"lang" binding:"oneof=en-us th-th" example:"th-th or en-us"`
	CategoryName string `form:"categoryName" binding:"required" example:""`
	AccessToken  string `form:"accessToken" binding:"required" example:""`
	UserId       int64  `json:"-"`
	Ip           string `json:"-"`
}
type AgcTestLoginRequest struct {
	Vendor       string `form:"vendor"`
	Browser      string `form:"browser" example:""`
	Lang         string `form:"lang" binding:"oneof=en-us th-th" example:"th-th or en-us"`
	CategoryName string `form:"categoryName" binding:"required" example:""`
	AccessToken  string `form:"accessToken" binding:"required" example:""`
	UserId       int64  `json:"-"`
	Ip           string `json:"-"`
}

type AgcPlayRequest struct {
	Vendor   string `json:"Vendor"`
	Browser  string `json:"Browser"`
	GameCode string `json:"GameCode"`
	Lang     string `json:"Lang"`
	GameHall string `json:"-"`
}

type AgcPlay struct {
	Code  int `json:"Code"`
	Error struct {
		Code    int    `json:"Code"`
		Message string `json:"Message"`
	} `json:"Error"`
	Message string `json:"Message"`
	Success bool   `json:"Success"`
	Result  struct {
		Data     string `json:"Data"`
		Settings struct {
			MaxPerBet float64 `json:"MaxPerBet"`
		} `json:"Result"`
		TargetUrl string `json:"TargetUrl"`
		Metadata  string `json:"Metadata"`
	}
}

type AgcPlayResponse struct {
	GameProvider string  `json:"gameProvider"`
	GameUrl      string  `json:"gameUrl"`
	MaxPerBet    float64 `json:"maxPerBet"`
	GameToken    string  `json:"gameToken"`
}

type AgcTransactionResponse struct {
	Error struct {
		Code    int    `json:"Code"`
		Message string `json:"Message"`
	}
	Message string `json:"Message"`
	Result  struct {
		BalanceAfter  float64 `json:"BalanceAfter"`
		BalanceBefore float64 `json:"BalanceBefore"`
	}
	Success   bool   `json:"Success"`
	TargetUrl string `json:"TargetUrl"`
}

type AgcBalance struct {
	Agentname  string `json:"Agentname" validate:"required"`
	PlayerName string `json:"PlayerName" validate:"required"`
	Timestamp  int64  `json:"TimeStamp" validate:"required"`
	Sign       string `json:"Sign" validate:"required"`
}
type AgcBalanceResponse struct {
	Balance   float64 `json:"Balance"`
	Error     int64   `json:"Error"`
	Message   string  `json:"Message"`
	Sign      string  `json:"Sign"`
	TimeStamp int64   `json:"TimeStamp"`
	UTC       string  `json:"UTC"`
}

type AgcSimpleWinlose struct {
	StartDate  string `json:"StartDate"`
	EndDate    string `json:"EndDate"`
	PageSize   int    `json:"PageSize"`
	PageIndex  int    `json:"PageIndex"`
	MemberName string `json:"MemberName"`
	AgentName  string `json:"AgentName"`
	Products   []int  `json:"Products"`
	TimeStamp  int    `json:"TimeStamp"`
	Sign       string `json:"Sign"`
}

type SimpleWinloseRecord struct {
	// Comm        float64
	// Company     float64
	// FullName    string
	// GrossComm   float64
	// Loyalty     float64
	// MemberId    int64
	Payout float64
	// PlayerId    int
	// Role        int
	// Total       float64
	TurnOver    float64
	UserName    string
	ValidAmount float64
	// Win         float64
}

type AgcSimpleWinloseResponse struct {
	Error *struct {
		Code    int
		Message string
	}
	Message string
	Result  struct {
		ProcessStatus []struct {
			Date   string
			Status bool
		}
		Records []SimpleWinloseRecord
		Summary struct {
			Comm  float64
			Comms struct {
				Player float64
			}
			Company      float64
			Details      interface{}
			GrossComm    float64
			Loyalty      float64
			PayOut       float64
			TurnOver     float64
			ValidAmount  float64
			ValidAmounts interface{}
			Win          float64
			Wins         struct {
				Player float64
			}
		}
		Total float64
	}
	Success   bool
	TargetUrl interface{}
}

type AgentPlayLog struct {
	ID                   int64
	Player               string
	TurnSport            float64
	TurnCasino           float64
	TurnGame             float64
	TurnLottery          float64
	TurnP2p              float64
	TurnFinancial        float64
	WinLoseSport         float64
	WinLoseCasino        float64
	WinLoseGame          float64
	WinLoseLottery       float64
	WinLoseP2p           float64
	WinLoseFinancial     float64
	ValidAmountSport     float64
	ValidAmountCasino    float64
	ValidAmountGame      float64
	ValidAmountLottery   float64
	ValidAmountP2p       float64
	ValidAmountFinancial float64
	TurnTotal            float64
	WinLoseTotal         float64
	ValidAmountTotal     float64
	Date                 string
	UserID               int64
}
