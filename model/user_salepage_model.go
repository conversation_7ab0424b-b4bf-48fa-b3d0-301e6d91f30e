package model

import (
	"time"
)

type PostUserSalepageRequest struct {
	SaleCode string `json:"saleCode" validate:"required"`
}

type UserSalepageStat struct {
	Id                  int64      `json:"-"`
	UserId              int64      `json:"-"`
	DailyKey            string     `json:"-"`
	LinkClickCount      int64      `json:"-"`
	MemberRegisterCount int64      `json:"-"`
	AdminClickCount     int64      `json:"-"`
	CreatedAt           time.Time  `json:"-"`
	UpdatedAt           *time.Time `json:"-"`
}
type UserSalepageStatResponse struct {
	Id                  int64      `json:"id"`
	UserId              int64      `json:"userId"`
	DailyKey            string     `json:"dailyKey"`
	LinkClickCount      int64      `json:"linkClickCount"`
	MemberRegisterCount int64      `json:"memberRegisterCount"`
	AdminClickCount     int64      `json:"adminClickCount"`
	CreatedAt           time.Time  `json:"-"`
	UpdatedAt           *time.Time `json:"-"`
}
type UserSalepageStatSummaryResponse struct {
	UserId                   int64 `json:"userId"`
	TotalLinkClickCount      int64 `json:"totalLinkClickCount"`
	TodayMemberRegisterCount int64 `json:"todayMemberRegisterCount"`
	TotalMemberRegisterCount int64 `json:"totalMemberRegisterCount"`
	TodayAdminClickCount     int64 `json:"todayAdminClickCount"`
	TotalAdminClickCount     int64 `json:"totalAdminClickCount"`
}
type UserSalepageStatCreateBody struct {
	Id                  int64  `json:"id"`
	UserId              int64  `json:"userId"`
	DailyKey            string `json:"dailyKey"`
	LinkClickCount      int64  `json:"linkClickCount"`
	MemberRegisterCount int64  `json:"memberRegisterCount"`
	AdminClickCount     int64  `json:"adminClickCount"`
}

type UserSalepageResponse struct {
	UserId   int64  `json:"userId"`
	SaleCode string `json:"saleCode"`
}
type UserSalepageUpdateRequest struct {
	UserId   int64   `json:"-" validate:"required"`
	SaleCode *string `json:"saleCode"`
}

type CustomSalepageBlockListRequest struct {
	UserId int64 `json:"userId" validate:"required"`
}
type CustomSalepageBlockResponse struct {
	Id          int64      `json:"id"`
	UserId      int64      `json:"userId"`
	Name        string     `json:"name"`
	Label       string     `json:"label"`
	IsShow      bool       `json:"isShow"`
	SortOrder   int64      `json:"sortOrder"`
	ValueName   string     `json:"valueName"`
	ValueColor  string     `json:"valueColor"`
	ValueImage1 string     `json:"valueImage1"`
	ValueUrl1   string     `json:"valueUrl1"`
	ValueImage2 string     `json:"valueImage2"`
	ValueUrl2   string     `json:"valueUrl2"`
	ValueImage3 string     `json:"valueImage3"`
	ValueUrl3   string     `json:"valueUrl3"`
	CreatedAt   time.Time  `json:"createdAt"`
	UpdatedAt   *time.Time `json:"updatedAt"`
}
type CustomSalepageBlockRawResponse struct {
	Id        int64  `json:"id"`
	UserId    int64  `json:"userId"`
	Name      string `json:"name"`
	Label     string `json:"label"`
	IsShow    bool   `json:"isShow"`
	SortOrder int64  `json:"sortOrder"`
}
type CustomSalepageBlockCreateBody struct {
	Id        int64   `json:"id"`
	UserId    int64   `json:"userId"`
	Name      string  `json:"name"`
	Label     string  `json:"label"`
	IsShow    bool    `json:"isShow"`
	SortOrder int64   `json:"sortOrder"`
	BlockKey  *string `json:"blockKey"`
}
type CustomSalepageBlockUpdateRequest struct {
	Id          int64   `json:"-" validate:"required"`
	UserId      int64   `json:"-" validate:"required"`
	IsShow      *bool   `json:"isShow"`
	ValueName   *string `json:"valueName"`
	ValueColor  *string `json:"valueColor"`
	ValueImage1 *string `json:"valueImage1"`
	ValueUrl1   *string `json:"valueUrl1"`
	ValueImage2 *string `json:"valueImage2"`
	ValueUrl2   *string `json:"valueUrl2"`
	ValueImage3 *string `json:"valueImage3"`
	ValueUrl3   *string `json:"valueUrl3"`
}
type CustomSalepageBlockUpdateBody struct {
	IsShow      *bool   `json:"isShow"`
	ValueName   *string `json:"valueName"`
	ValueColor  *string `json:"valueColor"`
	ValueImage1 *string `json:"valueImage1"`
	ValueUrl1   *string `json:"valueUrl1"`
	ValueImage2 *string `json:"valueImage2"`
	ValueUrl2   *string `json:"valueUrl2"`
	ValueImage3 *string `json:"valueImage3"`
	ValueUrl3   *string `json:"valueUrl3"`
}
