package model

import "mime/multipart"

type SuccessWithUploadedPath struct {
	Message  string `json:"message"`
	FilePath string `json:"filePath"`
}

type FileFullPathRequest struct {
	FilePath string `json:"filePath"`
}

type FileUploadResponse struct {
	ImageUrl string `json:"imageUrl"`
}

type FileImageResponse struct {
	ImageUrl string `json:"imageUrl"`
}

type FileUpload struct {
	Path string
	File multipart.File
}
