package model

import (
	"time"

	"gorm.io/gorm"
)

const (
	INVOICE_STATUS_WAIT_PAYMENT = 1
	INVOICE_STATUS_WAIT_CONFIRM = 2
	INVOICE_STATUS_COMPLETED    = 3
	INVOICE_STATUS_REJECTED     = 4
)
const (
	INVOICE_TYPE_WEB_RENEWAL      = 1
	INVOICE_TYPE_FASTBANK_RENEWAL = 2
	INVOICE_TYPE_SMS_RENEWAL      = 3
)

type WebMaster struct {
	Id                       int64          `json:"id"`
	Name                     string         `json:"name"`
	WebDomain                string         `json:"webDomain"`
	ApiKey                   string         `json:"apiKey"`
	LastRemoteUpdateAt       *time.Time     `json:"lastRemoteUpdateAt"`
	PaymentDetail            string         `json:"paymentDetail"`
	CurrentWebPackageId      int64          `json:"currentWebPackageId"`
	WebExpiredDate           string         `json:"webExpiredDate"`
	CurrentSmsPackageId      int64          `json:"currentSmsPackageId"`
	SmsCreditBalance         int64          `json:"smsCreditBalance"`
	SmsExpiredDate           string         `json:"smsExpiredDate"`
	CurrentFastbankPackageId int64          `json:"currentFastbankPackageId"`
	FastbankCreditBalance    int64          `json:"fastbankCreditBalance"`
	FastbankExpiredDate      string         `json:"fastbankExpiredDate"`
	FastbankFreeStartDate    string         `json:"fastbankFreeStartDate"`
	FastbankFreeEndDate      string         `json:"fastbankFreeEndDate"`
	CreatedAt                time.Time      `json:"createdAt"`
	UpdatedAt                *time.Time     `json:"updatedAt"`
	DeletedAt                gorm.DeletedAt `json:"deletedAt"`
}
type WebMasterListRequest struct {
	Search  string `form:"search"`
	Page    int    `form:"page" default:"1" min:"1"`
	Limit   int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol string `form:"sortCol"`
	SortAsc string `form:"sortAsc"`
}
type WebMasterResponse struct {
	Id                       int64      `json:"id"`
	Name                     string     `json:"name"`
	WebDomain                string     `json:"webDomain"`
	ApiKey                   string     `json:"apiKey"`
	LastRemoteUpdateAt       *time.Time `json:"lastRemoteUpdateAt"`
	PaymentDetail            string     `json:"paymentDetail"`
	CurrentWebPackageId      int64      `json:"currentWebPackageId"`
	WebExpiredDate           string     `json:"webExpiredDate"`
	CurrentSmsPackageId      int64      `json:"currentSmsPackageId"`
	SmsCreditBalance         int64      `json:"smsCreditBalance"`
	SmsExpiredDate           string     `json:"smsExpiredDate"`
	CurrentFastbankPackageId int64      `json:"currentFastbankPackageId"`
	FastbankCreditBalance    int64      `json:"fastbankCreditBalance"`
	FastbankExpiredDate      string     `json:"fastbankExpiredDate"`
	FastbankFreeStartDate    string     `json:"fastbankFreeStartDate"`
	FastbankFreeEndDate      string     `json:"fastbankFreeEndDate"`
	CreatedAt                time.Time  `json:"createdAt"`
	UpdatedAt                *time.Time `json:"updatedAt"`
}
type WebStatusResponse struct {
	Id                       int64      `json:"id"`
	Name                     string     `json:"name"`
	WebDomain                string     `json:"webDomain"`
	IsFrontEnabled           bool       `json:"isFrontEnabled"`
	IsBackEnabled            bool       `json:"isBackEnabled"`
	LastRemoteUpdateAt       *time.Time `json:"lastRemoteUpdateAt"`
	PaymentDetail            string     `json:"paymentDetail"`
	CurrentWebPackageId      int64      `json:"currentWebPackageId"`
	CurrentWebPackageName    string     `json:"currentWebPackageName"`
	WebExpiredDate           string     `json:"webExpiredDate"`
	CurrentSmsPackageId      int64      `json:"currentSmsPackageId"`
	SmsCreditBalance         int64      `json:"smsCreditBalance"`
	SmsExpiredDate           string     `json:"smsExpiredDate"`
	CurrentFastbankPackageId int64      `json:"currentFastbankPackageId"`
	FastbankCreditBalance    int64      `json:"fastbankCreditBalance"`
	FastbankExpiredDate      string     `json:"fastbankExpiredDate"`
	FastBankPackageType      string     `json:"fastBankPackageType"`
	FastbankFreeStartDate    string     `json:"fastbankFreeStartDate"`
	FastbankFreeEndDate      string     `json:"fastbankFreeEndDate"`
	MaintenanceMessage       string     `json:"maintenanceMessage"`
	UseOtpRegister           bool       `json:"useOtpRegister"`
	CreatedAt                time.Time  `json:"createdAt"`
	UpdatedAt                *time.Time `json:"updatedAt"`
}
type WebMasterCreateRequest struct {
	Name                     string `form:"name"`
	WebDomain                string `form:"webDomain" binding:"required"`
	ApiKey                   string `form:"apiKey"`
	PaymentDetail            string `json:"paymentDetail"`
	CurrentWebPackageId      int64  `json:"currentWebPackageId"`
	WebExpiredDate           string `json:"webExpiredDate"`
	CurrentSmsPackageId      int64  `json:"currentSmsPackageId"`
	SmsCreditBalance         int64  `json:"smsCreditBalance"`
	SmsExpiredDate           string `json:"smsExpiredDate"`
	CurrentFastbankPackageId int64  `json:"currentFastbankPackageId"`
	FastbankCreditBalance    int64  `json:"fastbankCreditBalance"`
	FastbankExpiredDate      string `json:"fastbankExpiredDate"`
}
type WebMasterCreateBody struct {
	Id                       int64  `json:"id"`
	Name                     string `json:"name"`
	WebDomain                string `json:"webDomain"`
	ApiKey                   string `json:"apiKey"`
	PaymentDetail            string `json:"paymentDetail"`
	CurrentWebPackageId      int64  `json:"currentWebPackageId"`
	WebExpiredDate           string `json:"webExpiredDate"`
	CurrentSmsPackageId      int64  `json:"currentSmsPackageId"`
	SmsCreditBalance         int64  `json:"smsCreditBalance"`
	SmsExpiredDate           string `json:"smsExpiredDate"`
	CurrentFastbankPackageId int64  `json:"currentFastbankPackageId"`
	FastbankCreditBalance    int64  `json:"fastbankCreditBalance"`
	FastbankExpiredDate      string `json:"fastbankExpiredDate"`
}

type WebMasterUpdateWebRenewalBody struct {
	WebId               int64  `json:"webId"`
	CurrentWebPackageId int64  `json:"currentWebPackageId"`
	WebExpiredDate      string `json:"webExpiredDate"`
}
type WebMasterUpdateSmsRenewalBody struct {
	WebId               int64  `json:"webId"`
	CurrentSmsPackageId int64  `json:"currentSmsPackageId"`
	SmsCredit           int64  `json:"smsCredit"`
	SmsExpiredDate      string `json:"smsExpiredDate"`
}
type WebMasterUpdateFastbankRenewalBody struct {
	WebId                    int64  `json:"webId"`
	CurrentFastbankPackageId int64  `json:"currentFastbankPackageId"`
	FastbankCredit           int64  `json:"fastbankCredit"`
	FastbankExpiredDate      string `json:"fastbankExpiredDate"`
}

type SmsRenewalPackage struct {
	Id             int64          `json:"id"`
	Name           string         `json:"name"`
	Price          float64        `json:"price"`
	RatePrice      float64        `json:"ratePrice"`
	RenewalCredits int            `json:"renewalCredits"`
	CreditDays     int            `json:"creditDays"`
	CreatedAt      time.Time      `json:"createdAt"`
	UpdatedAt      *time.Time     `json:"updatedAt"`
	DeletedAt      gorm.DeletedAt `json:"deletedAt"`
}
type SmsRenewalPackageListRequest struct {
	Search  string `form:"search"`
	Page    int    `form:"page" default:"1" min:"1"`
	Limit   int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol string `form:"sortCol"`
	SortAsc string `form:"sortAsc"`
}
type SmsRenewalPackageResponse struct {
	Id             int64     `json:"id"`
	Name           string    `json:"name"`
	Price          float64   `json:"price"`
	RatePrice      float64   `json:"ratePrice"`
	RenewalCredits int       `json:"renewalCredits"`
	CreditDays     int       `json:"creditDays"`
	CreatedAt      time.Time `json:"createdAt"`
}
type SmsRenewalBuyRequest struct {
	PackageId     int64  `form:"packageId" binding:"required"`
	PaymentDetail string `form:"paymentDetail"`
	AdminId       int64  `form:"adminId" json:"-"`
	BankId        int64  `form:"bankId"`
	AccountNo     string `form:"accountNo"`
	AccountName   string `form:"accountName"`
}
type SmsRenewalBuyUpdateBody struct {
	CurrentSmsPackageId *int64  `json:"currentSmsPackageId"`
	SmsCreditBalance    *int64  `json:"smsCreditBalance"`
	SmsExpiredDate      *string `json:"smsExpiredDate"`
}

type FastbankRenewalPackage struct {
	Id             int64          `json:"id"`
	Name           string         `json:"name"`
	Price          float64        `json:"price"`
	RenewalCredits int            `json:"renewalCredits"`
	CreditDays     int            `json:"creditDays"`
	CreatedAt      time.Time      `json:"createdAt"`
	UpdatedAt      *time.Time     `json:"updatedAt"`
	DeletedAt      gorm.DeletedAt `json:"deletedAt"`
}
type FastbankRenewalPackageListRequest struct {
	Search  string `form:"search"`
	Page    int    `form:"page" default:"1" min:"1"`
	Limit   int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol string `form:"sortCol"`
	SortAsc string `form:"sortAsc"`
}
type FastbankRenewalPackageResponse struct {
	Id             int64     `json:"id"`
	Name           string    `json:"name"`
	Price          float64   `json:"price"`
	RenewalCredits int       `json:"renewalCredits"`
	CreditDays     int       `json:"creditDays"`
	CreatedAt      time.Time `json:"createdAt"`
}
type FastbankRenewalBuyRequest struct {
	PackageId     int64  `form:"packageId" binding:"required"`
	PaymentDetail string `form:"paymentDetail"`
	AdminId       int64  `form:"adminId" json:"-"`
	BankId        int64  `form:"bankId"`
	AccountNo     string `form:"accountNo"`
	AccountName   string `form:"accountName"`
}
type FastbankRenewalBuyUpdateBody struct {
	CurrentFastbankPackageId *int64  `json:"currentFastbankPackageId"`
	FastbankCreditBalance    *int64  `json:"fastbankCreditBalance"`
	FastbankExpiredDate      *string `json:"fastbankExpiredDate"`
}
type FastbankRenewalFreeUpdateBody struct {
	CurrentFastbankPackageId *int64 `json:"currentFastbankPackageId"`
	FastbankFreeStartDate    string `json:"fastbankFreeStartDate"`
	FastbankFreeEndDate      string `json:"fastbankFreeEndDate"`
}

type BankAccountInfoResponse struct {
	DepositAccountCount   int     `json:"depositAccountCount"`
	LastMonthDepositPrice float64 `json:"lastMonthDepositPrice"`
	WithdrawAccountCount  int     `json:"withdrawAccountCount"`
	SavingAccountCount    int     `json:"savingAccountCount"`
}

type WebRenewalPackage struct {
	Id                     int64          `json:"id"`
	Name                   string         `json:"name"`
	PricePerMonth          float64        `json:"pricePerMonth"`
	RenewalDays            int            `json:"renewalDays"`
	LimitAdminCount        int            `json:"limitAdminCount"`
	LimitUserCount         int            `json:"limitUserCount"`
	LimitTransactionCount  int            `json:"limitTransactionCount"`
	LimitTransactionAmount float64        `json:"limitTransactionAmount"`
	LimitBankAccountCount  int            `json:"limitBankAccountCount"`
	CreatedAt              time.Time      `json:"createdAt"`
	UpdatedAt              *time.Time     `json:"updatedAt"`
	DeletedAt              gorm.DeletedAt `json:"deletedAt"`
}
type WebRenewalPackageListRequest struct {
	Search  string `form:"search"`
	Page    int    `form:"page" default:"1" min:"1"`
	Limit   int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol string `form:"sortCol"`
	SortAsc string `form:"sortAsc"`
}
type WebRenewalPackageResponse struct {
	Id                     int64      `json:"id"`
	Name                   string     `json:"name"`
	PricePerMonth          float64    `json:"pricePerMonth"`
	RenewalDays            int        `json:"renewalDays"`
	LimitAdminCount        int        `json:"limitAdminCount"`
	LimitUserCount         int        `json:"limitUserCount"`
	LimitTransactionCount  int        `json:"limitTransactionCount"`
	LimitTransactionAmount float64    `json:"limitTransactionAmount"`
	LimitBankAccountCount  int        `json:"limitBankAccountCount"`
	IsEnabled              bool       `json:"isEnabled"`
	LimitDetail            string     `json:"limitDetail"`
	CreatedAt              time.Time  `json:"createdAt"`
	UpdatedAt              *time.Time `json:"updatedAt"`
}
type WebRenewalBuyRequest struct {
	PackageId     int64  `form:"packageId" binding:"required"`
	RenewalMonths int64  `form:"renewalMonths" binding:"required" min:"1"`
	PaymentDetail string `form:"paymentDetail"`
	AdminId       int64  `form:"adminId" json:"-"`
	BankId        int64  `form:"bankId"`
	AccountNo     string `form:"accountNo"`
	AccountName   string `form:"accountName"`
}
type WebRenewalBuyUpdateBody struct {
	CurrentWebPackageId *int64  `json:"currentWebPackageId"`
	WebExpiredDate      *string `json:"webExpiredDate"`
}

type Invoice struct {
	Id                int64          `json:"id"`
	WebId             int64          `json:"webId"`
	WebName           string         `json:"webName"`
	InvoiceNo         string         `json:"invoiceNo"`
	InvoiceTypeId     int64          `json:"invoiceTypeId"`
	PackageId         int64          `json:"packageId"`
	RenewDays         int            `json:"renewDays"`
	RenewCreditAmount int            `json:"renewCreditAmount"`
	BankId            int64          `json:"bankId"`
	AccountNo         string         `json:"accountNo"`
	AccountName       string         `json:"accountName"`
	InvoiceAt         *time.Time     `json:"invoiceAt"`
	ExpireAt          *time.Time     `json:"expireAt"`
	PaidAt            *time.Time     `json:"paidAt"`
	PaidBy            *int64         `json:"paidBy"`
	PaymentDetail     string         `json:"paymentDetail"`
	SlipImagePath     string         `json:"slipImagePath"`
	ConfirmBy         *int64         `json:"confirmBy"`
	ConfirmAt         *time.Time     `json:"confirmAt"`
	StatusId          int64          `json:"statusId"`
	SumPrice          float64        `json:"sumPrice"`
	VatPercent        float64        `json:"vatPercent"`
	VatPrice          float64        `json:"vatPrice"`
	DiscountPrice     float64        `json:"discountPrice"`
	TotalPrice        float64        `json:"totalPrice"`
	CreateBy          int64          `json:"createBy"`
	CreatedAt         time.Time      `json:"createdAt"`
	UpdatedAt         *time.Time     `json:"updatedAt"`
	DeletedAt         gorm.DeletedAt `json:"deletedAt"`
}
type InvoiceListRequest struct {
	TypeId   *int64 `form:"typeId"`
	StatusId *int64 `form:"statusId"`
	Search   string `form:"search"`
	Page     int    `form:"page" default:"1" min:"1"`
	Limit    int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol  string `form:"sortCol"`
	SortAsc  string `form:"sortAsc"`
}
type InvoiceResponse struct {
	Id                int64      `json:"id"`
	WebId             int64      `json:"webId"`
	WebName           string     `json:"webName"`
	WebDomain         string     `json:"webDomain"`
	InvoiceNo         string     `json:"invoiceNo"`
	InvoiceTypeId     int64      `json:"invoiceTypeId"`
	InvoiceType       string     `json:"invoiceType"`
	InvoiceTypeDetail string     `json:"invoiceTypeDetail"`
	PackageId         int64      `json:"packageId"`
	RenewDays         int        `json:"renewDays"`
	RenewCreditAmount int        `json:"renewCreditAmount"`
	BankId            int64      `json:"bankId"`
	BankCode          string     `json:"bankCode"`
	AccountNo         string     `json:"accountNo"`
	AccountName       string     `json:"accountName"`
	PackageDetail     string     `json:"packageDetail"`
	InvoiceAt         *time.Time `json:"invoiceAt"`
	ExpireAt          *time.Time `json:"expireAt"`
	PaidAt            *time.Time `json:"paidAt"`
	PaidBy            *int64     `json:"paidBy"`
	PaidByName        string     `json:"paidByName"`
	PaymentDetail     string     `json:"paymentDetail"`
	SlipImagePath     string     `json:"slipImagePath"`
	ConfirmBy         *int64     `json:"confirmBy"`
	ConfirmByName     string     `json:"confirmByName"`
	ConfirmAt         *time.Time `json:"confirmAt"`
	StatusId          int64      `json:"statusId"`
	Status            string     `json:"status"`
	StatusDetail      string     `json:"statusDetail"`
	SumPrice          float64    `json:"sumPrice"`
	VatPercent        float64    `json:"vatPercent"`
	VatPrice          float64    `json:"vatPrice"`
	DiscountPrice     float64    `json:"discountPrice"`
	TotalPrice        float64    `json:"totalPrice"`
	CreateBy          int64      `json:"createBy"`
	CreatedAt         time.Time  `json:"createdAt"`
	UpdatedAt         *time.Time `json:"updatedAt"`
}
type InvoiceBillResponse struct {
	Id                int64      `json:"id"`
	WebId             int64      `json:"webId"`
	WebName           string     `json:"webName"`
	WebDomain         string     `json:"webDomain"`
	InvoiceNo         string     `json:"invoiceNo"`
	InvoiceTypeId     int64      `json:"invoiceTypeId"`
	InvoiceType       string     `json:"invoiceType"`
	InvoiceTypeDetail string     `json:"invoiceTypeDetail"`
	PackageId         int64      `json:"packageId"`
	RenewAmount       int        `json:"renewAmount"`
	BankId            int64      `json:"bankId"`
	AccountNo         string     `json:"accountNo"`
	AccountName       string     `json:"accountName"`
	PackageDetail     string     `json:"packageDetail"`
	InvoiceAt         *time.Time `json:"invoiceAt"`
	ExpireAt          *time.Time `json:"expireAt"`
	PaidAt            *time.Time `json:"paidAt"`
	PaidBy            *int64     `json:"paidBy"`
	PaidByName        string     `json:"paidByName"`
	PaymentDetail     string     `json:"paymentDetail"`
	ConfirmBy         *int64     `json:"confirmBy"`
	ConfirmByName     string     `json:"confirmByName"`
	ConfirmAt         *time.Time `json:"confirmAt"`
	StatusId          int64      `json:"statusId"`
	Status            string     `json:"status"`
	StatusDetail      string     `json:"statusDetail"`
	SumPrice          float64    `json:"sumPrice"`
	VatPercent        float64    `json:"vatPercent"`
	VatPrice          float64    `json:"vatPrice"`
	DiscountPrice     float64    `json:"discountPrice"`
	TotalPrice        float64    `json:"totalPrice"`
	CreateBy          int64      `json:"createBy"`
	CreatedAt         time.Time  `json:"createdAt"`
	UpdatedAt         *time.Time `json:"updatedAt"`
}
type InvoiceReceiptResponse struct {
	Id                int64      `json:"id"`
	WebId             int64      `json:"webId"`
	WebName           string     `json:"webName"`
	WebDomain         string     `json:"webDomain"`
	InvoiceNo         string     `json:"invoiceNo"`
	InvoiceTypeId     int64      `json:"invoiceTypeId"`
	InvoiceType       string     `json:"invoiceType"`
	InvoiceTypeDetail string     `json:"invoiceTypeDetail"`
	PackageId         int64      `json:"packageId"`
	RenewAmount       int        `json:"renewAmount"`
	BankId            int64      `json:"bankId"`
	AccountNo         string     `json:"accountNo"`
	AccountName       string     `json:"accountName"`
	PackageDetail     string     `json:"packageDetail"`
	InvoiceAt         *time.Time `json:"invoiceAt"`
	ExpireAt          *time.Time `json:"expireAt"`
	PaidAt            *time.Time `json:"paidAt"`
	PaidBy            *int64     `json:"paidBy"`
	PaidByName        string     `json:"paidByName"`
	PaymentDetail     string     `json:"paymentDetail"`
	ConfirmBy         *int64     `json:"confirmBy"`
	ConfirmByName     string     `json:"confirmByName"`
	ConfirmAt         *time.Time `json:"confirmAt"`
	StatusId          int64      `json:"statusId"`
	Status            string     `json:"status"`
	StatusDetail      string     `json:"statusDetail"`
	SumPrice          float64    `json:"sumPrice"`
	VatPercent        float64    `json:"vatPercent"`
	VatPrice          float64    `json:"vatPrice"`
	DiscountPrice     float64    `json:"discountPrice"`
	TotalPrice        float64    `json:"totalPrice"`
	CreateBy          int64      `json:"createBy"`
	CreatedAt         time.Time  `json:"createdAt"`
	UpdatedAt         *time.Time `json:"updatedAt"`
}

type InvoiceCreateBody struct {
	Id                int64      `json:"id"`
	WebId             int64      `json:"webId"`
	WebName           string     `json:"webName"`
	InvoiceNo         string     `json:"invoiceNo"`
	InvoiceTypeId     int64      `json:"invoiceTypeId"`
	PackageId         int64      `json:"packageId"`
	RenewDays         int        `json:"renewDays"`
	BankId            int64      `json:"bankId"`
	AccountNo         string     `json:"accountNo"`
	AccountName       string     `json:"accountName"`
	RenewCreditAmount int        `json:"renewCreditAmount"`
	PackageDetail     string     `json:"packageDetail"`
	InvoiceAt         time.Time  `json:"invoiceAt"`
	ExpireAt          time.Time  `json:"expireAt"`
	PaidAt            *time.Time `json:"paidAt"`
	PaidBy            *int64     `json:"paidBy"`
	PaymentDetail     string     `json:"paymentDetail"`
	ConfirmBy         *int64     `json:"confirmBy"`
	ConfirmAt         *time.Time `json:"confirmAt"`
	ConfirmByName     *string    `json:"confirmByName"`
	StatusId          int64      `json:"statusId"`
	SumPrice          float64    `json:"sumPrice"`
	VatPercent        float64    `json:"vatPercent"`
	VatPrice          float64    `json:"vatPrice"`
	DiscountPrice     float64    `json:"discountPrice"`
	TotalPrice        float64    `json:"totalPrice"`
	CreateBy          int64      `json:"createBy"`
}
type InvoiceUpdateBody struct {
	PaidAt    *time.Time `json:"paidAt"`
	ConfirmBy *int64     `json:"confirmBy"`
	ConfirmAt *time.Time `json:"confirmAt"`
	StatusId  *int64     `json:"statusId"`
}

type InvoiceHengPaymentRequest struct {
	InvoiceId int64 `form:"invoiceId" binding:"required"`
	AdminId   int64 `form:"adminId" json:"-"`
}
type InvoiceConfirmPaymentRequest struct {
	InvoiceId     int64      `form:"invoiceId" binding:"required"`
	PaidAt        *time.Time `form:"paidAt"`
	AdminId       int64      `form:"adminId" json:"-"`
	SlipImagePath string     `form:"slipImagePath"`
	RawQrCode     string     `form:"rawQrCode"`
}
type InvoiceConfirmPaymentBody struct {
	Id            int64     `json:"id"`
	PaidAt        time.Time `json:"paidAt"`
	PaidBy        int64     `json:"paidBy"`
	SlipImagePath string    `json:"slipImagePath"`
	RawQrCode     string    `json:"rawQrCode"`
}

type RemoteWebMasterRegisterRequest struct {
	Name string `json:"name"`
}
type RemoteWebMasterResponse struct {
	Id   int64  `json:"id"`
	Name string `json:"name"`
}

type InvoiceConfirmRequest struct {
	InvoiceId int64 `form:"invoiceId" binding:"required"`
	AdminId   int64 `form:"adminId" json:"-"`
}
type InvoiceConfirmBody struct {
	Id            int64     `json:"id"`
	ConfirmAt     time.Time `json:"confirmAt"`
	ConfirmByName string    `json:"confirmByName"`
	DiscountPrice float64   `json:"discountPrice"`
	TotalPrice    float64   `json:"totalPrice"`
}

type InvoicePayonexPaymentRequest struct {
	InvoiceId int64 `form:"invoiceId" binding:"required"`
	AdminId   int64 `form:"adminId" json:"-"`
}
