package model

import "time"

// 3. โบนัสกิจกรรม
// 3.1.แจกโบนัสแนะนำเพื่อน * เข้าเฉพาะยอดที่เกิน ที่ตั้งค่าไว้
// - confirmUserTakeAffiliateAmount กดถอนรายได้
// 3.2.แจกโบนัสคืนยอดเสีย * เข้าเฉพาะยอดที่เกิน ที่ตั้งค่าไว้
// - TakeUserReturnAmount กดรับโบนัสคืนยอดเสีย
// 3.3.รายได้พันธมิตร
// - WithdrawAllianceIncome กดโยกเคลียร์ยอดพันธมิตร - ไม่มีอนุมัติ
// 3.4.รายได้จากกิจกรรมกงล้อนำโชค
// - CheckLuckyWheelUserIncome หมุนกงล้อนำโชค
// 3.5.แจกโบนัสฟรี คืนยอดcommission * เข้าเฉพาะยอดที่เกิน ที่ตั้งค่าไว้
// - TakeUserReturnTurnAmount กดรับโบนัสคืนยอดcommission

const (
	BANK_PENDING_RECORD_TYPE_DEPOSIT         = 1
	BANK_PENDING_RECORD_TYPE_WITHDRAW        = 2
	BANK_PENDING_RECORD_TYPE_BONUS_AFF       = 3
	BANK_PENDING_RECORD_TYPE_RETURN_LOSS     = 4
	BANK_PENDING_RECORD_TYPE_ALLIANCE_INCOME = 5
	BANK_PENDING_RECORD_TYPE_LUCKY_WHEEL     = 6
	BANK_PENDING_RECORD_TYPE_RETURN_TURN     = 7
)
const (
	BANK_PENDING_RECORD_TYPE_APPROVED = "APPROVED"
	BANK_PENDING_RECORD_TYPE_REJECTED = "REJECTED"
)

type AdminCorpBankSummaryResponse struct {
	OfDate                 string    `json:"ofDate"`
	UpdateAt               time.Time `json:"updateAt"`
	TotalRecordCount       int64     `json:"totalRecordCount"`
	TotalProfitAmount      float64   `json:"totalProfitAmount"`
	DepositCount           int64     `json:"depositCount"`
	DepositAmount          float64   `json:"depositAmount"`
	WithdrawCount          int64     `json:"withdrawCount"`
	WithdrawAmount         float64   `json:"withdrawAmount"`
	BonusCount             int64     `json:"bonusCount"`
	BonusAmount            float64   `json:"bonusAmount"`
	CreditBackCount        int64     `json:"creditBackCount"`
	CreditBackAmount       float64   `json:"creditBackAmount"`
	NewMemberDepositCount  int64     `json:"newMemberDepositCount"`
	NewMemberDepositAmount float64   `json:"newMemberDepositAmount"`
	OldMemberDepositCount  int64     `json:"oldMemberDepositCount"`
	OldMemberDepositAmount float64   `json:"oldMemberDepositAmount"`
}

// type AdminCorpTransactionResponse struct {
// 	Id               int64     `json:"id"`
// 	UserId           int64     `json:"userId"`
// 	TransferTypeId   int64     `json:"transferTypeId"`
// 	TransferTypeName string    `json:"transferTypeName"`
// 	MemberCode       string    `json:"memberCode"`
// 	UserFullname     string    `json:"userFullname"`
// 	UserPhone        string    `json:"userPhone"`
// 	TransferAt       time.Time `json:"transferAt"`
// 	CreditAmount     float64   `json:"creditAmount"`
// 	AdminFullname    string    `json:"adminFullname"`
// }

type ReportBankTotalDepositWithdrawRequest struct {
	DateType string `form:"dateType" default:"daily"`
	FromDate string `form:"fromDate"`
	ToDate   string `form:"toDate"`
}
type ReportBankTotalDepositRaw struct {
	BankId          int64   `json:"bankId"`
	BankCode        string  `json:"bankCode"`
	BankName        string  `json:"bankName"`
	ToAccountId     int64   `json:"toAccountId"`
	ToAccount       string  `json:"toAccount"`
	ToAccountNumber string  `json:"toAccountNumber"`
	TotalDeposit    float64 `json:"totalDeposit"`
	DepositCount    int64   `json:"depositCount"`
}
type ReportBankTotalWithdrawRaw struct {
	BankId            int64   `json:"bankId"`
	BankCode          string  `json:"bankCode"`
	BankName          string  `json:"bankName"`
	FromAccountId     int64   `json:"fromAccountId"`
	FromAccount       string  `json:"fromAccount"`
	FromAccountNumber string  `json:"fromAccountNumber"`
	TotalWithdraw     float64 `json:"totalWithdraw"`
	WithdrawCount     int64   `json:"withdrawCount"`
}
type ReportPaymentTotalDepositRaw struct {
	MerchantId    int64   `json:"merchantId"`
	TotalDeposit  float64 `json:"totalDeposit"`
	DepositCount  int64   `json:"depositCount"`
	TotalWithdraw float64 `json:"totalWithdraw"`
	WithdrawCount int64   `json:"withdrawCount"`
}
type ReportBankTotalDepositWithdrawItem struct {
	Name          string  `json:"name"`
	TotalDeposit  float64 `json:"totalDeposit"`
	DepositCount  int64   `json:"depositCount"`
	TotalWithdraw float64 `json:"totalWithdraw"`
	WithdrawCount int64   `json:"withdrawCount"`
}
type ReportBankTotalDepositWithdrawResponse struct {
	List          []ReportBankTotalDepositWithdrawItem `json:"list"`
	TotalDeposit  float64                              `json:"totalDeposit"`
	DepositCount  int64                                `json:"depositCount"`
	TotalWithdraw float64                              `json:"totalWithdraw"`
	WithdrawCount int64                                `json:"withdrawCount"`
}

type BankPendingRecord struct {
	Id                  int64      `json:"-"`
	RefKey              string     `json:"-"`
	UserId              int64      `json:"-"`
	Name                string     `json:"-"`
	RecordTypeId        int64      `json:"-"`
	MemberCode          string     `json:"-"`
	UserFullname        string     `json:"-"`
	UserPhone           string     `json:"-"`
	FromBankCode        string     `json:"-"`
	FromAccount         string     `json:"-"`
	ToBankCode          string     `json:"-"`
	ToAccount           string     `json:"-"`
	TransferAt          *time.Time `json:"-"`
	CreditAmount        float64    `json:"-"`
	AdminFullname       string     `json:"-"`
	SocketAt            *time.Time `json:"-"`
	UpdateStatus        string     `json:"-"`
	UpdateAdminFullname string     `json:"-"`
	CreatedAt           time.Time  `json:"-"`
	UpdatedAt           *time.Time `json:"-"`
}
type BankPendingRecordResponse struct {
	Id                  int64      `json:"id"`
	RefKey              string     `json:"refKey"`
	UserId              int64      `json:"userId"`
	Name                string     `json:"name"`
	RecordTypeId        int64      `json:"recordTypeId"`
	MemberCode          string     `json:"memberCode"`
	UserFullname        string     `json:"userFullname"`
	UserPhone           string     `json:"userPhone"`
	FromBankCode        string     `json:"fromBankCode"`
	FromAccount         string     `json:"fromAccount"`
	ToBankCode          string     `json:"toBankCode"`
	ToAccount           string     `json:"toAccount"`
	TransferAt          *time.Time `json:"transferAt"`
	CreditAmount        float64    `json:"creditAmount"`
	AdminFullname       string     `json:"adminFullname"`
	SocketAt            *time.Time `json:"socketAt"`
	UpdateStatus        string     `json:"updateStatus"`
	UpdateAdminFullname string     `json:"updateAdminFullname"`
}
type BankPendingRecordCreateRequest struct {
	UserId        int64      `json:"userId"`
	Name          string     `json:"name"`
	RecordTypeId  int64      `json:"recordTypeId"`
	MemberCode    string     `json:"memberCode"`
	UserFullname  string     `json:"userFullname"`
	UserPhone     string     `json:"userPhone"`
	FromBankCode  string     `json:"fromBankCode"`
	FromAccount   string     `json:"fromAccount"`
	ToBankCode    string     `json:"toBankCode"`
	ToAccount     string     `json:"toAccount"`
	TransferAt    *time.Time `json:"transferAt"`
	CreditAmount  float64    `json:"creditAmount"`
	AdminFullname string     `json:"adminFullname"`
}
type BankPendingRecordCreateBody struct {
	Id            int64      `json:"id"`
	RefKey        string     `json:"refKey"`
	UserId        int64      `json:"userId"`
	Name          string     `json:"name"`
	RecordTypeId  int64      `json:"recordTypeId"`
	MemberCode    string     `json:"memberCode"`
	UserFullname  string     `json:"userFullname"`
	UserPhone     string     `json:"userPhone"`
	FromBankCode  string     `json:"fromBankCode"`
	FromAccount   string     `json:"fromAccount"`
	ToBankCode    string     `json:"toBankCode"`
	ToAccount     string     `json:"toAccount"`
	TransferAt    *time.Time `json:"transferAt"`
	CreditAmount  float64    `json:"creditAmount"`
	AdminFullname string     `json:"adminFullname"`
}

// type BankPendingRecordUpdateRequest struct {
// 	UserId              *int64     `json:"userId"`
// 	Name                *string    `json:"name"`
// 	RecordTypeId        *int64     `json:"recordTypeId"`
// 	MemberCode          *string    `json:"memberCode"`
// 	UserFullname        *string    `json:"userFullname"`
// 	UserPhone           *string    `json:"userPhone"`
// 	FromBankCode        *string    `json:"fromBankCode"`
// 	FromAccount         *string    `json:"fromAccount"`
// 	ToBankCode          *string    `json:"toBankCode"`
// 	ToAccount           *string    `json:"toAccount"`
// 	TransferAt          *time.Time `json:"transferAt"`
// 	CreditAmount        *float64   `json:"creditAmount"`
// 	AdminFullname       *string    `json:"adminFullname"`
// 	SocketAt            *time.Time `json:"socketAt"`
// 	UpdateStatus        *string    `json:"updateStatus"`
// 	UpdateAdminFullname *string    `json:"updateAdminFullname"`
// }
type BankPendingRecordUpdateBody struct {
	UserId              *int64     `json:"userId"`
	Name                *string    `json:"name"`
	RecordTypeId        *int64     `json:"recordTypeId"`
	MemberCode          *string    `json:"memberCode"`
	UserFullname        *string    `json:"userFullname"`
	UserPhone           *string    `json:"userPhone"`
	FromBankCode        *string    `json:"fromBankCode"`
	FromAccount         *string    `json:"fromAccount"`
	ToBankCode          *string    `json:"toBankCode"`
	ToAccount           *string    `json:"toAccount"`
	TransferAt          *time.Time `json:"transferAt"`
	CreditAmount        *float64   `json:"creditAmount"`
	AdminFullname       *string    `json:"adminFullname"`
	SocketAt            *time.Time `json:"socketAt"`
	UpdateStatus        *string    `json:"updateStatus"`
	UpdateAdminFullname *string    `json:"updateAdminFullname"`
}

type BankPendingRecordApproveRequest struct {
	Id      int64 `json:"id"`
	AdminId int64 `json:"adminId"`
}
type BankPendingRecordRejectRequest struct {
	Id      int64 `json:"id"`
	AdminId int64 `json:"adminId"`
}

type RawBankRecord struct {
	Id                     int64      `json:"id"`
	UserId                 *int64     `json:"userId"`
	TransactionTypeId      int64      `json:"transactionTypeId"`
	TransactionStatusId    int64      `json:"transactionStatusId"`
	FromBankCode           string     `json:"fromBankCode"`
	FromAccountNumber      string     `json:"fromAccountNumber"`
	ToBankCode             string     `json:"toBankCode"`
	ToAccountNumber        string     `json:"toAccountNumber"`
	CreditAmount           float64    `json:"creditAmount"`
	TransferAt             *time.Time `json:"transferAt"`
	CreatedByAdminId       int64      `json:"createdByAdminId"`
	CreatedByAdminFullname string     `json:"createdByAdminFullname"`
}
