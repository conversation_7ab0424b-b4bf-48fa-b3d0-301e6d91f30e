package model

import (
	"time"

	"gorm.io/gorm"
)

const (
	ADMIN_STATUS_ACTIVE   string = "ACTIVE"
	ADMIN_STATUS_DEACTIVE string = "DEACTIVE"
)

type Admin struct {
	Id           int64          `json:"id"`
	Username     string         `json:"username"`
	Password     string         `json:"password"`
	Fullname     string         `json:"fullname"`
	Firstname    string         `json:"firstname"`
	Lastname     string         `json:"lastname"`
	Phone        string         `json:"phone"`
	Email        string         `json:"email"`
	Role         string         `json:"role"`
	Status       string         `json:"status"`
	AdminGroupId int64          `json:"adminGroupId"`
	IsVerifyTotp bool           `json:"isVerifyTotp"`
	CreatedAt    time.Time      `json:"createdAt"`
	UpdatedAt    time.Time      `json:"updatedAt"`
	DeletedAt    gorm.DeletedAt `json:"deletedAt"`
	LogedinAt    *time.Time     `json:"logedinAt" gorm:"default:CURRENT_TIMESTAMP"`
}

type CreateAdmin struct {
	Username     string `json:"username" validate:"required,min=8,max=30"`
	Password     string `json:"password" validate:"required,min=8,max=30"`
	Fullname     string `json:"fullname" validate:"required,min=2,max=30"`
	Phone        string `json:"phone"`
	Email        string `json:"email" validate:"omitempty,email"`
	Status       string `json:"status" validate:"required" default:"ACTIVE"`
	AdminGroupId int64  `json:"adminGroupId" validate:"required"`
	CreateBy     int64  `json:"-"`
}

type LoginAdmin struct {
	Username     string `json:"username" validate:"required,min=8,max=30"`
	Password     string `json:"password" validate:"required,min=8,max=30"`
	CaptchaId    string `json:"captchaId" validate:"required"`
	CaptchaValue string `json:"captchaValue" validate:"required"`
	IpAddress    string `json:"ipAddress"`
	Browser      string `json:"-"`
	Device       string `json:"-"`
	Agent        string `json:"agent"`
}

type LoginAdminBody struct {
	Username     string `json:"username"`
	Password     string `json:"password"`
	CaptchaId    string `json:"captchaId"`
	CaptchaValue string `json:"captchaValue"`
	IpAddress    string `json:"ipAddress"`
	Browser      string `json:"browser"`
	Device       string `json:"device"`
	Agent        string `json:"agent"`
}

type LoginResponse struct {
	Token          string `json:"token"`
	RefreshToken   string `json:"refreshToken"`
	Id             *int64 `json:"id"`
	IsVerifyTotp   *bool  `json:"isVerifyTotp"`
	WebSettingTotp *bool  `json:"webSettingTotp"`
}

type AdminLoginUpdate struct {
	Ip        string    `json:"ip"`
	LogedinAt time.Time `json:"logedinAt"`
}

type AdminCreateGroup struct {
	GroupId       int64   `json:"groupId" validate:"required"`
	PermissionIds []int64 `json:"permissionIds" validate:"required"`
}

type AdminPermissionList struct {
	GroupId      int64 `json:"groupId"`
	PermissionId int64 `json:"permissionId"`
}

type AdminGroupPermission struct {
	Id           int64      `json:"id"`
	GroupId      int64      `json:"groupId"`
	PermissionId int64      `json:"permissionId"`
	CreatedAt    time.Time  `json:"createdAt"`
	UpdatedAt    time.Time  `json:"updatedAt"`
	DeletedAt    *time.Time `json:"deletedAt"`
}

type AdminGroupPermissionResponse struct {
	Id          int64            `json:"id"`
	Name        string           `json:"name"`
	Permissions []PermissionList `json:"permissions"`
}

type AdminGroup struct {
	Id         int64          `json:"id"`
	Name       string         `json:"name"`
	AdminCount int64          `json:"adminCount"`
	CreatedAt  time.Time      `json:"createdAt"`
	UpdatedAt  time.Time      `json:"updatedAt"`
	DeletedAt  gorm.DeletedAt `json:"deletedAt"`
}

type AdminUpdateGroup struct {
	GroupId       int64   `json:"-"`
	Name          string  `json:"name" validate:"required"`
	PermissionIds []int64 `json:"permissionIds" validate:"required"`
	UpdateBy      int64   `json:"-"`
}

type AdminGroupQuery struct {
	Page   int    `form:"page" validate:"min=1"`
	Limit  int    `form:"limit" validate:"min=1,max=100"`
	Search string `form:"search"`
}

type AdminListQuery struct {
	Page         int    `form:"page" validate:"min=1"`
	Limit        int    `form:"limit" validate:"min=1,max=100"`
	Search       string `form:"search"`
	Status       string `form:"status"`
	AdminGroupId string `form:"adminGroupId"`
}

type AdminGroupPagination struct {
	Total int64            `json:"total"`
	List  []GroupCountList `json:"list"`
}

type UpdateAdmin struct {
	Fullname     string
	Firstname    string
	Lastname     string
	Phone        string
	Email        *string
	Role         string
	Status       string
	AdminGroupId *int64
}

type AdminUpdateRequest struct {
	Fullname     string `json:"fullname" validate:"required,min=2,max=30"`
	Email        string `json:"email" validate:"omitempty,email"`
	Phone        string `json:"phone"`
	AdminGroupId *int64 `json:"adminGroupId"`
	Status       string `json:"status" validate:"required" example:"ACTIVE,DEACTIVE"`
	UpdateBy     int64  `json:"-"`
}

type AdminPermission struct {
	AdminId      int64 `json:"adminId"`
	PermissionId int64 `json:"permissionId"`
}

type AdminList struct {
	Id             int64  `json:"id"`
	Username       string `json:"username"`
	Fullname       string `json:"fullname"`
	Phone          string `json:"phone"`
	Email          string `json:"email"`
	Role           string `json:"role"`
	AdminGroupName string `json:"adminGroupName"`
	Status         string `json:"status"`
}

type AdminDetail struct {
	Id                 int64            `json:"id"`
	Username           string           `json:"username"`
	Fullname           string           `json:"fullname"`
	Phone              string           `json:"phone"`
	Email              string           `json:"email"`
	Role               string           `json:"role"`
	Status             string           `json:"status"`
	PermissionList     []PermissionList `json:"permissionList"`
	Group              *GroupDetail     `json:"group"`
	IsVerifyTotp       bool             `json:"isVerifyTotp"`
	WebSettingTotp     bool             `json:"webSettingTotp"`
	TokenExpiredMinute int64            `json:"tokenExpiredMinute"`
}
type GetAdminDetailTotp struct {
	Id             *int64 `json:"id"`
	IsVerifyTotp   bool   `json:"isVerifyTotp"`
	WebSettingTotp bool   `json:"webSettingTotp"`
}

type AdminGroupId struct {
	AdminGroupId int64 `json:"adminGroupId"`
}

type AdminUpdatePassword struct {
	Password string `json:"password" validate:"required,min=8,max=30"`
	UpdateBy int64  `json:"-"`
}

type AdminDeleteRequest struct {
	AdminId  int64 `json:"adminId" validate:"required"`
	UpdateBy int64 `json:"-"`
}

type CapchaResponse struct {
	Id     string `json:"id"`
	Base64 string `json:"base64"`
}

type CapchaVerifyRequest struct {
	Id          string `form:"id"`
	VerifyValue string `form:"verifyValue"`
}
type CapchaVerifyResponse struct {
	IsValid bool `json:"isValid"`
}

type AdminLoginLog struct {
	Id        int64     `json:"id"`
	Username  string    `json:"username"`
	Browser   string    `json:"browser"`
	Device    string    `json:"device"`
	IpAddress string    `json:"ipAddress"`
	CreateAt  time.Time `json:"createAt"`
}
type AdminLoginLogListRequest struct {
	Search  string `form:"search"`
	Page    int    `form:"page" default:"1" min:"1"`
	Limit   int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol string `form:"sortCol"`
	SortAsc string `form:"sortAsc"`
}
type AdminLoginLogResponse struct {
	Id        int64     `json:"id"`
	Username  string    `json:"username"`
	Browser   string    `json:"browser"`
	Device    string    `json:"device"`
	IpAddress string    `json:"ipAddress"`
	CreateAt  time.Time `json:"createAt"`
}

type AdminSingleSession struct {
	Id        int64     `json:"id"`
	AdminId   int64     `json:"adminId"`
	Md5Token  string    `json:"md5Token"`
	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
}
type UserSingleSession struct {
	Id        int64     `json:"id"`
	UserId    int64     `json:"userId"`
	Md5Token  string    `json:"md5Token"`
	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
}

type AdminLog struct {
	Id        int64     `json:"id"`
	Name      string    `json:"name"`
	AdminId   int64     `json:"adminId"`
	JsonReq   string    `json:"jsonReq"`
	CreatedAt time.Time `json:"createdAt"`
}
type AdminLogCreateBody struct {
	Id      int64  `json:"id"`
	Name    string `json:"name"`
	AdminId int64  `json:"adminId"`
	JsonReq string `json:"jsonReq"`
}

type TotpGererateSecret struct {
	AdminId int64 `json:"adminId"`
}

type TotpGererateSecretResponse struct {
	QRCode string `json:"qrCode"`
	Secret string `json:"secret"`
}

type TotpVerifyCheck struct {
	AdminId int64  `json:"adminId"`
	Totp    string `json:"totp"`
}
type AdminTotp struct {
	Id           int64  `json:"id"`
	TotpSecret   string `json:"totpSecret"`
	IsVerifyTotp bool   `json:"isVerifyTotp"`
}

type TotpVerifyResponse struct {
	TotpVerify   bool   `json:"totpVerify"`
	Token        string `json:"token"`
	RefreshToken string `json:"refreshToken"`
}

type TotpResetRequest struct {
	AdminId     int64 `json:"adminId"`
	UpdatedById int64 `json:"-"`
}

type AdminWebSetting struct {
	IsTotpVerify       bool  `json:"isTotpVerify"`
	TokenExpiredMinute int64 `json:"tokenExpiredMinute"`
}
type GetAdminTotpSetting struct {
	IsTotpVerify bool `json:"isTotpVerify"`
}

type WebSettingTotpResponse struct {
	WebSettingTotp bool `json:"webSettingTotp"`
}

type AdminRefreshToken struct {
	Token        string `json:"token"`
	RefreshToken string `json:"refreshToken"`
}
type AdminRefreshTokenRequest struct {
	AdminId      int64  `json:"-"`
	RefreshToken string `json:"refreshToken"`
}

type GetAdminOptionList struct {
	Id       int64  `json:"id"`
	Username string `json:"username"`
	Fullname string `json:"fullname"`
}
