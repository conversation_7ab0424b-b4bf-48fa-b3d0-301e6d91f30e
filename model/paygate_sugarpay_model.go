package model

import (
	"time"
)

// - ช่วงเวลาทำการของระบบฝาก (Deposit) คือ 02:01 - 23:00 น. [ช่วงเวลา 23:01 - 02:00 รายการเข้าช้า]
// - ช่วงเวลาทำการของระบบถอน (Withdraw) คือ 00:31 - 22:50 น. [ปิดถอน 22:51 - 00:30]

// *การฝากถอนต่อรายการ*
// ฝากขั้นต่ำ : 10 บาท
// ถอนขั้นต่ำ : 10 บาท

// ฝากมากสุด : ไม่จำกัด
// ถอนมากสุด 500,000/เริ่มต้นจำกัด20,000รายการต่อวันครับ

// รายการถอนจะเสียค่าธรรมเนียม 10 บาทต่อรายการค่ะ
const (
	SUGARPAY_DEFMIN_DEPOSIT_AMOUNT  = 50
	SUGARPAY_DEFMAX_DEPOSIT_AMOUNT  = 100000
	SUGARPAY_DEFMIN_WITHDRAW_AMOUNT = 100
	SUGARPAY_DEFMAX_WITHDRAW_AMOUNT = 100000
)

type SugarpayWebhookDepositResponse struct {
	Sign               string `json:"sign"`
	Timestamp          int64  `json:"timestamp"`
	OrderNo            string `json:"order_no"`
	MchOrderNo         string `json:"mch_order_no"`
	Status             int    `json:"status"`
	Amount             string `json:"amount"`
	PayTime            string `json:"pay_time"`
	RepayAccountBank   string `json:"repay_account_bank"`
	RepayAccountNumber string `json:"repay_account_number"`
	AccountNumber      string `json:"account_number"`
	AccountBank        string `json:"account_bank"`
}

type SugarpayErrorRemoteResponse struct {
	StatusCode int    `json:"statusCode"`
	Message    string `json:"message"`
}
type SugarpayError2RemoteResponse struct {
	Message    string `json:"message"`
	Error      string `json:"error"`
	StatusCode int    `json:"statusCode"`
}

type SugarpayCustomerCreateRemoteRequest struct {
	Name      string `json:"name" validate:"required"`
	BankCode  string `json:"bankCode" validate:"required"`
	AccountNo string `json:"accountNo" validate:"required"`
}
type SugarpayCustomerCreateRemoteResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Code    string `json:"code"`
	Data    struct {
		Partner      string   `json:"partner"`
		CustomerUuid string   `json:"customerUuid"`
		ClientCode   string   `json:"clientCode"`
		Name         string   `json:"name"`
		SearchName   []string `json:"searchName"`
		AccountNo    string   `json:"accountNo"`
		BankCode     string   `json:"bankCode"`
		Status       string   `json:"status"`
		CreatedAt    int64    `json:"createdAt"`
		UpdatedAt    int64    `json:"updatedAt"`
	} `json:"data"`
}
type SugarpayCustomerUpdateRemoteRequest struct {
	CustomerUuid string `json:"customerUuid" validate:"required"`
	Name         string `json:"name" validate:"required"`
	BankCode     string `json:"bankCode" validate:"required"`
	AccountNo    string `json:"accountNo" validate:"required"`
}
type SugarpayCustomerUpdateRemoteResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Code    string `json:"code"`
	Data    struct {
		Partner      string   `json:"partner"`
		CustomerUuid string   `json:"customerUuid"`
		ClientCode   string   `json:"clientCode"`
		Name         string   `json:"name"`
		SearchName   []string `json:"searchName"`
		AccountNo    string   `json:"accountNo"`
		BankCode     string   `json:"bankCode"`
		Status       string   `json:"status"`
		CreatedAt    int64    `json:"createdAt"`
		UpdatedAt    int64    `json:"updatedAt"`
	} `json:"data"`
}

type SugarpayDepositCreateRemoteRequest struct {
	OrderId      string  `json:"order_id"`
	Amount       float64 `json:"amount"`
	RefName      string  `json:"ref_name"`
	RefAccountNo string  `json:"ref_account_no"`
	RefBankCode  string  `json:"ref_bank_code"`
	UserId       string  `json:"user_id"`
	CallbackUrl  string  `json:"callback_url"`
	Signature    string  `json:"signature"`
}
type SugarpayDepositCreateRemoteResponse struct {
	Error   string `json:"error"`
	Code    string `json:"code"`
	Message string `json:"message"`
	Result  struct {
		RefId   string `json:"ref_id"`
		OrderId string `json:"order_id"`
		Amount  string `json:"amount"`
		Image   string `json:"image"`
		Timeout struct {
			Days string `json:"days"`
			Time string `json:"time"`
			Date string `json:"date"`
		} `json:"timeout"`
	} `json:"result"`
}

type SugarpayCheckBalanceRemoteResponse struct {
	Error   string `json:"error"`
	Code    string `json:"code"`
	Message string `json:"message"`
	Result  struct {
		Balance         string `json:"balance"`
		BalanceWithdraw string `json:"balance_withdraw"`
		Date            string `json:"date"`
	} `json:"result"`
}
type SugarpayCheckBalanceResponse struct {
	Balance         float64 `json:"balance"`
	BalanceWithdraw float64 `json:"balance_withdraw"`
	Date            string  `json:"date"`
}

type SugarpayGetOrderRemoteResponse struct {
	Success bool `json:"success"`
	Code    int  `json:"code"`
	Data    struct {
		OrderNo       string  `json:"orderNo"`
		ReceiveAddr   string  `json:"receiveAddr"`
		ChainName     string  `json:"chainName"`
		CoinUnit      string  `json:"coinUnit"`
		RequestAmount float64 `json:"requestAmount"`
		PayAmount     float64 `json:"payAmount"`
		Status        string  `json:"status"`
		Sign          string  `json:"sign"`
	} `json:"data"`
}
type SugarpayWithdrawCreateRemoteRequest struct {
	OrderId     string  `json:"order_id"`
	Amount      float64 `json:"amount"`
	ToAccountNo string  `json:"to_account_no"`
	ToBankCode  string  `json:"to_bank_code"`
	ToName      string  `json:"to_name"`
	CallbackUrl string  `json:"callback_url"`
	Signature   string  `json:"signature"`
}
type SugarpayWithdrawCreateRemoteResponse struct {
	Error   string `json:"error"`
	Code    string `json:"code"`
	Message string `json:"message"`
	Result  struct {
		RefId   string `json:"ref_id"`
		OrderId string `json:"order_id"`
		Price   string `json:"price"`
	} `json:"result"`
}

type SugarpayWebhookRequest struct {
	JsonPayload string `json:"jsonPayload"`
}
type SugarpayWebhookCreateBody struct {
	Id          int64  `json:"id"`
	Name        string `json:"name"`
	JsonPayload string `json:"jsonPayload"`
}

type SugarpayWebhookReturnResponse struct {
	Status string `json:"status"`
}

type SugarpayDepositWebhookResponse struct {
	Type            string `json:"type"`
	Status          string `json:"status"`
	StatusCode      string `json:"status_code"`
	AgentConfirm    string `json:"agent_confirm"`
	StmRefId        string `json:"stm_ref_id"`
	StmDate         string `json:"stm_date"`
	StmAmount       string `json:"stm_amount"`
	StmBankName     string `json:"stm_bank_name"`
	StmAccountNo    string `json:"stm_account_no"`
	StmRemark       string `json:"stm_remark"`
	TxnRefId        string `json:"txn_ref_id"`
	TxnOrderId      string `json:"txn_order_id"`
	TxnUserId       string `json:"txn_user_id"`
	DepositBalance  string `json:"deposit_balance"`
	WithdrawBalance string `json:"withdraw_balance"`
	Remark          string `json:"remark"`
	Signature       string `json:"signature"`
}

type SugarpayWithdrawWebhookResponse struct {
	Type            string `json:"type"`
	Status          string `json:"status"`
	StatusCode      string `json:"status_code"`
	AgentConfirm    string `json:"agent_confirm"`
	StmRefId        string `json:"stm_ref_id"`
	StmDate         string `json:"stm_date"`
	StmAmount       string `json:"stm_amount"`
	StmBankName     string `json:"stm_bank_name"`
	StmBankCode     string `json:"stm_bank_code"`
	StmLast4Account string `json:"stm_last_4account"`
	StmRemark       string `json:"stm_remark"`
	TxnRefId        string `json:"txn_ref_id"`
	TxnOrderId      string `json:"txn_order_id"`
	TxnUserId       string `json:"txn_user_id"`
	Timestamp       string `json:"timestamp"`
	AccountNo       string `json:"account_no"`
	AccountBankName string `json:"account_bank_name"`
	DepositBalance  string `json:"deposit_balance"`
	WithdrawBalance string `json:"withdraw_balance"`
	Remark          string `json:"remark"`
	Signature       string `json:"signature"`
}

type SugarpayCustomerDepositInfo struct {
	Name        string `json:"name"`
	DisplayName string `json:"displayName"`
	ShopName    string `json:"shopName"`
	MinAmount   int64  `json:"minAmount"`
	MaxAmount   int64  `json:"maxAmount"`
}
type SugarpayDepositCreateRequest struct {
	UserId int64   `form:"userId" json:"-" validate:"required"`
	Amount float64 `form:"amount" json:"amount" binding:"required,min=1" validate:"required,min=1"`
}
type SugarpayWithdrawCreateRequest struct {
	RefId  int64   `form:"refId" validate:"required"`
	UserId int64   `form:"userId" json:"-" validate:"required"`
	Amount float64 `form:"amount" binding:"required,min=1" validate:"required,min=1"`
}

const (
	SUGARPAY_ORDER_TYPE_DEPOSIT  = 1
	SUGARPAY_ORDER_TYPE_WITHDRAW = 2
)

type SugarpayCustomer struct {
	Id           int64  `json:"id"`
	UserId       int64  `json:"userId"`
	CustomerUuid string `json:"customerUuid"`
	FullName     string `json:"fullName"`
	BankCode     string `json:"bankCode"`
	AccountNo    string `json:"accountNo"`
	AccountName  string `json:"accountName"`
}
type SugarpayCustomerListRequest struct {
	Page    int    `form:"page" default:"1" min:"1"`
	Limit   int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol string `form:"sortCol"`
	SortAsc string `form:"sortAsc"`
}
type SugarpayCustomerResponse struct {
	Id           int64  `json:"id"`
	UserId       int64  `json:"userId"`
	CustomerUuid string `json:"customerUuid"`
	FullName     string `json:"fullName"`
	BankCode     string `json:"bankCode"`
	AccountNo    string `json:"accountNo"`
	AccountName  string `json:"accountName"`
}
type SugarpayCustomerCreateBody struct {
	Id           int64  `json:"id"`
	UserId       int64  `json:"userId"`
	CustomerUuid string `json:"customerUuid"`
	FullName     string `json:"fullName"`
	BankCode     string `json:"bankCode"`
	AccountNo    string `json:"accountNo"`
	AccountName  string `json:"accountName"`
}
type SugarpayCustomerUpdateBody struct {
	CustomerUuid *string `json:"customerUuid"`
	FullName     *string `json:"fullName"`
	BankCode     *string `json:"bankCode"`
	AccountNo    *string `json:"accountNo"`
	AccountName  *string `json:"accountName"`
}

type SugarpayOrder struct {
	Id                int64      `json:"id"`
	UserId            int64      `json:"userId"`
	OrderTypeId       int64      `json:"orderTypeId"`
	RefId             *int64     `json:"refId"`
	OrderNo           string     `json:"orderNo"`
	Amount            float64    `json:"amount"`
	TransactionNo     *string    `json:"transactionNo"`
	TransactionDate   *time.Time `json:"transactionDate"`
	TransactionStatus *string    `json:"transactionStatus"`
	PaymentAt         *time.Time `json:"paymentAt"`
	QrPromptpay       *string    `json:"qrPromptpay"`
	PaymentPageUrl    string     `json:"paymentPageUrl"`
	Remark            *string    `json:"remark"`
	ApiRemark         *string    `json:"apiRemark"`
	CreatedAt         time.Time  `json:"createdAt"`
	UpdatedAt         *time.Time `json:"updatedAt"`
}
type SugarpayOrderListRequest struct {
	UserId        *int64 `form:"userId"`
	OrderTypeId   *int64 `form:"orderTypeId"`
	OrderNo       string `form:"orderNo"`
	TransactionNo string `form:"transactionNo"`
	Amount        string `form:"amount"`
	Status        string `form:"status"`
	Page          int    `form:"page" default:"1" min:"1"`
	Limit         int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol       string `form:"sortCol"`
	SortAsc       string `form:"sortAsc"`
}
type SugarpayOrderResponse struct {
	Id                    int64      `json:"id"`
	UserId                int64      `json:"userId"`
	OrderTypeId           int64      `json:"orderTypeId"`
	OrderTypeName         string     `json:"orderTypeName"`
	RefId                 *int64     `json:"refId"`
	OrderNo               string     `json:"orderNo"`
	Amount                float64    `json:"amount"`
	TransferAmount        float64    `json:"transferAmount"`
	TransactionNo         *string    `json:"transactionNo"`
	TransactionDate       *time.Time `json:"transactionDate"`
	TransactionStatus     *string    `json:"transactionStatus"`
	PaymentAt             *time.Time `json:"paymentAt"`
	BankTransactionId     *string    `json:"bankTransactionId"`
	BankTransactionStatus string     `json:"bankTransactionStatus"`
	QrPromptpay           string     `json:"qrPromptpay"`
	PaymentPageUrl        string     `json:"paymentPageUrl"`
	Remark                *string    `json:"remark"`
	ApiRemark             *string    `json:"apiRemark"`
	CreatedAt             time.Time  `json:"createdAt"`
	UpdatedAt             *time.Time `json:"updatedAt"`
}
type SugarpayOrderWebResponse struct {
	UserId            int64     `json:"userId"`
	OrderNo           string    `json:"orderNo"`
	Amount            float64   `json:"amount"`
	TransferAmount    float64   `json:"transferAmount"`
	TransactionStatus string    `json:"transactionStatus"`
	QrCode            string    `json:"qrCode"`
	QrBase64          string    `json:"qrBase64"`
	QrUrl             string    `json:"qrUrl"`
	PaymentPageUrl    string    `json:"paymentPageUrl"`
	CreatedAt         time.Time `json:"createdAt"`
}
type SugarpayOrderQrResponse struct {
	Id       int64   `json:"id"`
	OrderNo  string  `json:"orderNo"`
	Amount   float64 `json:"amount"`
	QrBase64 string  `json:"qrBase64"`
}
type SugarpayOrderCreateRequest struct {
	UserId *int64  `form:"userId"`
	Amount float64 `form:"amount"`
}
type SugarpayOrderCreateBody struct {
	Id                int64   `json:"id"`
	UserId            int64   `json:"userId"`
	OrderTypeId       int64   `json:"orderTypeId"`
	RefId             *int64  `json:"refId"`
	OrderNo           string  `json:"orderNo"`
	Amount            float64 `json:"amount"`
	TransactionStatus string  `json:"transactionStatus"`
}
type SugarpayOrderUpdateBody struct {
	TransactionNo     string    `json:"transactionNo"`
	TransactionDate   time.Time `json:"transactionDate"`
	TransactionStatus string    `json:"transactionStatus"`
	TransferAmount    float64   `json:"transferAmount"`
	QrPromptpay       string    `json:"qrPromptpay"`
	PaymentPageUrl    string    `json:"paymentPageUrl"`
}
