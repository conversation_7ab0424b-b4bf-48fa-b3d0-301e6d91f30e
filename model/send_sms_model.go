package model

import "time"

// ACCEPTED = [COMPLETED, DELIVERED]
// UNDELIVERED = [UNDELIVERED, EXPIRED, REJECTED]
// PENDING = [PENDING]
// BLACKLIST = [BLACKLIST]

const (
	SEND_SMS_PENDING     = "PENDING"
	SEND_SMS_ACCEPTED    = "ACCEPTED"
	SEND_SMS_UNDELIVERED = "UNDELIVERED"
	SEND_SMS_BLACKLIST   = "BLACKLIST"
)

const (
	SMS_LOG_STATUS_PENDING     = "PENDING"
	SMS_LOG_STATUS_COMPLETED   = "COMPLETED"
	SMS_LOG_STATUS_REJECTED    = "REJECTED"
	SMS_LOG_STATUS_DELIVERED   = "DELIVERED"
	SMS_LOG_STATUS_UNDELIVERED = "UNDELIVERED"
	SMS_LOG_STATUS_EXPIRED     = "EXPIRED"
	SMS_LOG_STATUS_BLACKLIST   = "BLA<PERSON>KLIST"
)

type SendSms struct {
	Id              int64     `json:"id"`
	RefId           int64     `json:"refId"`
	UserId          int64     `json:"userId"`
	Phone           string    `json:"phone"`
	SenderNameId    int64     `json:"senderNameId"`
	Message         string    `json:"message"`
	CreditSmsAmount float64   `json:"creditSmsAmount"`
	Status          string    `json:"status"`
	LogStatus       string    `json:"logStatus"`
	CreatedAt       time.Time `json:"createdAt"`
	CreatedByID     int64     `json:"createdById"`
	UpdatedAt       time.Time `json:"updatedAt"`
}

type CreateSendSmsRequest struct {
	UserID       []int64 `json:"userId"`
	Message      string  `json:"message"`
	SenderNameId int64   `json:"senderNameId"`
	CreatedById  int64   `json:"-"`
}
type CheckSendSmsMessageSpamRequest struct {
	Message string `json:"message"`
}

type GetSendSmsUserInactiveResponse struct {
	ID    int64  `json:"id"`
	Phone string `json:"phone"`
}

type CreateSendSmsBody struct {
	Id              int64   `json:"id"`
	RefId           int64   `json:"refId"`
	UserId          int64   `json:"userId"`
	Phone           string  `json:"phone"`
	SenderNameId    int64   `json:"senderNameId"`
	Message         string  `json:"message"`
	CreditSmsAmount float64 `json:"creditSmsAmount"`
	Status          string  `json:"status"`
	LogStatus       string  `json:"logStatus"`
	CreatedById     int64   `json:"createdById"`
}

type GetSendSmsSenderNameResponse struct {
	Id         int64  `json:"id"`
	SenderName string `json:"senderName"`
}

type SendCyberSmsRequest struct {
	Message    string   `json:"message"`
	Phones     []string `json:"phones"`
	SenderName string   `json:"senderName"`
}

type SendCyberSmsResponse struct {
	Message string             `json:"message"`
	Data    []SendCyberSmsData `json:"data"`
}

type SendCyberSmsData struct {
	ID     int64  `json:"id"`
	Phone  string `json:"phone"`
	Status string `json:"status"`
}

type UpdateSendSmsBody struct {
	RefId     int64  `json:"refId"`
	Status    string `json:"status"`
	LogStatus string `json:"logStatus"`
}

type GetSendSmsListRequest struct {
	Status       string `form:"status"`
	Phone        string `form:"phone"`
	SenderNameId *int64 `form:"senderNameId"`
	DateType     string `form:"dateType"`
	FromDate     string `form:"fromDate"`
	ToDate       string `form:"toDate"`
	Page         int    `form:"page" default:"1" min:"1"`
	Limit        int    `form:"limit" default:"10" min:"1" max:"100"`
}

type SendSmsListResponse struct {
	Id              int64     `json:"id"`
	RefId           int64     `json:"refId"`
	UserId          int64     `json:"userId"`
	UserFullname    string    `json:"userFullname"`
	Phone           string    `json:"phone"`
	SenderNameId    int64     `json:"senderNameId"`
	SenderName      string    `json:"senderName"`
	Message         string    `json:"message"`
	CreditSmsAmount float64   `json:"creditSmsAmount"`
	Status          string    `json:"status"`
	CreatedAt       time.Time `json:"createdAt"`
	CreatedByID     int64     `json:"createdById"`
	CreatedByName   string    `json:"createdByName"`
}

type BulkStatisSmsCyberRequest struct {
	SmsId []int64 `json:"smsId"`
}

type SendSmsCyberResponse struct {
	Message string             `json:"message"`
	Data    []SendSmsCyberData `json:"data"`
}

type SendSmsCyberData struct {
	ID     int64  `json:"id"`
	Phone  string `json:"phone"`
	Status string `json:"status"`
}

type CheckSpamWordRequest struct {
	Message string `json:"message"`
}

type SendCyberOtpRequest struct {
	OtpKey string `json:"otpKey"`
	Phone  string `json:"phone"`
}
type SendCyberOtpData struct {
	OtpId  string `json:"otpId"`
	OtpRef string `json:"otpRef"`
}
type SendCyberOtpResponse struct {
	Message string           `json:"message"`
	Data    SendCyberOtpData `json:"data"`
}

type VerifyCyberOtpRequest struct {
	OtpCode string `json:"otpCode"`
	OtpId   string `json:"otpId"`
}
type VerifyCyberOtpResponse struct {
	Message string `json:"message"`
}
