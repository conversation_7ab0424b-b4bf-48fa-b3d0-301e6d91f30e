package model

import (
	"time"
)

type JbpayDepositRequest struct {
	<PERSON><PERSON><PERSON>        string  `json:"partner<PERSON>ey"`
	RepayAppId        string  `json:"repayAppId"`
	LoanAppId         string  `json:"loanAppId"`
	MerchantId        string  `json:"merchantId"`
	Token             string  `json:"token"`
	AesKey            string  `json:"aesKey"`
	OrderNo           string  `json:"orderNo"`
	BankCode          string  `json:"bankCode"`
	Amount            float64 `json:"amount"`
	UserFullname      string  `json:"userFullname"`
	UserMobile        string  `json:"userMobile"`
	UserAccountNumber string  `json:"userAccountNumber"`
	UserAccountBank   string  `json:"userAccountBank"`
}
type JbpayEncryptPayload struct {
	Data struct {
		PartnerKey string `json:"partner_key"`
		EnData     string `json:"en_data"`
	} `json:"data"`
}

type JbpayWebhookEncryptPayload struct {
	Data struct {
		<PERSON><PERSON><PERSON> string `json:"partner_key"`
		EnData     string `json:"en_data"`
		MchOrderNo string `json:"mch_order_no"`
	} `json:"data"`
}

type JbpayWebhookDepositResponse struct {
	Sign               string `json:"sign"`
	Timestamp          int64  `json:"timestamp"`
	OrderNo            string `json:"order_no"`
	MchOrderNo         string `json:"mch_order_no"`
	Status             int    `json:"status"`
	Amount             string `json:"amount"`
	PayTime            string `json:"pay_time"`
	RepayAccountBank   string `json:"repay_account_bank"`
	RepayAccountNumber string `json:"repay_account_number"`
	AccountNumber      string `json:"account_number"`
	AccountBank        string `json:"account_bank"`
}

type JbpayErrorRemoteResponse struct {
	Message string `json:"message"`
	Code    int64  `json:"code"`
	Data    string `json:"data"`
}
type JbpayErrorStringRemoteResponse struct {
	Message string `json:"message"`
	Code    string `json:"code"`
	Data    string `json:"data"`
}

type JbpayToken struct {
	Id          int64      `json:"id"`
	AccessToken string     `json:"accessToken"`
	ExpireAt    time.Time  `json:"expireAt"`
	CreateBy    int64      `json:"createBy"`
	CreatedAt   time.Time  `json:"createdAt"`
	UpdatedAt   *time.Time `json:"updatedAt"`
}
type JbpayTokenCreateBody struct {
	Id          int64     `json:"id"`
	AccessToken string    `json:"accessToken"`
	ExpireAt    time.Time `json:"expireAt"`
	CreateBy    int64     `json:"createBy"`
}
type JbpayTokenCreateRemoteRequest struct {
	AccessKey string `json:"accessKey" validate:"required"`
	SecretKey string `json:"secretKey" validate:"required"`
}
type JbpayTokenCreateRemoteResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Code    string `json:"code"`
	Data    struct {
		Token string `json:"token"`
	} `json:"data"`
}

type JbpayCustomerCreateRemoteRequest struct {
	Name      string `json:"name" validate:"required"`
	BankCode  string `json:"bankCode" validate:"required"`
	AccountNo string `json:"accountNo" validate:"required"`
}
type JbpayCustomerCreateRemoteResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Code    string `json:"code"`
	Data    struct {
		Partner      string   `json:"partner"`
		CustomerUuid string   `json:"customerUuid"`
		ClientCode   string   `json:"clientCode"`
		Name         string   `json:"name"`
		SearchName   []string `json:"searchName"`
		AccountNo    string   `json:"accountNo"`
		BankCode     string   `json:"bankCode"`
		Status       string   `json:"status"`
		CreatedAt    int64    `json:"createdAt"`
		UpdatedAt    int64    `json:"updatedAt"`
	} `json:"data"`
}
type JbpayCustomerUpdateRemoteRequest struct {
	CustomerUuid string `json:"customerUuid" validate:"required"`
	Name         string `json:"name" validate:"required"`
	BankCode     string `json:"bankCode" validate:"required"`
	AccountNo    string `json:"accountNo" validate:"required"`
}
type JbpayCustomerUpdateRemoteResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Code    string `json:"code"`
	Data    struct {
		Partner      string   `json:"partner"`
		CustomerUuid string   `json:"customerUuid"`
		ClientCode   string   `json:"clientCode"`
		Name         string   `json:"name"`
		SearchName   []string `json:"searchName"`
		AccountNo    string   `json:"accountNo"`
		BankCode     string   `json:"bankCode"`
		Status       string   `json:"status"`
		CreatedAt    int64    `json:"createdAt"`
		UpdatedAt    int64    `json:"updatedAt"`
	} `json:"data"`
}

type JbpayDepositCreateRemoteRequest struct {
	OrderNo           string  `json:"orderNo"`
	BankCode          string  `json:"bankCode"`
	Amount            float64 `json:"amount"`
	UserFullname      string  `json:"userFullname"`
	UserMobile        string  `json:"userMobile"`
	UserAccountNumber string  `json:"userAccountNumber"`
	UserAccountBank   string  `json:"userAccountBank"`
}
type JbpayDepositCreateRemoteResponseData struct {
	OrderNo            string  `json:"order_no"`
	MchOrderNo         string  `json:"mch_order_no"`
	ExpirationDate     string  `json:"expiration_date"`
	Status             int     `json:"status"`
	Amount             float64 `json:"amount"`
	OriginCode         string  `json:"origin_code"`
	Redirect           string  `json:"redirect"`
	BankCardName       string  `json:"bank_card_name"`
	BankDiscountAmount float64 `json:"bank_discount_amount"`
	SubBankName        string  `json:"sub_bank_name"`
	BankName           string  `json:"bank_name"`
	AccountNumber      string  `json:"account_number"`
}
type JbpayDepositCreateRemoteResponse struct {
	Code    string                               `json:"code"`
	Message string                               `json:"message"`
	Data    JbpayDepositCreateRemoteResponseData `json:"data"`
}
type JbpayDepositCreateRemote2Response struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Data    struct {
		OrderNo            string  `json:"order_no"`
		MchOrderNo         string  `json:"mch_order_no"`
		ExpirationDate     string  `json:"expiration_date"`
		Status             int     `json:"status"`
		Amount             float64 `json:"amount,string"`
		OriginCode         string  `json:"origin_code"`
		Redirect           string  `json:"redirect"`
		BankCardName       string  `json:"bank_card_name"`
		BankDiscountAmount float64 `json:"bank_discount_amount,string"`
		SubBankName        string  `json:"sub_bank_name"`
		BankName           string  `json:"bank_name"`
		AccountNumber      string  `json:"account_number"`
	} `json:"data"`
}

type JbpayCheckBalanceRemoteResponse struct {
	Success bool `json:"success"`
	Code    int  `json:"code"`
	Data    struct {
		InBalance struct {
			CoinChain string  `json:"coinChain"`
			CoinUnit  string  `json:"coinUnit"`
			Balance   float64 `json:"balance"`
			Freeze    float64 `json:"freeze"`
		} `json:"inBalance"`
		OutBalance struct {
			CoinChain string  `json:"coinChain"`
			CoinUnit  string  `json:"coinUnit"`
			Balance   float64 `json:"balance"`
			Freeze    float64 `json:"freeze"`
		} `json:"outBalance"`
		Sign             string `json:"sign"`
		CheckRequestTime int64  `json:"checkRequestTime"`
		DfBalance        struct {
			CoinChain string  `json:"coinChain"`
			CoinUnit  string  `json:"coinUnit"`
			Balance   float64 `json:"balance"`
			Freeze    float64 `json:"freeze"`
		} `json:"dfBalance"`
		DsBalance struct {
			CoinChain string  `json:"coinChain"`
			CoinUnit  string  `json:"coinUnit"`
			Balance   float64 `json:"balance"`
			Freeze    float64 `json:"freeze"`
		} `json:"dsBalance"`
	} `json:"data"`
}

type JbpayGetOrderRemoteResponse struct {
	Success bool `json:"success"`
	Code    int  `json:"code"`
	Data    struct {
		OrderNo       string  `json:"orderNo"`
		ReceiveAddr   string  `json:"receiveAddr"`
		ChainName     string  `json:"chainName"`
		CoinUnit      string  `json:"coinUnit"`
		RequestAmount float64 `json:"requestAmount"`
		PayAmount     float64 `json:"payAmount"`
		Status        string  `json:"status"`
		Sign          string  `json:"sign"`
	} `json:"data"`
}

type JbpayWithdrawCreateRemoteRequest struct {
	OrderNo           string  `json:"orderNo"`
	Amount            float64 `json:"amount"`
	UserFullname      string  `json:"userFullname"`
	UserMobile        string  `json:"userMobile"`
	UserAccountNumber string  `json:"userAccountNumber"`
	UserAccountBank   string  `json:"userAccountBank"`
}

type JbpayWithdrawCreateRemoteResponse struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Data    struct {
		OrderNo    string `json:"order_no"`
		MchOrderNo string `json:"mch_order_no"`
		Status     int    `json:"status"`
		PayMsg     string `json:"pay_msg"`
	} `json:"data"`
}

type JbpayWebhookRequest struct {
	JsonPayload string `json:"jsonPayload"`
}
type JbpayWebhookCreateBody struct {
	Id          int64  `json:"id"`
	Name        string `json:"name"`
	JsonPayload string `json:"jsonPayload"`
}

type JbpayDepositWebhookResponse struct {
	Uuid         string  `json:"uuid"`
	CustomerUuid string  `json:"customerUuid"`
	AccountName  string  `json:"accountName"`
	BankCode     string  `json:"bankCode"`
	AccountNo    string  `json:"accountNo"`
	Amount       float64 `json:"amount"`
	Fee          float64 `json:"fee"`
	// SettleAmount float64 `json:"settleAmount"`
	Type        string `json:"type"`
	Status      string `json:"status"`
	ReferenceId string `json:"referenceId"`
}

type JbpayWithDrawWebhookResponse struct {
	Type                    string    `json:"type"`
	CurrencyCode            string    `json:"currencyCode"`
	FundOutStatus           string    `json:"fundOutStatus"`
	Amount                  float64   `json:"amount"`
	ServiceFee              float64   `json:"serviceFee"`
	IsPaid                  bool      `json:"isPaid"`
	TransactionRemark       string    `json:"transactionRemark"`
	FundOutDescription      string    `json:"fundOutDescription"`
	TransactionRef1         string    `json:"transactionRef1"`
	FundOutPaymentReference string    `json:"fundOutPaymentReference"`
	BankName                string    `json:"bankName"`
	BankCode                string    `json:"bankCode"`
	AccountNumber           string    `json:"accountNumber"`
	CreatedAt               time.Time `json:"createdAt"`
	UpdatedAt               time.Time `json:"updatedAt"`
	BankTransactionRef      string    `json:"bankTransactionRef"`
}

type JbpayCustomerDepositInfo struct {
	Name        string `json:"name"`
	DisplayName string `json:"displayName"`
	ShopName    string `json:"shopName"`
	MinAmount   int64  `json:"minAmount"`
	MaxAmount   int64  `json:"maxAmount"`
}
type JbpayDepositCreateRequest struct {
	UserId int64   `form:"userId" json:"-" validate:"required"`
	Amount float64 `form:"amount" json:"amount" binding:"required,min=1" validate:"required,min=1"`
}
type JbpayWithdrawCreateRequest struct {
	RefId  int64   `form:"refId" validate:"required"`
	UserId int64   `form:"userId" json:"-" validate:"required"`
	Amount float64 `form:"amount" binding:"required,min=1" validate:"required,min=1"`
}

const (
	JBPAY_ORDER_TYPE_DEPOSIT  = 1
	JBPAY_ORDER_TYPE_WITHDRAW = 2
)

type JbpayCustomer struct {
	Id           int64  `json:"id"`
	UserId       int64  `json:"userId"`
	CustomerUuid string `json:"customerUuid"`
	FullName     string `json:"fullName"`
	BankCode     string `json:"bankCode"`
	AccountNo    string `json:"accountNo"`
	AccountName  string `json:"accountName"`
}
type JbpayCustomerListRequest struct {
	Page    int    `form:"page" default:"1" min:"1"`
	Limit   int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol string `form:"sortCol"`
	SortAsc string `form:"sortAsc"`
}
type JbpayCustomerResponse struct {
	Id           int64  `json:"id"`
	UserId       int64  `json:"userId"`
	CustomerUuid string `json:"customerUuid"`
	FullName     string `json:"fullName"`
	BankCode     string `json:"bankCode"`
	AccountNo    string `json:"accountNo"`
	AccountName  string `json:"accountName"`
}
type JbpayCustomerCreateBody struct {
	Id           int64  `json:"id"`
	UserId       int64  `json:"userId"`
	CustomerUuid string `json:"customerUuid"`
	FullName     string `json:"fullName"`
	BankCode     string `json:"bankCode"`
	AccountNo    string `json:"accountNo"`
	AccountName  string `json:"accountName"`
}
type JbpayCustomerUpdateBody struct {
	CustomerUuid *string `json:"customerUuid"`
	FullName     *string `json:"fullName"`
	BankCode     *string `json:"bankCode"`
	AccountNo    *string `json:"accountNo"`
	AccountName  *string `json:"accountName"`
}

type JbpayOrder struct {
	Id                int64      `json:"id"`
	UserId            int64      `json:"userId"`
	OrderTypeId       int64      `json:"orderTypeId"`
	RefId             *int64     `json:"refId"`
	OrderNo           string     `json:"orderNo"`
	Amount            float64    `json:"amount"`
	TransactionNo     *string    `json:"transactionNo"`
	TransactionDate   *time.Time `json:"transactionDate"`
	TransactionStatus *string    `json:"transactionStatus"`
	PaymentAt         *time.Time `json:"paymentAt"`
	QrPromptpay       *string    `json:"qrPromptpay"`
	PaymentPageUrl    string     `json:"paymentPageUrl"`
	Remark            *string    `json:"remark"`
	ApiRemark         *string    `json:"apiRemark"`
	CreatedAt         time.Time  `json:"createdAt"`
	UpdatedAt         *time.Time `json:"updatedAt"`
}
type JbpayOrderListRequest struct {
	UserId        *int64 `form:"userId"`
	OrderTypeId   *int64 `form:"orderTypeId"`
	OrderNo       string `form:"orderNo"`
	TransactionNo string `form:"transactionNo"`
	Amount        string `form:"amount"`
	Status        string `form:"status"`
	Page          int    `form:"page" default:"1" min:"1"`
	Limit         int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol       string `form:"sortCol"`
	SortAsc       string `form:"sortAsc"`
}
type JbpayOrderResponse struct {
	Id                    int64      `json:"id"`
	UserId                int64      `json:"userId"`
	OrderTypeId           int64      `json:"orderTypeId"`
	OrderTypeName         string     `json:"orderTypeName"`
	RefId                 *int64     `json:"refId"`
	OrderNo               string     `json:"orderNo"`
	Amount                float64    `json:"amount"`
	TransferAmount        float64    `json:"transferAmount"`
	TransactionNo         *string    `json:"transactionNo"`
	TransactionDate       *time.Time `json:"transactionDate"`
	TransactionStatus     *string    `json:"transactionStatus"`
	PaymentAt             *time.Time `json:"paymentAt"`
	BankTransactionId     *string    `json:"bankTransactionId"`
	BankTransactionStatus string     `json:"bankTransactionStatus"`
	QrPromptpay           string     `json:"qrPromptpay"`
	PaymentPageUrl        string     `json:"paymentPageUrl"`
	Remark                *string    `json:"remark"`
	ApiRemark             *string    `json:"apiRemark"`
	CreatedAt             time.Time  `json:"createdAt"`
	UpdatedAt             *time.Time `json:"updatedAt"`
}
type JbpayOrderWebResponse struct {
	UserId            int64     `json:"userId"`
	OrderNo           string    `json:"orderNo"`
	Amount            float64   `json:"amount"`
	TransferAmount    float64   `json:"transferAmount"`
	TransactionStatus string    `json:"transactionStatus"`
	QrCode            string    `json:"qrCode"`
	QrBase64          string    `json:"qrBase64"`
	PaymentPageUrl    string    `json:"paymentPageUrl"`
	CreatedAt         time.Time `json:"createdAt"`
}
type JbpayOrderQrResponse struct {
	Id       int64   `json:"id"`
	OrderNo  string  `json:"orderNo"`
	Amount   float64 `json:"amount"`
	QrBase64 string  `json:"qrBase64"`
}
type JbpayOrderCreateRequest struct {
	UserId *int64  `form:"userId"`
	Amount float64 `form:"amount"`
}
type JbpayOrderCreateBody struct {
	Id                int64   `json:"id"`
	UserId            int64   `json:"userId"`
	OrderTypeId       int64   `json:"orderTypeId"`
	RefId             *int64  `json:"refId"`
	OrderNo           string  `json:"orderNo"`
	Amount            float64 `json:"amount"`
	TransactionStatus string  `json:"transactionStatus"`
}
type JbpayOrderUpdateBody struct {
	TransactionNo     string    `json:"transactionNo"`
	TransactionDate   time.Time `json:"transactionDate"`
	TransactionStatus string    `json:"transactionStatus"`
	TransferAmount    float64   `json:"transferAmount"`
	QrPromptpay       string    `json:"qrPromptpay"`
	PaymentPageUrl    string    `json:"paymentPageUrl"`
}
