package model

type AllianceMemberRegister struct {
	OtpId        string `json:"otpId"`
	Code         string `json:"code"`
	Phone        string `json:"phone" binding:"required,min=10,max=12" example:"**********"`
	Password     string `json:"password" binding:"required,min=8,max=255,containsany=**********"`
	Fullname     string `json:"fullname" binding:"required,max=255"`
	BankAccount  string `json:"bankAccount" binding:"required,max=18"`
	BankId       int64  `json:"bankId" binding:"required"`
	LineId       string `json:"lineId"`
	ChannelId    *int64 `json:"channelId"`
	IpRegistered string `json:"-"`
	RefBy        *int64 `json:"refBy"`
}
