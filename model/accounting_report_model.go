package model

import "time"

type FastBankAccountResponse struct {
	Id                      int64      `json:"id"`
	BankId                  int64      `json:"bankId"`
	BankCode                string     `json:"bankCode"`
	BankName                string     `json:"bankName"`
	BankIconUrl             string     `json:"bankIconUrl"`
	AccountTypeId           int64      `json:"accountTypeId"`
	AccountTypeName         string     `json:"accountTypeName"`
	AccountName             string     `json:"accountName"`
	AccountNumber           string     `json:"accountNumber"`
	AccountBalance          float64    `json:"accountBalance"`
	AccountPriorityWithdraw int64      `json:"accountPriorityWithdraw"`
	AccountStatusName       string     `json:"accountStatusName"`
	DeviceUid               string     `json:"deviceUid"`
	PinCode                 string     `json:"pinCode"`
	ConnectionStatusId      int        `json:"connectionStatusId"`
	ConnectionStatusName    string     `json:"connectionStatusName"`
	LastConnUpdateAt        *time.Time `json:"lastConnUpdateAt"`
	SmsMode                 bool       `json:"smsMode"`
	CreatedAt               time.Time  `json:"createdAt"`
	UpdatedAt               *time.Time `json:"updatedAt"`
}
type ReportSummaryRequest struct {
	DateType string `form:"dateType" default:"daily"`
	DateFrom string `form:"dateFrom" default:"2006-01-02"`
	DateTo   string `form:"dateTo" default:"2006-01-02"`
	Remark   string `form:"remark" comment:"ใส่ให้มันต่าง กัน warning เฉยๆ"`
}
type ReportSummaryResponse struct {
	DateType                   string  `json:"dateType"`
	DateFrom                   string  `json:"dateFrom"`
	DateTo                     string  `json:"dateTo"`
	TotalNewUserCount          int64   `json:"totalNewUserCount"`
	TotalUserCount             int64   `json:"totalUserCount"`
	TotalActiveUserCount       int64   `json:"totalActiveUserCount"`
	TotalFirstDepositPrice     float64 `json:"totalFirstDepositPrice"`
	TotalFirstDepositUserCount int64   `json:"totalFirstDepositUserCount"`
	TotalDepositUserCount      int64   `json:"totalDepositUserCount"`
	TotalWithdrawUserCount     int64   `json:"totalWithdrawUserCount"`
	TotalBonusPrice            float64 `json:"totalBonusPrice"`
	TotalBonusCount            int64   `json:"totalBonusCount"`
	TotalProfitPrice           float64 `json:"totalProfitPrice"`
	TotalDepositPrice          float64 `json:"totalDepositPrice"`
	TotalWithdrawPrice         float64 `json:"totalWithdrawPrice"`
	TotalAffiliatePrice        float64 `json:"totalAffiliatePrice"`
	TotalAlliancePrice         float64 `json:"totalAlliancePrice"`
	TotalReturnLossTakenPrice  float64 `json:"totalReturnLossTakenPrice"`
	TotalReturnTurnTakenPrice  float64 `json:"totalReturnTurnTakenPrice"`
	TotalCreditBackPrice       float64 `json:"totalCreditBackPrice"`
	TotalCreditBackCount       int64   `json:"totalCreditBackCount"`
	TotalCancelCreditBack      float64 `json:"totalCancelCreditBack"`
	TotalTurn                  float64 `json:"totalTurn"`
	TotalWinlose               float64 `json:"totalWinlose"`
	TotalBankProfit            float64 `json:"totalBankProfit"`
	TotalPromotionWebCredit    float64 `json:"totalPromotionWebCredit"`  // ยอดโบนัสโปรโมชั่น
	TotalPromotionReturnLoss   float64 `json:"totalPromotionReturnLoss"` // ยอดโบนัสกิจกรรมคืนยอดเสีย
	TotalActivityLuckyWheel    float64 `json:"totalActivityLuckyWheel"`  // ยอดโบนัสกิจกรรมกงล้อ
	TotalCheckInBonus          float64 `json:"totalCheckInBonus"`        // ยอดโบนัสกิจกรรมเช็คอิน
	TotalPromotionCashCoupon   float64 `json:"totalPromotionCashCoupon"` // ยอดโบนัสกิจกรรมคูปองเงินสด
	TotalPromotionReturnTurn   float64 `json:"totalPromotionReturnTurn"` // ยอดโบนัสกิจกรรมคืนยอดเทิร์น
	TotalAdminCreateBonus      float64 `json:"totalAdminCreateBonus"`    // ยอดโบนัสกิจกรรมคูปองเงินสด
}

// UNUSED at 2025-05-20
type ReportPromotionBonusResponse struct {
	DateType string `json:"dateType"`
	DateFrom string `json:"dateFrom"`
	DateTo   string `json:"dateTo"`
	// TotalAccountingBonusPrice float64 `json:"totalAccountingBonusPrice"`
	// TotalAffiliateBonusPrice  float64 `json:"totalAffiliateBonusPrice"`
	TotalReturnLossTakenPrice float64 `json:"totalReturnLossTakenPrice"`
	TotalReturnTurnTakenPrice float64 `json:"totalReturnTurnTakenPrice"`
	TotalActivityBonusPrice   float64 `json:"totalActivityBonusPrice"`
	TotalActivityBonusCount   int64   `json:"totalActivityBonusCount"`
}

type FastBankAccountAndDailySummaryResponse struct {
	Id                      int64      `json:"id"`
	BankId                  int64      `json:"bankId"`
	BankCode                string     `json:"bankCode"`
	BankName                string     `json:"bankName"`
	BankIconUrl             string     `json:"bankIconUrl"`
	AccountTypeId           int64      `json:"accountTypeId"`
	AccountTypeName         string     `json:"accountTypeName"`
	AccountName             string     `json:"accountName"`
	AccountNumber           string     `json:"accountNumber"`
	AccountBalance          float64    `json:"accountBalance"`
	AccountPriorityWithdraw int64      `json:"accountPriorityWithdraw"`
	AccountStatusName       string     `json:"accountStatusName"`
	DeviceUid               string     `json:"deviceUid"`
	PinCode                 string     `json:"pinCode"`
	ConnectionStatusId      int        `json:"connectionStatusId"`
	ConnectionStatusName    string     `json:"connectionStatusName"`
	LastConnUpdateAt        *time.Time `json:"lastConnUpdateAt"`
	CreatedAt               time.Time  `json:"createdAt"`
	UpdatedAt               *time.Time `json:"updatedAt"`
	TotalWithdraw           float64    `json:"totalWithdraw"`
	TotalDeposit            float64    `json:"totalDeposit"`
	VerifyLoginBot          bool       `json:"verifyLoginBot"`
	SmsMode                 bool       `json:"smsMode"`
}

type CreateReportSummary struct {
	CreatedDate string `json:"createdDate"`
	// TotalUserCount             int64   `json:"totalUserCount"`
	TotalNewUserCount          int64   `json:"totalNewUserCount"`
	TotalActiveUserCount       int64   `json:"totalActiveUserCount"`
	TotalFirstDepositPrice     float64 `json:"totalFirstDepositPrice"`
	TotalFirstDepositUserCount int64   `json:"totalFirstDepositUserCount"`
	TotalDepositUserCount      int64   `json:"totalDepositUserCount"`
	TotalWithdrawUserCount     int64   `json:"totalWithdrawUserCount"`
	TotalBonusPrice            float64 `json:"totalBonusPrice"`
	TotalBonusCount            int64   `json:"totalBonusCount"`
	TotalProfitPrice           float64 `json:"totalProfitPrice"`
	TotalDepositPrice          float64 `json:"totalDepositPrice"`
	TotalWithdrawPrice         float64 `json:"totalWithdrawPrice"`
	TotalAffiliatePrice        float64 `json:"totalAffiliatePrice"`
	TotalAlliancePrice         float64 `json:"totalAlliancePrice"`
	TotalReturnLossTakenPrice  float64 `json:"totalReturnLossTakenPrice"`
	TotalReturnTurnTakenPrice  float64 `json:"totalReturnTurnTakenPrice"`
	TotalCreditBackPrice       float64 `json:"totalCreditBackPrice"`
	TotalCreditBackCount       int64   `json:"totalCreditBackCount"`
	TotalTurn                  float64 `json:"totalTurn"`
	TotalWinlose               float64 `json:"totalWinlose"`
	TotalBankProfit            float64 `json:"totalBankProfit"`
	TotalPromotionWebCredit    float64 `json:"totalPromotionWebCredit"`  // ยอดโบนัสโปรโมชั่น
	TotalPromotionReturnLoss   float64 `json:"totalPromotionReturnLoss"` // ยอดโบนัสกิจกรรมคืนยอดเสีย
	TotalActivityLuckyWheel    float64 `json:"totalActivityLuckyWheel"`  // ยอดโบนัสกิจกรรมกงล้อ
	TotalCheckInBonus          float64 `json:"totalCheckInBonus"`        // ยอดโบนัสกิจกรรมเช็คอิน
	TotalPromotionCashCoupon   float64 `json:"totalPromotionCashCoupon"` // ยอดโบนัสกิจกรรมคูปองเงินสด
	TotalPromotionReturnTurn   float64 `json:"totalPromotionReturnTurn"` // ยอดโบนัสกิจกรรมคืนยอดเทิร์น
	TotalAdminCreateBonus      float64 `json:"totalAdminCreateBonus"`    // ยอดโบนัสกิจกรรมคูปองเงินสด
}

type GetUserReportDailyNewUser struct {
	TotalNewUserCount int64     `json:"totalNewUserCount"`
	CreatedDate       time.Time `json:"createdDate"`
}

type GetUserReportDailyLoginCount struct {
	TotalActiveUserCount int64     `json:"totalActiveUserCount"`
	CreatedDate          time.Time `json:"createdDate"`
}

type GetUserReportDailyFirstDeposit struct {
	TotalFirstDepositUserCount int64     `json:"totalFirstDepositUserCount"`
	TotalFirstDepositPrice     float64   `json:"totalFirstDepositPrice"`
	CreatedDate                time.Time `json:"createdDate"`
}

type GetUserReportDailyDeposit struct {
	TotalDepositUserCount int64     `json:"totalDepositUserCount"`
	TotalDepositPrice     float64   `json:"totalDepositPrice"`
	CreatedDate           time.Time `json:"createdDate"`
}

type GetUserReportDailyWithdraw struct {
	TotalWithdrawUserCount int64     `json:"totalWithdrawUserCount"`
	TotalWithdrawPrice     float64   `json:"totalWithdrawPrice"`
	CreatedDate            time.Time `json:"createdDate"`
}
type GetUserReportDailyCancelCredit struct {
	TotalCancelCreditBack float64   `json:"totalCancelCreditBack"`
	CreatedDate           time.Time `json:"createdDate"`
}

type GetUserReportDailyWithdrawInfo struct {
	TotalReturnLossTakenPrice float64   `json:"totalReturnLossTakenPrice"`
	TotalAffiliatePrice       float64   `json:"totalAffiliatePrice"`
	TotalAlliancePrice        float64   `json:"totalAlliancePrice"`
	TotalReturnTurnTakenPrice float64   `json:"totalReturnTurnTakenPrice"`
	CreatedDate               time.Time `json:"createdDate"`
}

type GetUserReportDailyProfit struct {
	TotalCreditBackPrice float64   `json:"totalCreditBackPrice"`
	TotalCreditBackCount int64     `json:"totalCreditBackCount"`
	CreatedDate          time.Time `json:"createdDate"`
}

type GetUserReportDailyPlayLog struct {
	TotalTurn    float64   `json:"totalTurn"`
	TotalWinLose float64   `json:"totalWinLose"`
	CreatedDate  time.Time `json:"createdDate"`
}
type GetUserReportActivitySummaryReportDaily struct {
	TotalPromotionWebCredit  float64   `json:"totalPromotionWebCredit"`
	TotalPromotionCashCoupon float64   `json:"totalPromotionCashCoupon"`
	TotalAdminCreateBonus    float64   `json:"totalAdminCreateBonus"`
	TotalActivityLuckyWheel  float64   `json:"totalActivityLuckyWheel"`
	TotalPromotionReturnLoss float64   `json:"totalPromotionReturnLoss"`
	TotalCheckInBonus        float64   `json:"totalCheckInBonus"`
	TotalPromotionReturnTurn float64   `json:"totalPromotionReturnTurn"`
	TotalBonusCount          int64     `json:"totalBonusCount"`
	CreatedDate              time.Time `json:"createdDate"`
}

type UpdateReportSummary struct {
	CreatedDate                string    `json:"createdDate"`
	TotalNewUserCount          *int64    `json:"totalNewUserCount"`
	TotalActiveUserCount       *int64    `json:"totalActiveUserCount"`
	TotalFirstDepositPrice     *float64  `json:"totalFirstDepositPrice"`
	TotalFirstDepositUserCount *int64    `json:"totalFirstDepositUserCount"`
	TotalDepositUserCount      *int64    `json:"totalDepositUserCount"`
	TotalWithdrawUserCount     *int64    `json:"totalWithdrawUserCount"`
	TotalBonusPrice            *float64  `json:"totalBonusPrice"`
	TotalBonusCount            *int64    `json:"totalBonusCount"`
	TotalProfitPrice           *float64  `json:"totalProfitPrice"`
	TotalDepositPrice          *float64  `json:"totalDepositPrice"`
	TotalWithdrawPrice         *float64  `json:"totalWithdrawPrice"`
	TotalAffiliatePrice        *float64  `json:"totalAffiliatePrice"`
	TotalAlliancePrice         *float64  `json:"totalAlliancePrice"`
	TotalReturnLossTakenPrice  *float64  `json:"totalReturnLossTakenPrice"`
	TotalReturnTurnTakenPrice  *float64  `json:"totalReturnTurnTakenPrice"`
	TotalCreditBackPrice       *float64  `json:"totalCreditBackPrice"`
	TotalCreditBackCount       *int64    `json:"totalCreditBackCount"`
	TotalTurn                  *float64  `json:"totalTurn"`
	TotalWinlose               *float64  `json:"totalWinlose"`
	TotalBankProfit            *float64  `json:"totalBankProfit"`
	TotalPromotionWebCredit    *float64  `json:"totalPromotionWebCredit"`  // ยอดโบนัสโปรโมชั่น
	TotalPromotionReturnLoss   *float64  `json:"totalPromotionReturnLoss"` // ยอดโบนัสกิจกรรมคืนยอดเสีย
	TotalActivityLuckyWheel    *float64  `json:"totalActivityLuckyWheel"`  // ยอดโบนัสกิจกรรมกงล้อ
	TotalCheckInBonus          *float64  `json:"totalCheckInBonus"`        // ยอดโบนัสกิจกรรมเช็คอิน
	TotalPromotionCashCoupon   *float64  `json:"totalPromotionCashCoupon"` // ยอดโบนัสกิจกรรมคูปองเงินสด
	TotalPromotionReturnTurn   *float64  `json:"totalPromotionReturnTurn"` // ยอดโบนัสกิจกรรมคืนยอดเทิร์น
	TotalAdminCreateBonus      *float64  `json:"totalAdminCreateBonus"`    // ยอดโบนัสกิจกรรมคูปองเงินสด
	UpdatedAt                  time.Time `json:"updatedAt"`
}

type GetReportSummaryList struct {
	Id                         int64   `json:"id"`
	CreatedDate                string  `json:"createdDate"`
	TotalNewUserCount          int64   `json:"totalNewUserCount"`
	TotalActiveUserCount       int64   `json:"totalActiveUserCount"`
	TotalFirstDepositPrice     float64 `json:"totalFirstDepositPrice"`
	TotalFirstDepositUserCount int64   `json:"totalFirstDepositUserCount"`
	TotalDepositUserCount      int64   `json:"totalDepositUserCount"`
	TotalWithdrawUserCount     int64   `json:"totalWithdrawUserCount"`
	TotalBonus55Price          float64 `json:"totalBonusPrice"`
	TotalBonusCount            int64   `json:"totalBonusCount"`
	TotalProfitPrice           float64 `json:"totalProfitPrice"`
	TotalDepositPrice          float64 `json:"totalDepositPrice"`
	TotalWithdrawPrice         float64 `json:"totalWithdrawPrice"`
	TotalAffiliatePrice        float64 `json:"totalAffiliatePrice"`
	TotalAlliancePrice         float64 `json:"totalAlliancePrice"`
	TotalReturnLossTakenPrice  float64 `json:"totalReturnLossTakenPrice"`
	TotalReturnTurnTakenPrice  float64 `json:"totalReturnTurnTakenPrice"`
	TotalCredit55BackPrice     float64 `json:"totalCreditBackPrice"`
	TotalCreditBackCount       int64   `json:"totalCreditBackCount"`
	TotalTurn                  float64 `json:"totalTurn"`
	TotalWinlose               float64 `json:"totalWinlose"`
	TotalBankProfit            float64 `json:"totalBankProfit"`
	TotalPromotionWebCredit    float64 `json:"totalPromotionWebCredit"`  // ยอดโบนัสโปรโมชั่น
	TotalPromotionReturnLoss   float64 `json:"totalPromotionReturnLoss"` // ยอดโบนัสกิจกรรมคืนยอดเสีย
	TotalActivityLuckyWheel    float64 `json:"totalActivityLuckyWheel"`  // ยอดโบนัสกิจกรรมกงล้อ
	TotalCheckInBonus          float64 `json:"totalCheckInBonus"`        // ยอดโบนัสกิจกรรมเช็คอิน
	TotalPromotionCashCoupon   float64 `json:"totalPromotionCashCoupon"` // ยอดโบนัสกิจกรรมคูปองเงินสด
	TotalPromotionReturnTurn   float64 `json:"totalPromotionReturnTurn"` // ยอดโบนัสกิจกรรมคืนยอดเทิร์น
	TotalAdminCreateBonus      float64 `json:"totalAdminCreateBonus"`    // ยอดโบนัสกิจกรรมคูปองเงินสด
}
type GetReportSummaryListTotal struct {
	TotalNewUserCount          int64   `json:"totalNewUserCount"`
	TotalActiveUserCount       int64   `json:"totalActiveUserCount"`
	TotalFirstDepositPrice     float64 `json:"totalFirstDepositPrice"`
	TotalFirstDepositUserCount int64   `json:"totalFirstDepositUserCount"`
	TotalDepositUserCount      int64   `json:"totalDepositUserCount"`
	TotalWithdrawUserCount     int64   `json:"totalWithdrawUserCount"`
	TotalBonusPrice            float64 `json:"totalBonusPrice"`
	TotalBonusCount            int64   `json:"totalBonusCount"`
	TotalProfitPrice           float64 `json:"totalProfitPrice"`
	TotalDepositPrice          float64 `json:"totalDepositPrice"`
	TotalWithdrawPrice         float64 `json:"totalWithdrawPrice"`
	TotalAffiliatePrice        float64 `json:"totalAffiliatePrice"`
	TotalAlliancePrice         float64 `json:"totalAlliancePrice"`
	TotalReturnLossTakenPrice  float64 `json:"totalReturnLossTakenPrice"`
	TotalReturnTurnTakenPrice  float64 `json:"totalReturnTurnTakenPrice"`
	TotalCreditBackPrice       float64 `json:"totalCreditBackPrice"`
	TotalCreditBackCount       int64   `json:"totalCreditBackCount"`
	TotalTurn                  float64 `json:"totalTurn"`
	TotalWinlose               float64 `json:"totalWinlose"`
	TotalBankProfit            float64 `json:"totalBankProfit"`
	TotalPromotionWebCredit    float64 `json:"totalPromotionWebCredit"`  // ยอดโบนัสโปรโมชั่น
	TotalPromotionReturnLoss   float64 `json:"totalPromotionReturnLoss"` // ยอดโบนัสกิจกรรมคืนยอดเสีย
	TotalActivityLuckyWheel    float64 `json:"totalActivityLuckyWheel"`  // ยอดโบนัสกิจกรรมกงล้อ
	TotalCheckInBonus          float64 `json:"totalCheckInBonus"`        // ยอดโบนัสกิจกรรมเช็คอิน
	TotalPromotionCashCoupon   float64 `json:"totalPromotionCashCoupon"` // ยอดโบนัสกิจกรรมคูปองเงินสด
	TotalPromotionReturnTurn   float64 `json:"totalPromotionReturnTurn"` // ยอดโบนัสกิจกรรมคืนยอดเทิร์น
	TotalAdminCreateBonus      float64 `json:"totalAdminCreateBonus"`    // ยอดโบนัสกิจกรรมคูปองเงินสด
}

type TotalSumUserActiveCredit struct {
	TotalUserActiveCredit float64 `json:"totalUserActiveCredit"`
}

type CreateReportSummaryDashboardRerun struct {
	ActionKey   string `json:"-"`
	StartDate   string `json:"startDate"`
	EndDate     string `json:"endDate"`
	CreatedById int64  `json:"-"`
}
