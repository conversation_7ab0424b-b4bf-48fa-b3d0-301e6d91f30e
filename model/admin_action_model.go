package model

import (
	"time"
)

const (
	ADMIN_ACTION_MOVE_ACCOUNT_MONEY    = 4
	ADMIN_ACTION_LOGOUT                = 5
	ADMIN_ACTION_MANAGE                = 6
	ADMIN_ACTION_LOGIN                 = 7
	ADMIN_ACTION_MANAGE_APPLICATION    = 8
	ADMIN_ACTION_ACCOUNT_MANAGE        = 9
	ADMIN_ACTION_GROUP_MANAGE          = 10
	ADMIN_ACTION_MANAGE_USER           = 11
	ADMIN_ACTION_LOGIN_FAILED          = 12
	ADMIN_ACTION_COMMISION_MANAGE      = 13
	ADMIN_ACTION_MANAGE_TIDTURN        = 14
	ADMIN_ACTION_EDIT_SECTION          = 15
	ADMIN_ACTION_CATEGORY_GAME_SETTING = 16
	ADMIN_ACTION_GAME_PRIORITY_SETTING = 17
	ADMIN_ACTION_MARKETING_CONFIG      = 18
	ADMIN_ACTION_PROMOTION_RETURN      = 19
)

type AdminAction struct {
	Id          int64     `json:"id"`
	AdminId     int64     `json:"adminId"`
	TypeId      int64     `json:"typeId"`
	RefObjectId int64     `json:"refObjectId"`
	Detail      string    `json:"detail"`
	JsonInput   string    `json:"jsonInput"`
	JsonOutput  string    `json:"jsonOutput"`
	CreatedAt   time.Time `json:"createdAt"`
}
type AdminActionLogListRequest struct {
	AdminId   *int64 `form:"adminId"`
	AdminName string `form:"adminName"`
	TypeId    *int64 `form:"typeId"`
	Search    string `form:"search"`
	Page      int    `form:"page" default:"1" min:"1"`
	Limit     int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol   string `form:"sortCol"`
	SortAsc   string `form:"sortAsc"`
}
type AdminActionResponse struct {
	Id            int64     `json:"id"`
	AdminId       int64     `json:"adminId"`
	AdminUsername string    `json:"adminUsername"`
	TypeId        int64     `json:"typeId"`
	TypeName      string    `json:"typeName"`
	RefObjectId   int64     `json:"refObjectId"`
	Detail        string    `json:"detail"`
	CreatedAt     time.Time `json:"createdAt"`
}
type AdminActionCreateRequest struct {
	AdminId     int64  `json:"-" validate:"required"`
	TypeId      int64  `json:"typeId" validate:"required"`
	RefObjectId int64  `json:"refObjectId"`
	Detail      string `json:"detail"`
	JsonInput   string `json:"jsonInput"`
	JsonOutput  string `json:"jsonOutput"`
}
type AdminActionCreateBody struct {
	Id          int64  `json:"id"`
	AdminId     int64  `json:"adminId"`
	TypeId      int64  `json:"typeId"`
	Detail      string `json:"detail"`
	IsSuccess   bool   `json:"isSuccess"`
	IsShow      bool   `json:"isShow"`
	RefObjectId int64  `json:"refObjectId"`
	JsonInput   string `json:"jsonInput"`
	JsonOutput  string `json:"jsonOutput"`
}
