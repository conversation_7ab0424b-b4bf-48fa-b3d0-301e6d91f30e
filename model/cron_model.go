package model

import (
	"time"
)

type ApiStatus struct {
	Id            int64     `json:"id"`
	Path          string    `json:"path"`
	Page          int       `json:"page"`
	IsFailed      int       `json:"isFailed"`
	IsSuccess     int       `json:"isSuccess"`
	StatementDate string    `json:"statementDate"`
	CreatedAt     time.Time `json:"createdAt"`
	UpdatedAt     time.Time `json:"updatedAt"`
}
type ApiStatusRequest struct {
	Path          string `json:"path" binding:"required"`
	StatementDate string `json:"statementDate" binding:"required"`
}
type CronCreatePlayLogRequest struct {
	StatementDate string `form:"statementDate" binding:"required"`
}

type CronAction struct {
	Id        int64     `json:"id"`
	ActionKey string    `json:"actionKey"`
	Status    string    `json:"status"`
	Name      string    `json:"name"`
	Remark    string    `json:"remark"`
	UnlockAt  time.Time `json:"unlockAt"`
	CreatedAt time.Time `json:"createdAt"`
}
type CronActionCreateBody struct {
	Id        int64     `json:"id"`
	Status    string    `json:"status"`
	ActionKey string    `json:"actionKey"`
	Name      string    `json:"name"`
	Remark    string    `json:"remark"`
	UnlockAt  time.Time `json:"unlockAt"`
}

type RaceAction struct {
	Id          int64     `json:"id"`
	ActionKey   string    `json:"actionKey"`
	Status      string    `json:"status"`
	Name        string    `json:"name"`
	JsonRequest string    `json:"jsonRequest"`
	UnlockAt    time.Time `json:"unlockAt"`
	CreatedAt   time.Time `json:"createdAt"`
}
type RaceActionCreateBody struct {
	Id          int64     `json:"id"`
	Status      string    `json:"status"`
	ActionKey   string    `json:"actionKey"`
	Name        string    `json:"name"`
	JsonRequest string    `json:"jsonRequest"`
	UnlockAt    time.Time `json:"unlockAt"`
}
type RaceActionUpdateBody struct {
	Status    *string    `json:"status"`
	ActionKey *string    `json:"actionKey"`
	UnlockAt  *time.Time `json:"unlockAt"`
}
type ViewAgcSimpleWinLoseListRequest struct {
	AgentName     string `form:"agentName"`
	AgentKey      string `form:"agentKey"`
	MemberCode    string `form:"memberCode"`
	StatementDate string `form:"statementDate"`
	PageSize      int    `form:"pageSize"`
	PageIndex     int    `form:"pageIndex"`
}
