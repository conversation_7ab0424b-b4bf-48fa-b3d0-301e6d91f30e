package model

import (
	"time"
)

const (
	TURNOVER_STATEMENT_STATUS_PENDING   = int64(1)
	TURNOVER_STATEMENT_STATUS_CANCELED  = int64(2)
	TURNOVER_STATEMENT_STATUS_COMPLETED = int64(3)
	TURNOVER_STATEMENT_STATUS_EXPIRED   = int64(4)

	TURNOVER_TYPE_PROMOTION_FIRST_DEPOSIT = int64(1)
	TURNOVER_TYPE_PROMOTION_RETURN_LOSS   = int64(2)
	TURN_PROMOTION_SETTING_PLAY_ALL       = int64(3)
	TURN_PROMOTION_SETTING_PLAY_GAME      = int64(4)
	TURN_PROMOTION_SETTING_PLAY_SPORT     = int64(5)
	TURN_PROMOTION_SETTING_PLAY_CASINO    = int64(6)
	TURN_SETTING_COUPON_ALL               = int64(7)
	TURN_BONUS_BY_ADMIN                   = int64(8)
	TURN_BONUS_AFF_TYPE_NEW_REGISTER      = int64(9)
	TURN_BONUS_AFF_TYPE_FIRST_DEPOSIT     = int64(10)
	TURN_BONUS_AFF_TYPE_PLAY_COMMISSION   = int64(11)
	TURN_BONUS_ACTIVITY_DAILY             = int64(12)
	TURN_PROMOTION_SETTING_PLAY_P2P       = int64(13)
	TURN_PROMOTION_SETTING_PLAY_LOTTERY   = int64(14)
	TURN_PROMOTION_SETTING_PLAY_FINANCIAL = int64(15)
	TURNOVER_TYPE_PROMOTION_RETURN_TURN   = int64(16)
	TURNOVER_TYPE_SUCCESS_DEPOSIT         = int64(17)
	TURN_BONUS_ACTIVITY_DAILY_V2          = int64(18)
	TURN_BONUS_DAILY_QUEST_V2             = int64(19)
	// แยก status ออก เพราะ requriement บอก โปร ติิดเทิร์นห้ามรับซ้ำ แต่ คูปองเงินสด ไม่ต้องเช็ค เวลาเข็คเทิร์นจะไม่ต้อง งง
)

const (
	TURNOVER_CATE_FIRST_DEPOSIT            = "โบนัสฝากครั้งแรก"
	TURNOVER_CATE_BONUS                    = "บันทึกแจกโบนัส"
	TURNOVER_CATE_ACTIVITY                 = "กิจกรรม"
	TURNOVER_CATE_RETURN_LOSS              = "คืนยอดเสีย"
	TURNOVER_CATE_RETURN_TURN              = "คืนยอดคอมมิชชั่น"
	TURNOVER_CATE_PROMOTION                = "โปรโมชั่น"
	TURNOVER_CATE_LINK_AFFILIATE           = "รายได้ลิ้งก์รับทรัพย์"
	TURNOVER_CATE_AFF_TYPE_NEW_REGISTER    = "โบนัสสมัครรับรายได้เลย"
	TURNOVER_CATE_AFF_TYPE_FIRST_DEPOSIT   = "โบนัสสมัครฝากครั้งแรก"
	TURNOVER_CATE_AFF_TYPE_PLAY_COMMISSION = "รายได้คอมมิชชั่นลิ้งก์รับทรัพย์"
	TURNOVER_CATE_ACTIVITY_DAILY           = "รายได้กิจกรรมรายวัน"
	TURNOVER_CATE_SUCCESS_DEPOSIT          = "รายได้ฝากเงินสำเร็จ"
	TURNOVER_CATE_ACTIVITY_DAILY_V2        = "รายได้กิจกรรมรายวัน v2"
	TURNOVER_CATE_DAILY_QUEST_V2           = "รายได้กทำเควสประจำวัน v2"
)

type TurnoverUserStatement struct {
	Id              int64      `json:"id"`
	UserId          int64      `json:"userId"`
	TypeId          int64      `json:"typeId"`
	RefTypeId       int64      `json:"refTypeId"`
	PromotionName   string     `json:"promotionName"`
	BonusAmount     float64    `json:"bonusAmount"`
	StatusId        int64      `json:"statusId"`
	StartTurnAmount float64    `json:"startTurnAmount"`
	StartTurnAt     *time.Time `json:"startTurnAt"`
	TotalTurnAmount float64    `json:"totalTurnAmount"`
	EndTurnAt       *time.Time `json:"endTurnAt"`
	CreatedAt       time.Time  `json:"createdAt"`
	UpdatedAt       *time.Time `json:"updatedAt"`
}
type TurnoverUserStatementResponse struct {
	Id              int64      `json:"id"`
	UserId          int64      `json:"userId"`
	TypeId          int64      `json:"typeId"`
	TypeName        string     `json:"typeName"`
	RefTypeId       int64      `json:"refTypeId"`
	PromotionName   string     `json:"promotionName"`
	BonusAmount     float64    `json:"bonusAmount"`
	StatusId        int64      `json:"statusId"`
	StatusName      string     `json:"statusName"`
	StartTurnAmount float64    `json:"startTurnAmount"`
	StartTurnAt     *time.Time `json:"startTurnAt"`
	TotalTurnAmount float64    `json:"totalTurnAmount"`
	EndTurnAt       *time.Time `json:"endTurnAt"`
	CreatedAt       time.Time  `json:"createdAt"`
	UpdatedAt       *time.Time `json:"updatedAt"`
}
type TurnoverUserStatementListRequest struct {
	UserId   int64  `form:"userId" binding:"required"`
	IsActive *bool  `form:"isActive" default:"true"`
	Page     int    `form:"page" default:"1" min:"1"`
	Limit    int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol  string `form:"sortCol" extensions:"x-order:8"`
	SortAsc  string `form:"sortAsc" extensions:"x-order:9"`
}
type TurnoverUserCreateRequest struct {
	UserId        int64   `json:"userId"`
	TypeId        int64   `json:"typeId"`
	RefTypeId     int64   `json:"refTypeId"`
	Name          string  `json:"name"`
	PromotionName string  `json:"promotionName"`
	BonusAmount   float64 `json:"bonusAmount"`
	Amount        float64 `json:"amount"`
}
type TurnoverUserStatementCreateBody struct {
	Id              int64      `json:"id"`
	UserId          int64      `json:"userId"`
	TypeId          int64      `json:"typeId"`
	RefTypeId       int64      `json:"refTypeId"`
	Name            string     `json:"name"`
	PromotionName   string     `json:"promotionName"`
	BonusAmount     float64    `json:"bonusAmount"`
	StatusId        int64      `json:"statusId"`
	StartTurnAmount float64    `json:"startTurnAmount"`
	StartTurnAt     *time.Time `json:"startTurnAt"`
	TotalTurnAmount float64    `json:"totalTurnAmount"`
}
type TurnoverUserDecreaseRequest struct {
	UserId int64   `json:"userId"`
	Amount float64 `json:"amount"`
}

type TurnoverUserWithdrawRequest struct {
	UserId int64   `json:"userId"`
	Amount float64 `json:"amount"`
}

type TurnoverWithdrawLog struct {
	Id                 int64     `json:"id"`
	UserId             int64     `json:"userId"`
	OfDate             string    `json:"ofDate"`
	LogKey             string    `json:"logKey"`
	TotalWithdrawPrice float64   `json:"totalWithdrawPrice"`
	CurrentTurn        float64   `json:"currentTurn"`
	PlayTotal          float64   `json:"playTotal"`
	LastPlayY          float64   `json:"lastPlayY"`
	LastTotalX         float64   `json:"lastTotalX"`
	CreatedAt          time.Time `json:"createdAt"`
	UpdatedAt          time.Time `json:"updatedAt"`
}
type TurnoverWithdrawLogCreateBody struct {
	Id         int64   `json:"id"`
	UserId     int64   `json:"userId"`
	OfDate     string  `json:"ofDate"`
	LogKey     string  `json:"logKey"`
	LastTotalX float64 `json:"lastTotalX"`
}
type TurnoverWithdrawLogUpdateBody struct {
	WithdrawPrice float64 `json:"withdrawPrice"`
	CurrentTurn   float64 `json:"currentTurn"`
	PlayTotal     float64 `json:"playTotal"`
	LastPlayY     float64 `json:"lastPlayY"`
	LastTotalX    float64 `json:"lastTotalX"`
}

type TurnoverUserStatementUpdateBody struct {
	StatusId        int64      `json:"statusId"`
	TotalTurnAmount *float64   `json:"totalTurnAmount"`
	EndTurnAt       *time.Time `json:"endTurnAt"`
}
type TurnoverUserCurrentRequest struct {
	UserId int64 `json:"userId"`
}
type TurnoverUserCurrentResponse struct {
	UserId               int64   `json:"userId"`
	MemberCode           string  `json:"memberCode"`
	FromDate             string  `json:"fromDate"`
	ToDate               string  `json:"toDate"`
	TurnoverAmount       float64 `json:"turnoverAmount"`
	PlayedAmount         float64 `json:"playedAmount"`
	IsHasTurnover        bool    `json:"isHasTurnover"`
	CurrentPlayingAmount float64 `json:"currentPlayingAmount"`
	TotalLeftAmount      float64 `json:"totalLeftAmount"`
}
type TurnoverUserCurrentAmountRequest struct {
	UserId     int64  `json:"userId"`
	PlayerName string `json:"-"`
	From       string `json:"From"`
	To         string `json:"To"`
}
type AgentTurnoverRequest struct {
	AgentName  string  `json:"AgentName"`
	PlayerName string  `json:"PlayerName"`
	Timestamp  int     `json:"Timestamp"`
	Sign       string  `json:"Sign"`
	From       string  `json:"From"`
	To         string  `json:"To"`
	Product    []int64 `json:"Product"`
}
type AgentTurnoverResponse struct {
	Error     interface{} `json:"Error"`
	Message   string      `json:"Message"`
	Sign      string      `json:"Sign"`
	TimeStamp int64       `json:"TimeStamp"`
	UTC       string      `json:"UTC"`
	TurnOver  float64     `json:"TurnOver"`
}

type UserTurnoverStatementListRequest struct {
	FromDate      string `form:"fromDate"`
	ToDate        string `form:"toDate"`
	PromotionName string `form:"promotionName"`
	StatusId      *int64 `form:"statusId"`
	Search        string `form:"search"`
	Page          int    `form:"page" default:"1" min:"1"`
	Limit         int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol       string `form:"sortCol"`
	SortAsc       string `form:"sortAsc"`
}
type UserTurnoverStatementResponse struct {
	Id              int64      `json:"id"`
	UserId          int64      `json:"userId"`
	Username        string     `json:"username"`
	Fullname        string     `json:"fullname"`
	TypeId          int64      `json:"typeId"`
	TypeName        string     `json:"typeName"`
	PromotionName   string     `json:"promotionName"`
	BonusAmount     float64    `json:"bonusAmount"`
	StatusId        int64      `json:"statusId"`
	StatusName      string     `json:"statusName"`
	StartTurnAmount float64    `json:"startTurnAmount"`
	StartTurnAt     *time.Time `json:"startTurnAt"`
	TotalTurnAmount float64    `json:"totalTurnAmount"`
	EndTurnAt       *time.Time `json:"endTurnAt"`
	CreatedAt       time.Time  `json:"createdAt"`
	UpdatedAt       *time.Time `json:"updatedAt"`
}
type AdminCancelTurnoverStatementRequest struct {
	AdminId     int64 `form:"adminId" json:"-"`
	StatementId int64 `form:"statementId" binding:"required"`
}

type PromotionWebCheckTurnStatementRequest struct {
	UserId    int64 `form:"userId" binding:"required"`
	RefTypeId int64 `form:"refTypeId" binding:"required"`
}

type CreateTurnoverUserWithdrawLog struct {
	Id                 int64   `json:"-"`
	UserId             int64   `json:"userId"`
	LogKey             string  `json:"logKey"`
	TotalWithdrawPrice float64 `json:"totalWithdrawPrice"`
	CurrentTurn        float64 `json:"currentTurn"`
	PlayTotal          float64 `json:"playTotal"`
	LastPlayY          float64 `json:"lastPlayY"`
	LastTotalX         float64 `json:"lastTotalX"`
}

type CouponCashUserCheckTurnStatementRequest struct {
	UserId    int64   `form:"userId" binding:"required"`
	RefTypeId []int64 `form:"refTypeId" binding:"required"`
}
type CouponCashUserDeletedTurnStatementRequest struct {
	UserId    int64 `form:"userId" binding:"required"`
	RefTypeId int64 `form:"refTypeId" binding:"required"`
}

type CheckTurnSuccessOnThisDayRequest struct {
	UserId    int64  `form:"userId" binding:"required"`
	StartDate string `form:"startDate" binding:"required"`
}

type CheckTurnSuccessOnThisDayResponse struct {
	SumTotalTurnAllAmount       float64 `json:"sumTotalTurnAllAmount"`
	SumTotalTurnGameAmount      float64 `json:"sumTotalTurnGameAmount"`
	SumTotalTurnSportAmount     float64 `json:"sumTotalTurnSportAmount"`
	SumTotalTurnCasinoAmount    float64 `json:"sumTotalTurnCasinoAmount"`
	SumTotalTurnPvpAmount       float64 `json:"sumTotalTurnPvpAmount"`
	SumTotalTurnLottoAmount     float64 `json:"sumTotalTurnLottoAmount"`
	SumTotalTurnFinancialAmount float64 `json:"sumTotalTurnFinancialAmount"`
}

type SumLastestTurnOverReponseBackup struct {
	TotalToplayAmount float64   `json:"totalToplayAmount"`
	CheckOnDay        time.Time `json:"checkOnDay"`
}

type SumLastestTurnOverReponse struct {
	SumLastestTurnOverDetail []SumLastestTurnOverDetail `json:"sumLastestTurnOverDetail"`
	CheckOnDay               time.Time                  `json:"checkOnDay"`
}

type SumLastestTurnOverDetail struct {
	StartTurnAmount float64 `json:"startTurnAmount"`
	TypeId          int64   `json:"typeId"`
}
type GetTurnOverStatementNotClearResponse struct {
	Id            int64  `json:"id"`
	Name          string `json:"name"`
	PromotionName string `json:"promotionName"`
}

type TurnoverSetting struct {
	Id                            int64      `json:"id"`
	TidturnManualBonusPercent     int64      `json:"tidturnManualBonusPercent"`
	TidturnLinkRegisterPercent    int64      `json:"tidturnLinkRegisterPercent"`
	TidturnFirstDepositPercent    int64      `json:"tidturnFirstDepositPercent"`
	TidturnAffCommissionPercent   int64      `json:"tidturnAffCommissionPercent"`
	TidturnReturnLossPercent      int64      `json:"tidturnReturnLossPercent"`
	TidturnActivityDailyPercent   int64      `json:"tidturnActivityDailyPercent"`
	TidturnSuccessDepositPercent  int64      `json:"tidturnSuccessDepositPercent"`
	TidturnActivityDailyV2Percent int64      `json:"tidturnActivityDailyV2Percent"`
	CreatedAt                     time.Time  `json:"createdAt"`
	UpdatedAt                     *time.Time `json:"updatedAt"`
}
type TurnoverSettingResponse struct {
	Id                            int64      `json:"id"`
	TidturnManualBonusPercent     int64      `json:"tidturnManualBonusPercent"`
	TidturnLinkRegisterPercent    int64      `json:"tidturnLinkRegisterPercent"`
	TidturnFirstDepositPercent    int64      `json:"tidturnFirstDepositPercent"`
	TidturnAffCommissionPercent   int64      `json:"tidturnAffCommissionPercent"`
	TidturnReturnLossPercent      int64      `json:"tidturnReturnLossPercent"`
	TidturnReturnTurnPercent      int64      `json:"tidturnReturnTurnPercent"`
	TidturnActivityDailyPercent   int64      `json:"tidturnActivityDailyPercent"`
	TidturnSuccessDepositPercent  int64      `json:"tidturnSuccessDepositPercent"`
	TidturnActivityDailyV2Percent int64      `json:"tidturnActivityDailyV2Percent"`
	TidturnDailyQuestV2Percent    int64      `json:"tidturnDailyQuestV2Percent"`
	CreatedAt                     time.Time  `json:"createdAt"`
	UpdatedAt                     *time.Time `json:"updatedAt"`
}
type TurnoverSettingCreateBody struct {
	Id                            int64 `json:"id"`
	TidturnManualBonusPercent     int64 `json:"tidturnManualBonusPercent"`
	TidturnLinkRegisterPercent    int64 `json:"tidturnLinkRegisterPercent"`
	TidturnFirstDepositPercent    int64 `json:"tidturnFirstDepositPercent"`
	TidturnAffCommissionPercent   int64 `json:"tidturnAffCommissionPercent"`
	TidturnReturnLossPercent      int64 `json:"tidturnReturnLossPercent"`
	TidturnReturnTurnPercent      int64 `json:"tidturnReturnTurnPercent"`
	TidturnActivityDailyPercent   int64 `json:"tidturnActivityDailyPercent"`
	TidturnSuccessDepositPercent  int64 `json:"tidturnSuccessDepositPercent"`
	TidturnActivityDailyV2Percent int64 `json:"tidturnActivityDailyV2Percent"`
}
type TurnoverSettingUpdateRequest struct {
	TidturnManualBonusPercent     *int64 `form:"tidturnManualBonusPercent" validate:"min=0,max=1000"`
	TidturnLinkRegisterPercent    *int64 `form:"tidturnLinkRegisterPercent" validate:"min=0,max=1000"`
	TidturnFirstDepositPercent    *int64 `form:"tidturnFirstDepositPercent" validate:"min=0,max=1000"`
	TidturnAffCommissionPercent   *int64 `form:"tidturnAffCommissionPercent" validate:"min=0,max=1000"`
	TidturnReturnLossPercent      *int64 `form:"tidturnReturnLossPercent" validate:"min=0,max=1000"`
	TidturnReturnTurnPercent      *int64 `form:"tidturnReturnTurnPercent" validate:"min=0,max=1000"`
	TidturnActivityDailyPercent   *int64 `json:"tidturnActivityDailyPercent" validate:"min=0,max=1000"`
	TidturnSuccessDepositPercent  *int64 `json:"tidturnSuccessDepositPercent" validate:"min=0,max=1000"`
	TidturnActivityDailyV2Percent *int64 `json:"tidturnActivityDailyV2Percent" validate:"min=0,max=1000"`
	UpdateBy                      int64  `json:"-"`
}
type TurnoverSettingUpdateBody struct {
	TidturnManualBonusPercent     *int64 `json:"tidturnManualBonusPercent"`
	TidturnLinkRegisterPercent    *int64 `json:"tidturnLinkRegisterPercent"`
	TidturnFirstDepositPercent    *int64 `json:"tidturnFirstDepositPercent"`
	TidturnAffCommissionPercent   *int64 `json:"tidturnAffCommissionPercent"`
	TidturnReturnLossPercent      *int64 `json:"tidturnReturnLossPercent"`
	TidturnReturnTurnPercent      *int64 `json:"tidturnReturnTurnPercent"`
	TidturnActivityDailyPercent   *int64 `json:"tidturnActivityDailyPercent"`
	TidturnActivityDailyV2Percent *int64 `json:"tidturnActivityDailyV2Percent"`
	TidturnSuccessDepositPercent  *int64 `json:"tidturnSuccessDepositPercent"`
}

type GetUserTurnOverStartmentListRequest struct {
	UserId    int64  `form:"userId" binding:"required"`
	StartDate string `form:"startDate" time_format:"2006-01-02"`
	EndDate   string `form:"end" time_format:"2006-01-02"`
	Page      int    `form:"page" default:"1" min:"1"`
	Limit     int    `form:"limit" default:"10" min:"1" max:"100"`
	IsPending string `form:"-"`
}

type GetUserTurnOverStartmentListResponse struct {
	Message string                         `json:"message" validate:"required,min=1,max=255"`
	List    []GetUserTurnOverStartmentList `json:"list"`
	Total   int64                          `json:"total"`
}

type GetUserTurnOverStartmentList struct {
	Id              int64      `json:"id"`
	UserId          int64      `json:"userId"`
	TypeId          int64      `json:"typeId"`
	Name            string     `json:"name"`
	PromotionName   string     `json:"promotionName"`
	StartTurnAmount float64    `json:"startTurnAmount"`
	StartTurnAt     *time.Time `json:"startTurnAt"`
	TotalTurnAmount float64    `json:"totalTurnAmount"`
	StatusId        int64      `json:"statusId"`
	NeedToPlay      float64    `json:"needToPlay"`
	EndTurnAt       *time.Time `json:"endTurnAt"`
}

type GetTurnOverStartmentListRequest struct {
	StartDate string `form:"startDate" time_format:"2006-01-02"`
	EndDate   string `form:"end" time_format:"2006-01-02"`
	Search    string `form:"search"`
	Page      int    `form:"page" default:"1" min:"1"`
	Limit     int    `form:"limit" default:"10" min:"1" max:"100"`
}
type GetTurnOverStartmentListList struct {
	UserId             int64   `json:"userId"`
	MemberCode         string  `json:"memberCode"`
	Fullname           string  `json:"fullname"`
	Phone              string  `json:"phone"`
	SumStartTurnAmount float64 `json:"sumStartTurnAmount"`
}

type TurnOverWithdrawChecker struct {
	UserId int64   `json:"userId"`
	Amount float64 `json:"amount"`
}

type AvaliableGamePlayCheckTurnOverRequest struct {
	UserId   int64  `json:"-"`
	Category string `json:"category"`
}
