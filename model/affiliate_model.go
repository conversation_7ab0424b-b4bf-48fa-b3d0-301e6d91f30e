package model

import "time"

type AffiliateMemberListRequest struct {
	RefUserId        int64  `form:"refUserId" validate:"required"`
	RegisterFromDate string `form:"registerFromDate"`
	RegisterToDate   string `form:"registerToDate"`
	Search           string `form:"search"`
	Page             int    `form:"page" default:"1" min:"1"`
	Limit            int    `form:"limit" default:"10" min:"0" max:"100"`
	SortCol          string `form:"sortCol"`
	SortAsc          string `form:"sortAsc"`
}
type AffiliateMemberResponse struct {
	RefUserId            int64   `json:"refUserId"`
	UserId               int64   `json:"userId"`
	MemberCode           string  `json:"memberCode"`
	Username             string  `json:"username"`
	UserFullname         string  `json:"userFullname"`
	TotalDepositAmount   float64 `json:"totalDepositAmount"`
	TotalPlayAmount      float64 `json:"totalPlayAmount"`
	CurrentCreditBalance float64 `json:"currentCreditBalance"`
	RegisterAt           string  `json:"registerAt"`
}

type AffiliateDownlineTotalIncome struct {
	Id              int64      `json:"id"`
	UserId          int64      `json:"userId"`
	TotalCommission float64    `json:"totalCommission"`
	CreatedAt       time.Time  `json:"createdAt"`
	UpdatedAt       *time.Time `json:"updatedAt"`
}
type AffiliateDownlineTotalIncomeListRequest struct {
	RefBy int64 `form:"refBy" validate:"required"`
}
type AffiliateDownlineTotalIncomeResponse struct {
	Id              int64     `json:"id"`
	RefBy           int64     `json:"refBy"`
	UserId          int64     `json:"userId"`
	TotalCommission float64   `json:"totalCommission"`
	CreatedAt       time.Time `json:"createdAt"`
}
type AffiliateDownlineTotalIncomeCreateBody struct {
	Id              int64   `json:"id"`
	UserId          int64   `json:"userId"`
	TotalCommission float64 `json:"totalCommission"`
}
type AffiliateDownlineTotalIncomeIncreaseBody struct {
	Ukey       string  `json:"ukey"`
	Commission float64 `json:"commission"`
}
