package model

import (
	"time"
)

const (
	PROMOTION_RETURN_TYPE_LOSER      = 1
	PROMOTION_RETURN_CUT_TYPE_DAILY  = 1
	PROMOTION_RETURN_CUT_TYPE_WEEKLY = 2
	PROMOTION_RETURN_STATUS_PENDING  = int64(1)
	PROMOTION_RETURN_STATUS_READY    = int64(2)
	PROMOTION_RETURN_STATUS_TAKEN    = int64(3)
	PROMOTION_RETURN_STATUS_EXPIRED  = int64(4)
)

type PromotionOpenList struct {
	Key         string `json:"key"`
	Name        string `json:"name"`
	Description string `json:"description"`
	ImageUrl    string `json:"imageUrl"`
}
type PromotionReturnSetting struct {
	Id               int64      `json:"id"`
	ReturnPercent    float64    `json:"returnPercent"`
	ReturnTypeId     int64      `json:"returnTypeId"`
	CutTypeId        int64      `json:"cutTypeId"`
	MinLossPrice     float64    `json:"minLossPrice"`
	MaxReturnPrice   float64    `json:"maxReturnPrice"`
	CreditExpireDays int        `json:"creditExpireDays"`
	CalcOnSport      bool       `json:"calcOnSport"`
	CalcOnCasino     bool       `json:"calcOnCasino"`
	CalcOnGame       bool       `json:"calcOnGame"`
	CalcOnLottery    bool       `json:"calcOnLottery"`
	CalcOnP2p        bool       `json:"calcOnP2p"`
	CalcOnFinancial  bool       `json:"calcOnFinancial"`
	Detail           string     `json:"detail"`
	IsEnabled        bool       `json:"isEnable"`
	CreatedAt        time.Time  `json:"createdAt"`
	UpdatedAt        *time.Time `json:"updatedAt"`
}
type PromotionReturnSettingResponse struct {
	Id               int64      `json:"id"`
	ReturnPercent    float64    `json:"returnPercent"`
	ReturnTypeId     int64      `json:"returnTypeId"`
	CutTypeId        int64      `json:"cutTypeId"`
	MinLossPrice     float64    `json:"minLossPrice"`
	MaxReturnPrice   float64    `json:"maxReturnPrice"`
	CreditExpireDays int        `json:"creditExpireDays"`
	CalcOnSport      bool       `json:"calcOnSport"`
	CalcOnCasino     bool       `json:"calcOnCasino"`
	CalcOnGame       bool       `json:"calcOnGame"`
	CalcOnLottery    bool       `json:"calcOnLottery"`
	CalcOnP2p        bool       `json:"calcOnP2p"`
	CalcOnFinancial  bool       `json:"calcOnFinancial"`
	Detail           string     `json:"detail"`
	IsEnabled        bool       `json:"isEnable"`
	CreatedAt        time.Time  `json:"createdAt"`
	UpdatedAt        *time.Time `json:"updatedAt"`
	CacheExpiredAt   time.Time  `json:"cacheExpiredAt"`
}

type PromotionReturnSettingCreateBody struct {
	Id               int64   `json:"id"`
	ReturnPercent    float64 `json:"returnPercent"`
	ReturnTypeId     int64   `json:"returnTypeId"`
	CutTypeId        int64   `json:"cutTypeId"`
	MinLossPrice     float64 `json:"minLossPrice"`
	MaxReturnPrice   float64 `json:"maxReturnPrice"`
	CreditExpireDays int     `json:"creditExpireDays"`
	CalcOnSport      bool    `json:"calcOnSport"`
	CalcOnCasino     bool    `json:"calcOnCasino"`
	CalcOnGame       bool    `json:"calcOnGame"`
	CalcOnLottery    bool    `json:"calcOnLottery"`
	CalcOnP2p        bool    `json:"calcOnP2p"`
	CalcOnFinancial  bool    `json:"calcOnFinancial"`
	Detail           string  `json:"detail"`
}
type PromotionReturnSettingUpdateRequest struct {
	ReturnPercent    *float64 `json:"returnPercent"`
	ReturnTypeId     *int64   `json:"returnTypeId"`
	CutTypeId        *int64   `json:"cutTypeId"`
	MinLossPrice     *float64 `json:"minLossPrice"`
	MaxReturnPrice   *float64 `json:"maxReturnPrice"`
	CreditExpireDays *int     `json:"creditExpireDays"`
	CalcOnSport      *bool    `json:"calcOnSport"`
	CalcOnCasino     *bool    `json:"calcOnCasino"`
	CalcOnGame       *bool    `json:"calcOnGame"`
	CalcOnLottery    *bool    `json:"calcOnLottery"`
	CalcOnP2p        *bool    `json:"calcOnP2p"`
	CalcOnFinancial  *bool    `json:"calcOnFinancial"`
	Detail           *string  `json:"detail"`
	IsEnabled        *bool    `json:"isEnable"`
	UpdatedById      int64    `json:"-" gorm:"-"`
}
type PromotionReturnSettingUpdateBody struct {
	ReturnPercent    *float64 `json:"returnPercent"`
	ReturnTypeId     *int64   `json:"returnTypeId"`
	CutTypeId        *int64   `json:"cutTypeId"`
	MinLossPrice     *float64 `json:"minLossPrice"`
	MaxReturnPrice   *float64 `json:"maxReturnPrice"`
	CreditExpireDays *int     `json:"creditExpireDays"`
	CalcOnSport      *bool    `json:"calcOnSport"`
	CalcOnCasino     *bool    `json:"calcOnCasino"`
	CalcOnGame       *bool    `json:"calcOnGame"`
	CalcOnLottery    *bool    `json:"calcOnLottery"`
	CalcOnP2p        *bool    `json:"calcOnP2p"`
	CalcOnFinancial  *bool    `json:"calcOnFinancial"`
	Detail           string   `json:"detail"`
	IsEnabled        *bool    `json:"isEnable"`
}

type PromotionReturnUserDetail struct {
	UserId          int64                        `json:"userId"`
	StatusId        int64                        `json:"statusId"`
	StatusName      string                       `json:"statusName"`
	ReturnPercent   float64                      `json:"returnPercent"`
	ReturnPrice     float64                      `json:"returnPrice"`
	Detail          string                       `json:"detail"`
	RelatedItemList []PromotionReturnTransaction `json:"relatedItemList"`
}

type PromotionReturnTransaction struct {
	Id                 int64      `json:"id"`
	UserId             int64      `json:"userId"`
	DailyKey           string     `json:"dailyKey"`
	OfDate             string     `json:"ofDate"`
	TotalLossAmount    float64    `json:"totalLossAmount"`
	TotalLossSport     float64    `json:"totalLossSport"`
	TotalLossCasino    float64    `json:"totalLossCasino"`
	TotalLossGame      float64    `json:"totalLossGame"`
	TotalLossLottery   float64    `json:"totalLossLottery"`
	TotalLossP2p       float64    `json:"totalLossP2p"`
	TotalLossFinancial float64    `json:"totalLossFinancial"`
	StatusId           int64      `json:"statusId"`
	StatusName         string     `json:"statusName"`
	ReturnPercent      float64    `json:"returnPercent"`
	GameDetail         string     `json:"gameDetail"`
	ReturnTypeId       int64      `json:"returnTypeId"`
	CutTypeId          int64      `json:"cutTypeId"`
	MinLossPrice       float64    `json:"minLossPrice"`
	MaxReturnPrice     float64    `json:"maxReturnPrice"`
	CreditExpireDays   int        `json:"creditExpireDays"`
	ReturnPrice        float64    `json:"returnPrice"`
	CalcAt             *time.Time `json:"calcAt"`
	TakeAt             *time.Time `json:"takeAt"`
	TakenPrice         float64    `json:"takenPrice"`
	CreatedAt          time.Time  `json:"createdAt"`
	UpdatedAt          *time.Time `json:"updatedAt"`
}
type PromotionReturnTransactionListRequest struct {
	StatusId *int64 `form:"statusId"`
	UserId   int64  `form:"userId" json:"-"`
	Page     int    `form:"page" default:"1" min:"1"`
	Limit    int    `form:"limit" default:"10" min:"1" max:"100"`
}

type PromotionReturnHistoryUserListRequest struct {
	UserIds  []int64 `form:"userIds" json:"-"`
	DateType string  `form:"dateType"`
	FromDate string  `form:"fromDate"`
	ToDate   string  `form:"toDate"`
	Search   string  `form:"search"`
	Page     int     `form:"page" default:"1" min:"1"`
	Limit    int     `form:"limit" default:"10" min:"1" max:"100"`
	SortCol  string  `form:"sortCol"`
	SortAsc  string  `form:"sortAsc"`
}
type PromotionReturnHistoryUserSummaryResponse struct {
	DateType        string  `json:"dateType"`
	FromDate        string  `json:"fromDate"`
	ToDate          string  `json:"toDate"`
	TotalLossAmount float64 `json:"totalLossAmount"`
	TotalTakenPrice float64 `json:"totalTakenPrice"`
}
type PromotionReturnHistoryUserResponse struct {
	Id              int64   `json:"id"`
	MemberCode      string  `json:"memberCode"`
	Username        string  `json:"username"`
	Fullname        string  `json:"fullname"`
	TotalLossAmount float64 `json:"totalLossAmount"`
	TotalTakenPrice float64 `json:"totalTakenPrice"`
}
type PromotionReturnHistoryUserTotalResponse struct {
	UserId          int64   `json:"userId"`
	TotalLossAmount float64 `json:"totalLossAmount"`
	TotalTakenPrice float64 `json:"totalTakenPrice"`
}
type PromotionReturnHistoryListRequest struct {
	UserId   *int64 `form:"userId"`
	FromDate string `form:"fromDate"`
	ToDate   string `form:"toDate"`
	StatusId *int64 `form:"statusId"`
	Page     int    `form:"page" default:"1" min:"1"`
	Limit    int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol  string `form:"sortCol"`
	SortAsc  string `form:"sortAsc"`
}
type PromotionReturnHistoryReponse struct {
	Id                 int64   `json:"id"`
	UserId             int64   `json:"userId"`
	OfDate             string  `json:"ofDate"`
	TotalLossAmount    float64 `json:"totalLossAmount"`
	TotalLossSport     float64 `json:"totalLossSport"`
	TotalLossCasino    float64 `json:"totalLossCasino"`
	TotalLossGame      float64 `json:"totalLossGame"`
	TotalLossLottery   float64 `json:"totalLossLottery"`
	TotalLossP2p       float64 `json:"totalLossP2p"`
	TotalLossFinancial float64 `json:"totalLossFinancial"`
	CutTypeName        string  `json:"cutTypeName"`
	ReturnPercent      float64 `json:"returnPercent"`
	GameDetail         string  `json:"gameDetail"`
	ReturnPrice        float64 `json:"returnPrice"`
	CreditExpireDays   int     `json:"creditExpireDays"`
	CreditExpireAt     string  `json:"creditExpireAt"`
	LogStatus          string  `json:"logStatus"`
}
type PromotionReturnTransactionDailyKey struct {
	Id              int64     `json:"id"`
	UserId          int64     `json:"userId"`
	DailyKey        string    `json:"dailyKey"`
	TotalLossAmount float64   `json:"totalLossAmount"`
	CreatedAt       time.Time `json:"createdAt"`
}
type PromotionReturnTransactionCreateBody struct {
	Id                 int64      `json:"id"`
	UserId             int64      `json:"userId"`
	DailyKey           string     `json:"dailyKey"`
	TotalLossAmount    float64    `json:"totalLossAmount"`
	TotalLossSport     float64    `json:"totalLossSport"`
	TotalLossCasino    float64    `json:"totalLossCasino"`
	TotalLossGame      float64    `json:"totalLossGame"`
	TotalLossLottery   float64    `json:"totalLossLottery"`
	TotalLossP2p       float64    `json:"totalLossP2p"`
	TotalLossFinancial float64    `json:"totalLossFinancial"`
	OfDate             string     `json:"ofDate"`
	ReturnPercent      float64    `json:"returnPercent"`
	GameDetail         string     `json:"gameDetail"`
	ReturnTypeId       int64      `json:"returnTypeId"`
	CutTypeId          int64      `json:"cutTypeId"`
	MinLossPrice       float64    `json:"minLossPrice"`
	MaxReturnPrice     float64    `json:"maxReturnPrice"`
	CreditExpireDays   int        `json:"creditExpireDays"`
	ReturnPrice        float64    `json:"returnPrice"`
	CalcAt             *time.Time `json:"calcAt"`
	TakeAt             *time.Time `json:"takeAt"`
	TakenPrice         float64    `json:"takenPrice"`
}
type PromotionReturnTransactionUncalcListRequest struct {
	Page  int `form:"page" default:"1" min:"1"`
	Limit int `form:"limit" default:"10" min:"1" max:"100"`
}
type PromotionReturnTransactionCalcBody struct {
	CutTypeId   int64      `json:"cutTypeId"`
	StatusId    int64      `json:"statusId"`
	ReturnPrice float64    `json:"returnPrice"`
	CalcAt      *time.Time `json:"calcAt"`
}
type PromotionReturnTransactionUpdateBody struct {
	StatusId   int64      `json:"statusId"`
	TakeAt     *time.Time `json:"takeAt"`
	TakenPrice float64    `json:"takenPrice"`
}

type CronPlayLogCheckReponse struct {
	IsReady           bool  `json:"isReady"`
	TotalSuccessCount int64 `json:"totalSuccessCount"`
	TotalFailCount    int64 `json:"totalFailCount"`
}

type PromotionReturnCronRequest struct {
	OfDate string `form:"ofDate"`
}

type CutReturnLossDailyReponse struct {
	StatementDate      string `json:"statementDate"`
	TotalUserDataCount int64  `json:"totalUserDataCount"`
	TotalUserLossCount int64  `json:"totalUserLossCount"`
}
