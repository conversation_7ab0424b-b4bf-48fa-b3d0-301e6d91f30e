package model

import "time"

const (
	CONFIG_UPLOAD_DEPOSIT_SLIP_TYPE_DISABLED = "DISABLED"
	CONFIG_UPLOAD_DEPOSIT_SLIP_TYPE_FILE     = "FILE"
	CONFIG_UPLOAD_DEPOSIT_SLIP_TYPE_AUTO     = "QR_AUTO"
)

type Configuration struct {
	Id                            int64      `json:"id"`
	WebName                       string     `json:"webName"`
	LogoUrl                       string     `json:"logoUrl"`
	BackgroundColor               string     `json:"backgroundColor"`
	AutoUserApproveTypeId         int64      `json:"autoUserApproveTypeId"`
	UseOtpRegister                bool       `json:"useOtpRegister"`
	TurnWithdrawTypeId            int64      `json:"turnWithdrawTypeId"`
	AllowOnlineRegistration       bool       `json:"allowOnlineRegistration"`
	AllowOnlineRegisterForm       bool       `json:"allowOnlineRegisterForm"`
	CheckAccountNameFastbank      bool       `json:"checkAccountNameFastbank"`
	MinimumDeposit                int64      `json:"minimumDeposit"`
	MinimumWithdraw               float64    `json:"minimumWithdraw"`
	MinFirstMemberDeposit         int64      `json:"minFirstMemberDeposit"`
	IdLine                        string     `json:"idLine"`
	UrlLine                       string     `json:"urlLine"`
	ClearTurnCreditLess           float64    `json:"clearTurnCreditLess,omitempty"`
	UseUploadDepositSlip          bool       `json:"useUploadDepositSlip"`
	UploadDepositSlipType         string     `json:"uploadDepositSlipType"`
	UseThCurrency                 bool       `json:"useThCurrency"`
	UseLaosCurrency               bool       `json:"useLaosCurrency"`
	IdWhatsapp                    string     `json:"idWhatsapp"`
	UrlWhatsapp                   string     `json:"urlWhatsapp"`
	IdTelegram                    string     `json:"idTelegram"`
	ConfigurationRegisterFormatId int64      `json:"configurationRegisterFormatId"`
	UrlTelegram                   string     `json:"urlTelegram"`
	CheckPhoneCaptchaLen          int64      `json:"checkPhoneCaptchaLen"`
	OpenGameNewTab                bool       `json:"openGameNewTab"`
	ShowWebAffName                bool       `json:"showWebAffName"`
	CreatedAt                     time.Time  `json:"createdAt"`
	UpdatedAt                     *time.Time `json:"updatedAt"`
	DeletedAt                     *time.Time `json:"deletedAt"`
}
type ConfigurationResponse struct {
	Id                            int64     `json:"id"`
	WebName                       string    `json:"webName"`
	LogoUrl                       string    `json:"logoUrl"`
	BackgroundColor               string    `json:"backgroundColor"`
	AutoUserApproveTypeId         int64     `json:"autoUserApproveTypeId"`
	AutoUserApproveType           string    `json:"autoUserApproveType"`
	AutoUserApproveTypeLabelTh    string    `json:"autoUserApproveTypeLabelTh"`
	UseOtpRegister                bool      `json:"useOtpRegister"`
	AutoWithdrawType              string    `json:"autoWithdrawType"`
	AutoWithdrawTypeLabelTh       string    `json:"autoWithdrawTypeLabelTh"`
	TurnWithdrawTypeId            int64     `json:"turnWithdrawTypeId"`
	TurnWithdrawType              string    `json:"turnWithdrawType"`
	TurnWithdrawTypeLabelTh       string    `json:"turnWithdrawTypeLabelTh"`
	AllowOnlineRegistration       bool      `json:"allowOnlineRegistration"`
	AllowOnlineRegisterForm       bool      `json:"allowOnlineRegisterForm"`
	CheckAccountNameFastbank      bool      `json:"checkAccountNameFastbank"`
	IsShowDeposit                 bool      `json:"isShowDeposit"`
	IsShowWithdraw                bool      `json:"isShowWithdraw"`
	MinimumDeposit                int64     `json:"minimumDeposit"`
	MinimumWithdraw               float64   `json:"minimumWithdraw"`
	MinFirstMemberDeposit         int64     `json:"minFirstMemberDeposit"`
	IdLine                        string    `json:"idLine"`
	UrlLine                       string    `json:"urlLine"`
	UseThCurrency                 bool      `json:"useThCurrency"`
	UseLaosCurrency               bool      `json:"useLaosCurrency"`
	IdWhatsapp                    string    `json:"idWhatsapp"`
	UrlWhatsapp                   string    `json:"urlWhatsapp"`
	IdTelegram                    string    `json:"idTelegram"`
	ConfigurationRegisterFormatId int64     `json:"configurationRegisterFormatId"`
	UrlTelegram                   string    `json:"urlTelegram"`
	ClearTurnCreditLess           float64   `json:"clearTurnCreditLess,omitempty"`
	UseUploadDepositSlip          bool      `json:"useUploadDepositSlip"`
	UploadDepositSlipType         string    `json:"uploadDepositSlipType"`
	CheckPhoneCaptchaLen          int64     `json:"checkPhoneCaptchaLen"`
	OpenGameNewTab                bool      `json:"openGameNewTab"`
	ShowWebAffName                bool      `json:"showWebAffName"`
	IsTotpVerify                  bool      `json:"isTotpVerify"`
	TokenExpiredMinute            int64     `json:"tokenExpiredMinute"`
	WithdrawMaximumAuto           float64   `json:"withdrawMaximumAuto"`
	WithdrawMaximum               float64   `json:"withdrawMaximum"`
	CacheExpiredAt                time.Time `json:"cacheExpiredAt"`
}
type WebGetConfigurationRegisterFormatResponse struct {
	ConfigurationRegisterFormatId int64 `json:"configurationRegisterFormatId"`
}

type ConfigurationUserRegisterResponse struct {
	Id                            int64 `json:"id"`
	UseOtpRegister                bool  `json:"useOtpRegister"`
	AllowOnlineRegisterForm       bool  `json:"allowOnlineRegisterForm"`
	CheckAccountNameFastbank      bool  `json:"checkAccountNameFastbank"`
	ConfigurationRegisterFormatId int64 `json:"configurationRegisterFormatId"`
}
type ConfigurationBankResponse struct {
	UseThCurrency   bool `json:"useThCurrency"`
	UseLaosCurrency bool `json:"useLaosCurrency"`
}

type CreateConfigurationBody struct {
	Id                            *int64   `json:"id"`
	WebName                       *string  `json:"webName"`
	LogoUrl                       *string  `json:"logoUrl"`
	BackgroundColor               *string  `json:"backgroundColor"`
	AutoUserApproveTypeId         *int64   `json:"autoUserApproveTypeId"`
	UseOtpRegister                *bool    `json:"useOtpRegister"`
	TurnWithdrawTypeId            *int64   `json:"turnWithdrawTypeId"`
	AllowOnlineRegistration       *bool    `json:"allowOnlineRegistration"`
	AllowOnlineRegisterForm       *bool    `json:"allowOnlineRegisterForm"`
	CheckAccountNameFastbank      *bool    `json:"checkAccountNameFastbank"`
	IsShowDeposit                 *bool    `json:"isShowDeposit"`
	IsShowWithdraw                *bool    `json:"isShowWithdraw"`
	MinimumDeposit                *int64   `json:"minimumDeposit"`
	MinimumWithdraw               *float64 `json:"minimumWithdraw"`
	MinFirstMemberDeposit         *int64   `json:"minFirstMemberDeposit"`
	IdLine                        *string  `json:"idLine"`
	UrlLine                       *string  `json:"urlLine"`
	UseThCurrency                 *bool    `json:"useThCurrency"`
	UseLaosCurrency               *bool    `json:"useLaosCurrency"`
	IdWhatsapp                    *string  `json:"idWhatsapp"`
	UrlWhatsapp                   *string  `json:"urlWhatsapp"`
	IdTelegram                    *string  `json:"idTelegram"`
	ConfigurationRegisterFormatId *int64   `json:"configurationRegisterFormatId"`
	UrlTelegram                   *string  `json:"urlTelegram"`
	ClearTurnCreditLess           *float64 `json:"clearTurnCreditLess,omitempty"`
	UseUploadDepositSlip          *bool    `json:"useUploadDepositSlip"`
	UploadDepositSlipType         *string  `json:"uploadDepositSlipType"`
	CheckPhoneCaptchaLen          *int64   `json:"checkPhoneCaptchaLen"`
	OpenGameNewTab                *bool    `json:"openGameNewTab"`
	ShowWebAffName                *bool    `json:"showWebAffName"`
	IsTotpVerify                  *bool    `json:"isTotpVerify"`
	TokenExpiredMinute            *int64   `json:"tokenExpiredMinute"`
	WithdrawMaximumAuto           *float64 `json:"withdrawMaximumAuto"`
	WithdrawMaximum               *float64 `json:"withdrawMaximum"`
}
type UpdateConfigurationRequest struct {
	LogoUrl                       *string  `json:"logoUrl,omitempty"`
	WebName                       *string  `json:"webName,omitempty"`
	BackgroundColor               *string  `json:"backgroundColor,omitempty"`
	AutoUserApproveTypeId         *int64   `json:"autoUserApproveTypeId,omitempty"`
	UseOtpRegister                *bool    `json:"useOtpRegister,omitempty"`
	TurnWithdrawTypeId            *int64   `json:"turnWithdrawTypeId,omitempty"`
	AllowOnlineRegistration       *bool    `json:"allowOnlineRegistration,omitempty"`
	AllowOnlineRegisterForm       *bool    `json:"allowOnlineRegisterForm,omitempty"`
	CheckAccountNameFastbank      *bool    `json:"checkAccountNameFastbank,omitempty"`
	IsShowDeposit                 *bool    `json:"isShowDeposit,omitempty"`
	IsShowWithdraw                *bool    `json:"isShowWithdraw,omitempty"`
	MinimumDeposit                *int64   `json:"minimumDeposit,omitempty"`
	MinimumWithdraw               *float64 `json:"minimumWithdraw,omitempty"`
	MinFirstMemberDeposit         *int64   `json:"minFirstMemberDeposit,omitempty"`
	IdLine                        *string  `json:"idLine,omitempty"`
	UrlLine                       *string  `json:"urlLine,omitempty"`
	UseThCurrency                 *bool    `json:"useThCurrency"`
	UseLaosCurrency               *bool    `json:"useLaosCurrency"`
	IdWhatsapp                    *string  `json:"idWhatsapp"`
	UrlWhatsapp                   *string  `json:"urlWhatsapp"`
	IdTelegram                    *string  `json:"idTelegram"`
	ConfigurationRegisterFormatId *int64   `json:"configurationRegisterFormatId"`
	UrlTelegram                   *string  `json:"urlTelegram"`
	CheckPhoneCaptchaLen          *int64   `json:"checkPhoneCaptchaLen,omitempty"`
	OpenGameNewTab                *bool    `json:"openGameNewTab,omitempty"`
	ClearTurnCreditLess           *float64 `json:"clearTurnCreditLess,omitempty"`
	UseUploadDepositSlip          *bool    `json:"useUploadDepositSlip,omitempty"`
	UploadDepositSlipType         *string  `json:"uploadDepositSlipType"`
	ShowWebAffName                *bool    `json:"showWebAffName,omitempty"`
	IsTotpVerify                  *bool    `json:"isTotpVerify,omitempty"`
	TokenExpiredMinute            *int64   `json:"tokenExpiredMinute"`
	WithdrawMaximumAuto           *float64 `json:"withdrawMaximumAuto"`
	WithdrawMaximum               *float64 `json:"withdrawMaximum"`
	UpdateBy                      int64    `json:"-"`
}
type UpdateConfigurationBody struct {
	Id                            int64    `json:"id"`
	WebName                       *string  `json:"webName"`
	LogoUrl                       *string  `json:"logoUrl"`
	BackgroundColor               *string  `json:"backgroundColor"`
	AutoUserApproveTypeId         *int64   `json:"autoUserApproveTypeId"`
	UseOtpRegister                *bool    `json:"useOtpRegister"`
	TurnWithdrawTypeId            *int64   `json:"turnWithdrawTypeId"`
	AllowOnlineRegistration       *bool    `json:"allowOnlineRegistration"`
	AllowOnlineRegisterForm       *bool    `json:"allowOnlineRegisterForm"`
	CheckAccountNameFastbank      *bool    `json:"checkAccountNameFastbank"`
	IsShowDeposit                 *bool    `json:"isShowDeposit"`
	IsShowWithdraw                *bool    `json:"isShowWithdraw"`
	MinimumDeposit                *int64   `json:"minimumDeposit"`
	MinimumWithdraw               *float64 `json:"minimumWithdraw"`
	MinFirstMemberDeposit         *int64   `json:"minFirstMemberDeposit"`
	IdLine                        *string  `json:"idLine"`
	UrlLine                       *string  `json:"urlLine"`
	UseThCurrency                 *bool    `json:"useThCurrency"`
	UseLaosCurrency               *bool    `json:"useLaosCurrency"`
	IdWhatsapp                    *string  `json:"idWhatsapp"`
	UrlWhatsapp                   *string  `json:"urlWhatsapp"`
	IdTelegram                    *string  `json:"idTelegram"`
	ConfigurationRegisterFormatId *int64   `json:"configurationRegisterFormatId"`
	UrlTelegram                   *string  `json:"urlTelegram"`
	ClearTurnCreditLess           *float64 `json:"clearTurnCreditLess,omitempty"`
	UseUploadDepositSlip          *bool    `json:"useUploadDepositSlip"`
	UploadDepositSlipType         *string  `json:"uploadDepositSlipType"`
	CheckPhoneCaptchaLen          *int64   `json:"checkPhoneCaptchaLen"`
	OpenGameNewTab                *bool    `json:"openGameNewTab"`
	ShowWebAffName                *bool    `json:"showWebAffName"`
	IsTotpVerify                  *bool    `json:"isTotpVerify"`
	TokenExpiredMinute            *int64   `json:"tokenExpiredMinute"`
	WithdrawMaximumAuto           *float64 `json:"withdrawMaximumAuto"`
	WithdrawMaximum               *float64 `json:"withdrawMaximum"`
}

type AutoUserApproveType struct {
	Id      int64  `json:"id"`
	Name    string `json:"name"`
	LabelTh string `json:"label_th"`
	LabelEn string `json:"label_en"`
}

type AutoWithdrawType struct {
	Id      int64  `json:"id"`
	Name    string `json:"name"`
	LabelTh string `json:"label_th"`
	LabelEn string `json:"label_en"`
}

type TurnWithdrawType struct {
	Id      int64  `json:"id"`
	Name    string `json:"name"`
	LabelTh string `json:"label_th"`
	LabelEn string `json:"label_en"`
}

type GetWebConfigurationBody struct {
	MinimumDeposit        int64     `json:"minimumDeposit"`
	MinimumWithdraw       float64   `json:"minimumWithdraw"`
	MinFirstMemberDeposit int64     `json:"minFirstMemberDeposit"`
	CacheExpiredAt        time.Time `json:"cacheExpiredAt"`
	UseUploadDepositSlip  bool      `json:"useUploadDepositSlip"`
	UploadDepositSlipType string    `json:"uploadDepositSlipType"`
}

type GetContactConfigWebResponse struct {
	IdLine         string    `json:"idLine"`
	UrlLine        string    `json:"urlLine"`
	IdWhatsapp     string    `json:"idWhatsapp"`
	UrlWhatsapp    string    `json:"urlWhatsapp"`
	IdTelegram     string    `json:"idTelegram"`
	UrlTelegram    string    `json:"urlTelegram"`
	CacheExpiredAt time.Time `json:"cacheExpiredAt"`
}

type GetWebConfigurationResponse struct {
	MinimumDeposit        int64   `json:"minimumDeposit"`
	MinimumWithdraw       float64 `json:"minimumWithdraw"`
	MinFirstMemberDeposit int64   `json:"minFirstMemberDeposit"`
	MaximumWithdraw       float64 `json:"maximumWithdraw"`
	UseUploadDepositSlip  bool    `json:"useUploadDepositSlip"`
	UploadDepositSlipType string  `json:"uploadDepositSlipType"`
	MaximumWithdrawPerDay float64 `json:"maximumWithdrawPerDay"`
	CurrentWithdrawPerDay float64 `json:"currentWithdrawPerDay"`
	MaximumWithdrawCount  int64   `json:"maximumWithdrawCount"`
	CurrentWithdrawCount  int64   `json:"currentWithdrawCount"`
}

const (
	NOTI_TOKEN_TYPE_LINE     = 1
	NOTI_TOKEN_TYPE_TELEGRAM = 2
)

type ConfigurationBackofficeNotification struct {
	Id                         int64 `json:"id"`
	BackofficeIsOn             bool  `json:"backofficeIsOn"`
	BackofficeNewMember        bool  `json:"backofficeNewMember"`
	BackofficeDeposit          bool  `json:"backofficeDeposit"`
	BackofficeWithdraw         bool  `json:"backofficeWithdraw"`
	BackofficeBonus            bool  `json:"backofficeBonus"`
	BackofficeSoundOnNewMember bool  `json:"backofficeSoundOnNewMember"`
	BackofficeSoundOnDeposit   bool  `json:"backofficeSoundOnDeposit"`
	BackofficeSoundOnWithdraw  bool  `json:"backofficeSoundOnWithdraw"`
	BackofficeSoundOnBonus     bool  `json:"backofficeSoundOnBonus"`
}

type CreateConfigurationBackofficeNotificationBody struct {
	Id                         int64 `json:"-"`
	BackofficeIsOn             *bool `json:"backofficeIsOn"`
	BackofficeNewMember        *bool `json:"backofficeNewMember"`
	BackofficeDeposit          *bool `json:"backofficeDeposit"`
	BackofficeWithdraw         *bool `json:"backofficeWithdraw"`
	BackofficeBonus            *bool `json:"backofficeBonus"`
	BackofficeSoundOnNewMember *bool `json:"backofficeSoundOnNewMember"`
	BackofficeSoundOnDeposit   *bool `json:"backofficeSoundOnDeposit"`
	BackofficeSoundOnWithdraw  *bool `json:"backofficeSoundOnWithdraw"`
	BackofficeSoundOnBonus     *bool `json:"backofficeSoundOnBonus"`
}

type UpdateConfigurationBackofficeNotificationRequest struct {
	BackofficeIsOn             *bool `json:"backofficeIsOn"`
	BackofficeNewMember        *bool `json:"backofficeNewMember"`
	BackofficeDeposit          *bool `json:"backofficeDeposit"`
	BackofficeWithdraw         *bool `json:"backofficeWithdraw"`
	BackofficeBonus            *bool `json:"backofficeBonus"`
	BackofficeSoundOnNewMember *bool `json:"backofficeSoundOnNewMember"`
	BackofficeSoundOnDeposit   *bool `json:"backofficeSoundOnDeposit"`
	BackofficeSoundOnWithdraw  *bool `json:"backofficeSoundOnWithdraw"`
	BackofficeSoundOnBonus     *bool `json:"backofficeSoundOnBonus"`
}

type UpdateConfigurationBackofficeNotificationBody struct {
	Id                         *int64 `json:"id"`
	BackofficeIsOn             *bool  `json:"backofficeIsOn"`
	BackofficeNewMember        *bool  `json:"backofficeNewMember"`
	BackofficeDeposit          *bool  `json:"backofficeDeposit"`
	BackofficeWithdraw         *bool  `json:"backofficeWithdraw"`
	BackofficeBonus            *bool  `json:"backofficeBonus"`
	BackofficeSoundOnNewMember *bool  `json:"backofficeSoundOnNewMember"`
	BackofficeSoundOnDeposit   *bool  `json:"backofficeSoundOnDeposit"`
	BackofficeSoundOnWithdraw  *bool  `json:"backofficeSoundOnWithdraw"`
	BackofficeSoundOnBonus     *bool  `json:"backofficeSoundOnBonus"`
}

type GetConfigurationBackofficeNotificationResponse struct {
	Id                         int64 `json:"id"`
	BackofficeIsOn             bool  `json:"backofficeIsOn"`
	BackofficeNewMember        bool  `json:"backofficeNewMember"`
	BackofficeDeposit          bool  `json:"backofficeDeposit"`
	BackofficeWithdraw         bool  `json:"backofficeWithdraw"`
	BackofficeBonus            bool  `json:"backofficeBonus"`
	BackofficeSoundOnNewMember bool  `json:"backofficeSoundOnNewMember"`
	BackofficeSoundOnDeposit   bool  `json:"backofficeSoundOnDeposit"`
	BackofficeSoundOnWithdraw  bool  `json:"backofficeSoundOnWithdraw"`
	BackofficeSoundOnBonus     bool  `json:"backofficeSoundOnBonus"`
}

type ConfigurationNotificationToken struct {
	Id                                  int64  `json:"id"`
	ConfigurationExternalNotificationId int64  `json:"configurationExternalNotificationId"`
	ConfigurationTokenTypeId            int64  `json:"configurationTokenTypeId"`
	Token                               string `json:"token"`
	CreatedAt                           time.Time
	UpdatedAt                           *time.Time
	DeletedAt                           *time.Time
}

type CreateConfigurationNotificationTokenBody struct {
	Id                                  int64  `json:"-"`
	ConfigurationExternalNotificationId int64  `json:"-"`
	ConfigurationTokenTypeId            int64  `json:"configurationTokenTypeId"`
	Token                               string `json:"token"`
}

type UpdateConfigurationNotificationTokenRequest struct {
	Id                                  *int64  `json:"id"`
	ConfigurationExternalNotificationId *int64  `json:"-"`
	ConfigurationTokenTypeId            *int64  `json:"configurationTokenTypeId"`
	Token                               *string `json:"token"`
}

type UpdateConfigurationNotificationTokenBody struct {
	Id                                  int64   `json:"id"`
	ConfigurationExternalNotificationId *int64  `json:"configurationExternalNotificationId"`
	ConfigurationTokenTypeId            *int64  `json:"configurationTokenTypeId"`
	Token                               *string `json:"token"`
}
type CacheConfigurationNotificationToken struct {
	Result []GetConfigurationNotificationTokenResponse `json:"result"`
}
type GetConfigurationNotificationTokenResponse struct {
	Id                                  int64  `json:"id"`
	ConfigurationExternalNotificationId int64  `json:"configurationExternalNotificationId"`
	ConfigurationTokenTypeId            int64  `json:"configurationTokenTypeId"`
	Token                               string `json:"token"`
}
type GetConfigurationNotificationTokenList struct {
	ConfigurationExternalNotificationId *int64 `form:"configurationExternalNotificationId"`
	ConfigurationTokenTypeId            *int64 `form:"configurationTokenTypeId"`
}

type ConfigurationNotificationTokenGetById struct {
	Id int64 `uri:"id" binding:"required"`
}

type ConfigurationExternalNotification struct {
	Id                              int64      `json:"id"`
	NotificationName                string     `json:"notificationName"`
	TelegramIsOn                    bool       `json:"telegramIsOn"`
	TelegramNewMember               bool       `json:"telegramNewMember"`
	TelegramBeforeDeposit           bool       `json:"telegramBeforeDeposit"`
	TelegramAfterDeposit            bool       `json:"telegramAfterDeposit"`
	TelegramWithdrawSuccess         bool       `json:"telegramWithdrawSuccess"`
	TelegramWithdrawPending         bool       `json:"telegramWithdrawPending"`
	TelegramWithdrawFailed          bool       `json:"telegramWithdrawFailed"`
	TelegramPullCredit              bool       `json:"telegramPullCredit"`
	TelegramBonus                   bool       `json:"telegramBonus"`
	TelegramPromotion               bool       `json:"telegramPromotion"`
	TelegramActivityBeforeBonus     bool       `json:"telegramActivityBeforeBonus"`
	TelegramActivityAfterBonus      bool       `json:"telegramActivityAfterBonus"`
	TelegramMoveMoney               bool       `json:"telegramMoveMoney"`
	TelegramTransactionHourSummary  bool       `json:"telegramTransactionHourSummary"`
	TelegramAffiliateDailySummary   bool       `json:"telegramAffiliateDailySummary"`
	TelegramTransactionDailySummary bool       `json:"telegramTransactionDailySummary"`
	LineIsOn                        bool       `json:"lineIsOn"`
	LineNewMember                   bool       `json:"lineNewMember"`
	LineBeforeDeposit               bool       `json:"lineBeforeDeposit"`
	LineAfterDeposit                bool       `json:"lineAfterDeposit"`
	LineWithdrawSuccess             bool       `json:"lineWithdrawSuccess"`
	LineWithdrawPending             bool       `json:"lineWithdrawPending"`
	LineWithdrawFailed              bool       `json:"lineWithdrawFailed"`
	LinePullCredit                  bool       `json:"linePullCredit"`
	LineBonus                       bool       `json:"lineBonus"`
	LinePromotion                   bool       `json:"linePromotion"`
	LineActivityBeforeBonus         bool       `json:"lineActivityBeforeBonus"`
	LineActivityAfterBonus          bool       `json:"lineActivityAfterBonus"`
	LineMoveMoney                   bool       `json:"lineMoveMoney"`
	LineTransactionHourSummary      bool       `json:"lineTransactionHourSummary"`
	LineAffiliateDailySummary       bool       `json:"lineAffiliateDailySummary"`
	LineTransactionDailySummary     bool       `json:"lineTransactionDailySummary"`
	CreatedAt                       time.Time  `json:"createdAt"`
	UpdatedAt                       *time.Time `json:"updatedAt"`
	DeletedAt                       *time.Time `json:"deletedAt"`
}

type CreateConfigurationExternalNotificationRequest struct {
	Id                              int64                                      `json:"-"`
	NotificationName                string                                     `json:"notificationName"`
	TelegramIsOn                    bool                                       `json:"telegramIsOn"`
	TelegramNewMember               bool                                       `json:"telegramNewMember"`
	TelegramBeforeDeposit           bool                                       `json:"telegramBeforeDeposit"`
	TelegramAfterDeposit            bool                                       `json:"telegramAfterDeposit"`
	TelegramWithdrawSuccess         bool                                       `json:"telegramWithdrawSuccess"`
	TelegramWithdrawPending         bool                                       `json:"telegramWithdrawPending"`
	TelegramWithdrawFailed          bool                                       `json:"telegramWithdrawFailed"`
	TelegramPullCredit              bool                                       `json:"telegramPullCredit"`
	TelegramBonus                   bool                                       `json:"telegramBonus"`
	TelegramPromotion               bool                                       `json:"telegramPromotion"`
	TelegramActivityBeforeBonus     bool                                       `json:"telegramActivityBeforeBonus"`
	TelegramActivityAfterBonus      bool                                       `json:"telegramActivityAfterBonus"`
	TelegramMoveMoney               bool                                       `json:"telegramMoveMoney"`
	TelegramAffiliateDailySummary   bool                                       `json:"telegramAffiliateDailySummary"`
	TelegramTransactionHourSummary  bool                                       `json:"telegramTransactionHourSummary"`
	TelegramTransactionDailySummary bool                                       `json:"telegramTransactionDailySummary"`
	LineIsOn                        bool                                       `json:"lineIsOn"`
	LineNewMember                   bool                                       `json:"lineNewMember"`
	LineBeforeDeposit               bool                                       `json:"lineBeforeDeposit"`
	LineAfterDeposit                bool                                       `json:"lineAfterDeposit"`
	LineWithdrawSuccess             bool                                       `json:"lineWithdrawSuccess"`
	LineWithdrawPending             bool                                       `json:"lineWithdrawPending"`
	LineWithdrawFailed              bool                                       `json:"lineWithdrawFailed"`
	LinePullCredit                  bool                                       `json:"linePullCredit"`
	LineBonus                       bool                                       `json:"lineBonus"`
	LinePromotion                   bool                                       `json:"linePromotion"`
	LineActivityBeforeBonus         bool                                       `json:"lineActivityBeforeBonus"`
	LineActivityAfterBonus          bool                                       `json:"lineActivityAfterBonus"`
	LineMoveMoney                   bool                                       `json:"lineMoveMoney"`
	LineTransactionHourSummary      bool                                       `json:"lineTransactionHourSummary"`
	LineAffiliateDailySummary       bool                                       `json:"lineAffiliateDailySummary"`
	LineTransactionDailySummary     bool                                       `json:"lineTransactionDailySummary"`
	Tokens                          []CreateConfigurationNotificationTokenBody `json:"tokens"`
}
type CreateConfigurationExternalNotificationBody struct {
	Id                              int64  `json:"-"`
	NotificationName                string `json:"notificationName"`
	TelegramIsOn                    bool   `json:"telegramIsOn"`
	TelegramNewMember               bool   `json:"telegramNewMember"`
	TelegramBeforeDeposit           bool   `json:"telegramBeforeDeposit"`
	TelegramAfterDeposit            bool   `json:"telegramAfterDeposit"`
	TelegramWithdrawSuccess         bool   `json:"telegramWithdrawSuccess"`
	TelegramWithdrawPending         bool   `json:"telegramWithdrawPending"`
	TelegramWithdrawFailed          bool   `json:"telegramWithdrawFailed"`
	TelegramPullCredit              bool   `json:"telegramPullCredit"`
	TelegramBonus                   bool   `json:"telegramBonus"`
	TelegramPromotion               bool   `json:"telegramPromotion"`
	TelegramActivityBeforeBonus     bool   `json:"telegramActivityBeforeBonus"`
	TelegramActivityAfterBonus      bool   `json:"telegramActivityAfterBonus"`
	TelegramMoveMoney               bool   `json:"telegramMoveMoney"`
	TelegramTransactionHourSummary  bool   `json:"telegramTransactionHourSummary"`
	TelegramAffiliateDailySummary   bool   `json:"telegramAffiliateDailySummary"`
	TelegramTransactionDailySummary bool   `json:"telegramTransactionDailySummary"`
	LineIsOn                        bool   `json:"lineIsOn"`
	LineNewMember                   bool   `json:"lineNewMember"`
	LineBeforeDeposit               bool   `json:"lineBeforeDeposit"`
	LineAfterDeposit                bool   `json:"lineAfterDeposit"`
	LineWithdrawSuccess             bool   `json:"lineWithdrawSuccess"`
	LineWithdrawPending             bool   `json:"lineWithdrawPending"`
	LineWithdrawFailed              bool   `json:"lineWithdrawFailed"`
	LinePullCredit                  bool   `json:"linePullCredit"`
	LineBonus                       bool   `json:"lineBonus"`
	LinePromotion                   bool   `json:"linePromotion"`
	LineActivityBeforeBonus         bool   `json:"lineActivityBeforeBonus"`
	LineActivityAfterBonus          bool   `json:"lineActivityAfterBonus"`
	LineMoveMoney                   bool   `json:"lineMoveMoney"`
	LineTransactionHourSummary      bool   `json:"lineTransactionHourSummary"`
	LineAffiliateDailySummary       bool   `json:"lineAffiliateDailySummary"`
	LineTransactionDailySummary     bool   `json:"lineTransactionDailySummary"`
}

type GetConfigurationExternalNotificationListRequest struct {
	NotificationName string `form:"notificationName"`
	Page             int    `form:"page" default:"1"`
	Limit            int    `form:"limit" default:"10"`
}

type GetConfigurationExternalNotificationListResponse struct {
	Id               int64  `json:"id"`
	NotificationName string `json:"notificationName"`
	TelegramCount    int64  `json:"telegramCount"`
	LineCount        int64  `json:"lineCount"`
}

type GetConfigurationExternalNotificationByIdResponse struct {
	Id                              int64                                       `json:"id"`
	NotificationName                string                                      `json:"notificationName"`
	TelegramIsOn                    bool                                        `json:"telegramIsOn"`
	TelegramNewMember               bool                                        `json:"telegramNewMember"`
	TelegramBeforeDeposit           bool                                        `json:"telegramBeforeDeposit"`
	TelegramAfterDeposit            bool                                        `json:"telegramAfterDeposit"`
	TelegramWithdrawSuccess         bool                                        `json:"telegramWithdrawSuccess"`
	TelegramWithdrawPending         bool                                        `json:"telegramWithdrawPending"`
	TelegramWithdrawFailed          bool                                        `json:"telegramWithdrawFailed"`
	TelegramPullCredit              bool                                        `json:"telegramPullCredit"`
	TelegramBonus                   bool                                        `json:"telegramBonus"`
	TelegramPromotion               bool                                        `json:"telegramPromotion"`
	TelegramActivityBeforeBonus     bool                                        `json:"telegramActivityBeforeBonus"`
	TelegramActivityAfterBonus      bool                                        `json:"telegramActivityAfterBonus"`
	TelegramMoveMoney               bool                                        `json:"telegramMoveMoney"`
	TelegramTransactionHourSummary  bool                                        `json:"telegramTransactionHourSummary"`
	TelegramAffiliateDailySummary   bool                                        `json:"telegramAffiliateDailySummary"`
	TelegramTransactionDailySummary bool                                        `json:"telegramTransactionDailySummary"`
	LineIsOn                        bool                                        `json:"lineIsOn"`
	LineNewMember                   bool                                        `json:"lineNewMember"`
	LineBeforeDeposit               bool                                        `json:"lineBeforeDeposit"`
	LineAfterDeposit                bool                                        `json:"lineAfterDeposit"`
	LineWithdrawSuccess             bool                                        `json:"lineWithdrawSuccess"`
	LineWithdrawPending             bool                                        `json:"lineWithdrawPending"`
	LineWithdrawFailed              bool                                        `json:"lineWithdrawFailed"`
	LinePullCredit                  bool                                        `json:"linePullCredit"`
	LineBonus                       bool                                        `json:"lineBonus"`
	LinePromotion                   bool                                        `json:"linePromotion"`
	LineActivityBeforeBonus         bool                                        `json:"lineActivityBeforeBonus"`
	LineActivityAfterBonus          bool                                        `json:"lineActivityAfterBonus"`
	LineMoveMoney                   bool                                        `json:"lineMoveMoney"`
	LineTransactionHourSummary      bool                                        `json:"lineTransactionHourSummary"`
	LineAffiliateDailySummary       bool                                        `json:"lineAffiliateDailySummary"`
	LineTransactionDailySummary     bool                                        `json:"lineTransactionDailySummary"`
	CreatedAt                       time.Time                                   `json:"createdAt"`
	UpdatedAt                       *time.Time                                  `json:"updatedAt"`
	DeletedAt                       *time.Time                                  `json:"deletedAt"`
	Tokens                          []GetConfigurationNotificationTokenResponse `json:"tokens"`
}
type GetConfigurationExternalNotificationByIdBody struct {
	Id                              int64      `json:"id"`
	NotificationName                string     `json:"notificationName"`
	TelegramIsOn                    bool       `json:"telegramIsOn"`
	TelegramNewMember               bool       `json:"telegramNewMember"`
	TelegramBeforeDeposit           bool       `json:"telegramBeforeDeposit"`
	TelegramAfterDeposit            bool       `json:"telegramAfterDeposit"`
	TelegramWithdrawSuccess         bool       `json:"telegramWithdrawSuccess"`
	TelegramWithdrawPending         bool       `json:"telegramWithdrawPending"`
	TelegramWithdrawFailed          bool       `json:"telegramWithdrawFailed"`
	TelegramPullCredit              bool       `json:"telegramPullCredit"`
	TelegramBonus                   bool       `json:"telegramBonus"`
	TelegramPromotion               bool       `json:"telegramPromotion"`
	TelegramActivityBeforeBonus     bool       `json:"telegramActivityBeforeBonus"`
	TelegramActivityAfterBonus      bool       `json:"telegramActivityAfterBonus"`
	TelegramMoveMoney               bool       `json:"telegramMoveMoney"`
	TelegramTransactionHourSummary  bool       `json:"telegramTransactionHourSummary"`
	TelegramAffiliateDailySummary   bool       `json:"telegramAffiliateDailySummary"`
	TelegramTransactionDailySummary bool       `json:"telegramTransactionDailySummary"`
	LineIsOn                        bool       `json:"lineIsOn"`
	LineNewMember                   bool       `json:"lineNewMember"`
	LineBeforeDeposit               bool       `json:"lineBeforeDeposit"`
	LineAfterDeposit                bool       `json:"lineAfterDeposit"`
	LineWithdrawSuccess             bool       `json:"lineWithdrawSuccess"`
	LineWithdrawPending             bool       `json:"lineWithdrawPending"`
	LineWithdrawFailed              bool       `json:"lineWithdrawFailed"`
	LinePullCredit                  bool       `json:"linePullCredit"`
	LineBonus                       bool       `json:"lineBonus"`
	LinePromotion                   bool       `json:"linePromotion"`
	LineActivityBeforeBonus         bool       `json:"lineActivityBeforeBonus"`
	LineActivityAfterBonus          bool       `json:"lineActivityAfterBonus"`
	LineMoveMoney                   bool       `json:"lineMoveMoney"`
	LineTransactionHourSummary      bool       `json:"lineTransactionHourSummary"`
	LineAffiliateDailySummary       bool       `json:"lineAffiliateDailySummary"`
	LineTransactionDailySummary     bool       `json:"lineTransactionDailySummary"`
	CreatedAt                       time.Time  `json:"createdAt"`
	UpdatedAt                       *time.Time `json:"updatedAt"`
	DeletedAt                       *time.Time `json:"deletedAt"`
}

type UpdateConfigurationExternalNotificationRequest struct {
	Id                              int64                                         `json:"-"`
	NotificationName                *string                                       `json:"notificationName"`
	TelegramIsOn                    *bool                                         `json:"telegramIsOn"`
	TelegramNewMember               *bool                                         `json:"telegramNewMember"`
	TelegramBeforeDeposit           *bool                                         `json:"telegramBeforeDeposit"`
	TelegramAfterDeposit            *bool                                         `json:"telegramAfterDeposit"`
	TelegramWithdrawSuccess         *bool                                         `json:"telegramWithdrawSuccess"`
	TelegramWithdrawPending         *bool                                         `json:"telegramWithdrawPending"`
	TelegramWithdrawFailed          *bool                                         `json:"telegramWithdrawFailed"`
	TelegramPullCredit              *bool                                         `json:"telegramPullCredit"`
	TelegramBonus                   *bool                                         `json:"telegramBonus"`
	TelegramPromotion               *bool                                         `json:"telegramPromotion"`
	TelegramActivityBeforeBonus     *bool                                         `json:"telegramActivityBeforeBonus"`
	TelegramActivityAfterBonus      *bool                                         `json:"telegramActivityAfterBonus"`
	TelegramMoveMoney               *bool                                         `json:"telegramMoveMoney"`
	TelegramTransactionHourSummary  *bool                                         `json:"telegramTransactionHourSummary"`
	TelegramAffiliateDailySummary   *bool                                         `json:"telegramAffiliateDailySummary"`
	TelegramTransactionDailySummary *bool                                         `json:"telegramTransactionDailySummary"`
	LineIsOn                        *bool                                         `json:"lineIsOn"`
	LineNewMember                   *bool                                         `json:"lineNewMember"`
	LineBeforeDeposit               *bool                                         `json:"lineBeforeDeposit"`
	LineAfterDeposit                *bool                                         `json:"lineAfterDeposit"`
	LineWithdrawSuccess             *bool                                         `json:"lineWithdrawSuccess"`
	LineWithdrawPending             *bool                                         `json:"lineWithdrawPending"`
	LineWithdrawFailed              *bool                                         `json:"lineWithdrawFailed"`
	LinePullCredit                  *bool                                         `json:"linePullCredit"`
	LineBonus                       *bool                                         `json:"lineBonus"`
	LinePromotion                   *bool                                         `json:"linePromotion"`
	LineActivityBeforeBonus         *bool                                         `json:"lineActivityBeforeBonus"`
	LineActivityAfterBonus          *bool                                         `json:"lineActivityAfterBonus"`
	LineMoveMoney                   *bool                                         `json:"lineMoveMoney"`
	LineTransactionHourSummary      *bool                                         `json:"lineTransactionHourSummary"`
	LineAffiliateDailySummary       *bool                                         `json:"lineAffiliateDailySummary"`
	LineTransactionDailySummary     *bool                                         `json:"lineTransactionDailySummary"`
	Tokens                          []UpdateConfigurationNotificationTokenRequest `json:"tokens"`
}
type UpdateConfigurationExternalNotificationBody struct {
	Id                              int64   `json:"-"`
	NotificationName                *string `json:"notificationName"`
	TelegramIsOn                    *bool   `json:"telegramIsOn"`
	TelegramNewMember               *bool   `json:"telegramNewMember"`
	TelegramBeforeDeposit           *bool   `json:"telegramBeforeDeposit"`
	TelegramAfterDeposit            *bool   `json:"telegramAfterDeposit"`
	TelegramWithdrawSuccess         *bool   `json:"telegramWithdrawSuccess"`
	TelegramWithdrawPending         *bool   `json:"telegramWithdrawPending"`
	TelegramWithdrawFailed          *bool   `json:"telegramWithdrawFailed"`
	TelegramPullCredit              *bool   `json:"telegramPullCredit"`
	TelegramBonus                   *bool   `json:"telegramBonus"`
	TelegramPromotion               *bool   `json:"telegramPromotion"`
	TelegramActivityBeforeBonus     *bool   `json:"telegramActivityBeforeBonus"`
	TelegramActivityAfterBonus      *bool   `json:"telegramActivityAfterBonus"`
	TelegramMoveMoney               *bool   `json:"telegramMoveMoney"`
	TelegramTransactionHourSummary  *bool   `json:"telegramTransactionHourSummary"`
	TelegramAffiliateDailySummary   *bool   `json:"telegramAffiliateDailySummary"`
	TelegramTransactionDailySummary *bool   `json:"telegramTransactionDailySummary"`
	LineIsOn                        *bool   `json:"lineIsOn"`
	LineNewMember                   *bool   `json:"lineNewMember"`
	LineBeforeDeposit               *bool   `json:"lineBeforeDeposit"`
	LineAfterDeposit                *bool   `json:"lineAfterDeposit"`
	LineWithdrawSuccess             *bool   `json:"lineWithdrawSuccess"`
	LineWithdrawPending             *bool   `json:"lineWithdrawPending"`
	LineWithdrawFailed              *bool   `json:"lineWithdrawFailed"`
	LinePullCredit                  *bool   `json:"linePullCredit"`
	LineBonus                       *bool   `json:"lineBonus"`
	LinePromotion                   *bool   `json:"linePromotion"`
	LineActivityBeforeBonus         *bool   `json:"lineActivityBeforeBonus"`
	LineActivityAfterBonus          *bool   `json:"lineActivityAfterBonus"`
	LineMoveMoney                   *bool   `json:"lineMoveMoney"`
	LineTransactionHourSummary      *bool   `json:"lineTransactionHourSummary"`
	LineAffiliateDailySummary       *bool   `json:"lineAffiliateDailySummary"`
	LineTransactionDailySummary     *bool   `json:"lineTransactionDailySummary"`
}

type UpdateConfigurationExternalNotificationByIdRequest struct {
	Id int64 `uri:"id" binding:"required"`
}

type BankLimitConfiguration struct {
	Id                    int64   `json:"id"`
	MaxUserWithdrawAmount float64 `json:"maxUserWithdrawAmount"`
	MaxUserWithdrawCount  int64   `json:"maxUserWithdrawCount"`
}
type BankLimitConfigurationResponse struct {
	Id                    int64   `json:"id"`
	MaxUserWithdrawAmount float64 `json:"maxUserWithdrawAmount"`
	MaxUserWithdrawCount  int64   `json:"maxUserWithdrawCount"`
}
type CreateBankLimitConfigurationBody struct {
	Id                    *int64  `json:"id"`
	MaxUserWithdrawAmount float64 `json:"maxUserWithdrawAmount"`
	MaxUserWithdrawCount  int64   `json:"maxUserWithdrawCount"`
}
type UpdateBankLimitConfigurationRequest struct {
	MaxUserWithdrawAmount *float64 `json:"maxUserWithdrawAmount"`
	MaxUserWithdrawCount  *int64   `json:"maxUserWithdrawCount"`
	UpdateBy              int64    `json:"-"`
}
type UpdateBankLimitConfigurationBody struct {
	Id                    int64    `json:"id"`
	MaxUserWithdrawAmount *float64 `json:"maxUserWithdrawAmount"`
	MaxUserWithdrawCount  *int64   `json:"maxUserWithdrawCount"`
}
type UserBankLimitResponse struct {
	UserId                int64   `json:"userId"`
	CurrentWithdrawAmount float64 `json:"currentWithdrawAmount"`
	MaxUserWithdrawAmount float64 `json:"maxUserWithdrawAmount"`
	CurrentWithdrawCount  int64   `json:"currentWithdrawCount"`
	MaxUserWithdrawCount  int64   `json:"maxUserWithdrawCount"`
}

type GetAgentGamePrioritySettingListRequest struct {
	Search       string `form:"search"`
	Agent        string `form:"agent"`
	CategoryName string `form:"categoryName"`
	Limit        int    `form:"limit"`
	Page         int    `form:"page"`
}

type AgentGamePriorityListSetting struct {
	Id            int64      `json:"id"`
	VendorCode    string     `json:"vendorCode"`
	Detail        string     `json:"detail"`
	PriorityOrder int64      `json:"priorityOrder"`
	Agent         string     `json:"agent"`
	CategoryName  string     `json:"categoryName"`
	IsPopular     int        `json:"isPopular"`
	IsShow        int        `json:"isShow"`
	TotalPlayed   int64      `json:"totalPlayed"`
	LastPlayedAt  string     `json:"lastPlayedAt"`
	CreatedAt     time.Time  `json:"createdAt"`
	UpdatedAt     *time.Time `json:"updatedAt"`
	UpdatedByID   *int64     `json:"updatedByID"`
	UpdatedByName string     `json:"updatedByName"`
}
type AgentGamePriority struct {
	Id           int64  `json:"id"`
	VendorCode   string `json:"vendorCode"`
	Detail       string `json:"detail"`
	CategoryName string `json:"categoryName"`
	ImageName    string `json:"imageName"`
	IsPopular    int    `json:"isPopular"`
	IsShow       int    `json:"isShow"`
	TotalPlayed  int64  `json:"totalPlayed"`
}

type UpdateAgentGamePrioritySettingBody struct {
	Id          int64 `json:"-"`
	IsPopular   *int  `json:"isPopular"`
	IsShow      *int  `json:"isShow"`
	UpdatedByID int64 `json:"-"`
}

type BannerSetting struct {
	Id            int64      `json:"id"`
	Lang          string     `json:"lang"`
	ImageUrl      string     `json:"imageUrl"`
	LinkUrl       string     `json:"linkUrl"`
	IsShowPublic  int        `json:"isShowPublic"`
	IsShowLogedin int        `json:"isShowLogedin"`
	CreatedAt     time.Time  `json:"createdAt"`
	CreatedByID   int64      `json:"createdByID"`
	UpdatedAt     time.Time  `json:"updatedAt"`
	UpdatedByID   *int64     `json:"updatedByID"`
	DeletedAt     *time.Time `json:"deletedAt"`
	DeletedByID   *int64     `json:"deletedByID"`
}

type CreateBannerSettingRequest struct {
	Lang          string `json:"lang"`
	ImageUrl      string `json:"imageUrl"`
	LinkUrl       string `json:"linkUrl"`
	IsShowPublic  int    `json:"isShowPublic"`
	IsShowLogedin int    `json:"isShowLogedin"`
	CreatedByID   int64  `json:"-"`
}
type CreateBannerSettingBody struct {
	Lang          string `json:"lang"`
	ImageUrl      string `json:"imageUrl"`
	LinkUrl       string `json:"linkUrl"`
	IsShowPublic  int    `json:"isShowPublic"`
	IsShowLogedin int    `json:"isShowLogedin"`
	CreatedByID   int64  `json:"-"`
}
type BannerSettingUpdateRequest struct {
	ImageUrl      *string `json:"imageUrl"`
	LinkUrl       *string `json:"linkUrl"`
	IsShowPublic  *int    `json:"isShowPublic"`
	IsShowLogedin *int    `json:"isShowLogedin"`
}
type BannerSettingUpdateBody struct {
	ImageUrl      *string `json:"imageUrl"`
	LinkUrl       *string `json:"linkUrl"`
	IsShowPublic  *int    `json:"isShowPublic"`
	IsShowLogedin *int    `json:"isShowLogedin"`
}

type GetBannerSettingListRequest struct {
	Lang     string `form:"lang"`
	ShowMode string `form:"showMode" json:"-"`
}

type GetBannerSettingListResponse struct {
	Id            int64  `json:"id"`
	Lang          string `json:"lang"`
	ImageUrl      string `json:"imageUrl"`
	LinkUrl       string `json:"linkUrl"`
	IsShowPublic  int    `json:"isShowPublic"`
	IsShowLogedin int    `json:"isShowLogedin"`
}

type DeleteBannerSettingRequest struct {
	Id          []int64   `json:"id"`
	DeletedAt   time.Time `json:"-"`
	DeletedByID int64     `json:"-"`
}
type DeleteBannerSettingBody struct {
	Id          int64     `json:"id"`
	DeletedAt   time.Time `json:"-"`
	DeletedByID int64     `json:"-"`
}

type ConfigurationCheckShowTransactionResponse struct {
	IsShowDeposit  bool `json:"isShowDeposit"`
	IsShowWithdraw bool `json:"isShowWithdraw"`
}

type UpdateActivityMenuRequest struct {
	Id          int64      `json:"-"`
	Name        *string    `json:"name"`
	Description *string    `json:"description"`
	ImageUrl    *string    `json:"imageUrl"`
	UpdatedAt   *time.Time `json:"-"`
	UpdatedByID *int64     `json:"-"`
}
type GetActivityMenuRequest struct {
	Lang string `form:"lang"`
}
type GetActivityMenuResponse struct {
	Id           int64  `json:"id"`
	Name         string `json:"name"`
	Description  string `json:"description"`
	ImageUrl     string `json:"imageUrl"`
	LabelTh      string `json:"labelTh"`
	LabelEn      string `json:"labelEn"`
	ActivityType string `json:"activityType"`
	Lang         string `json:"lang"`
	SortOrder    int64  `json:"sortOrder"`
}

type GetActivityMenuItemRequest struct {
	Lang         string `form:"lang"`
	ActivityType string `form:"activityType"`
}

type RunningMessageResponse struct {
	Message string `json:"message"`
}
