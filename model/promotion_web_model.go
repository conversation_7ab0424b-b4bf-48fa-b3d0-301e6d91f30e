package model

import "time"

// PROMOTION_WEB_OPTION_MODEL
const (
	PROMOTION_WEB_USER_STATUS_ON_PROCESS  = int64(1)
	PROMOTION_WEB_USER_STATUS_CANCELED    = int64(3)
	PROMOTION_WEB_USER_STATUS_SUCCESS     = int64(2)
	PROMOTION_WEB_USER_STATUS_ON_WITHDRAW = int64(4)
)

// const (
// 	PROMOTION_WEB_TURNOVER_TYPE_ALL    = int64(1)
// 	PROMOTION_WEB_TURNOVER_TYPE_SPORT  = int64(2)
// 	PROMOTION_WEB_TURNOVER_TYPE_CASINO = int64(3)
// 	PROMOTION_WEB_TURNOVER_TYPE_SLOT   = int64(4)
// )

const (
	PROMOTION_WEB_BONUS_TYPE_PERCENT    = int64(1)
	PROMOTION_WEB_BONUS_TYPE_FIXED_RATE = int64(2)
)

const (
	PROMOTION_WEB_STATUS_DISABLE_WEB = int64(1)
	PROMOTION_WEB_STATUS_ACTIVE      = int64(2)
	PROMOTION_WEB_STATUS_CANCELED    = int64(3)
	PROMOTION_WEB_STATUS_ONLY_SHOW   = int64(4)
	PROMOTION_WEB_STATUS_ONLY_URL    = int64(5)
)

const (
	PROMOTION_WEB_TYPE_NEW_MEMBER_FREE         = int64(1) // [สม้ครใหม่แจกฟรี]  โปรโมชั่นสำหรับสมาชิกใหม่ แจกฟรี
	PROMOTION_WEB_TYPE_NEW_MEMBER_CONDITION    = int64(2) // [สมัครใหม่ตามเงื่อนไข]  โปรโมชั่นสำหรับสมาชิกใหม่ แจกฟรี ตามทำเงื่อนไข
	PROMOTION_WEB_TYPE_DEPOSIT_MINIMUM_PER_DAY = int64(3) // [ฝากขั้นต่ำต่อวัน]  โปรโมชั่นฝากขั้นต่ำต่อวัน
	PROMOTION_WEB_TYPE_FIRST_DEPOSIT           = int64(4) // [ฝากครั้งแรก]  โปรโมชั่นฝากครั้งแรก
	PROMOTION_WEB_TYPE_DEPOSIT_PER_DAY         = int64(5) // [ฝากทั้งวัน] โปรโมชั่นฝากทั้งวัน
	PROMOTION_WEB_TYPE_DEPOSIT_BY_TIME         = int64(6) // [ฝากตามช่วงเวลา]  โปรโมชั่นฝากตามช่วงเวลา
	PROMOTION_WEB_TYPE_FIRST_DEPOSIT_OF_DAY    = int64(7) // [ฝากครั้งแรกของวัน]  โปรโมชั่นฝากครั้งแรกของวัน
)

const (
	PROMOTION_WEB_DATE_TYPE_FIXED_DATE     = int64(1)
	PROMOTION_WEB_DATE_TYPE_NON_FIXED_DATE = int64(2)
)

const (
	NOT_PASS_PROMOTION = "NOT_PASS_PROMOTION"
	PASS_PROMOTION     = "PASS_PROMOTION"
	PASS_TO_WITHDRAW   = "PASS_TO_WITHDRAW"

	CONTINUE_TO_WITHDRAW = "CONTINUE_TO_WITHDRAW"
	NOT_PASS_TO_WITHDRAW = "NOT_PASS_TO_WITHDRAW"
)

const (
	PROMOTION_WEB_BONUS_CONDITION_MORE_THAN_OR_EQUAL = int64(1)
	PROMOTION_WEB_BONUS_CONDITION_LESS_THAN_OR_EQUAL = int64(2)
)

const (
	PROMOTION_WEB_TURN_OVER_TYPE_ALL       = int64(1)
	PROMOTION_WEB_TURN_OVER_TYPE_SPORT     = int64(2)
	PROMOTION_WEB_TURN_OVER_TYPE_CASINO    = int64(3)
	PROMOTION_WEB_TURN_OVER_TYPE_SLOT      = int64(4)
	PROMOTION_WEB_TURN_OVER_TYPE_P2P       = int64(5)
	PROMOTION_WEB_TURN_OVER_TYPE_LOTTERY   = int64(6)
	PROMOTION_WEB_TURN_OVER_TYPE_FINANCIAL = int64(7)
)

const (
	PROMOTION_WEB_REGISTER_MEMBER_STATUS_REGISTER_PENDING = int64(1)
	PROMOTION_WEB_REGISTER_MEMBER_STATUS_REGISTER_CONFIRM = int64(2)
	PROMOTION_WEB_REGISTER_MEMBER_STATUS_REGISTER_REJECT  = int64(3)
)

const (
	USER_WITHDRAW_LOCK_CREDIT_TYPE_PROMOTION = int64(1)
)

type PromotionWebTypeResponse struct {
	Id      int64  `json:"id"`
	Name    string `json:"name"`
	LabelTh string `json:"labelTh"`
	LabelEn string `json:"labelEn"`
}

type PromotionWebStatusResponse struct {
	Id      int64  `json:"id"`
	Name    string `json:"name"`
	LabelTh string `json:"labelTh"`
	LabelEn string `json:"labelEn"`
}

type PromotionWebBonusConditionResponse struct {
	Id      int64  `json:"id"`
	Syntax  string `json:"syntax"`
	Name    string `json:"name"`
	LabelTh string `json:"labelTh"`
	LabelEn string `json:"labelEn"`
}

type PromotionWebBonusTypeResponse struct {
	Id      int64  `json:"id"`
	Name    string `json:"name"`
	LabelTh string `json:"labelTh"`
	LabelEn string `json:"labelEn"`
}

type PromotionWebTurnoverTypeResponse struct {
	Id      int64  `json:"id"`
	Name    string `json:"name"`
	LabelTh string `json:"labelTh"`
	LabelEn string `json:"labelEn"`
}
type GetpromotionWebDateTypeResponse struct {
	Id      int64  `json:"id"`
	Name    string `json:"name"`
	LabelTh string `json:"labelTh"`
	LabelEn string `json:"labelEn"`
}

type GetPromotionWebUserStatus struct {
	Id      int64  `json:"id"`
	Name    string `json:"name"`
	LabelTh string `json:"labelTh"`
	LabelEn string `json:"labelEn"`
}

type PromotionWeb struct {
	Id                           int64   `json:"id"`
	PromotionWebTypeId           int64   `json:"promotionWebTypeId"`
	PromotionWebStatusId         int64   `json:"promotionWebStatusId"`
	ConditionDetail              string  `json:"conditionDetail"`
	ImageUrl                     string  `json:"imageUrl"`
	Name                         string  `json:"name"`
	ShortDescription             string  `json:"shortDescription"`
	Description                  string  `json:"description"`
	PromotionWebDateTypeId       int64   `json:"promotionWebDateTypeId"`
	StartDate                    string  `json:"startDate"`
	EndDate                      string  `json:"endDate"`
	FreeBonusAmount              float64 `json:"freeBonusAmount"`
	PrivilegePerDay              int64   `json:"privilegePerDay"`
	AbleWithdrawMorethan         float64 `json:"ableWithdrawMorethan"`
	PromotionWebBonusConditionId int64   `json:"promotionWebBonusConditionId"`
	BonusConditionAmount         float64 `json:"bonusConditionAmount"`
	PromotionWebBonusTypeId      int64   `json:"promotionWebBonusTypeId"`
	BonusTypeAmount              float64 `json:"bonusTypeAmount"`
	BonusTypeAmountMax           float64 `json:"bonusTypeAmountMax"`
	AbleWithdrawPertime          float64 `json:"ableWithdrawPertime"`
	PromotionWebTurnoverTypeId   int64   `json:"promotionWebTurnoverTypeId"`
	TurnoverAmount               float64 `json:"turnoverAmount"`
	Monday                       bool    `json:"monday"`
	Tuesday                      bool    `json:"tuesday"`
	Wednesday                    bool    `json:"wednesday"`
	Thursday                     bool    `json:"thursday"`
	Friday                       bool    `json:"friday"`
	Saturday                     bool    `json:"saturday"`
	Sunday                       bool    `json:"sunday"`
	TimeStart                    string  `json:"timeStart"`
	TimeEnd                      string  `json:"timeEnd"`
	CreatedAt                    string  `json:"createdAt"`
	UpdatedAt                    string  `json:"updatedAt"`
	DeletedAt                    string  `json:"deletedAt"`
}

type PromotionWebCreateRequest struct {
	Id                           int64     `json:"-"`
	PromotionWebTypeId           int64     `json:"promotionWebTypeId" binding:"required"`
	PromotionWebStatusId         int64     `json:"promotionWebStatusId" binding:"required"`
	ConditionDetail              string    `json:"conditionDetail"`
	ImageUrl                     string    `json:"imageUrl"`
	Name                         string    `json:"name" binding:"required"`
	ShortDescription             string    `json:"shortDescription" binding:"required"`
	Description                  string    `json:"description" binding:"required"`
	PromotionWebDateTypeId       int64     `json:"promotionWebDateTypeId" binding:"required"`
	StartDate                    *string   `json:"startDate"`
	EndDate                      *string   `json:"endDate"`
	FreeBonusAmount              float64   `json:"freeBonusAmount"`
	PrivilegePerDay              int64     `json:"privilegePerDay"`
	AbleWithdrawMorethan         float64   `json:"ableWithdrawMorethan"`
	PromotionWebBonusConditionId *int64    `json:"promotionWebBonusConditionId"`
	BonusConditionAmount         float64   `json:"bonusConditionAmount"`
	PromotionWebBonusTypeId      *int64    `json:"promotionWebBonusTypeId"`
	BonusTypeAmount              float64   `json:"bonusTypeAmount"`
	BonusTypeAmountMax           float64   `json:"bonusTypeAmountMax"`
	AbleWithdrawPertime          float64   `json:"ableWithdrawPertime"`
	PromotionWebTurnoverTypeId   *int64    `json:"promotionWebTurnoverTypeId"`
	TurnoverAmount               float64   `json:"turnoverAmount"`
	Monday                       bool      `json:"monday"`
	Tuesday                      bool      `json:"tuesday"`
	Wednesday                    bool      `json:"wednesday"`
	Thursday                     bool      `json:"thursday"`
	Friday                       bool      `json:"friday"`
	Saturday                     bool      `json:"saturday"`
	Sunday                       bool      `json:"sunday"`
	TimeStart                    *string   `json:"timeStart"`
	TimeEnd                      *string   `json:"timeEnd"`
	HiddenUrlLink                string    `json:"hiddenUrlLink"`
	CreatedByAdminId             int64     `json:"-"`
	UpdatedAt                    time.Time `json:"-"`
}

type PromotionWebGetByIdRequest struct {
	Id int64 `uri:"id" binding:"required"`
}
type ShowPromotionWebForUserById struct {
	Id int64 `uri:"id" binding:"required"`
}
type PromotionWebUserGetByIdRequest struct {
	Id int64 `uri:"id" binding:"required"`
}

// [2023/12/13] เปลี่ยนแบบเอา ครบทุกอัน ไม่ต้องเลิอก
type PromotionWebOptionRequest struct {
	Selected string `form:"selected"`
}

type PromotionWebGetByIdResponse struct {
	Id                               int64   `json:"id"`
	PromotionWebTypeId               int64   `json:"promotionWebTypeId"`
	PromotionWebTypeTh               string  `json:"promotionWebTypeTh"`
	PromotionWebStatusId             int64   `json:"promotionWebStatusId"`
	PromotionWebStatusTh             string  `json:"promotionWebStatusTh"`
	ConditionDetail                  string  `json:"conditionDetail"`
	ImageUrl                         string  `json:"imageUrl"`
	Name                             string  `json:"name"`
	ShortDescription                 string  `json:"shortDescription"`
	Description                      string  `json:"description"`
	PromotionWebDateTypeId           int64   `json:"promotionWebDateTypeId"`
	StartDate                        string  `json:"startDate"`
	EndDate                          string  `json:"endDate"`
	FreeBonusAmount                  float64 `json:"freeBonusAmount"`
	PrivilegePerDay                  int64   `json:"privilegePerDay"`
	AbleWithdrawMorethan             float64 `json:"ableWithdrawMorethan"`
	PromotionWebBonusConditionId     int64   `json:"promotionWebBonusConditionId"`
	PromotionWebBonusConditionTh     string  `json:"promotionWebBonusConditionTh"`
	PromotionWebBonusConditionSyntax string  `json:"promotionWebBonusConditionSyntax"`
	BonusConditionAmount             float64 `json:"bonusConditionAmount"`
	PromotionWebBonusTypeId          int64   `json:"promotionWebBonusTypeId"`
	PromotionWebBonusTypeTh          string  `json:"promotionWebBonusTypeTh"`
	BonusTypeAmount                  float64 `json:"bonusTypeAmount"`
	BonusTypeAmountMax               float64 `json:"bonusTypeAmountMax"`
	AbleWithdrawPertime              float64 `json:"ableWithdrawPertime"`
	PromotionWebTurnoverTypeId       int64   `json:"promotionWebTurnoverTypeId"`
	PromotionWebTurnoverTypeTh       string  `json:"promotionWebTurnoverTypeTh"`
	TurnoverAmount                   float64 `json:"turnoverAmount"`
	Monday                           bool    `json:"monday"`
	Tuesday                          bool    `json:"tuesday"`
	Wednesday                        bool    `json:"wednesday"`
	Thursday                         bool    `json:"thursday"`
	Friday                           bool    `json:"friday"`
	Saturday                         bool    `json:"saturday"`
	Sunday                           bool    `json:"sunday"`
	TimeStart                        string  `json:"timeStart"`
	TimeEnd                          string  `json:"timeEnd"`
}

type PromotionWebGetInternalListRequest struct {
	Page      int    `form:"page" default:"1"`
	Limit     int    `form:"limit" default:"10"`
	StartDate string `form:"startDate" time_format:"2006-01-02"`
	EndDate   string `form:"end" time_format:"2006-01-02"`
	Search    string `form:"search"`
}
type PromotionWebGetInternalListResponse struct {
	Id                               int64   `json:"id"`
	PromotionWebTypeId               int64   `json:"promotionWebTypeId"`
	PromotionWebTypeTh               string  `json:"promotionWebTypeTh"`
	PromotionWebStatusId             int64   `json:"promotionWebStatusId"`
	PromotionWebStatusTh             string  `json:"promotionWebStatusTh"`
	ConditionDetail                  string  `json:"conditionDetail"`
	ImageUrl                         string  `json:"imageUrl"`
	Name                             string  `json:"name"`
	ShortDescription                 string  `json:"shortDescription"`
	Description                      string  `json:"description"`
	PromotionWebDateTypeId           int64   `json:"promotionWebDateTypeId"`
	StartDate                        string  `json:"startDate"`
	EndDate                          string  `json:"endDate"`
	FreeBonusAmount                  float64 `json:"freeBonusAmount"`
	PrivilegePerDay                  int64   `json:"privilegePerDay"`
	AbleWithdrawMorethan             float64 `json:"ableWithdrawMorethan"`
	PromotionWebBonusConditionId     int64   `json:"promotionWebBonusConditionId"`
	PromotionWebBonusConditionTh     string  `json:"promotionWebBonusConditionTh"`
	PromotionWebBonusConditionSyntax string  `json:"promotionWebBonusConditionSyntax"`
	BonusConditionAmount             float64 `json:"bonusConditionAmount"`
	PromotionWebBonusTypeId          int64   `json:"promotionWebBonusTypeId"`
	PromotionWebBonusTypeTh          string  `json:"promotionWebBonusTypeTh"`
	BonusTypeAmount                  float64 `json:"bonusTypeAmount"`
	BonusTypeAmountMax               float64 `json:"bonusTypeAmountMax"`
	AbleWithdrawPertime              float64 `json:"ableWithdrawPertime"`
	PromotionWebTurnoverTypeId       int64   `json:"promotionWebTurnoverTypeId"`
	PromotionWebTurnoverTypeTh       string  `json:"promotionWebTurnoverTypeTh"`
	TurnoverAmount                   float64 `json:"turnoverAmount"`
	Monday                           bool    `json:"monday"`
	Tuesday                          bool    `json:"tuesday"`
	Wednesday                        bool    `json:"wednesday"`
	Thursday                         bool    `json:"thursday"`
	Friday                           bool    `json:"friday"`
	Saturday                         bool    `json:"saturday"`
	Sunday                           bool    `json:"sunday"`
	TimeStart                        string  `json:"timeStart"`
	TimeEnd                          string  `json:"timeEnd"`
}

type PromotionWebUpdateRequest struct {
	Id                           int64    `json:"-"`
	PromotionWebTypeId           *int64   `json:"promotionWebTypeId"`
	PromotionWebStatusId         *int64   `json:"promotionWebStatusId"`
	ConditionDetail              *string  `json:"conditionDetail"`
	ImageUrl                     *string  `json:"imageUrl"`
	Name                         *string  `json:"name"`
	ShortDescription             *string  `json:"shortDescription"`
	Description                  *string  `json:"description"`
	PromotionWebDateTypeId       int64    `json:"promotionWebDateTypeId"`
	StartDate                    *string  `json:"startDate"`
	EndDate                      *string  `json:"endDate"`
	FreeBonusAmount              *float64 `json:"freeBonusAmount"`
	PrivilegePerDay              *int64   `json:"privilegePerDay"`
	AbleWithdrawMorethan         *float64 `json:"ableWithdrawMorethan"`
	PromotionWebBonusConditionId *int64   `json:"promotionWebBonusConditionId"`
	BonusConditionAmount         *float64 `json:"bonusConditionAmount"`
	PromotionWebBonusTypeId      *int64   `json:"promotionWebBonusTypeId"`
	BonusTypeAmount              *float64 `json:"bonusTypeAmount"`
	BonusTypeAmountMax           *float64 `json:"bonusTypeAmountMax"`
	AbleWithdrawPertime          *float64 `json:"ableWithdrawPertime"`
	PromotionWebTurnoverTypeId   *int64   `json:"promotionWebTurnoverTypeId"`
	TurnoverAmount               *float64 `json:"turnoverAmount"`
	Monday                       *bool    `json:"monday"`
	Tuesday                      *bool    `json:"tuesday"`
	Wednesday                    *bool    `json:"wednesday"`
	Thursday                     *bool    `json:"thursday"`
	Friday                       *bool    `json:"friday"`
	Saturday                     *bool    `json:"saturday"`
	Sunday                       *bool    `json:"sunday"`
	TimeStart                    *string  `json:"timeStart"`
	TimeEnd                      *string  `json:"timeEnd"`
	HiddenUrlLink                *string  `json:"hiddenUrlLink"`
	UpdatedByAdminId             int64    `json:"-"`
}

type CancelPromotionWebRequest struct {
	Id                   int64     `json:"id"`
	CanceledByAdminId    int64     `json:"-"`
	CanceledAt           time.Time `json:"-"`
	PromotionWebStatusId int64     `json:"-"`
}
type DeletePromotionWebRequest struct {
	Id                   int64     `json:"id"`
	DeletedByAdminId     int64     `json:"-"`
	DeletedAt            time.Time `json:"-"`
	PromotionWebStatusId int64     `json:"-"`
}

type PromotionWebGetListRequest struct {
	Page                 int    `form:"page" default:"1"`
	Limit                int    `form:"limit" default:"10"`
	StartDate            string `form:"startDate" time_format:"2006-01-02"`
	EndDate              string `form:"end" time_format:"2006-01-02"`
	Search               string `form:"search"`
	PromotionWebStatusId *int64 `form:"promotionWebStatusId"`
}

type PromotionWebGetListResponse struct {
	Id                     int64     `json:"id"`
	PromotionWebTypeId     int64     `json:"promotionWebTypeId"`
	PromotionWebTypeTh     string    `json:"promotionWebTypeTh"`
	PromotionWebStatusId   int64     `json:"promotionWebStatusId"`
	PromotionWebStatusTh   string    `json:"promotionWebStatusTh"`
	Name                   string    `json:"name"`
	PromotionWebDateTypeId int64     `json:"promotionWebDateTypeId"`
	StartDate              string    `json:"startDate"`
	EndDate                string    `json:"endDate"`
	TimeStart              string    `json:"timeStart"`
	TimeEnd                string    `json:"timeEnd"`
	CreatedByAdminId       int64     `json:"createdByAdminId"`
	CreatedByAdminName     string    `json:"createdByAdminName"`
	UpdatedByAdminId       *int64    `json:"updatedByAdminId"`
	UpdatedByAdminName     string    `json:"updatedByAdminName"`
	CanceledByAdminId      *int64    `json:"canceledByAdminId"`
	CanceledByAdminName    string    `json:"canceledByAdminName"`
	HiddenUrlLink          string    `json:"hiddenUrlLink"`
	UpdatedAt              time.Time `json:"updatedAt"`
}

type PromotionWebUser struct {
	Id                       int64     `json:"id"`
	PromotionWebId           int64     `json:"promotionWebId"`
	UserId                   int64     `json:"userId"`
	PromotionWebUserStatusId int64     `json:"promotionWebUserStatusId"`
	TotalAmount              float64   `json:"totalAmount"`
	CanceledByAdminId        int64     `json:"canceledByAdminId"`
	CreatedAt                time.Time `json:"createdAt"`
	UpdatedAt                time.Time `json:"updatedAt"`
	DeletedAt                time.Time `json:"deletedAt"`
}

type PromotionWebUserCreateRequest struct {
	PromotionWebId int64  `json:"promotionWebId" binding:"required"`
	UserId         int64  `json:"-"`
	Newmember      *int64 `json:"-"`
}

type PromotionWebUserCreateBody struct {
	Id                       int64   `json:"-"`
	PromotionWebId           int64   `json:"promotionWebId"`
	UserId                   int64   `json:"userId"`
	PromotionWebUserStatusId int64   `json:"promotionWebUserStatusId"`
	TotalAmount              float64 `json:"totalAmount"`
	TotalDepositAmount       float64 `json:"totalDepositAmount"`
}

type PromotionWebUserByUserIdResponse struct {
	Id                       int64     `json:"id"`
	PromotionWebId           int64     `json:"promotionWebId"`
	PromotionName            string    `json:"promotionName"`
	UserId                   int64     `json:"userId"`
	MemberCode               string    `json:"memberCode"`
	FullName                 string    `json:"fullName"`
	Phone                    string    `json:"phone"`
	PromotionWebUserStatusId int64     `json:"promotionWebUserStatusId"`
	PromotionWebUserStatusTh string    `json:"promotionWebUserStatusTh"`
	TotalAmount              float64   `json:"totalAmount"`
	CreatedAt                time.Time `json:"createdAt"`
}
type PromotionWebUserGetListResponse struct {
	Id                       int64      `json:"id"`
	PromotionWebId           int64      `json:"promotionWebId"`
	PromotionName            string     `json:"promotionName"`
	UserId                   int64      `json:"userId"`
	MemberCode               string     `json:"memberCode"`
	FullName                 string     `json:"fullName"`
	Phone                    string     `json:"phone"`
	PromotionWebUserStatusId int64      `json:"promotionWebUserStatusId"`
	PromotionWebUserStatusTh string     `json:"promotionWebUserStatusTh"`
	TotalAmount              float64    `json:"totalAmount"`
	IsLocked                 bool       `json:"isLocked"`
	AbleWithdrawPertime      float64    `json:"ableWithdrawPertime"`
	AbleWithdrawMorethan     float64    `json:"ableWithdrawMorethan"`
	CreatedAt                time.Time  `json:"createdAt"`
	CanceledByAdminId        *int64     `json:"canceledByAdminId"`
	CanceledByAdminName      *string    `json:"canceledByAdminName"`
	CanceledAt               *time.Time `json:"canceledAt"`
	ApproveCreditByAdminId   *int64     `json:"approveCreditByAdminId"`
	ApproveCreditByAdminName string     `json:"approveCreditByAdminName"`
	ApproveCreditAt          *time.Time `json:"approveCreditAt"`
}

type PromotionWebUserByUserIdRequest struct {
	UserId int64 `uri:"userId" binding:"required"`
}

type PromotionWebUserGetListRequest struct {
	PromotionWebId           *int64 `form:"promotionWebId"`
	Page                     int    `form:"page" default:"1"`
	Limit                    int    `form:"limit" default:"10"`
	StartDate                string `form:"startDate" time_format:"2006-01-02"`
	EndDate                  string `form:"end" time_format:"2006-01-02"`
	PromotionWebUserStatusId *int64 `form:"promotionWebUserStatusId"`
	Search                   string `form:"search"`
	TypeList                 string `form:"typeList"`
}

type CancelPromotionWebUserByPromotionWebId struct {
	PromotionWebId    int64     `json:"promotionWebId"`
	CanceledByAdminId int64     `json:"canceledByAdminId"`
	CanceledAt        time.Time `json:"canceledAt"`
}

type SuccessPromotionWebUserByPromotionWebId struct {
	PromotionWebId int64 `json:"promotionWebId"`
}
type CancelPromotionWebUserToSuccess struct {
	Id                int64     `json:"id"`
	PromotionWebId    int64     `json:"promotionWebId"`
	CanceledByAdminId int64     `json:"canceledByAdminId"`
	CanceledAt        time.Time `json:"canceledAt"`
}
type CancelPromotionWebUserById struct {
	Id                int64 `json:"-"`
	CanceledByAdminId int64 `json:"canceledByAdminId"`
}

type GetPromtionWebIdToCancel struct {
	Id                       int64 `json:"id"`
	PromotionWebUserStatusId int64 `json:"promotionWebUserStatusId"`
}

type PromotionWebUserConfirm struct {
	Id                 int64     `json:"id"`
	ActionKey          string    `json:"actionKey"`
	PromotionWebId     int64     `json:"promotionWebId"`
	UserId             int64     `json:"userId"`
	PromotionWebUserId int64     `json:"promotionWebUserId"`
	CreatedAt          time.Time `json:"createdAt"`
	UpdatedAt          time.Time `json:"updatedAt"`
	DeletedAt          time.Time `json:"deletedAt"`
}

type PromotionWebUserConfirmCreateRequest struct {
	Id             int64  `json:"-"`
	ActionKey      string `json:"actionKey" binding:"required"`
	PromotionWebId int64  `json:"promotionWebId" binding:"required"`
	UserId         int64  `json:"userId" binding:"required"`
}

type PromotionWebUserConfirmUpdateRequest struct {
	Id        int64  `json:"-"`
	ActionKey string `json:"actionKey" binding:"required"`
}

type ShowPromotionWebForUserResponse struct {
	Id                      int64  `json:"id"`
	PromotionWebTypeId      int64  `json:"promotionWebTypeId"`
	PromotionWebTypeTh      string `json:"promotionWebTypeTh"`
	PromotionWebTypeEn      string `json:"promotionWebTypeEn"`
	PromotionWebStatusId    int64  `json:"promotionWebStatusId"`
	ConditonDetail          string `json:"conditonDetail"`
	ImageUrl                string `json:"imageUrl"`
	Name                    string `json:"name"`
	ShortDescription        string `json:"shortDescription"`
	Description             string `json:"description"`
	PromotionWebDateTypeId  int64  `json:"promotionWebDateTypeId"`
	PromotionWebDateTypeTh  string `json:"promotionWebDateTypeTh"`
	PromotionWebDateTypeEn  string `json:"promotionWebDateTypeEn"`
	StartDate               string `json:"startDate"`
	EndDate                 string `json:"endDate"`
	Monday                  bool   `json:"monday"`
	Tuesday                 bool   `json:"tuesday"`
	Wednesday               bool   `json:"wednesday"`
	Thursday                bool   `json:"thursday"`
	Friday                  bool   `json:"friday"`
	Saturday                bool   `json:"saturday"`
	Sunday                  bool   `json:"sunday"`
	TimeStart               string `json:"timeStart"`
	TimeEnd                 string `json:"timeEnd"`
	HiddenUrlLink           string `json:"hiddenUrlLink"`
	UserStatusWithPromotion string `json:"userStatusWithPromotion"`
}

type PromotionWebExpired struct {
	Id int64 `json:"id"`
}

type CheckUserPromotionBody struct {
	UserId int64 `json:"userId"`
	// CreditAmount *float64 `json:"creditAmount"`
	ImmediateWithPromotionUserId *int64 `json:"-"`
}

type SelectUserPromptionCase struct {
	UserId int64 `json:"userId"`
	// CreditAmount       float64 `json:"creditAmount"`
	PromotionWebTypeId int64 `json:"promotionWebTypeId"`
	PromotionWebId     int64 `json:"promotionWebId"`
	PromotionWebUserId int64 `json:"promotionWebUserId"`
}

type GetUserFirstDepositForPromotion struct {
	UserId           int64  `json:"userId"`
	FromTransferDate string `form:"fromTransferDate"`
	ToTransferDate   string `form:"toTransferDate"`
	PromotionId      int64  `json:"promotionId"`
}
type GetUserDepositWhileInPromotion struct {
	UserId           int64  `json:"userId"`
	FromTransferDate string `form:"fromTransferDate"`
	ToTransferDate   string `form:"toTransferDate"`
	PromotionId      int64  `json:"promotionId"`
}
type GetUserAbleWithdrawPertime struct {
	UserId           int64  `json:"userId"`
	FromTransferDate string `form:"fromTransferDate"`
	ToTransferDate   string `form:"toTransferDate"`
}

// AgcSimpleWinloseRequest
type AgcSimpleWinloseRequest struct {
	MemberCode string `form:"memberCode"`
	AddTime    *int64 `form:"addTime"`
	MinusTime  *int64 `form:"minusTime"`
	StartDate  string `form:"startDate"`
	EndDate    string `form:"endDate"`
}
type AgcSimpleWinloseSingle struct {
	StartDate  string `json:"StartDate"`
	EndDate    string `json:"EndDate"`
	PageSize   int    `json:"PageSize"`
	PageIndex  int    `json:"PageIndex"`
	MemberName string `json:"MemberName"`
	AgentName  string `json:"AgentName"`
	PlayerName string `json:"PlayerName"`
	Products   []int  `json:"Products"`
	TimeStamp  int    `json:"TimeStamp"`
	Sign       string `json:"Sign"`
}

type GetPromotionWebUserById struct {
	Id int64 `json:"id"`
}

type GetPromotionWebUserByIdResponse struct {
	Id                       int64     `json:"id"`
	PromotionWebId           int64     `json:"promotionWebId"`
	PromotionName            string    `json:"promotionName"`
	UserId                   int64     `json:"userId"`
	MemberCode               string    `json:"memberCode"`
	FullName                 string    `json:"fullName"`
	Phone                    string    `json:"phone"`
	PromotionWebUserStatusId int64     `json:"promotionWebUserStatusId"`
	PromotionWebUserStatusTh string    `json:"promotionWebUserStatusTh"`
	TotalAmount              float64   `json:"totalAmount"`
	CreatedAt                time.Time `json:"createdAt"`
}

type UpdatePromotionWebUserStatus struct {
	Id                       int64   `json:"id"`
	PromotionWebUserStatusId int64   `json:"promotionWebUserStatusId"`
	TotalAmount              float64 `json:"totalAmount"`
	TotalDepositAmount       float64 `json:"totalDepositAmount"`
}

type AutoCreateTesterForPromption struct {
	Id           int64
	Username     string
	Phone        string
	Password     string
	Fullname     string
	Firstname    string
	Lastname     string
	UserStatusId int64
	BankAccount  string
	BankId       int64
	ChannelId    int64
	LineId       string
	Encrypt      string
	VerifiedAt   *time.Time
	CreatedBy    int64
	UpdatedAt    *time.Time
}

type CheckPromotionWithdrawRequest struct {
	UserId         int64   `form:"userId"`
	CreditWithdraw float64 `form:"creditWithdraw"`
}

type PromotionWebUserGetListByUserIdRequest struct {
	UserId                   int64  `form:"userId" binding:"required"`
	PromotionWebUserStatusId *int64 `form:"promotionWebUserStatusId"`
	OfDate                   string `form:"ofDate"`
	DateType                 string `form:"dateType"`
	FromDate                 string `form:"fromDate"`
	ToDate                   string `form:"toDate"`
	Search                   string `form:"search"`
	Page                     int    `form:"page" default:"1"`
	Limit                    int    `form:"limit" default:"10"`
	SortCol                  string `form:"sortCol"`
	SortAsc                  string `form:"sortAsc"`
}

type PromotionWebUserGetListByUserIdResponse struct {
	Id                       int64     `json:"id"`
	PromotionWebId           int64     `json:"promotionWebId"`
	PromotionName            string    `json:"promotionName"`
	UserId                   int64     `json:"userId"`
	MemberCode               string    `json:"memberCode"`
	FullName                 string    `json:"fullName"`
	Phone                    string    `json:"phone"`
	PromotionWebUserStatusId int64     `json:"promotionWebUserStatusId"`
	PromotionWebUserStatusTh string    `json:"promotionWebUserStatusTh"`
	TotalAmount              float64   `json:"totalAmount"`
	CreatedAt                time.Time `json:"createdAt"`
	CanceledByAdminId        *int64    `json:"canceledByAdminId"`
	CanceledByAdminName      *string   `json:"canceledByAdminName"`
}

type PromotionWebUserCurrentWinLostResponse struct {
	PromotionWebUserId   int64   `json:"promotionWebUserId"`
	UserPlaylogGame      float64 `json:"userPlaylogGame"`
	UserPlaylogCasino    float64 `json:"userPlaylogCasino"`
	UserPlaylogSport     float64 `json:"userPlaylogSport"`
	UserPlaylogP2p       float64 `json:"userPlaylogP2p"`
	UserPlaylogLottery   float64 `json:"userPlaylogLottery"`
	UserPlaylogFinancial float64 `json:"userPlaylogFinancial"`
	UserPlaylogTotal     float64 `json:"userPlaylogTotal"`
	TurnCalculation      float64 `json:"turnCalculation"`
}

type PromotionWebUserCurrentWinLostRequest struct {
	Id int64 `uri:"id" binding:"required"`
}

type PromotionWebGetSildeListOnlyActive struct {
	Id                     int64  `json:"id"`
	PromotionWebTypeId     int64  `json:"promotionWebTypeId"`
	PromotionWebTypeTh     string `json:"promotionWebTypeTh"`
	PromotionWebStatusId   int64  `json:"promotionWebStatusId"`
	PromotionWebStatusTh   string `json:"promotionWebStatusTh"`
	Name                   string `json:"name"`
	PromotionWebDateTypeId int64  `json:"promotionWebDateTypeId"`
	StartDate              string `json:"startDate"`
	EndDate                string `json:"endDate"`
	TimeStart              string `json:"timeStart"`
	TimeEnd                string `json:"timeEnd"`
}

type CheckUserPromotionOnlyNewMemberFree struct {
	Id                               int64   `json:"id"`
	PromotionWebTypeId               int64   `json:"promotionWebTypeId"`
	PromotionWebTypeTh               string  `json:"promotionWebTypeTh"`
	PromotionWebStatusId             int64   `json:"promotionWebStatusId"`
	PromotionWebStatusTh             string  `json:"promotionWebStatusTh"`
	ConditionDetail                  string  `json:"conditionDetail"`
	ImageUrl                         string  `json:"imageUrl"`
	Name                             string  `json:"name"`
	ShortDescription                 string  `json:"shortDescription"`
	Description                      string  `json:"description"`
	PromotionWebDateTypeId           int64   `json:"promotionWebDateTypeId"`
	StartDate                        string  `json:"startDate"`
	EndDate                          string  `json:"endDate"`
	FreeBonusAmount                  float64 `json:"freeBonusAmount"`
	PrivilegePerDay                  int64   `json:"privilegePerDay"`
	AbleWithdrawMorethan             float64 `json:"ableWithdrawMorethan"`
	PromotionWebBonusConditionId     int64   `json:"promotionWebBonusConditionId"`
	PromotionWebBonusConditionTh     string  `json:"promotionWebBonusConditionTh"`
	PromotionWebBonusConditionSyntax string  `json:"promotionWebBonusConditionSyntax"`
	BonusConditionAmount             float64 `json:"bonusConditionAmount"`
	PromotionWebBonusTypeId          int64   `json:"promotionWebBonusTypeId"`
	PromotionWebBonusTypeTh          string  `json:"promotionWebBonusTypeTh"`
	BonusTypeAmount                  float64 `json:"bonusTypeAmount"`
	BonusTypeAmountMax               float64 `json:"bonusTypeAmountMax"`
	AbleWithdrawPertime              float64 `json:"ableWithdrawPertime"`
	PromotionWebTurnoverTypeId       int64   `json:"promotionWebTurnoverTypeId"`
	PromotionWebTurnoverTypeTh       string  `json:"promotionWebTurnoverTypeTh"`
	TurnoverAmount                   float64 `json:"turnoverAmount"`
	Monday                           bool    `json:"monday"`
	Tuesday                          bool    `json:"tuesday"`
	Wednesday                        bool    `json:"wednesday"`
	Thursday                         bool    `json:"thursday"`
	Friday                           bool    `json:"friday"`
	Saturday                         bool    `json:"saturday"`
	Sunday                           bool    `json:"sunday"`
	TimeStart                        string  `json:"timeStart"`
	TimeEnd                          string  `json:"timeEnd"`
}

type PromotionWebRegisterMember struct {
	Id             int64     `json:"id"`
	UserId         int64     `json:"userId"`
	PromotionId    int64     `json:"promotionId"`
	RegisterStatus int64     `json:"registerStatus"`
	RegisterAt     time.Time `json:"registerAt"`
	CreatedAt      time.Time `json:"createdAt"`
	UpdatedAt      time.Time `json:"updatedAt"`
	DeletedAt      time.Time `json:"deletedAt"`
}

type PromotionWebRegisterMemberCreateRequest struct {
	Id             int64     `json:"-"`
	UserId         int64     `json:"userId" binding:"required"`
	PromotionId    int64     `json:"promotionId" binding:"required"`
	RegisterStatus int64     `json:"registerStatus"`
	RegisterAt     time.Time `json:"registerAt"`
}

type PromotionWebRegisterMemberUpdateRequest struct {
	Id             int64      `json:"-"`
	UserId         *int64     `json:"userId"`
	PromotionId    *int64     `json:"promotionId"`
	RegisterStatus *int64     `json:"registerStatus"`
	RegisterAt     *time.Time `json:"registerAt"`
}

type PromotionWebRegisterMemberGetTotalResponse struct {
	AlreadyRegister int64 `json:"alreadyRegister"`
}

type PromotionWebUserGetListByPromotionWebIdRequest struct {
	PromotionWebId int64  `form:"promotionWebId" binding:"required"`
	FromToday      string `form:"fromToday"`
}

type PromotionWebRegisterMemberGetTotal struct {
	PromotionId     int64 `json:"promotionId"`
	AlreadyRegister int64 `json:"alreadyRegister"`
}

type CreateTurnOverPromotionWebUser struct {
	UserId             int64 `json:"userId"`
	PromotionWebTypeId int64 `json:"promotionWebTypeId"`
	PromotionWebId     int64 `json:"promotionWebId"`
	PromotionWebUserId int64 `json:"promotionWebUserId"`
}

type PromotionWebUserLog struct {
	Id          int64     `json:"id"`
	JsonRequest string    `json:"jsonRequest"`
	JsonPayload string    `json:"jsonPayload"`
	LogType     string    `json:"logType"`
	Status      string    `json:"status"`
	CreatedAt   time.Time `json:"createdAt"`
	UpdatedAt   time.Time `json:"updatedAt"`
	DeletedAt   time.Time `json:"deletedAt"`
}

type GetUserPromotionReportListRequest struct {
	Search    string `form:"search"`
	StartDate string `form:"startDate" time_format:"2006-01-02"`
	EndDate   string `form:"end" time_format:"2006-01-02"`
	Page      int    `form:"page" default:"1"`
	Limit     int    `form:"limit" default:"10"`
}

type PromotionWebGetListSummaryRequest struct {
	StartDate string `form:"startDate" time_format:"2006-01-02"`
	EndDate   string `form:"end" time_format:"2006-01-02"`
}
type PromotionWebGetListSummaryResponse struct {
	TotalBonusAmount float64 `json:"totalBonusAmount"`
}
type PromotionWebUserGetListSummaryRequest struct {
	PromotionWebId *int64 `form:"promotionWebId" `
	StartDate      string `form:"startDate" time_format:"2006-01-02"`
	EndDate        string `form:"end" time_format:"2006-01-02"`
}
type PromotionWebUserGetListSummaryResponse struct {
	TotalBonusAmount  float64 `json:"totalBonusAmount"`
	TotalCreditAmount float64 `json:"totalCreditAmount"`
}
type LockCreditPromotionCreateRequest struct {
	Id                 int64   `json:"-"`
	UserId             int64   `json:"userId" binding:"required"`
	PromotionId        int64   `json:"promotionId" binding:"required"`
	BonusAmount        float64 `json:"bonusAmount" binding:"required"`
	IsLocked           bool    `json:"isLocked"`
	PromotionWebUserId int64   `json:"promotionWebUserId"`
}
type LockCreditPromotionUpdateRequest struct {
	UserId      int64 `json:"userId" binding:"required"`
	PromotionId int64 `json:"promotionId" binding:"required"`
}

type LockCreditPromotionUpdateResposnse struct {
	IsLockedCredit bool `json:"isLockedCredit"`
}

type LockCreditWithdrawCreateRequest struct {
	Id                           int64   `json:"-"`
	UserId                       int64   `json:"userId"`
	RefId                        int64   `json:"refId"`
	Detail                       string  `json:"detail"`
	UserWithdrawLockCreditTypeId int64   `json:"userWithdrawLockCreditTypeId"`
	CreditMoreThan               float64 `json:"creditMoreThan"`
	AllowWithdrawAmount          float64 `json:"allowWithdrawAmount"`
	WithdrawAmount               float64 `json:"withdrawAmount"`
	PullCreditAmount             float64 `json:"pullCreditAmount"`
	IsLocked                     bool    `json:"isLocked"`
	IsPullCredit                 bool    `json:"isPullCredit"`
}

type GetLockCreditWithdrawListRequest struct {
	UserId    int64  `form:"userId"`
	Search    string `form:"search"`
	StartDate string `form:"startDate" time_format:"2006-01-02"`
	EndDate   string `form:"endDate" time_format:"2006-01-02"`
	Page      int    `form:"page" default:"1"`
	Limit     int    `form:"limit" default:"10"`
}

type GetLockCreditWithdrawListResponse struct {
	Id                           int64      `json:"id"`
	UserId                       int64      `json:"userId"`
	MemberCode                   string     `json:"memberCode"`
	Fullname                     string     `json:"fullname"`
	Phone                        string     `json:"phone"`
	RefId                        int64      `json:"refId"`
	Detail                       string     `json:"detail"`
	UserWithdrawLockCreditTypeId int64      `json:"userWithdrawLockCreditTypeId"`
	UserWithdrawLockCreditTypeTh string     `json:"userWithdrawLockCreditTypeTh"`
	CreditMoreThan               float64    `json:"creditMoreThan"`
	AllowWithdrawAmount          float64    `json:"allowWithdrawAmount"`
	WithdrawAmount               float64    `json:"withdrawAmount"`
	PullCreditAmount             float64    `json:"pullCreditAmount"`
	IsLocked                     bool       `json:"isLocked"`
	IsPullCredit                 bool       `json:"isPullCredit"`
	CreatedAt                    time.Time  `json:"createdAt"`
	ApprovedAt                   *time.Time `json:"approvedAt"`
	ApprovedById                 *int64     `json:"approvedById"`
	ApporvedByName               *string    `json:"apporvedByName"`
}
type UpdateLockCreditWithdrawRequest struct {
	Id           int64     `json:"-"`
	IsLocked     *bool     `json:"-"`
	ApprovedAt   time.Time `json:"-"`
	ApprovedById int64     `json:"-"`
}

type CheckLockCreditWithdrawByUserId struct {
	Id                           int64   `json:"id"`
	UserId                       int64   `json:"userId"`
	RefId                        int64   `json:"refId"`
	Detail                       string  `json:"detail"`
	UserWithdrawLockCreditTypeId int64   `json:"userWithdrawLockCreditTypeId"`
	CreditMoreThan               float64 `json:"creditMoreThan"`
	AllowWithdrawAmount          float64 `json:"allowWithdrawAmount"`
	IsLocked                     bool    `json:"isLocked"`
	IsPullCredit                 bool    `json:"isPullCredit"`
}

type UpdateLockCreditWithdrawAutoUpdated struct {
	Id               int64   `json:"id"`
	WithdrawAmount   float64 `json:"withdrawAmount"`
	PullCreditAmount float64 `json:"pullCreditAmount"`
	IsLocked         *bool   `json:"isLocked"`
}

type MigrateBackUpLockCreditBackResponse struct {
	Total        int `json:"total"`
	CountError   int `json:"countError"`
	CountSuccess int `json:"countSuccess"`
}

type GetAllWithdrawProcessPromotionWebUser struct {
	Id                       int64   `json:"id"`
	PromotionWebId           int64   `json:"promotionWebId"`
	PromotionWebTypeId       int64   `json:"promotionWebTypeId"`
	AbleWithdrawMorethan     float64 `json:"ableWithdrawMorethan"`
	AbleWithdrawPertime      float64 `json:"ableWithdrawPertime"`
	UserId                   int64   `json:"userId"`
	PromotionWebUserStatusId int64   `json:"promotionWebUserStatusId"`
	PromotionWebName         string  `json:"promotionWebName"`
}

type GetAllLockCreditWithdrawByRefId struct {
	RefId int64 `json:"refId"`
}

type ShowPromotionWebForHiddenUrlRequest struct {
	HiddenUrlLink string `uri:"hiddenUrlLink" binding:"required"`
}
