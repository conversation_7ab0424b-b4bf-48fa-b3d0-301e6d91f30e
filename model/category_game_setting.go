package model

type CreateCategoryGameSettingBody struct {
	IsOpenSlot          int `json:"isOpenSlot"`
	IsOpenCasino        int `json:"isOpenCasino"`
	IsOpenSport         int `json:"isOpenSport"`
	IsOpenLottery       int `json:"isOpenLottery"`
	IsOpenP2p           int `json:"isOpenP2p"`
	IsOpenExternalLotto int `json:"isOpenExternalLotto"`
	IsOpenCbLotto       int `json:"isOpenCbLotto"`
	CreatedById         int `json:"createdById"`
}

type UpdateCategoryGameSettingBody struct {
	IsOpenSlot          *int  `json:"isOpenSlot"`
	IsOpenCasino        *int  `json:"isOpenCasino"`
	IsOpenSport         *int  `json:"isOpenSport"`
	IsOpenLottery       *int  `json:"isOpenLottery"`
	IsOpenP2p           *int  `json:"isOpenP2p"`
	IsOpenExternalLotto *int  `json:"isOpenExternalLotto"`
	IsOpenCbLotto       *int  `json:"isOpenCbLotto"`
	UpdatedById         int64 `json:"-"`
}

type GetCategoryGameSettingResponse struct {
	IsOpenSlot          int `json:"isOpenSlot"`
	IsOpenCasino        int `json:"isOpenCasino"`
	IsOpenSport         int `json:"isOpenSport"`
	IsOpenLottery       int `json:"isOpenLottery"`
	IsOpenP2p           int `json:"isOpenP2p"`
	IsOpenExternalLotto int `json:"isOpenExternalLotto"`
	IsOpenCbLotto       int `json:"isOpenCbLotto"`
}

type GetCategoryGameSettingV2 struct {
	Id            int64  `json:"id"`
	PriorityOrder int64  `json:"priorityOrder"`
	Name          string `json:"name"`
	LabelTh       string `json:"labelTh"`
	LabelEn       string `json:"labelEn"`
	IsActive      int64  `json:"isActive"`
}
type UpdateCategoryGameSettingV2 struct {
	Id       int64  `json:"-"`
	IsActive *int64 `json:"isActive"`
}
