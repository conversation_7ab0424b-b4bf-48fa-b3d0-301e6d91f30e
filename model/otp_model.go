package model

type OtpRequestBody struct {
	Otcid             string `json:"otcId" validate:"required"`
	Mobile            string `json:"mobile" validate:"required"`
	Notifyurl         string `json:"notifyUrl" validate:"required"`
	Notifycontenttype string `json:"notifyContentType" validate:"required"`
	Callbackdata      string `json:"callbackData" validate:"required"`
}

type OtpResponseBody struct {
	Otpid         string `json:"Otpid" errormessage:"Otpid is required"`
	Referencecode string `json:"OtpCode"`
}

type OtpSendResponse struct {
	OtcId         string      `json:"otcId"`
	OtpId         string      `json:"otpId"`
	ReferenceCode string      `json:"referenceCode"`
	Success       interface{} `json:"success"`
}

type OtpVerifyBody struct {
	OtpId   string `json:"otpId"`
	OtpCode string `json:"otpCode"`
}

type OtpVerifyResponse struct {
	OtpId        string `json:"otpId"`
	Result       bool   `json:"result"`
	IsErrorCount bool   `json:"isErrorCount"`
	IsExprCode   bool   `json:"isExprCode"`
}
