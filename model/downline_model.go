package model

import (
	"time"
)

type DownlinePaymentInfo struct {
	BankPayment   string `json:"bankPayment"`
	AccountNumber string `json:"accountNumber"`
	AccountName   string `json:"accountName"`
	HengPayment   string `json:"hengPayment"`
	BitcoinKey    string `json:"bitcoinKey"`
	UsePayonex    bool   `json:"usePayonex"`
}

type DownlineWebInfo struct {
	Id                       int64   `json:"id"`
	Name                     string  `json:"name"`
	WebDomain                string  `json:"webDomain"`
	IsFrontEnabled           bool    `json:"isFrontEnabled"`
	IsBackEnabled            bool    `json:"isBackEnabled"`
	MaintenanceMessage       string  `json:"maintenanceMessage"`
	PaymentDetail            string  `json:"paymentDetail"`
	CurrentWebPackageId      int64   `json:"currentWebPackageId"`
	BuyWebPackageDiscount    float64 `json:"buyWebPackageDiscount"`
	WebExpiredDate           string  `json:"webExpiredDate"`
	CurrentSmsPackageId      int64   `json:"currentSmsPackageId"`
	SmsCreditBalance         int64   `json:"smsCreditBalance"`
	SmsExpiredDate           string  `json:"smsExpiredDate"`
	CurrentFastbankPackageId int64   `json:"currentFastbankPackageId"`
	FastbankCreditBalance    int64   `json:"fastbankCreditBalance"`
	FastbankExpiredDate      string  `json:"fastbankExpiredDate"`
	FastbankFreeStartDate    string  `json:"fastbankFreeStartDate"`
	FastbankFreeEndDate      string  `json:"fastbankFreeEndDate"`
}
type DownlineWebInfoRequest struct {
	ApiKey string `form:"apiKey" binding:"required" validate:"required,alphanum,min=32,max=32"`
}
type DownlineWebBalanceRequest struct {
	SmsCreditBalance      int64 `form:"smsCreditBalance"`
	FastbankCreditBalance int64 `form:"fastbankCreditBalance"`
}
type DownlineInvoiceSubmitRequest struct {
	Id                int64      `json:"id"`
	WebId             int64      `json:"webId"`
	WebName           string     `json:"webName"`
	InvoiceNo         string     `json:"invoiceNo"`
	InvoiceTypeId     int64      `json:"invoiceTypeId"`
	PackageId         int64      `json:"packageId"`
	PackageDetail     string     `json:"packageDetail"`
	RenewDays         int        `json:"renewDays"`
	RenewCreditAmount int        `json:"renewCreditAmount"`
	InvoiceAt         *time.Time `json:"invoiceAt"`
	ExpireAt          *time.Time `json:"expireAt"`
	PaidAt            *time.Time `json:"paidAt"`
	PaidByName        string     `json:"paidByName"`
	PaymentDetail     string     `json:"paymentDetail"`
	SlipImagePath     string     `json:"slipImagePath"`
	RawQrCode         string     `json:"rawQrCode"`
	ConfirmBy         *int64     `json:"confirmBy"`
	ConfirmAt         *time.Time `json:"confirmAt"`
	StatusId          int64      `json:"statusId"`
	SumPrice          float64    `json:"sumPrice"`
	VatPercent        float64    `json:"vatPercent"`
	VatPrice          float64    `json:"vatPrice"`
	DiscountPrice     float64    `json:"discountPrice"`
	TotalPrice        float64    `json:"totalPrice"`
	CreateBy          int64      `json:"createBy"`
	CreateByName      string     `json:"createByName"`
	CreatedAt         time.Time  `json:"createdAt"`
}
type DownlineInvoiceSubmitResponse struct {
	Id        int64  `json:"id"`
	WebId     int64  `json:"webId"`
	InvoiceNo string `json:"invoiceNo"`
}

type DownlineInvoiceListRequest struct {
	StatusId int64  `form:"statusId"`
	TypeId   *int64 `form:"typeId"`
}
type DownlineConfirmedInvoiceListRequest struct {
	ApiKey     string `form:"apiKey" binding:"required" validate:"required,alphanum,min=32,max=32"`
	PendingIds []int  `json:"pendingIds"`
}
type DownlineConfirmedInvoiceListResponse struct {
	Total int64                     `json:"total"`
	List  []DownlineInvoiceResponse `json:"list"`
}
type DownlineInvoiceResponse struct {
	Id                int64      `json:"id"`
	WebId             int64      `json:"webId"`
	WebName           string     `json:"webName"`
	DownlineInvoiceId int64      `json:"downlineInvoiceId"`
	InvoiceNo         string     `json:"invoiceNo"`
	InvoiceTypeId     int64      `json:"invoiceTypeId"`
	PackageId         int64      `json:"packageId"`
	PackageDetail     string     `json:"packageDetail"`
	RenewDays         int        `json:"renewDays"`
	RenewCreditAmount int        `json:"renewCreditAmount"`
	InvoiceAt         *time.Time `json:"invoiceAt"`
	ExpireAt          *time.Time `json:"expireAt"`
	PaidAt            *time.Time `json:"paidAt"`
	PaidByName        string     `json:"paidByName"`
	PaymentDetail     string     `json:"paymentDetail"`
	ConfirmByName     string     `json:"confirmByName"`
	ConfirmAt         *time.Time `json:"confirmAt"`
	StatusId          int64      `json:"statusId"`
	SumPrice          float64    `json:"sumPrice"`
	VatPercent        float64    `json:"vatPercent"`
	VatPrice          float64    `json:"vatPrice"`
	DiscountPrice     float64    `json:"discountPrice"`
	TotalPrice        float64    `json:"totalPrice"`
	CreateBy          int64      `json:"createBy"`
	CreateByName      string     `json:"createByName"`
	CreatedAt         time.Time  `json:"createdAt"`
}

type ScammerSubmitRequest struct {
	Id          int64  `json:"id"`
	Phone       string `json:"phone"`
	BankName    string `json:"bankName"`
	BankAccount string `json:"bankAccount"`
	Reason      string `json:"reason"`
	AccountName string `json:"accountName"`
}

type ScammerNumberListResponse struct {
	PhoneList      []string `json:"phoneList"`
	BankNumberList []string `json:"bankNumberList"`
}

type PublicWebInfo struct {
	Name               string `json:"name"`
	WebDomain          string `json:"webDomain"`
	IsFrontEnabled     bool   `json:"isFrontEnabled"`
	IsBackEnabled      bool   `json:"isBackEnabled"`
	MaintenanceMessage string `json:"maintenanceMessage"`
}

type DownlineScammerequest struct {
	UserId      int64  `form:"userId" binding:"required" validate:"required"`
	Phone       string `form:"phone"`
	BankAccount string `form:"bankAccount"`
	AccountName string `form:"accountName"`
	Page        int    `form:"page" default:"1" min:"1"`
	Limit       int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol     string `form:"sortCol"`
	SortAsc     string `form:"sortAsc"`
}
type DownlineScammerResponse struct {
	Id                int64     `json:"id"`
	WebName           string    `json:"webName"`
	WebId             int64     `json:"webId"`
	WebDomain         string    `json:"webDomain"`
	DownlineScammerId int64     `json:"dowlineScammerId"`
	Phone             string    `json:"phone"`
	BankName          string    `json:"bankName"`
	BankAccount       string    `json:"bankAccount"`
	Reason            string    `json:"reason"`
	AccountName       string    `json:"accountName"`
	CreatedAt         time.Time `json:"createdAt"`
}
type DownlineScammerPaginationResponse struct {
	Message string                    `json:"message"`
	List    []DownlineScammerResponse `json:"list"`
	Total   int64                     `json:"total"`
}

type DownlineLineContactResponse struct {
	Id            int64     `json:"id"`
	ContactLineId string    `json:"contactLineId"`
	CreatedAt     time.Time `json:"createdAt"`
	UpdatedAt     time.Time `json:"updatedAt"`
}
type DownlineInvoiceHengPaymentCreateRequest struct {
	InvoiceId int64   `json:"invoiceId"`
	Amount    float64 `json:"amount"`
}
type DownlineInvoiceHengPaymentCreateResponse struct {
	Id       int64   `json:"id"`
	OrderNo  string  `json:"orderNo"`
	Amount   float64 `json:"amount"`
	QrBase64 string  `json:"qrBase64"`
}

type DownlineInvoicePayonexPaymentCreateRequest struct {
	InvoiceId   int64   `json:"invoiceId"`
	BankCode    string  `json:"bankCode"`
	AccountNo   string  `json:"accountNo"`
	AccountName string  `json:"accountName"`
	Amount      float64 `json:"amount"`
}
type DownlineInvoicePayonexPaymentCreateResponse struct {
	Id        int64     `json:"id"`
	OrderNo   string    `json:"orderNo"`
	Amount    float64   `json:"amount"`
	QrBase64  string    `json:"qrBase64"`
	CreatedAt time.Time `json:"createdAt"`
}
