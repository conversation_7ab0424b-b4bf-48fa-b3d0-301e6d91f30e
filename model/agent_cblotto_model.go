package model

import "time"

type GetInternalAgentCblottoSetting struct {
	Id                 int64  `json:"id"`
	IsActive           bool   `json:"isActive"`
	ProgramAllowUse    string `json:"programAllowUse"`
	CblottoAppId       string `json:"cblottoAppId"`
	CblottoAppPrivate  string `json:"cblottoAppPrivate"`
	CblottoUrl         string `json:"cblottoUrl"`
	CblottoHrefBackUrl string `json:"cblottoHrefBackUrl"`
}

type UpdateAgentCblottoSetting struct {
	CblottoAppPrivate  *string   `json:"cblottoAppPrivate"`
	CblottoAppId       *string   `json:"cblottoAppId"`
	CblottoHrefBackUrl string    `json:"-"`
	IsActive           *bool     `json:"isActive"`
	UpdatedAt          time.Time `json:"-"`
	UpdatedById        int64     `json:"-"`
}

type GetInternalAgentCblottoSettingResponse struct {
	Id                int64  `json:"id"`
	IsActive          bool   `json:"isActive"`
	CblottoAppId      string `json:"cblottoAppId"`
	CblottoAppPrivate string `json:"cblottoAppPrivate"`
}

type AgentCblottoUserLoginGame struct {
	SecretKey  string `json:"-"`
	OperatorId string `json:"operatorId"`
	Username   string `json:"username"`
	// GameId     int64  `json:"gameId"`
}

type CallApiAgentCblottoDetail struct {
	CblottoAppId      string `json:"cblottoAppId"`
	CblottoAppPrivate string `json:"cblottoAppPrivate"`
	CblottoUrl        string `json:"cblottoUrl"`
}

type AgentCblottoUserLoginGameResponse struct {
	Url string `json:"url"`
}

type CallApiAgentCblottoLaunch struct {
	UserId int64 `json:"-"`
	// GameCode string `json:"gameCode"`
}

type CallBackAgentCblottoCheckBalanceRequest struct {
	Username string `json:"username"`
}
type CallBackAgentCblottoCheckBalanceResponse struct {
	Username string  `json:"username"`
	Balance  float64 `json:"balance"`
}

type CallBackAgentCblottoTransaction struct {
	TransactionType string  `json:"transactionType"` // bet, reward, cancel
	OperatorId      string  `json:"operatorId"`
	Username        string  `json:"username"`      // user
	TransactionId   string  `json:"transactionId"` // refcode ของ transaction ตอน bet
	RoundId         int64   `json:"roundId"`       // รอบที่เล่น เช่น id งวดที่
	GameId          int64   `json:"gameId"`        // หวยที่เล่น
	BetAmount       float64 `json:"betAmount"`     // ยอดแทง
	RewardAmount    float64 `json:"rewardAmount"`  // ยอดได้รับ
	CancelAmount    float64 `json:"cancelAmount"`  // ยอดยกเลิก
}

type CallBackAgentCblottoTransactionResponse struct {
	TransactionId string  `json:"transactionId"`
	RoundId       int64   `json:"roundId"`
	GameId        int64   `json:"gameId"`
	BetAmount     float64 `json:"betAmount"`
	RewardAmount  float64 `json:"rewardAmount"`
	CancelAmount  float64 `json:"cancelAmount"`
	Username      string  `json:"username"`      // user
	BalanceBefore float64 `json:"balanceBefore"` // ยอดเงินก่อนที่จะแทง
	BalanceAfter  float64 `json:"balanceAfter"`  // ยอดเงินหลังจากแทง
}

type CreateAgentCblottoCallback struct {
	Id             int64   `json:"id"`
	UserId         int64   `json:"userId"`
	MemberCode     string  `json:"memberCode"`
	Payoff         float64 `json:"payoff"`
	BetAmount      float64 `json:"betAmount"`
	WinloseAmount  float64 `json:"winloseAmount"`
	CancelAmount   float64 `json:"cancelAmount"`
	Balance        float64 `json:"balance"`
	BeforeBalance  float64 `json:"beforeBalance"`
	AfterBalance   float64 `json:"afterBalance"`
	TransactionId  string  `json:"transactionId"`
	RoundId        string  `json:"roundId"`
	GameId         string  `json:"gameId"`
	CallbackReason string  `json:"callbackReason"`
	Remark         string  `json:"remark"`
	IsSuccess      bool    `json:"isSuccess"`
}

type UpdateAgentCblottoCallback struct {
	UserId         *int64   `json:"userId"`
	MemberCode     string   `json:"memberCode"` // ใช้่เป็น หลัก
	Payoff         *float64 `json:"payoff"`
	BetAmount      *float64 `json:"betAmount"`
	WinloseAmount  *float64 `json:"winloseAmount"`
	CancelAmount   *float64 `json:"cancelAmount"`
	Balance        *float64 `json:"balance"`
	BeforeBalance  *float64 `json:"beforeBalance"`
	AfterBalance   *float64 `json:"afterBalance"`
	TransactionId  string   `json:"transactionId"` // ใช้่เป็น หลัก
	RoundId        string   `json:"roundId"`
	GameId         *string  `json:"gameId"`
	CallbackReason *string  `json:"callbackReason"`
	Remark         *string  `json:"remark"`
	IsSuccess      *bool    `json:"isSuccess"`
}
