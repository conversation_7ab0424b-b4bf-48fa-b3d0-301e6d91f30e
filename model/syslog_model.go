package model

type SystemLog struct {
	Id          int64  `json:"id"`
	Name        string `json:"name"`
	Status      string `json:"status"`
	JsonInput   string `json:"json_input"`
	JsonRequest string `json:"json_request"`
	JsonReponse string `json:"json_reponse"`
	CreatedAt   string `json:"created_at"`
}
type SystemLogCreateBody struct {
	Id          int64  `json:"id"`
	Name        string `json:"name"`
	Status      string `json:"status"`
	JsonInput   string `json:"json_input"`
	JsonRequest string `json:"json_request"`
}
type SystemLogUpdateBody struct {
	Id          int64  `json:"id"`
	JsonInput   string `json:"json_input"`
	JsonReponse string `json:"json_reponse"`
}
