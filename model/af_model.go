package model

import "time"

// var (
// 	SPORT  = []string{"บอลออนไลน์", "SPORT"}
// 	CASINO = []string{"คาสิโนออนไลน์", "CASINO"}
// 	SLOT   = []string{"เกมส์สล็อต", "SLOT"}
// )

type Affiliate struct {
	ID                  int64
	RegisterBonusCredit float64
	RefId               int64
	UserId              int64
	CreatedAt           time.Time
}

type AffiliateUser struct {
	UserId              int64   `json:"userId"`
	CommissionTotal     float64 `json:"commissionTotal"`
	CommissionCurrent   float64 `json:"commissionCurrent"`
	TotalWithdraw       float64 `json:"totalWithdraw"`
	FirstDepositBonus   float64 `json:"firstDepositBonus"`
	BonusShareTotal     float64 `json:"bonusShareTotal"`
	CommissionSport     float64 `json:"commissionSport"`
	CommissionCasino    float64 `json:"commissionCasino"`
	CommissionGame      float64 `json:"commissionSlot"`
	CommissionLottery   float64 `json:"commissionLottery"`
	CommissionP2p       float64 `json:"commissionP2p"`
	CommissionFinancial float64 `json:"commissionFinancial"`
	LinkClickTotal      int     `json:"linkClickTotal"`
	MemberTotal         int     `json:"memberTotal"`
	MemberDepositTotal  int     `json:"memberDepositTotal"`
}

type AfSummary struct {
	CommissionTotal     float64 `json:"commissionTotal"`
	CommissionCurrent   float64 `json:"commissionCurrent"`
	CommissionSport     float64 `json:"commissionSport"`
	CommissionCasino    float64 `json:"commissionCasino"`
	CommissionGame      float64 `json:"commissionSlot"`
	CommissionLottery   float64 `json:"commissionLottery"`
	CommissionP2p       float64 `json:"commissionP2p"`
	CommissionFinancial float64 `json:"commissionFinancial"`
	BonusShareTotal     float64 `json:"bonusShareTotal"`
	FirstDepositBonus   float64 `json:"firstDepositBonus"`
	LinkClickTotal      int     `json:"linkClickTotal"`
	MemberTotal         int     `json:"memberTotal"`
	MemberDepositTotal  int     `json:"memberDepositTotal"`
	PercentSport        float64 `json:"percentSport"`
	PercentCasino       float64 `json:"percentCasino"`
	PercentGame         float64 `json:"percentSlot"`
	PercentLottery      float64 `json:"percentLottery"`
	PercentP2p          float64 `json:"percentP2p"`
	PercentFinancial    float64 `json:"percentFinancial"`
	UserId              int64   `json:"userId"`
}

type AfCommission struct {
	ID                int64
	FirstDepositBonus float64
	CommissionFrom    string
	Sport             float64
	Casino            float64
	Slot              float64
	Lottery           float64
	P2p               float64
	Financial         float64
	UpdatedAt         *time.Time
}

type AfCommissionBody struct {
	FirstDepositBonus       float64 `json:"firstDepositBonus"`
	MaxLevel                int     `json:"maxLevel"`
	CommissionFrom          string  `json:"commissionFrom"`
	Sport                   float64 `json:"sport"`
	Casino                  float64 `json:"casino"`
	Slot                    float64 `json:"slot"`
	Lottery                 float64 `json:"lottery"`
	P2p                     float64 `json:"p2p"`
	Financial               float64 `json:"financial"`
	Sport1                  float64 `json:"sport1"`
	Casino1                 float64 `json:"casino1"`
	Slot1                   float64 `json:"slot1"`
	Lottery1                float64 `json:"lottery1"`
	P2p1                    float64 `json:"p2p1"`
	Financial1              float64 `json:"financial1"`
	Sport2                  float64 `json:"sport2"`
	Casino2                 float64 `json:"casino2"`
	Slot2                   float64 `json:"slot2"`
	Lottery2                float64 `json:"lottery2"`
	P2p2                    float64 `json:"p2p2"`
	Financial2              float64 `json:"financial2"`
	Sport3                  float64 `json:"sport3"`
	Casino3                 float64 `json:"casino3"`
	Slot3                   float64 `json:"slot3"`
	Lottery3                float64 `json:"lottery3"`
	P2p3                    float64 `json:"p2p3"`
	Financial3              float64 `json:"financial3"`
	ReferralBonus           float64 `json:"referralBonus"`
	CommissionWithdrawMin   int64   `json:"commissionWithdrawMin"`
	RegisterBonusTypeId     int64   `json:"registerBonusTypeId"`
	RegisterBonusMin        int64   `json:"registerBonusMin"`
	RegisterBonusCredit     int64   `json:"registerBonusCredit"`
	RegisterBonusOptionId   int64   `json:"registerBonusOptionId"`
	RegisterBonusMaxPercent int64   `json:"registerBonusMaxPercent"`
	Description             string  `json:"description"`
	CollectableDays         int64   `json:"collectableDays"`
	MaxCommission           float64 `json:"maxCommission"`
	MaxCommissionPerLine    float64 `json:"maxCommissionPerLine"`
}

type AfCommissionCreateBody struct {
	Id                    int64 `json:"id"`
	RegisterBonusTypeId   int64 `json:"registerBonusTypeId"`
	RegisterBonusOptionId int64 `json:"registerBonusOptionId"`
}

type AfCommissionUpdateRequest struct {
	FirstDepositBonus       *float64 `json:"firstDepositBonus"`
	MaxLevel                *int     `json:"maxLevel"`
	CommissionFrom          *string  `json:"commissionFrom"`
	Sport                   *float64 `json:"sport"`
	Casino                  *float64 `json:"casino"`
	Slot                    *float64 `json:"slot"`
	Lottery                 *float64 `json:"lottery"`
	P2p                     *float64 `json:"p2p"`
	Financial               *float64 `json:"financial"`
	Sport1                  *float64 `json:"sport1"`
	Casino1                 *float64 `json:"casino1"`
	Slot1                   *float64 `json:"slot1"`
	Lottery1                *float64 `json:"lottery1"`
	P2p1                    *float64 `json:"p2p1"`
	Financial1              *float64 `json:"financial1"`
	Sport2                  *float64 `json:"sport2"`
	Casino2                 *float64 `json:"casino2"`
	Slot2                   *float64 `json:"slot2"`
	Lottery2                *float64 `json:"lottery2"`
	P2p2                    *float64 `json:"p2p2"`
	Financial2              *float64 `json:"financial2"`
	Sport3                  *float64 `json:"sport3"`
	Casino3                 *float64 `json:"casino3"`
	Slot3                   *float64 `json:"slot3"`
	Lottery3                *float64 `json:"lottery3"`
	P2p3                    *float64 `json:"p2p3"`
	Financial3              *float64 `json:"financial3"`
	ReferralBonus           *float64 `json:"referralBonus"`
	CommissionWithdrawMin   *int64   `json:"commissionWithdrawMin"`
	RegisterBonusTypeId     *int64   `json:"registerBonusTypeId"`
	RegisterBonusMin        *int64   `json:"registerBonusMin"`
	RegisterBonusCredit     *int64   `json:"registerBonusCredit"`
	RegisterBonusOptionId   *int64   `json:"registerBonusOptionId"`
	RegisterBonusMaxPercent *int64   `json:"registerBonusMaxPercent"`
	Description             *string  `json:"description"`
	CollectableDays         *int64   `json:"collectableDays"`
	MaxCommission           *float64 `json:"maxCommission"`
	MaxCommissionPerLine    *float64 `json:"maxCommissionPerLine"`
	UpdateById              int64    `gorm:"-"`
}

type AfCommissionResponse struct {
	FirstDepositBonus       float64 `json:"firstDepositBonus"`
	MaxLevel                int     `json:"maxLevel"`
	CommissionFrom          string  `json:"commissionFrom"`
	Sport                   float64 `json:"sport"`
	Casino                  float64 `json:"casino"`
	Slot                    float64 `json:"slot"`
	Lottery                 float64 `json:"lottery"`
	P2p                     float64 `json:"p2p"`
	Financial               float64 `json:"financial"`
	Sport1                  float64 `json:"sport1"`
	Casino1                 float64 `json:"casino1"`
	Slot1                   float64 `json:"slot1"`
	Lottery1                float64 `json:"lottery1"`
	P2p1                    float64 `json:"p2p1"`
	Financial1              float64 `json:"financial1"`
	Sport2                  float64 `json:"sport2"`
	Casino2                 float64 `json:"casino2"`
	Slot2                   float64 `json:"slot2"`
	Lottery2                float64 `json:"lottery2"`
	P2p2                    float64 `json:"p2p2"`
	Financial2              float64 `json:"financial2"`
	Sport3                  float64 `json:"sport3"`
	Casino3                 float64 `json:"casino3"`
	Slot3                   float64 `json:"slot3"`
	Lottery3                float64 `json:"lottery3"`
	P2p3                    float64 `json:"p2p3"`
	Financial3              float64 `json:"financial3"`
	ReferralBonus           float64 `json:"referralBonus"`
	CommissionWithdrawMin   int64   `json:"commissionWithdrawMin"`
	RegisterBonusTypeId     int64   `json:"registerBonusTypeId"`
	RegisterBonusTypeName   string  `json:"-"`
	RegisterBonusMin        int64   `json:"registerBonusMin"`
	RegisterBonusCredit     int64   `json:"registerBonusCredit"`
	RegisterBonusOptionId   int64   `json:"registerBonusOptionId"`
	RegisterBonusOptionName string  `json:"-"`
	RegisterBonusMaxPercent int64   `json:"registerBonusMaxPercent"`
	Description             string  `json:"description"`
	CollectableDays         int64   `json:"collectableDays"`
	MaxCommission           float64 `json:"maxCommission"`
	MaxCommissionPerLine    float64 `json:"maxCommissionPerLine"`
}

type AfCommissionUser struct {
	Commission AfCommission
	Income     AfIncome
}

type AfCommisionList struct {
	Name    string  `json:"name"`
	Percent float64 `json:"percent"`
	Value   float64 `json:"value"`
}

type AfCommissionForUserResponse struct {
	Result []AfCommisionList `json:"result"`
}

type AffMember struct {
	ID                  int64   `json:"id"`
	CommissionSport     float64 `json:"commissionSport" `
	CommissionCasino    float64 `json:"commissionCasino"`
	CommissionSlot      float64 `json:"commissionSlot"`
	CommissionLottery   float64 `json:"commissionLottery"`
	CommissionP2p       float64 `json:"commissionP2p"`
	CommissionFinancial float64 `json:"commissionFinancial"`
	CommissionTotal     float64 `json:"-"`
	MemberCode          string  `json:"memberCode"`
	PlayBalance         float64 `json:"playBalance"`
	ReceivedBalance     float64 `json:"receivedBalance"`
}

type AffMemberResponse struct {
	Id                  int64   `json:"id"`
	Level               int     `json:"level"`
	CommissionSport     float64 `json:"commissionSport" `
	CommissionCasino    float64 `json:"commissionCasino"`
	CommissionSlot      float64 `json:"commissionSlot"`
	CommissionLottery   float64 `json:"commissionLottery"`
	CommissionP2p       float64 `json:"commissionP2p"`
	CommissionFinancial float64 `json:"commissionFinancial"`
	CommissionTotal     float64 `json:"-"`
	MemberCode          string  `json:"memberCode"`
	PlayBalance         float64 `json:"playBalance"`
	ReceivedBalance     float64 `json:"receivedBalance"`
}

type AffMemberListRequest struct {
	RefBy  *int64 `form:"refBy"`
	Level  *int   `form:"level"`
	Page   int    `form:"page"`
	Limit  int    `form:"limit"`
	From   string `form:"from"`
	To     string `form:"to"`
	Search string `form:"search"`
}
type AffMemberListAdminRequest struct {
	UserId int64  `form:"userId" binding:"required"`
	Page   int    `form:"page"`
	Limit  int    `form:"limit"`
	From   string `form:"from"`
	To     string `form:"to"`
}

type AfIncome struct {
	ID              *int64     `json:"id"`
	Sport           float64    `json:"-"`
	Casino          float64    `json:"-"`
	Slot            float64    `json:"-"`
	Lottery         float64    `json:"-"`
	P2p             float64    `json:"-"`
	Financial       float64    `json:"-"`
	ReceivedBalance float64    `json:"receivedBalance" gorm:"-"`
	PlayBalance     float64    `json:"playBalance" gorm:"-"`
	ReceivedTotal1  float64    `json:"receivedTotal" gorm:"-"`
	UserId          int64      `json:"userId"`
	RefId           int64      `json:"refId"`
	CreatedAt       *time.Time `json:"createdAt"`
}

type AfCreate struct {
	ID              *int64     `json:"id"`
	Sport           float64    `json:"-"`
	Casino          float64    `json:"-"`
	Slot            float64    `json:"-"`
	Lottery         float64    `json:"-"`
	P2p             float64    `json:"-"`
	Financial       float64    `json:"-"`
	ReceivedBalance float64    `json:"receivedBalance" gorm:"-"`
	PlayBalance     float64    `json:"playBalance" gorm:"-"`
	ReceivedTotal1  float64    `json:"receivedTotal" gorm:"-"`
	UserId          int64      `json:"userId"`
	RefId           int64      `json:"refId"`
	CreatedAt       *time.Time `json:"createdAt"`
}

type AfIncomeList struct {
	Sport           float64    `json:"-"`
	Casino          float64    `json:"-"`
	Slot            float64    `json:"-"`
	Lottery         float64    `json:"-"`
	P2p             float64    `json:"-"`
	Financial       float64    `json:"-"`
	ReceivedBalance float64    `json:"receivedBalance"`
	PlayBalance     float64    `json:"-" gorm:"-"`
	ReceivedTotal   float64    `json:"-" gorm:"-"`
	CreatedAt       *time.Time `json:"createdAt"`
}

type AfIncomeResponse struct {
	Result []AfIncomeList `json:"result"`
}

type AfRegisterBonusOptionResponse struct {
	Result []AfRegisterBonusOption `json:"result"`
}

type AfRegisterBonusOption struct {
	ID      int64  `json:"id"`
	LabelEn string `json:"labelEn"`
	LabelTh string `json:"labelTh"`
}

type AfRegisterBonusTypeResponse struct {
	Result []AfRegisterBonusType `json:"result"`
}

type AfRegisterBonusType struct {
	ID      int64  `json:"id"`
	LabelEn string `json:"labelEn"`
	LabelTh string `json:"labelTh"`
}

type AfReportQuery struct {
	Page       int    `form:"page" default:"1"`
	Limit      int    `form:"limit" default:"10"`
	MemberCode string `form:"memberCode"`
	From       string `form:"from" default:"2023-01-13"`
	To         string `form:"to" default:"2023-12-31"`
}

type AfReport struct {
	MemberCode         string  `json:"memberCode"`
	Fullname           string  `json:"fullname"`
	LinkClickTotal     int     `json:"linkClickTotal"`
	MemberTotal        int     `json:"memberTotal"`
	MemberDepositTotal int     `json:"memberDepositTotal"`
	CommissionTotal    float64 `json:"commissionTotal"`
	BonusShareTotal    float64 `json:"bonusShareTotal"`
	WithdrawTotal      float64 `json:"withdrawTotal"`
	CommissionCurrent  float64 `json:"commissionCurrent"`
	UserId             int64   `json:"userId"`
}

type AfReportResponse struct {
	Result []AfReport `json:"result"`
	Total  int64      `json:"total"`
}

type AfReportTran struct {
	CreditAmount float64
	UserId       int64
}

type AfMemberTotal struct {
	Total  int   `json:"total"`
	UserId int64 `json:"userId"`
}

type AfMemberDeoisitTotal struct {
	Total  int   `json:"total"`
	UserId int64 `json:"userId"`
}

const (
	AFF_TRANSACTION_STATUS_PENDING      = int64(1)
	AFF_TRANSACTION_STATUS_EXPIRED      = int64(2)
	AFF_TRANSACTION_STATUS_WAIT_CONFIRM = int64(3)
	AFF_TRANSACTION_STATUS_TRANSFERRED  = int64(4)
)
const (
	AFF_TRANSACTION_TYPE_NEW_REGISTER   = int64(1)
	AFF_TRANSACTION_TYPE_FIRST_DEPOSIT  = int64(2)
	AFF_TRANSACTION_TYPE_PLAY_COMMISION = int64(3)
)

type AffTransaction struct {
	Id           int64      `json:"id"`
	UserId       int64      `json:"userId"`
	IncomeAmount float64    `json:"incomeAmount"`
	TypeId       int64      `json:"typeId"`
	StatusId     int64      `json:"statusId"`
	TransferAt   *time.Time `json:"transferAt"`
	CreatedAt    time.Time  `json:"createdAt"`
	UpdatedAt    *time.Time `json:"updatedAt"`
}
type AffTransactionListRequest struct {
	UserId   *int64 `form:"userId"`
	StatusId *int64 `form:"statusId"`
	TypeId   *int64 `form:"typeId"`
	Page     int    `form:"page" default:"1" min:"1"`
	Limit    int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol  string `form:"sortCol"`
	SortAsc  string `form:"sortAsc"`
}
type AffTransactionResponse struct {
	Id           int64      `json:"id"`
	UserId       int64      `json:"userId"`
	IncomeAmount float64    `json:"incomeAmount"`
	TypeId       int64      `json:"typeId"`
	TypeName     string     `json:"typeName"`
	StatusId     int64      `json:"statusId"`
	StatusName   string     `json:"statusName"`
	TransferAt   *time.Time `json:"transferAt"`
	CreatedAt    time.Time  `json:"createdAt"`
	UpdatedAt    *time.Time `json:"updatedAt"`
}
type AffTransactionSummaryResponse struct {
	// const (
	// 	AFF_TRANSACTION_TYPE_NEW_REGISTER   = int64(1)
	// 	AFF_TRANSACTION_TYPE_FIRST_DEPOSIT  = int64(2)
	// 	AFF_TRANSACTION_TYPE_PLAY_COMMISION = int64(3)
	// )
	TotalAmountNewRegister   float64 `json:"totalAmountNewRegister"`
	TotalAmountFirstDeposit  float64 `json:"totalAmountFirstDeposit"`
	TotalAmountPlayCommision float64 `json:"totalAmountPlayCommision"`
	TotalAmount              float64 `json:"totalAmount"`
}
type AffTransactionCreateBody struct {
	Id           int64      `json:"id"`
	UserId       int64      `json:"userId"`
	DailyKey     string     `json:"dailyKey"`
	DownlineId   int64      `json:"downlineId"`
	IncomeAmount float64    `json:"incomeAmount"`
	TypeId       int64      `json:"typeId"`
	StatusId     int64      `json:"statusId"`
	TransferAt   *time.Time `json:"transferAt"`
}
type AffTransactionUpdateBody struct {
	StatusId   *int64     `json:"statusId"`
	TransferAt *time.Time `json:"transferAt"`
}

type AffTransactionWithdraw struct {
	Id             int64     `json:"id"`
	UserId         int64     `json:"userId"`
	RcKey          string    `json:"rcKey"`
	WithdrawAmount float64   `json:"withdrawAmount"`
	JsonRelatedIds string    `json:"jsonRelatedIds"`
	CreatedAt      time.Time `json:"createdAt"`
}
type AffTransactionWithdrawCreateBody struct {
	Id             int64   `json:"id"`
	UserId         int64   `json:"userId"`
	RcKey          string  `json:"rcKey"`
	WithdrawAmount float64 `json:"withdrawAmount"`
	JsonRelatedIds string  `json:"jsonRelatedIds"`
}

type UserAffIncomeReponse struct {
	UserId              int64   `json:"userId"`
	CommissionTotal     float64 `json:"commissionTotal"`
	CommissionCurrent   float64 `json:"commissionCurrent"`
	FirstDepositBonus   float64 `json:"firstDepositBonus"`
	BonusShareTotal     float64 `json:"bonusShareTotal"`
	CommissionSport     float64 `json:"commissionSport"`
	CommissionCasino    float64 `json:"commissionCasino"`
	CommissionGame      float64 `json:"commissionGame"`
	CommissionLottery   float64 `json:"commissionLottery"`
	CommissionP2p       float64 `json:"commissionP2p"`
	CommissionFinancial float64 `json:"commissionFinancial"`
	LinkClickTotal      int     `json:"linkClickTotal"`
	MemberTotal         int     `json:"memberTotal"`
	MemberDepositTotal  int     `json:"memberDepositTotal"`
	PlayBalance         float64 `json:"playBalance"`
	ReceivedBalance     float64 `json:"receivedBalance"`
}

type CronMigrateOldAffResponse struct {
	TotalMigrated   int `json:"totalMigrated"`
	TotalErrorCount int `json:"totalErrorCount"`
	MatchTotalCount int `json:"matchTotalCount"`
	NotMatchCount   int `json:"totalNotMatchCount"`
}

type GetWebAffiliateCommissionDetailResponse struct {
	Description string `json:"description"`
}
