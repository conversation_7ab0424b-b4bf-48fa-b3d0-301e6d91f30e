package model

import "time"

type Video struct {
	Id         int64
	Title      string
	CoverUrl   string
	YoutubeUrl string
	CreatedAt  time.Time
	UpdatedAt  *time.Time
}

type VideoDetail struct {
	Id         int64     `json:"id"`
	Title      string    `json:"title"`
	CoverUrl   string    `json:"coverUrl"`
	YoutubeUrl string    `json:"youtubeUrl"`
	CreatedAt  time.Time `json:"createdAt"`
}

type VideoQuery struct {
	Page    int    `form:"page" binding:"omitempty,min=1" default:"1"`
	Limit   int    `form:"limit" binding:"omitempty,min=1,max=100" default:"10"`
	StartAt string `form:"startAt" validate:"omitempty,datetime=2006-01-02"`
	EndAt   string `form:"endAt" validate:"omitempty,datetime=2006-01-02"`
	// Filter  string `form:"filter" validate:"omitempty"`
}

type VideoBody struct {
	Title      string `json:"-"`
	CoverUrl   string `json:"-"`
	YoutubeUrl string `json:"youtubeUrl" binding:"required"`
}

type VideoUpdateBody struct {
	Title      string `json:"-"`
	CoverUrl   string `json:"-"`
	YoutubeUrl string `json:"youtubeUrl" binding:"required"`
}

type VideoListResponse struct {
	List  []VideoDetail `json:"list"`
	Total int           `json:"total"`
}
