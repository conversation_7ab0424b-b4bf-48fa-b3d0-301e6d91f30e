package model

import (
	"time"

	"gorm.io/gorm"
)

type AgentInfo struct {
	Total int64 `json:"total"`
}

type AgentLog struct {
	Id          int64  `json:"id"`
	Name        string `json:"name"`
	Status      string `json:"status"`
	JsonInput   string `json:"json_input"`
	JsonRequest string `json:"json_request"`
	JsonReponse string `json:"json_reponse"`
	CreatedAt   string `json:"created_at"`
}
type AgentLogCreateBody struct {
	Id          int64  `json:"id"`
	Name        string `json:"name"`
	Status      string `json:"status"`
	JsonInput   string `json:"json_input"`
	JsonRequest string `json:"json_request"`
}
type AgentLogUpdateBody struct {
	Id          int64  `json:"id"`
	JsonReponse string `json:"json_reponse"`
}

type TestIncreaseUserCreditRequest struct {
	Amount float64 `json:"amount" binding:"required"`
	UserId int64   `json:"userId" binding:"required"`
}
type TestDecreaseUserCreditRequest struct {
	Amount float64 `json:"amount" binding:"required"`
	UserId int64   `json:"userId" binding:"required"`
}

type AgentGameListRequest struct {
	Vendor   string `form:"vendor" binding:"required"`
	Category string `form:"category"`
}

const (
	F888_HOOKACTION_AUTHENTICATE = "AUTHENTICATE" // When user login to our system from (Flow 1)
	F888_HOOKACTION_GET_BALANCE  = "GET_BALANCE"  // Game will call this event to get latest balance
	F888_HOOKACTION_BET_PLACE    = "BET_PLACE"    // When user bet in any game this event will call
	F888_HOOKACTION_BET_SETTLE   = "BET_SETTLE"   // Any event where balance increase will call this event or the bet is being settle back
	F888_HOOKACTION_BET_CANCEL   = "BET_CANCEL"   // If bet is invalid, example: timeout or cannot connect to agent
	F888_HOOKACTION_VOID_SETTLE  = "VOID_SETTLE"  // If game cancel previous settle, user will get balance deduct event
)

type HookF888CbgameRequest struct {
	JsonPayload string `json:"jsonPayload"`
}
type HookF888CbgameCreateBody struct {
	Id          int64  `json:"id"`
	Name        string `json:"name"`
	JsonPayload string `json:"jsonPayload"`
}
type HookF888CbgameBody struct {
	Data struct {
		Action  string `json:"action"`
		Payload struct {
			Username string `json:"username"`
		}
	} `json:"data"`
	Hashed string `json:"hashed"`
}
type HookF888CbgameResponse struct {
	Data struct {
		Username string  `json:"username"`
		Balance  float64 `json:"balance"`
		Currency string  `json:"currency"`
	} `json:"data"`
	Error error `json:"error"`
}

type F888Auth001Request struct {
	Username string `json:"username" binding:"required"`
}
type F888Auth001Response struct {
	Status int64 `json:"status"`
	Data   struct {
		Token string `json:"token"`
		User  struct {
			Id                string   `json:"_id"`
			Username          string   `json:"username"`
			Agent             string   `json:"agent"`
			BetLimit          int64    `json:"betLimit"`
			Locked            bool     `json:"locked"`
			CreatedAt         string   `json:"createdAt"`
			UpdatedAt         string   `json:"updatedAt"`
			ActiveGamePlaying string   `json:"activeGamePlaying"`
			LastGamePlaying   []string `json:"lastGamePlaying"`
		} `json:"user"`
	} `json:"data"`
}

type F888Game001Request struct {
	Username string `json:"username" binding:"required"`
}
type F888GameList struct {
	GameId          int64  `json:"gameId"`
	GameCode        string `json:"gameCode"`
	GameSlug        string `json:"gameSlug"`
	GameName        string `json:"gameName"`
	GameDescription string `json:"gameDescription"`
	GameIcon        string `json:"gameIcon"`
	GameType        string `json:"gameType"`
}
type F888GameResponse struct {
	Name      string         `json:"name"`
	GameBrand string         `json:"gameBrand"`
	Logo      string         `json:"logo"`
	Type      string         `json:"type"`
	List      []F888GameList `json:"list"`
}
type F888Game001Response struct {
	Data map[string]F888GameResponse `json:"data"`
}

type F888Game002Request struct {
	GameBrand string `json:"gameBrand" binding:"required"`
	GameCode  string `json:"gameCode" binding:"required"`
	Redirect  string `json:"redirect" binding:"required"`
}
type F888Game002Response struct {
	Data  string      `json:"data"`
	Error interface{} `json:"error"`
}

type Agf888User struct {
	Id           int64
	MemberCode   string
	Fullname     string
	Credit       float64
	Remark       string
	LastActionAt *time.Time
	CreatedAt    string
	UpdatedAt    string
	DeletedAt    gorm.DeletedAt
}
type Agf888UserResponse struct {
	Id           int64      `json:"id"`
	MemberCode   string     `json:"memberCode"`
	Fullname     string     `json:"fullname"`
	Credit       float64    `json:"credit"`
	Remark       string     `json:"remark"`
	LastActionAt *time.Time `json:"lastActionAt"`
	CreatedAt    string     `json:"createdAt"`
	UpdatedAt    string     `json:"updatedAt"`
}
type Agf888UserCreateBody struct {
	Id         int64  `json:"id"`
	MemberCode string `json:"memberCode"`
	Fullname   string `json:"fullname"`
	Remark     string `json:"remark"`
}
type Agf888UserUpdateBody struct {
	Fullname     *string    `json:"fullname"`
	Remark       *string    `json:"remark"`
	LastActionAt *time.Time `json:"lastActionAt"`
}

const (
	AGF888_USER_TRANSACTION_TYPE_DEPOSIT     = 1
	AGF888_USER_TRANSACTION_TYPE_WITHDRAW    = 2
	AGF888_USER_TRANSACTION_TYPE_BONUS       = 3
	AGF888_USER_TRANSACTION_TYPE_CREDIT_BACK = 4
	AGF888_USER_TRANSACTION_TYPE_BETPLACE    = 5
	AGF888_USER_TRANSACTION_TYPE_BETSETTLE   = 6
	AGF888_USER_TRANSACTION_TYPE_BETVOID     = 7
)

type Agf888UserTransaction struct {
	Id           int64
	UserId       int64
	TypeId       int64
	Ref1         string
	Ref2         string
	Detail       string
	TransferAt   time.Time
	CreditBefore float64
	CreditAmount float64
	CreditAfter  float64
	CreatedAt    string
}
type Agf888UserTransactionResponse struct {
	Id           int64     `json:"id"`
	UserId       int64     `json:"userId"`
	TypeId       int64     `json:"typeId"`
	Ref1         string    `json:"ref1"`
	Ref2         string    `json:"ref2"`
	Detail       string    `json:"detail"`
	TransferAt   time.Time `json:"transferAt"`
	CreditBefore float64   `json:"creditBefore"`
	CreditAmount float64   `json:"creditAmount"`
	CreditAfter  float64   `json:"creditAfter"`
	CreatedAt    string    `json:"createdAt"`
}
type Agf888UserTransactionCreateBody struct {
	Id           int64     `json:"id"`
	UserId       int64     `json:"userId"`
	TypeId       int64     `json:"typeId"`
	Ref1         string    `json:"ref1"`
	Ref2         string    `json:"ref2"`
	Detail       string    `json:"detail"`
	TransferAt   time.Time `json:"transferAt"`
	CreditBefore float64   `json:"creditBefore"`
	CreditAmount float64   `json:"creditAmount"`
	CreditAfter  float64   `json:"creditAfter"`
}

type Agf888UserPlayLog struct {
	Id                   int64
	UserId               int64
	StatementDate        string
	DailyKey             string
	TurnSport            float64
	ValidAmountSport     float64
	WinLoseSport         float64
	TurnCasino           float64
	ValidAmountCasino    float64
	WinLoseCasino        float64
	TurnGame             float64
	ValidAmountGame      float64
	WinLoseGame          float64
	TurnLottery          float64
	ValidAmountLottery   float64
	WinLoseLottery       float64
	TurnP2P              float64
	ValidAmountP2P       float64
	WinLoseP2P           float64
	TurnFinancial        float64
	ValidAmountFinancial float64
	WinLoseFinancial     float64
	TurnTotal            float64
	WinLoseTotal         float64
	ValidAmountTotal     float64
	CreatedAt            string
}
type Agf888UserPlayLogResponse struct {
	Id                   int64   `json:"id"`
	UserId               int64   `json:"userId"`
	StatementDate        string  `json:"statementDate"`
	DailyKey             string  `json:"dailyKey"`
	TurnSport            float64 `json:"turnSport"`
	ValidAmountSport     float64 `json:"validAmountSport"`
	WinLoseSport         float64 `json:"winLoseSport"`
	TurnCasino           float64 `json:"turnCasino"`
	ValidAmountCasino    float64 `json:"validAmountCasino"`
	WinLoseCasino        float64 `json:"winLoseCasino"`
	TurnGame             float64 `json:"turnGame"`
	ValidAmountGame      float64 `json:"validAmountGame"`
	WinLoseGame          float64 `json:"winLoseGame"`
	TurnLottery          float64 `json:"turnLottery"`
	ValidAmountLottery   float64 `json:"validAmountLottery"`
	WinLoseLottery       float64 `json:"winLoseLottery"`
	TurnP2P              float64 `json:"turnP2P"`
	ValidAmountP2P       float64 `json:"validAmountP2P"`
	WinLoseP2P           float64 `json:"winLoseP2P"`
	TurnFinancial        float64 `json:"turnFinancial"`
	ValidAmountFinancial float64 `json:"validAmountFinancial"`
	WinLoseFinancial     float64 `json:"winLoseFinancial"`
	TurnTotal            float64 `json:"turnTotal"`
	WinLoseTotal         float64 `json:"winLoseTotal"`
	ValidAmountTotal     float64 `json:"validAmountTotal"`
	CreatedAt            string  `json:"createdAt"`
}
type Agf888UserPlayLogCreateBody struct {
	Id                   int64   `json:"id"`
	UserId               int64   `json:"userId"`
	StatementDate        string  `json:"statementDate"`
	DailyKey             string  `json:"dailyKey"`
	TurnSport            float64 `json:"turnSport"`
	ValidAmountSport     float64 `json:"validAmountSport"`
	WinLoseSport         float64 `json:"winLoseSport"`
	TurnCasino           float64 `json:"turnCasino"`
	ValidAmountCasino    float64 `json:"validAmountCasino"`
	WinLoseCasino        float64 `json:"winLoseCasino"`
	TurnGame             float64 `json:"turnGame"`
	ValidAmountGame      float64 `json:"validAmountGame"`
	WinLoseGame          float64 `json:"winLoseGame"`
	TurnLottery          float64 `json:"turnLottery"`
	ValidAmountLottery   float64 `json:"validAmountLottery"`
	WinLoseLottery       float64 `json:"winLoseLottery"`
	TurnP2P              float64 `json:"turnP2P"`
	ValidAmountP2P       float64 `json:"validAmountP2P"`
	WinLoseP2P           float64 `json:"winLoseP2P"`
	TurnFinancial        float64 `json:"turnFinancial"`
	ValidAmountFinancial float64 `json:"validAmountFinancial"`
	WinLoseFinancial     float64 `json:"winLoseFinancial"`
	TurnTotal            float64 `json:"turnTotal"`
	WinLoseTotal         float64 `json:"winLoseTotal"`
	ValidAmountTotal     float64 `json:"validAmountTotal"`
}

type AgentF888RegisterRequest struct {
	MemberCode string `form:"memberCode" binding:"required"`
	Fullname   string `form:"fullname" binding:"required"`
	Remark     string `form:"remark"`
}
type AgentF888RegisterResponse struct {
	Id         int64  `json:"id"`
	MemberCode string `json:"memberCode"`
}
type AgentF888LoginRequest struct {
	MemberCode string `form:"memberCode" binding:"required"`
}
type AgentF888LoginResponse struct {
	Id         int64   `json:"id"`
	MemberCode string  `json:"memberCode"`
	Credit     float64 `json:"credit"`
	GameToken  string  `json:"gameToken"`
}
type AgentF888ChangePasswordRequest struct {
	MemberCode      string `form:"memberCode" binding:"required"`
	Password        string `form:"password" binding:"required"`
	ConfirmPassword string `form:"confirmPassword" binding:"required"`
	UserId          int64  `json:"-" validate:"required"`
}
type AgentF888ChangePasswordResponse struct {
	Id         int64  `json:"id"`
	MemberCode string `json:"memberCode"`
}

type AgentF888GameListRequest struct {
	GameToken string `form:"gameToken" binding:"required"`
	UserId    int64  `json:"-" validate:"required"`
}
type AgentF888GameListResponse struct {
	Name      string `json:"name"`
	GameBrand string `json:"gameBrand"`
	Logo      string `json:"logo"`
	Type      string `json:"type"`
}
type AgentF888GameUrlRequest struct {
	GameToken string `form:"gameToken" binding:"required"`
	GameBrand string `form:"gameBrand" binding:"required"`
	GameCode  string `form:"gameCode" binding:"required"`
	Redirect  string `form:"redirect"`
	UserId    int64  `json:"-" validate:"required"`
}
type AgentF888GameUrlResponse struct {
	GameUrl string `json:"gameUrl"`
}
type AgentF888DepositRequest struct {
	MemberCode string  `form:"memberCode" binding:"required"`
	Amount     float64 `form:"amount" binding:"required"`
	UserId     int64   `json:"-" validate:"required"`
}
type AgentF888DepositResponse struct {
	MemberCode string  `json:"memberCode"`
	Balance    float64 `json:"credit"`
}
type AgentF888WithdrawRequest struct {
	MemberCode string  `form:"memberCode" binding:"required"`
	Amount     float64 `form:"amount" binding:"required"`
	UserId     int64   `json:"-" validate:"required"`
}
type AgentF888WithdrawResponse struct {
	MemberCode string  `json:"memberCode"`
	Balance    float64 `json:"credit"`
}
type AgentF888PlayLogRequest struct {
	StatementDate string `form:"statementDate" binding:"required"`
}
type AgentF888PlayLogResponse struct {
	StatementDate    string  `json:"statementDate"`
	MemberCode       string  `json:"memberCode"`
	TotalTurn        float64 `json:"totalTurn"`
	TotalWinLose     float64 `json:"totalWinLose"`
	TotalValidAmount float64 `json:"totalValidAmount"`
}
