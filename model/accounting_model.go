package model

import (
	"time"

	"gorm.io/gorm"
)

// connection status
const (
	CONNECTION_CONNECTED    int64 = 1
	CONNECTION_DISCONNECTED int64 = 2
)

// bank account type
const (
	BANK_ACCOUNT_TYPE_DEPOSIT_ONLY  int64 = 1
	BANK_ACCOUNT_TYPE_WITHDRAW_ONLY int64 = 2
	BANK_ACCOUNT_TYPE_BOTH          int64 = 3
	BANK_ACCOUNT_TYPE_HOLD          int64 = 4
)

const (
	BANK_ID_KBANK    int64 = 1 // กสิกรไทย
	BANK_ID_SCB      int64 = 2 // ไทยพาณิชย์
	BANK_ID_BBL      int64 = 3 //กรุงเทพ
	BANK_ID_BAY      int64 = 4 //กรุงศรี
	BANK_ID_KTB      int64 = 5 // กรุงไทย
	BANK_ID_TTB      int64 = 6 // ทีเอ็มบีธนชาต
	BANK_ID_GSB      int64 = 7
	BANK_ID_BAAC     int64 = 8
	BANK_ID_KKP      int64 = 9 // เกียรตินาคิน
	BANK_ID_GHB      int64 = 10
	BANK_ID_UOB      int64 = 11
	BANK_ID_LH       int64 = 12
	BANK_ID_CIMB     int64 = 13
	BANK_ID_HSBC     int64 = 14
	BANK_ID_ICBC     int64 = 15
	BANK_ID_ISBT     int64 = 16
	BANK_ID_TISCO    int64 = 17
	BANK_ID_CITI     int64 = 18
	BANK_ID_SCBT     int64 = 19
	BANK_ID_TRUE     int64 = 20
	BANK_ID_EXTERNAL int64 = 21
	UNKNOWN_BANK     int64 = 100
)

type ConnectionStatusResponse struct {
	Id      int64  `json:"id"`
	Name    string `json:"name"`
	LabelTh string `json:"labelTh"`
	LabelEn string `json:"labelEn"`
}
type AccountStatusResponse struct {
	Id      int64  `json:"id"`
	Name    string `json:"name"`
	LabelTh string `json:"labelTh"`
	LabelEn string `json:"labelEn"`
}

type ConfirmRequest struct {
	Password string `json:"password"`
	UserId   int64  `json:"-"`
}

type Bank struct {
	Id        int64     `json:"id"`
	Name      string    `json:"name"`
	Code      string    `json:"code"`
	IconUrl   string    `json:"iconUrl"`
	TypeFlag  string    `json:"typeFlag"`
	CreatedAt time.Time `json:"createdAt"`
}
type BankResponse struct {
	Id       int64  `json:"id"`
	Name     string `json:"name"`
	Code     string `json:"code"`
	IconUrl  string `json:"iconUrl"`
	TypeFlag string `json:"typeFlag"`
}
type BankListRequest struct {
	Page    int    `form:"page" default:"1" min:"1"`
	Limit   int    `form:"limit" default:"10" min:"1" max:"100"`
	Search  string `form:"search"`
	SortCol string `form:"sortCol"`
	SortAsc string `form:"sortAsc"`
}
type BankListResponse struct {
	Id    int `json:"id"`
	Total int `json:"total"`
}

type AccountType struct {
	Id        int64     `json:"id"`
	Name      string    `json:"name"`
	TypeFlag  string    `json:"typeFlag"`
	CreatedAt time.Time `json:"createdAt"`
}
type AccountTypeResponse struct {
	Id   int64  `json:"id"`
	Name string `json:"name"`
}
type AccountTypeListRequest struct {
	Page    int    `form:"page" default:"1" min:"1"`
	Limit   int    `form:"limit" default:"10" min:"1" max:"100"`
	Search  string `form:"search"`
	SortCol string `form:"sortCol"`
	SortAsc string `form:"sortAsc"`
}
type AccountTypeListResponse struct {
	Id    int `json:"id"`
	Total int `json:"total"`
}

type BankAccount struct {
	Id                      int64      `json:"id"`
	BankId                  int64      `json:"bankId"`
	BankCode                string     `json:"bankCode"`
	BankName                string     `json:"bankName"`
	BankIconUrl             string     `json:"bankIconUrl"`
	AccountTypeId           int64      `json:"accountTypeId"`
	AccountTypeName         string     `json:"accountTypeName"`
	AccountName             string     `json:"accountName"`
	AccountNumber           string     `json:"accountNumber"`
	AccountBalance          float64    `json:"accountBalance" sql:"type:decimal(14,2);"`
	AccountPriorityWithdraw int64      `json:"accountPriorityWithdraw"`
	AccountStatusName       string     `json:"accountStatusName"`
	DeviceUid               string     `json:"deviceUid"`
	PinCode                 string     `json:"pinCode"`
	ConnectionStatusId      int64      `json:"connectionStatusId"`
	ConnectionStatusName    string     `json:"connectionStatusName"`
	ExternalId              string     `json:"-"`
	LastConnUpdateAt        *time.Time `json:"lastConnUpdateAt"`
	AutoWithdrawTypeId      int64      `json:"autoWithdrawTypeId"`
	// BankWithdrawMaximum     float64        `json:"bankWithdrawMaximum"`
	// AutoWithdrawMaximum     float64        `json:"autoWithdrawMaximum"`
	CreatedAt                  time.Time      `json:"createdAt"`
	UpdatedAt                  *time.Time     `json:"updatedAt"`
	DeletedAt                  gorm.DeletedAt `json:"deletedAt"`
	SmsMode                    bool           `json:"smsMode"`
	IsManualBank               bool           `json:"isManualBank"`
	BankUseCurrency            string         `json:"bankUseCurrency"`
	ImageUrl                   string         `json:"imageUrl"`
	ShowBankDepositOverDueTime int64          `json:"showBankDepositOverDueTime"`
	ShowBankDepositMaxDueTime  int64          `json:"showBankDepositMaxDueTime"`
}

type BankGetByIdRequest struct {
	Id int64 `uri:"id" binding:"required"`
}

type BankAccountListRequest struct {
	AccountNumber string `form:"accountNumber"`
	AccountType   string `form:"accountType"`
	Page          int    `form:"page" default:"1" min:"1"`
	Limit         int    `form:"limit" default:"30" min:"1" max:"300"`
	Search        string `form:"search"`
	SortCol       string `form:"sortCol"`
	SortAsc       string `form:"sortAsc"`
}

type BankAccountCreateRequest struct {
	Id                 int64   `json:"-"`
	BankId             int64   `json:"bankId" validate:"required"`
	AccountTypeId      int64   `json:"accountTypeId" validate:"required"`
	AccountName        string  `json:"accountName" validate:"required"`
	AccountNumber      string  `json:"accountNumber" validate:"required"`
	AccountBalance     float64 `json:"-"`
	DeviceUid          string  `json:"deviceUid"`
	PinCode            string  `json:"pinCode"`
	ExternalId         int64   `json:"-"`
	AutoWithdrawTypeId int64   `json:"autoWithdrawTypeId"`
	// BankWithdrawMaximum float64 `json:"bankWithdrawMaximum"`
	// AutoWithdrawMaximum float64 `json:"autoWithdrawMaximum"`
	ConnectionStatusId         int64   `json:"-"`
	CreateBy                   int64   `json:"-"`
	SmsMode                    bool    `json:"smsMode"`
	IsManualBank               bool    `json:"isManualBank"`
	ShowToBank                 []int64 `json:"showToBank"`
	ImageUrl                   string  `json:"imageUrl"`
	ShowBankDepositOverDueTime int64   `json:"showBankDepositOverDueTime"`
	ShowBankDepositMaxDueTime  int64   `json:"showBankDepositMaxDueTime"`
}

type BankAccountShowBank struct {
	Id            int64      `json:"id"`
	BankAccountId int64      `json:"bankAccountId"`
	BankId        int64      `json:"bankId"`
	CreatedAt     time.Time  `json:"createdAt"`
	UpdatedAt     *time.Time `json:"updatedAt"`
}

type CreateBankAccountShowBank struct {
	BankAccountId int64 `json:"bankAccountId"`
	BankId        int64 `json:"bankId"`
}
type GetBankAccountShowBank struct {
	Id            int64 `json:"id"`
	BankAccountId int64 `json:"bankAccountId"`
	BankId        int64 `json:"bankId"`
}

type BankAccountCreateBody struct {
	Id                 int64   `json:"-"`
	BankId             int64   `json:"bankId" validate:"required"`
	AccountTypeId      int64   `json:"accountTypeId" validate:"required"`
	AccountName        string  `json:"accountName" validate:"required"`
	AccountNumber      string  `json:"accountNumber" validate:"required"`
	AccountBalance     float64 `json:"-"`
	DeviceUid          string  `json:"deviceUid"`
	PinCode            string  `json:"pinCode"`
	AutoWithdrawTypeId int64   `json:"autoWithdrawTypeId"`
	// BankWithdrawMaximum float64   `json:"bankWithdrawMaximum"`
	// AutoWithdrawMaximum float64   `json:"autoWithdrawMaximum"`
	ConnectionStatusId         int64     `json:"-"`
	AdminUpdatedAt             time.Time `json:"adminUpdatedAt"`
	SmsMode                    bool      `json:"smsMode"`
	IsManualBank               bool      `json:"isManualBank"`
	ImageUrl                   string    `json:"imageUrl"`
	ShowBankDepositOverDueTime int64     `json:"showBankDepositOverDueTime"`
	ShowBankDepositMaxDueTime  int64     `json:"showBankDepositMaxDueTime"`
}

type BankAccountUpdateRequest struct {
	BankId             *int64  `json:"bankId"`
	AccountTypeId      *int64  `json:"accountTypeId"`
	AccountName        *string `json:"accountName"`
	AccountNumber      *string `json:"accountNumber"`
	IsShowFront        *bool   `json:"isShowFront"`
	DeviceUid          *string `json:"deviceUid"`
	PinCode            *string `json:"pinCode"`
	ConnectionStatus   *bool   `json:"-"`
	AutoWithdrawTypeId *int64  `json:"autoWithdrawTypeId"`
	// BankWithdrawMaximum *float64 `json:"bankWithdrawMaximum"`
	// AutoWithdrawMaximum *float64 `json:"autoWithdrawMaximum"`
	UpdateBy                   int64   `json:"-"`
	SmsMode                    *bool   `json:"smsMode"`
	IsManualBank               *bool   `json:"isManualBank"`
	ShowToBank                 []int64 `json:"showToBank"`
	ImageUrl                   *string `json:"imageUrl"`
	ShowBankDepositOverDueTime *int64  `json:"showBankDepositOverDueTime"`
	ShowBankDepositMaxDueTime  *int64  `json:"showBankDepositMaxDueTime"`
}

type BankAccountDeleteRequest struct {
	AccountId int64 `json:"accountId"`
	UpdateBy  int64 `json:"-"`
}

type BankAccountUpdateBody struct {
	BankId                  *int64     `json:"-"`
	AccountTypeId           *int64     `json:"accountTypeId"`
	AccountName             *string    `json:"-"`
	AccountNumber           *string    `json:"-"`
	DeviceUid               *string    `json:"deviceUid"`
	PinCode                 *string    `json:"pinCode"`
	ExternalId              *int64     `json:"-"`
	IsShowFront             *bool      `json:"-"`
	LastConnUpdateAt        *time.Time `json:"-"`
	ConnectionStatusId      *int64     `json:"-"`
	AccountBalance          *float64   `json:"-"`
	AccountPriorityWithdraw *int64     `json:"accountPriorityWithdraw"`
	AutoWithdrawTypeId      *int64     `json:"autoWithdrawTypeId"`
	// BankWithdrawMaximum     *float64   `json:"bankWithdrawMaximum"`
	// AutoWithdrawMaximum     *float64   `json:"autoWithdrawMaximum"`
	AdminUpdatedAt             time.Time `json:"adminUpdatedAt"`
	SmsMode                    *bool     `json:"smsMode"`
	IsManualBank               *bool     `json:"isManualBank"`
	ImageUrl                   *string   `json:"imageUrl"`
	ShowBankDepositOverDueTime *int64    `json:"showBankDepositOverDueTime"`
	ShowBankDepositMaxDueTime  *int64    `json:"showBankDepositMaxDueTime"`
}

type BankAccountDeleteBody struct {
	AccountNumber      string    `json:"-"`
	DeletedAt          time.Time `json:"-"`
	ConnectionStatusId int64     `json:"connectionStatusId"`
}

type BankAccountResponse struct {
	Id                      int64   `json:"id"`
	BankId                  int64   `json:"bankId"`
	BankCode                string  `json:"bankCode"`
	BankName                string  `json:"bankName"`
	BankIconUrl             string  `json:"bankIconUrl"`
	AccountTypeId           int64   `json:"accountTypeId"`
	AccountTypeName         string  `json:"accountTypeName"`
	IsShowFront             bool    `json:"isShowFront"`
	AccountName             string  `json:"accountName"`
	AccountNumber           string  `json:"accountNumber"`
	AccountBalance          float64 `json:"accountBalance"`
	AccountPriorityWithdraw int64   `json:"accountPriorityWithdraw"`
	DeviceUid               string  `json:"deviceUid"`
	AutoWithdrawTypeId      int64   `json:"autoWithdrawTypeId"`
	// BankWithdrawMaximum     float64        `json:"bankWithdrawMaximum"`
	// AutoWithdrawMaximum     float64        `json:"autoWithdrawMaximum"`
	AccountStatusName          string         `json:"accountStatusName"`
	ConnectionStatusId         int64          `json:"connectionStatusId"`
	ConnectionStatusName       string         `json:"connectionStatusName"`
	LastConnUpdateAt           *time.Time     `json:"lastConnUpdateAt"`
	CreatedAt                  time.Time      `json:"createdAt"`
	UpdatedAt                  *time.Time     `json:"updatedAt"`
	DeletedAt                  gorm.DeletedAt `json:"deletedAt"`
	AdminUpdatedAt             time.Time      `json:"adminUpdatedAt"`
	SmsMode                    bool           `json:"smsMode"`
	IsManualBank               bool           `json:"isManualBank"`
	ShowToBank                 []int64        `json:"showToBank" gorm:"-"`
	ImageUrl                   string         `json:"imageUrl"`
	ShowBankDepositOverDueTime int64          `json:"showBankDepositOverDueTime"`
	ShowBankDepositMaxDueTime  int64          `json:"showBankDepositMaxDueTime"`
}

type WebBankAccountResponse struct {
	Id                         int64   `json:"id"`
	BankId                     int64   `json:"bankId"`
	BankCode                   string  `json:"bankCode"`
	BankName                   string  `json:"bankName"`
	BankIconUrl                string  `json:"bankIconUrl"`
	AccountName                string  `json:"accountName"`
	AccountNumber              string  `json:"accountNumber"`
	CountryCode                string  `json:"countryCode"`
	UseCurrency                string  `json:"useCurrency"`
	MinimumDeposit             float64 `json:"minimumDeposit"`
	MinimumWithdraw            float64 `json:"minimumWithdraw"`
	SmsMode                    bool    `json:"smsMode"`
	ImageUrl                   string  `json:"imageUrl"`
	ShowBankDepositOverDueTime int64   `json:"showBankDepositOverDueTime"`
	ShowBankDepositMaxDueTime  int64   `json:"showBankDepositMaxDueTime"`
}

type BankAccountTransaction struct {
	Id                int64          `json:"id"`
	AccountId         int64          `json:"accountId"`
	Description       string         `json:"description"`
	TransactionTypeId int64          `json:"transactionTypeId"`
	TransactionTypeTh string         `json:"transactionTypeTh"`
	TransactionTypeEn string         `json:"transactionTypeEn"`
	Amount            float64        `json:"amount" sql:"type:decimal(14,2);"`
	TransferAt        time.Time      `json:"transferAt"`
	CreatedByUsername string         `json:"createdByUsername"`
	CreatedAt         time.Time      `json:"createdAt"`
	UpdatedAt         *time.Time     `json:"updatedAt"`
	DeletedAt         gorm.DeletedAt `json:"deletedAt"`
}

type BankAccountTransactionListRequest struct {
	AccountId         int    `form:"accountId"`
	FromCreatedDate   string `form:"fromCreatedDate"`
	ToCreatedDate     string `form:"toCreatedDate"`
	TransactionTypeId *int64 `form:"transactionTypeId"`
	Search            string `form:"search"`
	Page              int    `form:"page" default:"1" min:"1"`
	Limit             int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol           string `form:"sortCol"`
	SortAsc           string `form:"sortAsc"`
}

type BankAccountTransactionBody struct {
	AccountId         int64     `json:"accountId" validate:"required"`
	Description       string    `json:"description"`
	TransactionTypeId int64     `json:"transactionTypeId" validate:"required"`
	Amount            float64   `json:"amount" validate:"required"`
	TransferAt        time.Time `json:"transferAt" validate:"required"`
	CreatedByUsername string    `json:"-"`
}

type BankAccountTransactionResponse struct {
	Id                int64          `json:"id"`
	AccountId         int64          `json:"accountId"`
	BankName          string         `json:"bankName"`
	AccountName       string         `json:"accountName"`
	AccountNumber     string         `json:"accountNumber"`
	Description       string         `json:"description"`
	TransactionTypeId int64          `json:"transactionTypeId"`
	TransactionTypeTh string         `json:"transactionTypeTh"`
	TransactionTypeEn string         `json:"transactionTypeEn"`
	Amount            float64        `json:"amount" sql:"type:decimal(14,2);"`
	TransferAt        time.Time      `json:"transferAt"`
	CreatedByUsername string         `json:"createdByUsername"`
	CreatedAt         time.Time      `json:"createdAt"`
	UpdatedAt         *time.Time     `json:"updatedAt"`
	DeletedAt         gorm.DeletedAt `json:"deletedAt"`
}

type BankAccountTransfer struct {
	Id                 int64          `json:"id"`
	FromAccountId      int64          `json:"fromAccountId"`
	FromBankId         int64          `json:"fromBankId"`
	FromBankName       string         `json:"fromBankName"`
	FromAccountName    string         `json:"fromAccountName"`
	FromAccountNumber  string         `json:"fromAccountNumber"`
	ToAccountId        int64          `json:"toAccountId"`
	ToBankId           int64          `json:"toBankId"`
	ToBankName         string         `json:"toBankName"`
	ToAccountName      string         `json:"toAccountName"`
	ToAccountNumber    string         `json:"toAccountNumber"`
	Amount             float64        `json:"amount" sql:"type:decimal(14,2);"`
	TransferAt         time.Time      `json:"transferAt"`
	CreatedByUsername  string         `json:"createdByUsername"`
	Status             string         `json:"status"`
	ConfirmedAt        time.Time      `json:"confirmedAt"`
	ConfirmedByAdminId int64          `json:"confirmedByAdminId"`
	CreatedAt          time.Time      `json:"createdAt"`
	UpdatedAt          *time.Time     `json:"updatedAt"`
	DeletedAt          gorm.DeletedAt `json:"deletedAt"`
}

type BankAccountTransferListRequest struct {
	AccountId       int    `form:"accountId"`
	FromCreatedDate string `form:"fromCreatedDate"`
	ToCreatedDate   string `form:"toCreatedDate"`
	ToAccountId     int    `form:"toAccountId"`
	Status          string `form:"status"`
	Search          string `form:"search"`
	Page            int    `form:"page" default:"1" min:"1"`
	Limit           int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol         string `form:"sortCol"`
	SortAsc         string `form:"sortAsc"`
}

type BankAccountTransferBody struct {
	StatementStatusId int64     `json:"-"`
	FromAccountId     int64     `json:"fromAccountId" validate:"required"`
	FromBankId        int64     `json:"-"`
	FromAccountName   string    `json:"-"`
	FromAccountNumber string    `json:"-"`
	ToAccountId       int64     `json:"toAccountId" validate:"required"`
	ToBankId          int64     `json:"-"`
	ToAccountName     string    `json:"-"`
	ToAccountNumber   string    `json:"-"`
	Amount            float64   `json:"amount" validate:"required"`
	TransferAt        time.Time `json:"transferAt" validate:"required"`
	CreatedByUsername string    `json:"-"`
}

type BankAccountTransferConfirmBody struct {
	StatementStatusId  int64     `json:"statementStatusId" validate:"required"`
	ConfirmedByAdminId *int64    `json:"confirmedByAdminId" validate:"required"`
	ConfirmedAt        time.Time `json:"confirmedAt" validate:"required"`
}

type BankAccountTransferResponse struct {
	Id                 int64          `json:"id"`
	FromAccountId      int64          `json:"fromAccountId"`
	FromBankId         int64          `json:"fromBankId"`
	FromBankName       string         `json:"fromBankName"`
	FromAccountName    string         `json:"fromAccountName"`
	FomAccountNumber   string         `json:"fromAccountNumber"`
	ToAccountId        int64          `json:"toAccountId"`
	ToBankId           int64          `json:"toBankId"`
	ToBankName         string         `json:"toBankName"`
	ToAccountName      string         `json:"toAccountName"`
	ToAccountNumber    string         `json:"toAccountNumber"`
	Amount             float64        `json:"amount" sql:"type:decimal(14,2);"`
	TransferAt         time.Time      `json:"transferAt"`
	CreatedByUsername  string         `json:"createdByUsername"`
	Status             string         `json:"status"`
	ConfirmedAt        time.Time      `json:"confirmedAt"`
	ConfirmedByAdminId int64          `json:"confirmedByAdminId"`
	CreatedAt          time.Time      `json:"createdAt"`
	UpdatedAt          *time.Time     `json:"updatedAt"`
	DeletedAt          gorm.DeletedAt `json:"deletedAt"`
}

type BankAccountStatementListRequest struct {
	AccountId        int64  `form:"accountId" binding:"required"`
	StatementTypeId  int64  `form:"statementTypeId" extensions:"x-order:2"`
	FromTransferDate string `form:"fromTransferDate" extensions:"x-order:3"`
	ToTransferDate   string `form:"toTransferDate" extensions:"x-order:4"`
	Search           string `form:"search" extensions:"x-order:5"`
	Page             int    `form:"page" default:"1" min:"1"`
	Limit            int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol          string `form:"sortCol"`
	SortAsc          string `form:"sortAsc"`
}

type CustomerAccountInfoRequest struct {
	AccountFrom string `form:"-" json:"accountFrom"`
	AccountTo   string `form:"accountTo" json:"accountTo" validate:"required"`
	BankCode    string `form:"bankCode" json:"bankCode" validate:"required"`
}

type ExternalSettings struct {
	ApiEndpoint          string `json:"apiEndpoint"`
	ApiKey               string `json:"apiKey"`
	LocalWebhookEndpoint string `json:"localWebhookEndpoint"`
}

type ExternalReponseStatus struct {
	Code        int64  `json:"code"`
	Header      string `json:"header"`
	Description string `json:"description"`
}

type CustomerAccountInfoReponse struct {
	Data   CustomerAccountInfo   `json:"data"`
	Status ExternalReponseStatus `json:"status"`
}

type CustomerAccountInfo struct {
	AccountToName        string `json:"accountToName"`
	AccountTo            string `json:"accountTo"`
	AccountToDisplayName string `json:"accountToDisplayName"`
}

type ExternalAccount struct {
	BankId           int64   `json:"bankId"`
	BankCode         string  `json:"bankCode"`
	ClientName       string  `json:"clientName"`
	LastConnected    *int64  `json:"lastConnected"`
	CustomerId       int64   `json:"customerId"`
	DeviceId         string  `json:"deviceId"`
	WebhookUrl       *string `json:"webhookUrl"`
	WalletId         *int64  `json:"walletId"`
	Enable           bool    `json:"enable"`
	AccountNo        string  `json:"accountNo"`
	BankAccountId    *int64  `json:"bankAccountId"`
	VerifyLogin      bool    `json:"verifyLogin"`
	WebhookNotifyUrl *string `json:"webhookNotifyUrl"`
	Username         *string `json:"username"`
}

type BankAccountToggleFastbankRequest struct {
	AccountNo string `json:"accountNo"`
	Enable    bool   `json:"enable"`
	UpdateBy  int64  `json:"-"`
}

type ExternalAccountStatusRequest struct {
	AccountNumber string `json:"accountNumber"`
}
type ExternalAccountEnableRequest struct {
	AccountNo string `json:"accountNo"`
	Enable    bool   `json:"enable"`
}

type ExternalAccountBalance struct {
	LimitUsed            float64 `json:"limitUsed"`
	BranchId             string  `json:"branchId"`
	AccountName          string  `json:"accountName"`
	DailyLimitOtherBanks float64 `json:"dailyLimitOtherBanks"`
	DailyLimitPromptPay  float64 `json:"dailyLimitPromptPay"`
	AccruedInterest      float64 `json:"accruedInterest"`
	OverdraftLimit       float64 `json:"overdraftLimit"`
	DailyLimitSCBOther   float64 `json:"dailyLimitSCBOther"`
	DailyLimitSCBOwn     float64 `json:"dailyLimitSCBOwn"`
	AvailableBalance     string  `json:"availableBalance"`
	AccountNo            string  `json:"accountNo"`
	Currency             string  `json:"currency"`
	AccountBalance       string  `json:"accountBalance"`
	Status               struct {
		Code        int    `json:"code"`
		Header      string `json:"header"`
		Description string `json:"description"`
	} `json:"status"`
}
type ExternalAccountStatus struct {
	Success bool   `json:"success"`
	Enable  bool   `json:"enable"`
	Status  string `json:"status"`
}
type GetExternalAccountStatus struct {
	Success     bool   `json:"success"`
	Enable      bool   `json:"enable"`
	Status      string `json:"status"`
	VerifyLogin bool   `json:"verifyLogin"`
}
type UpdateAllBankAccountBotStatusResponse struct {
	Id            int64  `json:"id"`
	BankId        int64  `json:"bankId"`
	BankCode      string `json:"bankCode"`
	AccountName   string `json:"accountName"`
	AccountNumber string `json:"accountNumber"`
	Enable        bool   `json:"enable"`
	Status        string `json:"status"`
	VerifyLogin   bool   `json:"verifyLogin"`
}

type ExternalAccountCreateBody struct {
	AccountNo   string `json:"accountNo"`
	BankCode    string `json:"bankCode"`
	DeviceId    string `json:"deviceId"`
	AccountName string `json:"accountName"`
	// Password         string `json:"password"`
	Pin *string `json:"pin"`
	// Username         string `json:"username"`
	WebhookNotifyUrl string `json:"webhookNotifyUrl"`
	WebhookUrl       string `json:"webhookUrl"`
	SmsMode          bool   `json:"smsMode"`
}

type ExternalAccountUpdateBody struct {
	AccountName string `json:"accountName"`
	AccountNo   string `json:"accountNo"`
	BankCode    string `json:"bankCode"`
	DeviceId    string `json:"deviceId"`
	Pin         string `json:"pin"`
	WebhookUrl  string `json:"webhookUrl"`
	SmsMode     bool   `json:"smsMode"`
}

type ExternalAccountCreateResponse struct {
	Id               int64  `json:"id"`
	CustomerId       int64  `json:"customerId"`
	ApiKey           string `json:"apiKey"`
	BankId           int64  `json:"bankId"`
	BankCode         string `json:"bankCode"`
	DeviceId         string `json:"deviceId"`
	AccountNo        string `json:"accountNo"`
	Pin              string `json:"pin"`
	Username         string `json:"username"`
	Password         string `json:"password"`
	WebhookUrl       string `json:"webhookUrl"`
	WebhookNotifyUrl string `json:"webhookNotifyUrl"`
	WalletId         int64  `json:"walletId"`
	Enable           bool   `json:"enable"`
	VerifyLogin      bool   `json:"verifyLogin"`
	Deleted          bool   `json:"deleted"`
}

type ExternalListWithPagination struct {
	Content       interface{} `json:"content"`
	TotalElements int64       `json:"totalElements"`
}

type ExternalAccountLog struct {
	Id          int64  `json:"id"`
	ExternalId  int64  `json:"externalId"`
	ClientName  string `json:"clientName"`
	LogType     string `json:"logType"`
	Message     string `json:"message"`
	CreatedDate int64  `json:"createdDate"`
}

type ExternalAccountLogCreateBody struct {
	ExternalId         int64  `json:"externalId"`
	LogType            string `json:"logType"`
	Message            string `json:"message"`
	ExternalCreateDate string `json:"externalCreateDate"`
}

type ExternalStatementListRequest struct {
	AccountNumber string `form:"accountNumber" validate:"required"`
	OfDateTime    string `form:"ofDateTime"`
	OfDate        string `form:"ofDate"`
	OfTime        string `form:"ofTime"`
	TxnCode       string `form:"txnCode"`
	Page          int    `form:"page" default:"1" min:"1"`
	Limit         int    `form:"limit" default:"10" min:"1" max:"100"`
	Search        string `form:"search"`
	SortCol       string `form:"sortCol"`
	SortAsc       string `form:"sortAsc"`
}

type ExternalStatementListWithPagination struct {
	Content       []ExternalStatement `json:"content"`
	TotalElements int64               `json:"totalElements"`
}

type ExternalStatement struct {
	Id                 int64   `json:"id"`
	Amount             float64 `json:"amount"`
	BankAccountId      int64   `json:"bankAccountId"`
	BankCode           string  `json:"bankCode"`
	ChannelCode        string  `json:"channelCode"`
	ChannelDescription string  `json:"channelDescription"`
	Checksum           string  `json:"checksum"`
	CreatedDate        string  `json:"createdDate"`
	DateTime           string  `json:"dateTime"`
	Info               string  `json:"info"`
	IsRead             bool    `json:"isRead"`
	RawDateTime        string  `json:"rawDateTime"`
	AccountDetail      string  `json:"accountDetail"`
	StatementTypeId    int64   `json:"statementTypeId"`
	StatementStatusId  int64   `json:"statementStatusId"`
	TxnCode            string  `json:"txnCode"`
	TxnDescription     string  `json:"txnDescription"`
	UpdatedDate        string  `json:"updatedDate"`
}

type ExternalStatementWithTransactionListRequest struct {
	AccountId   int64  `form:"accountId" validate:"required"`
	OfDate      string `form:"ofDate"`
	DirectionId *int64 `form:"directionId"`
	StatusId    int64  `form:"statusId"`
	Page        int    `form:"page" default:"1" min:"1"`
	Limit       int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol     string `form:"sortCol"`
	SortAsc     string `form:"sortAsc"`
}

type ExternalStatementWithTransactionReponse struct {
	Id                int64      `json:"id"`
	TransactionAt     *time.Time `json:"transactionAt"`
	DateTime          time.Time  `json:"dateTime"`
	TxnCode           string     `json:"txnCode"`
	TxnDescription    string     `json:"txnDescription"`
	StatementTypeId   int64      `json:"statementTypeId"`
	Amount            float64    `json:"amount"`
	Info              string     `json:"info"`
	WorkSeconds       int64      `json:"workSeconds"`
	WorkStatusCode    string     `json:"workStatusCode"`
	StatusName        string     `json:"statusName"`
	StatementStatusId int64      `json:"statementStatusId"`
}

type ExternalAccountStatementEx struct {
	Id                 int64          `json:"id"`
	ExternalId         int64          `json:"externalId"`
	BankAccountId      int64          `json:"bankAccountId"`
	BankCode           string         `json:"bankCode"`
	Amount             float64        `json:"amount"`
	DateTime           time.Time      `json:"dateTime"`
	RawDateTime        time.Time      `json:"rawDateTime"`
	Info               string         `json:"info"`
	ChannelCode        string         `json:"channelCode"`
	ChannelDescription string         `json:"channelDescription"`
	TxnCode            string         `json:"txnCode"`
	TxnDescription     string         `json:"txnDescription"`
	Checksum           string         `json:"checksum"`
	IsRead             bool           `json:"isRead"`
	ExternalCreateDate string         `json:"externalCreateDate"`
	ExternalUpdateDate string         `json:"externalUpdateDate"`
	CreatedAt          time.Time      `json:"createdAt"`
	UpdatedAt          *time.Time     `json:"updatedAt"`
	DeletedAt          gorm.DeletedAt `json:"deletedAt"`
}

type ExternalAccountStatementCreateBody struct {
	ExternalId         int64   `json:"externalId"`
	BankAccountId      int64   `json:"bankAccountId"`
	BankCode           string  `json:"bankCode"`
	Amount             float64 `json:"amount"`
	DateTime           string  `json:"dateTime"`
	RawDateTime        string  `json:"rawDateTime"`
	Info               string  `json:"info"`
	ChannelCode        string  `json:"channelCode"`
	ChannelDescription string  `json:"channelDescription"`
	TxnCode            string  `json:"txnCode"`
	TxnDescription     string  `json:"txnDescription"`
	Checksum           string  `json:"checksum"`
	IsRead             bool    `json:"isRead"`
	ExternalCreateDate string  `json:"externalCreateDate"`
	ExternalUpdateDate string  `json:"externalUpdateDate"`
}

type ExternalAccountTransferRequest struct {
	SystemAccountId int64  `json:"systemAccountId" validate:"required"`
	AccountNumber   string `json:"accountNumber" validate:"required"`
	BankCode        string `json:"bankCode" validate:"required"`
	Amount          string `json:"amount" validate:"required"`
}

type ExternalAccountTransferBody struct {
	AccountForm string `json:"accountFrom"`
	AccountTo   string `json:"accountTo"`
	Amount      string `json:"amount"`
	BankCode    string `json:"bankCode"`
	Pin         string `json:"pin"`
}

type ExternalStatementSetReadBody struct {
	AccountNo   string `json:"accountNo"`
	StatementId int64  `json:"statementId"`
	UsedCredit  bool   `json:"usedCredit"`
}

type ExternalAccountError struct {
	Timestamp int64  `json:"timestamp"`
	Status    int    `json:"status"`
	Error     string `json:"error"`
	Path      string `json:"path"`
}

type RecheckWebhookRequest struct {
	AccountId  int64  `json:"accountId" validate:"required"`
	ExternalId int64  `json:"externalId" validate:"required"`
	OfDateTime string `json:"ofDateTime" validate:"required"`
}

type WebhookLog struct {
	Id          int64          `json:"id"`
	JsonRequest string         `json:"jsonRequest"`
	JsonPayload string         `json:"jsonPayload"`
	LogType     string         `json:"logType"`
	Status      string         `json:"status"`
	CreatedAt   time.Time      `json:"createdAt"`
	UpdatedAt   *time.Time     `json:"updatedAt"`
	DeletedAt   gorm.DeletedAt `json:"deletedAt"`
}

type WebhookLogCreateBody struct {
	Id          int64  `json:"id"`
	JsonRequest string `json:"jsonRequest"`
	JsonPayload string `json:"jsonPayload"`
	LogType     string `json:"logType"`
	Status      string `json:"status"`
}
type WebhookLogUpdateBody struct {
	JsonPayload string `json:"jsonPayload"`
	Status      string `json:"status"`
}

type WebhookStatementResponse struct {
	NewStatementList []WebhookStatement `json:"newStatementList"`
}

type WebhookStatement struct {
	Id                 int64     `json:"id"`
	CustomerId         int64     `json:"customerId"`
	ClientId           int64     `json:"clientId"`
	ClientName         string    `json:"clientName"`
	BankAccountId      int64     `json:"bankAccountId"`
	BankCode           string    `json:"bankCode"`
	Amount             float64   `json:"amount"`
	DateTime           time.Time `json:"dateTime"`
	RawDateTime        string    `json:"rawDateTime"`
	Info               string    `json:"info"`
	ChannelCode        string    `json:"channelCode"`
	ChannelDescription string    `json:"channelDescription"`
	TxnCode            string    `json:"txnCode"`
	TxnDescription     string    `json:"txnDescription"`
	Checksum           string    `json:"checksum"`
	IsRead             bool      `json:"isRead"`
	CreatedDate        string    `json:"createdDate"`
	UpdatedDate        string    `json:"updatedDate"`
}

type BotAccountConfig struct {
	Id        int64  `json:"id"`
	ConfigKey string `json:"configKey"`
	ConfigVal string `json:"configVal"`
}

type BotAccountConfigListRequest struct {
	SearchKey   *string `form:"searchKey"`
	SearchValue *string `form:"searchValue"`
	Page        int     `form:"page" default:"1" min:"1"`
	Limit       int     `form:"limit" default:"10" min:"1" max:"100"`
	SortCol     string  `form:"sortCol"`
	SortAsc     string  `form:"sortAsc"`
}

type BotAccountConfigCreateBody struct {
	ConfigKey string `json:"configKey"`
	ConfigVal string `json:"configVal"`
}

type BankAccountPriority struct {
	Id              int64      `json:"id"`
	Name            string     `json:"name"`
	ConditionType   string     `json:"conditionType"`
	MinDepositCount int        `json:"minDepositCount"`
	MinDepositTotal float64    `json:"minDepositTotal"`
	CreatedAt       time.Time  `json:"createdAt"`
	UpdatedAt       *time.Time `json:"updatedAt"`
}

type ApprovedByAdmin struct {
	Id int64 `json:"id"`
}

type PriorityWithdrawstructBody struct {
	Id                      int64 `json:"id"`
	AccountPriorityWithdraw int64 `json:"accountPriorityWithdraw" binding:"required,min=1"`
}

type PriorityWithdrawstructRequest struct {
	List []PriorityWithdrawstructBody `json:"list"`
}

type BankAccountWithdrawAndSumAmount struct {
	// Id                int64   `json:"id"`
	SumAmount         float64 `json:"sumAmount" sql:"type:decimal(14,2);"`
	FromAccountNumber string  `json:"fromAccountNumber"`
}

type BankAccounDepositAndSumAmount struct {
	// Id                int64   `json:"id"`
	SumAmount       float64 `json:"sumAmount" sql:"type:decimal(14,2);"`
	ToAccountNumber string  `json:"toAccountNumber"`
}

type BankAccountDetailAndSumAmountListBody struct {
	Id                      int64   `json:"id"`
	AccountNumber           string  `json:"accountNumber"`
	SumAmount               float64 `json:"sumAmount" sql:"type:decimal(14,2);"`
	ConnectionStatusId      int64   `json:"connectionStatusId"`
	AccountBalance          float64 `json:"accountBalance" sql:"type:decimal(14,2);"`
	AccountPriorityWithdraw int64   `json:"accountPriorityWithdraw"`
	BankWithdrawMaximum     float64 `json:"bankWithdrawMaximum"` // ไม่เกี่ยวกับ db
	AutoWithdrawMaximum     float64 `json:"autoWithdrawMaximum"` // ไม่เกี่ยวกับ db
}

type DragSortRequest struct {
	FromItemId int64 `form:"fromItemId" binding:"required"`
	ToItemId   int64 `form:"toItemId" binding:"required"`
}

type PriorityBankAccountResponse struct {
	Id                      int64 `json:"id"`
	AccountPriorityWithdraw int64 `json:"accountPriorityWithdraw"`
}

type PrioritySortResponse struct {
	Id            int64 `json:"id"`
	PriorityOrder int64 `json:"priorityOrder"`
}
type CheckDepositAccountMoveTransaction struct {
	FromAccountNumber string `json:"fromAccountNumber"`
	FromAccountBankId int64  `json:"fromAccountBankId"`
	ToAccountId       int64  `json:"toAccountId"`
	ToAccountBankId   int64  `json:"toAccountBankId"`
}

type CheckDuplicateWebhookAndAdminRecord struct {
	TransactionAt     time.Time `json:"transactionAt"`
	Amount            float64   `json:"amount"`
	FromBankId        int64     `json:"fromBankId"`
	FromAccountNumber string    `json:"fromAccountNumber"`
	StatausId         int64     `json:"statausId"`
	CheckFromWhere    string    `json:"checkFromWhere"`
	MemberCode        string    `json:"memberCode"`
	ToBankId          *int64    `json:"toBankId"`
}

type GetSumMoveTransactionFromAccountToDayResponse struct {
	TotalAmount float64 `json:"totalAmount"`
	TotalCount  int64   `json:"totalCount"`
	FromBankId  int64   `json:"fromBankId"`
}
type GetSumMoveTransactionToAccountToDayResponse struct {
	TotalAmount float64 `json:"totalAmount"`
	TotalCount  int64   `json:"totalCount"`
	ToAccountId int64   `json:"toAccountId"`
}

type ExternalCheckWithdrawTransaction struct {
	TransactionAt   time.Time `json:"transactionAt"`
	Amount          float64   `json:"amount"`
	ToBankId        int64     `json:"toBankId"`
	ToAccountNumber string    `json:"toAccountNumber"`
	FromAccountId   int64     `json:"fromAccountId"`
}

type ExchangeCurrency struct {
	Id             int64  `json:"id"`
	NameTh         string `json:"nameTh"`
	NameEn         string `json:"nameEn"`
	CurrencyCode   string `json:"currencyCode"`
	CurrencyNameTh string `json:"currencyNameTh"`
	CurrencyNameEn string `json:"currencyNameEn"`
	ImageUrl       string `json:"imageUrl"`
	IsMain         bool   `json:"isMain"`
}

type GetExchangeCurrencyListRequest struct {
	Page   int  `form:"page" default:"1" min:"1"`
	Limit  int  `form:"limit" default:"10" min:"1" max:"100"`
	IsMain bool `form:"isMain"`
}

type GetExchangeCurrencyListResponse struct {
	Id             int64  `json:"id"`
	NameTh         string `json:"nameTh"`
	NameEn         string `json:"nameEn"`
	CurrencyCode   string `json:"currencyCode"`
	CurrencyNameTh string `json:"currencyNameTh"`
	CurrencyNameEn string `json:"currencyNameEn"`
	ImageUrl       string `json:"imageUrl"`
	IsMain         bool   `json:"isMain"`
}

type ExchangeRate struct {
	Id                 int64      `json:"id"`
	ExchangeCurrencyId int64      `json:"exchangeCurrencyId"`
	ExchangeRate       float64    `json:"exchangeRate"`
	CreatedAt          time.Time  `json:"createdAt"`
	UpdatedAt          *time.Time `json:"updatedAt"`
	DeletedAt          *time.Time `json:"deletedAt"`
}

type CreateExchangeRateRequest struct {
	ExchangeCurrencyId int64   `json:"exchangeCurrencyId" `
	ExchangeRate       float64 `json:"exchangeRate" `
	MaxRateDeposit     float64 `json:"maxRateDeposit"`
}

type GetExchangeRateList struct {
	Id                       int64      `json:"id"`
	ExchangeCurrencyId       int64      `json:"exchangeCurrencyId"`
	ExchangeCurrencyNameTh   string     `json:"exchangeCurrencyNameTh"`
	ExchangeCurrencyNameEn   string     `json:"exchangeCurrencyNameEn"`
	ExchangeCurrencyCode     string     `json:"exchangeCurrencyCode"`
	ExchangeCurrencyImageUrl string     `json:"exchangeCurrencyImageUrl"`
	ExchangeRate             float64    `json:"exchangeRate"`
	MaxRateDeposit           float64    `json:"maxRateDeposit"`
	CreatedAt                time.Time  `json:"createdAt"`
	UpdatedAt                *time.Time `json:"updatedAt"`
}

type GetExchangeRate struct {
	Id             int64   `json:"id"`
	ExchangeRate   float64 `json:"exchangeRate"`
	MaxRateDeposit float64 `json:"maxRateDeposit"`
}

type CreateExchangeUpdateLogRequest struct {
	ExchangeCurrencyId int64   `json:"exchangeCurrencyId" `
	DefaultMainRate    float64 `json:"defaultMainRate"`
	ExchangeRate       float64 `json:"exchangeRate"`
	CurrencyNameTh     string  `json:"currencyNameTh"`
	CurrencyNameEn     string  `json:"currencyNameEn"`
	CreatedAdminId     int64   `json:"createdAdminId"`
	CreatedAdminName   string  `json:"createdAdminName"`
	MaxRateDeposit     float64 `json:"maxRateDeposit"`
}

type GetExchangeUpdateLogListRequest struct {
	ExchangeCurrencyId *int64 `form:"exchangeCurrencyId"`
	Page               int    `form:"page" default:"1" min:"1"`
	Limit              int    `form:"limit" default:"10" min:"1" max:"100"`
}

type GetExchangeUpdateLogListReponse struct {
	Id                 int64     `json:"id"`
	ExchangeCurrencyId int64     `json:"exchangeCurrencyId"`
	DefaultMainRate    float64   `json:"defaultMainRate"`
	ExchangeRate       float64   `json:"exchangeRate"`
	MaxRateDeposit     float64   `json:"maxRateDeposit"`
	CurrencyNameTh     string    `json:"currencyNameTh"`
	CurrencyNameEn     string    `json:"currencyNameEn"`
	CreatedAdminId     int64     `json:"createdAdminId"`
	CreatedAdminName   string    `json:"createdAdminName"`
	CreatedAt          time.Time `json:"createdAt"`
}

type PaygateSmsModeDeposit struct {
	Id                int64      `json:"id"`
	UserId            int64      `json:"userId"`
	RefId             *int64     `json:"refId"`
	AccountFrom       string     `json:"accountFrom"`
	BankAccountId     int64      `json:"bankAccountId"`
	BankAccountNo     string     `json:"bankAccountNo"`
	BankCode          string     `json:"bankCode"`
	OrderNo           string     `json:"orderNo"`
	Amount            float64    `json:"amount" sql:"type:decimal(10,2);"`
	TransferAmount    float64    `json:"transferAmount" sql:"type:decimal(10,2);"`
	TransactionNo     string     `json:"transactionNo"`
	TransactionDate   *time.Time `json:"transactionDate"`
	TransactionStatus string     `json:"transactionStatus"`
	PaymentAt         *time.Time `json:"paymentAt"`
	Remark            string     `json:"remark"`
	CreatedAt         time.Time  `json:"createdAt"`
	UpdatedAt         *time.Time `json:"updatedAt"`
}
type CreateSmsModeDepositFastbankRequest struct {
	UserId    int64   `json:"-"`
	Amount    float64 `json:"amount" validate:"required"`
	AccountId int64   `json:"accountId" validate:"required"`
}
type CreateSmsModeDepositFastbankResponse struct {
	Message        string  `json:"message"`
	TransferAmount float64 `json:"transferAmount"`
}

type CreateSmsModeFastbankDepositBody struct {
	AccountFrom   string  `json:"accountFrom" validate:"required"`
	Amount        float64 `json:"amount" validate:"required"`
	BankAccountNo string  `json:"bankAccountNo" validate:"required"`
	BankCode      string  `json:"bankCode" validate:"required"`
	RefNo         string  `json:"refNo" validate:"required"`
}
type CreateSmsModeFastbankDepositResponse struct {
	Id                int64   `json:"id"`
	BankAccountId     int64   `json:"bankAccountId"`
	BankAccountNo     string  `json:"bankAccountNo"`
	BankId            int64   `json:"bankId"`
	BankCode          string  `json:"bankCode"`
	Amount            float64 `json:"amount"`
	AmountWithDecimal float64 `json:"amountWithDecimal"`
	// DecimalSuffix     int64     `json:"decimalSuffix"`
	RefNo       string `json:"refNo"`
	AccountFrom string `json:"accountFrom"`
	InternalRef string `json:"internalRef"`
	// CreatedDate       time.Time `json:"createdDate"`
	// SuccessDate       time.Time `json:"successDate"`
	// Success           bool      `json:"success"`
	// Expire            bool      `json:"expire"`
}

// {"timeStamp":"2024-08-19T11:10:22.742Z","errorMessage":"รายการแจ้งฝากก่อนหน้าของ 557375373753753573 จำนวน 125 บาท ยังไม่ได้ดำเนินการเสร็จสิ้น"}
type CreateSmsModeFastbankDepositErrorResponse struct {
	TimeStamp    time.Time `json:"timeStamp"`
	ErrorMessage string    `json:"errorMessage"`
}

// {"timestamp":*************,"status":500,"error":"Internal Server Error","path":"/api/v2/statement/depositSms"}
type CreateSmsModeFastbankDepositErrorResponse2 struct {
	Timestamp int64  `json:"timestamp"`
	Status    int    `json:"status"`
	Error     string `json:"error"`
	Path      string `json:"path"`
}

type CreateSmsModeDepositBody struct {
	Id                int64   `json:"-"`
	UserId            int64   `json:"userId"`
	FromBankId        int64   `json:"fromBankId"`
	AccountFrom       string  `json:"accountFrom"`
	BankAccountId     int64   `json:"bankAccountId"`
	BankAccountNo     string  `json:"bankAccountNo"`
	BankCode          string  `json:"bankCode"`
	RefId             *int64  `json:"refId"`
	OrderNo           string  `json:"orderNo"`
	Detail            string  `json:"detail"`
	Amount            float64 `json:"amount"`
	TransactionStatus string  `json:"transactionStatus"`
}

type UpdateSmsModeDepositBody struct {
	Id                int64     `json:"id"`
	TransactionNo     string    `json:"transactionNo"`
	TransactionDate   time.Time `json:"transactionDate"`
	TransactionStatus string    `json:"transactionStatus"`
	TransferAmount    float64   `json:"transferAmount"`
	Remark            string    `json:"remark"`
}

type UpdateConfirmSmsModeDepositBody struct {
	Id                   *int64     `json:"id"`
	RefId                *int64     `json:"refId"`
	TransactionStatus    *string    `json:"transactionStatus"`
	PaymentAt            *time.Time `json:"paymentAt"`
	ConfirmedByAdminId   *int64     `json:"confirmedByAdminId"`
	ConfirmedByAdminName *string    `json:"confirmedByAdminName"`
}

type UpdateBankAccountIsShowBankRequest struct {
	Id          int64 `json:"-"`
	IsShowFront *bool `json:"isShowFront"`
	AdminId     int64 `json:"-"`
}

type SmsModeDepositOrderResponse struct {
	Id             int64     `json:"id"`
	UserId         int64     `json:"userId"`
	OrderNo        string    `json:"orderNo"`
	Amount         float64   `json:"amount"`
	TransferAmount float64   `json:"transferAmount"`
	CreatedAt      time.Time `json:"createdAt"`
}

type TotalBankStatementRequest struct {
	AccountId *int64 `form:"accountId"`
	FromDate  string `form:"fromDate"`
	ToDate    string `form:"toDate"`
}
type TotalBankStatementSummary struct {
	TotalDepositAmount float64 `json:"totalDepositAmount"`
}

type TotalBankTransactionSummary struct {
	TotalAutoDepositAmount  float64 `json:"totalAutoDepositAmount"`
	TotalAdminDepositAmount float64 `json:"totalAdminDepositAmount"`
}
type TotalBankTransactionSummaryResponse struct {
	TotalDepositAmount      float64 `json:"totalDepositAmount"`
	TotalAutoDepositAmount  float64 `json:"totalAutoDepositAmount"`
	TotalAdminDepositAmount float64 `json:"totalAdminDepositAmount"`
	IsTotalMatchTotal       bool    `json:"isTotalMatchTotal"`
}

type GetUserTransactionCountDepositTimeByUserResponse struct {
	BankDepositTime int64 `json:"bankDepositTime"`
}
