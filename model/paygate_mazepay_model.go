package model

import (
	"time"
)

// todo descp
const (
	MAZEPAY_DEFMIN_DEPOSIT_AMOUNT  = 100
	MAZEPAY_DEFMAX_DEPOSIT_AMOUNT  = 500000
	MAZEPAY_DEFMIN_WITHDRAW_AMOUNT = 100
	MAZEPAY_DEFMAX_WITHDRAW_AMOUNT = 500000
)

type MazepayWebhookDepositResponse struct {
	Sign               string `json:"sign"`
	Timestamp          int64  `json:"timestamp"`
	OrderNo            string `json:"order_no"`
	MchOrderNo         string `json:"mch_order_no"`
	Status             int    `json:"status"`
	Amount             string `json:"amount"`
	PayTime            string `json:"pay_time"`
	RepayAccountBank   string `json:"repay_account_bank"`
	RepayAccountNumber string `json:"repay_account_number"`
	AccountNumber      string `json:"account_number"`
	AccountBank        string `json:"account_bank"`
}

type MazepayErrorRemoteResponse struct {
	Code        string `json:"code"`
	Message     string `json:"message"`
	Description string `json:"description"`
}

type MazepayCustomerCreateRemoteRequest struct {
	BankUuid          string `json:"bank_uuid"`
	BankAccountNumber string `json:"bank_account_number"`
	BankAccountName   string `json:"bank_account_name"`
	BankAccountNameEn string `json:"bank_account_name_en"`
	Status            string `json:"status"`
	RefCode           string `json:"ref_code"`
}
type MazepayCustomerCreateRemoteResponse struct {
	Code        string `json:"code"`
	Message     string `json:"message"`
	Description string `json:"description"`
	Data        struct {
		Uuid              string `json:"uuid"`
		BankAccountNumber string `json:"bank_account_number"`
		BankAccountName   string `json:"bank_account_name"`
		BankAccountNameEn string `json:"bank_account_name_en"`
		Status            string `json:"status"`
		CreatedAt         string `json:"created_at"`
		RefCode           string `json:"ref_code"`
	} `json:"data"`
}
type MazepayCustomerUpdateRemoteRequest struct {
	CustomerUuid      string `json:"customer_uuid"`
	BankUuid          string `json:"bank_uuid"`
	BankAccountNumber string `json:"bank_account_number"`
	BankAccountName   string `json:"bank_account_name"`
	BankAccountNameEn string `json:"bank_account_name_en"`
	Status            string `json:"status"`
	RefCode           string `json:"ref_code"`
}
type MazepayCustomerUpdateRemoteResponse struct {
	Code        string `json:"code"`
	Message     string `json:"message"`
	Description string `json:"description"`
	Data        struct {
		Uuid              string `json:"uuid"`
		BankAccountNumber string `json:"bank_account_number"`
		BankAccountName   string `json:"bank_account_name"`
		BankAccountNameEn string `json:"bank_account_name_en"`
		Status            string `json:"status"`
		CreatedAt         string `json:"created_at"`
		RefCode           string `json:"ref_code"`
	} `json:"data"`
}

type MazepayDepositCreateRemoteRequest struct {
	CustomerAccountUuid string  `json:"customer_account_uuid"`
	Amount              float64 `json:"amount"`
	Currency            string  `json:"currency"`
	PaymentMethod       string  `json:"payment_method"`
	CallbackUrl         string  `json:"callback_url"`
	RedirectUrl         string  `json:"redirect_url"`
	MerchantOrderId     string  `json:"merchant_order_id"`
}

type MazepayDepositCreateRemoteResponse struct {
	Code        string `json:"code"`
	Message     string `json:"message"`
	Description string `json:"description"`
	Data        struct {
		Uuid                     string  `json:"uuid"`
		BankAccountNumber        string  `json:"bank_account_number"`
		BankAccountName          string  `json:"bank_account_name"`
		BankCode                 string  `json:"bank_code"`
		BankUuid                 string  `json:"bank_uuid"`
		BankNameEn               string  `json:"bank_name_en"`
		BankNameTh               string  `json:"bank_name_th"`
		CustomerTransferAmount   float64 `json:"customer_transfer_amount"`
		Amount                   float64 `json:"amount"`
		CustomerRequestAmount    float64 `json:"customer_request_amount"`
		Fee                      float64 `json:"fee"`
		Currency                 string  `json:"currency"`
		Type                     string  `json:"type"`
		TransactionStatus        string  `json:"transaction_status"`
		TransactionStatusMessage string  `json:"transaction_status_message"`
		PaymentMethod            string  `json:"payment_method"`
		CreatedAt                string  `json:"created_at"`
		UpdatedAt                string  `json:"updated_at"`
		CreatedBy                string  `json:"created_by"`
		UpdatedBy                string  `json:"updated_by"`
		RedirectUrl              string  `json:"redirect_url"`
		CallbackUrl              string  `json:"callback_url"`
		MerchantOrderId          string  `json:"merchant_order_id"`
		CallbackMessage          string  `json:"callback_message"`
		CallbackAt               string  `json:"callback_at"`
		Note                     string  `json:"note"`
		Tag                      string  `json:"tag"`
		PaymentLink              string  `json:"payment_link"`
		PaymentImageBase64       string  `json:"payment_image_base64"`
		BankAccountPromptpayId   string  `json:"bank_account_promptpay_id"`
	} `json:"data"`
}

type MazepayCheckBalanceRemoteResponse struct {
	Code        string `json:"code"`
	Message     string `json:"message"`
	Description string `json:"description"`
	Data        struct {
		Uuid             string  `json:"uuid"`
		Code             string  `json:"code"`
		Name             string  `json:"name"`
		ContactFirstname string  `json:"contact_firstname"`
		ContactLastname  string  `json:"contact_lastname"`
		Email            string  `json:"email"`
		PhoneNumber      string  `json:"phone_number"`
		Currency         string  `json:"currency"`
		ExchangeRate     float64 `json:"exchange_rate"`
		ExchangeRateType string  `json:"exchange_rate_type"`
		MinimumDeposit   float64 `json:"minimum_deposit"`
		MaximumWithdraw  float64 `json:"maximum_withdraw"`
		DailySettlement  float64 `json:"daily_settlement_amount"`
		WithdrawStatus   string  `json:"withdraw_status"`
		DepositStatus    string  `json:"deposit_status"`
		ClientId         string  `json:"client_id"`
		Status           string  `json:"status"`
		CreatedAt        string  `json:"created_at"`
		Credit           struct {
			Incoming  float64 `json:"incoming"`
			Outgoing  float64 `json:"outgoing"`
			Total     float64 `json:"total"`
			Available float64 `json:"available"`
		} `json:"credit"`
		EnabledWhitelist bool `json:"enabled_whitelist"`
	} `json:"data"`
}
type MazepayCheckBalanceResponse struct {
	Balance         float64 `json:"balance"`
	BalanceWithdraw float64 `json:"balance_withdraw"`
	Date            string  `json:"date"`
}

type MazepayGetOrderRemoteResponse struct {
	Code        string `json:"code"`
	Message     string `json:"message"`
	Description string `json:"description"`
	Data        struct {
		Status                 string  `json:"status"`
		Amount                 float64 `json:"amount"`
		BankAccountPromptpayId string  `json:"bank_account_promptpay_id"`
		BankAccountNumber      string  `json:"bank_account_number"`
	} `json:"data"`
}

type MazepayCancelDepositRemoteResponse struct {
	Code        string `json:"code"`
	Message     string `json:"message"`
	Description string `json:"description"`
	Data        struct {
		Uuid                   string  `json:"uuid"`
		CustomerRequestAmount  float64 `json:"customer_request_amount"`
		CustomerTransferAmount float64 `json:"customer_transfer_amount"`
		Type                   string  `json:"type"`
		TransactionStatus      string  `json:"transaction_status"`
		PaymentMethod          string  `json:"payment_method"`
	} `json:"data"`
}

type MazepayWithdrawCreateRemoteRequest struct {
	CustomerAccountUuid string  `json:"customer_account_uuid"`
	Amount              float64 `json:"amount"`
	Currency            string  `json:"currency"`
	CallbackUrl         string  `json:"callback_url"`
	MerchantOrderId     string  `json:"merchant_order_id"`
}
type MazepayWithdrawCreateRemoteResponse struct {
	Code        string `json:"code"`
	Message     string `json:"message"`
	Description string `json:"description"`
	Data        struct {
		Uuid                     string  `json:"uuid"`
		BankAccountNumber        string  `json:"bank_account_number"`
		BankAccountName          string  `json:"bank_account_name"`
		BankCode                 string  `json:"bank_code"`
		BankUuid                 string  `json:"bank_uuid"`
		BankNameEn               string  `json:"bank_name_en"`
		BankNameTh               string  `json:"bank_name_th"`
		CustomerAmount           float64 `json:"customer_transfer_amount"`
		Amount                   float64 `json:"amount"`
		CustomerTransferAmount   float64 `json:"customer_request_amount"`
		Fee                      float64 `json:"fee"`
		Currency                 string  `json:"currency"`
		Type                     string  `json:"type"`
		TransactionStatus        string  `json:"transaction_status"`
		TransactionStatusMessage string  `json:"transaction_status_message"`
		CreatedAt                string  `json:"created_at"`
		UpdatedAt                string  `json:"updated_at"`
		CreatedBy                string  `json:"created_by"`
		UpdatedBy                string  `json:"updated_by"`
		CallbackUrl              string  `json:"callback_url"`
		MerchantOrderId          string  `json:"merchant_order_id"`
		CallbackMessage          string  `json:"callback_message"`
		CallbackAt               string  `json:"callback_at"`
		Note                     string  `json:"note"`
	} `json:"data"`
}

type MazepayWebhookRequest struct {
	JsonPayload string `json:"jsonPayload"`
}
type MazepayWebhookCreateBody struct {
	Id          int64  `json:"id"`
	Name        string `json:"name"`
	JsonPayload string `json:"jsonPayload"`
}

type MazepayWebhookReturnResponse struct {
	Status string `json:"status"`
}

//	{
//		uuid:"********-2a9b-44a3-b6e5-fccb2ebcddaa",
//		customer_account_uuid:"c72159e0-f42f-4450-8c7c-fe68032d5919",
//		customer_transfer_amount:"12.99",
//		customer_request_amount:"12.00",
//		currency:"thb",
//		type:"deposit",
//		payment_method:"qr",
//		transaction_status: "success",
//		bank_account_name:"สมจิตร ใจดี",
//		bank_account_number:"**********",
//		merchant_order_id:"ORDERID:19282",
//		callback_url:"https://your_website.com/callback_when_success",
//		redirect_url:"https://your_website.com/confirm_payment_success"
//	  }
type MazepayDepositWebhookResponse struct {
	Uuid                   string `json:"uuid"`
	CustomerAccountUuid    string `json:"customer_account_uuid"`
	CustomerTransferAmount string `json:"customer_transfer_amount"`
	CustomerRequestAmount  string `json:"customer_request_amount"`
	Currency               string `json:"currency"`
	Type                   string `json:"type"`
	PaymentMethod          string `json:"payment_method"`
	TransactionStatus      string `json:"transaction_status"`
	BankAccountName        string `json:"bank_account_name"`
	BankAccountNumber      string `json:"bank_account_number"`
	MerchantOrderId        string `json:"merchant_order_id"`
	CallbackUrl            string `json:"callback_url"`
	RedirectUrl            string `json:"redirect_url"`
}

type MazepayWithdrawWebhookResponse struct {
	Uuid                   string `json:"uuid"`
	CustomerAccountUuid    string `json:"customer_account_uuid"`
	CustomerTransferAmount string `json:"customer_transfer_amount"`
	CustomerRequestAmount  string `json:"customer_request_amount"`
	Currency               string `json:"currency"`
	Type                   string `json:"type"`
	TransactionStatus      string `json:"transaction_status"`
	PaymentMethod          string `json:"payment_method"`
	BankAccountName        string `json:"bank_account_name"`
	BankAccountNumber      string `json:"bank_account_number"`
	MerchantOrderId        string `json:"merchant_order_id"`
	CallbackUrl            string `json:"callback_url"`
}

type MazepayCustomerDepositInfo struct {
	Name        string `json:"name"`
	DisplayName string `json:"displayName"`
	ShopName    string `json:"shopName"`
	MinAmount   int64  `json:"minAmount"`
	MaxAmount   int64  `json:"maxAmount"`
}
type MazepayDepositCreateRequest struct {
	UserId int64   `form:"userId" json:"-" validate:"required"`
	Amount float64 `form:"amount" json:"amount" binding:"required,min=1" validate:"required,min=1"`
}
type MazepayWithdrawCreateRequest struct {
	RefId  int64   `form:"refId" validate:"required"`
	UserId int64   `form:"userId" json:"-" validate:"required"`
	Amount float64 `form:"amount" binding:"required,min=1" validate:"required,min=1"`
}
type MazepayDepositCancelRequest struct {
	OrderId int64 `form:"orderId" validate:"required"`
	UserId  int64 `form:"userId" json:"-" validate:"required"`
}

const (
	MAZEPAY_ORDER_TYPE_DEPOSIT  = 1
	MAZEPAY_ORDER_TYPE_WITHDRAW = 2
)

type MazepayCustomer struct {
	Id           int64  `json:"id"`
	UserId       int64  `json:"userId"`
	CustomerUuid string `json:"customerUuid"`
	FullName     string `json:"fullName"`
	BankCode     string `json:"bankCode"`
	AccountNo    string `json:"accountNo"`
	AccountName  string `json:"accountName"`
}
type MazepayCustomerListRequest struct {
	Page    int    `form:"page" default:"1" min:"1"`
	Limit   int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol string `form:"sortCol"`
	SortAsc string `form:"sortAsc"`
}
type MazepayCustomerResponse struct {
	Id           int64  `json:"id"`
	UserId       int64  `json:"userId"`
	CustomerUuid string `json:"customerUuid"`
	FullName     string `json:"fullName"`
	BankCode     string `json:"bankCode"`
	AccountNo    string `json:"accountNo"`
	AccountName  string `json:"accountName"`
}
type MazepayCustomerCreateBody struct {
	Id           int64  `json:"id"`
	UserId       int64  `json:"userId"`
	CustomerUuid string `json:"customerUuid"`
	FullName     string `json:"fullName"`
	BankCode     string `json:"bankCode"`
	AccountNo    string `json:"accountNo"`
	AccountName  string `json:"accountName"`
}
type MazepayCustomerUpdateBody struct {
	CustomerUuid *string `json:"customerUuid"`
	FullName     *string `json:"fullName"`
	BankCode     *string `json:"bankCode"`
	AccountNo    *string `json:"accountNo"`
	AccountName  *string `json:"accountName"`
}

type MazepayOrder struct {
	Id                int64      `json:"id"`
	UserId            int64      `json:"userId"`
	OrderTypeId       int64      `json:"orderTypeId"`
	RefId             *int64     `json:"refId"`
	OrderNo           string     `json:"orderNo"`
	Amount            float64    `json:"amount"`
	TransactionNo     *string    `json:"transactionNo"`
	TransactionDate   *time.Time `json:"transactionDate"`
	TransactionStatus *string    `json:"transactionStatus"`
	PaymentAt         *time.Time `json:"paymentAt"`
	QrPromptpay       *string    `json:"qrPromptpay"`
	QrBase64          string     `json:"qrBase64"`
	ExtraPromptpayId  string     `json:"extraPromptpayId"`
	PaymentPageUrl    string     `json:"paymentPageUrl"`
	Remark            *string    `json:"remark"`
	ApiRemark         *string    `json:"apiRemark"`
	CreatedAt         time.Time  `json:"createdAt"`
	UpdatedAt         *time.Time `json:"updatedAt"`
}
type MazepayOrderListRequest struct {
	UserId        *int64 `form:"userId"`
	OrderTypeId   *int64 `form:"orderTypeId"`
	OrderNo       string `form:"orderNo"`
	TransactionNo string `form:"transactionNo"`
	Amount        string `form:"amount"`
	Status        string `form:"status"`
	Page          int    `form:"page" default:"1" min:"1"`
	Limit         int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol       string `form:"sortCol"`
	SortAsc       string `form:"sortAsc"`
}
type MazepayOrderResponse struct {
	Id                    int64      `json:"id"`
	UserId                int64      `json:"userId"`
	OrderTypeId           int64      `json:"orderTypeId"`
	OrderTypeName         string     `json:"orderTypeName"`
	RefId                 *int64     `json:"refId"`
	OrderNo               string     `json:"orderNo"`
	Amount                float64    `json:"amount"`
	TransferAmount        float64    `json:"transferAmount"`
	TransactionNo         string     `json:"transactionNo"`
	TransactionDate       *time.Time `json:"transactionDate"`
	TransactionStatus     string     `json:"transactionStatus"`
	PaymentAt             *time.Time `json:"paymentAt"`
	BankTransactionId     string     `json:"bankTransactionId"`
	BankTransactionStatus string     `json:"bankTransactionStatus"`
	QrPromptpay           string     `json:"qrPromptpay"`
	QrBase64              string     `json:"qrBase64"`
	ExtraPromptpayId      string     `json:"extraPromptpayId"`
	PaymentPageUrl        string     `json:"paymentPageUrl"`
	Remark                string     `json:"remark"`
	ApiRemark             string     `json:"apiRemark"`
	CreatedAt             time.Time  `json:"createdAt"`
	UpdatedAt             *time.Time `json:"updatedAt"`
}
type MazepayOrderWebResponse struct {
	OrderId           int64     `json:"orderId"`
	UserId            int64     `json:"userId"`
	OrderNo           string    `json:"orderNo"`
	Amount            float64   `json:"amount"`
	TransferAmount    float64   `json:"transferAmount"`
	TransactionStatus string    `json:"transactionStatus"`
	QrCode            string    `json:"qrCode"`
	QrBase64          string    `json:"qrBase64"`
	ExtraPromptpayId  string    `json:"extraPromptpayId"`
	QrUrl             string    `json:"qrUrl"`
	PaymentPageUrl    string    `json:"paymentPageUrl"`
	CreatedAt         time.Time `json:"createdAt"`
}
type MazepayOrderQrResponse struct {
	Id               int64   `json:"id"`
	OrderNo          string  `json:"orderNo"`
	Amount           float64 `json:"amount"`
	QrBase64         string  `json:"qrBase64"`
	ExtraPromptpayId string  `json:"extraPromptpayId"`
}
type MazepayOrderCreateRequest struct {
	UserId *int64  `form:"userId"`
	Amount float64 `form:"amount"`
}
type MazepayOrderCreateBody struct {
	Id                int64   `json:"id"`
	UserId            int64   `json:"userId"`
	OrderTypeId       int64   `json:"orderTypeId"`
	RefId             *int64  `json:"refId"`
	OrderNo           string  `json:"orderNo"`
	Amount            float64 `json:"amount"`
	TransactionStatus string  `json:"transactionStatus"`
}
type MazepayOrderUpdateBody struct {
	TransactionNo     string    `json:"transactionNo"`
	TransactionDate   time.Time `json:"transactionDate"`
	TransactionStatus string    `json:"transactionStatus"`
	TransferAmount    float64   `json:"transferAmount"`
	QrPromptpay       string    `json:"qrPromptpay"`
	QrBase64          string    `json:"qrBase64"`
	ExtraPromptpayId  string    `json:"extraPromptpayId"`
	PaymentPageUrl    string    `json:"paymentPageUrl"`
}
