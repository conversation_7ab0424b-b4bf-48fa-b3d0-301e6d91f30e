package model

// ใช้เรียก check status
const (
	IsMemberRegistration         = "IS_MEMBER_REGISTRATION"
	IsDepositBeforeCredit        = "IS_DEPOSIT_BEFORE_CREDIT"
	IsDepositAfterCredit         = "IS_DEPOSIT_AFTER_CREDIT"
	IsWithdrawalCreditSuccess    = "IS_WITHDRAWAL_CREDIT_SUCCESS"
	IsWithdrawalAwaitingTransfer = "IS_WITHDRAWAL_AWAITING_TRANSFER"
	IsWithdrawalCreditFailed     = "IS_WITHDRAWAL_CREDIT_FAILED"
	BounsNotification            = "IS_BOUNS_NOTIFICATION"
	PullCreditBackNotification   = "PULL_CREDIT_BACK_NOTIFICATION"
	ActitvityBeforeBonus         = "ACTITVITY_BEFORE_BONUS"
	ActitvityAfterBonus          = "ACTITVITY_AFTER_BONUS"
	PromotionBonus               = "PROMOTION_BONUS"
	MoveMoney                    = "MOVE_MONEY"
	TransactionHourSummary       = "TRANSACTION_HOUR_SUMMARY"
	AffiliateDailySummary        = "AFFILIATE_DAILY_SUMMARY"
	TransactionDailySummary      = "TRANSACTION_DAILY_SUMMARY"

	// ข้อความ
	IsMemberRegistrationMessage         = "แจ้ง สมัครสมาชิก"
	IsDepositBeforeCreditMessage        = "แจ้งฝาก ก่อนปรับเครดิต"
	IsDepositAfterCreditMessage         = "แจ้งฝาก หลังปรับเครดิต"
	IsWithdrawalCreditSuccessMessage    = "แจ้งถอน สำเร็จ"
	IsWithdrawalAwaitingTransferMessage = "แจ้งถอน รอโอนเงิน"
	IsWithdrawalCreditFailedMessage     = "แจ้งถอน ไม่สำเร็จ"
	BounsNotificationMessage            = "แจ้งฝากแบบโบนัส"
	PullCreditBackNotificationMessage   = "ดึงเครดิตกลับ"
	ActitvityBeforeBonusMessage         = "แจกโบนัสกิจกรรม (รออนุมัติ)"
	ActitvityAfterBonusMessage          = "แจกโบนัสกิจกรรม (หลังปรับเครดิต)"
	PromotionBonusMessage               = "โปรโมชั่นโบนัส"

	USE_ENDING_NOTI = "USE_ENDING_NOTI"
	USE_MATCH_NOTI  = "USE_MATCH_NOTI"
)

type ConfigurationNotificationType struct {
	Id      int64  `json:"id"`
	Name    string `json:"name"`
	LabelTh string `json:"label_th"`
	LabelEn string `json:"label_en"`
}

type ConfigurationNotification struct {
	Id                                int64  `json:"id"`
	CreditAbove                       int64  `json:"creditAbove"`
	LineToken                         string `json:"lineToken"`
	IsMemberRegistration              bool   `json:"isMemberRegistration"`
	IsDepositBeforeCredit             bool   `json:"isDepositBeforeCredit"`
	IsDepositAfterCredit              bool   `json:"isDepositAfterCredit"`
	IsWithdrawalCreditSuccess         bool   `json:"isWithdrawalCreditSuccess"`
	IsWithdrawalAwaitingTransfer      bool   `json:"isWithdrawalAwaitingTransfer"`
	IsWithdrawalCreditFailed          bool   `json:"isWithdrawalCreditFailed"`
	IsDepositBonus                    bool   `json:"isDepositBonus"`
	IsPullCreditBack                  bool   `json:"isPullCreditBack"`
	IsActitvityBeforeBonus            bool   `json:"isActitvityBeforeBonus"`
	IsActitvityAfterBonus             bool   `json:"isActitvityAfterBonus"`
	IsPromotionBonus                  bool   `json:"isPromotionBonus"`
	ConfigurationNotificationTypeId   int64  `json:"configurationNotificationTypeId"`
	ConfigurationNotificationTypeName string `json:"configurationNotificationTypeName"`
}

type CreateConfigurationNotificationBody struct {
	Id                              int64  `json:"id"`
	CreditAbove                     int64  `json:"creditAbove"`
	LineToken                       string `json:"lineToken"`
	IsMemberRegistration            bool   `json:"isMemberRegistration"`
	IsDepositBeforeCredit           bool   `json:"isDepositBeforeCredit"`
	IsDepositAfterCredit            bool   `json:"isDepositAfterCredit"`
	IsWithdrawalCreditSuccess       bool   `json:"isWithdrawalCreditSuccess"`
	IsWithdrawalAwaitingTransfer    bool   `json:"isWithdrawalAwaitingTransfer"`
	IsWithdrawalCreditFailed        bool   `json:"isWithdrawalCreditFailed"`
	IsDepositBonus                  bool   `json:"isDepositBonus"`
	IsPullCreditBack                bool   `json:"isPullCreditBack"`
	IsActitvityBeforeBonus          bool   `json:"isActitvityBeforeBonus"`
	IsActitvityAfterBonus           bool   `json:"isActitvityAfterBonus"`
	IsPromotionBonus                bool   `json:"isPromotionBonus"`
	ConfigurationNotificationTypeId int64  `json:"configurationNotificationTypeId"`
}

type UpdateConfigurationNotificationRequest struct {
	Id                              *int64  `json:"-,omitempty"`
	CreditAbove                     *int64  `json:"creditAbove,omitempty"`
	LineToken                       *string `json:"lineToken,omitempty"`
	IsMemberRegistration            *bool   `json:"isMemberRegistration,omitempty"`
	IsDepositBeforeCredit           *bool   `json:"isDepositBeforeCredit,omitempty"`
	IsDepositAfterCredit            *bool   `json:"isDepositAfterCredit,omitempty"`
	IsWithdrawalCreditSuccess       *bool   `json:"isWithdrawalCreditSuccess,omitempty"`
	IsWithdrawalAwaitingTransfer    *bool   `json:"isWithdrawalAwaitingTransfer,omitempty"`
	IsWithdrawalCreditFailed        *bool   `json:"isWithdrawalCreditFailed,omitempty"`
	IsDepositBonus                  *bool   `json:"isDepositBonus,omitempty"`
	IsPullCreditBack                *bool   `json:"isPullCreditBack,omitempty"`
	IsActitvityBeforeBonus          *bool   `json:"isActitvityBeforeBonus,omitempty"`
	IsActitvityAfterBonus           *bool   `json:"isActitvityAfterBonus,omitempty"`
	IsPromotionBonus                *bool   `json:"isPromotionBonus,omitempty"`
	ConfigurationNotificationTypeId *int64  `json:"configurationNotificationTypeId,omitempty"`
}

type UpdateConfigurationNotificationBody struct {
	Id                              *int64  `json:"id"`
	CreditAbove                     *int64  `json:"creditAbove"`
	LineToken                       *string `json:"lineToken"`
	IsMemberRegistration            *bool   `json:"isMemberRegistration"`
	IsDepositBeforeCredit           *bool   `json:"isDepositBeforeCredit"`
	IsDepositAfterCredit            *bool   `json:"isDepositAfterCredit"`
	IsWithdrawalCreditSuccess       *bool   `json:"isWithdrawalCreditSuccess"`
	IsWithdrawalAwaitingTransfer    *bool   `json:"isWithdrawalAwaitingTransfer"`
	IsWithdrawalCreditFailed        *bool   `json:"isWithdrawalCreditFailed"`
	IsDepositBonus                  *bool   `json:"isDepositBonus"`
	IsPullCreditBack                *bool   `json:"isPullCreditBack"`
	IsActitvityBeforeBonus          *bool   `json:"isActitvityBeforeBonus"`
	IsActitvityAfterBonus           *bool   `json:"isActitvityAfterBonus"`
	IsPromotionBonus                *bool   `json:"isPromotionBonus"`
	ConfigurationNotificationTypeId *int64  `json:"configurationNotificationTypeId"`
}

type NotifyExternalNotificationRequest struct {
	TypeNotify         string
	TransId            *int64
	MemberCode         string
	Amount             float64
	BonusCredit        *float64
	UserCredit         float64
	Phone              string
	ConfirmedByAdminId int64
	TimerCounter       string
	TransferDateTime   string
	ActionTime         string
	RefType            string
	RefMemberCode      string
	RefPhone           string
	RefAlias           string
	RefUsername        string
	WebScoket          WebScoket
	CronjobMessage     string
}
type NotifyLineRequest struct {
	TypeNotify         string
	TransId            *int64
	MemberCode         string
	Amount             float64
	BonusCredit        *float64
	UserCredit         float64
	Phone              string
	ConfirmedByAdminId int64
	TimerCounter       string
	TransferDateTime   string
	ActionTime         string
}

type GetTransactionSummaryReportNotificationResponse struct {
	TotalActivityBonusPrice       float64 `json:"totalActivityBonusPrice"`
	TotalDepositPrice             float64 `json:"totalDepositPrice"`
	TotalDepositUserCount         int64   `json:"totalDepositUserCount"`
	TotalWithdrawPrice            float64 `json:"totalWithdrawPrice"`
	TotalWithdrawUserCount        int64   `json:"totalWithdrawUserCount"`
	TotalBankProfit               float64 `json:"totalBankProfit"`
	TotalNewUserCount             int64   `json:"totalNewUserCount"`
	TotalDepositAboveHundredCount int64   `json:"totalDepositAboveHundredCount"`
}

type GetBankTransactionSummaryReportNotificationResponse struct {
	BankCode           string  `json:"bankCode"`
	AccountName        string  `json:"accountName"`
	AccountNumber      string  `json:"accountNumber"`
	TotalDepositPrice  float64 `json:"totalDepositPrice"`
	TotalWithdrawPrice float64 `json:"totalWithdrawPrice"`
}

type GetAllianceUserSummaryReportNotification struct {
	UserId                    int64  `json:"userId"`
	MemberCode                string `json:"memberCode"`
	UserFullname              string `json:"userFullname"`
	AllianceName              string `json:"allianceName"`
	TotalCountDownUserDeposit int64  `json:"totalCountUserDeposit"`
}
