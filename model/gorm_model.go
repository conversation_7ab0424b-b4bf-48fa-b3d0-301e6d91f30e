package model

import "time"

type GormColumn struct {
	Field   string `json:"field"`
	Type    string `json:"type"`
	Null    string `json:"null"`
	Key     string `json:"key"`
	Default string `json:"default"`
	Extra   string `json:"extra"`
}
type GormTable struct {
	Name    string       `json:"name"`
	SizeMb  float64      `json:"sizeMb" gorm:"-"`
	Columns []GormColumn `json:"columns" gorm:"-"`
}
type GormTableSize struct {
	Database string  `json:"database"`
	Table    string  `json:"table"`
	SizeMb   float64 `json:"sizeMb"`
}

type GormSetting struct {
	Id         int64      `json:"-"`
	ApiKey     string     `json:"apiKey"`
	LastCallAt *time.Time `json:"lastCallAt"`
	CreatedAt  time.Time  `json:"-"`
	UpdatedAt  *time.Time `json:"-"`
}
type GormSettingResponse struct {
	Id         int64      `json:"id"`
	ApiKey     string     `json:"apiKey"`
	LastCallAt *time.Time `json:"lastCallAt"`
	CreatedAt  time.Time  `json:"createdAt"`
	UpdatedAt  *time.Time `json:"updatedAt"`
}
type GormSettingUpdateRequest struct {
	ApiKey     *string    `json:"apiKey"`
	LastCallAt *time.Time `json:"lastCallAt"`
}
type GormSettingUpdateBody struct {
	ApiKey     *string    `json:"apiKey"`
	LastCallAt *time.Time `json:"lastCallAt"`
}

type GormTableCompareQuery struct {
	Endpoint string `json:"endpoint"`
	ApiKey   string `json:"apiKey"`
}
type GormTableCompareRequest struct {
	List []GormTableCompareQuery `json:"list"`
}

type ErrorRemoteResponse struct {
	Code    int64  `json:"code"`
	Message string `json:"message"`
}
