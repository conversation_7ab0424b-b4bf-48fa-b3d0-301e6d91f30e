package model

import (
	"time"
)

// todo descp
const (
	MEEPAY_DEFMIN_DEPOSIT_AMOUNT  = 100
	MEEPAY_DEFMAX_DEPOSIT_AMOUNT  = 500000
	MEEPAY_DEFMIN_WITHDRAW_AMOUNT = 100
	MEEPAY_DEFMAX_WITHDRAW_AMOUNT = 500000
)

type MeepayWebhookDepositResponse struct {
	Sign               string `json:"sign"`
	Timestamp          int64  `json:"timestamp"`
	OrderNo            string `json:"order_no"`
	MchOrderNo         string `json:"mch_order_no"`
	Status             int    `json:"status"`
	Amount             string `json:"amount"`
	PayTime            string `json:"pay_time"`
	RepayAccountBank   string `json:"repay_account_bank"`
	RepayAccountNumber string `json:"repay_account_number"`
	AccountNumber      string `json:"account_number"`
	AccountBank        string `json:"account_bank"`
}

type MeepayErrorRemoteResponse struct {
	Status  string `json:"status"`
	Message string `json:"message"`
}

type MeepayDepositCreateRemoteRequest struct {
	ClientId     string  `json:"clientId"`
	ClientSecret string  `json:"clientSecret"`
	Amount       float64 `json:"amount"`
	Reference    string  `json:"reference"`
	CallbackUrl  string  `json:"callbackUrl"`
	BankId       string  `json:"bankId"`
	BankName     string  `json:"bankName"`
	BankCode     string  `json:"bankCode"`
	Hash         string  `json:"hash"`
}
type MeepayDepositCreateRemoteResponse struct {
	TransactionId string `json:"transactionId"`
	Merchant      string `json:"merchant"`
	Amount        string `json:"amount"`
	QrCode        string `json:"qrCode"`
	Reference     string `json:"reference"`
	Status        string `json:"status"`
	Message       string `json:"message"`
}
type MeepayGetDepositOrderRemoteRequest struct {
	ClientId     string `json:"clientId"`
	ClientSecret string `json:"clientSecret"`
	Hash         string `json:"hash"`
}

type MeepayGetMerchantBalanceRemoteRequest struct {
	ClientId     string `json:"clientId"`
	ClientSecret string `json:"clientSecret"`
}
type MeepayCheckBalanceRemoteResponse struct {
	Merchant  string `json:"merchant"`
	Available struct {
		Amount string `json:"amount"`
	} `json:"available"`
	Pending struct {
		Amount string `json:"amount"`
	} `json:"pending"`
}
type MeepayCheckBalanceResponse struct {
	Balance         float64 `json:"balance"`
	BalanceWithdraw float64 `json:"balance_withdraw"`
	Date            string  `json:"date"`
}

type MeepayGetOrderRemoteResponse struct {
	Merchant      string `json:"merchant"`
	TransferDate  string `json:"transferDate"`
	Amount        string `json:"amount"`
	TransactionId string `json:"transactionId"`
	Reference     string `json:"reference"`
	BankCode      string `json:"bankCode"`
	BankId        string `json:"bankId"`
	BankName      string `json:"bankName"`
	PaymentStatus string `json:"paymentStatus"`
	Status        string `json:"status"`
	Message       string `json:"message"`
}

type MeepayWithdrawCreateRemoteRequest struct {
	ClientId     string  `json:"clientId"`
	ClientSecret string  `json:"clientSecret"`
	Amount       float64 `json:"amount"`
	Reference    string  `json:"reference"`
	CallbackUrl  string  `json:"callbackUrl"`
	BankId       string  `json:"bankId"`
	BankName     string  `json:"bankName"`
	BankCode     string  `json:"bankCode"`
	Hash         string  `json:"hash"`
}
type MeepayWithdrawCreateRemoteResponse struct {
	TransactionId string `json:"transactionId"`
	Merchant      string `json:"merchant"`
	Amount        string `json:"amount"`
	QrCode        string `json:"qrCode"`
	Reference     string `json:"reference"`
	Status        string `json:"status"`
	Message       string `json:"message"`
}

type MeepayWebhookRequest struct {
	JsonPayload string `json:"jsonPayload"`
}
type MeepayWebhookCreateBody struct {
	Id          int64  `json:"id"`
	Name        string `json:"name"`
	JsonPayload string `json:"jsonPayload"`
}

type MeepayWebhookReturnResponse struct {
	Status string `json:"status"`
}

type MeepayDepositWebhookResponse struct {
	Amount        float64 `json:"amount"`
	BankCode      string  `json:"bankCode"`
	BankId        string  `json:"bankId"`
	BankName      string  `json:"bankName"`
	Hash          string  `json:"hash"`
	Merchant      string  `json:"merchant"`
	Message       string  `json:"message"`
	Reference     string  `json:"reference"`
	Status        string  `json:"status"`
	TransactionId string  `json:"transactionId"`
	TransferDate  string  `json:"transferDate"`
}
type MeepayDepositWebhook2Response struct {
	Amount        string `json:"amount"`
	BankCode      string `json:"bankCode"`
	BankId        string `json:"bankId"`
	BankName      string `json:"bankName"`
	Hash          string `json:"hash"`
	Merchant      string `json:"merchant"`
	Message       string `json:"message"`
	Reference     string `json:"reference"`
	Status        string `json:"status"`
	TransactionId string `json:"transactionId"`
	TransferDate  string `json:"transferDate"`
}
type MeepayWithdrawWebhookResponse struct {
	Amount        float64 `json:"amount"`
	BankCode      string  `json:"bankCode"`
	BankId        string  `json:"bankId"`
	BankName      string  `json:"bankName"`
	Hash          string  `json:"hash"`
	Merchant      string  `json:"merchant"`
	Message       string  `json:"message"`
	Reference     string  `json:"reference"`
	Status        string  `json:"status"`
	TransactionId string  `json:"transactionId"`
	TransferDate  string  `json:"transferDate"`
}
type MeepayWithdrawWebhook2Response struct {
	Amount        string `json:"amount"`
	BankCode      string `json:"bankCode"`
	BankId        string `json:"bankId"`
	BankName      string `json:"bankName"`
	Hash          string `json:"hash"`
	Merchant      string `json:"merchant"`
	Message       string `json:"message"`
	Reference     string `json:"reference"`
	Status        string `json:"status"`
	TransactionId string `json:"transactionId"`
	TransferDate  string `json:"transferDate"`
}

type MeepayCustomerDepositInfo struct {
	Name        string `json:"name"`
	DisplayName string `json:"displayName"`
	ShopName    string `json:"shopName"`
	MinAmount   int64  `json:"minAmount"`
	MaxAmount   int64  `json:"maxAmount"`
}
type MeepayDepositCreateRequest struct {
	UserId int64   `form:"userId" json:"-" validate:"required"`
	Amount float64 `form:"amount" json:"amount" binding:"required,min=1" validate:"required,min=1"`
}
type MeepayWithdrawCreateRequest struct {
	RefId  int64   `form:"refId" validate:"required"`
	UserId int64   `form:"userId" json:"-" validate:"required"`
	Amount float64 `form:"amount" binding:"required,min=1" validate:"required,min=1"`
}
type MeepayDepositCancelRequest struct {
	OrderId int64 `form:"orderId" validate:"required"`
	UserId  int64 `form:"userId" json:"-" validate:"required"`
}

const (
	MEEPAY_ORDER_TYPE_DEPOSIT        = 1
	MEEPAY_ORDER_TYPE_WITHDRAW       = 2
	MEEPAY_ORDER_STATUS_PENDING      = "PENDING"
	MEEPAY_ORDER_STATUS_WAIT_PAYMENT = "WAIT_PAYMENT"
	MEEPAY_ORDER_STATUS_SUCCESS      = "SUCCESS"
	MEEPAY_ORDER_STATUS_ERROR        = "ERROR"
)

type MeepayOrder struct {
	Id                int64      `json:"id"`
	UserId            int64      `json:"userId"`
	OrderTypeId       int64      `json:"orderTypeId"`
	RefId             *int64     `json:"refId"`
	OrderNo           string     `json:"orderNo"`
	Amount            float64    `json:"amount"`
	TransactionNo     *string    `json:"transactionNo"`
	TransactionDate   *time.Time `json:"transactionDate"`
	TransactionStatus *string    `json:"transactionStatus"`
	PaymentAt         *time.Time `json:"paymentAt"`
	QrPromptpay       *string    `json:"qrPromptpay"`
	QrBase64          string     `json:"qrBase64"`
	ExtraPromptpayId  string     `json:"extraPromptpayId"`
	PaymentPageUrl    string     `json:"paymentPageUrl"`
	Remark            *string    `json:"remark"`
	ApiRemark         *string    `json:"apiRemark"`
	CreatedAt         time.Time  `json:"createdAt"`
	UpdatedAt         *time.Time `json:"updatedAt"`
}
type MeepayOrderListRequest struct {
	UserId        *int64 `form:"userId"`
	OrderTypeId   *int64 `form:"orderTypeId"`
	OrderNo       string `form:"orderNo"`
	TransactionNo string `form:"transactionNo"`
	Amount        string `form:"amount"`
	Status        string `form:"status"`
	Page          int    `form:"page" default:"1" min:"1"`
	Limit         int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol       string `form:"sortCol"`
	SortAsc       string `form:"sortAsc"`
}
type MeepayOrderResponse struct {
	Id                    int64      `json:"id"`
	UserId                int64      `json:"userId"`
	OrderTypeId           int64      `json:"orderTypeId"`
	OrderTypeName         string     `json:"orderTypeName"`
	RefId                 *int64     `json:"refId"`
	OrderNo               string     `json:"orderNo"`
	Amount                float64    `json:"amount"`
	TransferAmount        float64    `json:"transferAmount"`
	TransactionNo         string     `json:"transactionNo"`
	TransactionDate       *time.Time `json:"transactionDate"`
	TransactionStatus     string     `json:"transactionStatus"`
	PaymentAt             *time.Time `json:"paymentAt"`
	BankTransactionId     string     `json:"bankTransactionId"`
	BankTransactionStatus string     `json:"bankTransactionStatus"`
	QrPromptpay           string     `json:"qrPromptpay"`
	QrBase64              string     `json:"qrBase64"`
	ExtraPromptpayId      string     `json:"extraPromptpayId"`
	PaymentPageUrl        string     `json:"paymentPageUrl"`
	Remark                string     `json:"remark"`
	ApiRemark             string     `json:"apiRemark"`
	CreatedAt             time.Time  `json:"createdAt"`
	UpdatedAt             *time.Time `json:"updatedAt"`
}
type MeepayOrderWebResponse struct {
	OrderId           int64     `json:"orderId"`
	UserId            int64     `json:"userId"`
	OrderNo           string    `json:"orderNo"`
	Amount            float64   `json:"amount"`
	TransferAmount    float64   `json:"transferAmount"`
	TransactionStatus string    `json:"transactionStatus"`
	QrCode            string    `json:"qrCode"`
	QrBase64          string    `json:"qrBase64"`
	ExtraPromptpayId  string    `json:"extraPromptpayId"`
	QrUrl             string    `json:"qrUrl"`
	PaymentPageUrl    string    `json:"paymentPageUrl"`
	CreatedAt         time.Time `json:"createdAt"`
}
type MeepayOrderQrResponse struct {
	Id               int64   `json:"id"`
	OrderNo          string  `json:"orderNo"`
	Amount           float64 `json:"amount"`
	QrBase64         string  `json:"qrBase64"`
	ExtraPromptpayId string  `json:"extraPromptpayId"`
}
type MeepayOrderCreateRequest struct {
	UserId *int64  `form:"userId"`
	Amount float64 `form:"amount"`
}
type MeepayOrderCreateBody struct {
	Id                int64   `json:"id"`
	UserId            int64   `json:"userId"`
	OrderTypeId       int64   `json:"orderTypeId"`
	RefId             *int64  `json:"refId"`
	OrderNo           string  `json:"orderNo"`
	Amount            float64 `json:"amount"`
	TransactionStatus string  `json:"transactionStatus"`
}
type MeepayOrderUpdateBody struct {
	TransactionNo     string    `json:"transactionNo"`
	TransactionDate   time.Time `json:"transactionDate"`
	TransactionStatus string    `json:"transactionStatus"`
	TransferAmount    float64   `json:"transferAmount"`
	QrPromptpay       string    `json:"qrPromptpay"`
	QrBase64          string    `json:"qrBase64"`
	ExtraPromptpayId  string    `json:"extraPromptpayId"`
	PaymentPageUrl    string    `json:"paymentPageUrl"`
}
