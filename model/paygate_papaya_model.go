package model

import (
	"time"
)

type PapayaPayErrorRemoteResponse struct {
	StatusCode int    `json:"statusCode"`
	Message    string `json:"message"`
	Error      string `json:"error"`
}

//	{
//	    "statusCode": 200,
//	    "data": {
//	        "fundOutStatus": "REJECTED",
//	        "message": "Fund out request REJECTED due to Invalid Amount or Insufficient Balance",
//	        "data": {
//	            "type": "FundOut",
//	            "currencyCode": "THB",
//	            "fundOutPaymentReference": "TRRSL2OM2R6P4456",
//	            "fundOutStatus": "REJECTED",
//	            "accountNumber": "**********",
//	            "bankCode": "004",
//	            "amount": 30,
//	            "transactionRef1": "U94NP1UI4HPPB4UBRIQT",
//	            "serviceFee": null,
//	            "isPaid": false,
//	            "fundOutCallbackStatus": "00_NotSent",
//	            "fundOutDescription": "Withdrawal",
//	            "createdDate": "2024-07-11T20:34:58.000Z",
//	            "updatedDate": "2024-07-11T20:34:58.000Z"
//	        }
//	    }
//	}
type PapayaPayWithdrawErrorRemoteResponse struct {
	StatusCode int `json:"statusCode"`
	Data       struct {
		FundOutStatus string `json:"fundOutStatus"`
		Message       string `json:"message"`
		Data          struct {
			Type                    string      `json:"type"`
			CurrencyCode            string      `json:"currencyCode"`
			FundOutPaymentReference string      `json:"fundOutPaymentReference"`
			FundOutStatus           string      `json:"fundOutStatus"`
			AccountNumber           string      `json:"accountNumber"`
			BankCode                string      `json:"bankCode"`
			Amount                  float64     `json:"amount"`
			TransactionRef1         string      `json:"transactionRef1"`
			ServiceFee              interface{} `json:"serviceFee"`
			IsPaid                  bool        `json:"isPaid"`
			FundOutCallbackStatus   string      `json:"fundOutCallbackStatus"`
			FundOutDescription      string      `json:"fundOutDescription"`
			CreatedDate             time.Time   `json:"createdDate"`
			UpdatedDate             time.Time   `json:"updatedDate"`
		} `json:"data"`
	} `json:"data"`
}

type PapayaPayDepositCreateRemoteRequest struct {
	QrCodeTransactionId string  `form:"qrCodeTransactionId" json:"qrCodeTransactionId"`
	Currency            string  `form:"currency" json:"currency"`
	Amount              float64 `form:"amount" json:"amount"`
	PayMethod           string  `form:"payMethod" json:"payMethod"`
	BankCode            string  `form:"bankCode" json:"bankCode"`
	AccountNumber       string  `form:"accountNumber" json:"accountNumber"`
	AccountName         string  `form:"accountName" json:"accountName"`
	Description         string  `form:"description" json:"description"`
}
type PapayaPayDepositCreateRemoteResponse struct {
	StatusCode int    `json:"statusCode"`
	PayUrl     string `json:"payurl"`
	Message    string `json:"message"`
}

type PapayaPayCheckBalanceRemoteResponse struct {
	Success bool `json:"success"`
	Code    int  `json:"code"`
	Data    struct {
		InBalance struct {
			CoinChain string  `json:"coinChain"`
			CoinUnit  string  `json:"coinUnit"`
			Balance   float64 `json:"balance"`
			Freeze    float64 `json:"freeze"`
		} `json:"inBalance"`
		OutBalance struct {
			CoinChain string  `json:"coinChain"`
			CoinUnit  string  `json:"coinUnit"`
			Balance   float64 `json:"balance"`
			Freeze    float64 `json:"freeze"`
		} `json:"outBalance"`
		Sign             string `json:"sign"`
		CheckRequestTime int64  `json:"checkRequestTime"`
		DfBalance        struct {
			CoinChain string  `json:"coinChain"`
			CoinUnit  string  `json:"coinUnit"`
			Balance   float64 `json:"balance"`
			Freeze    float64 `json:"freeze"`
		} `json:"dfBalance"`
		DsBalance struct {
			CoinChain string  `json:"coinChain"`
			CoinUnit  string  `json:"coinUnit"`
			Balance   float64 `json:"balance"`
			Freeze    float64 `json:"freeze"`
		} `json:"dsBalance"`
	} `json:"data"`
}

type PapayaPayGetOrderRemoteResponse struct {
	Success bool `json:"success"`
	Code    int  `json:"code"`
	Data    struct {
		OrderNo       string  `json:"orderNo"`
		ReceiveAddr   string  `json:"receiveAddr"`
		ChainName     string  `json:"chainName"`
		CoinUnit      string  `json:"coinUnit"`
		RequestAmount float64 `json:"requestAmount"`
		PayAmount     float64 `json:"payAmount"`
		Status        string  `json:"status"`
		Sign          string  `json:"sign"`
	} `json:"data"`
}

type PapayaPayWithdrawCreateRemoteRequest struct {
	CurrencyCode            string  `form:"currencyCode" json:"currencyCode"`
	FundOutPaymentReference string  `form:"fundOutPaymentReference" json:"fundOutPaymentReference"`
	FundOutDescription      string  `form:"fundOutDescription" json:"fundOutDescription"`
	AccountName             string  `form:"accountName" json:"accountName"`
	AccountNumber           string  `form:"accountNumber" json:"accountNumber"`
	BankCode                string  `form:"bankCode" json:"bankCode"`
	Amount                  float64 `form:"amount" json:"amount"`
}

type PapayaPayWithdrawCreateRemoteResponse struct {
	StatusCode int `json:"statusCode"`
	Data       struct {
		FundOutStatus string `json:"fundOutStatus"`
		Message       string `json:"message"`
		Data          struct {
			Type                    string    `json:"type"`
			CurrencyCode            string    `json:"currencyCode"`
			FundOutPaymentReference string    `json:"fundOutPaymentReference"`
			FundOutStatus           string    `json:"fundOutStatus"`
			AccountNumber           string    `json:"accountNumber"`
			BankCode                string    `json:"bankCode"`
			Amount                  float64   `json:"amount"`
			TransactionRef1         string    `json:"transactionRef1"`
			ServiceFee              float64   `json:"serviceFee"`
			IsPaid                  bool      `json:"isPaid"`
			FundOutCallbackStatus   string    `json:"fundOutCallbackStatus"`
			FundOutDescription      string    `json:"fundOutDescription"`
			CreatedDate             time.Time `json:"createdDate"`
			UpdatedDate             time.Time `json:"updatedDate"`
		} `json:"data"`
	} `json:"data"`
}

type PapayaPayWebhookRequest struct {
	JsonPayload string `json:"jsonPayload"`
}
type PapayaPayWebhookCreateBody struct {
	Id          int64  `json:"id"`
	Name        string `json:"name"`
	JsonPayload string `json:"jsonPayload"`
}

type PapayaPayDepositWebhookResponse struct {
	Status              string    `json:"status"`
	Type                string    `json:"type"`
	CallbackStatus      string    `json:"callbackStatus"`
	CurrencyCode        string    `json:"currencyCode"`
	Amount              float64   `json:"amount"`
	Fees                float64   `json:"fees"`
	QrCodeTransactionId string    `json:"qrCodeTransactionId"`
	TransactionRef1     string    `json:"transactionRef1"`
	TransactionRef2     string    `json:"transactionRef2"`
	TransactionRef3     string    `json:"transactionRef3"`
	Merchant            string    `json:"merchant"`
	CreatedAt           time.Time `json:"createdAt"`
	UpdatedAt           time.Time `json:"updatedAt"`
	QrPayBankRef        string    `json:"qrPayBankRef"`
	Id                  string    `json:"id"`
}
type PapayaPayWithDrawWebhookResponse struct {
	Type                    string    `json:"type"`
	CurrencyCode            string    `json:"currencyCode"`
	FundOutStatus           string    `json:"fundOutStatus"`
	Amount                  float64   `json:"amount"`
	ServiceFee              float64   `json:"serviceFee"`
	IsPaid                  bool      `json:"isPaid"`
	TransactionRemark       string    `json:"transactionRemark"`
	FundOutDescription      string    `json:"fundOutDescription"`
	TransactionRef1         string    `json:"transactionRef1"`
	FundOutPaymentReference string    `json:"fundOutPaymentReference"`
	BankName                string    `json:"bankName"`
	BankCode                string    `json:"bankCode"`
	AccountNumber           string    `json:"accountNumber"`
	CreatedAt               time.Time `json:"createdAt"`
	UpdatedAt               time.Time `json:"updatedAt"`
	BankTransactionRef      string    `json:"bankTransactionRef"`
}

type PapayaPayCustomerDepositInfo struct {
	Name        string `json:"name"`
	DisplayName string `json:"displayName"`
	ShopName    string `json:"shopName"`
	MinAmount   int64  `json:"minAmount"`
	MaxAmount   int64  `json:"maxAmount"`
}
type PapayaPayDepositCreateRequest struct {
	UserId int64   `form:"userId" json:"-" validate:"required"`
	Amount float64 `form:"amount" json:"amount" binding:"required,min=1" validate:"required,min=1"`
}
type PapayaPayWithdrawCreateRequest struct {
	RefId  int64   `form:"refId" validate:"required"`
	UserId int64   `form:"userId" json:"-" validate:"required"`
	Amount float64 `form:"amount" binding:"required,min=1" validate:"required,min=1"`
}

const (
	PAPAYAPAY_ORDER_TYPE_DEPOSIT  = 1
	PAPAYAPAY_ORDER_TYPE_WITHDRAW = 2
)

type PapayaPayOrder struct {
	Id                int64      `json:"id"`
	UserId            int64      `json:"userId"`
	OrderTypeId       int64      `json:"orderTypeId"`
	RefId             *int64     `json:"refId"`
	OrderNo           string     `json:"orderNo"`
	Amount            float64    `json:"amount"`
	TransactionNo     *string    `json:"transactionNo"`
	TransactionDate   *time.Time `json:"transactionDate"`
	TransactionStatus *string    `json:"transactionStatus"`
	PaymentAt         *time.Time `json:"paymentAt"`
	QrPromptpay       *string    `json:"qrPromptpay"`
	CreatedAt         time.Time  `json:"createdAt"`
	UpdatedAt         *time.Time `json:"updatedAt"`
}
type PapayaPayOrderListRequest struct {
	UserId        *int64 `form:"userId"`
	OrderTypeId   *int64 `form:"orderTypeId"`
	OrderNo       string `form:"orderNo"`
	TransactionNo string `form:"transactionNo"`
	Amount        string `form:"amount"`
	Status        string `form:"status"`
	Page          int    `form:"page" default:"1" min:"1"`
	Limit         int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol       string `form:"sortCol"`
	SortAsc       string `form:"sortAsc"`
}
type PapayaPayOrderResponse struct {
	Id                int64      `json:"id"`
	UserId            int64      `json:"userId"`
	OrderTypeId       int64      `json:"orderTypeId"`
	OrderTypeName     string     `json:"orderTypeName"`
	RefId             *int64     `json:"refId"`
	OrderNo           string     `json:"orderNo"`
	Amount            float64    `json:"amount"`
	TransactionNo     *string    `json:"transactionNo"`
	TransactionDate   *time.Time `json:"transactionDate"`
	TransactionStatus *string    `json:"transactionStatus"`
	PaymentAt         *time.Time `json:"paymentAt"`
	QrPromptpay       string     `json:"qrPromptpay"`
	CreatedAt         time.Time  `json:"createdAt"`
	UpdatedAt         *time.Time `json:"updatedAt"`
}
type PapayaPayOrderWebResponse struct {
	UserId            int64     `json:"userId"`
	OrderNo           string    `json:"orderNo"`
	Amount            float64   `json:"amount"`
	TransactionStatus string    `json:"transactionStatus"`
	PaymentUrl        string    `json:"paymentUrl"`
	CreatedAt         time.Time `json:"createdAt"`
}
type PapayaPayOrderQrResponse struct {
	Id       int64   `json:"id"`
	OrderNo  string  `json:"orderNo"`
	Amount   float64 `json:"amount"`
	QrBase64 string  `json:"qrBase64"`
}
type PapayaPayOrderCreateRequest struct {
	UserId *int64  `form:"userId"`
	Amount float64 `form:"amount"`
}
type PapayaPayOrderCreateBody struct {
	Id                int64   `json:"id"`
	UserId            int64   `json:"userId"`
	OrderTypeId       int64   `json:"orderTypeId"`
	RefId             *int64  `json:"refId"`
	OrderNo           string  `json:"orderNo"`
	Amount            float64 `json:"amount"`
	TransactionStatus string  `json:"transactionStatus"`
}
type PapayaPayOrderUpdateBody struct {
	TransactionNo     string    `json:"transactionNo"`
	TransactionDate   time.Time `json:"transactionDate"`
	TransactionStatus string    `json:"transactionStatus"`
	QrPromptpay       string    `json:"qrPromptpay"`
}
