package model

import (
	"time"
)

const (
	ONEPAY_DEFMIN_DEPOSIT_AMOUNT  = 10
	ONEPAY_DEFMAX_DEPOSIT_AMOUNT  = 50000
	ONEPAY_DEFMIN_WITHDRAW_AMOUNT = 100
	ONEPAY_DEFMAX_WITHDRAW_AMOUNT = 100000
)

type OnepayWebhookDepositResponse struct {
	Sign               string `json:"sign"`
	Timestamp          int64  `json:"timestamp"`
	OrderNo            string `json:"order_no"`
	MchOrderNo         string `json:"mch_order_no"`
	Status             int    `json:"status"`
	Amount             string `json:"amount"`
	PayTime            string `json:"pay_time"`
	RepayAccountBank   string `json:"repay_account_bank"`
	RepayAccountNumber string `json:"repay_account_number"`
	AccountNumber      string `json:"account_number"`
	AccountBank        string `json:"account_bank"`
}

type OnepayErrorRemoteResponse struct {
	StatusCode int    `json:"statusCode"`
	Message    string `json:"message"`
}
type OnepayError2RemoteResponse struct {
	Message    string `json:"message"`
	Error      string `json:"error"`
	StatusCode int    `json:"statusCode"`
}

type OnepayToken struct {
	Id          int64      `json:"id"`
	AccessToken string     `json:"accessToken"`
	ExpireAt    time.Time  `json:"expireAt"`
	CreateBy    int64      `json:"createBy"`
	CreatedAt   time.Time  `json:"createdAt"`
	UpdatedAt   *time.Time `json:"updatedAt"`
}
type OnepayTokenCreateBody struct {
	Id          int64     `json:"id"`
	AccessToken string    `json:"accessToken"`
	ExpireAt    time.Time `json:"expireAt"`
	CreateBy    int64     `json:"createBy"`
}
type OnepayTokenCreateRemoteRequest struct {
	MerchantAccesskey string `json:"merchant_accesskey" validate:"required"`
}
type OnepayTokenCreateRemoteResponse struct {
	Token string `json:"token"`
}

type OnepayCustomerCreateRemoteRequest struct {
	Name      string `json:"name" validate:"required"`
	BankCode  string `json:"bankCode" validate:"required"`
	AccountNo string `json:"accountNo" validate:"required"`
}
type OnepayCustomerCreateRemoteResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Code    string `json:"code"`
	Data    struct {
		Partner      string   `json:"partner"`
		CustomerUuid string   `json:"customerUuid"`
		ClientCode   string   `json:"clientCode"`
		Name         string   `json:"name"`
		SearchName   []string `json:"searchName"`
		AccountNo    string   `json:"accountNo"`
		BankCode     string   `json:"bankCode"`
		Status       string   `json:"status"`
		CreatedAt    int64    `json:"createdAt"`
		UpdatedAt    int64    `json:"updatedAt"`
	} `json:"data"`
}
type OnepayCustomerUpdateRemoteRequest struct {
	CustomerUuid string `json:"customerUuid" validate:"required"`
	Name         string `json:"name" validate:"required"`
	BankCode     string `json:"bankCode" validate:"required"`
	AccountNo    string `json:"accountNo" validate:"required"`
}
type OnepayCustomerUpdateRemoteResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Code    string `json:"code"`
	Data    struct {
		Partner      string   `json:"partner"`
		CustomerUuid string   `json:"customerUuid"`
		ClientCode   string   `json:"clientCode"`
		Name         string   `json:"name"`
		SearchName   []string `json:"searchName"`
		AccountNo    string   `json:"accountNo"`
		BankCode     string   `json:"bankCode"`
		Status       string   `json:"status"`
		CreatedAt    int64    `json:"createdAt"`
		UpdatedAt    int64    `json:"updatedAt"`
	} `json:"data"`
}

type OnepayDepositCreateRemoteRequest struct {
	OrderId     string  `json:"order_id" validate:"required"`
	Amount      float64 `json:"amount" validate:"required"`
	RefAccount  string  `json:"ref_account" validate:"required"`
	RefBankCode string  `json:"ref_bank_code" validate:"required"`
	RefNameTh   string  `json:"ref_name_th" validate:"required"`
	RefNameEn   string  `json:"ref_name_en" validate:"required"`
	RefUserId   string  `json:"ref_user_id" validate:"required"`
	Ref1        string  `json:"ref1"`
	Ref2        string  `json:"ref2"`
	CallbackUrl string  `json:"callback_url" validate:"required"`
}
type OnepayDepositCreateRemoteResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Data    struct {
		QrCodeId  string `json:"qr_code_id"`
		QrCodeUrl string `json:"qr_code_url"`
		CreatedAt string `json:"created_at"`
		QrCode    string `json:"qr_code"`
		ExpiredAt string `json:"expired_at"`
		Amount    string `json:"amount"`
		RefNameTh string `json:"ref_name_th"`
		RefNameEn string `json:"ref_name_en"`
		RefUserId string `json:"ref_user_id"`
		TxnNo     string `json:"txn_no"`
	} `json:"data"`
}

type OnepayCheckBalanceRemoteResponse struct {
	Success bool `json:"success"`
	Code    int  `json:"code"`
	Data    struct {
		InBalance struct {
			CoinChain string  `json:"coinChain"`
			CoinUnit  string  `json:"coinUnit"`
			Balance   float64 `json:"balance"`
			Freeze    float64 `json:"freeze"`
		} `json:"inBalance"`
		OutBalance struct {
			CoinChain string  `json:"coinChain"`
			CoinUnit  string  `json:"coinUnit"`
			Balance   float64 `json:"balance"`
			Freeze    float64 `json:"freeze"`
		} `json:"outBalance"`
		Sign             string `json:"sign"`
		CheckRequestTime int64  `json:"checkRequestTime"`
		DfBalance        struct {
			CoinChain string  `json:"coinChain"`
			CoinUnit  string  `json:"coinUnit"`
			Balance   float64 `json:"balance"`
			Freeze    float64 `json:"freeze"`
		} `json:"dfBalance"`
		DsBalance struct {
			CoinChain string  `json:"coinChain"`
			CoinUnit  string  `json:"coinUnit"`
			Balance   float64 `json:"balance"`
			Freeze    float64 `json:"freeze"`
		} `json:"dsBalance"`
	} `json:"data"`
}

type OnepayGetOrderRemoteResponse struct {
	Success bool `json:"success"`
	Code    int  `json:"code"`
	Data    struct {
		OrderNo       string  `json:"orderNo"`
		ReceiveAddr   string  `json:"receiveAddr"`
		ChainName     string  `json:"chainName"`
		CoinUnit      string  `json:"coinUnit"`
		RequestAmount float64 `json:"requestAmount"`
		PayAmount     float64 `json:"payAmount"`
		Status        string  `json:"status"`
		Sign          string  `json:"sign"`
	} `json:"data"`
}
type OnepayWithdrawCreateRemoteRequest struct {
	OrderId      string  `json:"order_id" validate:"required"`
	Amount       float64 `json:"amount" validate:"required"`
	BankAccNo    string  `json:"bank_acc_no" validate:"required"`
	BankAccName  string  `json:"bank_acc_name" validate:"required"`
	BankCode     string  `json:"bank_code" validate:"required"`
	WithdrawCode string  `json:"withdraw_code"`
	CallbackUrl  string  `json:"callback_url"`
}
type OnepayWithdrawCreateRemoteResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Data    struct {
		TxnNo        string `json:"txn_no"`
		OrderId      string `json:"order_id"`
		BankCode     string `json:"bank_code"`
		BankAccNo    string `json:"bank_acc_no"`
		BankAccName  string `json:"bank_acc_name"`
		Amount       int    `json:"amount"`
		WithdrawTime string `json:"withdraw_time"`
		CreatedAt    string `json:"created_at"`
	} `json:"data"`
}

type OnepayWebhookRequest struct {
	JsonPayload string `json:"jsonPayload"`
}
type OnepayWebhookCreateBody struct {
	Id          int64  `json:"id"`
	Name        string `json:"name"`
	JsonPayload string `json:"jsonPayload"`
}

type OnepayWebhookResponse struct {
	Success           bool    `json:"success"`
	StatusDes         string  `json:"status_des"`
	Type              string  `json:"type"`
	Amount            float64 `json:"amount"`
	TxnNo             string  `json:"txn_no"`
	TxnRefId          string  `json:"txn_ref_id"`
	TxnRefOrderId     string  `json:"txn_ref_order_id"`
	TxnRefBankCode    string  `json:"txn_ref_bank_code"`
	TxnRefBankAccNo   string  `json:"txn_ref_bank_acc_no"`
	TxnRefBankAccName string  `json:"txn_ref_bank_acc_name"`
	TxnRefUserId      string  `json:"txn_ref_user_id"`
	TxnRef1           string  `json:"txn_ref1"`
	TxnRef2           string  `json:"txn_ref2"`
	TxnTimestamp      string  `json:"txn_timestamp"`
	StmTimestamp      string  `json:"stm_timestamp"`
	StmBankCode       string  `json:"stm_bank_code"`
	StmBankAccNo      string  `json:"stm_bank_acc_no"`
	StmBankAccName    string  `json:"stm_bank_acc_name"`
	StmDesc           string  `json:"stm_desc"`
	StmRefId          string  `json:"stm_ref_id"`
}

//	{
//	    "success": true,
//	    "status_des": "success_deposit",
//	    "type": "deposit",
//	    "amount": 24.07,
//	    "txn_ref_order_id": "DEV250225",
//	    "txn_ref_bank_code": "kbank",
//	    "txn_ref_bank_acc_no": "**********",
//	    "txn_ref_user_id": "5002",
//	    "txn_ref1": "",
//	    "txn_ref2": "",
//	    "txn_timestamp": "2025-02-14 11:50:00",
//	    "stm_timestamp": "2025-02-14 11:50:00",
//	    "stm_bank_code": "",
//	    "stm_bank_acc_no": "",
//	    "txn_ref_id": "52806ee5-3470-4e57-a7ef-12f5b791e1b7"
//	}
//
// [********] เปลี่ยนจาก TxnNo เป็น TxnRefId
type OnepayDepositWebhookResponse struct {
	Success           bool    `json:"success"`
	StatusDes         string  `json:"status_des"`
	Type              string  `json:"type"`
	Amount            float64 `json:"amount"`
	TxnNo             string  `json:"txn_no"`
	TxnRefOrderId     string  `json:"txn_ref_order_id"`
	TxnRefBankCode    string  `json:"txn_ref_bank_code"`
	TxnRefBankAccNo   string  `json:"txn_ref_bank_acc_no"`
	TxnRefBankAccName string  `json:"txn_ref_bank_acc_name"`
	TxnRefUserId      string  `json:"txn_ref_user_id"`
	TxnRef1           string  `json:"txn_ref1"`
	TxnRef2           string  `json:"txn_ref2"`
	TxnTimestamp      string  `json:"txn_timestamp"`
	StmTimestamp      string  `json:"stm_timestamp"`
	StmBankCode       string  `json:"stm_bank_code"`
	StmBankAccNo      string  `json:"stm_bank_acc_no"`
	StmBankAccName    string  `json:"stm_bank_acc_name"`
	StmDesc           string  `json:"stm_desc"`
}
type OnepayWithdrawWebhookResponse struct {
	Success           bool    `json:"success"`
	StatusDes         string  `json:"status_des"`
	Type              string  `json:"type"`
	Amount            float64 `json:"amount"`
	TxnRefId          string  `json:"txn_ref_id"`
	TxnRefOrderId     string  `json:"txn_ref_order_id"`
	TxnRefBankCode    string  `json:"txn_ref_bank_code"`
	TxnRefBankAccNo   string  `json:"txn_ref_bank_acc_no"`
	TxnRefBankAccName string  `json:"txn_ref_bank_acc_name"`
	TxnRefUserId      string  `json:"txn_ref_user_id"`
	TxnRef1           string  `json:"txn_ref1"`
	TxnRef2           string  `json:"txn_ref2"`
	TxnTimestamp      string  `json:"txn_timestamp"`
	StmTimestamp      string  `json:"stm_timestamp"`
	StmBankCode       string  `json:"stm_bank_code"`
	StmBankAccNo      string  `json:"stm_bank_acc_no"`
	StmBankAccName    string  `json:"stm_bank_acc_name"`
	StmDesc           string  `json:"stm_desc"`
	StmRefId          string  `json:"stm_ref_id"`
}

type OnepayCustomerDepositInfo struct {
	Name        string `json:"name"`
	DisplayName string `json:"displayName"`
	ShopName    string `json:"shopName"`
	MinAmount   int64  `json:"minAmount"`
	MaxAmount   int64  `json:"maxAmount"`
}
type OnepayDepositCreateRequest struct {
	UserId int64   `form:"userId" json:"-" validate:"required"`
	Amount float64 `form:"amount" json:"amount" binding:"required,min=1" validate:"required,min=1"`
}
type OnepayWithdrawCreateRequest struct {
	RefId  int64   `form:"refId" validate:"required"`
	UserId int64   `form:"userId" json:"-" validate:"required"`
	Amount float64 `form:"amount" binding:"required,min=1" validate:"required,min=1"`
}

const (
	ONEPAY_ORDER_TYPE_DEPOSIT  = 1
	ONEPAY_ORDER_TYPE_WITHDRAW = 2
)

type OnepayCustomer struct {
	Id           int64  `json:"id"`
	UserId       int64  `json:"userId"`
	CustomerUuid string `json:"customerUuid"`
	FullName     string `json:"fullName"`
	BankCode     string `json:"bankCode"`
	AccountNo    string `json:"accountNo"`
	AccountName  string `json:"accountName"`
}
type OnepayCustomerListRequest struct {
	Page    int    `form:"page" default:"1" min:"1"`
	Limit   int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol string `form:"sortCol"`
	SortAsc string `form:"sortAsc"`
}
type OnepayCustomerResponse struct {
	Id           int64  `json:"id"`
	UserId       int64  `json:"userId"`
	CustomerUuid string `json:"customerUuid"`
	FullName     string `json:"fullName"`
	BankCode     string `json:"bankCode"`
	AccountNo    string `json:"accountNo"`
	AccountName  string `json:"accountName"`
}
type OnepayCustomerCreateBody struct {
	Id           int64  `json:"id"`
	UserId       int64  `json:"userId"`
	CustomerUuid string `json:"customerUuid"`
	FullName     string `json:"fullName"`
	BankCode     string `json:"bankCode"`
	AccountNo    string `json:"accountNo"`
	AccountName  string `json:"accountName"`
}
type OnepayCustomerUpdateBody struct {
	CustomerUuid *string `json:"customerUuid"`
	FullName     *string `json:"fullName"`
	BankCode     *string `json:"bankCode"`
	AccountNo    *string `json:"accountNo"`
	AccountName  *string `json:"accountName"`
}

type OnepayOrder struct {
	Id                int64      `json:"id"`
	UserId            int64      `json:"userId"`
	OrderTypeId       int64      `json:"orderTypeId"`
	RefId             *int64     `json:"refId"`
	OrderNo           string     `json:"orderNo"`
	Amount            float64    `json:"amount"`
	TransactionNo     *string    `json:"transactionNo"`
	TransactionDate   *time.Time `json:"transactionDate"`
	TransactionStatus *string    `json:"transactionStatus"`
	PaymentAt         *time.Time `json:"paymentAt"`
	QrPromptpay       *string    `json:"qrPromptpay"`
	PaymentPageUrl    string     `json:"paymentPageUrl"`
	Remark            *string    `json:"remark"`
	ApiRemark         *string    `json:"apiRemark"`
	CreatedAt         time.Time  `json:"createdAt"`
	UpdatedAt         *time.Time `json:"updatedAt"`
}
type OnepayOrderListRequest struct {
	UserId        *int64 `form:"userId"`
	OrderTypeId   *int64 `form:"orderTypeId"`
	OrderNo       string `form:"orderNo"`
	TransactionNo string `form:"transactionNo"`
	Amount        string `form:"amount"`
	Status        string `form:"status"`
	Page          int    `form:"page" default:"1" min:"1"`
	Limit         int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol       string `form:"sortCol"`
	SortAsc       string `form:"sortAsc"`
}
type OnepayOrderResponse struct {
	Id                    int64      `json:"id"`
	UserId                int64      `json:"userId"`
	OrderTypeId           int64      `json:"orderTypeId"`
	OrderTypeName         string     `json:"orderTypeName"`
	RefId                 *int64     `json:"refId"`
	OrderNo               string     `json:"orderNo"`
	Amount                float64    `json:"amount"`
	TransactionNo         *string    `json:"transactionNo"`
	TransactionDate       *time.Time `json:"transactionDate"`
	TransactionStatus     *string    `json:"transactionStatus"`
	PaymentAt             *time.Time `json:"paymentAt"`
	BankTransactionId     *string    `json:"bankTransactionId"`
	BankTransactionStatus string     `json:"bankTransactionStatus"`
	QrPromptpay           string     `json:"qrPromptpay"`
	PaymentPageUrl        string     `json:"paymentPageUrl"`
	Remark                *string    `json:"remark"`
	ApiRemark             *string    `json:"apiRemark"`
	CreatedAt             time.Time  `json:"createdAt"`
	UpdatedAt             *time.Time `json:"updatedAt"`
}
type OnepayOrderWebResponse struct {
	UserId            int64     `json:"userId"`
	OrderNo           string    `json:"orderNo"`
	Amount            float64   `json:"amount"`
	TransferAmount    float64   `json:"transferAmount"`
	TransactionStatus string    `json:"transactionStatus"`
	QrCode            string    `json:"qrCode"`
	QrBase64          string    `json:"qrBase64"`
	PaymentPageUrl    string    `json:"paymentPageUrl"`
	CreatedAt         time.Time `json:"createdAt"`
}
type OnepayOrderQrResponse struct {
	Id       int64   `json:"id"`
	OrderNo  string  `json:"orderNo"`
	Amount   float64 `json:"amount"`
	QrBase64 string  `json:"qrBase64"`
}
type OnepayOrderCreateRequest struct {
	UserId *int64  `form:"userId"`
	Amount float64 `form:"amount"`
}
type OnepayOrderCreateBody struct {
	Id                int64   `json:"id"`
	UserId            int64   `json:"userId"`
	OrderTypeId       int64   `json:"orderTypeId"`
	RefId             *int64  `json:"refId"`
	OrderNo           string  `json:"orderNo"`
	Amount            float64 `json:"amount"`
	TransactionStatus string  `json:"transactionStatus"`
}
type OnepayOrderUpdateBody struct {
	TransactionNo     string    `json:"transactionNo"`
	TransactionDate   time.Time `json:"transactionDate"`
	TransactionStatus string    `json:"transactionStatus"`
	TransferAmount    float64   `json:"transferAmount"`
	QrPromptpay       string    `json:"qrPromptpay"`
	PaymentPageUrl    string    `json:"paymentPageUrl"`
}
