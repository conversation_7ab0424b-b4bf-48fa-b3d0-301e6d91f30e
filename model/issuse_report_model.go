package model

import "time"

type IssueReport struct {
	Id             int64      `json:"id"`
	Description    string     `json:"description"`
	IssueStatusId  int64      `json:"issueStatusId"`
	IssueStatusTh  string     `json:"issueStatusTh"`
	IssueStatusEn  string     `json:"issueStatusEn"`
	CreatedByName  string     `json:"createdByName"`
	ApprovedByName string     `json:"approvedByName"`
	CreatedAt      time.Time  `json:"createdAt"`
	UpdatedAt      *time.Time `json:"updatedAt"`
}

type WebUrlId struct {
	Id int64 `json:"id"`
}

type WebUrlBody struct {
	Id            int64  `json:"id"`
	IssueReportId int64  `json:"issueReportId"`
	IssueWebUrlId int64  `json:"issueWebUrlId"`
	Url           string `json:"url"`
}
type WebUrlRes struct {
	Id  int64  `json:"id"`
	Url string `json:"url"`
}

type IssueReportListRequest struct {
	Page           int    `form:"page" default:"1" min:"1"`
	Limit          int    `form:"limit" default:"10" min:"1" max:"100"`
	Search         string `form:"search"`
	IssueStatusId  *int64 `form:"issueStatusId"`
	FromCreateDate string `form:"fromCreateDate"`
	ToCreateDate   string `form:"toCreateDate"`
	SortCol        string `form:"sortCol"`
	SortAsc        string `form:"sortAsc"`
}

type IssueReportWeb struct {
	Id            int64 `json:"id"`
	IssueReportId int64 `json:"issueReportId"`
	IssueWebUrlId int64 `json:"issueWebUrlId"`
}

type IssueReportResponse struct {
	Id             int64  `json:"id"`
	Description    string `json:"description"`
	WebUrl         []WebUrlRes
	IssueStatusId  int64     `json:"issueStatusId"`
	IssueStatusTh  string    `json:"issueStatusTh"`
	IssueStatusEn  string    `json:"issueStatusEn"`
	CreatedByName  *string   `json:"createdByName"`
	ApprovedByName *string   `json:"approvedByName"`
	CreatedAt      time.Time `json:"createdAt"`
	UpdatedAt      time.Time `json:"updatedAt"`
}

type IssueReportCreateRequest struct {
	Id             int64  `json:"-"`
	Description    string `json:"description" validate:"required"`
	WebUrl         []WebUrlId
	IssueStatusId  int64   `json:"issueStatusId" validate:"required"`
	CreatedByName  *string `json:"-"`
	ApprovedByName *string `json:"-"`
}
type IssueReportCreateBody struct {
	Id            int64     `json:"-"`
	Description   string    `json:"description" validate:"required"`
	CreatedByName string    `json:"createdByName" validate:"required"`
	IssueStatusId int64     `json:"issueStatusId" validate:"required"`
	UpdatedAt     time.Time `json:"updatedAt"`
}

type IssueReportWebCreate struct {
	Id            int64 `json:"-"`
	IssueReportId int64 `json:"issueReportId" validate:"required"`
	IssueWebUrlId int64 `json:"issueWebUrlId" validate:"required"`
}
type IssueReportGetByIdRequest struct {
	Id int64 `uri:"id" binding:"required"`
}

type IssueReportUpdateRequest struct {
	Id             int64   `json:"-"`
	Description    *string `json:"description"`
	IssueStatusId  *int64  `json:"issueStatusId"`
	ApprovedByName *string `json:"-"`
}

type IssueReportUpdateBody struct {
	Id             int64   `json:"id" validate:"required"`
	Description    *string `json:"description" validate:"required"`
	IssueStatusId  *int64  `json:"issueStatusId" validate:"required"`
	ApprovedByName *string `json:"approvedByName" validate:"required"`
}

type IssueReportBody struct {
	Id             int64  `json:"id"`
	Description    string `json:"description"`
	IssueStatusId  int64  `json:"issueStatusId"`
	IssueStatusTh  string `json:"issueStatusTh"`
	IssueStatusEn  string `json:"issueStatusEn"`
	WebUrl         []WebUrlRes
	CreatedByName  string    `json:"createdByName"`
	ApprovedByName string    `json:"approvedByName"`
	CreatedAt      time.Time `json:"createdAt"`
	UpdatedAt      time.Time `json:"updatedAt"`
}

type IssueStatus struct {
	Id      int64  `json:"id"`
	Name    string `json:"name"`
	LabelTh string `json:"labelTh"`
	LabelEn string `json:"labelEn"`
}

type WebUrl struct {
	Id       int64     `json:"id"`
	Url      string    `json:"url"`
	CreateAt time.Time `json:"createAt"`
	UpdateAt time.Time `json:"updateAt"`
	DeleteAt time.Time `json:"deleteAt"`
}
type CreateWebUrl struct {
	Id  int64  `json:"-"`
	Url string `json:"url"`
}
type GetWebUrlBody struct {
	Id  int64  `json:"id"`
	Url string `json:"url"`
}

type GetWebUrlById struct {
	Id int64 `uri:"id" binding:"required"`
}
