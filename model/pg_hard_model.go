package model

import "time"

type GetPgHardGameListResponse struct {
	Id       string `json:"id"`
	ImageUrl string `json:"imageUrl"`
	Name     string `json:"name"`
	Provider string `json:"provider"`
	StringId string `json:"stringId"`
}

type GetPgHardGameSessionParams struct {
	OperatorId  string `json:"operatorId"`
	UserToken   string `json:"userToken"`
	GameId      string `json:"gameId"`
	PresetId    string `json:"presetId,omitempty"`
	PoolId      string `json:"poolId,omitempty"`
	UsePool     bool   `json:"usePool"`
	HrefBackURL string `json:"hrefBackUrl"`
}

type GetPgHardGame struct {
	UserId int64  `json:"-"`
	GameId string `json:"gameId"`
}

type GetPgOperatorPresetResponse struct {
	Id            string  `json:"id"`
	RtpNormal     float64 `json:"rtpNormal"`
	RtpBuyFeature float64 `json:"rtpBuyFeature"`
	Note          string  `json:"note"`
	CreatedAt     string  `json:"createdAt"`
	UpdatedAt     string  `json:"updatedAt"`
}

type GetPgOperatorPresetResponseList struct {
	Presets []GetPgOperatorPresetResponse `json:"presets"`
}

type CallBackPgHardCheckBalanceRequest struct {
	UserToken string `json:"userToken"`
}

type CallBackPgHardCheckBalanceResponse struct {
	Username string  `json:"username"`
	Currency string  `json:"currency"`
	Balance  float64 `json:"balance"`
}

type CallBackPgHardGameSettleBetRequest struct {
	OperatorId   string                                  `json:"operatorId"`
	Transactions CallBackPgHardGameSettleBetTransactions `json:"transactions"`
	UserToken    string                                  `json:"userToken"`
	RoundId      string                                  `json:"roundId"`
	GameId       string                                  `json:"gameId"`
	GameStringId string                                  `json:"gameStringId"`
	GameName     string                                  `json:"gameName"`
}

type CallBackPgHardGameSettleBetTransactions struct {
	TransactionId string  `json:"transactionId"`
	Payoff        float64 `json:"payoff"`
	BetAmount     float64 `json:"betAmount"`
}

type CallBackPgHardGameSettleBetResponse struct {
	Username      string  `json:"username"`
	Currency      string  `json:"currency"`
	BalanceBefore float64 `json:"balanceBefore"`
	BalanceAfter  float64 `json:"balanceAfter"`
}

type DecreaseUserCreditFromOtherAgentRequest struct {
	UserId int64   `form:"userId"`
	Amount float64 `form:"amount"`
}
type IncreaseUserCreditFromOtherAgentRequest struct {
	UserId int64   `form:"userId"`
	Amount float64 `form:"amount"`
}
type CreateAgentPgHardCallbackBody struct {
	Id            int64   `json:"-"`
	UserId        int64   `json:"userId"`
	MemberCode    string  `json:"memberCode"`
	TransactionId string  `json:"transactionId"`
	Payoff        float64 `json:"payoff"`
	BetAmount     float64 `json:"betAmount"`
	WinloseAmount float64 `json:"winloseAmount"`
	Balance       float64 `json:"balance"`
	BeforeBalance float64 `json:"beforeBalance"`
	AfterBalance  float64 `json:"afterBalance"`
	RoundId       string  `json:"roundId"`
	GameId        string  `json:"gameId"`
	GameStringId  string  `json:"gameStringId"`
	GameName      string  `json:"gameName"`
	IsSuccess     bool    `json:"isSuccess"`
	Remark        string  `json:"remark"`
}

type UpdateAgentPgHardCallbackBody struct {
	Id            int64   `json:"-"`
	Remark        string  `json:"remark"`
	BeforeBalance float64 `json:"beforeBalance"`
	AfterBalance  float64 `json:"afterBalance"`
	WinloseAmount float64 `json:"winloseAmount"`
	IsSuccess     bool    `json:"isSuccess"`
}

type AgentPgHardCallbackSummaryRequest struct {
	PageIndex     int    `form:"pageIndex"`
	PageSize      int    `form:"pageSize"`
	StatementDate string `form:"statementDate"`
}

type AgentPgHardCallbackSummary struct {
	UserID       int64   `json:"userId"`
	MemberCode   string  `json:"memberCode"`
	TotalPayoff  float64 `json:"totalPayoff"`
	TotalBet     float64 `json:"totalBet"`
	TotalWinlose float64 `json:"totalWinlose"`
}

type GetAgentPgHardSetting struct {
	IsActive       bool   `json:"isActive"`
	PgHardPresetId string `json:"pgHardPresetId"`
}

type GetAgentPgHardSettingResponse struct {
	Description      string `json:"description"`
	PgHardPrivateKey string `json:"pgHardPrivateKey"`
	PgHardOperatorId string `json:"pgHardOperatorId"`
	PgHardPresetId   string `json:"pgHardPresetId"`
	IsActive         bool   `json:"isActive"`
}

type UpdateAgentPgHardSetting struct {
	PgHardPrivateKey  *string   `json:"pgHardPrivateKey"`
	PgHardOperatorId  *string   `json:"pgHardOperatorId"`
	PgHardHrefBackUrl string    `json:"-"`
	PgHardPresetId    *string   `json:"pgHardPresetId"`
	IsActive          *bool     `json:"isActive"`
	UpdatedAt         time.Time `json:"-"`
	UpdatedById       int64     `json:"-"`
}

type GetInternalAgentPgHardSetting struct {
	Id                int64  `json:"-"`
	IsActive          bool   `json:"isActive"`
	ProgramAllowUse   string `json:"programAllowUse"`
	PgHardPrivateKey  string `json:"pgHardPrivateKey"`
	PgHardOperatorId  string `json:"pgHardOperatorId"`
	PgHardUrl         string `json:"pgHardUrl"`
	PgHardHrefBackUrl string `json:"pgHardHrefBackUrl"`
	PgHardPresetId    string `json:"pgHardPresetId"`
}

type CallApiAgentPgHardDetail struct {
	PgHardPrivateKey string `json:"pgHardPrivateKey"`
	PgHardOperatorId string `json:"pgHardOperatorId"`
	PgHardUrl        string `json:"pgHardUrl"`
}

type PgHardCallbackSummaryRequest struct {
	DateFrom string `form:"dateFrom" default:"2006-01-02"`
	DateTo   string `form:"dateTo" default:"2006-01-02"`
}
type PgHardCallbackSummaryResponse struct {
	UserId        int64     `json:"userId"`
	MemberCode    string    `json:"memberCode"`
	BetAmount     float64   `json:"betAmount"`
	Payoff        float64   `json:"payoff"`
	WinloseAmount float64   `json:"winloseAmount"`
	BeforeBalance float64   `json:"beforeBalance"`
	AfterBalance  float64   `json:"afterBalance"`
	CreatedAt     time.Time `json:"createdAt"`
}

type AgentPgHardCallbackSummaryReportRequest struct {
	DateFrom string `form:"dateFrom" default:"2006-01-02"`
	DateTo   string `form:"dateTo" default:"2006-01-02"`
}

type AgentPgHardCallbackSummaryReportResponse struct {
	Id            int64   `json:"id"`
	StatementDate string  `json:"statementDate"`
	UserID        int64   `json:"userId"`
	MemberCode    string  `json:"memberCode"`
	BetAmount     float64 `json:"betAmount"`
	Payoff        float64 `json:"payoff"`
	WinloseAmount float64 `json:"winloseAmount"`
	BeforeBalance float64 `json:"beforeBalance"`
	AfterBalance  float64 `json:"afterBalance"`
}

type CreateAgentPgHardCallbackSummaryReport struct {
	StatementDate string  `json:"statementDate"`
	UserId        int64   `json:"userId"`
	MemberCode    string  `json:"memberCode"`
	BetAmount     float64 `json:"betAmount"`
	Payoff        float64 `json:"payoff"`
	WinloseAmount float64 `json:"winloseAmount"`
	BeforeBalance float64 `json:"beforeBalance"`
	AfterBalance  float64 `json:"afterBalance"`
}

type UpdateAgentPgHardCallbackSummaryReport struct {
	Id            int64   `json:"-"`
	StatementDate string  `json:"statementDate"`
	UserId        int64   `json:"userId"`
	MemberCode    string  `json:"memberCode"`
	BetAmount     float64 `json:"betAmount"`
	Payoff        float64 `json:"payoff"`
	WinloseAmount float64 `json:"winloseAmount"`
	BeforeBalance float64 `json:"beforeBalance"`
	AfterBalance  float64 `json:"afterBalance"`
}
