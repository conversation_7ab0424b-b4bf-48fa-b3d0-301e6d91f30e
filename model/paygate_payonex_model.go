package model

import (
	"time"
)

type PayonexErrorRemoteResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Code    int64  `json:"code"`
	Data    string `json:"data"`
}
type PayonexErrorStringRemoteResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Code    string `json:"code"`
	Data    string `json:"data"`
}

type PayonexToken struct {
	Id          int64      `json:"id"`
	AccessToken string     `json:"accessToken"`
	ExpireAt    time.Time  `json:"expireAt"`
	CreateBy    int64      `json:"createBy"`
	CreatedAt   time.Time  `json:"createdAt"`
	UpdatedAt   *time.Time `json:"updatedAt"`
}
type PayonexTokenCreateBody struct {
	Id          int64     `json:"id"`
	AccessToken string    `json:"accessToken"`
	ExpireAt    time.Time `json:"expireAt"`
	CreateBy    int64     `json:"createBy"`
}
type PayonexTokenCreateRemoteRequest struct {
	AccessKey string `json:"accessKey" validate:"required"`
	SecretKey string `json:"secretKey" validate:"required"`
}
type PayonexTokenCreateRemoteResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Code    string `json:"code"`
	Data    struct {
		Token string `json:"token"`
	} `json:"data"`
}

type PayonexCustomerCreateRemoteRequest struct {
	Name      string `json:"name" validate:"required"`
	BankCode  string `json:"bankCode" validate:"required"`
	AccountNo string `json:"accountNo" validate:"required"`
}
type PayonexCustomerCreateRemoteResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Code    string `json:"code"`
	Data    struct {
		Partner      string   `json:"partner"`
		CustomerUuid string   `json:"customerUuid"`
		ClientCode   string   `json:"clientCode"`
		Name         string   `json:"name"`
		SearchName   []string `json:"searchName"`
		AccountNo    string   `json:"accountNo"`
		BankCode     string   `json:"bankCode"`
		Status       string   `json:"status"`
		CreatedAt    int64    `json:"createdAt"`
		UpdatedAt    int64    `json:"updatedAt"`
	} `json:"data"`
}
type PayonexCustomerUpdateRemoteRequest struct {
	CustomerUuid string `json:"customerUuid" validate:"required"`
	Name         string `json:"name" validate:"required"`
	BankCode     string `json:"bankCode" validate:"required"`
	AccountNo    string `json:"accountNo" validate:"required"`
}
type PayonexCustomerUpdateRemoteResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Code    string `json:"code"`
	Data    struct {
		Partner      string   `json:"partner"`
		CustomerUuid string   `json:"customerUuid"`
		ClientCode   string   `json:"clientCode"`
		Name         string   `json:"name"`
		SearchName   []string `json:"searchName"`
		AccountNo    string   `json:"accountNo"`
		BankCode     string   `json:"bankCode"`
		Status       string   `json:"status"`
		CreatedAt    int64    `json:"createdAt"`
		UpdatedAt    int64    `json:"updatedAt"`
	} `json:"data"`
}

type PayonexDepositCreateRemoteRequest struct {
	CustomerUuid string  `form:"customerUuid" json:"customerUuid" validate:"required"`
	Amount       float64 `form:"amount" json:"amount" binding:"required,min=1" validate:"required,min=1"`
	ReferenceId  string  `form:"referenceId" json:"referenceId" binding:"required" validate:"required"`
	Note         string  `form:"note" json:"note"`
	Remark       string  `form:"remark" json:"remark"`
}
type PayonexDepositCreateRemoteResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Code    string `json:"code"`
	Data    struct {
		Link           string  `json:"link"`
		QrCode         string  `json:"qrCode"`
		Uuid           string  `json:"uuid"`
		ClientName     string  `json:"clientName"`
		CustomerName   string  `json:"customerName"`
		QrExpireTime   int64   `json:"qrExpireTime"`
		QrType         string  `json:"qrType"`
		Option         string  `json:"option"`
		Amount         float64 `json:"amount"`
		TransferAmount float64 `json:"transferAmount"`
	} `json:"data"`
}

type PayonexCheckBalanceRemoteResponse struct {
	Success bool `json:"success"`
	Code    int  `json:"code"`
	Data    struct {
		InBalance struct {
			CoinChain string  `json:"coinChain"`
			CoinUnit  string  `json:"coinUnit"`
			Balance   float64 `json:"balance"`
			Freeze    float64 `json:"freeze"`
		} `json:"inBalance"`
		OutBalance struct {
			CoinChain string  `json:"coinChain"`
			CoinUnit  string  `json:"coinUnit"`
			Balance   float64 `json:"balance"`
			Freeze    float64 `json:"freeze"`
		} `json:"outBalance"`
		Sign             string `json:"sign"`
		CheckRequestTime int64  `json:"checkRequestTime"`
		DfBalance        struct {
			CoinChain string  `json:"coinChain"`
			CoinUnit  string  `json:"coinUnit"`
			Balance   float64 `json:"balance"`
			Freeze    float64 `json:"freeze"`
		} `json:"dfBalance"`
		DsBalance struct {
			CoinChain string  `json:"coinChain"`
			CoinUnit  string  `json:"coinUnit"`
			Balance   float64 `json:"balance"`
			Freeze    float64 `json:"freeze"`
		} `json:"dsBalance"`
	} `json:"data"`
}

type PayonexGetOrderRemoteResponse struct {
	Success bool `json:"success"`
	Code    int  `json:"code"`
	Data    struct {
		OrderNo       string  `json:"orderNo"`
		ReceiveAddr   string  `json:"receiveAddr"`
		ChainName     string  `json:"chainName"`
		CoinUnit      string  `json:"coinUnit"`
		RequestAmount float64 `json:"requestAmount"`
		PayAmount     float64 `json:"payAmount"`
		Status        string  `json:"status"`
		Sign          string  `json:"sign"`
	} `json:"data"`
}

type PayonexWithdrawCreateRemoteRequest struct {
	CustomerUuid string  `form:"customerUuid" json:"customerUuid" validate:"required"`
	Amount       float64 `form:"amount" json:"amount" binding:"required,min=1" validate:"required,min=1"`
	ReferenceId  string  `form:"referenceId" json:"referenceId" binding:"required" validate:"required"`
	Note         string  `form:"note" json:"note"`
	Remark       string  `form:"remark" json:"remark"`
}

type PayonexWithdrawCreateRemoteResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Code    string `json:"code"`
	Data    struct {
		Uuid string `json:"uuid"`
	} `json:"data"`
}

type PayonexWebhookRequest struct {
	JsonPayload string `json:"jsonPayload"`
}
type PayonexWebhookCreateBody struct {
	Id          int64  `json:"id"`
	Name        string `json:"name"`
	JsonPayload string `json:"jsonPayload"`
}

type PayonexDepositWebhookResponse struct {
	Uuid         string  `json:"uuid"`
	CustomerUuid string  `json:"customerUuid"`
	AccountName  string  `json:"accountName"`
	BankCode     string  `json:"bankCode"`
	AccountNo    string  `json:"accountNo"`
	Amount       float64 `json:"amount"`
	Fee          float64 `json:"fee"`
	// SettleAmount float64 `json:"settleAmount"`
	Type        string `json:"type"`
	Status      string `json:"status"`
	ReferenceId string `json:"referenceId"`
}

type PayonexWithDrawWebhookResponse struct {
	Type                    string    `json:"type"`
	CurrencyCode            string    `json:"currencyCode"`
	FundOutStatus           string    `json:"fundOutStatus"`
	Amount                  float64   `json:"amount"`
	ServiceFee              float64   `json:"serviceFee"`
	IsPaid                  bool      `json:"isPaid"`
	TransactionRemark       string    `json:"transactionRemark"`
	FundOutDescription      string    `json:"fundOutDescription"`
	TransactionRef1         string    `json:"transactionRef1"`
	FundOutPaymentReference string    `json:"fundOutPaymentReference"`
	BankName                string    `json:"bankName"`
	BankCode                string    `json:"bankCode"`
	AccountNumber           string    `json:"accountNumber"`
	CreatedAt               time.Time `json:"createdAt"`
	UpdatedAt               time.Time `json:"updatedAt"`
	BankTransactionRef      string    `json:"bankTransactionRef"`
}

type PayonexCustomerDepositInfo struct {
	Name        string `json:"name"`
	DisplayName string `json:"displayName"`
	ShopName    string `json:"shopName"`
	MinAmount   int64  `json:"minAmount"`
	MaxAmount   int64  `json:"maxAmount"`
}
type PayonexDepositCreateRequest struct {
	UserId int64   `form:"userId" json:"-" validate:"required"`
	Amount float64 `form:"amount" json:"amount" binding:"required,min=1" validate:"required,min=1"`
}
type PayonexWithdrawCreateRequest struct {
	RefId  int64   `form:"refId" validate:"required"`
	UserId int64   `form:"userId" json:"-" validate:"required"`
	Amount float64 `form:"amount" binding:"required,min=1" validate:"required,min=1"`
}

const (
	PAYONEX_ORDER_TYPE_DEPOSIT  = 1
	PAYONEX_ORDER_TYPE_WITHDRAW = 2
)

type PayonexCustomer struct {
	Id           int64  `json:"id"`
	UserId       int64  `json:"userId"`
	CustomerUuid string `json:"customerUuid"`
	FullName     string `json:"fullName"`
	BankCode     string `json:"bankCode"`
	AccountNo    string `json:"accountNo"`
	AccountName  string `json:"accountName"`
}
type PayonexCustomerListRequest struct {
	Page    int    `form:"page" default:"1" min:"1"`
	Limit   int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol string `form:"sortCol"`
	SortAsc string `form:"sortAsc"`
}
type PayonexCustomerResponse struct {
	Id           int64  `json:"id"`
	UserId       int64  `json:"userId"`
	CustomerUuid string `json:"customerUuid"`
	FullName     string `json:"fullName"`
	BankCode     string `json:"bankCode"`
	AccountNo    string `json:"accountNo"`
	AccountName  string `json:"accountName"`
}
type PayonexCustomerCreateBody struct {
	Id           int64  `json:"id"`
	UserId       int64  `json:"userId"`
	CustomerUuid string `json:"customerUuid"`
	FullName     string `json:"fullName"`
	BankCode     string `json:"bankCode"`
	AccountNo    string `json:"accountNo"`
	AccountName  string `json:"accountName"`
}
type PayonexCustomerUpdateBody struct {
	CustomerUuid *string `json:"customerUuid"`
	FullName     *string `json:"fullName"`
	BankCode     *string `json:"bankCode"`
	AccountNo    *string `json:"accountNo"`
	AccountName  *string `json:"accountName"`
}

type PayonexOrder struct {
	Id                    int64      `json:"id"`
	UserId                int64      `json:"userId"`
	OrderTypeId           int64      `json:"orderTypeId"`
	RefId                 *int64     `json:"refId"`
	OrderNo               string     `json:"orderNo"`
	Amount                float64    `json:"amount"`
	TransactionNo         *string    `json:"transactionNo"`
	TransactionDate       *time.Time `json:"transactionDate"`
	TransactionStatus     *string    `json:"transactionStatus"`
	PaymentAt             *time.Time `json:"paymentAt"`
	BankTransactionId     *string    `json:"bankTransactionId"`
	BankTransactionStatus string     `json:"bankTransactionStatus"`
	QrPromptpay           *string    `json:"qrPromptpay"`
	Note                  *string    `json:"note"`
	Remark                *string    `json:"remark"`
	ApiRemark             *string    `json:"apiRemark"`
	CreatedAt             time.Time  `json:"createdAt"`
	UpdatedAt             *time.Time `json:"updatedAt"`
}
type PayonexOrderListRequest struct {
	UserId        *int64   `form:"userId"`
	OrderTypeId   *int64   `form:"orderTypeId"`
	OrderNo       string   `form:"orderNo"`
	TransactionNo string   `form:"transactionNo"`
	Amount        *float64 `form:"amount"`
	Status        string   `form:"status"`
	Page          int      `form:"page" default:"1" min:"1"`
	Limit         int      `form:"limit" default:"10" min:"1" max:"100"`
	SortCol       string   `form:"sortCol"`
	SortAsc       string   `form:"sortAsc"`
}
type PayonexOrderResponse struct {
	Id                    int64      `json:"id"`
	UserId                int64      `json:"userId"`
	OrderTypeId           int64      `json:"orderTypeId"`
	OrderTypeName         string     `json:"orderTypeName"`
	RefId                 *int64     `json:"refId"`
	OrderNo               string     `json:"orderNo"`
	Amount                float64    `json:"amount"`
	TransferAmount        float64    `json:"transferAmount"`
	TransactionNo         *string    `json:"transactionNo"`
	TransactionDate       *time.Time `json:"transactionDate"`
	TransactionStatus     *string    `json:"transactionStatus"`
	PaymentAt             *time.Time `json:"paymentAt"`
	BankTransactionId     *string    `json:"bankTransactionId"`
	BankTransactionStatus string     `json:"bankTransactionStatus"`
	QrPromptpay           string     `json:"qrPromptpay"`
	Note                  *string    `json:"note"`
	Remark                *string    `json:"remark"`
	ApiRemark             *string    `json:"apiRemark"`
	CreatedAt             time.Time  `json:"createdAt"`
	UpdatedAt             *time.Time `json:"updatedAt"`
}
type PayonexOrderWebResponse struct {
	UserId            int64     `json:"userId"`
	OrderNo           string    `json:"orderNo"`
	Amount            float64   `json:"amount"`
	TransferAmount    float64   `json:"transferAmount"`
	TransactionStatus string    `json:"transactionStatus"`
	QrCode            string    `json:"qrCode"`
	QrBase64          string    `json:"qrBase64"`
	CreatedAt         time.Time `json:"createdAt"`
}
type PayonexOrderQrResponse struct {
	Id       int64   `json:"id"`
	OrderNo  string  `json:"orderNo"`
	Amount   float64 `json:"amount"`
	QrBase64 string  `json:"qrBase64"`
}
type PayonexOrderCreateRequest struct {
	UserId *int64  `form:"userId"`
	Amount float64 `form:"amount"`
}
type PayonexOrderCreateBody struct {
	Id                int64   `json:"id"`
	UserId            int64   `json:"userId"`
	OrderTypeId       int64   `json:"orderTypeId"`
	RefId             *int64  `json:"refId"`
	OrderNo           string  `json:"orderNo"`
	Amount            float64 `json:"amount"`
	TransactionStatus string  `json:"transactionStatus"`
}
type PayonexOrderUpdateBody struct {
	TransactionNo     string    `json:"transactionNo"`
	TransactionDate   time.Time `json:"transactionDate"`
	TransactionStatus string    `json:"transactionStatus"`
	TransferAmount    float64   `json:"transferAmount"`
	QrPromptpay       string    `json:"qrPromptpay"`
}
