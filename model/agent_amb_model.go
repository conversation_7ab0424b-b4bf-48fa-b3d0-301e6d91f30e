package model

type AmbErrorReponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
}
type AmbErrorStringReponse struct {
	Code string `json:"code"`
	Msg  string `json:"msg"`
}

type AmbRegister struct {
	AgentUsername string `json:"agentUsername"`
	Key           string `json:"key"`
	Username      string `json:"username"`
	Name          string `json:"name"`
	Password      string `json:"password"`
	Web           string `json:"web"`
}
type AmbRegisterResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
}

type AmbLogin struct {
	AgentUsername string `json:"agentUsername"`
	Key           string `json:"key"`
	Username      string `json:"username"`
	Web           string `json:"web"`
}
type AmbLoginResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Url  string `json:"url"`
}

type AmbChangePassword struct {
	AgentUsername string `json:"agentUsername" validate:"required"`
	Key           string `json:"key" validate:"required"`
	Username      string `json:"username" validate:"required"`
	Password      string `json:"password" validate:"required"`
	Web           string `json:"web" validate:"required"`
}
type AmbChangePasswordResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
}

type AmbDeposit struct {
	AgentUsername string  `json:"agentUsername"`
	Key           string  `json:"key"`
	Username      string  `json:"username"`
	Balance       float64 `json:"balance"`
	IsDp          bool    `json:"isDp"`
	Web           string  `json:"web"`
	RefId         *int64  `json:"refId" comment:"รหัสอ้างอิง"`
}
type AmbDepositReponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		BeforeCredit float64 `json:"beforeCredit"`
		AfterCredit  float64 `json:"afterCredit"`
		RefId        string  `json:"refId"`
		Agent        struct {
			BeforeCredit float64 `json:"beforeCredit"`
			AfterCredit  float64 `json:"afterCredit"`
		} `json:"agent"`
	} `json:"data"`
}

type AmbWithdraw struct {
	AgentUsername string  `json:"agentUsername"`
	Key           string  `json:"key"`
	Username      string  `json:"username"`
	Balance       float64 `json:"balance"`
	Web           string  `json:"web"`
	RefId         *int64  `json:"refId" comment:"รหัสอ้างอิง"`
}
type AmbWithdrawReponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		BeforeCredit float64 `json:"beforeCredit"`
		AfterCredit  float64 `json:"afterCredit"`
		RefId        string  `json:"refId"`
		Agent        struct {
			BeforeCredit float64 `json:"beforeCredit"`
			AfterCredit  float64 `json:"afterCredit"`
		} `json:"agent"`
	} `json:"data"`
}

type AmbStartGameRequest struct {
	AgentUsername string `json:"agentUsername" validate:"required"`
	Key           string `json:"key" validate:"required"`
	Username      string `json:"username" validate:"required"`
	GameID        string `json:"gameID" validate:"required"`
	Provider      string `json:"provider" validate:"required"`
	RedirectUrl   string `json:"redirectUrl" validate:"required"`
	Language      string `json:"language" validate:"required"`
	Tab           string `json:"tab" validate:"required"`
	Device        string `json:"device" validate:"required"`
	Ip            string `json:"ip" validate:"required"`
	Web           string `json:"web" validate:"required"`
}
type AmbStartGameResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		Uri string `json:"uri"`
	} `json:"data"`
}

type AmbPlayResponse struct {
	GameUrl   string  `json:"gameUrl"`
	MaxPerBet float64 `json:"maxPerBet"`
	GameToken string  `json:"gameToken"`
}

type AmbTransactionResponse struct {
	Error struct {
		Code    int    `json:"Code"`
		Message string `json:"Message"`
	}
	Message string `json:"Message"`
	Result  struct {
		BalanceAfter  float64 `json:"BalanceAfter"`
		BalanceBefore float64 `json:"BalanceBefore"`
	}
	Success   bool   `json:"Success"`
	TargetUrl string `json:"TargetUrl"`
}

type AmbBalance struct {
	AgentUsername string `json:"agentUsername"`
	Key           string `json:"key"`
	Username      string `json:"username"`
	Web           string `json:"web"`
}
type AmbBalanceResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		Username string  `json:"username"`
		Balance  float64 `json:"balance"`
		Active   int     `json:"active"`
		Currency string  `json:"currency"`
	} `json:"data"`
}

type AmbSimpleWinlose struct {
	AgentUsername string   `json:"agentUsername"`
	StartDate     string   `json:"startDate"`
	EndDate       string   `json:"endDate"`
	Key           string   `json:"key"`
	BetType       []string `json:"betType"`
	Web           string   `json:"web"`
}
type AmbSimpleWinloseData struct {
	MemberCode string `json:"_id"`
	BetAmt     struct {
		NumberDecimal string `json:"$numberDecimal"`
	} `json:"betAmt"`
	ValidAmt struct {
		NumberDecimal string `json:"$numberDecimal"`
	} `json:"validAmt"`
	WinLose struct {
		NumberDecimal string `json:"$numberDecimal"`
	} `json:"winLose"`
	Stake int `json:"stake"`
}
type AmbSimpleWinloseResponse struct {
	Code       int                    `json:"code"`
	LastUpdate string                 `json:"lastUpdate"`
	Msg        string                 `json:"msg"`
	Data       []AmbSimpleWinloseData `json:"data"`
}

// type AmbPlayLog struct {
// 	ID                int64
// 	Player            string
// 	TurnSport         float64
// 	TurnCasino        float64
// 	TurnGame          float64
// 	WinLoseSport      float64
// 	WinLoseCasino     float64
// 	WinLoseGame       float64
// 	ValidAmountSport  float64
// 	ValidAmountCasino float64
// 	ValidAmountGame   float64
// 	TurnTotal         float64
// 	WinLoseTotal      float64
// 	ValidAmountTotal  float64
// 	Date              string
// 	UserID            int64
// }

type AmbGameProviderListRequest struct {
	AgentUsername string `json:"agentUsername"`
	Key           string `json:"key"`
	Web           string `json:"web"`
}
type AmbGameProviderItemReponse struct {
	Provider           string `json:"provider"`
	ProviderTier       string `json:"providerTier"`
	ProviderName       string `json:"providerName"`
	ProviderType       string `json:"providerType"`
	LogoURL            string `json:"logoURL"`
	LogoTransparentURL string `json:"logoTransparentURL"`
	Status             string `json:"status"`
	DetailStatus       bool   `json:"detailStatus"`
}
type AmbGameProviderReponse struct {
	Code int                                     `json:"code"`
	Msg  string                                  `json:"msg"`
	Data map[string][]AmbGameProviderItemReponse `json:"data"`
}

type AmbGameListRequest struct {
	AgentUsername string `json:"agentUsername"`
	Key           string `json:"key"`
	Tab           string `json:"tab"`
	Prefix        string `json:"prefix"`
	Web           string `json:"web"`
}

type AmbGameItemReponse struct {
	Id       string   `json:"id"`
	Provider string   `json:"provider"`
	GameName string   `json:"gameName"`
	GameType []string `json:"gameType"`
	Image    struct {
		Vertical   string `json:"vertical"`
		Horizontal string `json:"horizontal"`
		Banner     string `json:"banner"`
	} `json:"image"`
	Status string  `json:"status"`
	Rtp    float64 `json:"rtp"`
}
type AmbGameReponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data []struct {
		Provider           string   `json:"provider"`
		ProviderName       string   `json:"providerName"`
		GameType           []string `json:"gameType"`
		LogoURL            string   `json:"logoURL"`
		LogoTransparentURL string   `json:"logoTransparentURL"`
		Image              struct {
			Vertical   string `json:"vertical"`
			Horizontal string `json:"horizontal"`
			Banner     string `json:"banner"`
		} `json:"image"`
		Games []AmbGameItemReponse `json:"games"`
	} `json:"data"`
}
