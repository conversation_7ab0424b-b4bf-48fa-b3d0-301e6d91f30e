package model

type LotteryPlaylogRequest struct {
	Date string `json:"Date"`
	Size int    `json:"size"`
	Page int    `json:"page"`
}

//	{
//		"content": [
//		  {
//			"userId": 89180,
//			"turnOver": 10,
//			"validAmount": 10,
//			"winLoss": 8990
//		  },
//		  {
//			"userId": 89181,
//			"turnOver": 1,
//			"validAmount": 1,
//			"winLoss": -1
//		  }
//		],
//		"pageable": {
//		  "sort": {
//			"empty": true,
//			"unsorted": true,
//			"sorted": false
//		  },
//		  "offset": 0,
//		  "pageSize": 20,
//		  "pageNumber": 0,
//		  "paged": true,
//		  "unpaged": false
//		},
//		"totalElements": 100,
//		"totalPages": 5,
//		"last": false,
//		"size": 20,
//		"number": 0,
//		"sort": {
//		  "empty": true,
//		  "unsorted": true,
//		  "sorted": false
//		},
//		"numberOfElements": 2,
//		"first": true,
//		"empty": false
//	  }

type LotteryPlaylogContent struct {
	UserId      int64   `json:"userId"`
	TurnOver    float64 `json:"turnOver"`
	ValidAmount float64 `json:"validAmount"`
	WinLoss     float64 `json:"winLoss"`
}
type LotteryPlaylogResponse struct {
	Content          []LotteryPlaylogContent `json:"content"`
	NumberOfElements int64                   `json:"numberOfElements"`
	Empty            bool                    `json:"empty"`
}

type LotteryWebhookRequest struct {
	JsonPayload string `json:"jsonPayload"`
}

type LotteryWebhookPlaylogRecord struct {
	UserId      int64   `json:"userId"`
	TurnOver    float64 `json:"turnOver"`
	ValidAmount float64 `json:"validAmount"`
	WinLoss     float64 `json:"winLoss"`
}
