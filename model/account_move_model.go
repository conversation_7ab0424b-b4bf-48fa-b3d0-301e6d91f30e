package model

import (
	"time"
)

const (
	ACCOUNT_MOVE_TRANS_PENDING = 1
	ACCOUNT_MOVE_TRANS_SUCCESS = 2
	ACCOUNT_MOVE_TRANS_FAIL    = 3
)

type SelectOptions struct {
	Id    int64  `json:"id"`
	Label string `json:"label"`
	Value string `json:"value"`
}
type AccountMoveAccountListRequest struct {
	Page  int `form:"page" default:"1" min:"1"`
	Limit int `form:"limit" default:"10" min:"1" max:"100"`
}
type AccountMoveTransaction struct {
	Id            int64      `json:"id"`
	AdminId       int64      `json:"adminId"`
	FromAccountId int64      `json:"fromAccountId"`
	ToAccountId   int64      `json:"toAccountId"`
	Amount        float64    `json:"amount"`
	StatusId      int64      `json:"statusId"`
	TransferAt    *time.Time `json:"transferAt"`
	Remark        string     `json:"remark"`
	IsSuccess     bool       `json:"isSuccess"`
	CreatedAt     time.Time  `json:"createdAt"`
	UpdatedAt     *time.Time `json:"updatedAt"`
}
type AccountMoveTransactionListRequest struct {
	StatusId      *int64 `form:"statusId"`
	FromAccountId *int64 `form:"fromAccountId"`
	ToAccountId   *int64 `form:"toAccountId"`
	TransferDate  string `form:"transferDate" example:"2021-12-31"`
	IsSuccess     *bool  `form:"isSuccess"`
	Page          int    `form:"page" default:"1" min:"1"`
	Limit         int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol       string `form:"sortCol"`
	SortAsc       string `form:"sortAsc"`
}
type AccountMoveTransactionStatusCount struct {
	TotalCount   int64 `json:"totalCount"`
	SuccessCount int64 `json:"successCount"`
	FailCount    int64 `json:"failCount"`
}
type AccountMoveTransactionResponse struct {
	Id                int64      `json:"id"`
	AdminId           int64      `json:"adminId"`
	AdminName         string     `json:"adminName"`
	FromAccountId     int64      `json:"fromAccountId"`
	FromAccountName   string     `json:"fromAccountName"`
	FromAccountNumber string     `json:"fromAccountNumber"`
	FromBankId        int64      `json:"fromBankId"`
	FromBankName      string     `json:"fromBankName"`
	FromBankCode      string     `json:"fromBankCode"`
	ToAccountId       int64      `json:"toAccountId"`
	ToAccountName     string     `json:"toAccountName"`
	ToAccountNumber   string     `json:"toAccountNumber"`
	ToBankId          int64      `json:"toBankId"`
	ToBankName        string     `json:"toBankName"`
	ToBankCode        string     `json:"toBankCode"`
	Amount            float64    `json:"amount"`
	StatusId          int64      `json:"statusId"`
	StatusName        string     `json:"statusName"`
	TransferAt        *time.Time `json:"transferAt"`
	Remark            string     `json:"remark"`
	CreatedAt         time.Time  `json:"createdAt"`
	UpdatedAt         *time.Time `json:"updatedAt"`
}
type AccountMoveTransactionCreateRequest struct {
	AdminId       int64   `form:"adminId" json:"-"`
	FromAccountId int64   `form:"fromAccountId" binding:"required"`
	ToAccountId   int64   `form:"toAccountId" binding:"required"`
	Amount        float64 `form:"amount" binding:"required"`
	Remark        string  `form:"remark"`
}
type AccountMoveTransactionCreateBody struct {
	Id               int64      `json:"id"`
	AdminId          int64      `json:"adminId"`
	FromAccountId    int64      `json:"fromAccountId"`
	ToAccountId      int64      `json:"toAccountId"`
	Amount           float64    `json:"amount"`
	StatusId         int64      `json:"statusId"`
	TransferAt       *time.Time `json:"transferAt"`
	Remark           string     `json:"remark"`
	JsonFastbankResp string     `json:"jsonFastbankResp"`
}
type AccountMoveTransactionUpdateBody struct {
	StatusId         int64      `json:"statusId"`
	TransferAt       *time.Time `json:"transferAt"`
	JsonFastbankResp string     `json:"jsonFastbankResp"`
	IsSuccess        bool       `json:"isSuccess"`
}

type ExternalAccountMoveCreateRequest struct {
	AccountFrom string  `json:"accountFrom"`
	AccountTo   string  `json:"accountTo"`
	Amount      float64 `json:"amount"`
	BankCode    string  `json:"bankCode"`
	Pin         string  `json:"pin"`
}
type ExternalAccountMoveCreateErrResponse struct {
	TimeStamp    string `json:"timeStamp"`
	ErrorMessage string `json:"errorMessage"`
}
type ExternalAccountMoveCreateResponse struct {
	Data struct {
		Note                 string  `json:"note"`
		QrData               string  `json:"qrData"`
		ToAccountName        string  `json:"toAccountName"`
		Amount               float64 `json:"amount"`
		IsExistingFavorite   bool    `json:"isExistingFavorite"`
		FromAccountName      string  `json:"fromAccountName"`
		TransactionDateTime  string  `json:"transactionDateTime"`
		Fee                  float64 `json:"fee"`
		ToAccountNo          string  `json:"toAccountNo"`
		FromAccountNo        string  `json:"fromAccountNo"`
		TransactionRefNo     string  `json:"transactionRefNo"`
		TransactionRefNoHash string  `json:"transactionRefNoHash"`
	} `json:"data"`
	Status struct {
		Code        int         `json:"code"`
		Header      interface{} `json:"header"`
		Description string      `json:"description"`
	} `json:"status"`
}

type AccountMoveStatementListRequest struct {
	AccountId int64 `form:"accountId" binding:"required"`
	Page      int   `form:"page" default:"1" min:"1"`
	Limit     int   `form:"limit" default:"10" min:"1" max:"100"`
}
type AccountMoveLogListRequest struct {
	AccountId int64 `form:"accountId" binding:"required"`
	Page      int   `form:"page" default:"1" min:"1"`
	Limit     int   `form:"limit" default:"10" min:"1" max:"100"`
}
type AccountMoveWebhookCreateRequest struct {
	AdminId     int64  `form:"adminId" json:"-"`
	AccountId   int64  `form:"accountId" binding:"required"`
	StatementId int64  `form:"statementId" binding:"required"`
	OfDate      string `form:"ofDate" binding:"required" default:"2006-01-02"`
	OfTime      string `form:"ofTime" binding:"required" default:"00:00"`
}

type ExternalListSort struct {
	Empty    bool `json:"empty"`
	Sorted   bool `json:"sorted"`
	Unsorted bool `json:"unsorted"`
}
type ExternalAccountStatementListRequest struct {
	AccountNumber string `json:"accountNumber"`
	Page          int    `json:"page"`
	Size          int    `json:"size"`
}
type ExternalAccountStatementListResponse struct {
	Content  []ExternalAccountStatement `json:"content"`
	Pageable struct {
		Sort       ExternalListSort `json:"sort"`
		Offset     int              `json:"offset"`
		PageNumber int              `json:"pageNumber"`
		PageSize   int              `json:"pageSize"`
		Unpaged    bool             `json:"unpaged"`
		Paged      bool             `json:"paged"`
	} `json:"pageable"`
	TotalPages       int              `json:"totalPages"`
	TotalElements    int64            `json:"totalElements"`
	Last             bool             `json:"last"`
	Number           int              `json:"number"`
	Sort             ExternalListSort `json:"sort"`
	Size             int              `json:"size"`
	NumberOfElements int              `json:"numberOfElements"`
	First            bool             `json:"first"`
	Empty            bool             `json:"empty"`
}
type ExternalAccountStatement struct {
	Id                    int64   `json:"id"`
	BankAccountId         int64   `json:"bankAccountId"`
	BankCode              string  `json:"bankCode"`
	Amount                float64 `json:"amount"`
	DateTime              string  `json:"dateTime"`
	RawDateTime           string  `json:"rawDateTime"`
	Info                  string  `json:"info"`
	ChannelCode           string  `json:"channelCode"`
	ChannelDescription    string  `json:"channelDescription"`
	TxnCode               string  `json:"txnCode"`
	TxnDescription        string  `json:"txnDescription"`
	Checksum              string  `json:"checksum"`
	IsRead                bool    `json:"isRead"`
	IsHasWebhookStatement bool    `json:"isHasWebhookStatement"`
	CreatedDate           string  `json:"createdDate"`
	UpdatedDate           string  `json:"updatedDate"`
}

type ExternalAccountLogListRequest struct {
	AccountNumber string `json:"accountNumber"`
	Page          int    `json:"page"`
	Size          int    `json:"size"`
}
type ExternalAccountLogListResponse struct {
	Content  []ExternalAccountLog `json:"content"`
	Pageable struct {
		Sort       ExternalListSort `json:"sort"`
		Offset     int              `json:"offset"`
		PageNumber int              `json:"pageNumber"`
		PageSize   int              `json:"pageSize"`
		Unpaged    bool             `json:"unpaged"`
		Paged      bool             `json:"paged"`
	} `json:"pageable"`
	TotalPages       int              `json:"totalPages"`
	TotalElements    int64            `json:"totalElements"`
	Last             bool             `json:"last"`
	Number           int              `json:"number"`
	Sort             ExternalListSort `json:"sort"`
	Size             int              `json:"size"`
	NumberOfElements int              `json:"numberOfElements"`
	First            bool             `json:"first"`
	Empty            bool             `json:"empty"`
}

type BankStatementExistsListRequest struct {
	AccountId   int64   `form:"accountId" extensions:"x-order:1"`
	ExternalIds []int64 `form:"externalIds" extensions:"x-order:2"`
	Page        int     `form:"page" extensions:"x-order:7" default:"1" min:"1"`
	Limit       int     `form:"limit" extensions:"x-order:8" default:"10" min:"1" max:"100"`
	SortCol     string  `form:"sortCol" extensions:"x-order:9"`
	SortAsc     string  `form:"sortAsc" extensions:"x-order:10"`
}
