package model

import (
	"time"
)

const (
	ORDER_CATEGORY_LOTTERY = int64(1)
)

const (
	ORDER_TYPE_LOTTERY_BET        = int64(1) // decrease balance
	ORDER_TYPE_LOTTERY_WIN        = int64(2) // increase balance
	ORDER_TYPE_LOTTERY_REFUND     = int64(3) // increase balance
	ORDER_TYPE_LOTTERY_COMMISSION = int64(4) // increase balance

	ORDER_TYPE_LOTTERY_BET_TH        = "แทงหวย"
	ORDER_TYPE_LOTTERY_WIN_TH        = "ถูกหวย"
	ORDER_TYPE_LOTTERY_REFUND_TH     = "คืนหวย"
	ORDER_TYPE_LOTTERY_COMMISSION_TH = "ค่าคอมหวย"
)

const (
	ORDER_STATUS_PENDING = int64(1)
	ORDER_STATUS_SUCCESS = int64(2)
	ORDER_STATUS_FAILED  = int64(3)
)

type OrderRaceCondition struct {
	Id        int64      `json:"id"`
	ActionKey string     `json:"actionKey"`
	CreatedAt time.Time  `json:"createdAt"`
	UpdatedAt time.Time  `json:"updatedAt"`
	DeletedAt *time.Time `json:"deletedAt"`
}

type CreateOrderRaceConditionBody struct {
	Id        int64  `json:"-"`
	ActionKey string `json:"actionKey"`
}

type GetOrderRaceConditionResponse struct {
	Id        int64  `json:"id"`
	ActionKey string `json:"actionKey"`
}

type CreateOrderLogRequest struct {
	OrderId int64  `json:"orderId"`
	JsonReq string `json:"jsonReq"`
	Remark  string `json:"remark"`
}
type OrderLottery struct {
	Id              int64      `json:"id"`
	OrderCategoryId int64      `json:"orderCategoryId"`
	OrderTypeId     int64      `json:"orderTypeId"`
	Ref1No          string     `json:"ref1No"` // external
	Ref2No          string     `json:"ref2No"` // internal
	UserId          int64      `json:"userId"`
	Amount          float64    `json:"amount"`
	OrderStatusId   int64      `json:"orderStatusId"`
	CreatedAt       time.Time  `json:"createdAt"`
	UpdatedAt       *time.Time `json:"updatedAt"`
	CancelledAt     *time.Time `json:"cancelledAt"`
	DeletedAt       *time.Time `json:"deletedAt"`
}

type CreateBetOrderLotteryRequest struct {
	Ref1No string  `json:"ref1No" validate:"required"`
	UserId int64   `json:"-"`
	Amount float64 `json:"amount" validate:"required"`
}

type CreateOrderLotteryBody struct {
	Id              int64   `json:"-"`
	OrderCategoryId int64   `json:"orderCategoryId"`
	OrderTypeId     int64   `json:"orderTypeId"`
	Ref1No          string  `json:"ref1No"`
	Ref2No          string  `json:"ref2No"`
	UserId          int64   `json:"userId"`
	Amount          float64 `json:"amount"`
	OrderStatusId   int64   `json:"orderStatusId"`
}

type CreateOrderLotteryResponse struct {
	Id      int64  `json:"id"`
	Ref2No  string `json:"ref2No"`
	Message string `json:"message"`
}

type UpdateOrderLotteryStatusRequest struct {
	Id            int64   `json:"id"`
	OrderStatusId *int64  `json:"orderStatusId"`
	Ref1No        *string `json:"ref2No"`
}

type CreateWinOrderLotteryRequest struct {
	Ref1No string  `json:"ref1No" validate:"required"`
	UserId int64   `json:"userId" validate:"required"`
	Amount float64 `json:"amount" validate:"required"`
}

//	type CreateWinOrderLotteryResponse struct {
//		Id      int64  `json:"id"`
//		Ref2No  string `json:"ref2No"`
//		Message string `json:"message"`
//	}
type CreateRefundOrderLotteryRequest struct {
	Ref1No string  `json:"ref1No" validate:"required"`
	UserId int64   `json:"userId" validate:"required"`
	Amount float64 `json:"amount" validate:"required"`
}

// type CreateRefundOrderLotteryResponse struct {
// 	Id      int64  `json:"id"`
// 	Ref2No  string `json:"ref2No"`
// 	Message string `json:"message"`
// }

type CreateCommissionOrderLotteryRequest struct {
	Ref1No string  `json:"ref1No" validate:"required"`
	UserId int64   `json:"userId" validate:"required"`
	Amount float64 `json:"amount" validate:"required"`
}

// type CreateCommissionOrderLotteryResponse struct {
// 	Id      int64  `json:"id"`
// 	Ref2No  string `json:"ref2No"`
// 	Message string `json:"message"`
// }

type SetSentOrderLotteryEncodeRequest struct {
	Category    string    `json:"category" validate:"required"`
	Type        string    `json:"type" validate:"required"`
	Ref1No      string    `json:"ref1No" validate:"required"`
	UserId      int64     `json:"userId" validate:"required"`
	Amount      float64   `json:"amount" validate:"required"`
	DateTimeNow time.Time `json:"dateTimeNow" validate:"required"`
}

type CreateOrderRequest struct {
	Apikey string `json:"apikey"`
}

type OrderDecodeBody struct {
	Category    string    `json:"category" validate:"required"`
	Type        string    `json:"type" validate:"required"`
	Ref1No      string    `json:"ref1No" validate:"required"`
	UserId      int64     `json:"userId" validate:"required"`
	Amount      float64   `json:"amount" validate:"required"`
	DateTimeNow time.Time `json:"dateTimeNow" validate:"required"`
}
