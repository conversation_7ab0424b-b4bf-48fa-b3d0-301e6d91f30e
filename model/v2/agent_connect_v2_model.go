package v2

type AgcSimpleWinLoseV2 struct {
	StartDate  string `json:"StartDate"`
	EndDate    string `json:"EndDate"`
	PageSize   int    `json:"PageSize"`
	PageIndex  int    `json:"PageIndex"`
	MemberName string `json:"MemberName"`
	AgentName  string `json:"AgentName"`
	Products   []int  `json:"Products"`
	TimeStamp  int    `json:"TimeStamp"`
	Sign       string `json:"Sign"`
}

type SimpleWinLoseRecordV2 struct {
	// Comm        float64
	// Company     float64
	// FullName    string
	// GrossComm   float64
	// Loyalty     float64
	// MemberId    int64
	Payout float64
	// PlayerId    int
	// Role        int
	// Total       float64
	TurnOver    float64
	UserName    string
	ValidAmount float64
	// Win         float64
}

type AgcSimpleWinLoseV2Response struct {
	Error *struct {
		Code    int
		Message string
	}
	Message string
	Result  struct {
		ProcessStatus []struct {
			Date   string
			Status bool
		}
		Records []SimpleWinLoseRecordV2
		Summary struct {
			Comm  float64
			Comms struct {
				Player float64
			}
			Company      float64
			Details      interface{}
			GrossComm    float64
			Loyalty      float64
			PayOut       float64
			TurnOver     float64
			ValidAmount  float64
			ValidAmounts interface{}
			Win          float64
			Wins         struct {
				Player float64
			}
		}
		Total float64
	}
	Success   bool
	TargetUrl interface{}
}

type AgentCtwCallbackSummaryV2Request struct {
	PageIndex     int    `form:"pageIndex"`
	PageSize      int    `form:"pageSize"`
	StatementDate string `form:"statementDate"`
}
type AgentCtwCallbackSummaryV2 struct {
	UserID       int64   `json:"userId"`
	MemberCode   string  `json:"memberCode"`
	TotalPayoff  float64 `json:"totalPayoff"`
	TotalBet     float64 `json:"totalBet"`
	TotalWinlose float64 `json:"totalWinlose"`
}

type AgentPgHardCallbackSummaryV2Request struct {
	PageIndex     int    `form:"pageIndex"`
	PageSize      int    `form:"pageSize"`
	StatementDate string `form:"statementDate"`
}

type AgentPgHardCallbackSummaryV2 struct {
	UserID       int64   `json:"userId"`
	MemberCode   string  `json:"memberCode"`
	TotalPayoff  float64 `json:"totalPayoff"`
	TotalBet     float64 `json:"totalBet"`
	TotalWinlose float64 `json:"totalWinlose"`
}
