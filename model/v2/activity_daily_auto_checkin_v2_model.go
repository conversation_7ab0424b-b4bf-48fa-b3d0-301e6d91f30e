package v2

import (
	"gorm.io/gorm"
	"time"
)

type GetUserTodayDepositForDailyAutoCheckinV2 struct {
	Id           int64     `json:"id"`
	UserId       int64     `json:"userId"`
	CreditAmount float64   `json:"creditAmount"`
	TransferAt   time.Time `json:"transferAt"`
}

type DailyAutoCheckinV2NextNo struct {
	Id              int64   `json:"id"`
	DailyId         int64   `json:"dailyId"`
	NoNumber        int64   `json:"noNumber"`
	CreditAmount    float64 `json:"creditAmount"`
	ChangeCountTime int64   `json:"changeCountTime"`
}

type GetDailyAutoCheckinV2Total struct {
	TotalAbleReviceNo                 int64   `json:"totalAbleReviceNo"`
	ConditionAmount                   float64 `json:"conditionAmount"`
	ActivityDailyV2TotalConditionId   int64   `json:"activityDailyV2TotalConditionId"`
	ActivityDailyV2TotalConditionName string  `json:"activityDailyV2TotalConditionName"`
	ChangeCountTime                   int64   `json:"changeCountTime"`
}

type CreateUserDailyAutoCheckinV2Request struct {
	Id                       int64   `json:"id"`
	ActivityDailyV2Id        int64   `json:"activityDailyV2Id"`
	CurrentNoNumber          int64   `json:"currentNoNumber"`
	UserId                   int64   `json:"userId"`
	AccumulatedCreditBalance float64 `json:"creditAmount"`
	CheckinAt                string  `json:"checkinAt"`
	ChangeCountTime          int64   `json:"changeCountTime"`
}

type UpdateUserDailyAutoCheckinV2Request struct {
	Id                        int64     `json:"id"`
	ActivityDailyV2Id         int64     `json:"activityDailyV2Id"`
	CurrentNoNumber           int64     `json:"currentNoNumber"`
	UserId                    int64     `json:"userId"`
	AccumulatedCreditBalance  float64   `json:"AccumulatedCreditBalance"`
	AccumulatedCreditReceived float64   `json:"AccumulatedCreditReceived"`
	IsCompleted               bool      `json:"isCompleted"`
	CheckinAt                 string    `json:"checkinAt"`
	ClaimAt                   time.Time `json:"claimAt"`
	ChangeCountTime           int64     `json:"changeCountTime"`
}

type UserDailyAutoCheckinReadyClaimResponse struct {
	Id                        int64     `json:"id"`
	ActivityDailyV2Id         int64     `json:"activityDailyV2Id"`
	CurrentNoNumber           int64     `json:"currentNoNumber"`
	AccumulatedCreditBalance  float64   `json:"AccumulatedCreditBalance"`
	AccumulatedCreditReceived float64   `json:"AccumulatedCreditReceived"`
	ChangeCountTime           int64     `json:"changeCountTime"`
	IsCompleted               bool      `json:"isCompleted"`
	CheckinAt                 string    `json:"checkinAt"`
	CreatedAt                 time.Time `json:"createdAt"`
	UpdatedAt                 time.Time `json:"updatedAt"`
}

type UserResponseV2 struct {
	Id              int64          `json:"id"`
	MemberCode      *string        `json:"memberCode"`
	Username        *string        `json:"username"`
	Phone           string         `json:"phone"`
	Password        string         `json:"password"`
	UserStatusId    int64          `json:"userStatusId"`
	Status          string         `json:"status"`
	Fullname        string         `json:"fullname"`
	Firstname       string         `json:"firstname"`
	Lastname        string         `json:"lastname"`
	BankCode        string         `json:"bankCode" gorm:"-"`
	BankAccount     string         `json:"bankAccount"`
	BankId          int64          `json:"bankId"`
	LineId          string         `json:"lineId"`
	Encrypt         string         `json:"-"`
	ChannelId       int64          `json:"channelId"`
	UserTypeId      int64          `json:"userTypeId"`
	TrueWallet      string         `json:"trueWallet"`
	Contact         string         `json:"contact"`
	Note            string         `json:"note"`
	Course          string         `json:"course"`
	Credit          float64        `json:"credit"`
	CreatedBy       int64          `json:"createdBy"`
	Ip              string         `json:"ip"`
	IsResetPassword bool           `json:"is_reset_password"`
	IpRegistered    string         `json:"ipRegistered"`
	CreatedAt       time.Time      `json:"createdAt"`
	UpdatedAt       time.Time      `json:"updatedAt"`
	DeletedAt       gorm.DeletedAt `json:"deletedAt"`
	LogedinAt       *time.Time     `json:"logedinAt" gorm:"default:CURRENT_TIMESTAMP"`
	LoginType       string         `json:"loginType"`
}

type RaceActionV2 struct {
	Id          int64     `json:"id"`
	ActionKey   string    `json:"actionKey"`
	Status      string    `json:"status"`
	Name        string    `json:"name"`
	JsonRequest string    `json:"jsonRequest"`
	UnlockAt    time.Time `json:"unlockAt"`
	CreatedAt   time.Time `json:"createdAt"`
}

type RaceActionCreateBodyV2 struct {
	Id          int64     `json:"id"`
	Status      string    `json:"status"`
	ActionKey   string    `json:"actionKey"`
	Name        string    `json:"name"`
	JsonRequest string    `json:"jsonRequest"`
	UnlockAt    time.Time `json:"unlockAt"`
}

type GetReportDailyAutoCheckinV2Request struct {
	DateType   string `form:"dateType" default:"daily"`
	FromDate   string `form:"fromDate"`
	ToDate     string `form:"toDate"`
	MemberCode string `form:"memberCode"`
	Page       int    `form:"page" default:"1" min:"1"`
	Limit      int    `form:"limit" default:"10" min:"1" max:"100"`
}

type GetReportDailyAutoCheckinV2Response struct {
	Id              int64      `json:"id"`
	UserId          int64      `json:"userId"`
	MemberCode      string     `json:"memberCode"`      // รหัสสมาชิก
	Fullname        string     `json:"fullname"`        // ชื่อ-นามสกุล
	Username        string     `json:"username"`        // เบอร์โทรศัพท์
	Description     string     `json:"description"`     // รายละเอียด
	AmountCondition float64    `json:"amountCondition"` // จำนวนเงื่อนไข
	TidturnPercent  int64      `json:"tidturnPercent"`  // เปอร์เซ็นต์การติดเทิร์น
	TypeId          int64      `json:"typeId"`
	TypeName        string     `json:"typeName"`
	RefTypeId       int64      `json:"refTypeId"`
	BonusAmount     float64    `json:"bonusAmount"` // เครดิตรางวัล
	StatusId        int64      `json:"statusId"`
	StatusName      string     `json:"statusName"`      // สถานะเทิร์น
	StartTurnAmount float64    `json:"startTurnAmount"` // จำนวนเทิร์น
	StartTurnAt     *time.Time `json:"startTurnAt"`
	TotalTurnAmount float64    `json:"totalTurnAmount"`
	EndTurnAt       *time.Time `json:"endTurnAt"`
	CreatedAt       time.Time  `json:"createdAt"` // วันเวลา
	UpdatedAt       *time.Time `json:"updatedAt"`
}

type DateTypeResponse struct {
	DateType string `json:"dateType"`
	DateFrom string `json:"dateFrom"`
	DateTo   string `json:"dateTo"`
}
