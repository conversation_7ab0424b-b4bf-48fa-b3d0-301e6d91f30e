package v2

import "time"

type RankingV2SettingResponse struct {
	Id        int64     `json:"id"`
	Name      string    `json:"name"`
	ImageUrl  string    `json:"imageUrl"`
	MinScore  float32   `json:"minScore"`
	MaxScore  float32   `json:"maxScore"`
	Sort      int64     `json:"sort"`
	IsActive  bool      `json:"isActive"`
	IsDeleted bool      `json:"isDeleted"`
	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
}

type CreateRankingV2SettingRequest struct {
	Name     string  `json:"name" binding:"required"`
	ImageUrl string  `json:"imageUrl"`
	MinScore float32 `json:"minScore" binding:"required"`
	MaxScore float32 `json:"maxScore" binding:"required"`
	Sort     int64   `json:"sort" default:"0"`
}

type UpdateRankingV2SettingRequest struct {
	Id        int64   `json:"id" binding:"required"`
	Name      string  `json:"name" binding:"required"`
	ImageUrl  string  `json:"imageUrl"`
	MinScore  float32 `json:"minScore" binding:"required"`
	MaxScore  float32 `json:"maxScore" binding:"required"`
	Sort      int64   `json:"sort" binding:"required"`
	IsActive  bool    `json:"isActive" default:"true"`
	IsDeleted bool    `json:"isDeleted" default:"false"`
}

type TotalRankingV2Response struct {
	Name           string  `json:"name"`
	ImageUrl       string  `json:"imageUrl"`
	UserAmount     int64   `json:"userAmount"`
	UserPercent    float64 `json:"userPercent"`
	TotalTurn      float64 `json:"totalTurn"`
	WinLosePercent float64 `json:"winLosePercent"`
}

type UserRankingV2Response struct {
	RankingNumber  int64   `json:"rankingNumber"`
	RankingName    string  `json:"rankingName"`
	TotalPlay      float64 `json:"totalPlay"`
	TotalTurn      float64 `json:"totalTurn"`
	WinLosePercent float64 `json:"winLosePercent"`
}

type RankingReportV2Response struct {
	RankingNumber  int64   `json:"rankingNumber"`
	MemberCode     string  `json:"memberCode"`
	TotalTurn      int     `json:"totalTurn"`
	TotalWinLose   int     `json:"totalWinLose"`
	WinLosePercent float64 `json:"winLosePercent"`
}

type RankingListRequest struct {
	Page  int `form:"page" default:"1" min:"1"`
	Limit int `form:"limit" default:"10" min:"1" max:"100"`
}

type TurnRankingV2Response struct {
	//Category       string  `json:"category"`
	//Percent        float64 `json:"Percent"`
	//TotalTurn      float64 `json:"totalTurn"`
	//WinLosePercent float64 `json:"winLosePercent"`
	TurnTotalSummary      float64 `json:"turn_total_summary"`
	TurnSportSummary      float64 `json:"turn_sport_summary"`
	TurnCasinoSummary     float64 `json:"turn_casino_summary"`
	TurnGameSummary       float64 `json:"turn_game_summary"`
	TurnLotterySummary    float64 `json:"turn_lottery_summary"`
	TurnSportPercentage   float64 `json:"turn_sport_percentage"`
	TurnCasinoPercentage  float64 `json:"turn_casino_percentage"`
	TurnGamePercentage    float64 `json:"turn_game_percentage"`
	TurnLotteryPercentage float64 `json:"turn_lottery_percentage"`
}
