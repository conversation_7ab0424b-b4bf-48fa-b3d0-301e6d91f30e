package v2

import "time"

type UserMemberList struct {
	Id         int64
	MemberCode string
}
type UserQuestTodayPlayLogCreateBody struct {
	Id                   int64   `json:"id"`
	UserId               int64   `json:"userId"`
	DailyKey             string  `json:"dailyKey"`
	MemberCode           string  `json:"-" gorm:"-"`
	StatementDate        string  `json:"statementDate"`
	TurnSport            float64 `json:"turnSport"`
	ValidAmountSport     float64 `json:"validAmountSport"`
	WinLoseSport         float64 `json:"winLoseSport"`
	TurnCasino           float64 `json:"turnCasino"`
	WinLoseCasino        float64 `json:"winLoseCasino"`
	ValidAmountCasino    float64 `json:"validAmountCasino"`
	TurnGame             float64 `json:"turnGame"`
	WinLoseGame          float64 `json:"winLoseGame"`
	ValidAmountGame      float64 `json:"validAmountGame"`
	TurnLottery          float64 `json:"turnLottery"`
	WinLoseLottery       float64 `json:"winLoseLottery"`
	ValidAmountLottery   float64 `json:"validAmountLottery"`
	TurnP2p              float64 `json:"turnP2p"`
	WinLoseP2p           float64 `json:"winLoseP2p"`
	ValidAmountP2p       float64 `json:"validAmountP2p"`
	TurnFinancial        float64 `json:"turnFinancial"`
	WinLoseFinancial     float64 `json:"winLoseFinancial"`
	ValidAmountFinancial float64 `json:"validAmountFinancial"`
	TurnTotal            float64 `json:"turnTotal"`
	WinLoseTotal         float64 `json:"winLoseTotal"`
	ValidAmountTotal     float64 `json:"validAmountTotal"`
}

type UserQuestTodayPlayLogUpdateBody struct {
	Id                   int64    `json:"id"`
	DailyKey             string   `json:"dailyKey"`
	TurnSport            *float64 `json:"turnSport"`
	ValidAmountSport     *float64 `json:"validAmountSport"`
	WinLoseSport         *float64 `json:"winLoseSport"`
	TurnCasino           *float64 `json:"turnCasino"`
	WinLoseCasino        *float64 `json:"winLoseCasino"`
	ValidAmountCasino    *float64 `json:"validAmountCasino"`
	TurnGame             *float64 `json:"turnGame"`
	WinLoseGame          *float64 `json:"winLoseGame"`
	ValidAmountGame      *float64 `json:"validAmountGame"`
	TurnLottery          *float64 `json:"turnLottery"`
	WinLoseLottery       *float64 `json:"winLoseLottery"`
	ValidAmountLottery   *float64 `json:"validAmountLottery"`
	TurnP2p              *float64 `json:"turnP2p"`
	WinLoseP2p           *float64 `json:"winLoseP2p"`
	ValidAmountP2p       *float64 `json:"validAmountP2p"`
	TurnFinancial        *float64 `json:"turnFinancial"`
	WinLoseFinancial     *float64 `json:"winLoseFinancial"`
	ValidAmountFinancial *float64 `json:"validAmountFinancial"`
	TurnTotal            *float64 `json:"turnTotal"`
	WinLoseTotal         *float64 `json:"winLoseTotal"`
	ValidAmountTotal     *float64 `json:"validAmountTotal"`
}

type UserQuestTodayPlayLogV2Response struct {
	Id                   int64     `json:"id"`
	UserId               int64     `json:"userId"`
	DailyKey             string    `json:"dailyKey"`
	StatementDate        string    `json:"statementDate"`
	TurnSport            float64   `json:"turnSport"`
	ValidAmountSport     float64   `json:"validAmountSport"`
	WinLoseSport         float64   `json:"winLoseSport"`
	TurnCasino           float64   `json:"turnCasino"`
	WinLoseCasino        float64   `json:"winLoseCasino"`
	ValidAmountCasino    float64   `json:"validAmountCasino"`
	TurnGame             float64   `json:"turnGame"`
	WinLoseGame          float64   `json:"winLoseGame"`
	ValidAmountGame      float64   `json:"validAmountGame"`
	TurnLottery          float64   `json:"turnLottery"`
	WinLoseLottery       float64   `json:"winLoseLottery"`
	ValidAmountLottery   float64   `json:"validAmountLottery"`
	TurnP2p              float64   `json:"turnP2p"`
	WinLoseP2p           float64   `json:"winLoseP2p"`
	ValidAmountP2p       float64   `json:"validAmountP2p"`
	TurnFinancial        float64   `json:"turnFinancial"`
	WinLoseFinancial     float64   `json:"winLoseFinancial"`
	ValidAmountFinancial float64   `json:"validAmountFinancial"`
	TurnTotal            float64   `json:"turnTotal"`
	WinLoseTotal         float64   `json:"winLoseTotal"`
	ValidAmountTotal     float64   `json:"validAmountTotal"`
	CreatedAt            time.Time `json:"createdAt"`
}

type AgcSimpleWinLose struct {
	StartDate  string `json:"StartDate"`
	EndDate    string `json:"EndDate"`
	PageSize   int    `json:"PageSize"`
	PageIndex  int    `json:"PageIndex"`
	MemberName string `json:"MemberName"`
	AgentName  string `json:"AgentName"`
	Products   []int  `json:"Products"`
	TimeStamp  int    `json:"TimeStamp"`
	Sign       string `json:"Sign"`
}
