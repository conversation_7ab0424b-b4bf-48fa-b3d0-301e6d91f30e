package v2

import (
	"time"
)

const (
	DAILY_CHECKIN_TYPE  = 1
	CREDIT_DEPOSIT_TYPE = 2
	SPORT_TYPE          = 3
	CASINO_TYPE         = 4
	GAME_TYPE           = 5
	LOTTERY_TYPE        = 6
	P2P_TYPE            = 7
)

const (
	NO_CONDITION        = 1
	MIN_MIMUM_CONDITION = 2
	EQUAL_CONDITION     = 3
)

type DailyQuestCondition struct {
	Id      int64  `json:"id"`
	Name    string `json:"name"`
	LabelTH string `json:"labelTH"`
	LabelEN string `json:"labelEN"`
}
type DailyQuest struct {
	Id          int64               `json:"id"`
	Reward      float64             `json:"reward"`
	Description string              `json:"description"`
	StartDate   string              `json:"startDate"`
	EndDate     string              `json:"endDate"`
	StartTime   string              `json:"startTime"`
	EndTime     string              `json:"endTime"`
	IsActive    bool                `json:"isActive"`
	IsDeleted   bool                `json:"isDeleted"`
	CreatedAt   *time.Time          `json:"createdAt"`
	UpdatedAt   *time.Time          `json:"updatedAt"`
	Quest       []*DailyQuestDetail `json:"quest" gorm:"-"`
}

type DailyQuestDetail struct {
	QuestId               int64   `json:"questId"`
	QuestName             string  `json:"questName"`
	QuestImageUrl         string  `json:"questImageUrl"`
	QuestTypeId           int64   `json:"questTypeId"`
	QuestTypeName         string  `json:"questTypeName"`
	QuestConditionId      int64   `json:"questConditionId"`
	QuestConditionName    string  `json:"questConditionName"`
	QuestConditionLabelTH string  `json:"questConditionLabelTH"`
	QuestConditionLabelEN string  `json:"questConditionLabelEN"`
	ConditionAmount       float64 `json:"conditionAmount"`
	Sort                  int64   `json:"sort"`
}

type WebDailyQuest struct {
	Id           int64                  `json:"id"`
	DailyQuestId int64                  `json:"dailyQuestId"`
	Reward       float64                `json:"reward"`
	Description  string                 `json:"description"`
	StartDate    string                 `json:"startDate"`
	EndDate      string                 `json:"endDate"`
	StartTime    string                 `json:"startTime"`
	EndTime      string                 `json:"endTime"`
	Status       bool                   `json:"status" default:"false"`
	IsClaim      bool                   `json:"isClaim" default:"false"`
	Quest        []*WebDailyQuestDetail `json:"quest" gorm:"-"`
}

type GetCheckDailyQuest struct {
	Id        int64                       `json:"id"`
	Reward    float64                     `json:"reward"`
	StartDate string                      `json:"startDate"`
	EndDate   string                      `json:"endDate"`
	StartTime string                      `json:"startTime"`
	EndTime   string                      `json:"endTime"`
	Status    bool                        `json:"status" default:"false"`
	IsClaim   bool                        `json:"isClaim" default:"false"`
	Details   []*GetCheckDailyQuestDetail `json:"quest" gorm:"-"`
}

type GetCheckDailyQuestDetail struct {
	QuestId               int64   `json:"questId"`
	DailyQuestConditionId int64   `json:"dailyQuestConditionId"`
	ConditionAmount       float64 `json:"conditionAmount"`
	QuestTypeId           int64   `json:"questTypeId"`
}

type WebDailyQuestDetail struct {
	QuestId               int64   `json:"questId"`
	QuestName             string  `json:"questName"`
	QuestImageUrl         string  `json:"questImageUrl"`
	QuestTypeId           int64   `json:"questTypeId"`
	QuestTypeName         string  `json:"questTypeName"`
	QuestConditionId      int64   `json:"questConditionId"`
	QuestConditionName    string  `json:"questConditionName"`
	QuestConditionLabelTH string  `json:"questConditionLabelTH"`
	QuestConditionLabelEN string  `json:"questConditionLabelEN"`
	ConditionAmount       float64 `json:"conditionAmount"`
	Sort                  int64   `json:"sort"`
	Status                bool    `json:"status"`
}

type CreateDailyQuestRequest struct {
	Reward      float64                  `json:"reward" binding:"required"`
	Description string                   `json:"description" binding:"required"`
	StartDate   string                   `json:"startDate" binding:"required"`
	EndDate     string                   `json:"endDate" binding:"required"`
	StartTime   string                   `json:"startTime" binding:"required"`
	EndTime     string                   `json:"endTime" binding:"required"`
	Quests      []CreateDailyQuestDetail `json:"quests" binding:"required"`
}

type CreateDailyQuestDetail struct {
	QuestId          int64   `json:"questId" binding:"required"`
	QuestConditionId int64   `json:"questConditionId" binding:"required"`
	ConditionAmount  float64 `json:"conditionAmount" binding:"required"`
	Sort             int64   `json:"sort" binding:"required"`
}

type UpdateDailyQuestRequest struct {
	Id          int64                    `json:"id" binding:"required"`
	Reward      float64                  `json:"reward" binding:"required"`
	Description string                   `json:"description" binding:"required"`
	StartDate   string                   `json:"startDate" binding:"required"`
	EndDate     string                   `json:"endDate" binding:"required"`
	StartTime   string                   `json:"startTime" binding:"required"`
	EndTime     string                   `json:"endTime" binding:"required"`
	IsDeleted   bool                     `json:"isDeleted" default:"false"`
	Quests      []CreateDailyQuestDetail `json:"quests" binding:"required"`
}

type UpdateDailyQuestActiveRequest struct {
	Id       int64 `json:"id"`
	IsActive bool  `json:"isActive" default:"false"`
}

type DailyQuestListRequest struct {
	Page  int `form:"page" default:"1" min:"1"`
	Limit int `form:"limit" default:"10" min:"1" max:"100"`
}

type CreateUserDailyQuestRequest struct {
	DailyQuestId int64 `form:"dailyQuestId"`
	QuestId      int64 `form:"questId"`
}

type CreateUserDailyQuestDetail struct {
	UserDailyQuestId int64 `json:"user_daily_quest_id"`
	QuestId          int64 `json:"quest_id"`
}

type UpdateUserDailyQuestDetails struct {
	UserId                 int64 `json:"userId"`
	DailyQuestId           int64 `json:"id"`
	UserDailyQuestDetailId int64 `json:"user_daily_quest_detail_id"`
	Counter                int64 `json:"counter"`
	//QuestId                int64 `json:"questId"`
	//Status                 bool  `json:"status"`
}

type UpdateUserDailyQuestDetailRequest struct {
	UserDailyQuestDetailId int64 `json:"user_daily_quest_detail_id"`
	Status                 bool  `json:"status"`
}

type ClaimUserDailyQuestReward struct {
	UserDailyQuestId int64 `json:"userDailyQuestId" binding:"required"`
}

type GetUserDailyQuestResponse struct {
	Id           int64                     `json:"id"`
	DailyQuestId int64                     `json:"dailyQuestId"`
	Reward       float64                   `json:"reward"`
	Status       bool                      `json:"status"`
	IsClaim      bool                      `json:"isClaim"`
	Quests       []GetUserDailyQuestDetail `json:"quests" gorm:"-"`
}

type GetUserDailyQuestDetail struct {
	Id      int64 `json:"id"`
	QuestId int64 `json:"questId"`
	Status  bool  `json:"status"`
}

type UpdateUserDailyQuest struct {
	Id     int64 `json:"id"`
	Status bool  `json:"status"`
}

type UserQuestPlayLogQueryRequest struct {
	QuestTypeId          int64   `json:"questTypeId" form:"questTypeId"`
	QuestConditionId     int64   `json:"questConditionId" form:"questConditionId"`
	QuestConditionAmount float64 `json:"questConditionAmount" form:"questConditionAmount"`
}

type UserDailyQuestListResponse struct {
	Id           int64     `json:"id"`
	DailyQuestId int64     `json:"dailyQuestId"`
	Reward       float64   `json:"reward"`
	Status       bool      `json:"status" default:"false"`
	IsClaim      bool      `json:"isClaim" default:"false"`
	CreatedAt    time.Time `json:"created_at"`
	Progress     string    `json:"progress"`
}

type GetUserDailyQuestClaimDetail struct {
	Id           int64   `json:"id"`
	DailyQuestId int64   `json:"dailyQuestId"`
	Reward       float64 `json:"reward"`
	Status       bool    `json:"status"`
	IsClaim      bool    `json:"isClaim"`
}

type GetUserDailyQuestUnfinished struct {
	Id           int64                             `json:"id"`
	UserId       int64                             `json:"userId"`
	DailyQuestId int64                             `json:"dailyQuestId"`
	Status       bool                              `json:"status"`
	IsClaim      bool                              `json:"isClaim"`
	CreatedAt    time.Time                         `json:"created_at"`
	Details      []*UserDailyQuestUnfinishedDetail `json:"quest" gorm:"-"`
}

type UserDailyQuestUnfinishedDetail struct {
	Id      int64 `json:"id"`
	QuestId int64 `json:"questId"`
	Status  bool  `json:"status"`
}
