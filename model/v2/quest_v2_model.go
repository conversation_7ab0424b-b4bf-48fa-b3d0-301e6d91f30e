package v2

import "time"

type Quest struct {
	Id            int64      `json:"id"`
	Name          string     `json:"name"`
	ImageUrl      string     `json:"imageUrl"`
	Description   string     `json:"description"`
	IsDeleted     bool       `json:"isDeleted"`
	CreatedAt     *time.Time `json:"createdAt"`
	UpdatedAt     *time.Time `json:"updatedAt"`
	QuestTypeId   int64      `json:"questTypeId"`
	QuestTypeName string     `json:"questTypeName"`
}

type CreateQuest struct {
	Name        string `json:"name" binding:"required"`
	ImageUrl    string `json:"imageUrl"`
	Description string `json:"description" binding:"required"`
	QuestTypeId int64  `json:"questTypeId" binding:"required"`
}

type UpdateQuest struct {
	Id          int64  `json:"id" binding:"required"`
	Name        string `json:"name" binding:"required"`
	ImageUrl    string `json:"imageUrl" binding:"required"`
	Description string `json:"description" binding:"required"`
	QuestTypeId int64  `json:"questTypeId" binding:"required"`
	IsDeleted   bool   `json:"isDeleted"`
}

type QuestListRequest struct {
	Page  int `form:"page" default:"1" min:"1"`
	Limit int `form:"limit" default:"10" min:"1" max:"100"`
}

type QuestType struct {
	Id         int64  `json:"id"`
	Name       string `json:"name"`
	QueryTable string `json:"queryTable"`
	IsActive   bool   `json:"isActive"`
	IsDelete   bool   `json:"isDelete"`
}

type ImageUploadResponse struct {
	ImageUrl string `json:"imageUrl"`
}
