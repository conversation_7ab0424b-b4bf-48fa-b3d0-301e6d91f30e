package model

import (
	"time"
)

const (
	ACTIVITY_LUCKY_WHEEL_SETTING_CONDITION_DEPOSIT = int64(1)
	ACTIVITY_LUCKY_WHEEL_SETTING_CONDITION_LOSE    = int64(2)
)

const (
	ACTIVITY_LUCKY_WHEEL_ROUND_STATUS_ACTIVE         = int64(1)
	ACTIVITY_LUCKY_WHEEL_ROUND_STATUS_EXPIRED        = int64(2)
	ACTIVITY_LUCKY_WHEEL_ROUND_STATUS_PRIZE_CLAIMED  = int64(3)
	ACTIVITY_LUCKY_WHEEL_ROUND_STATUS_PRIZE_RECEIVED = int64(4)

	ACTIVITY_LUCKY_WHEEL_SETTING_AMOUNT_SPIN_6  = int64(1)
	ACTIVITY_LUCKY_WHEEL_SETTING_AMOUNT_SPIN_8  = int64(2)
	ACTIVITY_LUCKY_WHEEL_SETTING_AMOUNT_SPIN_10 = int64(3)
	ACTIVITY_LUCKY_WHEEL_SETTING_AMOUNT_SPIN_12 = int64(4)
)

type ActivityLuckyWheelSettingResponse struct {
	Id                                      int64                                         `json:"id"`
	ConditionId                             int64                                         `json:"conditionId"`
	LosePerRoll                             int                                           `json:"losePerRoll"`
	MaxRollPerDay                           int                                           `json:"maxRollPerDay"`
	CumulativeExpiredDays                   int                                           `json:"cumulativeExpiredDays"`
	IsEnabled                               bool                                          `json:"isEnable"`
	CreatedAt                               time.Time                                     `json:"createdAt"`
	ActivityLuckyWheelSettingAmountSpinId   int64                                         `json:"activityLuckyWheelSettingAmountSpinId"`
	ActivityLuckyWheelSettingAmountSpinName string                                        `json:"activityLuckyWheelSettingAmountSpinName"`
	UpdatedAt                               *time.Time                                    `json:"updatedAt"`
	CacheExpiredAt                          time.Time                                     `json:"cacheExpiredAt"`
	ImageSpin                               GetActivityLuckyWheelSettingImageSpinResponse `gorm:"-" json:"imageSpin"`
}

type ActivityLuckyWheelSettingCreateBody struct {
	Id                                    int64 `json:"id"`
	ConditionId                           int64 `json:"conditionId"`
	LosePerRoll                           int   `json:"losePerRoll"`
	MaxRollPerDay                         int   `json:"maxRollPerDay"`
	ActivityLuckyWheelSettingAmountSpinId int64 `json:"activityLuckyWheelSettingAmountSpinId"`
	CumulativeExpiredDays                 int   `json:"cumulativeExpiredDays"`
}

type ActivityLuckyWheelSettingUpdateRequest struct {
	ConditionId                           int64                                        `json:"conditionId"`
	LosePerRoll                           int                                          `json:"losePerRoll"`
	MaxRollPerDay                         int                                          `json:"maxRollPerDay"`
	ActivityLuckyWheelSettingAmountSpinId int64                                        `json:"activityLuckyWheelSettingAmountSpinId"`
	CumulativeExpiredDays                 int                                          `json:"cumulativeExpiredDays"`
	IsEnabled                             *bool                                        `json:"isEnable"`
	ImageSpin                             ActivityLuckyWheelSettingImageSpinUpdateBody `json:"imageSpin"`
}

type ActivityLuckyWheelSettingUpdateBody struct {
	ConditionId                           int64 `json:"conditionId"`
	LosePerRoll                           int   `json:"losePerRoll"`
	MaxRollPerDay                         int   `json:"maxRollPerDay"`
	ActivityLuckyWheelSettingAmountSpinId int64 `json:"activityLuckyWheelSettingAmountSpinId"`
	CumulativeExpiredDays                 int   `json:"cumulativeExpiredDays"`
	IsEnabled                             *bool `json:"isEnable"`
}

type ActivityLuckyWheelResponse struct {
	Id                 int64      `json:"id"`
	Position           int        `json:"position"`
	Message            string     `json:"message"`
	MinimumReward      int        `json:"minimumReward"`
	HexBackgroundColor string     `json:"hexBackgroundColor"`
	FontColor          string     `json:"fontColor"`
	PercentWin         float64    `json:"percentWin"`
	CreatedAt          *time.Time `json:"createdAt"`
	UpdatedAt          *time.Time `json:"updatedAt"`
}

type ActivityLuckyWheelUserResponse struct {
	Id                 int64  `json:"id"`
	Position           int    `json:"position"`
	Message            string `json:"message"`
	HexBackgroundColor string `json:"hexBackgroundColor"`
	FontColor          string `json:"fontColor"`
}

type ActivityLuckyWheelUpdateBody struct {
	Id                 int64    `json:"id"`
	Message            *string  `json:"message"`
	MinimumReward      *int     `json:"minimumReward"`
	HexBackgroundColor *string  `json:"hexBackgroundColor"`
	FontColor          *string  `json:"fontColor"`
	PercentWin         *float64 `json:"percentWin"`
}

type ActivityLuckyWheelRequest struct {
	List []ActivityLuckyWheelUpdateBody `json:"list"`
}

type ConditionTypeResponse struct {
	Id      int64  `json:"id"`
	Name    string `json:"name"`
	LabelTh string `json:"labelTh"`
	LabelEn string `json:"labelEn"`
}

type ActivityLuckyWheelRoundBody struct {
	Id              int64      `json:"id"`
	UserId          int64      `json:"userId"`
	Received        int        `json:"received"`
	ConditionId     int64      `json:"conditionId"`
	ConditionAmount float64    `json:"conditionAmount"`
	ReceivedDate    time.Time  `json:"receivedDate"`
	ExpiredDate     *time.Time `json:"expiredDate"`
	CreatedAt       time.Time  `json:"createdAt"`
	UpdatedAt       *time.Time `json:"updatedAt"`
}

type ActivityLuckyWheelRoundUserRequest struct {
	UserId          int64   `json:"userId"`
	ConditionId     int64   `json:"conditionId"`
	ConditionAmount float64 `json:"conditionAmount"`
}

type ActivityLuckyWheelRoundUserBody struct {
	Id                   int64      `json:"id"`
	LuckyWheelRoundId    int64      `json:"luckyWheelRoundId"`
	UserId               int64      `json:"userId"`
	LuckyWheelId         *int64     `json:"luckyWheelId"`
	Reward               *float64   `json:"reward"`
	ConditionId          int64      `json:"conditionId"`
	ConditionAmount      float64    `json:"conditionAmount"`
	ConditionDescription string     `json:"conditionDescription"`
	StatusId             int64      `json:"StatusId"`
	ReceivedDate         time.Time  `json:"receivedDate"`
	ExpiredDate          *time.Time `json:"expiredDate"`
	RotatedDate          *time.Time `json:"rotatedDate"`
	CreatedAt            time.Time  `json:"createdAt"`
	UpdatedAt            *time.Time `json:"updatedAt"`
}

type ActivityLuckyWheelRoundUserResponse struct {
	Id              int64      `json:"id"`
	UserId          int64      `json:"userId"`
	LuckyWheelId    *int64     `json:"luckyWheelId"`
	Reward          *float64   `json:"reward"`
	ConditionId     int64      `json:"conditionId"`
	ConditionAmount float64    `json:"conditionAmount"`
	StatusId        int64      `json:"statusId"`
	ReceivedDate    time.Time  `json:"receivedDate"`
	ExpiredDate     *time.Time `json:"expiredDate"`
	RotatedDate     *time.Time `json:"rotatedDate"`
	CreatedAt       time.Time  `json:"createdAt"`
	UpdatedAt       *time.Time `json:"updatedAt"`
}

type RoundLuckyWheelUserResponse struct {
	ConditionId          int64                                         `json:"conditionId"`
	LosePerRoll          int                                           `json:"losePerRoll"`
	RoundLeft            int                                           `json:"roundLeft"`
	TotalAmountSpinRound int                                           `json:"totalAmountSpinRound"`
	TotalSpin            int                                           `json:"totalSpin"`
	ImageSpin            GetActivityLuckyWheelSettingImageSpinResponse `json:"imageSpin"`
}

type CheckLuckyWheelRoundCurrentUserResponse struct {
	Id              int64      `json:"id"`
	UserId          int64      `json:"userId"`
	LuckyWheelId    int64      `json:"luckyWheelId"`
	Reward          float64    `json:"reward"`
	ConditionId     int64      `json:"conditionId"`
	ConditionAmount float64    `json:"conditionAmount"`
	StatusId        int64      `json:"statusId"`
	ReceivedDate    time.Time  `json:"receivedDate"`
	ExpiredDate     *time.Time `json:"expiredDate"`
	RotatedDate     *time.Time `json:"rotatedDate"`
}

type UpdateActivityLuckyWheelRoundUserRequest struct {
	Id           int64      `json:"id"`
	UserIncomeId *int64     `json:"userIncomeId"`
	UserId       *int64     `json:"userId"`
	LuckyWheelId *int64     `json:"luckyWheelId"`
	Reward       *float64   `json:"reward"`
	StatusId     *int64     `json:"statusId"`
	RotatedDate  *time.Time `json:"rotatedDate"`
}

type RewardResponse struct {
	LuckyWheelId *int64   `json:"luckyWheelId"`
	Reward       *float64 `json:"reward"`
}

type LuckyweelSummaryListBody struct {
	LuckyWheelRoundId    int64     `json:"luckyWheelRoundId"`
	UserId               int64     `json:"userId"`
	MemberCode           string    `json:"memberCode"`
	LuckySpinCount       int64     `json:"luckySpinCount"`
	ConditionId          int64     `json:"conditionId"`
	ConditionDescription string    `json:"conditionDescription"`
	TotalReward          float64   `json:"totalReward"`
	ConditionAmount      float64   `json:"conditionAmount"`
	ReceivedDate         time.Time `json:"receivedDate"`
	StatusId             int64     `json:"statusId"`
}
type LuckyweelSummaryListResponse struct {
	LuckyWheelRoundId    int64     `json:"luckyWheelRoundId"`
	UserId               int64     `json:"userId"`
	MemberCode           string    `json:"memberCode"`
	LuckySpinCount       int64     `json:"luckySpinCount"`
	ConditionId          int64     `json:"conditionId"`
	ConditionDescription string    `json:"conditionDescription"`
	TotalReward          float64   `json:"totalReward"`
	ConditionAmount      float64   `json:"conditionAmount"`
	ReceivedDate         time.Time `json:"receivedDate"`
	StatusId             int64     `json:"statusId"`
}

type LuckyweelSummaryListRequest struct {
	Search   string `form:"search"`
	Page     int    `form:"page" default:"1" min:"1"`
	FromDate string `form:"fromDate" time_format:"2006-01-02" default:""`
	ToDate   string `form:"toDate" time_format:"2006-01-02" default:""`
	Limit    int    `form:"limit" default:"10" min:"1" max:"100"`
}

type LuckyweelRoundListResponse struct {
	Id                   int64      `json:"id"`
	UserId               int64      `json:"userId"`
	Received             int64      `json:"received"`
	ConditionId          int64      `json:"conditionId"`
	ConditionAmount      float64    `json:"conditionAmount"`
	ConditionDescription string     `json:"conditionDescription"`
	ReceivedDate         time.Time  `json:"receivedDate"`
	ExpiredDate          *time.Time `json:"expiredDate"`
	CreatedAt            time.Time  `json:"createdAt"`
	UpdatedAt            *time.Time `json:"updatedAt"`
}

type ActivityLuckyWheelRoundConfirmCreateRequest struct {
	Id               int64   `json:"id"`
	ActionKey        string  `json:"actionKey"`
	UserId           int64   `json:"userId"`
	LuckyWheelUserId int64   `json:"luckyWheelUserId"`
	ConditionAmount  float64 `json:"conditionAmount"`
}

type ActivityLuckyWheelRoundConfirmResponse struct {
	Id               int64   `json:"id"`
	ActionKey        string  `json:"actionKey"`
	UserId           int64   `json:"userId"`
	LuckyWheelUserId int64   `json:"luckyWheelUserId"`
	ConditionAmount  float64 `json:"conditionAmount"`
}

type LuckyweelSummaryByUserIdRequest struct {
	UserId int64 `form:"userId" binding:"required"`
	Page   int   `form:"page" default:"1" min:"1"`
	Limit  int   `form:"limit" default:"10" min:"1" max:"100"`
}

type LuckyweelSummaryByUserIdResponse struct {
	Id           int64     `json:"id"`
	UserId       int64     `json:"userId"`
	Reward       float64   `json:"reward"`
	ReceivedDate time.Time `json:"receivedDate"`
	RotatedDate  time.Time `json:"rotatedDate"`
}

type SuccessMemberCodeWithPagination struct {
	Message    string      `json:"message" validate:"required,min=1,max=255"`
	MemberCode string      `json:"memberCode"`
	List       interface{} `json:"list"`
	Total      int64       `json:"total"`
}

type LuckyWheelRoundInfo struct {
	LuckyWheelRoundId    int64   `json:"luckyWheelRoundId"`
	ConditionId          int64   `json:"condition_id"`
	ConditionDescription string  `json:"condition_description"`
	ConditionAmount      float64 `json:"condition_amount"`
}

type GetActivityLuckyWheelSettingImageSpinResponse struct {
	Id                                    int64  `json:"id"`
	ActivityLuckyWheelSettingAmountSpinId int64  `json:"activityLuckyWheelSettingAmountSpinId"`
	ImageMainSpinUrl                      string `json:"imageMainSpinUrl"`
	ImageArrowSpinUrl                     string `json:"imageArrowSpinUrl"`
	ImageButtonSpinUrl                    string `json:"imageButtonSpinUrl"`
}

type ActivityLuckyWheelSettingImageSpinUpdateBody struct {
	ActivityLuckyWheelSettingAmountSpinId int64  `json:"-"`
	ImageMainSpinUrl                      string `json:"imageMainSpinUrl"`
	ImageArrowSpinUrl                     string `json:"imageArrowSpinUrl"`
	ImageButtonSpinUrl                    string `json:"imageButtonSpinUrl"`
}
