package model

import (
	"time"
)

const (
	CREDIT_TYPE_DEPOSIT               = 1
	CREDIT_TYPE_WITHDRAW              = 2
	CREDIT_TYPE_BONUS                 = 3
	CREDIT_TYPE_PROMOTION_RETURN_LOSS = 4
	CREDIT_TYPE_AFFILIATE_INCOME      = 5
	CREDIT_TYPE_ALLIANCE_INCOME       = 6
	CREDIT_TYPE_TAKE_CREDIT_BACK      = 7
	CREDIT_TYPE_DAILY_ACTIVITY_BONUS  = 8
	CREDIT_TPYE_LUCKY_WHEEL           = 9
	CREDIT_TYPE_PROMOTION_WEB         = 10
	CREDIT_TYPE_COUPON_CASH           = 11
	CREDIT_TYPE_LOTTERY               = 12
	CREDIT_TYPE_PROMOTION_RETURN_TURN = 13
	CREDIT_TYPE_CANCEL_CREDIT         = 14
	CREDIT_TYPE_DAILY_QUEST_BONUS     = 15
)

const (
	CREDIT_DIRECTION_DEPOSIT  = 1
	CREDIT_DIRECTION_WITHDRAW = 2
)

// const (
// 	KBANK        = int64(1)
// 	SCB          = int64(2)
// 	BBL          = int64(3)
// 	BAY          = int64(4)
// 	KT           = int64(5)
// 	TTB          = int64(6)
// 	GSB          = int64(7)
// 	BAAC         = int64(8)
// 	KK           = int64(9)
// 	GHB          = int64(10)
// 	UOB          = int64(11)
// 	LH           = int64(12)
// 	CIMB         = int64(13)
// 	HSBC         = int64(14)
// 	ICBC         = int64(15)
// 	ISBT         = int64(16)
// 	TISCO        = int64(17)
// 	CITI         = int64(18)
// 	SCBT         = int64(19)
// 	TRUE         = int64(20)
// 	EXTERNAL     = int64(21)
// )

type UserTransaction struct {
	Id             int64      `json:"id"`
	UserId         int64      `json:"userId"`
	DirectionId    int64      `json:"directionId"`
	TypeId         int64      `json:"typeId"`
	AccountId      *int64     `json:"accountId"`
	RefId          *int64     `json:"refId"`
	Detail         string     `json:"detail"`
	PromotionId    *int64     `json:"promotionId"`
	CreditBefore   float64    `json:"creditBefore"`
	CreditBack     float64    `json:"creditBack"`
	CreditAmount   float64    `json:"creditAmount"`
	BonusAmount    float64    `json:"bonusAmount"`
	CreditAfter    float64    `json:"creditAfter"`
	TransferAt     time.Time  `json:"transferAt"`
	CreateAdminId  int64      `json:"createAdminId"`
	ConfirmAdminId *int64     `json:"confirmAdminId"`
	IsAdjustAuto   bool       `json:"isAdjustAuto"`
	WorkSeconds    int        `json:"workSeconds"`
	CreatedAt      time.Time  `json:"createdAt"`
	IsShow         bool       `json:"isShow"`
	RemovedAt      *time.Time `json:"removedAt"`
	RemoveAdminId  *int64     `json:"removeAdminId"`
}
type UserTransactionListRequest struct {
	UserId           *int64 `form:"userId"`
	OfDate           string `form:"ofDate"`
	DateType         string `form:"dateType"`
	FromDate         string `form:"fromDate"`
	ToDate           string `form:"toDate"`
	TypeId           *int64 `form:"typeId"`
	AccountId        *int64 `form:"accountId"`
	PaymentMerchatId *int64 `form:"paymentMerchatId"`
	Search           string `form:"search"`
	AdminId          *int64 `form:"adminId"`
	Page             int    `form:"page" default:"1" min:"1"`
	Limit            int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol          string `form:"sortCol"`
	SortAsc          string `form:"sortAsc"`
}
type UserTransactionForExcelListRequest struct {
	UserId    *int64 `form:"userId"`
	OfDate    string `form:"ofDate"`
	DateType  string `form:"dateType"`
	FromDate  string `form:"fromDate"`
	ToDate    string `form:"toDate"`
	TypeId    *int64 `form:"typeId"`
	AccountId *int64 `form:"accountId"`
	Search    string `form:"search"`
	AdminId   *int64 `form:"adminId"`
	SortCol   string `form:"sortCol"`
	SortAsc   string `form:"sortAsc"`
}
type UserTransactionResponse struct {
	Id                   int64      `json:"id"`
	UserId               int64      `json:"userId"`
	UserMemberCode       string     `json:"userMemberCode"`
	Username             string     `json:"username"`
	UserFullname         string     `json:"userFullname"`
	DirectionId          int64      `json:"directionId"`
	DirectionName        string     `json:"directionName"`
	TypeId               int64      `json:"typeId"`
	TypeName             string     `json:"typeName"`
	RefId                *int64     `json:"refId"`
	AccountId            *int64     `json:"accountId"`
	Detail               string     `json:"detail"`
	PromotionId          *int64     `json:"promotionId"`
	CreditBefore         float64    `json:"creditBefore"`
	CreditBack           float64    `json:"creditBack"`
	CreditAmount         float64    `json:"creditAmount"`
	BonusAmount          float64    `json:"bonusAmount"`
	CreditAfter          float64    `json:"creditAfter"`
	TransferAt           time.Time  `json:"transferAt"`
	SlipImgUrl           string     `json:"slipImgUrl"`
	CreateAdminId        int64      `json:"createAdminId"`
	CreateAdminUsername  string     `json:"createAdminUsername"`
	ConfirmAdminId       *int64     `json:"confirmAdminId"`
	ConfirmAdminUsername string     `json:"confirmAdminUsername"`
	IsAdjustAuto         bool       `json:"isAdjustAuto"`
	WorkSeconds          int        `json:"workSeconds"`
	CreatedAt            time.Time  `json:"createdAt"`
	RemovedAt            *time.Time `json:"removedAt"`
	RemovedAdminId       *int64     `json:"removedAdminId"`
	RemovedAdminUsername string     `json:"removedAdminUsername"`
}
type UserTransactionBonusResponse struct {
	Id                int64      `json:"id"`
	UserId            int64      `json:"userId"`
	DirectionId       int64      `json:"directionId"`
	TypeId            int64      `json:"typeId"`
	AccountId         *int64     `json:"accountId"`
	RefId             *int64     `json:"refId"`
	Detail            string     `json:"detail"`
	PromotionId       *int64     `json:"promotionId"`
	CreditBefore      float64    `json:"creditBefore"`
	CreditBack        float64    `json:"creditBack"`
	CreditAmount      float64    `json:"creditAmount"`
	BonusAmount       float64    `json:"bonusAmount"`
	CreditAfter       float64    `json:"creditAfter"`
	TransferAt        time.Time  `json:"transferAt"`
	CreateAdminId     int64      `json:"createAdminId"`
	ConfirmAdminId    *int64     `json:"confirmAdminId"`
	IsAdjustAuto      bool       `json:"isAdjustAuto"`
	WorkSeconds       int        `json:"workSeconds"`
	CreatedAt         time.Time  `json:"createdAt"`
	IsShow            bool       `json:"isShow"`
	RemovedAt         *time.Time `json:"removedAt"`
	RemoveAdminId     *int64     `json:"removeAdminId"`
	RefUserId         int64      `json:"refUserId"`
	AlliancePercent   float64    `json:"alliancePercent"`
	CommissionPercent float64    `json:"commissionPercent"`
}
type UserTransactionSummaryResponse struct {
	TotalDepositAmount    float64 `json:"totalDepositAmount"`
	TotalWithdrawAmount   float64 `json:"totalWithdrawAmount"`
	TotalBonusAmount      float64 `json:"totalBonusAmount"`
	TotalCancelCreditBack float64 `json:"totalCancelCreditBack"`
}
type UserTransactionCreateRequest struct {
	UserId           int64      `form:"userId"`
	TypeId           int64      `form:"typeId"`
	AccountId        *int64     `form:"accountId"`
	PaymentMerchatId *int64     `form:"paymentMerchatId"`
	Amount           float64    `form:"amount"`
	BonusAmount      float64    `form:"bonusAmount"`
	RefTransId       int64      `form:"refTransId"`
	IsAdjustAuto     bool       `form:"isAdjustAuto"`
	TransferAt       *time.Time `form:"transferAt"`
	CreateBy         *int64     `form:"createBy"`
	ConfirmBy        *int64     `form:"confirmBy"`
	StartWorkAt      time.Time  `form:"startWorkAt"`
	RefId            *int64     `form:"refId"`
	IsShow           *bool      `form:"isShow"`
	Detail           string     `form:"detail"`
	PromotionId      *int64     `form:"promotionId"`
}
type UserTransactionCreateResponse struct {
	AgentSuccess      bool      `form:"agentSuccess"`
	TransferAt        time.Time `form:"transferAt"`
	AgentBeforeAmount float64   `form:"agentBeforeAmount"`
	AgentAfterAmount  float64   `form:"agentAfterAmount"`
}
type UserTransactionShowUpdate struct {
	TransactionId   int64      `json:"transactionId"`
	ConfirmedAt     *time.Time `json:"confirmedAt"`
	ConfirmAdminId  *int64     `json:"confirmedByAdminId"`
	Detail          string     `json:"detail"`
	AccountId       *int64     `json:"accountId"`
	RemovedExternal *int64     `json:"removedExternal"`
	FailedAddCredit *int64     `json:"failedAddCredit"`
	TransferAt      *time.Time `json:"transferAt"`
}
type GetUserWithdrawCreditTransaction struct {
	Id             int64     `json:"id"`
	UserId         int64     `json:"userId"`
	DirectionId    int64     `json:"directionId"`
	TypeId         int64     `json:"typeId"`
	AccountId      int64     `json:"accountId"`
	RefId          int64     `json:"refId"`
	Detail         string    `json:"detail"`
	PromotionId    int64     `json:"promotionId"`
	CreditBefore   float64   `json:"creditBefore"`
	CreditBack     float64   `json:"creditBack"`
	CreditAmount   float64   `json:"creditAmount"`
	BonusAmount    float64   `json:"bonusAmount"`
	CreditAfter    float64   `json:"creditAfter"`
	TransferAt     time.Time `json:"transferAt"`
	CreateAdminId  int64     `json:"createAdminId"`
	ConfirmAdminId *int64    `json:"confirmAdminId"`
	IsAdjustAuto   bool      `json:"isAdjustAuto"`
	WorkSeconds    int       `json:"workSeconds"`
	CreatedAt      time.Time `json:"createdAt"`
	IsShow         bool      `json:"isShow"`
	RemoveAt       time.Time `json:"removeAt"`
	RemoveAdminId  int64     `json:"removeAdminId"`
}

type UserTransactionExternalShowUpdate struct {
	TransactionId      int64     `json:"transactionId"`
	ConfirmedAt        time.Time `json:"confirmedAt"`
	ConfirmedByAdminId *int64    `json:"confirmedByAdminId"`
	Detail             string    `json:"detail"`
	AccountId          *int64    `json:"accountId"`
}
