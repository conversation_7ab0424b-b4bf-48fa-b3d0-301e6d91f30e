package model

import (
	"time"
)

type PompayDepositRequest struct {
	<PERSON><PERSON><PERSON>        string  `json:"partner<PERSON>ey"`
	RepayAppId        string  `json:"repayAppId"`
	LoanAppId         string  `json:"loanAppId"`
	MerchantId        string  `json:"merchantId"`
	Token             string  `json:"token"`
	AesKey            string  `json:"aesKey"`
	OrderNo           string  `json:"orderNo"`
	BankCode          string  `json:"bankCode"`
	Amount            float64 `json:"amount"`
	UserFullname      string  `json:"userFullname"`
	UserMobile        string  `json:"userMobile"`
	UserAccountNumber string  `json:"userAccountNumber"`
	UserAccountBank   string  `json:"userAccountBank"`
}
type PompayEncryptPayload struct {
	Data struct {
		PartnerKey string `json:"partner_key"`
		EnData     string `json:"en_data"`
	} `json:"data"`
}

type PompayWebhookEncryptPayload struct {
	Data struct {
		Partner<PERSON><PERSON> string `json:"partner_key"`
		EnData     string `json:"en_data"`
		MchOrderNo string `json:"mch_order_no"`
	} `json:"data"`
}

type PompayWebhookDepositResponse struct {
	Sign               string `json:"sign"`
	Timestamp          int64  `json:"timestamp"`
	OrderNo            string `json:"order_no"`
	MchOrderNo         string `json:"mch_order_no"`
	Status             int    `json:"status"`
	Amount             string `json:"amount"`
	PayTime            string `json:"pay_time"`
	RepayAccountBank   string `json:"repay_account_bank"`
	RepayAccountNumber string `json:"repay_account_number"`
	AccountNumber      string `json:"account_number"`
	AccountBank        string `json:"account_bank"`
}

type PompayErrorRemoteResponse struct {
	Status  string `json:"status"`
	Message string `json:"message"`
}
type PompayErrorStringRemoteResponse struct {
	Message string `json:"message"`
	Code    string `json:"code"`
	Data    string `json:"data"`
}

type PompayToken struct {
	Id          int64      `json:"id"`
	AccessToken string     `json:"accessToken"`
	ExpireAt    time.Time  `json:"expireAt"`
	CreateBy    int64      `json:"createBy"`
	CreatedAt   time.Time  `json:"createdAt"`
	UpdatedAt   *time.Time `json:"updatedAt"`
}
type PompayTokenCreateBody struct {
	Id          int64     `json:"id"`
	AccessToken string    `json:"accessToken"`
	ExpireAt    time.Time `json:"expireAt"`
	CreateBy    int64     `json:"createBy"`
}
type PompayTokenCreateRemoteRequest struct {
	AccessKey string `json:"accessKey" validate:"required"`
	SecretKey string `json:"secretKey" validate:"required"`
}
type PompayTokenCreateRemoteResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Code    string `json:"code"`
	Data    struct {
		Token string `json:"token"`
	} `json:"data"`
}

type PompayCustomerCreateRemoteRequest struct {
	Name      string `json:"name" validate:"required"`
	BankCode  string `json:"bankCode" validate:"required"`
	AccountNo string `json:"accountNo" validate:"required"`
}
type PompayCustomerCreateRemoteResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Code    string `json:"code"`
	Data    struct {
		Partner      string   `json:"partner"`
		CustomerUuid string   `json:"customerUuid"`
		ClientCode   string   `json:"clientCode"`
		Name         string   `json:"name"`
		SearchName   []string `json:"searchName"`
		AccountNo    string   `json:"accountNo"`
		BankCode     string   `json:"bankCode"`
		Status       string   `json:"status"`
		CreatedAt    int64    `json:"createdAt"`
		UpdatedAt    int64    `json:"updatedAt"`
	} `json:"data"`
}
type PompayCustomerUpdateRemoteRequest struct {
	CustomerUuid string `json:"customerUuid" validate:"required"`
	Name         string `json:"name" validate:"required"`
	BankCode     string `json:"bankCode" validate:"required"`
	AccountNo    string `json:"accountNo" validate:"required"`
}
type PompayCustomerUpdateRemoteResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Code    string `json:"code"`
	Data    struct {
		Partner      string   `json:"partner"`
		CustomerUuid string   `json:"customerUuid"`
		ClientCode   string   `json:"clientCode"`
		Name         string   `json:"name"`
		SearchName   []string `json:"searchName"`
		AccountNo    string   `json:"accountNo"`
		BankCode     string   `json:"bankCode"`
		Status       string   `json:"status"`
		CreatedAt    int64    `json:"createdAt"`
		UpdatedAt    int64    `json:"updatedAt"`
	} `json:"data"`
}

type PompayDepositCreateRemoteRequest struct {
	ClientId          string `json:"clientId"`
	TransactionId     string `json:"transactionId"`
	CustName          string `json:"custName"`
	CustSecondaryName string `json:"custSecondaryName"`
	CustMobile        string `json:"custMobile"`
	CustEmail         string `json:"custEmail"`
	Amount            string `json:"amount"`
	ReturnUrl         string `json:"returnUrl"`
	CallbackUrl       string `json:"callbackUrl"`
	PaymentMethod     string `json:"paymentMethod"`
	BankAcc           string `json:"bankAcc"`
	CustBank          string `json:"custBank"`
	HashVal           string `json:"hashVal"`
}
type PompayDepositCreateRemoteResponse struct {
	BankAccount       string `json:"bankAccount"`
	BankAccountName   string `json:"bankAccountName"`
	BankAccountNameTH string `json:"bankAccountNameTH"`
	Bank              string `json:"bank"`
	ReferenceId       string `json:"referenceId"`
	TransactionId     string `json:"transactionId"`
	Amount            string `json:"amount"`
	DepositAmount     string `json:"deposit_amount"`
	DepositAmount2    string `json:"depositAmount"`
	Type              string `json:"type"`
	QrString          string `json:"qrString"`
}

type PompayCheckBalanceRemoteResponse struct {
	Success bool `json:"success"`
	Code    int  `json:"code"`
	Data    struct {
		InBalance struct {
			CoinChain string  `json:"coinChain"`
			CoinUnit  string  `json:"coinUnit"`
			Balance   float64 `json:"balance"`
			Freeze    float64 `json:"freeze"`
		} `json:"inBalance"`
		OutBalance struct {
			CoinChain string  `json:"coinChain"`
			CoinUnit  string  `json:"coinUnit"`
			Balance   float64 `json:"balance"`
			Freeze    float64 `json:"freeze"`
		} `json:"outBalance"`
		Sign             string `json:"sign"`
		CheckRequestTime int64  `json:"checkRequestTime"`
		DfBalance        struct {
			CoinChain string  `json:"coinChain"`
			CoinUnit  string  `json:"coinUnit"`
			Balance   float64 `json:"balance"`
			Freeze    float64 `json:"freeze"`
		} `json:"dfBalance"`
		DsBalance struct {
			CoinChain string  `json:"coinChain"`
			CoinUnit  string  `json:"coinUnit"`
			Balance   float64 `json:"balance"`
			Freeze    float64 `json:"freeze"`
		} `json:"dsBalance"`
	} `json:"data"`
}

type PompayGetOrderRemoteResponse struct {
	Success bool `json:"success"`
	Code    int  `json:"code"`
	Data    struct {
		OrderNo       string  `json:"orderNo"`
		ReceiveAddr   string  `json:"receiveAddr"`
		ChainName     string  `json:"chainName"`
		CoinUnit      string  `json:"coinUnit"`
		RequestAmount float64 `json:"requestAmount"`
		PayAmount     float64 `json:"payAmount"`
		Status        string  `json:"status"`
		Sign          string  `json:"sign"`
	} `json:"data"`
}

type PompayWithdrawCreateRemoteRequest struct {
	ClientId      string `json:"clientId"`
	TransactionId string `json:"transactionId"`
	CustName      string `json:"custName"`
	CustBank      string `json:"custBank"`
	CustBankAcc   string `json:"custBankAcc"`
	CustMobile    string `json:"custMobile"`
	CustEmail     string `json:"custEmail"`
	Amount        string `json:"amount"`
	CallbackUrl   string `json:"callbackUrl"`
	HashVal       string `json:"hashVal"`
}

type PompayWithdrawCreateRemoteResponse struct {
	Status string `json:"status"`
	Data   struct {
		ReferenceId   string `json:"referenceId"`
		TransactionId string `json:"transactionId"`
	} `json:"data"`
	Message string `json:"message"`
}

type PompayWebhookRequest struct {
	JsonPayload string `json:"jsonPayload"`
}
type PompayWebhookCreateBody struct {
	Id          int64  `json:"id"`
	Name        string `json:"name"`
	JsonPayload string `json:"jsonPayload"`
}

type PompayDepositWebhookResponse struct {
	ReferenceId       string `json:"referenceId"`
	TransactionId     string `json:"transactionId"`
	CustName          string `json:"custName"`
	CustSecondaryName string `json:"custSecondaryName"`
	BankName          string `json:"bankName"`
	BankReference     string `json:"bankReference"`
	BankReferenceTwo  string `json:"bankReferenceTwo"`
	Amount            string `json:"amount"`
	DepositAmount     string `json:"depositAmount"`
	Status            string `json:"status"`
	HashVal           string `json:"hashVal"`
}
type PompayWithDrawWebhookResponse struct {
	ReferenceId   string `json:"referenceId"`
	TransactionId string `json:"transactionId"`
	CustName      string `json:"custName"`
	CustBank      string `json:"custBank"`
	CustBankAcc   string `json:"custBankAcc"`
	Amount        string `json:"amount"`
	Fee           string `json:"fee"`
	Total         string `json:"total"`
	Remark        string `json:"remark"`
	Status        string `json:"status"`
	HashVal       string `json:"hashVal"`
	// Code          string `json:"code"` ถ้ามี error จะเป็น ตัวเลข ถ้าไม่มี error จะเป็น "00"
}

type PompayCustomerDepositInfo struct {
	Name        string `json:"name"`
	DisplayName string `json:"displayName"`
	ShopName    string `json:"shopName"`
	MinAmount   int64  `json:"minAmount"`
	MaxAmount   int64  `json:"maxAmount"`
}
type PompayDepositCreateRequest struct {
	UserId int64   `form:"userId" json:"-" validate:"required"`
	Amount float64 `form:"amount" json:"amount" binding:"required,min=1" validate:"required,min=1"`
}
type PompayWithdrawCreateRequest struct {
	RefId  int64   `form:"refId" validate:"required"`
	UserId int64   `form:"userId" json:"-" validate:"required"`
	Amount float64 `form:"amount" binding:"required,min=1" validate:"required,min=1"`
}

const (
	POMPAY_ORDER_TYPE_DEPOSIT  = 1
	POMPAY_ORDER_TYPE_WITHDRAW = 2
)

type PompayCustomer struct {
	Id           int64  `json:"id"`
	UserId       int64  `json:"userId"`
	CustomerUuid string `json:"customerUuid"`
	FullName     string `json:"fullName"`
	BankCode     string `json:"bankCode"`
	AccountNo    string `json:"accountNo"`
	AccountName  string `json:"accountName"`
}
type PompayCustomerListRequest struct {
	Page    int    `form:"page" default:"1" min:"1"`
	Limit   int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol string `form:"sortCol"`
	SortAsc string `form:"sortAsc"`
}
type PompayCustomerResponse struct {
	Id           int64  `json:"id"`
	UserId       int64  `json:"userId"`
	CustomerUuid string `json:"customerUuid"`
	FullName     string `json:"fullName"`
	BankCode     string `json:"bankCode"`
	AccountNo    string `json:"accountNo"`
	AccountName  string `json:"accountName"`
}
type PompayCustomerCreateBody struct {
	Id           int64  `json:"id"`
	UserId       int64  `json:"userId"`
	CustomerUuid string `json:"customerUuid"`
	FullName     string `json:"fullName"`
	BankCode     string `json:"bankCode"`
	AccountNo    string `json:"accountNo"`
	AccountName  string `json:"accountName"`
}
type PompayCustomerUpdateBody struct {
	CustomerUuid *string `json:"customerUuid"`
	FullName     *string `json:"fullName"`
	BankCode     *string `json:"bankCode"`
	AccountNo    *string `json:"accountNo"`
	AccountName  *string `json:"accountName"`
}

type PompayOrder struct {
	Id                int64      `json:"id"`
	UserId            int64      `json:"userId"`
	OrderTypeId       int64      `json:"orderTypeId"`
	RefId             *int64     `json:"refId"`
	OrderNo           string     `json:"orderNo"`
	Amount            float64    `json:"amount"`
	TransactionNo     *string    `json:"transactionNo"`
	TransactionDate   *time.Time `json:"transactionDate"`
	TransactionStatus *string    `json:"transactionStatus"`
	PaymentAt         *time.Time `json:"paymentAt"`
	QrPromptpay       *string    `json:"qrPromptpay"`
	PaymentPageUrl    string     `json:"paymentPageUrl"`
	Remark            *string    `json:"remark"`
	ApiRemark         *string    `json:"apiRemark"`
	CreatedAt         time.Time  `json:"createdAt"`
	UpdatedAt         *time.Time `json:"updatedAt"`
}
type PompayOrderListRequest struct {
	UserId        *int64   `form:"userId"`
	OrderTypeId   *int64   `form:"orderTypeId"`
	OrderNo       string   `form:"orderNo"`
	TransactionNo string   `form:"transactionNo"`
	Amount        *float64 `form:"amount"`
	Status        string   `form:"status"`
	Page          int      `form:"page" default:"1" min:"1"`
	Limit         int      `form:"limit" default:"10" min:"1" max:"100"`
	SortCol       string   `form:"sortCol"`
	SortAsc       string   `form:"sortAsc"`
}
type PompayOrderResponse struct {
	Id                    int64      `json:"id"`
	UserId                int64      `json:"userId"`
	OrderTypeId           int64      `json:"orderTypeId"`
	OrderTypeName         string     `json:"orderTypeName"`
	RefId                 *int64     `json:"refId"`
	OrderNo               string     `json:"orderNo"`
	Amount                float64    `json:"amount"`
	TransactionNo         *string    `json:"transactionNo"`
	TransactionDate       *time.Time `json:"transactionDate"`
	TransactionStatus     *string    `json:"transactionStatus"`
	PaymentAt             *time.Time `json:"paymentAt"`
	BankTransactionId     *string    `json:"bankTransactionId"`
	BankTransactionStatus string     `json:"bankTransactionStatus"`
	QrPromptpay           string     `json:"qrPromptpay"`
	PaymentPageUrl        string     `json:"paymentPageUrl"`
	Remark                *string    `json:"remark"`
	ApiRemark             *string    `json:"apiRemark"`
	CreatedAt             time.Time  `json:"createdAt"`
	UpdatedAt             *time.Time `json:"updatedAt"`
}
type PompayOrderWebResponse struct {
	UserId            int64     `json:"userId"`
	OrderNo           string    `json:"orderNo"`
	Amount            float64   `json:"amount"`
	TransferAmount    float64   `json:"transferAmount"`
	TransactionStatus string    `json:"transactionStatus"`
	QrCode            string    `json:"qrCode"`
	QrBase64          string    `json:"qrBase64"`
	PaymentPageUrl    string    `json:"paymentPageUrl"`
	ManualBankAccount string    `json:"manualBankAccount"`
	ManualBankNumber  string    `json:"manualBankNumber"`
	ManualBankCode    string    `json:"manualBankCode"`
	ManualBankName    string    `json:"manualBankName"`
	ManualAmount      string    `json:"manualAmount"`
	CreatedAt         time.Time `json:"createdAt"`
}
type PompayOrderQrResponse struct {
	Id       int64   `json:"id"`
	OrderNo  string  `json:"orderNo"`
	Amount   float64 `json:"amount"`
	QrBase64 string  `json:"qrBase64"`
}
type PompayOrderCreateRequest struct {
	UserId *int64  `form:"userId"`
	Amount float64 `form:"amount"`
}
type PompayOrderCreateBody struct {
	Id                int64   `json:"id"`
	UserId            int64   `json:"userId"`
	OrderTypeId       int64   `json:"orderTypeId"`
	RefId             *int64  `json:"refId"`
	OrderNo           string  `json:"orderNo"`
	Amount            float64 `json:"amount"`
	TransactionStatus string  `json:"transactionStatus"`
}
type PompayOrderUpdateBody struct {
	TransactionNo     string    `json:"transactionNo"`
	TransactionDate   time.Time `json:"transactionDate"`
	TransactionStatus string    `json:"transactionStatus"`
	TransferAmount    float64   `json:"transferAmount"`
	QrPromptpay       string    `json:"qrPromptpay"`
	PaymentPageUrl    string    `json:"paymentPageUrl"`
}
