package model

import (
	"time"

	"gorm.io/gorm"
)

const (
	PAYGATE_MERCHANT_ID_HENG         = 1
	PAYGATE_MERCHANT_TYPE_HENG       = "HENG"
	PAYGATE_MERCHANT_ID_LUCKYTHAI    = 2
	PAYGATE_MERCHANT_TYPE_LUCKYTHAI  = "LUCKYTH"
	PAYGATE_MERCHANT_ID_PAPAYAPAY    = 3
	PAYGATE_MERCHANT_TYPE_PAPAYAPAY  = "PAPAYAPAY"
	PAYGATE_MERCHANT_ID_PAYONEX      = 4
	PAYGATE_MERCHANT_TYPE_PAYONEX    = "PAYONEX"
	PAYGATE_MERCHANT_ID_JBPAY        = 5
	PAYGATE_MERCHANT_TYPE_JBPAY      = "JBPAY"
	PAYGATE_MERCHANT_ID_POMPAY       = 6
	PAYGATE_MERCHANT_TYPE_POMPAY     = "POMPAY"
	PAYGATE_MERCHANT_ID_PAYMENTCO    = 7
	PAYGATE_MERCHANT_TYPE_PAYMENTCO  = "PAYMENTCO"
	PAYGATE_MERCHANT_ID_ZAPPAY       = 8
	PAYGATE_MERCHANT_TYPE_ZAPPAY     = "ZAPPAY"
	PAYGATE_MERCHANT_ID_ONEPAY       = 9
	PAYGATE_MERCHANT_TYPE_ONEPAY     = "ONEPAY"
	PAYGATE_MERCHANT_ID_FLASHPAY     = 10
	PAYGATE_MERCHANT_TYPE_FLASHPAY   = "FLASHPAY"
	PAYGATE_MERCHANT_ID_BIZPAY       = 11
	PAYGATE_MERCHANT_TYPE_BIZPAY     = "BIZPAY"
	PAYGATE_MERCHANT_ID_SUGARPAY     = 12
	PAYGATE_MERCHANT_TYPE_SUGARPAY   = "SUGARPAY"
	PAYGATE_MERCHANT_ID_ZMANPAY      = 13
	PAYGATE_MERCHANT_TYPE_ZMANPAY    = "ZMANPAY"
	PAYGATE_MERCHANT_ID_POSTMANPAY   = 14
	PAYGATE_MERCHANT_TYPE_POSTMANPAY = "POSTMANPAY"
	PAYGATE_MERCHANT_ID_MAZEPAY      = 15
	PAYGATE_MERCHANT_TYPE_MAZEPAY    = "MAZEPAY"
	PAYGATE_MERCHANT_ID_MEEPAY       = 16
	PAYGATE_MERCHANT_TYPE_MEEPAY     = "MEEPAY"
)

const (
	PAYONEX_DEPOSIT_AMOUNT_MINIMUM    = 20
	PAYONEX_DEPOSIT_AMOUNT_MAXIMUM    = 200000
	PAYONEX_WITHDRAW_AMOUNT_MINIMUM   = 100
	PAYONEX_WITHDRAW_AMOUNT_MAXIMUM   = 2000000
	JBPAY_DEPOSIT_AMOUNT_MINIMUM      = 50
	JBPAY_DEPOSIT_AMOUNT_MAXIMUM      = 200000
	JBPAY_WITHDRAW_AMOUNT_MINIMUM     = 100
	JBPAY_WITHDRAW_AMOUNT_MAXIMUM     = 200000
	POMPAY_DEPOSIT_AMOUNT_MINIMUM     = 20
	POMPAY_DEPOSIT_AMOUNT_MAXIMUM     = 200000
	POMPAY_WITHDRAW_AMOUNT_MINIMUM    = 100
	POMPAY_WITHDRAW_AMOUNT_MAXIMUM    = 200000
	ZAPPAY_DEPOSIT_AMOUNT_MINIMUM     = 50
	ZAPPAY_DEPOSIT_AMOUNT_MAXIMUM     = 200000
	ZAPPAY_WITHDRAW_AMOUNT_MINIMUM    = 100
	ZAPPAY_WITHDRAW_AMOUNT_MAXIMUM    = 2000000
	PAYMENTCO_DEPOSIT_AMOUNT_MINIMUM  = 20
	PAYMENTCO_DEPOSIT_AMOUNT_MAXIMUM  = 200000
	PAYMENTCO_WITHDRAW_AMOUNT_MINIMUM = 100
	PAYMENTCO_WITHDRAW_AMOUNT_MAXIMUM = 2000000
)

type PaygateCustomerDepositAccount struct {
	MerchantId  int64  `json:"merchantId"`
	Name        string `json:"name"`
	DisplayName string `json:"displayName"`
	ShopName    string `json:"shopName"`
	MinAmount   int64  `json:"minAmount"`
	MaxAmount   int64  `json:"maxAmount"`
}

type PaygateMerchant struct {
	Id                int64          `json:"id"`
	Name              string         `json:"name"`
	DisplayName       string         `json:"displayName"`
	TypeName          string         `json:"typeName"`
	HasDeposit        bool           `json:"hasDeposit"`
	IsDepositEnabled  bool           `json:"isDepositEnabled"`
	HasWithdraw       bool           `json:"hasWithdraw"`
	IsWithdrawEnabled bool           `json:"isWithdrawEnabled"`
	ApiEndPoint       string         `json:"apiEndPoint"`
	ShopName          string         `json:"shopName"`
	Username          string         `json:"username"`
	Password          string         `json:"password"`
	PrivateKey        string         `json:"privateKey"`
	AccessKey         string         `json:"accessKey"`
	SecretKey         string         `json:"secretKey"`
	PartnerKey        string         `json:"partnerKey"`
	RepayAppId        string         `json:"repayAppId"`
	LoanAppId         string         `json:"loanAppId"`
	MerchantId        string         `json:"merchantId"`
	Token             string         `json:"token"`
	AesKey            string         `json:"aesKey"`
	Balance           float64        `json:"balance"`
	MerchantUpdateAt  *time.Time     `json:"merchantUpdateAt"`
	CallbackUrl       string         `json:"callbackUrl"`
	CreatedAt         time.Time      `json:"createdAt"`
	UpdatedAt         *time.Time     `json:"updatedAt"`
	DeletedAt         gorm.DeletedAt `json:"deletedAt"`
}
type PaygateMerchantListRequest struct {
	Page    int    `form:"page" default:"1" min:"1"`
	Limit   int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol string `form:"sortCol"`
	SortAsc string `form:"sortAsc"`
}
type PaygateMerchantCreateRequest struct {
	Name                   string  `form:"name" binding:"required"`
	DisplayName            string  `form:"displayName"`
	TypeName               string  `form:"typeName" binding:"required"`
	HasDeposit             bool    `form:"hasDeposit" binding:"required"`
	IsDepositEnabled       bool    `form:"isDepositEnabled"`
	HasWithdraw            bool    `form:"hasWithdraw" binding:"required"`
	IsWithdrawEnabled      bool    `form:"isWithdrawEnabled"`
	ApiEndPoint            string  `form:"apiEndPoint" binding:"required"`
	ShopName               string  `form:"shopName"`
	Username               string  `form:"username"`
	Password               string  `form:"password"`
	PrivateKey             string  `form:"privateKey"`
	AccessKey              string  `form:"accessKey"`
	SecretKey              string  `form:"secretKey"`
	PartnerKey             string  `form:"partnerKey"`
	RepayAppId             string  `form:"repayAppId"`
	LoanAppId              string  `form:"loanAppId"`
	MerchantId             string  `form:"merchantId"`
	Token                  string  `form:"token"`
	AesKey                 string  `form:"aesKey"`
	PaymentWithdrawMinimum float64 `form:"paymentWithdrawMinimum"`
	PaymentWithdrawMaximum float64 `form:"paymentWithdrawMaximum"`
	PaymentDepositMinimum  float64 `form:"paymentDepositMinimum"`
	PaymentDepositMaximum  float64 `form:"paymentDepositMaximum"`
}
type PaygateMerchantCreateBody struct {
	Id                     int64   `json:"id"`
	Name                   string  `json:"name"`
	DisplayName            string  `json:"displayName"`
	TypeName               string  `json:"typeName"`
	HasDeposit             bool    `json:"hasDeposit"`
	IsDepositEnabled       bool    `json:"isDepositEnabled"`
	HasWithdraw            bool    `json:"hasWithdraw"`
	IsWithdrawEnabled      bool    `json:"isWithdrawEnabled"`
	ApiEndPoint            string  `json:"apiEndPoint"`
	ShopName               string  `json:"shopName"`
	Username               string  `json:"username"`
	Password               string  `json:"password"`
	PrivateKey             string  `json:"privateKey"`
	AccessKey              string  `json:"accessKey"`
	SecretKey              string  `json:"secretKey"`
	PartnerKey             string  `json:"partnerKey"`
	RepayAppId             string  `json:"repayAppId"`
	LoanAppId              string  `json:"loanAppId"`
	MerchantId             string  `json:"merchantId"`
	Token                  string  `json:"token"`
	AesKey                 string  `json:"aesKey"`
	PaymentWithdrawMinimum float64 `json:"paymentWithdrawMinimum"`
	PaymentWithdrawMaximum float64 `json:"paymentWithdrawMaximum"`
	PaymentDepositMinimum  float64 `json:"paymentDepositMinimum"`
	PaymentDepositMaximum  float64 `json:"paymentDepositMaximum"`
}
type PaygateMerchantUpdateRequest struct {
	Name                   *string  `form:"name"`
	DisplayName            *string  `form:"displayName"`
	IsDepositEnabled       *bool    `form:"isDepositEnabled"`
	IsWithdrawEnabled      *bool    `form:"isWithdrawEnabled"`
	ApiEndPoint            *string  `form:"apiEndPoint"`
	ShopName               *string  `form:"shopName"`
	Username               *string  `form:"username"`
	Password               *string  `form:"password"`
	PrivateKey             *string  `form:"privateKey"`
	AccessKey              *string  `form:"accessKey"`
	SecretKey              *string  `form:"secretKey"`
	PartnerKey             *string  `form:"partnerKey"`
	RepayAppId             *string  `form:"repayAppId"`
	LoanAppId              *string  `form:"loanAppId"`
	MerchantId             *string  `form:"merchantId"`
	Token                  *string  `form:"token"`
	AesKey                 *string  `form:"aesKey"`
	PaymentWithdrawMinimum *float64 `form:"paymentWithdrawMinimum"`
	PaymentWithdrawMaximum *float64 `form:"paymentWithdrawMaximum"`
	PaymentDepositMinimum  *float64 `form:"paymentDepositMinimum"`
	PaymentDepositMaximum  *float64 `form:"paymentDepositMaximum"`
}
type PaygateMerchantUpdateBody struct {
	Name                   *string    `json:"name"`
	DisplayName            *string    `json:"displayName"`
	IsDepositEnabled       *bool      `json:"isDepositEnabled"`
	IsWithdrawEnabled      *bool      `json:"isWithdrawEnabled"`
	ApiEndPoint            *string    `json:"apiEndPoint"`
	ShopName               *string    `json:"shopName"`
	Username               *string    `json:"username"`
	Password               *string    `json:"password"`
	PrivateKey             *string    `json:"privateKey"`
	AccessKey              *string    `json:"accessKey"`
	SecretKey              *string    `json:"secretKey"`
	PartnerKey             *string    `form:"partnerKey"`
	RepayAppId             *string    `json:"repayAppId"`
	LoanAppId              *string    `json:"loanAppId"`
	MerchantId             *string    `form:"merchantId"`
	Token                  *string    `form:"token"`
	AesKey                 *string    `form:"aesKey"`
	Balance                *float64   `json:"balance"`
	MerchantUpdateAt       *time.Time `json:"merchantUpdateAt"`
	CallbackUrl            *string    `json:"callbackUrl"`
	PaymentWithdrawMinimum *float64   `json:"paymentWithdrawMinimum"`
	PaymentWithdrawMaximum *float64   `json:"paymentWithdrawMaximum"`
	PaymentDepositMinimum  *float64   `json:"paymentDepositMinimum"`
	PaymentDepositMaximum  *float64   `json:"paymentDepositMaximum"`
}
type PaygateMerchantCallbackUrl struct {
	Label string `json:"label"`
	Url   string `json:"url"`
}
type PaygateMerchantResponse struct {
	Id                     int64                        `json:"id"`
	Name                   string                       `json:"name"`
	DisplayName            string                       `json:"displayName"`
	TypeName               string                       `json:"typeName"`
	HasDeposit             bool                         `json:"hasDeposit"`
	IsDepositEnabled       bool                         `json:"isDepositEnabled"`
	HasWithdraw            bool                         `json:"hasWithdraw"`
	IsWithdrawEnabled      bool                         `json:"isWithdrawEnabled"`
	ApiEndPoint            string                       `json:"apiEndPoint"`
	ShopName               string                       `json:"shopName"`
	Username               string                       `json:"username"`
	Password               string                       `json:"password"`
	PrivateKey             string                       `json:"privateKey"`
	AccessKey              string                       `json:"accessKey"`
	SecretKey              string                       `json:"secretKey"`
	PartnerKey             string                       `json:"partnerKey"`
	RepayAppId             string                       `json:"repayAppId"`
	LoanAppId              string                       `json:"loanAppId"`
	MerchantId             string                       `json:"merchantId"`
	Token                  string                       `json:"token"`
	AesKey                 string                       `json:"aesKey"`
	Balance                float64                      `json:"balance"`
	MerchantUpdateAt       *time.Time                   `json:"merchantUpdateAt"`
	CallbackUrl            string                       `json:"callbackUrl"`
	PaymentWithdrawMinimum float64                      `json:"paymentWithdrawMinimum"`
	PaymentWithdrawMaximum float64                      `json:"paymentWithdrawMaximum"`
	PaymentDepositMinimum  float64                      `json:"paymentDepositMinimum"`
	PaymentDepositMaximum  float64                      `json:"paymentDepositMaximum"`
	CreatedAt              time.Time                    `json:"createdAt"`
	UpdatedAt              *time.Time                   `json:"updatedAt"`
	CallBackList           []PaygateMerchantCallbackUrl `json:"callBackList" gorm:"-"`
}
type PaygateMerchantLimitResponse struct {
	Id                     int64                        `json:"id"`
	Name                   string                       `json:"name"`
	HasDeposit             bool                         `json:"hasDeposit"`
	HasWithdraw            bool                         `json:"hasWithdraw"`
	PaymentWithdrawMinimum float64                      `json:"paymentWithdrawMinimum"`
	PaymentWithdrawMaximum float64                      `json:"paymentWithdrawMaximum"`
	PaymentDepositMinimum  float64                      `json:"paymentDepositMinimum"`
	PaymentDepositMaximum  float64                      `json:"paymentDepositMaximum"`
	CallBackList           []PaygateMerchantCallbackUrl `json:"callBackList" gorm:"-"`
}

type PaygateSetting struct {
	Id         int64      `json:"id"`
	MerchantId int64      `json:"merchantId"`
	Status     string     `json:"status"`
	CreatedAt  time.Time  `json:"createdAt"`
	UpdatedAt  *time.Time `json:"updatedAt"`
}
type PaygateSettingCreateBody struct {
	Id         int64  `json:"id"`
	MerchantId int64  `json:"merchantId"`
	Status     string `json:"status"`
}
type PaygateSettingUpdateRequest struct {
	MerchantId *int64 `form:"merchantId" validate:"required"`
	AdminId    *int64 `form:"adminId" json:"-"`
}
type PaygateSettingUpdateBody struct {
	MerchantId *int64  `json:"merchantId"`
	Status     *string `json:"status"`
}
type PaygateSettingResponse struct {
	Id                 int64      `json:"id"`
	MerchantId         int64      `json:"merchantId"`
	MerchantName       string     `json:"merchantName"`
	MerchantType       string     `json:"merchantType"`
	CallbackUrl        string     `json:"callbackUrl" gorm:"-"`
	FundInCallbackUrl  string     `json:"fundInCallbackUrl" gorm:"-"`
	FundOutCallbackUrl string     `json:"fundOutCallbackUrl" gorm:"-"`
	Status             string     `json:"status"`
	UpdateAt           *time.Time `json:"updateAt"`
}

type PaygateAccountResponse struct {
	Id                     int64                        `json:"id"`
	ProviderId             int64                        `json:"providerId"`
	Name                   string                       `json:"name"`
	DisplayName            string                       `json:"displayName"`
	TypeName               string                       `json:"typeName"`
	HasDeposit             bool                         `json:"hasDeposit"`
	IsDepositEnabled       bool                         `json:"isDepositEnabled"`
	HasWithdraw            bool                         `json:"hasWithdraw"`
	IsWithdrawEnabled      bool                         `json:"isWithdrawEnabled"`
	ApiEndPoint            string                       `json:"apiEndPoint"`
	ShopName               string                       `json:"shopName"`
	Username               string                       `json:"username"`
	Password               string                       `json:"password"`
	PrivateKey             string                       `json:"privateKey"`
	AccessKey              string                       `json:"accessKey"`
	SecretKey              string                       `json:"secretKey"`
	PartnerKey             string                       `json:"partnerKey"`
	RepayAppId             string                       `json:"repayAppId"`
	LoanAppId              string                       `json:"loanAppId"`
	MerchantId             string                       `json:"merchantId"`
	Token                  string                       `json:"token"`
	AesKey                 string                       `json:"aesKey"`
	Balance                float64                      `json:"balance"`
	MerchantUpdateAt       *time.Time                   `json:"merchantUpdateAt"`
	CallbackUrl            string                       `json:"callbackUrl"`
	PaymentWithdrawMinimum float64                      `json:"paymentWithdrawMinimum"`
	PaymentWithdrawMaximum float64                      `json:"paymentWithdrawMaximum"`
	PaymentDepositMinimum  float64                      `json:"paymentDepositMinimum"`
	PaymentDepositMaximum  float64                      `json:"paymentDepositMaximum"`
	CreatedAt              time.Time                    `json:"createdAt"`
	UpdatedAt              *time.Time                   `json:"updatedAt"`
	CallBackList           []PaygateMerchantCallbackUrl `json:"callBackList" gorm:"-"`
}

type PaygateAdminLog struct {
	Id           int64      `json:"id"`
	AdminId      int64      `json:"adminId"`
	Name         string     `json:"name"`
	Status       string     `json:"status"`
	JsonReq      string     `json:"jsonReq"`
	JsonResponse string     `json:"jsonResponse"`
	CreatedAt    time.Time  `json:"createdAt"`
	UpdatedAt    *time.Time `json:"updatedAt"`
}
type PaygateAdminLogListRequest struct {
	Page    int    `form:"page" default:"1" min:"1"`
	Limit   int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol string `form:"sortCol"`
	SortAsc string `form:"sortAsc"`
}
type PaygateAdminLogCreateBody struct {
	Id           int64  `json:"id"`
	AdminId      int64  `json:"adminId"`
	Name         string `json:"name"`
	Status       string `json:"status"`
	JsonReq      string `json:"jsonReq"`
	JsonResponse string `json:"jsonResponse"`
}
type PaygateAdminLogUpdateBody struct {
	Status       string `json:"status"`
	JsonResponse string `json:"jsonResponse"`
}
type PaygateAdminLogResponse struct {
	Id           int64      `json:"id"`
	AdminId      int64      `json:"adminId"`
	Name         string     `json:"name"`
	Status       string     `json:"status"`
	JsonReq      string     `json:"jsonReq"`
	JsonResponse string     `json:"jsonResponse"`
	CreatedAt    time.Time  `json:"createdAt"`
	UpdatedAt    *time.Time `json:"updatedAt"`
}
type PaygateSystemLogCreateBody struct {
	Id           int64  `json:"id"`
	Name         string `json:"name"`
	Status       string `json:"status"`
	JsonReq      string `json:"jsonReq"`
	JsonResponse string `json:"jsonResponse"`
}
type PaygateSystemLogUpdateBody struct {
	Status       *string `json:"status"`
	JsonResponse *string `json:"jsonResponse"`
}

type PaygateHengCheckRemoteResponse struct {
	ResponseCode string `json:"responseCode"`
	ResponseMesg string `json:"responseMesg"`
}

type PaygateHengLoginRemoteResponse struct {
	Id           string    `json:"_id"`
	UserRole     string    `json:"user_role"`
	UserStatus   string    `json:"user_status"`
	UserSecrete  string    `json:"user_secrete"`
	CallbackUrl  string    `json:"callback_url"`
	BasicAuth    string    `json:"basicAuth"`
	UserName     string    `json:"user_name"`
	ProviderId   string    `json:"provider_id"`
	CreatedAt    time.Time `json:"createdAt"`
	TokenExpires time.Time `json:"tokenExpires"`
	AccessToken  string    `json:"access_token"`
}

type PaygateHengBalanceRequest struct {
	AdminId int64 `uri:"adminId" binding:"required"`
}
type PaygateHengBalanceRemoteResponse struct {
	ResponseCode string `json:"responseCode"`
	ResponseMesg string `json:"responseMesg"`
	Data         struct {
		TotalBalance  float64 `json:"total_balance"`
		TotalDeposit  float64 `json:"total_deposit"`
		TotalWithdraw float64 `json:"total_withdraw"`
		UserName      string  `json:"user_name"`
	} `json:"data"`
}
type PaygateHengBalanceResponse struct {
	TotalBalance  float64 `json:"totalBalance"`
	TotalDeposit  float64 `json:"totalDeposit"`
	TotalWithdraw float64 `json:"totalWithdraw"`
}

type PaygateHengToken struct {
	Id          int64      `json:"id"`
	AccessToken string     `json:"accessToken"`
	ExpireAt    time.Time  `json:"expireAt"`
	CreateBy    int64      `json:"createBy"`
	CreatedAt   time.Time  `json:"createdAt"`
	UpdatedAt   *time.Time `json:"updatedAt"`
}
type PaygateHengTokenCreateBody struct {
	Id          int64     `json:"id"`
	AccessToken string    `json:"accessToken"`
	ExpireAt    time.Time `json:"expireAt"`
	CreateBy    int64     `json:"createBy"`
}

type PaygateHengOrderCreateRemoteRequest struct {
	Amount      float64 `form:"amount" json:"amount"`
	ReferenceNo string  `form:"referenceNo" json:"referenceNo"`
	ShopName    string  `form:"shopName" json:"ShopName"`
}
type PaygateHengOrderCreateRemoteResponse struct {
	TransactionId    string    `json:"TransactionID"`
	TransmitDateTime time.Time `json:"TransmitDateTime"`
	ProviderId       string    `json:"ProviderID"`
	Ref2             string    `json:"Ref2"`
	AmountPaid       string    `json:"AmountPaid"`
	QRPromptpay      string    `json:"QRPromptpay"`
	Status           struct {
		Code    string `json:"Code"`
		Message string `json:"Message"`
	} `json:"Status"`
}

type PaygateHengOrder struct {
	Id                    int64      `json:"id"`
	UserId                int64      `json:"userId"`
	OrderNo               string     `json:"orderNo"`
	ShopName              string     `json:"shopName"`
	Amount                float64    `json:"amount"`
	TransactionNo         *string    `json:"transactionNo"`
	TransactionDate       *time.Time `json:"transactionDate"`
	TransactionStatus     *string    `json:"transactionStatus"`
	PaymentAt             *time.Time `json:"paymentAt"`
	PaymentBankNo         string     `json:"paymentBankNo"`
	PaymentName           string     `json:"paymentName"`
	PaymentBankCode       string     `json:"paymentBankCode"`
	PaymentBankName       string     `json:"paymentBankName"`
	QrPromptpay           *string    `json:"qrPromptpay"`
	BankTransactionId     *int64     `json:"bankTransactionId"`
	BankTransactionStatus *string    `json:"bankTransactionStatus"`
	CreatedAt             time.Time  `json:"createdAt"`
	UpdatedAt             *time.Time `json:"updatedAt"`
}
type PaygateHengOrderListRequest struct {
	UserId        *int64 `form:"userId"`
	OrderNo       string `form:"orderNo"`
	TransactionNo string `form:"transactionNo"`
	Amount        string `form:"amount"`
	Status        string `form:"status"`
	Page          int    `form:"page" default:"1" min:"1"`
	Limit         int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol       string `form:"sortCol"`
	SortAsc       string `form:"sortAsc"`
}
type PaygateHengOrderResponse struct {
	Id                int64      `json:"id"`
	UserId            int64      `json:"userId"`
	OrderNo           string     `json:"orderNo"`
	ShopName          string     `json:"shopName"`
	Amount            float64    `json:"amount"`
	TransactionNo     *string    `json:"transactionNo"`
	TransactionDate   *time.Time `json:"transactionDate"`
	TransactionStatus *string    `json:"transactionStatus"`
	PaymentAt         *time.Time `json:"paymentAt"`
	QrPromptpay       string     `json:"qrPromptpay"`
	BankTransactionId *int64     `json:"bankTransactionId"`
	CreatedAt         time.Time  `json:"createdAt"`
	UpdatedAt         *time.Time `json:"updatedAt"`
}
type PaygateHengOrderQrResponse struct {
	Id       int64   `json:"id"`
	OrderNo  string  `json:"orderNo"`
	Amount   float64 `json:"amount"`
	QrBase64 string  `json:"qrBase64"`
}
type PaygateHengOrderCreateRequest struct {
	UserId *int64  `form:"userId"`
	Amount float64 `form:"amount"`
}
type PaygateHengOrderCreateBody struct {
	Id                int64   `json:"id"`
	UserId            int64   `json:"userId"`
	OrderNo           string  `json:"orderNo"`
	ShopName          string  `json:"shopName"`
	Amount            float64 `json:"amount"`
	TransactionStatus string  `json:"transactionStatus"`
}
type PaygateHengOrderUpdateBody struct {
	TransactionNo     string    `json:"transactionNo"`
	TransactionDate   time.Time `json:"transactionDate"`
	TransactionStatus string    `json:"transactionStatus"`
	QrPromptpay       string    `json:"qrPromptpay"`
}
type PaygateHengOrderUpdatePaymentBody struct {
	BankTransactionId int64  `json:"bankTransactionId"`
	PaymentBankNo     string `json:"paymentBankNo"`
	PaymentName       string `json:"paymentName"`
	PaymentBankCode   string `json:"paymentBankCode"`
	PaymentBankName   string `json:"paymentBankName"`
}

type PaygateHengWebhook struct {
	Id          int64     `json:"id"`
	Name        string    `json:"name"`
	JsonPayload string    `json:"jsonPayload"`
	CreatedAt   time.Time `json:"createdAt"`
}
type PaygateHengWebhookRequest struct {
	JsonPayload string `json:"jsonPayload"`
}
type PaygateHengWebhookCreateBody struct {
	Id          int64  `json:"id"`
	Name        string `json:"name"`
	JsonPayload string `json:"jsonPayload"`
}

type PaygateHengWebhookResponse struct {
	ResponseCode string `json:"responseCode"`
	ResponseMesg string `json:"responseMesg"`
	Data         struct {
		Amount     string `json:"amount"`
		Reference1 string `json:"reference1"`
		Reference2 string `json:"reference2"`
		FromBank   string `json:"fromBank"`
		FromName   string `json:"fromName"`
		ShortName  string `json:"shortName"`
		BankName   string `json:"bankName"`
	} `json:"data"`
}

type PaygateHengSetting struct {
	Id          int64      `json:"id"`
	Name        string     `json:"name"`
	IsEnabled   bool       `json:"isEnabled"`
	ApiEndPoint string     `json:"apiEndPoint"`
	ShopName    string     `json:"shopName"`
	Username    string     `json:"username"`
	Password    string     `json:"password"`
	Balance     float64    `json:"balance"`
	CallbackUrl string     `json:"callbackUrl"`
	CreatedAt   time.Time  `json:"createdAt"`
	UpdatedAt   *time.Time `json:"updatedAt"`
}
type PaygateHengSettingCreateBody struct {
	Id          int64  `json:"id"`
	Name        string `json:"name"`
	IsEnabled   bool   `json:"isEnabled"`
	ApiEndPoint string `json:"apiEndPoint"`
	ShopName    string `json:"shopName"`
	Username    string `json:"username"`
	Password    string `json:"password"`
	CallbackUrl string `json:"callbackUrl"`
}
type PaygateHengSettingUpdateRequest struct {
	Name        *string `form:"name"`
	IsEnabled   *bool   `form:"isEnabled"`
	ApiEndPoint *string `form:"apiEndPoint"`
	ShopName    *string `form:"shopName"`
	Username    *string `form:"username"`
	Password    *string `form:"password"`
	CallbackUrl *string `form:"callbackUrl"`
}
type PaygateHengSettingUpdateBody struct {
	Name        *string  `json:"name"`
	IsEnabled   *bool    `json:"isEnabled"`
	ApiEndPoint *string  `json:"apiEndPoint"`
	ShopName    *string  `json:"shopName"`
	Username    *string  `json:"username"`
	Password    *string  `json:"password"`
	Balance     *float64 `json:"balance"`
	CallbackUrl *string  `json:"callbackUrl"`
}
type PaygateHengSettingResponse struct {
	Id             int64      `json:"id"`
	Name           string     `json:"name"`
	DisplayName    string     `json:"displayName"`
	IsEnabled      bool       `json:"isEnabled"`
	ApiEndPoint    string     `json:"apiEndPoint"`
	ShopName       string     `json:"shopName"`
	Username       string     `json:"username"`
	Password       string     `json:"password"`
	Balance        float64    `json:"balance"`
	CallbackUrl    string     `json:"callbackUrl"`
	CreatedAt      time.Time  `json:"createdAt"`
	UpdatedAt      *time.Time `json:"updatedAt"`
	CacheExpiredAt time.Time  `json:"cacheExpiredAt"`
}
type PaygateHengCustomerDepositInfo struct {
	Name        string `json:"name"`
	DisplayName string `json:"displayName"`
	ShopName    string `json:"shopName"`
}

type PaygateHengDepositCreateRequest struct {
	UserId int64   `form:"userId" json:"userId" binding:"required" validate:"required"`
	Amount float64 `form:"amount" json:"amount" binding:"required,min=1" validate:"required,min=1"`
}
type PaygateHengDepositTestCheckName struct {
	UserId        int64  `form:"userId" json:"userId" binding:"required" validate:"required"`
	StatementName string `form:"statementName" json:"statementName" binding:"required" validate:"required"`
}

type LuckyThaiErrorRemoteResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Code    int    `json:"code"`
}
type LuckyThaiDepositCreateRemoteRequest struct {
	ReferenceNo string  `form:"referenceNo" json:"-"`
	Amount      float64 `form:"amount" json:"amount"`
}
type LuckyThaiDepositCreateRemoteResponse struct {
	Success bool `json:"success"`
	Code    int  `json:"code"`
	Data    struct {
		OrderNo       string  `json:"orderNo"`
		ReceiveAddr   string  `json:"receiveAddr"`
		ChainName     string  `json:"chainName"`
		CoinUnit      string  `json:"coinUnit"`
		RequestAmount float64 `json:"requestAmount"`
		Status        string  `json:"status"`
		PayUrl        string  `json:"payUrl"`
		Hrefbackurl   string  `json:"hrefbackurl"`
		Sign          string  `json:"sign"`
	} `json:"data"`
}

type LuckyThaiCheckBalanceRemoteResponse struct {
	Success bool `json:"success"`
	Code    int  `json:"code"`
	Data    struct {
		InBalance struct {
			CoinChain string  `json:"coinChain"`
			CoinUnit  string  `json:"coinUnit"`
			Balance   float64 `json:"balance"`
			Freeze    float64 `json:"freeze"`
		} `json:"inBalance"`
		OutBalance struct {
			CoinChain string  `json:"coinChain"`
			CoinUnit  string  `json:"coinUnit"`
			Balance   float64 `json:"balance"`
			Freeze    float64 `json:"freeze"`
		} `json:"outBalance"`
		Sign             string `json:"sign"`
		CheckRequestTime int64  `json:"checkRequestTime"`
		DfBalance        struct {
			CoinChain string  `json:"coinChain"`
			CoinUnit  string  `json:"coinUnit"`
			Balance   float64 `json:"balance"`
			Freeze    float64 `json:"freeze"`
		} `json:"dfBalance"`
		DsBalance struct {
			CoinChain string  `json:"coinChain"`
			CoinUnit  string  `json:"coinUnit"`
			Balance   float64 `json:"balance"`
			Freeze    float64 `json:"freeze"`
		} `json:"dsBalance"`
	} `json:"data"`
}

type LuckyThaiGetOrderRemoteResponse struct {
	Success bool `json:"success"`
	Code    int  `json:"code"`
	Data    struct {
		OrderNo       string  `json:"orderNo"`
		ReceiveAddr   string  `json:"receiveAddr"`
		ChainName     string  `json:"chainName"`
		CoinUnit      string  `json:"coinUnit"`
		RequestAmount float64 `json:"requestAmount"`
		PayAmount     float64 `json:"payAmount"`
		Status        string  `json:"status"`
		Sign          string  `json:"sign"`
	} `json:"data"`
}

type LuckyThaiWithdrawCreateRemoteRequest struct {
	ReferenceNo string  `form:"referenceNo" json:"-"`
	BankCode    string  `form:"bankCode" json:"bankCode"`
	AccountNo   string  `form:"accountNo" json:"accountNo"`
	Accountname string  `form:"accountname" json:"accountname"`
	Amount      float64 `form:"amount" json:"amount"`
}
type LuckyThaiWithdrawCreateRemoteResponse struct {
	Success bool `json:"success"`
	Code    int  `json:"code"`
	Data    struct {
		OrderNo       string  `json:"orderNo"`
		ReceiveAddr   string  `json:"receiveAddr"`
		ChainName     string  `json:"chainName"`
		CoinUnit      string  `json:"coinUnit"`
		RequestAmount float64 `json:"requestAmount"`
		Status        string  `json:"status"`
		PayUrl        string  `json:"payUrl"`
		Hrefbackurl   string  `json:"hrefbackurl"`
		Sign          string  `json:"sign"`
	} `json:"data"`
}

type LuckyThaiWebhookRequest struct {
	JsonPayload string `json:"jsonPayload"`
}
type LuckyThaiWebhookCreateBody struct {
	Id          int64  `json:"id"`
	Name        string `json:"name"`
	JsonPayload string `json:"jsonPayload"`
}

type LuckyThaiWebhookResponse struct {
	ChainName  string  `json:"chainName"`
	OrderNo    string  `json:"orderNo"`
	PayAmount  float64 `json:"payAmount"`
	ClientCode string  `json:"clientCode"`
	Sign       string  `json:"sign"`
	ClientNo   string  `json:"clientNo"`
	Txid       string  `json:"txid"`
	Payer      string  `json:"payer"`
	CoinUnit   string  `json:"coinUnit"`
	Status     string  `json:"status"`
}

type LuckyThaiCustomerDepositInfo struct {
	Name      string `json:"name"`
	ShopName  string `json:"shopName"`
	MinAmount int64  `json:"minAmount"`
	MaxAmount int64  `json:"maxAmount"`
}
type LuckyThaiDepositCreateRequest struct {
	UserId int64   `form:"userId" json:"-" validate:"required"`
	Amount float64 `form:"amount" json:"amount" binding:"required,min=1" validate:"required,min=1"`
}
type LuckyThaiWithdrawCreateRequest struct {
	RefId  int64   `form:"refId" validate:"required"`
	UserId int64   `form:"userId" json:"-" validate:"required"`
	Amount float64 `form:"amount" binding:"required,min=1" validate:"required,min=1"`
}

const (
	PAYGATE_ORDER_TYPE_DEPOSIT  = 1
	PAYGATE_ORDER_TYPE_WITHDRAW = 2
)

type LuckyThaiOrder struct {
	Id                int64      `json:"id"`
	UserId            int64      `json:"userId"`
	OrderTypeId       int64      `json:"orderTypeId"`
	RefId             *int64     `json:"refId"`
	OrderNo           string     `json:"orderNo"`
	Amount            float64    `json:"amount"`
	TransactionNo     *string    `json:"transactionNo"`
	TransactionDate   *time.Time `json:"transactionDate"`
	TransactionStatus *string    `json:"transactionStatus"`
	PaymentAt         *time.Time `json:"paymentAt"`
	QrPromptpay       *string    `json:"qrPromptpay"`
	CreatedAt         time.Time  `json:"createdAt"`
	UpdatedAt         *time.Time `json:"updatedAt"`
}
type LuckyThaiOrderListRequest struct {
	UserId        *int64 `form:"userId"`
	OrderTypeId   *int64 `form:"orderTypeId"`
	OrderNo       string `form:"orderNo"`
	TransactionNo string `form:"transactionNo"`
	Amount        string `form:"amount"`
	Status        string `form:"status"`
	Page          int    `form:"page" default:"1" min:"1"`
	Limit         int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol       string `form:"sortCol"`
	SortAsc       string `form:"sortAsc"`
}
type LuckyThaiOrderResponse struct {
	Id                int64      `json:"id"`
	UserId            int64      `json:"userId"`
	OrderTypeId       int64      `json:"orderTypeId"`
	OrderTypeName     string     `json:"orderTypeName"`
	RefId             *int64     `json:"refId"`
	OrderNo           string     `json:"orderNo"`
	Amount            float64    `json:"amount"`
	TransactionNo     *string    `json:"transactionNo"`
	TransactionDate   *time.Time `json:"transactionDate"`
	TransactionStatus *string    `json:"transactionStatus"`
	PaymentAt         *time.Time `json:"paymentAt"`
	QrPromptpay       string     `json:"qrPromptpay"`
	CreatedAt         time.Time  `json:"createdAt"`
	UpdatedAt         *time.Time `json:"updatedAt"`
}
type LuckyThaiOrderWebResponse struct {
	UserId            int64     `json:"userId"`
	OrderNo           string    `json:"orderNo"`
	Amount            float64   `json:"amount"`
	TransactionStatus string    `json:"transactionStatus"`
	PaymentUrl        string    `json:"paymentUrl"`
	CreatedAt         time.Time `json:"createdAt"`
}
type LuckyThaiOrderQrResponse struct {
	Id       int64   `json:"id"`
	OrderNo  string  `json:"orderNo"`
	Amount   float64 `json:"amount"`
	QrBase64 string  `json:"qrBase64"`
}
type LuckyThaiOrderCreateRequest struct {
	UserId *int64  `form:"userId"`
	Amount float64 `form:"amount"`
}
type LuckyThaiOrderCreateBody struct {
	Id                int64   `json:"id"`
	UserId            int64   `json:"userId"`
	OrderTypeId       int64   `json:"orderTypeId"`
	RefId             *int64  `json:"refId"`
	OrderNo           string  `json:"orderNo"`
	Amount            float64 `json:"amount"`
	TransactionStatus string  `json:"transactionStatus"`
}
type LuckyThaiOrderUpdateBody struct {
	TransactionNo     string    `json:"transactionNo"`
	TransactionDate   time.Time `json:"transactionDate"`
	TransactionStatus string    `json:"transactionStatus"`
	QrPromptpay       string    `json:"qrPromptpay"`
}

type HengOrderReportListRequest struct {
	DateType              string `form:"dateType"`
	FromDate              string `form:"fromDate"`
	ToDate                string `form:"toDate"`
	BankTransactionStatus string `form:"bankTransactionStatus"`
	Search                string `form:"search"`
	Page                  int    `form:"page" default:"1" min:"1"`
	Limit                 int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol               string `form:"sortCol"`
	SortAsc               string `form:"sortAsc"`
}
type HengOrderReportResponse struct {
	Id                    int64      `json:"id"`
	TransactionDate       time.Time  `json:"transactionDate"`
	MemberCode            string     `json:"memberCode"`
	TransactionNo         string     `json:"transactionNo"`
	OrderNo               string     `json:"orderNo"`
	Amount                string     `json:"amount"`
	TransactionStatus     string     `json:"transactionStatus"`
	PaymentAt             *time.Time `json:"paymentAt"`
	BankTransactionStatus string     `json:"bankTransactionStatus"`
	Remark                string     `json:"remark"`
}

type HengCallbackReportListRequest struct {
	DateType string `form:"dateType"`
	FromDate string `form:"fromDate"`
	ToDate   string `form:"toDate"`
	Search   string `form:"search"`
	Page     int    `form:"page" default:"1" min:"1"`
	Limit    int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol  string `form:"sortCol"`
	SortAsc  string `form:"sortAsc"`
}
type HengCallbackReportResponse struct {
	Id         int64     `json:"id"`
	CreatedAt  time.Time `json:"createdAt"`
	Reference1 string    `json:"reference1"`
	Reference2 string    `json:"reference2"`
	Amount     string    `json:"amount"`
	FromBank   string    `json:"fromBank"`
	FromName   string    `json:"fromName"`
	ShortName  string    `json:"shortName"`
	BankName   string    `json:"bankName"`
}

type HengUser struct {
	Id          int64      `json:"id"`
	UserId      int64      `json:"userId"`
	AccountNo   string     `json:"accountNo"`
	AccountName string     `json:"accountName"`
	BankNo      string     `json:"bankNo"`
	BankCode    string     `json:"bankCode"`
	BankName    string     `json:"bankName"`
	CreatedAt   time.Time  `json:"createdAt"`
	UpdatedAt   *time.Time `json:"updatedAt"`
}
type HengUserCreateBody struct {
	Id          int64  `json:"id"`
	UserId      int64  `json:"userId"`
	AccountNo   string `json:"accountNo"`
	AccountName string `json:"accountName"`
	BankNo      string `json:"bankNo"`
	BankCode    string `json:"bankCode"`
	BankName    string `json:"bankName"`
}
type HengUserUpdateBody struct {
	AccountNo   *string `json:"accountNo"`
	AccountName *string `json:"accountName"`
	BankNo      *string `json:"bankNo"`
	BankCode    *string `json:"bankCode"`
	BankName    *string `json:"bankName"`
}
type HengUserBankDetailResponse struct {
	Id                 int64      `json:"id"`
	MemberCode         string     `json:"memberCode"`
	Phone              string     `json:"phone"`
	Status             string     `json:"status"`
	Fullname           string     `json:"fullname"`
	Credit             float64    `json:"credit"`
	BankAccount        string     `json:"bankAccount"`
	BankId             int64      `json:"bankId"`
	BankName           string     `json:"bankName"`
	BankCode           string     `json:"bankCode"`
	PaymentAccountNo   string     `json:"paymentAccountNo"`
	PaymentAccountName string     `json:"paymentAccountName"`
	PaymentBankNo      string     `json:"paymentBankNo"`
	PaymentBankCode    string     `json:"paymentBankCode"`
	PaymentBankName    string     `json:"paymentBankName"`
	CreatedAt          time.Time  `json:"createdAt"`
	UpdatedAt          *time.Time `json:"updatedAt"`
}

type CheckPaymentAmountDetailPaygateMerchantResponse struct {
	PaymentWithdrawMinimum float64 `json:"paymentWithdrawMinimum"`
	PaymentWithdrawMaximum float64 `json:"paymentWithdrawMaximum"`
	PaymentDepositMinimum  float64 `json:"paymentDepositMinimum"`
	PaymentDepositMaximum  float64 `json:"paymentDepositMaximum"`
}

type CheckPaymentAmountDetailPaygateMerchantRequest struct {
	TypeName string `form:"typeName" binding:"required"`
}

type PaygateOrderReportListRequest struct {
	MerchantId            int64  `form:"merchantId" binding:"required" validate:"required"`
	DateType              string `form:"dateType"`
	FromDate              string `form:"fromDate"`
	ToDate                string `form:"toDate"`
	StatusName            string `form:"statusName"`
	BankTransactionStatus string `form:"bankTransactionStatus"`
	Search                string `form:"search"`
	Page                  int    `form:"page" default:"1" min:"1"`
	Limit                 int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol               string `form:"sortCol"`
	SortAsc               string `form:"sortAsc"`
}
type PaygateOrderReportResponse struct {
	Id                    int64      `json:"id"`
	CreatedAt             time.Time  `json:"createdAt"`
	MemberCode            string     `json:"memberCode"`
	AdminFullname         string     `json:"adminFullname"`
	MerchantName          string     `json:"merchantName"`
	TransactionNo         string     `json:"transactionNo"`
	OrderNo               string     `json:"orderNo"`
	Amount                string     `json:"amount"`
	TransactionStatus     string     `json:"transactionStatus"`
	PaymentAt             *time.Time `json:"paymentAt"`
	BankTransactionStatus string     `json:"bankTransactionStatus"`
	Remark                string     `json:"remark"`
}

type PaygateOrderCreateDepositRequest struct {
	Id         int64 `form:"id" binding:"required" validate:"required"`
	MerchantId int64 `form:"merchantId" binding:"required" validate:"required"`
	AdminId    int64 `form:"adminId" validate:"required" json:"-"`
}
type PaygateOrderIgnoreOrderRequest struct {
	Id         int64 `form:"id" binding:"required" validate:"required"`
	MerchantId int64 `form:"merchantId" binding:"required" validate:"required"`
	AdminId    int64 `form:"adminId" validate:"required" json:"-"`
}

type LastestAnyPaygateDeposit struct {
	Id         int64     `json:"id"`
	MerchantId int64     `json:"merchantId"`
	UserId     int64     `json:"userId"`
	Amount     float64   `json:"amount"`
	CreatedAt  time.Time `json:"createdAt"`
}

type PaygateAccount struct {
	Id                int64      `json:"id"`
	ProviderId        int64      `json:"providerId"`
	Name              string     `json:"name"`
	DisplayName       string     `json:"displayName"`
	TypeName          string     `json:"typeName"`
	HasDeposit        bool       `json:"hasDeposit"`
	IsDepositEnabled  bool       `json:"isDepositEnabled"`
	HasWithdraw       bool       `json:"hasWithdraw"`
	IsWithdrawEnabled bool       `json:"isWithdrawEnabled"`
	ApiEndPoint       string     `json:"apiEndPoint"`
	ShopName          string     `json:"shopName"`
	Username          string     `json:"username"`
	Password          string     `json:"password"`
	PrivateKey        string     `json:"privateKey"`
	AccessKey         string     `json:"accessKey"`
	SecretKey         string     `json:"secretKey"`
	PartnerKey        string     `json:"partnerKey"`
	RepayAppId        string     `json:"repayAppId"`
	LoanAppId         string     `json:"loanAppId"`
	MerchantId        string     `json:"merchantId"`
	Token             string     `json:"token"`
	AesKey            string     `json:"aesKey"`
	Balance           float64    `json:"balance"`
	MerchantUpdateAt  *time.Time `json:"merchantUpdateAt"`
	CallbackUrl       string     `json:"callbackUrl"`
	CreatedAt         time.Time  `json:"createdAt"`
	UpdatedAt         *time.Time `json:"updatedAt"`
}
type PaygateAccountListRequest struct {
	Page    int    `form:"page" default:"1" min:"1"`
	Limit   int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol string `form:"sortCol"`
	SortAsc string `form:"sortAsc"`
}
type PaygateAccountCreateRequest struct {
	ProviderId             int64   `form:"providerId" binding:"required"`
	DisplayName            string  `form:"displayName"`
	IsDepositEnabled       bool    `form:"isDepositEnabled"`
	IsWithdrawEnabled      bool    `form:"isWithdrawEnabled"`
	ShopName               string  `form:"shopName"`
	Username               string  `form:"username"`
	Password               string  `form:"password"`
	PrivateKey             string  `form:"privateKey"`
	AccessKey              string  `form:"accessKey"`
	SecretKey              string  `form:"secretKey"`
	PartnerKey             string  `form:"partnerKey"`
	RepayAppId             string  `form:"repayAppId"`
	LoanAppId              string  `form:"loanAppId"`
	MerchantId             string  `form:"merchantId"`
	Token                  string  `form:"token"`
	AesKey                 string  `form:"aesKey"`
	PaymentWithdrawMinimum float64 `form:"paymentWithdrawMinimum"`
	PaymentWithdrawMaximum float64 `form:"paymentWithdrawMaximum"`
	PaymentDepositMinimum  float64 `form:"paymentDepositMinimum"`
	PaymentDepositMaximum  float64 `form:"paymentDepositMaximum"`
}
type PaygateAccountCreateBody struct {
	Id                     int64   `json:"id"`
	ProviderId             int64   `json:"providerId"`
	Name                   string  `json:"name"`
	DisplayName            string  `json:"displayName"`
	TypeName               string  `json:"typeName"`
	HasDeposit             bool    `json:"hasDeposit"`
	IsDepositEnabled       bool    `json:"isDepositEnabled"`
	HasWithdraw            bool    `json:"hasWithdraw"`
	IsWithdrawEnabled      bool    `json:"isWithdrawEnabled"`
	ApiEndPoint            string  `json:"apiEndPoint"`
	ShopName               string  `json:"shopName"`
	Username               string  `json:"username"`
	Password               string  `json:"password"`
	PrivateKey             string  `json:"privateKey"`
	AccessKey              string  `json:"accessKey"`
	SecretKey              string  `json:"secretKey"`
	PartnerKey             string  `json:"partnerKey"`
	RepayAppId             string  `json:"repayAppId"`
	LoanAppId              string  `json:"loanAppId"`
	MerchantId             string  `json:"merchantId"`
	Token                  string  `json:"token"`
	AesKey                 string  `json:"aesKey"`
	PaymentWithdrawMinimum float64 `json:"paymentWithdrawMinimum"`
	PaymentWithdrawMaximum float64 `json:"paymentWithdrawMaximum"`
	PaymentDepositMinimum  float64 `json:"paymentDepositMinimum"`
	PaymentDepositMaximum  float64 `json:"paymentDepositMaximum"`
}
type PaygateAccountUpdateRequest struct {
	DisplayName            *string  `form:"displayName"`
	IsDepositEnabled       *bool    `form:"isDepositEnabled"`
	IsWithdrawEnabled      *bool    `form:"isWithdrawEnabled"`
	ApiEndPoint            *string  `form:"apiEndPoint"`
	ShopName               *string  `form:"shopName"`
	Username               *string  `form:"username"`
	Password               *string  `form:"password"`
	PrivateKey             *string  `form:"privateKey"`
	AccessKey              *string  `form:"accessKey"`
	SecretKey              *string  `form:"secretKey"`
	PartnerKey             *string  `form:"partnerKey"`
	RepayAppId             *string  `form:"repayAppId"`
	LoanAppId              *string  `form:"loanAppId"`
	MerchantId             *string  `form:"merchantId"`
	Token                  *string  `form:"token"`
	AesKey                 *string  `form:"aesKey"`
	PaymentWithdrawMinimum *float64 `form:"paymentWithdrawMinimum"`
	PaymentWithdrawMaximum *float64 `form:"paymentWithdrawMaximum"`
	PaymentDepositMinimum  *float64 `form:"paymentDepositMinimum"`
	PaymentDepositMaximum  *float64 `form:"paymentDepositMaximum"`
}
type PaygateAccountUpdateBody struct {
	DisplayName            *string    `json:"displayName"`
	IsDepositEnabled       *bool      `json:"isDepositEnabled"`
	IsWithdrawEnabled      *bool      `json:"isWithdrawEnabled"`
	ApiEndPoint            *string    `json:"apiEndPoint"`
	ShopName               *string    `json:"shopName"`
	Username               *string    `json:"username"`
	Password               *string    `json:"password"`
	PrivateKey             *string    `json:"privateKey"`
	AccessKey              *string    `json:"accessKey"`
	SecretKey              *string    `json:"secretKey"`
	PartnerKey             *string    `form:"partnerKey"`
	RepayAppId             *string    `json:"repayAppId"`
	LoanAppId              *string    `json:"loanAppId"`
	MerchantId             *string    `form:"merchantId"`
	Token                  *string    `form:"token"`
	AesKey                 *string    `form:"aesKey"`
	Balance                *float64   `json:"balance"`
	MerchantUpdateAt       *time.Time `json:"merchantUpdateAt"`
	CallbackUrl            *string    `json:"callbackUrl"`
	PaymentWithdrawMinimum *float64   `json:"paymentWithdrawMinimum"`
	PaymentWithdrawMaximum *float64   `json:"paymentWithdrawMaximum"`
	PaymentDepositMinimum  *float64   `json:"paymentDepositMinimum"`
	PaymentDepositMaximum  *float64   `json:"paymentDepositMaximum"`
}
