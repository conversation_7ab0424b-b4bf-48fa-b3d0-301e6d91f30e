package model

import "time"

type News struct {
	Id          int64
	Title       string
	CoverUrl    string
	Images      string
	Detail      string
	Tags        string
	SortOrder   int
	IsHighlight bool
	CreatedBy   int64
	CreatedAt   time.Time
	UpdatedAt   *time.Time
}

type NewsDetail struct {
	Id          int64     `json:"id"`
	Title       string    `json:"title"`
	CoverUrl    string    `json:"coverUrl"`
	Images      string    `json:"images"`
	Detail      string    `json:"detail"`
	Tags        string    `json:"tags"`
	SortOrder   int       `json:"sortOrder"`
	IsHighlight bool      `json:"isHighlight"`
	CreatedBy   string    `json:"createdBy"`
	CreatedAt   time.Time `json:"createdAt"`
}

type NewsQuery struct {
	Page    int    `form:"page" binding:"omitempty,min=1" default:"1"`
	Limit   int    `form:"limit" binding:"omitempty,min=1,max=100" default:"10"`
	StartAt string `form:"startAt" binding:"omitempty,datetime=2006-01-02"`
	EndAt   string `form:"endAt" binding:"omitempty,datetime=2006-01-02"`
	Filter  string `form:"filter" binding:"omitempty"`
}

type NewsBody struct {
	Title       string `json:"title" binding:"required,min=1,max=255"`
	CoverUrl    string `json:"coverUrl" binding:"required,min=1,max=255"`
	Images      string `json:"images,omitempty"`
	Detail      string `json:"detail" binding:"required"`
	Tags        string `json:"tags,omitempty"`
	IsHighlight bool   `json:"isHighlight,omitempty"`
	CreatedBy   int64  `json:"createdBy" binding:"required,min=1" default:"1"`
	CreatedAt   string `json:"createdAt,omitempty"`
}

type NewsUpdateBody struct {
	Title       string `json:"title" binding:"required,min=1,max=255"`
	CoverUrl    string `json:"coverUrl" binding:"required,min=1,max=255"`
	Images      string `json:"images"`
	Detail      string `json:"detail" binding:"required"`
	Tags        string `json:"tags"`
	SortOrder   int    `json:"sortOrder" binding:"omitempty,min=1"`
	IsHighlight bool   `json:"isHighlight"`
}

type NewsSortItem struct {
	Id        int64 `json:"id"`
	SortOrder int   `json:"sortOrder" binding:"required,min=1"`
}

type NewsSortBody struct {
	List []NewsSortItem `json:"list"`
}

type NewsListResponse struct {
	List  []NewsDetail `json:"list"`
	Total int          `json:"total"`
}
