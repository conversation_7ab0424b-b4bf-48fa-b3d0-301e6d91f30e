package model

import "time"

const (
	COUPON_USER_PENDING             = int64(1)
	COUPON_USER_RECEIVE_ON_TURNOVER = int64(2)
	COUPON_USER_SUCCESS             = int64(3)
	COUPON_USER_DELETE              = int64(4)
)

const (
	COUPON_ACTIVE = int64(1)
	COUPON_DELETE = int64(2)
)

const (
	COUPON_TURNOVER_PASS = "COUPON_TURNOVER_PASS"
	COUPON_TURNOVER_FAIL = "COUPON_CASH_USER_FAIL"
)

type CouponCashStatus struct {
	ID      int64  `json:"id"`
	Name    string `json:"name"`
	LabelTh string `json:"label_th"`
	LabelEn string `json:"label_en"`
}

type CouponCashUserStatus struct {
	ID      int64  `json:"id"`
	Name    string `json:"name"`
	LabelTh string `json:"label_th"`
	LabelEn string `json:"label_en"`
}

type CouponCash struct {
	Id                 int64     `json:"id"`
	Name               string    `json:"name"`
	CouponTotal        int64     `json:"couponTotal"`
	CouponTurnover     int64     `json:"couponTurnover"`
	TurnoverToPlay     float64   `json:"-"`
	CouponBonus        float64   `json:"couponBonus"`
	CouponCashStatusId int64     `json:"couponCashStatusId"`
	CreatedByAdminId   int64     `json:"createdByAdminId"`
	DeletedByAdminId   int64     `json:"deletedByAdminId"`
	CreatedAt          time.Time `json:"createdAt"`
	UpdatedAt          time.Time `json:"updatedAt"`
	DeletedAt          time.Time `json:"deletedAt"`
}

type CouponCashCreateRequest struct {
	Id                 int64   `json:"-"`
	Name               string  `json:"name" binding:"required"`
	CouponTotal        int64   `json:"couponTotal" binding:"required"`
	CouponTurnover     *int64  `json:"couponTurnover"`
	TurnoverToPlay     float64 `json:"-"`
	CouponBonus        float64 `json:"couponBonus" binding:"required"`
	CreatedByAdminId   int64   `json:"-"`
	CouponCashStatusId int64   `json:"-"`
}

type CouponCashUser struct {
	Id                     int64     `json:"id"`
	CouponCashId           int64     `json:"couponCashId"`
	UserId                 int64     `json:"userId"`
	GenerateKey            string    `json:"gernateKey"`
	CouponCashUserStatusId int64     `json:"couponCashUserStatusId"`
	UserReceiveAt          string    `json:"userReceiveAt"`
	CreatedAt              string    `json:"createdAt"`
	CreatedByAdminId       int64     `json:"createdByAdminId"`
	DeletedByAdminId       int64     `json:"deletedByAdminId"`
	UpdatedAt              time.Time `json:"updatedAt"`
	DeletedAt              time.Time `json:"deletedAt"`
}
type CouponCashCreateCouponCashUserBody struct {
	CouponCashId           int64     `json:"couponCashId" binding:"required"`
	GenerateKey            string    `json:"gernateKey" binding:"required"`
	CouponCashUserStatusId int64     `json:"couponCashUserStatusId" binding:"required"`
	CreatedByAdminId       int64     `json:"-"`
	CreatedAt              time.Time `json:"-"`
}

type GetCouponCashListRequest struct {
	Page               int    `form:"page" default:"1"`
	Limit              int    `form:"limit" default:"10"`
	StartDate          string `form:"startDate" time_format:"2006-01-02"`
	EndDate            string `form:"endDate" time_format:"2006-01-02"`
	Search             string `form:"search"`
	CouponCashStatusId *int64 `form:"couponCashStatusId"`
}

type GetCouponCashListResponse struct {
	Id                 int64     `json:"id"`
	Name               string    `json:"name"`
	CouponTotal        int64     `json:"couponTotal"`
	CouponTurnover     int64     `json:"couponTurnover"`
	TurnoverToPlay     float64   `json:"turnoverToPlay"`
	CouponBonus        float64   `json:"couponBonus"`
	CouponCashStatusId int64     `json:"couponCashStatusId"`
	CouponCashStatusTh string    `json:"couponCashStatusTh"`
	CreatedByAdminId   int64     `json:"createdByAdminId"`
	CreatedByAdminName string    `json:"createdByAdminName"`
	DeletedByAdminId   int64     `json:"deletedByAdminId"`
	DeletedByAdminName string    `json:"deletedByAdminName"`
	CreatedAt          time.Time `json:"createdAt"`
	UpdatedAt          time.Time `json:"updatedAt"`
	DeletedAt          time.Time `json:"deletedAt"`
}

type GetCouponCashUserListRequest struct {
	CouponCashId           *int64 `form:"couponCashId"`
	Page                   int    `form:"page" default:"1"`
	Limit                  int    `form:"limit" default:"10"`
	StartDate              string `form:"startDate" time_format:"2006-01-02"`
	EndDate                string `form:"endDate" time_format:"2006-01-02"`
	Search                 string `form:"search"`
	SearchCode             string `form:"searchCode"`
	CouponCashUserStatusId *int64 `form:"couponCashUserStatusId"`
}

type GetCouponCashUserListResponse struct {
	Id                       int64      `json:"id"`
	UserId                   int64      `json:"userId"`
	MemberCode               string     `json:"memberCode"`
	FullName                 string     `json:"fullName"`
	CouponCashId             int64      `json:"couponCashId"`
	CouponCashName           string     `json:"couponCashName"`
	CouponCashTurnover       int64      `json:"couponCashTurnover"`
	CouponCashTurnoverToPlay float64    `json:"couponCashTurnoverToPlay"`
	CouponCashBonus          float64    `json:"couponCashBonus"`
	GenerateKey              string     `json:"generateKey"`
	CouponCashUserStatusId   int64      `json:"couponCashUserStatusId"`
	CouponCashUserStatusTh   string     `json:"couponCashUserStatusTh"`
	CreatedAt                time.Time  `json:"createdAt"`
	UserReceiveAt            *time.Time `json:"userReceiveAt"`
	CreatedByAdminId         int64      `json:"createdByAdminId"`
	CreatedByAdminName       string     `json:"createdByAdminName"`
	DeletedByAdminId         int64      `json:"deletedByAdminId"`
	DeletedByAdminName       string     `json:"deletedByAdminName"`
}

type UserUseCouponCashRequest struct {
	GenerateKey string `json:"generateKey" binding:"required"`
	UserId      int64  `json:"-"`
}

type UpdateCouponCashUserFromUserBody struct {
	GenerateKey            string    `json:"generateKey" binding:"required"`
	UserId                 int64     `json:"userId" binding:"required"`
	CouponCashUserStatusId int64     `json:"couponCashUserStatusId" binding:"required"`
	UserReceiveAt          time.Time `json:"userReceiveAt" binding:"required"`
}

type CouponCashUserConfirm struct {
	Id               int64     `json:"id"`
	CouponCashUserId int64     `json:"couponCashUserId"`
	UserId           int64     `json:"userId"`
	ConfirmKey       string    `json:"confirmKey"`
	CreatedAt        time.Time `json:"createdAt"`
	UpdatedAt        time.Time `json:"updatedAt"`
	DeletedAt        time.Time `json:"deletedAt"`
}

type CouponCashUserConfirmCreateRequest struct {
	Id               int64  `json:"-"`
	CouponCashUserId int64  `json:"couponCashUserId" binding:"required"`
	UserId           int64  `json:"userId" binding:"required"`
	ConfirmKey       string `json:"confirmKey" binding:"required"`
}

type SoftDeleteCouponCashUserRequest struct {
	Id int64 `uri:"id" binding:"required"`
}

type SoftDeleteCouponCashUserByCouponCashId struct {
	Id               int64 `json:"id" binding:"required"`
	DeletedByAdminId int64 `json:"-"`
	DeletedAt        time.Time
}

type SoftDeleteCouponCashRequest struct {
	Id int64 `uri:"id" binding:"required"`
}

type SoftDeleteCouponCashUserById struct {
	Id               int64 `json:"id" binding:"required"`
	DeletedByAdminId int64 `json:"-"`
	DeletedAt        time.Time
}

type CheckCouponTurnOverWithdrawRequest struct {
	UserId         int64   `form:"userId" binding:"required"`
	WithdrawAmount float64 `form:"withdrawAmount" binding:"required"`
}

type CouponCashSummaryRequest struct {
	StartDate string `form:"startDate" time_format:"2006-01-02"`
	EndDate   string `form:"endDate" time_format:"2006-01-02"`
}
type CouponCashSummaryResponse struct {
	TotalCouponCash           int64   `json:"totalCouponCash"`           // จำนวนคูปองที่สร้าง
	TotalCouponCashUser       int64   `json:"totalCreateCouponCashUser"` // จำนวน code ทั้งหมดที่สร้าง
	TotalCouponCashUserUse    int64   `json:"totalCouponCashUserUse"`    // จำนวน code ทั้งหมดที่ใช้แล้ว
	TotalCouponCashUserBonus  float64 `json:"totalCouponCashUserBonus"`  // ยอดรวมโบนัสคูปองที่รับแล้ว
	TotalCouponCashUserNotUse int64   `json:"totalCouponCashUserNotUse"` // จำนวน code ทั้งหมดที่ยังไม่ได้ใช้
}
type CouponCashUserSummaryRequest struct {
	CouponCashId *int64 `form:"couponCashId"`
	StartDate    string `form:"startDate" time_format:"2006-01-02"`
	EndDate      string `form:"endDate" time_format:"2006-01-02"`
}
type CouponCashUserSummaryResponse struct {
	TotalCouponCash           int64   `json:"totalCouponCash"`           // จำนวนคูปองที่สร้าง
	TotalCouponCashUser       int64   `json:"totalCreateCouponCashUser"` // จำนวน code ทั้งหมดที่สร้าง
	TotalCouponCashUserUse    int64   `json:"totalCouponCashUserUse"`    // จำนวน code ทั้งหมดที่ใช้แล้ว
	TotalCouponCashUserBonus  float64 `json:"totalCouponCashUserBonus"`  // ยอดรวมโบนัสคูปองที่รับแล้ว
	TotalCouponCashUserNotUse int64   `json:"totalCouponCashUserNotUse"` // จำนวน code ทั้งหมดที่ยังไม่ได้ใช้
}
