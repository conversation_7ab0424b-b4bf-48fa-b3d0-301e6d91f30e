package model

import (
	"time"
)

type ZappayDepositRequest struct {
	<PERSON><PERSON><PERSON>        string  `json:"partner<PERSON>ey"`
	RepayAppId        string  `json:"repayAppId"`
	LoanAppId         string  `json:"loanAppId"`
	MerchantId        string  `json:"merchantId"`
	Token             string  `json:"token"`
	AesKey            string  `json:"aesKey"`
	OrderNo           string  `json:"orderNo"`
	BankCode          string  `json:"bankCode"`
	Amount            float64 `json:"amount"`
	UserFullname      string  `json:"userFullname"`
	UserMobile        string  `json:"userMobile"`
	UserAccountNumber string  `json:"userAccountNumber"`
	UserAccountBank   string  `json:"userAccountBank"`
}
type ZappayEncryptPayload struct {
	Data struct {
		PartnerKey string `json:"partner_key"`
		EnData     string `json:"en_data"`
	} `json:"data"`
}

type ZappayWebhookEncryptPayload struct {
	Data struct {
		<PERSON><PERSON><PERSON> string `json:"partner_key"`
		EnData     string `json:"en_data"`
		MchOrderNo string `json:"mch_order_no"`
	} `json:"data"`
}

type ZappayWebhookDepositResponse struct {
	Sign               string `json:"sign"`
	Timestamp          int64  `json:"timestamp"`
	OrderNo            string `json:"order_no"`
	MchOrderNo         string `json:"mch_order_no"`
	Status             int    `json:"status"`
	Amount             string `json:"amount"`
	PayTime            string `json:"pay_time"`
	RepayAccountBank   string `json:"repay_account_bank"`
	RepayAccountNumber string `json:"repay_account_number"`
	AccountNumber      string `json:"account_number"`
	AccountBank        string `json:"account_bank"`
}

type ZappayErrorRemoteResponse struct {
	Message string `json:"message"`
	Code    int64  `json:"code"`
	Data    string `json:"data"`
}
type ZappayErrorStringRemoteResponse struct {
	Message string `json:"message"`
	Code    string `json:"code"`
	Data    string `json:"data"`
}

type ZappayToken struct {
	Id          int64      `json:"id"`
	AccessToken string     `json:"accessToken"`
	ExpireAt    time.Time  `json:"expireAt"`
	CreateBy    int64      `json:"createBy"`
	CreatedAt   time.Time  `json:"createdAt"`
	UpdatedAt   *time.Time `json:"updatedAt"`
}
type ZappayTokenCreateBody struct {
	Id          int64     `json:"id"`
	AccessToken string    `json:"accessToken"`
	ExpireAt    time.Time `json:"expireAt"`
	CreateBy    int64     `json:"createBy"`
}
type ZappayTokenCreateRemoteRequest struct {
	AccessKey string `json:"accessKey" validate:"required"`
	SecretKey string `json:"secretKey" validate:"required"`
}
type ZappayTokenCreateRemoteResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Code    string `json:"code"`
	Data    struct {
		Token string `json:"token"`
	} `json:"data"`
}

type ZappayCustomerCreateRemoteRequest struct {
	Name      string `json:"name" validate:"required"`
	BankCode  string `json:"bankCode" validate:"required"`
	AccountNo string `json:"accountNo" validate:"required"`
}
type ZappayCustomerCreateRemoteResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Code    string `json:"code"`
	Data    struct {
		Partner      string   `json:"partner"`
		CustomerUuid string   `json:"customerUuid"`
		ClientCode   string   `json:"clientCode"`
		Name         string   `json:"name"`
		SearchName   []string `json:"searchName"`
		AccountNo    string   `json:"accountNo"`
		BankCode     string   `json:"bankCode"`
		Status       string   `json:"status"`
		CreatedAt    int64    `json:"createdAt"`
		UpdatedAt    int64    `json:"updatedAt"`
	} `json:"data"`
}
type ZappayCustomerUpdateRemoteRequest struct {
	CustomerUuid string `json:"customerUuid" validate:"required"`
	Name         string `json:"name" validate:"required"`
	BankCode     string `json:"bankCode" validate:"required"`
	AccountNo    string `json:"accountNo" validate:"required"`
}
type ZappayCustomerUpdateRemoteResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Code    string `json:"code"`
	Data    struct {
		Partner      string   `json:"partner"`
		CustomerUuid string   `json:"customerUuid"`
		ClientCode   string   `json:"clientCode"`
		Name         string   `json:"name"`
		SearchName   []string `json:"searchName"`
		AccountNo    string   `json:"accountNo"`
		BankCode     string   `json:"bankCode"`
		Status       string   `json:"status"`
		CreatedAt    int64    `json:"createdAt"`
		UpdatedAt    int64    `json:"updatedAt"`
	} `json:"data"`
}

type ZappayDepositCreateRemoteRequest struct {
	OrderNo           string  `json:"orderNo"`
	BankCode          string  `json:"bankCode"`
	Amount            float64 `json:"amount"`
	UserFullname      string  `json:"userFullname"`
	UserMobile        string  `json:"userMobile"`
	UserAccountNumber string  `json:"userAccountNumber"`
	UserAccountBank   string  `json:"userAccountBank"`
}
type ZappayDepositCreateRemoteResponseData struct {
	OrderNo            string  `json:"order_no"`
	MchOrderNo         string  `json:"mch_order_no"`
	ExpirationDate     string  `json:"expiration_date"`
	Status             int     `json:"status"`
	Amount             float64 `json:"amount"`
	OriginCode         string  `json:"origin_code"`
	Redirect           string  `json:"redirect"`
	BankCardName       string  `json:"bank_card_name"`
	BankDiscountAmount float64 `json:"bank_discount_amount"`
	SubBankName        string  `json:"sub_bank_name"`
	BankName           string  `json:"bank_name"`
	AccountNumber      string  `json:"account_number"`
}
type ZappayDepositCreateRemoteResponse struct {
	Code    string                                `json:"code"`
	Message string                                `json:"message"`
	Data    ZappayDepositCreateRemoteResponseData `json:"data"`
}
type ZappayDepositCreateRemote2Response struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Data    struct {
		OrderNo            string  `json:"order_no"`
		MchOrderNo         string  `json:"mch_order_no"`
		ExpirationDate     string  `json:"expiration_date"`
		Status             int     `json:"status"`
		Amount             float64 `json:"amount,string"`
		OriginCode         string  `json:"origin_code"`
		Redirect           string  `json:"redirect"`
		BankCardName       string  `json:"bank_card_name"`
		BankDiscountAmount float64 `json:"bank_discount_amount,string"`
		SubBankName        string  `json:"sub_bank_name"`
		BankName           string  `json:"bank_name"`
		AccountNumber      string  `json:"account_number"`
	} `json:"data"`
}

type ZappayCheckBalanceRemoteResponse struct {
	Success bool `json:"success"`
	Code    int  `json:"code"`
	Data    struct {
		InBalance struct {
			CoinChain string  `json:"coinChain"`
			CoinUnit  string  `json:"coinUnit"`
			Balance   float64 `json:"balance"`
			Freeze    float64 `json:"freeze"`
		} `json:"inBalance"`
		OutBalance struct {
			CoinChain string  `json:"coinChain"`
			CoinUnit  string  `json:"coinUnit"`
			Balance   float64 `json:"balance"`
			Freeze    float64 `json:"freeze"`
		} `json:"outBalance"`
		Sign             string `json:"sign"`
		CheckRequestTime int64  `json:"checkRequestTime"`
		DfBalance        struct {
			CoinChain string  `json:"coinChain"`
			CoinUnit  string  `json:"coinUnit"`
			Balance   float64 `json:"balance"`
			Freeze    float64 `json:"freeze"`
		} `json:"dfBalance"`
		DsBalance struct {
			CoinChain string  `json:"coinChain"`
			CoinUnit  string  `json:"coinUnit"`
			Balance   float64 `json:"balance"`
			Freeze    float64 `json:"freeze"`
		} `json:"dsBalance"`
	} `json:"data"`
}

type ZappayGetOrderRemoteResponse struct {
	Success bool `json:"success"`
	Code    int  `json:"code"`
	Data    struct {
		OrderNo       string  `json:"orderNo"`
		ReceiveAddr   string  `json:"receiveAddr"`
		ChainName     string  `json:"chainName"`
		CoinUnit      string  `json:"coinUnit"`
		RequestAmount float64 `json:"requestAmount"`
		PayAmount     float64 `json:"payAmount"`
		Status        string  `json:"status"`
		Sign          string  `json:"sign"`
	} `json:"data"`
}

type ZappayWithdrawCreateRemoteRequest struct {
	OrderNo           string  `json:"orderNo"`
	Amount            float64 `json:"amount"`
	UserFullname      string  `json:"userFullname"`
	UserMobile        string  `json:"userMobile"`
	UserAccountNumber string  `json:"userAccountNumber"`
	UserAccountBank   string  `json:"userAccountBank"`
}

type ZappayWithdrawCreateRemoteResponse struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Data    struct {
		OrderNo    string `json:"order_no"`
		MchOrderNo string `json:"mch_order_no"`
		Status     int    `json:"status"`
		PayMsg     string `json:"pay_msg"`
	} `json:"data"`
}

type ZappayWebhookRequest struct {
	JsonPayload string `json:"jsonPayload"`
}
type ZappayWebhookCreateBody struct {
	Id          int64  `json:"id"`
	Name        string `json:"name"`
	JsonPayload string `json:"jsonPayload"`
}

type ZappayDepositWebhookResponse struct {
	Uuid         string  `json:"uuid"`
	CustomerUuid string  `json:"customerUuid"`
	AccountName  string  `json:"accountName"`
	BankCode     string  `json:"bankCode"`
	AccountNo    string  `json:"accountNo"`
	Amount       float64 `json:"amount"`
	Fee          float64 `json:"fee"`
	// SettleAmount float64 `json:"settleAmount"`
	Type        string `json:"type"`
	Status      string `json:"status"`
	ReferenceId string `json:"referenceId"`
}

type ZappayWithDrawWebhookResponse struct {
	Type                    string    `json:"type"`
	CurrencyCode            string    `json:"currencyCode"`
	FundOutStatus           string    `json:"fundOutStatus"`
	Amount                  float64   `json:"amount"`
	ServiceFee              float64   `json:"serviceFee"`
	IsPaid                  bool      `json:"isPaid"`
	TransactionRemark       string    `json:"transactionRemark"`
	FundOutDescription      string    `json:"fundOutDescription"`
	TransactionRef1         string    `json:"transactionRef1"`
	FundOutPaymentReference string    `json:"fundOutPaymentReference"`
	BankName                string    `json:"bankName"`
	BankCode                string    `json:"bankCode"`
	AccountNumber           string    `json:"accountNumber"`
	CreatedAt               time.Time `json:"createdAt"`
	UpdatedAt               time.Time `json:"updatedAt"`
	BankTransactionRef      string    `json:"bankTransactionRef"`
}

type ZappayCustomerDepositInfo struct {
	Name        string `json:"name"`
	DisplayName string `json:"displayName"`
	ShopName    string `json:"shopName"`
	MinAmount   int64  `json:"minAmount"`
	MaxAmount   int64  `json:"maxAmount"`
}
type ZappayDepositCreateRequest struct {
	UserId int64   `form:"userId" json:"-" validate:"required"`
	Amount float64 `form:"amount" json:"amount" binding:"required,min=1" validate:"required,min=1"`
}
type ZappayWithdrawCreateRequest struct {
	RefId  int64   `form:"refId" validate:"required"`
	UserId int64   `form:"userId" json:"-" validate:"required"`
	Amount float64 `form:"amount" binding:"required,min=1" validate:"required,min=1"`
}

const (
	ZAPPAY_ORDER_TYPE_DEPOSIT  = 1
	ZAPPAY_ORDER_TYPE_WITHDRAW = 2
)

type ZappayCustomer struct {
	Id           int64  `json:"id"`
	UserId       int64  `json:"userId"`
	CustomerUuid string `json:"customerUuid"`
	FullName     string `json:"fullName"`
	BankCode     string `json:"bankCode"`
	AccountNo    string `json:"accountNo"`
	AccountName  string `json:"accountName"`
}
type ZappayCustomerListRequest struct {
	Page    int    `form:"page" default:"1" min:"1"`
	Limit   int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol string `form:"sortCol"`
	SortAsc string `form:"sortAsc"`
}
type ZappayCustomerResponse struct {
	Id           int64  `json:"id"`
	UserId       int64  `json:"userId"`
	CustomerUuid string `json:"customerUuid"`
	FullName     string `json:"fullName"`
	BankCode     string `json:"bankCode"`
	AccountNo    string `json:"accountNo"`
	AccountName  string `json:"accountName"`
}
type ZappayCustomerCreateBody struct {
	Id           int64  `json:"id"`
	UserId       int64  `json:"userId"`
	CustomerUuid string `json:"customerUuid"`
	FullName     string `json:"fullName"`
	BankCode     string `json:"bankCode"`
	AccountNo    string `json:"accountNo"`
	AccountName  string `json:"accountName"`
}
type ZappayCustomerUpdateBody struct {
	CustomerUuid *string `json:"customerUuid"`
	FullName     *string `json:"fullName"`
	BankCode     *string `json:"bankCode"`
	AccountNo    *string `json:"accountNo"`
	AccountName  *string `json:"accountName"`
}

type ZappayOrder struct {
	Id                int64      `json:"id"`
	UserId            int64      `json:"userId"`
	OrderTypeId       int64      `json:"orderTypeId"`
	RefId             *int64     `json:"refId"`
	OrderNo           string     `json:"orderNo"`
	Amount            float64    `json:"amount"`
	TransactionNo     *string    `json:"transactionNo"`
	TransactionDate   *time.Time `json:"transactionDate"`
	TransactionStatus *string    `json:"transactionStatus"`
	PaymentAt         *time.Time `json:"paymentAt"`
	QrPromptpay       *string    `json:"qrPromptpay"`
	PaymentPageUrl    string     `json:"paymentPageUrl"`
	Remark            *string    `json:"remark"`
	ApiRemark         *string    `json:"apiRemark"`
	CreatedAt         time.Time  `json:"createdAt"`
	UpdatedAt         *time.Time `json:"updatedAt"`
}
type ZappayOrderListRequest struct {
	UserId        *int64 `form:"userId"`
	OrderTypeId   *int64 `form:"orderTypeId"`
	OrderNo       string `form:"orderNo"`
	TransactionNo string `form:"transactionNo"`
	Amount        string `form:"amount"`
	Status        string `form:"status"`
	Page          int    `form:"page" default:"1" min:"1"`
	Limit         int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol       string `form:"sortCol"`
	SortAsc       string `form:"sortAsc"`
}
type ZappayOrderResponse struct {
	Id                    int64      `json:"id"`
	UserId                int64      `json:"userId"`
	OrderTypeId           int64      `json:"orderTypeId"`
	OrderTypeName         string     `json:"orderTypeName"`
	RefId                 *int64     `json:"refId"`
	OrderNo               string     `json:"orderNo"`
	Amount                float64    `json:"amount"`
	TransferAmount        float64    `json:"transferAmount"`
	TransactionNo         *string    `json:"transactionNo"`
	TransactionDate       *time.Time `json:"transactionDate"`
	TransactionStatus     *string    `json:"transactionStatus"`
	PaymentAt             *time.Time `json:"paymentAt"`
	BankTransactionId     *string    `json:"bankTransactionId"`
	BankTransactionStatus string     `json:"bankTransactionStatus"`
	QrPromptpay           string     `json:"qrPromptpay"`
	PaymentPageUrl        string     `json:"paymentPageUrl"`
	Remark                *string    `json:"remark"`
	ApiRemark             *string    `json:"apiRemark"`
	CreatedAt             time.Time  `json:"createdAt"`
	UpdatedAt             *time.Time `json:"updatedAt"`
}
type ZappayOrderWebResponse struct {
	UserId            int64     `json:"userId"`
	OrderNo           string    `json:"orderNo"`
	Amount            float64   `json:"amount"`
	TransferAmount    float64   `json:"transferAmount"`
	TransactionStatus string    `json:"transactionStatus"`
	QrCode            string    `json:"qrCode"`
	QrBase64          string    `json:"qrBase64"`
	PaymentPageUrl    string    `json:"paymentPageUrl"`
	CreatedAt         time.Time `json:"createdAt"`
}
type ZappayOrderQrResponse struct {
	Id       int64   `json:"id"`
	OrderNo  string  `json:"orderNo"`
	Amount   float64 `json:"amount"`
	QrBase64 string  `json:"qrBase64"`
}
type ZappayOrderCreateRequest struct {
	UserId *int64  `form:"userId"`
	Amount float64 `form:"amount"`
}
type ZappayOrderCreateBody struct {
	Id                int64   `json:"id"`
	UserId            int64   `json:"userId"`
	OrderTypeId       int64   `json:"orderTypeId"`
	RefId             *int64  `json:"refId"`
	OrderNo           string  `json:"orderNo"`
	Amount            float64 `json:"amount"`
	TransactionStatus string  `json:"transactionStatus"`
}
type ZappayOrderUpdateBody struct {
	TransactionNo     string    `json:"transactionNo"`
	TransactionDate   time.Time `json:"transactionDate"`
	TransactionStatus string    `json:"transactionStatus"`
	TransferAmount    float64   `json:"transferAmount"`
	QrPromptpay       string    `json:"qrPromptpay"`
	PaymentPageUrl    string    `json:"paymentPageUrl"`
}
