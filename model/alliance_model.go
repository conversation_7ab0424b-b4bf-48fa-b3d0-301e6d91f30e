package model

import (
	"time"
)

type DateTypeResponse struct {
	DateType string `json:"dateType"`
	DateFrom string `json:"dateFrom"`
	DateTo   string `json:"dateTo"`
}

type UserAlliance struct {
	Id      int64  `json:"id"`
	<PERSON>as   string `json:"alias"`
	UserId  int64  `json:"userId"`
	RefCode string `json:"refCode"`
}
type UserAllianceListRequest struct {
	UserIds []int64 `form:"userIds"`
	Page    int     `form:"page" default:"1" min:"1"`
	Limit   int     `form:"limit" default:"10" min:"1" max:"100"`
	SortCol string  `form:"sortCol"`
	SortAsc string  `form:"sortAsc"`
}

type Alliance struct {
	ID        int64
	RefId     int64
	UserId    int64
	CreatedAt time.Time
}

type AllianceCommission struct {
	ID            int64
	ReferralBonus float64
	Description   string
	UpdatedAt     *time.Time
}

type AllianceCommissionBody struct {
	ReferralBonus float64 `json:"referralBonus"`
	Description   string  `json:"description"`
}

type AllianceCommissionSettingUpdateRequest struct {
	Alias             string  `json:"alias"`
	RefCode           *string `json:"refCode"`
	AlliancePercent   float64 `json:"alliancePercent"`
	CommissionPercent float64 `json:"commissionPercent"`
	Description       string  `json:"description"`
	LinkClickTotal    int     `json:"-"`
	RecommendTotal    int     `json:"-"`
	UserId            int64   `json:"-"`
}

type AllianceCommissionSettingUser struct {
	Alias             string  `json:"alias"`
	RefCode           string  `json:"refCode"`
	AlliancePercent   float64 `json:"alliancePercent"`
	CommissionPercent float64 `json:"commissionPercent"`
	Description       string  `json:"description"`
	LinkClickTotal    int     `json:"-"`
	RecommendTotal    int     `json:"-"`
	UserId            int64   `json:"-"`
}

type AllianceSummaryQuery struct {
	UserId   int64  `form:"userId" json:"-"`
	FromDate string `form:"fromDate" time_format:"2006-01-02" default:"2023-04-01"`
	ToDate   string `form:"toDate" time_format:"2006-01-02" default:"2023-04-01"`
}

type AlSummary struct {
	LinkClickTotal      int64 `json:"linkClickTotal"`
	MemberRegisterToday int64 `json:"memberRegisterToday"`
	MemberOnlineToday   int64 `json:"memberOnlineToday"`
	NoMemberCode        int64 `json:"noMemberCode"`
	HaveMemberCode      int64 `json:"haveMemberCode"`
	RecommendTotal      int64 `json:"recommendTotal"`
}

type AllianceTransaction struct {
	Id         int64     `json:"id"`
	Amount     float64   `json:"-"`
	Type       string    `json:"-"`
	MemberCode string    `json:"memberCode"`
	Phone      string    `json:"phone"`
	Deposit    float64   `json:"deposit"`
	Withdraw   float64   `json:"withdraw"`
	NetAmount  float64   `json:"netAmount"`
	CreatedAt  time.Time `json:"createdAt"`
}

type AllianceTransactionDailyQuery struct {
	Page  int    `form:"page"`
	Limit int    `form:"limit"`
	From  string `form:"from" time_format:"2006-01-02" default:"2023-04-01"`
	To    string `form:"to" time_format:"2006-01-02" default:"2023-04-01"`
}

type AllianceTransactionMemberQuery struct {
	Page       int    `form:"page"`
	Limit      int    `form:"limit"`
	MemberCode string `form:"memberCode"`
	From       string `form:"from" time_format:"2006-01-02" default:"2023-04-01"`
	To         string `form:"to" time_format:"2006-01-02" default:"2023-04-01"`
}
type AllianceAdminTransactionMemberQuery struct {
	AdminId    int64  `form:"adminId" validate:"required" json:"-"`
	UserId     int64  `form:"userId" validate:"required"`
	Page       int    `form:"page"`
	Limit      int    `form:"limit"`
	MemberCode string `form:"memberCode"`
	From       string `form:"from" time_format:"2006-01-02" default:"2023-04-01"`
	To         string `form:"to" time_format:"2006-01-02" default:"2023-04-01"`
}

type AllianceTransactionResponse struct {
	Result []AllianceTransaction `json:"result"`
	Total  int64                 `json:"total"`
}

type AlCreateIncome struct {
	WinLose           float64
	WinLoseCalculated float64
	Turn              float64
	TurnCalculated    float64
	UserId            int64
	RefId             int64
}

type AllianceIncome struct {
	ID                  int64   `json:"id"`
	MemberCode          string  `json:"memberCode"`
	AccumulatedPlay     float64 `json:"accumulatedPlay"`
	WinLose             float64 `json:"winLose"`
	AlliancePercent     float64 `json:"-"`
	CommissionPercent   float64 `json:"-"`
	TotalCommission     float64 `json:"totalCommission"`
	WinLoseAlliance     float64 `json:"winLoseAlliance"`
	CommissionAlliance  float64 `json:"commissionAlliance"`
	TotalBonusPayout    float64 `json:"totalBonusPayout"`
	AllianceBonusPayout float64 `json:"allianceBonusPayout"`
	IncomeAlliance      float64 `json:"incomeAlliance"`
	UserId              int64   `json:"userId"`
}

type AlCountBonus struct {
	BonusAmount float64 `json:"-"`
	UserId      int64   `json:"-"`
}

type AllianceMemberTotalIncomeListRequest struct {
	RefUserId int64  `form:"refUserId" validate:"required" json:"-"`
	Search    string `form:"search"`
	FromDate  string `form:"fromDate"`
	ToDate    string `form:"toDate"`
	Page      int    `form:"page" default:"1" min:"1"`
	Limit     int    `form:"limit" default:"10" min:"0" max:"100"`
	SortCol   string `form:"sortCol"`
	SortAsc   string `form:"sortAsc"`
}
type AllianceAdminMemberTotalIncomeListRequest struct {
	RefUserId int64  `form:"refUserId" binding:"required"`
	AdminId   int64  `form:"adminId" json:"-"`
	Search    string `form:"search"`
	FromDate  string `form:"fromDate"`
	ToDate    string `form:"toDate"`
	Page      int    `form:"page" default:"1" min:"1"`
	Limit     int    `form:"limit" default:"10" min:"0" max:"100"`
	SortCol   string `form:"sortCol"`
	SortAsc   string `form:"sortAsc"`
}
type AllianceMemberTotalIncomeResponse struct {
	UserId                int64   `json:"userId"`
	MemberCode            string  `json:"memberCode"`
	TotalPlayAmount       float64 `json:"totalPlayAmount"`
	TotalWinLoseAmount    float64 `json:"totalWinLoseAmount"`
	TotalCommission       float64 `json:"totalCommission"`
	AllianceWinloseAmount float64 `json:"allianceWinloseAmount"`
	AllianceCommission    float64 `json:"allianceCommission"`
	TotalPaidBonus        float64 `json:"totalPaidBonus"`
	AlliancePaidBonus     float64 `json:"alliancePaidBonus"`
	AllianceIncome        float64 `json:"allianceIncome"`
}

type AllianceIncomeResponse struct {
	Result []AllianceIncome `json:"result"`
	Total  int64            `json:"total"`
}

type AllianceFirstDeposit struct {
	ID         int64     `json:"id"`
	Phone      string    `json:"phone"`
	MemberCode string    `json:"memberCode"`
	Credit     float64   `json:"credit"`
	CreatedAt  time.Time `json:"createdAt"`
}

type AllianceFirstDepositQuery struct {
	Page  int        `form:"page"`
	Limit int        `form:"limit"`
	From  *time.Time `form:"from" time_format:"2006-01-02 15:04" default:"2023-04-01 00:00"`
	To    *time.Time `form:"to" time_format:"2006-01-02 15:04" default:"2023-04-01 00:00"`
}

type AllianceFirstDepositResponse struct {
	Result []AllianceFirstDeposit `json:"result"`
	Total  int64                  `json:"total"`
}

type GetFirstDepositAliianceListRequest struct {
	Page  int    `form:"page" default:"1"`
	Limit int    `form:"limit" default:"10"`
	From  string `form:"from" time_format:"2006-01-02" default:"2023-04-01"`
	To    string `form:"to" time_format:"2006-01-02" default:"2023-04-01"`
}
type GetUserFirstDepositAliianceListRequest struct {
	AdminId int64  `form:"adminId" validate:"required" json:"-"`
	UserId  int64  `form:"userId" validate:"required"`
	Page    int    `form:"page" default:"1"`
	Limit   int    `form:"limit" default:"10"`
	From    string `form:"from" time_format:"2006-01-02" default:"2023-04-01"`
	To      string `form:"to" time_format:"2006-01-02" default:"2023-04-01"`
}

type GetFirstDepositAliianceSummaryRequest struct {
	From string `form:"from" time_format:"2006-01-02" default:"2023-04-01"`
	To   string `form:"to" time_format:"2006-01-02" default:"2023-04-01"`
}
type GetUserFirstDepositAliianceSummaryRequest struct {
	AdminId int64  `form:"adminId" validate:"required" json:"-"`
	UserId  int64  `form:"userId" validate:"required"`
	From    string `form:"from" time_format:"2006-01-02" default:"2023-04-01"`
	To      string `form:"to" time_format:"2006-01-02" default:"2023-04-01"`
}

type GetFirstDepositAliianceListResponse struct {
	Id             int64      `json:"id"`
	Phone          string     `json:"phone"`
	MemberCode     string     `json:"memberCode"`
	Credit         float64    `json:"credit"`
	FirstDepositAt *time.Time `json:"firstDepositAt"`
	RegisterDate   string     `json:"registerDate"`
}

type GetFirstDepositAllianceSummary struct {
	FirstDepositCount       int64   `json:"firstDepositCount"`
	NoMemberCode            int64   `json:"noMemberCode"`
	HaveMemberCode          int64   `json:"haveMemberCode"`
	RecommendTotal          int64   `json:"recommendTotal"`
	TotalFirstDepositAmount float64 `json:"totalFirstDepositAmount"`
}

type GetUserAliianceSummaryRequest struct {
	AdminId int64 `form:"adminId" validate:"required" json:"-"`
	UserId  int64 `form:"userId" validate:"required"`
}
