package model

import "time"

type GameDetail struct {
	VendorCode string `json:"vendorCode"`
	GameName   string `json:"gameName"`
	GameCode   string `json:"gameCode"`
	ImageUrl   string `json:"imageUrl"`
	Image      string `json:"image"`
	IsShowMain bool   `json:"isShowMain"`
}

//	type gameCode struct {
//		Desktop string `json:"desktop"`
//	}
//

type GameDetailPgsoft struct {
	GameName string `json:"gameName"`
	GameCode string `json:"gameCode"`
	GameImg  string `json:"gameImg"`
	GameType string `json:"gameType"`
}

type GameDetailBetSoft struct {
	GameName  string `json:"gameName"`
	GameCode  int    `json:"gameCode"`
	ImageIcon string `json:"imageIcon"`
}

type GameDetailBge struct {
	GameName  string `json:"gameName"`
	GameCode  int    `json:"gameCode"`
	ImageIcon string `json:"imageIcon"`
}

type GameDetailBng struct {
	GameName  string `json:"gameName"`
	GameCode  string `json:"gameCode"`
	ImageIcon string `json:"imageIcon"`
	Image     string `json:"image"`
}

type GameDetailCq9 struct {
	GameName  string      `json:"gameName"`
	GameCode  interface{} `json:"gameCode"`
	ImageIcon string      `json:"imageIcon"`
}

type GameDetailDragonGaming struct {
	GameName  string `json:"gameName"`
	GameCode  string `json:"gameCode"`
	ImageIcon string `json:"imageIcon"`
}

type GameDetailDreamTech struct {
	I18n      interface{} `json:"i18n"`
	GameCode  string      `json:"gameCode"`
	ImageIcon string      `json:"imageIcon"`
}

type GameDetailCQ9V2 struct {
	GameName  string `json:"gameName"`
	GameCode  string `json:"gameCode"`
	ImageIcon string `json:"imageIcon"`
}

type GameProvider struct {
	VendorCode string `json:"vendorCode"`
	VendorName string `json:"vendorName"`
}

type GamePGSlot struct {
	Result []struct {
		GameId   int    `json:"gameId"`
		GameName string `json:"gameName"`
		GameCode string `json:"gameCode"`
		Status   int    `json:"status"`
		Category int    `json:"category"`
	} `json:"Result"`
	TargetUrl interface{} `json:"TargetUrl"`
	Success   bool        `json:"Success"`
	Error     interface{} `json:"Error"`
	Message   string      `json:"Message"`
}
type GameCategory struct {
	Name             string `json:"name"`
	VendorCode       string `json:"vendorCode"`
	ImageName        string `json:"imageName"`
	ImageUrl         string `json:"imageUrl"`
	Category         string `json:"category"`
	IsActive         int    `json:"isActive"`
	IsShowMain       bool   `json:"isShowMain"`
	TotalPlayed      int64  `json:"totalPlayed"`
	IsUseNewPgh      bool   `json:"isUseNewPgh"`
	IsUseNewAgentCtw bool   `json:"isUseNewAgentCtw"`
	IsGameMaintain   bool   `json:"isGameMaintain"`
}

type GameCategoryResponse struct {
	Result    []GameCategory `json:"result"`
	ExpiredAt time.Time      `json:"expiredAt"`
}

type GameConfiguration struct {
	OpenGameNewTab bool `json:"openGameNewTab"`
}

type GamelistResponse struct {
	Result    []GameDetail `json:"result"`
	ExpiredAt time.Time    `json:"expiredAt"`
}

type AgcVendorMaintenanceList struct {
	Partner   string `json:"Partner"`
	Sign      string `json:"Sign"`
	TimeStamp int64  `json:"TimeStamp"`
}

type AgcVendorMaintenanceListResponse struct {
	Error   int64                            `json:"Error"`
	Message string                           `json:"Message"`
	UTC     string                           `json:"UTC"`
	Status  []AgcVendorMaintenanceListStatus `json:"Status"`
}

type AgcVendorMaintenanceListStatus struct {
	Vendor string `json:"Vendor"`
	Status int64  `json:"Status"`
}
