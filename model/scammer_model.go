package model

import (
	"time"
)

type Scammer struct {
	Id        int64
	UserId    int64
	Reason    string
	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt time.Time
}

type CreateScammer struct {
	Id     int64  `json:"-"`
	Reason string `json:"reason" validate:"max=255"`
	UserId int64  `json:"userId"`
}

type ScammertList struct {
	Id          int64      `json:"id"`
	UserId      int64      `json:"userId"`
	Fullname    string     `json:"fullname"`
	BankId      int64      `json:"bankId"`
	BankName    string     `json:"bankName"`
	BankAccount string     `json:"bankAccount"`
	Phone       string     `json:"phone"`
	Reason      string     `json:"reason"`
	CreatedAt   *time.Time `json:"createdAt"`
}

type ScammerDetail struct {
	Id   int64  `json:"id"`
	Name string `json:"name"`
}

type ScammerQuery struct {
	Page   int    `form:"page" default:"1"`
	Limit  int    `form:"limit" default:"10"`
	From   string `form:"from" time_format:"2006-01-02 "`
	To     string `form:"to" time_format:"2006-01-02 "`
	BankId *int64 `form:"bankId"`
	Word   string `form:"word"`
}

type ScammerUpdateUser struct {
	Id           int64      `json:"id"`
	UserStatusId int64      `json:"userStatusId"`
	DeletedAt    *time.Time `json:"deletedAt"`
}
type ScammerDelete struct {
	Id           int64 `json:"id"`
	UserStatusId int64 `json:"userStatusId"`
}

type ScammerSummary struct {
	TotalCount int64 `json:"totalCount"`
}
