package model

import (
	"time"
)

const (
	FLASHPAY_DEFMIN_DEPOSIT_AMOUNT  = 20
	FLASHPAY_DEFMAX_DEPOSIT_AMOUNT  = 50000
	FLASHPAY_DEFMIN_WITHDRAW_AMOUNT = 50
	FLASHPAY_DEFMAX_WITHDRAW_AMOUNT = 100000
)

type FlashpayWebhookDepositResponse struct {
	Sign               string `json:"sign"`
	Timestamp          int64  `json:"timestamp"`
	OrderNo            string `json:"order_no"`
	MchOrderNo         string `json:"mch_order_no"`
	Status             int    `json:"status"`
	Amount             string `json:"amount"`
	PayTime            string `json:"pay_time"`
	RepayAccountBank   string `json:"repay_account_bank"`
	RepayAccountNumber string `json:"repay_account_number"`
	AccountNumber      string `json:"account_number"`
	AccountBank        string `json:"account_bank"`
}

type FlashpayErrorRemoteResponse struct {
	StatusCode int    `json:"statusCode"`
	Message    string `json:"message"`
}
type FlashpayError2RemoteResponse struct {
	Message    string `json:"message"`
	Error      string `json:"error"`
	StatusCode int    `json:"statusCode"`
}

type FlashpayToken struct {
	Id          int64      `json:"id"`
	AccessToken string     `json:"accessToken"`
	ExpireAt    time.Time  `json:"expireAt"`
	CreateBy    int64      `json:"createBy"`
	CreatedAt   time.Time  `json:"createdAt"`
	UpdatedAt   *time.Time `json:"updatedAt"`
}
type FlashpayTokenCreateBody struct {
	Id          int64     `json:"id"`
	AccessToken string    `json:"accessToken"`
	ExpireAt    time.Time `json:"expireAt"`
	CreateBy    int64     `json:"createBy"`
}

type FlashpayTokenCreateRemoteRequest struct {
	SecretKey string `json:"secretKey" validate:"required"`
	Timestamp int64  `json:"timestamp" validate:"required"`
	Payload   struct {
		MerchantId string `json:"merchantId" validate:"required"`
		ClientId   string `json:"clientId" validate:"required"`
	} `json:"payload" validate:"required"`
}
type FlashpayTokenCreateRemoteResponse struct {
	Status    string `json:"status"`
	Signature string `json:"signature"`
	Timestamp int64  `json:"timestamp"`
}

type FlashpayCustomerCreateRemoteRequest struct {
	Name      string `json:"name" validate:"required"`
	BankCode  string `json:"bankCode" validate:"required"`
	AccountNo string `json:"accountNo" validate:"required"`
}
type FlashpayCustomerCreateRemoteResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Code    string `json:"code"`
	Data    struct {
		Partner      string   `json:"partner"`
		CustomerUuid string   `json:"customerUuid"`
		ClientCode   string   `json:"clientCode"`
		Name         string   `json:"name"`
		SearchName   []string `json:"searchName"`
		AccountNo    string   `json:"accountNo"`
		BankCode     string   `json:"bankCode"`
		Status       string   `json:"status"`
		CreatedAt    int64    `json:"createdAt"`
		UpdatedAt    int64    `json:"updatedAt"`
	} `json:"data"`
}
type FlashpayCustomerUpdateRemoteRequest struct {
	CustomerUuid string `json:"customerUuid" validate:"required"`
	Name         string `json:"name" validate:"required"`
	BankCode     string `json:"bankCode" validate:"required"`
	AccountNo    string `json:"accountNo" validate:"required"`
}
type FlashpayCustomerUpdateRemoteResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Code    string `json:"code"`
	Data    struct {
		Partner      string   `json:"partner"`
		CustomerUuid string   `json:"customerUuid"`
		ClientCode   string   `json:"clientCode"`
		Name         string   `json:"name"`
		SearchName   []string `json:"searchName"`
		AccountNo    string   `json:"accountNo"`
		BankCode     string   `json:"bankCode"`
		Status       string   `json:"status"`
		CreatedAt    int64    `json:"createdAt"`
		UpdatedAt    int64    `json:"updatedAt"`
	} `json:"data"`
}

type FlashpayDepositCreateRemoteRequest struct {
	ClientId          string  `json:"clientId" validate:"required"`
	MerchantId        string  `json:"merchantId" validate:"required"`
	TransactionId     string  `json:"transactionId" validate:"required"`
	BankAccountNumber string  `json:"bankAccountNumber" validate:"required"`
	BankName          string  `json:"bankName" validate:"required"`
	Name              string  `json:"name" validate:"required"`
	Amount            float64 `json:"amount" validate:"required"`
	CallbackUrl       string  `json:"callbackUrl" validate:"required"`
	Type              string  `json:"type" validate:"required"`
	Timeout           int     `json:"timeout" validate:"required"`
	Signature         string  `json:"signature" validate:"required"`
	Timestamp         int64   `json:"timestamp" validate:"required"`
}

type FlashpayDepositCreateRemoteResponse struct {
	Status  string `json:"status"`
	Message string `json:"message"`
	Data    struct {
		ClientId          string  `json:"clientId"`
		MerchantId        string  `json:"merchantId"`
		ReferenceId       string  `json:"referenceId"`
		TransactionId     string  `json:"transactionId"`
		Status            string  `json:"status"`
		Amount            float64 `json:"amount"`
		DepositAmount     float64 `json:"depositAmount"`
		Qrcode            string  `json:"qrcode"`
		BankAccountNumber string  `json:"bankAccountNumber"`
		BankAccountName   string  `json:"bankAccountName"`
		BankName          string  `json:"bankName"`
		BankCode          string  `json:"bankCode"`
		PromptpayNumber   string  `json:"promptpayNumber"`
		ExpireDate        string  `json:"expireDate"`
		CustomerData      struct {
			BankAccountNumber string `json:"bankAccountNumber"`
			BankName          string `json:"bankName"`
			Name              string `json:"name"`
		} `json:"customerData"`
	} `json:"data"`
}

type FlashpayCheckBalanceRemoteResponse struct {
	Status string `json:"status"`
	Data   struct {
		MerchantId     string  `json:"merchantId"`
		Name           string  `json:"name"`
		Balance        float64 `json:"balance"`
		OperateBalance float64 `json:"operateBalance"`
		ParkingBalance float64 `json:"parkingBalance"`
		FreezeBalance  float64 `json:"freezeBalance"`
		UpdatedAt      string  `json:"updatedAt"`
	} `json:"data"`
}

type FlashpayGetOrderRemoteResponse struct {
	Success bool `json:"success"`
	Code    int  `json:"code"`
	Data    struct {
		OrderNo       string  `json:"orderNo"`
		ReceiveAddr   string  `json:"receiveAddr"`
		ChainName     string  `json:"chainName"`
		CoinUnit      string  `json:"coinUnit"`
		RequestAmount float64 `json:"requestAmount"`
		PayAmount     float64 `json:"payAmount"`
		Status        string  `json:"status"`
		Sign          string  `json:"sign"`
	} `json:"data"`
}
type FlashpayWithdrawCreateRemoteRequest struct {
	ClientId          string  `json:"clientId" validate:"required"`
	MerchantId        string  `json:"merchantId" validate:"required"`
	TransactionId     string  `json:"transactionId" validate:"required"`
	BankAccountNumber string  `json:"bankAccountNumber" validate:"required"`
	Amount            float64 `json:"amount" validate:"required"`
	BankName          string  `json:"bankName" validate:"required"`
	Name              string  `json:"name" validate:"required"`
	Phone             string  `json:"phone" validate:"required"`
	CallbackUrl       string  `json:"callbackUrl" validate:"required"`
	Signature         string  `json:"signature" validate:"required"`
	Timestamp         int64   `json:"timestamp" validate:"required"`
}
type FlashpayWithdrawCreateRemoteResponse struct {
	Status  string `json:"status"`
	Message string `json:"message"`
	Data    struct {
		ClientId      string  `json:"clientId"`
		MerchantId    string  `json:"merchantId"`
		ReferenceId   string  `json:"referenceId"`
		TransactionId string  `json:"transactionId"`
		Amount        float64 `json:"amount"`
		Status        string  `json:"status"`
		CustomerData  struct {
			BankAccountNumber string `json:"bankAccountNumber"`
			BankName          string `json:"bankName"`
			Name              string `json:"name"`
			Phone             string `json:"phone"`
		} `json:"customerData"`
		SystemBankData struct {
			BankAccountNumber string `json:"bankAccountNumber"`
			BankName          string `json:"bankName"`
			BankCode          string `json:"bankCode"`
			Name              string `json:"name"`
		} `json:"systemBankData"`
	} `json:"data"`
}

type FlashpayWebhookRequest struct {
	JsonPayload string `json:"jsonPayload"`
}
type FlashpayWebhookCreateBody struct {
	Id          int64  `json:"id"`
	Name        string `json:"name"`
	JsonPayload string `json:"jsonPayload"`
}

type FlashpayWebhookResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

type FlashpayDepositWebhookResponse struct {
	ReferenceId       string  `json:"referenceId"`
	TransactionId     string  `json:"transactionId"`
	ClientId          string  `json:"clientId"`
	MerchantId        string  `json:"merchantId"`
	WalletId          string  `json:"walletId"`
	BankCode          string  `json:"bankCode"`
	BankName          string  `json:"bankName"`
	BankAccountNumber string  `json:"bankAccountNumber"`
	BankAccountName   string  `json:"bankAccountName"`
	Amount            float64 `json:"amount"`
	Status            string  `json:"status"`
	Timestamp         int64   `json:"timestamp"`
	MatchTimestamp    int64   `json:"matchTimestamp"`
	Type              string  `json:"type"`
	Hash              string  `json:"hash"`
}
type FlashpayWithdrawWebhookResponse struct {
	ReferenceId   string `json:"referenceId"`
	TransactionId string `json:"transactionId"`
	Status        string `json:"status"`
	Message       string `json:"message"`
	Qrcode        string `json:"qrcode"`
	Hash          string `json:"hash"`
}

type FlashpayCustomerDepositInfo struct {
	Name        string `json:"name"`
	DisplayName string `json:"displayName"`
	ShopName    string `json:"shopName"`
	MinAmount   int64  `json:"minAmount"`
	MaxAmount   int64  `json:"maxAmount"`
}
type FlashpayDepositCreateRequest struct {
	UserId int64   `form:"userId" json:"-" validate:"required"`
	Amount float64 `form:"amount" json:"amount" binding:"required,min=1" validate:"required,min=1"`
}
type FlashpayWithdrawCreateRequest struct {
	RefId  int64   `form:"refId" validate:"required"`
	UserId int64   `form:"userId" json:"-" validate:"required"`
	Amount float64 `form:"amount" binding:"required,min=1" validate:"required,min=1"`
}

const (
	FLASHPAY_ORDER_TYPE_DEPOSIT  = 1
	FLASHPAY_ORDER_TYPE_WITHDRAW = 2
)

type FlashpayCustomer struct {
	Id           int64  `json:"id"`
	UserId       int64  `json:"userId"`
	CustomerUuid string `json:"customerUuid"`
	FullName     string `json:"fullName"`
	BankCode     string `json:"bankCode"`
	AccountNo    string `json:"accountNo"`
	AccountName  string `json:"accountName"`
}
type FlashpayCustomerListRequest struct {
	Page    int    `form:"page" default:"1" min:"1"`
	Limit   int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol string `form:"sortCol"`
	SortAsc string `form:"sortAsc"`
}
type FlashpayCustomerResponse struct {
	Id           int64  `json:"id"`
	UserId       int64  `json:"userId"`
	CustomerUuid string `json:"customerUuid"`
	FullName     string `json:"fullName"`
	BankCode     string `json:"bankCode"`
	AccountNo    string `json:"accountNo"`
	AccountName  string `json:"accountName"`
}
type FlashpayCustomerCreateBody struct {
	Id           int64  `json:"id"`
	UserId       int64  `json:"userId"`
	CustomerUuid string `json:"customerUuid"`
	FullName     string `json:"fullName"`
	BankCode     string `json:"bankCode"`
	AccountNo    string `json:"accountNo"`
	AccountName  string `json:"accountName"`
}
type FlashpayCustomerUpdateBody struct {
	CustomerUuid *string `json:"customerUuid"`
	FullName     *string `json:"fullName"`
	BankCode     *string `json:"bankCode"`
	AccountNo    *string `json:"accountNo"`
	AccountName  *string `json:"accountName"`
}

type FlashpayOrder struct {
	Id                int64      `json:"id"`
	UserId            int64      `json:"userId"`
	OrderTypeId       int64      `json:"orderTypeId"`
	RefId             *int64     `json:"refId"`
	OrderNo           string     `json:"orderNo"`
	Amount            float64    `json:"amount"`
	TransactionNo     *string    `json:"transactionNo"`
	TransactionDate   *time.Time `json:"transactionDate"`
	TransactionStatus *string    `json:"transactionStatus"`
	PaymentAt         *time.Time `json:"paymentAt"`
	QrPromptpay       *string    `json:"qrPromptpay"`
	PaymentPageUrl    string     `json:"paymentPageUrl"`
	Remark            *string    `json:"remark"`
	ApiRemark         *string    `json:"apiRemark"`
	CreatedAt         time.Time  `json:"createdAt"`
	UpdatedAt         *time.Time `json:"updatedAt"`
}
type FlashpayOrderListRequest struct {
	UserId        *int64 `form:"userId"`
	OrderTypeId   *int64 `form:"orderTypeId"`
	OrderNo       string `form:"orderNo"`
	TransactionNo string `form:"transactionNo"`
	Amount        string `form:"amount"`
	Status        string `form:"status"`
	Page          int    `form:"page" default:"1" min:"1"`
	Limit         int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol       string `form:"sortCol"`
	SortAsc       string `form:"sortAsc"`
}
type FlashpayOrderResponse struct {
	Id                    int64      `json:"id"`
	UserId                int64      `json:"userId"`
	OrderTypeId           int64      `json:"orderTypeId"`
	OrderTypeName         string     `json:"orderTypeName"`
	RefId                 *int64     `json:"refId"`
	OrderNo               string     `json:"orderNo"`
	Amount                float64    `json:"amount"`
	TransactionNo         *string    `json:"transactionNo"`
	TransactionDate       *time.Time `json:"transactionDate"`
	TransactionStatus     *string    `json:"transactionStatus"`
	PaymentAt             *time.Time `json:"paymentAt"`
	BankTransactionId     *string    `json:"bankTransactionId"`
	BankTransactionStatus string     `json:"bankTransactionStatus"`
	QrPromptpay           string     `json:"qrPromptpay"`
	PaymentPageUrl        string     `json:"paymentPageUrl"`
	Remark                *string    `json:"remark"`
	ApiRemark             *string    `json:"apiRemark"`
	CreatedAt             time.Time  `json:"createdAt"`
	UpdatedAt             *time.Time `json:"updatedAt"`
}
type FlashpayOrderWebResponse struct {
	UserId            int64     `json:"userId"`
	OrderNo           string    `json:"orderNo"`
	Amount            float64   `json:"amount"`
	TransferAmount    float64   `json:"transferAmount"`
	TransactionStatus string    `json:"transactionStatus"`
	QrCode            string    `json:"qrCode"`
	QrBase64          string    `json:"qrBase64"`
	PaymentPageUrl    string    `json:"paymentPageUrl"`
	CreatedAt         time.Time `json:"createdAt"`
}
type FlashpayOrderQrResponse struct {
	Id       int64   `json:"id"`
	OrderNo  string  `json:"orderNo"`
	Amount   float64 `json:"amount"`
	QrBase64 string  `json:"qrBase64"`
}
type FlashpayOrderCreateRequest struct {
	UserId *int64  `form:"userId"`
	Amount float64 `form:"amount"`
}
type FlashpayOrderCreateBody struct {
	Id                int64   `json:"id"`
	UserId            int64   `json:"userId"`
	OrderTypeId       int64   `json:"orderTypeId"`
	RefId             *int64  `json:"refId"`
	OrderNo           string  `json:"orderNo"`
	Amount            float64 `json:"amount"`
	TransactionStatus string  `json:"transactionStatus"`
}
type FlashpayOrderUpdateBody struct {
	TransactionNo     string    `json:"transactionNo"`
	TransactionDate   time.Time `json:"transactionDate"`
	TransactionStatus string    `json:"transactionStatus"`
	TransferAmount    float64   `json:"transferAmount"`
	QrPromptpay       string    `json:"qrPromptpay"`
	PaymentPageUrl    string    `json:"paymentPageUrl"`
}
