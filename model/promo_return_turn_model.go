package model

import (
	"time"
)

const (
	PROMO_RETURNTURN_TYPE_LOSER      = 1
	PROMO_RETURNTURN_CUT_TYPE_DAILY  = 1
	PROMO_RETURNTURN_CUT_TYPE_WEEKLY = 2
	PROMO_RETURNTURN_STATUS_PENDING  = int64(1)
	PROMO_RETURNTURN_STATUS_READY    = int64(2)
	PROMO_RETURNTURN_STATUS_TAKEN    = int64(3)
	PROMO_RETURNTURN_STATUS_EXPIRED  = int64(4)
)

type PromotionReturnTurnSetting struct {
	Id                     int64      `json:"id"`
	ReturnTypeId           int64      `json:"returnTypeId"`
	CutTypeId              int64      `json:"cutTypeId"`
	MinTurnPrice           float64    `json:"minTurnPrice"`
	MaxReturnPrice         float64    `json:"maxReturnPrice"`
	CreditExpireDays       int        `json:"creditExpireDays"`
	CalcOnSport            bool       `json:"calcOnSport"`
	ReturnSportPercent     float64    `json:"returnSportPercent"`
	CalcOnCasino           bool       `json:"calcOnCasino"`
	ReturnCasinoPercent    float64    `json:"returnCasinoPercent"`
	CalcOnGame             bool       `json:"calcOnGame"`
	ReturnGamePercent      float64    `json:"returnGamePercent"`
	CalcOnLottery          bool       `json:"calcOnLottery"`
	ReturnLotteryPercent   float64    `json:"returnLotteryPercent"`
	CalcOnP2p              bool       `json:"calcOnP2p"`
	ReturnP2pPercent       float64    `json:"returnP2pPercent"`
	CalcOnFinancial        bool       `json:"calcOnFinancial"`
	ReturnFinancialPercent float64    `json:"returnFinancialPercent"`
	Detail                 string     `json:"detail"`
	IsEnabled              bool       `json:"isEnable"`
	CreatedAt              time.Time  `json:"createdAt"`
	UpdatedAt              *time.Time `json:"updatedAt"`
}
type PromotionReturnTurnSettingResponse struct {
	Id                     int64      `json:"id"`
	ReturnTypeId           int64      `json:"returnTypeId"`
	CutTypeId              int64      `json:"cutTypeId"`
	MinTurnPrice           float64    `json:"minTurnPrice"`
	MaxReturnPrice         float64    `json:"maxReturnPrice"`
	CreditExpireDays       int        `json:"creditExpireDays"`
	CalcOnSport            bool       `json:"calcOnSport"`
	ReturnSportPercent     float64    `json:"returnSportPercent"`
	CalcOnCasino           bool       `json:"calcOnCasino"`
	ReturnCasinoPercent    float64    `json:"returnCasinoPercent"`
	CalcOnGame             bool       `json:"calcOnGame"`
	ReturnGamePercent      float64    `json:"returnGamePercent"`
	CalcOnLottery          bool       `json:"calcOnLottery"`
	ReturnLotteryPercent   float64    `json:"returnLotteryPercent"`
	CalcOnP2p              bool       `json:"calcOnP2p"`
	ReturnP2pPercent       float64    `json:"returnP2pPercent"`
	CalcOnFinancial        bool       `json:"calcOnFinancial"`
	ReturnFinancialPercent float64    `json:"returnFinancialPercent"`
	Detail                 string     `json:"detail"`
	IsEnabled              bool       `json:"isEnable"`
	CreatedAt              time.Time  `json:"createdAt"`
	UpdatedAt              *time.Time `json:"updatedAt"`
	CacheExpiredAt         time.Time  `json:"cacheExpiredAt"`
}

type PromotionReturnTurnSettingCreateBody struct {
	Id                     int64   `json:"id"`
	ReturnTypeId           int64   `json:"returnTypeId"`
	CutTypeId              int64   `json:"cutTypeId"`
	MinTurnPrice           float64 `json:"minTurnPrice"`
	MaxReturnPrice         float64 `json:"maxReturnPrice"`
	CreditExpireDays       int     `json:"creditExpireDays"`
	CalcOnSport            bool    `json:"calcOnSport"`
	ReturnSportPercent     float64 `json:"returnSportPercent"`
	CalcOnCasino           bool    `json:"calcOnCasino"`
	ReturnCasinoPercent    float64 `json:"returnCasinoPercent"`
	CalcOnGame             bool    `json:"calcOnGame"`
	ReturnGamePercent      float64 `json:"returnGamePercent"`
	CalcOnLottery          bool    `json:"calcOnLottery"`
	ReturnLotteryPercent   float64 `json:"returnLotteryPercent"`
	CalcOnP2p              bool    `json:"calcOnP2p"`
	ReturnP2pPercent       float64 `json:"returnP2pPercent"`
	CalcOnFinancial        bool    `json:"calcOnFinancial"`
	ReturnFinancialPercent float64 `json:"returnFinancialPercent"`
	Detail                 string  `json:"detail"`
}
type PromotionReturnTurnSettingUpdateRequest struct {
	ReturnTypeId           *int64   `json:"returnTypeId"`
	CutTypeId              *int64   `json:"cutTypeId"`
	MinTurnPrice           *float64 `json:"minTurnPrice"`
	MaxReturnPrice         *float64 `json:"maxReturnPrice"`
	CreditExpireDays       *int     `json:"creditExpireDays"`
	CalcOnSport            *bool    `json:"calcOnSport"`
	ReturnSportPercent     *float64 `json:"returnSportPercent"`
	CalcOnCasino           *bool    `json:"calcOnCasino"`
	ReturnCasinoPercent    *float64 `json:"returnCasinoPercent"`
	CalcOnGame             *bool    `json:"calcOnGame"`
	ReturnGamePercent      *float64 `json:"returnGamePercent"`
	CalcOnLottery          *bool    `json:"calcOnLottery"`
	ReturnLotteryPercent   *float64 `json:"returnLotteryPercent"`
	CalcOnP2p              *bool    `json:"calcOnP2p"`
	ReturnP2pPercent       *float64 `json:"returnP2pPercent"`
	CalcOnFinancial        *bool    `json:"calcOnFinancial"`
	ReturnFinancialPercent *float64 `json:"returnFinancialPercent"`
	Detail                 *string  `json:"detail"`
	IsEnabled              *bool    `json:"isEnable"`
}
type PromotionReturnTurnSettingUpdateBody struct {
	ReturnTypeId           *int64   `json:"returnTypeId"`
	CutTypeId              *int64   `json:"cutTypeId"`
	MinTurnPrice           *float64 `json:"minTurnPrice"`
	MaxReturnPrice         *float64 `json:"maxReturnPrice"`
	CreditExpireDays       *int     `json:"creditExpireDays"`
	CalcOnSport            *bool    `json:"calcOnSport"`
	ReturnSportPercent     *float64 `json:"returnSportPercent"`
	CalcOnCasino           *bool    `json:"calcOnCasino"`
	ReturnCasinoPercent    *float64 `json:"returnCasinoPercent"`
	CalcOnGame             *bool    `json:"calcOnGame"`
	ReturnGamePercent      *float64 `json:"returnGamePercent"`
	CalcOnLottery          *bool    `json:"calcOnLottery"`
	ReturnLotteryPercent   *float64 `json:"returnLotteryPercent"`
	CalcOnP2p              *bool    `json:"calcOnP2p"`
	ReturnP2pPercent       *float64 `json:"returnP2pPercent"`
	CalcOnFinancial        *bool    `json:"calcOnFinancial"`
	ReturnFinancialPercent *float64 `json:"returnFinancialPercent"`
	Detail                 string   `json:"detail"`
	IsEnabled              *bool    `json:"isEnable"`
}

type PromotionReturnTurnUserDetail struct {
	UserId          int64                            `json:"userId"`
	StatusId        int64                            `json:"statusId"`
	StatusName      string                           `json:"statusName"`
	ReturnPrice     float64                          `json:"returnPrice"`
	Detail          string                           `json:"detail"`
	RelatedItemList []PromotionReturnTurnTransaction `json:"relatedItemList"`
}
type PromotionReturnTurnTransaction struct {
	Id                     int64      `json:"id"`
	UserId                 int64      `json:"userId"`
	DailyKey               string     `json:"dailyKey"`
	OfDate                 string     `json:"ofDate"`
	TotalTurnAmount        float64    `json:"totalTurnAmount"`
	TotalTurnSport         float64    `json:"totalTurnSport"`
	ReturnSportPercent     float64    `json:"returnSportPercent"`
	TotalTurnCasino        float64    `json:"totalTurnCasino"`
	ReturnCasinoPercent    float64    `json:"returnCasinoPercent"`
	TotalTurnGame          float64    `json:"totalTurnGame"`
	ReturnGamePercent      float64    `json:"returnGamePercent"`
	TotalTurnLottery       float64    `json:"totalTurnLottery"`
	ReturnLotteryPercent   float64    `json:"returnLotteryPercent"`
	TotalTurnP2p           float64    `json:"totalTurnP2p"`
	ReturnP2pPercent       float64    `json:"returnP2pPercent"`
	TotalTurnFinancial     float64    `json:"totalTurnFinancial"`
	ReturnFinancialPercent float64    `json:"returnFinancialPercent"`
	StatusId               int64      `json:"statusId"`
	StatusName             string     `json:"statusName"`
	GameDetail             string     `json:"gameDetail"`
	ReturnTypeId           int64      `json:"returnTypeId"`
	CutTypeId              int64      `json:"cutTypeId"`
	MinTurnPrice           float64    `json:"minTurnPrice"`
	MaxReturnPrice         float64    `json:"maxReturnPrice"`
	CreditExpireDays       int        `json:"creditExpireDays"`
	ReturnPrice            float64    `json:"returnPrice"`
	CalcAt                 *time.Time `json:"calcAt"`
	TakeAt                 *time.Time `json:"takeAt"`
	TakenPrice             float64    `json:"takenPrice"`
	CreatedAt              time.Time  `json:"createdAt"`
	UpdatedAt              *time.Time `json:"updatedAt"`
}
type PromotionReturnTurnTransactionListRequest struct {
	StatusId *int64 `form:"statusId"`
	UserId   int64  `form:"userId" json:"-"`
	Page     int    `form:"page" default:"1" min:"1"`
	Limit    int    `form:"limit" default:"10" min:"1" max:"100"`
}

type PromotionReturnTurnHistoryUserListRequest struct {
	UserIds  []int64 `form:"userIds" json:"-"`
	DateType string  `form:"dateType"`
	FromDate string  `form:"fromDate"`
	ToDate   string  `form:"toDate"`
	Search   string  `form:"search"`
	Page     int     `form:"page" default:"1" min:"1"`
	Limit    int     `form:"limit" default:"10" min:"1" max:"100"`
	SortCol  string  `form:"sortCol"`
	SortAsc  string  `form:"sortAsc"`
}
type PromotionReturnTurnHistoryUserSummaryResponse struct {
	DateType        string  `json:"dateType"`
	FromDate        string  `json:"fromDate"`
	ToDate          string  `json:"toDate"`
	TotalTurnAmount float64 `json:"totalTurnAmount"`
	TotalTakenPrice float64 `json:"totalTakenPrice"`
}
type PromotionReturnTurnHistoryUserResponse struct {
	Id              int64   `json:"id"`
	MemberCode      string  `json:"memberCode"`
	Username        string  `json:"username"`
	Fullname        string  `json:"fullname"`
	TotalTurnAmount float64 `json:"totalTurnAmount"`
	TotalTakenPrice float64 `json:"totalTakenPrice"`
}
type PromotionReturnTurnHistoryUserTotalResponse struct {
	UserId          int64   `json:"userId"`
	TotalTurnAmount float64 `json:"totalTurnAmount"`
	TotalTakenPrice float64 `json:"totalTakenPrice"`
}
type PromotionReturnTurnHistoryListRequest struct {
	UserId   *int64 `form:"userId"`
	FromDate string `form:"fromDate"`
	ToDate   string `form:"toDate"`
	StatusId *int64 `form:"statusId"`
	Page     int    `form:"page" default:"1" min:"1"`
	Limit    int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol  string `form:"sortCol"`
	SortAsc  string `form:"sortAsc"`
}
type PromotionReturnTurnHistoryReponse struct {
	Id                     int64   `json:"id"`
	UserId                 int64   `json:"userId"`
	OfDate                 string  `json:"ofDate"`
	TotalTurnAmount        float64 `json:"totalTurnAmount"`
	TotalTurnSport         float64 `json:"totalTurnSport"`
	ReturnSportPercent     float64 `json:"returnSportPercent"`
	TotalTurnCasino        float64 `json:"totalTurnCasino"`
	ReturnCasinoPercent    float64 `json:"returnCasinoPercent"`
	TotalTurnGame          float64 `json:"totalTurnGame"`
	ReturnGamePercent      float64 `json:"returnGamePercent"`
	TotalTurnLottery       float64 `json:"totalTurnLottery"`
	ReturnLotteryPercent   float64 `json:"returnLotteryPercent"`
	TotalTurnP2p           float64 `json:"totalTurnP2p"`
	ReturnP2pPercent       float64 `json:"returnP2pPercent"`
	TotalTurnFinancial     float64 `json:"totalTurnFinancial"`
	ReturnFinancialPercent float64 `json:"returnFinancialPercent"`
	CutTypeName            string  `json:"cutTypeName"`
	GameDetail             string  `json:"gameDetail"`
	ReturnPrice            float64 `json:"returnPrice"`
	CreditExpireDays       int     `json:"creditExpireDays"`
	CreditExpireAt         string  `json:"creditExpireAt"`
	LogStatus              string  `json:"logStatus"`
}
type PromotionReturnTurnTransactionDailyKey struct {
	Id              int64     `json:"id"`
	UserId          int64     `json:"userId"`
	DailyKey        string    `json:"dailyKey"`
	TotalTurnAmount float64   `json:"totalTurnAmount"`
	CreatedAt       time.Time `json:"createdAt"`
}
type PromotionReturnTurnTransactionCreateBody struct {
	Id                     int64      `json:"id"`
	UserId                 int64      `json:"userId"`
	DailyKey               string     `json:"dailyKey"`
	TotalTurnAmount        float64    `json:"totalTurnAmount"`
	TotalTurnSport         float64    `json:"totalTurnSport"`
	ReturnSportPercent     float64    `json:"returnSportPercent"`
	TotalTurnCasino        float64    `json:"totalTurnCasino"`
	ReturnCasinoPercent    float64    `json:"returnCasinoPercent"`
	TotalTurnGame          float64    `json:"totalTurnGame"`
	ReturnGamePercent      float64    `json:"returnGamePercent"`
	TotalTurnLottery       float64    `json:"totalTurnLottery"`
	ReturnLotteryPercent   float64    `json:"returnLotteryPercent"`
	TotalTurnP2p           float64    `json:"totalTurnP2p"`
	ReturnP2pPercent       float64    `json:"returnP2pPercent"`
	TotalTurnFinancial     float64    `json:"totalTurnFinancial"`
	ReturnFinancialPercent float64    `json:"returnFinancialPercent"`
	OfDate                 string     `json:"ofDate"`
	GameDetail             string     `json:"gameDetail"`
	ReturnTypeId           int64      `json:"returnTypeId"`
	CutTypeId              int64      `json:"cutTypeId"`
	MinTurnPrice           float64    `json:"minTurnPrice"`
	MaxReturnPrice         float64    `json:"maxReturnPrice"`
	CreditExpireDays       int        `json:"creditExpireDays"`
	ReturnPrice            float64    `json:"returnPrice"`
	CalcAt                 *time.Time `json:"calcAt"`
	TakeAt                 *time.Time `json:"takeAt"`
	TakenPrice             float64    `json:"takenPrice"`
}
type PromotionReturnTurnTransactionUncalcListRequest struct {
	Page  int `form:"page" default:"1" min:"1"`
	Limit int `form:"limit" default:"10" min:"1" max:"100"`
}
type PromotionReturnTurnTransactionCalcBody struct {
	CutTypeId   int64      `json:"cutTypeId"`
	StatusId    int64      `json:"statusId"`
	ReturnPrice float64    `json:"returnPrice"`
	CalcAt      *time.Time `json:"calcAt"`
}
type PromotionReturnTurnTransactionUpdateBody struct {
	StatusId   int64      `json:"statusId"`
	TakeAt     *time.Time `json:"takeAt"`
	TakenPrice float64    `json:"takenPrice"`
}

type CutReturnTurnDailyReponse struct {
	StatementDate      string `json:"statementDate"`
	TotalUserDataCount int64  `json:"totalUserDataCount"`
	TotalUserTurnCount int64  `json:"totalUserTurnCount"`
}
