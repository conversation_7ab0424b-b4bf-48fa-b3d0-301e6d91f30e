package model

import "time"

type GetInternalAgentCtwSetting struct {
	Id              int64  `json:"id"`
	IsActive        bool   `json:"isActive"`
	ProgramAllowUse string `json:"programAllowUse"`
	CtwAppId        string `json:"ctwAppId"`
	CtwAppPrivate   string `json:"ctwAppPrivate"`
	CtwUrl          string `json:"ctwUrl"`
	CtwHrefBackUrl  string `json:"ctwHrefBackUrl"`
}

type GetInternalAgentCtwSettingResponse struct {
	Id            int64  `json:"id"`
	IsActive      bool   `json:"isActive"`
	CtwAppId      string `json:"ctwAppId"`
	CtwAppPrivate string `json:"ctwAppPrivate"`
}

type UpdateAgentCtwSetting struct {
	CtwAppPrivate  *string   `json:"ctwAppPrivate"`
	CtwAppId       *string   `json:"ctwAppId"`
	CtwHrefBackUrl string    `json:"-"`
	IsActive       *bool     `json:"isActive"`
	UpdatedAt      time.Time `json:"-"`
	UpdatedById    int64     `json:"-"`
}

type CallApiAgentCtwDetail struct {
	CtwAppId      string `json:"ctwAppId"`
	CtwAppPrivate string `json:"ctwAppPrivate"`
	CtwUrl        string `json:"ctwUrl"`
}

type GetCtwGameListResponse struct {
	Code  int    `json:"code"`
	Error string `json:"error"`
	Data  struct {
		List []CtwGameList `json:"List"`
	} `json:"data"`
}

type CtwGameList struct {
	ID         string `json:"ID"`
	Name       string `json:"Name"`
	Type       int    `json:"Type"`
	IconUrl    string `json:"IconUrl"`
	WebIconUrl string `json:"WebIconUrl"`
	Platform   string `json:"Platform"`
}

type WebGetAgentCtwGameList struct {
	VendorCode string `form:"vendorCode"`
}

type WebGetAgentCtwGameListResponse struct {
	GameCode   string `json:"gameCode"`
	Name       string `json:"Name"`
	Type       int    `json:"Type"`
	IconUrl    string `json:"IconUrl"`
	WebIconUrl string `json:"WebIconUrl"`
	Platform   string `json:"Platform"`
}

type WebOurJsonGetAgentCtwGameListResponse struct {
	GameCode   string `json:"gameCode"`
	Name       string `json:"Name"`
	Type       int    `json:"type"`
	IconUrl    string `json:"iconUrl"`
	WebIconUrl string `json:"webIconUrl"`
	Platform   string `json:"platform"`
}

type CallApiAgentCtwLaunch struct {
	UserId   int64  `json:"-"`
	GameCode string `json:"gameCode"`
}

type CallApiAgentCtwLaunchBody struct {
	UserID string `json:"UserID"`
	GameID string `json:"GameID"`
}

type CallApiAgentCtwLaunchResponse struct {
	Code  int    `json:"code"`
	Error string `json:"error"`
	Data  struct {
		Url string `json:"Url"`
	} `json:"data"`
}
type PlayAgentCtwLaunchResponse struct {
	GameUrl string `json:"gameUrl"`
}

type CallBackAgentCtwCheckBalanceRequest struct {
	AppID     string `json:"AppID"`
	AppSecret string `json:"AppSecret"`
	UserID    string `json:"UserID"`
}

type CallBackAgentCtwCheckBalanceResponse struct {
	Code  int    `json:"code"`
	Error string `json:"error"`
	Data  struct {
		Balance float64 `json:"Balance"`
	} `json:"data"`
}

type CallBackCtwTranferInOutRequest struct {
	AppID         string  `json:"AppID"`
	AppSecret     string  `json:"AppSecret"`
	UserID        string  `json:"UserID"`
	TransactionID string  `json:"TransactionID"`
	Amount        float64 `json:"Amount"`
	RoundID       string  `json:"RoundID"`
	GameID        string  `json:"GameID"`
	ReqTime       string  `json:"ReqTime"`
	Reason        string  `json:"Reason"`
}

type CallBackCtwTranferInOutResponse struct {
	Code  int    `json:"code"`
	Error string `json:"error"`
	Data  struct {
		Balance float64 `json:"Balance"`
	} `json:"data"`
}

type CallBackCtwGameBetDetailRequest struct {
	RoundId string `json:"roundId"`
}
type CallBackCtwGameBetDetail struct {
	Code  int                          `json:"code"`
	Error string                       `json:"error"`
	Data  CallBackCtwGameBetDetailData `json:"data"`
}

type CallBackCtwGameBetDetailData struct {
	GameId   string  `json:"gameId"`
	GameName string  `json:"gameName"`
	UserId   string  `json:"userId"`
	Bet      float64 `json:"bet"`
	Win      float64 `json:"win"`
	Winloss  float64 `json:"winloss"`
	Date     string  `json:"date"`
	Balance  float64 `json:"balance"`
	RoundId  string  `json:"roundId"`
}

type CreateAgentCtwCallback struct {
	Id             int64   `json:"id"`
	UserId         int64   `json:"userId"`
	MemberCode     string  `json:"memberCode"`
	Payoff         float64 `json:"payoff"`
	BetAmount      float64 `json:"betAmount"`
	WinloseAmount  float64 `json:"winloseAmount"`
	Balance        float64 `json:"balance"`
	BeforeBalance  float64 `json:"beforeBalance"`
	AfterBalance   float64 `json:"afterBalance"`
	TransactionId  string  `json:"transactionId"`
	RoundId        string  `json:"roundId"`
	GameId         string  `json:"gameId"`
	CallbackReason string  `json:"callbackReason"`
	Remark         string  `json:"remark"`
	IsSuccess      bool    `json:"isSuccess"`
}

type UpdateAgentCtwCallback struct {
	UserId         *int64   `json:"userId"`
	MemberCode     string   `json:"memberCode"` // ใช้่เป็น หลัก
	Payoff         *float64 `json:"payoff"`
	BetAmount      *float64 `json:"betAmount"`
	WinloseAmount  *float64 `json:"winloseAmount"`
	Balance        *float64 `json:"balance"`
	BeforeBalance  *float64 `json:"beforeBalance"`
	AfterBalance   *float64 `json:"afterBalance"`
	TransactionId  *string  `json:"transactionId"`
	RoundId        string   `json:"roundId"` // ใช้่เป็น หลัก
	GameId         *string  `json:"gameId"`
	CallbackReason *string  `json:"callbackReason"`
	Remark         *string  `json:"remark"`
	IsSuccess      *bool    `json:"isSuccess"`
}

type AgentCtwCallbackSummaryRequest struct {
	PageIndex     int    `form:"pageIndex"`
	PageSize      int    `form:"pageSize"`
	StatementDate string `form:"statementDate"`
}

type AgentCtwCallbackSummary struct {
	UserID       int64   `json:"userId"`
	MemberCode   string  `json:"memberCode"`
	TotalPayoff  float64 `json:"totalPayoff"`
	TotalBet     float64 `json:"totalBet"`
	TotalWinlose float64 `json:"totalWinlose"`
}
