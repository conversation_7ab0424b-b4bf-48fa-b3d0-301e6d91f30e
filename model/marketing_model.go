package model

import "time"

type MarketingConfig struct {
	Id        int64  `json:"id"`
	ConfigKey string `json:"configKey"`
	ConfigVal string `json:"configVal"`
}

type MarketingConfigCreateBody struct {
	ConfigKey string `json:"configKey"`
	ConfigVal string `json:"configVal"`
}

type MarketingConfigResponse struct {
	UserIncomeMaxAmount float64 `json:"userIncomeMaxAmount"`
}
type MarketingConfigUpdateRequest struct {
	UserIncomeMaxAmount *float64 `json:"userIncomeMaxAmount"`
	UpdatedById         int64    `json:"-" gorm:"-"`
}

type AlertMenuCountResponse struct {
	PendingUserIncome    int64     `json:"pendingUserIncome"`
	PendingHengOrder     int64     `json:"pendingHengOrder"`
	PendingDepositCount  int64     `json:"pendingDepositCount"`
	PendingWithdrawCount int64     `json:"pendingWithdrawCount"`
	CacheExpireAt        time.Time `json:"cacheExpireAt"`
}

type TransactionReportDailyRow struct {
	OfDate string `json:"ofDate"`
}
type TransactionReportDailyQuery struct {
	OfDate   string `form:"ofDate" time_format:"2006-01-02" default:"2023-04-01"`
	FromDate string `form:"fromDate" time_format:"2006-01-02" default:""`
	ToDate   string `form:"toDate" time_format:"2006-01-02" default:""`
	Page     int    `form:"page"`
	Limit    int    `form:"limit"`
}
type TransactionReportDailyResponse struct {
	FromDate              string                   `json:"fromDate"`
	ToDate                string                   `json:"toDate"`
	NewuserCount          int64                    `json:"newuserCount"`
	FirstDepositAmount    float64                  `json:"firstDepositAmount"`
	FirstDepositCount     int64                    `json:"firstDepositCount"`
	FirstDayDepositAmount float64                  `json:"firstDayDepositAmount"`
	FirstDayDepositCount  int64                    `json:"firstDayDepositCount"`
	DepositAmount         float64                  `json:"depositAmount"`
	DepositCount          int64                    `json:"depositCount"`
	WithdrawAmount        float64                  `json:"withdrawAmount"`
	WithdrawCount         int64                    `json:"withdrawCount"`
	Total                 int64                    `json:"total"`
	List                  []TransactionReportDaily `json:"list"`
}
type TransactionReportDaily struct {
	OfDate                string  `json:"ofDate"`
	NewuserCount          int64   `json:"newuserCount"`
	FirstDepositAmount    float64 `json:"firstDepositAmount"`
	FirstDepositCount     int64   `json:"firstDepositCount"`
	FirstDayDepositAmount float64 `json:"firstDayDepositAmount"`
	FirstDayDepositCount  int64   `json:"firstDayDepositCount"`
	DepositAmount         float64 `json:"depositAmount"`
	DepositCount          int64   `json:"depositCount"`
	WithdrawAmount        float64 `json:"withdrawAmount"`
	WithdrawCount         int64   `json:"withdrawCount"`
}
