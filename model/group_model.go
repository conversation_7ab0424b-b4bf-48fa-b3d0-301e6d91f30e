package model

import (
	"time"
)

type Group struct {
	Id         int64      `json:"id"`
	Name       string     `json:"name"`
	AdminCount int64      `json:"adminCount"`
	CreatedAt  time.Time  `json:"createdAt"`
	UpdatedAt  time.Time  `json:"updatedAt"`
	DeletedAt  *time.Time `json:"deletedAt"`
}

type CreateGroup struct {
	Name          string  `json:"name" validate:"required"`
	PermissionIds []int64 `json:"permissionIds" validate:"required"`
	CreateBy      int64   `json:"-"`
}

type GroupCountList struct {
	Id         int64  `json:"id"`
	Name       string `json:"name"`
	AdminCount int64  `json:"adminCount"`
}

type GroupDetail struct {
	Id   int64  `json:"id"`
	Name string `json:"name"`
}

type DeleteGroup struct {
	Id       int64 `json:"id" validate:"required"`
	DeleteBy int64 `json:"-"`
}

type GroupName struct {
	Name string `json:"name" validate:"required"`
}
