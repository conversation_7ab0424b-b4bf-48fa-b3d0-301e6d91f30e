package model

import "time"

type Staff struct {
	Id         int64
	StaffUrl   string
	NameEn     string
	NameTh     string
	PositionEn string
	PositionTh string
	Type       string
	SortOrder  int
	CreatedAt  time.Time
	UpdatedAt  *time.Time
}

type StaffDetail struct {
	Id         int64  `json:"id"`
	StaffUrl   string `json:"staffUrl" binding:"required,min=1,max=255"`
	NameEn     string `json:"nameEn" binding:"required,min=1,max=255"`
	NameTh     string `json:"nameTh" binding:"required,min=1,max=255"`
	PositionEn string `json:"positionEn" binding:"required,min=1,max=20"`
	PositionTh string `json:"positionTh" binding:"required,min=1,max=20"`
	Type       string `json:"type" binding:"required,oneof=MANAGEMENT STAFF"`
	SortOrder  int    `json:"sortOrder" binding:"required,min=1"`
}

type StaffBody struct {
	Id         int64  `json:"-"`
	StaffUrl   string `json:"staffUrl" binding:"required,min=1,max=255"`
	NameEn     string `json:"nameEn" binding:"required,min=1,max=255"`
	NameTh     string `json:"nameTh" binding:"required,min=1,max=255"`
	PositionEn string `json:"positionEn" binding:"required,min=1,max=20"`
	PositionTh string `json:"positionTh" binding:"required,min=1,max=20"`
	Type       string `json:"type" binding:"required,oneof=MANAGEMENT STAFF"`
}

type StaffUpdateBody struct {
	StaffUrl   string `json:"staffUrl" binding:"required,min=1,max=255"`
	NameEn     string `json:"nameEn" binding:"required,min=1,max=255"`
	NameTh     string `json:"nameTh" binding:"required,min=1,max=255"`
	PositionEn string `json:"positionEn" binding:"required,min=1,max=20"`
	PositionTh string `json:"positionTh" binding:"required,min=1,max=20"`
	Type       string `json:"type" binding:"required,oneof=MANAGEMENT STAFF"`
}

type StaffSortItem struct {
	Id        int64 `json:"id"`
	SortOrder int   `json:"sortOrder" binding:"required,min=1"`
}

type StaffSortBody struct {
	List []StaffSortItem `json:"list"`
}

type StaffListResponse struct {
	ManagementList []StaffDetail `json:"managementList"`
	StaffList      []StaffDetail `json:"staffList"`
}
