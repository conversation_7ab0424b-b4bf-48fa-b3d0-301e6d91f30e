package model

import "time"

type Banner struct {
	Id        int64
	BannerUrl string
	Type      string
	SortOrder int
	IsActive  bool
	CreatedAt time.Time
	UpdatedAt *time.Time
}

type BannerDetail struct {
	Id        int64  `json:"id"`
	BannerUrl string `json:"bannerUrl"`
	Type      string `json:"type"`
	SortOrder int    `json:"sortOrder,omitempty"`
	IsActive  bool   `json:"isActive"`
}

type BannerTypeListResponse struct {
	List []string `json:"list"`
}

type BannerCreateBody struct {
	Id        int64  `json:"-"`
	BannerUrl string `json:"bannerUrl" binding:"required,min=1,max=255"`
	Type      string `json:"type" binding:"required,oneof=SHOP_TOP SHOP_BOTTOM TICKET STAFF PLAYER YOUTH"`
	SortOrder int    `json:"-"`
	IsActive  bool   `json:"isActive" example:"false"`
}

type BannerQuery struct {
	Type string `form:"type" binding:"required,oneof=STAFF PLAYER YOUTH"`
}

type BannerWebQuery struct {
	Type string `form:"type" binding:"required,oneof=SHOP_TOP SHOP_BOTTOM TICKET STAFF PLAYER YOUTH"`
}

type BannerSortItem struct {
	Id        int64 `json:"id"`
	SortOrder int   `json:"sortOrder" binding:"required,min=1"`
}

type BannerSortBody struct {
	List []BannerSortItem `json:"list"`
}

type BannerListResponse struct {
	List []BannerDetail `json:"list"`
}

type BannerShopListResponse struct {
	TopList    []BannerDetail `json:"topList"`
	BottomList []BannerDetail `json:"BottomList"`
}

type BannerUpdateBody struct {
	Id        int64  `json:"-"`
	BannerUrl string `json:"bannerUrl" binding:"required,min=1,max=255"`
	IsActive  bool   `json:"isActive" example:"false"`
}
