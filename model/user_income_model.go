package model

import (
	"time"
)

const (
	USER_INCOME_TYPE_AFFILIATE             = 1
	USER_INCOME_TYPE_PROMOTION_RETURN_LOSS = 2
	USER_INCOME_TYPE_ALLIANCE              = 3
	USER_INCOME_TYPE_LUCKY_WHEEL           = 4
	USER_INCOME_TYPE_PROMOTION_RETURN_TURN = 5
)
const (
	USER_INCOME_STATUS_PENDING   = 1
	USER_INCOME_STATUS_COMPLETED = 2
	USER_INCOME_STATUS_CANCELLED = 3
)

type UserIncomeLog struct {
	Id            int64      `json:"id"`
	UserId        int64      `json:"userId"`
	TypeId        int64      `json:"typeId"`
	RefId         *int64     `json:"refId"`
	Detail        string     `json:"detail"`
	CreditAmount  float64    `json:"creditAmount"`
	CreditAfter   float64    `json:"creditAfter"`
	TransferAt    *time.Time `json:"transferAt"`
	StatusId      int64      `json:"statusId"`
	CreateBy      int64      `json:"createBy"`
	CreateByName  string     `json:"createByName"`
	ConfirmBy     *int64     `json:"confirmBy"`
	ConfirmByName string     `json:"confirmByName"`
	CreatedAt     time.Time  `json:"createdAt"`
	UpdatedAt     time.Time  `json:"updatedAt"`
}
type UserIncomeLogListRequest struct {
	FromDate   string `form:"fromDate"`
	ToDate     string `form:"toDate"`
	TypeName   string `form:"typeName"`
	StatusId   *int64 `form:"statusId"`
	StatusName string `form:"statusName"`
	MemberCode string `form:"memberCode"`
	Page       int    `form:"page" default:"1" min:"1"`
	Limit      int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol    string `form:"sortCol"`
	SortAsc    string `form:"sortAsc"`
}
type UserIncomeWebLogResponse struct {
	Id           int64      `json:"id"`
	CreateDate   string     `json:"createDate"`
	TypeId       int64      `json:"typeId"`
	TypeDetail   string     `json:"typeDetail"`
	CreditAmount float64    `json:"creditAmount"`
	TransferAt   *time.Time `json:"transferAt"`
	StatusId     int64      `json:"statusId"`
	StatusDetail string     `json:"statusDetail"`
}
type UserIncomeWebLogListAdminRequest struct {
	UserId   int64  `form:"userId" binding:"required"`
	FromDate string `form:"fromDate"`
	ToDate   string `form:"toDate"`
	TypeId   *int64 `form:"typeId"`
	StatusId *int64 `form:"statusId"`
	Page     int    `form:"page" default:"1" min:"1"`
	Limit    int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol  string `form:"sortCol"`
	SortAsc  string `form:"sortAsc"`
}
type UserIncomeWebLogListRequest struct {
	UserId   int64  `form:"userId" json:"-" binding:"required"`
	FromDate string `form:"fromDate"`
	ToDate   string `form:"toDate"`
	TypeId   *int64 `form:"typeId"`
	StatusId *int64 `form:"statusId"`
	Page     int    `form:"page" default:"1" min:"1"`
	Limit    int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol  string `form:"sortCol"`
	SortAsc  string `form:"sortAsc"`
}
type UserIncomeWebLogListResponse struct {
	Message     string      `json:"message" validate:"required,min=1,max=255"`
	List        interface{} `json:"list"`
	Total       int64       `json:"total"`
	TotalIncome float64     `json:"totalIncome"`
}
type UserIncomeLogResponse struct {
	Id            int64      `json:"id"`
	UserId        int64      `json:"userId"`
	MemberCode    string     `json:"memberCode"`
	TypeId        int64      `json:"typeId"`
	TypeDetail    string     `json:"typeDetail"`
	RefId         *int64     `json:"refId"`
	Detail        string     `json:"detail"`
	CreditAmount  float64    `json:"creditAmount"`
	CreditAfter   float64    `json:"creditAfter"`
	TransferAt    *time.Time `json:"transferAt"`
	StatusId      int64      `json:"statusId"`
	StatusDetail  string     `json:"statusDetail"`
	CreateBy      int64      `json:"createBy"`
	CreateByName  string     `json:"createByName"`
	ConfirmBy     *int64     `json:"confirmBy"`
	ConfirmByName string     `json:"confirmByName"`
	CreatedAt     time.Time  `json:"createdAt"`
	UpdatedAt     time.Time  `json:"updatedAt"`
}

type UserIncomeLogTotalSummaryResponse struct {
	UserIncomeTotalAmount          float64 `json:"userIncomeTotalAmount"`
	AllianceIncomeTotalAmount      float64 `json:"allianceIncomeTotalAmount"`
	AffiliateIncomeTotalAmount     float64 `json:"affiliateIncomeTotalAmount"`
	PromotionReturnLossTotalAmount float64 `json:"promotionReturnLossTotalAmount"`
	PromotionReturnTurnTotalAmount float64 `json:"promotionReturnTurnTotalAmount"`
	LuckyWheelTotalAmount          float64 `json:"luckyWheelTotalAmount"`
}

type UserIncomeLogTotalSummaryRequest struct {
	FromDate string `form:"fromDate"`
	ToDate   string `form:"toDate"`
}
type UserIncomeLogCreateBody struct {
	Id            int64      `json:"id"`
	UserId        int64      `json:"userId"`
	TypeId        int64      `json:"typeId"`
	RefId         *int64     `json:"refId"`
	Detail        string     `json:"detail"`
	CreditAmount  float64    `json:"creditAmount"`
	CreditAfter   float64    `json:"creditAfter"`
	TransferAt    *time.Time `json:"transferAt"`
	StatusId      int64      `json:"statusId"`
	CreateBy      int64      `json:"createBy"`
	CreateByName  string     `json:"createByName"`
	ConfirmBy     *int64     `json:"confirmBy"`
	ConfirmByName string     `json:"confirmByName"`
}
type UserIncomeLogConfirmBody struct {
	Id            int64     `json:"id"`
	CreditAfter   float64   `json:"creditAfter"`
	TransferAt    time.Time `json:"transferAt"`
	ConfirmBy     int64     `json:"confirmBy"`
	ConfirmByName string    `json:"confirmByName"`
}

type UserIncomeCompletedLogListRequest struct {
	UserId   int64   `form:"userId" json:"-"`
	FromDate string  `form:"fromDate"`
	ToDate   string  `form:"toDate"`
	TypeName string  `form:"typeName"`
	TypeIds  []int64 `form:"typeIds" json:"-"`
	Page     int     `form:"page" default:"1" min:"1"`
	Limit    int     `form:"limit" default:"10" min:"1" max:"100"`
	SortCol  string  `form:"sortCol"`
	SortAsc  string  `form:"sortAsc"`
}
type UserIncomeCompletedLogResponse struct {
	Id           int64      `json:"id"`
	UserId       int64      `json:"userId"`
	Detail       string     `json:"detail"`
	CreditAmount float64    `json:"creditAmount"`
	TransferAt   *time.Time `json:"transferAt"`
}

type UserWinLoseSummaryReportRequest struct {
	DateType   string `form:"dateType"`
	FromDate   string `form:"fromDate"`
	ToDate     string `form:"toDate"`
	TypeName   string `form:"typeName"`
	MemberCode string `form:"memberCode"`
}
type UserTodayWinLoseSummaryReportRequest struct {
	TypeName   string `form:"typeName"`
	MemberCode string `form:"memberCode"`
}
type UserWinLoseSummaryReportResponse struct {
	TotalTurnOver   float64 `json:"totalTurnOver"`
	TotalDiffAmount float64 `json:"totalDiffAmount"`
	TotalWinLose    float64 `json:"totalWinLose"`
}
type UserWinLoseSummaryListRequest struct {
	DateType   string `form:"dateType"`
	FromDate   string `form:"fromDate"`
	ToDate     string `form:"toDate"`
	TypeName   string `form:"typeName"`
	MemberCode string `form:"memberCode"`
	Page       int    `form:"page" default:"1" min:"1"`
	Limit      int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol    string `form:"sortCol"`
	SortAsc    string `form:"sortAsc"`
}
type UserWinLoseDailySummaryRequest struct {
	OfDate     string `form:"ofDate"`
	TypeName   string `form:"typeName"`
	MemberCode string `form:"memberCode"`
}
type UserWinLoseDailyListRequest struct {
	OfDate     string `form:"ofDate"`
	TypeName   string `form:"typeName"`
	MemberCode string `form:"memberCode"`
	Page       int    `form:"page" default:"1" min:"1"`
	Limit      int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol    string `form:"sortCol"`
	SortAsc    string `form:"sortAsc"`
}
type UserTodayWinLoseSummaryListRequest struct {
	TypeName   string `form:"typeName"`
	MemberCode string `form:"memberCode"`
	Page       int    `form:"page" default:"1" min:"1"`
	Limit      int    `form:"limit" default:"10" min:"1" max:"100"`
	SortCol    string `form:"sortCol"`
	SortAsc    string `form:"sortAsc"`
}
type UserWinLoseSummaryResponse struct {
	UserId        int64   `json:"userId"`
	MemberCode    string  `json:"memberCode"`
	TotalTurnOver float64 `json:"totalTurnOver"`
	DiffAmount    float64 `json:"diffAmount"`
	TotalWinLose  float64 `json:"totalWinLose"`
}
type UserWinLoseDailyResponse struct {
	OfDate          string  `json:"ofDate"`
	TotalTurnOver   float64 `json:"totalTurnOver"`
	TotalDiffAmount float64 `json:"totalDiffAmount"`
	TotalWinLose    float64 `json:"totalWinLose"`
}

type CronAllianceIncomeCalcRequest struct {
	StatementDate string `form:"statementDate" binding:"required"`
}
type CronAllianceIncomeBonusListequest struct {
	UserIds       []int64 `form:"userIds" binding:"required"`
	StatementDate string  `form:"statementDate" binding:"required"`
}

type GetAliasByUserIdRequest struct {
	Ref      string `form:"ref"`
	RefCode  string `form:"refCode"`
	SaleCode string `form:"saleCode"`
}

type GetAliasByUserIdResponse struct {
	Alias string `json:"alias"`
}

type CronAllianceIncomeRecalcRequest struct {
	FromStatementDate string `form:"fromStatementDate" binding:"required"`
	ToStatementDate   string `form:"toStatementDate" binding:"required"`
}

type TestAddAllianceIncomeRequest struct {
	UserId        int64   `form:"userId" binding:"required"`
	StatementDate string  `form:"statementDate" binding:"required"`
	IncomeAmount  float64 `form:"incomeAmount" binding:"required"`
}
