package model

import "time"

type AffiliateUserListRequest struct {
	DateType   string `form:"dateType"`
	FromDate   string `form:"fromDate"`
	ToDate     string `form:"toDate"`
	MemberCode string `form:"memberCode"`
	Page       int    `form:"page" default:"1" min:"1"`
	Limit      int    `form:"limit" default:"10" min:"0" max:"100"`
	SortCol    string `form:"sortCol"`
	SortAsc    string `form:"sortAsc"`
}

type AffiliateUserResponse struct {
	UserId                   int64   `json:"userId"`
	MemberCode               string  `json:"memberCode"`
	Username                 string  `json:"username"`
	UserFullname             string  `json:"userFullname"`
	LinkClickTotal           int64   `json:"linkClickTotal"`
	MemberCount              int64   `json:"memberCount"`
	MemberDepositAmount      float64 `json:"memberDepositAmount"`
	TotalTurnSport           float64 `json:"totalTurnSport"`
	TotalTurnCasino          float64 `json:"totalTurnCasino"`
	TotalTurnGame            float64 `json:"totalTurnGame"`
	TotalTurnLottery         float64 `json:"totalTurnLottery"`
	TotalTurnP2p             float64 `json:"totalTurnP2p"`
	TotalTurnFinancial       float64 `json:"totalTurnFinancial"`
	TotalCommission          float64 `json:"commissionIncome"`
	LinkRegisterAmount       float64 `json:"linkRegisterAmount"`
	FirstDepositAmount       float64 `json:"firstDepositAmount"`
	TotalIncome              float64 `json:"totalIncome"`
	TotalIncomeWithdraw      float64 `json:"totalIncomeWithdraw"`
	IncomeBalance            float64 `json:"incomeBalance"`
	PromotionReturnLossTotal float64 `json:"promotionReturnLossTotal"`
	LuckyWheelTotal          float64 `json:"luckyWheelTotal"`
	CouponTotal              float64 `json:"couponTotal"`
	CreditBonusTotal         float64 `json:"creditBonusTotal"`
	PromotionWebTotal        float64 `json:"promotionWebTotal"`
	PromotionReturnTurnTotal float64 `json:"promotionReturnTurnTotal"`
	AllBonusTotal            float64 `json:"allBonusTotal"`
}

type AffiliateUserSummaryRequest struct {
	DateType   string `form:"dateType"`
	FromDate   string `form:"fromDate"`
	ToDate     string `form:"toDate"`
	MemberCode string `form:"memberCode"`
}

type AffiliateUserSummary struct {
	LinkClickSummaryTotal           int64   `json:"linkClickSummaryTotal"`
	MemberCountSummaryTotal         int64   `json:"memberCountSummaryTotal"`
	MemberDepositAmountSummaryTotal float64 `json:"memberDepositAmountSummaryTotal"`
	TurnSportSummaryTotal           float64 `json:"turnSportSummaryTotal"`
	TurnCasinoSummaryTotal          float64 `json:"turnCasinoSummaryTotal"`
	TurnGameSummaryTotal            float64 `json:"turnGameSummaryTotal"`
	TurnLotterySummaryTotal         float64 `json:"turnLotterySummaryTotal"`
	TurnP2pSummaryTotal             float64 `json:"turnP2pSummaryTotal"`
	TurnFinancialSummaryTotal       float64 `json:"turnFinancialSummaryTotal"`
	CommissionSummaryTotal          float64 `json:"commissionSummaryTotal"`
	LinkRegisterAmountSummaryTotal  float64 `json:"linkRegisterAmountSummaryTotal"`
	FirstDepositAmountSummaryTotal  float64 `json:"firstDepositAmountSummaryTotal"`
	IncomeSummaryTotal              float64 `json:"incomeSummaryTotal"`
	IncomeWithdrawSummaryTotal      float64 `json:"incomeWithdrawSummaryTotal"`
	IncomeBalanceSummaryTotal       float64 `json:"incomeBalanceSummaryTotal"`
	PromotionReturnLossTotal        float64 `json:"promotionReturnLossTotal"`
	LuckyWheelTotal                 float64 `json:"luckyWheelTotal"`
	CouponTotal                     float64 `json:"couponTotal"`
	CreditBonusTotal                float64 `json:"creditBonusTotal"`
	PromotionWebTotal               float64 `json:"promotionWebTotal"`
	PromotionReturnTurnTotal        float64 `json:"promotionReturnTurnTotal"`
	AllBonusTotal                   float64 `json:"allBonusTotal"`
}
type AffiliateUserSummaryResponse struct {
	TotalAffiliateUser              int64     `json:"totalAffiliateUser"`
	LinkClickSummaryTotal           int64     `json:"linkClickSummaryTotal"`
	MemberCountSummaryTotal         int64     `json:"memberCountSummaryTotal"`
	MemberDepositAmountSummaryTotal float64   `json:"memberDepositAmountSummaryTotal"`
	TurnSportSummaryTotal           float64   `json:"turnSportSummaryTotal"`
	TurnCasinoSummaryTotal          float64   `json:"turnCasinoSummaryTotal"`
	TurnGameSummaryTotal            float64   `json:"turnGameSummaryTotal"`
	TurnLotterySummaryTotal         float64   `json:"turnLotterySummaryTotal"`
	TurnP2pSummaryTotal             float64   `json:"turnP2pSummaryTotal"`
	TurnFinancialSummaryTotal       float64   `json:"turnFinancialSummaryTotal"`
	CommissionSummaryTotal          float64   `json:"commissionSummaryTotal"`
	LinkRegisterAmountSummaryTotal  float64   `json:"linkRegisterAmountSummaryTotal"`
	FirstDepositAmountSummaryTotal  float64   `json:"firstDepositAmountSummaryTotal"`
	IncomeSummaryTotal              float64   `json:"incomeSummaryTotal"`
	IncomeWithdrawSummaryTotal      float64   `json:"incomeWithdrawSummaryTotal"`
	IncomeBalanceSummaryTotal       float64   `json:"incomeBalanceSummaryTotal"`
	CacheExpireAt                   time.Time `json:"cacheExpireAt"`
	PromotionReturnLossTotal        float64   `json:"promotionReturnLossTotal"`
	LuckyWheelTotal                 float64   `json:"luckyWheelTotal"`
	CouponTotal                     float64   `json:"couponTotal"`
	CreditBonusTotal                float64   `json:"creditBonusTotal"`
	PromotionWebTotal               float64   `json:"promotionWebTotal"`
	PromotionReturnTurnTotal        float64   `json:"promotionReturnTurnTotal"`
	AllBonusTotal                   float64   `json:"allBonusTotal"`
}

type AffiliateDepositPlayListRequest struct {
	RefUserId  int64  `form:"refUserId" validate:"required"`
	Level      *int64 `form:"level"`
	DateType   string `form:"dateType"`
	FromDate   string `form:"fromDate"`
	ToDate     string `form:"toDate"`
	MemberCode string `form:"memberCode"`
	Page       int    `form:"page" default:"1" min:"1"`
	Limit      int    `form:"limit" default:"10" min:"0" max:"100"`
	SortCol    string `form:"sortCol"`
	SortAsc    string `form:"sortAsc"`
}
type AffiliateUserDepositPlayResponse struct {
	RefUserId           int64   `json:"refUserId"`
	UserId              int64   `json:"userId"`
	MemberCode          string  `json:"memberCode"`
	FirstDepositAmount  float64 `json:"firstDepositAmount"`
	TotalDepositBonus   float64 `json:"totalDepositBonus"`
	LinkRegisterAmount  float64 `json:"linkRegisterAmount"`
	TotalTurnSport      float64 `json:"totalTurnSport"`
	CommissionSport     float64 `json:"commissionSport"`
	TotalTurnCasino     float64 `json:"totalTurnCasino"`
	CommissionCasino    float64 `json:"commissionCasino"`
	TotalTurnGame       float64 `json:"totalTurnGame"`
	CommissionGame      float64 `json:"commissionGame"`
	TotalTurnLottery    float64 `json:"totalTurnLottery"`
	CommissionLottery   float64 `json:"commissionLottery"`
	TotalTurnP2p        float64 `json:"totalTurnP2p"`
	CommissionP2p       float64 `json:"commissionP2p"`
	TotalTurnFinancial  float64 `json:"totalTurnFinancial"`
	CommissionFinancial float64 `json:"commissionFinancial"`
	TotalPlayAmount     float64 `json:"totalPlayAmount"`
	TotalCommission     float64 `json:"totalCommission"`
	RegisterAt          string  `json:"registerAt"`
}
type AffiliateUserDownlineCountResponse struct {
	RefUserId   int64 `json:"refUserId"`
	Level1Count int64 `json:"level1Count"`
	Level2Count int64 `json:"level2Count"`
	Level3Count int64 `json:"level3Count"`
}

type AffiliateLevel struct {
	Id        int64
	Ukey      string
	UserId    int64
	UplineId  int64
	Level     int64
	CreatedAt time.Time
}
type AffiliateLevelCreateBody struct {
	Ukey     string `json:"ukey"`
	UserId   int64  `json:"userId"`
	UplineId int64  `json:"uplineId"`
	Level    int64  `json:"level"`
}

type MakeReportAffiliateUserResponse struct {
	Id                       string  `json:"id"`
	OfDate                   string  `json:"ofDate"`
	UserId                   int64   `json:"userId"`
	LinkClickTotal           int64   `json:"linkClickTotal"`
	MemberCount              int64   `json:"memberCount"`
	MemberDepositAmount      float64 `json:"memberDepositAmount"`
	TotalTurnSport           float64 `json:"totalTurnSport"`
	TotalTurnCasino          float64 `json:"totalTurnCasino"`
	TotalTurnGame            float64 `json:"totalTurnGame"`
	TotalTurnLottery         float64 `json:"totalTurnLottery"`
	TotalTurnP2p             float64 `json:"totalTurnP2p"`
	TotalTurnFinancial       float64 `json:"totalTurnFinancial"`
	TotalCommission          float64 `json:"commissionIncome"`
	LinkRegisterAmount       float64 `json:"linkRegisterAmount"`
	FirstDepositAmount       float64 `json:"firstDepositAmount"`
	TotalIncome              float64 `json:"totalIncome"`
	TotalIncomeWithdraw      float64 `json:"totalIncomeWithdraw"`
	IncomeBalance            float64 `json:"incomeBalance"`
	PromotionReturnLossTotal float64 `json:"promotionReturnLossTotal"`
	LuckyWheelTotal          float64 `json:"luckyWheelTotal"`
	CouponTotal              float64 `json:"couponTotal"`
	CreditBonusTotal         float64 `json:"creditBonusTotal"`
	PromotionWebTotal        float64 `json:"promotionWebTotal"`
	PromotionReturnTurnTotal float64 `json:"promotionReturnTurnTotal"`
	AllBonusTotal            float64 `json:"allBonusTotal"`
}

type MakeReportAffiliateUserUpdateBody struct {
	LinkClickTotal           int64   `json:"linkClickTotal"`
	MemberCount              int64   `json:"memberCount"`
	MemberDepositAmount      float64 `json:"memberDepositAmount"`
	TotalTurnSport           float64 `json:"totalTurnSport"`
	TotalTurnCasino          float64 `json:"totalTurnCasino"`
	TotalTurnGame            float64 `json:"totalTurnGame"`
	TotalTurnLottery         float64 `json:"totalTurnLottery"`
	TotalTurnP2p             float64 `json:"totalTurnP2p"`
	TotalTurnFinancial       float64 `json:"totalTurnFinancial"`
	TotalCommission          float64 `json:"commissionIncome"`
	LinkRegisterAmount       float64 `json:"linkRegisterAmount"`
	FirstDepositAmount       float64 `json:"firstDepositAmount"`
	TotalIncome              float64 `json:"totalIncome"`
	TotalIncomeWithdraw      float64 `json:"totalIncomeWithdraw"`
	IncomeBalance            float64 `json:"incomeBalance"`
	PromotionReturnLossTotal float64 `json:"promotionReturnLossTotal"`
	LuckyWheelTotal          float64 `json:"luckyWheelTotal"`
	CouponTotal              float64 `json:"couponTotal"`
	CreditBonusTotal         float64 `json:"creditBonusTotal"`
	PromotionWebTotal        float64 `json:"promotionWebTotal"`
	PromotionReturnTurnTotal float64 `json:"promotionReturnTurnTotal"`
	AllBonusTotal            float64 `json:"allBonusTotal"`
}
