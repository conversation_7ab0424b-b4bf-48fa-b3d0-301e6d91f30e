package model

import "time"

const (
	ACTIVITY_DAILY_STATUS_ACTIVE   int64 = 1
	ACTIVITY_DAILY_STATUS_DEACTIVE int64 = 2
)

const (
	ACTIVITY_DAY_MONDAY         int64 = 1
	ACTIVITY_DAY_TUESDAY        int64 = 2
	ACTIVITY_DAY_WEDNESDAY      int64 = 3
	ACTIVITY_DAY_THURSDAY       int64 = 4
	ACTIVITY_DAY_FRIDAY         int64 = 5
	ACTIVITY_DAY_SATURDAY       int64 = 6
	ACTIVITY_DAY_SUNDAY         int64 = 7
	ACTIVITY_DAY_COMPLETE_DAILY int64 = 8
)

const (
	NONE                       int64 = 1
	MINIMUM_DEPOSIT            int64 = 2
	DEPOSIT_ACCUMULATED_AMOUNT int64 = 3
)

type ActivityDaily struct {
	Id                       int64      `json:"id"`
	MondayBonus              float64    `json:"mondayBonus"`
	TuesdayBonus             float64    `json:"tuesdayBonus"`
	WednesdayBonus           float64    `json:"wednesdayBonus"`
	ThursdayBonus            float64    `json:"thursdayBonus"`
	FridayBonus              float64    `json:"fridayBonus"`
	SaturdayBonus            float64    `json:"saturdayBonus"`
	SundayBonus              float64    `json:"sundayBonus"`
	CompletedBonus           float64    `json:"completedBonus"`
	ActivityDailyConditionId int64      `json:"activityDailyConditionId"`
	ActivityDailyStatusId    int64      `json:"activityDailyStatusId"`
	DepositAmountCondition   float64    `json:"depositAmountCondition"`
	CreatedAt                *time.Time `json:"createdAt"`
	UpdatedAt                *time.Time `json:"updatedAt"`
	DeletedAt                *time.Time `json:"deletedAt"`
}

type ActivityDailyConditionResponse struct {
	Id      int64  `json:"id"`
	Name    string `json:"name"`
	LabelTh string `json:"labelTh"`
	LabelEn string `json:"labelEn"`
}

type ActivityDailyStatusResponse struct {
	Id      int64  `json:"id"`
	Name    string `json:"name"`
	LabelTh string `json:"labelTh"`
	LabelEn string `json:"labelEn"`
}

type CreateActivityDailyRequest struct {
	Id                       int64   `json:"-"`
	MondayBonus              float64 `json:"mondayBonus"`
	TuesdayBonus             float64 `json:"tuesdayBonus"`
	WednesdayBonus           float64 `json:"wednesdayBonus"`
	ThursdayBonus            float64 `json:"thursdayBonus"`
	FridayBonus              float64 `json:"fridayBonus"`
	SaturdayBonus            float64 `json:"saturdayBonus"`
	SundayBonus              float64 `json:"sundayBonus"`
	CompletedBonus           float64 `json:"completedBonus"`
	ActivityDailyConditionId *int64  `json:"activityDailyConditionId"`
	ActivityDailyStatusId    *int64  `json:"activityDailyStatusId"`
	DepositAmountCondition   float64 `json:"depositAmountCondition"`
	UpdatedByAdminId         int64   `json:"-"`
}

type UpdateActivityDailyRequest struct {
	Id                       int64    `json:"-"`
	MondayBonus              *float64 `json:"mondayBonus"`
	TuesdayBonus             *float64 `json:"tuesdayBonus"`
	WednesdayBonus           *float64 `json:"wednesdayBonus"`
	ThursdayBonus            *float64 `json:"thursdayBonus"`
	FridayBonus              *float64 `json:"fridayBonus"`
	SaturdayBonus            *float64 `json:"saturdayBonus"`
	SundayBonus              *float64 `json:"sundayBonus"`
	CompletedBonus           *float64 `json:"completedBonus"`
	ActivityDailyConditionId *int64   `json:"activityDailyConditionId"`
	ActivityDailyStatusId    *int64   `json:"activityDailyStatusId"`
	DepositAmountCondition   *float64 `json:"depositAmountCondition"`
	UpdatedByAdminId         int64    `json:"-"`
}

// type GetActivityDailyByIdRequest struct {
// 	Id int64 `uri:"id" binding:"required"`
// }

type GetActivityDailyResponse struct {
	Id                       int64     `json:"id"`
	MondayBonus              float64   `json:"mondayBonus"`
	TuesdayBonus             float64   `json:"tuesdayBonus"`
	WednesdayBonus           float64   `json:"wednesdayBonus"`
	ThursdayBonus            float64   `json:"thursdayBonus"`
	FridayBonus              float64   `json:"fridayBonus"`
	SaturdayBonus            float64   `json:"saturdayBonus"`
	SundayBonus              float64   `json:"sundayBonus"`
	CompletedBonus           float64   `json:"completedBonus"`
	ActivityDailyConditionId int64     `json:"activityDailyConditionId"`
	ActivityDailyConditionTh string    `json:"activityDailyConditionTh"`
	ActivityDailyStatusId    int64     `json:"activityDailyStatusId"`
	ActivityDailyStatusTh    string    `json:"activityDailyStatusTh"`
	DepositAmountCondition   float64   `json:"depositAmountCondition"`
	CacheExpiredAt           time.Time `json:"cacheExpiredAt"`
}

type GetWebActivityDailyResponse struct {
	ActivityDailyStatusId    int64   `json:"activityDailyStatusId"`
	ActivityDailyStatusTh    string  `json:"activityDailyStatusTh"`
	ActivityDailyConditionId int64   `json:"activityDailyConditionId"`
	DepositAmountCondition   float64 `json:"depositAmountCondition"`
	ActivityDailyConditionTh string  `json:"activityDailyConditionTh"`
	TodayAvailableDay        int64   `json:"todayAvailableDay"`
	MondayBonus              float64 `json:"mondayBonus"`
	UserCollectedMon         bool    `json:"userCollectedMon"`
	IsMon                    bool    `json:"isMon"`
	IsSkipMon                bool    `json:"isSkipMon"`
	TuesdayBonus             float64 `json:"tuesdayBonus"`
	UserCollectedTue         bool    `json:"userCollectedTue"`
	IsTue                    bool    `json:"isTue"`
	IsSkipTue                bool    `json:"isSkipTue"`
	WednesdayBonus           float64 `json:"wednesdayBonus"`
	UserCollectedWed         bool    `json:"userCollectedWed"`
	IsWed                    bool    `json:"isWed"`
	IsSkipWed                bool    `json:"isSkipWed"`
	ThursdayBonus            float64 `json:"thursdayBonus"`
	UserCollectedThu         bool    `json:"userCollectedThu"`
	IsThu                    bool    `json:"isThu"`
	IsSkipThu                bool    `json:"isSkipThu"`
	FridayBonus              float64 `json:"fridayBonus"`
	UserCollectedFri         bool    `json:"userCollectedFri"`
	IsFri                    bool    `json:"isFri"`
	IsSkipFri                bool    `json:"isSkipFri"`
	SaturdayBonus            float64 `json:"saturdayBonus"`
	UserCollectedSat         bool    `json:"userCollectedSat"`
	IsSat                    bool    `json:"isSat"`
	IsSkipSat                bool    `json:"isSkipSat"`
	SundayBonus              float64 `json:"sundayBonus"`
	UserCollectedSun         bool    `json:"userCollectedSun"`
	IsSun                    bool    `json:"isSun"`
	IsSkipSun                bool    `json:"isSkipSun"`
}

type ActivityDailyUser struct {
	Id             int64     `json:"id"`
	UserId         int64     `json:"userId"`
	CollectedBonus float64   `json:"collectedBonus"`
	CollectedDate  time.Time `json:"collectedDate"`
	ActivityDayId  int64     `json:"activityDayId"`
	CreatedAt      string    `json:"createdAt"`
	UpdatedAt      string    `json:"updatedAt"`
	DeletedAt      string    `json:"deletedAt"`
}

type CreateActivityDailyUserRequest struct {
	UserId int64 `json:"-"`
}
type CreateActivityDailyUserBody struct {
	Id                       int64     `json:"-"`
	DailyKey                 string    `json:"-"`
	UserId                   int64     `json:"-"`
	CollectedDate            time.Time `json:"-"`
	ActivityDayId            int64     `json:"-"`
	CollectedBonus           float64   `json:"-"`
	ActivityDailyConditionId int64     `json:"-"`
	AmountCondition          float64   `json:"-"`
	TidturnPercent           int64     `json:"-"`
}

type GetActivityDailyUserResponse struct {
	Id             int64     `json:"id"`
	UserId         int64     `json:"userId"`
	CollectedBonus float64   `json:"collectedBonus"`
	CollectedDate  time.Time `json:"collectedDate"`
	ActivityDayId  int64     `json:"activityDayId"`
}

type CheckDuplicateActivityDailyRequest struct {
	UserId             int64  `json:"userId"`
	CollectedDateCheck string `json:"collectedDateCheck"`
	DayId              int64  `json:"dayId"`
}

type CheckConditionUserRequest struct {
	UserId                 int64   `json:"userId"`
	PlayDate               string  `json:"playDate"`
	ConditionId            int64   `json:"conditionId"`
	DepositAmountCondition float64 `json:"depositAmountCondition"`
}

type GetSevenDayCollectedBounsResponse struct {
	CompletedBonus           float64 `json:"completedBonus"`
	CompletedBonusAvilable   bool    `json:"completedBonusAvilable"`
	CompletedCollected       bool    `json:"completedCollected"`
	ActivityDailyConditionId int64   `json:"activityDailyConditionId"`
	DepositAmountCondition   float64 `json:"depositAmountCondition"`
	ActivityDailyConditionTh string  `json:"activityDailyConditionTh"`
}

type CheckConfirmActivityDailyByDailyKeyResponse struct {
	Id             int64     `json:"id"`
	DailyKey       string    `json:"dailyKey"`
	UserId         int64     `json:"userId"`
	CollectedBonus float64   `json:"collectedBonus"`
	CollectedDate  time.Time `json:"collectedDate"`
	ActivityDayId  int64     `json:"activityDayId"`
}

type CheckDailyActivityUserTransactionListRequest struct {
	UserId   *int64 `form:"userId"`
	FromDate string `form:"fromDate"`
	ToDate   string `form:"toDate"`
}

type GetTurnoverUserActivityDailyRequest struct {
	DateType   string `form:"dateType" default:"daily"`
	FromDate   string `form:"fromDate"`
	ToDate     string `form:"toDate"`
	MemberCode string `form:"memberCode"`
	Page       int    `form:"page" default:"1" min:"1"`
	Limit      int    `form:"limit" default:"10" min:"1" max:"100"`
}

type GetTurnoverUserActivityDailyResponse struct {
	Id                         int64   `json:"id"`
	UserId                     int64   `json:"userId"`
	MemberCode                 string  `json:"memberCode"`  // รหัสสมาชิก
	Fullname                   string  `json:"fullname"`    // ชื่อ-นามสกุล
	Username                   string  `json:"username"`    // เบอร์โทรศัพท์
	Description                string  `json:"description"` // รายละเอียด
	ActivityDailyConditionId   int64   `json:"activityDailyConditionId"`
	ActivityDailyConditionName string  `json:"activityDailyConditionName"` // ชื่อเงื่อนไข
	AmountCondition            float64 `json:"amountCondition"`            // จำนวนเงื่อนไข

	TidturnPercent  int64      `json:"tidturnPercent"` // เปอร์เซ็นต์การติดเทิร์น
	TypeId          int64      `json:"typeId"`
	TypeName        string     `json:"typeName"`
	RefTypeId       int64      `json:"refTypeId"`
	BonusAmount     float64    `json:"bonusAmount"` // เครดิตรางวัล
	StatusId        int64      `json:"statusId"`
	StatusName      string     `json:"statusName"`      // สถานะเทิร์น
	StartTurnAmount float64    `json:"startTurnAmount"` // จำนวนเทิร์น
	StartTurnAt     *time.Time `json:"startTurnAt"`
	TotalTurnAmount float64    `json:"totalTurnAmount"`
	EndTurnAt       *time.Time `json:"endTurnAt"`
	CreatedAt       time.Time  `json:"createdAt"` // วันเวลา
	UpdatedAt       *time.Time `json:"updatedAt"`
}
