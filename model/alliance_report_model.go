package model

import "time"

const (
	ALLIANCE_INCOME_STATUS_PENDING = int64(1)
	ALLIANCE_INCOME_STATUS_READY   = int64(2)
	ALLIANCE_INCOME_STATUS_TAKEN   = int64(3)
)

type AlliancePlayLog struct {
	Id                int64     `json:"id"`
	Player            string    `json:"player"`
	TurnSport         float64   `json:"turnSport"`
	TurnCasino        float64   `json:"turnCasino"`
	TurnGame          float64   `json:"turnGame"`
	WinLoseSport      float64   `json:"winLoseSport"`
	WinLoseCasino     float64   `json:"winLoseCasino"`
	WinLoseGame       float64   `json:"winLoseGame"`
	ValidAmountSport  float64   `json:"validAmountSport"`
	ValidAmountCasino float64   `json:"validAmountCasino"`
	ValidAmountGame   float64   `json:"validAmountGame"`
	TurnTotal         float64   `json:"turnTotal"`
	WinLoseTotal      float64   `json:"winLoseTotal"`
	ValidAmountTotal  float64   `json:"validAmountTotal"`
	UserId            int64     `json:"userId"`
	RefUserId         int64     `json:"refUserId"`
	AlliancePercent   float64   `json:"alliancePercent"`
	CommissionPercent float64   `json:"commissionPercent"`
	Date              string    `json:"date"`
	CreatedAt         time.Time `json:"createdAt"`
}
type AlliancePlayLogListRequest struct {
	UserId   *int64 `form:"userId"`
	FromDate string `form:"fromDate"`
	ToDate   string `form:"toDate"`
	Page     int    `form:"page" default:"1" min:"1"`
	Limit    int    `form:"limit" default:"10" min:"0" max:"100"`
	SortCol  string `form:"sortCol"`
	SortAsc  string `form:"sortAsc"`
}
type AllianceBonusLog struct {
	Id                int64   `json:"id"`
	UserId            int64   `json:"userId"`
	TotalBonusAmount  float64 `json:"totalBonusAmount"`
	StatementDate     string  `json:"statementDate"`
	RefUserId         int64   `json:"refUserId"`
	AlliancePercent   float64 `json:"alliancePercent"`
	CommissionPercent float64 `json:"commissionPercent"`
}
type AllianceBonusLogListRequest struct {
	RefBy         *int64  `form:"refBy"`
	UserIds4      []int64 `form:"userIds"`
	StatementDate string  `form:"statementDate"`
	Page          int     `form:"page" default:"1" min:"1"`
	Limit         int     `form:"limit" default:"10" min:"0" max:"100"`
	SortCol       string  `form:"sortCol"`
	SortAsc       string  `form:"sortAsc"`
}

type AllianceUserListRequest struct {
	Search   string `form:"search"`
	DateType string `form:"dateType"`
	FromDate string `form:"fromDate"`
	ToDate   string `form:"toDate"`
	Page     int    `form:"page" default:"1" min:"1"`
	Limit    int    `form:"limit" default:"10" min:"0" max:"100"`
	SortCol  string `form:"sortCol"`
	SortAsc  string `form:"sortAsc"`
}
type AllianceUserResponse struct {
	UserId              int64   `json:"userId"`
	UserTypeId          int64   `json:"userTypeId"`
	MemberCode          string  `json:"memberCode"`
	Username            string  `json:"username"`
	UserFullname        string  `json:"userFullname"`
	AllianceName        string  `json:"allianceName"`
	AllianceCode        string  `json:"allianceCode"`
	AlliancePercent     float64 `json:"alliancePercent"`
	LinkClickTotal      int64   `json:"linkClickTotal"`
	NoMemberCodeTotal   int64   `json:"noMemberCodeTotal"`
	HaveMemberCodeTotal int64   `json:"haveMemberCodeTotal"`
	RecommendTotal      int64   `json:"recommendTotal"`
	FirstDepositAmount  float64 `json:"firstDepositAmount"`
}

type AllianceFirstDepositListRequest struct {
	RefUserId        int64  `form:"refUserId" validate:"required"`
	DateType         string `form:"dateType"`
	FromDate         string `form:"fromDate"`
	ToDate           string `form:"toDate"`
	RegisterFromDate string `form:"registerFromDate"`
	RegisterToDate   string `form:"registerToDate"`
	Page             int    `form:"page" default:"1" min:"1"`
	Limit            int    `form:"limit" default:"10" min:"0" max:"100"`
	SortCol          string `form:"sortCol"`
	SortAsc          string `form:"sortAsc"`
}
type AllianceFirstDepositSummaryResponse struct {
	RefUserId           int64   `json:"refUserId"`
	NoMemberCodeTotal   int64   `json:"noMemberCodeTotal"`
	HaveMemberCodeTotal int64   `json:"haveMemberCodeTotal"`
	FirstDepositTotal   int64   `json:"firstDepositTotal"`
	FirstDepositAmount  float64 `json:"firstDepositAmount"`
}
type AllianceUserFirstDepositResponse struct {
	UserId        int64     `json:"userId"`
	MemberCode    string    `json:"memberCode"`
	DepositAt     time.Time `json:"depositAt"`
	DepositAmount float64   `json:"depositAmount"`
	RegisterAt    time.Time `json:"registerAt"`
}

type AllianceWinLoseHistoryListRequest struct {
	RefUserId int64  `form:"refUserId" validate:"required"`
	DateType  string `form:"dateType"`
	FromDate  string `form:"fromDate"`
	ToDate    string `form:"toDate"`
	Page      int    `form:"page" default:"1" min:"1"`
	Limit     int    `form:"limit" default:"10" min:"0" max:"100"`
	SortCol   string `form:"sortCol"`
	SortAsc   string `form:"sortAsc"`
}
type AllianceWinLoseHistoryResponse struct {
	UserId                int64   `json:"userId"`
	MemberCode            string  `json:"memberCode"`
	TotalPlayAmount       float64 `json:"totalPlayAmount"`
	TotalWinLoseAmount    float64 `json:"totalWinLoseAmount"`
	TotalCommission       float64 `json:"totalCommission"`
	AllianceWinloseAmount float64 `json:"allianceWinloseAmount"`
	AllianceCommission    float64 `json:"allianceCommission"`
	TotalPaidBonus        float64 `json:"totalPaidBonus"`
	AlliancePaidBonus     float64 `json:"alliancePaidBonus"`
	AllianceIncome        float64 `json:"allianceIncome"`
	AlliancePendingIncome float64 `json:"alliancePendingIncome"`
	RegisterAt            string  `json:"registerAt"`
}
type AllianceWinLoseHistoryResponseWithPagination struct {
	Message    string                           `json:"message" validate:"required,min=1,max=255"`
	MemberCode string                           `json:"memberCode" validate:"required,min=1,max=255"`
	List       []AllianceWinLoseHistoryResponse `json:"list"`
	Total      int64                            `json:"total"`
}

type AllianceWinloseIncome struct {
	Id                    int64      `json:"id"`
	UserId                int64      `json:"userId"`
	StatusId              int64      `json:"statusId"`
	DailyKey              string     `json:"dailyKey"`
	StatementDate         string     `json:"statementDate"`
	OfDate                string     `json:"ofDate"`
	TotalPlayAmount       float64    `json:"totalPlayAmount"`
	TotalWinLoseAmount    float64    `json:"totalWinLoseAmount"`
	CommissionPercent     float64    `json:"commissionPercent"`
	AlliancePercent       float64    `json:"alliancePercent"`
	TotalCommission       float64    `json:"totalCommission"`
	AllianceWinloseAmount float64    `json:"allianceWinloseAmount"`
	AllianceCommission    float64    `json:"allianceCommission"`
	TotalPaidBonus        float64    `json:"totalPaidBonus"`
	AlliancePaidBonus     float64    `json:"alliancePaidBonus"`
	AllianceIncome        float64    `json:"allianceIncome"`
	TakeAt                *time.Time `json:"takeAt"`
	TakenPrice            float64    `json:"takenPrice"`
	CreatedAt             time.Time  `json:"createdAt"`
	UpdatedAt             time.Time  `json:"updatedAt"`
}
type AllianceWinloseIncomeCreateBody struct {
	Id                    int64   `json:"id"`
	UserId                int64   `json:"userId"`
	StatusId              int64   `json:"statusId"`
	DailyKey              string  `json:"dailyKey"`
	StatementDate         string  `json:"statementDate"`
	OfDate                string  `json:"ofDate"`
	TotalPlayAmount       float64 `json:"totalPlayAmount"`
	TotalWinLoseAmount    float64 `json:"totalWinLoseAmount"`
	CommissionPercent     float64 `json:"commissionPercent"`
	AlliancePercent       float64 `json:"alliancePercent"`
	TotalCommission       float64 `json:"totalCommission"`
	AllianceWinloseAmount float64 `json:"allianceWinloseAmount"`
	AllianceCommission    float64 `json:"allianceCommission"`
	TotalPaidBonus        float64 `json:"totalPaidBonus"`
	AlliancePaidBonus     float64 `json:"alliancePaidBonus"`
	AllianceIncome        float64 `json:"allianceIncome"`
}
type AllianceWinloseIncomeDailyKey struct {
	Id       int64  `json:"id"`
	UserId   int64  `json:"userId"`
	DailyKey string `json:"dailyKey"`
}
type AllianceWinloseIncomeUpdateTakenBody struct {
	StatusId   int64      `json:"statusId"`
	TakeAt     *time.Time `json:"takeAt"`
	TakenPrice float64    `json:"takenPrice"`
}
type AllianceWinlosePendingIncomeUpdateBody struct {
	TotalPlayAmount       float64 `json:"totalPlayAmount"`
	TotalWinLoseAmount    float64 `json:"totalWinLoseAmount"`
	CommissionPercent     float64 `json:"commissionPercent"`
	AlliancePercent       float64 `json:"alliancePercent"`
	TotalCommission       float64 `json:"totalCommission"`
	AllianceWinloseAmount float64 `json:"allianceWinloseAmount"`
	AllianceCommission    float64 `json:"allianceCommission"`
	TotalPaidBonus        float64 `json:"totalPaidBonus"`
	AlliancePaidBonus     float64 `json:"alliancePaidBonus"`
	AllianceIncome        float64 `json:"allianceIncome"`
}

type AllianceTotalWinLoseHistoryResponse struct {
	RefUserId             int64   `json:"refUserId"`
	FromDate              string  `json:"fromDate"`
	ToDate                string  `json:"toDate"`
	TotalPlayAmount       float64 `json:"totalPlayAmount"`
	TotalWinLoseAmount    float64 `json:"totalWinLoseAmount"`
	TotalCommission       float64 `json:"totalCommission"`
	AllianceWinloseAmount float64 `json:"allianceWinloseAmount"`
	AllianceCommission    float64 `json:"allianceCommission"`
	TotalPaidBonus        float64 `json:"totalPaidBonus"`
	AlliancePaidBonus     float64 `json:"alliancePaidBonus"`
	AllianceIncome        float64 `json:"allianceIncome"`
	AlliancePendingIncome float64 `json:"alliancePendingIncome"`
}

type AllianceIncomeWithdraw struct {
	Id           int64   `json:"id"`
	RefUserId    int64   `json:"refUserId"`
	IncomeAmount float64 `json:"incomeAmount"`
	FromDate     string  `json:"fromDate"`
	ToDate       string  `json:"toDate"`
	CreatedBy    int64   `json:"createdBy"`
	CreatedAt    string  `json:"createdAt"`
}
type AllianceIncomeWithdrawCreateRequest struct {
	RefUserId    int64   `json:"refUserId" validate:"required"`
	IncomeAmount float64 `json:"incomeAmount" validate:"required"`
	FromDate     string  `json:"fromDate" validate:"required"`
	ToDate       string  `json:"toDate" validate:"required"`
	TransferType string  `json:"transferType"`
	CreatedBy    int64   `json:"-"`
}
type AllianceIncomeWithdrawCreateBody struct {
	Id           int64   `json:"id"`
	RefUserId    int64   `json:"refUserId"`
	IncomeAmount float64 `json:"incomeAmount"`
	FromDate     string  `json:"fromDate"`
	ToDate       string  `json:"toDate"`
	CreatedBy    int64   `json:"createdBy"`
}
type AllianceIncomeWithdrawHistoryListRequest struct {
	RefUserId int64  `form:"refUserId" validate:"required"`
	FromDate  string `form:"fromDate"`
	ToDate    string `form:"toDate"`
	Page      int    `form:"page" default:"1" min:"1"`
	Limit     int    `form:"limit" default:"10" min:"0" max:"100"`
	SortCol   string `form:"sortCol"`
	SortAsc   string `form:"sortAsc"`
}
type AllianceIncomeWithdrawHistoryResponse struct {
	Id                int64   `json:"id"`
	RefUserId         int64   `json:"refUserId"`
	FromDate          string  `json:"fromDate"`
	ToDate            string  `json:"toDate"`
	IncomeAmount      float64 `json:"incomeAmount"`
	CreatedAt         string  `json:"createdAt"`
	CreatedBy         int64   `json:"createdBy"`
	CreatedByUsername string  `json:"createdByUsername"`
}

type AllianceBankTransactionListRequest struct {
	RefUserId int64  `form:"refUserId" validate:"required"`
	DateType  string `form:"dateType"`
	FromDate  string `form:"fromDate"`
	ToDate    string `form:"toDate"`
	Page      int    `form:"page" default:"1" min:"1"`
	Limit     int    `form:"limit" default:"10" min:"0" max:"100"`
	SortCol   string `form:"sortCol"`
	SortAsc   string `form:"sortAsc"`
}
type AllianceBankTransactionResponse struct {
	RefUserId           int64   `json:"refUserId"`
	UserId              int64   `json:"userId"`
	MemberCode          string  `json:"memberCode"`
	TotalDepositAmount  float64 `json:"totalDepositAmount"`
	TotalWithdrawAmount float64 `json:"totalWithdrawAmount"`
	TotalBonusAmount    float64 `json:"totalBonusAmount"`
	TotalAmount         float64 `json:"totalAmount"`
}
type AllianceBankTransactionResponseWithPagination struct {
	Message    string                            `json:"message" validate:"required,min=1,max=255"`
	MemberCode string                            `json:"memberCode" validate:"required,min=1,max=255"`
	List       []AllianceBankTransactionResponse `json:"list"`
	Total      int64                             `json:"total"`
}

type AllianceWinLoseTotalListRequest struct {
	DateType string `form:"dateType"`
	FromDate string `form:"fromDate"`
	ToDate   string `form:"toDate"`
	Page     int    `form:"page" default:"1" min:"1"`
	Limit    int    `form:"limit" default:"10" min:"0" max:"100"`
	SortCol  string `form:"sortCol"`
	SortAsc  string `form:"sortAsc"`
}
type AllianceWinLoseSumTotalRequest struct {
	DateType string `form:"dateType"`
	FromDate string `form:"fromDate"`
	ToDate   string `form:"toDate"`
}
type AllianceWinLoseTotalResponse struct {
	UserId                   int64   `json:"userId"`
	MemberCode               string  `json:"memberCode"`
	UserFullname             string  `json:"userFullname"`
	TotalPlayAmount          float64 `json:"totalPlayAmount"`
	TotalWinLoseAmount       float64 `json:"totalWinLoseAmount"`
	TotalCommission          float64 `json:"totalCommission"`
	AllianceWinloseAmount    float64 `json:"allianceWinloseAmount"`
	AllianceCommission       float64 `json:"allianceCommission"`
	TotalPaidBonus           float64 `json:"totalPaidBonus"`
	AlliancePaidBonus        float64 `json:"alliancePaidBonus"`
	AllianceIncome           float64 `json:"allianceIncome"`
	FromDate                 string  `json:"fromDate"`
	ToDate                   string  `json:"toDate"`
	AlliancePendingIncome    float64 `json:"alliancePendingIncome"`
	AffiliateMemberTotal     float64 `json:"affiliateMemberTotal"`
	PromotionReturnLossTotal float64 `json:"promotionReturnLossTotal"`
	LuckyWheelTotal          float64 `json:"luckyWheelTotal"`
	CouponTotal              float64 `json:"couponTotal"`
	CreditBonusTotal         float64 `json:"creditBonusTotal"`
	PromotionWebTotal        float64 `json:"promotionWebTotal"`
	PromotionReturnTurnTotal float64 `json:"promotionReturnTurnTotal"`
	DailyActivityBonusTotal  float64 `json:"dailyActivityBonusTotal"`
}
type GetSumAllianceWinLoseTotalResponse struct {
	TotalPlayAmount          float64 `json:"totalPlayAmount"`
	TotalWinLoseAmount       float64 `json:"totalWinLoseAmount"`
	TotalCommission          float64 `json:"totalCommission"`
	AllianceWinloseAmount    float64 `json:"allianceWinloseAmount"`
	AllianceCommission       float64 `json:"allianceCommission"`
	TotalPaidBonus           float64 `json:"totalPaidBonus"`
	AlliancePaidBonus        float64 `json:"alliancePaidBonus"`
	AllianceIncome           float64 `json:"allianceIncome"`
	AlliancePendingIncome    float64 `json:"alliancePendingIncome"`
	AffiliateMemberTotal     float64 `json:"affiliateMemberTotal"`
	PromotionReturnLossTotal float64 `json:"promotionReturnLossTotal"`
	LuckyWheelTotal          float64 `json:"luckyWheelTotal"`
	CouponTotal              float64 `json:"couponTotal"`
	CreditBonusTotal         float64 `json:"creditBonusTotal"`
	PromotionWebTotal        float64 `json:"promotionWebTotal"`
	PromotionReturnTurnTotal float64 `json:"promotionReturnTurnTotal"`
	DailyActivityBonusTotal  float64 `json:"dailyActivityBonusTotal"`
}

type JsonVendor struct {
	Name        string   `json:"Name"`
	VendorCode  string   `json:"VendorCode"`
	VendorAlias []string `json:"VendorAlias"`
	ImageName   string   `json:"ImageName"`
	Category    string   `json:"Category"`
}

type AllianceUserSumPlayLog struct {
	Date                 string  `json:"date"`
	SumTurnSport         float64 `json:"turnSport"`
	SumTurnCasino        float64 `json:"turnCasino"`
	SumTurnGame          float64 `json:"turnGame"`
	SumWinLoseSport      float64 `json:"winLoseSport"`
	SumWinLoseCasino     float64 `json:"winLoseCasino"`
	SumWinLoseGame       float64 `json:"winLoseGame"`
	SumValidAmountSport  float64 `json:"validAmountSport"`
	SumValidAmountCasino float64 `json:"validAmountCasino"`
	SumValidAmountGame   float64 `json:"validAmountGame"`
	SumTurnTotal         float64 `json:"turnTotal"`
	SumWinLoseTotal      float64 `json:"winLoseTotal"`
	SumValidAmountTotal  float64 `json:"validAmountTotal"`
	UserId               int64   `json:"userId"`
	RefUserId            int64   `json:"refUserId"`
	AlliancePercent      float64 `json:"alliancePercent"`
	CommissionPercent    float64 `json:"commissionPercent"`
}

type GetAgentGameCategoryListFromMasterRequest struct {
	AgentType    string `form:"agentType"`
	CategoryName string `form:"categoryName"`
}

type GetAgentGameCategoryListFromMasterResponse struct {
	Data map[string][]GameCategory `json:"data"`
}

type GetAgentGameRequestList struct {
	AgentType  string `form:"agentType"`
	VendorCode string `form:"vendorCode"`
}
