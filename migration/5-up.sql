
-- 2024-12-18 AGENT F888 ให้ RUN MANUAL

CREATE TABLE IF NOT EXISTS `agf888_hook` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `name` VA<PERSON>HA<PERSON>(255) NOT NULL,
    `json_payload` LONGTEXT NOT NULL,
    `created_at` DATETIME DEFAULT (CURRENT_TIMESTAMP),
    PRIMARY KEY (`id`)
);

CREATE TABLE IF NOT EXISTS `agf888_user` (
	`id` BIGINT NOT NULL AUTO_INCREMENT,
	`member_code` VARCHAR(255) NULL DEFAULT NULL,
	`fullname` VARCHAR(255) NULL DEFAULT NULL,
	`credit` DECIMAL(14,2) NULL DEFAULT '0.00',
	`remark` VARCHAR(255) NULL DEFAULT NULL,
	`last_action_at` DATETIME NULL DEFAULT NULL,
	`created_at` DATETIME NULL DEFAULT (CURRENT_TIMESTAMP),
	`updated_at` DATETIME NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
	`deleted_at` DATETIME NULL DEFAULT NULL,
	PRIMARY KEY (`id`),
	UNIQUE INDEX `uni_member_code` (`member_code`)
);

CREATE TABLE IF NOT EXISTS `agf888_user_transaction_type` (
	`id` BIGINT NOT NULL AUTO_INCREMENT,
	`name` VARCHAR(255) NULL,
	`created_at` DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP),
	PRIMARY KEY (`id`)
);
INSERT INTO `agf888_user_transaction_type` (`id`, `name`) VALUES
  (1, 'DEPOSIT'),
  (2, 'WITHDRAW'),
  (3, 'BONUS'),
  (4, 'CREDIT_BACK'),
  (5, 'BET_PLACE'),
  (6, 'BET_SETTLE'),
  (7, 'BET_VOID');

CREATE TABLE IF NOT EXISTS `agf888_user_transaction` (
	`id` BIGINT NOT NULL AUTO_INCREMENT,
	`user_id` BIGINT NOT NULL,
	`type_id` BIGINT NOT NULL,
	`ref1` VARCHAR(255) NULL,
	`ref2` VARCHAR(255) NULL,
	`detail` VARCHAR(255) NOT NULL DEFAULT '',
	`transfer_at` DATETIME NOT NULL,
	`credit_before` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
	`credit_amount` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
	`credit_after` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
	`created_at` DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP),
	PRIMARY KEY (`id`),
	INDEX `idx_type_id` (`type_id`),
	INDEX `idx_user_id` (`user_id`),
	INDEX `idx_transfer_at` (`transfer_at`)
);

CREATE TABLE IF NOT EXISTS `agf888_user_playlog` (
	`id` BIGINT NOT NULL AUTO_INCREMENT,
	`user_id` BIGINT NOT NULL,
	`statement_date` DATE NOT NULL,
	`daily_key` VARCHAR(255) NULL DEFAULT NULL,
	`turn_sport` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
	`valid_amount_sport` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
	`win_lose_sport` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
	`turn_casino` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
	`valid_amount_casino` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
	`win_lose_casino` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
	`turn_game` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
	`valid_amount_game` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
	`win_lose_game` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
	`turn_lottery` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
	`valid_amount_lottery` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
	`win_lose_lottery` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
	`turn_p2p` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
	`valid_amount_p2p` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
	`win_lose_p2p` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
	`turn_financial` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
	`valid_amount_financial` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
	`win_lose_financial` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
	`turn_total` DECIMAL(14,2) NULL DEFAULT '0.00',
	`win_lose_total` DECIMAL(14,2) NULL DEFAULT '0.00',
	`valid_amount_total` DECIMAL(14,2) NULL DEFAULT '0.00',
	`created_at` DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP),
	PRIMARY KEY (`id`),
	UNIQUE INDEX `daily_key` (`daily_key`),
	INDEX `user_id` (`user_id`),
	INDEX `statement_date` (`statement_date`)
);

-- UP PROD แล้ว 2025-02-28 