
-- CHECK OLD PAYGATE SYSTEM LOG
SELECT 'db_allforwin.paygate_system_log' as name ,COUNT(*) FROM `db_allforwin`.`paygate_system_log` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 3 MONTH)
UNION ALL
SELECT 'db_demo.paygate_system_log' as name ,COUNT(*) FROM `db_demo`.`paygate_system_log` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 3 MONTH)
UNION ALL
SELECT 'db_grandcasino.paygate_system_log' as name ,COUNT(*) FROM `db_grandcasino`.`paygate_system_log` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 3 MONTH)
UNION ALL
SELECT 'db_luckytree.paygate_system_log' as name ,COUNT(*) FROM `db_luckytree`.`paygate_system_log` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 3 MONTH)
UNION ALL
SELECT 'db_munmax.paygate_system_log' as name ,COUNT(*) FROM `db_munmax`.`paygate_system_log` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 3 MONTH)
UNION ALL
SELECT 'db_one1.paygate_system_log' as name ,COUNT(*) FROM `db_one1`.`paygate_system_log` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 3 MONTH)
UNION ALL
SELECT 'db_sagame1991.paygate_system_log' as name ,COUNT(*) FROM `db_sagame1991`.`paygate_system_log` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 3 MONTH)
UNION ALL
SELECT 'db_sbo88thailand.paygate_system_log' as name ,COUNT(*) FROM `db_sbo88thailand`.`paygate_system_log` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 3 MONTH)
UNION ALL
SELECT 'db_sexypgth.paygate_system_log' as name ,COUNT(*) FROM `db_sexypgth`.`paygate_system_log` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 3 MONTH)
UNION ALL
SELECT 'db_solo1688.paygate_system_log' as name ,COUNT(*) FROM `db_solo1688`.`paygate_system_log` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 3 MONTH)
UNION ALL
SELECT 'db_solo78.paygate_system_log' as name ,COUNT(*) FROM `db_solo78`.`paygate_system_log` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 3 MONTH)
UNION ALL
SELECT 'db_solopromax.paygate_system_log' as name ,COUNT(*) FROM `db_solopromax`.`paygate_system_log` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 3 MONTH)
UNION ALL
SELECT 'db_x365.paygate_system_log' as name ,COUNT(*) FROM `db_x365`.`paygate_system_log` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 3 MONTH);

-- CLEAN UP OLD PAYGATE SYSTEM LOG
-- DELETE FROM `paygate_system_log` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 3 MONTH);
-- ANALYZE TABLE `paygate_system_log`;

-- CHECK OLD API STATUS LOG
SELECT * FROM `db_allforwin`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_demo`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_grandcasino`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_luckytree`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_munmax`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_one1`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_sagame1991`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_sbo88thailand`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_sexypgth`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_solo1688`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_solo78`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_solopromax`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_x365`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1;

-- CLEAN UP OLD API STATUS LOG
-- DELETE FROM `api_status` WHERE is_success = 1 AND `created_at` < DATE_SUB(NOW(), INTERVAL 3 MONTH);
-- SELECT * FROM `api_status` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 3 MONTH);

SELECT * FROM `db_allforwin`.`turnover_setting`
UNION ALL
SELECT * FROM `db_demo`.`turnover_setting`
UNION ALL
SELECT * FROM `db_grandcasino`.`turnover_setting`
UNION ALL
SELECT * FROM `db_luckytree`.`turnover_setting`
UNION ALL
SELECT * FROM `db_munmax`.`turnover_setting`
UNION ALL
SELECT * FROM `db_one1`.`turnover_setting`
UNION ALL
SELECT * FROM `db_sagame1991`.`turnover_setting`
UNION ALL
SELECT * FROM `db_sbo88thailand`.`turnover_setting`
UNION ALL
SELECT * FROM `db_sexypgth`.`turnover_setting`
UNION ALL
SELECT * FROM `db_solo1688`.`turnover_setting`
UNION ALL
SELECT * FROM `db_solo78`.`turnover_setting`
UNION ALL
SELECT * FROM `db_solopromax`.`turnover_setting`
UNION ALL
SELECT * FROM `db_x365`.`turnover_setting`;

SELECT 'db_allforwin' AS web, name, renewal_credits, updated_at FROM `db_allforwin`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_demo' AS web, name, renewal_credits, updated_at FROM `db_demo`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_grandcasino' AS web, name, renewal_credits, updated_at FROM `db_grandcasino`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_luckytree' AS web, name, renewal_credits, updated_at FROM `db_luckytree`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_munmax' AS web, name, renewal_credits, updated_at FROM `db_munmax`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_one1' AS web, name, renewal_credits, updated_at FROM `db_one1`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_sagame1991' AS web, name, renewal_credits, updated_at FROM `db_sagame1991`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_sbo88thailand' AS web, name, renewal_credits, updated_at FROM `db_sbo88thailand`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_sexypgth' AS web, name, renewal_credits, updated_at FROM `db_sexypgth`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_solo1688' AS web, name, renewal_credits, updated_at FROM `db_solo1688`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_solo78' AS web, name, renewal_credits, updated_at FROM `db_solo78`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_solopromax' AS web, name, renewal_credits, updated_at FROM `db_solopromax`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_x365' AS web, name, renewal_credits, updated_at FROM `db_x365`.`renewal_fastbank_package`;