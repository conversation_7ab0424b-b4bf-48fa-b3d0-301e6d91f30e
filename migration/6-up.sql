

-- lotto migration xxxx-xx-xx
    
CREATE TABLE IF NOT EXISTS `agent_cblotto_setting` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `is_active` TINYINT NOT NULL DEFAULT 0,
    `program_allow_use` ENUM('ALLOW_USE','NOT_ALLOW_USE') NOT NULL DEFAULT 'NOT_ALLOW_USE',
    `cblotto_app_id` VARCHAR(255) DEFAULT '',
    `cblotto_app_private` VARCHAR(255) DEFAULT '',
    `cblotto_url` VARCHAR(255) DEFAULT '',
    `cblotto_href_back_url` VARCHAR(255) DEFAULT '',
    `created_at` DATETIME DEFAULT NOW(),
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
    `updated_by_id` BIGINT NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
);

-- cblotto-dev-api.cbgame88.com 
-- cblotto-api.cbgame88.com 

INSERT INTO `agent_cblotto_setting` (`id`, `is_active`, `program_allow_use`, `cblotto_url`) VALUES
(1, 0, 'ALLOW_USE', 'https://cblotto-api.cbgame88.com');

CREATE TABLE IF NOT EXISTS `agent_cblotto_callback` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT NOT NULL,
    `member_code` VARCHAR(255) NOT NULL,
    `payoff` DECIMAL(14,2) NOT NULL,
    `bet_amount` DECIMAL(14,2) NOT NULL,
    `winlose_amount` DECIMAL(14,2) NOT NULL,
    `cancel_amount` DECIMAL(14,2) NOT NULL,
    `balance` DECIMAL(14,2) NOT NULL,
    `before_balance` DECIMAL(14,2) NOT NULL,
    `after_balance` DECIMAL(14,2) NOT NULL,
    `transaction_id` VARCHAR(255) NOT NULL,
    `round_id` VARCHAR(255) NOT NULL, -- ใช้ round ID เป็นหลัก เพราะมัน stack up จาก round
    `game_id` VARCHAR(255) NOT NULL,
    `callback_reason` VARCHAR(255) NOT NULL,
    `remark` VARCHAR(255) NOT NULL,
    `is_success` TINYINT NOT NULL DEFAULT 0,
    `created_at` DATETIME DEFAULT NOW(),
    PRIMARY KEY (`id`)
);

CREATE INDEX `idx_user_id` ON `agent_cblotto_callback` (`user_id`);
CREATE INDEX `idx_member_code` ON `agent_cblotto_callback` (`member_code`);
CREATE INDEX `idx_is_round_id` ON `agent_cblotto_callback` (`round_id`);
CREATE INDEX `idx_is_success` ON `agent_cblotto_callback` (`is_success`);
