-- --------------------------------------------------------
-- Host:                         db-cyberrich-prod.ondigitalocean.com
-- Server version:               8.0.30 - Source distribution
-- Server OS:                    Linux
-- HeidiSQL Version:             12.5.0.6677
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

use db_xxx;

CREATE TABLE IF NOT EXISTS `account_move_transaction_status` (
  `id` bigint NOT NULL,
  `name` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
INSERT INTO `account_move_transaction_status` (`id`, `name`, `created_at`) VALUES
	(1, 'รอโอน', '2023-11-14 19:15:30'),
	(2, 'สำเร็จ', '2023-11-14 19:15:30'),
	(3, 'ไม่สำเร็จ', '2023-11-14 19:15:30');

CREATE TABLE IF NOT EXISTS `account_move_transaction` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `admin_id` bigint DEFAULT NULL,
  `from_account_id` bigint NOT NULL,
  `to_account_id` bigint NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `status_id` bigint NOT NULL DEFAULT '1',
  `transfer_at` datetime DEFAULT NULL,
  `remark` varchar(255) DEFAULT NULL,
  `is_success` tinyint NOT NULL DEFAULT '0',
  `json_fastbank_resp` text,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);

CREATE TABLE IF NOT EXISTS `account_status`(
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT,
    `name` ENUM('ACTIVE', 'DEACTIVE') NULL DEFAULT NULL,
    `label_th` VARCHAR(255) NULL DEFAULT NULL,
    `label_en` VARCHAR(255) NULL DEFAULT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
    `deleted_at` DATETIME NULL DEFAULT NULL
);
INSERT INTO `account_status` (`id`, `name`, `label_th`, `label_en`) VALUES
  (1, 'ACTIVE', 'ใช้งาน', 'active'),
  (2, 'DEACTIVE', 'ระงับการใช้งาน', 'deactive');


CREATE TABLE IF NOT EXISTS `admin_action_type` (
  `id` bigint NOT NULL,
  `name` varchar(255) NOT NULL DEFAULT '',
  `detail` varchar(255) NOT NULL DEFAULT '',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);

INSERT INTO `admin_action_type` (`id`, `name`, `detail`, `created_at`) VALUES
	(4, 'MOVE_ACCOUNT_MONEY', 'Admin Move Money', '2023-10-21 09:37:47'),
	(5, 'LOGOUT', 'Admin Logout', '2023-11-07 03:47:45'),
	(6, 'MANAGE', 'Admin Manage', '2023-11-07 03:47:45'),
	(7, 'LOGIN', 'Admin Login', '2023-11-07 03:47:45'),
	(8, 'MANAGE_APPLICATION', 'Application Config', '2023-11-07 03:47:45'),
	(9, 'ACCOUNT_MANAGE', 'Bank Account', '2023-11-07 03:47:45'),
	(10, 'GROUP_CREATE', 'Manage Admin Group', '2023-11-07 03:47:45');

CREATE TABLE IF NOT EXISTS `admin_action` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `admin_id` bigint NOT NULL,
  `type_id` bigint NOT NULL DEFAULT '1',
  `is_success` tinyint NOT NULL DEFAULT '0',
  `ref_object_id` bigint DEFAULT NULL,
  `detail` varchar(255) DEFAULT NULL,
  `json_input` text,
  `json_output` text,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `admin_action_user_id_index` (`admin_id`)
);

CREATE TABLE IF NOT EXISTS `admin_group_permission` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `group_id` bigint DEFAULT NULL,
  `permission_id` bigint DEFAULT NULL,
  `is_read` tinyint DEFAULT '0',
  `is_write` tinyint DEFAULT '0',
  `created_at` datetime DEFAULT (now()),
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_deleted_at` (`deleted_at`),
  KEY `idx_group_id` (`group_id`),
  KEY `idx_permission_id` (`permission_id`)
);

CREATE TABLE IF NOT EXISTS `admin_group` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `admin_count` int DEFAULT '0',
  `created_at` datetime DEFAULT (now()),
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni_name` (`name`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_deleted_at` (`deleted_at`)
);

CREATE TABLE IF NOT EXISTS `admin` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `username` varchar(255) DEFAULT NULL,
  `password` varchar(255) DEFAULT NULL,
  `role` enum('SUPER_ADMIN','ADMIN') DEFAULT NULL,
  `status` enum('ACTIVE','DEACTIVE') DEFAULT NULL,
  `firstname` varchar(255) DEFAULT NULL,
  `lastname` varchar(255) DEFAULT NULL,
  `fullname` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `phone` varchar(255) DEFAULT NULL,
  `ip` varchar(255) DEFAULT NULL,
  `admin_group_id` int DEFAULT NULL,
  `logedin_at` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT (now()),
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni_email` (`email`),
  UNIQUE KEY `uni_username` (`username`),
  KEY `idx_admin_group_id` (`admin_group_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_deleted_at` (`deleted_at`),
  KEY `idx_status` (`status`)
);
INSERT INTO `admin` (`id`, `username`, `password`, `role`, `status`, `firstname`, `lastname`, `fullname`, `email`, `phone`, `ip`, `admin_group_id`, `logedin_at`, `created_at`, `updated_at`, `deleted_at`) VALUES
	(1, 'superadmin', '', 'SUPER_ADMIN', 'ACTIVE', 'admin', 'admin', NULL, NULL, NULL, NULL, NULL, NULL, '2023-10-30 15:39:28', NULL, NULL);


CREATE TABLE IF NOT EXISTS `affiliate_commission` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `first_deposit_bonus` decimal(14,2) DEFAULT '0.00',
  `sport` decimal(14,2) DEFAULT '0.00',
  `casino` decimal(14,2) DEFAULT '0.00',
  `slot` decimal(14,2) DEFAULT '0.00',
  `referral_bonus` decimal(14,2) DEFAULT '0.00',
  `commission_withdraw_min` int DEFAULT '0',
  `register_bonus_type_id` bigint DEFAULT NULL,
  `register_bonus_min` int DEFAULT '0',
  `register_bonus_credit` int DEFAULT '0',
  `register_bonus_max_percent` int DEFAULT '0',
  `description` text,
  `register_bonus_option_id` bigint DEFAULT NULL,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `register_bonus_option_id` (`register_bonus_option_id`),
  KEY `register_bonus_type_id` (`register_bonus_type_id`),
  CONSTRAINT `affiliate_commission_ibfk_1` FOREIGN KEY (`register_bonus_type_id`) REFERENCES `register_bonus_type` (`id`),
  CONSTRAINT `affiliate_commission_ibfk_2` FOREIGN KEY (`register_bonus_option_id`) REFERENCES `register_bonus_option` (`id`)
);
INSERT INTO `affiliate_commission` (`id`, `first_deposit_bonus`, `sport`, `casino`, `slot`, `referral_bonus`, `commission_withdraw_min`, `register_bonus_type_id`, `register_bonus_min`, `register_bonus_credit`, `register_bonus_max_percent`, `description`, `register_bonus_option_id`, `updated_at`) VALUES
	(1, 0.00, 0.70, 0.40, 0.40, 20.00, 50, 1, 300, 50, 0, NULL, 3, '2023-10-31 08:12:36');

CREATE TABLE IF NOT EXISTS `affiliate_income` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `sport` decimal(14,2) DEFAULT '0.00',
  `casino` decimal(14,2) DEFAULT '0.00',
  `slot` decimal(14,2) DEFAULT '0.00',
  `user_id` bigint NOT NULL,
  `ref_id` bigint DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `affiliate_income_ref_id_fk` (`ref_id`),
  KEY `affiliate_income_user_id_fk` (`user_id`),
  CONSTRAINT `affiliate_income_ref_id_fk` FOREIGN KEY (`ref_id`) REFERENCES `user` (`id`),
  CONSTRAINT `affiliate_income_user_id_fk` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
);

CREATE TABLE IF NOT EXISTS `affiliate` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `register_bonus_credit` decimal(14,2) DEFAULT '0.00',
  `ref_id` bigint NOT NULL,
  `user_id` bigint NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `affiliate_ref_id_user_id_uindex` (`ref_id`,`user_id`),
  KEY `idx_user_id` (`user_id`),
  CONSTRAINT `affiliate_Users_id_fk` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`),
  CONSTRAINT `fk_ref_id` FOREIGN KEY (`ref_id`) REFERENCES `user` (`id`)
);

CREATE TABLE IF NOT EXISTS `agent_info` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `total` int DEFAULT '0',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
INSERT INTO `agent_info` (`id`, `total`, `updated_at`) VALUES
	(1, 0, '2023-11-12 14:09:01');

CREATE TABLE IF NOT EXISTS `alliance_commission` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `referral_bonus` decimal(14,2) NOT NULL DEFAULT '0.00',
  `description` text,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
INSERT INTO `alliance_commission` (`id`, `referral_bonus`, `description`, `created_at`, `updated_at`) VALUES
	(1, 10.00, '', '2023-10-10 08:48:35', '2023-10-30 11:33:57');

CREATE TABLE IF NOT EXISTS `alliance_first_deposit` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `credit` decimal(14,2) NOT NULL DEFAULT '0.00',
  `user_id` bigint NOT NULL,
  `ref_id` bigint NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `alliance_first_deposit_ref_id_fk` (`ref_id`),
  KEY `alliance_first_deposit_user_id_fk` (`user_id`),
  CONSTRAINT `alliance_first_deposit_ref_id_fk` FOREIGN KEY (`ref_id`) REFERENCES `user` (`id`),
  CONSTRAINT `alliance_first_deposit_user_id_fk` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
);

CREATE TABLE IF NOT EXISTS `alliance_income` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `turn` decimal(14,2) NOT NULL DEFAULT '0.00',
  `turn_calculated` decimal(14,2) NOT NULL DEFAULT '0.00',
  `win_lose` decimal(14,2) NOT NULL DEFAULT '0.00',
  `win_lose_calculated` decimal(14,2) NOT NULL DEFAULT '0.00',
  `user_id` bigint NOT NULL,
  `ref_id` bigint NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `alliance_income_ref_id_fk` (`ref_id`),
  KEY `alliance_income_user_id_fk` (`user_id`),
  CONSTRAINT `alliance_income_ref_id_fk` FOREIGN KEY (`ref_id`) REFERENCES `user` (`id`),
  CONSTRAINT `alliance_income_user_id_fk` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
);

CREATE TABLE IF NOT EXISTS `alliance_transaction` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `deposit` decimal(14,2) NOT NULL DEFAULT '0.00',
  `withdraw` decimal(14,2) NOT NULL DEFAULT '0.00',
  `net_amount` decimal(14,2) NOT NULL DEFAULT '0.00',
  `user_id` bigint NOT NULL,
  `ref_id` bigint NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `alliance_transaction_ref_id_fk` (`ref_id`),
  KEY `alliance_transaction_user_id_fk` (`user_id`),
  CONSTRAINT `alliance_transaction_ref_id_fk` FOREIGN KEY (`ref_id`) REFERENCES `user` (`id`),
  CONSTRAINT `alliance_transaction_user_id_fk` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
);

CREATE TABLE IF NOT EXISTS `alliance` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `ref_id` bigint NOT NULL,
  `user_id` bigint NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `alliance_user_id_fk` (`user_id`),
  CONSTRAINT `alliance_user_id_fk` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
);

CREATE TABLE IF NOT EXISTS `api_status` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `path` varchar(255) NOT NULL,
  `page` int DEFAULT '0',
  `is_failed` tinyint DEFAULT '0',
  `is_success` tinyint DEFAULT '0',
  `statement_date` varchar(10) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_path` (`path`),
  KEY `idx_stement_date` (`statement_date`)
);

CREATE TABLE IF NOT EXISTS `auto_user_approve_type` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `label_th` varchar(255) DEFAULT NULL,
  `label_en` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
);
INSERT INTO `auto_user_approve_type` (`id`, `name`, `label_th`, `label_en`, `created_at`, `updated_at`, `deleted_at`) VALUES
	(1, 'after signing up', 'รับ user ทันทีหลังสมัครสมาชิก', 'Approved users immediately after signing up', '2023-09-18 12:00:15', NULL, NULL),
	(2, 'first deposit', 'รับ user หลังจากฝากครั้งแรก', 'Approved user after first deposit', '2023-09-18 12:00:15', NULL, NULL);

CREATE TABLE IF NOT EXISTS `auto_withdraw_type` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `label_th` varchar(255) DEFAULT NULL,
  `label_en` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
);
INSERT INTO `auto_withdraw_type` (`id`, `name`, `label_th`, `label_en`, `created_at`, `updated_at`, `deleted_at`) VALUES
	(1, 'withdraw Document', 'สร้างใบงานถอน', 'Create a withdraw Document', '2023-09-18 12:00:15', NULL, NULL),
	(2, 'withdraw auto', 'ปรับเครดิต ถอนออโต้', 'Adjust credit, auto withdraw', '2023-09-18 12:00:15', NULL, NULL);

CREATE TABLE IF NOT EXISTS `bank_account_priority` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `condition_type` varchar(255) NOT NULL DEFAULT 'or',
  `min_deposit_count` int NOT NULL DEFAULT '0',
  `min_deposit_total` decimal(14,2) NOT NULL DEFAULT '0.00',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
);
INSERT INTO `bank_account_priority` (`id`, `name`, `condition_type`, `min_deposit_count`, `min_deposit_total`, `created_at`, `updated_at`, `deleted_at`) VALUES
	(1, 'ระดับ NEW ทั่วไป', 'or', 0, 0.00, '2023-09-18 12:00:14', NULL, NULL),
	(2, 'ระดับ Gold ฝากมากกว่า 10 ครั้ง หรือ ฝากสะสมมากกว่า 10,000 บาท', 'or', 10, 10000.00, '2023-09-18 12:00:14', NULL, NULL),
	(3, 'ระดับ Platinum ฝากมากกว่า 20 ครั้ง หรือ ฝากสะสมมากกว่า 100,000 บาท', 'or', 20, 100000.00, '2023-09-18 12:00:14', NULL, NULL),
	(4, 'ระดับ VIP ฝากมากกว่า 30 ครั้ง หรือ ฝากสะสมมากกว่า 500,000 บาท', 'or', 30, 500000.00, '2023-09-18 12:00:14', NULL, NULL);


CREATE TABLE IF NOT EXISTS `bank_account_transaction` (
  `id` int NOT NULL AUTO_INCREMENT,
  `account_id` bigint NOT NULL,
  `description` varchar(255) NOT NULL,
  `transaction_type_id` bigint NOT NULL,
  `amount` decimal(14,2) NOT NULL,
  `transfer_at` datetime NOT NULL,
  `created_by_username` varchar(255) NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_account_id` (`account_id`),
  KEY `transaction_type_id` (`transaction_type_id`),
  CONSTRAINT `bank_account_transaction_ibfk_1` FOREIGN KEY (`transaction_type_id`) REFERENCES `transaction_type` (`id`)
);


CREATE TABLE IF NOT EXISTS `bank_account_type` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `limit_flag` varchar(8) NOT NULL DEFAULT '********',
  `allow_deposit` tinyint NOT NULL DEFAULT '0',
  `allow_withdraw` tinyint NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
INSERT INTO `bank_account_type` (`id`, `name`, `limit_flag`, `allow_deposit`, `allow_withdraw`, `created_at`) VALUES
	(1, 'เฉพาะฝาก', '********', 1, 0, '2023-09-18 12:00:12'),
	(2, 'เฉพาะถอน', '********', 0, 1, '2023-09-18 12:00:12'),
	(3, 'ฝาก-ถอน', '********', 1, 1, '2023-09-18 12:00:12'),
	(4, 'พักเงิน', '********', 0, 0, '2023-09-18 12:00:12');

CREATE TABLE IF NOT EXISTS `bank_account` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `bank_id` bigint NOT NULL,
  `account_type_id` bigint NOT NULL,
  `account_name` varchar(255) NOT NULL,
  `account_number` varchar(255) NOT NULL,
  `account_balance` decimal(14,2) NOT NULL DEFAULT '0.00',
  `account_priority_withdraw` bigint DEFAULT NULL,
  `device_uid` varchar(255) NOT NULL,
  `pin_code` varchar(255) NOT NULL,
  `external_id` bigint DEFAULT NULL,
  `connection_status_id` bigint DEFAULT NULL,
  `last_conn_update_at` datetime DEFAULT NULL,
  `admin_updated_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni_account_number` (`account_number`),
  KEY `connection_status_id` (`connection_status_id`),
  KEY `idx_account_type_id` (`account_type_id`),
  KEY `idx_bank_id` (`bank_id`),
  KEY `idx_external_id` (`external_id`),
  CONSTRAINT `bank_account_ibfk_1` FOREIGN KEY (`connection_status_id`) REFERENCES `connection_status` (`id`),
  CONSTRAINT `bank_account_ibfk_3` FOREIGN KEY (`account_type_id`) REFERENCES `bank_account_type` (`id`)
);

CREATE TABLE IF NOT EXISTS `bank_confirm_statement` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `statement_id` bigint NOT NULL,
  `action_type` varchar(255) NOT NULL,
  `user_id` bigint DEFAULT NULL,
  `account_id` bigint NOT NULL,
  `json_before` text NOT NULL,
  `confirmed_at` datetime DEFAULT NULL,
  `confirmed_by_admin_id` bigint DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni_statement_id` (`statement_id`),
  KEY `idx_account_id` (`account_id`)
);

CREATE TABLE IF NOT EXISTS `bank_statement` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `account_id` bigint NOT NULL,
  `external_id` bigint DEFAULT NULL,
  `amount` decimal(14,2) NOT NULL,
  `detail` varchar(255) NOT NULL,
  `from_bank_id` bigint DEFAULT NULL,
  `from_account_number` varchar(255) DEFAULT NULL,
  `statement_type_id` bigint NOT NULL DEFAULT '0',
  `transfer_at` datetime NOT NULL,
  `statement_status_id` bigint NOT NULL DEFAULT '0',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni_external_id` (`external_id`),
  KEY `idx_account_id` (`account_id`),
  KEY `statement_status_id` (`statement_status_id`),
  KEY `statement_type_id` (`statement_type_id`),
  CONSTRAINT `bank_statement_ibfk_1` FOREIGN KEY (`statement_type_id`) REFERENCES `statement_type` (`id`),
  CONSTRAINT `bank_statement_ibfk_2` FOREIGN KEY (`statement_status_id`) REFERENCES `statement_status` (`id`)
);

CREATE TABLE IF NOT EXISTS `bank_transaction_confirm` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `action_key` varchar(255) NOT NULL,
  `transaction_id` bigint NOT NULL,
  `user_id` bigint NOT NULL,
  `transaction_type_id` bigint NOT NULL,
  `from_account_id` bigint DEFAULT NULL,
  `to_account_id` bigint DEFAULT NULL,
  `json_before` text,
  `transfer_at` datetime DEFAULT NULL,
  `slip_url` varchar(255) DEFAULT NULL,
  `credit_amount` decimal(14,2) DEFAULT NULL,
  `bank_charge_amount` decimal(14,2) NOT NULL DEFAULT '0.00',
  `bonus_amount` decimal(14,2) NOT NULL DEFAULT '0.00',
  `confirmed_at` datetime DEFAULT NULL,
  `confirmed_by_admin_id` bigint DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni_action_key` (`action_key`),
  UNIQUE KEY `uni_transaction_id` (`transaction_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `transaction_type_id` (`transaction_type_id`),
  CONSTRAINT `bank_transaction_confirm_ibfk_1` FOREIGN KEY (`transaction_type_id`) REFERENCES `transaction_type` (`id`)
);

CREATE TABLE IF NOT EXISTS `bank_transaction_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `json_request` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `json_payload` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `log_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
);

CREATE TABLE IF NOT EXISTS `bank_transaction_slip` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `raw_qr_code` varchar(255) DEFAULT NULL,
  `statement_id` bigint DEFAULT NULL,
  `slip_url` text,
  `amount` decimal(14,2) NOT NULL DEFAULT '0.00',
  `status_id` bigint NOT NULL,
  `type` enum('DEPOSIT','WITHDRAW') NOT NULL DEFAULT 'DEPOSIT',
  `user_id` bigint NOT NULL,
  `bank_id` bigint DEFAULT NULL,
  `bank_account_id` bigint DEFAULT NULL,
  `deposited_at` datetime DEFAULT NULL,
  `confirmed_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `raw_qr_code` (`raw_qr_code`),
  KEY `fk_bank_account_idx` (`bank_account_id`),
  KEY `fk_bank_id_idx` (`bank_id`),
  KEY `idx_status_id` (`status_id`),
  KEY `idx_type` (`type`),
  KEY `idx_user_id` (`user_id`),
  CONSTRAINT `fk_bank_account` FOREIGN KEY (`bank_account_id`) REFERENCES `bank_account` (`id`),
  CONSTRAINT `fk_bank_id` FOREIGN KEY (`bank_id`) REFERENCES `bank` (`id`),
  CONSTRAINT `fk_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_user_transaction_slip_cybergame.transaction_status` FOREIGN KEY (`status_id`) REFERENCES `transaction_status` (`id`)
);

CREATE TABLE IF NOT EXISTS `bank_transaction` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `member_code` varchar(255) DEFAULT NULL,
  `user_id` bigint DEFAULT NULL,
  `statement_id` bigint DEFAULT NULL,
  `transaction_type_id` bigint NOT NULL,
  `promotion_id` bigint DEFAULT NULL,
  `from_account_id` bigint DEFAULT NULL,
  `from_bank_id` bigint DEFAULT NULL,
  `from_account_name` varchar(255) DEFAULT NULL,
  `from_account_number` varchar(255) DEFAULT NULL,
  `to_account_id` bigint DEFAULT NULL,
  `to_bank_id` bigint DEFAULT NULL,
  `to_account_name` varchar(255) DEFAULT NULL,
  `to_account_number` varchar(255) DEFAULT NULL,
  `credit_amount` decimal(14,2) NOT NULL DEFAULT '0.00',
  `credit_back` decimal(14,2) NOT NULL DEFAULT '0.00',
  `over_amount` decimal(14,2) NOT NULL DEFAULT '0.00',
  `deposit_channel` varchar(255) DEFAULT NULL,
  `bonus_amount` decimal(14,2) NOT NULL DEFAULT '0.00',
  `bonus_reason` varchar(255) DEFAULT NULL,
  `before_amount` decimal(14,2) NOT NULL DEFAULT '0.00',
  `after_amount` decimal(14,2) NOT NULL DEFAULT '0.00',
  `transfer_at` datetime DEFAULT NULL,
  `created_by_admin_id` bigint NOT NULL,
  `cancel_remark` varchar(255) DEFAULT NULL,
  `canceled_at` datetime DEFAULT NULL,
  `canceled_by_admin_id` bigint DEFAULT NULL,
  `confirmed_at` datetime DEFAULT NULL,
  `confirmed_by_admin_id` bigint DEFAULT NULL,
  `transaction_status_id` bigint NOT NULL,
  `is_first_deposit` tinyint NOT NULL DEFAULT '0',
  `is_auto_credit` tinyint NOT NULL DEFAULT '0',
  `auto_process_timer` varchar(8) NOT NULL DEFAULT '0',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_user_transaction_cybergame.transaction_status_4` (`transaction_status_id`),
  KEY `idx_from_account_id` (`from_account_id`),
  KEY `idx_to_account_id` (`to_account_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `transaction_type_id` (`transaction_type_id`),
  CONSTRAINT `bank_transaction_ibfk_1` FOREIGN KEY (`transaction_type_id`) REFERENCES `transaction_type` (`id`),
  CONSTRAINT `bank_transaction_ibfk_6` FOREIGN KEY (`transaction_status_id`) REFERENCES `transaction_status` (`id`),
  CONSTRAINT `FK_user_transaction_cybergame.transaction_status` FOREIGN KEY (`transaction_status_id`) REFERENCES `transaction_status` (`id`),
  CONSTRAINT `FK_user_transaction_cybergame.transaction_status_2` FOREIGN KEY (`transaction_status_id`) REFERENCES `transaction_status` (`id`),
  CONSTRAINT `FK_user_transaction_cybergame.transaction_status_3` FOREIGN KEY (`transaction_status_id`) REFERENCES `transaction_status` (`id`),
  CONSTRAINT `FK_user_transaction_cybergame.transaction_status_4` FOREIGN KEY (`transaction_status_id`) REFERENCES `transaction_status` (`id`)
);

CREATE TABLE IF NOT EXISTS `bank` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `code` varchar(255) NOT NULL,
  `icon_url` varchar(255) NOT NULL,
  `type_flag` varchar(8) NOT NULL DEFAULT '********',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni_code` (`code`)
);
INSERT INTO `bank` (`id`, `name`, `code`, `icon_url`, `type_flag`, `created_at`) VALUES
	(1, 'กสิกรไทย', 'kbank', 'https://storage.googleapis.com/cbgame/banks/kbank.png', '********', '2023-09-18 12:00:11'),
	(2, 'ไทยพาณิชย์', 'scb', 'https://storage.googleapis.com/cbgame/banks/scb.png', '********', '2023-09-18 12:00:11'),
	(3, 'กรุงเทพ', 'bbl', 'https://storage.googleapis.com/cbgame/banks/bbl.png', '********', '2023-09-18 12:00:11'),
	(4, 'กรุงศรี', 'bay', 'https://storage.googleapis.com/cbgame/banks/bay.png', '********', '2023-09-18 12:00:11'),
	(5, 'กรุงไทย', 'ktb', 'https://storage.googleapis.com/cbgame/banks/ktb.png', '********', '2023-09-18 12:00:11'),
	(6, 'ทีเอ็มบีธนชาต', 'ttb', 'https://storage.googleapis.com/cbgame/banks/ttb.png', '********', '2023-09-18 12:00:11'),
	(7, 'ออมสิน', 'gsb', 'https://storage.googleapis.com/cbgame/banks/gsb.png', '********', '2023-09-18 12:00:11'),
	(8, 'ธกส', 'baac', 'https://storage.googleapis.com/cbgame/banks/baac.png', '********', '2023-09-18 12:00:11'),
	(9, 'เกียรตินาคิน', 'kk', 'https://storage.googleapis.com/cbgame/banks/kk.png', '********', '2023-09-18 12:00:11'),
	(10, 'อาคารสงเคราะห์', 'ghb', 'https://storage.googleapis.com/cbgame/banks/ghb.png', '********', '2023-09-18 12:00:11'),
	(11, 'ยูโอบี', 'uob', 'https://storage.googleapis.com/cbgame/banks/uob.png', '********', '2023-09-18 12:00:11'),
	(12, 'แลนด์ แอนด์ เฮ้าส์', 'lh', 'https://storage.googleapis.com/cbgame/banks/lh.png', '********', '2023-09-18 12:00:11'),
	(13, 'ซีไอเอ็มบี', 'cimb', 'https://storage.googleapis.com/cbgame/banks/cimb.png', '********', '2023-09-18 12:00:11'),
	(14, 'เอชเอสบีซี', 'hsbc', 'https://storage.googleapis.com/cbgame/banks/hsbc.png', '********', '2023-09-18 12:00:11'),
	(15, 'ไอซีบีซี', 'icbc', 'https://storage.googleapis.com/cbgame/banks/icbc.png', '********', '2023-09-18 12:00:11'),
	(16, 'ธนาคารอิสลาม', 'isbt', 'https://storage.googleapis.com/cbgame/banks/isbt.png', '********', '2023-10-30 07:25:21'),
	(17, 'ทิสโก้', 'tisco', 'https://storage.googleapis.com/cbgame/banks/tisco.png', '********', '2023-10-30 07:25:21'),
	(18, 'ซิตี้แบงก์', 'citi', 'https://storage.googleapis.com/cbgame/banks/citi.png', '********', '2023-10-30 07:25:21'),
	(19, 'สแตนดาร์ดชาร์เตอร์ด', 'scbt', 'https://storage.googleapis.com/cbgame/banks/scbt.png', '********', '2023-10-30 07:25:21'),
	(20, 'TrueMoney Wallet', 'true', 'https://storage.googleapis.com/cbgame/banks/true.png', '********', '2023-10-30 07:25:21');

CREATE TABLE IF NOT EXISTS `botaccount_config` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `config_key` varchar(255) NOT NULL,
  `config_val` varchar(255) NOT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_config_key` (`config_key`)
);

INSERT INTO `botaccount_config` (`id`, `config_key`, `config_val`, `deleted_at`) VALUES
	(1, 'allow_create_external_account', 'all', NULL),
	(2, 'allow_create_external_account', 'not_list', NULL),
	(3, 'allow_external_account_number', 'set to list and set account number ', NULL),
	(4, 'allow_withdraw_from_account', '_all', NULL),
	(5, 'withdraw_max_per_time', '50000', NULL),
	(6, 'withdraw_bankaccount_limit', '200000', NULL);

CREATE TABLE IF NOT EXISTS `botaccount_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `external_id` bigint NOT NULL,
  `client_name` varchar(255) NOT NULL,
  `log_type` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `external_create_date` varchar(255) NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_external_id` (`external_id`)
);

CREATE TABLE IF NOT EXISTS `botaccount_statement` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `external_id` bigint NOT NULL,
  `bank_account_id` bigint NOT NULL,
  `bank_code` varchar(255) NOT NULL,
  `amount` decimal(14,2) NOT NULL,
  `date_time` datetime NOT NULL,
  `raw_date_time` datetime NOT NULL,
  `info` varchar(255) NOT NULL,
  `channel_code` varchar(255) NOT NULL,
  `channel_description` varchar(255) NOT NULL,
  `txn_code` varchar(255) NOT NULL,
  `txn_description` varchar(255) NOT NULL,
  `checksum` varchar(255) NOT NULL,
  `is_read` tinyint(1) NOT NULL,
  `external_create_date` varchar(255) NOT NULL,
  `extermal_update_date` varchar(255) NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_bank_account_id` (`bank_account_id`),
  KEY `idx_external_id` (`external_id`)
);

CREATE TABLE IF NOT EXISTS `commission_transfer` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `detail` varchar(255) DEFAULT NULL,
  `commission` decimal(14,2) NOT NULL DEFAULT '0.00',
  `status` tinyint DEFAULT '0',
  `user_id` bigint NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `commission_transfer_user_id_fk` (`user_id`),
  CONSTRAINT `commission_transfer_user_id_fk` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
);

CREATE TABLE IF NOT EXISTS `configuration_notification_type` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `label_th` varchar(255) DEFAULT NULL,
  `label_en` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
);
INSERT INTO `configuration_notification_type` (`id`, `name`, `label_th`, `label_en`, `created_at`, `updated_at`, `deleted_at`) VALUES
	(1, 'ON_ACTIVE', 'เปิดใช้งาน', 'on active', '2023-09-18 12:00:15', NULL, NULL),
	(2, 'CLOSE_WEB', 'ปิดใช้งานหน้าเว็บ', 'close web', '2023-09-18 12:00:15', NULL, NULL),
	(3, 'OFF_ACTIVE', 'ปิดใช้งาน', 'off active', '2023-09-18 12:00:15', NULL, NULL);

CREATE TABLE IF NOT EXISTS `configuration_notification` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `credit_above` bigint DEFAULT '0',
  `line_token` varchar(255) DEFAULT NULL,
  `is_member_registration` tinyint(1) NOT NULL DEFAULT '0',
  `is_deposit_before_credit` tinyint(1) NOT NULL DEFAULT '0',
  `is_deposit_after_credit` tinyint(1) NOT NULL DEFAULT '0',
  `is_withdrawal_credit_success` tinyint(1) NOT NULL DEFAULT '0',
  `is_withdrawal_awaiting_transfer` tinyint(1) NOT NULL DEFAULT '0',
  `is_withdrawal_credit_failed` tinyint(1) NOT NULL DEFAULT '0',
  `configuration_notification_type_id` bigint DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `configuration_notification_type_id` (`configuration_notification_type_id`),
  CONSTRAINT `configuration_notification_ibfk_1` FOREIGN KEY (`configuration_notification_type_id`) REFERENCES `configuration_notification_type` (`id`)
);
INSERT INTO `configuration_notification` (`id`, `credit_above`, `line_token`, `is_member_registration`, `is_deposit_before_credit`, `is_deposit_after_credit`, `is_withdrawal_credit_success`, `is_withdrawal_awaiting_transfer`, `is_withdrawal_credit_failed`, `configuration_notification_type_id`, `created_at`, `updated_at`, `deleted_at`) VALUES
	(1, 1, '', 1, 1, 1, 1, 1, 1, 1, '2023-09-18 12:00:15', '2023-10-31 01:40:28', NULL);

CREATE TABLE IF NOT EXISTS `configuration_web` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `logo_url` varchar(255) DEFAULT '255',
  `web_name` varchar(50) DEFAULT NULL,
  `background_color` char(7) DEFAULT '#ffffff',
  `auto_user_approve_type_id` bigint DEFAULT NULL,
  `use_otp_register` tinyint(1) NOT NULL DEFAULT '0',
  `auto_withdraw_type_id` bigint DEFAULT NULL,
  `turn_withdraw_type_id` bigint DEFAULT NULL,
  `allow_online_registration` tinyint(1) NOT NULL DEFAULT '0',
  `minimum_deposit` bigint DEFAULT '0',
  `minimum_withdraw` bigint DEFAULT '0',
  `maximum_withdraw` bigint DEFAULT '0',
  `id_line` varchar(255) DEFAULT NULL,
  `url_line` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_auto_user_approve_type_id` (`auto_user_approve_type_id`),
  KEY `idx_auto_withdraw_type_id` (`auto_withdraw_type_id`),
  KEY `idx_turn_withdraw_type_id` (`turn_withdraw_type_id`),
  CONSTRAINT `configuration_web_ibfk_1` FOREIGN KEY (`auto_user_approve_type_id`) REFERENCES `auto_user_approve_type` (`id`),
  CONSTRAINT `configuration_web_ibfk_2` FOREIGN KEY (`auto_withdraw_type_id`) REFERENCES `auto_withdraw_type` (`id`),
  CONSTRAINT `configuration_web_ibfk_3` FOREIGN KEY (`turn_withdraw_type_id`) REFERENCES `turn_withdraw_type` (`id`)
);
INSERT INTO `configuration_web` (`id`, `logo_url`, `web_name`, `background_color`, `auto_user_approve_type_id`, `use_otp_register`, `auto_withdraw_type_id`, `turn_withdraw_type_id`, `allow_online_registration`, `minimum_deposit`, `minimum_withdraw`, `maximum_withdraw`, `id_line`, `url_line`, `created_at`, `updated_at`, `deleted_at`) VALUES
	(1, '', 'name', '#1a9900', 2, 0, 1, 1, 1, 1, 1, 50000, '', '', '2023-10-30 13:13:10', '2023-11-10 03:41:45', NULL);

CREATE TABLE IF NOT EXISTS `connection_status` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` enum('CONNECTED','DISCONNECTED') DEFAULT NULL,
  `label_th` varchar(255) DEFAULT NULL,
  `label_en` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
);
INSERT INTO `connection_status` (`id`, `name`, `label_th`, `label_en`, `created_at`, `updated_at`, `deleted_at`) VALUES
	(1, 'CONNECTED', 'เปิดใช้งาน', 'Active', '2023-09-18 12:00:13', '2023-10-04 10:20:58', NULL),
	(2, 'DISCONNECTED', 'ไม่ได้เชื่อมต่อ', 'disconnected', '2023-09-18 12:00:13', NULL, NULL);

CREATE TABLE IF NOT EXISTS `cron_action` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `action_key` varchar(255) DEFAULT NULL,
  `status` enum('PENDING','SUCCESS','FAILED') NOT NULL DEFAULT 'PENDING',
  `name` varchar(255) DEFAULT NULL,
  `remark` text,
  `unlock_at` datetime NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni_action_key` (`action_key`)
);

CREATE TABLE IF NOT EXISTS `issue_report` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `description` text,
  `issue_status_id` bigint DEFAULT NULL,
  `created_by_name` varchar(255) DEFAULT NULL,
  `approved_by_name` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `issue_status_id` (`issue_status_id`),
  CONSTRAINT `issue_report_ibfk_1` FOREIGN KEY (`issue_status_id`) REFERENCES `issue_status` (`id`),
  CONSTRAINT `issue_report_ibfk_2` FOREIGN KEY (`issue_status_id`) REFERENCES `issue_status` (`id`)
);

CREATE TABLE IF NOT EXISTS `issue_status` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` enum('RECEIVED','INQUEUE','PROCESSING','FIXED') NOT NULL DEFAULT 'RECEIVED',
  `label_th` varchar(255) DEFAULT NULL,
  `label_en` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
);

INSERT INTO `issue_status` (`id`, `name`, `label_th`, `label_en`) VALUES
	(1, 'RECEIVED', 'รับเรื่องแล้ว', 'received'),
	(2, 'INQUEUE', 'รอคิวแก้ไข', 'inqueue'),
	(3, 'PROCESSING', 'กำลังแก้ไข', 'processing'),
	(4, 'FIXED', 'เสร็จสิ้น', 'fixed');

CREATE TABLE IF NOT EXISTS `issue_web_report` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `issue_report_id` bigint DEFAULT NULL,
  `issue_web_url_id` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `issue_report_id` (`issue_report_id`),
  KEY `issue_web_url_id` (`issue_web_url_id`),
  CONSTRAINT `issue_web_report_ibfk_1` FOREIGN KEY (`issue_report_id`) REFERENCES `issue_report` (`id`),
  CONSTRAINT `issue_web_report_ibfk_2` FOREIGN KEY (`issue_web_url_id`) REFERENCES `issue_web_url` (`id`)
);

CREATE TABLE IF NOT EXISTS `issue_web_url` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `url` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
);
INSERT INTO `issue_web_url` (`url`) VALUES ('this web site');

CREATE TABLE IF NOT EXISTS `migrate_temp_mem_partner` (
  `mpart_id` bigint NOT NULL,
  `mirate_status_id` bigint DEFAULT '0',
  `site_id` bigint DEFAULT NULL,
  `mpart_user_id_mk` bigint DEFAULT NULL,
  `mpart_mem_id` bigint DEFAULT NULL,
  `mpart_mem_code` varchar(255) DEFAULT NULL,
  `mpart_codename` varchar(255) DEFAULT NULL,
  `mpart_com_sport` decimal(14,2) DEFAULT NULL,
  `mpart_com_casino` decimal(14,2) DEFAULT NULL,
  `mpart_com_game` decimal(14,2) DEFAULT NULL,
  `mpart_agent` int DEFAULT NULL,
  `mpart_sport` int DEFAULT NULL,
  `mpart_casino` int DEFAULT NULL,
  `mpart_game` int DEFAULT NULL,
  `mpart_bonus_free` int DEFAULT NULL,
  `mpart_bonus` int DEFAULT NULL,
  `mpart_tran_day` int DEFAULT NULL,
  `mpart_create` datetime DEFAULT NULL,
  `mpart_update` datetime DEFAULT NULL,
  PRIMARY KEY (`mpart_id`)
);

CREATE TABLE IF NOT EXISTS `migrate_temp_user` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `mirate_status_id` bigint DEFAULT '0',
  `member_code` varchar(255) DEFAULT NULL,
  `ref` varchar(20) DEFAULT NULL,
  `ref_by` bigint DEFAULT NULL,
  `username` varchar(255) DEFAULT NULL,
  `password` varchar(255) DEFAULT NULL,
  `phone` varchar(255) NOT NULL,
  `user_status_id` bigint DEFAULT '1',
  `user_type_id` bigint DEFAULT '1',
  `firstname` varchar(255) DEFAULT NULL,
  `lastname` varchar(255) DEFAULT NULL,
  `fullname` varchar(255) DEFAULT NULL,
  `credit` decimal(14,2) DEFAULT '0.00',
  `ip` varchar(20) DEFAULT NULL,
  `bank_id` int DEFAULT NULL,
  `bank_account` varchar(15) DEFAULT NULL,
  `channel_id` int DEFAULT NULL,
  `true_wallet` varchar(20) DEFAULT NULL,
  `contact` varchar(255) DEFAULT NULL,
  `note` varchar(255) DEFAULT NULL,
  `course` varchar(50) DEFAULT NULL,
  `line_id` varchar(30) DEFAULT NULL,
  `encrypt` varchar(255) DEFAULT NULL,
  `ip_registered` varchar(20) DEFAULT NULL,
  `verified_at` datetime DEFAULT NULL,
  `created_by` bigint DEFAULT NULL,
  `is_reset_password` tinyint DEFAULT '0',
  `logedin_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
);

CREATE TABLE IF NOT EXISTS `migration` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `version` bigint DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`)
);

CREATE TABLE IF NOT EXISTS `permission` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `permission_key` varchar(255) DEFAULT NULL,
  `main` tinyint DEFAULT '0',
  `name` varchar(255) DEFAULT NULL,
  `position` int DEFAULT NULL,
  `created_at` datetime DEFAULT (now()),
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni_permission_key` (`permission_key`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_deleted_at` (`deleted_at`),
  KEY `idx_position` (`position`)
);

INSERT INTO `permission` (`id`, `permission_key`, `main`, `name`, `position`, `created_at`, `updated_at`, `deleted_at`) VALUES
	(1, 'guide', 0, 'คู่มือการใช้งาน', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(2, 'admin', 1, 'ผู้ใช้งาน', NULL, '2023-09-18 12:00:10', '2023-10-17 07:30:51', NULL),
	(3, 'admin_manager', 0, 'จัดการผู้ใช้งาน', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(4, 'admin_group', 0, 'กลุ่มผู้ใช้งาน', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(5, 'summary', 1, 'สรุปภาพรวม', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(6, 'agent', 1, 'จัดการเว็บเอเย่น', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(7, 'agent_list', 0, 'รายการเว็บเอเย่น', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(8, 'agent_credit', 0, 'รายงานเพิ่ม-ลด เครดิต', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(9, 'bank', 1, 'จัดการธนาคาร', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(10, 'bank_list', 0, 'รายการธนาคาร', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(11, 'bank_transaction', 0, 'รายงานธุรกรรมเงินสด', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(12, 'bank_account', 0, 'รายการเดินบัญชีธนาคาร', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(13, 'promotion', 1, 'จัดการโปรโมชั่น', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(14, 'marketing_manage', 1, 'จัดการการตลาด', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(15, 'marketing_manage_link', 0, 'รายการลิ้งรับทรัพย์', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(16, 'marketing_manage_partner', 0, 'รายการพันธมิตร', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(17, 'activity_manage', 1, 'จัดการกิจกรรม', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(18, 'activity_manage_return', 0, 'คืนยอดเสีย', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(19, 'activity_manage_lucky', 0, 'กงล้อนำโชค', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(20, 'activity_manage_checkin', 0, 'เช็คอินรายวัน', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(21, 'activity_manage_coupon', 0, 'คูปองเงินสด', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(22, 'member', 1, 'จัดการสมาชิกเว็บ', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(23, 'member_list', 0, 'รายการสมาชิกเว็บ', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(24, 'member_transaction', 0, 'ประวัติฝาก-ถอนสมาชิก', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(25, 'member_channel', 0, 'ตั้งค่าช่องทางที่รู้จัก', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(26, 'member_history', 0, 'ประวัติการแก้ไขข้อมูล', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(27, 'member_misconduct', 0, 'รายการมิจฉาชีพ', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(28, 'report', 1, 'รายงาน', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(29, 'report_member', 0, 'ยอดสมาชิกผู้ใช้งาน', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(30, 'report_deposit', 0, 'ยอดฝาก-ถอน', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(31, 'report_deposittime', 0, 'จำนวนฝาก-ถอนตามเวลา', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(32, 'report_bonus', 0, 'รายงานการแจกโบนัส', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(33, 'report_membertime', 0, 'จำนวนสมาชิกนับเวลาบันทึก', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(34, 'report_memberchannel', 0, 'ยอดสมาชิกตามช่องทางที่รู้จัก', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(35, 'report_memberuser', 0, 'จำนวนบันทึกรายการตามผู้ใช้งาน', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(36, 'marketing_report', 1, 'รายงานการตลาด', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(37, 'marketing_report_return', 0, 'คืนยอดเสีย', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(38, 'marketing_report_link', 0, 'ลิงค์รับทรัพย์', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(39, 'marketing_report_partner', 0, 'พันธมิตร', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(40, 'winlose_report', 1, 'รายงานข้อมูล แพ้-ชนะ', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(41, 'activity_report', 1, 'รายงานกิจกรรม', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(42, 'activity_report_lucky', 0, 'กงล้อนำโชค', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(43, 'deposit_list', 1, 'รายการฝาก', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(44, 'withdraw_list', 1, 'รายการถอน', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(45, 'setting', 1, 'ตั้งค่าระบบ', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(46, 'setting_basic', 0, 'ข้อมูลเบื้องต้น', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(47, 'setting_line', 0, 'แจ้งเตือนกลุ่ม line', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(48, 'setting_sms', 0, 'PushMessage line', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(49, 'setting_cybernoti', 0, 'แจ้งเตือน Cyber Notify', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(50, 'status_update', 1, 'สถานะเรื่องแจ้งแก้ไข', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(51, 'invoice_notice', 1, 'ใบแจ้งหนี้', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(52, 'bank_transfermoney', 0, 'โยกเงิน', NULL, '2023-10-08 08:04:29', NULL, NULL),
	(53, 'admin_history', 0, 'ประวัติการเข้าสู่ระบบผู้ใช้งาน', NULL, '2023-10-17 07:30:51', NULL, NULL),
	(54, 'promotion_list', 0, 'รายการโปรโมชั่น', NULL, '2023-10-17 07:30:51', NULL, NULL),
	(55, 'promotion_member', 0, 'โปรโมชั่นลูกค้า', NULL, '2023-10-17 07:30:51', NULL, NULL),
	(56, 'transfer_pending', 1, 'รายการโอนรอดำเนินการ', NULL, '2023-10-17 07:30:51', NULL, NULL),
	(57, 'deposit_withdrawal', 1, 'รายการฝาก-ถอน เสร็จสิ้น', NULL, '2023-10-17 07:30:51', NULL, NULL),
	(58, 'marketing_report_winlose', 0, 'รายงานข้อมูลแพ้ ชนะ', NULL, '2023-10-17 07:30:51', NULL, NULL),
	(59, 'marketing_report_wallet', 0, 'โยกเงินเข้ากระเป๋าหลัก', NULL, '2023-10-17 07:30:51', NULL, NULL),
	(60, 'marketing_report_balance', 0, 'ตรวจสอบยอดลิ้งค์แนะนำและพันธมิตร', NULL, '2023-10-17 07:30:51', NULL, NULL);

CREATE TABLE IF NOT EXISTS `play_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `player` varchar(255) DEFAULT '',
  `turn_sport` decimal(14,2) NOT NULL DEFAULT '0.00',
  `turn_casino` decimal(14,2) NOT NULL DEFAULT '0.00',
  `turn_game` decimal(14,2) NOT NULL DEFAULT '0.00',
  `win_lose_sport` decimal(14,2) NOT NULL DEFAULT '0.00',
  `win_lose_casino` decimal(14,2) NOT NULL DEFAULT '0.00',
  `win_lose_game` decimal(14,2) NOT NULL DEFAULT '0.00',
  `turn_total` decimal(14,2) DEFAULT '0.00',
  `win_lose_total` decimal(14,2) DEFAULT '0.00',
  `user_id` bigint NOT NULL,
  `date` varchar(10) DEFAULT '',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_date` (`date`)
);

CREATE TABLE IF NOT EXISTS `promotion_return_cut_type` (
  `id` bigint NOT NULL,
  `name` varchar(255) NOT NULL DEFAULT '',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);

INSERT INTO `promotion_return_cut_type` (`id`, `name`, `created_at`) VALUES
	(1, 'รายวัน', '2023-10-06 08:15:31');

CREATE TABLE IF NOT EXISTS `promotion_return_loser_status` (
  `id` bigint NOT NULL,
  `name` varchar(255) NOT NULL DEFAULT '',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);

INSERT INTO `promotion_return_loser_status` (`id`, `name`, `created_at`) VALUES
	(1, 'PENDING', '2023-10-09 06:52:18'),
	(2, 'READY', '2023-10-09 06:52:18'),
	(3, 'TAKEN', '2023-10-09 06:52:18'),
	(4, 'EXPIRED', '2023-10-09 06:52:18');

CREATE TABLE IF NOT EXISTS `promotion_return_loser_type` (
  `id` bigint NOT NULL,
  `name` varchar(255) NOT NULL DEFAULT '',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);

INSERT INTO `promotion_return_loser_type` (`id`, `name`, `created_at`) VALUES
	(1, 'คืนยอดเสียเมื่อยอดเสียเกิน', '2023-10-06 08:15:30');

CREATE TABLE IF NOT EXISTS `promotion_return_loser` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `status_id` bigint NOT NULL DEFAULT '1',
  `daily_key` varchar(255) NOT NULL DEFAULT '',
  `of_date` date NOT NULL,
  `total_loss_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `return_percent` decimal(10,2) NOT NULL DEFAULT '0.00',
  `return_type_id` bigint NOT NULL DEFAULT '1',
  `cut_type_id` bigint NOT NULL DEFAULT '1',
  `min_loss_price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `max_return_price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `credit_expire_days` int NOT NULL DEFAULT '0',
  `return_price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `calc_at` datetime DEFAULT NULL,
  `take_at` datetime DEFAULT NULL,
  `taken_price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `promotion_return_loser_daily_key_index` (`daily_key`),
  KEY `promotion_return_loser_status_id_index` (`status_id`),
  KEY `promotion_return_loser_user_id_index` (`user_id`)
);

CREATE TABLE IF NOT EXISTS `promotion_return_setting` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `return_percent` decimal(10,2) NOT NULL DEFAULT '0.00',
  `return_type_id` bigint NOT NULL DEFAULT '1',
  `cut_type_id` bigint NOT NULL DEFAULT '1',
  `min_loss_price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `max_return_price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `credit_expire_days` int NOT NULL DEFAULT '0',
  `detail` text NOT NULL,
  `is_enabled` tinyint NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);

INSERT INTO `promotion_return_setting` (`id`, `return_percent`, `return_type_id`, `cut_type_id`, `min_loss_price`, `max_return_price`, `credit_expire_days`, `detail`, `is_enabled`, `created_at`, `updated_at`) VALUES
	(1, 5.00, 1, 1, 500.00, 1000.00, 0, '<ol><li><span style="color: rgb(255, 255, 255); background-color: rgb(0, 0, 0);">* ลูกค้าต้องมียอดเสีย 500 บาท/วันขึ้นไป ถึงจะได้รับยอดเสีย 5% สูงสุด 1000 บาทต่อวัน</span></li><li><span style="color: rgb(255, 255, 255); background-color: rgb(0, 0, 0);">* ยอดเสียจะคิดตั้งแต่ 11.00 น.วันนี้ ถึง 11.00 น.ของพรุ่งนี้ค่ะ</span></li><li><span style="color: rgb(255, 255, 255); background-color: rgb(0, 0, 0);">* ระบบจะคืนยอดเสียให้อัตโนมัติ หลังจาก 14.30 น. ของทุกวัน&nbsp;</span></li></ol>', 0, '2023-10-08 03:52:49', '2023-10-31 07:01:45');

CREATE TABLE IF NOT EXISTS `promotion_web_bonus_type` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` enum('PERCENT','FIXED RATE') NOT NULL DEFAULT 'PERCENT',
  `label_th` varchar(255) DEFAULT NULL,
  `label_en` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
);

INSERT INTO `promotion_web_bonus_type` (`id`, `name`, `label_th`, `label_en`) VALUES
	(1, 'PERCENT', 'เปอร์เซ็นต์', 'percent'),
	(2, 'FIXED RATE', 'จำนวนเงิน', 'fixed rate');

CREATE TABLE IF NOT EXISTS `promotion_web_condition_type` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` enum('DAILY','WEEKLY','NOT_DEPOSITED_X_DAYS','DEPOSITED_X_TIMES','NEVER_DEPOSITED','MONTHLY','EVERY_X_DAYS','X_TIMES','DEPOSIT','IP_ADDRESS_X_TIMES','TURNOVER_FIXED','TURNOVER_RATE') NOT NULL DEFAULT 'DAILY',
  `transaction_type_id` bigint DEFAULT NULL,
  `label_th` varchar(255) DEFAULT NULL,
  `label_en` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `transaction_type_id` (`transaction_type_id`),
  CONSTRAINT `promotion_web_condition_type_ibfk_1` FOREIGN KEY (`transaction_type_id`) REFERENCES `transaction_type` (`id`)
);

INSERT INTO `promotion_web_condition_type` (`id`, `name`, `transaction_type_id`, `label_th`, `label_en`) VALUES
	(1, 'DAILY', 1, 'รายวัน', 'daily'),
	(2, 'WEEKLY', 1, 'รายสัปดาห์', 'weekly'),
	(3, 'NOT_DEPOSITED_X_DAYS', 1, 'ไม่ฝากเงินใน X วัน', 'not deposited x days'),
	(4, 'DEPOSITED_X_TIMES', 1, 'ฝากเงิน X ครั้ง', 'deposited x times'),
	(5, 'NEVER_DEPOSITED', 1, 'ไม่เคยฝากเงิน', 'never deposited'),
	(6, 'MONTHLY', 1, 'รายเดือน', 'monthly'),
	(7, 'EVERY_X_DAYS', 1, 'ทุก X วัน', 'every x days'),
	(8, 'X_TIMES', 1, 'X ครั้ง', 'x times'),
	(9, 'DEPOSIT', 1, 'ฝากเงิน', 'deposit'),
	(10, 'IP_ADDRESS_X_TIMES', 1, 'IP Address ซ้ำ X ครั้ง', 'ip address x times'),
	(11, 'TURNOVER_FIXED', 2, 'เทิร์นเท่ากับ X บาท', 'turnover fixed'),
	(12, 'TURNOVER_RATE', 2, 'เทิร์น X เท่า', 'turnover rate');

CREATE TABLE IF NOT EXISTS `promotion_web_condition` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `promotion_web_id` bigint NOT NULL,
  `transaction_type_id` bigint NOT NULL,
  `promotion_web_condition_type_id` bigint NOT NULL,
  `condition_amount` decimal(10,2) NOT NULL,
  `reset_at` datetime NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `promotion_web_condition_type_id` (`promotion_web_condition_type_id`),
  KEY `promotion_web_id` (`promotion_web_id`),
  KEY `transaction_type_id` (`transaction_type_id`),
  CONSTRAINT `promotion_web_condition_ibfk_1` FOREIGN KEY (`promotion_web_id`) REFERENCES `promotion_web` (`id`),
  CONSTRAINT `promotion_web_condition_ibfk_2` FOREIGN KEY (`transaction_type_id`) REFERENCES `transaction_type` (`id`),
  CONSTRAINT `promotion_web_condition_ibfk_3` FOREIGN KEY (`promotion_web_condition_type_id`) REFERENCES `promotion_web_condition_type` (`id`)
);

CREATE TABLE IF NOT EXISTS `promotion_web_type` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` enum('DEPOSIT','CUSTOMIZED') NOT NULL DEFAULT 'DEPOSIT',
  `label_th` varchar(255) DEFAULT NULL,
  `label_en` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
);

INSERT INTO `promotion_web_type` (`id`, `name`, `label_th`, `label_en`) VALUES
	(1, 'DEPOSIT', 'ฝากเงิน', 'deposit'),
	(2, 'CUSTOMIZED', 'กำหนดเอง', 'customized');

CREATE TABLE IF NOT EXISTS `promotion_web` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `promotion_web_type_id` bigint NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text,
  `start_date` datetime NOT NULL,
  `end_date` datetime NOT NULL,
  `image_url` varchar(255) DEFAULT NULL,
  `promotion_web_bonus_type_id` bigint NOT NULL,
  `bonus_amount` decimal(10,2) NOT NULL,
  `show_banner` int DEFAULT '0',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `promotion_web_bonus_type_id` (`promotion_web_bonus_type_id`),
  KEY `promotion_web_type_id` (`promotion_web_type_id`),
  CONSTRAINT `promotion_web_ibfk_1` FOREIGN KEY (`promotion_web_type_id`) REFERENCES `promotion_web_type` (`id`),
  CONSTRAINT `promotion_web_ibfk_2` FOREIGN KEY (`promotion_web_bonus_type_id`) REFERENCES `promotion_web_bonus_type` (`id`)
);

CREATE TABLE IF NOT EXISTS `recommend_channel` (
  `id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(255) DEFAULT '255',
  `status` enum('ACTIVE','DEACTIVE') DEFAULT 'ACTIVE',
  `url` varchar(255) DEFAULT '255',
  `created_at` datetime DEFAULT (now()),
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);

INSERT INTO `recommend_channel` (`id`, `title`, `status`, `url`, `created_at`, `updated_at`) VALUES
	(1, 'ยูทูบ', 'ACTIVE', 'youtbe.com', '2023-09-15 03:50:18', '2023-10-05 04:37:19'),
	(2, 'เฟส', 'DEACTIVE', 'facebook.com', '2023-09-15 04:05:12', '2023-10-05 04:17:28'),
	(3, 'ไลน์', 'ACTIVE', 'line.me', '2023-09-15 04:05:31', '2023-10-05 04:37:24'),
	(7, 'FaceBook', 'ACTIVE', 'www.facebook.com', '2023-10-05 04:16:14', '2023-10-31 03:56:30');

CREATE TABLE IF NOT EXISTS `register_bonus_option` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` enum('CREDIT','DEPOSIT_PERCENT') NOT NULL,
  `label_en` varchar(255) DEFAULT NULL,
  `label_th` varchar(255) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);

INSERT INTO `register_bonus_option` (`id`, `name`, `label_en`, `label_th`, `created_at`) VALUES
	(3, 'CREDIT', 'Credit', 'เครดิต', '2023-10-08 10:11:24'),
	(4, 'DEPOSIT_PERCENT', 'Percentage of the first deposit amount', 'เปอร์เซ็นยอดฝากครั้งแรก', '2023-10-08 10:11:24');

CREATE TABLE IF NOT EXISTS `register_bonus_type` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` enum('FIRST_DEPOSIT') NOT NULL,
  `label_en` varchar(255) DEFAULT NULL,
  `label_th` varchar(255) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);

INSERT INTO `register_bonus_type` (`id`, `name`, `label_en`, `label_th`, `created_at`) VALUES
	(1, 'FIRST_DEPOSIT', 'First Deposit', 'ฝากครั้งแรก', '2023-10-08 10:05:15');

CREATE TABLE IF NOT EXISTS `scammer` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `reason` varchar(255) DEFAULT NULL,
  `user_id` bigint DEFAULT NULL,
  `scammer_created_by` bigint DEFAULT NULL,
  `scammer_updated_by` bigint DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `scammer_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
);

CREATE TABLE IF NOT EXISTS `statement_status` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` enum('IGNORED','PENDING','CONFIRMED') DEFAULT NULL,
  `label_th` varchar(255) DEFAULT NULL,
  `label_en` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
);
INSERT INTO `statement_status` (`id`, `name`, `label_th`, `label_en`, `created_at`, `updated_at`, `deleted_at`) VALUES
	(1, 'PENDING', 'รอตรวจสอบ', 'pending', '2023-09-19 03:33:10', '2023-09-19 03:37:18', NULL),
	(2, 'CONFIRMED', 'ตรวจสอบแล้ว', 'confirmed', '2023-09-19 03:33:10', '2023-09-19 03:38:13', NULL),
	(3, 'IGNORED', 'ไม่สนใจ', 'ignored', '2023-09-19 03:33:10', '2023-09-19 03:38:30', NULL);

CREATE TABLE IF NOT EXISTS `statement_type` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` enum('TRANSFER_IN','TRANSFER_OUT') DEFAULT NULL,
  `label_th` varchar(255) DEFAULT NULL,
  `label_en` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
);
INSERT INTO `statement_type` (`id`, `name`, `label_th`, `label_en`, `created_at`, `updated_at`, `deleted_at`) VALUES
	(1, 'TRANSFER_IN', 'โอนเข้า', 'transfer_in', '2023-09-19 02:39:23', NULL, NULL),
	(2, 'TRANSFER_OUT', 'โอนออก', 'transfer_out', '2023-09-19 02:39:23', NULL, NULL);

CREATE TABLE IF NOT EXISTS `transaction_status` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` enum('PENDING','DEPOSIT_PENDING_CREDIT','DEPOSIT_PENDING_SLIP','DEPOSIT_PENDING_MULTIUSER','DEPOSIT_CREDIT_APPROVED_GAME','DEPOSIT_CREDIT_REJECTED_GAME','WITHDRAW_PENDING','WITHDRAW_OVER_BUDGET','WITHDRAW_APPROVED','WITHDRAW_REJECTED','WITHDRAW_FAILED','WITHDRAW_SUCCESS','WITHDRAW_OVER_MAX','WITHDRAW_CANCELED','DEPOSIT_IGNORE') NOT NULL DEFAULT 'PENDING',
  `label_th` varchar(255) DEFAULT NULL,
  `label_en` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
);
INSERT INTO `transaction_status` (`id`, `name`, `label_th`, `label_en`, `created_at`, `updated_at`, `deleted_at`) VALUES
	(1, 'PENDING', 'รอตรวจสอบและดำเนินการ', 'pending', '2023-10-10 04:11:48', NULL, NULL),
	(2, 'DEPOSIT_PENDING_CREDIT', 'รออนุมัติเครดิต', 'pending credit', '2023-10-10 04:11:48', NULL, NULL),
	(3, 'DEPOSIT_PENDING_SLIP', 'รายการแจ้งฝาก', 'pending slip', '2023-10-10 04:11:48', NULL, NULL),
	(4, 'DEPOSIT_PENDING_MULTIUSER', 'เลขบัญชีซ้ำ', 'pending multiuser', '2023-10-10 04:11:48', NULL, NULL),
	(5, 'DEPOSIT_CREDIT_APPROVED_GAME', 'ฝากสำเร็จ', 'credit approved game', '2023-10-10 04:11:48', NULL, NULL),
	(6, 'DEPOSIT_CREDIT_REJECTED_GAME', 'ฝากไม่สำเร็จ', 'credit rejected game', '2023-10-10 04:11:48', NULL, NULL),
	(7, 'WITHDRAW_PENDING', 'รอตรวจสอบและดำเนินการ', 'transaction withdraw pending', '2023-10-10 04:11:48', NULL, NULL),
	(8, 'WITHDRAW_OVER_BUDGET', 'แจ้งถอนวงเงินสูง', 'trans status withdraw over budget', '2023-10-10 04:11:48', NULL, NULL),
	(9, 'WITHDRAW_APPROVED', 'ยืนยันการแจ้งถอน', 'withdraw approved', '2023-10-10 04:11:48', NULL, NULL),
	(10, 'WITHDRAW_REJECTED', 'แจ้งถอนไม่สำเร็จ', 'withdraw rejected', '2023-10-10 04:11:48', NULL, NULL),
	(11, 'WITHDRAW_FAILED', 'ถอนจากระบบไม่สำเร็จ', 'withdraw failed', '2023-10-10 04:11:48', NULL, NULL),
	(12, 'WITHDRAW_SUCCESS', 'ถอนสำเร็จ', 'withdraw success', '2023-10-10 04:11:48', NULL, NULL),
	(13, 'WITHDRAW_OVER_MAX', 'ถอนถอนวงเงินสูง', 'withdraw over max', '2023-10-10 04:11:48', NULL, NULL),
	(14, 'WITHDRAW_CANCELED', 'ยกเลิกรายการถอน', 'withdraw canceled', '2023-10-10 04:11:48', NULL, NULL),
	(15, 'DEPOSIT_IGNORE', 'เพิกเฉย', 'deposit ignore', '2023-10-10 04:11:48', NULL, NULL);

CREATE TABLE IF NOT EXISTS `transaction_type` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `label_th` varchar(255) DEFAULT NULL,
  `label_en` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
);
INSERT INTO `transaction_type` (`id`, `name`, `label_th`, `label_en`, `created_at`, `updated_at`, `deleted_at`) VALUES
	(1, 'DEPOSIT', 'ฝาก', 'deposit', '2023-09-18 12:00:13', NULL, NULL),
	(2, 'WITHDRAW', 'ถอน', 'withdraw', '2023-09-18 12:00:13', NULL, NULL),
	(3, 'BONUS', 'โบนัส', 'bonus', '2023-09-18 12:00:13', NULL, NULL),
	(4, 'GETCREDITBACK', 'ดึงเครดิตกลับ', 'getcreditback', '2023-09-18 12:00:13', NULL, NULL);

CREATE TABLE IF NOT EXISTS `turn_withdraw_type` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `label_th` varchar(255) DEFAULT NULL,
  `label_en` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
);
INSERT INTO `turn_withdraw_type` (`id`, `name`, `label_th`, `label_en`, `created_at`, `updated_at`, `deleted_at`) VALUES
	(1, 'turn off', 'ปิดการใช้งาน', 'Turn off', '2023-09-18 12:00:15', NULL, NULL),
	(2, 'count 1 turn', 'นับเทิร์น 1 เท่า', 'Check trend, withdraw after count 1 turn', '2023-09-18 12:00:15', NULL, NULL);

CREATE TABLE IF NOT EXISTS `turnover_statement_status` (
  `id` bigint NOT NULL,
  `name` varchar(255) NOT NULL DEFAULT '',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
INSERT INTO `turnover_statement_status` (`id`, `name`, `created_at`) VALUES
	(1, 'PENDING', '2023-10-09 08:52:45'),
	(2, 'CANCELED', '2023-10-09 08:52:45'),
	(3, 'COMPLETED', '2023-10-09 08:52:45'),
	(4, 'EXPIRED', '2023-10-09 08:52:45');

CREATE TABLE IF NOT EXISTS `turnover_statement_type` (
  `id` bigint NOT NULL,
  `name` varchar(255) NOT NULL DEFAULT '',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
INSERT INTO `turnover_statement_type` (`id`, `name`, `created_at`) VALUES
	(1, 'PROMOTION_FIRST_DEPOSIT', '2023-10-09 08:52:44'),
	(2, 'PROMOTION_RETURN_LOSS', '2023-10-09 08:52:44');

CREATE TABLE IF NOT EXISTS `turnover_statement` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `type_id` bigint NOT NULL DEFAULT '1',
  `ref_type_id` bigint DEFAULT NULL,
  `promotion_name` varchar(255) NOT NULL,
  `bonus_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `status_id` bigint NOT NULL DEFAULT '1',
  `start_turn_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `start_turn_at` datetime DEFAULT NULL,
  `total_turn_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `end_turn_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `turnover_statement_status_id_index` (`status_id`),
  KEY `turnover_statement_user_id_index` (`user_id`)
);

CREATE TABLE IF NOT EXISTS `turnover_withdraw_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `log_key` varchar(255) NOT NULL DEFAULT '',
  `total_withdraw_price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `current_turn` decimal(10,2) NOT NULL DEFAULT '0.00',
  `play_total` decimal(10,2) NOT NULL DEFAULT '0.00',
  `last_play_y` decimal(10,2) NOT NULL DEFAULT '0.00',
  `last_total_x` decimal(10,2) NOT NULL DEFAULT '0.00',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_turnover_withdraw_log_log_key` (`log_key`),
  KEY `turnover_withdraw_log_user_id_index` (`user_id`)
);

CREATE TABLE IF NOT EXISTS `user_affiliate` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `commission_total` decimal(14,2) DEFAULT '0.00',
  `commission_current` decimal(14,2) DEFAULT '0.00',
  `first_deposit_bonus` decimal(14,2) DEFAULT '0.00',
  `bonus_share_total` decimal(14,2) DEFAULT '0.00',
  `commission_sport` decimal(14,2) DEFAULT '0.00',
  `commission_casino` decimal(14,2) DEFAULT '0.00',
  `commission_game` decimal(14,2) DEFAULT '0.00',
  `link_click_total` int DEFAULT '0',
  `member_total` int DEFAULT '0',
  `member_deposit_total` int DEFAULT '0',
  `play_balance` decimal(14,2) DEFAULT '0.00',
  `received_balance` decimal(14,2) DEFAULT '0.00',
  `user_id` bigint NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_affiliate_user_id_fk` (`user_id`),
  CONSTRAINT `user_affiliate_user_id_fk` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
);

CREATE TABLE IF NOT EXISTS `user_alliance` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `alias` varchar(255) NOT NULL DEFAULT '',
  `first_deposit` int NOT NULL DEFAULT '0',
  `link_click_total` int NOT NULL DEFAULT '0',
  `member_register_today` int NOT NULL DEFAULT '0',
  `member_online_today` int DEFAULT '0',
  `no_member_code_total` int NOT NULL DEFAULT '0',
  `have_member_code_total` int NOT NULL DEFAULT '0',
  `recommend_total` int NOT NULL DEFAULT '0',
  `referral_bonus` decimal(14,2) DEFAULT '0.00',
  `description` text,
  `alliance_percent` decimal(14,2) NOT NULL DEFAULT '0.00',
  `commission_percent` decimal(14,2) NOT NULL DEFAULT '0.00',
  `user_id` bigint NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_alliance_user_id_fk` (`user_id`),
  CONSTRAINT `user_alliance_user_id_fk` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
);

CREATE TABLE IF NOT EXISTS `user_login_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint DEFAULT NULL,
  `ip` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT (now()),
  PRIMARY KEY (`id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_user_id` (`user_id`)
);

CREATE TABLE IF NOT EXISTS `user_otp` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `ref` varchar(255) NOT NULL,
  `type` varchar(255) NOT NULL,
  `ants_otp_id` varchar(255) NOT NULL,
  `user_id` bigint DEFAULT NULL,
  `created_at` datetime DEFAULT (now()),
  `verified_at` datetime DEFAULT NULL,
  `expired_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_ants_otp_id` (`ants_otp_id`),
  KEY `idx_ref` (`ref`),
  KEY `idx_type` (`type`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_verified_at` (`verified_at`)
);

CREATE TABLE IF NOT EXISTS `user_status` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` enum('ACTIVE','DEACTIVE') DEFAULT NULL,
  `label_th` varchar(255) DEFAULT NULL,
  `label_en` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
);
INSERT INTO `user_status` (`id`, `name`, `label_th`, `label_en`) VALUES
	(1, 'ACTIVE', 'ผู้ใช้งาน ใช้งาน', 'active'),
	(2, 'DEACTIVE', 'โดนงับการใช้งาน', 'deactive');

CREATE TABLE IF NOT EXISTS `user_transaction_direction` (
  `id` bigint NOT NULL,
  `name` varchar(255) NOT NULL DEFAULT '',
  `detail` varchar(255) NOT NULL DEFAULT '',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
INSERT INTO `user_transaction_direction` (`id`, `name`, `detail`, `created_at`) VALUES
	(1, 'DEPOSIT', 'ฝาก', '2023-10-20 06:27:30'),
	(2, 'WITHDRAW', 'ถอน', '2023-10-20 06:27:30');

CREATE TABLE IF NOT EXISTS `user_transaction_type` (
  `id` bigint NOT NULL,
  `name` varchar(255) NOT NULL DEFAULT '',
  `detail` varchar(255) NOT NULL DEFAULT '',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
INSERT INTO `user_transaction_type` (`id`, `name`, `detail`, `created_at`) VALUES
	(1, 'DEPOSIT', 'ฝาก', '2023-10-19 08:06:44'),
	(2, 'WITHDRAW', 'ถอน', '2023-10-19 08:06:44'),
	(3, 'BONUS', 'โบนัส', '2023-10-19 08:06:44'),
	(4, 'PROMOTION_RETURN_LOSS', 'แจกโบนัสฟรี คืนยอดเสีย', '2023-10-19 08:06:44'),
	(5, 'AFFILIATE_INCOME', 'โบนัสรายได้แนะนำเพื่อน', '2023-10-19 08:06:44'),
	(6, 'ALLIANCE_INCOME', 'โบนัสรายได้พันธมิตร', '2023-10-19 08:06:44'),
	(7, 'TAKE_CREDIT_BACK', 'ดึงเครดิตกลับ', '2023-10-20 09:00:02');

CREATE TABLE IF NOT EXISTS `user_transaction` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `direction_id` bigint NOT NULL DEFAULT '1',
  `promotion_id` bigint DEFAULT NULL,
  `type_id` bigint NOT NULL,
  `account_id` bigint DEFAULT NULL,
  `ref_id` bigint DEFAULT NULL,
  `detail` varchar(255) NOT NULL DEFAULT '',
  `credit_before` decimal(10,2) NOT NULL DEFAULT '0.00',
  `credit_back` decimal(10,2) NOT NULL DEFAULT '0.00',
  `credit_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `bonus_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `credit_after` decimal(10,2) NOT NULL DEFAULT '0.00',
  `transfer_at` datetime DEFAULT NULL,
  `create_admin_id` bigint DEFAULT NULL,
  `confirm_admin_id` bigint DEFAULT NULL,
  `is_adjust_auto` tinyint NOT NULL DEFAULT '0',
  `is_show` tinyint NOT NULL DEFAULT '1',
  `work_seconds` int NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `removed_at` datetime DEFAULT NULL,
  `remove_admin_id` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_credit_transaction_type_id_index` (`type_id`),
  KEY `user_credit_transaction_user_id_index` (`user_id`)
);

CREATE TABLE IF NOT EXISTS `user_type` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` enum('NONE','AFFILIATE','ALLIANCE') DEFAULT NULL,
  `label_th` varchar(255) DEFAULT NULL,
  `label_en` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
);
INSERT INTO `user_type` (`id`, `name`, `label_th`, `label_en`) VALUES
	(1, 'NONE', 'ไม่มี', 'none'),
	(2, 'AFFILIATE', 'ลิงค์รับทรัพย์', 'affiliate'),
	(3, 'ALLIANCE', 'พันธมิตร', 'alliance');

CREATE TABLE IF NOT EXISTS `user_update_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `created_by_username` varchar(255) DEFAULT NULL,
  `ip` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT (now()),
  PRIMARY KEY (`id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_user_id` (`user_id`)
);

CREATE TABLE IF NOT EXISTS `user` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `member_code` varchar(255) DEFAULT NULL,
  `ref_by` bigint DEFAULT NULL,
  `username` varchar(255) DEFAULT NULL,
  `password` varchar(255) DEFAULT NULL,
  `phone` varchar(255) NOT NULL,
  `user_status_id` bigint NOT NULL DEFAULT '1',
  `user_type_id` bigint NOT NULL DEFAULT '1',
  `firstname` varchar(255) DEFAULT NULL,
  `lastname` varchar(255) DEFAULT NULL,
  `fullname` varchar(255) DEFAULT NULL,
  `credit` decimal(14,2) DEFAULT '0.00',
  `ip` varchar(20) DEFAULT NULL,
  `bank_account` varchar(15) DEFAULT NULL,
  `channel_id` int DEFAULT NULL,
  `true_wallet` varchar(20) DEFAULT NULL,
  `contact` varchar(255) DEFAULT NULL,
  `note` varchar(255) DEFAULT NULL,
  `course` varchar(50) DEFAULT NULL,
  `line_id` varchar(30) DEFAULT NULL,
  `encrypt` varchar(255) DEFAULT NULL,
  `ip_registered` varchar(20) DEFAULT NULL,
  `verified_at` datetime DEFAULT NULL,
  `bank_id` int DEFAULT NULL,
  `created_by` int DEFAULT NULL,
  `is_reset_password` tinyint DEFAULT '0',
  `logedin_at` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT (now()),
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_bank_id` (`bank_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_deleted_at` (`deleted_at`),
  KEY `idx_member_code` (`member_code`),
  KEY `idx_phone` (`phone`),
  KEY `idx_ref_by` (`ref_by`),
  KEY `idx_verified_at` (`verified_at`),
  KEY `user_status_id` (`user_status_id`),
  KEY `user_type_id` (`user_type_id`),
  CONSTRAINT `user_ibfk_1` FOREIGN KEY (`user_status_id`) REFERENCES `user_status` (`id`),
  CONSTRAINT `user_ibfk_2` FOREIGN KEY (`user_type_id`) REFERENCES `user_type` (`id`)
);

CREATE TABLE IF NOT EXISTS `webhook_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `json_request` text NOT NULL,
  `json_payload` text NOT NULL,
  `log_type` varchar(255) NOT NULL,
  `status` varchar(255) NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
);

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;

ALTER TABLE `admin` DROP INDEX `uni_email`;

CREATE TABLE affiliate_link_click (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    ref_by BIGINT NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX idx_ref_by ON affiliate_link_click (ref_by);

CREATE TABLE alliance_income_withdraw_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    ref_user_id BIGINT NOT NULL,
    income_amount DECIMAL(14,2) NOT NULL DEFAULT 0.00,
    from_date VARCHAR(11) NOT NULL,
    to_date VARCHAR(11) NOT NULL,
    created_by BIGINT NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX idx_ref_user_id ON alliance_income_withdraw_log (ref_user_id);

ALTER TABLE `user`
	DROP INDEX `idx_phone`,
	ADD UNIQUE INDEX `uni_phone` (`phone`) USING BTREE;

CREATE TABLE user_income_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    type_id BIGINT NOT NULL,
    ref_id BIGINT NULL DEFAULT NULL,
    detail VARCHAR(255) NOT NULL DEFAULT '',
    credit_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
    credit_after DECIMAL(10,2) NOT NULL DEFAULT 0,
    transfer_at DATETIME NULL DEFAULT NULL,
    status_id BIGINT NOT NULL DEFAULT 0,
    create_by_name VARCHAR(255) NOT NULL DEFAULT '',
    confirm_by_name VARCHAR(255) NOT NULL DEFAULT '',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
CREATE INDEX user_income_log_user_id_index ON user_income_log (user_id);
CREATE INDEX user_income_log_type_id_index ON user_income_log (type_id);
CREATE INDEX user_income_log_ref_id_index ON user_income_log (ref_id);
CREATE INDEX user_income_log_status_id_index ON user_income_log (status_id);

CREATE TABLE user_income_log_type (
    id BIGINT PRIMARY KEY,
    name VARCHAR(255) NOT NULL DEFAULT '',
    detail VARCHAR(255) NOT NULL DEFAULT '',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

INSERT INTO `user_income_log_type` (`id`,`name`,`detail`) VALUES 
    (1,'AFFILIATE', 'แนะนำเพื่อน'),
    (2,'PROMOTION_RETURN_LOSS', 'คืนยอดเสีย'),
    (3,'ALLIANCE', 'รายได้พันธมิตร');

CREATE TABLE user_income_log_status (
    id BIGINT PRIMARY KEY,
    name VARCHAR(255) NOT NULL DEFAULT '',
    detail VARCHAR(255) NOT NULL DEFAULT '',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

INSERT INTO `user_income_log_status` (`id`,`name`,`detail`) VALUES 
    (1,'READY', 'รอกดรับ'),
    (2,'COMPLETED', 'สำเร็จ');

ALTER TABLE `user`
	ADD COLUMN `last_action_at` DATETIME NULL DEFAULT NULL AFTER `verified_at`;

UPDATE `user` SET `updated_at`= `created_at` WHERE updated_at IS NULL;
UPDATE `user` SET `last_action_at`= `updated_at` WHERE last_action_at IS NULL;

CREATE INDEX `idx_user_id` ON `play_log` (`user_id`);
ALTER TABLE `play_log`
	ADD COLUMN `valid_amount_sport` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `win_lose_game`,
	ADD COLUMN `valid_amount_casino` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `valid_amount_sport`,
	ADD COLUMN `valid_amount_game` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `valid_amount_casino`,
	ADD COLUMN `valid_amount_total` DECIMAL(14,2) NULL DEFAULT '0.00' AFTER `win_lose_total`;

DELETE FROM `recommend_channel` WHERE `id` < 100;
INSERT INTO `recommend_channel` (`id`, `title`, `status`, `url`) VALUES
	(1, 'ยูทูบ', 'ACTIVE', 'www.youtube.com'),
	(3, 'ไลน์', 'ACTIVE', 'line.me'),
	(7, 'FaceBook', 'ACTIVE', 'www.facebook.com'),
	(8, 'อื่นๆ', 'ACTIVE', 'other'),
	(9, 'Google', 'ACTIVE', 'google'),
	(10, 'เพื่อนแนะนำมา', 'ACTIVE', 'friends'),
	(11, 'พันธมิตร', 'ACTIVE', 'alliance'),
	(12, 'Tiktok', 'ACTIVE', 'tiktok'),
	(13, 'FbLive', 'ACTIVE', 'fblive');

-- ---------------configuration_web-------------------------
ALTER TABLE `configuration_web` DROP FOREIGN KEY `configuration_web_ibfk_2`;
ALTER TABLE `configuration_web` DROP COLUMN `auto_withdraw_type_id`;
ALTER TABLE `configuration_web` DROP COLUMN `minimum_withdraw`;
ALTER TABLE `configuration_web` DROP COLUMN `maximum_withdraw`;

-- --------------auto_withdraw_type_id--------------------
ALTER TABLE `bank_account` ADD COLUMN `auto_withdraw_type_id` BIGINT(19) NOT NULL DEFAULT 1 AFTER `account_priority_withdraw`;
ALTER TABLE `bank_account` ADD FOREIGN KEY (`auto_withdraw_type_id`) REFERENCES `auto_withdraw_type` (`id`);
ALTER TABLE `bank_account` ADD COLUMN `bank_withdraw_maximum` DECIMAL(10,2) NOT NULL DEFAULT 50000.00 AFTER `account_priority_withdraw`;
ALTER TABLE `bank_account` ADD COLUMN `auto_withdraw_maximum` DECIMAL(10,2) NOT NULL DEFAULT 25000.00 AFTER `bank_withdraw_maximum`;

-- line notify change ให้เป็น 1 เเพราะ ปกติ จะเปิดให้ตลอด ไม่สามารถปิดได้
ALTER TABLE `configuration_notification`
ADD COLUMN `is_deposit_bonus` tinyint(1) NOT NULL DEFAULT '1' AFTER `is_withdrawal_credit_failed`;
ALTER TABLE `configuration_notification`
ADD COLUMN `is_pull_credit_back` tinyint(1) NOT NULL DEFAULT '1' AFTER `is_deposit_bonus`;


-- BOF OF V1.2.0 --
CREATE TABLE IF NOT EXISTS `alliance_winlose_income` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `status_id` bigint NOT NULL DEFAULT '1',
  `daily_key` varchar(255) NOT NULL DEFAULT '',
  `statement_date` DATE NOT NULL,
  `of_date` DATE NOT NULL,
  `total_play_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `total_win_lose_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `commission_percent` decimal(10,2) NOT NULL DEFAULT '0.00',
  `alliance_percent` decimal(10,2) NOT NULL DEFAULT '0.00',
  `total_commission` decimal(10,2) NOT NULL DEFAULT '0.00',
  `alliance_winlose_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `alliance_commission` decimal(10,2) NOT NULL DEFAULT '0.00',
  `total_paid_bonus` decimal(10,2) NOT NULL DEFAULT '0.00',
  `alliance_paid_bonus` decimal(10,2) NOT NULL DEFAULT '0.00',
  `alliance_income` decimal(10,2) NOT NULL DEFAULT '0.00',
  `take_at` datetime DEFAULT NULL,
  `taken_price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
CREATE INDEX `idx_user_id` ON `alliance_winlose_income` (`user_id`);
CREATE INDEX `idx_status_id` ON `alliance_winlose_income` (`status_id`);
CREATE UNIQUE INDEX `uni_daily_key` ON `alliance_winlose_income` (`daily_key`);

CREATE TABLE IF NOT EXISTS `marketing_config` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT,
  `config_key` varchar(255) NOT NULL,
  `config_val` varchar(255) NOT NULL,
  `deleted_at` datetime DEFAULT NULL
);

ALTER TABLE `user_income_log`
	ADD COLUMN `create_by` BIGINT(19) NOT NULL DEFAULT '0' AFTER `status_id`,
	ADD COLUMN `confirm_by` BIGINT(19) NULL DEFAULT NULL AFTER `create_by`;

UPDATE `user_income_log_status` SET `name`='PENDING', `detail`='รออนุมัติ' WHERE `id`=1;


UPDATE `configuration_web` SET `web_name`='xxx.net' WHERE  `id`=1;
UPDATE `issue_web_url` SET `url`='xxx.net' WHERE  `id`=1;
UPDATE `agent_info` SET `total`=1000000 WHERE  `id`=1;

-- --- HOT FIX 2023-11-29 ------

ALTER TABLE `bank_account`
	ADD COLUMN `is_show_front` TINYINT NOT NULL DEFAULT 0 AFTER `account_type_id`;

-- --- HOT FIX 2023-11-30 ------

ALTER TABLE `configuration_web` ADD COLUMN `minimum_withdraw` DECIMAL(14,2) NULL DEFAULT '1' AFTER `minimum_deposit`;

CREATE TABLE IF NOT EXISTS `user_playlog_status` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `statement_date` VARCHAR(10) DEFAULT NULL,
  `path` VARCHAR(255) NOT NULL,
  `page` INT DEFAULT '1',
  `status_id` INT DEFAULT '1',
  `out_message` VARCHAR(255),
  `out_total` BIGINT,
  `out_json_error` TEXT,
  `out_json_summary` TEXT,
  `out_target_url` VARCHAR(255),
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
CREATE INDEX `path` ON `user_playlog_status` (`path`);
CREATE INDEX `statement_date` ON `user_playlog_status` (`statement_date`);

CREATE TABLE IF NOT EXISTS `user_playlog` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `user_id` BIGINT NOT NULL,
  `statement_date` VARCHAR(10) DEFAULT NULL,
  `daily_key` VARCHAR(255) DEFAULT NULL,
  `turn_sport` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `valid_amount_sport` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `win_lose_sport` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `turn_casino` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `valid_amount_casino` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `win_lose_casino` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `turn_game` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `valid_amount_game` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `win_lose_game` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `turn_total` DECIMAL(14,2) DEFAULT '0.00',
  `win_lose_total` DECIMAL(14,2) DEFAULT '0.00',
  `valid_amount_total` DECIMAL(14,2) DEFAULT '0.00',
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
CREATE INDEX `user_id` ON `user_playlog` (`user_id`);
CREATE INDEX `statement_date` ON `user_playlog` (`statement_date`);
CREATE UNIQUE INDEX `daily_key` ON `user_playlog` (`daily_key`);

-- --- HOT FIX 2023-12-01 ------

ALTER TABLE `user_otp`
	ADD COLUMN `phone` VARCHAR(50) NOT NULL DEFAULT '' AFTER `id`;
ALTER TABLE `user_otp`
	ADD COLUMN `local_pin` VARCHAR(10) NULL DEFAULT NULL AFTER `user_id`;

INSERT INTO `permission` (`permission_key`, `main`, `name`) VALUES ('member_edit', 1, 'เพิ่ม แก้ไข ข้อมูลสมาชิกเว็บ'); 
INSERT INTO `permission` (`permission_key`, `main`, `name`) VALUES ('member_delete', 1, 'ลบ ข้อมูลสมาชิกเว็บ');
INSERT INTO `permission` (`permission_key`, `main`, `name`) VALUES ('marketing_manage_edit', 1, 'เพิ่ม แก้ไข ลบ ข้อมูลจัดการการตลาด');
INSERT INTO `permission` (`permission_key`, `main`, `name`) VALUES ('scammer_cancel', 1, 'ยกเลิกมิจฉาชีพ');

ALTER TABLE `user`
	CHANGE COLUMN `bank_account` `bank_account` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci' AFTER `ip`;

CREATE TABLE IF NOT EXISTS `renewal_web_master` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT,
  `name` varchar(255) NOT NULL DEFAULT '',
  `web_domain` varchar(255) NOT NULL DEFAULT '',
  `api_key` varchar(255) NOT NULL DEFAULT '',
  `payment_detail` varchar(255) NOT NULL DEFAULT '',
  `current_web_package_id` bigint NULL DEFAULT NULL,
  `web_expired_date` DATE NOT NULL,
  `current_sms_package_id` bigint NULL DEFAULT NULL,
  `sms_credit_balance` bigint NOT NULL DEFAULT 0,
  `sms_expired_date` DATE NOT NULL,
  `current_fastbank_package_id` bigint NULL DEFAULT NULL,
  `fastbank_credit_balance` bigint NOT NULL DEFAULT 0,
  `fastbank_expired_date` DATE NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL
);
CREATE UNIQUE INDEX `uni_web_domain` ON `renewal_web_master` (`web_domain`);

CREATE TABLE IF NOT EXISTS `renewal_web_package` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT,
  `name` varchar(255) NOT NULL DEFAULT '',
  `price_per_month` decimal(10,2) NOT NULL DEFAULT '0.00',
  `renewal_days` INT NOT NULL DEFAULT 0,
  `limit_admin_count` INT NOT NULL DEFAULT 0,
  `limit_user_count` INT NOT NULL DEFAULT 0,
  `limit_transaction_count` INT NOT NULL DEFAULT 0,
  `limit_transaction_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `limit_bank_account_count` INT NOT NULL DEFAULT 0,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
INSERT INTO `renewal_web_package` (`id`, `name`, `price_per_month`, `renewal_days`, `limit_admin_count`, `limit_user_count`, `limit_transaction_count`, `limit_transaction_amount`,  `limit_bank_account_count`) VALUES
(1, 'Website ขนาดเล็ก', 20000.00, 30, 25, 20000, 0, 2500000.00, 3),
(2, 'Company บริษัท', 25000.00, 30, 50, 50000, 0, 6000000.00, 5),
(3, 'Enterprise องค์กรใหญ่', 30000.00, 30, 50, 0, 0, 0.00, 0);

CREATE TABLE IF NOT EXISTS `invoice` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT,
  `web_id` bigint NOT NULL,
  `web_name` varchar(255) NOT NULL DEFAULT '',
  `invoice_no` varchar(255) NOT NULL DEFAULT '',
  `invoice_type_id` bigint NOT NULL DEFAULT '0',
  `package_id` bigint NOT NULL DEFAULT '0',
  `renew_days` int NOT NULL DEFAULT '0',
  `renew_credit_amount` int NOT NULL DEFAULT '0',
  `package_detail` varchar(255) NOT NULL DEFAULT '',
  `invoice_at` DATETIME NULL DEFAULT NULL,
  `expire_at` DATETIME NULL DEFAULT NULL,
  `paid_at` DATETIME NULL DEFAULT NULL,
  `paid_by` bigint NULL DEFAULT NULL,
  `payment_detail` varchar(255) NOT NULL DEFAULT '',
  `confirm_by` bigint NULL DEFAULT NULL,
  `confirm_at` DATETIME NULL DEFAULT NULL,
  `status_id` bigint NOT NULL DEFAULT '1',
  `sum_price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `vat_percent` decimal(10,2) NOT NULL DEFAULT '0.00',
  `vat_price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `total_price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `create_by` bigint NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE `invoice_type` (
    id BIGINT PRIMARY KEY,
    name VARCHAR(255) NOT NULL DEFAULT '',
    detail VARCHAR(255) NOT NULL DEFAULT '',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);
INSERT INTO `invoice_type` (`id`,`name`,`detail`) VALUES 
    (1,'WEB_RENEWAL', 'ต่ออายุ'),
    (2,'FASTBANK_RENEWAL', 'เติมเครดิต'),
    (3,'SMS_RENEWAL', 'สั่งซื้อข้อความ');

CREATE TABLE `invoice_status` (
    id BIGINT PRIMARY KEY,
    name VARCHAR(255) NOT NULL DEFAULT '',
    detail VARCHAR(255) NOT NULL DEFAULT '',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);
INSERT INTO `invoice_status` (`id`,`name`,`detail`) VALUES 
    (1,'WAIT_PAYMENT', 'รอการชำระ'),
    (2,'WAIT_CONFIRM', 'รอตรวจสอบยอด'),
    (3,'COMPLETED', 'ชำระแล้ว');

-- --- WORKING AFTER 2023-12-02 ------

INSERT INTO `invoice_status` (`id`,`name`,`detail`) VALUES 
    (4,'CANCELED', 'ไม่สำเร็จ');
    
UPDATE `user_income_log_type` SET `detail`='แจกโบนัสแนะนำเพื่อน' WHERE `id`=1;
UPDATE `user_income_log_type` SET `detail`='แจกโบนัสคืนยอดเสีย' WHERE  `id`=2;

-- add user_transaction_type 
INSERT INTO `user_transaction_type` (`id`, `name`, `detail`) VALUES
(8,'CREDIT_TYPE_DAILY_ACTIVITY_BONUS','โบนัสกิจกรรมรายวัน');

INSERT INTO `user_transaction_type` (`id`, `name`, `detail`) VALUES
(9,'CREDIT_TPYE_LUCKY_WHEEL','เครดิตจากกิจกรรมกงล้อนำโชค');

INSERT INTO `user_income_log_type` (`id`,`name`,`detail`) VALUES 
  (4,'USER_INCOME_TYPE_LUCKY_WHEEL', 'รายได้จากกิจกรรมกงล้อนำโชค');

-- ------------

CREATE TABLE IF NOT EXISTS `activity_lucky_wheel_setting` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT,
  `condition_id` bigint NULL DEFAULT NULL,
  `lose_per_roll` int NOT NULL,
  `max_roll_per_day` int NOT NULL,
  `cumulative_expired_days` int DEFAULT NULL,
  `is_enabled` tinyint DEFAULT '0',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS `activity_lucky_wheel_condition` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` enum('DEPOSITED','LOSE')NULL DEFAULT NULL,
  `label_th` varchar(255) NULL DEFAULT NULL,
  `label_en` varchar(255) NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
);

INSERT INTO `activity_lucky_wheel_condition` (`id`, `name`, `label_th`, `label_en`) VALUES
	(1, 'DEPOSITED', 'ทุกยอด XX ฝาก ได้หมุน 1 สิทธิ์', 'everyamount can be deposited for 1 right'),
	(2, 'LOSE', 'ทุกยอด XX เสีย ได้หมุน 1 สิทธิ์', 'everyamount can be lose for 1 right');

CREATE TABLE IF NOT EXISTS `activity_lucky_wheel` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT,
  `position` int NOT NULL,
  `message` varchar(255) NOT NULL,
  `minimum_reward` int NOT NULL,
  `hex_background_color` varchar(255) NOT NULL,
  `percent_win` decimal(10,2) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

INSERT INTO `activity_lucky_wheel` (`id`, `position`, `message`,`minimum_reward`, `hex_background_color`, `percent_win`,`created_at`, `updated_at`) VALUES
	(1, 1, '1 บาท', 1, '#0917d7', '99.00', '2023-11-22 19:15:30', '2023-11-22 19:15:30'),
    (2, 2, '10 บาท', 10, '#e000ee', '15.00', '2023-11-22 19:15:30', '2023-11-22 19:15:30'),
    (3, 3, '20 บาท', 20, '#26b5c0', '10.00', '2023-11-22 19:15:30', '2023-11-22 19:15:30'),
    (4, 4, '100 บาท', 100, '#3ac828', '2.00', '2023-11-22 19:15:30', '2023-11-22 19:15:30'),
    (5, 5, '200 บาท', 200, '#dac910', '1.00', '2023-11-22 19:15:30', '2023-11-22 19:15:30'),
    (6, 6, '300 บาท', 300, '#1aa23c', '1.00', '2023-11-22 19:15:30', '2023-11-22 19:15:30'),
    (7, 7, '400 บาท', 400, '#fd0808', '1.00', '2023-11-22 19:15:30', '2023-11-22 19:15:30'),
    (8, 8, '500 บาท', 500, '#ecb1b', '1.00', '2023-11-22 19:15:30', '2023-11-22 19:15:30');

CREATE TABLE IF NOT EXISTS `activity_lucky_wheel_round_user` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT,
  `user_income_id` BIGINT(19) NULL DEFAULT NULL,
  `user_id` bigint NULL DEFAULT NULL,
  `lucky_wheel_id` bigint NULL DEFAULT NULL,
  `reward` decimal(10,2) NULL DEFAULT NULL,
  `condition_id` bigint NULL DEFAULT NULL,
  `condition_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `status_id` bigint DEFAULT 1,
  `received_date` datetime NULL DEFAULT NULL,
  `expired_date` datetime NULL DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS `activity_lucky_wheel_round_status` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` enum('ACTIVE','EXPIRED','PRIZE_CLAIMED','PRIZE_RECEIVED') DEFAULT NULL,
  `label_th` varchar(255) DEFAULT NULL,
  `label_en` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
);

INSERT INTO `activity_lucky_wheel_round_status` (`id`, `name`, `label_th`, `label_en`) VALUES
	(1, 'ACTIVE', 'ใช้งาน', 'active'),
  (2, 'EXPIRED', 'หมดอายุ', 'expired'),
  (3, 'PRIZE_CLAIMED', 'ออกรางวัล', 'prize claimed'),
  (4, 'PRIZE_RECEIVED', 'รับรางวัลแล้ว', 'prize received');

-- EOF LUKEY WHEEL --

CREATE TABLE IF NOT EXISTS `user_affiliate_income` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `user_id` BIGINT NOT NULL,
  `statement_date` VARCHAR(10) DEFAULT NULL,
  `status_id` bigint NOT NULL DEFAULT '1',
  `daily_key` VARCHAR(255) DEFAULT NULL,
  `turn_sport` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `percent_sport` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `commission_sport` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `turn_casino` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `percent_casino` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `commission_casino` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `turn_game` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `percent_game` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `commission_game` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `turn_total` DECIMAL(14,2) DEFAULT '0.00',
  `commission_total` DECIMAL(14,2) DEFAULT '0.00',
  `take_at` datetime DEFAULT NULL,
  `taken_price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
CREATE INDEX `user_id` ON `user_affiliate_income` (`user_id`);
CREATE INDEX `statement_date` ON `user_affiliate_income` (`statement_date`);
CREATE INDEX `status_id` ON `user_affiliate_income` (`status_id`);
CREATE UNIQUE INDEX `daily_key` ON `user_affiliate_income` (`daily_key`);

INSERT INTO activity_lucky_wheel_setting (condition_id, lose_per_roll, max_roll_per_day, cumulative_expired_days, is_enabled) VALUES (1, 1, 3, 10, 0);

-- EOF 2023-12-07 --

ALTER TABLE `renewal_web_master`
	ADD COLUMN `last_remote_update_at` DATETIME NULL DEFAULT NULL AFTER `api_key`;

-- Acticity Week

CREATE TABLE `activity_daily`(
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `monday_bonus` DECIMAL(10,2) DEFAULT NULL,
    `tuesday_bonus` DECIMAL(10,2) DEFAULT NULL,
    `wednesday_bonus` DECIMAL(10,2) DEFAULT NULL,
    `thursday_bonus` DECIMAL(10,2) DEFAULT NULL,
    `friday_bonus` DECIMAL(10,2) DEFAULT NULL,
    `saturday_bonus` DECIMAL(10,2) DEFAULT NULL,
    `sunday_bonus` DECIMAL(10,2) DEFAULT NULL,
    `completed_bonus` DECIMAL(10,2) DEFAULT NULL,
    `activity_daily_condition_id` BIGINT NOT NULL, 
    `activity_daily_status_id` BIGINT NOT NULL,
    `deposit_amount_condition` DECIMAL(10,2) DEFAULT NULL,
    `updated_by_admin_id` BIGINT DEFAULT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
    `deleted_at` DATETIME DEFAULT NULL,
    PRIMARY KEY (`id`)
);

INSERT INTO `activity_daily` (`id`, `monday_bonus`, `tuesday_bonus`, `wednesday_bonus`, `thursday_bonus`, `friday_bonus`, `saturday_bonus`, `sunday_bonus`, `completed_bonus`, `activity_daily_condition_id`, `activity_daily_status_id`, `deposit_amount_condition`, `updated_by_admin_id`) VALUES
 (1, 1.00, 2.00, 3.00, 4.00, 5.00, 6.00, 7.00, 10.00, 1, 2, 10.00, 1);


CREATE TABLE `activity_daily_condition` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `name` ENUM('NONE','MINIMUM_DEPOSIT','DEPOSIT_ACCUMULATED_AMOUNT') NOT NULL DEFAULT 'NONE',
  `label_th` VARCHAR(255) DEFAULT NULL,
  `label_en` VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
);

INSERT INTO `activity_daily_condition` (`id`, `name`, `label_th`, `label_en`) VALUES
(1, 'NONE', 'ไม่มีเงื่อนไข', 'none'),
(2, 'MINIMUM_DEPOSIT', 'ฝากขั้นต่ำ', 'minimum deposit'),
(3, 'DEPOSIT_ACCUMULATED_AMOUNT','ฝากตามยอด', 'deposit accumulated amount');

CREATE TABLE `activity_daily_status` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `name` ENUM('ACTIVE','DEACTIVE') NOT NULL DEFAULT 'ACTIVE',
  `label_th` VARCHAR(255) DEFAULT NULL,
  `label_en` VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
);

INSERT INTO `activity_daily_status` (`id`, `name`, `label_th`, `label_en`) VALUES
(1,'ACTIVE','ใช้งาน','active'),
(2,'DEACTIVE','ยกเลิก','deactive');


ALTER TABLE `activity_daily` ADD FOREIGN KEY (`activity_daily_condition_id`) REFERENCES `activity_daily_condition` (`id`);
ALTER TABLE `activity_daily` ADD FOREIGN KEY (`activity_daily_status_id`) REFERENCES `activity_daily_status` (`id`);



CREATE TABLE `activity_daily_user` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `daily_key` varchar(255) NOT NULL, 
  `user_id` BIGINT NOT NULL,
  `collected_bonus` DECIMAL(10,2) DEFAULT NULL,
  `collected_date` DATETIME DEFAULT NULL,
  `activity_day_id` BIGINT NOT NULL,
  `created_at` DATETIME NOT NULL DEFAULT NOW(),
  `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
  `deleted_at` DATETIME DEFAULT NULL,
  UNIQUE KEY `uni_daily_key` (`daily_key`),
  PRIMARY KEY (`id`)
);

CREATE TABLE `activity_day` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `name` ENUM('MON','TUE','WED','THU','FRI','SAT','SUN','COMPLETE_DAILY') NOT NULL DEFAULT 'MON',
  `label_th` VARCHAR(255) DEFAULT NULL,
  `label_en` VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
);

INSERT INTO `activity_day` (`id`, `name`, `label_th`, `label_en`) VALUES
(1,'MON','จันทร์','monday'),
(2,'TUE','อังคาร','tuesday'),
(3,'WED','พุธ','wednesday'),
(4,'THU','พฤหัสบดี','thursday'),
(5,'FRI','ศุกร์','friday'),
(6,'SAT','เสาร์','saturday'),
(7,'SUN','อาทิตย์','sunday'),
(8,'COMPLETE_DAILY', 'เก็บครบ', 'complete');
ALTER TABLE `activity_daily_user` ADD FOREIGN KEY (`activity_day_id`) REFERENCES `activity_day` (`id`);

-- bank_transaction
ALTER TABLE `bank_transaction`
	ADD COLUMN `external_match_id` BIGINT(19) NULL DEFAULT NULL AFTER `statement_id`;

-- EOF 2023-12-08 --

-- MIGRATE of 2023-12-12 --

ALTER TABLE `invoice`
	ADD COLUMN `confirm_by_name` VARCHAR(255) NULL DEFAULT NULL AFTER `confirm_by`;

UPDATE `invoice_status` SET `name`='REJECTED',`detail`='ไม่อนุมัติ' WHERE  `id`=4;

CREATE TABLE IF NOT EXISTS `renewal_sms_package` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `rate_price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `renewal_credits` int NOT NULL DEFAULT '0',
  `credit_days` int NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
INSERT INTO `renewal_sms_package` (`id`, `name`, `price`, `rate_price`, `renewal_credits`, `credit_days`) VALUES (1, 'แพคเกจ 1,500', 1500, 0.80, 1860, 80);
INSERT INTO `renewal_sms_package` (`id`, `name`, `price`, `rate_price`, `renewal_credits`, `credit_days`) VALUES (2, 'แพคเกจ 5,000', 5000, 0.68, 7353, 280);
INSERT INTO `renewal_sms_package` (`id`, `name`, `price`, `rate_price`, `renewal_credits`, `credit_days`) VALUES (3, 'แพคเกจ 10,000', 10000, 0.58, 17241, 380);
INSERT INTO `renewal_sms_package` (`id`, `name`, `price`, `rate_price`, `renewal_credits`, `credit_days`) VALUES (4, 'แพคเกจ 30,000', 30000, 0.46, 65217, 380);

CREATE TABLE IF NOT EXISTS `renewal_fastbank_package` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `renewal_credits` int NOT NULL DEFAULT '0',
  `credit_days` int NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
INSERT INTO `renewal_fastbank_package` (`id`, `name`, `price`, `renewal_credits`, `credit_days`) VALUES (1, 'แพคเกจ 2,500', 2500, 10000, 45);
INSERT INTO `renewal_fastbank_package` (`id`, `name`, `price`, `renewal_credits`, `credit_days`) VALUES (2, 'แพคเกจ 5,000', 5000, 20000, 60);
INSERT INTO `renewal_fastbank_package` (`id`, `name`, `price`, `renewal_credits`, `credit_days`) VALUES (3, 'แพคเกจ 10,000', 10000, 41000, 90);
INSERT INTO `renewal_fastbank_package` (`id`, `name`, `price`, `renewal_credits`, `credit_days`) VALUES (4, 'แพคเกจ 30,000', 30000, 152000, 350);
INSERT INTO `renewal_fastbank_package` (`id`, `name`, `price`, `renewal_credits`, `credit_days`) VALUES (5, 'แพคเกจ 50,000', 50000, 260000, 350);

-- MASTER
-- ALTER TABLE `cybergame_web`
-- 	ADD COLUMN `last_remote_update_at` DATETIME NULL DEFAULT NULL AFTER `api_key`;

-- INSERT INTO `invoice_status` (`id`, `name`, `detail`) VALUES (4, 'REJECTED', 'ไม่อนุมัติ');
-- DROP TABLE `renewal_fastbank_package`;
-- DROP TABLE `renewal_sms_package`;

-- CREATE TABLE IF NOT EXISTS `renewal_sms_package` (
--   `id` bigint NOT NULL AUTO_INCREMENT,
--   `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
--   `price` decimal(10,2) NOT NULL DEFAULT '0.00',
--   `rate_price` decimal(10,2) NOT NULL DEFAULT '0.00',
--   `renewal_credits` int NOT NULL DEFAULT '0',
--   `credit_days` int NOT NULL DEFAULT '0',
--   `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
--   `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
--   PRIMARY KEY (`id`) USING BTREE
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
-- INSERT INTO `renewal_sms_package` (`id`, `name`, `price`, `rate_price`, `renewal_credits`, `credit_days`) VALUES (1, 'แพคเกจ 1,500', 1500, 0.80, 1860, 80);
-- INSERT INTO `renewal_sms_package` (`id`, `name`, `price`, `rate_price`, `renewal_credits`, `credit_days`) VALUES (2, 'แพคเกจ 5,000', 5000, 0.68, 7353, 280);
-- INSERT INTO `renewal_sms_package` (`id`, `name`, `price`, `rate_price`, `renewal_credits`, `credit_days`) VALUES (3, 'แพคเกจ 10,000', 10000, 0.58, 17241, 380);
-- INSERT INTO `renewal_sms_package` (`id`, `name`, `price`, `rate_price`, `renewal_credits`, `credit_days`) VALUES (4, 'แพคเกจ 30,000', 30000, 0.46, 65217, 380);

-- CREATE TABLE IF NOT EXISTS `renewal_fastbank_package` (
--   `id` bigint NOT NULL AUTO_INCREMENT,
--   `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
--   `price` decimal(10,2) NOT NULL DEFAULT '0.00',
--   `renewal_credits` int NOT NULL DEFAULT '0',
--   `credit_days` int NOT NULL DEFAULT '0',
--   `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
--   `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
--   PRIMARY KEY (`id`) USING BTREE
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
-- INSERT INTO `renewal_fastbank_package` (`id`, `name`, `price`, `renewal_credits`, `credit_days`) VALUES (1, 'แพคเกจ 2,500', 2500, 10000, 45);
-- INSERT INTO `renewal_fastbank_package` (`id`, `name`, `price`, `renewal_credits`, `credit_days`) VALUES (2, 'แพคเกจ 5,000', 5000, 20000, 60);
-- INSERT INTO `renewal_fastbank_package` (`id`, `name`, `price`, `renewal_credits`, `credit_days`) VALUES (3, 'แพคเกจ 10,000', 10000, 41000, 90);
-- INSERT INTO `renewal_fastbank_package` (`id`, `name`, `price`, `renewal_credits`, `credit_days`) VALUES (4, 'แพคเกจ 30,000', 30000, 152000, 350);
-- INSERT INTO `renewal_fastbank_package` (`id`, `name`, `price`, `renewal_credits`, `credit_days`) VALUES (5, 'แพคเกจ 50,000', 50000, 260000, 350);

-- EOF 2023-12-12 --

-- MIGRATE of 2023-12-13 --
ALTER TABLE `user_update_log`
	ADD COLUMN `json_result` LONGTEXT NULL AFTER `ip`;

-- MIGRATE of 2023-12-14 --
ALTER TABLE `invoice`
	ADD COLUMN `slip_image_path` VARCHAR(1000) NOT NULL DEFAULT '' AFTER `payment_detail`;

-- ALTER TABLE master.`invoice`
-- 	ADD COLUMN `slip_image_path` VARCHAR(1000) NOT NULL DEFAULT '' AFTER `payment_detail`;

-- MIGRATE of 2023-12-15 --
ALTER TABLE `renewal_web_master`
	ADD COLUMN `is_front_enabled` TINYINT NOT NULL DEFAULT '1' AFTER `last_remote_update_at`,
	ADD COLUMN `is_back_enabled` TINYINT NOT NULL DEFAULT '1' AFTER `is_front_enabled`,
	ADD COLUMN `maintenance_message` VARCHAR(1000) NOT NULL DEFAULT '' AFTER `is_back_enabled`;

-- MIGRATE of 2023-12-19 --
ALTER TABLE `configuration_web`
	ADD COLUMN `min_first_member_deposit` INT NOT NULL DEFAULT '0' AFTER `minimum_withdraw`;

-- ALTER TABLE master.`invoice`
-- 	ADD COLUMN `discount_price` DECIMAL(10,2) NOT NULL DEFAULT '0.00' AFTER `vat_price`;

-- MIGRATE of 2023-12-21
CREATE TABLE IF NOT EXISTS `activity_lucky_wheel_round` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `user_id` BIGINT NULL DEFAULT NULL,
  `received` INT DEFAULT '0',
  `condition_id` BIGINT NULL DEFAULT NULL,
  `condition_amount` DECIMAL(10,2) NOT NULL DEFAULT '0.00',
  `received_date` DATETIME NULL DEFAULT NULL,
  `expired_date` DATETIME NULL DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

ALTER TABLE `activity_lucky_wheel_round_user`
	ADD COLUMN `condition_description` VARCHAR(255) NULL DEFAULT NULL AFTER `lucky_wheel_id`;

CREATE TABLE IF NOT EXISTS `activity_lucky_wheel_round_confirm` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `action_key` varchar(255) NOT NULL,
  `user_id` BIGINT NOT NULL,
  `lucky_wheel_user_id` BIGINT NOT NULL,
  `condition_amount` DECIMAL(10,2) NOT NULL DEFAULT '0.00',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` DATETIME DEFAULT NULL,
  UNIQUE KEY `uni_action_key` (`action_key`)
);

INSERT INTO `bank` (`id`, `name`, `code`, `icon_url`, `type_flag`) VALUES   (21, 'ธนาคารภายนอก', 'external', 'https://storage.googleapis.com/cbgame/banks/none.png', '********');

ALTER TABLE `activity_lucky_wheel_round_user`
	ADD COLUMN `lucky_wheel_round_id` BIGINT NULL DEFAULT NULL AFTER `id`;

-- MIGRATE of 2023-12-23
ALTER TABLE `activity_lucky_wheel_round_user`
  ADD COLUMN `rotated_date` datetime NULL DEFAULT NULL AFTER `expired_date`;

-- MIGRATE of 2023-12-29 --

CREATE TABLE IF NOT EXISTS `bank_transaction_withdraw_confirm` (
	`id` BIGINT PRIMARY KEY AUTO_INCREMENT,
	`confirm_key` varchar(255) NOT NULL,
	`user_id` BIGINT NOT NULL,
	`bank_transaction_id` BIGINT NULL,
	`created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
	`updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
	`deleted_at` DATETIME DEFAULT NULL,
	UNIQUE KEY `uni_confirm_key` (`confirm_key`)
  );

-- MIGRATE of 2023-12-24 --
ALTER TABLE `invoice`
	ADD COLUMN `discount_price` DECIMAL(10,2) NOT NULL DEFAULT '0.00' AFTER `vat_price`;

-- MIGRATE of 2024-01-04 -

ALTER TABLE `renewal_web_master`
	ADD COLUMN `fastbank_free_start_date` DATE NULL DEFAULT NULL AFTER `fastbank_expired_date`,
	ADD COLUMN `fastbank_free_end_date` DATE NULL DEFAULT NULL AFTER `fastbank_free_start_date`;

CREATE TABLE `agent_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NULL DEFAULT NULL,
  `status` varchar(255) NULL DEFAULT NULL,
  `json_input` text NULL DEFAULT NULL,
  `json_request` text NULL DEFAULT NULL,
  `json_reponse` text NULL DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
);

CREATE TABLE IF NOT EXISTS `web_popup` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT,
  `sort_order` bigint NOT NULL DEFAULT 0,
  `img_path` varchar(255) NOT NULL DEFAULT '',
  `is_show` tinyint NOT NULL DEFAULT 0,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL
);

CREATE TABLE IF NOT EXISTS `race_action` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `action_key` varchar(255) DEFAULT NULL,
  `status` enum('PENDING','SUCCESS','FAILED') NOT NULL DEFAULT 'PENDING',
  `name` varchar(255) DEFAULT NULL,
  `json_request` text,
  `unlock_at` datetime NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni_action_key` (`action_key`)
);

-- เอา package อื่นๆ ออกไปให้หมด
DELETE FROM `renewal_web_package` WHERE `id`=1;
DELETE FROM `renewal_web_package` WHERE `id`=2;

-- MIGRATE of 2024-01-08 -

CREATE TABLE `sys_user_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NULL DEFAULT NULL,
  `status` varchar(255) NULL DEFAULT NULL,
  `json_input` text NULL DEFAULT NULL,
  `json_request` text NULL DEFAULT NULL,
  `json_reponse` text NULL DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
);

-- MIGRATE of 2024-01-12

CREATE TABLE IF NOT EXISTS `user_today_playlog` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `user_id` BIGINT NOT NULL,
  `daily_key` VARCHAR(255) DEFAULT NULL,
  `statement_date` VARCHAR(10) DEFAULT NULL,
  `turn_sport` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `valid_amount_sport` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `win_lose_sport` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `turn_casino` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `valid_amount_casino` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `win_lose_casino` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `turn_game` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `valid_amount_game` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `win_lose_game` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `turn_total` DECIMAL(14,2) DEFAULT '0.00',
  `win_lose_total` DECIMAL(14,2) DEFAULT '0.00',
  `valid_amount_total` DECIMAL(14,2) DEFAULT '0.00',
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
CREATE INDEX `user_id` ON `user_today_playlog` (`user_id`);
CREATE INDEX `statement_date` ON `user_today_playlog` (`statement_date`);
CREATE UNIQUE INDEX `daily_key` ON `user_today_playlog` (`daily_key`);

-- EOF VERSION 1.2.x --