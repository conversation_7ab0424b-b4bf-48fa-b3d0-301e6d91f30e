-- USE `db_4bar88`;
-- USE `db_5playgold`;
-- USE `db_9goldclub`;
-- USE `db_a4u_top`;
-- USE `db_allin98`;
-- USE `db_ambet65`;
-- USE `db_atmplus`;
-- USE `db_auto_game`;
-- USE `db_autoslot168`;
-- USE `db_baza888`;
-- USE `db_bbmslot`;
-- USE `db_brb88bet`;
-- USE `db_brobet`;
-- USE `db_cnone_bet`;
-- USE `db_eleven111`;
-- USE `db_ezrich`;
-- USE `db_fivedragon`;
-- USE `db_gm1_plus`;
-- USE `db_kaya68`;
-- USE `db_luckydream`;
-- USE `db_luxxplay`;
-- USE `db_maga89_net`;
-- USE `db_meechok789`;
-- USE `db_megaclub`;
-- USE `db_mixxway`;
-- USE `db_mr89`;
-- USE `db_onebet24hr`;
-- USE `db_paplern999`;
-- USE `db_pgautoall`;
-- USE `db_rabbitrich`;
-- USE `db_river_club`;
-- USE `db_rogerclub`;
-- USE `db_royal447`;
-- USE `db_s7_luck`;
-- USE `db_setthisiam`;
-- USE `db_showpow777`;
-- USE `db_supmongkol`;
-- USE `db_therich333`;
-- USE `db_thluckyrich`;
-- USE `db_topgame789`;
-- USE `db_topsure168`;
-- USE `db_uca999`;
-- USE `db_uk888club`;
-- USE `db_urwin888`;
-- USE `db_utaslot`;
-- USE `db_winlotto555`;
-- USE `db_warior88`;
-- USE `db_wowgame88`;

SELECT TABLE_SCHEMA, TABLE_NAME, TABLE_COLLATION FROM information_schema.TABLES WHERE TABLE_NAME = 'api_status';
-- db_auto_game
-- db_onebet24hr
-- db_rabbitrich

-- ALTER TABLE db_auto_game.api_status CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
-- ALTER TABLE db_onebet24hr.api_status CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
-- ALTER TABLE db_rabbitrich.api_status CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;

-- CHECK OLD PAYGATE SYSTEM LOG
SELECT 'db_4bar88.paygate_system_log' as name ,COUNT(*) FROM `db_4bar88`.`paygate_system_log` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 3 MONTH)
UNION ALL
SELECT 'db_5playgold.paygate_system_log' as name ,COUNT(*) FROM `db_5playgold`.`paygate_system_log` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 3 MONTH);

-- CLEAN UP OLD PAYGATE SYSTEM LOG
-- DELETE FROM `paygate_system_log` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 3 MONTH);
-- ANALYZE TABLE `paygate_system_log`;
-- SELECT COUNT(*) FROM `paygate_system_log`;

-- CHECK OLD API STATUS LOG
SELECT * FROM `db_4bar88`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_5playgold`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_9goldclub`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_a4u_top`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_allin98`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_ambet65`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_atmplus`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_auto_game`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_autoslot168`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_baza888`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_bbmslot`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_brb88bet`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_brobet`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1  
UNION ALL
SELECT * FROM `db_cnone_bet`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1   
UNION ALL
SELECT * FROM `db_eleven111`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_ezrich`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_fivedragon`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_gm1_plus`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_kaya68`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_luckydream`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_luxxplay`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_maga89_net`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_meechok789`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_megaclub`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_mixxway`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_mr89`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_onebet24hr`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_paplern999`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_pgautoall`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_rabbitrich`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_river_club`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_rogerclub`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_royal447`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_s7_luck`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_setthisiam`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_showpow777`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_supmongkol`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_therich333`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_thluckyrich`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_topgame789`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_topsure168`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_uca999`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_uk888club`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_urwin888`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_utaslot`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_winlotto555`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_warior88`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1
UNION ALL
SELECT * FROM `db_wowgame88`.`api_status` WHERE statement_date = '2025-06-08' AND is_success != 1;


-- CLEAN UP OLD API STATUS LOG
-- DELETE FROM `api_status` WHERE is_success = 1 AND `created_at` < DATE_SUB(NOW(), INTERVAL 3 MONTH);
-- SELECT * FROM `api_status` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 3 MONTH);

SELECT 'db_4bar88' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_4bar88`.`turnover_setting`
UNION ALL
SELECT 'db_5playgold' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_5playgold`.`turnover_setting`
UNION ALL
SELECT 'db_9goldclub' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_9goldclub`.`turnover_setting`
UNION ALL
SELECT 'db_a4u_top' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_a4u_top`.`turnover_setting`
UNION ALL
SELECT 'db_allin98' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_allin98`.`turnover_setting`
UNION ALL
SELECT 'db_ambet65' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_ambet65`.`turnover_setting`
UNION ALL
SELECT 'db_atmplus' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_atmplus`.`turnover_setting`
UNION ALL
SELECT 'db_auto_game' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_auto_game`.`turnover_setting`
UNION ALL
SELECT 'db_autoslot168' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_autoslot168`.`turnover_setting`
UNION ALL
SELECT 'db_baza888' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_baza888`.`turnover_setting`
UNION ALL
SELECT 'db_bbmslot' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_bbmslot`.`turnover_setting`
UNION ALL
SELECT 'db_brb88bet' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_brb88bet`.`turnover_setting`
UNION ALL
SELECT 'db_brobet' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_brobet`.`turnover_setting`
UNION ALL
SELECT 'db_cnone_bet' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_cnone_bet`.`turnover_setting`
UNION ALL
SELECT 'db_eleven111' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_eleven111`.`turnover_setting`
UNION ALL
SELECT 'db_ezrich' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_ezrich`.`turnover_setting`
UNION ALL
SELECT 'db_fivedragon' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_fivedragon`.`turnover_setting`
UNION ALL
SELECT 'db_gm1_plus' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_gm1_plus`.`turnover_setting`
UNION ALL
SELECT 'db_kaya68' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_kaya68`.`turnover_setting`
UNION ALL
SELECT 'db_luckydream' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_luckydream`.`turnover_setting`
UNION ALL
SELECT 'db_luxxplay' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_luxxplay`.`turnover_setting`
UNION ALL
SELECT 'db_maga89_net' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_maga89_net`.`turnover_setting`
UNION ALL
SELECT 'db_meechok789' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_meechok789`.`turnover_setting`
UNION ALL
SELECT 'db_megaclub' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_megaclub`.`turnover_setting`
UNION ALL
SELECT 'db_mixxway' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_mixxway`.`turnover_setting`
UNION ALL
SELECT 'db_mr89' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_mr89`.`turnover_setting`
UNION ALL
SELECT 'db_onebet24hr' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_onebet24hr`.`turnover_setting`
UNION ALL
SELECT 'db_paplern999' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_paplern999`.`turnover_setting`
UNION ALL
SELECT 'db_pgautoall' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_pgautoall`.`turnover_setting`
UNION ALL
SELECT 'db_rabbitrich' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_rabbitrich`.`turnover_setting`
UNION ALL
SELECT 'db_river_club' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_river_club`.`turnover_setting`
UNION ALL
SELECT 'db_rogerclub' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_rogerclub`.`turnover_setting`
UNION ALL
SELECT 'db_royal447' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_royal447`.`turnover_setting`
UNION ALL
SELECT 'db_s7_luck' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_s7_luck`.`turnover_setting`
UNION ALL
SELECT 'db_setthisiam' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_setthisiam`.`turnover_setting`
UNION ALL
SELECT 'db_showpow777' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_showpow777`.`turnover_setting`
UNION ALL
SELECT 'db_supmongkol' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_supmongkol`.`turnover_setting`
UNION ALL
SELECT 'db_therich333' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_therich333`.`turnover_setting`
UNION ALL
SELECT 'db_thluckyrich' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_thluckyrich`.`turnover_setting`
UNION ALL
SELECT 'db_topgame789' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_topgame789`.`turnover_setting`
UNION ALL
SELECT 'db_topsure168' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_topsure168`.`turnover_setting`
UNION ALL
SELECT 'db_uca999' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_uca999`.`turnover_setting`
UNION ALL
SELECT 'db_uk888club' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_uk888club`.`turnover_setting`
UNION ALL
SELECT 'db_urwin888' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_urwin888`.`turnover_setting`
UNION ALL
SELECT 'db_utaslot' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_utaslot`.`turnover_setting`
UNION ALL
SELECT 'db_winlotto555' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_winlotto555`.`turnover_setting`
UNION ALL
SELECT 'db_warior88' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_warior88`.`turnover_setting`
UNION ALL
SELECT 'db_wowgame88' as name, tidturn_return_loss_percent, tidturn_return_turn_percent FROM `db_wowgame88`.`turnover_setting`;


-- SELECT * FROM `renewal_fastbank_package` LIMIT 1000;
SELECT 'db_4bar88' AS web, name, renewal_credits, updated_at FROM `db_4bar88`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_5playgold' AS web, name, renewal_credits, updated_at FROM `db_5playgold`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_9goldclub' AS web, name, renewal_credits, updated_at FROM `db_9goldclub`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_a4u_top' AS web, name, renewal_credits, updated_at FROM `db_a4u_top`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_allin98' AS web, name, renewal_credits, updated_at FROM `db_allin98`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_ambet65' AS web, name, renewal_credits, updated_at FROM `db_ambet65`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_atmplus' AS web, name, renewal_credits, updated_at FROM `db_atmplus`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_auto_game' AS web, name, renewal_credits, updated_at FROM `db_auto_game`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_autoslot168' AS web, name, renewal_credits, updated_at FROM `db_autoslot168`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_baza888' AS web, name, renewal_credits, updated_at FROM `db_baza888`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_bbmslot' AS web, name, renewal_credits, updated_at FROM `db_bbmslot`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_brb88bet' AS web, name, renewal_credits, updated_at FROM `db_brb88bet`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_brobet' AS web, name, renewal_credits, updated_at FROM `db_brobet`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_cnone_bet' AS web, name, renewal_credits, updated_at FROM `db_cnone_bet`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_eleven111' AS web, name, renewal_credits, updated_at FROM `db_eleven111`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_ezrich' AS web, name, renewal_credits, updated_at FROM `db_ezrich`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_fivedragon' AS web, name, renewal_credits, updated_at FROM `db_fivedragon`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_gm1_plus' AS web, name, renewal_credits, updated_at FROM `db_gm1_plus`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_kaya68' AS web, name, renewal_credits, updated_at FROM `db_kaya68`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_luckydream' AS web, name, renewal_credits, updated_at FROM `db_luckydream`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_luxxplay' AS web, name, renewal_credits, updated_at FROM `db_luxxplay`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_maga89_net' AS web, name, renewal_credits, updated_at FROM `db_maga89_net`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_meechok789' AS web, name, renewal_credits, updated_at FROM `db_meechok789`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_megaclub' AS web, name, renewal_credits, updated_at FROM `db_megaclub`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_mixxway' AS web, name, renewal_credits, updated_at FROM `db_mixxway`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_mr89' AS web, name, renewal_credits, updated_at FROM `db_mr89`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_onebet24hr' AS web, name, renewal_credits, updated_at FROM `db_onebet24hr`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_paplern999' AS web, name, renewal_credits, updated_at FROM `db_paplern999`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_pgautoall' AS web, name, renewal_credits, updated_at FROM `db_pgautoall`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_rabbitrich' AS web, name, renewal_credits, updated_at FROM `db_rabbitrich`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_river_club' AS web, name, renewal_credits, updated_at FROM `db_river_club`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_rogerclub' AS web, name, renewal_credits, updated_at FROM `db_rogerclub`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_royal447' AS web, name, renewal_credits, updated_at FROM `db_royal447`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_s7_luck' AS web, name, renewal_credits, updated_at FROM `db_s7_luck`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_setthisiam' AS web, name, renewal_credits, updated_at FROM `db_setthisiam`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_showpow777' AS web, name, renewal_credits, updated_at FROM `db_showpow777`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_supmongkol' AS web, name, renewal_credits, updated_at FROM `db_supmongkol`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_therich333' AS web, name, renewal_credits, updated_at FROM `db_therich333`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_thluckyrich' AS web, name, renewal_credits, updated_at FROM `db_thluckyrich`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_topgame789' AS web, name, renewal_credits, updated_at FROM `db_topgame789`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_topsure168' AS web, name, renewal_credits, updated_at FROM `db_topsure168`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_uca999' AS web, name, renewal_credits, updated_at FROM `db_uca999`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_uk888club' AS web, name, renewal_credits, updated_at FROM `db_uk888club`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_urwin888' AS web, name, renewal_credits, updated_at FROM `db_urwin888`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_utaslot' AS web, name, renewal_credits, updated_at FROM `db_utaslot`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_winlotto555' AS web, name, renewal_credits, updated_at FROM `db_winlotto555`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_warior88' AS web, name, renewal_credits, updated_at FROM `db_warior88`.`renewal_fastbank_package`
UNION ALL
SELECT 'db_wowgame88' AS web, name, renewal_credits, updated_at FROM `db_wowgame88`.`renewal_fastbank_package`;
