DROP TABLE `promotion_web_condition_type`;
DROP TABLE `promotion_web_condition`;
DROP TABLE `promotion_web_bonus_type`;
DROP TABLE `promotion_web`;
DROP TABLE `promotion_return_setting`;
DROP TABLE `scammer`;
DROP TABLE `register_bonus_type`;
DROP TABLE `register_bonus_option`;
DROP TABLE `recommend_channel`;
DROP TABLE `promotion_web_type`;
DROP TABLE `promotion_return_loser_type`;
DROP TABLE `migration`;
DROP TABLE `migrate_temp_user`;
DROP TABLE `migrate_temp_mem_partner`;
DROP TABLE `issue_web_url`;
DROP TABLE `issue_web_report`;
DROP TABLE `promotion_return_loser_status`;
DROP TABLE `promotion_return_loser`;
DROP TABLE `promotion_return_cut_type`;
DROP TABLE `play_log`;
DROP TABLE `permission`;
DROP TABLE `user_transaction`;
DROP TABLE `user_status`;
DROP TABLE `user_otp`;
DROP TABLE `user_login_log`;
DROP TABLE `user_alliance`;
DROP TABLE `webhook_log`;
DROP TABLE `user_update_log`;
DROP TABLE `user_type`;
DROP TABLE `user_transaction_type`;
DROP TABLE `user_transaction_direction`;
DROP TABLE `user_affiliate`;
DROP TABLE `turnover_statement`;
DROP TABLE `transaction_type`;
DROP TABLE `transaction_status`;
DROP TABLE `statement_type`;
DROP TABLE `statement_status`;
DROP TABLE `user`;
DROP TABLE `turn_withdraw_type`;
DROP TABLE `turnover_withdraw_log`;
DROP TABLE `turnover_statement_type`;
DROP TABLE `turnover_statement_status`;
DROP TABLE `alliance_income`;
DROP TABLE `alliance_first_deposit`;
DROP TABLE `alliance_commission`;
DROP TABLE `alliance`;
DROP TABLE `agent_info`;
DROP TABLE `bank`;
DROP TABLE `auto_withdraw_type`;
DROP TABLE `auto_user_approve_type`;
DROP TABLE `api_status`;
DROP TABLE `alliance_transaction`;
DROP TABLE `affiliate_income`;
DROP TABLE `admin_action`;
DROP TABLE `admin`;
DROP TABLE `account_status`;
DROP TABLE `account_move_transaction_status`;
DROP TABLE `account_move_transaction`;
DROP TABLE `affiliate_commission`;
DROP TABLE `affiliate`;
DROP TABLE `admin_group_permission`;
DROP TABLE `admin_group`;
DROP TABLE `admin_action_type`;
DROP TABLE `configuration_notification_type`;
DROP TABLE `configuration_notification`;
DROP TABLE `commission_transfer`;
DROP TABLE `botaccount_statement`;
DROP TABLE `botaccount_log`;
DROP TABLE `issue_status`;
DROP TABLE `issue_report`;
DROP TABLE `cron_action`;
DROP TABLE `connection_status`;
DROP TABLE `configuration_web`;
DROP TABLE `botaccount_config`;
DROP TABLE `bank_confirm_statement`;
DROP TABLE `bank_account_type`;
DROP TABLE `bank_account_transaction`;
DROP TABLE `bank_account_priority`;
DROP TABLE `bank_account`;
DROP TABLE `bank_transaction_slip`;
DROP TABLE `bank_transaction_log`;
DROP TABLE `bank_transaction_confirm`;
DROP TABLE `bank_transaction`;
DROP TABLE `bank_statement`;