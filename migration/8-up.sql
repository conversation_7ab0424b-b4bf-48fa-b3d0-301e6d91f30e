ALTER TABLE `bank_account`
  ADD COLUMN `show_bank_deposit_over_due_time` INT NOT NULL DEFAULT 0 AFTER `image_url`;

CREATE TABLE IF NOT EXISTS `quest_type_v2` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255),
    `query_table` VARCHAR(100),
    `is_active` TINYINT NOT NULL DEFAULT 1,
    `is_deleted` TINYINT NOT NULL DEFAULT 0,
    PRIMARY KEY (`id`)
);

INSERT INTO `quest_type_v2` (`id`, `name`, `query_table`, `is_active`, `is_deleted`) VALUES
 (1, 'เช็คอิน', 'daily_checkin', 1, 0),
 (2, 'เติมเครดิต', 'credit_deposit', 1, 0),
 (3, 'กีฬา', 'sport', 1, 0),
 (4, 'คาสิโน', 'casino', 1, 0),
 (5, 'เกมส์', 'game', 1, 0),
 (6, 'ลอตเตอรี่', 'lottery', 1, 0),
 (7, 'p2p', 'p2p', 1, 0);

CREATE TABLE IF NOT EXISTS `quest_v2` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `quest_type_id` BIGINT NOT NULL,
    `name` VARCHAR(255) NOT NULL,
    `image_url` VARCHAR(255),
    `description` VARCHAR(255),
    `is_deleted` TINYINT NOT NULL DEFAULT 0,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `quest_v2_quest_type_id_fk` (`quest_type_id`),
    CONSTRAINT `quest_v2_quest_type_id_fk` FOREIGN KEY (`quest_type_id`) REFERENCES `quest_type_v2` (`id`)
);

CREATE TABLE IF NOT EXISTS `daily_quest_condition_v2` (
    id       bigint auto_increment primary key,
    name     varchar(255) not null,
    label_th varchar(255) not null,
    label_en varchar(255) not null
);

INSERT INTO `daily_quest_condition_v2` (`id`, `name`, `label_th`, `label_en`) VALUES
(1, 'no condition', 'ไม่มีเงื่อนไข', 'no condition'),
(2, 'min', 'ขั้นต่ำ', 'minimum'),
(3, 'equal', 'เท่ากับ', 'equal'),

CREATE TABLE IF NOT EXISTS `daily_quest_v2` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `description` VARCHAR(255),
    `reward` DECIMAL(10,2) NOT NULL DEFAULT 0,
    `start_date` DATE NOT NULL,
    `end_date` DATE NOT NULL,
    `start_time` TIME NOT NULL DEFAULT '00:00:00',
    `end_time` TIME NOT NULL DEFAULT '00:00:00',
    `is_deleted` TINYINT NOT NULL DEFAULT 0,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
);

CREATE TABLE IF NOT EXISTS `daily_quest_detail_v2` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `quest_id` BIGINT NOT NULL,
    `daily_quest_id` BIGINT NOT NULL,
    `daily_quest_condition_id` BIGINT NOT NULL,
    `condition_amount` DECIMAL(10,2) NOT NULL DEFAULT 0,
    `sort` BIGINT NOT NULL DEFAULT 0,
    PRIMARY KEY (`id`),
    KEY `daily_quest_detail_v2_quest_id_fk` (`quest_id`),
    KEY `daily_quest_detail_v2_daily_quest_id_fk` (`daily_quest_id`),
    KEY `daily_quest_detail_v2_daily_quest_condition_id_fk` (`daily_quest_condition_id`),
    CONSTRAINT `daily_quest_detail_v2_quest_id_fk` FOREIGN KEY (`quest_id`) REFERENCES `quest_v2` (`id`),
    CONSTRAINT `daily_quest_detail_v2_daily_quest_id_fk` FOREIGN KEY (`daily_quest_id`) REFERENCES `daily_quest_v2` (`id`),
    CONSTRAINT `daily_quest_detail_v2_daily_quest_condition_id_fk` FOREIGN KEY (`daily_quest_condition_id`) REFERENCES `daily_quest_condition_v2` (`id`)
);

CREATE TABLE  IF NOT EXISTS `user_daily_quest_v2` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT NOT NULL,
    `daily_quest_id` BIGINT NOT NULL,
    `status` TINYINT NOT NULL DEFAULT 0,
    `is_claim` TINYINT NOT NULL DEFAULT 0,
    `created_at` DATE NOT NULL,
    PRIMARY KEY (`id`),
    KEY `user_quest_v2_user_id_fk` (`user_id`),
    KEY `user_quest_v2_daily_quest_id_fk` (`daily_quest_id`),
    CONSTRAINT `user_quest_v2_user_id_fk` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`),
    CONSTRAINT `user_quest_v2_daily_quest_id_fk` FOREIGN KEY (`daily_quest_id`) REFERENCES `daily_quest_v2` (`id`)
);

CREATE TABLE  IF NOT EXISTS `user_daily_quest_detail_v2` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `user_daily_quest_id` BIGINT NOT NULL,
    `quest_id` BIGINT NOT NULL,
    `status` TINYINT NOT NULL DEFAULT 0,
    PRIMARY KEY (`id`),
    KEY `user_daily_quest_detail_v2_user_daily_quest_id_fk` (`user_daily_quest_id`),
    KEY `user_daily_quest_detail_v2_quest_id_fk` (`quest_id`),
    CONSTRAINT `user_daily_quest_detail_v2_user_daily_quest_id_fk` FOREIGN KEY (`user_daily_quest_id`) REFERENCES `user_daily_quest_v2` (`id`),
    CONSTRAINT `user_daily_quest_detail_v2_quest_id_fk` FOREIGN KEY (`quest_id`) REFERENCES `quest_v2` (`id`)
);

CREATE TABLE IF NOT EXISTS `activity_daily_auto_checkin_user_v2` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `activity_daily_v2_id` BIGINT NOT NULL,
    `current_no_number` INT NOT NULL,
    `user_id` BIGINT NOT NULL,
    `accumulated_credit_balance` DECIMAL(14,2) NOT NULL,
    `is_completed` TINYINT NOT NULL DEFAULT 0,
    `is_claimed` TINYINT NOT NULL DEFAULT 0,
    `claim_at` DATETIME NULL,
    `checkin_at` DATE NOT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
    PRIMARY KEY (`id`)
);

CREATE TABLE IF NOT EXISTS `user_quest_today_playlog_v2` (
     id                     bigint auto_increment primary key,
     user_id                bigint                                   not null,
     daily_key              varchar(255)                             null,
    statement_date         varchar(10)                              null,
    turn_sport             decimal(14, 2) default 0.00              not null,
    valid_amount_sport     decimal(14, 2) default 0.00              not null,
    win_lose_sport         decimal(14, 2) default 0.00              not null,
    turn_casino            decimal(14, 2) default 0.00              not null,
    valid_amount_casino    decimal(14, 2) default 0.00              not null,
    win_lose_casino        decimal(14, 2) default 0.00              not null,
    turn_game              decimal(14, 2) default 0.00              not null,
    valid_amount_game      decimal(14, 2) default 0.00              not null,
    win_lose_game          decimal(14, 2) default 0.00              not null,
    turn_lottery           decimal(14, 2) default 0.00              not null,
    win_lose_lottery       decimal(14, 2) default 0.00              not null,
    valid_amount_lottery   decimal(14, 2) default 0.00              not null,
    turn_p2p               decimal(14, 2) default 0.00              not null,
    win_lose_p2p           decimal(14, 2) default 0.00              not null,
    valid_amount_p2p       decimal(14, 2) default 0.00              not null,
    turn_financial         decimal(14, 2) default 0.00              not null,
    win_lose_financial     decimal(14, 2) default 0.00              not null,
    valid_amount_financial decimal(14, 2) default 0.00              not null,
    turn_total             decimal(14, 2) default 0.00              null,
    win_lose_total         decimal(14, 2) default 0.00              null,
    valid_amount_total     decimal(14, 2) default 0.00              null,
    created_at             datetime       default CURRENT_TIMESTAMP not null,
    updated_at             datetime       default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
    constraint daily_key unique (daily_key)
);
create index statement_date on `user_quest_today_playlog_v2` (statement_date);
create index user_id on `user_quest_today_playlog_v2` (user_id);

INSERT INTO `user_transaction_type` (`id`, `name`, `detail`, `created_at`) VALUES
(15,'CREDIT_TYPE_DAILY_QUEST_BONUS','โบนัสเควสประจำวัน', '2023-10-09 08:52:44');

INSERT INTO `turnover_statement_type` (`id`, `name`, `created_at`) VALUES
(19, 'TURN_BONUS_DAILY_QUEST_V2', '2023-10-09 08:52:44');

ALTER TABLE `turnover_setting`
    ADD COLUMN `tidturn_daily_quest_v2_percent` INT DEFAULT 100;

CREATE TABLE IF NOT EXISTS `ranking_v2` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255),
    `image_url` VARCHAR(255),
    `min_score` INT NOT NULL DEFAULT 0,
    `max_score` INT NOT NULL DEFAULT 0,
    `sort` INT NOT NULL DEFAULT 0,
    `is_active` TINYINT NOT NULL DEFAULT 1,
    `is_deleted` TINYINT NOT NULL DEFAULT 0,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
);

INSERT INTO `ranking_v2` (`id`, `name`, `image_url`, `min_score`, `max_score`, `sort`, `is_active`, `is_deleted` , `created_at`, `updated_at`) VALUES
(1, 'Diamond', '', 401, 500, 1, 1, 0, NOW(), NOW()),
(2, 'Gold', '', 201, 300, 2, 1, 0, NOW(), NOW()),
(3, 'Silver', '', 101, 200, 3, 1, 0, NOW(), NOW()),
(4, 'Bronze', '', 0, 100, 4, 1, 0, NOW(), NOW());

ALTER TABLE daily_quest_v2
ADD COLUMN is_active BIGINT NOT NULL DEFAULT 0;

INSERT INTO `permission` (`id`, `permission_key`, `main`, `name`, `position`, `created_at`, `updated_at`, `deleted_at`) VALUES
(105, 'setting_quest', 1, 'จัดการภารกิจรายวัน', NULL, '2025-06-22 23:00:00', NULL, NULL),
(106, 'setting_quest_list', 0, 'รายการภารกิจรายวัน', NULL, '2023-06-22 23:00:00', NULL, NULL),
(107, 'quest_list', 0, 'รายการภารกิจ', NULL, '2023-06-22 23:00:00', NULL, NULL),
(108, 'ranking', 1, 'Ranking', NULL, '2023-06-22 23:00:00', NULL, NULL);

ALTER TABLE activity_daily_auto_checkin_user_v2
ADD COLUMN change_count_time INT NOT NULL DEFAULT 10;

ALTER TABLE activity_daily_auto_checkin_user_v2
ADD COLUMN accumulated_credit_received DECIMAL(14,2) NOT NULL DEFAULT 0.00;
