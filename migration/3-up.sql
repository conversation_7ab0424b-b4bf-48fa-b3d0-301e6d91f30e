-- --------------------------------------------------------
-- Host:                         db-cyberrich-prod.ondigitalocean.com
-- Server version:               8.0.30 - Source distribution
-- Server OS:                    Linux
-- HeidiSQL Version:             12.5.0.6677
-- --------------------------------------------------------

-- API Version: 1.3.x
-- ตั้งค่า ชื่อฐ่านข้อมูล และ โดเมนเนม
USE xxx;
SET @DOMAIN_NAME = 'xxx.com';

-- DO NOT EDIT AFTER THIS LINE --

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

CREATE TABLE IF NOT EXISTS `account_move_transaction_status` (
  `id` bigint NOT NULL,
  `name` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
INSERT INTO `account_move_transaction_status` (`id`, `name`, `created_at`) VALUES
	(1, 'รอโอน', '2023-11-14 19:15:30'),
	(2, 'สำเร็จ', '2023-11-14 19:15:30'),
	(3, 'ไม่สำเร็จ', '2023-11-14 19:15:30');

CREATE TABLE IF NOT EXISTS `account_move_transaction` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `admin_id` bigint DEFAULT NULL,
  `from_account_id` bigint NOT NULL,
  `to_account_id` bigint NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `status_id` bigint NOT NULL DEFAULT '1',
  `transfer_at` datetime DEFAULT NULL,
  `remark` varchar(255) DEFAULT NULL,
  `is_success` tinyint NOT NULL DEFAULT '0',
  `json_fastbank_resp` text,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);

CREATE TABLE IF NOT EXISTS `account_status`(
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT,
    `name` ENUM('ACTIVE', 'DEACTIVE') NULL DEFAULT NULL,
    `label_th` VARCHAR(255) NULL DEFAULT NULL,
    `label_en` VARCHAR(255) NULL DEFAULT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
    `deleted_at` DATETIME NULL DEFAULT NULL
);
INSERT INTO `account_status` (`id`, `name`, `label_th`, `label_en`) VALUES
  (1, 'ACTIVE', 'ใช้งาน', 'active'),
  (2, 'DEACTIVE', 'ระงับการใช้งาน', 'deactive');


CREATE TABLE IF NOT EXISTS `admin_action_type` (
  `id` bigint NOT NULL,
  `name` varchar(255) NOT NULL DEFAULT '',
  `detail` varchar(255) NOT NULL DEFAULT '',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);

INSERT INTO `admin_action_type` (`id`, `name`, `detail`, `created_at`) VALUES
	(4, 'MOVE_ACCOUNT_MONEY', 'Admin Move Money', '2023-10-21 09:37:47'),
	(5, 'LOGOUT', 'Admin Logout', '2023-11-07 03:47:45'),
	(6, 'MANAGE', 'Admin Manage', '2023-11-07 03:47:45'),
	(7, 'LOGIN', 'Admin Login', '2023-11-07 03:47:45'),
	(8, 'MANAGE_APPLICATION', 'Application Config', '2023-11-07 03:47:45'),
	(9, 'ACCOUNT_MANAGE', 'Bank Account', '2023-11-07 03:47:45'),
	(10, 'GROUP_CREATE', 'Manage Admin Group', '2023-11-07 03:47:45');

CREATE TABLE IF NOT EXISTS `admin_action` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `admin_id` bigint NOT NULL,
  `type_id` bigint NOT NULL DEFAULT '1',
  `is_success` tinyint NOT NULL DEFAULT '0',
  `ref_object_id` bigint DEFAULT NULL,
  `detail` varchar(255) DEFAULT NULL,
  `json_input` text,
  `json_output` text,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `admin_action_user_id_index` (`admin_id`)
);

CREATE TABLE IF NOT EXISTS `admin_group_permission` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `group_id` bigint DEFAULT NULL,
  `permission_id` bigint DEFAULT NULL,
  `is_read` tinyint DEFAULT '0',
  `is_write` tinyint DEFAULT '0',
  `created_at` datetime DEFAULT (now()),
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_deleted_at` (`deleted_at`),
  KEY `idx_group_id` (`group_id`),
  KEY `idx_permission_id` (`permission_id`)
);

CREATE TABLE IF NOT EXISTS `admin_group` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `admin_count` int DEFAULT '0',
  `created_at` datetime DEFAULT (now()),
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni_name` (`name`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_deleted_at` (`deleted_at`)
);

CREATE TABLE IF NOT EXISTS `admin` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `username` varchar(255) DEFAULT NULL,
  `password` varchar(255) DEFAULT NULL,
  `role` enum('SUPER_ADMIN','ADMIN') DEFAULT NULL,
  `status` enum('ACTIVE','DEACTIVE') DEFAULT NULL,
  `firstname` varchar(255) DEFAULT NULL,
  `lastname` varchar(255) DEFAULT NULL,
  `fullname` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `phone` varchar(255) DEFAULT NULL,
  `ip` varchar(255) DEFAULT NULL,
  `admin_group_id` int DEFAULT NULL,
  `logedin_at` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT (now()),
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni_email` (`email`),
  UNIQUE KEY `uni_username` (`username`),
  KEY `idx_admin_group_id` (`admin_group_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_deleted_at` (`deleted_at`),
  KEY `idx_status` (`status`)
);
INSERT INTO `admin` (`id`, `username`, `password`, `role`, `status`, `firstname`, `lastname`, `fullname`, `email`, `phone`, `ip`, `admin_group_id`, `logedin_at`, `created_at`, `updated_at`, `deleted_at`) VALUES
	(1, 'superadmin', '', 'SUPER_ADMIN', 'ACTIVE', 'admin', 'admin', NULL, NULL, NULL, NULL, NULL, NULL, '2023-10-30 15:39:28', NULL, NULL);


CREATE TABLE IF NOT EXISTS `affiliate_commission` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `first_deposit_bonus` decimal(14,2) DEFAULT '0.00',
  `sport` decimal(14,2) DEFAULT '0.00',
  `casino` decimal(14,2) DEFAULT '0.00',
  `slot` decimal(14,2) DEFAULT '0.00',
  `referral_bonus` decimal(14,2) DEFAULT '0.00',
  `commission_withdraw_min` int DEFAULT '0',
  `register_bonus_type_id` bigint DEFAULT NULL,
  `register_bonus_min` int DEFAULT '0',
  `register_bonus_credit` int DEFAULT '0',
  `register_bonus_max_percent` int DEFAULT '0',
  `description` text,
  `register_bonus_option_id` bigint DEFAULT NULL,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `register_bonus_option_id` (`register_bonus_option_id`),
  KEY `register_bonus_type_id` (`register_bonus_type_id`),
  CONSTRAINT `affiliate_commission_ibfk_1` FOREIGN KEY (`register_bonus_type_id`) REFERENCES `register_bonus_type` (`id`),
  CONSTRAINT `affiliate_commission_ibfk_2` FOREIGN KEY (`register_bonus_option_id`) REFERENCES `register_bonus_option` (`id`)
);
INSERT INTO `affiliate_commission` (`id`, `first_deposit_bonus`, `sport`, `casino`, `slot`, `referral_bonus`, `commission_withdraw_min`, `register_bonus_type_id`, `register_bonus_min`, `register_bonus_credit`, `register_bonus_max_percent`, `description`, `register_bonus_option_id`, `updated_at`) VALUES
	(1, 0.00, 0.70, 0.40, 0.40, 20.00, 50, 1, 300, 50, 0, NULL, 3, '2023-10-31 08:12:36');

CREATE TABLE IF NOT EXISTS `affiliate_income` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `sport` decimal(14,2) DEFAULT '0.00',
  `casino` decimal(14,2) DEFAULT '0.00',
  `slot` decimal(14,2) DEFAULT '0.00',
  `user_id` bigint NOT NULL,
  `ref_id` bigint DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `affiliate_income_ref_id_fk` (`ref_id`),
  KEY `affiliate_income_user_id_fk` (`user_id`),
  CONSTRAINT `affiliate_income_ref_id_fk` FOREIGN KEY (`ref_id`) REFERENCES `user` (`id`),
  CONSTRAINT `affiliate_income_user_id_fk` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
);

CREATE TABLE IF NOT EXISTS `affiliate` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `register_bonus_credit` decimal(14,2) DEFAULT '0.00',
  `ref_id` bigint NOT NULL,
  `user_id` bigint NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `affiliate_ref_id_user_id_uindex` (`ref_id`,`user_id`),
  KEY `idx_user_id` (`user_id`),
  CONSTRAINT `affiliate_Users_id_fk` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`),
  CONSTRAINT `fk_ref_id` FOREIGN KEY (`ref_id`) REFERENCES `user` (`id`)
);

CREATE TABLE IF NOT EXISTS `agent_info` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `total` int DEFAULT '0',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
INSERT INTO `agent_info` (`id`, `total`, `updated_at`) VALUES
	(1, 0, '2023-11-12 14:09:01');

CREATE TABLE IF NOT EXISTS `alliance_commission` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `referral_bonus` decimal(14,2) NOT NULL DEFAULT '0.00',
  `description` text,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
INSERT INTO `alliance_commission` (`id`, `referral_bonus`, `description`, `created_at`, `updated_at`) VALUES
	(1, 10.00, '', '2023-10-10 08:48:35', '2023-10-30 11:33:57');

CREATE TABLE IF NOT EXISTS `alliance_first_deposit` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `credit` decimal(14,2) NOT NULL DEFAULT '0.00',
  `user_id` bigint NOT NULL,
  `ref_id` bigint NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `alliance_first_deposit_ref_id_fk` (`ref_id`),
  KEY `alliance_first_deposit_user_id_fk` (`user_id`),
  CONSTRAINT `alliance_first_deposit_ref_id_fk` FOREIGN KEY (`ref_id`) REFERENCES `user` (`id`),
  CONSTRAINT `alliance_first_deposit_user_id_fk` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
);

CREATE TABLE IF NOT EXISTS `alliance_income` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `turn` decimal(14,2) NOT NULL DEFAULT '0.00',
  `turn_calculated` decimal(14,2) NOT NULL DEFAULT '0.00',
  `win_lose` decimal(14,2) NOT NULL DEFAULT '0.00',
  `win_lose_calculated` decimal(14,2) NOT NULL DEFAULT '0.00',
  `user_id` bigint NOT NULL,
  `ref_id` bigint NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `alliance_income_ref_id_fk` (`ref_id`),
  KEY `alliance_income_user_id_fk` (`user_id`),
  CONSTRAINT `alliance_income_ref_id_fk` FOREIGN KEY (`ref_id`) REFERENCES `user` (`id`),
  CONSTRAINT `alliance_income_user_id_fk` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
);

CREATE TABLE IF NOT EXISTS `alliance_transaction` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `deposit` decimal(14,2) NOT NULL DEFAULT '0.00',
  `withdraw` decimal(14,2) NOT NULL DEFAULT '0.00',
  `net_amount` decimal(14,2) NOT NULL DEFAULT '0.00',
  `user_id` bigint NOT NULL,
  `ref_id` bigint NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `alliance_transaction_ref_id_fk` (`ref_id`),
  KEY `alliance_transaction_user_id_fk` (`user_id`),
  CONSTRAINT `alliance_transaction_ref_id_fk` FOREIGN KEY (`ref_id`) REFERENCES `user` (`id`),
  CONSTRAINT `alliance_transaction_user_id_fk` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
);

CREATE TABLE IF NOT EXISTS `alliance` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `ref_id` bigint NOT NULL,
  `user_id` bigint NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `alliance_user_id_fk` (`user_id`),
  CONSTRAINT `alliance_user_id_fk` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
);

CREATE TABLE IF NOT EXISTS `api_status` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `path` varchar(255) NOT NULL,
  `page` int DEFAULT '0',
  `is_failed` tinyint DEFAULT '0',
  `is_success` tinyint DEFAULT '0',
  `statement_date` varchar(10) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_path` (`path`),
  KEY `idx_stement_date` (`statement_date`)
);

CREATE TABLE IF NOT EXISTS `auto_user_approve_type` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `label_th` varchar(255) DEFAULT NULL,
  `label_en` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
);
INSERT INTO `auto_user_approve_type` (`id`, `name`, `label_th`, `label_en`, `created_at`, `updated_at`, `deleted_at`) VALUES
	(1, 'after signing up', 'รับ user ทันทีหลังสมัครสมาชิก', 'Approved users immediately after signing up', '2023-09-18 12:00:15', NULL, NULL),
	(2, 'first deposit', 'รับ user หลังจากฝากครั้งแรก', 'Approved user after first deposit', '2023-09-18 12:00:15', NULL, NULL);

CREATE TABLE IF NOT EXISTS `auto_withdraw_type` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `label_th` varchar(255) DEFAULT NULL,
  `label_en` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
);
INSERT INTO `auto_withdraw_type` (`id`, `name`, `label_th`, `label_en`, `created_at`, `updated_at`, `deleted_at`) VALUES
	(1, 'withdraw Document', 'สร้างใบงานถอน', 'Create a withdraw Document', '2023-09-18 12:00:15', NULL, NULL),
	(2, 'withdraw auto', 'ปรับเครดิต ถอนออโต้', 'Adjust credit, auto withdraw', '2023-09-18 12:00:15', NULL, NULL);

CREATE TABLE IF NOT EXISTS `bank_account_priority` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `condition_type` varchar(255) NOT NULL DEFAULT 'or',
  `min_deposit_count` int NOT NULL DEFAULT '0',
  `min_deposit_total` decimal(14,2) NOT NULL DEFAULT '0.00',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
);
INSERT INTO `bank_account_priority` (`id`, `name`, `condition_type`, `min_deposit_count`, `min_deposit_total`, `created_at`, `updated_at`, `deleted_at`) VALUES
	(1, 'ระดับ NEW ทั่วไป', 'or', 0, 0.00, '2023-09-18 12:00:14', NULL, NULL),
	(2, 'ระดับ Gold ฝากมากกว่า 10 ครั้ง หรือ ฝากสะสมมากกว่า 10,000 บาท', 'or', 10, 10000.00, '2023-09-18 12:00:14', NULL, NULL),
	(3, 'ระดับ Platinum ฝากมากกว่า 20 ครั้ง หรือ ฝากสะสมมากกว่า 100,000 บาท', 'or', 20, 100000.00, '2023-09-18 12:00:14', NULL, NULL),
	(4, 'ระดับ VIP ฝากมากกว่า 30 ครั้ง หรือ ฝากสะสมมากกว่า 500,000 บาท', 'or', 30, 500000.00, '2023-09-18 12:00:14', NULL, NULL);


CREATE TABLE IF NOT EXISTS `bank_account_transaction` (
  `id` int NOT NULL AUTO_INCREMENT,
  `account_id` bigint NOT NULL,
  `description` varchar(255) NOT NULL,
  `transaction_type_id` bigint NOT NULL,
  `amount` decimal(14,2) NOT NULL,
  `transfer_at` datetime NOT NULL,
  `created_by_username` varchar(255) NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_account_id` (`account_id`),
  KEY `transaction_type_id` (`transaction_type_id`),
  CONSTRAINT `bank_account_transaction_ibfk_1` FOREIGN KEY (`transaction_type_id`) REFERENCES `transaction_type` (`id`)
);


CREATE TABLE IF NOT EXISTS `bank_account_type` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `limit_flag` varchar(8) NOT NULL DEFAULT '********',
  `allow_deposit` tinyint NOT NULL DEFAULT '0',
  `allow_withdraw` tinyint NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
INSERT INTO `bank_account_type` (`id`, `name`, `limit_flag`, `allow_deposit`, `allow_withdraw`, `created_at`) VALUES
	(1, 'เฉพาะฝาก', '********', 1, 0, '2023-09-18 12:00:12'),
	(2, 'เฉพาะถอน', '********', 0, 1, '2023-09-18 12:00:12'),
	(3, 'ฝาก-ถอน', '********', 1, 1, '2023-09-18 12:00:12'),
	(4, 'พักเงิน', '********', 0, 0, '2023-09-18 12:00:12');

CREATE TABLE IF NOT EXISTS `bank_account` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `bank_id` bigint NOT NULL,
  `account_type_id` bigint NOT NULL,
  `account_name` varchar(255) NOT NULL,
  `account_number` varchar(255) NOT NULL,
  `account_balance` decimal(14,2) NOT NULL DEFAULT '0.00',
  `account_priority_withdraw` bigint DEFAULT NULL,
  `device_uid` varchar(255) NOT NULL,
  `pin_code` varchar(255) NOT NULL,
  `external_id` bigint DEFAULT NULL,
  `connection_status_id` bigint DEFAULT NULL,
  `last_conn_update_at` datetime DEFAULT NULL,
  `admin_updated_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni_account_number` (`account_number`),
  KEY `connection_status_id` (`connection_status_id`),
  KEY `idx_account_type_id` (`account_type_id`),
  KEY `idx_bank_id` (`bank_id`),
  KEY `idx_external_id` (`external_id`),
  CONSTRAINT `bank_account_ibfk_1` FOREIGN KEY (`connection_status_id`) REFERENCES `connection_status` (`id`),
  CONSTRAINT `bank_account_ibfk_3` FOREIGN KEY (`account_type_id`) REFERENCES `bank_account_type` (`id`)
);

CREATE TABLE IF NOT EXISTS `bank_confirm_statement` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `statement_id` bigint NOT NULL,
  `action_type` varchar(255) NOT NULL,
  `user_id` bigint DEFAULT NULL,
  `account_id` bigint NOT NULL,
  `json_before` text NOT NULL,
  `confirmed_at` datetime DEFAULT NULL,
  `confirmed_by_admin_id` bigint DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni_statement_id` (`statement_id`),
  KEY `idx_account_id` (`account_id`)
);

CREATE TABLE IF NOT EXISTS `bank_statement` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `account_id` bigint NOT NULL,
  `external_id` bigint DEFAULT NULL,
  `amount` decimal(14,2) NOT NULL,
  `detail` varchar(255) NOT NULL,
  `from_bank_id` bigint DEFAULT NULL,
  `from_account_number` varchar(255) DEFAULT NULL,
  `statement_type_id` bigint NOT NULL DEFAULT '0',
  `transfer_at` datetime NOT NULL,
  `statement_status_id` bigint NOT NULL DEFAULT '0',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni_external_id` (`external_id`),
  KEY `idx_account_id` (`account_id`),
  KEY `statement_status_id` (`statement_status_id`),
  KEY `statement_type_id` (`statement_type_id`),
  CONSTRAINT `bank_statement_ibfk_1` FOREIGN KEY (`statement_type_id`) REFERENCES `statement_type` (`id`),
  CONSTRAINT `bank_statement_ibfk_2` FOREIGN KEY (`statement_status_id`) REFERENCES `statement_status` (`id`)
);

CREATE TABLE IF NOT EXISTS `bank_transaction_confirm` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `action_key` varchar(255) NOT NULL,
  `transaction_id` bigint NOT NULL,
  `user_id` bigint NOT NULL,
  `transaction_type_id` bigint NOT NULL,
  `from_account_id` bigint DEFAULT NULL,
  `to_account_id` bigint DEFAULT NULL,
  `json_before` text,
  `transfer_at` datetime DEFAULT NULL,
  `slip_url` varchar(255) DEFAULT NULL,
  `credit_amount` decimal(14,2) DEFAULT NULL,
  `bank_charge_amount` decimal(14,2) NOT NULL DEFAULT '0.00',
  `bonus_amount` decimal(14,2) NOT NULL DEFAULT '0.00',
  `confirmed_at` datetime DEFAULT NULL,
  `confirmed_by_admin_id` bigint DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni_action_key` (`action_key`),
  UNIQUE KEY `uni_transaction_id` (`transaction_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `transaction_type_id` (`transaction_type_id`),
  CONSTRAINT `bank_transaction_confirm_ibfk_1` FOREIGN KEY (`transaction_type_id`) REFERENCES `transaction_type` (`id`)
);

CREATE TABLE IF NOT EXISTS `bank_transaction_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `json_request` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `json_payload` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `log_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
);

CREATE TABLE IF NOT EXISTS `bank_transaction_slip` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `raw_qr_code` varchar(255) DEFAULT NULL,
  `statement_id` bigint DEFAULT NULL,
  `slip_url` text,
  `amount` decimal(14,2) NOT NULL DEFAULT '0.00',
  `status_id` bigint NOT NULL,
  `type` enum('DEPOSIT','WITHDRAW') NOT NULL DEFAULT 'DEPOSIT',
  `user_id` bigint NOT NULL,
  `bank_id` bigint DEFAULT NULL,
  `bank_account_id` bigint DEFAULT NULL,
  `deposited_at` datetime DEFAULT NULL,
  `confirmed_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `raw_qr_code` (`raw_qr_code`),
  KEY `fk_bank_account_idx` (`bank_account_id`),
  KEY `fk_bank_id_idx` (`bank_id`),
  KEY `idx_status_id` (`status_id`),
  KEY `idx_type` (`type`),
  KEY `idx_user_id` (`user_id`),
  CONSTRAINT `fk_bank_account` FOREIGN KEY (`bank_account_id`) REFERENCES `bank_account` (`id`),
  CONSTRAINT `fk_bank_id` FOREIGN KEY (`bank_id`) REFERENCES `bank` (`id`),
  CONSTRAINT `fk_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_user_transaction_slip_cybergame.transaction_status` FOREIGN KEY (`status_id`) REFERENCES `transaction_status` (`id`)
);

CREATE TABLE IF NOT EXISTS `bank_transaction` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `member_code` varchar(255) DEFAULT NULL,
  `user_id` bigint DEFAULT NULL,
  `statement_id` bigint DEFAULT NULL,
  `transaction_type_id` bigint NOT NULL,
  `promotion_id` bigint DEFAULT NULL,
  `from_account_id` bigint DEFAULT NULL,
  `from_bank_id` bigint DEFAULT NULL,
  `from_account_name` varchar(255) DEFAULT NULL,
  `from_account_number` varchar(255) DEFAULT NULL,
  `to_account_id` bigint DEFAULT NULL,
  `to_bank_id` bigint DEFAULT NULL,
  `to_account_name` varchar(255) DEFAULT NULL,
  `to_account_number` varchar(255) DEFAULT NULL,
  `credit_amount` decimal(14,2) NOT NULL DEFAULT '0.00',
  `credit_back` decimal(14,2) NOT NULL DEFAULT '0.00',
  `over_amount` decimal(14,2) NOT NULL DEFAULT '0.00',
  `deposit_channel` varchar(255) DEFAULT NULL,
  `bonus_amount` decimal(14,2) NOT NULL DEFAULT '0.00',
  `bonus_reason` varchar(255) DEFAULT NULL,
  `before_amount` decimal(14,2) NOT NULL DEFAULT '0.00',
  `after_amount` decimal(14,2) NOT NULL DEFAULT '0.00',
  `transfer_at` datetime DEFAULT NULL,
  `created_by_admin_id` bigint NOT NULL,
  `cancel_remark` varchar(255) DEFAULT NULL,
  `canceled_at` datetime DEFAULT NULL,
  `canceled_by_admin_id` bigint DEFAULT NULL,
  `confirmed_at` datetime DEFAULT NULL,
  `confirmed_by_admin_id` bigint DEFAULT NULL,
  `transaction_status_id` bigint NOT NULL,
  `is_first_deposit` tinyint NOT NULL DEFAULT '0',
  `is_auto_credit` tinyint NOT NULL DEFAULT '0',
  `auto_process_timer` varchar(8) NOT NULL DEFAULT '0',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_user_transaction_cybergame.transaction_status_4` (`transaction_status_id`),
  KEY `idx_from_account_id` (`from_account_id`),
  KEY `idx_to_account_id` (`to_account_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `transaction_type_id` (`transaction_type_id`),
  CONSTRAINT `bank_transaction_ibfk_1` FOREIGN KEY (`transaction_type_id`) REFERENCES `transaction_type` (`id`),
  CONSTRAINT `bank_transaction_ibfk_6` FOREIGN KEY (`transaction_status_id`) REFERENCES `transaction_status` (`id`),
  CONSTRAINT `FK_user_transaction_cybergame.transaction_status` FOREIGN KEY (`transaction_status_id`) REFERENCES `transaction_status` (`id`),
  CONSTRAINT `FK_user_transaction_cybergame.transaction_status_2` FOREIGN KEY (`transaction_status_id`) REFERENCES `transaction_status` (`id`),
  CONSTRAINT `FK_user_transaction_cybergame.transaction_status_3` FOREIGN KEY (`transaction_status_id`) REFERENCES `transaction_status` (`id`),
  CONSTRAINT `FK_user_transaction_cybergame.transaction_status_4` FOREIGN KEY (`transaction_status_id`) REFERENCES `transaction_status` (`id`)
);

CREATE TABLE IF NOT EXISTS `bank` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `code` varchar(255) NOT NULL,
  `icon_url` varchar(255) NOT NULL,
  `type_flag` varchar(8) NOT NULL DEFAULT '********',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni_code` (`code`)
);
INSERT INTO `bank` (`id`, `name`, `code`, `icon_url`, `type_flag`, `created_at`) VALUES
	(1, 'กสิกรไทย', 'kbank', 'https://storage.googleapis.com/cbgame/banks/kbank.png', '********', '2023-09-18 12:00:11'),
	(2, 'ไทยพาณิชย์', 'scb', 'https://storage.googleapis.com/cbgame/banks/scb.png', '********', '2023-09-18 12:00:11'),
	(3, 'กรุงเทพ', 'bbl', 'https://storage.googleapis.com/cbgame/banks/bbl.png', '********', '2023-09-18 12:00:11'),
	(4, 'กรุงศรี', 'bay', 'https://storage.googleapis.com/cbgame/banks/bay.png', '********', '2023-09-18 12:00:11'),
	(5, 'กรุงไทย', 'ktb', 'https://storage.googleapis.com/cbgame/banks/ktb.png', '********', '2023-09-18 12:00:11'),
	(6, 'ทีเอ็มบีธนชาต', 'ttb', 'https://storage.googleapis.com/cbgame/banks/ttb.png', '********', '2023-09-18 12:00:11'),
	(7, 'ออมสิน', 'gsb', 'https://storage.googleapis.com/cbgame/banks/gsb.png', '********', '2023-09-18 12:00:11'),
	(8, 'ธกส', 'baac', 'https://storage.googleapis.com/cbgame/banks/baac.png', '********', '2023-09-18 12:00:11'),
	(9, 'เกียรตินาคิน', 'kk', 'https://storage.googleapis.com/cbgame/banks/kk.png', '********', '2023-09-18 12:00:11'),
	(10, 'อาคารสงเคราะห์', 'ghb', 'https://storage.googleapis.com/cbgame/banks/ghb.png', '********', '2023-09-18 12:00:11'),
	(11, 'ยูโอบี', 'uob', 'https://storage.googleapis.com/cbgame/banks/uob.png', '********', '2023-09-18 12:00:11'),
	(12, 'แลนด์ แอนด์ เฮ้าส์', 'lh', 'https://storage.googleapis.com/cbgame/banks/lh.png', '********', '2023-09-18 12:00:11'),
	(13, 'ซีไอเอ็มบี', 'cimb', 'https://storage.googleapis.com/cbgame/banks/cimb.png', '********', '2023-09-18 12:00:11'),
	(14, 'เอชเอสบีซี', 'hsbc', 'https://storage.googleapis.com/cbgame/banks/hsbc.png', '********', '2023-09-18 12:00:11'),
	(15, 'ไอซีบีซี', 'icbc', 'https://storage.googleapis.com/cbgame/banks/icbc.png', '********', '2023-09-18 12:00:11'),
	(16, 'ธนาคารอิสลาม', 'isbt', 'https://storage.googleapis.com/cbgame/banks/isbt.png', '********', '2023-10-30 07:25:21'),
	(17, 'ทิสโก้', 'tisco', 'https://storage.googleapis.com/cbgame/banks/tisco.png', '********', '2023-10-30 07:25:21'),
	(18, 'ซิตี้แบงก์', 'citi', 'https://storage.googleapis.com/cbgame/banks/citi.png', '********', '2023-10-30 07:25:21'),
	(19, 'สแตนดาร์ดชาร์เตอร์ด', 'scbt', 'https://storage.googleapis.com/cbgame/banks/scbt.png', '********', '2023-10-30 07:25:21'),
	(20, 'TrueMoney Wallet', 'true', 'https://storage.googleapis.com/cbgame/banks/true.png', '********', '2023-10-30 07:25:21');

CREATE TABLE IF NOT EXISTS `botaccount_config` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `config_key` varchar(255) NOT NULL,
  `config_val` varchar(255) NOT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_config_key` (`config_key`)
);

INSERT INTO `botaccount_config` (`id`, `config_key`, `config_val`, `deleted_at`) VALUES
	(1, 'allow_create_external_account', 'all', NULL),
	(2, 'allow_create_external_account', 'not_list', NULL),
	(3, 'allow_external_account_number', 'set to list and set account number ', NULL),
	(4, 'allow_withdraw_from_account', '_all', NULL),
	(5, 'withdraw_max_per_time', '50000', NULL),
	(6, 'withdraw_bankaccount_limit', '200000', NULL);

CREATE TABLE IF NOT EXISTS `botaccount_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `external_id` bigint NOT NULL,
  `client_name` varchar(255) NOT NULL,
  `log_type` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `external_create_date` varchar(255) NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_external_id` (`external_id`)
);

CREATE TABLE IF NOT EXISTS `botaccount_statement` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `external_id` bigint NOT NULL,
  `bank_account_id` bigint NOT NULL,
  `bank_code` varchar(255) NOT NULL,
  `amount` decimal(14,2) NOT NULL,
  `date_time` datetime NOT NULL,
  `raw_date_time` datetime NOT NULL,
  `info` varchar(255) NOT NULL,
  `channel_code` varchar(255) NOT NULL,
  `channel_description` varchar(255) NOT NULL,
  `txn_code` varchar(255) NOT NULL,
  `txn_description` varchar(255) NOT NULL,
  `checksum` varchar(255) NOT NULL,
  `is_read` tinyint(1) NOT NULL,
  `external_create_date` varchar(255) NOT NULL,
  `extermal_update_date` varchar(255) NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_bank_account_id` (`bank_account_id`),
  KEY `idx_external_id` (`external_id`)
);

CREATE TABLE IF NOT EXISTS `commission_transfer` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `detail` varchar(255) DEFAULT NULL,
  `commission` decimal(14,2) NOT NULL DEFAULT '0.00',
  `status` tinyint DEFAULT '0',
  `user_id` bigint NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `commission_transfer_user_id_fk` (`user_id`),
  CONSTRAINT `commission_transfer_user_id_fk` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
);

CREATE TABLE IF NOT EXISTS `configuration_notification_type` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `label_th` varchar(255) DEFAULT NULL,
  `label_en` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
);
INSERT INTO `configuration_notification_type` (`id`, `name`, `label_th`, `label_en`, `created_at`, `updated_at`, `deleted_at`) VALUES
	(1, 'ON_ACTIVE', 'เปิดใช้งาน', 'on active', '2023-09-18 12:00:15', NULL, NULL),
	(2, 'CLOSE_WEB', 'ปิดใช้งานหน้าเว็บ', 'close web', '2023-09-18 12:00:15', NULL, NULL),
	(3, 'OFF_ACTIVE', 'ปิดใช้งาน', 'off active', '2023-09-18 12:00:15', NULL, NULL);

CREATE TABLE IF NOT EXISTS `configuration_notification` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `credit_above` bigint DEFAULT '0',
  `line_token` varchar(255) DEFAULT NULL,
  `is_member_registration` tinyint(1) NOT NULL DEFAULT '0',
  `is_deposit_before_credit` tinyint(1) NOT NULL DEFAULT '0',
  `is_deposit_after_credit` tinyint(1) NOT NULL DEFAULT '0',
  `is_withdrawal_credit_success` tinyint(1) NOT NULL DEFAULT '0',
  `is_withdrawal_awaiting_transfer` tinyint(1) NOT NULL DEFAULT '0',
  `is_withdrawal_credit_failed` tinyint(1) NOT NULL DEFAULT '0',
  `configuration_notification_type_id` bigint DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `configuration_notification_type_id` (`configuration_notification_type_id`),
  CONSTRAINT `configuration_notification_ibfk_1` FOREIGN KEY (`configuration_notification_type_id`) REFERENCES `configuration_notification_type` (`id`)
);
INSERT INTO `configuration_notification` (`id`, `credit_above`, `line_token`, `is_member_registration`, `is_deposit_before_credit`, `is_deposit_after_credit`, `is_withdrawal_credit_success`, `is_withdrawal_awaiting_transfer`, `is_withdrawal_credit_failed`, `configuration_notification_type_id`, `created_at`, `updated_at`, `deleted_at`) VALUES
	(1, 1, '', 1, 1, 1, 1, 1, 1, 1, '2023-09-18 12:00:15', '2023-10-31 01:40:28', NULL);

CREATE TABLE IF NOT EXISTS `configuration_web` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `logo_url` varchar(255) DEFAULT '255',
  `web_name` varchar(50) DEFAULT NULL,
  `background_color` char(7) DEFAULT '#ffffff',
  `auto_user_approve_type_id` bigint DEFAULT NULL,
  `use_otp_register` tinyint(1) NOT NULL DEFAULT '0',
  `auto_withdraw_type_id` bigint DEFAULT NULL,
  `turn_withdraw_type_id` bigint DEFAULT NULL,
  `allow_online_registration` tinyint(1) NOT NULL DEFAULT '0',
  `minimum_deposit` bigint DEFAULT '0',
  `minimum_withdraw` bigint DEFAULT '0',
  `maximum_withdraw` bigint DEFAULT '0',
  `id_line` varchar(255) DEFAULT NULL,
  `url_line` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_auto_user_approve_type_id` (`auto_user_approve_type_id`),
  KEY `idx_auto_withdraw_type_id` (`auto_withdraw_type_id`),
  KEY `idx_turn_withdraw_type_id` (`turn_withdraw_type_id`),
  CONSTRAINT `configuration_web_ibfk_1` FOREIGN KEY (`auto_user_approve_type_id`) REFERENCES `auto_user_approve_type` (`id`),
  CONSTRAINT `configuration_web_ibfk_2` FOREIGN KEY (`auto_withdraw_type_id`) REFERENCES `auto_withdraw_type` (`id`),
  CONSTRAINT `configuration_web_ibfk_3` FOREIGN KEY (`turn_withdraw_type_id`) REFERENCES `turn_withdraw_type` (`id`)
);
INSERT INTO `configuration_web` (`id`, `logo_url`, `web_name`, `background_color`, `auto_user_approve_type_id`, `use_otp_register`, `auto_withdraw_type_id`, `turn_withdraw_type_id`, `allow_online_registration`, `minimum_deposit`, `minimum_withdraw`, `maximum_withdraw`, `id_line`, `url_line`, `created_at`, `updated_at`, `deleted_at`) VALUES
	(1, '', 'name', '#1a9900', 2, 0, 1, 1, 1, 1, 1, 50000, '', '', '2023-10-30 13:13:10', '2023-11-10 03:41:45', NULL);

CREATE TABLE IF NOT EXISTS `connection_status` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` enum('CONNECTED','DISCONNECTED') DEFAULT NULL,
  `label_th` varchar(255) DEFAULT NULL,
  `label_en` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
);
INSERT INTO `connection_status` (`id`, `name`, `label_th`, `label_en`, `created_at`, `updated_at`, `deleted_at`) VALUES
	(1, 'CONNECTED', 'เปิดใช้งาน', 'Active', '2023-09-18 12:00:13', '2023-10-04 10:20:58', NULL),
	(2, 'DISCONNECTED', 'ไม่ได้เชื่อมต่อ', 'disconnected', '2023-09-18 12:00:13', NULL, NULL);

CREATE TABLE IF NOT EXISTS `cron_action` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `action_key` varchar(255) DEFAULT NULL,
  `status` enum('PENDING','SUCCESS','FAILED') NOT NULL DEFAULT 'PENDING',
  `name` varchar(255) DEFAULT NULL,
  `remark` text,
  `unlock_at` datetime NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni_action_key` (`action_key`)
);

CREATE TABLE IF NOT EXISTS `issue_report` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `description` text,
  `issue_status_id` bigint DEFAULT NULL,
  `created_by_name` varchar(255) DEFAULT NULL,
  `approved_by_name` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `issue_status_id` (`issue_status_id`),
  CONSTRAINT `issue_report_ibfk_1` FOREIGN KEY (`issue_status_id`) REFERENCES `issue_status` (`id`),
  CONSTRAINT `issue_report_ibfk_2` FOREIGN KEY (`issue_status_id`) REFERENCES `issue_status` (`id`)
);

CREATE TABLE IF NOT EXISTS `issue_status` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` enum('RECEIVED','INQUEUE','PROCESSING','FIXED') NOT NULL DEFAULT 'RECEIVED',
  `label_th` varchar(255) DEFAULT NULL,
  `label_en` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
);

INSERT INTO `issue_status` (`id`, `name`, `label_th`, `label_en`) VALUES
	(1, 'RECEIVED', 'รับเรื่องแล้ว', 'received'),
	(2, 'INQUEUE', 'รอคิวแก้ไข', 'inqueue'),
	(3, 'PROCESSING', 'กำลังแก้ไข', 'processing'),
	(4, 'FIXED', 'เสร็จสิ้น', 'fixed');

CREATE TABLE IF NOT EXISTS `issue_web_report` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `issue_report_id` bigint DEFAULT NULL,
  `issue_web_url_id` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `issue_report_id` (`issue_report_id`),
  KEY `issue_web_url_id` (`issue_web_url_id`),
  CONSTRAINT `issue_web_report_ibfk_1` FOREIGN KEY (`issue_report_id`) REFERENCES `issue_report` (`id`),
  CONSTRAINT `issue_web_report_ibfk_2` FOREIGN KEY (`issue_web_url_id`) REFERENCES `issue_web_url` (`id`)
);

CREATE TABLE IF NOT EXISTS `issue_web_url` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `url` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
);
INSERT INTO `issue_web_url` (`url`) VALUES ('this web site');

CREATE TABLE IF NOT EXISTS `migrate_temp_mem_partner` (
  `mpart_id` bigint NOT NULL,
  `mirate_status_id` bigint DEFAULT '0',
  `site_id` bigint DEFAULT NULL,
  `mpart_user_id_mk` bigint DEFAULT NULL,
  `mpart_mem_id` bigint DEFAULT NULL,
  `mpart_mem_code` varchar(255) DEFAULT NULL,
  `mpart_codename` varchar(255) DEFAULT NULL,
  `mpart_com_sport` decimal(14,2) DEFAULT NULL,
  `mpart_com_casino` decimal(14,2) DEFAULT NULL,
  `mpart_com_game` decimal(14,2) DEFAULT NULL,
  `mpart_agent` int DEFAULT NULL,
  `mpart_sport` int DEFAULT NULL,
  `mpart_casino` int DEFAULT NULL,
  `mpart_game` int DEFAULT NULL,
  `mpart_bonus_free` int DEFAULT NULL,
  `mpart_bonus` int DEFAULT NULL,
  `mpart_tran_day` int DEFAULT NULL,
  `mpart_create` datetime DEFAULT NULL,
  `mpart_update` datetime DEFAULT NULL,
  PRIMARY KEY (`mpart_id`)
);

CREATE TABLE IF NOT EXISTS `migrate_temp_user` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `mirate_status_id` bigint DEFAULT '0',
  `member_code` varchar(255) DEFAULT NULL,
  `ref` varchar(20) DEFAULT NULL,
  `ref_by` bigint DEFAULT NULL,
  `username` varchar(255) DEFAULT NULL,
  `password` varchar(255) DEFAULT NULL,
  `phone` varchar(255) NOT NULL,
  `user_status_id` bigint DEFAULT '1',
  `user_type_id` bigint DEFAULT '1',
  `firstname` varchar(255) DEFAULT NULL,
  `lastname` varchar(255) DEFAULT NULL,
  `fullname` varchar(255) DEFAULT NULL,
  `credit` decimal(14,2) DEFAULT '0.00',
  `ip` varchar(20) DEFAULT NULL,
  `bank_id` int DEFAULT NULL,
  `bank_account` varchar(15) DEFAULT NULL,
  `channel_id` int DEFAULT NULL,
  `true_wallet` varchar(20) DEFAULT NULL,
  `contact` varchar(255) DEFAULT NULL,
  `note` varchar(255) DEFAULT NULL,
  `course` varchar(50) DEFAULT NULL,
  `line_id` varchar(30) DEFAULT NULL,
  `encrypt` varchar(255) DEFAULT NULL,
  `ip_registered` varchar(20) DEFAULT NULL,
  `verified_at` datetime DEFAULT NULL,
  `created_by` bigint DEFAULT NULL,
  `is_reset_password` tinyint DEFAULT '0',
  `logedin_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
);

CREATE TABLE IF NOT EXISTS `migration` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `version` bigint DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`)
);

CREATE TABLE IF NOT EXISTS `permission` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `permission_key` varchar(255) DEFAULT NULL,
  `main` tinyint DEFAULT '0',
  `name` varchar(255) DEFAULT NULL,
  `position` int DEFAULT NULL,
  `created_at` datetime DEFAULT (now()),
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni_permission_key` (`permission_key`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_deleted_at` (`deleted_at`),
  KEY `idx_position` (`position`)
);

INSERT INTO `permission` (`id`, `permission_key`, `main`, `name`, `position`, `created_at`, `updated_at`, `deleted_at`) VALUES
	(1, 'guide', 0, 'คู่มือการใช้งาน', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(2, 'admin', 1, 'ผู้ใช้งาน', NULL, '2023-09-18 12:00:10', '2023-10-17 07:30:51', NULL),
	(3, 'admin_manager', 0, 'จัดการผู้ใช้งาน', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(4, 'admin_group', 0, 'กลุ่มผู้ใช้งาน', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(5, 'summary', 1, 'สรุปภาพรวม', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(6, 'agent', 1, 'จัดการเว็บเอเย่น', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(7, 'agent_list', 0, 'รายการเว็บเอเย่น', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(8, 'agent_credit', 0, 'รายงานเพิ่ม-ลด เครดิต', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(9, 'bank', 1, 'จัดการธนาคาร', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(10, 'bank_list', 0, 'รายการธนาคาร', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(11, 'bank_transaction', 0, 'รายงานธุรกรรมเงินสด', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(12, 'bank_account', 0, 'รายการเดินบัญชีธนาคาร', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(13, 'promotion', 1, 'จัดการโปรโมชั่น', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(14, 'marketing_manage', 1, 'จัดการการตลาด', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(15, 'marketing_manage_link', 0, 'รายการลิ้งรับทรัพย์', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(16, 'marketing_manage_partner', 0, 'รายการพันธมิตร', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(17, 'activity_manage', 1, 'จัดการกิจกรรม', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(18, 'activity_manage_return', 0, 'คืนยอดเสีย', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(19, 'activity_manage_lucky', 0, 'กงล้อนำโชค', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(20, 'activity_manage_checkin', 0, 'เช็คอินรายวัน', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(21, 'activity_manage_coupon', 0, 'คูปองเงินสด', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(22, 'member', 1, 'จัดการสมาชิกเว็บ', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(23, 'member_list', 0, 'รายการสมาชิกเว็บ', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(24, 'member_transaction', 0, 'ประวัติฝาก-ถอนสมาชิก', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(25, 'member_channel', 0, 'ตั้งค่าช่องทางที่รู้จัก', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(26, 'member_history', 0, 'ประวัติการแก้ไขข้อมูล', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(27, 'member_misconduct', 0, 'รายการมิจฉาชีพ', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(28, 'report', 1, 'รายงาน', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(29, 'report_member', 0, 'ยอดสมาชิกผู้ใช้งาน', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(30, 'report_deposit', 0, 'ยอดฝาก-ถอน', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(31, 'report_deposittime', 0, 'จำนวนฝาก-ถอนตามเวลา', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(32, 'report_bonus', 0, 'รายงานการแจกโบนัส', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(33, 'report_membertime', 0, 'จำนวนสมาชิกนับเวลาบันทึก', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(34, 'report_memberchannel', 0, 'ยอดสมาชิกตามช่องทางที่รู้จัก', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(35, 'report_memberuser', 0, 'จำนวนบันทึกรายการตามผู้ใช้งาน', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(36, 'marketing_report', 1, 'รายงานการตลาด', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(37, 'marketing_report_return', 0, 'คืนยอดเสีย', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(38, 'marketing_report_link', 0, 'ลิงค์รับทรัพย์', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(39, 'marketing_report_partner', 0, 'พันธมิตร', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(40, 'winlose_report', 1, 'รายงานข้อมูล แพ้-ชนะ', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(41, 'activity_report', 1, 'รายงานกิจกรรม', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(42, 'activity_report_lucky', 0, 'กงล้อนำโชค', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(43, 'deposit_list', 1, 'รายการฝาก', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(44, 'withdraw_list', 1, 'รายการถอน', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(45, 'setting', 1, 'ตั้งค่าระบบ', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(46, 'setting_basic', 0, 'ข้อมูลเบื้องต้น', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(47, 'setting_line', 0, 'แจ้งเตือนกลุ่ม line', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(48, 'setting_sms', 0, 'PushMessage line', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(49, 'setting_cybernoti', 0, 'แจ้งเตือน Cyber Notify', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(50, 'status_update', 1, 'สถานะเรื่องแจ้งแก้ไข', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(51, 'invoice_notice', 1, 'ใบแจ้งหนี้', NULL, '2023-09-18 12:00:10', NULL, NULL),
	(52, 'bank_transfermoney', 0, 'โยกเงิน', NULL, '2023-10-08 08:04:29', NULL, NULL),
	(53, 'admin_history', 0, 'ประวัติการเข้าสู่ระบบผู้ใช้งาน', NULL, '2023-10-17 07:30:51', NULL, NULL),
	(54, 'promotion_list', 0, 'รายการโปรโมชั่น', NULL, '2023-10-17 07:30:51', NULL, NULL),
	(55, 'promotion_member', 0, 'โปรโมชั่นลูกค้า', NULL, '2023-10-17 07:30:51', NULL, NULL),
	(56, 'transfer_pending', 1, 'รายการโอนรอดำเนินการ', NULL, '2023-10-17 07:30:51', NULL, NULL),
	(57, 'deposit_withdrawal', 1, 'รายการฝาก-ถอน เสร็จสิ้น', NULL, '2023-10-17 07:30:51', NULL, NULL),
	(58, 'marketing_report_winlose', 0, 'รายงานข้อมูลแพ้ ชนะ', NULL, '2023-10-17 07:30:51', NULL, NULL),
	(59, 'marketing_report_wallet', 0, 'โยกเงินเข้ากระเป๋าหลัก', NULL, '2023-10-17 07:30:51', NULL, NULL),
	(60, 'marketing_report_balance', 0, 'ตรวจสอบยอดลิ้งค์แนะนำและพันธมิตร', NULL, '2023-10-17 07:30:51', NULL, NULL);

CREATE TABLE IF NOT EXISTS `play_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `player` varchar(255) DEFAULT '',
  `turn_sport` decimal(14,2) NOT NULL DEFAULT '0.00',
  `turn_casino` decimal(14,2) NOT NULL DEFAULT '0.00',
  `turn_game` decimal(14,2) NOT NULL DEFAULT '0.00',
  `win_lose_sport` decimal(14,2) NOT NULL DEFAULT '0.00',
  `win_lose_casino` decimal(14,2) NOT NULL DEFAULT '0.00',
  `win_lose_game` decimal(14,2) NOT NULL DEFAULT '0.00',
  `turn_total` decimal(14,2) DEFAULT '0.00',
  `win_lose_total` decimal(14,2) DEFAULT '0.00',
  `user_id` bigint NOT NULL,
  `date` varchar(10) DEFAULT '',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_date` (`date`)
);

CREATE TABLE IF NOT EXISTS `promotion_return_cut_type` (
  `id` bigint NOT NULL,
  `name` varchar(255) NOT NULL DEFAULT '',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);

INSERT INTO `promotion_return_cut_type` (`id`, `name`, `created_at`) VALUES
	(1, 'รายวัน', '2023-10-06 08:15:31');

CREATE TABLE IF NOT EXISTS `promotion_return_loser_status` (
  `id` bigint NOT NULL,
  `name` varchar(255) NOT NULL DEFAULT '',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);

INSERT INTO `promotion_return_loser_status` (`id`, `name`, `created_at`) VALUES
	(1, 'PENDING', '2023-10-09 06:52:18'),
	(2, 'READY', '2023-10-09 06:52:18'),
	(3, 'TAKEN', '2023-10-09 06:52:18'),
	(4, 'EXPIRED', '2023-10-09 06:52:18');

CREATE TABLE IF NOT EXISTS `promotion_return_loser_type` (
  `id` bigint NOT NULL,
  `name` varchar(255) NOT NULL DEFAULT '',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);

INSERT INTO `promotion_return_loser_type` (`id`, `name`, `created_at`) VALUES
	(1, 'คืนยอดเสียเมื่อยอดเสียเกิน', '2023-10-06 08:15:30');

CREATE TABLE IF NOT EXISTS `promotion_return_loser` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `status_id` bigint NOT NULL DEFAULT '1',
  `daily_key` varchar(255) NOT NULL DEFAULT '',
  `of_date` date NOT NULL,
  `total_loss_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `return_percent` decimal(10,2) NOT NULL DEFAULT '0.00',
  `return_type_id` bigint NOT NULL DEFAULT '1',
  `cut_type_id` bigint NOT NULL DEFAULT '1',
  `min_loss_price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `max_return_price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `credit_expire_days` int NOT NULL DEFAULT '0',
  `return_price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `calc_at` datetime DEFAULT NULL,
  `take_at` datetime DEFAULT NULL,
  `taken_price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `promotion_return_loser_daily_key_index` (`daily_key`),
  KEY `promotion_return_loser_status_id_index` (`status_id`),
  KEY `promotion_return_loser_user_id_index` (`user_id`)
);

CREATE TABLE IF NOT EXISTS `promotion_return_setting` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `return_percent` decimal(10,2) NOT NULL DEFAULT '0.00',
  `return_type_id` bigint NOT NULL DEFAULT '1',
  `cut_type_id` bigint NOT NULL DEFAULT '1',
  `min_loss_price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `max_return_price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `credit_expire_days` int NOT NULL DEFAULT '0',
  `detail` text NOT NULL,
  `is_enabled` tinyint NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);

INSERT INTO `promotion_return_setting` (`id`, `return_percent`, `return_type_id`, `cut_type_id`, `min_loss_price`, `max_return_price`, `credit_expire_days`, `detail`, `is_enabled`, `created_at`, `updated_at`) VALUES
	(1, 5.00, 1, 1, 500.00, 1000.00, 0, '<ol><li><span style="color: rgb(255, 255, 255); background-color: rgb(0, 0, 0);">* ลูกค้าต้องมียอดเสีย 500 บาท/วันขึ้นไป ถึงจะได้รับยอดเสีย 5% สูงสุด 1000 บาทต่อวัน</span></li><li><span style="color: rgb(255, 255, 255); background-color: rgb(0, 0, 0);">* ยอดเสียจะคิดตั้งแต่ 11.00 น.วันนี้ ถึง 11.00 น.ของพรุ่งนี้ค่ะ</span></li><li><span style="color: rgb(255, 255, 255); background-color: rgb(0, 0, 0);">* ระบบจะคืนยอดเสียให้อัตโนมัติ หลังจาก 14.30 น. ของทุกวัน&nbsp;</span></li></ol>', 0, '2023-10-08 03:52:49', '2023-10-31 07:01:45');

CREATE TABLE IF NOT EXISTS `promotion_web_bonus_type` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` enum('PERCENT','FIXED RATE') NOT NULL DEFAULT 'PERCENT',
  `label_th` varchar(255) DEFAULT NULL,
  `label_en` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
);

INSERT INTO `promotion_web_bonus_type` (`id`, `name`, `label_th`, `label_en`) VALUES
	(1, 'PERCENT', 'เปอร์เซ็นต์', 'percent'),
	(2, 'FIXED RATE', 'จำนวนเงิน', 'fixed rate');

CREATE TABLE IF NOT EXISTS `promotion_web_condition_type` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` enum('DAILY','WEEKLY','NOT_DEPOSITED_X_DAYS','DEPOSITED_X_TIMES','NEVER_DEPOSITED','MONTHLY','EVERY_X_DAYS','X_TIMES','DEPOSIT','IP_ADDRESS_X_TIMES','TURNOVER_FIXED','TURNOVER_RATE') NOT NULL DEFAULT 'DAILY',
  `transaction_type_id` bigint DEFAULT NULL,
  `label_th` varchar(255) DEFAULT NULL,
  `label_en` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `transaction_type_id` (`transaction_type_id`),
  CONSTRAINT `promotion_web_condition_type_ibfk_1` FOREIGN KEY (`transaction_type_id`) REFERENCES `transaction_type` (`id`)
);

INSERT INTO `promotion_web_condition_type` (`id`, `name`, `transaction_type_id`, `label_th`, `label_en`) VALUES
	(1, 'DAILY', 1, 'รายวัน', 'daily'),
	(2, 'WEEKLY', 1, 'รายสัปดาห์', 'weekly'),
	(3, 'NOT_DEPOSITED_X_DAYS', 1, 'ไม่ฝากเงินใน X วัน', 'not deposited x days'),
	(4, 'DEPOSITED_X_TIMES', 1, 'ฝากเงิน X ครั้ง', 'deposited x times'),
	(5, 'NEVER_DEPOSITED', 1, 'ไม่เคยฝากเงิน', 'never deposited'),
	(6, 'MONTHLY', 1, 'รายเดือน', 'monthly'),
	(7, 'EVERY_X_DAYS', 1, 'ทุก X วัน', 'every x days'),
	(8, 'X_TIMES', 1, 'X ครั้ง', 'x times'),
	(9, 'DEPOSIT', 1, 'ฝากเงิน', 'deposit'),
	(10, 'IP_ADDRESS_X_TIMES', 1, 'IP Address ซ้ำ X ครั้ง', 'ip address x times'),
	(11, 'TURNOVER_FIXED', 2, 'เทิร์นเท่ากับ X บาท', 'turnover fixed'),
	(12, 'TURNOVER_RATE', 2, 'เทิร์น X เท่า', 'turnover rate');

CREATE TABLE IF NOT EXISTS `promotion_web_condition` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `promotion_web_id` bigint NOT NULL,
  `transaction_type_id` bigint NOT NULL,
  `promotion_web_condition_type_id` bigint NOT NULL,
  `condition_amount` decimal(10,2) NOT NULL,
  `reset_at` datetime NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `promotion_web_condition_type_id` (`promotion_web_condition_type_id`),
  KEY `promotion_web_id` (`promotion_web_id`),
  KEY `transaction_type_id` (`transaction_type_id`),
  CONSTRAINT `promotion_web_condition_ibfk_1` FOREIGN KEY (`promotion_web_id`) REFERENCES `promotion_web` (`id`),
  CONSTRAINT `promotion_web_condition_ibfk_2` FOREIGN KEY (`transaction_type_id`) REFERENCES `transaction_type` (`id`),
  CONSTRAINT `promotion_web_condition_ibfk_3` FOREIGN KEY (`promotion_web_condition_type_id`) REFERENCES `promotion_web_condition_type` (`id`)
);

CREATE TABLE IF NOT EXISTS `promotion_web_type` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` enum('DEPOSIT','CUSTOMIZED') NOT NULL DEFAULT 'DEPOSIT',
  `label_th` varchar(255) DEFAULT NULL,
  `label_en` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
);

INSERT INTO `promotion_web_type` (`id`, `name`, `label_th`, `label_en`) VALUES
	(1, 'DEPOSIT', 'ฝากเงิน', 'deposit'),
	(2, 'CUSTOMIZED', 'กำหนดเอง', 'customized');

CREATE TABLE IF NOT EXISTS `promotion_web` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `promotion_web_type_id` bigint NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text,
  `start_date` datetime NOT NULL,
  `end_date` datetime NOT NULL,
  `image_url` varchar(255) DEFAULT NULL,
  `promotion_web_bonus_type_id` bigint NOT NULL,
  `bonus_amount` decimal(10,2) NOT NULL,
  `show_banner` int DEFAULT '0',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `promotion_web_bonus_type_id` (`promotion_web_bonus_type_id`),
  KEY `promotion_web_type_id` (`promotion_web_type_id`),
  CONSTRAINT `promotion_web_ibfk_1` FOREIGN KEY (`promotion_web_type_id`) REFERENCES `promotion_web_type` (`id`),
  CONSTRAINT `promotion_web_ibfk_2` FOREIGN KEY (`promotion_web_bonus_type_id`) REFERENCES `promotion_web_bonus_type` (`id`)
);

CREATE TABLE IF NOT EXISTS `recommend_channel` (
  `id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(255) DEFAULT '255',
  `status` enum('ACTIVE','DEACTIVE') DEFAULT 'ACTIVE',
  `url` varchar(255) DEFAULT '255',
  `created_at` datetime DEFAULT (now()),
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);

INSERT INTO `recommend_channel` (`id`, `title`, `status`, `url`, `created_at`, `updated_at`) VALUES
	(1, 'ยูทูบ', 'ACTIVE', 'youtbe.com', '2023-09-15 03:50:18', '2023-10-05 04:37:19'),
	(2, 'เฟส', 'DEACTIVE', 'facebook.com', '2023-09-15 04:05:12', '2023-10-05 04:17:28'),
	(3, 'ไลน์', 'ACTIVE', 'line.me', '2023-09-15 04:05:31', '2023-10-05 04:37:24'),
	(7, 'FaceBook', 'ACTIVE', 'www.facebook.com', '2023-10-05 04:16:14', '2023-10-31 03:56:30');

CREATE TABLE IF NOT EXISTS `register_bonus_option` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` enum('CREDIT','DEPOSIT_PERCENT') NOT NULL,
  `label_en` varchar(255) DEFAULT NULL,
  `label_th` varchar(255) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);

INSERT INTO `register_bonus_option` (`id`, `name`, `label_en`, `label_th`, `created_at`) VALUES
	(3, 'CREDIT', 'Credit', 'เครดิต', '2023-10-08 10:11:24'),
	(4, 'DEPOSIT_PERCENT', 'Percentage of the first deposit amount', 'เปอร์เซ็นยอดฝากครั้งแรก', '2023-10-08 10:11:24');

CREATE TABLE IF NOT EXISTS `register_bonus_type` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` enum('FIRST_DEPOSIT') NOT NULL,
  `label_en` varchar(255) DEFAULT NULL,
  `label_th` varchar(255) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);

INSERT INTO `register_bonus_type` (`id`, `name`, `label_en`, `label_th`, `created_at`) VALUES
	(1, 'FIRST_DEPOSIT', 'First Deposit', 'ฝากครั้งแรก', '2023-10-08 10:05:15');

CREATE TABLE IF NOT EXISTS `scammer` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `reason` varchar(255) DEFAULT NULL,
  `user_id` bigint DEFAULT NULL,
  `scammer_created_by` bigint DEFAULT NULL,
  `scammer_updated_by` bigint DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `scammer_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
);

CREATE TABLE IF NOT EXISTS `statement_status` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` enum('IGNORED','PENDING','CONFIRMED') DEFAULT NULL,
  `label_th` varchar(255) DEFAULT NULL,
  `label_en` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
);
INSERT INTO `statement_status` (`id`, `name`, `label_th`, `label_en`, `created_at`, `updated_at`, `deleted_at`) VALUES
	(1, 'PENDING', 'รอตรวจสอบ', 'pending', '2023-09-19 03:33:10', '2023-09-19 03:37:18', NULL),
	(2, 'CONFIRMED', 'ตรวจสอบแล้ว', 'confirmed', '2023-09-19 03:33:10', '2023-09-19 03:38:13', NULL),
	(3, 'IGNORED', 'ไม่สนใจ', 'ignored', '2023-09-19 03:33:10', '2023-09-19 03:38:30', NULL);

CREATE TABLE IF NOT EXISTS `statement_type` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` enum('TRANSFER_IN','TRANSFER_OUT') DEFAULT NULL,
  `label_th` varchar(255) DEFAULT NULL,
  `label_en` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
);
INSERT INTO `statement_type` (`id`, `name`, `label_th`, `label_en`, `created_at`, `updated_at`, `deleted_at`) VALUES
	(1, 'TRANSFER_IN', 'โอนเข้า', 'transfer_in', '2023-09-19 02:39:23', NULL, NULL),
	(2, 'TRANSFER_OUT', 'โอนออก', 'transfer_out', '2023-09-19 02:39:23', NULL, NULL);

CREATE TABLE IF NOT EXISTS `transaction_status` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` enum('PENDING','DEPOSIT_PENDING_CREDIT','DEPOSIT_PENDING_SLIP','DEPOSIT_PENDING_MULTIUSER','DEPOSIT_CREDIT_APPROVED_GAME','DEPOSIT_CREDIT_REJECTED_GAME','WITHDRAW_PENDING','WITHDRAW_OVER_BUDGET','WITHDRAW_APPROVED','WITHDRAW_REJECTED','WITHDRAW_FAILED','WITHDRAW_SUCCESS','WITHDRAW_OVER_MAX','WITHDRAW_CANCELED','DEPOSIT_IGNORE') NOT NULL DEFAULT 'PENDING',
  `label_th` varchar(255) DEFAULT NULL,
  `label_en` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
);
INSERT INTO `transaction_status` (`id`, `name`, `label_th`, `label_en`, `created_at`, `updated_at`, `deleted_at`) VALUES
	(1, 'PENDING', 'รอตรวจสอบและดำเนินการ', 'pending', '2023-10-10 04:11:48', NULL, NULL),
	(2, 'DEPOSIT_PENDING_CREDIT', 'รออนุมัติเครดิต', 'pending credit', '2023-10-10 04:11:48', NULL, NULL),
	(3, 'DEPOSIT_PENDING_SLIP', 'รายการแจ้งฝาก', 'pending slip', '2023-10-10 04:11:48', NULL, NULL),
	(4, 'DEPOSIT_PENDING_MULTIUSER', 'เลขบัญชีซ้ำ', 'pending multiuser', '2023-10-10 04:11:48', NULL, NULL),
	(5, 'DEPOSIT_CREDIT_APPROVED_GAME', 'ฝากสำเร็จ', 'credit approved game', '2023-10-10 04:11:48', NULL, NULL),
	(6, 'DEPOSIT_CREDIT_REJECTED_GAME', 'ฝากไม่สำเร็จ', 'credit rejected game', '2023-10-10 04:11:48', NULL, NULL),
	(7, 'WITHDRAW_PENDING', 'รอตรวจสอบและดำเนินการ', 'transaction withdraw pending', '2023-10-10 04:11:48', NULL, NULL),
	(8, 'WITHDRAW_OVER_BUDGET', 'แจ้งถอนวงเงินสูง', 'trans status withdraw over budget', '2023-10-10 04:11:48', NULL, NULL),
	(9, 'WITHDRAW_APPROVED', 'ยืนยันการแจ้งถอน', 'withdraw approved', '2023-10-10 04:11:48', NULL, NULL),
	(10, 'WITHDRAW_REJECTED', 'แจ้งถอนไม่สำเร็จ', 'withdraw rejected', '2023-10-10 04:11:48', NULL, NULL),
	(11, 'WITHDRAW_FAILED', 'ถอนจากระบบไม่สำเร็จ', 'withdraw failed', '2023-10-10 04:11:48', NULL, NULL),
	(12, 'WITHDRAW_SUCCESS', 'ถอนสำเร็จ', 'withdraw success', '2023-10-10 04:11:48', NULL, NULL),
	(13, 'WITHDRAW_OVER_MAX', 'ถอนถอนวงเงินสูง', 'withdraw over max', '2023-10-10 04:11:48', NULL, NULL),
	(14, 'WITHDRAW_CANCELED', 'ยกเลิกรายการถอน', 'withdraw canceled', '2023-10-10 04:11:48', NULL, NULL),
	(15, 'DEPOSIT_IGNORE', 'เพิกเฉย', 'deposit ignore', '2023-10-10 04:11:48', NULL, NULL);

CREATE TABLE IF NOT EXISTS `transaction_type` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `label_th` varchar(255) DEFAULT NULL,
  `label_en` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
);
INSERT INTO `transaction_type` (`id`, `name`, `label_th`, `label_en`, `created_at`, `updated_at`, `deleted_at`) VALUES
	(1, 'DEPOSIT', 'ฝาก', 'deposit', '2023-09-18 12:00:13', NULL, NULL),
	(2, 'WITHDRAW', 'ถอน', 'withdraw', '2023-09-18 12:00:13', NULL, NULL),
	(3, 'BONUS', 'โบนัส', 'bonus', '2023-09-18 12:00:13', NULL, NULL),
	(4, 'GETCREDITBACK', 'ดึงเครดิตกลับ', 'getcreditback', '2023-09-18 12:00:13', NULL, NULL);

CREATE TABLE IF NOT EXISTS `turn_withdraw_type` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `label_th` varchar(255) DEFAULT NULL,
  `label_en` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
);
INSERT INTO `turn_withdraw_type` (`id`, `name`, `label_th`, `label_en`, `created_at`, `updated_at`, `deleted_at`) VALUES
	(1, 'turn off', 'ปิดการใช้งาน', 'Turn off', '2023-09-18 12:00:15', NULL, NULL),
	(2, 'count 1 turn', 'นับเทิร์น 1 เท่า', 'Check trend, withdraw after count 1 turn', '2023-09-18 12:00:15', NULL, NULL);

CREATE TABLE IF NOT EXISTS `turnover_statement_status` (
  `id` bigint NOT NULL,
  `name` varchar(255) NOT NULL DEFAULT '',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
INSERT INTO `turnover_statement_status` (`id`, `name`, `created_at`) VALUES
	(1, 'PENDING', '2023-10-09 08:52:45'),
	(2, 'CANCELED', '2023-10-09 08:52:45'),
	(3, 'COMPLETED', '2023-10-09 08:52:45'),
	(4, 'EXPIRED', '2023-10-09 08:52:45');

CREATE TABLE IF NOT EXISTS `turnover_statement_type` (
  `id` bigint NOT NULL,
  `name` varchar(255) NOT NULL DEFAULT '',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
INSERT INTO `turnover_statement_type` (`id`, `name`, `created_at`) VALUES
	(1, 'PROMOTION_FIRST_DEPOSIT', '2023-10-09 08:52:44'),
	(2, 'PROMOTION_RETURN_LOSS', '2023-10-09 08:52:44');

CREATE TABLE IF NOT EXISTS `turnover_statement` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `type_id` bigint NOT NULL DEFAULT '1',
  `ref_type_id` bigint DEFAULT NULL,
  `promotion_name` varchar(255) NOT NULL,
  `bonus_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `status_id` bigint NOT NULL DEFAULT '1',
  `start_turn_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `start_turn_at` datetime DEFAULT NULL,
  `total_turn_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `end_turn_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `turnover_statement_status_id_index` (`status_id`),
  KEY `turnover_statement_user_id_index` (`user_id`)
);

CREATE TABLE IF NOT EXISTS `turnover_withdraw_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `log_key` varchar(255) NOT NULL DEFAULT '',
  `total_withdraw_price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `current_turn` decimal(10,2) NOT NULL DEFAULT '0.00',
  `play_total` decimal(10,2) NOT NULL DEFAULT '0.00',
  `last_play_y` decimal(10,2) NOT NULL DEFAULT '0.00',
  `last_total_x` decimal(10,2) NOT NULL DEFAULT '0.00',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_turnover_withdraw_log_log_key` (`log_key`),
  KEY `turnover_withdraw_log_user_id_index` (`user_id`)
);

CREATE TABLE IF NOT EXISTS `user_affiliate` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `commission_total` decimal(14,2) DEFAULT '0.00',
  `commission_current` decimal(14,2) DEFAULT '0.00',
  `first_deposit_bonus` decimal(14,2) DEFAULT '0.00',
  `bonus_share_total` decimal(14,2) DEFAULT '0.00',
  `commission_sport` decimal(14,2) DEFAULT '0.00',
  `commission_casino` decimal(14,2) DEFAULT '0.00',
  `commission_game` decimal(14,2) DEFAULT '0.00',
  `link_click_total` int DEFAULT '0',
  `member_total` int DEFAULT '0',
  `member_deposit_total` int DEFAULT '0',
  `play_balance` decimal(14,2) DEFAULT '0.00',
  `received_balance` decimal(14,2) DEFAULT '0.00',
  `user_id` bigint NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_affiliate_user_id_fk` (`user_id`),
  CONSTRAINT `user_affiliate_user_id_fk` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
);

CREATE TABLE IF NOT EXISTS `user_alliance` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `alias` varchar(255) NOT NULL DEFAULT '',
  `first_deposit` int NOT NULL DEFAULT '0',
  `link_click_total` int NOT NULL DEFAULT '0',
  `member_register_today` int NOT NULL DEFAULT '0',
  `member_online_today` int DEFAULT '0',
  `no_member_code_total` int NOT NULL DEFAULT '0',
  `have_member_code_total` int NOT NULL DEFAULT '0',
  `recommend_total` int NOT NULL DEFAULT '0',
  `referral_bonus` decimal(14,2) DEFAULT '0.00',
  `description` text,
  `alliance_percent` decimal(14,2) NOT NULL DEFAULT '0.00',
  `commission_percent` decimal(14,2) NOT NULL DEFAULT '0.00',
  `user_id` bigint NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_alliance_user_id_fk` (`user_id`),
  CONSTRAINT `user_alliance_user_id_fk` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
);

CREATE TABLE IF NOT EXISTS `user_login_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint DEFAULT NULL,
  `ip` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT (now()),
  PRIMARY KEY (`id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_user_id` (`user_id`)
);

CREATE TABLE IF NOT EXISTS `user_otp` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `ref` varchar(255) NOT NULL,
  `type` varchar(255) NOT NULL,
  `ants_otp_id` varchar(255) NOT NULL,
  `user_id` bigint DEFAULT NULL,
  `created_at` datetime DEFAULT (now()),
  `verified_at` datetime DEFAULT NULL,
  `expired_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_ants_otp_id` (`ants_otp_id`),
  KEY `idx_ref` (`ref`),
  KEY `idx_type` (`type`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_verified_at` (`verified_at`)
);

CREATE TABLE IF NOT EXISTS `user_status` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` enum('ACTIVE','DEACTIVE') DEFAULT NULL,
  `label_th` varchar(255) DEFAULT NULL,
  `label_en` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
);
INSERT INTO `user_status` (`id`, `name`, `label_th`, `label_en`) VALUES
	(1, 'ACTIVE', 'ผู้ใช้งาน ใช้งาน', 'active'),
	(2, 'DEACTIVE', 'โดนงับการใช้งาน', 'deactive');

CREATE TABLE IF NOT EXISTS `user_transaction_direction` (
  `id` bigint NOT NULL,
  `name` varchar(255) NOT NULL DEFAULT '',
  `detail` varchar(255) NOT NULL DEFAULT '',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
INSERT INTO `user_transaction_direction` (`id`, `name`, `detail`, `created_at`) VALUES
	(1, 'DEPOSIT', 'ฝาก', '2023-10-20 06:27:30'),
	(2, 'WITHDRAW', 'ถอน', '2023-10-20 06:27:30');

CREATE TABLE IF NOT EXISTS `user_transaction_type` (
  `id` bigint NOT NULL,
  `name` varchar(255) NOT NULL DEFAULT '',
  `detail` varchar(255) NOT NULL DEFAULT '',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
INSERT INTO `user_transaction_type` (`id`, `name`, `detail`, `created_at`) VALUES
	(1, 'DEPOSIT', 'ฝาก', '2023-10-19 08:06:44'),
	(2, 'WITHDRAW', 'ถอน', '2023-10-19 08:06:44'),
	(3, 'BONUS', 'โบนัส', '2023-10-19 08:06:44'),
	(4, 'PROMOTION_RETURN_LOSS', 'แจกโบนัสฟรี คืนยอดเสีย', '2023-10-19 08:06:44'),
	(5, 'AFFILIATE_INCOME', 'โบนัสรายได้แนะนำเพื่อน', '2023-10-19 08:06:44'),
	(6, 'ALLIANCE_INCOME', 'โบนัสรายได้พันธมิตร', '2023-10-19 08:06:44'),
	(7, 'TAKE_CREDIT_BACK', 'ดึงเครดิตกลับ', '2023-10-20 09:00:02');

CREATE TABLE IF NOT EXISTS `user_transaction` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `direction_id` bigint NOT NULL DEFAULT '1',
  `promotion_id` bigint DEFAULT NULL,
  `type_id` bigint NOT NULL,
  `account_id` bigint DEFAULT NULL,
  `ref_id` bigint DEFAULT NULL,
  `detail` varchar(255) NOT NULL DEFAULT '',
  `credit_before` decimal(10,2) NOT NULL DEFAULT '0.00',
  `credit_back` decimal(10,2) NOT NULL DEFAULT '0.00',
  `credit_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `bonus_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `credit_after` decimal(10,2) NOT NULL DEFAULT '0.00',
  `transfer_at` datetime DEFAULT NULL,
  `create_admin_id` bigint DEFAULT NULL,
  `confirm_admin_id` bigint DEFAULT NULL,
  `is_adjust_auto` tinyint NOT NULL DEFAULT '0',
  `is_show` tinyint NOT NULL DEFAULT '1',
  `work_seconds` int NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `removed_at` datetime DEFAULT NULL,
  `remove_admin_id` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_credit_transaction_type_id_index` (`type_id`),
  KEY `user_credit_transaction_user_id_index` (`user_id`)
);

CREATE TABLE IF NOT EXISTS `user_type` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` enum('NONE','AFFILIATE','ALLIANCE') DEFAULT NULL,
  `label_th` varchar(255) DEFAULT NULL,
  `label_en` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
);
INSERT INTO `user_type` (`id`, `name`, `label_th`, `label_en`) VALUES
	(1, 'NONE', 'ไม่มี', 'none'),
	(2, 'AFFILIATE', 'ลิงค์รับทรัพย์', 'affiliate'),
	(3, 'ALLIANCE', 'พันธมิตร', 'alliance');

CREATE TABLE IF NOT EXISTS `user_update_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `created_by_username` varchar(255) DEFAULT NULL,
  `ip` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT (now()),
  PRIMARY KEY (`id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_user_id` (`user_id`)
);

CREATE TABLE IF NOT EXISTS `user` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `member_code` varchar(255) DEFAULT NULL,
  `ref_by` bigint DEFAULT NULL,
  `username` varchar(255) DEFAULT NULL,
  `password` varchar(255) DEFAULT NULL,
  `phone` varchar(255) NOT NULL,
  `user_status_id` bigint NOT NULL DEFAULT '1',
  `user_type_id` bigint NOT NULL DEFAULT '1',
  `firstname` varchar(255) DEFAULT NULL,
  `lastname` varchar(255) DEFAULT NULL,
  `fullname` varchar(255) DEFAULT NULL,
  `credit` decimal(14,2) DEFAULT '0.00',
  `ip` varchar(20) DEFAULT NULL,
  `bank_account` varchar(15) DEFAULT NULL,
  `channel_id` int DEFAULT NULL,
  `true_wallet` varchar(20) DEFAULT NULL,
  `contact` varchar(255) DEFAULT NULL,
  `note` varchar(255) DEFAULT NULL,
  `course` varchar(50) DEFAULT NULL,
  `line_id` varchar(30) DEFAULT NULL,
  `encrypt` varchar(255) DEFAULT NULL,
  `ip_registered` varchar(20) DEFAULT NULL,
  `verified_at` datetime DEFAULT NULL,
  `bank_id` int DEFAULT NULL,
  `created_by` int DEFAULT NULL,
  `is_reset_password` tinyint DEFAULT '0',
  `logedin_at` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT (now()),
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_bank_id` (`bank_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_deleted_at` (`deleted_at`),
  KEY `idx_member_code` (`member_code`),
  KEY `idx_phone` (`phone`),
  KEY `idx_ref_by` (`ref_by`),
  KEY `idx_verified_at` (`verified_at`),
  KEY `user_status_id` (`user_status_id`),
  KEY `user_type_id` (`user_type_id`),
  CONSTRAINT `user_ibfk_1` FOREIGN KEY (`user_status_id`) REFERENCES `user_status` (`id`),
  CONSTRAINT `user_ibfk_2` FOREIGN KEY (`user_type_id`) REFERENCES `user_type` (`id`)
);

CREATE TABLE IF NOT EXISTS `webhook_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `json_request` text NOT NULL,
  `json_payload` text NOT NULL,
  `log_type` varchar(255) NOT NULL,
  `status` varchar(255) NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
);

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;

ALTER TABLE `admin` DROP INDEX `uni_email`;

CREATE TABLE affiliate_link_click (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    ref_by BIGINT NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX idx_ref_by ON affiliate_link_click (ref_by);

CREATE TABLE alliance_income_withdraw_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    ref_user_id BIGINT NOT NULL,
    income_amount DECIMAL(14,2) NOT NULL DEFAULT 0.00,
    from_date VARCHAR(11) NOT NULL,
    to_date VARCHAR(11) NOT NULL,
    created_by BIGINT NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX idx_ref_user_id ON alliance_income_withdraw_log (ref_user_id);

ALTER TABLE `user`
	DROP INDEX `idx_phone`,
	ADD UNIQUE INDEX `uni_phone` (`phone`) USING BTREE;

CREATE TABLE user_income_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    type_id BIGINT NOT NULL,
    ref_id BIGINT NULL DEFAULT NULL,
    detail VARCHAR(255) NOT NULL DEFAULT '',
    credit_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
    credit_after DECIMAL(10,2) NOT NULL DEFAULT 0,
    transfer_at DATETIME NULL DEFAULT NULL,
    status_id BIGINT NOT NULL DEFAULT 0,
    create_by_name VARCHAR(255) NOT NULL DEFAULT '',
    confirm_by_name VARCHAR(255) NOT NULL DEFAULT '',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
CREATE INDEX user_income_log_user_id_index ON user_income_log (user_id);
CREATE INDEX user_income_log_type_id_index ON user_income_log (type_id);
CREATE INDEX user_income_log_ref_id_index ON user_income_log (ref_id);
CREATE INDEX user_income_log_status_id_index ON user_income_log (status_id);

CREATE TABLE user_income_log_type (
    id BIGINT PRIMARY KEY,
    name VARCHAR(255) NOT NULL DEFAULT '',
    detail VARCHAR(255) NOT NULL DEFAULT '',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

INSERT INTO `user_income_log_type` (`id`,`name`,`detail`) VALUES
    (1,'AFFILIATE', 'แนะนำเพื่อน'),
    (2,'PROMOTION_RETURN_LOSS', 'คืนยอดเสีย'),
    (3,'ALLIANCE', 'รายได้พันธมิตร');

CREATE TABLE user_income_log_status (
    id BIGINT PRIMARY KEY,
    name VARCHAR(255) NOT NULL DEFAULT '',
    detail VARCHAR(255) NOT NULL DEFAULT '',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

INSERT INTO `user_income_log_status` (`id`,`name`,`detail`) VALUES
    (1,'READY', 'รอกดรับ'),
    (2,'COMPLETED', 'สำเร็จ');

ALTER TABLE `user`
	ADD COLUMN `last_action_at` DATETIME NULL DEFAULT NULL AFTER `verified_at`;

UPDATE `user` SET `updated_at`= `created_at` WHERE updated_at IS NULL;
UPDATE `user` SET `last_action_at`= `updated_at` WHERE last_action_at IS NULL;

CREATE INDEX `idx_user_id` ON `play_log` (`user_id`);
ALTER TABLE `play_log`
	ADD COLUMN `valid_amount_sport` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `win_lose_game`,
	ADD COLUMN `valid_amount_casino` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `valid_amount_sport`,
	ADD COLUMN `valid_amount_game` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `valid_amount_casino`,
	ADD COLUMN `valid_amount_total` DECIMAL(14,2) NULL DEFAULT '0.00' AFTER `win_lose_total`;

DELETE FROM `recommend_channel` WHERE `id` < 100;
INSERT INTO `recommend_channel` (`id`, `title`, `status`, `url`) VALUES
	(1, 'ยูทูบ', 'ACTIVE', 'www.youtube.com'),
	(3, 'ไลน์', 'ACTIVE', 'line.me'),
	(7, 'FaceBook', 'ACTIVE', 'www.facebook.com'),
	(8, 'อื่นๆ', 'ACTIVE', 'other'),
	(9, 'Google', 'ACTIVE', 'google'),
	(10, 'เพื่อนแนะนำมา', 'ACTIVE', 'friends'),
	(11, 'พันธมิตร', 'ACTIVE', 'alliance'),
	(12, 'Tiktok', 'ACTIVE', 'tiktok'),
	(13, 'FbLive', 'ACTIVE', 'fblive');

-- ---------------configuration_web-------------------------
ALTER TABLE `configuration_web` DROP FOREIGN KEY `configuration_web_ibfk_2`;
ALTER TABLE `configuration_web` DROP COLUMN `auto_withdraw_type_id`;
ALTER TABLE `configuration_web` DROP COLUMN `minimum_withdraw`;
ALTER TABLE `configuration_web` DROP COLUMN `maximum_withdraw`;

-- --------------auto_withdraw_type_id--------------------
ALTER TABLE `bank_account` ADD COLUMN `auto_withdraw_type_id` BIGINT(19) NOT NULL DEFAULT 1 AFTER `account_priority_withdraw`;
ALTER TABLE `bank_account` ADD FOREIGN KEY (`auto_withdraw_type_id`) REFERENCES `auto_withdraw_type` (`id`);
ALTER TABLE `bank_account` ADD COLUMN `bank_withdraw_maximum` DECIMAL(10,2) NOT NULL DEFAULT 50000.00 AFTER `account_priority_withdraw`;
ALTER TABLE `bank_account` ADD COLUMN `auto_withdraw_maximum` DECIMAL(10,2) NOT NULL DEFAULT 25000.00 AFTER `bank_withdraw_maximum`;

-- line notify change ให้เป็น 1 เเพราะ ปกติ จะเปิดให้ตลอด ไม่สามารถปิดได้
ALTER TABLE `configuration_notification`
ADD COLUMN `is_deposit_bonus` tinyint(1) NOT NULL DEFAULT '1' AFTER `is_withdrawal_credit_failed`;
ALTER TABLE `configuration_notification`
ADD COLUMN `is_pull_credit_back` tinyint(1) NOT NULL DEFAULT '1' AFTER `is_deposit_bonus`;


-- BOF OF V1.2.0 --
CREATE TABLE IF NOT EXISTS `alliance_winlose_income` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `status_id` bigint NOT NULL DEFAULT '1',
  `daily_key` varchar(255) NOT NULL DEFAULT '',
  `statement_date` DATE NOT NULL,
  `of_date` DATE NOT NULL,
  `total_play_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `total_win_lose_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `commission_percent` decimal(10,2) NOT NULL DEFAULT '0.00',
  `alliance_percent` decimal(10,2) NOT NULL DEFAULT '0.00',
  `total_commission` decimal(10,2) NOT NULL DEFAULT '0.00',
  `alliance_winlose_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `alliance_commission` decimal(10,2) NOT NULL DEFAULT '0.00',
  `total_paid_bonus` decimal(10,2) NOT NULL DEFAULT '0.00',
  `alliance_paid_bonus` decimal(10,2) NOT NULL DEFAULT '0.00',
  `alliance_income` decimal(10,2) NOT NULL DEFAULT '0.00',
  `take_at` datetime DEFAULT NULL,
  `taken_price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
CREATE INDEX `idx_user_id` ON `alliance_winlose_income` (`user_id`);
CREATE INDEX `idx_status_id` ON `alliance_winlose_income` (`status_id`);
CREATE UNIQUE INDEX `uni_daily_key` ON `alliance_winlose_income` (`daily_key`);

CREATE TABLE IF NOT EXISTS `marketing_config` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT,
  `config_key` varchar(255) NOT NULL,
  `config_val` varchar(255) NOT NULL,
  `deleted_at` datetime DEFAULT NULL
);

ALTER TABLE `user_income_log`
	ADD COLUMN `create_by` BIGINT(19) NOT NULL DEFAULT '0' AFTER `status_id`,
	ADD COLUMN `confirm_by` BIGINT(19) NULL DEFAULT NULL AFTER `create_by`;

UPDATE `user_income_log_status` SET `name`='PENDING', `detail`='รออนุมัติ' WHERE `id`=1;


UPDATE `configuration_web` SET `web_name`=@DOMAIN_NAME WHERE  `id`=1;
UPDATE `issue_web_url` SET `url`=@DOMAIN_NAME WHERE  `id`=1;
UPDATE `agent_info` SET `total`=1000000 WHERE  `id`=1;

-- --- HOT FIX 2023-11-29 ------

ALTER TABLE `bank_account`
	ADD COLUMN `is_show_front` TINYINT NOT NULL DEFAULT 0 AFTER `account_type_id`;

-- --- HOT FIX 2023-11-30 ------

ALTER TABLE `configuration_web` ADD COLUMN `minimum_withdraw` DECIMAL(14,2) NULL DEFAULT '1' AFTER `minimum_deposit`;

CREATE TABLE IF NOT EXISTS `user_playlog_status` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `statement_date` VARCHAR(10) DEFAULT NULL,
  `path` VARCHAR(255) NOT NULL,
  `page` INT DEFAULT '1',
  `status_id` INT DEFAULT '1',
  `out_message` VARCHAR(255),
  `out_total` BIGINT,
  `out_json_error` TEXT,
  `out_json_summary` TEXT,
  `out_target_url` VARCHAR(255),
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
CREATE INDEX `path` ON `user_playlog_status` (`path`);
CREATE INDEX `statement_date` ON `user_playlog_status` (`statement_date`);

CREATE TABLE IF NOT EXISTS `user_playlog` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `user_id` BIGINT NOT NULL,
  `statement_date` VARCHAR(10) DEFAULT NULL,
  `daily_key` VARCHAR(255) DEFAULT NULL,
  `turn_sport` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `valid_amount_sport` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `win_lose_sport` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `turn_casino` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `valid_amount_casino` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `win_lose_casino` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `turn_game` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `valid_amount_game` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `win_lose_game` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `turn_total` DECIMAL(14,2) DEFAULT '0.00',
  `win_lose_total` DECIMAL(14,2) DEFAULT '0.00',
  `valid_amount_total` DECIMAL(14,2) DEFAULT '0.00',
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
CREATE INDEX `user_id` ON `user_playlog` (`user_id`);
CREATE INDEX `statement_date` ON `user_playlog` (`statement_date`);
CREATE UNIQUE INDEX `daily_key` ON `user_playlog` (`daily_key`);

-- --- HOT FIX 2023-12-01 ------

ALTER TABLE `user_otp`
	ADD COLUMN `phone` VARCHAR(50) NOT NULL DEFAULT '' AFTER `id`;
ALTER TABLE `user_otp`
	ADD COLUMN `local_pin` VARCHAR(10) NULL DEFAULT NULL AFTER `user_id`;

INSERT INTO `permission` (`permission_key`, `main`, `name`) VALUES ('member_edit', 1, 'เพิ่ม แก้ไข ข้อมูลสมาชิกเว็บ');
INSERT INTO `permission` (`permission_key`, `main`, `name`) VALUES ('member_delete', 1, 'ลบ ข้อมูลสมาชิกเว็บ');
INSERT INTO `permission` (`permission_key`, `main`, `name`) VALUES ('marketing_manage_edit', 1, 'เพิ่ม แก้ไข ลบ ข้อมูลจัดการการตลาด');
INSERT INTO `permission` (`permission_key`, `main`, `name`) VALUES ('scammer_cancel', 1, 'ยกเลิกมิจฉาชีพ');

ALTER TABLE `user`
	CHANGE COLUMN `bank_account` `bank_account` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci' AFTER `ip`;

CREATE TABLE IF NOT EXISTS `renewal_web_master` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT,
  `name` varchar(255) NOT NULL DEFAULT '',
  `web_domain` varchar(255) NOT NULL DEFAULT '',
  `api_key` varchar(255) NOT NULL DEFAULT '',
  `payment_detail` varchar(255) NOT NULL DEFAULT '',
  `current_web_package_id` bigint NULL DEFAULT NULL,
  `web_expired_date` DATE NOT NULL,
  `current_sms_package_id` bigint NULL DEFAULT NULL,
  `sms_credit_balance` bigint NOT NULL DEFAULT 0,
  `sms_expired_date` DATE NOT NULL,
  `current_fastbank_package_id` bigint NULL DEFAULT NULL,
  `fastbank_credit_balance` bigint NOT NULL DEFAULT 0,
  `fastbank_expired_date` DATE NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL
);
CREATE UNIQUE INDEX `uni_web_domain` ON `renewal_web_master` (`web_domain`);

CREATE TABLE IF NOT EXISTS `renewal_web_package` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT,
  `name` varchar(255) NOT NULL DEFAULT '',
  `price_per_month` decimal(10,2) NOT NULL DEFAULT '0.00',
  `renewal_days` INT NOT NULL DEFAULT 0,
  `limit_admin_count` INT NOT NULL DEFAULT 0,
  `limit_user_count` INT NOT NULL DEFAULT 0,
  `limit_transaction_count` INT NOT NULL DEFAULT 0,
  `limit_transaction_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `limit_bank_account_count` INT NOT NULL DEFAULT 0,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
INSERT INTO `renewal_web_package` (`id`, `name`, `price_per_month`, `renewal_days`, `limit_admin_count`, `limit_user_count`, `limit_transaction_count`, `limit_transaction_amount`,  `limit_bank_account_count`) VALUES
(1, 'Website ขนาดเล็ก', 20000.00, 30, 25, 20000, 0, 2500000.00, 3),
(2, 'Company บริษัท', 25000.00, 30, 50, 50000, 0, 6000000.00, 5),
(3, 'Enterprise องค์กรใหญ่', 30000.00, 30, 50, 0, 0, 0.00, 0);

CREATE TABLE IF NOT EXISTS `invoice` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT,
  `web_id` bigint NOT NULL,
  `web_name` varchar(255) NOT NULL DEFAULT '',
  `invoice_no` varchar(255) NOT NULL DEFAULT '',
  `invoice_type_id` bigint NOT NULL DEFAULT '0',
  `package_id` bigint NOT NULL DEFAULT '0',
  `renew_days` int NOT NULL DEFAULT '0',
  `renew_credit_amount` int NOT NULL DEFAULT '0',
  `package_detail` varchar(255) NOT NULL DEFAULT '',
  `invoice_at` DATETIME NULL DEFAULT NULL,
  `expire_at` DATETIME NULL DEFAULT NULL,
  `paid_at` DATETIME NULL DEFAULT NULL,
  `paid_by` bigint NULL DEFAULT NULL,
  `payment_detail` varchar(255) NOT NULL DEFAULT '',
  `confirm_by` bigint NULL DEFAULT NULL,
  `confirm_at` DATETIME NULL DEFAULT NULL,
  `status_id` bigint NOT NULL DEFAULT '1',
  `sum_price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `vat_percent` decimal(10,2) NOT NULL DEFAULT '0.00',
  `vat_price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `total_price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `create_by` bigint NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE `invoice_type` (
    id BIGINT PRIMARY KEY,
    name VARCHAR(255) NOT NULL DEFAULT '',
    detail VARCHAR(255) NOT NULL DEFAULT '',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);
INSERT INTO `invoice_type` (`id`,`name`,`detail`) VALUES
    (1,'WEB_RENEWAL', 'ต่ออายุ'),
    (2,'FASTBANK_RENEWAL', 'เติมเครดิต'),
    (3,'SMS_RENEWAL', 'สั่งซื้อข้อความ');

CREATE TABLE `invoice_status` (
    id BIGINT PRIMARY KEY,
    name VARCHAR(255) NOT NULL DEFAULT '',
    detail VARCHAR(255) NOT NULL DEFAULT '',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);
INSERT INTO `invoice_status` (`id`,`name`,`detail`) VALUES
    (1,'WAIT_PAYMENT', 'รอการชำระ'),
    (2,'WAIT_CONFIRM', 'รอตรวจสอบยอด'),
    (3,'COMPLETED', 'ชำระแล้ว');

-- --- WORKING AFTER 2023-12-02 ------

INSERT INTO `invoice_status` (`id`,`name`,`detail`) VALUES
    (4,'CANCELED', 'ไม่สำเร็จ');

UPDATE `user_income_log_type` SET `detail`='แจกโบนัสแนะนำเพื่อน' WHERE `id`=1;
UPDATE `user_income_log_type` SET `detail`='แจกโบนัสคืนยอดเสีย' WHERE  `id`=2;

-- add user_transaction_type
INSERT INTO `user_transaction_type` (`id`, `name`, `detail`) VALUES
(8,'CREDIT_TYPE_DAILY_ACTIVITY_BONUS','โบนัสกิจกรรมรายวัน');

INSERT INTO `user_transaction_type` (`id`, `name`, `detail`) VALUES
(9,'CREDIT_TPYE_LUCKY_WHEEL','เครดิตจากกิจกรรมกงล้อนำโชค');

INSERT INTO `user_income_log_type` (`id`,`name`,`detail`) VALUES
  (4,'USER_INCOME_TYPE_LUCKY_WHEEL', 'รายได้จากกิจกรรมกงล้อนำโชค');

-- ------------

CREATE TABLE IF NOT EXISTS `activity_lucky_wheel_setting` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT,
  `condition_id` bigint NULL DEFAULT NULL,
  `lose_per_roll` int NOT NULL,
  `max_roll_per_day` int NOT NULL,
  `cumulative_expired_days` int DEFAULT NULL,
  `is_enabled` tinyint DEFAULT '0',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS `activity_lucky_wheel_condition` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` enum('DEPOSITED','LOSE')NULL DEFAULT NULL,
  `label_th` varchar(255) NULL DEFAULT NULL,
  `label_en` varchar(255) NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
);

INSERT INTO `activity_lucky_wheel_condition` (`id`, `name`, `label_th`, `label_en`) VALUES
	(1, 'DEPOSITED', 'ทุกยอด XX ฝาก ได้หมุน 1 สิทธิ์', 'everyamount can be deposited for 1 right'),
	(2, 'LOSE', 'ทุกยอด XX เสีย ได้หมุน 1 สิทธิ์', 'everyamount can be lose for 1 right');

CREATE TABLE IF NOT EXISTS `activity_lucky_wheel` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT,
  `position` int NOT NULL,
  `message` varchar(255) NOT NULL,
  `minimum_reward` int NOT NULL,
  `hex_background_color` varchar(255) NOT NULL,
  `percent_win` decimal(10,2) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

INSERT INTO `activity_lucky_wheel` (`id`, `position`, `message`,`minimum_reward`, `hex_background_color`, `percent_win`,`created_at`, `updated_at`) VALUES
	(1, 1, '1 บาท', 1, '#0917d7', '99.00', '2023-11-22 19:15:30', '2023-11-22 19:15:30'),
    (2, 2, '10 บาท', 10, '#e000ee', '15.00', '2023-11-22 19:15:30', '2023-11-22 19:15:30'),
    (3, 3, '20 บาท', 20, '#26b5c0', '10.00', '2023-11-22 19:15:30', '2023-11-22 19:15:30'),
    (4, 4, '100 บาท', 100, '#3ac828', '2.00', '2023-11-22 19:15:30', '2023-11-22 19:15:30'),
    (5, 5, '200 บาท', 200, '#dac910', '1.00', '2023-11-22 19:15:30', '2023-11-22 19:15:30'),
    (6, 6, '300 บาท', 300, '#1aa23c', '1.00', '2023-11-22 19:15:30', '2023-11-22 19:15:30'),
    (7, 7, '400 บาท', 400, '#fd0808', '1.00', '2023-11-22 19:15:30', '2023-11-22 19:15:30'),
    (8, 8, '500 บาท', 500, '#ecb1b', '1.00', '2023-11-22 19:15:30', '2023-11-22 19:15:30');

CREATE TABLE IF NOT EXISTS `activity_lucky_wheel_round_user` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT,
  `user_income_id` BIGINT(19) NULL DEFAULT NULL,
  `user_id` bigint NULL DEFAULT NULL,
  `lucky_wheel_id` bigint NULL DEFAULT NULL,
  `reward` decimal(10,2) NULL DEFAULT NULL,
  `condition_id` bigint NULL DEFAULT NULL,
  `condition_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `status_id` bigint DEFAULT 1,
  `received_date` datetime NULL DEFAULT NULL,
  `expired_date` datetime NULL DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS `activity_lucky_wheel_round_status` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` enum('ACTIVE','EXPIRED','PRIZE_CLAIMED','PRIZE_RECEIVED') DEFAULT NULL,
  `label_th` varchar(255) DEFAULT NULL,
  `label_en` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
);

INSERT INTO `activity_lucky_wheel_round_status` (`id`, `name`, `label_th`, `label_en`) VALUES
	(1, 'ACTIVE', 'ใช้งาน', 'active'),
  (2, 'EXPIRED', 'หมดอายุ', 'expired'),
  (3, 'PRIZE_CLAIMED', 'ออกรางวัล', 'prize claimed'),
  (4, 'PRIZE_RECEIVED', 'รับรางวัลแล้ว', 'prize received');

-- EOF LUKEY WHEEL --

CREATE TABLE IF NOT EXISTS `user_affiliate_income` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `user_id` BIGINT NOT NULL,
  `statement_date` VARCHAR(10) DEFAULT NULL,
  `status_id` bigint NOT NULL DEFAULT '1',
  `daily_key` VARCHAR(255) DEFAULT NULL,
  `turn_sport` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `percent_sport` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `commission_sport` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `turn_casino` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `percent_casino` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `commission_casino` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `turn_game` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `percent_game` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `commission_game` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `turn_total` DECIMAL(14,2) DEFAULT '0.00',
  `commission_total` DECIMAL(14,2) DEFAULT '0.00',
  `take_at` datetime DEFAULT NULL,
  `taken_price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
CREATE INDEX `user_id` ON `user_affiliate_income` (`user_id`);
CREATE INDEX `statement_date` ON `user_affiliate_income` (`statement_date`);
CREATE INDEX `status_id` ON `user_affiliate_income` (`status_id`);
CREATE UNIQUE INDEX `daily_key` ON `user_affiliate_income` (`daily_key`);

INSERT INTO activity_lucky_wheel_setting (condition_id, lose_per_roll, max_roll_per_day, cumulative_expired_days, is_enabled) VALUES (1, 1, 3, 10, 0);

-- EOF 2023-12-07 --

ALTER TABLE `renewal_web_master`
	ADD COLUMN `last_remote_update_at` DATETIME NULL DEFAULT NULL AFTER `api_key`;

-- Acticity Week

CREATE TABLE `activity_daily`(
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `monday_bonus` DECIMAL(10,2) DEFAULT NULL,
    `tuesday_bonus` DECIMAL(10,2) DEFAULT NULL,
    `wednesday_bonus` DECIMAL(10,2) DEFAULT NULL,
    `thursday_bonus` DECIMAL(10,2) DEFAULT NULL,
    `friday_bonus` DECIMAL(10,2) DEFAULT NULL,
    `saturday_bonus` DECIMAL(10,2) DEFAULT NULL,
    `sunday_bonus` DECIMAL(10,2) DEFAULT NULL,
    `completed_bonus` DECIMAL(10,2) DEFAULT NULL,
    `activity_daily_condition_id` BIGINT NOT NULL,
    `activity_daily_status_id` BIGINT NOT NULL,
    `deposit_amount_condition` DECIMAL(10,2) DEFAULT NULL,
    `updated_by_admin_id` BIGINT DEFAULT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
    `deleted_at` DATETIME DEFAULT NULL,
    PRIMARY KEY (`id`)
);

INSERT INTO `activity_daily` (`id`, `monday_bonus`, `tuesday_bonus`, `wednesday_bonus`, `thursday_bonus`, `friday_bonus`, `saturday_bonus`, `sunday_bonus`, `completed_bonus`, `activity_daily_condition_id`, `activity_daily_status_id`, `deposit_amount_condition`, `updated_by_admin_id`) VALUES
 (1, 1.00, 2.00, 3.00, 4.00, 5.00, 6.00, 7.00, 10.00, 1, 2, 10.00, 1);


CREATE TABLE `activity_daily_condition` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `name` ENUM('NONE','MINIMUM_DEPOSIT','DEPOSIT_ACCUMULATED_AMOUNT') NOT NULL DEFAULT 'NONE',
  `label_th` VARCHAR(255) DEFAULT NULL,
  `label_en` VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
);

INSERT INTO `activity_daily_condition` (`id`, `name`, `label_th`, `label_en`) VALUES
(1, 'NONE', 'ไม่มีเงื่อนไข', 'none'),
(2, 'MINIMUM_DEPOSIT', 'ฝากขั้นต่ำ', 'minimum deposit'),
(3, 'DEPOSIT_ACCUMULATED_AMOUNT','ฝากตามยอด', 'deposit accumulated amount');

CREATE TABLE `activity_daily_status` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `name` ENUM('ACTIVE','DEACTIVE') NOT NULL DEFAULT 'ACTIVE',
  `label_th` VARCHAR(255) DEFAULT NULL,
  `label_en` VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
);

INSERT INTO `activity_daily_status` (`id`, `name`, `label_th`, `label_en`) VALUES
(1,'ACTIVE','ใช้งาน','active'),
(2,'DEACTIVE','ยกเลิก','deactive');


ALTER TABLE `activity_daily` ADD FOREIGN KEY (`activity_daily_condition_id`) REFERENCES `activity_daily_condition` (`id`);
ALTER TABLE `activity_daily` ADD FOREIGN KEY (`activity_daily_status_id`) REFERENCES `activity_daily_status` (`id`);



CREATE TABLE `activity_daily_user` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `daily_key` varchar(255) NOT NULL,
  `user_id` BIGINT NOT NULL,
  `collected_bonus` DECIMAL(10,2) DEFAULT NULL,
  `collected_date` DATETIME DEFAULT NULL,
  `activity_day_id` BIGINT NOT NULL,
  `created_at` DATETIME NOT NULL DEFAULT NOW(),
  `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
  `deleted_at` DATETIME DEFAULT NULL,
  UNIQUE KEY `uni_daily_key` (`daily_key`),
  PRIMARY KEY (`id`)
);

CREATE TABLE `activity_day` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `name` ENUM('MON','TUE','WED','THU','FRI','SAT','SUN','COMPLETE_DAILY') NOT NULL DEFAULT 'MON',
  `label_th` VARCHAR(255) DEFAULT NULL,
  `label_en` VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
);

INSERT INTO `activity_day` (`id`, `name`, `label_th`, `label_en`) VALUES
(1,'MON','จันทร์','monday'),
(2,'TUE','อังคาร','tuesday'),
(3,'WED','พุธ','wednesday'),
(4,'THU','พฤหัสบดี','thursday'),
(5,'FRI','ศุกร์','friday'),
(6,'SAT','เสาร์','saturday'),
(7,'SUN','อาทิตย์','sunday'),
(8,'COMPLETE_DAILY', 'เก็บครบ', 'complete');
ALTER TABLE `activity_daily_user` ADD FOREIGN KEY (`activity_day_id`) REFERENCES `activity_day` (`id`);

-- bank_transaction
ALTER TABLE `bank_transaction`
	ADD COLUMN `external_match_id` BIGINT(19) NULL DEFAULT NULL AFTER `statement_id`;

-- EOF 2023-12-08 --

-- MIGRATE of 2023-12-12 --

ALTER TABLE `invoice`
	ADD COLUMN `confirm_by_name` VARCHAR(255) NULL DEFAULT NULL AFTER `confirm_by`;

UPDATE `invoice_status` SET `name`='REJECTED',`detail`='ไม่อนุมัติ' WHERE  `id`=4;

CREATE TABLE IF NOT EXISTS `renewal_sms_package` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `rate_price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `renewal_credits` int NOT NULL DEFAULT '0',
  `credit_days` int NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
INSERT INTO `renewal_sms_package` (`id`, `name`, `price`, `rate_price`, `renewal_credits`, `credit_days`) VALUES (1, 'แพคเกจ 1,500', 1500, 0.80, 1860, 80);
INSERT INTO `renewal_sms_package` (`id`, `name`, `price`, `rate_price`, `renewal_credits`, `credit_days`) VALUES (2, 'แพคเกจ 5,000', 5000, 0.68, 7353, 280);
INSERT INTO `renewal_sms_package` (`id`, `name`, `price`, `rate_price`, `renewal_credits`, `credit_days`) VALUES (3, 'แพคเกจ 10,000', 10000, 0.58, 17241, 380);
INSERT INTO `renewal_sms_package` (`id`, `name`, `price`, `rate_price`, `renewal_credits`, `credit_days`) VALUES (4, 'แพคเกจ 30,000', 30000, 0.46, 65217, 380);

CREATE TABLE IF NOT EXISTS `renewal_fastbank_package` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `renewal_credits` int NOT NULL DEFAULT '0',
  `credit_days` int NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
INSERT INTO `renewal_fastbank_package` (`id`, `name`, `price`, `renewal_credits`, `credit_days`) VALUES (1, 'แพคเกจ 2,500', 2500, 10000, 45);
INSERT INTO `renewal_fastbank_package` (`id`, `name`, `price`, `renewal_credits`, `credit_days`) VALUES (2, 'แพคเกจ 5,000', 5000, 20000, 60);
INSERT INTO `renewal_fastbank_package` (`id`, `name`, `price`, `renewal_credits`, `credit_days`) VALUES (3, 'แพคเกจ 10,000', 10000, 41000, 90);
INSERT INTO `renewal_fastbank_package` (`id`, `name`, `price`, `renewal_credits`, `credit_days`) VALUES (4, 'แพคเกจ 30,000', 30000, 152000, 350);
INSERT INTO `renewal_fastbank_package` (`id`, `name`, `price`, `renewal_credits`, `credit_days`) VALUES (5, 'แพคเกจ 50,000', 50000, 260000, 350);

-- MASTER
-- ALTER TABLE `cybergame_web`
-- 	ADD COLUMN `last_remote_update_at` DATETIME NULL DEFAULT NULL AFTER `api_key`;

-- INSERT INTO `invoice_status` (`id`, `name`, `detail`) VALUES (4, 'REJECTED', 'ไม่อนุมัติ');
-- DROP TABLE `renewal_fastbank_package`;
-- DROP TABLE `renewal_sms_package`;

-- CREATE TABLE IF NOT EXISTS `renewal_sms_package` (
--   `id` bigint NOT NULL AUTO_INCREMENT,
--   `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
--   `price` decimal(10,2) NOT NULL DEFAULT '0.00',
--   `rate_price` decimal(10,2) NOT NULL DEFAULT '0.00',
--   `renewal_credits` int NOT NULL DEFAULT '0',
--   `credit_days` int NOT NULL DEFAULT '0',
--   `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
--   `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
--   PRIMARY KEY (`id`) USING BTREE
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
-- INSERT INTO `renewal_sms_package` (`id`, `name`, `price`, `rate_price`, `renewal_credits`, `credit_days`) VALUES (1, 'แพคเกจ 1,500', 1500, 0.80, 1860, 80);
-- INSERT INTO `renewal_sms_package` (`id`, `name`, `price`, `rate_price`, `renewal_credits`, `credit_days`) VALUES (2, 'แพคเกจ 5,000', 5000, 0.68, 7353, 280);
-- INSERT INTO `renewal_sms_package` (`id`, `name`, `price`, `rate_price`, `renewal_credits`, `credit_days`) VALUES (3, 'แพคเกจ 10,000', 10000, 0.58, 17241, 380);
-- INSERT INTO `renewal_sms_package` (`id`, `name`, `price`, `rate_price`, `renewal_credits`, `credit_days`) VALUES (4, 'แพคเกจ 30,000', 30000, 0.46, 65217, 380);

-- CREATE TABLE IF NOT EXISTS `renewal_fastbank_package` (
--   `id` bigint NOT NULL AUTO_INCREMENT,
--   `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
--   `price` decimal(10,2) NOT NULL DEFAULT '0.00',
--   `renewal_credits` int NOT NULL DEFAULT '0',
--   `credit_days` int NOT NULL DEFAULT '0',
--   `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
--   `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
--   PRIMARY KEY (`id`) USING BTREE
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
-- INSERT INTO `renewal_fastbank_package` (`id`, `name`, `price`, `renewal_credits`, `credit_days`) VALUES (1, 'แพคเกจ 2,500', 2500, 10000, 45);
-- INSERT INTO `renewal_fastbank_package` (`id`, `name`, `price`, `renewal_credits`, `credit_days`) VALUES (2, 'แพคเกจ 5,000', 5000, 20000, 60);
-- INSERT INTO `renewal_fastbank_package` (`id`, `name`, `price`, `renewal_credits`, `credit_days`) VALUES (3, 'แพคเกจ 10,000', 10000, 41000, 90);
-- INSERT INTO `renewal_fastbank_package` (`id`, `name`, `price`, `renewal_credits`, `credit_days`) VALUES (4, 'แพคเกจ 30,000', 30000, 152000, 350);
-- INSERT INTO `renewal_fastbank_package` (`id`, `name`, `price`, `renewal_credits`, `credit_days`) VALUES (5, 'แพคเกจ 50,000', 50000, 260000, 350);

-- EOF 2023-12-12 --

-- MIGRATE of 2023-12-13 --
ALTER TABLE `user_update_log`
	ADD COLUMN `json_result` LONGTEXT NULL AFTER `ip`;

-- MIGRATE of 2023-12-14 --
ALTER TABLE `invoice`
	ADD COLUMN `slip_image_path` VARCHAR(1000) NOT NULL DEFAULT '' AFTER `payment_detail`;

-- ALTER TABLE master.`invoice`
-- 	ADD COLUMN `slip_image_path` VARCHAR(1000) NOT NULL DEFAULT '' AFTER `payment_detail`;

-- MIGRATE of 2023-12-15 --
ALTER TABLE `renewal_web_master`
	ADD COLUMN `is_front_enabled` TINYINT NOT NULL DEFAULT '1' AFTER `last_remote_update_at`,
	ADD COLUMN `is_back_enabled` TINYINT NOT NULL DEFAULT '1' AFTER `is_front_enabled`,
	ADD COLUMN `maintenance_message` VARCHAR(1000) NOT NULL DEFAULT '' AFTER `is_back_enabled`;

-- MIGRATE of 2023-12-19 --
ALTER TABLE `configuration_web`
	ADD COLUMN `min_first_member_deposit` INT NOT NULL DEFAULT '0' AFTER `minimum_withdraw`;

-- ALTER TABLE master.`invoice`
-- 	ADD COLUMN `discount_price` DECIMAL(10,2) NOT NULL DEFAULT '0.00' AFTER `vat_price`;

-- MIGRATE of 2023-12-21
CREATE TABLE IF NOT EXISTS `activity_lucky_wheel_round` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `user_id` BIGINT NULL DEFAULT NULL,
  `received` INT DEFAULT '0',
  `condition_id` BIGINT NULL DEFAULT NULL,
  `condition_amount` DECIMAL(10,2) NOT NULL DEFAULT '0.00',
  `received_date` DATETIME NULL DEFAULT NULL,
  `expired_date` DATETIME NULL DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

ALTER TABLE `activity_lucky_wheel_round_user`
	ADD COLUMN `condition_description` VARCHAR(255) NULL DEFAULT NULL AFTER `lucky_wheel_id`;

CREATE TABLE IF NOT EXISTS `activity_lucky_wheel_round_confirm` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `action_key` varchar(255) NOT NULL,
  `user_id` BIGINT NOT NULL,
  `lucky_wheel_user_id` BIGINT NOT NULL,
  `condition_amount` DECIMAL(10,2) NOT NULL DEFAULT '0.00',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` DATETIME DEFAULT NULL,
  UNIQUE KEY `uni_action_key` (`action_key`)
);

INSERT INTO `bank` (`id`, `name`, `code`, `icon_url`, `type_flag`) VALUES   (21, 'ธนาคารภายนอก', 'external', 'https://storage.googleapis.com/cbgame/banks/none.png', '********');

ALTER TABLE `activity_lucky_wheel_round_user`
	ADD COLUMN `lucky_wheel_round_id` BIGINT NULL DEFAULT NULL AFTER `id`;

-- MIGRATE of 2023-12-23
ALTER TABLE `activity_lucky_wheel_round_user`
  ADD COLUMN `rotated_date` datetime NULL DEFAULT NULL AFTER `expired_date`;

-- MIGRATE of 2023-12-29 --

CREATE TABLE IF NOT EXISTS `bank_transaction_withdraw_confirm` (
	`id` BIGINT PRIMARY KEY AUTO_INCREMENT,
	`confirm_key` varchar(255) NOT NULL,
	`user_id` BIGINT NOT NULL,
	`bank_transaction_id` BIGINT NULL,
	`created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
	`updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
	`deleted_at` DATETIME DEFAULT NULL,
	UNIQUE KEY `uni_confirm_key` (`confirm_key`)
  );

-- MIGRATE of 2023-12-24 --
ALTER TABLE `invoice`
	ADD COLUMN `discount_price` DECIMAL(10,2) NOT NULL DEFAULT '0.00' AFTER `vat_price`;

-- MIGRATE of 2024-01-04 -

ALTER TABLE `renewal_web_master`
	ADD COLUMN `fastbank_free_start_date` DATE NULL DEFAULT NULL AFTER `fastbank_expired_date`,
	ADD COLUMN `fastbank_free_end_date` DATE NULL DEFAULT NULL AFTER `fastbank_free_start_date`;

CREATE TABLE `agent_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NULL DEFAULT NULL,
  `status` varchar(255) NULL DEFAULT NULL,
  `json_input` text NULL DEFAULT NULL,
  `json_request` text NULL DEFAULT NULL,
  `json_reponse` text NULL DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
);

CREATE TABLE IF NOT EXISTS `web_popup` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT,
  `sort_order` bigint NOT NULL DEFAULT 0,
  `img_path` varchar(255) NOT NULL DEFAULT '',
  `is_show` tinyint NOT NULL DEFAULT 0,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL
);

CREATE TABLE IF NOT EXISTS `race_action` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `action_key` varchar(255) DEFAULT NULL,
  `status` enum('PENDING','SUCCESS','FAILED') NOT NULL DEFAULT 'PENDING',
  `name` varchar(255) DEFAULT NULL,
  `json_request` text,
  `unlock_at` datetime NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni_action_key` (`action_key`)
);

-- เอา package อื่นๆ ออกไปให้หมด
DELETE FROM `renewal_web_package` WHERE `id`=1;
DELETE FROM `renewal_web_package` WHERE `id`=2;

-- MIGRATE of 2024-01-08 -

CREATE TABLE `sys_user_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NULL DEFAULT NULL,
  `status` varchar(255) NULL DEFAULT NULL,
  `json_input` text NULL DEFAULT NULL,
  `json_request` text NULL DEFAULT NULL,
  `json_reponse` text NULL DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
);

-- MIGRATE of 2024-01-12

CREATE TABLE IF NOT EXISTS `user_today_playlog` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `user_id` BIGINT NOT NULL,
  `daily_key` VARCHAR(255) DEFAULT NULL,
  `statement_date` VARCHAR(10) DEFAULT NULL,
  `turn_sport` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `valid_amount_sport` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `win_lose_sport` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `turn_casino` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `valid_amount_casino` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `win_lose_casino` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `turn_game` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `valid_amount_game` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `win_lose_game` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `turn_total` DECIMAL(14,2) DEFAULT '0.00',
  `win_lose_total` DECIMAL(14,2) DEFAULT '0.00',
  `valid_amount_total` DECIMAL(14,2) DEFAULT '0.00',
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
CREATE INDEX `user_id` ON `user_today_playlog` (`user_id`);
CREATE INDEX `statement_date` ON `user_today_playlog` (`statement_date`);
CREATE UNIQUE INDEX `daily_key` ON `user_today_playlog` (`daily_key`);

SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0;
DROP TABLE `promotion_web`;
SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS;
DROP TABLE `promotion_web_bonus_type`;
DROP TABLE `promotion_web_condition`;
DROP TABLE `promotion_web_condition_type`;
DROP TABLE `promotion_web_type`;

--  promotion_web_type
CREATE TABLE `promotion_web_type` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(255) NOT NULL,
  `label_th` VARCHAR(255) DEFAULT NULL,
  `label_en` VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
);

INSERT INTO `promotion_web_type` (`id`, `name`, `label_th`, `label_en`) VALUES
(1,'NEW_MEMBER_FREE','โปรโมชั่นสมัครใหม่แจกฟรี','new member free'),
(2,'NEW_MEMBER_CONDITION','โปรโมชั่นสมัครใหม่ตามมเงื่อนไข','new member condition'),
(3,'DEPOSIT_MINIMUM_PER_DAY','โปรโมชั่นฝากขั้นต่ำต่อวัน','deposit minimum per day'),
(4,'FIRST_DEPOSIT','โปรโมชั่นฝากครั้งแรก','first deposit'),
(5,'DEPOSIT_PER_DAY','โปรโมชั่นฝากทั้งวัน','deposit per day'),
(6,'DEPOSIT_BY_TIME','โปรโมชั่นฝากตามช่วงเวลา','deposit by time');

-- promotion_web_status
CREATE TABLE `promotion_web_status` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(255) NOT NULL,
  `label_th` VARCHAR(255) DEFAULT NULL,
  `label_en` VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
);

INSERT INTO `promotion_web_status` (`id`, `name`, `label_th`, `label_en`) VALUES
(1,'DISABLE_WEB','ปิดการแสดงหน้าเว็บไซต์','disactive web'),
(2,'ACTIVE','ใช้งาน','active'),
(3,'CANCELED','ยกเลิก','canceled');

-- promotion_web_bonus_condition

CREATE TABLE `promotion_web_bonus_condition` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `syntax` VARCHAR(10) NULL DEFAULT NULL,
  `name` VARCHAR(255) NOT NULL,
  `label_th` VARCHAR(255) DEFAULT NULL,
  `label_en` VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
);


INSERT INTO `promotion_web_bonus_condition` (`id`,`syntax`, `name`, `label_th`, `label_en`) VALUES
(1,'>=','MORE_THAN_OR_EQUAL','มากกว่าหรือเท่ากับ','more than or equal'),
(2,'<=','LESS_THAN_OR_EQUAL','น้อยกว่าหรือเท่ากับ','less than or equal');


-- promotion_web_bonus_type
CREATE TABLE `promotion_web_bonus_type` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(255) NOT NULL,
  `label_th` VARCHAR(255) DEFAULT NULL,
  `label_en` VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
);

INSERT INTO `promotion_web_bonus_type` (`id`, `name`, `label_th`, `label_en`) VALUES
	(1, 'PERCENT', 'เปอร์เซ็นต์', 'percent'),
	(2, 'FIXED RATE', 'จำนวนเงิน', 'fixed rate');

CREATE TABLE `promotion_web_turnover_type` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(255) NOT NULL,
  `label_th` VARCHAR(255) DEFAULT NULL,
  `label_en` VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
);

INSERT INTO `promotion_web_turnover_type` (`id`, `name`, `label_th`, `label_en`) VALUES
    (1, 'ALL', 'ทุกเกม', 'all'),
    (2, 'SPORT', 'กีฬา', 'sport'),
    (3, 'CASINO', 'คาสิโน', 'casino'),
    (4, 'SLOT', 'สล็อต', 'slot');

-- promotion_web_date_type
CREATE TABLE `promotion_web_date_type` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(255) NOT NULL,
  `label_th` VARCHAR(255) DEFAULT NULL,
  `label_en` VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
);

INSERT INTO `promotion_web_date_type` (`id`, `name`, `label_th`, `label_en`) VALUES
(1,'FIXED_DATE','เริ่มต้น-สิ้นสุด','fixed date'),
(2,'NON_FIXED_DATE','ไม่กำหนดระยะเวลา','non fixed date');
-- promotion_web

CREATE TABLE `promotion_web` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `promotion_web_type_id` BIGINT NOT NULL,
    `promotion_web_status_id` BIGINT NOT NULL,
    `condition_detail` VARCHAR(255) DEFAULT NULL,
    `image_url` VARCHAR(255) DEFAULT NULL,
    `name` VARCHAR(255) NOT NULL,
    `short_description` VARCHAR(255) NOT NULL,
    `description` TEXT NOT NULL,
    `promotion_web_date_type_id` BIGINT NOT NULL,
    `start_date` DATE DEFAULT NULL,
    `end_date` DATE DEFAULT NULL,

    `free_bonus_amount` DECIMAL(10,2) DEFAULT NULL,
    `privilege_per_day` INT DEFAULT NULL,
    `able_withdraw_morethan` DECIMAL(10,2) DEFAULT NULL,

    -- Fields for specific promotion conditions
    `promotion_web_bonus_condition_id` BIGINT DEFAULT NULL,
    `bonus_condition_amount` DECIMAL(10,2) DEFAULT NULL,
    `promotion_web_bonus_type_id` BIGINT DEFAULT NULL,
    `bonus_type_amount` DECIMAL(10,2) DEFAULT NULL,
    `bonus_type_amount_max` DECIMAL(10,2) DEFAULT NULL,
    `able_withdraw_pertime` DECIMAL(10,2) DEFAULT NULL,
    `promotion_web_turnover_type_id` BIGINT DEFAULT NULL,
    `turnover_amount` DECIMAL(10,2) DEFAULT NULL,

    -- Fields for day and time specific promotions
    `monday` BOOLEAN DEFAULT NULL,
    `tuesday` BOOLEAN DEFAULT NULL,
    `wednesday` BOOLEAN DEFAULT NULL,
    `thursday` BOOLEAN DEFAULT NULL,
    `friday` BOOLEAN DEFAULT NULL,
    `saturday` BOOLEAN DEFAULT NULL,
    `sunday` BOOLEAN DEFAULT NULL,
    `time_start` TIME DEFAULT NULL,
    `time_end` TIME DEFAULT NULL,

    `created_by_admin_id` BIGINT DEFAULT NULL,
    `updated_by_admin_id` BIGINT DEFAULT NULL,
    `canceled_by_admin_id` BIGINT DEFAULT NULL,
    `deleted_by_admin_id` BIGINT DEFAULT NULL,

    `created_at` DATETIME DEFAULT NOW(),
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
    `deleted_at` DATETIME DEFAULT NULL,
    PRIMARY KEY (`id`)
);


ALTER TABLE `promotion_web` ADD FOREIGN KEY (`promotion_web_type_id`) REFERENCES `promotion_web_type` (`id`);
ALTER TABLE `promotion_web` ADD FOREIGN KEY (`promotion_web_status_id`) REFERENCES `promotion_web_status` (`id`);
ALTER TABLE `promotion_web` ADD FOREIGN KEY (`promotion_web_bonus_condition_id`) REFERENCES `promotion_web_bonus_condition` (`id`);
ALTER TABLE `promotion_web` ADD FOREIGN KEY (`promotion_web_bonus_type_id`) REFERENCES `promotion_web_bonus_type` (`id`);
ALTER TABLE `promotion_web` ADD FOREIGN KEY (`promotion_web_turnover_type_id`) REFERENCES `promotion_web_turnover_type` (`id`);
ALTER TABLE `promotion_web` ADD FOREIGN KEY (`promotion_web_date_type_id`) REFERENCES `promotion_web_date_type` (`id`);
--  promotion_web_user
CREATE TABLE `promotion_web_user` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `promotion_web_id` BIGINT NOT NULL,
  `user_id` BIGINT NOT NULL,
  `promotion_web_user_status_id` BIGINT NOT NULL,
  `total_amount` DECIMAL(10,2) DEFAULT NULL,
  `canceled_by_admin_id` BIGINT DEFAULT NULL,
  `deleted_by_admin_id` BIGINT DEFAULT NULL,
  `created_at` DATETIME DEFAULT NOW(),
  `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
  `deleted_at` DATETIME DEFAULT NULL,
  PRIMARY KEY (`id`)
);


--  promotion_web_user_status
CREATE TABLE `promotion_web_user_status` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(255) NOT NULL,
  `label_th` VARCHAR(255) DEFAULT NULL,
  `label_en` VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
);

INSERT INTO `promotion_web_user_status` (`id`, `name`, `label_th`, `label_en`) VALUES
(1,'ON_PROCESS','รอผ่านเงื่อนไข','on process'),
(2,'SUCCESS','สำเร็จ','success'),
(3,'CANCELED','ยกเลิก','canceled'),
(4,'WITHDRAW_ON_PROCESS','อยู่ระหว่างเงือนไขถอน','withdraw on process');

-- promotion_web_user_confirm
CREATE TABLE `promotion_web_user_confirm` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `action_key` VARCHAR(255) NOT NULL,
  `promotion_web_id` BIGINT NOT NULL,
  `user_id` BIGINT NOT NULL,
  `promotion_web_user_id` BIGINT(19) DEFAULT NULL,
  `created_at` DATETIME DEFAULT NOW(),
  `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
  `deleted_at` DATETIME DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE (`action_key`)
);

-- promotion_web_user_log
CREATE TABLE `promotion_web_user_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `json_request` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `json_payload` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `log_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
);


INSERT INTO `user_transaction_type` (`id`, `name`, `detail`) VALUES (10, 'CREDIT_TYPE_PROMOTION_WEB', 'โปรโมชั่นเว็บ');


-- MIGRATE of 2024-01-25

CREATE TABLE IF NOT EXISTS `user_withdraw_setting` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT NOT NULL,
    `accumulated_amount` DECIMAL(10, 2) NOT NULL DEFAULT 3000,
    `maximum_time_per_day` DECIMAL(10, 2) NOT NULL DEFAULT 5,
    `updated_by_admin_id` BIGINT DEFAULT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
    `deleted_at` DATETIME DEFAULT NULL,
    PRIMARY KEY (`id`)
);

CREATE TABLE IF NOT EXISTS `user_withdraw_config` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `config_key` VARCHAR(255) NOT NULL,
    `config_value` DECIMAL(10, 2) NOT NULL,
    `deleted_at` DATETIME DEFAULT NULL,
    PRIMARY KEY (`id`)
);

INSERT INTO `user_withdraw_config` (`config_key`, `config_value`) VALUES ('withdraw_accumulated_amount', 3000);
INSERT INTO `user_withdraw_config` (`config_key`, `config_value`) VALUES ('withdraw_maximum_time_per_day', 5);

INSERT INTO `permission` (`permission_key`, `main`, `name`) VALUES ('alliances_commission_edit',1, 'แก้ไขข้อมูลพันธมิตร');

CREATE TABLE IF NOT EXISTS `line_login` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `access_token` VARCHAR(255) NOT NULL,
    `refresh_token` VARCHAR(255) NOT NULL,
    `uuid` VARCHAR(255) NOT NULL,
    `image_url` VARCHAR(255) NOT NULL,
    `display_name` VARCHAR(255) NOT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
    PRIMARY KEY (`id`)
);

ALTER TABLE `user`
	ADD COLUMN `login_type` VARCHAR(255) NOT NULL DEFAULT 'phone' AFTER `username`;

-- MIGRATE of 2024-02-01

CREATE TABLE IF NOT EXISTS `paygate_heng_token` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `access_token` VARCHAR(255) NOT NULL,
    `expire_at` DATETIME NOT NULL,
    `create_by` BIGINT NOT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
    PRIMARY KEY (`id`)
);

CREATE TABLE IF NOT EXISTS `paygate_heng_order` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT NOT NULL,
    `order_no` VARCHAR(255) NOT NULL,
    `shop_name` VARCHAR(255) NOT NULL,
    `amount` DECIMAL(10,2) NOT NULL,
    `transaction_no` VARCHAR(255) NULL,
    `transaction_date` DATETIME  NULL,
    `transaction_status` VARCHAR(255) NULL,
    `payment_at` DATETIME NULL DEFAULT NULL,
    `qr_promptpay` VARCHAR(255) NULL,
    `created_at` DATETIME DEFAULT NOW(),
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
    PRIMARY KEY (`id`)
);
CREATE UNIQUE INDEX `order_no` ON `paygate_heng_order` (`order_no`);
CREATE UNIQUE INDEX `transaction_no` ON `paygate_heng_order` (`transaction_no`);

CREATE TABLE IF NOT EXISTS `paygate_heng_webhook` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL,
    `json_payload` LONGTEXT NOT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    PRIMARY KEY (`id`)
);

CREATE TABLE IF NOT EXISTS `paygate_heng_setting` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL,
    `is_enabled` TINYINT NOT NULL DEFAULT 0,
    `api_end_point` VARCHAR(255) NOT NULL,
    `shop_name` VARCHAR(255) NOT NULL,
    `username` VARCHAR(255) NOT NULL,
    `password` VARCHAR(255) NOT NULL,
    `balance` DECIMAL(10,2) NOT NULL DEFAULT 0,
    `callback_url` VARCHAR(255) NOT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
    PRIMARY KEY (`id`)
);

INSERT INTO `promotion_web_type` (`id`, `name`, `label_th`, `label_en`) VALUES (7, 'FIRST_DEPOSIT_OF_DAY', 'โปรโมชั้นฝากครั้งแรกของวัน', 'first deposit of the day');

ALTER TABLE `configuration_notification`
	ADD COLUMN `is_actitvity_before_bonus` TINYINT NOT NULL DEFAULT '1' AFTER `is_pull_credit_back`;
ALTER TABLE `configuration_notification`
	ADD COLUMN `is_actitvity_after_bonus` TINYINT NOT NULL DEFAULT '1' AFTER `is_pull_credit_back`;
ALTER TABLE `configuration_notification`
	ADD COLUMN `is_promotion_bonus` TINYINT NOT NULL DEFAULT '1' AFTER `is_pull_credit_back`;

INSERT INTO `permission` (`permission_key`, `main`, `name`) VALUES ('news_popup', 1, 'จัดการข่าวสารหน้าบ้าน');

-- MIGRATE of 2024-02-05

ALTER TABLE `invoice`
  ADD COLUMN `raw_qr_code`  VARCHAR(255) NOT NULL DEFAULT '' AFTER `slip_image_path`;

-- MIGRATE of 2024-02-15

ALTER TABLE `configuration_web`
	ADD COLUMN `check_phone_captcha_len` TINYINT NOT NULL DEFAULT '0' AFTER `url_line`;

CREATE TABLE IF NOT EXISTS `promotion_web_register_member` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT NOT NULL,
    `promotion_id` BIGINT NOT NULL,
    `register_status` BIGINT NOT NULL DEFAULT 1,
    `register_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    `deleted_at` DATETIME DEFAULT NULL,
    PRIMARY KEY (`id`)
);


CREATE TABLE IF NOT EXISTS `promotion_web_register_member_status` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL,
    `label_th` VARCHAR(255) NOT NULL,
    `label_en` VARCHAR(255) NOT NULL,
    `deleted_at` DATETIME DEFAULT NULL,
    PRIMARY KEY (`id`)
);

INSERT INTO `promotion_web_register_member_status` (`id`, `name`, `label_th`, `label_en`) VALUES
(1, 'REGISTER_PENDING', 'รอการยืนยัน', 'register pending'),
(2, 'REGISTER_CONFIRM', 'ยืนยัน', 'register confirm'),
(3, 'REGISTER_REJECT', 'ปฏิเสธ', 'register reject');


CREATE TABLE IF NOT EXISTS `coupon_cash` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL,
    `coupon_total` BIGINT NOT NULL,
    `coupon_turnover` BIGINT NOT NULL,
    `turnover_to_play` DECIMAL(10, 2) NOT NULL,
    `coupon_bonus` DECIMAL(10, 2) NOT NULL,
    `coupon_cash_status_id` BIGINT NOT NULL DEFAULT 1,
    `created_by_admin_id` BIGINT NOT NULL,
    `deleted_by_admin_id` BIGINT DEFAULT NULL,
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    `deleted_at` DATETIME DEFAULT NULL,
    PRIMARY KEY (`id`)
);

CREATE TABLE IF NOT EXISTS `coupon_cash_user` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `coupon_cash_id` BIGINT NOT NULL,
    `user_id` BIGINT DEFAULT NULL,
    `generate_key` VARCHAR(255) NOT NULL,
    `coupon_cash_user_status_id` BIGINT NOT NULL DEFAULT 1,
    `user_receive_at` DATETIME DEFAULT NULL,
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `created_by_admin_id` BIGINT NOT NULL,
    `deleted_by_admin_id` BIGINT DEFAULT NULL,
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    `deleted_at` DATETIME DEFAULT NULL,
     PRIMARY KEY (`id`)
);

CREATE TABLE IF NOT EXISTS `coupon_cash_user_status` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL,
    `label_th` VARCHAR(255) NOT NULL,
    `label_en` VARCHAR(255) NOT NULL,
    `deleted_at` DATETIME DEFAULT NULL,
    PRIMARY KEY (`id`)
);

INSERT INTO `coupon_cash_user_status` (`id`, `name`, `label_th`, `label_en`) VALUES
(1, 'COUPON_PENDING', 'ยังไม่มีการรับ', 'coupon pending'),
(2, 'COUPON_RECEIVE_ON_TURNOVER', 'รับเงินแล้วแต่ติดเทิร์นโอเวอร์', 'coupon receive on turnover'),
(3, 'COUPON_SUCCESS', 'สำเร็จ', 'coupon success'),
(4, 'COUPON_DELETE', 'ลบ', 'coupon delete');

CREATE TABLE IF NOT EXISTS `coupon_cash_status` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL,
    `label_th` VARCHAR(255) NOT NULL,
    `label_en` VARCHAR(255) NOT NULL,
    `deleted_at` DATETIME DEFAULT NULL,
    PRIMARY KEY (`id`)
);

INSERT INTO `coupon_cash_status` (`id`, `name`, `label_th`, `label_en`) VALUES
(1, 'COUPON_ACTIVE', 'ใช้งานได้', 'coupon active'),
(2, 'COUPON_DELETE', 'ลบ', 'coupon delete');

INSERT INTO `user_transaction_type` (`id`, `name`, `detail`) VALUES
(11, 'COUPON_CASH', 'คูปองเงินสด');


CREATE TABLE IF NOT EXISTS `coupon_cash_user_confirm` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `coupon_cash_user_id` BIGINT NOT NULL,
    `user_id` BIGINT NOT NULL,
    `confirm_key` VARCHAR(255) NOT NULL,
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    `deleted_at` DATETIME DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `confirm_key` (`confirm_key`)
);

-- MIGRATE of 2024-02-29

ALTER TABLE `configuration_web`
	ADD COLUMN `open_game_new_tab` TINYINT(3) NOT NULL DEFAULT '0' AFTER `check_phone_captcha_len`;

ALTER TABLE `promotion_return_setting`
	ADD COLUMN `calc_on_sport` TINYINT NOT NULL DEFAULT '1' AFTER `credit_expire_days`,
	ADD COLUMN `calc_on_casino` TINYINT NOT NULL DEFAULT '1' AFTER `calc_on_sport`,
	ADD COLUMN `calc_on_game` TINYINT NOT NULL DEFAULT '1' AFTER `calc_on_casino`;

ALTER TABLE `promotion_return_loser`
	ADD COLUMN `total_loss_sport` DECIMAL(10,2) NOT NULL DEFAULT '0.00' AFTER `total_loss_amount`,
	ADD COLUMN `total_loss_casino` DECIMAL(10,2) NOT NULL DEFAULT '0.00' AFTER `total_loss_sport`,
	ADD COLUMN `total_loss_game` DECIMAL(10,2) NOT NULL DEFAULT '0.00' AFTER `total_loss_casino`;

ALTER TABLE `promotion_return_loser`
	ADD COLUMN `game_detail` VARCHAR(50) NOT NULL DEFAULT '' AFTER `return_percent`;

UPDATE `promotion_return_loser` SET `game_detail`='sport,casino,game' WHERE `game_detail`='';

CREATE TABLE IF NOT EXISTS `admin_single_session` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `admin_id` BIGINT NOT NULL,
    `md5_token` VARCHAR(500) NOT NULL,
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uni_admin_id` (`admin_id`)
);

CREATE TABLE IF NOT EXISTS `user_single_session` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT NOT NULL,
    `md5_token` VARCHAR(500) NOT NULL,
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uni_user_id` (`user_id`)
);

ALTER TABLE `configuration_web`
	ADD COLUMN `show_web_aff_name` TINYINT(3) NOT NULL DEFAULT '1' AFTER `open_game_new_tab`;

INSERT INTO `turnover_statement_type` (`id`, `name`) VALUES
(3, 'TURN_SETTING_PLAY_ALL'),
(4, 'TURN_SETTING_PLAY_GAME'),
(5, 'TURN_SETTING_PLAY_SPORT'),
(6, 'TURN_SETTING_PLAY_CASINO'),
(7, 'TURN_SETTING_COUPON_ALL');

ALTER TABLE `coupon_cash`
	CHANGE COLUMN `turnover_to_play` `turnover_to_play` DECIMAL(10,2) NOT NULL DEFAULT '0' AFTER `coupon_turnover`;

ALTER TABLE `coupon_cash`
	CHANGE COLUMN `coupon_turnover` `coupon_turnover` BIGINT(19) NOT NULL DEFAULT '0' AFTER `coupon_total`;

ALTER TABLE `configuration_web`
	ADD COLUMN `clear_turn_credit_less` DECIMAL(14,2) NULL DEFAULT '0' AFTER `url_line`;

ALTER TABLE `transaction_status`
	CHANGE COLUMN `name` `name` ENUM('PENDING','DEPOSIT_PENDING_CREDIT','DEPOSIT_PENDING_SLIP','DEPOSIT_PENDING_MULTIUSER','DEPOSIT_CREDIT_APPROVED_GAME','DEPOSIT_CREDIT_REJECTED_GAME','WITHDRAW_PENDING','WITHDRAW_OVER_BUDGET','WITHDRAW_APPROVED','WITHDRAW_REJECTED','WITHDRAW_FAILED','WITHDRAW_SUCCESS','WITHDRAW_OVER_MAX','WITHDRAW_CANCELED','DEPOSIT_IGNORE','WITHDRAW_UNSURE') NOT NULL DEFAULT 'PENDING' COLLATE 'utf8mb4_general_ci' AFTER `id`;

INSERT INTO `transaction_status` (`id`, `label_th`, `label_en`) VALUES (16, 'โปรดตรวจสอบสเตทเม้นท์', 'please check statement');
UPDATE `transaction_status` SET `name`='WITHDRAW_UNSURE' WHERE  `id`=16;

CREATE TABLE IF NOT EXISTS `bank_transaction_external_detail`
(
	`id` BIGINT NOT NULL AUTO_INCREMENT,
	`bank_transaction_id` BIGINT NOT NULL,
	`detail` TEXT NOT NULL,
	`error_code` VARCHAR(255) DEFAULT NULL,
	`created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
	PRIMARY KEY (`id`)
);

-- MIGRATE of 2024-03-11

ALTER TABLE `affiliate_commission`
	ADD COLUMN `collectable_days` INT NOT NULL DEFAULT '0' AFTER `description`;

CREATE TABLE IF NOT EXISTS `affiliate_transaction` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `daily_key` varchar(255) DEFAULT NULL,
  `income_amount` decimal(14,2) NOT NULL DEFAULT '0.00',
  `type_id` bigint NOT NULL,
  `status_id` bigint NOT NULL,
  `transfer_at` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni_daily_key` (`daily_key`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_type_id` (`type_id`),
  INDEX `idx_status_id` (`status_id`)
);

CREATE TABLE IF NOT EXISTS `affiliate_transaction_status` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
INSERT INTO `affiliate_transaction_status` (`id`, `name`, `description`) VALUES
  (1, 'PENDING', 'รอกดรับ'),
  (2, 'EXPIRED', 'หมดอายุ'),
  (3, 'WAIT_CONFIRM', 'รออนุมัติ'),
  (4, 'TRANSFERRED', 'รับแล้ว');

CREATE TABLE IF NOT EXISTS `affiliate_transaction_type` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
INSERT INTO `affiliate_transaction_type` (`id`, `name`, `description`) VALUES
  (1, 'NEW_MEMBER', 'แนะนำเพื่อนสมัครรับรายได้'),
  (2, 'FIRST_DEPOSIT', 'แนะนำเพื่อนสมัครฝากครั้งแรก'),
  (3, 'PLAY_COMMISION', 'แนะนำเพื่อนรายได้คอมมิชชั่น');

CREATE TABLE IF NOT EXISTS `affiliate_transaction_withdraw` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `rc_key` varchar(255) DEFAULT NULL,
  `withdraw_amount` decimal(14,2) NOT NULL DEFAULT '0.00',
  `json_related_ids` text DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  INDEX `idx_user_id` (`user_id`)
);

ALTER TABLE `affiliate_transaction`
	ADD COLUMN `downline_id` BIGINT NULL DEFAULT NULL AFTER `daily_key`;

-- migration 2024-03-22 ต้องแจ้ง outsource ด้วย ที่ย้ายรูปที่เป็น config ด้วยเพราะ google drive จะเปลี่ยน folder เพื่อ check ว่า รูปไปเสียบางจากที่คุยกับ P.lay เลยกันเคสนี้โดย up ขึ้น cloudflare ไปก่อน

UPDATE `bank` SET `icon_url`='https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/kbank.png/public' WHERE `id`=1;
UPDATE `bank` SET `icon_url`='https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/scb.png/public' WHERE `id`=2;
UPDATE `bank` SET `icon_url`='https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/bbl.png/public' WHERE `id`=3;
UPDATE `bank` SET `icon_url`='https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/bay.png/public' WHERE `id`=4;
UPDATE `bank` SET `icon_url`='https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/ktb.png/public' WHERE `id`=5;
UPDATE `bank` SET `icon_url`='https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/ttb.png/public' WHERE `id`=6;
UPDATE `bank` SET `icon_url`='https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/gsb.png/public' WHERE `id`=7;
UPDATE `bank` SET `icon_url`='https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/baac.png/public' WHERE `id`=8;
UPDATE `bank` SET `icon_url`='https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/kk.png/public' WHERE `id`=9;
UPDATE `bank` SET `icon_url`='https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/ghb.png/public' WHERE `id`=10;
UPDATE `bank` SET `icon_url`='https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/uob.png/public' WHERE `id`=11;
UPDATE `bank` SET `icon_url`='https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/lh.png/public' WHERE `id`=12;
UPDATE `bank` SET `icon_url`='https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/cimb.png/public' WHERE `id`=13;
UPDATE `bank` SET `icon_url`='https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/hsbc.png/public' WHERE `id`=14;
UPDATE `bank` SET `icon_url`='https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/icbc.png/public' WHERE `id`=15;
UPDATE `bank` SET `icon_url`='https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/isbt.png/public' WHERE `id`=16;
UPDATE `bank` SET `icon_url`='https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/tisco.png/public' WHERE `id`=17;
UPDATE `bank` SET `icon_url`='https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/citi.png/public' WHERE `id`=18;
UPDATE `bank` SET `icon_url`='https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/scbt.png/public' WHERE `id`=19;
UPDATE `bank` SET `icon_url`='https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/true.png/public' WHERE `id`=20;

-- MIGRATE of 2024-03-28

CREATE TABLE IF NOT EXISTS `telegram_last_update` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `update_uid` BIGINT NOT NULL,
    PRIMARY KEY (`id`)
);

CREATE TABLE IF NOT EXISTS `telegram_chat_token` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `update_uid` BIGINT NOT NULL,
    `chat_uid` VARCHAR(255) NOT NULL,
    `chat_title` VARCHAR(255) NOT NULL,
    `chat_type` VARCHAR(255) NOT NULL,
    `token` VARCHAR(255) NOT NULL,
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
);

ALTER TABLE `paygate_heng_order`
	ADD COLUMN `remark` VARCHAR(255) NULL DEFAULT NULL AFTER `qr_promptpay`;

--   UNIQUE KEY `uni_rc_key` (`rc_key`),

-- MIGRATE of 2024-04-02

ALTER TABLE `configuration_web`
	ADD COLUMN `use_upload_deposit_slip` TINYINT(1) NOT NULL DEFAULT '0' AFTER `use_otp_register`;

DROP TABLE IF EXISTS `bank_transaction_slip`;

CREATE TABLE IF NOT EXISTS `bank_transaction_slip` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `user_id` INT(11) NOT NULL,
    `status` INT DEFAULT 1, -- 1: รอตรวจสอบ, 2: ไม่ผ่าน, 3: ผ่าน
    `transaction_id` BIGINT DEFAULT NULL,
    `raw_qr_code` VARCHAR(255) DEFAULT NULL,
    `from_account_number` VARCHAR(255) DEFAULT NULL,
    `from_account_name` VARCHAR(255) DEFAULT NULL,
    `from_bank_name` VARCHAR(255) DEFAULT NULL,
    `to_account_number` VARCHAR(255) DEFAULT NULL,
    `to_account_name` VARCHAR(255) DEFAULT NULL,
    `amount` DECIMAL(10,2) DEFAULT NULL,
    `transaction_date` DATETIME DEFAULT NULL,
    `remark` TEXT DEFAULT NULL,
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `raw_qr_code` (`raw_qr_code`)
);


-- MIGRATE of 2024-04-04

ALTER TABLE `promotion_web_user`
    ADD COLUMN `canceled_at` DATETIME DEFAULT NULL AFTER `deleted_at`;

CREATE TABLE IF NOT EXISTS `configuration_backoffice_notification` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `backoffice_is_on` TINYINT(1) NOT NULL DEFAULT '0',
  `backoffice_new_member` TINYINT(1) NOT NULL DEFAULT '0',  -- แจ้งเตือนสมัครสมาชิก
  `backoffice_deposit` TINYINT(1) NOT NULL DEFAULT '0', -- แจ้งเตือน ฝาก
  `backoffice_withdraw` TINYINT(1) NOT NULL DEFAULT '0',-- แจ้งเตือน ถอน
  `backoffice_bonus` TINYINT(1) NOT NULL DEFAULT '0',  -- แจ้งเตือน แจกโบนัสกิจกรรม (รออนุมัติ)
  `backoffice_sound_on_new_member` TINYINT(1) NOT NULL DEFAULT '0',
  `backoffice_sound_on_deposit` TINYINT(1) NOT NULL DEFAULT '0',
  `backoffice_sound_on_withdraw` TINYINT(1) NOT NULL DEFAULT '0',
  `backoffice_sound_on_bonus` TINYINT(1) NOT NULL DEFAULT '0',
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` DATETIME DEFAULT NULL,
  PRIMARY KEY (`id`)
);


CREATE TABLE IF NOT EXISTS `configuration_external_notification` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `notification_name` varchar(255) NOT NULL,

  `telegram_is_on` TINYINT(1) NOT NULL DEFAULT '0',
  `telegram_new_member` TINYINT(1) NOT NULL DEFAULT '0', -- แจ้งเตือนสมัครสมาชิก
  `telegram_before_deposit` TINYINT(1) NOT NULL DEFAULT '0', -- แจ้งเตือน ฝาก (ก่อนปรับเครดิต)
  `telegram_after_deposit` TINYINT(1) NOT NULL DEFAULT '0', -- แจ้งเตือน ฝาก (หลังปรับเครดิต)
  `telegram_withdraw_success` TINYINT(1) NOT NULL DEFAULT '0', -- แจ้งเตือน ถอนสำเร็จ
  `telegram_withdraw_pending` TINYINT(1) NOT NULL DEFAULT '0', -- แจ้งเตือน ถอน รอโอน
  `telegram_withdraw_failed` TINYINT(1) NOT NULL DEFAULT '0', -- แจ้งเตือน ถอนไม่สำเร็จ
  `telegram_pull_credit` TINYINT(1) NOT NULL DEFAULT '0', --  แจ้งเตือน ดึงเครดิตกลับ
  `telegram_bonus` TINYINT(1) NOT NULL DEFAULT '0', -- แจ้งเตือน แจกโบนัส
  `telegram_promotion` TINYINT(1) NOT NULL DEFAULT '0', -- แจ้งเตือน แจกโบนัสโปรโมชัน
  `telegram_activity_before_bonus` TINYINT(1) NOT NULL DEFAULT '0', -- แจ้งเตือน แจกโบนัสกิจกรรม (หลังปรับเครดิต)
  `telegram_activity_after_bonus` TINYINT(1) NOT NULL DEFAULT '0', -- แจ้งเตือน แจกโบนัสกิจกรรม (รออนุมัติ)

  `line_is_on` TINYINT(1) NOT NULL DEFAULT '0',
  `line_new_member` TINYINT(1) NOT NULL DEFAULT '0', -- แจ้งเตือนสมัครสมาชิก
  `line_before_deposit` TINYINT(1) NOT NULL DEFAULT '0', -- แจ้งเตือน ฝาก (ก่อนปรับเครดิต)
  `line_after_deposit` TINYINT(1) NOT NULL DEFAULT '0', -- แจ้งเตือน ฝาก (หลังปรับเครดิต)
  `line_withdraw_success` TINYINT(1) NOT NULL DEFAULT '0', -- แจ้งเตือน ถอนสำเร็จ
  `line_withdraw_pending` TINYINT(1) NOT NULL DEFAULT '0', -- แจ้งเตือน ถอน รอโอน
  `line_withdraw_failed` TINYINT(1) NOT NULL DEFAULT '0', -- แจ้งเตือน ถอนไม่สำเร็จ
  `line_pull_credit` TINYINT(1) NOT NULL DEFAULT '0', --  แจ้งเตือน ดึงเครดิตกลับ
  `line_bonus` TINYINT(1) NOT NULL DEFAULT '0', -- แจ้งเตือน แจกโบนัส
  `line_promotion` TINYINT(1) NOT NULL DEFAULT '0', -- แจ้งเตือน แจกโบนัสโปรโมชัน
  `line_activity_before_bonus` TINYINT(1) NOT NULL DEFAULT '0', -- แจ้งเตือน แจกโบนัสกิจกรรม (หลังปรับเครดิต)
  `line_activity_after_bonus` TINYINT(1) NOT NULL DEFAULT '0', -- แจ้งเตือน แจกโบนัสกิจกรรม (รออนุมัติ)

  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` DATETIME DEFAULT NULL,
  PRIMARY KEY (`id`)
);

CREATE TABLE IF NOT EXISTS `configuration_notification_token`(
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `configuration_external_notification_id` BIGINT NOT NULL,
  `configuration_token_type_id` BIGINT NOT NULL,
  `token` varchar(255) NOT NULL,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` DATETIME DEFAULT NULL,
  PRIMARY KEY (`id`)
);

CREATE TABLE IF NOT EXISTS `configuration_token_type`(
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(255) NOT NULL,
  `label_th` VARCHAR(255) NOT NULL,
  `label_en` VARCHAR(255) NOT NULL,
  `deleted_at` DATETIME DEFAULT NULL,
  PRIMARY KEY (`id`)
);

INSERT INTO `configuration_token_type` (`id`, `name`, `label_th`, `label_en`) VALUES
(1, 'TELEGRAM', 'เทเลเกรม', 'telegram'),
(2, 'LINE', 'ไลน์', 'line');

-- MIGRATE of 2024-04-10

CREATE TABLE IF NOT EXISTS `admin_log` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL,
    `admin_id` BIGINT NOT NULL,
    `json_req` TEXT NOT NULL,
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
);

UPDATE `configuration_token_type` SET `id`=2, `name`='TELEGRAMLINE', `label_th`='เทเลเกรม', `label_en`='telegram', `deleted_at`=NULL WHERE `id`=2;
UPDATE `configuration_token_type` SET `id`=1, `name`='LINE', `label_th`='ไลน์', `label_en`='line', `deleted_at`=NULL WHERE `id`=1;

ALTER TABLE `recommend_channel`
	ADD COLUMN `graph_color` VARCHAR(255) NOT NULL DEFAULT '' AFTER `title`;

ALTER TABLE `user_alliance`
	ADD COLUMN `ref_code` VARCHAR(255) NULL DEFAULT NULL AFTER `alias`;

CREATE UNIQUE INDEX `uni_ref_code` ON `user_alliance` (`ref_code`);

INSERT INTO `admin_action_type` (`id`, `name`, `detail`) VALUES (11, 'MANAGE_USER', 'Manage User');

UPDATE `auto_withdraw_type` SET `label_th`='ไม่ออโต้ รอแอดมินอนุมัติ' WHERE  `id`=1;

-- MIGRATE of 2024-04-23

CREATE TABLE IF NOT EXISTS `paygate_luckyth_order_type`(
  `id` BIGINT NOT NULL,
  `name` VARCHAR(255) NOT NULL,
  `label_th` VARCHAR(255) NOT NULL,
  `label_en` VARCHAR(255) NOT NULL,
  `deleted_at` DATETIME DEFAULT NULL,
  PRIMARY KEY (`id`)
);
INSERT INTO `paygate_luckyth_order_type` (`id`, `name`, `label_th`, `label_en`) VALUES
(1, 'DEPOSIT', 'ฝากเงิน', 'deposit'),
(2, 'WITHDRAW', 'ถอนเงิน', 'withdraw');

CREATE TABLE IF NOT EXISTS `paygate_luckyth_order` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT NOT NULL,
    `order_type_id` BIGINT NOT NULL,
    `order_no` VARCHAR(255) NOT NULL,
    `amount` DECIMAL(10,2) NOT NULL,
    `transaction_no` VARCHAR(255) NULL,
    `transaction_date` DATETIME  NULL,
    `transaction_status` VARCHAR(255) NULL,
    `payment_at` DATETIME NULL DEFAULT NULL,
    `qr_promptpay` VARCHAR(255) NULL,
    `remark` VARCHAR(255) NULL DEFAULT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
    PRIMARY KEY (`id`)
);
CREATE UNIQUE INDEX `uni_order_no` ON `paygate_luckyth_order` (`order_no`);
CREATE UNIQUE INDEX `uni_transaction_no` ON `paygate_luckyth_order` (`transaction_no`);
CREATE INDEX `idx_order_type_id` ON `paygate_luckyth_order` (`order_type_id`);

CREATE TABLE IF NOT EXISTS `paygate_luckyth_webhook` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL,
    `json_payload` LONGTEXT NOT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    PRIMARY KEY (`id`)
);

ALTER TABLE `bank_account`
	ADD COLUMN `sms_mode` TINYINT(3) NOT NULL DEFAULT '0' AFTER `admin_updated_at`;

-- MIGRATE of 2024-05-02

CREATE TABLE IF NOT EXISTS `paygate_merchant` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL,
    `type_name` VARCHAR(255) NOT NULL,
    `has_deposit` TINYINT NOT NULL DEFAULT 0,
    `is_deposit_enabled` TINYINT NOT NULL DEFAULT 0,
    `has_withdraw` TINYINT NOT NULL DEFAULT 0,
    `is_withdraw_enabled` TINYINT NOT NULL DEFAULT 0,
    `api_end_point` VARCHAR(255) NULL,
    `shop_name` VARCHAR(255) NULL,
    `username` VARCHAR(255) NULL,
    `password` VARCHAR(255) NULL,
    `private_key` VARCHAR(255) NULL,
    `balance` DECIMAL(10,2) NOT NULL DEFAULT 0,
    `merchant_update_at` DATETIME NULL,
    `callback_url` VARCHAR(255) NULL,
    `created_at` DATETIME DEFAULT NOW(),
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
    `deleted_at` DATETIME DEFAULT NULL,
    PRIMARY KEY (`id`)
);
INSERT INTO `paygate_merchant` (`name`, `type_name`, `has_deposit`, `is_deposit_enabled`, `has_withdraw`, `is_withdraw_enabled`, `api_end_point`, `shop_name`, `username`, `password`, `balance`, `callback_url`) VALUES
('HENG', 'HENG', 1, 0, 0, 0, 'https://api-proxy.cbgame88.com/hengpay/api/v2', '', '', '', 0, ''),
('LUCKYTH', 'LUCKYTH', 1, 0, 1, 0, 'https://api7.luckythb.xyz', '', '', '', 0, '');

UPDATE `paygate_merchant` INNER JOIN paygate_heng_setting ON paygate_heng_setting.name = paygate_merchant.name
SET `paygate_merchant`.`is_deposit_enabled`=paygate_heng_setting.is_enabled,
`paygate_merchant`.`shop_name`=paygate_heng_setting.shop_name,
`paygate_merchant`.`username`=paygate_heng_setting.username,
`paygate_merchant`.`password`=paygate_heng_setting.password
WHERE `paygate_merchant`.`id` = 1;

CREATE TABLE IF NOT EXISTS `paygate_setting` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `merchant_id` BIGINT NOT NULL,
    `status` VARCHAR(255) NOT NULL DEFAULT 'PENDING',
    `created_at` DATETIME DEFAULT NOW(),
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
    PRIMARY KEY (`id`)
);
INSERT INTO `paygate_setting` (`merchant_id`) VALUES (0);

CREATE TABLE IF NOT EXISTS `paygate_admin_log` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `admin_id` BIGINT NOT NULL,
    `name` VARCHAR(255) NOT NULL,
    `status` VARCHAR(255) NOT NULL,
    `json_req` TEXT NOT NULL,
    `json_response` TEXT NOT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
    PRIMARY KEY (`id`)
);

CREATE TABLE IF NOT EXISTS `paygate_system_log` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL,
    `status` VARCHAR(255) NOT NULL,
    `json_req` TEXT NOT NULL,
    `json_response` TEXT NOT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
    PRIMARY KEY (`id`)
);

-- TURNOVER SETTING

CREATE TABLE IF NOT EXISTS `turnover_setting` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `tidturn_manual_bonus_percent` INT NOT NULL,
    `tidturn_link_register_percent` INT NOT NULL,
    `tidturn_first_deposit_percent` INT NOT NULL,
    `tidturn_aff_commission_percent` INT NOT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
    PRIMARY KEY (`id`)
);
INSERT INTO `turnover_setting` (`tidturn_manual_bonus_percent`, `tidturn_link_register_percent`, `tidturn_first_deposit_percent`, `tidturn_aff_commission_percent`) VALUES (100, 100, 100, 0);

ALTER TABLE `transaction_status`
	CHANGE COLUMN `name` `name` ENUM('PENDING','DEPOSIT_PENDING_CREDIT','DEPOSIT_PENDING_SLIP','DEPOSIT_PENDING_MULTIUSER','DEPOSIT_CREDIT_APPROVED_GAME','DEPOSIT_CREDIT_REJECTED_GAME','WITHDRAW_PENDING','WITHDRAW_OVER_BUDGET','WITHDRAW_APPROVED','WITHDRAW_REJECTED','WITHDRAW_FAILED','WITHDRAW_SUCCESS','WITHDRAW_OVER_MAX','WITHDRAW_CANCELED','DEPOSIT_IGNORE','WITHDRAW_UNSURE','TRANSFERING') NOT NULL DEFAULT 'PENDING' COLLATE 'utf8mb4_general_ci' AFTER `id`;

INSERT INTO `transaction_status` (`id`, `name`, `label_th`, `label_en`) VALUES (17, 'TRANSFERING', 'อยู่ในระหว่างการโอนเงิน', 'transfering money');

ALTER TABLE `paygate_luckyth_order`
	ADD COLUMN `ref_id` BIGINT(19) NULL DEFAULT NULL AFTER `order_type_id`;

INSERT INTO `turnover_statement_type` (`id`, `name`) VALUES
(8,'TURN_BONUS_BY_ADMIN');

UPDATE `turnover_statement_type` SET `name` =  'TURN_PROMOTION_SETTING_PLAY_ALL' WHERE `id` = 3;
UPDATE `turnover_statement_type` SET `name` =  'TURN_PROMOTION_SETTING_PLAY_GAME' WHERE `id` = 4;
UPDATE `turnover_statement_type` SET `name` =  'TURN_PROMOTION_SETTING_PLAY_SPORT' WHERE `id` = 5;
UPDATE `turnover_statement_type` SET `name` =  'TURN_PROMOTION_SETTING_PLAY_CASINO' WHERE `id` = 6;

ALTER TABLE `turnover_statement`
	ADD COLUMN `name` VARCHAR(255) NULL DEFAULT NULL AFTER `ref_type_id`;

INSERT INTO `turnover_statement_type` (`id`, `name`) VALUES
  (9,'TURN_BONUS_AFF_TYPE_NEW_REGISTER');
INSERT INTO `turnover_statement_type` (`id`, `name`) VALUES
  (10,'TURN_BONUS_AFF_TYPE_FIRST_DEPOSIT ');
INSERT INTO `turnover_statement_type` (`id`, `name`) VALUES
  (11,'TURN_BONUS_AFF_TYPE_PLAY_COMMISSION ');


-- MIGRATE of 2024-05-10
CREATE TABLE IF NOT EXISTS `exchange_currency` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `name_th` VARCHAR(255) NOT NULL,
  `name_en` VARCHAR(255) NOT NULL,
  `currency_code` VARCHAR(3) NOT NULL,
  `currency_name_th` VARCHAR(255) NOT NULL,
  `currency_name_en` VARCHAR(255) NOT NULL,
  `image_url` VARCHAR(255) NOT NULL,
  `is_main` TINYINT DEFAULT 0,
  PRIMARY KEY (`id`),
  UNIQUE KEY `currency_code_unique` (`currency_code`)
);

INSERT INTO `exchange_currency` (`id`, `name_th`, `name_en`, `currency_code`, `currency_name_th`, `currency_name_en`, `image_url`, `is_main`) VALUES
(1,'ไทย','Thailand','THB','บาท','Baht','https://www.countryflags.com/wp-content/uploads/thailand-flag-png-large.png',1),
(2,'ลาว','Laos','LAK','กีบ','Kip','https://www.countryflags.com/wp-content/uploads/laos-flag-png-large.png',0),
(3,'เมียนมา','Myanmar','MMK','เจา','Kyat','https://www.countryflags.com/wp-content/uploads/myanmar-flag-png-large.png',0);

CREATE TABLE IF NOT EXISTS `exchange_rate` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `exchange_currency_id` BIGINT NOT NULL,
  `exchange_rate` DECIMAL(10,5) NOT NULL,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` DATETIME DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `exchange_currency_id_unique` (`exchange_currency_id`),
  KEY `exchange_currency_id_index` (`exchange_currency_id`)
);

INSERT INTO `exchange_rate` (`id`, `exchange_currency_id`, `exchange_rate`) VALUES
(1,1,1),
(2,2,580.01465);



ALTER TABLE `configuration_web` ADD COLUMN `use_th_currency` TINYINT(1) NOT NULL DEFAULT '1' AFTER `use_upload_deposit_slip`;
ALTER TABLE `configuration_web` ADD COLUMN `use_laos_currency` TINYINT(1) NOT NULL DEFAULT '0' AFTER `use_th_currency`;

ALTER TABLE `bank` ADD COLUMN `country_code` VARCHAR(50) NULL DEFAULT NULL AFTER `type_flag`;
UPDATE `bank` SET `country_code` = 'TH' ;

ALTER TABLE `bank` ADD COLUMN `use_currency` VARCHAR(50) NULL DEFAULT NULL AFTER `country_code`;

UPDATE `bank` SET `use_currency` = 'THB' ;


INSERT INTO `bank` (`id`, `name`, `code`, `icon_url`, `type_flag`,`country_code`,`use_currency`) VALUES
(50, 'ธนาคารการค้าต่างประเทศลาว (LAK)', 'BCEL', 'https://storage.googleapis.com/cbgame/banks/none.png', '********','LAOS','LAK'),
(51, 'ธนาคารการค้าต่างประเทศลาว (THB)', 'BCELTH', 'https://storage.googleapis.com/cbgame/banks/none.png', '********','LAOS','THB'),
(52, 'ธนาคารพัฒนาลาว', 'LDB', 'https://storage.googleapis.com/cbgame/banks/none.png', '********','LAOS','LAK'),
(53, 'ธนาคารส่งเสริมการเกษตร', 'APB', 'https://storage.googleapis.com/cbgame/banks/none.png', '********','LAOS','LAK'),
(54, 'ธนาคารร่วมธุรกิจลาว', 'BOL', 'https://storage.googleapis.com/cbgame/banks/none.png', '********','LAOS','LAK'),
(55, 'ธนาคารร่วมพัฒนา', 'LDBBLALA XXX', 'https://storage.googleapis.com/cbgame/banks/none.png', '********','LAOS','LAK'),
(56, 'ธนาคาร ST', 'ST', 'https://storage.googleapis.com/cbgame/banks/none.png', '********','LAOS','LAK'),
(57, 'ธนาคาร BIC', 'BIC', 'https://storage.googleapis.com/cbgame/banks/none.png', '********','LAOS','LAK'),
(58, 'ธนาคาร Maruhan Japan', 'Maruhan Japan', 'https://storage.googleapis.com/cbgame/banks/none.png', '********','LAOS','LAK'),
(59, 'ธนาคาร Sacombank', 'Sacombank', 'https://storage.googleapis.com/cbgame/banks/none.png', '********','LAOS','LAK'),
(60, 'ธนาคารแห่งประเทศจีน', 'BKCHTHBK', 'https://storage.googleapis.com/cbgame/banks/none.png', '********','LAOS','LAK'),
(61, 'ธนาคาร Vietin', 'Vietin', 'https://storage.googleapis.com/cbgame/banks/none.png', '********','LAOS','LAK'),
(62, 'ธนาคาร ACLEDA', 'ACLEDA', 'https://storage.googleapis.com/cbgame/banks/none.png', '********','LAOS','LAK');


ALTER TABLE `bank_transaction`
	ADD COLUMN `currency_amount` DECIMAL(14,2) NULL DEFAULT '0.00' AFTER `credit_amount`;

-- MIGRATE of 2024-05-14
  ALTER TABLE `configuration_web` ADD COLUMN `id_whatsapp` VARCHAR(255) NOT NULL DEFAULT '' AFTER `url_line`;
ALTER TABLE `configuration_web` ADD COLUMN `url_whatsapp` VARCHAR(255) NOT NULL DEFAULT '' AFTER `id_whatsapp`;

-- exchange_update_log
CREATE TABLE IF NOT EXISTS `exchange_update_log` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `exchange_currency_id` BIGINT NOT NULL,
  `default_main_rate` DECIMAL(10,2) NOT NULL,
  `exchange_rate` DECIMAL(10,2) NOT NULL,
  `currency_name_th` VARCHAR(255) NOT NULL,
  `currency_name_en` VARCHAR(255) NOT NULL,
  `created_admin_id` BIGINT NOT NULL,
  `created_admin_name` VARCHAR(255) NOT NULL,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);

-- MIGRATE of 2024-05-21

CREATE TABLE IF NOT EXISTS `order_category` (
	`id` BIGINT NOT NULL AUTO_INCREMENT,
	`name` VARCHAR(255) NOT NULL,
	`created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
	`updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
	`deleted_at` DATETIME DEFAULT NULL,
	PRIMARY KEY (`id`)
);
INSERT INTO `order_category` (`id`, `name`) VALUES
(1,'LOTTERY');

CREATE TABLE IF NOT EXISTS `order_type` (
	`id` BIGINT NOT NULL AUTO_INCREMENT,
	`name` VARCHAR(255) NOT NULL,
	`label_th` VARCHAR(255) NOT NULL,
	`label_en` VARCHAR(255) NOT NULL,
	`created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
	`updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
	`deleted_at` DATETIME DEFAULT NULL,
	PRIMARY KEY (`id`)
);

INSERT INTO `order_type` (`id`, `name`, `label_th`, `label_en`) VALUES
(1,'LOTTERY_BET','แทงหวย','LOTTERY_BET'),
(2,'LOTTERY_WIN','ถูกรางวัล','LOTTERY_WIN'),
(3,'LOTTERY_REFUND','คืนเงิน','LOTTERY_REFUND'),
(4,'LOTTERY_COMMISSION','คอมมิชชั่น','LOTTERY_COMMISSION');

CREATE TABLE IF NOT EXISTS `order` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `order_category_id` BIGINT NOT NULL,
  `order_type_id` BIGINT NOT NULL,
  `ref1_no` VARCHAR(255) NOT NULL COMMENT 'external unique with order_type_id',
  `ref2_no` VARCHAR(255) NOT NULL COMMENT 'internal',
  `user_id` BIGINT NOT NULL,
  `amount` DECIMAL(10,2) NOT NULL,
  `order_status_id` BIGINT NOT NULL,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `cancelled_at` DATETIME DEFAULT NULL,
  `deleted_at` DATETIME DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ref1_no_order_unique` (`ref1_no`, `order_type_id`, `order_category_id`),
  UNIQUE KEY `ref2_no_unique` (`ref2_no`),
  KEY `order_user_id_index` (`user_id`),
  KEY `order_transaction_id_index` (`order_type_id`)
);

CREATE TABLE IF NOT EXISTS `order_race_condition` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `action_key` VARCHAR(255) NOT NULL,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` DATETIME DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `action_key_unique` (`action_key`)
);

INSERT INTO `user_transaction_type` (`id`, `name`) VALUES (12, 'CREDIT_TYPE_LOTTERY');

CREATE TABLE IF NOT EXISTS `order_status` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(255) NOT NULL,
  `label_th` VARCHAR(255) NOT NULL,
  `label_en` VARCHAR(255) NOT NULL,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` DATETIME DEFAULT NULL,
  PRIMARY KEY (`id`)
);

INSERT INTO `order_status` (`id`, `name`, `label_th`, `label_en`) VALUES
(1,'PENDING','รอดำเนินการ','PENDING'),
(2,'SUCCESS','สำเร็จ','SUCCESS'),
(3,'FAILED','ไม่สำเร็จ','FAILED');

CREATE TABLE IF NOT EXISTS  `order_log` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `order_id` BIGINT DEFAULT NULL,
  `json_req` TEXT DEFAULT NULL,
  `remark` VARCHAR(255) DEFAULT NULL,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);

-- ยังไม่ได้เพิ่ม screct prod
-- SECRET_KEY_ORDER_GAME= secret_key_order_game

INSERT INTO `promotion_return_cut_type` (`id`, `name`) VALUES (2, 'รายสัปดาห์');

ALTER TABLE `bank_transaction`
	ADD COLUMN `slip_img_url` VARCHAR(1000) NULL DEFAULT NULL AFTER `deposit_channel`;

ALTER TABLE `configuration_web`
	ADD COLUMN `upload_deposit_slip_type` VARCHAR(255) NULL DEFAULT NULL AFTER `use_upload_deposit_slip`;

UPDATE `configuration_web` SET `upload_deposit_slip_type`='DISABLED';

CREATE TABLE IF NOT EXISTS `configuration_bank_limit` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `max_user_withdraw_amount` DECIMAL(14,2) NOT NULL DEFAULT 0,
  `max_user_withdraw_count` INT NOT NULL DEFAULT 0,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);

ALTER TABLE `affiliate_commission`
	ADD COLUMN `max_commission` INT NOT NULL DEFAULT '0' AFTER `description`;

ALTER TABLE `turnover_setting`
	ADD COLUMN `tidturn_return_loss_percent` INT NOT NULL DEFAULT '100' AFTER `tidturn_aff_commission_percent`;

-- MIGRATE of 2024-05-27

ALTER TABLE `user_affiliate`
	ADD COLUMN `total_withdraw` DECIMAL(14,2) NULL DEFAULT '0.00' AFTER `commission_current`;

UPDATE `user_affiliate` SET `total_withdraw` = `commission_total` - `commission_current`;

CREATE TABLE IF NOT EXISTS `user_playlog_summary` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `user_id` BIGINT NOT NULL,
  `playlog_start_date` DATE NOT NULL,
  `total_turn` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `total_win_loss` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
CREATE INDEX `idx_user_id` ON `user_playlog_summary` (`user_id`);

-- MIGRATE of 2024-06-05

ALTER TABLE `exchange_currency`
ADD COLUMN `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
ADD COLUMN `deleted_at` DATETIME DEFAULT NULL;

-- MIGRATE of 2024-06-07

ALTER TABLE `paygate_heng_order`
	ADD COLUMN `bank_transaction_id` BIGINT NULL DEFAULT NULL AFTER `payment_at`;

UPDATE `paygate_heng_order` SET `bank_transaction_id`=0 WHERE  `bank_transaction_id` IS NULL AND `transaction_status`='SUCCESS' AND `remark` IS NULL;

ALTER TABLE `paygate_heng_order`
	ADD COLUMN `payment_bank_no` VARCHAR(255) NOT NULL DEFAULT '' AFTER `payment_at`,
	ADD COLUMN `payment_name` VARCHAR(255) NOT NULL DEFAULT '' AFTER `payment_bank_no`,
	ADD COLUMN `payment_bank_code` VARCHAR(255) NOT NULL DEFAULT '' AFTER `payment_name`,
	ADD COLUMN `payment_bank_name` VARCHAR(255) NOT NULL DEFAULT '' AFTER `payment_bank_code`;

ALTER TABLE `paygate_heng_order`
	ADD COLUMN `bank_transaction_status` VARCHAR(255) NULL DEFAULT NULL AFTER `bank_transaction_id`;

CREATE TABLE IF NOT EXISTS `paygate_heng_user` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `user_id` BIGINT NOT NULL,
  `account_no` VARCHAR(255) NOT NULL,
  `account_name` VARCHAR(255) NOT NULL,
  `bank_no` VARCHAR(255) NOT NULL,
  `bank_code` VARCHAR(255) NOT NULL,
  `bank_name` VARCHAR(255) NOT NULL,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
CREATE UNIQUE INDEX `idx_user_id` ON `paygate_heng_user` (`user_id`);

-- MIGRATE of 2024-06-20

INSERT INTO `user_income_log_status` (`id`, `name`, `detail`) VALUES (3, 'CANCELED', 'ไม่อนุมัติ');

ALTER TABLE `affiliate_commission`
	ADD COLUMN `max_commission_per_line` INT NOT NULL DEFAULT '0' AFTER `max_commission`;

CREATE TABLE IF NOT EXISTS `affiliate_downline_total_income` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `user_id` BIGINT NOT NULL,
  `total_commission` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
CREATE UNIQUE INDEX `idx_user_id` ON `affiliate_downline_total_income` (`user_id`);

-- MIGRATE of 2024-06-26

INSERT INTO `promotion_web_status` (`id`, `name`, `label_th`, `label_en`)  VALUES (4, 'SHOW_ONLY', 'เปิดแสดงแต่ไม่ใช้งาน', 'show only');

-- MIGRATE of 2024-07-12

/* priority_order */
ALTER TABLE `promotion_web`
	ADD COLUMN `priority_order` BIGINT(19) NOT NULL DEFAULT '0' AFTER `id`;

  UPDATE `promotion_web`
SET `priority_order` = `id`;

CREATE TABLE IF NOT EXISTS `paygate_papaya_order_type`(
  `id` BIGINT NOT NULL,
  `name` VARCHAR(255) NOT NULL,
  `label_th` VARCHAR(255) NOT NULL,
  `label_en` VARCHAR(255) NOT NULL,
  `deleted_at` DATETIME DEFAULT NULL,
  PRIMARY KEY (`id`)
);
INSERT INTO `paygate_papaya_order_type` (`id`, `name`, `label_th`, `label_en`) VALUES
(1, 'DEPOSIT', 'ฝากเงิน', 'deposit'),
(2, 'WITHDRAW', 'ถอนเงิน', 'withdraw');

CREATE TABLE IF NOT EXISTS `paygate_papaya_order` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT NOT NULL,
    `order_type_id` BIGINT NOT NULL,
    `order_no` VARCHAR(255) NOT NULL,
    `amount` DECIMAL(10,2) NOT NULL,
    `transaction_no` VARCHAR(255) NULL,
    `transaction_date` DATETIME  NULL,
    `transaction_status` VARCHAR(255) NULL,
    `payment_at` DATETIME NULL DEFAULT NULL,
    `qr_promptpay` VARCHAR(255) NULL,
    `remark` VARCHAR(255) NULL DEFAULT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
    PRIMARY KEY (`id`)
);
CREATE UNIQUE INDEX `uni_order_no` ON `paygate_papaya_order` (`order_no`);
CREATE UNIQUE INDEX `uni_transaction_no` ON `paygate_papaya_order` (`transaction_no`);
CREATE INDEX `idx_order_type_id` ON `paygate_papaya_order` (`order_type_id`);

CREATE TABLE IF NOT EXISTS `paygate_papaya_webhook` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL,
    `json_payload` LONGTEXT NOT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    PRIMARY KEY (`id`)
);

INSERT INTO `paygate_merchant` (`name`, `type_name`, `has_deposit`, `is_deposit_enabled`, `has_withdraw`, `is_withdraw_enabled`, `api_end_point`, `shop_name`, `username`, `password`, `balance`, `callback_url`) VALUES
('PAPAYAPAY', 'PAPAYAPAY', 1, 0, 1, 0, 'https://scb.xyzonline.app', '', '', '', 0, '');

ALTER TABLE `paygate_papaya_order`
	ADD COLUMN `ref_id` BIGINT NULL DEFAULT NULL AFTER `order_type_id`;

ALTER TABLE `configuration_web`
	ADD COLUMN `allow_online_register_form` TINYINT(1) NOT NULL DEFAULT '1' AFTER `allow_online_registration`,
	ADD COLUMN `check_account_name_fastbank` TINYINT(1) NOT NULL DEFAULT '0' AFTER `allow_online_register_form`;

-- MIGRATE of 2024-07-26
ALTER TABLE `paygate_merchant`
	ADD COLUMN `access_key` VARCHAR(255) NULL DEFAULT NULL AFTER `private_key`,
	ADD COLUMN `secret_key` VARCHAR(255) NULL DEFAULT NULL AFTER `access_key`;

CREATE TABLE IF NOT EXISTS `paygate_payonex_token` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `access_token` VARCHAR(2048) NOT NULL,
    `expire_at` DATETIME NOT NULL,
    `create_by` BIGINT NOT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
    PRIMARY KEY (`id`)
);

CREATE TABLE IF NOT EXISTS `paygate_payonex_customer`(
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `user_id` BIGINT NOT NULL,
  `customer_uuid` VARCHAR(255) NOT NULL,
  `full_name` VARCHAR(255) NOT NULL,
  `bank_code` VARCHAR(255) NOT NULL,
  `account_no` VARCHAR(255) NOT NULL,
  `account_name` VARCHAR(255) NOT NULL,
  PRIMARY KEY (`id`)
);
CREATE UNIQUE INDEX `uni_user_id` ON `paygate_payonex_customer` (`user_id`);

CREATE TABLE IF NOT EXISTS `paygate_payonex_order_type`(
  `id` BIGINT NOT NULL,
  `name` VARCHAR(255) NOT NULL,
  `label_th` VARCHAR(255) NOT NULL,
  `label_en` VARCHAR(255) NOT NULL,
  PRIMARY KEY (`id`)
);
INSERT INTO `paygate_payonex_order_type` (`id`, `name`, `label_th`, `label_en`) VALUES
(1, 'DEPOSIT', 'ฝากเงิน', 'deposit'),
(2, 'WITHDRAW', 'ถอนเงิน', 'withdraw');

CREATE TABLE IF NOT EXISTS `paygate_payonex_order` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT NOT NULL,
    `order_type_id` BIGINT NOT NULL,
    `ref_id` BIGINT NULL DEFAULT NULL,
    `order_no` VARCHAR(255) NOT NULL,
    `amount` DECIMAL(10,2) NOT NULL DEFAULT 0,
    `transfer_amount` DECIMAL(10,2) NOT NULL DEFAULT 0,
    `transaction_no` VARCHAR(255) NULL,
    `transaction_date` DATETIME  NULL,
    `transaction_status` VARCHAR(255) NULL,
    `payment_at` DATETIME NULL DEFAULT NULL,
    `qr_promptpay` VARCHAR(255) NULL,
    `note` VARCHAR(255) NULL DEFAULT NULL,
    `remark` VARCHAR(255) NULL DEFAULT NULL,
    `api_remark` VARCHAR(255) NULL DEFAULT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
    PRIMARY KEY (`id`)
);
CREATE UNIQUE INDEX `uni_order_no` ON `paygate_payonex_order` (`order_no`);
CREATE UNIQUE INDEX `uni_transaction_no` ON `paygate_payonex_order` (`transaction_no`);
CREATE INDEX `idx_order_type_id` ON `paygate_payonex_order` (`order_type_id`);

CREATE TABLE IF NOT EXISTS `paygate_payonex_webhook` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL,
    `json_payload` LONGTEXT NOT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    PRIMARY KEY (`id`)
);

INSERT INTO `paygate_merchant` (`id`, `name`, `type_name`, `has_deposit`, `is_deposit_enabled`, `has_withdraw`, `is_withdraw_enabled`, `api_end_point`, `shop_name`, `username`, `password`, `balance`, `callback_url`)
VALUES (4, 'PAYONEX', 'PAYONEX', 1, 0, 1, 0, 'https://api.payonex.asia', '', '', '', 0, '');


ALTER TABLE `configuration_web`
	ADD COLUMN `withdraw_maximum_auto` DECIMAL(14,2) NULL DEFAULT '3000.00' AFTER `minimum_withdraw`;
ALTER TABLE `configuration_web`
	ADD COLUMN `withdraw_maximum` DECIMAL(14,2) NULL DEFAULT '49999.00' AFTER `withdraw_maximum_auto`;


ALTER TABLE `paygate_merchant`
	ADD COLUMN `payment_deposit_minimum` DECIMAL(10,2) NOT NULL DEFAULT '0' AFTER `balance`;

ALTER TABLE `paygate_merchant`
	ADD COLUMN `payment_withdraw_minimum` DECIMAL(10,2) NOT NULL DEFAULT '0' AFTER `payment_deposit_minimum`;

ALTER TABLE `paygate_merchant`
  ADD COLUMN `payment_withdraw_maximum` DECIMAL(10,2) NOT NULL DEFAULT '0' AFTER `payment_withdraw_minimum`;

ALTER TABLE `paygate_merchant`
	ADD COLUMN `payment_deposit_maximum` DECIMAL(10,2) NOT NULL DEFAULT '0' AFTER `payment_deposit_minimum`;

  UPDATE `paygate_merchant`
SET
    `payment_withdraw_maximum`= 0,
    `payment_withdraw_minimum` = 0,
    `payment_deposit_minimum` = 100,
    `payment_deposit_maximum` = 2000000
WHERE type_name = 'HENG';

  UPDATE `paygate_merchant`
SET
    `payment_withdraw_maximum`= 50000,
    `payment_withdraw_minimum` = 100,
    `payment_deposit_minimum` = 100,
    `payment_deposit_maximum` = 50000
WHERE type_name = 'LUCKYTH';

  UPDATE `paygate_merchant`
SET
    `payment_withdraw_maximum`= 50000,
    `payment_withdraw_minimum` = 100,
    `payment_deposit_minimum` = 100,
    `payment_deposit_maximum` = 2000000
WHERE type_name = 'PAPAYAPAY';

  UPDATE `paygate_merchant`
SET
    `payment_withdraw_maximum`= 2000000,
    `payment_withdraw_minimum` = 500,
    `payment_deposit_minimum` = 20,
    `payment_deposit_maximum` = 200000
WHERE type_name = 'PAYONEX';

-- MIGRATE of 2024-08-02

UPDATE `paygate_merchant` SET `api_end_point`='https://api-proxy.cbgame88.com/payonex' WHERE `id`=4;


ALTER TABLE `affiliate_commission`
	CHANGE COLUMN `max_commission` `max_commission` DECIMAL(14,2) NULL DEFAULT 0 AFTER `description`;

ALTER TABLE `affiliate_commission`
	CHANGE COLUMN `max_commission_per_line` `max_commission_per_line` DECIMAL(14,2) NULL DEFAULT 0 AFTER `max_commission`;

-- MIGRATE of 2024-08-20

-- new sms mode
INSERT INTO `bank` (`id`, `name`, `code`, `icon_url`, `type_flag`,`country_code`,`use_currency`) VALUES
(100, 'ไม่พบข้อมูลธนาคาร', 'unknown', 'https://storage.googleapis.com/cbgame/banks/none.png', '********','TH','THB');


CREATE TABLE IF NOT EXISTS `paygate_smsmode_deposit` (
   `id` BIGINT NOT NULL AUTO_INCREMENT,
   `user_id` BIGINT NOT NULL,
   `ref_id` BIGINT NULL DEFAULT NULL COMMENT 'ref_id ของ bank transaction',
   `account_from` VARCHAR(255) NOT NULL COMMENT 'เลขบัญชีที่โอนเข้ามา',
   `bank_account_id` BIGINT NOT NULL COMMENT 'id ของ bank',
   `bank_account_no` VARCHAR(255) NOT NULL COMMENT 'เลขบัญชีของ bof',
   `bank_code` VARCHAR(255) NOT NULL COMMENT 'รหัสธนาคารของ bof',
   `order_no` VARCHAR(255) NOT NULL COMMENT 'เรา gen ให้ fastbank',
   `amount` DECIMAL(10,2) NOT NULL DEFAULT 0,
   `transfer_amount` DECIMAL(10,2) NOT NULL DEFAULT 0,
   `transaction_no` VARCHAR(255) NULL COMMENT 'เลขที่อ้างอิง id fastbank',
   `transaction_date` DATETIME  NULL,
   `transaction_status` VARCHAR(255) NULL,
   `payment_at` DATETIME NULL DEFAULT NULL,
   `remark` VARCHAR(255) NULL DEFAULT NULL,
   `created_at` DATETIME DEFAULT NOW(),
   `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
   PRIMARY KEY (`id`),
   UNIQUE KEY `uni_order_no` (`order_no`),
   UNIQUE KEY `uni_transaction_no` (`transaction_no`)
);


UPDATE `bank` SET `code`='kkp' WHERE  `id`=9;


ALTER TABLE `configuration_web`
	ADD COLUMN `id_telegram` VARCHAR(255) NOT NULL DEFAULT '' AFTER `url_whatsapp`;

ALTER TABLE `configuration_web`
	ADD COLUMN `url_telegram` VARCHAR(255) NOT NULL DEFAULT '' AFTER `id_telegram`;

-- MIGRATE of 2024-08-30

CREATE UNIQUE INDEX `uni_ref_id` ON `paygate_payonex_order` (`ref_id`);

-- JBpayment Gateway

ALTER TABLE `paygate_merchant`
	ADD COLUMN `partner_key` VARCHAR(255) NULL DEFAULT NULL AFTER `secret_key`,
	ADD COLUMN `app_id` VARCHAR(255) NULL DEFAULT NULL AFTER `partner_key`,
	ADD COLUMN `merchant_id` VARCHAR(255) NULL DEFAULT NULL AFTER `app_id`,
	ADD COLUMN `token` VARCHAR(255) NULL DEFAULT NULL AFTER `merchant_id`,
	ADD COLUMN `aes_key` VARCHAR(255) NULL DEFAULT NULL AFTER `token`;

ALTER TABLE `paygate_merchant`
	CHANGE COLUMN `app_id` `repay_app_id` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci' AFTER `partner_key`,
	ADD COLUMN `loan_app_id` VARCHAR(255) NULL DEFAULT NULL AFTER `repay_app_id`;

CREATE TABLE IF NOT EXISTS `paygate_jbpay_order_type`(
  `id` BIGINT NOT NULL,
  `name` VARCHAR(255) NOT NULL,
  `label_th` VARCHAR(255) NOT NULL,
  `label_en` VARCHAR(255) NOT NULL,
  PRIMARY KEY (`id`)
);
INSERT INTO `paygate_jbpay_order_type` (`id`, `name`, `label_th`, `label_en`) VALUES
(1, 'DEPOSIT', 'ฝากเงิน', 'deposit'),
(2, 'WITHDRAW', 'ถอนเงิน', 'withdraw');

CREATE TABLE IF NOT EXISTS `paygate_jbpay_order` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT NOT NULL,
    `order_type_id` BIGINT NOT NULL,
    `ref_id` BIGINT NULL DEFAULT NULL,
    `order_no` VARCHAR(255) NOT NULL,
    `amount` DECIMAL(10,2) NOT NULL DEFAULT 0,
    `transfer_amount` DECIMAL(10,2) NOT NULL DEFAULT 0,
    `transaction_no` VARCHAR(255) NULL,
    `transaction_date` DATETIME  NULL,
    `transaction_status` VARCHAR(255) NULL,
    `payment_at` DATETIME NULL DEFAULT NULL,
    `qr_promptpay` VARCHAR(255) NULL,
    `payment_page_url` VARCHAR(255) NULL,
    `note` VARCHAR(255) NULL DEFAULT NULL,
    `remark` VARCHAR(1000) NULL DEFAULT NULL,
    `api_remark` VARCHAR(255) NULL DEFAULT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
    PRIMARY KEY (`id`)
);
CREATE UNIQUE INDEX `uni_order_no` ON `paygate_jbpay_order` (`order_no`);
CREATE UNIQUE INDEX `uni_transaction_no` ON `paygate_jbpay_order` (`transaction_no`);
CREATE INDEX `idx_order_type_id` ON `paygate_jbpay_order` (`order_type_id`);

CREATE TABLE IF NOT EXISTS `paygate_jbpay_webhook` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL,
    `json_payload` LONGTEXT NOT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    PRIMARY KEY (`id`)
);

INSERT INTO `paygate_merchant` (`id`, `name`, `type_name`, `has_deposit`, `is_deposit_enabled`, `has_withdraw`, `is_withdraw_enabled`, `api_end_point`, `shop_name`, `username`, `password`, `balance`, `callback_url`)
VALUES (5, 'JBPAY', 'JBPAY', 1, 0, 1, 0, 'https://api-proxy.cbgame88.com/jbpay', '', '', '', 0, '');

ALTER TABLE `bank_transaction`
ADD INDEX `idx_transfer_at` (`transfer_at`);

ALTER TABLE `bank_transaction`
ADD INDEX `idx_deleted_at` (`deleted_at`);

ALTER TABLE `bank_transaction`
ADD INDEX `idx_statement_id` (`statement_id`);

ALTER TABLE `activity_lucky_wheel_round_user`
ADD INDEX `idx_user_id` (`user_id`);

ALTER TABLE `user_affiliate_income`
ADD INDEX `idx_created_at` (`created_at`);

-- MIGRATE of 2024-08-29

CREATE TABLE IF NOT EXISTS `user_first_deposit` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT NOT NULL,
    `transfer_at` DATETIME NULL DEFAULT NULL,
    `amount` DECIMAL(10,2) NOT NULL DEFAULT 0,
    `remark` VARCHAR(255) NULL DEFAULT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    PRIMARY KEY (`id`)
);
CREATE UNIQUE INDEX `uni_user_id` ON `user_first_deposit` (`user_id`);

-- เช็ค ว่ามี user ที่มีการฝากเงินครั้งแรกมากกว่า 1 ครั้ง
-- SELECT COUNT(*), user_id FROM `bank_transaction` WHERE bank_transaction.transaction_type_id = 1 AND is_first_deposit = 1 GROUP BY `user_id` HAVING COUNT(*) > 1;
-- UPDATE is_first_deposit = 0 if count > 1
-- UPDATE `bank_transaction` SET is_first_deposit = 0 WHERE bank_transaction.transaction_type_id = 1 AND is_first_deposit = 1 AND user_id IN (SELECT user_id FROM `bank_transaction` WHERE bank_transaction.transaction_type_id = 1 AND is_first_deposit = 1 GROUP BY `user_id` HAVING COUNT(*) > 1);

-- SELECT COUNT(*) FROM `user_first_deposit`;

-- MIGRATION
INSERT INTO `user_first_deposit` (`user_id`, `transfer_at`, `amount`, `remark`)
SELECT `user_id`, `transfer_at`, `credit_amount`, CONCAT(COALESCE(`from_account_number`, 'NULL'), '(', COALESCE(`from_bank_id`, 'NULL'), ') TO ', COALESCE(`to_account_number`, 'NULL'), '(', COALESCE(`to_bank_id`, 'NULL'), ')|', COALESCE(`credit_amount`, 'NULL'), '|', COALESCE(`deposit_channel`, 'NULL'), ' AT ', COALESCE(`transfer_at`, 'NULL'), '(', COALESCE(`created_at`, 'NULL'), ')')
FROM `bank_transaction` WHERE bank_transaction.transaction_type_id = 1 AND is_first_deposit = 1;

-- SELECT COUNT(*) FROM `user_first_deposit`;

-- RECHECK COUNT IS EQUAL on both table
-- SELECT (SELECT COUNT(*) FROM `user_first_deposit`) AS `user_first_deposit_count`,
--   (SELECT COUNT(*) FROM `bank_transaction` WHERE bank_transaction.transaction_type_id = 1 AND is_first_deposit = 1) AS `bank_transaction_count`;

CREATE INDEX `idx_type_name` ON `user_type` (`name`);
CREATE INDEX `idx_type_name_th` ON `user_type` (`label_th`);
CREATE INDEX `idx_type_name_en` ON `user_type` (`label_en`);

CREATE INDEX `idx_transfer_at` ON `user_transaction` (`transfer_at`);
CREATE INDEX `idx_created_at` ON `bank_transaction` (`created_at`);
CREATE INDEX `idx_confirmed_at` ON `bank_transaction` (`confirmed_at`);

-- DELETE FROM race_action WHERE created_at < '2024-03-01';
-- DELETE FROM bank_transaction_confirm WHERE created_at < '2024-03-01';

CREATE INDEX `idx_ref_id` ON `user_transaction` (`ref_id`);

-- MIGRATE of 2024-08-30

ALTER TABLE `paygate_payonex_order`
	ADD COLUMN `bank_transaction_id` BIGINT NULL DEFAULT NULL AFTER `payment_at`;
ALTER TABLE `paygate_payonex_order`
	ADD COLUMN `bank_transaction_status` VARCHAR(255) NULL DEFAULT 'PENDING' AFTER `bank_transaction_id`;
ALTER TABLE `paygate_payonex_order`
	ADD COLUMN `action_by` BIGINT NULL DEFAULT NULL AFTER `remark`;

-- UPDATE `paygate_payonex_order` SET `bank_transaction_id`=0 WHERE  `bank_transaction_id` IS NULL AND `transaction_status`='SUCCESS' AND `remark` IS NULL AND `order_type_id`=1;

ALTER TABLE `paygate_jbpay_order`
	ADD COLUMN `bank_transaction_id` BIGINT NULL DEFAULT NULL AFTER `payment_at`;
ALTER TABLE `paygate_jbpay_order`
	ADD COLUMN `bank_transaction_status` VARCHAR(255) NULL DEFAULT 'PENDING' AFTER `bank_transaction_id`;
ALTER TABLE `paygate_jbpay_order`
	ADD COLUMN `action_by` BIGINT NULL DEFAULT NULL AFTER `remark`;

-- UPDATE `paygate_jbpay_order` SET `bank_transaction_status`='SUCCESS' WHERE `bank_transaction_id` IS NULL AND `transaction_status`='PAID' AND `remark` IS NULL AND `order_type_id`=1;
-- UPDATE `paygate_jbpay_order` SET `bank_transaction_status`='SUCCESS' WHERE `bank_transaction_id` IS NULL AND `transaction_status`='PAID' AND `qr_promptpay` = 'success' AND `order_type_id`=/;

ALTER TABLE `paygate_smsmode_deposit`
  ADD COLUMN `from_bank_id` BIGINT NOT NULL AFTER `ref_id`;

ALTER TABLE `paygate_smsmode_deposit`
  ADD COLUMN `detail` VARCHAR(255) NULL DEFAULT NULL AFTER `order_no`;

UPDATE paygate_smsmode_deposit pd
JOIN user u ON pd.user_id = u.id
JOIN bank b ON u.bank_id = b.id
SET pd.from_bank_id = b.id;


ALTER TABLE `paygate_smsmode_deposit`
  ADD COLUMN `confirmed_by_admin_id` BIGINT DEFAULT NULL AFTER `remark`;

ALTER TABLE `paygate_smsmode_deposit`
  ADD COLUMN `confirmed_by_admin_name` VARCHAR(255) DEFAULT '' AFTER `confirmed_by_admin_id`;

CREATE INDEX `idx_statement_date` ON `alliance_winlose_income` (`statement_date`);

-- MIGRATE AT 2024-09-05

ALTER TABLE `turnover_setting`
	ADD COLUMN `tidturn_activity_daily_percent` INT NOT NULL DEFAULT '100' AFTER `tidturn_return_loss_percent`;

INSERT INTO `turnover_statement_type` (`id`, `name`) VALUES
	(12, 'TURN_BONUS_ACTIVITY_DAILY');

ALTER TABLE `activity_daily_user`
	ADD COLUMN `activity_daily_condition_id` BIGINT(19) NULL AFTER `activity_day_id`;

  ALTER TABLE `activity_daily_user`
	ADD COLUMN `tidturn_percent` BIGINT(19) NOT NULL DEFAULT '0' AFTER `activity_daily_condition_id`;

ALTER TABLE `activity_daily_user`
	ADD COLUMN `amount_condition` FLOAT NOT NULL DEFAULT '0' AFTER `activity_daily_condition_id`;

ALTER TABLE `user_transaction`
  ADD COLUMN `payment_merchart_id` BIGINT(19) NULL DEFAULT NULL AFTER `account_id`;

CREATE INDEX `idx_removed_at` ON `user_transaction` (`removed_at`);
CREATE INDEX `idx_is_show` ON `user_transaction` (`is_show`);

-- MIGATE PROD แล้ว เมื่อ 2024-09-12 TULA

-- GAME sport(1) casino(2) game(4) Then Add more column by lottery(3), p2p(6), Financial(7)
-- ** (5) ไม่มีห้ามดึง = พัง
-- ทั้งหมด 7 ตาราง
-- 1. play_log
ALTER TABLE `play_log`
	ADD COLUMN `turn_lottery` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `turn_game`,
	ADD COLUMN `win_lose_lottery` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `win_lose_game`,
	ADD COLUMN `valid_amount_lottery` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `valid_amount_game`;

ALTER TABLE `play_log`
  ADD COLUMN `turn_p2p` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `turn_lottery`,
  ADD COLUMN `win_lose_p2p` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `win_lose_lottery`,
  ADD COLUMN `valid_amount_p2p` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `valid_amount_lottery`;

ALTER TABLE `play_log`
  ADD COLUMN `turn_financial` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `turn_p2p`,
  ADD COLUMN `win_lose_financial` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `win_lose_p2p`,
  ADD COLUMN `valid_amount_financial` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `valid_amount_p2p`;

-- 2. user_playlog
ALTER TABLE `user_playlog`
  ADD COLUMN `turn_lottery` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `win_lose_game`,
  ADD COLUMN `valid_amount_lottery` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `turn_lottery`,
  ADD COLUMN `win_lose_lottery` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `valid_amount_lottery`;

ALTER TABLE `user_playlog`
  ADD COLUMN `turn_p2p` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `win_lose_lottery`,
  ADD COLUMN `valid_amount_p2p` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `turn_p2p`,
  ADD COLUMN `win_lose_p2p` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `valid_amount_p2p`;

ALTER TABLE `user_playlog`
  ADD COLUMN `turn_financial` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `win_lose_p2p`,
  ADD COLUMN `valid_amount_financial` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `turn_financial`,
  ADD COLUMN `win_lose_financial` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `valid_amount_financial`;

-- 3. user_today_playlog
ALTER TABLE `user_today_playlog`
  ADD COLUMN `turn_lottery` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `win_lose_game`,
  ADD COLUMN `win_lose_lottery` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `turn_lottery`,
  ADD COLUMN `valid_amount_lottery` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `win_lose_lottery`;

ALTER TABLE `user_today_playlog`
  ADD COLUMN `turn_p2p` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `valid_amount_lottery`,
  ADD COLUMN `win_lose_p2p` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `turn_p2p`,
  ADD COLUMN `valid_amount_p2p` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `win_lose_p2p`;

ALTER TABLE `user_today_playlog`
  ADD COLUMN `turn_financial` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `valid_amount_p2p`,
  ADD COLUMN `win_lose_financial` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `turn_financial`,
  ADD COLUMN `valid_amount_financial` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `win_lose_financial`;

-- 4. user_affiliate
ALTER TABLE `affiliate_commission`
	ADD COLUMN `lottery` DECIMAL(14,2) NULL DEFAULT '0.00' AFTER `slot`,
	ADD COLUMN `p2p` DECIMAL(14,2) NULL DEFAULT '0.00' AFTER `lottery`,
	ADD COLUMN `financial` DECIMAL(14,2) NULL DEFAULT '0.00' AFTER `p2p`;

ALTER TABLE `user_affiliate`
  ADD COLUMN `commission_lottery` DECIMAL(14,2) DEFAULT '0.00' AFTER `commission_game`,
  ADD COLUMN `commission_p2p` DECIMAL(14,2) DEFAULT '0.00' AFTER `commission_lottery`,
  ADD COLUMN `commission_financial` DECIMAL(14,2) DEFAULT '0.00' AFTER `commission_p2p`;

-- 5. user_affiliate_income
ALTER TABLE `user_affiliate_income`
  ADD COLUMN `turn_lottery` DECIMAL(14,2) DEFAULT '0.00' AFTER `commission_game`,
  ADD COLUMN `percent_lottery` DECIMAL(14,2) DEFAULT '0.00' AFTER `turn_lottery`,
  ADD COLUMN `commission_lottery` DECIMAL(14,2) DEFAULT '0.00' AFTER `turn_lottery`,
  ADD COLUMN `turn_p2p` DECIMAL(14,2) DEFAULT '0.00' AFTER `commission_lottery`,
  ADD COLUMN `percent_p2p` DECIMAL(14,2) DEFAULT '0.00' AFTER `turn_p2p`,
  ADD COLUMN `commission_p2p` DECIMAL(14,2) DEFAULT '0.00' AFTER `turn_p2p`,
  ADD COLUMN `turn_financial` DECIMAL(14,2) DEFAULT '0.00' AFTER `commission_p2p`,
  ADD COLUMN `percent_financial` DECIMAL(14,2) DEFAULT '0.00' AFTER `turn_financial`,
  ADD COLUMN `commission_financial` DECIMAL(14,2) DEFAULT '0.00' AFTER `turn_financial`;

-- 6. promotion_return_setting
ALTER TABLE `promotion_return_setting`
  ADD COLUMN `calc_on_lottery` TINYINT NOT NULL DEFAULT '1' AFTER `calc_on_game`,
  ADD COLUMN `calc_on_p2p` TINYINT NOT NULL DEFAULT '1' AFTER `calc_on_lottery`,
  ADD COLUMN `calc_on_financial` TINYINT NOT NULL DEFAULT '1' AFTER `calc_on_p2p`;

-- 7. promotion_return_loser
ALTER TABLE `promotion_return_loser`
  ADD COLUMN `total_loss_lottery` DECIMAL(10,2) NOT NULL DEFAULT '0.00' AFTER `total_loss_game`,
  ADD COLUMN `total_loss_p2p` DECIMAL(10,2) NOT NULL DEFAULT '0.00' AFTER `total_loss_lottery`,
  ADD COLUMN `total_loss_financial` DECIMAL(10,2) NOT NULL DEFAULT '0.00' AFTER `total_loss_p2p`;

ALTER TABLE `race_action`
	CHANGE COLUMN `status` `status` ENUM('PENDING','SUCCESS','FAILED','TIMEOUT') NOT NULL DEFAULT 'PENDING' COLLATE 'utf8mb4_general_ci' AFTER `action_key`;

CREATE TABLE IF NOT EXISTS `play_log_status` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `statement_date` VARCHAR(255) NOT NULL,
    `json_product_id` VARCHAR(255)  NULL,
    `playlog_sport_status` VARCHAR(255)  NULL,
    `playlog_game_status` VARCHAR(255)  NULL,
    `playlog_casino_status` VARCHAR(255)  NULL,
    `playlog_lottery_status` VARCHAR(255)  NULL,
    `playlog_p2p_status` VARCHAR(255)  NULL,
    `playlog_financial_status` VARCHAR(255)  NULL,
    `cut_daily_affiliate_status` VARCHAR(255)  NULL,
    `cut_daily_alliance_status` VARCHAR(255)  NULL,
    `promotion_return_loss_status` VARCHAR(255)  NULL,
    `remark` TEXT NULL DEFAULT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
    PRIMARY KEY (`id`)
);
CREATE UNIQUE INDEX `uni_statement_date` ON `play_log_status` (`statement_date`);

ALTER TABLE `promotion_return_loser`
	CHANGE COLUMN `game_detail` `game_detail` VARCHAR(255) NOT NULL DEFAULT '' COLLATE 'utf8mb4_general_ci' AFTER `return_percent`;


INSERT INTO `turnover_statement_type` (`id`, `name`) VALUES
  (13, 'TURN_PROMOTION_SETTING_PLAY_P2P'),
  (14, 'TURN_PROMOTION_SETTING_PLAY_LOTTERY'),
  (15, 'TURN_PROMOTION_SETTING_PLAY_FINANCIAL');


INSERT INTO `promotion_web_turnover_type` (`id`, `name`, `label_th`, `label_en`) VALUES
    (5, 'P2P', 'P2P', 'p2p'),
    (6, 'LOTTERY', 'ลอตเตอรี่', 'lottery'),
    (7, 'FINANCIAL', 'การเงิน', 'financial');

-- ข้างล่างนี้ ยังไม่ได้ PROD 2024-09-12 TULA

CREATE TABLE IF NOT EXISTS `report_summary_dashboard` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `created_date` VARCHAR(255) NOT NULL,
    `total_new_user_count` BIGINT NOT NULL DEFAULT 0,
    `total_active_user_count` BIGINT NOT NULL DEFAULT 0,
    `total_first_deposit_price` DECIMAL(14,2) NOT NULL DEFAULT 0,
    `total_first_deposit_user_count` BIGINT NOT NULL DEFAULT 0,
    `total_deposit_user_count` BIGINT NOT NULL DEFAULT 0,
    `total_withdraw_user_count` BIGINT NOT NULL DEFAULT 0,
    `total_bonus_price` DECIMAL(14,2) NOT NULL DEFAULT 0,
    `total_profit_price` DECIMAL(14,2) NOT NULL DEFAULT 0,
    `total_deposit_price` DECIMAL(14,2) NOT NULL DEFAULT 0,
    `total_withdraw_price` DECIMAL(14,2) NOT NULL DEFAULT 0,
    `total_affiliate_price` DECIMAL(14,2) NOT NULL DEFAULT 0,
    `total_alliance_price` DECIMAL(14,2) NOT NULL DEFAULT 0,
    `total_return_loss_taken_price` DECIMAL(14,2) NOT NULL DEFAULT 0,
    `total_credit_back_price` DECIMAL(14,2) NOT NULL DEFAULT 0,
    `total_turn` DECIMAL(14,2) NOT NULL DEFAULT 0,
    `total_winlose` DECIMAL(14,2) NOT NULL DEFAULT 0,
    `total_bank_profit` DECIMAL(14,2) NOT NULL DEFAULT 0,
    `total_promotion_web_credit` DECIMAL(14,2) NOT NULL DEFAULT 0,
    `total_promotion_return_loss` DECIMAL(14,2) NOT NULL DEFAULT 0,
    `total_activity_lucky_wheel` DECIMAL(14,2) NOT NULL DEFAULT 0,
    `total_check_in_bonus` DECIMAL(14,2) NOT NULL DEFAULT 0,
    `total_promotion_cash_coupon` DECIMAL(14,2) NOT NULL DEFAULT 0,
    `total_admin_create_bonus` DECIMAL(14,2) NOT NULL DEFAULT 0,
    `created_at` DATETIME DEFAULT NOW(),
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
    PRIMARY KEY (`id`)
);

ALTER TABLE `report_summary_dashboard`
	ADD COLUMN `total_return_turn_taken_price` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `total_return_loss_taken_price`,
	ADD COLUMN `total_promotion_return_turn` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `total_promotion_cash_coupon`;

-- โปรโมชั่นคืนยอดเทิร์น

CREATE TABLE IF NOT EXISTS `promotion_return_turn_cut_type` (
  `id` bigint NOT NULL,
  `name` varchar(255) NOT NULL DEFAULT '',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);

INSERT INTO `promotion_return_turn_cut_type` (`id`, `name`, `created_at`) VALUES
	(1, 'รายวัน', '2023-10-06 08:15:31'),
  (2, 'รายสัปดาห์', '2023-10-06 08:15:31');

CREATE TABLE IF NOT EXISTS `promotion_return_turn_status` (
  `id` bigint NOT NULL,
  `name` varchar(255) NOT NULL DEFAULT '',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);

INSERT INTO `promotion_return_turn_status` (`id`, `name`, `created_at`) VALUES
	(1, 'PENDING', '2023-10-09 06:52:18'),
	(2, 'READY', '2023-10-09 06:52:18'),
	(3, 'TAKEN', '2023-10-09 06:52:18'),
	(4, 'EXPIRED', '2023-10-09 06:52:18');

CREATE TABLE IF NOT EXISTS `promotion_return_turn_type` (
  `id` bigint NOT NULL,
  `name` varchar(255) NOT NULL DEFAULT '',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);

INSERT INTO `promotion_return_turn_type` (`id`, `name`, `created_at`) VALUES
	(1, 'คืนยอดเสียเมื่อยอดเทิร์นเกิน', '2023-10-06 08:15:30');

CREATE TABLE IF NOT EXISTS `promotion_return_turn` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `status_id` bigint NOT NULL DEFAULT '1',
  `daily_key` varchar(255) NOT NULL DEFAULT '',
  `of_date` date NOT NULL,
  `total_turn_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `total_turn_sport` decimal(10,2) NOT NULL DEFAULT '0.00',
  `total_turn_casino` decimal(10,2) NOT NULL DEFAULT '0.00',
  `total_turn_game` decimal(10,2) NOT NULL DEFAULT '0.00',
  `total_turn_lottery` decimal(10,2) NOT NULL DEFAULT '0.00',
  `total_turn_p2p` decimal(10,2) NOT NULL DEFAULT '0.00',
  `total_turn_financial` decimal(10,2) NOT NULL DEFAULT '0.00',
  `return_percent` decimal(10,2) NOT NULL DEFAULT '0.00',
  `game_detail` varchar(50) NOT NULL DEFAULT '',
  `return_type_id` bigint NOT NULL DEFAULT '1',
  `cut_type_id` bigint NOT NULL DEFAULT '1',
  `min_turn_price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `max_return_price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `credit_expire_days` int NOT NULL DEFAULT '0',
  `return_price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `calc_at` datetime DEFAULT NULL,
  `take_at` datetime DEFAULT NULL,
  `taken_price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `promotion_return_turn_daily_key_index` (`daily_key`),
  KEY `promotion_return_turn_status_id_index` (`status_id`),
  KEY `promotion_return_turn_user_id_index` (`user_id`)
);

CREATE TABLE IF NOT EXISTS `promotion_return_turn_setting` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `return_percent` decimal(10,2) NOT NULL DEFAULT '0.00',
  `return_type_id` bigint NOT NULL DEFAULT '1',
  `cut_type_id` bigint NOT NULL DEFAULT '1',
  `min_turn_price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `max_return_price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `credit_expire_days` int NOT NULL DEFAULT '0',
  `calc_on_sport` tinyint NOT NULL DEFAULT '1',
  `calc_on_casino` tinyint NOT NULL DEFAULT '1',
  `calc_on_game` tinyint NOT NULL DEFAULT '1',
  `calc_on_lottery` tinyint NOT NULL DEFAULT '1',
  `calc_on_p2p` tinyint NOT NULL DEFAULT '1',
  `calc_on_financial` tinyint NOT NULL DEFAULT '1',
  `detail` text NOT NULL,
  `is_enabled` tinyint NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);

INSERT INTO `promotion_return_turn_setting` (`id`, `return_percent`, `return_type_id`, `cut_type_id`, `min_turn_price`, `max_return_price`, `credit_expire_days`, `detail`, `is_enabled`) VALUES
	(1, 5.00, 1, 1, 500.00, 1000.00, 0, 'คืนยอดเทิร์น', 0);

-- MIGRATE RETURN_TURN
INSERT INTO `turnover_statement_type` (`id`, `name`) VALUES (16, 'PROMOTION_RETURN_TURN');
INSERT INTO `user_transaction_type` (`id`, `name`, `detail`) VALUES (13, 'PROMOTION_RETURN_TURN', 'แจกโบนัสฟรี คืนยอดเทิร์น');
INSERT INTO `user_income_log_type` (`id`, `name`, `detail`) VALUES (5, 'PROMOTION_RETURN_TURN', 'แจกโบนัส คืนยอดเทิร์น');

-- 3step Affiliate
-- 1. user_affiliate
ALTER TABLE `affiliate_commission`
	ADD COLUMN `sport2` DECIMAL(14,2) NULL DEFAULT '0.00' AFTER `financial`,
	ADD COLUMN `casino2` DECIMAL(14,2) NULL DEFAULT '0.00' AFTER `sport2`,
	ADD COLUMN `slot2` DECIMAL(14,2) NULL DEFAULT '0.00' AFTER `casino2`,
	ADD COLUMN `lottery2` DECIMAL(14,2) NULL DEFAULT '0.00' AFTER `slot2`,
	ADD COLUMN `p2p2` DECIMAL(14,2) NULL DEFAULT '0.00' AFTER `lottery2`,
	ADD COLUMN `financial2` DECIMAL(14,2) NULL DEFAULT '0.00' AFTER `p2p2`,
  ADD COLUMN `sport3` DECIMAL(14,2) NULL DEFAULT '0.00' AFTER `financial2`,
  ADD COLUMN `casino3` DECIMAL(14,2) NULL DEFAULT '0.00' AFTER `sport3`,
  ADD COLUMN `slot3` DECIMAL(14,2) NULL DEFAULT '0.00' AFTER `casino3`,
  ADD COLUMN `lottery3` DECIMAL(14,2) NULL DEFAULT '0.00' AFTER `slot3`,
  ADD COLUMN `p2p3` DECIMAL(14,2) NULL DEFAULT '0.00' AFTER `lottery3`,
  ADD COLUMN `financial3` DECIMAL(14,2) NULL DEFAULT '0.00' AFTER `p2p3`;

ALTER TABLE `affiliate_commission`
	ADD COLUMN `max_level` INT NOT NULL DEFAULT '1' AFTER `first_deposit_bonus`;

ALTER TABLE `affiliate_commission`
	ADD COLUMN `sport1` DECIMAL(14,2) NULL DEFAULT '0.00' AFTER `financial`,
	ADD COLUMN `casino1` DECIMAL(14,2) NULL DEFAULT '0.00' AFTER `sport1`,
	ADD COLUMN `slot1` DECIMAL(14,2) NULL DEFAULT '0.00' AFTER `casino1`,
	ADD COLUMN `lottery1` DECIMAL(14,2) NULL DEFAULT '0.00' AFTER `slot1`,
	ADD COLUMN `p2p1` DECIMAL(14,2) NULL DEFAULT '0.00' AFTER `lottery1`,
  ADD COLUMN `financial1` DECIMAL(14,2) NULL DEFAULT '0.00' AFTER `p2p1`;

CREATE TABLE `affiliate_level` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `ukey` varchar(255) NOT NULL,
  `user_id` bigint NOT NULL,
  `upline_id` bigint NOT NULL,
  `level` int NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
CREATE UNIQUE INDEX `uni_ukey` ON `affiliate_level` (`ukey`);
CREATE INDEX `idx_user_id` ON `affiliate_level` (`user_id`);
CREATE INDEX `idx_upline_id` ON `affiliate_level` (`upline_id`);

-- MIGRATE affiliate_level 1 by SELECT from user with ref_by then INSERT INTO affiliate_level as upline_id + level 1
-- DELETE FROM `affiliate_level` WHERE `upline_id` = 0;
INSERT INTO `affiliate_level` (`user_id`,`ukey`, `upline_id`, `level`) SELECT `id`, CONCAT(CONCAT(`ref_by`, '-'), `id`), `ref_by`, 1 FROM `user` WHERE `ref_by` IS NOT NULL AND `ref_by` != 0;

ALTER TABLE `user_affiliate_income`
	ADD COLUMN `percent_sport1` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `percent_sport`,
	ADD COLUMN `percent_sport2` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `percent_sport1`,
	ADD COLUMN `commission_sport1` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `commission_sport`,
	ADD COLUMN `commission_sport2` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `commission_sport1`,
  ADD COLUMN `percent_casino1` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `percent_casino`,
  ADD COLUMN `percent_casino2` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `percent_casino1`,
  ADD COLUMN `commission_casino1` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `commission_casino`,
  ADD COLUMN `commission_casino2` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `commission_casino1`,
  ADD COLUMN `percent_game1` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `percent_game`,
  ADD COLUMN `percent_game2` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `percent_game1`,
  ADD COLUMN `commission_game1` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `commission_game`,
  ADD COLUMN `commission_game2` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `commission_game1`,
  ADD COLUMN `percent_lottery1` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `percent_lottery`,
  ADD COLUMN `percent_lottery2` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `percent_lottery1`,
  ADD COLUMN `commission_lottery1` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `commission_lottery`,
  ADD COLUMN `commission_lottery2` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `commission_lottery1`,
  ADD COLUMN `percent_p2p1` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `percent_p2p`,
  ADD COLUMN `percent_p2p2` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `percent_p2p1`,
  ADD COLUMN `commission_p2p1` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `commission_p2p`,
  ADD COLUMN `commission_p2p2` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `commission_p2p1`,
  ADD COLUMN `percent_financial1` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `percent_financial`,
  ADD COLUMN `percent_financial2` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `percent_financial1`,
  ADD COLUMN `commission_financial1` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `commission_financial`,
  ADD COLUMN `commission_financial2` DECIMAL(14,2) NOT NULL DEFAULT '0.00' AFTER `commission_financial1`;

ALTER TABLE `affiliate_level`
	ADD COLUMN `total_commission` DECIMAL(14,2) NOT NULL DEFAULT (0) AFTER `level`;

-- MIGRATE all data from affiliate_downline_total_income to affiliate_level.total_commission
UPDATE `affiliate_level` al
JOIN (
  SELECT `user_id`, `total_commission` AS `total_commission`
  FROM `affiliate_downline_total_income`
) adti ON al.`user_id` = adti.`user_id`
SET al.`total_commission` = adti.`total_commission`
WHERE al.`level` = 1;


-- BANK
CREATE TABLE IF NOT EXISTS `bank_account_show_bank` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `bank_account_id` BIGINT NOT NULL,
  `bank_id` BIGINT NOT NULL COMMENT 'id ของ user bank ที่จะแสดง',
  `created_at` DATETIME DEFAULT NOW(),
  `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
  PRIMARY KEY (`id`),
  INDEX `idx_bank_account_id` (`bank_account_id`),
  INDEX `idx_bank_id` (`bank_id`)
);

ALTER TABLE `admin`
    ADD COLUMN `totp_secret` VARCHAR(255) NOT NULL DEFAULT '' AFTER `ip`;
ALTER TABLE `admin`
	ADD COLUMN `is_verify_totp` INT(3) NOT NULL DEFAULT 0 AFTER `totp_secret`;

ALTER TABLE `configuration_web`
	ADD COLUMN `is_totp_verify` TINYINT(3) NOT NULL DEFAULT '0' AFTER `show_web_aff_name`;

-- MIGATE PROD แล้ว เมื่อ 2024-10-04

ALTER TABLE `configuration_web`
	ADD COLUMN `token_expired_minute` INT NOT NULL DEFAULT '0' AFTER `is_totp_verify`;

-- POMPAY
CREATE TABLE IF NOT EXISTS `paygate_pompay_customer` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `customer_uuid` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `full_name` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `bank_code` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `account_no` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `account_name` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni_user_id` (`user_id`)
);
CREATE TABLE IF NOT EXISTS `paygate_pompay_order` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `order_type_id` bigint NOT NULL,
  `ref_id` bigint DEFAULT NULL,
  `order_no` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `transfer_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `transaction_no` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `transaction_date` datetime DEFAULT NULL,
  `transaction_status` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `payment_at` datetime DEFAULT NULL,
  `bank_transaction_id` bigint DEFAULT NULL,
  `bank_transaction_status` varchar(255) COLLATE utf8mb4_general_ci DEFAULT 'PENDING',
  `qr_promptpay` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `note` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `remark` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `action_by` bigint DEFAULT NULL,
  `api_remark` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni_order_no` (`order_no`),
  UNIQUE KEY `uni_id` (`id`),
  UNIQUE KEY `uni_transaction_no` (`transaction_no`),
  KEY `idx_order_type_id` (`order_type_id`)
);
CREATE TABLE IF NOT EXISTS `paygate_pompay_order_type` (
  `id` bigint NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `label_th` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `label_en` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  PRIMARY KEY (`id`)
);
CREATE TABLE IF NOT EXISTS `paygate_pompay_token` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `access_token` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `expire_at` datetime NOT NULL,
  `create_by` bigint NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
CREATE TABLE IF NOT EXISTS `paygate_pompay_webhook` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `json_payload` longtext COLLATE utf8mb4_general_ci NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);

INSERT INTO `paygate_merchant` (`id`, `name`, `type_name`, `has_deposit`, `is_deposit_enabled`, `has_withdraw`, `is_withdraw_enabled`, `api_end_point`) VALUES (6, 'POMPAY', 'POMPAY', 1, 0, 1, 0, 'https://pompay.asia/v2');
UPDATE `paygate_merchant` SET `payment_deposit_minimum`=50, `payment_deposit_maximum`=200000, `payment_withdraw_minimum`=100, `payment_withdraw_maximum`=200000 WHERE `id`=6;

ALTER TABLE `paygate_payonex_order`
	CHANGE COLUMN `bank_transaction_status` `bank_transaction_status` VARCHAR(255) NULL DEFAULT 'PENDING' COLLATE 'utf8mb4_general_ci' AFTER `bank_transaction_id`;

UPDATE `paygate_payonex_order` SET `bank_transaction_status`='SUCCESS' WHERE `bank_transaction_id` IS NULL AND `transaction_status`='PAID' AND `remark` IS NULL AND `order_type_id`=1;

UPDATE `user_income_log_type` SET `detail`='แจกโบนัสฟรี คืนยอดcommission' WHERE  `id`=5;
UPDATE `user_transaction_type` SET `detail`='แจกโบนัสฟรี คืนยอดcommission' WHERE  `id`=13;

ALTER TABLE `bank_account`
	ADD COLUMN `is_manual_bank` TINYINT NOT NULL DEFAULT '0' AFTER `sms_mode`;

-- MIGATE PROD แล้ว เมื่อ 2024-10-09

INSERT INTO `admin_action_type` (`id`, `name`, `detail`) VALUES (12, 'ADMIN_LOGIN_FAILED', 'Admin login failed');

-- Aff Report

CREATE TABLE IF NOT EXISTS `report_affiliate_user` (
  `id` VARCHAR(255) NOT NULL,
  `of_date` DATE NOT NULL,
  `user_id` BIGINT NOT NULL,
  `link_click_total` BIGINT NOT NULL DEFAULT '0',
  `member_count` BIGINT NOT NULL DEFAULT '0',
  `member_deposit_amount` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `total_turn_sport` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `total_turn_casino` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `total_turn_game` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `total_turn_lottery` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `total_turn_p2p` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `total_turn_financial` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `total_commission` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `link_register_amount` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `first_deposit_amount` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `total_income` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `total_income_withdraw` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `income_balance` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `promotion_return_loss_total` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `lucky_wheel_total` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `coupon_total` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `credit_bonus_total` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `promotion_web_total` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `promotion_return_turn_total` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `all_bonus_total` DECIMAL(14,2) NOT NULL DEFAULT '0.00',
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY `uni_daily_user_id` (`user_id`, `of_date`),
  PRIMARY KEY (`id`)
);

-- PAYMENTCO
CREATE TABLE IF NOT EXISTS `paygate_paymentco_customer` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `customer_uuid` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `full_name` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `bank_code` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `account_no` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `account_name` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni_user_id` (`user_id`)
);
CREATE TABLE IF NOT EXISTS `paygate_paymentco_order` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `order_type_id` bigint NOT NULL,
  `ref_id` bigint DEFAULT NULL,
  `order_no` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `transfer_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `transaction_no` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `transaction_date` datetime DEFAULT NULL,
  `transaction_status` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `payment_at` datetime DEFAULT NULL,
  `bank_transaction_id` bigint DEFAULT NULL,
  `bank_transaction_status` varchar(255) COLLATE utf8mb4_general_ci DEFAULT 'PENDING',
  `qr_promptpay` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `note` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `remark` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `action_by` bigint DEFAULT NULL,
  `api_remark` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni_order_no` (`order_no`),
  UNIQUE KEY `uni_id` (`id`),
  UNIQUE KEY `uni_transaction_no` (`transaction_no`),
  KEY `idx_order_type_id` (`order_type_id`)
);
CREATE TABLE IF NOT EXISTS `paygate_paymentco_order_type` (
  `id` bigint NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `label_th` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `label_en` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  PRIMARY KEY (`id`)
);
CREATE TABLE IF NOT EXISTS `paygate_paymentco_token` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `access_token` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `expire_at` datetime NOT NULL,
  `create_by` bigint NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
CREATE TABLE IF NOT EXISTS `paygate_paymentco_webhook` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `json_payload` longtext COLLATE utf8mb4_general_ci NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);

CREATE INDEX `idx_affiliate_link_click_created_at` ON `affiliate_link_click` (`created_at`);

-- MIGATE PROD ถึงจุดนี้แล้ว เมื่อ 2024-10-17

-- ยังไม่เปิดใช้งาน รออาทิตย์หน้าค่อยทำ Migrate ข้างล่างนี้

-- MIGATE PROD 2024-10-30
INSERT INTO `permission` (`id`, `permission_key`, `main`, `name`) VALUES (80, 'export_file_user', 1, 'โหลดไฟล์ยูสเซอร์');
INSERT INTO `permission` (`id`, `permission_key`, `main`, `name`) VALUES (81, 'export_file_transaction', 1, 'โหลดไฟล์รายการธุรกรรม');

-- MIGATE PROD 2024-11-01
INSERT INTO `paygate_merchant` (`id`, `name`, `type_name`, `has_deposit`, `is_deposit_enabled`, `has_withdraw`, `is_withdraw_enabled`, `api_end_point`) VALUES (7, 'PaymentCo', 'PAYMENTCO', 1, 0, 1, 0, 'https://paymentco.top/proxy');
UPDATE `paygate_merchant` SET `payment_deposit_minimum`=50, `payment_deposit_maximum`=200000, `payment_withdraw_minimum`=100, `payment_withdraw_maximum`=200000 WHERE `id`=7;

-- CREATE UNIQUE INDEX `uni_admin_single_hash` ON `admin_single_session` (`md5_token`);

-- MIGATE PROD 2024-11-03

UPDATE `configuration_web` SET `token_expired_minute`=1440 ;

-- MIGATE PROD 2024-11-04

ALTER TABLE `bank_account`
	ADD COLUMN `image_url` VARCHAR(500) NOT NULL DEFAULT '' AFTER `is_manual_bank`;

-- MIGATE PROD 2024-11-15

ALTER TABLE `affiliate_commission`
  CHANGE COLUMN `sport` `sport` DECIMAL(14,3) NULL DEFAULT '0.00' AFTER `financial`,
  CHANGE COLUMN `casino` `casino` DECIMAL(14,3) NULL DEFAULT '0.00' AFTER `sport`,
  CHANGE COLUMN `slot` `slot` DECIMAL(14,3) NULL DEFAULT '0.00' AFTER `casino`,
  CHANGE COLUMN `lottery` `lottery` DECIMAL(14,3) NULL DEFAULT '0.00' AFTER `slot`,
  CHANGE COLUMN `p2p` `p2p` DECIMAL(14,3) NULL DEFAULT '0.00' AFTER `lottery`,
  CHANGE COLUMN `financial` `financial` DECIMAL(14,3) NULL DEFAULT '0.00' AFTER `p2p`;
ALTER TABLE `affiliate_commission`
  CHANGE COLUMN `sport1` `sport1` DECIMAL(14,3) NULL DEFAULT '0.00' AFTER `financial`,
  CHANGE COLUMN `casino1` `casino1` DECIMAL(14,3) NULL DEFAULT '0.00' AFTER `sport1`,
  CHANGE COLUMN `slot1` `slot1` DECIMAL(14,3) NULL DEFAULT '0.00' AFTER `casino1`,
  CHANGE COLUMN `lottery1` `lottery1` DECIMAL(14,3) NULL DEFAULT '0.00' AFTER `slot1`,
  CHANGE COLUMN `p2p1` `p2p1` DECIMAL(14,3) NULL DEFAULT '0.00' AFTER `lottery1`,
  CHANGE COLUMN `financial1` `financial1` DECIMAL(14,3) NULL DEFAULT '0.00' AFTER `p2p1`;
ALTER TABLE `affiliate_commission`
	CHANGE COLUMN `sport2` `sport2` DECIMAL(14,3) NULL DEFAULT '0.00' AFTER `financial1`,
	CHANGE COLUMN `casino2` `casino2` DECIMAL(14,3) NULL DEFAULT '0.00' AFTER `sport2`,
	CHANGE COLUMN `slot2` `slot2` DECIMAL(14,3) NULL DEFAULT '0.00' AFTER `casino2`,
	CHANGE COLUMN `lottery2` `lottery2` DECIMAL(14,3) NULL DEFAULT '0.00' AFTER `slot2`,
	CHANGE COLUMN `p2p2` `p2p2` DECIMAL(14,3) NULL DEFAULT '0.00' AFTER `lottery2`,
	CHANGE COLUMN `financial2` `financial2` DECIMAL(14,3) NULL DEFAULT '0.00' AFTER `p2p2`;
ALTER TABLE `affiliate_commission`
	CHANGE COLUMN `sport3` `sport3` DECIMAL(14,3) NULL DEFAULT '0.00' AFTER `financial2`,
	CHANGE COLUMN `casino3` `casino3` DECIMAL(14,3) NULL DEFAULT '0.00' AFTER `sport3`,
	CHANGE COLUMN `slot3` `slot3` DECIMAL(14,3) NULL DEFAULT '0.00' AFTER `casino3`,
	CHANGE COLUMN `lottery3` `lottery3` DECIMAL(14,3) NULL DEFAULT '0.00' AFTER `slot3`,
	CHANGE COLUMN `p2p3` `p2p3` DECIMAL(14,3) NULL DEFAULT '0.00' AFTER `lottery3`,
	CHANGE COLUMN `financial3` `financial3` DECIMAL(14,3) NULL DEFAULT '0.00' AFTER `p2p3`;


INSERT INTO `admin_action_type` (`id`, `name`, `detail`) VALUES (13, 'ADMIN_ACTION_COMMISSION_MANAGE', 'Admin manage commission');
INSERT INTO `admin_action_type` (`id`, `name`, `detail`) VALUES (14, 'ADMIN_ACTION_MANAGE_TIDTURN', 'Admin manage turnover setting');

ALTER TABLE `turnover_setting`
	ADD COLUMN `tidturn_success_deposit_percent` INT NOT NULL DEFAULT '0' AFTER `tidturn_activity_daily_percent`;

  INSERT INTO `turnover_statement_type` (`id`, `name`) VALUES (17, 'TURNOVER_TYPE_SUCCESS_DEPOSIT');

ALTER TABLE `exchange_rate`
	ADD COLUMN `max_rate_deposit` DECIMAL(10,5) NOT NULL DEFAULT '0' AFTER `exchange_rate`;

ALTER TABLE `exchange_update_log`
	ADD COLUMN `max_rate_deposit` DECIMAL(10,2) NOT NULL DEFAULT '0' AFTER `exchange_rate`;

ALTER TABLE `exchange_rate`
	CHANGE COLUMN `max_rate_deposit` `max_rate_deposit` DECIMAL(20,5) NOT NULL DEFAULT '0.00000' AFTER `exchange_rate`;

-- MIGATE PROD 2024-11-28

CREATE TABLE IF NOT EXISTS `edit_section` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `section_deposit` TEXT NOT NULL,
  `section_withdraw` TEXT NOT NULL,
  `created_at` DATETIME DEFAULT NOW(),
  `created_by_id` BIGINT NOT NULL,
  `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
  `updated_by_id` BIGINT NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
);

CREATE TABLE IF NOT EXISTS `category_game_setting` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `is_open_slot` TINYINT NOT NULL DEFAULT '1',
  `is_open_casino` TINYINT NOT NULL DEFAULT '1',
  `is_open_sport` TINYINT NOT NULL DEFAULT '1',
  `is_open_lottery` TINYINT NOT NULL DEFAULT '0',
  `is_open_p2p` TINYINT NOT NULL DEFAULT '0',
  `is_open_external_lotto` TINYINT NOT NULL DEFAULT '0',
  `created_at` DATETIME DEFAULT NOW(),
  `created_by_id` BIGINT DEFAULT NULL,
  `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
  `updated_by_id` BIGINT NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
);

INSERT INTO `admin_action_type` (`id`, `name`, `detail`) VALUES (15, 'ADMIN_ACTION_EDIT_SECTION', 'Admin edit section');
INSERT INTO `admin_action_type` (`id`, `name`, `detail`) VALUES (16, 'ADMIN_ACTION_CATEGORY_GAME_SETTING', 'Admin edit category game setting');


-- MIGATE PROD 2024-11-09

CREATE TABLE IF NOT EXISTS `send_sms` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `ref_id` BIGINT DEFAULT NULL,
  `user_id` BIGINT NOT NULL,
  `phone` VARCHAR(255) NOT NULL,
  `sender_name_id` BIGINT NOT NULL,
  `message` TEXT NOT NULL,
  `credit_sms_amount` DECIMAL(10,2) NOT NULL DEFAULT '0.00',
  `status` VARCHAR(255) NOT NULL,
  `log_status` VARCHAR(255) NOT NULL,
  `created_at` DATETIME DEFAULT NOW(),
  `created_by_id` BIGINT NOT NULL,
  `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
  PRIMARY KEY (`id`)
);

CREATE TABLE IF NOT EXISTS `send_sms_sender_name` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `sender_name` VARCHAR(255) NOT NULL,
  `is_sender_name_active` TINYINT NOT NULL DEFAULT '1',
  `created_at` DATETIME DEFAULT NOW(),
  `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
  PRIMARY KEY (`id`)
);


INSERT INTO `send_sms_sender_name` (`sender_name`) VALUES
  ('ez-otp');


-- MIGATE PROD 2024-12-12

CREATE TABLE IF NOT EXISTS `paygate_zappay_order` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT NOT NULL,
    `order_type_id` BIGINT NOT NULL,
    `ref_id` BIGINT NULL DEFAULT NULL,
    `order_no` VARCHAR(255) NOT NULL,
    `amount` DECIMAL(10,2) NOT NULL DEFAULT 0,
    `transfer_amount` DECIMAL(10,2) NOT NULL DEFAULT 0,
    `transaction_no` VARCHAR(255) NULL,
    `transaction_date` DATETIME NULL,
    `transaction_status` VARCHAR(255) NULL,
    `payment_at` DATETIME NULL DEFAULT NULL,
    `bank_transaction_id` BIGINT NULL DEFAULT NULL,
    `bank_transaction_status` VARCHAR(255) NULL DEFAULT 'PENDING',
    `qr_promptpay` VARCHAR(255) NULL,
    `payment_page_url` VARCHAR(255) NULL,
    `note` VARCHAR(255) NULL DEFAULT NULL,
    `remark` VARCHAR(1000) NULL DEFAULT NULL,
    `action_by` BIGINT NULL DEFAULT NULL,
    `api_remark` VARCHAR(255) NULL DEFAULT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
    PRIMARY KEY (`id`)
);
CREATE UNIQUE INDEX `uni_order_no` ON `paygate_zappay_order` (`order_no`);
CREATE UNIQUE INDEX `uni_transaction_no` ON `paygate_zappay_order` (`transaction_no`);
CREATE INDEX `idx_order_type_id` ON `paygate_zappay_order` (`order_type_id`);

CREATE TABLE IF NOT EXISTS `paygate_zappay_webhook` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL,
    `json_payload` LONGTEXT NOT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    PRIMARY KEY (`id`)
);

INSERT INTO `paygate_merchant` (`id`, `name`, `type_name`, `has_deposit`, `is_deposit_enabled`, `has_withdraw`, `is_withdraw_enabled`, `api_end_point`, `shop_name`, `username`, `password`, `balance`, `callback_url`)
VALUES (8, 'ZAPPAY', 'ZAPPAY', 1, 0, 1, 0, 'https://api-proxy.cbgame88.com/zappay', '', '', '', 0, '');

CREATE TABLE `paygate_order_type` (
	`id` BIGINT NOT NULL,
	`name` VARCHAR(255) NOT NULL COLLATE 'utf8mb4_0900_ai_ci',
	`label_th` VARCHAR(255) NOT NULL COLLATE 'utf8mb4_0900_ai_ci',
	`label_en` VARCHAR(255) NOT NULL COLLATE 'utf8mb4_0900_ai_ci',
	PRIMARY KEY (`id`)
);
INSERT INTO `paygate_order_type` (`id`, `name`, `label_th`, `label_en`) VALUES
(1, 'DEPOSIT', 'ฝากเงิน', 'deposit'),
(2, 'WITHDRAW', 'ถอนเงิน', 'withdraw');

UPDATE `paygate_merchant` SET `api_end_point`='https://api-proxy.cbgame88.com/jbpay' WHERE  `id`=8;

-- MIGATE PROD 2024-12-23

INSERT INTO `admin_action_type` (`id`, `name`, `detail`) VALUES (17, 'ADMIN_ACTION_GAME_PRIORITY_SETTING', 'Admin edit game priority setting');

CREATE TABLE IF NOT EXISTS `agent_game_priority_setting` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `agent` VARCHAR(50) NOT NULL,
    `vendor_code` VARCHAR(50) NOT NULL,
    `detail` TEXT NOT NULL,
    `category_name` VARCHAR(255) NOT NULL,
    `priority_order` BIGINT NOT NULL DEFAULT 0,
    `image_name` VARCHAR(255) NULL DEFAULT NULL,
    `is_popular` TINYINT NOT NULL DEFAULT 0,
    `is_show` TINYINT NOT NULL DEFAULT 1,
    `total_played` BIGINT NOT NULL DEFAULT 0,
    `last_played_at` DATETIME NULL DEFAULT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
    `updated_by_id` BIGINT NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
);

-- add all the add image_name
INSERT INTO `agent_game_priority_setting`
(`id`, `agent`, `vendor_code`, `priority_order`, `detail`, `category_name`, `is_popular`, `is_show`, `image_name`)
VALUES
(1, 'AGC', 'M8', 1, 'M-Sports', 'SPORT', 0, 1, 'msport'),
(2, 'AGC', 'PGSOFT', 2, 'PGSOFT', 'SLOT', 0, 1, 'egame-pgsoft'),
(3, 'AGC', 'JOKER', 3, 'JOKER', 'SLOT', 0, 1, 'egame-joker'),
(4, 'AGC', 'SSport', 4, 'S-Sports', 'SPORT', 0, 1, 'ssport'),
(5, 'AGC', 'SBOVS', 5, 'SBO Virtual Sport', 'SPORT', 0, 1, 'sbovs'),
(6, 'AGC', 'IA', 6, 'IA E-Sports', 'SPORT', 0, 1, 'img-ia'),
(7, 'AGC', 'SBO', 7, 'SBO Sport', 'SPORT', 0, 1, 'sbobet'),
(8, 'AGC', 'SB', 8, 'Sexy Baccarat', 'CASINO', 0, 1, 'sb-casino'),
(9, 'AGC', 'SA', 9, 'SA Gaming', 'CASINO', 0, 1, 'sa-casino'),
(10, 'AGC', 'WM', 10, 'WM', 'CASINO', 0, 1, 'wm-casino'),
(11, 'AGC', 'DG', 11, 'Dream Gaming', 'CASINO', 0, 1, 'dc-casino'),
(12, 'AGC', 'EZUGI', 12, 'EZUGI', 'CASINO', 0, 1, 'ezugi-casino'),
(13, 'AGC', 'XG', 13, 'XG', 'CASINO', 0, 1, 'xg-casino'),
(14, 'AGC', 'CQ9LIVE', 14, 'CQ9LIVE', 'CASINO', 0, 1, 'img-cq9-live'),
(15, 'AGC', 'YeeBet', 15, 'YeeBet', 'CASINO', 0, 1, 'img-yeebet'),
(16, 'AGC', 'AllBet', 16, 'AllBet', 'CASINO', 0, 1, 'ab-casino'),
(17, 'AGC', 'BG', 17, 'BG Live Casino', 'CASINO', 0, 1, 'bg-casino'),
(18, 'AGC', 'CT855', 18, 'CT855', 'CASINO', 0, 1, 'ct855-casino'),
(19, 'AGC', 'FGG', 19, 'Fair Guaranted Gaming', 'CASINO', 0, 1, 'fgg-casino'),
(20, 'AGC', 'MaxBet', 20, 'Max Bet', 'SPORT', 0, 1, 'maxbet'),
(21, 'AGC', 'CQ9', 21, 'CQ9', 'SLOT', 0, 1, 'img-g-cq9'),
(22, 'AGC', 'MT', 22, 'Muay Thai', 'SPORT', 0, 1, 'sport-mt'),
(23, 'AGC', 'Genesis', 23, 'Genesis', 'SLOT', 0, 1, 'img-gennesis'),
(24, 'AGC', 'SAE', 24, 'SimplePlay', 'SLOT', 0, 1, 'img-sp'),
(25, 'AGC', 'SPADE', 25, 'Spade Gaming', 'SLOT', 0, 1, 'img-spade'),
(26, 'AGC', 'MX', 26, 'Pragmatic', 'SLOT', 0, 1, 'mx-tournament-en'),
(27, 'AGC', 'Habanero', 27, 'Habanero', 'SLOT', 0, 1, 'egame-habanero'),
(28, 'AGC', 'KM', 28, 'KingMaker', 'SLOT', 0, 1, 'egame-km'),
(29, 'AGC', 'FUN', 29, 'FUN Gaming', 'SLOT', 0, 1, 'img-fungaming'),
(30, 'AGC', 'M8Poker', 30, 'M8 Poker', 'P2P', 0, 1, 'img-m8poker'),
(31, 'AGC', 'JiliGames', 31, 'Jili Games', 'SLOT', 0, 1, 'img-jili'),
(32, 'AGC', 'BS', 32, 'BetSoft', 'SLOT', 0, 1, 'img-g-betsoft'),
(33, 'AGC', 'VT', 33, 'Virtual Tech', 'SLOT', 0, 1, 'egame-vt'),
(34, 'AGC', 'MG', 34, 'Microgaming', 'SLOT', 0, 1, 'img-mgm'),
(35, 'AGC', 'QQ', 35, 'QQLottery', 'LOTTO', 0, 1, 'img-lolto'),
(36, 'AGC', 'PNG', 36, 'Play and Go', 'SLOT', 0, 1, 'img-playgo'),
(37, 'AGC', 'Mario', 37, 'Mario', 'SLOT', 0, 1, 'img-mario'),
(38, 'AGC', 'V8', 38, 'V8 Poker', 'P2P', 0, 1, 'img-v8poker'),
(39, 'AGC', 'DT', 39, 'DreamTech', 'SLOT', 0, 1, 'egame-dt'),
(40, 'AGC', 'DPT', 40, 'PlayTech Digital', 'SLOT', 0, 1, 'egame-dpt'),
(41, 'AGC', 'JDB', 41, 'JDB', 'SLOT', 0, 1, 'img-jdb'),
(42, 'AGC', 'SSPORTLCS', 42, 'S-Sport Lay Correct Score', 'SPORT', 0, 1, 'img-ssport-lcs'),
(43, 'AGC', 'Saba', 43, 'Saba Sport (IBC)', 'SPORT', 0, 1, 'img-saba-sport'),
(44, 'AGC', 'IG360', 44, 'IG360(Golf)', 'SPORT', 0, 1, 'img-ig360-golf'),
(45, 'AGC', 'MIKI', 45, 'Miki Mouse', 'CASINO', 0, 1, 'img-miki-mouse'),
(46, 'AGC', 'MGL', 46, 'MG Live', 'CASINO', 0, 1, 'img-mg-live'),
(47, 'AGC', 'WON', 47, 'WON Casino', 'CASINO', 0, 1, 'img-won-casino'),
(48, 'AGC', 'LG88', 48, 'LG88', 'CASINO', 0, 1, 'img-lg88'),
(49, 'AGC', 'RCB', 49, 'RCB988', 'CASINO', 0, 1, 'img-rcb988'),
(50, 'AGC', 'AG', 50, 'AG Deluxe Suite', 'CASINO', 0, 1, 'img-ag-deluxe'),
(51, 'AGC', 'CF', 51, 'CockFight', 'CASINO', 0, 1, 'img-cock-fight'),
(52, 'AGC', 'LS', 52, 'Lucky Streak', 'CASINO', 0, 1, 'img-lucky-streak'),
(53, 'AGC', 'APOLLO', 53, 'APOLLO', 'SLOT', 0, 1, 'img-apollo'),
(54, 'AGC', 'NETENT', 54, 'NetEnt', 'SLOT', 0, 1, 'img-netent'),
(55, 'AGC', 'NLC', 55, 'No Limit City', 'SLOT', 0, 1, 'img-nolimit-city'),
(56, 'AGC', 'M365', 56, 'M365', 'SLOT', 0, 1, 'img-m365'),
(57, 'AGC', 'MEGAH5', 57, 'MEGAH5', 'SLOT', 0, 1, 'img-megah5'),
(58, 'AGC', 'Live22', 58, 'Live22', 'SLOT', 0, 1, 'img-live22-game'),
(59, 'AGC', 'PGS', 59, 'Pegasus', 'SLOT', 0, 1, 'img-pegasus'),
(60, 'AGC', 'AP', 60, 'Advantplay', 'SLOT', 0, 1, 'img-advantplay'),
(61, 'AGC', 'OGE', 61, 'One Gaming', 'SLOT', 0, 1, 'img-one-gaming'),
(62, 'AGC', 'DragonGaming', 62, 'DragonGaming', 'SLOT', 0, 1, 'img-dragon-gaming'),
(63, 'AGC', 'RG', 63, 'Relax Gaming', 'SLOT', 0, 1, 'img-relax-gaming'),
(64, 'AGC', 'PT', 64, 'SkyWind', 'SLOT', 0, 1, 'img-skywind'),
(65, 'AGC', 'ACE333', 65, 'ACE333', 'SLOT', 0, 1, 'img-ace333'),
(66, 'AGC', 'Kiss918', 66, '918Kiss', 'SLOT', 0, 1, 'img-kiss918'),
(67, 'AGC', 'BNG', 67, 'BNG', 'SLOT', 0, 1, 'img-bng-game'),
(68, 'AGC', 'YGG', 68, 'YGG', 'SLOT', 0, 1, 'img-ygg-game'),
(69, 'AGC', 'HACKSAW', 69, 'HACKSAW', 'SLOT', 0, 1, 'img-hacksaw'),
(70, 'AGC', 'GFG', 70, 'GFG', 'SLOT', 0, 1, 'img-gfg-game'),
(71, 'AGC', 'PS', 71, 'PlayStar', 'SLOT', 0, 1, 'img-play-star'),
(72, 'AGC', 'CG', 72, 'Creative Gaming', 'SLOT', 0, 1, 'img-creative-game'),
(73, 'AGC', 'YGR', 73, 'YGR Games', 'SLOT', 0, 1, 'img-ygr-game'),
(74, 'AGC', 'T1', 74, 'T1GAMES', 'SLOT', 0, 1, 'img-t1-game'),
(75, 'AGC', 'ES', 75, 'ESGAMES', 'SLOT', 0, 1, 'img-esgame'),
(76, 'AGC', 'ML', 76, 'Malaysia Lottery (ABS)', 'LOTTO', 0, 1, 'img-malaysia-lottery'),
(77, 'AGC', 'VL', 77, 'VL (XOSO79)', 'LOTTO', 0, 1, 'img-vl-xoso79'),
(78, 'AGC', 'OG', 78, 'OG Poker', 'P2P', 0, 1, 'img-og-poker'),
(79, 'AGC', 'EVO', 79, 'Evolution Live Casino', 'CASINO', 0, 1, 'img-evo-casino'),
(80, 'AGC', 'GD', 80, 'Gold Diamon Gaming', 'CASINO', 0, 1, 'img-gd-diamon'),
(81, 'AGC', 'PPL', 81, 'Pragmatic Play Live', 'CASINO', 0, 1, 'img-ppl-play'),
(82, 'AGC', 'B2B', 82, 'B2B', 'CASINO', 0, 1, 'img-b2b'),
(83, 'AGC', 'BGE', 83, 'BE E-Game', 'SLOT', 0, 1, 'img-be-gaming'),
(84, 'AGC', 'GG', 84, 'Global Gaming', 'SLOT', 0, 1, 'img-global-gaming'),
(85, 'AGC', 'C93', 85, '93 Connect', 'LOTTO', 0, 1, 'img-93-connect'),
(86, 'AGC', 'KN', 86, 'Keno Lottery', 'LOTTO', 0, 1, 'img-keno-lottery'),
(87, 'AGC', 'LEG', 87, 'LE Gaming', 'P2P', 0, 1, 'img-le-gaming'),
(88, 'AGC', 'IG', 88, 'IG E-games', 'SLOT', 0, 1, 'ig-e-gaming'),
(89, 'AGC', 'MIMI', 89, 'Mimi Gaming', 'SLOT', 0, 1, 'mimi-gaming'),
(90, 'AGC', 'BGE', 90, 'BE E-Game', 'SLOT', 0, 1, 'be-e-game'),
(91, 'AGC', 'FS', 91, 'Fast Spin', 'SLOT', 0, 1, 'fast-spin'),
(92, 'AGC', 'SMARTSOFT', 92, 'Smart Soft', 'SLOT', 0, 1, 'smart-soft'),
(93, 'AGC', 'DS', 93, 'Dragoon Soft', 'SLOT', 0, 1, 'dragon-soft'),
(94, 'AGC', 'NS', 94, 'Next Spin', 'SLOT', 0, 1, 'next-spin'),
(95, 'AGC', 'R88', 95, 'Rich88', 'SLOT', 0, 1, 'rich-88'),
(96, 'AGC', 'ILOVEU', 96, 'Iloveu', 'SLOT', 0, 1, 'i-love-you'),
(97, 'AGC', 'ASKMEBET', 97, 'ASKMEBET', 'SLOT', 0, 1, 'ask-me-bet'),
(98, 'AGC', 'P3D', 98, 'Perdana 3D', 'LOTTO', 0, 1, 'perdana-3d'),
(99, 'AGC', 'VIVO', 99, 'Vivo Gaming', 'CASINO', 0, 1, 'vivo-gaming'),
(100, 'AGC', 'ONCASINO', 100, 'Onbet Casino', 'CASINO', 0, 1, 'onbet-casino'),
(101, 'AGC', 'BetSwiz', 101, 'BetSwiz', 'SPORT', 0, 1, 'img-bet-swiz'),
(102, 'AGC', 'CMD', 102, 'CMD', 'SPORT', 0, 1, 'img-cmd'),
(103, 'AGC', 'AFB', 103, 'AFB Casino', 'CASINO', 0, 1, 'img-afb-casino'),
(104, 'AGC', 'EVO888', 104, 'Evo888', 'SLOT', 0, 1, 'img-evo-888'),
(105, 'AGC', 'ML2', 105, 'Malaysia Lottery 2', 'LOTTO', 0, 1, 'img-ml-2');


INSERT INTO `permission` (`id`,`permission_key`, `main`) VALUES (82,'payment_gateway', 1);

-- MIGATE PROD 2024-12-26

INSERT INTO `admin_action_type` (`id`, `name`, `detail`) VALUES (18, 'ADMIN_ACTION_MARKETING_CONFIG', 'Admin manage marketing config');
INSERT INTO `admin_action_type` (`id`, `name`, `detail`) VALUES (19, 'ADMIN_ACTION_PROMOTION_RETURN', 'Admin manage promotion return');

/* จัดการ banner */

CREATE TABLE IF NOT EXISTS `banner_setting` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `lang` VARCHAR(255) NOT NULL,
    `image_url` VARCHAR(255) NOT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    `created_by_id` BIGINT NOT NULL,
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
    `updated_by_id` BIGINT NULL DEFAULT NULL,
    `deleted_at` DATETIME NULL DEFAULT NULL,
    `deleted_by_id` BIGINT NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
);

-- MIGATE PROD 2025-01-06

CREATE TABLE IF NOT EXISTS `promotion_web_lock_credit` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT NOT NULL,
    `promotion_id` BIGINT NOT NULL,
    `bonus_amount` DECIMAL(10,2) NOT NULL DEFAULT 0,
    `is_locked` TINYINT NOT NULL DEFAULT 0,
    `created_at` DATETIME DEFAULT NOW(),
    PRIMARY KEY (`id`)
);

UPDATE `promotion_web_status` SET `label_th`='ปิดการแสเงผลหน้าเว็บไซต์แต่เปิดใช้งานอยู่' WHERE  `id`=1;

-- MIGATE PROD 2025-01-09

-- 2025-01-02 UserTierSetting

-- INSERT INTO `admin_group_permission` (`group_id`, `permission_id`) VALUES (1, 83);

CREATE TABLE IF NOT EXISTS `user_tier_setting` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `type` VARCHAR(255) NOT NULL,
    `sort_order` INT NOT NULL,
    `name` VARCHAR(255) NOT NULL,
    `from_value` BIGINT NOT NULL,
    `to_value` BIGINT NOT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
    PRIMARY KEY (`id`)
);
CREATE UNIQUE INDEX `uni_type_sort_order` ON `user_tier_setting` (`type`, `sort_order`);

CREATE TABLE IF NOT EXISTS `user_tier_data` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT NOT NULL,
    `total_deposit` DECIMAL(14,2),
    `total_turn` DECIMAL(14,2),
    `created_at` DATETIME DEFAULT NOW(),
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
    PRIMARY KEY (`id`),
    UNIQUE KEY `uni_user_id` (`user_id`)
);

-- activity daily v2

INSERT INTO `permission` (`id`, `permission_key`, `main`, `name`) VALUES (83, 'speciel_setting', 1, 'Setting พิเศษ');

CREATE TABLE IF NOT EXISTS `activity_daily_v2_total` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `total_able_revice_no` INT NOT NULL,
    `change_count_time` INT NOT NULL,
    `is_active` TINYINT NOT NULL DEFAULT 0,
    `created_at` DATETIME DEFAULT NOW(),
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
    `updated_by_id` BIGINT NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
);

INSERT INTO `activity_daily_v2_total` (`total_able_revice_no`, `change_count_time`) VALUES (0, 0);


CREATE TABLE IF NOT EXISTS `activity_daily_v2` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `change_count_time` INT NOT NULL,
    `no_number` INT NOT NULL,
    `credit_amount` DECIMAL(14,2) NOT NULL,
    `is_higlight` TINYINT NOT NULL DEFAULT 0,
    `created_at` DATETIME DEFAULT NOW(),
    `created_by_id` BIGINT NOT NULL,
    PRIMARY KEY (`id`)
);


CREATE TABLE IF NOT EXISTS `activity_daily_v2_user` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `activity_daily_v2_id` BIGINT NOT NULL,
    `change_count_time` INT NOT NULL,
    `no_number` INT NOT NULL,
    `user_id` BIGINT NOT NULL,
    `credit_amount` DECIMAL(14,2) NOT NULL,
    `received_at` DATETIME NULL DEFAULT NULL,
    `date_received` DATE NULL DEFAULT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
    PRIMARY KEY (`id`),
    UNIQUE KEY `uni_user_id_date_received` (`user_id`, `date_received`)
);

INSERT INTO `turnover_statement_type` (`id`, `name`) VALUES (18, 'TURN_BONUS_ACTIVITY_DAILY_V2');

ALTER TABLE `turnover_setting`
	ADD COLUMN `tidturn_activity_daily_v2_percent` INT NOT NULL DEFAULT '100' AFTER `tidturn_activity_daily_percent`;

INSERT INTO `permission` (`id`, `permission_key`, `main`, `name`) VALUES (84, 'promotion_view', 1, 'ดูโปรโมชั่น');
INSERT INTO `permission` (`id`, `permission_key`, `main`, `name`) VALUES (85, 'promotion_edit', 1, 'แก้ไขโปรโมชั่น');
INSERT INTO `permission` (`id`, `permission_key`, `main`, `name`) VALUES (86, 'promotion_delete', 1, 'ลบโปรโมชั่น');
INSERT INTO `permission` (`id`, `permission_key`, `main`, `name`) VALUES (87, 'promotion_approved', 1, 'อนุมัติโปรโมชั่นสมาชิกที่รับโปรโมชั่น');
INSERT INTO `permission` (`id`, `permission_key`, `main`, `name`) VALUES (88, 'promotion_canceled', 1, 'ยกเลิกโปรโมชั่นสมาชิกที่รับโปรโมชั่น');


INSERT INTO `permission` (`id`, `permission_key`, `main`, `name`) VALUES (89, 'payment_gateway_edit', 1, 'แก้ไข Payment Gateway');

ALTER TABLE `promotion_web_user`
	ADD COLUMN `approve_credit_by_admin_id` BIGINT NULL DEFAULT NULL AFTER `deleted_by_admin_id`;

ALTER TABLE `promotion_web_user`
	ADD COLUMN `approve_credit_at` DATETIME NULL DEFAULT NULL AFTER `canceled_at`;

ALTER TABLE `promotion_web_lock_credit`
	ADD COLUMN `promotion_web_user_id` BIGINT NULL AFTER `user_id`;


-- MIGATE PROD 2025-01-17
CREATE TABLE IF NOT EXISTS `user_withdraw_lock_credit_type` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL,
    `label_th` VARCHAR(255) NOT NULL,
    PRIMARY KEY (`id`)
);

INSERT INTO `user_withdraw_lock_credit_type` (`id`, `name`,`label_th`) VALUES (1, 'USER_WITHDRAW_LOCK_CREDIT_TYPE_PROMOTION', 'โปรโมชั่น');

CREATE TABLE IF NOT EXISTS `user_withdraw_lock_credit` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT NOT NULL,
    `ref_id` BIGINT NOT NULL,
    `detail` TEXT NOT NULL,
    `user_withdraw_lock_credit_type_id` BIGINT NOT NULL,
    `credit_more_than` DECIMAL(14,2) NOT NULL,
    `allow_withdraw_amount` DECIMAL(14,2) NOT NULL,
    `withdraw_amount` DECIMAL(14,2) NOT NULL,
    `pull_credit_amount` DECIMAL(14,2) NOT NULL,
    `is_locked` TINYINT NOT NULL DEFAULT 0,
    `is_pull_credit` TINYINT NOT NULL DEFAULT 0,
    `created_at` DATETIME DEFAULT NOW(),
    `approved_at` DATETIME NULL DEFAULT NULL,
    `approved_by_id` BIGINT NULL DEFAULT NULL,
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
    PRIMARY KEY (`id`)
);

UPDATE `promotion_web_status` SET `label_th`='ปิดการแสดงผลหน้าเว็บไซต์แต่เปิดใช้งานอยู่' WHERE  `id`=1;

-- MIGATE PROD 2025-01-23

CREATE TABLE IF NOT EXISTS `report_summary_dashboard_rerun` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `action_key` VARCHAR(255) NOT NULL,
    `start_date` VARCHAR(255) NOT NULL,
    `end_date` VARCHAR(255) NOT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    `created_by_id` BIGINT NOT NULL,
    PRIMARY KEY (`id`)
);

ALTER TABLE `configuration_web`
	ADD COLUMN `is_show_deposit` TINYINT(1) NOT NULL DEFAULT '1' AFTER `check_account_name_fastbank`,
	ADD COLUMN `is_show_withdraw` TINYINT(1) NOT NULL DEFAULT '1' AFTER `is_show_deposit`;

ALTER TABLE `promotion_web`
	ADD COLUMN `hidden_url_link` VARCHAR(255) NOT NULL DEFAULT '' AFTER `time_end`;

  INSERT INTO `promotion_web_status` (`id`, `name`, `label_th`, `label_en`) VALUES (5, 'ONLY_URL', 'เฉพาะลูกค้าที่เข้าจากลิ้งก์', 'use link');


-- MIGATE PROD 2025-01-xx
-- ไม่แน่ใจ ว่าจะใช้หรือไม่ ครับ แต่เห็น comment
-- CREATE TABLE IF NOT EXISTS `agent_f888_hook` (
--     `id` BIGINT NOT NULL AUTO_INCREMENT,
--     `name` VARCHAR(255) NOT NULL,
--     `json_payload` LONGTEXT NOT NULL,
--     `created_at` DATETIME DEFAULT NOW(),
--     PRIMARY KEY (`id`)
-- );

-- UP PROD แล้ว เมื่อ 2025-01-28 --
ALTER TABLE `invoice`
	ADD COLUMN `bank_id` BIGINT NULL DEFAULT NULL AFTER `package_detail`,
	ADD COLUMN `account_no` VARCHAR(255) NULL DEFAULT NULL AFTER `bank_id`,
	ADD COLUMN `account_name` VARCHAR(255) NULL DEFAULT NULL AFTER `account_no`;

-- MIGATE PROD 2025-01-30

CREATE TABLE IF NOT EXISTS `agent_pg_hard_callback` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT NOT NULL,
    `member_code` VARCHAR(255) NOT NULL,
    `transaction_id` VARCHAR(255) NOT NULL,
    `payoff` DECIMAL(14,2) NOT NULL,
    `bet_amount` DECIMAL(14,2) NOT NULL,
    `winlose_amount` DECIMAL(14,2) NOT NULL,
    `balance` DECIMAL(14,2) NOT NULL,
    `before_balance` DECIMAL(14,2) NOT NULL,
    `after_balance` DECIMAL(14,2) NOT NULL,
    `round_id` VARCHAR(255) NOT NULL,
    `game_id` VARCHAR(255) NOT NULL,
    `game_string_id` VARCHAR(255) NOT NULL,
    `game_name` VARCHAR(255) NOT NULL,
    `remark` VARCHAR(255) NOT NULL,
    `is_success` TINYINT NOT NULL DEFAULT 0,
    `created_at` DATETIME DEFAULT NOW(),
    PRIMARY KEY (`id`)
);

CREATE INDEX `idx_user_id` ON `agent_pg_hard_callback` (`user_id`);
CREATE INDEX `idx_member_code` ON `agent_pg_hard_callback` (`member_code`);
CREATE INDEX `idx_is_success` ON `agent_pg_hard_callback` (`is_success`);
CREATE INDEX `idx_created_at` ON `agent_pg_hard_callback` (`created_at`);

CREATE TABLE IF NOT EXISTS `agent_pg_hard_setting` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `is_active` TINYINT NOT NULL DEFAULT 0,
    `program_allow_use` ENUM('ALLOW_USE','NOT_ALLOW_USE') NOT NULL DEFAULT 'NOT_ALLOW_USE',
    `pg_hard_private_key` VARCHAR(255) DEFAULT '',
    `pg_hard_operator_id` VARCHAR(255) DEFAULT '',
    `pg_hard_url` VARCHAR(255) DEFAULT '',
    `pg_hard_href_back_url` VARCHAR(255) DEFAULT '',
    `pg_hard_preset_id` VARCHAR(255) DEFAULT '',
    `created_at` DATETIME DEFAULT NOW(),
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
    `updated_by_id` BIGINT NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
);

INSERT INTO `agent_pg_hard_setting` (`id`, `is_active`, `program_allow_use`) VALUES
(1, 0, 'NOT_ALLOW_USE');

-- MIGATE PROD 2025-01-31

UPDATE `agent_pg_hard_setting` SET `pg_hard_url`='https://api.pghard.com' WHERE  `id`=1;

-- MIGATE PROD 2025-02-03

UPDATE `agent_game_priority_setting` SET `vendor_code`='QQ' WHERE  `id`=86;

-- MIGATE PROD 2025-02-14 ข้างล่างนี้ยังไม่ได้ขึ้น PROD

CREATE TABLE IF NOT EXISTS `paygate_onepay_order` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT NOT NULL,
    `order_type_id` BIGINT NOT NULL,
    `ref_id` BIGINT NULL DEFAULT NULL,
    `order_no` VARCHAR(255) NOT NULL,
    `amount` DECIMAL(10,2) NOT NULL DEFAULT 0,
    `transfer_amount` DECIMAL(10,2) NOT NULL DEFAULT 0,
    `transaction_no` VARCHAR(255) NULL,
    `transaction_date` DATETIME NULL,
    `transaction_status` VARCHAR(255) NULL,
    `payment_at` DATETIME NULL DEFAULT NULL,
    `bank_transaction_id` BIGINT NULL DEFAULT NULL,
    `bank_transaction_status` VARCHAR(255) NULL DEFAULT 'PENDING',
    `qr_promptpay` VARCHAR(255) NULL,
    `payment_page_url` VARCHAR(255) NULL,
    `note` VARCHAR(255) NULL DEFAULT NULL,
    `remark` VARCHAR(1000) NULL DEFAULT NULL,
    `action_by` BIGINT NULL DEFAULT NULL,
    `api_remark` VARCHAR(255) NULL DEFAULT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
    PRIMARY KEY (`id`)
);
CREATE UNIQUE INDEX `uni_order_no` ON `paygate_onepay_order` (`order_no`);
CREATE UNIQUE INDEX `uni_transaction_no` ON `paygate_onepay_order` (`transaction_no`);
CREATE INDEX `idx_order_type_id` ON `paygate_onepay_order` (`order_type_id`);

CREATE TABLE IF NOT EXISTS `paygate_onepay_webhook` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL,
    `json_payload` LONGTEXT NOT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    PRIMARY KEY (`id`)
);

INSERT INTO `paygate_merchant` (`id`, `name`, `type_name`, `has_deposit`, `is_deposit_enabled`, `has_withdraw`, `is_withdraw_enabled`, `api_end_point`, `shop_name`, `username`, `password`, `balance`, `callback_url`)
VALUES (9, 'ONEPAY', 'ONEPAY', 1, 0, 1, 0, 'https://api-proxy.cbgame88.com/onepay', '', '', '', 0, '');

CREATE TABLE IF NOT EXISTS `paygate_onepay_token` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `access_token` VARCHAR(2048) NOT NULL,
    `expire_at` DATETIME NOT NULL,
    `create_by` BIGINT NOT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
    PRIMARY KEY (`id`)
);

ALTER TABLE `paygate_onepay_order`
	CHANGE COLUMN `qr_promptpay` `qr_promptpay` TEXT NULL COLLATE 'utf8mb4_general_ci' AFTER `bank_transaction_status`;

UPDATE `agent_pg_hard_setting` SET `program_allow_use`='ALLOW_USE' WHERE  `id`=1;

-- MIGATE PROD 2025-02-14


CREATE TABLE IF NOT EXISTS `agent_ctw_setting` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `is_active` TINYINT NOT NULL DEFAULT 0,
    `program_allow_use` ENUM('ALLOW_USE','NOT_ALLOW_USE') NOT NULL DEFAULT 'NOT_ALLOW_USE',
    `ctw_app_id` VARCHAR(255) DEFAULT '',
    `ctw_app_private` VARCHAR(255) DEFAULT '',
    `ctw_url` VARCHAR(255) DEFAULT '',
    `ctw_href_back_url` VARCHAR(255) DEFAULT '',
    `created_at` DATETIME DEFAULT NOW(),
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
    `updated_by_id` BIGINT NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
);

-- stage https://ctw-seamless-api-staging.u2fh4j.com ใช้ แค่ตอน dev หรือ local only
-- prod https://ctw-seamless-api.u2fh4j.com

INSERT INTO `agent_ctw_setting` (`id`, `is_active`, `program_allow_use`, `ctw_url`) VALUES
(1, 0, 'ALLOW_USE', 'https://ctw-seamless-api.u2fh4j.com');


CREATE TABLE IF NOT EXISTS `agent_ctw_callback` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT NOT NULL,
    `member_code` VARCHAR(255) NOT NULL,
    `payoff` DECIMAL(14,2) NOT NULL,
    `bet_amount` DECIMAL(14,2) NOT NULL,
    `winlose_amount` DECIMAL(14,2) NOT NULL,
    `balance` DECIMAL(14,2) NOT NULL,
    `before_balance` DECIMAL(14,2) NOT NULL,
    `after_balance` DECIMAL(14,2) NOT NULL,
    `transaction_id` VARCHAR(255) NOT NULL,
    `round_id` VARCHAR(255) NOT NULL, -- ใช้ round ID เป็นหลัก เพราะมัน stack up จาก round
    `game_id` VARCHAR(255) NOT NULL,
    `callback_reason` VARCHAR(255) NOT NULL,
    `remark` VARCHAR(255) NOT NULL,
    `is_success` TINYINT NOT NULL DEFAULT 0,
    `created_at` DATETIME DEFAULT NOW(),
    PRIMARY KEY (`id`)
);

CREATE INDEX `idx_user_id` ON `agent_ctw_callback` (`user_id`);
CREATE INDEX `idx_member_code` ON `agent_ctw_callback` (`member_code`);
CREATE INDEX `idx_is_round_id` ON `agent_ctw_callback` (`round_id`);
CREATE INDEX `idx_is_success` ON `agent_ctw_callback` (`is_success`);
CREATE INDEX `idx_created_at` ON `agent_ctw_callback` (`created_at`);


-- MIGATE PROD 2025-02-21
-- activity_daily_v2_total

CREATE TABLE IF NOT EXISTS `activity_daily_v2_total_condition` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL,
    `label_th` VARCHAR(255) NOT NULL,
    `label_en` VARCHAR(255) NOT NULL,
    PRIMARY KEY (`id`)
);

INSERT INTO `activity_daily_v2_total_condition` (`id`, `name`, `label_th`, `label_en`) VALUES
(1, 'no condition', 'ไม่มีเงื่อนไข', 'no condition'),
(2, 'min_deposit', 'ฝากขั้นต่ำ', 'min deposit'),
(3, 'overall_deposit', 'ฝากตามยอด', 'overall deposit');

ALTER TABLE `activity_daily_v2_total` ADD COLUMN `activity_daily_v2_total_condition_id` BIGINT DEFAULT 1 AFTER `change_count_time`;
ALTER TABLE `activity_daily_v2_total` ADD COLUMN `condition_amount` DECIMAL(14,2) DEFAULT 0 AFTER `activity_daily_v2_total_condition_id`;

ALTER TABLE `activity_daily_v2_total` ADD CONSTRAINT `fk_activity_daily_v2_total_condition_id` FOREIGN KEY (`activity_daily_v2_total_condition_id`) REFERENCES `activity_daily_v2_total_condition`(`id`);

-- MIGATE PROD 2025-02-28

INSERT INTO `permission` (`id`, `permission_key`, `main`, `name`) VALUES (90, 'admin_manage_create', 1, 'เพิ่มผู้ใช้งาน');
INSERT INTO `permission` (`id`, `permission_key`, `main`, `name`) VALUES (91, 'admin_manage_edit', 1, 'แก้ไขผู้ใช้งาน');
INSERT INTO `permission` (`id`, `permission_key`, `main`, `name`) VALUES (92, 'admin_manage_reset_2fa', 1, 'รีเชตการใช้งาน 2FA');
INSERT INTO `permission` (`id`, `permission_key`, `main`, `name`) VALUES (93, 'admin_manage_delete', 1, 'ลบผู้ใช้งาน');
INSERT INTO `permission` (`id`, `permission_key`, `main`, `name`) VALUES (94, 'admin_manage_group', 1, 'กลุ่มผู้ใช้งาน');
INSERT INTO `permission` (`id`, `permission_key`, `main`, `name`) VALUES (95, 'admin_manage_login_history', 1, 'ประวัติการเข้าสู่ระบบผู้ใช้งาน');
INSERT INTO `permission` (`id`, `permission_key`, `main`, `name`) VALUES (96, 'admin_manage_transaction_history', 1, 'ประวัติการทำรายการ');
INSERT INTO `permission` (`id`, `permission_key`, `main`, `name`) VALUES (97, 'admin_manage', 1, 'จัดการผู้ใช้งาน');

-- 2025-02-25 FlashPay

CREATE TABLE IF NOT EXISTS `paygate_flashpay_order` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT NOT NULL,
    `order_type_id` BIGINT NOT NULL,
    `ref_id` BIGINT NULL DEFAULT NULL,
    `order_no` VARCHAR(255) NOT NULL,
    `amount` DECIMAL(10,2) NOT NULL DEFAULT 0,
    `transfer_amount` DECIMAL(10,2) NOT NULL DEFAULT 0,
    `transaction_no` VARCHAR(255) NULL,
    `transaction_date` DATETIME NULL,
    `transaction_status` VARCHAR(255) NULL,
    `payment_at` DATETIME NULL DEFAULT NULL,
    `bank_transaction_id` BIGINT NULL DEFAULT NULL,
    `bank_transaction_status` VARCHAR(255) NULL DEFAULT 'PENDING',
    `qr_promptpay` VARCHAR(255) NULL,
    `payment_page_url` VARCHAR(255) NULL,
    `note` VARCHAR(255) NULL DEFAULT NULL,
    `remark` VARCHAR(1000) NULL DEFAULT NULL,
    `action_by` BIGINT NULL DEFAULT NULL,
    `api_remark` VARCHAR(255) NULL DEFAULT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
    PRIMARY KEY (`id`)
);
CREATE UNIQUE INDEX `uni_order_no` ON `paygate_flashpay_order` (`order_no`);
CREATE UNIQUE INDEX `uni_transaction_no` ON `paygate_flashpay_order` (`transaction_no`);
CREATE INDEX `idx_order_type_id` ON `paygate_flashpay_order` (`order_type_id`);

CREATE TABLE IF NOT EXISTS `paygate_flashpay_webhook` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL,
    `json_payload` LONGTEXT NOT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    PRIMARY KEY (`id`)
);

INSERT INTO `paygate_merchant` (`id`, `name`, `type_name`, `has_deposit`, `is_deposit_enabled`, `has_withdraw`, `is_withdraw_enabled`, `api_end_point`, `shop_name`, `username`, `password`, `balance`, `callback_url`)
VALUES (10, 'FLASHPAY', 'FLASHPAY', 1, 0, 1, 0, 'https://lb.cluster02.flash-pay.io', 'Flashpay', '', '', 0, '');

CREATE TABLE IF NOT EXISTS `paygate_flashpay_token` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `access_token` VARCHAR(2048) NOT NULL,
    `expire_at` DATETIME NOT NULL,
    `create_by` BIGINT NOT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
    PRIMARY KEY (`id`)
);

-- EOF FlashPay
-- UP PROD แล้ว 2025-02-28

-- agent migration 2025-03-07

INSERT INTO `agent_game_priority_setting`
(`id`, `agent`, `vendor_code`, `priority_order`, `detail`, `category_name`, `is_popular`, `is_show`, `image_name`) VALUES
(106, 'AGC', 'TF', 106, 'TF Gaming', 'SPORT', 0, 1, 'img-tf-gaming'),
(107, 'AGC', 'RTG', 107, 'RTG', 'SLOT', 0, 1, 'img-rtg'),
(108, 'AGC', 'BETTER', 108, 'BT-Gaming', 'SLOT', 0, 1, 'img-bt-g'),
(109, 'AGC', 'SPRIBE', 109, 'Spribe Aviator', 'SLOT', 0, 1, 'img-spribe'),
(110, 'AGC', 'Eloto', 110, 'Eloto', 'LOTTO', 0, 1, 'img-e-loto');


INSERT INTO `agent_game_priority_setting` (`id`, `agent`, `vendor_code`, `priority_order`, `detail`, `category_name`, `is_popular`, `is_show`, `image_name`) VALUES
(111, 'AGC', 'G5G', 110, '5G Games', 'SLOT', 0, 1, 'img-g-5g');


-- MIGATE PROD 2025-03-18

CREATE TABLE `configuration_register_format` (
	`id` BIGINT NOT NULL AUTO_INCREMENT,
	`name` VARCHAR(255) NOT NULL,
	`label_th` VARCHAR(255) NOT NULL,
	`label_en` VARCHAR(255) NOT NULL,
	PRIMARY KEY (`id`)
);

INSERT INTO `configuration_register_format` (`id`, `name`, `label_th`, `label_en`) VALUES
(1, 'DEFALT', 'ยืนยันเบอร์โทรศัพท์-รหัสผ่านและข้อมูลธนาคาร', 'Phone - password and bank detail'),
(2, 'SPERATE_BANK', 'ยืนยันเบอร์โทรศัพท์-รหัสผ่าน-ข้อมูลธนาคาร', 'Phone - password - bank detail');

ALTER TABLE `configuration_web`
	ADD COLUMN `configuration_register_format_id` BIGINT NULL DEFAULT '1' AFTER `turn_withdraw_type_id`;

ALTER TABLE `category_game_setting`
	ADD COLUMN `is_open_cb_lotto` TINYINT NOT NULL DEFAULT '0' AFTER `is_open_external_lotto`;


-- 2025-03-24 BIZPAY

CREATE TABLE IF NOT EXISTS `paygate_bizpay_order` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT NOT NULL,
    `order_type_id` BIGINT NOT NULL,
    `ref_id` BIGINT NULL DEFAULT NULL,
    `order_no` VARCHAR(255) NOT NULL,
    `amount` DECIMAL(10,2) NOT NULL DEFAULT 0,
    `transfer_amount` DECIMAL(10,2) NOT NULL DEFAULT 0,
    `transaction_no` VARCHAR(255) NULL,
    `transaction_date` DATETIME NULL,
    `transaction_status` VARCHAR(255) NULL,
    `payment_at` DATETIME NULL DEFAULT NULL,
    `bank_transaction_id` BIGINT NULL DEFAULT NULL,
    `bank_transaction_status` VARCHAR(255) NULL DEFAULT 'PENDING',
    `qr_promptpay` VARCHAR(255) NULL,
    `payment_page_url` VARCHAR(255) NULL,
    `note` VARCHAR(255) NULL DEFAULT NULL,
    `remark` VARCHAR(1000) NULL DEFAULT NULL,
    `action_by` BIGINT NULL DEFAULT NULL,
    `api_remark` VARCHAR(255) NULL DEFAULT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
    PRIMARY KEY (`id`)
);
CREATE UNIQUE INDEX `uni_order_no` ON `paygate_bizpay_order` (`order_no`);
CREATE UNIQUE INDEX `uni_transaction_no` ON `paygate_bizpay_order` (`transaction_no`);
CREATE INDEX `idx_order_type_id` ON `paygate_bizpay_order` (`order_type_id`);

CREATE TABLE IF NOT EXISTS `paygate_bizpay_webhook` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL,
    `json_payload` LONGTEXT NOT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    PRIMARY KEY (`id`)
);

INSERT INTO `paygate_merchant` (`id`, `name`, `type_name`, `has_deposit`, `is_deposit_enabled`, `has_withdraw`, `is_withdraw_enabled`, `api_end_point`, `shop_name`)
VALUES (11, 'BIZPAY', 'BIZPAY', 1, 0, 1, 0, 'https://api.dev.bit-payz.com:1337', 'BizPayment');

-- EOF BIZPAY

-- SELECT * FROM `paygate_merchant` WHERE `deleted_at` IS NULL;
-- SELECT paygate_setting.merchant_id, paygate_merchant.name, paygate_setting.status FROM paygate_setting JOIN paygate_merchant ON paygate_setting.merchant_id = paygate_merchant.id;
-- UPDATE `paygate_merchant` SET `has_deposit`=0, `is_deposit_enabled`=0, `has_withdraw`=0, `is_withdraw_enabled`=0, `deleted_at`='2025-03-27 13:01:02' WHERE  `id` IN (1,2,3,7,9,10);
-- SELECT * FROM `paygate_merchant` WHERE `deleted_at` IS NULL;

-- Migrate ส่วนนี้แล้ว เมื่อ 2025-03-31

ALTER TABLE `paygate_merchant`
	ADD COLUMN `display_name` VARCHAR(255) NOT NULL AFTER `name`;

UPDATE `paygate_merchant` SET `display_name`='เติมเงินผ่าน QR Code' WHERE  `display_name`='';

ALTER TABLE `admin_action`
	ADD COLUMN `is_show` TINYINT NOT NULL DEFAULT '1' AFTER `detail`;

UPDATE `admin_action` SET `is_show`=0 WHERE  detail LIKE 'INVALID_ADMIN_TOKEN_%';

-- ^^^^ Migrate ส่วนบนนี้ ไปก่อนแล้ว เมื่อ 2025-03-31 ^^^^^

-- MIGATE PROD 2025-03-xx

ALTER TABLE `telegram_last_update`
	ADD COLUMN `telegram_token` VARCHAR(255) NULL DEFAULT NULL AFTER `update_uid`;

INSERT INTO `permission` (`id`, `permission_key`, `main`, `name`) VALUES (98, 'playlist', 1, 'รายการเล่น');
INSERT INTO `permission` (`id`, `permission_key`, `main`, `name`) VALUES (99, 'setting_game', 1, 'ตั้งค่าเกมส์');
INSERT INTO `permission` (`id`, `permission_key`, `main`, `name`) VALUES (100, 'send_sms', 1, 'ส่ง sms');

-- 2025-03-24 SUGARPAY

CREATE TABLE IF NOT EXISTS `paygate_sugarpay_order` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT NOT NULL,
    `order_type_id` BIGINT NOT NULL,
    `ref_id` BIGINT NULL DEFAULT NULL,
    `order_no` VARCHAR(255) NOT NULL,
    `amount` DECIMAL(10,2) NOT NULL DEFAULT 0,
    `transfer_amount` DECIMAL(10,2) NOT NULL DEFAULT 0,
    `transaction_no` VARCHAR(255) NULL,
    `transaction_date` DATETIME NULL,
    `transaction_status` VARCHAR(255) NULL,
    `payment_at` DATETIME NULL DEFAULT NULL,
    `bank_transaction_id` BIGINT NULL DEFAULT NULL,
    `bank_transaction_status` VARCHAR(255) NULL DEFAULT 'PENDING',
    `qr_promptpay` VARCHAR(255) NULL,
    `payment_page_url` VARCHAR(255) NULL,
    `note` VARCHAR(255) NULL DEFAULT NULL,
    `remark` VARCHAR(1000) NULL DEFAULT NULL,
    `action_by` BIGINT NULL DEFAULT NULL,
    `api_remark` VARCHAR(255) NULL DEFAULT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
    PRIMARY KEY (`id`)
);
CREATE UNIQUE INDEX `uni_order_no` ON `paygate_sugarpay_order` (`order_no`);
CREATE UNIQUE INDEX `uni_transaction_no` ON `paygate_sugarpay_order` (`transaction_no`);
CREATE INDEX `idx_order_type_id` ON `paygate_sugarpay_order` (`order_type_id`);

CREATE TABLE IF NOT EXISTS `paygate_sugarpay_webhook` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL,
    `json_payload` LONGTEXT NOT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    PRIMARY KEY (`id`)
);

INSERT INTO `paygate_merchant` (`id`, `name`, `display_name`, `type_name`, `has_deposit`, `is_deposit_enabled`, `has_withdraw`, `is_withdraw_enabled`, `api_end_point`, `shop_name`)
VALUES (12, 'SUGARPAY','เติมเงินผ่าน QR Code', 'SUGARPAY', 1, 0, 1, 0, 'https://api-proxy.cbgame88.com/sugarpay', 'Edit shop name here');

-- EOF SUGARPAY

-- ข้างล่างนี้ PROD ยังไม่ได้ Migrate

DELETE FROM `agent_game_priority_setting` WHERE  `vendor_code`='MT';

-- 2025-04-18 Multiple Paygate Account
CREATE TABLE `paygate_account` (
	`id` BIGINT NOT NULL AUTO_INCREMENT,
	`provider_id` BIGINT NOT NULL,
	`name` VARCHAR(255) NOT NULL COLLATE 'utf8mb4_general_ci',
	`display_name` VARCHAR(255) NOT NULL COLLATE 'utf8mb4_general_ci',
	`type_name` VARCHAR(255) NOT NULL COLLATE 'utf8mb4_general_ci',
	`has_deposit` TINYINT NOT NULL DEFAULT '0',
	`is_deposit_enabled` TINYINT NOT NULL DEFAULT '0',
	`has_withdraw` TINYINT NOT NULL DEFAULT '0',
	`is_withdraw_enabled` TINYINT NOT NULL DEFAULT '0',
	`api_end_point` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
	`shop_name` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
	`username` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
	`password` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
	`private_key` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
	`access_key` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
	`secret_key` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
	`partner_key` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
	`repay_app_id` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
	`loan_app_id` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
	`merchant_id` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
	`token` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
	`aes_key` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
	`balance` DECIMAL(10,2) NOT NULL DEFAULT '0.00',
	`payment_deposit_minimum` DECIMAL(10,2) NOT NULL DEFAULT '0.00',
	`payment_deposit_maximum` DECIMAL(10,2) NOT NULL DEFAULT '0.00',
	`payment_withdraw_minimum` DECIMAL(10,2) NOT NULL DEFAULT '0.00',
	`payment_withdraw_maximum` DECIMAL(10,2) NOT NULL DEFAULT '0.00',
	`merchant_update_at` DATETIME NULL DEFAULT NULL,
	`callback_url` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
	`created_at` DATETIME NULL DEFAULT (CURRENT_TIMESTAMP),
	`updated_at` DATETIME NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
	`deleted_at` DATETIME NULL DEFAULT NULL,
	PRIMARY KEY (`id`)
);

-- for now, 1 provider_id = 1 account only
CREATE UNIQUE INDEX `idx_provider_id` ON `paygate_account` (`provider_id`);

-- then if paygate_setting.merchant_id and paygate_setting.status = 'ACTIVE' then INSERT selected merchant_id all columns to paygate_account
INSERT INTO paygate_account (
    provider_id, name, display_name, type_name, has_deposit, is_deposit_enabled, has_withdraw, is_withdraw_enabled, api_end_point,
    shop_name, username, password, private_key, access_key, secret_key, partner_key, repay_app_id, loan_app_id, merchant_id,
    token, aes_key, balance, payment_deposit_minimum, payment_deposit_maximum, payment_withdraw_minimum, payment_withdraw_maximum, merchant_update_at,
    callback_url
) SELECT
    m.id AS provider_id, m.name, m.display_name, m.type_name, m.has_deposit, m.is_deposit_enabled, m.has_withdraw, m.is_withdraw_enabled,
    m.api_end_point, m.shop_name, m.username, m.password, m.private_key, m.access_key, m.secret_key, m.partner_key, m.repay_app_id, m.loan_app_id,
    m.merchant_id, m.token, m.aes_key, m.balance, m.payment_deposit_minimum, m.payment_deposit_maximum, m.payment_withdraw_minimum, m.payment_withdraw_maximum,
    m.updated_at AS merchant_update_at, m.callback_url
FROM paygate_merchant m
JOIN paygate_setting s ON s.merchant_id = m.id
WHERE s.status = 'ACTIVE';

-- Review data in paygate_account
SELECT * FROM paygate_setting;
SELECT * FROM paygate_account WHERE deleted_at IS NULL;

-- migrate 2025-04-25

INSERT INTO `transaction_type` (`name`, `label_th`, `label_en`) VALUES ('CANCELCREDIT', 'ยกเลิกเติมเครดิต', 'cancelcredit');
INSERT INTO `user_transaction_type` (`id`, `name`, `detail`) VALUES (14, 'CREDIT_TYPE_CANCEL_CREDIT', 'ยกเลิกเติมเครดิต');

ALTER TABLE `configuration_external_notification`
  ADD COLUMN `telegram_move_money` TINYINT NOT NULL DEFAULT 0 AFTER `telegram_activity_after_bonus`,
  ADD COLUMN `line_move_money` TINYINT NOT NULL DEFAULT 0 AFTER `line_activity_after_bonus`,
  ADD COLUMN `telegram_transaction_hour_summary` TINYINT NOT NULL DEFAULT 0 AFTER `telegram_move_money`,
  ADD COLUMN `line_transaction_hour_summary` TINYINT NOT NULL DEFAULT 0 AFTER `line_move_money`,
  ADD COLUMN `telegram_affiliate_daily_summary` TINYINT NOT NULL DEFAULT 0 AFTER `telegram_transaction_hour_summary`,
  ADD COLUMN `line_affiliate_daily_summary` TINYINT NOT NULL DEFAULT 0 AFTER `line_transaction_hour_summary`,
  ADD COLUMN `telegram_transaction_daily_summary` TINYINT NOT NULL DEFAULT 0 AFTER `telegram_affiliate_daily_summary`,
  ADD COLUMN `line_transaction_daily_summary` TINYINT NOT NULL DEFAULT 0 AFTER `line_affiliate_daily_summary`; 

-- ข้างบนถึงจุดนี้ Migrate PROD ทั้งหมดแล้ว

INSERT INTO `permission` (`id`, `permission_key`, `main`, `name`) VALUES (101, 'create_withdraw_tranasction', 1, 'บันทึกรายการถอน');
INSERT INTO `permission` (`id`, `permission_key`, `main`, `name`) VALUES (102, 'cancel_tranasction_credit', 1, 'ยกเลิกเติมเครดิต');

ALTER TABLE `affiliate_commission`
	ADD COLUMN `commission_from` VARCHAR(255) NOT NULL DEFAULT '' AFTER `max_level`;

-- ข้างบนถึงจุดนี้ Migrate PROD ทั้งหมดแล้ว

ALTER TABLE `configuration_web`
	ADD COLUMN `otp_key` VARCHAR(255) NOT NULL DEFAULT '' AFTER `token_expired_minute`;

CREATE TABLE IF NOT EXISTS `agent_pg_hard_callback_summary` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT NOT NULL,
    `statement_date` VARCHAR(255) NOT NULL,
    `member_code` VARCHAR(255) NOT NULL,
    `bet_amount` DECIMAL(14,2) NOT NULL,
    `payoff` DECIMAL(14,2) NOT NULL,
    `winlose_amount` DECIMAL(14,2) NOT NULL,
    `before_balance` DECIMAL(14,2) NOT NULL,
    `after_balance` DECIMAL(14,2) NOT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    PRIMARY KEY (`id`)
);

-- ข้างบนถึงจุดนี้ Migrate PROD ทั้งหมดแล้ว เมื่อ 2025-05-02

-- 2025-05-05 ZMANPAY

CREATE TABLE IF NOT EXISTS `paygate_zmanpay_order` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT NOT NULL,
    `order_type_id` BIGINT NOT NULL,
    `ref_id` BIGINT NULL DEFAULT NULL,
    `order_no` VARCHAR(255) NOT NULL,
    `amount` DECIMAL(10,2) NOT NULL DEFAULT 0,
    `transfer_amount` DECIMAL(10,2) NOT NULL DEFAULT 0,
    `transaction_no` VARCHAR(255) NULL,
    `transaction_date` DATETIME NULL,
    `transaction_status` VARCHAR(255) NULL,
    `payment_at` DATETIME NULL DEFAULT NULL,
    `bank_transaction_id` BIGINT NULL DEFAULT NULL,
    `bank_transaction_status` VARCHAR(255) NULL DEFAULT 'PENDING',
    `qr_promptpay` VARCHAR(255) NULL,
    `payment_page_url` VARCHAR(255) NULL,
    `note` VARCHAR(255) NULL DEFAULT NULL,
    `remark` VARCHAR(1000) NULL DEFAULT NULL,
    `action_by` BIGINT NULL DEFAULT NULL,
    `api_remark` VARCHAR(255) NULL DEFAULT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
    PRIMARY KEY (`id`)
);
CREATE UNIQUE INDEX `uni_order_no` ON `paygate_zmanpay_order` (`order_no`);
CREATE UNIQUE INDEX `uni_transaction_no` ON `paygate_zmanpay_order` (`transaction_no`);
CREATE INDEX `idx_order_type_id` ON `paygate_zmanpay_order` (`order_type_id`);

ALTER TABLE `paygate_zmanpay_order`
	ADD COLUMN `qr_base64` TEXT NULL AFTER `qr_promptpay`;

CREATE TABLE IF NOT EXISTS `paygate_zmanpay_webhook` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL,
    `json_payload` LONGTEXT NOT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    PRIMARY KEY (`id`)
);

INSERT INTO `paygate_merchant` (`id`, `name`, `display_name`, `type_name`, `has_deposit`, `is_deposit_enabled`, `has_withdraw`, `is_withdraw_enabled`, `api_end_point`, `shop_name`)
VALUES (13, 'ZMANPAY','เติมเงินผ่าน QR Code', 'ZMANPAY', 1, 0, 1, 0, 'https://api.zapman.net/api', 'Edit shop name here');

CREATE TABLE IF NOT EXISTS `paygate_zmanpay_customer`(
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `user_id` BIGINT NOT NULL,
  `customer_uuid` VARCHAR(255) NOT NULL,
  `full_name` VARCHAR(255) NOT NULL,
  `bank_code` VARCHAR(255) NOT NULL,
  `account_no` VARCHAR(255) NOT NULL,
  `account_name` VARCHAR(255) NOT NULL,
  PRIMARY KEY (`id`)
);
CREATE UNIQUE INDEX `uni_user_id` ON `paygate_zmanpay_customer` (`user_id`);

-- EOF ZMANPAY

-- รายการฝาก-ถอนเสร็จสิ้น เพิ่ม permission 'ลบ' รายการ
INSERT INTO `permission` (`id`, `permission_key`, `main`, `name`) VALUES (103, 'delete_user_transaction', 1, 'ลบรายการฝาก-ถอนเสร็จสิ้น');

ALTER TABLE `banner_setting`
	ADD COLUMN `link_url` VARCHAR(255) NOT NULL DEFAULT '' AFTER `image_url`,
	ADD COLUMN `is_show_public` TINYINT NOT NULL DEFAULT (0) AFTER `link_url`,
	ADD COLUMN `is_show_logedin` TINYINT NOT NULL DEFAULT (0) AFTER `is_show_public`;

-- default is_show_logedin = 1 if both is_show_public and is_show_logedin = 0
-- UPDATE `banner_setting` SET `is_show_logedin`=1 WHERE `is_show_public`=0 AND `is_show_logedin`=0;

ALTER TABLE `promotion_return_turn_setting`
  ADD COLUMN `return_sport_percent` TINYINT NOT NULL DEFAULT '0' AFTER `calc_on_sport`,
  ADD COLUMN `return_casino_percent` TINYINT NOT NULL DEFAULT '0' AFTER `calc_on_casino`,
  ADD COLUMN `return_game_percent` TINYINT NOT NULL DEFAULT '0' AFTER `calc_on_game`,
  ADD COLUMN `return_lottery_percent` TINYINT NOT NULL DEFAULT '0' AFTER `calc_on_lottery`,
  ADD COLUMN `return_p2p_percent` TINYINT NOT NULL DEFAULT '0' AFTER `calc_on_p2p`,
  ADD COLUMN `return_financial_percent` TINYINT NOT NULL DEFAULT '0' AFTER `calc_on_financial`;

UPDATE `promotion_return_turn_setting` SET `return_sport_percent`=`return_percent` WHERE `return_sport_percent`=0;
UPDATE `promotion_return_turn_setting` SET `return_casino_percent`=`return_percent` WHERE `return_casino_percent`=0;
UPDATE `promotion_return_turn_setting` SET `return_game_percent`=`return_percent` WHERE `return_game_percent`=0;
UPDATE `promotion_return_turn_setting` SET `return_lottery_percent`=`return_percent` WHERE `return_lottery_percent`=0;
UPDATE `promotion_return_turn_setting` SET `return_p2p_percent`=`return_percent` WHERE `return_p2p_percent`=0;
UPDATE `promotion_return_turn_setting` SET `return_financial_percent`=`return_percent` WHERE `return_financial_percent`=0;

ALTER TABLE `promotion_return_turn`
	ADD COLUMN `return_sport_percent` DECIMAL(10,2) NOT NULL DEFAULT '0.00' AFTER `total_turn_sport`,
	ADD COLUMN `return_casino_percent` DECIMAL(10,2) NOT NULL DEFAULT '0.00' AFTER `total_turn_casino`,
  ADD COLUMN `return_game_percent` DECIMAL(10,2) NOT NULL DEFAULT '0.00' AFTER `total_turn_game`,
  ADD COLUMN `return_lottery_percent` DECIMAL(10,2) NOT NULL DEFAULT '0.00' AFTER `total_turn_lottery`,
  ADD COLUMN `return_p2p_percent` DECIMAL(10,2) NOT NULL DEFAULT '0.00' AFTER `total_turn_p2p`,
  ADD COLUMN `return_financial_percent` DECIMAL(10,2) NOT NULL DEFAULT '0.00' AFTER `total_turn_financial`;

UPDATE `promotion_return_turn` SET `return_sport_percent`=`return_percent` WHERE `return_sport_percent`=0;
UPDATE `promotion_return_turn` SET `return_casino_percent`=`return_percent` WHERE `return_casino_percent`=0;
UPDATE `promotion_return_turn` SET `return_game_percent`=`return_percent` WHERE `return_game_percent`=0;
UPDATE `promotion_return_turn` SET `return_lottery_percent`=`return_percent` WHERE `return_lottery_percent`=0;
UPDATE `promotion_return_turn` SET `return_p2p_percent`=`return_percent` WHERE `return_p2p_percent`=0;
UPDATE `promotion_return_turn` SET `return_financial_percent`=`return_percent` WHERE `return_financial_percent`=0;

-- todo later Remove unused column return_percent
-- ALTER TABLE `promotion_return_turn` DROP COLUMN `return_percent`;

CREATE TABLE IF NOT EXISTS `gorm_setting`(
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `api_key` VARCHAR(255) NOT NULL,
  `last_call_at` DATETIME NULL DEFAULT NULL,
  `created_at` DATETIME DEFAULT NOW(),
  `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
  PRIMARY KEY (`id`)
);
-- set uuid for gorm_setting as api_key
INSERT INTO `gorm_setting` (`api_key`) VALUES (UUID());

-- SELECT `id`, `name`, `status`, `json_input`, `json_request`, `json_reponse`, `created_at` FROM `cybergame`.`agent_log` WHERE  `id`=7070;

-- SQL_MODE='';
-- SET sql_mode = '';
-- SELECT * FROM `agent_log` WHERE `name`='AgcRegister' AND created_at IN (
--   SELECT created_at FROM `agent_log` WHERE `name`='AgcRegister' group by created_at having count(*) > 1
-- );

ALTER TABLE `paygate_zmanpay_order`
	ADD COLUMN `extra_promptpay_id` VARCHAR(255) NULL DEFAULT NULL AFTER `payment_page_url`;

-- HOTFIX 20250515 TULA
ALTER TABLE `promotion_return_turn_setting`
	CHANGE COLUMN `return_sport_percent` `return_sport_percent` DECIMAL(10,2) NOT NULL DEFAULT (0) AFTER `calc_on_sport`,
	CHANGE COLUMN `return_casino_percent` `return_casino_percent` DECIMAL(10,2) NOT NULL DEFAULT (0) AFTER `calc_on_casino`,
	CHANGE COLUMN `return_game_percent` `return_game_percent` DECIMAL(10,2) NOT NULL DEFAULT (0) AFTER `calc_on_game`,
	CHANGE COLUMN `return_lottery_percent` `return_lottery_percent` DECIMAL(10,2) NOT NULL DEFAULT (0) AFTER `calc_on_lottery`,
	CHANGE COLUMN `return_p2p_percent` `return_p2p_percent` DECIMAL(10,2) NOT NULL DEFAULT (0) AFTER `calc_on_p2p`,
	CHANGE COLUMN `return_financial_percent` `return_financial_percent` DECIMAL(10,2) NOT NULL DEFAULT (0) AFTER `calc_on_financial`;

-- SELECT return_percent, return_sport_percent, return_casino_percent, return_game_percent, return_lottery_percent, return_p2p_percent, return_financial_percent FROM promotion_return_turn_setting;

-- UPDATE `promotion_return_turn_setting` SET `return_sport_percent`=`return_percent` WHERE `return_sport_percent` != `return_percent`
-- UPDATE `promotion_return_turn_setting` SET `return_casino_percent`=`return_percent` WHERE `return_casino_percent` != `return_percent`
-- UPDATE `promotion_return_turn_setting` SET `return_game_percent`=`return_percent` WHERE `return_game_percent` != `return_percent`
-- UPDATE `promotion_return_turn_setting` SET `return_lottery_percent`=`return_percent` WHERE `return_lottery_percent` != `return_percent`
-- UPDATE `promotion_return_turn_setting` SET `return_p2p_percent`=`return_percent` WHERE `return_p2p_percent` != `return_percent`
-- UPDATE `promotion_return_turn_setting` SET `return_financial_percent`=`return_percent` WHERE `return_financial_percent` != `return_percent`

-- SELECT return_percent, return_sport_percent, return_casino_percent, return_game_percent, return_lottery_percent, return_p2p_percent, return_financial_percent FROM promotion_return_turn_setting;

-- migrate 2025-05-23

INSERT INTO `activity_lucky_wheel` (`id`, `position`, `message`,`minimum_reward`, `hex_background_color`, `percent_win`) VALUES
	(9,9, '600  บาท', 600, '#ecb1b', '0.5'),
  (10,10, '700 บาท', 700, '#ecb1b', '0.30'),
  (11,11, '800 บาท', 800, '#ecb1b', '0.20'),
  (12,12, '900 บาท', 900, '#ecb1b', '0.00');

ALTER TABLE `activity_lucky_wheel`
  ADD COLUMN `font_color` VARCHAR(255) NOT NULL DEFAULT '#000000' AFTER `hex_background_color`;


CREATE TABLE IF NOT EXISTS `activity_lucky_wheel_setting_amount_spin` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(255) NOT NULL,
  `label_th` varchar(255) DEFAULT NULL,
  `label_en` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
);

INSERT INTO `activity_lucky_wheel_setting_amount_spin` (`id`, `name`, `label_th`, `label_en`) VALUES
(1, '6', '6 ช่อง', '6 slots'),
(2, '8', '8 ช่อง', '8 slots'),
(3, '10', '10 ช่อง', '10 slots'),
(4, '12', '12 ช่อง', '12 slots');


ALTER TABLE `activity_lucky_wheel_setting`
  ADD COLUMN `activity_lucky_wheel_setting_amount_spin_id` BIGINT NOT NULL DEFAULT 2 AFTER `cumulative_expired_days`;

CREATE TABLE IF NOT EXISTS `activity_lucky_wheel_setting_image_spin` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `activity_lucky_wheel_setting_amount_spin_id` BIGINT NOT NULL,
  `image_main_spin_url` VARCHAR(255) NOT NULL DEFAULT '',
  `image_arrow_spin_url` VARCHAR(255) NOT NULL DEFAULT '',
  `image_button_spin_url` VARCHAR(255) NOT NULL DEFAULT '',
  `created_at` DATETIME DEFAULT NOW(),
  `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
  PRIMARY KEY (`id`)
);

INSERT INTO `activity_lucky_wheel_setting_image_spin` (`id`, `activity_lucky_wheel_setting_amount_spin_id`) VALUES
(1, 1),
(2, 2),
(3, 3),
(4, 4);


ALTER TABLE `promotion_web_user` 
ADD COLUMN `total_deposit_amount` DECIMAL(10,2) DEFAULT 0 AFTER `promotion_web_user_status_id`;

ALTER TABLE `bank_account`
  ADD COLUMN `show_bank_deposit_over_due_time` INT NOT NULL DEFAULT 0 AFTER `image_url`;
  
-- sprint ********-23

ALTER TABLE `user_alliance`
	ADD COLUMN `sale_code` VARCHAR(255) NULL DEFAULT NULL AFTER `ref_code`,
	ADD COLUMN `sale_click_total` INT NOT NULL DEFAULT '0' AFTER `link_click_total`;

CREATE UNIQUE INDEX `idx_sale_code` ON `user_alliance` (`sale_code`);

CREATE TABLE IF NOT EXISTS `user_alliance_salepage_stat` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `user_id` BIGINT NOT NULL,
  `daily_key` VARCHAR(255) NOT NULL DEFAULT '',
  `link_click_count` INT NOT NULL DEFAULT '0',
  `member_register_count` INT NOT NULL DEFAULT '0',
  `admin_click_count` INT NOT NULL DEFAULT '0',
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
CREATE INDEX `idx_user_id` ON `user_alliance_salepage_stat` (`user_id`);
CREATE UNIQUE INDEX `idx_daily_key` ON `user_alliance_salepage_stat` (`daily_key`);

-- cybergame.user_salepage_block
CREATE  TABLE IF NOT EXISTS `user_master_salepage_block` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(255) NOT NULL DEFAULT '',
  `label` VARCHAR(255) NOT NULL DEFAULT '',
  `is_show` TINYINT NOT NULL DEFAULT '0',
  `sort_order` BIGINT NOT NULL DEFAULT '0',
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);

INSERT INTO `user_master_salepage_block` (`id`, `name`, `label`, `is_show`, `sort_order`) VALUES
(1, 'FACEBOOK_PIXEL', 'จัดการ facebook pixel', 0, 1),
(2, 'BACKGROUND_COLOR', 'จัดการสีพื้นหลัง', 1, 2),
(3, 'LOGO', 'จัดการโลโก้', 1, 3),
(4, 'TEXT_1', 'จัดการข้อความ (1)', 0, 4),
(5, 'TEXT_2', 'จัดการข้อความ (2)', 0, 5),
(6, 'TEXT_3', 'จัดการข้อความ (3)', 0, 6),
(7, 'BANNER_1', 'จัดการแบนเนอร์ (1)', 0, 7),
(8, 'BANNER_2', 'จัดการแบนเนอร์ (2)', 0, 8),
(9, 'BANNER_3', 'จัดการแบนเนอร์ (3)', 0, 9),
(10, 'BUTTON_REGISTER', 'จัดการปุ่มสมัครสมาชิก', 1, 10),
(11, 'BUTTON_CONTACT_US', 'จัดการปุ่มติดต่อเรา', 1, 11),
(12, 'LIST_GOOD_WITHDRAW', 'จัดการแสดงผลรายการถอนล่าสุด', 0, 12),
(13, 'FOOTER_BAR', 'จัดการ Footer', 1, 13);

CREATE  TABLE IF NOT EXISTS `user_salepage_block` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `user_id` BIGINT NOT NULL,
  
  `name` VARCHAR(255) NOT NULL DEFAULT '',
  `label` VARCHAR(255) NOT NULL DEFAULT '',
  `is_show` TINYINT NOT NULL DEFAULT '0',
  `sort_order` BIGINT NOT NULL DEFAULT '0',

  `value_name` VARCHAR(1000) NOT NULL DEFAULT '',
  `value_color` VARCHAR(1000) NOT NULL DEFAULT '',
  `value_image1` VARCHAR(1000) NOT NULL DEFAULT '',
  `value_url1` VARCHAR(1000) NOT NULL DEFAULT '',
  `value_image2` VARCHAR(1000) NOT NULL DEFAULT '',
  `value_url2` VARCHAR(1000) NOT NULL DEFAULT '',
  `value_image3` VARCHAR(1000) NOT NULL DEFAULT '',
  `value_url3` VARCHAR(1000) NOT NULL DEFAULT '',

  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
CREATE INDEX `idx_user_id` ON `user_salepage_block` (`user_id`);
CREATE INDEX `idx_sort_order` ON `user_salepage_block` (`sort_order`);

ALTER TABLE `report_summary_dashboard`
	ADD COLUMN `total_bonus_count` BIGINT NOT NULL DEFAULT '0' AFTER `total_bonus_price`,
	ADD COLUMN `total_credit_back_count` BIGINT NOT NULL DEFAULT '0' AFTER `total_credit_back_price`;

CREATE  TABLE IF NOT EXISTS `bank_pending_record` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `ref_key` VARCHAR(255) NULL,
  `user_id` BIGINT NULL,
  `name` VARCHAR(255) NOT NULL DEFAULT '',
  `record_type_id` BIGINT NOT NULL DEFAULT 0,
  `member_code` VARCHAR(255) NULL ,
  `user_fullname` VARCHAR(255) NULL,
  `user_phone` VARCHAR(255) NULL,
  `from_bank_code` VARCHAR(255) NULL,
  `from_account` VARCHAR(255) NULL,
  `to_bank_code` VARCHAR(255) NULL,
  `to_account` VARCHAR(255) NULL,
  `transfer_at` DATETIME NULL,
  `credit_amount` DECIMAL(10,2) NOT NULL DEFAULT '0.00',
  `admin_fullname` VARCHAR(255) NULL,
  `socket_at` DATETIME NULL,
  `update_status` VARCHAR(255) NULL,
  `update_admin_fullname` VARCHAR(255) NULL,
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
CREATE UNIQUE INDEX `idx_ref_key` ON `bank_pending_record` (`ref_key`);
CREATE INDEX `idx_socket_at` ON `bank_pending_record` (`socket_at`);

-- migrate แล้วเมื่อ 2025-05-23

ALTER TABLE `user_salepage_block`
	ADD COLUMN `block_key` VARCHAR(255) NULL DEFAULT NULL AFTER `sort_order`;

CREATE UNIQUE INDEX `uni_block_key` ON `user_salepage_block` ( `block_key`);

-- ทำเฉพาะเว็บที่จะย้ายไป Maze
-- UPDATE `paygate_account` SET `api_end_point`='https://api.maze01.com/api' WHERE  `api_end_point`='https://api.zapman.net/api';
-- UPDATE `paygate_merchant` SET `api_end_point`='https://api.maze01.com/api' WHERE  `api_end_point`='https://api.zapman.net/api';
-- ย้ายกลับ
-- UPDATE `paygate_account` SET `api_end_point`='https://api.zapman.net/api' WHERE  `api_end_point`='https://api.maze01.com/api';
-- UPDATE `paygate_merchant` SET `api_end_point`='https://api.zapman.net/api' WHERE  `api_end_point`='https://api.maze01.com/api';

-- migrate แล้วเมื่อ 2025-05-27
-- 2025-05-28 POSTMANPAY

CREATE TABLE IF NOT EXISTS `paygate_postmanpay_order` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT NOT NULL,
    `order_type_id` BIGINT NOT NULL,
    `ref_id` BIGINT NULL DEFAULT NULL,
    `order_no` VARCHAR(255) NOT NULL,
    `amount` DECIMAL(10,2) NOT NULL DEFAULT 0,
    `transfer_amount` DECIMAL(10,2) NOT NULL DEFAULT 0,
    `transaction_no` VARCHAR(255) NULL,
    `transaction_date` DATETIME NULL,
    `transaction_status` VARCHAR(255) NULL,
    `payment_at` DATETIME NULL DEFAULT NULL,
    `bank_transaction_id` BIGINT NULL DEFAULT NULL,
    `bank_transaction_status` VARCHAR(255) NULL DEFAULT 'PENDING',
    `qr_promptpay` VARCHAR(255) NULL,
    `payment_page_url` VARCHAR(255) NULL,
    `note` VARCHAR(255) NULL DEFAULT NULL,
    `remark` VARCHAR(1000) NULL DEFAULT NULL,
    `action_by` BIGINT NULL DEFAULT NULL,
    `api_remark` VARCHAR(255) NULL DEFAULT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
    PRIMARY KEY (`id`)
);
CREATE UNIQUE INDEX `uni_order_no` ON `paygate_postmanpay_order` (`order_no`);
CREATE UNIQUE INDEX `uni_transaction_no` ON `paygate_postmanpay_order` (`transaction_no`);
CREATE INDEX `idx_order_type_id` ON `paygate_postmanpay_order` (`order_type_id`);

CREATE TABLE IF NOT EXISTS `paygate_postmanpay_webhook` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL,
    `json_payload` LONGTEXT NOT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    PRIMARY KEY (`id`)
);

INSERT INTO `paygate_merchant` (`id`, `name`, `display_name`, `type_name`, `has_deposit`, `is_deposit_enabled`, `has_withdraw`, `is_withdraw_enabled`, `api_end_point`, `shop_name`)
  VALUES (14, 'POSTMANPAY', 'เติมเงินผ่าน QR Code' , 'POSTMANPAY', 1, 0, 1, 0, 'https://api.2stepx.com', 'POSTMAN Payment');

UPDATE `paygate_merchant` SET `payment_deposit_minimum`=100, `payment_deposit_maximum`=100000, `payment_withdraw_minimum`=100, `payment_withdraw_maximum`=100000 WHERE  `id`=14;

-- EOF POSTMANPAY
-- migrate แล้วเมื่อ 2025-06-02

-- 2025-06-04 MAZEPAY

CREATE TABLE IF NOT EXISTS `paygate_mazepay_order` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT NOT NULL,
    `order_type_id` BIGINT NOT NULL,
    `ref_id` BIGINT NULL DEFAULT NULL,
    `order_no` VARCHAR(255) NOT NULL,
    `amount` DECIMAL(10,2) NOT NULL DEFAULT 0,
    `transfer_amount` DECIMAL(10,2) NOT NULL DEFAULT 0,
    `transaction_no` VARCHAR(255) NULL,
    `transaction_date` DATETIME NULL,
    `transaction_status` VARCHAR(255) NULL,
    `payment_at` DATETIME NULL DEFAULT NULL,
    `bank_transaction_id` BIGINT NULL DEFAULT NULL,
    `bank_transaction_status` VARCHAR(255) NULL DEFAULT 'PENDING',
    `qr_promptpay` VARCHAR(255) NULL,
    `qr_base64` TEXT NULL,
    `extra_promptpay_id` VARCHAR(255) NULL,
    `payment_page_url` VARCHAR(255) NULL,
    `note` VARCHAR(255) NULL DEFAULT NULL,
    `remark` VARCHAR(1000) NULL DEFAULT NULL,
    `action_by` BIGINT NULL DEFAULT NULL,
    `api_remark` VARCHAR(255) NULL DEFAULT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
    PRIMARY KEY (`id`)
);
CREATE UNIQUE INDEX `uni_order_no` ON `paygate_mazepay_order` (`order_no`);
CREATE UNIQUE INDEX `uni_transaction_no` ON `paygate_mazepay_order` (`transaction_no`);
CREATE INDEX `idx_order_type_id` ON `paygate_mazepay_order` (`order_type_id`);


CREATE TABLE IF NOT EXISTS `paygate_mazepay_webhook` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL,
    `json_payload` LONGTEXT NOT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    PRIMARY KEY (`id`)
);

INSERT INTO `paygate_merchant` (`id`, `name`, `display_name`, `type_name`, `has_deposit`, `is_deposit_enabled`, `has_withdraw`, `is_withdraw_enabled`, `api_end_point`, `shop_name`)
  VALUES (15, 'MAZEPAY','เติมเงินผ่าน QR Code', 'MAZEPAY', 1, 0, 1, 0, 'https://api.maze01.com/api', 'Edit shop name here');

UPDATE `paygate_merchant` SET `payment_deposit_minimum`=100, `payment_deposit_maximum`=100000, `payment_withdraw_minimum`=100, `payment_withdraw_maximum`=100000 WHERE  `id`=15;

CREATE TABLE IF NOT EXISTS `paygate_mazepay_customer`(
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `user_id` BIGINT NOT NULL,
  `customer_uuid` VARCHAR(255) NOT NULL,
  `full_name` VARCHAR(255) NOT NULL,
  `bank_code` VARCHAR(255) NOT NULL,
  `account_no` VARCHAR(255) NOT NULL,
  `account_name` VARCHAR(255) NOT NULL,
  PRIMARY KEY (`id`)
);
CREATE UNIQUE INDEX `uni_user_id` ON `paygate_mazepay_customer` (`user_id`);

-- todo Migrate web used mazepay config from zmappay into mazepay

-- EOF MAZEPAY
-- 2025-06-04 MEEPAY

CREATE TABLE IF NOT EXISTS `paygate_meepay_order` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT NOT NULL,
    `order_type_id` BIGINT NOT NULL,
    `ref_id` BIGINT NULL DEFAULT NULL,
    `order_no` VARCHAR(255) NOT NULL,
    `amount` DECIMAL(10,2) NOT NULL DEFAULT 0,
    `transfer_amount` DECIMAL(10,2) NOT NULL DEFAULT 0,
    `transaction_no` VARCHAR(255) NULL,
    `transaction_date` DATETIME NULL,
    `transaction_status` VARCHAR(255) NULL,
    `payment_at` DATETIME NULL DEFAULT NULL,
    `bank_transaction_id` BIGINT NULL DEFAULT NULL,
    `bank_transaction_status` VARCHAR(255) NULL DEFAULT 'PENDING',
    `qr_promptpay` VARCHAR(255) NULL,
    `qr_base64` TEXT NULL,
    `extra_promptpay_id` VARCHAR(255) NULL,
    `payment_page_url` VARCHAR(255) NULL,
    `note` VARCHAR(255) NULL DEFAULT NULL,
    `remark` VARCHAR(1000) NULL DEFAULT NULL,
    `action_by` BIGINT NULL DEFAULT NULL,
    `api_remark` VARCHAR(255) NULL DEFAULT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
    PRIMARY KEY (`id`)
);
CREATE UNIQUE INDEX `uni_order_no` ON `paygate_meepay_order` (`order_no`);
CREATE UNIQUE INDEX `uni_transaction_no` ON `paygate_meepay_order` (`transaction_no`);
CREATE INDEX `idx_order_type_id` ON `paygate_meepay_order` (`order_type_id`);

CREATE TABLE IF NOT EXISTS `paygate_meepay_webhook` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL,
    `json_payload` LONGTEXT NOT NULL,
    `created_at` DATETIME DEFAULT NOW(),
    PRIMARY KEY (`id`)
);

INSERT INTO `paygate_merchant` (`id`, `name`, `display_name`, `type_name`, `has_deposit`, `is_deposit_enabled`, `has_withdraw`, `is_withdraw_enabled`, `api_end_point`, `shop_name`)
  VALUES (16, 'MEEPAY','เติมเงินผ่าน QR Code', 'MEEPAY', 1, 0, 1, 0, 'https://api.meepay.me/v1', 'Edit shop name here');

UPDATE `paygate_merchant` SET `payment_deposit_minimum`=100, `payment_deposit_maximum`=100000, `payment_withdraw_minimum`=100, `payment_withdraw_maximum`=100000 WHERE  `id`=16;

CREATE TABLE IF NOT EXISTS `paygate_meepay_customer`(
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `user_id` BIGINT NOT NULL,
  `customer_uuid` VARCHAR(255) NOT NULL,
  `full_name` VARCHAR(255) NOT NULL,
  `bank_code` VARCHAR(255) NOT NULL,
  `account_no` VARCHAR(255) NOT NULL,
  `account_name` VARCHAR(255) NOT NULL,
  PRIMARY KEY (`id`)
);
CREATE UNIQUE INDEX `uni_user_id` ON `paygate_meepay_customer` (`user_id`);

-- EOF MEEPAY

-- MA Delete log before 3 months
-- DELETE FROM `paygate_system_log` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 3 MONTH);
-- ANALYZE TABLE `paygate_system_log`;

CREATE TABLE IF NOT EXISTS `activity_menu` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(255) NOT NULL DEFAULT '',
  `description` VARCHAR(500) NOT NULL DEFAULT '',
  `image_url` VARCHAR(255) NOT NULL DEFAULT '',
  `label_th` VARCHAR(255) NOT NULL DEFAULT '',
  `label_en` VARCHAR(255) NOT NULL DEFAULT '',
  `sort_order` BIGINT NOT NULL DEFAULT '0',
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_by_id` BIGINT NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
);

INSERT INTO `activity_menu` (`id`, `name`, `description`, `image_url`, `label_th`, `label_en`, `sort_order`) VALUES
(1, 'คืนยอดเสีย', 'เข้าเล่น คืนยอดเสีย', '', 'คืนยอดเสีย', 'RETURN_LOSS', 1),
(2, 'Commission', 'เข้าเล่น Commission', '', 'Commission', 'RETURN_TURN', 2),
(3, 'กิจกรรมกงล้อนำโชค', 'เข้าเล่น กิจกรรมกงล้อนำโชค', '', 'กิจกรรมกงล้อนำโชค', 'GOOD_WHEEL', 3),
(4, 'เช็คอินรายวัน', 'เข้าเล่น เช็คอินรายวัน', '', 'เช็คอินรายวัน', 'CHECK_IN', 4),
(5, 'เช็คอินรายวัน V2', 'เข้าเล่น เช็คอินรายวัน V2', '', 'เช็คอินรายวัน V2', 'ACTIVITY_DAILY_V2', 5),
(6, 'คูปองเงินสด', 'เข้าเล่น คูปองเงินสด', '', 'คูปองเงินสด', 'COUPON_CASH', 6);

-- TidturnReturnTurnPercent
ALTER TABLE `turnover_setting`
	ADD COLUMN `tidturn_return_turn_percent` INT NOT NULL DEFAULT '100' AFTER `tidturn_return_loss_percent`;

UPDATE `turnover_setting` SET `tidturn_return_turn_percent` = `tidturn_return_loss_percent` WHERE `tidturn_return_loss_percent` != 100;

-- add new block TOTAL_USER_ONLINE and TOTAL_JACKPOT
INSERT INTO `user_master_salepage_block` (`id`, `name`, `label`, `is_show`, `sort_order`) VALUES
(14, 'TOTAL_USER_ONLINE', 'จำนวนผู้ใช้งานออนไลน์', 0, 14),
(15, 'TOTAL_JACKPOT', 'จำนวนแจ็คพอต', 0, 15);

-- migrate แล้วเมื่อ 2025-06-09

ALTER TABLE `activity_menu`
  ADD COLUMN `lang` VARCHAR(10) NOT NULL DEFAULT 'th' AFTER `sort_order`;

INSERT INTO `activity_menu` (`id`, `name`, `description`, `image_url`, `label_th`, `label_en`,`sort_order`, `lang`) VALUES
(7, '', '', '', 'คืนยอดเสีย', 'RETURN_LOSS', 7, 'en'),
(8, '', '', '', 'Commission', 'RETURN_TURN', 8, 'en'),
(9, '', '', '', 'กิจกรรมกงล้อนำโชค', 'GOOD_WHEEL', 9, 'en'),
(10, '', '', '', 'เช็คอินรายวัน', 'CHECK_IN', 10, 'en'),
(11, '', '', '', 'เช็คอินรายวัน V2', 'ACTIVITY_DAILY_V2', 11, 'en'),
(12, '', '', '', 'คูปองเงินสด', 'COUPON_CASH', 12, 'en'),
(13, '', '', '', 'คืนยอดเสีย', 'RETURN_LOSS', 13, 'mm'),
(14, '', '', '', 'Commission', 'RETURN_TURN', 14, 'mm'),
(15, '', '', '', 'กิจกรรมกงล้อนำโชค', 'GOOD_WHEEL', 15, 'mm'),
(16, '', '', '', 'เช็คอินรายวัน', 'CHECK_IN', 16, 'mm'),
(17, '', '', '', 'เช็คอินรายวัน V2', 'ACTIVITY_DAILY_V2', 17, 'mm'),
(18, '', '', '', 'คูปองเงินสด', 'COUPON_CASH', 18, 'mm'),
(19, '', '', '', 'คืนยอดเสีย', 'RETURN_LOSS', 19, 'la'),
(20, '', '', '', 'Commission', 'RETURN_TURN', 20, 'la'),
(21, '', '', '', 'กิจกรรมกงล้อนำโชค', 'GOOD_WHEEL', 21, 'la'),
(22, '', '', '', 'เช็คอินรายวัน', 'CHECK_IN', 22, 'la'),
(23, '', '', '', 'เช็คอินรายวัน V2', 'ACTIVITY_DAILY_V2', 23, 'la'),
(24, '', '', '', 'คูปองเงินสด', 'COUPON_CASH', 24, 'la'),
(25, '', '', '', 'คืนยอดเสีย', 'RETURN_LOSS', 25, 'cn'),
(26, '', '', '', 'Commission', 'RETURN_TURN', 26, 'cn'),
(27, '', '', '', 'กิจกรรมกงล้อนำโชค', 'GOOD_WHEEL', 27, 'cn'),
(28, '', '', '', 'เช็คอินรายวัน', 'CHECK_IN', 28, 'cn'),
(29, '', '', '', 'เช็คอินรายวัน V2', 'ACTIVITY_DAILY_V2', 29, 'cn'),
(30, '', '', '', 'คูปองเงินสด', 'COUPON_CASH', 30, 'cn');

INSERT INTO `permission` (`id`, `permission_key`, `main`, `name`) VALUES (104, 'admin_dashboard', 1, 'แดชบอร์ดแอดมิน');

-- 2025-06-13 ปรับราคา renewal_fastbank_package
-- | ราคา (บาท) | จำนวนเครดิตที่ให้ (เดิม) | เครดิตต่อบาท | หลังปรับ (เครดิต) |
-- | ---------- | ------------------------ | ------------ | ----------------- |
-- | 2500       | 10000                     | 2.8          | 7000              |
-- | 5000       | 20000                    | 3            | 15000             |
-- | 10000      | 41000                    | 3.3          | 33000             |
-- | 30000      | 152000                   | 3.77         | 113000            |
-- | 50000      | 260000                   | 4            | 200000            |

-- SELECT * FROM `renewal_fastbank_package` LIMIT 1000;
UPDATE `renewal_fastbank_package` SET `renewal_credits` = 7000 WHERE `price` = 2500;
UPDATE `renewal_fastbank_package` SET `renewal_credits` = 15000 WHERE `price` = 5000;
UPDATE `renewal_fastbank_package` SET `renewal_credits` = 33000 WHERE `price` = 10000;
UPDATE `renewal_fastbank_package` SET `renewal_credits` = 113000 WHERE `price` = 30000;
UPDATE `renewal_fastbank_package` SET `renewal_credits` = 200000 WHERE `price` = 50000;
-- SELECT * FROM `renewal_fastbank_package` LIMIT 1000;

-- migrate แล้วเมื่อ 2025-06-13

ALTER TABLE `bank_account`
  ADD COLUMN `show_bank_deposit_max_due_time` INT NOT NULL DEFAULT 100 AFTER `show_bank_deposit_over_due_time`;

  INSERT INTO `agent_game_priority_setting` (`id`, `agent`, `vendor_code`, `priority_order`, `detail`, `category_name`, `is_popular`, `is_show`, `image_name`) VALUES
(112, 'AGC', 'GXW', 112, 'GX Wicket', 'SPORT', 0, 0, 'img-gxw'),
(113, 'AGC', 'RGS', 113, 'Galaxsys', 'SLOT', 0, 0, 'img-rgs'),
(114, 'AGC', 'BF', 114, 'BF-Sport', 'SPORT', 0, 0, 'img-bf-sport'),
(115, 'AGC', 'BESOFT', 115, 'Besoft Gaming', 'SLOT', 0, 0, 'img-besoft-game'),
(116, 'AGC', 'PLAYTECHC', 116, 'PlayTech Live', 'CASINO', 0, 0, 'img-play-techc');

CREATE TABLE IF NOT EXISTS `category_game_setting_v2` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `priority_order` INT NOT NULL DEFAULT '0',
  `name` VARCHAR(255) NOT NULL DEFAULT '',
  `label_th` VARCHAR(255) NOT NULL DEFAULT '',
  `label_en` VARCHAR(255) NOT NULL DEFAULT '',
  `is_active` TINYINT NOT NULL DEFAULT '1',
  `api_is_allowed` TINYINT NOT NULL DEFAULT '0',
  `created_at` DATETIME DEFAULT NOW(),
  `created_by_id` BIGINT DEFAULT NULL,
  `updated_at` DATETIME NULL DEFAULT NULL ON UPDATE NOW(),
  `updated_by_id` BIGINT NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
);

INSERT INTO `category_game_setting_v2` (`id`, `priority_order`, `name`, `label_th`, `label_en`, `is_active`, `api_is_allowed`) VALUES
(1, 1, 'SLOT', 'สล็อต', 'SLOT', 1, 1),
(2, 2, 'CASINO', 'คาสิโน', 'CASINO', 1, 1),
(3, 3, 'SPORT', 'กีฬา', 'SPORT', 1, 1),
(4, 4, 'LOTTERY', 'หวย', 'LOTTERY', 1, 1),
(5, 5, 'P2P', 'P2P', 'P2P', 1, 1),
(6, 6, 'POPULAR', 'ยอดนิยม', 'POPULAR', 1, 1),
(7, 7, 'CBLOTTO', 'CB Lotto', 'CB Lotto', 0, 0);

-- migrate แล้วเมื่อ 2025-06-19
-- ข้างล่างนี้ PROD ยังไม่ได้ Migrate
