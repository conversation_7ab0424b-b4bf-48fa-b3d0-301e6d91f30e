
-- 2024-04-xx migration move to 3 later

-- CREATE TABLE IF NOT EXISTS `table_name` (
--     `id` BIGINT NOT NULL AUTO_INCREMENT,
--     `name` VARCHAR(255) NOT NULL,
--     `admin_id` BIGINT NOT NULL,
--     `json_req` TEXT NOT NULL,
--     `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
--     PRIMARY KEY (`id`)
-- );

-- ALTER TABLE `bank_account`
-- 	ADD COLUMN `sms_mode` TINYINT(3) NOT NULL DEFAULT '0' AFTER `admin_updated_at`;

-- INSERT INTO `turnover_statement_type` (`id`, `name`) VALUES 
-- (8,'TURN_BONUS_BY_ADMIN');

-- UPDATE `turnover_statement_type` SET `name` =  'TURN_PROMOTION_SETTING_PLAY_ALL' WHERE `id` = 3;
-- UPDATE `turnover_statement_type` SET `name` =  'TURN_PROMOTION_SETTING_PLAY_GAME' WHERE `id` = 4;
-- UPDATE `turnover_statement_type` SET `name` =  'TURN_PROMOTION_SETTING_PLAY_SPORT' WHERE `id` = 5;
-- UPDATE `turnover_statement_type` SET `name` =  'TURN_PROMOTION_SETTING_PLAY_CASINO' WHERE `id` = 6;

-- ALTER TABLE `turnover_statement`
-- 	ADD COLUMN `name` VARCHAR(255) NULL DEFAULT NULL AFTER `ref_type_id`;

-- 	INSERT INTO `turnover_statement_type` (`id`, `name`) VALUES 
-- (9,'TURN_BONUS_AFF_TYPE_NEW_REGISTER');

-- 	INSERT INTO `turnover_statement_type` (`id`, `name`) VALUES 
-- (10,'TURN_BONUS_AFF_TYPE_FIRST_DEPOSIT ');
-- 	INSERT INTO `turnover_statement_type` (`id`, `name`) VALUES 
-- (11,'TURN_BONUS_AFF_TYPE_PLAY_COMMISSION ');


-- CREATE TABLE IF NOT EXISTS `order_category` (
-- 	`id` BIGINT NOT NULL AUTO_INCREMENT,
-- 	`name` VARCHAR(255) NOT NULL,
-- 	`created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
-- 	`updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
-- 	`deleted_at` DATETIME DEFAULT NULL,
-- 	PRIMARY KEY (`id`)
-- );
-- INSERT INTO `order_category` (`id`, `name`) VALUES 
-- (1,'LOTTERY');

-- CREATE TABLE IF NOT EXISTS `order_type` (
-- 	`id` BIGINT NOT NULL AUTO_INCREMENT,
-- 	`name` VARCHAR(255) NOT NULL,
-- 	`label_th` VARCHAR(255) NOT NULL,
-- 	`label_en` VARCHAR(255) NOT NULL,
-- 	`created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
-- 	`updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
-- 	`deleted_at` DATETIME DEFAULT NULL,
-- 	PRIMARY KEY (`id`)
-- );

-- INSERT INTO `order_type` (`id`, `name`, `label_th`, `label_en`) VALUES
-- (1,'LOTTERY_BET','แทงหวย','LOTTERY_BET'),
-- (2,'LOTTERY_WIN','ถูกรางวัล','LOTTERY_WIN'),
-- (3,'LOTTERY_REFUND','คืนเงิน','LOTTERY_REFUND'),
-- (4,'LOTTERY_COMMISSION','คอมมิชชั่น','LOTTERY_COMMISSION');

-- CREATE TABLE IF NOT EXISTS `order` (
--   `id` BIGINT NOT NULL AUTO_INCREMENT,
--   `order_category_id` BIGINT NOT NULL,
--   `order_type_id` BIGINT NOT NULL,
--   `ref1_no` VARCHAR(255) NOT NULL COMMENT 'external unique with order_type_id',
--   `ref2_no` VARCHAR(255) NOT NULL COMMENT 'internal',
--   `user_id` BIGINT NOT NULL,
--   `amount` DECIMAL(10,2) NOT NULL,
--   `order_status_id` BIGINT NOT NULL,
--   `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
--   `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
--   `cancelled_at` DATETIME DEFAULT NULL,
--   `deleted_at` DATETIME DEFAULT NULL,
--   PRIMARY KEY (`id`),
--   UNIQUE KEY `ref1_no_order_unique` (`ref1_no`, `order_type_id`, `order_category_id`),
--   UNIQUE KEY `ref2_no_unique` (`ref2_no`),
--   KEY `order_user_id_index` (`user_id`),
--   KEY `order_transaction_id_index` (`order_type_id`)
-- );

-- CREATE TABLE IF NOT EXISTS `order_race_condition` (
--   `id` BIGINT NOT NULL AUTO_INCREMENT,
--   `action_key` VARCHAR(255) NOT NULL,
--   `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
--   `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
--   `deleted_at` DATETIME DEFAULT NULL,
--   PRIMARY KEY (`id`),
--   UNIQUE KEY `action_key_unique` (`action_key`)
-- );

-- INSERT INTO `user_transaction_type` (`id`, `name`) VALUES (12, 'CREDIT_TYPE_LOTTERY');

-- CREATE TABLE IF NOT EXISTS `order_status` (
--   `id` BIGINT NOT NULL AUTO_INCREMENT,
--   `name` VARCHAR(255) NOT NULL,
--   `label_th` VARCHAR(255) NOT NULL,
--   `label_en` VARCHAR(255) NOT NULL,
--   `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
--   `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
--   `deleted_at` DATETIME DEFAULT NULL,
--   PRIMARY KEY (`id`)
-- );

-- INSERT INTO `order_status` (`id`, `name`, `label_th`, `label_en`) VALUES
-- (1,'PENDING','รอดำเนินการ','PENDING'),
-- (2,'SUCCESS','สำเร็จ','SUCCESS'),
-- (3,'FAILED','ไม่สำเร็จ','FAILED');

-- CREATE TABLE IF NOT EXISTS  `order_log` (
--   `id` BIGINT NOT NULL AUTO_INCREMENT,
--   `order_id` BIGINT DEFAULT NULL,
--   `json_req` TEXT DEFAULT NULL,
--   `remark` VARCHAR(255) DEFAULT NULL,
--   `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
--   PRIMARY KEY (`id`)
-- );


-- CREATE TABLE IF NOT EXISTS `exchange_currency` (
--   `id` BIGINT NOT NULL AUTO_INCREMENT,
--   `name_th` VARCHAR(255) NOT NULL,
--   `name_en` VARCHAR(255) NOT NULL,
--   `currency_code` VARCHAR(3) NOT NULL,
--   `currency_name_th` VARCHAR(255) NOT NULL,
--   `currency_name_en` VARCHAR(255) NOT NULL,
--   `image_url` VARCHAR(255) NOT NULL,
--   `is_main` TINYINT DEFAULT 0,
--   PRIMARY KEY (`id`),
--   UNIQUE KEY `currency_code_unique` (`currency_code`)
-- );

-- INSERT INTO `exchange_currency` (`id`, `name_th`, `name_en`, `currency_code`, `currency_name_th`, `currency_name_en`, `image_url`, `is_main`) VALUES
-- (1,'ไทย','Thailand','THB','บาท','Baht','https://www.countryflags.com/wp-content/uploads/thailand-flag-png-large.png',1),
-- (2,'ลาว','Laos','LAK','กีบ','Kip','https://www.countryflags.com/wp-content/uploads/laos-flag-png-large.png',0),
-- (3,'เมียนมา','Myanmar','MMK','เจา','Kyat','https://www.countryflags.com/wp-content/uploads/myanmar-flag-png-large.png',0);

-- CREATE TABLE IF NOT EXISTS `exchange_rate` (
--   `id` BIGINT NOT NULL AUTO_INCREMENT,
--   `exchange_currency_id` BIGINT NOT NULL,
--   `exchange_rate` DECIMAL(10,5) NOT NULL, 
--   `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
--   `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
--   `deleted_at` DATETIME DEFAULT NULL,
--   PRIMARY KEY (`id`),
--   UNIQUE KEY `exchange_currency_id_unique` (`exchange_currency_id`),
--   KEY `exchange_currency_id_index` (`exchange_currency_id`)
-- );

-- INSERT INTO `exchange_rate` (`id`, `exchange_currency_id`, `exchange_rate`) VALUES
-- (1,1,1),
-- (2,2,580.01465);



-- ALTER TABLE `configuration_web` ADD COLUMN `use_th_currency` TINYINT(1) NOT NULL DEFAULT '1' AFTER `use_upload_deposit_slip`;
-- ALTER TABLE `configuration_web` ADD COLUMN `use_laos_currency` TINYINT(1) NOT NULL DEFAULT '0' AFTER `use_th_currency`;

-- ALTER TABLE `bank` ADD COLUMN `country_code` VARCHAR(50) NULL DEFAULT NULL AFTER `type_flag`; 
-- UPDATE `bank` SET `country_code` = 'TH' ;

-- ALTER TABLE `bank` ADD COLUMN `use_currency` VARCHAR(50) NULL DEFAULT NULL AFTER `country_code`; 

-- UPDATE `bank` SET `use_currency` = 'THB' ;


-- INSERT INTO `bank` (`id`, `name`, `code`, `icon_url`, `type_flag`,`country_code`,`use_currency`) VALUES
-- (50, 'ธนาคารการค้าต่างประเทศลาว (LAK)', 'BCEL', 'https://storage.googleapis.com/cbgame/banks/none.png', '********','LAOS','LAK'),
-- (51, 'ธนาคารการค้าต่างประเทศลาว (THB)', 'BCELTH', 'https://storage.googleapis.com/cbgame/banks/none.png', '********','LAOS','THB'),
-- (52, 'ธนาคารพัฒนาลาว', 'LDB', 'https://storage.googleapis.com/cbgame/banks/none.png', '********','LAOS','LAK'),
-- (53, 'ธนาคารส่งเสริมการเกษตร', 'APB', 'https://storage.googleapis.com/cbgame/banks/none.png', '********','LAOS','LAK'),
-- (54, 'ธนาคารร่วมธุรกิจลาว', 'BOL', 'https://storage.googleapis.com/cbgame/banks/none.png', '********','LAOS','LAK'),
-- (55, 'ธนาคารร่วมพัฒนา', 'LDBBLALA XXX', 'https://storage.googleapis.com/cbgame/banks/none.png', '********','LAOS','LAK'),
-- (56, 'ธนาคาร ST', 'ST', 'https://storage.googleapis.com/cbgame/banks/none.png', '********','LAOS','LAK'),
-- (57, 'ธนาคาร BIC', 'BIC', 'https://storage.googleapis.com/cbgame/banks/none.png', '********','LAOS','LAK'),
-- (58, 'ธนาคาร Maruhan Japan', 'Maruhan Japan', 'https://storage.googleapis.com/cbgame/banks/none.png', '********','LAOS','LAK'),
-- (59, 'ธนาคาร Sacombank', 'Sacombank', 'https://storage.googleapis.com/cbgame/banks/none.png', '********','LAOS','LAK'),
-- (60, 'ธนาคารแห่งประเทศจีน', 'BKCHTHBK', 'https://storage.googleapis.com/cbgame/banks/none.png', '********','LAOS','LAK'),
-- (61, 'ธนาคาร Vietin', 'Vietin', 'https://storage.googleapis.com/cbgame/banks/none.png', '********','LAOS','LAK'),
-- (62, 'ธนาคาร ACLEDA', 'ACLEDA', 'https://storage.googleapis.com/cbgame/banks/none.png', '********','LAOS','LAK');


-- ALTER TABLE `bank_transaction`
-- 	ADD COLUMN `currency_amount` DECIMAL(14,2) NULL DEFAULT '0.00' AFTER `credit_amount`;

-- ALL DB list in 2024-08-06
-- db_4bar88
-- db_9goldclub
-- db_a4u_top
-- db_allin98
-- db_ambet65
-- db_atmplus
-- db_auto_game
-- db_autoslot168
-- db_baza888
-- db_bbmslot
-- db_brb88bet
-- db_brobet
-- db_cnone_bet
-- db_eleven111
-- db_ezrich
-- db_fivedragon
-- db_gm1_plus
-- db_kaya68
-- db_luxxplay
-- db_maga89_net
-- db_megaclub
-- db_mixxway
-- db_mr89
-- db_paplern999
-- db_pgautoall
-- db_rabitrich
-- db_river_club
-- db_rogerclub
-- db_royal447
-- db_s7_luck
-- db_showpow777
-- db_therich333
-- db_topgame789
-- db_topsure168
-- db_uca999
-- db_uk888club
-- db_utaslot
-- db_warrior88
-- db_wowgame88

-- select count from all db as one query
-- SELECT (SELECT COUNT(*) FROM `db_4bar88`.`affiliate_transaction` WHERE income_amount < 0) AS db_4bar88,
-- (SELECT COUNT(*) FROM `db_9goldclub`.`affiliate_transaction` WHERE income_amount < 0) AS db_9goldclub,
-- (SELECT COUNT(*) FROM `db_a4u_top`.`affiliate_transaction` WHERE income_amount < 0) AS db_a4u_top,
-- (SELECT COUNT(*) FROM `db_allin98`.`affiliate_transaction` WHERE income_amount < 0) AS db_allin98,
-- (SELECT COUNT(*) FROM `db_ambet65`.`affiliate_transaction` WHERE income_amount < 0) AS db_ambet65,
-- (SELECT COUNT(*) FROM `db_atmplus`.`affiliate_transaction` WHERE income_amount < 0) AS db_atmplus,
-- (SELECT COUNT(*) FROM `db_auto_game`.`affiliate_transaction` WHERE income_amount < 0) AS db_auto_game,
-- (SELECT COUNT(*) FROM `db_autoslot168`.`affiliate_transaction` WHERE income_amount < 0) AS db_autoslot168,
-- (SELECT COUNT(*) FROM `db_baza888`.`affiliate_transaction` WHERE income_amount < 0) AS db_baza888,
-- (SELECT COUNT(*) FROM `db_bbmslot`.`affiliate_transaction` WHERE income_amount < 0) AS db_bbmslot,
-- (SELECT COUNT(*) FROM `db_brb88bet`.`affiliate_transaction` WHERE income_amount < 0) AS db_brb88bet,
-- (SELECT COUNT(*) FROM `db_brobet`.`affiliate_transaction` WHERE income_amount < 0) AS db_brobet,
-- (SELECT COUNT(*) FROM `db_cnone_bet`.`affiliate_transaction` WHERE income_amount < 0) AS db_cnone_bet,
-- (SELECT COUNT(*) FROM `db_eleven111`.`affiliate_transaction` WHERE income_amount < 0) AS db_eleven111,
-- (SELECT COUNT(*) FROM `db_ezrich`.`affiliate_transaction` WHERE income_amount < 0) AS db_ezrich,
-- (SELECT COUNT(*) FROM `db_fivedragon`.`affiliate_transaction` WHERE income_amount < 0) AS db_fivedragon,
-- (SELECT COUNT(*) FROM `db_gm1_plus`.`affiliate_transaction` WHERE income_amount < 0) AS db_gm1_plus,
-- (SELECT COUNT(*) FROM `db_kaya68`.`affiliate_transaction` WHERE income_amount < 0) AS db_kaya68,
-- (SELECT COUNT(*) FROM `db_luxxplay`.`affiliate_transaction` WHERE income_amount < 0) AS db_luxxplay,
-- (SELECT COUNT(*) FROM `db_maga89_net`.`affiliate_transaction` WHERE income_amount < 0) AS db_maga89_net,
-- (SELECT COUNT(*) FROM `db_megaclub`.`affiliate_transaction` WHERE income_amount < 0) AS db_megaclub,
-- (SELECT COUNT(*) FROM `db_mixxway`.`affiliate_transaction` WHERE income_amount < 0) AS db_mixxway,
-- (SELECT COUNT(*) FROM `db_mr89`.`affiliate_transaction` WHERE income_amount < 0) AS db_mr89,
-- (SELECT COUNT(*) FROM `db_paplern999`.`affiliate_transaction` WHERE income_amount < 0) AS db_paplern999,
-- (SELECT COUNT(*) FROM `db_pgautoall`.`affiliate_transaction` WHERE income_amount < 0) AS db_pgautoall,
-- (SELECT COUNT(*) FROM `db_rabbitrich`.`affiliate_transaction` WHERE income_amount < 0) AS db_rabitrich,
-- (SELECT COUNT(*) FROM `db_river_club`.`affiliate_transaction` WHERE income_amount < 0) AS db_river_club,
-- (SELECT COUNT(*) FROM `db_rogerclub`.`affiliate_transaction` WHERE income_amount < 0) AS db_rogerclub,
-- (SELECT COUNT(*) FROM `db_royal447`.`affiliate_transaction` WHERE income_amount < 0) AS db_royal447,
-- (SELECT COUNT(*) FROM `db_s7_luck`.`affiliate_transaction` WHERE income_amount < 0) AS db_s7_luck,
-- (SELECT COUNT(*) FROM `db_showpow777`.`affiliate_transaction` WHERE income_amount < 0) AS db_showpow777,
-- (SELECT COUNT(*) FROM `db_therich333`.`affiliate_transaction` WHERE income_amount < 0) AS db_therich333,
-- (SELECT COUNT(*) FROM `db_topgame789`.`affiliate_transaction` WHERE income_amount < 0) AS db_topgame789,
-- (SELECT COUNT(*) FROM `db_topsure168`.`affiliate_transaction` WHERE income_amount < 0) AS db_topsure168,
-- (SELECT COUNT(*) FROM `db_uca999`.`affiliate_transaction` WHERE income_amount < 0) AS db_uca999,
-- (SELECT COUNT(*) FROM `db_uk888club`.`affiliate_transaction` WHERE income_amount < 0) AS db_uk888club,
-- (SELECT COUNT(*) FROM `db_utaslot`.`affiliate_transaction` WHERE income_amount < 0) AS db_utaslot,
-- (SELECT COUNT(*) FROM `db_warior88`.`affiliate_transaction` WHERE income_amount < 0) AS db_warrior88,
-- (SELECT COUNT(*) FROM `db_wowgame88`.`affiliate_transaction` WHERE income_amount < 0) AS db_wowgame88;


-- INSERT INTO `affiliate_transaction` (`user_id`, `daily_key`, `downline_id`, `income_amount`, `type_id`, `status_id`) VALUES 
-- (5002, 'test1', 6156, 1.01, 1, 1),
-- (5002, 'test2', 6156, 2.02, 1, 1),
-- (5002, 'test2', 6156, 2.03, 1, 1),
-- (5002, 'test2', 6156, 2.04, 1, 1),
-- (5002, 'test2', 6156, 2.05, 1, 1),
-- (5002, 'test2', 6156, 3.01, 1, 1);

-- UPDATE `user_affiliate` SET `commission_total`=12.16, `commission_current`=12.16, `total_withdraw`=0 WHERE  `user_id`=5002;

-- SELECT tb_user.id, tb_user.ref_by, tb_ref.user_type_id, tb_ref.id FROM user tb_user
-- JOIN user tb_ref ON tb_user.ref_by = tb_ref.id
-- WHERE tb_user.ref_by IS NOT NULL AND tb_user.ref_by != 0;


-- MA delete old row that create before 3 months
-- COUNT BEFORE from these 3 tables as one query

SELECT (SELECT COUNT(*) FROM user) AS user_count,
(SELECT COUNT(*) FROM affiliate_link_click) AS affiliate_link_click,
(SELECT COUNT(*) FROM affiliate_link_click WHERE created_at < '2024-01-01') AS affiliate_link_click2;

DELETE FROM `affiliate_link_click` WHERE `created_at` < '2024-01-01';

-- 3 months ago
SELECT (SELECT COUNT(*) FROM `webhook_log`) AS webhook_log,
(SELECT COUNT(*) FROM `webhook_log` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 3 MONTH)) AS webhook_log2,
(SELECT COUNT(*) FROM `cron_action`) AS cron_action,
(SELECT COUNT(*) FROM `cron_action` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 3 MONTH)) AS cron_action2,
(SELECT COUNT(*) FROM `race_action`) AS race_action,
(SELECT COUNT(*) FROM `race_action` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 3 MONTH)) AS race_action2;

DELETE FROM `webhook_log` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 3 MONTH);
DELETE FROM `cron_action` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 3 MONTH);
DELETE FROM `race_action` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 3 MONTH);

-- 15 days ago
SELECT (SELECT COUNT(*) FROM `agent_log`) AS agent_log,
(SELECT COUNT(*) FROM `agent_log` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 15 DAY)) AS agent_log2,
(SELECT COUNT(*) FROM `webhook_log`) AS webhook_log,
(SELECT COUNT(*) FROM `webhook_log` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 15 DAY)) AS webhook_log2,
(SELECT COUNT(*) FROM `cron_action`) AS cron_action,
(SELECT COUNT(*) FROM `cron_action` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 15 DAY)) AS cron_action2,
(SELECT COUNT(*) FROM `race_action`) AS race_action,
(SELECT COUNT(*) FROM `race_action` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 15 DAY)) AS race_action2;

DELETE FROM `agent_log` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 15 DAY);
DELETE FROM `webhook_log` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 15 DAY);
DELETE FROM `cron_action` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 15 DAY);
DELETE FROM `race_action` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 15 DAY);

ANALYZE TABLE `webhook_log`;
ANALYZE TABLE `cron_action`;
ANALYZE TABLE `race_action`;

SELECT TABLES.TABLE_SCHEMA, TABLES.TABLE_NAME, (TABLES.DATA_LENGTH + TABLES.INDEX_LENGTH) / 1024 / 1024 AS "MB used (estimate)"
, TABLES.DATA_FREE / 1024 / 1024 AS "MB allocated but unused (estimate)"
, INNODB_TABLESPACES.FILE_SIZE / 1024 / 1024 AS "MB on disk"
 FROM information_schema.TABLES JOIN information_schema.INNODB_TABLESPACES ON (INNODB_TABLESPACES.NAME = TABLES.TABLE_SCHEMA || '/' || TABLES.TABLE_NAME) 
 WHERE INNODB_TABLESPACES.FILE_SIZE > 10 * 1024 * 1024 
 AND  TABLES.TABLE_SCHEMA = 'cybergame'
 ORDER BY TABLES.DATA_FREE DESC;
 
OPTIMIZE TABLE `db_autoslot168`.`webhook_log`;
ANALYZE TABLE `db_autoslot168`.`webhook_log`;

-- Payment gateway log = 3 months
-- paygate_admin_log
-- paygate_heng_order
-- paygate_heng_webhook
-- 

SELECT (SELECT COUNT(*) FROM `paygate_admin_log`) AS paygate_admin_log,
(SELECT COUNT(*) FROM `paygate_admin_log` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 3 MONTH)) AS paygate_admin_log2;




-- paygate_heng_token = 15 days
SELECT (SELECT COUNT(*) FROM `paygate_heng_token`) AS paygate_heng_token,
(SELECT COUNT(*) FROM `paygate_heng_token` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 3 MONTH)) AS paygate_heng_token2;

DELETE FROM `paygate_heng_token` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 3 MONTH);
ANALYZE TABLE `paygate_heng_token`;


-- SELECT * FROM `INFORMATION_SCHEMA`.`TABLES` WHERE TABLE_NAME = 'play_log'

-- SELECT TABLE_SCHEMA,COUNT(*) FROM information_schema.columns WHERE table_name = 'play_log' GROUP BY TABLE_SCHEMA


-- Check api status db cybergame 01

SELECT 'db_4bar88' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_4bar88.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_9goldclub' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_9goldclub.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_a4u_top' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_a4u_top.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_allin98' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_allin98.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_ambet65' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_ambet65.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_atmplus' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_atmplus.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_auto_game' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_auto_game.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_autoslot168' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_autoslot168.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_baza888' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_baza888.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_bbmslot' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_bbmslot.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_brb88bet' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_brb88bet.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_brobet' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_brobet.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_cnone_bet' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_cnone_bet.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_eleven111' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_eleven111.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_ezrich' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_ezrich.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_fivedragon' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_fivedragon.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_gm1_plus' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_gm1_plus.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_kaya68' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_kaya68.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_luxxplay' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_luxxplay.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_maga89_net' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_maga89_net.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_megaclub' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_megaclub.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_mixxway' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_mixxway.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_mr89' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_mr89.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_paplern999' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_paplern999.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_pgautoall' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_pgautoall.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_rabbitrich' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_rabbitrich.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_river_club' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_river_club.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_rogerclub' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_rogerclub.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_royal447' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_royal447.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_s7_luck' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_s7_luck.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_showpow777' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_showpow777.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_therich333' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_therich333.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_topgame789' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_topgame789.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_topsure168' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_topsure168.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_uca999' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_uca999.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_uk888club' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_uk888club.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_utaslot' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_utaslot.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_warior88' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_warior88.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_wowgame88' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_wowgame88.api_status 
WHERE statement_date = '2024-09-13';


-- Check api status db  outsource

SELECT 'db_allforwin' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_allforwin.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_demo' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_demo.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_grandcasino' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_grandcasino.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_munmax' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_munmax.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_one1' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_one1.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_sagame1991' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_sagame1991.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_sbo88thailand' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_sbo88thailand.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_sexypg888' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_sexypg888.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_sexypgth' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_sexypgth.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_solo78' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_solo78.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'db_solopromax' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM db_solopromax.api_status 
WHERE statement_date = '2024-09-13'
UNION ALL
SELECT 'demo1' AS database_name, id , is_failed, is_success, CONVERT(statement_date USING utf8) AS statement_date
FROM demo1.api_status 
WHERE statement_date = '2024-09-13';


-- AFF 3 STEP

-- AUTO CALC 
-- SELECT tb_log.user_id, tb_level.upline_id
-- , (tb_log.turn_sport + tb_log.turn_casino + tb_log.turn_game + tb_log.turn_lottery + tb_log.turn_p2p + tb_log.turn_financial) AS total_turn_amount
-- , (tb_log.commission_sport + tb_log.commission_casino + tb_log.commission_game + tb_log.commission_lottery + tb_log.commission_p2p + tb_log.commission_financial) AS total_commission
-- , (tb_log.commission_sport1 + tb_log.commission_casino1 + tb_log.commission_game1 + tb_log.commission_lottery1 + tb_log.commission_p2p1 + tb_log.commission_financial1) AS total_commission1
-- , (tb_log.commission_sport2 + tb_log.commission_casino2 + tb_log.commission_game2 + tb_log.commission_lottery2 + tb_log.commission_p2p2 + tb_log.commission_financial2) AS total_commission2
-- FROM user_affiliate_income tb_log
-- JOIN affiliate_level tb_level ON tb_log.user_id = tb_level.user_id
-- WHERE tb_log.statement_date = '2024-09-11';

-- รหัสสมาชิก	turnSport	turnCasino	turnGame	turnLottery	turnP2p	turnFinace	turnTotal
-- zta68pk52000945	16	15	10	14	10	13	78
-- zta68pk52000946	24	5	4	-	20	14	67
-- zta68pk52000947	12	-	3	-	5	15	35
-- zta68pk52000948	6	6	2	25	27	16	82
INSERT INTO `play_log` (`player`, `user_id`, `date`, `turn_sport`, `turn_casino`, `turn_game`, `turn_lottery`, `turn_p2p`, `turn_financial`, `turn_total`) VALUES
  ('zta68pk52000945', 89088, '2024-09-17', 16, 15, 10, 14, 10, 13, 78),
  ('zta68pk52000946', 89089, '2024-09-17', 24, 5, 4, 0, 20, 14, 67),
  ('zta68pk52000947', 89090, '2024-09-17', 12, 0, 3, 0, 5, 15, 35),
  ('zta68pk52000948', 89091, '2024-09-17', 6, 6, 2, 25, 27, 16, 82);

-- daily key zta68pk52000985_20240917_4
INSERT INTO `user_playlog` (`daily_key`, `user_id`, `statement_date`, `turn_sport`, `turn_casino`, `turn_game`, `turn_lottery`, `turn_p2p`, `turn_financial`, `turn_total`) VALUES
  ('zta68pk52000945_20240917_1', 89088, '2024-09-17', 16, 15, 10, 14, 10, 13, 78),
  ('zta68pk52000946_20240917_1', 89089, '2024-09-17', 24, 5, 4, 0, 20, 14, 67),
  ('zta68pk52000947_20240917_1', 89090, '2024-09-17', 12, 0, 3, 0, 5, 15, 35),
  ('zta68pk52000948_20240917_1', 89091, '2024-09-17', 6, 6, 2, 25, 27, 16, 82);

-- รหัสสมาชิก	turnSport	turnCasino	turnGame	turnLottery	turnP2p	turnFinace
-- zta68pk52000987						
-- zta68pk52000988	10	15	4	10	20	12
-- zta68pk52000992	15	5	7	15	17	23
-- zta68pk52000989	23	16	11	16	23	43
-- zta68pk52000993	10	23	13	23	12	12
-- zta68pk52000994	50	43	32	12	13	12
-- zta68pk52000990	22	5	2	3	10	12
-- zta68pk52000997	11	12	23	12	12	32
-- zta68pk52000996	9	19	12	23	11	10
INSERT INTO `play_log` (`player`, `user_id`, `date`, `turn_sport`, `turn_casino`, `turn_game`, `turn_lottery`, `turn_p2p`, `turn_financial`, `turn_total`) VALUES
  ('zta68pk52000987', 89137, '2024-09-17', 0, 0, 0, 0, 0, 0, 0),
  ('zta68pk52000988', 89138, '2024-09-17', 10, 15, 4, 10, 20, 12, 71),
  ('zta68pk52000992', 89142, '2024-09-17', 15, 5, 7, 15, 17, 23, 82),
  ('zta68pk52000989', 89139, '2024-09-17', 23, 16, 11, 16, 23, 43, 132),
  ('zta68pk52000993', 89143, '2024-09-17', 10, 23, 13, 23, 12, 12, 93),
  ('zta68pk52000994', 89144, '2024-09-17', 50, 43, 32, 12, 13, 12, 162),
  ('zta68pk52000990', 89140, '2024-09-17', 22, 5, 2, 3, 10, 12, 54),
  ('zta68pk52000997', 89145, '2024-09-17', 11, 12, 23, 12, 12, 32, 92),
  ('zta68pk52000996', 89146, '2024-09-17', 9, 19, 12, 23, 11, 10, 84);

INSERT INTO `user_playlog` (`daily_key`, `user_id`, `statement_date`, `turn_sport`, `turn_casino`, `turn_game`, `turn_lottery`, `turn_p2p`, `turn_financial`, `turn_total`) VALUES
  ('zta68pk52000987_20240917_1', 89137, '2024-09-17', 0, 0, 0, 0, 0, 0, 0),
  ('zta68pk52000988_20240917_1', 89138, '2024-09-17', 10, 15, 4, 10, 20, 12, 71),
  ('zta68pk52000992_20240917_1', 89142, '2024-09-17', 15, 5, 7, 15, 17, 23, 82),
  ('zta68pk52000989_20240917_1', 89139, '2024-09-17', 23, 16, 11, 16, 23, 43, 132),
  ('zta68pk52000993_20240917_1', 89143, '2024-09-17', 10, 23, 13, 23, 12, 12, 93),
  ('zta68pk52000994_20240917_1', 89144, '2024-09-17', 50, 43, 32, 12, 13, 12, 162),
  ('zta68pk52000990_20240917_1', 89140, '2024-09-17', 22, 5, 2, 3, 10, 12, 54),
  ('zta68pk52000997_20240917_1', 89145, '2024-09-17', 11, 12, 23, 12, 12, 32, 92),
  ('zta68pk52000996_20240917_1', 89146, '2024-09-17', 9, 19, 12, 23, 11, 10, 84);


SELECT pl.user_id, SUM(pl.win_lose_total)
FROM `db_river_club`.`play_log` pl 
WHERE pl.`date` = '2024-09-18'
GROUP BY pl.user_id;
HAVING SUM(pl.win_lose_total) < 0
ORDER BY SUM(pl.win_lose_total) ASC;

SELECT pl.`date`, pl.user_id, pl.win_lose_total, prl.total_loss_amount, prl.return_price
FROM `db_river_club`.`play_log` pl 
LEFT JOIN `db_river_club`.`promotion_return_loser` prl ON pl.user_id = prl.user_id AND pl.`date` = prl.of_date
WHERE pl.`date` = '2024-09-10' AND pl.win_lose_total < 0 
ORDER BY pl.`win_lose_total` ASC;



SELECT 'db_4bar88' AS database_name, merchant_id FROM db_4bar88.paygate_setting WHERE id = 1
UNION ALL
SELECT 'db_9goldclub' AS database_name, merchant_id FROM db_9goldclub.paygate_setting WHERE id = 1
UNION ALL
SELECT 'db_a4u_top' AS database_name, merchant_id FROM db_a4u_top.paygate_setting WHERE id = 1
UNION ALL
SELECT 'db_allin98' AS database_name, merchant_id FROM db_allin98.paygate_setting WHERE id = 1
UNION ALL
SELECT 'db_ambet65' AS database_name, merchant_id FROM db_ambet65.paygate_setting WHERE id = 1
UNION ALL
SELECT 'db_atmplus' AS database_name, merchant_id FROM db_atmplus.paygate_setting WHERE id = 1
UNION ALL
SELECT 'db_auto_game' AS database_name, merchant_id FROM db_auto_game.paygate_setting WHERE id = 1
UNION ALL
SELECT 'db_autoslot168' AS database_name, merchant_id FROM db_autoslot168.paygate_setting WHERE id = 1
UNION ALL
SELECT 'db_baza888' AS database_name, merchant_id FROM db_baza888.paygate_setting WHERE id = 1
UNION ALL
SELECT 'db_bbmslot' AS database_name, merchant_id FROM db_bbmslot.paygate_setting WHERE id = 1
UNION ALL
SELECT 'db_brb88bet' AS database_name, merchant_id FROM db_brb88bet.paygate_setting WHERE id = 1
UNION ALL
SELECT 'db_brobet' AS database_name, merchant_id FROM db_brobet.paygate_setting WHERE id = 1
UNION ALL
SELECT 'db_cnone_bet' AS database_name, merchant_id FROM db_cnone_bet.paygate_setting WHERE id = 1
UNION ALL
SELECT 'db_eleven111' AS database_name, merchant_id FROM db_eleven111.paygate_setting WHERE id = 1
UNION ALL
SELECT 'db_ezrich' AS database_name, merchant_id FROM db_ezrich.paygate_setting WHERE id = 1
UNION ALL
SELECT 'db_fivedragon' AS database_name, merchant_id FROM db_fivedragon.paygate_setting WHERE id = 1
UNION ALL
SELECT 'db_gm1_plus' AS database_name, merchant_id FROM db_gm1_plus.paygate_setting WHERE id = 1
UNION ALL
SELECT 'db_kaya68' AS database_name, merchant_id FROM db_kaya68.paygate_setting WHERE id = 1
UNION ALL
SELECT 'db_luxxplay' AS database_name, merchant_id FROM db_luxxplay.paygate_setting WHERE id = 1
UNION ALL
SELECT 'db_maga89_net' AS database_name, merchant_id FROM db_maga89_net.paygate_setting WHERE id = 1
UNION ALL
SELECT 'db_megaclub' AS database_name, merchant_id FROM db_megaclub.paygate_setting WHERE id = 1
UNION ALL
SELECT 'db_mixxway' AS database_name, merchant_id FROM db_mixxway.paygate_setting WHERE id = 1
UNION ALL
SELECT 'db_mr89' AS database_name, merchant_id FROM db_mr89.paygate_setting WHERE id = 1
UNION ALL
SELECT 'db_paplern999' AS database_name, merchant_id FROM db_paplern999.paygate_setting WHERE id = 1
UNION ALL
SELECT 'db_pgautoall' AS database_name, merchant_id FROM db_pgautoall.paygate_setting WHERE id = 1
UNION ALL
SELECT 'db_rabbitrich' AS database_name, merchant_id FROM db_rabbitrich.paygate_setting WHERE id = 1
UNION ALL
SELECT 'db_river_club' AS database_name, merchant_id FROM db_river_club.paygate_setting WHERE id = 1
UNION ALL
SELECT 'db_rogerclub' AS database_name, merchant_id FROM db_rogerclub.paygate_setting WHERE id = 1
UNION ALL
SELECT 'db_royal447' AS database_name, merchant_id FROM db_royal447.paygate_setting WHERE id = 1
UNION ALL
SELECT 'db_s7_luck' AS database_name, merchant_id FROM db_s7_luck.paygate_setting WHERE id = 1
UNION ALL
SELECT 'db_showpow777' AS database_name, merchant_id FROM db_showpow777.paygate_setting WHERE id = 1
UNION ALL
SELECT 'db_therich333' AS database_name, merchant_id FROM db_therich333.paygate_setting WHERE id = 1
UNION ALL
SELECT 'db_topgame789' AS database_name, merchant_id FROM db_topgame789.paygate_setting WHERE id = 1
UNION ALL
SELECT 'db_topsure168' AS database_name, merchant_id FROM db_topsure168.paygate_setting WHERE id = 1
UNION ALL
SELECT 'db_uca999' AS database_name, merchant_id FROM db_uca999.paygate_setting WHERE id = 1
UNION ALL
SELECT 'db_uk888club' AS database_name, merchant_id FROM db_uk888club.paygate_setting WHERE id = 1
UNION ALL
SELECT 'db_utaslot' AS database_name, merchant_id FROM db_utaslot.paygate_setting WHERE id = 1
UNION ALL
SELECT 'db_warior88' AS database_name, merchant_id FROM db_warior88.paygate_setting WHERE id = 1
UNION ALL
SELECT 'db_wowgame88' AS database_name, merchant_id FROM db_wowgame88.paygate_setting WHERE id = 1;


SELECT 'db_4bar88' AS db_name, id , chat_uid, chat_title, chat_type, token AS statement_date FROM db_4bar88.telegram_chat_token 
UNION ALL
SELECT 'db_9goldclub' AS db_name, id , chat_uid, chat_title, chat_type, token AS statement_date FROM db_9goldclub.telegram_chat_token 
UNION ALL
SELECT 'db_a4u_top' AS db_name, id , chat_uid, chat_title, chat_type, token AS statement_date FROM db_a4u_top.telegram_chat_token 
UNION ALL
SELECT 'db_allin98' AS db_name, id , chat_uid, chat_title, chat_type, token AS statement_date FROM db_allin98.telegram_chat_token 
UNION ALL
SELECT 'db_ambet65' AS db_name, id , chat_uid, chat_title, chat_type, token AS statement_date FROM db_ambet65.telegram_chat_token 
UNION ALL
SELECT 'db_atmplus' AS db_name, id , chat_uid, chat_title, chat_type, token AS statement_date FROM db_atmplus.telegram_chat_token 
UNION ALL
SELECT 'db_auto_game' AS db_name, id , chat_uid, chat_title, chat_type, token AS statement_date FROM db_auto_game.telegram_chat_token 
UNION ALL
SELECT 'db_autoslot168' AS db_name, id , chat_uid, chat_title, chat_type, token AS statement_date FROM db_autoslot168.telegram_chat_token 
UNION ALL
SELECT 'db_baza888' AS db_name, id , chat_uid, chat_title, chat_type, token AS statement_date FROM db_baza888.telegram_chat_token 
UNION ALL
SELECT 'db_bbmslot' AS db_name, id , chat_uid, chat_title, chat_type, token AS statement_date FROM db_bbmslot.telegram_chat_token 
UNION ALL
SELECT 'db_brb88bet' AS db_name, id , chat_uid, chat_title, chat_type, token AS statement_date FROM db_brb88bet.telegram_chat_token 
UNION ALL
SELECT 'db_brobet' AS db_name, id , chat_uid, chat_title, chat_type, token AS statement_date FROM db_brobet.telegram_chat_token 
UNION ALL
SELECT 'db_cnone_bet' AS db_name, id , chat_uid, chat_title, chat_type, token AS statement_date FROM db_cnone_bet.telegram_chat_token 
UNION ALL
SELECT 'db_eleven111' AS db_name, id , chat_uid, chat_title, chat_type, token AS statement_date FROM db_eleven111.telegram_chat_token 
UNION ALL
SELECT 'db_ezrich' AS db_name, id , chat_uid, chat_title, chat_type, token AS statement_date FROM db_ezrich.telegram_chat_token 
UNION ALL
SELECT 'db_fivedragon' AS db_name, id , chat_uid, chat_title, chat_type, token AS statement_date FROM db_fivedragon.telegram_chat_token 
UNION ALL
SELECT 'db_gm1_plus' AS db_name, id , chat_uid, chat_title, chat_type, token AS statement_date FROM db_gm1_plus.telegram_chat_token 
UNION ALL
SELECT 'db_kaya68' AS db_name, id , chat_uid, chat_title, chat_type, token AS statement_date FROM db_kaya68.telegram_chat_token 
UNION ALL
SELECT 'db_luxxplay' AS db_name, id , chat_uid, chat_title, chat_type, token AS statement_date FROM db_luxxplay.telegram_chat_token 
UNION ALL
SELECT 'db_maga89_net' AS db_name, id , chat_uid, chat_title, chat_type, token AS statement_date FROM db_maga89_net.telegram_chat_token 
UNION ALL
SELECT 'db_megaclub' AS db_name, id , chat_uid, chat_title, chat_type, token AS statement_date FROM db_megaclub.telegram_chat_token 
UNION ALL
SELECT 'db_mixxway' AS db_name, id , chat_uid, chat_title, chat_type, token AS statement_date FROM db_mixxway.telegram_chat_token 
UNION ALL
SELECT 'db_mr89' AS db_name, id , chat_uid, chat_title, chat_type, token AS statement_date FROM db_mr89.telegram_chat_token 
UNION ALL
SELECT 'db_paplern999' AS db_name, id , chat_uid, chat_title, chat_type, token AS statement_date FROM db_paplern999.telegram_chat_token 
UNION ALL
SELECT 'db_pgautoall' AS db_name, id , chat_uid, chat_title, chat_type, token AS statement_date FROM db_pgautoall.telegram_chat_token 
UNION ALL
SELECT 'db_rabbitrich' AS db_name, id , chat_uid, chat_title, chat_type, token AS statement_date FROM db_rabbitrich.telegram_chat_token 
UNION ALL
SELECT 'db_river_club' AS db_name, id , chat_uid, chat_title, chat_type, token AS statement_date FROM db_river_club.telegram_chat_token 
UNION ALL
SELECT 'db_rogerclub' AS db_name, id , chat_uid, chat_title, chat_type, token AS statement_date FROM db_rogerclub.telegram_chat_token 
UNION ALL
SELECT 'db_royal447' AS db_name, id , chat_uid, chat_title, chat_type, token AS statement_date FROM db_royal447.telegram_chat_token 
UNION ALL
SELECT 'db_s7_luck' AS db_name, id , chat_uid, chat_title, chat_type, token AS statement_date FROM db_s7_luck.telegram_chat_token 
UNION ALL
SELECT 'db_showpow777' AS db_name, id , chat_uid, chat_title, chat_type, token AS statement_date FROM db_showpow777.telegram_chat_token 
UNION ALL
SELECT 'db_therich333' AS db_name, id , chat_uid, chat_title, chat_type, token AS statement_date FROM db_therich333.telegram_chat_token 
UNION ALL
SELECT 'db_topgame789' AS db_name, id , chat_uid, chat_title, chat_type, token AS statement_date FROM db_topgame789.telegram_chat_token 
UNION ALL
SELECT 'db_topsure168' AS db_name, id , chat_uid, chat_title, chat_type, token AS statement_date FROM db_topsure168.telegram_chat_token 
UNION ALL
SELECT 'db_uca999' AS db_name, id , chat_uid, chat_title, chat_type, token AS statement_date FROM db_uca999.telegram_chat_token 
UNION ALL
SELECT 'db_uk888club' AS db_name, id , chat_uid, chat_title, chat_type, token AS statement_date FROM db_uk888club.telegram_chat_token 
UNION ALL
SELECT 'db_utaslot' AS db_name, id , chat_uid, chat_title, chat_type, token AS statement_date FROM db_utaslot.telegram_chat_token 
UNION ALL
SELECT 'db_warior88' AS db_name, id , chat_uid, chat_title, chat_type, token AS statement_date FROM db_warior88.telegram_chat_token 
UNION ALL
SELECT 'db_wowgame88' AS db_name, id , chat_uid, chat_title, chat_type, token AS statement_date FROM db_wowgame88.telegram_chat_token;
