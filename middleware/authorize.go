package middleware

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt"
)

type authError struct {
	Message string `json:"message" example:"error" `
}

func AuthorizeAdmin(c *gin.Context) {

	token := c.Request.Header.Get("Authorization")
	if token == "" {
		c.AbortWithStatusJSON(401, authError{
			Message: "Unauthorized",
		})
		return
	}

	if len(strings.Split(token, " ")) != 2 {
		c.AbortWithStatusJSON(401, authError{
			Message: "Unauthorized",
		})
		return
	}

	token = strings.Split(token, " ")[1]

	claims, err := jwt.ParseWithClaims(token, jwt.MapClaims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(os.Getenv("JWT_SECRET_ADMIN")), nil
	})

	if err != nil {
		// log.Println("ParseWithClaims.err", err)
		c.AbortWithStatusJSON(401, authError{
			Message: "Unauthorized",
		})
		return
	}

	if !claims.Valid {
		c.AbortWithStatusJSON(401, authError{
			Message: "Unauthorized",
		})
		return
	}

	if claims.Claims.(jwt.MapClaims)["deviceId"] != nil {
		c.AbortWithStatusJSON(401, authError{
			Message: "Unauthorized",
		})
		return
	}

	if claims.Claims.(jwt.MapClaims)["adminId"] == nil &&
		claims.Claims.(jwt.MapClaims)["phone"] == nil &&
		claims.Claims.(jwt.MapClaims)["username"] == nil &&
		claims.Claims.(jwt.MapClaims)["email"] == nil &&
		claims.Claims.(jwt.MapClaims)["role"] == nil {
		c.AbortWithStatusJSON(401, authError{
			Message: "Unauthorized",
		})
		return
	}

	c.Set("adminId", claims.Claims.(jwt.MapClaims)["adminId"])
	c.Set("phone", claims.Claims.(jwt.MapClaims)["phone"])
	c.Set("username", claims.Claims.(jwt.MapClaims)["username"])
	c.Set("email", claims.Claims.(jwt.MapClaims)["email"])
	c.Set("role", claims.Claims.(jwt.MapClaims)["role"])

	c.Next()
}

func AuthorizeUser(c *gin.Context) {

	token := c.Request.Header.Get("Authorization")
	if token == "" {
		c.AbortWithStatusJSON(401, authError{
			Message: "Unauthorized",
		})
		return
	}

	if len(strings.Split(token, " ")) != 2 {
		c.AbortWithStatusJSON(401, authError{
			Message: "Unauthorized",
		})
		return
	}

	token = strings.Split(token, " ")[1]

	claims, err := jwt.ParseWithClaims(token, jwt.MapClaims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(os.Getenv("JWT_SECRET_USER")), nil
	})

	if err != nil {
		c.AbortWithStatusJSON(401, authError{
			Message: "Unauthorized",
		})
		return
	}

	if !claims.Valid {
		c.AbortWithStatusJSON(401, authError{
			Message: "Unauthorized",
		})
		return
	}

	if claims.Claims.(jwt.MapClaims)["userId"] == nil &&
		claims.Claims.(jwt.MapClaims)["phone"] == nil &&
		claims.Claims.(jwt.MapClaims)["username"] == nil {
		c.AbortWithStatusJSON(401, authError{
			Message: "Unauthorized",
		})
		return
	}

	c.Set("userId", claims.Claims.(jwt.MapClaims)["userId"])
	c.Set("phone", claims.Claims.(jwt.MapClaims)["phone"])
	c.Set("username", claims.Claims.(jwt.MapClaims)["username"])
	c.Set("memberCode", claims.Claims.(jwt.MapClaims)["memberCode"])

	c.Next()
}

// verifySignature checks if the provided signature is valid
func verifySignature(agentKey, timestamp, signature, secretKey string) bool {
	message := agentKey + timestamp
	h := hmac.New(sha256.New, []byte(secretKey))
	h.Write([]byte(message))
	expectedSignature := base64.StdEncoding.EncodeToString(h.Sum(nil))
	return hmac.Equal([]byte(expectedSignature), []byte(signature))
}

// HMACMiddleware is an HTTP middleware for verifying HMAC signatures
func HMACMiddleware(c *gin.Context) {
	secretKey := os.Getenv("JWT_SECRET_USER")
	token := c.Request.Header.Get("Authorization")
	if token == "" {
		c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"message": "Unauthorized"})
		return
	}

	tokenParts := strings.Split(token, " ")
	if len(tokenParts) != 2 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"message": "Unauthorized"})
		return
	}

	signature := tokenParts[1]
	timestamp := c.Request.Header.Get("Timestamp")
	agentKey := c.Request.Header.Get("Agent-Key")

	if !verifySignature(agentKey, timestamp, signature, secretKey) {
		c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"message": "Unauthorized"})
		return
	}

	// Verify timestamp to prevent replay attacks (optional)
	ts, err := time.Parse(time.RFC3339, timestamp)
	if err != nil || time.Since(ts) > 5*time.Minute {
		c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"message": "Unauthorized"})
		return
	}

	c.Set("agentKey", agentKey)
	c.Next()
}

func AuthorizeBasicUser(c *gin.Context) {

	// You can simply do as follow:
	// 1. Convert Base64 string Zm1sZzoxYmM2YmU3OGRhNzlhN… to plain text
	// 2. Compare username and password with the one store in your system
	// 3. If incorrect, reject this request

	token := c.Request.Header.Get("Authorization")
	if token == "" {
		c.AbortWithStatusJSON(401, authError{
			Message: "Unauthorized",
		})
		return
	}
	// fmt.Println("DEBUG.token", token)

	if len(strings.Split(token, " ")) != 2 {
		c.AbortWithStatusJSON(401, authError{
			Message: "Unauthorized",
		})
		return
	}

	token = strings.Split(token, " ")[1]

	plains, err := base64.StdEncoding.DecodeString(token)
	if err != nil {
		c.AbortWithStatusJSON(401, authError{
			Message: "Unauthorized",
		})
		return
	}
	// fmt.Println("DEBUG.plains", string(plains))

	claims := strings.Split(string(plains), ":")
	if len(claims) != 2 || claims[0] == "" || claims[1] == "" {
		c.AbortWithStatusJSON(401, authError{
			Message: "Unauthorized",
		})
		return
	}

	// Check username and password with ENV
	if claims[0] != os.Getenv("AGF88_API_USERNAME") || claims[1] != os.Getenv("AGF88_API_KEY") {
		c.AbortWithStatusJSON(401, authError{
			Message: "Unauthorized",
		})
		return
	}

	// c.Set("userId", claims.Claims.(jwt.MapClaims)["userId"])
	// c.Set("memberCode", claims.Claims.(jwt.MapClaims)["memberCode"])

	c.Next()
}

func AuthorizePgHard(c *gin.Context) {
	fmt.Println("Header", c.Request.Header)
	token := c.Request.Header.Get("Authorization")
	if token == "" {
		c.AbortWithStatusJSON(401, authError{
			Message: "Unauthorized",
		})
		return
	}
	fmt.Println("token", token)

	if token != os.Getenv("PG_HARD_PRIVATE_KEY") {
		c.AbortWithStatusJSON(401, authError{
			Message: "Unauthorized",
		})
		return
	}

	fmt.Println("token", token)
	c.Next()
}
