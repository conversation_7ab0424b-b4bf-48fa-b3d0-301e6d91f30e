package middleware

import (
	"cybergame-api/model"
	"errors"

	"github.com/mojocn/base64Captcha"
)

var store = base64Captcha.DefaultMemStore

func GenerateCaptcha() model.CapchaResponse {

	driver := base64Captcha.NewDriverDigit(100, 240, 6, 0.8, 90)
	captcha := base64Captcha.NewCaptcha(driver, store)
	id, b64s, _ := captcha.Generate()
	return model.CapchaResponse{
		Id:     id,
		Base64: b64s,
	}
}

func GenerateUserCaptcha(len int) model.CapchaResponse {

	if len == 0 {
		return model.CapchaResponse{}
	}

	driver := base64Captcha.NewDriverDigit(100, 240, len, 0.5, 50)
	captcha := base64Captcha.NewCaptcha(driver, store)
	id, b64s, _ := captcha.Generate()
	return model.CapchaResponse{
		Id:     id,
		Base64: b64s,
	}
}

func VerifyCaptcha(param model.CapchaVerifyRequest) (*model.CapchaVerifyResponse, error) {

	var result model.CapchaVerifyResponse

	if param.Id == "" || param.VerifyValue == "" {
		return nil, errors.New("INVALID_CAPTCHA")
	}

	// verify the captcha then clear it from memory
	if store.Verify(param.Id, param.VerifyValue, true) {
		result.IsValid = true
	} else {
		return nil, errors.New("INVALID_CAPTCHA")
	}
	return &result, nil
}
