package middleware

import (
	"cybergame-api/model"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type connectDB struct {
	db *gorm.DB
}

func Role(db *gorm.DB) connectDB {
	return connectDB{db}
}

func (r connectDB) CheckPermission(perKey []string) gin.HandlerFunc {
	return func(c *gin.Context) {

		role := c.MustGet("role").(string)
		if role == "SUPER_ADMIN" {
			c.Next()
			return
		}

		id, err := c.MustGet("adminId").(float64)
		if !err {
			c.AbortWithStatusJSON(401, gin.H{
				"message": "Unauthorized",
			})
			return
		}

		// GetAdmin Active
		var admin model.Admin
		sql := r.db.Table("admin")
		sql = sql.Select("id, status")
		sql = sql.Where("id = ?", int64(id))
		sql = sql.Where("status = ?", "ACTIVE")
		sql = sql.Where("deleted_at IS NULL")
		sql = sql.Limit(1)
		if err := sql.Take(&admin).Error; err != nil {
			c.AbortWithStatusJSON(401, gin.H{
				"message": "Unauthorized",
			})
			return
		}

		var count int64

		permissionCount := r.db.Table("admin")
		permissionCount = permissionCount.Joins("LEFT JOIN admin_group_permission ON admin.admin_group_id = admin_group_permission.group_id")
		permissionCount = permissionCount.Joins("LEFT JOIN permission ON permission.id = admin_group_permission.permission_id")
		permissionCount = permissionCount.Select("admin_group_permission.id")
		permissionCount = permissionCount.Where("admin.id = ?", int64(id))
		permissionCount = permissionCount.Where("permission.permission_key IN ?", perKey)

		if err := permissionCount.Limit(1).Count(&count).Error; err != nil {
			c.AbortWithStatusJSON(500, gin.H{
				"message": "Internal Server Error",
			})
			return
		}

		if count == 0 {
			c.AbortWithStatusJSON(403, gin.H{
				"message": "Permission Denied",
			})
			return
		}

		c.Next()
	}
}
