package middleware

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type scamDB struct {
	db *gorm.DB
}

func Scam(db *gorm.DB) scamDB {
	return scamDB{db}
}

func (r scamDB) CheckScam(c *gin.Context) {

	id := int64(c.Must<PERSON>et("userId").(float64))

	var total int64

	if err := r.db.Table("scammer").
		Select("id").
		Where("user_id = ?", id).
		Count(&total).
		Limit(1).
		Error; err != nil {
		c.AbortWithStatusJSON(500, gin.H{
			"message": "Internal Server Error",
		})
		return
	}

	if total > 0 {
		c.AbortWithStatusJSON(403, gin.H{
			"message": "ถูกระงับการใช้งาน",
		})
		return
	}

	c.Next()
}
