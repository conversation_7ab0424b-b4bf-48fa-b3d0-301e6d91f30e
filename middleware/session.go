package middleware

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"log"
	"os"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

func SingleSession(db *gorm.DB) connectDB {
	return connectDB{db}
}

type SessionDuplicateLog struct {
	AdminId   int64
	Token     string
	ExpiredAt time.Time
}

var alreadyLogDuplicate = make(map[string]SessionDuplicateLog)

func (r connectDB) SingleAdminSession() gin.HandlerFunc {
	return func(c *gin.Context) {

		ssMode := os.Getenv("SINGLE_SESSION")
		if !strings.Contains(ssMode, "admin") {
			c.Next()
			return
		}

		token := c.Request.Header.Get("Authorization")
		if token == "" {
			c.AbortWithStatusJSON(401, authError{
				Message: "Unauthorized",
			})
			return
		}
		tempToken := strings.Split(token, " ")
		if len(tempToken) != 2 {
			c.AbortWithStatusJSON(401, authError{
				Message: "Unauthorized",
			})
			return
		}
		hashToken := helper.GetMD5Hash(tempToken[1])

		// Not super admin
		role := c.MustGet("role").(string)
		if role == "SUPER_ADMIN" {
			c.Next()
			return
		}

		adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

		// GetAdmin Active
		var admin model.AdminSingleSession
		sql := r.db.Table("admin_single_session")
		sql = sql.Select("id, md5_token")
		sql = sql.Where("admin_id = ?", adminId)
		sql = sql.Limit(1)
		if err := sql.Take(&admin).Error; err != nil {
			c.AbortWithStatusJSON(401, gin.H{
				"message": "Unauthorized",
			})
			return
		}

		if admin.Md5Token != hashToken {
			log.Println("INVALID_ADMIN_TOKEN_", admin.Md5Token, " for ", hashToken)
			if _, ok := alreadyLogDuplicate[hashToken]; !ok {
				// AddLog
				alreadyLogDuplicate[hashToken] = SessionDuplicateLog{
					AdminId:   adminId,
					Token:     hashToken,
					ExpiredAt: time.Now().Add(5 * time.Minute),
				}
				var createLogBody = map[string]interface{}{
					"admin_id": adminId,
					"is_show":  false,
					"type_id":  7,
					"detail":   "INVALID_ADMIN_TOKEN_" + admin.Md5Token + " for " + hashToken,
					"json_input": helper.StructJson(map[string]interface{}{
						"admin_id":   adminId,
						"db_hash":    admin.Md5Token,
						"input_hash": hashToken,
					}),
				}
				if err := r.db.Table("admin_action").Create(createLogBody).Error; err != nil {
					log.Println("ERROR_CREATE_ADMIN_ACTION", err.Error())
				}
				// Clear log
				for i, v := range alreadyLogDuplicate {
					if v.ExpiredAt.Before(time.Now()) {
						delete(alreadyLogDuplicate, i)
					}
				}
				log.Printf("TOTAL_LOG_DUPLICATE: %d", len(alreadyLogDuplicate))
			}
			c.AbortWithStatusJSON(401, gin.H{
				"message": "Unauthorized",
			})
			return
		}

		c.Next()
	}
}

func (r connectDB) SingleUserSession() gin.HandlerFunc {
	return func(c *gin.Context) {

		ssMode := os.Getenv("SINGLE_SESSION")
		if !strings.Contains(ssMode, "user") {
			c.Next()
			return
		}

		token := c.Request.Header.Get("Authorization")
		if token == "" {
			c.AbortWithStatusJSON(401, authError{
				Message: "Unauthorized",
			})
			return
		}
		tempToken := strings.Split(token, " ")
		if len(tempToken) != 2 {
			c.AbortWithStatusJSON(401, authError{
				Message: "Unauthorized",
			})
			return
		}
		hashToken := helper.GetMD5Hash(tempToken[1])

		userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

		// GetAdmin Active
		var user model.AdminSingleSession
		sql := r.db.Table("user_single_session")
		sql = sql.Select("id, md5_token")
		sql = sql.Where("user_id = ?", userId)
		sql = sql.Limit(1)
		if err := sql.Take(&user).Error; err != nil {
			c.AbortWithStatusJSON(401, gin.H{
				"message": "Unauthorized",
			})
			return
		}

		if user.Md5Token != hashToken {
			log.Println("INVALID_USER_TOKEN_", user.Md5Token, " for ", hashToken)
			c.AbortWithStatusJSON(401, gin.H{
				"message": "Unauthorized",
			})
			return
		}

		c.Next()
	}
}
