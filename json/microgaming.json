[{"GameName": "Mining Pots of Gold", "GameCode": "SMG_miningPotsOfGold", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/20240206/0d51dc0044e140b5bef24cc781760295_smg_MiningPotsOfGold_icon_square_300x300_en.png"}, {"GameName": "Crazy Rich Tigers", "GameCode": "SMG_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/20240206/0807254f92a1410cbcfcd55ac610c93c_smg_CrazyRichTigers_icon_square_300x300_en.png"}, {"GameName": "Break Away Max", "GameCode": "SMG_breakAwayMax", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/20231117/4f7cf0307cf2459b806191d5dc65dcdd_smg_BreakawayMAX_icon_square_200x200_en.png"}, {"GameName": "Fishin' Pots of Gold™ Gold Blitz™", "GameCode": "SMG_fishinPotsOfGoldGoldBlitz", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/20231117/0350dfbe16624055b3c50847ec2abf1f_smg_FishinPotsOfGoldGoldBlitz_icon_square_200x200_en.png"}, {"GameName": "Pong Pong Mahjong", "GameCode": "SMG_pongPongMahjong", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/20231025/1e7c2a9dffb446d992591e97565ec895_smg_Pongpongmahjong_icon_square_300x300_en.png"}, {"GameName": "Legendary Treasures", "GameCode": "SMG_legendaryTreasures", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/20231025/397bf34bb6d4402da9859dcf8d08679a_smg_LegendaryTreasures_icon_square_300x300_en.png"}, {"GameName": "Queens of Ra", "GameCode": "SMG_queensOfRa", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/20231025/6ac58cb2b5124c7d92c5d15582cfcb83_smg_queensofRa_icon_square_300x300_en.png"}, {"GameName": "Gem Fire Frenzy", "GameCode": "SMG_gemFireFrenzy", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/20231025/ebf612040196431fa3f8725ecc73c2f6_smg_gemFireFrenzy_icon_square_300x300_en.png"}, {"GameName": "The Eternal Widow", "GameCode": "SMG_theEternalWidow", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/20231025/570911539dfa45b48104679e3e9cc386_smg_theEternalWidow_icon_square_300x300_en.png"}, {"GameName": "Hot Chilies", "GameCode": "SMG_hotChillies", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/20231025/d94e7bf6d227435695a1c3556e7a0220_smg_hotChilies_icon_square_300x300_en.png"}, {"GameName": "Anvil & Ore", "GameCode": "SMG_anvilAndOre", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/20231025/351927da81a54918ba0373402409e471_smg_Anvil_Ore_icon_square_300x300_en.png"}, {"GameName": "Unusual Suspects", "GameCode": "SMG_unusualSuspects", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/20231025/88242e7d05354904878afd72f3921bfa_smg_UnusualSuspects_icon_square_300x300_en.png"}, {"GameName": "Asgardian Fire", "GameCode": "SMG_asgardianFire", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/20231025/4dda6c71f1c24a77bab27dfb68753e73_smg_AsgardianFire_icon_square_300x300_en.png"}, {"GameName": "Tiki Tiki Boom", "GameCode": "SMG_tikiTikiBoom", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/20231025/f2569896e5384800807b9ac987d8b2c5_smg_tikiTikiBoom_icon_square_300x300_en.png"}, {"GameName": "9 Pots of Gold Megaways", "GameCode": "SMG_9PotsOfGoldMegaways", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/20231025/1e158fec4ccd4b1e82d78cd70f5eba25_smg_9PotsOfGoldMegaways_icon_square_300x300_en.png"}, {"GameName": "<PERSON>", "GameCode": "SMG_catpurry", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/20231025/c18267d92148430ab92195542a7f04b1_smg_catpurry_icon_square_300x300_en.png"}, {"GameName": "Amazon - Lost Gold", "GameCode": "SMG_amazonLostGold", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/20230801/85dbef1ea7094c8c9bb61359707a9dca_smg_amazonLostGold_icon_square_300x300_en.png"}, {"GameName": "4 Masks of Inca", "GameCode": "SMG_4MasksOfInca", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/20230801/ea2990c424154ac988654e6c164c4c10_smg_4MasksOfInca_icon_square_300x300_en.png"}, {"GameName": "Dog Days", "GameCode": "SMG_dogDays", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/20230801/a703832720b34308a2260df1ecf0f9c5_smg_DogDays_icon_square_300x300_en.png"}, {"GameName": "<PERSON><PERSON><PERSON> : The Magic Ring", "GameCode": "SMG_andvariTheMagicRing", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/20230801/363580c26fa24f8a86137c3de0b639ba_smg_andvariTheMagicRing_icon_square_300x300_en.png"}, {"GameName": "Fish 'Em Up", "GameCode": "SMG_fishEmUp", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/20230801/5eec09edcd2947a1b2171c1b963f0eff_smg_fishemup_icon_square_300x300_en.png"}, {"GameName": "Magic Jokers", "GameCode": "SMG_magicJokers", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/20230801/fa8649e9019e4360997fc3240aa2c0cb_smg_MagicJokers_icon_square_300x300_en.png"}, {"GameName": "Storm to Riches", "GameCode": "SMG_stormToRiches", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/20230801/d5d05f7142ab4aa09ce9539f31ba43c9_smg_StormToRiches_icon_square_300x300_en.png"}, {"GameName": "Tippy Tavern", "GameCode": "SMG_tippyTavern", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/20230801/44179a1153ec4e7db45104f83b012b7d_smg_tippytavern_icon_square_300x300_en.png"}, {"GameName": "FlyX", "GameCode": "SMG_flyX", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/20230801/c3ca979da0394a8a90f6e7bcbca34b93_smg_FlyX_icon_square_300x300_en.png"}, {"GameName": "Almighty Zeus Empire", "GameCode": "SMG_almightyZeusEmpire", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/20230515/87cd75d575d44b898331c32c394cd540_smg_almightyzeusempire_icon_square_300x300_en.png"}, {"GameName": "Wildfire Wins Extreme", "GameCode": "SMG_wildfireWinsExtreme", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/15f9c6ad57064905a8d31354d65c4b8a_smg_wildfirewinsextreme_icon_square_600x600_en.jpg"}, {"GameName": "Fire and Roses Joker", "GameCode": "SMG_fireAndRosesJoker", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/6b0cabbaa3744d969827502dcd70ff4c_smg_fireandrosesjoker_icon_square_600x600_en.jpg"}, {"GameName": "Rome Supermatch", "GameCode": "SMG_romeSupermatch", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/20230515/5cc70ec3ddc9464685f27ea85891ded2_smg_romesupermatch_icon_square_300x300_en.png"}, {"GameName": "<PERSON><PERSON>", "GameCode": "SMG_bisonMoon", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/e3c2c74882844e99a785a687398cb231_smg_bisonmoon_icon_square_600x600_en.jpg"}, {"GameName": "Soccer Striker", "GameCode": "SMG_soccerStriker", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/f71fc2ada2e84aa09ca539ae6c6b7d94_smg_soccerstriker_icon_square_600x600_en.jpg"}, {"GameName": "Gold Blitz", "GameCode": "SMG_goldBlitz", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/0cb8ee0fc14e40f8a0d6c90633763c47_smg_goldblitz_icon_square_600x600_en.jpg"}, {"GameName": "Gallo Gold Bruno's™ Megaways™", "GameCode": "SMG_galloGoldMegaways", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/1daa60a047914c1791710d8c67b3d23d_smg_gallogoldmegaways_icon_square_600x600_en.jpg"}, {"GameName": "<PERSON>", "GameCode": "SMG_candyRushWilds", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/4d1ab7ae97ea48479df528e09aef90f5_smg_candyrushwilds_icon_square_600x600_en.jpg"}, {"GameName": "777 Super BigBuildUp™ Deluxe™", "GameCode": "SMG_777superBigBuildUpDeluxe", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/6aea43c497dd4aba9a5f5fead9031002_smg_777superbigbuildupdeluxe_icon_square_600x600_en.jpg"}, {"GameName": "<PERSON><PERSON> P<PERSON>e Hot Stacks", "GameCode": "SMG_chilliPepeHotStacks", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/20230515/b67aed88dfd045fb9056addc31a3c544_smg_ChilliPepeHotStacks_icon_square_300x300_en.png"}, {"GameName": "Hyper Gold", "GameCode": "SMG_hyperGold", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/ec3a9dcfc0cf4259ba35eb80ebef47ae_smg_hypergold_icon_square_600x600_en.jpg"}, {"GameName": "Masters of Olympus", "GameCode": "SMG_mastersOfOlympus", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/e4680f68e35e41e9b0185bde8e311fb2_smg_mastersofolympus_icon_square_600x600_en.jpg"}, {"GameName": "Ancient Fortunes: Poseidon Megaways ™", "GameCode": "SMG_ancientFortunesPoseidonMegaways", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/3a919e1746f6473c93a5ba7fa9617e33_smg_ancientfortunesposeidonmegaways_icon_square_600x600_en.jpg"}, {"GameName": "Wildfire Wins", "GameCode": "SMG_wildfireWins", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/51e7c65c20c64082ada6e991bf6c3b1a_smg_wildfirewins_icon_square_600x600_en.jpg"}, {"GameName": "10000 Wishes", "GameCode": "SMG_10000Wishes", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/7f9b36d206eb4416aa69006a7c25d7c3_smg_10000wishes_icon_square_600x600_en.jpg"}, {"GameName": "Chests of Gold: Power Combo", "GameCode": "SMG_chestsOfGold", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/fc5874dac43d4695a564db16f97e525a_smg_chestsofgold_icon_square_600x600_en.jpg"}, {"GameName": "777 Mega Deluxe™", "GameCode": "SMG_777MegaDeluxe", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/10d8d6a920cc41cda8cd2bfe606c1803_smg_777megadeluxe_icon_square_600x600_en.jpg"}, {"GameName": "Gold Collector", "GameCode": "SMG_goldCollector", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/b04cb6ced99844319cf534d3f502011c_smg_goldcollector_icon_square_600x600_en.jpg"}, {"GameName": "Leprechaun Strike", "GameCode": "SMG_leprechaunStrike", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/62f07ff26cb14d3fa3e7c141828bf786_smg_leprechaunstrike_icon_square_600x600_en.jpg"}, {"GameName": "Lucky Twins Wilds", "GameCode": "SMG_luckyTwinsWilds", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/3c2e852bb4d8427bb987d82e302c66eb_smg_luckytwinswilds_icon_square_600x600_en.jpg"}, {"GameName": "Rome: Fight For Gold", "GameCode": "SMG_romeFightForGold", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/20230515/9585368c188e48269cd99040d865cc01_smg_RomeFightforGold_icon_square_300x300_en.png"}, {"GameName": "Thunderstruck Stormchaser", "GameCode": "SMG_thunderstruckStormchaser", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/5146917af2e2496f9763688fd90aef17_smg_thunderstruckstormchaser_icon_square_600x600_en.jpg"}, {"GameName": "Fortune Dragon™", "GameCode": "SMG_fortuneDragon", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/2bc667cc91484e209efc655c05b03152_smg_fortunedragon_icon_square_600x600_en.jpg"}, {"GameName": "Cash 'N Riches Megaways™", "GameCode": "SMG_cashNRichesMegaways", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/20bd693e411d4e86bee2f2703ee59d60_smg_cashnrichesmegaways_icon_square_600x600_en.jpg"}, {"GameName": "Football Finals X UP", "GameCode": "SMG_footballFinalsXUP", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/e1e19d39ea9d44c08449f30de85d5fe3_smg_footballfinalsxup_icon_square_600x600_en.jpg"}, {"GameName": "Mega Money Wheel", "GameCode": "SMG_megaMoneyWheel", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/535232753aad47bbadcf999031515bb6_smg_megamoneywheel_icon_square_600x600_en.jpg"}, {"GameName": "Lucky Twins Link and <PERSON>", "GameCode": "SMG_luckyTwinsLinkAndWin", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/c7cd7ed84392494fa4ceae883cabc1fd_smg_luckytwinslinkandwin_icon_square_600x600_en.jpg"}, {"GameName": "Fishin' <PERSON><PERSON>ts of Gold", "GameCode": "SMG_fishinBiggerPots", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/0fb6321c5ad24270a3019dcdde743fad_smg_fishinbiggerpots_icon_square_600x600_en.jpg"}, {"GameName": "Tigers Ice", "GameCode": "SMG_tigersIce", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/8161308dd4cb41fe865350d580de4241_smg_tigersice_icon_square_600x600_en.jpg"}, {"GameName": "6 Rubies of Tribute", "GameCode": "SMG_6RubiesOfTribute", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/cba9d81f16bf482fa9bd242fdbd2d4e0_smg_6rubiesoftribute_icon_square_600x600_en.jpg"}, {"GameName": "Rocky's Gold UltraWays", "GameCode": "SMG_rockysGoldUltraWays", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/9e8862a2bc0c4467844a39f3109f2a16_smg_rockysgoldultraways_icon_square_600x600_en.jpg"}, {"GameName": "<PERSON>'s Christmas Fortune", "GameCode": "SMG_fionasChristmasFortune", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/ec33b2f473924999b7b8cfbcc2e4fbd0_smg_fionaschristmasfortune_icon_square_600x600_en.jpg"}, {"GameName": "Golden Dragons", "GameCode": "SMG_goldenDragons", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/d1958e20ba014cb0bfa7a1f2a65b53c7_smg_goldendragons_icon_square_600x600_en.jpg"}, {"GameName": "<PERSON>in Christmas Pots of Gold", "GameCode": "SMG_fishinChristmasPotsOfGold", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/223942ec1e5e40a4a41060b0d0604cda_smg_fishinchristmaspotsofgold_icon_square_600x600_en.jpg"}, {"GameName": "9 Skulls of Gold", "GameCode": "SMG_9SkullsOfGold", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/15bba40aa2544f1fb95ce81dab13c247_smg_9skullsofgold_icon_square_600x600_en.jpg"}, {"GameName": "WWE: <PERSON><PERSON> of the Wilds", "GameCode": "SMG_wweClashOfTheWilds", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/690f24c5304e48db9adc64ad63962cff_smg_wweclashofthewilds_icon_square_600x600_en.jpg"}, {"GameName": "108 Heroes", "GameCode": "SMG_108Heroes", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/a5999141f59f48ef818499d46388e419_smg_108heroes_icon_square_600x600_en.jpg"}, {"GameName": "15 Tridents", "GameCode": "SMG_15Tridents", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/ad6b32bb3e6b4d789a781590edc0a510_smg_15tridents_icon_square_600x600_en.jpg"}, {"GameName": "25000 Talons", "GameCode": "SMG_25000Talons", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/aa1db7841a994fa3a28d5ac06c7740c9_smg_25000talons_icon_square_600x600_en.jpg"}, {"GameName": "4 Diamond Blues™ - Megaways™", "GameCode": "SMG_4DiamondBlues", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/5d6ed87eb5ea42c6b9775684f299598d_smg_4diamondblues_icon_square_600x600_en.jpg"}, {"GameName": "5 Reel Drive", "GameCode": "SMG_5ReelDrive", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/9676b9e0387747ce9970292018ce5469_smg_5reeldrive_icon_square_600x600_en.jpg"}, {"GameName": "5 Star Knockout", "GameCode": "SMG_5StarKnockout", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/bad084fd81824b05a078df5c7eaf1f37_smg_5starknockout_icon_square_600x600_en.jpg"}, {"GameName": "777 Royal Wheel", "GameCode": "SMG_777RoyalWheel", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/74bff502b0a34b15874433922e7d2039_smg_777royalwheel_icon_square_600x600_en.jpg"}, {"GameName": "777 Surge", "GameCode": "SMG_777Surge", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/0ce9fd7eabef460aa5579bff91f92343_smg_777surge_icon_square_600x600_en.jpg"}, {"GameName": "8 Golden Skulls of the Holly Roger Megaways™", "GameCode": "SMG_8GoldenSkullsOfHollyRoger", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/ffd595b287374a5f9323e7912887406b_smg_8goldenskullsofhollyroger_icon_square_600x600_en.jpg"}, {"GameName": "9 Blazing Diamonds", "GameCode": "SMG_9BlazingDiamonds", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/f287676643884221b69a9ff2c5bffdd3_smg_9blazingdiamonds_icon_square_600x600_en.jpg"}, {"GameName": "9 Mad Hats", "GameCode": "SMG_9MadHats", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/0391a010aca94317afdd8d6d71336974_smg_9madhats_icon_square_600x600_en.jpg"}, {"GameName": "9 Masks Of Fire", "GameCode": "SMG_9masksOfFire", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/0eae450965914a1b9c750f6bf1f2a45c_smg_9masksoffire_icon_square_600x600_en.jpg"}, {"GameName": "9 Masks of Fire™ HyperSpins™", "GameCode": "SMG_9masksOfFireHyperSpins", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/94602deb3614444097443ba6419216e2_smg_9masksoffirehyperspins_icon_square_600x600_en.jpg"}, {"GameName": "9 Pots of Gold", "GameCode": "SMG_9potsOfGold", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/7a295ed8a67948bd842f7bfc0b0efcaa_smg_9potsofgold_icon_square_600x600_en.jpg"}, {"GameName": "A Dark Matter", "GameCode": "SMG_aDarkMatter", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/debb3c161a8e4065b60566f103e6029d_smg_adarkmatter_icon_square_600x600_en.jpg"}, {"GameName": "A Tale of Elves", "GameCode": "SMG_aTaleOfElves", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/b5c12141b2d1498b8bec6bda7ec6067f_smg_ataleofelves_icon_square_600x600_en.jpg"}, {"GameName": "AbraCatDabra", "GameCode": "SMG_abraCatDabra", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/6d380f6937b44babb519ce724b38a9a0_smg_abracatdabra_icon_square_600x600_en.jpg"}, {"GameName": "ActionOps Snow and Sable", "GameCode": "SMG_actionOpsSnowAndSable", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/a219e9511aa54d4785625eff4a6feeb1_smg_actionopssnowandsable_icon_square_600x600_en.jpg"}, {"GameName": "Adventure Palace", "GameCode": "SMG_adventurePalace", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/656ce647b3084dc9a96fdca1d8482ead_smg_adventurepalace_icon_square_600x600_en.jpg"}, {"GameName": "Adventures Of Doubloon Island", "GameCode": "SMG_adventuresOfDoubloonIsland", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/621d77eaca0041998bc45822e0233ee4_smg_adventuresofdoubloonisland_icon_square_600x600_en.jpg"}, {"GameName": "Africa X UP™", "GameCode": "SMG_africaXUP", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/6ce5517bff41475f80a92547169a2bbd_smg_africaxup_icon_square_600x600_en.jpg"}, {"GameName": "Age of Conquest", "GameCode": "SMG_ageOfConquest", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/079c454f43cf4ff3a33436054cdf85f4_smg_ageofconquest_icon_square_600x600_en.jpg"}, {"GameName": "Age Of Discovery", "GameCode": "SMG_ageOfDiscovery", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/69e2810370a6414ca6a538390304d75e_smg_ageofdiscovery_icon_square_600x600_en.jpg"}, {"GameName": "Agent <PERSON>", "GameCode": "SMG_agentJaneBlonde", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/84504b9aff274780923fdcbc32136ea5_smg_agentjaneblonde_icon_square_600x600_en.jpg"}, {"GameName": "Agent <PERSON>", "GameCode": "SMG_agentJaneBlondeMaxVolume", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/3fb35397b44b494f85040fe7b102755f_smg_agentjaneblondemaxvolume_icon_square_600x600_en.jpg"}, {"GameName": "Agent <PERSON> Returns", "GameCode": "SMG_agentjaneblondereturns", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/0cbf142c6aef47fa89b3eac2b22c58c3_smg_agentjaneblondereturns_icon_square_600x600_en.jpg"}, {"GameName": "Alaskan Fishing", "GameCode": "SMG_alaskanFishing", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/ea45c4e4875845ac9a203fe6db62d26e_smg_alaskanfishing_icon_square_600x600_en.jpg"}, {"GameName": "Alchemist Stone", "GameCode": "SMG_alchemistStone", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/082779efba8e458ab97a0a17f8bec13e_smg_alchemiststone_icon_square_600x600_en.jpg"}, {"GameName": "Alchemy Blast", "GameCode": "SMG_alchemyBlast", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/b91c495ebcaf4ae0b404d5aa14f2b731_smg_alchemyblast_icon_square_600x600_en.jpg"}, {"GameName": "Alchemy Fortunes", "GameCode": "SMG_alchemyFortunes", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/2057e323b0434c0899102cb986c43bcb_smg_alchemyfortunes_icon_square_600x600_en.jpg"}, {"GameName": "Amazing Link Zeus", "GameCode": "SMG_amazingLinkZeus", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/8ae6b951ee154de6bd241667d1fe146f_smg_amazinglinkzeus_icon_square_600x600_en.jpg"}, {"GameName": "Amazing Link™ Apollo", "GameCode": "SMG_amazingLinkApollo", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/f5df2a4a3412430f81ef1eb05d3b2d3e_smg_amazinglinkapollo_icon_square_600x600_en.jpg"}, {"GameName": "Amazon Kingdom", "GameCode": "SMG_amazonKingdom", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/87ae6a3eb98b4719827659e96f79d6ce_smg_amazonkingdom_icon_square_600x600_en.jpg"}, {"GameName": "Ancient Fortunes: Zeus", "GameCode": "SMG_ancientFortunesZeus", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/e305c44d2a8a4df0be8ee126ae9cef5c_smg_ancientfortuneszeus_icon_square_600x600_en.jpg"}, {"GameName": "Aquanauts", "GameCode": "SMG_aquanauts", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/ff97e5ea3aca409b85ab31a0653ece53_smg_aquanauts_icon_square_600x600_en.jpg"}, {"GameName": "Arctic Enchantress", "GameCode": "SMG_arcticEnchantress", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/1439221e5bef4edd9a93a5d0b6830a62_smg_arcticenchantress_icon_square_600x600_en.jpg"}, {"GameName": "<PERSON><PERSON>", "GameCode": "SMG_ariana", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/45433b7e7fd64fb882dd1873b7dac121_smg_ariana_icon_square_600x600_en.jpg"}, {"GameName": "Ark of Ra", "GameCode": "SMG_arkOfRa", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/9a802d83823442ec894166b5eedc68fd_smg_arkofra_icon_square_600x600_en.jpg"}, {"GameName": "Asian Beauty", "GameCode": "SMG_asianBeauty", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/bc36da922b684ba298e7da544eeeccf2_smg_asianbeauty_icon_square_600x600_en.jpg"}, {"GameName": "<PERSON><PERSON><PERSON>", "GameCode": "SMG_assassin<PERSON><PERSON>", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/f8ef1205c54d437691639f0ce46f0bcc_smg_assassinmoon_icon_square_600x600_en.jpg"}, {"GameName": "Astro Legends: <PERSON><PERSON> and <PERSON><PERSON>", "GameCode": "SMG_astroLegendsLyraandErion", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/1f24813a1fc54406a0561a441d942b62_smg_astrolegendslyraanderion_icon_square_600x600_en.jpg"}, {"GameName": "Atlantis Rising", "GameCode": "SMG_atlantisRising", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/a70ac47fda5a41f3a4a4a70e084aa3ea_smg_atlantisrising_icon_square_600x600_en.jpg"}, {"GameName": "<PERSON>", "GameCode": "SMG_augustus", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/087b68b8f55944a096377fec6bcf27f5_smg_augustus_icon_square_600x600_en.jpg"}, {"GameName": "Aurora Wilds", "GameCode": "SMG_auroraWilds", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/a8dc77cf4b8c42e8af917028643e3f46_smg_aurorawilds_icon_square_600x600_en.jpg"}, {"GameName": "Avalon", "GameCode": "SMG_avalon", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/96edde0feb494a448a38ba3f0aebb2ee_smg_avalon_icon_square_600x600_en.jpg"}, {"GameName": "Aztec Falls", "GameCode": "SMG_aztecFalls", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/6e567ff5f9d74e4a881e69713dca07b8_smg_aztecfalls_icon_square_600x600_en.jpg"}, {"GameName": "Badminton Hero", "GameCode": "SMG_badmintonHero", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/af0748c53da347099372190c7017d69a_smg_badmintonhero_icon_square_600x600_en.jpg"}, {"GameName": "Banana Odyssey", "GameCode": "SMG_bananaOdyssey", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/07a5fb772e9741d1abccceaa75481b3e_smg_bananaodyssey_icon_square_600x600_en.jpg"}, {"GameName": "Bar Bar Black Sheep 5 Reel", "GameCode": "SMG_barBarBlackSheep5Reel", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/34f2beb25c90466bbfe632ed68899bf5_smg_barbarblacksheep5reel_icon_square_600x600_en.jpg"}, {"GameName": "Bars And Stripes", "GameCode": "SMG_BarsAndStripes", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/fdb361f8a9744552be9fc550d88ba67a_smg_barsandstripes_icon_square_600x600_en.jpg"}, {"GameName": "Basketball Star", "GameCode": "SMG_basketballStar", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/335eda19b3944781868dd5f57ef3098f_smg_basketballstar_icon_square_600x600_en.jpg"}, {"GameName": "Basketball Star Deluxe", "GameCode": "SMG_basketballStarDeluxe", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/8cb2111d76e242c18f58bdb945a41e16_smg_basketballstardeluxe_icon_square_600x600_en.jpg"}, {"GameName": "Basketball Star on Fire", "GameCode": "SMG_basketballStaronFire", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/e72e5a1638064261bbfe0be9e00fe28c_smg_basketballstaronfire_icon_square_600x600_en.jpg"}, {"GameName": "Basketball Star Wilds", "GameCode": "SMG_basketballStarWilds", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/fb9c33f35b184791a2ecfe89b21b4576_smg_basketballstarwilds_icon_square_600x600_en.jpg"}, {"GameName": "Beach Babes", "GameCode": "SMG_beachBabes", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/4a2e5628bd834299a017fb1898c54d45_smg_beachbabes_icon_square_600x600_en.jpg"}, {"GameName": "Beautiful Bones", "GameCode": "SMG_beautifulBones", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/f3d30e7357774072af5cce220449dc92_smg_beautifulbones_icon_square_600x600_en.jpg"}, {"GameName": "Big Boom Riches", "GameCode": "SMG_bigBoomRiches", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/abd5132c4afb4b71aa839bd23e617f54_smg_bigboomriches_icon_square_600x600_en.jpg"}, {"GameName": "Big Kahuna", "GameCode": "SMG_bigKahuna", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/40fb61cd265f49219d6817d5217b6498_smg_bigkahuna_icon_square_600x600_en.jpg"}, {"GameName": "Big Top", "GameCode": "SMG_bigTop", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/2f300f1fe01e44a4a27c3a66a1ea0a9c_smg_bigtop_icon_square_600x600_en.jpg"}, {"GameName": "Bikini Party", "GameCode": "SMG_bikiniParty", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/5c4fa2730d6640e1aab20f37d754a323_smg_bikiniparty_icon_square_600x600_en.jpg"}, {"GameName": "Blazing Mammoth", "GameCode": "SMG_blazingMam<PERSON>h", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/15b36f8d40e34713ac5fd3ce7011527c_smg_blazingmammoth_icon_square_600x600_en.jpg"}, {"GameName": "Boat of Fortune", "GameCode": "SMG_boatofFortune", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/81954fe8cc0b4ce4953f66cc56b0ccc1_smg_boatoffortune_icon_square_600x600_en.jpg"}, {"GameName": "Bolt X UP", "GameCode": "SMG_boltXUP", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/46e21b89db0d454aaf499b1125faf60c_smg_boltxup_icon_square_600x600_en.jpg"}, {"GameName": "Boogie Monsters", "GameCode": "SMG_boogieMonsters", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/b723bec569994dae8db4e7242a774968_smg_boogiemonsters_icon_square_600x600_en.jpg"}, {"GameName": "Book of King Arthur", "GameCode": "SMG_bookOfKingArthur", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/8d8280fade2b4336937962e36a138807_smg_bookofkingarthur_icon_square_600x600_en.jpg"}, {"GameName": "Book Of Mrs Claus", "GameCode": "SMG_bookOfMrsClaus", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/7ac9c80e48f84a47bf86f1888ea32896_smg_bookofmrsclaus_icon_square_600x600_en.jpg"}, {"GameName": "Book of Oz", "GameCode": "SMG_bookOfOz", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/d0d4d02e34f742faaf602f72672b5e9c_smg_bookofoz_icon_square_600x600_en.jpg"}, {"GameName": "Book of Oz Lock 'N Spin", "GameCode": "SMG_bookOfOzLockNSpin", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/4f53f7eb3b664f9a986ac1716f017a99_smg_bookofozlocknspin_icon_square_600x600_en.jpg"}, {"GameName": "Bookie of Odds", "GameCode": "SMG_bookieOfOdds", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/38a5808133fc450e8a6032e1e6998963_smg_bookieofodds_icon_square_600x600_en.jpg"}, {"GameName": "Boom Pirates", "GameCode": "SMG_boomPirates", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/6462fcafe08344ccace760fa91775fe1_smg_boompirates_icon_square_600x600_en.jpg"}, {"GameName": "Break Away", "GameCode": "SMG_breakAway", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/cad5dd636267444e90d37d6785ec4eba_smg_breakaway_icon_square_600x600_en.jpg"}, {"GameName": "Break Away Deluxe", "GameCode": "SMG_breakAwayDeluxe", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/02bb06e1a93c437eba0bb82bb737b24a_smg_breakawaydeluxe_icon_square_600x600_en.jpg"}, {"GameName": "Break Away Lucky Wilds", "GameCode": "SMG_breakAwayLuckyWilds", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/48b2b8df79684c61a050b027539cbf53_smg_breakawayluckywilds_icon_square_600x600_en.jpg"}, {"GameName": "Break Away Shootout", "GameCode": "SMG_breakAwayShootout", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/f5a8ce1e8f864736bb866b5ac43ce730_smg_breakawayshootout_icon_square_600x600_en.jpg"}, {"GameName": "Break Away Ultra", "GameCode": "SMG_breakAwayUltra", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/93d344f141df418d814eafd0b8f1c91b_smg_breakawayultra_icon_square_600x600_en.jpg"}, {"GameName": "Break Away v90", "GameCode": "SMG_BreakAwayV90", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/6810b58e7a0a499fbcbb3749e8fa1a9a_smg_breakawayv90_icon_square_600x600_en.jpg"}, {"GameName": "Break da Bank", "GameCode": "SMG_breakDaBank", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/ea0b2c791c5740a2bf6c08fbfdb98392_smg_breakdabank_icon_square_600x600_en.jpg"}, {"GameName": "Break da Bank Again", "GameCode": "SMG_breakDaBankAgain", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/52ab322b931d4c54bfb0042dbc0e6bcf_smg_breakdabankagain_icon_square_600x600_en.jpg"}, {"GameName": "Break da Bank Again Respin", "GameCode": "SMG_breakDaBankAgainRespin", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/be8a0c0377a748eca9f93c207dd9a2ef_smg_breakdabankagainrespin_icon_square_600x600_en.jpg"}, {"GameName": "Break Da Bank Again™ MEGAWAYS™", "GameCode": "SMG_breakDaBankAgainMegaways", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/78c43057355a4b56b5677c2bcc46665e_smg_breakdabankagainmegaways_icon_square_600x600_en.jpg"}, {"GameName": "<PERSON><PERSON><PERSON>", "GameCode": "SMG_bubbleBeez", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/44ee92b7dd6f47428f9c9a3a80e3b1ab_smg_bubblebeez_icon_square_300x300_en.png"}, {"GameName": "Burning Desire", "GameCode": "SMG_burningDesire", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/d7efd208d3fd47afb1ace94808bea5e7_smg_burningdesire_icon_square_600x600_en.jpg"}, {"GameName": "Bush Telegraph", "GameCode": "SMG_bushTelegraph", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/c34f10b9605c40a8b6599a288b76697e_smg_bushtelegraph_icon_square_600x600_en.jpg"}, {"GameName": "Bust the Bank", "GameCode": "SMG_bustTheBank", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/0096f81a479e43e28ccd6fc0d6849113_smg_bustthebank_icon_square_600x600_en.jpg"}, {"GameName": "Cairo Link and Win", "GameCode": "SMG_cairoLinkWin", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/0ae663d61a6848208ca5dca562aa03d9_smg_cairolinkwin_icon_square_600x600_en.jpg"}, {"GameName": "Carnaval", "GameCode": "SMG_carnaval", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/eb57b3c9c6bd475f84f643c11b1af6fc_smg_carnaval_icon_square_600x600_en.jpg"}, {"GameName": "Carnaval Jackpot ", "GameCode": "SMG_carnavalJackpot", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/c7d2a2f29558425bada94bb0f421806b_smg_carnavaljackpot_icon_square_600x600_en.jpg"}, {"GameName": "Cash Crazy", "GameCode": "SMG_cashCrazy", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/d54e6ea80455479b897e3cf87c12f0b0_smg_cashcrazy_icon_square_600x600_en.jpg"}, {"GameName": "Cash of Kingdoms", "GameCode": "SMG_cashOfKingdoms", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/fc748d8821f14a0487ba7f71954d79c3_smg_cashofkingdoms_icon_square_600x600_en.jpg"}, {"GameName": "Cashapillar", "GameCode": "SMG_cashapillar", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/328e6221315a41939231bab749eb0c88_smg_cashapillar_icon_square_600x600_en.jpg"}, {"GameName": "Cat Clans", "GameCode": "SMG_catClans", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/49c575f63cfe4c6ea13261e2b9115fa7_smg_catclans_icon_square_600x600_en.jpg"}, {"GameName": "Cats of the Caribbean", "GameCode": "SMG_catsOfTheCaribbean", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/cf3e195e6fcc4109a6568b4cfbae8134_smg_catsofthecaribbean_icon_square_600x600_en.jpg"}, {"GameName": "Centre Court", "GameCode": "SMG_centreCourt", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/b7c71dd36d1b45479759ed789aebbf40_smg_centrecourt_icon_square_600x600_en.jpg"}, {"GameName": "Champions of Olympus", "GameCode": "SMG_championsOfOlympus", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/20230801/561d72677fd94268a7f5739d8ba83b39_smg_ChampionsOfOlympus_icon_square_300x300_en.png"}, {"GameName": "Chicago Gold", "GameCode": "SMG_chicagoGold", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/aad0598aa01044b9aa81676ea1468db0_smg_chicagogold_icon_square_600x600_en.jpg"}, {"GameName": "Chronicles of Olympus X UP", "GameCode": "SMG_chroniclesOfOlympusXUP", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/5154a554a26a48aa845a75fb37d26ce1_smg_chroniclesofolympusxup_icon_square_600x600_en.jpg"}, {"GameName": "Circus Jugglers Jackpots", "GameCode": "SMG_circusJugglersJackpots", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/8e033912951a410682614d87c1888947_smg_circusjugglersjackpots_icon_square_600x600_en.jpg"}, {"GameName": "Classic 243", "GameCode": "SMG_classic243", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/393742fa85af49c6a4f3fb8915b32a79_smg_classic243_icon_square_600x600_en.jpg"}, {"GameName": "<PERSON> - 5 <PERSON><PERSON>", "GameCode": "SMG_coolBuck5Reel", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/dcf8dbfab682479683c0490d29f80bdf_smg_coolbuck5reel_icon_square_600x600_en.jpg"}, {"GameName": "<PERSON>", "GameCode": "SMG_coolWolf", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/0066124f77064066aabc41011ae6230f_smg_coolwolf_icon_square_600x600_en.jpg"}, {"GameName": "Cossacks: The Wild Hunt", "GameCode": "SMG_cossacksTheWildHunt", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/a5c0df215bde4284a74b40146552b7a4_smg_cossacksthewildhunt_icon_square_600x600_en.jpg"}, {"GameName": "Cricket Star", "GameCode": "SMG_cricketStar", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/b6c9fbafe18a470db8ed953d055e9618_smg_cricketstar_icon_square_600x600_en.jpg"}, {"GameName": "Cricket Star Scratch", "GameCode": "SMG_cricketStarScratch", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/6e7f342ac2514887a7ef1356f281a2c8_smg_cricketstarscratch_icon_square_600x600_en.jpg"}, {"GameName": "deadmau5", "GameCode": "SMG_deadmau5", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/c16058d677a8428da0204de1422fdbd9_smg_deadmau5_icon_square_600x600_en.jpg"}, {"GameName": "Deck the Halls", "GameCode": "SMG_deckTheHalls", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/f82f7c6ece254d06bf3d776e4f2a5d70_smg_deckthehalls_icon_square_600x600_en.jpg"}, {"GameName": "Dia del Mariachi Megaways", "GameCode": "SMG_diaDelMariachiMegaways", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/d987aa3530e14cd1976e78e93ccb2aa9_smg_diadelmariachimegaways_icon_square_600x600_en.jpg"}, {"GameName": "Diamond Empire", "GameCode": "SMG_diamondEmpire", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/a90a3112cc4743e38a85c642571f0c82_smg_diamondempire_icon_square_600x600_en.jpg"}, {"GameName": "Diamond King Jackpots", "GameCode": "SMG_diamondKingJackpots", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/42453c1b445247019109d95dd8ba9a37_smg_diamondkingjackpots_icon_square_600x600_en.jpg"}, {"GameName": "Divine Diamonds", "GameCode": "SMG_divine<PERSON><PERSON><PERSON>s", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/271125e0760744c98344c3bc6f5596b7_smg_divinediamonds_icon_square_600x600_en.jpg"}, {"GameName": "<PERSON> Riches <PERSON>", "GameCode": "SMG_divineR<PERSON>s<PERSON><PERSON><PERSON>", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/d8817bd3bdbc4d0ab17118b1d0d3319c_smg_divinericheshelios_icon_square_600x600_en.jpg"}, {"GameName": "Doki Doki Fireworks", "GameCode": "SMG_dokiDokiFireworks", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/d13e6f2e9c5148519ad14e355ac1c9f5_smg_dokidokifireworks_icon_square_600x600_en.jpg"}, {"GameName": "<PERSON><PERSON>", "GameCode": "SMG_dokiDokiParfait", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/2ab0f05df4a343bf8453dfbc9cf99856_smg_dokidokiparfait_icon_square_600x600_en.jpg"}, {"GameName": "Dolphin Coast", "GameCode": "SMG_dolphinCoast", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/4c25ea54e4074b8bb04d576bf4b7cf4c_smg_dolphincoast_icon_square_600x600_en.jpg"}, {"GameName": "Dragon Dance", "GameCode": "SMG_dragonDance", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/12e2850fcdf54d50aba46031f21948d3_smg_dragondance_icon_square_600x600_en.jpg"}, {"GameName": "Dragon Shard", "GameCode": "SMG_dragonShard", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/f7e45737da2940fdb7697b75f6b6583d_smg_dragonshard_icon_square_600x600_en.jpg"}, {"GameName": "Dragon's Breath", "GameCode": "SMG_dragonsBreath", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/b0b32f068c784f2cbef20b98f92c76fc_smg_dragonsbreath_icon_square_600x600_en.jpg"}, {"GameName": "Dragon's Keep", "GameCode": "SMG_dragonsKeep", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/d0d013b52af74b42971ad2f857e3de4d_smg_dragonskeep_icon_square_600x600_en.jpg"}, {"GameName": "Dragonz", "GameCode": "SMG_dragonz", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/f6769e25cdd640999cb0f6507e0ff736_smg_dragonz_icon_square_600x600_en.jpg"}, {"GameName": "Dream Date", "GameCode": "SMG_dreamDate", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/7705a7b718fc4b45b97f9f57aafcaec8_smg_dreamdate_icon_square_600x600_en.jpg"}, {"GameName": "Dungeons and Diamonds", "GameCode": "SMG_dungeonsAndDiamonds", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/35ebe01501fb496e92e660df3f6042b6_smg_dungeonsanddiamonds_icon_square_600x600_en.jpg"}, {"GameName": "Eagle's Wings", "GameCode": "SMG_eaglesWings", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/74a93c5a04bb4c5690174eb2706f8a37_smg_eagleswings_icon_square_600x600_en.jpg"}, {"GameName": "Egyptian Tombs", "GameCode": "SMG_egyptianTombs", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/10b124992c3044b9b57c8de272151a59_smg_egyptiantombs_icon_square_600x600_en.jpg"}, {"GameName": "Emerald Gold", "GameCode": "SMG_emeraldGold", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/124a349bcec04deabec9676a903f7189_smg_emeraldgold_icon_square_600x600_en.jpg"}, {"GameName": "Emperor Of The Sea", "GameCode": "SMG_emperorOfTheSea", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/d07df8c77e4e4e569f12aa93a68a1714_smg_emperorofthesea_icon_square_600x600_en.jpg"}, {"GameName": "Emperor of the Sea Deluxe", "GameCode": "SMG_emperorOfTheSeaDeluxe", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/1c0ebe73dad14e72aea983cb8258222e_smg_emperoroftheseadeluxe_icon_square_600x600_en.jpg"}, {"GameName": "Exotic Cats", "GameCode": "SMG_exoticCats", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/716c391d861643f7a4ad56d2cc8d5eaa_smg_exoticcats_icon_square_600x600_en.jpg"}, {"GameName": "Fire Forge", "GameCode": "SMG_fireForge", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/70653e7977e74b40a9ee198c7596cb61_smg_fireforge_icon_square_600x600_en.jpg"}, {"GameName": "Fish Party", "GameCode": "SMG_fishParty", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/27d57a0b5f244f0daa125e467fcf97f3_smg_fishparty_icon_square_600x600_en.jpg"}, {"GameName": "Fishin' Pots of Gold", "GameCode": "SMG_fishinPotsOfGold", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/67b691b89d35434dbfa0dedc5cb98345_smg_fishinpotsofgold_icon_square_600x600_en.jpg"}, {"GameName": "Flower Fortunes Asia", "GameCode": "SMG_flowerFortunesAsia", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/f754709842ed46cc86699068ca99c6f9_smg_flowerfortunesasia_icon_square_600x600_en.jpg"}, {"GameName": "Football Star", "GameCode": "SMG_footballStar", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/f89951aaaeec46318b95899afe3ab078_smg_footballstar_icon_square_600x600_en.jpg"}, {"GameName": "Football Star Deluxe", "GameCode": "SMG_footballStarDeluxe", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/10778703cbe944bbbee5c537f1556841_smg_footballstardeluxe_icon_square_600x600_en.jpg"}, {"GameName": "Forbidden Throne", "GameCode": "SMG_forbiddenThrone", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/d3fee11dc6e94969a74f2bff13aae191_smg_forbiddenthrone_icon_square_600x600_en.jpg"}, {"GameName": "Forgotten Island", "GameCode": "SMG_forgottenIsland", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/5f254bbbf1324f8db0c77d4e29e39b5e_smg_forgottenisland_icon_square_600x600_en.jpg"}, {"GameName": "Fortune Girl", "GameCode": "SMG_fortuneGirl", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/54e10cba711f44f7b66e2aea778753ce_smg_fortunegirl_icon_square_600x600_en.jpg"}, {"GameName": "Fortune Pike Gold", "GameCode": "SMG_fortunePikeGold", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/387ddcc4688148dfbb4170e3ce16195d_smg_FortunePikeGold_icon_square_300x300_en.png"}, {"GameName": "Fortune Rush", "GameCode": "SMG_fortuneRush", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/f3ec12318ac94643939aaf57584a448a_smg_fortunerush_icon_square_600x600_en.jpg"}, {"GameName": "Fortunium", "GameCode": "SMG_fortunium", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/1e5c17e4716f414fa4f390eaafb0a5b8_smg_fortunium_icon_square_600x600_en.jpg"}, {"GameName": "Frozen Diamonds", "GameCode": "SMG_frozenDiamonds", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/ebb95331c5354377bcb72a580a56a6d0_smg_frozendiamonds_icon_square_600x600_en.jpg"}, {"GameName": "Fruit Blast", "GameCode": "SMG_fruitBlast", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/59f18af1292b4fa883d90b4b957d39ae_smg_fruitblast_icon_square_600x600_en.jpg"}, {"GameName": "Fruit vs Candy", "GameCode": "SMG_fruitVSCandy", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/fe6373ef03c74f1f9cc822765820ab48_smg_fruitvscandy_icon_square_600x600_en.jpg"}, {"GameName": "Gems And Dragons", "GameCode": "SMG_gemsAndDragons", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/4088aa07fefa4383947138e5e85d5475_smg_gemsanddragons_icon_square_600x600_en.jpg"}, {"GameName": "Gems Odyssey", "GameCode": "SMG_gemsOdyssey", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/eaf523e6fbfb4ecca0c5496c17b8c28b_smg_gemsodyssey_icon_square_600x600_en.jpg"}, {"GameName": "Girls With Guns - Jungle Heat", "GameCode": "SMG_girlsWithGunsJungleHeat", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/12023a59d893421d84e3cd842c44f430_smg_girlswithgunsjungleheat_icon_square_600x600_en.jpg"}, {"GameName": "Gold Factory", "GameCode": "SMG_goldFactory", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/897a817e3d204149be35465316113e9e_smg_goldfactory_icon_square_600x600_en.jpg"}, {"GameName": "Golden Era", "GameCode": "SMG_goldenEra", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/c520f84224f340eca210dd59d757a700_smg_goldenera_icon_square_600x600_en.jpg"}, {"GameName": "Golden Princess", "GameCode": "SMG_goldenPrincess", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/c0c6cdbfc87941d198ee2912e59c3f2f_smg_goldenprincess_icon_square_600x600_en.jpg"}, {"GameName": "Golden Stallion", "GameCode": "SMG_goldenStallion", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/1f6fc71103d5487c8e31a25a232f8b9e_smg_goldenstallion_icon_square_600x600_en.jpg"}, {"GameName": "<PERSON><PERSON>", "GameCode": "SMG_gopherGold", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/38a3cce3bf6043caad2fb73c5435133a_smg_gophergold_icon_square_600x600_en.jpg"}, {"GameName": "Granny Vs Zombies", "GameCode": "SMG_grannyVsZombies", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/b481968e55854c158d3c003a7f5ee817_smg_GrannyvsZombies_icon_square_300x300_en.png"}, {"GameName": "Halloweenies", "GameCode": "SMG_halloweenies", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/1a6f0c2fff8f4fde9ff1844216c9e0ce_smg_halloweenies_icon_square_600x600_en.jpg"}, {"GameName": "Happy Holidays", "GameCode": "SMG_HappyHolidays", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/2e9693ce06b34c7fa811ac95abca3b4a_smg_happyholidays_icon_square_600x600_en.jpg"}, {"GameName": "Happy Lucky Cats", "GameCode": "SMG_happyLuckyCats", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/383d810fb6884b00a01f2458f4e74822_smg_happyluckycats_icon_square_600x600_en.jpg"}, {"GameName": "Happy Monster Claw", "GameCode": "SMG_happyMonsterClaw", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/58cc695cdb0f49249e603070c40bbd35_smg_happymonsterclaw_icon_square_600x600_en.jpg"}, {"GameName": "High Society", "GameCode": "SMG_highSociety", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/6bd03d36dedb43209cfed63195d783ab_smg_highsociety_icon_square_600x600_en.jpg"}, {"GameName": "<PERSON>", "GameCode": "SMG_holly<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/bef7973a33384742a12e6886cc269d98_smg_hollyjollypenguins_icon_square_600x600_en.jpg"}, {"GameName": "<PERSON><PERSON> - The Yellow Emperor", "GameCode": "SMG_huangdiTheYellowEmperor", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/5563f7cedd1b4465a5c6ee7a1c8b346a_smg_huangditheyellowemperor_icon_square_600x600_en.jpg"}, {"GameName": "Hyper Star", "GameCode": "SMG_hyperStar", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/77b64a54ede6434cb90cde29803b9852_smg_hyperstar_icon_square_600x600_en.jpg"}, {"GameName": "Hyper Strike HyperSpins", "GameCode": "SMG_hyperStrikeHyperSpins", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/5ed9fe3a77bc4efababaac426c15da64_smg_hyperstrikehyperspins_icon_square_600x600_en.jpg"}, {"GameName": "Hyper Strike™", "GameCode": "SMG_hyperStrike", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/b3102ea2b16f4d8eadebd9164111eeab_smg_hyperstrike_icon_square_600x600_en.jpg"}, {"GameName": "Immortal Romance", "GameCode": "SMG_immortalRomance", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/d8c28e1bf7604fc8a04ddbfc5f0f755c_smg_immortalromance_icon_square_600x600_en.jpg"}, {"GameName": "Immortal Romance v90", "GameCode": "SMG_ImmortalRomancev90", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/88fa852c620c4f80bdf689380f1cb738_smg_immortalromancev90_icon_square_600x600_en.jpg"}, {"GameName": "Immortal Romance Video Bingo", "GameCode": "SMG_immortalRomanceVideoBingo", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/909b0750c42844cf9999beda2d531408_smg_immortalromancevideobingo_icon_square_600x600_en.jpg"}, {"GameName": "Incan Adventure", "GameCode": "SMG_incanAdventure", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/6ec38e23639e4b8dacf6ec9207db2224_smg_incanadventure_icon_square_600x600_en.jpg"}, {"GameName": "Ingots of Cai Shen", "GameCode": "SMG_ingotsOfCaiShen", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/27c793f47fcb4369945316c455be96cb_smg_ingotsofcaishen_icon_square_600x600_en.jpg"}, {"GameName": "<PERSON>", "GameCode": "SMG_jadeShuriken", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/42ad8573b30f4b1a9accd19a5eef212b_smg_jadeshuriken_icon_square_600x600_en.jpg"}, {"GameName": "Joyful Joker Megaways", "GameCode": "SMG_joyfulJoker", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/bd9355549cff4a58a9b751bfe3425242_smg_joyfuljoker_icon_square_600x600_en.jpg"}, {"GameName": "Jungle Jim - <PERSON>", "GameCode": "SMG_jungleJimElDorado", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/f2853b8256654200a5624f2bd885ea60_smg_junglejimeldorado_icon_square_600x600_en.jpg"}, {"GameName": "<PERSON> and the Lost Sphinx", "GameCode": "SMG_jungleJimAndTheLostSphinx", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/f00fc19966ed4720a3c4a0641f062cb2_smg_junglejimandthelostsphinx_icon_square_600x600_en.jpg"}, {"GameName": "Jurassic Park Gold", "GameCode": "SMG_jurassicParkGold", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/89f191963f2f42b5837e3534412a170f_smg_jurassicparkgold_icon_square_600x600_en.jpg"}, {"GameName": "Jurassic World", "GameCode": "SMG_jurassicWorld", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/e9c2f6b184a74c70852c287ec67cc05b_smg_jurassicworld_icon_square_600x600_en.jpg"}, {"GameName": "Jurassic World Raptor Riches", "GameCode": "SMG_jurassicWorldRaptorRiches", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/88781d1a088b43b1b60af5cada97bc25_smg_jurassicworldraptorriches_icon_square_600x600_en.jpg"}, {"GameName": "Karaoke Party", "GameCode": "SMG_karaokeParty", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/b6ca2205f8d04d47b350b131690d6cad_smg_karaokeparty_icon_square_600x600_en.jpg"}, {"GameName": "Kathman<PERSON>", "GameCode": "SMG_kathmandu", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/d4af9dd5d3fe44c39422afa65a5d9c93_smg_kathmandu_icon_square_600x600_en.jpg"}, {"GameName": "King <PERSON><PERSON>", "GameCode": "SMG_kingTusk", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/b2c5d6527ec24bfcbc0422c54550531d_smg_kingtusk_icon_square_600x600_en.jpg"}, {"GameName": "Kings Of Cash", "GameCode": "SMG_kingsOfCash", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/09e7c441ee3544c3a7a85772a932360f_smg_kingsofcash_icon_square_600x600_en.jpg"}, {"GameName": "Kings of Crystals", "GameCode": "SMG_kingsOfCrystals", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/5fd5a62a23b244b5b745571e84b3c46b_smg_kingsofcrystals_icon_square_600x600_en.jpg"}, {"GameName": "Kitsune Adventure", "GameCode": "SMG_kitsuneAdventure", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/c4b230fef78e4c54960850c47f2eb597_smg_kitsuneadventure_icon_square_600x600_en.jpg"}, {"GameName": "Kodiak Kingdom", "GameCode": "SMG_kodiakKingdom", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/c36bec13b9244e68b040428572f8b757_smg_kodiakkingdom_icon_square_600x600_en.jpg"}, {"GameName": "Ladies Nite", "GameCode": "SMG_ladiesNite", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/15fee050df974a569a90dceda8f180e0_smg_ladiesnite_icon_square_600x600_en.jpg"}, {"GameName": "Ladies Nite 2 Turn Wild", "GameCode": "SMG_ladiesNite2TurnWild", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/2740b5808ca64b8581dafc265dab16b6_smg_ladiesnite2turnwild_icon_square_600x600_en.jpg"}, {"GameName": "Ladies Nite v90", "GameCode": "SMG_LadiesNiteV90", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/6ac7f7a6b49342ff8b2fa4a4b655346a_smg_ladiesnitev90_icon_square_600x600_en.jpg"}, {"GameName": "<PERSON>: Temples and Tombs", "GameCode": "SMG_laraCroftTemplesAndTombs", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/0d22f6083bf94c2dbe881dabd503cc91_smg_laracrofttemplesandtombs_icon_square_600x600_en.jpg"}, {"GameName": "Legacy of Oz ™", "GameCode": "SMG_legacyOfOz", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/53f02c5c5b254e81bc05eab98caa322e_smg_legacyofoz_icon_square_600x600_en.jpg"}, {"GameName": "Legend of the Moon Lovers", "GameCode": "SMG_LegendOftheMoonLovers", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/3b84bf9d40954e71859a6ce79aaf08dd_smg_legendofthemoonlovers_icon_square_600x600_en.jpg"}, {"GameName": "Life Of Riches", "GameCode": "SMG_lifeOfRiches", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/d933600fa40e42439d11eac4273de23c_smg_lifeofriches_icon_square_600x600_en.jpg"}, {"GameName": "Lightning Fortunes", "GameCode": "SMG_lightningFortunes", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/e5806783fa654c7eaa7fd3bd9da7758d_smg_lightningfortunes_icon_square_600x600_en.jpg"}, {"GameName": "Lion's Pride", "GameCode": "SMG_lionsPride", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/8718491f5379434bba102f1380e90132_smg_lionspride_icon_square_600x600_en.jpg"}, {"GameName": "Liquid Gold", "GameCode": "SMG_liquidGold", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/84ee95f8cb78431abd174a3bb02a8f5e_smg_liquidgold_icon_square_600x600_en.jpg"}, {"GameName": "Loaded", "GameCode": "SMG_loaded", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/d445f0d280c24d8c8adaeab16bb30258_smg_loaded_icon_square_600x600_en.jpg"}, {"GameName": "Long Mu Fortunes", "GameCode": "SMG_longMuFortunes", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/41aa55a9ec8849e3b3cd59b4de779dae_smg_longmufortunes_icon_square_600x600_en.jpg"}, {"GameName": "Lost Vegas", "GameCode": "SMG_lostVegas", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/1395f634303a4ff08bae94c08be17569_smg_lostvegas_icon_square_600x600_en.jpg"}, {"GameName": "Lucha Legends", "GameCode": "SMG_luchaLegends", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/a0884fe03b9942049e1da69cc3bafc47_smg_luchalegends_icon_square_600x600_en.jpg"}, {"GameName": "Lucky Bachelors", "GameCode": "SMG_lucky<PERSON><PERSON><PERSON>s", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/7f699cdd2853482994c5b4c01864cd7d_smg_luckybachelors_icon_square_600x600_en.jpg"}, {"GameName": "<PERSON>", "GameCode": "SMG_luckyClucks", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/8640f8021c734066b5778f6330077e2b_smg_luckyclucks_icon_square_600x600_en.jpg"}, {"GameName": "Lucky Firecracker", "GameCode": "SMG_luckyfirecracker", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/20230420/db749e4b5a984473ab4517cd9ee2ca4c_smg_LuckyFirecracker_icon_square_300x300_en.png"}, {"GameName": "<PERSON>", "GameCode": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/6fb6143535654a24801c3b8c30b729e4_smg_luckykoi_icon_square_600x600_en.jpg"}, {"GameName": "<PERSON>", "GameCode": "SMG_luckyLeprechaun", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/07a461b9b061441680e703ba57547360_smg_luckyleprechaun_icon_square_600x600_en.jpg"}, {"GameName": "<PERSON> Clusters", "GameCode": "SMG_luckyLeprechaunClusters", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/0d6a7fc0b8c54ab59d845dd5d37f3359_smg_luckyleprechaunclusters_icon_square_600x600_en.jpg"}, {"GameName": "<PERSON> Little <PERSON>", "GameCode": "SMG_luckyLittleGods", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/cb50991334724214b85c5f88fc89f60d_smg_luckylittlegods_icon_square_600x600_en.jpg"}, {"GameName": "Lucky Riches Hyperspins", "GameCode": "SMG_luckyRichesHyperspins", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/147f4e4b2aa44d84b6f74c33aac70dfd_smg_luckyricheshyperspins_icon_square_600x600_en.jpg"}, {"GameName": "Lucky Twins", "GameCode": "SMG_luckyTwins", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/ee6f9035b7da47d0b033979823c53392_smg_luckytwins_icon_square_600x600_en.jpg"}, {"GameName": "Lucky Twins Catcher", "GameCode": "SMG_luckyT<PERSON>sCatcher", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/ab39713fa401440aa5fb72d4995663e7_smg_luckytwinscatcher_icon_square_600x600_en.jpg"}, {"GameName": "Lucky Twins Jackpot", "GameCode": "SMG_luckyTwinsJackpot", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/696a57f92f9742888744a2a372a41ee7_smg_luckytwinsjackpot_icon_square_600x600_en.jpg"}, {"GameName": "<PERSON>", "GameCode": "<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/708ea9d8cb25447b94797d282c14a56e_smg_luckyzodiac_icon_square_600x600_en.jpg"}, {"GameName": "Magic of Sahara", "GameCode": "SMG_magicOfSahara", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/0410c465b0d04cf1a101cb4dfbce9044_smg_magicofsahara_icon_square_600x600_en.jpg"}, {"GameName": "Mask of <PERSON><PERSON>", "GameCode": "SMG_maskOfAmun", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/cd99f3e11b7c4bbea46ebe08ccdaed2e_smg_maskofamun_icon_square_600x600_en.jpg"}, {"GameName": "Masters of Valhalla", "GameCode": "SMG_mastersOfValhalla", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/771dde3b76b6433788a807cc64c79aec_smg_mastersofvalhalla_icon_square_600x600_en.jpg"}, {"GameName": "<PERSON><PERSON>", "GameCode": "SMG_mauiMischief", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/4a813545daf44b75b43ed793f9007f2f_smg_mauimischief_icon_square_600x600_en.jpg"}, {"GameName": "<PERSON> and the Alien Attack", "GameCode": "SMG_maxDamageArcade", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/2d1f0d700615408b9b7be00c65e54741_smg_maxdamagearcade_icon_square_600x600_en.jpg"}, {"GameName": "Mayan Princess", "GameCode": "SMG_mayanPrincess", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/91a8603e4bea4e5299551b6c642c5219_smg_mayanprincess_icon_square_600x600_en.jpg"}, {"GameName": "Mega Money Multiplier", "GameCode": "SMG_megaMoneyMultiplier", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/e6d9956980154a9ca25afbc223993f78_smg_megamoneymultiplier_icon_square_600x600_en.jpg"}, {"GameName": "Mermaids Millions", "GameCode": "SMG_mermaidsMillions", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/fffeac044f9340a599eff2e203a6722b_smg_mermaidsmillions_icon_square_600x600_en.jpg"}, {"GameName": "Monkey <PERSON>", "GameCode": "SMG_monkeyBonanza", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/20230417/84e6034dc06a48d6a4ac99eec9e84632_smg_MonkeyBonanza_icon_square_300x300_en.png"}, {"GameName": "Monster Blast", "GameCode": "SMG_monsterBlast", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/aef6ef5249b147f5adf7603f4197d8fc_smg_monsterblast_icon_square_600x600_en.jpg"}, {"GameName": "Mystic Dreams", "GameCode": "SMG_mysticDreams", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/290524eb340c4179b581c2c62bfb642b_smg_mysticdreams_icon_square_600x600_en.jpg"}, {"GameName": "Neptune's Riches: Ocean of Wilds", "GameCode": "SMG_neptunesRichesOceanOfWilds", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/5cc76f4832be4a019dfa8f58abb7e90b_smg_neptunesrichesoceanofwilds_icon_square_600x600_en.jpg"}, {"GameName": "Noble Sky", "GameCode": "SMG_nobleSky", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/f27042d112794d6a9f111646eb4e0383_smg_noblesky_icon_square_600x600_en.jpg"}, {"GameName": "<PERSON><PERSON>'s Riches", "GameCode": "SMG_odinsRiches", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/73d4238d3364489ebae22cf92de811b8_smg_odinsriches_icon_square_600x600_en.jpg"}, {"GameName": "<PERSON><PERSON>", "GameCode": "SMG_oniHunter", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/9d2fc2e0be9e4f2d870bd0c40d9a64eb_smg_onihunter_icon_square_600x600_en.jpg"}, {"GameName": "<PERSON><PERSON>", "GameCode": "SMG_oniHunterNightSakura", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/c6a440e43aa24b1eaa56be429205f762_smg_onihunternightsakura_icon_square_600x600_en.jpg"}, {"GameName": "Oni Hunter Plus", "GameCode": "SMG_oniHunterPlus", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/1b5654119b4b4c87898db2a6f0cbafe2_smg_onihunterplus_icon_square_600x600_en.jpg"}, {"GameName": "Our Days", "GameCode": "SMG_ourDaysA", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/533fa76698c8426185032383b3814516_smg_ourdaysa_icon_square_600x600_en.jpg"}, {"GameName": "<PERSON><PERSON> Up™", "GameCode": "SMG_pileEmUp", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/9ef7c656431b4b828a568ab70851bac3_smg_pileemup_icon_square_600x600_en.jpg"}, {"GameName": "Pistoleras", "GameCode": "SMG_pistoleras", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/1b5aa79eaa64447d8d1e031fe986381b_smg_pistoleras_icon_square_600x600_en.jpg"}, {"GameName": "Playboy", "GameCode": "SMG_playboy", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/064048aad397422fa91e54da084c4ae6_smg_playboy_icon_square_600x600_en.jpg"}, {"GameName": "Playboy Fortunes", "GameCode": "SMG_playboyFortunes", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/9bee9aaac0eb493583b68c6f29c116c5_smg_playboyfortunes_icon_square_600x600_en.jpg"}, {"GameName": "Playboy Gold", "GameCode": "SMG_playboyGold", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/334aad57541746c5b9849249cb0a58ab_smg_playboygold_icon_square_600x600_en.jpg"}, {"GameName": "Playboy Gold Jackpots", "GameCode": "SMG_playboyGoldJackpots", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/d09dba8f14584483b802f7d3e531f001_smg_playboygoldjackpots_icon_square_600x600_en.jpg"}, {"GameName": "Playboy Wilds", "GameCode": "SMG_playboyWilds", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/6241a7538582430ca26232e79871737a_smg_playboywilds_icon_square_600x600_en.jpg"}, {"GameName": "Pure Platinum", "GameCode": "SMG_purePlatinum", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/484f7a28d8f0416bac7e1344bf84853b_smg_pureplatinum_icon_square_600x600_en.jpg"}, {"GameName": "Queen of Alexandria™", "GameCode": "SMG_queenofAlexandria", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/99e15b1a3187470691f0cce25b5cda49_smg_queenofalexandria_icon_square_600x600_en.jpg"}, {"GameName": "Queen of the Crystal Rays", "GameCode": "SMG_queenOfTheCrystalRays", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/7a8c1e93171640bfb299079cd953f434_smg_queenofthecrystalrays_icon_square_600x600_en.jpg"}, {"GameName": "Rabbit In The Hat", "GameCode": "SMG_rabbitinthehat", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/873ded2befa64c7fb0d5302665f22442_smg_rabbitinthehat_icon_square_600x600_en.jpg"}, {"GameName": "Reel Gems", "GameCode": "SMG_reelGems", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/7cf66403aa0f4840aea7ae51ce190a52_smg_reelgems_icon_square_600x600_en.jpg"}, {"GameName": "Reel Gems Deluxe", "GameCode": "SMG_reelGemsDeluxe", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/7373474b74154b6b90f97dbefe5cb791_smg_reelgemsdeluxe_icon_square_600x600_en.jpg"}, {"GameName": "<PERSON><PERSON>", "GameCode": "SMG_reelSpinner", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/6f36f80c8dcd462d876c5947e3d74fc9_smg_reelspinner_icon_square_600x600_en.jpg"}, {"GameName": "Reel Strike", "GameCode": "SMG_reelStrike", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/038a838fb0614c07a7abcc242d438b95_smg_reelstrike_icon_square_600x600_en.jpg"}, {"GameName": "<PERSON>el <PERSON>", "GameCode": "SMG_ReelTalent", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/8e61dafc98d94fd198d8efb5fd119872_smg_reeltalent_icon_square_600x600_en.jpg"}, {"GameName": "Reel Thunder", "GameCode": "SMG_reelThunder", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/495228d9627e487e90916fd3a9ff8707_smg_reelthunder_icon_square_600x600_en.jpg"}, {"GameName": "Relic Seekers", "GameCode": "SMG_relicSeekers", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/88cd48d7076c41578fc9644f8938335c_smg_relicseekers_icon_square_600x600_en.jpg"}, {"GameName": "<PERSON><PERSON>", "GameCode": "SMG_retroReels", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/741045efb4d6408c8178ab22231f0a73_smg_retroreels_icon_square_600x600_en.jpg"}, {"GameName": "<PERSON><PERSON> - Diamond Glitz", "GameCode": "SMG_retroReelsDiamondGlitz", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/3e7b9902534d471d9496d1b6a7694ca8_smg_retroreelsdiamondglitz_icon_square_600x600_en.jpg"}, {"GameName": "<PERSON><PERSON> - Extreme Heat", "GameCode": "SMG_retroReelsExtremeHeat", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/d29aa70cc0d840d29130067b4870839c_smg_retroreelsextremeheat_icon_square_600x600_en.jpg"}, {"GameName": "<PERSON><PERSON><PERSON><PERSON>", "GameCode": "SMG_rhymingReelsGeorgiePorgie", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/1426a03bbf654bf4b7bca0988a734e9b_smg_rhymingreelsgeorgieporgie_icon_square_600x600_en.jpg"}, {"GameName": "Rhyming Reels Hearts And Tarts", "GameCode": "SMG_rhymingReelsHeartsAndTarts", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/f88c2ec78b194f14961ba4483bb29edd_smg_rhymingreelsheartsandtarts_icon_square_600x600_en.jpg"}, {"GameName": "Riviera Riches", "GameCode": "SMG_rivieraRiches", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/dfe67fb21536454cb8e4815dd63746a2_smg_rivierariches_icon_square_600x600_en.jpg"}, {"GameName": "<PERSON>'s Heroes", "GameCode": "SMG_robinHoodsHeroes", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/7d0126f53eda47bb8b25bbdd4541d5bf_smg_robinhoodsheroes_icon_square_600x600_en.jpg"}, {"GameName": "<PERSON><PERSON>", "GameCode": "SMG_romanovRiches", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/f430943d13124c929a8aba3d8862f4e7_smg_romanovriches_icon_square_600x600_en.jpg"}, {"GameName": "Rugby Star", "GameCode": "SMG_rugbyStar", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/20230417/b95217b7294440f8bdca688d9a8e97c8_smg_rugbystar_icon_square_300x300_en.png"}, {"GameName": "Rugby Star Deluxe", "GameCode": "SMG_rugbyStarDeluxe", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/20230417/ae7af1d8420645018fbe6ee9e8fd1c41_smg_rugbystardeluxe_icon_square_300x300_en.jpg"}, {"GameName": "Santa's Wild Ride", "GameCode": "SMG_santasWildRide", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/44fc1092d0e6474f90631a025f6c15d8_smg_santaswildride_icon_square_600x600_en.jpg"}, {"GameName": "Scrooge", "GameCode": "SMG_scrooge", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/2dda6a5705c24611a40e7a68e33104bd_smg_scrooge_icon_square_600x600_en.jpg"}, {"GameName": "Secret Admirer", "GameCode": "SMG_secretAdmirer", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/5cca456ebe91411e899787dc8657ecaa_smg_secretadmirer_icon_square_600x600_en.jpg"}, {"GameName": "Secret Romance", "GameCode": "SMG_secretRomance", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/ec4d6fbaf360460e879bcc544a11058e_smg_secretromance_icon_square_600x600_en.jpg"}, {"GameName": "Serengeti Gold", "GameCode": "SMG_serengetiGold", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/3747bf33959c47c7bef0e3820ea8dcdf_smg_serengetigold_icon_square_600x600_en.jpg"}, {"GameName": "Shamrock Holmes Megaways™", "GameCode": "SMG_shamrockHolmes", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/96fb229134c84a04868038f026f12084_smg_shamrockholmes_icon_square_600x600_en.jpg"}, {"GameName": "Shanghai Beauty", "GameCode": "SMG_shanghaiBeauty", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/3b6dbec773f14be293d9758b987a7a44_smg_shanghaibeauty_icon_square_600x600_en.jpg"}, {"GameName": "Sherlock Of London Online Slot", "GameCode": "SMG_sherlockOfLondonOnlineSlot", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/d07865feb64f411bbcb0902653c38ac7_smg_sherlockoflondononlineslot_icon_square_600x600_en.jpg"}, {"GameName": "Shogun of Time", "GameCode": "SMG_shogunofTime", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/e079d47d56aa497cb4dd7a6ca9d4ceb8_smg_shogunoftime_icon_square_600x600_en.jpg"}, {"GameName": "Showdown Saloon", "GameCode": "SMG_showdownSaloon", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/533edb47c39c4411bca18be4ddf1597c_smg_showdownsaloon_icon_square_600x600_en.jpg"}, {"GameName": "Silver Fang", "GameCode": "SMG_silverFang", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/a76e3925e3824097851ffa8ea5884ffe_smg_silverfang_icon_square_600x600_en.jpg"}, {"GameName": "Silver Seas", "GameCode": "SMG_silverSeas", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/61447756e3294e05a3fea7877efdb349_smg_silverseas_icon_square_600x600_en.jpg"}, {"GameName": "Silverback Multiplier Mountain", "GameCode": "SMG_silverbackMultiplierMountain", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/98fb144b901f492893d07facc51126d9_smg_silverbackmultipliermountain_icon_square_600x600_en.jpg"}, {"GameName": "Sisters of Oz: Jackpots", "GameCode": "SMG_sistersofOzJackpots", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/613db3eab31d4b089571174165af184c_smg_sistersofozjackpots_icon_square_600x600_en.jpg"}, {"GameName": "Spin Spin Sugar", "GameCode": "SMG_spinSpinSugar", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/20230515/9cd3edc30960466498cac66675765d47_smg_SpinSpinSugar_icon_square_300x300_en.png"}, {"GameName": "Solar Wilds", "GameCode": "SMG_solarWilds", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/4cbf9d81ccfe4fa59b851ad47eca4798_smg_solarwilds_icon_square_600x600_en.jpg"}, {"GameName": "Sonic Links", "GameCode": "SMG_sonicLinks", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/585108e8291a4719aa8d8a4f588416a5_smg_soniclinks_icon_square_600x600_en.jpg"}, {"GameName": "Spring Break", "GameCode": "SMG_springBreak", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/f1e6e63baef24b93bd02d252902876f6_smg_springbreak_icon_square_600x600_en.jpg"}, {"GameName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "GameCode": "SMG_squealinRiches", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/2e8f9ee0ad0947898653b46e876f6610_smg_squealinriches_icon_square_600x600_en.jpg"}, {"GameName": "Stardust", "GameCode": "SMG_stardust", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/0a856bebea844d96b1ed053de5acf3c9_smg_stardust_icon_square_600x600_en.jpg"}, {"GameName": "Starlight Kiss", "GameCode": "SMG_starlightKiss", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/fddcd42f5b4549ea9b08be0791df82f3_smg_starlightkiss_icon_square_600x600_en.jpg"}, {"GameName": "Starlite Fruits™", "GameCode": "SMG_starliteFruits", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/b98769213de44335afa6a287676015ea_smg_starlitefruits_icon_square_600x600_en.jpg"}, {"GameName": "Stash of the Titans", "GameCode": "SMG_stashOfTheTitans", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/71714b9e18d54e6faa867dc1bac44fe8_smg_stashofthetitans_icon_square_600x600_en.jpg"}, {"GameName": "Sterling Silver", "GameCode": "SMG_sterlingSilver", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/f9bab00b6f214d1f8bc5a768e5a6d733_smg_sterlingsilver_icon_square_600x600_en.jpg"}, {"GameName": "Sugar Parade", "GameCode": "SMG_sugarParade", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/24e8a9a444684c5ba164a8ae27e56a5d_smg_sugarparade_icon_square_600x600_en.jpg"}, {"GameName": "Summertime", "GameCode": "SMG_summertime", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/69934bb3c29643ab9551cb363d87b7e9_smg_summertime_icon_square_600x600_en.jpg"}, {"GameName": "Sun Quest", "GameCode": "SMG_sunQuest", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/5b30bb98a2a94d06916cce321e2c03d8_smg_sunquest_icon_square_600x600_en.jpg"}, {"GameName": "SunTide", "GameCode": "SMG_sunTide", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/6bb4fe0e74e24de19871fbd9130f573d_smg_suntide_icon_square_600x600_en.jpg"}, {"GameName": "Sure Win", "GameCode": "SMG_sureWin", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/23c14cfec4b741408ca4d7c879cf9c7f_smg_surewin_icon_square_600x600_en.jpg"}, {"GameName": "<PERSON><PERSON>", "GameCode": "SMG_tallyHo", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/cbe647700a95474787a1575e6cc86202_smg_tallyho_icon_square_600x600_en.jpg"}, {"GameName": "<PERSON>", "GameCode": "SMG_tarzan", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/a6c20f41f2e340a8bc745ddc1d174f9b_smg_tarzan_icon_square_600x600_en.jpg"}, {"GameName": "TARZAN® and the Jewels of Opar", "GameCode": "SMG_tarzanAndtheJewelsofOpar", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/8208ac2ab9054a25b1ca28c2678e1ccb_smg_tarzanandthejewelsofopar_icon_square_600x600_en.jpg"}, {"GameName": "Tasty Street", "GameCode": "SMG_tastyStreet", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/b6e4a766012d4309826904d00df6838a_smg_tastystreet_icon_square_600x600_en.jpg"}, {"GameName": "The Finer Reels of Life", "GameCode": "SMG_theFinerReelsOfLife", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/a0daa9fa93934ab0a445c40184245697_smg_thefinerreelsoflife_icon_square_600x600_en.jpg"}, {"GameName": "The Grand Journey", "GameCode": "SMG_theGrandJourney", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/c20fceb8f592488d8e8f7a8961325f27_smg_thegrandjourney_icon_square_600x600_en.jpg"}, {"GameName": "The Great Albini", "GameCode": "SMG_theGreatAlbini", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/e0e22b5011b34fa7aa517a8289ed0dc5_smg_thegreatalbini_icon_square_600x600_en.jpg"}, {"GameName": "The Great Albini 2", "GameCode": "SMG_theGreatAlbini2", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/374c1d5c550848dbbe43714a1d1d1a68_smg_thegreatalbini2_icon_square_600x600_en.jpg"}, {"GameName": "The Heat Is On", "GameCode": "SMG_theHeatIsOn", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/f42a58deb6c6479eb864752ed4a01a69_smg_theheatison_icon_square_600x600_en.jpg"}, {"GameName": "The Incredible Balloon Machine", "GameCode": "SMG_theIncredibleBalloonMachine", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/289aca554f2f4a1fb581a2c275b3d478_smg_theincredibleballoonmachine_icon_square_600x600_en.jpg"}, {"GameName": "The Phantom of the Opera", "GameCode": "SMG_thePhantomOfTheOpera", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/a7da44395e294d3087c34f0419e374ab_smg_thephantomoftheopera_icon_square_600x600_en.jpg"}, {"GameName": "The Rat Pack", "GameCode": "SMG_theRatPack", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/b3027e4850014de999dceb9f49dc0837_smg_theratpack_icon_square_600x600_en.jpg"}, {"GameName": "The Twisted Circus", "GameCode": "SMG_theTwistedCircus", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/b237483fcc234eb784ee2111717857a2_smg_thetwistedcircus_icon_square_600x600_en.jpg"}, {"GameName": "Thunderstruck", "GameCode": "SMG_thunderstruck", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/8257c61d6657440d8867f965f19446a8_smg_thunderstruck_icon_square_600x600_en.jpg"}, {"GameName": "ThunderStruck II", "GameCode": "SMG_thunderstruck2", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/152f7f6b93f6493a843244fa7fc7fcf0_smg_thunderstruck2_icon_square_600x600_en.jpg"}, {"GameName": "Thunderstruck Wild Lightning", "GameCode": "SMG_thunderstruckWildLightning", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/666f407fb9d54450ab91607bc73d58ce_smg_thunderstruckwildlightning_icon_square_600x600_en.jpg"}, {"GameName": "Tiger's Eye", "GameCode": "SMG_tigersEye", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/756c8910cc7645aaa30e9d616113c45c_smg_tigerseye_icon_square_600x600_en.jpg"}, {"GameName": "<PERSON>iki <PERSON>", "GameCode": "SMG_tikiReward", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/8e11cd361dd1406691b6b5bd8328b989_smg_tikireward_icon_square_600x600_en.jpg"}, {"GameName": "Timelines", "GameCode": "SMG_timeLines", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/ef6d6d02118e4aa38cc2f4493e2e9811_smg_timelines_icon_square_600x600_en.jpg"}, {"GameName": "Titans of the Sun - Hyperion", "GameCode": "SMG_titansOfTheSunHyperion", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/36d3c2b0b3e148e1a0069255dd451e0c_smg_titansofthesunhyperion_icon_square_600x600_en.jpg"}, {"GameName": "Titans of the Sun - Theia", "GameCode": "SMG_titansOfTheSunTheia", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/e6f0c7c56e4d4c868c259fa86d4a7b10_smg_titansofthesuntheia_icon_square_600x600_en.jpg"}, {"GameName": "Tomb Raider", "GameCode": "SM<PERSON>_<PERSON><PERSON><PERSON>er", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/8a03c91a8fd84a84998d34be1e89d8ca_smg_tombraider_icon_square_600x600_en.jpg"}, {"GameName": "Tomb Raider Secret of the Sword", "GameCode": "SMG_RubyTombRaiderII", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/1f8412fc47d840ec8afa9eb907a947d9_smg_rubytombraiderii_icon_square_600x600_en.jpg"}, {"GameName": "Treasure Dash", "GameCode": "SMG_treasureDash", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/2ce3f352781f4b4187db5ee3bcd82a61_smg_treasuredash_icon_square_600x600_en.jpg"}, {"GameName": "Treasure Palace", "GameCode": "SMG_treasurePalace", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/517f5c364a6e4feeaa73a5485cd75d52_smg_treasurepalace_icon_square_600x600_en.jpg"}, {"GameName": "Treasures of Kilauea", "GameCode": "SMG_treasuresOfKilauea", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/a7ebb888367e4613b17120c21f446f66_smg_treasuresofkilauea_icon_square_600x600_en.jpg"}, {"GameName": "Treasures of Lion City", "GameCode": "SMG_treasuresOfLionCity", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/30a042a62d274f40b81b3418ae194dbc_smg_treasuresoflioncity_icon_square_600x600_en.jpg"}, {"GameName": "Trojan Kingdom", "GameCode": "SMG_trojanKingdom", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/22ce58d7b0414ce48fce48d71ed29f7e_smg_trojankingdom_icon_square_600x600_en.jpg"}, {"GameName": "Untamed - Giant Panda", "GameCode": "SMG_untamedGiantPanda", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/0344cbef206d4f8b8e7c8997ad20e082_smg_untamedgiantpanda_icon_square_600x600_en.jpg"}, {"GameName": "Village People Macho Moves", "GameCode": "SMG_villagePeople", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/5541d56e9cd5437393a3d842b2603bb2_smg_villagepeople_icon_square_600x600_en.jpg"}, {"GameName": "<PERSON><PERSON><PERSON>", "GameCode": "SMG_wacky<PERSON>anda", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/2c3f31578e8b4fedad1235af3b98a212_smg_wackypanda_icon_square_600x600_en.jpg"}, {"GameName": "Wanted Outlaws", "GameCode": "SMG_wantedOutlaws", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/b64f3903e9794b39bc9e427565e3d5c9_smg_wantedoutlaws_icon_square_600x600_en.jpg"}, {"GameName": "Western Gold", "GameCode": "SMG_westernGold", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/04455226780f42e1a5d53228febcd789_smg_westerngold_icon_square_600x600_en.jpg"}, {"GameName": "Western Gold 2", "GameCode": "SMG_westernGold2", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/6fcb32a3075e435a9a6390462e40e819_smg_westerngold2_icon_square_600x600_en.jpg"}, {"GameName": "What A Hoot", "GameCode": "SMG_whatAHoot", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/92f838a2935d451a9e7b993c61d1f18a_smg_whatahoot_icon_square_600x600_en.jpg"}, {"GameName": "Wheel of Winners", "GameCode": "SMG_switchWheelOfWinners", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/f57ce87851c145fd8f653d575d74f8cf_smg_switchwheelofwinners_icon_square_600x600_en.jpg"}, {"GameName": "Wicked Tales: Dark Red", "GameCode": "SMG_wickedTalesDarkRed", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/3d46e6d250f849e9857ba719c491bdc6_smg_wickedtalesdarkred_icon_square_600x600_en.jpg"}, {"GameName": "Wild Catch (New)", "GameCode": "SMG_wildCatchNew", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/84027dd07f78485c8b6dfb64efd1637f_smg_wildcatchnew_icon_square_600x600_en.jpg"}, {"GameName": "Wild Orient", "GameCode": "SMG_wildOrient", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/0434bd1f9081451bb04474cfda53dd42_smg_wildorient_icon_square_600x600_en.jpg"}, {"GameName": "<PERSON> Scarabs", "GameCode": "SMG_wildScarabs", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/9a2514ea55c04cdbb733f9e7dac005f3_smg_wildscarabs_icon_square_600x600_en.jpg"}, {"GameName": "Wild Wild Romance", "GameCode": "SMG_wildWildRomance", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/89babb6c2b0e4cba94eec8f3b2ae9668_smg_wildwildromance_icon_square_600x600_en.jpg"}, {"GameName": "Win Sum Dim Sum", "GameCode": "SMG_winSumDimSum", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/11036fb3736f4623ad091d2a8d617116_smg_winsumdimsum_icon_square_600x600_en.jpg"}, {"GameName": "<PERSON> Blaze™ Megaways™", "GameCode": "SMG_wolfBlazeMegaways", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/f1afa76e3dee4f6ab28e5731c5be7d1d_smg_wolfblazemegaways_icon_square_600x600_en.jpg"}, {"GameName": "WWE Legends: <PERSON> & Win", "GameCode": "SMG_wweLegendsLinkWin", "ImageIcon": "https://upld.linkv2.com/UploadedFiles/games-images/MG/********/248a4b75a027401eb9c57d858316bd28_smg_wwelegendslinkwin_icon_square_600x600_en.jpg"}]