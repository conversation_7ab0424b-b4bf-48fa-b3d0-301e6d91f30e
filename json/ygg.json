[{"DCGameCode": 7305, "GameCode": 7305, "GameName": "Reef Run", "GameNameCN": "海洋动物传", "ReleaseDate": "2014-09-30", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7305.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7310, "GameCode": 7310, "GameName": "Dark Joker Rizes", "GameNameCN": "地狱小丑", "ReleaseDate": "2014-12-31", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7310.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7313, "GameCode": 7313, "GameName": "<PERSON> the Virus", "GameNameCN": "赛勒斯病毒", "ReleaseDate": "2015-04-30", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7313.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7315, "GameCode": 7315, "GameName": "Chibeasties", "GameNameCN": "萌萌外星人", "ReleaseDate": "2015-05-31", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7315.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7316, "GameCode": 7316, "GameName": "Vikings go Wild", "GameNameCN": "狂野北欧海盗", "ReleaseDate": "2015-07-31", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7316.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7317, "GameCode": 7317, "GameName": "<PERSON> and the Stolen Stones", "GameNameCN": "福尔摩斯和被盗石头", "ReleaseDate": "2015-09-08", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7317.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7318, "GameCode": 7318, "GameName": "Doubles", "GameNameCN": "双倍水晶果", "ReleaseDate": "2015-09-30", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7318.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7308, "GameCode": 7308, "GameName": "Cazino Zeppelin", "GameNameCN": "齐柏林赌场", "ReleaseDate": "2015-10-14", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7308.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7320, "GameCode": 7320, "GameName": "Incinerator", "GameNameCN": "激情燃烧", "ReleaseDate": "2015-12-31", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7320.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7302, "GameCode": 7302, "GameName": "Winterberries", "GameNameCN": "冬季浆果", "ReleaseDate": "2016-01-13", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7302.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7322, "GameCode": 7322, "GameName": "Golden Fish Tank", "GameNameCN": "金鱼缸", "ReleaseDate": "2016-01-31", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7322.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7301, "GameCode": 7301, "GameName": "Jokerizer", "GameNameCN": "游乐园小丑", "ReleaseDate": "2016-02-29", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7301.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7323, "GameCode": 7323, "GameName": "Seasons", "GameNameCN": "丰收季", "ReleaseDate": "2016-02-29", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7323.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7321, "GameCode": 7321, "GameName": "Wicked Circus", "GameNameCN": "邪恶马戏团", "ReleaseDate": "2016-03-31", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7321.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7325, "GameCode": 7325, "GameName": "Legend of the Golden Monkey", "GameNameCN": "传奇美猴王", "ReleaseDate": "2016-05-23", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7325.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7326, "GameCode": 7326, "GameName": "Bicicleta", "GameNameCN": "足球决赛", "ReleaseDate": "2016-06-08", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7326.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7327, "GameCode": 7327, "GameName": "Legend of the White Snake Lady", "GameNameCN": "白蛇传", "ReleaseDate": "2016-07-20", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7327.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7328, "GameCode": 7328, "GameName": "Big Blox", "GameNameCN": "大布洛思", "ReleaseDate": "2016-08-23", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7328.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7329, "GameCode": 7329, "GameName": "Double Dragons", "GameNameCN": "双龙", "ReleaseDate": "2016-09-21", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7329.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7319, "GameCode": 7319, "GameName": "Nirvana", "GameNameCN": "涅盘", "ReleaseDate": "2016-10-23", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7319.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7330, "GameCode": 7330, "GameName": "Super Heroes", "GameNameCN": "超级英雄", "ReleaseDate": "2016-10-23", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7330.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7331, "GameCode": 7331, "GameName": "Vikings go Berzerk", "GameNameCN": "暴怒北欧海盗", "ReleaseDate": "2016-11-22", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7331.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7332, "GameCode": 7332, "GameName": "Alchymedes", "GameNameCN": "炼金师", "ReleaseDate": "2017-01-23", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7332.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7333, "GameCode": 7333, "GameName": "<PERSON> and the Beast", "GameNameCN": "美女与野兽", "ReleaseDate": "2017-02-22", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7333.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7334, "GameCode": 7334, "GameName": "Chibeasties 2", "GameNameCN": "萌萌外星人2", "ReleaseDate": "2017-03-17", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7334.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7335, "GameCode": 7335, "GameName": "Power Plant", "GameNameCN": "发电厂", "ReleaseDate": "2017-04-23", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7335.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7338, "GameCode": 7338, "GameName": "Sunny Shores", "GameNameCN": "阳光海滩", "ReleaseDate": "2017-05-23", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7338.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7339, "GameCode": 7339, "GameName": "Spina Colada", "GameNameCN": "水果朗姆酒", "ReleaseDate": "2017-06-21", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7339.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7336, "GameCode": 7336, "GameName": "<PERSON>", "GameNameCN": "乐手莱恩", "ReleaseDate": "2017-07-23", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7336.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7341, "GameCode": 7341, "GameName": "Valley of the Gods", "GameNameCN": "山谷之神", "ReleaseDate": "2017-08-23", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7341.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7389, "GameCode": 7389, "GameName": "Valley of the Gods 2", "GameNameCN": "山谷之神", "ReleaseDate": "2017-08-23", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7389.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7337, "GameCode": 7337, "GameName": "Jungle Books", "GameNameCN": "丛林冒险", "ReleaseDate": "2017-09-19", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7337.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7340, "GameCode": 7340, "GameName": "<PERSON><PERSON><PERSON>", "GameNameCN": "轰炸南瓜", "ReleaseDate": "2017-10-23", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7340.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7342, "GameCode": 7342, "GameName": "Reptoids", "GameNameCN": "外星爬虫人", "ReleaseDate": "2017-11-22", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7342.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 8301, "GameCode": 8301, "GameName": "Orient Express", "GameNameCN": "东方快车", "ReleaseDate": "2017-11-29", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/8301.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7345, "GameCode": 7345, "GameName": "Ozwins Jackpots", "GameNameCN": "奥兹温超级大奖池", "ReleaseDate": "2018-01-23", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7345.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7343, "GameCode": 7343, "GameName": "Gem Rocks", "GameNameCN": "宝石矿山", "ReleaseDate": "2018-02-21", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7343.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7346, "GameCode": 7346, "GameName": "Easter Island", "GameNameCN": "复活岛", "ReleaseDate": "2018-03-21", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7346.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7348, "GameCode": 7348, "GameName": "<PERSON><PERSON>", "GameNameCN": "摔角高手", "ReleaseDate": "2018-04-23", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7348.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7304, "GameCode": 7304, "GameName": "Fruitoids", "GameNameCN": "速冻水果", "ReleaseDate": "2018-05-14", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7304.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7347, "GameCode": 7347, "GameName": "Vikings Go To Hell", "GameNameCN": "去死吧维京人", "ReleaseDate": "2018-05-23", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7347.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7349, "GameCode": 7349, "GameName": "<PERSON>ts<PERSON><PERSON><PERSON>", "GameNameCN": "图坦卡蒙破坏者", "ReleaseDate": "2018-06-20", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7349.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 21001, "GameCode": 21001, "GameName": "Sonya Blackjack", "GameNameCN": "索妮雅21点", "ReleaseDate": "2018-07-01", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/21001.jpg?v=1", "GameType": "Table game", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7350, "GameCode": 7350, "GameName": "Penguin City", "GameNameCN": "企鹅城市", "ReleaseDate": "2018-07-23", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7350.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7351, "GameCode": 7351, "GameName": "<PERSON><PERSON>'s <PERSON><PERSON>", "GameNameCN": "汉佐的道场", "ReleaseDate": "2018-08-22", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7351.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7352, "GameCode": 7352, "GameName": "Wolf Hunters", "GameNameCN": "狼猎人", "ReleaseDate": "2018-09-02", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7352.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7353, "GameCode": 7353, "GameName": "Dark Vortex", "GameNameCN": "黑暗漩涡", "ReleaseDate": "2018-10-23", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7353.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7354, "GameCode": 7354, "GameName": "<PERSON>", "GameNameCN": "安息日男爵", "ReleaseDate": "2018-11-21", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7354.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 8302, "GameCode": 8302, "GameName": "Trolls Bridge", "GameNameCN": "巨魔之桥", "ReleaseDate": "2019-01-07", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/8302.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 21002, "GameCode": 21002, "GameName": "Lucky Blackjack", "GameNameCN": "幸运21点", "ReleaseDate": "2019-01-14", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/21002.jpg?v=1", "GameType": "Table game", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7357, "GameCode": 7357, "GameName": "Cazino Cosmos", "GameNameCN": "宇宙奇幻赌场", "ReleaseDate": "2019-01-23", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7357.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7358, "GameCode": 7358, "GameName": "Champions of Rome", "GameNameCN": "罗马冠军", "ReleaseDate": "2019-02-20", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7358.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7359, "GameCode": 7359, "GameName": "Dwarf Mine", "GameNameCN": "矮人矿工", "ReleaseDate": "2019-03-20", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7359.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7360, "GameCode": 7360, "GameName": "<PERSON>", "GameNameCN": "嘉年华大师", "ReleaseDate": "2019-04-23", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7360.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 1001, "GameCode": 1001, "GameName": "Niagara Falls", "GameNameCN": "尼加拉瓜大瀑布", "ReleaseDate": "2019-05-08", "GameStudio": "Northern Lights", "ImageIcon": "/media/egames/ygg/1001.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7361, "GameCode": 7361, "GameName": "Jackpot Raiders", "GameNameCN": "夺宝奇兵", "ReleaseDate": "2019-05-22", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7361.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7366, "GameCode": 7366, "GameName": "Wild Robo Factory", "GameNameCN": "机器人冒险工厂", "ReleaseDate": "2019-06-23", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7366.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7364, "GameCode": 7364, "GameName": "The One Armed Bandit", "GameNameCN": "独臂强盗", "ReleaseDate": "2019-07-23", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7364.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7370, "GameCode": 7370, "GameName": "<PERSON><PERSON><PERSON><PERSON>", "GameNameCN": "相扑争霸", "ReleaseDate": "2019-08-21", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7370.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 1002, "GameCode": 1002, "GameName": "Nikola Teslas Incredible Machine", "GameNameCN": "特斯拉的奇幻机器", "ReleaseDate": "2019-09-04", "GameStudio": "<PERSON><PERSON><PERSON>", "ImageIcon": "/media/egames/ygg/1002.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7371, "GameCode": 7371, "GameName": "Sahara Nights", "GameNameCN": "撒哈拉之夜", "ReleaseDate": "2019-09-23", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7371.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7365, "GameCode": 7365, "GameName": "Age of Asgard", "GameNameCN": "阿萨神族", "ReleaseDate": "2019-10-23", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7365.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 1003, "GameCode": 1003, "GameName": "9kYeti", "GameNameCN": "雪怪人", "ReleaseDate": "2019-11-13", "GameStudio": "4ThePlayer", "ImageIcon": "/media/egames/ygg/1003.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7372, "GameCode": 7372, "GameName": "<PERSON><PERSON>'s Journey", "GameNameCN": "阿尔多游记", "ReleaseDate": "2019-11-20", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7372.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7374, "GameCode": 7374, "GameName": "Temple Stacks: <PERSON><PERSON>", "GameNameCN": "黄金寺庙", "ReleaseDate": "2020-01-21", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7374.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7356, "GameCode": 7356, "GameName": "Time Travel Tigers", "GameNameCN": "时空老虎旅人", "ReleaseDate": "2020-02-11", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7356.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7375, "GameCode": 7375, "GameName": "Brazil Bomba", "GameNameCN": "巴西狂欢节", "ReleaseDate": "2020-02-19", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7375.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 8303, "GameCode": 8303, "GameName": "Royal Family", "GameNameCN": "皇室大家庭", "ReleaseDate": "2020-03-03", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/8303.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 1008, "GameCode": 1008, "GameName": "All Star Knockout", "GameNameCN": "满天星淘汰赛", "ReleaseDate": "2020-03-10", "GameStudio": "Northern Lights", "ImageIcon": "/media/egames/ygg/1008.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 8304, "GameCode": 8304, "GameName": "Pirates: Smugglers Paradise", "GameNameCN": "海盗-精灵岛", "ReleaseDate": "2020-03-17", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/8304.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7383, "GameCode": 7383, "GameName": "Multifly!", "GameNameCN": "飞向奇迹", "ReleaseDate": "2020-03-23", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7383.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7362, "GameCode": 7362, "GameName": "<PERSON>", "GameNameCN": "威廉英雄传说", "ReleaseDate": "2020-04-15", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7362.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7381, "GameCode": 7381, "GameName": "Neon Rush: <PERSON><PERSON>", "GameNameCN": "霓虹冲锋", "ReleaseDate": "2020-04-23", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7381.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 8306, "GameCode": 8306, "GameName": "Blood Moon Wilds", "GameNameCN": "噬血之月", "ReleaseDate": "2020-05-07", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/8306.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7384, "GameCode": 7384, "GameName": "Arthur's Fortune", "GameNameCN": "阿瑟的宝藏", "ReleaseDate": "2020-05-21", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7384.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7388, "GameCode": 7388, "GameName": "Lightning Joker", "GameNameCN": "电光小丑女", "ReleaseDate": "2020-06-10", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7388.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 8308, "GameCode": 8308, "GameName": "Avatars: Gateway Guardians", "GameNameCN": "阿凡达特攻队", "ReleaseDate": "2020-06-17", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/8308.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7382, "GameCode": 7382, "GameName": "<PERSON>", "GameNameCN": "招财幸运猫", "ReleaseDate": "2020-06-24", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7382.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 1013, "GameCode": 1013, "GameName": "All Star Knockout Ultra Gamble", "GameNameCN": "超级满天星淘汰赛", "ReleaseDate": "2020-07-02", "GameStudio": "Northern Lights", "ImageIcon": "/media/egames/ygg/1013.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7387, "GameCode": 7387, "GameName": "Vault of Fortune", "GameNameCN": "财宝金库", "ReleaseDate": "2020-07-23", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7387.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7386, "GameCode": 7386, "GameName": "Football Glory", "GameNameCN": "足球传奇", "ReleaseDate": "2020-08-12", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7386.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7373, "GameCode": 7373, "GameName": "Jackpot Express", "GameNameCN": "奖池快车", "ReleaseDate": "2020-08-24", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7373.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 1014, "GameCode": 1014, "GameName": "Zeus Vs Thor", "GameNameCN": "双神-宙斯与雷神", "ReleaseDate": "2020-09-16", "GameStudio": "4theplayer", "ImageIcon": "/media/egames/ygg/1014.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7389, "GameCode": 7389, "GameName": "Valley of the Gods 2", "GameNameCN": "山谷之神2", "ReleaseDate": "2020-09-24", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7389.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 1015, "GameCode": 1015, "GameName": "Victoria Wild", "GameNameCN": "维多利亚狂野", "ReleaseDate": "2020-10-01", "GameStudio": "Truelab", "ImageIcon": "/media/egames/ygg/1015.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 1017, "GameCode": 1017, "GameName": "Rock the cash bar", "GameNameCN": "摇滚现金", "ReleaseDate": "2020-10-13", "GameStudio": "Northern Lights", "ImageIcon": "/media/egames/ygg/1017.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7392, "GameCode": 7392, "GameName": "Hyperburst", "GameNameCN": "超级爆发", "ReleaseDate": "2020-10-15", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7392.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7390, "GameCode": 7390, "GameName": "Hades", "GameNameCN": "黑帝斯", "ReleaseDate": "2020-10-22", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7390.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 1018, "GameCode": 1018, "GameName": "<PERSON><PERSON>", "GameNameCN": "茉莉莫拉", "ReleaseDate": "2020-11-17", "GameStudio": "Reflex Gaming", "ImageIcon": "/media/egames/ygg/1018.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7391, "GameCode": 7391, "GameName": "Syncronite", "GameNameCN": "暗阎石", "ReleaseDate": "2020-11-19", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7391.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7402, "GameCode": 7402, "GameName": "Carol of The Elves", "GameNameCN": "小精灵之歌", "ReleaseDate": "2020-11-30", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7402.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 8309, "GameCode": 8309, "GameName": "Pirates 2: <PERSON><PERSON><PERSON>", "GameNameCN": "海盗2:叛乱", "ReleaseDate": "2020-12-03", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/8309.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 1024, "GameCode": 1024, "GameName": "Christmas Tree", "GameNameCN": "圣诞树", "ReleaseDate": "2020-12-08", "GameStudio": "Truelab", "ImageIcon": "/media/egames/ygg/1024.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7398, "GameCode": 7398, "GameName": "Frost Queen Jackpots", "GameNameCN": "冰风暴女王奖池", "ReleaseDate": "2021-01-21", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7398.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 1020, "GameCode": 1020, "GameName": "Atlantis Megaways", "GameNameCN": "亚特兰蒂斯之谜", "ReleaseDate": "2021-02-03", "GameStudio": "Reel Play", "ImageIcon": "/media/egames/ygg/1020.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 1022, "GameCode": 1022, "GameName": "Giza Infinity Reels", "GameNameCN": "吉萨无限卷轴", "ReleaseDate": "2021-02-03", "GameStudio": "Reel Play", "ImageIcon": "/media/egames/ygg/1022.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 1025, "GameCode": 1025, "GameName": "Big Bucks Bandits Megaways", "GameNameCN": "荒野强盗团", "ReleaseDate": "2021-02-16", "GameStudio": "Reel Play", "ImageIcon": "/media/egames/ygg/1025.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7376, "GameCode": 7376, "GameName": "<PERSON><PERSON>", "GameNameCN": "愿望卷轴", "ReleaseDate": "2021-02-25", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7376.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 1026, "GameCode": 1026, "GameName": "El Dorado Infinity Reels", "GameNameCN": "黄金国无限卷轴", "ReleaseDate": "2021-03-05", "GameStudio": "Reel Play", "ImageIcon": "/media/egames/ygg/1026.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 1030, "GameCode": 1030, "GameName": "Viking Runes", "GameNameCN": "维京符文", "ReleaseDate": "2021-03-16", "GameStudio": "TrueLab", "ImageIcon": "/media/egames/ygg/1030.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 1028, "GameCode": 1028, "GameName": "<PERSON>", "GameNameCN": "重锤游戏场", "ReleaseDate": "2021-03-18", "GameStudio": "Reel Play", "ImageIcon": "/media/egames/ygg/1028.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 1036, "GameCode": 1036, "GameName": "Desperate <PERSON>wgs", "GameNameCN": "西部恶犬", "ReleaseDate": "2021-03-19", "GameStudio": "Reflex Gaming", "ImageIcon": "/media/egames/ygg/1036.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7396, "GameCode": 7396, "GameName": "Easter Island 2", "GameNameCN": "复活岛2", "ReleaseDate": "2021-03-24", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7396.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 1032, "GameCode": 1032, "GameName": "<PERSON><PERSON>", "GameNameCN": "约翰传奇", "ReleaseDate": "2021-03-30", "GameStudio": "Peter & Sons", "ImageIcon": "/media/egames/ygg/1032.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 1035, "GameCode": 1035, "GameName": "Thor Infinity Reels", "GameNameCN": "雷神无限卷轴", "ReleaseDate": "2021-04-02", "GameStudio": "Reel Play", "ImageIcon": "/media/egames/ygg/1035.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 1027, "GameCode": 1027, "GameName": "Ancient Eclipse", "GameNameCN": "远古日蚀", "ReleaseDate": "2021-04-06", "GameStudio": "Bang Bang Games", "ImageIcon": "/media/egames/ygg/1027.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7397, "GameCode": 7397, "GameName": "Atlantean Gigarise", "GameNameCN": "亚特兰蒂斯古城", "ReleaseDate": "2021-04-22", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7397.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 1038, "GameCode": 1038, "GameName": "<PERSON>", "GameNameCN": "罗宾大盗", "ReleaseDate": "2021-04-27", "GameStudio": "Peter & Sons", "ImageIcon": "/media/egames/ygg/1038.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 1043, "GameCode": 1043, "GameName": "Royal Dragon Infinity Reels", "GameNameCN": "龙王无限卷轴", "ReleaseDate": "2021-05-02", "GameStudio": "Reel Play", "ImageIcon": "/media/egames/ygg/1043.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 1033, "GameCode": 1033, "GameName": "Odin Infinity Reels Megaways", "GameNameCN": "奥丁无限卷轴", "ReleaseDate": "2021-05-09", "GameStudio": "Reel Play", "ImageIcon": "/media/egames/ygg/1033.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 1054, "GameCode": 1054, "GameName": "The Hot Offer", "GameNameCN": "火速补给", "ReleaseDate": "2021-05-11", "GameStudio": "Bang Bang Games", "ImageIcon": "/media/egames/ygg/1054.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 1040, "GameCode": 1040, "GameName": "<PERSON><PERSON><PERSON>", "GameNameCN": "釜之谜", "ReleaseDate": "2021-05-16", "GameStudio": "Peter & Sons", "ImageIcon": "/media/egames/ygg/1040.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 1053, "GameCode": 1053, "GameName": "Word of Thoth", "GameNameCN": "月神传说", "ReleaseDate": "2021-05-18", "GameStudio": "<PERSON>", "ImageIcon": "/media/egames/ygg/1053.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 10011, "GameCode": 10011, "GameName": "Jaguar SuperWays", "GameNameCN": "捷豹印记", "ReleaseDate": "2021-05-23", "GameStudio": "Reel Play", "ImageIcon": "/media/egames/ygg/10011.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7401, "GameCode": 7401, "GameName": "Gator Gold Gigablox", "GameNameCN": "掏金鳄", "ReleaseDate": "2021-05-27", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7401.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 10022, "GameCode": 10022, "GameName": "Legion Hot1", "GameNameCN": "炙热军团1", "ReleaseDate": "2021-06-20", "GameStudio": "Hot Rise Games", "ImageIcon": "/media/egames/ygg/10022.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7406, "GameCode": 7406, "GameName": "Suncatcher Gigablox ", "GameNameCN": "追日者", "ReleaseDate": "2021-06-24", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7406.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 10034, "GameCode": 10034, "GameName": "Crazy Mix", "GameNameCN": "乐派对", "ReleaseDate": "2021-06-30", "GameStudio": "TrueLab", "ImageIcon": "/media/egames/ygg/10034.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 10016, "GameCode": 10016, "GameName": "Gargoyle Infinity Reels", "GameNameCN": "石像鬼无限卷轴", "ReleaseDate": "2021-07-04", "GameStudio": "Reel Play", "ImageIcon": "/media/egames/ygg/10016.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 10053, "GameCode": 10053, "GameName": "Krazy Klimber", "GameNameCN": "疯狂攀登者", "ReleaseDate": "2021-07-07", "GameStudio": "Reflex Gaming", "ImageIcon": "/media/egames/ygg/10053.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 10025, "GameCode": 10025, "GameName": "12 Trojan Mysteries", "GameNameCN": "12个特洛伊之谜", "ReleaseDate": "2021-07-07", "GameStudio": "4ThePlayer", "ImageIcon": "/media/egames/ygg/10025.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 10035, "GameCode": 10035, "GameName": "Robin Nottingham Raiders", "GameNameCN": "救世主罗宾", "ReleaseDate": "2021-07-11", "GameStudio": "Peter & Sons", "ImageIcon": "/media/egames/ygg/10035.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 10010, "GameCode": 10010, "GameName": "Tiki Infinity Reels Megaways", "GameNameCN": "蒂基无限卷轴", "ReleaseDate": "2021-07-18", "GameStudio": "Reel Play", "ImageIcon": "/media/egames/ygg/10010.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 10021, "GameCode": 10021, "GameName": "Golden Gorgon", "GameNameCN": "黄金蛇发女妖", "ReleaseDate": "2021-07-29", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/10021.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 10046, "GameCode": 10046, "GameName": "Martian Miner Infinity Reels", "GameNameCN": "火星矿工无限卷轴", "ReleaseDate": "2021-08-04", "GameStudio": "ReelPlay (Boomerang)", "ImageIcon": "/media/egames/ygg/10046.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 10064, "GameCode": 10064, "GameName": "Crystal Falls", "GameNameCN": "水晶瀑布", "ReleaseDate": "2021-08-04", "GameStudio": "Bulletproof MULTIMAX", "ImageIcon": "/media/egames/ygg/10064.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 1042, "GameCode": 1042, "GameName": "Wild Joker Stacks", "GameNameCN": "疯狂小丑", "ReleaseDate": "2021-08-18", "GameStudio": "ReelPlay (Boomerang)", "ImageIcon": "/media/egames/ygg/1042.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 10082, "GameCode": 10082, "GameName": "The Bounty Hunter", "GameNameCN": "公元3021奖金猎人", "ReleaseDate": "2021-08-18", "GameStudio": "Reflex Gaming GIGABLOX", "ImageIcon": "/media/egames/ygg/10082.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 10102, "GameCode": 10102, "GameName": "<PERSON><PERSON>", "GameNameCN": "妖姬诱音", "ReleaseDate": "2021-08-22", "GameStudio": "TrueLab", "ImageIcon": "/media/egames/ygg/10102.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7408, "GameCode": 7408, "GameName": "<PERSON><PERSON>", "GameNameCN": "猛禽兽啸", "ReleaseDate": "2021-08-27", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7408.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 10007, "GameCode": 10007, "GameName": "Jambo Cash", "GameNameCN": "东非金库", "ReleaseDate": "2021-09-08", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/10007.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 10074, "GameCode": 10074, "GameName": "Towering Pays Valhalla", "GameNameCN": "英灵神殿", "ReleaseDate": "2021-09-08", "GameStudio": "Reelplay", "ImageIcon": "/media/egames/ygg/10074.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 10123, "GameCode": 10123, "GameName": "Golden Haul Infinity Reels", "GameNameCN": "掏金矿坑无限卷轴", "ReleaseDate": "2021-10-06", "GameStudio": "Bad Dingo", "ImageIcon": "/media/egames/ygg/10123.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 10080, "GameCode": 10080, "GameName": "10x Rewind", "GameNameCN": "急速倒转", "ReleaseDate": "2021-09-30", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/10080.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 10090, "GameCode": 10090, "GameName": "Hammer of Gods", "GameNameCN": "灵神锤", "ReleaseDate": "2021-09-30", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/10090.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 10086, "GameCode": 10086, "GameName": "Medusa Hot 1", "GameNameCN": "美杜莎：死亡之穴 ", "ReleaseDate": "2021-10-13", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/10086.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 10173, "GameCode": 10173, "GameName": "Vikings Go Berzerk: Reloaded", "GameNameCN": "暴怒北欧海盗-重装版", "ReleaseDate": "2021-10-01", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/10173.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 10180, "GameCode": 10180, "GameName": "Winfall in Paradise", "GameNameCN": "热带天堂大冒险", "ReleaseDate": "2021-10-29", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/10180.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 10172, "GameCode": 10172, "GameName": "Hunters Moon", "GameNameCN": "猎月传说", "ReleaseDate": "2021-10-20", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/10172.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 10182, "GameCode": 10182, "GameName": "Gems Infinity Reels", "GameNameCN": "魔幻宝石无限卷轴", "ReleaseDate": "2021-10-25", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/10182.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 10085, "GameCode": 10085, "GameName": "TIGER_TIGER", "GameNameCN": "猛虎聚", "ReleaseDate": "2021-11-10", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/10085.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 10137, "GameCode": 10137, "GameName": "SavannaRoar", "GameNameCN": "荒原狮鸣", "ReleaseDate": "2021-11-10", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/10137.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 10171, "GameCode": 10171, "GameName": "Hypernova 10K Ways", "GameNameCN": "骇星齐爆", "ReleaseDate": "2021-11-03", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/10171.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 10054, "GameCode": 10054, "GameName": "90k Yeti Gigablox", "GameNameCN": "雪怪人族", "ReleaseDate": "2021-11-10", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/10054.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 7409, "GameCode": 7409, "GameName": "Golden Fish Tank 2 Gigablox", "GameNameCN": "金鱼缸2", "ReleaseDate": "2021-10-28", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/7409.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 10174, "GameCode": 10174, "GameName": "SERENDIPITY", "GameNameCN": "幻惑奇缘", "ReleaseDate": "2021-11-17", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/10174.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 10194, "GameCode": 10194, "GameName": "ThunderOfThor", "GameNameCN": "轰鸣雷神", "ReleaseDate": "2021-11-30", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/10194.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 10208, "GameCode": 10208, "GameName": "Age Of Beasts Infinity Reels", "GameNameCN": "猛兽王朝无限卷轴", "ReleaseDate": "2021-12-13", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/10208.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 10181, "GameCode": 10181, "GameName": "Monster Blox", "GameNameCN": "妖之气息", "ReleaseDate": "2021-12-03", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/10181.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 10140, "GameCode": 10140, "GameName": "Five Clans", "GameNameCN": "五宗族", "ReleaseDate": "2021-11-24", "GameStudio": "Reflex Gaming", "ImageIcon": "/media/egames/ygg/10140.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 10071, "GameCode": 10071, "GameName": "Lady Merlin Lightning Chase", "GameNameCN": "巫妖魔灵", "ReleaseDate": "2021-11-24", "GameStudio": "Boomerang", "ImageIcon": "/media/egames/ygg/10071.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 10170, "GameCode": 10170, "GameName": "Gator Gold Deluxe Gigablox", "GameNameCN": "奢迷掏金鳄", "ReleaseDate": "2021-11-26", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/10170.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}, {"DCGameCode": 10111, "GameCode": 10111, "GameName": "Valkyries", "GameNameCN": "圣女武神", "ReleaseDate": "2021-12-17", "GameStudio": "Yggdrasil", "ImageIcon": "/media/egames/ygg/10111.jpg?v=1", "GameType": "SLOT", "ContentType": "Standard", "GameStatus": 1}]