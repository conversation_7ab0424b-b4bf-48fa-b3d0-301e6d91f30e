[{"GameType": "Slot", "GameCode": "ape6dxf7sk35y", "GameName": "Roma Legacy", "SupportedPlatForms": "Desktop,Mobile", "Specials": "new,recommend", "Technology": null, "Order": 0, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/ape6dxf7sk35y.png"}, {"GameType": "Slot", "GameCode": "e9qs4cbtga5ue", "GameName": "Wealth God", "SupportedPlatForms": "Desktop,Mobile", "Specials": "new,recommend", "Technology": null, "Order": 1, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/e9qs4cbtga5ue.png"}, {"GameType": "Slot", "GameCode": "uh4amsg355x7a", "GameName": "Fruit Paradise", "SupportedPlatForms": "Desktop,Mobile", "Specials": "new", "Technology": null, "Order": 2, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/uh4amsg355x7a.png"}, {"GameType": "Slot", "GameCode": "zowjitp4zqhm1", "GameName": "Critter Mania", "SupportedPlatForms": "Desktop,Mobile", "Specials": "new,recommend", "Technology": null, "Order": 3, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/3pc6upfiiki4o.png"}, {"GameType": "Slot", "GameCode": "4ib98eidwno4c", "GameName": "<PERSON><PERSON> ", "SupportedPlatForms": "Desktop,Mobile", "Specials": "new,recommend", "Technology": null, "Order": 3, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/4ib98eidwno4c.png"}, {"GameType": "Slot", "GameCode": "4bnxpkpqi6sk4", "GameName": "B<PERSON><PERSON><PERSON>", "SupportedPlatForms": "Desktop,Mobile", "Specials": "new,recommend", "Technology": null, "Order": 4, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/4bnxpkpqi6sk4.png"}, {"GameType": "Slot", "GameCode": "67s75yrbo4dae", "GameName": "<PERSON><PERSON><PERSON>", "SupportedPlatForms": "Desktop,Mobile", "Specials": "new,recommend", "Technology": null, "Order": 5, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/67s75yrbo4dae.png"}, {"GameType": "Slot", "GameCode": "c53raraonrmbq", "GameName": "Pan Jin Lian 2", "SupportedPlatForms": "Desktop,Mobile", "Specials": "new,recommend", "Technology": null, "Order": 6, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/c53raraonrmbq.png"}, {"GameType": "Slot", "GameCode": "5m6k9j7rwspjs", "GameName": "Roma", "SupportedPlatForms": "Desktop,Mobile", "Specials": ",recommend", "Technology": null, "Order": 7, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/5m6k9j7rwspjs.png"}, {"GameType": "Fishing", "GameCode": "b8rzo7uzqt4sw", "GameName": "Fish Hunting: Golden Toad", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 8, "DefaultWidth": 1280, "DefaultHeight": 720, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/b8rzo7uzqt4sw.png"}, {"GameType": "Slot", "GameCode": "ww3a8wsu4de7c", "GameName": "Sizzling Hot", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 9, "DefaultWidth": 1105, "DefaultHeight": 737, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/ww3a8wsu4de7c.png"}, {"GameType": "<PERSON><PERSON>", "GameCode": "tocki7xk7xwq1", "GameName": "Burning Pearl Bingo", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 10, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/tocki7xk7xwq1.png"}, {"GameType": "Fishing", "GameCode": "wi17jwsu4de7c", "GameName": "Fish Hunting: <PERSON>", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 11, "DefaultWidth": 1280, "DefaultHeight": 720, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/wi17jwsu4de7c.png"}, {"GameType": "Slot", "GameCode": "ha1jzrho1gmjq", "GameName": "Mayan Gems", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 12, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/ha1jzrho1gmjq.png"}, {"GameType": "Fishing", "GameCode": "8d7r1okge7nrk", "GameName": "Fish Hunting: <PERSON>", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 13, "DefaultWidth": 1280, "DefaultHeight": 720, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/8d7r1okge7nrk.png"}, {"GameType": "Slot", "GameCode": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "GameName": "Hu<PERSON>", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 14, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/pirtanombyroh.png"}, {"GameType": "<PERSON><PERSON>", "GameCode": "cz3wgrounyetc", "GameName": "Neptune Treasure Bingo", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 15, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/cz3wgrounyetc.png"}, {"GameType": "<PERSON><PERSON>", "GameCode": "z7k6mqf3z495a", "GameName": "Crypto Mania Bingo", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 16, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/z7k6mqf3z495a.png"}, {"GameType": "Slot", "GameCode": "soojfuqnaxycn", "GameName": "Hot Fruits", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 17, "DefaultWidth": 1105, "DefaultHeight": 737, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/soojfuqnaxycn.png"}, {"GameType": "Slot", "GameCode": "byz81hmsq748k", "GameName": "Supreme Caishen", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 18, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/byz81hmsq748k.png"}, {"GameType": "Slot", "GameCode": "9w6aa6u5xbhzh", "GameName": "Golden Dragon", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 19, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/9w6aa6u5xbhzh.png"}, {"GameType": "Slot", "GameCode": "3yfmucpss64mk", "GameName": "Dragon Power Flame", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 20, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/3yfmucpss64mk.png"}, {"GameType": "<PERSON><PERSON>", "GameCode": "ezjsgctugyauc", "GameName": "<PERSON><PERSON><PERSON>", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 21, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/ezjsgctugyauc.png"}, {"GameType": "Slot", "GameCode": "dhdirsn3m3xia", "GameName": "Lucky God", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 22, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/dhdirsn3m3xia.png"}, {"GameType": "Slot", "GameCode": "ne4gq55cpitgg", "GameName": "Beanstalk", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 23, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/ne4gq55cpitgg.png"}, {"GameType": "Slot", "GameCode": "j9nzkkbjfaz1a", "GameName": "Horus Eye", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 24, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/j9nzkkbjfaz1a.png"}, {"GameType": "<PERSON><PERSON>", "GameCode": "fn6yhwksk7kfk", "GameName": "<PERSON><PERSON>", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 25, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/fn6yhwksk7kfk.png"}, {"GameType": "Slot", "GameCode": "ef1uyxt98o6ur", "GameName": "Lucky God Progressive", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 26, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/ef1uyxt98o6ur.png"}, {"GameType": "Fishing", "GameCode": "nzkseaudcbosc", "GameName": "Fish Hunting: <PERSON>", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 27, "DefaultWidth": 1280, "DefaultHeight": 720, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/nzkseaudcbosc.png"}, {"GameType": "Slot", "GameCode": "bcizh7dipjiso", "GameName": "<PERSON><PERSON>", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 28, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/bcizh7dipjiso.png"}, {"GameType": "Slot", "GameCode": "aodmmxp1sqamn", "GameName": "<PERSON>", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 29, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/aodmmxp1sqamn.png"}, {"GameType": "Slot", "GameCode": "3hj4fkfji4z4a", "GameName": "Lucky God Progressive 2", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 30, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/3hj4fkfji4z4a.png"}, {"GameType": "Slot", "GameCode": "7cz37fritkfao", "GameName": "Lucky Rooster", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 31, "DefaultWidth": 1105, "DefaultHeight": 737, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/7cz37fritkfao.png"}, {"GameType": "Slot", "GameCode": "zygj7oqga9nck", "GameName": "<PERSON><PERSON><PERSON>", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 32, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/zygj7oqga9nck.png"}, {"GameType": "Fishing", "GameCode": "kk8nqm3cfwtng", "GameName": "Fish Haiba", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 33, "DefaultWidth": 1280, "DefaultHeight": 720, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/kk8nqm3cfwtng.png"}, {"GameType": "Slot", "GameCode": "86burqb38a9ua", "GameName": "Bushido Blade", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 34, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/86burqb38a9ua.png"}, {"GameType": "Slot", "GameCode": "3fx69pizs144w", "GameName": "Lucky Streak", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 35, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/3fx69pizs144w.png"}, {"GameType": "Slot", "GameCode": "ofy9b9z99u69r", "GameName": "Fire Reign", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 36, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/ofy9b9z99u69r.png"}, {"GameType": "Slot", "GameCode": "8rqwot18etnuw", "GameName": "Thunder God", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 37, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/8rqwot18etnuw.png"}, {"GameType": "Slot", "GameCode": "swt38osdadyhc", "GameName": "Black Beard Legacy", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 39, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/swt38osdadyhc.png"}, {"GameType": "Fishing", "GameCode": "1jeqx59c7ztqg", "GameName": "Fish Hunter Monster Awaken", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 40, "DefaultWidth": 1280, "DefaultHeight": 720, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/1jeqx59c7ztqg.png"}, {"GameType": "Slot", "GameCode": "hcu3p8r71kj3y", "GameName": "Power Stars", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 41, "DefaultWidth": 1105, "DefaultHeight": 737, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/hcu3p8r71kj3y.png"}, {"GameType": "Slot", "GameCode": "tbfxuhxs694xk", "GameName": "<PERSON>", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 42, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/tbfxuhxs694xk.png"}, {"GameType": "Slot", "GameCode": "y4jnah5oqf58q", "GameName": "Yakuza", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 43, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/y4jnah5oqf58q.png"}, {"GameType": "Slot", "GameCode": "wcaadzg74mj7y", "GameName": "<PERSON>", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 44, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/wcaadzg74mj7y.png"}, {"GameType": "Slot", "GameCode": "mur8wje4dccb1", "GameName": "Scheherazade", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 45, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/mur8wje4dccb1.png"}, {"GameType": "Slot", "GameCode": "ur8593z8hu17w", "GameName": "Burning Pearl", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 46, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/ur8593z8hu17w.png"}, {"GameType": "Slot", "GameCode": "4d5kdkpqi6sk4", "GameName": "Safari Heat", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 47, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/4d5kdkpqi6sk4.png"}, {"GameType": "Fishing", "GameCode": "xkhy6baryz7xs", "GameName": "Fish Hunter 2 EX - Newbie", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 48, "DefaultWidth": 1280, "DefaultHeight": 720, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/xkhy6baryz7xs.png"}, {"GameType": "Slot", "GameCode": "xtpy4bx49xhx1", "GameName": "Safari Life", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 49, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/xtpy4bx49xhx1.png"}, {"GameType": "Slot", "GameCode": "6jupbdhctsbeg", "GameName": "Ancient Rome", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 50, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/6jupbdhctsbeg.png"}, {"GameType": "Slot", "GameCode": "55hj8ghaugxj6", "GameName": "Happy Buddha", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 51, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/55hj8ghaugxj6.png"}, {"GameType": "Slot", "GameCode": "satj3o6ya8dcq", "GameName": "Just Jewels Deluxe", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 52, "DefaultWidth": 1105, "DefaultHeight": 737, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/satj3o6ya8dcq.png"}, {"GameType": "Slot", "GameCode": "rg5oqz19mtqir", "GameName": "Bali", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 53, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/rg5oqz19mtqir.png"}, {"GameType": "Slot", "GameCode": "fwria11mjbrwh", "GameName": "Three Kingdoms Quest", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 54, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/fwria11mjbrwh.png"}, {"GameType": "Slot", "GameCode": "wr5axzs95uq7r", "GameName": "Forest Treasure", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 55, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/wr5axzs95uq7r.png"}, {"GameType": "Slot", "GameCode": "r8oiyz19mtqir", "GameName": "Lucky Joker", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 56, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/r8oiyz19mtqir.png"}, {"GameType": "Slot", "GameCode": "ebudnqj68h6d4", "GameName": "Happy Party", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 57, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/ebudnqj68h6d4.png"}, {"GameType": "Fishing", "GameCode": "st5cmuqnaxycn", "GameName": "Fish Hunting: <PERSON> Fish 5", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 58, "DefaultWidth": 1280, "DefaultHeight": 720, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/st5cmuqnaxycn.png"}, {"GameType": "Slot", "GameCode": "orm4x9z99u69r", "GameName": "League Of Legends", "SupportedPlatForms": "Desktop,Mobile", "Specials": "recommend", "Technology": null, "Order": 59, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/orm4x9z99u69r.png"}, {"GameType": "Slot", "GameCode": "oajk3h9o685xq", "GameName": "Money Vault", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 60, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/oajk3h9o685xq.png"}, {"GameType": "Slot", "GameCode": "gkubyu4cjibrg", "GameName": "Joker Madness", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 61, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/gkubyu4cjibrg.png"}, {"GameType": "Slot", "GameCode": "n1ydr5mncpogn", "GameName": "<PERSON><PERSON>", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 62, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/n1ydr5mncpogn.png"}, {"GameType": "Slot", "GameCode": "q9gi4yybyadoe", "GameName": "Wild Giant Panda", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 63, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/q9gi4yybyadoe.png"}, {"GameType": "Slot", "GameCode": "ioheiiqk3xrc1", "GameName": "Book Of Ra", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 64, "DefaultWidth": 1105, "DefaultHeight": 737, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/ioheiiqk3xrc1.png"}, {"GameType": "Slot", "GameCode": "3jxqtp7wssiks", "GameName": "The Legend Of White Snake", "SupportedPlatForms": "Desktop,Mobile", "Specials": null, "Technology": null, "Order": 65, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/3jxqtp7wssiks.png"}, {"GameType": "Slot", "GameCode": "gd3rn1kqj7gr4", "GameName": "Queen", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 66, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/gd3rn1kqj7gr4.png"}, {"GameType": "Slot", "GameCode": "bmr8675wqiigs", "GameName": "Witch's Brew", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 67, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/bmr8675wqiigs.png"}, {"GameType": "Slot", "GameCode": "uafejs6a58xp6", "GameName": "Bount<PERSON> Hunter", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 68, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/uafejs6a58xp6.png"}, {"GameType": "Slot", "GameCode": "5cx47jffukp3o", "GameName": "Fabulous Eights", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 69, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/5cx47jffukp3o.png"}, {"GameType": "Slot", "GameCode": "ipz77igi3mfho", "GameName": "Winter Sweets", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 70, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/ipz77igi3mfho.png"}, {"GameType": "Slot", "GameCode": "113qm5xnhxoqn", "GameName": "<PERSON><PERSON><PERSON>", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 71, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/113qm5xnhxoqn.png"}, {"GameType": "Slot", "GameCode": "43bx3e7ywgukq", "GameName": "Dolphin's Pearl Deluxe", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 72, "DefaultWidth": 1105, "DefaultHeight": 737, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/43bx3e7ywgukq.png"}, {"GameType": "Slot", "GameCode": "hf5hx8w9u1q3r", "GameName": "Book Of Ra Deluxe", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 73, "DefaultWidth": 1280, "DefaultHeight": 720, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/hf5hx8w9u1q3r.png"}, {"GameType": "Slot", "GameCode": "4akkze7ywgukq", "GameName": "Crypto Mania", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 74, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/4akkze7ywgukq.png"}, {"GameType": "Slot", "GameCode": "79mafnrjt48aa", "GameName": "<PERSON>", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 75, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/79mafnrjt48aa.png"}, {"GameType": "Slot", "GameCode": "d5qfgs4amfxf6", "GameName": "<PERSON><PERSON><PERSON>", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 76, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/d5qfgs4amfxf6.png"}, {"GameType": "Slot", "GameCode": "bwwza4umpbwsh", "GameName": "Bonus Bear", "SupportedPlatForms": "Desktop,Mobile", "Specials": null, "Technology": null, "Order": 77, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/bwwza4umpbwsh.png"}, {"GameType": "Slot", "GameCode": "d4fyes4amfxf6", "GameName": "<PERSON>", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 78, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/d4fyes4amfxf6.png"}, {"GameType": "Slot", "GameCode": "c85wq8o9doqtr", "GameName": "<PERSON>", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 79, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/c85wq8o9doqtr.png"}, {"GameType": "Slot", "GameCode": "y5n8sh5oqf58q", "GameName": "Tiger's Lair", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 80, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/y5n8sh5oqf58q.png"}, {"GameType": "Slot", "GameCode": "u6d7fsg355x7a", "GameName": "Panther Moon", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 81, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/u6d7fsg355x7a.png"}, {"GameType": "Slot", "GameCode": "9upe5bm4xph81", "GameName": "<PERSON> King", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 82, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/9upe5bm4xph81.png"}, {"GameType": "Slot", "GameCode": "3erm9p7wssiks", "GameName": "Flames Of Fortune", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 83, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/3erm9p7wssiks.png"}, {"GameType": "Slot", "GameCode": "9xpa7brfxj7zo", "GameName": "<PERSON><PERSON><PERSON>", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 84, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/9xpa7brfxj7zo.png"}, {"GameType": "Slot", "GameCode": "4jdxbm7cistkg", "GameName": "Talisman", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 85, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/4jdxbm7cistkg.png"}, {"GameType": "Slot", "GameCode": "k3anse3yrrunq", "GameName": "MoneyBangBang", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 86, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/k3anse3yrrunq.png"}, {"GameType": "Slot", "GameCode": "cuarr8e1ncebn", "GameName": "Tropical Crush", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 87, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/cuarr8e1ncebn.png"}, {"GameType": "Slot", "GameCode": "awn5jciusna5c", "GameName": "Captains <PERSON>", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 88, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/awn5jciusna5c.png"}, {"GameType": "Fishing", "GameCode": "xq9ohbyf9m79o", "GameName": "Bird Paradise", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 89, "DefaultWidth": 1280, "DefaultHeight": 720, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/xq9ohbyf9m79o.png"}, {"GameType": "Slot", "GameCode": "b4pde45epfzg6", "GameName": "Genie 2", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 90, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/b4pde45epfzg6.png"}, {"GameType": "Slot", "GameCode": "texkt79w6ziqs", "GameName": "Wukong", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 91, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/texkt79w6ziqs.png"}, {"GameType": "Slot", "GameCode": "jpiuhpbifei1o", "GameName": "Golden Rooster", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 92, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/jpiuhpbifei1o.png"}, {"GameType": "Slot", "GameCode": "1ru5x5zx7us6r", "GameName": "Lightning God", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 93, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/1ru5x5zx7us6r.png"}, {"GameType": "Slot", "GameCode": "hb4cpgc6u6qj4", "GameName": "Mythological", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 94, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/hb4cpgc6u6qj4.png"}, {"GameType": "Slot", "GameCode": "srd3xusx3ughr", "GameName": "Enter The KTV", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 95, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/srd3xusx3ughr.png"}, {"GameType": "Slot", "GameCode": "7f9h9fwz11kaw", "GameName": "Lucky Lady Charm", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 96, "DefaultWidth": 1105, "DefaultHeight": 737, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/7f9h9fwz11kaw.png"}, {"GameType": "Slot", "GameCode": "4eekxm7cistkg", "GameName": "Dragon's Realm", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 97, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/4eekxm7cistkg.png"}, {"GameType": "Slot", "GameCode": "gn1bc1kqj7gr4", "GameName": "Bagua", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 98, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/gn1bc1kqj7gr4.png"}, {"GameType": "Slot", "GameCode": "o3nxzh9o685xq", "GameName": "<PERSON><PERSON>", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 99, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/o3nxzh9o685xq.png"}, {"GameType": "Slot", "GameCode": "rh8iwwntk3mie", "GameName": "Dolphin Reef", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 100, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/rh8iwwntk3mie.png"}, {"GameType": "Slot", "GameCode": "w4ypzw6o48mpq", "GameName": "Dragon Phoenix", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 101, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/w4ypzw6o48mpq.png"}, {"GameType": "Slot", "GameCode": "wtupmzq14xepn", "GameName": "Lions Dance", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 102, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/wtupmzq14xepn.png"}, {"GameType": "Slot", "GameCode": "xmzfobaryz7xs", "GameName": "Lord Of The Ocean", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 103, "DefaultWidth": 1105, "DefaultHeight": 737, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/xmzfobaryz7xs.png"}, {"GameType": "Fishing", "GameCode": "ddpg1amgc71gk", "GameName": "Insect Paradise", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 104, "DefaultWidth": 1280, "DefaultHeight": 720, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/ddpg1amgc71gk.png"}, {"GameType": "Slot", "GameCode": "5ii9zgw5unc3h", "GameName": "Neptune Treasure", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 105, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/5ii9zgw5unc3h.png"}, {"GameType": "Slot", "GameCode": "aij68ciusna5c", "GameName": "Columbus", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 106, "DefaultWidth": 1105, "DefaultHeight": 737, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/aij68ciusna5c.png"}, {"GameType": "Slot", "GameCode": "naagsa5ycfugq", "GameName": "Ancient Egypt", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 107, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/naagsa5ycfugq.png"}, {"GameType": "Slot", "GameCode": "b1cnw7mkppwg1", "GameName": "Thug Life", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 108, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/b1cnw7mkppwg1.png"}, {"GameType": "Slot", "GameCode": "1wt58azdhdo6c", "GameName": "Wild Fairies", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 109, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/1wt58azdhdo6c.png"}, {"GameType": "Slot", "GameCode": "7tccifcktqre1", "GameName": "Chinese Boss", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 110, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/7tccifcktqre1.png"}, {"GameType": "Slot", "GameCode": "wykepsq659qp4", "GameName": "Four Dragons", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 111, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/wykepsq659qp4.png"}, {"GameType": "Slot", "GameCode": "qd1fcneqbhgy4", "GameName": "Immortals", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 112, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/qd1fcneqbhgy4.png"}, {"GameType": "Slot", "GameCode": "ufc6t3z8hu17w", "GameName": "Santa Surprise", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 113, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/ufc6t3z8hu17w.png"}, {"GameType": "Slot", "GameCode": "wfo7bzs95uq7r", "GameName": "<PERSON>", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 114, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/wfo7bzs95uq7r.png"}, {"GameType": "Slot", "GameCode": "ywozehuuqbazc", "GameName": "Golden Island", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 115, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/ywozehuuqbazc.png"}, {"GameType": "Slot", "GameCode": "d8cso3u8ct1iw", "GameName": "Phoenix 888", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 116, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/d8cso3u8ct1iw.png"}, {"GameType": "Slot", "GameCode": "kf41ymtxfos1r", "GameName": "Ocean Paradise", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 117, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/kf41ymtxfos1r.png"}, {"GameType": "Slot", "GameCode": "quof<PERSON><PERSON><PERSON><PERSON>", "GameName": "Bagua 2", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 118, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/quofrdenycyyn.png"}, {"GameType": "Slot", "GameCode": "u17q53q45xcp1", "GameName": "White Snake", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 119, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/u17q53q45xcp1.png"}, {"GameType": "Slot", "GameCode": "oqt9p9876m39y", "GameName": "Azteca", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 120, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/oqt9p9876m39y.png"}, {"GameType": "Fishing", "GameCode": "4omkmmpnwqokn", "GameName": "Fish Hunter Spongebob", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 121, "DefaultWidth": 1280, "DefaultHeight": 720, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/4omkmmpnwqokn.png"}, {"GameType": "Slot", "GameCode": "4tyxfmpnwqokn", "GameName": "Octagon Gem", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 122, "DefaultWidth": 1105, "DefaultHeight": 737, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/4tyxfmpnwqokn.png"}, {"GameType": "Slot", "GameCode": "6c5apdrpokbay", "GameName": "<PERSON><PERSON>'s Gift", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 123, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/6c5apdrpokbay.png"}, {"GameType": "Slot", "GameCode": "8kzbot4rew7ds", "GameName": "Journey To The West", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 124, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/8kzbot4rew7ds.png"}, {"GameType": "Slot", "GameCode": "y6q14hdtq35ze", "GameName": "Beach Life", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 125, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/y6q14hdtq35ze.png"}, {"GameType": "Slot", "GameCode": "1q36p58phmt6y", "GameName": "<PERSON><PERSON>", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 126, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/1q36p58phmt6y.png"}, {"GameType": "Slot", "GameCode": "jsguaktmfyw1h", "GameName": "Hercules", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 127, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/jsguaktmfyw1h.png"}, {"GameType": "Slot", "GameCode": "kdn8ckjqfhsn4", "GameName": "<PERSON><PERSON><PERSON>'s Tomb", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 128, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/kdn8ckjqfhsn4.png"}, {"GameType": "Slot", "GameCode": "gsttgo1debywc", "GameName": "Octagon Gem 2", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 129, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/gsttgo1debywc.png"}, {"GameType": "Slot", "GameCode": "ruufkzk1kpefn", "GameName": "SilverBullet Progressive", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 130, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/ruufkzk1kpefn.png"}, {"GameType": "Slot", "GameCode": "axt5pxf7sk35y", "GameName": "Highway Kings", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 132, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/axt5pxf7sk35y.png"}, {"GameType": "Slot", "GameCode": "7b6c7rcs16kjk", "GameName": "Ocean Spray", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 133, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/7b6c7rcs16kjk.png"}, {"GameType": "Slot", "GameCode": "t656f48j75z6a", "GameName": "Great Blue", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 134, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/t656f48j75z6a.png"}, {"GameType": "Slot", "GameCode": "8u9r4tj48chd1", "GameName": "Dynamite Reels", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 135, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/8u9r4tj48chd1.png"}, {"GameType": "Slot", "GameCode": "igg7tisz4ukhw", "GameName": "Egypt Queen", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 136, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/igg7tisz4ukhw.png"}, {"GameType": "Slot", "GameCode": "wpu7pzg74mj7y", "GameName": "Lucky Drum", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 137, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/wpu7pzg74mj7y.png"}, {"GameType": "Slot", "GameCode": "gqotnunpejbwy", "GameName": "Fortune Festival", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 138, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/gqotnunpejbwy.png"}, {"GameType": "Slot", "GameCode": "5trxijc4uqcj1", "GameName": "Cursed", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 139, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/5trxijc4uqcj1.png"}, {"GameType": "Slot", "GameCode": "bes8675wqiigs", "GameName": "Captain's Treasure", "SupportedPlatForms": "Desktop,Mobile", "Specials": null, "Technology": null, "Order": 140, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/bes8675wqiigs.png"}, {"GameType": "Slot", "GameCode": "96k1k6d3x39za", "GameName": "Big Game Safari", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 141, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/96k1k6d3x39za.png"}, {"GameType": "Slot", "GameCode": "ahf5icfts455e", "GameName": "Jin Fu Xing Yun", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 142, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/ahf5icfts455e.png"}, {"GameType": "Slot", "GameCode": "m94wkgy3daxta", "GameName": "Mythical Sand", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 143, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/m94wkgy3daxta.png"}, {"GameType": "Fishing", "GameCode": "qq5ocdypyeboy", "GameName": "Fish Hunter 2 EX - Novice", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 144, "DefaultWidth": 1280, "DefaultHeight": 720, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/qq5ocdypyeboy.png"}, {"GameType": "Slot", "GameCode": "93ine65axf986", "GameName": "<PERSON><PERSON>", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 145, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/93ine65axf986.png"}, {"GameType": "Slot", "GameCode": "ggutqu1xjtgwr", "GameName": "Oasis", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 146, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/ggutqu1xjtgwr.png"}, {"GameType": "Slot", "GameCode": "5bgx7epgw61kk", "GameName": "Queen 2", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 147, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/5bgx7epgw61kk.png"}, {"GameType": "Slot", "GameCode": "7rw3tfwz11kaw", "GameName": "Arctic Treasure", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 148, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/7rw3tfwz11kaw.png"}, {"GameType": "Slot", "GameCode": "c41bsraonrmbq", "GameName": "<PERSON><PERSON>", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 149, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/c41bsraonrmbq.png"}, {"GameType": "Slot", "GameCode": "s77hiogba5dhe", "GameName": "Peach Ban<PERSON>", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 150, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/s77hiogba5dhe.png"}, {"GameType": "Slot", "GameCode": "k9gz4ebbrau1e", "GameName": "Fifty Dragons", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 151, "DefaultWidth": 1105, "DefaultHeight": 737, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/k9gz4ebbrau1e.png"}, {"GameType": "Slot", "GameCode": "9ii7s6u5xbhzh", "GameName": "Yggdrasil", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 152, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/9ii7s6u5xbhzh.png"}, {"GameType": "Slot", "GameCode": "ie9eti6w4zfcs", "GameName": "Ancient Artifact", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 153, "DefaultWidth": 1105, "DefaultHeight": 737, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/ie9eti6w4zfcs.png"}, {"GameType": "Slot", "GameCode": "o7f9ih8t6559e", "GameName": "Empress Regnant", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 154, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/o7f9ih8t6559e.png"}, {"GameType": "Slot", "GameCode": "a7q65cfts455e", "GameName": "Ong Bak 2", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 155, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/a7q65cfts455e.png"}, {"GameType": "Slot", "GameCode": "6o5emdcnoqyen", "GameName": "Aztec Temple", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 156, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/6o5emdcnoqyen.png"}, {"GameType": "Slot", "GameCode": "dxxsh3dfmjpio", "GameName": "Tai Shang Lao Jun", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 157, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/dxxsh3dfmjpio.png"}, {"GameType": "Slot", "GameCode": "47g95efbw4u4e", "GameName": "Heist", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 158, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/47g95efbw4u4e.png"}, {"GameType": "Fishing", "GameCode": "p63ornyjba8oa", "GameName": "Fishermans Wharf", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 159, "DefaultWidth": 1280, "DefaultHeight": 720, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/p63ornyjba8oa.png"}, {"GameType": "Slot", "GameCode": "jbzd1cjsgh4dk", "GameName": "Sparta", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 160, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/jbzd1cjsgh4dk.png"}, {"GameType": "Slot", "GameCode": "kia1eetdryo1c", "GameName": "<PERSON>", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 161, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/kia1eetdryo1c.png"}, {"GameType": "Slot", "GameCode": "uwf5zss55dc7h", "GameName": "<PERSON><PERSON>", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 162, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/uwf5zss55dc7h.png"}, {"GameType": "Slot", "GameCode": "nh9swadbc3use", "GameName": "HighwayKings JP", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 163, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/nh9swadbc3use.png"}, {"GameType": "Slot", "GameCode": "x46x869a989x6", "GameName": "Fat Choy Choy Sun", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 164, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/x46x869a989x6.png"}, {"GameType": "Slot", "GameCode": "5864tji8w113w", "GameName": "Thai Paradise", "SupportedPlatForms": "Desktop,Mobile", "Specials": null, "Technology": null, "Order": 165, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/5864tji8w113w.png"}, {"GameType": "Slot", "GameCode": "9mqe9bhroi78s", "GameName": "Golden Monkey King", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 166, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/9mqe9bhroi78s.png"}, {"GameType": "Slot", "GameCode": "fk9yoi4wkifrs", "GameName": "Fifty Lions", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 167, "DefaultWidth": 1105, "DefaultHeight": 737, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/fk9yoi4wkifrs.png"}, {"GameType": "Slot", "GameCode": "rsjogw1ukbeic", "GameName": "Four Tigers", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 168, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/rsjogw1ukbeic.png"}, {"GameType": "Slot", "GameCode": "6po7ddrpokbay", "GameName": "Alice In Wonderland", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 169, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/6po7ddrpokbay.png"}, {"GameType": "Slot", "GameCode": "foff4ikkjprr1", "GameName": "Water Margin", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 170, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/foff4ikkjprr1.png"}, {"GameType": "Slot", "GameCode": "e5jgac3ogr5dq", "GameName": "Ranchers Wealth", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 171, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/e5jgac3ogr5dq.png"}, {"GameType": "Slot", "GameCode": "kxyznmbpret1y", "GameName": "Enchanted Forest", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 172, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/kxyznmbpret1y.png"}, {"GameType": "Slot", "GameCode": "j6j1rkbjfaz1a", "GameName": "Five Tiger Generals", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 173, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/j6j1rkbjfaz1a.png"}, {"GameType": "Slot", "GameCode": "yxdzc9d7qj3zy", "GameName": "Fire Reel", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 174, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/yxdzc9d7qj3zy.png"}, {"GameType": "Slot", "GameCode": "h33c3rho1gmjq", "GameName": "Streets Of Chicago", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 175, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/h33c3rho1gmjq.png"}, {"GameType": "Slot", "GameCode": "xbxy1yegyhnyk", "GameName": "Jungle Island", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 176, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/xbxy1yegyhnyk.png"}, {"GameType": "Slot", "GameCode": "rqaonzn7kjjiy", "GameName": "The Four Invention", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 177, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/rqaonzn7kjjiy.png"}, {"GameType": "Slot", "GameCode": "69xaiyrbo4dae", "GameName": "A Night Out", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 178, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/69xaiyrbo4dae.png"}, {"GameType": "Slot", "GameCode": "99bzr6d3x39za", "GameName": "<PERSON>", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 179, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/99bzr6d3x39za.png"}, {"GameType": "Slot", "GameCode": "qxoi<PERSON><PERSON>eb<PERSON>", "GameName": "Geisha", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 180, "DefaultWidth": 1105, "DefaultHeight": 737, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/qxoindypyeboy.png"}, {"GameType": "Slot", "GameCode": "yqe1n9d7qj3zy", "GameName": "Three Kingdoms 2", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 181, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/yqe1n9d7qj3zy.png"}, {"GameType": "Slot", "GameCode": "fqho1inijjfwo", "GameName": "Dragon Of The Eastern Sea", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 183, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/fqho1inijjfwo.png"}, {"GameType": "Slot", "GameCode": "i4rc816e388c6", "GameName": "<PERSON>", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 184, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/i4rc816e388c6.png"}, {"GameType": "Slot", "GameCode": "abkqpqp6z66m4", "GameName": "Santa Workshop", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 185, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/abkqpqp6z66m4.png"}, {"GameType": "Fishing", "GameCode": "g54rso4yefdrq", "GameName": "Fish Hunter 2 EX - Pro", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 186, "DefaultWidth": 1280, "DefaultHeight": 720, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/g54rso4yefdrq.png"}, {"GameType": "Slot", "GameCode": "z1pc5tp4zqhm1", "GameName": "Silver Bullet", "SupportedPlatForms": "Desktop,Mobile", "Specials": null, "Technology": null, "Order": 187, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/z1pc5tp4zqhm1.png"}, {"GameType": "Slot", "GameCode": "jzpssktmfyw1h", "GameName": "Zodiac", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 188, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/jzpssktmfyw1h.png"}, {"GameType": "Slot", "GameCode": "qieoeyodyyyoc", "GameName": "Captain's Treasure Pro", "SupportedPlatForms": "Desktop,Mobile", "Specials": null, "Technology": null, "Order": 189, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/qieoeyodyyyoc.png"}, {"GameType": "Slot", "GameCode": "cd4rcge6dhqb4", "GameName": "Dia De Los Muertos", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 190, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/cd4rcge6dhqb4.png"}, {"GameType": "Slot", "GameCode": "bzgza4umpbwsh", "GameName": "Third Prince's Journey", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 191, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/bzgza4umpbwsh.png"}, {"GameType": "Slot", "GameCode": "ateqfxp1sqamn", "GameName": "Dolphin Treasure", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 192, "DefaultWidth": 1105, "DefaultHeight": 737, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/ateqfxp1sqamn.png"}, {"GameType": "Slot", "GameCode": "x5ikj69a989x6", "GameName": "Gold Trail", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 193, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/x5ikj69a989x6.png"}, {"GameType": "Slot", "GameCode": "pd6rhresnhkbk", "GameName": "Shaolin", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 194, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/pd6rhresnhkbk.png"}, {"GameType": "Slot", "GameCode": "exesnxb7ge3uy", "GameName": "Haunted House", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 195, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/exesnxb7ge3uy.png"}, {"GameType": "Slot", "GameCode": "84igeq3a8r9d6", "GameName": "Nugget Hunter", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 196, "DefaultWidth": 1105, "DefaultHeight": 737, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/84igeq3a8r9d6.png"}, {"GameType": "Slot", "GameCode": "5ypkuepgw61kk", "GameName": "Water Reel", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 197, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/5ypkuepgw61kk.png"}, {"GameType": "Slot", "GameCode": "8nsbhokge7nrk", "GameName": "Queen Of The Nile", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 198, "DefaultWidth": 1105, "DefaultHeight": 737, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/8nsbhokge7nrk.png"}, {"GameType": "Slot", "GameCode": "dkzdo35rcipfs", "GameName": "China", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 199, "DefaultWidth": 1680, "DefaultHeight": 1050, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/dkzdo35rcipfs.png"}, {"GameType": "Slot", "GameCode": "itzp5iqk3xrc1", "GameName": "Wild Spirit", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 200, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/itzp5iqk3xrc1.png"}, {"GameType": "Fishing", "GameCode": "ary5bxi9z165r", "GameName": "Fish Hunter 2 EX - My Club", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 201, "DefaultWidth": 1280, "DefaultHeight": 720, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/ary5bxi9z165r.png"}, {"GameType": "Slot", "GameCode": "nqyun5dpcjtsy", "GameName": "Cyber Race", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 202, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/nqyun5dpcjtsy.png"}, {"GameType": "Slot", "GameCode": "b5ggg45epfzg6", "GameName": "Super Stars", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 203, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/b5ggg45epfzg6.png"}, {"GameType": "Slot", "GameCode": "tqi9778i7mi6o", "GameName": "Miami", "SupportedPlatForms": "Desktop,Mobile", "Specials": "", "Technology": null, "Order": 204, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/tqi9778i7mi6o.png"}, {"GameType": "Slot", "GameCode": "s6xhiogba5dhe", "GameName": "Football Rules", "SupportedPlatForms": "Desktop,Mobile", "Specials": null, "Technology": null, "Order": 205, "DefaultWidth": 960, "DefaultHeight": 600, "ImageIcon": "//img.zhenqinghua.com/gameimages/landscape/s6xhiogba5dhe.png"}]