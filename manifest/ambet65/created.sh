kube<PERSON>l create ns ambet65
# -----------
kube<PERSON>l create secret docker-registry ambet65-registrykey --docker-username=cyberrich --docker-password=************************************ --docker-email=<EMAIL> --namespace=ambet65
kubectl create secret generic google-service-account-volume --namespace=ambet65 --from-file=google_service_account.json=google_service_account.json
kubectl create secret generic ambet65-api-env --namespace=ambet65 --from-env-file=.env

# -----------
kubectl apply -f .\service.yaml
kubectl apply -f .\ingress.yaml
kubectl apply -f .\deployment.yaml

# ------------

kubectl apply -f .\manifest\ambet65\cronJobs\cutDaily.yaml
kubectl apply -f .\manifest\ambet65\cronJobs\simplewinlose.yaml