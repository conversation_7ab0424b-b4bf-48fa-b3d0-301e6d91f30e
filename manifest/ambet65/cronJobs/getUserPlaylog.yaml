apiVersion: batch/v1
kind: CronJob
metadata:
  name: ambet65-get-user-playlog
  namespace: ambet65
spec:
  schedule: "* * * * *"
  successfulJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: callout
              image: buildpack-deps:curl
              imagePullPolicy: IfNotPresent
              command:
                - /bin/sh
                - -ec
                - curl -X GET https://api.ambet65.com/api/cronjobs/get-user-playlog
          restartPolicy: Never
