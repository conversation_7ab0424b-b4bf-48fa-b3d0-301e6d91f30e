apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-ambet65-api
  namespace: ambet65
spec:
  rules:
    - host: api.ambet65.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: ambet65-api
                port:
                  number: 3000
    - host: api.akrich88.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: ambet65-api
                port:
                  number: 3000
    - host: api.baga789.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: ambet65-api
                port:
                  number: 3000

  ingressClassName: nginx
