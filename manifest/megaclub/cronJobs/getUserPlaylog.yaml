apiVersion: batch/v1
kind: CronJob
metadata:
  name: megaclub-get-user-playlog
  namespace: megaclub
spec:
  schedule: "* * * * *"
  successfulJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: callout
              image: buildpack-deps:curl
              imagePullPolicy: IfNotPresent
              command:
                - /bin/sh
                - -ec
                - curl -X GET https://api.megaclub.pro/api/cronjobs/get-user-playlog
          restartPolicy: Never
