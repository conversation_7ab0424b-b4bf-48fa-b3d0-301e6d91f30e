apiVersion: batch/v1
kind: CronJob
metadata:
  name: megaclub-cut-daily
  namespace: megaclub
spec:
  schedule: "* * * * *"
  successfulJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: callout
              image: buildpack-deps:curl
              imagePullPolicy: IfNotPresent
              command:
                - /bin/sh
                - -ec
                - curl -X GET https://api.megaclub.pro/api/cron/promotion-return-loser/cut-daily
          restartPolicy: Never