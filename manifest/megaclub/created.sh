kube<PERSON>l create ns megaclub
# -----------
kube<PERSON>l create secret docker-registry megaclub-registrykey --docker-username=cyberrich --docker-password=************************************ --docker-email=<EMAIL> --namespace=cybergame-prod
kubectl create secret generic google-service-account-volume --namespace=megaclub --from-file=google_service_account.json=google_service_account.json
kubectl create secret generic megaclub-api-env --namespace=megaclub --from-env-file=.env

# -----------
kubectl apply -f .\service.yaml
kubectl apply -f .\ingress.yaml
kubectl apply -f .\deployment.yaml

# ------------

kubectl apply -f .\manifest\megaclub\cronJobs\cutDaily.yaml
kubectl apply -f .\manifest\megaclub\cronJobs\simplewinlose.yaml

# ------------ env pull vault script
./vault.sh https://vault.cyberrichdigital.com/v1/cybergame-prod/data/megaclub.top/api/.env env hvs.CAESINm-6Pav7HuhGZZEO9sZs5NerVnHVu_0VH6nJD2r3N1ZGh4KHGh2cy5GV0F3eVJ6TzlIWkpKR3dmcEFZWFVUNWo manifest/megaclub