$secret = kubectl get secret megaclub-web-env -n megaclub -o json | ConvertFrom-Json
$secret.data.psobject.Properties | ForEach-Object {
    $key = $_.Name
    $value = $_.Value
    $decoded = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($value))
    Write-Output "$key=$decoded"
}

# old backup
# NEXTAUTH_SECRET=MFIyZ3w0qo7Y5bAVu9hjHDJS2PEcqViflkrzYPVC1Po=
# NEXTAUTH_URL=https://megaclub1122.com
# NEXT_PUBLIC_API_ENDPOINT=https://api.megaclub1122.com/api
# NEXT_PUBLIC_SOCKET_ENDPOINT=https://socket.megaclub1122.com
# NEXT_PUBLIC_WEB_NAME=megaclub
# NEXT_PUBLIC_WEB_THEME_IMAGE=set04
# NEXT_PUBLIC_WEB_THEME_SWIPER=set01