apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-megaclub-api
  namespace: megaclub
spec:
  rules:
    - host: api.megaclub.pro
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: megaclub-api
                port:
                  number: 3000
    - host: api.megaclub1122.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: megaclub-api
                port:
                  number: 3000
    - host: api.megaclubstory.online
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: megaclub-api
                port:
                  number: 3000
    - host: api.megaclubpremium.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: megaclub-api
                port:
                  number: 3000
    - host: api.megaclubexpert.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: megaclub-api
                port:
                  number: 3000

  ingressClassName: nginx
