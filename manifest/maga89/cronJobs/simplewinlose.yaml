apiVersion: batch/v1
kind: CronJob
metadata:
  name: maga89-simplewinlose
  namespace: maga89
spec:
  schedule: "30 05 * * *"
  successfulJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: callout
              image: buildpack-deps:curl
              imagePullPolicy: IfNotPresent
              command:
                - /bin/sh
                - -ec
                - curl -X GET https://api.maga89.net/api/cronjobs/simplewinlose
          restartPolicy: Never
