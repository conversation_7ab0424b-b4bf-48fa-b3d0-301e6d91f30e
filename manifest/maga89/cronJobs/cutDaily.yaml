apiVersion: batch/v1
kind: CronJob
metadata:
  name: maga89-cut-daily
  namespace: maga89
spec:
  schedule: "* * * * *"
  successfulJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: callout
              image: buildpack-deps:curl
              imagePullPolicy: IfNotPresent
              command:
                - /bin/sh
                - -ec
                - curl -X GET https://api.maga89.net/api/cron/promotion-return-loser/cut-daily
          restartPolicy: Never