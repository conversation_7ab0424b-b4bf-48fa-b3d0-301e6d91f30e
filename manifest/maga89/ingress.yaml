apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-maga89-api
  namespace: maga89
spec:
  rules:
    - host: api.maga89.net
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: maga89-api
                port:
                  number: 3000
    - host: api.meetang.fun
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: maga89-api
                port:
                  number: 3000

  ingressClassName: nginx
