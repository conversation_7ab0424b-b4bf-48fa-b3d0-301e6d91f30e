kube<PERSON>l create ns maga89
# -----------
kube<PERSON>l create secret docker-registry maga89-registrykey --docker-username=cyberrich --docker-password=************************************ --docker-email=<EMAIL> --namespace=maga89
kubectl create secret generic google-service-account-volume --namespace=maga89 --from-file=google_service_account.json=google_service_account.json
kubectl create secret generic maga89-api-env --namespace=maga89 --from-env-file=.env

# -----------
kubectl apply -f .\service.yaml
kubectl apply -f .\ingress.yaml
kubectl apply -f .\deployment.yaml

# ------------

kubectl apply -f .\manifest\maga89\cronJobs\cutDaily.yaml
kubectl apply -f .\manifest\maga89\cronJobs\simplewinlose.yaml