apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-topgame789-api
  namespace: topgame789
spec:
  rules:
    - host: api.topgame789.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: topgame789-api
                port:
                  number: 3000
    - host: api.lisbao789.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: topgame789-api
                port:
                  number: 3000
                  
  ingressClassName: nginx
