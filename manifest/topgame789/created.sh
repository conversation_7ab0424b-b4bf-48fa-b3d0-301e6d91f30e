kubectl create ns topgame789
# -----------
kube<PERSON>l create secret docker-registry topgame789-registrykey --docker-username=cyberrich --docker-password=************************************ --docker-email=<EMAIL> --namespace=topgame789
kubectl create secret generic google-service-account-volume --namespace=topgame789 --from-file=google_service_account.json=google_service_account.json
kubectl create secret generic topgame789-api-env --namespace=topgame789 --from-env-file=.env

# -----------
kubectl apply -f .\service.yaml
kubectl apply -f .\ingress.yaml
kubectl apply -f .\deployment.yaml

# ------------

kubectl apply -f .\manifest\topgame789\cronJobs\cutDaily.yaml
kubectl apply -f .\manifest\topgame789\cronJobs\simplewinlose.yaml
kubectl apply -f .\manifest\topgame789\cronJobs\getUserPlaylog.yaml