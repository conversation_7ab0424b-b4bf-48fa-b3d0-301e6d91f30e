apiVersion: batch/v1
kind: CronJob
metadata:
  name: baza888-cut-daily
  namespace: baza888
spec:
  schedule: "* * * * *"
  successfulJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: callout
              image: buildpack-deps:curl
              imagePullPolicy: IfNotPresent
              command:
                - /bin/sh
                - -ec
                - curl -X GET https://api.baza888.net/api/cron/promotion-return-loser/cut-daily
          restartPolicy: Never