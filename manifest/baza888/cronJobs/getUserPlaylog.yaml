apiVersion: batch/v1
kind: CronJob
metadata:
  name: baza888-get-user-playlog
  namespace: baza888
spec:
  schedule: "* * * * *"
  successfulJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: callout
              image: buildpack-deps:curl
              imagePullPolicy: IfNotPresent
              command:
                - /bin/sh
                - -ec
                - curl -X GET https://api.baza888.net/api/cronjobs/get-user-playlog
          restartPolicy: Never
