$secret = kubectl get secret baza888-web-env -n baza888 -o json | ConvertFrom-Json
$secret.data.psobject.Properties | ForEach-Object {
    $key = $_.Name
    $value = $_.Value
    $decoded = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($value))
    Write-Output "$key=$decoded"
}

# old backup
# NEXTAUTH_SECRET=R+7Y/a5wHb/oHNizkh8KX/GKm6mtgtKATaaQJ1ZfDZ4=
# NEXTAUTH_URL=https://baza888.net
# NEXT_PUBLIC_API_ENDPOINT=https://api.baza888.net/api
# NEXT_PUBLIC_SOCKET_ENDPOINT=https://socket.baza888.net
# NEXT_PUBLIC_WEB_NAME=baza888
# NEXT_PUBLIC_WEB_THEME_IMAGE=set02
# NEXT_PUBLIC_WEB_THEME_SWIPER=baza888