kubectl delete secret allin98-api-env -n allin98
kubectl create secret generic allin98-api-env --namespace=allin98 --from-env-file=./manifest/allin98/.env

kubectl delete secret atmplus-api-env -n atmplus
kubectl create secret generic atmplus-api-env --namespace=atmplus --from-env-file=./manifest/atmplus/.env

kubectl delete secret autogame-api-env -n autogame
kubectl create secret generic autogame-api-env --namespace=autogame --from-env-file=./manifest/autogame/.env

kubectl delete secret autoslot168-api-env -n autoslot168
kubectl create secret generic autoslot168-api-env --namespace=autoslot168 --from-env-file=./manifest/autoslot168/.env

kubectl delete secret baza888-api-env -n baza888
kubectl create secret generic baza888-api-env --namespace=baza888 --from-env-file=./manifest/baza888/.env

kubectl delete secret brb88bet-api-env -n brb88bet
kubectl create secret generic brb88bet-api-env --namespace=brb88bet --from-env-file=./manifest/brb88bet/.env

kubectl delete secret brobet-api-env -n brobet
kubectl create secret generic brobet-api-env --namespace=brobet --from-env-file=./manifest/brobet/.env

kubectl delete secret cnone-api-env -n cnone
kubectl create secret generic cnone-api-env --namespace=cnone --from-env-file=./manifest/cnone/.env

kubectl delete secret eleven111-api-env -n eleven111
kubectl create secret generic eleven111-api-env --namespace=eleven111 --from-env-file=./manifest/eleven111/.env

kubectl delete secret ezrich-api-env -n ezrich
kubectl create secret generic ezrich-api-env --namespace=ezrich --from-env-file=./manifest/ezrich/.env

kubectl delete secret gm1-api-env -n gm1
kubectl create secret generic gm1-api-env --namespace=gm1 --from-env-file=./manifest/gm1/.env

kubectl delete secret goldclub9-api-env -n goldclub9
kubectl create secret generic goldclub9-api-env --namespace=goldclub9 --from-env-file=./manifest/goldclub9/.env

kubectl delete secret kaya68-api-env -n kaya68
kubectl create secret generic kaya68-api-env --namespace=kaya68 --from-env-file=./manifest/kaya68/.env

kubectl delete secret maxnum168-api-env -n maxnum168
kubectl create secret generic maxnum168-api-env --namespace=maxnum168 --from-env-file=./manifest/maxnum168/.env

kubectl delete secret megaclub-api-env -n megaclub
kubectl create secret generic megaclub-api-env --namespace=megaclub --from-env-file=./manifest/megaclub/.env

kubectl delete secret mk88max-api-env -n cybergame-prod
kubectl create secret generic mk88max-api-env --namespace=cybergame-prod --from-env-file=./manifest/mk88max/.env

kubectl delete secret mr89-api-env -n mr89
kubectl create secret generic mr89-api-env --namespace=mr89 --from-env-file=./manifest/mr89/.env

kubectl delete secret riverclub-api-env -n riverclub
kubectl create secret generic riverclub-api-env --namespace=riverclub --from-env-file=./manifest/riverclub/.env

kubectl delete secret royal447-api-env -n royal447
kubectl create secret generic royal447-api-env --namespace=royal447 --from-env-file=./manifest/royal447/.env

kubectl delete secret s7luck-api-env -n s7luck
kubectl create secret generic s7luck-api-env --namespace=s7luck --from-env-file=./manifest/s7luck/.env

kubectl delete secret therich333-api-env -n therich333
kubectl create secret generic therich333-api-env --namespace=therich333 --from-env-file=./manifest/therich333/.env

kubectl delete secret topsure168-api-env -n topsure168
kubectl create secret generic topsure168-api-env --namespace=topsure168 --from-env-file=./manifest/topsure168/.env

kubectl delete secret uk888club-api-env -n uk888club
kubectl create secret generic uk888club-api-env --namespace=uk888club --from-env-file=./manifest/uk888club/.env

kubectl delete secret utaslot-api-env -n utaslot
kubectl create secret generic utaslot-api-env --namespace=utaslot --from-env-file=./manifest/utaslot/.env

# kubectl delete secret w4bar88-api-env -n w4bar88
# kubectl create secret generic w4bar88-api-env --namespace=w4bar88 --from-env-file=./manifest/w4bar88/.env

kubectl delete secret warior88-api-env -n warior88
kubectl create secret generic warior88-api-env --namespace=warior88 --from-env-file=./manifest/warior88/.env

kubectl delete secret wowgame88-api-env -n wowgame88
kubectl create secret generic wowgame88-api-env --namespace=wowgame88 --from-env-file=./manifest/wowgame88/.env

kubectl delete secret topgame789-api-env -n topgame789
kubectl create secret generic topgame789-api-env --namespace=topgame789 --from-env-file=./manifest/topgame789/.env

kubectl delete secret bbmslot-api-env -n bbmslot
kubectl create secret generic bbmslot-api-env --namespace=bbmslot --from-env-file=./manifest/bbmslot/.env

kubectl delete secret rogerclub-api-env -n rogerclub
kubectl create secret generic rogerclub-api-env --namespace=rogerclub --from-env-file=./manifest/rogerclub/.env

kubectl delete secret ambet65-api-env -n ambet65
kubectl create secret generic ambet65-api-env --namespace=ambet65 --from-env-file=./manifest/ambet65/.env

kubectl delete secret maga89-api-env -n maga89
kubectl create secret generic maga89-api-env --namespace=maga89 --from-env-file=./manifest/maga89/.env

kubectl delete secret paplern999-api-env -n paplern999
kubectl create secret generic paplern999-api-env --namespace=paplern999 --from-env-file=./manifest/paplern999/.env

kubectl delete secret a4u-api-env -n a4u
kubectl create secret generic a4u-api-env --namespace=a4u --from-env-file=./manifest/a4u/.env

kubectl delete secret showpow777-api-env -n showpow777
kubectl create secret generic showpow777-api-env --namespace=showpow777 --from-env-file=./manifest/showpow777/.env

kubectl delete secret fivedragon-api-env -n fivedragon
kubectl create secret generic fivedragon-api-env --namespace=fivedragon --from-env-file=./manifest/fivedragon/.env

kubectl delete secret mixxway-api-env -n mixxway
kubectl create secret generic mixxway-api-env --namespace=mixxway --from-env-file=./manifest/mixxway/.env

kubectl delete secret pgautoall-api-env -n pgautoall
kubectl create secret generic pgautoall-api-env --namespace=pgautoall --from-env-file=./manifest/pgautoall/.env

kubectl delete secret luxxplay-api-env -n luxxplay
kubectl create secret generic luxxplay-api-env --namespace=luxxplay --from-env-file=./manifest/luxxplay/.env

kubectl delete secret rabbitrich-api-env -n rabbitrich
kubectl create secret generic rabbitrich-api-env --namespace=rabbitrich --from-env-file=./manifest/rabbitrich/.env

kubectl delete secret uca999-api-env -n uca999
kubectl create secret generic uca999-api-env --namespace=uca999 --from-env-file=./manifest/uca999/.env

kubectl delete secret supmongkol-api-env -n supmongkol
kubectl create secret generic supmongkol-api-env --namespace=supmongkol --from-env-file=./manifest/supmongkol/.env

kubectl delete secret meechok789-api-env -n meechok789
kubectl create secret generic meechok789-api-env --namespace=meechok789 --from-env-file=./manifest/meechok789/.env

kubectl delete secret urwin888-api-env -n urwin888
kubectl create secret generic urwin888-api-env --namespace=urwin888 --from-env-file=./manifest/urwin888/.env

kubectl delete secret winlotto555-api-env -n winlotto555
kubectl create secret generic winlotto555-api-env --namespace=winlotto555 --from-env-file=./manifest/winlotto555/.env

kubectl delete secret setthisiam-api-env -n setthisiam
kubectl create secret generic setthisiam-api-env --namespace=setthisiam --from-env-file=./manifest/setthisiam/.env

kubectl delete secret thluckyrich-api-env -n thluckyrich
kubectl create secret generic thluckyrich-api-env --namespace=thluckyrich --from-env-file=./manifest/thluckyrich/.env

kubectl delete secret luckydream-api-env -n luckydream
kubectl create secret generic luckydream-api-env --namespace=luckydream --from-env-file=./manifest/luckydream/.env

kubectl delete secret onebet24hr-api-env -n onebet24hr
kubectl create secret generic onebet24hr-api-env --namespace=onebet24hr --from-env-file=./manifest/onebet24hr/.env

kubectl delete secret playgold5-api-env -n playgold5
kubectl create secret generic playgold5-api-env --namespace=playgold5 --from-env-file=./manifest/playgold5/.env
