$secret = kubectl get secret therich333-web-env -n therich333 -o json | ConvertFrom-Json
$secret.data.psobject.Properties | ForEach-Object {
    $key = $_.Name
    $value = $_.Value
    $decoded = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($value))
    Write-Output "$key=$decoded"
}

# old backup
# NEXTAUTH_SECRET=x8jgqOv4ZyV6c+KVArRnvDreLlbVLPigbpXgo4tCWcg=
# NEXTAUTH_URL=https://333therich.com
# NEXT_PUBLIC_API_ENDPOINT=https://api.333therich.com/api
# NEXT_PUBLIC_SOCKET_ENDPOINT=https://socket.333therich.com
# NEXT_PUBLIC_WEB_NAME=therich333
# NEXT_PUBLIC_WEB_THEME_IMAGE=set08
# NEXT_PUBLIC_WEB_THEME_SWIPER=set02