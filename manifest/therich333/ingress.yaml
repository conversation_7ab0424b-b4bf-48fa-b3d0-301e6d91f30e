apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-therich333-api
  namespace: therich333
spec:
  rules:
    - host: api.therich333.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: therich333-api
                port:
                  number: 3000
    - host: api.333therich.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: therich333-api
                port:
                  number: 3000
    - host: api.dida.center
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: therich333-api
                port:
                  number: 3000

  ingressClassName: nginx
