apiVersion: batch/v1
kind: CronJob
metadata:
  name: therich333-get-user-playlog
  namespace: therich333
spec:
  schedule: "* * * * *"
  successfulJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: callout
              image: buildpack-deps:curl
              imagePullPolicy: IfNotPresent
              command:
                - /bin/sh
                - -ec
                - curl -X GET https://api.therich333.com/api/cronjobs/get-user-playlog
          restartPolicy: Never
