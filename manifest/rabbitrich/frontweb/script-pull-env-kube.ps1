$secret = kubectl get secret rabbitrich-web-env -n rabbitrich -o json | ConvertFrom-Json
$secret.data.psobject.Properties | ForEach-Object {
    $key = $_.Name
    $value = $_.Value
    $decoded = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($value))
    Write-Output "$key=$decoded"
}

# old backup
# NEXTAUTH_SECRET=NbPxaY0apEpwKINKGiv76gKA8ym6oo/cplm7XIYO6hI=
# NEXTAUTH_URL=https://rabbitrich.net
# NEXT_PUBLIC_API_ENDPOINT=https://api.rabbitrich.net/api
# NEXT_PUBLIC_SOCKET_ENDPOINT=https://socket.rabbitrich.net
# NEXT_PUBLIC_WEB_NAME=rabbits2
# NEXT_PUBLIC_WEB_THEME_IMAGE=set02