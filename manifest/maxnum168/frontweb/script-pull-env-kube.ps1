$secret = kubectl get secret maxnum168-web-env -n maxnum168 -o json | ConvertFrom-Json
$secret.data.psobject.Properties | ForEach-Object {
    $key = $_.Name
    $value = $_.Value
    $decoded = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($value))
    Write-Output "$key=$decoded"
}

# old backup
# NEXTAUTH_SECRET=m2a9j6FSKNQV0jbhUK63TJ8oo5rRorNnBPu6LzqBQwk=
# NEXTAUTH_URL=https://maxnum168.net
# NEXT_PUBLIC_API_ENDPOINT=https://api.maxnum168.net/api
# NEXT_PUBLIC_SOCKET_ENDPOINT=https://socket.maxnum168.net/
# NEXT_PUBLIC_WEB_NAME=maxnum168
# NEXT_PUBLIC_WEB_THEME_IMAGE=set06
# NEXT_PUBLIC_WEB_THEME_SWIPER=maxnum168