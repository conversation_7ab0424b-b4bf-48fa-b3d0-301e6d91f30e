kube<PERSON>l create ns pgautoall
# -----------
kube<PERSON>l create secret docker-registry pgautoall-registrykey --docker-username=cyberrich --docker-password=************************************ --docker-email=<EMAIL> --namespace=pgautoall
kubectl create secret generic google-service-account-volume --namespace=pgautoall --from-file=google_service_account.json=google_service_account.json
kubectl create secret generic pgautoall-api-env --namespace=pgautoall --from-env-file=.env

# -----------
kubectl apply -f .\service.yaml
kubectl apply -f .\ingress.yaml
kubectl apply -f .\deployment.yaml

# ------------

kubectl apply -f .\manifest\pgautoall\cronJobs\cutDaily.yaml
kubectl apply -f .\manifest\pgautoall\cronJobs\simplewinlose.yaml

# ------------ env pull vault script
./vault.sh https://vault.cyberrichdigital.com/v1/cybergame-prod/data/pgautoall/api/.env env hvs.CAESIEzvECIz6z-g4Ll9ftD-1Q_AUA6IhBIk9tuyAOk4BX3MGh4KHGh2cy54WHV0Z0tycm9mbE5RaVhCckZQc3MxalE manifest/pgautoall