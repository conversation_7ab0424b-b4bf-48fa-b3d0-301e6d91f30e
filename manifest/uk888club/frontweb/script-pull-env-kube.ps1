$secret = kubectl get secret uk888club-web-env -n uk888club -o json | ConvertFrom-Json
$secret.data.psobject.Properties | ForEach-Object {
    $key = $_.Name
    $value = $_.Value
    $decoded = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($value))
    Write-Output "$key=$decoded"
}

# old backup
# NEXTAUTH_SECRET=1n3B/cGoyr43cvRWLqO533/TA/E9WpUAp6GwFdek154=
# NEXTAUTH_URL=https://uk888club.net
# NEXT_PUBLIC_API_ENDPOINT=https://api.uk888club.net/api
# NEXT_PUBLIC_SOCKET_ENDPOINT=https://socket.uk888club.net
# NEXT_PUBLIC_WEB_NAME=uk888club
# NEXT_PUBLIC_WEB_THEME_IMAGE=set03
# NEXT_PUBLIC_WEB_THEME_SWIPER=uk888club