apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-uk888club-api
  namespace: uk888club
spec:
  rules:
    - host: api.uk888club.net
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: uk888club-api
                port:
                  number: 3000
    - host: api.uk888club.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: uk888club-api
                port:
                  number: 3000

  ingressClassName: nginx
