apiVersion: batch/v1
kind: CronJob
metadata:
  name: eleven111-get-user-playlog
  namespace: eleven111
spec:
  schedule: "* * * * *"
  successfulJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: callout
              image: buildpack-deps:curl
              imagePullPolicy: IfNotPresent
              command:
                - /bin/sh
                - -ec
                - curl -X GET https://api.111pgtime.com/api/cronjobs/get-user-playlog
          restartPolicy: Never
