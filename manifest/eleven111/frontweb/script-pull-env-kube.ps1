$secret = kubectl get secret eleven111-web-env -n eleven111 -o json | ConvertFrom-Json
$secret.data.psobject.Properties | ForEach-Object {
    $key = $_.Name
    $value = $_.Value
    $decoded = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($value))
    Write-Output "$key=$decoded"
}

# old backup
# NEXTAUTH_SECRET=MFIyZ3w0qo7Y5bAVu9hjHDJS2PEcqViflkrzYPVC1Po=
# NEXTAUTH_URL=https://111mgm.co
# NEXT_LINE_CALLBACK_URL=https://111mgm.co/api/auth/callback/line
# NEXT_LINE_CLIENT_ID=2003244568
# NEXT_LINE_CLIENT_SECRET=65fc0bf273a24b4c96e2610a97d66c7f
# NEXT_PUBLIC_API_ENDPOINT=https://api.111mgm.co/api
# NEXT_PUBLIC_LINE_HAS=true
# NEXT_PUBLIC_SOCKET_ENDPOINT=https://socket.111mgm.co
# NEXT_PUBLIC_WEB_NAME=111mgm
# NEXT_PUBLIC_WEB_THEME_IMAGE=set07
# NEXT_PUBLIC_WEB_THEME_SWIPER=set01