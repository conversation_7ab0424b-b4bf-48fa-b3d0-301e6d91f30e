apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-eleven111-api
  namespace: eleven111
spec:
  rules:
    - host: api.eleven111.net
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: eleven111-api
                port:
                  number: 3000
    - host: api.111pgtime.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: eleven111-api
                port:
                  number: 3000

  ingressClassName: nginx
