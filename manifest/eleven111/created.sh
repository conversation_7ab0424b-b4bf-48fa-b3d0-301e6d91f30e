kube<PERSON>l create ns eleven111
# -----------
kube<PERSON>l create secret docker-registry eleven111-registrykey --docker-username=cyberrich --docker-password=************************************ --docker-email=<EMAIL> --namespace=eleven111
kubectl create secret generic google-service-account-volume --namespace=eleven111 --from-file=google_service_account.json=google_service_account.json
kubectl create secret generic eleven111-api-env --namespace=eleven111 --from-env-file=.env

# -----------
kubectl apply -f .\service.yaml
kubectl apply -f .\ingress.yaml
kubectl apply -f .\deployment.yaml

# ------------

kubectl apply -f .\manifest\eleven111\cronJobs\cutDaily.yaml
kubectl apply -f .\manifest\eleven111\cronJobs\simplewinlose.yaml

# ------------ env pull vault script
./vault.sh https://vault.cyberrichdigital.com/v1/cybergame-prod/data/eleven111.top/api/.env env hvs.CAESINm-6Pav7HuhGZZEO9sZs5NerVnHVu_0VH6nJD2r3N1ZGh4KHGh2cy5GV0F3eVJ6TzlIWkpKR3dmcEFZWFVUNWo manifest/eleven111