$secret = kubectl get secret kaya68-web-env -n kaya68 -o json | ConvertFrom-Json
$secret.data.psobject.Properties | ForEach-Object {
    $key = $_.Name
    $value = $_.Value
    $decoded = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($value))
    Write-Output "$key=$decoded"
}

# old backup
# NEXTAUTH_SECRET=c5NaDyuPmBvjUQW3o+6gdWLZjAkJyp/mS6rZyc3wq4c=
# NEXTAUTH_URL=https://kaya65.com
# NEXT_PUBLIC_API_ENDPOINT=https://api.kaya65.com/api
# NEXT_PUBLIC_SOCKET_ENDPOINT=https://socket.kaya65.com
# NEXT_PUBLIC_WEB_NAME=kaya68
# NEXT_PUBLIC_WEB_THEME_IMAGE=set08
# NEXT_PUBLIC_WEB_THEME_SWIPER=set02