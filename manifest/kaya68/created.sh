kube<PERSON>l create ns kaya68
# -----------
kube<PERSON>l create secret docker-registry kaya68-registrykey --docker-username=cyberrich --docker-password=************************************ --docker-email=<EMAIL> --namespace=cybergame-prod
kubectl create secret generic google-service-account-volume --namespace=kaya68 --from-file=google_service_account.json=google_service_account.json
kubectl create secret generic kaya68-api-env --namespace=kaya68 --from-env-file=.env

# -----------
kubectl apply -f .\service.yaml
kubectl apply -f .\ingress.yaml
kubectl apply -f .\deployment.yaml

# ------------

kubectl apply -f .\manifest\kaya68\cronJobs\cutDaily.yaml
kubectl apply -f .\manifest\kaya68\cronJobs\simplewinlose.yaml

# ------------ env pull vault script
./vault.sh https://vault.cyberrichdigital.com/v1/cybergame-prod/data/kaya68.top/api/.env env hvs.CAESINm-6Pav7HuhGZZEO9sZs5NerVnHVu_0VH6nJD2r3N1ZGh4KHGh2cy5GV0F3eVJ6TzlIWkpKR3dmcEFZWFVUNWo manifest/kaya68