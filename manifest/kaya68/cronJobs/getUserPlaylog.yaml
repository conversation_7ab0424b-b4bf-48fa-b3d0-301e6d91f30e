apiVersion: batch/v1
kind: CronJob
metadata:
  name: kaya68-get-user-playlog
  namespace: kaya68
spec:
  schedule: "* * * * *"
  successfulJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: callout
              image: buildpack-deps:curl
              imagePullPolicy: IfNotPresent
              command:
                - /bin/sh
                - -ec
                - curl -X GET https://api.kaya68.com/api/cronjobs/get-user-playlog
          restartPolicy: Never
