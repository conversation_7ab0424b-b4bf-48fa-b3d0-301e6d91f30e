apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-kaya68-api
  namespace: kaya68
spec:
  rules:
    - host: api.kaya68.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: kaya68-api
                port:
                  number: 3000
    - host: api.kaya95.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: kaya68-api
                port:
                  number: 3000
    - host: api.kaya65.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: kaya68-api
                port:
                  number: 3000

  ingressClassName: nginx
