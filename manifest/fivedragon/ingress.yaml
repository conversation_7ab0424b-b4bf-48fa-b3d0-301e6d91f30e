apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-fivedragon-api
  namespace: fivedragon
spec:
  rules:
    - host: api.5dragon.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: fivedragon-api
                port:
                  number: 3000
    - host: api.dragon-xo.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: fivedragon-api
                port:
                  number: 3000

  ingressClassName: nginx
