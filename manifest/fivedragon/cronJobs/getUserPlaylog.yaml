apiVersion: batch/v1
kind: CronJob
metadata:
  name: fivedragon-get-user-playlog
  namespace: fivedragon
spec:
  schedule: "* * * * *"
  successfulJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: callout
              image: buildpack-deps:curl
              imagePullPolicy: IfNotPresent
              command:
                - /bin/sh
                - -ec
                - curl -X GET https://api.5dragon.com/api/cronjobs/get-user-playlog
          restartPolicy: Never
