kubectl create ns uca999
# -----------
kube<PERSON>l create secret docker-registry uca999-registrykey --docker-username=cyberrich --docker-password=************************************ --docker-email=<EMAIL> --namespace=uca999
kubectl create secret generic google-service-account-volume --namespace=uca999 --from-file=google_service_account.json=google_service_account.json
kubectl create secret generic uca999-api-env --namespace=uca999 --from-env-file=.env

# -----------
kubectl apply -f .\service.yaml
kubectl apply -f .\ingress.yaml
kubectl apply -f .\deployment.yaml

# ------------

kubectl apply -f .\manifest\uca999\cronJobs\cutDaily.yaml
kubectl apply -f .\manifest\uca999\cronJobs\simplewinlose.yaml

# ------------ env pull vault script
./vault.sh https://vault.cyberrichdigital.com/v1/cybergame-prod/data/uca999/api/.env env hvs.CAESICgvnAVt8zj6HuO4OzxIgm-JXe1LZtX3J5mSopF587MpGh4KHGh2cy5MdTBLSmNkc29iU3MzaEtUVXBoWHNjR1A manifest/uca999