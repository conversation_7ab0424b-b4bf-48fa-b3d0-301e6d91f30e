$secret = kubectl get secret uca999-web-env -n uca999 -o json | ConvertFrom-Json
$secret.data.psobject.Properties | ForEach-Object {
    $key = $_.Name
    $value = $_.Value
    $decoded = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($value))
    Write-Output "$key=$decoded"
}

# old backup
# NEXTAUTH_SECRET=wvLm+8qCPtNsVcWpW5B6b+z3vrRStQhAxriM1nrmDm0=
# NEXTAUTH_URL=https://uca999.com
# NEXT_PUBLIC_API_ENDPOINT=https://api.uca999.com/api
# NEXT_PUBLIC_SOCKET_ENDPOINT=https://socket.uca999.com
# NEXT_PUBLIC_WEB_NAME=uca999game
# NEXT_PUBLIC_WEB_THEME_IMAGE=set08
# NEXT_PUBLIC_WEB_THEME_SWIPER=set01