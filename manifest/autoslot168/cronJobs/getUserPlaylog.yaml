apiVersion: batch/v1
kind: CronJob
metadata:
  name: autoslot168-get-user-playlog
  namespace: autoslot168
spec:
  schedule: "* * * * *"
  successfulJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: callout
              image: buildpack-deps:curl
              imagePullPolicy: IfNotPresent
              command:
                - /bin/sh
                - -ec
                - curl -X GET https://api.autoslotclub.win/api/cronjobs/get-user-playlog
          restartPolicy: Never
