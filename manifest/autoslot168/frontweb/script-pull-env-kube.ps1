$secret = kubectl get secret autoslot168-web-env -n autoslot168 -o json | ConvertFrom-Json
$secret.data.psobject.Properties | ForEach-Object {
    $key = $_.Name
    $value = $_.Value
    $decoded = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($value))
    Write-Output "$key=$decoded"
}

# old backup
# NEXTAUTH_SECRET=6Uy15/7U/kLlOlR1mi91oOPbQ9IYP7/xa7cACLUS+44=
# NEXTAUTH_URL=https://autoslotclub.win
# NEXT_PUBLIC_API_ENDPOINT=https://api.autoslotclub.win/api
# NEXT_PUBLIC_SOCKET_ENDPOINT=https://socket.autoslotclub.win
# NEXT_PUBLIC_WEB_NAME=autoslot168
# NEXT_PUBLIC_WEB_THEME_IMAGE=set08
# NEXT_PUBLIC_WEB_THEME_SWIPER=set02