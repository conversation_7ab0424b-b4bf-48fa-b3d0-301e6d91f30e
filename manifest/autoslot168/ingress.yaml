apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-autoslot168-api
  namespace: autoslot168
spec:
  rules:
    - host: api.autoslot168.bet
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: autoslot168-api
                port:
                  number: 3000
    - host: api.autoslotclub.win
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: autoslot168-api
                port:
                  number: 3000
                  
  ingressClassName: nginx
