apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-atmplus-api
  namespace: atmplus
spec:
  rules:
    - host: api.atmplus.bet
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: atmplus-api
                port:
                  number: 3000
    - host: api.atmplusvip.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: atmplus-api
                port:
                  number: 3000
    - host: api.atmplusvip.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: atmplus-api
                port:
                  number: 3000
    - host: api.atmvip.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: atmplus-api
                port:
                  number: 3000

  ingressClassName: nginx
