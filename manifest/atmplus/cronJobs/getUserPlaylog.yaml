apiVersion: batch/v1
kind: CronJob
metadata:
  name: atmplus-get-user-playlog
  namespace: atmplus
spec:
  schedule: "* * * * *"
  successfulJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: callout
              image: buildpack-deps:curl
              imagePullPolicy: IfNotPresent
              command:
                - /bin/sh
                - -ec
                - curl -X GET https://api.atmplusvip.com/api/cronjobs/get-user-playlog
          restartPolicy: Never
