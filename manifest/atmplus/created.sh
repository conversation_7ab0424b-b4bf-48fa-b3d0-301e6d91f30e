kube<PERSON>l create ns atmplus
# -----------
kube<PERSON>l create secret docker-registry atmplus-registrykey --docker-username=cyberrich --docker-password=************************************ --docker-email=<EMAIL> --namespace=atmplus
kubectl create secret generic google-service-account-volume --namespace=atmplus --from-file=google_service_account.json=google_service_account.json
kubectl create secret generic atmplus-api-env --namespace=atmplus --from-env-file=.env

# -----------
kubectl apply -f .\service.yaml
kubectl apply -f .\ingress.yaml
kubectl apply -f .\deployment.yaml

# ------------

kubectl apply -f .\manifest\atmplus\cronJobs\cutDaily.yaml
kubectl apply -f .\manifest\atmplus\cronJobs\simplewinlose.yaml

# ------------ env pull vault script
./vault.sh https://vault.cyberrichdigital.com/v1/cybergame-prod/data/atmplus.top/api/.env env hvs.CAESINm-6Pav7HuhGZZEO9sZs5NerVnHVu_0VH6nJD2r3N1ZGh4KHGh2cy5GV0F3eVJ6TzlIWkpKR3dmcEFZWFVUNWo manifest/atmplus