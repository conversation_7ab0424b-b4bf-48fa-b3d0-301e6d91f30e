$secret = kubectl get secret atmplus-web-env -n atmplus -o json | ConvertFrom-Json
$secret.data.psobject.Properties | ForEach-Object {
    $key = $_.Name
    $value = $_.Value
    $decoded = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($value))
    Write-Output "$key=$decoded"
}

# old backup
# NEXTAUTH_SECRET=HhQLZZLtoXuUDr7DmqI6CBCdKm49H5jmT7Wu2BiE0mo=
# NEXTAUTH_URL=https://atmvip.co
# NEXT_PUBLIC_API_ENDPOINT=https://api.atmvip.co/api
# NEXT_PUBLIC_SOCKET_ENDPOINT=https://socket.atmvip.co
# NEXT_PUBLIC_WEB_NAME=atmplus
# NEXT_PUBLIC_WEB_THEME_IMAGE=set08
# NEXT_PUBLIC_WEB_THEME_SWIPER=set01