$secret = kubectl get secret utaslot-web-env -n utaslot -o json | ConvertFrom-Json
$secret.data.psobject.Properties | ForEach-Object {
    $key = $_.Name
    $value = $_.Value
    $decoded = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($value))
    Write-Output "$key=$decoded"
}

# old backup
# NEXTAUTH_SECRET=MFIyZ3w0qo7Y5bAVu9hjHDJS2PEcqViflkrzYPVC1Po=
# NEXTAUTH_URL=https://utasportsgame.com
# NEXT_PUBLIC_API_ENDPOINT=https://api.utasportsgame.com/api
# NEXT_PUBLIC_SOCKET_ENDPOINT=https://socket.utasportsgame.com
# NEXT_PUBLIC_WEB_NAME=utaslot
# NEXT_PUBLIC_WEB_THEME_IMAGE=set03
# NEXT_PUBLIC_WEB_THEME_SWIPER=set01