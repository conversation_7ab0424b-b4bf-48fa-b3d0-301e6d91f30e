apiVersion: batch/v1
kind: CronJob
metadata:
  name: utaslot-get-user-playlog
  namespace: utaslot
spec:
  schedule: "* * * * *"
  successfulJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: callout
              image: buildpack-deps:curl
              imagePullPolicy: IfNotPresent
              command:
                - /bin/sh
                - -ec
                - curl -X GET https://api.utaslot.com/api/cronjobs/get-user-playlog
          restartPolicy: Never
