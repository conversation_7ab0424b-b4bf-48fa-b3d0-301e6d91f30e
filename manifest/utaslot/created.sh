kubectl create ns utaslot
# -----------
kubectl create secret docker-registry utaslot-registrykey --docker-username=cyberrich --docker-password=************************************ --docker-email=<EMAIL> --namespace=utaslot
kubectl create secret generic google-service-account-volume --namespace=utaslot --from-file=google_service_account.json=google_service_account.json
kubectl create secret generic utaslot-api-env --namespace=utaslot --from-env-file=.env

# -----------
kubectl apply -f .\service.yaml
kubectl apply -f .\ingress.yaml
kubectl apply -f .\deployment.yaml

# ------------

kubectl apply -f .\manifest\utaslot\cronJobs\cutDaily.yaml
kubectl apply -f .\manifest\utaslot\cronJobs\simplewinlose.yaml
kubectl apply -f .\manifest\utaslot\cronJobs\getUserPlaylog.yaml