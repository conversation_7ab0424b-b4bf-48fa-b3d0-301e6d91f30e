apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-utaslot-api
  namespace: utaslot
spec:
  rules:
    - host: api.utaslot.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: utaslot-api
                port:
                  number: 3000
    - host: api.zettoslot.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: utaslot-api
                port:
                  number: 3000
    - host: api.utasportsgame.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: utaslot-api
                port:
                  number: 3000

  ingressClassName: nginx
