apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-riverclub-api
  namespace: riverclub
spec:
  rules:
    - host: api.riverclub.club
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: riverclub-api
                port:
                  number: 3000
    - host: api.riverclub.vip
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: riverclub-api
                port:
                  number: 3000
  ingressClassName: nginx
