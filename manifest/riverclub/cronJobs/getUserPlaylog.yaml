apiVersion: batch/v1
kind: CronJob
metadata:
  name: riverclub-get-user-playlog
  namespace: riverclub
spec:
  schedule: "* * * * *"
  successfulJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: callout
              image: buildpack-deps:curl
              imagePullPolicy: IfNotPresent
              command:
                - /bin/sh
                - -ec
                - curl -X GET https://api.riverclub.vip/api/cronjobs/get-user-playlog
          restartPolicy: Never
