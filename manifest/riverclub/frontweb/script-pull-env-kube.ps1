$secret = kubectl get secret riverclub-web-env -n riverclub -o json | ConvertFrom-Json
$secret.data.psobject.Properties | ForEach-Object {
    $key = $_.Name
    $value = $_.Value
    $decoded = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($value))
    Write-Output "$key=$decoded"
}

# old backup
# NEXTAUTH_SECRET=mUifbEiA25XyuFVqsjrvUdciiw3ooNujvHx5guejprE=
# NEXTAUTH_URL=https://riverclub.club
# NEXT_PUBLIC_API_ENDPOINT=https://api.riverclub.club/api
# NEXT_PUBLIC_SOCKET_ENDPOINT=https://socket.riverclub.club
# NEXT_PUBLIC_WEB_NAME=riverclub
# NEXT_PUBLIC_WEB_THEME_IMAGE=set08
# NEXT_PUBLIC_WEB_THEME_SWIPER=set02