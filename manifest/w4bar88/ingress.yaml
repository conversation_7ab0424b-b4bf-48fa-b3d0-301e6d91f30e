apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-w4bar88-api
  namespace: w4bar88
spec:
  rules:
    - host: api.4bar88.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: w4bar88-api
                port:
                  number: 3000
    - host: api.4barnew.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: w4bar88-api
                port:
                  number: 3000

  ingressClassName: nginx
