apiVersion: batch/v1
kind: CronJob
metadata:
  name: w4bar88-cut-daily
  namespace: w4bar88
spec:
  schedule: "* * * * *"
  successfulJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: callout
              image: buildpack-deps:curl
              imagePullPolicy: IfNotPresent
              command:
                - /bin/sh
                - -ec
                - curl -X GET https://api.4barnew.com/api/cron/promotion-return-loser/cut-daily
          restartPolicy: Never