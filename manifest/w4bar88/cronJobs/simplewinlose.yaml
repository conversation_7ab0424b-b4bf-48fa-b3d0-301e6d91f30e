apiVersion: batch/v1
kind: CronJob
metadata:
  name: w4bar88-simplewinlose
  namespace: w4bar88
spec:
  schedule: "30 05 * * *"
  successfulJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: callout
              image: buildpack-deps:curl
              imagePullPolicy: IfNotPresent
              command:
                - /bin/sh
                - -ec
                - curl -X GET https://api.4barnew.com/api/cronjobs/simplewinlose
          restartPolicy: Never
