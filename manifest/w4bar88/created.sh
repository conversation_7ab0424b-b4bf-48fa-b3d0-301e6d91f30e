kubectl create ns w4bar88
# -----------
kube<PERSON>l create secret docker-registry w4bar88-registrykey --docker-username=cyberrich --docker-password=************************************ --docker-email=<EMAIL> --namespace=w4bar88
kubectl create secret generic google-service-account-volume --namespace=w4bar88 --from-file=google_service_account.json=google_service_account.json
kubectl create secret generic w4bar88-api-env --namespace=w4bar88 --from-env-file=.env

# -----------
kubectl apply -f .\service.yaml
kubectl apply -f .\ingress.yaml
kubectl apply -f .\deployment.yaml

# ------------

kubectl apply -f .\manifest\w4bar88\cronJobs\simplewinlose.yaml
kubectl apply -f .\manifest\w4bar88\cronJobs\dailyCronJob.yaml

# ------------ env pull vault script
./vault.sh https://vault.cyberrichdigital.com/v1/cybergame-prod/data/w4bar88.top/api/.env env hvs.CAESINm-6Pav7HuhGZZEO9sZs5NerVnHVu_0VH6nJD2r3N1ZGh4KHGh2cy5GV0F3eVJ6TzlIWkpKR3dmcEFZWFVUNWo manifest/w4bar88