---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: bbmslot-api
  namespace: bbmslot
spec:
  replicas: 1
  selector:
    matchLabels:
      app: bbmslot-api
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: bbmslot-api
    spec:
      imagePullSecrets:
        - name: bbmslot-registrykey
      containers:
        - name: bbmslot-api
          image: cyberrich/cybergame-api:v1.3.0
          imagePullPolicy: Always
          # livenessProbe:
          #   httpGet:
          #     path: /api/ping
          #     port: 3000
          #   initialDelaySeconds: 15
          #   periodSeconds: 4
          # readinessProbe:
          #   httpGet:
          #     path: /api/ping
          #     port: 3000
          #   initialDelaySeconds: 15
          #   periodSeconds: 4
          ports:
            - name: http
              containerPort: 3000
          volumeMounts:
            - name: google-service-account-volume
              mountPath: /secrets/
          envFrom:
            - secretRef:
                name: bbmslot-api-env
          command: ["/bin/sh", "-c", "cp /secrets/google_service_account.json /app/google_service_account.json && ./build/API"]
      volumes:
        - name: google-service-account-volume
          secret:
            secretName: google-service-account-volume
