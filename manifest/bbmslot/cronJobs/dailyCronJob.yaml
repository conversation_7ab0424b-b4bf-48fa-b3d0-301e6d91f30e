apiVersion: batch/v1
kind: CronJob
metadata:
  name: bbmslot-get-cutdaily-and-getuserplaylog
  namespace: bbmslot
spec:
  schedule: "*/2 * * * *"
  successfulJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: cutdaily-and-getuserplaylog
              image: buildpack-deps:curl
              imagePullPolicy: IfNotPresent
              command:
                - /bin/sh
                - -ec
                - |
                  echo 'Running get-user-playlog cronjob'
                  curl -X GET https://api.bbmslot.com/api/cronjobs/get-user-playlog
                  sleep 60
                  echo 'Running cut-daily cronjob'
                  curl -X GET https://api.bbmslot.com/api/cron/promotion-return-loser/cut-daily
          restartPolicy: Never
