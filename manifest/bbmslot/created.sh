kubectl create ns bbmslot
# -----------
kube<PERSON>l create secret docker-registry bbmslot-registrykey --docker-username=cyberrich --docker-password=************************************ --docker-email=<EMAIL> --namespace=bbmslot
kubectl create secret generic google-service-account-volume --namespace=bbmslot --from-file=google_service_account.json=google_service_account.json
kubectl create secret generic bbmslot-api-env --namespace=bbmslot --from-env-file=.env

# -----------
kubectl apply -f .\service.yaml
kubectl apply -f .\ingress.yaml
kubectl apply -f .\deployment.yaml

# ------------

kubectl apply -f .\manifest\bbmslot\cronJobs\cutDaily.yaml
kubectl apply -f .\manifest\bbmslot\cronJobs\simplewinlose.yaml