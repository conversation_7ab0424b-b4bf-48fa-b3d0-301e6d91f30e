apiVersion: batch/v1
kind: CronJob
metadata:
  name: s7luck-get-user-playlog
  namespace: s7luck
spec:
  schedule: "* * * * *"
  successfulJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: callout
              image: buildpack-deps:curl
              imagePullPolicy: IfNotPresent
              command:
                - /bin/sh
                - -ec
                - curl -X GET https://api.s7luck.net/api/cronjobs/get-user-playlog
          restartPolicy: Never
