apiVersion: batch/v1
kind: CronJob
metadata:
  name: s7luck-cut-daily
  namespace: s7luck
spec:
  schedule: "* * * * *"
  successfulJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: callout
              image: buildpack-deps:curl
              imagePullPolicy: IfNotPresent
              command:
                - /bin/sh
                - -ec
                - curl -X GET https://api.s7luck.net/api/cron/promotion-return-loser/cut-daily
          restartPolicy: Never