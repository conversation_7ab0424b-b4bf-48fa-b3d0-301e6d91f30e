$secret = kubectl get secret urwin888-web-env -n urwin888 -o json | ConvertFrom-Json
$secret.data.psobject.Properties | ForEach-Object {
    $key = $_.Name
    $value = $_.Value
    $decoded = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($value))
    Write-Output "$key=$decoded"
}

# old backup
# NEXTAUTH_SECRET=5maFDVLvYUm0nUHfrV7Pz9p1yeJBUCXax560atG11oQ=
# NEXTAUTH_URL=https://urwin888.com
# NEXT_PUBLIC_API_ENDPOINT=https://api.urwin888.com/api
# NEXT_PUBLIC_SOCKET_ENDPOINT=https://socket.urwin888.com
# NEXT_PUBLIC_WEB_NAME=urwin888
# NEXT_PUBLIC_WEB_THEME_IMAGE=tt-set01
# NEXT_PUBLIC_WEB_THEME_SWIPER=urwin888