---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cbg-dev-api
  namespace: cbg-dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: cbg-dev-api
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: cbg-dev-api
    spec:
      imagePullSecrets:
        - name: cbg-registrykey
      containers:
        - name: cbg-dev-api
          image: cyberrich/cybergame-api:dev
          imagePullPolicy: Always
          # livenessProbe:
          #   httpGet:
          #     path: /api/ping
          #     port: 3000
          #   initialDelaySeconds: 15
          #   periodSeconds: 4
          # readinessProbe:
          #   httpGet:
          #     path: /api/ping
          #     port: 3000
          #   initialDelaySeconds: 15
          #   periodSeconds: 4
          ports:
            - name: http
              containerPort: 3000
          volumeMounts:
            - name: google-service-account-volume
              mountPath: /secrets/
          envFrom:
            - secretRef:
                name: cbg-dev-env-api
          command: ["/bin/sh", "-c", "cp /secrets/google_service_account.json /app/google_service_account.json && ./build/API"]
      volumes:
        - name: google-service-account-volume
          secret:
            secretName: cbgame-google-service-account-dev
