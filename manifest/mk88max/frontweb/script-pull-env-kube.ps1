$secret = kubectl get secret mk88max-web-env -n cybergame-prod -o json | ConvertFrom-Json
$secret.data.psobject.Properties | ForEach-Object {
    $key = $_.Name
    $value = $_.Value
    $decoded = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($value))
    Write-Output "$key=$decoded"
}

# old backup
# NEXTAUTH_SECRET=m2a9j6FSKNQV0jbhUK63TJ8oo5rRorNnBPu6LzqBQwk=
# NEXTAUTH_URL=https://st9max.com
# NEXT_LINE_CALLBACK_URL=https://st9max.com/api/auth/callback/line
# NEXT_LINE_CLIENT_ID=2003178542
# NEXT_LINE_CLIENT_SECRET=6e4681669d840d5004856e93954bf0ce
# NEXT_PUBLIC_API_ENDPOINT=https://api.st9max.com/api
# NEXT_PUBLIC_LINE_HAS=true
# NEXT_PUBLIC_SOCKET_ENDPOINT=https://socket.st9max.com/
# NEXT_PUBLIC_WEB_NAME=st9max
# NEXT_PUBLIC_WEB_THEME_IMAGE=set08
# NEXT_PUBLIC_WEB_THEME_SWIPER=set01