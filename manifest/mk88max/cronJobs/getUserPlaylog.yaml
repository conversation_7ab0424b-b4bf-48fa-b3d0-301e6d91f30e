apiVersion: batch/v1
kind: CronJob
metadata:
  name: mk88max-get-user-playlog
  namespace: cybergame-prod
spec:
  schedule: "* * * * *"
  successfulJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: callout
              image: buildpack-deps:curl
              imagePullPolicy: IfNotPresent
              command:
                - /bin/sh
                - -ec
                - curl -X GET https://api.st9max.com/api/cronjobs/get-user-playlog
          restartPolicy: Never
