---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mk88max-api
  namespace: cybergame-prod
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mk88max-api
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: mk88max-api
    spec:
      imagePullSecrets:
        - name: cybergame-registrykey
      containers:
        - name: mk88max-api
          image: cyberrich/cybergame-api:v1.3.0
          imagePullPolicy: Always
          # livenessProbe:
          #   httpGet:
          #     path: /api/ping
          #     port: 3000
          #   initialDelaySeconds: 15
          #   periodSeconds: 4
          # readinessProbe:
          #   httpGet:
          #     path: /api/ping
          #     port: 3000
          #   initialDelaySeconds: 15
          #   periodSeconds: 4
          ports:
            - name: http
              containerPort: 3000
          volumeMounts:
            - name: google-service-account-volume
              mountPath: /secrets/
          envFrom:
            - secretRef:
                name: mk88max-api-env
          command: ["/bin/sh", "-c", "cp /secrets/google_service_account.json /app/google_service_account.json && ./build/API"]
      volumes:
        - name: google-service-account-volume
          secret:
            secretName: cybergame-google-service-account-prod
