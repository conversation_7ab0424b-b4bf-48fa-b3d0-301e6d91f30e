kubectl create ns mk88max
# -----------
kube<PERSON>l create secret docker-registry mk88max-registrykey --docker-username=cyberrich --docker-password=************************************ --docker-email=<EMAIL> --namespace=cybergame-prod
kubectl create secret generic google-service-account-volume --namespace=cybergame-prod --from-file=google_service_account.json=google_service_account.json
kubectl create secret generic mk88max-api-env --namespace=cybergame-prod --from-env-file=.env

# -----------
kubectl apply -f .\service.yaml
kubectl apply -f .\ingress.yaml
kubectl apply -f .\deployment.yaml

# ------------

kubectl apply -f .\manifest\mk88max\cronJobs\cutDaily.yaml
kubectl apply -f .\manifest\mk88max\cronJobs\simplewinlose.yaml

# ------------ env pull vault script
./vault.sh https://vault.cyberrichdigital.com/v1/cybergame-prod/data/mk88max.top/api/.env env hvs.CAESINm-6Pav7HuhGZZEO9sZs5NerVnHVu_0VH6nJD2r3N1ZGh4KHGh2cy5GV0F3eVJ6TzlIWkpKR3dmcEFZWFVUNWo manifest/mk88max