apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-mk88max-api
  namespace: cybergame-prod
spec:
  rules:
    - host: api.mk88max.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: mk88max-api
                port:
                  number: 3000
    - host: api.st9max.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: mk88max-api
                port:
                  number: 3000

  ingressClassName: nginx
