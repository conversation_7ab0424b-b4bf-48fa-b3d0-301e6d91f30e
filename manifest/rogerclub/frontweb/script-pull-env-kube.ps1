$secret = kubectl get secret rogerclub-web-env -n rogerclub -o json | ConvertFrom-Json
$secret.data.psobject.Properties | ForEach-Object {
    $key = $_.Name
    $value = $_.Value
    $decoded = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($value))
    Write-Output "$key=$decoded"
}

# old backup
# NEXTAUTH_SECRET=izEr0k9fEI/XBqt/DAw80CxnZr16Uyori7EnCATHRHo=
# NEXTAUTH_URL=https://rogerclub.win
# NEXT_PUBLIC_API_ENDPOINT=https://api.rogerclub.win/api
# NEXT_PUBLIC_SOCKET_ENDPOINT=https://socket.rogerclub.win
# NEXT_PUBLIC_WEB_NAME=rogerclub
# NEXT_PUBLIC_WEB_THEME_IMAGE=set08
# NEXT_PUBLIC_WEB_THEME_SWIPER=set02