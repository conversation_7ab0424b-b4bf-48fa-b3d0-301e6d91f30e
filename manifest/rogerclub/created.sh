kube<PERSON>l create ns rogerclub
# -----------
kube<PERSON>l create secret docker-registry rogerclub-registrykey --docker-username=cyberrich --docker-password=************************************ --docker-email=<EMAIL> --namespace=rogerclub
kubectl create secret generic google-service-account-volume --namespace=rogerclub --from-file=google_service_account.json=google_service_account.json
kubectl create secret generic rogerclub-api-env --namespace=rogerclub --from-env-file=.env

# -----------
kubectl apply -f .\service.yaml
kubectl apply -f .\ingress.yaml
kubectl apply -f .\deployment.yaml

# ------------

kubectl apply -f .\manifest\rogerclub\cronJobs\cutDaily.yaml
kubectl apply -f .\manifest\rogerclub\cronJobs\simplewinlose.yaml