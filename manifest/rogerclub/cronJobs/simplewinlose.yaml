apiVersion: batch/v1
kind: CronJob
metadata:
  name: rogerclub-simplewinlose
  namespace: rogerclub
spec:
  schedule: "30 05 * * *"
  successfulJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: callout
              image: buildpack-deps:curl
              imagePullPolicy: IfNotPresent
              command:
                - /bin/sh
                - -ec
                - curl -X GET https://api.rogerclub.co/api/cronjobs/simplewinlose
          restartPolicy: Never
