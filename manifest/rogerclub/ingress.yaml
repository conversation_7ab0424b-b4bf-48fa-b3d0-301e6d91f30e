apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-rogerclub-api
  namespace: rogerclub
spec:
  rules:
    - host: api.rogerclub.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: rogerclub-api
                port:
                  number: 3000
    - host: api.rogerclub.club
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: rogerclub-api
                port:
                  number: 3000
    - host: api.rogerclub.win
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: rogerclub-api
                port:
                  number: 3000

  ingressClassName: nginx
