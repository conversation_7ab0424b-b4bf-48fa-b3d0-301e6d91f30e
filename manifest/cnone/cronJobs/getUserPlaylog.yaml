apiVersion: batch/v1
kind: CronJob
metadata:
  name: cnone-get-user-playlog
  namespace: cnone
spec:
  schedule: "* * * * *"
  successfulJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: callout
              image: buildpack-deps:curl
              imagePullPolicy: IfNotPresent
              command:
                - /bin/sh
                - -ec
                - curl -X GET https://api.cnone.bet/api/cronjobs/get-user-playlog
          restartPolicy: Never
