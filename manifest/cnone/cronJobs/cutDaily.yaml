apiVersion: batch/v1
kind: CronJob
metadata:
  name: cnone-cut-daily
  namespace: cnone
spec:
  schedule: "* * * * *"
  successfulJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: callout
              image: buildpack-deps:curl
              imagePullPolicy: IfNotPresent
              command:
                - /bin/sh
                - -ec
                - curl -X GET https://api.cnone.bet/api/cron/promotion-return-loser/cut-daily
          restartPolicy: Never