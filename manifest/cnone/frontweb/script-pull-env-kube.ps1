$secret = kubectl get secret cnone-web-env -n cnone -o json | ConvertFrom-Json
$secret.data.psobject.Properties | ForEach-Object {
    $key = $_.Name
    $value = $_.Value
    $decoded = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($value))
    Write-Output "$key=$decoded"
}

# old backup
# NEXTAUTH_SECRET=lfC2X04Rga415N+BfAcyfgZ4YkxVARSjgs5mzmg0qwY=
# NEXTAUTH_URL=https://mcone.biz
# NEXT_PUBLIC_API_ENDPOINT=https://api.mcone.biz/api
# NEXT_PUBLIC_SOCKET_ENDPOINT=https://socket.mcone.biz
# NEXT_PUBLIC_WEB_NAME=cnone
# NEXT_PUBLIC_WEB_THEME_IMAGE=set08
# NEXT_PUBLIC_WEB_THEME_SWIPER=set01