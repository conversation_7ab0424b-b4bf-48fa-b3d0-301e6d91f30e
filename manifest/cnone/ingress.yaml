apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-cnone-api
  namespace: cnone
spec:
  rules:
    - host: api.cnone.bet
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: cnone-api
                port:
                  number: 3000
    - host: api.mcone.biz
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: cnone-api
                port:
                  number: 3000

  ingressClassName: nginx
