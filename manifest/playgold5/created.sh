kube<PERSON>l create ns playgold5
# -----------
kube<PERSON>l create secret docker-registry playgold5-registrykey --docker-username=cyberrich --docker-password=************************************ --docker-email=<EMAIL> --namespace=playgold5
kubectl create secret generic google-service-account-volume --namespace=playgold5 --from-file=google_service_account.json=google_service_account.json
kubectl create secret generic playgold5-api-env --namespace=playgold5 --from-env-file=.env

# -----------
kubectl apply -f .\service.yaml
kubectl apply -f .\ingress.yaml
kubectl apply -f .\deployment.yaml

# ------------

kubectl apply -f .\manifest\playgold5\cronJobs\cutDaily.yaml
kubectl apply -f .\manifest\playgold5\cronJobs\simplewinlose.yaml

# ------------ env pull vault script
./vault.sh https://vault.cyberrichdigital.com/v1/cybergame-prod/data/playgold5/api/.env env hvs.CAESICgvnAVt8zj6HuO4OzxIgm-JXe1LZtX3J5mSopF587MpGh4KHGh2cy5MdTBLSmNkc29iU3MzaEtUVXBoWHNjR1A manifest/playgold5