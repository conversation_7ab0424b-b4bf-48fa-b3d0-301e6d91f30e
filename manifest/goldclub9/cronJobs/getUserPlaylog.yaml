apiVersion: batch/v1
kind: CronJob
metadata:
  name: goldclub9-get-user-playlog
  namespace: goldclub9
spec:
  schedule: "* * * * *"
  successfulJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: callout
              image: buildpack-deps:curl
              imagePullPolicy: IfNotPresent
              command:
                - /bin/sh
                - -ec
                - curl -X GET https://api.9goldclub.com/api/cronjobs/get-user-playlog
          restartPolicy: Never
