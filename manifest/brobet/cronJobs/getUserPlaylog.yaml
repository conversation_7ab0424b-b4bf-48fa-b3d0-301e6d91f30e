apiVersion: batch/v1
kind: CronJob
metadata:
  name: brobet-get-user-playlog
  namespace: brobet
spec:
  schedule: "* * * * *"
  successfulJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: callout
              image: buildpack-deps:curl
              imagePullPolicy: IfNotPresent
              command:
                - /bin/sh
                - -ec
                - curl -X GET https://api.brobet.co/api/cronjobs/get-user-playlog
          restartPolicy: Never
