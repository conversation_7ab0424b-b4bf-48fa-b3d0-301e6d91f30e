$secret = kubectl get secret brobet-web-env -n brobet -o json | ConvertFrom-Json
$secret.data.psobject.Properties | ForEach-Object {
    $key = $_.Name
    $value = $_.Value
    $decoded = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($value))
    Write-Output "$key=$decoded"
}

# old backup
# NEXTAUTH_SECRET=Pq4PQ+DZGMxRdCbScJQcwfd3628+D2RDRbUtcWHHauo=
# NEXTAUTH_URL=https://brobet.co
# NEXT_PUBLIC_API_ENDPOINT=https://api.brobet.co/api
# NEXT_PUBLIC_SOCKET_ENDPOINT=https://socket.brobet.co
# NEXT_PUBLIC_WEB_NAME=brobet
# NEXT_PUBLIC_WEB_THEME_IMAGE=set05
# NEXT_PUBLIC_WEB_THEME_SWIPER=set02