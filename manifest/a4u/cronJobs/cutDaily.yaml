apiVersion: batch/v1
kind: CronJob
metadata:
  name: a4u-cut-daily
  namespace: a4u
spec:
  schedule: "* * * * *"
  successfulJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: callout
              image: buildpack-deps:curl
              imagePullPolicy: IfNotPresent
              command:
                - /bin/sh
                - -ec
                - curl -X GET https://api.a4u.top/api/cron/promotion-return-loser/cut-daily
          restartPolicy: Never