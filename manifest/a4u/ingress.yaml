apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-a4u-api
  namespace: a4u
spec:
  rules:
    - host: api.a4u.top
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: a4u-api
                port:
                  number: 3000
    - host: demo-api.cbgame88.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: a4u-api
                port:
                  number: 3000

  ingressClassName: nginx
