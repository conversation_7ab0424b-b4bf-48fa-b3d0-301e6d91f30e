$secret = kubectl get secret a4u-web-env -n a4u -o json | ConvertFrom-Json
$secret.data.psobject.Properties | ForEach-Object {
    $key = $_.Name
    $value = $_.Value
    $decoded = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($value))
    Write-Output "$key=$decoded"
}

# old backup
# NEXTAUTH_SECRET=k7MfcdFmWw2ib71MIoLH7rjPpHhVaYO2MYwJb3qahrA=
# NEXTAUTH_URL=https://demo-web.cbgame88.com
# NEXT_PUBLIC_API_ENDPOINT=https://demo-api.cbgame88.com/api
# NEXT_PUBLIC_SOCKET_ENDPOINT=https://demo-socket.cbgame88.com
# NEXT_PUBLIC_WEB_NAME=demo
# NEXT_PUBLIC_WEB_THEME_IMAGE=set08
# NEXT_PUBLIC_WEB_THEME_SWIPER=set02