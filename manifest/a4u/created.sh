kube<PERSON>l create ns a4u
# -----------
kube<PERSON>l create secret docker-registry a4u-registrykey --docker-username=cyberrich --docker-password=************************************ --docker-email=<EMAIL> --namespace=a4u
kubectl create secret generic google-service-account-volume --namespace=a4u --from-file=google_service_account.json=google_service_account.json
kubectl create secret generic a4u-api-env --namespace=a4u --from-env-file=.env

# -----------
kubectl apply -f .\service.yaml
kubectl apply -f .\ingress.yaml
kubectl apply -f .\deployment.yaml

# ------------

kubectl apply -f .\manifest\a4u\cronJobs\cutDaily.yaml
kubectl apply -f .\manifest\a4u\cronJobs\simplewinlose.yaml

# ------------ env pull vault script
./vault.sh https://vault.cyberrichdigital.com/v1/cybergame-prod/data/a4u.top/api/.env env hvs.CAESINm-6Pav7HuhGZZEO9sZs5NerVnHVu_0VH6nJD2r3N1ZGh4KHGh2cy5GV0F3eVJ6TzlIWkpKR3dmcEFZWFVUNWo manifest/a4u