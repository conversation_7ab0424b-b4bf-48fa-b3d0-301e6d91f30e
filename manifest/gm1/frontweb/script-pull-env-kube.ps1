$secret = kubectl get secret gm1-web-env -n gm1 -o json | ConvertFrom-Json
$secret.data.psobject.Properties | ForEach-Object {
    $key = $_.Name
    $value = $_.Value
    $decoded = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($value))
    Write-Output "$key=$decoded"
}

# old backup
# NEXTAUTH_SECRET=z0VEnTCPKS6HCd5VukJt6RpIQDTzzvHPqvuzP/RMKiA=
# NEXTAUTH_URL=https://gm1s.co
# NEXT_LINE_CALLBACK_URL=https://gm1s.co/api/auth/callback/line
# NEXT_LINE_CLIENT_ID=2004506816
# NEXT_LINE_CLIENT_SECRET=8d7d417bfd97589ffd49f787e67d7e15
# NEXT_PUBLIC_API_ENDPOINT=https://api.gm1s.co/api
# NEXT_PUBLIC_LINE_HAS=true
# NEXT_PUBLIC_SOCKET_ENDPOINT=https://socket.gm1s.co
# NEXT_PUBLIC_WEB_NAME=gm1
# NEXT_PUBLIC_WEB_THEME_IMAGE=set08
# NEXT_PUBLIC_WEB_THEME_SWIPER=set02