apiVersion: batch/v1
kind: CronJob
metadata:
  name: gm1-cut-daily
  namespace: gm1
spec:
  schedule: "* * * * *"
  successfulJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: callout
              image: buildpack-deps:curl
              imagePullPolicy: IfNotPresent
              command:
                - /bin/sh
                - -ec
                - curl -X GET https://api.gm1.plus/api/cron/promotion-return-loser/cut-daily
          restartPolicy: Never