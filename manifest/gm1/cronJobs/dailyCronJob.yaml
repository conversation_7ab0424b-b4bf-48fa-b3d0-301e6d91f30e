apiVersion: batch/v1
kind: CronJob
metadata:
  name: gm1-get-cutdaily-and-getuserplaylog
  namespace: gm1
spec:
  schedule: "*/2 * * * *"
  successfulJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: cutdaily-and-getuserplaylog
              image: buildpack-deps:curl
              imagePullPolicy: IfNotPresent
              command:
                - /bin/sh
                - -ec
                - |
                  echo 'Running get-user-playlog cronjob'
                  curl -X GET https://api.gm1.plus/api/cronjobs/get-user-playlog
                  sleep 60
                  echo 'Running cut-daily cronjob'
                  curl -X GET https://api.gm1.plus/api/cron/promotion-return-loser/cut-daily
          restartPolicy: Never
