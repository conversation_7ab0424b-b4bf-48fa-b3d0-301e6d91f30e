kube<PERSON>l create ns gm1
# -----------
kube<PERSON>l create secret docker-registry gm1-registrykey --docker-username=cyberrich --docker-password=************************************ --docker-email=<EMAIL> --namespace=gm1
kubectl create secret generic google-service-account-volume --namespace=gm1 --from-file=google_service_account.json=google_service_account.json
kubectl create secret generic gm1-api-env --namespace=gm1 --from-env-file=.env

# -----------
kubectl apply -f .\service.yaml
kubectl apply -f .\ingress.yaml
kubectl apply -f .\deployment.yaml

# ------------

kubectl apply -f .\manifest\gm1\cronJobs\cutDaily.yaml
kubectl apply -f .\manifest\gm1\cronJobs\simplewinlose.yaml

# ------------ env pull vault script
./vault.sh https://vault.cyberrichdigital.com/v1/cybergame-prod/data/5dragon.com/api/.env env hvs.CAESINm-6Pav7HuhGZZEO9sZs5NerVnHVu_0VH6nJD2r3N1ZGh4KHGh2cy5GV0F3eVJ6TzlIWkpKR3dmcEFZWFVUNWo manifest/gm1