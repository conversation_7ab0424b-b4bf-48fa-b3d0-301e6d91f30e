apiVersion: batch/v1
kind: CronJob
metadata:
  name: royal447-cut-daily
  namespace: royal447
spec:
  schedule: "* * * * *"
  successfulJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: callout
              image: buildpack-deps:curl
              imagePullPolicy: IfNotPresent
              command:
                - /bin/sh
                - -ec
                - curl -X GET https://api.royal447.com/api/cron/promotion-return-loser/cut-daily
          restartPolicy: Never