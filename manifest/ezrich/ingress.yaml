apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-ezrich-api
  namespace: ezrich
spec:
  rules:
    - host: api.ezrich.vip
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: ezrich-api
                port:
                  number: 3000
    - host: api.ezclub.fun
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: ezrich-api
                port:
                  number: 3000
    - host: api.ezclub.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: ezrich-api
                port:
                  number: 3000

  ingressClassName: nginx
