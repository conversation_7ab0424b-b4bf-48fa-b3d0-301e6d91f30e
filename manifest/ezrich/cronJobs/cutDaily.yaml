apiVersion: batch/v1
kind: CronJob
metadata:
  name: ezrich-cut-daily
  namespace: ezrich
spec:
  schedule: "* * * * *"
  successfulJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: callout
              image: buildpack-deps:curl
              imagePullPolicy: IfNotPresent
              command:
                - /bin/sh
                - -ec
                - curl -X GET https://api.ezrich.vip/api/cron/promotion-return-loser/cut-daily
          restartPolicy: Never