$secret = kubectl get secret ezrich-web-env -n ezrich -o json | ConvertFrom-Json
$secret.data.psobject.Properties | ForEach-Object {
    $key = $_.Name
    $value = $_.Value
    $decoded = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($value))
    Write-Output "$key=$decoded"
}

# old backup
# NEXTAUTH_SECRET=7x9n9VVKH6VmqfTU2yXy6cG+9gSpJNhsOX7sg01ZhiM=
# NEXTAUTH_URL=https://ezclub.co
# NEXT_PUBLIC_API_ENDPOINT=https://api.ezclub.co/api
# NEXT_PUBLIC_DOMAIN_NAME=ezclub.co
# NEXT_PUBLIC_SOCKET_ENDPOINT=https://socket.ezclub.co
# NEXT_PUBLIC_WEB_NAME=ezrich
# NEXT_PUBLIC_WEB_THEME_IMAGE=set08
# NEXT_PUBLIC_WEB_THEME_SWIPER=set02