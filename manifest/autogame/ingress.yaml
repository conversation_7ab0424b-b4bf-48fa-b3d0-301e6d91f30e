apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-autogame-api
  namespace: autogame
spec:
  rules:
    - host: api.auto-game.net
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: autogame-api
                port:
                  number: 3000
    - host: api.lucky89win.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: autogame-api
                port:
                  number: 3000

  ingressClassName: nginx
