apiVersion: batch/v1
kind: CronJob
metadata:
  name: autogame-get-user-playlog
  namespace: autogame
spec:
  schedule: "* * * * *"
  successfulJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: callout
              image: buildpack-deps:curl
              imagePullPolicy: IfNotPresent
              command:
                - /bin/sh
                - -ec
                - curl -X GET https://api.auto-game.net/api/cronjobs/get-user-playlog
          restartPolicy: Never
