$secret = kubectl get secret autogame-web-env -n autogame -o json | ConvertFrom-Json
$secret.data.psobject.Properties | ForEach-Object {
    $key = $_.Name
    $value = $_.Value
    $decoded = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($value))
    Write-Output "$key=$decoded"
}

# old backup
# NEXTAUTH_SECRET=vOZTHD27aCXOqlnCTI6IHVDJx0c93iiBhhkKYvdg9vg=
# NEXTAUTH_URL=https://lucky89win.com
# NEXT_PUBLIC_API_ENDPOINT=https://api.auto-gameee.com/api
# NEXT_PUBLIC_SOCKET_ENDPOINT=https://socket.auto-gameee.com
# NEXT_PUBLIC_WEB_NAME=autogame
# NEXT_PUBLIC_WEB_THEME_IMAGE=set02
# NEXT_PUBLIC_WEB_THEME_SWIPER=set01