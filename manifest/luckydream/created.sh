kube<PERSON>l create ns luckydream
# -----------
kube<PERSON>l create secret docker-registry luckydream-registrykey --docker-username=cyberrich --docker-password=************************************ --docker-email=<EMAIL> --namespace=luckydream
kubectl create secret generic google-service-account-volume --namespace=luckydream --from-file=google_service_account.json=google_service_account.json
kubectl create secret generic luckydream-api-env --namespace=luckydream --from-env-file=.env

# -----------
kubectl apply -f .\service.yaml
kubectl apply -f .\ingress.yaml
kubectl apply -f .\deployment.yaml

# ------------

kubectl apply -f .\manifest\luckydream\cronJobs\cutDaily.yaml
kubectl apply -f .\manifest\luckydream\cronJobs\simplewinlose.yaml

# ------------ env pull vault script
./vault.sh https://vault.cyberrichdigital.com/v1/cybergame-prod/data/luckydream/api/.env env hvs.CAESICgvnAVt8zj6HuO4OzxIgm-JXe1LZtX3J5mSopF587MpGh4KHGh2cy5MdTBLSmNkc29iU3MzaEtUVXBoWHNjR1A manifest/luckydream