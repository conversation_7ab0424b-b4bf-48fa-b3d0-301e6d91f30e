$secret = kubectl get secret supmongkol-web-env -n supmongkol -o json | ConvertFrom-Json
$secret.data.psobject.Properties | ForEach-Object {
    $key = $_.Name
    $value = $_.Value
    $decoded = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($value))
    Write-Output "$key=$decoded"
}

# old backup
# NEXTAUTH_SECRET=8Q5CL9fTiJ0k1M2qnyrIjMno+/nbRSHdIITIEN/XvpY=
# NEXTAUTH_URL=https://supmongkol.com
# NEXT_PUBLIC_API_ENDPOINT=https://api.supmongkol.com/api
# NEXT_PUBLIC_SOCKET_ENDPOINT=https://socket.supmongkol.com
# NEXT_PUBLIC_WEB_NAME=supmongkol
# NEXT_PUBLIC_WEB_THEME_IMAGE=set07
# NEXT_PUBLIC_WEB_THEME_SWIPER=set01