kubectl create ns supmongkol
# -----------
kubectl create secret docker-registry supmongkol-registrykey --docker-username=cyberrich --docker-password=************************************ --docker-email=<EMAIL> --namespace=supmongkol
kubectl create secret generic google-service-account-volume --namespace=supmongkol --from-file=google_service_account.json=google_service_account.json
kubectl create secret generic supmongkol-api-env --namespace=supmongkol --from-env-file=.env

# -----------
kubectl apply -f .\service.yaml
kubectl apply -f .\ingress.yaml
kubectl apply -f .\deployment.yaml

# ------------

kubectl apply -f .\manifest\supmongkol\cronJobs\cutDaily.yaml
kubectl apply -f .\manifest\supmongkol\cronJobs\simplewinlose.yaml

# ------------ env pull vault script
./vault.sh https://vault.cyberrichdigital.com/v1/cybergame-prod/data/supmongkol/api/.env env hvs.CAESICgvnAVt8zj6HuO4OzxIgm-JXe1LZtX3J5mSopF587MpGh4KHGh2cy5MdTBLSmNkc29iU3MzaEtUVXBoWHNjR1A manifest/supmongkol