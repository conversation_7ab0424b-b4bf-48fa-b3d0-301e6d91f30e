kubectl create ns onebet24hr
# >> namespace/onebet24hr created

kubectl create secret docker-registry onebet24hr-registrykey --docker-username=cyberrich --docker-password=************************************ --docker-email=<EMAIL> --namespace=onebet24hr
# >> secret/onebet24hr-registrykey created
# kubectl create secret generic google-service-account-volume --namespace=onebet24hr --from-file=google_service_account.json=google_service_account.json

# cd ...\cybergame-api\manifest\onebet24hr
kubectl create secret generic onebet24hr-api-env --namespace=onebet24hr --from-env-file=.env

# -----------
kubectl apply -f .\service.yaml
kubectl apply -f .\ingress.yaml
kubectl apply -f .\deployment.yaml
# >> service/onebet24hr-api created
# >> ingress.networking.k8s.io/ingress-onebet24hr-api created
# >> deployment.apps/onebet24hr-api created
# ------------

# kubectl apply -f .\manifest\onebet24hr\cronJobs\cutDaily.yaml
# kubectl apply -f .\manifest\onebet24hr\cronJobs\simplewinlose.yaml

# ------------ env pull vault script
# ./vault.sh https://vault.cyberrichdigital.com/v1/cybergame-prod/data/onebet24hr/api/.env env hvs.CAESICgvnAVt8zj6HuO4OzxIgm-JXe1LZtX3J5mSopF587MpGh4KHGh2cy5MdTBLSmNkc29iU3MzaEtUVXBoWHNjR1A manifest/onebet24hr
# kubectl apply -f .\ingress.yaml
