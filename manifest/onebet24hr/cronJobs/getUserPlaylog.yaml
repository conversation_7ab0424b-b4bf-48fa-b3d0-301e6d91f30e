apiVersion: batch/v1
kind: CronJob
metadata:
  name: onebet24hr-get-user-playlog
  namespace: onebet24hr
spec:
  schedule: "* * * * *"
  successfulJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: callout
              image: buildpack-deps:curl
              imagePullPolicy: IfNotPresent
              command:
                - /bin/sh
                - -ec
                - curl -X GET https://api.onebet24hr.com/api/cronjobs/get-user-playlog
          restartPolicy: Never
