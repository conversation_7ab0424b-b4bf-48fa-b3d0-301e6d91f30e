# ------- packed restart last update 2025-04-28 -------
# kubectl rollout restart deployment w4bar88-api -n w4bar88
# kubectl rollout restart deployment w4bar88-admin -n w4bar88
# kubectl rollout restart deployment w4bar88-web -n w4bar88
# sleep 20
# kubectl rollout restart deployment goldclub9-api -n goldclub9
# kubectl rollout restart deployment goldclub9-admin -n goldclub9
# kubectl rollout restart deployment goldclub9-web -n goldclub9
# sleep 20
kubectl rollout restart deployment a4u-api -n a4u
kubectl rollout restart deployment a4u-admin -n a4u
kubectl rollout restart deployment a4u-web -n a4u
# sleep 20 REMOVED NOTFOUND 2025-05-06
# kubectl rollout restart deployment allin98-api -n allin98
# kubectl rollout restart deployment allin98-admin -n allin98
# kubectl rollout restart deployment allin98-web -n allin98
sleep 20
kubectl rollout restart deployment ambet65-api -n ambet65
kubectl rollout restart deployment ambet65-admin -n ambet65
kubectl rollout restart deployment ambet65-web -n ambet65
sleep 20
kubectl rollout restart deployment atmplus-api -n atmplus
kubectl rollout restart deployment atmplus-admin -n atmplus
kubectl rollout restart deployment atmplus-web -n atmplus
sleep 20
kubectl rollout restart deployment autogame-api -n autogame
kubectl rollout restart deployment autogame-admin -n autogame
kubectl rollout restart deployment autogame-web -n autogame
sleep 20
kubectl rollout restart deployment autoslot168-api -n autoslot168
kubectl rollout restart deployment autoslot168-admin -n autoslot168
kubectl rollout restart deployment autoslot168-web -n autoslot168
sleep 20
kubectl rollout restart deployment baza888-api -n baza888
kubectl rollout restart deployment baza888-admin -n baza888
kubectl rollout restart deployment baza888-web -n baza888
# sleep 20
# kubectl rollout restart deployment bbmslot-api -n bbmslot
# kubectl rollout restart deployment bbmslot-admin -n bbmslot
# kubectl rollout restart deployment bbmslot-web -n bbmslot
sleep 20
kubectl rollout restart deployment brb88bet-api -n brb88bet
kubectl rollout restart deployment brb88bet-admin -n brb88bet
kubectl rollout restart deployment brb88bet-web -n brb88bet
sleep 20
kubectl rollout restart deployment brobet-api -n brobet
kubectl rollout restart deployment brobet-admin -n brobet
kubectl rollout restart deployment brobet-web -n brobet
sleep 20
kubectl rollout restart deployment cnone-api -n cnone
kubectl rollout restart deployment cnone-admin -n cnone
kubectl rollout restart deployment cnone-web -n cnone
sleep 20
kubectl rollout restart deployment eleven111-api -n eleven111
kubectl rollout restart deployment eleven111-admin -n eleven111
kubectl rollout restart deployment eleven111-web -n eleven111
sleep 20
kubectl rollout restart deployment ezrich-api -n ezrich
kubectl rollout restart deployment ezrich-admin -n ezrich
kubectl rollout restart deployment ezrich-web -n ezrich
# sleep 20
# kubectl rollout restart deployment fivedragon-api -n fivedragon
# kubectl rollout restart deployment fivedragon-admin -n fivedragon
# kubectl rollout restart deployment fivedragon-web -n fivedragon
sleep 20
kubectl rollout restart deployment gm1-api -n gm1
kubectl rollout restart deployment gm1-admin -n gm1
kubectl rollout restart deployment gm1-web -n gm1
sleep 20
kubectl rollout restart deployment kaya68-api -n kaya68
kubectl rollout restart deployment kaya68-admin -n kaya68
kubectl rollout restart deployment kaya68-web -n kaya68
sleep 20
kubectl rollout restart deployment luxxplay-api -n luxxplay
kubectl rollout restart deployment luxxplay-admin -n luxxplay
kubectl rollout restart deployment luxxplay-web -n luxxplay
sleep 20
kubectl rollout restart deployment maxnum168-api -n maxnum168
kubectl rollout restart deployment maxnum168-admin -n maxnum168
kubectl rollout restart deployment maxnum168-web -n maxnum168
sleep 20
kubectl rollout restart deployment maga89-api -n maga89
kubectl rollout restart deployment maga89-admin -n maga89
kubectl rollout restart deployment maga89-web -n maga89
sleep 20
kubectl rollout restart deployment meechok789-api -n meechok789
kubectl rollout restart deployment meechok789-admin -n meechok789
# kubectl rollout restart deployment meechok789-web -n meechok789
sleep 20
kubectl rollout restart deployment megaclub-api -n megaclub
kubectl rollout restart deployment megaclub-admin -n megaclub
kubectl rollout restart deployment megaclub-web -n megaclub
sleep 20
kubectl rollout restart deployment mk88max-api -n cybergame-prod
kubectl rollout restart deployment mk88max-admin -n cybergame-prod
kubectl rollout restart deployment mk88max-web -n cybergame-prod
# sleep 20
# kubectl rollout restart deployment mixxway-api -n mixxway
# kubectl rollout restart deployment mixxway-admin -n mixxway
# kubectl rollout restart deployment mixxway-web -n mixxway
sleep 20
kubectl rollout restart deployment mr89-api -n mr89
kubectl rollout restart deployment mr89-admin -n mr89
kubectl rollout restart deployment mr89-web -n mr89
sleep 20
kubectl rollout restart deployment paplern999-api -n paplern999
kubectl rollout restart deployment paplern999-admin -n paplern999
kubectl rollout restart deployment paplern999-web -n paplern999
# sleep 20
# kubectl rollout restart deployment pgautoall-api -n pgautoall
# kubectl rollout restart deployment pgautoall-admin -n pgautoall
# kubectl rollout restart deployment pgautoall-web -n pgautoall
sleep 20
kubectl rollout restart deployment rabbitrich-api -n rabbitrich
kubectl rollout restart deployment rabbitrich-admin -n rabbitrich
kubectl rollout restart deployment rabbitrich-web -n rabbitrich
sleep 20
kubectl rollout restart deployment riverclub-api -n riverclub
kubectl rollout restart deployment riverclub-admin -n riverclub
kubectl rollout restart deployment riverclub-web -n riverclub
sleep 20
kubectl rollout restart deployment rogerclub-api -n rogerclub
kubectl rollout restart deployment rogerclub-admin -n rogerclub
kubectl rollout restart deployment rogerclub-web -n rogerclub
# sleep 20
# kubectl rollout restart deployment royal447-api -n royal447
# kubectl rollout restart deployment royal447-admin -n royal447
# kubectl rollout restart deployment royal447-web -n royal447
# sleep 20
# kubectl rollout restart deployment s7luck-api -n s7luck
# kubectl rollout restart deployment s7luck-admin -n s7luck
# kubectl rollout restart deployment s7luck-web -n s7luck
sleep 20
kubectl rollout restart deployment setthisiam-api -n setthisiam
kubectl rollout restart deployment setthisiam-web -n setthisiam
kubectl rollout restart deployment setthisiam-admin -n setthisiam
sleep 20
kubectl rollout restart deployment showpow777-api -n showpow777
kubectl rollout restart deployment showpow777-admin -n showpow777
kubectl rollout restart deployment showpow777-web -n showpow777
sleep 20
kubectl rollout restart deployment supmongkol-api -n supmongkol
kubectl rollout restart deployment supmongkol-admin -n supmongkol
kubectl rollout restart deployment supmongkol-web -n supmongkol
sleep 20
kubectl rollout restart deployment therich333-api -n therich333
kubectl rollout restart deployment therich333-admin -n therich333
kubectl rollout restart deployment therich333-web -n therich333
sleep 20
kubectl rollout restart deployment thluckyrich-api -n thluckyrich
kubectl rollout restart deployment thluckyrich-web -n thluckyrich
kubectl rollout restart deployment thluckyrich-admin -n thluckyrich
# sleep 20
# kubectl rollout restart deployment topgame789-api -n topgame789
# kubectl rollout restart deployment topgame789-admin -n topgame789
# kubectl rollout restart deployment topgame789-web -n topgame789
sleep 20
kubectl rollout restart deployment topsure168-api -n topsure168
kubectl rollout restart deployment topsure168-admin -n topsure168
kubectl rollout restart deployment topsure168-web -n topsure168
sleep 20
kubectl rollout restart deployment uca999-api -n uca999
kubectl rollout restart deployment uca999-admin -n uca999
kubectl rollout restart deployment uca999-web -n uca999
sleep 20
kubectl rollout restart deployment uk888club-api -n uk888club
kubectl rollout restart deployment uk888club-admin -n uk888club
kubectl rollout restart deployment uk888club-web -n uk888club
sleep 20
kubectl rollout restart deployment urwin888-api -n urwin888
kubectl rollout restart deployment urwin888-admin -n urwin888
kubectl rollout restart deployment urwin888-web -n urwin888
sleep 20
kubectl rollout restart deployment utaslot-api -n utaslot
kubectl rollout restart deployment utaslot-admin -n utaslot
kubectl rollout restart deployment utaslot-web -n utaslot
sleep 20
kubectl rollout restart deployment warior88-api -n warior88
kubectl rollout restart deployment warior88-admin -n warior88
kubectl rollout restart deployment warior88-web -n warior88
sleep 20
kubectl rollout restart deployment winlotto555-api -n winlotto555
kubectl rollout restart deployment winlotto555-admin -n winlotto555
kubectl rollout restart deployment winlotto555-web -n winlotto555
sleep 20
kubectl rollout restart deployment wowgame88-api -n wowgame88
kubectl rollout restart deployment wowgame88-admin -n wowgame88
kubectl rollout restart deployment wowgame88-web -n wowgame88
# ------- packed restart last update 2025-04-28 -------
sleep 20
kubectl rollout restart deployment onebet24hr-api -n onebet24hr
kubectl rollout restart deployment onebet24hr-admin -n onebet24hr
kubectl rollout restart deployment onebet24hr-web -n onebet24hr
sleep 20
kubectl rollout restart deployment luckydream-api -n luckydream
kubectl rollout restart deployment luckydream-admin -n luckydream
kubectl rollout restart deployment luckydream-web -n luckydream
# ------- packed restart last update 2025-06-02 -------
kubectl rollout restart deployment playgold5-api -n playgold5
kubectl rollout restart deployment playgold5-admin -n playgold5
kubectl rollout restart deployment playgold5-web -n playgold5
