$secret = kubectl get secret mr89-web-env -n mr89 -o json | ConvertFrom-Json
$secret.data.psobject.Properties | ForEach-Object {
    $key = $_.Name
    $value = $_.Value
    $decoded = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($value))
    Write-Output "$key=$decoded"
}

# old backup
# NEXTAUTH_SECRET=Nhys5Bf5pxTfESAwRIt4YG6/+TQ3tsAqPyVE9RE1gmQ=
# NEXTAUTH_URL=https://m8r9.com
# NEXT_PUBLIC_API_ENDPOINT=https://api.m8r9.com/api
# NEXT_PUBLIC_SOCKET_ENDPOINT=https://socket.m8r9.com
# NEXT_PUBLIC_WEB_NAME=mr89
# NEXT_PUBLIC_WEB_THEME_IMAGE=set02
# NEXT_PUBLIC_WEB_THEME_SWIPER=set01