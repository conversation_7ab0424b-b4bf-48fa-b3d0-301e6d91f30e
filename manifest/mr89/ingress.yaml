apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-mr89-api
  namespace: mr89
spec:
  rules:
    - host: api.mr89.net
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: mr89-api
                port:
                  number: 3000
    - host: api.m8r9.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: mr89-api
                port:
                  number: 3000
    - host: api.aw8t.net
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: mr89-api
                port:
                  number: 3000

  ingressClassName: nginx

