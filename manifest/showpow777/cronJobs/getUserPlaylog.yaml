apiVersion: batch/v1
kind: CronJob
metadata:
  name: showpow777-get-user-playlog
  namespace: showpow777
spec:
  schedule: "* * * * *"
  successfulJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: callout
              image: buildpack-deps:curl
              imagePullPolicy: IfNotPresent
              command:
                - /bin/sh
                - -ec
                - curl -X GET https://api.showpow777.com/api/cronjobs/get-user-playlog
          restartPolicy: Never
