apiVersion: batch/v1
kind: CronJob
metadata:
  name: showpow777-get-cutdaily-and-getuserplaylog
  namespace: showpow777
spec:
  schedule: "*/2 * * * *"
  successfulJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: cutdaily-and-getuserplaylog
              image: buildpack-deps:curl
              imagePullPolicy: IfNotPresent
              command:
                - /bin/sh
                - -ec
                - |
                  echo 'Running get-user-playlog cronjob'
                  curl -X GET https://api.showpow777.com/api/cronjobs/get-user-playlog
                  sleep 60
                  echo 'Running cut-daily cronjob'
                  curl -X GET https://api.showpow777.com/api/cron/promotion-return-loser/cut-daily
          restartPolicy: Never
