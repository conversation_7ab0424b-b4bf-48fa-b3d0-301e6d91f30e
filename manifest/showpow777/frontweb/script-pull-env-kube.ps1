$secret = kubectl get secret showpow777-web-env -n showpow777 -o json | ConvertFrom-Json
$secret.data.psobject.Properties | ForEach-Object {
    $key = $_.Name
    $value = $_.Value
    $decoded = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($value))
    Write-Output "$key=$decoded"
}

# old backup
# NEXTAUTH_SECRET=lpn3Y6yodBpjHNf6po9z63+ffYK5wzG5tPg3HU4KPtU=
# NEXTAUTH_URL=https://showpow777.com
# NEXT_PUBLIC_API_ENDPOINT=https://api.showpow777.com/api
# NEXT_PUBLIC_SOCKET_ENDPOINT=https://socket.showpow777.com
# NEXT_PUBLIC_WEB_NAME=showpow777
# NEXT_PUBLIC_WEB_THEME_IMAGE=set08
# NEXT_PUBLIC_WEB_THEME_SWIPER=set01