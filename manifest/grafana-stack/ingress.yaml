apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-kube-prometheus-stack-grafana
  namespace: kube-prometheus-stack
spec:
  rules:
    - host: log01.cbgame88.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: kube-prometheus-stack-grafana
                port:
                  number: 3000

  ingressClassName: nginx
