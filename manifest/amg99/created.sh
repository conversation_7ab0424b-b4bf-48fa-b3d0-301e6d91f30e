kube<PERSON>l create ns amg99
# -----------
kube<PERSON>l create secret docker-registry amg99-registrykey --docker-username=cyberrich --docker-password=************************************ --docker-email=<EMAIL> --namespace=amg99
kubectl create secret generic google-service-account-volume --namespace=amg99 --from-file=google_service_account.json=google_service_account.json
kubectl create secret generic amg99-api-env --namespace=amg99 --from-env-file=.env

# -----------
kubectl apply -f .\service.yaml
kubectl apply -f .\ingress.yaml
kubectl apply -f .\deployment.yaml

# ------------

kubectl apply -f .\manifest\amg99\cronJobs\cutDaily.yaml
kubectl apply -f .\manifest\amg99\cronJobs\simplewinlose.yaml

# ------------ env pull vault script
./vault.sh https://vault.cyberrichdigital.com/v1/cybergame-prod/data/amg99/api/.env env hvs.CAESICgvnAVt8zj6HuO4OzxIgm-JXe1LZtX3J5mSopF587MpGh4KHGh2cy5MdTBLSmNkc29iU3MzaEtUVXBoWHNjR1A manifest/amg99