kube<PERSON>l create ns mixxway
# -----------
kube<PERSON>l create secret docker-registry mixxway-registrykey --docker-username=cyberrich --docker-password=************************************ --docker-email=<EMAIL> --namespace=mixxway
kubectl create secret generic google-service-account-volume --namespace=mixxway --from-file=google_service_account.json=google_service_account.json
kubectl create secret generic mixxway-api-env --namespace=mixxway --from-env-file=.env

# -----------
kubectl apply -f .\service.yaml
kubectl apply -f .\ingress.yaml
kubectl apply -f .\deployment.yaml

# ------------

kubectl apply -f .\manifest\mixxway\cronJobs\cutDaily.yaml
kubectl apply -f .\manifest\mixxway\cronJobs\simplewinlose.yaml

# ------------ env pull vault script
./vault.sh https://vault.cyberrichdigital.com/v1/cybergame-prod/data/mixxway/api/.env env hvs.CAESICgvnAVt8zj6HuO4OzxIgm-JXe1LZtX3J5mSopF587MpGh4KHGh2cy5MdTBLSmNkc29iU3MzaEtUVXBoWHNjR1A manifest/mixxway