$secret = kubectl get secret mixxway-web-env -n mixxway -o json | ConvertFrom-Json
$secret.data.psobject.Properties | ForEach-Object {
    $key = $_.Name
    $value = $_.Value
    $decoded = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($value))
    Write-Output "$key=$decoded"
}

# old backup
# NEXTAUTH_SECRET=c5NaDyuPmBvjUQW3o+6gdWLZjAkJyp/mS6rZyc3wq4c=
# NEXTAUTH_URL=https://meetang.fun
# NEXT_PUBLIC_API_ENDPOINT= https://api.meetang.fun/api
# NEXT_PUBLIC_SOCKET_ENDPOINT=https://socket.meetang.fun
# NEXT_PUBLIC_WEB_NAME=meetang
# NEXT_PUBLIC_WEB_THEME_IMAGE=set07
# NEXT_PUBLIC_WEB_THEME_SWIPER=set01