kube<PERSON>l create ns topsure168
# -----------
kubectl create secret docker-registry topsure168-registrykey --docker-username=cyberrich --docker-password=************************************ --docker-email=<EMAIL> --namespace=topsure168
kubectl create secret generic google-service-account-volume --namespace=topsure168 --from-file=google_service_account.json=google_service_account.json
kubectl create secret generic topsure168-api-env --namespace=topsure168 --from-env-file=.env

# -----------
kubectl apply -f .\service.yaml
kubectl apply -f .\ingress.yaml
kubectl apply -f .\deployment.yaml

# ------------

kubectl apply -f .\manifest\topsure168\cronJobs\cutDaily.yaml
kubectl apply -f .\manifest\topsure168\cronJobs\simplewinlose.yaml
kubectl apply -f .\manifest\topsure168\cronJobs\getUserPlaylog.yaml