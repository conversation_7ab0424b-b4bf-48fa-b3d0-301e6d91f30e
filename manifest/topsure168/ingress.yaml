apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-topsure168-api
  namespace: topsure168
spec:
  rules:
    - host: api.topsure168.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: topsure168-api
                port:
                  number: 3000
    - host: api.tsure.net
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: topsure168-api
                port:
                  number: 3000
    - host: api.aw8g.net
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: topsure168-api
                port:
                  number: 3000

  ingressClassName: nginx
