apiVersion: batch/v1
kind: CronJob
metadata:
  name: topsure168-get-user-playlog
  namespace: topsure168
spec:
  schedule: "* * * * *"
  successfulJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: callout
              image: buildpack-deps:curl
              imagePullPolicy: IfNotPresent
              command:
                - /bin/sh
                - -ec
                - curl -X GET https://api.topsure168.com/api/cronjobs/get-user-playlog
          restartPolicy: Never
