$secret = kubectl get secret topsure168-web-env -n topsure168 -o json | ConvertFrom-Json
$secret.data.psobject.Properties | ForEach-Object {
    $key = $_.Name
    $value = $_.Value
    $decoded = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($value))
    Write-Output "$key=$decoded"
}

# old backup
# NEXTAUTH_SECRET=Ww8pbt3eUIC1Q9rO+kmmvPHzhVxeZljqdiYNWuz2wGE=
# NEXTAUTH_URL=https://aw8g.net
# NEXT_PUBLIC_API_ENDPOINT=https://api.aw8g.net/api
# NEXT_PUBLIC_SOCKET_ENDPOINT=https://socket.aw8g.net
# NEXT_PUBLIC_WEB_NAME=aw8
# NEXT_PUBLIC_WEB_THEME_IMAGE=set-aw8g-01
# NEXT_PUBLIC_WEB_THEME_SWIPER=set12