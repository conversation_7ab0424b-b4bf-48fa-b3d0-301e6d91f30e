kube<PERSON>l create ns winlotto555
# -----------
kube<PERSON>l create secret docker-registry winlotto555-registrykey --docker-username=cyberrich --docker-password=************************************ --docker-email=<EMAIL> --namespace=winlotto555
kubectl create secret generic google-service-account-volume --namespace=winlotto555 --from-file=google_service_account.json=google_service_account.json
kubectl create secret generic winlotto555-api-env --namespace=winlotto555 --from-env-file=.env

# -----------
kubectl apply -f .\service.yaml
kubectl apply -f .\ingress.yaml
kubectl apply -f .\deployment.yaml

# ------------

kubectl apply -f .\manifest\winlotto555\cronJobs\simplewinlose.yaml
kubectl apply -f .\manifest\winlotto555\cronJobs\dailyCronJob.yaml

# ------------ env pull vault script
./vault.sh https://vault.cyberrichdigital.com/v1/cybergame-prod/data/winlotto555.top/api/.env env hvs.CAESINm-6Pav7HuhGZZEO9sZs5NerVnHVu_0VH6nJD2r3N1ZGh4KHGh2cy5GV0F3eVJ6TzlIWkpKR3dmcEFZWFVUNWo manifest/winlotto555