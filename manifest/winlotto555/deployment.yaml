---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: winlotto555-api
  namespace: winlotto555
spec:
  replicas: 1
  selector:
    matchLabels:
      app: winlotto555-api
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: winlotto555-api
    spec:
      imagePullSecrets:
        - name: winlotto555-registrykey
      containers:
        - name: winlotto555-api
          image: cyberrich/cybergame-api:v1.3.0
          imagePullPolicy: Always
          # livenessProbe:
          #   httpGet:
          #     path: /api/ping
          #     port: 3000
          #   initialDelaySeconds: 15
          #   periodSeconds: 4
          # readinessProbe:
          #   httpGet:
          #     path: /api/ping
          #     port: 3000
          #   initialDelaySeconds: 15
          #   periodSeconds: 4
          ports:
            - name: http
              containerPort: 3000
          volumeMounts:
            - name: google-service-account-volume
              mountPath: /secrets/
          envFrom:
            - secretRef:
                name: winlotto555-api-env
          command: ["/bin/sh", "-c", "cp /secrets/google_service_account.json /app/google_service_account.json && ./build/API"]
      volumes:
        - name: google-service-account-volume
          secret:
            secretName: google-service-account-volume
