$secret = kubectl get secret winlotto555-web-env -n winlotto555 -o json | ConvertFrom-Json
$secret.data.psobject.Properties | ForEach-Object {
    $key = $_.Name
    $value = $_.Value
    $decoded = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($value))
    Write-Output "$key=$decoded"
}

# old backup
# NEXTAUTH_SECRET=YHdXL4wUu0Q1JbnKQVR5s0lCCG3W5Viv/2sGqQIs+go=
# NEXTAUTH_URL=https://huayvisa.com
# NEXT_PUBLIC_API_ENDPOINT=https://api.huayvisa.com/api
# NEXT_PUBLIC_API_ENDPOINT_LOTTO=https://api.lotto29.com/api/v1
# NEXT_PUBLIC_CDN_URL_LOTTO=https://cdn.tidtech.dev
# NEXT_PUBLIC_COMPANY_ID=2
# NEXT_PUBLIC_DOMAIN_NAME=winlotto555.com
# NEXT_PUBLIC_LOTTO_HAS=true
# NEXT_PUBLIC_SOCKET_ENDPOINT=https://socket.huayvisa.com
# NEXT_PUBLIC_WEB_NAME=winlotto555
# NEXT_PUBLIC_WEB_THEME_IMAGE=set06
# NEXT_PUBLIC_WEB_THEME_SWIPER=winlotto555