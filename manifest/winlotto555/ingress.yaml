apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-winlotto555-api
  namespace: winlotto555
spec:
  rules:
    - host: api.winlotto555.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: winlotto555-api
                port:
                  number: 3000
    - host: api.huayvisa.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: winlotto555-api
                port:
                  number: 3000
    - host: api.visagame.club
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: winlotto555-api
                port:
                  number: 3000
    - host: api.visagame.net
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: winlotto555-api
                port:
                  number: 3000
    - host: api.visagame.org
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: winlotto555-api
                port:
                  number: 3000
    - host: api.visagame.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: winlotto555-api
                port:
                  number: 3000

  ingressClassName: nginx
