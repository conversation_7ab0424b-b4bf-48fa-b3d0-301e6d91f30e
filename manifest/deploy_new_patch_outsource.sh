# ======================= api =======================
kubectl rollout restart deployment allforwins-api -n allforwins
# kubectl rollout restart deployment demo-api -n demo
kubectl rollout restart deployment grandcasino-api -n grandcasino
sleep 20
kubectl rollout restart deployment munmax-api -n munmax
kubectl rollout restart deployment one1-api -n one1
kubectl rollout restart deployment sagame1991-api -n sagame1991
sleep 20
kubectl rollout restart deployment solo78-api -n solo78
kubectl rollout restart deployment solopromax-api -n solopromax
kubectl rollout restart deployment sbo88thailand-api -n sbo88thailand
sleep 20
kubectl rollout restart deployment sexypg888-api -n sexypg888
kubectl rollout restart deployment sexypgth-api -n sexypgth
kubectl rollout restart deployment x365-api -n x365
sleep 20
kubectl rollout restart deployment luckytree-api -n luckytree
kubectl rollout restart deployment solo1688-api -n solo1688

# admin
kubectl rollout restart deployment allforwins-admin -n allforwins
# kubectl rollout restart deployment demo-admin -n demo
kubectl rollout restart deployment grandcasino-admin -n grandcasino
sleep 20
kubectl rollout restart deployment munmax-admin -n munmax
kubectl rollout restart deployment one1-admin -n one1
kubectl rollout restart deployment sagame1991-admin -n sagame1991
sleep 20
kubectl rollout restart deployment solo78-admin -n solo78
kubectl rollout restart deployment solopromax-admin -n solopromax
kubectl rollout restart deployment sbo88thailand-admin -n sbo88thailand
sleep 20
kubectl rollout restart deployment sexypg888-admin -n sexypg888
kubectl rollout restart deployment sexypgth-admin -n sexypgth
kubectl rollout restart deployment x365-admin -n x365
sleep 20
kubectl rollout restart deployment luckytree-admin -n luckytree
kubectl rollout restart deployment solo1688-admin -n solo1688

# ======================= web =======================
kubectl rollout restart deployment allforwins-web -n allforwins
# kubectl rollout restart deployment demo-web -n demo
kubectl rollout restart deployment grandcasino-web -n grandcasino
sleep 20
kubectl rollout restart deployment munmax-web -n munmax
kubectl rollout restart deployment one1-web -n one1
kubectl rollout restart deployment sagame1991-web -n sagame1991
sleep 20
kubectl rollout restart deployment solo78-web -n solo78
kubectl rollout restart deployment solopromax-web -n solopromax
kubectl rollout restart deployment sbo88thailand-web -n sbo88thailand
sleep 20
kubectl rollout restart deployment sexypg888-web -n sexypg888
kubectl rollout restart deployment sexypgth-web -n sexypgth
kubectl rollout restart deployment x365-web -n x365
sleep 20
kubectl rollout restart deployment luckytree-web -n luckytree
kubectl rollout restart deployment solo1688-web -n solo1688

# ======================= package =======================
kubectl rollout restart deployment allforwins-api -n allforwins
kubectl rollout restart deployment allforwins-admin -n allforwins
kubectl rollout restart deployment allforwins-web -n allforwins
# sleep 20 REMOVED 20250506 NOTFOUND
# kubectl rollout restart deployment demo-api -n demo
# kubectl rollout restart deployment demo-admin -n demo
# kubectl rollout restart deployment demo-web -n demo
sleep 20
kubectl rollout restart deployment grandcasino-api -n grandcasino
kubectl rollout restart deployment grandcasino-admin -n grandcasino
kubectl rollout restart deployment grandcasino-web -n grandcasino
sleep 20
kubectl rollout restart deployment munmax-api -n munmax
kubectl rollout restart deployment munmax-admin -n munmax
kubectl rollout restart deployment munmax-web -n munmax
sleep 20
kubectl rollout restart deployment one1-api -n one1
kubectl rollout restart deployment one1-admin -n one1
kubectl rollout restart deployment one1-web -n one1
sleep 20
kubectl rollout restart deployment sagame1991-api -n sagame1991
kubectl rollout restart deployment sagame1991-admin -n sagame1991
kubectl rollout restart deployment sagame1991-web -n sagame1991
sleep 20
kubectl rollout restart deployment solo78-api -n solo78
kubectl rollout restart deployment solo78-admin -n solo78
kubectl rollout restart deployment solo78-web -n solo78
sleep 20
kubectl rollout restart deployment solopromax-api -n solopromax
kubectl rollout restart deployment solopromax-admin -n solopromax
kubectl rollout restart deployment solopromax-web -n solopromax
sleep 20
kubectl rollout restart deployment sbo88thailand-api -n sbo88thailand
kubectl rollout restart deployment sbo88thailand-admin -n sbo88thailand
kubectl rollout restart deployment sbo88thailand-web -n sbo88thailand
sleep 20
kubectl rollout restart deployment sexypg888-api -n sexypg888
kubectl rollout restart deployment sexypg888-admin -n sexypg888 
kubectl rollout restart deployment sexypg888-web -n sexypg888
sleep 20
kubectl rollout restart deployment sexypgth-admin -n sexypgth
kubectl rollout restart deployment sexypgth-web -n sexypgth 
kubectl rollout restart deployment sexypgth-api -n sexypgth
sleep 20
kubectl rollout restart deployment x365-api -n x365
kubectl rollout restart deployment x365-admin -n x365
kubectl rollout restart deployment x365-web -n x365
sleep 20
kubectl rollout restart deployment luckytree-api -n luckytree
kubectl rollout restart deployment luckytree-admin -n luckytree
kubectl rollout restart deployment luckytree-web -n luckytree
# LastUpdate 2025-04-28
sleep 20
kubectl rollout restart deployment solo1688-api -n solo1688
kubectl rollout restart deployment solo1688-admin -n solo1688
kubectl rollout restart deployment solo1688-web -n solo1688
# LastUpdate 2025-06-09