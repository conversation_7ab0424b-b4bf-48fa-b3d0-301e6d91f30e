$secret = kubectl get secret luxxplay-web-env -n luxxplay -o json | ConvertFrom-Json
$secret.data.psobject.Properties | ForEach-Object {
    $key = $_.Name
    $value = $_.Value
    $decoded = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($value))
    Write-Output "$key=$decoded"
}

# old backup
# NEXTAUTH_SECRET=8JL88SNHrZPuJLwsPjUoXngOkJ03dvt8Vf5l8oJvT30=
# NEXTAUTH_URL=https://luxxplay.com
# NEXT_LINE_CALLBACK_URL=https://luxxplay.com/api/auth/callback/line
# NEXT_LINE_CLIENT_ID=2005754273
# NEXT_LINE_CLIENT_SECRET=8c44153ce1fb9dacbffaf6fa8cebfbe6
# NEXT_PUBLIC_API_ENDPOINT=https://api.luxxplay.com/api
# NEXT_PUBLIC_LINE_HAS=true
# NEXT_PUBLIC_SOCKET_ENDPOINT=https://socket.luxxplay.com
# NEXT_PUBLIC_WEB_NAME=luxxplay
# NEXT_PUBLIC_WEB_THEME_IMAGE=set03