$secret = kubectl get secret thluckyrich-web-env -n thluckyrich -o json | ConvertFrom-Json
$secret.data.psobject.Properties | ForEach-Object {
    $key = $_.Name
    $value = $_.Value
    $decoded = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($value))
    Write-Output "$key=$decoded"
}

# old backup
# NEXTAUTH_SECRET=A7iCidw056soviixGynwphK3EXmEE0SaU+NXsopTDZc=
# NEXTAUTH_URL=https://thluckyrich.com
# NEXT_PUBLIC_API_ENDPOINT=https://api.thluckyrich.com/api
# NEXT_PUBLIC_SOCKET_ENDPOINT=https://socket.thluckyrich.com
# NEXT_PUBLIC_WEB_NAME=thluckyrich
# NEXT_PUBLIC_WEB_THEME_IMAGE=set05
# NEXT_PUBLIC_WEB_THEME_SWIPER=set02