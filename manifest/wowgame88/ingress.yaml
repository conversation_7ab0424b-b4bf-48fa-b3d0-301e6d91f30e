apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-wowgame88-api
  namespace: wowgame88
spec:
  rules:
    - host: api.wowgame88.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: wowgame88-api
                port:
                  number: 3000
    - host: api.yoiyuka.fun
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: wowgame88-api
                port:
                  number: 3000

  ingressClassName: nginx
