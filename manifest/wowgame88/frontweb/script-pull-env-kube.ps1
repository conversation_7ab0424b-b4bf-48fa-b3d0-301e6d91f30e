$secret = kubectl get secret wowgame88-web-env -n wowgame88 -o json | ConvertFrom-Json
$secret.data.psobject.Properties | ForEach-Object {
    $key = $_.Name
    $value = $_.Value
    $decoded = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($value))
    Write-Output "$key=$decoded"
}

# old backup
# NEXTAUTH_SECRET=MFIyZ3w0qo7Y5bAVu9hjHDJS2PEcqViflkrzYPVC1Po=
# NEXTAUTH_URL=https://yoiyuka.fun
# NEXT_PUBLIC_API_ENDPOINT=https://api.yoiyuka.fun/api
# NEXT_PUBLIC_SOCKET_ENDPOINT=https://socket.yoiyuka.fun
# NEXT_PUBLIC_WEB_NAME=wowgame88
# NEXT_PUBLIC_WEB_THEME_IMAGE=set08
# NEXT_PUBLIC_WEB_THEME_SWIPER=set03