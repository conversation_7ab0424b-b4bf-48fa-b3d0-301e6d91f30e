apiVersion: batch/v1
kind: CronJob
metadata:
  name: warior88-cut-daily
  namespace: warior88
spec:
  schedule: "* * * * *"
  successfulJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: callout
              image: buildpack-deps:curl
              imagePullPolicy: IfNotPresent
              command:
                - /bin/sh
                - -ec
                - curl -X GET https://api.opwarior.net/api/cron/promotion-return-loser/cut-daily
          restartPolicy: Never