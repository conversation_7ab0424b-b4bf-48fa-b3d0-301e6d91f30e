$secret = kubectl get secret warior88-web-env -n warior88 -o json | ConvertFrom-Json
$secret.data.psobject.Properties | ForEach-Object {
    $key = $_.Name
    $value = $_.Value
    $decoded = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($value))
    Write-Output "$key=$decoded"
}

# old backup
# NEXTAUTH_SECRET=lDTzjyGKEB/f/dzNoH0NBnMl8GcZE3kBFMsD6AH9YVA=
# NEXTAUTH_URL=https://opwarior.net
# NEXT_PUBLIC_API_ENDPOINT=https://api.opwarior.net/api
# NEXT_PUBLIC_SOCKET_ENDPOINT=https://socket.opwarior.net
# NEXT_PUBLIC_WEB_NAME=wariorop
# NEXT_PUBLIC_WEB_THEME_IMAGE=set08
# NEXT_PUBLIC_WEB_THEME_SWIPER=set02