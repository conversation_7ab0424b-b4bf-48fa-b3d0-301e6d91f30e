kube<PERSON>l create ns warior88
# -----------
kube<PERSON>l create secret docker-registry warior88-registrykey --docker-username=cyberrich --docker-password=************************************ --docker-email=<EMAIL> --namespace=warior88
kubectl create secret generic google-service-account-volume --namespace=warior88 --from-file=google_service_account.json=google_service_account.json
kubectl create secret generic warior88-api-env --namespace=warior88 --from-env-file=.env

# -----------
kubectl apply -f .\service.yaml
kubectl apply -f .\ingress.yaml
kubectl apply -f .\deployment.yaml

# ------------

kubectl apply -f .\manifest\warior88\cronJobs\cutDaily.yaml
kubectl apply -f .\manifest\warior88\cronJobs\simplewinlose.yaml

# ------------ env pull vault script
./vault.sh https://vault.cyberrichdigital.com/v1/cybergame-prod/data/warior88.top/api/.env env hvs.CAESINm-6Pav7HuhGZZEO9sZs5NerVnHVu_0VH6nJD2r3N1ZGh4KHGh2cy5GV0F3eVJ6TzlIWkpKR3dmcEFZWFVUNWo manifest/warior88