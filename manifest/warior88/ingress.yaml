apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-warior88-api
  namespace: warior88
spec:
  rules:
    - host: api.warior88.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: warior88-api
                port:
                  number: 3000
    - host: api.opwarior.net
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: warior88-api
                port:
                  number: 3000

  ingressClassName: nginx
