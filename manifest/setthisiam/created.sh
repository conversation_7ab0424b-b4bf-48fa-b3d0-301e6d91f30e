kube<PERSON>l create ns setthisiam
# -----------
kube<PERSON>l create secret docker-registry setthisiam-registrykey --docker-username=cyberrich --docker-password=************************************ --docker-email=<EMAIL> --namespace=setthisiam
kubectl create secret generic google-service-account-volume --namespace=setthisiam --from-file=google_service_account.json=google_service_account.json
kubectl create secret generic setthisiam-api-env --namespace=setthisiam --from-env-file=.env

# -----------
kubectl apply -f .\service.yaml
kubectl apply -f .\ingress.yaml
kubectl apply -f .\deployment.yaml

# ------------

kubectl apply -f .\manifest\setthisiam\cronJobs\simplewinlose.yaml
kubectl apply -f .\manifest\setthisiam\cronJobs\dailyCronJob.yaml

# ------------ env pull vault script
./vault.sh https://vault.cyberrichdigital.com/v1/cybergame-prod/data/setthisiam.top/api/.env env hvs.CAESINm-6Pav7HuhGZZEO9sZs5NerVnHVu_0VH6nJD2r3N1ZGh4KHGh2cy5GV0F3eVJ6TzlIWkpKR3dmcEFZWFVUNWo manifest/setthisiam