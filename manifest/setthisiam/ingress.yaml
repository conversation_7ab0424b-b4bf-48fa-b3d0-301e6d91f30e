apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-setthisiam-api
  namespace: setthisiam
spec:
  rules:
    - host: api.xn--h3c8accyhi9dya9h.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: setthisiam-api
                port:
                  number: 3000
    - host: api.richsiam.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: setthisiam-api
                port:
                  number: 3000
    - host: api.ruayrichlotto.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: setthisiam-api
                port:
                  number: 3000
    - host: api.ruayrich88.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: setthisiam-api
                port:
                  number: 3000

  ingressClassName: nginx
