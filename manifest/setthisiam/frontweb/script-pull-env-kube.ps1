$secret = kubectl get secret setthisiam-web-env -n setthisiam -o json | ConvertFrom-Json
$secret.data.psobject.Properties | ForEach-Object {
    $key = $_.Name
    $value = $_.Value
    $decoded = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($value))
    Write-Output "$key=$decoded"
}

# old backup
# NEXTAUTH_SECRET=8f0ihIkLzYGwqt09Y4iPOSrg0cTf2SJ73NgvxWNhUOc=
# NEXTAUTH_URL=https://ruayrich88.com
# NEXT_PUBLIC_API_ENDPOINT=https://api.ruayrich88.com/api
# NEXT_PUBLIC_DOMAIN_NAME=ruayrich88.com
# NEXT_PUBLIC_LOTTO_HAS=true
# NEXT_PUBLIC_SOCKET_ENDPOINT=https://socket.ruayrich88.com
# NEXT_PUBLIC_WEB_NAME=ruayrich88
# NEXT_PUBLIC_WEB_THEME_IMAGE=tt-set01
# NEXT_PUBLIC_WEB_THEME_SWIPER=set02