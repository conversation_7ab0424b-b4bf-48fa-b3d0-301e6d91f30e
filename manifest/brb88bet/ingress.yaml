apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-brb88bet-api
  namespace: brb88bet
spec:
  rules:
    - host: api.brb88bet.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: brb88bet-api
                port:
                  number: 3000
    - host: api.brb8.win
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: brb88bet-api
                port:
                  number: 3000

  ingressClassName: nginx
