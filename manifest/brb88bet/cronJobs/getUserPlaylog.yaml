apiVersion: batch/v1
kind: CronJob
metadata:
  name: brb88bet-get-user-playlog
  namespace: brb88bet
spec:
  schedule: "* * * * *"
  successfulJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: callout
              image: buildpack-deps:curl
              imagePullPolicy: IfNotPresent
              command:
                - /bin/sh
                - -ec
                - curl -X GET https://api.brb8.win/api/cronjobs/get-user-playlog
          restartPolicy: Never
