$secret = kubectl get secret brb88bet-web-env -n brb88bet -o json | ConvertFrom-Json
$secret.data.psobject.Properties | ForEach-Object {
    $key = $_.Name
    $value = $_.Value
    $decoded = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($value))
    Write-Output "$key=$decoded"
}

# old backup
# NEXTAUTH_SECRET=P6dt5K4ujU7xexTAWgdRgodoVzP15owJIh+8rd+BJyg=
# NEXTAUTH_URL=https://brb8.win
# NEXT_PUBLIC_API_ENDPOINT=https://api.brb8.win/api
# NEXT_PUBLIC_SOCKET_ENDPOINT=https://socket.brb8.win
# NEXT_PUBLIC_WEB_NAME=brb88bet
# NEXT_PUBLIC_WEB_THEME_IMAGE=set08
# NEXT_PUBLIC_WEB_THEME_SWIPER=set01