kubectl create ns brb88bet
# -----------
kube<PERSON>l create secret docker-registry brb88bet-registrykey --docker-username=cyberrich --docker-password=************************************ --docker-email=<EMAIL> --namespace=brb88bet
kubectl create secret generic google-service-account-volume --namespace=brb88bet --from-file=google_service_account.json=google_service_account.json
kubectl create secret generic brb88bet-api-env --namespace=brb88bet --from-env-file=.env

# -----------
kubectl apply -f .\service.yaml
kubectl apply -f .\ingress.yaml
kubectl apply -f .\deployment.yaml

# ------------

kubectl apply -f .\manifest\brb88bet\cronJobs\cutDaily.yaml
kubectl apply -f .\manifest\brb88bet\cronJobs\simplewinlose.yaml

# ------------ env pull vault script
./vault.sh https://vault.cyberrichdigital.com/v1/cybergame-prod/data/brb88bet.top/api/.env env hvs.CAESINm-6Pav7HuhGZZEO9sZs5NerVnHVu_0VH6nJD2r3N1ZGh4KHGh2cy5GV0F3eVJ6TzlIWkpKR3dmcEFZWFVUNWo manifest/brb88bet