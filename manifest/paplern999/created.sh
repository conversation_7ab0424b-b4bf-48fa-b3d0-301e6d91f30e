kube<PERSON>l create ns paplern999
# -----------
kube<PERSON>l create secret docker-registry paplern999-registrykey --docker-username=cyberrich --docker-password=************************************ --docker-email=<EMAIL> --namespace=paplern999
kubectl create secret generic google-service-account-volume --namespace=paplern999 --from-file=google_service_account.json=google_service_account.json
kubectl create secret generic paplern999-api-env --namespace=paplern999 --from-env-file=.env

# -----------
kubectl apply -f .\service.yaml
kubectl apply -f .\ingress.yaml
kubectl apply -f .\deployment.yaml

# ------------

kubectl apply -f .\manifest\paplern999\cronJobs\cutDaily.yaml
kubectl apply -f .\manifest\paplern999\cronJobs\simplewinlose.yaml