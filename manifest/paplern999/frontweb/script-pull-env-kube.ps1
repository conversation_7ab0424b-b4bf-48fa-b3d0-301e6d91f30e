$secret = kubectl get secret paplern999-web-env -n paplern999 -o json | ConvertFrom-Json
$secret.data.psobject.Properties | ForEach-Object {
    $key = $_.Name
    $value = $_.Value
    $decoded = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($value))
    Write-Output "$key=$decoded"
}

# old backup
# NEXTAUTH_SECRET=uZSKegXGf42d5f87jSblRgW+iCsRYey/XGy3K3iD7nU=
# NEXTAUTH_URL=https://paplern999.com
# NEXT_PUBLIC_API_ENDPOINT=https://api.paplern999.com/api
# NEXT_PUBLIC_SOCKET_ENDPOINT=https://socket.paplern999.com
# NEXT_PUBLIC_WEB_NAME=paplern999
# NEXT_PUBLIC_WEB_THEME_IMAGE=set08
# NEXT_PUBLIC_WEB_THEME_SWIPER=set02