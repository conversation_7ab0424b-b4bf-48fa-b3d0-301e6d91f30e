$secret = kubectl get secret allin98-web-env -n allin98 -o json | ConvertFrom-Json
$secret.data.psobject.Properties | ForEach-Object {
    $key = $_.Name
    $value = $_.Value
    $decoded = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($value))
    Write-Output "$key=$decoded"
}

# old backup
# NEXTAUTH_SECRET=RYn+sjJiBGrBqBDq+FbFA8rKppKkG/0i6PW86dMLFzE=
# NEXTAUTH_URL=https://baga789.com
# NEXT_PUBLIC_API_ENDPOINT=https://api.baga789.com/api
# NEXT_PUBLIC_SOCKET_ENDPOINT=https://socket.baga789.com
# NEXT_PUBLIC_WEB_NAME=akrich88
# NEXT_PUBLIC_WEB_THEME_IMAGE=set03
# NEXT_PUBLIC_WEB_THEME_SWIPER=set03