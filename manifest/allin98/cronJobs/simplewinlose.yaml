apiVersion: batch/v1
kind: CronJob
metadata:
  name: allin98-simplewinlose
  namespace: allin98
spec:
  schedule: "30 05 * * *"
  successfulJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: callout
              image: buildpack-deps:curl
              imagePullPolicy: IfNotPresent
              command:
                - /bin/sh
                - -ec
                - curl -X GET https://api.allin98.net/api/cronjobs/simplewinlose
          restartPolicy: Never
