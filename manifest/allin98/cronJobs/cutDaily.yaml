apiVersion: batch/v1
kind: CronJob
metadata:
  name: allin98-cut-daily
  namespace: allin98
spec:
  schedule: "* * * * *"
  successfulJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: callout
              image: buildpack-deps:curl
              imagePullPolicy: IfNotPresent
              command:
                - /bin/sh
                - -ec
                - curl -X GET https://api.allin98.net/api/cron/promotion-return-loser/cut-daily
          restartPolicy: Never