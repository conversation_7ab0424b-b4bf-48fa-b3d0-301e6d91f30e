#!/bin/bash

# Usage: vault.sh <url> <file_type> <token> <destination>
URL=$1
FILE_TYPE=$2
TOKEN=$3
DESTINATION=$4

# Set output file name
if [ ! -z "$DESTINATION" ]; then
  mkdir -p "$DESTINATION"
  FILE_NAME="$DESTINATION/.env"
else
  FILE_NAME=".env"
fi

echo "Fetching from URL: $URL"
echo "File type: $FILE_TYPE"
echo "Token: <hidden for security>"
echo "Output file: $FILE_NAME"

# Fetch JSON data using curl and save it to a temporary file
TEMP_FILE=$(mktemp)
HTTP_STATUS=$(curl --silent --write-out "%{http_code}" --output "$TEMP_FILE" \
  --location "$URL" --header "X-Vault-Token: $TOKEN")

# Validate HTTP status
if [ "$HTTP_STATUS" -ne 200 ]; then
  echo "Error: HTTP status $HTTP_STATUS"
  echo "Response content:"
  cat "$TEMP_FILE"
  rm "$TEMP_FILE"
  exit 1
fi

# Check if jq is installed
if ! command -v jq &> /dev/null; then
  echo "jq is not installed. Please install jq to proceed."
  rm "$TEMP_FILE"
  exit 1
fi

# Debug response content
echo "Response from Vault:"
cat "$TEMP_FILE"

# Parse JSON response
if [ "$FILE_TYPE" = "env" ]; then
  # Extract the 'data.data' field and convert it to environment variable format
  if ! jq -r '.data?.data // {} | to_entries | .[] | .key + "=" + (.value | tostring)' "$TEMP_FILE" > "$FILE_NAME"; then
    echo "Error: Failed to parse JSON or unexpected response structure."
    rm "$TEMP_FILE"
    exit 1
  fi
else
  # Save raw JSON data to the file
  if ! jq -r '.data?.data // {}' "$TEMP_FILE" > "$FILE_NAME"; then
    echo "Error: Failed to parse JSON or unexpected response structure."
    rm "$TEMP_FILE"
    exit 1
  fi
fi

# Cleanup temporary file
rm "$TEMP_FILE"

echo "Environment file created: $FILE_NAME"
