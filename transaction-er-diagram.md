# TidTech API Transaction System - ER Diagram Documentation

## 1. System Overview
The TidTech API is a comprehensive financial transaction system for online gaming and betting platforms. It manages user deposits, withdrawals, promotions, affiliates, and various payment gateways.

## 2. Core Transaction Entities

### 2.1 Main Transaction Tables

#### **bank_transaction**
Primary transaction table for all financial movements.

| Column | Type | Description | Relationships |
|--------|------|-------------|---------------|
| id | BIGINT PK | Primary key | |
| member_code | VARCHAR(255) | User member code | |
| user_id | BIGINT FK | User reference | → user.id |
| statement_id | BIGINT FK | Bank statement reference | → bank_statement.id |
| transaction_type_id | BIGINT FK | Transaction type | → transaction_type.id |
| promotion_id | BIGINT FK | Applied promotion | → promotion_web.id |
| from_account_id | BIGINT FK | Source bank account | → bank_account.id |
| from_bank_id | BIGINT FK | Source bank | → bank.id |
| to_account_id | BIGINT FK | Destination account | → bank_account.id |
| to_bank_id | BIGINT FK | Destination bank | → bank.id |
| credit_amount | DECIMAL(14,2) | Transaction amount | |
| bonus_amount | DECIMAL(14,2) | Bonus amount applied | |
| before_amount | DECIMAL(14,2) | Balance before transaction | |
| after_amount | DECIMAL(14,2) | Balance after transaction | |
| transaction_status_id | BIGINT FK | Current status | → transaction_status.id |
| transfer_at | DATETIME | Transaction timestamp | |
| created_by_admin_id | BIGINT FK | Admin who created | → admin.id |
| confirmed_by_admin_id | BIGINT FK | Admin who confirmed | → admin.id |
| canceled_by_admin_id | BIGINT FK | Admin who canceled | → admin.id |
| is_auto_credit | BOOLEAN | Auto credit flag | |

#### **bank_statement**
Records all bank account movements (incoming/outgoing).

| Column | Type | Description | Relationships |
|--------|------|-------------|---------------|
| id | BIGINT PK | Primary key | |
| account_id | BIGINT FK | Bank account | → bank_account.id |
| external_id | BIGINT | External reference | |
| amount | DECIMAL(14,2) | Statement amount | |
| statement_type_id | BIGINT FK | Type (in/out) | → statement_type.id |
| statement_status_id | BIGINT FK | Processing status | → statement_status.id |
| from_bank_id | BIGINT FK | Source bank | → bank.id |
| from_account_number | VARCHAR(255) | Source account number | |
| transfer_at | DATETIME | Transfer timestamp | |

#### **bank_transaction_slip**
Stores deposit slip information and QR codes.

| Column | Type | Description | Relationships |
|--------|------|-------------|---------------|
| id | BIGINT PK | Primary key | |
| raw_qr_code | VARCHAR(255) | QR code data | |
| statement_id | BIGINT FK | Related statement | → bank_statement.id |
| slip_url | TEXT | Slip image URL | |
| amount | DECIMAL(14,2) | Slip amount | |
| status_id | BIGINT FK | Slip status | → transaction_status.id |
| user_id | BIGINT FK | User reference | → user.id |
| bank_id | BIGINT FK | Bank reference | → bank.id |
| bank_account_id | BIGINT FK | Bank account | → bank_account.id |
| deposited_at | DATETIME | Deposit timestamp | |

### 2.2 Transaction Type and Status Tables

#### **transaction_type**
| ID | Name | Description |
|----|------|-------------|
| 1 | DEPOSIT | User deposit |
| 2 | WITHDRAW | User withdrawal |
| 3 | BONUS | Bonus credit |
| 4 | CREDITBACK | Credit return |
| 5 | CREDITCANCEL | Credit cancellation |

#### **transaction_status**
| ID | Name | Description |
|----|------|-------------|
| 1 | PENDING | Pending processing |
| 2 | DEPOSIT_PENDING_CREDIT | Deposit awaiting credit |
| 3 | DEPOSIT_PENDING_SLIP | Awaiting slip upload |
| 4 | DEPOSIT_PENDING_MULTIUSER | Multiple user match |
| 5 | DEPOSIT_CREDIT_APPROVED | Deposit approved |
| 6 | DEPOSIT_CREDIT_REJECTED | Deposit rejected |
| 7 | WITHDRAW_PENDING | Withdrawal pending |
| 8 | WITHDRAW_OVER_BUDGET | Exceeds budget limit |
| 9 | WITHDRAW_APPROVED | Withdrawal approved |
| 10 | WITHDRAW_REJECTED | Withdrawal rejected |
| 11 | WITHDRAW_FAILED | Withdrawal failed |
| 12 | WITHDRAW_SUCCESS | Withdrawal successful |
| 13 | WITHDRAW_OVER_MAX | Exceeds max amount |
| 14 | WITHDRAW_CANCELED | Withdrawal canceled |
| 15 | DEPOSIT_IGNORE | Deposit ignored |
| 16 | WITHDRAW_UNSURE | Uncertain status |
| 17 | WITHDRAW_TRANSFERRING | Transfer in progress |

#### **statement_type**
| ID | Name | Description |
|----|------|-------------|
| 1 | INCOMING | Incoming transaction (deposit) |
| 2 | OUTGOING | Outgoing transaction (withdrawal) |

#### **statement_status**
| ID | Name | Description |
|----|------|-------------|
| 1 | PENDING | Pending processing |
| 2 | MATCHED | Matched with transaction |
| 3 | UNMATCHED | No matching transaction |
| 4 | PROCESSED | Successfully processed |
| 5 | FAILED | Processing failed |
| 6 | IGNORED | Ignored/skipped |

## 3. User and Account Management

### 3.1 User Tables

#### **user**
Core user table for the system.

| Column | Type | Description | Relationships |
|--------|------|-------------|---------------|
| id | BIGINT PK | Primary key | |
| member_code | VARCHAR(255) | Unique member code | |
| username | VARCHAR(255) | Login username | |
| phone | VARCHAR(20) | Phone number | |
| password | VARCHAR(255) | Encrypted password | |
| user_status_id | BIGINT FK | User status | → user_status.id |
| fullname | VARCHAR(255) | Full name | |
| bank_account | VARCHAR(50) | Bank account number | |
| bank_id | BIGINT FK | Primary bank | → bank.id |
| channel_id | BIGINT FK | Registration channel | → channel.id |
| credit | DECIMAL(14,2) | Current balance | |
| created_by | BIGINT FK | Creator admin/affiliate | → admin.id or affiliate.id |
| ip_registered | VARCHAR(45) | Registration IP | |

#### **user_transaction**
Tracks all user credit movements.

| Column | Type | Description | Relationships |
|--------|------|-------------|---------------|
| id | BIGINT PK | Primary key | |
| user_id | BIGINT FK | User reference | → user.id |
| transaction_type_id | BIGINT FK | Transaction type | → user_transaction_type.id |
| amount | DECIMAL(14,2) | Transaction amount | |
| before_amount | DECIMAL(14,2) | Balance before | |
| after_amount | DECIMAL(14,2) | Balance after | |
| reference_id | BIGINT | Reference to source transaction | |
| created_at | DATETIME | Transaction timestamp | |

## 4. Banking System

### 4.1 Bank Configuration

#### **bank**
Master list of supported banks.

| Column | Type | Description | Relationships |
|--------|------|-------------|---------------|
| id | BIGINT PK | Primary key | |
| name | VARCHAR(255) | Bank name | |
| code | VARCHAR(10) | Bank code | |
| short_name | VARCHAR(50) | Short name | |
| color | VARCHAR(7) | Display color | |
| icon_url | VARCHAR(255) | Bank icon URL | |
| is_active | BOOLEAN | Active flag | |

#### **bank_account**
Company bank accounts for receiving deposits.

| Column | Type | Description | Relationships |
|--------|------|-------------|---------------|
| id | BIGINT PK | Primary key | |
| bank_id | BIGINT FK | Bank reference | → bank.id |
| account_name | VARCHAR(255) | Account name | |
| account_number | VARCHAR(50) | Account number | |
| account_type_id | BIGINT FK | Account type | → account_type.id |
| account_status_id | BIGINT FK | Account status | → account_status.id |
| daily_limit | DECIMAL(14,2) | Daily transaction limit | |
| monthly_limit | DECIMAL(14,2) | Monthly transaction limit | |

## 5. Promotion System

### 5.1 Promotion Tables

#### **promotion_web**
Web promotions and bonuses configuration.

| Column | Type | Description | Relationships |
|--------|------|-------------|---------------|
| id | BIGINT PK | Primary key | |
| promotion_web_type_id | BIGINT FK | Promotion type | → promotion_web_type.id |
| promotion_web_status_id | BIGINT FK | Status | → promotion_web_status.id |
| name | VARCHAR(255) | Promotion name | |
| image_url | VARCHAR(255) | Promotion image | |
| start_date | DATE | Start date | |
| end_date | DATE | End date | |
| free_bonus_amount | DECIMAL(14,2) | Free bonus amount | |
| bonus_condition_amount | DECIMAL(14,2) | Condition amount | |
| bonus_type_amount | DECIMAL(14,2) | Bonus amount/percentage | |
| turnover_amount | INT | Required turnover | |
| min_deposit | DECIMAL(14,2) | Minimum deposit | |
| max_bonus | DECIMAL(14,2) | Maximum bonus | |

#### **promotion_web_user**
Tracks user participation in promotions.

| Column | Type | Description | Relationships |
|--------|------|-------------|---------------|
| id | BIGINT PK | Primary key | |
| promotion_web_id | BIGINT FK | Promotion | → promotion_web.id |
| user_id | BIGINT FK | User | → user.id |
| promotion_web_user_status_id | BIGINT FK | Status | → promotion_web_user_status.id |
| received_amount | DECIMAL(14,2) | Amount received | |
| turnover_amount | DECIMAL(14,2) | Turnover achieved | |
| turnover_target | DECIMAL(14,2) | Turnover required | |
| created_at | DATETIME | Participation date | |

### 5.2 Promotion Types

#### **promotion_web_type**
| ID | Name | Description |
|----|------|-------------|
| 1 | NEW_MEMBER_FREE | New member free bonus |
| 2 | NEW_MEMBER_CONDITION | New member conditional bonus |
| 3 | DEPOSIT_MINIMUM_PER_DAY | Daily minimum deposit |
| 4 | FIRST_DEPOSIT | First deposit bonus |
| 5 | DEPOSIT_PER_DAY | All day deposit bonus |
| 6 | DEPOSIT_BY_TIME | Time-based deposit bonus |
| 7 | FIRST_DEPOSIT_OF_DAY | First deposit of the day |

## 6. Payment Gateway Integration

### 6.1 Payment Gateway Tables

#### **payment_gateway**
Configured payment gateways.

| Column | Type | Description | Relationships |
|--------|------|-------------|---------------|
| id | BIGINT PK | Primary key | |
| name | VARCHAR(255) | Gateway name | |
| code | VARCHAR(50) | Gateway code | |
| gateway_type_id | BIGINT FK | Gateway type | → payment_gateway_type.id |
| is_active | BOOLEAN | Active flag | |
| min_amount | DECIMAL(14,2) | Minimum transaction | |
| max_amount | DECIMAL(14,2) | Maximum transaction | |
| fee_percent | DECIMAL(5,2) | Transaction fee % | |
| api_url | VARCHAR(255) | API endpoint | |
| merchant_id | VARCHAR(255) | Merchant ID | |
| api_key | TEXT | Encrypted API key | |

### 6.2 Gateway-Specific Tables

Each payment gateway has its own transaction tracking table:
- **paygate_bizpay_transaction**
- **paygate_flashpay_transaction**
- **paygate_jbpayment_transaction**
- **paygate_mazepay_transaction**
- **paygate_meepay_transaction**
- **paygate_onepay_transaction**
- **paygate_papaya_transaction**
- **paygate_paymentco_transaction**
- **paygate_payonex_transaction**
- **paygate_pompay_transaction**
- **paygate_postmanpay_transaction**
- **paygate_sugarpay_transaction**
- **paygate_zappay_transaction**
- **paygate_zmanpay_transaction**

Common structure:
| Column | Type | Description | Relationships |
|--------|------|-------------|---------------|
| id | BIGINT PK | Primary key | |
| bank_transaction_id | BIGINT FK | Main transaction | → bank_transaction.id |
| gateway_transaction_id | VARCHAR(255) | Gateway's transaction ID | |
| status | VARCHAR(50) | Gateway status | |
| response_data | TEXT | Gateway response JSON | |
| created_at | DATETIME | Transaction timestamp | |

## 7. Affiliate and Alliance System

### 7.1 Affiliate Tables

#### **affiliate**
Affiliate partner configuration.

| Column | Type | Description | Relationships |
|--------|------|-------------|---------------|
| id | BIGINT PK | Primary key | |
| user_id | BIGINT FK | Associated user | → user.id |
| register_bonus_credit | DECIMAL(14,2) | Registration bonus | |
| is_active | BOOLEAN | Active flag | |
| created_at | DATETIME | Creation date | |

#### **affiliate_income**
Tracks affiliate earnings.

| Column | Type | Description | Relationships |
|--------|------|-------------|---------------|
| id | BIGINT PK | Primary key | |
| affiliate_id | BIGINT FK | Affiliate | → affiliate.id |
| sport | DECIMAL(14,2) | Sports betting income | |
| casino | DECIMAL(14,2) | Casino income | |
| slot | DECIMAL(14,2) | Slot income | |
| p2p | DECIMAL(14,2) | P2P income | |
| total | DECIMAL(14,2) | Total income | |
| period_date | DATE | Income period | |

### 7.2 Alliance Tables

#### **alliance**
Alliance partner configuration.

| Column | Type | Description | Relationships |
|--------|------|-------------|---------------|
| id | BIGINT PK | Primary key | |
| ref_id | BIGINT FK | Reference user | → user.id |
| turnover_percent | DECIMAL(5,2) | Turnover percentage | |
| credit_holding | DECIMAL(14,2) | Holding amount | |
| is_active | BOOLEAN | Active flag | |

## 8. Entity Relationship Diagram (Mermaid)

```mermaid
erDiagram
    USER ||--o{ BANK_TRANSACTION : "has"
    USER ||--o{ USER_TRANSACTION : "tracks"
    USER ||--o{ PROMOTION_WEB_USER : "participates"
    USER }o--|| BANK : "primary_bank"
    USER }o--|| USER_STATUS : "has_status"
    
    BANK_TRANSACTION }o--|| TRANSACTION_TYPE : "has_type"
    BANK_TRANSACTION }o--|| TRANSACTION_STATUS : "has_status"
    BANK_TRANSACTION }o--|| BANK_STATEMENT : "references"
    BANK_TRANSACTION }o--|| PROMOTION_WEB : "applies"
    BANK_TRANSACTION }o--|| BANK_ACCOUNT : "from_account"
    BANK_TRANSACTION }o--|| BANK_ACCOUNT : "to_account"
    BANK_TRANSACTION }o--|| ADMIN : "created_by"
    BANK_TRANSACTION }o--|| ADMIN : "confirmed_by"
    
    BANK_STATEMENT }o--|| BANK_ACCOUNT : "belongs_to"
    BANK_STATEMENT }o--|| STATEMENT_TYPE : "has_type"
    BANK_STATEMENT }o--|| STATEMENT_STATUS : "has_status"
    BANK_STATEMENT }o--|| BANK : "from_bank"
    
    STATEMENT_TYPE {
        int id PK
        string name
        string description
    }
    
    STATEMENT_STATUS {
        int id PK
        string name
        string description
    }
    
    BANK_TRANSACTION_SLIP }o--|| USER : "uploaded_by"
    BANK_TRANSACTION_SLIP }o--|| BANK : "bank"
    BANK_TRANSACTION_SLIP }o--|| BANK_STATEMENT : "references"
    BANK_TRANSACTION_SLIP }o--|| TRANSACTION_STATUS : "has_status"
    
    BANK_ACCOUNT }o--|| BANK : "belongs_to"
    BANK_ACCOUNT }o--|| ACCOUNT_TYPE : "has_type"
    BANK_ACCOUNT }o--|| ACCOUNT_STATUS : "has_status"
    
    PROMOTION_WEB }o--|| PROMOTION_WEB_TYPE : "has_type"
    PROMOTION_WEB }o--|| PROMOTION_WEB_STATUS : "has_status"
    PROMOTION_WEB ||--o{ PROMOTION_WEB_USER : "used_by"
    
    PAYMENT_GATEWAY ||--o{ PAYGATE_TRANSACTION : "processes"
    PAYGATE_TRANSACTION }o--|| BANK_TRANSACTION : "references"
    
    AFFILIATE }o--|| USER : "associated_with"
    AFFILIATE ||--o{ AFFILIATE_INCOME : "earns"
    
    ALLIANCE }o--|| USER : "references"
    ALLIANCE ||--o{ ALLIANCE_TRANSACTION : "has"
```

## 9. Transaction Flow Diagrams

### 9.1 Deposit Flow

```mermaid
sequenceDiagram
    participant User
    participant API
    participant BankStatement
    participant BankTransaction
    participant UserTransaction
    participant PromotionEngine
    
    User->>API: Upload deposit slip/QR
    API->>BankStatement: Create pending statement
    API->>BankTransaction: Create pending transaction
    API->>PromotionEngine: Check applicable promotions
    PromotionEngine-->>API: Return bonus amount
    API->>BankTransaction: Update with bonus
    API->>UserTransaction: Update user credit
    API->>User: Confirm deposit
```

### 9.2 Withdrawal Flow

```mermaid
sequenceDiagram
    participant User
    participant API
    participant BankTransaction
    participant PaymentGateway
    participant UserTransaction
    
    User->>API: Request withdrawal
    API->>BankTransaction: Create withdrawal transaction
    API->>UserTransaction: Deduct user credit
    API->>PaymentGateway: Process withdrawal
    PaymentGateway-->>API: Confirm transfer
    API->>BankTransaction: Update status
    API->>User: Confirm withdrawal
```

## 10. Key Business Rules

### 10.1 Transaction Processing Rules

1. **Deposit Rules:**
   - Minimum deposit amount varies by promotion
   - QR code validation required for automated processing
   - Multi-user detection prevents duplicate credits
   - Bonus calculation based on active promotions

2. **Withdrawal Rules:**
   - Turnover requirements must be met
   - Daily/monthly limits apply per user tier
   - Auto-withdrawal available for verified accounts
   - Multiple payment gateways for redundancy

3. **Promotion Rules:**
   - User can only have one active promotion
   - Turnover requirements = (deposit + bonus) × multiplier
   - Time-based promotions have specific windows
   - First deposit bonuses are one-time only

### 10.2 Status Transitions

**Deposit Status Flow:**
```
PENDING → PENDING_CREDIT → CREDIT_APPROVED
        ↓                 ↓
    PENDING_SLIP    CREDIT_REJECTED
        ↓
    PENDING_MULTIUSER
```

**Withdrawal Status Flow:**
```
PENDING → APPROVED → TRANSFERRING → SUCCESS
        ↓         ↓              ↓
    OVER_BUDGET  REJECTED      FAILED
        ↓
    OVER_MAX → CANCELED
```

## 11. Indexes and Performance Considerations

### Critical Indexes:
- `bank_transaction`: (user_id, transaction_type_id, created_at)
- `bank_statement`: (account_id, statement_status_id, transfer_at)
- `user_transaction`: (user_id, created_at)
- `promotion_web_user`: (user_id, promotion_web_id, status_id)

### Partitioning Strategy:
- Consider partitioning `bank_transaction` by month
- Archive completed transactions older than 1 year
- Separate read replicas for reporting queries

## 12. Security Considerations

1. **Sensitive Data Encryption:**
   - User passwords (bcrypt)
   - Payment gateway API keys
   - Bank account numbers (partial masking)

2. **Audit Trail:**
   - All transactions logged with admin actions
   - IP tracking for user activities
   - Immutable transaction history

3. **Access Control:**
   - Role-based permissions for admin operations
   - Transaction limits based on user tier
   - Multi-factor authentication for withdrawals

## 13. Integration Points

### External Systems:
1. **Payment Gateways** - 14 different providers
2. **Bank APIs** - QR code validation services
3. **SMS Providers** - OTP and notifications
4. **Game Providers** - Credit integration
5. **Affiliate Networks** - Commission tracking

### Internal Services:
1. **Notification Service** - Transaction alerts
2. **Reporting Service** - Financial reports
3. **Risk Management** - Fraud detection
4. **Customer Support** - Transaction queries

---

*Document Version: 1.0*  
*Last Updated: 2024*  
*System: TidTech API - Financial Transaction Management System*