package repository

import (
	"bytes"
	"cybergame-api/model"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"math/rand"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	"gorm.io/gorm"
)

func NewWebPopupRepository(db *gorm.DB) WebPopupRepository {
	return &repo{db}
}

type WebPopupRepository interface {
	GetWebPopupById(id int64) (*model.WebPopupResponse, error)
	GetWebPopupList(req model.WebPopupListRequest) ([]model.WebPopupResponse, int64, error)
	CreateWebPopup(body model.WebPopupCreateBody) (*int64, error)
	UpdateWebPopup(id int64, body model.WebPopupUpdateBody) error
	SortOrderWebPopup(req model.DragSortRequest) error
	DeleteWebPopup(id int64) error
	UploadImageToCloudflare(pathUplaod string, filename string, fileReader io.Reader) (*model.CloudFlareUploadCreateBody, error)
	DeleteUploadImages(imageCloudFlarePathName string) error
	UploadImageToS3(pathUpload string, filename string, fileReader io.Reader) (*model.FileUploadResponse, error)

	GetAllUploads() (*model.GetAllUploadsResponse, error)
	DownloadAllSource() error
}

func (r repo) GetWebPopupById(id int64) (*model.WebPopupResponse, error) {

	var record model.WebPopupResponse

	selectedFields := "tb_popup.id AS id, tb_popup.sort_order AS sort_order, tb_popup.img_path AS img_path, tb_popup.is_show AS is_show, tb_popup.created_at AS created_at, tb_popup.updated_at AS updated_at"
	if err := r.db.Table("web_popup as tb_popup").
		Select(selectedFields).
		Where("tb_popup.id = ?", id).
		Where("tb_popup.deleted_at IS NULL").
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetWebPopupList(req model.WebPopupListRequest) ([]model.WebPopupResponse, int64, error) {

	var list []model.WebPopupResponse
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("web_popup as tb_popup")
	count = count.Select("tb_popup.id")
	if req.IsShow != nil {
		count = count.Where("tb_popup.is_show = ?", *req.IsShow)
	}
	if err = count.
		Where("tb_popup.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_popup.id AS id, tb_popup.sort_order AS sort_order, tb_popup.img_path AS img_path, tb_popup.is_show AS is_show, tb_popup.created_at AS created_at, tb_popup.updated_at AS updated_at"
		query := r.db.Table("web_popup as tb_popup")
		query = query.Select(selectedFields)
		if req.IsShow != nil {
			query = query.Where("tb_popup.is_show = ?", *req.IsShow)
		}
		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		} else {
			query = query.Order("tb_popup.sort_order ASC")
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Where("tb_popup.deleted_at IS NULL").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}

	return list, total, nil
}

func (r repo) CreateWebPopup(body model.WebPopupCreateBody) (*int64, error) {

	if err := r.db.Table("web_popup").Create(&body).Error; err != nil {
		return nil, err
	}

	// SORT_OERDER //
	updateBody := map[string]interface{}{
		"sort_order": body.Id,
	}
	if err := r.db.Table("web_popup").Where("id = ?", body.Id).Updates(updateBody).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) UpdateWebPopup(id int64, body model.WebPopupUpdateBody) error {

	if err := r.db.Table("web_popup").Where("id = ?", id).Updates(body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) SortOrderWebPopup(req model.DragSortRequest) error {

	tableName := "web_popup"

	var list []model.SortOrder
	selectedFields := "id, sort_order"
	query := r.db.Table(tableName)
	query = query.Select(selectedFields)
	if err := query.
		Where("id IN ?", []int64{req.FromItemId, req.ToItemId}).
		Limit(2).
		Find(&list).
		Error; err != nil {
		return err
	}

	var fromItem *model.SortOrder
	var toItem *model.SortOrder
	for _, record := range list {
		if record.Id == req.FromItemId {
			fromItem = &model.SortOrder{
				Id:        record.Id,
				SortOrder: record.SortOrder,
			}
		} else if record.Id == req.ToItemId {
			toItem = &model.SortOrder{
				Id:        record.Id,
				SortOrder: record.SortOrder,
			}
		}
	}

	if fromItem != nil && toItem != nil {
		// Sort Direction //
		if fromItem.SortOrder < toItem.SortOrder {
			// Drag down  //
			whereShiftDown := r.db.Where("sort_order > ?", fromItem.SortOrder).Where("sort_order <= ?", toItem.SortOrder)
			if err := r.db.Table(tableName).Where(whereShiftDown).Update("sort_order", gorm.Expr("sort_order - 1")).Error; err != nil {
				return err
			}
			// Go to
			if err := r.db.Table(tableName).Where("id = ?", fromItem.Id).Update("sort_order", toItem.SortOrder).Error; err != nil {
				return err
			}
		} else if fromItem.SortOrder > toItem.SortOrder {
			// Drag up = shift up //
			whereShiftDown := r.db.Where("sort_order < ?", fromItem.SortOrder).Where("sort_order >= ?", toItem.SortOrder)
			if err := r.db.Table(tableName).Where(whereShiftDown).Update("sort_order", gorm.Expr("sort_order + 1")).Error; err != nil {
				return err
			}
			// Go to
			if err := r.db.Table(tableName).Where("id = ?", fromItem.Id).Update("sort_order", toItem.SortOrder).Error; err != nil {
				return err
			}
		}
	}
	return nil
}

func (r repo) DeleteWebPopup(id int64) error {

	if err := r.db.Table("web_popup").Where("id = ?", id).Delete(&model.WebPopup{}).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) DeleteUploadImages(imageCloudFlarePathName string) error {
	// Extract the desired substring from the imageCloudFlarePathName
	extractedSubstring := extractSubstring(imageCloudFlarePathName)
	if extractedSubstring == "" {
		log.Println("DeleteUploadImages.Error: extractedSubstring is empty :", imageCloudFlarePathName)
		return nil
	}

	accountId := os.Getenv("CLOUDFLARE_ACCOUNT_ID")
	token := os.Getenv("CLOUDFLARE_API_TOKEN")
	url := os.Getenv("CLOUDFLARE_UPLOAD_URL") + "/accounts/" + accountId + "/images/v1/" + extractedSubstring

	request, err := http.NewRequest("DELETE", url, nil)
	if err != nil {
		log.Println("DeleteUploadImages.NewRequest.Error:", err)
		return err
	}
	request.Header.Set("Authorization", "Bearer "+token)

	client := &http.Client{}
	resp, err := client.Do(request)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	responseData, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	var response model.CloudFlareDeleteResponse
	if err := json.Unmarshal([]byte(responseData), &response); err != nil {
		log.Println("DeleteUploadImages.Error.Unmarshal:", err)
		return err
	}

	if resp.StatusCode != 200 {
		if response.Errors[0].Code == 5404 {
			return nil
		}
		return errors.New("DeleteUploadImages.Error: remote response status code," + string(responseData))
	}

	return nil
}

func extractSubstring(imageCloudFlarePathName string) string {
	parts := strings.Split(imageCloudFlarePathName, "/")
	var startIndex, endIndex int
	for i, part := range parts {
		if part == "imagedelivery.net" {
			startIndex = i + 3
		}
		if part == "public" && i > startIndex {
			endIndex = i
			break
		}
	}
	if startIndex > 0 && endIndex > 0 {
		return strings.Join(parts[startIndex-1:endIndex], "/")
	}
	return ""
}

func (r repo) UploadImageToCloudflare(pathUplaod string, filename string, fileReader io.Reader) (*model.CloudFlareUploadCreateBody, error) {

	// [set imageCloudFlarePathName]
	randomFilename := generateRandomString()

	// [set imageCloudFlarePathName]
	// imageCloudFlarePathName := fmt.Sprintf("cbgame/%v/web-popup/upload/image/%v", dbName, randomFilename)
	imageCloudFlarePathName := fmt.Sprintf(pathUplaod + randomFilename)
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)
	// "https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/themes/set01/play-n-go.png/public"
	// ที่ใช้ id เรา สามารถกำหนดชื่อ id เองได้ ถ้าไม่ใส่ จะได้ response Id อะไรกลับมาไม่รู้ ถีงแม่ว่าชื่อ file จะเป็นชื่อ path ก็ตาม
	// "url/id/variants"
	writer.WriteField("id", imageCloudFlarePathName)
	part, err := writer.CreateFormFile("file", filename)
	if err != nil {
		log.Println("UploadImageToCloudflare.CreateFormFile.Error:", err)
		return nil, err
	}
	_, err = io.Copy(part, fileReader)
	if err != nil {
		log.Println("UploadImageToCloudflare.Copy.Error:", err)
		return nil, err
	}
	writer.Close()

	accountId := os.Getenv("CLOUDFLARE_ACCOUNT_ID")
	token := os.Getenv("CLOUDFLARE_API_TOKEN")
	url := os.Getenv("CLOUDFLARE_UPLOAD_URL") + "/accounts/" + accountId + "/images/v1"

	request, err := http.NewRequest("POST", url, body)
	if err != nil {
		log.Println("UploadImageToCloudflare.NewRequest.Error:", err)
		return nil, err
	}
	request.Header.Set("Authorization", "Bearer "+token)
	request.Header.Set("Content-Type", writer.FormDataContentType())

	client := &http.Client{}
	resp, err := client.Do(request)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	responseData, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode != 200 {
		log.Println("UploadImageToCloudflare.Error: remote response status code,", string(responseData))
		return nil, errors.New("UploadImageToCloudflare.Error: remote response status code," + string(responseData))
	}

	var response model.CloudFlareUploadResponse
	if err := json.Unmarshal([]byte(responseData), &response); err != nil {
		log.Println("UploadImageToCloudflare.Error.Unmarshal:", err)
		return nil, err
	}

	saveData := model.CloudFlareUploadCreateBody{
		ImageId:           response.Result.Id,
		Filename:          response.Result.Filename,
		Uploaded:          response.Result.Uploaded,
		RequireSignedURLs: response.Result.RequireSignedURLs,
		FileUrl:           response.Result.Variants[0],
	}

	return &saveData, nil
}

func generateRandomString() string {
	currentTime := time.Now()
	randomPart := rand.Intn(1000) // Generate a random number between 0 and 999
	return fmt.Sprintf("%d%d%d_%d%d%d", currentTime.Year(), currentTime.Month(), currentTime.Day(), currentTime.Hour(), currentTime.Minute(), randomPart)
}

func (r repo) UploadImageToS3(pathUpload string, filename string, fileReader io.Reader) (*model.FileUploadResponse, error) {
	bucketName := os.Getenv("AWS_S3_BUCKET_NAME")

	randomFilename := generateRandomString()
	fileKey := fmt.Sprintf("%s%s", pathUpload, randomFilename)

	awsRegion := os.Getenv("AWS_S3_BUCKET_REGION")
	awsAccessKey := os.Getenv("AWS_S3_BUCKET_ACCESS_KEY_ID")
	awsSecretKey := os.Getenv("AWS_S3_BUCKET_SECRET_ACCESS_KEY")

	sess, err := session.NewSession(&aws.Config{
		Region:      aws.String(awsRegion),
		Credentials: credentials.NewStaticCredentials(awsAccessKey, awsSecretKey, ""),
	})
	if err != nil {
		log.Println("UploadImageToS3.NewSession.Error:", err)
		return nil, errors.New("failed to create AWS session")
	}

	s3Client := s3.New(sess)

	body := &bytes.Buffer{}
	content, err := io.Copy(body, fileReader)
	if err != nil {
		log.Println("UploadImageToS3.Copy.Error:", err)
		return nil, err
	}

	input := &s3.PutObjectInput{
		Bucket:        aws.String(bucketName),
		Key:           aws.String(fileKey),
		Body:          bytes.NewReader(body.Bytes()),
		ContentLength: aws.Int64(content),
		ContentType:   aws.String(http.DetectContentType(body.Bytes())),
		//ACL:           aws.String("public-read"),
	}

	_, err = s3Client.PutObject(input)
	if err != nil {
		log.Println("UploadImageToS3.PutObject.Error:", err)
		return nil, fmt.Errorf("failed to upload file to S3: %w", err)
	}

	fileUrl := ""
	cloudFrontUrl := os.Getenv("AWS_S3_CLOUDFRONT_URL")
	if cloudFrontUrl == "" {
		// old
		// https://cbgame.s3.ap-southeast-1.amazonaws.com/cbgame/db_brobet/bank-account/2025213_1338732
		//fileUrl = fmt.Sprintf("https://%s.s3.%s.amazonaws.com/%s", bucketName, awsRegion, fileKey)

		// Hardcode.
		fileUrl = fmt.Sprintf("https://cdn.tidtech.net/%s", fileKey)
	} else {
		// ย้ายมาเป็น cloudfront 2025-04-07
		// https://cdn.cbgame88.com/cbgame/db_brobet/bank-account/2025213_1338732
		fileUrl = fmt.Sprintf("%s/%s", cloudFrontUrl, fileKey)
	}
	response := &model.FileUploadResponse{
		ImageUrl: fileUrl,
	}

	return response, nil
}

func (r repo) GetAllUploads() (*model.GetAllUploadsResponse, error) {

	accountId := os.Getenv("CLOUDFLARE_ACCOUNT_ID")
	token := os.Getenv("CLOUDFLARE_API_TOKEN")
	url := fmt.Sprintf("%s/accounts/%s/images/v1?page=%d&per_page=%d",
		os.Getenv("CLOUDFLARE_UPLOAD_URL"), accountId, 1, 5)

	request, err := http.NewRequest("GET", url, nil)
	if err != nil {
		log.Println("GetUploadsByPage.NewRequest.Error:", err)
		return nil, err
	}
	request.Header.Set("Authorization", "Bearer "+token)

	client := &http.Client{}
	resp, err := client.Do(request)
	if err != nil {
		log.Println("GetUploadsByPage.Do.Error:", err)
		return nil, err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		responseData, _ := io.ReadAll(resp.Body)
		log.Println("GetUploadsByPage.Error: remote response status code,", string(responseData))
		return nil, errors.New("GetUploadsByPage.Error: remote response status code," + string(responseData))
	}

	responseData, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Println("GetUploadsByPage.ReadAll.Error:", err)
		return nil, err
	}

	// fmt.Println("Raw Response JSON:", string(responseData)) // Debugging

	var response model.GetAllUploadsResponse
	if err := json.Unmarshal(responseData, &response); err != nil {
		log.Println("GetUploadsByPage.Unmarshal.Error:", err)
		return nil, err
	}

	if !response.Success {
		log.Println("GetUploadsByPage.Error: response success is false,", response.Errors)
		return nil, errors.New("GetUploadsByPage.Error: response success is false")
	}

	fmt.Println("Parsed Response Result:", response.Result)
	return &response, nil
}

func imagesource() []string {

	var images = []string{
		// example
		"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/729e3fa1-1d7f-430c-65f4-966fa6658900/public",
	}

	return images
}

func (r repo) DownloadAllSource() error {
	// Directory to save images
	outputDir := "F:/xVhPJrN2Mvh3likGpmWIgg"
	err := os.MkdirAll(outputDir, os.ModePerm)
	if err != nil {
		return fmt.Errorf("failed to create output directory: %w", err)
	}

	images := imagesource()
	var wg sync.WaitGroup      // To wait for all goroutines to finish
	var mu sync.Mutex          // Mutex to safely update the errors slice
	var errorMessages []string // To store error messages

	for _, image := range images {
		wg.Add(1)
		go func(image string) {
			defer wg.Done()

			// Extract the filename from the URL
			parts := strings.Split(image, "/")
			filename := parts[len(parts)-2] // Gets the unique ID from the URL

			// Full path to save the file
			filepath := filepath.Join(outputDir, fmt.Sprintf("%s", filename))

			// Download the image
			err := downloadFile(image, filepath)
			if err != nil {
				// Add error message to the slice
				mu.Lock()
				errorMessages = append(errorMessages, fmt.Sprintf("failed to download image %s: %v", image, err))
				mu.Unlock()
				fmt.Printf("failed to download image %s: %v\n", image, err)
			} else {
				fmt.Printf("successfully downloaded: %s\n", filepath)
			}
		}(image) // Pass `image` as an argument to avoid closure issues
	}

	wg.Wait() // Wait for all goroutines to complete

	// Return collected errors along with nil or some other error if needed
	if len(errorMessages) > 0 {
		return fmt.Errorf("some images failed to download")
	}

	return nil
}

// downloadFile downloads a file from a URL and saves it to the specified path
func downloadFile(url string, filepath string) error {
	// Make HTTP GET request
	resp, err := http.Get(url)
	if err != nil {
		return fmt.Errorf("failed to make GET request: %w", err)
	}
	defer resp.Body.Close()

	// Check if the response is successful
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	// Create the file
	out, err := os.Create(filepath)
	if err != nil {
		return fmt.Errorf("failed to create file: %w", err)
	}
	defer out.Close()

	// Write the response body to the file
	_, err = io.Copy(out, resp.Body)
	if err != nil {
		return fmt.Errorf("failed to save file: %w", err)
	}

	return nil
}
