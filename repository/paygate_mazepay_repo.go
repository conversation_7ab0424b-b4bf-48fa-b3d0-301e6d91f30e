package repository

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"cybergame-api/helper"
	"cybergame-api/model"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm"
)

func NewMazepayRepository(db *gorm.DB) MazepayRepository {
	return &repo{db}
}

type MazepayRepository interface {
	GetDb() *gorm.DB
	// Mazepay-RD
	// MazepayEncryptRepayDesposit(partnerKey string, amount int64) (*model.MazepayEncryptPayload, error)

	// MERCHANT
	// GetNOTUSEPaygateMerchantById(id int64) (*model.PaygateMerchantNgResponse, error)
	// GetPaygateMerchantList(req model.PaygateMerchantListRequest) ([]model.PaygateMerchantNgResponse, int64, error)
	// CreatePaygateMerchant(body model.PaygateMerchantCreateBody) (*int64, error)
	// UpdatePaygateMerchant(id int64, body model.PaygateMerchantUpdateBody) error
	// DeletePaygateMerchant(id int64) error
	// // SETTING
	// GetPaygateSetting() (*model.PaygateSettingResponse, error)
	// CreatePaygateSetting(body model.PaygateSettingCreateBody) (*int64, error)
	// UpdatePaygateSetting(id int64, body model.PaygateSettingUpdateBody) error
	// DeletePaygateSetting(id int64) error
	// // AdminLog
	// GetPaygateAdminLogById(id int64) (*model.PaygateAdminLogResponse, error)
	// GetPaygateAdminLogList(req model.PaygateAdminLogListRequest) ([]model.PaygateAdminLogResponse, int64, error)
	// CreatePaygateAdminLog(body model.PaygateAdminLogCreateBody) (*int64, error)
	// UpdatePaygateAdminLog(id int64, body model.PaygateAdminLogUpdateBody) error
	// // SystemLog
	CreatePaygateSystemLog(body model.PaygateSystemLogCreateBody) (*int64, error)
	// UpdatePaygateSystemLog(id int64, body model.PaygateSystemLogUpdateBody) error

	// REF-PAYGATE
	GetRawMazepayPendingDepositOrderById(id int64) (*model.MazepayOrderResponse, error)
	GetPaygateAccountByProviderId(id int64) (*model.PaygateAccountResponse, error)
	// Mazepay-REMOTE
	// MazepayGetToken(setting model.PaygateAccountResponse) (*model.MazepayTokenCreateRemoteResponse, error)
	MazepayDeposit(setting model.PaygateAccountResponse, req model.MazepayDepositCreateRemoteRequest) (*model.MazepayDepositCreateRemoteResponse, error)
	MazepayWithdraw(setting model.PaygateAccountResponse, req model.MazepayWithdrawCreateRemoteRequest) (*model.MazepayWithdrawCreateRemoteResponse, error)
	// MazepayCheckBalance(setting model.PaygateAccountResponse) (*model.MazepayCheckBalanceResponse, error)
	MazepayGetOrder(setting model.PaygateAccountResponse, orderNo string) (*model.MazepayGetOrderRemoteResponse, error)
	MazepayCancelDeposit(setting model.PaygateAccountResponse, orderNo string) (*model.MazepayCancelDepositRemoteResponse, error)
	MazepayCreateCustomer(setting model.PaygateAccountResponse, req model.MazepayCustomerCreateRemoteRequest) (*model.MazepayCustomerCreateRemoteResponse, error)
	MazepayUpdateCustomer(setting model.PaygateAccountResponse, req model.MazepayCustomerUpdateRemoteRequest) (*model.MazepayCustomerUpdateRemoteResponse, error)
	// Mazepay-Decrypt
	// MazepayDecryptRepayDespositPayload(setting model.PaygateAccountResponse, payload model.MazepayWebhookEncryptPayload) (*model.MazepayWebhookDepositResponse, error)
	// Mazepay-DB
	CreateMazepayWebhook(body model.MazepayWebhookCreateBody) (*int64, error)
	GetDbMazepayOrderList(req model.MazepayOrderListRequest) ([]model.MazepayOrderResponse, int64, error)
	GetDbMazepayOrderById(id int64) (*model.MazepayOrderResponse, error)
	GetDbMazepayWithdrawOrderByRefId(refId int64) (*model.MazepayOrderResponse, error)
	CreateDbMazepayOrder(body model.MazepayOrderCreateBody) (*int64, error)
	CheckMazepayDepositOrderInLast5Minutes(userId int64) (*model.MazepayOrderResponse, error)
	UpdateDbMazepayOrderError(id int64, remark string) error
	UpdateDbMazepayOrder(id int64, body model.MazepayOrderUpdateBody) error
	ApproveDbMazepayOrder(id int64, webhookStatus string) error
	UpdateMazepayOrderBankSucess(id int64, transId int64, actionBy int64) error
	// Token
	// GetDbMazepayAccessToken() (*model.MazepayToken, error)
	// CreateDbMazepayAccessToken(body model.MazepayTokenCreateBody) (*int64, error)
	// Customer
	GetMazepayCustomerById(id int64) (*model.MazepayCustomerResponse, error)
	GetMazepayCustomerByUserId(userId int64) (*model.MazepayCustomerResponse, error)
	// CheckMazepayCustomerByUserId(user model.UserBankDetailBody) (*model.MazepayCustomerResponse, error)
	GetMazepayCustomerList(req model.MazepayCustomerListRequest) ([]model.MazepayCustomerResponse, int64, error)
	CreateMazepayCustomer(body model.MazepayCustomerCreateBody) (*int64, error)
	UpdateMazepayCustomer(id int64, body model.MazepayCustomerUpdateBody) error
	DeleteMazepayCustomer(id int64) error
	// REF-USER
	GetUserById(id int64) (*model.UserResponse, error)
	// REF-MemberCode
	GetUserForGenMember(id int64) (*model.UserDetail, error)
	GetAgentInfo() (*model.AgentInfo, error)
	IncrementTotal() error
	UpdateMemberCode(userId int64, data model.UserUpdateMemberAndRef) error
	AgcRegister(data model.AgcRegister) error
	AmbRegister(data model.AmbRegister) error
	GetMemberById(id int64) (*model.Member, error)
	IsFirstDeposit(userId int64) bool
	UpdateGenMemberCodeToAffilate(userId int64) error
	CreateAffiliateMember(refBy int64, userId int64) error
	// CronjobDeleteMazepayWebhook() error
	// REF-RC
	GetRaceActionIdByActionKey(actionKey string) (*int64, error)
	CreateRaceCondition(body model.RaceActionCreateBody) (int64, error)
	// REF-BankTransaction-Deposit
	GetUserBankDetailById(id int64) (*model.UserBankDetailBody, error)
	GetDepositCurrentProcessingUserPromotion(userId int64) (*model.PromotionWebUserByUserIdResponse, error)
	InsertBankTransaction(data model.BankTransactionCreateBody) (*int64, error)
	IncreaseUserCredit(body model.UserTransactionCreateRequest) (*model.UserTransactionCreateResponse, error)
	UpdateDepositTransactionStatusFromAgent(transId int64, body model.UserTransactionCreateResponse) error
	UpdateAutoProcessTimer(timer string, id int64) error
	// ConfirmDeposit
	GetBankTransactionById(id int64) (*model.BankTransaction, error)
	CreateTransactionAction(data model.CreateBankTransactionActionBody) (*int64, error)
	ConfirmPendingDepositTransaction(id int64, body model.BankDepositTransactionConfirmBody) error
	RollbackTransactionAction(actionId int64) error
	// Order
	GetPendingMazepayDepositOrder(id int64) (*model.MazepayOrderResponse, error)
	GetLastestMazepayDepositOrderByUserId(userId int64) (*model.MazepayOrderResponse, error)

	// REF-BANK
	GetLastestBankAdminManualDepositTransaction(userId int64) (*model.BankTransaction, error)
	ShowUserTransaction(req model.UserTransactionShowUpdate) error
	UpdateTransactionStatusTransferingToSuccess(id int64, secondUsed string) error
	RollbackTransactionStatusTransferingToConfirmed(id int64) error
	CreateBankTransactionExternalDetail(body model.BankTransactionExternalDetailCreateRequest) (int64, error)
	UpdateUserTransactionStatus(body model.UpdateUserTransactionStatusRequest) error
	GetUserWithdrawCreditTransactionByRefId(transactionId int64, creditAmount float64) (*model.UserTransaction, error)
	// REF-SysLog
	CreateSystemLog(body model.SystemLogCreateBody) (*int64, error)
	// REF-WebConfiguration
	GetWebConfiguration() (*model.GetWebConfigurationBody, error)

	// [TIER]
	IncreaseUserTierDepositAmount(userId int64, amount float64) error
}

func (r repo) GetMazepayTimestamp(now time.Time) string {

	// return now.UnixNano() / int64(time.Second)
	return strconv.FormatInt(now.UnixNano()/int64(time.Second), 10)
}

func (r repo) MazepayCreateSignature(clientId string, secretKey string, xtimestamp string, jsonPayload string, queryString string) string {

	// Combine values to construct a unique string for the signature
	// const combinedString = [xClientId, xTimestamp, requestBodyString, queryString].join('|');
	combinedString := fmt.Sprintf("%s|%s|%s|%s", clientId, xtimestamp, jsonPayload, queryString)

	// // Generate the HMAC SHA-256 signature using the Client Secret
	// const xSignature = createHmac('sha256', xClientSecret)
	// .update(combinedString)
	// .digest('hex');
	// xSignature := helper.HashSHA256(setting.SecretKey, combinedString)

	h := hmac.New(sha256.New, []byte(secretKey))
	h.Write([]byte(combinedString))
	// sign := base64.StdEncoding.EncodeToString(h.Sum(nil))
	// encode to hex string
	sign := fmt.Sprintf("%x", h.Sum(nil))

	return sign
}

func (r repo) MazepayDeposit(setting model.PaygateAccountResponse, req model.MazepayDepositCreateRemoteRequest) (*model.MazepayDepositCreateRemoteResponse, error) {

	// ผมอยากเก็บ default เอาไว้กัน Adminตั้งค่าต่ำกว่า payment
	if req.Amount < model.MAZEPAY_DEFMIN_DEPOSIT_AMOUNT || req.Amount > model.MAZEPAY_DEFMAX_DEPOSIT_AMOUNT {
		log.Println("req.Amount", req.Amount)
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}
	if setting.PaymentDepositMinimum > 0 && req.Amount < setting.PaymentDepositMinimum {
		log.Println("req.Amount", req.Amount, "setting.PaymentDepositMinimum", setting.PaymentDepositMinimum)
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}
	if setting.PaymentDepositMaximum > 0 && req.Amount > setting.PaymentDepositMaximum {
		log.Println("req.Amount", req.Amount, "setting.PaymentDepositMaximum", setting.PaymentDepositMaximum)
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}
	// PrerequisitesMazepay
	if setting.ApiEndPoint == "" || setting.AccessKey == "" || setting.SecretKey == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}

	apiEndPoint := setting.ApiEndPoint
	xtimestamp := r.GetMazepayTimestamp(time.Now().UTC())
	sign := r.MazepayCreateSignature(setting.AccessKey, setting.SecretKey, xtimestamp, helper.StructJson(req), "")
	// fmt.Println("signPayLoad.Json.Signature", sign)

	// 	curl -X 'POST' \
	// 	'https://api.zapman.net/api/v1/client/tx/deposit' \
	// 	-H 'accept: application/json' \
	// 	-H 'x-client-id: fb2778bb4a8b07c9eeb99b4ff8f222fc' \
	// 	-H 'x-signature: 130c2efe105f5a139c34ad7ad7341bf503180bd8de286e27a5092ef137e571ee' \
	// 	-H 'x-timestamp: **********' \
	// 	-H 'Content-Type: application/json' \
	// 	-d '{
	// 	"customer_account_uuid": "string",
	// 	"amount": 123,
	// 	"currency": "thb",
	// 	"payment_method": "qr",
	// 	"callback_url": "string",
	// 	"redirect_url": "string",
	// 	"merchant_order_id": "string"
	//   }'
	epUrl := fmt.Sprintf("%s/v1/client/tx/deposit", apiEndPoint)
	// fmt.Println("MazepayDeposit url ------> ", epUrl)

	// SysLog
	if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
		Name:   "MazepayDeposit",
		Status: "CALL",
		JsonReq: helper.StructJson(map[string]interface{}{
			"epUrl":      epUrl,
			"setting":    setting,
			"req":        req,
			"xtimestamp": xtimestamp,
		}),
	}); err != nil {
		log.Println("MazepayDeposit.CreatePaygateSystemLog.ERROR", err)
	}

	client := &http.Client{}
	client.Timeout = 30 * time.Second

	jsonBody, _ := json.Marshal(req)
	payload := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest("POST", epUrl, payload)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("x-client-id", setting.AccessKey)
	reqExternal.Header.Set("x-signature", sign)
	reqExternal.Header.Set("x-timestamp", xtimestamp)
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}

	// fmt.Println("MazepayDeposit.resp.Body", string(responseData))
	// {
	// 	"code": "ZAP20000",
	// 	"message": "Success",
	// 	"description": "Request processed successfully",
	// 	"data": {
	// 		"uuid": "83e67833-22e4-45ed-a871-af6bb298d8b1",
	// 		"bank_account_number": "**********",
	// 		"bank_account_name": "tester",
	// 		"bank_code": "KBANK",
	// 		"bank_uuid": "59210d13-196e-4669-9e00-cee06772db0a",
	// 		"bank_name_en": "Kasikorn Bank",
	// 		"bank_name_th": "ธนาคารกสิกรไทย",
	// 		"customer_transfer_amount": 133.46,
	// 		"amount": 133.46,
	// 		"customer_request_amount": 133,
	// 		"fee": 1.862,
	// 		"currency": "thb",
	// 		"type": "deposit",
	// 		"transaction_status": "initial",
	// 		"transaction_status_message": "",
	// 		"payment_method": "qr",
	// 		"created_at": "2025-05-05T09:46:08.526Z",
	// 		"updated_at": "2025-05-05T09:46:08.526Z",
	// 		"created_by": "<EMAIL>",
	// 		"updated_by": "<EMAIL>",
	// 		"redirect_url": "",
	// 		"callback_url": "https://dev-api.cbgame88.com/api/webhook/mazepay/dep-callback",
	// 		"merchant_order_id": "565f2d80e2924b5b25055",
	// 		"callback_message": null,
	// 		"callback_at": null,
	// 		"note": "",
	// 		"tag": "",
	// 		"payment_link": "https://zapman.net/payment-link/83e67833-22e4-45ed-a871-af6bb298d8b1",
	// 		"payment_image_base64": "data:image/png;base64,iVBORw0KGgoAAAAg==",
	// 		"bank_account_promptpay_id": "*************",
	// 		"response_callback_status": null,
	// 		"response_callback_message": null
	// 	}
	// }

	// 2025/05/05 16:54:25 422
	// 2025/05/05 16:54:25 {"code":"ZAP42210","message":"Unprocessable Entity","description":"This customer still has a pending deposit transaction.","data":null}

	if response.StatusCode != 200 && response.StatusCode != 201 {
		log.Println("MazepayDeposit.NOT_OK, StatusCode=", response.StatusCode)
		log.Println("MazepayDeposit.NOT_OK, responseData=", string(responseData))
		var errMsg2 model.MazepayErrorRemoteResponse
		errJson2 := json.Unmarshal(responseData, &errMsg2)
		if errJson2 == nil {
			// Pretty ErrorMessage = message + " - " + description if not empty
			if errMsg2.Message == "" {
				errMsg2.Message = errMsg2.Description
			} else if errMsg2.Description != "" {
				errMsg2.Message = errMsg2.Message + " - " + errMsg2.Description
			}
			return nil, fmt.Errorf("[%s] %s", errMsg2.Code, errMsg2.Message)
		} else {
			log.Println("MazepayDeposit.NOT_OK.Unmarshal.errJson2 ------> ", errJson2)
		}
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	}

	var result model.MazepayDepositCreateRemoteResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("MazepayDeposit resp.Body ------> ", string(responseData))
		log.Println("MazepayDeposit.Unmarshal.Err ------> ", errJson)
		// TRY-3-err2
		var errMsg3 model.MazepayErrorRemoteResponse
		errJson3 := json.Unmarshal(responseData, &errMsg3)
		if errJson3 == nil {
			return nil, errors.New(errMsg3.Message)
		} else {
			log.Println("MazepayDeposit.Unmarshal.errJson3 ------> ", errJson3)
		}
		return nil, errors.New("INVALID_UNMARSHAL_JSON")
	}

	// fmt.Println("MazepayDepositCreateRemoteResponse.result", result)
	// 2025/05/28 23:01:41 {"code":"42205","message":"Unprocessable Entity","description":"No deposit bank channel is available.","data":null}

	if result.Code != "ZAP20000" && result.Code != "20000" {
		log.Println("MazepayDepositCreateRemoteResponse.result", helper.StructJson(result))
		// SysLog
		if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "MazepayDeposit",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(req),
			JsonResponse: helper.StructJson(map[string]interface{}{"resp": string(responseData), "result": helper.StructJson(result)}),
		}); err != nil {
			log.Println("MazepayDeposit.CreatePaygateSystemLog", err)
		}
		// Pretty ErrorMessage = message + " - " + description if not empty
		if result.Message == "" {
			result.Message = result.Description
		} else if result.Description != "" {
			result.Message = result.Message + " - " + result.Description
		}
		return nil, errors.New(result.Message)
	}
	return &result, nil
}

func (r repo) MazepayGetOrder(setting model.PaygateAccountResponse, orderNo string) (*model.MazepayGetOrderRemoteResponse, error) {

	// PrerequisitesMazepay
	if setting.ApiEndPoint == "" || setting.AccessKey == "" || setting.SecretKey == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}

	apiEndPoint := setting.ApiEndPoint
	xtimestamp := r.GetMazepayTimestamp(time.Now().UTC())
	// path := orderNo
	sign := r.MazepayCreateSignature(setting.AccessKey, setting.SecretKey, xtimestamp, "{}", "")
	// fmt.Println("signPayLoad.Json.Signature", sign)

	// curl -X 'GET' \
	// 'https://api.zapman.net/api/v1/client/tx/check-status/2838d3b9-bb83-49d2-b5c9-7b33462e4394' \
	// -H 'accept: */*' \
	// -H 'x-client-id: x-client-id' \
	// -H 'x-signature: x-signature' \
	// -H 'x-timestamp: x-timestamp'
	epUrl := fmt.Sprintf("%s/v1/client/tx/check-status/%s", apiEndPoint, orderNo)
	// fmt.Println("MazepayGetOrder url ------> ", epUrl)

	// SysLog
	if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
		Name:   "MazepayGetOrder",
		Status: "CALL",
		JsonReq: helper.StructJson(map[string]interface{}{
			"epUrl":      epUrl,
			"setting":    setting,
			"orderNo":    orderNo,
			"xtimestamp": xtimestamp,
		}),
	}); err != nil {
		log.Println("MazepayGetOrder.CreatePaygateSystemLog.ERROR", err)
	}

	client := &http.Client{}
	client.Timeout = 30 * time.Second

	reqExternal, _ := http.NewRequest("GET", epUrl, nil)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("x-client-id", setting.AccessKey)
	reqExternal.Header.Set("x-signature", sign)
	reqExternal.Header.Set("x-timestamp", xtimestamp)
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}

	// fmt.Println("MazepayGetOrder.resp.Body", string(responseData))
	// MazepayGetOrder.resp.Body {
	// 	"code": "ZAP20000",
	// 	"message": "Success",
	// 	"description": "Request processed successfully",
	// 	"data": {
	// 		"status": "expired",
	// 		"amount": 156.72,
	// 		"bank_account_promptpay_id": "*************",
	// 		"redirect_url": "https://dev-web.cbgame88.com/deposit"
	// 	}
	// }
	// MazepayGetOrder.resp.Body {
	// 	"code": "ZAP20000",
	// 	"message": "Success",
	// 	"description": "Request processed successfully",
	// 	"data": {
	// 		"status": "initial",
	// 		"amount": 157.57,
	// 		"bank_account_promptpay_id": "*************",
	// 		"redirect_url": "https://dev-web.cbgame88.com/deposit"
	// 	}
	// }
	if response.StatusCode != 200 && response.StatusCode != 201 {
		log.Println("MazepayGetOrder.NOT_OK, StatusCode=", response.StatusCode)
		log.Println("MazepayGetOrder.NOT_OK, responseData=", string(responseData))
		var errMsg2 model.MazepayErrorRemoteResponse
		errJson2 := json.Unmarshal(responseData, &errMsg2)
		if errJson2 == nil {
			// Pretty ErrorMessage = message + " - " + description if not empty
			if errMsg2.Message == "" {
				errMsg2.Message = errMsg2.Description
			} else if errMsg2.Description != "" {
				errMsg2.Message = errMsg2.Message + " - " + errMsg2.Description
			}
			return nil, fmt.Errorf("[%s] %s", errMsg2.Code, errMsg2.Message)
		} else {
			log.Println("Unmarshal.errJson2 ------> ", errJson2)
		}
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	}

	var result model.MazepayGetOrderRemoteResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("MazepayGetOrder resp.Body ------> ", string(responseData))
		log.Println("Unmarshal.Err ------> ", errJson)
		// TRY-3-err2
		var errMsg3 model.MazepayErrorRemoteResponse
		errJson3 := json.Unmarshal(responseData, &errMsg3)
		if errJson3 == nil {
			return nil, errors.New(errMsg3.Message)
		} else {
			log.Println("Unmarshal.errJson3 ------> ", errJson3)
		}
		return nil, errors.New("INVALID_UNMARSHAL_JSON")
	}

	// fmt.Println("MazepayGetOrderCreateRemoteResponse.result", result)

	if result.Code != "ZAP20000" && result.Code != "20000" {
		log.Println("MazepayGetOrderCreateRemoteResponse.result", helper.StructJson(result))
		// SysLog
		if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "MazepayGetOrder",
			Status:       "ERROR",
			JsonReq:      orderNo,
			JsonResponse: helper.StructJson(map[string]interface{}{"resp": string(responseData), "result": helper.StructJson(result)}),
		}); err != nil {
			log.Println("MazepayGetOrder.CreatePaygateSystemLog", err)
		}
		// Pretty ErrorMessage = message + " - " + description if not empty
		if result.Message == "" {
			result.Message = result.Description
		} else if result.Description != "" {
			result.Message = result.Message + " - " + result.Description
		}
		return nil, errors.New(result.Message)
	}
	return &result, nil
}

func (r repo) MazepayCancelDeposit(setting model.PaygateAccountResponse, orderNo string) (*model.MazepayCancelDepositRemoteResponse, error) {

	// PrerequisitesMazepay
	if setting.ApiEndPoint == "" || setting.AccessKey == "" || setting.SecretKey == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}

	apiEndPoint := setting.ApiEndPoint
	xtimestamp := r.GetMazepayTimestamp(time.Now().UTC())
	sign := r.MazepayCreateSignature(setting.AccessKey, setting.SecretKey, xtimestamp, "{}", "")
	// fmt.Println("signPayLoad.Json.Signature", sign)

	// curl -X 'GET' \
	// 'https://api.zapman.net/api/v1/client/tx/cancel-deposit/transaction_uuid%20' \
	// -H 'accept: */*' \
	// -H 'x-client-id: x-client-id' \
	// -H 'x-signature: x-signature' \
	// -H 'x-timestamp: x-timestamp'
	epUrl := fmt.Sprintf("%s/v1/client/tx/cancel-deposit/%s", apiEndPoint, orderNo)
	// fmt.Println("MazepayCancelDeposit url ------> ", epUrl)

	// SysLog
	if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
		Name:   "MazepayCancelDeposit",
		Status: "CALL",
		JsonReq: helper.StructJson(map[string]interface{}{
			"epUrl":      epUrl,
			"setting":    setting,
			"orderNo":    orderNo,
			"xtimestamp": xtimestamp,
		}),
	}); err != nil {
		log.Println("MazepayCancelDeposit.CreatePaygateSystemLog.ERROR", err)
	}

	client := &http.Client{}
	client.Timeout = 30 * time.Second

	reqExternal, _ := http.NewRequest("GET", epUrl, nil)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("x-client-id", setting.AccessKey)
	reqExternal.Header.Set("x-signature", sign)
	reqExternal.Header.Set("x-timestamp", xtimestamp)
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}

	// fmt.Println("MazepayCancelDeposit.resp.Body", string(responseData))
	// MazepayCancelDeposit.resp.Body {
	// 	"code": "ZAP20000",
	// 	"message": "Success",
	// 	"description": "Request processed successfully",
	// 	"data": {
	// 		"uuid": "21a5766a-85eb-4951-b6f1-8750098fb069",
	// 		"bank_account_number": "**********",
	// 		"bank_account_name": "TESTER",
	// 		"bank_code": "KBANK",
	// 		"bank_uuid": "59210d13-196e-4669-9e00-cee06772db0a",
	// 		"bank_name_en": "Kasikorn Bank",
	// 		"bank_name_th": "ธนาคารกสิกรไทย",
	// 		"customer_transfer_amount": 157.57,
	// 		"amount": 157.57,
	// 		"customer_request_amount": 157,
	// 		"fee": 2.198,
	// 		"currency": "thb",
	// 		"type": "deposit",
	// 		"transaction_status": "deposit_canceled",
	// 		"transaction_status_message": "",
	// 		"payment_method": "qr",
	// 		"created_at": "2025-05-05T12: 35: 19.446Z",
	// 		"updated_at": "2025-05-05T12: 36: 48.668Z",
	// 		"created_by": "<EMAIL>",
	// 		"updated_by": "<EMAIL>",
	// 		"redirect_url": "https: //dev-web.cbgame88.com/deposit",
	// 		"callback_url": "https://dev-api.cbgame88.com/api/webhook/mazepay/dep-callback",
	// 		"merchant_order_id": "40d772e1ff9f4451250519",
	// 		"callback_message": null,
	// 		"callback_at": null,
	// 		"note": "",
	// 		"tag": "",
	// 		"response_callback_status": null,
	// 		"response_callback_message": null
	// 	}
	// }
	if response.StatusCode != 200 && response.StatusCode != 201 {
		log.Println("MazepayGetOrder.NOT_OK, StatusCode=", response.StatusCode)
		log.Println("MazepayGetOrder.NOT_OK, responseData=", string(responseData))
		var errMsg2 model.MazepayErrorRemoteResponse
		errJson2 := json.Unmarshal(responseData, &errMsg2)
		if errJson2 == nil {
			// Pretty ErrorMessage = message + " - " + description if not empty
			if errMsg2.Message == "" {
				errMsg2.Message = errMsg2.Description
			} else if errMsg2.Description != "" {
				errMsg2.Message = errMsg2.Message + " - " + errMsg2.Description
			}
			return nil, fmt.Errorf("[%s] %s", errMsg2.Code, errMsg2.Message)
		} else {
			log.Println("Unmarshal.errJson2 ------> ", errJson2)
		}
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	}

	var result model.MazepayCancelDepositRemoteResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("MazepayCancelDeposit resp.Body ------> ", string(responseData))
		log.Println("Unmarshal.Err ------> ", errJson)
		// TRY-3-err2
		var errMsg3 model.MazepayErrorRemoteResponse
		errJson3 := json.Unmarshal(responseData, &errMsg3)
		if errJson3 == nil {
			return nil, errors.New(errMsg3.Message)
		} else {
			log.Println("Unmarshal.errJson3 ------> ", errJson3)
		}
		return nil, errors.New("INVALID_UNMARSHAL_JSON")
	}

	// fmt.Println("MazepayCancelDepositRemoteResponse.result", result)

	if result.Code != "ZAP20000" && result.Code != "20000" {
		log.Println("MazepayCancelDepositRemoteResponse.result", helper.StructJson(result))
		// SysLog
		if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "MazepayCancelDeposit",
			Status:       "ERROR",
			JsonReq:      orderNo,
			JsonResponse: helper.StructJson(map[string]interface{}{"resp": string(responseData), "result": helper.StructJson(result)}),
		}); err != nil {
			log.Println("MazepayCancelDeposit.CreatePaygateSystemLog", err)
		}
		// Pretty ErrorMessage = message + " - " + description if not empty
		if result.Message == "" {
			result.Message = result.Description
		} else if result.Description != "" {
			result.Message = result.Message + " - " + result.Description
		}
		return nil, errors.New(result.Message)
	}
	return &result, nil
}

func (r repo) MazepayCreateCustomer(setting model.PaygateAccountResponse, req model.MazepayCustomerCreateRemoteRequest) (*model.MazepayCustomerCreateRemoteResponse, error) {

	apiEndPoint := setting.ApiEndPoint
	xtimestamp := r.GetMazepayTimestamp(time.Now().UTC())
	// 	curl -X 'POST' \
	// 	'https://api.zapman.net/api/v1/client/customer' \
	// 	-H 'accept: application/json' \
	// 	-H 'x-client-id: x-client-id' \
	// 	-H 'x-signature: x-signature' \
	// 	-H 'x-timestamp: x-timestamp' \
	// 	-H 'Content-Type: application/json' \
	// 	-d '{
	// 	"bank_uuid": "********-0000-0000-0000-**********00",
	// 	"bank_account_number": "**********",
	// 	"bank_account_name": "สมชาย สบายดี",
	// 	"bank_account_name_en": "Somchai Sabuydee",
	// 	"status": "active",
	// 	"ref_code": "refCode"
	//   }'
	epUrl := fmt.Sprintf("%s/v1/client/customer", apiEndPoint)
	// fmt.Println("MazepayCreateCustomer url ------> ", epUrl)
	sign := r.MazepayCreateSignature(setting.AccessKey, setting.SecretKey, xtimestamp, helper.StructJson(req), "")
	// fmt.Println("signPayLoad.Json.Signature", sign)

	// SysLog
	if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
		Name:   "MazepayCreateCustomer",
		Status: "CALL",
		JsonReq: helper.StructJson(map[string]interface{}{
			"epUrl":      epUrl,
			"setting":    setting,
			"req":        req,
			"xtimestamp": xtimestamp,
		}),
	}); err != nil {
		log.Println("MazepayCreateCustomer.CreatePaygateSystemLog.ERROR", err)
	}

	client := &http.Client{}
	client.Timeout = 30 * time.Second

	jsonBody, _ := json.Marshal(req)
	payload := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest("POST", epUrl, payload)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("x-client-id", setting.AccessKey)
	reqExternal.Header.Set("x-signature", sign)
	reqExternal.Header.Set("x-timestamp", xtimestamp)
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}

	// fmt.Println("MazepayCreateCustomer.resp.Body", string(responseData))
	// MazepayCreateCustomer.resp.Body {"code":"ZAP20000","message":"Success","description":"Request processed successfully","data":{"uuid":"4f9c3532-6b47-4432-af07-993e2d8fb84e",
	// "bank_account_number":"*********","bank_account_name":"paymenttester","bank_account_name_en":"","status":"active","created_at":"2025-05-05T08:44:26.207Z","ref_code":""}}

	if response.StatusCode != 200 && response.StatusCode != 201 {
		log.Println("MazepayCreateCustomer.NOT_OK, StatusCode=", response.StatusCode)
		log.Println("MazepayCreateCustomer.NOT_OK, responseData=", string(responseData))
		var errMsg2 model.MazepayErrorRemoteResponse
		errJson2 := json.Unmarshal(responseData, &errMsg2)
		if errJson2 == nil {
			// Pretty ErrorMessage = message + " - " + description if not empty
			if errMsg2.Message == "" {
				errMsg2.Message = errMsg2.Description
			} else if errMsg2.Description != "" {
				errMsg2.Message = errMsg2.Message + " - " + errMsg2.Description
			}
			return nil, fmt.Errorf("[%s] %s", errMsg2.Code, errMsg2.Message)
		} else {
			log.Println("MazepayCreateCustomer.Unmarshal.errJson2 ------> ", errJson2)
		}
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	}

	var result model.MazepayCustomerCreateRemoteResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("MazepayCreateCustomer resp.Body ------> ", string(responseData))
		log.Println("MazepayCreateCustomer.Unmarshal.Err ------> ", errJson)
		var errMsg1 model.MazepayErrorRemoteResponse
		errJson2 := json.Unmarshal(responseData, &errMsg1)
		if errJson2 != nil {
			log.Println("MazepayCreateCustomer resp.Body ------> ", string(responseData))
			log.Println("MazepayCreateCustomer.Unmarshal.Err ------> ", errJson)
		} else {
			return nil, errors.New(errMsg1.Message)
		}
		return nil, errors.New("INVALID_UNMARSHAL_JSON")
	}

	// fmt.Println("MazepayCreateCustomerRemoteResponse.result", result)
	// MazepayCreateCustomerRemoteResponse.result {ZAP20000 Success Request processed successfully {030c2582-5cb4-4364-999e-05dad8a6124e ********* paymenttester  active 2025-05-05T08:48:18.419Z }}

	return &result, nil
}

func (r repo) MazepayUpdateCustomer(setting model.PaygateAccountResponse, req model.MazepayCustomerUpdateRemoteRequest) (*model.MazepayCustomerUpdateRemoteResponse, error) {

	apiEndPoint := setting.ApiEndPoint
	xtimestamp := r.GetMazepayTimestamp(time.Now().UTC())
	// path := req.CustomerUuid
	jsonReq := make(map[string]interface{})
	jsonReq["bank_uuid"] = req.BankUuid
	jsonReq["bank_account_number"] = req.BankAccountNumber
	jsonReq["bank_account_name"] = req.BankAccountName
	jsonReq["bank_account_name_en"] = req.BankAccountNameEn
	jsonReq["ref_code"] = req.RefCode
	jsonReq["status"] = req.Status
	// fmt.Println("MazepayUpdateCustomer.path", path)
	// fmt.Println("MazepayUpdateCustomer.jsonReq", helper.StructJson(jsonReq))
	sign := r.MazepayCreateSignature(setting.AccessKey, setting.SecretKey, xtimestamp, helper.StructJson(jsonReq), "")
	// fmt.Println("signPayLoad.Json.Signature", sign)
	// fmt.Println("signPayLoad.Json.xtimestamp", xtimestamp)

	// 	curl -X 'PATCH' \
	// 	'https://api.zapman.net/api/v1/client/customer/uuid' \
	// 	-H 'accept: application/json' \
	// 	-H 'x-client-id: x-client-id' \
	// 	-H 'x-signature: x-signature' \
	// 	-H 'x-timestamp: x-timestamp' \
	// 	-H 'Content-Type: application/json' \
	// 	-d '{
	// 	"bank_uuid": "string",
	// 	"bank_account_number": "string",
	// 	"bank_account_name": "string",
	// 	"bank_account_name_en": "string",
	// 	"ref_code": "string",
	// 	"status": "active"
	//   }'
	epUrl := fmt.Sprintf("%s/v1/client/customer/%s", apiEndPoint, req.CustomerUuid)
	// fmt.Println("MazepayUpdateCustomer url ------> ", epUrl)

	// SysLog
	if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
		Name:   "MazepayUpdateCustomer",
		Status: "CALL",
		JsonReq: helper.StructJson(map[string]interface{}{
			"epUrl":   epUrl,
			"setting": setting,
			"req":     req,
		}),
	}); err != nil {
		log.Println("MazepayUpdateCustomer.CreatePaygateSystemLog.ERROR", err)
	}

	client := &http.Client{}
	client.Timeout = 30 * time.Second

	jsonBody, _ := json.Marshal(jsonReq)
	payload := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest("PATCH", epUrl, payload)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("x-client-id", setting.AccessKey)
	reqExternal.Header.Set("x-signature", sign)
	reqExternal.Header.Set("x-timestamp", xtimestamp)
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}

	// fmt.Println("MazepayUpdateCustomer.resp.Body", string(responseData))

	if response.StatusCode != 200 && response.StatusCode != 201 {
		log.Println("MazepayUpdateCustomer.NOT_OK, StatusCode=", response.StatusCode)
		log.Println("MazepayUpdateCustomer.NOT_OK, responseData=", string(responseData))
		var errMsg2 model.MazepayErrorRemoteResponse
		errJson2 := json.Unmarshal(responseData, &errMsg2)
		if errJson2 == nil {
			// Pretty ErrorMessage = message + " - " + description if not empty
			if errMsg2.Message == "" {
				errMsg2.Message = errMsg2.Description
			} else if errMsg2.Description != "" {
				errMsg2.Message = errMsg2.Message + " - " + errMsg2.Description
			}
			return nil, fmt.Errorf("[%s] %s", errMsg2.Code, errMsg2.Message)
		} else {
			log.Println("MazepayUpdateCustomer.Unmarshal.errJson2 ------> ", errJson2)
		}
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	}

	var result model.MazepayCustomerUpdateRemoteResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("MazepayUpdateCustomer resp.Body ------> ", string(responseData))
		log.Println("Unmarshal.Err ------> ", errJson)
		var errMsg1 model.MazepayErrorRemoteResponse
		errJson2 := json.Unmarshal(responseData, &errMsg1)
		if errJson2 != nil {
			log.Println("MazepayUpdateCustomer resp.Body ------> ", string(responseData))
			log.Println("Unmarshal.Err ------> ", errJson)
		} else {
			return nil, errors.New(errMsg1.Message)
		}
		return nil, errors.New("INVALID_UNMARSHAL_JSON")
	}

	// fmt.Println("MazepayUpdateCustomerRemoteResponse.result", helper.StructJson(result))
	// {
	// 	"code": "ZAP20000",
	// 	"message": "Success",
	// 	"description": "Request processed successfully",
	// 	"data": {
	// 		"uuid": "030c2582-5cb4-4364-999e-05dad8a6124e",
	// 		"bank": {
	// 			"uuid": "59210d13-196e-4669-9e00-cee06772db0a",
	// 			"code": "KBANK",
	// 			"name_en": "Kasikorn Bank",
	// 			"name_th": "ธนาคารกสิกรไทย""},"bank_account_number":"**********","bank_account_name":"TESTER","bank_account_name_en":"","status":"active","created_at":"2025-05-05T08: 48: 18.419Z","ref_code":""}}
	// MazepayUpdateCustomerRemoteResponse.result {
	// 				"code": "ZAP20000",
	// 				"message": "Success",
	// 				"description": "Request processed successfully",
	// 				"data": {
	// 					"uuid": "030c2582-5cb4-4364-999e-05dad8a6124e",
	// 					"bank_account_number": "**********",
	// 					"bank_account_name": "TESTER",
	// 					"bank_account_name_en": "",
	// 					"status": "active",
	// 					"created_at": "2025-05-05T08:48:18.419Z",
	// 					"ref_code": ""
	// 				}
	// 			}
	return &result, nil
}

func (r repo) MazepayCheckBalance(setting model.PaygateAccountResponse) (*model.MazepayCheckBalanceRemoteResponse, error) {

	// PrerequisitesMazepay
	if setting.ApiEndPoint == "" || setting.AccessKey == "" || setting.SecretKey == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}

	// curl -X 'GET' \
	// 'https://api.zapman.net/api/v1/client/merchant' \
	// -H 'accept: */*' \
	// -H 'x-client-id: x-client-id' \
	// -H 'x-signature: x-signature' \
	// -H 'x-timestamp: x-timestamp'
	apiEndPoint := setting.ApiEndPoint
	xtimestamp := r.GetMazepayTimestamp(time.Now().UTC())
	sign := r.MazepayCreateSignature(setting.AccessKey, setting.SecretKey, xtimestamp, "{}", "")

	// POST Endpoint: https:// {{API_ENDPOINT}}/v1/deposit/qrcode
	epUrl := fmt.Sprintf("%s/v1/client/merchant", apiEndPoint)
	// fmt.Println("MazepayCheckBalance url ------> ", epUrl)

	// SysLog
	if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
		Name:   "MazepayCheckBalance",
		Status: "CALL",
		JsonReq: helper.StructJson(map[string]interface{}{
			"epUrl":   epUrl,
			"setting": setting,
		}),
	}); err != nil {
		log.Println("MazepayCheckBalance.CreatePaygateSystemLog.ERROR", err)
	}

	client := &http.Client{}
	client.Timeout = 30 * time.Second

	reqExternal, _ := http.NewRequest("GET", epUrl, nil)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("x-client-id", setting.AccessKey)
	reqExternal.Header.Set("x-signature", sign)
	reqExternal.Header.Set("x-timestamp", xtimestamp)
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}
	// fmt.Println("MazepayCheckBalance.resp.Body", string(responseData))
	// MazepayCheckBalance.resp.Body {
	// 	"code": "ZAP20000",
	// 	"message": "Success",
	// 	"description": "Request processed successfully",
	// 	"data": {
	// 		"uuid": "asdbasd12-1234-4acd-8bfd-xxxxxxxxxxxxx",
	// 		"code": "rv",
	// 		"name": "RV",
	// 		"contact_firstname": "-",
	// 		"contact_lastname": "-",
	// 		"email": "<EMAIL>",
	// 		"phone_number": "-",
	// 		"currency": "thb",
	// 		"exchange_rate": 1,
	// 		"exchange_rate_type": "fix",
	// 		"minimum_deposit": 100,
	// 		"maximum_withdraw": 2000000,
	// 		"daily_settlement_amount": 2000000,
	// 		"withdraw_status": "active",
	// 		"deposit_status": "active",
	// 		"client_id": "fb2778bxxxxxxxxxxxxxxxxxxx",
	// 		"status": "active",
	// 		"created_at": "2025-04-27T18:34:45.532Z",
	// 		"credit": {
	// 			"incoming": 0,
	// 			"outgoing": 0,
	// 			"total": 0,
	// 			"available": 0
	// 		},
	// 		"enabled_whitelist": false
	// 	}
	// }

	if response.StatusCode != 200 && response.StatusCode != 201 {
		log.Println(response.StatusCode)
		log.Println(string(responseData))
		var errMsg3 model.MazepayErrorRemoteResponse
		errJson3 := json.Unmarshal(responseData, &errMsg3)
		if errJson3 == nil {
			return nil, errors.New(errMsg3.Message)
		} else {
			log.Println("MazepayCheckBalance.Unmarshal.errJson3 ------> ", errJson3)
		}
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	}

	// ERROR CODE LIST

	var result model.MazepayCheckBalanceRemoteResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("MazepayCheckBalance resp.Body ------> ", string(responseData))
		log.Println("MazepayCheckBalance.Unmarshal.Err ------> ", errJson)
		var errMsg3 model.MazepayErrorRemoteResponse
		errJson3 := json.Unmarshal(responseData, &errMsg3)
		if errJson3 == nil {
			return nil, errors.New(errMsg3.Message)
		} else {
			log.Println("Unmarshal.errJson3 ------> ", errJson3)
		}
		return nil, errors.New("INVALID_UNMARSHAL_JSON")
	}

	// fmt.Println("MazepayCheckBalanceCreateRemoteResponse.result", result)

	if result.Code != "ZAP20000" && result.Code != "20000" {
		log.Println("MazepayCheckBalanceCreateRemoteResponse.result", helper.StructJson(result))
		// SysLog
		if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "MazepayCheckBalance.Result",
			Status:       "ERROR",
			JsonReq:      "{}",
			JsonResponse: helper.StructJson(map[string]interface{}{"resp": string(responseData), "result": helper.StructJson(result)}),
		}); err != nil {
			log.Println("MazepayCheckBalance.Result.CreatePaygateSystemLog.ERROR", err)
		}
		// Pretty ErrorMessage = message + " - " + description if not empty
		if result.Message == "" {
			result.Message = result.Description
		} else if result.Description != "" {
			result.Message = result.Message + " - " + result.Description
		}
		return nil, errors.New(result.Message)
	}
	return &result, nil
}

func (r repo) MazepayWithdraw(setting model.PaygateAccountResponse, req model.MazepayWithdrawCreateRemoteRequest) (*model.MazepayWithdrawCreateRemoteResponse, error) {

	// PrerequisitesMazepay
	if setting.ApiEndPoint == "" || setting.AccessKey == "" || setting.SecretKey == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}

	// Will Check Balance before Withdraw
	balance, err := r.MazepayCheckBalance(setting)
	if err != nil {
		return nil, errors.New("PAYGATE_CANT_CHECK_BALANCE")
	}
	if balance.Data.Credit.Available < req.Amount {
		log.Println("MazepayWithdraw balance.Data.Credit", helper.StructJson(balance.Data.Credit))
		return nil, errors.New("PAYGATE_INSUFFICIENT_BALANCE")
	}

	apiEndPoint := setting.ApiEndPoint
	xtimestamp := r.GetMazepayTimestamp(time.Now().UTC())
	sign := r.MazepayCreateSignature(setting.AccessKey, setting.SecretKey, xtimestamp, helper.StructJson(req), "")

	// 	curl -X 'POST' \
	// 	'https://api.zapman.net/api/v1/client/tx/withdraw' \
	// 	-H 'accept: application/json' \
	// 	-H 'x-client-id: x-client-id' \
	// 	-H 'x-signature: x-signature' \
	// 	-H 'x-timestamp: x-timestamp' \
	// 	-H 'Content-Type: application/json' \
	// 	-d '{
	// 	"customer_account_uuid": "string",
	// 	"amount": 0,
	// 	"currency": "thb",
	// 	"callback_url": "string",
	// 	"merchant_order_id": "string"
	//   }'
	epUrl := fmt.Sprintf("%s/v1/client/tx/withdraw", apiEndPoint)
	// fmt.Println("MazepayWithdraw url ------> ", epUrl)

	// SysLog
	if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
		Name:   "MazepayWithdraw",
		Status: "CALL",
		JsonReq: helper.StructJson(map[string]interface{}{
			"epUrl":   epUrl,
			"setting": setting,
			"req":     req,
		}),
	}); err != nil {
		log.Println("MazepayWithdraw.CreatePaygateSystemLog.ERROR", err)
	}

	client := &http.Client{}
	client.Timeout = 30 * time.Second

	jsonBody, _ := json.Marshal(req)
	payload := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest("POST", epUrl, payload)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("x-client-id", setting.AccessKey)
	reqExternal.Header.Set("x-signature", sign)
	reqExternal.Header.Set("x-timestamp", xtimestamp)
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}
	// fmt.Println("MazepayWithdraw.resp.Body", string(responseData))

	if response.StatusCode != 200 && response.StatusCode != 201 {
		log.Println(response.StatusCode)
		log.Println(string(responseData))
		var errMsg3 model.MazepayErrorRemoteResponse
		errJson3 := json.Unmarshal(responseData, &errMsg3)
		if errJson3 == nil {
			return nil, errors.New(errMsg3.Message)
		} else {
			log.Println("MazepayWithdraw.Unmarshal.errJson3 ------> ", errJson3)
		}
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	}

	// ERROR CODE LIST

	var result model.MazepayWithdrawCreateRemoteResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("MazepayWithdraw resp.Body ------> ", string(responseData))
		log.Println("MazepayWithdraw.Unmarshal.Err ------> ", errJson)
		var errMsg3 model.MazepayErrorRemoteResponse
		errJson3 := json.Unmarshal(responseData, &errMsg3)
		if errJson3 == nil {
			return nil, errors.New(errMsg3.Message)
		} else {
			log.Println("Unmarshal.errJson3 ------> ", errJson3)
		}
		return nil, errors.New("INVALID_UNMARSHAL_JSON")
	}

	// fmt.Println("MazepayWithdrawCreateRemoteResponse.result", result)

	if result.Code != "ZAP20000" && result.Code != "20000" {
		log.Println("MazepayWithdrawCreateRemoteResponse.result", helper.StructJson(result))
		// SysLog
		if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "MazepayWithdraw.Result",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(req),
			JsonResponse: helper.StructJson(map[string]interface{}{"resp": string(responseData), "result": helper.StructJson(result)}),
		}); err != nil {
			log.Println("MazepayWithdraw.Result.CreatePaygateSystemLog.ERROR", err)
		}
		// Pretty ErrorMessage = message + " - " + description if not empty
		if result.Message == "" {
			result.Message = result.Description
		} else if result.Description != "" {
			result.Message = result.Message + " - " + result.Description
		}
		return nil, errors.New(result.Message)
	}
	return &result, nil
}

func (r repo) CreateMazepayWebhook(body model.MazepayWebhookCreateBody) (*int64, error) {

	if err := r.db.Table("paygate_mazepay_webhook").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) GetDbMazepayOrderList(req model.MazepayOrderListRequest) ([]model.MazepayOrderResponse, int64, error) {

	var list []model.MazepayOrderResponse
	var total int64

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("paygate_mazepay_order as tb_order")
	count = count.Select("tb_order.id")
	if req.UserId != nil {
		count = count.Where("tb_order.user_id = ?", req.UserId)
	}
	if req.OrderTypeId != nil {
		count = count.Where("tb_order.order_type_id = ?", req.OrderTypeId)
	}
	if req.OrderNo != "" {
		count = count.Where("tb_order.order_no = ?", req.OrderNo)
	}
	if req.TransactionNo != "" {
		count = count.Where("tb_order.transaction_no = ?", req.TransactionNo)
	}
	if req.Amount != "" {
		count = count.Where("tb_order.amount = ?", req.Amount)
	}
	if req.Status != "" {
		count = count.Where("tb_order.transaction_status = ?", req.Status)
	}

	if err := count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount, tb_order.transfer_amount AS transfer_amount"
		selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
		selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.qr_base64 AS qr_base64, tb_order.extra_promptpay_id AS extra_promptpay_id, tb_order.payment_page_url AS payment_page_url"
		selectedFields += ", tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
		selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

		query := r.db.Table("paygate_mazepay_order as tb_order")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN paygate_order_type AS tb_type ON tb_order.order_type_id = tb_type.id")
		if req.UserId != nil {
			query = query.Where("tb_order.user_id = ?", req.UserId)
		}
		if req.OrderTypeId != nil {
			query = query.Where("tb_order.order_type_id = ?", req.OrderTypeId)
		}
		if req.OrderNo != "" {
			query = query.Where("tb_order.order_no = ?", req.OrderNo)
		}
		if req.TransactionNo != "" {
			query = query.Where("tb_order.transaction_no = ?", req.TransactionNo)
		}
		if req.Amount != "" {
			query = query.Where("tb_order.amount = ?", req.Amount)
		}
		if req.Status != "" {
			query = query.Where("tb_order.transaction_status = ?", req.Status)
		}

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetDbMazepayOrderById(id int64) (*model.MazepayOrderResponse, error) {

	var record model.MazepayOrderResponse

	selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount, tb_order.transfer_amount AS transfer_amount"
	selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
	selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.qr_base64 AS qr_base64, tb_order.extra_promptpay_id AS extra_promptpay_id, tb_order.payment_page_url AS payment_page_url"
	selectedFields += ", tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
	selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

	if err := r.db.Table("paygate_mazepay_order as tb_order").
		Select(selectedFields).
		Joins("LEFT JOIN paygate_order_type AS tb_type ON tb_order.order_type_id = tb_type.id").
		Where("tb_order.id = ?", id).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetPendingMazepayDepositOrder(id int64) (*model.MazepayOrderResponse, error) {

	var record model.MazepayOrderResponse

	selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount, tb_order.transfer_amount AS transfer_amount"
	selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
	selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.qr_base64 AS qr_base64, tb_order.extra_promptpay_id AS extra_promptpay_id, tb_order.payment_page_url AS payment_page_url"
	selectedFields += ", tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
	selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

	if err := r.db.Table("paygate_mazepay_order as tb_order").
		Select(selectedFields).
		Joins("LEFT JOIN paygate_order_type AS tb_type ON tb_order.order_type_id = tb_type.id").
		Where("tb_order.id = ?", id).
		Where("tb_order.transaction_status = ?", "WAIT_PAYMENT").
		Order("tb_order.id DESC").
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetLastestMazepayDepositOrderByUserId(userId int64) (*model.MazepayOrderResponse, error) {

	var record model.MazepayOrderResponse

	selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount, tb_order.transfer_amount AS transfer_amount"
	selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
	selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.qr_base64 AS qr_base64, tb_order.extra_promptpay_id AS extra_promptpay_id, tb_order.payment_page_url AS payment_page_url"
	selectedFields += ", tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
	selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

	if err := r.db.Table("paygate_mazepay_order as tb_order").
		Select(selectedFields).
		Joins("LEFT JOIN paygate_order_type AS tb_type ON tb_order.order_type_id = tb_type.id").
		Where("tb_order.order_type_id = ?", model.PAYGATE_ORDER_TYPE_DEPOSIT).
		Where("tb_order.user_id = ?", userId).
		// Where("tb_order.transaction_status = ?", "WAIT_PAYMENT").
		Order("tb_order.id DESC").
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetDbMazepayWithdrawOrderByRefId(refId int64) (*model.MazepayOrderResponse, error) {

	var record model.MazepayOrderResponse

	selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount, tb_order.transfer_amount AS transfer_amount"
	selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
	selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.qr_base64 AS qr_base64, tb_order.extra_promptpay_id AS extra_promptpay_id, tb_order.payment_page_url AS payment_page_url"
	selectedFields += ", tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
	selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

	if err := r.db.Table("paygate_mazepay_order as tb_order").
		Select(selectedFields).
		Joins("LEFT JOIN paygate_order_type AS tb_type ON tb_order.order_type_id = tb_type.id").
		Where("tb_order.ref_id = ?", refId).
		Where("tb_order.order_type_id = ?", model.PAYGATE_ORDER_TYPE_WITHDRAW).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) CreateDbMazepayOrder(body model.MazepayOrderCreateBody) (*int64, error) {

	if err := r.db.Table("paygate_mazepay_order").Create(&body).Error; err != nil {
		return nil, err
	}

	// Use Random UUID as OrderNo //
	uuid := helper.GenerateRandomUuid(16)
	agentName := uuid

	orderNo := fmt.Sprintf("%v%v%v", agentName, time.Now().Format("0601"), body.Id)
	if body.OrderNo == "" {
		updateBody := make(map[string]interface{})
		updateBody["order_no"] = orderNo
		if err := r.db.Table("paygate_mazepay_order").Where("id = ?", body.Id).Updates(updateBody).Error; err != nil {
			return nil, err
		}
	}
	return &body.Id, nil
}

func (r repo) CheckMazepayDepositOrderInLast5Minutes(userId int64) (*model.MazepayOrderResponse, error) {

	var record model.MazepayOrderResponse

	selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount, tb_order.transfer_amount AS transfer_amount"
	selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
	selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.qr_base64 AS qr_base64, tb_order.extra_promptpay_id AS extra_promptpay_id, tb_order.payment_page_url AS payment_page_url"
	selectedFields += ", tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
	selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

	query := r.db.Table("paygate_mazepay_order as tb_order")
	query = query.Select(selectedFields)
	query = query.Joins("LEFT JOIN paygate_order_type AS tb_type ON tb_order.order_type_id = tb_type.id")
	query = query.Where("tb_order.order_type_id = ?", model.PAYGATE_ORDER_TYPE_DEPOSIT)
	query = query.Where("tb_order.user_id = ?", userId)
	// query = query.Where("tb_order.amount = ?", amount) หนึ่งคน หนึ่ง Order แทรกไม่ได้
	if err := query.
		Where("tb_order.transaction_status = ?", "WAIT_PAYMENT").
		Order("tb_order.id DESC").
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) UpdateDbMazepayOrderError(id int64, remark string) error {

	updateBody := map[string]interface{}{
		"transaction_status": "ERROR",
		"remark":             remark,
	}
	// sql := r.db.Table("paygate_mazepay_order").Where("id = ?", id).Where("transaction_status = ?", "PENDING") and WAIT_PAYMENT
	sql := r.db.Table("paygate_mazepay_order").Where("id = ?", id).Where("transaction_status IN ?", []string{"PENDING", "WAIT_PAYMENT"})
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdateDbMazepayOrder(id int64, body model.MazepayOrderUpdateBody) error {

	if err := r.db.Table("paygate_mazepay_order").Where("id = ?", id).Updates(body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) ApproveDbMazepayOrder(id int64, webhookStatus string) error {

	updateBody := make(map[string]interface{})
	updateBody["transaction_status"] = webhookStatus
	updateBody["payment_at"] = time.Now()

	sql := r.db.Table("paygate_mazepay_order").Where("id = ?", id).Where("transaction_status = ?", "WAIT_PAYMENT")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdateMazepayOrderBankSucess(id int64, transId int64, actionBy int64) error {

	updateBody := map[string]interface{}{
		"bank_transaction_id":     transId,
		"bank_transaction_status": "SUCCESS",
		"action_by":               actionBy,
	}

	sql := r.db.Table("paygate_mazepay_order").Where("id = ?", id).Where("bank_transaction_status = ?", "PENDING")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetMazepayCustomerById(id int64) (*model.MazepayCustomerResponse, error) {

	var record model.MazepayCustomerResponse

	selectedFields := "*"
	if err := r.db.Table("paygate_mazepay_customer as tb_customer").
		Select(selectedFields).
		Where("tb_customer.id = ?", id).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetMazepayCustomerByUserId(userId int64) (*model.MazepayCustomerResponse, error) {

	var record model.MazepayCustomerResponse

	selectedFields := "*"
	if err := r.db.Table("paygate_mazepay_customer as tb_customer").
		Select(selectedFields).
		Where("tb_customer.user_id = ?", userId).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetMazepayCustomerList(req model.MazepayCustomerListRequest) ([]model.MazepayCustomerResponse, int64, error) {

	var list []model.MazepayCustomerResponse
	var total int64

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("paygate_mazepay_customer as tb_customer")
	count = count.Select("tb_customer.id")

	if err := count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "*"
		query := r.db.Table("paygate_mazepay_customer as tb_customer")
		query = query.Select(selectedFields)

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}

	return list, total, nil
}

func (r repo) CreateMazepayCustomer(body model.MazepayCustomerCreateBody) (*int64, error) {

	if err := r.db.Table("paygate_mazepay_customer").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) UpdateMazepayCustomer(id int64, body model.MazepayCustomerUpdateBody) error {

	if err := r.db.Table("paygate_mazepay_customer").Where("id = ?", id).Updates(body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) DeleteMazepayCustomer(id int64) error {

	if err := r.db.Table("paygate_mazepay_customer").Where("id = ?", id).Delete(&model.MazepayCustomer{}).Error; err != nil {
		return err
	}
	return nil
}
