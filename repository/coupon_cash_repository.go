package repository

import (
	"cybergame-api/model"
	"time"

	"gorm.io/gorm"
)

func NewCouponCashRepository(db *gorm.DB) CouponCashRepository {
	return &repo{db}
}

type CouponCashRepository interface {
	// option
	GetCouponCashStatus() ([]model.CouponCashStatus, error)
	GetCouponCashUserStatus() ([]model.CouponCashUserStatus, error)
	//coupon cash main
	CreateCouponCash(req model.CouponCashCreateRequest) (int64, error)
	GetCouponCashList(req model.GetCouponCashListRequest) ([]model.GetCouponCashListResponse, int64, error)
	GetCouponCashUserList(req model.GetCouponCashUserListRequest) ([]model.GetCouponCashUserListResponse, int64, error)
	SoftDeleteCouponCashById(body model.SoftDeleteCouponCashUserByCouponCashId) error
	SoftDeleteCouponCashUserByCouponCashId(body model.SoftDeleteCouponCashUserByCouponCashId) error
	GetCouponCashUserById(id int64) (*model.CouponCashUser, error)
	//coupon cash internal
	CreateCouponCashUser(reqChunk []model.CouponCashCreateCouponCashUserBody) error
	DeleteCouponCashUserByCouponCashId(couponCashId int64) error
	DeleteCouponCashById(couponCashId int64) error
	UpdateCouponCashUserFromUser(req model.UpdateCouponCashUserFromUserBody) error
	GetCouponCashUserByGenerateKey(generateKey string) (model.CouponCashUser, error)
	GetCouponCashById(couponCashId int64) (model.CouponCash, error)
	IncreaseUserCredit(body model.UserTransactionCreateRequest) (*model.UserTransactionCreateResponse, error)
	CouponCashUserConfirmCreate(req model.CouponCashUserConfirmCreateRequest) (int64, error)
	GetCouponCashUserConfirm(confirmKey string) (int64, error)
	GetUserById(id int64) (*model.UserResponse, error)
	CouponCashUserConfirmRollback(confirmKeyId int64, newNewconfirmKey string) error
	DeleteCouponCashUserById(body model.SoftDeleteCouponCashUserById) error
	GetCouponCashUserWithdrawTurnOverByUserId(userId int64) ([]model.GetCouponCashUserListResponse, error)
	GetTodaySumUserPlayLogList(req model.UserTodayPlaylogListRequest) ([]model.UserTodaySumPlaylogReponse, int64, error)
	ClearWithdrawCouponCashUserUpdateAllToSuccess() error
	ClearWithdrawCouponCashUserUpdateToSuccess(userId int64) error
	CreateTurnoverUserStatement(body model.TurnoverUserStatementCreateBody) (*int64, error)
	CouponCashUserCheckTurnStatement(req model.CouponCashUserCheckTurnStatementRequest) ([]model.TurnoverUserStatementResponse, error)
	UpdateTurnoverUserStatement(id int64, body model.TurnoverUserStatementUpdateBody) error
	CreateTurnoverUserWithdrawLog(body model.CreateTurnoverUserWithdrawLog) (*int64, error)
	UpdateCouponCashToSuccessWithId(id int64) error
	CouponCashUserCheckTurnStatementDeleted(req model.CouponCashUserDeletedTurnStatementRequest) (*model.TurnoverUserStatementResponse, error)
	GetConfiguration() (*model.ConfigurationResponse, error)
	GetUser(id int64) (*model.UserDetail, error)
	CheckTurnSuccessOnThisDay(req model.CheckTurnSuccessOnThisDayRequest) (*model.CheckTurnSuccessOnThisDayResponse, error)
	CheckAvalibleCouponAndPromtion(userId int64, typeCheck string) (*model.TurnoverUserStatementResponse, error)

	//coupon cash summary
	CouponCashSummary(body model.CouponCashSummaryRequest) (*model.CouponCashSummaryResponse, error)
	CouponCashUserSummary(req model.CouponCashUserSummaryRequest) (*model.CouponCashUserSummaryResponse, error)
}

func (r repo) GetCouponCashStatus() ([]model.CouponCashStatus, error) {

	var result []model.CouponCashStatus
	selectedFields := "id, name, label_th, label_en"
	query := r.db.Table("coupon_cash_status")
	query = query.Select(selectedFields)
	if err := query.Find(&result).Error; err != nil {
		return nil, err
	}

	return result, nil
}
func (r repo) GetCouponCashUserStatus() ([]model.CouponCashUserStatus, error) {

	var result []model.CouponCashUserStatus
	selectedFields := "id, name, label_th, label_en"
	query := r.db.Table("coupon_cash_user_status")
	query = query.Select(selectedFields)
	if err := query.Find(&result).Error; err != nil {
		return nil, err
	}

	return result, nil
}

func (r repo) CreateCouponCash(req model.CouponCashCreateRequest) (int64, error) {

	query := r.db.Table("coupon_cash")
	query = query.Create(&req)
	if err := query.Error; err != nil {
		return 0, err
	}

	return req.Id, nil
}

func (r repo) CreateCouponCashUser(reqChunk []model.CouponCashCreateCouponCashUserBody) error {

	query := r.db.Table("coupon_cash_user")
	query = query.Create(&reqChunk)
	if err := query.Error; err != nil {
		return err
	}

	return nil
}

func (r repo) DeleteCouponCashUserByCouponCashId(couponCashId int64) error {

	query := r.db.Table("coupon_cash_user")
	query = query.Where("coupon_cash_id IN ?", couponCashId)
	query = query.Delete(&model.CouponCashUser{})
	if err := query.Error; err != nil {
		return err
	}

	return nil
}

func (r repo) DeleteCouponCashById(couponCashId int64) error {

	query := r.db.Table("coupon_cash")
	query = query.Where("id = ?", couponCashId)
	query = query.Delete(&model.CouponCash{})
	if err := query.Error; err != nil {
		return err
	}

	return nil
}

func (r repo) GetCouponCashList(req model.GetCouponCashListRequest) ([]model.GetCouponCashListResponse, int64, error) {
	var list []model.GetCouponCashListResponse
	var total int64
	var err error

	selectedFields := "coupon_cash.id as id, coupon_cash.name as name, coupon_cash.coupon_total as coupon_total, coupon_cash.coupon_turnover as coupon_turnover"
	selectedFields += ", coupon_cash.coupon_bonus as coupon_bonus"
	selectedFields += ", coupon_cash.coupon_cash_status_id as coupon_cash_status_id , coupon_cash_status.label_th as coupon_cash_status_th"
	selectedFields += ", coupon_cash.created_by_admin_id as created_by_admin_id, admin.fullname as created_by_admin_name"
	selectedFields += ", coupon_cash.deleted_by_admin_id as deleted_by_admin_id, admin2.fullname as deleted_by_admin_name"
	selectedFields += ", coupon_cash.created_at as created_at, coupon_cash.updated_at as updated_at, coupon_cash.deleted_at as deleted_at"
	selectedFields += ", coupon_cash.turnover_to_play as turnover_to_play"

	count := r.db.Table("coupon_cash")
	count = count.Joins("LEFT JOIN admin ON coupon_cash.created_by_admin_id = admin.id")
	count = count.Joins("LEFT JOIN admin as admin2 ON coupon_cash.deleted_by_admin_id = admin2.id")
	count = count.Joins("JOIN coupon_cash_status ON coupon_cash.coupon_cash_status_id = coupon_cash_status.id")
	count = count.Select("coupon_cash.id")
	if req.StartDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.StartDate)
		if err != nil {
			return nil, total, err
		}
		count = count.Where("coupon_cash.created_at >= ?", startDateAtBkk)
	}

	if req.EndDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.EndDate)
		if err != nil {
			return nil, total, err
		}
		count = count.Where("coupon_cash.created_at <= ?", endDateAtBkk)
	}

	if req.Search != "" {
		count = count.Where("coupon_cash.name LIKE ?", "%"+req.Search+"%")
	}

	if req.CouponCashStatusId != nil {
		count = count.Where("coupon_cash.coupon_cash_status_id = ?", req.CouponCashStatusId)
	}

	if err := count.Count(&total).Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		query := r.db.Table("coupon_cash")
		query = query.Joins("LEFT JOIN admin ON coupon_cash.created_by_admin_id = admin.id")
		query = query.Joins("LEFT JOIN admin as admin2 ON coupon_cash.deleted_by_admin_id = admin2.id")
		query = query.Joins("JOIN coupon_cash_status ON coupon_cash.coupon_cash_status_id = coupon_cash_status.id")
		query = query.Select(selectedFields)
		if req.StartDate != "" {
			startDateAtBkk, err := r.ParseBodBkk(req.StartDate)
			if err != nil {
				return nil, total, err
			}
			query = query.Where("coupon_cash.created_at >= ?", startDateAtBkk)
		}

		if req.EndDate != "" {
			endDateAtBkk, err := r.ParseEodBkk(req.EndDate)
			if err != nil {
				return nil, total, err
			}
			query = query.Where("coupon_cash.created_at <= ?", endDateAtBkk)
		}

		if req.Search != "" {
			query = query.Where("coupon_cash.name LIKE ?", "%"+req.Search+"%")
		}

		if req.CouponCashStatusId != nil {
			query = query.Where("coupon_cash.coupon_cash_status_id = ?", req.CouponCashStatusId)
		}

		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}

		if err = query.
			Order("coupon_cash.created_at desc").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetCouponCashUserList(req model.GetCouponCashUserListRequest) ([]model.GetCouponCashUserListResponse, int64, error) {

	var list []model.GetCouponCashUserListResponse
	var total int64

	selectedFields := "coupon_cash_user.id as id, user.member_code as member_code, user.fullname as full_name, coupon_cash_user.coupon_cash_id as coupon_cash_id"
	selectedFields += ", coupon_cash.name as coupon_cash_name, coupon_cash.coupon_turnover as coupon_cash_turnover, coupon_cash.coupon_bonus as coupon_cash_bonus"
	selectedFields += ", coupon_cash_user.generate_key as generate_key, coupon_cash_user.coupon_cash_user_status_id as coupon_cash_user_status_id"
	selectedFields += ", coupon_cash_user_status.label_th as coupon_cash_user_status_th"
	selectedFields += ", coupon_cash_user.created_at as created_at, coupon_cash_user.user_receive_at as user_receive_at"
	selectedFields += ", coupon_cash_user.created_by_admin_id as created_by_admin_id, admin.fullname as created_by_admin_name"
	selectedFields += ", coupon_cash_user.deleted_by_admin_id as deleted_by_admin_id, admin2.fullname as deleted_by_admin_name"
	selectedFields += ", user.id as user_id"
	selectedFields += ", coupon_cash.turnover_to_play as coupon_cash_turnover_to_play"

	count := r.db.Table("coupon_cash_user")
	count = count.Joins("LEFT JOIN user ON coupon_cash_user.user_id = user.id")
	count = count.Joins("JOIN coupon_cash ON coupon_cash_user.coupon_cash_id = coupon_cash.id")
	count = count.Joins("JOIN coupon_cash_user_status ON coupon_cash_user.coupon_cash_user_status_id = coupon_cash_user_status.id")
	count = count.Joins("LEFT JOIN admin ON coupon_cash_user.created_by_admin_id = admin.id")
	count = count.Joins("LEFT JOIN admin as admin2 ON coupon_cash_user.deleted_by_admin_id = admin2.id")
	count = count.Select("coupon_cash_user.id")
	if req.CouponCashId != nil {
		count = count.Where("coupon_cash_user.coupon_cash_id = ?", req.CouponCashId)
	}

	if req.StartDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.StartDate)
		if err != nil {
			return nil, total, err
		}
		count = count.Where("coupon_cash_user.user_receive_at >= ?", startDateAtBkk)
	}

	if req.EndDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.EndDate)
		if err != nil {
			return nil, total, err
		}
		count = count.Where("coupon_cash_user.user_receive_at <= ?", endDateAtBkk)
	}

	if req.Search != "" {
		count = count.Where("user.member_code LIKE ? OR user.fullname LIKE ?", "%"+req.Search+"%", "%"+req.Search+"%")
	}
	if req.SearchCode != "" {
		count = count.Where("coupon_cash_user.generate_key LIKE ?", "%"+req.SearchCode+"%")
	}

	if req.CouponCashUserStatusId != nil {
		count = count.Where("coupon_cash_user.coupon_cash_user_status_id = ?", req.CouponCashUserStatusId)
	}

	if err := count.Count(&total).Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		query := r.db.Table("coupon_cash_user")
		query = query.Joins("LEFT JOIN user ON coupon_cash_user.user_id = user.id")
		query = query.Joins("JOIN coupon_cash ON coupon_cash_user.coupon_cash_id = coupon_cash.id")
		query = query.Joins("JOIN coupon_cash_user_status ON coupon_cash_user.coupon_cash_user_status_id = coupon_cash_user_status.id")
		query = query.Joins("LEFT JOIN admin ON coupon_cash_user.created_by_admin_id = admin.id")
		query = query.Joins("LEFT JOIN admin as admin2 ON coupon_cash_user.deleted_by_admin_id = admin2.id")
		query = query.Select(selectedFields)
		if req.CouponCashId != nil {
			query = query.Where("coupon_cash_user.coupon_cash_id = ?", req.CouponCashId)
		}

		if req.StartDate != "" {
			startDateAtBkk, err := r.ParseBodBkk(req.StartDate)
			if err != nil {
				return nil, total, err
			}
			query = query.Where("coupon_cash_user.user_receive_at >= ?", startDateAtBkk)
		}

		if req.EndDate != "" {
			endDateAtBkk, err := r.ParseEodBkk(req.EndDate)
			if err != nil {
				return nil, total, err
			}
			query = query.Where("coupon_cash_user.user_receive_at <= ?", endDateAtBkk)
		}

		if req.Search != "" {
			query = query.Where("user.member_code LIKE ? OR user.fullname LIKE ?", "%"+req.Search+"%", "%"+req.Search+"%")
		}
		if req.SearchCode != "" {
			query = query.Where("coupon_cash_user.generate_key LIKE ?", "%"+req.SearchCode+"%")
		}

		if req.CouponCashUserStatusId != nil {
			query = query.Where("coupon_cash_user.coupon_cash_user_status_id = ?", req.CouponCashUserStatusId)
		}

		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}

		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}

	return list, total, nil
}

func (r repo) UpdateCouponCashUserFromUser(req model.UpdateCouponCashUserFromUserBody) error {

	updateBody := map[string]interface{}{
		"coupon_cash_user_status_id": req.CouponCashUserStatusId,
		"user_receive_at":            req.UserReceiveAt,
		"user_id":                    req.UserId,
	}

	query := r.db.Table("coupon_cash_user")
	query = query.Where("generate_key = ?", req.GenerateKey)
	query = query.Updates(updateBody)
	if err := query.Error; err != nil {
		return err
	}

	return nil

}

func (r repo) GetCouponCashUserByGenerateKey(generateKey string) (model.CouponCashUser, error) {
	var result model.CouponCashUser
	query := r.db.Table("coupon_cash_user")
	query = query.Where("generate_key = ?", generateKey)
	if err := query.First(&result).Error; err != nil {
		return result, err
	}
	return result, nil
}

func (r repo) GetCouponCashById(couponCashId int64) (model.CouponCash, error) {
	var result model.CouponCash
	query := r.db.Table("coupon_cash")
	query = query.Where("id = ?", couponCashId)
	if err := query.First(&result).Error; err != nil {
		return result, err
	}
	return result, nil
}

func (r repo) CouponCashUserConfirmCreate(req model.CouponCashUserConfirmCreateRequest) (int64, error) {
	query := r.db.Table("coupon_cash_user_confirm")
	query = query.Create(&req)
	if err := query.Error; err != nil {
		return 0, err
	}

	return req.Id, nil
}

func (r repo) GetCouponCashUserConfirm(confirmKey string) (int64, error) {

	var result model.CouponCashUserConfirm
	query := r.db.Table("coupon_cash_user_confirm")
	query = query.Where("confirm_key = ?", confirmKey)
	if err := query.First(&result).Error; err != nil {
		return result.Id, err
	}
	return result.Id, nil
}

func (r repo) CouponCashUserConfirmRollback(confirmKeyId int64, newNewconfirmKey string) error {
	query := r.db.Table("coupon_cash_user_confirm")
	query = query.Where("id = ?", confirmKeyId)
	query = query.Update("confirm_key", newNewconfirmKey)
	if err := query.Error; err != nil {
		return err
	}
	return nil

}

func (r repo) SoftDeleteCouponCashById(body model.SoftDeleteCouponCashUserByCouponCashId) error {

	updatedBody := map[string]interface{}{
		"coupon_cash_status_id": model.COUPON_DELETE,
		"deleted_by_admin_id":   body.DeletedByAdminId,
		"deleted_at":            body.DeletedAt,
	}

	query := r.db.Table("coupon_cash")
	query = query.Where("id = ?", body.Id)
	query = query.Updates(updatedBody)
	if err := query.Error; err != nil {
		return err
	}

	return nil
}

func (r repo) SoftDeleteCouponCashUserByCouponCashId(body model.SoftDeleteCouponCashUserByCouponCashId) error {

	updatedBody := map[string]interface{}{
		"coupon_cash_user_status_id": model.COUPON_USER_DELETE,
		"deleted_by_admin_id":        body.DeletedByAdminId,
		"deleted_at":                 body.DeletedAt,
	}

	query := r.db.Table("coupon_cash_user")
	query = query.Where("coupon_cash_id = ?", body.Id)
	query = query.Where("coupon_cash_user_status_id = ?", model.COUPON_USER_PENDING)
	query = query.Updates(updatedBody)
	if err := query.Error; err != nil {
		return err
	}

	return nil
}

func (r repo) DeleteCouponCashUserById(body model.SoftDeleteCouponCashUserById) error {

	updatedBody := map[string]interface{}{
		"coupon_cash_user_status_id": model.COUPON_USER_DELETE,
		"deleted_by_admin_id":        body.DeletedByAdminId,
		"deleted_at":                 body.DeletedAt,
	}

	query := r.db.Table("coupon_cash_user")
	query = query.Where("id = ?", body.Id)
	query = query.Where("coupon_cash_user_status_id = ?", model.COUPON_USER_PENDING)
	query = query.Updates(updatedBody)
	if err := query.Error; err != nil {
		return err
	}

	return nil
}

func (r repo) GetCouponCashUserById(id int64) (*model.CouponCashUser, error) {
	var result model.CouponCashUser
	query := r.db.Table("coupon_cash_user")
	query = query.Where("id = ?", id)
	if err := query.First(&result).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (r repo) GetCouponCashUserWithdrawTurnOverByUserId(userId int64) ([]model.GetCouponCashUserListResponse, error) {

	var list []model.GetCouponCashUserListResponse
	selectedFields := "coupon_cash_user.id as id, user.member_code as member_code, user.fullname as full_name, coupon_cash_user.coupon_cash_id as coupon_cash_id"
	selectedFields += ", coupon_cash.name as coupon_cash_name, coupon_cash.coupon_turnover as coupon_cash_turnover, coupon_cash.coupon_bonus as coupon_cash_bonus"
	selectedFields += ", coupon_cash_user.generate_key as generate_key, coupon_cash_user.coupon_cash_user_status_id as coupon_cash_user_status_id"
	selectedFields += ", coupon_cash_user_status.label_th as coupon_cash_user_status_th"
	selectedFields += ", coupon_cash_user.created_at as created_at, coupon_cash_user.user_receive_at as user_receive_at"
	selectedFields += ", coupon_cash.turnover_to_play as coupon_cash_turnover_to_play"

	query := r.db.Table("coupon_cash_user")
	query = query.Joins("LEFT JOIN user ON coupon_cash_user.user_id = user.id")
	query = query.Joins("JOIN coupon_cash ON coupon_cash_user.coupon_cash_id = coupon_cash.id")
	query = query.Joins("JOIN coupon_cash_user_status ON coupon_cash_user.coupon_cash_user_status_id = coupon_cash_user_status.id")
	query = query.Select(selectedFields)
	query = query.Where("coupon_cash_user.user_id = ?", userId)
	query = query.Where("coupon_cash_user.coupon_cash_user_status_id = ?", model.COUPON_USER_RECEIVE_ON_TURNOVER)
	if err := query.Scan(&list).Error; err != nil {
		return nil, err
	}
	return list, nil

}

func (r repo) ClearWithdrawCouponCashUserUpdateToSuccess(userId int64) error {
	timeNow := time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02")

	updatedBody := map[string]interface{}{
		"coupon_cash_user_status_id": model.COUPON_USER_SUCCESS,
	}

	query := r.db.Table("coupon_cash_user")
	query = query.Where("user_id = ?", userId)
	query = query.Where("coupon_cash_user_status_id = ?", model.COUPON_USER_RECEIVE_ON_TURNOVER)
	endDateAtBkk, err := r.ParseBodBkk(timeNow)
	if err != nil {
		return nil
	}
	query = query.Where("user_receive_at <= ?", endDateAtBkk)
	query = query.Updates(updatedBody)
	if err := query.Error; err != nil {
		return err
	}

	return nil
}
func (r repo) ClearWithdrawCouponCashUserUpdateAllToSuccess() error {
	timeNow := time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02")

	updatedBody := map[string]interface{}{
		"coupon_cash_user_status_id": model.COUPON_USER_SUCCESS,
	}

	query := r.db.Table("coupon_cash_user")
	query = query.Where("coupon_cash_user_status_id = ?", model.COUPON_USER_RECEIVE_ON_TURNOVER)
	query = query.Where("user_receive_at <= ?", timeNow)
	endDateAtBkk, err := r.ParseBodBkk(timeNow)
	if err != nil {
		return nil
	}
	query = query.Where("user_receive_at <= ?", endDateAtBkk)
	query = query.Updates(updatedBody)
	if err := query.Error; err != nil {
		return err
	}

	return nil
}

func (r repo) UpdateCouponCashToSuccessWithId(id int64) error {

	updatedBody := map[string]interface{}{
		"coupon_cash_user_status_id": model.COUPON_USER_SUCCESS,
	}

	query := r.db.Table("coupon_cash_user")
	query = query.Where("coupon_cash_user_status_id = ?", model.COUPON_USER_RECEIVE_ON_TURNOVER)
	query = query.Where("id = ?", id)
	query = query.Updates(updatedBody)
	if err := query.Error; err != nil {
		return err
	}

	return nil
}

func (r repo) CouponCashSummary(req model.CouponCashSummaryRequest) (*model.CouponCashSummaryResponse, error) {

	var result model.CouponCashSummaryResponse

	var CouponCash struct {
		TotalCouponCash     int64 `json:"total_coupon_cash"`
		TotalCouponCashUser int64 `json:"total_coupon_cash_user"`
	}

	selectedFields := "COUNT(coupon_cash.id) as total_coupon_cash"
	selectedFields += ", SUM(coupon_cash.coupon_total) as total_coupon_cash_user"

	query := r.db.Table("coupon_cash")
	if req.StartDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.StartDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("coupon_cash.created_at >= ?", startDateAtBkk)
	}

	if req.EndDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.EndDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("coupon_cash.created_at <= ?", endDateAtBkk)
	}

	if err := query.Select(selectedFields).Take(&CouponCash).Error; err != nil {
		return nil, err
	}

	result.TotalCouponCash = CouponCash.TotalCouponCash
	result.TotalCouponCashUser = CouponCash.TotalCouponCashUser

	var CouponCashUser struct {
		TotalCouponCashUserUse    int64   `json:"total_coupon_cash_user_use"`
		TotalCouponCashUserNotUse int64   `json:"total_coupon_cash_user_not_use"`
		TotalCouponCashUserBonus  float64 `json:"total_coupon_cash_user_bonus"`
	}

	selectedFields = " COUNT(CASE WHEN coupon_cash_user.user_id IS NOT NULL THEN 0 ELSE NULL END) AS total_coupon_cash_user_use"
	selectedFields += ", COUNT(CASE WHEN coupon_cash_user.user_id IS NULL THEN 0 ELSE NULL END) AS total_coupon_cash_user_not_use"
	selectedFields += ", SUM(CASE WHEN coupon_cash_user.user_id > 0 THEN coupon_cash.coupon_bonus ELSE 0 END) AS total_coupon_cash_user_bonus"

	query = r.db.Table("coupon_cash_user")
	query = query.Joins("JOIN coupon_cash ON coupon_cash_user.coupon_cash_id = coupon_cash.id")
	query = query.Select(selectedFields)
	if req.StartDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.StartDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("coupon_cash.created_at >= ?", startDateAtBkk)

	}

	if req.EndDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.EndDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("coupon_cash.created_at <= ?", endDateAtBkk)

	}

	if err := query.Take(&CouponCashUser).Error; err != nil {
		return nil, err
	}

	result.TotalCouponCashUserUse = CouponCashUser.TotalCouponCashUserUse
	result.TotalCouponCashUserNotUse = CouponCashUser.TotalCouponCashUserNotUse
	result.TotalCouponCashUserBonus = CouponCashUser.TotalCouponCashUserBonus
	return &result, nil
}

func (r repo) CouponCashUserSummary(req model.CouponCashUserSummaryRequest) (*model.CouponCashUserSummaryResponse, error) {

	var result model.CouponCashUserSummaryResponse

	var CouponCash struct {
		TotalCouponCash     int64 `json:"total_coupon_cash"`
		TotalCouponCashUser int64 `json:"total_coupon_cash_user"`
	}

	selectedFields := "COUNT(coupon_cash.id) as total_coupon_cash"
	selectedFields += ", SUM(coupon_cash.coupon_total) as total_coupon_cash_user"

	query := r.db.Table("coupon_cash")

	if req.CouponCashId != nil {
		query = query.Where("coupon_cash.id = ?", req.CouponCashId)
	}

	if err := query.Select(selectedFields).Take(&CouponCash).Error; err != nil {
		return nil, err
	}

	result.TotalCouponCash = CouponCash.TotalCouponCash
	result.TotalCouponCashUser = CouponCash.TotalCouponCashUser

	var CouponCashUser struct {
		TotalCouponCashUserUse    int64   `json:"total_coupon_cash_user_use"`
		TotalCouponCashUserNotUse int64   `json:"total_coupon_cash_user_not_use"`
		TotalCouponCashUserBonus  float64 `json:"total_coupon_cash_user_bonus"`
	}

	selectedFields = " COUNT(CASE WHEN coupon_cash_user.user_id IS NOT NULL THEN 0 ELSE NULL END) AS total_coupon_cash_user_use"
	selectedFields += ", COUNT(CASE WHEN coupon_cash_user.user_id IS NULL THEN 0 ELSE NULL END) AS total_coupon_cash_user_not_use"
	selectedFields += ", SUM(CASE WHEN coupon_cash_user.user_id > 0 THEN coupon_cash.coupon_bonus ELSE 0 END) AS total_coupon_cash_user_bonus"

	query = r.db.Table("coupon_cash_user")
	query = query.Joins("JOIN coupon_cash ON coupon_cash_user.coupon_cash_id = coupon_cash.id")
	query = query.Select(selectedFields)
	if req.StartDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.StartDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("coupon_cash_user.user_receive_at >= ?", startDateAtBkk)

	}

	if req.EndDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.EndDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("coupon_cash_user.user_receive_at <= ?", endDateAtBkk)

	}
	if req.CouponCashId != nil {
		query = query.Where("coupon_cash.id = ?", req.CouponCashId)
	}

	if err := query.Take(&CouponCashUser).Error; err != nil {
		return nil, err
	}

	result.TotalCouponCashUserUse = CouponCashUser.TotalCouponCashUserUse
	result.TotalCouponCashUserNotUse = CouponCashUser.TotalCouponCashUserNotUse
	result.TotalCouponCashUserBonus = CouponCashUser.TotalCouponCashUserBonus

	return &result, nil
}
