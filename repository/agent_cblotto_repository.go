package repository

import (
	"bytes"
	"cybergame-api/model"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"gorm.io/gorm"
)

func NewAgentCblottoRepository(db *gorm.DB) AgentCblottoRepository {
	return &repo{db}
}

type AgentCblottoRepository interface {
	// admin
	GetInternalAgentCblottoSetting() (*model.GetInternalAgentCblottoSetting, error)
	UpdateAgentCblottoSetting(body model.UpdateAgentCblottoSetting) error
	ClearCacheAgentCblottoSetting()

	// web [ยังไม่มี เกม list]
	//รอ เกม list
	GetAgentCblottoGameLogin(reqBody model.AgentCblottoUserLoginGame, urlDetail model.CallApiAgentCblottoDetail) (*model.AgentCblottoUserLoginGameResponse, error)

	// internal
	GetMemberCode(id int64) (*string, error)

	// call back
	GetUserByMemberCode(memberCode string) (*model.GetUserByMemberCode, error)
	DecreaseUserCreditFromOtherAgent(body model.DecreaseUserCreditFromOtherAgentRequest) (*model.UserTransactionCreateResponse, error)
	IncreaseUserCreditFromOtherAgent(body model.IncreaseUserCreditFromOtherAgentRequest) (*model.UserTransactionCreateResponse, error)
	CreateOrUpdateAgentCblottoCallback(body model.CreateAgentCblottoCallback) error
	UpdateAgentCblottoCallback(body model.UpdateAgentCblottoCallback) error
}

var saveCacheAgentCblottoSetting *model.GetInternalAgentCblottoSetting

func (r repo) GetInternalAgentCblottoSetting() (*model.GetInternalAgentCblottoSetting, error) {

	if saveCacheAgentCblottoSetting != nil {
		return saveCacheAgentCblottoSetting, nil
	}

	var result model.GetInternalAgentCblottoSetting

	selectFields := "id, is_active, program_allow_use, cblotto_app_id, cblotto_app_private, cblotto_url, cblotto_href_back_url"

	query := r.db.Table("agent_cblotto_setting")
	query = query.Where("id = ?", 1)
	query = query.Select(selectFields)
	query = query.Take(&result)
	if err := query.Error; err != nil {
		return nil, err
	}

	saveCacheAgentCblottoSetting = &result

	return &result, nil
}

func (r repo) ClearCacheAgentCblottoSetting() {
	saveCacheAgentCblottoSetting = nil
}

func (r repo) UpdateAgentCblottoSetting(body model.UpdateAgentCblottoSetting) error {

	query := r.db.Table("agent_cblotto_setting")
	query = query.Where("id = ?", 1)
	query = query.Updates(&body)
	if err := query.Error; err != nil {
		return err
	}

	saveCacheAgentCblottoSetting = nil

	return nil
}

func (r repo) GetAgentCblottoGameLogin(reqBody model.AgentCblottoUserLoginGame, urlDetail model.CallApiAgentCblottoDetail) (*model.AgentCblottoUserLoginGameResponse, error) {

	agentCblottoPrivateKey := urlDetail.CblottoAppPrivate
	agentCblottoUrl := urlDetail.CblottoUrl
	url := fmt.Sprintf("%s/api/external/user/login-game", agentCblottoUrl)

	requestBody, err := json.Marshal(reqBody)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request body: %w", err)
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", agentCblottoPrivateKey)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}

	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	var session model.AgentCblottoUserLoginGameResponse
	if err := json.NewDecoder(resp.Body).Decode(&session); err != nil {
		return nil, fmt.Errorf("failed to decode response body: %w", err)
	}

	return &session, nil
}

func (r repo) CreateOrUpdateAgentCblottoCallback(body model.CreateAgentCblottoCallback) error {

	// case ของ call back อันนี้ต้องใช้เ round และ member code เป็น หลัก ไม่งั้น duplicate ได้
	var result model.CreateAgentCblottoCallback
	selectFields := "id, payoff, bet_amount"
	query := r.db.Table("agent_cblotto_callback")
	query = query.Where("transaction_id = ? AND member_code = ?", body.TransactionId, body.MemberCode)
	query = query.Select(selectFields)
	query = query.Take(&result)
	if err := query.Error; err != nil {
		if err != gorm.ErrRecordNotFound {
			return err
		}
	}

	if result.Id == 0 {
		createQuery := r.db.Table("agent_cblotto_callback")
		createQuery = createQuery.Create(&body)
		if err := createQuery.Error; err != nil {
			return err
		}
	} else {
		var updateBody model.UpdateAgentCblottoCallback
		updateBody.UserId = &body.UserId
		updateBody.MemberCode = body.MemberCode
		if body.Payoff != 0 {
			updateBody.Payoff = &body.Payoff
		}
		if body.BetAmount != 0 {
			updateBody.BetAmount = &body.BetAmount
		}
		payoff := body.Payoff + result.Payoff
		betAmount := body.BetAmount + result.BetAmount
		if payoff == betAmount {
			// ถ้าเท่ากันจะไม่นับ เป็น turn จะให้เป็น 0 P.Lay confirm
			payoff = 0
			betAmount = 0
		}
		winloseAmount := payoff - betAmount
		updateBody.WinloseAmount = &winloseAmount
		updateBody.CancelAmount = &body.CancelAmount
		// stack
		// updateBody.Balance = &body.Balance
		// updateBody.BeforeBalance = &body.BeforeBalance
		updateBody.AfterBalance = &body.AfterBalance
		updateBody.TransactionId = body.TransactionId
		updateBody.RoundId = body.RoundId
		updateBody.GameId = &body.GameId
		updateBody.CallbackReason = &body.CallbackReason
		updateBody.Remark = &body.Remark
		updateBody.IsSuccess = &body.IsSuccess

		updateQuery := r.db.Table("agent_cblotto_callback")
		updateQuery = updateQuery.Where("id = ?", result.Id)
		updateQuery = updateQuery.Updates(&updateBody)
		if err := updateQuery.Error; err != nil {
			return err
		}
	}

	return nil
}

func (r repo) UpdateAgentCblottoCallback(body model.UpdateAgentCblottoCallback) error {

	query := r.db.Table("agent_cblotto_callback")
	query = query.Where("transaction_id = ? AND member_code = ?", body.TransactionId, body.MemberCode)
	query = query.Updates(&body)
	if err := query.Error; err != nil {
		return err
	}

	return nil
}
