package repository

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"errors"
	"fmt"
	"log"
	"strings"
	"time"

	"gorm.io/gorm"
)

var afCommissionResponse *model.AfCommissionResponse

func NewAffiliateRepository(db *gorm.DB) AffiliateRepository {
	return &repo{db}
}

type AffiliateRepository interface {
	GetDb() *gorm.DB
	GetDateFromDateType(req model.DateTypeResponse) (*model.DateTypeResponse, error)
	// CRUD
	GetUserAffiliate(userId int64) (*model.AffiliateUser, error)
	GetUserAffiliateList(userIds []int64) ([]model.AffiliateUser, error)
	CreateMissingAffiliate(userId int64) error
	GetUserAffSummary(userId int64) (*model.AfSummary, error)
	GetCommissionSetting() (*model.AfCommissionResponse, error)
	GetCommissionByUserId(userId int64) (*model.AfCommissionUser, error)
	GetAffMember(req model.AffMemberListRequest) ([]model.AffMember, int64, error)
	GetActiveAffMemberByLevel(req model.AffMemberListRequest) ([]model.AffMemberResponse, int64, error)
	GetIncome(req model.AffMemberListRequest) ([]model.AfIncomeList, int64, error)
	GetUserIncomeWebLogList(req model.UserIncomeWebLogListRequest) (*model.UserIncomeWebLogListResponse, error)
	GetCommissionUser(userId int64) (*model.AfSummary, error)
	GetAfRegisterBonusType() ([]model.AfRegisterBonusType, error)
	GetAfRegisterBonusOption() ([]model.AfRegisterBonusOption, error)
	GetReport(query model.AfReportQuery) ([]model.AfReport, *int64, error)
	CreateAffiliate(data model.Affiliate) error
	// CreateAffCommision(data []model.AfCreate) error
	UpdateCommission(data model.AfCommissionUpdateRequest) error
	UpdateTotalMemberFirstDeposit(refId int64) error
	IncreaseMemberFirstDeposit(userId, refId int64) error
	UpdateCommissionFirstDeposit(userId, refId int64, amount float64) error
	WithdrawTotalCommission(userId int64, withdrawAmount float64) error
	IncreaseLinkClick(userId int64) error
	// REF-RACE_CONDITION_BLOCKER
	GetRaceActionIdByActionKey(actionKey string) (*int64, error)
	GetRaceActionByActionKey(actionKey string) (*model.RaceAction, error)
	CreateRaceCondition(body model.RaceActionCreateBody) (int64, error)
	UpdateRaceCondition(id int64, body model.RaceActionUpdateBody) error
	// REF-USER_CREDIT
	GetUserMemberInfoById(id int64) (*model.UserResponse, error)
	IncreaseUserCredit(body model.UserTransactionCreateRequest) (*model.UserTransactionCreateResponse, error)
	DecreaseUserCredit(body model.UserTransactionCreateRequest) (*model.UserTransactionCreateResponse, error)
	// REF-BANK_LOG
	SuccessLog(name string, req interface{}, result interface{}) error
	ErrorLog(name string, req interface{}, result interface{}) error
	//
	GetUserIdByMemberList(memberList []string) ([]model.UserMemberList, error)
	GetPlayLog(MemberCode []string) ([]model.AffMember, error)
	// USER_RELATED
	GetRefUserByRefId(refId int64) (*model.UserGetDataByRef, error)
	GetUserAffiliateMemberList(req model.AffiliateMemberListRequest) ([]model.AffiliateMemberResponse, int64, error)
	// REF-USER_INCOME
	CreateUserIncomeLog(body model.UserIncomeLogCreateBody) (*int64, error)
	GetUserIncomeLogById(id int64) (*model.UserIncomeLogResponse, error)
	ConfirmUserIncomeLog(body model.UserIncomeLogConfirmBody) error
	GetUserIncomeCompletedLogList(req model.UserIncomeCompletedLogListRequest) ([]model.UserIncomeCompletedLogResponse, int64, error)
	GetMarketingConfigByKey(configKey string, defaultVal string) (*model.MarketingConfig, error)
	// Report
	MakeReportAffiliateUserList(statementDate string) error
	GetAffiliateUserList(req model.AffiliateUserListRequest) ([]model.AffiliateUserResponse, int64, error)
	GetAffiliateActiveUserList(req model.AffiliateUserListRequest) ([]model.AffiliateUserResponse, int64, error)
	GetAffiliateUserSummary(req model.AffiliateUserSummaryRequest) (*model.AffiliateUserSummaryResponse, error)
	GetAffiliateUserSummaryRealTime(req model.AffiliateUserSummaryRequest) (*model.AffiliateUserSummaryResponse, error)
	GetAffiliateDepositPlayList(req model.AffiliateDepositPlayListRequest) ([]model.AffiliateUserDepositPlayResponse, int64, error)
	GetAffiliateDepositPlayCountLevel(req model.AffiliateDepositPlayListRequest) (*model.AffiliateUserDownlineCountResponse, error)
	// REF-PLAY_LOG
	// GetUserPlayLogList(req model.UserPlaylogListRequest) ([]model.UserPlaylogResponse, int64, error)
	GetUserAffiliateIncomeKeyList(dailyKeyList []string) ([]string, int64, error)
	CreateUserAffiliateIncomeBulk(bodyList map[string]model.UserAffiliateIncomeCreateBody) error
	GetUserAffiliateTransactionKeyList(dailyKeyList []string) ([]string, int64, error)
	CreateUserAffiliateTransactionBulk(bodyList map[string]model.UserAffiliateIncomeTotalResponse) error
	// WEB-SOCKET
	// WebSocket(reqAlert model.WebScoket) error
	GetUserForGenmemberByUserId(userId int64) (*model.GetUserForGenmemberByUserId, error)
	// USER-AFF-INCOME
	GetAffTransactionById(id int64) (*model.AffTransactionResponse, error)
	GetAffTransactionList(req model.AffTransactionListRequest) ([]model.AffTransactionResponse, int64, error)
	GetAffTransactionListSummary(req model.AffTransactionListRequest) (*model.AffTransactionSummaryResponse, error)
	CreateAffTransaction(body model.AffTransactionCreateBody) (*int64, error)
	UpdateAffTransaction(id int64, body model.AffTransactionUpdateBody) error
	SetPendingAffTransactionList(ids []int64) error
	GetTotalAffTransactionPlayCommission(transIds []int64) (float64, error)
	// SetExpireAffTransactionList(ids []int64) error
	SetConfirmAffTransactionList(ids []int64) error
	CreateAffTransactionWithdraw(body model.AffTransactionWithdrawCreateBody) (*int64, error)
	GetAffTransactionWithdrawById(id int64) (*model.AffTransactionWithdraw, error)
	GetAffTransactionWithdrawByKey(rcKey string) (*model.AffTransactionWithdraw, error)
	ExpireUserAffiliateTransaction(days int64) error
	// turn over
	CreateTurnoverUserStatement(body model.TurnoverUserStatementCreateBody) (*int64, error)
	UpdateTurnoverUserStatement(id int64, body model.TurnoverUserStatementUpdateBody) error
	CreateTurnoverUserWithdrawLog(body model.CreateTurnoverUserWithdrawLog) (*int64, error)
	// MigrateAffLevel
	MigrateAffiliateUserAllLevel(uplineId int64) error

	// admin action
	CreateAdminAction(body model.AdminActionCreateBody) (*int64, error)

	// web
	GetWebAffiliateCommissionDetail() (*model.GetWebAffiliateCommissionDetailResponse, error)
}

func (r repo) CreateMissingAffiliate(userId int64) error {

	var oldAffiliate model.Affiliate
	if err := r.db.Table("user_affiliate").
		Where("user_id = ?", userId).
		Take(&oldAffiliate).
		Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			affiliate := map[string]interface{}{}
			affiliate["user_id"] = userId
			if err := r.db.Table("user_affiliate").
				Create(&affiliate).
				Error; err != nil {
				return err
			}
		}
	}
	return nil
}

func (r repo) GetUserAffiliate(userId int64) (*model.AffiliateUser, error) {

	var oldAffiliate model.AffiliateUser
	if err := r.db.Table("user_affiliate").
		Where("user_id = ?", userId).
		Take(&oldAffiliate).
		Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			affiliate := map[string]interface{}{}
			affiliate["user_id"] = userId
			if err := r.db.Table("user_affiliate").
				Create(&affiliate).
				Error; err != nil {
				return nil, err
			}
			// REGET
			if err := r.db.Table("user_affiliate").
				Where("user_id = ?", userId).
				Take(&oldAffiliate).
				Error; err != nil {
				return nil, err
			}
		}
	}
	return &oldAffiliate, nil
}

func (r repo) GetUserAffiliateList(userIds []int64) ([]model.AffiliateUser, error) {

	var list []model.AffiliateUser
	// SELECT //
	selectedFields := "*"
	query := r.db.Table("user_affiliate")
	query = query.Select(selectedFields)
	query = query.Where("user_id IN (?)", userIds)
	if err := query.Scan(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (r repo) Summary(userId int64) (*model.AfSummary, error) {

	var data model.AfSummary

	selectedFields := "sport AS percent_sport, casino AS percent_casino, slot AS percent_game"
	selectedFields += ", lottery AS percent_lottery, p2p AS percent_p2p, financial AS percent_financial"
	if err := r.db.Table("affiliate_commission").Select(selectedFields).Scan(&data).Limit(1).Error; err != nil {
		return nil, err
	}

	if err := r.db.Table("user_affiliate").
		Select(`
			id,
			commission_total,
			commission_current,
			first_deposit_bonus,
			bonus_share_total,
			link_click_total,
			member_total,
			member_deposit_total
		`).
		Where("user_id = ?", userId).
		Scan(&data).
		Limit(1).
		Error; err != nil {
		return nil, err
	}

	if err := r.db.Table("affiliate_income").
		Select(`
			SUM(sport) AS commission_sport,
			SUM(casino) AS commission_casino,
			SUM(slot) AS commission_game,
			SUM(lottery) AS commission_lottery,
			SUM(p2p) AS commission_p2p,
			SUM(financial) AS commission_financial
		`).
		Where("ref_id = ?", userId).
		Scan(&data).
		Error; err != nil {
		return nil, err
	}

	return &data, nil
}

func (r repo) GetUserAffSummary(userId int64) (*model.AfSummary, error) {

	var data model.AfSummary

	selectedFields1 := "sport AS percent_sport, casino AS percent_casino, slot AS percent_game"
	selectedFields1 += ", lottery AS percent_lottery, p2p AS percent_p2p, financial AS percent_financial"

	if err := r.db.Table("affiliate_commission").
		Select(selectedFields1).
		Scan(&data).
		Limit(1).
		Error; err != nil {
		return nil, err
	}

	selectedFields := "user_id, commission_total, commission_current, first_deposit_bonus, bonus_share_total, link_click_total, member_total, member_deposit_total"
	selectedFields += ", commission_sport, commission_casino, commission_game, commission_lottery, commission_p2p, commission_financial"
	if err := r.db.Table("user_affiliate").
		Select(selectedFields).
		Where("user_id = ?", userId).
		Scan(&data).
		Limit(1).
		Error; err != nil {
		return nil, err
	}

	return &data, nil
}

func (r repo) GetCommissionSetting() (*model.AfCommissionResponse, error) {

	var data model.AfCommissionResponse

	// [2025-04-28] เพิ่มตัวเลือกจะคำนวนจาก Turn/Valid หรือ Winlose

	if afCommissionResponse != nil {
		return afCommissionResponse, nil
	}

	selectedFields := "ac.first_deposit_bonus, ac.max_level AS max_level, ac.commission_from AS commission_from"
	selectedFields += ", ac.sport, ac.casino, ac.slot, ac.lottery, ac.p2p, ac.financial"
	selectedFields += ", ac.sport1, ac.casino1, ac.slot1, ac.lottery1, ac.p2p1, ac.financial1"
	selectedFields += ", ac.sport2, ac.casino2, ac.slot2, ac.lottery2, ac.p2p2, ac.financial2"
	selectedFields += ", ac.sport3, ac.casino3, ac.slot3, ac.lottery3, ac.p2p3, ac.financial3"
	selectedFields += ", ac.referral_bonus, ac.commission_withdraw_min, rbt.id AS register_bonus_type_id, rbt.name AS register_bonus_type_name"
	selectedFields += ", ac.register_bonus_min, ac.register_bonus_credit, rbo.id AS register_bonus_option_id, rbo.name AS register_bonus_option_name, ac.register_bonus_max_percent, ac.description"
	selectedFields += ", ac.collectable_days, ac.max_commission, ac.max_commission_per_line"
	if err := r.db.Table("affiliate_commission ac").
		Select(selectedFields).
		Joins("LEFT JOIN register_bonus_type rbt ON rbt.id = ac.register_bonus_type_id").
		Joins("LEFT JOIN register_bonus_option rbo ON rbo.id = ac.register_bonus_option_id").
		Take(&data).
		Error; err != nil {
		return nil, err
	}

	afCommissionResponse = &data

	return &data, nil
}

func (r repo) GetCommissionByUserId(userId int64) (*model.AfCommissionUser, error) {

	var commission model.AfCommission
	var income model.AfIncome

	if err := r.db.Table("affiliate_commission").
		Take(&commission).
		Error; err != nil {
		return nil, err
	}

	if err := r.db.Table("affiliate_income").
		Select("sport, casino, slot, lottery, p2p, financial").
		Where("user_id = ?", userId).
		Scan(&income).
		Limit(1).
		Error; err != nil {
		return nil, nil
	}

	var data model.AfCommissionUser
	data.Commission = commission
	data.Income = income

	return &data, nil
}

func (r repo) GetAffMember(req model.AffMemberListRequest) ([]model.AffMember, int64, error) {

	// todo : use summary table จะได้ลบข้อมูลได้ หรือมันเป็นต่อคนต่อวันอยู่แล้ว ???

	if req.RefBy == nil || *req.RefBy == 0 {
		return nil, 0, errors.New("INVALID_REF_BY")
	}

	var list []model.AffMember
	var total int64

	count := r.db.Table("user_affiliate_income AS tb_logs").Select("tb_logs.user_id")
	count = count.Joins("LEFT JOIN user AS tb_user ON tb_user.id = tb_logs.user_id")
	count = count.Where("tb_user.ref_by = ?", req.RefBy)
	if req.From != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.From)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("tb_logs.statement_date >= ? ", startDateAtBkk)
	}
	if req.To != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.To)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("tb_logs.statement_date <= ?", endDateAtBkk)
	}
	// count = count.Group("tb_logs.user_id")
	if err := count.
		// Where("tb_user.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, 0, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_logs.user_id AS id, SUM(tb_logs.turn_total) AS play_balance, SUM(tb_logs.commission_total) AS received_balance"
		// selectedFields += ", SUM(tb_logs.commission_sport) AS commission_sport, SUM(tb_logs.commission_casino) AS commission_casino, SUM(tb_logs.commission_game) AS commission_game"
		// selectedFields += ", SUM(tb_logs.commission_lottery) AS commission_lottery, SUM(tb_logs.commission_p2p) AS commission_p2p, SUM(tb_logs.commission_financial) AS commission_financial"
		query := r.db.Table("user_affiliate_income AS tb_logs")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN user AS tb_user ON tb_user.id = tb_logs.user_id")
		query = query.Where("tb_user.ref_by = ?", req.RefBy)
		if req.From != "" {
			startDateAtBkk, err := r.ParseBodBkk(req.From)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("tb_logs.statement_date >= ? ", startDateAtBkk)
		}
		if req.To != "" {
			endDateAtBkk, err := r.ParseEodBkk(req.To)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("tb_logs.statement_date <= ?", endDateAtBkk)
		}
		query = query.Group("tb_logs.user_id")
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}

	// Append member_code
	userIds := []int64{}
	for _, u := range list {
		userIds = append(userIds, u.ID)
	}

	// select
	if len(userIds) > 0 {
		// SELECT //
		var userList []model.User
		selectedFields := "tb_user.id AS id, tb_user.member_code AS member_code"
		query := r.db.Table("user AS tb_user")
		query = query.Select(selectedFields)
		query = query.Where("tb_user.id IN (?)", userIds)
		if err := query.Scan(&userList).Error; err != nil {
			return nil, 0, err
		}

		for i, u := range list {
			for _, m := range userList {
				if u.ID == m.Id {
					list[i].MemberCode = *m.MemberCode
					break
				}
			}
		}
	}

	return list, total, nil
}

func (r repo) GetActiveAffMemberByLevel(req model.AffMemberListRequest) ([]model.AffMemberResponse, int64, error) {

	// todo : use summary table จะได้ลบข้อมูลได้ หรือมันเป็นต่อคนต่อวันอยู่แล้ว ???

	// แสดงเฉพาะคนที่มียอดรายได้ ???
	if req.RefBy == nil || *req.RefBy == 0 {
		return nil, 0, errors.New("INVALID_REF_BY")
	}

	var list []model.AffMemberResponse
	var total int64

	count := r.db.Table("user_affiliate_income AS tb_logs").Select("tb_logs.user_id")
	count = count.Joins("INNER JOIN affiliate_level AS tb_level ON tb_level.user_id = tb_logs.user_id")
	count = count.Joins("INNER JOIN user AS tb_user ON tb_user.id = tb_level.user_id")
	count = count.Where("tb_level.upline_id = ?", req.RefBy)
	if req.Level != nil {
		count = count.Where("tb_level.level = ?", req.Level)
	}
	if req.Search != "" {
		searchText := fmt.Sprintf("%%%s%%", req.Search)
		count = count.Where("tb_user.member_code LIKE ?", searchText)
	}
	if req.From != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.From)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("tb_logs.statement_date >= ? ", startDateAtBkk)
	}
	if req.To != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.To)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("tb_logs.statement_date <= ?", endDateAtBkk)
	}
	// count = count.Group("tb_logs.user_id")
	if err := count.
		// Where("tb_user.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, 0, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_logs.user_id AS id, SUM(tb_logs.turn_total) AS play_balance, SUM(tb_logs.commission_total) AS received_balance"
		// selectedFields += ", SUM(tb_logs.commission_sport) AS commission_sport, SUM(tb_logs.commission_casino) AS commission_casino, SUM(tb_logs.commission_game) AS commission_game"
		// selectedFields += ", SUM(tb_logs.commission_lottery) AS commission_lottery, SUM(tb_logs.commission_p2p) AS commission_p2p, SUM(tb_logs.commission_financial) AS commission_financial"

		query := r.db.Table("user_affiliate_income AS tb_logs")
		query = query.Select(selectedFields)
		query = query.Joins("INNER JOIN affiliate_level AS tb_level ON tb_level.user_id = tb_logs.user_id")
		query = query.Joins("INNER JOIN user AS tb_user ON tb_user.id = tb_level.user_id")
		query = query.Where("tb_level.upline_id = ?", req.RefBy)
		if req.Level != nil {
			query = query.Where("tb_level.level = ?", req.Level)
		}
		if req.Search != "" {
			searchText := fmt.Sprintf("%%%s%%", req.Search)
			query = query.Where("tb_user.member_code LIKE ?", searchText)
		}
		if req.From != "" {
			startDateAtBkk, err := r.ParseBodBkk(req.From)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("tb_logs.statement_date >= ? ", startDateAtBkk)
		}
		if req.To != "" {
			endDateAtBkk, err := r.ParseEodBkk(req.To)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("tb_logs.statement_date <= ?", endDateAtBkk)
		}
		query = query.Group("tb_logs.user_id")
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}

	// Append member_code [OK_ON_PAGING]
	userIds := []int64{}
	for _, u := range list {
		userIds = append(userIds, u.Id)
	}

	// select
	if len(userIds) > 0 {
		// SELECT //
		var userList []struct {
			Id         int64
			MemberCode *string
			Level      int
		}
		selectedFields := "tb_user.id AS id, tb_user.member_code AS member_code, tb_level.level AS level"
		query := r.db.Table("user AS tb_user")
		query = query.Select(selectedFields)
		query = query.Joins("INNER JOIN affiliate_level AS tb_level ON tb_level.user_id = tb_user.id")
		query = query.Where("tb_level.upline_id = ?", req.RefBy)
		query = query.Where("tb_user.id IN (?)", userIds)

		if err := query.Scan(&userList).Error; err != nil {
			return nil, 0, err
		}
		// [OK_ON_PAGING]
		for i, u := range list {
			for _, m := range userList {
				if u.Id == m.Id {
					list[i].MemberCode = *m.MemberCode
					list[i].Level = m.Level
					break
				}
			}
		}
	}

	return list, total, nil
}

func (r repo) GetPlayLog(MemberCode []string) ([]model.AffMember, error) {

	var data []model.AffMember

	selectedFields := "pl.turn_total AS play_balance, user.member_code AS member_code"

	query := r.db.Table("affiliate")
	query = query.Select(selectedFields)
	query = query.Joins("LEFT JOIN affiliate_income ai ON ai.user_id = affiliate.user_id")
	query = query.Joins("LEFT JOIN play_log pl ON pl.user_id = affiliate.user_id")
	query = query.Joins("LEFT JOIN user ON user.id = affiliate.user_id")
	query = query.Where("user.member_code IS NOT NULL")

	// Construct the query dynamically for each member code
	for _, member := range MemberCode {
		query = query.Or("user.member_code LIKE ?", member)
	}

	if err := query.
		Group("pl.id,pl.turn_total,user.member_code").
		Find(&data).Error; err != nil {
		return nil, err
	}

	return data, nil
}

func (r repo) GetIncome(req model.AffMemberListRequest) ([]model.AfIncomeList, int64, error) {

	var data []model.AfIncomeList
	var total int64

	count := r.db.Table("affiliate_income").Select("created_at")
	count = count.Where("ref_id = ?", req.RefBy)
	count = count.Group("created_at")
	if req.From != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.From)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("affiliate_income.created_at >= ? ", startDateAtBkk)
	}
	if req.To != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.To)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("affiliate_income.created_at <= ?", endDateAtBkk)
	}
	if err := count.
		Count(&total).
		Order("affiliate_income.created_at desc").Error; err != nil {
		return nil, 0, err
	}

	if total > 0 {

		selectedFields := "affiliate_income.created_at AS created_at"
		selectedFields += ", COALESCE(SUM(affiliate_income.sport + affiliate_income.casino + affiliate_income.slot + affiliate_income.lottery + affiliate_income.p2p + affiliate_income.financial)), 0) AS received_balance"

		query := r.db.Table("affiliate_income")
		query = query.Select(selectedFields)
		query = query.Where("ref_id = ?", req.RefBy)
		query = query.Group("created_at")
		if req.From != "" {
			startDateAtBkk, err := r.ParseBodBkk(req.From)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("affiliate_income.created_at >= ? ", startDateAtBkk)
		}
		if req.To != "" {
			endDateAtBkk, err := r.ParseEodBkk(req.To)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("affiliate_income.created_at <= ?", endDateAtBkk)
		}
		if err := query.Group("affiliate_income.created_at").
			Order("affiliate_income.created_at desc").
			Find(&data).Error; err != nil {
			return nil, 0, err
		}
	}
	return data, total, nil
}

func (r repo) GetUserIncomeWebLogList(req model.UserIncomeWebLogListRequest) (*model.UserIncomeWebLogListResponse, error) {

	var list []model.UserIncomeWebLogResponse
	var total int64
	var totalIncome float64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("affiliate_transaction AS tb_log")
	count = count.Select("tb_log.id")
	count = count.Where("tb_log.income_amount > 0")
	count = count.Where("tb_log.user_id = ?", req.UserId)
	if req.FromDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.FromDate)
		if err != nil {
			return nil, err
		}
		count = count.Where("tb_log.created_at >= ? ", startDateAtBkk)
	}
	if req.ToDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.ToDate)
		if err != nil {
			return nil, err
		}
		count = count.Where("tb_log.created_at <= ? ", endDateAtBkk)
	}
	if req.TypeId != nil {
		count = count.Where("tb_log.type_id = ?", req.TypeId)
	}
	if req.StatusId != nil {
		count = count.Where("tb_log.status_id = ?", req.StatusId)
	}
	if err = count.
		Count(&total).
		Error; err != nil {
		return nil, err
	}

	if total > 0 {
		// SUM total income //
		sql3 := r.db.Table("affiliate_transaction AS tb_log")
		sql3 = sql3.Select("COALESCE(SUM(tb_log.income_amount), 0) AS total_income")
		sql3 = sql3.Where("tb_log.income_amount > 0")
		sql3 = sql3.Where("tb_log.user_id = ?", req.UserId)
		if req.FromDate != "" {
			startDateAtBkk, err := r.ParseBodBkk(req.FromDate)
			if err != nil {
				return nil, err
			}
			sql3 = sql3.Where("tb_log.created_at >= ? ", startDateAtBkk)
		}
		if req.ToDate != "" {
			endDateAtBkk, err := r.ParseEodBkk(req.ToDate)
			if err != nil {
				return nil, err
			}
			sql3 = sql3.Where("tb_log.created_at <= ? ", endDateAtBkk)
		}
		if req.TypeId != nil {
			sql3 = sql3.Where("tb_log.type_id = ?", req.TypeId)
		}
		if req.StatusId != nil {
			sql3 = sql3.Where("tb_log.status_id = ?", req.StatusId)
		}
		if err = sql3.Scan(&totalIncome).
			Error; err != nil {
			return nil, err
		}

		// SELECT //
		selectedFields := "tb_log.id AS id, DATE_FORMAT(CONVERT_TZ(tb_log.created_at, '+00:00', '+07:00'), '%Y-%m-%d') AS create_date"
		selectedFields += ", tb_log.type_id AS type_id, tb_type.description AS type_detail"
		selectedFields += ", tb_log.income_amount AS credit_amount, tb_log.transfer_at AS transfer_at"
		selectedFields += ", tb_log.status_id AS status_id, tb_status.description AS status_detail"
		query := r.db.Table("affiliate_transaction AS tb_log")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN affiliate_transaction_type AS tb_type ON tb_type.id = tb_log.type_id")
		query = query.Joins("LEFT JOIN affiliate_transaction_status AS tb_status ON tb_status.id = tb_log.status_id")
		query = query.Where("tb_log.user_id = ?", req.UserId)
		query = query.Where("tb_log.income_amount > 0")
		if req.FromDate != "" {
			startDateAtBkk, err := r.ParseBodBkk(req.FromDate)
			if err != nil {
				return nil, err
			}
			query = query.Where("tb_log.created_at >= ? ", startDateAtBkk)
		}
		if req.ToDate != "" {
			endDateAtBkk, err := r.ParseEodBkk(req.ToDate)
			if err != nil {
				return nil, err
			}
			query = query.Where("tb_log.created_at <= ? ", endDateAtBkk)
		}
		if req.TypeId != nil {
			query = query.Where("tb_log.type_id = ?", req.TypeId)
		}
		if req.StatusId != nil {
			query = query.Where("tb_log.status_id = ?", req.StatusId)
		}
		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		} else {
			// เรียงวันที่คอลัมน์แรก ล่าสุดขึ้นแสดงก่อน (ปัจจุบันเรียงมั่ว)
			query = query.Order("tb_log.created_at DESC")
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, err
		}
	}
	return &model.UserIncomeWebLogListResponse{
		Message:     "Success",
		List:        list,
		Total:       total,
		TotalIncome: totalIncome,
	}, nil
}

func (r repo) GetCommissionUser(userId int64) (*model.AfSummary, error) {

	var data model.AfSummary

	if err := r.db.Table("user_affiliate").
		Select("commission_current").
		Where("user_id = ?", userId).
		Take(&data).
		Error; err != nil {
		return nil, err
	}
	return &data, nil
}

func (r repo) GetAfRegisterBonusType() ([]model.AfRegisterBonusType, error) {

	var data []model.AfRegisterBonusType

	if err := r.db.Table("register_bonus_type").
		Find(&data).
		Error; err != nil {
		return nil, err
	}

	return data, nil
}

func (r repo) GetAfRegisterBonusOption() ([]model.AfRegisterBonusOption, error) {

	var data []model.AfRegisterBonusOption

	if err := r.db.Table("register_bonus_option").
		Find(&data).
		Error; err != nil {
		return nil, err
	}

	return data, nil
}

func (r repo) GetReport(query model.AfReportQuery) ([]model.AfReport, *int64, error) {

	var list []model.AfReport
	var total int64

	count := r.db.Table("user_affiliate").
		Joins("LEFT JOIN user ON user.id = user_affiliate.user_id")

	if query.MemberCode != "" {
		count.Where("user.member_code Like ?", fmt.Sprintf("%%%s%%", query.MemberCode))
	}

	if query.From != "" && query.To != "" {
		count.Where("user_affiliate.created_at BETWEEN ? AND ?", query.From, query.To)
	}

	if err := count.
		Where("user.member_code IS NOT NULL").
		Where("user.member_code != ?", "wait").
		Count(&total).
		Error; err != nil {
		return nil, nil, err
	}

	if total < 1 {
		return nil, &total, nil
	}

	exec := r.db.Table("user_affiliate").
		Joins("LEFT JOIN user ON user.id = user_affiliate.user_id").
		Select(`
				user.member_code,
				user.fullname,
				user_affiliate.link_click_total,
				user_affiliate.commission_total,
				user_affiliate.bonus_share_total,
				user_affiliate.commission_current,
				user_affiliate.user_id
			`)

	if query.MemberCode != "" {
		exec.Where("user.member_code Like ?", fmt.Sprintf("%%%s%%", query.MemberCode))
	}

	if query.From != "" && query.To != "" {
		exec.Where("user_affiliate.created_at BETWEEN ? AND ?", query.From, query.To)
	}

	if err := exec.
		Where("user.member_code IS NOT NULL").
		Where("user.member_code != ?", "wait").
		Limit(query.Limit).
		Offset(query.Limit * query.Page).
		Scan(&list).
		Order("user_affiliate.created_at desc").
		Error; err != nil {
		return nil, nil, err
	}

	userIds := []int64{}
	for _, i := range list {

		// check userIds is duplicate
		isDuplicate := false
		for _, j := range userIds {
			if i.UserId == j {
				isDuplicate = true
				break
			}
		}

		if isDuplicate {
			continue
		}

		userIds = append(userIds, i.UserId)
	}

	memberList := []model.AfMemberTotal{}

	if err := r.db.Table("affiliate").
		Select("COUNT(id) AS total, ref_id AS user_id").
		Where("ref_id IN ?", userIds).
		Group("ref_id").
		Scan(&memberList).
		Error; err != nil {
		return nil, nil, err
	}

	for i, u := range list {
		for _, m := range memberList {
			if u.UserId == m.UserId {
				list[i].MemberTotal = m.Total
			}
		}
	}

	memberDepositList := []model.AfMemberDeoisitTotal{}

	if err := r.db.Table("user_transaction").
		Select("COUNT(id) AS total, user_id").
		Where("user_id IN ?", userIds).
		Where("type_id = ?", model.CREDIT_TYPE_AFFILIATE_INCOME).
		Group("user_id").
		Scan(&memberDepositList).
		Error; err != nil {
		return nil, nil, err
	}

	for i, u := range list {
		for _, m := range memberDepositList {
			if u.UserId == m.UserId {
				list[i].MemberDepositTotal += m.Total
			}
		}
	}

	tranList := []model.AfReportTran{}

	if err := r.db.Table("user_transaction").
		Select("credit_amount").
		Where("user_id IN ?", userIds).
		Find(&tranList).
		Error; err != nil {
		return nil, nil, err
	}

	for i, u := range list {
		for _, t := range tranList {
			if u.UserId == t.UserId {
				list[i].WithdrawTotal += t.CreditAmount
			}
		}
	}

	return list, &total, nil
}

func (r repo) CreateAffiliate(data model.Affiliate) error {

	tx := r.db.Begin()

	if err := tx.Table("affiliate").
		Create(&data).
		Error; err != nil {
		tx.Rollback()
		return err
	}

	var obj = map[string]interface{}{
		"commission_total":     0.00,
		"commission_current":   0.00,
		"first_deposit_bonus":  0.00,
		"link_click_total":     0,
		"member_total":         0,
		"member_deposit_total": 0,
		"user_id":              data.UserId,
	}

	if err := tx.Table("user_affiliate").
		Create(obj).
		Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}

	return nil
}

func (r repo) UpdateCommission(updateBody model.AfCommissionUpdateRequest) error {

	id := int64(0)

	if err := r.db.Table("affiliate_commission").
		Select("id").
		Take(&id).
		Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// Create
			var createBody model.AfCommissionCreateBody
			if updateBody.RegisterBonusTypeId != nil {
				createBody.RegisterBonusTypeId = *updateBody.RegisterBonusTypeId
			}
			if updateBody.RegisterBonusOptionId != nil {
				createBody.RegisterBonusOptionId = *updateBody.RegisterBonusOptionId
			}
			if err := r.db.Table("affiliate_commission").
				Create(createBody).
				Error; err != nil {
				return err
			}
			id = createBody.Id
		} else {
			return err
		}
	}

	// ทำไมต้องดัก ??? && data.RegisterBonusTypeId != 0 && data.RegisterBonusOptionId != 0
	if id != 0 {
		if err := r.db.Table("affiliate_commission").
			Where("id = ?", id).
			Updates(&updateBody).
			Error; err != nil {
			return err
		}
	}

	afCommissionResponse = nil

	return nil
}

func (r repo) UpdateTotalMemberFirstDeposit(refId int64) error {

	if refId == 0 {
		return nil
	}

	// test
	if err := r.IncreaseMemberFirstDeposit(0, refId); err != nil {
		return err
	}

	var total_count int64
	if err := r.db.Table("user").
		Select("COUNT(user.id)").
		Where("user.ref_by = ?", refId).
		Count(&total_count).
		Error; err != nil {
		return err
	}

	// นับใหม่ทุกครั้ง
	var total_member_count int64
	if err := r.db.Table("user_first_deposit").
		Select("COUNT(user_first_deposit.id)").
		Joins("INNER JOIN user ON user.id = user_first_deposit.user_id").
		Where("user.ref_by = ?", refId).
		Count(&total_member_count).
		Error; err != nil {
		return err
	}

	// Sum Total AFF Income
	obj := map[string]interface{}{}
	// [20241007] นับแยกเพราะตรงนี้จะมีเงื่อนไข setting > 0 =>	obj["member_deposit_total"] = gorm.Expr("member_deposit_total + ?", 1)
	// กับ userAfObj["member_total"] = gorm.Expr("member_total + ?", 1)
	// นับใหม่ทุกครั้ง
	obj["member_total"] = total_count
	obj["member_deposit_total"] = total_member_count
	if err := r.db.Table("user_affiliate").
		Where("user_id = ?", refId).
		Updates(obj).
		Error; err != nil {
		return err
	}

	return nil
}

func (r repo) IncreaseMemberFirstDeposit(userId, refId int64) error {

	if refId == 0 {
		return nil
	}

	// Sum Total AFF Income
	obj := map[string]interface{}{}
	// [20241007] นับแยกเพราะตรงนี้จะมีเงื่อนไข setting > 0 =>	obj["member_deposit_total"] = gorm.Expr("member_deposit_total + ?", 1)
	// นับใหม่ทุกครั้ง
	obj["member_deposit_total"] = gorm.Expr("member_deposit_total + ?", 1)
	if err := r.db.Table("user_affiliate").
		Where("user_id = ?", refId).
		Updates(obj).
		Error; err != nil {
		return err
	}

	return nil
}

func (r repo) UpdateCommissionFirstDeposit(userId, refId int64, amount float64) error {

	if refId == 0 {
		return nil
	}

	// CONFIG-LIMIT-ALL-AFFCOMMISSION
	afCommissionSetting, err := r.GetCommissionSetting()
	if err != nil {
		return err
	}
	if afCommissionSetting.MaxCommission > 0 {
		refUserAffiliate, err := r.GetUserAffiliate(refId)
		if err != nil {
			return err
		}
		if (refUserAffiliate.TotalWithdraw + refUserAffiliate.CommissionCurrent) >= afCommissionSetting.MaxCommission {
			amount = 0 // No Commission
		} else {
			// 10 - (0 + 0) = 10
			canGetAmount := afCommissionSetting.MaxCommission - (refUserAffiliate.TotalWithdraw + refUserAffiliate.CommissionCurrent)
			if canGetAmount > 0 {
				if amount > canGetAmount {
					amount = canGetAmount
				}
			} else {
				amount = 0 // NO MORE
			}
		}
	}

	tx := r.db.Begin()

	// Create Register Bonus Transaction
	var createTransactionBody model.AffTransactionCreateBody
	createTransactionBody.UserId = refId
	createTransactionBody.DailyKey = fmt.Sprintf("U%dREF%d", userId, refId)
	createTransactionBody.DownlineId = userId
	createTransactionBody.IncomeAmount = amount
	createTransactionBody.TypeId = model.AFF_TRANSACTION_TYPE_FIRST_DEPOSIT
	createTransactionBody.StatusId = model.AFF_TRANSACTION_STATUS_PENDING
	if err := tx.Table("affiliate_transaction").Create(&createTransactionBody).Error; err != nil {
		tx.Rollback()
		return err
	}
	// Sum Total AFF Income
	obj := map[string]interface{}{}
	obj["commission_total"] = gorm.Expr("commission_total + ?", amount)
	obj["commission_current"] = gorm.Expr("commission_current + ?", amount)
	obj["first_deposit_bonus"] = gorm.Expr("first_deposit_bonus + ?", amount)
	// [20241007] นับแยกเพราะตรงนี้จะมีเงื่อนไข setting > 0 => obj["member_deposit_total"] = gorm.Expr("member_deposit_total + ?", 1)
	if err := tx.Table("user_affiliate").
		Where("user_id = ?", refId).
		Updates(obj).
		Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}
	return nil
}

func (r repo) WithdrawTotalCommission(userId int64, withdrawAmount float64) error {

	updateBody := map[string]interface{}{}
	updateBody["commission_current"] = gorm.Expr("commission_current - ?", withdrawAmount)
	updateBody["total_withdraw"] = gorm.Expr("total_withdraw + ?", withdrawAmount)

	if err := r.db.Table("user_affiliate").
		Where("user_id = ?", userId).
		Updates(updateBody).
		Error; err != nil {
		return err
	}
	return nil
}

func (r repo) IncreaseLinkClick(userId int64) error {

	if err := r.db.Table("user_affiliate").
		Where("user_id = ?", userId).
		Update("link_click_total", gorm.Expr("link_click_total + ?", 1)).
		Error; err != nil {
		return err
	}

	return nil
}

func (r repo) GetUserAffiliateMemberList(req model.AffiliateMemberListRequest) ([]model.AffiliateMemberResponse, int64, error) {

	var list []model.AffiliateMemberResponse
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("user AS users")
	count = count.Select("users.id")
	count = count.Where("users.ref_by = ?", req.RefUserId)
	if req.RegisterFromDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.RegisterFromDate)
		if err != nil {
			return nil, total, err
		}
		count = count.Where("users.created_at >= ? ", startDateAtBkk)
	}
	if req.RegisterToDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.RegisterToDate)
		if err != nil {
			return nil, total, err
		}
		count = count.Where("users.created_at <= ?", endDateAtBkk)
	}
	if req.Search != "" {
		searchText := fmt.Sprintf("%%%s%%", req.Search)
		count = count.Where(r.db.Where("users.member_code LIKE ?", searchText).Or("users.username LIKE ?", searchText).Or("users.fullname LIKE ?", searchText))
	}

	if err = count.
		Where("users.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "users.ref_by AS ref_user_id, users.id AS user_id, users.member_code AS member_code, users.username AS username, users.fullname AS user_fullname"
		selectedFields += ", users.credit AS current_credit_balance, users.created_at AS register_at"
		query := r.db.Table("user AS users")
		query = query.Select(selectedFields)
		// WHERE //
		query = query.Where("users.ref_by = ?", req.RefUserId)
		if req.RegisterFromDate != "" {
			startDateAtBkk, err := r.ParseBodBkk(req.RegisterFromDate)
			if err != nil {
				return nil, total, err
			}
			query = query.Where("users.created_at >= ? ", startDateAtBkk)
		}
		if req.RegisterToDate != "" {
			endDateAtBkk, err := r.ParseEodBkk(req.RegisterToDate)
			if err != nil {
				return nil, total, err
			}
			query = query.Where("users.created_at <= ?", endDateAtBkk)
		}
		if req.Search != "" {
			searchText := fmt.Sprintf("%%%s%%", req.Search)
			query = query.Where(r.db.Where("users.member_code LIKE ?", searchText).Or("users.username LIKE ?", searchText).Or("users.fullname LIKE ?", searchText))
		}

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Where("users.deleted_at IS NULL").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, 0, err
		}

		// APPEND TOTAL DATA
		userIds := []int64{}
		for _, v := range list {
			userIds = append(userIds, v.UserId)
		}

		// play_log for total_play_amount
		var playLog []struct {
			UserId          int64   `json:"user_id"`
			TotalPlayAmount float64 `json:"total_play_amount"`
		}
		if err = r.db.Table("play_log").
			Select("user_id, SUM(turn_total) AS total_play_amount").
			Where("user_id IN ?", userIds).
			Group("user_id").
			Scan(&playLog).
			Error; err != nil {
			return nil, 0, err
		}

		// user_transaction for total_deposit_amount
		var userTransaction []struct {
			UserId             int64   `json:"user_id"`
			TotalDepositAmount float64 `json:"total_deposit_amount"`
		}
		if err = r.db.Table("user_transaction").
			Select("user_id, SUM(credit_amount) AS total_deposit_amount").
			Where("user_id IN ?", userIds).
			Where("type_id = ?", model.CREDIT_TYPE_DEPOSIT).
			Where("is_show = ?", true).
			Where("removed_at IS NULL").
			Group("user_id").
			Scan(&userTransaction).
			Error; err != nil {
			return nil, 0, err
		}

		// MERGE DATA BY USER_ID
		for index, item := range list {
			for _, v2 := range playLog {
				if item.UserId == v2.UserId {
					list[index].TotalPlayAmount = v2.TotalPlayAmount
					break
				}
			}
			for _, v3 := range userTransaction {
				if item.UserId == v3.UserId {
					list[index].TotalDepositAmount = v3.TotalDepositAmount
					break
				}
			}
		}

	}
	return list, total, nil
}

func (r repo) GetAffiliateUserList(req model.AffiliateUserListRequest) ([]model.AffiliateUserResponse, int64, error) {

	var list []model.AffiliateUserResponse
	var total int64

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return nil, 0, err
	}

	// Mink.J — Today at 5:27 PM
	// รายงานลิงก์รับทรัพย์ ใส่ข้อมูลดังนี้
	// จำนวนคลิก หมายถึง จำนวนที่ (คน) กดลิงก์เข้ามาต่อคลิก เอาทศนิยมออก
	// จำนวนแนะนำ หมายถึง จำนวนที่ (คน) ที่ทำการสมัครมาจากลิงก์ เอาทศนิยมออก
	// จำนวนฝาก หมายถึง ยอดเงิน ของลูกยูสจากการฝากครั้งแรก
	// รายได้ค่าคอม หมายถึง รายได้คอมมิชัน (ที่คิดจาก % ยอดเทิร์น) รวมทุกประเภท
	// โบนัสแชร์ลิงก์ หมายถึง รายได้สมัคร รับรายได้เลย
	// โบนัสปิดลูกค้า หมายถึง โบนัสที่หัวลิงก์ได้ จากการฝากครั้งแรกของลูกลิ้งก์ เช่นลูกลิงก์ฝาก 100 เราได้ 20 (20คือรายได้เรา)
	// รายได้รวม หมายถึง รายได้รวมทั้งหมดที่หัวลิงก์ได้ (รายได้ลูกลิงก์สมัครรับรายได้เลย, รายได้จากลูกลิงก์สมัครฝากครั้งแรก และรายได้ค่าคอม)
	// รายได้ที่ถอน หมายถึง รายได้รวมที่หัวลิงก์ได้รับเข้ากระเป๋าหลักไปแล้วทั้งหมด
	// รายได้คงเหลือ หมายถึง รายได้รวมที่หัวลิงก์ยังไม่ได้โยกเข้ากระเป๋าหลักหน้าบ้าน

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("user AS tb_user")
	count = count.Select("tb_user.id")
	count = count.Where("tb_user.user_type_id = ?", model.USER_TYPE_AFFILIATE)
	if req.MemberCode != "" {
		searchText := fmt.Sprintf("%%%s%%", req.MemberCode)
		count = count.Where("tb_user.member_code LIKE ?", searchText)
	}
	if err = count.
		Where("tb_user.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_user.id AS user_id, tb_user.member_code AS member_code, tb_user.username AS username, tb_user.fullname AS user_fullname"
		query := r.db.Table("user AS tb_user")
		query = query.Select(selectedFields)
		query = query.Where("tb_user.user_type_id = ?", model.USER_TYPE_AFFILIATE)
		if req.MemberCode != "" {
			searchText := fmt.Sprintf("%%%s%%", req.MemberCode)
			query = query.Where("tb_user.member_code LIKE ?", searchText)
		}
		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Where("tb_user.deleted_at IS NULL").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, 0, err
		}

		// APPEND TOTAL DATA
		refIds := []int64{}
		for _, v := range list {
			refIds = append(refIds, v.UserId)
		}
		if len(refIds) == 0 {
			return list, total, nil
		}

		var linkLog []struct {
			RefBy      int64 `json:"ref_by"`
			TotalClick int64 `json:"total_click"`
		}

		sql := r.db.Table("affiliate_link_click").
			Select("ref_by AS ref_by, COUNT(*) AS total_click").
			Where("ref_by IN (?)", refIds).
			Group("ref_by")

		if dateType.DateFrom != "" {
			startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
			if err != nil {
				return nil, 0, err
			}
			sql = sql.Where("created_at >= ? ", startDateAtBkk)
		}
		if dateType.DateTo != "" {
			endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
			if err != nil {
				return nil, 0, err
			}
			sql = sql.Where("created_at <=  ?", endDateAtBkk)
		}
		if err = sql.Scan(&linkLog).
			Error; err != nil {
			return nil, 0, err
		}

		// user that ref_by me and register(created_at) between start and end date
		var userRegisterLog []struct {
			RefBy          int64 `json:"ref_by"`
			RecommendTotal int64 `json:"recommendTotal"`
		}

		selectedFields1 := "ref_by AS ref_by, COUNT(*) AS recommend_total"
		query1 := r.db.Table("user").
			Select(selectedFields1).
			Where("ref_by IN (?)", refIds).
			Where("deleted_at IS NULL").
			Group("ref_by")
		if dateType.DateFrom != "" {
			startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
			if err != nil {
				return nil, 0, err
			}
			query1 = query1.Where("created_at >= ? ", startDateAtBkk)
		}
		if dateType.DateTo != "" {
			endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
			if err != nil {
				return nil, 0, err
			}
			query1 = query1.Where("created_at <=  ?", endDateAtBkk)
		}
		if err = query1.Scan(&userRegisterLog).Error; err != nil {
			return nil, 0, err
		}

		// user that have user_transaction Deposit and confirm_at between start and end date
		var userDepositLog []struct {
			RefBy        int64   `json:"ref_by"`
			TotalDeposit float64 `json:"total_deposit"`
		}
		selectedFields2 := "tb_user.ref_by AS ref_by, SUM(tb_log.credit_amount) AS total_deposit"
		query2 := r.db.Table("user_transaction AS tb_log").
			Select(selectedFields2).
			Joins("INNER JOIN user AS tb_user ON tb_user.id = tb_log.user_id").
			Where("tb_log.type_id = ?", model.CREDIT_TYPE_DEPOSIT).
			Where("tb_user.ref_by IN (?)", refIds).
			Where("tb_log.is_show = ?", true).
			Where("tb_log.removed_at IS NULL").
			Group("tb_user.ref_by")
		if dateType.DateFrom != "" {
			startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
			if err != nil {
				return nil, 0, err
			}
			query2 = query2.Where("tb_log.transfer_at >= ? ", startDateAtBkk)
		}
		if dateType.DateTo != "" {
			endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
			if err != nil {
				return nil, 0, err
			}
			query2 = query2.Where("tb_log.transfer_at <=  ?", endDateAtBkk)
		}
		if err = query2.Scan(&userDepositLog).Error; err != nil {
			return nil, 0, err
		}

		// user that have user_affiliate_income Deposit and confirm_at between start and end date
		var userIncomeLog []struct {
			RefBy              int64   `json:"ref_by"`
			TotalTurnSport     float64 `json:"total_turn_sport"`
			TotalTurnCasino    float64 `json:"total_turn_casino"`
			TotalTurnGame      float64 `json:"total_turn_game"`
			TotalTurnLottery   float64 `json:"total_turn_lottery"`
			TotalTurnP2p       float64 `json:"total_turn_p2p"`
			TotalTurnFinancial float64 `json:"total_turn_financial"`
			TotalCommission    float64 `json:"total_commission"`
		}
		selectedFields3 := "tb_user.ref_by AS ref_by, SUM(tb_log.turn_sport) AS total_turn_sport, SUM(tb_log.turn_casino) AS total_turn_casino, SUM(tb_log.turn_game) AS total_turn_game"
		selectedFields3 += ", SUM(tb_log.turn_lottery) AS total_turn_lottery, SUM(tb_log.turn_p2p) AS total_turn_p2p, SUM(tb_log.turn_financial) AS total_turn_financial"
		selectedFields3 += ", SUM(tb_log.commission_total) AS total_commission"
		query3 := r.db.Table("user_affiliate_income AS tb_log").
			Select(selectedFields3).
			Joins("INNER JOIN user AS tb_user ON tb_user.id = tb_log.user_id").
			Where("tb_user.ref_by IN (?)", refIds).
			Group("tb_user.ref_by")
		if dateType.DateFrom != "" {
			startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
			if err != nil {
				return nil, 0, err
			}
			query3 = query3.Where("tb_log.created_at >= ? ", startDateAtBkk)
		}
		if dateType.DateTo != "" {
			endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
			if err != nil {
				return nil, 0, err
			}
			query3 = query3.Where("tb_log.created_at <=  ?", endDateAtBkk)
		}
		if err = query3.Scan(&userIncomeLog).Error; err != nil {
			return nil, 0, err
		}

		// ref that have affiliate_transaction As โบนัส Type แชร์ลิงค์(1), โบนัส ยอดฝากแรก(2) and created_at between start and end date
		var userTransactionLog []struct {
			RefBy              int64   `json:"ref_by"`
			LinkRegisterAmount float64 `json:"link_register_amount"`
			FirstDepositAmount float64 `json:"first_deposit_amount"`
		}
		selectedFields4 := "tb_log.user_id AS ref_by"
		selectedFields4 += ", SUM(CASE WHEN tb_log.type_id = 1 THEN tb_log.income_amount ELSE 0 END) AS link_register_amount"
		selectedFields4 += ", SUM(CASE WHEN tb_log.type_id = 2 THEN tb_log.income_amount ELSE 0 END) AS first_deposit_amount"
		query4 := r.db.Table("affiliate_transaction AS tb_log").
			Select(selectedFields4).
			Where("tb_log.user_id IN (?)", refIds).
			Group("tb_log.user_id")
		if dateType.DateFrom != "" {
			startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
			if err != nil {
				return nil, 0, err
			}
			query4 = query4.Where("tb_log.created_at >= ? ", startDateAtBkk)
		}
		if dateType.DateTo != "" {
			endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
			if err != nil {
				return nil, 0, err
			}
			query4 = query4.Where("tb_log.created_at <=  ?", endDateAtBkk)
		}
		if err = query4.Scan(&userTransactionLog).Error; err != nil {
			return nil, 0, err
		}

		// กรอกข้อมูลลิ้งรับทรัพย์
		// get total income transaction without date
		// Withdraw = 3,4 **pending confirm is withdrawed
		// income_balance = 1 ** only pending รอกดรับ
		// Where("tb_log.status_id != ?", model.AFF_TRANSACTION_STATUS_EXPIRED). เอาออกเพราะต้องรวมทุกยอด
		var userTotalIncome []struct {
			UserId        int64   `json:"user_id"`
			TotalIncome   float64 `json:"total_income"`
			TotalWithdraw float64 `json:"total_withdraw"`
			IncomeBalance float64 `json:"income_balance"`
		}
		selectedFields5 := "tb_log.user_id AS user_id, SUM(tb_log.income_amount) AS total_income, SUM(CASE WHEN tb_log.status_id IN (3,4) THEN tb_log.income_amount ELSE 0 END) AS total_withdraw"
		selectedFields5 += ", SUM(CASE WHEN tb_log.status_id IN (1) THEN tb_log.income_amount ELSE 0 END) AS income_balance"
		query5 := r.db.Table("affiliate_transaction AS tb_log").
			Select(selectedFields5).
			Where("tb_log.user_id IN ?", refIds)

		if dateType.DateFrom != "" {
			startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
			if err != nil {
				return nil, 0, err
			}
			query5 = query5.Where("tb_log.created_at >= ? ", startDateAtBkk)
		}
		if dateType.DateTo != "" {
			endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
			if err != nil {
				return nil, 0, err
			}
			query5 = query5.Where("tb_log.created_at <=  ?", endDateAtBkk)
		}

		if err = query5.Group("tb_log.user_id").Scan(&userTotalIncome).Error; err != nil {
			return nil, 0, err
		}

		var userIncomeLog2 []struct {
			RefBy                    int64   `json:"ref_by"`
			PromotionReturnLossTotal float64 `json:"promotionReturnLossTotal"` // โบนัสคืนยอดเสีย // มี
			LuckyWheelTotal          float64 `json:"luckyWheelTotal"`          // กงล้อ   // มี
			CouponTotal              float64 `json:"couponTotal"`              // คูปอง   // มี
			CreditBonusTotal         float64 `json:"creditBonusTotal"`         // โบนัสเครดิต  // มี
			PromotionWebTotal        float64 `json:"promotionWebTotal"`        // โปรโมชั่น   //มี
			PromotionReturnTurnTotal float64 `json:"promotionReturnTurnTotal"` // โบนัสคืนยอดเทิร์น // มี
			AllBonusTotal            float64 `json:"allBonusTotal"`
		}

		// user_income_log  use user_id join user_income_log confirm P.Tula Discord 20240715
		selectedFields6 := "tb_user.ref_by AS ref_by"
		selectedFields6 += ", SUM(CASE WHEN income_logs.type_id = 4 THEN income_logs.bonus_amount ELSE 0 END) AS promotion_return_loss_total"
		selectedFields6 += ", SUM(CASE WHEN income_logs.type_id = 9 THEN income_logs.bonus_amount ELSE 0 END) AS lucky_wheel_total"
		selectedFields6 += ", SUM(CASE WHEN income_logs.type_id = 11 THEN income_logs.bonus_amount ELSE 0 END) AS coupon_total"
		selectedFields6 += ", SUM(CASE WHEN income_logs.type_id = 3 THEN income_logs.bonus_amount ELSE 0 END) AS credit_bonus_total"
		selectedFields6 += ", SUM(CASE WHEN income_logs.type_id = 10 THEN income_logs.bonus_amount ELSE 0 END) AS promotion_web_total"
		selectedFields6 += ", SUM(CASE WHEN income_logs.type_id = 13 THEN income_logs.bonus_amount ELSE 0 END) AS promotion_return_turn_total"

		query6 := r.db.Table("user_transaction AS income_logs").
			Select(selectedFields6).
			Joins("INNER JOIN user AS tb_user ON tb_user.id = income_logs.user_id").
			Where("tb_user.ref_by IN (?)", refIds).
			Group("tb_user.ref_by")

		if dateType.DateFrom != "" {
			startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
			if err != nil {
				return nil, 0, err
			}
			query6 = query6.Where("income_logs.transfer_at >= ? ", startDateAtBkk)
		}
		if dateType.DateTo != "" {
			endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
			if err != nil {
				return nil, 0, err
			}
			query6 = query6.Where("income_logs.transfer_at <=  ?", endDateAtBkk)
		}

		if err := query6.Scan(&userIncomeLog2).Error; err != nil {
			return nil, 0, err
		}

		// MERGE DATA BY USER_ID
		for index, item := range list {
			for _, refData := range linkLog {
				if item.UserId == refData.RefBy {
					list[index].LinkClickTotal = refData.TotalClick
					break
				}
			}
			for _, refData := range userRegisterLog {
				if item.UserId == refData.RefBy {
					list[index].MemberCount = refData.RecommendTotal
					break
				}
			}
			for _, refData := range userDepositLog {
				if item.UserId == refData.RefBy {
					list[index].MemberDepositAmount = refData.TotalDeposit
					break
				}
			}
			for _, refData := range userIncomeLog {
				if item.UserId == refData.RefBy {
					list[index].TotalTurnSport = refData.TotalTurnSport
					list[index].TotalTurnCasino = refData.TotalTurnCasino
					list[index].TotalTurnGame = refData.TotalTurnGame
					list[index].TotalTurnLottery = refData.TotalTurnLottery
					list[index].TotalTurnP2p = refData.TotalTurnP2p
					list[index].TotalTurnFinancial = refData.TotalTurnFinancial
					list[index].TotalCommission = refData.TotalCommission
					break
				}
			}
			for _, refData := range userTransactionLog {
				if item.UserId == refData.RefBy {
					list[index].LinkRegisterAmount = refData.LinkRegisterAmount
					list[index].FirstDepositAmount = refData.FirstDepositAmount
					break
				}
			}
			// ไม่ได้กรองตามวัน Total = ALL DATE
			// list[index].TotalIncome = list[index].TotalCommission + list[index].LinkRegisterAmount + list[index].FirstDepositAmount
			for _, v3 := range userTotalIncome {
				if item.UserId == v3.UserId {
					list[index].TotalIncome = v3.TotalIncome
					list[index].TotalIncomeWithdraw = v3.TotalWithdraw
					list[index].IncomeBalance = v3.IncomeBalance
					break
				}
			}

			for _, v4 := range userIncomeLog2 {
				if item.UserId == v4.RefBy {
					list[index].PromotionReturnLossTotal = v4.PromotionReturnLossTotal
					list[index].LuckyWheelTotal = v4.LuckyWheelTotal
					list[index].CouponTotal = v4.CouponTotal
					list[index].CreditBonusTotal = v4.CreditBonusTotal
					list[index].PromotionWebTotal = v4.PromotionWebTotal
					list[index].PromotionReturnTurnTotal = v4.PromotionReturnTurnTotal
					list[index].AllBonusTotal = v4.PromotionReturnLossTotal + v4.LuckyWheelTotal + v4.CouponTotal + v4.CreditBonusTotal + v4.PromotionWebTotal + v4.PromotionReturnTurnTotal
					break
				}
			}
		}

	}
	return list, total, nil
}

func (r repo) getTotalAffiliateUserList(req model.AffiliateUserListRequest) ([]model.AffiliateUserResponse, int64, error) {

	// ดึงทีละเยอะๆ จัดกลุ่มเอาไว้ทำ Report
	var list []model.AffiliateUserResponse
	var total int64

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return nil, 0, err
	}

	// Mink.J — Today at 5:27 PM
	// รายงานลิงก์รับทรัพย์ ใส่ข้อมูลดังนี้
	// จำนวนคลิก หมายถึง จำนวนที่ (คน) กดลิงก์เข้ามาต่อคลิก เอาทศนิยมออก
	// จำนวนแนะนำ หมายถึง จำนวนที่ (คน) ที่ทำการสมัครมาจากลิงก์ เอาทศนิยมออก
	// จำนวนฝาก หมายถึง ยอดเงิน ของลูกยูสจากการฝากครั้งแรก
	// รายได้ค่าคอม หมายถึง รายได้คอมมิชัน (ที่คิดจาก % ยอดเทิร์น) รวมทุกประเภท
	// โบนัสแชร์ลิงก์ หมายถึง รายได้สมัคร รับรายได้เลย
	// โบนัสปิดลูกค้า หมายถึง โบนัสที่หัวลิงก์ได้ จากการฝากครั้งแรกของลูกลิ้งก์ เช่นลูกลิงก์ฝาก 100 เราได้ 20 (20คือรายได้เรา)
	// รายได้รวม หมายถึง รายได้รวมทั้งหมดที่หัวลิงก์ได้ (รายได้ลูกลิงก์สมัครรับรายได้เลย, รายได้จากลูกลิงก์สมัครฝากครั้งแรก และรายได้ค่าคอม)
	// รายได้ที่ถอน หมายถึง รายได้รวมที่หัวลิงก์ได้รับเข้ากระเป๋าหลักไปแล้วทั้งหมด
	// รายได้คงเหลือ หมายถึง รายได้รวมที่หัวลิงก์ยังไม่ได้โยกเข้ากระเป๋าหลักหน้าบ้าน

	// SELECT //
	selectedFields := "tb_user.id AS user_id, tb_user.member_code AS member_code, tb_user.username AS username, tb_user.fullname AS user_fullname"
	query := r.db.Table("user AS tb_user")
	query = query.Select(selectedFields)
	query = query.Where("tb_user.user_type_id = ?", model.USER_TYPE_AFFILIATE)
	if req.MemberCode != "" {
		searchText := fmt.Sprintf("%%%s%%", req.MemberCode)
		query = query.Where("tb_user.member_code LIKE ?", searchText)
	}
	// Sort by ANY //
	req.SortCol = strings.TrimSpace(req.SortCol)
	if req.SortCol != "" {
		if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
			req.SortAsc = "DESC"
		} else {
			req.SortAsc = "ASC"
		}
		query = query.Order(req.SortCol + " " + req.SortAsc)
	}
	if req.Limit > 0 {
		query = query.Limit(req.Limit)
	}
	if err = query.
		Where("tb_user.deleted_at IS NULL").
		Offset(req.Page * req.Limit).
		Scan(&list).
		Error; err != nil {
		return nil, 0, err
	}

	// APPEND TOTAL DATA
	refIds := []int64{}
	for _, v := range list {
		refIds = append(refIds, v.UserId)
	}
	if len(refIds) == 0 {
		return list, total, nil
	}

	var linkLog []struct {
		RefBy      int64 `json:"ref_by"`
		TotalClick int64 `json:"total_click"`
	}

	sql := r.db.Table("affiliate_link_click").
		Select("ref_by AS ref_by, COUNT(*) AS total_click").
		Where("ref_by IN (?)", refIds).
		Group("ref_by")

	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return nil, 0, err
		}
		sql = sql.Where("created_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return nil, 0, err
		}
		sql = sql.Where("created_at <=  ?", endDateAtBkk)
	}
	if err = sql.Scan(&linkLog).
		Error; err != nil {
		return nil, 0, err
	}

	// user that ref_by me and register(created_at) between start and end date
	var userRegisterLog []struct {
		RefBy          int64 `json:"ref_by"`
		RecommendTotal int64 `json:"recommendTotal"`
	}

	selectedFields1 := "ref_by AS ref_by, COUNT(*) AS recommend_total"
	query1 := r.db.Table("user").
		Select(selectedFields1).
		Where("ref_by IN (?)", refIds).
		Where("deleted_at IS NULL").
		Group("ref_by")
	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return nil, 0, err
		}
		query1 = query1.Where("created_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return nil, 0, err
		}
		query1 = query1.Where("created_at <=  ?", endDateAtBkk)
	}
	if err = query1.Scan(&userRegisterLog).Error; err != nil {
		return nil, 0, err
	}

	// user that have user_transaction Deposit and confirm_at between start and end date
	var userDepositLog []struct {
		RefBy        int64   `json:"ref_by"`
		TotalDeposit float64 `json:"total_deposit"`
	}
	selectedFields2 := "tb_user.ref_by AS ref_by, SUM(tb_log.credit_amount) AS total_deposit"
	query2 := r.db.Table("user_transaction AS tb_log").
		Select(selectedFields2).
		Joins("INNER JOIN user AS tb_user ON tb_user.id = tb_log.user_id").
		Where("tb_log.type_id = ?", model.CREDIT_TYPE_DEPOSIT).
		Where("tb_user.ref_by IN (?)", refIds).
		Where("tb_log.is_show = ?", true).
		Where("tb_log.removed_at IS NULL").
		Group("tb_user.ref_by")
	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return nil, 0, err
		}
		query2 = query2.Where("tb_log.transfer_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return nil, 0, err
		}
		query2 = query2.Where("tb_log.transfer_at <=  ?", endDateAtBkk)
	}
	if err = query2.Scan(&userDepositLog).Error; err != nil {
		return nil, 0, err
	}

	// user that have user_affiliate_income Deposit and confirm_at between start and end date
	var userIncomeLog []struct {
		RefBy              int64   `json:"ref_by"`
		TotalTurnSport     float64 `json:"total_turn_sport"`
		TotalTurnCasino    float64 `json:"total_turn_casino"`
		TotalTurnGame      float64 `json:"total_turn_game"`
		TotalTurnLottery   float64 `json:"total_turn_lottery"`
		TotalTurnP2p       float64 `json:"total_turn_p2p"`
		TotalTurnFinancial float64 `json:"total_turn_financial"`
		TotalCommission    float64 `json:"total_commission"`
	}
	selectedFields3 := "tb_user.ref_by AS ref_by, SUM(tb_log.turn_sport) AS total_turn_sport, SUM(tb_log.turn_casino) AS total_turn_casino, SUM(tb_log.turn_game) AS total_turn_game"
	selectedFields3 += ", SUM(tb_log.turn_lottery) AS total_turn_lottery, SUM(tb_log.turn_p2p) AS total_turn_p2p, SUM(tb_log.turn_financial) AS total_turn_financial"
	selectedFields3 += ", SUM(tb_log.commission_total) AS total_commission"
	query3 := r.db.Table("user_affiliate_income AS tb_log").
		Select(selectedFields3).
		Joins("INNER JOIN user AS tb_user ON tb_user.id = tb_log.user_id").
		Where("tb_user.ref_by IN (?)", refIds).
		Group("tb_user.ref_by")
	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return nil, 0, err
		}
		query3 = query3.Where("tb_log.created_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return nil, 0, err
		}
		query3 = query3.Where("tb_log.created_at <=  ?", endDateAtBkk)
	}
	if err = query3.Scan(&userIncomeLog).Error; err != nil {
		return nil, 0, err
	}

	// ref that have affiliate_transaction As โบนัส Type แชร์ลิงค์(1), โบนัส ยอดฝากแรก(2) and created_at between start and end date
	var userTransactionLog []struct {
		RefBy              int64   `json:"ref_by"`
		LinkRegisterAmount float64 `json:"link_register_amount"`
		FirstDepositAmount float64 `json:"first_deposit_amount"`
	}
	selectedFields4 := "tb_log.user_id AS ref_by"
	selectedFields4 += ", SUM(CASE WHEN tb_log.type_id = 1 THEN tb_log.income_amount ELSE 0 END) AS link_register_amount"
	selectedFields4 += ", SUM(CASE WHEN tb_log.type_id = 2 THEN tb_log.income_amount ELSE 0 END) AS first_deposit_amount"
	query4 := r.db.Table("affiliate_transaction AS tb_log").
		Select(selectedFields4).
		Where("tb_log.user_id IN (?)", refIds).
		Group("tb_log.user_id")
	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return nil, 0, err
		}
		query4 = query4.Where("tb_log.created_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return nil, 0, err
		}
		query4 = query4.Where("tb_log.created_at <=  ?", endDateAtBkk)
	}
	if err = query4.Scan(&userTransactionLog).Error; err != nil {
		return nil, 0, err
	}

	// กรอกข้อมูลลิ้งรับทรัพย์
	// get total income transaction without date
	// Withdraw = 3,4 **pending confirm is withdrawed
	// income_balance = 1 ** only pending รอกดรับ
	// Where("tb_log.status_id != ?", model.AFF_TRANSACTION_STATUS_EXPIRED). เอาออกเพราะต้องรวมทุกยอด
	var userTotalIncome []struct {
		UserId        int64   `json:"user_id"`
		TotalIncome   float64 `json:"total_income"`
		TotalWithdraw float64 `json:"total_withdraw"`
		IncomeBalance float64 `json:"income_balance"`
	}
	selectedFields5 := "tb_log.user_id AS user_id, SUM(tb_log.income_amount) AS total_income, SUM(CASE WHEN tb_log.status_id IN (3,4) THEN tb_log.income_amount ELSE 0 END) AS total_withdraw"
	selectedFields5 += ", SUM(CASE WHEN tb_log.status_id IN (1) THEN tb_log.income_amount ELSE 0 END) AS income_balance"
	query5 := r.db.Table("affiliate_transaction AS tb_log").
		Select(selectedFields5).
		Where("tb_log.user_id IN ?", refIds)

	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return nil, 0, err
		}
		query5 = query5.Where("tb_log.created_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return nil, 0, err
		}
		query5 = query5.Where("tb_log.created_at <=  ?", endDateAtBkk)
	}

	if err = query5.Group("tb_log.user_id").Scan(&userTotalIncome).Error; err != nil {
		return nil, 0, err
	}

	var userIncomeLog2 []struct {
		RefBy                    int64   `json:"ref_by"`
		PromotionReturnLossTotal float64 `json:"promotionReturnLossTotal"` // โบนัสคืนยอดเสีย // มี
		LuckyWheelTotal          float64 `json:"luckyWheelTotal"`          // กงล้อ   // มี
		CouponTotal              float64 `json:"couponTotal"`              // คูปอง   // มี
		CreditBonusTotal         float64 `json:"creditBonusTotal"`         // โบนัสเครดิต  // มี
		PromotionWebTotal        float64 `json:"promotionWebTotal"`        // โปรโมชั่น   //มี
		PromotionReturnTurnTotal float64 `json:"promotionReturnTurnTotal"` // โบนัสคืนยอดเทิร์น // มี
		AllBonusTotal            float64 `json:"allBonusTotal"`
	}

	// user_income_log  use user_id join user_income_log confirm P.Tula Discord 20240715
	selectedFields6 := "tb_user.ref_by AS ref_by"
	selectedFields6 += ", SUM(CASE WHEN income_logs.type_id = 4 THEN income_logs.bonus_amount ELSE 0 END) AS promotion_return_loss_total"
	selectedFields6 += ", SUM(CASE WHEN income_logs.type_id = 9 THEN income_logs.bonus_amount ELSE 0 END) AS lucky_wheel_total"
	selectedFields6 += ", SUM(CASE WHEN income_logs.type_id = 11 THEN income_logs.bonus_amount ELSE 0 END) AS coupon_total"
	selectedFields6 += ", SUM(CASE WHEN income_logs.type_id = 3 THEN income_logs.bonus_amount ELSE 0 END) AS credit_bonus_total"
	selectedFields6 += ", SUM(CASE WHEN income_logs.type_id = 10 THEN income_logs.bonus_amount ELSE 0 END) AS promotion_web_total"
	selectedFields6 += ", SUM(CASE WHEN income_logs.type_id = 13 THEN income_logs.bonus_amount ELSE 0 END) AS promotion_return_turn_total"

	query6 := r.db.Table("user_transaction AS income_logs").
		Select(selectedFields6).
		Joins("INNER JOIN user AS tb_user ON tb_user.id = income_logs.user_id").
		Where("tb_user.ref_by IN (?)", refIds).
		Group("tb_user.ref_by")

	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return nil, 0, err
		}
		query6 = query6.Where("income_logs.transfer_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return nil, 0, err
		}
		query6 = query6.Where("income_logs.transfer_at <=  ?", endDateAtBkk)
	}

	if err := query6.Scan(&userIncomeLog2).Error; err != nil {
		return nil, 0, err
	}

	// MERGE DATA BY USER_ID
	for index, item := range list {
		for _, refData := range linkLog {
			if item.UserId == refData.RefBy {
				list[index].LinkClickTotal = refData.TotalClick
				break
			}
		}
		for _, refData := range userRegisterLog {
			if item.UserId == refData.RefBy {
				list[index].MemberCount = refData.RecommendTotal
				break
			}
		}
		for _, refData := range userDepositLog {
			if item.UserId == refData.RefBy {
				list[index].MemberDepositAmount = refData.TotalDeposit
				break
			}
		}
		for _, refData := range userIncomeLog {
			if item.UserId == refData.RefBy {
				list[index].TotalTurnSport = refData.TotalTurnSport
				list[index].TotalTurnCasino = refData.TotalTurnCasino
				list[index].TotalTurnGame = refData.TotalTurnGame
				list[index].TotalTurnLottery = refData.TotalTurnLottery
				list[index].TotalTurnP2p = refData.TotalTurnP2p
				list[index].TotalTurnFinancial = refData.TotalTurnFinancial
				list[index].TotalCommission = refData.TotalCommission
				break
			}
		}
		for _, refData := range userTransactionLog {
			if item.UserId == refData.RefBy {
				list[index].LinkRegisterAmount = refData.LinkRegisterAmount
				list[index].FirstDepositAmount = refData.FirstDepositAmount
				break
			}
		}
		// ไม่ได้กรองตามวัน Total = ALL DATE
		// list[index].TotalIncome = list[index].TotalCommission + list[index].LinkRegisterAmount + list[index].FirstDepositAmount
		for _, v3 := range userTotalIncome {
			if item.UserId == v3.UserId {
				list[index].TotalIncome = v3.TotalIncome
				list[index].TotalIncomeWithdraw = v3.TotalWithdraw
				list[index].IncomeBalance = v3.IncomeBalance
				break
			}
		}

		for _, v4 := range userIncomeLog2 {
			if item.UserId == v4.RefBy {
				list[index].PromotionReturnLossTotal = v4.PromotionReturnLossTotal
				list[index].LuckyWheelTotal = v4.LuckyWheelTotal
				list[index].CouponTotal = v4.CouponTotal
				list[index].CreditBonusTotal = v4.CreditBonusTotal
				list[index].PromotionWebTotal = v4.PromotionWebTotal
				list[index].PromotionReturnTurnTotal = v4.PromotionReturnTurnTotal
				list[index].AllBonusTotal = v4.PromotionReturnLossTotal + v4.LuckyWheelTotal + v4.CouponTotal + v4.CreditBonusTotal + v4.PromotionWebTotal + v4.PromotionReturnTurnTotal
				break
			}
		}

	}
	return list, total, nil
}

func (r repo) MakeReportAffiliateUserList(statementDate string) error {

	// var list []model.AffiliateUserResponse
	// var total int64

	// 2024-10-15 GetAffiliateUserList => GetAffiliateActiveUserList
	// สิ่งที่ต้องการ
	// 1.เอารายการยูสที่เป็น 0 ทั้งหมดออก
	// 2.เรียงรายได้ค่าคอมสูงสุดขึ้นก่อน

	// รหัสสมาชิก
	// ชื่อผู้ใช้/ชื่อ
	// จำนวนคลิก
	// จำนวนแนะนำ
	// จำนวนฝาก
	// ยอดเทิร์น Sport
	// ยอดเทิร์น Casino
	// ยอดเทิร์น Game
	// ยอดเทิร์น Lottery
	// ยอดเทิร์น P2P
	// ยอดเทิร์น Financial
	// รายได้(ค่าคอม)
	// สมัครรับรายได้
	// รายได้ยอดฝากครั้งแรก
	// โบนัสโปรโมชั่น
	// โบนัสคืนยอดเสีย
	// โบนัสกงล้อ
	// โบนัสคูปองเงินสด
	// บันทึกแจกโบนัส
	// ยอดจ่ายโบนัสทั้งหมด
	// รายได้รวม (ทั้งหมด)
	// รายได้ที่ถอน (ทั้งหมด)
	// รายได้คงเหลือ (ทั้งหมด)
	// จัดการ

	// from 1 to 30 (this month)
	ofDate, err := time.Parse("2006-01-02", statementDate)
	if err != nil {
		return err
	}

	for page := 0; page < 1000; page++ {
		createList := make(map[string]model.MakeReportAffiliateUserResponse)
		// var updateList []model.MakeReportAffiliateUserResponse
		var query model.AffiliateUserListRequest
		query.FromDate = ofDate.Format("2006-01-02")
		query.ToDate = ofDate.Format("2006-01-02")
		// query.MemberCode = "zta68pk52001062"
		query.Page = page
		query.Limit = 2000
		list, _, err := r.getTotalAffiliateUserList(query)
		if err != nil {
			return err
		}
		// Sleep 0.1 second
		time.Sleep(100 * time.Millisecond)
		if len(list) > 0 {
			for _, row := range list {
				isHasData := false
				var newRow model.MakeReportAffiliateUserResponse
				uuid := fmt.Sprintf("%s-%d", ofDate.Format("060102"), row.UserId)
				newRow.Id = uuid
				newRow.OfDate = query.FromDate // one day
				newRow.UserId = row.UserId
				newRow.LinkClickTotal = row.LinkClickTotal
				newRow.MemberCount = row.MemberCount
				isHasData = isHasData || (row.MemberCount+row.LinkClickTotal) > 0
				newRow.TotalTurnSport = row.TotalTurnSport
				newRow.TotalTurnCasino = row.TotalTurnCasino
				newRow.TotalTurnGame = row.TotalTurnGame
				newRow.TotalTurnLottery = row.TotalTurnLottery
				newRow.TotalTurnP2p = row.TotalTurnP2p
				newRow.TotalTurnFinancial = row.TotalTurnFinancial
				newRow.TotalCommission = row.TotalCommission
				isHasData = isHasData || (row.TotalTurnSport+row.TotalTurnCasino+row.TotalTurnGame+row.TotalTurnLottery+row.TotalTurnP2p+row.TotalTurnFinancial+row.TotalCommission) > 0
				newRow.MemberDepositAmount = row.MemberDepositAmount
				newRow.LinkRegisterAmount = row.LinkRegisterAmount
				newRow.FirstDepositAmount = row.FirstDepositAmount
				newRow.TotalIncome = row.TotalIncome
				newRow.TotalIncomeWithdraw = row.TotalIncomeWithdraw
				newRow.IncomeBalance = row.IncomeBalance
				isHasData = isHasData || (row.MemberDepositAmount+row.LinkRegisterAmount+row.FirstDepositAmount+row.TotalIncome+row.TotalIncomeWithdraw+row.IncomeBalance) > 0
				newRow.PromotionReturnLossTotal = row.PromotionReturnLossTotal
				newRow.LuckyWheelTotal = row.LuckyWheelTotal
				newRow.CouponTotal = row.CouponTotal
				newRow.CreditBonusTotal = row.CreditBonusTotal
				newRow.PromotionWebTotal = row.PromotionWebTotal
				newRow.PromotionReturnTurnTotal = row.PromotionReturnTurnTotal
				newRow.AllBonusTotal = row.AllBonusTotal
				isHasData = isHasData || (row.PromotionReturnLossTotal+row.LuckyWheelTotal+row.CouponTotal+row.CreditBonusTotal+row.PromotionWebTotal+row.PromotionReturnTurnTotal+row.AllBonusTotal) > 0
				if isHasData {
					createList[uuid] = newRow
					if len(createList) >= 500 {
						r.syncReportAffiliateUserList(createList)
						createList = make(map[string]model.MakeReportAffiliateUserResponse)
					}
				}
			}
			// LEFTOVER
			r.syncReportAffiliateUserList(createList)
		} else {
			break
		}
	}

	return nil
}

func (r repo) checkDiffReportAffiliateUserList(createRow model.MakeReportAffiliateUserResponse, dbRow model.MakeReportAffiliateUserResponse) *model.MakeReportAffiliateUserUpdateBody {

	// Compare both row for 22 fields
	dbJson := helper.StructJson(model.MakeReportAffiliateUserUpdateBody{
		LinkClickTotal:           dbRow.LinkClickTotal,
		MemberCount:              dbRow.MemberCount,
		MemberDepositAmount:      dbRow.MemberDepositAmount,
		TotalTurnSport:           dbRow.TotalTurnSport,
		TotalTurnCasino:          dbRow.TotalTurnCasino,
		TotalTurnGame:            dbRow.TotalTurnGame,
		TotalTurnLottery:         dbRow.TotalTurnLottery,
		TotalTurnP2p:             dbRow.TotalTurnP2p,
		TotalTurnFinancial:       dbRow.TotalTurnFinancial,
		TotalCommission:          dbRow.TotalCommission,
		LinkRegisterAmount:       dbRow.LinkRegisterAmount,
		FirstDepositAmount:       dbRow.FirstDepositAmount,
		TotalIncome:              dbRow.TotalIncome,
		TotalIncomeWithdraw:      dbRow.TotalIncomeWithdraw,
		IncomeBalance:            dbRow.IncomeBalance,
		PromotionReturnLossTotal: dbRow.PromotionReturnLossTotal,
		LuckyWheelTotal:          dbRow.LuckyWheelTotal,
		CouponTotal:              dbRow.CouponTotal,
		CreditBonusTotal:         dbRow.CreditBonusTotal,
		PromotionWebTotal:        dbRow.PromotionWebTotal,
		PromotionReturnTurnTotal: dbRow.PromotionReturnTurnTotal,
		AllBonusTotal:            dbRow.AllBonusTotal,
	})
	updateRow := model.MakeReportAffiliateUserUpdateBody{
		LinkClickTotal:           createRow.LinkClickTotal,
		MemberCount:              createRow.MemberCount,
		MemberDepositAmount:      createRow.MemberDepositAmount,
		TotalTurnSport:           createRow.TotalTurnSport,
		TotalTurnCasino:          createRow.TotalTurnCasino,
		TotalTurnGame:            createRow.TotalTurnGame,
		TotalTurnLottery:         createRow.TotalTurnLottery,
		TotalTurnP2p:             createRow.TotalTurnP2p,
		TotalTurnFinancial:       createRow.TotalTurnFinancial,
		TotalCommission:          createRow.TotalCommission,
		LinkRegisterAmount:       createRow.LinkRegisterAmount,
		FirstDepositAmount:       createRow.FirstDepositAmount,
		TotalIncome:              createRow.TotalIncome,
		TotalIncomeWithdraw:      createRow.TotalIncomeWithdraw,
		IncomeBalance:            createRow.IncomeBalance,
		PromotionReturnLossTotal: createRow.PromotionReturnLossTotal,
		LuckyWheelTotal:          createRow.LuckyWheelTotal,
		CouponTotal:              createRow.CouponTotal,
		CreditBonusTotal:         createRow.CreditBonusTotal,
		PromotionWebTotal:        createRow.PromotionWebTotal,
		PromotionReturnTurnTotal: createRow.PromotionReturnTurnTotal,
		AllBonusTotal:            createRow.AllBonusTotal,
	}
	if dbJson != helper.StructJson(updateRow) {
		return &updateRow
	}
	return nil
}

func (r repo) syncReportAffiliateUserList(createList map[string]model.MakeReportAffiliateUserResponse) {

	if len(createList) > 0 {
		updateList := make(map[string]model.MakeReportAffiliateUserUpdateBody)
		updateKey := map[string]string{}
		for _, v := range createList {
			updateKey[v.Id] = v.Id
		}

		dbMap := map[string]model.MakeReportAffiliateUserResponse{}
		var dbList []model.MakeReportAffiliateUserResponse
		sql := r.db.Table("report_affiliate_user").Where("id IN (?)", helper.MapIdsToStringArray(updateKey))
		if err := sql.Scan(&dbList).Error; err != nil {
			log.Println("Error Get Report Affiliate User", err)
			return
		}
		for _, v := range dbList {
			dbMap[v.Id] = v
		}

		for _, v := range createList {
			if _, ok := dbMap[v.Id]; ok {
				if updateRow := r.checkDiffReportAffiliateUserList(v, dbMap[v.Id]); updateRow != nil {
					updateList[v.Id] = *updateRow
				}
				// Always remove from create list if found in db
				delete(createList, v.Id)
			}
		}

		if len(updateList) > 0 {
			for uuid, v := range updateList {
				if err := r.db.Table("report_affiliate_user").Where("id = ?", uuid).Updates(v).Error; err != nil {
					log.Println("Error Update Report Affiliate User", err)
				}
			}
		}
		if len(createList) > 0 {
			var createDbList []model.MakeReportAffiliateUserResponse
			for _, v := range createList {
				createDbList = append(createDbList, v)
			}
			if err := r.db.Table("report_affiliate_user").Create(&createDbList).Error; err != nil {
				log.Println("Error Create Report Affiliate User", err)
			}
		}
	}

}

func (r repo) GetAffiliateActiveUserList(req model.AffiliateUserListRequest) ([]model.AffiliateUserResponse, int64, error) {

	var list []model.AffiliateUserResponse
	var total int64

	// 2024-10-15 GetAffiliateUserList => GetAffiliateActiveUserList
	// สิ่งที่ต้องการ
	// 1.เอารายการยูสที่เป็น 0 ทั้งหมดออก
	// 2.เรียงรายได้ค่าคอมสูงสุดขึ้นก่อน

	// รหัสสมาชิก
	// รายได้ยอดฝากครั้งแรก
	// สมัครรับรายได้
	// ยอดเล่นกีฬา
	// ค่าคอมกีฬา
	// ยอดเล่นคาสิโน
	// ค่าคอมคาสิโน
	// ยอดเล่นเกมส์
	// ค่าคอมเกมส์
	// ยอดเล่นลอตเตอรี่
	// ค่าคอมลอตเตอรี่
	// ยอดเล่น P2P
	// ค่าคอม P2P
	// ยอดเล่นการเงิน
	// ค่าคอมการเงิน
	// ยอดเล่นรวม
	// ยอดที่ได้รับ
	// วัน - เวลาที่สมัคร

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return nil, 0, err
	}

	// Mink.J — Today at 5:27 PM
	// รายงานลิงก์รับทรัพย์ ใส่ข้อมูลดังนี้
	// จำนวนคลิก หมายถึง จำนวนที่ (คน) กดลิงก์เข้ามาต่อคลิก เอาทศนิยมออก = 1.affiliate_link_click
	// จำนวนแนะนำ หมายถึง จำนวนที่ (คน) ที่ทำการสมัครมาจากลิงก์ เอาทศนิยมออก = 2.user
	// จำนวนฝาก หมายถึง ยอดเงิน ของลูกยูสจากการฝากครั้งแรก = 3.user_transaction
	// รายได้ค่าคอม หมายถึง รายได้คอมมิชัน (ที่คิดจาก % ยอดเทิร์น) รวมทุกประเภท = 4.user_affiliate_income
	// โบนัสแชร์ลิงก์ หมายถึง รายได้สมัคร รับรายได้เลย = 5.affiliate_transaction
	// โบนัสปิดลูกค้า หมายถึง โบนัสที่หัวลิงก์ได้ จากการฝากครั้งแรกของลูกลิ้งก์ เช่นลูกลิงก์ฝาก 100 เราได้ 20 (20คือรายได้เรา) = 6.affiliate_transaction
	// รายได้รวม หมายถึง รายได้รวมทั้งหมดที่หัวลิงก์ได้ (รายได้ลูกลิงก์สมัครรับรายได้เลย, รายได้จากลูกลิงก์สมัครฝากครั้งแรก และรายได้ค่าคอม) = 7.affiliate_transaction
	// รายได้ที่ถอน หมายถึง รายได้รวมที่หัวลิงก์ได้รับเข้ากระเป๋าหลักไปแล้วทั้งหมด = 8.affiliate_transaction
	// รายได้คงเหลือ หมายถึง รายได้รวมที่หัวลิงก์ยังไม่ได้โยกเข้ากระเป๋าหลักหน้าบ้าน = 9.affiliate_transaction

	// Count report's user DISTINCT
	count := r.db.Table("report_affiliate_user AS tb_report")
	count = count.Select("COUNT(DISTINCT(tb_report.user_id))")
	count = count.Joins("JOIN user AS tb_user ON tb_user.id = tb_report.user_id")
	count = count.Where("tb_user.user_type_id = ?", model.USER_TYPE_AFFILIATE)
	if req.MemberCode != "" {
		searchText := fmt.Sprintf("%%%s%%", req.MemberCode)
		count = count.Where("tb_user.member_code LIKE ?", searchText)
	}
	if dateType.DateFrom != "" {
		count = count.Where("tb_report.of_date >= ? ", dateType.DateFrom)
	}
	if dateType.DateTo != "" {
		count = count.Where("tb_report.of_date <=  ?", dateType.DateTo)
	}
	if err = count.
		Where("tb_user.deleted_at IS NULL").
		Scan(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_report.user_id AS user_id, tb_user.member_code AS member_code, tb_user.username AS username, tb_user.fullname AS user_fullname"
		selectedFields += ", SUM(tb_report.link_click_total) AS link_click_total, SUM(tb_report.member_count) AS member_count, SUM(tb_report.member_deposit_amount) AS member_deposit_amount"
		selectedFields += ", SUM(tb_report.total_turn_sport) AS total_turn_sport, SUM(tb_report.total_turn_casino) AS total_turn_casino, SUM(tb_report.total_turn_game) AS total_turn_game"
		selectedFields += ", SUM(tb_report.total_turn_lottery) AS total_turn_lottery, SUM(tb_report.total_turn_p2p) AS total_turn_p2p, SUM(tb_report.total_turn_financial) AS total_turn_financial"
		selectedFields += ", SUM(tb_report.total_commission) AS total_commission, SUM(tb_report.link_register_amount) AS link_register_amount, SUM(tb_report.first_deposit_amount) AS first_deposit_amount"
		selectedFields += ", SUM(tb_report.total_income) AS total_income, SUM(tb_report.total_income_withdraw) AS total_income_withdraw, SUM(tb_report.income_balance) AS income_balance"
		selectedFields += ", SUM(tb_report.promotion_return_loss_total) AS promotion_return_loss_total, SUM(tb_report.lucky_wheel_total) AS lucky_wheel_total"
		selectedFields += ", SUM(tb_report.coupon_total) AS coupon_total, SUM(tb_report.credit_bonus_total) AS credit_bonus_total"
		selectedFields += ", SUM(tb_report.promotion_web_total) AS promotion_web_total, SUM(tb_report.promotion_return_turn_total) AS promotion_return_turn_total"
		selectedFields += ", SUM(tb_report.all_bonus_total) AS all_bonus_total"
		query := r.db.Table("report_affiliate_user AS tb_report")
		query = query.Select(selectedFields)
		query = query.Joins("JOIN user AS tb_user ON tb_user.id = tb_report.user_id")
		query = query.Where("tb_user.user_type_id = ?", model.USER_TYPE_AFFILIATE)
		if req.MemberCode != "" {
			searchText := fmt.Sprintf("%%%s%%", req.MemberCode)
			query = query.Where("tb_user.member_code LIKE ?", searchText)
		}
		if dateType.DateFrom != "" {
			query = query.Where("tb_report.of_date >= ? ", dateType.DateFrom)
		}
		if dateType.DateTo != "" {
			query = query.Where("tb_report.of_date <=  ?", dateType.DateTo)
		}
		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		} else {
			query = query.Order("total_commission DESC")
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		query = query.Group("tb_report.user_id")
		if err = query.
			Where("tb_user.deleted_at IS NULL").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, 0, err
		}
	}
	return list, total, nil
}

func (r repo) GetAffiliateUserSummary(req model.AffiliateUserSummaryRequest) (*model.AffiliateUserSummaryResponse, error) {

	var summary model.AffiliateUserSummaryResponse

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return nil, err
	}
	selectedFields := "COUNT(DISTINCT(tb_report.user_id)) AS total_affiliate_user"
	selectedFields += ", SUM(tb_report.link_click_total) AS link_click_summary_total"
	selectedFields += ", SUM(tb_report.member_count) AS member_count_summary_total"
	selectedFields += ", SUM(tb_report.member_deposit_amount) AS member_deposit_amount_summary_total"
	selectedFields += ", SUM(tb_report.total_turn_sport) AS turn_sport_summary_total"
	selectedFields += ", SUM(tb_report.total_turn_casino) AS turn_casino_summary_total"
	selectedFields += ", SUM(tb_report.total_turn_game) AS turn_game_summary_total"
	selectedFields += ", SUM(tb_report.total_turn_lottery) AS turn_lottery_summary_total"
	selectedFields += ", SUM(tb_report.total_turn_p2p) AS turn_p2p_summary_total"
	selectedFields += ", SUM(tb_report.total_turn_financial) AS turn_financial_summary_total"
	selectedFields += ", SUM(tb_report.total_commission) AS commission_summary_total"
	selectedFields += ", SUM(tb_report.link_register_amount) AS link_register_amount_summary_total"
	selectedFields += ", SUM(tb_report.first_deposit_amount) AS first_deposit_amount_summary_total"
	selectedFields += ", SUM(tb_report.total_income) AS income_summary_total"
	selectedFields += ", SUM(tb_report.total_income_withdraw) AS income_withdraw_summary_total"
	selectedFields += ", SUM(tb_report.income_balance) AS income_balance_summary_total"
	selectedFields += ", SUM(tb_report.promotion_return_loss_total) AS promotion_return_loss_total"
	selectedFields += ", SUM(tb_report.lucky_wheel_total) AS lucky_wheel_total"
	selectedFields += ", SUM(tb_report.coupon_total) AS coupon_total"
	selectedFields += ", SUM(tb_report.credit_bonus_total) AS credit_bonus_total"
	selectedFields += ", SUM(tb_report.promotion_web_total) AS promotion_web_total"
	selectedFields += ", SUM(tb_report.promotion_return_turn_total) AS promotion_return_turn_total"
	selectedFields += ", SUM(tb_report.all_bonus_total) AS all_bonus_total"
	query := r.db.Table("report_affiliate_user AS tb_report")
	query = query.Select(selectedFields)
	query = query.Joins("JOIN user AS tb_user ON tb_user.id = tb_report.user_id")
	query = query.Where("tb_user.user_type_id = ?", model.USER_TYPE_AFFILIATE)
	if req.MemberCode != "" {
		searchText := fmt.Sprintf("%%%s%%", req.MemberCode)
		query = query.Where("tb_user.member_code LIKE ?", searchText)
	}
	if dateType.DateFrom != "" {
		query = query.Where("tb_report.of_date >= ? ", dateType.DateFrom)
	}
	if dateType.DateTo != "" {
		query = query.Where("tb_report.of_date <=  ?", dateType.DateTo)
	}
	if err = query.
		Where("tb_user.deleted_at IS NULL").
		Scan(&summary).
		Error; err != nil {
		return nil, err
	}

	return &summary, nil
}

func (r repo) GetAffiliateUserSummaryRealTime(req model.AffiliateUserSummaryRequest) (*model.AffiliateUserSummaryResponse, error) {

	var summary model.AffiliateUserSummaryResponse

	// get ref user id
	refIds := []int64{}

	dateType, errDateType := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if errDateType != nil {
		return nil, errDateType
	}

	// /affiliate_repository.go:1500 SLOW SQL >= 200ms สรุปภาพรวม ?
	// รอทำ Table นับดีๆ แยกตามวัน
	// [20250110] ยอดไม่ตรง เพราะ 1. ref_by user ถูกลบ และ user_type_id != 2

	// รหัสสมาชิก (จำนวน AFF ที่มียอดมากกว่า 0 = ที่ถูกนำมาสร้าง report )
	// ชื่อผู้ใช้/ชื่อ (หัวสายที่เป็น AFF)
	// จำนวนคลิก =
	// จำนวนแนะนำ =
	// จำนวนฝาก =
	// ยอดเทิร์น Sport =
	// ยอดเทิร์น Casino =
	// ยอดเทิร์น Game =
	// ยอดเทิร์น Lottery =
	// ยอดเทิร์น P2P =
	// ยอดเทิร์น Financial =
	// รายได้(ค่าคอม) =
	// สมัครรับรายได้ =
	// รายได้ยอดฝากครั้งแรก =
	// โบนัสโปรโมชั่น =
	// โบนัสคืนยอดเสีย =
	// โบนัสกงล้อ =
	// โบนัสคูปองเงินสด =
	// บันทึกแจกโบนัส =
	// ยอดจ่ายโบนัสทั้งหมด =
	// รายได้รวม (ทั้งหมด) =
	// รายได้ที่ถอน (ทั้งหมด) =
	// รายได้คงเหลือ (ทั้งหมด) =
	// จัดการ = Detail ดูลูกสาย ระดับที่ 1

	if req.MemberCode != "" {
		// กรอง ref_id ถ้า มี member_code
		// ถ้าไม่กรอง จะเอาทุก user ที่เป็น affiliate
		var list []model.AffiliateUserResponse

		selectedFields1 := "tb_user.id AS user_id"
		query1 := r.db.Table("user AS tb_user")
		query1 = query1.Select(selectedFields1)
		query1 = query1.Where("tb_user.user_type_id = ?", model.USER_TYPE_AFFILIATE)
		if req.MemberCode != "" {
			searchText := fmt.Sprintf("%%%s%%", req.MemberCode)
			query1 = query1.Where("tb_user.member_code LIKE ?", searchText)
		}
		if err := query1.
			Where("tb_user.deleted_at IS NULL").
			Scan(&list).
			Error; err != nil {
			return nil, err
		}
		for _, v := range list {
			refIds = append(refIds, v.UserId)
		}

		summary.TotalAffiliateUser = int64(len(refIds))
	} else {
		// กรอง ref_id ถ้า มี member_code
		// ถ้าไม่กรอง จะเอาทุก user ที่เป็น affiliate
		var countAll int64

		selectedFields1 := "id"
		query1 := r.db.Table("user AS tb_user")
		query1 = query1.Select(selectedFields1)
		query1 = query1.Where("tb_user.user_type_id = ?", model.USER_TYPE_AFFILIATE)
		if err := query1.
			Where("tb_user.deleted_at IS NULL").
			Count(&countAll).
			Error; err != nil {
			return nil, err
		}
		summary.TotalAffiliateUser = countAll
	}

	// TotalAffiliateUser

	// link_click_total
	var linkLog struct {
		TotalClick int64 `json:"total_click"`
	}

	query2 := r.db.Table("affiliate_link_click AS tb_log")
	query2 = query2.Select("COUNT(*) AS total_click")
	if len(refIds) > 0 {
		query2 = query2.Where("tb_log.ref_by IN (?)", refIds)
	} else {
		// Need user for user_type
		query2 = query2.Joins("INNER JOIN user AS tb_user ON tb_user.id = tb_log.ref_by")
		query2 = query2.Where("tb_user.user_type_id = ?", model.USER_TYPE_AFFILIATE)
	}
	// query2 = query2.Group("ref_by")
	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return nil, err
		}
		query2 = query2.Where("tb_log.created_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return nil, err
		}
		query2 = query2.Where("tb_log.created_at <= ?", endDateAtBkk)
	}
	if err := query2.
		Where("tb_user.deleted_at IS NULL").
		Take(&linkLog).
		Error; err != nil {
		linkLog.TotalClick = 0
	}

	summary.LinkClickSummaryTotal = linkLog.TotalClick

	// user that ref_by me and register(created_at) between start and end date
	var userRegisterLog struct {
		RecommendTotal int64 `json:"recommendTotal"`
	}

	selectedFields3 := "COUNT(*) AS recommend_total"
	query3 := r.db.Table("user AS tb_user")
	query3 = query3.Select(selectedFields3)
	if len(refIds) > 0 {
		query3 = query3.Where("tb_user.ref_by IN (?)", refIds)
	} else {
		// only affiliate user's member
		query3 = query3.Joins("LEFT JOIN user AS tb_ref_by ON tb_ref_by.id = tb_user.ref_by")
		// query3 = query3.Where("tb_user.ref_by IS NOT NULL")
		query3 = query3.Where("tb_ref_by.user_type_id = ?", model.USER_TYPE_AFFILIATE)
	}
	query3 = query3.Where("tb_user.deleted_at IS NULL")
	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return nil, err
		}
		query3 = query3.Where("tb_user.created_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return nil, err
		}
		query3 = query3.Where("tb_user.created_at <= ?", endDateAtBkk)
	}
	if err := query3.
		Take(&userRegisterLog).
		Error; err != nil {
		userRegisterLog.RecommendTotal = 0
	}

	summary.MemberCountSummaryTotal = userRegisterLog.RecommendTotal

	// user that have user_transaction Deposit and confirm_at between start and end date
	var userDepositLog struct {
		TotalDeposit float64 `json:"total_deposit"`
	}

	selectedFields4 := "SUM(tb_log.credit_amount) AS total_deposit"
	query4 := r.db.Table("user_transaction AS tb_log")
	query4 = query4.Select(selectedFields4)
	query4 = query4.Joins("INNER JOIN user AS tb_user ON tb_user.id = tb_log.user_id")
	query4 = query4.Where("tb_log.type_id = ?", model.CREDIT_TYPE_DEPOSIT)
	if len(refIds) > 0 {
		query4 = query4.Where("tb_user.ref_by IN (?)", refIds)
	} else {
		// only affiliate user's member
		// เช็คแค่ refby ไม่ได้แล้ว query4 = query4.Where("tb_user.ref_by IS NOT NULL AND tb_user.ref_by != 0")
		// [20250110] ยอดไม่ตรง เพราะ 1. ref_by user ถูกลบ และ user_type_id != 2
		query4 = query4.Joins("LEFT JOIN user AS tb_refby ON tb_refby.id = tb_user.ref_by")
		query4 = query4.Where("tb_refby.user_type_id = ?", model.USER_TYPE_AFFILIATE)
		query4 = query4.Where("tb_refby.deleted_at IS NULL")
	}
	query4 = query4.Where("tb_log.is_show = ?", true)
	query4 = query4.Where("tb_log.removed_at IS NULL")
	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return nil, err
		}
		query4 = query4.Where("transfer_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return nil, err
		}
		query4 = query4.Where("transfer_at <= ?", endDateAtBkk)
	}
	// 2024/08/28 18:44:55 /app/repository/affiliate_repository.go:1377 SLOW SQL >= 200ms [3941.588ms] [rows:4] todo
	if err := query4.
		Where("tb_user.deleted_at IS NULL").
		Take(&userDepositLog).
		Error; err != nil {
		userDepositLog.TotalDeposit = 0
	}

	summary.MemberDepositAmountSummaryTotal = userDepositLog.TotalDeposit

	// user that have user_affiliate_income Deposit and confirm_at between start and end date
	var userIncomeLog struct {
		TotalTurnSport     float64 `json:"total_turn_sport"`
		TotalTurnCasino    float64 `json:"total_turn_casino"`
		TotalTurnGame      float64 `json:"total_turn_game"`
		TotalTurnLottery   float64 `json:"total_turn_lottery"`
		TotalTurnP2p       float64 `json:"total_turn_p2p"`
		TotalTurnFinancial float64 `json:"total_turn_financial"`
		TotalCommission    float64 `json:"total_commission"`
	}

	selectedFields5 := "SUM(tb_log.turn_sport) AS total_turn_sport, SUM(tb_log.turn_casino) AS total_turn_casino, SUM(tb_log.turn_game) AS total_turn_game"
	selectedFields5 += ", SUM(tb_log.turn_lottery) AS total_turn_lottery, SUM(tb_log.turn_p2p) AS total_turn_p2p, SUM(tb_log.turn_financial) AS total_turn_financial"
	selectedFields5 += ", SUM(tb_log.commission_total) AS total_commission"
	query5 := r.db.Table("user_affiliate_income AS tb_log")
	query5 = query5.Joins("INNER JOIN user AS tb_user ON tb_user.id = tb_log.user_id")
	query5 = query5.Select(selectedFields5)
	if len(refIds) > 0 {
		query5 = query5.Where("tb_user.ref_by IN ?", refIds)
	} else {
		// only affiliate user's member
		// ไม่ได้ query5 = query5.Where("tb_user.ref_by IS NOT NULL AND tb_user.ref_by != 0")
		// [20250110] ยอดไม่ตรง เพราะ 1. ref_by user ถูกลบ และ user_type_id != 2
		query5 = query5.Joins("LEFT JOIN user AS tb_refby ON tb_refby.id = tb_user.ref_by")
		query5 = query5.Where("tb_refby.user_type_id = ?", model.USER_TYPE_AFFILIATE)
		query5 = query5.Where("tb_refby.deleted_at IS NULL")
	}
	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return nil, err
		}
		query5 = query5.Where("tb_log.created_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return nil, err
		}
		query5 = query5.Where("tb_log.created_at <= ?", endDateAtBkk)
	}
	if err := query5.
		Where("tb_user.deleted_at IS NULL").
		Take(&userIncomeLog).
		Error; err != nil {
		userIncomeLog.TotalTurnSport = 0
		userIncomeLog.TotalTurnCasino = 0
		userIncomeLog.TotalTurnGame = 0
		userIncomeLog.TotalTurnLottery = 0
		userIncomeLog.TotalTurnP2p = 0
		userIncomeLog.TotalTurnFinancial = 0
		userIncomeLog.TotalCommission = 0
	}

	summary.TurnSportSummaryTotal = userIncomeLog.TotalTurnSport
	summary.TurnCasinoSummaryTotal = userIncomeLog.TotalTurnCasino
	summary.TurnGameSummaryTotal = userIncomeLog.TotalTurnGame
	summary.TurnLotterySummaryTotal = userIncomeLog.TotalTurnLottery
	summary.TurnP2pSummaryTotal = userIncomeLog.TotalTurnP2p
	summary.TurnFinancialSummaryTotal = userIncomeLog.TotalTurnFinancial
	summary.CommissionSummaryTotal = userIncomeLog.TotalCommission

	// ref that have affiliate_transaction As โบนัส Type แชร์ลิงค์(1), โบนัส ยอดฝากแรก(2) and created_at between start and end date
	var userTransactionLog struct {
		LinkRegisterAmount float64 `json:"link_register_amount"`
		FirstDepositAmount float64 `json:"first_deposit_amount"`
	}

	selectedFields6 := "SUM(CASE WHEN tb_log.type_id = 1 THEN income_amount ELSE 0 END) AS link_register_amount"
	selectedFields6 += ", SUM(CASE WHEN tb_log.type_id = 2 THEN income_amount ELSE 0 END) AS first_deposit_amount"
	query6 := r.db.Table("affiliate_transaction AS tb_log")
	query6 = query6.Select(selectedFields6)
	query6 = query6.Joins("INNER JOIN user AS tb_user ON tb_user.id = tb_log.user_id")
	if len(refIds) > 0 {
		query6 = query6.Where("tb_log.user_id IN ?", refIds)
	} else {
		// [20250110] ยอดไม่ตรง เพราะ 1. ref_by user ถูกลบ และ user_type_id != 2
		query6 = query6.Joins("LEFT JOIN user AS tb_refby ON tb_refby.id = tb_user.ref_by")
		query6 = query6.Where("tb_refby.user_type_id = ?", model.USER_TYPE_AFFILIATE)
		query6 = query6.Where("tb_refby.deleted_at IS NULL")
	}
	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return nil, err
		}
		query6 = query6.Where("tb_log.created_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return nil, err
		}
		query6 = query6.Where("tb_log.created_at <= ?", endDateAtBkk)
	}
	if err := query6.
		Where("tb_user.deleted_at IS NULL").
		Take(&userTransactionLog).
		Error; err != nil {
		userTransactionLog.LinkRegisterAmount = 0
		userTransactionLog.FirstDepositAmount = 0
	}

	summary.LinkRegisterAmountSummaryTotal = userTransactionLog.LinkRegisterAmount
	summary.FirstDepositAmountSummaryTotal = userTransactionLog.FirstDepositAmount

	// กรอกข้อมูลลิ้งรับทรัพย์
	// get total income transaction without date
	// Withdraw = 3,4 **pending confirm is withdrawed
	// income_balance = 1 ** only pending รอกดรับ
	// Where("tb_log.status_id != ?", model.AFF_TRANSACTION_STATUS_EXPIRED). เอาออกเพราะต้องรวมทุกยอด
	var userTotalIncome struct {
		TotalIncome   float64 `json:"total_income"`
		TotalWithdraw float64 `json:"total_withdraw"`
		IncomeBalance float64 `json:"income_balance"`
	}

	selectedFields7 := "SUM(income_amount) AS total_income"
	selectedFields7 += ", SUM(CASE WHEN tb_log.status_id IN (3,4) THEN income_amount ELSE 0 END) AS total_withdraw"
	selectedFields7 += ", SUM(CASE WHEN tb_log.status_id IN (1) THEN income_amount ELSE 0 END) AS income_balance"
	query7 := r.db.Table("affiliate_transaction AS tb_log")
	query7 = query7.Select(selectedFields7)
	query7 = query7.Joins("INNER JOIN user AS tb_user ON tb_user.id = tb_log.user_id")
	if len(refIds) > 0 {
		query7 = query7.Where("tb_log.user_id IN (?)", refIds)
	} else {
		// [20250110] ยอดไม่ตรง เพราะ 1. ref_by user ถูกลบ และ user_type_id != 2
		query7 = query7.Joins("LEFT JOIN user AS tb_refby ON tb_refby.id = tb_user.ref_by")
		query7 = query7.Where("tb_refby.user_type_id = ?", model.USER_TYPE_AFFILIATE)
		query7 = query7.Where("tb_refby.deleted_at IS NULL")
	}
	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return nil, err
		}
		query7 = query7.Where("tb_log.created_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return nil, err
		}
		query7 = query7.Where("tb_log.created_at <= ?", endDateAtBkk)
	}
	if err := query7.
		Where("tb_user.deleted_at IS NULL").
		Take(&userTotalIncome).
		Error; err != nil {
		userTotalIncome.TotalIncome = 0
		userTotalIncome.TotalWithdraw = 0
		userTotalIncome.IncomeBalance = 0
	}

	summary.IncomeSummaryTotal = userTotalIncome.TotalIncome
	summary.IncomeWithdrawSummaryTotal = userTotalIncome.TotalWithdraw
	summary.IncomeBalanceSummaryTotal = userTotalIncome.IncomeBalance

	var userIncomeLog2 struct {
		PromotionReturnLossTotal float64 `json:"promotionReturnLossTotal"` // โบนัสคืนยอดเสีย // มี
		LuckyWheelTotal          float64 `json:"luckyWheelTotal"`          // กงล้อ   // มี
		CouponTotal              float64 `json:"couponTotal"`              // คูปอง   // มี
		CreditBonusTotal         float64 `json:"creditBonusTotal"`         // โบนัสเครดิต  // มี
		PromotionWebTotal        float64 `json:"promotionWebTotal"`        // โปรโมชั่น   //มี
		PromotionReturnTurnTotal float64 `json:"promotionReturnTurnTotal"` // โบนัสคืนยอดเทิร์น // มี
		AllBonusTotal            float64 `json:"allBonusTotal"`
	}

	// user_income_log  use user_id join user_income_log confirm P.Tula Discord 20240715
	selectedFields8 := "SUM(CASE WHEN income_logs.type_id = 4 THEN income_logs.bonus_amount ELSE 0 END) AS promotion_return_loss_total"
	selectedFields8 += ", SUM(CASE WHEN income_logs.type_id = 9 THEN income_logs.bonus_amount ELSE 0 END) AS lucky_wheel_total"
	selectedFields8 += ", SUM(CASE WHEN income_logs.type_id = 11 THEN income_logs.bonus_amount ELSE 0 END) AS coupon_total"
	selectedFields8 += ", SUM(CASE WHEN income_logs.type_id = 3 THEN income_logs.bonus_amount ELSE 0 END) AS credit_bonus_total"
	selectedFields8 += ", SUM(CASE WHEN income_logs.type_id = 10 THEN income_logs.bonus_amount ELSE 0 END) AS promotion_web_total"
	selectedFields8 += ", SUM(CASE WHEN income_logs.type_id = 13 THEN income_logs.bonus_amount ELSE 0 END) AS promotion_return_turn_total"

	query8 := r.db.Table("user_transaction AS income_logs").
		Select(selectedFields8).
		Joins("INNER JOIN user AS tb_user ON tb_user.id = income_logs.user_id")
	if len(refIds) > 0 {
		query8 = query8.Where("tb_user.ref_by IN (?)", refIds)
	} else {
		// only affiliate user's member
		// เช็คแค่นี้ไม่ได้ query8 = query8.Where("tb_user.ref_by IS NOT NULL AND tb_user.ref_by != 0")
		// [20250110] ยอดไม่ตรง เพราะ 1. ref_by user ถูกลบ และ user_type_id != 2
		query8 = query8.Joins("LEFT JOIN user AS tb_refby ON tb_refby.id = tb_user.ref_by")
		query8 = query8.Where("tb_refby.user_type_id = ?", model.USER_TYPE_AFFILIATE)
		query8 = query8.Where("tb_refby.deleted_at IS NULL")
	}
	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return nil, err
		}
		query8 = query8.Where("income_logs.transfer_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return nil, err
		}
		query8 = query8.Where("income_logs.transfer_at <=  ?", endDateAtBkk)
	}
	query8 = query8.Where("tb_user.deleted_at IS NULL")
	if err := query8.Take(&userIncomeLog2).Error; err != nil {
		return nil, err
	}
	summary.PromotionReturnLossTotal = userIncomeLog2.PromotionReturnLossTotal
	summary.LuckyWheelTotal = userIncomeLog2.LuckyWheelTotal
	summary.CouponTotal = userIncomeLog2.CouponTotal
	summary.CreditBonusTotal = userIncomeLog2.CreditBonusTotal
	summary.PromotionWebTotal = userIncomeLog2.PromotionWebTotal
	summary.PromotionReturnTurnTotal = userIncomeLog2.PromotionReturnTurnTotal
	summary.AllBonusTotal = userIncomeLog2.PromotionReturnLossTotal + userIncomeLog2.LuckyWheelTotal + userIncomeLog2.CouponTotal + userIncomeLog2.CreditBonusTotal + userIncomeLog2.PromotionWebTotal + userIncomeLog2.PromotionReturnTurnTotal

	return &summary, nil
}

func (r repo) GetAffiliateDepositPlayList(req model.AffiliateDepositPlayListRequest) ([]model.AffiliateUserDepositPlayResponse, int64, error) {

	var list []model.AffiliateUserDepositPlayResponse
	var total int64

	dateType, errDateType := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if errDateType != nil {
		return nil, 0, errDateType
	}

	// Count total records for pagination purposes (without limit and offset) //
	if req.Level != nil {
		count := r.db.Table("affiliate_level AS tb_level")
		count = count.Select("tb_level.user_id")
		count = count.Joins("INNER JOIN user AS tb_user ON tb_user.id = tb_level.user_id")
		count = count.Where("tb_level.upline_id = ?", req.RefUserId)
		count = count.Where("tb_level.level = ?", req.Level)
		if req.MemberCode != "" {
			searchText := fmt.Sprintf("%%%s%%", req.MemberCode)
			count = count.Where("tb_user.member_code LIKE ?", searchText)
		}
		if err := count.
			Where("tb_user.deleted_at IS NULL").
			Count(&total).
			Error; err != nil {
			return nil, total, err
		}
	} else {
		count := r.db.Table("user AS users")
		count = count.Select("users.id")
		count = count.Where("users.ref_by = ?", req.RefUserId)
		if req.MemberCode != "" {
			searchText := fmt.Sprintf("%%%s%%", req.MemberCode)
			count = count.Where("users.member_code LIKE ?", searchText)
		}
		if err := count.
			Where("users.deleted_at IS NULL").
			Count(&total).
			Error; err != nil {
			return nil, total, err
		}
	}

	if total > 0 {
		if req.Level != nil {
			// SELECT //
			selectedFields := "tb_user.ref_by AS ref_user_id, tb_user.id AS user_id, tb_user.member_code AS member_code, tb_user.created_at AS register_at"
			query := r.db.Table("affiliate_level AS tb_level")
			query = query.Select(selectedFields)
			query = query.Joins("INNER JOIN user AS tb_user ON tb_user.id = tb_level.user_id")
			query = query.Where("tb_level.upline_id = ?", req.RefUserId)
			query = query.Where("tb_level.level = ?", req.Level)
			if req.MemberCode != "" {
				searchText := fmt.Sprintf("%%%s%%", req.MemberCode)
				query = query.Where("tb_user.member_code LIKE ?", searchText)
			}
			// Sort by ANY //
			req.SortCol = strings.TrimSpace(req.SortCol)
			if req.SortCol != "" {
				if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
					req.SortAsc = "DESC"
				} else {
					req.SortAsc = "ASC"
				}
				query = query.Order(req.SortCol + " " + req.SortAsc)
			}
			if req.Limit > 0 {
				query = query.Limit(req.Limit)
			}
			if err := query.
				Where("tb_user.deleted_at IS NULL").
				Offset(req.Page * req.Limit).
				Scan(&list).
				Error; err != nil {
				return nil, 0, err
			}
		} else {
			// SELECT //
			selectedFields := "users.ref_by AS ref_user_id, users.id AS user_id, users.member_code AS member_code, users.created_at AS register_at"
			query := r.db.Table("user AS users")
			query = query.Select(selectedFields)
			query = query.Where("users.ref_by = ?", req.RefUserId)
			if req.MemberCode != "" {
				searchText := fmt.Sprintf("%%%s%%", req.MemberCode)
				query = query.Where("users.member_code LIKE ?", searchText)
			}
			// Sort by ANY //
			req.SortCol = strings.TrimSpace(req.SortCol)
			if req.SortCol != "" {
				if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
					req.SortAsc = "DESC"
				} else {
					req.SortAsc = "ASC"
				}
				query = query.Order(req.SortCol + " " + req.SortAsc)
			}
			if req.Limit > 0 {
				query = query.Limit(req.Limit)
			}
			if err := query.
				Where("users.deleted_at IS NULL").
				Offset(req.Page * req.Limit).
				Scan(&list).
				Error; err != nil {
				return nil, 0, err
			}
		}

		// APPEND TOTAL DATA *DOWNLINE ONLY*
		userIds := []int64{}
		for _, v := range list {
			userIds = append(userIds, v.UserId)
		}

		// user that have user_affiliate_income Deposit and confirm_at between start and end date
		var userIncomeLog []struct {
			UserId              int64   `json:"user_id"`
			TotalTurnSport      float64 `json:"total_turn_sport"`
			CommissionSport     float64 `json:"commission_sport"`
			TotalTurnCasino     float64 `json:"total_turn_casino"`
			CommissionCasino    float64 `json:"commission_casino"`
			TotalTurnGame       float64 `json:"total_turn_game"`
			CommissionGame      float64 `json:"commission_game"`
			TotalTurnLottery    float64 `json:"total_turn_lottery"`
			CommissionLottery   float64 `json:"commission_lottery"`
			TotalTurnP2p        float64 `json:"total_turn_p2p"`
			CommissionP2p       float64 `json:"commission_p2p"`
			TotalTurnFinancial  float64 `json:"total_turn_financial"`
			CommissionFinancial float64 `json:"commission_financial"`
			TotalPlayAmount     float64 `json:"total_play_amount"`
			TotalCommission     float64 `json:"total_commission"`
		}
		selectedFields1 := "tb_log.user_id AS user_id"
		if req.Level != nil {
			if *req.Level == 3 {
				selectedFields1 += ", SUM(tb_log.turn_sport) AS total_turn_sport, SUM(tb_log.commission_sport2) AS commission_sport"
				selectedFields1 += ", SUM(tb_log.turn_casino) AS total_turn_casino, SUM(tb_log.commission_casino2) AS commission_casino"
				selectedFields1 += ", SUM(tb_log.turn_game) AS total_turn_game, SUM(tb_log.commission_game2) AS commission_game"
				selectedFields1 += ", SUM(tb_log.turn_lottery) AS total_turn_lottery, SUM(tb_log.commission_lottery2) AS commission_lottery"
				selectedFields1 += ", SUM(tb_log.turn_p2p) AS total_turn_p2p, SUM(tb_log.commission_p2p2) AS commission_p2p"
				selectedFields1 += ", SUM(tb_log.turn_financial) AS total_turn_financial, SUM(tb_log.commission_financial2) AS commission_financial"
				selectedFields1 += ", SUM(tb_log.turn_sport) + SUM(tb_log.turn_casino) + SUM(tb_log.turn_game) + SUM(tb_log.turn_lottery) + SUM(tb_log.turn_p2p) + SUM(tb_log.turn_financial) AS total_play_amount"
				selectedFields1 += ", SUM(tb_log.commission_sport2) + SUM(tb_log.commission_casino2) + SUM(tb_log.commission_game2) + SUM(tb_log.commission_lottery2) + SUM(tb_log.commission_p2p2) + SUM(tb_log.commission_financial2) AS total_commission"
			} else if *req.Level == 2 {
				selectedFields1 += ", SUM(tb_log.turn_sport) AS total_turn_sport, SUM(tb_log.commission_sport1) AS commission_sport"
				selectedFields1 += ", SUM(tb_log.turn_casino) AS total_turn_casino, SUM(tb_log.commission_casino1) AS commission_casino"
				selectedFields1 += ", SUM(tb_log.turn_game) AS total_turn_game, SUM(tb_log.commission_game1) AS commission_game"
				selectedFields1 += ", SUM(tb_log.turn_lottery) AS total_turn_lottery, SUM(tb_log.commission_lottery1) AS commission_lottery"
				selectedFields1 += ", SUM(tb_log.turn_p2p) AS total_turn_p2p, SUM(tb_log.commission_p2p1) AS commission_p2p"
				selectedFields1 += ", SUM(tb_log.turn_financial) AS total_turn_financial, SUM(tb_log.commission_financial1) AS commission_financial"
				selectedFields1 += ", SUM(tb_log.turn_sport) + SUM(tb_log.turn_casino) + SUM(tb_log.turn_game) + SUM(tb_log.turn_lottery) + SUM(tb_log.turn_p2p) + SUM(tb_log.turn_financial) AS total_play_amount"
				selectedFields1 += ", SUM(tb_log.commission_sport1) + SUM(tb_log.commission_casino1) + SUM(tb_log.commission_game1) + SUM(tb_log.commission_lottery1) + SUM(tb_log.commission_p2p1) + SUM(tb_log.commission_financial1) AS total_commission"
			} else {
				selectedFields1 += ", SUM(tb_log.turn_sport) AS total_turn_sport, SUM(tb_log.commission_sport) AS commission_sport"
				selectedFields1 += ", SUM(tb_log.turn_casino) AS total_turn_casino, SUM(tb_log.commission_casino) AS commission_casino"
				selectedFields1 += ", SUM(tb_log.turn_game) AS total_turn_game, SUM(tb_log.commission_game) AS commission_game"
				selectedFields1 += ", SUM(tb_log.turn_lottery) AS total_turn_lottery, SUM(tb_log.commission_lottery) AS commission_lottery"
				selectedFields1 += ", SUM(tb_log.turn_p2p) AS total_turn_p2p, SUM(tb_log.commission_p2p) AS commission_p2p"
				selectedFields1 += ", SUM(tb_log.turn_financial) AS total_turn_financial, SUM(tb_log.commission_financial) AS commission_financial"
				selectedFields1 += ", SUM(tb_log.turn_sport) + SUM(tb_log.turn_casino) + SUM(tb_log.turn_game) + SUM(tb_log.turn_lottery) + SUM(tb_log.turn_p2p) + SUM(tb_log.turn_financial) AS total_play_amount"
				selectedFields1 += ", SUM(tb_log.commission_sport) + SUM(tb_log.commission_casino) + SUM(tb_log.commission_game) + SUM(tb_log.commission_lottery) + SUM(tb_log.commission_p2p) + SUM(tb_log.commission_financial) AS total_commission"
			}
		} else {
			selectedFields1 += ", SUM(tb_log.turn_sport) AS total_turn_sport, SUM(tb_log.commission_sport) AS commission_sport"
			selectedFields1 += ", SUM(tb_log.turn_casino) AS total_turn_casino, SUM(tb_log.commission_casino) AS commission_casino"
			selectedFields1 += ", SUM(tb_log.turn_game) AS total_turn_game, SUM(tb_log.commission_game) AS commission_game"
			selectedFields1 += ", SUM(tb_log.turn_lottery) AS total_turn_lottery, SUM(tb_log.commission_lottery) AS commission_lottery"
			selectedFields1 += ", SUM(tb_log.turn_p2p) AS total_turn_p2p, SUM(tb_log.commission_p2p) AS commission_p2p"
			selectedFields1 += ", SUM(tb_log.turn_financial) AS total_turn_financial, SUM(tb_log.commission_financial) AS commission_financial"
			selectedFields1 += ", SUM(tb_log.turn_sport) + SUM(tb_log.turn_casino) + SUM(tb_log.turn_game) AS total_play_amount, SUM(tb_log.commission_total) AS total_commission"
		}
		query1 := r.db.Table("user_affiliate_income AS tb_log").
			Select(selectedFields1).
			Where("tb_log.user_id IN (?)", userIds).
			Group("tb_log.user_id")
		if dateType.DateFrom != "" {
			startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
			if err != nil {
				return nil, 0, err
			}
			query1 = query1.Where("tb_log.created_at >= ? ", startDateAtBkk)
		}
		if dateType.DateTo != "" {
			endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
			if err != nil {
				return nil, 0, err
			}
			query1 = query1.Where("tb_log.created_at <=  ?", endDateAtBkk)
		}
		if err := query1.Scan(&userIncomeLog).Error; err != nil {
			return nil, 0, err
		}

		// โบนัสฝากครั้งแรกของสมาชิก *เฉพาะเลเวล 1*
		var userDepositLog []struct {
			UserId             int64   `json:"user_id"`
			FirstDepositAmount float64 `json:"first_deposit_amount"`
		}
		if req.Level != nil && *req.Level == 1 {
			selectedFields2 := "tb_log.downline_id AS user_id, tb_log.income_amount AS first_deposit_amount"
			query2 := r.db.Table("affiliate_transaction AS tb_log").
				Select(selectedFields2).
				Where("tb_log.type_id = ?", model.AFF_TRANSACTION_TYPE_FIRST_DEPOSIT).
				Where("tb_log.downline_id IN (?)", userIds)

			// if dateType.DateFrom != "" {
			// 	startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
			// 	if err != nil {
			// 		return nil, 0, err
			// 	}
			// 	query2 = query2.Where("tb_log.created_at >= ? ", startDateAtBkk)
			// }
			// if dateType.DateTo != "" {
			// 	endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
			// 	if err != nil {
			// 		return nil, 0, err
			// 	}
			// 	query2 = query2.Where("tb_log.created_at <=  ?", endDateAtBkk)
			// }
			if err := query2.Scan(&userDepositLog).Error; err != nil {
				return nil, 0, err
			}
		}

		// affiliate is for link_register_amount ไม่ได้กรองตามวัน Total = ALL DATE
		var userRegistereLog []struct {
			UserId             int64   `json:"user_id"`
			LinkRegisterAmount float64 `json:"link_register_amount"`
		}
		if req.Level != nil && *req.Level == 1 {
			selectedFields3 := "tb_log.user_id AS user_id, SUM(tb_log.register_bonus_credit) AS link_register_amount"
			query3 := r.db.Table("affiliate AS tb_log").
				Select(selectedFields3).
				Where("tb_log.user_id IN (?)", userIds).
				Group("tb_log.user_id")
			// if dateType.DateFrom != "" {
			// 	startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
			// 	if err != nil {
			// 		return nil, 0, err
			// 	}
			// 	query3 = query3.Where("tb_log.created_at >= ? ", startDateAtBkk)
			// }
			// if dateType.DateTo != "" {
			// 	endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
			// 	if err != nil {
			// 		return nil, 0, err
			// 	}
			// 	query3 = query3.Where("tb_log.created_at <=  ?", endDateAtBkk)
			// }
			if err := query3.Scan(&userRegistereLog).Error; err != nil {
				return nil, 0, err
			}
		}

		// MERGE DATA BY USER_ID
		for index, item := range list {
			for _, refData := range userIncomeLog {
				if item.UserId == refData.UserId {
					list[index].TotalTurnSport = refData.TotalTurnSport
					list[index].CommissionSport = refData.CommissionSport
					list[index].TotalTurnCasino = refData.TotalTurnCasino
					list[index].CommissionCasino = refData.CommissionCasino
					list[index].TotalTurnGame = refData.TotalTurnGame
					list[index].CommissionGame = refData.CommissionGame
					list[index].TotalTurnLottery = refData.TotalTurnLottery
					list[index].CommissionLottery = refData.CommissionLottery
					list[index].TotalTurnP2p = refData.TotalTurnP2p
					list[index].CommissionP2p = refData.CommissionP2p
					list[index].TotalTurnFinancial = refData.TotalTurnFinancial
					list[index].CommissionFinancial = refData.CommissionFinancial
					list[index].TotalPlayAmount = refData.TotalPlayAmount
					list[index].TotalCommission = refData.TotalCommission
					break
				}
			}
			for _, refData := range userDepositLog {
				if item.UserId == refData.UserId {
					list[index].FirstDepositAmount = refData.FirstDepositAmount
					break
				}
			}
			for _, refData := range userRegistereLog {
				if item.UserId == refData.UserId {
					list[index].LinkRegisterAmount = refData.LinkRegisterAmount
					break
				}
			}
		}
	}
	return list, total, nil
}

func (r repo) GetAffiliateDepositPlayCountLevel(req model.AffiliateDepositPlayListRequest) (*model.AffiliateUserDownlineCountResponse, error) {

	var sumData model.AffiliateUserDownlineCountResponse

	// SELECT //
	selectedFields := "tb_level.upline_id AS ref_user_id"
	selectedFields += ", COUNT(CASE WHEN tb_level.level = 1 THEN tb_level.user_id ELSE NULL END) AS level1_count"
	selectedFields += ", COUNT(CASE WHEN tb_level.level = 2 THEN tb_level.user_id ELSE NULL END) AS level2_count"
	selectedFields += ", COUNT(CASE WHEN tb_level.level = 3 THEN tb_level.user_id ELSE NULL END) AS level3_count"
	query := r.db.Table("affiliate_level AS tb_level")
	query = query.Select(selectedFields)
	query = query.Where("tb_level.upline_id = ?", req.RefUserId)
	query = query.Group("tb_level.upline_id")
	if err := query.Scan(&sumData).Error; err != nil {
		return nil, err
	}
	return &sumData, nil
}

func (r repo) MigrateAffiliateUserAllLevel(userId int64) error {

	// RECUSIVE MAX at 500 rows //
	createList := make(map[string]model.AffiliateLevelCreateBody)
	ukeyList := make(map[string]string)
	page := 0
	total := -1
	for total != 0 {
		// SELECT per User AS new AS register //
		// Register = has ref_by = level 1
		// level 2 = ref_by of level 1
		// level 3 = ref_by of level 2
		var baseUserList []struct {
			UserId         int64 `json:"user_id"`
			Level1UplineId int64 `json:"level1_upline_id"`
			Level2UplineId int64 `json:"level2_upline_id"`
			Level3UplineId int64 `json:"level3_upline_id"`
		}
		selectedFields := "tb_user.id AS user_id, tb_user.ref_by AS level1_upline_id"
		selectedFields += ", tb_level1.ref_by AS level2_upline_id"
		selectedFields += ", tb_level2.ref_by AS level3_upline_id"
		query := r.db.Table("user AS tb_user")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN user AS tb_level1 ON tb_level1.id = tb_user.ref_by")
		query = query.Joins("LEFT JOIN user AS tb_level2 ON tb_level2.id = tb_level1.ref_by")
		if userId != 0 {
			query = query.Where("tb_user.id = ?", userId)
		}
		query = query.Offset(page * 500)
		query = query.Limit(500)
		if err := query.Scan(&baseUserList).Error; err != nil {
			return err
		}

		fmt.Println("list", helper.StructJson(baseUserList))
		total = len(baseUserList)
		page++

		if total > 0 {
			for _, v := range baseUserList {
				if v.UserId != 0 && v.Level1UplineId != 0 {
					ukey := fmt.Sprintf("%d-%d", v.Level1UplineId, v.UserId)
					newRow1 := model.AffiliateLevelCreateBody{
						Ukey:     ukey,
						UserId:   v.UserId,
						UplineId: v.Level1UplineId,
						Level:    1,
					}
					createList[ukey] = newRow1
					ukeyList[ukey] = ukey
				}
				if v.UserId != 0 && v.Level2UplineId != 0 {
					ukey := fmt.Sprintf("%d-%d", v.Level2UplineId, v.UserId)
					newRow2 := model.AffiliateLevelCreateBody{
						Ukey:     ukey,
						UserId:   v.UserId,
						UplineId: v.Level2UplineId,
						Level:    2,
					}
					createList[ukey] = newRow2
					ukeyList[ukey] = ukey
				}
				if v.UserId != 0 && v.Level3UplineId != 0 {
					ukey := fmt.Sprintf("%d-%d", v.Level3UplineId, v.UserId)
					newRow3 := model.AffiliateLevelCreateBody{
						Ukey:     ukey,
						UserId:   v.UserId,
						UplineId: v.Level3UplineId,
						Level:    3,
					}
					createList[ukey] = newRow3
					ukeyList[ukey] = ukey
				}
				// fmt.Println("createList", helper.StructJson(createList))
				if len(createList) >= 500 {
					if err := r.CreateAffiliateUserAllLevelBulk(ukeyList, createList); err != nil {
						return err
					}
					createList = make(map[string]model.AffiliateLevelCreateBody)
					ukeyList = make(map[string]string)
				}
			}
		}
	}

	// LEFTOVER
	if len(createList) > 0 {
		if err := r.CreateAffiliateUserAllLevelBulk(ukeyList, createList); err != nil {
			return err
		}
	}

	return nil
}

func (r repo) CreateAffiliateUserAllLevelBulk(ukeyList map[string]string, createList map[string]model.AffiliateLevelCreateBody) error {

	mapDbList := make(map[string]model.AffiliateLevel)
	var dbList []model.AffiliateLevel

	if len(ukeyList) == 0 {
		log.Println("NO_UKEY_LIST")
		return nil
	}

	query := r.db.Table("affiliate_level")
	query = query.Select("*")
	query = query.Where("ukey IN (?)", helper.MapIdsToStringArray(ukeyList))
	if err := query.Scan(&dbList).Error; err != nil {
		return err
	}
	// Mapping
	for _, v := range dbList {
		mapDbList[v.Ukey] = v
	}
	// INSERT only not exists
	createBody := []model.AffiliateLevelCreateBody{}
	for k, v := range createList {
		if _, ok := mapDbList[k]; !ok {
			createBody = append(createBody, v)
		}
	}
	if len(createBody) > 0 {
		if err := r.db.Table("affiliate_level").Create(&createBody).Error; err != nil {
			return err
		}
	}

	return nil
}

func (r repo) GetAffTransactionById(id int64) (*model.AffTransactionResponse, error) {

	var record model.AffTransactionResponse

	selectedFields := "tb_log.id AS id, tb_log.user_id AS user_id, tb_log.income_amount AS income_amount"
	selectedFields += ", tb_log.type_id AS type_id, tb_type.name AS type_name, tb_log.status_id AS status_id, tb_status.name AS status_name"
	selectedFields += ", tb_log.transfer_at AS transfer_at, tb_log.created_at AS created_at, tb_log.updated_at AS updated_at"
	if err := r.db.Table("affiliate_transaction AS tb_log").
		Joins("LEFT JOIN affiliate_transaction_type AS tb_type ON tb_type.id = tb_log.type_id").
		Joins("LEFT JOIN affiliate_transaction_status AS tb_status ON tb_status.id = tb_log.status_id").
		Select(selectedFields).
		Where("tb_log.id = ?", id).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetAffTransactionList(req model.AffTransactionListRequest) ([]model.AffTransactionResponse, int64, error) {

	var list []model.AffTransactionResponse
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("affiliate_transaction AS tb_log")
	count = count.Select("tb_log.id")
	if req.UserId != nil {
		count = count.Where("tb_log.user_id = ?", req.UserId)
	}
	if req.StatusId != nil {
		count = count.Where("tb_log.status_id = ?", req.StatusId)
	}
	if req.TypeId != nil {
		count = count.Where("tb_log.type_id = ?", req.TypeId)
	}
	if err = count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_log.id AS id, tb_log.user_id AS user_id, tb_log.income_amount AS income_amount"
		selectedFields += ", tb_log.type_id AS type_id, tb_type.name AS type_name, tb_log.status_id AS status_id, tb_status.name AS status_name"
		selectedFields += ", tb_log.transfer_at AS transfer_at, tb_log.created_at AS created_at, tb_log.updated_at AS updated_at"
		query := r.db.Table("affiliate_transaction AS tb_log")
		query = query.Joins("LEFT JOIN affiliate_transaction_type AS tb_type ON tb_type.id = tb_log.type_id")
		query = query.Joins("LEFT JOIN affiliate_transaction_status AS tb_status ON tb_status.id = tb_log.status_id")
		query = query.Select(selectedFields)
		if req.UserId != nil {
			query = query.Where("tb_log.user_id = ?", req.UserId)
		}
		if req.StatusId != nil {
			query = query.Where("tb_log.status_id = ?", req.StatusId)
		}
		if req.TypeId != nil {
			query = query.Where("tb_log.type_id = ?", req.TypeId)
		}
		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetAffTransactionListSummary(req model.AffTransactionListRequest) (*model.AffTransactionSummaryResponse, error) {
	// type AffTransactionSummaryResponse struct {
	// 	// const (
	// 	// 	AFF_TRANSACTION_TYPE_NEW_REGISTER   = int64(1)
	// 	// 	AFF_TRANSACTION_TYPE_FIRST_DEPOSIT  = int64(2)
	// 	// 	AFF_TRANSACTION_TYPE_PLAY_COMMISION = int64(3)
	// 	// )
	// 	TotalAmountNewRegister   float64 `json:"totalAmountNewRegister"`
	// 	TotalAmountFirstDeposit  float64 `json:"totalAmountFirstDeposit"`
	// 	TotalAmountPlayCommision float64 `json:"totalAmountPlayCommision"`
	// 	TotalAmount              float64 `json:"totalAmount"`
	// }
	var record model.AffTransactionSummaryResponse
	var err error

	// SELECT //
	// selectedFields := "tb_log.id AS id, tb_log.user_id AS user_id, tb_log.income_amount AS income_amount"
	// selectedFields += ", tb_log.type_id AS type_id, tb_type.name AS type_name, tb_log.status_id AS status_id, tb_status.name AS status_name"
	// selectedFields += ", tb_log.transfer_at AS transfer_at, tb_log.created_at AS created_at, tb_log.updated_at AS updated_at"

	// SELECT SUM CASE//
	selectedFields := "SUM(CASE WHEN tb_log.type_id = 1 THEN tb_log.income_amount ELSE 0 END) AS total_amount_new_register"
	selectedFields += ", SUM(CASE WHEN tb_log.type_id = 2 THEN tb_log.income_amount ELSE 0 END) AS total_amount_first_deposit"
	selectedFields += ", SUM(CASE WHEN tb_log.type_id = 3 THEN tb_log.income_amount ELSE 0 END) AS total_amount_play_commision"
	selectedFields += ", SUM(tb_log.income_amount) AS total_amount"

	query := r.db.Table("affiliate_transaction AS tb_log")
	query = query.Joins("LEFT JOIN affiliate_transaction_type AS tb_type ON tb_type.id = tb_log.type_id")
	query = query.Joins("LEFT JOIN affiliate_transaction_status AS tb_status ON tb_status.id = tb_log.status_id")
	query = query.Select(selectedFields)
	if req.UserId != nil {
		query = query.Where("tb_log.user_id = ?", req.UserId)
	}
	if req.StatusId != nil {
		query = query.Where("tb_log.status_id = ?", req.StatusId)
	}
	if req.TypeId != nil {
		query = query.Where("tb_log.type_id = ?", req.TypeId)
	}
	if err = query.
		Take(&record).
		Error; err != nil {
		return nil, err
	}

	return &record, nil
}

func (r repo) GetTotalAffTransactionPlayCommission(transIds []int64) (float64, error) {

	var totalAmount float64

	if len(transIds) > 0 {
		// Count total records for pagination purposes (without limit and offset) //
		count := r.db.Table("affiliate_transaction AS tb_log")
		count = count.Select("SUM(tb_log.income_amount) AS total_amount")
		count = count.Where("tb_log.id IN (?)", transIds)
		count = count.Where("tb_log.type_id = ?", []int64{model.AFF_TRANSACTION_TYPE_PLAY_COMMISION})
		if err := count.
			Take(&totalAmount).
			Error; err != nil {
			return totalAmount, err
		}
	}
	return totalAmount, nil
}

func (r repo) CreateAffTransaction(body model.AffTransactionCreateBody) (*int64, error) {

	if err := r.db.Table("affiliate_transaction").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) UpdateAffTransaction(id int64, body model.AffTransactionUpdateBody) error {

	if err := r.db.Table("affiliate_transaction").Where("id = ?", id).Updates(body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) SetPendingAffTransactionList(ids []int64) error {

	updateBody := map[string]interface{}{
		"status_id": model.AFF_TRANSACTION_STATUS_WAIT_CONFIRM,
	}
	sql := r.db.Table("affiliate_transaction").Where("id IN ?", ids).Where("status_id", model.AFF_TRANSACTION_STATUS_PENDING)
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) SetConfirmAffTransactionList(ids []int64) error {

	updateBody := map[string]interface{}{
		"status_id":   model.AFF_TRANSACTION_STATUS_TRANSFERRED,
		"transfer_at": time.Now(),
	}
	sql := r.db.Table("affiliate_transaction").Where("id IN ?", ids).Where("status_id", model.AFF_TRANSACTION_STATUS_WAIT_CONFIRM)
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) CreateAffTransactionWithdraw(body model.AffTransactionWithdrawCreateBody) (*int64, error) {

	if err := r.db.Table("affiliate_transaction_withdraw").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) GetAffTransactionWithdrawById(id int64) (*model.AffTransactionWithdraw, error) {

	var record model.AffTransactionWithdraw

	selectedFields := "*"
	if err := r.db.Table("affiliate_transaction_withdraw AS tb_log").
		Select(selectedFields).
		Where("tb_log.id = ?", id).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetAffTransactionWithdrawByKey(rcKey string) (*model.AffTransactionWithdraw, error) {

	var record model.AffTransactionWithdraw

	selectedFields := "*"
	if err := r.db.Table("affiliate_transaction_withdraw AS tb_log").
		Select(selectedFields).
		Where("tb_log.rc_key = ?", rcKey).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) ExpireUserAffiliateTransaction(days int64) error {

	// select Pending Transaction
	var list []model.AffTransactionResponse
	selectedFields := "user_id, SUM(income_amount) AS income_amount"
	sql1 := r.db.Table("affiliate_transaction").Where("status_id", model.AFF_TRANSACTION_STATUS_PENDING)
	if days > 0 {
		sql1 = sql1.Where("created_at <= ?", time.Now().AddDate(0, 0, int(days)*-1))
	} else {
		return errors.New("INVALID_DAYS")
	}
	sql1 = sql1.Group("user_id")
	if err := sql1.Select(selectedFields).Scan(&list).Error; err != nil {
		return err
	}

	userList := make(map[int64]struct {
		UserId             int64
		TotalPendingIncome float64
	}, 0)
	for _, v := range list {
		userList[v.UserId] = struct {
			UserId             int64
			TotalPendingIncome float64
		}{
			UserId:             v.UserId,
			TotalPendingIncome: v.IncomeAmount,
		}
	}

	// ทำรวม หรือ ทำแยก User
	tx := r.db.Begin()

	updateBody := map[string]interface{}{
		"status_id": model.AFF_TRANSACTION_STATUS_EXPIRED,
	}
	sql := tx.Table("affiliate_transaction").Where("status_id", model.AFF_TRANSACTION_STATUS_PENDING)
	if days > 0 {
		sql = sql.Where("created_at <= ?", time.Now().AddDate(0, 0, int(days)*-1))
	} else {
		tx.Rollback()
		return errors.New("INVALID_DAYS")
	}
	if err := sql.Updates(updateBody).Error; err != nil {
		tx.Rollback()
		return err
	}
	// Remove Current Commission commission_current by user_id
	for _, v := range userList {
		if v.TotalPendingIncome > 0 && days > 0 {
			sql := tx.Table("user_affiliate").Where("user_id", v.UserId)
			if err := sql.Update("commission_current", gorm.Expr("commission_current - ?", v.TotalPendingIncome)).Error; err != nil {
				tx.Rollback()
				return err
			}
		} else {
			log.Println("No Pending Income", helper.StructJson(v))
		}
	}

	tx.Commit()

	return nil
}

func (r repo) GetWebAffiliateCommissionDetail() (*model.GetWebAffiliateCommissionDetailResponse, error) {

	var record model.GetWebAffiliateCommissionDetailResponse

	selectedFields := "affiliate_commission.description AS description"
	if err := r.db.Table("affiliate_commission").
		Select(selectedFields).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}
