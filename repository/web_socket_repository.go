package repository

import (
	"bytes"
	"cybergame-api/model"
	"io"
	"log"
	"net/http"
	"os"
	"strings"

	jsoniter "github.com/json-iterator/go"
	"gorm.io/gorm"
)

func NewWebSocket(db *gorm.DB) WebSocketRepository {
	return &repo{db}
}

type WebSocketRepository interface {
	WebSocket(reqAlert model.WebScoket) error
	WebSocketUpdateAdminCorpDashboard(payload []model.WebSocketUpdateAdminDashboardPayload) error
}

func (r repo) WebSocket(reqAlert model.WebScoket) error {

	url := os.Getenv("SOCKET_DEPOSIT_URL")
	payload := model.WebScoket{
		UserID:      reqAlert.UserID,
		Amount:      reqAlert.Amount,
		PhoneNumber: reqAlert.PhoneNumber,
		FullName:    reqAlert.FullName,
		MemberCode:  reqAlert.MemberCode,
		AlertType:   reqAlert.AlertType,
	}

	// Marshal the payload into a JSON body
	payloadJSON, err := jsoniter.Marshal(payload)
	if err != nil {
		log.Println(err)
		return err
	}

	client := &http.Client{}
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(payloadJSON))
	if err != nil {
		log.Println(err)
		return err
	}

	req.Header.Add("Content-Type", "application/json")

	res, err := client.Do(req)
	if err != nil {
		log.Println(err)
		return err
	}
	defer res.Body.Close()

	_, err = io.ReadAll(res.Body)
	if err != nil {
		log.Println(err)
		return err
	}

	return nil
}

// func (r repo) WebSocketWithdraw(userId string, amount float64) error {

// 	url := os.Getenv("SOCKET_WITHDRAW_URL")
// 	payload := model.WebScoketWithdraw{
// 		UserID:  userId,
// 		Amount:  amount,
// 		Message: fmt.Sprintf("Membercode: %s ถอนเงินจำนวน: %v ", userId, amount),
// 	}

// 	// Marshal the payload into a JSON body
// 	payloadJSON, err := jsoniter.Marshal(payload)
// 	if err != nil {
// 		log.Println(err)
// 		return err
// 	}

// 	client := &http.Client{}
// 	req, err := http.NewRequest("POST", url, bytes.NewBuffer(payloadJSON))
// 	if err != nil {
// 		log.Println(err)
// 		return err
// 	}

// 	req.Header.Add("Content-Type", "application/json")

// 	res, err := client.Do(req)
// 	if err != nil {
// 		log.Println(err)
// 		return err
// 	}
// 	defer res.Body.Close()

// 	_, err = io.ReadAll(res.Body)
// 	if err != nil {
// 		log.Println(err)
// 		return err
// 	}

// 	return nil
// }

func (r repo) WebSocketUpdateAdminCorpDashboard(payload []model.WebSocketUpdateAdminDashboardPayload) error {

	// SOCKET_DEPOSIT_URL=https://dev-socket.cbgame88.com/send-deposit
	// app.post('/update-admincorp-dashboard', (req, res) => {
	//   const { list } = req.body;
	//   // Emit the "update-admincorp-dashboard" event with the received data using the server's io instance
	//   io.emit('update-admincorp-dashboard', { list });
	//   console.log(`AdminCorp dashboard updated length: ${list.length}`);
	//   res.json({ message: 'AdminCorp dashboard update sent successfully' });
	// });

	// status
	// APPROVED = อนุมัติ สีเขียว

	// update-admincorp-dashboard
	url := os.Getenv("SOCKET_DEPOSIT_URL")
	// later change ENV to SOCKET_URL
	// if strings.Contains(url, "/send-deposit") {
	url = strings.Replace(url, "/send-deposit", "/update-admincorp-dashboard", 1)
	// fmt.Println("WebSocketUpdateAdminCorpDashboard.url=", url)

	// Marshal the payload into a JSON body
	socketPayload := map[string]interface{}{
		"list": payload,
	}
	payloadJSON, err := jsoniter.Marshal(socketPayload)
	if err != nil {
		log.Println("WebSocketUpdateAdminCorpDashboard.Marshal", err)
		return err
	}

	client := &http.Client{}
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(payloadJSON))
	if err != nil {
		log.Println("WebSocketUpdateAdminCorpDashboard.NewRequest", err)
		return err
	}
	req.Header.Add("Content-Type", "application/json")

	res, err := client.Do(req)
	if err != nil {
		log.Println("WebSocketUpdateAdminCorpDashboard.Do", err)
		return err
	}
	defer res.Body.Close()

	if _, err = io.ReadAll(res.Body); err != nil {
		log.Println("WebSocketUpdateAdminCorpDashboard.ReadAll", err)
		return err
	}
	return nil
}
