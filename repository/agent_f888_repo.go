package repository

import (
	"bytes"
	"cybergame-api/helper"
	"cybergame-api/model"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"time"

	"gorm.io/gorm"
)

func NewAgentF888Repository(db *gorm.DB) AgentF888Repository {
	return &repo{db}
}

type AgentF888Repository interface {

	// AmbRegister(data model.AmbRegister) error
	// AmbLogin(data model.AmbLogin) (*model.AmbLoginResponse, error)
	// AmbChangePassword(data model.AmbChangePassword) (*model.AmbChangePasswordResponse, error)
	// AmbGetCredit(data model.AmbBalance) (*model.AmbBalanceResponse, error)
	// AmbPlay(data model.AmbStartGameRequest) (*model.AmbStartGameResponse, error)

	// AmbDepositAgent(data model.AmbDeposit) (*model.AmbDepositReponse, error)
	// AmbWithdrawAgent(data model.AmbWithdraw) (*model.AmbWithdrawReponse, error)
	// // Game
	// AmbGetGameProviderList(req model.AmbGameProviderListRequest) (*model.AmbGameProviderReponse, error)
	// AmbGetGameList(req model.AmbGameListRequest) (*model.AmbGameReponse, error)
	// // Report
	// AmbSimpleWinLose(data model.AmbSimpleWinlose) (*model.AmbSimpleWinloseResponse, error)
	// AmbGetLastPageByPath(path string) (int64, error)
	// AmbGetApistatus(req model.ApiStatusRequest) (*model.ApiStatus, error)
	// InsertAmbApiStatus(path, date string) error
	// InsertAmbPlayLog(list []model.AgentPlayLog) error
	// AmbUpdateFailed(id int64, page int) error
	// AmbUpdateSuccess(id int64) error

	// Agf888
	GetAgf888UserByMemberCode(memberCode string) (*model.Agf888UserResponse, error)
	CreateAgf888User(body model.Agf888UserCreateBody) (*int64, error)
	Agf888IncreaseUserCredit(body model.Agf888UserTransactionCreateBody) (float64, error)
	Agf888DecreaseUserCredit(body model.Agf888UserTransactionCreateBody) (float64, error)
	// Hook
	CreateF888Webhook(body model.HookF888CbgameCreateBody) (*int64, error)
	// EP
	EndpointAuth001(req model.F888Auth001Request) (*model.F888Auth001Response, error)
	EndpointGame001(userToken string) (*model.F888Game001Response, error)
	EndpointGame002(userToken string, req model.F888Game002Request) (*model.F888Game002Response, error)
}

func (r repo) GetAgf888UserByMemberCode(memberCode string) (*model.Agf888UserResponse, error) {

	var record model.Agf888UserResponse

	selectedFields := "tb_user.id AS id, tb_user.member_code AS member_code, tb_user.fullname AS fullname, tb_user.credit AS credit, tb_user.remark AS remark"
	selectedFields += ", tb_user.last_action_at AS last_action_at, tb_user.created_at AS created_at, tb_user.updated_at AS updated_at"
	if err := r.db.Table("agf888_user AS tb_user").
		Select(selectedFields).
		Where("tb_user.member_code = ?", memberCode).
		Where("tb_user.deleted_at IS NULL").
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) CreateAgf888User(body model.Agf888UserCreateBody) (*int64, error) {

	if err := r.db.Table("agf888_user").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) Agf888IncreaseUserCredit(body model.Agf888UserTransactionCreateBody) (float64, error) {

	tx := r.db.Begin()

	if err := tx.Table("agf888_user_transaction").Create(&body).Error; err != nil {
		tx.Rollback()
		return 0, err
	}

	if err := tx.Table("agf888_user").Where("id = ?", body.UserId).Update("credit", gorm.Expr("credit + ?", body.CreditAmount)).Error; err != nil {
		tx.Rollback()
		return 0, err
	}

	tx.Commit()

	var user model.Agf888User
	if err := r.db.Table("agf888_user").Select("credit").Where("id = ?", body.UserId).Take(&user).Error; err != nil {
		return 0, err
	}
	return user.Credit, nil
}

func (r repo) Agf888DecreaseUserCredit(body model.Agf888UserTransactionCreateBody) (float64, error) {

	tx := r.db.Begin()

	if err := tx.Table("agf888_user_transaction").Create(&body).Error; err != nil {
		tx.Rollback()
		return 0, err
	}

	if err := tx.Table("agf888_user").Where("id = ?", body.UserId).Update("credit", gorm.Expr("credit - ?", body.CreditAmount)).Error; err != nil {
		tx.Rollback()
		return 0, err
	}

	tx.Commit()

	var user model.Agf888User
	if err := r.db.Table("agf888_user").Select("credit").Where("id = ?", body.UserId).Take(&user).Error; err != nil {
		return 0, err
	}
	return user.Credit, nil
}

func (r repo) CreateF888Webhook(body model.HookF888CbgameCreateBody) (*int64, error) {

	if err := r.db.Table("agf888_hook").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) EndpointAuth001(req model.F888Auth001Request) (*model.F888Auth001Response, error) {

	// log.Println("EndpointAuth001 req ------> ", helper.StructJson(req))

	ep := os.Getenv("AGF88_ENDPOINT")
	epUser := os.Getenv("AGF88_API_USERNAME")
	epKey := os.Getenv("AGF88_API_KEY")
	if ep == "" || epUser == "" || epKey == "" {
		return nil, errors.New("ENV_AGF88_T3_NOT_SET")
	}

	url := fmt.Sprintf("%s/api/v1/user/authorize", ep)

	// "Basic"+" "+ encoding base64(API Username + “:” + API Key)
	token := base64.StdEncoding.EncodeToString([]byte(epUser + ":" + epKey))
	auth := "Basic " + token

	// fmt.Println("DEBUG.EndpointAuth001 url ------> ", url)
	// fmt.Println("DEBUG.EndpointAuth001 auth ------> ", auth)

	// [SYSLOG] CALL LOG
	sysLogId, err := r.CreateAgentLog(model.AgentLogCreateBody{
		Name:   "EndpointAuth001",
		Status: "PENDING",
		JsonRequest: helper.StructJson(map[string]interface{}{
			"endpoint": url,
			"epUser":   epUser,
			"req":      req,
		}),
	})
	if err != nil {
		log.Println("EndpointAuth001.CreateAgentLog error ------> ", err.Error())
	}

	client := &http.Client{}
	client.Timeout = 3 * time.Second
	jsonBody, _ := json.Marshal(req)
	reqBody := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest("POST", url, reqBody)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("Authorization", auth)
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println("EndpointAuth001.CLIENT_CALL_ERROR", err.Error())
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type": "CLIENT_CALL_ERROR",
				"error_msg":  err.Error(),
			}),
		}); sysLogErr != nil {
			log.Println("EndpointAuth001.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, errors.New("CLIENT_CALL_ERROR")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println("EndpointAuth001.RESPONSE_READ_ERROR", err)
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type": "RESPONSE_READ_ERROR",
				"error_msg":  err.Error(),
			}),
		}); sysLogErr != nil {
			log.Println("EndpointAuth001.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, errors.New("RESPONSE_READ_ERROR")
	}

	// fmt.Println("DEBUG.EndpointAuth001.responseData", string(responseData))
	// DEBUG.EndpointAuth001.responseData {"status":5000,"error":"Cannot destructure property 'username' of 'response.data.data' as it is undefined."}

	if response.StatusCode != 200 {
		log.Println("EndpointAuth001.HTTP_NOT_200", response.StatusCode)
		log.Println("EndpointAuth001.response_data", string(responseData))
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type":    "INVALID_RESPONSE_CODE",
				"error_msg":     "HTTP_NOT_200",
				"http_code":     response.StatusCode,
				"response_data": string(responseData),
			}),
		}); sysLogErr != nil {
			log.Println("EndpointAuth001.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, errors.New("INVALID_RESPONSE_CODE")
	}

	var result model.F888Auth001Response
	if errJson := json.Unmarshal(responseData, &result); errJson != nil {
		log.Println("EndpointAuth001.CANT_PARSE_RESPONSE_DATA ------> ", string(responseData))
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type":    "Unmarshal_ERROR",
				"error_msg":     errJson.Error(),
				"response_data": string(responseData),
			}),
		}); sysLogErr != nil {
			log.Println("EndpointAuth001.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, errors.New("CANT_PARSE_RESPONSE_DATA")
	}
	// if result.Status != 1000 {
	// 	log.Println("EndpointAuth001 result ------> ", helper.StructJson(result))
	// 	// CANT_CHANGE_PASSWORD_403 : Permission denied.
	// 	// CANT_CHANGE_PASSWORD_2007 : Password is invalid.
	// 	// CANT_CHANGE_PASSWORD_12 : Password is same.
	// 	// [SYSLOG]
	// 	if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
	// 		Id: *sysLogId,
	// 		JsonReponse: helper.StructJson(map[string]interface{}{
	// 			"error_type":     "CANT_CHANGE_PASSWORD",
	// 			"error_msg":      result.Msg,
	// 			"response_model": helper.StructJson(result),
	// 		}),
	// 	}); sysLogErr != nil {
	// 		log.Println("EndpointAuth001.SetAgentLogError error ------> ", sysLogErr.Error())
	// 	}
	// 	if result.Status != 12 {
	// 		return nil, fmt.Errorf("CANT_CHANGE_PASSWORD_%d", result.Status)
	// 	}
	// }

	// [SYSLOG]
	if sysLogErr := r.SetAgentLogSuccess(model.AgentLogUpdateBody{
		Id:          *sysLogId,
		JsonReponse: helper.StructJson(result),
	}); sysLogErr != nil {
		log.Println("EndpointAuth001.SetAgentLogSuccess error ------> ", sysLogErr.Error())
	}
	return &result, nil
}

func (r repo) EndpointGame001(userToken string) (*model.F888Game001Response, error) {

	fmt.Println("DEBUG.EndpointGame001 userToken ------> *****")

	if userToken == "" {
		return nil, errors.New("EMPTY_USER_TOKEN")
	}

	ep := os.Getenv("AGF88_ENDPOINT")
	if ep == "" {
		return nil, errors.New("ENV_AGF88_T3_NOT_SET")
	}

	url := fmt.Sprintf("%s/api/v1/game/get-game-list", ep)
	auth := "Bearer " + userToken

	// [SYSLOG] CALL LOG
	sysLogId, err := r.CreateAgentLog(model.AgentLogCreateBody{
		Name:   "EndpointGame001",
		Status: "PENDING",
		JsonRequest: helper.StructJson(map[string]interface{}{
			"endpoint": url,
		}),
	})
	if err != nil {
		log.Println("EndpointGame001.CreateAgentLog error ------> ", err.Error())
	}

	client := &http.Client{}
	client.Timeout = 3 * time.Second
	reqExternal, _ := http.NewRequest("POST", url, nil)
	reqExternal.Header.Set("Authorization", auth)
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println("EndpointGame001.CLIENT_CALL_ERROR", err.Error())
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type": "CLIENT_CALL_ERROR",
				"error_msg":  err.Error(),
			}),
		}); sysLogErr != nil {
			log.Println("EndpointGame001.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, errors.New("CLIENT_CALL_ERROR")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println("EndpointGame001.RESPONSE_READ_ERROR", err)
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type": "RESPONSE_READ_ERROR",
				"error_msg":  err.Error(),
			}),
		}); sysLogErr != nil {
			log.Println("EndpointGame001.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, errors.New("RESPONSE_READ_ERROR")
	}

	fmt.Println("DEBUG.EndpointGame001.responseData", string(responseData))

	if response.StatusCode != 200 {
		log.Println("EndpointGame001.HTTP_NOT_200", response.StatusCode)
		log.Println("EndpointGame001.response_data", string(responseData))
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type":    "INVALID_RESPONSE_CODE",
				"error_msg":     "HTTP_NOT_200",
				"http_code":     response.StatusCode,
				"response_data": string(responseData),
			}),
		}); sysLogErr != nil {
			log.Println("EndpointGame001.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, errors.New("INVALID_RESPONSE_CODE")
	}

	var result model.F888Game001Response
	if errJson := json.Unmarshal(responseData, &result); errJson != nil {
		log.Println("EndpointGame001.CANT_PARSE_RESPONSE_DATA ------> ", string(responseData))
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type":    "Unmarshal_ERROR",
				"error_msg":     errJson.Error(),
				"response_data": string(responseData),
			}),
		}); sysLogErr != nil {
			log.Println("EndpointGame001.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, errors.New("CANT_PARSE_RESPONSE_DATA")
	}
	// if result.Status != 1000 {
	// 	log.Println("EndpointGame001 result ------> ", helper.StructJson(result))
	// 	// CANT_CHANGE_PASSWORD_403 : Permission denied.
	// 	// CANT_CHANGE_PASSWORD_2007 : Password is invalid.
	// 	// CANT_CHANGE_PASSWORD_12 : Password is same.
	// 	// [SYSLOG]
	// 	if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
	// 		Id: *sysLogId,
	// 		JsonReponse: helper.StructJson(map[string]interface{}{
	// 			"error_type":     "CANT_CHANGE_PASSWORD",
	// 			"error_msg":      result.Msg,
	// 			"response_model": helper.StructJson(result),
	// 		}),
	// 	}); sysLogErr != nil {
	// 		log.Println("EndpointGame001.SetAgentLogError error ------> ", sysLogErr.Error())
	// 	}
	// 	if result.Status != 12 {
	// 		return nil, fmt.Errorf("CANT_CHANGE_PASSWORD_%d", result.Status)
	// 	}
	// }

	// [SYSLOG]
	if sysLogErr := r.SetAgentLogSuccess(model.AgentLogUpdateBody{
		Id:          *sysLogId,
		JsonReponse: helper.StructJson(result),
	}); sysLogErr != nil {
		log.Println("EndpointGame001.SetAgentLogSuccess error ------> ", sysLogErr.Error())
	}
	return &result, nil
}

func (r repo) EndpointGame002(userToken string, req model.F888Game002Request) (*model.F888Game002Response, error) {

	// log.Println("EndpointGame002 req ------> ", helper.StructJson(req))

	if userToken == "" {
		return nil, errors.New("EMPTY_USER_TOKEN")
	}

	ep := os.Getenv("AGF88_ENDPOINT")
	if ep == "" {
		return nil, errors.New("ENV_AGF88_T3_NOT_SET")
	}

	url := fmt.Sprintf("%s/api/v1/game/get-game-list", ep)
	auth := "Bearer " + userToken

	// [SYSLOG] CALL LOG
	sysLogId, err := r.CreateAgentLog(model.AgentLogCreateBody{
		Name:   "EndpointGame002",
		Status: "PENDING",
		JsonRequest: helper.StructJson(map[string]interface{}{
			"endpoint": url,
			"req":      req,
		}),
	})
	if err != nil {
		log.Println("EndpointGame002.CreateAgentLog error ------> ", err.Error())
	}

	client := &http.Client{}
	client.Timeout = 3 * time.Second
	jsonBody, _ := json.Marshal(req)
	reqBody := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest("POST", url, reqBody)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("Authorization", auth)
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println("EndpointGame002.CLIENT_CALL_ERROR", err.Error())
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type": "CLIENT_CALL_ERROR",
				"error_msg":  err.Error(),
			}),
		}); sysLogErr != nil {
			log.Println("EndpointGame002.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, errors.New("CLIENT_CALL_ERROR")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println("EndpointGame002.RESPONSE_READ_ERROR", err)
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type": "RESPONSE_READ_ERROR",
				"error_msg":  err.Error(),
			}),
		}); sysLogErr != nil {
			log.Println("EndpointGame002.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, errors.New("RESPONSE_READ_ERROR")
	}

	// fmt.Println("DEBUG.EndpointGame002.responseData", string(responseData))

	if response.StatusCode != 200 {
		log.Println("EndpointGame002.HTTP_NOT_200", response.StatusCode)
		log.Println("EndpointGame002.response_data", string(responseData))
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type":    "INVALID_RESPONSE_CODE",
				"error_msg":     "HTTP_NOT_200",
				"http_code":     response.StatusCode,
				"response_data": string(responseData),
			}),
		}); sysLogErr != nil {
			log.Println("EndpointGame002.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, errors.New("INVALID_RESPONSE_CODE")
	}

	var result model.F888Game002Response
	if errJson := json.Unmarshal(responseData, &result); errJson != nil {
		log.Println("EndpointGame002.CANT_PARSE_RESPONSE_DATA ------> ", string(responseData))
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type":    "Unmarshal_ERROR",
				"error_msg":     errJson.Error(),
				"response_data": string(responseData),
			}),
		}); sysLogErr != nil {
			log.Println("EndpointGame002.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, errors.New("CANT_PARSE_RESPONSE_DATA")
	}
	// if result.Status != 1000 {
	// 	log.Println("EndpointGame002 result ------> ", helper.StructJson(result))
	// 	// CANT_CHANGE_PASSWORD_403 : Permission denied.
	// 	// CANT_CHANGE_PASSWORD_2007 : Password is invalid.
	// 	// CANT_CHANGE_PASSWORD_12 : Password is same.
	// 	// [SYSLOG]
	// 	if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
	// 		Id: *sysLogId,
	// 		JsonReponse: helper.StructJson(map[string]interface{}{
	// 			"error_type":     "CANT_CHANGE_PASSWORD",
	// 			"error_msg":      result.Msg,
	// 			"response_model": helper.StructJson(result),
	// 		}),
	// 	}); sysLogErr != nil {
	// 		log.Println("EndpointGame002.SetAgentLogError error ------> ", sysLogErr.Error())
	// 	}
	// 	if result.Status != 12 {
	// 		return nil, fmt.Errorf("CANT_CHANGE_PASSWORD_%d", result.Status)
	// 	}
	// }

	// [SYSLOG]
	if sysLogErr := r.SetAgentLogSuccess(model.AgentLogUpdateBody{
		Id:          *sysLogId,
		JsonReponse: helper.StructJson(result),
	}); sysLogErr != nil {
		log.Println("EndpointGame002.SetAgentLogSuccess error ------> ", sysLogErr.Error())
	}
	return &result, nil
}
