package repository

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"errors"
	"fmt"
	"log"
	"strings"
	"time"

	"gorm.io/gorm"
)

func NewUserRepository(db *gorm.DB) UserRepository {
	return &repo{db}
}

type UserRepository interface {
	GetUserLoginLogs(req model.GetUserLogListRequest) ([]model.UserLoginLog, int64, error)
	GetUser(id int64) (*model.UserDetail, error)
	GetUserInfo(id int64) (*model.UserInfo, error)
	GetPasswordByUserId(id int64) (*string, error)
	GetUserForGenMember(id int64) (*model.UserDetail, error)
	FrontGetUser(id int64) (*model.UserMe, error)
	GetUserList(query model.UserListQuery) ([]model.UserList, *int64, error)
	// REF-USERTIER
	GetTierSetting(name string) (*model.UserTierSetting, error)
	GetUserTierSettingList(req model.UserTierSettingListRequest) ([]model.UserTierSettingItem, int64, error)
	// Excel
	GetUserListForExcel(query model.UserListQueryForExcel) ([]model.UserListForExcel, *int64, error)
	GetUpdateLogs(query model.UserUpdateQuery) (*[]model.UserUpdateLogResponse, *int64, error)
	GetRegisterOtpByPhone(data model.UserSendOtpBody) (*model.UserOtp, error)
	GetForgetOtpByPhone(data model.UserSendOtpBody) (*model.UserOtp, error)
	GetUserOtpLogByOtpId(data model.UserResendOtpBody) (*model.UserOtp, error)
	GetForgetOtpLogByOtpId(otpId string) (*model.UserOtp, error)
	GetFrontUserByPhone(phone string) (*model.UserResponse, error)
	GetUserById(id int64) (*model.UserResponse, error)
	GetUserIdByRef(id int64) (*model.UserGetDataByRef, error)
	GetUserIdByRefCode(refCode string) (*model.UserGetDataByRef, error)
	GetUserIdBySaleCode(saleCode string) (*model.UserGetDataByRef, error)
	GetRefTypeByRef(userId int64) (string, error)
	GetMemberCode(id int64) (*string, error)
	GetBankList() ([]model.UserBank, error)
	GetUserIdByMemberList(memberList []string) ([]model.UserMemberList, error)
	CheckUserByPhone(phone string) (*model.UserResponse, error)
	CheckUserByAccountNo(accountNo string) (*model.UserResponse, error)
	CheckUserPhone(phone string) (bool, error)
	CheckUserById(id int64) (bool, error)
	CountUser() (*int64, error)
	FrontCheckUserBank(bankNumber string) (bool, error)
	FrontCheckUserTrueWallet(trueNumber string) (bool, error)
	FrontCountUser() (*int64, error)
	CreateUser(user model.UserFormCreate) (*int64, error)
	CreateUserOtpOfForget(data model.UserOtpBody) error
	CreateUserRegisterOtp(data model.UserOtpBody) error
	GetLocalRegisterOtpByPhone(phone string) (*model.UserOtp, error)
	CreateLocalRegisterOtp(data model.UserOtpBody) error
	UpdateUserOtpLocalPin(id int64, localPin string) error

	UserRegister(body model.UserRegisterForm, afObj model.Affiliate, credit float64, refTypeId int64) (*int64, error)
	UpdateUser(userId int64, data model.UpdateUserBody) error
	UpdateUserPassword(userId int64, data model.UserUpdatePassword) error
	UpdateMemberCode(userId int64, data model.UserUpdateMemberAndRef) error
	UpdateLogin(userId int64, ip string) error
	FrontUpdateUser(userId int64, data model.FrontUserUpdate) error
	FrontUpdatePassword(userId int64, password string) error
	// UpdateCredit(userId, trxId int64, data model.UserUpdateCredit, tranType string) (*model.ResponseGameCredit, error)
	DeleteUser(user model.UserResponse) error
	UserUpdateCredit(id int64, credit float64) error
	// REF:WEB_SETTING configuration web backoffice
	GetUserRegisterConfiguration() (*model.ConfigurationUserRegisterResponse, error)
	GetConfiguration() (*model.ConfigurationResponse, error)
	// InactiveUser
	GetInactiveUserList(req model.InactiveUserListRequest) ([]model.InactiveUserResponse, int64, error)
	RemoveInactiveUserById(userId int64, dateType string) error
	GetInactiveUserListForExcel(req model.InactiveUserListForExcelRequest) ([]model.InactiveUserResponse, int64, error)

	GetUserForGenmemberByUserId(userId int64) (*model.GetUserForGenmemberByUserId, error)
	GetUserAllianceInfoByUserId(userId int64) (*model.UserAllianceInfo, error)
	GetUserForGenmemberByMemberCode(memberCode string) (*model.GetUserForGenmemberByUserId, error)
	UpdateGenMemberCodeToAffilate(userId int64) error
	CreateAffiliateMember(userId int64, refId int64) error

	// WebSocket(reqAlert model.WebScoket) error
	GetBankById(id int64) (*model.Bank, error)
	GetAccountInfoFastbank(body model.AccountInfoFastbankRequest) (*model.AccountInfoFastbankResponse, error)

	// ChangeRefBy-MoveRefBy
	MoveAffiliateUserMember(body model.MoveAffiliateUserMemberBody, userlogInfo model.UserUpdateLogs) error
	RemoveAffiliateUserMember(body model.MoveAffiliateUserMemberBody, userlogInfo model.UserUpdateLogs) error
	// Admin
	GetAdminById(id int64) (*model.Admin, error)
	// AGENT
	GetAgentInfo() (*model.AgentInfo, error)
	IncrementTotal() error
	// AgentProvider - AGC
	AgcRegister(data model.AgcRegister) error
	AgcLogin(data model.AgcLogin) (*model.AgcLoginResponse, error)
	AgcChangePassword(data model.AgcChangePassword) (*model.AgcChangePasswordResponse, error)
	AgcGetCredit(data model.AgcBalance) (*model.AgcBalanceResponse, error)
	// AgentProvider - AMB
	AmbRegister(data model.AmbRegister) error
	AmbLogin(data model.AmbLogin) (*model.AmbLoginResponse, error)
	AmbChangePassword(data model.AmbChangePassword) (*model.AmbChangePasswordResponse, error)
	AmbGetCredit(data model.AmbBalance) (*model.AmbBalanceResponse, error)
	// REF-[SYSLOG]
	CreateSystemLog(body model.SystemLogCreateBody) (*int64, error)
	SetSystemLogSuccess(body model.SystemLogUpdateBody) error
	SetSystemLogError(body model.SystemLogUpdateBody) error
	// DEPOSIT
	CreateMissingAffiliate(userId int64) error
	CountUserTransactionDeposit(userId int64) (int64, error)

	// USER WITHDRAW SETTING
	UserWithdrawSettingCreate(req model.UserWithdrawSettingCreateRequest) (int64, error)
	UserWithdrawSettingUpdateByUserId(req model.UserWithdrawSettingUpdateByUserIdRequest) error
	UserWithdrawSettingGetById(id int64) (*model.UserWithdrawSettingGetByUserIdResponse, error)
	UserWithdrawSettingGetByUserId(userId int64) (*model.UserWithdrawSettingGetByUserIdResponse, error)
	UserWithdrawConfigGetByKey(key string) (*model.UserWithdrawConfig, error)
	UserWithdrawSettingGetListByUserId(userId []int64) ([]model.UserWithdrawSettingGetByUserIdResponse, error)

	// Line-Login
	CreateLineLoginLog(body model.LineLoginLogCreateBody) (*int64, error)
	CheckUserByLineUuid(lineUuid string) (*model.UserResponse, error)
	ValidateLineToken(token string) (*model.LineValidateTokenResponse, error)
	GetFrontUserByLineUuid(lineUuid string) (*model.UserResponse, error)
	// UserLineRegister(body model.UserRegisterForm, afObj model.Affiliate, credit float64, refTypeId int64) (*int64, error)

	//UserUpdateLog
	UserUpdateLog(changes []model.UserUpdateLogs) error
	GetChannelOption() ([]model.RecommendList, error)
	GetBanksOption() ([]model.BankResponse, error)

	// SingleSession
	SetUserSingleSession(userId int64, token string) error

	CronjobDeleteUserLoginLog() error

	// Report
	GetRecommendList(query model.RecommendQuery) ([]model.RecommendList, int64, error)
	GetUserRefChannelListGraph(req model.UserChannelSummaryGraphRequest) ([]model.UserChannelTotalCount, error)
	GetUserChannelListGraph(req model.UserChannelSummaryGraphRequest) ([]model.UserChannelTotalCount, error)

	// [ADMIN_LOG]
	CreateAdminLog(body model.AdminLogCreateBody) (*int64, error)
	// REFCODE
	GetUserAllianceInfoList(req model.UserAllianceListRequest) ([]model.UserAlliance, int64, error)

	// turn over
	CreateTurnoverUserStatement(body model.TurnoverUserStatementCreateBody) (*int64, error)
	UpdateTurnoverUserStatement(id int64, body model.TurnoverUserStatementUpdateBody) error
	CreateTurnoverUserWithdrawLog(body model.CreateTurnoverUserWithdrawLog) (*int64, error)

	// REF-Summary per User
	GetTotalPlaylogSummaryByUserId(userId int64) (*model.UserPlaylogSummary, error)
	GetUserPlaylogSummaryByUserId(userId int64) (*model.UserPlaylogSummaryResponse, error)
	GetUserPlaylogSummaryList(req model.UserPlaylogSummaryListRequest) ([]model.UserPlaylogSummaryResponse, int64, error)
	CreateUserPlaylogSummary(body model.UserPlaylogSummaryCreateBody) (*int64, error)
	UpdateUserPlaylogSummary(id int64, body model.UserPlaylogSummaryUpdateBody) error

	GetHeadUsersRefUserId(userId int64) (*model.UserResponse, error)
	AlGetCommissionSettingUser(userId int64) (*model.AllianceCommissionSettingUser, error)

	// RACE_CONDITION_BLOCKER
	GetRaceActionByActionKey(actionKey string) (*model.RaceAction, error)
	GetRaceActionIdByActionKey(actionKey string) (*int64, error)
	CreateRaceCondition(body model.RaceActionCreateBody) (int64, error)

	// RAW PLAYLOG
	GetAgentCtwCallbackPlayLogList(req model.AgentCallbackPlayLogListRequest) ([]model.AgentCtwCallbackPlayLogListResponse, int64, error)
	GetAgentPgHardCallbackPlayLogList(req model.AgentCallbackPlayLogListRequest) ([]model.AgentPgHardCallbackPlayLogListResponse, int64, error)
	AgcWinloseReport(data model.AgcWinloseReportRequest) (*model.AgcWinloseReportResponse, error)
	AgcTicketReport(data model.AgcTicketsFetchPagingRequest) (*model.AgcTicketReportResponse, error)

	// ACTION
	GetCronActionById(id int64) (*model.CronAction, error)
	GetCronActionByActionKey(actionKey string) (*model.CronAction, error)
	CreateCronAction(body model.CronActionCreateBody) (int64, error)
	SetSuccessCronAction(id int64, remark string) error
	SetFailCronAction(id int64, remark string) error
}

func (r repo) UserUpdateCredit(id int64, credit float64) error {

	if err := r.db.Table("user").Where("id = ?", id).Update("credit", credit).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetUserLoginLogs(req model.GetUserLogListRequest) ([]model.UserLoginLog, int64, error) {
	var total int64
	var logs []model.UserLoginLog
	var err error

	count := r.db.Table("user_login_log")
	count = count.Select("id")
	count = count.Where("user_id = ?", req.UserId)
	if err := count.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if total > 0 {
		query := r.db.Table("user_login_log")
		query = query.Where("user_id = ?", req.UserId)
		query = query.Select("id, user_id, ip, created_at")

		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}

		if err = query.
			Order("created_at DESC").
			Offset(req.Limit * req.Page).
			Scan(&logs).
			Error; err != nil {
			return nil, 0, err
		}
	}

	return logs, total, nil
}

func (r repo) GetUserList(query model.UserListQuery) ([]model.UserList, *int64, error) {

	var list []model.UserList
	var total int64

	// user_repository.go:261 SLOW SQL >= 200ms
	// user_repository.go:256 SLOW SQL >= 200ms แก้ ตอน COUNT ไม่ได้ พอ Where 2 อย่างแล้ว ช้า

	// Set UserTier Rank to Amount
	useSpecielSetting := false
	filterByUserTier := false
	spSetting, err := r.GetTierSetting("use_speciel_setting")
	if err == nil && spSetting.FromValue == 1 {
		useSpecielSetting = true
		// Allow Filter
		if query.DepositRankId != nil {
			depTier, err := r.GetUserTierSettingById(*query.DepositRankId)
			if err == nil {
				query.DepositTotalAmountMin = &depTier.FromValue
				query.DepositTotalAmountMax = &depTier.ToValue
				filterByUserTier = true
			}
		}
		if query.TurnOverRankId != nil {
			turnTier, err := r.GetUserTierSettingById(*query.TurnOverRankId)
			if err == nil {
				query.TurnOverTotalAmountMin = &turnTier.FromValue
				query.TurnOverTotalAmountMax = &turnTier.ToValue
				filterByUserTier = true
			}
		}
	}

	// แจ้งมิจฉาชีพ
	var scammerNumbers model.ScammerNumberListResponse
	if query.UserCategory == "scammer" {
		if masterResp, err := r.GetScammerNumberList(); err != nil {
			return nil, nil, err
		} else {
			scammerNumbers.PhoneList = masterResp.PhoneList
			scammerNumbers.BankNumberList = masterResp.BankNumberList
		}
		// No Scammer
		if len(scammerNumbers.PhoneList) == 0 && len(scammerNumbers.BankNumberList) == 0 {
			return nil, &total, nil
		}
	}

	execTotal := r.db.Table("user").Select("user.id")
	if query.Search != "" {
		execTotal = execTotal.Joins("LEFT JOIN bank ON bank.id = user.bank_id")
		execTotal = execTotal.Joins("LEFT JOIN recommend_channel ON recommend_channel.id = user.channel_id")
		execTotal = execTotal.Where("user.username LIKE ? OR user.phone LIKE ? OR user.member_code LIKE ? OR user.bank_account LIKE ? OR user.fullname LIKE ? OR recommend_channel.title LIKE ? OR bank.name LIKE ?", "%"+query.Search+"%", "%"+query.Search+"%", "%"+query.Search+"%", "%"+query.Search+"%", "%"+query.Search+"%", "%"+query.Search+"%", "%"+query.Search+"%")
	}
	if query.From != "" {
		startDateAtBkk, err := r.ParseBodBkk(query.From)
		if err != nil {
			return nil, nil, err
		}
		execTotal = execTotal.Where("user.created_at >= ? ", startDateAtBkk)
	}
	if query.To != "" {
		endDateAtBkk, err := r.ParseEodBkk(query.To)
		if err != nil {
			return nil, nil, err
		}
		execTotal = execTotal.Where("user.created_at <=  ?", endDateAtBkk)
	}
	if query.UserCategory != "" {
		// แจ้งมิจฉาชีพ
		if query.UserCategory == "member" {
			execTotal = execTotal.Where("user.member_code IS NOT NULL AND user.member_code != ?", "")
		} else if query.UserCategory == "non-member" {
			execTotal = execTotal.Where("user.member_code IS NULL OR user.member_code = ?", "")
		} else if query.UserCategory == "scammer" {
			if len(scammerNumbers.PhoneList) > 0 && len(scammerNumbers.BankNumberList) > 0 {
				execTotal = execTotal.Where(r.db.Where("user.phone IN ?", scammerNumbers.PhoneList).Or("user.bank_account IN ?", scammerNumbers.BankNumberList))
			} else if len(scammerNumbers.PhoneList) > 0 {
				execTotal = execTotal.Where("user.phone IN ?", scammerNumbers.PhoneList)
			} else if len(scammerNumbers.BankNumberList) > 0 {
				execTotal = execTotal.Where("user.bank_account IN ?", scammerNumbers.BankNumberList)
			}
		}
	}
	if useSpecielSetting && filterByUserTier {
		execTotal = execTotal.Joins("LEFT JOIN user_tier_data AS tb_user_tier_data ON tb_user_tier_data.user_id = user.id")
		if query.DepositTotalAmountMin != nil && query.DepositTotalAmountMax != nil {
			execTotal = execTotal.Where("IFNULL(tb_user_tier_data.total_deposit, 0) >= ? AND IFNULL(tb_user_tier_data.total_deposit, 0) <= ?", *query.DepositTotalAmountMin, *query.DepositTotalAmountMax)
		}
		if query.TurnOverTotalAmountMin != nil && query.TurnOverTotalAmountMax != nil {
			execTotal = execTotal.Where("IFNULL(tb_user_tier_data.total_turn, 0) >= ? AND IFNULL(tb_user_tier_data.total_turn, 0) <= ?", *query.TurnOverTotalAmountMin, *query.TurnOverTotalAmountMax)
		}
	}
	execTotal = execTotal.Where("user.deleted_at IS NULL")
	if err := execTotal.Count(&total).Error; err != nil {
		return nil, nil, err
	}
	if total == 0 {
		return nil, &total, nil
	}

	selectedFields := "user.id, user.phone, user.member_code, user_type.name AS type, user.fullname, user.bank_account, user.credit, user.ip, user.ip_registered, user.created_at, user.updated_at"
	selectedFields += ", user.logedin_at, user.note, bank.name AS bank_name, user.bank_id, admin.username AS created_by, user.channel_id, tb_ref.member_code AS ref_by, tb_ref_user_type.label_th AS channel"
	selectedFields += ", user.login_type AS login_type, user.user_type_id AS user_type_id"
	if useSpecielSetting {
		selectedFields += ", IFNULL(tb_user_tier_data.total_deposit, 0) AS total_deposit_amount, IFNULL(tb_user_tier_data.total_turn, 0) AS total_turn_over_amount"
	}

	exec := r.db.Table("user").
		Joins("LEFT JOIN bank ON bank.id = user.bank_id").
		Joins("LEFT JOIN user tb_ref ON tb_ref.id = user.ref_by").
		Joins("LEFT JOIN admin ON admin.id = user.created_by").
		Joins("LEFT JOIN user_type ON user_type.id = user.user_type_id").
		Joins("LEFT JOIN user_type tb_ref_user_type ON tb_ref_user_type.id = tb_ref.user_type_id").
		Joins("LEFT JOIN recommend_channel ON recommend_channel.id = user.channel_id").
		Select(selectedFields)

	if query.Search != "" {
		exec = exec.Where("user.username LIKE ? OR user.phone LIKE ? OR user.member_code LIKE ? OR user.bank_account LIKE ? OR user.fullname LIKE ? OR recommend_channel.title LIKE ? OR bank.name LIKE ?", "%"+query.Search+"%", "%"+query.Search+"%", "%"+query.Search+"%", "%"+query.Search+"%", "%"+query.Search+"%", "%"+query.Search+"%", "%"+query.Search+"%")
	}
	if query.From != "" {
		startDateAtBkk, err := r.ParseBodBkk(query.From)
		if err != nil {
			return nil, nil, err
		}
		exec = exec.Where("user.created_at >= ? ", startDateAtBkk)
	}
	if query.To != "" {
		endDateAtBkk, err := r.ParseEodBkk(query.To)
		if err != nil {
			return nil, nil, err
		}
		exec = exec.Where("user.created_at <=  ?", endDateAtBkk)
	}
	if query.UserCategory != "" {
		if query.UserCategory == "member" {
			exec = exec.Where("user.member_code IS NOT NULL AND user.member_code != ?", "")
		} else if query.UserCategory == "non-member" {
			exec = exec.Where("user.member_code IS NULL OR user.member_code = ?", "")
		} else if query.UserCategory == "scammer" {
			if len(scammerNumbers.PhoneList) > 0 && len(scammerNumbers.BankNumberList) > 0 {
				exec = exec.Where(r.db.Where("user.phone IN ?", scammerNumbers.PhoneList).Or("user.bank_account IN ?", scammerNumbers.BankNumberList))
			} else if len(scammerNumbers.PhoneList) > 0 {
				exec = exec.Where("user.phone IN ?", scammerNumbers.PhoneList)
			} else if len(scammerNumbers.BankNumberList) > 0 {
				exec = exec.Where("user.bank_account IN ?", scammerNumbers.BankNumberList)
			}
		}
	}
	if useSpecielSetting {
		exec = exec.Joins("LEFT JOIN user_tier_data AS tb_user_tier_data ON tb_user_tier_data.user_id = user.id")
		if query.DepositTotalAmountMin != nil && query.DepositTotalAmountMax != nil {
			exec = exec.Where("IFNULL(tb_user_tier_data.total_deposit, 0) >= ? AND IFNULL(tb_user_tier_data.total_deposit, 0) <= ?", *query.DepositTotalAmountMin, *query.DepositTotalAmountMax)
		}
		if query.TurnOverTotalAmountMin != nil && query.TurnOverTotalAmountMax != nil {
			exec = exec.Where("IFNULL(tb_user_tier_data.total_turn, 0) >= ? AND IFNULL(tb_user_tier_data.total_turn, 0) <= ?", *query.TurnOverTotalAmountMin, *query.TurnOverTotalAmountMax)
		}
	}
	exec = exec.Where("user.deleted_at IS NULL")
	if err := exec.
		Limit(query.Limit).
		Offset(query.Limit * query.Page).
		Order("user.id DESC").
		Find(&list).
		Error; err != nil {
		return nil, nil, err
	}

	channelIds := []int64{}
	for _, v := range list {
		if v.RefBy == "" && v.ChannelId != 0 {
			channelIds = append(channelIds, v.ChannelId)
		}
	}

	if len(channelIds) == 0 {
		return list, &total, nil
	}

	channelIds = helper.RemoveDuplicateInt64(channelIds)

	var channelList []model.UserChannel

	if err := r.db.Table("recommend_channel").
		Select("id, title").
		Where("id IN ?", channelIds).
		Scan(&channelList).
		Error; err != nil {
		return nil, nil, err
	}

	for i, j := range list {
		for _, k := range channelList {
			if j.ChannelId == k.Id && j.RefBy == "" {
				list[i].Channel = k.Title
			}
		}
	}

	return list, &total, nil
}

func (r repo) GetUserListForExcel(query model.UserListQueryForExcel) ([]model.UserListForExcel, *int64, error) {

	var err error
	var list []model.UserListForExcel
	var total int64

	// แจ้งมิจฉาชีพ
	var scammerNumbers model.ScammerNumberListResponse
	if query.UserCategory == "scammer" {
		if masterResp, err := r.GetScammerNumberList(); err != nil {
			return nil, nil, err
		} else {
			scammerNumbers.PhoneList = masterResp.PhoneList
			scammerNumbers.BankNumberList = masterResp.BankNumberList
		}
		// No Scammer
		if len(scammerNumbers.PhoneList) == 0 && len(scammerNumbers.BankNumberList) == 0 {
			return nil, &total, nil
		}
	}

	execTotal := r.db.Table("user").
		Joins("LEFT JOIN bank ON bank.id = user.bank_id").
		Joins("LEFT JOIN recommend_channel ON recommend_channel.id = user.channel_id").
		Select("user.id")

	if query.Search != "" {
		execTotal = execTotal.Where("user.username LIKE ? OR user.phone LIKE ? OR user.member_code LIKE ? OR user.bank_account LIKE ? OR user.fullname LIKE ? OR recommend_channel.title LIKE ? OR bank.name LIKE ?", "%"+query.Search+"%", "%"+query.Search+"%", "%"+query.Search+"%", "%"+query.Search+"%", "%"+query.Search+"%", "%"+query.Search+"%", "%"+query.Search+"%")
	}

	if query.From != "" {
		startDateAtBkk, err := r.ParseBodBkk(query.From)
		if err != nil {
			return nil, nil, err
		}
		execTotal = execTotal.Where("user.created_at >= ? ", startDateAtBkk)
	}

	if query.To != "" {
		endDateAtBkk, err := r.ParseEodBkk(query.To)
		if err != nil {
			return nil, nil, err
		}
		execTotal = execTotal.Where("user.created_at <=  ?", endDateAtBkk)
	}

	// แจ้งมิจฉาชีพ
	if query.UserCategory != "" {
		if query.UserCategory == "member" {
			execTotal = execTotal.Where("user.member_code IS NOT NULL AND user.member_code != ?", "")
		} else if query.UserCategory == "non-member" {
			execTotal = execTotal.Where("user.member_code IS NULL OR user.member_code = ?", "")
		} else if query.UserCategory == "scammer" {
			if len(scammerNumbers.PhoneList) > 0 && len(scammerNumbers.BankNumberList) > 0 {
				execTotal = execTotal.Where(r.db.Where("user.phone IN ?", scammerNumbers.PhoneList).Or("user.bank_account IN ?", scammerNumbers.BankNumberList))
			} else if len(scammerNumbers.PhoneList) > 0 {
				execTotal = execTotal.Where("user.phone IN ?", scammerNumbers.PhoneList)
			} else if len(scammerNumbers.BankNumberList) > 0 {
				execTotal = execTotal.Where("user.bank_account IN ?", scammerNumbers.BankNumberList)
			}
		}
	}

	execTotal = execTotal.Where("user.deleted_at IS NULL")
	if err = execTotal.Count(&total).Error; err != nil {
		return nil, nil, err
	}

	if total == 0 {
		return nil, &total, nil
	}

	selectedFields := "user.id, user.phone, user.member_code, user_type.name AS type, user.fullname, user.bank_account, user.credit, user.ip, user.ip_registered, user.created_at, user.updated_at"
	selectedFields += ", user.logedin_at, user.note, bank.name AS bank_name, user.bank_id, admin.username AS created_by, user.channel_id, tb_ref.member_code AS ref_by, tb_ref_user_type.label_th AS channel"
	selectedFields += ", user.login_type AS login_type"

	exec := r.db.Table("user").
		Joins("LEFT JOIN bank ON bank.id = user.bank_id").
		Joins("LEFT JOIN user tb_ref ON tb_ref.id = user.ref_by").
		Joins("LEFT JOIN admin ON admin.id = user.created_by").
		Joins("LEFT JOIN user_type ON user_type.id = user.user_type_id").
		Joins("LEFT JOIN user_type tb_ref_user_type ON tb_ref_user_type.id = tb_ref.user_type_id").
		Joins("LEFT JOIN recommend_channel ON recommend_channel.id = user.channel_id").
		Select(selectedFields)

	if query.Search != "" {
		exec = exec.Where("user.username LIKE ? OR user.phone LIKE ? OR user.member_code LIKE ? OR user.bank_account LIKE ? OR user.fullname LIKE ? OR recommend_channel.title LIKE ? OR bank.name LIKE ?", "%"+query.Search+"%", "%"+query.Search+"%", "%"+query.Search+"%", "%"+query.Search+"%", "%"+query.Search+"%", "%"+query.Search+"%", "%"+query.Search+"%")
	}

	if query.From != "" {
		startDateAtBkk, err := r.ParseBodBkk(query.From)
		if err != nil {
			return nil, nil, err
		}
		exec = exec.Where("user.created_at >= ? ", startDateAtBkk)
	}

	if query.To != "" {
		endDateAtBkk, err := r.ParseEodBkk(query.To)
		if err != nil {
			return nil, nil, err
		}
		exec = exec.Where("user.created_at <=  ?", endDateAtBkk)
	}

	if query.UserCategory != "" {
		if query.UserCategory == "member" {
			exec = exec.Where("user.member_code IS NOT NULL AND user.member_code != ?", "")
		} else if query.UserCategory == "non-member" {
			exec = exec.Where("user.member_code IS NULL OR user.member_code = ?", "")
		} else if query.UserCategory == "scammer" {
			if len(scammerNumbers.PhoneList) > 0 && len(scammerNumbers.BankNumberList) > 0 {
				exec = exec.Where(r.db.Where("user.phone IN ?", scammerNumbers.PhoneList).Or("user.bank_account IN ?", scammerNumbers.BankNumberList))
			} else if len(scammerNumbers.PhoneList) > 0 {
				exec = exec.Where("user.phone IN ?", scammerNumbers.PhoneList)
			} else if len(scammerNumbers.BankNumberList) > 0 {
				exec = exec.Where("user.bank_account IN ?", scammerNumbers.BankNumberList)
			}
		}
	}

	exec = exec.Where("user.deleted_at IS NULL")
	if err := exec.
		Order("user.id DESC").
		Find(&list).
		Error; err != nil {
		return nil, nil, err
	}

	channelIds := []int64{}
	for _, v := range list {
		if v.RefBy == "" && v.ChannelId != 0 {
			channelIds = append(channelIds, v.ChannelId)
		}
	}

	if len(channelIds) == 0 {
		return list, &total, nil
	}

	channelIds = helper.RemoveDuplicateInt64(channelIds)

	var channelList []model.UserChannel

	if err := r.db.Table("recommend_channel").
		Select("id, title").
		Where("id IN ?", channelIds).
		Scan(&channelList).
		Error; err != nil {
		return nil, nil, err
	}

	for i, j := range list {
		for _, k := range channelList {
			if j.ChannelId == k.Id && j.RefBy == "" {
				list[i].Channel = k.Title
			}
		}
	}

	return list, &total, nil
}

func (r repo) GetUser(id int64) (*model.UserDetail, error) {

	var admin *model.UserDetail
	selectedFields := "user.id as id, user.member_code as member_code, user.phone"
	selectedFields += ", user.fullname as fullname, user.bank_account as bank_account, user.true_wallet as true_wallet, user.contact as contact"
	selectedFields += ", user.note as note, user.course as course, user_type.name as type, user.credit as credit, user.encrypt as encrypt"
	selectedFields += ", user.ip_registered as ip_registered, recommend_channel.title as channel, recommend_channel.id as channel_id, bank.name as bank_name"
	selectedFields += ", user.bank_id as bank_id"
	selectedFields += ", user.login_type AS login_type"

	if err := r.db.Table("user").
		Joins("LEFT JOIN recommend_channel ON recommend_channel.id = user.channel_id").
		Joins("LEFT JOIN user_type ON user_type.id = user.user_type_id").
		Joins("LEFT JOIN bank ON bank.id = user.bank_id").
		Select(selectedFields).
		Where("user.id = ?", id).
		Where("user.deleted_at IS NULL").
		Take(&admin).
		Error; err != nil {
		return nil, err
	}

	return admin, nil
}

func (r repo) GetUserInfo(id int64) (*model.UserInfo, error) {

	var admin model.UserInfo
	selectedFields := "user.id as id, user.member_code as member_code, user.phone"
	selectedFields += ", user.fullname as fullname, user.bank_account as bank_account, user.true_wallet as true_wallet, user.contact as contact"
	selectedFields += ", user.note as note, user.course as course, user_type.name as type, user.credit as credit, user.encrypt as encrypt"
	selectedFields += ", user.ip_registered as ip_registered, recommend_channel.title as channel, recommend_channel.id as channel_id, bank.name as bank_name"
	selectedFields += ", user.bank_id as bank_id"
	selectedFields += ", user.ref_by as ref_by, tb_ref.member_code as ref_by_member_code"

	if err := r.db.Table("user").
		Joins("LEFT JOIN recommend_channel ON recommend_channel.id = user.channel_id").
		Joins("LEFT JOIN user_type ON user_type.id = user.user_type_id").
		Joins("LEFT JOIN bank ON bank.id = user.bank_id").
		Joins("LEFT JOIN user tb_ref ON tb_ref.id = user.ref_by").
		Select(selectedFields).
		Where("user.id = ?", id).
		Where("user.deleted_at IS NULL").
		Take(&admin).
		Error; err != nil {
		return nil, err
	}
	return &admin, nil
}

func (r repo) GetPasswordByUserId(id int64) (*string, error) {

	var password string

	if err := r.db.Table("user").
		Select("password").
		Where("id = ?", id).
		Where("deleted_at IS NULL").
		Take(&password).
		Error; err != nil {
		return nil, err
	}

	return &password, nil
}

func (r repo) GetUserForGenMember(id int64) (*model.UserDetail, error) {

	var admin *model.UserDetail

	if err := r.db.Table("user").
		Select("id, phone, fullname, encrypt, ip_registered").
		Where("id = ?", id).
		Where("deleted_at IS NULL").
		Take(&admin).
		Error; err != nil {
		return nil, err
	}

	return admin, nil
}

func (r repo) FrontGetUser(id int64) (*model.UserMe, error) {

	var admin *model.UserMe

	if err := r.db.Table("user").
		Joins("LEFT JOIN bank ON bank.id = user.bank_id").
		Joins("LEFT JOIN user_type ON user_type.id = user.user_type_id").
		Select(`
			user.id,
			user.member_code,
			user.phone,
			user.bank_account,
			user.fullname,
			user.encrypt,
			bank.name AS bank_name,
			bank.code AS bank_code,
			user_type.name AS type,
			user.login_type AS login_type
		`).
		Where("user.id = ?", id).
		Where("user.deleted_at IS NULL").
		Take(&admin).
		Error; err != nil {
		return nil, err
	}

	return admin, nil
}

func (r repo) GetUpdateLogs(query model.UserUpdateQuery) (*[]model.UserUpdateLogResponse, *int64, error) {

	var logs []model.UserUpdateLogResponse
	var total int64

	exec := r.db.Table("user_update_log AS tb_log")

	selectedFields := "tb_log.id as id, tb_log.user_id as user_id, tb_log.description as description"
	selectedFields += ", tb_log.created_by_username as created_by_username, tb_log.ip as ip, tb_log.created_at as created_at"
	exec = exec.Select(selectedFields)
	if query.Search != "" {
		exec = exec.Where("tb_log.description LIKE ?", "%"+query.Search+"%")
	}
	if query.From != "" {
		startDateAtBkk, err := r.ParseBodBkk(query.From)
		if err != nil {
			return nil, nil, err
		}
		exec = exec.Where("tb_log.created_at >= ? ", startDateAtBkk)
	}
	if query.To != "" {
		endDateAtBkk, err := r.ParseEodBkk(query.To)
		if err != nil {
			return nil, nil, err
		}
		exec = exec.Where("tb_log.created_at <=  ?", endDateAtBkk)
	}
	if err := exec.
		Limit(query.Limit).
		Offset(query.Limit * query.Page).
		Find(&logs).
		Order("tb_log.created_at DESC").
		Error; err != nil {
		return nil, nil, err
	}

	execTotal := r.db.Table("user_update_log")

	if query.Search != "" {
		execTotal = execTotal.Where("description LIKE ?", "%"+query.Search+"%")
	}

	if query.From != "" {
		startDateAtBkk, err := r.ParseBodBkk(query.From)
		if err != nil {
			return nil, nil, err
		}
		execTotal = execTotal.Where("created_at >= ? ", startDateAtBkk)
	}
	if query.To != "" {
		endDateAtBkk, err := r.ParseEodBkk(query.To)
		if err != nil {
			return nil, nil, err
		}
		execTotal = execTotal.Where("created_at <=  ?", endDateAtBkk)
	}

	if err := execTotal.
		Count(&total).
		Error; err != nil {
		return nil, nil, err
	}

	return &logs, &total, nil
}

func (r repo) GetRegisterOtpByPhone(data model.UserSendOtpBody) (*model.UserOtp, error) {

	var otp *model.UserOtp
	if err := r.db.Table("user_otp uo").
		Select("uo.id, uo.ants_otp_id, uo.user_id, uo.expired_at, uo.phone, uo.local_pin").
		Where("uo.phone = ?", data.Phone).
		Where("uo.type = ?", "REGISTER").
		Last(&otp).
		Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return otp, nil
}

func (r repo) GetForgetOtpByPhone(data model.UserSendOtpBody) (*model.UserOtp, error) {

	var otp *model.UserOtp
	if err := r.db.Table("user_otp uo").
		Joins("JOIN user u ON u.id = uo.user_id").
		Select("uo.id, uo.ants_otp_id, uo.user_id, uo.expired_at, uo.phone, uo.local_pin").
		Where("u.phone = ?", data.Phone).
		Where("uo.type = ?", "FORGET").
		Last(&otp).
		Error; err != nil {

		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return otp, nil
}

func (r repo) GetUserOtpLogByOtpId(data model.UserResendOtpBody) (*model.UserOtp, error) {

	var otp model.UserOtp

	if err := r.db.Table("user_otp uo").
		Select("uo.id, uo.ants_otp_id, uo.user_id, uo.expired_at, uo.phone, uo.local_pin").
		Where("uo.type = ?", "REGISTER").
		Where("uo.ants_otp_id = ?", data.OtpId).
		Where("uo.phone = ?", data.Phone).
		Take(&otp).
		Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &otp, nil
}

func (r repo) GetForgetOtpLogByOtpId(otpId string) (*model.UserOtp, error) {

	var otp model.UserOtp

	if err := r.db.Table("user_otp uo").
		Select("uo.id, uo.ants_otp_id, uo.user_id, uo.expired_at, uo.phone, uo.local_pin").
		Where("uo.type = ?", "FORGET").
		Where("uo.ants_otp_id = ?", otpId).
		Last(&otp).
		Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &otp, nil
}

func (r repo) GetFrontUserByPhone(phone string) (*model.UserResponse, error) {

	var user model.UserResponse

	// login by phone
	// user_repository.go:784 SLOW SQL >= 200ms - Fixed by TULA

	if err := r.db.Table("user").
		Joins("LEFT JOIN user_status ON user_status.id = user.user_status_id").
		Select("user.id , user_status.id as user_status_id, user_status.name as status, member_code, username, phone, password, encrypt").
		Where("phone = ?", phone).
		Where("user.deleted_at IS NULL").
		Scan(&user).
		Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

func (r repo) GetUserById(id int64) (*model.UserResponse, error) {

	var user model.UserResponse

	if err := r.db.Table("user").
		Select("id, password , is_reset_password, fullname, username, member_code, encrypt, user_status_id, phone, bank_id, bank_account, login_type").
		Where("id = ?", id).
		Where("user.deleted_at IS NULL").
		Take(&user).
		Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

func (r repo) GetRefUserByRefId(refId int64) (*model.UserGetDataByRef, error) {

	var result model.UserGetDataByRef

	if err := r.db.Table("user").
		Select("user.id as user_id, user.user_type_id as user_type_id").
		Where("user.id = ?", refId).
		Where("user.deleted_at IS NULL").
		Take(&result).
		Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (r repo) GetUserIdByRef(userId int64) (*model.UserGetDataByRef, error) {

	var result model.UserGetDataByRef

	if err := r.db.Table("user").
		Joins("LEFT JOIN user_type ON user_type.id = user.user_type_id").
		Select("user.id as user_id, user.user_type_id as user_type_id").
		Where("user.id = ?", userId).
		Where("user.deleted_at IS NULL").
		Take(&result).
		Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (r repo) GetUserIdByRefCode(refCode string) (*model.UserGetDataByRef, error) {

	var result model.UserGetDataByRef

	if err := r.db.Table("user").
		Select("user.id as user_id, user.user_type_id as user_type_id").
		Joins("LEFT JOIN user_type ON user_type.id = user.user_type_id").
		Joins("LEFT JOIN user_alliance AS tb_ref_setting ON tb_ref_setting.user_id = user.id").
		Where("tb_ref_setting.ref_code = ?", refCode).
		Where("user.deleted_at IS NULL").
		Take(&result).
		Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (r repo) GetUserIdBySaleCode(saleCode string) (*model.UserGetDataByRef, error) {

	var result model.UserGetDataByRef

	if err := r.db.Table("user").
		Select("user.id as user_id, user.user_type_id as user_type_id").
		Joins("LEFT JOIN user_type ON user_type.id = user.user_type_id").
		Joins("LEFT JOIN user_alliance AS tb_ref_setting ON tb_ref_setting.user_id = user.id").
		Where("tb_ref_setting.sale_code = ?", saleCode).
		Where("user.deleted_at IS NULL").
		Take(&result).
		Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (r repo) GetRefTypeByRef(userId int64) (string, error) {

	var result string

	if err := r.db.Table("user").
		Joins("LEFT JOIN user_type ON user_type.id = user.user_type_id").
		Select("user_type.name").
		Where("user.id = ?", userId).
		Where("user.deleted_at IS NULL").
		Scan(&result).Limit(1).
		Error; err != nil {
		return "", err
	}

	return result, nil
}

func (r repo) GetMemberCode(id int64) (*string, error) {

	var memberCode string

	if err := r.db.Table("user").
		Select("member_code").
		Where("id = ?", id).
		Where("user.deleted_at IS NULL").
		Take(&memberCode).
		Error; err != nil {
		return nil, err
	}

	return &memberCode, nil
}

func (r repo) GetBankList() ([]model.UserBank, error) {

	var banks []model.UserBank
	var configuration model.ConfigurationBankResponse

	selectedFields := "con.use_th_currency AS use_th_currency, con.use_laos_currency AS use_laos_currency"
	configBank := r.db.Table("configuration_web AS con")
	configBank = configBank.Select(selectedFields)
	if err := configBank.Take(&configuration).Error; err != nil {
		configuration.UseThCurrency = true
	}

	if !configuration.UseThCurrency && !configuration.UseLaosCurrency {
		configuration.UseThCurrency = true
	}

	query := r.db.Table("bank")
	query = query.Select("id, name, code")
	if configuration.UseThCurrency && configuration.UseLaosCurrency {
		query = query.Where("country_code IN ?", []string{"TH", "ALL", "LAOS"})
	} else if configuration.UseThCurrency {
		query = query.Where("country_code IN ?", []string{"TH", "ALL"})
	} else if configuration.UseLaosCurrency {
		query = query.Where("country_code IN ?", []string{"LAOS", "ALL"})
	}
	if err := query.Find(&banks).Error; err != nil {
		return nil, err
	}

	return banks, nil
}

func (r repo) GetUserIdByMemberList(memberList []string) ([]model.UserMemberList, error) {

	var result []model.UserMemberList

	if err := r.db.Table("user").
		Select("id, member_code").
		Where("member_code IN ?", memberList).
		Scan(&result).
		Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (r repo) GetUserMemberByUserId(userId []int64) ([]model.UserMemberList, error) {

	var result []model.UserMemberList

	if err := r.db.Table("user").
		Select("id, member_code").
		Where("id IN (?)", userId).
		Scan(&result).
		Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (r repo) CheckUserByPhone(phone string) (*model.UserResponse, error) {

	var user model.UserResponse

	if err := r.db.Table("user").
		Select("id, password, is_reset_password").
		Where("phone = ?", phone).
		Where("user.deleted_at IS NULL").
		Take(&user).
		Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

func (r repo) CheckUserByAccountNo(accountNo string) (*model.UserResponse, error) {

	var user *model.UserResponse

	if err := r.db.Table("user").
		Select("id, password, is_reset_password").
		Where("bank_account = ?", accountNo).
		Where("user.deleted_at IS NULL").
		Take(&user).
		Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return user, nil
}

func (r repo) CheckUserPhone(phone string) (bool, error) {

	var user model.UserResponse

	if err := r.db.Table("user").
		Where("phone = ?", phone).
		Take(&user).
		Error; err != nil {

		if err == gorm.ErrRecordNotFound {
			return false, nil
		}

		return false, err
	}

	if user.Id != 0 {
		return false, nil
	}

	return true, nil
}

func (r repo) CheckUserById(id int64) (bool, error) {
	var user model.UserResponse

	if err := r.db.Table("user").
		Where("id = ?", id).
		Take(&user).
		Error; err != nil {

		if err == gorm.ErrRecordNotFound {
			return false, nil
		}

		return false, err
	}

	return true, nil
}

func (r repo) FrontCheckUserBank(bankNumber string) (bool, error) {

	var user model.UserResponse

	if err := r.db.Table("user").
		Select("id").
		Where("bank_account = ?", bankNumber).
		Take(&user).
		Error; err != nil {

		if err == gorm.ErrRecordNotFound {
			return false, nil
		}

		return false, err
	}

	if user.Id == 0 {
		return false, nil
	}

	return true, nil
}

func (r repo) FrontCheckUserTrueWallet(trueNumber string) (bool, error) {

	var user model.UserResponse

	if err := r.db.Table("user").
		Select("id").
		Where("true_wallet = ?", trueNumber).
		Take(&user).
		Error; err != nil {

		if err == gorm.ErrRecordNotFound {
			return false, nil
		}

		return false, err
	}

	if user.Id == 0 {
		return false, nil
	}

	return true, nil
}

func (r repo) FrontCountUser() (*int64, error) {

	var total int64

	if err := r.db.Table("user").
		Count(&total).
		Error; err != nil {
		return nil, err
	}

	return &total, nil
}

func (r repo) CountUser() (*int64, error) {

	var total int64

	if err := r.db.Table("user").
		Count(&total).
		Error; err != nil {
		return nil, err
	}

	return &total, nil
}

func (r repo) CreateUser(body model.UserFormCreate) (*int64, error) {

	var record model.UserResponse

	// Create if No Phone Exists, Update if Password is blank
	// [20240108] Sill old flow but Always CREATE !
	if err := r.db.Table("user").
		Select("id, password").
		Where("phone = ?", body.Phone).
		Take(&record).
		Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// CREATE_NEW
			if err := r.db.Table("user").Create(&body).Error; err != nil {
				return nil, err
			}
			return &body.Id, nil
		}
		return nil, err
	}

	if record.Id != 0 && record.Password == "" && body.Password != "" {
		// UPDATE
		if err := r.db.Table("user").
			Where("id = ?", record.Id).
			Where("password = ?", "").
			Updates(&body).
			Error; err != nil {
			return nil, err
		}
	} else {
		log.Println("User Already Exists")
	}

	// AFFLEVEL
	if record.Id != 0 {
		if err := r.MigrateAffiliateUserAllLevel(record.Id); err != nil {
			log.Println("CreateUser.MigrateAffiliateUserAllLevel.error", err)
		}
	}

	return &record.Id, nil
}

func (r repo) CreateUserOtpOfForget(data model.UserOtpBody) error {

	tx := r.db.Begin()

	if err := tx.Table("user_otp").
		Create(&data).
		Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Table("user").
		Where("id = ?", data.UserId).
		Update("is_reset_password", true).
		Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}

	return nil
}

func (r repo) CreateUserRegisterOtp(data model.UserOtpBody) error {

	tx := r.db.Begin()

	if err := tx.Table("user_otp").
		Create(&data).
		Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}
	return nil
}

func (r repo) GetLocalRegisterOtpByPhone(phone string) (*model.UserOtp, error) {

	var record model.UserOtp

	if err := r.db.Table("user_otp").
		Select("*").
		Where("phone = ?", phone).
		Order("id DESC").
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) CreateLocalRegisterOtp(data model.UserOtpBody) error {

	tx := r.db.Begin()

	if err := tx.Table("user_otp").
		Create(&data).
		Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}
	return nil
}

func (r repo) UpdateUserOtpLocalPin(id int64, localPin string) error {

	obj := map[string]interface{}{}
	obj["local_pin"] = localPin

	if err := r.db.Table("user_otp").
		Where("id = ?", id).
		Updates(&obj).
		Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdateUserFullname(userId int64, fullname string) error {

	if err := r.db.Table("user").Where("id = ?", userId).
		Update("fullname", fullname).
		Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdateUser(userId int64, data model.UpdateUserBody) error {

	tx := r.db.Begin()

	if err := tx.Table("user").
		Where("id = ?", userId).
		Updates(&data).
		Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}

	return nil
}

func (r repo) UserUpdateLog(changes []model.UserUpdateLogs) error {

	tx := r.db.Begin()

	if len(changes) > 0 {
		if err := tx.Table("user_update_log").
			Create(&changes).
			Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}

	return nil
}

func (r repo) UserRegister(body model.UserRegisterForm, afObj model.Affiliate, credit float64, refTypeId int64) (*int64, error) {

	tx := r.db.Begin()

	if body.LoginType == "" {
		body.LoginType = "phone"
	}

	if err := tx.Table("user").Create(&body).Error; err != nil {
		tx.Rollback()
		return nil, err
	}
	if body.Id == 0 {
		tx.Rollback()
		return nil, errors.New("CANT_CREATE_USER")
	}

	userAffCreateBody := map[string]interface{}{}
	userAffCreateBody["user_id"] = body.Id
	if err := tx.Table("user_affiliate").
		Create(&userAffCreateBody).
		Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	if refTypeId == model.USER_TYPE_AFFILIATE && afObj.RefId > 0 {
		// Apply AFF Commission
		if err := r.userRegisterApplyAfCommission(tx, body.Id, afObj.RefId, credit); err != nil {
			tx.Rollback()
			return nil, err
		}
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	// AFFLEVEL
	if body.Id != 0 {
		if err := r.MigrateAffiliateUserAllLevel(body.Id); err != nil {
			log.Println("UserRegister.MigrateAffiliateUserAllLevel.error", err)
		}
	}

	return &body.Id, nil
}

func (r repo) UpdateUserPassword(userId int64, data model.UserUpdatePassword) error {

	obj := map[string]interface{}{}
	obj["password"] = data.NewPassword
	// obj["encrypt"] = data.Encrypt

	if err := r.db.Table("user").
		Where("id = ?", userId).
		Updates(&obj).
		Error; err != nil {
		return err
	}

	return nil
}

func (r repo) UpdateMemberCode(userId int64, data model.UserUpdateMemberAndRef) error {

	obj := map[string]interface{}{}
	obj["member_code"] = data.MemberCode
	obj["encrypt"] = data.Encrypt

	if err := r.db.Table("user").
		Where("id = ?", userId).
		Updates(&obj).
		Error; err != nil {
		return err
	}

	return nil
}
func (r repo) UpdateGenMemberCodeToAffilate(userId int64) error {

	obj := map[string]interface{}{}
	obj["user_type_id"] = model.USER_TYPE_AFFILIATE

	if err := r.db.Table("user").
		Where("id = ?", userId).
		Where("user_type_id = ?", model.USER_TYPE_NONE).
		Updates(&obj).
		Error; err != nil {
		return err
	}

	if err := r.CreateMissingAffiliate(userId); err != nil {
		return err
	}

	return nil
}

func (r repo) UpdateLogin(userId int64, ip string) error {

	userObj := map[string]interface{}{
		"ip":             ip,
		"logedin_at":     time.Now(),
		"last_action_at": time.Now(), // Requried
	}

	tx := r.db.Begin()

	if err := tx.Table("user").
		Where("id = ?", userId).
		Updates(&userObj).
		Error; err != nil {
		tx.Rollback()
		return err
	}

	loginLogObj := model.UserLoginLog{
		UserId: userId,
		Ip:     ip,
	}

	if err := tx.Table("user_login_log").
		Create(&loginLogObj).
		Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}

	return nil
}

func (r repo) FrontUpdateUser(userId int64, data model.FrontUserUpdate) error {

	if err := r.db.Table("user").
		Where("id = ?", userId).
		Updates(&data).
		Error; err != nil {
		return err
	}

	return nil
}

func (r repo) FrontUpdatePassword(userId int64, password string) error {

	obj := map[string]interface{}{
		"password":          password,
		"is_reset_password": false,
	}
	// if encrypt != "" {
	// 	obj["encrypt"] = encrypt
	// }

	if err := r.db.Table("user").
		Where("id = ?", userId).
		Updates(obj).
		Error; err != nil {
		return err
	}
	return nil
}

func (r repo) DeleteUser(user model.UserResponse) error {

	if user.Id != 0 {
		// Delete with unique phone , update phone to phone_del{id}
		// Delete with unique bank_account , update bank_account to bank_account_del{id}
		updatebody := map[string]interface{}{
			"phone":        gorm.Expr("CONCAT(phone, '_del', id)"),
			"bank_account": gorm.Expr("CONCAT(bank_account, '_del', id)"),
			"deleted_at":   time.Now(),
		}
		if err := r.db.Table("user").Where("id = ?", user.Id).Updates(&updatebody).Error; err != nil {
			return err
		}
	}
	return nil
}

func (r repo) GetInactiveUserList(req model.InactiveUserListRequest) ([]model.InactiveUserResponse, int64, error) {

	var list []model.InactiveUserResponse
	var total int64
	var err error

	// 	[ตอบ] 5 วันที่แล้ว เวลา 00.00น.- วันนี้ เวลา 00.00น. (ตัวอย่างเช่น วันนี้วันที่ 14/11/66, 5 วันที่แล้ว คือ 09/11/66 เวลา 00.00 น. ถึง 14/11/66 เวลา 00.00 น.)
	// ตัวอย่างอื่นๆเช่น 1 เดือน จะบวกไป 1 เดือน ไม่สนว่าจะมี 30 หรือ 31 วัน
	beginingAt, err := time.Parse("2006-01-02", time.Now().Format("2006-01-02"))
	if err != nil {
		return nil, 0, err
	}
	var startAt time.Time
	// endAt := beginingAt
	if req.DateType == "5_days" {
		startAt = beginingAt.AddDate(0, 0, -5)
	} else if req.DateType == "10_days" {
		startAt = beginingAt.AddDate(0, 0, -10)
	} else if req.DateType == "15_days" {
		startAt = beginingAt.AddDate(0, 0, -15)
	} else if req.DateType == "1_month" {
		startAt = beginingAt.AddDate(0, -1, 0)
	} else if req.DateType == "2_month" {
		startAt = beginingAt.AddDate(0, -2, 0)
	} else if req.DateType == "3_month" {
		startAt = beginingAt.AddDate(0, -3, 0)
	} else if req.DateType == "4_month" {
		startAt = beginingAt.AddDate(0, -4, 0)
	} else if req.DateType == "5_month" {
		startAt = beginingAt.AddDate(0, -5, 0)
	} else {
		return nil, 0, errors.New("invalid date type")
	}

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("user as users")
	count = count.Select("users.id")
	if req.Username != "" {
		search_like := fmt.Sprintf("%%%s%%", req.Username)
		count = count.Where("users.username LIKE ?", search_like)
	}
	// P.Layer And P.Mink 20240827 เอาวันที ไม่ ACTIVE ตั้งแต่ 5 วันลงไป
	count = count.Where("users.last_action_at <= ?", startAt)
	// count = count.Where("users.last_action_at <= ?", endAt)

	if err = count.
		Where("users.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "users.id as id, users.username as username, users.credit as credit, users.last_action_at as last_action_at"
		selectedFields += ", DATEDIFF(NOW(), users.last_action_at) as last_action_days"
		query := r.db.Table("user as users")
		query = query.Select(selectedFields)
		if req.Username != "" {
			search_like := fmt.Sprintf("%%%s%%", req.Username)
			query = query.Where("users.username LIKE ?", search_like)
		}
		// P.Layer And P.Mink 20240827 เอาวันที ไม่ ACTIVE ตั้งแต่ 5 วันลงไป
		query = query.Where("users.last_action_at <= ?", startAt)
		// query = query.Where("users.last_action_at >= ?", startAt)
		// query = query.Where("users.last_action_at <= ?", endAt)

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		} else {
			query = query.Order("users.last_action_at ASC")
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Where("users.deleted_at IS NULL").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}

	return list, total, nil
}

func (r repo) GetInactiveUserListForExcel(req model.InactiveUserListForExcelRequest) ([]model.InactiveUserResponse, int64, error) {

	var list []model.InactiveUserResponse
	var total int64
	var err error

	// 	[ตอบ] 5 วันที่แล้ว เวลา 00.00น.- วันนี้ เวลา 00.00น. (ตัวอย่างเช่น วันนี้วันที่ 14/11/66, 5 วันที่แล้ว คือ 09/11/66 เวลา 00.00 น. ถึง 14/11/66 เวลา 00.00 น.)
	// ตัวอย่างอื่นๆเช่น 1 เดือน จะบวกไป 1 เดือน ไม่สนว่าจะมี 30 หรือ 31 วัน
	beginingAt, err := time.Parse("2006-01-02", time.Now().Format("2006-01-02"))
	if err != nil {
		return nil, 0, err
	}
	var startAt time.Time
	// endAt := beginingAt
	if req.DateType == "5_days" {
		startAt = beginingAt.AddDate(0, 0, -5)
	} else if req.DateType == "10_days" {
		startAt = beginingAt.AddDate(0, 0, -10)
	} else if req.DateType == "15_days" {
		startAt = beginingAt.AddDate(0, 0, -15)
	} else if req.DateType == "1_month" {
		startAt = beginingAt.AddDate(0, -1, 0)
	} else if req.DateType == "2_month" {
		startAt = beginingAt.AddDate(0, -2, 0)
	} else if req.DateType == "3_month" {
		startAt = beginingAt.AddDate(0, -3, 0)
	} else if req.DateType == "4_month" {
		startAt = beginingAt.AddDate(0, -4, 0)
	} else if req.DateType == "5_month" {
		startAt = beginingAt.AddDate(0, -5, 0)
	} else {
		return nil, 0, errors.New("invalid date type")
	}

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("user as users")
	count = count.Select("users.id")
	if req.Username != "" {
		search_like := fmt.Sprintf("%%%s%%", req.Username)
		count = count.Where("users.username LIKE ?", search_like)
	}
	count = count.Where("users.last_action_at <= ?", startAt)
	// count = count.Where("users.last_action_at <= ?", endAt)

	if err = count.
		Where("users.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "users.id as id, users.username as username, users.credit as credit, users.last_action_at as last_action_at"
		selectedFields += ", DATEDIFF(NOW(), users.last_action_at) as last_action_days"
		query := r.db.Table("user as users")
		query = query.Select(selectedFields)
		if req.Username != "" {
			search_like := fmt.Sprintf("%%%s%%", req.Username)
			query = query.Where("users.username LIKE ?", search_like)
		}
		query = query.Where("users.last_action_at <= ?", startAt)
		// query = query.Where("users.last_action_at <= ?", endAt)

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		} else {
			query = query.Order("users.last_action_at ASC")
		}

		if err = query.
			Where("users.deleted_at IS NULL").
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}

	}

	return list, total, nil
}

func (r repo) RemoveInactiveUserById(userId int64, dateType string) error {

	if userId != 0 {
		// 	[ตอบ] 5 วันที่แล้ว เวลา 00.00น.- วันนี้ เวลา 00.00น. (ตัวอย่างเช่น วันนี้วันที่ 14/11/66, 5 วันที่แล้ว คือ 09/11/66 เวลา 00.00 น. ถึง 14/11/66 เวลา 00.00 น.)
		// ตัวอย่างอื่นๆเช่น 1 เดือน จะบวกไป 1 เดือน ไม่สนว่าจะมี 30 หรือ 31 วัน
		beginingAt, err := time.Parse("2006-01-02", time.Now().Format("2006-01-02"))
		if err != nil {
			return err
		}
		var startAt time.Time
		// endAt := beginingAt
		if dateType == "5_days" {
			startAt = beginingAt.AddDate(0, 0, -5)
		} else if dateType == "10_days" {
			startAt = beginingAt.AddDate(0, 0, -10)
		} else if dateType == "15_days" {
			startAt = beginingAt.AddDate(0, 0, -15)
		} else if dateType == "1_month" {
			startAt = beginingAt.AddDate(0, -1, 0)
		} else if dateType == "2_month" {
			startAt = beginingAt.AddDate(0, -2, 0)
		} else if dateType == "3_month" {
			startAt = beginingAt.AddDate(0, -3, 0)
		} else if dateType == "4_month" {
			startAt = beginingAt.AddDate(0, -4, 0)
		} else if dateType == "5_month" {
			startAt = beginingAt.AddDate(0, -5, 0)
		} else {
			return errors.New("invalid date type")
		}

		// Delete with unique phone , update phone to phone_del{id}
		updatebody := map[string]interface{}{
			"phone":      gorm.Expr("CONCAT(phone, '_del', id)"),
			"deleted_at": time.Now().UTC(),
		}
		query := r.db.Table("user").Where("id = ?", userId)
		query = query.Where("last_action_at <= ?", startAt)
		// query = query.Where("last_action_at >= ?", startAt)
		// query = query.Where("last_action_at <= ?", endAt)

		// query = query.Limit(1)
		if err := query.Updates(&updatebody).Error; err != nil {
			return err
		}
	}
	return nil
}

func (r repo) GetUserAllianceInfoByUserId(userId int64) (*model.UserAllianceInfo, error) {

	var user model.UserAllianceInfo

	selectedFields := "tb_user.id AS id, tb_user.ref_by AS ref_by, tb_user.member_code AS member_code, tb_user.user_type_id AS user_type_id, tb_alliance.alias AS alias, tb_alliance.ref_code AS ref_code"

	if err := r.db.Table("user AS tb_user").
		Select(selectedFields).
		Joins("LEFT JOIN user_alliance AS tb_alliance ON tb_user.id = tb_alliance.user_id").
		Where("tb_user.id = ?", userId).
		Where("deleted_at IS NULL").
		Take(&user).
		Error; err != nil {
		return nil, err
	}

	return &user, nil
}

func (r repo) GetUserForGenmemberByUserId(userId int64) (*model.GetUserForGenmemberByUserId, error) {

	var user model.GetUserForGenmemberByUserId

	if err := r.db.Table("user").
		Select("id, ref_by, member_code, user_type_id").
		Where("id = ?", userId).
		Where("deleted_at IS NULL").
		Take(&user).
		Error; err != nil {
		return nil, err
	}

	return &user, nil
}

func (r repo) GetUserForGenmemberByMemberCode(memberCode string) (*model.GetUserForGenmemberByUserId, error) {

	var user model.GetUserForGenmemberByUserId

	if err := r.db.Table("user").
		Select("id, ref_by, member_code, user_type_id").
		Where("member_code = ?", memberCode).
		Where("deleted_at IS NULL").
		Take(&user).
		Error; err != nil {
		return nil, err
	}

	return &user, nil
}

func (r repo) CountUserTransactionDeposit(userId int64) (int64, error) {

	var total int64

	if err := r.db.Table("user_transaction").
		Where("user_id = ?", userId).
		Where("type_id = ?", model.CREDIT_TYPE_DEPOSIT).
		Count(&total).
		Error; err != nil {
		return 0, err
	}

	return total, nil
}

func (r repo) UserWithdrawSettingCreate(req model.UserWithdrawSettingCreateRequest) (int64, error) {

	query := r.db.Table("user_withdraw_setting")
	query = query.Create(&req)
	if err := query.Error; err != nil {
		return 0, err
	}

	return req.Id, nil
}

func (r repo) UserWithdrawSettingUpdateByUserId(req model.UserWithdrawSettingUpdateByUserIdRequest) error {

	query := r.db.Table("user_withdraw_setting")
	query = query.Where("user_id = ?", req.UserId)
	query = query.Updates(&req)
	if err := query.Error; err != nil {
		return err
	}

	return nil
}

func (r repo) UserWithdrawSettingGetById(id int64) (*model.UserWithdrawSettingGetByUserIdResponse, error) {

	var result model.UserWithdrawSettingGetByUserIdResponse

	query := r.db.Table("user_withdraw_setting")
	query = query.Select("id, user_id, accumulated_amount, maximum_time_per_day, updated_by_admin_id")
	query = query.Where("id = ?", id)
	query = query.Where("deleted_at IS NULL")

	if err := query.First(&result).Error; err != nil {
		return nil, err
	}

	return &result, nil
}

func (r repo) UserWithdrawSettingGetByUserId(userId int64) (*model.UserWithdrawSettingGetByUserIdResponse, error) {

	var result model.UserWithdrawSettingGetByUserIdResponse

	query := r.db.Table("user_withdraw_setting")
	query = query.Select("id, user_id, accumulated_amount, maximum_time_per_day, updated_by_admin_id")
	query = query.Where("user_id = ?", userId)
	query = query.Where("deleted_at IS NULL")

	if err := query.First(&result).Error; err != nil {
		return nil, err
	}

	return &result, nil
}

func (r repo) UserWithdrawSettingGetListByUserId(userId []int64) ([]model.UserWithdrawSettingGetByUserIdResponse, error) {

	var list []model.UserWithdrawSettingGetByUserIdResponse

	query := r.db.Table("user_withdraw_setting")
	query = query.Select("id, user_id, accumulated_amount, maximum_time_per_day, updated_by_admin_id")
	query = query.Where("user_id IN ?", userId)
	query = query.Where("deleted_at IS NULL")

	if err := query.Scan(&list).Error; err != nil {
		return nil, err
	}

	return list, nil
}

func (r repo) UserWithdrawConfigGetByKey(key string) (*model.UserWithdrawConfig, error) {

	var result model.UserWithdrawConfig

	query := r.db.Table("user_withdraw_config")
	query = query.Select("id, config_key, config_value")
	query = query.Where("config_key = ?", key)
	query = query.Where("deleted_at IS NULL")

	if err := query.First(&result).Error; err != nil {
		return nil, err
	}

	return &result, nil
}

func (r repo) CreateLineLoginLog(body model.LineLoginLogCreateBody) (*int64, error) {

	if err := r.db.Table("line_login").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) GetFrontUserByLineUuid(lineUuid string) (*model.UserResponse, error) {

	var user model.UserResponse

	if err := r.db.Table("user").
		Select("user.id , user_status.id as user_status_id, user_status.name as status, member_code, username, phone, password, encrypt").
		Joins("LEFT JOIN user_status ON user_status.id = user.user_status_id").
		Where("username = ?", lineUuid).
		Where("user.deleted_at IS NULL").
		Take(&user).
		Error; err != nil {
		return nil, err
	}
	return &user, nil
}

func (r repo) CheckUserByLineUuid(lineUuid string) (*model.UserResponse, error) {

	var user model.UserResponse

	if err := r.db.Table("user").
		Select("id, password, is_reset_password").
		Where("username = ?", lineUuid).
		Where("user.deleted_at IS NULL").
		Take(&user).
		Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

func (r repo) UserLineRegister(body model.UserRegisterForm, afObj model.Affiliate, credit float64, refTypeId int64) (*int64, error) {

	tx := r.db.Begin()

	if err := tx.Table("user").Create(&body).Error; err != nil {
		tx.Rollback()
		return nil, err
	}
	if body.Id == 0 {
		tx.Rollback()
		return nil, errors.New("CANT_CREATE_USER")
	}

	createUserAffBody := map[string]interface{}{}
	createUserAffBody["user_id"] = body.Id
	if err := tx.Table("user_affiliate").
		Create(&createUserAffBody).
		Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	if refTypeId == model.USER_TYPE_AFFILIATE && afObj.RefId > 0 {
		// Apply AFF Commission
		if err := r.userRegisterApplyAfCommission(tx, body.Id, afObj.RefId, credit); err != nil {
			tx.Rollback()
			return nil, err
		}
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	// AFFLEVEL
	if body.Id != 0 {
		if err := r.MigrateAffiliateUserAllLevel(body.Id); err != nil {
			log.Println("UserLineRegister.MigrateAffiliateUserAllLevel.error", err)
		}
	}

	return &body.Id, nil
}

func (r repo) userRegisterApplyAfCommission(tx *gorm.DB, userId int64, refBy int64, credit float64) error {

	// CONFIG-LIMIT-ALL-AFFCOMMISSION
	afCommissionSetting, err := r.GetCommissionSetting()
	if err != nil {
		return err
	}
	if afCommissionSetting.MaxCommission > 0 {
		refUserAffiliate, err := r.GetUserAffiliate(refBy)
		if err != nil {
			return err
		}
		if (refUserAffiliate.TotalWithdraw + refUserAffiliate.CommissionCurrent) >= afCommissionSetting.MaxCommission {
			credit = 0 // No Commission
		}
		// Limit Top
		canGetAmount := afCommissionSetting.MaxCommission - (refUserAffiliate.TotalWithdraw + refUserAffiliate.CommissionCurrent)
		if credit > canGetAmount {
			credit = canGetAmount
		}
		// Limit Bottom
		if credit < 0 {
			credit = 0
		}
	}

	// Create Register Bonus (Remove Later)
	var afObj model.Affiliate
	afObj.RefId = refBy
	afObj.UserId = userId
	afObj.RegisterBonusCredit = credit
	if err := tx.Table("affiliate").
		Create(&afObj).
		Error; err != nil {
		return err
	}
	// Create Register Bonus Transaction
	var createTransactionBody model.AffTransactionCreateBody
	createTransactionBody.UserId = refBy
	createTransactionBody.DailyKey = fmt.Sprintf("REGUSER-%d", userId)
	createTransactionBody.DownlineId = userId
	createTransactionBody.IncomeAmount = credit
	createTransactionBody.TypeId = model.AFF_TRANSACTION_TYPE_NEW_REGISTER
	createTransactionBody.StatusId = model.AFF_TRANSACTION_STATUS_PENDING
	if err := tx.Table("affiliate_transaction").Create(&createTransactionBody).Error; err != nil {
		return err
	}
	// Sum Total AFF Income
	userAfObj := map[string]interface{}{}
	userAfObj["commission_total"] = gorm.Expr("commission_total + ?", credit)
	userAfObj["commission_current"] = gorm.Expr("commission_current + ?", credit)
	userAfObj["bonus_share_total"] = gorm.Expr("bonus_share_total + ?", credit)
	userAfObj["member_total"] = gorm.Expr("member_total + ?", 1)
	if err := tx.Table("user_affiliate").
		Where("user_id = ?", refBy).
		Updates(&userAfObj).
		Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetChannelOption() ([]model.RecommendList, error) {

	var recommends []model.RecommendList

	db := r.db.Table("recommend_channel").Select("id, title, status, url, created_at")

	if err := db.
		Scan(&recommends).
		Order("id ASC").
		Error; err != nil {
		return nil, err
	}

	return recommends, nil
}

func (r repo) GetBanksOption() ([]model.BankResponse, error) {

	var list []model.BankResponse

	query := r.db.Table("bank")
	query = query.Select("id, name, code, icon_url, icon_url, type_flag")

	if err := query.
		Scan(&list).
		Error; err != nil {
		return nil, err
	}

	return list, nil
}

func (r repo) SetUserSingleSession(userId int64, token string) error {

	// get
	var user model.UserSingleSession
	if err := r.db.Table("user_single_session").
		Select("id").
		Where("user_id = ?", userId).
		Take(&user).
		Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			hashToken := helper.GetMD5Hash(token)
			createBody := model.UserSingleSession{
				UserId:   userId,
				Md5Token: hashToken,
			}
			if err := r.db.Table("user_single_session").
				Create(&createBody).
				Error; err != nil {
				return err
			}
			return nil
		}
		return err
	}

	// update
	hashToken := helper.GetMD5Hash(token)
	if err := r.db.Table("user_single_session").
		Where("user_id = ?", userId).
		Update("md5_token", hashToken).
		Error; err != nil {
		return err
	}

	return nil
}

func (r repo) CronjobDeleteUserLoginLog() error {

	CurrentDate := time.Now().UTC().Add(time.Hour * 7).Format("2006-01-02")
	startDateAtBkk, err := r.ParseBodBkk(CurrentDate)
	if err != nil {
		return err
	}

	if err := r.db.Unscoped().Table("user_login_log").Where("DATEDIFF(?, created_at) > 30", startDateAtBkk).Delete(&model.UserLoginLog{}).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetUserRefChannelListGraph(req model.UserChannelSummaryGraphRequest) ([]model.UserChannelTotalCount, error) {

	var list []model.UserChannelTotalCount

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return list, err
	}

	selectedFields := "DATE_FORMAT(CONVERT_TZ(tb_user.created_at, '+00:00', '+07:00'), '%Y-%m-%d') as of_date"
	selectedFields += ", COUNT(tb_user.id) as user_count, tb_ref.user_type_id as channel_id"

	query := r.db.Table("user AS tb_user")
	query = query.Select(selectedFields)
	query = query.Joins("LEFT JOIN user AS tb_ref ON tb_ref.id = tb_user.ref_by")
	query = query.Where("tb_ref.user_type_id IN (2,3)")

	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return list, err
		}
		query = query.Where("tb_user.created_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return list, err
		}
		query = query.Where("tb_user.created_at <=  ?", endDateAtBkk)
	}

	query = query.Group("channel_id, of_date")
	if err := query.Scan(&list).Error; err != nil {
		return nil, err
	}

	return list, nil
}

func (r repo) GetUserChannelListGraph(req model.UserChannelSummaryGraphRequest) ([]model.UserChannelTotalCount, error) {

	var list []model.UserChannelTotalCount

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return list, err
	}

	selectedFields := "DATE_FORMAT(CONVERT_TZ(tb_user.created_at, '+00:00', '+07:00'), '%Y-%m-%d') as of_date"
	selectedFields += ", COUNT(tb_user.id) as user_count, tb_user.channel_id as channel_id"

	query := r.db.Table("user AS tb_user")
	query = query.Select(selectedFields)
	query = query.Joins("LEFT JOIN user AS tb_ref ON tb_ref.id = tb_user.ref_by")
	query = query.Joins("LEFT JOIN recommend_channel AS tb_channel ON tb_channel.id = tb_user.channel_id")
	query = query.Where("tb_ref.user_type_id IS NULL")
	// query = query.Where("tb_channel.id IS NOT NULL")

	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return list, err
		}
		query = query.Where("tb_user.created_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return list, err
		}
		query = query.Where("tb_user.created_at <=  ?", endDateAtBkk)
	}

	query = query.Group("channel_id, of_date")
	if err := query.Scan(&list).Error; err != nil {
		return nil, err
	}

	return list, nil
}

func (r repo) GetHeadUsersRefUserId(userId int64) (*model.UserResponse, error) {
	var userRef model.UserResponse

	err := r.db.Table("user AS u1").
		Select("u2.id, u2.username, u2.member_code, u2.user_type_id, u2.ref_by, u2.phone, u2.fullname").
		Joins("JOIN user AS u2 ON u1.ref_by = u2.id").
		Where("u1.id = ?", userId).
		Where("u1.deleted_at IS NULL").
		Where("u2.deleted_at IS NULL").
		Take(&userRef).Error

	if err != nil {
		return nil, err
	}

	return &userRef, nil
}
