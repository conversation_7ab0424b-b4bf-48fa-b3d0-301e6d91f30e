package repository

import (
	"cybergame-api/model"

	"gorm.io/gorm"
)

func NewGameOrderRepository(db *gorm.DB) GameOrderRepository {
	return &repo{db}
}

type GameOrderRepository interface {
	// race condition
	CreateOrderRaceCondition(body model.CreateOrderRaceConditionBody) (int64, error)
	GetOrderRaceConditionByActionKey(actionKey string) (*model.GetOrderRaceConditionResponse, error)

	// log
	CreateOrderLog(body model.CreateOrderLogRequest) error

	// lottery
	CreateOrderLottery(body model.CreateOrderLotteryBody) (int64, error)

	// internal
	IncreaseUserCredit(body model.UserTransactionCreateRequest) (*model.UserTransactionCreateResponse, error)
	DecreaseUserCredit(body model.UserTransactionCreateRequest) (*model.UserTransactionCreateResponse, error)
	UpdateOrderLotteryStatus(body model.UpdateOrderLotteryStatusRequest) error
	GetCheckRef1No(ref1No string) (*model.OrderLottery, error)
}

func (r repo) CreateOrderLottery(body model.CreateOrderLotteryBody) (int64, error) {

	query := r.db.Table("order")
	query = query.Create(&body)
	if query.Error != nil {
		return 0, query.Error
	}

	return body.Id, nil
}

func (r repo) CreateOrderRaceCondition(body model.CreateOrderRaceConditionBody) (int64, error) {

	query := r.db.Table("order_race_condition")
	query = query.Create(&body)
	if query.Error != nil {
		return 0, query.Error
	}

	return body.Id, nil
}

func (r repo) GetOrderRaceConditionByActionKey(actionKey string) (*model.GetOrderRaceConditionResponse, error) {

	var result model.GetOrderRaceConditionResponse

	selectedFields := "id, action_key"
	query := r.db.Table("order_race_condition")
	query = query.Select(selectedFields)
	query = query.Where("action_key = ?", actionKey)
	query = query.Take(&result)
	if query.Error != nil {
		return nil, query.Error
	}
	return &result, nil
}

func (r repo) CreateOrderLog(body model.CreateOrderLogRequest) error {

	query := r.db.Table("order_log")
	query = query.Create(&body)
	if query.Error != nil {
		return query.Error
	}

	return nil
}

func (r repo) UpdateOrderLotteryStatus(body model.UpdateOrderLotteryStatusRequest) error {

	query := r.db.Table("order")
	query = query.Where("id = ?", body.Id)
	query = query.Updates(&body)
	if query.Error != nil {
		return query.Error
	}

	return nil
}

func (r repo) GetCheckRef1No(ref1No string) (*model.OrderLottery, error) {
	var result model.OrderLottery

	selectedFields := "id, ref1_no, ref2_no, user_id, amount, order_status_id"
	selectedFields += ", created_at, updated_at, cancelled_at, deleted_at"

	query := r.db.Table("order")
	query = query.Select(selectedFields)
	query = query.Where("ref1_no = ?", ref1No)
	query = query.Take(&result)
	if query.Error != nil {
		return nil, query.Error
	}
	return &result, nil
}
