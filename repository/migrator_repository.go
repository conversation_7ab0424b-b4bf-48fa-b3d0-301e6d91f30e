package repository

import (
	"cybergame-api/model"
	"strings"
	"time"

	"gorm.io/gorm"
)

func NewMigratorRepository(db *gorm.DB) MigratorRepository {
	return &repo{db}
}

type MigratorRepository interface {
	GetPendingOldUserList(req model.MigratorUserListRequest) ([]model.MigratorUser, int64, error)
	CreateTransferUser(body map[string]interface{}) error
	GetUserListByPhoneList(phoneList []string) ([]model.User, int64, error)
	CreateTransferUserBulk(bodyList map[string]model.MigratorUserCreateBody) error
	UpdateTransferUser(id int64, body map[string]interface{}) error
	GetPendingOldPartnerList(req model.MigratorPartnerListRequest) ([]model.MigratorPartner, int64, error)
	GetOldUserByMemberCode(memberCode string) (*model.User, error)
	CreateUserAlliance(body map[string]interface{}) error
	SetUserMigrationDone(id int64, mirate_status_id int64) error
	// Affiliate/Alliance
	GetUserHasRefList(req model.UserHasRefByListRequest) ([]model.UserHasRefBy, int64, error)
	GetMissingAffiliateUserList(req model.UserHasRefByListRequest) ([]model.UserHasRefBy, int64, error)
	GetAffiliateRefererList(req model.UserHasRefByListRequest) ([]model.UserHasRefBy, int64, error)
	CreateAffiliateMember(refBy int64, userId int64) error
	CreateAllianceMember(refBy int64, userId int64) error
	UpdateAffiliateSummary(userId int64) error
	// First Deposit Transaction
	GetMissingFirstTransactionList(req model.UserHasRefByListRequest) ([]model.MigrateBankTransaction, int64, error)
	MatchUserTransactionAsFirstDeposit(body model.MigrateBankTransaction) error
	// Old User
	GetOldUserMemberList(req model.MigratorOldUserListRequest) ([]model.MigratorOldUser, int64, error)
	GetOldDepositInfoList(userIds []int64) ([]model.MigratorOldUserDeposit, error)
	SetOldDepositInfoList(userId int64) (*model.MigratorOldUserDeposit, error)
	// REF
	GetRefUserByRefId(refId int64) (*model.UserGetDataByRef, error)
	// Bcel
	GetPendingBcelUserList(req model.MigratorUserListRequest) ([]model.MigratorBcelUser, int64, error)
}

func (r repo) GetOldUserByMemberCode(memberCode string) (*model.User, error) {

	var user model.User

	if err := r.db.Table("user").
		Select("id, username", "member_code").
		Where("member_code = ?", memberCode).
		Where("user.deleted_at IS NULL").
		Take(&user).
		Error; err != nil {
		return nil, err
	}
	return &user, nil
}

func (r repo) GetPendingOldUserList(req model.MigratorUserListRequest) ([]model.MigratorUser, int64, error) {

	var list []model.MigratorUser
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("migrate_temp_user as users")
	count = count.Select("users.id")

	if err = count.
		Where("users.mirate_status_id = ?", 0).
		Where("users.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "*"
		query := r.db.Table("migrate_temp_user as users")
		query = query.Select(selectedFields)

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Where("users.mirate_status_id = ?", 0).
			Where("users.deleted_at IS NULL").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}

	return list, total, nil
}

func (r repo) CreateTransferUser(body map[string]interface{}) error {

	if err := r.db.Table("user").Create(body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetUserListByPhoneList(phoneList []string) ([]model.User, int64, error) {

	var list []model.User
	var total int64
	var err error

	// phoneList := make([]string, 0)
	// if len(bulkBody) > 0 {
	// 	for k := range bulkBody {
	// 		phoneList = append(phoneList, k)
	// 	}
	// }

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("user as users")
	count = count.Select("users.id")
	count = count.Where("users.phone IN ?", phoneList)
	if err = count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "id, phone, created_at"
		query := r.db.Table("user as users")
		query = query.Select(selectedFields)
		query = query.Where("users.phone IN ?", phoneList)
		if err = query.
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) CreateTransferUserBulk(bodyList map[string]model.MigratorUserCreateBody) error {

	var createList []model.MigratorUserCreateBody
	for _, v := range bodyList {
		createList = append(createList, v)
	}

	// fmt.Println("createList", helper.StructJson(createList))

	if err := r.db.Table("user").Create(createList).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetPendingOldPartnerList(req model.MigratorPartnerListRequest) ([]model.MigratorPartner, int64, error) {

	var list []model.MigratorPartner
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("migrate_temp_mem_partner as settings")
	count = count.Select("settings.mpart_id")
	if err = count.
		Where("settings.mirate_status_id = ?", 0).
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "*"
		query := r.db.Table("migrate_temp_mem_partner as settings")
		query = query.Select(selectedFields)

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Where("settings.mirate_status_id = ?", 0).
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}

	return list, total, nil
}

func (r repo) CreateUserAlliance(body map[string]interface{}) error {

	if err := r.db.Table("user_alliance").Create(body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) CreateInitUsersAffiliate(ids []int) error {

	insertList := []map[string]interface{}{}
	for _, id := range ids {
		insertList = append(insertList, map[string]interface{}{
			"user_id": id,
		})
	}

	if err := r.db.Table("user_affiliate").Create(insertList).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) SetUserMigrationDone(id int64, mirate_status_id int64) error {

	updateData := map[string]interface{}{
		"mirate_status_id": mirate_status_id,
	}
	if err := r.db.Table("migrate_temp_user").Where("id = ?", id).Where("mirate_status_id = ?", 0).Updates(updateData).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdateTransferUser(id int64, body map[string]interface{}) error {

	if err := r.db.Table("user").Where("id = ?", id).Updates(body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetUserHasRefList(req model.UserHasRefByListRequest) ([]model.UserHasRefBy, int64, error) {

	var list []model.UserHasRefBy
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("user as settings")
	count = count.Select("settings.id")
	if err = count.
		Where("settings.ref_by > ?", 0).
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "settings.id, settings.ref_by, settings.user_type_id"
		query := r.db.Table("user as settings")
		query = query.Select(selectedFields)

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}

		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}

		if err = query.
			Where("settings.ref_by > ?", 0).
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}

	return list, total, nil
}

func (r repo) GetMissingAffiliateUserList(req model.UserHasRefByListRequest) ([]model.UserHasRefBy, int64, error) {

	var list []model.UserHasRefBy
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("user as tb_user")
	count = count.Select("tb_user.id")
	count = count.Where("user_type_id = ?", model.USER_TYPE_AFFILIATE)
	count = count.Where("id NOT IN (SELECT user_id FROM user_affiliate)")
	if err = count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_user.id, tb_user.ref_by, tb_user.user_type_id"
		query := r.db.Table("user as tb_user")
		query = query.Select(selectedFields)
		query = query.Where("user_type_id = ?", model.USER_TYPE_AFFILIATE)
		query = query.Where("id NOT IN (SELECT user_id FROM user_affiliate)")
		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) CreateAffiliateMember(refBy int64, userId int64) error {

	// CHECK IF EXIST //
	var count int64
	if err := r.db.Table("affiliate").Where("ref_id = ?", refBy).Where("user_id = ?", userId).Count(&count).Error; err != nil {
		return err
	}
	if count == 0 {
		body := map[string]interface{}{
			"ref_id":  refBy,
			"user_id": userId,
		}
		if err := r.db.Table("affiliate").Create(body).Error; err != nil {
			return err
		}
	}

	return nil
}

func (r repo) CreateAllianceMember(refBy int64, userId int64) error {

	// CHECK IF EXIST //
	var count int64
	if err := r.db.Table("alliance").Where("ref_id = ?", refBy).Where("user_id = ?", userId).Count(&count).Error; err != nil {
		return err
	}
	if count == 0 {
		body := map[string]interface{}{
			"ref_id":  refBy,
			"user_id": userId,
		}
		if err := r.db.Table("alliance").Create(body).Error; err != nil {
			return err
		}
	}
	return nil
}

func (r repo) GetAffiliateRefererList(req model.UserHasRefByListRequest) ([]model.UserHasRefBy, int64, error) {

	var list []model.UserHasRefBy
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("user as settings")
	count = count.Select("settings.id")
	if err = count.
		Where("settings.ref_by > ?", 0).
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "settings.id, settings.ref_by, settings.user_type_id"
		query := r.db.Table("user as settings")
		query = query.Select(selectedFields)

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Where("settings.ref_by > ?", 0).
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}

	return list, total, nil
}

func (r repo) UpdateAffiliateSummary(userId int64) error {

	// CHECK IF EXIST //
	var countExist int64
	if err := r.db.Table("user_affiliate").Where("user_id = ?", userId).Count(&countExist).Error; err != nil {
		return err
	}
	var countMember int64
	if err := r.db.Table("affiliate").Where("ref_id = ?", userId).Count(&countMember).Error; err != nil {
		return err
	}
	if countExist == 0 {
		body := map[string]interface{}{
			"member_total": countMember,
			"user_id":      userId,
		}
		if err := r.db.Table("user_affiliate").Create(body).Error; err != nil {
			return err
		}
	} else {
		// update
		body := map[string]interface{}{
			"member_total": countMember,
		}
		if err := r.db.Table("user_affiliate").Where("user_id = ?", userId).Updates(body).Error; err != nil {
			return err
		}
	}
	return nil
}

func (r repo) GetMissingFirstTransactionList(req model.UserHasRefByListRequest) ([]model.MigrateBankTransaction, int64, error) {

	var list []model.MigrateBankTransaction
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("bank_transaction as tb_log")
	count = count.Select("tb_log.id")
	count = count.Where("tb_log.transaction_type_id = ?", model.TRANSACTION_TYPE_DEPOSIT)
	count = count.Where("tb_log.is_first_deposit = ?", true)
	count = count.Where("tb_log.id NOT IN (SELECT ref_id FROM user_transaction WHERE ref_id IS NOT NULL)")
	if err = count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_log.id, tb_log.user_id, tb_log.transfer_at, tb_log.confirmed_at, tb_log.credit_amount"
		query := r.db.Table("bank_transaction as tb_log")
		query = query.Select(selectedFields)
		query = query.Where("tb_log.transaction_type_id = ?", model.TRANSACTION_TYPE_DEPOSIT)
		query = query.Where("tb_log.is_first_deposit = ?", true)
		query = query.Where("tb_log.id NOT IN (SELECT ref_id FROM user_transaction WHERE ref_id IS NOT NULL)")
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) MatchUserTransactionAsFirstDeposit(body model.MigrateBankTransaction) error {

	// CHECK IF EXIST //
	// 1. Match by user_id and transfer_at and credit_amount
	var record *model.UserTransaction
	query := r.db.Table("user_transaction").Where("ref_id IS NULL")
	query = query.Select("id")
	query = query.Where("user_id = ?", body.UserId)
	query = query.Where("transfer_at = ?", body.TransferAt)
	query = query.Where("credit_amount = ?", body.CreditAmount)
	if err := query.Take(&record).Error; err != nil {
		if err != gorm.ErrRecordNotFound {
			return err
		}
	}
	if record != nil && record.Id > 0 {
		updateBody := map[string]interface{}{
			"ref_id": body.Id,
		}
		if err := r.db.Table("user_transaction").Where("id = ?", record.Id).Where("ref_id IS NULL").Updates(updateBody).Error; err != nil {
			return err
		}
	} else {
		// Try Confirm Time
		query := r.db.Table("user_transaction").Where("ref_id IS NULL")
		query = query.Select("id")
		query = query.Where("user_id = ?", body.UserId)
		query = query.Where("transfer_at = ?", body.ConfirmedAt)
		query = query.Where("credit_amount = ?", body.CreditAmount)
		if err := query.Take(&record).Error; err != nil {
			return err
		}
		if record != nil && record.Id > 0 {
			updateBody := map[string]interface{}{
				"ref_id": body.Id,
			}
			if err := r.db.Table("user_transaction").Where("id = ?", record.Id).Where("ref_id IS NULL").Updates(updateBody).Error; err != nil {
				return err
			}
		}
	}
	return nil
}

func (r repo) GetOldUserMemberList(req model.MigratorOldUserListRequest) ([]model.MigratorOldUser, int64, error) {

	var list []model.MigratorOldUser
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("user as tb_user")
	count = count.Select("tb_user.id")
	count = count.Where("tb_user.member_code IS NOT NULL")
	count = count.Where("tb_user.member_code != ''")
	count = count.Where("tb_user.id >= ?", req.FromUserId)
	count = count.Where("tb_user.id < ?", req.ToUserId)
	if err = count.
		Where("tb_user.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_user.id, tb_user.member_code, tb_user.phone"
		query := r.db.Table("user as tb_user")
		query = query.Select(selectedFields)
		query = query.Where("tb_user.member_code IS NOT NULL")
		query = query.Where("tb_user.member_code != ''")
		query = query.Where("tb_user.id >= ?", req.FromUserId)
		query = query.Where("tb_user.id < ?", req.ToUserId)
		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Where("tb_user.deleted_at IS NULL").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetOldDepositInfoList(userIds []int64) ([]model.MigratorOldUserDeposit, error) {

	var list []model.MigratorOldUserDeposit

	selectedFields := "tb_log.user_id, COUNT(tb_log.id) as deposit_count, COUNT(IF(tb_log.is_first_deposit = 1, 1, NULL)) > 0 as has_first_deposit"
	selectedFields += ", MAX(IF(tb_log.is_first_deposit = 1, tb_log.id, NULL)) as first_deposit_id"

	query := r.db.Table("bank_transaction as tb_log")
	query = query.Select(selectedFields)
	query = query.Where("tb_log.user_id IN ?", userIds)
	query = query.Where("tb_log.transaction_type_id = ?", model.TRANSACTION_TYPE_DEPOSIT)
	query = query.Where("tb_log.deleted_at IS NULL")
	query = query.Group("tb_log.user_id")
	if err := query.Scan(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (r repo) SetOldDepositInfoList(userId int64) (*model.MigratorOldUserDeposit, error) {

	var result model.MigratorOldUserDeposit

	selectedFields := "tb_log.user_id, COUNT(tb_log.id) as deposit_count, COUNT(IF(tb_log.is_first_deposit = 1, 1, NULL)) > 0 as has_first_deposit"
	selectedFields += ", MAX(IF(tb_log.is_first_deposit = 1, tb_log.id, NULL)) as first_deposit_id"

	query := r.db.Table("bank_transaction as tb_log")
	query = query.Select(selectedFields)
	query = query.Where("tb_log.user_id = ?", userId)
	query = query.Where("tb_log.transaction_type_id = ?", model.TRANSACTION_TYPE_DEPOSIT)
	query = query.Where("tb_log.deleted_at IS NULL")
	query = query.Group("tb_log.user_id")
	if err := query.Take(&result).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			result.UserId = userId
			result.DepositCount = 0
			result.HasFirstDeposit = false
			result.FirstDepositId = 0
			result.NewDepositId = 0
		} else {
			return nil, err
		}
	}

	// INSERT //
	// insertBody := map[string]interface{}{
	// 	"id":                    int64(0),
	// 	"user_id":               userId,
	// 	"transaction_type_id":   model.TRANSACTION_TYPE_DEPOSIT,
	// 	"transfer_at":           "2023-10-31 00:00:00",
	// 	"confirmed_at":          "2023-10-31 00:00:00",
	// 	"credit_amount":         0,
	// 	"transaction_status_id": model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED,
	// 	"is_first_deposit":      1,
	// }
	transferAt, err := time.Parse("2006-01-02 15:04:05", "2023-10-30 17:00:00")
	if err != nil {
		return nil, err
	}
	insertBody := model.MigratorOldUserFirstDepositCreateBody{
		UserId:              userId,
		TransactionTypeId:   model.TRANSACTION_TYPE_DEPOSIT,
		TransferAt:          transferAt.UTC(),
		ConfirmedAt:         transferAt.UTC(),
		CreditAmount:        0,
		TransactionStatusId: model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED,
		DepositChannel:      "MIGRATOR",
		IsFirstDeposit:      1,
		CreatedByAdminId:    1,
		CreatedAt:           transferAt.UTC(),
		// DeletedAt:           transferAt.UTC(), // No show but still first deposit
	}
	if err := r.db.Table("bank_transaction").Create(&insertBody).Error; err != nil {
		return nil, err
	}
	result.NewDepositId = insertBody.Id

	// UPDATE //
	if result.HasFirstDeposit && result.FirstDepositId > 0 {
		updateBody := map[string]interface{}{
			"is_first_deposit": 0,
		}
		query2 := r.db.Table("bank_transaction").Where("user_id = ?", userId).Where("is_first_deposit = ?", 1).Where("id = ?", result.FirstDepositId)
		if err := query2.Updates(&updateBody).Error; err != nil {
			return nil, err
		}
	}
	return &result, nil
}

func (r repo) GetPendingBcelUserList(req model.MigratorUserListRequest) ([]model.MigratorBcelUser, int64, error) {

	var list []model.MigratorBcelUser
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("migrate_temp_user_bcel as users")
	count = count.Select("users.id")

	if err = count.
		Where("users.mirate_status_id = ?", 0).
		Where("users.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "*"
		query := r.db.Table("migrate_temp_user_bcel as users")
		query = query.Select(selectedFields)

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Where("users.mirate_status_id = ?", 0).
			Where("users.deleted_at IS NULL").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}

	return list, total, nil
}
