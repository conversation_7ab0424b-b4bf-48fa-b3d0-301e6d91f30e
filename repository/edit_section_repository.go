package repository

import (
	"cybergame-api/model"

	"gorm.io/gorm"
)

func NewEditSectionRepository(db *gorm.DB) EditSectionRepository {
	return &repo{db}
}

type EditSectionRepository interface {
	UpdateEditSection(body model.UpdateEditSectionBody) error
	GetEditSection() (*model.GetEditSectionResponse, error)

	CreateAdminAction(body model.AdminActionCreateBody) (*int64, error)
}

func (r *repo) UpdateEditSection(body model.UpdateEditSectionBody) error {

	if err := r.db.Table("edit_section").Where("id =?", 1).Updates(body).Error; err != nil {
		return err
	}

	return nil
}

func (r *repo) GetEditSection() (*model.GetEditSectionResponse, error) {

	var result model.GetEditSectionResponse

	selectedFields := " edit_section.section_deposit as section_deposit, edit_section.section_withdraw as section_withdraw "

	if err := r.db.Table("edit_section").
		Select(selectedFields).
		Where("edit_section.id = ?", 1).
		Take(&result).
		Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			var createBody model.CreateEditSectionBody
			createBody.SectionDeposit = ""
			createBody.SectionWithdraw = ""
			createBody.CreatedById = 0
			if err := r.db.Table("edit_section").Create(&createBody).Error; err != nil {
				return nil, err
			}
			result.SectionDeposit = ""
			result.SectionWithdraw = ""
			return &result, nil
		}
		return nil, err
	}

	return &result, nil
}
