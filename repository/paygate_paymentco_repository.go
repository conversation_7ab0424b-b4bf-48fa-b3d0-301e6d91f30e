package repository

import (
	"bytes"
	"cybergame-api/helper"
	"cybergame-api/model"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"mime/multipart"
	"net/http"
	"os"
	"strings"
	"time"

	"gorm.io/gorm"
)

func NewPaymentcoRepository(db *gorm.DB) PaymentcoRepository {

	// LOG : [2024-10-15]
	// File : PaymentCo Payment API version 1_1_1.pdf

	// Endpoints may change when MID is upgraded from Staging to Production.
	// https://paymentco.top/proxy/pay/unifiedbdt

	// Whitelisting
	// Some of the API functions will not work if your server IP addresses are not whitelisted.
	// Please provide IP addresses, if you have not provided yet.

	// 1.REQUIERMENT = Terminology
	// 1.1 mid = Merchant’s unique ID, refer to Reseller profile Client ID field
	// 1.2 merchantname = from https://paymentco.top/web-thb/merchantsummary
	// 1.3 Security Key = A unique security key value is assigned to each MID. Security key is used
	// to encode the Signature (Sign) key. This is emailed to the registered Technical email address
	// 1.4 Signature (Sign) = This is the result of the MD5 hashing. This is posted as a parameter whenever API posting is performed

	// 2.FUNCTION
	// 2.1 Deposit = todo
	// 2.2 Withdraw = todo

	// Latest RD
	// 1. Use POMPAY Template

	return &repo{db}
}

type PaymentcoRepository interface {
	GetDb() *gorm.DB
	// MERCHANT
	// GetNOTUSEPaygateMerchantById(id int64) (*model.PaygateMerchantNgResponse, error)
	// GetPaygateMerchantList(req model.PaygateMerchantListRequest) ([]model.PaygateMerchantNgResponse, int64, error)
	// CreatePaygateMerchant(body model.PaygateMerchantCreateBody) (*int64, error)
	// UpdatePaygateMerchant(id int64, body model.PaygateMerchantUpdateBody) error
	// DeletePaygateMerchant(id int64) error
	// // SETTING
	// GetPaygateSetting() (*model.PaygateSettingResponse, error)
	// CreatePaygateSetting(body model.PaygateSettingCreateBody) (*int64, error)
	// UpdatePaygateSetting(id int64, body model.PaygateSettingUpdateBody) error
	// DeletePaygateSetting(id int64) error
	// Paymentco-DB
	CreatePaymentcoWebhook(body model.PaymentcoWebhookCreateBody) (*int64, error)
	GetDbPaymentcoOrderList(req model.PaymentcoOrderListRequest) ([]model.PaymentcoOrderResponse, int64, error)
	GetDbPaymentcoOrderById(id int64) (*model.PaymentcoOrderResponse, error)
	GetDbPaymentcoOrderByRefId(refId int64) (*model.PaymentcoOrderResponse, error)
	CreateDbPaymentcoOrder(body model.PaymentcoOrderCreateBody) (*int64, error)
	UpdateDbPaymentcoOrderError(id int64, remark string) error
	UpdateDbPaymentcoOrder(id int64, body model.PaymentcoOrderUpdateBody) error
	ApproveDbPaymentcoOrder(id int64, webhookStatus string) error
	UpdatePaymentcoOrderBankSucess(id int64, transId int64, actionBy int64) error
	// Paymentco-REMOTE
	PaymentcoDeposit(setting model.PaygateAccountResponse, req model.PaymentcoDepositCreateRemoteRequest) (*model.PaymentcoDepositCreateRemoteResponse, error)
	PaymentcoWithdraw(setting model.PaygateAccountResponse, req model.PaymentcoWithdrawCreateRemoteRequest) (*model.PaymentcoWithdrawCreateRemoteResponse, error)
	// REF-PAYGATE
	GetRawPaymentcoPendingDepositOrderById(id int64) (*model.PaymentcoOrderResponse, error)
	GetPaygateAccountByProviderId(id int64) (*model.PaygateAccountResponse, error)
	// REF-Promotion
	GetDepositCurrentProcessingUserPromotion(userId int64) (*model.PromotionWebUserByUserIdResponse, error)
	// REF-User
	GetMemberById(id int64) (*model.Member, error)
	GetUserBankDetailById(userId int64) (*model.UserBankDetailBody, error)
	// REF-UserTransaction
	IsFirstDeposit(userId int64) bool
	IncreaseUserCredit(body model.UserTransactionCreateRequest) (*model.UserTransactionCreateResponse, error)
	UpdateDepositTransactionStatusFromAgent(transId int64, body model.UserTransactionCreateResponse) error
	UpdateAutoProcessTimer(timer string, id int64) error
	ShowUserTransaction(req model.UserTransactionShowUpdate) error
	UpdateTransactionStatusTransferingToSuccess(id int64, secondUsed string) error
	UpdateUserTransactionStatus(body model.UpdateUserTransactionStatusRequest) error
	// REF-BANKING
	GetLastestBankAdminManualDepositTransaction(userId int64) (*model.BankTransaction, error)
	// REF-BankTransaction
	GetBankTransactionById(id int64) (*model.BankTransaction, error)
	InsertBankTransaction(data model.BankTransactionCreateBody) (*int64, error)
	CreateTransactionAction(data model.CreateBankTransactionActionBody) (*int64, error)
	ConfirmPendingDepositTransaction(id int64, body model.BankDepositTransactionConfirmBody) error
	RollbackTransactionAction(actionId int64) error
	GetUserWithdrawCreditTransactionByRefId(transactionId int64, creditAmount float64) (*model.UserTransaction, error)
	// REF-RC
	GetRaceActionIdByActionKey(actionKey string) (*int64, error)
	CreateRaceCondition(body model.RaceActionCreateBody) (int64, error)
	// REF-SystemLog
	CreateSystemLog(body model.SystemLogCreateBody) (*int64, error)
	CreatePaygateSystemLog(body model.PaygateSystemLogCreateBody) (*int64, error)
	// REF-WebConfiguration
	GetWebConfiguration() (*model.GetWebConfigurationBody, error)

	// [TIER]
	IncreaseUserTierDepositAmount(userId int64, amount float64) error
}

func (r repo) PaymentcoSign(signStr string) string {

	// Example MD5 string:
	// amount=10000&appId=S48&asyncNotifyUrl=https://abc.com/get
	// Callback&clientIp=127.0.0.1&goodsDesc=30-Stretch-NavyBlue&g
	// oodsName=AA Blue Jeans&nonceStr=GpmA791dfPcYKghG&orderId
	// =153058314536667665&payChannel=Default&tradeType=BANKTX
	// &version=1.0&key=jsk383is9JJKW03kksdvnek

	// Example posted data:
	// amount=10000&appId=S48&asyncNotifyUrl=https://abc.com/get
	// Callback&clientIp=127.0.0.1&goodsDesc=30-Stretch-NavyBlue&g
	// oodsName=AA Blue Jeans&nonceStr=5COP11725423495&orderId
	// =153058314536667665&payChannel=Default&tradeType=BANKTX
	// &version=1.0&sign=8A077A2f25C252EF5995B796BCBAD28F

	fmt.Println("signStr", signStr)

	return strings.ToUpper(helper.GetMD5Hash(signStr))
}

func (r repo) PaymentcoDeposit(setting model.PaygateAccountResponse, req model.PaymentcoDepositCreateRemoteRequest) (*model.PaymentcoDepositCreateRemoteResponse, error) {

	// log.Println("PaymentcoDeposit req ------> ")

	// Process
	// Post the following parameters to the endpoint given above.
	// Table 1
	// Parameter Type Requirement Description Example
	// appId string yes MID
	// ***********
	// orderId string yes OrderID
	// 153058314536667665
	// amount string yes Amount
	// 10000
	// payChannel string yes Payment channel code
	// default
	// goodsName string yes Product name
	// AA Blue Jeans
	// goodsDesc string yes Product description
	// 30-Stretch-NavyBlue
	// clientIp string yes user IP
	// 127.0.0.1
	// asyncNotifyUrl string yes Notify URL for the Payment Result
	// http://www.xxx.com/notify
	// tradeType string yes Method
	// BANKTX
	// bankCode string no Bank code
	// ********
	// version string yes Version
	// 1.0
	// nonceStr string yes Random text string
	// GpmA791dfPcYKghG
	// sign string yes MD5 hash
	// 1BA6BD029193DCDC1EE549EADF09413A

	webhookDomain := os.Getenv("CYBERGAME_WEBHOOK_ENDPOINT")

	apiEndPoint := setting.ApiEndPoint
	req.AppId = setting.MerchantId
	if req.OrderId == "" {
		return nil, errors.New("INVALID_ORDER_ID")
	}
	if req.Amount == "" {
		return nil, errors.New("INVALID_AMOUNT")
	}
	req.PayChannel = "Default"
	req.GoodsName = "Customer-Deposit"
	req.GoodsDesc = "Deposit-from-customer"
	req.ClientIp = "*************"
	req.AsyncNotifyUrl = fmt.Sprintf("%s/paymentco/callback", webhookDomain)
	req.TradeType = "BANKTX"
	// If merchant do not know what BankCode to use, just ignore sending this paramet req.BankCode = "********"
	// req.BankCode = "THB"
	req.Version = "1.0"
	req.NonceStr = helper.RandStrings(16)
	// Example MD5 string:
	// amount=10000&appId=S48&asyncNotifyUrl=https://abc.com/get
	// Callback&clientIp=127.0.0.1&goodsDesc=30-Stretch-NavyBlue
	// &goodsName=AA Blue Jeans&nonceStr=GpmA791dfPcYKghG
	// &orderId=153058314536667665&payChannel=Default&tradeType=BANKTX
	// &version=1.0&key=jsk383is9JJKW03kksdvnek

	// 8a077a2f25c252ef5995b796bcbad28f = 8A077A2f25C252EF5995B796BCBAD28F

	// amount=10000&appId=S48&asyncNotifyUrl=https://abc.com/getCallback&clientIp=127.0.0.1&goodsDesc=30-Stretch-NavyBlue&goodsName=AA Blue Jeans&nonceStr=GpmA791dfPcYKghG&orderId=153058314536667665&payChannel=Default&tradeType=BANKTX&version=1.0&key=jsk383is9JJKW03kksdvnek
	// exSign := r.PaymentcoSign("amount=10000&appId=S48&asyncNotifyUrl=https://abc.com/getCallback&clientIp=127.0.0.1&goodsDesc=30-Stretch-NavyBlue&goodsName=AA Blue Jeans&nonceStr=GpmA791dfPcYKghG&orderId=153058314536667665&payChannel=Default&tradeType=BANKTX&version=1.0&key=jsk383is9JJKW03kksdvnek"))
	// amount=13&appId=1000138&asyncNotifyUrl=https://dev-api.cbgame88.com/api/webhook/paymentco/callback&clientIp=127.0.0.1&goodsDesc=Deposit-from-customer&goodsName=Customer-Deposit&nonceStr=vHCjjzGq&orderId=PGWAY24104&payChannel=Default&tradeType=BANKTX&version=1.0&key=s3bRtkSvIjO32Lg7E0JlJc6cBuy6v2k1rjh3eHgJlFyKFxZ2mDpvSlGI4UzoMlvd

	// amount=13.00&appId=1000138&asyncNotifyUrl=https://dev-api.cbgame88.com/api/webhook/paymentco/callback
	// &clientIp=127.0.0.1&goodsDesc=Deposit from customer
	// &goodsName=Customer Deposit&nonceStr=YDgvOYXn
	// &orderId=PGWAY24101&payChannel=Default&tradeType=BANKTX
	// &version=1.0&key=xxxxxxxxxxxxxxxxxx

	req.Sign = r.PaymentcoSign(fmt.Sprintf("amount=%v&appId=%v&asyncNotifyUrl=%v&clientIp=%v&goodsDesc=%v&goodsName=%v&nonceStr=%v&orderId=%v&payChannel=%v&tradeType=%v&version=%v&key=%v", req.Amount, req.AppId, req.AsyncNotifyUrl, req.ClientIp, req.GoodsDesc, req.GoodsName, req.NonceStr, req.OrderId, req.PayChannel, req.TradeType, req.Version, setting.SecretKey))

	// Endpoint: https://paymentco.top/proxy/pay/unifiedbdt
	// https://paymentco.top/proxy/pay/unifiedorder << from Support POSTMAN
	// https://paymentco.top/proxy/pay/unifiedbdt
	epUrl := fmt.Sprintf("%s/pay/unifiedorder", apiEndPoint)

	// SysLog
	if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
		Name:   "CreatePaymentcoDeposit.PaymentcoDeposit",
		Status: "CALL",
		JsonReq: helper.StructJson(map[string]interface{}{
			"apiEndPoint":    epUrl,
			"appId":          req.AppId,
			"orderId":        req.OrderId,
			"amount":         req.Amount,
			"payChannel":     req.PayChannel,
			"goodsName":      req.GoodsName,
			"goodsDesc":      req.GoodsDesc,
			"clientIp":       req.ClientIp,
			"asyncNotifyUrl": req.AsyncNotifyUrl,
			"tradeType":      req.TradeType,
			"version":        req.Version,
			"nonceStr":       req.NonceStr,
			"sign":           req.Sign,
		}),
	}); err != nil {
		log.Println("CreatePaymentcoDeposit.CreateSysLog", err)
	}

	client := &http.Client{}
	client.Timeout = 3 * time.Second

	// jsonBody, _ := json.Marshal(req)
	// reqBody := bytes.NewBuffer(jsonBody)
	payload := &bytes.Buffer{}
	writer := multipart.NewWriter(payload)
	_ = writer.WriteField("appId", req.AppId)
	_ = writer.WriteField("orderId", req.OrderId)
	_ = writer.WriteField("amount", req.Amount)
	_ = writer.WriteField("payChannel", req.PayChannel)
	_ = writer.WriteField("goodsName", req.GoodsName)
	_ = writer.WriteField("goodsDesc", req.GoodsDesc)
	_ = writer.WriteField("clientIp", req.ClientIp)
	_ = writer.WriteField("asyncNotifyUrl", req.AsyncNotifyUrl)
	_ = writer.WriteField("tradeType", req.TradeType)
	_ = writer.WriteField("version", req.Version)
	_ = writer.WriteField("nonceStr", req.NonceStr)
	_ = writer.WriteField("sign", req.Sign)
	err := writer.Close()
	if err != nil {
		log.Println("CreatePaymentcoDeposit.WriteField", err)
		return nil, err
	}

	reqExternal, _ := http.NewRequest("POST", epUrl, payload)
	reqExternal.Header.Set("Content-Type", writer.FormDataContentType())
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}
	if response.StatusCode == 404 {
		return nil, errors.New("EMDPOINT_404_NOT_FOUND")
	}
	if response.StatusCode != 200 {
		log.Println(response.StatusCode)
		log.Println(string(responseData))
		// 500 {"status":"E4011","message":"Missing clientId"}
		var errResp model.PaymentcoErrorRemoteResponse
		errJson := json.Unmarshal(responseData, &errResp)
		if errJson == nil {
			return nil, errors.New(errResp.Msg)
		}
		return nil, errors.New("PAYMENT_SERVER_ERROR")
	}

	// {"code":40001,"msg":"parameter incorrect"}
	// {"code":50002,"msg":"sign error"}
	// fmt.Println(string(responseData))

	var errResp model.PaymentcoErrorRemoteResponse

	if errJson := json.Unmarshal(responseData, &errResp); errJson == nil && errResp.Code != 0 {
		return nil, errors.New(errResp.Msg)
	}

	var result model.PaymentcoDepositCreateRemoteResponse
	if errJson := json.Unmarshal(responseData, &result); errJson != nil {
		log.Println("PaymentcoDeposit resp.Body ------> ", string(responseData))
		log.Println("Unmarshal.Err ------> ", errJson)
		return nil, errJson
	}

	// Remote do not have Ref2 == OrderId
	result.ReferenceId = req.OrderId

	return &result, nil
}

func (r repo) PaymentcoWithdraw(setting model.PaygateAccountResponse, req model.PaymentcoWithdrawCreateRemoteRequest) (*model.PaymentcoWithdrawCreateRemoteResponse, error) {

	// log.Println("PaymentcoWithdraw req ------> ")

	// Translate Bank Name
	convertedBankList := map[string]string{
		"kbank": "kbank",
	}
	if val, ok := convertedBankList[req.BankName]; ok {
		req.BankName = val
	}

	webhookDomain := os.Getenv("CYBERGAME_WEBHOOK_ENDPOINT")
	apiEndPoint := setting.ApiEndPoint
	req.AppId = setting.MerchantId
	if req.Amount == "" {
		return nil, errors.New("INVALID_AMOUNT")
	}
	if req.OrderId == "" {
		return nil, errors.New("INVALID_ORDER_ID")
	}
	if req.AccountName == "" {
		return nil, errors.New("INVALID_ACCOUNT_NAME")
	}
	if req.AccountNo == "" {
		return nil, errors.New("INVALID_ACCOUNT_NO")
	}
	if req.BankName == "" {
		return nil, errors.New("INVALID_BANK_NAME")
	}
	req.AsyncNotifyUrl = fmt.Sprintf("%s/paymentco/callback", webhookDomain)
	req.PayType = "BANKTX"
	req.Branch = "Bangkok"
	req.NonceStr = helper.RandStrings(16)
	req.Version = "1.0"
	// Hash Key Example (clientId + transactionId + custName + custBank + custBankAcc + custMobile + custEmail + amount + callbackUrl + clientSecret)
	req.Sign = r.PaymentcoSign(fmt.Sprintf("appId=%v&amount=%v&orderId=%v&accountName=%v&accountNo=%v&bankName=%v&asyncNotifyUrl=%v&payType=%v&branch=%v&nonceStr=%v&version=%v&key=%v", req.AppId, req.Amount, req.OrderId, req.AccountName, req.AccountNo, req.BankName, req.AsyncNotifyUrl, req.PayType, req.Branch, req.NonceStr, req.Version, setting.SecretKey))

	// https://paymentco.top/proxy/pay/withdrawal
	// https://paymentco.top/proxy + /pay/withdrawal
	epUrl := fmt.Sprintf("%s/pay/withdrawal", apiEndPoint)

	// SysLog
	if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
		Name:   "CreatePaymentcoWithdraw.PaymentcoWithdraw",
		Status: "CALL",
		JsonReq: helper.StructJson(map[string]interface{}{
			"apiEndPoint":    epUrl,
			"appId":          req.AppId,
			"amount":         req.Amount,
			"orderId":        req.OrderId,
			"accountName":    req.AccountName,
			"accountNo":      req.AccountNo,
			"bankName":       req.BankName,
			"asyncNotifyUrl": req.AsyncNotifyUrl,
			"payType":        req.PayType,
			"branch":         req.Branch,
			"nonceStr":       req.NonceStr,
			"version":        req.Version,
			"sign":           req.Sign,
		}),
	}); err != nil {
		log.Println("CreatePaymentcoWithdraw.CreateSysLog", err)
	}

	client := &http.Client{}
	client.Timeout = 3 * time.Second

	// jsonBody, _ := json.Marshal(req)
	// reqBody := bytes.NewBuffer(jsonBody)
	payload := &bytes.Buffer{}
	writer := multipart.NewWriter(payload)
	_ = writer.WriteField("appId", req.AppId)
	_ = writer.WriteField("amount", req.Amount)
	_ = writer.WriteField("orderId", req.OrderId)
	_ = writer.WriteField("accountName", req.AccountName)
	_ = writer.WriteField("accountNo", req.AccountNo)
	_ = writer.WriteField("bankName", req.BankName)
	_ = writer.WriteField("asyncNotifyUrl", req.AsyncNotifyUrl)
	_ = writer.WriteField("payType", req.PayType)
	_ = writer.WriteField("branch", req.Branch)
	_ = writer.WriteField("nonceStr", req.NonceStr)
	_ = writer.WriteField("version", req.Version)
	_ = writer.WriteField("sign", req.Sign)
	err := writer.Close()
	if err != nil {
		log.Println("CreatePaymentcoDeposit.WriteField", err)
		return nil, err
	}

	reqExternal, _ := http.NewRequest("POST", epUrl, payload)
	reqExternal.Header.Set("Content-Type", writer.FormDataContentType())
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}
	if response.StatusCode == 404 {
		return nil, errors.New("EMDPOINT_404_NOT_FOUND")
	}
	if response.StatusCode != 200 {
		log.Println(response.StatusCode)
		log.Println(string(responseData))
		// 500 {"status":"E4011","message":"Missing clientId"}
		var errResp model.PaymentcoErrorRemoteResponse
		errJson := json.Unmarshal(responseData, &errResp)
		if errJson == nil {
			return nil, errors.New(errResp.Msg)
		}
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	}

	fmt.Println("PaymentcoWithdraw.responseData")
	fmt.Println(string(responseData))

	var result model.PaymentcoWithdrawCreateRemoteResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("PaymentcoWithdraw resp.Body ------> ", string(responseData))
		log.Println("Unmarshal.Err ------> ", errJson)
		return nil, errJson
	}

	fmt.Println("PaymentcoWithdraw.result", helper.StructJson(result))

	return &result, nil
}

func (r repo) CreatePaymentcoWebhook(body model.PaymentcoWebhookCreateBody) (*int64, error) {

	if err := r.db.Table("paygate_paymentco_webhook").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) GetDbPaymentcoOrderList(req model.PaymentcoOrderListRequest) ([]model.PaymentcoOrderResponse, int64, error) {

	var list []model.PaymentcoOrderResponse
	var total int64

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("paygate_paymentco_order as tb_order")
	count = count.Select("tb_order.id")
	if req.UserId != nil {
		count = count.Where("tb_order.user_id = ?", req.UserId)
	}
	if req.OrderTypeId != nil {
		count = count.Where("tb_order.order_type_id = ?", req.OrderTypeId)
	}
	if req.OrderNo != "" {
		count = count.Where("tb_order.order_no = ?", req.OrderNo)
	}
	if req.TransactionNo != "" {
		count = count.Where("tb_order.transaction_no = ?", req.TransactionNo)
	}
	if req.Amount != nil {
		// +- 5.00
		minAmount := *req.Amount - float64(5)
		maxAmount := *req.Amount + float64(5)
		count = count.Where("tb_order.amount BETWEEN ? AND ?", minAmount, maxAmount)
	}
	if req.Status != "" {
		count = count.Where("tb_order.transaction_status = ?", req.Status)
	}

	if err := count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount"
		selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
		selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
		selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

		query := r.db.Table("paygate_paymentco_order as tb_order")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN paygate_paymentco_order_type AS tb_type ON tb_order.order_type_id = tb_type.id")
		if req.UserId != nil {
			query = query.Where("tb_order.user_id = ?", req.UserId)
		}
		if req.OrderTypeId != nil {
			query = query.Where("tb_order.order_type_id = ?", req.OrderTypeId)
		}
		if req.OrderNo != "" {
			query = query.Where("tb_order.order_no = ?", req.OrderNo)
		}
		if req.TransactionNo != "" {
			query = query.Where("tb_order.transaction_no = ?", req.TransactionNo)
		}
		if req.Amount != nil {
			// +- 5.00
			minAmount := *req.Amount - float64(5)
			maxAmount := *req.Amount + float64(5)
			query = query.Where("tb_order.amount BETWEEN ? AND ?", minAmount, maxAmount)
		}
		if req.Status != "" {
			query = query.Where("tb_order.transaction_status = ?", req.Status)
		}

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetDbPaymentcoOrderById(id int64) (*model.PaymentcoOrderResponse, error) {

	var record model.PaymentcoOrderResponse

	selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount"
	selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
	selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
	selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

	if err := r.db.Table("paygate_paymentco_order as tb_order").
		Select(selectedFields).
		Joins("LEFT JOIN paygate_paymentco_order_type AS tb_type ON tb_order.order_type_id = tb_type.id").
		Where("tb_order.id = ?", id).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetDbPaymentcoOrderByRefId(refId int64) (*model.PaymentcoOrderResponse, error) {

	var record model.PaymentcoOrderResponse

	selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount"
	selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
	selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
	selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

	if err := r.db.Table("paygate_paymentco_order as tb_order").
		Select(selectedFields).
		Joins("LEFT JOIN paygate_paymentco_order_type AS tb_type ON tb_order.order_type_id = tb_type.id").
		Where("tb_order.ref_id = ?", refId).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) CreateDbPaymentcoOrder(body model.PaymentcoOrderCreateBody) (*int64, error) {

	if err := r.db.Table("paygate_paymentco_order").Create(&body).Error; err != nil {
		return nil, err
	}

	// Update order_no if Empty in HPG{YYYYMMDD}{ID} //
	// orderNo := fmt.Sprintf("HPG%v%v", time.Now().Format("200601"), body.Id)
	// [20240209] Update order_no if Empty in {AGENT_NAME}{YYMM}{ID} //
	agentName := os.Getenv("PAYGATE_ORDER_PREFIX")
	if ginMode := os.Getenv("GIN_MODE"); ginMode == "debug" {
		agentName = "P7D" // DEVELOPMENT
	}
	if agentName == "" {
		agentName = "P7G" // ** MIN_LEN=10
	} else {
		agentName = strings.ToUpper(agentName)
	}
	orderNo := fmt.Sprintf("%v%v%v", agentName, time.Now().Format("0601"), body.Id)
	if body.OrderNo == "" {
		updateBody := make(map[string]interface{})
		updateBody["order_no"] = orderNo
		if err := r.db.Table("paygate_paymentco_order").Where("id = ?", body.Id).Updates(updateBody).Error; err != nil {
			return nil, err
		}
	}
	return &body.Id, nil
}

func (r repo) UpdateDbPaymentcoOrderError(id int64, remark string) error {

	updateBody := map[string]interface{}{
		"transaction_status": "ERROR",
		"remark":             remark,
	}
	sql := r.db.Table("paygate_paymentco_order").Where("id = ?", id).Where("transaction_status = ?", "PENDING")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdateDbPaymentcoOrder(id int64, body model.PaymentcoOrderUpdateBody) error {

	if err := r.db.Table("paygate_paymentco_order").Where("id = ?", id).Updates(body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) ApproveDbPaymentcoOrder(id int64, webhookStatus string) error {

	updateBody := make(map[string]interface{})
	updateBody["transaction_status"] = webhookStatus
	updateBody["payment_at"] = time.Now()

	sql := r.db.Table("paygate_paymentco_order").Where("id = ?", id).Where("transaction_status = ?", "WAIT_PAYMENT")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdatePaymentcoOrderBankSucess(id int64, transId int64, actionBy int64) error {

	updateBody := map[string]interface{}{
		"bank_transaction_id":     transId,
		"bank_transaction_status": "SUCCESS",
		"action_by":               actionBy,
	}

	sql := r.db.Table("paygate_paymentco_order").Where("id = ?", id).Where("bank_transaction_status = ?", "PENDING")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}
