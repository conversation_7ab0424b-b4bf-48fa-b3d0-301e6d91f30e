package repository

import (
	"bytes"
	"cybergame-api/helper"
	"cybergame-api/model"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strings"
	"time"

	"gorm.io/gorm"
)

func NewPapayaPayRepository(db *gorm.DB) PapayaPayRepository {
	return &repo{db}
}

type PapayaPayRepository interface {
	GetDb() *gorm.DB
	// MERCHANT
	// GetNOTUSEPaygateMerchantById(id int64) (*model.PaygateMerchantNgResponse, error)
	// GetPaygateMerchantList(req model.PaygateMerchantListRequest) ([]model.PaygateMerchantNgResponse, int64, error)
	// CreatePaygateMerchant(body model.PaygateMerchantCreateBody) (*int64, error)
	// UpdatePaygateMerchant(id int64, body model.PaygateMerchantUpdateBody) error
	// DeletePaygateMerchant(id int64) error
	// // SETTING
	// GetPaygateSetting() (*model.PaygateSettingResponse, error)
	// CreatePaygateSetting(body model.PaygateSettingCreateBody) (*int64, error)
	// UpdatePaygateSetting(id int64, body model.PaygateSettingUpdateBody) error
	// DeletePaygateSetting(id int64) error
	// // AdminLog
	// GetPaygateAdminLogById(id int64) (*model.PaygateAdminLogResponse, error)
	// GetPaygateAdminLogList(req model.PaygateAdminLogListRequest) ([]model.PaygateAdminLogResponse, int64, error)
	// CreatePaygateAdminLog(body model.PaygateAdminLogCreateBody) (*int64, error)
	// UpdatePaygateAdminLog(id int64, body model.PaygateAdminLogUpdateBody) error
	// // SystemLog
	CreatePaygateSystemLog(body model.PaygateSystemLogCreateBody) (*int64, error)
	// UpdatePaygateSystemLog(id int64, body model.PaygateSystemLogUpdateBody) error
	GetPaygateAccountByProviderId(id int64) (*model.PaygateAccountResponse, error)
	// PapayaPay-REMOTE
	PapayaPayDeposit(setting model.PaygateAccountResponse, req model.PapayaPayDepositCreateRemoteRequest) (*model.PapayaPayDepositCreateRemoteResponse, error)
	PapayaPayWithdraw(setting model.PaygateAccountResponse, req model.PapayaPayWithdrawCreateRemoteRequest) (*model.PapayaPayWithdrawCreateRemoteResponse, error)
	// PapayaPayCheckBalance(setting model.PaygateAccountResponse) (*model.PapayaPayCheckBalanceRemoteResponse, error)
	// PapayaPayGetOrder(setting model.PaygateAccountResponse, orderNo string) (*model.PapayaPayGetOrderRemoteResponse, error)
	// PapayaPay-DB
	CreatePapayaPayWebhook(body model.PapayaPayWebhookCreateBody) (*int64, error)
	GetDbPapayaPayOrderList(req model.PapayaPayOrderListRequest) ([]model.PapayaPayOrderResponse, int64, error)
	GetDbPapayaPayOrderById(id int64) (*model.PapayaPayOrderResponse, error)
	GetDbPapayaPayOrderByRefId(refId int64) (*model.PapayaPayOrderResponse, error)
	CreateDbPapayaPayOrder(body model.PapayaPayOrderCreateBody) (*int64, error)
	UpdateDbPapayaPayOrderError(id int64, remark string) error
	UpdateDbPapayaPayOrder(id int64, body model.PapayaPayOrderUpdateBody) error
	ApproveDbPapayaPayOrder(id int64, webhookStatus string) error

	// REF-USER
	GetUserById(id int64) (*model.UserResponse, error)
	// REF-MemberCode
	GetUserForGenMember(id int64) (*model.UserDetail, error)
	GetAgentInfo() (*model.AgentInfo, error)
	IncrementTotal() error
	UpdateMemberCode(userId int64, data model.UserUpdateMemberAndRef) error
	AgcRegister(data model.AgcRegister) error
	AmbRegister(data model.AmbRegister) error
	GetMemberById(id int64) (*model.Member, error)
	IsFirstDeposit(userId int64) bool
	UpdateGenMemberCodeToAffilate(userId int64) error
	CreateAffiliateMember(refBy int64, userId int64) error
	// CronjobDeletePapayaPayWebhook() error
	// REF-RC
	GetRaceActionIdByActionKey(actionKey string) (*int64, error)
	CreateRaceCondition(body model.RaceActionCreateBody) (int64, error)
	// REF-BankTransaction-Deposit
	GetUserBankDetailById(id int64) (*model.UserBankDetailBody, error)
	GetDepositCurrentProcessingUserPromotion(userId int64) (*model.PromotionWebUserByUserIdResponse, error)
	InsertBankTransaction(data model.BankTransactionCreateBody) (*int64, error)
	IncreaseUserCredit(body model.UserTransactionCreateRequest) (*model.UserTransactionCreateResponse, error)
	UpdateDepositTransactionStatusFromAgent(transId int64, body model.UserTransactionCreateResponse) error
	UpdateAutoProcessTimer(timer string, id int64) error
	// ConfirmDeposit
	GetBankTransactionById(id int64) (*model.BankTransaction, error)
	CreateTransactionAction(data model.CreateBankTransactionActionBody) (*int64, error)
	ConfirmPendingDepositTransaction(id int64, body model.BankDepositTransactionConfirmBody) error
	RollbackTransactionAction(actionId int64) error
	// REF-BANK
	GetLastestBankAdminManualDepositTransaction(userId int64) (*model.BankTransaction, error)
	ShowUserTransaction(req model.UserTransactionShowUpdate) error
	UpdateTransactionStatusTransferingToSuccess(id int64, secondUsed string) error
	RollbackTransactionStatusTransferingToConfirmed(id int64) error
	CreateBankTransactionExternalDetail(body model.BankTransactionExternalDetailCreateRequest) (int64, error)
	// REF-SysLog
	CreateSystemLog(body model.SystemLogCreateBody) (*int64, error)
	// REF-WebConfiguration
	GetWebConfiguration() (*model.GetWebConfigurationBody, error)

	// tier
	IncreaseUserTierDepositAmount(userId int64, amount float64) error
}

func (r repo) PapayaPayDeposit(setting model.PaygateAccountResponse, req model.PapayaPayDepositCreateRemoteRequest) (*model.PapayaPayDepositCreateRemoteResponse, error) {

	// 3.3 [THB] Fund In Transactions Common Request/response parameters
	// 3.3.1 Fund In Transaction Request Header
	// Field	Required	Type	Description
	// Content-Type	Y	String	application/json
	// Accept	Y	String	application/json
	// transactiontoken	Y	String	Merchant Secret API Key Provided from Back Office
	// 3.3.2 Fund In Transaction Request Body (application/json)
	// {
	// 	"qrCodeTransactionId": "PAKTIZXHDFCAVEXJPJ9",
	// 	"currency": "THB",
	// 	"amount": 10.00,
	// 	"payMethod": "thaiqr",
	// 	"bankCode": "001",
	// 	"accountNumber": "*********",
	// 	"accountName": "ชนิกานต์ ตังกบดี",
	// 	"description": "Payment"
	// }
	// log.Println("PapayaPayDeposit req ------> ")
	// ผมอยากเก็บ default เอาไว้กัน Adminตั้งค่าต่ำกว่า payment
	if req.Amount < 100 || req.Amount > 200000 {
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}
	if req.Amount < setting.PaymentDepositMinimum || req.Amount > setting.PaymentDepositMaximum {
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}

	apiEndPoint := setting.ApiEndPoint
	apiKey := setting.PrivateKey

	// POST Endpoint: https://scb.xyzonline.app/api/v2/create-qr-payment
	epUrl := fmt.Sprintf("%s/api/v2/create-qr-payment", apiEndPoint)
	log.Println("PapayaPayDeposit url ------> ", epUrl)

	// SysLog
	if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
		Name:   "CreatePapayaPayDeposit.PapayaPayDeposit",
		Status: "CALL",
		JsonReq: helper.StructJson(map[string]interface{}{
			"epUrl":   epUrl,
			"setting": setting,
			"req":     req,
		}),
	}); err != nil {
		log.Println("CreatePaygateSystemLog.CreateSysLog", err)
	}

	client := &http.Client{}
	client.Timeout = 3 * time.Second

	jsonBody, _ := json.Marshal(req)
	payload := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest("POST", epUrl, payload)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("transactiontoken", apiKey)
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}
	if response.StatusCode != 200 {
		log.Println(response.StatusCode)
		log.Println(string(responseData))
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	}

	var result model.PapayaPayDepositCreateRemoteResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("PapayaPayDeposit resp.Body ------> ", string(responseData))
		log.Println("Unmarshal.Err ------> ", errJson)
		var errMsg1 model.PapayaPayErrorRemoteResponse
		errJson2 := json.Unmarshal(responseData, &errMsg1)
		if errJson2 != nil {
			log.Println("PapayaPayDeposit resp.Body ------> ", string(responseData))
			log.Println("Unmarshal.Err ------> ", errJson)
		} else {
			return nil, errors.New(errMsg1.Message)
		}
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}
	// if !result.Success || result.Code != 200 {
	// 	log.Println("PapayaPayDeposit INVALID_RESPONSE_CODE ------> ", helper.StructJson(result))
	// 	// Code Status Explanation
	// 	var errResp model.PapayaPayErrorRemoteResponse
	// 	errJson := json.Unmarshal(responseData, &errResp)
	// 	if errJson != nil {
	// 		log.Println("PapayaPayDeposit resp.Body ------> ", string(responseData))
	// 		log.Println("Unmarshal.Err ------> ", errJson)
	// 		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	// 	}
	// 	return nil, errors.New(errResp.Message)
	// }
	return &result, nil
}

func (r repo) PapayaPayWithdraw(setting model.PaygateAccountResponse, req model.PapayaPayWithdrawCreateRemoteRequest) (*model.PapayaPayWithdrawCreateRemoteResponse, error) {

	// 3.4 Fund out Transaction Common Request/response parameters
	// 3.4.1 Fund out Transaction Request Header
	// Field	Required	Type	Description
	// Content-Type	Y	String	application/json
	// Accept	Y	String	application/json
	// transactiontoken	Y	String	Merchant Secret API Key Provided from Back Office
	// 3.4.2 Fund out Transaction Request Body (application/json)
	// {
	//     "currencyCode": "THB",
	//     "fundOutPaymentReference": "TRRSL2OM2R6P489SPR86",
	//     "fundOutDescription": "Withdrawal",
	//     "accountName": "ชนิกานต์ ตังกบดี",
	//     "accountNumber": "**********",
	//     "bankCode": "014",
	//     "amount": 30.00
	// }
	// log.Println("PapayaPayWithdraw req ------> ")
	// ผมอยากเก็บ default เอาไว้กัน Adminตั้งค่าต่ำกว่า payment
	if req.Amount < 100 || req.Amount > 2000000 {
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}
	if req.Amount < setting.PaymentWithdrawMinimum || req.Amount > setting.PaymentWithdrawMaximum {
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}

	apiEndPoint := setting.ApiEndPoint
	apiKey := setting.PrivateKey

	// POST Endpoint: https://scb.xyzonline.app/api/v2/create-fundout
	epUrl := fmt.Sprintf("%s/api/v2/create-fundout", apiEndPoint)
	log.Println("PapayaPayWithdraw url ------> ", epUrl)

	// SysLog
	if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
		Name:   "CreatePapayaPayDeposit.PapayaPayWithdraw",
		Status: "CALL",
		JsonReq: helper.StructJson(map[string]interface{}{
			"epUrl":   epUrl,
			"setting": setting,
			"req":     req,
		}),
	}); err != nil {
		log.Println("CreatePaygateSystemLog.CreateSysLog", err)
	}

	client := &http.Client{}
	client.Timeout = 3 * time.Second

	jsonBody, _ := json.Marshal(req)
	payload := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest("POST", epUrl, payload)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("transactiontoken", apiKey)
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}
	if response.StatusCode != 200 {
		log.Println(response.StatusCode)
		log.Println(string(responseData))
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	}

	var result model.PapayaPayWithdrawCreateRemoteResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("PapayaPayWithdraw resp.Body ------> ", string(responseData))
		log.Println("Unmarshal.Err ------> ", errJson)
		return nil, errJson
	}

	// todo 2024/07/11 21:23:16 PapayaPayWithdraw INVALID_RESPONSE_CODE ------>  {"success":false,"code":0,"data":{"orderNo":"","receiveAddr":"","chainName":"","coinUnit":"","requestAmount":0,"status":"","payUrl":"","hrefbackurl":"","sign":""}}

	// {
	// 	"statusCode": 400,
	// 	"message": "Kindly specify different fundOutPaymentReference, one you specified already exist in the system",
	// 	"error": "Bad Request"
	// }

	// REJECTED
	// Bank Transaction REF below minimum

	if result.StatusCode != 200 {
		log.Println("PapayaPayWithdraw INVALID_RESPONSE_CODE ------> ", helper.StructJson(result))
		var errResp model.PapayaPayErrorRemoteResponse
		errJson := json.Unmarshal(responseData, &errResp)
		if errJson != nil {
			log.Println("PapayaPayWithdraw resp.Body ------> ", string(responseData))
			log.Println("Unmarshal.Err ------> ", errJson)
			return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
		}
		return nil, errors.New(errResp.Message)
	}
	return &result, nil
}

func (r repo) CreatePapayaPayWebhook(body model.PapayaPayWebhookCreateBody) (*int64, error) {

	if err := r.db.Table("paygate_papaya_webhook").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) GetDbPapayaPayOrderList(req model.PapayaPayOrderListRequest) ([]model.PapayaPayOrderResponse, int64, error) {

	var list []model.PapayaPayOrderResponse
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("paygate_papaya_order as tb_order")
	count = count.Select("tb_order.id")
	if req.UserId != nil {
		count = count.Where("tb_order.user_id = ?", req.UserId)
	}
	if req.OrderTypeId != nil {
		count = count.Where("tb_order.order_type_id = ?", req.OrderTypeId)
	}
	if req.OrderNo != "" {
		count = count.Where("tb_order.order_no = ?", req.OrderNo)
	}
	if req.TransactionNo != "" {
		count = count.Where("tb_order.transaction_no = ?", req.TransactionNo)
	}
	if req.Amount != "" {
		count = count.Where("tb_order.amount = ?", req.Amount)
	}
	if req.Status != "" {
		count = count.Where("tb_order.transaction_status = ?", req.Status)
	}

	if err = count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount"
		selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
		selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
		selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

		query := r.db.Table("paygate_papaya_order as tb_order")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN paygate_papaya_order_type AS tb_type ON tb_order.order_type_id = tb_type.id")
		if req.UserId != nil {
			query = query.Where("tb_order.user_id = ?", req.UserId)
		}
		if req.OrderTypeId != nil {
			query = query.Where("tb_order.order_type_id = ?", req.OrderTypeId)
		}
		if req.OrderNo != "" {
			query = query.Where("tb_order.order_no = ?", req.OrderNo)
		}
		if req.TransactionNo != "" {
			query = query.Where("tb_order.transaction_no = ?", req.TransactionNo)
		}
		if req.Amount != "" {
			query = query.Where("tb_order.amount = ?", req.Amount)
		}
		if req.Status != "" {
			query = query.Where("tb_order.transaction_status = ?", req.Status)
		}

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetDbPapayaPayOrderById(id int64) (*model.PapayaPayOrderResponse, error) {

	var record model.PapayaPayOrderResponse

	selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount"
	selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
	selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
	selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

	if err := r.db.Table("paygate_papaya_order as tb_order").
		Select(selectedFields).
		Joins("LEFT JOIN paygate_papaya_order_type AS tb_type ON tb_order.order_type_id = tb_type.id").
		Where("tb_order.id = ?", id).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetDbPapayaPayOrderByRefId(refId int64) (*model.PapayaPayOrderResponse, error) {

	var record model.PapayaPayOrderResponse

	selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount"
	selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
	selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
	selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

	if err := r.db.Table("paygate_papaya_order as tb_order").
		Select(selectedFields).
		Joins("LEFT JOIN paygate_papaya_order_type AS tb_type ON tb_order.order_type_id = tb_type.id").
		Where("tb_order.ref_id = ?", refId).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) CreateDbPapayaPayOrder(body model.PapayaPayOrderCreateBody) (*int64, error) {

	if err := r.db.Table("paygate_papaya_order").Create(&body).Error; err != nil {
		return nil, err
	}

	// Update order_no if Empty in HPG{YYYYMMDD}{ID} //
	// orderNo := fmt.Sprintf("HPG%v%v", time.Now().Format("200601"), body.Id)
	// [20240209] Update order_no if Empty in {AGENT_NAME}{YYMM}{ID} //
	agentName := os.Getenv("PAYGATE_ORDER_PREFIX")
	if ginMode := os.Getenv("GIN_MODE"); ginMode == "debug" {
		agentName = "P3D" // DEVELOPMENT
	}
	if agentName == "" {
		agentName = "P3G" // ** MIN_LEN=10
	} else {
		agentName = strings.ToUpper(agentName)
	}
	orderNo := fmt.Sprintf("%v%v%v", agentName, time.Now().Format("0601"), body.Id)
	if body.OrderNo == "" {
		updateBody := make(map[string]interface{})
		updateBody["order_no"] = orderNo
		if err := r.db.Table("paygate_papaya_order").Where("id = ?", body.Id).Updates(updateBody).Error; err != nil {
			return nil, err
		}
	}
	return &body.Id, nil
}

func (r repo) UpdateDbPapayaPayOrderError(id int64, remark string) error {

	updateBody := map[string]interface{}{
		"transaction_status": "ERROR",
		"remark":             remark,
	}
	sql := r.db.Table("paygate_papaya_order").Where("id = ?", id).Where("transaction_status = ?", "PENDING")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdateDbPapayaPayOrder(id int64, body model.PapayaPayOrderUpdateBody) error {

	if err := r.db.Table("paygate_papaya_order").Where("id = ?", id).Updates(body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) ApproveDbPapayaPayOrder(id int64, webhookStatus string) error {

	updateBody := make(map[string]interface{})
	updateBody["transaction_status"] = webhookStatus
	updateBody["payment_at"] = time.Now()

	sql := r.db.Table("paygate_papaya_order").Where("id = ?", id).Where("transaction_status = ?", "WAIT_PAYMENT")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}
