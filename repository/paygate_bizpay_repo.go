package repository

import (
	"bytes"
	"cybergame-api/helper"
	"cybergame-api/model"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"strings"
	"time"

	"github.com/golang-jwt/jwt"
	"gorm.io/gorm"
)

func NewBizpayRepository(db *gorm.DB) BizpayRepository {
	return &repo{db}
}

type BizpayRepository interface {
	GetDb() *gorm.DB
	// Bizpay-RD
	// BizpayEncryptRepayDesposit(partnerKey string, amount int64) (*model.BizpayEncryptPayload, error)

	// MERCHANT
	// GetNOTUSEPaygateMerchantById(id int64) (*model.PaygateMerchantNgResponse, error)
	// GetPaygateMerchantList(req model.PaygateMerchantListRequest) ([]model.PaygateMerchantNgResponse, int64, error)
	// CreatePaygateMerchant(body model.PaygateMerchantCreateBody) (*int64, error)
	// UpdatePaygateMerchant(id int64, body model.PaygateMerchantUpdateBody) error
	// DeletePaygateMerchant(id int64) error
	// // SETTING
	// GetPaygateSetting() (*model.PaygateSettingResponse, error)
	// CreatePaygateSetting(body model.PaygateSettingCreateBody) (*int64, error)
	// UpdatePaygateSetting(id int64, body model.PaygateSettingUpdateBody) error
	// DeletePaygateSetting(id int64) error
	// // AdminLog
	// GetPaygateAdminLogById(id int64) (*model.PaygateAdminLogResponse, error)
	// GetPaygateAdminLogList(req model.PaygateAdminLogListRequest) ([]model.PaygateAdminLogResponse, int64, error)
	// CreatePaygateAdminLog(body model.PaygateAdminLogCreateBody) (*int64, error)
	// UpdatePaygateAdminLog(id int64, body model.PaygateAdminLogUpdateBody) error
	// // SystemLog
	CreatePaygateSystemLog(body model.PaygateSystemLogCreateBody) (*int64, error)
	// UpdatePaygateSystemLog(id int64, body model.PaygateSystemLogUpdateBody) error

	// REF-PAYGATE
	GetRawBizpayPendingDepositOrderById(id int64) (*model.BizpayOrderResponse, error)
	GetPaygateAccountByProviderId(id int64) (*model.PaygateAccountResponse, error)
	// Bizpay-REMOTE
	// BizpayGetToken(setting model.PaygateAccountResponse) (*model.BizpayTokenCreateRemoteResponse, error)
	BizpayDeposit(setting model.PaygateAccountResponse, req model.BizpayDepositCreateRemoteRequest) (*model.BizpayDepositCreateRemoteResponse, error)
	BizpayWithdraw(setting model.PaygateAccountResponse, req model.BizpayWithdrawCreateRemoteRequest) (*model.BizpayWithdrawCreateRemoteResponse, error)
	BizpayCheckBalance(setting model.PaygateAccountResponse) (*model.BizpayCheckBalanceRemoteResponse, error)
	// BizpayGetOrder(setting model.PaygateAccountResponse, orderNo string) (*model.BizpayGetOrderRemoteResponse, error)
	// BizpayCreateCustomer(setting model.PaygateAccountResponse, req model.BizpayCustomerCreateRemoteRequest) (*model.BizpayCustomerCreateRemoteResponse, error)
	// BizpayUpdateCustomer(setting model.PaygateAccountResponse, req model.BizpayCustomerUpdateRemoteRequest) (*model.BizpayCustomerUpdateRemoteResponse, error)
	// Bizpay-Decrypt
	// BizpayDecryptRepayDespositPayload(setting model.PaygateAccountResponse, payload model.BizpayWebhookEncryptPayload) (*model.BizpayWebhookDepositResponse, error)
	// Bizpay-DB
	CreateBizpayWebhook(body model.BizpayWebhookCreateBody) (*int64, error)
	GetDbBizpayOrderList(req model.BizpayOrderListRequest) ([]model.BizpayOrderResponse, int64, error)
	GetDbBizpayOrderById(id int64) (*model.BizpayOrderResponse, error)
	GetDbBizpayOrderByRefId(refId int64) (*model.BizpayOrderResponse, error)
	CheckBizpayDepositOrderInLast5Minutes(userId int64, amount float64) (*model.BizpayOrderResponse, error)
	CreateDbBizpayOrder(body model.BizpayOrderCreateBody) (*int64, error)
	UpdateDbBizpayOrderError(id int64, remark string) error
	UpdateDbBizpayOrder(id int64, body model.BizpayOrderUpdateBody) error
	ApproveDbBizpayOrder(id int64, webhookStatus string) error
	UpdateBizpayOrderBankSucess(id int64, transId int64, actionBy int64) error
	// Token
	// GetDbBizpayAccessToken() (*model.BizpayToken, error)
	// CreateDbBizpayAccessToken(body model.BizpayTokenCreateBody) (*int64, error)
	// Customer
	// GetBizpayCustomerById(id int64) (*model.BizpayCustomerResponse, error)
	// GetBizpayCustomerByUserId(userId int64) (*model.BizpayCustomerResponse, error)
	// CheckBizpayCustomerByUserId(user model.UserBankDetailBody) (*model.BizpayCustomerResponse, error)
	// GetBizpayCustomerList(req model.BizpayCustomerListRequest) ([]model.BizpayCustomerResponse, int64, error)
	// CreateBizpayCustomer(body model.BizpayCustomerCreateBody) (*int64, error)
	// UpdateBizpayCustomer(id int64, body model.BizpayCustomerUpdateBody) error
	// DeleteBizpayCustomer(id int64) error
	// REF-USER
	GetUserById(id int64) (*model.UserResponse, error)
	// REF-MemberCode
	GetUserForGenMember(id int64) (*model.UserDetail, error)
	GetAgentInfo() (*model.AgentInfo, error)
	IncrementTotal() error
	UpdateMemberCode(userId int64, data model.UserUpdateMemberAndRef) error
	AgcRegister(data model.AgcRegister) error
	AmbRegister(data model.AmbRegister) error
	GetMemberById(id int64) (*model.Member, error)
	IsFirstDeposit(userId int64) bool
	UpdateGenMemberCodeToAffilate(userId int64) error
	CreateAffiliateMember(refBy int64, userId int64) error
	// CronjobDeleteBizpayWebhook() error
	// REF-RC
	GetRaceActionIdByActionKey(actionKey string) (*int64, error)
	CreateRaceCondition(body model.RaceActionCreateBody) (int64, error)
	// REF-BankTransaction-Deposit
	GetUserBankDetailById(id int64) (*model.UserBankDetailBody, error)
	GetDepositCurrentProcessingUserPromotion(userId int64) (*model.PromotionWebUserByUserIdResponse, error)
	InsertBankTransaction(data model.BankTransactionCreateBody) (*int64, error)
	IncreaseUserCredit(body model.UserTransactionCreateRequest) (*model.UserTransactionCreateResponse, error)
	UpdateDepositTransactionStatusFromAgent(transId int64, body model.UserTransactionCreateResponse) error
	UpdateAutoProcessTimer(timer string, id int64) error
	// ConfirmDeposit
	GetBankTransactionById(id int64) (*model.BankTransaction, error)
	CreateTransactionAction(data model.CreateBankTransactionActionBody) (*int64, error)
	ConfirmPendingDepositTransaction(id int64, body model.BankDepositTransactionConfirmBody) error
	RollbackTransactionAction(actionId int64) error
	// REF-BANK
	GetLastestBankAdminManualDepositTransaction(userId int64) (*model.BankTransaction, error)
	ShowUserTransaction(req model.UserTransactionShowUpdate) error
	UpdateTransactionStatusTransferingToSuccess(id int64, secondUsed string) error
	RollbackTransactionStatusTransferingToConfirmed(id int64) error
	CreateBankTransactionExternalDetail(body model.BankTransactionExternalDetailCreateRequest) (int64, error)
	UpdateUserTransactionStatus(body model.UpdateUserTransactionStatusRequest) error
	GetUserWithdrawCreditTransactionByRefId(transactionId int64, creditAmount float64) (*model.UserTransaction, error)
	// REF-SysLog
	CreateSystemLog(body model.SystemLogCreateBody) (*int64, error)
	// REF-WebConfiguration
	GetWebConfiguration() (*model.GetWebConfigurationBody, error)

	// [TIER]
	IncreaseUserTierDepositAmount(userId int64, amount float64) error
}

func (r repo) GetBizpayTimestamp(now time.Time) int64 {

	return now.UnixNano() / int64(time.Millisecond)
}

func (r repo) BizpayCreateSignature(timestamp int64, merchantId string, clientId string, secretKey string) string {
	// var timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
	// var securityKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey));
	// var credentials = new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha256);
	// var header = new JwtHeader(credentials);
	// var payload = new JwtPayload
	// {
	// 	{ "merchantId", merchantId },
	// 	{ "clientId", clientId },
	// 	{ "iat", timestamp }
	// };
	// var token = new JwtSecurityToken(header, payload);
	// var tokenHandler = new JwtSecurityTokenHandler();
	// return tokenHandler.WriteToken(token);
	// timestamp := r.GetBizpayTimestamp(time.Now())
	// var securityKey = []byte(secretKey)
	// var credentials = helper.NewSigningCredentials(securityKey, "HS256")
	// var header = helper.NewJwtHeader(credentials)
	// var payload = map[string]interface{}{
	// 	"merchantId": merchantId,
	// 	"clientId":   clientId,
	// 	"iat":        timestamp,
	// }
	// var token = helper.NewJwtSecurityToken(header, payload)
	// var tokenHandler = helper.NewJwtSecurityTokenHandler()
	// return tokenHandler.WriteToken(token)

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"merchantId": merchantId,
		"clientId":   clientId,
		"iat":        timestamp,
	})

	tokenString, err := token.SignedString([]byte(secretKey))
	if err != nil {
		return ""
	}
	return tokenString
}

func (r repo) BizpayGetToken(timestamp int64, setting model.PaygateAccountResponse) (*model.BizpayTokenCreateRemoteResponse, error) {

	if setting.ApiEndPoint == "" || setting.PartnerKey == "" {
		return nil, errors.New("INVALID_SETTING")
	}

	// use PartnerKey as MerchantAccesskey
	var req model.BizpayTokenCreateRemoteRequest
	req.SecretKey = setting.SecretKey
	req.Timestamp = timestamp
	req.Payload.ClientId = setting.PartnerKey
	req.Payload.MerchantId = setting.MerchantId

	apiEndPoint := setting.ApiEndPoint
	// POST Endpoint: {{API_ENDPOINT}}/api/v1/jwt/create
	epUrl := fmt.Sprintf("%s/api/v1/jwt/create", apiEndPoint)

	// SysLog
	if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
		Name:   "CreateGetBizpayToken.GetBizpayToken",
		Status: "CALL",
		JsonReq: helper.StructJson(map[string]interface{}{
			"epUrl":   epUrl,
			"setting": setting,
			"req":     req,
		}),
	}); err != nil {
		log.Println("CreatePaygateSystemLog.CreateSysLog", err)
	}

	client := &http.Client{}
	client.Timeout = 30 * time.Second

	jsonBody, _ := json.Marshal(req)
	payload := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest("POST", epUrl, payload)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("x-api-key", setting.AccessKey)
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}
	if response.StatusCode != 200 && response.StatusCode != 201 {
		log.Println(response.StatusCode)
		log.Println(string(responseData))
		var errMsg2 model.BizpayErrorRemoteResponse
		errJson2 := json.Unmarshal(responseData, &errMsg2)
		if errJson2 == nil {
			return nil, errors.New(errMsg2.Message)
		} else {
			log.Println("Unmarshal.errJson2 ------> ", errJson2)
		}
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	}

	var result model.BizpayTokenCreateRemoteResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("GetBizpayToken resp.Body ------> ", string(responseData))
		log.Println("Unmarshal.Err ------> ", errJson)
		var errMsg1 model.BizpayErrorRemoteResponse
		errJson2 := json.Unmarshal(responseData, &errMsg1)
		if errJson2 != nil {
			log.Println("GetBizpayToken resp.Body ------> ", string(responseData))
			log.Println("Unmarshal.Err ------> ", errJson)
		} else {
			return nil, errors.New(errMsg1.Message)
		}
		return nil, errors.New("INVALID_UNMARSHAL_JSON")
	}

	return &result, nil
}

func (r repo) BizpayDeposit(setting model.PaygateAccountResponse, req model.BizpayDepositCreateRemoteRequest) (*model.BizpayDepositCreateRemoteResponse, error) {

	// ผมอยากเก็บ default เอาไว้กัน Adminตั้งค่าต่ำกว่า payment
	if req.Amount < model.BIZPAY_DEFMIN_DEPOSIT_AMOUNT || req.Amount > model.BIZPAY_DEFMAX_DEPOSIT_AMOUNT {
		log.Println("req.Amount", req.Amount)
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}
	if setting.PaymentDepositMinimum > 0 && req.Amount < setting.PaymentDepositMinimum {
		log.Println("req.Amount", req.Amount, "setting.PaymentDepositMinimum", setting.PaymentDepositMinimum)
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}
	if setting.PaymentDepositMaximum > 0 && req.Amount > setting.PaymentDepositMaximum {
		log.Println("req.Amount", req.Amount, "setting.PaymentDepositMaximum", setting.PaymentDepositMaximum)
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}
	// Prerequisites
	if setting.ApiEndPoint == "" || setting.PartnerKey == "" || setting.AccessKey == "" || setting.SecretKey == "" || setting.MerchantId == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}

	actionAtUtc := time.Now().UTC()
	timestamp := r.GetBizpayTimestamp(actionAtUtc)
	sign := r.BizpayCreateSignature(timestamp, setting.MerchantId, setting.PartnerKey, setting.SecretKey)
	// {
	// 	"clientId": "{{CLIENT_ID}}",
	// 	"merchantId": "{{MERCHANT_ID}}",
	// 	"transactionId": "BZP0PTB01776723e7X1",
	// 	"bankAccountNumber": "**********",
	// 	"bankName": "BBL",
	// 	"name": "เฮง ร่ำรวย",
	// 	"amount": 100,
	// 	"callbackUrl": "https://xxx.xxx/callback",
	// 	"type": "QR",
	// 	"timeout": 5,
	// 	"signature": "{{vault:json-web-token}}",
	// 	"timestamp": *************
	// }
	req.ClientId = setting.PartnerKey
	req.MerchantId = setting.MerchantId
	req.Timeout = 5
	req.Signature = sign
	req.Timestamp = timestamp

	apiEndPoint := setting.ApiEndPoint
	// POST Endpoint: {{API_ENDPOINT}}/api/v1/deposit/create
	epUrl := fmt.Sprintf("%s/api/v1/deposit/create", apiEndPoint)
	log.Println("BizpayDeposit url ------> ", epUrl)

	// SysLog
	if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
		Name:   "CreateBizpayDeposit.BizpayDeposit",
		Status: "CALL",
		JsonReq: helper.StructJson(map[string]interface{}{
			"epUrl":   epUrl,
			"setting": setting,
			"req":     req,
		}),
	}); err != nil {
		log.Println("CreatePaygateSystemLog.CreateSysLog", err)
	}

	client := &http.Client{}
	client.Timeout = 30 * time.Second

	jsonBody, _ := json.Marshal(req)
	payload := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest("POST", epUrl, payload)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("x-api-key", setting.AccessKey)
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}

	// fmt.Println("BizpayDeposit.resp.Body", string(responseData))
	//	BizpayDeposit resp.Body ------>  {
	//	    "status": "success",
	//	    "message": "Create Success",
	//	    "data": {
	//	        "clientId": "TKdGbyKbPV",
	//	        "merchantId": "udPnDhwbYP",
	//	        "referenceId": "1VCp0Lzanh",
	//	        "transactionId": "DEV25023",
	//	        "status": "pending",
	//	        "amount": 123,
	//	        "depositAmount": 122.35,
	//	        "qrcode": "00020101021129370016A0000006770101110213*************53037645406122.355802TH63044935",
	//	        "bankAccountNumber": "**********",
	//	        "bankAccountName": "S J Mart 1971 Co.,Ltd ",
	//	        "bankName": "KTB",
	//	        "bankCode": "006",
	//	        "promptpayNumber": "*************",
	//	        "expireDate": "2025-02-25T08:26:57.173Z",
	//	        "customerData": {
	//	            "bankAccountNumber": "**********",
	//	            "bankName": "KBANK",
	//	            "name": "paymenttester"
	//	        }
	//	    }
	//	}

	if response.StatusCode != 200 {
		log.Println(response.StatusCode)
		log.Println(string(responseData))
		var errMsg2 model.BizpayErrorRemoteResponse
		errJson2 := json.Unmarshal(responseData, &errMsg2)
		if errJson2 == nil {
			return nil, errors.New(errMsg2.Message)
		} else {
			log.Println("Unmarshal.errJson2 ------> ", errJson2)
		}
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	}

	var result model.BizpayDepositCreateRemoteResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("BizpayDeposit resp.Body ------> ", string(responseData))
		log.Println("Unmarshal.Err ------> ", errJson)
		// TRY-3-err2
		var errMsg3 model.BizpayErrorRemoteResponse
		errJson3 := json.Unmarshal(responseData, &errMsg3)
		if errJson3 == nil {
			return nil, errors.New(errMsg3.Message)
		} else {
			log.Println("Unmarshal.errJson3 ------> ", errJson3)
		}
		return nil, errors.New("INVALID_UNMARSHAL_JSON")
	}

	// fmt.Println("BizpayDepositCreateRemoteResponse.result", result)
	// BizpayDeposit.remoteResp {"success":false,"message":"Cannot read properties of undefined (reading 'length')","data":{"qr_code_id":"","qr_code_url":"","created_at":"","qr_code":"","expired_at":"","amount":"","ref_name_th":"","ref_name_en":"","ref_user_id":""}}
	// BizpayDeposit.remoteResp {"success":false,"message":"Cannot read properties of null (reading 'webhook_url')","data":{"qr_code_id":"","qr_code_url":"","created_at":"","qr_code":"","expired_at":"","amount":"","ref_name_th":"","ref_name_en":"","ref_user_id":""}}

	if strings.ToLower(result.Status) != "success" {
		// SysLog
		if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "BizpayDeposit",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(req),
			JsonResponse: helper.StructJson(map[string]interface{}{"resp": string(responseData), "result": helper.StructJson(result)}),
		}); err != nil {
			log.Println("BizpayDeposit.CreateSysLog", err)
		}
		return nil, errors.New(result.Message)
	}
	return &result, nil
}

func (r repo) BizpayCheckBalance(setting model.PaygateAccountResponse) (*model.BizpayCheckBalanceRemoteResponse, error) {

	// Prerequisites
	if setting.ApiEndPoint == "" || setting.PartnerKey == "" || setting.AccessKey == "" || setting.SecretKey == "" || setting.MerchantId == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}

	apiEndPoint := setting.ApiEndPoint
	// POST Endpoint: {{API_ENDPOINT}}/api/v1/merchant/balance?merchantId={{MERCHANT_ID}}
	epUrl := fmt.Sprintf("%s/api/v1/merchant/balance?merchantId=%s", apiEndPoint, setting.MerchantId)

	// SysLog
	syslogId, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
		Name:   "BizpayCheckBalance",
		Status: "CALL",
		JsonReq: helper.StructJson(map[string]interface{}{
			"epUrl":   epUrl,
			"setting": setting,
		}),
	})
	if err != nil {
		log.Println("BizpayCheckBalance.CreatePaygateSystemLog.ERROR", err)
	}

	client := &http.Client{}
	client.Timeout = 30 * time.Second

	reqExternal, _ := http.NewRequest("GET", epUrl, nil)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("x-api-key", setting.AccessKey)
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}

	// Update SystemLog for Response.
	jsonResponse :=
		helper.StructJson(map[string]interface{}{
			"epUrl": epUrl,
			"resp":  string(responseData),
		})
	if err := r.UpdatePaygateSystemLog(*syslogId, model.PaygateSystemLogUpdateBody{
		JsonResponse: &jsonResponse,
	}); err != nil {
		log.Println("BizpayCheckBalance.UpdatePaygateSystemLog.ERROR", err)
	}

	if response.StatusCode != 200 {
		log.Println(response.StatusCode)
		log.Println(string(responseData))
		var errMsg3 model.BizpayErrorRemoteResponse
		errJson3 := json.Unmarshal(responseData, &errMsg3)
		if errJson3 == nil {
			return nil, errors.New(errMsg3.Message)
		} else {
			log.Println("Unmarshal.errJson3 ------> ", errJson3)
		}
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	}

	var result model.BizpayCheckBalanceRemoteResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("BizpayCheckBalance resp.Body ------> ", string(responseData))
		log.Println("Unmarshal.Err ------> ", errJson)
		var errMsg3 model.BizpayErrorRemoteResponse
		errJson3 := json.Unmarshal(responseData, &errMsg3)
		if errJson3 == nil {
			return nil, errors.New(errMsg3.Message)
		} else {
			log.Println("Unmarshal.errJson3 ------> ", errJson3)
		}
		return nil, errors.New("INVALID_UNMARSHAL_JSON")
	}

	// fmt.Println("BizpayCheckBalanceRemoteResponse.result", result)

	return &result, nil
}

func (r repo) BizpayWithdraw(setting model.PaygateAccountResponse, req model.BizpayWithdrawCreateRemoteRequest) (*model.BizpayWithdrawCreateRemoteResponse, error) {

	// ผมอยากเก็บ default เอาไว้กัน Adminตั้งค่าต่ำกว่า payment
	// if req.Amount < model.BIZPAY_DEFMIN_WITHDRAW_AMOUNT || req.Amount > model.BIZPAY_DEFMAX_WITHDRAW_AMOUNT {
	// 	log.Println("req.Amount", req.Amount)
	// 	return nil, errors.New("INVALID_AMOUNT_RANGE")
	// }
	// if req.Amount < setting.PaymentWithdrawMinimum || req.Amount > setting.PaymentWithdrawMaximum {
	// 	log.Println("req.Amount", req.Amount, " setting.PaymentWithdrawMinimum", setting.PaymentWithdrawMinimum, " setting.PaymentWithdrawMaximum", setting.PaymentWithdrawMaximum)
	// 	return nil, errors.New("INVALID_AMOUNT_RANGE")
	// }

	// Will Check Balance before Withdraw
	balance, err := r.BizpayCheckBalance(setting)
	if err != nil {
		return nil, errors.New("PAYGATE_CANT_CHECK_BALANCE")
	}
	if balance.Data.Balance < req.Amount || balance.Data.OperateBalance < req.Amount {
		log.Println("balance.Data.Balance", balance.Data.Balance, "balance.Data.OperateBalance", balance.Data.OperateBalance, "req.Amount", req.Amount)
		return nil, errors.New("PAYGATE_INSUFFICIENT_BALANCE")
	}

	// Prerequisites
	if setting.ApiEndPoint == "" || setting.PartnerKey == "" || setting.AccessKey == "" || setting.SecretKey == "" || setting.MerchantId == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}

	actionAtUtc := time.Now().UTC()
	timestamp := r.GetBizpayTimestamp(actionAtUtc)
	sign := r.BizpayCreateSignature(timestamp, setting.MerchantId, setting.PartnerKey, setting.SecretKey)
	//	{
	//	    "clientId": "{{CLIENT_ID}}", // The unique identifier for the client initiating the payout
	//	    "merchantId": "{{MERCHANT_ID}}", // The unique identifier for the merchant
	//	    "transactionId": "POP0PTB01776723e7X1", // A unique transaction ID generated by the client
	//	    "bankAccountNumber": "**********", // The customer's bank account number to which the payout will be sent
	//	    "amount": 1, // The payout amount
	//	    "bankName": "KTB", // The name of the customer's bank (as per the List Bank Code API)
	//	    "name": "เฮง ร่ำรวย", // The name of the customer or account holder
	//	    "phone": "", // (Optional) The phone number of the customer
	//	    "callbackUrl": "https://xxxx.com/callback", // The URL to receive the payout status updates
	//	    "signature": "{{vault:json-web-token}}", // The signature generated using the API key and secret key to authenticate the request
	//	    "timestamp": ************* // The timestamp of the request, which matches the timestamp used in the signature
	//	}
	req.ClientId = setting.PartnerKey
	req.MerchantId = setting.MerchantId
	req.Signature = sign
	req.Timestamp = timestamp

	apiEndPoint := setting.ApiEndPoint
	// POST Endpoint: {{API_ENDPOINT}}/api/v1/payout/create
	epUrl := fmt.Sprintf("%s/api/v1/payout/create", apiEndPoint)

	// SysLog
	if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
		Name:   "BizpayWithdraw.BizpayWithdraw",
		Status: "CALL",
		JsonReq: helper.StructJson(map[string]interface{}{
			"epUrl":   epUrl,
			"setting": setting,
			"req":     req,
		}),
	}); err != nil {
		log.Println("CreatePaygateSystemLog.CreateSysLog", err)
	}

	client := &http.Client{}
	client.Timeout = 30 * time.Second

	jsonBody, _ := json.Marshal(req)
	payload := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest("POST", epUrl, payload)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("x-api-key", setting.AccessKey)
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}
	// fmt.Println("BizpayWithdraw.resp.Body", string(responseData))

	if response.StatusCode != 200 {
		log.Println(response.StatusCode)
		log.Println(string(responseData))
		var errMsg3 model.BizpayErrorRemoteResponse
		errJson3 := json.Unmarshal(responseData, &errMsg3)
		if errJson3 == nil {
			return nil, errors.New(errMsg3.Message)
		} else {
			log.Println("Unmarshal.errJson3 ------> ", errJson3)
		}
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	}

	//  {"code":"10002","message":"PARTNER_KEY Invalid","data":""}

	var result model.BizpayWithdrawCreateRemoteResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("BizpayWithdraw resp.Body ------> ", string(responseData))
		log.Println("Unmarshal.Err ------> ", errJson)
		var errMsg3 model.BizpayErrorRemoteResponse
		errJson3 := json.Unmarshal(responseData, &errMsg3)
		if errJson3 == nil {
			return nil, errors.New(errMsg3.Message)
		} else {
			log.Println("Unmarshal.errJson3 ------> ", errJson3)
		}
		return nil, errors.New("INVALID_UNMARSHAL_JSON")
	}

	// fmt.Println("BizpayWithdrawCreateRemoteResponse.result", result)
	//	{
	//	    "status": "success",
	//	    "message": "Create Success",
	//	    "data": {
	//	        "clientId": "TKdGbyKbPV",
	//	        "merchantId": "udPnDhwbYP",
	//	        "referenceId": "QfPLWIkKHO",
	//	        "transactionId": "PG10-250216",
	//	        "amount": 50,
	//	        "status": "pending",
	//	        "customerData": {
	//	            "bankAccountNumber": "**********",
	//	            "bankName": "KBANK",
	//	            "name": "paymenttester",
	//	            "phone": "**********"
	//	        },
	//	        "systemBankData": {
	//	            "bankAccountNumber": "**********",
	//	            "bankName": "KTB",
	//	            "bankCode": "006",
	//	            "name": "S J Mart 1971 Co.,Ltd "
	//	        }
	//	    }
	//	}
	if strings.ToLower(result.Status) != "success" {
		// SysLog
		if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "BizpayWithdraw",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(req),
			JsonResponse: helper.StructJson(map[string]interface{}{"resp": string(responseData), "result": helper.StructJson(result)}),
		}); err != nil {
			log.Println("BizpayWithdraw.CreateSysLog", err)
		}
		return nil, errors.New(result.Message)
	}
	return &result, nil
}

func (r repo) CreateBizpayWebhook(body model.BizpayWebhookCreateBody) (*int64, error) {

	if err := r.db.Table("paygate_bizpay_webhook").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) GetDbBizpayOrderList(req model.BizpayOrderListRequest) ([]model.BizpayOrderResponse, int64, error) {

	var list []model.BizpayOrderResponse
	var total int64

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("paygate_bizpay_order as tb_order")
	count = count.Select("tb_order.id")
	if req.UserId != nil {
		count = count.Where("tb_order.user_id = ?", req.UserId)
	}
	if req.OrderTypeId != nil {
		count = count.Where("tb_order.order_type_id = ?", req.OrderTypeId)
	}
	if req.OrderNo != "" {
		count = count.Where("tb_order.order_no = ?", req.OrderNo)
	}
	if req.TransactionNo != "" {
		count = count.Where("tb_order.transaction_no = ?", req.TransactionNo)
	}
	if req.Amount != "" {
		count = count.Where("tb_order.amount = ?", req.Amount)
	}
	if req.Status != "" {
		count = count.Where("tb_order.transaction_status = ?", req.Status)
	}

	if err := count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount, tb_order.transfer_amount AS transfer_amount"
		selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
		selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.payment_page_url AS payment_page_url"
		selectedFields += ", tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
		selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

		query := r.db.Table("paygate_bizpay_order as tb_order")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN paygate_order_type AS tb_type ON tb_order.order_type_id = tb_type.id")
		if req.UserId != nil {
			query = query.Where("tb_order.user_id = ?", req.UserId)
		}
		if req.OrderTypeId != nil {
			query = query.Where("tb_order.order_type_id = ?", req.OrderTypeId)
		}
		if req.OrderNo != "" {
			query = query.Where("tb_order.order_no = ?", req.OrderNo)
		}
		if req.TransactionNo != "" {
			query = query.Where("tb_order.transaction_no = ?", req.TransactionNo)
		}
		if req.Amount != "" {
			query = query.Where("tb_order.amount = ?", req.Amount)
		}
		if req.Status != "" {
			query = query.Where("tb_order.transaction_status = ?", req.Status)
		}

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetDbBizpayOrderById(id int64) (*model.BizpayOrderResponse, error) {

	var record model.BizpayOrderResponse

	selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount, tb_order.transfer_amount AS transfer_amount"
	selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
	selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.payment_page_url AS payment_page_url"
	selectedFields += ", tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
	selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

	if err := r.db.Table("paygate_bizpay_order as tb_order").
		Select(selectedFields).
		Joins("LEFT JOIN paygate_order_type AS tb_type ON tb_order.order_type_id = tb_type.id").
		Where("tb_order.id = ?", id).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetDbBizpayOrderByRefId(refId int64) (*model.BizpayOrderResponse, error) {

	var record model.BizpayOrderResponse

	selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount, tb_order.transfer_amount AS transfer_amount"
	selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
	selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.payment_page_url AS payment_page_url"
	selectedFields += ", tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
	selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

	if err := r.db.Table("paygate_bizpay_order as tb_order").
		Select(selectedFields).
		Joins("LEFT JOIN paygate_order_type AS tb_type ON tb_order.order_type_id = tb_type.id").
		Where("tb_order.ref_id = ?", refId).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) CheckBizpayDepositOrderInLast5Minutes(userId int64, amount float64) (*model.BizpayOrderResponse, error) {

	var record model.BizpayOrderResponse

	selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount, tb_order.transfer_amount AS transfer_amount"
	selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
	selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.payment_page_url AS payment_page_url"
	selectedFields += ", tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
	selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

	query := r.db.Table("paygate_bizpay_order as tb_order")
	query = query.Select(selectedFields)
	query = query.Joins("LEFT JOIN paygate_order_type AS tb_type ON tb_order.order_type_id = tb_type.id")
	query = query.Where("tb_order.user_id = ?", userId)
	query = query.Where("tb_order.amount = ?", amount)
	if err := query.
		Where("tb_order.transaction_status = ?", "WAIT_PAYMENT").
		Order("tb_order.id DESC").
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) CreateDbBizpayOrder(body model.BizpayOrderCreateBody) (*int64, error) {

	if err := r.db.Table("paygate_bizpay_order").Create(&body).Error; err != nil {
		return nil, err
	}

	// Use Random UUID as OrderNo //
	uuid := helper.GenerateRandomUuid(16)
	agentName := uuid

	orderNo := fmt.Sprintf("%v%v%v", agentName, time.Now().Format("0601"), body.Id)
	if body.OrderNo == "" {
		updateBody := make(map[string]interface{})
		updateBody["order_no"] = orderNo
		if err := r.db.Table("paygate_bizpay_order").Where("id = ?", body.Id).Updates(updateBody).Error; err != nil {
			return nil, err
		}
	}
	return &body.Id, nil
}

func (r repo) UpdateDbBizpayOrderError(id int64, remark string) error {

	updateBody := map[string]interface{}{
		"transaction_status": "ERROR",
		"remark":             remark,
	}
	sql := r.db.Table("paygate_bizpay_order").Where("id = ?", id).Where("transaction_status = ?", "PENDING")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdateDbBizpayOrder(id int64, body model.BizpayOrderUpdateBody) error {

	if err := r.db.Table("paygate_bizpay_order").Where("id = ?", id).Updates(body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) ApproveDbBizpayOrder(id int64, webhookStatus string) error {

	updateBody := make(map[string]interface{})
	updateBody["transaction_status"] = webhookStatus
	updateBody["payment_at"] = time.Now()

	sql := r.db.Table("paygate_bizpay_order").Where("id = ?", id).Where("transaction_status = ?", "WAIT_PAYMENT")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdateBizpayOrderBankSucess(id int64, transId int64, actionBy int64) error {

	updateBody := map[string]interface{}{
		"bank_transaction_id":     transId,
		"bank_transaction_status": "SUCCESS",
		"action_by":               actionBy,
	}

	sql := r.db.Table("paygate_bizpay_order").Where("id = ?", id).Where("bank_transaction_status = ?", "PENDING")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}
