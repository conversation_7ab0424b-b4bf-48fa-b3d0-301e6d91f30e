package repository

import (
	"cybergame-api/model"

	"gorm.io/gorm"
)

func NewAffiliateLimitRepository(db *gorm.DB) AffiliateLimitRepository {
	return &repo{db}
}

type AffiliateLimitRepository interface {
	GetAffiliateDownlineTotalIncomeMap(refId int64) (map[int64]model.AffiliateDownlineTotalIncomeResponse, error)
	GetAffiliateDownlineTotalIncomeList(refIds []int64) ([]model.AffiliateDownlineTotalIncomeResponse, error)
	// CreateAffiliateDownlineTotalIncome(body model.AffiliateDownlineTotalIncomeCreateBody) (*int64, error)
	// IncreaseAffiliateDownlineTotalIncome(userId int64, incomeAmount float64) error
}

func (r repo) GetAffiliateDownlineTotalIncomeMap(refId int64) (map[int64]model.AffiliateDownlineTotalIncomeResponse, error) {

	result := make(map[int64]model.AffiliateDownlineTotalIncomeResponse)
	var list []model.AffiliateDownlineTotalIncomeResponse

	// MOVE TABLE affiliate_downline_total_income => affiliate_level

	// SELECT //
	selectedFields := "tb_total_log.id, tb_total_log.user_id, tb_total_log.total_commission, tb_total_log.created_at"
	selectedFields += ", tb_total_log.upline_id AS ref_by"
	query := r.db.Table("affiliate_level AS tb_total_log")
	query = query.Select(selectedFields)
	query = query.Where("tb_total_log.upline_id = ?", refId)
	if err := query.Scan(&list).Error; err != nil {
		return nil, err
	}

	for _, item := range list {
		result[item.UserId] = item
	}
	return result, nil
}

func (r repo) GetAffiliateDownlineTotalIncomeList(refIds []int64) ([]model.AffiliateDownlineTotalIncomeResponse, error) {

	var list []model.AffiliateDownlineTotalIncomeResponse

	// MOVE TABLE affiliate_downline_total_income => affiliate_level

	// SELECT //
	selectedFields := "tb_total_log.id, tb_total_log.user_id, tb_total_log.total_commission, tb_total_log.created_at"
	selectedFields += ", tb_total_log.upline_id AS ref_by"
	query := r.db.Table("affiliate_level AS tb_total_log")
	query = query.Select(selectedFields)
	query = query.Where("tb_total_log.upline_id IN (?)", refIds)
	if err := query.Scan(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (r repo) CreateAffiliateDownlineTotalIncome(body model.AffiliateDownlineTotalIncomeCreateBody) (*int64, error) {

	if err := r.db.Table("affiliate_downline_total_income").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) IncreaseAffiliateDownlineTotalIncome(userId int64, incomeAmount float64) error {

	// Increase the total commission
	if err := r.db.Table("affiliate_downline_total_income").Where("user_id = ?", userId).Update("total_commission", gorm.Expr("total_commission + ?", incomeAmount)).Error; err != nil {
		return err
	}
	return nil
}
