package repository

import (
	"cybergame-api/model"

	"gorm.io/gorm"
)

func NewBannerRepository(db *gorm.DB) BannerRepository {
	return &repo{db}
}

type BannerRepository interface {
	GetBannerById(bannerId int64) (*model.BannerDetail, error)
	GetBannerIsActive(query model.BannerWebQuery) ([]model.BannerDetail, error)
	GetBannerList(query model.BannerQuery) ([]model.BannerDetail, error)
	GetBannerShopList() ([]model.BannerDetail, []model.BannerDetail, error)
	GetBannerTicketList() ([]model.BannerDetail, error)
	GetCoverUrlByBannerId(bannerId int64) (string, error)
	GetBannerTypeList() ([]model.BannerTypeListResponse, error)
	CountBannerByType(bannerType string) (int64, error)
	CountBannerIsActiveByType(bannerType string) (int64, error)
	CreateBanner(banner model.BannerCreateBody) error
	UpdateBanner(body model.BannerUpdateBody) error
	UpdateBannerSortOrder(list model.BannerSortBody) error
	UpdateBannerShop(bannerId string) error
	UpdateBannerTicket(bannerId string) error
	DeleteBanner(bannerId int64) error
}

func (r repo) GetBannerById(bannerId int64) (*model.BannerDetail, error) {

	var result model.BannerDetail

	if err := r.db.Table("banner").
		Select("id, banner_url, type, sort_order, is_active").
		Where("id = ?", bannerId).
		Take(&result).
		Error; err != nil {
		return nil, err
	}

	return &result, nil
}

func (r repo) GetBannerIsActive(query model.BannerWebQuery) ([]model.BannerDetail, error) {

	var result []model.BannerDetail

	exec := r.db.Table("banner")

	if query.Type == "SHOP_TOP" || query.Type == "TICKET" {
		exec = exec.Select("id, banner_url, type, is_active")
	} else {
		exec = exec.Select("id, banner_url, type, sort_order, is_active")
	}

	if err := exec.
		Where("type = ?", query.Type).
		Where("is_active = ?", true).
		Order("sort_order ASC").
		Find(&result).
		Error; err != nil {
		return nil, err
	}

	return result, nil
}

func (r repo) GetBannerList(query model.BannerQuery) ([]model.BannerDetail, error) {

	var result []model.BannerDetail

	if err := r.db.Table("banner").
		Select("id, banner_url, type, sort_order, is_active").
		Where("type = ?", query.Type).
		Order("sort_order ASC").
		Find(&result).
		Error; err != nil {
		return nil, err
	}

	return result, nil
}

func (r repo) GetBannerShopList() ([]model.BannerDetail, []model.BannerDetail, error) {

	var shopTop []model.BannerDetail
	var shopBottom []model.BannerDetail

	if err := r.db.Table("banner").
		Select("id, banner_url, type, is_active").
		Where("type = ?", "SHOP_TOP").
		Find(&shopTop).
		Error; err != nil {
		return nil, nil, err
	}

	if err := r.db.Table("banner").
		Select("id, banner_url, type, sort_order, is_active").
		Where("type = ?", "SHOP_BOTTOM").
		Order("sort_order ASC").
		Find(&shopBottom).
		Error; err != nil {
		return nil, nil, err
	}

	return shopTop, shopBottom, nil
}

func (r repo) GetBannerTicketList() ([]model.BannerDetail, error) {

	var ticketList []model.BannerDetail

	if err := r.db.Table("banner").
		Select("id, banner_url, type, is_active").
		Where("type = ?", "TICKET").
		Find(&ticketList).
		Error; err != nil {
		return nil, err
	}

	return ticketList, nil
}

func (r repo) GetCoverUrlByBannerId(bannerId int64) (string, error) {

	var result string

	if err := r.db.Table("banner").
		Select("banner_url").
		Where("id = ?", bannerId).
		Pluck("banner_url", &result).
		Error; err != nil {
		return "", err
	}

	return result, nil
}

func (r repo) GetBannerTypeList() ([]model.BannerTypeListResponse, error) {

	var result []model.BannerTypeListResponse

	result = append(result, model.BannerTypeListResponse{
		List: []string{"SHOP_TOP", "SHOP_BOTTOM", "TICKET", "STAFF", "PLAYER", "YOUTH"},
	})

	return result, nil
}

func (r repo) CountBannerByType(bannerType string) (int64, error) {

	var result int64

	if err := r.db.Table("banner").
		Select("id").
		Where("type = ?", bannerType).
		Count(&result).
		Error; err != nil {
		return 0, err
	}

	return result, nil
}

func (r repo) CountBannerIsActiveByType(bannerType string) (int64, error) {

	var result int64

	if err := r.db.Table("banner").
		Select("id").
		Where("type = ?", bannerType).
		Where("is_active = ?", true).
		Count(&result).
		Error; err != nil {
		return 0, err
	}

	return result, nil
}

func (r repo) CreateBanner(banner model.BannerCreateBody) error {

	tx := r.db.Begin()

	if err := tx.Table("banner").Where("is_active = ?", true).Update("is_active", false).Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Table("banner").Create(&banner).Error; err != nil {
		return err
	}

	if err := tx.Commit().Error; err != nil {
		return err
	}

	return nil
}

func (r repo) UpdateBanner(body model.BannerUpdateBody) error {

	update := map[string]interface{}{
		"banner_url": body.BannerUrl,
		"is_active":  body.IsActive,
	}

	if err := r.db.Table("banner").
		Where("id = ?", body.Id).
		Where("type NOT IN ?", []string{"SHOP_TOP", "TICKET"}).
		Updates(&update).
		Error; err != nil {
		return err
	}

	return nil
}

func (r repo) UpdateBannerSortOrder(list model.BannerSortBody) error {

	tx := r.db.Begin()

	for _, item := range list.List {
		if err := tx.Table("banner").Where("id = ?", item.Id).Update("sort_order", item.SortOrder).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	if err := tx.Commit().Error; err != nil {
		return err
	}

	return nil
}

func (r repo) UpdateBannerShop(bannerId string) error {

	tx := r.db.Begin()

	if err := tx.Table("banner").
		Where("is_active = ?", true).
		Where("type = ?", "SHOP_TOP").
		Update("is_active", false).
		Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Table("banner").
		Where("id = ?", bannerId).
		Where("type = ?", "SHOP_TOP").
		Update("is_active", true).
		Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Commit().Error; err != nil {
		return err
	}

	return nil
}

func (r repo) UpdateBannerTicket(bannerId string) error {

	tx := r.db.Begin()

	if err := tx.Table("banner").
		Where("is_active = ?", true).
		Where("type = ?", "TICKET").
		Update("is_active", false).
		Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Table("banner").
		Where("id = ?", bannerId).
		Where("type = ?", "TICKET").
		Update("is_active", true).
		Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Commit().Error; err != nil {
		return err
	}

	return nil
}

func (r repo) DeleteBanner(bannerId int64) error {

	if err := r.db.Table("banner").Where("id = ?", bannerId).Delete(&model.Banner{}).Error; err != nil {
		return err
	}

	return nil
}
