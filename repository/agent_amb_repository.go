package repository

import (
	"bytes"
	"cybergame-api/helper"
	"cybergame-api/model"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"time"

	"gorm.io/gorm"
)

func NewAgentAmbRepository(db *gorm.DB) AgentAmbRepository {
	return &repo{db}
}

type AgentAmbRepository interface {
	AmbRegister(data model.AmbRegister) error
	AmbLogin(data model.AmbLogin) (*model.AmbLoginResponse, error)
	AmbChangePassword(data model.AmbChangePassword) (*model.AmbChangePasswordResponse, error)
	AmbGetCredit(data model.AmbBalance) (*model.AmbBalanceResponse, error)
	AmbPlay(data model.AmbStartGameRequest) (*model.AmbStartGameResponse, error)

	AmbDepositAgent(data model.AmbDeposit) (*model.AmbDepositReponse, error)
	AmbWithdrawAgent(data model.AmbWithdraw) (*model.AmbWithdrawReponse, error)
	// Game
	AmbGetGameProviderList(req model.AmbGameProviderListRequest) (*model.AmbGameProviderReponse, error)
	AmbGetGameList(req model.AmbGameListRequest) (*model.AmbGameReponse, error)
	// Report
	AmbSimpleWinLose(data model.AmbSimpleWinlose) (*model.AmbSimpleWinloseResponse, error)
	AmbGetLastPageByPath(path string) (int64, error)
	AmbGetApistatus(req model.ApiStatusRequest) (*model.ApiStatus, error)
	InsertAmbApiStatus(path, date string) error
	InsertAmbPlayLog(list []model.AgentPlayLog) error
	AmbUpdateFailed(id int64, page int) error
	AmbUpdateSuccess(id int64) error

	// other agent adjust credit
	AmbWithdrawAgentFromOtherAgent(data model.AmbWithdraw) (*model.AmbWithdrawReponse, error)
	AmbDepositAgentForOtherAgent(data model.AmbDeposit) (*model.AmbDepositReponse, error)
}

func (r repo) AmbRegister(data model.AmbRegister) error {

	data.AgentUsername = os.Getenv("AGENT_NAME")
	data.Key = os.Getenv("AGENT_KEY")
	data.Web = os.Getenv("AGENT_WEB_NAME")

	// log.Println("AmbRegister req ------> ", helper.StructJson(data))

	// https://api-proxy.a4u.top/ambexapi/ext/createUser/aaaaaaaaaaaaaa/bbbbbbbbbbbb
	url := fmt.Sprintf("%s/ext/createUser/%s/%s", os.Getenv("AGENT_ENDPOINT"), os.Getenv("AGENT_PREFIX"), os.Getenv("AGENT_NAME"))

	// [SYSLOG] INIT
	sysLogId, err := r.CreateAgentLog(model.AgentLogCreateBody{
		Name:   "AmbRegister",
		Status: "PENDING",
		JsonRequest: helper.StructJson(map[string]interface{}{
			"endpoint": url,
			"body":     data,
		}),
	})
	if err != nil {
		log.Println("AmbRegister.CreateAgentLog error ------> ", err.Error())
	}

	client := &http.Client{}
	client.Timeout = 3 * time.Second
	jsonBody, _ := json.Marshal(data)
	reqBody := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest("POST", url, reqBody)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("Accept", "application/json")
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println("AmbRegister.CLIENT_CALL_ERROR", err.Error())
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type": "CLIENT_CALL_ERROR",
				"error_msg":  err.Error(),
			}),
		}); sysLogErr != nil {
			log.Println("AmbRegister.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return errors.New("CLIENT_CALL_ERROR")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println("AmbRegister.RESPONSE_READ_ERROR", err)
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type": "RESPONSE_READ_ERROR",
				"error_msg":  err.Error(),
			}),
		}); sysLogErr != nil {
			log.Println("AmbRegister.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return errors.New("RESPONSE_READ_ERROR")
	}

	if response.StatusCode != 200 {
		log.Println("AmbRegister.HTTP_NOT_200", response.StatusCode)
		log.Println("AmbRegister.response_data", string(responseData))
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type":    "INVALID_RESPONSE_CODE",
				"error_msg":     "HTTP_NOT_200",
				"http_code":     response.StatusCode,
				"response_data": string(responseData),
			}),
		}); sysLogErr != nil {
			log.Println("AmbRegister.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return errors.New("INVALID_RESPONSE_CODE")
	}

	var result model.AmbRegisterResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("AmbRegister.CANT_PARSE_RESPONSE_DATA ------> ", string(responseData))
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type":    "Unmarshal_ERROR",
				"error_msg":     errJson.Error(),
				"response_data": string(responseData),
			}),
		}); sysLogErr != nil {
			log.Println("AmbRegister.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return errors.New("CANT_PARSE_RESPONSE_DATA")
	}
	if result.Code != 0 {
		log.Println("AmbRegister CANT_REGISTER", helper.StructJson(result))
		// CANT_REGISTER_403 : Permission denied.
		// CANT_REGISTER_100007 : Missing 'password' field.
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type":     "CANT_REGISTER",
				"error_msg":      result.Msg,
				"response_model": helper.StructJson(result),
			}),
		}); sysLogErr != nil {
			log.Println("AmbRegister.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return fmt.Errorf("CANT_REGISTER_%d", result.Code)
	}

	// [SYSLOG]
	if sysLogErr := r.SetAgentLogSuccess(model.AgentLogUpdateBody{
		Id:          *sysLogId,
		JsonReponse: helper.StructJson(result),
	}); sysLogErr != nil {
		log.Println("AmbRegister.SetAgentLogSuccess error ------> ", sysLogErr.Error())
	}
	return nil
}

func (r repo) AmbLogin(data model.AmbLogin) (*model.AmbLoginResponse, error) {

	data.AgentUsername = os.Getenv("AGENT_NAME")
	data.Key = os.Getenv("AGENT_KEY")
	data.Web = os.Getenv("AGENT_WEB_NAME")

	// log.Println("AmbLogin req ------> ", helper.StructJson(data))

	// https://api-proxy.a4u.top/ambexapi/ext/redirectLogin/aaaaaaaaaaaaaa/bbbbbbbbbbbb
	url := fmt.Sprintf("%s/ext/redirectLogin/%s/%s", os.Getenv("AGENT_ENDPOINT"), os.Getenv("AGENT_PREFIX"), os.Getenv("AGENT_NAME"))

	client := &http.Client{}
	client.Timeout = 3 * time.Second
	jsonBody, _ := json.Marshal(data)
	reqBody := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest("POST", url, reqBody)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("Accept", "application/json")
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println("AmbLogin.CLIENT_CALL_ERROR", err.Error())
		return nil, errors.New("CLIENT_CALL_ERROR")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println("AmbLogin.RESPONSE_READ_ERROR", err)
		return nil, errors.New("RESPONSE_READ_ERROR")
	}

	if response.StatusCode != 200 {
		log.Println("AmbLogin.HTTP_NOT_200", response.StatusCode)
		log.Println("AmbLogin.response_data", string(responseData))
		return nil, errors.New("INVALID_RESPONSE_CODE")
	}

	var result model.AmbLoginResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("AmbLogin.CANT_PARSE_RESPONSE_DATA ------> ", string(responseData))
		return nil, errors.New("CANT_PARSE_RESPONSE_DATA")
	}
	if result.Code != 0 {
		log.Println("AmbLogin result ------> ", helper.StructJson(result))
		// CANT_LOGIN_403 : Permission denied.
		return nil, fmt.Errorf("CANT_LOGIN_%d", result.Code)
	}
	return &result, nil
}

func (r repo) AmbChangePassword(data model.AmbChangePassword) (*model.AmbChangePasswordResponse, error) {

	data.AgentUsername = os.Getenv("AGENT_NAME")
	data.Key = os.Getenv("AGENT_KEY")
	data.Web = os.Getenv("AGENT_WEB_NAME")

	// log.Println("AmbChangePassword req ------> ", helper.StructJson(data))

	// https://api-proxy.a4u.top/ambexapi/ext/changePassword/aaaaaaaaaaaaaa/bbbbbbbbbbbb
	url := fmt.Sprintf("%s/ext/changePassword/%s/%s", os.Getenv("AGENT_ENDPOINT"), os.Getenv("AGENT_PREFIX"), os.Getenv("AGENT_NAME"))

	// [SYSLOG] INIT
	sysLogId, err := r.CreateAgentLog(model.AgentLogCreateBody{
		Name:   "AmbChangePassword",
		Status: "PENDING",
		JsonRequest: helper.StructJson(map[string]interface{}{
			"endpoint": url,
			"body":     data,
		}),
	})
	if err != nil {
		log.Println("AmbChangePassword.CreateAgentLog error ------> ", err.Error())
	}

	client := &http.Client{}
	client.Timeout = 3 * time.Second
	jsonBody, _ := json.Marshal(data)
	reqBody := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest("POST", url, reqBody)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("Accept", "application/json")
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println("AmbChangePassword.CLIENT_CALL_ERROR", err.Error())
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type": "CLIENT_CALL_ERROR",
				"error_msg":  err.Error(),
			}),
		}); sysLogErr != nil {
			log.Println("AmbChangePassword.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, errors.New("CLIENT_CALL_ERROR")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println("AmbChangePassword.RESPONSE_READ_ERROR", err)
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type": "RESPONSE_READ_ERROR",
				"error_msg":  err.Error(),
			}),
		}); sysLogErr != nil {
			log.Println("AmbChangePassword.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, errors.New("RESPONSE_READ_ERROR")
	}

	if response.StatusCode != 200 {
		log.Println("AmbChangePassword.HTTP_NOT_200", response.StatusCode)
		log.Println("AmbChangePassword.response_data", string(responseData))
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type":    "INVALID_RESPONSE_CODE",
				"error_msg":     "HTTP_NOT_200",
				"http_code":     response.StatusCode,
				"response_data": string(responseData),
			}),
		}); sysLogErr != nil {
			log.Println("AmbChangePassword.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, errors.New("INVALID_RESPONSE_CODE")
	}

	var result model.AmbChangePasswordResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("AmbChangePassword.CANT_PARSE_RESPONSE_DATA ------> ", string(responseData))
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type":    "Unmarshal_ERROR",
				"error_msg":     errJson.Error(),
				"response_data": string(responseData),
			}),
		}); sysLogErr != nil {
			log.Println("AmbChangePassword.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, errors.New("CANT_PARSE_RESPONSE_DATA")
	}
	if result.Code != 0 {
		log.Println("AmbChangePassword result ------> ", helper.StructJson(result))
		// CANT_CHANGE_PASSWORD_403 : Permission denied.
		// CANT_CHANGE_PASSWORD_2007 : Password is invalid.
		// CANT_CHANGE_PASSWORD_12 : Password is same.
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type":     "CANT_CHANGE_PASSWORD",
				"error_msg":      result.Msg,
				"response_model": helper.StructJson(result),
			}),
		}); sysLogErr != nil {
			log.Println("AmbChangePassword.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		if result.Code != 12 {
			return nil, fmt.Errorf("CANT_CHANGE_PASSWORD_%d", result.Code)
		}
	}

	// [SYSLOG]
	if sysLogErr := r.SetAgentLogSuccess(model.AgentLogUpdateBody{
		Id:          *sysLogId,
		JsonReponse: helper.StructJson(result),
	}); sysLogErr != nil {
		log.Println("AmbChangePassword.SetAgentLogSuccess error ------> ", sysLogErr.Error())
	}
	return &result, nil
}

func (r repo) AmbGetCredit(data model.AmbBalance) (*model.AmbBalanceResponse, error) {

	data.AgentUsername = os.Getenv("AGENT_NAME")
	data.Key = os.Getenv("AGENT_KEY")
	data.Web = os.Getenv("AGENT_WEB_NAME")

	// log.Println("AmbGetCredit req ------> ", helper.StructJson(data))

	var result *model.AmbBalanceResponse

	// https://api-proxy.a4u.top/ambexapi/ext/getProfileAndCredit/aaaaaaaaaaaaaa/bbbbbbbbbbbb
	url := fmt.Sprintf("%s/ext/getProfileAndCredit/%s/%s", os.Getenv("AGENT_ENDPOINT"), os.Getenv("AGENT_PREFIX"), os.Getenv("AGENT_NAME"))

	res, err := helper.Post(url, data)
	if err != nil {
		log.Println("AmbGetCredit error ------> ", helper.StructJson(err))
		return nil, err
	}

	log.Println("AmbGetCredit resp ------> ", helper.StructJson(res))

	jsonRes, _ := json.Marshal(res)
	if err := json.Unmarshal(jsonRes, &result); err != nil {
		return nil, err
	}

	return result, nil
}

func (r repo) AmbDepositAgent(data model.AmbDeposit) (*model.AmbDepositReponse, error) {

	data.AgentUsername = os.Getenv("AGENT_NAME")
	data.Key = os.Getenv("AGENT_KEY")
	data.Web = os.Getenv("AGENT_WEB_NAME")

	// ** IsDp=true คือ เป็นรายการฝากเงิน และ ใช้สำหรับนับยอดได้เสีย และ เทรินโอเวอร์
	// false คือ เป็นรายการฝากเงินเพื่อคืนเงินเข้ารายการฝากไม่นำไปนับยอดได้เสีย และ เทรินโอเวอร์

	// log.Println("AmbDepositAgent req ------> ", helper.StructJson(data))

	// https://api-proxy.a4u.top/ambexapi/ext/deposit/aaaaaaaaaaaaaa/bbbbbbbbbbbb
	url := fmt.Sprintf("%s/ext/deposit/%s/%s", os.Getenv("AGENT_ENDPOINT"), os.Getenv("AGENT_PREFIX"), os.Getenv("AGENT_NAME"))

	// [SYSLOG] INIT
	sysLogId, err := r.CreateAgentLog(model.AgentLogCreateBody{
		Name:   "AmbDepositAgent",
		Status: "PENDING",
		JsonRequest: helper.StructJson(map[string]interface{}{
			"endpoint": url,
			"body":     data,
		}),
	})
	if err != nil {
		log.Println("AmbDepositAgent.CreateAgentLog error ------> ", err.Error())
	}

	client := &http.Client{}
	client.Timeout = 3 * time.Second
	jsonBody, _ := json.Marshal(data)
	reqBody := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest("POST", url, reqBody)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("Accept", "application/json")
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println("AmbDepositAgent.CLIENT_CALL_ERROR", err.Error())
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type": "CLIENT_CALL_ERROR",
				"error_msg":  err.Error(),
			}),
		}); sysLogErr != nil {
			log.Println("AmbDepositAgent.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, errors.New("CLIENT_CALL_ERROR")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println("AmbDepositAgent.RESPONSE_READ_ERROR", err)
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type": "RESPONSE_READ_ERROR",
				"error_msg":  err.Error(),
			}),
		}); sysLogErr != nil {
			log.Println("AmbDepositAgent.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, errors.New("RESPONSE_READ_ERROR")
	}

	if response.StatusCode != 200 {
		log.Println("AmbDepositAgent.HTTP_NOT_200", response.StatusCode)
		log.Println("AmbDepositAgent.response_data", string(responseData))
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type":    "INVALID_RESPONSE_CODE",
				"error_msg":     "HTTP_NOT_200",
				"http_code":     response.StatusCode,
				"response_data": string(responseData),
			}),
		}); sysLogErr != nil {
			log.Println("AmbDepositAgent.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, errors.New("INVALID_RESPONSE_CODE")
	}

	var result model.AmbDepositReponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("AmbDepositAgent.CANT_PARSE_RESPONSE_DATA ------> ", string(responseData))
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type":    "Unmarshal_ERROR",
				"error_msg":     errJson.Error(),
				"response_data": string(responseData),
			}),
		}); sysLogErr != nil {
			log.Println("AmbDepositAgent.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, errors.New("CANT_PARSE_RESPONSE_DATA")
	}
	if result.Code != 0 {
		log.Println("AmbDepositAgent result ------> ", helper.StructJson(result))
		// CANT_DEPOSIT_403 : Permission denied.
		// CANT_DEPOSIT_400 : Data bad request.
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type":     "CANT_DEPOSIT",
				"error_msg":      result.Msg,
				"response_model": helper.StructJson(result),
			}),
		}); sysLogErr != nil {
			log.Println("AmbDepositAgent.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, fmt.Errorf("CANT_DEPOSIT_%d", result.Code)
	}

	// [SYSLOG]
	if sysLogErr := r.SetAgentLogSuccess(model.AgentLogUpdateBody{
		Id:          *sysLogId,
		JsonReponse: helper.StructJson(result),
	}); sysLogErr != nil {
		log.Println("AmbDepositAgent.SetAgentLogSuccess error ------> ", sysLogErr.Error())
	}
	return &result, nil
}

func (r repo) AmbWithdrawAgent(data model.AmbWithdraw) (*model.AmbWithdrawReponse, error) {

	data.AgentUsername = os.Getenv("AGENT_NAME")
	data.Key = os.Getenv("AGENT_KEY")
	data.Web = os.Getenv("AGENT_WEB_NAME")

	// log.Println("AmbWithdrawAgent req ------> ", helper.StructJson(data))

	// https://api-proxy.a4u.top/ambexapi/ext/withdrawal/aaaaaaaaaaaaaa/bbbbbbbbbbbb
	url := fmt.Sprintf("%s/ext/withdrawal/%s/%s", os.Getenv("AGENT_ENDPOINT"), os.Getenv("AGENT_PREFIX"), os.Getenv("AGENT_NAME"))

	// [SYSLOG] INIT
	sysLogId, err := r.CreateAgentLog(model.AgentLogCreateBody{
		Name:   "AmbWithdrawAgent",
		Status: "PENDING",
		JsonRequest: helper.StructJson(map[string]interface{}{
			"endpoint": url,
			"body":     data,
		}),
	})
	if err != nil {
		log.Println("AmbWithdrawAgent.CreateAgentLog error ------> ", err.Error())
	}

	client := &http.Client{}
	client.Timeout = 3 * time.Second
	jsonBody, _ := json.Marshal(data)
	reqBody := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest("POST", url, reqBody)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("Accept", "application/json")
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println("AmbWithdrawAgent.CLIENT_CALL_ERROR", err.Error())
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type": "CLIENT_CALL_ERROR",
				"error_msg":  err.Error(),
			}),
		}); sysLogErr != nil {
			log.Println("AmbWithdrawAgent.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, errors.New("CLIENT_CALL_ERROR")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println("AmbWithdrawAgent.RESPONSE_READ_ERROR", err)
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type": "RESPONSE_READ_ERROR",
				"error_msg":  err.Error(),
			}),
		}); sysLogErr != nil {
			log.Println("AmbWithdrawAgent.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, errors.New("RESPONSE_READ_ERROR")
	}

	if response.StatusCode != 200 {
		log.Println("AmbWithdrawAgent.HTTP_NOT_200", response.StatusCode)
		log.Println("AmbWithdrawAgent.response_data", string(responseData))
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type":    "INVALID_RESPONSE_CODE",
				"error_msg":     "HTTP_NOT_200",
				"http_code":     response.StatusCode,
				"response_data": string(responseData),
			}),
		}); sysLogErr != nil {
			log.Println("AmbWithdrawAgent.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, errors.New("INVALID_RESPONSE_CODE")
	}

	var result model.AmbWithdrawReponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("AmbWithdrawAgent.CANT_PARSE_RESPONSE_DATA ------> ", string(responseData))
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type":    "Unmarshal_ERROR",
				"error_msg":     errJson.Error(),
				"response_data": string(responseData),
			}),
		}); sysLogErr != nil {
			log.Println("AmbWithdrawAgent.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, errors.New("CANT_PARSE_RESPONSE_DATA")
	}
	if result.Code != 0 {
		log.Println("AmbWithdrawAgent result ------> ", helper.StructJson(result))
		// CANT_WITHDRAW_403 : Permission denied.
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type":     "CANT_WITHDRAW",
				"error_msg":      result.Msg,
				"response_model": helper.StructJson(result),
			}),
		}); sysLogErr != nil {
			log.Println("AmbWithdrawAgent.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, fmt.Errorf("CANT_WITHDRAW_%d", result.Code)
	}

	// [SYSLOG]
	if sysLogErr := r.SetAgentLogSuccess(model.AgentLogUpdateBody{
		Id:          *sysLogId,
		JsonReponse: helper.StructJson(result),
	}); sysLogErr != nil {
		log.Println("AmbWithdrawAgent.SetAgentLogSuccess error ------> ", sysLogErr.Error())
	}
	return &result, nil
}

func (r repo) AmbGetGameProviderList(req model.AmbGameProviderListRequest) (*model.AmbGameProviderReponse, error) {

	req.AgentUsername = os.Getenv("AGENT_NAME")
	req.Key = os.Getenv("AGENT_KEY")
	req.Web = os.Getenv("AGENT_WEB_NAME")

	// log.Println("AmbGetGameProviderList req ------> ", helper.StructJson(req))

	// https://api-proxy.a4u.top/ambexapi/ext/providerPrefixList/aaaaaaaaaaaaaa/bbbbbbbbbbbb
	url := fmt.Sprintf("%s/ext/providerPrefixList/%s/%s", os.Getenv("AGENT_ENDPOINT"), os.Getenv("AGENT_PREFIX"), os.Getenv("AGENT_NAME"))

	client := &http.Client{}
	client.Timeout = 3 * time.Second
	jsonBody, _ := json.Marshal(req)
	reqBody := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest("POST", url, reqBody)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("Accept", "application/json")
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("INVALID_RESPONSE_DATA")
	}

	if response.StatusCode != 200 {
		log.Println(response.StatusCode)
		log.Println(string(responseData))
		return nil, errors.New("INVALID_RESPONSE_CODE")
	}

	var result model.AmbGameProviderReponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("AmbGetGameProviderList resp.Body ------> ", string(responseData))
		log.Println("errJson ------> ", errJson)
		return nil, errors.New("CANT_PARSE_RESPONSE_DATA")
	}
	if result.Code != 0 {
		log.Println("AmbGetGameProviderList result ------> ", helper.StructJson(result))
		// CANT_GETGAME_403 : Permission denied.
		return nil, fmt.Errorf("CANT_GETGAME_%d", result.Code)
	}
	return &result, nil
}

func (r repo) AmbGetGameList(req model.AmbGameListRequest) (*model.AmbGameReponse, error) {

	req.AgentUsername = os.Getenv("AGENT_NAME")
	req.Key = os.Getenv("AGENT_KEY")
	req.Web = os.Getenv("AGENT_WEB_NAME")

	// log.Println("AmbGetGameList req ------> ", helper.StructJson(req))

	// https://api-proxy.a4u.top/ambexapi/ext/gameListByProviderPrefix/aaaaaaaaaaaaaa/bbbbbbbbbbbb
	url := fmt.Sprintf("%s/ext/gameListByProviderPrefix/%s/%s", os.Getenv("AGENT_ENDPOINT"), os.Getenv("AGENT_PREFIX"), os.Getenv("AGENT_NAME"))

	client := &http.Client{}
	client.Timeout = 3 * time.Second
	jsonBody, _ := json.Marshal(req)
	reqBody := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest("POST", url, reqBody)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("Accept", "application/json")
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("INVALID_RESPONSE_DATA")
	}

	if response.StatusCode != 200 {
		log.Println(response.StatusCode)
		log.Println(string(responseData))
		return nil, errors.New("INVALID_RESPONSE_CODE")
	}

	var result model.AmbGameReponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("AmbGetGameList resp.Body ------> ", string(responseData))
		log.Println("errJson ------> ", errJson)
		return nil, errors.New("CANT_PARSE_RESPONSE_DATA")
	}
	if result.Code != 0 {
		log.Println("AmbGetGameList result ------> ", helper.StructJson(result))
		// CANT_REGISTER_403 : Permission denied.
		return nil, fmt.Errorf("CANT_REGISTER_%d", result.Code)
	}
	return &result, nil
}

func (r repo) AmbPlay(data model.AmbStartGameRequest) (*model.AmbStartGameResponse, error) {

	data.AgentUsername = os.Getenv("AGENT_NAME")
	data.Key = os.Getenv("AGENT_KEY")
	data.Web = os.Getenv("AGENT_WEB_NAME")

	// log.Println("AmbPlay req ------> ", helper.StructJson(data))

	// https://api-proxy.a4u.top/ambexapi/ext/startGame/aaaaaaaaaaaaaa/bbbbbbbbbbbb
	url := fmt.Sprintf("%s/ext/startGame/%s/%s", os.Getenv("AGENT_ENDPOINT"), os.Getenv("AGENT_PREFIX"), os.Getenv("AGENT_NAME"))
	log.Println("AmbPlay url ------> ", url)

	client := &http.Client{}
	client.Timeout = 3 * time.Second
	jsonBody, _ := json.Marshal(data)
	reqBody := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest("POST", url, reqBody)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("Accept", "application/json")
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("INVALID_RESPONSE_DATA")
	}
	if response.StatusCode != 200 {
		log.Println(response.StatusCode)
		log.Println(string(responseData))
		return nil, errors.New("INVALID_RESPONSE_CODE")
	}

	var result model.AmbStartGameResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("AmbPlay resp.Body ------> ", string(responseData))
		log.Println("Unmarshal.Err ------> ", errJson)
		// {"code":"2003","msg":"Sorry for inconvenience. We're under maintenance until 2023-12-25 11:45 (GMT+7) (mk9N27XWxnXPSFFYwig4o5)"}
		// {"code":"9999","msg":"Game is under maintenance (no53rmEbT4KGHx2ZgRNV6c)"}
		var errorResp model.AmbErrorReponse
		errJson2 := json.Unmarshal(responseData, &errorResp)
		if errJson2 != nil {
			log.Println("Unmarshal2.Err ------> ", errJson2)
			var error3Resp model.AmbErrorStringReponse
			errJson3 := json.Unmarshal(responseData, &error3Resp)
			if errJson3 != nil {
				log.Println("Unmarshal3.Err ------> ", errJson3)
				return nil, errors.New("CANT_PARSE_RESPONSE_DATA")
			}
			return nil, errors.New(error3Resp.Msg)
		}
		return nil, errors.New(errorResp.Msg)
	}
	if result.Code != 0 {
		log.Println("AmbPlay result ------> ", helper.StructJson(result))
		// CANT_STARTGAME_403 : Permission denied.
		// CANT_STARTGAME_100024 : missing 'gameID = no list = lobby
		// CANT_STARTGAME_{"code":100029,"msg":"This game is currently unavailable for you, please contact your agent."
		// return nil, fmt.Errorf("CANT_STARTGAME_%d", result.Code)
		return nil, errors.New(result.Msg)
	}
	return &result, nil
}

func (r repo) AmbSimpleWinLose(data model.AmbSimpleWinlose) (*model.AmbSimpleWinloseResponse, error) {

	data.AgentUsername = os.Getenv("AGENT_NAME")
	data.Key = os.Getenv("AGENT_KEY")
	data.Web = os.Getenv("AGENT_WEB_NAME")

	// log.Println("AmbSimpleWinLose req ------> ", helper.StructJson(data))

	// Use for Merchant Get Report Winlose Account
	// ** api สามารถเรียกใช้ได้ ชั่วโมงละ 6 คร้ง และ ใส่วันที่ได้ไม่เกิน 1 วัน **
	// Method : POST
	// Path : {{domain}}/ext/reportWinLoseAccount/{{prefix}}/{{agentUsername}}

	// Use for Merchant Get Report Total Winlose Account
	// ** api สามารถเรียกใช้ได้ ชั่วโมงละ 6 คร้ง และ ใส่วันที่ได้ไม่เกิน 1 วัน **
	// Method : POST
	// Path : {{domain}}/ext/totalReportWinLoseAccount/{{prefix}}/{{agentUsername}}

	// 11. Get Winlose And Turnover All Member By Date
	// 11.1 Get Winlose And Turnover All Member By Date
	// Use for Merchant Get Winlose And Turnover All Member By Date
	// ** api สามารถเรียกใช้ได้ ชั่วโมงละ 6 คร้ง และ ใส่วันที่ได้ไม่เกิน 1 วัน กรุณาดึงไปเก็บไว้ทุกวันถ้าจะทำคืนยอดเสียรายสัปดา หรือ เดือน **
	// Method : POST
	// Path : {{domain}}/ext/allMemberWinLoseByDate/{{prefix}}/{{agentUsername}}

	// 11.2 Get Total Winlose And Turnover All Member By Date

	// https://api-proxy.a4u.top/ambexapi/ext/allMemberWinLoseByDate/aaaaaaaaaaaaaa/bbbbbbbbbbbb
	url := fmt.Sprintf("%s/ext/allMemberWinLoseByDate/%s/%s", os.Getenv("AGENT_ENDPOINT"), os.Getenv("AGENT_PREFIX"), os.Getenv("AGENT_NAME"))
	log.Println("AmbSimpleWinLose url ------> ", url)

	client := &http.Client{}
	client.Timeout = 3 * time.Second
	jsonBody, _ := json.Marshal(data)
	reqBody := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest("POST", url, reqBody)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("Accept", "application/json")
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("INVALID_RESPONSE_DATA")
	}
	if response.StatusCode != 200 {
		log.Println(response.StatusCode)
		log.Println(string(responseData))
		return nil, errors.New("INVALID_RESPONSE_CODE")
	}

	var result model.AmbSimpleWinloseResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("AmbSimpleWinLose resp.Body ------> ", string(responseData))
		return nil, errors.New("CANT_PARSE_RESPONSE_DATA")
	}
	if result.Code != 0 {
		log.Println("AmbSimpleWinLose result ------> ", helper.StructJson(result))
		// CANT_SIMPLEWINLOSE_403 : Permission denied.
		// CANT_SIMPLEWINLOSE_400 : startDate and endDate cannot more than 24 hours.
		return nil, fmt.Errorf("CANT_SIMPLEWINLOSE_%d", result.Code)
	}
	return &result, nil
}

func (r repo) AmbGetApistatus(req model.ApiStatusRequest) (*model.ApiStatus, error) {

	var record model.ApiStatus

	selectedFields := "statuses.id as id, statuses.path as path, statuses.page as page, statuses.is_failed as is_failed, statuses.is_success as is_success"
	selectedFields += ", statuses.statement_date as statement_date, statuses.created_at as created_at, statuses.updated_at as updated_at"
	if err := r.db.Table("api_status as statuses").
		Select(selectedFields).
		Where("statuses.path = ?", req.Path).
		Where("statuses.statement_date = ?", req.StatementDate).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) InsertAmbApiStatus(path, date string) error {

	var id int64

	if err := r.db.Table("api_status").
		Select("id").
		Where("statement_date = ?", date).
		Where("path = ?", path).
		Where("is_success = ?", 0).
		Scan(&id).Error; err != nil {
		return err
	}

	if id < 1 {

		data := map[string]interface{}{}
		data["path"] = path
		data["statement_date"] = date

		if err := r.db.Table("api_status").
			Create(&data).Error; err != nil {
			return err
		}
	}

	return nil
}

func (r repo) InsertAmbPlayLog(list []model.AgentPlayLog) error {

	tx := r.db.Begin()

	if err := tx.Table("play_log").
		Create(&list).Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}

	return nil
}

func (r repo) AmbUpdateFailed(id int64, page int) error {

	obj := map[string]interface{}{}
	obj["is_failed"] = 1
	obj["page"] = page

	if err := r.db.Table("api_status").
		Where("id = ?", id).
		Where("is_success = ?", 0).
		Where("is_failed = ?", 0).
		Updates(obj).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) AmbUpdateSuccess(id int64) error {

	obj := map[string]interface{}{}
	obj["is_success"] = 1

	if err := r.db.Table("api_status").
		Where("id = ?", id).
		Where("is_success = ?", 0).
		Where("is_failed = ?", 0).
		Updates(obj).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) AmbGetLastPageByPath(path string) (int64, error) {

	var lastPage int64

	if err := r.db.Table("api_status").Where("path = ?", path).Select("page").Scan(&lastPage).Error; err != nil {
		return 0, err
	}

	return lastPage, nil
}

func (r repo) AmbGetRefListByMembers(members []string) ([]model.PlayerUserList, error) {

	var list []model.PlayerUserList
	var users []model.PlayerUserList

	if err := r.db.Table("user").
		Select("id AS user_id, member_code").
		Where("member_code IN ?", members).
		Scan(&users).Error; err != nil {
		return nil, err
	}

	userId := []int64{}
	for _, v := range users {
		userId = append(userId, v.UserID)
	}

	if err := r.db.Table("affiliate").
		Joins("LEFT JOIN user ref ON ref.id = affiliate.ref_id").
		Joins("LEFT JOIN user user ON user.id = affiliate.user_id").
		Select("affiliate.user_id, affiliate.ref_id, user.member_code").
		Where("affiliate.user_id IN ?", userId).
		Scan(&list).Error; err != nil {
		return nil, err
	}

	return list, nil
}

func (r repo) AmbWithdrawAgentFromOtherAgent(data model.AmbWithdraw) (*model.AmbWithdrawReponse, error) {

	data.AgentUsername = os.Getenv("AGENT_NAME")
	data.Key = os.Getenv("AGENT_KEY")
	data.Web = os.Getenv("AGENT_WEB_NAME")

	// log.Println("Start time ------> " + time.Now().Format("2006-01-02 15:04:05.000"))
	// log.Println("AmbWithdrawAgentFromOtherAgent req ------> ", helper.StructJson(data))

	// https://api-proxy.a4u.top/ambexapi/ext/withdrawal/aaaaaaaaaaaaaa/bbbbbbbbbbbb
	url := fmt.Sprintf("%s/ext/withdrawal/%s/%s", os.Getenv("AGENT_ENDPOINT"), os.Getenv("AGENT_PREFIX"), os.Getenv("AGENT_NAME"))

	// [SYSLOG] INIT
	sysLogId, err := r.CreateAgentLog(model.AgentLogCreateBody{
		Name:   "AmbWithdrawAgentFromOtherAgent",
		Status: "PENDING",
		JsonRequest: helper.StructJson(map[string]interface{}{
			"endpoint": url,
			"body":     data,
		}),
	})
	if err != nil {
		log.Println("AmbWithdrawAgentFromOtherAgent.CreateAgentLog error ------> ", err.Error())
	}

	client := &http.Client{}
	client.Timeout = 120 * time.Second // P.lay ขอ 2 min
	jsonBody, _ := json.Marshal(data)
	reqBody := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest("POST", url, reqBody)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("Accept", "application/json")
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println("AmbWithdrawAgentFromOtherAgent.CLIENT_CALL_ERROR", err.Error())
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type": "CLIENT_CALL_ERROR",
				"error_msg":  err.Error(),
			}),
		}); sysLogErr != nil {
			log.Println("AmbWithdrawAgentFromOtherAgent.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, errors.New("CLIENT_CALL_ERROR")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println("AmbWithdrawAgentFromOtherAgent.RESPONSE_READ_ERROR", err)
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type": "RESPONSE_READ_ERROR",
				"error_msg":  err.Error(),
			}),
		}); sysLogErr != nil {
			log.Println("AmbWithdrawAgentFromOtherAgent.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, errors.New("RESPONSE_READ_ERROR")
	}

	if response.StatusCode != 200 {
		log.Println("AmbWithdrawAgentFromOtherAgent.HTTP_NOT_200", response.StatusCode)
		log.Println("AmbWithdrawAgentFromOtherAgent.response_data", string(responseData))
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type":    "INVALID_RESPONSE_CODE",
				"error_msg":     "HTTP_NOT_200",
				"http_code":     response.StatusCode,
				"response_data": string(responseData),
			}),
		}); sysLogErr != nil {
			log.Println("AmbWithdrawAgentFromOtherAgent.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, errors.New("INVALID_RESPONSE_CODE")
	}

	var result model.AmbWithdrawReponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("AmbWithdrawAgentFromOtherAgent.CANT_PARSE_RESPONSE_DATA ------> ", string(responseData))
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type":    "Unmarshal_ERROR",
				"error_msg":     errJson.Error(),
				"response_data": string(responseData),
			}),
		}); sysLogErr != nil {
			log.Println("AmbWithdrawAgentFromOtherAgent.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, errors.New("CANT_PARSE_RESPONSE_DATA")
	}
	if result.Code != 0 {
		log.Println("AmbWithdrawAgentFromOtherAgent result ------> ", helper.StructJson(result))
		// CANT_WITHDRAW_403 : Permission denied.
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type":     "CANT_WITHDRAW",
				"error_msg":      result.Msg,
				"response_model": helper.StructJson(result),
			}),
		}); sysLogErr != nil {
			log.Println("AmbWithdrawAgentFromOtherAgent.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, fmt.Errorf("CANT_WITHDRAW_%d", result.Code)
	}

	// [SYSLOG]
	if sysLogErr := r.SetAgentLogSuccess(model.AgentLogUpdateBody{
		Id:          *sysLogId,
		JsonReponse: helper.StructJson(result),
	}); sysLogErr != nil {
		log.Println("AmbWithdrawAgentFromOtherAgent.SetAgentLogSuccess error ------> ", sysLogErr.Error())
	}

	fmt.Println("End")
	return &result, nil
}

func (r repo) AmbDepositAgentForOtherAgent(data model.AmbDeposit) (*model.AmbDepositReponse, error) {

	data.AgentUsername = os.Getenv("AGENT_NAME")
	data.Key = os.Getenv("AGENT_KEY")
	data.Web = os.Getenv("AGENT_WEB_NAME")

	// ** IsDp=true คือ เป็นรายการฝากเงิน และ ใช้สำหรับนับยอดได้เสีย และ เทรินโอเวอร์
	// false คือ เป็นรายการฝากเงินเพื่อคืนเงินเข้ารายการฝากไม่นำไปนับยอดได้เสีย และ เทรินโอเวอร์
	// log.Println("Start time ------> " + time.Now().Format("2006-01-02 15:04:05.000"))
	// log.Println("AmbDepositAgentForOtherAgent req ------> ", helper.StructJson(data))

	// https://api-proxy.a4u.top/ambexapi/ext/deposit/aaaaaaaaaaaaaa/bbbbbbbbbbbb
	url := fmt.Sprintf("%s/ext/deposit/%s/%s", os.Getenv("AGENT_ENDPOINT"), os.Getenv("AGENT_PREFIX"), os.Getenv("AGENT_NAME"))

	// [SYSLOG] INIT
	sysLogId, err := r.CreateAgentLog(model.AgentLogCreateBody{
		Name:   "AmbDepositAgentForOtherAgent",
		Status: "PENDING",
		JsonRequest: helper.StructJson(map[string]interface{}{
			"endpoint": url,
			"body":     data,
		}),
	})
	if err != nil {
		log.Println("AmbDepositAgentForOtherAgent.CreateAgentLog error ------> ", err.Error())
	}

	client := &http.Client{}
	client.Timeout = 3 * time.Second
	jsonBody, _ := json.Marshal(data)
	reqBody := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest("POST", url, reqBody)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("Accept", "application/json")
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println("AmbDepositAgentForOtherAgent.CLIENT_CALL_ERROR", err.Error())
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type": "CLIENT_CALL_ERROR",
				"error_msg":  err.Error(),
			}),
		}); sysLogErr != nil {
			log.Println("AmbDepositAgentForOtherAgent.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, errors.New("CLIENT_CALL_ERROR")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println("AmbDepositAgentForOtherAgent.RESPONSE_READ_ERROR", err)
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type": "RESPONSE_READ_ERROR",
				"error_msg":  err.Error(),
			}),
		}); sysLogErr != nil {
			log.Println("AmbDepositAgentForOtherAgent.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, errors.New("RESPONSE_READ_ERROR")
	}

	if response.StatusCode != 200 {
		log.Println("AmbDepositAgentForOtherAgent.HTTP_NOT_200", response.StatusCode)
		log.Println("AmbDepositAgentForOtherAgent.response_data", string(responseData))
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type":    "INVALID_RESPONSE_CODE",
				"error_msg":     "HTTP_NOT_200",
				"http_code":     response.StatusCode,
				"response_data": string(responseData),
			}),
		}); sysLogErr != nil {
			log.Println("AmbDepositAgentForOtherAgent.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, errors.New("INVALID_RESPONSE_CODE")
	}

	var result model.AmbDepositReponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("AmbDepositAgentForOtherAgent.CANT_PARSE_RESPONSE_DATA ------> ", string(responseData))
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type":    "Unmarshal_ERROR",
				"error_msg":     errJson.Error(),
				"response_data": string(responseData),
			}),
		}); sysLogErr != nil {
			log.Println("AmbDepositAgentForOtherAgent.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, errors.New("CANT_PARSE_RESPONSE_DATA")
	}
	if result.Code != 0 {
		log.Println("AmbDepositAgentForOtherAgent result ------> ", helper.StructJson(result))
		// CANT_DEPOSIT_403 : Permission denied.
		// CANT_DEPOSIT_400 : Data bad request.
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type":     "CANT_DEPOSIT",
				"error_msg":      result.Msg,
				"response_model": helper.StructJson(result),
			}),
		}); sysLogErr != nil {
			log.Println("AmbDepositAgentForOtherAgent.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, fmt.Errorf("CANT_DEPOSIT_%d", result.Code)
	}

	// [SYSLOG]
	if sysLogErr := r.SetAgentLogSuccess(model.AgentLogUpdateBody{
		Id:          *sysLogId,
		JsonReponse: helper.StructJson(result),
	}); sysLogErr != nil {
		log.Println("AmbDepositAgentForOtherAgent.SetAgentLogSuccess error ------> ", sysLogErr.Error())
	}
	return &result, nil
}
