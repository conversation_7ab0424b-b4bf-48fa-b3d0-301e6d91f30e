package repository

import (
	"cybergame-api/model"

	"gorm.io/gorm"
)

func NewCategoryGameSettingRepository(db *gorm.DB) CategoryGameSettingRepository {
	return &repo{db}
}

type CategoryGameSettingRepository interface {
	UpdateCategoryGameSetting(body model.UpdateCategoryGameSettingBody) error
	GetCategoryGameSetting() (*model.GetCategoryGameSettingResponse, error)

	CreateAdminAction(body model.AdminActionCreateBody) (*int64, error)

	//v2
	GetCategoryGameSettingV2() ([]model.GetCategoryGameSettingV2, error)
	SortGetCategoryGameSettingV2(req model.DragSortRequest) error
	WebGetCategoryGameSettingV2() ([]model.GetCategoryGameSettingV2, error)
	UpdateCategoryGameSettingV2(body model.UpdateCategoryGameSettingV2) error
}

func (r *repo) UpdateCategoryGameSetting(body model.UpdateCategoryGameSettingBody) error {

	if err := r.db.Table("category_game_setting").Where("id =?", 1).Updates(body).Error; err != nil {
		return err
	}

	return nil
}

func (r *repo) GetCategoryGameSetting() (*model.GetCategoryGameSettingResponse, error) {

	var result model.GetCategoryGameSettingResponse

	selectedFields := "category_game_setting.is_open_slot as is_open_slot, category_game_setting.is_open_casino as is_open_casino, category_game_setting.is_open_sport as is_open_sport, category_game_setting.is_open_lottery as is_open_lottery, category_game_setting.is_open_p2p as is_open_p2p "
	selectedFields += ", category_game_setting.is_open_external_lotto as is_open_external_lotto"
	selectedFields += ", category_game_setting.is_open_cb_lotto as is_open_cb_lotto"
	if err := r.db.Table("category_game_setting").
		Select(selectedFields).
		Where("category_game_setting.id = ?", 1).
		Take(&result).
		Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			var createBody model.CreateCategoryGameSettingBody
			createBody.IsOpenSlot = 1
			createBody.IsOpenCasino = 1
			createBody.IsOpenSport = 1
			createBody.IsOpenLottery = 1
			createBody.IsOpenP2p = 1
			createBody.IsOpenExternalLotto = 1
			createBody.IsOpenCbLotto = 1
			if err := r.db.Table("category_game_setting").Create(&createBody).Error; err != nil {
				return nil, err
			}
			result.IsOpenSlot = 1
			result.IsOpenCasino = 1
			result.IsOpenSport = 1
			result.IsOpenLottery = 1
			result.IsOpenP2p = 1
			result.IsOpenExternalLotto = 1
			result.IsOpenCbLotto = 1
			return &result, nil
		}
		return nil, err
	}

	return &result, nil
}

func (r *repo) GetCategoryGameSettingV2() ([]model.GetCategoryGameSettingV2, error) {

	var result []model.GetCategoryGameSettingV2

	selectedFields := "id, priority_order, name, label_th, label_en, is_active"
	if err := r.db.Table("category_game_setting_v2").
		Select(selectedFields).
		Where("api_is_allowed = ?", 1).
		Order("priority_order ASC").
		Scan(&result).
		Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, gorm.ErrRecordNotFound
		}
		return nil, err
	}

	return result, nil

}

func (r repo) SortGetCategoryGameSettingV2(req model.DragSortRequest) error {

	tableName := "category_game_setting_v2"

	// ===== NO NEED TO EDIT AFTER THIS LINE ===== //

	var list []model.SortOrder
	selectedFields := "id, priority_order as sort_order"
	query := r.db.Table(tableName)
	query = query.Select(selectedFields)
	if err := query.
		Where("id IN ?", []int64{req.FromItemId, req.ToItemId}).
		Limit(2).
		Find(&list).
		Error; err != nil {
		return err
	}

	var fromItem *model.SortOrder
	var toItem *model.SortOrder
	for _, record := range list {
		if record.Id == req.FromItemId {
			fromItem = &model.SortOrder{
				Id:        record.Id,
				SortOrder: record.SortOrder,
			}
		} else if record.Id == req.ToItemId {
			toItem = &model.SortOrder{
				Id:        record.Id,
				SortOrder: record.SortOrder,
			}
		}
	}

	if fromItem != nil && toItem != nil {
		// Sort Direction //
		if fromItem.SortOrder < toItem.SortOrder {
			// Drag down  //
			whereShiftDown := r.db.Where("priority_order > ?", fromItem.SortOrder).Where("priority_order <= ?", toItem.SortOrder)
			if err := r.db.Table(tableName).Where(whereShiftDown).Update("priority_order", gorm.Expr("priority_order - 1")).Error; err != nil {
				return err
			}
			// Go to
			if err := r.db.Table(tableName).Where("id = ?", fromItem.Id).Update("priority_order", toItem.SortOrder).Error; err != nil {
				return err
			}
		} else if fromItem.SortOrder > toItem.SortOrder {
			// Drag up = shift up //
			whereShiftDown := r.db.Where("priority_order < ?", fromItem.SortOrder).Where("priority_order >= ?", toItem.SortOrder)
			if err := r.db.Table(tableName).Where(whereShiftDown).Update("priority_order", gorm.Expr("priority_order + 1")).Error; err != nil {
				return err
			}
			// Go to
			if err := r.db.Table(tableName).Where("id = ?", fromItem.Id).Update("priority_order", toItem.SortOrder).Error; err != nil {
				return err
			}
		}
	}
	return nil
}

func (r *repo) WebGetCategoryGameSettingV2() ([]model.GetCategoryGameSettingV2, error) {

	var result []model.GetCategoryGameSettingV2

	selectedFields := "id, priority_order, name, label_th, label_en, is_active"
	if err := r.db.Table("category_game_setting_v2").
		Select(selectedFields).
		Where("api_is_allowed = ?", 1).
		Where("is_active = ?", 1).
		Order("priority_order ASC").
		Scan(&result).
		Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, gorm.ErrRecordNotFound
		}
		return nil, err
	}

	return result, nil

}

func (r *repo) UpdateCategoryGameSettingV2(body model.UpdateCategoryGameSettingV2) error {

	if err := r.db.Table("category_game_setting_v2").Where("id = ?", body.Id).Updates(body).Error; err != nil {
		return err
	}

	return nil
}
