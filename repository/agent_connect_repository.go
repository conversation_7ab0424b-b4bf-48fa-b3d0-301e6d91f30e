package repository

import (
	"bytes"
	"cybergame-api/helper"
	"cybergame-api/model"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"math"
	"net/http"
	"os"
	"time"

	"gorm.io/gorm"
)

func NewAgentConnectRepository(db *gorm.DB) AgentConnectRepository {
	return &repo{db}
}

type AgentConnectRepository interface {
	AgcRegister(data model.AgcRegister) error
	AgcLogin(data model.AgcLogin) (*model.AgcLoginResponse, error)
	AgcChangePassword(data model.AgcChangePassword) (*model.AgcChangePasswordResponse, error)
	AgcGetCredit(data model.AgcBalance) (*model.AgcBalanceResponse, error)
	AgcVendorMaintenanceList(data model.AgcVendorMaintenanceList) (*model.AgcVendorMaintenanceListResponse, error)
	// Transaction
	AgcAddCreditForAf(data model.TransactionAgentRequest) (*model.ResponseGameCredit, error)
	AgcDepositAgent(data model.TransactionAgentRequest) (*model.ResponseGameCredit, error)
	AgcWithdrawAgent(data model.TransactionAgentRequest) (*model.ResponseGameCredit, error)
	AgcPlay(token string, data interface{}) (*model.AgcPlay, error)
	// Report
	AgcGetApistatus(req model.ApiStatusRequest) (*model.ApiStatus, error)
	AgcSimpleWinLose(data model.AgcSimpleWinlose) (*model.AgcSimpleWinloseResponse, error)
	InsertAgcApiStatus(path, date string) error
	InsertAgcPlayLog(data []model.AgentPlayLog, path string, page int, success bool) error
	AgcUpdateFailed(id int64, page int) error
	AgcUpdateSuccess(id int64) error
	// Tidtech
	AgcSimpleWinLoseTidtech(data model.AgcSimpleWinlose) (*model.AgcSimpleWinloseResponse, error)
	// MOCK+TEST
	AgcSimpleWinLoseMock(data model.AgcSimpleWinlose) (*model.AgcSimpleWinloseResponse, error)

	// other agent adjustment
	AgcWithdrawAgentForOtherAgent(data model.TransactionAgentRequest) (*model.ResponseGameCredit, error)
	AgcDepositAgentForOtherAgent(data model.TransactionAgentRequest) (*model.ResponseGameCredit, error)
}

func (r repo) AgcRegister(data model.AgcRegister) error {

	// log.Println("AgcRegister req ------> ", helper.StructJson(data))

	url := fmt.Sprintf("%s/credit-auth/xregister", os.Getenv("AUTH_API"))

	// [SYSLOG] INIT
	sysLogId, err := r.CreateAgentLog(model.AgentLogCreateBody{
		Name:   "AgcRegister",
		Status: "PENDING",
		JsonRequest: helper.StructJson(map[string]interface{}{
			"endpoint": url,
			"body":     data,
		}),
	})
	if err != nil {
		log.Println("AgcRegister.CreateAgentLog error ------> ", err.Error())
	}

	result, err := helper.Post(url, data)
	if err != nil {
		log.Println("AgcRegister.CLIENT_CALL_ERROR", err.Error())
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type": "CLIENT_CALL_ERROR",
				"error_msg":  err.Error(),
			}),
		}); sysLogErr != nil {
			log.Println("AgcRegister.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return err
	}

	// log.Println("AgcRegister resp ------> ", helper.StructJson(result))

	if result.(map[string]interface{})["Success"] != true {
		log.Println("AgcRegister CANT_REGISTER", helper.StructJson(result))
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type":     "CANT_REGISTER",
				"error_msg":      "CANT_REGISTER",
				"response_model": helper.StructJson(result),
			}),
		}); sysLogErr != nil {
			log.Println("AgcRegister.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return errors.New(result.(map[string]interface{})["Error"].(map[string]interface{})["Message"].(string))
	}

	// [SYSLOG]
	if sysLogErr := r.SetAgentLogSuccess(model.AgentLogUpdateBody{
		Id:          *sysLogId,
		JsonReponse: helper.StructJson(result),
	}); sysLogErr != nil {
		log.Println("AgcRegister.SetAgentLogSuccess error ------> ", sysLogErr.Error())
	}
	return nil
}

func (r repo) AgcLogin(data model.AgcLogin) (*model.AgcLoginResponse, error) {

	// fmt.Println("AgcLogin req ------> ", helper.StructJson(data))

	url := fmt.Sprintf("%s/credit-auth/login", os.Getenv("AUTH_API"))

	jsonBody, _ := json.Marshal(data)
	reqBody := bytes.NewBuffer(jsonBody)
	call := http.Client{}
	req, err := http.NewRequest("POST", url, reqBody)
	if err != nil {
		log.Println("AgcLogin error NewRequest ------> ", err)
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	resp, err := call.Do(req)
	if err != nil {
		log.Println("AgcLogin error ------> ", helper.StructJson(err))
		return nil, err
	}
	defer resp.Body.Close()

	var res model.AgcLoginResponse
	if err := json.NewDecoder(resp.Body).Decode(&res); err != nil {
		log.Println("AgcLogin error NewDecoder------> ", helper.StructJson(resp.Body))
		return nil, err
	}

	// fmt.Println("AgcLogin resp ------> ", helper.StructJson(res))
	return &res, nil
}

func (r repo) AgcChangePassword(data model.AgcChangePassword) (*model.AgcChangePasswordResponse, error) {

	// log.Println("AgcChangePassword req ------> ", helper.StructJson(data))

	url := fmt.Sprintf("%s/credit-auth/changepassword", os.Getenv("AUTH_API"))

	// [SYSLOG] INIT
	sysLogId, err := r.CreateAgentLog(model.AgentLogCreateBody{
		Name:   "AgcChangePassword",
		Status: "PENDING",
		JsonRequest: helper.StructJson(map[string]interface{}{
			"endpoint": url,
			"body":     data,
		}),
	})
	if err != nil {
		log.Println("AgcChangePassword.CreateAgentLog error ------> ", err.Error())
	}

	resp, err := helper.Post(url, data)
	if err != nil {
		log.Println("AgcChangePassword.CLIENT_CALL_ERROR", err.Error())
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type": "CLIENT_CALL_ERROR",
				"error_msg":  err.Error(),
			}),
		}); sysLogErr != nil {
			log.Println("AgcChangePassword.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, err
	}

	var result model.AgcChangePasswordResponse
	jsonRes, _ := json.Marshal(resp)
	if errJson := json.Unmarshal(jsonRes, &result); errJson != nil {
		log.Println("AgcChangePassword.CANT_PARSE_RESPONSE_DATA ------> ", helper.StructJson(resp))
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type":    "Unmarshal_ERROR",
				"error_msg":     errJson.Error(),
				"response_data": helper.StructJson(resp),
			}),
		}); sysLogErr != nil {
			log.Println("AgcChangePassword.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, errJson
	}

	if !result.Status {
		log.Println("AgcChangePassword CANT_CHANGE_PASSWORD", helper.StructJson(result))
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type":     "CANT_CHANGE_PASSWORD",
				"error_msg":      "CANT_CHANGE_PASSWORD",
				"response_model": helper.StructJson(result),
			}),
		}); sysLogErr != nil {
			log.Println("AgcChangePassword.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		// SAME PASS -12
		if result.ErrorCode != -12 {
			return nil, fmt.Errorf("CANT_CHANGE_PASSWORD_%d", int(math.Abs(float64(result.ErrorCode))))
		}
	}

	// [SYSLOG]
	if sysLogErr := r.SetAgentLogSuccess(model.AgentLogUpdateBody{
		Id:          *sysLogId,
		JsonReponse: helper.StructJson(result),
	}); sysLogErr != nil {
		log.Println("AgcChangePassword.SetAgentLogSuccess error ------> ", sysLogErr.Error())
	}
	return &result, nil
}

func (r repo) AgcAddCreditForAf(data model.TransactionAgentRequest) (*model.ResponseGameCredit, error) {

	// log.Println("AgcAddCreditForAf req ------> ", helper.StructJson(data))

	timeNow := time.Now()
	timeToInt := int(timeNow.Unix())

	agentName := os.Getenv("AGENT_NAME")
	sign := fmt.Sprintf("%s%s", agentName, data.PlayerName)

	agent := model.TransactionAgent{}
	agent.RefId = data.RefId
	agent.AgentName = agentName
	agent.PlayerName = data.PlayerName
	agent.Amount = data.Amount
	agent.Timestamp = timeToInt
	agent.Sign = helper.CreateSign(os.Getenv("AGENT_KEY"), sign, timeNow)

	url := fmt.Sprintf("%s/credit-transfer/deposit", os.Getenv("TRANSFER_API"))

	// [SYSLOG] INIT
	sysLogId, err := r.CreateAgentLog(model.AgentLogCreateBody{
		Name:   "AgcAddCreditForAf",
		Status: "PENDING",
		JsonRequest: helper.StructJson(map[string]interface{}{
			"endpoint": url,
			"body":     agent,
		}),
	})
	if err != nil {
		log.Println("AgcAddCreditForAf.CreateAgentLog error ------> ", err.Error())
	}

	resp, err := helper.Post(url, agent)
	if err != nil {
		log.Println("AgcAddCreditForAf.CLIENT_CALL_ERROR", err.Error())
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type": "CLIENT_CALL_ERROR",
				"error_msg":  err.Error(),
			}),
		}); sysLogErr != nil {
			log.Println("AgcAddCreditForAf.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, err
	}

	var result *model.AgcTransactionResponse
	jsonRes, _ := json.Marshal(resp)
	if errJson := json.Unmarshal(jsonRes, &result); errJson != nil {
		log.Println("AgcAddCreditForAf.CANT_PARSE_RESPONSE_DATA ------> ", helper.StructJson(resp))
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type":    "Unmarshal_ERROR",
				"error_msg":     errJson.Error(),
				"response_data": helper.StructJson(resp),
			}),
		}); sysLogErr != nil {
			log.Println("AgcAddCreditForAf.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, errJson
	}

	if !result.Success {
		log.Println("AgcAddCreditForAf CANT_ADD_AF_CREDIT", helper.StructJson(result))
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type":     "CANT_ADD_AF_CREDIT",
				"error_msg":      "CANT_ADD_AF_CREDIT",
				"response_model": helper.StructJson(result),
			}),
		}); sysLogErr != nil {
			log.Println("AgcAddCreditForAf.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, errors.New(result.Error.Message)
	}

	var resultUpdateDeposit model.ResponseGameCredit
	if result != nil {
		resultUpdateDeposit.Success = result.Success
		resultUpdateDeposit.UserId = data.UserId
		resultUpdateDeposit.BeforeAmount = result.Result.BalanceBefore
		resultUpdateDeposit.AfterAmount = result.Result.BalanceAfter
	}

	// [SYSLOG]
	if sysLogErr := r.SetAgentLogSuccess(model.AgentLogUpdateBody{
		Id:          *sysLogId,
		JsonReponse: helper.StructJson(result),
	}); sysLogErr != nil {
		log.Println("AgcAddCreditForAf.SetAgentLogSuccess error ------> ", sysLogErr.Error())
	}
	return &resultUpdateDeposit, nil
}

func (r repo) AgcDepositAgent(data model.TransactionAgentRequest) (*model.ResponseGameCredit, error) {

	// log.Println("AgcDepositAgent req ------> ", helper.StructJson(data))

	timeNow := time.Now()
	timeToInt := int(timeNow.Unix())

	agentName := os.Getenv("AGENT_NAME")
	sign := fmt.Sprintf("%s%s", agentName, data.PlayerName)

	agent := model.TransactionAgent{}
	agent.RefId = data.RefId
	agent.AgentName = agentName
	agent.PlayerName = data.PlayerName
	agent.Amount = data.Amount
	agent.Timestamp = timeToInt
	agent.Sign = helper.CreateSign(os.Getenv("AGENT_KEY"), sign, timeNow)

	url := fmt.Sprintf("%s/credit-transfer/deposit", os.Getenv("TRANSFER_API"))

	// [SYSLOG] INIT
	sysLogId, err := r.CreateAgentLog(model.AgentLogCreateBody{
		Name:   "AgcDepositAgent",
		Status: "PENDING",
		JsonRequest: helper.StructJson(map[string]interface{}{
			"endpoint": url,
			"body":     agent,
		}),
	})
	if err != nil {
		log.Println("AgcDepositAgent.CreateAgentLog error ------> ", err.Error())
	}

	resp, err := helper.Post(url, agent)
	if err != nil {
		log.Println("AgcDepositAgent.CLIENT_CALL_ERROR", err.Error())
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type": "CLIENT_CALL_ERROR",
				"error_msg":  err.Error(),
			}),
		}); sysLogErr != nil {
			log.Println("AgcDepositAgent.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, err
	}

	var result *model.AgcTransactionResponse
	jsonRes, _ := json.Marshal(resp)
	if errJson := json.Unmarshal(jsonRes, &result); errJson != nil {
		log.Println("AgcDepositAgent.CANT_PARSE_RESPONSE_DATA ------> ", helper.StructJson(resp))
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type":    "Unmarshal_ERROR",
				"error_msg":     errJson.Error(),
				"response_data": helper.StructJson(resp),
			}),
		}); sysLogErr != nil {
			log.Println("AgcDepositAgent.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, errJson
	}

	if !result.Success {
		log.Println("AgcDepositAgent CANT_DEPOSIT", helper.StructJson(result))
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type":     "CANT_DEPOSIT",
				"error_msg":      "CANT_DEPOSIT",
				"response_model": helper.StructJson(result),
			}),
		}); sysLogErr != nil {
			log.Println("AgcDepositAgent.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, errors.New(result.Error.Message)
	}

	var resultUpdateDeposit model.ResponseGameCredit
	if result != nil {
		resultUpdateDeposit.Success = result.Success
		resultUpdateDeposit.UserId = data.UserId
		resultUpdateDeposit.BeforeAmount = result.Result.BalanceBefore
		resultUpdateDeposit.AfterAmount = result.Result.BalanceAfter
	}

	// [SYSLOG]
	if sysLogErr := r.SetAgentLogSuccess(model.AgentLogUpdateBody{
		Id:          *sysLogId,
		JsonReponse: helper.StructJson(result),
	}); sysLogErr != nil {
		log.Println("AgcDepositAgent.SetAgentLogSuccess error ------> ", sysLogErr.Error())
	}
	return &resultUpdateDeposit, nil
}

func (r repo) AgcWithdrawAgent(data model.TransactionAgentRequest) (*model.ResponseGameCredit, error) {

	// log.Println("AgcWithdrawAgent req ------> ", helper.StructJson(data))

	timeNow := time.Now()
	timeToInt := int(timeNow.Unix())

	agentName := os.Getenv("AGENT_NAME")
	sign := fmt.Sprintf("%s%s", agentName, data.PlayerName)

	agent := model.TransactionAgent{}
	agent.RefId = data.RefId
	agent.AgentName = agentName
	agent.PlayerName = data.PlayerName
	agent.Amount = data.Amount
	agent.Timestamp = timeToInt
	agent.Sign = helper.CreateSign(os.Getenv("AGENT_KEY"), sign, timeNow)

	url := fmt.Sprintf("%s/credit-transfer/withdraw", os.Getenv("TRANSFER_API"))

	// [SYSLOG] INIT
	sysLogId, err := r.CreateAgentLog(model.AgentLogCreateBody{
		Name:   "AgcWithdrawAgent",
		Status: "PENDING",
		JsonRequest: helper.StructJson(map[string]interface{}{
			"endpoint": url,
			"body":     agent,
		}),
	})
	if err != nil {
		log.Println("AgcWithdrawAgent.CreateAgentLog error ------> ", err.Error())
	}

	resp, err := helper.Post(url, agent)
	if err != nil {
		log.Println("AgcWithdrawAgent.CLIENT_CALL_ERROR", err.Error())
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type": "CLIENT_CALL_ERROR",
				"error_msg":  err.Error(),
			}),
		}); sysLogErr != nil {
			log.Println("AgcWithdrawAgent.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, err
	}

	var result *model.AgcTransactionResponse
	jsonRes, _ := json.Marshal(resp)
	if errJson := json.Unmarshal(jsonRes, &result); errJson != nil {
		log.Println("AgcWithdrawAgent.CANT_PARSE_RESPONSE_DATA ------> ", helper.StructJson(resp))
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type":    "Unmarshal_ERROR",
				"error_msg":     errJson.Error(),
				"response_data": helper.StructJson(resp),
			}),
		}); sysLogErr != nil {
			log.Println("AgcWithdrawAgent.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, errJson
	}

	if !result.Success {
		log.Println("AgcWithdrawAgent CANT_WITHDRAW", helper.StructJson(result))
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type":     "CANT_WITHDRAW",
				"error_msg":      "CANT_WITHDRAW",
				"response_model": helper.StructJson(result),
			}),
		}); sysLogErr != nil {
			log.Println("AgcWithdrawAgent.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, errors.New(result.Error.Message)
	}
	var resultUpdateWithdraw model.ResponseGameCredit
	if result != nil {
		resultUpdateWithdraw.Success = result.Success
		resultUpdateWithdraw.UserId = data.UserId
		resultUpdateWithdraw.BeforeAmount = result.Result.BalanceBefore
		resultUpdateWithdraw.AfterAmount = result.Result.BalanceAfter
		// updateCredit := model.UserUpdateCredit{}
		// updateCredit.Status = result.Success
		// updateCredit.Credit = result.Result.BalanceAfter
		// updateCredit.OldCredit = result.Result.BalanceBefore

		// resultUpdateWithdraw, err = r.UpdateCredit(userID, trxID, updateCredit, "withdraw")
		// if err != nil {
		// 	return nil, err
		// }
	}

	// [SYSLOG]
	if sysLogErr := r.SetAgentLogSuccess(model.AgentLogUpdateBody{
		Id:          *sysLogId,
		JsonReponse: helper.StructJson(result),
	}); sysLogErr != nil {
		log.Println("AgcWithdrawAgent.SetAgentLogSuccess error ------> ", sysLogErr.Error())
	}
	return &resultUpdateWithdraw, nil
}

func (r repo) AgcPlay(token string, data interface{}) (*model.AgcPlay, error) {

	// fmt.Println("AgcPlay req ------> ", token, helper.StructJson(data))

	url := fmt.Sprintf("%s/play/login", os.Getenv("OPENGAME_API"))

	// client := &http.Client{}
	// client.Timeout = 3 * time.Second

	jsonBody, _ := json.Marshal(data)
	reqBody := bytes.NewBuffer(jsonBody)

	call := http.Client{}
	req, err := http.NewRequest("POST", url, reqBody)
	if err != nil {
		log.Println("AgcPlay error NewRequest ------> ", err)
		return nil, err
	}
	referer := "http://" + os.Getenv("DOMAIN_NAME")
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("Accept", "application/json")
	req.Header.Set("Referer", referer)

	resp, err := call.Do(req)
	if err != nil {
		log.Println("AgcPlay error ------> ", helper.StructJson(err))
		return nil, err
	}
	defer resp.Body.Close()

	// ERROR AgcPlay.data.Error.Code Code: -1012 fail operation == ตอนนี้ทางค่าย Jili ปิดปรับปรุงอยู่ครับ
	// fmt.Println("AgcPlay resp ------> ", helper.StructJson(resp))

	var res model.AgcPlay
	if err := json.NewDecoder(resp.Body).Decode(&res); err != nil {
		log.Println("AgcPlay error NewDecoder------> ", helper.StructJson(resp.Body))
		return nil, err
	}
	return &res, nil
}

func (r repo) AgcGetCredit(data model.AgcBalance) (*model.AgcBalanceResponse, error) {

	// fmt.Println("AgcGetCredit req ------> ", helper.StructJson(data))

	var result *model.AgcBalanceResponse

	url := fmt.Sprintf("%s/credit-transfer/balance", os.Getenv("TRANSFER_API"))
	res, err := helper.Post(url, data)
	if err != nil {
		log.Println("AgcGetCredit error ------> ", helper.StructJson(err))
		return nil, err
	}
	// fmt.Println("AgcGetCredit resp ------> ", helper.StructJson(res))

	jsonRes, _ := json.Marshal(res)
	if err := json.Unmarshal(jsonRes, &result); err != nil {
		log.Println("AgcGetCredit resp Unmarshal error ------> ", helper.StructJson(res))
		return nil, err
	}
	return result, nil
}

func (r repo) AgcSimpleWinLoseTidtech(data model.AgcSimpleWinlose) (*model.AgcSimpleWinloseResponse, error) {

	// log.Println("AgcSimpleWinLoseTidtech req ------> ", helper.StructJson(data))
	// AgcSimpleWinLose
	var result model.AgcSimpleWinloseResponse

	ep := "https://proxy.tidtech.co/2all/transfer/api"
	url := fmt.Sprintf("%s/reports/simplewinlose", ep)
	res, err := helper.Post(url, data)
	if err != nil {
		log.Println("AgcSimpleWinLoseTidtech error ------> ", helper.StructJson(err))
		return nil, err
	}

	jsonRes, _ := json.Marshal(res)
	if err := json.Unmarshal(jsonRes, &result); err != nil {
		log.Println("AgcSimpleWinLoseTidtech resp ------> ", helper.StructJson(res))
		return nil, err
	}

	return &result, nil
}

func (r repo) AgcSimpleWinLose(data model.AgcSimpleWinlose) (*model.AgcSimpleWinloseResponse, error) {

	// log.Println("AgcSimpleWinLose req ------> ", helper.StructJson(data))

	// if os.Getenv("GIN_MODE") == "debug" {
	// 	return r.AgcSimpleWinLoseMock(data)
	// }

	// With Lottery if products id include AGENT_PRODUCT_LOTTERY(3)
	// hasExternalLotteryApi := os.Getenv("LOTTERY_ENDPOINT") != "" && os.Getenv("AGENT_NAME") != ""
	// hasLotteryProduct := false
	// if len(data.Products) > 0 {
	// 	for _, v := range data.Products {
	// 		if v == model.AGENT_PRODUCT_LOTTERY {
	// 			hasLotteryProduct = true
	// 			break
	// 		}
	// 	}
	// }
	// if hasExternalLotteryApi && hasLotteryProduct {
	// 	// Remove AGENT_PRODUCT_LOTTERY from products
	// 	fmt.Println("Remove AGENT_PRODUCT_LOTTERY from products")
	// 	fmt.Println("Before", data.Products)
	// 	products := []int{}
	// 	for _, v := range data.Products {
	// 		if v != model.AGENT_PRODUCT_LOTTERY {
	// 			products = append(products, v)
	// 		}
	// 	}
	// 	data.Products = products
	// 	fmt.Println("After", data.Products)
	// }

	// AgcSimpleWinLose
	var result model.AgcSimpleWinloseResponse

	url := fmt.Sprintf("%s/reports/simplewinlose", os.Getenv("TRANSFER_API"))
	res, err := helper.Post(url, data)
	if err != nil {
		log.Println("AgcSimpleWinLose error ------> ", helper.StructJson(err))
		return nil, err
	}

	jsonRes, _ := json.Marshal(res)
	if err := json.Unmarshal(jsonRes, &result); err != nil {
		log.Println("AgcSimpleWinLose resp ------> ", helper.StructJson(res))
		return nil, err
	}

	// if hasExternalLotteryApi && hasLotteryProduct {
	// 	// Append Lottery data to result
	// 	var lotteryQuery model.LotteryPlaylogRequest
	// 	lotteryQuery.Date = data.StartDate
	// 	lotteryQuery.PageSize = data.PageSize
	// 	lotteryQuery.PageIndex = data.PageIndex
	// 	lotteryData, err := r.LotterySimpleWinLose(lotteryQuery)
	// 	if err != nil {
	// 		log.Println("AgcSimpleWinLose.LotterySimpleWinLose error ------> ", err)
	// 	}
	// 	// Append Lottery data to result
	// 	for _, v := range lotteryData.Records {
	// 		var newRow model.SimpleWinloseRecord
	// 		newRow.UserName = v.MemberCode
	// 		newRow.Payout = v.WinLose
	// 		newRow.TurnOver = v.TurnOver
	// 		newRow.ValidAmount = v.ValidAmount
	// 		result.Result.Records = append(result.Result.Records, newRow)
	// 	}
	// }

	return &result, nil
}

func (r repo) AgcSimpleWinLoseMock(data model.AgcSimpleWinlose) (*model.AgcSimpleWinloseResponse, error) {

	// log.Println("AgcSimpleWinLoseMock req ------> ", helper.StructJson(data))

	// member zta68pk52000911-20
	// memberList := []string{"zta68pk52000911", "zta68pk52000912", "zta68pk52000913", "zta68pk52000914", "zta68pk52000915", "zta68pk52000916", "zta68pk52000917", "zta68pk52000918", "zta68pk52000919", "zta68pk52000920"}
	// productIds := []int{model.AGENT_PRODUCT_SPORT, model.AGENT_PRODUCT_CASINO, model.AGENT_PRODUCT_GAME, model.AGENT_PRODUCT_LOTTERY, model.AGENT_PRODUCT_P2P, model.AGENT_PRODUCT_FINANCIAL}
	var result model.AgcSimpleWinloseResponse

	// AgcSimpleWinLose
	if len(data.Products) > 0 && data.PageIndex == 1 {
		for _, v := range data.Products {
			filename := fmt.Sprintf("json/acg_p%d.json", v)
			jsonByte, err := helper.ReadJsonFile(filename)
			if err != nil {
				log.Println("AgcSimpleWinLoseMock ReadJsonFile ------> ", filename)
				return nil, err
			}
			// fmt.Println("jsonByte", string(jsonByte))
			// jsonRes, _ := json.Marshal(jsonByte)
			if err := json.Unmarshal(jsonByte, &result); err != nil {
				log.Println("AgcSimpleWinLoseMock Unmarshal ------> ", filename)
				return nil, err
			}
		}
	}

	return &result, nil
}

func (r repo) AgcGetApistatus(req model.ApiStatusRequest) (*model.ApiStatus, error) {

	var record model.ApiStatus

	selectedFields := "statuses.id as id, statuses.path as path, statuses.page as page, statuses.is_failed as is_failed, statuses.is_success as is_success"
	selectedFields += ", statuses.statement_date as statement_date, statuses.created_at as created_at, statuses.updated_at as updated_at"
	if err := r.db.Table("api_status as statuses").
		Select(selectedFields).
		Where("statuses.path = ?", req.Path).
		Where("statuses.statement_date = ?", req.StatementDate).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) AgcCountFailApiStatus(statementDate string) (int64, error) {

	var total int64

	selectedFields := "statuses.id"
	if err := r.db.Table("api_status as statuses").
		Select(selectedFields).
		Where("statuses.statement_date = ?", statementDate).
		Where(r.db.Where("statuses.is_failed = ?", 1).Or(r.db.Where("statuses.is_failed = ?", 0).Where("statuses.is_success = ?", 0))).
		Count(&total).
		Error; err != nil {
		return -1, err
	}
	return total, nil
}

func (r repo) AgcResetFailApiStatus(statementDate string) error {

	// update fail without success to pending

	if err := r.db.Table("api_status").Where("is_failed = ?", 1).Where("is_success = ?", 0).Where("statement_date = ?", statementDate).Updates(map[string]interface{}{"is_failed": 0}).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) InsertAgcApiStatus(path, date string) error {

	var id int64

	if err := r.db.Table("api_status").
		Select("id").
		Where("statement_date = ?", date).
		Where("path = ?", path).
		Where("is_success = ?", 0).
		Scan(&id).Error; err != nil {
		return err
	}

	if id < 1 {
		data := map[string]interface{}{}
		data["path"] = path
		data["statement_date"] = date
		if err := r.db.Table("api_status").
			Create(&data).Error; err != nil {
			return err
		}
	}
	return nil
}

func (r repo) InsertAgcPlaylogStatus(jsonProductId, statementDate string) error {

	var id int64

	if err := r.db.Table("play_log_status").Select("id").
		Where("statement_date = ?", statementDate).
		Take(&id).Error; err != nil {
		if err.Error() == "record not found" {
			// CREATE NEW
			data := map[string]interface{}{}
			data["json_product_id"] = jsonProductId
			data["statement_date"] = statementDate
			data["playlog_sport_status"] = "PENDING"
			data["playlog_game_status"] = "PENDING"
			data["playlog_casino_status"] = "PENDING"
			data["playlog_lottery_status"] = "PENDING"
			data["playlog_p2p_status"] = "PENDING"
			data["playlog_financial_status"] = "PENDING"
			data["cut_daily_affiliate_status"] = "PENDING"
			data["cut_daily_alliance_status"] = "PENDING"
			data["promotion_return_loss_status"] = "PENDING"
			if err := r.db.Table("play_log_status").Create(&data).Error; err == nil {
				// SUCCESS
				return nil
			}
		}
		return err
	}

	if id == 0 {
		// CREATE NEW
		data := map[string]interface{}{}
		data["json_product_id"] = jsonProductId
		data["statement_date"] = statementDate
		data["playlog_sport_status"] = "PENDING"
		data["playlog_game_status"] = "PENDING"
		data["playlog_casino_status"] = "PENDING"
		data["playlog_lottery_status"] = "PENDING"
		data["playlog_p2p_status"] = "PENDING"
		data["playlog_financial_status"] = "PENDING"
		data["cut_daily_affiliate_status"] = "PENDING"
		data["cut_daily_alliance_status"] = "PENDING"
		data["promotion_return_loss_status"] = "PENDING"
		if err := r.db.Table("play_log_status").Create(&data).Error; err != nil {
			return err
		}
	}
	return nil
}

func (r repo) MigrateOldAgcPlaylogStatus(jsonProductId, statementDate string) error {

	var id int64

	if err := r.db.Table("play_log_status").Select("id").
		Where("statement_date = ?", statementDate).
		Take(&id).Error; err != nil {
		if err.Error() == "record not found" {
			// CREATE NEW
			data := map[string]interface{}{}
			data["json_product_id"] = jsonProductId
			data["statement_date"] = statementDate
			data["playlog_sport_status"] = "NO_DATA"
			data["playlog_game_status"] = "NO_DATA"
			data["playlog_casino_status"] = "NO_DATA"
			data["playlog_lottery_status"] = "NO_DATA"
			data["playlog_p2p_status"] = "NO_DATA"
			data["playlog_financial_status"] = "NO_DATA"
			data["cut_daily_affiliate_status"] = "NO_DATA"
			data["cut_daily_alliance_status"] = "NO_DATA"
			data["promotion_return_loss_status"] = "NO_DATA"
			if err := r.db.Table("play_log_status").Create(&data).Error; err != nil {
				log.Println("MigrateOldAgcPlaylogStatus error ------> ", err.Error())
				return nil
			}

			// check from
			var apiStatusList []model.ApiStatus
			if err := r.db.Table("api_status").
				Select("id, path, page, is_failed, is_success, statement_date, created_at, updated_at").
				Where("statement_date = ?", statementDate).
				Scan(&apiStatusList).Error; err != nil {
				log.Println("MigrateOldAgcPlaylogStatus error ------> ", err.Error())
				return err
			}

			if len(apiStatusList) > 0 {
				updateBody := map[string]interface{}{}
				updateBody["playlog_sport_status"] = "FAILED"
				updateBody["playlog_casino_status"] = "FAILED"
				updateBody["playlog_game_status"] = "FAILED"
				updateBody["promotion_return_loss_status"] = "FAILED"

				completedCount := 0
				for _, v := range apiStatusList {
					if v.Path == "simplewinlose1" {
						if v.IsSuccess == 1 {
							updateBody["playlog_sport_status"] = "DONE"
							completedCount += 1
						}
					} else if v.Path == "simplewinlose2" {
						if v.IsSuccess == 1 {
							updateBody["playlog_casino_status"] = "DONE"
							completedCount += 1
						}
					} else if v.Path == "simplewinlose4" {
						if v.IsSuccess == 1 {
							updateBody["playlog_game_status"] = "DONE"
							completedCount += 1
						}
					}
				}

				// if playlog all done = done
				if completedCount == 3 {
					updateBody["cut_daily_affiliate_status"] = "DONE"
					updateBody["cut_daily_alliance_status"] = "DONE"
					updateBody["promotion_return_loss_status"] = "DONE"
				}

				if err := r.db.Table("play_log_status").Where("statement_date = ?", statementDate).Updates(updateBody).Error; err != nil {
					return err
				}
			}
		}
		return err
	}

	return nil
}

func (r repo) UpdateAgcPlaylogStatus(productId int, statementDate string, status string) error {

	updateBody := map[string]interface{}{}

	if productId == model.AGENT_PRODUCT_SPORT {
		updateBody["playlog_sport_status"] = status
	} else if productId == model.AGENT_PRODUCT_CASINO {
		updateBody["playlog_casino_status"] = status
	} else if productId == model.AGENT_PRODUCT_GAME {
		updateBody["playlog_game_status"] = status
	} else if productId == model.AGENT_PRODUCT_LOTTERY {
		updateBody["playlog_lottery_status"] = status
	} else if productId == model.AGENT_PRODUCT_P2P {
		updateBody["playlog_p2p_status"] = status
	} else if productId == model.AGENT_PRODUCT_FINANCIAL {
		updateBody["playlog_financial_status"] = status
	} else {
		return errors.New("PRODUCT_ID_NOT_FOUND")
	}

	if err := r.db.Table("play_log_status").Where("statement_date = ?", statementDate).Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdateAgcCronCalcStatus(name string, statementDate string, status string) error {

	updateBody := map[string]interface{}{}

	if name == "AFFILIATE" {
		updateBody["cut_daily_affiliate_status"] = status
	} else if name == "ALLIANCE" {
		updateBody["cut_daily_alliance_status"] = status
	} else if name == "PROMOTION_RETURN_LOSS" {
		updateBody["promotion_return_loss_status"] = status
	} else {
		return errors.New("PRODUCT_ID_NOT_FOUND")
	}

	if err := r.db.Table("play_log_status").Where("statement_date = ?", statementDate).Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) InsertAgcPlayLog(data []model.AgentPlayLog, path string, page int, success bool) error {

	tx := r.db.Begin()

	if err := tx.Table("play_log").
		Create(&data).Error; err != nil {
		tx.Rollback()
		return err
	}

	// obj := map[string]interface{}{}
	// obj["is_failed"] = 0
	// obj["page"] = page

	// if err := tx.Table("api_status").
	// 	Where("path = ?", path).
	// 	Where("is_success = ?", 0).
	// 	Updates(obj).Error; err != nil {
	// 	tx.Rollback()
	// 	return err
	// }

	// if success {
	// 	if err := tx.Table("api_status").
	// 		Where("path = ?", path).
	// 		Where("is_failed = ?", 0).
	// 		Where("is_success = ?", 0).
	// 		Update("is_success", 1).
	// 		Error; err != nil {
	// 		tx.Rollback()
	// 		return err
	// 	}
	// }

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}

	return nil
}

func (r repo) AgcUpdateFailed(id int64, page int) error {

	obj := map[string]interface{}{}
	obj["is_failed"] = 1
	obj["page"] = page

	if err := r.db.Table("api_status").
		Where("id = ?", id).
		Where("is_success = ?", 0).
		Where("is_failed = ?", 0).
		Updates(obj).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) AgcUpdateSuccess(id int64) error {

	obj := map[string]interface{}{}
	obj["is_success"] = 1

	if err := r.db.Table("api_status").
		Where("id = ?", id).
		Where("is_success = ?", 0).
		Where("is_failed = ?", 0).
		Updates(obj).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetRefListByMembers(members []string) ([]model.PlayerUserList, error) {

	var list []model.PlayerUserList
	var users []model.PlayerUserList

	if err := r.db.Table("user").
		Select("id AS user_id, member_code").
		Where("member_code IN ?", members).
		Scan(&users).Error; err != nil {
		return nil, err
	}

	userId := []int64{}
	for _, v := range users {
		userId = append(userId, v.UserID)
	}

	if err := r.db.Table("affiliate").
		Joins("LEFT JOIN user ref ON ref.id = affiliate.ref_id").
		Joins("LEFT JOIN user user ON user.id = affiliate.user_id").
		Select("affiliate.user_id, affiliate.ref_id, user.member_code").
		Where("affiliate.user_id IN ?", userId).
		Scan(&list).Error; err != nil {
		return nil, err
	}

	return list, nil
}

// func (r repo) AgcLogin(data model.AgcLogin) (*model.AgcLoginResponse, error) {

// 	log.Println("AgcLogin req ------> ", helper.StructJson(data))

// 	url := fmt.Sprintf("%s/credit-auth/login", os.Getenv("AUTH_API"))

// 	// client := &http.Client{}
// 	// client.Timeout = 3 * time.Second

// 	jsonBody, _ := json.Marshal(data)
// 	reqBody := bytes.NewBuffer(jsonBody)
// 	call := http.Client{}
// 	req, err := http.NewRequest("POST", url, reqBody)
// 	if err != nil {
// 		return nil, err
// 	}

// 	req.Header.Set("Content-Type", "application/json")
// 	req.Header.Set("Accept", "application/json")

// 	resp, err := call.Do(req)
// 	if err != nil {
// 		log.Println("AgcLogin error ------> ", helper.StructJson(err))
// 		return nil, err
// 	}
// 	defer resp.Body.Close()

// 	var res model.AgcLoginResponse
// 	if err := json.NewDecoder(resp.Body).Decode(&res); err != nil {
// 		log.Println("AgcLogin error NewDecoder------> ", helper.StructJson(resp.Body))
// 		return nil, err
// 	}

// 	log.Println("AgcLogin resp ------> ", helper.StructJson(res))
// 	return &res, nil
// }

func (r repo) AgcVendorMaintenanceList(data model.AgcVendorMaintenanceList) (*model.AgcVendorMaintenanceListResponse, error) {

	url := os.Getenv("AUTH_API") + "/api/maintenance/allvendorstatus"
	fmt.Println("url: ", url)
	requestBody, err := json.Marshal(data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request body: %w", err)
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")

	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// print response
	fmt.Println("AgcVendorMaintenanceList response: ", resp)
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	var loginResponse model.AgcVendorMaintenanceListResponse
	decoder := json.NewDecoder(resp.Body)
	if err := decoder.Decode(&loginResponse); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &loginResponse, nil
}

func (r repo) AgcWithdrawAgentForOtherAgent(data model.TransactionAgentRequest) (*model.ResponseGameCredit, error) {

	// log.Println("Start time AgcWithdrawAgentForOtherAgent ------> " + time.Now().Format("2006-01-02 15:04:05.000"))
	// log.Println("AgcWithdrawAgentForOtherAgent req ------> ", helper.StructJson(data))

	timeNow := time.Now()
	timeToInt := int(timeNow.Unix())

	agentName := os.Getenv("AGENT_NAME")
	sign := fmt.Sprintf("%s%s", agentName, data.PlayerName)

	agent := model.TransactionAgent{}
	agent.RefId = data.RefId
	agent.AgentName = agentName
	agent.PlayerName = data.PlayerName
	agent.Amount = data.Amount
	agent.Timestamp = timeToInt
	agent.Sign = helper.CreateSign(os.Getenv("AGENT_KEY"), sign, timeNow)

	url := fmt.Sprintf("%s/credit-transfer/withdraw", os.Getenv("TRANSFER_API"))

	// [SYSLOG] INIT
	sysLogId, err := r.CreateAgentLog(model.AgentLogCreateBody{
		Name:   "AgcWithdrawAgentForOtherAgent",
		Status: "PENDING",
		JsonRequest: helper.StructJson(map[string]interface{}{
			"endpoint": url,
			"body":     agent,
		}),
	})
	if err != nil {
		log.Println("AgcWithdrawAgentForOtherAgent.CreateAgentLog error ------> ", err.Error())
	}

	resp, err := helper.Post(url, agent)
	if err != nil {
		log.Println("AgcWithdrawAgentForOtherAgent.CLIENT_CALL_ERROR", err.Error())
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type": "CLIENT_CALL_ERROR",
				"error_msg":  err,
			}),
		}); sysLogErr != nil {
			log.Println("AgcWithdrawAgentForOtherAgent.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, err
	}

	var result *model.AgcTransactionResponse
	jsonRes, _ := json.Marshal(resp)
	if errJson := json.Unmarshal(jsonRes, &result); errJson != nil {
		log.Println("AgcWithdrawAgentForOtherAgent.CANT_PARSE_RESPONSE_DATA ------> ", helper.StructJson(resp))
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type":    "Unmarshal_ERROR",
				"error_msg":     errJson.Error(),
				"response_data": helper.StructJson(resp),
			}),
		}); sysLogErr != nil {
			log.Println("AgcWithdrawAgentForOtherAgent.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, errJson
	}

	if !result.Success {
		log.Println("AgcWithdrawAgentForOtherAgent CANT_WITHDRAW", helper.StructJson(result))
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type":     "CANT_WITHDRAW",
				"error_msg":      "CANT_WITHDRAW",
				"response_model": helper.StructJson(result),
			}),
		}); sysLogErr != nil {
			log.Println("AgcWithdrawAgentForOtherAgent.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, errors.New(result.Error.Message)
	}
	var resultUpdateWithdraw model.ResponseGameCredit
	if result != nil {
		resultUpdateWithdraw.Success = result.Success
		resultUpdateWithdraw.UserId = data.UserId
		resultUpdateWithdraw.BeforeAmount = result.Result.BalanceBefore
		resultUpdateWithdraw.AfterAmount = result.Result.BalanceAfter
		// updateCredit := model.UserUpdateCredit{}
		// updateCredit.Status = result.Success
		// updateCredit.Credit = result.Result.BalanceAfter
		// updateCredit.OldCredit = result.Result.BalanceBefore

		// resultUpdateWithdraw, err = r.UpdateCredit(userID, trxID, updateCredit, "withdraw")
		// if err != nil {
		// 	return nil, err
		// }
	}

	// [SYSLOG]
	if sysLogErr := r.SetAgentLogSuccess(model.AgentLogUpdateBody{
		Id:          *sysLogId,
		JsonReponse: helper.StructJson(result),
	}); sysLogErr != nil {
		log.Println("AgcWithdrawAgentForOtherAgent.SetAgentLogSuccess error ------> ", sysLogErr.Error())
	}
	log.Println("END time AgcWithdrawAgentForOtherAgent ------> " + time.Now().Format("2006-01-02 15:04:05.000"))
	return &resultUpdateWithdraw, nil
}

func (r repo) AgcDepositAgentForOtherAgent(data model.TransactionAgentRequest) (*model.ResponseGameCredit, error) {

	// log.Println("Start time AgcDepositAgentForOtherAgent ------> " + time.Now().Format("2006-01-02 15:04:05.000"))
	// log.Println("AgcDepositAgentForOtherAgent req ------> ", helper.StructJson(data))

	timeNow := time.Now()
	timeToInt := int(timeNow.Unix())

	agentName := os.Getenv("AGENT_NAME")
	sign := fmt.Sprintf("%s%s", agentName, data.PlayerName)

	agent := model.TransactionAgent{}
	agent.RefId = data.RefId
	agent.AgentName = agentName
	agent.PlayerName = data.PlayerName
	agent.Amount = data.Amount
	agent.Timestamp = timeToInt
	agent.Sign = helper.CreateSign(os.Getenv("AGENT_KEY"), sign, timeNow)

	url := fmt.Sprintf("%s/credit-transfer/deposit", os.Getenv("TRANSFER_API"))

	// [SYSLOG] INIT
	sysLogId, err := r.CreateAgentLog(model.AgentLogCreateBody{
		Name:   "AgcDepositAgentForOtherAgent",
		Status: "PENDING",
		JsonRequest: helper.StructJson(map[string]interface{}{
			"endpoint": url,
			"body":     agent,
		}),
	})
	if err != nil {
		log.Println("AgcDepositAgentForOtherAgent.CreateAgentLog error ------> ", err.Error())
	}

	resp, err := helper.Post(url, agent)
	if err != nil {
		log.Println("AgcDepositAgentForOtherAgent.CLIENT_CALL_ERROR", err.Error())
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type": "CLIENT_CALL_ERROR",
				"error_msg":  err.Error(),
			}),
		}); sysLogErr != nil {
			log.Println("AgcDepositAgentForOtherAgent.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, err
	}

	var result *model.AgcTransactionResponse
	jsonRes, _ := json.Marshal(resp)
	if errJson := json.Unmarshal(jsonRes, &result); errJson != nil {
		log.Println("AgcDepositAgentForOtherAgent.CANT_PARSE_RESPONSE_DATA ------> ", helper.StructJson(resp))
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type":    "Unmarshal_ERROR",
				"error_msg":     errJson.Error(),
				"response_data": helper.StructJson(resp),
			}),
		}); sysLogErr != nil {
			log.Println("AgcDepositAgentForOtherAgent.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, errJson
	}

	if !result.Success {
		log.Println("AgcDepositAgentForOtherAgent CANT_DEPOSIT", helper.StructJson(result))
		// [SYSLOG]
		if sysLogErr := r.SetAgentLogError(model.AgentLogUpdateBody{
			Id: *sysLogId,
			JsonReponse: helper.StructJson(map[string]interface{}{
				"error_type":     "CANT_DEPOSIT",
				"error_msg":      "CANT_DEPOSIT",
				"response_model": helper.StructJson(result),
			}),
		}); sysLogErr != nil {
			log.Println("AgcDepositAgentForOtherAgent.SetAgentLogError error ------> ", sysLogErr.Error())
		}
		return nil, errors.New(result.Error.Message)
	}

	var resultUpdateDeposit model.ResponseGameCredit
	if result != nil {
		resultUpdateDeposit.Success = result.Success
		resultUpdateDeposit.UserId = data.UserId
		resultUpdateDeposit.BeforeAmount = result.Result.BalanceBefore
		resultUpdateDeposit.AfterAmount = result.Result.BalanceAfter
	}

	// [SYSLOG]
	if sysLogErr := r.SetAgentLogSuccess(model.AgentLogUpdateBody{
		Id:          *sysLogId,
		JsonReponse: helper.StructJson(result),
	}); sysLogErr != nil {
		log.Println("AgcDepositAgentForOtherAgent.SetAgentLogSuccess error ------> ", sysLogErr.Error())
	}
	fmt.Printf("End time AgcDepositAgentForOtherAgent ------> %s\n", time.Now().Format("2006-01-02 15:04:05.000"))
	return &resultUpdateDeposit, nil
}
