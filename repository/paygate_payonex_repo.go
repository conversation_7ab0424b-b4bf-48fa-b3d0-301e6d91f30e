package repository

import (
	"bytes"
	"cybergame-api/helper"
	"cybergame-api/model"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strings"
	"time"

	"gorm.io/gorm"
)

func NewPayonexRepository(db *gorm.DB) PayonexRepository {
	return &repo{db}
}

type PayonexRepository interface {
	GetDb() *gorm.DB
	// MERCHANT
	// GetNOTUSEPaygateMerchantById(id int64) (*model.PaygateMerchantNgResponse, error)
	// GetPaygateMerchantList(req model.PaygateMerchantListRequest) ([]model.PaygateMerchantNgResponse, int64, error)
	// CreatePaygateMerchant(body model.PaygateMerchantCreateBody) (*int64, error)
	// UpdatePaygateMerchant(id int64, body model.PaygateMerchantUpdateBody) error
	// DeletePaygateMerchant(id int64) error
	// // SETTING
	// GetPaygateSetting() (*model.PaygateSettingResponse, error)
	// CreatePaygateSetting(body model.PaygateSettingCreateBody) (*int64, error)
	// UpdatePaygateSetting(id int64, body model.PaygateSettingUpdateBody) error
	// DeletePaygateSetting(id int64) error
	// // AdminLog
	// GetPaygateAdminLogById(id int64) (*model.PaygateAdminLogResponse, error)
	// GetPaygateAdminLogList(req model.PaygateAdminLogListRequest) ([]model.PaygateAdminLogResponse, int64, error)
	// CreatePaygateAdminLog(body model.PaygateAdminLogCreateBody) (*int64, error)
	// UpdatePaygateAdminLog(id int64, body model.PaygateAdminLogUpdateBody) error
	// // SystemLog
	CreatePaygateSystemLog(body model.PaygateSystemLogCreateBody) (*int64, error)
	// UpdatePaygateSystemLog(id int64, body model.PaygateSystemLogUpdateBody) error
	GetPaygateAccountByProviderId(id int64) (*model.PaygateAccountResponse, error)
	// Payonex-REMOTE
	PayonexGetToken(setting model.PaygateAccountResponse) (*model.PayonexTokenCreateRemoteResponse, error)
	PayonexDeposit(token string, setting model.PaygateAccountResponse, req model.PayonexDepositCreateRemoteRequest) (*model.PayonexDepositCreateRemoteResponse, error)
	PayonexWithdraw(token string, setting model.PaygateAccountResponse, req model.PayonexWithdrawCreateRemoteRequest) (*model.PayonexWithdrawCreateRemoteResponse, error)
	// PayonexCheckBalance(setting model.PaygateAccountResponse) (*model.PayonexCheckBalanceRemoteResponse, error)
	// PayonexGetOrder(setting model.PaygateAccountResponse, orderNo string) (*model.PayonexGetOrderRemoteResponse, error)
	PayonexCreateCustomer(token string, setting model.PaygateAccountResponse, req model.PayonexCustomerCreateRemoteRequest) (*model.PayonexCustomerCreateRemoteResponse, error)
	PayonexUpdateCustomer(token string, setting model.PaygateAccountResponse, req model.PayonexCustomerUpdateRemoteRequest) (*model.PayonexCustomerUpdateRemoteResponse, error)
	// Payonex-DB
	CreatePayonexWebhook(body model.PayonexWebhookCreateBody) (*int64, error)
	GetDbPayonexOrderList(req model.PayonexOrderListRequest) ([]model.PayonexOrderResponse, int64, error)
	GetDbPayonexOrderById(id int64) (*model.PayonexOrderResponse, error)
	GetDbPayonexOrderByRefId(refId int64) (*model.PayonexOrderResponse, error)
	CheckPayonexDepositOrderInLast5Minutes(userId int64, amount float64) (*model.PayonexOrderResponse, error)
	CreateDbPayonexOrder(body model.PayonexOrderCreateBody) (*int64, error)
	UpdateDbPayonexOrderError(id int64, remark string) error
	UpdateDbPayonexOrder(id int64, body model.PayonexOrderUpdateBody) error
	ApproveDbPayonexOrder(id int64, webhookStatus string) error
	UpdatePayonexOrderBankSucess(id int64, transId int64, actionBy int64) error
	// Token
	GetDbPayonexAccessToken() (*model.PayonexToken, error)
	CreateDbPayonexAccessToken(body model.PayonexTokenCreateBody) (*int64, error)
	// Customer
	GetPayonexCustomerById(id int64) (*model.PayonexCustomerResponse, error)
	GetPayonexCustomerByUserId(userId int64) (*model.PayonexCustomerResponse, error)
	// CheckPayonexCustomerByUserId(user model.UserBankDetailBody) (*model.PayonexCustomerResponse, error)
	GetPayonexCustomerList(req model.PayonexCustomerListRequest) ([]model.PayonexCustomerResponse, int64, error)
	CreatePayonexCustomer(body model.PayonexCustomerCreateBody) (*int64, error)
	UpdatePayonexCustomer(id int64, body model.PayonexCustomerUpdateBody) error
	DeletePayonexCustomer(id int64) error
	// PAYONEX-REPORT
	GetRawPayonexPendingDepositOrderById(id int64) (*model.PayonexOrderResponse, error)
	// REF-USER
	GetUserById(id int64) (*model.UserResponse, error)
	// REF-MemberCode
	GetUserForGenMember(id int64) (*model.UserDetail, error)
	GetAgentInfo() (*model.AgentInfo, error)
	IncrementTotal() error
	UpdateMemberCode(userId int64, data model.UserUpdateMemberAndRef) error
	AgcRegister(data model.AgcRegister) error
	AmbRegister(data model.AmbRegister) error
	GetMemberById(id int64) (*model.Member, error)
	IsFirstDeposit(userId int64) bool
	UpdateGenMemberCodeToAffilate(userId int64) error
	CreateAffiliateMember(refBy int64, userId int64) error
	// CronjobDeletePayonexWebhook() error
	// REF-RC
	GetRaceActionIdByActionKey(actionKey string) (*int64, error)
	CreateRaceCondition(body model.RaceActionCreateBody) (int64, error)
	// REF-BankTransaction-Deposit
	GetUserBankDetailById(id int64) (*model.UserBankDetailBody, error)
	GetDepositCurrentProcessingUserPromotion(userId int64) (*model.PromotionWebUserByUserIdResponse, error)
	InsertBankTransaction(data model.BankTransactionCreateBody) (*int64, error)
	IncreaseUserCredit(body model.UserTransactionCreateRequest) (*model.UserTransactionCreateResponse, error)
	UpdateDepositTransactionStatusFromAgent(transId int64, body model.UserTransactionCreateResponse) error
	UpdateAutoProcessTimer(timer string, id int64) error
	// ConfirmDeposit
	GetBankTransactionById(id int64) (*model.BankTransaction, error)
	CreateTransactionAction(data model.CreateBankTransactionActionBody) (*int64, error)
	ConfirmPendingDepositTransaction(id int64, body model.BankDepositTransactionConfirmBody) error
	RollbackTransactionAction(actionId int64) error
	// REF-BANK
	GetLastestBankAdminManualDepositTransaction(userId int64) (*model.BankTransaction, error)
	ShowUserTransaction(req model.UserTransactionShowUpdate) error
	UpdateTransactionStatusTransferingToSuccess(id int64, secondUsed string) error
	RollbackTransactionStatusTransferingToConfirmed(id int64) error
	CreateBankTransactionExternalDetail(body model.BankTransactionExternalDetailCreateRequest) (int64, error)
	UpdateUserTransactionStatus(body model.UpdateUserTransactionStatusRequest) error
	GetUserWithdrawCreditTransactionByRefId(transactionId int64, creditAmount float64) (*model.UserTransaction, error)
	// REF-SysLog
	CreateSystemLog(body model.SystemLogCreateBody) (*int64, error)
	// REF-WebConfiguration
	GetWebConfiguration() (*model.GetWebConfigurationBody, error)

	// [TIER]
	IncreaseUserTierDepositAmount(userId int64, amount float64) error
}

func (r repo) PayonexGetToken(setting model.PaygateAccountResponse) (*model.PayonexTokenCreateRemoteResponse, error) {

	if setting.AccessKey == "" || setting.SecretKey == "" {
		return nil, errors.New("INVALID_SETTING")
	}

	// {
	// 	"accessKey": "aa-bb-cc-dd-ee",
	// 	"secretKey": "aa-bb-cc-dd-ee"
	// }
	var req model.PayonexTokenCreateRemoteRequest
	req.AccessKey = setting.AccessKey
	req.SecretKey = setting.SecretKey
	// fmt.Println("GetPayonexToken req ------> ", req)

	apiEndPoint := setting.ApiEndPoint // https://api-proxy.cbgame88.com/payonex

	// POST Endpoint: https://api-proxy.cbgame88.com/payonex/authenticate
	epUrl := fmt.Sprintf("%s/authenticate", apiEndPoint)
	// fmt.Println("GetPayonexToken url ------> ", epUrl)

	// SysLog
	if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
		Name:   "CreateGetPayonexToken.GetPayonexToken",
		Status: "CALL",
		JsonReq: helper.StructJson(map[string]interface{}{
			"epUrl":   epUrl,
			"setting": setting,
			"req":     req,
		}),
	}); err != nil {
		log.Println("CreatePaygateSystemLog.CreateSysLog", err)
	}

	client := &http.Client{}
	client.Timeout = 20 * time.Second

	jsonBody, _ := json.Marshal(req)
	payload := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest("POST", epUrl, payload)
	reqExternal.Header.Set("Content-Type", "application/json")
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}
	if response.StatusCode != 200 {
		log.Println("GetPayonexToken HTTP.req NOT 200 ------> ", response.StatusCode, string(responseData))
		var errMsg3 model.PayonexErrorStringRemoteResponse
		if errJson3 := json.Unmarshal(responseData, &errMsg3); errJson3 == nil {
			errMsg := errMsg3.Message
			if errMsg == "" {
				errMsg = errMsg3.Data
			}
			return nil, errors.New(errMsg)
		} else {
			log.Println("Unmarshal.errJson3 ------> ", errJson3)
		}
		var errMsg2 model.PayonexErrorRemoteResponse
		if errJson2 := json.Unmarshal(responseData, &errMsg2); errJson2 == nil {
			errMsg := errMsg2.Message
			if errMsg == "" {
				errMsg = errMsg2.Data
			}
			return nil, errors.New(errMsg)
		} else {
			log.Println("Unmarshal.errJson2 ------> ", errJson2)
		}
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	}

	var result model.PayonexTokenCreateRemoteResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("GetPayonexToken resp.Body ------> ", string(responseData))
		log.Println("Unmarshal.Err ------> ", errJson)
		var errMsg1 model.PayonexErrorRemoteResponse
		errJson2 := json.Unmarshal(responseData, &errMsg1)
		if errJson2 != nil {
			log.Println("GetPayonexToken resp.Body ------> ", string(responseData))
			log.Println("Unmarshal.Err ------> ", errJson)
		} else {
			return nil, errors.New(errMsg1.Message)
		}
		return nil, errors.New("INVALID_UNMARSHAL_JSON")
	}

	return &result, nil
}

func (r repo) PayonexCreateCustomer(token string, setting model.PaygateAccountResponse, req model.PayonexCustomerCreateRemoteRequest) (*model.PayonexCustomerCreateRemoteResponse, error) {

	// {
	// 	"name" : "CUSTOMERNAME",
	// 	"bankCode": "KBANK",
	// 	"accountNo": "****************"
	// }
	// log.Println("PayonexCreateCustomer req ------> ")
	if token == "" {
		log.Println("PayonexCreateCustomer.EMPY_TOKEN.ERROR")
		return nil, errors.New("INVALID_TOKEN")
	}

	apiEndPoint := setting.ApiEndPoint // https://api-proxy.cbgame88.com/payonex

	// POST Endpoint: https://api-proxy.cbgame88.com/payonex/v2/customers
	epUrl := fmt.Sprintf("%s/v2/customers", apiEndPoint)
	log.Println("PayonexCreateCustomer url ------> ", epUrl)

	// SysLog
	if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
		Name:   "CreatePayonexCreateCustomer.PayonexCreateCustomer",
		Status: "CALL",
		JsonReq: helper.StructJson(map[string]interface{}{
			"epUrl":   epUrl,
			"setting": setting,
			"req":     req,
		}),
	}); err != nil {
		log.Println("CreatePaygateSystemLog.CreateSysLog", err)
	}

	client := &http.Client{}
	client.Timeout = 20 * time.Second

	jsonBody, _ := json.Marshal(req)
	payload := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest("POST", epUrl, payload)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("Authorization", token)
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}
	if response.StatusCode != 200 {
		log.Println(response.StatusCode)
		log.Println(string(responseData))
		var errMsg2 model.PayonexErrorRemoteResponse
		errJson2 := json.Unmarshal(responseData, &errMsg2)
		if errJson2 == nil {
			return nil, errors.New(errMsg2.Message)
		} else {
			log.Println("Unmarshal.errJson2 ------> ", errJson2)
		}
		var errMsg3 model.PayonexErrorStringRemoteResponse
		errJson3 := json.Unmarshal(responseData, &errMsg3)
		if errJson3 == nil {
			return nil, errors.New(errMsg3.Message)
		} else {
			log.Println("Unmarshal.errJson3 ------> ", errJson3)
		}
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	}

	var result model.PayonexCustomerCreateRemoteResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("PayonexCreateCustomer resp.Body ------> ", string(responseData))
		log.Println("Unmarshal.Err ------> ", errJson)
		var errMsg1 model.PayonexErrorRemoteResponse
		errJson2 := json.Unmarshal(responseData, &errMsg1)
		if errJson2 != nil {
			log.Println("PayonexCreateCustomer resp.Body ------> ", string(responseData))
			log.Println("Unmarshal.Err ------> ", errJson)
		} else {
			return nil, errors.New(errMsg1.Message)
		}
		return nil, errors.New("INVALID_UNMARSHAL_JSON")
	}

	return &result, nil
}

func (r repo) PayonexUpdateCustomer(token string, setting model.PaygateAccountResponse, req model.PayonexCustomerUpdateRemoteRequest) (*model.PayonexCustomerUpdateRemoteResponse, error) {

	// {
	// 	"name" : "CUSTOMERNAME",
	// 	"bankCode": "KBANK",
	// 	"accountNo": "****************"
	// }
	// log.Println("PayonexUpdateCustomer req ------> ")
	if token == "" {
		log.Println("PayonexUpdateCustomer.ERROR.EMPTY_TOKEN")
		return nil, errors.New("INVALID_TOKEN")
	}

	apiEndPoint := setting.ApiEndPoint // https://api-proxy.cbgame88.com/payonex
	// PUT Endpoint: https://api-proxy.cbgame88.com/payonex/v2/customers/aaeb-1111-2222-3333-2c7e2
	epUrl := fmt.Sprintf("%s/v2/customers/%s", apiEndPoint, req.CustomerUuid)
	log.Println("PayonexUpdateCustomer url ------> ", epUrl)

	// SysLog
	if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
		Name:   "CreatePayonexUpdateCustomer.PayonexUpdateCustomer",
		Status: "CALL",
		JsonReq: helper.StructJson(map[string]interface{}{
			"epUrl":   epUrl,
			"setting": setting,
			"req":     req,
		}),
	}); err != nil {
		log.Println("CreatePaygateSystemLog.CreateSysLog", err)
	}

	client := &http.Client{}
	client.Timeout = 20 * time.Second

	jsonBody, _ := json.Marshal(map[string]interface{}{
		"name":      req.Name,
		"bankCode":  req.BankCode,
		"accountNo": req.AccountNo,
	})
	payload := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest("PUT", epUrl, payload)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("Authorization", token)
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}
	if response.StatusCode != 200 {
		log.Println(response.StatusCode)
		log.Println(string(responseData))
		var errMsg2 model.PayonexErrorRemoteResponse
		errJson2 := json.Unmarshal(responseData, &errMsg2)
		if errJson2 == nil {
			return nil, errors.New(errMsg2.Message)
		} else {
			log.Println("Unmarshal.errJson2 ------> ", errJson2)
		}
		var errMsg3 model.PayonexErrorStringRemoteResponse
		errJson3 := json.Unmarshal(responseData, &errMsg3)
		if errJson3 == nil {
			return nil, errors.New(errMsg3.Message)
		} else {
			log.Println("Unmarshal.errJson3 ------> ", errJson3)
		}
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	}

	var result model.PayonexCustomerUpdateRemoteResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("PayonexUpdateCustomer resp.Body ------> ", string(responseData))
		log.Println("Unmarshal.Err ------> ", errJson)
		var errMsg1 model.PayonexErrorRemoteResponse
		errJson2 := json.Unmarshal(responseData, &errMsg1)
		if errJson2 != nil {
			log.Println("PayonexUpdateCustomer resp.Body ------> ", string(responseData))
			log.Println("Unmarshal.Err ------> ", errJson)
		} else {
			return nil, errors.New(errMsg1.Message)
		}
		return nil, errors.New("INVALID_UNMARSHAL_JSON")
	}

	return &result, nil
}

func (r repo) PayonexDeposit(token string, setting model.PaygateAccountResponse, req model.PayonexDepositCreateRemoteRequest) (*model.PayonexDepositCreateRemoteResponse, error) {

	// {
	// 	"customerUuid": "5d53abc5-eb1a-4cbe-b2aa-655593083f3b",
	// 	"amount": "21.45",
	// 	"referenceId": "REF000001",
	// 	"note": "test API",
	// 	"remark": "test API"
	// }

	// ผมอยากเก็บ default เอาไว้กัน Adminตั้งค่าต่ำกว่า payment
	if req.Amount < 20 || req.Amount > 200000 {
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}
	if req.Amount < setting.PaymentDepositMinimum || req.Amount > setting.PaymentDepositMaximum {
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}
	if token == "" {
		return nil, errors.New("INVALID_TOKEN")
	}

	apiEndPoint := setting.ApiEndPoint // https://api-proxy.cbgame88.com/payonex

	// POST Endpoint: https://api-proxy.cbgame88.com/payonex/transactions/deposit/request
	epUrl := fmt.Sprintf("%s/transactions/deposit/request", apiEndPoint)
	log.Println("PayonexDeposit url ------> ", epUrl)

	// SysLog
	if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
		Name:   "PayonexDeposit",
		Status: "CALL",
		JsonReq: helper.StructJson(map[string]interface{}{
			"epUrl":   epUrl,
			"setting": setting,
			"req":     req,
		}),
	}); err != nil {
		log.Println("CreatePaygateSystemLog.CreateSysLog", err)
	}

	client := &http.Client{}
	client.Timeout = 20 * time.Second

	jsonBody, _ := json.Marshal(req)
	payload := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest("POST", epUrl, payload)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("Authorization", token)
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}
	if response.StatusCode != 200 {
		log.Println(response.StatusCode)
		log.Println(string(responseData))
		var errMsg2 model.PayonexErrorRemoteResponse
		errJson2 := json.Unmarshal(responseData, &errMsg2)
		if errJson2 == nil {
			return nil, errors.New(errMsg2.Message)
		} else {
			log.Println("Unmarshal.errJson2 ------> ", errJson2)
		}
		var errMsg3 model.PayonexErrorStringRemoteResponse
		errJson3 := json.Unmarshal(responseData, &errMsg3)
		if errJson3 == nil {
			return nil, errors.New(errMsg3.Message)
		} else {
			log.Println("Unmarshal.errJson3 ------> ", errJson3)
		}
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	}

	var result model.PayonexDepositCreateRemoteResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("PayonexDeposit resp.Body ------> ", string(responseData))
		log.Println("Unmarshal.Err ------> ", errJson)
		var errMsg1 model.PayonexErrorRemoteResponse
		errJson2 := json.Unmarshal(responseData, &errMsg1)
		if errJson2 != nil {
			log.Println("PayonexDeposit resp.Body ------> ", string(responseData))
			log.Println("Unmarshal.Err ------> ", errJson)
		} else {
			return nil, errors.New(errMsg1.Message)
		}
		return nil, errors.New("INVALID_UNMARSHAL_JSON")
	}

	return &result, nil
}

func (r repo) PayonexWithdraw(token string, setting model.PaygateAccountResponse, req model.PayonexWithdrawCreateRemoteRequest) (*model.PayonexWithdrawCreateRemoteResponse, error) {

	// {
	// 	"customerUuid": "5d53abc5-eb1a-4cbe-b2aa-655593083f3b",
	// 	"amount": "21.45",
	// 	"referenceId": "REF000001",
	// 	"note": "test API",
	// 	"remark": "test API"
	// }

	// log.Println("PayonexDeposit req ------> ")
	// ผมอยากเก็บ default เอาไว้กัน Adminตั้งค่าต่ำกว่า payment
	if req.Amount < 20 || req.Amount > 200000 {
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}
	if req.Amount < setting.PaymentWithdrawMinimum || req.Amount > setting.PaymentWithdrawMaximum {
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}
	if token == "" {
		log.Println("PayonexWithdraw.ERROR.EMPTY_TOKEN")
		return nil, errors.New("INVALID_TOKEN")
	}

	apiEndPoint := setting.ApiEndPoint // https://api-proxy.cbgame88.com/payonex

	// POST Endpoint: https://api-proxy.cbgame88.com/payonex/transactions/withdraw/request
	epUrl := fmt.Sprintf("%s/transactions/withdraw/request", apiEndPoint)
	log.Println("PayonexWithdraw url ------> ", epUrl)

	// SysLog
	if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
		Name:   "PayonexWithdraw",
		Status: "CALL",
		JsonReq: helper.StructJson(map[string]interface{}{
			"epUrl":   epUrl,
			"setting": setting,
			"req":     req,
		}),
	}); err != nil {
		log.Println("CreatePaygateSystemLog.CreateSysLog", err)
	}

	client := &http.Client{}
	client.Timeout = 20 * time.Second

	jsonBody, _ := json.Marshal(req)
	payload := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest("POST", epUrl, payload)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("Authorization", token)
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}
	if response.StatusCode != 200 {
		log.Println(response.StatusCode)
		log.Println(string(responseData))
		var errMsg2 model.PayonexErrorRemoteResponse
		errJson2 := json.Unmarshal(responseData, &errMsg2)
		if errJson2 == nil {
			return nil, errors.New(errMsg2.Message)
		} else {
			log.Println("Unmarshal.errJson2 ------> ", errJson2)
		}
		var errMsg3 model.PayonexErrorStringRemoteResponse
		errJson3 := json.Unmarshal(responseData, &errMsg3)
		if errJson3 == nil {
			return nil, errors.New(errMsg3.Message)
		} else {
			log.Println("Unmarshal.errJson3 ------> ", errJson3)
		}
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	}

	// {
	// 	"success": false,
	// 	"message": "Withdraw+fee balance not enough",
	// 	"code": "40813"
	// }

	var result model.PayonexWithdrawCreateRemoteResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("PayonexWithdraw resp.Body ------> ", string(responseData))
		log.Println("Unmarshal.Err ------> ", errJson)
		return nil, errJson
	}

	return &result, nil
}

func (r repo) CreatePayonexWebhook(body model.PayonexWebhookCreateBody) (*int64, error) {

	if err := r.db.Table("paygate_payonex_webhook").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) GetDbPayonexOrderList(req model.PayonexOrderListRequest) ([]model.PayonexOrderResponse, int64, error) {

	var list []model.PayonexOrderResponse
	var total int64

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("paygate_payonex_order as tb_order")
	count = count.Select("tb_order.id")
	if req.UserId != nil {
		count = count.Where("tb_order.user_id = ?", req.UserId)
	}
	if req.OrderTypeId != nil {
		count = count.Where("tb_order.order_type_id = ?", req.OrderTypeId)
	}
	if req.OrderNo != "" {
		count = count.Where("tb_order.order_no = ?", req.OrderNo)
	}
	if req.TransactionNo != "" {
		count = count.Where("tb_order.transaction_no = ?", req.TransactionNo)
	}
	if req.Amount != nil {
		// +- 5.00
		minAmount := *req.Amount - float64(5)
		maxAmount := *req.Amount + float64(5)
		count = count.Where("tb_order.amount BETWEEN ? AND ?", minAmount, maxAmount)
	}
	if req.Status != "" {
		count = count.Where("tb_order.transaction_status = ?", req.Status)
	}

	if err := count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount, tb_order.transfer_amount AS transfer_amount"
		selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
		selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
		selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

		query := r.db.Table("paygate_payonex_order as tb_order")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN paygate_payonex_order_type AS tb_type ON tb_order.order_type_id = tb_type.id")
		if req.UserId != nil {
			query = query.Where("tb_order.user_id = ?", req.UserId)
		}
		if req.OrderTypeId != nil {
			query = query.Where("tb_order.order_type_id = ?", req.OrderTypeId)
		}
		if req.OrderNo != "" {
			query = query.Where("tb_order.order_no = ?", req.OrderNo)
		}
		if req.TransactionNo != "" {
			query = query.Where("tb_order.transaction_no = ?", req.TransactionNo)
		}
		if req.Amount != nil {
			// +- 5.00
			minAmount := *req.Amount - float64(5)
			maxAmount := *req.Amount + float64(5)
			query = query.Where("tb_order.amount BETWEEN ? AND ?", minAmount, maxAmount)
		}
		if req.Status != "" {
			query = query.Where("tb_order.transaction_status = ?", req.Status)
		}

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetDbPayonexOrderById(id int64) (*model.PayonexOrderResponse, error) {

	var record model.PayonexOrderResponse

	selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount, tb_order.transfer_amount AS transfer_amount"
	selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
	selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
	selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

	if err := r.db.Table("paygate_payonex_order as tb_order").
		Select(selectedFields).
		Joins("LEFT JOIN paygate_payonex_order_type AS tb_type ON tb_order.order_type_id = tb_type.id").
		Where("tb_order.id = ?", id).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetDbPayonexOrderByRefId(refId int64) (*model.PayonexOrderResponse, error) {

	var record model.PayonexOrderResponse

	selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount, tb_order.transfer_amount AS transfer_amount"
	selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
	selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
	selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

	if err := r.db.Table("paygate_payonex_order as tb_order").
		Select(selectedFields).
		Joins("LEFT JOIN paygate_payonex_order_type AS tb_type ON tb_order.order_type_id = tb_type.id").
		Where("tb_order.ref_id = ?", refId).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) CheckPayonexDepositOrderInLast5Minutes(userId int64, amount float64) (*model.PayonexOrderResponse, error) {

	var record model.PayonexOrderResponse

	selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount, tb_order.transfer_amount AS transfer_amount"
	selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
	selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
	selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

	query := r.db.Table("paygate_payonex_order as tb_order")
	query = query.Select(selectedFields)
	query = query.Joins("LEFT JOIN paygate_order_type AS tb_type ON tb_order.order_type_id = tb_type.id")
	query = query.Where("tb_order.user_id = ?", userId)
	query = query.Where("tb_order.amount = ?", amount)
	if err := query.
		Where("tb_order.transaction_status = ?", "WAIT_PAYMENT").
		Order("tb_order.id DESC").
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) CreateDbPayonexOrder(body model.PayonexOrderCreateBody) (*int64, error) {

	if err := r.db.Table("paygate_payonex_order").Create(&body).Error; err != nil {
		return nil, err
	}

	// Update order_no if Empty in HPG{YYYYMMDD}{ID} //
	// orderNo := fmt.Sprintf("HPG%v%v", time.Now().Format("200601"), body.Id)
	// [20240209] Update order_no if Empty in {AGENT_NAME}{YYMM}{ID} //
	agentName := os.Getenv("PAYGATE_ORDER_PREFIX")
	if ginMode := os.Getenv("GIN_MODE"); ginMode == "debug" {
		agentName = "P4D" // DEVELOPMENT
	}
	if agentName == "" {
		agentName = "P4G" // ** MIN_LEN=10
	} else {
		agentName = strings.ToUpper(agentName)
	}
	orderNo := fmt.Sprintf("%v%v%v", agentName, time.Now().Format("0601"), body.Id)
	if body.OrderNo == "" {
		updateBody := make(map[string]interface{})
		updateBody["order_no"] = orderNo
		if err := r.db.Table("paygate_payonex_order").Where("id = ?", body.Id).Updates(updateBody).Error; err != nil {
			return nil, err
		}
	}
	return &body.Id, nil
}

func (r repo) UpdateDbPayonexOrderError(id int64, remark string) error {

	updateBody := map[string]interface{}{
		"transaction_status": "ERROR",
		"remark":             remark,
	}
	sql := r.db.Table("paygate_payonex_order").Where("id = ?", id).Where("transaction_status = ?", "PENDING")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdateDbPayonexOrder(id int64, body model.PayonexOrderUpdateBody) error {

	if err := r.db.Table("paygate_payonex_order").Where("id = ?", id).Updates(body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) ApproveDbPayonexOrder(id int64, webhookStatus string) error {

	updateBody := make(map[string]interface{})
	updateBody["transaction_status"] = webhookStatus
	updateBody["payment_at"] = time.Now()

	sql := r.db.Table("paygate_payonex_order").Where("id = ?", id).Where("transaction_status = ?", "WAIT_PAYMENT")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdatePayonexOrderBankSucess(id int64, transId int64, actionBy int64) error {

	updateBody := map[string]interface{}{
		"bank_transaction_id":     transId,
		"bank_transaction_status": "SUCCESS",
		"action_by":               actionBy,
	}

	sql := r.db.Table("paygate_payonex_order").Where("id = ?", id).Where("bank_transaction_status = ?", "PENDING")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetPayonexCustomerById(id int64) (*model.PayonexCustomerResponse, error) {

	var record model.PayonexCustomerResponse

	selectedFields := "*"
	if err := r.db.Table("paygate_payonex_customer as tb_customer").
		Select(selectedFields).
		Where("tb_customer.id = ?", id).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetPayonexCustomerByUserId(userId int64) (*model.PayonexCustomerResponse, error) {

	var record model.PayonexCustomerResponse

	selectedFields := "*"
	if err := r.db.Table("paygate_payonex_customer as tb_customer").
		Select(selectedFields).
		Where("tb_customer.user_id = ?", userId).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetPayonexCustomerList(req model.PayonexCustomerListRequest) ([]model.PayonexCustomerResponse, int64, error) {

	var list []model.PayonexCustomerResponse
	var total int64

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("paygate_payonex_customer as tb_customer")
	count = count.Select("tb_customer.id")

	if err := count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "*"
		query := r.db.Table("paygate_payonex_customer as tb_customer")
		query = query.Select(selectedFields)

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}

	return list, total, nil
}

func (r repo) CreatePayonexCustomer(body model.PayonexCustomerCreateBody) (*int64, error) {

	if err := r.db.Table("paygate_payonex_customer").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) UpdatePayonexCustomer(id int64, body model.PayonexCustomerUpdateBody) error {

	if err := r.db.Table("paygate_payonex_customer").Where("id = ?", id).Updates(body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) DeletePayonexCustomer(id int64) error {

	if err := r.db.Table("paygate_payonex_customer").Where("id = ?", id).Delete(&model.PayonexCustomer{}).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetDbPayonexAccessToken() (*model.PayonexToken, error) {

	var result model.PayonexToken
	expireAt := time.Now().Add(time.Minute * 2)

	if err := r.db.Table("paygate_payonex_token").
		Where("expire_at > ?", expireAt).
		Take(&result).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (r repo) CreateDbPayonexAccessToken(body model.PayonexTokenCreateBody) (*int64, error) {

	if err := r.db.Table("paygate_payonex_token").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}
