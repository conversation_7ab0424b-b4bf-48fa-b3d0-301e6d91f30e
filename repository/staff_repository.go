package repository

import (
	"cybergame-api/model"

	"gorm.io/gorm"
)

func NewStaffRepository(db *gorm.DB) StaffRepository {
	return &repo{db}
}

type StaffRepository interface {
	GetStaffById(staffId int64) (*model.StaffDetail, error)
	GetStaffList() ([]model.StaffDetail, error)
	GetCoverUrlByStaffId(staffId int64) (string, error)
	CreateStaff(staff model.StaffBody) error
	UpdateStaff(staffId int64, body model.StaffUpdateBody) error
	UpdateStaffSortOrder(list model.StaffSortBody) error
	DeleteStaff(staffId int64) error
}

func (r repo) GetStaffById(staffId int64) (*model.StaffDetail, error) {

	var result model.StaffDetail

	if err := r.db.Table("staff").
		Select("id, staff_url, name_en, name_th, position_en, position_th, type, sort_order, created_at").
		Where("id = ?", staffId).
		Take(&result).
		Error; err != nil {
		return nil, err
	}

	return &result, nil
}

func (r repo) GetStaffList() ([]model.StaffDetail, error) {

	var result []model.StaffDetail

	if err := r.db.Table("staff").
		Select("id, staff_url, name_en, name_th, position_en, position_th, type, sort_order, created_at").
		Order("sort_order ASC").
		Scan(&result).
		Error; err != nil {
		return nil, err
	}

	return result, nil
}

func (r repo) GetCoverUrlByStaffId(staffId int64) (string, error) {

	var result string

	if err := r.db.Table("staff").
		Select("staff_url").
		Where("id = ?", staffId).
		Pluck("staff_url", &result).
		Error; err != nil {
		return "", err
	}

	return result, nil
}

func (r repo) CreateStaff(staff model.StaffBody) error {

	tx := r.db.Begin()
	var countType int64

	if err := tx.Table("staff").Create(&staff).Error; err != nil {
		tx.Rollback()
		return err
	}

	if staff.Type == "MANAGEMENT" {
		if err := tx.Table("staff").Where("type = ?", "MANAGEMENT").Count(&countType).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	if staff.Type == "STAFF" {
		if err := tx.Table("staff").Where("type = ?", "STAFF").Count(&countType).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	if err := tx.Table("staff").Where("id = ?", staff.Id).Update("sort_order", countType).Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Commit().Error; err != nil {
		return err
	}

	return nil
}

func (r repo) UpdateStaff(staffId int64, body model.StaffUpdateBody) error {

	update := map[string]interface{}{
		"staff_url":   body.StaffUrl,
		"name_en":     body.NameEn,
		"name_th":     body.NameTh,
		"position_en": body.PositionEn,
		"position_th": body.PositionTh,
		"type":        body.Type,
	}

	if err := r.db.Table("staff").Where("id = ?", staffId).Updates(update).Error; err != nil {
		return err
	}

	return nil
}

func (r repo) UpdateStaffSortOrder(list model.StaffSortBody) error {

	tx := r.db.Begin()

	for _, item := range list.List {
		if err := tx.Table("staff").Where("id = ?", item.Id).Update("sort_order", item.SortOrder).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	if err := tx.Commit().Error; err != nil {
		return err
	}

	return nil
}

func (r repo) DeleteStaff(staffId int64) error {

	if err := r.db.Table("staff").Where("id = ?", staffId).Delete(&model.Staff{}).Error; err != nil {
		return err
	}

	return nil
}
