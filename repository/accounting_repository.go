package repository

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"fmt"
	"io"
	"math"
	"strings"
	"time"

	"gorm.io/gorm"
)

func NewAccountingRepository(db *gorm.DB) AccountingRepository {
	return &repo{db}
}

type AccountingRepository interface {
	GetDb() *gorm.DB
	//Options
	GetConnectionStatus() ([]model.ConnectionStatusResponse, error)
	GetAccountStatus() ([]model.AccountStatusResponse, error)

	GetAdminById(id int64) (*model.Admin, error)

	GetBanks(req model.BankListRequest) (*model.SuccessWithPagination, error)
	GetBankByCodeWithFlag(code string) (*model.BankResponse, error)
	GetBankById(id int64) (*model.Bank, error)
	GetBankByCode(code string) (*model.Bank, error)

	GetAccountTypes(req model.AccountTypeListRequest) (*model.SuccessWithPagination, error)
	GetAccounTypeById(id int64) (*model.AccountType, error)

	GetUserByMemberCode(memberCode string) (*model.GetUserByMemberCode, error)
	GetUser(id int64) (*model.UserDetail, error)

	SortBankAccountList(req model.DragSortRequest) error
	HasBankAccount(accountNumber string) (bool, error)
	GetBankAccountById(id int64) (*model.BankAccount, error)
	GetBankAccountByAccountNumber(accountNumber string) (*model.BankAccount, error)
	GetBankAccountByExternalId(id int64) (*model.BankAccount, error)
	GetActiveExternalAccount() (*model.BankAccount, error)
	GetDepositAccountById(id int64) (*model.BankAccount, error)
	GetWithdrawAccountById(id int64) (*model.BankAccount, error)
	GetBankAccounts(data model.BankAccountListRequest) (*model.SuccessWithPagination, error)
	GetFastBankAccountList(req model.BankAccountListRequest) ([]model.BankAccountResponse, int64, error)
	GetBankAccountForValidation() ([]model.BankAccountResponse, error)
	GetBankAccountForWithdraw() (*model.BankAccount, error)
	GetBankAccountOnlyTrueAccountForWithdraw() (*model.BankAccount, error)
	GetBankAccountPriorities() (*model.SuccessWithPagination, error)
	GetBotBankAccounts(data model.BankAccountListRequest) (*model.SuccessWithPagination, error)
	CreateBankAccount(data model.BankAccountCreateBody) (*int64, error)
	UpdateBankAccountIsShowBank(data model.UpdateBankAccountIsShowBankRequest) error
	// ResetMainWithdrawBankAccount() error
	UpdateBankAccount(id int64, data model.BankAccountUpdateBody) error
	DeleteBankAccount(id int64, data model.BankAccountDeleteBody) error

	GetTransactionById(id int64) (*model.BankAccountTransaction, error)
	GetTransactions(data model.BankAccountTransactionListRequest) (*model.SuccessWithPagination, error)
	CreateTransaction(data model.BankAccountTransactionBody) error
	UpdateTransaction(id int64, data model.BankAccountTransactionBody) error
	DeleteTransaction(id int64) error

	GetTransferById(id int64) (*model.BankAccountTransfer, error)
	GetTransfers(data model.BankAccountTransferListRequest) (*model.SuccessWithPagination, error)
	CreateTransfer(data model.BankAccountTransferBody) error
	ConfirmTransfer(id int64, data model.BankAccountTransferConfirmBody) error
	DeleteTransfer(id int64) error

	CreateWebhookLog(body model.WebhookLogCreateBody) (*int64, error)
	UpdateWebhookLog(id int64, body model.WebhookLogUpdateBody) error
	GetWebhookStatementByExternalId(id int64) (*model.BankStatement, error)
	CreateWebhookStatement(body model.BankStatementCreateBody) (*int64, error)

	GetBotaccountConfigByKey(req model.BotAccountConfigListRequest) (*model.BotAccountConfig, error)
	GetBotaccountConfigs(req model.BotAccountConfigListRequest) (*model.SuccessWithPagination, error)
	CreateBotaccountConfig(data model.BotAccountConfigCreateBody) error
	DeleteBotaccountConfigByKey(key string) error
	DeleteBotaccountConfigById(id int64) error

	// Banking REPO
	GetBankTransactionWithStatementList(statementIds []int64) ([]model.BankTransactionStatementResponse, int64, error)
	GetBankStatements(req model.BankStatementListRequest) (*model.SuccessWithPagination, error)
	GetBankExternalStatements(externalIds []int64) (*model.SuccessWithPagination, error)
	GetBankExternalStatementMaps(externalIds []int64) (map[int64]model.BankStatement, error)
	GetBankStatementByExternalId(externalId int64) (*model.BankStatement, error)
	GetTransactionByStatementId(statementId int64) (model.BankTransaction, error)
	GetMemberById(id int64) (*model.Member, error)
	IncreaseMemberCredit(body model.MemberStatementCreateBody) error
	GetMemberStatementTypeByCode(code string) (*model.MemberStatementType, error)
	GetPossibleOwnersByStatementId(req model.GetPossibleOwnersRequest) ([]model.Member, int64, error)
	GetBankStatementById(id int64) (*model.BankStatement, error)
	InsertBankTransaction(data model.BankTransactionCreateBody) (*int64, error)
	CreateBankDepositTransactionNoOwner(data model.BankTransactionNoOwnerCreateBody) (*int64, error)
	UpdateDepositTransactionOwner(statementid int64, data model.BankTransactionUpdateOwnerBody) (*int64, error)
	CreateBankWithdrawTransaction(data model.BankTransactionCreateBody) (*int64, error)
	UpdateBankTransaction(id int64, data interface{}) error
	GetBankTransactionById(id int64) (*model.BankTransaction, error)
	CreateTransactionAction(data model.CreateBankTransactionActionBody) (*int64, error)
	RollbackTransactionAction(id int64) error
	ConfirmPendingDepositTransaction(id int64, data model.BankDepositTransactionConfirmBody) error
	ConfirmPendingCreditDepositTransaction(id int64, data model.BankDepositTransactionConfirmBody) error
	ConfirmPendingWithdrawTransaction(id int64, data model.BankWithdrawTransactionConfirmBody) error
	CreateStatementAction(data model.CreateBankStatementActionBody) error
	UpdateBankStatementStatus(data model.BankStatementUpdateBody) error
	// MatchStatementOwner(id int64, data model.BankStatementUpdateBody) error
	IgnoreStatementOwner(id int64, data model.BankStatementUpdateBody) error

	//Check duplicate if user send Confirm deposit in before webhook
	CheckDuplicateDepositFromWeb(find model.BankStatementCreateBody) (total int64, err error)
	CheckUserByBankAccountNumber(bankAccount string, bankId int64) (total int64, err error)
	FindMoveTransaction(checkAccount model.CheckDepositAccountMoveTransaction) (total int64, err error)
	UpdateBankAccountPriority(list model.PriorityWithdrawstructRequest) error

	GetWebBankDepositAccount() ([]model.WebBankAccountResponse, error)
	GetActiveDepositBankAccountList() ([]model.WebBankAccountResponse, error)
	IsFirstDeposit(userId int64) bool
	SetUserFirstDeposit(body model.UserFirstDepositCreateBody, transId *int64) (*int64, error)

	//validation priority withdraw
	GetBankAccountWithSumWithdrawCreditAmount() ([]model.BankAccountWithdrawAndSumAmount, error)
	GetBankAccountWithSumWithdrawCreditAmountByAccount(bankAccountList []string) ([]model.BankAccountWithdrawAndSumAmount, error)
	GetConfiguration() (*model.ConfigurationResponse, error)
	GetBankAccountWithWithdrawSumCreditAmountByAccountNumber(bankAccount string) (*model.BankAccountWithdrawAndSumAmount, error)
	GetSumMoveTransactionFromAccountToDay() ([]model.GetSumMoveTransactionFromAccountToDayResponse, error)
	GetSumMoveTransactionToAccountToDay() ([]model.GetSumMoveTransactionToAccountToDayResponse, error)

	// web Socket
	// WebSocket(reqAlert model.WebScoket) error
	UpdateAutoProcessTimer(timer string, id int64) error

	//turn over
	CreateTurnoverUserStatement(body model.TurnoverUserStatementCreateBody) (*int64, error)
	// REF-USER_CREDIT
	IncreaseUserCredit(body model.UserTransactionCreateRequest) (*model.UserTransactionCreateResponse, error)
	DecreaseUserCredit(body model.UserTransactionCreateRequest) (*model.UserTransactionCreateResponse, error)
	ShowUserTransaction(req model.UserTransactionShowUpdate) error
	// Update after call USER_CREDIT
	UpdateDepositTransactionStatusFromAgent(transId int64, body model.UserTransactionCreateResponse) error
	UpdateWithdrawTransactionStatusFromAgent(transId int64, body model.UserTransactionCreateResponse) error

	CheckWebhookStatementDuplicateWithRecord(account string, fromBankId int64) (*model.BankStatement, error)

	CreateBankTransactionLog(data model.BankTransactionLogCreate) (*int64, error)
	UpdateBankTransactionLog(id int64, data model.BankTransactionLogBody) error

	CheckDuplicateWebhookAndAdminRecord(req model.CheckDuplicateWebhookAndAdminRecord) (*model.BankTransaction, error)
	CheckDuplicateWebhookAndAdminRecord2(req model.CheckDuplicateWebhookAndAdminRecord) (*model.BankTransaction, error)
	UpdateTransactionDuplicateWithExternalMatch(id int64, statementId int64) error

	SuccessLog(name string, req interface{}, result interface{}) error
	ErrorLog(name string, req interface{}, result interface{}) error

	GetWebConfiguration() (*model.GetWebConfigurationBody, error)
	// REF-FASTBANK_CREDIT
	DecreaseFastbankCredit(fastbankCredit int64) error
	GetSummaryReportAccountList() ([]model.FastBankAccountResponse, int64, error)
	GetBankAccountWithSumDepostCreditAmount() ([]model.BankAccounDepositAndSumAmount, error)
	GetBankAccountWithSumDepostCreditAmountByAccount(bankAccountList []string) ([]model.BankAccounDepositAndSumAmount, error)
	// REF-FASTBANK-READ
	SetExternalStatementRead(body model.ExternalStatementSetReadBody) error
	// REF-User AFFILIATE
	UpdateGenMemberCodeToAffilate(userId int64) error
	CreateAffiliateMember(refBy int64, userId int64) error

	//promotion
	UpdatePromotionToBankTransaction(transactionId int64, promotionWebUserId int64) error

	//withdraw external
	ExternalCheckWithdrawTransaction(isTrueMoney bool, req model.ExternalCheckWithdrawTransaction) (*model.GetBankTransactionWithdrawListResponse, error)
	GetTransactionWithdrawById(transId int64) (*model.GetBankTransactionWithdrawListResponse, error)
	UpdateAdminAndTransactionStatus(id int64, body model.UpdateConfirmAutoWithdrawBody) error
	UpdateConfirmedByAdminId(id int64, body model.UpdateConfirmedByAdminIdRequest) error

	GetRaceActionIdByActionKey(actionKey string) (*int64, error)
	CreateRaceCondition(body model.RaceActionCreateBody) (int64, error)
	GetLocalWebInfo() (*model.WebStatusResponse, error)

	CronjobDeleteBankTransactionLog() error
	CronjobDeleteWebHookLog() error
	// REPORT
	GetBankTransactionSummaryGraph2(req model.BankTransactionGraph2Request) (*model.BankTransactionGraph2Response, error)
	// [ADMIN_LOG]
	CreateAdminLog(body model.AdminLogCreateBody) (*int64, error)
	CreateAdminAction(body model.AdminActionCreateBody) (*int64, error)
	CreateBankTransactionExternalDetail(body model.BankTransactionExternalDetailCreateRequest) (int64, error)

	// exchage
	GetExchangeCurrencyList(req model.GetExchangeCurrencyListRequest) ([]model.GetExchangeCurrencyListResponse, error)
	CreateExchangeRate(req []model.CreateExchangeRateRequest) error
	DeleteAllExchangeRate() error
	GetExchangeRateList(show string) ([]model.GetExchangeRateList, error)
	GetLaosExchangeCurrency() (*model.GetExchangeRate, error)
	CreateExchangeUpdateLog(req []model.CreateExchangeUpdateLogRequest) error
	GetExchangeCurrencyInternalList() ([]model.GetExchangeCurrencyListResponse, error)
	GetExchangeUpdateLogList(req model.GetExchangeUpdateLogListRequest) ([]model.GetExchangeUpdateLogListReponse, int64, error)

	// sms
	CreateDBSmsModeDeposit(data model.CreateSmsModeDepositBody) (*int64, error)
	UpdateDBSmsModeDeposit(data model.UpdateSmsModeDepositBody) error
	CountDBSmsModeDeposit() (int64, error)
	GetSmsModeDepositFromOrder(orderNo string) (*model.PaygateSmsModeDeposit, error)
	UpdateConfirmSmsModeDeposit(data model.UpdateConfirmSmsModeDepositBody) error
	CheckSmsModeDepositOrderInLastOrder(userId int64, bankAccountId int64) (*model.SmsModeDepositOrderResponse, error)

	// show to bank
	CreateBankAccountShowBank(body []model.CreateBankAccountShowBank) error
	DeleteBankAccountShowBankByBankAccountId(bankAccountId int64) error
	GetBankAccountShowBankByBankAccountId(bankAccountId []int64) ([]model.GetBankAccountShowBank, error)
	GetUserBankIdByUserId(userId int64) (*model.UserBankDetailBody, error)

	GetBankAccountMigration() ([]int64, error)
	GetBankCurreny() ([]model.BankResponse, error)
	GetBankAccountShowBankByBank() ([]model.GetBankAccountShowBank, error)

	// upload image
	UploadImageToCloudflare(pathUplaod string, filename string, fileReader io.Reader) (*model.CloudFlareUploadCreateBody, error)
	UploadImageToS3(pathUpload string, filename string, fileReader io.Reader) (*model.FileUploadResponse, error)

	// tier
	IncreaseUserTierDepositAmount(userId int64, amount float64) error

	// account
	TotalBankStatementSummary(req model.TotalBankStatementRequest) (*model.TotalBankTransactionSummaryResponse, error)
	GetUserTransactionCountDepositTimeByUser(userId int64) (*model.GetUserTransactionCountDepositTimeByUserResponse, error)
}

func (r repo) GetConnectionStatus() ([]model.ConnectionStatusResponse, error) {
	var result []model.ConnectionStatusResponse

	selectedFields := "id, name, label_th, label_en"
	if err := r.db.Table("connection_status").Select(selectedFields).Scan(&result).Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (r repo) GetAccountStatus() ([]model.AccountStatusResponse, error) {
	var result []model.AccountStatusResponse
	selectedFields := "id, name, label_th, label_en"
	if err := r.db.Table("account_status").Select(selectedFields).Scan(&result).Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (r repo) GetAdminById(id int64) (*model.Admin, error) {

	var admin model.Admin

	if err := r.db.Table("admin").
		Select("id, username, phone, password, email, role, fullname").
		Where("id = ?", id).
		First(&admin).
		Error; err != nil {
		return nil, err
	}
	return &admin, nil
}

func (r repo) GetBanks(req model.BankListRequest) (*model.SuccessWithPagination, error) {

	var list []model.BankResponse
	var total int64
	var err error
	// p.lay ให้เพิ่ม 2024/05/10
	var configuration model.ConfigurationBankResponse

	selectedFields := "con.use_th_currency AS use_th_currency, con.use_laos_currency AS use_laos_currency"
	configBank := r.db.Table("configuration_web AS con")
	configBank = configBank.Select(selectedFields)
	if err := configBank.Take(&configuration).Error; err != nil {
		configuration.UseThCurrency = true
	}

	if !configuration.UseThCurrency && !configuration.UseLaosCurrency {
		configuration.UseThCurrency = true
	}
	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("bank")
	count = count.Select("id")
	if configuration.UseThCurrency && configuration.UseLaosCurrency {
		count = count.Where("country_code IN ?", []string{"TH", "ALL", "LAOS"})
	} else if configuration.UseThCurrency {
		count = count.Where("country_code IN ?", []string{"TH", "ALL"})
	} else if configuration.UseLaosCurrency {
		count = count.Where("country_code IN ?", []string{"LAOS", "ALL"})
	}
	if req.Search != "" {
		count = count.Where("code = ?", req.Search)
	}
	if err = count.
		Count(&total).
		Error; err != nil {
		return nil, err
	}

	if total > 0 {
		// SELECT //
		query := r.db.Table("bank")
		query = query.Select("id, name, code, icon_url, icon_url, type_flag")
		if configuration.UseThCurrency && configuration.UseLaosCurrency {
			query = query.Where("country_code IN ?", []string{"TH", "ALL", "LAOS"})
		} else if configuration.UseThCurrency {
			query = query.Where("country_code IN ?", []string{"TH", "ALL"})
		} else if configuration.UseLaosCurrency {
			query = query.Where("country_code IN ?", []string{"LAOS", "ALL"})
		}
		if req.Search != "" {
			query = query.Where("code = ?", req.Search)
		}

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, err
		}
	}

	// End count total records for pagination purposes (without limit and offset) //
	var result model.SuccessWithPagination
	if list == nil {
		list = []model.BankResponse{}
	}
	result.List = list
	result.Total = total
	return &result, nil
}

func (r repo) GetBankById(id int64) (*model.Bank, error) {

	var result *model.Bank
	if err := r.db.Table("bank").
		Select("id, name, code, icon_url, type_flag").
		Where("id = ?", id).
		First(&result).
		Error; err != nil {
		return nil, err
	}

	// if result.Id == 0 {
	// 	return nil, errors.New(bankNotFound)
	// }
	return result, nil
}

func (r repo) GetBankByCode(code string) (*model.Bank, error) {

	var result *model.Bank
	if err := r.db.Table("bank").
		Select("id, name, code, icon_url, type_flag").
		Where("code = ?", code).
		First(&result).
		Error; err != nil {
		return nil, err
	}

	// if result.Id == 0 {
	// 	return nil, errors.New(bankNotFound)
	// }
	return result, nil
}
func (r repo) GetBankByCodeWithFlag(code string) (*model.BankResponse, error) {

	code = strings.ToLower(code)
	var result *model.BankResponse
	if err := r.db.Table("bank").
		Select("id, name, code, icon_url, type_flag").
		Where("code = ?", code).
		First(&result).
		Error; err != nil {
		return nil, err
	}

	// if result.Id == 0 {
	// 	return nil, errors.New(bankNotFound)
	// }
	return result, nil
}

func (r repo) GetAccountTypes(req model.AccountTypeListRequest) (*model.SuccessWithPagination, error) {

	var list []model.AccountTypeResponse
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("bank_account_type")
	count = count.Select("id")
	if req.Search != "" {
		count = count.Where("name = ?", req.Search)
	}
	if err = count.
		Count(&total).
		Error; err != nil {
		return nil, err
	}

	if total > 0 {
		// SELECT //
		query := r.db.Table("bank_account_type")
		query = query.Select("id, name, limit_flag")
		if req.Search != "" {
			query = query.Where("name = ?", req.Search)
		}

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, err
		}
	}

	// End count total records for pagination purposes (without limit and offset) //
	var result model.SuccessWithPagination
	if list == nil {
		list = []model.AccountTypeResponse{}
	}
	result.List = list
	result.Total = total
	return &result, nil
}

func (r repo) GetAccounTypeById(id int64) (*model.AccountType, error) {

	var result model.AccountType
	if err := r.db.Table("bank_account_type").
		Select("id, name, limit_flag").
		Where("id = ?", id).
		First(&result).
		Error; err != nil {
		return nil, err
	}

	// if result.Id == 0 {
	// 	return nil, errors.New("Account type not found")
	// }
	return &result, nil
}

func (r repo) GetUserByMemberCode(memberCode string) (*model.GetUserByMemberCode, error) {

	var result model.GetUserByMemberCode
	if err := r.db.Table("user").
		Joins("LEFT JOIN bank ON bank.id = user.bank_id").
		Select(`
			user.id,
			user.member_code,
			user.fullname,
			user.username,
			bank.name AS bankName,
			bank.code AS bankCode,
			user.bank_account,
			user.credit,
			bank.code AS bank_code
		`).
		Where("user.member_code = ?", memberCode).
		Scan(&result).
		Limit(1).
		Error; err != nil {
		return nil, err
	}

	return &result, nil
}

func (r repo) HasBankAccount(accountNumber string) (bool, error) {
	var count int64
	if err := r.db.Table("bank_account").
		Select("id").
		Where("account_number = ?", accountNumber).
		Where("deleted_at IS NULL").
		Limit(1).
		Count(&count).
		Error; err != nil {
		return false, err
	}
	return count > 0, nil
}

func (r repo) GetBankAccountById(id int64) (*model.BankAccount, error) {

	var accounting model.BankAccount
	selectedFields := "accounts.id, accounts.bank_id, accounts.account_type_id, accounts.account_name, accounts.account_number, accounts.account_balance, accounts.device_uid, accounts.pin_code "
	selectedFields += ", accounts.last_conn_update_at, accounts.created_at, accounts.updated_at"
	selectedFields += ", banks.name as bank_name, banks.code as bank_code, banks.icon_url as bank_icon_url, banks.type_flag"
	selectedFields += ", account_types.name as account_type_name, account_types.limit_flag"
	selectedFields += ", accounts.connection_status_id, connection_status.name as connection_status_name"
	selectedFields += ", accounts.account_priority_withdraw as account_priority_withdraw"
	selectedFields += ", accounts.auto_withdraw_type_id as auto_withdraw_type_id"
	selectedFields += ", accounts.sms_mode"
	selectedFields += ", accounts.show_bank_deposit_over_due_time as show_bank_deposit_over_due_time"
	selectedFields += ", accounts.show_bank_deposit_max_due_time as show_bank_deposit_max_due_time"
	// selectedFields += ", account_status.id as account_status_id, account_status.name as account_status_name"
	selectedFields += ", banks.use_currency as bank_use_currency"
	// [********] เอาออกเพราะไม่ได้ใช้ ไปใช้ config แทน
	// selectedFields += ", accounts.bank_withdraw_maximum as bank_withdraw_maximum, accounts.auto_withdraw_maximum as auto_withdraw_maximum"
	selectedFields += ", accounts.is_manual_bank as is_manual_bank"
	selectedFields += ", accounts.image_url as image_url"

	if err := r.db.Table("bank_account as accounts").
		Select(selectedFields).
		Joins("LEFT JOIN bank AS banks ON banks.id = accounts.bank_id").
		Joins("LEFT JOIN bank_account_type AS account_types ON account_types.id = accounts.account_type_id").
		Joins("LEFT JOIN connection_status ON connection_status.id = accounts.connection_status_id").
		// Joins("LEFT JOIN account_status ON account_status.id = accounts.account_status_id").
		Where("accounts.id = ?", id).
		Where("accounts.deleted_at IS NULL").
		First(&accounting).
		Error; err != nil {
		return nil, err
	}
	return &accounting, nil
}

func (r repo) GetDepositAccountById(id int64) (*model.BankAccount, error) {

	var accounting model.BankAccount
	selectedFields := "accounts.id, accounts.bank_id, accounts.account_type_id, accounts.account_name, accounts.account_number, accounts.account_balance, accounts.device_uid, accounts.pin_code"
	selectedFields += ", accounts.last_conn_update_at, accounts.created_at, accounts.updated_at"
	selectedFields += ", banks.name as bank_name, banks.code as bank_code, banks.icon_url as bank_icon_url, banks.type_flag"
	selectedFields += ", account_types.name as account_type_name, account_types.limit_flag"
	selectedFields += ", accounts.connection_status_id, connection_status.name as connection_status_name"
	selectedFields += ", accounts.auto_withdraw_type_id as auto_withdraw_type_id"
	// selectedFields += ", account_status.id as account_status_id, account_status.name as account_status_name"
	if err := r.db.Table("bank_account as accounts").
		Select(selectedFields).
		Joins("LEFT JOIN bank AS banks ON banks.id = accounts.bank_id").
		Joins("LEFT JOIN bank_account_type AS account_types ON account_types.id = accounts.account_type_id").
		Joins("LEFT JOIN connection_status ON connection_status.id = accounts.connection_status_id").
		// Joins("LEFT JOIN account_status ON account_status.id = accounts.account_status_id").
		Where("accounts.id = ?", id).
		Where("account_types.allow_deposit = 1").
		Where("accounts.deleted_at IS NULL").
		First(&accounting).
		Error; err != nil {
		return nil, err
	}
	return &accounting, nil
}

func (r repo) GetWithdrawAccountById(id int64) (*model.BankAccount, error) {

	var accounting model.BankAccount
	selectedFields := "accounts.id, accounts.bank_id, accounts.account_type_id, accounts.account_name, accounts.account_number, accounts.account_balance, accounts.device_uid, accounts.pin_code"
	selectedFields += ", accounts.last_conn_update_at, accounts.created_at, accounts.updated_at"
	selectedFields += ", banks.name as bank_name, banks.code as bank_code, banks.icon_url as bank_icon_url, banks.type_flag"
	selectedFields += ", account_types.name as account_type_name, account_types.limit_flag"
	selectedFields += ", accounts.connection_status_id, connection_status.name as connection_status_name"
	selectedFields += ", accounts.auto_withdraw_type_id as auto_withdraw_type_id"
	// selectedFields += ", account_status.id as account_status_id, account_status.name as account_status_name"
	if err := r.db.Table("bank_account as accounts").
		Select(selectedFields).
		Joins("LEFT JOIN bank AS banks ON banks.id = accounts.bank_id").
		Joins("LEFT JOIN bank_account_type AS account_types ON account_types.id = accounts.account_type_id").
		Joins("LEFT JOIN connection_status ON connection_status.id = accounts.connection_status_id").
		// Joins("LEFT JOIN account_status ON account_status.id = accounts.account_status_id").
		Where("accounts.id = ?", id).
		Where("account_types.allow_withdraw = 1").
		Where("accounts.deleted_at IS NULL").
		First(&accounting).
		Error; err != nil {
		return nil, err
	}
	return &accounting, nil
}

func (r repo) GetBankAccountByAccountNumber(accountNumber string) (*model.BankAccount, error) {

	var accounting model.BankAccount
	selectedFields := "accounts.id, accounts.bank_id, accounts.account_type_id, accounts.account_name, accounts.account_number, accounts.account_balance, accounts.device_uid, accounts.pin_code"
	selectedFields += ", accounts.last_conn_update_at, accounts.created_at, accounts.updated_at"
	selectedFields += ", banks.name as bank_name, banks.code as bank_code, banks.icon_url as bank_icon_url, banks.type_flag"
	selectedFields += ", account_types.name as account_type_name, account_types.limit_flag"
	selectedFields += ", accounts.connection_status_id, connection_status.name as connection_status_name"
	selectedFields += ", accounts.auto_withdraw_type_id as auto_withdraw_type_id"
	// selectedFields += ", account_status.id as account_status_id, account_status.name as account_status_name"
	if err := r.db.Table("bank_account as accounts").
		Select(selectedFields).
		Joins("LEFT JOIN bank AS banks ON banks.id = accounts.bank_id").
		Joins("LEFT JOIN bank_account_type AS account_types ON account_types.id = accounts.account_type_id").
		Joins("LEFT JOIN connection_status ON connection_status.id = accounts.connection_status_id").
		// Joins("LEFT JOIN account_status ON account_status.id = accounts.account_status_id").
		Where("accounts.account_number = ?", accountNumber).
		Where("accounts.deleted_at IS NULL").
		First(&accounting).
		Error; err != nil {
		return nil, err
	}
	return &accounting, nil
}

func (r repo) GetBankAccountByExternalId(external_id int64) (*model.BankAccount, error) {

	var accounting model.BankAccount
	selectedFields := "accounts.id, accounts.bank_id, accounts.account_type_id, accounts.account_name, accounts.account_number, accounts.account_balance, accounts.device_uid, accounts.pin_code"
	selectedFields += ", accounts.last_conn_update_at, accounts.created_at, accounts.updated_at"
	selectedFields += ", accounts.connection_status_id, connection_status.name as connection_status_name"
	selectedFields += ", accounts.auto_withdraw_type_id as auto_withdraw_type_id"
	selectedFields += ", banks.name as bank_name"
	selectedFields += ", accounts.sms_mode"

	if err := r.db.Table("bank_account as accounts").
		Select(selectedFields).
		Joins("LEFT JOIN bank AS banks ON banks.id = accounts.bank_id").
		Joins("LEFT JOIN connection_status ON connection_status.id = accounts.connection_status_id").
		// Joins("LEFT JOIN account_status ON account_status.id = accounts.account_status_id").
		Where("accounts.external_id = ?", external_id).
		Where("accounts.deleted_at IS NULL").
		First(&accounting).
		Error; err != nil {
		return nil, err
	}
	return &accounting, nil
}

func (r repo) GetActiveExternalAccount() (*model.BankAccount, error) {

	var accounting model.BankAccount
	selectedFields := "accounts.id, accounts.bank_id, accounts.account_type_id, accounts.account_name, accounts.account_number, accounts.account_balance, accounts.device_uid, accounts.pin_code"
	selectedFields += ", accounts.last_conn_update_at, accounts.created_at, accounts.updated_at"
	selectedFields += ", accounts.connection_status_id, connection_status.name as connection_status_name"
	selectedFields += ", accounts.auto_withdraw_type_id as auto_withdraw_type_id"
	// selectedFields += ", account_status.id as account_status_id, account_status.name as account_status_name"
	if err := r.db.Table("bank_account as accounts").
		Select(selectedFields).
		Joins("LEFT JOIN connection_status ON connection_status.id = accounts.connection_status_id").
		// Joins("LEFT JOIN account_status ON account_status.id = accounts.account_status_id").
		Where("accounts.connection_status_id = ?", model.CONNECTION_CONNECTED).
		Where("accounts.external_id IS NOT NULL").
		Where("accounts.deleted_at IS NULL").
		First(&accounting).
		Error; err != nil {
		return nil, err
	}
	return &accounting, nil
}

func (r repo) GetBankAccounts(req model.BankAccountListRequest) (*model.SuccessWithPagination, error) {

	var list []model.BankAccountResponse
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("bank_account AS accounts")
	count = count.Select("accounts.id")
	count = count.Joins("LEFT JOIN bank AS banks ON banks.id = accounts.bank_id")
	count = count.Joins("LEFT JOIN bank_account_type AS account_types ON account_types.id = accounts.account_type_id")
	if req.Search != "" {
		search_like := fmt.Sprintf("%%%s%%", req.Search)
		count = count.Where(r.db.Where("account_name LIKE ?", search_like).Or("account_number LIKE ?", search_like))
	}
	if req.AccountNumber != "" {
		count = count.Where("account_number = ?", req.AccountNumber)
	}
	if req.AccountType == "DEPOSIT" {
		count = count.Where("accounts.account_type_id in (?,?)", model.BANK_ACCOUNT_TYPE_DEPOSIT_ONLY, model.BANK_ACCOUNT_TYPE_BOTH)
	} else if req.AccountType == "WITHDRAW" {
		count = count.Where("accounts.account_type_id in (?,?)", model.BANK_ACCOUNT_TYPE_WITHDRAW_ONLY, model.BANK_ACCOUNT_TYPE_BOTH)
	} else if req.AccountType == "HOLD" {
		count = count.Where("accounts.account_type_id = ?", model.BANK_ACCOUNT_TYPE_HOLD)
	}
	if err = count.
		Where("accounts.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, err
	}

	if total > 0 {
		// SELECT //
		query := r.db.Table("bank_account AS accounts")
		selectedFields := "accounts.id, accounts.bank_id, accounts.account_type_id, accounts.account_name, accounts.account_number, accounts.account_balance, accounts.device_uid, accounts.pin_code"
		selectedFields += ", accounts.last_conn_update_at, accounts.created_at, accounts.updated_at, accounts.is_show_front as is_show_front"
		selectedFields += ", banks.name as bank_name, banks.code as bank_code, banks.icon_url as bank_icon_url, banks.type_flag"
		selectedFields += ", account_types.name as account_type_name, account_types.limit_flag"
		selectedFields += ", accounts.connection_status_id, connection_status.name as connection_status_name, accounts.account_priority_withdraw as account_priority_withdraw"
		selectedFields += ", accounts.device_uid as device_uid"
		selectedFields += ", accounts.admin_updated_at as admin_updated_at"
		selectedFields += ", accounts.auto_withdraw_type_id as auto_withdraw_type_id"
		selectedFields += ", accounts.sms_mode"
		selectedFields += ", accounts.is_manual_bank as is_manual_bank"
		selectedFields += ", accounts.image_url as image_url"
		selectedFields += ", accounts.show_bank_deposit_over_due_time as show_bank_deposit_over_due_time"
		selectedFields += ", accounts.show_bank_deposit_max_due_time as show_bank_deposit_max_due_time"
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN bank AS banks ON banks.id = accounts.bank_id")
		query = query.Joins("LEFT JOIN bank_account_type AS account_types ON account_types.id = accounts.account_type_id")
		query = query.Joins("LEFT JOIN connection_status ON connection_status.id = accounts.connection_status_id")
		if req.Search != "" {
			search_like := fmt.Sprintf("%%%s%%", req.Search)
			query = query.Where(r.db.Where("accounts.account_name LIKE ?", search_like).Or("accounts.account_number LIKE ?", search_like))
		}
		if req.AccountNumber != "" {
			query = query.Where("accounts.account_number = ?", req.AccountNumber)
		}
		if req.AccountType == "DEPOSIT" {
			query = query.Where("accounts.account_type_id in (?,?)", model.BANK_ACCOUNT_TYPE_DEPOSIT_ONLY, model.BANK_ACCOUNT_TYPE_BOTH)
		} else if req.AccountType == "WITHDRAW" {
			query = query.Where("accounts.account_type_id in (?,?)", model.BANK_ACCOUNT_TYPE_WITHDRAW_ONLY, model.BANK_ACCOUNT_TYPE_BOTH)
		} else if req.AccountType == "HOLD" {
			query = query.Where("accounts.account_type_id = ?", model.BANK_ACCOUNT_TYPE_HOLD)
		}

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Where("accounts.deleted_at IS NULL").
			Order("accounts.account_priority_withdraw ASC").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, err
		}
	}

	var bankAccountId []int64
	for _, v := range list {
		bankAccountId = append(bankAccountId, v.Id)
	}

	var listShowToBank []model.BankAccountShowBank
	selectedField2 := "id, bank_account_id, bank_id"
	query2 := r.db.Table("bank_account_show_bank")
	query2 = query2.Select(selectedField2)
	query2 = query2.Where("bank_account_show_bank.bank_account_id IN (?)", bankAccountId)
	query2 = query2.Order("bank_account_show_bank.id ASC")
	if err := query2.Scan(&listShowToBank).Error; err != nil {
		return nil, err
	}

	bankAccountShowBank := make(map[int64][]int64)
	for _, v := range listShowToBank {
		if _, ok := bankAccountShowBank[v.BankAccountId]; !ok {
			bankAccountShowBank[v.BankAccountId] = []int64{}
		}
		bankAccountShowBank[v.BankAccountId] = append(bankAccountShowBank[v.BankAccountId], v.BankId)
	}

	for i, v := range list {
		if _, ok := bankAccountShowBank[v.Id]; ok {
			list[i].ShowToBank = bankAccountShowBank[v.Id]
		}
	}

	// End count total records for pagination purposes (without limit and offset) //
	var result model.SuccessWithPagination
	result.List = list
	result.Total = total
	return &result, nil
}

func (r repo) GetFastBankAccountList(req model.BankAccountListRequest) ([]model.BankAccountResponse, int64, error) {

	var list []model.BankAccountResponse
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("bank_account AS accounts")
	count = count.Select("accounts.id")
	count = count.Joins("LEFT JOIN bank AS banks ON banks.id = accounts.bank_id")
	count = count.Joins("LEFT JOIN bank_account_type AS account_types ON account_types.id = accounts.account_type_id")
	if req.Search != "" {
		search_like := fmt.Sprintf("%%%s%%", req.Search)
		count = count.Where(r.db.Where("account_name LIKE ?", search_like).Or("account_number LIKE ?", search_like))
	}
	if req.AccountNumber != "" {
		count = count.Where("account_number = ?", req.AccountNumber)
	}
	if req.AccountType == "DEPOSIT" {
		count = count.Where("accounts.account_type_id in (?,?)", model.BANK_ACCOUNT_TYPE_DEPOSIT_ONLY, model.BANK_ACCOUNT_TYPE_BOTH)
	} else if req.AccountType == "WITHDRAW" {
		count = count.Where("accounts.account_type_id in (?,?)", model.BANK_ACCOUNT_TYPE_WITHDRAW_ONLY, model.BANK_ACCOUNT_TYPE_BOTH)
	} else {
		// NOT HOLD
		count = count.Where("accounts.account_type_id IN (?,?,?)", model.BANK_ACCOUNT_TYPE_DEPOSIT_ONLY, model.BANK_ACCOUNT_TYPE_WITHDRAW_ONLY, model.BANK_ACCOUNT_TYPE_BOTH)
	}
	if err = count.
		Where("accounts.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, 0, err
	}

	if total > 0 {
		// SELECT //
		query := r.db.Table("bank_account AS accounts")
		selectedFields := "accounts.id, accounts.bank_id, accounts.account_type_id, accounts.account_name, accounts.account_number, accounts.account_balance, accounts.device_uid, accounts.pin_code"
		selectedFields += ", accounts.last_conn_update_at, accounts.created_at, accounts.updated_at, accounts.is_show_front as is_show_front"
		selectedFields += ", banks.name as bank_name, banks.code as bank_code, banks.icon_url as bank_icon_url, banks.type_flag"
		selectedFields += ", account_types.name as account_type_name, account_types.limit_flag"
		selectedFields += ", accounts.connection_status_id, connection_status.name as connection_status_name, accounts.account_priority_withdraw as account_priority_withdraw"
		selectedFields += ", accounts.device_uid as device_uid"
		selectedFields += ", accounts.admin_updated_at as admin_updated_at"
		selectedFields += ", accounts.auto_withdraw_type_id as auto_withdraw_type_id"
		selectedFields += ", accounts.sms_mode"
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN bank AS banks ON banks.id = accounts.bank_id")
		query = query.Joins("LEFT JOIN bank_account_type AS account_types ON account_types.id = accounts.account_type_id")
		query = query.Joins("LEFT JOIN connection_status ON connection_status.id = accounts.connection_status_id")
		if req.Search != "" {
			search_like := fmt.Sprintf("%%%s%%", req.Search)
			query = query.Where(r.db.Where("accounts.account_name LIKE ?", search_like).Or("accounts.account_number LIKE ?", search_like))
		}
		if req.AccountNumber != "" {
			query = query.Where("accounts.account_number = ?", req.AccountNumber)
		}
		if req.AccountType == "DEPOSIT" {
			query = query.Where("accounts.account_type_id in (?,?)", model.BANK_ACCOUNT_TYPE_DEPOSIT_ONLY, model.BANK_ACCOUNT_TYPE_BOTH)
		} else if req.AccountType == "WITHDRAW" {
			query = query.Where("accounts.account_type_id in (?,?)", model.BANK_ACCOUNT_TYPE_WITHDRAW_ONLY, model.BANK_ACCOUNT_TYPE_BOTH)
		} else {
			// NOT HOLD
			query = query.Where("accounts.account_type_id IN (?,?,?)", model.BANK_ACCOUNT_TYPE_DEPOSIT_ONLY, model.BANK_ACCOUNT_TYPE_WITHDRAW_ONLY, model.BANK_ACCOUNT_TYPE_BOTH)
		}

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Where("accounts.deleted_at IS NULL").
			Order("accounts.account_priority_withdraw ASC").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, 0, err
		}
	}
	return list, total, nil
}

func (r repo) GetBankAccountForValidation() ([]model.BankAccountResponse, error) {

	var list []model.BankAccountResponse
	var err error
	// SELECT //
	query := r.db.Table("bank_account AS accounts")
	selectedFields := "accounts.id, accounts.bank_id, accounts.account_type_id, accounts.account_name, accounts.account_number, accounts.account_balance, accounts.device_uid, accounts.pin_code"
	selectedFields += ", accounts.last_conn_update_at, accounts.created_at, accounts.updated_at, accounts.is_show_front as is_show_front"
	selectedFields += ", banks.name as bank_name, banks.code as bank_code, banks.icon_url as bank_icon_url, banks.type_flag"
	selectedFields += ", account_types.name as account_type_name, account_types.limit_flag"
	selectedFields += ", accounts.connection_status_id, connection_status.name as connection_status_name, accounts.account_priority_withdraw as account_priority_withdraw"
	selectedFields += ", accounts.auto_withdraw_type_id as auto_withdraw_type_id"
	// selectedFields += ", account_status.id as account_status_id, account_status.name as account_status_name"
	query = query.Select(selectedFields)
	query = query.Joins("LEFT JOIN bank AS banks ON banks.id = accounts.bank_id")
	query = query.Joins("LEFT JOIN bank_account_type AS account_types ON account_types.id = accounts.account_type_id")
	query = query.Joins("LEFT JOIN connection_status ON connection_status.id = accounts.connection_status_id")
	// query = query.Joins("LEFT JOIN account_status ON account_status.id = accounts.account_status_id")
	if err = query.
		Where("accounts.deleted_at IS NULL").
		// [********] NO CHECK Where("accounts.connection_status_id = ?", model.CONNECTION_CONNECTED).
		Where("accounts.account_type_id in (?,?)", model.BANK_ACCOUNT_TYPE_WITHDRAW_ONLY, model.BANK_ACCOUNT_TYPE_BOTH).
		Order("accounts.account_priority_withdraw ASC").
		Scan(&list).
		Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (r repo) GetBankAccountForWithdraw() (*model.BankAccount, error) {

	var record model.BankAccount
	var err error
	// SELECT //
	query := r.db.Table("bank_account AS accounts")
	selectedFields := "accounts.id, accounts.bank_id, accounts.account_type_id, accounts.account_name, accounts.account_number, accounts.account_balance, accounts.device_uid, accounts.pin_code"
	selectedFields += ", accounts.last_conn_update_at, accounts.created_at, accounts.updated_at, accounts.is_show_front as is_show_front"
	selectedFields += ", banks.name as bank_name, banks.code as bank_code, banks.icon_url as bank_icon_url, banks.type_flag"
	selectedFields += ", account_types.name as account_type_name, account_types.limit_flag"
	selectedFields += ", accounts.connection_status_id, connection_status.name as connection_status_name, accounts.account_priority_withdraw as account_priority_withdraw"
	selectedFields += ", accounts.auto_withdraw_type_id as auto_withdraw_type_id"
	query = query.Select(selectedFields)
	query = query.Joins("LEFT JOIN bank AS banks ON banks.id = accounts.bank_id")
	query = query.Joins("LEFT JOIN bank_account_type AS account_types ON account_types.id = accounts.account_type_id")
	query = query.Joins("LEFT JOIN connection_status ON connection_status.id = accounts.connection_status_id")
	if err = query.
		Where("accounts.deleted_at IS NULL").
		// [********] NO CHECK Where("accounts.connection_status_id = ?", model.CONNECTION_CONNECTED).
		Where("accounts.account_type_id in (?,?)", model.BANK_ACCOUNT_TYPE_WITHDRAW_ONLY, model.BANK_ACCOUNT_TYPE_BOTH).
		Order("accounts.account_priority_withdraw ASC").
		Scan(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetBankAccountPriorities() (*model.SuccessWithPagination, error) {

	var list []model.BankAccountPriority
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("bank_account_priority AS priorities")
	count = count.Select("priorities.id")
	if err = count.
		Where("priorities.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, err
	}

	if total > 0 {
		// SELECT //
		query := r.db.Table("bank_account_priority AS priorities")
		selectedFields := "priorities.id, priorities.name, priorities.condition_type, priorities.min_deposit_count, priorities.min_deposit_total, priorities.created_at, priorities.updated_at"
		query = query.Select(selectedFields)

		query = query.Order("id ASC")
		if err = query.
			Where("priorities.deleted_at IS NULL").
			Scan(&list).
			Error; err != nil {
			return nil, err
		}
	}

	// End count total records for pagination purposes (without limit and offset) //
	var result model.SuccessWithPagination
	result.List = list
	result.Total = total
	return &result, nil
}

func (r repo) GetBotBankAccounts(req model.BankAccountListRequest) (*model.SuccessWithPagination, error) {

	var list []model.BankAccountResponse
	var total int64
	var err error
	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("bank_account")
	count = count.Select("id")
	// count = count.Where("device_uid != ?", "")
	// count = count.Where("pin_code != ?", "")
	// 2024/11/25  new case with sms mode remove device_uid and pin_code condition
	count = count.Where("external_id IS NOT NULL")
	if req.Search != "" {
		searchLike := fmt.Sprintf("%%%s%%", req.Search)
		count = count.Where(r.db.Where("account_name LIKE ?", searchLike).Or("account_number LIKE ?", searchLike))
	}
	if err = count.
		Where("deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "accounts.id, accounts.bank_id, accounts.account_type_id, accounts.account_name, accounts.account_number, accounts.account_balance, accounts.device_uid, accounts.pin_code"
		selectedFields += ", accounts.last_conn_update_at, accounts.created_at, accounts.updated_at, accounts.is_show_front as is_show_front"
		selectedFields += ", banks.name as bank_name, banks.code as bank_code, banks.icon_url as bank_icon_url, banks.type_flag"
		selectedFields += ", account_types.name as account_type_name, account_types.limit_flag"
		selectedFields += ", accounts.connection_status_id, connection_status.name as connection_status_name"
		selectedFields += ", accounts.auto_withdraw_type_id as auto_withdraw_type_id"
		// selectedFields += ", account_status.id as account_status_id, account_status.name as account_status_name"
		query := r.db.Table("bank_account AS accounts")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN bank AS banks ON banks.id = accounts.bank_id")
		query = query.Joins("LEFT JOIN bank_account_type AS account_types ON account_types.id = accounts.account_type_id")
		query = query.Joins("LEFT JOIN connection_status ON connection_status.id = accounts.connection_status_id")
		// query = query.Joins("LEFT JOIN account_status ON account_status.id = accounts.account_status_id")
		// query = query.Where("accounts.device_uid != ?", "")
		// query = query.Where("accounts.pin_code != ?", "")
		// 2024/11/25  new case with sms mode remove device_uid and pin_code condition
		query = query.Where("accounts.external_id IS NOT NULL")
		if req.Search != "" {
			searchLike := fmt.Sprintf("%%%s%%", req.Search)
			query = query.Where(r.db.Where("accounts.account_name LIKE ?", searchLike).Or("accounts.account_number LIKE ?", searchLike))
		}
		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Where("accounts.deleted_at IS NULL").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, err
		}
	}

	// End count total records for pagination purposes (without limit and offset) //
	var result model.SuccessWithPagination
	result.List = list
	result.Total = total
	return &result, nil
}

// func (r repo) ResetMainWithdrawBankAccount() error {

// 	cleanUpData := map[string]interface{}{
// 		"is_main_withdraw": 0,
// 	}
// 	if err := r.db.Table("bank_account").Where("is_main_withdraw != 0").Updates(cleanUpData).Error; err != nil {
// 		return err
// 	}
// 	return nil
// }

func (r repo) CreateBankAccount(data model.BankAccountCreateBody) (*int64, error) {

	// if data.IsMainWithdraw {
	// 	if err := r.ResetMainWithdrawBankAccount(); err != nil {
	// 		return err
	// 	}
	// }

	if err := r.db.Table("bank_account").Create(&data).Error; err != nil {
		return nil, err
	}
	return &data.Id, nil
}

func (r repo) UpdateBankAccount(id int64, data model.BankAccountUpdateBody) error {
	if err := r.db.Table("bank_account").Where("id = ?", id).Updates(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) DeleteBankAccount(id int64, data model.BankAccountDeleteBody) error {
	if err := r.db.Table("bank_account").Where("id = ?", id).Updates(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetTransactionById(id int64) (*model.BankAccountTransaction, error) {
	var record model.BankAccountTransaction
	selectedFields := "transactions.id, transactions.account_id, transactions.description, transactions.transaction_type_id, transactions.amount, transactions.transfer_at, transactions.created_by_username, transactions.created_at, transactions.updated_at"
	selectedFields += ",accounts.bank_id, accounts.account_type_id, accounts.account_name, accounts.account_number, accounts.account_balance, accounts.created_at, accounts.updated_at"
	selectedFields += ",banks.name as bank_name, banks.code as bank_code, banks.icon_url as bank_icon_url, banks.type_flag"
	selectedFields += ",transaction_type.label_th as transaction_type_th, transaction_type.label_en as transaction_type_en"
	// selectedFields += ", account_status.id as account_status_id, account_status.name as account_status_name"
	if err := r.db.Table("bank_account_transaction as transactions").
		Select(selectedFields).
		Joins("LEFT JOIN bank_account AS accounts ON accounts.id = transactions.account_id").
		Joins("LEFT JOIN bank AS banks ON banks.id = accounts.bank_id").
		// Joins("LEFT JOIN account_status ON account_status.id = accounts.account_status_id").
		Joins("LEFT JOIN transaction_type ON transaction_type.id = transactions.transaction_type_id").
		Where("transactions.id = ?", id).
		Where("transactions.deleted_at IS NULL").
		First(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetTransactions(req model.BankAccountTransactionListRequest) (*model.SuccessWithPagination, error) {

	var list []model.BankAccountTransactionResponse
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("bank_account_transaction as transactions")
	count = count.Select("transactions.id")
	count = count.Joins("LEFT JOIN bank_account AS accounts ON accounts.id = transactions.account_id")
	count = count.Joins("LEFT JOIN bank AS banks ON banks.id = accounts.bank_id")
	if req.AccountId != 0 {
		count = count.Where("transactions.account_id = ?", req.AccountId)
	}
	if req.FromCreatedDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.FromCreatedDate)
		if err != nil {
			return nil, err
		}
		count = count.Where("transactions.created_at >= ? ", startDateAtBkk)
	}
	if req.ToCreatedDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.ToCreatedDate)
		if err != nil {
			return nil, err
		}
		count = count.Where("transactions.created_at <=  ?", endDateAtBkk)
	}
	if req.TransactionTypeId != nil {
		count = count.Where("transactions.transaction_type_id = ?", req.TransactionTypeId)
	}
	if req.Search != "" {
		search_like := fmt.Sprintf("%%%s%%", req.Search)
		count = count.Where(r.db.Where("transactions.description LIKE ?", search_like).Or("accounts.account_name LIKE ?", search_like).Or("accounts.account_number LIKE ?", search_like))
	}
	if err = count.
		Where("transactions.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "transactions.id, transactions.account_id, transactions.description, transactions.transaction_type_id, transactions.amount, transactions.transfer_at, transactions.created_by_username, transactions.created_at as created_at, transactions.updated_at"
		selectedFields += ",accounts.bank_id, accounts.account_type_id, accounts.account_name, accounts.account_number, accounts.account_balance, accounts.updated_at"
		selectedFields += ",banks.name as bank_name, banks.code as bank_code, banks.icon_url as bank_icon_url, banks.type_flag"
		selectedFields += ",transaction_type.label_th as transaction_type_th, transaction_type.label_en as transaction_type_en"
		// selectedFields += ", account_status.id as account_status_id, account_status.name as account_status_name"
		query := r.db.Table("bank_account_transaction as transactions")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN bank_account AS accounts ON accounts.id = transactions.account_id")
		query = query.Joins("LEFT JOIN bank AS banks ON banks.id = accounts.bank_id")
		query = query.Joins("LEFT JOIN transaction_type ON transaction_type.id = transactions.transaction_type_id")

		// query = query.Joins("LEFT JOIN account_status ON account_status.id = accounts.account_status_id")
		if req.AccountId != 0 {
			query = query.Where("transactions.account_id = ?", req.AccountId)
		}
		if req.FromCreatedDate != "" {
			startDateAtBkk, err := r.ParseBodBkk(req.FromCreatedDate)
			if err != nil {
				return nil, err
			}
			query = query.Where("transactions.created_at >= ? ", startDateAtBkk)
		}
		if req.ToCreatedDate != "" {
			endDateAtBkk, err := r.ParseEodBkk(req.ToCreatedDate)
			if err != nil {
				return nil, err
			}
			query = query.Where("transactions.created_at <=  ?", endDateAtBkk)
		}
		if req.TransactionTypeId != nil {
			query = query.Where("transactions.transaction_type_id = ?", req.TransactionTypeId)
		}
		if req.Search != "" {
			search_like := fmt.Sprintf("%%%s%%", req.Search)
			query = query.Where(r.db.Where("transactions.description LIKE ?", search_like).Or("accounts.account_name LIKE ?", search_like).Or("accounts.account_number LIKE ?", search_like))
		}

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Where("transactions.deleted_at IS NULL").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, err
		}
	}

	// End count total records for pagination purposes (without limit and offset) //
	var result model.SuccessWithPagination
	result.List = list
	result.Total = total
	return &result, nil
}

func (r repo) CreateTransaction(data model.BankAccountTransactionBody) error {
	if err := r.db.Table("bank_account_transaction").Create(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdateTransaction(id int64, data model.BankAccountTransactionBody) error {
	if err := r.db.Table("bank_account_transaction").Where("id = ?", id).Updates(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) DeleteTransaction(id int64) error {
	if err := r.db.Table("bank_account_transaction").Where("id = ?", id).Delete(&model.BankAccountTransaction{}).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetTransferById(id int64) (*model.BankAccountTransfer, error) {
	var record model.BankAccountTransfer
	selectedFields := "transfers.id, transfers.from_account_id, transfers.from_bank_id, transfers.from_account_name, transfers.from_account_number"
	selectedFields += ",transfers.to_account_id, transfers.to_bank_id, transfers.to_account_name, transfers.to_account_number"
	selectedFields += ",transfers.amount, transfers.transfer_at, transfers.created_by_username, transfers.status, transfers.confirmed_at, transfers.confirmed_by_admin_id, transfers.created_at, transfers.updated_at"
	selectedFields += ",from_banks.name as from_bank_name, from_banks.code as from_bank_code, from_banks.icon_url as from_bank_icon_url, from_banks.type_flag as from_bank_type_flag"
	selectedFields += ",to_banks.name as to_bank_name, to_banks.code as to_bank_code, to_banks.icon_url as to_bank_icon_url, to_banks.type_flag as to_bank_type_flag"
	if err := r.db.Table("bank_account_transfer as transfers").
		Select(selectedFields).
		Joins("LEFT JOIN bank AS from_banks ON from_banks.id = transfers.from_bank_id").
		Joins("LEFT JOIN bank AS to_banks ON to_banks.id = transfers.to_bank_id").
		Where("transfers.id = ?", id).
		Where("transfers.deleted_at IS NULL").
		First(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetTransfers(req model.BankAccountTransferListRequest) (*model.SuccessWithPagination, error) {

	var list []model.BankAccountTransferResponse
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("bank_account_transfer as transfers")
	count = count.Joins("LEFT JOIN bank AS from_banks ON from_banks.id = transfers.from_bank_id")
	count = count.Joins("LEFT JOIN bank AS to_banks ON to_banks.id = transfers.to_bank_id")
	count = count.Select("transfers.id")
	if req.AccountId != 0 {
		count = count.Where("transfers.from_account_id = ?", req.AccountId)
	}
	if req.FromCreatedDate != "" {
		count = count.Where("transfers.created_at >= ?", req.FromCreatedDate)
	}
	if req.ToCreatedDate != "" {
		count = count.Where("transfers.created_at <= ?", req.ToCreatedDate)
	}
	if req.ToAccountId != 0 {
		count = count.Where("transfers.to_account_id = ?", req.ToAccountId)
	}
	if req.Status != "" {
		count = count.Where("transfers.status = ?", req.Status)
	}
	if req.Search != "" {
		search_like := fmt.Sprintf("%%%s%%", req.Search)
		count = count.Where(r.db.Where("transfers.from_account_name LIKE ?", search_like).Or("transfers.from_account_number LIKE ?", search_like).Or("transfers.to_account_name LIKE ?", search_like).Or("transfers.to_account_number LIKE ?", search_like))
	}
	if err = count.
		Where("transfers.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "transfers.id, transfers.from_account_id, transfers.from_bank_id, transfers.from_account_name, transfers.from_account_number"
		selectedFields += ",transfers.to_account_id, transfers.to_bank_id, transfers.to_account_name, transfers.to_account_number"
		selectedFields += ",transfers.amount, transfers.transfer_at, transfers.created_by_username, transfers.status, transfers.confirmed_at, transfers.confirmed_by_admin_id, transfers.created_at, transfers.updated_at"
		selectedFields += ",from_banks.name as from_bank_name, to_banks.name as to_bank_name"
		query := r.db.Table("bank_account_transfer as transfers")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN bank AS from_banks ON from_banks.id = transfers.from_bank_id")
		query = query.Joins("LEFT JOIN bank AS to_banks ON to_banks.id = transfers.to_bank_id")
		if req.AccountId != 0 {
			query = query.Where("transfers.from_account_id = ?", req.AccountId)
		}
		if req.FromCreatedDate != "" {
			query = query.Where("transfers.created_at >= ?", req.FromCreatedDate)
		}
		if req.ToCreatedDate != "" {
			query = query.Where("transfers.created_at <= ?", req.ToCreatedDate)
		}
		if req.ToAccountId != 0 {
			query = query.Where("transfers.to_account_id = ?", req.ToAccountId)
		}
		if req.Status != "" {
			query = query.Where("transfers.status = ?", req.Status)
		}
		if req.Search != "" {
			search_like := fmt.Sprintf("%%%s%%", req.Search)
			query = query.Where(r.db.Where("transfers.from_account_name LIKE ?", search_like).Or("transfers.from_account_number LIKE ?", search_like).Or("transfers.to_account_name LIKE ?", search_like).Or("transfers.to_account_number LIKE ?", search_like))
		}

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Where("transfers.deleted_at IS NULL").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, err
		}
	}

	// End count total records for pagination purposes (without limit and offset) //
	var result model.SuccessWithPagination
	result.List = list
	result.Total = total
	return &result, nil
}

func (r repo) CreateTransfer(data model.BankAccountTransferBody) error {
	if err := r.db.Table("bank_account_transfer").Create(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) ConfirmTransfer(id int64, data model.BankAccountTransferConfirmBody) error {
	if err := r.db.Table("bank_account_transfer").Where("id = ?", id).Updates(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) DeleteTransfer(id int64) error {
	if err := r.db.Table("bank_account_transfer").Where("id = ?", id).Delete(&model.BankAccountTransfer{}).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) CreateWebhookLog(data model.WebhookLogCreateBody) (*int64, error) {
	if err := r.db.Table("webhook_log").Create(&data).Error; err != nil {
		return nil, err
	}
	return &data.Id, nil
}

func (r repo) UpdateWebhookLog(id int64, data model.WebhookLogUpdateBody) error {
	if err := r.db.Table("webhook_log").Where("id = ?", id).Updates(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetWebhookStatementByExternalId(external_id int64) (*model.BankStatement, error) {

	var record model.BankStatement
	selectedFields := "statements.id, statements.external_id, statements.account_id, statements.detail, statements.statement_type_id, statements.transfer_at, statements.amount, statements.statement_status_id, statements.created_at, statements.updated_at"
	if err := r.db.Table("bank_statement as statements").
		Select(selectedFields).
		Where("statements.external_id = ?", external_id).
		Where("statements.deleted_at IS NULL").
		First(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) CreateWebhookStatement(data model.BankStatementCreateBody) (*int64, error) {

	if err := r.db.Table("bank_statement").Create(&data).Error; err != nil {
		return nil, err
	}
	return &data.Id, nil
}

func (r repo) GetBotaccountConfigs(req model.BotAccountConfigListRequest) (*model.SuccessWithPagination, error) {

	var list []model.BotAccountConfig
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("botaccount_config as configs")
	count = count.Select("configs.id")
	if req.SearchKey != nil {
		count = count.Where("configs.config_key = ?", req.SearchKey)
	}
	if req.SearchValue != nil {
		count = count.Where("configs.config_val = ?", req.SearchValue)
	}
	if err = count.
		Where("configs.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "configs.id, configs.config_key, configs.config_val"
		query := r.db.Table("botaccount_config as configs")
		query = query.Select(selectedFields)
		if req.SearchKey != nil {
			query = query.Where("configs.config_key = ?", req.SearchKey)
		}
		if req.SearchValue != nil {
			query = query.Where("configs.config_val = ?", req.SearchValue)
		}

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Where("configs.deleted_at IS NULL").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, err
		}
	}

	// End count total records for pagination purposes (without limit and offset) //
	var result model.SuccessWithPagination
	result.List = list
	result.Total = total
	return &result, nil
}

func (r repo) GetBotaccountConfigByKey(req model.BotAccountConfigListRequest) (*model.BotAccountConfig, error) {

	var record model.BotAccountConfig
	var err error

	// SELECT //
	selectedFields := "configs.id, configs.config_key, configs.config_val"
	query := r.db.Table("botaccount_config as configs")
	query = query.Select(selectedFields)
	if req.SearchKey != nil {
		query = query.Where("configs.config_key = ?", req.SearchKey)
	}
	if req.SearchValue != nil {
		query = query.Where("configs.config_val = ?", req.SearchValue)
	}
	if err = query.
		Where("configs.deleted_at IS NULL").
		First(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) CreateBotaccountConfig(data model.BotAccountConfigCreateBody) error {
	if err := r.db.Table("botaccount_config").Create(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) DeleteBotaccountConfigByKey(key string) error {
	if err := r.db.Table("botaccount_config").Where("config_key = ?", key).Delete(&model.BankAccountTransfer{}).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) DeleteBotaccountConfigById(id int64) error {
	if err := r.db.Table("botaccount_config").Where("id = ?", id).Delete(&model.BankAccountTransfer{}).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) MatchAutoStatementOwner(id int64, data model.BankStatementUpdateBody) error {
	if err := r.db.Table("bank_statement").Where("id = ?", id).Updates(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) CheckDuplicateDepositFromWeb(find model.BankStatementCreateBody) (total int64, err error) {
	// Truncate the seconds part of the transferAt time to zero
	find.TransferAt = find.TransferAt.Truncate(time.Minute)
	transferAtStr := find.TransferAt.Format("2006-01-02 15:04")

	if err = r.db.Table("bank_statement").
		Where("statement_type_id = ?", model.STATEMENT_STATUS_PENDING).
		Where("statement_status_id = ?", model.STATEMENT_TYPE_TRANSFER_IN).
		Where("from_account_number LIKE ?", "%"+find.FromAccountNumber+"%").
		Where("amount = ?", find.Amount).
		Where("from_bank_id = ?", find.FromBankId).
		Where("transfer_at LIKE ?", "%"+transferAtStr+"%").
		Where("external_id = ?", find.ExternalId).
		Where("deleted_at IS NULL").Count(&total).Error; err != nil {
		return 0, err
	}
	return total, nil
}

func (r repo) CheckUserByBankAccountNumber(bankAccount string, bankId int64) (total int64, err error) {

	if err = r.db.Table("user").
		Where("user.bank_account LIKE ?", "%"+bankAccount+"%").
		Where("user.bank_id = ?", bankId).
		Where("deleted_at IS NULL").Count(&total).Error; err != nil {
		return 0, err
	}
	return total, nil
}

func (r repo) FindMoveTransaction(checkAccount model.CheckDepositAccountMoveTransaction) (total int64, err error) {

	query := r.db.Table("account_move_transaction")
	query = query.Joins("LEFT JOIN bank_account AS accounts ON accounts.id = account_move_transaction.from_account_id")
	query = query.Where("accounts.account_number LIKE ?", "%"+checkAccount.FromAccountNumber+"%")
	// ทำเพราะ ออมสิน ไม่มี บอกว่า ธนาคารไหนโอนเข้ามาให้
	if checkAccount.ToAccountBankId != model.BANK_ID_GSB {
		query = query.Where("accounts.bank_id = ?", checkAccount.FromAccountBankId)
	}
	query = query.Where("to_account_id = ?", checkAccount.ToAccountId)
	if err = query.Count(&total).Error; err != nil {
		return 0, err
	}

	return total, nil
}

func (r repo) UpdateBankAccountPriority(list model.PriorityWithdrawstructRequest) error {

	for _, v := range list.List {
		if err := r.db.Table("bank_account_priority").Where("id = ?", v.Id).Updates(&v).Error; err != nil {
			return err
		}
	}
	return nil
}

func (r repo) GetWebBankDepositAccount() ([]model.WebBankAccountResponse, error) {

	var list []model.WebBankAccountResponse

	selectedFields := "accounts.id as id ,banks.name as bank_name, banks.icon_url as bank_icon_url, accounts.account_name, accounts.account_number"
	selectedFields += ", accounts.bank_id as bank_id, banks.code as bank_code"
	selectedFields += ", banks.country_code as country_code"
	selectedFields += ", banks.use_currency as use_currency"
	selectedFields += ", accounts.sms_mode as sms_mode"
	selectedFields += ", accounts.image_url as image_url"
	selectedFields += ", accounts.show_bank_deposit_over_due_time as show_bank_deposit_over_due_time"
	selectedFields += ", accounts.show_bank_deposit_max_due_time as show_bank_deposit_max_due_time"

	query := r.db.Table("bank_account as accounts")
	query = query.Select(selectedFields)
	query = query.Joins("LEFT JOIN bank AS banks ON banks.id = accounts.bank_id")
	query = query.Where("accounts.account_type_id in (?,?)", model.BANK_ACCOUNT_TYPE_DEPOSIT_ONLY, model.BANK_ACCOUNT_TYPE_BOTH)
	query = query.Where("accounts.is_show_front = ?", true)
	query = query.Where("accounts.deleted_at IS NULL")
	if err := query.Scan(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (r repo) GetActiveDepositBankAccountList() ([]model.WebBankAccountResponse, error) {

	var list []model.WebBankAccountResponse
	selectedFields := "accounts.id as id ,banks.name as bank_name, banks.icon_url as bank_icon_url, accounts.account_name, accounts.account_number"
	selectedFields += ", accounts.bank_id as bank_id, banks.code as bank_code"
	selectedFields += ", accounts.sms_mode as sms_mode"
	query := r.db.Table("bank_account as accounts")
	query = query.Select(selectedFields)
	query = query.Joins("LEFT JOIN bank AS banks ON banks.id = accounts.bank_id")
	query = query.Where("accounts.account_type_id in (?,?)", model.BANK_ACCOUNT_TYPE_DEPOSIT_ONLY, model.BANK_ACCOUNT_TYPE_BOTH)
	query = query.Where("accounts.connection_status_id = ?", model.CONNECTION_CONNECTED)
	query = query.Where("accounts.deleted_at IS NULL")
	if err := query.Scan(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (r repo) GetBankAccountWithSumWithdrawCreditAmount() ([]model.BankAccountWithdrawAndSumAmount, error) {
	var record []model.BankAccountWithdrawAndSumAmount
	now := time.Now().UTC().Add(7 * time.Hour)

	// Calculate the start and end times for the range (00:00:00 to 23:59:59) adjusted by 7 hours.
	startTimeUTCMinus7 := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()).Add(-7 * time.Hour)
	endTimeUTCMinus7 := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, *********, now.Location()).Add(-7 * time.Hour)

	selectedFields := "bank_transaction.from_account_number AS from_account_number, SUM(bank_transaction.credit_amount) AS sum_amount"

	if err := r.db.Table("bank_transaction").
		Select(selectedFields).
		Where("bank_transaction.transaction_type_id = ?", model.TRANSACTION_TYPE_WITHDRAW).
		Where("bank_transaction.transfer_at >= ?", startTimeUTCMinus7).
		Where("bank_transaction.transfer_at <= ?", endTimeUTCMinus7).
		Where("bank_transaction.transaction_status_id IN (?,?)", model.TRANS_STATUS_WITHDRAW_SUCCESS, model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED).
		Group("bank_transaction.from_account_number").
		Scan(&record).
		Error; err != nil {
		return nil, err
	}

	return record, nil
}
func (r repo) GetBankAccountWithSumWithdrawCreditAmountByAccount(bankAccountList []string) ([]model.BankAccountWithdrawAndSumAmount, error) {
	var record []model.BankAccountWithdrawAndSumAmount
	now := time.Now().UTC().Add(7 * time.Hour)

	// Calculate the start and end times for the range (00:00:00 to 23:59:59) adjusted by 7 hours.
	startTimeUTCMinus7 := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()).Add(-7 * time.Hour)
	endTimeUTCMinus7 := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, *********, now.Location()).Add(-7 * time.Hour)

	selectedFields := "bank_transaction.from_account_number AS from_account_number,  COALESCE(SUM(bank_transaction.credit_amount), 0) AS sum_amount"

	if err := r.db.Table("bank_transaction").
		Select(selectedFields).
		Where("bank_transaction.transaction_type_id = ?", model.TRANSACTION_TYPE_WITHDRAW).
		Where("bank_transaction.transfer_at >= ?", startTimeUTCMinus7).
		Where("bank_transaction.transfer_at <= ?", endTimeUTCMinus7).
		Where("bank_transaction.transaction_status_id IN (?,?)", model.TRANS_STATUS_WITHDRAW_SUCCESS, model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED).
		Where("bank_transaction.from_account_number IN (?)", bankAccountList).
		Group("bank_transaction.from_account_number").
		Scan(&record).
		Error; err != nil {
		return nil, err
	}

	for _, accountNumber := range bankAccountList {
		found := false
		for _, recordItem := range record {
			if recordItem.FromAccountNumber == accountNumber {
				found = true
				break
			}
		}

		if !found {
			record = append(record, model.BankAccountWithdrawAndSumAmount{
				FromAccountNumber: accountNumber,
				SumAmount:         0,
			})
		}
	}

	return record, nil
}

func (r repo) GetBankAccountWithSumDepostCreditAmount() ([]model.BankAccounDepositAndSumAmount, error) {
	var record []model.BankAccounDepositAndSumAmount
	now := time.Now().UTC().Add(7 * time.Hour)

	// Calculate the start and end times for the range (00:00:00 to 23:59:59) adjusted by 7 hours.
	startTimeUTCMinus7 := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()).Add(-7 * time.Hour)
	endTimeUTCMinus7 := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, *********, now.Location()).Add(-7 * time.Hour)

	selectedFields := "bank_transaction.to_account_number AS to_account_number, SUM(bank_transaction.credit_amount) AS sum_amount"

	if err := r.db.Table("bank_transaction").
		Select(selectedFields).
		Where("bank_transaction.transaction_type_id = ?", model.TRANSACTION_TYPE_DEPOSIT).
		Where("bank_transaction.transfer_at >= ?", startTimeUTCMinus7).
		Where("bank_transaction.transfer_at <= ?", endTimeUTCMinus7).
		Where("bank_transaction.transaction_status_id IN (?,?)", model.TRANS_STATUS_WITHDRAW_SUCCESS, model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED).
		Group("bank_transaction.to_account_number").
		Scan(&record).
		Error; err != nil {
		return nil, err
	}

	return record, nil
}

func (r repo) GetBankAccountWithSumDepostCreditAmountByAccount(bankAccountList []string) ([]model.BankAccounDepositAndSumAmount, error) {
	var record []model.BankAccounDepositAndSumAmount
	now := time.Now().UTC().Add(7 * time.Hour)

	// Calculate the start and end times for the range (00:00:00 to 23:59:59) adjusted by 7 hours.
	startTimeUTCMinus7 := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()).Add(-7 * time.Hour)
	endTimeUTCMinus7 := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, *********, now.Location()).Add(-7 * time.Hour)

	selectedFields := "bank_transaction.to_account_number AS to_account_number, SUM(bank_transaction.credit_amount) AS sum_amount"

	if err := r.db.Table("bank_transaction").
		Select(selectedFields).
		Where("bank_transaction.transaction_type_id = ?", model.TRANSACTION_TYPE_DEPOSIT).
		Where("bank_transaction.transfer_at >= ?", startTimeUTCMinus7).
		Where("bank_transaction.transfer_at <= ?", endTimeUTCMinus7).
		Where("bank_transaction.transaction_status_id IN (?,?)", model.TRANS_STATUS_WITHDRAW_SUCCESS, model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED).
		Where("bank_transaction.to_account_number IN (?)", bankAccountList).
		Group("bank_transaction.to_account_number").
		Scan(&record).
		Error; err != nil {
		return nil, err
	}

	for _, accountNumber := range bankAccountList {
		found := false
		for _, recordItem := range record {
			if recordItem.ToAccountNumber == accountNumber {
				found = true
				break
			}
		}

		if !found {
			record = append(record, model.BankAccounDepositAndSumAmount{
				ToAccountNumber: accountNumber,
				SumAmount:       0,
			})
		}
	}

	return record, nil
}

func (r repo) GetBankAccountWithWithdrawSumCreditAmountByAccountNumber(bankAccount string) (*model.BankAccountWithdrawAndSumAmount, error) {
	var record *model.BankAccountWithdrawAndSumAmount

	// Get the current time in your local time zone (UTC+7).
	now := time.Now().UTC().Add(7 * time.Hour)

	// Calculate the start and end times for the range (00:00:00 to 23:59:59) adjusted by 7 hours.
	startTimeUTCMinus7 := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()).Add(-7 * time.Hour)
	endTimeUTCMinus7 := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, *********, now.Location()).Add(-7 * time.Hour)

	selectedFields := "bank_transaction.from_account_number AS from_account_number, SUM(bank_transaction.credit_amount) AS sum_amount"

	if err := r.db.Table("bank_transaction").
		Select(selectedFields).
		Where("bank_transaction.transaction_type_id = ?", model.TRANSACTION_TYPE_WITHDRAW).
		Where("bank_transaction.from_account_number = ?", bankAccount).
		Where("bank_transaction.transfer_at >= ?", startTimeUTCMinus7).
		Where("bank_transaction.transfer_at <= ?", endTimeUTCMinus7).
		Where("bank_transaction.transaction_status_id IN (?,?)", model.TRANS_STATUS_WITHDRAW_SUCCESS, model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED).
		Group("bank_transaction.from_account_number").
		Scan(&record).
		Error; err != nil {
		return nil, err
	}

	return record, nil
}

func (r repo) SortBankAccountList(req model.DragSortRequest) error {

	var results []model.PriorityBankAccountResponse

	selectedFields := "sort_rows.id, sort_rows.account_priority_withdraw"
	query := r.db.Table("bank_account as sort_rows")
	query = query.Select(selectedFields)
	if err := query.
		Where("sort_rows.id IN ?", []int64{req.FromItemId, req.ToItemId}).
		Limit(2).
		Find(&results).
		Error; err != nil {
		return err
	}

	var fromItem *model.PriorityBankAccountResponse
	var toItem *model.PriorityBankAccountResponse
	for _, result := range results {
		if result.Id == req.FromItemId {
			fromItem = &model.PriorityBankAccountResponse{
				Id:                      result.Id,
				AccountPriorityWithdraw: result.AccountPriorityWithdraw,
			}
		} else if result.Id == req.ToItemId {

			toItem = &model.PriorityBankAccountResponse{
				Id:                      result.Id,
				AccountPriorityWithdraw: result.AccountPriorityWithdraw,
			}
		}
	}

	if fromItem != nil && toItem != nil {
		// Sort Direction //
		if fromItem.AccountPriorityWithdraw < toItem.AccountPriorityWithdraw {
			// Drag down  //
			whereShiftDown := r.db.Where("account_priority_withdraw > ?", fromItem.AccountPriorityWithdraw).Where("account_priority_withdraw <= ?", toItem.AccountPriorityWithdraw)
			if err := r.db.Table("bank_account").Where(whereShiftDown).Update("account_priority_withdraw", gorm.Expr("account_priority_withdraw - 1")).Error; err != nil {
				return err
			}
			// Go to
			if err := r.db.Table("bank_account").Where("id = ?", fromItem.Id).Update("account_priority_withdraw", toItem.AccountPriorityWithdraw).Error; err != nil {
				return err
			}
		} else if fromItem.AccountPriorityWithdraw > toItem.AccountPriorityWithdraw {
			// Drag up = shift up //
			whereShiftDown := r.db.Where("account_priority_withdraw < ?", fromItem.AccountPriorityWithdraw).Where("account_priority_withdraw >= ?", toItem.AccountPriorityWithdraw)
			if err := r.db.Table("bank_account").Where(whereShiftDown).Update("account_priority_withdraw", gorm.Expr("account_priority_withdraw + 1")).Error; err != nil {
				return err
			}
			// Go to
			if err := r.db.Table("bank_account").Where("id = ?", fromItem.Id).Update("account_priority_withdraw", toItem.AccountPriorityWithdraw).Error; err != nil {
				return err
			}
		}
	}
	return nil
}

func (r repo) CheckWebhookStatementDuplicateWithRecord(account string, fromBankId int64) (*model.BankStatement, error) {

	var record model.BankStatement
	selectedFields := "statements.id, statements.external_id, statements.account_id, statements.detail, statements.statement_type_id, statements.transfer_at, statements.amount, statements.statement_status_id, statements.created_at, statements.updated_at"
	if err := r.db.Table("bank_statement as statements").
		Select(selectedFields).
		Where("statements.detail LIKE ?", "%"+account+"%").
		Where("statements.from_bank_id = ?", fromBankId).
		Where("statements.external_id IS NULL").
		Where("statements.deleted_at IS NULL").
		Order("statements.id DESC").
		First(&record).
		Error; err != nil {
		return nil, err
	}

	return &record, nil
}

func (r repo) CreateBankTransactionLog(data model.BankTransactionLogCreate) (*int64, error) {
	if err := r.db.Table("bank_transaction_log").Create(&data).Error; err != nil {
		return nil, err
	}
	return &data.Id, nil
}

func (r repo) SuccessLog(name string, req interface{}, result interface{}) error {

	var createLog model.BankTransactionLogCreate
	createLog.Status = "SUCCESS"
	createLog.JsonRequest = helper.StructJson(req)
	createLog.JsonPayload = helper.StructJson(result)
	createLog.LogType = name
	if err := r.db.Table("bank_transaction_log").Create(&createLog).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) ErrorLog(name string, req interface{}, result interface{}) error {

	var createLog model.BankTransactionLogCreate
	createLog.Status = "ERROR"
	createLog.JsonRequest = helper.StructJson(req)
	createLog.JsonPayload = helper.StructJson(result)
	createLog.LogType = name
	if err := r.db.Table("bank_transaction_log").Create(&createLog).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdateBankTransactionLog(id int64, data model.BankTransactionLogBody) error {
	if err := r.db.Table("bank_transaction_log").Where("id = ?", id).Updates(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) CheckDuplicateWebhookAndAdminRecord(req model.CheckDuplicateWebhookAndAdminRecord) (*model.BankTransaction, error) {

	var record model.BankTransaction
	selectedFields := "transactions.id"

	statementTimingLenght := 3 * 60 * time.Minute

	query := r.db.Table("bank_transaction as transactions")
	query = query.Select(selectedFields)
	query = query.Where("transactions.credit_amount = ?", req.Amount)
	query = query.Where("transactions.from_bank_id = ?", req.FromBankId)
	query = query.Where("transactions.transaction_type_id = ?", model.TRANSACTION_TYPE_DEPOSIT)

	if req.CheckFromWhere == "ADMIN" {
		fromBankAccount := req.FromAccountNumber
		if len(fromBankAccount) > 4 {
			fromBankAccount = fromBankAccount[len(fromBankAccount)-4:]
		}
		query = query.Where("transactions.transfer_at >= ?", req.TransactionAt.Add(-2*time.Minute))
		query = query.Where("transactions.transfer_at <= ?", req.TransactionAt.Add(2*time.Minute))
		query = query.Where("transactions.from_account_number LIKE ?", "%"+fromBankAccount+"%")
		query = query.Where("transactions.transaction_status_id = ?", model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED)
		query = query.Where("transactions.member_code = ?", req.MemberCode)
	} else {
		// from webhook
		// [********] Confirm by P.lay SCB Set -1 day when 23.00 - 00.00 but do it to set the whole data before save in db
		// transactionTimeBkk := req.TransactionAt.Add(7 * time.Hour)
		// if req.ToBankId != nil && *req.ToBankId == model.BANK_ID_SCB && transactionTimeBkk.Hour() >= 23 && transactionTimeBkk.Hour() < 1 {
		// 	scbTransactionDate := req.TransactionAt.Add(-24 * time.Hour)
		// 	query = query.Where("transactions.transfer_at >= ? OR transactions.transfer_at >= ?", scbTransactionDate.Add(-1*statementTimingLenght), req.TransactionAt.Add(-1*statementTimingLenght))
		// 	query = query.Where("transactions.transfer_at <= ? OR transactions.transfer_at <= ?", scbTransactionDate.Add(statementTimingLenght), req.TransactionAt.Add(statementTimingLenght))
		// } else {
		query = query.Where("transactions.transfer_at >= ?", req.TransactionAt.Add(-1*statementTimingLenght))
		query = query.Where("transactions.transfer_at <= ?", req.TransactionAt.Add(statementTimingLenght))
		// }
		query = query.Where("transactions.from_account_number LIKE ?", "%"+req.FromAccountNumber+"%")
		query = query.Where("transactions.member_code = ?", req.MemberCode)
		// add check slip [********] เกิดจาด slip มาก่อน
		query = query.Where("(transactions.created_by_admin_id > ? OR (transactions.created_by_admin_id >= ? AND transactions.deposit_channel = 'SLIP'))", 0, 0)

		query = query.Where("transactions.external_match_id IS NULL")

	}

	if err := query.
		Where("transactions.deleted_at IS NULL").
		Find(&record).
		Error; err != nil {
		return nil, err
	}

	return &record, nil
}

func (r repo) CheckDuplicateWebhookAndAdminRecord2(req model.CheckDuplicateWebhookAndAdminRecord) (*model.BankTransaction, error) {

	var record []model.BankTransaction
	selectedFields := "transactions.id, transactions.from_account_number"

	statementTimingLenght := 2 * time.Minute

	query := r.db.Table("bank_transaction as transactions")
	query = query.Select(selectedFields)
	query = query.Where("transactions.credit_amount = ?", req.Amount)
	query = query.Where("transactions.from_bank_id = ?", req.FromBankId)
	query = query.Where("transactions.transaction_type_id = ?", model.TRANSACTION_TYPE_DEPOSIT)

	if req.CheckFromWhere == "ADMIN" {
		query = query.Where("transactions.transfer_at >= ?", req.TransactionAt.Add(-1*statementTimingLenght))
		query = query.Where("transactions.transfer_at <= ?", req.TransactionAt.Add(statementTimingLenght))
		// query = query.Where("transactions.from_account_number LIKE ?", "%"+fromBankAccount+"%")
		query = query.Where("transactions.transaction_status_id = ?", model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED)
		query = query.Where("transactions.member_code = ?", req.MemberCode)
	} else {
		// 2024/11/11 change Admin fault
		query = query.Where("transactions.transfer_at >= ?", req.TransactionAt.Add(-1*statementTimingLenght))
		query = query.Where("transactions.transfer_at <= ?", req.TransactionAt.Add(statementTimingLenght))
		// query = query.Where("transactions.from_account_number LIKE ?", "%"+req.FromAccountNumber+"%")
		query = query.Where("transactions.member_code = ?", req.MemberCode)
		query = query.Where("(transactions.created_by_admin_id > ? OR (transactions.created_by_admin_id >= ? AND transactions.deposit_channel = 'SLIP'))", 0, 0)
		query = query.Where("transactions.external_match_id IS NULL")
	}

	if err := query.
		Where("transactions.deleted_at IS NULL").
		Scan(&record).
		Error; err != nil {
		return nil, err
	}

	// SLOW SQL
	var response model.BankTransaction
	if len(record) > 0 {
		var fromBankAccount string
		if req.CheckFromWhere == "ADMIN" {
			fromBankAccount = req.FromAccountNumber
			if len(fromBankAccount) > 4 {
				fromBankAccount = fromBankAccount[len(fromBankAccount)-4:]
			}
		} else {
			fromBankAccount = req.FromAccountNumber
		}

		for _, recordItem := range record {
			if strings.Contains(recordItem.FromAccountNumber, fromBankAccount) {
				response = recordItem
				break
			}
		}
		return &response, nil
	}

	return &response, nil
}

func (r repo) UpdateTransactionDuplicateWithExternalMatch(id int64, statementId int64) error {

	// if err := r.db.Table("bank_transaction").Where("id = ?", id).Where("external_match_id IS NULL").Update("external_match_id", statementId).Error; err != nil {
	// 	return err
	// }

	// case slip and unmatch list need to also update statement_id
	if err := r.db.Table("bank_transaction").Where("id = ?", id).Updates(map[string]interface{}{"external_match_id": statementId, "statement_id": statementId}).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetSumMoveTransactionFromAccountToDay() ([]model.GetSumMoveTransactionFromAccountToDayResponse, error) {
	var record []model.GetSumMoveTransactionFromAccountToDayResponse
	now := time.Now().UTC().Add(7 * time.Hour)

	startTimeUTCMinus7 := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()).Add(-7 * time.Hour)
	endTimeUTCMinus7 := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, *********, now.Location()).Add(-7 * time.Hour)

	selectedFields := "SUM(account_move_transaction.amount) AS total_amount, account_move_transaction.from_account_id AS from_bank_id"

	if err := r.db.Table("account_move_transaction").
		Select(selectedFields).
		Where("account_move_transaction.is_success = ?", 1).
		Where("account_move_transaction.transfer_at >= ?", startTimeUTCMinus7).
		Where("account_move_transaction.transfer_at <= ?", endTimeUTCMinus7).
		Group("account_move_transaction.from_account_id").
		Scan(&record).
		Error; err != nil {
		return nil, err
	}

	return record, nil
}

func (r repo) GetSumMoveTransactionToAccountToDay() ([]model.GetSumMoveTransactionToAccountToDayResponse, error) {
	var record []model.GetSumMoveTransactionToAccountToDayResponse
	now := time.Now().UTC().Add(7 * time.Hour)

	startTimeUTCMinus7 := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()).Add(-7 * time.Hour)
	endTimeUTCMinus7 := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, *********, now.Location()).Add(-7 * time.Hour)

	selectedFields := "SUM(account_move_transaction.amount) AS total_amount, account_move_transaction.to_account_id AS to_account_id"

	if err := r.db.Table("account_move_transaction").
		Select(selectedFields).
		Where("account_move_transaction.is_success = ?", 1).
		Where("account_move_transaction.transfer_at >= ?", startTimeUTCMinus7).
		Where("account_move_transaction.transfer_at <= ?", endTimeUTCMinus7).
		Group("account_move_transaction.to_account_id").
		Scan(&record).
		Error; err != nil {
		return nil, err
	}

	return record, nil
}

func (r repo) GetBankAccountOnlyTrueAccountForWithdraw() (*model.BankAccount, error) {

	var record model.BankAccount
	var err error
	// SELECT //
	query := r.db.Table("bank_account AS accounts")
	selectedFields := "accounts.id, accounts.bank_id, accounts.account_type_id, accounts.account_name, accounts.account_number, accounts.account_balance, accounts.device_uid, accounts.pin_code"
	selectedFields += ", accounts.last_conn_update_at, accounts.created_at, accounts.updated_at, accounts.is_show_front as is_show_front"
	selectedFields += ", banks.name as bank_name, banks.code as bank_code, banks.icon_url as bank_icon_url, banks.type_flag"
	selectedFields += ", account_types.name as account_type_name, account_types.limit_flag"
	selectedFields += ", accounts.connection_status_id, connection_status.name as connection_status_name, accounts.account_priority_withdraw as account_priority_withdraw"
	selectedFields += ", accounts.auto_withdraw_type_id as auto_withdraw_type_id"
	query = query.Select(selectedFields)
	query = query.Joins("LEFT JOIN bank AS banks ON banks.id = accounts.bank_id")
	query = query.Joins("LEFT JOIN bank_account_type AS account_types ON account_types.id = accounts.account_type_id")
	query = query.Joins("LEFT JOIN connection_status ON connection_status.id = accounts.connection_status_id")
	if err = query.
		Where("accounts.deleted_at IS NULL").
		// Where("accounts.connection_status_id = ?", model.CONNECTION_CONNECTED).
		Where("accounts.bank_id = ?", model.BANK_ID_TRUE).
		Where("accounts.account_type_id in (?,?)", model.BANK_ACCOUNT_TYPE_WITHDRAW_ONLY, model.BANK_ACCOUNT_TYPE_BOTH).
		Order("accounts.account_priority_withdraw ASC").
		Scan(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) ExternalCheckWithdrawTransaction(isTrueMoney bool, req model.ExternalCheckWithdrawTransaction) (*model.GetBankTransactionWithdrawListResponse, error) {

	var record model.GetBankTransactionWithdrawListResponse
	selectedFields := "bank_transaction.id AS id, bank_transaction.statement_id AS statement_id, bank_transaction.promotion_id AS promotion_id, bank_transaction.credit_amount AS credit_amount, bank_transaction.credit_back AS credit_back, bank_transaction.deposit_channel AS deposit_channel, bank_transaction.over_amount AS over_amount, bank_transaction.bonus_amount AS bonus_amount"
	selectedFields += ", bank_transaction.from_account_id AS from_account_id, bank_transaction.from_bank_id AS from_bank_id, bank_transaction.from_account_number AS from_account_number,from_bank.name AS from_bank_name"
	selectedFields += ", bank_transaction.is_auto_credit AS is_auto_credit, bank_transaction.transfer_at AS transfer_at"
	selectedFields += ", bank_transaction.slip_img_url"
	selectedFields += ", user.id AS user_id, user.member_code AS user_member_code, user.fullname AS user_fullname,user.username AS username"
	selectedFields += ", transaction_type.id AS transaction_type_id, transaction_type.label_th AS transaction_type_th, transaction_type.label_en AS transaction_type_en"
	selectedFields += ", bank_account.id AS to_account_id, bank_transaction.to_bank_id AS to_bank_id, to_bank.name AS to_bank_name,user.fullname AS to_account_name, bank_transaction.to_account_number AS to_account_number"
	selectedFields += ", transaction_status.id AS transaction_status_id, transaction_status.label_th AS transaction_status_th, transaction_status.label_en AS transaction_status_en"
	selectedFields += ", bank_transaction.confirmed_at AS confirmed_at, bank_transaction.confirmed_by_admin_id AS confirmed_by_admin_id"
	selectedFields += ", from_bank.icon_url AS from_bank_icon_url, to_bank.icon_url AS to_bank_icon_url"
	selectedFields += ", bank_transaction.cancel_remark AS cancel_remark"
	selectedFields += ", CASE WHEN bank_transaction.created_by_admin_id = 0 THEN 'อัตโนมัติ' ELSE admincreate.username END AS created_by_username"
	selectedFields += ", CASE WHEN bank_transaction.confirmed_by_admin_id = 0 THEN 'อัตโนมัติ' ELSE adminconfirmed.username END AS confirmed_by_username"
	selectedFields += ", bank_transaction.after_amount AS after_amount"
	req.Amount = math.Abs(req.Amount)

	query := r.db.Table("bank_transaction")
	query = query.Joins("LEFT JOIN user ON user.id = bank_transaction.user_id")
	query = query.Joins("LEFT JOIN transaction_type ON transaction_type.id = bank_transaction.transaction_type_id")
	query = query.Joins("LEFT JOIN bank_account ON bank_account.id = bank_transaction.to_account_id")
	query = query.Joins("LEFT JOIN bank AS to_bank ON to_bank.id = bank_transaction.to_bank_id")
	query = query.Joins("LEFT JOIN bank AS from_bank ON from_bank.id = bank_transaction.from_bank_id")
	query = query.Joins("LEFT JOIN transaction_status ON transaction_status.id = bank_transaction.transaction_status_id")
	query = query.Joins("LEFT JOIN admin ON admin.id = bank_transaction.created_by_admin_id")
	query = query.Joins("LEFT JOIN admin AS admincreate ON admincreate.id = bank_transaction.created_by_admin_id")
	query = query.Joins("LEFT JOIN admin AS adminconfirmed ON adminconfirmed.id = bank_transaction.confirmed_by_admin_id")
	query = query.Joins("JOIN bank_transaction_external_detail ON bank_transaction_external_detail.bank_transaction_id = bank_transaction.id")

	query = query.Select(selectedFields)
	query = query.Where("bank_transaction_external_detail.error_code != ?", 0)
	//******** confirm isTrueMoney by p.lay
	if isTrueMoney {
		query = query.Where("bank_transaction.to_account_number LIKE ?", "%"+req.ToAccountNumber+"%")
	} else {
		query = query.Where("bank_transaction.to_bank_id = ?", req.ToBankId)
		query = query.Where("bank_transaction.to_account_number LIKE ?", "%"+req.ToAccountNumber+"%")
	}
	query = query.Where("bank_transaction.credit_amount = ?", req.Amount)
	// ใช้ เวลา หาไม่ได้ เลย เป็น desc แทน
	// ******** กลับมาใช้เวลา P.lay confirm
	query = query.Where("bank_transaction.confirmed_at>= ?", req.TransactionAt.Add(-1*time.Minute))
	query = query.Where("bank_transaction.confirmed_at <= ?", req.TransactionAt.Add(2*time.Minute))
	query = query.Where("bank_transaction.from_account_id = ?", req.FromAccountId)
	query = query.Where("bank_transaction.external_match_id IS NULL")
	query = query.Where("bank_transaction.transaction_type_id = ?", model.TRANSACTION_TYPE_WITHDRAW)

	if err := query.
		Where("bank_transaction.deleted_at IS NULL").
		Order("bank_transaction.transfer_at ASC").
		First(&record).
		Error; err != nil {
		return nil, err
	}

	return &record, nil
}

func (r repo) CronjobDeleteBankTransactionLog() error {

	CurrentDate := time.Now().UTC().Add(time.Hour * 7).Format("2006-01-02")
	startDateAtBkk, err := r.ParseBodBkk(CurrentDate)
	if err != nil {
		return err
	}

	if err := r.db.Unscoped().Table("bank_transaction_log").Where("DATEDIFF(?, created_at) > 30", startDateAtBkk).Delete(&model.BankTransactionLog{}).Error; err != nil {
		return err
	}

	return nil
}
func (r repo) CronjobDeleteWebHookLog() error {

	CurrentDate := time.Now().UTC().Add(time.Hour * 7).Format("2006-01-02")
	startDateAtBkk, err := r.ParseBodBkk(CurrentDate)
	if err != nil {
		return err
	}

	if err := r.db.Unscoped().Table("webhook_log").Where("DATEDIFF(?, created_at) > 30", startDateAtBkk).Delete(&model.WebhookLog{}).Error; err != nil {
		return err
	}

	return nil
}

func (r repo) GetBankTransactionSummaryGraph2(req model.BankTransactionGraph2Request) (*model.BankTransactionGraph2Response, error) {

	var result model.BankTransactionGraph2Response
	var graph1 []model.BankTransGraphItem
	var graph2 []model.UserTransGraphItem

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return &result, err
	}

	// GRAPH 1
	// SUM type deposit(1), first deposit(1 with flag), bonus(3,4,5,6,7,8,9,10,11) and withdraw(2)
	// Group by transfer_at

	bonusTransactionType := []int{
		// model.CREDIT_TYPE_DEPOSIT,
		// model.CREDIT_TYPE_WITHDRAW,
		model.CREDIT_TYPE_BONUS,
		model.CREDIT_TYPE_PROMOTION_RETURN_LOSS,
		model.CREDIT_TYPE_AFFILIATE_INCOME,
		model.CREDIT_TYPE_ALLIANCE_INCOME,
		// model.CREDIT_TYPE_TAKE_CREDIT_BACK,
		model.CREDIT_TYPE_DAILY_ACTIVITY_BONUS,
		model.CREDIT_TPYE_LUCKY_WHEEL,
		model.CREDIT_TYPE_PROMOTION_WEB,
		model.CREDIT_TYPE_COUPON_CASH,
		model.CREDIT_TYPE_LOTTERY,
		model.CREDIT_TYPE_PROMOTION_RETURN_TURN,
		// model.CREDIT_TYPE_CANCEL_CREDIT,
	}
	selectedFields := "DATE(CONVERT_TZ(tb_log.transfer_at,'+00:00','+07:00')) AS of_date"
	selectedFields += ", SUM(CASE WHEN tb_log.type_id = 1 THEN tb_log.credit_amount ELSE 0 END) AS deposit_amount"
	// selectedFields += ", SUM(CASE WHEN type_id = 1 AND is_first_deposit = 1 THEN tb_log.credit_amount ELSE 0 END) AS first_deposit_amount"
	selectedFields += ", SUM(CASE WHEN tb_log.type_id IN (?) THEN tb_log.bonus_amount ELSE 0 END) AS bonus_amount"
	selectedFields += ", SUM(CASE WHEN tb_log.type_id = 2 THEN tb_log.credit_amount ELSE 0 END) AS withdraw_amount"

	sql := r.db.Table("user_transaction AS tb_log").Select(selectedFields, bonusTransactionType)
	sql = sql.Where("tb_log.is_show = ?", true)
	sql = sql.Where("tb_log.removed_at IS NULL")

	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return &result, err
		}
		sql = sql.Where("tb_log.transfer_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return &result, err
		}
		sql = sql.Where("tb_log.transfer_at <=  ?", endDateAtBkk)
	}
	sql = sql.Group("of_date").Order("of_date ASC")
	if err := sql.Scan(&graph1).Error; err != nil {
		return &result, err
	}

	// Saparate first deposit query (need to JOIN with bank_transaction)
	var totalDepositData []struct {
		OfDate             string
		FirstDepositAmount float64
	}

	selectedFields2 := "DATE(CONVERT_TZ(tb_log.transfer_at,'+00:00','+07:00')) AS of_date"
	selectedFields2 += ", SUM(tb_log.credit_amount) AS first_deposit_amount"

	firstDepQuery := r.db.Table("user_transaction AS tb_log")
	firstDepQuery = firstDepQuery.Select(selectedFields2)
	firstDepQuery = firstDepQuery.Joins("JOIN bank_transaction AS tb_bank_tb_log ON tb_bank_tb_log.id = tb_log.ref_id")
	firstDepQuery = firstDepQuery.Where("tb_log.type_id = ?", 1)
	firstDepQuery = firstDepQuery.Where("tb_bank_tb_log.is_first_deposit = ?", true)
	firstDepQuery = firstDepQuery.Where("tb_log.is_show = ?", true)
	firstDepQuery = firstDepQuery.Where("tb_log.removed_at IS NULL")
	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return &result, err
		}
		firstDepQuery = firstDepQuery.Where("tb_log.transfer_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return &result, err
		}
		firstDepQuery = firstDepQuery.Where("tb_log.transfer_at <=  ?", endDateAtBkk)
	}
	firstDepQuery = firstDepQuery.Group("of_date")
	if err := firstDepQuery.Scan(&totalDepositData).Error; err != nil {
		return &result, err
	}

	// Append to graph 1 by of_date
	for _, v := range totalDepositData {
		for i, v2 := range graph1 {
			if v2.OfDate == v.OfDate {
				graph1[i].FirstDepositAmount = v.FirstDepositAmount
				break
			}
		}
	}

	// GRAPH 2 + data4 with new user
	// Group by
	// active = last_action_at
	// new = created_at

	// fix to use from  user_login_log instead

	// 	CREATE TABLE IF NOT EXISTS `user_login_log` (
	//   `id` bigint NOT NULL AUTO_INCREMENT,
	//   `user_id` bigint DEFAULT NULL,
	//   `ip` varchar(255) DEFAULT NULL,
	//   `created_at` datetime DEFAULT (now()),
	//   PRIMARY KEY (`id`),
	//   KEY `idx_created_at` (`created_at`),
	//   KEY `idx_user_id` (`user_id`)
	// );

	// selectedFields3 := "DATE(CONVERT_TZ(user_login_log.created_at,'+00:00','+07:00')) AS of_date"
	// 	CREATE TABLE IF NOT EXISTS `user_login_log` (
	//   `id` bigint NOT NULL AUTO_INCREMENT,
	//   `user_id` bigint DEFAULT NULL,
	//   `ip` varchar(255) DEFAULT NULL,
	//   `created_at` datetime DEFAULT (now()),
	//   PRIMARY KEY (`id`),
	//   KEY `idx_created_at` (`created_at`),
	//   KEY `idx_user_id` (`user_id`)
	// );

	// selectedFields3 := "DATE(CONVERT_TZ(user_login_log.created_at,'+00:00','+07:00')) AS of_date"
	selectedFields3 := "DATE(CONVERT_TZ(user_login_log.created_at,'+00:00','+07:00')) AS of_date"
	selectedFields3 += ", COUNT(DISTINCT user_login_log.user_id) AS active_user_count"

	sql3 := r.db.Table("user_login_log").Select(selectedFields3)
	sql3 = sql3.Where("user_login_log.created_at IS NOT NULL")
	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return &result, err
		}
		sql3 = sql3.Where("user_login_log.created_at >= ?", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return &result, err
		}
		sql3 = sql3.Where("user_login_log.created_at <= ?", endDateAtBkk)
	}
	// Group only by the date (of_date)
	sql3 = sql3.Group("of_date").Order("of_date ASC")
	if err := sql3.Scan(&graph2).Error; err != nil {
		return &result, err
	}

	newUserIds := []int64{}
	data4 := []struct {
		UserId int64
		OfDate string
	}{}

	selectedFields4 := "tb_user.id AS user_id, DATE(CONVERT_TZ(tb_user.created_at,'+00:00','+07:00')) AS of_date"

	sql4 := r.db.Table("user AS tb_user").Select(selectedFields4)
	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return &result, err
		}
		sql4 = sql4.Where("tb_user.created_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return &result, err
		}
		sql4 = sql4.Where("tb_user.created_at <=  ?", endDateAtBkk)
	}
	if err := sql4.Scan(&data4).Error; err != nil {
		return &result, err
	}

	// append
	for _, v := range data4 {
		newUserIds = append(newUserIds, v.UserId)
		// append to graph 2 by of_date
		for i, v2 := range graph2 {
			if v2.OfDate == v.OfDate {
				graph2[i].NewUserCount++
				break
			}
		}
	}

	if len(newUserIds) > 0 {
		var totalDepositData []struct {
			OfDate            string
			FirstDepositCount int64
		}

		// User has first deposit
		// log.transfer_at equal to user.created_at ผู้ใช้งานที่สมัครและทำการฝากครั้งแรกภายในวันที่สมัคร
		selectedFields4 := "DATE(CONVERT_TZ(tb_log.transfer_at,'+00:00','+07:00')) AS of_date"
		selectedFields4 += ", COUNT(*) AS first_deposit_count"

		firstDepUserQuery := r.db.Table("user_transaction AS tb_log")
		firstDepUserQuery = firstDepUserQuery.Select(selectedFields4)
		firstDepUserQuery = firstDepUserQuery.Joins("JOIN bank_transaction AS tb_bank_tb_log ON tb_bank_tb_log.id = tb_log.ref_id")
		firstDepUserQuery = firstDepUserQuery.Joins("JOIN user AS tb_user ON tb_user.id = tb_log.user_id")
		firstDepUserQuery = firstDepUserQuery.Where("DATE(CONVERT_TZ(tb_log.transfer_at,'+00:00','+07:00')) = DATE(CONVERT_TZ(tb_user.created_at,'+00:00','+07:00'))")
		firstDepUserQuery = firstDepUserQuery.Where("tb_log.type_id = ?", 1)
		firstDepUserQuery = firstDepUserQuery.Where("tb_bank_tb_log.is_first_deposit = ?", true)
		firstDepUserQuery = firstDepUserQuery.Where("tb_log.is_show = ?", true)
		firstDepUserQuery = firstDepUserQuery.Where("tb_log.removed_at IS NULL")
		firstDepUserQuery = firstDepUserQuery.Where("tb_log.user_id IN (?)", newUserIds)
		if dateType.DateFrom != "" {
			startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
			if err != nil {
				return &result, err
			}
			firstDepUserQuery = firstDepUserQuery.Where("tb_log.transfer_at >= ? ", startDateAtBkk)
		}
		if dateType.DateTo != "" {
			endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
			if err != nil {
				return &result, err
			}
			firstDepUserQuery = firstDepUserQuery.Where("tb_log.transfer_at <=  ?", endDateAtBkk)
		}
		firstDepUserQuery = firstDepUserQuery.Group("of_date")
		if err := firstDepUserQuery.Scan(&totalDepositData).Error; err != nil {
			return &result, err
		}

		// Append to graph 1 by of_date
		for _, v := range totalDepositData {
			for i, v2 := range graph2 {
				if v2.OfDate == v.OfDate {
					graph2[i].FirstDepositUserCount = v.FirstDepositCount
					break
				}
			}
		}
	}

	result.BankTransGraphList = graph1
	result.UserTransGraphList = graph2

	return &result, nil
}

func (r repo) GetExchangeCurrencyList(req model.GetExchangeCurrencyListRequest) ([]model.GetExchangeCurrencyListResponse, error) {

	var list []model.GetExchangeCurrencyListResponse
	selectedFields := "exchange_currency.id AS id, exchange_currency.name_th AS name_th, exchange_currency.name_en AS name_en, exchange_currency.currency_code AS currency_code, exchange_currency.currency_name_th AS currency_name_th, exchange_currency.currency_name_en AS currency_name_en, exchange_currency.image_url AS image_url, exchange_currency.is_main AS is_main "
	query := r.db.Table("exchange_currency")
	query = query.Select(selectedFields)
	query = query.Where("exchange_currency.deleted_at IS NULL")
	if req.IsMain {
		query = query.Where("exchange_currency.is_main = ?", true)
	} else {
		query = query.Where("exchange_currency.is_main = ?", false)
	}
	query = query.Order("exchange_currency.id ASC")
	if err := query.Scan(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}
func (r repo) GetExchangeCurrencyInternalList() ([]model.GetExchangeCurrencyListResponse, error) {

	var list []model.GetExchangeCurrencyListResponse
	selectedFields := "exchange_currency.id AS id, exchange_currency.name_th AS name_th, exchange_currency.name_en AS name_en, exchange_currency.currency_code AS currency_code, exchange_currency.currency_name_th AS currency_name_th, exchange_currency.currency_name_en AS currency_name_en, exchange_currency.image_url AS image_url, exchange_currency.is_main AS is_main "
	query := r.db.Table("exchange_currency")
	query = query.Select(selectedFields)
	query = query.Where("exchange_currency.deleted_at IS NULL")
	query = query.Order("exchange_currency.id ASC")
	if err := query.Scan(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (r repo) CreateExchangeRate(req []model.CreateExchangeRateRequest) error {

	query := r.db.Table("exchange_rate")
	query = query.Create(&req)
	if err := query.Error; err != nil {
		return err
	}

	return nil
}

func (r repo) DeleteAllExchangeRate() error {

	if err := r.db.Unscoped().Table("exchange_rate").Where("exchange_currency_id != ?", 1).Delete(&model.ExchangeRate{}).Error; err != nil {
		return err
	}

	return nil
}

func (r repo) GetExchangeRateList(show string) ([]model.GetExchangeRateList, error) {

	var list []model.GetExchangeRateList
	selectedFields := "exchange_rate.id AS id, exchange_rate.exchange_currency_id AS exchange_currency_id, exchange_currency.name_th AS exchange_currency_name_th, exchange_currency.name_en AS exchange_currency_name_en, exchange_currency.currency_code AS exchange_currency_code, exchange_currency.image_url AS exchange_currency_image_url, exchange_rate.exchange_rate AS exchange_rate, exchange_rate.created_at AS created_at, exchange_rate.updated_at AS updated_at"
	selectedFields += ", exchange_rate.max_rate_deposit AS max_rate_deposit"
	query := r.db.Table("exchange_rate")
	query = query.Select(selectedFields)
	if show != "ALL" {
		query = query.Where("exchange_rate.exchange_currency_id != ?", 1)
	}
	query = query.Joins("JOIN exchange_currency ON exchange_currency.id = exchange_rate.exchange_currency_id")
	query = query.Order("exchange_rate.id ASC")
	if err := query.Scan(&list).Error; err != nil {
		return nil, err
	}

	return list, nil
}

func (r repo) GetLaosExchangeCurrency() (*model.GetExchangeRate, error) {

	var exchangeRate model.GetExchangeRate

	// where currency_code = "LAK"

	selectedFields := "exchange_rate.id AS id, exchange_rate.exchange_rate AS exchange_rate, exchange_rate.max_rate_deposit AS max_rate_deposit"
	query := r.db.Table("exchange_rate")
	query = query.Select(selectedFields)
	query = query.Joins("JOIN exchange_currency ON exchange_currency.id = exchange_rate.exchange_currency_id")
	query = query.Where("exchange_currency.currency_code = ?", "LAK")
	query = query.Order("exchange_rate.id DESC")
	query = query.Limit(1)
	if err := query.Take(&exchangeRate).Error; err != nil {
		// default
		exchangeRate.Id = 0
		exchangeRate.ExchangeRate = 580.0
		exchangeRate.MaxRateDeposit = 0.0
		return &exchangeRate, nil // use default
	}

	return &exchangeRate, nil
}

func (r repo) CreateExchangeUpdateLog(req []model.CreateExchangeUpdateLogRequest) error {

	query := r.db.Table("exchange_update_log")
	query = query.Create(&req)
	if err := query.Error; err != nil {
		return err
	}

	return nil
}

func (r repo) GetExchangeUpdateLogList(req model.GetExchangeUpdateLogListRequest) ([]model.GetExchangeUpdateLogListReponse, int64, error) {

	selectedFields := "exchange_update_log.id AS id, exchange_update_log.exchange_currency_id AS exchange_currency_id, exchange_update_log.default_main_rate AS default_main_rate, exchange_update_log.exchange_rate AS exchange_rate, exchange_update_log.currency_name_th AS currency_name_th, exchange_update_log.currency_name_en AS currency_name_en, exchange_update_log.created_admin_id AS created_admin_id, exchange_update_log.created_admin_name AS created_admin_name, exchange_update_log.created_at AS created_at"
	selectedFields += ", exchange_update_log.max_rate_deposit AS max_rate_deposit"
	var list []model.GetExchangeUpdateLogListReponse
	var total int64
	var err error

	count := r.db.Table("exchange_update_log")
	count = count.Select("exchange_update_log.id")

	if req.ExchangeCurrencyId != nil {
		count = count.Where("exchange_update_log.exchange_currency_id = ?", *req.ExchangeCurrencyId)
	}

	if err = count.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if total > 0 {
		query := r.db.Table("exchange_update_log")
		query = query.Select(selectedFields)
		query = query.Order("exchange_update_log.id DESC")

		if req.ExchangeCurrencyId != nil {
			query = query.Where("exchange_update_log.exchange_currency_id = ?", *req.ExchangeCurrencyId)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}

		if err = query.Offset(req.Page * req.Limit).Scan(&list).Error; err != nil {
			return nil, 0, err
		}
	}

	return list, total, nil
}

func (r repo) CreateDBSmsModeDeposit(data model.CreateSmsModeDepositBody) (*int64, error) {
	if err := r.db.Table("paygate_smsmode_deposit").Create(&data).Error; err != nil {
		return nil, err
	}
	return &data.Id, nil
}

func (r repo) UpdateDBSmsModeDeposit(data model.UpdateSmsModeDepositBody) error {
	if err := r.db.Table("paygate_smsmode_deposit").Where("id = ?", data.Id).Updates(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) CountDBSmsModeDeposit() (int64, error) {
	// var total int64
	// if err := r.db.Table("paygate_smsmode_deposit").Count(&total).Error; err != nil {
	// 	return 0, err
	// }
	// return total, nil

	// P.layer last id
	var lastId int64
	if err := r.db.Table("paygate_smsmode_deposit").Select("id").Order("id DESC").Limit(1).Scan(&lastId).Error; err != nil {
		return 0, err
	}

	return lastId, nil

}

func (r repo) GetSmsModeDepositFromOrder(orderNo string) (*model.PaygateSmsModeDeposit, error) {
	var smsModeDeposit model.PaygateSmsModeDeposit

	selectedFields := "id, user_id, ref_id, account_from, bank_account_id"
	selectedFields += ", bank_account_no, bank_code, order_no, amount, transfer_amount"
	selectedFields += ", transaction_no, transaction_date, transaction_status, payment_at, remark"
	selectedFields += ", created_at, updated_at"

	query := r.db.Table("paygate_smsmode_deposit")
	query = query.Select(selectedFields)
	query = query.Where("order_no = ?", orderNo)
	query = query.Where("transaction_status = ?", "WAITING_CONFIRM")
	if err := query.First(&smsModeDeposit).Error; err != nil {
		return nil, err
	}

	return &smsModeDeposit, nil
}

func (r repo) UpdateConfirmSmsModeDeposit(data model.UpdateConfirmSmsModeDepositBody) error {

	if err := r.db.Table("paygate_smsmode_deposit").Where("id = ?", data.Id).Updates(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) CreateBankAccountShowBank(body []model.CreateBankAccountShowBank) error {

	if err := r.db.Table("bank_account_show_bank").Create(&body).Error; err != nil {
		return err
	}

	return nil
}

func (r repo) DeleteBankAccountShowBankByBankAccountId(bankAccountId int64) error {

	if err := r.db.Table("bank_account_show_bank").Where("bank_account_id = ?", bankAccountId).Delete(&model.BankAccountShowBank{}).Error; err != nil {
		return err
	}

	return nil
}

func (r repo) GetBankAccountShowBankByBankAccountId(bankAccountId []int64) ([]model.GetBankAccountShowBank, error) {

	var list []model.GetBankAccountShowBank

	selectedFields := "id, bank_account_id, bank_id"
	query := r.db.Table("bank_account_show_bank")
	query = query.Select(selectedFields)
	query = query.Where("bank_account_show_bank.bank_account_id IN (?)", bankAccountId)
	query = query.Order("bank_account_show_bank.id ASC")
	if err := query.Scan(&list).Error; err != nil {
		return nil, err
	}

	return list, nil
}

func (r repo) GetUserBankIdByUserId(userId int64) (*model.UserBankDetailBody, error) {

	var record model.UserBankDetailBody

	selectedFields := "user.id AS user_id, user.bank_id AS bank_id"
	if err := r.db.Table("user").
		Select(selectedFields).
		Where("user.deleted_at IS NULL").
		Where("user.id = ?", userId).
		First(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetBankAccountMigration() ([]int64, error) {

	var list []int64

	query := r.db.Table("bank_account AS accounts")
	selectedFields := "accounts.id"
	query = query.Select(selectedFields)
	if err := query.
		Where("accounts.deleted_at IS NULL").
		Scan(&list).
		Error; err != nil {
		return nil, err
	}

	return list, nil
}

func (r repo) GetBankCurreny() ([]model.BankResponse, error) {

	var list []model.BankResponse
	var err error
	var configuration model.ConfigurationBankResponse

	selectedFields := "con.use_th_currency AS use_th_currency, con.use_laos_currency AS use_laos_currency"
	configBank := r.db.Table("configuration_web AS con")
	configBank = configBank.Select(selectedFields)
	if err := configBank.Take(&configuration).Error; err != nil {
		configuration.UseThCurrency = true
	}

	if !configuration.UseThCurrency && !configuration.UseLaosCurrency {
		configuration.UseThCurrency = true
	}

	// SELECT //
	query := r.db.Table("bank")
	query = query.Select("id, name, code, icon_url, icon_url, type_flag")
	if configuration.UseThCurrency && configuration.UseLaosCurrency {
		query = query.Where("country_code IN ?", []string{"TH", "ALL", "LAOS"})
	} else if configuration.UseThCurrency {
		query = query.Where("country_code IN ?", []string{"TH", "ALL"})
	} else if configuration.UseLaosCurrency {
		query = query.Where("country_code IN ?", []string{"LAOS", "ALL"})
	}
	if err = query.
		Scan(&list).
		Error; err != nil {
		return nil, err
	}

	return list, nil
}

func (r repo) GetBankAccountShowBankByBank() ([]model.GetBankAccountShowBank, error) {

	var list []model.GetBankAccountShowBank

	selectedFields := "id, bank_account_id, bank_id"
	query := r.db.Table("bank_account_show_bank")
	query = query.Select(selectedFields)
	query = query.Order("bank_account_show_bank.id ASC")
	if err := query.Scan(&list).Error; err != nil {
		return nil, err
	}

	return list, nil
}

func (r repo) UpdateBankAccountIsShowBank(data model.UpdateBankAccountIsShowBankRequest) error {

	if err := r.db.Table("bank_account").Where("id = ?", data.Id).Updates(&data).Error; err != nil {
		return err
	}

	return nil
}

func (r repo) CheckSmsModeDepositOrderInLastOrder(userId int64, bankAccountId int64) (*model.SmsModeDepositOrderResponse, error) {

	var record model.SmsModeDepositOrderResponse

	selectedFields := "tb_order.id AS id, tb_order.user_id AS user_id, tb_order.order_no AS order_no, tb_order.transfer_amount AS transfer_amount"
	selectedFields += ", tb_order.amount AS amount, tb_order.created_at AS created_at"

	query := r.db.Table("paygate_smsmode_deposit as tb_order")
	query = query.Select(selectedFields)
	query = query.Where("tb_order.user_id = ?", userId)
	query = query.Where("tb_order.bank_account_id = ?", bankAccountId)
	if err := query.
		Where("tb_order.transaction_status = ?", "WAITING_CONFIRM").
		Order("tb_order.id DESC").
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) TotalBankStatementSummary(req model.TotalBankStatementRequest) (*model.TotalBankTransactionSummaryResponse, error) {

	var record1 model.TotalBankStatementSummary
	var err error

	selectedFields1 := "SUM(bank_statement.amount) AS total_deposit_amount"

	query1 := r.db.Table("bank_statement")
	query1 = query1.Select(selectedFields1)
	query1 = query1.Where("bank_statement.statement_status_id IN (?)", []int{1, 2})

	if req.AccountId != nil {
		query1 = query1.Where("bank_statement.account_id = ?", *req.AccountId)
	}
	if req.FromDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.FromDate)
		if err != nil {
			return nil, err
		}
		query1 = query1.Where("bank_statement.transfer_at >= ?", startDateAtBkk)
	}
	if req.ToDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.ToDate)
		if err != nil {
			return nil, err
		}
		query1 = query1.Where("bank_statement.transfer_at <= ?", endDateAtBkk)
	}

	if err = query1.
		Take(&record1).
		Error; err != nil {
		record1.TotalDepositAmount = 0
	}

	var record2 model.TotalBankTransactionSummary
	selectedFields2 := "SUM(CASE WHEN bank_transaction.created_by_admin_id != 0 THEN bank_transaction.credit_amount ELSE 0 END) AS total_admin_deposit_amount"
	selectedFields2 += ", SUM(CASE WHEN bank_transaction.created_by_admin_id = 0 THEN bank_transaction.credit_amount ELSE 0 END) AS total_auto_deposit_amount"

	query2 := r.db.Table("bank_transaction")
	query2 = query2.Select(selectedFields2)
	query2 = query2.Where("bank_transaction.transaction_type_id = ?", model.TRANSACTION_TYPE_DEPOSIT)
	query2 = query2.Where("bank_transaction.transaction_status_id = ?", 5)

	if req.AccountId != nil {
		query2 = query2.Where("bank_transaction.to_account_id = ?", *req.AccountId)
	}
	if req.FromDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.FromDate)
		if err != nil {
			return nil, err
		}
		query2 = query2.Where("bank_transaction.transfer_at >= ?", startDateAtBkk)
	}
	if req.ToDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.ToDate)
		if err != nil {
			return nil, err
		}
		query2 = query2.Where("bank_transaction.transfer_at <= ?", endDateAtBkk)
	}

	if err = query2.
		Take(&record2).
		Error; err != nil {
		record2.TotalAdminDepositAmount = 0
		record2.TotalAutoDepositAmount = 0
	}

	var res model.TotalBankTransactionSummaryResponse
	res.TotalDepositAmount = record1.TotalDepositAmount
	res.TotalAdminDepositAmount = record2.TotalAdminDepositAmount
	res.TotalAutoDepositAmount = record2.TotalAutoDepositAmount

	return &res, nil
}

func (r repo) GetUserTransactionCountDepositTimeByUser(userId int64) (*model.GetUserTransactionCountDepositTimeByUserResponse, error) {

	var record model.GetUserTransactionCountDepositTimeByUserResponse

	selectedFields := "COUNT(*) AS bank_deposit_time"
	query := r.db.Table("user_transaction")
	query = query.Select(selectedFields)
	query = query.Where("user_transaction.user_id = ?", userId)
	query = query.Where("user_transaction.type_id = ?", model.TRANSACTION_TYPE_DEPOSIT)
	query = query.Where("user_transaction.is_show = ?", true)
	// query = query.Where("user_transaction.account_id = ?", bankAccountId)
	// only 3 months
	query = query.Where("user_transaction.transfer_at >= ?", time.Now().AddDate(0, -3, 0))
	if err := query.
		Take(&record).
		Error; err != nil {
		return &record, nil
	}

	return &record, nil
}
