package repository

import (
	"cybergame-api/model"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"time"

	"gorm.io/gorm"
)

func NewAgentInfoRepository(db *gorm.DB) AgentInfoRepository {
	return &repo{db}
}

type AgentInfoRepository interface {
	GetDb() *gorm.DB
	GetAgentInfo() (*model.AgentInfo, error)
	IncrementTotal() error
	// Log
	CreateAgentLog(body model.AgentLogCreateBody) (*int64, error)
	SetAgentLogSuccess(body model.AgentLogUpdateBody) error
	SetAgentLogError(body model.AgentLogUpdateBody) error
	// AgentProvider - AGC
	AgcLogin(data model.AgcLogin) (*model.AgcLoginResponse, error)
	AmbGetGameProviderList(req model.AmbGameProviderListRequest) (*model.AmbGameProviderReponse, error)
	AmbGetGameList(req model.AmbGameListRequest) (*model.AmbGameReponse, error)
	AgcPlay(token string, data interface{}) (*model.AgcPlay, error)
	AgcGetApistatus(req model.ApiStatusRequest) (*model.ApiStatus, error)
	AgcCountFailApiStatus(statementDate string) (int64, error)
	AgcResetFailApiStatus(statementDate string) error
	AgcSimpleWinLose(data model.AgcSimpleWinlose) (*model.AgcSimpleWinloseResponse, error)
	AgcSimpleWinLoseTidtech(data model.AgcSimpleWinlose) (*model.AgcSimpleWinloseResponse, error)
	InsertAgcApiStatus(path, date string) error
	InsertAgcPlayLog(data []model.AgentPlayLog, path string, page int, success bool) error
	AgcUpdateFailed(id int64, page int) error
	AgcUpdateSuccess(id int64) error
	GetAgcPlaylogStatus(statementDate string) (*model.ReportPlayLogStatusResponse, error)
	InsertAgcPlaylogStatus(jsonProductId, statementDate string) error
	UpdateAgcPlaylogStatus(productId int, statementDate string, status string) error
	UpdateAgcCronCalcStatus(name string, statementDate string, status string) error
	MigrateOldAgcPlaylogStatus(jsonProductId, statementDate string) error
	AgcVendorMaintenanceList(data model.AgcVendorMaintenanceList) (*model.AgcVendorMaintenanceListResponse, error)
	RandomUserWithMemberForGetStatusMatainace() (*model.UserMe, error)
	// Lottery
	LotterySimpleWinLose(data model.LotteryPlaylogRequest) (*model.LotteryPlaylogResponse, error)
	// AgentProvider - AMB
	AmbLogin(data model.AmbLogin) (*model.AmbLoginResponse, error)
	AmbPlay(data model.AmbStartGameRequest) (*model.AmbStartGameResponse, error)
	AmbGetApistatus(req model.ApiStatusRequest) (*model.ApiStatus, error)
	AmbSimpleWinLose(data model.AmbSimpleWinlose) (*model.AmbSimpleWinloseResponse, error)
	InsertAmbApiStatus(path, date string) error
	InsertAmbPlayLog(list []model.AgentPlayLog) error
	AmbUpdateFailed(id int64, page int) error
	AmbUpdateSuccess(id int64) error
	// UserPlaylog
	GetUserPlaylogCompletedStatus(path string, statementDate string) (*model.UserPlaylogStatus, error)
	CreateUserPlaylogStatus(body model.UserPlaylogStatusCreateBody) error
	GetPlayLogKeyList(dailyKeyList []string) ([]string, int64, error)
	CreateUserPlaylogBulkDirect(createList map[string]model.UserPlaylogCreateBody) error
	CreateUserPlaylogBulk(bodyList map[string]model.UserPlaylogCreateBody, memberList []string) error
	// GetUserPlayLogList(req model.UserPlaylogListRequest) ([]model.UserPlaylogResponse, int64, error)
	GetDownlineUserTotalPlayLogList(req model.UserPlaylogListRequest) ([]model.UserPlaylogTotalResponse, int64, error)
	GetUserTotalAffIncomeList(req model.UserAffiliateIncomeTotalListRequest) ([]model.UserAffiliateIncomeTotalResponse, int64, error)
	GetUserTotalAffIncomeListWithLimit(req model.UserAffiliateIncomeTotalListRequest) (map[int64]model.UserAffiliateIncomeTotalResponse, error)
	GetUserLevelAffIncomeListWithLimit(req model.UserAffiliateIncomeTotalListRequest, setting model.AfCommissionResponse) (map[int64]model.UserAffiliateIncomeTotalResponse, error)
	GetUserAffiliateIncomeKeyList(dailyKeyList []string) ([]string, int64, error)
	CreateUserAffiliateIncomeBulk(bodyList map[string]model.UserAffiliateIncomeCreateBody) error
	// REF-USER
	FrontGetUser(id int64) (*model.UserMe, error)
	GetRefListByMembers(members []string) ([]model.PlayerUserList, error)
	// [SYSLOG]
	CreateSystemLog(body model.SystemLogCreateBody) (*int64, error)
	// Config
	GetConfiguration() (*model.ConfigurationResponse, error)
	// Cronjob delete log
	CronjobDeleteAgentLog() error
	CronjobDeleteBankTransactionLog() error
	CronjobDeletePaygateHengWebhook() error
	CronjobDeletePromotionWebUserLog() error
	CronjobDeleteUserLoginLog() error
	CronjobDeleteWebHookLog() error
	CronjobDeletePgHardCallBack() error
	// REF-ACTION
	GetRaceActionByActionKey(actionKey string) (*model.RaceAction, error)
	CreateRaceCondition(body model.RaceActionCreateBody) (int64, error)
	UpdateRaceCondition(id int64, body model.RaceActionUpdateBody) error
	// get game from Master
	GetAgentGameCategoryListFromMaster(req model.GetAgentGameCategoryListFromMasterRequest) ([]model.GameCategory, error)
	GetAgentGameListFromMaster(req model.GetAgentGameRequestList) ([]model.GameDetail, error)
	// REF-AFF-MIGRATE
	CountPendingAffUserIncomeLogList() (int64, error)
	GetPendingAffiliateIncomeList() ([]model.UserAffIncomeReponse, error)
	MigrateAffTransactionFull(row model.UserAffIncomeReponse) error
	MigrateAffTransactionEtc(row model.UserAffIncomeReponse) error
	// REF-Config
	GetCommissionSetting() (*model.AfCommissionResponse, error)
	// REF AFF
	GetUserIdByMemberList(memberList []string) ([]model.UserMemberList, error)
	GetUserMemberByUserId(userId []int64) ([]model.UserMemberList, error)
	GetUserAffiliateTransactionKeyList(dailyKeyList []string) ([]string, int64, error)
	CreateUserAffiliateTransactionBulk(bodyList map[string]model.UserAffiliateIncomeTotalResponse) error
	ExpireUserAffiliateTransaction(days int64) error
	// REF-ALLIANCE
	GetAllianceWinloseIncomeListByDailyKeyList(bulkBody map[string]model.AllianceWinloseIncomeCreateBody) ([]model.AllianceWinloseIncomeDailyKey, int64, error)
	UpdatePendingAllianceWinloseIncome(id int64, body model.AllianceWinlosePendingIncomeUpdateBody) error
	CreateAllianceWinloseIncomeBulk(bulkMap map[string]model.AllianceWinloseIncomeCreateBody) error
	GetAllianceTotalBonusLogList(req model.AllianceBonusLogListRequest) ([]model.UserTransactionBonusResponse, int64, error)
	GetAllianceSumUserPlayLogList(req model.AlliancePlayLogListRequest) ([]model.AllianceUserSumPlayLog, int64, error)
	// REF-AFF
	MigrateAffiliateUserAllLevel(userId int64) error
	UpdateTotalMemberFirstDeposit(refBy int64) error
	// REF-WEBHOOK
	CreateWebhookLog(data model.WebhookLogCreateBody) (*int64, error)
	// REF-TODAY PLAYLOG
	GetTodaySumUserPlayLogList(req model.UserTodayPlaylogListRequest) ([]model.UserTodaySumPlaylogReponse, int64, error)
	CreateUserTodayPlaylogBulkDirect(createList map[string]model.UserTodayPlaylogCreateBody) error
	CreateUserTodayPlaylogBulk(bodyList map[string]model.UserTodayPlaylogCreateBody, memberList []string) error
	GetTodayPlayLogKeyList(dailyKeyList []string) ([]model.UserTodayPlaylogResponse, int64, error)
	UpdateUserTodayPlaylog(updateBody model.UserTodayPlaylogUpdateBody) error

	// priority setting
	GetAgentGamePrioritySetting(categoryName string, isActiveOnly bool) ([]model.AgentGamePriority, error)

	// UserTier
	IncreaseUserTotalTurnForUserTier(createList map[string]model.UserPlaylogCreateBody, memberList []string) error
	IncreaseUserTotalTurnForUserTierDirect(createList map[string]model.UserPlaylogCreateBody) error

	// promotion
	CheckIsLockedCreditPromotionByUserId(userId int64) (bool, error)

	// PG-HARD
	GetAgentPgHardCallback(req model.AgentPgHardCallbackSummaryRequest) ([]model.AgentPgHardCallbackSummary, error)
	GetInternalAgentPgHardSetting() (*model.GetInternalAgentPgHardSetting, error)

	// CTW
	GetInternalAgentCtwSetting() (*model.GetInternalAgentCtwSetting, error)
	GetAgentCtwCallback(req model.AgentCtwCallbackSummaryRequest) ([]model.AgentCtwCallbackSummary, error)
}

func (r repo) GetAgentInfo() (*model.AgentInfo, error) {

	var data model.AgentInfo

	if err := r.db.Table("agent_info").
		Select("total").
		Take(&data).
		Error; err != nil {
		return nil, err
	}
	return &data, nil
}

func (r repo) IncrementTotal() error {

	if err := r.db.Table("agent_info").Where("id = ?", 1).
		Update("total", gorm.Expr("total + ?", 1)).
		Error; err != nil {
		return err
	}
	return nil
}

func (r repo) CreateAgentLog(body model.AgentLogCreateBody) (*int64, error) {

	if err := r.db.Table("agent_log").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) SetAgentLogSuccess(body model.AgentLogUpdateBody) error {

	updateBody := map[string]interface{}{
		"status":       "SUCCESS",
		"json_reponse": body.JsonReponse,
	}

	if err := r.db.Table("agent_log").Where("id = ?", body.Id).Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) SetAgentLogError(body model.AgentLogUpdateBody) error {

	updateBody := map[string]interface{}{
		"status":       "ERROR",
		"json_reponse": body.JsonReponse,
	}

	if err := r.db.Table("agent_log").Where("id = ?", body.Id).Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) CronjobDeleteAgentLog() error {

	CurrentDate := time.Now().UTC().Add(time.Hour * 7).Format("2006-01-02")
	startDateAtBkk, err := r.ParseBodBkk(CurrentDate)
	if err != nil {
		return err
	}

	if err := r.db.Unscoped().Table("agent_log").Where("DATEDIFF(?, created_at) > 7", startDateAtBkk).Delete(&model.AgentLog{}).Error; err != nil {
		return err
	}

	return nil
}

func (r repo) GetAgentGameCategoryListFromMaster(req model.GetAgentGameCategoryListFromMasterRequest) ([]model.GameCategory, error) {
	// Define the base URL
	baseURL := os.Getenv("MASTER_WEB_ENDPOINT") + "/api/agent-game/category-list"

	// Define the query parameters
	agentType := req.AgentType
	categoryName := req.CategoryName

	// Construct the complete URL with query parameters
	url := fmt.Sprintf("%s?agentType=%s&categoryName=%s", baseURL, agentType, categoryName)

	// Make a GET request to the API
	resp, err := http.Get(url)
	if err != nil {
		log.Printf("Error making request: %v", err)
	}
	defer resp.Body.Close()

	// Check if the response status code is OK
	if resp.StatusCode != http.StatusOK {
		log.Printf("Unexpected status code: %d", resp.StatusCode)
	}

	// Decode the response JSON into a map[string][]model.GetAgentGameCategoryItem
	var categoryMap map[string][]model.GameCategory
	err = json.NewDecoder(resp.Body).Decode(&categoryMap)
	if err != nil {
		log.Printf("Error decoding JSON: %v", err)
	}

	// // Print the retrieved data
	// fmt.Println("Retrieved categories:")
	// for categoryName, categoryItems := range categoryMap {
	// 	fmt.Printf("Category: %s\n", categoryName)
	// 	for _, item := range categoryItems {
	// 		fmt.Printf("Name: %s, VendorCode: %s, ImageName: %s, Category: %s\n",
	// 			item.Name, item.VendorCode, item.ImageName, item.Category)
	// 	}
	// }

	return categoryMap[categoryName], nil
}

func (r repo) GetAgentGameListFromMaster(req model.GetAgentGameRequestList) ([]model.GameDetail, error) {
	// Define the base URL
	baseURL := os.Getenv("MASTER_WEB_ENDPOINT") + "/api/agent-game/game-list"

	// Define the query parameters
	agentType := req.AgentType
	vendorCode := req.VendorCode

	// Construct the complete URL with query parameters
	url := fmt.Sprintf("%s?agentType=%s&vendorCode=%s", baseURL, agentType, vendorCode)

	// Make a GET request to the API
	resp, err := http.Get(url)
	if err != nil {
		log.Printf("Error making request: %v", err)
	}
	defer resp.Body.Close()

	// Check if the response status code is OK
	if resp.StatusCode != http.StatusOK {
		log.Printf("Unexpected status code: %d", resp.StatusCode)
	}

	// Decode the response JSON into a map[string][]model.GetAgentGameCategoryItem
	var categoryMap map[string][]model.GameDetail
	err = json.NewDecoder(resp.Body).Decode(&categoryMap)
	if err != nil {
		log.Printf("Error decoding JSON: %v", err)
	}

	// Print the retrieved data
	// fmt.Println("Retrieved categories:")
	// for categoryName, categoryItems := range categoryMap {
	// 	fmt.Printf("Category: %s\n", categoryName)
	// 	for _, item := range categoryItems {
	// 		fmt.Printf("Name: %s, VendorCode: %s, ImageName: %s, Category: %s\n", item.GameName, item.VendorCode, item.Image, item.IsShowMain)
	// 	}
	// }

	return categoryMap[vendorCode], nil
}

func (r repo) MigrateAffTransactionFull(row model.UserAffIncomeReponse) error {

	// ยังไม่เคยรับ แยกได้
	fixedKeyDate := "20240301"
	tx := r.db.Begin()

	// AFF_TRANSACTION_TYPE_FIRST_DEPOSIT
	if row.FirstDepositBonus > 0 {
		var createLinkAff model.AffTransactionCreateBody
		createLinkAff.UserId = row.UserId
		createLinkAff.DailyKey = fmt.Sprintf("M-U%dREF%d", row.UserId, 0) // ONCE PER USER
		// createLinkAff.DownlineId = userId // SUM ALL USER
		createLinkAff.IncomeAmount = row.FirstDepositBonus
		createLinkAff.TypeId = model.AFF_TRANSACTION_TYPE_FIRST_DEPOSIT
		createLinkAff.StatusId = model.AFF_TRANSACTION_STATUS_PENDING
		if err := tx.Table("affiliate_transaction").Create(&createLinkAff).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	if row.BonusShareTotal > 0 {
		// Create Register Bonus Transaction
		var createTransactionBody model.AffTransactionCreateBody
		createTransactionBody.UserId = row.UserId
		createTransactionBody.DailyKey = fmt.Sprintf("M-REGUSER-%d", row.UserId) // ONCE PER USER
		// createLinkAff.DownlineId = userId // SUM ALL USER
		createTransactionBody.IncomeAmount = row.BonusShareTotal
		createTransactionBody.TypeId = model.AFF_TRANSACTION_TYPE_NEW_REGISTER
		createTransactionBody.StatusId = model.AFF_TRANSACTION_STATUS_PENDING
		if err := tx.Table("affiliate_transaction").Create(&createTransactionBody).Error; err != nil {
			tx.Rollback()
			return err
		}

	}

	if row.CommissionSport > 0 || row.CommissionCasino > 0 || row.CommissionGame > 0 || row.CommissionLottery > 0 || row.CommissionP2p > 0 || row.CommissionFinancial > 0 {
		var newRow model.AffTransactionCreateBody
		newRow.UserId = row.UserId
		newRow.DailyKey = fmt.Sprintf("M-D%sU%d", fixedKeyDate, row.UserId) // ONCE PER USER
		// createLinkAff.DownlineId = userId // SUM ALL USER
		newRow.IncomeAmount = row.CommissionSport + row.CommissionCasino + row.CommissionGame + row.CommissionLottery + row.CommissionP2p + row.CommissionFinancial
		newRow.TypeId = model.AFF_TRANSACTION_TYPE_PLAY_COMMISION
		newRow.StatusId = model.AFF_TRANSACTION_STATUS_PENDING
		if err := tx.Table("affiliate_transaction").Create(&newRow).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	tx.Commit()

	return nil
}

func (r repo) MigrateAffTransactionEtc(row model.UserAffIncomeReponse) error {

	// รับแล้ว รวมไว้เป็น 1 รายการ ให้ตรงกับยอดรวม
	fixedKeyDate := "20240301"

	var newRow model.AffTransactionCreateBody
	newRow.UserId = row.UserId
	newRow.DailyKey = fmt.Sprintf("M-D%sU%d", fixedKeyDate, row.UserId) // ONCE PER USER
	// createLinkAff.DownlineId = userId // SUM ALL USER
	newRow.IncomeAmount = row.CommissionCurrent // use Current ยอดไม่ตรงกับรวมเองก็ช่าง ใช้ยอดคงเหลือเป็นหลัก
	newRow.TypeId = model.AFF_TRANSACTION_TYPE_PLAY_COMMISION
	newRow.StatusId = model.AFF_TRANSACTION_STATUS_PENDING
	if err := r.db.Table("affiliate_transaction").Create(&newRow).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) IncreaseUserTotalTurnForUserTier(createList map[string]model.UserPlaylogCreateBody, memberList []string) error {

	// NOT Direct = no user id

	// ENV show Tier Setting todo
	spSetting, err := r.GetTierSetting("use_speciel_setting")
	if err != nil || spSetting.Id == 0 {
		return nil
	}

	// check Setting enabled
	setting, err := r.GetUserTierSetting("is_turnover_tier_setting")
	if err != nil {
		return err
	}
	if setting.FromValue == 0 {
		return nil
	}

	// set createList user id
	userMap := make(map[string]model.UserMemberList, 0)
	UserList, err := r.GetUserListByMemberList(memberList)
	if err != nil {
		return err
	}
	for _, v := range UserList {
		userMap[v.MemberCode] = v
	}
	for k, v := range createList {
		if user, ok := userMap[v.MemberCode]; ok {
			v.UserId = user.Id
			createList[k] = v
		}
	}

	// Have User ID
	var userIds []int64
	for _, body := range createList {
		userIds = append(userIds, body.UserId)
	}

	if len(userIds) > 0 {
		// UPDATE OR INSERT //
		var list []model.UserTierData
		if err := r.db.Table("user_tier_data").Select("id, user_id").Where("user_id IN (?)", userIds).Scan(&list).Error; err != nil {
			return err
		}

		mapUserData := make(map[int64]model.UserTierData)
		for _, item := range list {
			mapUserData[item.UserId] = item
		}

		var createBody []model.UserTierDataCreateBody
		for _, userId := range userIds {
			if _, ok := mapUserData[userId]; !ok {
				createBody = append(createBody, model.UserTierDataCreateBody{
					UserId:    userId,
					TotalTurn: 0,
				})
			}
		}
		if len(createBody) > 0 {
			if err := r.db.Table("user_tier_data").Create(createBody).Error; err != nil {
				return err
			}
		}

		tx := r.db.Begin()
		for _, body := range createList {
			if body.TurnTotal > 0 {
				if err := tx.Table("user_tier_data").Where("user_id = ?", body.UserId).
					Update("total_turn", gorm.Expr("total_turn + ?", body.TurnTotal)).Error; err != nil {
					tx.Rollback()
					return err
				}
			}
		}
		if err := tx.Commit().Error; err != nil {
			return err
		}
	}
	return nil
}

func (r repo) IncreaseUserTotalTurnForUserTierDirect(createList map[string]model.UserPlaylogCreateBody) error {

	// ENV show Tier Setting todo
	spSetting, err := r.GetTierSetting("use_speciel_setting")
	if err != nil || spSetting.Id == 0 {
		return nil
	}

	// check Setting enabled
	setting, err := r.GetUserTierSetting("is_turnover_tier_setting")
	if err != nil {
		return err
	}
	if setting.FromValue == 0 {
		return nil
	}

	var userIds []int64
	for _, body := range createList {
		userIds = append(userIds, body.UserId)
	}

	if len(userIds) > 0 {
		// UPDATE OR INSERT //
		var list []model.UserTierData
		if err := r.db.Table("user_tier_data").Select("id, user_id").Where("user_id IN (?)", userIds).Scan(&list).Error; err != nil {
			return err
		}

		mapUserData := make(map[int64]model.UserTierData)
		for _, item := range list {
			mapUserData[item.UserId] = item
		}

		var createBody []model.UserTierDataCreateBody
		for _, userId := range userIds {
			if _, ok := mapUserData[userId]; !ok {
				createBody = append(createBody, model.UserTierDataCreateBody{
					UserId:    userId,
					TotalTurn: 0,
				})
			}
		}
		if len(createBody) > 0 {
			if err := r.db.Table("user_tier_data").Create(createBody).Error; err != nil {
				return err
			}
		}

		tx := r.db.Begin()
		for _, body := range createList {
			if body.TurnTotal > 0 {
				if err := tx.Table("user_tier_data").Where("user_id = ?", body.UserId).
					Update("total_turn", gorm.Expr("total_turn + ?", body.TurnTotal)).Error; err != nil {
					tx.Rollback()
					return err
				}
			}
		}
		if err := tx.Commit().Error; err != nil {
			return err
		}
	}
	return nil
}

func (r repo) RandomUserWithMemberForGetStatusMatainace() (*model.UserMe, error) {

	var data model.UserMe

	if err := r.db.Table("user").Select("user.id").
		Where("user.member_code IS NOT NULL").
		Where("user.deleted_at IS NULL").
		Take(&data).Error; err != nil {
		return nil, err
	}

	return &data, nil
}
