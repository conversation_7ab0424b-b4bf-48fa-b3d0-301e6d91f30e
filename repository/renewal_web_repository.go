package repository

import (
	"cybergame-api/model"
	"fmt"
	"io"
	"log"
	"strings"
	"time"

	"gorm.io/gorm"
)

func NewRenewalWebRepository(db *gorm.DB) RenewalWebRepository {
	return &repo{db}
}

type RenewalWebRepository interface {
	// Dunk GetConfigurationWeb
	GetConfiguration() (*model.ConfigurationResponse, error)
	// MyWeb
	GetLocalWebInfo() (*model.WebStatusResponse, error)
	CreateWebStatus(body model.WebMasterCreateBody) (*int64, error)
	DecreaseSmsCredit(smsCredit int64) error
	DecreaseFastbankCredit(fastbankCredit int64) error
	UpdateWebMasterInfo(id int64, updateBody map[string]interface{}) error
	// Validate Package
	GetWebAdminCount() (int64, error)
	GetWebUserCount() (int64, error)
	GetBankAccountInfo() (*model.BankAccountInfoResponse, error)
	// Web Renewal
	GetWebRenewalPackageList(req model.WebRenewalPackageListRequest) ([]model.WebRenewalPackageResponse, int64, error)
	GetWebRenewalPackageById(id int64) (*model.WebRenewalPackageResponse, error)
	CreateWebRenewalInvoice(body model.InvoiceCreateBody) (*int64, error)
	UpdateWebRenewal(updateBody model.WebMasterUpdateWebRenewalBody) error
	// SMS Renewal
	GetSmsRenewalPackageList(req model.SmsRenewalPackageListRequest) ([]model.SmsRenewalPackageResponse, int64, error)
	GetSmsRenewalPackageById(id int64) (*model.SmsRenewalPackageResponse, error)
	CreateSmsRenewalInvoice(body model.InvoiceCreateBody) (*int64, error)
	UpdateSmsRenewal(updateBody model.WebMasterUpdateSmsRenewalBody) error
	// Fastbank Renewal
	GetFastbankRenewalPackageList(req model.FastbankRenewalPackageListRequest) ([]model.FastbankRenewalPackageResponse, int64, error)
	GetFastbankRenewalPackageById(id int64) (*model.FastbankRenewalPackageResponse, error)
	CreateFastbankRenewalInvoice(body model.InvoiceCreateBody) (*int64, error)
	UpdateFastbankRenewal(updateBody model.WebMasterUpdateFastbankRenewalBody) error
	// Invoice - from renewal web sms and fastbank credit
	GetInvoiceList(req model.InvoiceListRequest) ([]model.InvoiceResponse, int64, error)
	GetInvoiceById(id int64) (*model.InvoiceResponse, error)
	ViewBillById(id int64) (*model.InvoiceBillResponse, error)
	ViewReceiptById(id int64) (*model.InvoiceReceiptResponse, error)
	ConfirmPayment(body model.InvoiceConfirmPaymentBody) error
	ConfirmInvoice(body model.InvoiceConfirmBody) error
	RejectInvoice(body model.InvoiceConfirmBody) error
	// REF-HENG
	CreateInvoiceHengPayment(body model.DownlineInvoiceHengPaymentCreateRequest) (*model.DownlineInvoiceHengPaymentCreateResponse, error)
	CreateInvoicePayonexPayment(body model.DownlineInvoicePayonexPaymentCreateRequest) (*model.DownlineInvoicePayonexPaymentCreateResponse, error)
	// REF-REMOTE
	GetWebInfo() (*model.DownlineWebInfo, error)
	// REF-CLOUDFLARE
	UploadImageToCloudflare(pathUplaod string, filename string, fileReader io.Reader) (*model.CloudFlareUploadCreateBody, error)

	// S3
	UploadImageToS3(pathUpload string, filename string, fileReader io.Reader) (*model.FileUploadResponse, error)
}

func (r repo) GetLocalWebInfo() (*model.WebStatusResponse, error) {

	var record model.WebStatusResponse

	selectedFields := "tb_webs.id as id, tb_webs.name as name, tb_webs.web_domain as web_domain, tb_webs.api_key as api_key, tb_webs.payment_detail as payment_detail"
	selectedFields += ", tb_webs.last_remote_update_at as last_remote_update_at"
	selectedFields += ", tb_webs.is_front_enabled as is_front_enabled, tb_webs.is_back_enabled as is_back_enabled, tb_webs.maintenance_message as maintenance_message"
	selectedFields += ", tb_webs.current_web_package_id as current_web_package_id, tb_web_package.name as current_web_package_name"
	selectedFields += ", tb_webs.current_sms_package_id as current_sms_package_id, tb_webs.web_expired_date as web_expired_date, tb_webs.sms_credit_balance as sms_credit_balance, tb_webs.sms_expired_date as sms_expired_date"
	selectedFields += ", tb_webs.current_fastbank_package_id as current_fastbank_package_id, tb_webs.fastbank_credit_balance as fastbank_credit_balance, tb_webs.fastbank_expired_date as fastbank_expired_date"
	selectedFields += ", tb_webs.fastbank_free_start_date as fastbank_free_start_date, tb_webs.fastbank_free_end_date as fastbank_free_end_date"
	selectedFields += ", tb_webs.created_at as created_at, tb_webs.updated_at as updated_at"
	query := r.db.Table("renewal_web_master as tb_webs")
	query = query.Select(selectedFields)
	query = query.Joins("LEFT JOIN renewal_web_package as tb_web_package ON tb_web_package.id = tb_webs.current_web_package_id")
	query = query.Where("tb_webs.deleted_at IS NULL")
	if err := query.Take(&record).Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) CreateWebStatus(body model.WebMasterCreateBody) (*int64, error) {

	if err := r.db.Table("renewal_web_master").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) DecreaseSmsCredit(smsCredit int64) error {

	// getDefault Web
	var web model.WebStatusResponse

	selectedFields := "tb_webs.id as id"
	query := r.db.Table("renewal_web_master as tb_webs")
	query = query.Select(selectedFields)
	query = query.Where("tb_webs.deleted_at IS NULL")
	if err := query.Take(&web).Error; err != nil {
		return err
	}

	updateBody := map[string]interface{}{}
	updateBody["sms_credit_balance"] = gorm.Expr("sms_credit_balance - ?", smsCredit)
	queryUpdate := r.db.Table("renewal_web_master").Where("id = ?", web.Id)
	if err := queryUpdate.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) DecreaseFastbankCredit(fastbankCredit int64) error {

	// getDefault Web
	var web model.WebStatusResponse

	selectedFields := "tb_webs.id as id"
	selectedFields += ", tb_webs.fastbank_free_start_date as fastbank_free_start_date, tb_webs.fastbank_free_end_date as fastbank_free_end_date"
	query := r.db.Table("renewal_web_master as tb_webs")
	query = query.Select(selectedFields)
	query = query.Where("tb_webs.deleted_at IS NULL")
	if err := query.Take(&web).Error; err != nil {
		return err
	}

	// Set FastBank Package Type
	web.FastBankPackageType = "CREDIT"
	// Check Buffe Credit //
	if web.FastbankFreeStartDate != "" && web.FastbankFreeEndDate != "" {
		actionAt := time.Now()
		buffeStartAt := actionAt.AddDate(1, 2, 3) // MOCK INVALID DATE
		buffeEndAt := actionAt.AddDate(1, 2, 3)   // MOCK INVALID DATE
		if dbStartAt, err := time.Parse("2006-01-02T00:00:00Z", web.FastbankFreeStartDate); err == nil {
			buffeStartAt = dbStartAt
		}
		if dbEndAt, err := time.Parse("2006-01-02T00:00:00Z", web.FastbankFreeEndDate); err == nil {
			buffeEndAt = dbEndAt.AddDate(0, 0, 1) // END AT 23:59:59
		}
		if actionAt.After(buffeStartAt) && actionAt.Before(buffeEndAt) {
			web.FastBankPackageType = "FREE"
		}
	}

	if web.FastBankPackageType == "CREDIT" {
		updateBody := map[string]interface{}{}
		updateBody["fastbank_credit_balance"] = gorm.Expr("fastbank_credit_balance - ?", fastbankCredit)

		queryUpdate := r.db.Table("renewal_web_master").Where("id = ?", web.Id)
		if err := queryUpdate.Updates(updateBody).Error; err != nil {
			return err
		}
	} else if web.FastBankPackageType == "FREE" {
		log.Println("FASTBANK FREE FROM", web.FastbankFreeStartDate, "TO", web.FastbankFreeEndDate)
	}

	return nil
}

func (r repo) UpdateWebMasterInfo(id int64, updateBody map[string]interface{}) error {

	if err := r.db.Table("renewal_web_master").Where("id = ?", id).Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetWebAdminCount() (int64, error) {

	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("admin as tb_admin")
	count = count.Select("tb_admin.id")
	count = count.Where("tb_admin.deleted_at IS NULL")
	if err = count.
		Count(&total).
		Error; err != nil {
		return 0, err
	}
	return total, nil
}

func (r repo) GetWebUserCount() (int64, error) {

	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("user as tb_user")
	count = count.Select("tb_user.id")
	count = count.Where("tb_user.deleted_at IS NULL")
	if err = count.
		Count(&total).
		Error; err != nil {
		return 0, err
	}
	return total, nil
}

func (r repo) GetBankAccountInfo() (*model.BankAccountInfoResponse, error) {

	var result model.BankAccountInfoResponse
	var err error

	selectedFields := "SUM(tb_log.credit_amount) as last_month_deposit_price"
	sql := r.db.Table("bank_transaction as tb_log")
	sql = sql.Select(selectedFields)
	sql = sql.Where("tb_log.transaction_type_id = ?", model.TRANSACTION_TYPE_DEPOSIT)
	sql = sql.Where("tb_log.created_at BETWEEN DATE_SUB(NOW(), INTERVAL 1 MONTH) AND NOW()")
	sql = sql.Where("tb_log.deleted_at IS NULL")
	if err = sql.Take(&result).Error; err != nil {
		return nil, err
	}

	selectedFields2 := "COUNT(IF(tb_account.account_type_id IN (1,3), tb_account.id, NULL)) as deposit_account_count"
	selectedFields2 += ", COUNT(IF(tb_account.account_type_id IN (2,3), tb_account.id, NULL)) as withdraw_account_count"
	selectedFields2 += ", COUNT(IF(tb_account.account_type_id = 4, tb_account.id, NULL)) as saving_account_count"
	sql2 := r.db.Table("bank_account as tb_account")
	sql2 = sql2.Select(selectedFields2)
	sql2 = sql2.Where("tb_account.deleted_at IS NULL")
	if err = sql2.Take(&result).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (r repo) GetWebRenewalPackageList(req model.WebRenewalPackageListRequest) ([]model.WebRenewalPackageResponse, int64, error) {

	var list []model.WebRenewalPackageResponse
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("renewal_web_package as tb_package")
	count = count.Select("tb_package.id")

	if err = count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "*"
		query := r.db.Table("renewal_web_package as tb_package")
		query = query.Select(selectedFields)

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetWebRenewalPackageById(id int64) (*model.WebRenewalPackageResponse, error) {

	var record model.WebRenewalPackageResponse

	selectedFields := "*"
	if err := r.db.Table("renewal_web_package as tb_package").
		Select(selectedFields).
		Where("tb_package.id = ?", id).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) CreateWebRenewalInvoice(body model.InvoiceCreateBody) (*int64, error) {

	if err := r.db.Table("invoice").Create(&body).Error; err != nil {
		return nil, err
	}

	// SET INVOICE NUMBER //
	// pad id left with 0 for 6 digits //
	invoiceNumber := fmt.Sprintf("%06d", body.Id)
	query := r.db.Table("invoice").Where("id = ?", body.Id)
	// if err := query.Update("invoice_no", gorm.Expr("invoice_no + ?", invoiceNumber)).Error; err != nil {
	// user concat
	if err := query.Update("invoice_no", gorm.Expr("concat(invoice_no, ?)", invoiceNumber)).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) UpdateWebRenewal(body model.WebMasterUpdateWebRenewalBody) error {

	updateBody := map[string]interface{}{}
	updateBody["current_web_package_id"] = body.CurrentWebPackageId
	updateBody["web_expired_date"] = body.WebExpiredDate

	query := r.db.Table("renewal_web_master").Where("id = ?", body.WebId)
	if err := query.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetSmsRenewalPackageList(req model.SmsRenewalPackageListRequest) ([]model.SmsRenewalPackageResponse, int64, error) {

	var list []model.SmsRenewalPackageResponse
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("renewal_sms_package as tb_package")
	count = count.Select("tb_package.id")

	if err = count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "*"
		query := r.db.Table("renewal_sms_package as tb_package")
		query = query.Select(selectedFields)

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetSmsRenewalPackageById(id int64) (*model.SmsRenewalPackageResponse, error) {

	var record model.SmsRenewalPackageResponse

	selectedFields := "*"
	if err := r.db.Table("renewal_sms_package as tb_package").
		Select(selectedFields).
		Where("tb_package.id = ?", id).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) CreateSmsRenewalInvoice(body model.InvoiceCreateBody) (*int64, error) {

	if err := r.db.Table("invoice").Create(&body).Error; err != nil {
		return nil, err
	}

	// SET INVOICE NUMBER //
	// pad id left with 0 for 6 digits //
	invoiceNumber := fmt.Sprintf("%06d", body.Id)
	query := r.db.Table("invoice").Where("id = ?", body.Id)
	// if err := query.Update("invoice_no", gorm.Expr("invoice_no + ?", invoiceNumber)).Error; err != nil {
	// user concat
	if err := query.Update("invoice_no", gorm.Expr("concat(invoice_no, ?)", invoiceNumber)).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) UpdateSmsRenewal(body model.WebMasterUpdateSmsRenewalBody) error {

	updateBody := map[string]interface{}{}
	updateBody["current_sms_package_id"] = body.CurrentSmsPackageId
	updateBody["sms_credit_balance"] = gorm.Expr("sms_credit_balance + ?", body.SmsCredit)
	updateBody["sms_expired_date"] = body.SmsExpiredDate

	query := r.db.Table("renewal_web_master").Where("id = ?", body.WebId)
	if err := query.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetFastbankRenewalPackageList(req model.FastbankRenewalPackageListRequest) ([]model.FastbankRenewalPackageResponse, int64, error) {

	var list []model.FastbankRenewalPackageResponse
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("renewal_fastbank_package as tb_package")
	count = count.Select("tb_package.id")

	if err = count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "*"
		query := r.db.Table("renewal_fastbank_package as tb_package")
		query = query.Select(selectedFields)

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetFastbankRenewalPackageById(id int64) (*model.FastbankRenewalPackageResponse, error) {

	var record model.FastbankRenewalPackageResponse

	selectedFields := "*"
	if err := r.db.Table("renewal_fastbank_package as tb_package").
		Select(selectedFields).
		Where("tb_package.id = ?", id).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) CreateFastbankRenewalInvoice(body model.InvoiceCreateBody) (*int64, error) {

	if err := r.db.Table("invoice").Create(&body).Error; err != nil {
		return nil, err
	}

	// SET INVOICE NUMBER //
	// pad id left with 0 for 6 digits //
	invoiceNumber := fmt.Sprintf("%06d", body.Id)
	query := r.db.Table("invoice").Where("id = ?", body.Id)
	// if err := query.Update("invoice_no", gorm.Expr("invoice_no + ?", invoiceNumber)).Error; err != nil {
	// user concat
	if err := query.Update("invoice_no", gorm.Expr("concat(invoice_no, ?)", invoiceNumber)).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) UpdateFastbankRenewal(body model.WebMasterUpdateFastbankRenewalBody) error {

	updateBody := map[string]interface{}{}
	updateBody["current_fastbank_package_id"] = body.CurrentFastbankPackageId
	updateBody["fastbank_credit_balance"] = gorm.Expr("fastbank_credit_balance + ?", body.FastbankCredit)
	updateBody["fastbank_expired_date"] = body.FastbankExpiredDate

	query := r.db.Table("renewal_web_master").Where("id = ?", body.WebId)
	if err := query.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetInvoiceList(req model.InvoiceListRequest) ([]model.InvoiceResponse, int64, error) {

	var list []model.InvoiceResponse
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("invoice as tb_invoice")
	count = count.Select("tb_invoice.id")
	if req.TypeId != nil {
		count = count.Where("tb_invoice.invoice_type_id = ?", req.TypeId)
	}
	if req.StatusId != nil {
		count = count.Where("tb_invoice.status_id = ?", req.StatusId)
	}
	if req.Search != "" {
		searchText := "%" + req.Search + "%"
		count = count.Where("tb_invoice.invoice_no LIKE ?", "%"+searchText+"%")
	}
	if err = count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_invoice.id as id, tb_invoice.web_id as web_id, tb_invoice.web_name as web_name, tb_webs.web_domain as web_domain, tb_invoice.invoice_no as invoice_no"
		selectedFields += ", tb_invoice.invoice_type_id as invoice_type_id, tb_invoice_type.name as invoice_type, tb_invoice_type.detail as invoice_type_detail"
		selectedFields += ", tb_invoice.invoice_at as invoice_at, tb_invoice.expire_at as expire_at, tb_invoice.paid_at as paid_at, tb_invoice.payment_detail as payment_detail"
		selectedFields += ", tb_invoice.confirm_by as confirm_by, tb_invoice.confirm_at as confirm_at"
		selectedFields += ", tb_invoice.bank_id AS bank_id, tb_invoice.account_no AS account_no, tb_invoice.account_name AS account_name"
		selectedFields += ", tb_invoice.package_id as package_id, tb_invoice.package_detail as package_detail, tb_invoice.renew_days as renew_days, tb_invoice.renew_credit_amount as renew_credit_amount"
		selectedFields += ", tb_invoice.status_id as status_id, tb_invoice_status.name as status, tb_invoice_status.detail as status_detail"
		selectedFields += ", tb_invoice.sum_price as sum_price, tb_invoice.vat_percent as vat_percent, tb_invoice.vat_price as vat_price"
		selectedFields += ", tb_invoice.discount_price as discount_price, tb_invoice.total_price as total_price"
		selectedFields += ", tb_invoice.slip_image_path as slip_image_path"
		selectedFields += ", tb_invoice.created_at as created_at, tb_invoice.updated_at as updated_at"
		query := r.db.Table("invoice as tb_invoice")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN renewal_web_master as tb_webs ON tb_webs.id = tb_invoice.web_id")
		query = query.Joins("LEFT JOIN invoice_type as tb_invoice_type ON tb_invoice_type.id = tb_invoice.invoice_type_id")
		query = query.Joins("LEFT JOIN invoice_status as tb_invoice_status ON tb_invoice_status.id = tb_invoice.status_id")
		if req.TypeId != nil {
			query = query.Where("tb_invoice.invoice_type_id = ?", req.TypeId)
		}
		if req.StatusId != nil {
			query = query.Where("tb_invoice.status_id = ?", req.StatusId)
		}
		if req.Search != "" {
			searchText := "%" + req.Search + "%"
			query = query.Where("tb_invoice.invoice_no LIKE ?", "%"+searchText+"%")
		}
		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetInvoiceById(id int64) (*model.InvoiceResponse, error) {

	var record model.InvoiceResponse

	selectedFields := "tb_invoice.id as id, tb_invoice.web_id as web_id, tb_invoice.web_name as web_name, tb_webs.web_domain as web_domain, tb_invoice.invoice_no as invoice_no"
	selectedFields += ", tb_invoice.invoice_type_id as invoice_type_id, tb_invoice_type.name as invoice_type, tb_invoice_type.detail as invoice_type_detail"
	selectedFields += ", tb_invoice.invoice_at as invoice_at, tb_invoice.expire_at as expire_at, tb_invoice.paid_at as paid_at, tb_invoice.payment_detail as payment_detail"
	selectedFields += ", tb_invoice.confirm_by as confirm_by, tb_invoice.confirm_at as confirm_at"
	selectedFields += ", tb_invoice.bank_id AS bank_id, tb_invoice.account_no AS account_no, tb_invoice.account_name AS account_name, tb_bank.code as bank_code"
	selectedFields += ", tb_invoice.package_id as package_id, tb_invoice.package_detail as package_detail, tb_invoice.renew_days as renew_days, tb_invoice.renew_credit_amount as renew_credit_amount"
	selectedFields += ", tb_invoice.status_id as status_id, tb_invoice_status.name as status, tb_invoice_status.detail as status_detail"
	selectedFields += ", tb_invoice.sum_price as sum_price, tb_invoice.vat_percent as vat_percent, tb_invoice.vat_price as vat_price"
	selectedFields += ", tb_invoice.discount_price as discount_price, tb_invoice.total_price as total_price"
	selectedFields += ", tb_invoice.slip_image_path as slip_image_path"
	selectedFields += ", tb_invoice.created_at as created_at, tb_invoice.updated_at as updated_at"
	query := r.db.Table("invoice as tb_invoice")
	query = query.Select(selectedFields)
	query = query.Joins("LEFT JOIN renewal_web_master as tb_webs ON tb_webs.id = tb_invoice.web_id")
	query = query.Joins("LEFT JOIN invoice_type as tb_invoice_type ON tb_invoice_type.id = tb_invoice.invoice_type_id")
	query = query.Joins("LEFT JOIN invoice_status as tb_invoice_status ON tb_invoice_status.id = tb_invoice.status_id")
	query = query.Joins("LEFT JOIN bank as tb_bank ON tb_bank.id = tb_invoice.bank_id")
	query = query.Where("tb_invoice.id = ?", id)
	if err := query.Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) ViewBillById(id int64) (*model.InvoiceBillResponse, error) {

	var record model.InvoiceBillResponse

	selectedFields := "tb_invoice.id as id, tb_invoice.web_id as web_id, tb_invoice.web_name as web_name, tb_webs.web_domain as web_domain, tb_invoice.invoice_no as invoice_no"
	selectedFields += ", tb_invoice.invoice_type_id as invoice_type_id, tb_invoice_type.name as invoice_type, tb_invoice_type.detail as invoice_type_detail"
	selectedFields += ", tb_invoice.invoice_at as invoice_at, tb_invoice.expire_at as expire_at, tb_invoice.paid_at as paid_at, tb_invoice.payment_detail as payment_detail"
	selectedFields += ", tb_invoice.confirm_by as confirm_by, tb_invoice.confirm_at as confirm_at"
	selectedFields += ", tb_invoice.bank_id AS bank_id, tb_invoice.account_no AS account_no, tb_invoice.account_name AS account_name"
	selectedFields += ", IF(tb_invoice.invoice_type_id = 1, tb_invoice.renew_days, tb_invoice.renew_credit_amount) AS renew_amount"
	selectedFields += ", tb_invoice.package_id as package_id, tb_invoice.package_detail as package_detail"
	selectedFields += ", tb_invoice.status_id as status_id, tb_invoice_status.name as status, tb_invoice_status.detail as status_detail"
	selectedFields += ", tb_invoice.sum_price as sum_price, tb_invoice.vat_percent as vat_percent, tb_invoice.vat_price as vat_price"
	selectedFields += ", tb_invoice.discount_price as discount_price, tb_invoice.total_price as total_price"
	selectedFields += ", tb_invoice.created_at as created_at, tb_invoice.updated_at as updated_at"
	query := r.db.Table("invoice as tb_invoice")
	query = query.Select(selectedFields)
	query = query.Joins("LEFT JOIN renewal_web_master as tb_webs ON tb_webs.id = tb_invoice.web_id")
	query = query.Joins("LEFT JOIN invoice_type as tb_invoice_type ON tb_invoice_type.id = tb_invoice.invoice_type_id")
	query = query.Joins("LEFT JOIN invoice_status as tb_invoice_status ON tb_invoice_status.id = tb_invoice.status_id")
	query = query.Where("tb_invoice.id = ?", id)
	if err := query.Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) ViewReceiptById(id int64) (*model.InvoiceReceiptResponse, error) {

	var record model.InvoiceReceiptResponse

	selectedFields := "tb_invoice.id as id, tb_invoice.web_id as web_id, tb_invoice.web_name as web_name, tb_webs.web_domain as web_domain, tb_invoice.invoice_no as invoice_no"
	selectedFields += ", tb_invoice.invoice_type_id as invoice_type_id, tb_invoice_type.name as invoice_type, tb_invoice_type.detail as invoice_type_detail"
	selectedFields += ", tb_invoice.invoice_at as invoice_at, tb_invoice.expire_at as expire_at, tb_invoice.paid_at as paid_at, tb_invoice.payment_detail as payment_detail"
	selectedFields += ", tb_invoice.confirm_by as confirm_by, tb_invoice.confirm_at as confirm_at"
	selectedFields += ", tb_invoice.bank_id AS bank_id, tb_invoice.account_no AS account_no, tb_invoice.account_name AS account_name"
	selectedFields += ", IF(tb_invoice.invoice_type_id = 1, tb_invoice.renew_days, tb_invoice.renew_credit_amount) AS renew_amount"
	selectedFields += ", tb_invoice.package_id as package_id, tb_invoice.package_detail as package_detail"
	selectedFields += ", tb_invoice.status_id as status_id, tb_invoice_status.name as status, tb_invoice_status.detail as status_detail"
	selectedFields += ", tb_invoice.sum_price as sum_price, tb_invoice.vat_percent as vat_percent, tb_invoice.vat_price as vat_price"
	selectedFields += ", tb_invoice.discount_price as discount_price, tb_invoice.total_price as total_price"
	selectedFields += ", tb_invoice.created_at as created_at, tb_invoice.updated_at as updated_at"
	query := r.db.Table("invoice as tb_invoice")
	query = query.Select(selectedFields)
	query = query.Joins("LEFT JOIN renewal_web_master as tb_webs ON tb_webs.id = tb_invoice.web_id")
	query = query.Joins("LEFT JOIN invoice_type as tb_invoice_type ON tb_invoice_type.id = tb_invoice.invoice_type_id")
	query = query.Joins("LEFT JOIN invoice_status as tb_invoice_status ON tb_invoice_status.id = tb_invoice.status_id")
	query = query.Where("tb_invoice.id = ?", id)
	if err := query.Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) ConfirmPayment(body model.InvoiceConfirmPaymentBody) error {

	updateBody := map[string]interface{}{}
	updateBody["paid_at"] = body.PaidAt
	updateBody["paid_by"] = body.PaidBy
	updateBody["slip_image_path"] = body.SlipImagePath
	updateBody["raw_qr_code"] = body.RawQrCode
	updateBody["status_id"] = model.INVOICE_STATUS_WAIT_CONFIRM

	query := r.db.Table("invoice").Where("id = ?", body.Id).Where("status_id = ?", model.INVOICE_STATUS_WAIT_PAYMENT)
	if err := query.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) ConfirmInvoice(body model.InvoiceConfirmBody) error {

	updateBody := map[string]interface{}{}
	updateBody["confirm_at"] = body.ConfirmAt
	updateBody["confirm_by_name"] = body.ConfirmByName
	updateBody["status_id"] = model.INVOICE_STATUS_COMPLETED
	updateBody["discount_price"] = body.DiscountPrice
	updateBody["total_price"] = body.TotalPrice

	query := r.db.Table("invoice").Where("id = ?", body.Id).Where("status_id = ?", model.INVOICE_STATUS_WAIT_CONFIRM)
	if err := query.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) RejectInvoice(body model.InvoiceConfirmBody) error {

	updateBody := map[string]interface{}{}
	updateBody["confirm_at"] = body.ConfirmAt
	updateBody["confirm_by_name"] = body.ConfirmByName
	updateBody["status_id"] = model.INVOICE_STATUS_REJECTED

	query := r.db.Table("invoice").Where("id = ?", body.Id).Where("status_id = ?", model.INVOICE_STATUS_WAIT_CONFIRM)
	if err := query.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}
