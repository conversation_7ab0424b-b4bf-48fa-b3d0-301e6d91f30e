package repository

import (
	"cybergame-api/model"
	"fmt"
	"math"
	"strconv"
	"time"

	"gorm.io/gorm"
)

func NewAccountingReportRepository(db *gorm.DB) AccountingReportRepository {
	return &repo{db}
}

type AccountingReportRepository interface {
	// Summary report
	GetSummaryReportAccountList() ([]model.FastBankAccountResponse, int64, error)
	GetUserReportDaily(req model.ReportSummaryRequest) (*model.ReportSummaryResponse, error)
	GetAccountingReportDaily(req model.ReportSummaryRequest) (*model.ReportSummaryResponse, error)
	GetUserAccountingReportDaily(req model.ReportSummaryRequest) (*model.ReportSummaryResponse, error)
	GetUserIncomeReportDaily(req model.ReportSummaryRequest) (*model.ReportSummaryResponse, error)
	// UNUSED GetPromotionReportDaily(req model.ReportSummaryRequest) (*model.ReportPromotionBonusResponse, error)
	GetUserCreditReportDaily(req model.ReportSummaryRequest) (*model.ReportSummaryResponse, error)
	GetUserTodayPlaylogReportDaily(req model.ReportSummaryRequest) (*model.ReportSummaryResponse, error)
	GetActivitySummaryReportDaily(req model.ReportSummaryRequest) (*model.ReportSummaryResponse, error)

	// back up
	ManualBackupGetUserReportDaily(req model.ReportSummaryRequest) ([]model.GetUserReportDailyNewUser, []model.GetUserReportDailyLoginCount, error)
	ManualBackupGetUserAccountingReportDaily(req model.ReportSummaryRequest) ([]model.GetUserReportDailyFirstDeposit, []model.GetUserReportDailyDeposit, []model.GetUserReportDailyWithdraw, []model.GetUserReportDailyCancelCredit, error)
	ManualBackupGetUserIncomeReportDaily(req model.ReportSummaryRequest) ([]model.GetUserReportDailyWithdrawInfo, error)
	ManualBackupGetUserCreditReportDaily(req model.ReportSummaryRequest) ([]model.GetUserReportDailyProfit, error)
	ManualBackupGetUserTodayPlaylogReportDaily(req model.ReportSummaryRequest) ([]model.GetUserReportDailyPlayLog, error)
	ManualBackupGetActivitySummaryReportDaily(req model.ReportSummaryRequest) ([]model.GetUserReportActivitySummaryReportDaily, error)

	CreateReportByCronjob(createBody []model.CreateReportSummary) error
	UpdateReportByCronjob(updateBody model.UpdateReportSummary) error
	GetReportSummaryList(req model.ReportSummaryRequest) ([]model.GetReportSummaryList, error)

	GetReportSummaryListTotal(req model.ReportSummaryRequest) (*model.GetReportSummaryListTotal, error)
	GetDateFromDateType(req model.DateTypeResponse) (*model.DateTypeResponse, error)

	GetUserCreditSummaryTotal() (*model.TotalSumUserActiveCredit, error)

	CreateReportSummaryDashboardRerun(createBody model.CreateReportSummaryDashboardRerun) error
	CheckAlreadyExistReportSummaryDashboardRerun(actionKey string) (bool, error)
}

func (r repo) GetSummaryReportAccountList() ([]model.FastBankAccountResponse, int64, error) {

	// Only Fastbank Accounts //
	var list []model.FastBankAccountResponse
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("bank_account as accounts")
	count = count.Select("accounts.id")
	// count = count.Where("device_uid != ?", "")
	// count = count.Where("pin_code != ?", "")
	count = count.Where("external_id IS NOT NULL")
	// P.Mink Confirm เอาแค่รายการ ที่ เลือกเป็น แสดงในหน้าเว็บ กับ สถานะเชื่อมต่อ
	// count = count.Where("is_show_front = ? AND connection_status_id = ?", 1, model.CONNECTION_CONNECTED)
	// P.lay ******** ขอ แก เป็น ดัก แยก is_show_front กับ connection_status_id
	count = count.Where("is_show_front = ? OR connection_status_id = ?", 1, model.CONNECTION_CONNECTED)
	count = count.Where("accounts.account_type_id IN ?", []int64{1, 2, 3}) // ฝาก+ถอน
	if err = count.
		Where("accounts.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "accounts.id, accounts.bank_id, accounts.account_type_id, accounts.account_name, accounts.account_number, accounts.account_balance, accounts.device_uid, accounts.pin_code "
		selectedFields += ", accounts.last_conn_update_at, accounts.created_at, accounts.admin_updated_at as updated_at"
		selectedFields += ", banks.name as bank_name, banks.code as bank_code, banks.icon_url as bank_icon_url, banks.type_flag"
		selectedFields += ", account_types.name as account_type_name, account_types.limit_flag"
		selectedFields += ", accounts.sms_mode"
		selectedFields += ", accounts.connection_status_id, connection_status.name as connection_status_name"
		query := r.db.Table("bank_account as accounts")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN bank AS banks ON banks.id = accounts.bank_id")
		query = query.Joins("LEFT JOIN bank_account_type AS account_types ON account_types.id = accounts.account_type_id")
		query = query.Joins("LEFT JOIN connection_status AS connection_status ON connection_status.id = accounts.connection_status_id")
		// P.lay ******** smsmode ไม่ต้องมี PIN + DeviceUID
		// query = query.Where("device_uid != ?", "")
		// query = query.Where("pin_code != ?", "")
		query = query.Where("external_id IS NOT NULL")
		// P.Mink Confirm เอาแค่รายการ ที่ เลือกเป็น แสดงในหน้าเว็บ กับ สถานะเชื่อมต่อ
		// query = query.Where("is_show_front = ? AND connection_status_id = ?", 1, model.CONNECTION_CONNECTED)
		// P.lay ******** ขอ แก เป็น ดัก แยก is_show_front กับ connection_status_id
		query = query.Where("is_show_front = ? OR connection_status_id = ?", 1, model.CONNECTION_CONNECTED)
		query = query.Where("account_type_id IN ?", []int64{1, 2, 3}) // ฝาก+ถอน
		if err = query.
			Where("accounts.deleted_at IS NULL").
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetUserReportDaily(req model.ReportSummaryRequest) (*model.ReportSummaryResponse, error) {

	var result model.ReportSummaryResponse

	// Query Date daily, yesterday, last_week, last_month
	// Query Date daily, yesterday, last_week, last_month
	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.DateFrom,
		DateTo:   req.DateTo,
	})
	if err != nil {
		return nil, err
	}
	// use Bangkok timezone as new date
	startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
	if err != nil {
		return nil, err
	}
	endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
	if err != nil {
		return nil, err
	}

	result.DateFrom = req.DateFrom
	result.DateTo = req.DateTo

	// 1.ในวงเล็บ ( ) หมายถึง แสดงยอดสมาชิกทั้งหมดที่มี
	var totalUserCount int64
	var totalUserCountQuery = r.db.Table("user as users")
	totalUserCountQuery = totalUserCountQuery.Select("COUNT(users.id)")
	totalUserCountQuery = totalUserCountQuery.Where("users.deleted_at IS NULL")
	if err := totalUserCountQuery.Count(&totalUserCount).Error; err != nil {
		return nil, err
	}

	// 2.217 แสดงจำนวนสมาชิกสมัครมาในช่วงเวลาที่เลือก
	var totalNewUserCount int64
	var totalNewUserCountQuery = r.db.Table("user as users")
	totalNewUserCountQuery = totalNewUserCountQuery.Select("COUNT(users.id)")
	totalNewUserCountQuery = totalNewUserCountQuery.Where("users.created_at BETWEEN ? AND ?", startDateAtBkk, endDateAtBkk)
	totalNewUserCountQuery = totalNewUserCountQuery.Where("users.deleted_at IS NULL")
	if err := totalNewUserCountQuery.Count(&totalNewUserCount).Error; err != nil {
		return nil, err
	}

	// 3.401 แสดงยอดสมาชิกใช้งาน วัดจากจาก Login ในช่วงขณะนั้นตามตัวกรอง
	var totalLoginCount int64
	totalLoginCountQuery := r.db.Table("user_login_log as logs")
	totalLoginCountQuery = totalLoginCountQuery.Select("COUNT(DISTINCT logs.user_id)")
	totalLoginCountQuery = totalLoginCountQuery.Joins("LEFT JOIN user AS tb_users ON tb_users.id = logs.user_id")
	totalLoginCountQuery = totalLoginCountQuery.Where("logs.created_at BETWEEN ? AND ?", startDateAtBkk, endDateAtBkk)
	// ยอดใช้งาน [20240103] นับเฉพาะ Users ที่มีรหัสสมาชิกแล้วเท่านั้น
	totalLoginCountQuery = totalLoginCountQuery.Where("tb_users.member_code IS NOT NULL")
	totalLoginCountQuery = totalLoginCountQuery.Where("tb_users.deleted_at IS NULL")
	if err := totalLoginCountQuery.Count(&totalLoginCount).Error; err != nil {
		return nil, err
	}

	result.TotalUserCount = totalUserCount
	result.TotalNewUserCount = totalNewUserCount
	result.TotalActiveUserCount = totalLoginCount

	return &result, nil
}

func (r repo) GetAccountingReportDaily(req model.ReportSummaryRequest) (*model.ReportSummaryResponse, error) {

	var result model.ReportSummaryResponse

	// Query Date daily, yesterday, last_week, last_month
	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.DateFrom,
		DateTo:   req.DateTo,
	})
	if err != nil {
		return nil, err
	}

	// use Bangkok timezone as new date
	startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
	if err != nil {
		return nil, err
	}
	endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
	if err != nil {
		return nil, err
	}

	fistDepositInfo := map[string]interface{}{
		"total_deposit_user_count": 0,
		"total_deposit_price":      0,
	}
	firstDepQuery := r.db.Table("bank_transaction as logs")
	firstDepQuery = firstDepQuery.Select("COUNT(DISTINCT logs.user_id) as total_deposit_user_count, SUM(logs.credit_amount) as total_deposit_price")
	firstDepQuery = firstDepQuery.Where("logs.type_id = ?", model.CREDIT_TYPE_DEPOSIT)
	firstDepQuery = firstDepQuery.Where("logs.confirmed_at BETWEEN ? AND ?", startDateAtBkk, endDateAtBkk)
	firstDepQuery = firstDepQuery.Where("logs.transaction_status_id = ?", model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED)
	firstDepQuery = firstDepQuery.Where("logs.is_first_deposit = ?", true)
	firstDepQuery = firstDepQuery.Where("logs.deleted_at IS NULL")
	if err := firstDepQuery.Take(&fistDepositInfo).Error; err != nil {
		return nil, err
	}
	result.TotalFirstDepositUserCount = int64(fistDepositInfo["total_deposit_user_count"].(int64))
	// check is null
	if fistDepositInfo["total_deposit_price"] != nil {
		totalTurnoverAmount, err := strconv.ParseFloat(fistDepositInfo["total_deposit_price"].(string), 64)
		if err != nil {
			return nil, err
		}
		result.TotalFirstDepositPrice = totalTurnoverAmount
	}

	// Profit แสดงยอดเงินกำไรสุทธิ = ยอดฝาก - ยอดถอน
	// transaction_type_id 1 = deposit, 2 = withdraw
	depositInfo := map[string]interface{}{
		"total_deposit_price": 0,
		"total_deposit_count": 0,
	}
	var query = r.db.Table("bank_transaction as logs")
	query = query.Select("COUNT(*) as total_deposit_count, SUM(logs.credit_amount) as total_deposit_price")
	query = query.Where("logs.transaction_type_id = ?", model.CREDIT_TYPE_DEPOSIT)
	query = query.Where("logs.confirmed_at BETWEEN ? AND ?", startDateAtBkk, endDateAtBkk)
	query = query.Where("logs.transaction_status_id = ?", model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED)
	query = query.Where("logs.deleted_at IS NULL")
	if err := query.Take(&depositInfo).Error; err != nil {
		return nil, err
	}
	if depositInfo["total_deposit_price"] != nil {
		depositPrice, err := strconv.ParseFloat(depositInfo["total_deposit_price"].(string), 64)
		if err != nil {
			return nil, err
		}
		result.TotalDepositPrice = math.Floor(depositPrice*100) / 100
	}
	result.TotalDepositUserCount = int64(depositInfo["total_deposit_count"].(int64))

	// withdraw
	withdrawInfo := map[string]interface{}{
		"total_withdraw_price":     0,
		"total_withdraw_count":     0,
		"total_cancel_credit_back": 0,
	}
	queryWithdraw := r.db.Table("bank_transaction as logs")
	queryWithdraw = queryWithdraw.Select("COUNT(*) as total_withdraw_count, SUM(logs.credit_amount) as total_withdraw_price")
	queryWithdraw = queryWithdraw.Where("logs.transaction_type_id = ?", model.CREDIT_TYPE_WITHDRAW)
	queryWithdraw = queryWithdraw.Where("logs.confirmed_at BETWEEN ? AND ?", startDateAtBkk, endDateAtBkk)
	queryWithdraw = queryWithdraw.Where("logs.transaction_status_id = ?", model.TRANS_STATUS_WITHDRAW_SUCCESS)
	queryWithdraw = queryWithdraw.Where("logs.deleted_at IS NULL")
	if err := queryWithdraw.Take(&withdrawInfo).Error; err != nil {
		return nil, err
	}
	if withdrawInfo["total_withdraw_price"] != nil {
		withdrawPrice, err := strconv.ParseFloat(withdrawInfo["total_withdraw_price"].(string), 64)
		if err != nil {
			return nil, err
		}
		result.TotalWithdrawPrice = math.Floor(withdrawPrice*100) / 100
	}
	result.TotalWithdrawUserCount = int64(withdrawInfo["total_withdraw_count"].(int64))

	// cancelCreditInfo
	cancelCreditInfo := map[string]interface{}{
		"total_cancel_credit_back": 0,
	}
	selectedFields2 := "SUM(logs.credit_back) AS total_cancel_credit_back"
	queryCancelCredit := r.db.Table("user_transaction as logs")
	queryCancelCredit = queryCancelCredit.Select(selectedFields2)
	queryCancelCredit = queryCancelCredit.Where("logs.type_id = ?", model.CREDIT_TYPE_CANCEL_CREDIT)
	queryCancelCredit = queryCancelCredit.Where("logs.transfer_at BETWEEN ? AND ?", startDateAtBkk, endDateAtBkk)
	queryCancelCredit = queryCancelCredit.Where("logs.is_show = ?", true)
	queryCancelCredit = queryCancelCredit.Where("logs.removed_at IS NULL")
	if err := queryCancelCredit.Take(&cancelCreditInfo).Error; err != nil {
		return nil, err
	}
	if cancelCreditInfo["total_cancel_credit_back"] != nil {
		cancelPrice, err := strconv.ParseFloat(cancelCreditInfo["total_cancel_credit_back"].(string), 64)
		if err != nil {
			return nil, err
		}
		result.TotalCancelCreditBack = math.Floor(cancelPrice*100) / 100
	}

	// [********] ยกเลิกเติมเครดิต = หักลบออกจากยอดฝาก
	result.TotalDepositPrice = result.TotalDepositPrice - result.TotalCancelCreditBack

	result.TotalProfitPrice = (math.Round((result.TotalDepositPrice)*100) - math.Round((result.TotalWithdrawPrice)*100)) / 100

	return &result, nil
}

func (r repo) GetUserAccountingReportDaily(req model.ReportSummaryRequest) (*model.ReportSummaryResponse, error) {

	var result model.ReportSummaryResponse

	// Query Date daily, yesterday, last_week, last_month
	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.DateFrom,
		DateTo:   req.DateTo,
	})
	if err != nil {
		return nil, err
	}

	// use Bangkok timezone as new date
	startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
	if err != nil {
		return nil, err
	}
	endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
	if err != nil {
		return nil, err
	}

	fistDepositInfo := map[string]interface{}{
		"total_deposit_user_count": 0,
		"total_deposit_price":      0,
	}
	firstDepQuery := r.db.Table("user_transaction as logs")
	firstDepQuery = firstDepQuery.Select("COUNT(DISTINCT logs.user_id) as total_deposit_user_count, SUM(logs.credit_amount) as total_deposit_price")
	firstDepQuery = firstDepQuery.Joins("LEFT JOIN bank_transaction AS tb_bank_logs ON tb_bank_logs.id = logs.ref_id")
	firstDepQuery = firstDepQuery.Where("logs.type_id = ?", model.CREDIT_TYPE_DEPOSIT)
	firstDepQuery = firstDepQuery.Where("logs.transfer_at BETWEEN ? AND ?", startDateAtBkk, endDateAtBkk)
	firstDepQuery = firstDepQuery.Where("tb_bank_logs.is_first_deposit = ?", true)
	firstDepQuery = firstDepQuery.Where("logs.is_show = ?", true)
	firstDepQuery = firstDepQuery.Where("logs.removed_at IS NULL")
	if err := firstDepQuery.Take(&fistDepositInfo).Error; err != nil {
		return nil, err
	}
	result.TotalFirstDepositUserCount = int64(fistDepositInfo["total_deposit_user_count"].(int64))
	// check is null
	if fistDepositInfo["total_deposit_price"] != nil {
		totalTurnoverAmount, err := strconv.ParseFloat(fistDepositInfo["total_deposit_price"].(string), 64)
		if err != nil {
			return nil, err
		}
		result.TotalFirstDepositPrice = totalTurnoverAmount
	}

	// Profit แสดงยอดเงินกำไรสุทธิ = ยอดฝาก - ยอดถอน
	// transaction_type_id 1 = deposit, 2 = withdraw
	depositInfo := map[string]interface{}{
		"total_deposit_price": 0,
		"total_deposit_count": 0,
	}
	var query = r.db.Table("user_transaction as logs")
	query = query.Select("COUNT(*) as total_deposit_count, SUM(logs.credit_amount) as total_deposit_price")
	query = query.Where("logs.type_id = ?", model.CREDIT_TYPE_DEPOSIT)
	query = query.Where("logs.transfer_at BETWEEN ? AND ?", startDateAtBkk, endDateAtBkk)
	query = query.Where("logs.is_show = ?", true)
	query = query.Where("logs.removed_at IS NULL")
	if err := query.Take(&depositInfo).Error; err != nil {
		return nil, err
	}
	if depositInfo["total_deposit_price"] != nil {
		depositPrice, err := strconv.ParseFloat(depositInfo["total_deposit_price"].(string), 64)
		if err != nil {
			return nil, err
		}
		result.TotalDepositPrice = math.Floor(depositPrice*100) / 100
	}
	result.TotalDepositUserCount = int64(depositInfo["total_deposit_count"].(int64))

	// withdraw
	withdrawInfo := map[string]interface{}{
		"total_withdraw_price": 0,
		"total_withdraw_count": 0,
	}
	selectedFields := "COUNT(*) as total_withdraw_count, SUM(logs.credit_amount) AS total_withdraw_price"
	queryWithdraw := r.db.Table("user_transaction as logs")
	queryWithdraw = queryWithdraw.Select(selectedFields)
	queryWithdraw = queryWithdraw.Where("logs.type_id = ?", model.CREDIT_TYPE_WITHDRAW)
	queryWithdraw = queryWithdraw.Where("logs.transfer_at BETWEEN ? AND ?", startDateAtBkk, endDateAtBkk)
	queryWithdraw = queryWithdraw.Where("logs.is_show = ?", true)
	queryWithdraw = queryWithdraw.Where("logs.removed_at IS NULL")
	if err := queryWithdraw.Take(&withdrawInfo).Error; err != nil {
		return nil, err
	}
	if withdrawInfo["total_withdraw_price"] != nil {
		withdrawPrice, err := strconv.ParseFloat(withdrawInfo["total_withdraw_price"].(string), 64)
		if err != nil {
			return nil, err
		}
		result.TotalWithdrawPrice = math.Floor(withdrawPrice*100) / 100
	}
	result.TotalWithdrawUserCount = int64(withdrawInfo["total_withdraw_count"].(int64))

	// cancelCreditInfo
	cancelCreditInfo := map[string]interface{}{
		"total_cancel_credit_back": 0,
	}
	selectedFields2 := "SUM(logs.credit_back) AS total_cancel_credit_back"
	queryCancelCredit := r.db.Table("user_transaction as logs")
	queryCancelCredit = queryCancelCredit.Select(selectedFields2)
	queryCancelCredit = queryCancelCredit.Where("logs.type_id = ?", model.CREDIT_TYPE_CANCEL_CREDIT)
	queryCancelCredit = queryCancelCredit.Where("logs.transfer_at BETWEEN ? AND ?", startDateAtBkk, endDateAtBkk)
	queryCancelCredit = queryCancelCredit.Where("logs.is_show = ?", true)
	queryCancelCredit = queryCancelCredit.Where("logs.removed_at IS NULL")
	if err := queryCancelCredit.Take(&cancelCreditInfo).Error; err != nil {
		return nil, err
	}
	if cancelCreditInfo["total_cancel_credit_back"] != nil {
		cancelPrice, err := strconv.ParseFloat(cancelCreditInfo["total_cancel_credit_back"].(string), 64)
		if err != nil {
			return nil, err
		}
		result.TotalCancelCreditBack = math.Floor(cancelPrice*100) / 100
	}

	// [********] ยกเลิกเติมเครดิต = หักลบออกจากยอดฝาก
	result.TotalDepositPrice = result.TotalDepositPrice - result.TotalCancelCreditBack

	result.TotalBankProfit = (math.Round((result.TotalDepositPrice)*100) - math.Round((result.TotalWithdrawPrice)*100)) / 100

	return &result, nil
}

func (r repo) GetUserIncomeReportDaily(req model.ReportSummaryRequest) (*model.ReportSummaryResponse, error) {

	var result model.ReportSummaryResponse

	// Query Date daily, yesterday, last_week, last_month
	// now := time.Now()
	// if req.DateType == "daily" {
	// 	req.DateFrom = now.Format("2006-01-02")
	// 	req.DateTo = now.Format("2006-01-02")
	// } else if req.DateType == "yesterday" {
	// 	req.DateFrom = now.AddDate(0, 0, -1).Format("2006-01-02")
	// 	req.DateTo = now.AddDate(0, 0, -1).Format("2006-01-02")
	// } else if req.DateType == "last_week" {
	// 	req.DateFrom = now.AddDate(0, 0, -7).Format("2006-01-02")
	// 	req.DateTo = now.Format("2006-01-02")
	// } else if req.DateType == "last_month" {
	// 	// 30 day not exactly 1 month
	// 	req.DateFrom = now.AddDate(0, 0, -30).Format("2006-01-02")
	// 	req.DateTo = now.Format("2006-01-02")
	// }
	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.DateFrom,
		DateTo:   req.DateTo,
	})
	if err != nil {
		return nil, err
	}

	// use Bangkok timezone as new date
	startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
	if err != nil {
		return nil, err
	}
	endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
	if err != nil {
		return nil, err
	}

	// แสดงจำนวนเงินพันธมิตรในช่วงเวลาที่เลือก (จำนวนเงินที่สมาชิกกดรับ/โยกเงินเข้ากระเป๋าหลักแล้ว)
	withdrawInfo := map[string]interface{}{
		"total_return_loss_price": 0.0,
		"total_return_turn_price": 0.0,
		"total_affiliate_price":   0.0,
		"total_alliance_price":    0.0,
	}
	selectedFields := fmt.Sprintf("SUM(IF(logs.type_id = %d, logs.credit_amount + logs.bonus_amount, 0)) as total_return_loss_price", model.CREDIT_TYPE_PROMOTION_RETURN_LOSS)
	selectedFields += fmt.Sprintf(", SUM(IF(logs.type_id = %d, logs.credit_amount + logs.bonus_amount, 0)) as total_affiliate_price", model.CREDIT_TYPE_AFFILIATE_INCOME)
	selectedFields += fmt.Sprintf(", SUM(IF(logs.type_id = %d, logs.credit_amount + logs.bonus_amount, 0)) as total_alliance_price", model.CREDIT_TYPE_ALLIANCE_INCOME)
	selectedFields += fmt.Sprintf(", SUM(IF(logs.type_id = %d, logs.credit_amount + logs.bonus_amount, 0)) as total_return_turn_price", model.CREDIT_TYPE_PROMOTION_RETURN_TURN)
	queryWithdraw := r.db.Table("user_transaction as logs")
	queryWithdraw = queryWithdraw.Select(selectedFields)
	queryWithdraw = queryWithdraw.Where("logs.transfer_at BETWEEN ? AND ?", startDateAtBkk, endDateAtBkk)
	queryWithdraw = queryWithdraw.Where("logs.removed_at IS NULL")
	queryWithdraw = queryWithdraw.Where("logs.is_show = ?", true)
	if err := queryWithdraw.Take(&withdrawInfo).Error; err != nil {
		return nil, err
	}
	if withdrawInfo["total_return_loss_price"] != nil {
		returnPrice, err := strconv.ParseFloat(withdrawInfo["total_return_loss_price"].(string), 64)
		if err != nil {
			return nil, err
		}
		result.TotalReturnLossTakenPrice = math.Floor(returnPrice*100) / 100
	}
	if withdrawInfo["total_affiliate_price"] != nil {
		returnPrice, err := strconv.ParseFloat(withdrawInfo["total_affiliate_price"].(string), 64)
		if err != nil {
			return nil, err
		}
		result.TotalAffiliatePrice = math.Floor(returnPrice*100) / 100
	}
	if withdrawInfo["total_alliance_price"] != nil {
		returnPrice, err := strconv.ParseFloat(withdrawInfo["total_alliance_price"].(string), 64)
		if err != nil {
			return nil, err
		}
		result.TotalAlliancePrice = math.Floor(returnPrice*100) / 100
	}
	if withdrawInfo["total_return_turn_price"] != nil {
		returnPrice, err := strconv.ParseFloat(withdrawInfo["total_return_turn_price"].(string), 64)
		if err != nil {
			return nil, err
		}
		result.TotalReturnTurnTakenPrice = math.Floor(returnPrice*100) / 100
	}
	return &result, nil
}

func (r repo) GetPromotionReportDaily(req model.ReportSummaryRequest) (*model.ReportPromotionBonusResponse, error) {

	var result model.ReportPromotionBonusResponse

	// Query Date daily, yesterday, last_week, last_month
	// now := time.Now()
	// if req.DateType == "daily" {
	// 	req.DateFrom = now.Format("2006-01-02")
	// 	req.DateTo = now.Format("2006-01-02")
	// } else if req.DateType == "yesterday" {
	// 	req.DateFrom = now.AddDate(0, 0, -1).Format("2006-01-02")
	// 	req.DateTo = now.AddDate(0, 0, -1).Format("2006-01-02")
	// } else if req.DateType == "last_week" {
	// 	req.DateFrom = now.AddDate(0, 0, -7).Format("2006-01-02")
	// 	req.DateTo = now.Format("2006-01-02")
	// } else if req.DateType == "last_month" {
	// 	// 30 day not exactly 1 month
	// 	req.DateFrom = now.AddDate(0, 0, -30).Format("2006-01-02")
	// 	req.DateTo = now.Format("2006-01-02")
	// }
	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.DateFrom,
		DateTo:   req.DateTo,
	})
	if err != nil {
		return nil, err
	}

	// use Bangkok timezone as new date
	startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
	if err != nil {
		return nil, err
	}
	endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
	if err != nil {
		return nil, err
	}

	// ค่าคืนยอดเสีย (บาท) จากที่สมาชิกรับจริง
	// turnInfo := map[string]interface{}{
	// 	"total_taken_price": 0,
	// }
	// var query = r.db.Table("promotion_return_loser as logs")
	// query = query.Select("SUM(taken_price) as total_taken_price")
	// query = query.Where("logs.take_at BETWEEN ? AND ?", startDateAtBkk, endDateAtBkk)
	// if err := query.Take(&turnInfo).Error; err != nil {
	// 	return nil, err
	// }
	// if turnInfo["total_taken_price"] != nil {
	// 	takenPrice, err := strconv.ParseFloat(turnInfo["total_taken_price"].(string), 64)
	// 	if err != nil {
	// 		return nil, err
	// 	}
	// 	result.TotalReturnLossTakenPrice = takenPrice
	// }

	// รวมยอดโบนัสฝาก และ รายการโบนัส
	// accountingInfo := map[string]interface{}{
	// 	"total_bonus_price": 0,
	// }
	// bankTransferBonusQuery := r.db.Table("bank_transaction as logs")
	// bankTransferBonusQuery = bankTransferBonusQuery.Select("SUM(logs.bonus_amount) as total_bonus_price")
	// bankTransferBonusQuery = bankTransferBonusQuery.Where("logs.transaction_type_id IN ?", []int64{model.TRANSACTION_TYPE_DEPOSIT, model.TRANSACTION_TYPE_BONUS})
	// bankTransferBonusQuery = bankTransferBonusQuery.Where("logs.confirmed_at BETWEEN ? AND ?", startDateAtBkk, endDateAtBkk)
	// bankTransferBonusQuery = bankTransferBonusQuery.Where("logs.transaction_status_id = ?", model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED)
	// bankTransferBonusQuery = bankTransferBonusQuery.Where("logs.deleted_at IS NULL")
	// if err := bankTransferBonusQuery.Take(&accountingInfo).Error; err != nil {
	// 	return nil, err
	// }
	// // check is null
	// if accountingInfo["total_bonus_price"] != nil {
	// 	bonusPrice, err := strconv.ParseFloat(accountingInfo["total_bonus_price"].(string), 64)
	// 	if err != nil {
	// 		return nil, err
	// 	}
	// 	result.TotalAccountingBonusPrice = bonusPrice
	// }

	// รวมยอดโบนัสจาการแนะนำเพื่อน
	// affiliateInfo := map[string]interface{}{
	// 	"total_bonus_price": 0,
	// }
	// affiliateQuery := r.db.Table("user_transaction as tb_log")
	// affiliateQuery = affiliateQuery.Select("SUM(tb_log.bonus_amount) as total_bonus_price")
	// affiliateQuery = affiliateQuery.Where("tb_log.transfer_at BETWEEN ? AND ?", startDateAtBkk, endDateAtBkk)
	// affiliateQuery = affiliateQuery.Where("tb_log.type_id = ?", model.CREDIT_TYPE_AFFILIATE_INCOME)
	// affiliateQuery = affiliateQuery.Where("tb_log.removed_at IS NULL")
	// affiliateQuery = affiliateQuery.Where("tb_log.is_show = ?", true)
	// if err := affiliateQuery.Take(&affiliateInfo).Error; err != nil {
	// 	return nil, err
	// }
	// // check is null
	// if affiliateInfo["total_bonus_price"] != nil {
	// 	bonusPrice, err := strconv.ParseFloat(affiliateInfo["total_bonus_price"].(string), 64)
	// 	if err != nil {
	// 		return nil, err
	// 	}
	// 	result.TotalAffiliateBonusPrice = bonusPrice
	// }

	// รวมยอดโบนัสจากกิจกรรม TotalActivityBonusPrice
	ActivityInfo := map[string]interface{}{
		"total_bonus_price": 0,
		"total_bonus_count": 0,
	}
	ActivityQuery := r.db.Table("user_transaction AS tb_log")
	ActivityQuery = ActivityQuery.Select("SUM(tb_log.bonus_amount) AS total_bonus_price, COUNT(tb_log.id) AS total_bonus_count")
	ActivityQuery = ActivityQuery.Where("tb_log.transfer_at BETWEEN ? AND ?", startDateAtBkk, endDateAtBkk)
	// ** ดึงทุกโบนัส **
	// ActivityQuery = ActivityQuery.Where("tb_log.type_id IN ?", []int64{model.CREDIT_TYPE_DAILY_ACTIVITY_BONUS, model.CREDIT_TPYE_LUCKY_WHEEL})
	ActivityQuery = ActivityQuery.Where("tb_log.removed_at IS NULL")
	ActivityQuery = ActivityQuery.Where("tb_log.is_show = ?", true)
	if err := ActivityQuery.Take(&ActivityInfo).Error; err != nil {
		return nil, err
	}

	// check is null
	if ActivityInfo["total_bonus_price"] != nil {
		bonusPrice, err := strconv.ParseFloat(ActivityInfo["total_bonus_price"].(string), 64)
		if err != nil {
			return nil, err
		}
		result.TotalActivityBonusPrice = bonusPrice
	}
	if ActivityInfo["total_bonus_count"] != nil {
		bonusCount := ActivityInfo["total_bonus_count"].(int64)
		result.TotalActivityBonusCount = bonusCount
	}

	return &result, nil
}

func (r repo) GetUserCreditReportDaily(req model.ReportSummaryRequest) (*model.ReportSummaryResponse, error) {

	var result model.ReportSummaryResponse

	// Query Date daily, yesterday, last_week, last_month
	// now := time.Now()
	// if req.DateType == "daily" {
	// 	req.DateFrom = now.Format("2006-01-02")
	// 	req.DateTo = now.Format("2006-01-02")
	// } else if req.DateType == "yesterday" {
	// 	req.DateFrom = now.AddDate(0, 0, -1).Format("2006-01-02")
	// 	req.DateTo = now.AddDate(0, 0, -1).Format("2006-01-02")
	// } else if req.DateType == "last_week" {
	// 	req.DateFrom = now.AddDate(0, 0, -7).Format("2006-01-02")
	// 	req.DateTo = now.Format("2006-01-02")
	// } else if req.DateType == "last_month" {
	// 	// 30 day not exactly 1 month
	// 	req.DateFrom = now.AddDate(0, 0, -30).Format("2006-01-02")
	// 	req.DateTo = now.Format("2006-01-02")
	// }
	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.DateFrom,
		DateTo:   req.DateTo,
	})
	if err != nil {
		return nil, err
	}

	// use Bangkok timezone as new date
	startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
	if err != nil {
		return nil, err
	}
	endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
	if err != nil {
		return nil, err
	}

	// แสดงจำนวนดึงเครดิตดึงกลับในช่วงเวลาที่เลือก
	withdrawInfo := map[string]interface{}{
		"total_credit_back_price": 0.0,
		"total_credit_back_count": 0.0,
	}
	selectedFields := "SUM(logs.credit_back) AS total_credit_back_price, COUNT(logs.id) AS total_credit_back_count"
	queryWithdraw := r.db.Table("user_transaction AS logs")
	queryWithdraw = queryWithdraw.Select(selectedFields)
	queryWithdraw = queryWithdraw.Where("logs.type_id = ?", model.CREDIT_TYPE_TAKE_CREDIT_BACK)
	queryWithdraw = queryWithdraw.Where("logs.transfer_at BETWEEN ? AND ?", startDateAtBkk, endDateAtBkk)
	queryWithdraw = queryWithdraw.Where("logs.removed_at IS NULL")
	queryWithdraw = queryWithdraw.Where("logs.is_show = ?", true)
	if err := queryWithdraw.Take(&withdrawInfo).Error; err != nil {
		return nil, err
	}
	if withdrawInfo["total_credit_back_price"] != nil {
		returnPrice, err := strconv.ParseFloat(withdrawInfo["total_credit_back_price"].(string), 64)
		if err != nil {
			return nil, err
		}
		result.TotalCreditBackPrice = math.Floor(returnPrice*100) / 100
	}
	if withdrawInfo["total_credit_back_count"] != nil {
		returnCount := withdrawInfo["total_credit_back_count"].(int64)
		result.TotalCreditBackCount = returnCount
	}

	return &result, nil
}

func (r repo) GetUserTodayPlaylogReportDaily(req model.ReportSummaryRequest) (*model.ReportSummaryResponse, error) {

	var result model.ReportSummaryResponse

	// Query Date daily, yesterday, last_week, last_month
	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.DateFrom,
		DateTo:   req.DateTo,
	})
	if err != nil {
		return nil, err
	}

	// แสดงจำนวนเงินพันธมิตรในช่วงเวลาที่เลือก (จำนวนเงินที่สมาชิกกดรับ/โยกเงินเข้ากระเป๋าหลักแล้ว)
	playlogInfo := map[string]interface{}{
		"total_turn":     0.0,
		"total_win_lose": 0.0,
	}
	selectedFields := "SUM(turn_total) as total_turn, SUM(win_lose_total) as total_win_lose"
	queryPlayLog := r.db.Table("user_today_playlog as logs")
	queryPlayLog = queryPlayLog.Select(selectedFields)
	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return nil, err
		}
		queryPlayLog = queryPlayLog.Where("logs.statement_date >= ?", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return nil, err
		}
		queryPlayLog = queryPlayLog.Where("logs.statement_date <= ?", endDateAtBkk)
	}
	if err := queryPlayLog.Take(&playlogInfo).Error; err != nil {
		return nil, err
	}
	if playlogInfo["total_turn"] != nil {
		returnPrice, err := strconv.ParseFloat(playlogInfo["total_turn"].(string), 64)
		if err != nil {
			return nil, err
		}
		result.TotalTurn = math.Floor(returnPrice*100) / 100
	}
	if playlogInfo["total_win_lose"] != nil {
		returnPrice, err := strconv.ParseFloat(playlogInfo["total_win_lose"].(string), 64)
		if err != nil {
			return nil, err
		}
		result.TotalWinlose = math.Floor(returnPrice*100) / 100
	}
	return &result, nil
}

func (r repo) GetActivitySummaryReportDaily(req model.ReportSummaryRequest) (*model.ReportSummaryResponse, error) {

	// Query Date daily, yesterday, last_week, last_month
	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.DateFrom,
		DateTo:   req.DateTo,
	})
	if err != nil {
		return nil, err
	}
	// const (
	// 	CREDIT_TYPE_DEPOSIT               = 1
	// 	CREDIT_TYPE_WITHDRAW              = 2
	// 	CREDIT_TYPE_BONUS                 = 3
	// 	CREDIT_TYPE_PROMOTION_RETURN_LOSS = 4
	// 	CREDIT_TYPE_AFFILIATE_INCOME      = 5
	// 	CREDIT_TYPE_ALLIANCE_INCOME       = 6
	// 	CREDIT_TYPE_TAKE_CREDIT_BACK      = 7
	// 	CREDIT_TYPE_DAILY_ACTIVITY_BONUS  = 8
	// 	CREDIT_TPYE_LUCKY_WHEEL           = 9
	// 	CREDIT_TYPE_PROMOTION_WEB         = 10
	// 	CREDIT_TYPE_COUPON_CASH           = 11
	// 	CREDIT_TYPE_LOTTERY               = 12
	// 	CREDIT_TYPE_PROMOTION_RETURN_TURN = 13
	// 	CREDIT_TYPE_CANCEL_CREDIT         = 14
	// )
	var result model.ReportSummaryResponse
	// allBonusIds := []int64{
	// 	model.CREDIT_TYPE_BONUS,                 // 3
	// 	model.CREDIT_TYPE_PROMOTION_RETURN_LOSS, // 4
	// 	// model.CREDIT_TYPE_AFFILIATE_INCOME , // 5
	// 	// model.CREDIT_TYPE_ALLIANCE_INCOME,  // 6
	// 	model.CREDIT_TYPE_DAILY_ACTIVITY_BONUS,  // 8
	// 	model.CREDIT_TPYE_LUCKY_WHEEL,           // 9
	// 	model.CREDIT_TYPE_PROMOTION_WEB,         // 10
	// 	model.CREDIT_TYPE_COUPON_CASH,           // 11
	// 	model.CREDIT_TYPE_PROMOTION_RETURN_TURN, // 13
	// }
	// SEVEN_USER_BONUS_INCOME
	selectedFields := "SUM(CASE WHEN income_logs.type_id = 10 THEN income_logs.bonus_amount ELSE 0 END) AS total_promotion_web_credit"
	selectedFields += ", SUM(CASE WHEN income_logs.type_id = 11 THEN income_logs.bonus_amount ELSE 0 END) AS total_promotion_cash_coupon"
	selectedFields += ", SUM(CASE WHEN income_logs.type_id = 3 THEN income_logs.bonus_amount ELSE 0 END) AS total_admin_create_bonus"
	selectedFields += ", SUM(CASE WHEN income_logs.type_id = 9 THEN income_logs.bonus_amount ELSE 0 END) AS total_activity_lucky_wheel"
	selectedFields += ", SUM(CASE WHEN income_logs.type_id = 4 THEN income_logs.bonus_amount ELSE 0 END) AS total_promotion_return_loss"
	selectedFields += ", SUM(CASE WHEN income_logs.type_id = 8 THEN income_logs.bonus_amount ELSE 0 END) AS total_check_in_bonus"
	selectedFields += ", SUM(CASE WHEN income_logs.type_id = 13 THEN income_logs.bonus_amount ELSE 0 END) AS total_promotion_return_turn"
	// TotalBonusCount
	selectedFields += ", COUNT(CASE WHEN income_logs.type_id IN (10, 11, 3, 9, 4, 8, 13) THEN income_logs.id END) AS total_bonus_count"

	query := r.db.Table("user_transaction as income_logs").
		Select(selectedFields).
		Joins("INNER JOIN user as tb_user ON tb_user.id = income_logs.user_id")

	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return nil, err
		}
		query = query.Where("income_logs.transfer_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return nil, err
		}
		query = query.Where("income_logs.transfer_at <=  ?", endDateAtBkk)
	}

	if err := query.Scan(&result).Error; err != nil {
		return nil, err
	}

	return &result, nil
}

func (r repo) ManualBackupGetUserReportDaily(req model.ReportSummaryRequest) ([]model.GetUserReportDailyNewUser, []model.GetUserReportDailyLoginCount, error) {

	// Query Date daily, yesterday, last_week, last_month
	// Query Date daily, yesterday, last_week, last_month
	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.DateFrom,
		DateTo:   req.DateTo,
	})
	if err != nil {
		fmt.Println("dateType", err)
		return nil, nil, err
	}
	// use Bangkok timezone as new date
	startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
	if err != nil {
		return nil, nil, err
	}
	endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
	if err != nil {
		return nil, nil, err
	}

	// Define the struct to store the count
	var totalNewUserCount []model.GetUserReportDailyNewUser
	selectedFields := "DATE(CONVERT_TZ(users.created_at, '+00:00', '+07:00')) as created_date, COUNT(users.id) as total_new_user_count"
	var totalNewUserCountQuery = r.db.Table("user as users")
	totalNewUserCountQuery = totalNewUserCountQuery.Select(selectedFields)
	totalNewUserCountQuery = totalNewUserCountQuery.Where("users.created_at BETWEEN ? AND ?", startDateAtBkk, endDateAtBkk)
	totalNewUserCountQuery = totalNewUserCountQuery.Where("users.deleted_at IS NULL")
	totalNewUserCountQuery = totalNewUserCountQuery.Group("DATE(CONVERT_TZ(users.created_at, '+00:00', '+07:00'))")
	if err := totalNewUserCountQuery.Scan(&totalNewUserCount).Error; err != nil {
		return nil, nil, err
	}

	var totalLoginCount []model.GetUserReportDailyLoginCount
	selectedField2 := "DATE(CONVERT_TZ(logs.created_at, '+00:00', '+07:00')) as created_date, COUNT(DISTINCT logs.user_id) as total_active_user_count"
	totalLoginCountQuery := r.db.Table("user_login_log as logs")
	totalLoginCountQuery = totalLoginCountQuery.Select(selectedField2)
	totalLoginCountQuery = totalLoginCountQuery.Joins("LEFT JOIN user AS tb_users ON tb_users.id = logs.user_id")
	totalLoginCountQuery = totalLoginCountQuery.Where("logs.created_at BETWEEN ? AND ?", startDateAtBkk, endDateAtBkk)
	// ยอดใช้งาน [20240103] นับเฉพาะ Users ที่มีรหัสสมาชิกแล้วเท่านั้น
	totalLoginCountQuery = totalLoginCountQuery.Where("tb_users.member_code IS NOT NULL")
	totalLoginCountQuery = totalLoginCountQuery.Where("tb_users.deleted_at IS NULL")
	totalLoginCountQuery = totalLoginCountQuery.Group("DATE(CONVERT_TZ(logs.created_at, '+00:00', '+07:00'))")
	if err := totalLoginCountQuery.Scan(&totalLoginCount).Error; err != nil {
		return nil, nil, err
	}

	return totalNewUserCount, totalLoginCount, nil
}

func (r repo) ManualBackupGetUserAccountingReportDaily(req model.ReportSummaryRequest) ([]model.GetUserReportDailyFirstDeposit, []model.GetUserReportDailyDeposit, []model.GetUserReportDailyWithdraw, []model.GetUserReportDailyCancelCredit, error) {

	// Query Date daily, yesterday, last_week, last_month
	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.DateFrom,
		DateTo:   req.DateTo,
	})
	if err != nil {
		return nil, nil, nil, nil, err
	}

	// use Bangkok timezone as new date
	startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
	if err != nil {
		return nil, nil, nil, nil, err
	}
	endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
	if err != nil {
		return nil, nil, nil, nil, err
	}

	var fistDepositInfo []model.GetUserReportDailyFirstDeposit
	selectedField1 := "DATE(CONVERT_TZ(logs.transfer_at, '+00:00', '+07:00')) as created_date, COUNT(DISTINCT logs.user_id) as total_first_deposit_user_count, SUM(logs.credit_amount) as total_first_deposit_price"
	firstDepQuery := r.db.Table("user_transaction as logs")
	firstDepQuery = firstDepQuery.Select(selectedField1)
	firstDepQuery = firstDepQuery.Joins("LEFT JOIN bank_transaction AS tb_bank_logs ON tb_bank_logs.id = logs.ref_id")
	firstDepQuery = firstDepQuery.Where("logs.type_id = ?", model.CREDIT_TYPE_DEPOSIT)
	firstDepQuery = firstDepQuery.Where("logs.transfer_at BETWEEN ? AND ?", startDateAtBkk, endDateAtBkk)
	firstDepQuery = firstDepQuery.Where("tb_bank_logs.is_first_deposit = ?", true)
	firstDepQuery = firstDepQuery.Where("logs.is_show = ?", true)
	firstDepQuery = firstDepQuery.Where("logs.removed_at IS NULL")
	firstDepQuery = firstDepQuery.Group("DATE(CONVERT_TZ(logs.transfer_at, '+00:00', '+07:00'))")
	if err := firstDepQuery.Scan(&fistDepositInfo).Error; err != nil {
		return nil, nil, nil, nil, err
	}

	var depositInfo []model.GetUserReportDailyDeposit
	selectedField2 := "DATE(CONVERT_TZ(logs.transfer_at, '+00:00', '+07:00')) as created_date, COUNT(*) as total_deposit_user_count, SUM(logs.credit_amount) as total_deposit_price"
	var query = r.db.Table("user_transaction as logs")
	query = query.Select(selectedField2)
	query = query.Where("logs.type_id = ?", model.CREDIT_TYPE_DEPOSIT)
	query = query.Where("logs.transfer_at BETWEEN ? AND ?", startDateAtBkk, endDateAtBkk)
	query = query.Where("logs.is_show = ?", true)
	query = query.Where("logs.removed_at IS NULL")
	query = query.Group("DATE(CONVERT_TZ(logs.transfer_at, '+00:00', '+07:00'))")
	if err := query.Scan(&depositInfo).Error; err != nil {
		return nil, nil, nil, nil, err
	}

	var withdrawInfo []model.GetUserReportDailyWithdraw
	selectedField3 := "DATE(CONVERT_TZ(logs.transfer_at, '+00:00', '+07:00')) as created_date, COUNT(*) as total_withdraw_user_count, SUM(logs.credit_amount) as total_withdraw_price"
	queryWithdraw := r.db.Table("user_transaction as logs")
	queryWithdraw = queryWithdraw.Select(selectedField3)
	queryWithdraw = queryWithdraw.Where("logs.type_id = ?", model.CREDIT_TYPE_WITHDRAW)
	queryWithdraw = queryWithdraw.Where("logs.transfer_at BETWEEN ? AND ?", startDateAtBkk, endDateAtBkk)
	queryWithdraw = queryWithdraw.Where("logs.is_show = ?", true)
	queryWithdraw = queryWithdraw.Where("logs.removed_at IS NULL")
	queryWithdraw = queryWithdraw.Group("DATE(CONVERT_TZ(logs.transfer_at, '+00:00', '+07:00'))")
	if err := queryWithdraw.Scan(&withdrawInfo).Error; err != nil {
		return nil, nil, nil, nil, err
	}

	var cancelCreditInfo []model.GetUserReportDailyCancelCredit
	selectedField4 := "DATE(CONVERT_TZ(logs.transfer_at, '+00:00', '+07:00')) AS created_date, SUM(logs.credit_back) AS total_cancel_credit_back"
	query4 := r.db.Table("user_transaction AS logs")
	query4 = query4.Select(selectedField4)
	query4 = query4.Where("logs.type_id = ?", model.CREDIT_TYPE_CANCEL_CREDIT)
	query4 = query4.Where("logs.transfer_at BETWEEN ? AND ?", startDateAtBkk, endDateAtBkk)
	query4 = query4.Where("logs.is_show = ?", true)
	query4 = query4.Where("logs.removed_at IS NULL")
	query4 = query4.Group("DATE(CONVERT_TZ(logs.transfer_at, '+00:00', '+07:00'))")
	if err := query4.Scan(&cancelCreditInfo).Error; err != nil {
		return nil, nil, nil, nil, err
	}

	return fistDepositInfo, depositInfo, withdrawInfo, cancelCreditInfo, nil
}

func (r repo) ManualBackupGetUserIncomeReportDaily(req model.ReportSummaryRequest) ([]model.GetUserReportDailyWithdrawInfo, error) {

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.DateFrom,
		DateTo:   req.DateTo,
	})
	if err != nil {
		return nil, err
	}

	// use Bangkok timezone as new date
	startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
	if err != nil {
		return nil, err
	}
	endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
	if err != nil {
		return nil, err
	}

	// แสดงจำนวนเงินพันธมิตรในช่วงเวลาที่เลือก (จำนวนเงินที่สมาชิกกดรับ/โยกเงินเข้ากระเป๋าหลักแล้ว)
	var withdrawInfo []model.GetUserReportDailyWithdrawInfo

	selectedFields := "DATE(CONVERT_TZ(logs.transfer_at, '+00:00', '+07:00')) as created_date"
	selectedFields += fmt.Sprintf(", SUM(IF(logs.type_id = %d, logs.credit_amount + logs.bonus_amount, 0)) as total_return_loss_taken_price", model.CREDIT_TYPE_PROMOTION_RETURN_LOSS)
	selectedFields += fmt.Sprintf(", SUM(IF(logs.type_id = %d, logs.credit_amount + logs.bonus_amount, 0)) as total_affiliate_price", model.CREDIT_TYPE_AFFILIATE_INCOME)
	selectedFields += fmt.Sprintf(", SUM(IF(logs.type_id = %d, logs.credit_amount + logs.bonus_amount, 0)) as total_alliance_price", model.CREDIT_TYPE_ALLIANCE_INCOME)
	selectedFields += fmt.Sprintf(", SUM(IF(logs.type_id = %d, logs.credit_amount + logs.bonus_amount, 0)) as total_return_turn_taken_price", model.CREDIT_TYPE_PROMOTION_RETURN_TURN)
	queryWithdraw := r.db.Table("user_transaction as logs")
	queryWithdraw = queryWithdraw.Select(selectedFields)
	queryWithdraw = queryWithdraw.Where("logs.transfer_at BETWEEN ? AND ?", startDateAtBkk, endDateAtBkk)
	queryWithdraw = queryWithdraw.Where("logs.removed_at IS NULL")
	queryWithdraw = queryWithdraw.Where("logs.is_show = ?", true)
	queryWithdraw = queryWithdraw.Group("DATE(CONVERT_TZ(logs.transfer_at, '+00:00', '+07:00'))")
	if err := queryWithdraw.Scan(&withdrawInfo).Error; err != nil {
		return nil, err
	}
	// if withdrawInfo["total_return_loss_price"] != nil {
	// 	returnPrice, err := strconv.ParseFloat(withdrawInfo["total_return_loss_price"].(string), 64)
	// 	if err != nil {
	// 		return nil, err
	// 	}
	// 	result.TotalReturnLossTakenPrice = math.Floor(returnPrice*100) / 100
	// }
	// if withdrawInfo["total_affiliate_price"] != nil {
	// 	returnPrice, err := strconv.ParseFloat(withdrawInfo["total_affiliate_price"].(string), 64)
	// 	if err != nil {
	// 		return nil, err
	// 	}
	// 	result.TotalAffiliatePrice = math.Floor(returnPrice*100) / 100
	// }
	// if withdrawInfo["total_alliance_price"] != nil {
	// 	returnPrice, err := strconv.ParseFloat(withdrawInfo["total_alliance_price"].(string), 64)
	// 	if err != nil {
	// 		return nil, err
	// 	}
	// 	result.TotalAlliancePrice = math.Floor(returnPrice*100) / 100
	// }

	for _, v := range withdrawInfo {
		// math.Floor(returnPrice*100) / 100
		v.TotalReturnLossTakenPrice = math.Floor(v.TotalReturnLossTakenPrice*100) / 100
		v.TotalAffiliatePrice = math.Floor(v.TotalAffiliatePrice*100) / 100
		v.TotalAlliancePrice = math.Floor(v.TotalAlliancePrice*100) / 100
		v.TotalReturnTurnTakenPrice = math.Floor(v.TotalReturnTurnTakenPrice*100) / 100
	}
	return withdrawInfo, nil
}

func (r repo) ManualBackupGetUserCreditReportDaily(req model.ReportSummaryRequest) ([]model.GetUserReportDailyProfit, error) {

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.DateFrom,
		DateTo:   req.DateTo,
	})
	if err != nil {
		return nil, err
	}

	// use Bangkok timezone as new date
	startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
	if err != nil {
		return nil, err
	}
	endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
	if err != nil {
		return nil, err
	}

	// แสดงจำนวนดึงเครดิตดึงกลับในช่วงเวลาที่เลือก
	// pullBackCredit := map[string]interface{}{
	// 	"total_credit_back_price": 0.0,
	// }

	var pullBackCredit []model.GetUserReportDailyProfit
	// selectedFields := fmt.Sprintf("SUM(IF(logs.type_id = %d, logs.credit_back, 0)) as total_credit_back_price", model.CREDIT_TYPE_TAKE_CREDIT_BACK)

	selectedFields := "DATE(CONVERT_TZ(logs.transfer_at, '+00:00', '+07:00')) AS created_date"
	selectedFields += ", SUM(logs.credit_back) AS total_credit_back_price, COUNT(logs.id) AS total_credit_back_count"
	queryWithdraw := r.db.Table("user_transaction AS logs")
	queryWithdraw = queryWithdraw.Select(selectedFields)
	queryWithdraw = queryWithdraw.Where("logs.type_id IN (?)", model.CREDIT_TYPE_TAKE_CREDIT_BACK)
	queryWithdraw = queryWithdraw.Where("logs.transfer_at BETWEEN ? AND ?", startDateAtBkk, endDateAtBkk)
	queryWithdraw = queryWithdraw.Where("logs.removed_at IS NULL")
	queryWithdraw = queryWithdraw.Where("logs.is_show = ?", true)
	queryWithdraw = queryWithdraw.Group("DATE(CONVERT_TZ(logs.transfer_at, '+00:00', '+07:00'))")
	if err := queryWithdraw.Scan(&pullBackCredit).Error; err != nil {
		return nil, err
	}
	// if pullBackCredit["total_credit_back_price"] != nil {
	// 	returnPrice, err := strconv.ParseFloat(pullBackCredit["total_credit_back_price"].(string), 64)
	// 	if err != nil {
	// 		return nil, err
	// 	}
	// 	result.TotalCreditBackPrice = math.Floor(returnPrice*100) / 100
	// }

	for _, v := range pullBackCredit {
		// math.Floor(returnPrice*100) / 100
		v.TotalCreditBackPrice = math.Floor(v.TotalCreditBackPrice*100) / 100
		// v.TotalCreditBackCount = math.Floor(v.TotalCreditBackCount*100) / 100
	}

	return pullBackCredit, nil
}

func (r repo) ManualBackupGetUserTodayPlaylogReportDaily(req model.ReportSummaryRequest) ([]model.GetUserReportDailyPlayLog, error) {

	// Query Date daily, yesterday, last_week, last_month
	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.DateFrom,
		DateTo:   req.DateTo,
	})
	if err != nil {
		return nil, err
	}

	// Initialize the result slice
	var playlogInfo []model.GetUserReportDailyPlayLog

	// Select the required fields
	selectedFields := "logs.statement_date as statement_date"
	selectedFields += ", SUM(turn_total) as total_turn, SUM(win_lose_total) as total_win_lose"

	// Build the query
	queryPlayLog := r.db.Table("user_today_playlog as logs")
	queryPlayLog = queryPlayLog.Select(selectedFields)

	// Add date filters to the query
	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return nil, err
		}
		queryPlayLog = queryPlayLog.Where("logs.statement_date >= ?", startDateAtBkk.Add(7*time.Hour).Format("2006-01-02"))
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return nil, err
		}
		queryPlayLog = queryPlayLog.Where("logs.statement_date <= ?", endDateAtBkk.Add(7*time.Hour).Format("2006-01-02"))
	}

	// Group by the statement date
	queryPlayLog = queryPlayLog.Group("logs.statement_date")

	// Execute the query but scan the `created_date` as a string (or byte slice) first
	type playlogTemp struct {
		TotalTurn     float64
		TotalWinLose  float64
		StatementDate string // Temporary field to scan the date as a string
	}

	var tempResults []playlogTemp
	if err := queryPlayLog.Scan(&tempResults).Error; err != nil {
		return nil, err
	}

	// Now parse the results and convert the date
	for _, tempLog := range tempResults {
		var logEntry model.GetUserReportDailyPlayLog

		// Parse the created_date string into time.Time
		layout := "2006-01-02" // Adjust layout based on the actual format of logs.statement_date
		logEntry.CreatedDate, err = time.Parse(layout, tempLog.StatementDate)
		if err != nil {
			return nil, fmt.Errorf("error parsing date: %v", err)
		}

		// Assign the other fields
		logEntry.TotalTurn = math.Floor(tempLog.TotalTurn*100) / 100
		logEntry.TotalWinLose = math.Floor(tempLog.TotalWinLose*100) / 100

		// Append to the result
		playlogInfo = append(playlogInfo, logEntry)
	}

	return playlogInfo, nil
}

func (r repo) ManualBackupGetActivitySummaryReportDaily(req model.ReportSummaryRequest) ([]model.GetUserReportActivitySummaryReportDaily, error) {

	// Query Date daily, yesterday, last_week, last_month
	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.DateFrom,
		DateTo:   req.DateTo,
	})
	if err != nil {
		return nil, err
	}

	var list []model.GetUserReportActivitySummaryReportDaily
	// SEVEN_USER_BONUS_INCOME
	selectedFields := " DATE(CONVERT_TZ(income_logs.transfer_at, '+00:00', '+07:00')) as created_date"
	selectedFields += ", SUM(CASE WHEN income_logs.type_id = 10 THEN income_logs.bonus_amount ELSE 0 END) AS total_promotion_web_credit"
	selectedFields += ", SUM(CASE WHEN income_logs.type_id = 11 THEN income_logs.bonus_amount ELSE 0 END) AS total_promotion_cash_coupon"
	selectedFields += ", SUM(CASE WHEN income_logs.type_id = 3 THEN income_logs.bonus_amount ELSE 0 END) AS total_admin_create_bonus"
	selectedFields += ", SUM(CASE WHEN income_logs.type_id = 9 THEN income_logs.bonus_amount ELSE 0 END) AS total_activity_lucky_wheel"
	selectedFields += ", SUM(CASE WHEN income_logs.type_id = 4 THEN income_logs.bonus_amount ELSE 0 END) AS total_promotion_return_loss"
	selectedFields += ", SUM(CASE WHEN income_logs.type_id = 8 THEN income_logs.bonus_amount ELSE 0 END) AS total_check_in_bonus"
	selectedFields += ", SUM(CASE WHEN income_logs.type_id = 13 THEN income_logs.bonus_amount ELSE 0 END) AS total_promotion_return_turn"
	// TotalBonusCount
	selectedFields += ", COUNT(CASE WHEN income_logs.type_id IN (10, 11, 3, 9, 4, 8, 13) THEN income_logs.id END) AS total_bonus_count"

	query := r.db.Table("user_transaction as income_logs").
		Select(selectedFields).
		Joins("INNER JOIN user as tb_user ON tb_user.id = income_logs.user_id")

	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return nil, err
		}
		query = query.Where("income_logs.transfer_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return nil, err
		}
		query = query.Where("income_logs.transfer_at <=  ?", endDateAtBkk)
	}

	query = query.Group("DATE(CONVERT_TZ(income_logs.transfer_at, '+00:00', '+07:00'))")
	if err := query.Scan(&list).Error; err != nil {
		return nil, err
	}

	return list, nil
}

func (r repo) CreateReportByCronjob(createBody []model.CreateReportSummary) error {

	if err := r.db.Table("report_summary_dashboard").Create(&createBody).Error; err != nil {
		return err
	}

	return nil
}

func (r repo) UpdateReportByCronjob(updateBody model.UpdateReportSummary) error {

	if err := r.db.Table("report_summary_dashboard").Where("created_date = ?", updateBody.CreatedDate).Updates(&updateBody).Error; err != nil {
		return err
	}

	return nil
}

func (r repo) GetReportSummaryList(req model.ReportSummaryRequest) ([]model.GetReportSummaryList, error) {

	var result []model.GetReportSummaryList

	query := r.db.Table("report_summary_dashboard")

	selectedField := "id, created_date, total_new_user_count"
	selectedField += ", total_active_user_count, total_first_deposit_price, total_first_deposit_user_count"
	selectedField += ", total_deposit_user_count, total_withdraw_user_count"
	selectedField += ", total_bonus_price, total_bonus_count, total_profit_price, total_deposit_price"
	selectedField += ", total_withdraw_price, total_affiliate_price, total_alliance_price"
	selectedField += ", total_return_loss_taken_price, total_credit_back_price, total_credit_back_count, total_turn"
	selectedField += ", total_winlose, total_bank_profit, total_promotion_web_credit"
	selectedField += ", total_promotion_return_loss, total_activity_lucky_wheel, total_check_in_bonus"
	selectedField += ", total_promotion_return_turn"
	selectedField += ", total_promotion_cash_coupon, total_admin_create_bonus"

	query = query.Select(selectedField)

	if req.DateFrom != "" {
		query = query.Where("created_date >= ?", req.DateFrom)
	}
	if req.DateTo != "" {
		query = query.Where("created_date <= ?", req.DateTo)
	}

	if err := query.Scan(&result).Error; err != nil {
		return nil, err
	}

	return result, nil
}

func (r repo) GetReportSummaryListTotal(req model.ReportSummaryRequest) (*model.GetReportSummaryListTotal, error) {

	var result model.GetReportSummaryListTotal

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.DateFrom,
		DateTo:   req.DateTo,
	})
	if err != nil {
		return nil, err
	}

	query := r.db.Table("report_summary_dashboard")

	selectedField := "SUM(total_new_user_count) as total_new_user_count"
	selectedField += ", SUM(total_active_user_count) as total_active_user_count"
	selectedField += ", SUM(total_first_deposit_price) as total_first_deposit_price"
	selectedField += ", SUM(total_first_deposit_user_count) as total_first_deposit_user_count"
	selectedField += ", SUM(total_deposit_user_count) as total_deposit_user_count"
	selectedField += ", SUM(total_withdraw_user_count) as total_withdraw_user_count"
	selectedField += ", SUM(total_bonus_price) as total_bonus_price"
	selectedField += ", SUM(total_bonus_count) AS total_bonus_count"
	selectedField += ", SUM(total_profit_price) as total_profit_price"
	selectedField += ", SUM(total_deposit_price) as total_deposit_price"
	selectedField += ", SUM(total_withdraw_price) as total_withdraw_price"
	selectedField += ", SUM(total_affiliate_price) as total_affiliate_price"
	selectedField += ", SUM(total_alliance_price) as total_alliance_price"
	selectedField += ", SUM(total_return_loss_taken_price) as total_return_loss_taken_price"
	selectedField += ", SUM(total_return_turn_taken_price) as total_return_turn_taken_price"
	selectedField += ", SUM(total_credit_back_price) as total_credit_back_price"
	selectedField += ", SUM(total_credit_back_count) AS total_credit_back_count"
	selectedField += ", SUM(total_turn) as total_turn"
	selectedField += ", SUM(total_winlose) as total_winlose"
	selectedField += ", SUM(total_bank_profit) as total_bank_profit"
	selectedField += ", SUM(total_promotion_web_credit) as total_promotion_web_credit"
	selectedField += ", SUM(total_promotion_return_loss) as total_promotion_return_loss"
	selectedField += ", SUM(total_activity_lucky_wheel) as total_activity_lucky_wheel"
	selectedField += ", SUM(total_check_in_bonus) as total_check_in_bonus"
	selectedField += ", SUM(total_promotion_return_turn) as total_promotion_return_turn"
	selectedField += ", SUM(total_promotion_cash_coupon) as total_promotion_cash_coupon"
	selectedField += ", SUM(total_admin_create_bonus) as total_admin_create_bonus"

	query = query.Select(selectedField)

	if dateType.DateFrom != "" {
		query = query.Where("created_date >= ?", dateType.DateFrom)
	}
	if dateType.DateTo != "" {
		query = query.Where("created_date <= ?", dateType.DateTo)
	}

	if err := query.Take(&result).Error; err != nil {
		return nil, err
	}

	return &result, nil
}

func (r repo) GetUserCreditSummaryTotal() (*model.TotalSumUserActiveCredit, error) {

	var result model.TotalSumUserActiveCredit

	query := r.db.Table("user")

	selectedField := "SUM(credit) as total_user_active_credit"

	query = query.Select(selectedField)
	query = query.Where("deleted_at IS NULL")

	if err := query.Take(&result).Error; err != nil {
		return nil, err
	}

	return &result, nil
}

func (r repo) CreateReportSummaryDashboardRerun(createBody model.CreateReportSummaryDashboardRerun) error {

	if err := r.db.Table("report_summary_dashboard_rerun").Create(&createBody).Error; err != nil {
		return err
	}

	return nil
}

func (r repo) CheckAlreadyExistReportSummaryDashboardRerun(actionKey string) (bool, error) {
	var count int64

	err := r.db.Table("report_summary_dashboard_rerun").
		Where("action_key = ?", actionKey).
		Count(&count).Error

	if err != nil {
		return false, err
	}

	return count > 0, nil
}
