package repository

import (
	"bytes"
	"cybergame-api/helper"
	"cybergame-api/model"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strings"
	"time"

	"gorm.io/gorm"
)

func NewZappayRepository(db *gorm.DB) ZappayRepository {
	return &repo{db}
}

type ZappayRepository interface {
	GetDb() *gorm.DB
	// Zappay-RD
	// ZappayEncryptRepayDesposit(partnerKey string, amount int64) (*model.ZappayEncryptPayload, error)

	// MERCHANT
	// GetNOTUSEPaygateMerchantById(id int64) (*model.PaygateMerchantNgResponse, error)
	// GetPaygateMerchantList(req model.PaygateMerchantListRequest) ([]model.PaygateMerchantNgResponse, int64, error)
	// CreatePaygateMerchant(body model.PaygateMerchantCreateBody) (*int64, error)
	// UpdatePaygateMerchant(id int64, body model.PaygateMerchantUpdateBody) error
	// DeletePaygateMerchant(id int64) error
	// // SETTING
	// GetPaygateSetting() (*model.PaygateSettingResponse, error)
	// CreatePaygateSetting(body model.PaygateSettingCreateBody) (*int64, error)
	// UpdatePaygateSetting(id int64, body model.PaygateSettingUpdateBody) error
	// DeletePaygateSetting(id int64) error
	// // AdminLog
	// GetPaygateAdminLogById(id int64) (*model.PaygateAdminLogResponse, error)
	// GetPaygateAdminLogList(req model.PaygateAdminLogListRequest) ([]model.PaygateAdminLogResponse, int64, error)
	// CreatePaygateAdminLog(body model.PaygateAdminLogCreateBody) (*int64, error)
	// UpdatePaygateAdminLog(id int64, body model.PaygateAdminLogUpdateBody) error
	// // SystemLog
	CreatePaygateSystemLog(body model.PaygateSystemLogCreateBody) (*int64, error)
	// UpdatePaygateSystemLog(id int64, body model.PaygateSystemLogUpdateBody) error

	// REF-PAYGATE
	GetRawZappayPendingDepositOrderById(id int64) (*model.ZappayOrderResponse, error)
	GetPaygateAccountByProviderId(id int64) (*model.PaygateAccountResponse, error)
	// Zappay-REMOTE
	// ZappayGetToken(setting model.PaygateAccountResponse) (*model.ZappayTokenCreateRemoteResponse, error)
	ZappayDeposit(setting model.PaygateAccountResponse, req model.ZappayDepositCreateRemoteRequest) (*model.ZappayDepositCreateRemoteResponse, error)
	ZappayWithdraw(setting model.PaygateAccountResponse, req model.ZappayWithdrawCreateRemoteRequest) (*model.ZappayWithdrawCreateRemoteResponse, error)
	// ZappayCheckBalance(setting model.PaygateAccountResponse) (*model.ZappayCheckBalanceRemoteResponse, error)
	// ZappayGetOrder(setting model.PaygateAccountResponse, orderNo string) (*model.ZappayGetOrderRemoteResponse, error)
	// ZappayCreateCustomer(setting model.PaygateAccountResponse, req model.ZappayCustomerCreateRemoteRequest) (*model.ZappayCustomerCreateRemoteResponse, error)
	// ZappayUpdateCustomer(setting model.PaygateAccountResponse, req model.ZappayCustomerUpdateRemoteRequest) (*model.ZappayCustomerUpdateRemoteResponse, error)
	// Zappay-Decrypt
	ZappayDecryptRepayDespositPayload(setting model.PaygateAccountResponse, payload model.ZappayWebhookEncryptPayload) (*model.ZappayWebhookDepositResponse, error)
	// Zappay-DB
	CreateZappayWebhook(body model.ZappayWebhookCreateBody) (*int64, error)
	GetDbZappayOrderList(req model.ZappayOrderListRequest) ([]model.ZappayOrderResponse, int64, error)
	GetDbZappayOrderById(id int64) (*model.ZappayOrderResponse, error)
	GetDbZappayOrderByRefId(refId int64) (*model.ZappayOrderResponse, error)
	CheckZappayDepositOrderInLast5Minutes(userId int64, amount float64) (*model.ZappayOrderResponse, error)
	CreateDbZappayOrder(body model.ZappayOrderCreateBody) (*int64, error)
	UpdateDbZappayOrderError(id int64, remark string) error
	UpdateDbZappayOrder(id int64, body model.ZappayOrderUpdateBody) error
	ApproveDbZappayOrder(id int64, webhookStatus string) error
	UpdateZappayOrderBankSucess(id int64, transId int64, actionBy int64) error
	// Token
	// GetDbZappayAccessToken() (*model.ZappayToken, error)
	// CreateDbZappayAccessToken(body model.ZappayTokenCreateBody) (*int64, error)
	// Customer
	// GetZappayCustomerById(id int64) (*model.ZappayCustomerResponse, error)
	// GetZappayCustomerByUserId(userId int64) (*model.ZappayCustomerResponse, error)
	// CheckZappayCustomerByUserId(user model.UserBankDetailBody) (*model.ZappayCustomerResponse, error)
	// GetZappayCustomerList(req model.ZappayCustomerListRequest) ([]model.ZappayCustomerResponse, int64, error)
	// CreateZappayCustomer(body model.ZappayCustomerCreateBody) (*int64, error)
	// UpdateZappayCustomer(id int64, body model.ZappayCustomerUpdateBody) error
	// DeleteZappayCustomer(id int64) error
	// REF-USER
	GetUserById(id int64) (*model.UserResponse, error)
	// REF-MemberCode
	GetUserForGenMember(id int64) (*model.UserDetail, error)
	GetAgentInfo() (*model.AgentInfo, error)
	IncrementTotal() error
	UpdateMemberCode(userId int64, data model.UserUpdateMemberAndRef) error
	AgcRegister(data model.AgcRegister) error
	AmbRegister(data model.AmbRegister) error
	GetMemberById(id int64) (*model.Member, error)
	IsFirstDeposit(userId int64) bool
	UpdateGenMemberCodeToAffilate(userId int64) error
	CreateAffiliateMember(refBy int64, userId int64) error
	// CronjobDeleteZappayWebhook() error
	// REF-RC
	GetRaceActionIdByActionKey(actionKey string) (*int64, error)
	CreateRaceCondition(body model.RaceActionCreateBody) (int64, error)
	// REF-BankTransaction-Deposit
	GetUserBankDetailById(id int64) (*model.UserBankDetailBody, error)
	GetDepositCurrentProcessingUserPromotion(userId int64) (*model.PromotionWebUserByUserIdResponse, error)
	InsertBankTransaction(data model.BankTransactionCreateBody) (*int64, error)
	IncreaseUserCredit(body model.UserTransactionCreateRequest) (*model.UserTransactionCreateResponse, error)
	UpdateDepositTransactionStatusFromAgent(transId int64, body model.UserTransactionCreateResponse) error
	UpdateAutoProcessTimer(timer string, id int64) error
	// ConfirmDeposit
	GetBankTransactionById(id int64) (*model.BankTransaction, error)
	CreateTransactionAction(data model.CreateBankTransactionActionBody) (*int64, error)
	ConfirmPendingDepositTransaction(id int64, body model.BankDepositTransactionConfirmBody) error
	RollbackTransactionAction(actionId int64) error
	// REF-BANK
	GetLastestBankAdminManualDepositTransaction(userId int64) (*model.BankTransaction, error)
	ShowUserTransaction(req model.UserTransactionShowUpdate) error
	UpdateTransactionStatusTransferingToSuccess(id int64, secondUsed string) error
	RollbackTransactionStatusTransferingToConfirmed(id int64) error
	CreateBankTransactionExternalDetail(body model.BankTransactionExternalDetailCreateRequest) (int64, error)
	UpdateUserTransactionStatus(body model.UpdateUserTransactionStatusRequest) error
	GetUserWithdrawCreditTransactionByRefId(transactionId int64, creditAmount float64) (*model.UserTransaction, error)
	// REF-SysLog
	CreateSystemLog(body model.SystemLogCreateBody) (*int64, error)
	// REF-WebConfiguration
	GetWebConfiguration() (*model.GetWebConfigurationBody, error)

	IncreaseUserTierDepositAmount(userId int64, amount float64) error
}

func TestZappayHashSign(appid, mch_id, token, partner_uid string) bool {

	expectedSign := "23451226e60ea90aa631c98127503e8b"

	// {
	// 	"amount": "51",
	// 	"mch_order_no": "signer_tr_319760526",
	// 	"bank_code": "BANK",
	// 	"user_name": "member01",
	// 	"user_mobile": "66",
	// 	"repay_account_number": "************",
	// 	"repay_account_bank": "KBANK",
	// 	"partner_uid": "11111111_2222222222_33333333333",
	// 	"appid": "30005XXXXX",
	// 	"mch_id": 30005X,
	// 	"partner_key": "11111111_2222222222_33333333333",
	// 	"timestamp": **********,
	// 	"sign": "23451226e60ea90aa631c98127503e8b"
	// }
	// {
	// 	"amount": "51",
	// 	"appid": "30005XXXXX",
	// 	"bank_code": "BANK",
	// 	"mch_id": 30005X,
	// 	"mch_order_no": "signer_tr_319760526",
	// 	"partner_key": "11111111_2222222222_33333333333",
	// 	"partner_uid": "11111111_2222222222_33333333333",
	// 	"repay_account_bank": "KBANK",
	// 	"repay_account_number": "************",
	// 	"timestamp": **********,
	// 	"user_mobile": "66",
	// 	"user_name": "member01"
	// }
	payload := make(map[string]interface{})
	payload["amount"] = "51"
	payload["mch_order_no"] = "signer_tr_319760526"
	payload["bank_code"] = "BANK"
	payload["user_name"] = "member01"
	payload["user_mobile"] = "66"
	payload["repay_account_number"] = "************"
	payload["repay_account_bank"] = "KBANK"
	payload["partner_uid"] = partner_uid
	payload["appid"] = appid
	payload["mch_id"] = mch_id
	payload["partner_key"] = partner_uid
	// payload["timestamp"] = **********
	// payload["sign"] = expectedSign

	fmt.Println("payload", helper.StructJson(payload))

	sign1 := (helper.GetMD5Hash(fmt.Sprintf("%s*|*%s@!@%d", token, helper.StructJson(payload), **********)))
	fmt.Println("sign1", sign1)

	sign := helper.GetMD5Hash(helper.GetMD5Hash(fmt.Sprintf("%s*|*%s@!@%d", token, helper.StructJson(payload), **********)))
	fmt.Println("sign", sign)

	if sign == expectedSign {
		fmt.Println("Success")
		return true
	} else {
		fmt.Println("Failed")
	}

	return false
}

func ZappayEncryptRepayDespositPayload(req model.ZappayDepositRequest) (*model.ZappayEncryptPayload, error) {

	actionAt := time.Now()

	var result model.ZappayEncryptPayload
	result.Data.PartnerKey = req.PartnerKey

	// Request interface signature and encryption rules
	// Step 1, generate a signature: sign=md5(md5(token + *|* + json_encode(data) +@!@+ timestamp)) spliced together, data (including public parameters and business
	// parameters of each interface) is the POST request data (excluding signature
	// parameters timestamp and sign). The data content generates a json string in a
	// (lexicographical) manner, and timestamp is the timestamp

	payload := make(map[string]interface{})
	payload["mch_order_no"] = req.OrderNo
	payload["amount"] = fmt.Sprintf("%.2f", req.Amount)
	payload["bank_code"] = req.BankCode
	payload["user_name"] = req.UserFullname
	// ไม่ต้องส่งก็ได้ payload["user_mobile"] = req.UserMobile
	payload["mch_id"] = req.MerchantId
	payload["partner_uid"] = req.PartnerKey
	payload["repay_account_number"] = req.UserAccountNumber
	payload["repay_account_bank"] = req.UserAccountBank
	payload["appid"] = req.RepayAppId
	payload["partner_key"] = req.PartnerKey

	// Step 2: Encrypt the overall data (AES): Encryption involves parameters: method(encryption method): AES-128-CBC aesKey (encryption key): needs to be given by the developer
	// iv (initialization direction, fixed value): mYxiZZhwtvkYAxOV
	// Encrypt all data in json format according to the openssl protocol. Using the methodencryption method, the encrypted ciphertext is generated by key and iv, and the
	// encrypted ciphertext and partner_key are sent to the server together.

	// Example of request parameter content (unencrypted)
	// { "partner_key":"**********", "en_data":{ "appid":*********, "amount":"10000.00", "mch_id":100000, "mch_order_no":"***************", "sign":"ed2a3fa69fa2d6e83e4f052614c93ca9", "timestamp":**********}}

	// Example of request parameter content (encrypted)
	// { "data": { "partner_key": "**********", "en_data": "eUtOJPHoE1/p3SpiQOADbS9LGRXdI8hJGb1ACrx8r9wS2wQftysZsC4H4rNr79Pj5k9fEUFy6X8m+a4L03o/3EiUEQCw6UqsM8ajC53E8quYmCzkD481HdNIrnCqcVrg7I9EWa4XLcTvFa9X3EpjvpCvrGP6n11pl4rIZm1dH28="}}

	sign := helper.GetMD5Hash(helper.GetMD5Hash(fmt.Sprintf("%s*|*%s@!@%d", req.Token, helper.StructJson(payload), actionAt.Unix())))
	payload["timestamp"] = actionAt.Unix()
	payload["sign"] = sign

	encryptData, err := helper.EncryptAES128CBC(req.AesKey, helper.StructJson(payload), "mYxiZZhwtvkYAxOV")
	if err != nil {
		return nil, err
	}
	result.Data.EnData = encryptData

	return &result, nil
}

func (r repo) ZappayDecryptRepayDespositPayload(setting model.PaygateAccountResponse, payload model.ZappayWebhookEncryptPayload) (*model.ZappayWebhookDepositResponse, error) {

	var result model.ZappayWebhookDepositResponse

	// sign := helper.GetMD5Hash(helper.GetMD5Hash(fmt.Sprintf("%s*|*%s@!@%d", req.Token, helper.StructJson(payload), actionAt.Unix())))
	// payload["timestamp"] = actionAt.Unix()
	// payload["sign"] = sign

	output, err := helper.DecryptAES128CBC(setting.AesKey, payload.Data.EnData, "mYxiZZhwtvkYAxOV")
	if err != nil {
		return nil, err
	}

	// fmt.Println("ZappayDecryptRepayDespositPayload.output", output)
	// 2024/08/21 21:02:06 ZappayDecryptRepayDespositPayload.output {"sign":"4370c27e10e3e0826ba7c03b95bf8224","timestamp":**********,"order_no":"************-********-CP","mch_order_no":"PGWAY240834","status":1,"amount":"61.00","pay_time":"2024-08-21 21:02:00","repay_account_bank":"kbank","repay_account_number":"**********","account_number":"**********","account_bank":"KBANK"}
	// 2024/08/21 21:02:06 Unmarshal.Err ------>  invalid character '\b' after top-level value
	// 2024/08/21 21:02:06 INVALID_UNMARSHAL_JSON
	// 2024/08/21 21:02:06 createZappayWithdrawWebhook.ERROR INVALID_UNMARSHAL_JSON

	errJson := json.Unmarshal([]byte(output), &result)
	if errJson != nil {
		log.Println("ZappayDecryptRepayDespositPayload.output", output)
		log.Println("Unmarshal.Err ------> ", errJson)
		return nil, errors.New("INVALID_UNMARSHAL_JSON")
	}

	return &result, nil
}

func ZappayEncryptLoanWithdrawPayload(req model.ZappayDepositRequest) (*model.ZappayEncryptPayload, error) {

	actionAt := time.Now()

	var result model.ZappayEncryptPayload
	result.Data.PartnerKey = req.PartnerKey

	// Request interface signature and encryption rules
	// Step 1, generate a signature: sign=md5(md5(token + *|* + json_encode(data) +@!@+ timestamp)) spliced together, data (including public parameters and business
	// parameters of each interface) is the POST request data (excluding signature
	// parameters timestamp and sign). The data content generates a json string in a
	// (lexicographical) manner, and timestamp is the timestamp
	// {
	// 	"amount": "10.00",
	// 	"bank_code": "BBL",
	// 	"bank_card": "************",
	// 	"user_name": "hang",
	// 	"user_mobile": "**********",
	// 	"mch_order_no": "signer_tl_819270908",
	// 	"appid": "*********",
	// 	"mch_id": 10001
	// }
	payload := make(map[string]interface{})
	payload["mch_order_no"] = req.OrderNo
	payload["amount"] = fmt.Sprintf("%.2f", req.Amount)
	payload["bank_code"] = req.UserAccountBank
	payload["bank_card"] = req.UserAccountNumber
	payload["user_name"] = req.UserFullname
	// ไม่ต้องส่งก็ได้ payload["user_mobile"] = req.UserMobile
	payload["appid"] = req.LoanAppId
	payload["mch_id"] = req.MerchantId
	payload["partner_uid"] = req.PartnerKey
	payload["partner_key"] = req.PartnerKey

	// Step 2: Encrypt the overall data (AES): Encryption involves parameters: method(encryption method): AES-128-CBC aesKey (encryption key): needs to be given by the developer
	// iv (initialization direction, fixed value): mYxiZZhwtvkYAxOV
	// Encrypt all data in json format according to the openssl protocol. Using the methodencryption method, the encrypted ciphertext is generated by key and iv, and the
	// encrypted ciphertext and partner_key are sent to the server together.

	// Example of request parameter content (unencrypted)
	// { "partner_key":"**********", "en_data":{ "appid":*********, "amount":"10000.00", "mch_id":100000, "mch_order_no":"***************", "sign":"ed2a3fa69fa2d6e83e4f052614c93ca9", "timestamp":**********}}

	// Example of request parameter content (encrypted)
	// { "data": { "partner_key": "**********", "en_data": "eUtOJPHoE1/p3SpiQOADbS9LGRXdI8hJGb1ACrx8r9wS2wQftysZsC4H4rNr79Pj5k9fEUFy6X8m+a4L03o/3EiUEQCw6UqsM8ajC53E8quYmCzkD481HdNIrnCqcVrg7I9EWa4XLcTvFa9X3EpjvpCvrGP6n11pl4rIZm1dH28="}}

	sign := helper.GetMD5Hash(helper.GetMD5Hash(fmt.Sprintf("%s*|*%s@!@%d", req.Token, helper.StructJson(payload), actionAt.Unix())))
	payload["timestamp"] = actionAt.Unix()
	payload["sign"] = sign

	// fmt.Println("ZappayEncryptLoanWithdrawPayload.payload", helper.StructJson(payload))

	encryptData, err := helper.EncryptAES128CBC(req.AesKey, helper.StructJson(payload), "mYxiZZhwtvkYAxOV")
	if err != nil {
		return nil, err
	}
	result.Data.EnData = encryptData

	return &result, nil
}

func (r repo) ZappayDeposit(setting model.PaygateAccountResponse, req model.ZappayDepositCreateRemoteRequest) (*model.ZappayDepositCreateRemoteResponse, error) {

	// ผมอยากเก็บ default เอาไว้กัน Adminตั้งค่าต่ำกว่า payment
	if req.Amount < 50 || req.Amount > 200000 {
		log.Println("req.Amount", req.Amount)
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}
	if setting.PaymentDepositMinimum > 0 && req.Amount < setting.PaymentDepositMinimum {
		log.Println("req.Amount", req.Amount, "setting.PaymentDepositMinimum", setting.PaymentDepositMinimum)
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}
	if setting.PaymentDepositMaximum > 0 && req.Amount > setting.PaymentDepositMaximum {
		log.Println("req.Amount", req.Amount, "setting.PaymentDepositMaximum", setting.PaymentDepositMaximum)
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}
	if setting.PartnerKey == "" || setting.RepayAppId == "" || setting.MerchantId == "" || setting.Token == "" || setting.AesKey == "" || setting.ApiEndPoint == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}

	apiEndPoint := setting.ApiEndPoint // https://api-proxy.cbgame88.com/zappay

	// POST Endpoint: https://api-proxy.cbgame88.com/zappay/trade/repay
	epUrl := fmt.Sprintf("%s/trade/repay", apiEndPoint)
	log.Println("ZappayDeposit url ------> ", epUrl)

	// SysLog
	if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
		Name:   "CreateZappayDeposit.ZappayDeposit",
		Status: "CALL",
		JsonReq: helper.StructJson(map[string]interface{}{
			"epUrl":   epUrl,
			"setting": setting,
			"req":     req,
		}),
	}); err != nil {
		log.Println("CreatePaygateSystemLog.CreateSysLog", err)
	}

	var body model.ZappayDepositRequest
	body.PartnerKey = setting.PartnerKey
	body.RepayAppId = setting.RepayAppId
	body.MerchantId = setting.MerchantId
	body.Token = setting.Token
	body.AesKey = setting.AesKey
	body.OrderNo = req.OrderNo
	body.BankCode = req.BankCode
	body.Amount = req.Amount
	body.UserFullname = req.UserFullname
	body.UserMobile = req.UserMobile
	body.UserAccountNumber = req.UserAccountNumber
	body.UserAccountBank = req.UserAccountBank
	enPayload, err := ZappayEncryptRepayDespositPayload(body)
	if err != nil {
		return nil, err
	}

	client := &http.Client{}
	client.Timeout = 20 * time.Second

	jsonBody, _ := json.Marshal(enPayload)
	payload := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest("POST", epUrl, payload)
	reqExternal.Header.Set("Content-Type", "application/json")
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}
	if response.StatusCode != 200 {
		log.Println(response.StatusCode)
		log.Println(string(responseData))
		var errMsg2 model.ZappayErrorRemoteResponse
		errJson2 := json.Unmarshal(responseData, &errMsg2)
		if errJson2 == nil {
			return nil, errors.New(errMsg2.Message)
		} else {
			log.Println("Unmarshal.errJson2 ------> ", errJson2)
		}
		var errMsg3 model.ZappayErrorStringRemoteResponse
		errJson3 := json.Unmarshal(responseData, &errMsg3)
		if errJson3 == nil {
			return nil, errors.New(errMsg3.Message)
		} else {
			log.Println("Unmarshal.errJson3 ------> ", errJson3)
		}
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	}

	var result model.ZappayDepositCreateRemoteResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("ZappayDeposit resp.Body ------> ", string(responseData))
		log.Println("Unmarshal.Err ------> ", errJson)
		// TRY-1
		var result1 model.ZappayDepositCreateRemote2Response
		errJson1 := json.Unmarshal(responseData, &result1)
		if errJson1 == nil {
			// Convert to model.ZappayDepositCreateRemoteResponse
			result = model.ZappayDepositCreateRemoteResponse{
				Code:    result1.Code,
				Message: result1.Message,
				Data: model.ZappayDepositCreateRemoteResponseData{
					OrderNo:            result1.Data.OrderNo,
					MchOrderNo:         result1.Data.MchOrderNo,
					ExpirationDate:     result1.Data.ExpirationDate,
					Status:             result1.Data.Status,
					Amount:             result1.Data.Amount,
					OriginCode:         result1.Data.OriginCode,
					Redirect:           result1.Data.Redirect,
					BankCardName:       result1.Data.BankCardName,
					BankDiscountAmount: result1.Data.BankDiscountAmount,
					SubBankName:        result1.Data.SubBankName,
					BankName:           result1.Data.BankName,
					AccountNumber:      result1.Data.AccountNumber,
				},
			}
			return &result, nil
		} else {
			log.Println("Unmarshal.errJson1 ------> ", errJson1)
		}
		// TRY-2-err1
		var errMsg2 model.ZappayErrorStringRemoteResponse
		errJson2 := json.Unmarshal(responseData, &errMsg2)
		if errJson2 == nil {
			return nil, errors.New(errMsg2.Message)
		} else {
			log.Println("Unmarshal.errJson2 ------> ", errJson2)
		}
		// TRY-3-err2
		var errMsg3 model.ZappayErrorRemoteResponse
		errJson3 := json.Unmarshal(responseData, &errMsg3)
		if errJson3 == nil {
			return nil, errors.New(errMsg3.Message)
		} else {
			log.Println("Unmarshal.errJson3 ------> ", errJson3)
		}
		return nil, errors.New("INVALID_UNMARSHAL_JSON")
	}

	return &result, nil
}

func (r repo) ZappayWithdraw(setting model.PaygateAccountResponse, req model.ZappayWithdrawCreateRemoteRequest) (*model.ZappayWithdrawCreateRemoteResponse, error) {

	// ผมอยากเก็บ default เอาไว้กัน Adminตั้งค่าต่ำกว่า payment
	// if req.Amount < 20 || req.Amount > 200000 {
	// 	log.Println("req.Amount", req.Amount)
	// 	return nil, errors.New("INVALID_AMOUNT_RANGE")
	// }
	// if req.Amount < setting.PaymentWithdrawMinimum || req.Amount > setting.PaymentWithdrawMaximum {
	// 	log.Println("req.Amount", req.Amount, " setting.PaymentWithdrawMinimum", setting.PaymentWithdrawMinimum, " setting.PaymentWithdrawMaximum", setting.PaymentWithdrawMaximum)
	// 	return nil, errors.New("INVALID_AMOUNT_RANGE")
	// }

	apiEndPoint := setting.ApiEndPoint // https://api-proxy.cbgame88.com/zappay

	// POST Endpoint: https://api-proxy.cbgame88.com/zappay/trade/pay
	epUrl := fmt.Sprintf("%s/trade/pay", apiEndPoint)

	// SysLog
	if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
		Name:   "CreateZappayDeposit.ZappayWithdraw",
		Status: "CALL",
		JsonReq: helper.StructJson(map[string]interface{}{
			"epUrl":   epUrl,
			"setting": setting,
			"req":     req,
		}),
	}); err != nil {
		log.Println("CreatePaygateSystemLog.CreateSysLog", err)
	}

	var body model.ZappayDepositRequest
	body.PartnerKey = setting.PartnerKey
	body.LoanAppId = setting.LoanAppId
	body.MerchantId = setting.MerchantId
	body.Token = setting.Token
	body.AesKey = setting.AesKey
	body.OrderNo = req.OrderNo
	body.Amount = req.Amount
	body.UserFullname = req.UserFullname
	body.UserMobile = req.UserMobile
	body.UserAccountNumber = req.UserAccountNumber
	body.UserAccountBank = req.UserAccountBank
	enPayload, err := ZappayEncryptLoanWithdrawPayload(body)
	if err != nil {
		return nil, err
	}

	client := &http.Client{}
	client.Timeout = 20 * time.Second

	jsonBody, _ := json.Marshal(enPayload)
	payload := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest("POST", epUrl, payload)
	reqExternal.Header.Set("Content-Type", "application/json")
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}
	if response.StatusCode != 200 {
		log.Println(response.StatusCode)
		log.Println(string(responseData))
		var errMsg2 model.ZappayErrorStringRemoteResponse
		errJson2 := json.Unmarshal(responseData, &errMsg2)
		if errJson2 == nil {
			return nil, errors.New(errMsg2.Message)
		} else {
			log.Println("Unmarshal.errJson2 ------> ", errJson2)
		}
		var errMsg3 model.ZappayErrorRemoteResponse
		errJson3 := json.Unmarshal(responseData, &errMsg3)
		if errJson3 == nil {
			return nil, errors.New(errMsg3.Message)
		} else {
			log.Println("Unmarshal.errJson3 ------> ", errJson3)
		}
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	}

	//  {"code":"10002","message":"PARTNER_KEY Invalid","data":""}

	var result model.ZappayWithdrawCreateRemoteResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("ZappayWithdraw resp.Body ------> ", string(responseData))
		log.Println("Unmarshal.Err ------> ", errJson)
		var errMsg2 model.ZappayErrorStringRemoteResponse
		errJson2 := json.Unmarshal(responseData, &errMsg2)
		if errJson2 == nil {
			return nil, errors.New(errMsg2.Message)
		} else {
			log.Println("Unmarshal.errJson2 ------> ", errJson2)
		}
		var errMsg3 model.ZappayErrorRemoteResponse
		errJson3 := json.Unmarshal(responseData, &errMsg3)
		if errJson3 == nil {
			return nil, errors.New(errMsg3.Message)
		} else {
			log.Println("Unmarshal.errJson3 ------> ", errJson3)
		}
		return nil, errors.New("INVALID_UNMARSHAL_JSON")
	}

	return &result, nil
}

func (r repo) CreateZappayWebhook(body model.ZappayWebhookCreateBody) (*int64, error) {

	if err := r.db.Table("paygate_zappay_webhook").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) GetDbZappayOrderList(req model.ZappayOrderListRequest) ([]model.ZappayOrderResponse, int64, error) {

	var list []model.ZappayOrderResponse
	var total int64

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("paygate_zappay_order as tb_order")
	count = count.Select("tb_order.id")
	if req.UserId != nil {
		count = count.Where("tb_order.user_id = ?", req.UserId)
	}
	if req.OrderTypeId != nil {
		count = count.Where("tb_order.order_type_id = ?", req.OrderTypeId)
	}
	if req.OrderNo != "" {
		count = count.Where("tb_order.order_no = ?", req.OrderNo)
	}
	if req.TransactionNo != "" {
		count = count.Where("tb_order.transaction_no = ?", req.TransactionNo)
	}
	if req.Amount != "" {
		count = count.Where("tb_order.amount = ?", req.Amount)
	}
	if req.Status != "" {
		count = count.Where("tb_order.transaction_status = ?", req.Status)
	}

	if err := count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount, tb_order.transfer_amount AS transfer_amount"
		selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
		selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.payment_page_url AS payment_page_url"
		selectedFields += ", tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
		selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

		query := r.db.Table("paygate_zappay_order as tb_order")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN paygate_order_type AS tb_type ON tb_order.order_type_id = tb_type.id")
		if req.UserId != nil {
			query = query.Where("tb_order.user_id = ?", req.UserId)
		}
		if req.OrderTypeId != nil {
			query = query.Where("tb_order.order_type_id = ?", req.OrderTypeId)
		}
		if req.OrderNo != "" {
			query = query.Where("tb_order.order_no = ?", req.OrderNo)
		}
		if req.TransactionNo != "" {
			query = query.Where("tb_order.transaction_no = ?", req.TransactionNo)
		}
		if req.Amount != "" {
			query = query.Where("tb_order.amount = ?", req.Amount)
		}
		if req.Status != "" {
			query = query.Where("tb_order.transaction_status = ?", req.Status)
		}

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetDbZappayOrderById(id int64) (*model.ZappayOrderResponse, error) {

	var record model.ZappayOrderResponse

	selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount, tb_order.transfer_amount AS transfer_amount"
	selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
	selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.payment_page_url AS payment_page_url"
	selectedFields += ", tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
	selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

	if err := r.db.Table("paygate_zappay_order as tb_order").
		Select(selectedFields).
		Joins("LEFT JOIN paygate_order_type AS tb_type ON tb_order.order_type_id = tb_type.id").
		Where("tb_order.id = ?", id).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetDbZappayOrderByRefId(refId int64) (*model.ZappayOrderResponse, error) {

	var record model.ZappayOrderResponse

	selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount, tb_order.transfer_amount AS transfer_amount"
	selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
	selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.payment_page_url AS payment_page_url"
	selectedFields += ", tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
	selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

	if err := r.db.Table("paygate_zappay_order as tb_order").
		Select(selectedFields).
		Joins("LEFT JOIN paygate_order_type AS tb_type ON tb_order.order_type_id = tb_type.id").
		Where("tb_order.ref_id = ?", refId).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) CreateDbZappayOrder(body model.ZappayOrderCreateBody) (*int64, error) {

	if err := r.db.Table("paygate_zappay_order").Create(&body).Error; err != nil {
		return nil, err
	}

	// Update order_no if Empty in HPG{YYYYMMDD}{ID} //
	// orderNo := fmt.Sprintf("HPG%v%v", time.Now().Format("200601"), body.Id)
	// [20240209] Update order_no if Empty in {AGENT_NAME}{YYMM}{ID} //
	agentName := os.Getenv("PAYGATE_ORDER_PREFIX")
	if ginMode := os.Getenv("GIN_MODE"); ginMode == "debug" {
		agentName = "P8D" // DEVELOPMENT
	}
	if agentName == "" {
		agentName = "P8G" // ** MIN_LEN=10
	} else {
		agentName = strings.ToUpper(agentName)
	}
	orderNo := fmt.Sprintf("%v%v%v", agentName, time.Now().Format("0601"), body.Id)
	if body.OrderNo == "" {
		updateBody := make(map[string]interface{})
		updateBody["order_no"] = orderNo
		if err := r.db.Table("paygate_zappay_order").Where("id = ?", body.Id).Updates(updateBody).Error; err != nil {
			return nil, err
		}
	}
	return &body.Id, nil
}

func (r repo) CheckZappayDepositOrderInLast5Minutes(userId int64, amount float64) (*model.ZappayOrderResponse, error) {

	var record model.ZappayOrderResponse

	selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount, tb_order.transfer_amount AS transfer_amount"
	selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
	selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.payment_page_url AS payment_page_url"
	selectedFields += ", tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
	selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

	query := r.db.Table("paygate_zappay_order as tb_order")
	query = query.Select(selectedFields)
	query = query.Joins("LEFT JOIN paygate_order_type AS tb_type ON tb_order.order_type_id = tb_type.id")
	query = query.Where("tb_order.user_id = ?", userId)
	query = query.Where("tb_order.amount = ?", amount)
	if err := query.
		Where("tb_order.transaction_status = ?", "WAIT_PAYMENT").
		Order("tb_order.id DESC").
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) UpdateDbZappayOrderError(id int64, remark string) error {

	updateBody := map[string]interface{}{
		"transaction_status": "ERROR",
		"remark":             remark,
	}
	sql := r.db.Table("paygate_zappay_order").Where("id = ?", id).Where("transaction_status = ?", "PENDING")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdateDbZappayOrder(id int64, body model.ZappayOrderUpdateBody) error {

	if err := r.db.Table("paygate_zappay_order").Where("id = ?", id).Updates(body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) ApproveDbZappayOrder(id int64, webhookStatus string) error {

	updateBody := make(map[string]interface{})
	updateBody["transaction_status"] = webhookStatus
	updateBody["payment_at"] = time.Now()

	sql := r.db.Table("paygate_zappay_order").Where("id = ?", id).Where("transaction_status = ?", "WAIT_PAYMENT")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdateZappayOrderBankSucess(id int64, transId int64, actionBy int64) error {

	updateBody := map[string]interface{}{
		"bank_transaction_id":     transId,
		"bank_transaction_status": "SUCCESS",
		"action_by":               actionBy,
	}

	sql := r.db.Table("paygate_zappay_order").Where("id = ?", id).Where("bank_transaction_status = ?", "PENDING")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}
