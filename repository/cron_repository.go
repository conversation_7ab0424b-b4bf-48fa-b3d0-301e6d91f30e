package repository

import (
	"cybergame-api/model"

	"gorm.io/gorm"
)

func NewCronRepository(db *gorm.DB) CronRepository {
	return &repo{db}
}

type CronRepository interface {
	// ACTION
	GetCronActionById(id int64) (*model.CronAction, error)
	GetCronActionByActionKey(actionKey string) (*model.CronAction, error)
	CreateCronAction(body model.CronActionCreateBody) (int64, error)
	SetSuccessCronAction(id int64, remark string) error
	SetFailCronAction(id int64, remark string) error
	// RACE_CONDITION_BLOCKER
	GetRaceActionByActionKey(actionKey string) (*model.RaceAction, error)
	GetRaceActionIdByActionKey(actionKey string) (*int64, error)
	CreateRaceCondition(body model.RaceActionCreateBody) (int64, error)
	//Delete log
	CronjobDeleteAgentLog() error
	CronjobDeleteBankTransactionLog() error
	CronjobDeletePaygateHengWebhook() error
}

func (r repo) GetCronActionById(id int64) (*model.CronAction, error) {

	var record model.CronAction
	// GET //
	selectedFields := "id, name, status, action_key, remark, unlock_at"
	query := r.db.Table("cron_action as action")
	query = query.Select(selectedFields)
	if err := query.
		Where("action.id = ?", id).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetCronActionByActionKey(actionKey string) (*model.CronAction, error) {

	var record model.CronAction
	// GET //
	selectedFields := "id, name, status, action_key, remark, unlock_at"
	query := r.db.Table("cron_action as action")
	query = query.Select(selectedFields)
	if err := query.
		Where("action.action_key = ?", actionKey).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) CreateCronAction(body model.CronActionCreateBody) (int64, error) {

	if err := r.db.Table("cron_action").Create(&body).Error; err != nil {
		return 0, err
	}
	return body.Id, nil
}

func (r repo) SetSuccessCronAction(id int64, remark string) error {

	updateBody := map[string]interface{}{
		"status": "SUCCESS",
		"remark": remark,
	}
	if err := r.db.Table("cron_action").Where("id = ?", id).Where("status = ?", "PENDING").Updates(&updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) SetFailCronAction(id int64, remark string) error {

	updateBody := map[string]interface{}{
		"status": "FAILED",
		"remark": remark,
	}
	if err := r.db.Table("cron_action").Where("id = ?", id).Where("status = ?", "PENDING").Updates(&updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetRaceActionByActionKey(actionKey string) (*model.RaceAction, error) {

	var record model.RaceAction
	// GET //
	selectedFields := "id, name, status, action_key, json_request, unlock_at"
	query := r.db.Table("race_action as action")
	query = query.Select(selectedFields)
	if err := query.
		Where("action.action_key = ?", actionKey).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetRaceActionIdByActionKey(actionKey string) (*int64, error) {

	var record model.RaceAction
	// GET //
	selectedFields := "id"
	query := r.db.Table("race_action as action")
	query = query.Select(selectedFields)
	if err := query.
		Where("action.action_key = ?", actionKey).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record.Id, nil
}

func (r repo) CreateRaceCondition(body model.RaceActionCreateBody) (int64, error) {

	if err := r.db.Table("race_action").Create(&body).Error; err != nil {
		return 0, err
	}
	return body.Id, nil
}

func (r repo) UpdateRaceCondition(id int64, body model.RaceActionUpdateBody) error {

	if body.ActionKey != nil {
		// Allow Update ActionKey only on PENDING
		if err := r.db.Table("race_action").Where("id = ?", id).Where("status = ?", "PENDING").Updates(&body).Error; err != nil {
			return err
		}
	} else {
		if err := r.db.Table("race_action").Where("id = ?", id).Updates(&body).Error; err != nil {
			return err
		}
	}
	return nil
}
