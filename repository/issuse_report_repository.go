package repository

import (
	"cybergame-api/model"

	"gorm.io/gorm"
)

func NewIssueReportRepository(db *gorm.DB) IssueReportRepository {
	return &repo{db}
}

type IssueReportRepository interface {
	// issue report web
	CreateIssueReportWeb(body []model.IssueReportWebCreate) error
	GetIssueReportWebById(ids []int64) ([]model.WebUrlBody, error)
	// issue report
	CreateIssueReport(body model.IssueReportCreateBody) (*int64, error)
	GetIssueReportById(id int64) (*model.IssueReport, error)
	UpdateIssueReport(body model.IssueReportUpdateBody) error
	GetIssueReportList(req model.IssueReportListRequest) ([]model.IssueReport, int64, error)
	DeleteIssueReportAndWeb(id int64) error
	//Option
	GetIssueStatusOptions() ([]model.IssueStatus, error)
	//web url
	CreateWebUrl(body model.CreateWebUrl) (*int64, error)
	GetWebUrlList() ([]model.GetWebUrlBody, error)
	GetWebUrlById(req model.GetWebUrlById) (*model.GetWebUrlBody, error)
	DeleteWebUrlById(req model.GetWebUrlById) error
}

func (r repo) CreateIssueReportWeb(body []model.IssueReportWebCreate) error {
	var err error
	if err = r.db.Table("issue_web_report").Create(&body).Error; err != nil {
		return err
	}

	return err
}

func (r repo) GetIssueReportWebById(ids []int64) ([]model.WebUrlBody, error) {
	var webUrl []model.WebUrlBody
	var err error
	selectedFeild := "issue_web_report.id AS id, issue_web_url.url AS url, issue_web_report.issue_report_id AS issue_report_id, issue_web_report.issue_web_url_id AS issue_web_url_id"
	query := r.db.Table("issue_web_report")
	query = query.Select(selectedFeild)
	query = query.Joins("LEFT JOIN issue_web_url ON issue_web_report.issue_web_url_id = issue_web_url.id")
	query = query.Where("issue_web_report.issue_report_id IN ?", ids)
	if err = query.Scan(&webUrl).Error; err != nil {
		return nil, err
	}

	return webUrl, nil

}

func (r repo) CreateIssueReport(body model.IssueReportCreateBody) (*int64, error) {

	if err := r.db.Table("issue_report").Create(&body).Error; err != nil {
		return nil, err
	}

	return &body.Id, nil
}

func (r repo) GetIssueReportById(id int64) (*model.IssueReport, error) {

	var issueReport model.IssueReport
	selectedFeild := "issue_report.id AS id, issue_report.description AS description"
	selectedFeild += ", issue_report.issue_status_id AS issue_status_id , issue_status.label_th AS issue_status_th, issue_status.label_en AS issue_status_en"
	selectedFeild += ", issue_report.created_by_name AS created_by_name, issue_report.approved_by_name AS approved_by_name"
	selectedFeild += ", issue_report.created_at AS created_at, issue_report.updated_at AS updated_at"

	query := r.db.Table("issue_report").Select(selectedFeild)
	query = query.Joins("LEFT JOIN issue_status ON issue_report.issue_status_id = issue_status.id")
	query = query.Where("issue_report.id = ?", id)
	query = query.First(&issueReport)
	if err := query.Where("issue_report.deleted_at IS NULL").Error; err != nil {
		return nil, err
	}

	return &issueReport, nil
}

func (r repo) UpdateIssueReport(body model.IssueReportUpdateBody) error {

	if err := r.db.Table("issue_report").Where("id = ?", body.Id).Updates(&body).Error; err != nil {
		return err
	}

	return nil
}

func (r repo) GetIssueReportList(req model.IssueReportListRequest) ([]model.IssueReport, int64, error) {
	var issueReport []model.IssueReport
	var total int64
	var err error
	selectedFeild := "issue_report.id AS id, issue_report.description AS description"
	selectedFeild += ", issue_report.issue_status_id AS issue_status_id , issue_status.label_th AS issue_status_th, issue_status.label_en AS issue_status_en"
	selectedFeild += ", issue_report.created_by_name AS created_by_name, issue_report.approved_by_name AS approved_by_name"
	selectedFeild += ", issue_report.created_at AS created_at, issue_report.updated_at AS updated_at"

	count := r.db.Table("issue_report").Select("issue_report.id")
	count = count.Joins("LEFT JOIN issue_status ON issue_report.issue_status_id = issue_status.id")

	if req.FromCreateDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.FromCreateDate)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("issue_report.created_at >= ? ", startDateAtBkk)
	}
	if req.ToCreateDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.ToCreateDate)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("issue_report.created_at <=  ?", endDateAtBkk)
	}

	if req.Search != "" {
		count = count.Where("issue_report.description LIKE ?", "%"+req.Search+"%")
	}

	if req.IssueStatusId != nil {
		count = count.Where("issue_report.issue_status_id = ?", req.IssueStatusId)
	}

	if err = count.Where("issue_report.deleted_at IS NULL").Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if total > 0 {
		query := r.db.Table("issue_report").Select(selectedFeild)
		query = query.Joins("LEFT JOIN issue_status ON issue_report.issue_status_id = issue_status.id")
		if req.FromCreateDate != "" {
			startDateAtBkk, err := r.ParseBodBkk(req.FromCreateDate)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("issue_report.created_at >= ? ", startDateAtBkk)
		}
		if req.ToCreateDate != "" {
			endDateAtBkk, err := r.ParseEodBkk(req.ToCreateDate)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("issue_report.created_at <=  ?", endDateAtBkk)
		}

		if req.Search != "" {
			query = query.Where("issue_report.description LIKE ?", "%"+req.Search+"%")
		}

		if req.IssueStatusId != nil {
			query = query.Where("issue_report.issue_status_id = ?", req.IssueStatusId)
		}

		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}

		if err = query.
			Where("issue_report.deleted_at IS NULL").
			Limit(req.Limit).Offset(req.Limit * req.Page).
			Order("issue_report.updated_at DESC").
			Scan(&issueReport).
			Error; err != nil {
			return nil, 0, err
		}

	}
	return issueReport, total, nil
}

func (r repo) DeleteIssueReportAndWeb(id int64) error {

	if err := r.db.Table("issue_report").Where("id = ?", id).Delete(model.IssueReport{}).Error; err != nil {
		return err
	}

	if err := r.db.Table("issue_web_report").Where("issue_report_id = ?", id).Delete(model.IssueReportWeb{}).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetIssueStatusOptions() ([]model.IssueStatus, error) {

	var issueStatus []model.IssueStatus
	query := r.db.Table("issue_status")
	query = query.Select("id, name, label_th, label_en")
	query = query.Order("issue_status.id ASC")
	query = query.Scan(&issueStatus)
	if err := query.Error; err != nil {
		return nil, err
	}

	return issueStatus, nil
}

func (r repo) CreateWebUrl(body model.CreateWebUrl) (*int64, error) {
	var err error
	if err = r.db.Table("issue_web_url").Create(&body).Error; err != nil {
		return nil, err
	}

	return &body.Id, err
}

func (r repo) GetWebUrlList() ([]model.GetWebUrlBody, error) {

	var webUrl []model.GetWebUrlBody
	var err error
	selectedFeild := "issue_web_url.id AS id, issue_web_url.url AS url"
	query := r.db.Table("issue_web_url")
	query = query.Select(selectedFeild)
	if err = query.Scan(&webUrl).Error; err != nil {
		return nil, err
	}

	return webUrl, nil
}

func (r repo) GetWebUrlById(req model.GetWebUrlById) (*model.GetWebUrlBody, error) {

	var webUrl model.GetWebUrlBody
	var err error
	selectedFeild := "issue_web_url.id AS id, issue_web_url.url AS url"
	query := r.db.Table("issue_web_url")
	query = query.Select(selectedFeild)
	query = query.Where("issue_web_url.id = ?", req.Id)
	if err = query.Scan(&webUrl).Error; err != nil {
		return nil, err
	}

	return &webUrl, nil
}

func (r repo) DeleteWebUrlById(req model.GetWebUrlById) error {

	var err error
	if err = r.db.Table("issue_web_url").Where("id = ?", req.Id).Delete(model.WebUrl{}).Error; err != nil {
		return err
	}
	return nil
}
