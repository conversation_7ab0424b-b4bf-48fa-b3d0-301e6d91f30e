package repository

import (
	"bytes"
	"cybergame-api/helper"
	"cybergame-api/model"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"mime/multipart"
	"net/http"
	"os"
	"time"

	"gorm.io/gorm"
)

func NewTransactionRepository(db *gorm.DB) TransactionRepository {
	return &repo{db}
}

type TransactionRepository interface {
	GetTransactionList(query model.TransactionQuery) ([]model.TransactionDetail, int64, error)
	GetBankActiveList() ([]model.TransactionBankList, error)
	GetAccountBankIdByBankId(bankId int64) (int64, error)
	GetStatementIdAndStatus(amount int64, fromBank, fromAccNo, toAccNo string) (*model.TransactionStatement, error)
	Deposit(data model.TransactionDepositBody) error
	Withdraw(data model.TransactionWithdrawBody) error

	GetBankAccountById(id int64) (*model.BankAccount, error)
	DepositConfirmedFromUser(body model.CreateDepositConfirmedFromUserBody) (*int64, error)
	FindUserDepositInBankTransaction(find model.ValidateQrDataWithBankTransactionBody) ([]model.FindPossibleOwnerByConfirmDeposit, int64, error)
	CreateBankStatement(data model.BankStatementCreateBody) (*int64, error)
	GetUserBankIdByName(bankname string) (*int64, error)
	InsertBankTransaction(data model.BankTransactionCreateBody) (*int64, error)
	GetMemberById(id int64) (*model.Member, error)
	UpdateUserTransactionStatementId(id int64, statementId int64) error
	UpdateFromBankAccount(statementId int64, bankAccount string) error

	ReadQrFromFileUpload(req *http.Request) (*string, error)

	FindBankByName(name string) (*model.Bank, error)

	CreateBankTransactionLog(data model.BankTransactionLogCreate) (*int64, error)
	// REF-BANKING
	IncreaseUserCredit(body model.UserTransactionCreateRequest) (*model.UserTransactionCreateResponse, error)
	DecreaseUserCredit(body model.UserTransactionCreateRequest) (*model.UserTransactionCreateResponse, error)
	// [SYSLOG]
	CreateSystemLog(body model.SystemLogCreateBody) (*int64, error)
}

func (r repo) GetTransactionList(query model.TransactionQuery) ([]model.TransactionDetail, int64, error) {

	list := []model.TransactionDetail{}
	total := int64(0)

	if err := r.db.Table("bank_transaction_slip").
		Where("user_id = ?", query.UserId).
		Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if total < 1 {
		return nil, 0, nil
	}

	if err := r.db.Table("bank_transaction_slip").
		Joins("INNER JOIN transaction_status AS ts ON ts.id = bank_transaction_slip.status_id").
		Select("bank_transaction_slip.id, bank_transaction_slip.type, bank_transaction_slip.deposited_at, bank_transaction_slip.amount, ts.label_th AS status, bank_transaction_slip.deposited_at, bank_transaction_slip.created_at").
		Where("user_id = ?", query.UserId).
		Limit(query.Limit).
		Offset(query.Page * query.Limit).
		Order("id desc").
		Find(&list).Error; err != nil {
		return nil, 0, err
	}
	return list, total, nil
}

func (r repo) GetAccountBankIdByBankId(bankId int64) (int64, error) {

	var statementID int64

	if err := r.db.Table("bank_account").
		Select("id").
		Where("bank_id = ?", bankId).
		Where("connection_status_id = ?", 1).
		// Where("account_status_id = ?", 1).
		Take(&statementID).Error; err != nil {
		return 0, err
	}
	return statementID, nil
}

func (r repo) GetBankActiveList() ([]model.TransactionBankList, error) {

	list := []model.TransactionBankList{}

	if err := r.db.Table("bank_account ba").
		Joins("INNER JOIN bank b ON b.id = ba.bank_id").
		Select("b.id, ba.account_name, b.name AS bank_name, ba.account_number").
		Where("ba.connection_status_id = ?", 1).
		// Where("ba.account_status_id = ?", 1).
		Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (r repo) GetStatementIdAndStatus(amount int64, fromBank, fromAccNo, toAccNo string) (*model.TransactionStatement, error) {

	var result *model.TransactionStatement

	if err := r.db.Table("bank_transaction bt").
		Joins("INNER JOIN bank b ON b.id = bt.from_bank_id").
		Joins("INNER JOIN transaction_type tt ON tt.id = bt.transaction_type_id").
		Select("bt.id").
		Where("tt.name = ?", "DEPOSIT").
		Where("bt.credit_amount = ?", amount).
		Where("bt.from_bank_id = ?", fromBank).
		Where("bt.from_account_number = ?", fromAccNo).
		Where("bt.to_account_number LIKE ?", "%"+toAccNo+"%").
		Take(&result).Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (r repo) Deposit(data model.TransactionDepositBody) error {

	newData := map[string]interface{}{}
	newData["slip_url"] = data.SlipURL
	newData["amount"] = data.Amount
	newData["type"] = data.Type
	newData["status_id"] = data.StatusId
	newData["user_id"] = data.UserID
	newData["bank_id"] = data.BankID
	newData["bank_account_id"] = data.BankAccountID
	newData["deposited_at"] = data.DepositedAt

	if err := r.db.Table("bank_transaction_slip").
		Create(&newData).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) Withdraw(data model.TransactionWithdrawBody) error {

	if err := r.db.Table("bank_transaction_slip").
		Create(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) DepositConfirmedFromUser(body model.CreateDepositConfirmedFromUserBody) (*int64, error) {

	if err := r.db.Table("bank_transaction_slip").
		Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) FindUserDepositInBankTransaction(find model.ValidateQrDataWithBankTransactionBody) ([]model.FindPossibleOwnerByConfirmDeposit, int64, error) {
	var possibleValidate []model.FindPossibleOwnerByConfirmDeposit
	var total int64

	// Calculate the start time by subtracting 3 minutes from req.TransferDate
	startTime := find.TransferDate.Add(-3 * time.Minute)

	// Calculate the end time by adding 3 minutes to req.TransferDate
	endTime := find.TransferDate.Add(3 * time.Minute)

	count := r.db.Table("bank_transaction").
		Joins("LEFT JOIN bank_statement ON bank_statement.id = bank_transaction.statement_id").
		Select("bank_transaction.statement_id,bank_transaction.transaction_status_id,bank_transaction.id").
		Where("bank_transaction.credit_amount = ?", find.TransferAmount).
		Where("bank_transaction.to_account_id = ?", find.ToBankAccountId).
		Where("bank_transaction.transfer_at >= ? AND bank_transaction.transfer_at <= ?", startTime, endTime).
		Where("bank_statement.from_account_number LIKE ?", "%"+find.FromBankAccount+"%")
	if find.FromBankId != nil {
		count = count.Where("bank_statement.from_bank_id = ?", *find.FromBankId)
	}

	if err := count.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if total > 0 {

		query := r.db.Table("bank_transaction").
			Joins("LEFT JOIN bank_statement ON bank_statement.id = bank_transaction.statement_id").
			Select("bank_transaction.statement_id,bank_transaction.transaction_status_id,bank_transaction.id").
			Where("bank_transaction.credit_amount = ?", find.TransferAmount).
			Where("bank_transaction.to_account_id = ?", find.ToBankAccountId).
			Where("bank_transaction.transfer_at >= ? AND bank_transaction.transfer_at <= ?", startTime, endTime).
			Where("bank_statement.from_account_number LIKE ?", "%"+find.FromBankAccount+"%")

		if find.FromBankId != nil {
			query = query.Where("bank_statement.from_bank_id = ?", *find.FromBankId)
		}

		if err := query.Scan(&possibleValidate).Error; err != nil {
			return nil, 0, err
		}
	}
	return possibleValidate, total, nil
}

func (r repo) GetUserBankIdByName(bankname string) (*int64, error) {

	var bankId int64
	if err := r.db.Table("bank").
		Select("id").
		Where("name = ?", bankname).
		Take(&bankId).Error; err != nil {
		return nil, err
	}
	return &bankId, nil
}

func (r repo) UpdateUserTransactionStatementId(id int64, statementId int64) error {

	if err := r.db.Table("bank_transaction_slip").
		Where("id = ?", id).
		Update("statement_id", statementId).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) ReadQrFromFileUpload(req *http.Request) (*string, error) {
	var result string

	fileReader, _, err := req.FormFile("file")
	if err != nil {
		log.Println("ReadQrFromFileUpload.ERROR.FormFile", err)
		return nil, errors.New(err.Error())
	}

	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)
	part, err := writer.CreateFormFile("file", "image.jpg")
	if err != nil {
		log.Println("ReadQrFromFileUpload.Error.CreateFormFile", err)
		return nil, err
	}
	_, err = io.Copy(part, fileReader)
	if err != nil {
		log.Println("ReadQrFromFileUpload.Error.IoCopy", err)
		return nil, err
	}
	writer.Close()
	defer fileReader.Close()

	url := os.Getenv("RAW_QRCODE_URL")
	client := &http.Client{}
	reqHttp, err := http.NewRequest("POST", url, body)

	if err != nil {
		log.Println("ReadQrFromFileUpload.Error.NewRequest:", err)
		return nil, errors.New(err.Error())
	}
	reqHttp.Header.Set("Content-Type", writer.FormDataContentType())
	response, err := client.Do(reqHttp)
	if err != nil {
		log.Println("ReadQrFromFileUpload.Error.Do:", err)
		return nil, errors.New(err.Error())
	}
	defer response.Body.Close()

	if response.StatusCode != 200 {
		log.Println("ReadQrFromFileUpload.Error4: remote response status code,", response.StatusCode)
		log.Println("ReadQrFromFileUpload.Error4: remote url,", url)
		log.Println("ReadQrFromFileUpload.Error4: remote req,", helper.StructJson(req))
		return nil, errors.New("remote response status code : " + fmt.Sprintf("%v", response.StatusCode))
	}

	// Parse the JSON response as an array
	var qrCodeResponses []model.QrCodeResponse
	err = json.NewDecoder(response.Body).Decode(&qrCodeResponses)
	if err != nil {
		log.Println("ReadQrFromFileUpload.Error.Decode:", err)
		return nil, errors.New(err.Error())
	}

	if len(qrCodeResponses) > 0 && len(qrCodeResponses[0].Symbol) > 0 {
		result = qrCodeResponses[0].Symbol[0].Data
	}

	return &result, nil
}

func (r repo) FindBankByName(name string) (*model.Bank, error) {

	var bank model.Bank

	if err := r.db.Table("bank").
		Where("name = ?", name).
		First(&bank).Error; err != nil {
		return nil, err
	}
	return &bank, nil
}

func (r repo) UpdateFromBankAccount(statementId int64, bankAccount string) error {

	if err := r.db.Table("bank_transaction").
		Where("statement_id = ?", statementId).
		Update("from_account_number", bankAccount).Error; err != nil {
		return nil
	}
	return nil
}
