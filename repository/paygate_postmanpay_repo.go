package repository

import (
	"bytes"
	"cybergame-api/helper"
	"cybergame-api/model"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"strings"
	"time"

	"github.com/golang-jwt/jwt"
	"gorm.io/gorm"
)

func NewPostmanPayRepository(db *gorm.DB) PostmanPayRepository {
	return &repo{db}
}

type PostmanPayRepository interface {
	GetDb() *gorm.DB
	// PostmanPay-RD
	// PostmanPayEncryptRepayDesposit(partnerKey string, amount int64) (*model.PostmanPayEncryptPayload, error)

	// MERCHANT
	// GetNOTUSEPaygateMerchantById(id int64) (*model.PaygateMerchantNgResponse, error)
	// GetPaygateMerchantList(req model.PaygateMerchantListRequest) ([]model.PaygateMerchantNgResponse, int64, error)
	// CreatePaygateMerchant(body model.PaygateMerchantCreateBody) (*int64, error)
	// UpdatePaygateMerchant(id int64, body model.PaygateMerchantUpdateBody) error
	// DeletePaygateMerchant(id int64) error
	// // SETTING
	// GetPaygateSetting() (*model.PaygateSettingResponse, error)
	// CreatePaygateSetting(body model.PaygateSettingCreateBody) (*int64, error)
	// UpdatePaygateSetting(id int64, body model.PaygateSettingUpdateBody) error
	// DeletePaygateSetting(id int64) error
	// // AdminLog
	// GetPaygateAdminLogById(id int64) (*model.PaygateAdminLogResponse, error)
	// GetPaygateAdminLogList(req model.PaygateAdminLogListRequest) ([]model.PaygateAdminLogResponse, int64, error)
	// CreatePaygateAdminLog(body model.PaygateAdminLogCreateBody) (*int64, error)
	// UpdatePaygateAdminLog(id int64, body model.PaygateAdminLogUpdateBody) error
	// // SystemLog
	CreatePaygateSystemLog(body model.PaygateSystemLogCreateBody) (*int64, error)
	// UpdatePaygateSystemLog(id int64, body model.PaygateSystemLogUpdateBody) error

	// REF-PAYGATE
	GetRawPostmanPayPendingDepositOrderById(id int64) (*model.PostmanPayOrderResponse, error)
	GetPaygateAccountByProviderId(id int64) (*model.PaygateAccountResponse, error)
	// PostmanPay-REMOTE
	// PostmanPayGetToken(setting model.PaygateAccountResponse) (*model.PostmanPayTokenCreateRemoteResponse, error)
	PostmanPayDeposit(setting model.PaygateAccountResponse, req model.PostmanPayDepositCreateRemoteRequest) (*model.PostmanPayDepositCreateRemoteResponse, error)
	PostmanPayWithdraw(setting model.PaygateAccountResponse, req model.PostmanPayWithdrawCreateRemoteRequest) (*model.PostmanPayWithdrawCreateRemoteResponse, error)
	PostmanPayCheckBalance(setting model.PaygateAccountResponse) (*model.PostmanPayCheckBalanceRemoteResponse, error)
	// PostmanPayGetOrder(setting model.PaygateAccountResponse, orderNo string) (*model.PostmanPayGetOrderRemoteResponse, error)
	// PostmanPayCreateCustomer(setting model.PaygateAccountResponse, req model.PostmanPayCustomerCreateRemoteRequest) (*model.PostmanPayCustomerCreateRemoteResponse, error)
	// PostmanPayUpdateCustomer(setting model.PaygateAccountResponse, req model.PostmanPayCustomerUpdateRemoteRequest) (*model.PostmanPayCustomerUpdateRemoteResponse, error)
	// PostmanPay-Decrypt
	// PostmanPayDecryptRepayDespositPayload(setting model.PaygateAccountResponse, payload model.PostmanPayWebhookEncryptPayload) (*model.PostmanPayWebhookDepositResponse, error)
	// PostmanPay-DB
	CreatePostmanPayWebhook(body model.PostmanPayWebhookCreateBody) (*int64, error)
	GetDbPostmanPayOrderList(req model.PostmanPayOrderListRequest) ([]model.PostmanPayOrderResponse, int64, error)
	GetDbPostmanPayOrderById(id int64) (*model.PostmanPayOrderResponse, error)
	GetDbPostmanPayOrderByRefId(refId int64) (*model.PostmanPayOrderResponse, error)
	CheckPostmanPayDepositOrderInLast5Minutes(userId int64, amount float64) (*model.PostmanPayOrderResponse, error)
	CreateDbPostmanPayOrder(body model.PostmanPayOrderCreateBody) (*int64, error)
	UpdateDbPostmanPayOrderError(id int64, remark string) error
	UpdateDbPostmanPayOrder(id int64, body model.PostmanPayOrderUpdateBody) error
	ApproveDbPostmanPayOrder(id int64, webhookStatus string) error
	UpdatePostmanPayOrderBankSucess(id int64, transId int64, actionBy int64) error
	// Token
	// GetDbPostmanPayAccessToken() (*model.PostmanPayToken, error)
	// CreateDbPostmanPayAccessToken(body model.PostmanPayTokenCreateBody) (*int64, error)
	// Customer
	// GetPostmanPayCustomerById(id int64) (*model.PostmanPayCustomerResponse, error)
	// GetPostmanPayCustomerByUserId(userId int64) (*model.PostmanPayCustomerResponse, error)
	// CheckPostmanPayCustomerByUserId(user model.UserBankDetailBody) (*model.PostmanPayCustomerResponse, error)
	// GetPostmanPayCustomerList(req model.PostmanPayCustomerListRequest) ([]model.PostmanPayCustomerResponse, int64, error)
	// CreatePostmanPayCustomer(body model.PostmanPayCustomerCreateBody) (*int64, error)
	// UpdatePostmanPayCustomer(id int64, body model.PostmanPayCustomerUpdateBody) error
	// DeletePostmanPayCustomer(id int64) error
	// REF-USER
	GetUserById(id int64) (*model.UserResponse, error)
	// REF-MemberCode
	GetUserForGenMember(id int64) (*model.UserDetail, error)
	GetAgentInfo() (*model.AgentInfo, error)
	IncrementTotal() error
	UpdateMemberCode(userId int64, data model.UserUpdateMemberAndRef) error
	AgcRegister(data model.AgcRegister) error
	AmbRegister(data model.AmbRegister) error
	GetMemberById(id int64) (*model.Member, error)
	IsFirstDeposit(userId int64) bool
	UpdateGenMemberCodeToAffilate(userId int64) error
	CreateAffiliateMember(refBy int64, userId int64) error
	// CronjobDeletePostmanPayWebhook() error
	// REF-RC
	GetRaceActionIdByActionKey(actionKey string) (*int64, error)
	CreateRaceCondition(body model.RaceActionCreateBody) (int64, error)
	// REF-BankTransaction-Deposit
	GetUserBankDetailById(id int64) (*model.UserBankDetailBody, error)
	GetDepositCurrentProcessingUserPromotion(userId int64) (*model.PromotionWebUserByUserIdResponse, error)
	InsertBankTransaction(data model.BankTransactionCreateBody) (*int64, error)
	IncreaseUserCredit(body model.UserTransactionCreateRequest) (*model.UserTransactionCreateResponse, error)
	UpdateDepositTransactionStatusFromAgent(transId int64, body model.UserTransactionCreateResponse) error
	UpdateAutoProcessTimer(timer string, id int64) error
	// ConfirmDeposit
	GetBankTransactionById(id int64) (*model.BankTransaction, error)
	CreateTransactionAction(data model.CreateBankTransactionActionBody) (*int64, error)
	ConfirmPendingDepositTransaction(id int64, body model.BankDepositTransactionConfirmBody) error
	RollbackTransactionAction(actionId int64) error
	// Order
	GetPendingPostmanPayDepositOrder(id int64) (*model.PostmanPayOrderResponse, error)
	GetLastestPostmanPayDepositOrderByUserId(userId int64) (*model.PostmanPayOrderResponse, error)
	// REF-BANK
	GetLastestBankAdminManualDepositTransaction(userId int64) (*model.BankTransaction, error)
	ShowUserTransaction(req model.UserTransactionShowUpdate) error
	UpdateTransactionStatusTransferingToSuccess(id int64, secondUsed string) error
	RollbackTransactionStatusTransferingToConfirmed(id int64) error
	CreateBankTransactionExternalDetail(body model.BankTransactionExternalDetailCreateRequest) (int64, error)
	UpdateUserTransactionStatus(body model.UpdateUserTransactionStatusRequest) error
	GetUserWithdrawCreditTransactionByRefId(transactionId int64, creditAmount float64) (*model.UserTransaction, error)
	// REF-SysLog
	CreateSystemLog(body model.SystemLogCreateBody) (*int64, error)
	// REF-WebConfiguration
	GetWebConfiguration() (*model.GetWebConfigurationBody, error)

	// [TIER]
	IncreaseUserTierDepositAmount(userId int64, amount float64) error
}

func (r repo) GetPostmanPayTimestamp(now time.Time) int64 {

	return now.UnixNano() / int64(time.Millisecond)
}

func (r repo) PostmanPayCreateSignature(timestamp int64, merchantId string, clientId string, secretKey string) string {

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"merchantId": merchantId,
		"clientId":   clientId,
		"iat":        timestamp,
	})

	tokenString, err := token.SignedString([]byte(secretKey))
	if err != nil {
		return ""
	}
	return tokenString
}

func (r repo) PostmanPayDeposit(setting model.PaygateAccountResponse, req model.PostmanPayDepositCreateRemoteRequest) (*model.PostmanPayDepositCreateRemoteResponse, error) {

	// [Prerequisites] EP + CLIENTID + APIKEY + SECRET + MERCHANTID
	if setting.ApiEndPoint == "" || setting.PartnerKey == "" || setting.AccessKey == "" || setting.SecretKey == "" || setting.MerchantId == "" {
		log.Println("PostmanPayDeposit.EMPTY_SETTING", setting)
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}

	// ผมอยากเก็บ default เอาไว้กัน Adminตั้งค่าต่ำกว่า payment
	if req.Amount < model.POSTMANPAY_DEFMIN_DEPOSIT_AMOUNT || req.Amount > model.POSTMANPAY_DEFMAX_DEPOSIT_AMOUNT {
		log.Println("PostmanPayDeposit.INVALID_AMOUNT_RANGE req.Amount=", req.Amount, "POSTMANPAY_DEFMIN_DEPOSIT_AMOUNT=", model.POSTMANPAY_DEFMIN_DEPOSIT_AMOUNT, "POSTMANPAY_DEFMAX_DEPOSIT_AMOUNT=", model.POSTMANPAY_DEFMAX_DEPOSIT_AMOUNT)
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}
	if setting.PaymentDepositMinimum > 0 && req.Amount < setting.PaymentDepositMinimum {
		log.Println("PostmanPayDeposit.INVALID_AMOUNT_RANGE req.Amount=", req.Amount, "setting.PaymentDepositMinimum=", setting.PaymentDepositMinimum)
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}
	if setting.PaymentDepositMaximum > 0 && req.Amount > setting.PaymentDepositMaximum {
		log.Println("PostmanPayDeposit.INVALID_AMOUNT_RANGE req.Amount=", req.Amount, "setting.PaymentDepositMaximum=", setting.PaymentDepositMaximum)
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}

	actionAtUtc := time.Now().UTC()
	timestamp := r.GetPostmanPayTimestamp(actionAtUtc)
	sign := r.PostmanPayCreateSignature(timestamp, setting.MerchantId, setting.PartnerKey, setting.SecretKey)

	req.ClientId = setting.PartnerKey
	req.MerchantId = setting.MerchantId
	req.Timeout = 5
	req.Signature = sign
	req.Timestamp = timestamp

	apiEndPoint := setting.ApiEndPoint
	// POST Endpoint: {{API_ENDPOINT}}/api/v1/deposit/create
	epUrl := fmt.Sprintf("%s/api/v1/deposit/create", apiEndPoint)
	// fmt.Println("PostmanPayDeposit url ------> ", epUrl)

	// SysLog
	if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
		Name:   "CreatePostmanPayDeposit.PostmanPayDeposit",
		Status: "CALL",
		JsonReq: helper.StructJson(map[string]interface{}{
			"epUrl":   epUrl,
			"setting": setting,
			"req":     req,
		}),
	}); err != nil {
		log.Println("CreatePaygateSystemLog.CreateSysLog", err)
	}

	client := &http.Client{}
	client.Timeout = 30 * time.Second

	jsonBody, _ := json.Marshal(req)
	payload := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest("POST", epUrl, payload)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("x-api-key", setting.AccessKey)
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}

	// fmt.Println("PostmanPayDeposit.resp.Body", string(responseData))

	if response.StatusCode != 200 {
		log.Println("PostmanPayDeposit.UnmarshalNOT200.StatusCode=", response.StatusCode)
		log.Println("PostmanPayDeposit.UnmarshalNOT200.responseData=", string(responseData))
		var errMsg2 model.PostmanPayErrorRemoteResponse
		errJson2 := json.Unmarshal(responseData, &errMsg2)
		if errJson2 == nil {
			return nil, errors.New(errMsg2.Message)
		} else {
			log.Println("PostmanPayDeposit.UnmarshalNOT200.errJson2=", errJson2)
		}
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	}

	var result model.PostmanPayDepositCreateRemoteResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("PostmanPayDeposit resp.Body ------> ", string(responseData))
		log.Println("Unmarshal.Err ------> ", errJson)
		// TRY-3-err2
		var errMsg3 model.PostmanPayErrorRemoteResponse
		errJson3 := json.Unmarshal(responseData, &errMsg3)
		if errJson3 == nil {
			return nil, errors.New(errMsg3.Message)
		} else {
			log.Println("Unmarshal.errJson3 ------> ", errJson3)
		}
		return nil, errors.New("INVALID_UNMARSHAL_JSON")
	}

	// fmt.Println("PostmanPayDepositCreateRemoteResponse.result", result)
	// PostmanPayDeposit.remoteResp {"success":false,"message":"Cannot read properties of undefined (reading 'length')","data":{"qr_code_id":"","qr_code_url":"","created_at":"","qr_code":"","expired_at":"","amount":"","ref_name_th":"","ref_name_en":"","ref_user_id":""}}
	// PostmanPayDeposit.remoteResp {"success":false,"message":"Cannot read properties of null (reading 'webhook_url')","data":{"qr_code_id":"","qr_code_url":"","created_at":"","qr_code":"","expired_at":"","amount":"","ref_name_th":"","ref_name_en":"","ref_user_id":""}}

	if strings.ToLower(result.Status) != "success" {
		// SysLog
		if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "PostmanPayDeposit",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(req),
			JsonResponse: helper.StructJson(map[string]interface{}{"resp": string(responseData), "result": helper.StructJson(result)}),
		}); err != nil {
			log.Println("PostmanPayDeposit.CreateSysLog", err)
		}
		return nil, errors.New(result.Message)
	}
	return &result, nil
}

func (r repo) PostmanPayCheckBalance(setting model.PaygateAccountResponse) (*model.PostmanPayCheckBalanceRemoteResponse, error) {

	// [Prerequisites] EP + CLIENTID + APIKEY + SECRET + MERCHANTID
	if setting.ApiEndPoint == "" || setting.PartnerKey == "" || setting.AccessKey == "" || setting.SecretKey == "" || setting.MerchantId == "" {
		log.Println("PostmanPayCheckBalance.EMPTY_SETTING", setting)
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}

	apiEndPoint := setting.ApiEndPoint
	// POST Endpoint: {{API_ENDPOINT}}/api/v1/merchant/balance?merchantId={{MERCHANT_ID}}
	epUrl := fmt.Sprintf("%s/api/v1/merchant/balance?merchantId=%s", apiEndPoint, setting.MerchantId)

	// [SysLog]
	syslogId, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
		Name:   "PostmanPayCheckBalance",
		Status: "CALL",
		JsonReq: helper.StructJson(map[string]interface{}{
			"epUrl":   epUrl,
			"setting": setting,
		}),
	})
	if err != nil {
		log.Println("PostmanPayCheckBalance.CreatePaygateSystemLog.ERROR", err)
	}

	// ====================================================================

	client := &http.Client{}
	client.Timeout = 30 * time.Second

	reqExternal, _ := http.NewRequest("GET", epUrl, nil)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("x-api-key", setting.AccessKey)
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}

	// [SysLog] Update SystemLog for Response.
	jsonResponse := helper.StructJson(map[string]interface{}{
		"epUrl": epUrl,
		"resp":  string(responseData),
	})
	if err := r.UpdatePaygateSystemLog(*syslogId, model.PaygateSystemLogUpdateBody{
		JsonResponse: &jsonResponse,
	}); err != nil {
		log.Println("PostmanPayCheckBalance.UpdatePaygateSystemLog.ERROR", err)
	}

	if response.StatusCode != 200 {
		log.Println(response.StatusCode)
		log.Println(string(responseData))
		var errMsg3 model.PostmanPayErrorRemoteResponse
		errJson3 := json.Unmarshal(responseData, &errMsg3)
		if errJson3 == nil {
			return nil, errors.New(errMsg3.Message)
		} else {
			log.Println("Unmarshal.errJson3 ------> ", errJson3)
		}
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	}

	var result model.PostmanPayCheckBalanceRemoteResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("PostmanPayCheckBalance resp.Body ------> ", string(responseData))
		log.Println("Unmarshal.Err ------> ", errJson)
		var errMsg3 model.PostmanPayErrorRemoteResponse
		errJson3 := json.Unmarshal(responseData, &errMsg3)
		if errJson3 == nil {
			return nil, errors.New(errMsg3.Message)
		} else {
			log.Println("Unmarshal.errJson3 ------> ", errJson3)
		}
		return nil, errors.New("INVALID_UNMARSHAL_JSON")
	}

	fmt.Println("PostmanPayCheckBalanceRemoteResponse.result", result)

	return &result, nil
}

func (r repo) PostmanPayWithdraw(setting model.PaygateAccountResponse, req model.PostmanPayWithdrawCreateRemoteRequest) (*model.PostmanPayWithdrawCreateRemoteResponse, error) {

	// [Prerequisites] EP + CLIENTID + APIKEY + SECRET + MERCHANTID
	if setting.ApiEndPoint == "" || setting.PartnerKey == "" || setting.AccessKey == "" || setting.SecretKey == "" || setting.MerchantId == "" {
		log.Println("PostmanPayWithdraw.EMPTY_SETTING", setting)
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}

	// Will Check Balance before Withdraw
	balance, err := r.PostmanPayCheckBalance(setting)
	if err != nil {
		return nil, errors.New("PAYGATE_CANT_CHECK_BALANCE")
	}
	if balance.Data.Balance < req.Amount || balance.Data.OperateBalance < req.Amount {
		log.Println("balance.Data.Balance", balance.Data.Balance, "balance.Data.OperateBalance", balance.Data.OperateBalance, "req.Amount", req.Amount)
		return nil, errors.New("PAYGATE_INSUFFICIENT_BALANCE")
	}

	actionAtUtc := time.Now().UTC()
	timestamp := r.GetPostmanPayTimestamp(actionAtUtc)
	sign := r.PostmanPayCreateSignature(timestamp, setting.MerchantId, setting.PartnerKey, setting.SecretKey)

	req.ClientId = setting.PartnerKey
	req.MerchantId = setting.MerchantId
	req.Signature = sign
	req.Timestamp = timestamp

	apiEndPoint := setting.ApiEndPoint
	// POST Endpoint: {{API_ENDPOINT}}/api/v1/payout/create
	epUrl := fmt.Sprintf("%s/api/v1/payout/create", apiEndPoint)

	// SysLog
	if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
		Name:   "PostmanPayWithdraw.PostmanPayWithdraw",
		Status: "CALL",
		JsonReq: helper.StructJson(map[string]interface{}{
			"epUrl":   epUrl,
			"setting": setting,
			"req":     req,
		}),
	}); err != nil {
		log.Println("CreatePaygateSystemLog.CreateSysLog", err)
	}

	client := &http.Client{}
	client.Timeout = 30 * time.Second

	jsonBody, _ := json.Marshal(req)
	payload := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest("POST", epUrl, payload)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("x-api-key", setting.AccessKey)
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}

	fmt.Println("PostmanPayWithdraw.resp.Body", string(responseData))

	if response.StatusCode != 200 {
		log.Println(response.StatusCode)
		log.Println(string(responseData))
		var errMsg3 model.PostmanPayErrorRemoteResponse
		errJson3 := json.Unmarshal(responseData, &errMsg3)
		if errJson3 == nil {
			return nil, errors.New(errMsg3.Message)
		} else {
			log.Println("Unmarshal.errJson3 ------> ", errJson3)
		}
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	}

	//  {"code":"10002","message":"PARTNER_KEY Invalid","data":""}

	var result model.PostmanPayWithdrawCreateRemoteResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("PostmanPayWithdraw resp.Body ------> ", string(responseData))
		log.Println("Unmarshal.Err ------> ", errJson)
		var errMsg3 model.PostmanPayErrorRemoteResponse
		errJson3 := json.Unmarshal(responseData, &errMsg3)
		if errJson3 == nil {
			return nil, errors.New(errMsg3.Message)
		} else {
			log.Println("Unmarshal.errJson3 ------> ", errJson3)
		}
		return nil, errors.New("INVALID_UNMARSHAL_JSON")
	}

	fmt.Println("PostmanPayWithdrawCreateRemoteResponse.result", result)

	if strings.ToLower(result.Status) != "success" {
		// SysLog
		if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "PostmanPayWithdraw",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(req),
			JsonResponse: helper.StructJson(map[string]interface{}{"resp": string(responseData), "result": helper.StructJson(result)}),
		}); err != nil {
			log.Println("PostmanPayWithdraw.CreateSysLog", err)
		}
		return nil, errors.New(result.Message)
	}
	return &result, nil
}

func (r repo) CreatePostmanPayWebhook(body model.PostmanPayWebhookCreateBody) (*int64, error) {

	if err := r.db.Table("paygate_postmanpay_webhook").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) GetDbPostmanPayOrderList(req model.PostmanPayOrderListRequest) ([]model.PostmanPayOrderResponse, int64, error) {

	var list []model.PostmanPayOrderResponse
	var total int64

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("paygate_postmanpay_order as tb_order")
	count = count.Select("tb_order.id")
	if req.UserId != nil {
		count = count.Where("tb_order.user_id = ?", req.UserId)
	}
	if req.OrderTypeId != nil {
		count = count.Where("tb_order.order_type_id = ?", req.OrderTypeId)
	}
	if req.OrderNo != "" {
		count = count.Where("tb_order.order_no = ?", req.OrderNo)
	}
	if req.TransactionNo != "" {
		count = count.Where("tb_order.transaction_no = ?", req.TransactionNo)
	}
	if req.Amount != "" {
		count = count.Where("tb_order.amount = ?", req.Amount)
	}
	if req.Status != "" {
		count = count.Where("tb_order.transaction_status = ?", req.Status)
	}

	if err := count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount, tb_order.transfer_amount AS transfer_amount"
		selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
		selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.payment_page_url AS payment_page_url"
		selectedFields += ", tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
		selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

		query := r.db.Table("paygate_postmanpay_order as tb_order")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN paygate_order_type AS tb_type ON tb_order.order_type_id = tb_type.id")
		if req.UserId != nil {
			query = query.Where("tb_order.user_id = ?", req.UserId)
		}
		if req.OrderTypeId != nil {
			query = query.Where("tb_order.order_type_id = ?", req.OrderTypeId)
		}
		if req.OrderNo != "" {
			query = query.Where("tb_order.order_no = ?", req.OrderNo)
		}
		if req.TransactionNo != "" {
			query = query.Where("tb_order.transaction_no = ?", req.TransactionNo)
		}
		if req.Amount != "" {
			query = query.Where("tb_order.amount = ?", req.Amount)
		}
		if req.Status != "" {
			query = query.Where("tb_order.transaction_status = ?", req.Status)
		}

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetDbPostmanPayOrderById(id int64) (*model.PostmanPayOrderResponse, error) {

	var record model.PostmanPayOrderResponse

	selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount, tb_order.transfer_amount AS transfer_amount"
	selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
	selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.payment_page_url AS payment_page_url"
	selectedFields += ", tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
	selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

	if err := r.db.Table("paygate_postmanpay_order as tb_order").
		Select(selectedFields).
		Joins("LEFT JOIN paygate_order_type AS tb_type ON tb_order.order_type_id = tb_type.id").
		Where("tb_order.id = ?", id).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetPendingPostmanPayDepositOrder(id int64) (*model.PostmanPayOrderResponse, error) {

	var record model.PostmanPayOrderResponse

	selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount, tb_order.transfer_amount AS transfer_amount"
	selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
	selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.qr_base64 AS qr_base64, tb_order.extra_promptpay_id AS extra_promptpay_id, tb_order.payment_page_url AS payment_page_url"
	selectedFields += ", tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
	selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

	if err := r.db.Table("paygate_postmanpay_order as tb_order").
		Select(selectedFields).
		Joins("LEFT JOIN paygate_order_type AS tb_type ON tb_order.order_type_id = tb_type.id").
		Where("tb_order.id = ?", id).
		Where("tb_order.transaction_status = ?", "WAIT_PAYMENT").
		Order("tb_order.id DESC").
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetLastestPostmanPayDepositOrderByUserId(userId int64) (*model.PostmanPayOrderResponse, error) {

	var record model.PostmanPayOrderResponse

	selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount, tb_order.transfer_amount AS transfer_amount"
	selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
	selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.qr_base64 AS qr_base64, tb_order.extra_promptpay_id AS extra_promptpay_id, tb_order.payment_page_url AS payment_page_url"
	selectedFields += ", tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
	selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

	if err := r.db.Table("paygate_postmanpay_order as tb_order").
		Select(selectedFields).
		Joins("LEFT JOIN paygate_order_type AS tb_type ON tb_order.order_type_id = tb_type.id").
		Where("tb_order.order_type_id = ?", model.PAYGATE_ORDER_TYPE_DEPOSIT).
		Where("tb_order.user_id = ?", userId).
		// Where("tb_order.transaction_status = ?", "WAIT_PAYMENT").
		Order("tb_order.id DESC").
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetDbPostmanPayOrderByRefId(refId int64) (*model.PostmanPayOrderResponse, error) {

	var record model.PostmanPayOrderResponse

	selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount, tb_order.transfer_amount AS transfer_amount"
	selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
	selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.payment_page_url AS payment_page_url"
	selectedFields += ", tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
	selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

	if err := r.db.Table("paygate_postmanpay_order as tb_order").
		Select(selectedFields).
		Joins("LEFT JOIN paygate_order_type AS tb_type ON tb_order.order_type_id = tb_type.id").
		Where("tb_order.ref_id = ?", refId).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) CheckPostmanPayDepositOrderInLast5Minutes(userId int64, amount float64) (*model.PostmanPayOrderResponse, error) {

	var record model.PostmanPayOrderResponse

	selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount, tb_order.transfer_amount AS transfer_amount"
	selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
	selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.payment_page_url AS payment_page_url"
	selectedFields += ", tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
	selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

	query := r.db.Table("paygate_postmanpay_order as tb_order")
	query = query.Select(selectedFields)
	query = query.Joins("LEFT JOIN paygate_order_type AS tb_type ON tb_order.order_type_id = tb_type.id")
	query = query.Where("tb_order.user_id = ?", userId)
	query = query.Where("tb_order.amount = ?", amount)
	if err := query.
		Where("tb_order.transaction_status = ?", "WAIT_PAYMENT").
		Order("tb_order.id DESC").
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) CreateDbPostmanPayOrder(body model.PostmanPayOrderCreateBody) (*int64, error) {

	if err := r.db.Table("paygate_postmanpay_order").Create(&body).Error; err != nil {
		return nil, err
	}

	// Use Random UUID as OrderNo //
	uuid := helper.GenerateRandomUuid(16)
	agentName := uuid

	orderNo := fmt.Sprintf("%v%v%v", agentName, time.Now().Format("0601"), body.Id)
	if body.OrderNo == "" {
		updateBody := make(map[string]interface{})
		updateBody["order_no"] = orderNo
		if err := r.db.Table("paygate_postmanpay_order").Where("id = ?", body.Id).Updates(updateBody).Error; err != nil {
			return nil, err
		}
	}
	return &body.Id, nil
}

func (r repo) UpdateDbPostmanPayOrderError(id int64, remark string) error {

	updateBody := map[string]interface{}{
		"transaction_status": "ERROR",
		"remark":             remark,
	}
	sql := r.db.Table("paygate_postmanpay_order").Where("id = ?", id).Where("transaction_status = ?", "PENDING")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdateDbPostmanPayOrder(id int64, body model.PostmanPayOrderUpdateBody) error {

	if err := r.db.Table("paygate_postmanpay_order").Where("id = ?", id).Updates(body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) ApproveDbPostmanPayOrder(id int64, webhookStatus string) error {

	updateBody := make(map[string]interface{})
	updateBody["transaction_status"] = webhookStatus
	updateBody["payment_at"] = time.Now()

	sql := r.db.Table("paygate_postmanpay_order").Where("id = ?", id).Where("transaction_status = ?", "WAIT_PAYMENT")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdatePostmanPayOrderBankSucess(id int64, transId int64, actionBy int64) error {

	updateBody := map[string]interface{}{
		"bank_transaction_id":     transId,
		"bank_transaction_status": "SUCCESS",
		"action_by":               actionBy,
	}

	sql := r.db.Table("paygate_postmanpay_order").Where("id = ?", id).Where("bank_transaction_status = ?", "PENDING")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}
