package repository

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"errors"
	"fmt"
	"os"

	"github.com/go-sql-driver/mysql"
	"github.com/pquerna/otp/totp"
	"gorm.io/gorm"
)

func NewAdminRepository(db *gorm.DB) AdminRepository {
	return &repo{db}
}

type AdminRepository interface {
	GetAdmin(id int64) (*model.Admin, []model.PermissionList, *model.GroupDetail, *model.AdminWebSetting, error)
	GetAdminById(id int64) (*model.Admin, error)
	GetAdminList(query model.AdminListQuery) (*[]model.AdminList, *int64, error)
	GetGroup(groupId int) (*model.AdminGroupPermissionResponse, error)
	GetGroupList(query model.AdminGroupQuery) (*[]model.GroupCountList, *int64, error)
	GetAdminByUsername(username string) (*model.Admin, error)
	GetAdminGroup(adminId int64) (*model.AdminGroupId, error)
	CheckAdmin(username string) (bool, error)
	CheckAdminById(id int64) (bool, error)
	CheckPhone(phone string) (bool, error)
	GetAdminCount(id int64) (int64, error)
	CreateAdmin(admin model.Admin) error
	CreateGroupAdmin(data []model.AdminPermissionList) error
	UpdateGroup(groupId int64, name string, data []model.AdminPermissionList) error
	UpdateAdmin(adminId int64, OldGroupId *int64, data model.UpdateAdmin) error
	UpdatePassword(adminId int64, data model.AdminUpdatePassword) error
	DeleteAdmin(adminId int64) error

	GetAdminLoginLogList(query model.AdminLoginLogListRequest) ([]model.AdminAction, int64, error)
	// WebInfo
	GetWebInfo() (*model.DownlineWebInfo, error)
	// SysLog
	CreateSystemLog(body model.SystemLogCreateBody) (*int64, error)
	// SingleSession
	SetAdminSingleSession(adminId int64, token string) error

	// totp
	TotpGererateSecret(req model.TotpGererateSecret) (*model.TotpGererateSecretResponse, error)
	TotpVerifyCheck(req model.TotpVerifyCheck) (bool, error)
	TotpReset(adminId int64) error
	GetAdminTotpSetting() (*model.GetAdminTotpSetting, error)
	GetLoginAdminWebSetting(id int64) (*model.AdminWebSetting, error)

	CreateAdminAction(body model.AdminActionCreateBody) (*int64, error)

	GetAdminOptionList() ([]model.GetAdminOptionList, error)
}

func (r repo) GetAdminList(query model.AdminListQuery) (*[]model.AdminList, *int64, error) {

	var list []model.AdminList
	var total int64

	exec := r.db.Model(model.Admin{}).Table("admin a").
		Joins("LEFT JOIN admin_group ag ON ag.id = a.admin_group_id").
		Select("a.id, a.username, a.fullname, a.phone, a.email, ag.name AS admin_group_name, a.status, a.role")

	if query.Search != "" {
		exec = exec.Where("a.username LIKE ?", "%"+query.Search+"%")
	}

	if query.Status != "" {
		exec = exec.Where("a.status = ?", query.Status)
	}

	if query.AdminGroupId != "" {
		exec = exec.Where("a.admin_group_id = ?", query.AdminGroupId)
	}

	if err := exec.
		Where("a.role != ?", "SUPER_ADMIN").
		Limit(query.Limit).
		Offset(query.Limit * query.Page).
		Find(&list).
		Error; err != nil {
		return nil, nil, err
	}

	execTotal := r.db.Model(model.Admin{}).Table("admin a").
		Joins("LEFT JOIN admin_group ag ON ag.id = a.admin_group_id").
		Select("a.id")

	if query.Search != "" {
		execTotal = execTotal.Where("a.username LIKE ?", "%"+query.Search+"%")
	}

	if query.Status != "" {
		execTotal = execTotal.Where("a.status = ?", query.Status)
	}

	if query.AdminGroupId != "" {
		execTotal = execTotal.Where("a.admin_group_id = ?", query.AdminGroupId)
	}

	if err := execTotal.
		Where("a.role != ?", "SUPER_ADMIN").
		Count(&total).
		Error; err != nil {
		return nil, nil, err
	}

	return &list, &total, nil
}

func (r repo) GetAdmin(id int64) (*model.Admin, []model.PermissionList, *model.GroupDetail, *model.AdminWebSetting, error) {

	var admin *model.Admin
	var permission []model.PermissionList
	var group *model.GroupDetail
	var adminWebSetting *model.AdminWebSetting

	if err := r.db.Model(model.Admin{}).Table("admin").
		Select("id, username, fullname, phone, email, role, status, admin_group_id, is_verify_totp").
		Where("id = ?", id).
		Take(&admin).
		Error; err != nil {
		return nil, nil, nil, nil, err
	}

	if admin != nil && admin.AdminGroupId != 0 {
		if err := r.db.Table("admin_group_permission agp").
			Joins("LEFT JOIN permission p ON p.id = agp.permission_id").
			Select("p.id, p.name, p.permission_key").
			Where("agp.group_id = ?", admin.AdminGroupId).
			Find(&permission).
			Error; err != nil {
			return nil, nil, nil, nil, err
		}
		if err := r.db.Table("configuration_web AS con").
			Select("con.is_totp_verify, con.token_expired_minute").
			Take(&adminWebSetting).
			Error; err != nil {
			adminWebSetting.IsTotpVerify = false
			adminWebSetting.TokenExpiredMinute = 0
		}

		if err := r.db.Table("admin_group").
			Select("id, name").
			Where("id = ?", admin.AdminGroupId).
			Take(&group).
			Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return admin, permission, nil, adminWebSetting, nil
			}
			return nil, nil, nil, nil, err
		}

	}

	return admin, permission, group, adminWebSetting, nil
}

func (r repo) GetAdminTotpSetting() (*model.GetAdminTotpSetting, error) {

	var totpSetting *model.GetAdminTotpSetting

	if err := r.db.Table("configuration_web AS con").
		Select("con.is_totp_verify").
		Take(&totpSetting).
		Error; err != nil {
		totpSetting.IsTotpVerify = false
	}

	return totpSetting, nil
}

func (r repo) GetLoginAdminWebSetting(id int64) (*model.AdminWebSetting, error) {

	var totpSetting *model.AdminWebSetting

	if err := r.db.Table("configuration_web AS con").
		// TokenExpiredMinute
		Select("con.is_totp_verify, con.token_expired_minute").
		Take(&totpSetting).
		Error; err != nil {
		totpSetting.IsTotpVerify = false
		totpSetting.TokenExpiredMinute = 0
	}
	// super admin
	if id == 1 {
		totpSetting.TokenExpiredMinute = 0

		if totpSetting.IsTotpVerify {
			totpSetting.IsTotpVerify = false
		}
	}

	return totpSetting, nil
}

func (r repo) GetGroup(groupId int) (*model.AdminGroupPermissionResponse, error) {

	var group model.Group
	var permission []model.PermissionList

	if err := r.db.Table("admin_group").
		Select("id, name").
		Where("id = ?", groupId).
		First(&group).
		Error; err != nil {
		return nil, err
	}

	if err := r.db.Table("permission p").
		Select("p.id, p.name, gp.is_read, gp.is_write").
		Joins("LEFT JOIN admin_group_permission gp ON gp.permission_id = p.id").
		Where("gp.group_id = ?", groupId).
		Where("gp.deleted_at IS NULL").
		Find(&permission).
		Error; err != nil {
		return nil, err
	}

	var result model.AdminGroupPermissionResponse
	result.Id = group.Id
	result.Name = group.Name
	result.Permissions = permission

	return &result, nil
}

func (r repo) GetGroupList(query model.AdminGroupQuery) (*[]model.GroupCountList, *int64, error) {

	var exec = r.db.Model(model.Group{}).
		Table("admin_group").
		Select("id, name, admin_count").
		Limit(query.Limit).
		Offset(query.Limit * query.Page)

	if query.Search != "" {
		exec = exec.Where("name LIKE ?", "%"+query.Search+"%")
	}

	var list []model.GroupCountList
	if err := exec.
		Find(&list).
		Error; err != nil {
		return nil, nil, err
	}

	var execTotal = r.db.Model(model.Group{}).
		Table("admin_group").
		Select("id")

	if query.Search != "" {
		execTotal = execTotal.Where("name LIKE ?", "%"+query.Search+"%")
	}

	var total int64
	if err := execTotal.
		Count(&total).
		Error; err != nil {
		return nil, nil, err
	}

	return &list, &total, nil
}

func (r repo) GetAdminByUsername(username string) (*model.Admin, error) {

	var admin model.Admin

	if err := r.db.Table("admin").
		Select("id, status, username, phone, password, email, role, is_verify_totp").
		Where("username = ?", username).
		First(&admin).
		Error; err != nil {
		return nil, err
	}
	return &admin, nil
}

func (r repo) GetAdminGroup(adminId int64) (*model.AdminGroupId, error) {

	var admin *model.AdminGroupId

	if err := r.db.Table("admin").
		Select("admin_group_id").
		Where("id = ?", adminId).
		First(&admin).
		Error; err != nil {

		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}

		return nil, err
	}

	return admin, nil
}

func (r repo) CheckAdmin(username string) (bool, error) {
	var user model.Admin

	if err := r.db.Table("admin").
		Where("username = ?", username).
		First(&user).
		Error; err != nil {

		if err == gorm.ErrRecordNotFound {
			return false, nil
		}

		return false, err
	}

	if user.Id != 0 {
		return false, nil
	}

	return true, nil
}

func (r repo) CheckAdminById(id int64) (bool, error) {
	var user model.Admin

	if err := r.db.Table("admin").
		Where("id = ?", id).
		First(&user).
		Error; err != nil {

		if err == gorm.ErrRecordNotFound {
			return false, nil
		}

		return false, err
	}

	return true, nil
}

func (r repo) CheckPhone(phone string) (bool, error) {
	var user model.Admin

	if err := r.db.Table("admin").
		Where("phone = ?", phone).
		First(&user).
		Error; err != nil {

		if err == gorm.ErrRecordNotFound {
			return false, nil
		}

		return false, err
	}

	return true, nil
}

func (r repo) GetAdminCount(id int64) (int64, error) {

	var adminCount int64
	if err := r.db.Table("admin_group").
		Select("admin_count").
		Where("id = ?", id).
		Find(&adminCount).
		Limit(1).
		Error; err != nil {

		if err == gorm.ErrRecordNotFound {
			return 0, nil
		}

		return 0, err
	}

	return adminCount, nil
}

func (r repo) CreateAdmin(admin model.Admin) error {

	tx := r.db.Begin()

	if err := tx.Table("admin").
		Create(&admin).
		Error; err != nil {
		tx.Rollback()

		var dup *mysql.MySQLError
		if errors.As(err, &dup); dup.Number == 1062 {
			return errors.New("username or Email already exists")
		}

		return err
	}

	if err := tx.Table("admin_group").
		Where("id = ?", admin.AdminGroupId).
		Update("admin_count", gorm.Expr("admin_count + ?", 1)).
		Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Commit().Error; err != nil {
		return err
	}

	return nil
}

func (r repo) CreateGroupAdmin(data []model.AdminPermissionList) error {

	if err := r.db.Table("admin_group_permission").
		Create(&data).
		Error; err != nil {
		return err
	}

	return nil
}

func (r repo) UpdateGroup(groupId int64, name string, data []model.AdminPermissionList) error {

	tx := r.db.Begin()

	if err := tx.Table("admin_group").
		Where("id = ?", groupId).
		Update("name", name).
		Error; err != nil {

		tx.Rollback()

		var dup *mysql.MySQLError
		if errors.As(err, &dup); dup.Number == 1062 {
			return errors.New("มีชื่อกลุ่มนี้อยู่แล้ว")
		}

		return err
	}

	if err := tx.Table("admin_group_permission").
		Where("group_id = ?", groupId).
		Delete(&model.AdminGroupPermission{}).
		Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Table("admin_group_permission").
		Create(&data).
		Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Commit().Error; err != nil {
		return err
	}

	return nil
}

func (r repo) UpdateAdmin(adminId int64, OldGroupId *int64, data model.UpdateAdmin) error {

	tx := r.db.Begin()

	if err := tx.Model(model.Admin{}).Table("admin").
		Where("id = ?", adminId).
		Updates(&data).
		Error; err != nil {
		tx.Rollback()
		return err
	}

	if data.AdminGroupId != nil && *data.AdminGroupId != *OldGroupId {

		if err := tx.Table("admin_group").
			Where("id = ?", data.AdminGroupId).
			Update("admin_count", gorm.Expr("admin_count + ?", 1)).
			Error; err != nil {
			tx.Rollback()
			return err
		}

		if err := tx.Table("admin_group").
			Where("id = ?", OldGroupId).
			Update("admin_count", gorm.Expr("admin_count - ?", 1)).
			Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	if err := tx.Commit().Error; err != nil {
		return err
	}

	return nil
}

func (r repo) UpdatePassword(adminId int64, data model.AdminUpdatePassword) error {

	if err := r.db.Model(model.Admin{}).Table("admin").
		Where("id = ?", adminId).
		Update("password", data.Password).
		Error; err != nil {
		return err
	}

	return nil
}

func (r repo) DeleteAdmin(adminId int64) error {

	var groupId int64

	tx := r.db.Begin()

	if err := tx.Table("admin").
		Select("admin_group_id").
		Where("id = ?", adminId).
		Find(&groupId).
		Limit(1).
		Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Table("admin").
		Where("id = ?", adminId).
		Delete(&model.Admin{}).
		Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Table("admin_group").
		Where("id = ?", groupId).
		Update("admin_count", gorm.Expr("admin_count - ?", 1)).
		Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Commit().Error; err != nil {
		return err
	}

	return nil
}

func (r repo) GetAdminLoginLogList(req model.AdminLoginLogListRequest) ([]model.AdminAction, int64, error) {

	// [20250331] is_show หน้า admin login log เอารายการแบบนี้กรองออกค่ะ INVALID_ADMIN_TOKEN_

	var list []model.AdminAction
	var total int64

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("admin_action as logs")
	count = count.Select("logs.id")
	count = count.Where("logs.is_show = ?", true)
	if req.Search != "" {
		search_like := fmt.Sprintf("%%%s%%", req.Search)
		count = count.Where(r.db.Where("logs.json_output LIKE ?", search_like))
	}
	if err := count.
		Where("logs.type_id = ?", model.ADMIN_ACTION_LOGIN).
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "*"
		query := r.db.Table("admin_action as logs")
		query = query.Select(selectedFields)
		query = query.Where("logs.is_show = ?", true)
		if req.Search != "" {
			search_like := fmt.Sprintf("%%%s%%", req.Search)
			query = query.Where(r.db.Where("logs.json_output LIKE ?", search_like))
		}

		// Sort by ANY //
		// req.SortCol = strings.TrimSpace(req.SortCol)
		// if req.SortCol != "" {
		// 	if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
		// 		req.SortAsc = "DESC"
		// 	} else {
		// 		req.SortAsc = "ASC"
		// 	}
		// 	query = query.Order(req.SortCol + " " + req.SortAsc)
		// }

		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Where("logs.type_id = ?", model.ADMIN_ACTION_LOGIN).
			Order("logs.created_at DESC").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) SetAdminSingleSession(adminId int64, token string) error {

	// get
	var admin model.AdminSingleSession
	if err := r.db.Table("admin_single_session").
		Select("id").
		Where("admin_id = ?", adminId).
		Take(&admin).
		Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			hashToken := helper.GetMD5Hash(token)
			createBody := model.AdminSingleSession{
				AdminId:  adminId,
				Md5Token: hashToken,
			}
			if err := r.db.Table("admin_single_session").
				Create(&createBody).
				Error; err != nil {
				return err
			}
			return nil
		}
		return err
	}

	// update
	hashToken := helper.GetMD5Hash(token)
	if err := r.db.Table("admin_single_session").
		Where("admin_id = ?", adminId).
		Update("md5_token", hashToken).
		Error; err != nil {
		return err
	}

	return nil
}

func (r repo) TotpGererateSecret(req model.TotpGererateSecret) (*model.TotpGererateSecretResponse, error) {

	var admin model.Admin
	if err := r.db.Table("admin").
		Select("id, is_verify_totp , email, username").
		Where("id = ?", req.AdminId).
		Take(&admin).
		Error; err != nil {
		return nil, err
	}

	setDefaultAccountName := admin.Email
	if admin.Email == "" {
		setDefaultAccountName = admin.Username
	}

	var response model.TotpGererateSecretResponse

	if !admin.IsVerifyTotp {
		getDomain := os.Getenv("DOMAIN_NAME")
		key, err := totp.Generate(totp.GenerateOpts{
			Issuer:      getDomain,
			AccountName: setDefaultAccountName,
		})
		if err != nil {
			return nil, err
		}

		if err := r.db.Table("admin").
			Where("id = ?", req.AdminId).
			Update("totp_secret", key.Secret()).
			Error; err != nil {
			return nil, err
		}
		response.QRCode = key.URL()
		response.Secret = key.Secret()
	} else {
		return nil, errors.New("เคยทำการยืนยันการใช้งาน TOTP ไปแล้ว")
	}

	return &response, nil
}

func (r repo) TotpVerifyCheck(req model.TotpVerifyCheck) (bool, error) {

	var admin model.AdminTotp
	if err := r.db.Table("admin").
		Select("id, totp_secret, is_verify_totp").
		Where("id = ?", req.AdminId).
		Take(&admin).
		Error; err != nil {
		return false, err
	}

	// validate
	if totp.Validate(req.Totp, admin.TotpSecret) {
		if err := r.db.Table("admin").
			Where("id = ?", req.AdminId).
			Update("is_verify_totp", true).
			Error; err != nil {
			return false, err
		}
		return true, nil
	}

	return false, nil
}

func (r repo) TotpReset(adminId int64) error {

	if err := r.db.Table("admin").
		Where("id = ?", adminId).
		Updates(map[string]interface{}{"totp_secret": "", "is_verify_totp": false}).
		Error; err != nil {
		return err
	}

	return nil
}

func (r repo) GetAdminOptionList() ([]model.GetAdminOptionList, error) {

	var record []model.GetAdminOptionList
	// ทำแบบ search ไปแล้ว แต่ เอา ให้ option dropdown แทน
	// showLimit := 30

	selectedFields := "id, username, fullname"

	query := r.db.Table("admin").Select(selectedFields)
	// if adminFullname != "" {
	// 	query = query.Where("fullname LIKE ? OR username LIKE ?", "%"+adminFullname+"%", "%"+adminFullname+"%")
	// }
	// query = query.Limit(showLimit)
	query = query.Scan(&record)
	if err := query.Error; err != nil {
		return nil, err
	}

	return record, nil
}
