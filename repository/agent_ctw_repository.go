package repository

import (
	"bytes"
	"cybergame-api/model"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"gorm.io/gorm"
)

func NewAgentCtwRepository(db *gorm.DB) AgentCtwRepository {
	return &repo{db}
}

type AgentCtwRepository interface {
	// admin
	GetInternalAgentCtwSetting() (*model.GetInternalAgentCtwSetting, error)
	UpdateAgentCtwSetting(body model.UpdateAgentCtwSetting) error
	ClearCacheAgentCtwSetting()

	// web
	GetAgentCtwGameList(urlDetail model.CallApiAgentCtwDetail) (*model.GetCtwGameListResponse, error)
	CallApiAgentCtwLaunch(urlDetail model.CallApiAgentCtwDetail, body model.CallApiAgentCtwLaunchBody) (*model.CallApiAgentCtwLaunchResponse, error)

	// internal
	GetMemberCode(id int64) (*string, error)

	// call back
	GetUserByMemberCode(memberCode string) (*model.GetUserByMemberCode, error)
	DecreaseUserCreditFromOtherAgent(body model.DecreaseUserCreditFromOtherAgentRequest) (*model.UserTransactionCreateResponse, error)
	IncreaseUserCreditFromOtherAgent(body model.IncreaseUserCreditFromOtherAgentRequest) (*model.UserTransactionCreateResponse, error)
	CallApiAgentCtwBetDetail(urlDetail model.CallApiAgentCtwDetail, body model.CallBackCtwGameBetDetailRequest) (*model.CallBackCtwGameBetDetail, error)
	CreateOrUpdateAgentCtwCallback(body model.CreateAgentCtwCallback) error
	UpdateAgentCtwCallback(body model.UpdateAgentCtwCallback) error
}

var saveCacheAgentCtwSetting *model.GetInternalAgentCtwSetting

func (r repo) GetInternalAgentCtwSetting() (*model.GetInternalAgentCtwSetting, error) {

	if saveCacheAgentCtwSetting != nil {
		return saveCacheAgentCtwSetting, nil
	}

	var result model.GetInternalAgentCtwSetting

	selectFields := "id, is_active, program_allow_use, ctw_app_id, ctw_app_private, ctw_url, ctw_href_back_url"

	query := r.db.Table("agent_ctw_setting")
	query = query.Where("id = ?", 1)
	query = query.Select(selectFields)
	query = query.Take(&result)
	if err := query.Error; err != nil {
		return nil, err
	}

	saveCacheAgentCtwSetting = &result

	return &result, nil
}

func (r repo) ClearCacheAgentCtwSetting() {
	saveCacheAgentCtwSetting = nil
}

func (r repo) UpdateAgentCtwSetting(body model.UpdateAgentCtwSetting) error {

	query := r.db.Table("agent_ctw_setting")
	query = query.Where("id = ?", 1)
	query = query.Updates(&body)
	if err := query.Error; err != nil {
		return err
	}

	saveCacheAgentCtwSetting = nil

	return nil
}

func (r repo) GetAgentCtwGameList(urlDetail model.CallApiAgentCtwDetail) (*model.GetCtwGameListResponse, error) {

	ctwAppId := urlDetail.CtwAppId
	ctwAppPrivate := urlDetail.CtwAppPrivate
	ctwUrl := urlDetail.CtwUrl
	fmt.Println("ctwUrl", urlDetail)

	url := fmt.Sprintf("%s/api/v1/game/list", ctwUrl)

	requestBody, err := json.Marshal(map[string]interface{}{})
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request body: %w", err)
	}

	req, err := http.NewRequest("GET", url, bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("AppID", ctwAppId)
	req.Header.Set("AppSecret", ctwAppPrivate)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	var games model.GetCtwGameListResponse
	decoder := json.NewDecoder(resp.Body)
	if err := decoder.Decode(&games); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &games, nil
}

func (r repo) CallApiAgentCtwLaunch(urlDetail model.CallApiAgentCtwDetail, body model.CallApiAgentCtwLaunchBody) (*model.CallApiAgentCtwLaunchResponse, error) {

	ctwAppId := urlDetail.CtwAppId
	ctwAppPrivate := urlDetail.CtwAppPrivate
	ctwUrl := urlDetail.CtwUrl
	fmt.Println("ctwUrl", urlDetail)

	url := fmt.Sprintf("%s/api/v1/game/launch", ctwUrl)

	requestBody, err := json.Marshal(body)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request body: %w", err)
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("AppID", ctwAppId)
	req.Header.Set("AppSecret", ctwAppPrivate)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	var games model.CallApiAgentCtwLaunchResponse
	decoder := json.NewDecoder(resp.Body)
	if err := decoder.Decode(&games); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &games, nil
}

func (r repo) CallApiAgentCtwBetDetail(urlDetail model.CallApiAgentCtwDetail, body model.CallBackCtwGameBetDetailRequest) (*model.CallBackCtwGameBetDetail, error) {

	ctwAppId := urlDetail.CtwAppId
	ctwAppPrivate := urlDetail.CtwAppPrivate
	ctwUrl := urlDetail.CtwUrl
	fmt.Println("ctwUrl", urlDetail)

	url := fmt.Sprintf("%s/api/v1/record/betdetail", ctwUrl)

	requestBody, err := json.Marshal(body)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request body: %w", err)
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("AppID", ctwAppId)
	req.Header.Set("AppSecret", ctwAppPrivate)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	var games model.CallBackCtwGameBetDetail
	decoder := json.NewDecoder(resp.Body)
	if err := decoder.Decode(&games); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &games, nil
}

func (r repo) CreateOrUpdateAgentCtwCallback(body model.CreateAgentCtwCallback) error {

	// case ของ call back อันนี้ต้องใช้เ round และ member code เป็น หลัก ไม่งั้น duplicate ได้
	var result model.CreateAgentCtwCallback
	selectFields := "id, payoff, bet_amount"
	query := r.db.Table("agent_ctw_callback")
	query = query.Where("round_id = ? AND member_code = ?", body.RoundId, body.MemberCode)
	query = query.Select(selectFields)
	query = query.Take(&result)
	if err := query.Error; err != nil {
		if err != gorm.ErrRecordNotFound {
			return err
		}
	}

	if result.Id == 0 {
		createQuery := r.db.Table("agent_ctw_callback")
		createQuery = createQuery.Create(&body)
		if err := createQuery.Error; err != nil {
			return err
		}
	} else {
		var updateBody model.UpdateAgentCtwCallback
		updateBody.UserId = &body.UserId
		updateBody.MemberCode = body.MemberCode
		if body.Payoff != 0 {
			updateBody.Payoff = &body.Payoff
		}
		if body.BetAmount != 0 {
			updateBody.BetAmount = &body.BetAmount
		}
		payoff := body.Payoff + result.Payoff
		betAmount := body.BetAmount + result.BetAmount
		// if payoff == betAmount {
		// 	payoff = 0
		// 	betAmount = 0
		// }
		// 	// ถ้าเท่ากันจะไม่นับ เป็น turn จะให้เป็น 0 P.Lay confirm 15 - 15 = 0
		winloseAmount := payoff - betAmount
		updateBody.WinloseAmount = &winloseAmount
		// stack
		// updateBody.Balance = &body.Balance
		// updateBody.BeforeBalance = &body.BeforeBalance
		updateBody.AfterBalance = &body.AfterBalance
		updateBody.TransactionId = &body.TransactionId
		updateBody.RoundId = body.RoundId
		updateBody.GameId = &body.GameId
		updateBody.CallbackReason = &body.CallbackReason
		updateBody.Remark = &body.Remark
		updateBody.IsSuccess = &body.IsSuccess

		updateQuery := r.db.Table("agent_ctw_callback")
		updateQuery = updateQuery.Where("id = ?", result.Id)
		updateQuery = updateQuery.Updates(&updateBody)
		if err := updateQuery.Error; err != nil {
			return err
		}
	}

	return nil
}

func (r repo) UpdateAgentCtwCallback(body model.UpdateAgentCtwCallback) error {

	query := r.db.Table("agent_ctw_callback")
	query = query.Where("round_id = ? AND member_code = ?", body.RoundId, body.MemberCode)
	query = query.Updates(&body)
	if err := query.Error; err != nil {
		return err
	}

	return nil
}

func (r repo) GetAgentCtwCallback(req model.AgentCtwCallbackSummaryRequest) ([]model.AgentCtwCallbackSummary, error) {

	var result []model.AgentCtwCallbackSummary
	selectFields := "user_id, member_code, sum(payoff) as total_payoff, sum(bet_amount) as total_bet"
	selectFields += ", sum(winlose_amount) as total_winlose"
	query := r.db.Table("agent_ctw_callback")
	query = query.Select(selectFields)
	query = query.Where("is_success = ?", 1)
	if req.StatementDate != "" {
	}
	if req.StatementDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.StatementDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("created_at >= ?", startDateAtBkk)

		endDateAtBkk, err := r.ParseEodBkk(req.StatementDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("created_at <= ?", endDateAtBkk)
	}
	query = query.Group("user_id, member_code")
	query = query.Offset((req.PageIndex - 1) * req.PageSize).Limit(req.PageSize)
	query = query.Scan(&result)
	if err := query.Error; err != nil {
		return nil, err
	}

	return result, nil
}
