package repository

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"
)

var promotionReturnCuttypeOptions *model.SelectOptionsCache
var promotionReturnSetting *model.PromotionReturnSettingResponse

func NewPromotionReturnRepository(db *gorm.DB) PromotionReturnRepository {
	return &repo{db}
}

type PromotionReturnRepository interface {
	GetDb() *gorm.DB
	GetUserTurnoverStatementList(req model.UserTurnoverStatementListRequest) ([]model.UserTurnoverStatementResponse, int64, error)
	CancelUserTurnoverStatement(id int64) error
	GetTurnoverUserStatementById(id int64) (*model.TurnoverUserStatementResponse, error)

	// REF-PROMOTION_RETURN_TURN
	GetReturnTurnSetting() (*model.PromotionReturnTurnSettingResponse, error)

	GetReturnCutTypeOptions() ([]model.SelectOptions, error)
	GetReturnCutTypeById(id int64) (*model.SelectOptions, error)
	GetReturnSetting() (*model.PromotionReturnSettingResponse, error)
	GetFirstActivityDaily() (*model.GetActivityDailyResponse, error)
	GetActivityLuckyWheelSetting() (*model.ActivityLuckyWheelSettingResponse, error)
	GetActivityDailyV2IsActive() (*model.GetActivityDailyV2IsActive, error)
	CreateReturnSetting(body model.PromotionReturnSettingCreateBody) (*int64, error)
	UpdateReturnSetting(id int64, body model.PromotionReturnSettingUpdateBody) error
	GetReturnHistoryUserList(req model.PromotionReturnHistoryUserListRequest) ([]model.PromotionReturnHistoryUserResponse, int64, error)
	GetReturnHistoryAllUserList(req model.PromotionReturnHistoryUserListRequest) ([]model.PromotionReturnHistoryUserResponse, int64, error)
	GetTotalUserReturnList(req model.PromotionReturnHistoryUserListRequest) (map[int64]model.PromotionReturnHistoryUserTotalResponse, error)
	GetReturnHistoryUserSummary(req model.PromotionReturnHistoryUserListRequest) (*model.PromotionReturnHistoryUserSummaryResponse, error)
	GetReturnHistoryLogList(req model.PromotionReturnHistoryListRequest) ([]model.PromotionReturnHistoryReponse, int64, error)

	GetCurrentReturnTransactionList(userId int64) ([]model.PromotionReturnTransaction, error)
	GetReturnTransactionList(req model.PromotionReturnTransactionListRequest) ([]model.PromotionReturnTransaction, int64, error)
	GetReturnUncalcTransactionList(req model.PromotionReturnTransactionUncalcListRequest) ([]model.PromotionReturnTransaction, int64, error)
	CreateReturnTransaction(body model.PromotionReturnTransactionCreateBody) (*int64, error)
	GetReturnTransactionListByDailyKeyList(bulkBody map[string]model.PromotionReturnTransactionCreateBody) ([]model.PromotionReturnTransactionDailyKey, int64, error)
	CreateReturnTransactionBulk(bulkBody map[string]model.PromotionReturnTransactionCreateBody) error
	UpdateCalcReturnTransaction(id int64, body model.PromotionReturnTransactionCalcBody) error
	UpdateTakeReturnTransaction(id int64, body model.PromotionReturnTransactionUpdateBody) error
	UpdateExpriedReturnTransaction(id int64, body model.PromotionReturnTransactionUpdateBody) error
	// Play_log
	GetUserMemberInfoById(id int64) (*model.UserResponse, error)
	GetDailyTotalUserPaylogList(statementDate string) ([]model.PlaylogTotalAmount, error)
	CheckSystemHasDailyCuttypeInWeek(statementDate string) (int64, error)
	GetWeeklyTotalUserPaylogList(statementDate string) ([]model.PlaylogTotalAmount, error)
	CheckDailyPlayLog(statementDate string) (*model.CronPlayLogCheckReponse, error)
	// REF-USER_CREDIT
	IncreaseUserCredit(body model.UserTransactionCreateRequest) (*model.UserTransactionCreateResponse, error)
	DecreaseUserCredit(body model.UserTransactionCreateRequest) (*model.UserTransactionCreateResponse, error)
	// REF-CRON_ACTION
	GetCronActionById(id int64) (*model.CronAction, error)
	GetCronActionByActionKey(actionKey string) (*model.CronAction, error)
	CreateCronAction(body model.CronActionCreateBody) (int64, error)
	SetSuccessCronAction(id int64, remark string) error
	SetFailCronAction(id int64, remark string) error
	// REF-CRON_STATUS
	UpdateAgcCronCalcStatus(name string, statementDate string, status string) error
	// REF-USER_INCOME
	CreateUserIncomeLog(body model.UserIncomeLogCreateBody) (*int64, error)
	GetMarketingConfigByKey(configKey string, defaultVal string) (*model.MarketingConfig, error)
	GetUserIncomeLogById(id int64) (*model.UserIncomeLogResponse, error)
	ConfirmUserIncomeLog(body model.UserIncomeLogConfirmBody) error

	// WebSocket(reqAlert model.WebScoket) error

	CreateAdminAction(body model.AdminActionCreateBody) (*int64, error)

	// web actvity
	GetActivityMenu(req model.GetActivityMenuRequest) ([]model.GetActivityMenuResponse, error)
}

func (r repo) GetUserTurnoverStatementList(req model.UserTurnoverStatementListRequest) ([]model.UserTurnoverStatementResponse, int64, error) {

	var list []model.UserTurnoverStatementResponse
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("turnover_statement as statements")
	count = count.Select("statements.id")
	count = count.Joins("LEFT JOIN user as users ON users.id = statements.user_id")
	if req.FromDate != "" {
		fromTimeUtc, err := r.ParseBodUTC(req.FromDate)
		if err != nil {
			return nil, total, err
		}
		count = count.Where("statements.start_turn_at >= ?", fromTimeUtc.Format("2006-01-02")+" 00:00:00")
	}
	if req.ToDate != "" {
		toTimeUtc, err := r.ParseEodUTC(req.ToDate)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("statements.start_turn_at <= ?", toTimeUtc.Format("2006-01-02")+" 23:59:59")
	}
	if req.PromotionName != "" {
		count = count.Where("statements.promotion_name LIKE ?", "%"+req.PromotionName+"%")
	}
	if req.StatusId != nil {
		count = count.Where("statements.status_id = ?", req.StatusId)
	}
	if req.Search != "" {
		search_like := fmt.Sprintf("%%%s%%", req.Search)
		count = count.Where(r.db.Where("users.username LIKE ?", search_like).Or("users.fullname LIKE ?", search_like))
	}
	if err = count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "statements.id as id, statements.user_id as user_id, statements.type_id as type_id, statements.ref_type_id as ref_type_id, statements.status_id as status_id, statements.start_turn_amount as start_turn_amount"
		selectedFields += ", statements.promotion_name as promotion_name, statements.bonus_amount as bonus_amount"
		selectedFields += ", statements.start_turn_at as start_turn_at, statements.total_turn_amount as total_turn_amount, statements.end_turn_at as end_turn_at, statements.created_at as created_at, statements.updated_at as updated_at"
		selectedFields += ", types.name as type_name, statuses.name as status_name"
		selectedFields += ", users.username as username, users.fullname as fullname"
		query := r.db.Table("turnover_statement as statements")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN turnover_statement_type as types ON types.id = statements.type_id")
		query = query.Joins("LEFT JOIN turnover_statement_status as statuses ON statuses.id = statements.status_id")
		query = query.Joins("LEFT JOIN user as users ON users.id = statements.user_id")
		if req.FromDate != "" {
			fromTimeUtc, err := r.ParseBodUTC(req.FromDate)
			if err != nil {
				return nil, total, err
			}
			query = query.Where("statements.start_turn_at >= ?", fromTimeUtc.Format("2006-01-02")+" 00:00:00")
		}
		if req.ToDate != "" {
			toTimeUtc, err := r.ParseEodUTC(req.ToDate)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("statements.start_turn_at <= ?", toTimeUtc.Format("2006-01-02")+" 23:59:59")
		}
		if req.PromotionName != "" {
			query = query.Where("statements.promotion_name LIKE ?", "%"+req.PromotionName+"%")
		}
		if req.StatusId != nil {
			query = query.Where("statements.status_id = ?", req.StatusId)
		}
		if req.Search != "" {
			search_like := fmt.Sprintf("%%%s%%", req.Search)
			query = query.Where(r.db.Where("users.username LIKE ?", search_like).Or("users.fullname LIKE ?", search_like))
		}
		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) CancelUserTurnoverStatement(id int64) error {

	if err := r.db.Table("turnover_statement").Where("id = ?", id).Where("status_id = ?", model.TURNOVER_STATEMENT_STATUS_PENDING).Updates(map[string]interface{}{"status_id": model.TURNOVER_STATEMENT_STATUS_CANCELED}).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetReturnCutTypeById(id int64) (*model.SelectOptions, error) {

	var record model.SelectOptions

	options, err := r.GetReturnCutTypeOptions()
	if err != nil {
		return nil, err
	}

	for _, v := range options {
		if v.Id == id {
			record = v
			break
		}
	}
	return &record, nil
}

func (r repo) GetReturnCutTypeOptions() ([]model.SelectOptions, error) {

	var options []model.SelectOptions

	if promotionReturnCuttypeOptions != nil {
		return promotionReturnCuttypeOptions.Options, nil
	}

	selectedFields := "id AS id, id AS value, name AS label"
	if err := r.db.Table("promotion_return_cut_type").
		Select(selectedFields).
		Scan(&options).
		Error; err != nil {
		return nil, err
	}

	promotionReturnCuttypeOptions = &model.SelectOptionsCache{
		Options:        options,
		CacheExpiredAt: time.Now().Add(60 * time.Minute),
	}
	return options, nil
}

func (r repo) GetReturnSetting() (*model.PromotionReturnSettingResponse, error) {

	var record model.PromotionReturnSettingResponse

	if promotionReturnSetting != nil && time.Now().Before(promotionReturnSetting.CacheExpiredAt) {
		// fmt.Println("Get Cache:", helper.StructJson(promotionReturnSetting))
		return promotionReturnSetting, nil
	}

	selectedFields := "id, return_percent, return_type_id, cut_type_id, min_loss_price, max_return_price"
	selectedFields += ", credit_expire_days, calc_on_sport, calc_on_casino, calc_on_game, calc_on_lottery, calc_on_p2p, calc_on_financial"
	selectedFields += ", detail, is_enabled, created_at, updated_at"
	if err := r.db.Table("promotion_return_setting as settings").
		Select(selectedFields).
		Take(&record).
		Error; err != nil {
		return nil, err
	}

	// Set Cache
	promotionReturnSetting = &record
	promotionReturnSetting.CacheExpiredAt = time.Now().Add(60 * time.Minute)
	// fmt.Println("Set Cache:", helper.StructJson(promotionReturnSetting))

	return &record, nil
}

func (r repo) CreateReturnSetting(body model.PromotionReturnSettingCreateBody) (*int64, error) {

	if err := r.db.Table("promotion_return_setting").Create(&body).Error; err != nil {
		return nil, err
	}

	// Clear Cache = GetNewData Later
	promotionReturnSetting = nil

	return &body.Id, nil
}

func (r repo) UpdateReturnSetting(id int64, body model.PromotionReturnSettingUpdateBody) error {

	tx := r.db.Begin()

	// เปิดได้ อย่างเดียว จะคืนยอดเสีย หรือ จะคืนยอดเทิร์น
	// [20241028] ยกเลิกการเปิดได้อย่างเดียว จะเป็นการเปิดทั้ง ยอดเสีย และ ยอดเทิร์น พร้อมกันได้
	// if body.IsEnabled != nil && *body.IsEnabled {
	// 	// เปิด ยอดเสีย จะไปปิด ยอดเทิร์น
	// 	updateTurnBody := map[string]interface{}{"is_enabled": false}
	// 	if err := tx.Table("promotion_return_turn_setting").Where("is_enabled = ?", true).Updates(updateTurnBody).Error; err != nil {
	// 		tx.Rollback()
	// 		return err
	// 	}
	// }

	if err := tx.Table("promotion_return_setting").Where("id = ?", id).Updates(body).Error; err != nil {
		tx.Rollback()
		return err
	}

	tx.Commit()

	// Clear Cache = GetNewData Later
	promotionReturnSetting = nil

	return nil
}

func (r repo) GetReturnHistoryAllUserList(req model.PromotionReturnHistoryUserListRequest) ([]model.PromotionReturnHistoryUserResponse, int64, error) {

	var list []model.PromotionReturnHistoryUserResponse
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("user as users")
	count = count.Select("users.id")
	if req.Search != "" {
		search_like := fmt.Sprintf("%%%s%%", req.Search)
		count = count.Where(r.db.Where("users.member_code LIKE ?", search_like).Or("users.username LIKE ?", search_like).Or("users.fullname LIKE ?", search_like))
	}
	if err = count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "users.id as id, users.member_code, users.username, users.fullname"
		query := r.db.Table("user as users")
		query = query.Select(selectedFields)
		if req.Search != "" {
			search_like := fmt.Sprintf("%%%s%%", req.Search)
			query = query.Where(r.db.Where("users.member_code LIKE ?", search_like).Or("users.username LIKE ?", search_like).Or("users.fullname LIKE ?", search_like))
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetTotalUserReturnList(req model.PromotionReturnHistoryUserListRequest) (map[int64]model.PromotionReturnHistoryUserTotalResponse, error) {

	var list []model.PromotionReturnHistoryUserTotalResponse
	var err error

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return nil, err
	}

	// SELECT //
	selectedFields := "tb_log.user_id as user_id, SUM(tb_log.total_loss_amount) as total_loss_amount"
	selectedFields += ", SUM(tb_log.taken_price) as total_taken_price"
	query := r.db.Table("promotion_return_loser as tb_log")
	query = query.Select(selectedFields)
	if req.UserIds != nil && len(req.UserIds) > 0 {
		query = query.Where("tb_log.user_id IN ?", req.UserIds)
	}
	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return nil, err
		}
		query = query.Where("tb_log.created_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return nil, err
		}
		query = query.Where("tb_log.created_at <=  ?", endDateAtBkk)
	}
	// NO_STATUS_NEED query = query.Where("tb_log.status_id != ?", model.PROMOTION_RETURN_STATUS_PENDING)
	if err = query.
		Group("tb_log.user_id").
		Scan(&list).
		Error; err != nil {
		return nil, err
	}

	result := make(map[int64]model.PromotionReturnHistoryUserTotalResponse)
	for _, v := range list {
		result[v.UserId] = v
	}
	return result, nil
}

func (r repo) GetReturnHistoryUserList(req model.PromotionReturnHistoryUserListRequest) ([]model.PromotionReturnHistoryUserResponse, int64, error) {

	var list []model.PromotionReturnHistoryUserResponse
	var total int64
	var err error

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return nil, total, err
	}

	// SELECT //
	selectedFields := "tb_log.user_id as id, SUM(tb_log.total_loss_amount) as total_loss_amount"
	selectedFields += ", SUM(tb_log.taken_price) as total_taken_price"
	query := r.db.Table("promotion_return_loser as tb_log")
	query = query.Select(selectedFields)
	query = query.Joins("INNER JOIN user as tb_user ON tb_user.id = tb_log.user_id")
	if req.UserIds != nil && len(req.UserIds) > 0 {
		query = query.Where("tb_log.user_id IN ?", req.UserIds)
	}
	if req.Search != "" {
		// seach member code from table user
		search_like := fmt.Sprintf("%%%s%%", req.Search)
		query = query.Where(r.db.Where("tb_user.member_code LIKE ?", search_like).Or("tb_user.username LIKE ?", search_like).Or("tb_user.fullname LIKE ?", search_like))
	}
	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return nil, total, err
		}
		query = query.Where("tb_log.created_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return nil, total, err
		}
		query = query.Where("tb_log.created_at <=  ?", endDateAtBkk)
	}
	// NO_STATUS_NEED query = query.Where("tb_log.status_id != ?", model.PROMOTION_RETURN_STATUS_PENDING)
	if err = query.
		Group("tb_log.user_id").
		Scan(&list).
		Error; err != nil {
		return nil, total, err
	}

	// Append user member_code/name
	userIds := make(map[int64]int64)
	for _, v := range list {
		userIds[v.Id] = v.Id
	}
	if len(userIds) > 0 {
		var userList []model.User
		var userMap = make(map[int64]model.User)
		selectedFields2 := "id, member_code, username, fullname"
		query2 := r.db.Table("user")
		query2 = query2.Select(selectedFields2)
		query2 = query2.Where("id IN (?)", helper.MapIdsToInt64Array(userIds))
		if err = query2.
			Scan(&userList).
			Error; err != nil {
			return nil, total, err
		}
		for _, v := range userList {
			userMap[v.Id] = v
		}
		for i, v := range list {
			if user, ok := userMap[v.Id]; ok {
				list[i].MemberCode = *user.MemberCode
				list[i].Username = *user.Username
				list[i].Fullname = user.Fullname
				// Display as Negative Value
				if v.TotalLossAmount > 0 {
					list[i].TotalLossAmount = v.TotalLossAmount * -1
				}
			}
		}
	}

	return list, total, nil
}

func (r repo) GetReturnHistoryUserSummary(req model.PromotionReturnHistoryUserListRequest) (*model.PromotionReturnHistoryUserSummaryResponse, error) {

	var result model.PromotionReturnHistoryUserSummaryResponse
	var err error

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return &result, err
	}

	// SELECT //
	selectedFields := "SUM(tb_log.total_loss_amount) as total_loss_amount, SUM(tb_log.taken_price) as total_taken_price"
	query := r.db.Table("promotion_return_loser as tb_log")
	query = query.Select(selectedFields)
	query = query.Joins("INNER JOIN user as tb_user ON tb_user.id = tb_log.user_id")
	// NO_STATUS_NEED query = query.Where("tb_log.status_id != ?", model.PROMOTION_RETURN_STATUS_PENDING)
	if req.Search != "" {
		// seach member code from table user
		search_like := fmt.Sprintf("%%%s%%", req.Search)
		query = query.Where(r.db.Where("tb_user.member_code LIKE ?", search_like).Or("tb_user.username LIKE ?", search_like).Or("tb_user.fullname LIKE ?", search_like))
	}
	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return &result, err
		}
		query = query.Where("tb_log.created_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return &result, err
		}
		query = query.Where("tb_log.created_at <=  ?", endDateAtBkk)
	}
	if err = query.
		Scan(&result).
		Error; err != nil {
		return nil, err
	}

	result.DateType = dateType.DateType
	result.FromDate = dateType.DateFrom
	result.ToDate = dateType.DateTo
	// Display as Negative Value
	if result.TotalLossAmount > 0 {
		result.TotalLossAmount = result.TotalLossAmount * -1
	}
	return &result, nil
}

func (r repo) GetReturnHistoryLogList(req model.PromotionReturnHistoryListRequest) ([]model.PromotionReturnHistoryReponse, int64, error) {

	var list []model.PromotionReturnHistoryReponse
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("promotion_return_loser as logs")
	count = count.Select("logs.id")
	count = count.Where("logs.status_id != ?", model.PROMOTION_RETURN_STATUS_PENDING)
	if req.UserId != nil {
		count = count.Where("logs.user_id = ?", req.UserId)
	}
	if req.FromDate != "" {
		fromTimeUtc, err := r.ParseBodUTC(req.FromDate)
		if err != nil {
			return nil, total, err
		}
		count = count.Where("logs.of_date >= ?", fromTimeUtc.Format("2006-01-02"))
	}
	if req.ToDate != "" {
		toTimeUtc, err := r.ParseEodUTC(req.ToDate)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("logs.of_date <= ?", toTimeUtc.Format("2006-01-02"))
	}
	if req.StatusId != nil {
		count = count.Where("logs.status_id = ?", req.StatusId)
	}
	if err = count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "logs.id as id, logs.user_id, logs.total_loss_amount, logs.total_loss_sport, logs.total_loss_casino, logs.total_loss_game, logs.total_loss_lottery, logs.total_loss_p2p, logs.total_loss_financial"
		selectedFields += ", logs.of_date, logs.return_price, logs.return_percent, logs.game_detail"
		// if logs.credit_expire_days = 0 return null else return DATE_ADD(logs.of_date, INTERVAL logs.credit_expire_days DAY)
		// selectedFields += ", DATE_ADD(logs.of_date, INTERVAL logs.credit_expire_days DAY) as credit_expire_at"
		selectedFields += ", CASE WHEN logs.credit_expire_days = 0 THEN NULL ELSE DATE_ADD(logs.of_date, INTERVAL logs.credit_expire_days DAY) END as credit_expire_at"
		selectedFields += ", logs.cut_type_id, types.name as cut_type_name"
		selectedFields += ", logs.status_id, status.name as log_status"
		query := r.db.Table("promotion_return_loser as logs")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN promotion_return_cut_type as types ON types.id = logs.cut_type_id")
		query = query.Joins("LEFT JOIN promotion_return_loser_status as status ON status.id = logs.status_id")
		query = query.Where("logs.status_id != ?", model.PROMOTION_RETURN_STATUS_PENDING)
		if req.UserId != nil {
			query = query.Where("logs.user_id = ?", req.UserId)
		}
		if req.FromDate != "" {
			fromTimeUtc, err := r.ParseBodUTC(req.FromDate)
			if err != nil {
				return nil, total, err
			}
			query = query.Where("logs.of_date >= ?", fromTimeUtc.Format("2006-01-02"))
		}
		if req.ToDate != "" {
			toTimeUtc, err := r.ParseEodUTC(req.ToDate)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("logs.of_date <= ?", toTimeUtc.Format("2006-01-02"))
		}
		if req.StatusId != nil {
			query = query.Where("logs.status_id = ?", req.StatusId)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			if req.SortCol == "of_date" {
				query = query.Order("STR_TO_DATE(of_date, '%Y-%m-%d') " + req.SortAsc)
			} else {
				query = query.Order(req.SortCol + " " + req.SortAsc)
			}
		}

		if err = query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetCurrentReturnTransactionList(userId int64) ([]model.PromotionReturnTransaction, error) {

	var list []model.PromotionReturnTransaction

	selectedFields := "logs.id, logs.user_id, logs.daily_key, logs.of_date, logs.total_loss_amount, logs.return_percent, logs.return_type_id, logs.cut_type_id, logs.min_loss_price, logs.max_return_price"
	selectedFields += ", logs.credit_expire_days, logs.return_price, logs.calc_at, logs.take_at, logs.taken_price, logs.created_at, logs.updated_at"
	selectedFields += ", logs.total_loss_sport as total_loss_sport, logs.total_loss_casino as total_loss_casino, logs.total_loss_game as total_loss_game"
	selectedFields += ", logs.total_loss_lottery as total_loss_lottery, logs.total_loss_p2p as total_loss_p2p, logs.total_loss_financial as total_loss_financial"
	selectedFields += ", status.id as status_id, status.name as status_name"
	if err := r.db.Table("promotion_return_loser as logs").
		Select(selectedFields).
		Joins("LEFT JOIN promotion_return_loser_status as status ON status.id = logs.status_id").
		Where("logs.user_id = ?", userId).
		Where("logs.status_id = ?", model.PROMOTION_RETURN_STATUS_READY).
		Scan(&list).
		Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (r repo) GetReturnTransactionList(req model.PromotionReturnTransactionListRequest) ([]model.PromotionReturnTransaction, int64, error) {

	var list []model.PromotionReturnTransaction
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("promotion_return_loser as logs")
	count = count.Select("logs.id")
	count = count.Where("logs.user_id = ?", req.UserId)
	if req.StatusId != nil {
		count = count.Where("logs.status_id = ?", req.StatusId)
	}
	if err = count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "logs.id, logs.user_id, logs.daily_key, logs.of_date, logs.total_loss_amount, logs.return_percent, logs.return_type_id, logs.cut_type_id, logs.min_loss_price, logs.max_return_price"
		selectedFields += ", logs.credit_expire_days, logs.return_price, logs.calc_at, logs.take_at, logs.taken_price, logs.created_at, logs.updated_at"
		selectedFields += ", total_loss_sport, total_loss_casino, total_loss_game, total_loss_lottery, total_loss_p2p, total_loss_financial, game_detail"
		selectedFields += ", status.id as status_id, status.name as status_name"
		query := r.db.Table("promotion_return_loser as logs")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN promotion_return_loser_status as status ON status.id = logs.status_id")
		query = query.Where("logs.user_id = ?", req.UserId)
		if req.StatusId != nil {
			query = query.Where("logs.status_id = ?", req.StatusId)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}

		if err = query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetReturnUncalcTransactionList(req model.PromotionReturnTransactionUncalcListRequest) ([]model.PromotionReturnTransaction, int64, error) {

	var list []model.PromotionReturnTransaction
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("promotion_return_loser as logs")
	count = count.Select("logs.id")
	count = count.Where("logs.status_id = ?", model.PROMOTION_RETURN_STATUS_PENDING)
	count = count.Where("logs.calc_at IS NULL")
	if err = count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "id, user_id, total_loss_amount, return_percent, return_type_id, cut_type_id, min_loss_price, max_return_price, credit_expire_days, return_price, calc_at, take_at, taken_price, created_at, updated_at"
		selectedFields += ", total_loss_sport, total_loss_casino, total_loss_game, total_loss_lottery, total_loss_p2p, total_loss_financial, game_detail"
		query := r.db.Table("promotion_return_loser as logs")
		query = query.Select(selectedFields)
		query = query.Where("logs.status_id = ?", model.PROMOTION_RETURN_STATUS_PENDING)
		query = query.Where("logs.calc_at IS NULL")
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetReturnTransactionListByDailyKeyList(bulkBody map[string]model.PromotionReturnTransactionCreateBody) ([]model.PromotionReturnTransactionDailyKey, int64, error) {

	var list []model.PromotionReturnTransactionDailyKey
	var total int64
	var err error

	dailyKeys := make([]string, 0)
	if len(bulkBody) > 0 {
		for k := range bulkBody {
			dailyKeys = append(dailyKeys, k)
		}
	}

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("promotion_return_loser as logs")
	count = count.Select("logs.id")
	count = count.Where("logs.daily_key IN ?", dailyKeys)
	if err = count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "id, user_id, daily_key, total_loss_amount, created_at"
		query := r.db.Table("promotion_return_loser as logs")
		query = query.Select(selectedFields)
		query = query.Where("logs.daily_key IN ?", dailyKeys)
		if err = query.
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) CreateReturnTransaction(body model.PromotionReturnTransactionCreateBody) (*int64, error) {

	if err := r.db.Table("promotion_return_loser").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) CreateReturnTransactionBulk(bulkMap map[string]model.PromotionReturnTransactionCreateBody) error {

	bulkBody := make([]model.PromotionReturnTransactionCreateBody, 0)
	for _, v := range bulkMap {
		bulkBody = append(bulkBody, v)
	}

	if err := r.db.Table("promotion_return_loser").Create(&bulkBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdateCalcReturnTransaction(id int64, body model.PromotionReturnTransactionCalcBody) error {

	if err := r.db.Table("promotion_return_loser").Where("id = ?", id).Where("calc_at IS NULL").Where("status_id = ?", model.PROMOTION_RETURN_STATUS_PENDING).Updates(body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdateTakeReturnTransaction(id int64, body model.PromotionReturnTransactionUpdateBody) error {

	if err := r.db.Table("promotion_return_loser").Where("id = ?", id).Where("take_at IS NULL").Where("status_id = ?", model.PROMOTION_RETURN_STATUS_READY).Updates(body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdateExpriedReturnTransaction(id int64, body model.PromotionReturnTransactionUpdateBody) error {

	if err := r.db.Table("promotion_return_loser").Where("id = ?", id).Where("take_at IS NULL").Where("status_id = ?", model.PROMOTION_RETURN_STATUS_READY).Updates(body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) CheckSystemHasDailyCuttypeInWeek(statementDate string) (int64, error) {

	var total int64

	ofDate, err := time.Parse("2006-01-02", statementDate)
	if err != nil {
		return -2, err
	}

	// Get date from Monday to Sunday that has this day in week
	var days []string
	monday := ofDate.AddDate(0, 0, -int(ofDate.Weekday()))
	if ofDate.Weekday() == time.Sunday {
		// Sunday is ZERO, Check ofDate from SAT-SUN(today) SAME as today as SAT(BEFORE SUN)
		monday = ofDate.AddDate(0, 0, -6)
	}
	for i := 0; i < 7; i++ {
		days = append(days, monday.AddDate(0, 0, i).Format("2006-01-02"))
	}

	if err := r.db.Table("promotion_return_loser as tb_log").
		Select("COUNT(tb_log.id)").
		Where("tb_log.of_date IN ?", days).
		// ANY TYPE Where("tb_log.cut_type_id = ?", model.PROMOTION_RETURN_CUT_TYPE_DAILY).
		Count(&total).
		Error; err != nil {
		return -1, err
	}
	return total, nil
}

func (r repo) WithdrawCommissionFromPromotionReturnLoss(userId int64, amount float64) error {

	tx := r.db.Begin()

	// var commission float64
	// if err := tx.Table("user_affiliate").
	// 	Select("commission_current").
	// 	Where("user_id = ?", userId).
	// 	Take(&commission).
	// 	Error; err != nil {
	// 	tx.Rollback()
	// 	return err
	// }

	commissionTransfer := map[string]interface{}{}
	commissionTransfer["detail"] = "Return play loss success"
	commissionTransfer["status"] = true
	commissionTransfer["commission"] = amount
	commissionTransfer["user_id"] = userId
	if err := tx.Table("commission_transfer").
		Create(&commissionTransfer).
		Error; err != nil {
		tx.Rollback()
		return err
	}

	// use affiliate ?
	// if err := tx.Table("user_affiliate").
	// 	Where("user_id = ?", userId).
	// 	Update("commission_current", 0.00).
	// 	Error; err != nil {
	// 	tx.Rollback()
	// 	return err
	// }

	var credit float64

	if err := tx.Table("user").
		Select("credit").
		Where("id = ?", userId).
		Take(&credit).
		Error; err != nil {
		tx.Rollback()
		return err
	}

	user := map[string]interface{}{}
	user["credit"] = gorm.Expr("credit + ?", amount)

	if err := tx.Table("user").
		Where("id = ?", userId).
		Updates(&user).
		Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}
	return nil
}

func (r repo) CheckDailyPlayLog(statementDate string) (*model.CronPlayLogCheckReponse, error) {

	var record model.CronPlayLogCheckReponse

	// statementDate = "2023-10-25"
	// type CronPlayLogCheckReponse struct {
	// 	IsReady           bool  `json:"isReady"`
	// 	TotalSuccessCount int64 `json:"totalSuccessCount"`
	// 	TotalFailCount    int64 `json:"totalFailCount"`
	// }
	// SELECT SUM(IF(is_success = 1, 1, 0)) AS success_count, SUM(IF(is_success = 0, 1, 0)) AS error_count FROM api_status WHERE statement_date = '2023-10-21';
	// simplewinlose1
	// simplewinlose2
	// simplewinlose4
	// simplewinlose3
	// simplewinlose6
	// simplewinlose7
	// simplewinlose4

	successKeys := []string{"simplewinlose1", "simplewinlose2", "simplewinlose4", "simplewinlose3", "simplewinlose6", "simplewinlose7"}

	selectedFields := "SUM(IF(is_success = 1, 1, 0)) AS total_success_count, SUM(IF(is_success = 0, 1, 0)) AS total_fail_count"
	if err := r.db.Table("api_status as play_logs").
		Select(selectedFields).
		Where("play_logs.statement_date = ?", statementDate).
		Where("play_logs.path IN ?", successKeys).
		Take(&record).
		Error; err != nil {
		return nil, err
	}

	if record.TotalSuccessCount == int64(len(successKeys)) && record.TotalFailCount == 0 {
		record.IsReady = true
	}
	return &record, nil
}
