package repository

import (
	"cybergame-api/model"

	"gorm.io/gorm"
)

func NewNewsRepository(db *gorm.DB) NewsRepository {
	return &repo{db}
}

type NewsRepository interface {
	GetNewsById(newsId int64) (*model.NewsDetail, error)
	GetNewsList(query model.NewsQuery) ([]model.NewsDetail, int, error)
	GetNewsHighLightList() ([]model.NewsDetail, error)
	GetCoverUrlByNewsId(newsId int64) (string, error)
	CountNewsHightlight(newsId *int64) (int64, error)
	CreateNews(news model.NewsBody) error
	UpdateNews(newsId int64, body model.NewsUpdateBody) error
	UpdateNewsSortOrder(list model.NewsSortBody) error
	DeleteNews(newsId int64) error
}

func (r repo) GetNewsById(newsId int64) (*model.NewsDetail, error) {

	var result model.NewsDetail

	if err := r.db.Table("news n").
		Joins("JOIN admin a ON n.created_by = a.id").
		Select("n.id, n.title, n.cover_url, n.images, n.detail, n.tags, n.sort_order, a.display_name AS created_by, n.is_highlight, n.created_at").
		Where("n.id = ?", newsId).
		Take(&result).
		Error; err != nil {
		return nil, err
	}

	return &result, nil
}

func (r repo) GetNewsList(query model.NewsQuery) ([]model.NewsDetail, int, error) {

	var total int64
	var result []model.NewsDetail

	queryTotal := r.db.Table("news")

	if query.Filter != "" {
		queryTotal = queryTotal.Where("title LIKE ?", "%"+query.Filter+"%")
	}

	if query.StartAt != "" && query.EndAt != "" {
		queryTotal = queryTotal.Where("created_at BETWEEN ? AND ?", query.StartAt, query.EndAt)
	}

	if err := queryTotal.
		Select("id").
		Where("is_highlight = ?", false).
		Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if total == 0 {
		return nil, 0, nil
	}

	queryData := r.db.Table("news n")

	if query.Filter != "" {
		queryData = queryData.Where("n.title LIKE ?", "%"+query.Filter+"%")
	}

	if query.StartAt != "" && query.EndAt != "" {
		queryData = queryData.Where("n.created_at BETWEEN ? AND ?", query.StartAt, query.EndAt)
	}

	if err := queryData.
		Joins("JOIN admin a ON n.created_by = a.id").
		Select("n.id, n.title, n.cover_url, n.images, n.detail, n.tags, n.sort_order, a.display_name AS created_by, n.is_highlight, n.created_at").
		Where("n.is_highlight = ?", false).
		Offset(query.Page * query.Limit).
		Limit(query.Limit).
		Order("n.created_at DESC").
		Scan(&result).
		Error; err != nil {
		return nil, 0, err
	}

	return result, int(total), nil
}

func (r repo) GetNewsHighLightList() ([]model.NewsDetail, error) {

	var result []model.NewsDetail

	if err := r.db.Table("news n").
		Joins("JOIN admin a ON n.created_by = a.id").
		Select("n.id, n.title, n.cover_url, n.images, n.detail, n.tags, n.sort_order, a.display_name AS created_by, n.is_highlight, n.created_at").
		Where("n.is_highlight = ?", true).
		Order("n.sort_order ASC").
		Order("n.created_at ASC").
		Scan(&result).
		Error; err != nil {
		return nil, err
	}

	return result, nil
}

func (r repo) GetCoverUrlByNewsId(newsId int64) (string, error) {

	var result string

	if err := r.db.Table("news").
		Select("cover_url").
		Where("id = ?", newsId).
		Pluck("cover_url", &result).
		Error; err != nil {
		return "", err
	}

	return result, nil
}

func (r repo) CountNewsHightlight(newsId *int64) (int64, error) {

	var total int64
	query := r.db.Table("news").Select("id")

	if newsId != nil {
		query = query.Where("id != ?", newsId)
	}

	if err := query.
		Where("is_highlight = ?", true).
		Count(&total).Error; err != nil {
		return 0, err
	}

	return total, nil
}

func (r repo) CreateNews(news model.NewsBody) error {

	if err := r.db.Table("news").Create(&news).Error; err != nil {
		return err
	}

	return nil
}

func (r repo) UpdateNews(newsId int64, body model.NewsUpdateBody) error {

	tx := r.db.Begin()

	update := map[string]interface{}{
		"title":        body.Title,
		"cover_url":    body.CoverUrl,
		"images":       body.Images,
		"detail":       body.Detail,
		"tags":         body.Tags,
		"is_highlight": body.IsHighlight,
	}

	if body.SortOrder != 0 {
		update["sort_order"] = body.SortOrder
	}

	if err := tx.Table("news").Where("id = ?", newsId).Updates(update).Error; err != nil {
		tx.Rollback()
		return err
	}

	if !body.IsHighlight {

		update := map[string]interface{}{
			"sort_order":   0,
			"is_highlight": false,
		}

		if err := tx.Table("news").Where("id = ?", newsId).Updates(update).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	if err := tx.Commit().Error; err != nil {
		return err
	}

	return nil
}

func (r repo) UpdateNewsSortOrder(list model.NewsSortBody) error {

	tx := r.db.Begin()

	for _, item := range list.List {
		if err := tx.Table("news").Where("id = ?", item.Id).Update("sort_order", item.SortOrder).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	if err := tx.Commit().Error; err != nil {
		return err
	}

	return nil
}

func (r repo) DeleteNews(newsId int64) error {

	if err := r.db.Table("news").Where("id = ?", newsId).Delete(&model.News{}).Error; err != nil {
		return err
	}

	return nil
}
