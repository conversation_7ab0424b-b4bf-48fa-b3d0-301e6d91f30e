package repository

import (
	"bytes"
	"cybergame-api/helper"
	"cybergame-api/model"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm"
)

func NewSugarpayRepository(db *gorm.DB) SugarpayRepository {
	return &repo{db}
}

type SugarpayRepository interface {
	GetDb() *gorm.DB
	// Sugarpay-RD
	// SugarpayEncryptRepayDesposit(partnerKey string, amount int64) (*model.SugarpayEncryptPayload, error)

	// MERCHANT
	// GetNOTUSEPaygateMerchantById(id int64) (*model.PaygateMerchantNgResponse, error)
	// GetPaygateMerchantList(req model.PaygateMerchantListRequest) ([]model.PaygateMerchantNgResponse, int64, error)
	// CreatePaygateMerchant(body model.PaygateMerchantCreateBody) (*int64, error)
	// UpdatePaygateMerchant(id int64, body model.PaygateMerchantUpdateBody) error
	// DeletePaygateMerchant(id int64) error
	// // SETTING
	// GetPaygateSetting() (*model.PaygateSettingResponse, error)
	// CreatePaygateSetting(body model.PaygateSettingCreateBody) (*int64, error)
	// UpdatePaygateSetting(id int64, body model.PaygateSettingUpdateBody) error
	// DeletePaygateSetting(id int64) error
	// // AdminLog
	// GetPaygateAdminLogById(id int64) (*model.PaygateAdminLogResponse, error)
	// GetPaygateAdminLogList(req model.PaygateAdminLogListRequest) ([]model.PaygateAdminLogResponse, int64, error)
	// CreatePaygateAdminLog(body model.PaygateAdminLogCreateBody) (*int64, error)
	// UpdatePaygateAdminLog(id int64, body model.PaygateAdminLogUpdateBody) error
	// // SystemLog
	CreatePaygateSystemLog(body model.PaygateSystemLogCreateBody) (*int64, error)
	// UpdatePaygateSystemLog(id int64, body model.PaygateSystemLogUpdateBody) error

	// REF-PAYGATE
	GetRawSugarpayPendingDepositOrderById(id int64) (*model.SugarpayOrderResponse, error)
	GetPaygateAccountByProviderId(id int64) (*model.PaygateAccountResponse, error)
	// Sugarpay-REMOTE
	// SugarpayGetToken(setting model.PaygateAccountResponse) (*model.SugarpayTokenCreateRemoteResponse, error)
	SugarpayDeposit(setting model.PaygateAccountResponse, req model.SugarpayDepositCreateRemoteRequest) (*model.SugarpayDepositCreateRemoteResponse, error)
	SugarpayWithdraw(setting model.PaygateAccountResponse, req model.SugarpayWithdrawCreateRemoteRequest) (*model.SugarpayWithdrawCreateRemoteResponse, error)
	SugarpayCheckBalance(setting model.PaygateAccountResponse) (*model.SugarpayCheckBalanceResponse, error)
	// SugarpayGetOrder(setting model.PaygateAccountResponse, orderNo string) (*model.SugarpayGetOrderRemoteResponse, error)
	// SugarpayCreateCustomer(setting model.PaygateAccountResponse, req model.SugarpayCustomerCreateRemoteRequest) (*model.SugarpayCustomerCreateRemoteResponse, error)
	// SugarpayUpdateCustomer(setting model.PaygateAccountResponse, req model.SugarpayCustomerUpdateRemoteRequest) (*model.SugarpayCustomerUpdateRemoteResponse, error)
	// Sugarpay-Decrypt
	// SugarpayDecryptRepayDespositPayload(setting model.PaygateAccountResponse, payload model.SugarpayWebhookEncryptPayload) (*model.SugarpayWebhookDepositResponse, error)
	// Sugarpay-DB
	CreateSugarpayWebhook(body model.SugarpayWebhookCreateBody) (*int64, error)
	GetDbSugarpayOrderList(req model.SugarpayOrderListRequest) ([]model.SugarpayOrderResponse, int64, error)
	GetDbSugarpayOrderById(id int64) (*model.SugarpayOrderResponse, error)
	GetDbSugarpayOrderByRefId(refId int64) (*model.SugarpayOrderResponse, error)
	CreateDbSugarpayOrder(body model.SugarpayOrderCreateBody) (*int64, error)
	CheckSugarpayDepositOrderInLast5Minutes(userId int64) (*model.SugarpayOrderResponse, error)
	UpdateDbSugarpayOrderError(id int64, remark string) error
	UpdateDbSugarpayOrder(id int64, body model.SugarpayOrderUpdateBody) error
	ApproveDbSugarpayOrder(id int64, webhookStatus string) error
	UpdateSugarpayOrderBankSucess(id int64, transId int64, actionBy int64) error
	// Token
	// GetDbSugarpayAccessToken() (*model.SugarpayToken, error)
	// CreateDbSugarpayAccessToken(body model.SugarpayTokenCreateBody) (*int64, error)
	// Customer
	// GetSugarpayCustomerById(id int64) (*model.SugarpayCustomerResponse, error)
	// GetSugarpayCustomerByUserId(userId int64) (*model.SugarpayCustomerResponse, error)
	// CheckSugarpayCustomerByUserId(user model.UserBankDetailBody) (*model.SugarpayCustomerResponse, error)
	// GetSugarpayCustomerList(req model.SugarpayCustomerListRequest) ([]model.SugarpayCustomerResponse, int64, error)
	// CreateSugarpayCustomer(body model.SugarpayCustomerCreateBody) (*int64, error)
	// UpdateSugarpayCustomer(id int64, body model.SugarpayCustomerUpdateBody) error
	// DeleteSugarpayCustomer(id int64) error
	// REF-USER
	GetUserById(id int64) (*model.UserResponse, error)
	// REF-MemberCode
	GetUserForGenMember(id int64) (*model.UserDetail, error)
	GetAgentInfo() (*model.AgentInfo, error)
	IncrementTotal() error
	UpdateMemberCode(userId int64, data model.UserUpdateMemberAndRef) error
	AgcRegister(data model.AgcRegister) error
	AmbRegister(data model.AmbRegister) error
	GetMemberById(id int64) (*model.Member, error)
	IsFirstDeposit(userId int64) bool
	UpdateGenMemberCodeToAffilate(userId int64) error
	CreateAffiliateMember(refBy int64, userId int64) error
	// CronjobDeleteSugarpayWebhook() error
	// REF-RC
	GetRaceActionIdByActionKey(actionKey string) (*int64, error)
	CreateRaceCondition(body model.RaceActionCreateBody) (int64, error)
	// REF-BankTransaction-Deposit
	GetUserBankDetailById(id int64) (*model.UserBankDetailBody, error)
	GetDepositCurrentProcessingUserPromotion(userId int64) (*model.PromotionWebUserByUserIdResponse, error)
	InsertBankTransaction(data model.BankTransactionCreateBody) (*int64, error)
	IncreaseUserCredit(body model.UserTransactionCreateRequest) (*model.UserTransactionCreateResponse, error)
	UpdateDepositTransactionStatusFromAgent(transId int64, body model.UserTransactionCreateResponse) error
	UpdateAutoProcessTimer(timer string, id int64) error
	// ConfirmDeposit
	GetBankTransactionById(id int64) (*model.BankTransaction, error)
	CreateTransactionAction(data model.CreateBankTransactionActionBody) (*int64, error)
	ConfirmPendingDepositTransaction(id int64, body model.BankDepositTransactionConfirmBody) error
	RollbackTransactionAction(actionId int64) error
	// REF-BANK
	GetLastestBankAdminManualDepositTransaction(userId int64) (*model.BankTransaction, error)
	ShowUserTransaction(req model.UserTransactionShowUpdate) error
	UpdateTransactionStatusTransferingToSuccess(id int64, secondUsed string) error
	RollbackTransactionStatusTransferingToConfirmed(id int64) error
	CreateBankTransactionExternalDetail(body model.BankTransactionExternalDetailCreateRequest) (int64, error)
	UpdateUserTransactionStatus(body model.UpdateUserTransactionStatusRequest) error
	GetUserWithdrawCreditTransactionByRefId(transactionId int64, creditAmount float64) (*model.UserTransaction, error)
	// REF-SysLog
	CreateSystemLog(body model.SystemLogCreateBody) (*int64, error)
	// REF-WebConfiguration
	GetWebConfiguration() (*model.GetWebConfigurationBody, error)

	// [TIER]
	IncreaseUserTierDepositAmount(userId int64, amount float64) error
}

func (r repo) GetSugarpayTimestamp(now time.Time) int64 {

	return now.UnixNano() / int64(time.Millisecond)
}

func (r repo) SugarpayCreateSignature(payload map[string]interface{}) string {

	// **Signature Guide**
	// Signature uses a Secret key to encrypt the solution. Convert to JSON String and MD5 is key to encryption.

	// Add Secret Key to object or array parameters like this

	// Javascript
	// PHP

	// let param = {
	//   "from_acc":"xxxx",
	//   "from_bank":"xxx",
	//   "key":"Your_secret_key",
	//   "amount":"100.00"
	// }
	// Sort the key parameters alphabetically in ascending order and convert them into JSON string.
	// The example result is

	// javascript
	// PHP

	// let json_string = JSON.stringify(sortObjectByKey(param));

	// //sorting object by key
	// function sortObjectByKey(obj) {
	//   const sortedKeys = Object.keys(obj).sort();
	//   const sortedObj = {};
	//   sortedKeys.forEach(key => {
	// 	sortedObj[key] = obj[key];
	//   });
	//   return sortedObj;
	// }
	// Use JSON string from step 2 be encrypted to MD5 and you get signature

	// JavaScript
	// PHP

	// import MD5 from "crypto-js/md5";
	// let signature = MD5(json_string).toString();
	// When you get signature bring it to make Payload Data like this

	// JavaScript
	// PHP

	// param = {
	//   "from_acc":"xxxx",
	//   "from_bank":"xxx",
	//   "signature": signature,
	//   "amount":"100.00"
	// }
	// Use payload from step 4 to post data to API. When we received data. we use same step format to verify signature.
	// Sort the key parameters alphabetically in ascending order and convert them into JSON string.

	// Sort the key parameters alphabetically in ascending order and convert them into JSON string.
	// ksort($array_data);
	// $json_string = json_encode($array_data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES );

	// สร้าง slice ของ keys
	var keys []string
	for key := range payload {
		keys = append(keys, key)
	}

	// จัดเรียง keys
	sort.Strings(keys)

	// แสดงผลลัพธ์ตามลำดับของ keys
	sortedPayload := make(map[string]interface{})
	for _, key := range keys {
		sortedPayload[key] = payload[key]
	}

	jsonStr := helper.StructJson(sortedPayload)
	sign := helper.GetMD5Hash(jsonStr)

	return sign
}

func (r repo) SugarpayDeposit(setting model.PaygateAccountResponse, req model.SugarpayDepositCreateRemoteRequest) (*model.SugarpayDepositCreateRemoteResponse, error) {

	// ผมอยากเก็บ default เอาไว้กัน Adminตั้งค่าต่ำกว่า payment
	if req.Amount < model.SUGARPAY_DEFMIN_DEPOSIT_AMOUNT || req.Amount > model.SUGARPAY_DEFMAX_DEPOSIT_AMOUNT {
		log.Println("req.Amount", req.Amount)
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}
	if setting.PaymentDepositMinimum > 0 && req.Amount < setting.PaymentDepositMinimum {
		log.Println("req.Amount", req.Amount, "setting.PaymentDepositMinimum", setting.PaymentDepositMinimum)
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}
	if setting.PaymentDepositMaximum > 0 && req.Amount > setting.PaymentDepositMaximum {
		log.Println("req.Amount", req.Amount, "setting.PaymentDepositMaximum", setting.PaymentDepositMaximum)
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}
	// PrerequisitesSugarpay
	if setting.ApiEndPoint == "" || setting.AccessKey == "" || setting.SecretKey == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}

	// actionAtUtc := time.Now().UTC()
	// timestamp := r.GetSugarpayTimestamp(actionAtUtc)
	apiKey := setting.AccessKey
	authorization := "Basic " + base64.StdEncoding.EncodeToString([]byte(apiKey+":"))

	// BuildSign from RequestBody + sorted Key
	signPayLoad := make(map[string]interface{})
	signPayLoad["amount"] = fmt.Sprintf("%.2f", req.Amount)
	signPayLoad["callback_url"] = req.CallbackUrl
	signPayLoad["order_id"] = req.OrderId
	signPayLoad["ref_account_no"] = req.RefAccountNo
	signPayLoad["ref_bank_code"] = req.RefBankCode
	signPayLoad["ref_name"] = req.RefName
	signPayLoad["key"] = setting.SecretKey
	signPayLoad["user_id"] = req.UserId

	// Sort the key parameters alphabetically in ascending order and convert them into JSON string.
	// The example result is
	// {
	// 	"amount": "100.00",
	// 	"callback_url": "https://example.com/callback",

	sign := r.SugarpayCreateSignature(signPayLoad)
	// fmt.Println("signPayLoad", signPayLoad)
	// fmt.Println("signPayLoad.Json", helper.StructJson(signPayLoad))
	// fmt.Println("signPayLoad.Json.Signature", sign)
	// fmt.Println("signPayLoad.Json.authorization", authorization)

	reqPayLoad := make(map[string]interface{})
	reqPayLoad["amount"] = fmt.Sprintf("%.2f", req.Amount)
	reqPayLoad["callback_url"] = req.CallbackUrl
	reqPayLoad["order_id"] = req.OrderId
	reqPayLoad["ref_account_no"] = req.RefAccountNo
	reqPayLoad["ref_bank_code"] = req.RefBankCode
	reqPayLoad["ref_name"] = req.RefName
	reqPayLoad["signature"] = sign
	reqPayLoad["user_id"] = req.UserId

	apiEndPoint := setting.ApiEndPoint
	// POST Endpoint: https:// {{API_ENDPOINT}}/v1/deposit/qrcode
	epUrl := fmt.Sprintf("%s/v1/deposit/qrcode", apiEndPoint)
	// fmt.Println("SugarpayDeposit url ------> ", epUrl)

	// SysLog
	if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
		Name:   "CreateSugarpayDeposit.SugarpayDeposit",
		Status: "CALL",
		JsonReq: helper.StructJson(map[string]interface{}{
			"epUrl":      epUrl,
			"setting":    setting,
			"reqPayLoad": reqPayLoad,
		}),
	}); err != nil {
		log.Println("CreatePaygateSystemLog.CreateSysLog", err)
	}

	client := &http.Client{}
	client.Timeout = 30 * time.Second

	jsonBody, _ := json.Marshal(reqPayLoad)
	payload := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest("POST", epUrl, payload)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("Authorization", authorization)
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}

	// fmt.Println("SugarpayDeposit.resp.Body", string(responseData))

	if response.StatusCode != 200 {
		log.Println(response.StatusCode)
		log.Println(string(responseData))
		var errMsg2 model.SugarpayErrorRemoteResponse
		errJson2 := json.Unmarshal(responseData, &errMsg2)
		if errJson2 == nil {
			return nil, errors.New(errMsg2.Message)
		} else {
			log.Println("Unmarshal.errJson2 ------> ", errJson2)
		}
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	}

	var result model.SugarpayDepositCreateRemoteResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("SugarpayDeposit resp.Body ------> ", string(responseData))
		log.Println("Unmarshal.Err ------> ", errJson)
		// TRY-3-err2
		var errMsg3 model.SugarpayErrorRemoteResponse
		errJson3 := json.Unmarshal(responseData, &errMsg3)
		if errJson3 == nil {
			return nil, errors.New(errMsg3.Message)
		} else {
			log.Println("Unmarshal.errJson3 ------> ", errJson3)
		}
		return nil, errors.New("INVALID_UNMARSHAL_JSON")
	}

	// fmt.Println("SugarpayDepositCreateRemoteResponse.result", result)
	// SugarpayDeposit.remoteResp {"
	// {
	// 	"error": "0",
	// 	"code": "200",
	// 	"message": "Request deposit success",
	// 	"result": {
	// 	  "ref_id": "xxxx-xxxx-xxxx-xxxx",
	// 	  "order_id": "ORDER20230101001",
	// 	  "amount": "100.50",
	// 	  "image": "{api_url}/deposit/image/qrcode?token=xxxx-xxxx-xxxx-xxxx",
	// 	  "timeout": {
	// 		"days": "YYYY-MM-DD",
	// 		"time": "HH:mm:ss",
	// 		"date": "2022-02-01T10:00:20.168Z"
	// 	  }
	// 	}
	//   }

	if result.Error != "0" && result.Code != "200" {
		// SysLog
		if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "SugarpayDeposit",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(reqPayLoad),
			JsonResponse: helper.StructJson(map[string]interface{}{"resp": string(responseData), "result": helper.StructJson(result)}),
		}); err != nil {
			log.Println("SugarpayDeposit.CreateSysLog", err)
		}
		return nil, errors.New(result.Message)
	}
	return &result, nil
}

func (r repo) SugarpayCheckBalance(setting model.PaygateAccountResponse) (*model.SugarpayCheckBalanceResponse, error) {

	// PrerequisitesSugarpay
	if setting.ApiEndPoint == "" || setting.AccessKey == "" || setting.SecretKey == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}

	// actionAtUtc := time.Now().UTC()
	// timestamp := r.GetSugarpayTimestamp(actionAtUtc)
	apiKey := setting.AccessKey
	authorization := "Basic " + base64.StdEncoding.EncodeToString([]byte(apiKey+":"))

	// BuildSign from RequestBody + sorted Key
	signPayLoad := make(map[string]interface{})
	signPayLoad["key"] = setting.SecretKey

	sign := r.SugarpayCreateSignature(signPayLoad)

	reqPayLoad := make(map[string]interface{})
	reqPayLoad["signature"] = sign

	apiEndPoint := setting.ApiEndPoint
	// POST Endpoint: {{API_ENDPOINT.io}}https://xxxx.io/v1/balance
	epUrl := fmt.Sprintf("%s/v1/balance", apiEndPoint)

	// SysLog
	syslogId, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
		Name:   "SugarpayCheckBalance",
		Status: "CALL",
		JsonReq: helper.StructJson(map[string]interface{}{
			"epUrl":   epUrl,
			"setting": setting,
		}),
	})
	if err != nil {
		log.Println("SugarpayCheckBalance.CreatePaygateSystemLog.ERROR", err)
	}

	client := &http.Client{}
	client.Timeout = 30 * time.Second

	reqExternal, _ := http.NewRequest("POST", epUrl, nil)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("Authorization", authorization)
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}
	// fmt.Println("SugarpayCheckBalance.resp.Body", string(responseData))

	// Update SystemLog for Response.
	jsonResponse :=
		helper.StructJson(map[string]interface{}{
			"epUrl": epUrl,
			"resp":  string(responseData),
		})
	if err := r.UpdatePaygateSystemLog(*syslogId, model.PaygateSystemLogUpdateBody{
		JsonResponse: &jsonResponse,
	}); err != nil {
		log.Println("SugarpayCheckBalance.UpdatePaygateSystemLog.ERROR", err)
	}

	if response.StatusCode != 200 {
		log.Println(response.StatusCode)
		log.Println(string(responseData))
		var errMsg3 model.SugarpayErrorRemoteResponse
		errJson3 := json.Unmarshal(responseData, &errMsg3)
		if errJson3 == nil {
			return nil, errors.New(errMsg3.Message)
		} else {
			log.Println("Unmarshal.errJson3 ------> ", errJson3)
		}
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	}

	var result model.SugarpayCheckBalanceRemoteResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("SugarpayCheckBalance resp.Body ------> ", string(responseData))
		log.Println("Unmarshal.Err ------> ", errJson)
		var errMsg3 model.SugarpayErrorRemoteResponse
		errJson3 := json.Unmarshal(responseData, &errMsg3)
		if errJson3 == nil {
			return nil, errors.New(errMsg3.Message)
		} else {
			log.Println("Unmarshal.errJson3 ------> ", errJson3)
		}
		return nil, errors.New("INVALID_UNMARSHAL_JSON")
	}

	// fmt.Println("SugarpayCheckBalanceRemoteResponse.result", result)

	// convert string to float64
	var balanceResult model.SugarpayCheckBalanceResponse
	balanceResult.Balance, _ = strconv.ParseFloat(result.Result.Balance, 64)
	balanceResult.BalanceWithdraw, _ = strconv.ParseFloat(result.Result.BalanceWithdraw, 64)
	balanceResult.Date = result.Result.Date

	return &balanceResult, nil
}

func (r repo) SugarpayWithdraw(setting model.PaygateAccountResponse, req model.SugarpayWithdrawCreateRemoteRequest) (*model.SugarpayWithdrawCreateRemoteResponse, error) {

	// Will Check Balance before Withdraw
	balance, err := r.SugarpayCheckBalance(setting)
	if err != nil {
		return nil, errors.New("PAYGATE_CANT_CHECK_BALANCE")
	}
	if balance.BalanceWithdraw < req.Amount {
		log.Println("balance.Balance", balance.Balance, "balance.BalanceWithdraw", balance.BalanceWithdraw, "req.Amount", req.Amount)
		return nil, errors.New("PAYGATE_INSUFFICIENT_BALANCE")
	}

	// PrerequisitesSugarpay
	if setting.ApiEndPoint == "" || setting.AccessKey == "" || setting.SecretKey == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}
	// || setting.LoanAppId == "" no API required

	// actionAtUtc := time.Now().UTC()
	// timestamp := r.GetSugarpayTimestamp(actionAtUtc)
	apiKey := setting.AccessKey
	authorization := "Basic " + base64.StdEncoding.EncodeToString([]byte(apiKey+":"))

	// BuildSign from RequestBody + sorted Key
	signPayLoad := make(map[string]interface{})
	signPayLoad["amount"] = fmt.Sprintf("%.2f", req.Amount)
	signPayLoad["callback_url"] = req.CallbackUrl
	signPayLoad["order_id"] = req.OrderId
	signPayLoad["to_account_no"] = req.ToAccountNo
	signPayLoad["to_bank_code"] = req.ToBankCode
	signPayLoad["to_name"] = req.ToName
	signPayLoad["key"] = setting.SecretKey

	sign := r.SugarpayCreateSignature(signPayLoad)
	// fmt.Println("SugarpayWithdraw.signPayLoad", signPayLoad)
	// fmt.Println("SugarpayWithdraw.signPayLoad.Json", helper.StructJson(signPayLoad))
	// fmt.Println("SugarpayWithdraw.signPayLoad.Json.Signature", sign)
	// fmt.Println("SugarpayWithdraw.signPayLoad.Json.authorization", authorization)

	reqPayLoad := make(map[string]interface{})
	reqPayLoad["amount"] = fmt.Sprintf("%.2f", req.Amount)
	reqPayLoad["callback_url"] = req.CallbackUrl
	reqPayLoad["order_id"] = req.OrderId
	reqPayLoad["to_account_no"] = req.ToAccountNo
	reqPayLoad["to_bank_code"] = req.ToBankCode
	reqPayLoad["to_name"] = req.ToName
	reqPayLoad["signature"] = sign

	apiEndPoint := setting.ApiEndPoint
	// POST Endpoint: https:// {{API_ENDPOINT}}/v1/deposit/qrcode
	epUrl := fmt.Sprintf("%s/v1/deposit/qrcode", apiEndPoint)
	// fmt.Println("SugarpayWithdraw url ------> ", epUrl)

	// SysLog
	if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
		Name:   "SugarpayWithdraw.SugarpayWithdraw",
		Status: "CALL",
		JsonReq: helper.StructJson(map[string]interface{}{
			"epUrl":   epUrl,
			"setting": setting,
			"req":     req,
		}),
	}); err != nil {
		log.Println("CreatePaygateSystemLog.CreateSysLog", err)
	}

	client := &http.Client{}
	client.Timeout = 30 * time.Second

	jsonBody, _ := json.Marshal(req)
	payload := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest("POST", epUrl, payload)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("Authorization", authorization)
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}
	// fmt.Println("SugarpayWithdraw.resp.Body", string(responseData))

	if response.StatusCode != 200 {
		log.Println(response.StatusCode)
		log.Println(string(responseData))
		var errMsg3 model.SugarpayErrorRemoteResponse
		errJson3 := json.Unmarshal(responseData, &errMsg3)
		if errJson3 == nil {
			return nil, errors.New(errMsg3.Message)
		} else {
			log.Println("Unmarshal.errJson3 ------> ", errJson3)
		}
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	}

	// ERROR CODE LIST

	var result model.SugarpayWithdrawCreateRemoteResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("SugarpayWithdraw resp.Body ------> ", string(responseData))
		log.Println("Unmarshal.Err ------> ", errJson)
		var errMsg3 model.SugarpayErrorRemoteResponse
		errJson3 := json.Unmarshal(responseData, &errMsg3)
		if errJson3 == nil {
			return nil, errors.New(errMsg3.Message)
		} else {
			log.Println("Unmarshal.errJson3 ------> ", errJson3)
		}
		return nil, errors.New("INVALID_UNMARSHAL_JSON")
	}

	// fmt.Println("SugarpayWithdrawCreateRemoteResponse.result", result)
	//	{
	//		"error": "0",
	//		"code": "200",
	//		"message": "Request withdraw success",
	//		"result": {
	//		  "ref_id": "xxxx-xxxx-xxxx-xxxx",
	//		  "order_id": "xxxxxxxxxx",
	//		  "price": "100.00"
	//		}
	//	  }

	if result.Error != "0" && result.Code != "200" {
		// SysLog
		if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "SugarpayWithdraw",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(req),
			JsonResponse: helper.StructJson(map[string]interface{}{"resp": string(responseData), "result": helper.StructJson(result)}),
		}); err != nil {
			log.Println("SugarpayWithdraw.CreateSysLog", err)
		}
		return nil, errors.New(result.Message)
	}
	return &result, nil
}

func (r repo) CreateSugarpayWebhook(body model.SugarpayWebhookCreateBody) (*int64, error) {

	if err := r.db.Table("paygate_sugarpay_webhook").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) GetDbSugarpayOrderList(req model.SugarpayOrderListRequest) ([]model.SugarpayOrderResponse, int64, error) {

	var list []model.SugarpayOrderResponse
	var total int64

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("paygate_sugarpay_order as tb_order")
	count = count.Select("tb_order.id")
	if req.UserId != nil {
		count = count.Where("tb_order.user_id = ?", req.UserId)
	}
	if req.OrderTypeId != nil {
		count = count.Where("tb_order.order_type_id = ?", req.OrderTypeId)
	}
	if req.OrderNo != "" {
		count = count.Where("tb_order.order_no = ?", req.OrderNo)
	}
	if req.TransactionNo != "" {
		count = count.Where("tb_order.transaction_no = ?", req.TransactionNo)
	}
	if req.Amount != "" {
		count = count.Where("tb_order.amount = ?", req.Amount)
	}
	if req.Status != "" {
		count = count.Where("tb_order.transaction_status = ?", req.Status)
	}

	if err := count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount, tb_order.transfer_amount AS transfer_amount"
		selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
		selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.payment_page_url AS payment_page_url"
		selectedFields += ", tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
		selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

		query := r.db.Table("paygate_sugarpay_order as tb_order")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN paygate_order_type AS tb_type ON tb_order.order_type_id = tb_type.id")
		if req.UserId != nil {
			query = query.Where("tb_order.user_id = ?", req.UserId)
		}
		if req.OrderTypeId != nil {
			query = query.Where("tb_order.order_type_id = ?", req.OrderTypeId)
		}
		if req.OrderNo != "" {
			query = query.Where("tb_order.order_no = ?", req.OrderNo)
		}
		if req.TransactionNo != "" {
			query = query.Where("tb_order.transaction_no = ?", req.TransactionNo)
		}
		if req.Amount != "" {
			query = query.Where("tb_order.amount = ?", req.Amount)
		}
		if req.Status != "" {
			query = query.Where("tb_order.transaction_status = ?", req.Status)
		}

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetDbSugarpayOrderById(id int64) (*model.SugarpayOrderResponse, error) {

	var record model.SugarpayOrderResponse

	selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount, tb_order.transfer_amount AS transfer_amount"
	selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
	selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.payment_page_url AS payment_page_url"
	selectedFields += ", tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
	selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

	if err := r.db.Table("paygate_sugarpay_order as tb_order").
		Select(selectedFields).
		Joins("LEFT JOIN paygate_order_type AS tb_type ON tb_order.order_type_id = tb_type.id").
		Where("tb_order.id = ?", id).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetDbSugarpayOrderByRefId(refId int64) (*model.SugarpayOrderResponse, error) {

	var record model.SugarpayOrderResponse

	selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount, tb_order.transfer_amount AS transfer_amount"
	selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
	selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.payment_page_url AS payment_page_url"
	selectedFields += ", tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
	selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

	if err := r.db.Table("paygate_sugarpay_order as tb_order").
		Select(selectedFields).
		Joins("LEFT JOIN paygate_order_type AS tb_type ON tb_order.order_type_id = tb_type.id").
		Where("tb_order.ref_id = ?", refId).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) CreateDbSugarpayOrder(body model.SugarpayOrderCreateBody) (*int64, error) {

	if err := r.db.Table("paygate_sugarpay_order").Create(&body).Error; err != nil {
		return nil, err
	}

	// Use Random UUID as OrderNo //
	uuid := helper.GenerateRandomUuid(16)
	agentName := uuid

	orderNo := fmt.Sprintf("%v%v%v", agentName, time.Now().Format("0601"), body.Id)
	if body.OrderNo == "" {
		updateBody := make(map[string]interface{})
		updateBody["order_no"] = orderNo
		if err := r.db.Table("paygate_sugarpay_order").Where("id = ?", body.Id).Updates(updateBody).Error; err != nil {
			return nil, err
		}
	}
	return &body.Id, nil
}

func (r repo) CheckSugarpayDepositOrderInLast5Minutes(userId int64) (*model.SugarpayOrderResponse, error) {

	var record model.SugarpayOrderResponse

	selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount, tb_order.transfer_amount AS transfer_amount"
	selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
	selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.payment_page_url AS payment_page_url"
	selectedFields += ", tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
	selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

	query := r.db.Table("paygate_sugarpay_order as tb_order")
	query = query.Select(selectedFields)
	query = query.Joins("LEFT JOIN paygate_order_type AS tb_type ON tb_order.order_type_id = tb_type.id")
	query = query.Where("tb_order.user_id = ?", userId)
	// query = query.Where("tb_order.amount = ?", amount) หนึ่งคน หนึ่ง Order แทรกไม่ได้
	if err := query.
		Where("tb_order.transaction_status = ?", "WAIT_PAYMENT").
		Order("tb_order.id DESC").
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) UpdateDbSugarpayOrderError(id int64, remark string) error {

	updateBody := map[string]interface{}{
		"transaction_status": "ERROR",
		"remark":             remark,
	}
	sql := r.db.Table("paygate_sugarpay_order").Where("id = ?", id).Where("transaction_status = ?", "PENDING")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdateDbSugarpayOrder(id int64, body model.SugarpayOrderUpdateBody) error {

	if err := r.db.Table("paygate_sugarpay_order").Where("id = ?", id).Updates(body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) ApproveDbSugarpayOrder(id int64, webhookStatus string) error {

	updateBody := make(map[string]interface{})
	updateBody["transaction_status"] = webhookStatus
	updateBody["payment_at"] = time.Now()

	sql := r.db.Table("paygate_sugarpay_order").Where("id = ?", id).Where("transaction_status = ?", "WAIT_PAYMENT")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdateSugarpayOrderBankSucess(id int64, transId int64, actionBy int64) error {

	updateBody := map[string]interface{}{
		"bank_transaction_id":     transId,
		"bank_transaction_status": "SUCCESS",
		"action_by":               actionBy,
	}

	sql := r.db.Table("paygate_sugarpay_order").Where("id = ?", id).Where("bank_transaction_status = ?", "PENDING")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}
