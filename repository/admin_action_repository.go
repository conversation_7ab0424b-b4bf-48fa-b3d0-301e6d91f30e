package repository

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"fmt"
	"strings"

	"gorm.io/gorm"
)

func NewAdminActionRepository(db *gorm.DB) AdminActionRepository {
	return &repo{db}
}

type AdminActionRepository interface {
	// Options
	GetAdminActionTypeOptions() ([]model.SelectOptions, error)
	// CRUD
	GetAdminActionLogList(req model.AdminActionLogListRequest) ([]model.AdminActionResponse, int64, error)
	CreateAdminAction(body model.AdminActionCreateBody) (*int64, error)
}

func (r *repo) GetAdminActionTypeOptions() ([]model.SelectOptions, error) {

	options := []model.SelectOptions{}

	// SELECT //
	selectedFields := "id as id, detail as label"
	var sql = r.db.Table("admin_action_type").Select(selectedFields)
	if err := sql.
		Scan(&options).
		Error; err != nil {
		return nil, err
	}
	return options, nil
}

func (r repo) GetAdminActionLogList(req model.AdminActionLogListRequest) ([]model.AdminActionResponse, int64, error) {

	// [20250331] is_show หน้า admin log เอารายการแบบนี้กรองออกค่ะ INVALID_ADMIN_TOKEN_

	var list []model.AdminActionResponse
	var total int64

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("admin_action as logs")
	count = count.Select("logs.id")
	count = count.Joins("LEFT JOIN admin_action_type as types ON types.id = logs.type_id")
	count = count.Joins("LEFT JOIN admin as admins ON admins.id = logs.admin_id")
	count = count.Where("logs.is_show = ?", true)
	if req.AdminId != nil {
		count = count.Where("logs.admin_id = ?", req.AdminId)
	}
	if req.AdminName != "" {
		count = count.Where("admins.username LIKE ?", fmt.Sprintf("%%%s%%", req.AdminName))
	}
	if req.TypeId != nil {
		count = count.Where("logs.type_id = ?", req.TypeId)
	}
	if req.Search != "" {
		search_like := fmt.Sprintf("%%%s%%", req.Search)
		count = count.Where(r.db.Where("admins.username LIKE ?", search_like).Or("types.detail LIKE ?", search_like).Or("logs.detail LIKE ?", search_like))
	}
	if err := count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "logs.id, logs.admin_id, logs.type_id, logs.detail, logs.ref_object_id, logs.json_input, logs.json_output, logs.created_at"
		selectedFields += ", admins.username as admin_username, types.detail as type_name"
		query := r.db.Table("admin_action as logs")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN admin_action_type as types ON types.id = logs.type_id")
		query = query.Joins("LEFT JOIN admin as admins ON admins.id = logs.admin_id")
		query = query.Where("logs.is_show = ?", true)
		if req.AdminId != nil {
			query = query.Where("logs.admin_id = ?", req.AdminId)
		}
		if req.AdminName != "" {
			query = query.Where("admins.username LIKE ?", fmt.Sprintf("%%%s%%", req.AdminName))
		}
		if req.TypeId != nil {
			query = query.Where("logs.type_id = ?", req.TypeId)
		}
		if req.Search != "" {
			search_like := fmt.Sprintf("%%%s%%", req.Search)
			query = query.Where(r.db.Where("admins.username LIKE ?", search_like).Or("types.detail LIKE ?", search_like).Or("logs.detail LIKE ?", search_like))
		}

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			req.SortCol = helper.ToSnake(req.SortCol)
			// check . if not exist, add it with main table name
			if !strings.Contains(req.SortCol, ".") {
				req.SortCol = "logs." + req.SortCol
			}
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) CreateAdminAction(body model.AdminActionCreateBody) (*int64, error) {

	if err := r.db.Table("admin_action").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}
