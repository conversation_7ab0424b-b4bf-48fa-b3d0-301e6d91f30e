package repository

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"errors"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"gorm.io/gorm"
)

func NewUserCreditRepository(db *gorm.DB) UserCreditRepository {
	return &repo{db}
}

type UserCreditRepository interface {
	GetDb() *gorm.DB
	GetuserTransferBankAccountList() ([]model.WebBankAccountResponse, error)
	GetTypeOptions(ids []int64) ([]model.SelectOptions, error)
	GetSortOptions() ([]model.SelectOptions, error)
	GetCreditTransactionById(id int64) (*model.UserTransactionResponse, error)
	GetUserCreditTransactionSummary(model.UserTransactionListRequest) (*model.UserTransactionSummaryResponse, error)
	GetCreditTransactionList(req model.UserTransactionListRequest) ([]model.UserTransactionResponse, int64, error)
	RemoveCreditTransaction(id int64, adminId int64) error
	GetCreditTransactionRemovedList(req model.UserTransactionListRequest) ([]model.UserTransactionResponse, int64, error)
	IncreaseUserCredit(body model.UserTransactionCreateRequest) (*model.UserTransactionCreateResponse, error)
	DecreaseUserCredit(body model.UserTransactionCreateRequest) (*model.UserTransactionCreateResponse, error)
	ShowUserTransaction(req model.UserTransactionShowUpdate) error

	GetCreditTransactionForExcelList(req model.UserTransactionForExcelListRequest) ([]model.UserTransactionResponse, int64, error)
	// REF-PlaylogReport
	GetReportPlayLogStatusResponse(req model.ReportPlayLogStatusRequest) ([]model.ReportPlayLogStatusResponse, int64, error)
	GetReportPlayLogResponse(req model.ReportPlayLogResponseRequest) ([]model.ReportPlayLogResponse, int64, error)
	GetReportPlayLogSummary(req model.ReportPlayLogResponseRequest) (*model.ReportPlayLogSummaryResponse, error)
	// REF-ACTION
	GetRaceActionByActionKey(actionKey string) (*model.RaceAction, error)
	CreateRaceCondition(body model.RaceActionCreateBody) (int64, error)
	UpdateRaceCondition(id int64, body model.RaceActionUpdateBody) error
}

func (r repo) GetuserTransferBankAccountList() ([]model.WebBankAccountResponse, error) {

	var list []model.WebBankAccountResponse

	selectedFields := "accounts.id as id ,banks.name as bank_name, banks.icon_url as bank_icon_url, accounts.account_name, accounts.account_number"
	selectedFields += ", accounts.bank_id as bank_id, banks.code as bank_code"
	selectedFields += ", accounts.sms_mode as sms_mode"

	query := r.db.Table("bank_account as accounts")
	query = query.Select(selectedFields)
	query = query.Joins("LEFT JOIN bank AS banks ON banks.id = accounts.bank_id")
	// มาทั้งหมด query = query.Where("accounts.account_type_id in (?,?)", model.BANK_ACCOUNT_TYPE_DEPOSIT_ONLY, model.BANK_ACCOUNT_TYPE_BOTH)
	// มาทั้งหมด query = query.Where("accounts.connection_status_id = ?", model.CONNECTION_CONNECTED)
	query = query.Where("accounts.deleted_at IS NULL")
	if err := query.Scan(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (r repo) GetTypeOptions(ids []int64) ([]model.SelectOptions, error) {

	options := []model.SelectOptions{}

	// SELECT //
	selectedFields := "id as id, detail as label, name as value"
	var sql = r.db.Table("user_transaction_type").Select(selectedFields)
	if len(ids) > 0 {
		sql = sql.Where("id IN ?", ids)
	}
	if err := sql.
		Scan(&options).
		Error; err != nil {
		return nil, err
	}
	return options, nil
}

func (r repo) GetSortOptions() ([]model.SelectOptions, error) {

	options := []model.SelectOptions{}

	options = append(options, model.SelectOptions{
		Id:    0,
		Label: "เวลาบันทึก",
		Value: "created_at",
	})
	options = append(options, model.SelectOptions{
		Id:    0,
		Label: "เวลาโอน",
		Value: "transfer_at",
	})
	return options, nil
}

func (r repo) getMemberById(id int64) (*model.Member, error) {

	var record model.Member

	selectedFields := "users.id, users.member_code, users.username, users.phone, users.fullname, users.credit, users.bank_account, users.bank_id as bank_id"
	selectedFields += ", users.ref_by, users.user_type_id, user_type.name as user_type_name, tb_bank.name as bank_name"
	if err := r.db.Table("user as users").
		Select(selectedFields).
		Joins("LEFT JOIN user_type ON user_type.id = users.user_type_id").
		Joins("LEFT JOIN bank as tb_bank ON tb_bank.id = users.bank_id").
		Where("users.id = ?", id).
		Where("users.deleted_at IS NULL").
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) IncreaseUserCredit(body model.UserTransactionCreateRequest) (*model.UserTransactionCreateResponse, error) {

	var result model.UserTransactionCreateResponse

	var transferAt time.Time
	if body.TransferAt != nil {
		transferAt = *body.TransferAt
	} else {
		transferAt = time.Now()
	}

	totalAmount := body.Amount + body.BonusAmount
	if totalAmount <= 0 {
		return nil, errors.New("INVALID_AMOUNT")
	}
	// [********] แก้บัค ทศนิยมเกิน แต่จริงๆส่งทศนิยมได้หลายหลัก แต่เลขทั้งหมดห้ามเกิน 8-9 หลัก
	totalAmountSatang := int64(totalAmount * 100)
	totalAmount = float64(totalAmountSatang) / 100

	user, err := r.getMemberById(body.UserId)
	if err != nil {
		return nil, err
	}

	createBody := make(map[string]interface{})
	createBody["direction_id"] = model.CREDIT_DIRECTION_DEPOSIT
	createBody["is_show"] = true
	if body.IsShow != nil {
		createBody["is_show"] = body.IsShow
	}
	if body.RefId != nil {
		createBody["ref_id"] = body.RefId
	}
	if body.AccountId != nil {
		createBody["account_id"] = body.AccountId
	}
	if body.PaymentMerchatId != nil {
		createBody["payment_merchart_id"] = body.PaymentMerchatId
	}
	if body.TypeId == model.CREDIT_TYPE_AFFILIATE_INCOME || body.TypeId == model.CREDIT_TYPE_ALLIANCE_INCOME {
		agentProvider := os.Getenv("AGENT_PROVIDER")
		if agentProvider == "AMB" {
			// [increase] AgentCredit
			agentData := model.AmbDeposit{}
			agentData.Username = user.MemberCode
			agentData.Balance = totalAmount
			agentData.IsDp = true // ติดเทิน
			agentData.RefId = body.RefId
			gameRes, err := r.AmbDepositAgent(agentData)
			if err != nil {
				return nil, err
			}
			result.TransferAt = transferAt
			result.AgentSuccess = true
			result.AgentBeforeAmount = gameRes.Data.BeforeCredit
			result.AgentAfterAmount = gameRes.Data.AfterCredit
		} else {
			// [increase] AgentCredit
			agentData := model.TransactionAgentRequest{}
			agentData.UserId = user.Id
			agentData.PlayerName = user.MemberCode
			agentData.Amount = totalAmount
			agentData.RefId = body.RefId
			agentResp, err := r.AgcAddCreditForAf(agentData)
			if err != nil {
				log.Println("ERROR.IncreaseUserCredit.AddCreditForAf", err)
				return nil, err
			}
			result.TransferAt = transferAt
			result.AgentSuccess = agentResp.Success
			result.AgentBeforeAmount = agentResp.BeforeAmount
			result.AgentAfterAmount = agentResp.AfterAmount
		}

		if body.TypeId == model.CREDIT_TYPE_AFFILIATE_INCOME {
			createBody["detail"] = "โบนัสรายได้แนะนำเพื่อน"
		} else if body.TypeId == model.CREDIT_TYPE_ALLIANCE_INCOME {
			// ไม่มีการเอาเงินเข้าเครดิต !!!!
			createBody["detail"] = "โบนัสรายได้พันธมิตร"
		}
		createBody["user_id"] = user.Id
		createBody["type_id"] = body.TypeId
		createBody["credit_before"] = user.Credit
		createBody["credit_amount"] = body.Amount
		createBody["bonus_amount"] = body.BonusAmount
		createBody["credit_after"] = user.Credit + totalAmount
		createBody["transfer_at"] = transferAt
	} else if body.TypeId == model.CREDIT_TYPE_BONUS {
		// [increase] AgentCredit
		agentProvider := os.Getenv("AGENT_PROVIDER")
		if agentProvider == "AMB" {
			agentData := model.AmbDeposit{}
			agentData.Username = user.MemberCode
			agentData.Balance = totalAmount
			agentData.IsDp = true // ติดเทิน
			agentData.RefId = body.RefId
			gameRes, err := r.AmbDepositAgent(agentData)
			if err != nil {
				return nil, err
			}
			result.TransferAt = transferAt
			result.AgentSuccess = true
			result.AgentBeforeAmount = gameRes.Data.BeforeCredit
			result.AgentAfterAmount = gameRes.Data.AfterCredit
		} else {
			agentData := model.TransactionAgentRequest{}
			agentData.UserId = user.Id
			agentData.PlayerName = user.MemberCode
			agentData.Amount = totalAmount
			agentData.RefId = body.RefId
			gameRes, err := r.AgcDepositAgent(agentData)
			if err != nil {
				return nil, err
			}
			result.TransferAt = transferAt
			result.AgentSuccess = gameRes.Success
			result.AgentBeforeAmount = gameRes.BeforeAmount
			result.AgentAfterAmount = gameRes.AfterAmount
		}

		createBody["detail"] = "แจกโบนัสฟรี" + fmt.Sprintf(": %s", body.Detail)
		createBody["user_id"] = user.Id
		createBody["type_id"] = body.TypeId
		createBody["credit_before"] = result.AgentBeforeAmount
		createBody["bonus_amount"] = body.BonusAmount
		createBody["credit_after"] = result.AgentAfterAmount
		createBody["transfer_at"] = transferAt
	} else if body.TypeId == model.CREDIT_TYPE_PROMOTION_RETURN_LOSS || body.TypeId == model.CREDIT_TYPE_PROMOTION_RETURN_TURN {
		// Agent
		agentProvider := os.Getenv("AGENT_PROVIDER")
		if agentProvider == "AMB" {
			agentData := model.AmbDeposit{}
			agentData.Username = user.MemberCode
			agentData.Balance = totalAmount
			agentData.IsDp = true // ติดเทิน
			agentData.RefId = body.RefId
			gameRes, err := r.AmbDepositAgent(agentData)
			if err != nil {
				return nil, err
			}
			result.TransferAt = transferAt
			result.AgentSuccess = true
			result.AgentBeforeAmount = gameRes.Data.BeforeCredit
			result.AgentAfterAmount = gameRes.Data.AfterCredit
		} else {
			agentData := model.TransactionAgentRequest{}
			agentData.UserId = user.Id
			agentData.PlayerName = user.MemberCode
			agentData.Amount = totalAmount
			agentData.RefId = body.RefId
			gameRes, err := r.AgcDepositAgent(agentData)
			if err != nil {
				return nil, err
			}
			result.TransferAt = transferAt
			result.AgentSuccess = gameRes.Success
			result.AgentBeforeAmount = gameRes.BeforeAmount
			result.AgentAfterAmount = gameRes.AfterAmount
		}
		// คืนยอดเสีย และ คืนยอดเทิร์น
		createBody["detail"] = body.Detail
		createBody["user_id"] = user.Id
		createBody["type_id"] = body.TypeId
		createBody["credit_before"] = result.AgentBeforeAmount
		createBody["bonus_amount"] = body.BonusAmount
		createBody["credit_after"] = result.AgentAfterAmount
		createBody["transfer_at"] = transferAt
	} else {
		// Agent
		agentProvider := os.Getenv("AGENT_PROVIDER")
		if agentProvider == "AMB" {
			agentData := model.AmbDeposit{}
			agentData.Username = user.MemberCode
			agentData.Balance = totalAmount
			agentData.IsDp = false // ไม่ติดเทิน
			agentData.RefId = body.RefId
			gameRes, err := r.AmbDepositAgent(agentData)
			if err != nil {
				return nil, err
			}
			result.TransferAt = transferAt
			result.AgentSuccess = true
			result.AgentBeforeAmount = gameRes.Data.BeforeCredit
			result.AgentAfterAmount = gameRes.Data.AfterCredit
		} else {
			agentData := model.TransactionAgentRequest{}
			agentData.UserId = user.Id
			agentData.PlayerName = user.MemberCode
			agentData.Amount = totalAmount
			agentData.RefId = body.RefId
			gameRes, err := r.AgcDepositAgent(agentData)
			if err != nil {
				return nil, err
			}
			result.TransferAt = transferAt
			result.AgentSuccess = gameRes.Success
			result.AgentBeforeAmount = gameRes.BeforeAmount
			result.AgentAfterAmount = gameRes.AfterAmount
		}
		// MOCK
		// result.AgentSuccess = true
		// result.AgentBeforeAmount = 11
		// result.AgentAfterAmount = 22

		createBody["detail"] = body.Detail
		createBody["user_id"] = user.Id
		createBody["type_id"] = body.TypeId
		createBody["credit_before"] = result.AgentBeforeAmount
		createBody["credit_amount"] = body.Amount
		createBody["bonus_amount"] = body.BonusAmount
		createBody["credit_after"] = result.AgentAfterAmount
		createBody["transfer_at"] = transferAt
	}

	if body.CreateBy != nil {
		createBody["create_admin_id"] = body.CreateBy
	}
	if body.ConfirmBy != nil {
		createBody["confirm_admin_id"] = body.ConfirmBy
	}
	createBody["is_adjust_auto"] = body.IsAdjustAuto
	if body.PromotionId != nil {
		createBody["promotion_id"] = body.PromotionId
	}
	// User_Transaction
	// 1. process time
	// list how to count
	// ข้อ 2.
	// ดึงเครดิตกลับ: คือ เอาเครดิตออกจาก User นั้น โดยเรียก api withdraw ของ agent แต่ไม่มาเพิ่มเครดิตฝั่งเรา = ดึงเครดิตกลับ
	// โบนัส: คือการฝาก (ยิง api agent -> deposit)ทุกอย่างที่ไม่ใช่เติมเงิน
	// เครดิตพนันคงเหลือ: balance หลังจากทำรายการ
	// 	ข้อ 3:
	// ถ้าแยกฝัง api agent จะมีแค่ฝากกับถอน
	// ถ้าแยกฝั่งเรา
	// ฝาก
	// 3.1.1 ฝาก-เติมเงิน: คือการฝากแบบเติมเงิน
	// 3.1.2 ฝาก-โบนัส: คือการเพิ่มเครดิตทุกๆอย่าง ที่ไม่มีการเติมเงินจริง หรือไม่มีเงินจริงเข้าระบบ (คอม คืนยอดเสีย Aff ทุกอย่าง)
	// ถอน
	// 3.2.1 ถอน: ถอนปกติ โอนเงินให้ User
	// 3.2.2 ดึงยอดกลับ: ถอน api แต่ไม่ให้เงิน User หรือต้องการลบเครดิตออกจากระบบ
	// ข้อ 4: ผู้บันทึก เป็นชื่อคนอนุมัติ จะมีแค่ admin กับ auto เท่านั้น ไม่มี User
	// ข้อ 5: Soft Delete
	if err := r.db.Transaction(func(tx *gorm.DB) error {
		// if err := r.db.Table("user").Where("id = ?", user.Id).UpdateColumn("credit", gorm.Expr("credit + ?", body.Amount)).Error; err != nil {
		// 	return err
		// }
		// worktime = work_seconds
		getWorkingProcess := 1
		if !body.StartWorkAt.IsZero() {
			// ms := time.Since(body.StartWorkAt).Milliseconds()
			// debug.Println("ms", ms)

			//  Since เป็น +7 ตามenv ต้องปรับ เป็น UTC
			getWorkingProcess := int(time.Now().UTC().Sub(body.StartWorkAt.UTC()).Seconds())
			if getWorkingProcess < 1 {
				getWorkingProcess = 1
			}
		}
		createBody["work_seconds"] = getWorkingProcess
		if err := r.db.Table("user_transaction").Create(&createBody).Error; err != nil {
			return err
		}
		return nil // COMMIT
	}); err != nil {
		return nil, err
	}
	return &result, nil
}

func (r repo) DecreaseUserCredit(body model.UserTransactionCreateRequest) (*model.UserTransactionCreateResponse, error) {

	var result model.UserTransactionCreateResponse

	var transferAt time.Time
	if body.TransferAt != nil {
		transferAt = *body.TransferAt
	} else {
		transferAt = time.Now()
	}
	if body.Amount <= 0 && body.BonusAmount <= 0 {
		return nil, errors.New("INVALID_AMOUNT")
	}

	user, err := r.GetMemberById(body.UserId)
	if err != nil {
		return nil, err
	}

	createBody := make(map[string]interface{})
	createBody["direction_id"] = model.CREDIT_DIRECTION_WITHDRAW
	createBody["is_show"] = true
	if body.IsShow != nil {
		createBody["is_show"] = body.IsShow
	}
	if body.RefId != nil {
		createBody["ref_id"] = body.RefId
	}
	if body.AccountId != nil {
		createBody["account_id"] = body.AccountId
	}
	if body.PaymentMerchatId != nil {
		createBody["payment_merchart_id"] = body.PaymentMerchatId
	}
	if body.TypeId == model.CREDIT_TYPE_TAKE_CREDIT_BACK {
		// Agent
		agentProvider := os.Getenv("AGENT_PROVIDER")
		if agentProvider == "AMB" {
			agentData := model.AmbWithdraw{}
			agentData.Username = user.MemberCode
			agentData.Balance = body.Amount
			agentData.RefId = body.RefId
			gameRes, err := r.AmbWithdrawAgent(agentData)
			if err != nil {
				return nil, err
			}
			result.TransferAt = transferAt
			result.AgentSuccess = true
			result.AgentBeforeAmount = gameRes.Data.BeforeCredit
			result.AgentAfterAmount = gameRes.Data.AfterCredit
		} else {
			agentData := model.TransactionAgentRequest{}
			agentData.UserId = user.Id
			agentData.PlayerName = user.MemberCode
			agentData.Amount = body.Amount
			agentData.RefId = body.RefId
			gameRes, err := r.AgcWithdrawAgent(agentData)
			if err != nil {
				log.Println("ERROR.DecreaseUserCredit.WithdrawAgent", err)
				return nil, err
			}
			result.TransferAt = transferAt
			result.AgentSuccess = gameRes.Success
			result.AgentBeforeAmount = gameRes.BeforeAmount
			result.AgentAfterAmount = gameRes.AfterAmount
		}
		// MOCK
		// result.AgentSuccess = true
		// result.AgentBeforeAmount = 11
		// result.AgentAfterAmount = 22
		if body.Detail != "" {
			createBody["detail"] = fmt.Sprintf("ดึงเครดิตกลับ : %s", body.Detail)
		} else {
			createBody["detail"] = "ดึงเครดิตกลับ"
		}
		createBody["user_id"] = user.Id
		createBody["type_id"] = body.TypeId
		createBody["credit_before"] = result.AgentBeforeAmount
		createBody["credit_back"] = body.Amount
		createBody["credit_after"] = result.AgentAfterAmount
		createBody["transfer_at"] = transferAt
		createBody["create_admin_id"] = body.CreateBy
		createBody["confirm_admin_id"] = body.ConfirmBy

	} else if body.TypeId == model.CREDIT_TYPE_CANCEL_CREDIT {
		// Agent
		agentProvider := os.Getenv("AGENT_PROVIDER")
		if agentProvider == "AMB" {
			agentData := model.AmbWithdraw{}
			agentData.Username = user.MemberCode
			agentData.Balance = body.Amount
			agentData.RefId = body.RefId
			gameRes, err := r.AmbWithdrawAgent(agentData)
			if err != nil {
				return nil, err
			}
			result.TransferAt = transferAt
			result.AgentSuccess = true
			result.AgentBeforeAmount = gameRes.Data.BeforeCredit
			result.AgentAfterAmount = gameRes.Data.AfterCredit
		} else {
			agentData := model.TransactionAgentRequest{}
			agentData.UserId = user.Id
			agentData.PlayerName = user.MemberCode
			agentData.Amount = body.Amount
			agentData.RefId = body.RefId
			gameRes, err := r.AgcWithdrawAgent(agentData)
			if err != nil {
				log.Println("ERROR.DecreaseUserCredit.WithdrawAgent", err)
				return nil, err
			}
			result.TransferAt = transferAt
			result.AgentSuccess = gameRes.Success
			result.AgentBeforeAmount = gameRes.BeforeAmount
			result.AgentAfterAmount = gameRes.AfterAmount
		}
		if body.Detail != "" {
			createBody["detail"] = fmt.Sprintf("ยกเลิกเติมเครดิต : %s", body.Detail)
		} else {
			createBody["detail"] = "ยกเลิกเติมเครดิต"
		}
		createBody["user_id"] = user.Id
		createBody["type_id"] = body.TypeId
		createBody["credit_before"] = result.AgentBeforeAmount
		createBody["credit_back"] = body.Amount
		createBody["credit_after"] = result.AgentAfterAmount
		createBody["transfer_at"] = transferAt
		createBody["create_admin_id"] = body.CreateBy
		createBody["confirm_admin_id"] = body.ConfirmBy

	} else {
		// Agent
		transferAt := time.Now()
		agentProvider := os.Getenv("AGENT_PROVIDER")
		if agentProvider == "AMB" {
			agentData := model.AmbWithdraw{}
			agentData.Username = user.MemberCode
			agentData.Balance = body.Amount
			agentData.RefId = body.RefId
			gameRes, err := r.AmbWithdrawAgent(agentData)
			if err != nil {
				return nil, err
			}
			result.TransferAt = transferAt
			result.AgentSuccess = true
			result.AgentBeforeAmount = gameRes.Data.BeforeCredit
			result.AgentAfterAmount = gameRes.Data.AfterCredit
		} else {
			agentData := model.TransactionAgentRequest{}
			agentData.UserId = user.Id
			agentData.PlayerName = user.MemberCode
			agentData.Amount = body.Amount
			agentData.RefId = body.RefId
			gameRes, err := r.AgcWithdrawAgent(agentData)
			if err != nil {
				return nil, err
			}
			result.TransferAt = transferAt
			result.AgentSuccess = gameRes.Success
			result.AgentBeforeAmount = gameRes.BeforeAmount
			result.AgentAfterAmount = gameRes.AfterAmount
		}
		// MOCK
		// result.AgentSuccess = true
		// result.AgentBeforeAmount = 11
		// result.AgentAfterAmount = 22
		createBody["detail"] = body.Detail

		createBody["user_id"] = user.Id
		createBody["type_id"] = body.TypeId
		createBody["credit_before"] = result.AgentBeforeAmount
		createBody["credit_amount"] = body.Amount
		createBody["credit_after"] = result.AgentAfterAmount
		createBody["transfer_at"] = transferAt
		createBody["create_admin_id"] = body.CreateBy
		createBody["confirm_admin_id"] = body.ConfirmBy
	}

	if err := r.db.Transaction(func(tx *gorm.DB) error {
		// worktime = work_seconds
		getWorkingProcess := 1
		if !body.StartWorkAt.IsZero() {
			// ms := time.Since(body.StartWorkAt).Milliseconds()
			// debug.Println("ms", ms)

			//  Since เป็น +7 ตามenv ต้องปรับ เป็น UTC
			getWorkingProcess := int(time.Now().UTC().Sub(body.StartWorkAt.UTC()).Seconds())
			if getWorkingProcess < 1 {
				getWorkingProcess = 1
			}
		}
		createBody["work_seconds"] = getWorkingProcess
		if err := r.db.Table("user_transaction").Create(&createBody).Error; err != nil {
			return err
		}
		if err := tx.Table("user").Where("id = ?", user.Id).UpdateColumn("credit", gorm.Expr("credit - ?", body.Amount)).Error; err != nil {
			return err
		}
		return nil // COMMIT
	}); err != nil {
		return nil, err
	}

	return &result, nil
}

func (r repo) GetCreditTransactionById(id int64) (*model.UserTransactionResponse, error) {

	var record model.UserTransactionResponse

	selectedFields := "logs.id, logs.direction_id, directions.name as direction_name, logs.account_id, logs.detail, logs.promotion_id"
	selectedFields += ", logs.user_id, users.member_code as user_member_code, users.username, users.fullname as user_fullname"
	// selectedFields += ", logs.type_id, IF(logs.type_id IN (1,2), '', types.detail) as type_name" // show empty string if type_id = 1 or 2
	selectedFields += ", logs.type_id, types.detail as type_name, logs.ref_id AS ref_id"
	selectedFields += ", logs.credit_before, logs.credit_back, logs.credit_amount, logs.bonus_amount, logs.credit_after, logs.transfer_at"
	selectedFields += ", logs.create_admin_id, create_admin.username as create_admin_username, logs.confirm_admin_id, confirm_admin.username as confirm_admin_username"
	selectedFields += ", logs.is_adjust_auto, logs.work_seconds, logs.created_at"
	query := r.db.Table("user_transaction as logs")
	query = query.Select(selectedFields)
	query = query.Joins("LEFT JOIN user as users ON users.id = logs.user_id")
	query = query.Joins("LEFT JOIN user_transaction_type as types ON types.id = logs.type_id")
	query = query.Joins("LEFT JOIN user_transaction_direction as directions ON directions.id = logs.direction_id")
	query = query.Joins("LEFT JOIN admin as create_admin ON create_admin.id = logs.create_admin_id")
	query = query.Joins("LEFT JOIN admin as confirm_admin ON confirm_admin.id = logs.confirm_admin_id")
	if err := query.
		Where("logs.id = ?", id).
		Where("logs.removed_at IS NULL").
		Where("logs.is_show = ?", true).
		Take(&record).
		Error; err != nil {
		return nil, err
	}

	// Deposit and Withdraw Slip
	transIds := make(map[int64]int64)
	if record.RefId != nil {
		if record.TypeId == model.CREDIT_TYPE_DEPOSIT || record.TypeId == model.CREDIT_TYPE_WITHDRAW {
			transIds[*record.RefId] = *record.RefId
		}
		if len(transIds) > 0 {
			var bankTransList []model.BankTransaction
			if err := r.db.Table("bank_transaction").Select("id, slip_img_url").Where("id IN (?)", helper.MapIdsToInt64Array(transIds)).Limit(1).Scan(&bankTransList).Error; err != nil {
				return &record, nil
			}
			if record.TypeId == model.CREDIT_TYPE_DEPOSIT || record.TypeId == model.CREDIT_TYPE_WITHDRAW {
				for _, slip := range bankTransList {
					if *record.RefId == slip.Id {
						record.SlipImgUrl = slip.SlipImgUrl
					}
				}
			}
		}
	}

	return &record, nil
}

func (r repo) GetUserCreditTransactionSummaryOld(req model.UserTransactionListRequest) (*model.UserTransactionSummaryResponse, error) {

	var record model.UserTransactionSummaryResponse

	// [********] เพิ่มรายการโบนัส
	// [********] คืนยอดเทิร์น
	bonusTypeList := []int{
		model.CREDIT_TYPE_BONUS,
		model.CREDIT_TYPE_AFFILIATE_INCOME,
		model.CREDIT_TYPE_ALLIANCE_INCOME,
		model.CREDIT_TYPE_PROMOTION_RETURN_LOSS,
		model.CREDIT_TYPE_DAILY_ACTIVITY_BONUS,
		model.CREDIT_TPYE_LUCKY_WHEEL,
		model.CREDIT_TYPE_PROMOTION_WEB,
		model.CREDIT_TYPE_COUPON_CASH,
		model.CREDIT_TYPE_PROMOTION_RETURN_TURN,
	}

	// SUM
	selectedFields := "SUM(CASE WHEN logs.type_id = ? THEN logs.credit_amount ELSE 0 END) AS total_deposit_amount"
	// [********] ยกเลิกเติมเครดิต = หักลบออกจากยอดฝาก
	selectedFields += ", SUM(CASE WHEN logs.type_id = ? THEN logs.credit_back ELSE 0 END) AS total_cancel_credit_back"
	// [20231213] หน้ารายการฝาก-ถอน เสร็จสิ้น ช่วยปรับให้ยอดรวมรายการถอน ไม่รวมยอดดึงเครดิตกลับหน่อยค่ะ // , model.CREDIT_TYPE_TAKE_CREDIT_BACK
	// selectedFields += ", SUM(CASE WHEN logs.type_id = ? THEN logs.credit_amount ELSE 0 END) + SUM(CASE WHEN logs.type_id = ? THEN logs.credit_back ELSE 0 END) AS total_withdraw_amount"
	selectedFields += ", SUM(CASE WHEN logs.type_id = ? THEN logs.credit_amount ELSE 0 END) AS total_withdraw_amount"
	selectedFields += ", SUM(CASE WHEN logs.type_id IN ? OR (logs.type_id = ? AND logs.bonus_amount > 0) THEN logs.bonus_amount ELSE 0 END) AS total_bonus_amount"

	query := r.db.Table("user_transaction as logs")
	query = query.Select(selectedFields, model.CREDIT_TYPE_DEPOSIT, model.CREDIT_TYPE_CANCEL_CREDIT, model.CREDIT_TYPE_WITHDRAW, bonusTypeList, model.CREDIT_TYPE_DEPOSIT)
	query = query.Joins("LEFT JOIN user as users ON users.id = logs.user_id")
	if req.AccountId != nil {
		query = query.Where("logs.account_id = ?", req.AccountId)
	}
	if req.UserId != nil {
		query = query.Where("logs.user_id = ?", req.UserId)
	}
	if req.TypeId != nil {
		query = query.Where("logs.type_id = ?", req.TypeId)
	}
	if req.OfDate != "" {
		// OfDate is Primary
		startDateAtBkk, err := r.ParseBodBkk(req.OfDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("logs.transfer_at >= ? ", startDateAtBkk)
		endDateAtBkk, err := r.ParseEodBkk(req.OfDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("logs.transfer_at <=  ?", endDateAtBkk)
	} else {
		// Query Date today, yesterday, this_month
		actionTime := time.Now()
		bbkLoc := time.FixedZone("Asia/Bangkok", 7*60*60)
		filterTime := actionTime.In(bbkLoc)
		if req.DateType == "today" {
			req.FromDate = filterTime.Format("2006-01-02")
			req.ToDate = filterTime.Format("2006-01-02")
		} else if req.DateType == "yesterday" {
			req.FromDate = filterTime.AddDate(0, 0, -1).Format("2006-01-02")
			req.ToDate = filterTime.AddDate(0, 0, -1).Format("2006-01-02")
		} else if req.DateType == "this_month" {
			// full of this month
			req.FromDate = filterTime.Format("2006-01") + "-01"
			req.ToDate = filterTime.AddDate(0, 1, -1).Format("2006-01-02")
		}

		if req.FromDate != "" {
			startDateAtBkk, err := r.ParseBodBkk(req.FromDate)
			if err != nil {
				return nil, err
			}
			query = query.Where("logs.transfer_at >= ? ", startDateAtBkk)
		}
		if req.ToDate != "" {
			endDateAtBkk, err := r.ParseEodBkk(req.ToDate)
			if err != nil {
				return nil, err
			}
			query = query.Where("logs.transfer_at <=  ?", endDateAtBkk)
		}
	}
	if req.Search != "" {
		search_like := fmt.Sprintf("%%%s%%", req.Search)
		query = query.Where(r.db.Where("logs.detail LIKE ?", search_like).Or("users.member_code LIKE ?", search_like).Or("users.username LIKE ?", search_like).Or("users.fullname LIKE ?", search_like))
	}

	if err := query.
		Where("logs.removed_at IS NULL").
		Where("logs.is_show = ?", true).
		Take(&record).
		Error; err != nil {
		if err != gorm.ErrRecordNotFound {
			return nil, err
		}
	}
	return &record, nil
}

func (r repo) GetUserCreditTransactionSummary(req model.UserTransactionListRequest) (*model.UserTransactionSummaryResponse, error) {

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return nil, err
	}

	var record model.UserTransactionSummaryResponse

	// [********] เพิ่มรายการโบนัส
	// [********] คืนยอดเทิร์น
	bonusTypeList := []int{
		model.CREDIT_TYPE_BONUS,
		model.CREDIT_TYPE_AFFILIATE_INCOME,
		model.CREDIT_TYPE_ALLIANCE_INCOME,
		model.CREDIT_TYPE_PROMOTION_RETURN_LOSS,
		model.CREDIT_TYPE_DAILY_ACTIVITY_BONUS,
		model.CREDIT_TPYE_LUCKY_WHEEL,
		model.CREDIT_TYPE_PROMOTION_WEB,
		model.CREDIT_TYPE_COUPON_CASH,
		model.CREDIT_TYPE_PROMOTION_RETURN_TURN,
	}

	// SUM
	selectedFields := "SUM(CASE WHEN logs.type_id = ? THEN logs.credit_amount ELSE 0 END) AS total_deposit_amount"
	// [********] ยกเลิกเติมเครดิต = หักลบออกจากยอดฝาก
	selectedFields += ", SUM(CASE WHEN logs.type_id = ? THEN logs.credit_back ELSE 0 END) AS total_cancel_credit_back"
	// [20231213] หน้ารายการฝาก-ถอน เสร็จสิ้น ช่วยปรับให้ยอดรวมรายการถอน ไม่รวมยอดดึงเครดิตกลับหน่อยค่ะ // , model.CREDIT_TYPE_TAKE_CREDIT_BACK
	// selectedFields += ", SUM(CASE WHEN logs.type_id = ? THEN logs.credit_amount ELSE 0 END) + SUM(CASE WHEN logs.type_id = ? THEN logs.credit_back ELSE 0 END) AS total_withdraw_amount"
	selectedFields += ", SUM(CASE WHEN logs.type_id = ? THEN logs.credit_amount ELSE 0 END) AS total_withdraw_amount"
	selectedFields += ", SUM(CASE WHEN logs.type_id IN ? OR (logs.type_id = ? AND logs.bonus_amount > 0) THEN logs.bonus_amount ELSE 0 END) AS total_bonus_amount"

	query := r.db.Table("user_transaction as logs")
	query = query.Select(selectedFields, model.CREDIT_TYPE_DEPOSIT, model.CREDIT_TYPE_CANCEL_CREDIT, model.CREDIT_TYPE_WITHDRAW, bonusTypeList, model.CREDIT_TYPE_DEPOSIT)
	query = query.Joins("LEFT JOIN user as users ON users.id = logs.user_id")
	if req.AccountId != nil {
		query = query.Where("logs.account_id = ?", req.AccountId)
	}
	if req.UserId != nil {
		query = query.Where("logs.user_id = ?", req.UserId)
	}
	if req.TypeId != nil {
		query = query.Where("logs.type_id = ?", req.TypeId)
	}
	if req.OfDate != "" {
		// OfDate is Primary
		startDateAtBkk, err := r.ParseBodBkk(req.OfDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("logs.transfer_at >= ? ", startDateAtBkk)
		endDateAtBkk, err := r.ParseEodBkk(req.OfDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("logs.transfer_at <=  ?", endDateAtBkk)
	} else {
		if req.FromDate != "" {
			startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
			if err != nil {
				return nil, err
			}
			query = query.Where("logs.transfer_at >= ? ", startDateAtBkk)
		}
		if req.ToDate != "" {
			endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
			if err != nil {
				return nil, err
			}
			query = query.Where("logs.transfer_at <=  ?", endDateAtBkk)
		}
	}

	if req.PaymentMerchatId != nil {
		query = query.Where("logs.payment_merchart_id = ?", req.PaymentMerchatId)
	}

	if req.Search != "" {
		search_like := fmt.Sprintf("%%%s%%", req.Search)
		query = query.Where(r.db.Where("logs.detail LIKE ?", search_like).Or("users.member_code LIKE ?", search_like).Or("users.username LIKE ?", search_like).Or("users.fullname LIKE ?", search_like))
	}

	if err := query.
		Where("logs.removed_at IS NULL").
		Where("logs.is_show = ?", true).
		Take(&record).
		Error; err != nil {
		if err != gorm.ErrRecordNotFound {
			return nil, err
		}
	}
	return &record, nil
}

func (r repo) GetCreditTransactionList(req model.UserTransactionListRequest) ([]model.UserTransactionResponse, int64, error) {

	var list []model.UserTransactionResponse
	var total int64

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("user_transaction as logs")
	count = count.Select("logs.id")
	if req.AccountId != nil {
		count = count.Where("logs.account_id = ?", req.AccountId)
	}
	if req.UserId != nil {
		count = count.Where("logs.user_id = ?", req.UserId)
	}
	if req.TypeId != nil {
		count = count.Where("logs.type_id = ?", req.TypeId)
	}
	if req.OfDate != "" {
		// OfDate is Primary
		startDateAtBkk, err := r.ParseBodBkk(req.OfDate)
		if err != nil {
			return nil, total, err
		}
		count = count.Where("logs.transfer_at >= ? ", startDateAtBkk)
		endDateAtBkk, err := r.ParseEodBkk(req.OfDate)
		if err != nil {
			return nil, total, err
		}
		count = count.Where("logs.transfer_at <=  ?", endDateAtBkk)
	} else {

		// Query Date today, yesterday, this_month
		actionTime := time.Now()
		bbkLoc := time.FixedZone("Asia/Bangkok", 7*60*60)
		filterTime := actionTime.In(bbkLoc)
		if req.DateType == "today" {
			req.FromDate = filterTime.Format("2006-01-02")
			req.ToDate = filterTime.Format("2006-01-02")
		} else if req.DateType == "yesterday" {
			req.FromDate = filterTime.AddDate(0, 0, -1).Format("2006-01-02")
			req.ToDate = filterTime.AddDate(0, 0, -1).Format("2006-01-02")
		} else if req.DateType == "this_month" {
			// full of this month
			req.FromDate = filterTime.Format("2006-01") + "-01"
			req.ToDate = filterTime.AddDate(0, 1, -1).Format("2006-01-02")
		}

		if req.FromDate != "" {
			startDateAtBkk, err := r.ParseBodBkk(req.FromDate)
			if err != nil {
				return nil, total, err
			}
			count = count.Where("logs.transfer_at >= ? ", startDateAtBkk)
		}
		if req.ToDate != "" {
			endDateAtBkk, err := r.ParseEodBkk(req.ToDate)
			if err != nil {
				return nil, total, err
			}
			count = count.Where("logs.transfer_at <=  ?", endDateAtBkk)
		}
	}
	if req.Search != "" {
		count = count.Joins("LEFT JOIN user as users ON users.id = logs.user_id")
		search_like := fmt.Sprintf("%%%s%%", req.Search)
		count = count.Where(r.db.Where("logs.detail LIKE ?", search_like).Or("users.member_code LIKE ?", search_like).Or("users.username LIKE ?", search_like).Or("users.fullname LIKE ?", search_like))
	}
	if req.AdminId != nil {
		count = count.Where(r.db.Where("logs.create_admin_id = ?", req.AdminId).Or("logs.confirm_admin_id = ?", req.AdminId))
	}
	if req.PaymentMerchatId != nil {
		count = count.Where("logs.payment_merchart_id = ?", req.PaymentMerchatId)
	}

	if err := count.
		Where("logs.removed_at IS NULL").
		Where("logs.is_show = ?", true).
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "logs.id, logs.direction_id, directions.name as direction_name, logs.account_id, logs.detail, logs.promotion_id"
		selectedFields += ", logs.user_id, users.member_code as user_member_code, users.username, users.fullname as user_fullname"
		// selectedFields += ", logs.type_id, IF(logs.type_id IN (1,2), '', types.detail) as type_name" // show empty string if type_id = 1 or 2
		selectedFields += ", logs.type_id, types.detail as type_name, logs.ref_id AS ref_id"
		selectedFields += ", logs.credit_before, logs.credit_back, logs.credit_amount, logs.bonus_amount, logs.credit_after, logs.transfer_at"
		selectedFields += ", logs.create_admin_id, create_admin.username as create_admin_username, logs.confirm_admin_id, confirm_admin.username as confirm_admin_username"
		selectedFields += ", logs.is_adjust_auto, logs.work_seconds, logs.created_at"
		query := r.db.Table("user_transaction as logs")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN user as users ON users.id = logs.user_id")
		query = query.Joins("LEFT JOIN user_transaction_type as types ON types.id = logs.type_id")
		query = query.Joins("LEFT JOIN user_transaction_direction as directions ON directions.id = logs.direction_id")
		query = query.Joins("LEFT JOIN admin as create_admin ON create_admin.id = logs.create_admin_id")
		query = query.Joins("LEFT JOIN admin as confirm_admin ON confirm_admin.id = logs.confirm_admin_id")

		// Filter by ANY //
		if req.AccountId != nil {
			query = query.Where("logs.account_id = ?", req.AccountId)
		}
		if req.UserId != nil {
			query = query.Where("logs.user_id = ?", req.UserId)
		}
		if req.TypeId != nil {
			query = query.Where("logs.type_id = ?", req.TypeId)
		}
		if req.OfDate != "" {
			// OfDate is Primary
			startDateAtBkk, err := r.ParseBodBkk(req.OfDate)
			if err != nil {
				return nil, total, err
			}
			query = query.Where("logs.transfer_at >= ? ", startDateAtBkk)
			endDateAtBkk, err := r.ParseEodBkk(req.OfDate)
			if err != nil {
				return nil, total, err
			}
			query = query.Where("logs.transfer_at <=  ?", endDateAtBkk)
		} else {

			// Query Date today, yesterday, this_month
			actionTime := time.Now()
			bbkLoc := time.FixedZone("Asia/Bangkok", 7*60*60)
			filterTime := actionTime.In(bbkLoc)
			if req.DateType == "today" {
				req.FromDate = filterTime.Format("2006-01-02")
				req.ToDate = filterTime.Format("2006-01-02")
			} else if req.DateType == "yesterday" {
				req.FromDate = filterTime.AddDate(0, 0, -1).Format("2006-01-02")
				req.ToDate = filterTime.AddDate(0, 0, -1).Format("2006-01-02")
			} else if req.DateType == "this_month" {
				// full of this month
				req.FromDate = filterTime.Format("2006-01") + "-01"
				req.ToDate = filterTime.AddDate(0, 1, -1).Format("2006-01-02")
			}

			if req.FromDate != "" {
				startDateAtBkk, err := r.ParseBodBkk(req.FromDate)
				if err != nil {
					return nil, total, err
				}
				query = query.Where("logs.transfer_at >= ? ", startDateAtBkk)
			}
			if req.ToDate != "" {
				endDateAtBkk, err := r.ParseEodBkk(req.ToDate)
				if err != nil {
					return nil, total, err
				}
				query = query.Where("logs.transfer_at <=  ?", endDateAtBkk)
			}
		}
		if req.Search != "" {
			search_like := fmt.Sprintf("%%%s%%", req.Search)
			query = query.Where(r.db.Where("logs.detail LIKE ?", search_like).Or("users.member_code LIKE ?", search_like).Or("users.username LIKE ?", search_like).Or("users.fullname LIKE ?", search_like))
		}
		if req.AdminId != nil {
			query = query.Where(r.db.Where("logs.create_admin_id = ?", req.AdminId).Or("logs.confirm_admin_id = ?", req.AdminId))
		}
		if req.PaymentMerchatId != nil {
			query = query.Where("logs.payment_merchart_id = ?", req.PaymentMerchatId)
		}

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		} else {
			query = query.Order("transfer_at ASC")
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Where("logs.removed_at IS NULL").
			Where("logs.is_show = ?", true).
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}

		// Deposit And withdraw slipUrl
		transIds := make(map[int64]int64)
		for _, v := range list {
			if v.RefId != nil {
				if v.TypeId == model.CREDIT_TYPE_DEPOSIT || v.TypeId == model.CREDIT_TYPE_WITHDRAW || v.TypeId == model.CREDIT_TYPE_BONUS {
					transIds[*v.RefId] = *v.RefId
				}
			}
		}

		if len(transIds) > 0 {
			var bankTransList []model.BankTransaction
			if err := r.db.Table("bank_transaction").Select("id, slip_img_url").Where("id IN (?)", helper.MapIdsToInt64Array(transIds)).Scan(&bankTransList).Error; err != nil {
				return list, total, err
			}
			for i, v := range list {
				if v.RefId != nil {
					if v.TypeId == model.CREDIT_TYPE_DEPOSIT || v.TypeId == model.CREDIT_TYPE_WITHDRAW || v.TypeId == model.CREDIT_TYPE_BONUS {
						for _, slip := range bankTransList {
							if *v.RefId == slip.Id {
								list[i].SlipImgUrl = slip.SlipImgUrl
							}
						}
					}
				}
			}
		}

	}
	return list, total, nil
}

func (r repo) GetCreditTransactionForExcelList(req model.UserTransactionForExcelListRequest) ([]model.UserTransactionResponse, int64, error) {

	var list []model.UserTransactionResponse
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("user_transaction as logs")
	count = count.Select("logs.id")
	count = count.Joins("LEFT JOIN user as users ON users.id = logs.user_id")
	if req.AccountId != nil {
		count = count.Where("logs.account_id = ?", req.AccountId)
	}
	if req.UserId != nil {
		count = count.Where("logs.user_id = ?", req.UserId)
	}
	if req.TypeId != nil {
		count = count.Where("logs.type_id = ?", req.TypeId)
	}
	if req.OfDate != "" {
		// OfDate is Primary
		startDateAtBkk, err := r.ParseBodBkk(req.OfDate)
		if err != nil {
			return nil, total, err
		}
		count = count.Where("logs.transfer_at >= ? ", startDateAtBkk)
		endDateAtBkk, err := r.ParseEodBkk(req.OfDate)
		if err != nil {
			return nil, total, err
		}
		count = count.Where("logs.transfer_at <=  ?", endDateAtBkk)
	} else {

		// Query Date today, yesterday, this_month
		actionTime := time.Now()
		bbkLoc := time.FixedZone("Asia/Bangkok", 7*60*60)
		filterTime := actionTime.In(bbkLoc)
		if req.DateType == "today" {
			req.FromDate = filterTime.Format("2006-01-02")
			req.ToDate = filterTime.Format("2006-01-02")
		} else if req.DateType == "yesterday" {
			req.FromDate = filterTime.AddDate(0, 0, -1).Format("2006-01-02")
			req.ToDate = filterTime.AddDate(0, 0, -1).Format("2006-01-02")
		} else if req.DateType == "this_month" {
			// full of this month
			req.FromDate = filterTime.Format("2006-01") + "-01"
			req.ToDate = filterTime.AddDate(0, 1, -1).Format("2006-01-02")
		}

		if req.FromDate != "" {
			startDateAtBkk, err := r.ParseBodBkk(req.FromDate)
			if err != nil {
				return nil, total, err
			}
			count = count.Where("logs.transfer_at >= ? ", startDateAtBkk)
		}
		if req.ToDate != "" {
			endDateAtBkk, err := r.ParseEodBkk(req.ToDate)
			if err != nil {
				return nil, total, err
			}
			count = count.Where("logs.transfer_at <=  ?", endDateAtBkk)
		}
	}
	if req.Search != "" {
		search_like := fmt.Sprintf("%%%s%%", req.Search)
		count = count.Where(r.db.Where("logs.detail LIKE ?", search_like).Or("users.member_code LIKE ?", search_like).Or("users.username LIKE ?", search_like).Or("users.fullname LIKE ?", search_like))
	}
	if req.AdminId != nil {
		count = count.Where(r.db.Where("logs.create_admin_id = ?", req.AdminId).Or("logs.confirm_admin_id = ?", req.AdminId))
	}

	if err = count.
		Where("logs.removed_at IS NULL").
		Where("logs.is_show = ?", true).
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "logs.id, logs.direction_id, directions.name as direction_name, logs.account_id, logs.detail, logs.promotion_id"
		selectedFields += ", logs.user_id, users.member_code as user_member_code, users.username, users.fullname as user_fullname"
		// selectedFields += ", logs.type_id, IF(logs.type_id IN (1,2), '', types.detail) as type_name" // show empty string if type_id = 1 or 2
		selectedFields += ", logs.type_id, types.detail as type_name, logs.ref_id AS ref_id"
		selectedFields += ", logs.credit_before, logs.credit_back, logs.credit_amount, logs.bonus_amount, logs.credit_after, logs.transfer_at"
		selectedFields += ", logs.create_admin_id, create_admin.username as create_admin_username, logs.confirm_admin_id, confirm_admin.username as confirm_admin_username"
		selectedFields += ", logs.is_adjust_auto, logs.work_seconds, logs.created_at"
		query := r.db.Table("user_transaction as logs")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN user as users ON users.id = logs.user_id")
		query = query.Joins("LEFT JOIN user_transaction_type as types ON types.id = logs.type_id")
		query = query.Joins("LEFT JOIN user_transaction_direction as directions ON directions.id = logs.direction_id")
		query = query.Joins("LEFT JOIN admin as create_admin ON create_admin.id = logs.create_admin_id")
		query = query.Joins("LEFT JOIN admin as confirm_admin ON confirm_admin.id = logs.confirm_admin_id")

		// Filter by ANY //
		if req.AccountId != nil {
			query = query.Where("logs.account_id = ?", req.AccountId)
		}
		if req.UserId != nil {
			query = query.Where("logs.user_id = ?", req.UserId)
		}
		if req.TypeId != nil {
			query = query.Where("logs.type_id = ?", req.TypeId)
		}
		if req.OfDate != "" {
			// OfDate is Primary
			startDateAtBkk, err := r.ParseBodBkk(req.OfDate)
			if err != nil {
				return nil, total, err
			}
			query = query.Where("logs.transfer_at >= ? ", startDateAtBkk)
			endDateAtBkk, err := r.ParseEodBkk(req.OfDate)
			if err != nil {
				return nil, total, err
			}
			query = query.Where("logs.transfer_at <=  ?", endDateAtBkk)
		} else {

			// Query Date today, yesterday, this_month
			actionTime := time.Now()
			bbkLoc := time.FixedZone("Asia/Bangkok", 7*60*60)
			filterTime := actionTime.In(bbkLoc)
			if req.DateType == "today" {
				req.FromDate = filterTime.Format("2006-01-02")
				req.ToDate = filterTime.Format("2006-01-02")
			} else if req.DateType == "yesterday" {
				req.FromDate = filterTime.AddDate(0, 0, -1).Format("2006-01-02")
				req.ToDate = filterTime.AddDate(0, 0, -1).Format("2006-01-02")
			} else if req.DateType == "this_month" {
				// full of this month
				req.FromDate = filterTime.Format("2006-01") + "-01"
				req.ToDate = filterTime.AddDate(0, 1, -1).Format("2006-01-02")
			}

			if req.FromDate != "" {
				startDateAtBkk, err := r.ParseBodBkk(req.FromDate)
				if err != nil {
					return nil, total, err
				}
				query = query.Where("logs.transfer_at >= ? ", startDateAtBkk)
			}
			if req.ToDate != "" {
				endDateAtBkk, err := r.ParseEodBkk(req.ToDate)
				if err != nil {
					return nil, total, err
				}
				query = query.Where("logs.transfer_at <=  ?", endDateAtBkk)
			}
		}
		if req.Search != "" {
			search_like := fmt.Sprintf("%%%s%%", req.Search)
			query = query.Where(r.db.Where("logs.detail LIKE ?", search_like).Or("users.member_code LIKE ?", search_like).Or("users.username LIKE ?", search_like).Or("users.fullname LIKE ?", search_like))
		}
		if req.AdminId != nil {
			query = query.Where(r.db.Where("logs.create_admin_id = ?", req.AdminId).Or("logs.confirm_admin_id = ?", req.AdminId))
		}

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		} else {
			query = query.Order("transfer_at ASC")
		}

		if err = query.
			Where("logs.removed_at IS NULL").
			Where("logs.is_show = ?", true).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) ShowUserTransaction(req model.UserTransactionShowUpdate) error {

	updateData := map[string]interface{}{}
	if req.TransactionId != 0 {
		updateData["ref_id"] = req.TransactionId
	}
	// ไม่มีใน db
	// if req.ConfirmedAt != nil {
	// 	updateData["confirmed_at"] = req.ConfirmedAt
	// }
	if req.ConfirmAdminId != nil {
		updateData["confirm_admin_id"] = req.ConfirmAdminId
	}
	if req.Detail != "" {
		updateData["detail"] = req.Detail
	}
	if req.AccountId != nil {
		updateData["account_id"] = req.AccountId
	}

	if req.RemovedExternal != nil && *req.RemovedExternal == 1 {
		updateData["account_id"] = gorm.Expr("NULL")
	}
	if req.FailedAddCredit != nil && *req.FailedAddCredit == 1 {
		updateData["is_show"] = false
	} else {
		updateData["is_show"] = true
	}
	if err := r.db.Table("user_transaction").Where("ref_id = ?", req.TransactionId).Where("is_show = ?", false).Updates(&updateData).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) RemoveCreditTransaction(id int64, adminId int64) error {

	updateData := map[string]interface{}{
		"removed_at":      time.Now(),
		"remove_admin_id": adminId,
	}
	if err := r.db.Table("user_transaction").Where("id = ?", id).Updates(&updateData).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetCreditTransactionRemovedList(req model.UserTransactionListRequest) ([]model.UserTransactionResponse, int64, error) {

	var list []model.UserTransactionResponse
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("user_transaction as logs")
	count = count.Select("logs.id")
	count = count.Joins("LEFT JOIN user as users ON users.id = logs.user_id")
	if req.AccountId != nil {
		count = count.Where("logs.account_id = ?", req.AccountId)
	}
	if req.UserId != nil {
		count = count.Where("logs.user_id = ?", req.UserId)
	}
	if req.TypeId != nil {
		count = count.Where("logs.type_id = ?", req.TypeId)
	}
	if req.OfDate != "" {
		// OfDate is Primary
		startDateAtBkk, err := r.ParseBodBkk(req.OfDate)
		if err != nil {
			return nil, total, err
		}
		count = count.Where("logs.transfer_at >= ? ", startDateAtBkk)
		endDateAtBkk, err := r.ParseEodBkk(req.OfDate)
		if err != nil {
			return nil, total, err
		}
		count = count.Where("logs.transfer_at <=  ?", endDateAtBkk)
	} else {

		// Query Date today, yesterday, this_month
		actionTime := time.Now()
		bbkLoc := time.FixedZone("Asia/Bangkok", 7*60*60)
		filterTime := actionTime.In(bbkLoc)
		if req.DateType == "today" {
			req.FromDate = filterTime.Format("2006-01-02")
			req.ToDate = filterTime.Format("2006-01-02")
		} else if req.DateType == "yesterday" {
			req.FromDate = filterTime.AddDate(0, 0, -1).Format("2006-01-02")
			req.ToDate = filterTime.AddDate(0, 0, -1).Format("2006-01-02")
		} else if req.DateType == "this_month" {
			// full of this month
			req.FromDate = filterTime.Format("2006-01") + "-01"
			req.ToDate = filterTime.AddDate(0, 1, -1).Format("2006-01-02")
		}

		if req.FromDate != "" {
			startDateAtBkk, err := r.ParseBodBkk(req.FromDate)
			if err != nil {
				return nil, total, err
			}
			count = count.Where("logs.transfer_at >= ? ", startDateAtBkk)
		}
		if req.ToDate != "" {
			endDateAtBkk, err := r.ParseEodBkk(req.ToDate)
			if err != nil {
				return nil, total, err
			}
			count = count.Where("logs.transfer_at <=  ?", endDateAtBkk)
		}
	}
	if req.Search != "" {
		search_like := fmt.Sprintf("%%%s%%", req.Search)
		count = count.Where(r.db.Where("logs.detail LIKE ?", search_like).Or("users.member_code LIKE ?", search_like).Or("users.username LIKE ?", search_like).Or("users.fullname LIKE ?", search_like))
	}

	if err = count.
		Where("logs.removed_at IS NOT NULL").
		Where("logs.is_show = ?", true).
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "logs.id, logs.direction_id, directions.name as direction_name, logs.account_id, logs.detail, logs.promotion_id"
		selectedFields += ", logs.user_id, users.member_code as user_member_code, users.username, users.fullname as user_fullname"
		// selectedFields += ", logs.type_id, IF(logs.type_id IN (1,2), '', types.detail) as type_name" // show empty string if type_id = 1 or 2
		selectedFields += ", logs.type_id, types.detail as type_name, logs.ref_id AS ref_id"
		selectedFields += ", logs.credit_before, logs.credit_back, logs.credit_amount, logs.bonus_amount, logs.credit_after, logs.transfer_at"
		selectedFields += ", logs.create_admin_id, create_admin.username as create_admin_username, logs.confirm_admin_id, confirm_admin.username as confirm_admin_username"
		selectedFields += ", logs.is_adjust_auto, logs.work_seconds, logs.created_at"
		selectedFields += ", logs.removed_at, logs.remove_admin_id as removed_admin_id, remove_admin.username as removed_admin_username"
		query := r.db.Table("user_transaction as logs")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN user as users ON users.id = logs.user_id")
		query = query.Joins("LEFT JOIN user_transaction_type as types ON types.id = logs.type_id")
		query = query.Joins("LEFT JOIN user_transaction_direction as directions ON directions.id = logs.direction_id")
		query = query.Joins("LEFT JOIN admin as create_admin ON create_admin.id = logs.create_admin_id")
		query = query.Joins("LEFT JOIN admin as confirm_admin ON confirm_admin.id = logs.confirm_admin_id")
		query = query.Joins("LEFT JOIN admin as remove_admin ON remove_admin.id = logs.remove_admin_id")
		// Filter by ANY //
		if req.AccountId != nil {
			query = query.Where("logs.account_id = ?", req.AccountId)
		}
		if req.UserId != nil {
			query = query.Where("logs.user_id = ?", req.UserId)
		}
		if req.TypeId != nil {
			query = query.Where("logs.type_id = ?", req.TypeId)
		}
		if req.OfDate != "" {
			// OfDate is Primary
			startDateAtBkk, err := r.ParseBodBkk(req.OfDate)
			if err != nil {
				return nil, total, err
			}
			query = query.Where("logs.transfer_at >= ? ", startDateAtBkk)
			endDateAtBkk, err := r.ParseEodBkk(req.OfDate)
			if err != nil {
				return nil, total, err
			}
			query = query.Where("logs.transfer_at <=  ?", endDateAtBkk)
		} else {

			// Query Date today, yesterday, this_month
			actionTime := time.Now()
			bbkLoc := time.FixedZone("Asia/Bangkok", 7*60*60)
			filterTime := actionTime.In(bbkLoc)
			if req.DateType == "today" {
				req.FromDate = filterTime.Format("2006-01-02")
				req.ToDate = filterTime.Format("2006-01-02")
			} else if req.DateType == "yesterday" {
				req.FromDate = filterTime.AddDate(0, 0, -1).Format("2006-01-02")
				req.ToDate = filterTime.AddDate(0, 0, -1).Format("2006-01-02")
			} else if req.DateType == "this_month" {
				// full of this month
				req.FromDate = filterTime.Format("2006-01") + "-01"
				req.ToDate = filterTime.AddDate(0, 1, -1).Format("2006-01-02")
			}

			if req.FromDate != "" {
				startDateAtBkk, err := r.ParseBodBkk(req.FromDate)
				if err != nil {
					return nil, total, err
				}
				query = query.Where("logs.transfer_at >= ? ", startDateAtBkk)
			}
			if req.ToDate != "" {
				endDateAtBkk, err := r.ParseEodBkk(req.ToDate)
				if err != nil {
					return nil, total, err
				}
				query = query.Where("logs.transfer_at <=  ?", endDateAtBkk)
			}
		}
		if req.Search != "" {
			search_like := fmt.Sprintf("%%%s%%", req.Search)
			query = query.Where(r.db.Where("logs.detail LIKE ?", search_like).Or("users.member_code LIKE ?", search_like).Or("users.username LIKE ?", search_like).Or("users.fullname LIKE ?", search_like))
		}

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Where("logs.removed_at IS NOT NULL").
			Where("logs.is_show = ?", true).
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}

		// Deposit And withdraw slipUrl
		transIds := make(map[int64]int64)
		for _, v := range list {
			if v.RefId != nil {
				if v.TypeId == model.CREDIT_TYPE_DEPOSIT || v.TypeId == model.CREDIT_TYPE_WITHDRAW {
					transIds[*v.RefId] = *v.RefId
				}
			}
		}

		if len(transIds) > 0 {
			var bankTransList []model.BankTransaction
			if err := r.db.Table("bank_transaction").Select("id, slip_img_url").Where("id IN (?)", helper.MapIdsToInt64Array(transIds)).Scan(&bankTransList).Error; err != nil {
				return list, total, err
			}
			for i, v := range list {
				if v.RefId != nil {
					if v.TypeId == model.CREDIT_TYPE_DEPOSIT || v.TypeId == model.CREDIT_TYPE_WITHDRAW {
						for _, slip := range bankTransList {
							if *v.RefId == slip.Id {
								list[i].SlipImgUrl = slip.SlipImgUrl
							}
						}
					}
				}
			}
		}
	}

	return list, total, nil
}
