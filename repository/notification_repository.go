package repository

import (
	"bytes"
	"cybergame-api/helper"
	"cybergame-api/model"
	"fmt"
	"math"
	"net/http"
	"os"
	"strconv"
	"time"

	"gorm.io/gorm"
)

func NewNotificationRepository(db *gorm.DB) NotificationRepository {
	return &repo{db}
}

type NotificationRepository interface {

	// TelegramToken
	GetTelegramAccessToken() (*model.TelegramAccessTokenResponse, error)
	UpdateTelegramAccessToken(token string) error

	//notification options
	GetNotificationType() ([]model.ConfigurationNotificationType, error)

	//notification configurations
	GetConfigurationNotification() (*model.ConfigurationNotification, error)
	CreateConfigurationNotification(body model.CreateConfigurationNotificationBody) (*int64, error)
	UpdateConfigurationNotification(body model.UpdateConfigurationNotificationBody) error

	//line notification
	SendLineNotification(message string, lineToken string) error
	GetTransactionByIdForNoti(transId int64) (*model.GetBankTransactionDepositListResponse, error)
	GetConfiguration() (*model.ConfigurationResponse, error)

	//admin
	GetAdmin(id int64) (*model.Admin, []model.PermissionList, *model.GroupDetail, *model.AdminWebSetting, error)

	// new
	CreateConfigurationBackofficeNotification(body model.CreateConfigurationBackofficeNotificationBody) (*int64, error)
	GetConfigurationBackofficeNotification() (*model.GetConfigurationBackofficeNotificationResponse, error)
	UpdateConfigurationBackofficeNotification(body model.UpdateConfigurationBackofficeNotificationBody) error

	//token
	CreateConfigurationNotificationToken(body []model.CreateConfigurationNotificationTokenBody) error
	UpdateConfigurationNotificationToken(body model.UpdateConfigurationNotificationTokenBody) error
	DeleteConfigurationNotificationToken(id int64) error
	GetConfigurationNotificationTokenById(id int64) (*model.GetConfigurationNotificationTokenResponse, error)
	GetConfigurationNotificationTokenList(req model.GetConfigurationNotificationTokenList) ([]model.GetConfigurationNotificationTokenResponse, error)
	DeleteConfigurationNotificationTokenWithConfigId(configurationExternalNotificationId int64) error
	GetConfigurationNotificationTokenByExternalId(external int64) ([]model.GetConfigurationNotificationTokenResponse, error)

	//external notification
	CreateConfigurationExternalNotification(body model.CreateConfigurationExternalNotificationBody) (*int64, error)
	DeleteConfigurationExternalNotification(id int64) error
	GetConfigurationExternalNotificationList(req model.GetConfigurationExternalNotificationListRequest) ([]model.GetConfigurationExternalNotificationListResponse, int64, error)
	GetConfigurationExternalNotificationById(id int64) (*model.GetConfigurationExternalNotificationByIdBody, error)
	UpdateConfigurationExternalNotification(body model.UpdateConfigurationExternalNotificationBody) error
	GetConfigurationExternalNotification() ([]model.GetConfigurationExternalNotificationByIdBody, error)

	WebSocket(reqAlert model.WebScoket) error
	SendTelegramMessage(chatInfo model.TelegramChatTokenResponse, message string) (*model.TelegramSendMessageResponse, error)
	GetTelegramChatTokenByToken(token string) (*model.TelegramChatTokenResponse, error)
	GetFirstTimeDepositForNoti(transId int64) (bool, error)

	//backup
	AutoCreateBackUpTokenInUse() (*model.ConfigurationNotification, error)
	GetConfigurationExternalNotificationInternal() ([]model.GetConfigurationExternalNotificationByIdBody, error)
	UpdateOldTokenToLine(oldToken string) error
	// clear cache
	ClearNotificationCache() error

	// summary report
	GetUserNewMemberForNoti() (*model.UserNewMemberCountResponse, error)
	GetFirstTimeDepositATodayAmountForNoti() (int64, error)

	// NOTIFICATION REPORT
	GetAccountMoveTransactionDetail(id int64) (*model.AccountMoveTransactionResponse, error)
	GetTransactionSummaryReportNotification(startDate time.Time, endDate time.Time) (*model.GetTransactionSummaryReportNotificationResponse, error)
	GetBankTransactionSummaryReportNotification(startDate time.Time, endDate time.Time) ([]model.GetBankTransactionSummaryReportNotificationResponse, error)
	GetAllianceUserSummaryReportNotification(startDate time.Time, endDate time.Time) ([]model.GetAllianceUserSummaryReportNotification, error)
	ParseBodBkk(input string) (*time.Time, error)
	ParseEodBkk(input string) (*time.Time, error)
}

var savecacheExternalNoti []model.GetConfigurationExternalNotificationByIdBody
var savecacheBackofficeNoti *model.GetConfigurationBackofficeNotificationResponse

func (r *repo) GetNotificationType() ([]model.ConfigurationNotificationType, error) {

	var options []model.ConfigurationNotificationType

	selectedFields := "id, name, label_th, label_en"
	sql := r.db.Table("configuration_notification_type")
	sql = sql.Select(selectedFields)

	if err := sql.Find(&options).Error; err != nil {
		return nil, err
	}
	return options, nil
}

func (r *repo) GetConfigurationNotification() (*model.ConfigurationNotification, error) {

	var configurationNotification model.ConfigurationNotification

	selectedFields := "cn.id AS id, cn.credit_above AS credit_above, cn.line_token AS line_token"
	selectedFields += ", cn.is_member_registration AS is_member_registration, cn.is_deposit_before_credit AS is_deposit_before_credit, cn.is_deposit_after_credit AS is_deposit_after_credit, cn.is_withdrawal_credit_success AS is_withdrawal_credit_success, cn.is_withdrawal_awaiting_transfer AS is_withdrawal_awaiting_transfer, cn.is_withdrawal_credit_failed AS is_withdrawal_credit_failed"
	selectedFields += ", cnt.label_th AS configuration_notification_type_name , cn.configuration_notification_type_id AS configuration_notification_type_id"
	selectedFields += ", cn.is_deposit_bonus AS is_deposit_bonus, cn.is_pull_credit_back AS is_pull_credit_back"
	selectedFields += ", cn.is_actitvity_before_bonus AS is_actitvity_before_bonus, cn.is_actitvity_after_bonus AS is_actitvity_after_bonus"
	selectedFields += ", cn.is_promotion_bonus AS is_promotion_bonus"

	sql := r.db.Table("configuration_notification AS cn")
	sql = sql.Joins("JOIN configuration_notification_type AS cnt ON cnt.id = cn.configuration_notification_type_id")
	sql = sql.Select(selectedFields)
	if err := sql.First(&configurationNotification).Error; err != nil {
		return nil, err
	}
	return &configurationNotification, nil
}

func (r *repo) CreateConfigurationNotification(body model.CreateConfigurationNotificationBody) (*int64, error) {

	sql := r.db.Table("configuration_notification")
	if err := sql.Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r *repo) UpdateConfigurationNotification(body model.UpdateConfigurationNotificationBody) error {

	updataData := make(map[string]interface{})

	updataData["is_member_registration"] = body.IsMemberRegistration
	updataData["is_deposit_before_credit"] = body.IsDepositBeforeCredit
	updataData["is_deposit_after_credit"] = body.IsDepositAfterCredit
	updataData["is_withdrawal_credit_success"] = body.IsWithdrawalCreditSuccess
	updataData["is_withdrawal_awaiting_transfer"] = body.IsWithdrawalAwaitingTransfer
	updataData["is_withdrawal_credit_failed"] = body.IsWithdrawalCreditFailed
	updataData["is_deposit_bonus"] = body.IsDepositBonus
	updataData["is_pull_credit_back"] = body.IsPullCreditBack
	updataData["is_actitvity_before_bonus"] = body.IsActitvityBeforeBonus
	updataData["is_actitvity_after_bonus"] = body.IsActitvityAfterBonus
	updataData["is_promotion_bonus"] = body.IsPromotionBonus

	if body.CreditAbove != nil {
		updataData["credit_above"] = body.CreditAbove
	}
	if body.LineToken != nil {
		updataData["line_token"] = body.LineToken
	}
	if body.ConfigurationNotificationTypeId != nil {
		updataData["configuration_notification_type_id"] = body.ConfigurationNotificationTypeId
	}
	if err := r.db.Table("configuration_notification").Where("id = ?", body.Id).Updates(updataData).Error; err != nil {
		return err
	}

	return nil
}

func (r repo) SendLineNotification(message string, lineToken string) error {

	url := os.Getenv("LINE_NOTIFY_URL")
	token := lineToken

	// Create the request body with the message
	body := []byte(fmt.Sprintf("message=%s", message))

	// Create a new HTTP POST request
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(body))
	if err != nil {
		return err
	}

	// Set the Authorization header
	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	// Send the HTTP request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// Check the response status
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("failed to send Line notification, status: %d", resp.StatusCode)
	}

	return nil
}

func (r *repo) CreateConfigurationBackofficeNotification(body model.CreateConfigurationBackofficeNotificationBody) (*int64, error) {

	query := r.db.Table("configuration_backoffice_notification")
	if err := query.Create(&body).Error; err != nil {
		return nil, err
	}

	savecacheBackofficeNoti = nil

	return &body.Id, nil
}

func (r *repo) GetConfigurationBackofficeNotification() (*model.GetConfigurationBackofficeNotificationResponse, error) {

	if savecacheBackofficeNoti != nil {
		return savecacheBackofficeNoti, nil
	}

	var configurationResponse model.GetConfigurationBackofficeNotificationResponse

	selectedFields := "cbn.id AS id, cbn.backoffice_is_on AS backoffice_is_on, cbn.backoffice_new_member AS backoffice_new_member, cbn.backoffice_deposit AS backoffice_deposit, cbn.backoffice_withdraw AS backoffice_withdraw, cbn.backoffice_bonus AS backoffice_bonus"
	selectedFields += ", cbn.backoffice_sound_on_new_member AS backoffice_sound_on_new_member, cbn.backoffice_sound_on_deposit AS backoffice_sound_on_deposit, cbn.backoffice_sound_on_withdraw AS backoffice_sound_on_withdraw, cbn.backoffice_sound_on_bonus AS backoffice_sound_on_bonus"
	sql := r.db.Table("configuration_backoffice_notification AS cbn")
	sql = sql.Select(selectedFields)
	if err := sql.First(&configurationResponse).Error; err != nil {
		return nil, err
	}

	savecacheBackofficeNoti = &configurationResponse

	return &configurationResponse, nil
}

func (r *repo) UpdateConfigurationBackofficeNotification(body model.UpdateConfigurationBackofficeNotificationBody) error {

	updataData := make(map[string]interface{})
	if body.BackofficeIsOn != nil {
		updataData["backoffice_is_on"] = body.BackofficeIsOn
	}
	// updataData["backoffice_new_member"] = body.BackofficeNewMember
	// updataData["backoffice_deposit"] = body.BackofficeDeposit
	// updataData["backoffice_withdraw"] = body.BackofficeWithdraw
	// updataData["backoffice_bonus"] = body.BackofficeBonus
	if body.BackofficeNewMember != nil {
		updataData["backoffice_new_member"] = body.BackofficeNewMember
	}
	if body.BackofficeDeposit != nil {
		updataData["backoffice_deposit"] = body.BackofficeDeposit
	}
	if body.BackofficeWithdraw != nil {
		updataData["backoffice_withdraw"] = body.BackofficeWithdraw
	}
	if body.BackofficeBonus != nil {
		updataData["backoffice_bonus"] = body.BackofficeBonus
	}
	if body.BackofficeSoundOnNewMember != nil {
		updataData["backoffice_sound_on_new_member"] = body.BackofficeSoundOnNewMember
	}
	if body.BackofficeSoundOnDeposit != nil {
		updataData["backoffice_sound_on_deposit"] = body.BackofficeSoundOnDeposit
	}
	if body.BackofficeSoundOnWithdraw != nil {
		updataData["backoffice_sound_on_withdraw"] = body.BackofficeSoundOnWithdraw
	}
	if body.BackofficeSoundOnBonus != nil {
		updataData["backoffice_sound_on_bonus"] = body.BackofficeSoundOnBonus
	}
	if err := r.db.Table("configuration_backoffice_notification").Where("id = ?", body.Id).Updates(updataData).Error; err != nil {
		return err
	}

	savecacheBackofficeNoti = nil

	return nil
}

func (r *repo) CreateConfigurationNotificationToken(body []model.CreateConfigurationNotificationTokenBody) error {

	query := r.db.Table("configuration_notification_token")
	if err := query.Create(&body).Error; err != nil {
		return err
	}
	return nil
}

func (r *repo) UpdateConfigurationNotificationToken(body model.UpdateConfigurationNotificationTokenBody) error {

	if err := r.db.Table("configuration_notification_token").Where("id = ?", body.Id).Updates(&body).Error; err != nil {
		return err
	}
	return nil
}

func (r *repo) DeleteConfigurationNotificationToken(id int64) error {

	if err := r.db.Table("configuration_notification_token").Where("id = ?", id).Delete(&model.ConfigurationNotificationToken{}).Error; err != nil {
		return err
	}
	return nil
}

func (r *repo) DeleteConfigurationNotificationTokenWithConfigId(configurationExternalNotificationId int64) error {

	if err := r.db.Table("configuration_notification_token").Where("configuration_external_notification_id = ?", configurationExternalNotificationId).Delete(&model.ConfigurationNotificationToken{}).Error; err != nil {
		return err
	}
	return nil
}

func (r *repo) GetConfigurationNotificationTokenById(id int64) (*model.GetConfigurationNotificationTokenResponse, error) {

	var configurationNotificationToken model.GetConfigurationNotificationTokenResponse

	selectedFields := "cnt.id AS id, cnt.configuration_external_notification_id AS configuration_external_notification_id, cnt.configuration_token_type_id AS configuration_token_type_id, cnt.token AS token"
	sql := r.db.Table("configuration_notification_token AS cnt")
	sql = sql.Select(selectedFields)
	sql = sql.Where("cnt.id = ?", id)
	if err := sql.First(&configurationNotificationToken).Error; err != nil {
		return nil, err
	}

	return &configurationNotificationToken, nil
}

func (r *repo) GetConfigurationNotificationTokenByExternalId(external int64) ([]model.GetConfigurationNotificationTokenResponse, error) {

	var configurationNotificationToken []model.GetConfigurationNotificationTokenResponse

	selectedFields := "cnt.id AS id, cnt.configuration_external_notification_id AS configuration_external_notification_id, cnt.configuration_token_type_id AS configuration_token_type_id, cnt.token AS token"
	sql := r.db.Table("configuration_notification_token AS cnt")
	sql = sql.Select(selectedFields)
	sql = sql.Where("cnt.configuration_external_notification_id = ?", external)
	if err := sql.Scan(&configurationNotificationToken).Error; err != nil {
		return nil, err
	}

	return configurationNotificationToken, nil
}

func (r *repo) GetConfigurationNotificationTokenList(req model.GetConfigurationNotificationTokenList) ([]model.GetConfigurationNotificationTokenResponse, error) {

	var configurationNotificationToken []model.GetConfigurationNotificationTokenResponse

	selectedFields := "cnt.id AS id, cnt.configuration_external_notification_id AS configuration_external_notification_id, cnt.configuration_token_type_id AS configuration_token_type_id, cnt.token AS token"
	sql := r.db.Table("configuration_notification_token AS cnt")
	sql = sql.Select(selectedFields)
	if req.ConfigurationExternalNotificationId != nil {
		sql = sql.Where("cnt.configuration_external_notification_id = ?", req.ConfigurationExternalNotificationId)
	}
	if req.ConfigurationTokenTypeId != nil {
		sql = sql.Where("cnt.configuration_token_type_id = ?", req.ConfigurationTokenTypeId)
	}
	if err := sql.Scan(&configurationNotificationToken).Error; err != nil {
		return nil, err
	}

	return configurationNotificationToken, nil
}

func (r *repo) CreateConfigurationExternalNotification(body model.CreateConfigurationExternalNotificationBody) (*int64, error) {

	query := r.db.Table("configuration_external_notification")
	if err := query.Create(&body).Error; err != nil {
		return nil, err
	}

	savecacheExternalNoti = nil

	return &body.Id, nil
}

func (r *repo) DeleteConfigurationExternalNotification(id int64) error {

	if err := r.db.Table("configuration_external_notification").Where("id = ?", id).Delete(&model.ConfigurationExternalNotification{}).Error; err != nil {
		return err
	}

	savecacheExternalNoti = nil

	return nil
}

func (r *repo) GetConfigurationExternalNotificationList(req model.GetConfigurationExternalNotificationListRequest) ([]model.GetConfigurationExternalNotificationListResponse, int64, error) {

	var configurationExternalNotification []model.GetConfigurationExternalNotificationListResponse
	var total int64

	selectedFields := "cen.id AS id, cen.notification_name AS notification_name"
	selectedFields += ", (SELECT COUNT(*) FROM configuration_notification_token AS cnt WHERE cnt.configuration_external_notification_id = cen.id AND cnt.configuration_token_type_id = 1) AS line_count"
	selectedFields += ", (SELECT COUNT(*) FROM configuration_notification_token AS cnt WHERE cnt.configuration_external_notification_id = cen.id AND cnt.configuration_token_type_id = 2) AS telegram_count"

	count := r.db.Table("configuration_external_notification AS cen")
	count = count.Select(selectedFields)
	if req.NotificationName != "" {
		count = count.Where("cen.notification_name LIKE ?", "%"+req.NotificationName+"%")
	}
	if err := count.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if total > 0 {
		query := r.db.Table("configuration_external_notification AS cen")
		query = query.Select(selectedFields)
		if req.NotificationName != "" {
			query = query.Where("cen.notification_name LIKE ?", "%"+req.NotificationName+"%")
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&configurationExternalNotification).Error; err != nil {
			return nil, 0, err
		}
	}

	return configurationExternalNotification, total, nil
}

func (r *repo) GetConfigurationExternalNotificationById(id int64) (*model.GetConfigurationExternalNotificationByIdBody, error) {

	var configurationExternalNotification model.GetConfigurationExternalNotificationByIdBody

	selectedFields := "cen.id AS id, cen.notification_name AS notification_name"
	selectedFields += ", cen.telegram_is_on AS telegram_is_on, cen.telegram_new_member AS telegram_new_member, cen.telegram_before_deposit AS telegram_before_deposit, cen.telegram_after_deposit AS telegram_after_deposit, cen.telegram_withdraw_success AS telegram_withdraw_success, cen.telegram_withdraw_pending AS telegram_withdraw_pending, cen.telegram_withdraw_failed AS telegram_withdraw_failed, cen.telegram_pull_credit AS telegram_pull_credit, cen.telegram_bonus AS telegram_bonus, cen.telegram_promotion AS telegram_promotion, cen.telegram_activity_before_bonus AS telegram_activity_before_bonus, cen.telegram_activity_after_bonus AS telegram_activity_after_bonus"
	selectedFields += ", cen.line_is_on AS line_is_on, cen.line_new_member AS line_new_member, cen.line_before_deposit AS line_before_deposit, cen.line_after_deposit AS line_after_deposit, cen.line_withdraw_success AS line_withdraw_success, cen.line_withdraw_pending AS line_withdraw_pending, cen.line_withdraw_failed AS line_withdraw_failed, cen.line_pull_credit AS line_pull_credit, cen.line_bonus AS line_bonus, cen.line_promotion AS line_promotion, cen.line_activity_before_bonus AS line_activity_before_bonus, cen.line_activity_after_bonus AS line_activity_after_bonus"
	selectedFields += ", cen.telegram_move_money AS telegram_move_money, cen.line_move_money AS line_move_money"
	selectedFields += ", cen.telegram_transaction_hour_summary AS telegram_transaction_hour_summary, cen.line_transaction_hour_summary AS line_transaction_hour_summary"
	selectedFields += ", cen.telegram_affiliate_daily_summary AS telegram_affiliate_daily_summary, cen.line_affiliate_daily_summary AS line_affiliate_daily_summary"
	selectedFields += ", cen.telegram_transaction_daily_summary AS telegram_transaction_daily_summary, cen.line_transaction_daily_summary AS line_transaction_daily_summary"

	sql := r.db.Table("configuration_external_notification AS cen")
	sql = sql.Select(selectedFields)
	sql = sql.Where("cen.id = ?", id)
	if err := sql.First(&configurationExternalNotification).Error; err != nil {
		return nil, err
	}
	return &configurationExternalNotification, nil
}

func (r *repo) GetConfigurationExternalNotification() ([]model.GetConfigurationExternalNotificationByIdBody, error) {

	if len(savecacheExternalNoti) > 0 {
		return savecacheExternalNoti, nil
	}

	var configurationExternalNotification []model.GetConfigurationExternalNotificationByIdBody

	selectedFields := "cen.id AS id, cen.notification_name AS notification_name"
	selectedFields += ", cen.telegram_is_on AS telegram_is_on, cen.telegram_new_member AS telegram_new_member, cen.telegram_before_deposit AS telegram_before_deposit, cen.telegram_after_deposit AS telegram_after_deposit, cen.telegram_withdraw_success AS telegram_withdraw_success, cen.telegram_withdraw_pending AS telegram_withdraw_pending, cen.telegram_withdraw_failed AS telegram_withdraw_failed, cen.telegram_pull_credit AS telegram_pull_credit, cen.telegram_bonus AS telegram_bonus, cen.telegram_promotion AS telegram_promotion, cen.telegram_activity_before_bonus AS telegram_activity_before_bonus, cen.telegram_activity_after_bonus AS telegram_activity_after_bonus"
	selectedFields += ", cen.line_is_on AS line_is_on, cen.line_new_member AS line_new_member, cen.line_before_deposit AS line_before_deposit, cen.line_after_deposit AS line_after_deposit, cen.line_withdraw_success AS line_withdraw_success, cen.line_withdraw_pending AS line_withdraw_pending, cen.line_withdraw_failed AS line_withdraw_failed, cen.line_pull_credit AS line_pull_credit, cen.line_bonus AS line_bonus, cen.line_promotion AS line_promotion, cen.line_activity_before_bonus AS line_activity_before_bonus, cen.line_activity_after_bonus AS line_activity_after_bonus"
	selectedFields += ", cen.telegram_move_money AS telegram_move_money, cen.line_move_money AS line_move_money"
	selectedFields += ", cen.telegram_transaction_hour_summary AS telegram_transaction_hour_summary, cen.line_transaction_hour_summary AS line_transaction_hour_summary"
	selectedFields += ", cen.telegram_affiliate_daily_summary AS telegram_affiliate_daily_summary, cen.line_affiliate_daily_summary AS line_affiliate_daily_summary"
	selectedFields += ", cen.telegram_transaction_daily_summary AS telegram_transaction_daily_summary, cen.line_transaction_daily_summary AS line_transaction_daily_summary"
	sql := r.db.Table("configuration_external_notification AS cen")
	sql = sql.Select(selectedFields)
	if err := sql.Scan(&configurationExternalNotification).Error; err != nil {
		return nil, err
	}

	savecacheExternalNoti = configurationExternalNotification

	return configurationExternalNotification, nil
}

func (r *repo) UpdateConfigurationExternalNotification(body model.UpdateConfigurationExternalNotificationBody) error {

	updataData := make(map[string]interface{})

	if body.NotificationName != nil {
		updataData["notification_name"] = *body.NotificationName
	}
	if body.TelegramIsOn != nil {
		updataData["telegram_is_on"] = *body.TelegramIsOn
	}
	if body.TelegramNewMember != nil {
		updataData["telegram_new_member"] = *body.TelegramNewMember
	}
	if body.TelegramBeforeDeposit != nil {
		updataData["telegram_before_deposit"] = *body.TelegramBeforeDeposit
	}
	if body.TelegramAfterDeposit != nil {
		updataData["telegram_after_deposit"] = *body.TelegramAfterDeposit
	}
	if body.TelegramWithdrawSuccess != nil {
		updataData["telegram_withdraw_success"] = *body.TelegramWithdrawSuccess
	}
	if body.TelegramWithdrawPending != nil {
		updataData["telegram_withdraw_pending"] = *body.TelegramWithdrawPending
	}
	if body.TelegramWithdrawFailed != nil {
		updataData["telegram_withdraw_failed"] = *body.TelegramWithdrawFailed
	}
	if body.TelegramPullCredit != nil {
		updataData["telegram_pull_credit"] = *body.TelegramPullCredit
	}
	if body.TelegramBonus != nil {
		updataData["telegram_bonus"] = *body.TelegramBonus
	}
	if body.TelegramPromotion != nil {
		updataData["telegram_promotion"] = *body.TelegramPromotion
	}
	if body.TelegramActivityBeforeBonus != nil {
		updataData["telegram_activity_before_bonus"] = *body.TelegramActivityBeforeBonus
	}
	if body.TelegramActivityAfterBonus != nil {
		updataData["telegram_activity_after_bonus"] = *body.TelegramActivityAfterBonus
	}
	if body.LineIsOn != nil {
		updataData["line_is_on"] = *body.LineIsOn
	}
	if body.LineNewMember != nil {
		updataData["line_new_member"] = *body.LineNewMember
	}
	if body.LineBeforeDeposit != nil {
		updataData["line_before_deposit"] = *body.LineBeforeDeposit
	}
	if body.LineAfterDeposit != nil {
		updataData["line_after_deposit"] = *body.LineAfterDeposit
	}
	if body.LineWithdrawSuccess != nil {
		updataData["line_withdraw_success"] = *body.LineWithdrawSuccess
	}
	if body.LineWithdrawPending != nil {
		updataData["line_withdraw_pending"] = *body.LineWithdrawPending
	}
	if body.LineWithdrawFailed != nil {
		updataData["line_withdraw_failed"] = *body.LineWithdrawFailed
	}
	if body.LinePullCredit != nil {
		updataData["line_pull_credit"] = *body.LinePullCredit
	}
	if body.LineBonus != nil {
		updataData["line_bonus"] = *body.LineBonus
	}
	if body.LinePromotion != nil {
		updataData["line_promotion"] = *body.LinePromotion
	}
	if body.LineActivityBeforeBonus != nil {
		updataData["line_activity_before_bonus"] = *body.LineActivityBeforeBonus
	}
	if body.LineActivityAfterBonus != nil {
		updataData["line_activity_after_bonus"] = *body.LineActivityAfterBonus
	}
	if body.TelegramMoveMoney != nil {
		updataData["telegram_move_money"] = *body.TelegramMoveMoney
	}
	if body.LineMoveMoney != nil {
		updataData["line_move_money"] = *body.LineMoveMoney
	}
	if body.TelegramTransactionHourSummary != nil {
		updataData["telegram_transaction_hour_summary"] = *body.TelegramTransactionHourSummary
	}
	if body.LineTransactionHourSummary != nil {
		updataData["line_transaction_hour_summary"] = *body.LineTransactionHourSummary
	}
	if body.TelegramAffiliateDailySummary != nil {
		updataData["telegram_affiliate_daily_summary"] = *body.TelegramAffiliateDailySummary
	}
	if body.LineAffiliateDailySummary != nil {
		updataData["line_affiliate_daily_summary"] = *body.LineAffiliateDailySummary
	}
	if body.TelegramTransactionDailySummary != nil {
		updataData["telegram_transaction_daily_summary"] = *body.TelegramTransactionDailySummary
	}
	if body.LineTransactionDailySummary != nil {
		updataData["line_transaction_daily_summary"] = *body.LineTransactionDailySummary
	}

	if err := r.db.Table("configuration_external_notification").Where("id = ?", body.Id).Updates(updataData).Error; err != nil {
		return err
	}

	savecacheExternalNoti = nil

	return nil
}

func (r *repo) AutoCreateBackUpTokenInUse() (*model.ConfigurationNotification, error) {

	var configurationNotification model.ConfigurationNotification

	selectedFields := "cn.id AS id, cn.credit_above AS credit_above, cn.line_token AS line_token"
	selectedFields += ", cn.is_member_registration AS is_member_registration, cn.is_deposit_before_credit AS is_deposit_before_credit, cn.is_deposit_after_credit AS is_deposit_after_credit, cn.is_withdrawal_credit_success AS is_withdrawal_credit_success, cn.is_withdrawal_awaiting_transfer AS is_withdrawal_awaiting_transfer, cn.is_withdrawal_credit_failed AS is_withdrawal_credit_failed"
	selectedFields += ", cnt.label_th AS configuration_notification_type_name , cn.configuration_notification_type_id AS configuration_notification_type_id"
	selectedFields += ", cn.is_deposit_bonus AS is_deposit_bonus, cn.is_pull_credit_back AS is_pull_credit_back"
	selectedFields += ", cn.is_actitvity_before_bonus AS is_actitvity_before_bonus, cn.is_actitvity_after_bonus AS is_actitvity_after_bonus"
	selectedFields += ", cn.is_promotion_bonus AS is_promotion_bonus"

	sql := r.db.Table("configuration_notification AS cn")
	sql = sql.Joins("JOIN configuration_notification_type AS cnt ON cnt.id = cn.configuration_notification_type_id")
	sql = sql.Select(selectedFields)
	if err := sql.First(&configurationNotification).Error; err != nil {
		return nil, err
	}
	return &configurationNotification, nil
}

func (r *repo) GetConfigurationExternalNotificationInternal() ([]model.GetConfigurationExternalNotificationByIdBody, error) {

	var configurationExternalNotification []model.GetConfigurationExternalNotificationByIdBody

	selectedFields := "cen.id AS id, cen.notification_name AS notification_name"
	selectedFields += ", cen.telegram_is_on AS telegram_is_on, cen.telegram_new_member AS telegram_new_member, cen.telegram_before_deposit AS telegram_before_deposit, cen.telegram_after_deposit AS telegram_after_deposit, cen.telegram_withdraw_success AS telegram_withdraw_success, cen.telegram_withdraw_pending AS telegram_withdraw_pending, cen.telegram_withdraw_failed AS telegram_withdraw_failed, cen.telegram_pull_credit AS telegram_pull_credit, cen.telegram_bonus AS telegram_bonus, cen.telegram_promotion AS telegram_promotion, cen.telegram_activity_before_bonus AS telegram_activity_before_bonus, cen.telegram_activity_after_bonus AS telegram_activity_after_bonus"
	selectedFields += ", cen.line_is_on AS line_is_on, cen.line_new_member AS line_new_member, cen.line_before_deposit AS line_before_deposit, cen.line_after_deposit AS line_after_deposit, cen.line_withdraw_success AS line_withdraw_success, cen.line_withdraw_pending AS line_withdraw_pending, cen.line_withdraw_failed AS line_withdraw_failed, cen.line_pull_credit AS line_pull_credit, cen.line_bonus AS line_bonus, cen.line_promotion AS line_promotion, cen.line_activity_before_bonus AS line_activity_before_bonus, cen.line_activity_after_bonus AS line_activity_after_bonus"
	selectedFields += ", cen.telegram_move_money AS telegram_move_money, cen.line_move_money AS line_move_money"
	selectedFields += ", cen.telegram_transaction_hour_summary AS telegram_transaction_hour_summary, cen.line_transaction_hour_summary AS line_transaction_hour_summary"
	selectedFields += ", cen.telegram_affiliate_daily_summary AS telegram_affiliate_daily_summary, cen.line_affiliate_daily_summary AS line_affiliate_daily_summary"
	selectedFields += ", cen.telegram_transaction_daily_summary AS telegram_transaction_daily_summary, cen.line_transaction_daily_summary AS line_transaction_daily_summary"
	sql := r.db.Table("configuration_external_notification AS cen")
	sql = sql.Select(selectedFields)
	if err := sql.Scan(&configurationExternalNotification).Error; err != nil {
		return nil, err
	}
	return configurationExternalNotification, nil
}

func (r repo) UpdateOldTokenToLine(oldToken string) error {

	if err := r.db.Table("configuration_notification_token").Where("token = ?", oldToken).Update("configuration_token_type_id", model.NOTI_TOKEN_TYPE_LINE).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdateSwitchTokenId() error {

	if err := r.db.Table("configuration_notification_token").Where("configuration_token_type_id = ?", model.NOTI_TOKEN_TYPE_LINE).Update("configuration_token_type_id", model.NOTI_TOKEN_TYPE_LINE).Error; err != nil {
		return err
	}

	if err := r.db.Table("configuration_notification_token").Where("configuration_token_type_id = ?", model.NOTI_TOKEN_TYPE_TELEGRAM).Update("configuration_token_type_id", model.NOTI_TOKEN_TYPE_TELEGRAM).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) ClearNotificationCache() error {

	savecacheExternalNoti = nil
	savecacheBackofficeNoti = nil
	return nil
}

func (r repo) GetUserNewMemberForNoti() (*model.UserNewMemberCountResponse, error) {

	// yesturday := time.Now().UTC().Add(time.Hour*7).AddDate(0, 0, -1).Format("2006-01-02")
	today := time.Now().UTC().Add(time.Hour * 7).Format("2006-01-02")

	startDateAtBkk, err := r.ParseBodBkk(today)
	if err != nil {
		return nil, err
	}
	endDateAtBkk, err := r.ParseEodBkk(today)
	if err != nil {
		return nil, err
	}

	var list model.UserNewMemberCountResponse
	var userWithMemberCode int64

	query := r.db.Table("user")
	query = query.Select("id")
	query = query.Where("deleted_at IS NULL")
	query = query.Where("member_code IS NULL")
	query = query.Where("created_at >= ? ", startDateAtBkk)
	query = query.Where("created_at <=  ?", endDateAtBkk)

	if err := query.Count(&userWithMemberCode).Error; err != nil {
		return nil, err
	}

	var userWithoutMemberCode int64

	query2 := r.db.Table("user")
	query2 = query2.Select("id")
	query2 = query2.Where("deleted_at IS NULL")
	query2 = query2.Where("member_code IS NOT NULL")
	query2 = query2.Where("created_at >= ? ", startDateAtBkk)
	query2 = query2.Where("created_at <=  ?", endDateAtBkk)

	if err := query2.Count(&userWithoutMemberCode).Error; err != nil {
		return nil, err
	}

	list.UserWithMemberCode = userWithMemberCode
	list.UserWithoutMemberCode = userWithoutMemberCode
	list.Total = userWithMemberCode + userWithoutMemberCode

	return &list, nil
}

func (r repo) GetFirstTimeDepositForNoti(transId int64) (bool, error) {

	var count int64

	err := r.db.Table("bank_transaction").
		Where("id = ?", transId).
		Where("is_first_deposit = ?", 1).
		Count(&count).Error

	if err != nil {
		return false, err
	}

	return count > 0, nil
}

func (r repo) GetFirstTimeDepositATodayAmountForNoti() (int64, error) {

	todayDateTime := time.Now().UTC().Add(time.Hour * 7).Format("2006-01-02")

	startDateAtBkk, err := r.ParseBodBkk(todayDateTime)
	if err != nil {
		return 0, err
	}
	endDateAtBkk, err := r.ParseEodBkk(todayDateTime)
	if err != nil {
		return 0, err
	}

	var count int64

	err = r.db.Table("bank_transaction").
		Where("is_first_deposit = ?", 1).
		Where("transfer_at >= ?", startDateAtBkk).
		Where("transfer_at <= ?", endDateAtBkk).
		Count(&count).Error

	if err != nil {
		return 0, err
	}

	return count, nil
}

func (r repo) GetTransactionSummaryReportNotification(startDate time.Time, endDate time.Time) (*model.GetTransactionSummaryReportNotificationResponse, error) {

	var result model.GetTransactionSummaryReportNotificationResponse
	// bonus
	ActivityInfo := map[string]interface{}{
		"total_bonus_price": 0,
	}
	queryBouns := r.db.Table("user_transaction as tb_log")
	queryBouns = queryBouns.Select("SUM(tb_log.bonus_amount) as total_bonus_price")
	queryBouns = queryBouns.Where("tb_log.transfer_at BETWEEN ? AND ?", startDate, endDate)
	queryBouns = queryBouns.Where("tb_log.removed_at IS NULL")
	queryBouns = queryBouns.Where("tb_log.is_show = ?", true)
	if err := queryBouns.Take(&ActivityInfo).Error; err != nil {
		return &result, nil
	}

	// check is null
	if ActivityInfo["total_bonus_price"] != nil {
		bonusPrice, err := strconv.ParseFloat(ActivityInfo["total_bonus_price"].(string), 64)
		if err != nil {
			return &result, nil
		}
		result.TotalActivityBonusPrice = bonusPrice
	}

	// deposit
	depositInfo := map[string]interface{}{
		"total_deposit_price": 0,
		"total_deposit_count": 0,
	}
	var queryDeposit = r.db.Table("user_transaction as logs")
	queryDeposit = queryDeposit.Select("COUNT(*) as total_deposit_count, SUM(logs.credit_amount) as total_deposit_price")
	queryDeposit = queryDeposit.Where("logs.type_id = ?", 1)
	queryDeposit = queryDeposit.Where("logs.transfer_at BETWEEN ? AND ?", startDate, endDate)
	queryDeposit = queryDeposit.Where("logs.is_show = ?", true)
	queryDeposit = queryDeposit.Where("logs.removed_at IS NULL")
	if err := queryDeposit.Take(&depositInfo).Error; err != nil {
		return &result, nil
	}
	if depositInfo["total_deposit_price"] != nil {
		depositPrice, err := strconv.ParseFloat(depositInfo["total_deposit_price"].(string), 64)
		if err != nil {
			return &result, nil
		}
		result.TotalDepositPrice = math.Floor(depositPrice*100) / 100
	}
	result.TotalDepositUserCount = int64(depositInfo["total_deposit_count"].(int64))

	// withdraw
	withdrawInfo := map[string]interface{}{
		"total_withdraw_price": 0,
		"total_withdraw_count": 0,
	}
	queryWithdraw := r.db.Table("user_transaction as logs")
	queryWithdraw = queryWithdraw.Select("COUNT(*) as total_withdraw_count, SUM(logs.credit_amount) as total_withdraw_price")
	queryWithdraw = queryWithdraw.Where("logs.type_id = ?", 2)
	queryWithdraw = queryWithdraw.Where("logs.transfer_at BETWEEN ? AND ?", startDate, endDate)
	queryWithdraw = queryWithdraw.Where("logs.is_show = ?", true)
	queryWithdraw = queryWithdraw.Where("logs.removed_at IS NULL")
	if err := queryWithdraw.Take(&withdrawInfo).Error; err != nil {
		return &result, nil
	}
	if withdrawInfo["total_withdraw_price"] != nil {
		withdrawPrice, err := strconv.ParseFloat(withdrawInfo["total_withdraw_price"].(string), 64)
		if err != nil {
			return &result, nil
		}
		result.TotalWithdrawPrice = math.Floor(withdrawPrice*100) / 100
	}
	result.TotalWithdrawUserCount = int64(withdrawInfo["total_withdraw_count"].(int64))

	result.TotalBankProfit = (math.Round((result.TotalDepositPrice)*100) - math.Round((result.TotalWithdrawPrice)*100)) / 100

	// user create count
	var totalNewUserCount int64
	var queryTotalNewUserCount = r.db.Table("user as users")
	queryTotalNewUserCount = queryTotalNewUserCount.Select("COUNT(users.id)")
	queryTotalNewUserCount = queryTotalNewUserCount.Where("users.created_at BETWEEN ? AND ?", startDate, endDate)
	queryTotalNewUserCount = queryTotalNewUserCount.Where("users.deleted_at IS NULL")
	if err := queryTotalNewUserCount.Count(&totalNewUserCount).Error; err != nil {
		return &result, nil
	}
	result.TotalNewUserCount = totalNewUserCount

	// deposit more than 100> count
	var depositAboveHundredCount int64
	var queryDepositAboveHundred = r.db.Table("user_transaction as logs")
	queryDepositAboveHundred = queryDepositAboveHundred.Select("COUNT(logs.credit_amount)")
	queryDepositAboveHundred = queryDepositAboveHundred.Where("logs.credit_amount > ?", 100)
	queryDepositAboveHundred = queryDepositAboveHundred.Where("logs.type_id = ?", 1)
	queryDepositAboveHundred = queryDepositAboveHundred.Where("logs.transfer_at BETWEEN ? AND ?", startDate, endDate)
	queryDepositAboveHundred = queryDepositAboveHundred.Where("logs.is_show = ?", true)
	queryDepositAboveHundred = queryDepositAboveHundred.Where("logs.removed_at IS NULL")
	if err := queryDepositAboveHundred.Count(&depositAboveHundredCount).Error; err != nil {
		return &result, nil
	}
	result.TotalDepositAboveHundredCount = depositAboveHundredCount

	return &result, nil
}

func (r repo) GetBankTransactionSummaryReportNotification(startDate time.Time, endDate time.Time) ([]model.GetBankTransactionSummaryReportNotificationResponse, error) {

	// account bank deposit
	var bankTransactionSummaryDeposit []model.GetBankTransactionSummaryReportNotificationResponse
	selectedFieldBankTransactionDeposit := "ba.account_name, ba.account_number, b.code as bank_code"
	selectedFieldBankTransactionDeposit += ", SUM(CASE WHEN bt.transaction_type_id = 1 THEN bt.credit_amount ELSE 0 END) AS total_deposit_price"
	queryBankTransactionDeposit := r.db.Table("bank_transaction AS bt")
	queryBankTransactionDeposit = queryBankTransactionDeposit.Select(selectedFieldBankTransactionDeposit)
	queryBankTransactionDeposit = queryBankTransactionDeposit.Joins("LEFT JOIN bank_account AS ba ON bt.to_account_id = ba.id")
	queryBankTransactionDeposit = queryBankTransactionDeposit.Joins("LEFT JOIN bank AS b ON ba.bank_id = b.id")
	queryBankTransactionDeposit = queryBankTransactionDeposit.Where("bt.to_account_id IS NOT NULL")
	queryBankTransactionDeposit = queryBankTransactionDeposit.Where("bt.transfer_at BETWEEN ? AND ?", startDate, endDate)
	queryBankTransactionDeposit = queryBankTransactionDeposit.Where("bt.transaction_status_id = ?", model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED)
	queryBankTransactionDeposit = queryBankTransactionDeposit.Group("ba.account_name, ba.account_number, b.code")

	if err := queryBankTransactionDeposit.Scan(&bankTransactionSummaryDeposit).Error; err != nil {
		return nil, err
	}

	// account bank withdraw
	var bankTransactionSummaryWithdraw []model.GetBankTransactionSummaryReportNotificationResponse
	selectedFieldBankTransactionWithdraw := "ba.account_name, ba.account_number, b.code as bank_code"
	selectedFieldBankTransactionWithdraw += ", SUM(CASE WHEN bt.transaction_type_id = 2 THEN bt.credit_amount ELSE 0 END) AS total_withdraw_price"
	queryBankTransactionWithdraw := r.db.Table("bank_transaction AS bt")
	queryBankTransactionWithdraw = queryBankTransactionWithdraw.Select(selectedFieldBankTransactionWithdraw)
	queryBankTransactionWithdraw = queryBankTransactionWithdraw.Joins("LEFT JOIN bank_account AS ba ON bt.from_account_id = ba.id")
	queryBankTransactionWithdraw = queryBankTransactionWithdraw.Joins("LEFT JOIN bank AS b ON ba.bank_id = b.id")
	queryBankTransactionWithdraw = queryBankTransactionWithdraw.Where("bt.from_account_id IS NOT NULL")
	queryBankTransactionWithdraw = queryBankTransactionWithdraw.Where("bt.transfer_at BETWEEN ? AND ?", startDate, endDate)
	queryBankTransactionWithdraw = queryBankTransactionWithdraw.Where("bt.transaction_status_id = ?", model.TRANS_STATUS_WITHDRAW_SUCCESS)
	queryBankTransactionWithdraw = queryBankTransactionWithdraw.Group("ba.account_name, ba.account_number, b.code")

	if err := queryBankTransactionWithdraw.Scan(&bankTransactionSummaryWithdraw).Error; err != nil {
		return nil, err
	}

	mergedAccountBankMap := make(map[string]model.GetBankTransactionSummaryReportNotificationResponse)

	createKey := func(bankCode, accountName, accountNumber string) string {
		return bankCode + "|" + accountName + "|" + accountNumber
	}

	for _, d := range bankTransactionSummaryDeposit {
		key := createKey(d.BankCode, d.AccountName, d.AccountNumber)
		mergedAccountBankMap[key] = model.GetBankTransactionSummaryReportNotificationResponse{
			BankCode:           d.BankCode,
			AccountName:        d.AccountName,
			AccountNumber:      d.AccountNumber,
			TotalDepositPrice:  math.Floor(d.TotalDepositPrice*100) / 100,
			TotalWithdrawPrice: 0,
		}
	}

	for _, w := range bankTransactionSummaryWithdraw {
		key := createKey(w.BankCode, w.AccountName, w.AccountNumber)
		entry, exists := mergedAccountBankMap[key]
		if exists {
			entry.TotalWithdrawPrice = math.Floor(w.TotalWithdrawPrice*100) / 100
			mergedAccountBankMap[key] = entry
		} else {
			mergedAccountBankMap[key] = model.GetBankTransactionSummaryReportNotificationResponse{
				BankCode:           w.BankCode,
				AccountName:        w.AccountName,
				AccountNumber:      w.AccountNumber,
				TotalDepositPrice:  0,
				TotalWithdrawPrice: math.Floor(w.TotalWithdrawPrice*100) / 100,
			}
		}
	}

	// Step 4: Convert map to slice
	var result []model.GetBankTransactionSummaryReportNotificationResponse
	for _, v := range mergedAccountBankMap {
		result = append(result, v)
	}

	// payment deposit
	var paymentTransactionSummaryDeposit []model.GetBankTransactionSummaryReportNotificationResponse
	selectedFieldpaymentTransactionDeposit := "bt.to_account_name as account_name, bt.to_account_number as account_number"
	selectedFieldpaymentTransactionDeposit += ", SUM(CASE WHEN bt.transaction_type_id = 1 THEN bt.credit_amount ELSE 0 END) AS total_deposit_price"
	queryPaymentTransactionDeposit := r.db.Table("bank_transaction AS bt")
	queryPaymentTransactionDeposit = queryPaymentTransactionDeposit.Select(selectedFieldpaymentTransactionDeposit)
	queryPaymentTransactionDeposit = queryPaymentTransactionDeposit.Where("bt.to_account_id IS NULL")
	queryPaymentTransactionDeposit = queryPaymentTransactionDeposit.Where("bt.to_account_number IS NOT NULL")
	queryPaymentTransactionDeposit = queryPaymentTransactionDeposit.Where("bt.transfer_at BETWEEN ? AND ?", startDate, endDate)
	queryPaymentTransactionDeposit = queryPaymentTransactionDeposit.Where("bt.transaction_status_id = ?", model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED)
	queryPaymentTransactionDeposit = queryPaymentTransactionDeposit.Group("bt.to_account_name, bt.to_account_number")

	if err := queryPaymentTransactionDeposit.Scan(&paymentTransactionSummaryDeposit).Error; err != nil {
		return nil, err
	}

	// payment withdraw
	var paymentTransactionSummaryWithdraw []model.GetBankTransactionSummaryReportNotificationResponse
	selectedFieldPaymentTransactionWithdraw := "bt.from_account_name as account_name, bt.from_account_number as account_number"
	selectedFieldPaymentTransactionWithdraw += ", SUM(CASE WHEN bt.transaction_type_id = 2 THEN bt.credit_amount ELSE 0 END) AS total_withdraw_price"
	queryPaymentTransactionWithdraw := r.db.Table("bank_transaction AS bt")
	queryPaymentTransactionWithdraw = queryPaymentTransactionWithdraw.Select(selectedFieldPaymentTransactionWithdraw)
	queryPaymentTransactionWithdraw = queryPaymentTransactionWithdraw.Where("bt.from_account_id IS NULL")
	queryPaymentTransactionWithdraw = queryPaymentTransactionWithdraw.Where("bt.from_account_number IS NOT NULL")
	queryPaymentTransactionWithdraw = queryPaymentTransactionWithdraw.Where("bt.transfer_at BETWEEN ? AND ?", startDate, endDate)
	queryPaymentTransactionWithdraw = queryPaymentTransactionWithdraw.Where("bt.transaction_status_id = ?", model.TRANS_STATUS_WITHDRAW_SUCCESS)
	queryPaymentTransactionWithdraw = queryPaymentTransactionWithdraw.Group("bt.from_account_name, bt.from_account_number")

	if err := queryPaymentTransactionWithdraw.Scan(&paymentTransactionSummaryWithdraw).Error; err != nil {
		return nil, err
	}

	mergedPaymentMap := make(map[string]model.GetBankTransactionSummaryReportNotificationResponse)
	for _, d := range paymentTransactionSummaryDeposit {
		key := createKey("", d.AccountName, d.AccountNumber)
		mergedPaymentMap[key] = model.GetBankTransactionSummaryReportNotificationResponse{
			BankCode:           "",
			AccountName:        d.AccountName,
			AccountNumber:      d.AccountNumber,
			TotalDepositPrice:  math.Floor(d.TotalDepositPrice*100) / 100,
			TotalWithdrawPrice: 0,
		}
	}

	for _, w := range paymentTransactionSummaryWithdraw {
		key := createKey("", w.AccountName, w.AccountNumber)
		entry, exists := mergedPaymentMap[key]
		if exists {
			entry.TotalWithdrawPrice = math.Floor(w.TotalWithdrawPrice*100) / 100
			mergedPaymentMap[key] = entry
		} else {
			mergedPaymentMap[key] = model.GetBankTransactionSummaryReportNotificationResponse{
				BankCode:           "",
				AccountName:        w.AccountName,
				AccountNumber:      w.AccountNumber,
				TotalDepositPrice:  0,
				TotalWithdrawPrice: math.Floor(w.TotalWithdrawPrice*100) / 100,
			}
		}
	}

	// Step 4: Convert map to slice
	for _, v := range mergedPaymentMap {
		result = append(result, v)
	}

	return result, nil

}

// func (r repo) GetAffiliateSummaryReportNotification(startDate time.Time, endDate time.Time) ([]model.GetAffiliateSummaryReportNotificationResponse, error) {

// 	var response []model.GetAffiliateSummaryReportNotificationResponse

// 	selectField := "u.member_code, u.fullname, COUNT(a.id) AS total_downline_count"
// 	query := r.db.Table("affiliate AS a")
// 	query = query.Select(selectField)
// 	query = query.Joins("LEFT JOIN user AS u ON a.ref_id = u.id")
// 	query = query.Where("a.created_at BETWEEN ? AND ?", startDate, endDate)

// 	query = query.Group("u.member_code, u.fullname")

// 	if err := query.Scan(&response).Error; err != nil {
// 		return nil, err
// 	}

// 	return response, nil
// }

func (r repo) GetAllianceUserSummaryReportNotification(startDate time.Time, endDate time.Time) ([]model.GetAllianceUserSummaryReportNotification, error) {

	var listAlliance []model.GetAllianceUserSummaryReportNotification

	// SELECT //
	selectedFields := "users.id AS user_id, users.member_code AS member_code, users.fullname AS user_fullname"
	selectedFields += ", alliance_details.alias AS alliance_name"
	query := r.db.Table("user as users")
	query = query.Joins("LEFT JOIN user_alliance as alliance_details ON alliance_details.user_id = users.id")
	query = query.Select(selectedFields)
	query = query.Where("users.user_type_id = ?", model.USER_TYPE_ALLIANCE)
	if err := query.
		Where("users.deleted_at IS NULL").
		Scan(&listAlliance).
		Error; err != nil {
		return nil, err
	}

	// APPEND TOTAL DATA
	refIds := []int64{}
	for _, v := range listAlliance {
		refIds = append(refIds, v.UserId)
	}

	// startDate time.Time, endDate time.Time
	var userDepositTransaction []struct {
		RefId                      int64 `json:"ref_id"`
		CountDepositAmountMoreThan int64 `json:"count_deposit_amount_more_than"`
	}

	selectedFields2 := "u.ref_by AS ref_id, COUNT(logs.id) AS count_deposit_amount_more_than"
	query2 := r.db.Table("bank_transaction as logs").
		Select(selectedFields2).
		Joins("LEFT JOIN user AS u ON u.id = logs.user_id").
		Where("logs.transaction_type_id = ?", model.TRANSACTION_TYPE_DEPOSIT).
		Where("logs.transaction_status_id = ?", model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED).
		Where("logs.transfer_at BETWEEN ? AND ?", startDate, endDate).
		Where("u.ref_by IN (?)", refIds).
		Group("u.ref_by")

	if err := query2.Scan(&userDepositTransaction).Error; err != nil {
		return nil, err
	}

	// main user
	// {
	// 	"rows":
	// 	[
	// 		{
	// 			"ref_id": 89615,
	// 			"count_deposit_amount_more_than": 2
	// 		}
	// 	]
	// }

	// map
	// {
	// 	"rows":
	// 	[
	// 		{
	// 			"user_id": 89615,
	// 			"member_code": "zta68pk52001327",
	// 			"user_fullname": "ลองหวย",
	// 			"alliance_name": "test"
	// 		}
	// 	]
	// }

	// type GetAllianceUserSummaryReportNotification struct {
	// 	UserId                    int64  `json:"userId"`
	// 	MemberCode                string `json:"memberCode"`
	// 	UserFullname              string `json:"userFullname"`
	// 	AllianceName              string `json:"allianceName"`
	// 	TotalCountDownUserDeposit int64  `json:"totalCountUserDeposit"`
	// }

	var response []model.GetAllianceUserSummaryReportNotification
	for _, v := range userDepositTransaction {
		for _, user := range listAlliance {
			if v.RefId == user.UserId {
				response = append(response, model.GetAllianceUserSummaryReportNotification{
					UserId:                    user.UserId,
					MemberCode:                user.MemberCode,
					UserFullname:              user.UserFullname,
					AllianceName:              user.AllianceName,
					TotalCountDownUserDeposit: v.CountDepositAmountMoreThan,
				})
			}
		}
	}

	fmt.Println("response", helper.StructJson(response))

	return response, nil
}
