package repository

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"encoding/json"
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm"
)

func NewTurnoverRepository(db *gorm.DB) TurnoverRepository {
	return &repo{db}
}

type TurnoverRepository interface {
	GetDb() *gorm.DB
	// REF:User
	GetUserMemberInfoById(id int64) (*model.UserResponse, error)
	// AGENT
	GetAgentPlayerTurnover(query model.TurnoverUserCurrentAmountRequest) (*model.AgentTurnoverResponse, error)
	// Active/Current
	GetUserTurnoverInfo(userId int64) (*time.Time, float64, error)
	// Withdraw/Cutoff
	GetTurnoverWithdrawLog(logKey string) (*model.TurnoverWithdrawLog, error)
	GetUserTurnoverBeforeWithdrawInfo(userId int64, of_date string) (*model.TurnoverWithdrawLog, error)
	CreateTurnoverUserWithdraw(body model.TurnoverWithdrawLogCreateBody) (*int64, error)
	// Logs
	GetTurnoverUserStatementById(id int64) (*model.TurnoverUserStatementResponse, error)
	GetTurnoverUserStatementList(req model.TurnoverUserStatementListRequest) ([]model.TurnoverUserStatementResponse, int64, error)
	CreateTurnoverUserStatement(body model.TurnoverUserStatementCreateBody) (*int64, error)
	UpdateTurnoverUserStatement(id int64, body model.TurnoverUserStatementUpdateBody) error
	// REF-CRON_ACTION
	GetCronActionById(id int64) (*model.CronAction, error)
	GetCronActionByActionKey(actionKey string) (*model.CronAction, error)
	CreateCronAction(body model.CronActionCreateBody) (int64, error)
	SetSuccessCronAction(id int64, remark string) error
	SetFailCronAction(id int64, remark string) error
	// Promotion
	PromotionWebCheckTurnStatement(req model.PromotionWebCheckTurnStatementRequest) (*model.TurnoverUserStatementResponse, error)
	CreateTurnoverUserWithdrawLog(body model.CreateTurnoverUserWithdrawLog) (*int64, error)
	// CouponCash
	CouponCashUserCheckTurnStatement(req model.CouponCashUserCheckTurnStatementRequest) ([]model.TurnoverUserStatementResponse, error)
	CouponCashUserCheckTurnStatementDeleted(req model.CouponCashUserDeletedTurnStatementRequest) (*model.TurnoverUserStatementResponse, error)
	CheckTurnSuccessOnThisDay(req model.CheckTurnSuccessOnThisDayRequest) (*model.CheckTurnSuccessOnThisDayResponse, error)
	CheckAvalibleCouponAndPromtion(userId int64, typeCheck string) (*model.TurnoverUserStatementResponse, error)
	//other
	SumLastestTurnOver(userId int64) (*model.SumLastestTurnOverReponse, error)
	UpdateClearEveryTurnoverUserStatement(userId int64, body model.TurnoverUserStatementUpdateBody) error
	GetTurnOverStatementNotClear(userId int64) []model.GetTurnOverStatementNotClearResponse
	// SETTING
	GetTurnoverSetting() (*model.TurnoverSettingResponse, error)
	CreateTurnoverSetting(body model.TurnoverSettingCreateBody) (*int64, error)
	UpdateTurnoverSetting(id int64, body model.TurnoverSettingUpdateBody) error
	// NEW LIST TURN OVER
	GetUserTurnOverStartmentListPendingOnly(req model.GetUserTurnOverStartmentListRequest) ([]model.GetUserTurnOverStartmentList, int64, error)
	GetTodaySumUserPlayLogList(req model.UserTodayPlaylogListRequest) ([]model.UserTodaySumPlaylogReponse, int64, error)
	TurnOverPendingList(req model.GetTurnOverStartmentListRequest) ([]model.GetTurnOverStartmentListList, int64, error)

	// admin action
	CreateAdminAction(body model.AdminActionCreateBody) (*int64, error)
}

func (r repo) GetUserMemberInfoById(id int64) (*model.UserResponse, error) {

	var user model.UserResponse

	if err := r.db.Table("user").
		Select("id, username, member_code, fullname, credit").
		Where("id = ?", id).
		Take(&user).
		Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

func (r repo) GetUserTurnoverInfo(userId int64) (*time.Time, float64, error) {

	firstTurnOverInfo := map[string]interface{}{
		"id":            0,
		"user_id":       0,
		"start_turn_at": nil,
	}
	totalTurnoverInfo := map[string]interface{}{
		"user_id":               0,
		"total_turnover_amount": "0.0",
	}

	if err := r.db.Table("turnover_statement").
		Select("id, user_id, start_turn_at").
		Where("user_id = ?", userId).
		Order("id ASC").
		Take(&firstTurnOverInfo).
		Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, 0, nil
		}
		return nil, 0, err
	}

	if err := r.db.Table("turnover_statement").
		Select("user_id, SUM(total_turn_amount) as total_turnover_amount").
		Where("user_id = ?", userId).
		Where("status_id = ?", model.TURNOVER_STATEMENT_STATUS_PENDING).
		Group("user_id").
		Take(&totalTurnoverInfo).
		Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, 0, nil
		}
		return nil, 0, err
	}
	// convert string to float64
	totalTurnoverAmount, err := strconv.ParseFloat(totalTurnoverInfo["total_turnover_amount"].(string), 64)
	if err != nil {
		return nil, 0, err
	}
	var firstTurnOverAt *time.Time
	if firstTurnOverInfo["start_turn_at"] != nil {
		time := firstTurnOverInfo["start_turn_at"].(time.Time)
		firstTurnOverAt = &time
	}

	return firstTurnOverAt, totalTurnoverAmount, nil
}

func (r repo) GetTurnoverWithdrawLog(logKey string) (*model.TurnoverWithdrawLog, error) {

	var record model.TurnoverWithdrawLog

	if err := r.db.Table("turnover_withdraw_log").
		Select("*").
		Where("log_key = ?", logKey).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetUserTurnoverBeforeWithdrawInfo(userId int64, logKey string) (*model.TurnoverWithdrawLog, error) {

	var record model.TurnoverWithdrawLog

	if err := r.db.Table("turnover_withdraw_log").
		Select("*").
		Where("user_id = ?", userId).
		Where("log_key != ?", logKey). // WARNING: may has future date when you cant sort
		Order("id DESC").
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) CreateTurnoverUserWithdraw(body model.TurnoverWithdrawLogCreateBody) (*int64, error) {

	if err := r.db.Table("turnover_withdraw_log").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) GetTurnoverUserStatementById(id int64) (*model.TurnoverUserStatementResponse, error) {

	var record model.TurnoverUserStatementResponse

	selectedFields := "statements.id as id, statements.user_id as user_id, statements.type_id as type_id, statements.ref_type_id as ref_type_id, statements.status_id as status_id, statements.start_turn_amount as start_turn_amount"
	selectedFields += ", statements.promotion_name as promotion_name, statements.bonus_amount as bonus_amount"
	selectedFields += ", statements.start_turn_at as start_turn_at, statements.total_turn_amount as total_turn_amount, statements.end_turn_at as end_turn_at, statements.created_at as created_at, statements.updated_at as updated_at"
	selectedFields += ", types.name as type_name, statuses.name as status_name"
	if err := r.db.Table("turnover_statement as statements").
		Select(selectedFields).
		Joins("LEFT JOIN turnover_statement_type as types ON types.id = statements.type_id").
		Joins("LEFT JOIN turnover_statement_status as statuses ON statuses.id = statements.status_id").
		Where("statements.id = ?", id).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetTurnoverUserStatementList(req model.TurnoverUserStatementListRequest) ([]model.TurnoverUserStatementResponse, int64, error) {

	var list []model.TurnoverUserStatementResponse
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("turnover_statement as statements")
	count = count.Select("statements.id")
	count = count.Where("statements.user_id = ?", req.UserId)
	if req.IsActive != nil {
		if *req.IsActive {
			count = count.Where("statements.status_id = ?", model.TURNOVER_STATEMENT_STATUS_PENDING)
			count = count.Where("statements.total_turn_amount > ?", 0)
		} else {
			count = count.Where("statements.status_id != ?", model.TURNOVER_STATEMENT_STATUS_PENDING)
		}
	}
	if err = count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "statements.id as id, statements.user_id as user_id, statements.type_id as type_id, statements.ref_type_id as ref_type_id, statements.status_id as status_id, statements.start_turn_amount as start_turn_amount"
		selectedFields += ", statements.promotion_name as promotion_name, statements.bonus_amount as bonus_amount"
		selectedFields += ", statements.start_turn_at as start_turn_at, statements.total_turn_amount as total_turn_amount, statements.end_turn_at as end_turn_at, statements.created_at as created_at, statements.updated_at as updated_at"
		selectedFields += ", types.name as type_name, statuses.name as status_name"
		query := r.db.Table("turnover_statement as statements")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN turnover_statement_type as types ON types.id = statements.type_id")
		query = query.Joins("LEFT JOIN turnover_statement_status as statuses ON statuses.id = statements.status_id")
		query = query.Where("statements.user_id = ?", req.UserId)
		if req.IsActive != nil {
			if *req.IsActive {
				query = query.Where("statements.status_id = ?", model.TURNOVER_STATEMENT_STATUS_PENDING)
				query = query.Where("statements.total_turn_amount > ?", 0)
			} else {
				query = query.Where("statements.status_id != ?", model.TURNOVER_STATEMENT_STATUS_PENDING)
			}
		}
		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) CreateTurnoverUserStatement(body model.TurnoverUserStatementCreateBody) (*int64, error) {

	if err := r.db.Table("turnover_statement").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) UpdateTurnoverUserStatement(id int64, body model.TurnoverUserStatementUpdateBody) error {

	if err := r.db.Table("turnover_statement").Where("id = ?", id).Updates(body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdateClearEveryTurnoverUserStatement(userId int64, body model.TurnoverUserStatementUpdateBody) error {

	if err := r.db.Table("turnover_statement").Where("status_id = ?", model.TURNOVER_STATEMENT_STATUS_PENDING).Where("user_id = ?", userId).Updates(body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetAgentPlayerTurnover(query model.TurnoverUserCurrentAmountRequest) (*model.AgentTurnoverResponse, error) {

	var result model.AgentTurnoverResponse

	timeNow := time.Now()
	timeToInt := int(timeNow.Unix())

	agentName := os.Getenv("AGENT_NAME")
	sign := fmt.Sprintf("%s%s", agentName, query.PlayerName)

	agent := model.AgentTurnoverRequest{}
	agent.AgentName = agentName
	agent.PlayerName = query.PlayerName
	agent.Timestamp = timeToInt
	agent.Sign = helper.CreateSign(os.Getenv("AGENT_KEY"), sign, timeNow)
	agent.From = query.From
	agent.To = query.To
	// agent.Product = query.ProductIds ไม่ได้ใช้ ?? ไม่ส่งก็ได้ ??
	// debug.Println("agent", helper.StructJson(agent))
	url := fmt.Sprintf("%s/credit-transfer/xturnover", os.Getenv("TRANSFER_API"))
	res, err := helper.Post(url, agent)
	if err != nil {
		return nil, err
	}

	jsonRes, _ := json.Marshal(res)
	if err := json.Unmarshal(jsonRes, &result); err != nil {
		return nil, err
	}

	// todo {
	//   "message": "request frequency limit is 10 seconds (-15)",
	//   "data": null
	// }

	// Ex. ERROR
	// {
	// 	"Error": -15,
	// 	"Message": "request frequency limit is 10 seconds (-15)",
	// 	"Sign": "19c778320c66dbdc3325ab0a67f4f5478ed26be8bb66b2af874bc700a226c668",
	// 	"TimeStamp": 1696917941,
	// 	"TurnOver": 0,
	// 	"UTC": "2023-10-10T07:11:42.5809745Z"
	// }
	// debug.Println("result", helper.StructJson(result))
	return &result, nil
}

func (r repo) PromotionWebCheckTurnStatement(req model.PromotionWebCheckTurnStatementRequest) (*model.TurnoverUserStatementResponse, error) {

	// log.Println("req ==============", req)
	var result model.TurnoverUserStatementResponse
	var err error

	// SELECT //
	selectedFields := "statements.id as id, statements.user_id as user_id, statements.type_id as type_id, statements.ref_type_id as ref_type_id, statements.status_id as status_id, statements.start_turn_amount as start_turn_amount"
	selectedFields += ", statements.promotion_name as promotion_name, statements.bonus_amount as bonus_amount"
	selectedFields += ", statements.start_turn_at as start_turn_at, statements.total_turn_amount as total_turn_amount, statements.end_turn_at as end_turn_at, statements.created_at as created_at, statements.updated_at as updated_at"
	selectedFields += ", types.name as type_name, statuses.name as status_name"
	query := r.db.Table("turnover_statement as statements")
	query = query.Select(selectedFields)
	query = query.Joins("LEFT JOIN turnover_statement_type as types ON types.id = statements.type_id")
	query = query.Joins("LEFT JOIN turnover_statement_status as statuses ON statuses.id = statements.status_id")
	query = query.Where("statements.user_id = ?", req.UserId)
	query = query.Where("statements.status_id = ?", model.TURNOVER_STATEMENT_STATUS_PENDING)
	query = query.Where("statements.ref_type_id = ?", req.RefTypeId)

	if err = query.
		First(&result).
		Error; err != nil {
		return nil, err
	}

	return &result, nil
}

func (r repo) CreateTurnoverUserWithdrawLog(body model.CreateTurnoverUserWithdrawLog) (*int64, error) {

	if err := r.db.Table("turnover_withdraw_log").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) CouponCashUserCheckTurnStatement(req model.CouponCashUserCheckTurnStatementRequest) ([]model.TurnoverUserStatementResponse, error) {

	// log.Println("req ==============", req)
	var list []model.TurnoverUserStatementResponse
	var err error

	// SELECT //
	selectedFields := "statements.id as id, statements.user_id as user_id, statements.type_id as type_id, statements.ref_type_id as ref_type_id, statements.status_id as status_id, statements.start_turn_amount as start_turn_amount"
	selectedFields += ", statements.promotion_name as promotion_name, statements.bonus_amount as bonus_amount"
	selectedFields += ", statements.start_turn_at as start_turn_at, statements.total_turn_amount as total_turn_amount, statements.end_turn_at as end_turn_at, statements.created_at as created_at, statements.updated_at as updated_at"
	selectedFields += ", types.name as type_name, statuses.name as status_name"
	query := r.db.Table("turnover_statement as statements")
	query = query.Select(selectedFields)
	query = query.Joins("LEFT JOIN turnover_statement_type as types ON types.id = statements.type_id")
	query = query.Joins("LEFT JOIN turnover_statement_status as statuses ON statuses.id = statements.status_id")
	query = query.Where("statements.user_id = ?", req.UserId)
	query = query.Where("statements.status_id = ?", model.TURNOVER_STATEMENT_STATUS_PENDING)
	// RefTypeId []int64 `form:"refTypeId" binding:"required"`
	query = query.Where("statements.ref_type_id IN ?", req.RefTypeId)
	if err = query.
		Scan(&list).
		Error; err != nil {
		return nil, err
	}

	return list, nil
}

func (r repo) CouponCashUserCheckTurnStatementDeleted(req model.CouponCashUserDeletedTurnStatementRequest) (*model.TurnoverUserStatementResponse, error) {

	// log.Println("req ==============", req)
	var record model.TurnoverUserStatementResponse
	var err error

	// SELECT //
	selectedFields := "statements.id as id, statements.user_id as user_id, statements.type_id as type_id, statements.ref_type_id as ref_type_id, statements.status_id as status_id, statements.start_turn_amount as start_turn_amount"
	selectedFields += ", statements.promotion_name as promotion_name, statements.bonus_amount as bonus_amount"
	selectedFields += ", statements.start_turn_at as start_turn_at, statements.total_turn_amount as total_turn_amount, statements.end_turn_at as end_turn_at, statements.created_at as created_at, statements.updated_at as updated_at"
	selectedFields += ", types.name as type_name, statuses.name as status_name"
	query := r.db.Table("turnover_statement as statements")
	query = query.Select(selectedFields)
	query = query.Joins("LEFT JOIN turnover_statement_type as types ON types.id = statements.type_id")
	query = query.Joins("LEFT JOIN turnover_statement_status as statuses ON statuses.id = statements.status_id")
	query = query.Where("statements.user_id = ?", req.UserId)
	query = query.Where("statements.status_id = ?", model.TURNOVER_STATEMENT_STATUS_PENDING)
	// RefTypeId []int64 `form:"refTypeId" binding:"required"`
	query = query.Where("statements.ref_type_id IN ?", req.RefTypeId)
	if err = query.
		First(&record).
		Error; err != nil {
		return nil, err
	}

	return &record, nil
}

func (r repo) CheckTurnSuccessOnThisDay(req model.CheckTurnSuccessOnThisDayRequest) (*model.CheckTurnSuccessOnThisDayResponse, error) {

	var result model.CheckTurnSuccessOnThisDayResponse
	var err error

	// SELECT //
	selectedFields := "SUM(turnover_statement.start_turn_amount) as sum_total_turn_all_amount"
	selectedFields += ", SUM(CASE WHEN turnover_statement.type_id = 4 THEN turnover_statement.total_turn_amount ELSE 0 END) as sum_total_turn_game_amount"
	selectedFields += ", SUM(CASE WHEN turnover_statement.type_id = 5 THEN turnover_statement.total_turn_amount ELSE 0 END) as sum_total_turn_sport_amount"
	selectedFields += ", SUM(CASE WHEN turnover_statement.type_id = 6 THEN turnover_statement.total_turn_amount ELSE 0 END) as sum_total_turn_casino_amount"
	selectedFields += ", SUM(CASE WHEN turnover_statement.type_id = 13 THEN turnover_statement.total_turn_amount ELSE 0 END) as sum_total_turn_Pvp_amount"
	selectedFields += ", SUM(CASE WHEN turnover_statement.type_id = 14 THEN turnover_statement.total_turn_amount ELSE 0 END) as sum_total_turn_lotto_amount"
	selectedFields += ", SUM(CASE WHEN turnover_statement.type_id = 15 THEN turnover_statement.total_turn_amount ELSE 0 END) as sum_total_turn_financial_amount"

	query := r.db.Table("turnover_statement")
	query = query.Select(selectedFields)
	query = query.Where("turnover_statement.user_id = ?", req.UserId)

	startDateAtBkk, err := r.ParseBodBkk(req.StartDate)
	if err != nil {
		return nil, err
	}
	EndDateAtBkk, err := r.ParseEodBkk(req.StartDate)
	if err != nil {
		return nil, err
	}
	query = query.Where("turnover_statement.start_turn_at >= ?", startDateAtBkk)
	query = query.Where("turnover_statement.start_turn_at <= ?", EndDateAtBkk)
	//ไม่ต้องมี แล้ว เพราะ จะ เช็คล่าสุดของทุกการ เคลีย turn
	// query = query.Where("turnover_statement.type_id IN ?", []int64{model.TURN_SETTING_PLAY_ALL, model.TURN_SETTING_PLAY_GAME, model.TURN_SETTING_PLAY_SPORT, model.TURN_SETTING_PLAY_CASINO, model.TURN_SETTING_COUPON_ALL})
	query = query.Where("turnover_statement.status_id = ?", model.TURNOVER_STATEMENT_STATUS_COMPLETED)

	if err = query.
		Scan(&result).
		Error; err != nil {
		return nil, err
	}

	return &result, nil
}

func (r repo) CheckTurnSuccessOnThisDayBackup(req model.CheckTurnSuccessOnThisDayRequest) (*model.CheckTurnSuccessOnThisDayResponse, error) {

	var result model.CheckTurnSuccessOnThisDayResponse
	var err error

	// SELECT //
	selectedFields := "SUM(turnover_statement.start_turn_amount) as sum_start_turn_amount"
	query := r.db.Table("turnover_statement")
	query = query.Select(selectedFields)
	query = query.Where("turnover_statement.user_id = ?", req.UserId)

	startDateAtBkk, err := r.ParseBodBkk(req.StartDate)
	if err != nil {
		return nil, err
	}
	EndDateAtBkk, err := r.ParseEodBkk(req.StartDate)
	if err != nil {
		return nil, err
	}
	query = query.Where("turnover_statement.start_turn_at >= ?", startDateAtBkk)
	query = query.Where("turnover_statement.start_turn_at <= ?", EndDateAtBkk)
	//ไม่ต้องมี แล้ว เพราะ จะ เช็คล่าสุดของทุกการ เคลีย turn
	// query = query.Where("turnover_statement.type_id IN ?", []int64{model.TURN_SETTING_PLAY_ALL, model.TURN_SETTING_PLAY_GAME, model.TURN_SETTING_PLAY_SPORT, model.TURN_SETTING_PLAY_CASINO, model.TURN_SETTING_COUPON_ALL})
	query = query.Where("turnover_statement.status_id = ?", model.TURNOVER_STATEMENT_STATUS_COMPLETED)

	if err = query.
		Scan(&result).
		Error; err != nil {
		return nil, err
	}

	return &result, nil
}
func (r repo) CheckAvalibleCouponAndPromtion(userId int64, typeCheck string) (*model.TurnoverUserStatementResponse, error) {

	var result model.TurnoverUserStatementResponse
	var err error
	// SELECT //
	selectedFields := "statements.id as id, statements.user_id as user_id, statements.type_id as type_id, statements.ref_type_id as ref_type_id, statements.status_id as status_id, statements.start_turn_amount as start_turn_amount"
	selectedFields += ", statements.promotion_name as promotion_name, statements.bonus_amount as bonus_amount"
	selectedFields += ", statements.start_turn_at as start_turn_at, statements.total_turn_amount as total_turn_amount, statements.end_turn_at as end_turn_at, statements.created_at as created_at, statements.updated_at as updated_at"
	selectedFields += ", types.name as type_name, statuses.name as status_name"
	query := r.db.Table("turnover_statement as statements")
	query = query.Joins("LEFT JOIN turnover_statement_type as types ON types.id = statements.type_id")
	query = query.Joins("LEFT JOIN turnover_statement_status as statuses ON statuses.id = statements.status_id")
	query = query.Select(selectedFields)

	if typeCheck == "COUPON_CASH" {
		query = query.Where("statements.type_id IN ?", []int64{model.TURN_PROMOTION_SETTING_PLAY_ALL, model.TURN_PROMOTION_SETTING_PLAY_GAME, model.TURN_PROMOTION_SETTING_PLAY_SPORT, model.TURN_PROMOTION_SETTING_PLAY_CASINO, model.TURN_PROMOTION_SETTING_PLAY_P2P, model.TURN_PROMOTION_SETTING_PLAY_LOTTERY, model.TURN_PROMOTION_SETTING_PLAY_FINANCIAL})
	}
	if typeCheck == "PROMOTION" {
		query = query.Where("statements.type_id IN ?", []int64{model.TURN_SETTING_COUPON_ALL})
	}
	query = query.Where("statements.status_id = ?", model.TURNOVER_STATEMENT_STATUS_PENDING)
	query = query.Where("statements.total_turn_amount > ?", 0)
	query = query.Where("statements.user_id = ?", userId)

	if err = query.
		First(&result).
		Error; err != nil {
		return nil, err
	}

	return &result, nil
}

func (r repo) SumLastestTurnOverBackUp(userId int64) (*model.SumLastestTurnOverReponseBackup, error) {

	// get the lowest start_turn_at date first
	var lowestStartTurnAt time.Time
	if err := r.db.Table("turnover_statement").
		Select("start_turn_at").
		Where("user_id = ?", userId).
		Where("total_turn_amount > ?", 0).
		Order("start_turn_at ASC").
		Take(&lowestStartTurnAt).
		Error; err != nil {
		return nil, err
	}

	// fmt.Println("lowestStartTurnAt", lowestStartTurnAt)

	var result model.SumLastestTurnOverReponseBackup
	result.CheckOnDay = lowestStartTurnAt

	query := r.db.Table("turnover_statement")
	query = query.Select("SUM(start_turn_amount) as total_toplay_amount")
	query = query.Where("user_id = ?", userId)
	query = query.Where("start_turn_at >= ?", lowestStartTurnAt)
	query = query.Where("total_turn_amount > ?", 0)
	if err := query.
		Scan(&result).
		Error; err != nil {
		return nil, err
	}

	// fmt.Println("result", result)

	return &result, nil
}

func (r repo) SumLastestTurnOver(userId int64) (*model.SumLastestTurnOverReponse, error) {

	// get the lowest start_turn_at date first
	var lowestStartTurnAt time.Time
	if err := r.db.Table("turnover_statement").
		Select("start_turn_at").
		Where("user_id = ?", userId).
		Where("total_turn_amount > ?", 0).
		Order("start_turn_at ASC").
		Take(&lowestStartTurnAt).
		Limit(1).
		Error; err != nil {
		return nil, err
	}

	// fmt.Println("lowestStartTurnAt", lowestStartTurnAt)
	var response model.SumLastestTurnOverReponse
	response.CheckOnDay = lowestStartTurnAt

	var resultDetail []model.SumLastestTurnOverDetail
	selectedFields := "turnover_statement.start_turn_amount, turnover_statement.type_id"
	query := r.db.Table("turnover_statement")
	query = query.Select(selectedFields)
	query = query.Where("user_id = ?", userId)
	query = query.Where("start_turn_at >= ?", lowestStartTurnAt)
	query = query.Where("total_turn_amount > ?", 0)
	if err := query.
		Scan(&resultDetail).
		Error; err != nil {
		return nil, err
	}

	response.SumLastestTurnOverDetail = resultDetail

	return &response, nil
}

func (r repo) GetTurnOverStatementNotClear(userId int64) []model.GetTurnOverStatementNotClearResponse {

	var list []model.GetTurnOverStatementNotClearResponse
	query := r.db.Table("turnover_statement")
	query = query.Select("id, promotion_name, name")
	query = query.Where("user_id = ?", userId)
	query = query.Where("status_id = ?", model.TURNOVER_STATEMENT_STATUS_PENDING)
	query = query.Where("total_turn_amount > ?", 0)
	query = query.Where("end_turn_at IS NULL")
	query.Scan(&list)
	return list

}

func (r repo) GetTurnoverSetting() (*model.TurnoverSettingResponse, error) {

	var record model.TurnoverSettingResponse

	selectedFields := "*"
	if err := r.db.Table("turnover_setting").
		Select(selectedFields).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) CreateTurnoverSetting(body model.TurnoverSettingCreateBody) (*int64, error) {

	if err := r.db.Table("turnover_setting").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) UpdateTurnoverSetting(id int64, body model.TurnoverSettingUpdateBody) error {

	if err := r.db.Table("turnover_setting").Where("id = ?", id).Updates(body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetUserTurnOverStartmentListPendingOnly(req model.GetUserTurnOverStartmentListRequest) ([]model.GetUserTurnOverStartmentList, int64, error) {

	var list []model.GetUserTurnOverStartmentList
	var total int64
	var err error

	// fmt.Println("req ==============", req)
	selectedFields := "id, user_id, type_id, name, promotion_name, start_turn_amount, start_turn_at,end_turn_at"
	selectedFields += ", total_turn_amount, end_turn_at, status_id"
	count := r.db.Table("turnover_statement")
	count = count.Select("id").Where("user_id = ?", req.UserId)

	if req.IsPending == "PENDING" {
		if req.StartDate != "" {
			startDateAtBkk, err := r.ParseBodBkk(req.StartDate)
			if err != nil {
				return nil, total, err
			}
			count = count.Where("start_turn_at >= ?", startDateAtBkk)
		}
		if req.EndDate != "" {
			EndDateAtBkk, err := r.ParseEodBkk(req.EndDate)
			if err != nil {
				return nil, total, err
			}
			count = count.Where("start_turn_at <= ?", EndDateAtBkk)
		}
		count = count.Where("status_id = ?", model.TURNOVER_STATEMENT_STATUS_PENDING)
	} else if req.IsPending == "COMPELETED_AND_PENDING" {
		if req.StartDate != "" {
			startDateAtBkk, err := r.ParseBodBkk(req.StartDate)
			if err != nil {
				return nil, total, err
			}
			count = count.Where("start_turn_at >= ? AND status_id = ? OR start_turn_at >= ? AND status_id = ?", startDateAtBkk, model.TURNOVER_STATEMENT_STATUS_COMPLETED, startDateAtBkk, model.TURNOVER_STATEMENT_STATUS_PENDING)
			// count = count.Where("end_turn_at >= ? AND status_id = ? OR start_turn_at >= ? AND status_id = ?", startDateAtBkk, model.TURNOVER_STATEMENT_STATUS_COMPLETED, startDateAtBkk, model.TURNOVER_STATEMENT_STATUS_PENDING)
		}
		if req.EndDate != "" {
			EndDateAtBkk, err := r.ParseEodBkk(req.EndDate)
			if err != nil {
				return nil, total, err
			}
			count = count.Where("start_turn_at >= ? AND status_id = ? OR start_turn_at >= ? AND status_id = ?", EndDateAtBkk, model.TURNOVER_STATEMENT_STATUS_COMPLETED, EndDateAtBkk, model.TURNOVER_STATEMENT_STATUS_PENDING)
			// count = count.Where("end_turn_at >= ? AND status_id = ? OR start_turn_at >= ? AND status_id = ?", EndDateAtBkk, model.TURNOVER_STATEMENT_STATUS_COMPLETED, EndDateAtBkk, model.TURNOVER_STATEMENT_STATUS_PENDING)
		}
	} else {
		if req.StartDate != "" {
			startDateAtBkk, err := r.ParseBodBkk(req.StartDate)
			if err != nil {
				return nil, total, err
			}
			count = count.Where("start_turn_at >= ?", startDateAtBkk)
		}
		if req.EndDate != "" {
			EndDateAtBkk, err := r.ParseEodBkk(req.EndDate)
			if err != nil {
				return nil, total, err
			}
			count = count.Where("start_turn_at <= ?", EndDateAtBkk)
		}
	}

	if req.IsPending == "COMPLETED" {
		count = count.Where("status_id = ?", model.TURNOVER_STATEMENT_STATUS_COMPLETED)
	}

	if err = count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		query := r.db.Table("turnover_statement")
		query = query.Select(selectedFields).Where("user_id = ?", req.UserId)

		if req.IsPending == "PENDING" {
			if req.StartDate != "" {
				startDateAtBkk, err := r.ParseBodBkk(req.StartDate)
				if err != nil {
					return nil, total, err
				}
				query = query.Where("start_turn_at >= ?", startDateAtBkk)
			}
			if req.EndDate != "" {
				EndDateAtBkk, err := r.ParseEodBkk(req.EndDate)
				if err != nil {
					return nil, total, err
				}
				query = query.Where("start_turn_at <= ?", EndDateAtBkk)
			}
			query = query.Where("status_id = ?", model.TURNOVER_STATEMENT_STATUS_PENDING)
		} else if req.IsPending == "COMPELETED_AND_PENDING" {
			if req.StartDate != "" {
				startDateAtBkk, err := r.ParseBodBkk(req.StartDate)
				if err != nil {
					return nil, total, err
				}
				// query = query.Where("end_turn_at >= ? AND status_id = ? OR start_turn_at >= ? AND status_id = ?", startDateAtBkk, model.TURNOVER_STATEMENT_STATUS_COMPLETED, startDateAtBkk, model.TURNOVER_STATEMENT_STATUS_PENDING)
				query = query.Where("start_turn_at >= ? AND status_id = ? OR start_turn_at >= ? AND status_id = ?", startDateAtBkk, model.TURNOVER_STATEMENT_STATUS_COMPLETED, startDateAtBkk, model.TURNOVER_STATEMENT_STATUS_PENDING)
			}
			if req.EndDate != "" {
				EndDateAtBkk, err := r.ParseEodBkk(req.EndDate)
				if err != nil {
					return nil, total, err
				}
				// query = query.Where("end_turn_at >= ? AND status_id = ? OR start_turn_at >= ? AND status_id = ?", EndDateAtBkk, model.TURNOVER_STATEMENT_STATUS_COMPLETED, EndDateAtBkk, model.TURNOVER_STATEMENT_STATUS_PENDING)
				query = query.Where("start_turn_at >= ? AND status_id = ? OR start_turn_at >= ? AND status_id = ?", EndDateAtBkk, model.TURNOVER_STATEMENT_STATUS_COMPLETED, EndDateAtBkk, model.TURNOVER_STATEMENT_STATUS_PENDING)
			}
		} else {
			if req.StartDate != "" {
				startDateAtBkk, err := r.ParseBodBkk(req.StartDate)
				if err != nil {
					return nil, total, err
				}
				query = query.Where("start_turn_at >= ?", startDateAtBkk)
			}
			if req.EndDate != "" {
				EndDateAtBkk, err := r.ParseEodBkk(req.EndDate)
				if err != nil {
					return nil, total, err
				}
				query = query.Where("start_turn_at <= ?", EndDateAtBkk)
			}
		}

		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}

	return list, total, nil
}

func (r repo) TurnOverPendingList(req model.GetTurnOverStartmentListRequest) ([]model.GetTurnOverStartmentListList, int64, error) {
	var list []model.GetTurnOverStartmentListList
	var total int64
	var err error

	selectedFields := "user.id as user_id, user.member_code as member_code, user.fullname as fullname, user.phone as phone"
	selectedFields += ", SUM(turnover_statement.start_turn_amount) as sum_start_turn_amount"

	count := r.db.Table("turnover_statement")
	count = count.Select("user.id")
	count = count.Joins("LEFT JOIN user ON user.id = turnover_statement.user_id")
	count = count.Where("turnover_statement.status_id = ?", model.TURNOVER_STATEMENT_STATUS_PENDING)
	count = count.Where("turnover_statement.total_turn_amount > ?", 0)
	if req.StartDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.StartDate)
		if err != nil {
			return nil, total, err
		}
		count = count.Where("turnover_statement.start_turn_at >= ?", startDateAtBkk)
	}
	if req.EndDate != "" {
		EndDateAtBkk, err := r.ParseEodBkk(req.EndDate)
		if err != nil {
			return nil, total, err
		}
		count = count.Where("turnover_statement.start_turn_at <= ?", EndDateAtBkk)
	}
	if req.Search != "" {
		count = count.Where("user.member_code LIKE ? OR user.fullname LIKE ? OR user.phone LIKE ?", "%"+req.Search+"%", "%"+req.Search+"%", "%"+req.Search+"%")
	}

	count = count.Group("user.id, user.member_code, user.fullname, user.phone")
	if err = count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		query := r.db.Table("turnover_statement")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN user ON user.id = turnover_statement.user_id")
		query = query.Where("turnover_statement.status_id = ?", model.TURNOVER_STATEMENT_STATUS_PENDING)
		query = query.Where("turnover_statement.total_turn_amount > ?", 0)
		if req.StartDate != "" {
			startDateAtBkk, err := r.ParseBodBkk(req.StartDate)
			if err != nil {
				return nil, total, err
			}
			query = query.Where("turnover_statement.start_turn_at >= ?", startDateAtBkk)
		}
		if req.EndDate != "" {
			EndDateAtBkk, err := r.ParseEodBkk(req.EndDate)
			if err != nil {
				return nil, total, err
			}
			query = query.Where("turnover_statement.start_turn_at <= ?", EndDateAtBkk)
		}
		if req.Search != "" {
			query = query.Where("user.member_code LIKE ? OR user.fullname LIKE ? OR user.phone LIKE ?", "%"+req.Search+"%", "%"+req.Search+"%", "%"+req.Search+"%")
		}

		query = query.Group("user.id, user.member_code, user.fullname, user.phone")
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}

	return list, total, nil
}
