package repository

import (
	"cybergame-api/model"

	"gorm.io/gorm"
)

func NewUserTierRepository(db *gorm.DB) UserTierRepository {
	return &repo{db}
}

type UserTierRepository interface {
	GetUserListTierSetting() (*model.UserListTierSettingResponse, error)
	GetTierSetting(name string) (*model.UserTierSetting, error)
	GetUserTierSettingById(id int64) (*model.UserTierSetting, error)
	GetUserTierSetting(name string) (*model.UserTierSetting, error)
	GetUserTierSettingList(req model.UserTierSettingListRequest) ([]model.UserTierSettingItem, int64, error)
	CreateUserTierSettingBulk(body []model.UserTierSettingCreateBody) error
	UpdateUserTierSetting(id int64, body model.UserTierSettingUpdateBody) error
	UpdateUserTierDepositSettingBySortId(sortOrder int64, body model.UserTierSettingUpdateBody) error
	UpdateUserTierTurnOverSettingBySortId(sortOrder int64, body model.UserTierSettingUpdateBody) error
	DeleteUserTierSettingByIds(ids []int64) error
	// UserData
	IncreaseUserTierDepositAmount(userId int64, amount float64) error
	IncreaseUserTierTurnoverAmount(userId int64, amount float64) error
}

func (r repo) GetUserListTierSetting() (*model.UserListTierSettingResponse, error) {

	var result model.UserListTierSettingResponse
	var list []model.UserTierSetting
	// ENV enable Tier Setting
	// tierSetting, err := s.repo.GetTierSetting("is_deposit_tier_setting")
	// if err == nil && tierSetting.Id != 0 {
	// 	if tierSetting.FromValue == 1 {
	// 		result.IsEnvEnabled = true
	// 	}
	// }

	// if result.IsEnvEnabled {
	// 	tierSetting2, err := s.repo.GetTierSetting("is_deposit_tier_setting")
	// 	if err == nil && tierSetting2.FromValue == 1 {
	// 		result.IsEnvEnabled = true
	// 	}
	// 	tierSetting3, err := s.repo.GetTierSetting("is_turnover_tier_setting")
	// 	if err == nil && tierSetting3.FromValue == 1 {
	// 		result.IsEnvEnabled = true
	// 	}
	// }
	if err := r.db.Table("user_tier_setting").Where("type = ?", model.USERTIER_TYPE_ENV).Scan(&list).Error; err != nil {
		return nil, err
	}

	for _, item := range list {
		if item.Name == "use_speciel_setting" {
			if item.FromValue == 1 {
				result.IsEnvEnabled = true
			} else {
				result.IsEnvEnabled = false
				result.IsDepositEnabled = false
				result.IsTurnOverEnabled = false
				break
			}
		} else if item.Name == "is_deposit_tier_setting" {
			if item.FromValue == 1 {
				result.IsDepositEnabled = true
			}
		} else if item.Name == "is_turnover_tier_setting" {
			if item.FromValue == 1 {
				result.IsTurnOverEnabled = true
			}
		}
	}

	return &result, nil
}

func (r repo) GetTierSetting(name string) (*model.UserTierSetting, error) {

	var record model.UserTierSetting

	if err := r.db.Table("user_tier_setting").
		Where("type = ?", model.USERTIER_TYPE_ENV).
		Where("name = ?", name).
		Limit(1).
		Scan(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetUserTierSettingById(id int64) (*model.UserTierSetting, error) {

	var record model.UserTierSetting

	if err := r.db.Table("user_tier_setting").
		Where("id = ?", id).
		Limit(1).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetUserTierSetting(name string) (*model.UserTierSetting, error) {

	var record model.UserTierSetting

	if err := r.db.Table("user_tier_setting").Where("type = ?", model.USERTIER_TYPE_ENV).Where("name = ?", name).Limit(1).Take(&record).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// create default record
			sortOrder := 1
			if name == "is_deposit_tier_setting" {
				sortOrder = 2
			} else if name == "is_turnover_tier_setting" {
				sortOrder = 3
			}
			createBody := model.UserTierSettingCreateBody{
				Type:      model.USERTIER_TYPE_ENV,
				SortOrder: sortOrder,
				Name:      name,
				FromValue: 0,
				ToValue:   0,
			}
			if err := r.db.Table("user_tier_setting").Create(&createBody).Error; err != nil {
				return nil, err
			}
			// reget record
			if err := r.db.Table("user_tier_setting").Where("type = ?", model.USERTIER_TYPE_ENV).Where("name = ?", name).Limit(1).Take(&record).Error; err != nil {
				return nil, err
			}
			return &record, nil
		}
		return nil, err
	}
	return &record, nil
}

func (r repo) GetUserTierSettingList(req model.UserTierSettingListRequest) ([]model.UserTierSettingItem, int64, error) {

	var list []model.UserTierSettingItem
	var total int64

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("user_tier_setting AS tb_setting")
	count = count.Select("tb_setting.id")
	if req.Type != "" {
		count = count.Where("tb_setting.type = ?", req.Type)
	}
	if err := count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "*"
		query := r.db.Table("user_tier_setting AS tb_setting")
		query = query.Select(selectedFields)
		if req.Type != "" {
			query = query.Where("tb_setting.type = ?", req.Type)
		}
		// Sort by ANY //
		query = query.Order("tb_setting.sort_order ASC")
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}

	return list, total, nil
}

func (r repo) CreateUserTierSettingBulk(body []model.UserTierSettingCreateBody) error {

	if err := r.db.Table("user_tier_setting").Create(&body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdateUserTierSetting(id int64, body model.UserTierSettingUpdateBody) error {

	if err := r.db.Table("user_tier_setting").Where("id = ?", id).Where("type = ?", model.USERTIER_TYPE_ENV).Updates(body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdateUserTierDepositSettingBySortId(sortOrder int64, body model.UserTierSettingUpdateBody) error {

	if err := r.db.Table("user_tier_setting").Where("sort_order = ?", sortOrder).Where("type = ?", model.USERTIER_TYPE_DEPOSIT).Updates(body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdateUserTierTurnOverSettingBySortId(sortOrder int64, body model.UserTierSettingUpdateBody) error {

	if err := r.db.Table("user_tier_setting").Where("sort_order = ?", sortOrder).Where("type = ?", model.USERTIER_TYPE_TURNOVER).Updates(body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) DeleteUserTierSettingByIds(ids []int64) error {

	if err := r.db.Table("user_tier_setting").Where("id IN (?)", ids).Delete(&model.UserTierSetting{}).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) IncreaseUserTierDepositAmount(userId int64, amount float64) error {

	// check Setting enabled
	setting, err := r.GetUserTierSetting("is_deposit_tier_setting")
	if err != nil {
		return err
	}
	if setting.FromValue == 0 {
		return nil
	}

	// UPDATE OR INSERT //
	var record model.UserTierData
	if err := r.db.Table("user_tier_data").Select("id").Where("user_id = ?", userId).Limit(1).Take(&record).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			createBody := model.UserTierDataCreateBody{
				UserId:       userId,
				TotalDeposit: amount,
			}
			if err := r.db.Table("user_tier_data").Create(&createBody).Error; err != nil {
				return err
			}
			return nil
		}
	}

	if err := r.db.Table("user_tier_data").Where("user_id = ?", userId).Update("total_deposit", gorm.Expr("total_deposit + ?", amount)).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) IncreaseUserTierTurnoverAmount(userId int64, amount float64) error {

	// check Setting enabled
	setting, err := r.GetUserTierSetting("is_turnover_tier_setting")
	if err != nil {
		return err
	}
	if setting.FromValue == 0 {
		return nil
	}

	// UPDATE OR INSERT //
	var record model.UserTierData
	if err := r.db.Table("user_tier_data").Select("id").Where("user_id = ?", userId).Limit(1).Take(&record).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			createBody := model.UserTierDataCreateBody{
				UserId:    userId,
				TotalTurn: amount,
			}
			if err := r.db.Table("user_tier_data").Create(&createBody).Error; err != nil {
				return err
			}
			return nil
		}
	}

	if err := r.db.Table("user_tier_data").Where("user_id = ?", userId).Update("total_turn", gorm.Expr("total_turn + ?", amount)).Error; err != nil {
		return err
	}
	return nil
}
