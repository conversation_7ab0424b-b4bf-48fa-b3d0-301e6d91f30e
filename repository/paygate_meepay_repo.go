package repository

import (
	"bytes"
	"crypto/sha256"
	"cybergame-api/helper"
	"cybergame-api/model"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm"
)

func NewMeepayRepository(db *gorm.DB) MeepayRepository {
	return &repo{db}
}

type MeepayRepository interface {
	GetDb() *gorm.DB
	// // SystemLog
	CreatePaygateSystemLog(body model.PaygateSystemLogCreateBody) (*int64, error)
	// UpdatePaygateSystemLog(id int64, body model.PaygateSystemLogUpdateBody) error
	// REF-PAYGATE
	GetRawMeepayPendingDepositOrderById(id int64) (*model.MeepayOrderResponse, error)
	GetPaygateAccountByProviderId(id int64) (*model.PaygateAccountResponse, error)
	// Meepay-REMOTE
	// MeepayGetToken(setting model.PaygateAccountResponse) (*model.MeepayTokenCreateRemoteResponse, error)
	MeepayDeposit(setting model.PaygateAccountResponse, req model.MeepayDepositCreateRemoteRequest) (*model.MeepayDepositCreateRemoteResponse, error)
	MeepayWithdraw(setting model.PaygateAccountResponse, req model.MeepayWithdrawCreateRemoteRequest) (*model.MeepayWithdrawCreateRemoteResponse, error)
	MeepayCheckBalance(setting model.PaygateAccountResponse) (*model.MeepayCheckBalanceRemoteResponse, error)
	// MeepayGetOrder(setting model.PaygateAccountResponse, orderNo string) (*model.MeepayGetOrderRemoteResponse, error)
	// MeepayCancelDeposit(setting model.PaygateAccountResponse, orderNo string) (*model.MeepayCancelDepositRemoteResponse, error)
	// MeepayCreateCustomer(setting model.PaygateAccountResponse, req model.MeepayCustomerCreateRemoteRequest) (*model.MeepayCustomerCreateRemoteResponse, error)
	// MeepayUpdateCustomer(setting model.PaygateAccountResponse, req model.MeepayCustomerUpdateRemoteRequest) (*model.MeepayCustomerUpdateRemoteResponse, error)
	// Meepay-Decrypt
	// MeepayDecryptRepayDespositPayload(setting model.PaygateAccountResponse, payload model.MeepayWebhookEncryptPayload) (*model.MeepayWebhookDepositResponse, error)
	// Meepay-DB
	CreateMeepayWebhook(body model.MeepayWebhookCreateBody) (*int64, error)
	GetDbMeepayOrderList(req model.MeepayOrderListRequest) ([]model.MeepayOrderResponse, int64, error)
	GetDbMeepayOrderById(id int64) (*model.MeepayOrderResponse, error)
	GetDbMeepayWithdrawOrderByRefId(refId int64) (*model.MeepayOrderResponse, error)
	CreateDbMeepayOrder(body model.MeepayOrderCreateBody) (*int64, error)
	CheckMeepayDepositOrderInLast5Minutes(userId int64) (*model.MeepayOrderResponse, error)
	UpdateDbMeepayOrderError(id int64, remark string) error
	UpdateDbMeepayOrder(id int64, body model.MeepayOrderUpdateBody) error
	ApproveDbMeepayOrder(id int64, webhookStatus string) error
	UpdateMeepayOrderBankSucess(id int64, transId int64, actionBy int64) error
	// Token
	// GetDbMeepayAccessToken() (*model.MeepayToken, error)
	// CreateDbMeepayAccessToken(body model.MeepayTokenCreateBody) (*int64, error)
	// Customer
	// GetMeepayCustomerById(id int64) (*model.MeepayCustomerResponse, error)
	// GetMeepayCustomerByUserId(userId int64) (*model.MeepayCustomerResponse, error)
	// // CheckMeepayCustomerByUserId(user model.UserBankDetailBody) (*model.MeepayCustomerResponse, error)
	// GetMeepayCustomerList(req model.MeepayCustomerListRequest) ([]model.MeepayCustomerResponse, int64, error)
	// CreateMeepayCustomer(body model.MeepayCustomerCreateBody) (*int64, error)
	// UpdateMeepayCustomer(id int64, body model.MeepayCustomerUpdateBody) error
	// DeleteMeepayCustomer(id int64) error
	// REF-USER
	GetUserById(id int64) (*model.UserResponse, error)
	// REF-MemberCode
	GetUserForGenMember(id int64) (*model.UserDetail, error)
	GetAgentInfo() (*model.AgentInfo, error)
	IncrementTotal() error
	UpdateMemberCode(userId int64, data model.UserUpdateMemberAndRef) error
	AgcRegister(data model.AgcRegister) error
	AmbRegister(data model.AmbRegister) error
	GetMemberById(id int64) (*model.Member, error)
	IsFirstDeposit(userId int64) bool
	UpdateGenMemberCodeToAffilate(userId int64) error
	CreateAffiliateMember(refBy int64, userId int64) error
	// CronjobDeleteMeepayWebhook() error
	// REF-RC
	GetRaceActionIdByActionKey(actionKey string) (*int64, error)
	CreateRaceCondition(body model.RaceActionCreateBody) (int64, error)
	// REF-BankTransaction-Deposit
	GetUserBankDetailById(id int64) (*model.UserBankDetailBody, error)
	GetDepositCurrentProcessingUserPromotion(userId int64) (*model.PromotionWebUserByUserIdResponse, error)
	InsertBankTransaction(data model.BankTransactionCreateBody) (*int64, error)
	IncreaseUserCredit(body model.UserTransactionCreateRequest) (*model.UserTransactionCreateResponse, error)
	UpdateDepositTransactionStatusFromAgent(transId int64, body model.UserTransactionCreateResponse) error
	UpdateAutoProcessTimer(timer string, id int64) error
	// ConfirmDeposit
	GetBankTransactionById(id int64) (*model.BankTransaction, error)
	CreateTransactionAction(data model.CreateBankTransactionActionBody) (*int64, error)
	ConfirmPendingDepositTransaction(id int64, body model.BankDepositTransactionConfirmBody) error
	RollbackTransactionAction(actionId int64) error
	// Order
	GetPendingMeepayDepositOrder(id int64) (*model.MeepayOrderResponse, error)
	GetLastestMeepayDepositOrderByUserId(userId int64) (*model.MeepayOrderResponse, error)
	// REF-BANK
	GetLastestBankAdminManualDepositTransaction(userId int64) (*model.BankTransaction, error)
	ShowUserTransaction(req model.UserTransactionShowUpdate) error
	UpdateTransactionStatusTransferingToSuccess(id int64, secondUsed string) error
	RollbackTransactionStatusTransferingToConfirmed(id int64) error
	CreateBankTransactionExternalDetail(body model.BankTransactionExternalDetailCreateRequest) (int64, error)
	UpdateUserTransactionStatus(body model.UpdateUserTransactionStatusRequest) error
	GetUserWithdrawCreditTransactionByRefId(transactionId int64, creditAmount float64) (*model.UserTransaction, error)
	// REF-SysLog
	CreateSystemLog(body model.SystemLogCreateBody) (*int64, error)
	// REF-WebConfiguration
	GetWebConfiguration() (*model.GetWebConfigurationBody, error)
	// [TIER]
	IncreaseUserTierDepositAmount(userId int64, amount float64) error
}

func (r repo) MeepayCreateSignature(clientId string, reference string, clientSecret string) string {

	// ค่าแฮช (Hash) ที่ใช้ยืนยันความถูกต้องของข้อมูล
	// Hash โดยใช้ HMAC SHA256 clientId + reference + clientSecret
	// message := fmt.Sprintf("%s%s%s", clientId, reference, clientSecret)

	// // Generate the HMAC SHA-256 signature using the Client Secret
	// h := hmac.New(sha256.New, []byte(clientSecret))
	// h.Write([]byte(message))
	// // encode to hex string
	// return fmt.Sprintf("%x", h.Sum(nil))

	// Concatenate clientId and reference to create the message
	message := clientId + reference + clientSecret
	fmt.Println("MeepayCreateSignature.message=", message)
	// Create a new HMAC using SHA256 and the clientSecret as the key
	// h := hmac.New(sha256.New, []byte(clientSecret))
	// h.Write([]byte(message))

	// encode sha256 without hmac
	h := sha256.New()
	h.Write([]byte(message))

	// Get the final HMAC hash and encode it to hexadecimal string
	return hex.EncodeToString(h.Sum(nil))
}

func (r repo) MeepayDeposit(setting model.PaygateAccountResponse, req model.MeepayDepositCreateRemoteRequest) (*model.MeepayDepositCreateRemoteResponse, error) {

	// ผมอยากเก็บ default เอาไว้กัน Adminตั้งค่าต่ำกว่า payment
	if req.Amount < model.MEEPAY_DEFMIN_DEPOSIT_AMOUNT || req.Amount > model.MEEPAY_DEFMAX_DEPOSIT_AMOUNT {
		log.Println("req.Amount", req.Amount)
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}
	if setting.PaymentDepositMinimum > 0 && req.Amount < setting.PaymentDepositMinimum {
		log.Println("req.Amount", req.Amount, "setting.PaymentDepositMinimum", setting.PaymentDepositMinimum)
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}
	if setting.PaymentDepositMaximum > 0 && req.Amount > setting.PaymentDepositMaximum {
		log.Println("req.Amount", req.Amount, "setting.PaymentDepositMaximum", setting.PaymentDepositMaximum)
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}
	// PrerequisitesMeepay
	if setting.ApiEndPoint == "" || setting.MerchantId == "" || setting.SecretKey == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}

	apiEndPoint := setting.ApiEndPoint
	sign := r.MeepayCreateSignature(setting.MerchantId, req.Reference, setting.SecretKey)
	fmt.Println("MeepayDeposit.MeepayCreateSignature=", sign)
	// Complete Request 6/9+3/9
	req.ClientId = setting.MerchantId
	req.ClientSecret = setting.SecretKey
	req.Hash = sign

	// curl --location 'https://api.meepay.me/v1/charge' \
	// --header 'Content-Type: application/json' \
	// --data '{
	// 	"clientId": "<clientId>",
	// 	"clientSecret": "<clientSecret>",
	// 	"amount": 50,
	// 	"reference": "ORDER12345",
	// 	"callbackUrl": "https://yourdomain.com/callback",
	// 	"bankId": "***********",
	// 	"bankName": "ชื่อ นามสกุล",
	// 	"bankCode": "014",
	// 	"hash": "<hash>"
	// }'
	epUrl := fmt.Sprintf("%s/charge", apiEndPoint)
	fmt.Println("MeepayDeposit url ------> ", epUrl)

	// SysLog
	if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
		Name:   "MeepayDeposit",
		Status: "CALL",
		JsonReq: helper.StructJson(map[string]interface{}{
			"epUrl":      epUrl,
			"merchantId": setting.MerchantId,
			"req":        req,
		}),
	}); err != nil {
		log.Println("MeepayDeposit.CreatePaygateSystemLog.ERROR", err)
	}

	client := &http.Client{}
	client.Timeout = 30 * time.Second

	jsonBody, _ := json.Marshal(req)
	payload := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest("POST", epUrl, payload)
	reqExternal.Header.Set("Content-Type", "application/json")
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseBody, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}

	fmt.Println("MeepayDeposit.resp.Body", string(responseBody))

	if response.StatusCode != 200 && response.StatusCode != 201 {
		log.Println("MeepayDeposit.NOT_OK, StatusCode=", response.StatusCode)
		log.Println("MeepayDeposit.NOT_OK, responseBody=", string(responseBody))
		var errMsg2 model.MeepayErrorRemoteResponse
		errJson2 := json.Unmarshal(responseBody, &errMsg2)
		if errJson2 == nil {
			return nil, fmt.Errorf("invalid status : %s", errMsg2.Message)
		} else {
			log.Println("MeepayDeposit.NOT_OK.Unmarshal.errJson2 ------> ", errJson2)
		}
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	}

	var remoteResp model.MeepayDepositCreateRemoteResponse
	errJson := json.Unmarshal(responseBody, &remoteResp)
	if errJson != nil {
		log.Println("MeepayDeposit resp.Body ------> ", string(responseBody))
		log.Println("MeepayDeposit.Unmarshal.Err ------> ", errJson)
		// TRY-3-err2
		var errMsg3 model.MeepayErrorRemoteResponse
		errJson3 := json.Unmarshal(responseBody, &errMsg3)
		if errJson3 == nil {
			return nil, errors.New(errMsg3.Message)
		} else {
			log.Println("MeepayDeposit.Unmarshal.errJson3 ------> ", errJson3)
		}
		return nil, errors.New("INVALID_UNMARSHAL_JSON")
	}

	fmt.Println("MeepayDepositCreateRemoteResponse.remoteResp", remoteResp)

	if strings.ToLower(remoteResp.Status) != "succeeded" {
		log.Println("MeepayDepositCreateRemoteResponse.remoteResp", helper.StructJson(remoteResp))
		// SysLog
		if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "MeepayDeposit",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(req),
			JsonResponse: helper.StructJson(map[string]interface{}{"resp": string(responseBody), "remoteResp": helper.StructJson(remoteResp)}),
		}); err != nil {
			log.Println("MeepayDeposit.CreatePaygateSystemLog", err)
		}
		// Pretty ErrorMessage = message + " - " + Status if not empty
		if remoteResp.Message == "" {
			remoteResp.Message = remoteResp.Status
		} else if remoteResp.Status != "" {
			remoteResp.Message = remoteResp.Message + " - " + remoteResp.Status
		}
		return nil, errors.New(remoteResp.Message)
	}
	return &remoteResp, nil
}

func (r repo) MeepayGetOrder(setting model.PaygateAccountResponse, reference string) (*model.MeepayGetOrderRemoteResponse, error) {

	// PrerequisitesMeepay
	if setting.ApiEndPoint == "" || setting.MerchantId == "" || setting.SecretKey == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}

	apiEndPoint := setting.ApiEndPoint
	sign := r.MeepayCreateSignature(setting.MerchantId, reference, setting.SecretKey)
	fmt.Println("MeepayDeposit.MeepayCreateSignature=", sign)
	var req model.MeepayGetDepositOrderRemoteRequest
	req.ClientId = setting.MerchantId
	req.ClientSecret = setting.SecretKey
	req.Hash = sign

	// curl --location 'https://api.meepay.me/v1/charge/<reference>' \
	// --header 'Content-Type: application/json' \
	// --data '{
	// 	"clientId": "<clientId>",
	// 	"clientSecret": "<clientSecret>",
	// 	"hash": "<hash>"
	// }'
	epUrl := fmt.Sprintf("%s/charge/%s", apiEndPoint, reference)
	fmt.Println("MeepayGetOrder url ------> ", epUrl)

	// SysLog
	if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
		Name:   "MeepayGetOrder",
		Status: "CALL",
		JsonReq: helper.StructJson(map[string]interface{}{
			"epUrl":      epUrl,
			"merchantId": setting.MerchantId,
			"orderNo":    reference,
			"req":        req,
		}),
	}); err != nil {
		log.Println("MeepayGetOrder.CreatePaygateSystemLog.ERROR", err)
	}

	client := &http.Client{}
	client.Timeout = 30 * time.Second

	jsonBody, _ := json.Marshal(req)
	payload := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest("POST", epUrl, payload)
	reqExternal.Header.Set("Content-Type", "application/json")
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseBody, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}

	fmt.Println("MeepayGetOrder.resp.Body", string(responseBody))

	if response.StatusCode != 200 && response.StatusCode != 201 {
		log.Println("MeepayGetOrder.NOT_OK, StatusCode=", response.StatusCode)
		log.Println("MeepayGetOrder.NOT_OK, responseBody=", string(responseBody))
		var errMsg2 model.MeepayErrorRemoteResponse
		errJson2 := json.Unmarshal(responseBody, &errMsg2)
		if errJson2 == nil {
			return nil, fmt.Errorf("invalid status : %s", errMsg2.Message)
		} else {
			log.Println("MeepayGetOrder.Unmarshal.errJson2 ------> ", errJson2)
		}
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	}

	var remoteResp model.MeepayGetOrderRemoteResponse
	errJson := json.Unmarshal(responseBody, &remoteResp)
	if errJson != nil {
		log.Println("MeepayGetOrder resp.Body ------> ", string(responseBody))
		log.Println("MeepayGetOrder.Unmarshal.Err ------> ", errJson)
		// TRY-3-err2
		var errMsg3 model.MeepayErrorRemoteResponse
		errJson3 := json.Unmarshal(responseBody, &errMsg3)
		if errJson3 == nil {
			return nil, errors.New(errMsg3.Message)
		} else {
			log.Println("Unmarshal.errJson3 ------> ", errJson3)
		}
		return nil, errors.New("INVALID_UNMARSHAL_JSON")
	}

	fmt.Println("MeepayGetOrder.remoteResp", remoteResp)

	if strings.ToLower(remoteResp.Status) != "succeeded" {
		log.Println("MeepayGetOrder.remoteResp", helper.StructJson(remoteResp))
		// SysLog
		if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "MeepayGetOrder",
			Status:       "ERROR",
			JsonReq:      reference,
			JsonResponse: helper.StructJson(map[string]interface{}{"resp": string(responseBody), "remoteResp": helper.StructJson(remoteResp)}),
		}); err != nil {
			log.Println("MeepayGetOrder.CreatePaygateSystemLog", err)
		}
		// Pretty ErrorMessage = message + " - " + Status if not empty
		if remoteResp.Message == "" {
			remoteResp.Message = remoteResp.Status
		} else if remoteResp.Status != "" {
			remoteResp.Message = remoteResp.Message + " - " + remoteResp.Status
		}
		return nil, errors.New(remoteResp.Message)
	}
	return &remoteResp, nil
}

func (r repo) MeepayCheckBalance(setting model.PaygateAccountResponse) (*model.MeepayCheckBalanceRemoteResponse, error) {

	// PrerequisitesMeepay
	if setting.ApiEndPoint == "" || setting.MerchantId == "" || setting.SecretKey == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}
	apiEndPoint := setting.ApiEndPoint

	// curl --location 'https://api.meepay.me/v1/balance' \
	// --header 'Content-Type: application/json' \
	// --data '{
	// 	"clientId": "<clientId>",
	// 	"clientSecret": "<clientSecret>"
	// }'
	epUrl := fmt.Sprintf("%s/balance", apiEndPoint)
	fmt.Println("MeepayCheckBalance url ------> ", epUrl)

	var req model.MeepayGetMerchantBalanceRemoteRequest
	req.ClientId = setting.MerchantId
	req.ClientSecret = setting.SecretKey

	// SysLog
	if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
		Name:   "MeepayCheckBalance",
		Status: "CALL",
		JsonReq: helper.StructJson(map[string]interface{}{
			"epUrl":   epUrl,
			"setting": setting,
		}),
	}); err != nil {
		log.Println("MeepayCheckBalance.CreatePaygateSystemLog.ERROR", err)
	}

	client := &http.Client{}
	client.Timeout = 30 * time.Second

	jsonBody, _ := json.Marshal(req)
	payload := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest("POST", epUrl, payload)
	reqExternal.Header.Set("Content-Type", "application/json")
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseBody, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}
	fmt.Println("MeepayCheckBalance.resp.Body", string(responseBody))

	if response.StatusCode != 200 && response.StatusCode != 201 {
		log.Println(response.StatusCode)
		log.Println(string(responseBody))
		var errMsg3 model.MeepayErrorRemoteResponse
		errJson3 := json.Unmarshal(responseBody, &errMsg3)
		if errJson3 == nil {
			return nil, errors.New(errMsg3.Message)
		} else {
			log.Println("MeepayCheckBalance.Unmarshal.errJson3 ------> ", errJson3)
		}
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	}

	// ERROR CODE LIST

	var remoteResp model.MeepayCheckBalanceRemoteResponse
	errJson := json.Unmarshal(responseBody, &remoteResp)
	if errJson != nil {
		log.Println("MeepayCheckBalance resp.Body ------> ", string(responseBody))
		log.Println("MeepayCheckBalance.Unmarshal.Err ------> ", errJson)
		var errMsg3 model.MeepayErrorRemoteResponse
		errJson3 := json.Unmarshal(responseBody, &errMsg3)
		if errJson3 == nil {
			return nil, errors.New(errMsg3.Message)
		} else {
			log.Println("Unmarshal.errJson3 ------> ", errJson3)
		}
		return nil, errors.New("INVALID_UNMARSHAL_JSON")
	}

	fmt.Println("MeepayCheckBalanceCreateRemoteResponse.remoteResp", remoteResp)

	// MOCK todo
	// remoteResp.Available.Amount = "1000.00"

	return &remoteResp, nil
}

func (r repo) MeepayWithdraw(setting model.PaygateAccountResponse, req model.MeepayWithdrawCreateRemoteRequest) (*model.MeepayWithdrawCreateRemoteResponse, error) {

	// PrerequisitesMeepay
	if setting.ApiEndPoint == "" || setting.MerchantId == "" || setting.SecretKey == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}

	// Will Check Balance before Withdraw
	balance, err := r.MeepayCheckBalance(setting)
	if err != nil {
		log.Println("MeepayWithdraw.MeepayCheckBalance.Error=", err)
		return nil, errors.New("PAYGATE_CANT_CHECK_BALANCE")
	}
	if balance.Available.Amount != "" {
		// Parse Float Amount
		availableAmount, err := strconv.ParseFloat(balance.Available.Amount, 64)
		if err != nil {
			log.Println("MeepayWithdraw.ParseFloat.ERROR=", err)
			return nil, errors.New("PAYGATE_CANT_CHECK_BALANCE")
		}
		if availableAmount < req.Amount {
			log.Println("MeepayWithdraw balance.Data.Credit", helper.StructJson(balance.Available.Amount))
			return nil, errors.New("PAYGATE_INSUFFICIENT_BALANCE")
		}
	}

	apiEndPoint := setting.ApiEndPoint
	sign := r.MeepayCreateSignature(setting.MerchantId, req.Reference, setting.SecretKey)
	fmt.Println("MeepayWithdraw.MeepayCreateSignature=", sign)
	// Complete Request 6/9+3/9
	req.ClientId = setting.MerchantId
	req.ClientSecret = setting.SecretKey
	req.Hash = sign

	// curl --location 'https://api.meepay.me/v1/payout' \
	// --header 'Content-Type: application/json' \
	// --data '{
	// 	"clientId": "<clientId>",
	// 	"clientSecret": "<clientSecret>",
	// 	"amount": 50,
	// 	"reference": "ORDER12345",
	// 	"callbackUrl": "https://yourdomain.com/callback",
	// 	"bankId": "***********",
	// 	"bankName": "ชื่อ นามสกุล",
	// 	"bankCode": "014",
	// 	"hash": "<hash>"
	// }'
	epUrl := fmt.Sprintf("%s/payout", apiEndPoint)
	fmt.Println("MeepayWithdraw url ------> ", epUrl)

	// SysLog
	if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
		Name:   "MeepayWithdraw",
		Status: "CALL",
		JsonReq: helper.StructJson(map[string]interface{}{
			"epUrl":      epUrl,
			"merchantId": setting.MerchantId,
			"req":        req,
		}),
	}); err != nil {
		log.Println("MeepayWithdraw.CreatePaygateSystemLog.ERROR", err)
	}

	client := &http.Client{}
	client.Timeout = 30 * time.Second

	jsonBody, _ := json.Marshal(req)
	payload := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest("POST", epUrl, payload)
	reqExternal.Header.Set("Content-Type", "application/json")
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseBody, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}
	fmt.Println("MeepayWithdraw.resp.Body", string(responseBody))

	if response.StatusCode != 200 && response.StatusCode != 201 {
		log.Println(response.StatusCode)
		log.Println(string(responseBody))
		var errMsg3 model.MeepayErrorRemoteResponse
		errJson1 := json.Unmarshal(responseBody, &errMsg3)
		if errJson1 == nil {
			return nil, errors.New(errMsg3.Message)
		} else {
			log.Println("MeepayWithdraw.Unmarshal.errJson1 ------> ", errJson1)
		}
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	}

	// ERROR CODE LIST

	var remoteResp model.MeepayWithdrawCreateRemoteResponse
	errJson := json.Unmarshal(responseBody, &remoteResp)
	if errJson != nil {
		log.Println("MeepayWithdraw resp.Body ------> ", string(responseBody))
		log.Println("MeepayWithdraw.Unmarshal.Err ------> ", errJson)
		var errMsg3 model.MeepayErrorRemoteResponse
		errJson3 := json.Unmarshal(responseBody, &errMsg3)
		if errJson3 == nil {
			return nil, errors.New(errMsg3.Message)
		} else {
			log.Println("Unmarshal.errJson3 ------> ", errJson3)
		}
		return nil, errors.New("INVALID_UNMARSHAL_JSON")
	}

	fmt.Println("MeepayWithdrawCreateRemoteResponse.remoteResp", remoteResp)

	if strings.ToLower(remoteResp.Status) != "succeeded" {
		log.Println("MeepayWithdrawCreateRemoteResponse.remoteResp", helper.StructJson(remoteResp))
		// SysLog
		if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "MeepayWithdraw.remoteResp",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(req),
			JsonResponse: helper.StructJson(map[string]interface{}{"resp": string(responseBody), "remoteResp": helper.StructJson(remoteResp)}),
		}); err != nil {
			log.Println("MeepayWithdraw.remoteResp.CreatePaygateSystemLog.ERROR", err)
		}
		// Pretty ErrorMessage = message + " - " + Status if not empty
		if remoteResp.Message == "" {
			remoteResp.Message = remoteResp.Status
		} else if remoteResp.Status != "" {
			remoteResp.Message = remoteResp.Message + " - " + remoteResp.Status
		}
		return nil, errors.New(remoteResp.Message)
	}
	return &remoteResp, nil
}

func (r repo) CreateMeepayWebhook(body model.MeepayWebhookCreateBody) (*int64, error) {

	if err := r.db.Table("paygate_meepay_webhook").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) GetDbMeepayOrderList(req model.MeepayOrderListRequest) ([]model.MeepayOrderResponse, int64, error) {

	var list []model.MeepayOrderResponse
	var total int64

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("paygate_meepay_order as tb_order")
	count = count.Select("tb_order.id")
	if req.UserId != nil {
		count = count.Where("tb_order.user_id = ?", req.UserId)
	}
	if req.OrderTypeId != nil {
		count = count.Where("tb_order.order_type_id = ?", req.OrderTypeId)
	}
	if req.OrderNo != "" {
		count = count.Where("tb_order.order_no = ?", req.OrderNo)
	}
	if req.TransactionNo != "" {
		count = count.Where("tb_order.transaction_no = ?", req.TransactionNo)
	}
	if req.Amount != "" {
		count = count.Where("tb_order.amount = ?", req.Amount)
	}
	if req.Status != "" {
		count = count.Where("tb_order.transaction_status = ?", req.Status)
	}

	if err := count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount, tb_order.transfer_amount AS transfer_amount"
		selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
		selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.qr_base64 AS qr_base64, tb_order.extra_promptpay_id AS extra_promptpay_id, tb_order.payment_page_url AS payment_page_url"
		selectedFields += ", tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
		selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

		query := r.db.Table("paygate_meepay_order as tb_order")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN paygate_order_type AS tb_type ON tb_order.order_type_id = tb_type.id")
		if req.UserId != nil {
			query = query.Where("tb_order.user_id = ?", req.UserId)
		}
		if req.OrderTypeId != nil {
			query = query.Where("tb_order.order_type_id = ?", req.OrderTypeId)
		}
		if req.OrderNo != "" {
			query = query.Where("tb_order.order_no = ?", req.OrderNo)
		}
		if req.TransactionNo != "" {
			query = query.Where("tb_order.transaction_no = ?", req.TransactionNo)
		}
		if req.Amount != "" {
			query = query.Where("tb_order.amount = ?", req.Amount)
		}
		if req.Status != "" {
			query = query.Where("tb_order.transaction_status = ?", req.Status)
		}

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetDbMeepayOrderById(id int64) (*model.MeepayOrderResponse, error) {

	var record model.MeepayOrderResponse

	selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount, tb_order.transfer_amount AS transfer_amount"
	selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
	selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.qr_base64 AS qr_base64, tb_order.extra_promptpay_id AS extra_promptpay_id, tb_order.payment_page_url AS payment_page_url"
	selectedFields += ", tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
	selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

	if err := r.db.Table("paygate_meepay_order as tb_order").
		Select(selectedFields).
		Joins("LEFT JOIN paygate_order_type AS tb_type ON tb_order.order_type_id = tb_type.id").
		Where("tb_order.id = ?", id).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetPendingMeepayDepositOrder(id int64) (*model.MeepayOrderResponse, error) {

	var record model.MeepayOrderResponse

	selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount, tb_order.transfer_amount AS transfer_amount"
	selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
	selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.qr_base64 AS qr_base64, tb_order.extra_promptpay_id AS extra_promptpay_id, tb_order.payment_page_url AS payment_page_url"
	selectedFields += ", tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
	selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

	if err := r.db.Table("paygate_meepay_order as tb_order").
		Select(selectedFields).
		Joins("LEFT JOIN paygate_order_type AS tb_type ON tb_order.order_type_id = tb_type.id").
		Where("tb_order.id = ?", id).
		Where("tb_order.transaction_status = ?", model.MEEPAY_ORDER_STATUS_WAIT_PAYMENT).
		Order("tb_order.id DESC").
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetLastestMeepayDepositOrderByUserId(userId int64) (*model.MeepayOrderResponse, error) {

	var record model.MeepayOrderResponse

	selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount, tb_order.transfer_amount AS transfer_amount"
	selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
	selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.qr_base64 AS qr_base64, tb_order.extra_promptpay_id AS extra_promptpay_id, tb_order.payment_page_url AS payment_page_url"
	selectedFields += ", tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
	selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

	if err := r.db.Table("paygate_meepay_order as tb_order").
		Select(selectedFields).
		Joins("LEFT JOIN paygate_order_type AS tb_type ON tb_order.order_type_id = tb_type.id").
		Where("tb_order.order_type_id = ?", model.PAYGATE_ORDER_TYPE_DEPOSIT).
		Where("tb_order.user_id = ?", userId).
		Order("tb_order.id DESC").
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetDbMeepayWithdrawOrderByRefId(refId int64) (*model.MeepayOrderResponse, error) {

	var record model.MeepayOrderResponse

	selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount, tb_order.transfer_amount AS transfer_amount"
	selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
	selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.qr_base64 AS qr_base64, tb_order.extra_promptpay_id AS extra_promptpay_id, tb_order.payment_page_url AS payment_page_url"
	selectedFields += ", tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
	selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

	if err := r.db.Table("paygate_meepay_order as tb_order").
		Select(selectedFields).
		Joins("LEFT JOIN paygate_order_type AS tb_type ON tb_order.order_type_id = tb_type.id").
		Where("tb_order.ref_id = ?", refId).
		Where("tb_order.order_type_id = ?", model.PAYGATE_ORDER_TYPE_WITHDRAW).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) CreateDbMeepayOrder(body model.MeepayOrderCreateBody) (*int64, error) {

	// DEFAULT VALUES //
	if body.TransactionStatus == "" {
		body.TransactionStatus = model.MEEPAY_ORDER_STATUS_PENDING
	}

	// No id in orderno, set before create //
	// if body.OrderNo == "" {
	// 	// Use Random UUID as OrderNo // 12+4 MAX 20
	// 	uuid := helper.GenerateRandomUuid(12)
	// 	orderNo := fmt.Sprintf("%v%v", time.Now().Format("0601"), uuid)
	// 	body.OrderNo = orderNo
	// }

	if err := r.db.Table("paygate_meepay_order").Create(&body).Error; err != nil {
		return nil, err
	}

	// Use Random UUID as OrderNo //
	agentName := "OR"

	orderNo := fmt.Sprintf("%v%v%v", agentName, time.Now().Format("0601"), body.Id)
	if body.OrderNo == "" {
		updateBody := make(map[string]interface{})
		updateBody["order_no"] = orderNo
		if err := r.db.Table("paygate_meepay_order").Where("id = ?", body.Id).Updates(updateBody).Error; err != nil {
			return nil, err
		}
	}
	return &body.Id, nil
}

func (r repo) CheckMeepayDepositOrderInLast5Minutes(userId int64) (*model.MeepayOrderResponse, error) {

	var record model.MeepayOrderResponse

	selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount, tb_order.transfer_amount AS transfer_amount"
	selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
	selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.qr_base64 AS qr_base64, tb_order.extra_promptpay_id AS extra_promptpay_id, tb_order.payment_page_url AS payment_page_url"
	selectedFields += ", tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
	selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

	query := r.db.Table("paygate_meepay_order as tb_order")
	query = query.Select(selectedFields)
	query = query.Joins("LEFT JOIN paygate_order_type AS tb_type ON tb_order.order_type_id = tb_type.id")
	query = query.Where("tb_order.order_type_id = ?", model.PAYGATE_ORDER_TYPE_DEPOSIT)
	query = query.Where("tb_order.user_id = ?", userId)
	// query = query.Where("tb_order.amount = ?", amount) หนึ่งคน หนึ่ง Order แทรกไม่ได้
	if err := query.
		Where("tb_order.transaction_status = ?", model.MEEPAY_ORDER_STATUS_PENDING).
		Order("tb_order.id DESC").
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) UpdateDbMeepayOrderError(id int64, remark string) error {

	updateBody := map[string]interface{}{
		"transaction_status": model.MEEPAY_ORDER_STATUS_ERROR,
		"remark":             remark,
	}
	// can be error from PENDING and WAIT_PAYMENT
	sql := r.db.Table("paygate_meepay_order").Where("id = ?", id).Where("transaction_status IN ?", []string{model.MEEPAY_ORDER_STATUS_PENDING, model.MEEPAY_ORDER_STATUS_WAIT_PAYMENT})
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdateDbMeepayOrder(id int64, body model.MeepayOrderUpdateBody) error {

	if err := r.db.Table("paygate_meepay_order").Where("id = ?", id).Updates(body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) ApproveDbMeepayOrder(id int64, webhookStatus string) error {

	updateBody := make(map[string]interface{})
	updateBody["transaction_status"] = webhookStatus
	updateBody["payment_at"] = time.Now()

	sql := r.db.Table("paygate_meepay_order").Where("id = ?", id).Where("transaction_status = ?", model.MEEPAY_ORDER_STATUS_WAIT_PAYMENT)
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdateMeepayOrderBankSucess(id int64, transId int64, actionBy int64) error {

	updateBody := map[string]interface{}{
		"bank_transaction_id":     transId,
		"bank_transaction_status": model.MEEPAY_ORDER_STATUS_SUCCESS,
		"action_by":               actionBy,
	}

	sql := r.db.Table("paygate_meepay_order").Where("id = ?", id).Where("bank_transaction_status = ?", model.MEEPAY_ORDER_STATUS_PENDING)
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}
