package repository

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"fmt"
	"io"
	"log"
	"time"

	"gorm.io/gorm"
)

func NewUserSalepageRepository(db *gorm.DB) UserSalepageRepository {
	return &repo{db}
}

type UserSalepageRepository interface {
	GetUserSalepageStatDailKey(userId int64) string
	GetUserSalepageStatSummary(userId int64) (*model.UserSalepageStatSummaryResponse, error)
	GetUserSalepageInfo(userId int64) (*model.UserSalepageResponse, error)
	CheckuserSalepageCodeUnique(userId int64, saleCode string) error
	UpdateUserSalepageInfo(body model.UserSalepageUpdateRequest) error
	// private use GetUserSalepageStatByDailyKey(dailyKey string) (*model.UserSalepageStatResponse, error)
	GetCustomSalepageBlockList(req model.CustomSalepageBlockListRequest) ([]model.CustomSalepageBlockResponse, error)
	GetCustomSalepageBlockById(id int64) (*model.CustomSalepageBlockResponse, error)
	UpdateCustomSalepageBlock(id int64, body model.CustomSalepageBlockUpdateBody) error
	SortCustomSalepageBlock(req model.DragSortRequest) error
	// private use CreateUserSalepageStat(body model.UserSalepageStatCreateBody) (*int64, error)
	GetUserIdFromSalepageCode(saleCode string) (int64, error)
	IncreaseTodayLinkClick(userId int64) error
	IncreaseTodayMemberRegisterClick(userId int64) error
	IncreaseTodayAdminClick(userId int64) error
	// REF S3
	UploadImageToS3(pathUpload string, filename string, fileReader io.Reader) (*model.FileUploadResponse, error)
}

func (s *repo) GetUserSalepageStatDailKey(userId int64) string {

	actionAtUtc := time.Now().UTC()
	dailyKey := fmt.Sprintf("D%sU%d", actionAtUtc.Format("060102"), userId)
	return dailyKey
}

func (r *repo) GetUserSalepageStatSummary(userId int64) (*model.UserSalepageStatSummaryResponse, error) {

	var record model.UserSalepageStatSummaryResponse

	dailyKey := r.GetUserSalepageStatDailKey(userId)

	selectedFields := "tb_stat.user_id AS user_id, tb_stat.member_register_count AS today_member_register_count, tb_stat.admin_click_count AS today_admin_click_count"
	if err := r.db.Table("user_alliance_salepage_stat AS tb_stat").
		Select(selectedFields).
		Where("tb_stat.daily_key = ?", dailyKey).
		Limit(1).
		Scan(&record).
		Error; err != nil {
		return nil, err
	}

	// SUM ALL group by user_id
	selectedFields2 := "tb_stat.user_id AS user_id, SUM(tb_stat.link_click_count) AS total_link_click_count"
	selectedFields2 += ", SUM(tb_stat.member_register_count) AS total_member_register_count, SUM(tb_stat.admin_click_count) AS total_admin_click_count"
	if err := r.db.Table("user_alliance_salepage_stat AS tb_stat").
		Select(selectedFields2).
		Where("tb_stat.user_id = ?", userId).
		Group("tb_stat.user_id").
		Scan(&record).
		Error; err != nil {
		return nil, err
	}

	return &record, nil
}

func (r *repo) GetUserSalepageInfo(userId int64) (*model.UserSalepageResponse, error) {

	var record model.UserSalepageResponse

	selectedFields := "tb_setting.user_id AS user_id, tb_setting.sale_code AS sale_code"
	if err := r.db.Table("user_alliance AS tb_setting").
		Select(selectedFields).
		Where("tb_setting.user_id = ?", userId).
		Take(&record).
		Error; err != nil {
		return nil, err
	}

	// Auto
	if record.SaleCode == "" {
		// update user_alliance.sale_code
		genCode := helper.RandStrings(6)
		if err := r.db.Table("user_alliance").Where("user_id = ?", userId).Update("sale_code", genCode).Error; err != nil {
			// Try again on Unique
			genCode := helper.RandStrings(6)
			if err := r.db.Table("user_alliance").Where("user_id = ?", userId).Update("sale_code", genCode).Error; err != nil {
				log.Println("Error.UpdateUserSalepageInfo", err)
			}
		}
		// reGet
		if err := r.db.Table("user_alliance AS tb_setting").
			Select(selectedFields).
			Where("tb_setting.user_id = ?", userId).
			Take(&record).
			Error; err != nil {
			return nil, err
		}
	}

	return &record, nil
}

func (r *repo) CheckuserSalepageCodeUnique(userId int64, saleCode string) error {

	var record model.UserSalepageResponse

	selectedFields := "tb_setting.user_id AS user_id, tb_setting.sale_code AS sale_code"
	if err := r.db.Table("user_alliance AS tb_setting").
		Select(selectedFields).
		Where("tb_setting.sale_code = ? AND tb_setting.user_id != ?", saleCode, userId).
		Take(&record).
		Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil
		}
		return err
	}

	if record.UserId != 0 {
		return fmt.Errorf("saleCode %s already exists", saleCode)
	}

	return nil
}

func (r *repo) UpdateUserSalepageInfo(body model.UserSalepageUpdateRequest) error {

	if err := r.db.Table("user_alliance").Where("user_id = ?", body.UserId).Updates(body).Error; err != nil {
		return err
	}
	return nil
}

func (r *repo) GetCustomSalepageBlockList(req model.CustomSalepageBlockListRequest) ([]model.CustomSalepageBlockResponse, error) {

	var list []model.CustomSalepageBlockResponse

	// Alway check for master user_master_salepage_block
	var count int64
	if err := r.db.Table("user_salepage_block AS tb_block").Select("tb_block.id").Where("tb_block.user_id = ?", req.UserId).Count(&count).
		Error; err != nil {
		return nil, err
	}
	log.Println("GetCustomSalepageBlockList.Count=", count)

	// [20250605] add new block TOTAL_USER_ONLINE and TOTAL_JACKPOT
	maxBlockCount := int64(15)
	if count == 0 {
		// Create ALL default record by Copy from user_master_salepage_block
		var masterList []model.CustomSalepageBlockRawResponse
		selectedFields := "id, name, label, is_show, sort_order"
		if err := r.db.Table("user_master_salepage_block AS tb_master").
			Select(selectedFields).
			Scan(&masterList).
			Error; err != nil {
			log.Println("GetCustomSalepageBlockList.CreateDefaultRecord.SELECT_MASTER.ERROR=", err)
		}
		var createList []model.CustomSalepageBlockCreateBody
		for _, row := range masterList {
			// masterList[i].UserId = req.UserId
			// masterList[i].SortOrder = int64(i + 1) // Sort order starts from 1
			// Set default value for blockKey
			// [20250526] One BlockKey for each user, Modified to use user_id and any unique row id later or left it NULL for multi-blocks.
			blockKey := fmt.Sprintf("U%dR%d", req.UserId, row.Id)
			// masterList[i].BlockKey = &blockKey
			createList = append(createList, model.CustomSalepageBlockCreateBody{
				UserId:    req.UserId,
				Name:      row.Name,
				Label:     row.Label,
				IsShow:    row.IsShow,
				SortOrder: row.SortOrder,
				BlockKey:  &blockKey,
			})
		}
		if len(createList) > 0 {
			if err := r.db.Table("user_salepage_block").Create(&createList).Error; err != nil {
				log.Println("GetCustomSalepageBlockList.CreateDefaultRecord.CREATE_COPY.ERROR=", err)
				// continue to return list.
			}
		}
	} else if count < maxBlockCount {
		// Create SOME default record by Copy from user_master_salepage_block
		var masterList []model.CustomSalepageBlockRawResponse
		selectedFields := "id, name, label, is_show, sort_order"
		if err := r.db.Table("user_master_salepage_block AS tb_master").
			Select(selectedFields).
			Scan(&masterList).
			Error; err != nil {
			log.Println("GetCustomSalepageBlockList.CreateDefaultRecord.SELECT_MASTER.ERROR=", err)
		}
		for _, row := range masterList {
			// Set default value for blockKey
			// [20250526] One BlockKey for each user, Modified to use user_id and any unique row id later or left it NULL for multi-blocks.
			blockKey := fmt.Sprintf("U%dR%d", req.UserId, row.Id)
			// Check if this block already exists
			var existingBlock model.CustomSalepageBlockResponse
			if err := r.db.Table("user_salepage_block AS tb_block").
				Select("tb_block.id").
				Where("tb_block.user_id = ? AND tb_block.name = ?", req.UserId, row.Name).
				Take(&existingBlock).
				Error; err != nil {
				if err == gorm.ErrRecordNotFound {
					createBlock := model.CustomSalepageBlockCreateBody{
						UserId:    req.UserId,
						Name:      row.Name,
						Label:     row.Label,
						IsShow:    row.IsShow,
						SortOrder: row.SortOrder,
						BlockKey:  &blockKey,
					}
					if err := r.db.Table("user_salepage_block").Create(&createBlock).Error; err != nil {
						log.Println("GetCustomSalepageBlockList.CreateDefaultRecord.CREATE_SOME.ERROR=", err)
						// continue to return list.
					}
				}
			}
		}

	}

	selectedFields := "*"
	if err := r.db.Table("user_salepage_block AS tb_block").
		Select(selectedFields).
		Where("tb_block.user_id = ?", req.UserId).
		Order("tb_block.sort_order ASC").
		Scan(&list).
		Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (r *repo) GetCustomSalepageBlockById(id int64) (*model.CustomSalepageBlockResponse, error) {

	var record model.CustomSalepageBlockResponse

	selectedFields := "*"
	if err := r.db.Table("user_salepage_block AS tb_block").
		Select(selectedFields).
		Where("tb_block.id = ?", id).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r *repo) UpdateCustomSalepageBlock(id int64, body model.CustomSalepageBlockUpdateBody) error {

	if err := r.db.Table("user_salepage_block").Where("id = ?", id).Updates(body).Error; err != nil {
		return err
	}
	return nil
}

func (r *repo) SortCustomSalepageBlock(req model.DragSortRequest) error {

	tableName := "user_salepage_block"

	// ===== NO NEED TO EDIT AFTER THIS LINE ===== //

	var list []model.SortOrder
	selectedFields := "id, sort_order"
	query := r.db.Table(tableName)
	query = query.Select(selectedFields)
	if err := query.
		Where("id IN ?", []int64{req.FromItemId, req.ToItemId}).
		Limit(2).
		Find(&list).
		Error; err != nil {
		return err
	}

	var fromItem *model.SortOrder
	var toItem *model.SortOrder
	for _, record := range list {
		if record.Id == req.FromItemId {
			fromItem = &model.SortOrder{
				Id:        record.Id,
				SortOrder: record.SortOrder,
			}
		} else if record.Id == req.ToItemId {
			toItem = &model.SortOrder{
				Id:        record.Id,
				SortOrder: record.SortOrder,
			}
		}
	}

	if fromItem != nil && toItem != nil {
		// Sort Direction //
		if fromItem.SortOrder < toItem.SortOrder {
			// Drag down  //
			whereShiftDown := r.db.Where("sort_order > ?", fromItem.SortOrder).Where("sort_order <= ?", toItem.SortOrder)
			if err := r.db.Table(tableName).Where(whereShiftDown).Update("sort_order", gorm.Expr("sort_order - 1")).Error; err != nil {
				return err
			}
			// Go to
			if err := r.db.Table(tableName).Where("id = ?", fromItem.Id).Update("sort_order", toItem.SortOrder).Error; err != nil {
				return err
			}
		} else if fromItem.SortOrder > toItem.SortOrder {
			// Drag up = shift up //
			whereShiftDown := r.db.Where("sort_order < ?", fromItem.SortOrder).Where("sort_order >= ?", toItem.SortOrder)
			if err := r.db.Table(tableName).Where(whereShiftDown).Update("sort_order", gorm.Expr("sort_order + 1")).Error; err != nil {
				return err
			}
			// Go to
			if err := r.db.Table(tableName).Where("id = ?", fromItem.Id).Update("sort_order", toItem.SortOrder).Error; err != nil {
				return err
			}
		}
	}
	return nil
}

func (r *repo) CheckIsDailyKeyExists(dailyKey string) (string, error) {

	var record model.UserSalepageStat

	selectedFields := "daily_key AS daily_key"
	if err := r.db.Table("user_alliance_salepage_stat AS tb_stat").
		Select(selectedFields).
		Where("tb_stat.daily_key = ?", dailyKey).
		Take(&record).
		Error; err != nil {
		return "", err
	}
	return record.DailyKey, nil
}

func (r *repo) CreateUserSalepageStat(body model.UserSalepageStatCreateBody) (*int64, error) {

	if err := r.db.Table("user_alliance_salepage_stat").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r *repo) GetUserIdFromSalepageCode(saleCode string) (int64, error) {

	var record model.UserAlliance

	if err := r.db.Table("user_alliance AS tb_setting").
		Select("tb_setting.user_id AS user_id").
		Where("tb_setting.sale_code = ?", saleCode).
		Take(&record).
		Error; err != nil {
		return 0, err
	}
	return record.UserId, nil
}

func (r *repo) IncreaseTodayLinkClick(userId int64) error {

	dailyKey := r.GetUserSalepageStatDailKey(userId)

	// Check if dailyKey exists
	if _, err := r.CheckIsDailyKeyExists(dailyKey); err != nil {
		if err == gorm.ErrRecordNotFound {
			// Create a new record if it doesn't exist
			body := model.UserSalepageStatCreateBody{
				UserId:         userId,
				DailyKey:       dailyKey,
				LinkClickCount: 1,
			}
			if _, err := r.CreateUserSalepageStat(body); err != nil {
				return err
			}
			// Return nil if the record is created successfully with one click
			return nil
		}
		return err
	}

	if err := r.db.Table("user_alliance_salepage_stat").Where("daily_key = ?", dailyKey).
		Update("link_click_count", gorm.Expr("link_click_count + 1")).
		Error; err != nil {
		return err
	}
	return nil
}

func (r *repo) IncreaseTodayMemberRegisterClick(userId int64) error {

	dailyKey := r.GetUserSalepageStatDailKey(userId)

	// Check if dailyKey exists
	if _, err := r.CheckIsDailyKeyExists(dailyKey); err != nil {
		if err == gorm.ErrRecordNotFound {
			// Create a new record if it doesn't exist
			body := model.UserSalepageStatCreateBody{
				UserId:              userId,
				DailyKey:            dailyKey,
				MemberRegisterCount: 1,
			}
			if _, err := r.CreateUserSalepageStat(body); err != nil {
				return err
			}
			// Return nil if the record is created successfully with one click
			return nil
		}
		return err
	}

	if err := r.db.Table("user_alliance_salepage_stat").Where("daily_key = ?", dailyKey).
		Update("member_register_count", gorm.Expr("member_register_count + 1")).
		Error; err != nil {
		return err
	}
	return nil
}

func (r *repo) IncreaseTodayAdminClick(userId int64) error {

	dailyKey := r.GetUserSalepageStatDailKey(userId)

	// Check if dailyKey exists
	if _, err := r.CheckIsDailyKeyExists(dailyKey); err != nil {
		if err == gorm.ErrRecordNotFound {
			// Create a new record if it doesn't exist
			body := model.UserSalepageStatCreateBody{
				UserId:          userId,
				DailyKey:        dailyKey,
				AdminClickCount: 1,
			}
			if _, err := r.CreateUserSalepageStat(body); err != nil {
				return err
			}
			// Return nil if the record is created successfully with one click
			return nil
		}
		return err
	}

	if err := r.db.Table("user_alliance_salepage_stat").Where("daily_key = ?", dailyKey).
		Update("admin_click_count", gorm.Expr("admin_click_count + 1")).
		Error; err != nil {
		return err
	}
	return nil
}
