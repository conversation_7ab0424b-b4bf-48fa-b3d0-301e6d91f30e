package repository

import (
	"cybergame-api/model"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"time"

	"gorm.io/gorm"
)

func NewLineRepository(db *gorm.DB) LineRepository {
	return &repo{db}
}

type LineRepository interface {
	ValidateLineToken(token string) (*model.LineValidateTokenResponse, error)
}

func (r repo) ValidateLineToken(token string) (*model.LineValidateTokenResponse, error) {

	clientId := os.Getenv("LINE_LOGIN_CLIENT_ID")

	// log.Println("ValidateLineToken req ------> ", clientId)

	// https://api.line.me/oauth2/v2.1/verify?access_token=xxxx.aa-bb
	url := fmt.Sprintf("https://api.line.me/oauth2/v2.1/verify?access_token=%s", token)

	client := &http.Client{}
	client.Timeout = 3 * time.Second
	reqExternal, _ := http.NewRequest("GET", url, nil)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("Accept", "application/json")
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("INVALID_RESPONSE_DATA")
	}

	if response.StatusCode != 200 {
		log.Println(response.StatusCode)
		log.Println(string(responseData))
		return nil, errors.New("INVALID_RESPONSE_CODE")
	}

	var result model.LineValidateTokenResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("ValidateLineToken resp.Body ------> ", string(responseData))
		log.Println("errJson ------> ", errJson)
		return nil, errors.New("CANT_PARSE_RESPONSE_DATA")
	}
	if result.ClientId != clientId {
		log.Println("ValidateLineToken ClientId not match", result)
		// CANT_GETGAME_403 : Permission denied.
		return nil, fmt.Errorf("INVALID_TOKEN_%s", "SITE")
	}
	return &result, nil
}
