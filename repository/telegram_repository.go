package repository

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"os"
	"time"

	"gorm.io/gorm"
)

func NewTelegramRepository(db *gorm.DB) TelegramRepository {
	return &repo{db}
}

type TelegramRepository interface {
	// Telegram
	GetTelegramAccessToken() (*model.TelegramAccessTokenResponse, error)
	UpdateTelegramAccessToken(token string) error
	//
	GetWebhookLastUpdateUid() (*int64, error)
	SaveWebhookLastUpdateUid(updateUid int64) error
	GetTelegramUpdateList(botUid string, lastUpdateId int64) (*model.TelegramGetUpdatesResponse, error)
	GetChatToken(updateUid int64, chat model.TelegramGetUpdates) (string, error)
	SendTelegramMessage(chatInfo model.TelegramChatTokenResponse, message string) (*model.TelegramSendMessageResponse, error)
	GetTelegramChatTokenByToken(token string) (*model.TelegramChatTokenResponse, error)
}

func (r repo) GetTelegramAccessToken() (*model.TelegramAccessTokenResponse, error) {

	var result model.TelegramAccessTokenResponse

	selectedFields := "tb_token.telegram_token AS access_token"
	if err := r.db.Table("telegram_last_update AS tb_token").
		Select(selectedFields).
		Limit(1).
		Scan(&result).
		Error; err != nil {
		if err.Error() == gorm.ErrRecordNotFound.Error() {
			return &result, nil
		}
		return nil, err
	}

	if result.AccessToken == "" {
		// Use ENV on NotSet
		botUid := os.Getenv("TELEGRAM_BOT_TOKEN")
		result.AccessToken = botUid
	}

	return &result, nil
}

func (r repo) UpdateTelegramAccessToken(token string) error {

	// create if not exists
	var count int64
	if err := r.db.Table("telegram_last_update").Count(&count).Error; err != nil {
		return err
	}
	if count > 0 {
		// update
		// reset update_uid
		updateBody := map[string]interface{}{
			"telegram_token": token,
			"update_uid":     0,
		}
		if err := r.db.Table("telegram_last_update").
			Where("id = ?", 1).
			Updates(updateBody).
			Error; err != nil {
			return err
		}
	} else {
		// insert
		if err := r.db.Table("telegram_last_update").
			Create(map[string]interface{}{
				"telegram_token": token,
				"update_uid":     0,
			}).Error; err != nil {
			return err
		}
	}
	return nil
}

func (r repo) GetWebhookLastUpdateUid() (*int64, error) {

	var result int64
	initUid := int64(0)

	selectedFields := "update_uid"
	if err := r.db.Table("telegram_last_update as tb_latest_uid").
		Select(selectedFields).
		Take(&result).
		Error; err != nil {
		if err.Error() == gorm.ErrRecordNotFound.Error() {
			return &initUid, nil
		}
		return nil, err
	}
	return &result, nil
}

func (r repo) SaveWebhookLastUpdateUid(updateUid int64) error {

	// create if not exists
	var count int64
	if err := r.db.Table("telegram_last_update").Count(&count).Error; err != nil {
		return err
	}
	if count > 0 {
		// update
		if err := r.db.Table("telegram_last_update").
			Where("id = ?", 1).
			Update("update_uid", updateUid).
			Error; err != nil {
			return err
		}
	} else {
		// insert
		if err := r.db.Table("telegram_last_update").
			Create(map[string]interface{}{
				"update_uid": updateUid,
			}).Error; err != nil {
			return err
		}
	}
	return nil
}

func (r repo) GetChatToken(updateUid int64, chat model.TelegramGetUpdates) (string, error) {

	var result string

	// get
	selectedFields := "token"
	if err := r.db.Table("telegram_chat_token").
		Select(selectedFields).
		Where("chat_uid = ?", chat.Message.Chat.Id).
		Take(&result).
		Error; err != nil {
		if err.Error() == gorm.ErrRecordNotFound.Error() {
			// insert
			genToken := helper.AlphaNumerics(16)
			if err := r.db.Table("telegram_chat_token").
				Create(map[string]interface{}{
					"update_uid": updateUid,
					"chat_uid":   chat.Message.Chat.Id,
					"chat_title": chat.Message.Chat.Title,
					"chat_type":  chat.Message.Chat.Type,
					"token":      genToken,
				}).Error; err != nil {
				return "", err
			}
			return genToken, nil
		}
	}
	return result, nil
}

func (r repo) GetTelegramChatTokenByToken(token string) (*model.TelegramChatTokenResponse, error) {

	var result model.TelegramChatTokenResponse

	selectedFields := "tb_chat.chat_uid AS chat_id, tb_token.telegram_token AS access_token"
	if err := r.db.Table("telegram_chat_token AS tb_chat").
		Select(selectedFields).
		Joins("LEFT JOIN telegram_last_update AS tb_token ON tb_token.id = 1"). // todo suport multiple token
		Where("tb_chat.token = ?", token).
		Take(&result).
		Error; err != nil {
		if err.Error() == gorm.ErrRecordNotFound.Error() {
			return nil, nil
		}
		return nil, err
	}
	return &result, nil
}

func (r repo) GetTelegramUpdateList(botUid string, lastUpdateId int64) (*model.TelegramGetUpdatesResponse, error) {

	endPoint := "https://api.telegram.org"
	// botUid := os.Getenv("TELEGRAM_BOT_TOKEN")
	offset := fmt.Sprintf("%d", lastUpdateId)

	// fmt.Println("GetTelegramUpdateList req ------> ", lastUpdateId)
	if botUid == "" {
		log.Println("GetTelegramUpdateList.ENV_TELEGRAM_BOT_TOKEN_NOT_SET")
		return nil, errors.New("ENV_TELEGRAM_BOT_TOKEN_NOT_SET")
	}

	// GET https://api.telegram.org/bot{bot_uid}/getUpdates?offset=&limit=100
	url := fmt.Sprintf("%s/bot%s/getUpdates?offset=%s&limit=100", endPoint, botUid, offset)

	client := &http.Client{}
	client.Timeout = 3 * time.Second
	reqExternal, _ := http.NewRequest("GET", url, nil)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("Accept", "application/json")
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println("GetTelegramUpdateList.CLIENT_CALL_ERROR", err.Error())
		return nil, errors.New("CLIENT_CALL_ERROR")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println("GetTelegramUpdateList.RESPONSE_READ_ERROR", err)
		return nil, errors.New("RESPONSE_READ_ERROR")
	}

	if response.StatusCode != 200 {
		log.Println("GetTelegramUpdateList.HTTP_NOT_200", response.StatusCode)
		log.Println("GetTelegramUpdateList.response_data", string(responseData))
		return nil, errors.New("INVALID_RESPONSE_CODE")
	}

	var result model.TelegramGetUpdatesResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("GetTelegramUpdateList.CANT_PARSE_RESPONSE_DATA ------> ", string(responseData))
		return nil, errors.New("CANT_PARSE_RESPONSE_DATA")
	}

	// fmt.Println("GetTelegramUpdateList res ------> ", helper.StructJson(result))

	return &result, nil
}

func (r repo) SendTelegramMessage(chatInfo model.TelegramChatTokenResponse, message string) (*model.TelegramSendMessageResponse, error) {

	// fmt.Println("SendTelegramMessage req chatId=", chatInfo.ChatId, " message=", message)

	endPoint := "https://api.telegram.org"
	// botUid := os.Getenv("TELEGRAM_BOT_TOKEN")
	if chatInfo.AccessToken == "" {
		log.Println("SendTelegramMessage.ENV_TELEGRAM_BOT_TOKEN_NOT_SET")
		return nil, errors.New("ENV_TELEGRAM_BOT_TOKEN_NOT_SET")
	}
	if chatInfo.ChatId == 0 {
		log.Println("SendTelegramMessage.CHAT_ID_IS_ZERO")
		return nil, errors.New("CHAT_ID_IS_ZERO")
	}

	message = url.QueryEscape(message)

	// POST https://api.telegram.org/botaaaaaaaa:bbbbbbbbbbb/sendMessage?chat_id=-000000000&text=ก็ยังใช้ได้อยู่
	url := fmt.Sprintf("%s/bot%s/sendMessage?chat_id=%d&text=%s", endPoint, chatInfo.AccessToken, chatInfo.ChatId, message)
	// fmt.Println("SendTelegramMessage url ------> ", url)

	client := &http.Client{}
	client.Timeout = 3 * time.Second
	reqExternal, err := http.NewRequest("POST", url, nil)
	if err != nil {
		log.Println("SendTelegramMessage.HTTP_REQUEST_ERROR", err.Error())
		return nil, errors.New("HTTP_REQUEST_ERROR")
	}
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("Accept", "application/json")
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println("SendTelegramMessage.CLIENT_CALL_ERROR", err.Error())
		return nil, errors.New("CLIENT_CALL_ERROR")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println("SendTelegramMessage.RESPONSE_READ_ERROR", err)
		return nil, errors.New("RESPONSE_READ_ERROR")
	}

	if response.StatusCode != 200 {
		log.Println("SendTelegramMessage.HTTP_NOT_200", response.StatusCode)
		log.Println("SendTelegramMessage.response_data", string(responseData))
		var telegErr1 model.TelegramErrorResponse
		errJson1 := json.Unmarshal(responseData, &telegErr1)
		if errJson1 == nil {
			log.Println("SendTelegramMessage.errJson1 ------> ", helper.StructJson(telegErr1))
			return nil, errors.New(telegErr1.Description)
		}
		return nil, errors.New("INVALID_RESPONSE_CODE")
	}

	var result model.TelegramSendMessageResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("SendTelegramMessage.CANT_PARSE_RESPONSE_DATA ------> ", string(responseData))
		return nil, errors.New("CANT_PARSE_RESPONSE_DATA")
	}
	return &result, nil
}
