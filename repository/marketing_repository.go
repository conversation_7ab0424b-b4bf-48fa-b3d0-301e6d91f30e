package repository

import (
	"cybergame-api/model"
	"time"

	"gorm.io/gorm"
)

func NewMarketingRepository(db *gorm.DB) MarketingRepository {
	return &repo{db}
}

type MarketingRepository interface {
	GetMarketingConfigByKey(key string, defaultVal string) (*model.MarketingConfig, error)
	UpdateMarketingConfig(key string, val string) error
	DeleteMarketingConfigByKey(key string) error
	// RACE_CONDITION_BLOCKER
	GetRaceActionByActionKey(actionKey string) (*model.RaceAction, error)
	GetRaceActionIdByActionKey(actionKey string) (*int64, error)
	CreateRaceCondition(body model.RaceActionCreateBody) (int64, error)
	// TEST-CASE
	// TestRcConfirmUserIncome(id int64) error
	// REF-USER_INCOME
	CreateUserIncomeLog(body model.UserIncomeLogCreateBody) (*int64, error)
	GetUserIncomeLogById(id int64) (*model.UserIncomeLogResponse, error)
	GetUserIncomeLogList(req model.UserIncomeLogListRequest) ([]model.UserIncomeLogResponse, int64, error)
	GetUserIncomeLogListSummary(req model.UserIncomeLogTotalSummaryRequest) (*model.UserIncomeLogTotalSummaryResponse, error)
	GetUserIncomePendingCount() (int64, error)
	GetHengOrderSuccessPendingCount() (int64, error)
	GetUserIncomePendingList(req model.UserIncomeLogListRequest) ([]model.UserIncomeLogResponse, int64, error)
	ConfirmUserIncomeLog(body model.UserIncomeLogConfirmBody) error
	CancelUserIncomeLog(body model.UserIncomeLogConfirmBody) error
	GetUserWinLoseSummary(req model.UserWinLoseSummaryReportRequest) (*model.UserWinLoseSummaryReportResponse, error)
	GetUserWinLoseSummaryList(req model.UserWinLoseSummaryListRequest) ([]model.UserWinLoseSummaryResponse, int64, error)
	GetUserWinLoseDailySummary(req model.UserWinLoseDailySummaryRequest) (*model.UserWinLoseSummaryReportResponse, error)
	GetUserWinLoseDailyList(req model.UserWinLoseDailyListRequest) ([]model.UserWinLoseDailyResponse, int64, error)
	GetUserTodayWinLoseSummary(req model.UserTodayWinLoseSummaryReportRequest) (*model.UserWinLoseSummaryReportResponse, error)
	GetUserTodayWinLoseSummaryList(req model.UserTodayWinLoseSummaryListRequest) ([]model.UserWinLoseSummaryResponse, int64, error)
	// REF-Admin
	GetAdminById(id int64) (*model.Admin, error)
	// REF-PROMOTION-RETURN
	GetUserMemberInfoById(id int64) (*model.UserResponse, error)
	IncreaseUserCredit(body model.UserTransactionCreateRequest) (*model.UserTransactionCreateResponse, error)
	CreateTurnoverUserStatement(body model.TurnoverUserStatementCreateBody) (*int64, error)
	// Lucky Wheel
	GetActivityLuckyWheelRoundUserByInComeId(id int64) (*model.ActivityLuckyWheelRoundUserResponse, error)
	UpdateActivityLuckyWheelRoundUser(body model.UpdateActivityLuckyWheelRoundUserRequest) error
	CreateActivityLuckyWheelRoundConfirm(body model.ActivityLuckyWheelRoundConfirmCreateRequest) (int64, error)
	GetActivityLuckyWheelRoundConfirmByKey(Key string) (*model.ActivityLuckyWheelRoundConfirmResponse, error)
	RollBackConfirmActionConfirmActivityLuckyWheel(id int64) error
	RollBackActivityLuckyWheelUser(id int64) error
	// REF-AFF
	GetAffTransactionWithdrawById(id int64) (*model.AffTransactionWithdraw, error)
	SetConfirmAffTransactionList(ids []int64) error
	// MARKETING_REPORT
	GetTransactionReportDaily(query model.TransactionReportDailyQuery) (model.TransactionReportDailyResponse, error)

	CountDepositPendingStatus() (int64, error)
	CountWithdrawPendingStatus() (int64, error)

	CreateAdminAction(body model.AdminActionCreateBody) (*int64, error)
}

func (r repo) CountDepositPendingStatus() (int64, error) {

	var total int64

	// marketing_repository.go:72 SLOW SQL >= 200ms
	// Fixed by Limit to 30 days before
	beforeTransferAt := time.Now().AddDate(0, 0, -30)

	count := r.db.Table("bank_transaction as transactions")
	count = count.Select("transactions.id")
	count = count.Where("transactions.transaction_type_id in (?,?)", model.TRANSACTION_TYPE_DEPOSIT, model.TRANSACTION_TYPE_BONUS)
	count = count.Where("transactions.transaction_status_id IN ?", []int64{model.TRANS_STATUS_PENDING, model.TRANS_STATUS_DEPOSIT_PENDING_CREDIT, model.TRANS_STATUS_DEPOSIT_PENDING_SLIP, model.TRANS_STATUS_DEPOSIT_PENDING_MULTIUSER})
	count = count.Where("transactions.transfer_at >= ?", beforeTransferAt)
	count = count.Where("transactions.deleted_at IS NULL")
	if err := count.Count(&total).Error; err != nil {
		return 0, err
	}
	return total, nil

}
func (r repo) CountWithdrawPendingStatus() (int64, error) {

	var total int64

	// marketing_repository.go:72 SLOW SQL >= 200ms
	// Fixed by Limit to 30 days before
	beforeTransferAt := time.Now().AddDate(0, 0, -30)

	count := r.db.Table("bank_transaction as transactions")
	count = count.Select("transactions.id")
	count = count.Where("transactions.transaction_type_id in (?)", []int64{model.TRANSACTION_TYPE_WITHDRAW, model.TRANSACTION_TYPE_CREDITBACK, model.TRANSACTION_TYPE_CREDITCANCEL})
	count = count.Where("transactions.transaction_status_id IN ?", []int64{model.TRANS_STATUS_WITHDRAW_OVER_BUDGET, model.TRANS_STATUS_WITHDRAW_APPROVED, model.TRANS_STATUS_WITHDRAW_OVER_MAX, model.TRANS_STATUS_WITHDRAW_UNSURE})
	count = count.Where("transactions.transfer_at >= ?", beforeTransferAt)
	count = count.Where("transactions.deleted_at IS NULL")
	if err := count.Count(&total).Error; err != nil {
		return 0, err
	}
	return total, nil
}

func (r repo) GetMarketingConfigByKey(key string, defaultVal string) (*model.MarketingConfig, error) {

	var record model.MarketingConfig
	var err error

	// SELECT //
	selectedFields := "configs.id, configs.config_key, configs.config_val"
	query := r.db.Table("marketing_config as configs")
	query = query.Select(selectedFields)
	query = query.Where("configs.config_key = ?", key)
	query = query.Where("configs.deleted_at IS NULL")
	if err = query.Take(&record).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// create new record
			var data model.MarketingConfigCreateBody
			data.ConfigKey = key
			data.ConfigVal = defaultVal
			if err := r.db.Table("marketing_config").Create(&data).Error; err != nil {
				return nil, err
			}
			// reget
			if err = query.Take(&record).Error; err != nil {
				return nil, err
			}
			return &record, nil
		}
		return nil, err
	}
	return &record, nil
}

func (r repo) UpdateMarketingConfig(key string, val string) error {

	if err := r.db.Table("marketing_config").Where("config_key = ?", key).Update("config_val", val).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) DeleteMarketingConfigByKey(key string) error {
	if err := r.db.Table("marketing_config").Where("config_key = ?", key).Delete(&model.BankAccountTransfer{}).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetTransactionReportDaily(req model.TransactionReportDailyQuery) (model.TransactionReportDailyResponse, error) {

	var result model.TransactionReportDailyResponse

	var total int64

	// DateType
	if req.OfDate != "" {
		// ofdate = 2021-01-01 => from 2021-01-01 to 2021-01-31
		startAt, err := time.Parse("2006-01-02", req.OfDate)
		if err != nil {
			return result, err
		}
		firstOfMonth := time.Date(startAt.Year(), startAt.Month(), 1, 0, 0, 0, 0, time.UTC)
		lastOfMonth := firstOfMonth.AddDate(0, 1, -1)
		req.FromDate = firstOfMonth.Format("2006-01-02")
		req.ToDate = lastOfMonth.Format("2006-01-02")
	}

	result, err := r.getTransactionReportDailyTotal(req)
	if err != nil {
		return result, err
	}

	// swap req
	var queryFromDate string
	var queryToDate string

	// Generate Date
	var groupedDate []model.TransactionReportDailyRow
	fromDate, err := time.Parse("2006-01-02", req.FromDate)
	if err != nil {
		return result, err
	}
	toDate, err := time.Parse("2006-01-02", req.ToDate)
	if err != nil {
		return result, err
	}
	dateOffset := req.Limit * req.Page
	dateLimit := req.Limit
	for d := fromDate; d.Before(toDate) || d.Equal(toDate); d = d.AddDate(0, 0, 1) {
		total++
		if dateOffset > 0 {
			dateOffset--
			continue
		}
		if len(groupedDate) < dateLimit {
			if queryFromDate == "" {
				queryFromDate = d.Format("2006-01-02")
			}
			groupedDate = append(groupedDate, model.TransactionReportDailyRow{OfDate: d.Format("2006-01-02")})
			result.List = append(result.List, model.TransactionReportDaily{
				OfDate: d.Format("2006-01-02"),
			})
			queryToDate = d.Format("2006-01-02")
		}
	}
	result.FromDate = queryFromDate
	result.ToDate = queryToDate

	if total > 0 {
		// [20231203] Use BBK timezone and confirmed_at (same as reporting)

		var newUserList []model.TransactionReportDaily
		selectedFields2 := "DATE(CONVERT_TZ(tb_user.created_at, 'UTC', '+07:00')) as of_date"
		selectedFields2 += ", COUNT(tb_user.id) as newuser_count"
		sql2 := r.db.Table("user as tb_user")
		sql2 = sql2.Select(selectedFields2)
		if queryFromDate != "" {
			startDateAtBkk, err := r.ParseBodBkk(queryFromDate)
			if err != nil {
				return result, err
			}
			sql2 = sql2.Where("tb_user.created_at >= ? ", startDateAtBkk)
		}
		if queryToDate != "" {
			endDateAtBkk, err := r.ParseEodBkk(queryToDate)
			if err != nil {
				return result, err
			}

			sql2 = sql2.Where("tb_user.created_at <=  ?", endDateAtBkk)
		}
		sql2 = sql2.Group("DATE(CONVERT_TZ(tb_user.created_at, 'UTC', '+07:00'))")
		if err := sql2.Scan(&newUserList).Error; err != nil {
			return result, err
		}
		// SET
		for _, v := range newUserList {
			ofDateStr := v.OfDate[0:10]
			for j, vv := range result.List {
				if ofDateStr == vv.OfDate {
					result.List[j].NewuserCount = v.NewuserCount
					break
				}
			}
		}

		var newUserDepositSameDayList []model.TransactionReportDaily
		selectedFields3 := "DATE(CONVERT_TZ(tb_user.created_at, 'UTC', '+07:00')) as of_date"
		selectedFields3 += ", COUNT(tb_transaction.id) AS first_day_deposit_count"
		selectedFields3 += ", SUM(tb_transaction.credit_amount) AS first_day_deposit_amount"
		sql3 := r.db.Table("user as tb_user")
		sql3 = sql3.Joins("INNER JOIN bank_transaction as tb_transaction ON tb_transaction.user_id = tb_user.id AND DATE(CONVERT_TZ(tb_user.created_at, 'UTC', '+07:00')) = DATE(CONVERT_TZ(tb_transaction.confirmed_at, 'UTC', '+07:00'))")
		sql3 = sql3.Select(selectedFields3)
		sql3 = sql3.Where("tb_transaction.is_first_deposit = 1")
		if queryFromDate != "" {
			startDateAtBkk, err := r.ParseBodBkk(queryFromDate)
			if err != nil {
				return result, err
			}
			sql3 = sql3.Where("tb_user.created_at >= ? ", startDateAtBkk)
		}
		if queryToDate != "" {
			endDateAtBkk, err := r.ParseEodBkk(queryToDate)
			if err != nil {
				return result, err
			}

			sql3 = sql3.Where("tb_user.created_at <=  ?", endDateAtBkk)
		}
		sql3 = sql3.Group("DATE(CONVERT_TZ(tb_user.created_at, 'UTC', '+07:00'))")
		if err := sql3.Scan(&newUserDepositSameDayList).Error; err != nil {
			return result, err
		}
		// SET
		for _, v := range newUserDepositSameDayList {
			ofDateStr := v.OfDate[0:10]
			for j, vv := range result.List {
				if ofDateStr == vv.OfDate {
					result.List[j].FirstDayDepositAmount = v.FirstDayDepositAmount
					result.List[j].FirstDayDepositCount = v.FirstDayDepositCount
					break
				}
			}
		}

		var transactionList []model.TransactionReportDaily
		selectedFields := "DATE(CONVERT_TZ(tb_transaction.confirmed_at, 'UTC', '+07:00')) as of_date"
		// selectedFields += ", SUM(CASE WHEN tb_transaction.transaction_type_id = 1 THEN tb_transaction.credit_amount ELSE 0 END) AS deposit_amount"
		// selectedFields += ", COUNT(CASE WHEN tb_transaction.transaction_type_id = 1 THEN tb_transaction.id ELSE NULL END) AS deposit_count"
		selectedFields += ", SUM(CASE WHEN tb_transaction.transaction_type_id = 1 AND tb_transaction.transaction_status_id = 5 THEN tb_transaction.credit_amount ELSE 0 END) AS deposit_amount"
		selectedFields += ", COUNT(CASE WHEN tb_transaction.transaction_type_id = 1 AND tb_transaction.transaction_status_id = 5 THEN tb_transaction.id ELSE NULL END) AS deposit_count"
		selectedFields += ", SUM(CASE WHEN tb_transaction.transaction_type_id = 1 AND tb_transaction.is_first_deposit = 1 THEN tb_transaction.credit_amount ELSE 0 END) AS first_deposit_amount"
		selectedFields += ", COUNT(CASE WHEN tb_transaction.transaction_type_id = 1 AND tb_transaction.is_first_deposit = 1 THEN tb_transaction.id ELSE NULL END) AS first_deposit_count"
		selectedFields += ", SUM(CASE WHEN tb_transaction.transaction_type_id = 2 AND tb_transaction.transaction_status_id = 12 THEN tb_transaction.credit_amount ELSE 0 END) AS withdraw_amount"
		selectedFields += ", COUNT(CASE WHEN tb_transaction.transaction_type_id = 2 AND tb_transaction.transaction_status_id = 12 THEN tb_transaction.id ELSE NULL END) AS withdraw_count"
		sql := r.db.Table("bank_transaction as tb_transaction")
		sql = sql.Select(selectedFields)
		sql = sql.Joins("INNER JOIN user as tb_user ON tb_user.id = tb_transaction.user_id")
		if queryFromDate != "" {
			startDateAtBkk, err := r.ParseBodBkk(queryFromDate)
			if err != nil {
				return result, err
			}
			sql = sql.Where("tb_transaction.confirmed_at >= ? ", startDateAtBkk)
		}
		if queryToDate != "" {
			endDateAtBkk, err := r.ParseEodBkk(queryToDate)
			if err != nil {
				return result, err
			}
			sql = sql.Where("tb_transaction.confirmed_at <=  ?", endDateAtBkk)
		}

		// NO PAGING IN SQL //
		sql = sql.Where("tb_transaction.confirmed_at IS NOT NULL")
		sql = sql.Where("tb_transaction.transaction_status_id IN (?,?)", model.TRANS_STATUS_WITHDRAW_SUCCESS, model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED)
		sql = sql.Group("DATE(CONVERT_TZ(tb_transaction.confirmed_at, 'UTC', '+07:00'))")
		// marketing_repository.go:316 SLOW SQL >= 200ms [3981.463ms] [rows:10]
		if err := sql.Scan(&transactionList).Error; err != nil {
			return result, err
		}
		// SET
		for _, v := range transactionList {
			ofDateStr := v.OfDate[0:10]
			for j, vv := range result.List {
				if ofDateStr == vv.OfDate {
					result.List[j].FirstDepositAmount = v.FirstDepositAmount
					result.List[j].FirstDepositCount = v.FirstDepositCount
					result.List[j].DepositAmount = v.DepositAmount
					result.List[j].DepositCount = v.DepositCount
					result.List[j].WithdrawAmount = v.WithdrawAmount
					result.List[j].WithdrawCount = v.WithdrawCount
					break
				}
			}
		}
	}

	result.Total = total

	return result, nil
}

func (r repo) getTransactionReportDailyTotal(req model.TransactionReportDailyQuery) (model.TransactionReportDailyResponse, error) {

	var result model.TransactionReportDailyResponse

	// DateType
	if req.OfDate != "" {
		// ofdate = 2021-01-01 => from 2021-01-01 to 2021-01-31
		startAt, err := time.Parse("2006-01-02", req.OfDate)
		if err != nil {
			return result, err
		}
		firstOfMonth := time.Date(startAt.Year(), startAt.Month(), 1, 0, 0, 0, 0, time.UTC)
		lastOfMonth := firstOfMonth.AddDate(0, 1, -1)
		req.FromDate = firstOfMonth.Format("2006-01-02")
		req.ToDate = lastOfMonth.Format("2006-01-02")
	}

	// swap req
	var queryFromDate string
	var queryToDate string

	// Generate Date
	fromDate, err := time.Parse("2006-01-02", req.FromDate)
	if err != nil {
		return result, err
	}
	toDate, err := time.Parse("2006-01-02", req.ToDate)
	if err != nil {
		return result, err
	}
	queryFromDate = fromDate.Format("2006-01-02")
	queryToDate = toDate.Format("2006-01-02")
	result.FromDate = queryFromDate
	result.ToDate = queryToDate

	// [20231203] Use BBK timezone and confirmed_at (same as reporting)

	var newUserList model.TransactionReportDaily
	selectedFields2 := "COUNT(tb_user.id) as newuser_count"
	sql2 := r.db.Table("user as tb_user")
	sql2 = sql2.Select(selectedFields2)
	if queryFromDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(queryFromDate)
		if err != nil {
			return result, err
		}
		sql2 = sql2.Where("tb_user.created_at >= ? ", startDateAtBkk)
	}
	if queryToDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(queryToDate)
		if err != nil {
			return result, err
		}

		sql2 = sql2.Where("tb_user.created_at <=  ?", endDateAtBkk)
	}
	if err := sql2.Take(&newUserList).Error; err != nil {
		return result, err
	}
	// SET
	result.NewuserCount = newUserList.NewuserCount

	var newUserDepositSameDayList model.TransactionReportDaily
	selectedFields3 := "COUNT(tb_transaction.id) AS first_day_deposit_count"
	selectedFields3 += ", SUM(tb_transaction.credit_amount) AS first_day_deposit_amount"
	sql3 := r.db.Table("user as tb_user")
	sql3 = sql3.Joins("INNER JOIN bank_transaction as tb_transaction ON tb_transaction.user_id = tb_user.id AND DATE(CONVERT_TZ(tb_user.created_at, 'UTC', '+07:00')) = DATE(CONVERT_TZ(tb_transaction.confirmed_at, 'UTC', '+07:00'))")
	sql3 = sql3.Select(selectedFields3)
	sql3 = sql3.Where("tb_transaction.is_first_deposit = 1")
	if queryFromDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(queryFromDate)
		if err != nil {
			return result, err
		}
		sql3 = sql3.Where("tb_user.created_at >= ? ", startDateAtBkk)
	}
	if queryToDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(queryToDate)
		if err != nil {
			return result, err
		}

		sql3 = sql3.Where("tb_user.created_at <=  ?", endDateAtBkk)
	}
	// marketing_repository.go:425 SLOW SQL >= 200ms [1753.284ms] [rows:1]
	if err := sql3.Scan(&newUserDepositSameDayList).Error; err != nil {
		return result, err
	}
	// SET
	result.FirstDayDepositAmount = newUserDepositSameDayList.FirstDayDepositAmount
	result.FirstDayDepositCount = newUserDepositSameDayList.FirstDayDepositCount

	var transactionList model.TransactionReportDaily
	selectedFields := "SUM(CASE WHEN tb_transaction.transaction_type_id = 1 AND tb_transaction.transaction_status_id = 5 THEN tb_transaction.credit_amount ELSE 0 END) AS deposit_amount"
	selectedFields += ", COUNT(CASE WHEN tb_transaction.transaction_type_id = 1 AND tb_transaction.transaction_status_id = 5 THEN tb_transaction.id ELSE NULL END) AS deposit_count"
	selectedFields += ", SUM(CASE WHEN tb_transaction.transaction_type_id = 1 AND tb_transaction.is_first_deposit = 1 THEN tb_transaction.credit_amount ELSE 0 END) AS first_deposit_amount"
	selectedFields += ", COUNT(CASE WHEN tb_transaction.transaction_type_id = 1 AND tb_transaction.is_first_deposit = 1 THEN tb_transaction.id ELSE NULL END) AS first_deposit_count"
	// old
	// selectedFields += ", SUM(CASE WHEN tb_transaction.transaction_type_id = 2 THEN tb_transaction.credit_amount ELSE 0 END) AS withdraw_amount"
	// selectedFields += ", COUNT(CASE WHEN tb_transaction.transaction_type_id = 2 THEN tb_transaction.id ELSE NULL END) AS withdraw_count"
	// new
	selectedFields += ", SUM(CASE WHEN tb_transaction.transaction_type_id = 2 AND tb_transaction.transaction_status_id = 12 THEN tb_transaction.credit_amount ELSE 0 END) AS withdraw_amount"
	selectedFields += ", COUNT(CASE WHEN tb_transaction.transaction_type_id = 2 AND tb_transaction.transaction_status_id = 12 THEN tb_transaction.id ELSE NULL END) AS withdraw_count"
	sql := r.db.Table("bank_transaction as tb_transaction")
	sql = sql.Select(selectedFields)
	sql = sql.Joins("INNER JOIN user as tb_user ON tb_user.id = tb_transaction.user_id")
	if queryFromDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(queryFromDate)
		if err != nil {
			return result, err
		}
		sql = sql.Where("tb_transaction.confirmed_at >= ? ", startDateAtBkk)
	}
	if queryToDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(queryToDate)
		if err != nil {
			return result, err
		}
		sql = sql.Where("tb_transaction.confirmed_at <=  ?", endDateAtBkk)
	}
	// NO PAGING IN SQL //
	sql = sql.Where("tb_transaction.confirmed_at IS NOT NULL")
	sql = sql.Where("tb_transaction.transaction_status_id IN (?,?)", model.TRANS_STATUS_WITHDRAW_SUCCESS, model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED)
	if err := sql.Scan(&transactionList).Error; err != nil {
		return result, err
	}
	// SET
	result.FirstDepositAmount = transactionList.FirstDepositAmount
	result.FirstDepositCount = transactionList.FirstDepositCount
	result.DepositAmount = transactionList.DepositAmount
	result.DepositCount = transactionList.DepositCount
	result.WithdrawAmount = transactionList.WithdrawAmount
	result.WithdrawCount = transactionList.WithdrawCount

	return result, nil
}
