package repository

import (
	"bytes"
	"cybergame-api/helper"
	"cybergame-api/model"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strings"
	"time"

	"gorm.io/gorm"
)

func NewPaymentGatewayRepository(db *gorm.DB) PaymentGatewayRepository {
	return &repo{db}
}

type PaymentGatewayRepository interface {
	GetDb() *gorm.DB
	// WEB
	GetDepositActivePaygateMerchantList() ([]model.PaygateAccountResponse, error)
	// MERCHANT
	GetPaygateMerchantById(id int64) (*model.PaygateMerchantResponse, error)
	// GetPaygateMerchantList(req model.PaygateMerchantListRequest) ([]model.PaygateMerchantNgResponse, int64, error)
	// CreatePaygateMerchant(body model.PaygateMerchantCreateBody) (*int64, error)
	// UpdatePaygateMerchant(id int64, body model.PaygateMerchantUpdateBody) error
	// DeletePaygateMerchant(id int64) error
	// SETTING
	// GetPaygateSetting() (*model.PaygateSettingResponse, error)
	// CreatePaygateSetting(body model.PaygateSettingCreateBody) (*int64, error)
	// UpdatePaygateSetting(id int64, body model.PaygateSettingUpdateBody) error
	// DeletePaygateSetting(id int64) error
	// MultipleProvider
	GetPaygateMerchantLimitList() ([]model.PaygateMerchantLimitResponse, error)
	GetNonActivePaygateMerchantOption() ([]model.SelectOptions, error)
	GetPaygateAccountById(id int64) (*model.PaygateAccountResponse, error)
	GetPaygateAccountList(req model.PaygateAccountListRequest) ([]model.PaygateAccountResponse, int64, error)
	GetPaygateAccountByProviderId(providerId int64) (*model.PaygateAccountResponse, error)
	CreatePaygateAccount(body model.PaygateAccountCreateBody) (*int64, error)
	UpdatePaygateAccount(id int64, body model.PaygateAccountUpdateBody) error
	DeletePaygateAccount(id int64) error
	GetPaygateWithdrawAccount() (*model.PaygateAccountResponse, error)
	// SingleWithdrawAccount
	SetSingleEnableWithdrawAccount(id int64) error
	// AdminLog
	GetPaygateAdminLogById(id int64) (*model.PaygateAdminLogResponse, error)
	GetPaygateAdminLogList(req model.PaygateAdminLogListRequest) ([]model.PaygateAdminLogResponse, int64, error)
	CreatePaygateAdminLog(body model.PaygateAdminLogCreateBody) (*int64, error)
	UpdatePaygateAdminLog(id int64, body model.PaygateAdminLogUpdateBody) error
	// SystemLog
	CreatePaygateSystemLog(body model.PaygateSystemLogCreateBody) (*int64, error)
	UpdatePaygateSystemLog(id int64, body model.PaygateSystemLogUpdateBody) error
	// HENG
	CheckPaygateHeng(setting model.PaygateHengSettingResponse) (*model.PaygateHengCheckRemoteResponse, error)
	LoginPaygateHeng(setting model.PaygateHengSettingResponse) (*model.PaygateHengLoginRemoteResponse, error)
	GetPaygateHengBalance(accessToken string, setting model.PaygateHengSettingResponse) (*model.PaygateHengBalanceRemoteResponse, error)
	// GetPaygateHengOrderList(accessToken string, req model.PaygateHengOrderListRequest) ([]model.PaygateHengOrderResponse, int64, error)
	CreatePaygateHengOrder(accessToken string, setting model.PaygateHengSettingResponse, body model.PaygateHengOrderCreateRemoteRequest) (*model.PaygateHengOrderCreateRemoteResponse, error)
	UpdatePaygateHengCallbackUrl(accessToken string, setting model.PaygateHengSettingResponse) (*model.PaygateHengCheckRemoteResponse, error)
	// HENG-REPORT
	GetHengOrderReportList(req model.PaygateOrderReportListRequest) ([]model.HengOrderReportResponse, int64, error)
	GetHengCallbackReportResponse(req model.HengCallbackReportListRequest) ([]model.HengCallbackReportResponse, int64, error)
	// PAYONEX-REPORT
	GetRawPayonexPendingDepositOrderById(id int64) (*model.PayonexOrderResponse, error)
	IgnoreDbPayonexPendingDepositOrder(id int64, actionBy int64) error
	GetPayonexOrderReportList(req model.PaygateOrderReportListRequest) ([]model.PaygateOrderReportResponse, int64, error)
	// JBPay-REPORT
	GetRawJbpayPendingDepositOrderById(id int64) (*model.JbpayOrderResponse, error)
	IgnoreDbJbpayPendingDepositOrder(id int64, actionBy int64) error
	GetJbpayOrderReportList(req model.PaygateOrderReportListRequest) ([]model.PaygateOrderReportResponse, int64, error)
	// Pompay-REPORT
	GetRawPompayPendingDepositOrderById(id int64) (*model.PompayOrderResponse, error)
	IgnoreDbPompayPendingDepositOrder(id int64, actionBy int64) error
	GetPompayOrderReportList(req model.PaygateOrderReportListRequest) ([]model.PaygateOrderReportResponse, int64, error)
	// Paymentco-REPORT
	GetRawPaymentcoPendingDepositOrderById(id int64) (*model.PaymentcoOrderResponse, error)
	IgnoreDbPaymentcoPendingDepositOrder(id int64, actionBy int64) error
	GetPaymentcoOrderReportList(req model.PaygateOrderReportListRequest) ([]model.PaygateOrderReportResponse, int64, error)
	// Zappay-REPORT
	GetRawZappayPendingDepositOrderById(id int64) (*model.ZappayOrderResponse, error)
	IgnoreDbZappayPendingDepositOrder(id int64, actionBy int64) error
	GetZappayOrderReportList(req model.PaygateOrderReportListRequest) ([]model.PaygateOrderReportResponse, int64, error)
	// Onepay-REPORT
	GetRawOnepayPendingDepositOrderById(id int64) (*model.OnepayOrderResponse, error)
	IgnoreDbOnepayPendingDepositOrder(id int64, actionBy int64) error
	GetOnepayOrderReportList(req model.PaygateOrderReportListRequest) ([]model.PaygateOrderReportResponse, int64, error)
	// Flashpay-REPORT
	GetRawFlashpayPendingDepositOrderById(id int64) (*model.FlashpayOrderResponse, error)
	IgnoreDbFlashpayPendingDepositOrder(id int64, actionBy int64) error
	GetFlashpayOrderReportList(req model.PaygateOrderReportListRequest) ([]model.PaygateOrderReportResponse, int64, error)
	// Bizpay-REPORT
	GetRawBizpayPendingDepositOrderById(id int64) (*model.BizpayOrderResponse, error)
	IgnoreDbBizpayPendingDepositOrder(id int64, actionBy int64) error
	GetBizpayOrderReportList(req model.PaygateOrderReportListRequest) ([]model.PaygateOrderReportResponse, int64, error)
	// Sugarpay-REPORT
	GetRawSugarpayPendingDepositOrderById(id int64) (*model.SugarpayOrderResponse, error)
	IgnoreDbSugarpayPendingDepositOrder(id int64, actionBy int64) error
	GetSugarpayOrderReportList(req model.PaygateOrderReportListRequest) ([]model.PaygateOrderReportResponse, int64, error)
	// Zmanpay-REPORT
	GetRawZmanpayPendingDepositOrderById(id int64) (*model.ZmanpayOrderResponse, error)
	IgnoreDbZmanpayPendingDepositOrder(id int64, actionBy int64) error
	GetZmanpayOrderReportList(req model.PaygateOrderReportListRequest) ([]model.PaygateOrderReportResponse, int64, error)
	// PostmanPay-REPORT
	GetRawPostmanPayPendingDepositOrderById(id int64) (*model.PostmanPayOrderResponse, error)
	IgnoreDbPostmanPayPendingDepositOrder(id int64, actionBy int64) error
	GetPostmanPayOrderReportList(req model.PaygateOrderReportListRequest) ([]model.PaygateOrderReportResponse, int64, error)
	// Mazepay-REPORT
	GetRawMazepayPendingDepositOrderById(id int64) (*model.MazepayOrderResponse, error)
	IgnoreDbMazepayPendingDepositOrder(id int64, actionBy int64) error
	GetMazepayOrderReportList(req model.PaygateOrderReportListRequest) ([]model.PaygateOrderReportResponse, int64, error)
	// Meepay-REPORT
	GetRawMeepayPendingDepositOrderById(id int64) (*model.MeepayOrderResponse, error)
	IgnoreDbMeepayPendingDepositOrder(id int64, actionBy int64) error
	GetMeepayOrderReportList(req model.PaygateOrderReportListRequest) ([]model.PaygateOrderReportResponse, int64, error)
	// Local
	GetDbPaygateHengAccessToken() (*model.PaygateHengToken, error)
	CreateDbPaygateHengAccessToken(body model.PaygateHengTokenCreateBody) (*int64, error)
	CreateDbPaygateHengOrder(body model.PaygateHengOrderCreateBody) (*int64, error)
	UpdateCreatePaygateHengOrderError(id int64, remark string) error
	UpdatePaygateHengOrderRemark(id int64, remark string) error
	UpdatePaygateHengOrderBank(id int64, transId int64) error
	UpdateDbPaygateHengOrder(id int64, body model.PaygateHengOrderUpdateBody) error
	GetDbPaygateHengOrderList(req model.PaygateHengOrderListRequest) ([]model.PaygateHengOrderResponse, int64, error)
	GetDbPaygateHengOrderById(id int64) (*model.PaygateHengOrderResponse, error)
	GetRawPaygateHengPendingDepositOrderById(id int64) (*model.PaygateHengOrder, error)
	CreatePaygateHengWebhook(body model.PaygateHengWebhookCreateBody) (*int64, error)
	ApproveDbPaygateHengOrder(id int64, updateBody model.PaygateHengOrderUpdatePaymentBody) error
	IgnoreDbPaygateHengPendingDepositOrder(id int64) error
	// Heng User
	GetHengUserById(userId int64) (*model.HengUser, error)
	CreateHengUser(body model.HengUserCreateBody) (*int64, error)
	UpdateHengUser(userId int64, body model.HengUserUpdateBody) error
	GetHengUserBankDetailById(userId int64) (*model.HengUserBankDetailResponse, error)
	// LUCKYTHAI-REMOTE
	LuckyThaiDeposit(setting model.PaygateAccountResponse, req model.LuckyThaiDepositCreateRemoteRequest) (*model.LuckyThaiDepositCreateRemoteResponse, error)
	LuckyThaiWithdraw(setting model.PaygateAccountResponse, req model.LuckyThaiWithdrawCreateRemoteRequest) (*model.LuckyThaiWithdrawCreateRemoteResponse, error)
	LuckyThaiCheckBalance(setting model.PaygateAccountResponse) (*model.LuckyThaiCheckBalanceRemoteResponse, error)
	LuckyThaiGetOrder(setting model.PaygateAccountResponse, orderNo string) (*model.LuckyThaiGetOrderRemoteResponse, error)
	// LUCKYTHAI-DB
	CreateLuckyThaiWebhook(body model.LuckyThaiWebhookCreateBody) (*int64, error)
	GetDbLuckyThaiOrderList(req model.LuckyThaiOrderListRequest) ([]model.LuckyThaiOrderResponse, int64, error)
	GetDbLuckyThaiOrderById(id int64) (*model.LuckyThaiOrderResponse, error)
	GetDbLuckyThaiOrderByRefId(refId int64) (*model.LuckyThaiOrderResponse, error)
	CreateDbLuckyThaiOrder(body model.LuckyThaiOrderCreateBody) (*int64, error)
	UpdateDbLuckyThaiOrderError(id int64, remark string) error
	UpdateDbLuckyThaiOrder(id int64, body model.LuckyThaiOrderUpdateBody) error
	ApproveDbLuckyThaiOrder(id int64, webhookStatus string) error
	// REF-BankTransaction-Deposit
	GetUserBankDetailById(id int64) (*model.UserBankDetailBody, error)
	GetDepositCurrentProcessingUserPromotion(userId int64) (*model.PromotionWebUserByUserIdResponse, error)
	InsertBankTransaction(data model.BankTransactionCreateBody) (*int64, error)
	IncreaseUserCredit(body model.UserTransactionCreateRequest) (*model.UserTransactionCreateResponse, error)
	UpdateDepositTransactionStatusFromAgent(transId int64, body model.UserTransactionCreateResponse) error
	UpdateAutoProcessTimer(timer string, id int64) error
	// ConfirmDeposit
	GetBankTransactionById(id int64) (*model.BankTransaction, error)
	CreateTransactionAction(data model.CreateBankTransactionActionBody) (*int64, error)
	ConfirmPendingDepositTransaction(id int64, body model.BankDepositTransactionConfirmBody) error
	RollbackTransactionAction(actionId int64) error
	// REF-Websocket
	// WebSocket(reqAlert model.WebScoket) error
	// REF-MemberCode
	GetUserForGenMember(id int64) (*model.UserDetail, error)
	GetAgentInfo() (*model.AgentInfo, error)
	IncrementTotal() error
	UpdateMemberCode(userId int64, data model.UserUpdateMemberAndRef) error
	AgcRegister(data model.AgcRegister) error
	AmbRegister(data model.AmbRegister) error
	GetMemberById(id int64) (*model.Member, error)
	IsFirstDeposit(userId int64) bool
	UpdateGenMemberCodeToAffilate(userId int64) error
	CreateAffiliateMember(refBy int64, userId int64) error
	CronjobDeletePaygateHengWebhook() error
	// REF-RC
	GetRaceActionIdByActionKey(actionKey string) (*int64, error)
	CreateRaceCondition(body model.RaceActionCreateBody) (int64, error)
	// REF-BANK
	GetLastestBankAdminManualDepositTransaction(userId int64) (*model.BankTransaction, error)
	ShowUserTransaction(req model.UserTransactionShowUpdate) error
	UpdateTransactionStatusTransferingToSuccess(id int64, secondUsed string) error
	RollbackTransactionStatusTransferingToConfirmed(id int64) error
	// REF-SysLog
	CreateSystemLog(body model.SystemLogCreateBody) (*int64, error)
	// REF-USER
	GetUserById(id int64) (*model.UserResponse, error)
	GetPaygateMerchantOption() ([]model.SelectOptions, error)
	GetLastestAnyPaygateDeposit(paymentId int64, userId int64) (*model.LastestAnyPaygateDeposit, error)

	// [TIER]
	IncreaseUserTierDepositAmount(userId int64, amount float64) error
}

func (r repo) GetDepositActivePaygateMerchantList() ([]model.PaygateAccountResponse, error) {

	var list []model.PaygateAccountResponse

	selectedFields := "*"
	if err := r.db.Table("paygate_account as tb_account").
		Select(selectedFields).
		Where("tb_account.is_deposit_enabled = ?", true).
		Where("tb_account.deleted_at IS NULL").
		Scan(&list).
		Error; err != nil {
		return nil, err
	}

	for i, record := range list {
		if record.ProviderId == model.PAYGATE_MERCHANT_ID_PAYONEX {
			if list[i].PaymentDepositMinimum < model.PAYONEX_DEPOSIT_AMOUNT_MINIMUM {
				list[i].PaymentDepositMinimum = model.PAYONEX_DEPOSIT_AMOUNT_MINIMUM
			}
			if list[i].PaymentDepositMaximum > model.PAYONEX_DEPOSIT_AMOUNT_MAXIMUM {
				list[i].PaymentDepositMaximum = model.PAYONEX_DEPOSIT_AMOUNT_MAXIMUM
			}
		} else if record.ProviderId == model.PAYGATE_MERCHANT_ID_JBPAY {
			if list[i].PaymentDepositMinimum < model.JBPAY_DEPOSIT_AMOUNT_MINIMUM {
				list[i].PaymentDepositMinimum = model.JBPAY_DEPOSIT_AMOUNT_MINIMUM
			}
			if list[i].PaymentDepositMaximum > model.JBPAY_DEPOSIT_AMOUNT_MAXIMUM {
				list[i].PaymentDepositMaximum = model.JBPAY_DEPOSIT_AMOUNT_MAXIMUM
			}
		} else if record.ProviderId == model.PAYGATE_MERCHANT_ID_POMPAY {
			if list[i].PaymentDepositMinimum < model.POMPAY_DEPOSIT_AMOUNT_MINIMUM {
				list[i].PaymentDepositMinimum = model.POMPAY_DEPOSIT_AMOUNT_MINIMUM
			}
			if list[i].PaymentDepositMaximum > model.POMPAY_DEPOSIT_AMOUNT_MAXIMUM {
				list[i].PaymentDepositMaximum = model.POMPAY_DEPOSIT_AMOUNT_MAXIMUM
			}
		} else if record.ProviderId == model.PAYGATE_MERCHANT_ID_ZAPPAY {
			if list[i].PaymentDepositMinimum < model.ZAPPAY_DEPOSIT_AMOUNT_MAXIMUM {
				list[i].PaymentDepositMinimum = model.ZAPPAY_DEPOSIT_AMOUNT_MINIMUM
			}
			if list[i].PaymentDepositMaximum > model.ZAPPAY_DEPOSIT_AMOUNT_MAXIMUM {
				list[i].PaymentDepositMaximum = model.ZAPPAY_DEPOSIT_AMOUNT_MAXIMUM
			}
		} else if record.ProviderId == model.PAYGATE_MERCHANT_ID_BIZPAY {
			if list[i].PaymentDepositMinimum < model.BIZPAY_DEFMIN_DEPOSIT_AMOUNT {
				list[i].PaymentDepositMinimum = model.BIZPAY_DEFMIN_DEPOSIT_AMOUNT
			}
			if list[i].PaymentDepositMaximum > model.BIZPAY_DEFMAX_DEPOSIT_AMOUNT {
				list[i].PaymentDepositMaximum = model.BIZPAY_DEFMAX_DEPOSIT_AMOUNT
			}
		} else if record.ProviderId == model.PAYGATE_MERCHANT_ID_SUGARPAY {
			if list[i].PaymentDepositMinimum < model.SUGARPAY_DEFMIN_DEPOSIT_AMOUNT {
				list[i].PaymentDepositMinimum = model.SUGARPAY_DEFMIN_DEPOSIT_AMOUNT
			}
			if list[i].PaymentDepositMaximum > model.SUGARPAY_DEFMAX_DEPOSIT_AMOUNT {
				list[i].PaymentDepositMaximum = model.SUGARPAY_DEFMAX_DEPOSIT_AMOUNT
			}
		} else if record.ProviderId == model.PAYGATE_MERCHANT_ID_ZMANPAY {
			if list[i].PaymentDepositMinimum < model.ZMANPAY_DEFMIN_DEPOSIT_AMOUNT {
				list[i].PaymentDepositMinimum = model.ZMANPAY_DEFMIN_DEPOSIT_AMOUNT
			}
			if list[i].PaymentDepositMaximum > model.ZMANPAY_DEFMAX_DEPOSIT_AMOUNT {
				list[i].PaymentDepositMaximum = model.ZMANPAY_DEFMAX_DEPOSIT_AMOUNT
			}
		} else if record.ProviderId == model.PAYGATE_MERCHANT_ID_POSTMANPAY {
			if list[i].PaymentDepositMinimum < model.POSTMANPAY_DEFMIN_DEPOSIT_AMOUNT {
				list[i].PaymentDepositMinimum = model.POSTMANPAY_DEFMIN_DEPOSIT_AMOUNT
			}
			if list[i].PaymentDepositMaximum > model.POSTMANPAY_DEFMAX_DEPOSIT_AMOUNT {
				list[i].PaymentDepositMaximum = model.POSTMANPAY_DEFMAX_DEPOSIT_AMOUNT
			}
		} else if record.ProviderId == model.PAYGATE_MERCHANT_ID_MAZEPAY {
			if list[i].PaymentDepositMinimum < model.MAZEPAY_DEFMIN_DEPOSIT_AMOUNT {
				list[i].PaymentDepositMinimum = model.MAZEPAY_DEFMIN_DEPOSIT_AMOUNT
			}
			if list[i].PaymentDepositMaximum > model.MAZEPAY_DEFMAX_DEPOSIT_AMOUNT {
				list[i].PaymentDepositMaximum = model.MAZEPAY_DEFMAX_DEPOSIT_AMOUNT
			}
		} else if record.ProviderId == model.PAYGATE_MERCHANT_ID_MEEPAY {
			if list[i].PaymentDepositMinimum < model.MEEPAY_DEFMIN_DEPOSIT_AMOUNT {
				list[i].PaymentDepositMinimum = model.MEEPAY_DEFMIN_DEPOSIT_AMOUNT
			}
			if list[i].PaymentDepositMaximum > model.MEEPAY_DEFMAX_DEPOSIT_AMOUNT {
				list[i].PaymentDepositMaximum = model.MEEPAY_DEFMAX_DEPOSIT_AMOUNT
			}
		}

		// CALLBACK URL for Copy //
		if record.ProviderId == model.PAYGATE_MERCHANT_ID_JBPAY {
			// webhookRoute.POST("/jbpayment/repay-callback", handler.createJbpayDepositWebhook)
			// webhookRoute.POST("/jbpayment/loan-callback", handler.createJbpayWithdrawWebhook)
			webhookDomain := os.Getenv("CYBERGAME_WEBHOOK_ENDPOINT")
			repayCallbackurl := fmt.Sprintf("%s/jbpayment/repay-callback", webhookDomain)
			list[i].CallBackList = append(list[i].CallBackList, model.PaygateMerchantCallbackUrl{
				Label: "JBPAY_REPAY_CALLBACK",
				Url:   repayCallbackurl,
			})
			// JBPAY_LOAN_CALLBACK
			loanCallbackurl := fmt.Sprintf("%s/jbpayment/loan-callback", webhookDomain)
			list[i].CallBackList = append(list[i].CallBackList, model.PaygateMerchantCallbackUrl{
				Label: "JBPAY_LOAN_CALLBACK",
				Url:   loanCallbackurl,
			})
		} else if record.ProviderId == model.PAYGATE_MERCHANT_ID_ZAPPAY {
			// webhookRoute.POST("/zappay/repay-callback", handler.createZappayDepositWebhook)
			// webhookRoute.POST("/zappay/loan-callback", handler.createZappayWithdrawWebhook)
			webhookDomain := os.Getenv("CYBERGAME_WEBHOOK_ENDPOINT")
			repayCallbackurl := fmt.Sprintf("%s/zappay/repay-callback", webhookDomain)
			list[i].CallBackList = append(list[i].CallBackList, model.PaygateMerchantCallbackUrl{
				Label: "ZAPPAY_REPAY_CALLBACK",
				Url:   repayCallbackurl,
			})
			// ZAPPAY_LOAN_CALLBACK
			loanCallbackurl := fmt.Sprintf("%s/zappay/loan-callback", webhookDomain)
			list[i].CallBackList = append(list[i].CallBackList, model.PaygateMerchantCallbackUrl{
				Label: "ZAPPAY_LOAN_CALLBACK",
				Url:   loanCallbackurl,
			})
		}
	}

	return list, nil
}

func (r repo) GetPaygateAdminLogById(id int64) (*model.PaygateAdminLogResponse, error) {

	var record model.PaygateAdminLogResponse

	selectedFields := "*"
	if err := r.db.Table("paygate_admin_log as tb_log").
		Select(selectedFields).
		Where("tb_log.id = ?", id).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetPaygateAdminLogList(req model.PaygateAdminLogListRequest) ([]model.PaygateAdminLogResponse, int64, error) {

	var list []model.PaygateAdminLogResponse
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("paygate_admin_log as tb_log")
	count = count.Select("tb_log.id")

	if err = count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "*"
		query := r.db.Table("paygate_admin_log as tb_log")
		query = query.Select(selectedFields)

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}

	return list, total, nil
}

func (r repo) CreatePaygateAdminLog(body model.PaygateAdminLogCreateBody) (*int64, error) {

	if err := r.db.Table("paygate_admin_log").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) UpdatePaygateAdminLog(id int64, body model.PaygateAdminLogUpdateBody) error {

	if err := r.db.Table("paygate_admin_log").Where("id = ?", id).Updates(body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) CreatePaygateSystemLog(body model.PaygateSystemLogCreateBody) (*int64, error) {

	if err := r.db.Table("paygate_system_log").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) UpdatePaygateSystemLog(id int64, body model.PaygateSystemLogUpdateBody) error {

	if err := r.db.Table("paygate_system_log").Where("id = ?", id).Updates(body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) CheckPaygateHeng(setting model.PaygateHengSettingResponse) (*model.PaygateHengCheckRemoteResponse, error) {

	// log.Println("CheckPaygateHeng req ------> ")

	// POST "https://api.amulet168.co/api/v1/check"
	url := fmt.Sprintf("%s/check", setting.ApiEndPoint)
	// "apiEndPoint": "https://api.amulet168.co/api/v2", if endwith /v2 use url from v2
	if setting.ApiEndPoint[len(setting.ApiEndPoint)-3:] == "/v2" {
		return nil, errors.New("V2_NOT_SUPPORT")
	}
	// log.Println("CheckPaygateHeng url ------> ", url)

	client := &http.Client{}
	client.Timeout = 3 * time.Second
	reqExternal, _ := http.NewRequest("GET", url, nil)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("Accept", "application/json")

	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}
	if response.StatusCode != 200 {
		log.Println(response.StatusCode)
		log.Println(string(responseData))
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	}

	var result model.PaygateHengCheckRemoteResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("CheckPaygateHeng resp.Body ------> ", string(responseData))
		log.Println("Unmarshal.Err ------> ", errJson)
		return nil, errJson
	}
	if result.ResponseCode != "000" {
		log.Println("CheckPaygateHeng result ------> ", helper.StructJson(result))
		return nil, errors.New(result.ResponseMesg)
	}
	return &result, nil
}

func (r repo) LoginPaygateHeng(setting model.PaygateHengSettingResponse) (*model.PaygateHengLoginRemoteResponse, error) {

	remoteReqBody := make(map[string]interface{})
	remoteReqBody["user_name"] = setting.Username
	remoteReqBody["password"] = setting.Password

	// log.Println("LoginPaygateHeng req ------> ", remoteReqBody)

	// POST "https://api.amulet168.co/api/v1/login"
	// POST "https://api.amulet168.co/api/v2/auth/login"
	url := fmt.Sprintf("%s/login", setting.ApiEndPoint)
	// "apiEndPoint": "https://api.amulet168.co/api/v2", if endwith /v2 use url from v2
	if setting.ApiEndPoint[len(setting.ApiEndPoint)-3:] == "/v2" {
		url = fmt.Sprintf("%s/auth/login", setting.ApiEndPoint)
	}
	log.Println("LoginPaygateHeng url ------> ", url)

	client := &http.Client{}
	client.Timeout = 3 * time.Second
	jsonBody, _ := json.Marshal(remoteReqBody)
	reqBody := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest("POST", url, reqBody)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("Accept", "application/json")

	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}
	if response.StatusCode != 200 {
		log.Println(response.StatusCode)
		log.Println(string(responseData))
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	}

	var result model.PaygateHengLoginRemoteResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("LoginPaygateHeng resp.Body ------> ", string(responseData))
		log.Println("Unmarshal.Err ------> ", errJson)
		return nil, errJson
	}
	if result.AccessToken == "" {
		log.Println("LoginPaygateHeng result ------> ", helper.StructJson(result))
		return nil, errors.New("INVALID_ACCESS_TOKEN")
	}
	return &result, nil
}

func (r repo) UpdatePaygateHengCallbackUrl(accessToken string, setting model.PaygateHengSettingResponse) (*model.PaygateHengCheckRemoteResponse, error) {

	log.Println("UpdatePaygateHengCallbackUrl req.setting ------> ", setting.CallbackUrl)

	remoteReqBody := make(map[string]interface{})
	remoteReqBody["callback_url"] = setting.CallbackUrl
	remoteReqBody["basic_user"] = setting.Username
	remoteReqBody["basic_password"] = setting.Password

	method := "POST"
	// POST "https://api.amulet168.co/api/v1/user/callback/update"
	// PUT {{BASE-URL}}/api/v2/auth/user/callback/update
	url := fmt.Sprintf("%s/user/callback/update", setting.ApiEndPoint)
	// "apiEndPoint": "https://api.amulet168.co/api/v2", if endwith /v2 use url from v2
	if setting.ApiEndPoint[len(setting.ApiEndPoint)-3:] == "/v2" {
		method = "PUT"
		url = fmt.Sprintf("%s/auth/user/callback/update", setting.ApiEndPoint)
	}
	log.Println("UpdatePaygateHengCallbackUrl url ------> ", url)

	client := &http.Client{}
	client.Timeout = 3 * time.Second
	jsonBody, _ := json.Marshal(remoteReqBody)
	reqBody := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest(method, url, reqBody)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("Accept", "application/json")
	// req.Header.Add("x-access-token", "xxx.yyy.zzz")
	reqExternal.Header.Set("X-Access-Token", accessToken)
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}
	if response.StatusCode != 200 {
		log.Println(response.StatusCode)
		log.Println(string(responseData))
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	}

	var result model.PaygateHengCheckRemoteResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("UpdatePaygateHengCallbackUrl resp.Body ------> ", string(responseData))
		log.Println("Unmarshal.Err ------> ", errJson)
		return nil, errJson
	}
	if result.ResponseCode != "000" {
		log.Println("UpdatePaygateHengCallbackUrl result ------> ", helper.StructJson(result))
		return nil, errors.New(result.ResponseMesg)
	}
	return &result, nil
}

func (r repo) GetPaygateHengBalance(accessToken string, setting model.PaygateHengSettingResponse) (*model.PaygateHengBalanceRemoteResponse, error) {

	log.Println("GetPaygateHengBalance req.setting.ShopName ------> ", setting.ShopName)

	// POST "https://api.amulet168.co/api/v1/user/balances"
	method := "POST"
	url := fmt.Sprintf("%s/user/balances", setting.ApiEndPoint)
	// "apiEndPoint": "https://api.amulet168.co/api/v2", if endwith /v2 use url from v2
	if setting.ApiEndPoint[len(setting.ApiEndPoint)-3:] == "/v2" {
		// method = "GET" <pre>Cannot GET /api/v2/auth/user/balances</pre>
		// url = fmt.Sprintf("%s/auth/user/balances", setting.ApiEndPoint)
		return nil, errors.New("V2_NOT_SUPPORT")
	}
	log.Println("GetPaygateHengBalance url ------> ", url)

	client := &http.Client{}
	client.Timeout = 3 * time.Second
	reqExternal, _ := http.NewRequest(method, url, nil)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("Accept", "application/json")
	// req.Header.Add("x-access-token", "xxx.yyy.zzz")
	reqExternal.Header.Set("X-Access-Token", accessToken)
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}
	if response.StatusCode != 200 {
		log.Println(response.StatusCode)
		log.Println(string(responseData))
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	}

	var result model.PaygateHengBalanceRemoteResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("GetPaygateHengBalance resp.Body ------> ", string(responseData))
		log.Println("Unmarshal.Err ------> ", errJson)
		return nil, errJson
	}
	if result.ResponseCode != "000" {
		log.Println("GetPaygateHengBalance result ------> ", helper.StructJson(result))
		return nil, errors.New(result.ResponseMesg)
	}
	return &result, nil
}

func (r repo) CreatePaygateHengOrder(accessToken string, setting model.PaygateHengSettingResponse, body model.PaygateHengOrderCreateRemoteRequest) (*model.PaygateHengOrderCreateRemoteResponse, error) {

	body.ShopName = setting.ShopName

	// log.Println("CreatePaygateHengOrder req ------> ", helper.StructJson(body))

	// POST "https://api.amulet168.co/api/v1/CreateOrder/text"
	// POST {{BASE-URL}}/api/v2/order/create/text
	url := fmt.Sprintf("%s/CreateOrder/text", setting.ApiEndPoint)
	// "apiEndPoint": "https://api.amulet168.co/api/v2", if endwith /v2 use url from v2
	if setting.ApiEndPoint[len(setting.ApiEndPoint)-3:] == "/v2" {
		url = fmt.Sprintf("%s/order/create/text", setting.ApiEndPoint)
	}
	log.Println("CreatePaygateHengOrder url ------> ", url)

	client := &http.Client{}
	client.Timeout = 3 * time.Second
	jsonBody, _ := json.Marshal(body)
	reqBody := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest("POST", url, reqBody)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("Accept", "application/json")
	// req.Header.Add("x-access-token", "xxx.yyy.zzz")
	reqExternal.Header.Set("X-Access-Token", accessToken)
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}
	if response.StatusCode != 200 {
		log.Println(response.StatusCode)
		log.Println(string(responseData))
		var errorResp model.PaygateHengCheckRemoteResponse
		errJson := json.Unmarshal(responseData, &errorResp)
		if errJson != nil {
			return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
		}
		return nil, errors.New(errorResp.ResponseMesg)
	}

	var result model.PaygateHengOrderCreateRemoteResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("CreatePaygateHengOrder resp.Body ------> ", string(responseData))
		log.Println("Unmarshal.Err ------> ", errJson)
		return nil, errJson
	}
	if result.Status.Code != "000" {
		log.Println("CreatePaygateHengOrder result ------> ", helper.StructJson(result))
		return nil, errors.New(result.Status.Message)
	}
	return &result, nil
}

func (r repo) GetDbPaygateHengOrderList(req model.PaygateHengOrderListRequest) ([]model.PaygateHengOrderResponse, int64, error) {

	var list []model.PaygateHengOrderResponse
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("paygate_heng_order as tb_order")
	count = count.Select("tb_order.id")
	if req.UserId != nil {
		count = count.Where("tb_order.user_id = ?", req.UserId)
	}
	if req.OrderNo != "" {
		count = count.Where("tb_order.order_no = ?", req.OrderNo)
	}
	if req.TransactionNo != "" {
		count = count.Where("tb_order.transaction_no = ?", req.TransactionNo)
	}
	if req.Amount != "" {
		count = count.Where("tb_order.amount = ?", req.Amount)
	}
	if req.Status != "" {
		count = count.Where("tb_order.transaction_status = ?", req.Status)
	}

	if err = count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.order_no AS order_no, tb_order.shop_name AS shop_name, tb_order.amount AS amount"
		selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
		selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"

		query := r.db.Table("paygate_heng_order as tb_order")
		query = query.Select(selectedFields)
		if req.UserId != nil {
			query = query.Where("tb_order.user_id = ?", req.UserId)
		}
		if req.OrderNo != "" {
			query = query.Where("tb_order.order_no = ?", req.OrderNo)
		}
		if req.TransactionNo != "" {
			query = query.Where("tb_order.transaction_no = ?", req.TransactionNo)
		}
		if req.Amount != "" {
			query = query.Where("tb_order.amount = ?", req.Amount)
		}
		if req.Status != "" {
			query = query.Where("tb_order.transaction_status = ?", req.Status)
		}

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetDbPaygateHengOrderById(id int64) (*model.PaygateHengOrderResponse, error) {

	var record model.PaygateHengOrderResponse

	selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.order_no AS order_no, tb_order.shop_name AS shop_name, tb_order.amount AS amount"
	selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
	selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"

	if err := r.db.Table("paygate_heng_order as tb_order").
		Select(selectedFields).
		Where("tb_order.id = ?", id).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetRawPaygateHengPendingDepositOrderById(id int64) (*model.PaygateHengOrder, error) {

	var record model.PaygateHengOrder

	selectedFields := "*"

	if err := r.db.Table("paygate_heng_order as tb_order").
		Select(selectedFields).
		Where("tb_order.id = ?", id).
		Where("tb_order.transaction_status = ?", "SUCCESS").
		Where("tb_order.bank_transaction_status = ?", "รอดำเนินการ").
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetDbPaygateHengAccessToken() (*model.PaygateHengToken, error) {

	var result model.PaygateHengToken
	expireAt := time.Now().Add(time.Minute * 2)

	if err := r.db.Table("paygate_heng_token").
		Where("expire_at > ?", expireAt).
		Take(&result).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (r repo) CreateDbPaygateHengAccessToken(body model.PaygateHengTokenCreateBody) (*int64, error) {

	if err := r.db.Table("paygate_heng_token").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) CreateDbPaygateHengOrder(body model.PaygateHengOrderCreateBody) (*int64, error) {

	if err := r.db.Table("paygate_heng_order").Create(&body).Error; err != nil {
		return nil, err
	}

	// Update order_no if Empty in HPG{YYYYMMDD}{ID} //
	// orderNo := fmt.Sprintf("HPG%v%v", time.Now().Format("200601"), body.Id)
	// [20240209] Update order_no if Empty in {AGENT_NAME}{YYMM}{ID} //
	agentName := os.Getenv("AGENT_NAME")
	if agentName == "" {
		agentName = "HPG"
	} else {
		agentName = strings.ToUpper(agentName)
	}
	orderNo := fmt.Sprintf("%v%v%v", agentName, time.Now().Format("0601"), body.Id)
	if body.OrderNo == "" {
		updateBody := make(map[string]interface{})
		updateBody["order_no"] = orderNo
		if err := r.db.Table("paygate_heng_order").Where("id = ?", body.Id).Updates(updateBody).Error; err != nil {
			return nil, err
		}
	}
	return &body.Id, nil
}

func (r repo) UpdateCreatePaygateHengOrderError(id int64, remark string) error {

	updateBody := map[string]interface{}{
		"transaction_status": "ERROR",
		"remark":             remark,
	}
	sql := r.db.Table("paygate_heng_order").Where("id = ?", id).Where("transaction_status = ?", "PENDING")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdatePaygateHengOrderRemark(id int64, remark string) error {

	updateBody := map[string]interface{}{
		"remark": remark,
	}
	sql := r.db.Table("paygate_heng_order").Where("id = ?", id).Where("remark IS NULL")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdatePaygateHengOrderBank(id int64, transId int64) error {

	updateBody := map[string]interface{}{
		"bank_transaction_id":     transId,
		"bank_transaction_status": "ฝากสำเร็จ",
	}
	sql := r.db.Table("paygate_heng_order").Where("id = ?", id).Where("bank_transaction_id IS NULL")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdateDbPaygateHengOrder(id int64, body model.PaygateHengOrderUpdateBody) error {

	if err := r.db.Table("paygate_heng_order").Where("id = ?", id).Updates(body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) CreatePaygateHengWebhook(body model.PaygateHengWebhookCreateBody) (*int64, error) {

	if err := r.db.Table("paygate_heng_webhook").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) ApproveDbPaygateHengOrder(id int64, exData model.PaygateHengOrderUpdatePaymentBody) error {

	updateBody := make(map[string]interface{})
	updateBody["transaction_status"] = "SUCCESS"
	updateBody["payment_at"] = time.Now()
	updateBody["payment_bank_no"] = exData.PaymentBankNo
	updateBody["payment_name"] = exData.PaymentName
	updateBody["payment_bank_code"] = exData.PaymentBankCode
	updateBody["payment_bank_name"] = exData.PaymentBankName
	updateBody["bank_transaction_status"] = "รอดำเนินการ"

	sql := r.db.Table("paygate_heng_order").Where("id = ?", id).Where("transaction_status = ?", "WAIT_PAYMENT")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) IgnoreDbPaygateHengPendingDepositOrder(id int64) error {

	updateBody := make(map[string]interface{})
	updateBody["bank_transaction_status"] = "ยกเลิก"

	sql := r.db.Table("paygate_heng_order").Where("id = ?", id).Where("transaction_status = ?", "SUCCESS").Where("bank_transaction_status = ?", "รอดำเนินการ")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) CronjobDeletePaygateHengWebhook() error {

	CurrentDate := time.Now().UTC().Add(time.Hour * 7).Format("2006-01-02")
	startDateAtBkk, err := r.ParseBodBkk(CurrentDate)
	if err != nil {
		return err
	}

	if err := r.db.Unscoped().Table("paygate_heng_webhook").Where("DATEDIFF(?, created_at) > 30", startDateAtBkk).Delete(&model.PaygateHengWebhook{}).Error; err != nil {
		return err
	}

	return nil
}

func (r repo) LuckyThaiSign(clientCode, chainName, coinUnit, clientNo, privateKey string, currentTime int64) string {

	var sign string
	//   //PHP Code example
	//   function generate() {
	// 	$clientCode = "yourClientCode";
	// 	$chainName = "BANK";
	// 	$coinUnit = "THB";
	// 	$privateKey = "yourPrivateKey";
	// 	$currentTime = time();
	// 	$clientNo = substr($currentTime,6);
	// 	$signStr = $clientCode."&".$chainName."&".$coinUnit."&".$clientNo."&".$currentTime.$privateKey;
	// 	$signature = md5($signStr);
	// 	return $signature;
	//   }
	signStr := fmt.Sprintf("%v&%v&%v&%v&%v%v", clientCode, chainName, coinUnit, clientNo, currentTime, privateKey)
	sign = helper.GetMD5Hash(signStr)
	// "[sign] Invalid md5[clientCode&chainName&coinUnit&clientNo&requestTimestamp+pk]",
	// md5(clientCode&chainName&coinUnit&clientNo&requestTimestamp+privateKey)
	return sign
}

func (r repo) LuckyThaiDeposit(setting model.PaygateAccountResponse, req model.LuckyThaiDepositCreateRemoteRequest) (*model.LuckyThaiDepositCreateRemoteResponse, error) {

	// log.Println("LuckyThaiDeposit req ------> ")

	sandBoxMode := false
	ginMode := os.Getenv("GIN_MODE")
	if ginMode == "debug" && setting.ShopName == "DEV_DEMO" {
		sandBoxMode = true
	}
	if sandBoxMode {
		sandboxBalance := model.LuckyThaiDepositCreateRemoteResponse{
			Success: true,
			Code:    200,
		}
		sandboxBalance.Data.OrderNo = helper.AlphaNumerics(10)
		sandboxBalance.Data.PayUrl = "https://dev-web.cbgame88.com/deposit"
		return &sandboxBalance, nil
	}

	// request limit 100—50k "[requestAmount] range [100.0~1010000.0]",
	// ผมอยากเก็บ default เอาไว้กัน Adminตั้งค่าต่ำกว่า payment
	if req.Amount < 100 || req.Amount > 50000 {
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}
	if req.Amount < setting.PaymentDepositMinimum || req.Amount > setting.PaymentDepositMaximum {
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}

	webhookDomain := os.Getenv("CYBERGAME_WEBHOOK_ENDPOINT")
	callbackurl := fmt.Sprintf("%s/luckyth/callback", webhookDomain)
	hrefbackurl := "https://" + os.Getenv("WEB_DOMAIN")
	apiEndPoint := setting.ApiEndPoint
	clientCode := setting.Username
	privateKey := setting.PrivateKey
	// chainName := "BANK"
	// coinUnit := "THB"

	// apiEndPoint := os.Getenv("PAYGATE_LUCKY_ENDPOINT")
	// clientCode := os.Getenv("PAYGATE_LUCKY_CLIENT_CODE")
	// privateKey := os.Getenv("PAYGATE_LUCKY_PRIVATE_KEY")
	// callbackurl := os.Getenv("PAYGATE_LUCKY_CALLBACK_URL")
	// hrefbackurl := os.Getenv("PAYGATE_LUCKY_HREFBACK_URL")
	// clientCode := "code"
	// privateKey := "priKey"

	// POST "https://API-DOMAIN/api/coin/pay/request"
	// Submission Method：application/x-www-form-urlencoded
	epUrl := fmt.Sprintf("%s/api/coin/pay/request", apiEndPoint)
	log.Println("LuckyThaiDeposit url ------> ", epUrl)

	// SysLog
	if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
		Name:   "CreateLuckyThaiDeposit.LuckyThaiDeposit",
		Status: "CALL",
		JsonReq: helper.StructJson(map[string]interface{}{
			"apiEndPoint":   apiEndPoint,
			"clientCode":    clientCode,
			"chainName":     "BANK",
			"coinUnit":      "THB",
			"clientNo":      req.ReferenceNo,
			"requestAmount": req.Amount,
			"privateKey":    privateKey,
			"callbackurl":   callbackurl,
			"hrefbackurl":   hrefbackurl,
		}),
	}); err != nil {
		log.Println("CreateLuckyThaiDeposit.CreateSysLog", err)
	}

	// clientCode	YES	String	store’s code(clientCode)	S001878rm3Nu
	// chainName	YES	String	Payment Methon(Default：BANK)	BANK
	// coinUnit	YES	String	Currency: INR,THB,USDT(Default：THB)	THB
	// clientNo	YES	String	(Merchant)Order number	DP202212018906
	// memberFlag	NO	String	(Merchant)member id	1001
	// requestAmount	YES	BigDecimal(10,5)	Requested amount	Limit amount 100—50000
	// requestTimestamp	YES	String	Current timestamp when request(13 digits)	*************
	// callbackurl	YES	String	Callback URL	https://www.yoururl.com/api/pay/callback
	// hrefbackurl	YES	String
	// sign	YES	String	Request signature(needs to be encrypted and signed according to requirements, refer to “Sign Signature Method” for specific methods)	0fcc2443d98af6a48c38ff01e9ab62b7
	// toPayQr	YES	String	Type of returned result, set to 0 for default redirection to payment page	0
	// dataType	No	String	PAY_PAGE: Indicates return to payment page URL; QR_CODE: Indicates return QR-code string	Pl

	ts := time.Now().UnixNano() / int64(time.Millisecond)
	sign := r.LuckyThaiSign(clientCode, "BANK", "THB", req.ReferenceNo, privateKey, ts)

	// body := map[string]interface{}{
	// 	"clientCode": clientCode,
	// 	"chainName":  "BANK",
	// 	"coinUnit":   "THB",
	// 	"clientNo":   req.ReferenceNo,
	// 	// "memberFlag":       "1001",
	// 	"requestAmount":    req.Amount,
	// 	"requestTimestamp": ts,
	// 	"callbackurl":      "https://stage-api-master.cbgame88.com/api/bai-commision/webhook/heng/callback",
	// 	"hrefbackurl":      "https://api-master.cbgame88.com/api/webhook/heng/callback",
	// 	"sign":             sign,
	// 	"toPayQr":          "0",
	// 	"dataType":         "PAY_PAGE",
	// }

	client := &http.Client{}
	client.Timeout = 3 * time.Second

	payload := strings.NewReader(fmt.Sprintf("clientCode=%v&chainName=%v&coinUnit=%v&clientNo=%v&requestAmount=%v&requestTimestamp=%v&callbackurl=%v&hrefbackurl=%v&sign=%v&toPayQr=%v&dataType=%v", clientCode, "BANK", "THB", req.ReferenceNo, req.Amount, ts, callbackurl, hrefbackurl, sign, "0", "PAY_PAGE"))

	reqExternal, _ := http.NewRequest("POST", epUrl, payload)
	reqExternal.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}
	if response.StatusCode != 200 {
		log.Println(response.StatusCode)
		log.Println(string(responseData))
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	}

	var result model.LuckyThaiDepositCreateRemoteResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("LuckyThaiDeposit resp.Body ------> ", string(responseData))
		log.Println("Unmarshal.Err ------> ", errJson)
		return nil, errJson
	}
	if !result.Success || result.Code != 200 {
		log.Println("LuckyThaiDeposit INVALID_RESPONSE_CODE ------> ", helper.StructJson(result))
		// Code Status Explanation
		// 500	Invalid Payee	Invalid payee information
		// 500	CREATE FAILED	Order creation failed
		// 303	[clientCode] Can not be empty	clientCode is empty or incorrect
		// 303	[chainName] Can not be empty	chainName is empty or incorrect
		// 303	[coinUnit] Can not be empty	coinUnit is empty or incorrect
		// 303	[memberFlag] Can not be empty	memberFlag is empty or incorrect
		// 303	[requestAmount] Can not be empty	requestAmount is empty or incorrect
		// 303	“[requestAmount] range {min}~{max}”	min: minimum amount, max: maximum amount
		// 303	[clientNo] Can not be empty	clientNo is empty or incorrect
		// 303	[requestTimestamp] Can not be empty	requestTimestamp is empty or incorrect
		// 303	Order timeout (+8)	“requestTimestamp parameter timed out in one minute”
		// 303	[callbackurl] Can not be empty	callbackurl cannot be empty
		// 303	[callbackurl] Invalid	Invalid callbackurl; it must start with non-http
		// 303	Invalid Shop	Merchant does not exist or is closed
		// 303	“{shop} Not supported yet {coin}”	shop: merchant name, coin: cryptocurrency. The merchant has not configured the corresponding cryptocurrency
		// 303	Not supported yet Shop: {shop}:{coin}:{channel}”	hop: merchant name, coin: cryptocurrency, channel: channel. The merchant has not configured the corresponding cryptocurrency and channel
		// 303	Balance exceeds limit:{max}	Merchant balance exceeds the maximum allowable amount
		// 303	Duplicate order: {clientNo}	Duplicate order
		// 303	[sign] Invalid md5[clientCode&chainName&coinUnit&clientNo&requestTimestamp+pk]	Signature error
		// 500	Refuse to create order!	Payment order rejected
		// 303	Rejected due to unauthorized sender: {IP}	Payment application rejected, invalid merchant IP
		// 303	payAmount must be greater than: {min}	Payment application, amount is less than the minimum limit
		// 303	[payAmount] Must be less than: {max}	Payment application, amount exceeds the maximum limit
		// 303	Insufficient Shop balance	Payment application, insufficient merchant balance
		var errResp model.LuckyThaiErrorRemoteResponse
		errJson := json.Unmarshal(responseData, &errResp)
		if errJson != nil {
			log.Println("LuckyThaiDeposit resp.Body ------> ", string(responseData))
			log.Println("Unmarshal.Err ------> ", errJson)
			return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
		}
		return nil, errors.New(errResp.Message)
	}
	return &result, nil
}

func (r repo) LuckyThaiWithdraw(setting model.PaygateAccountResponse, req model.LuckyThaiWithdrawCreateRemoteRequest) (*model.LuckyThaiWithdrawCreateRemoteResponse, error) {

	// log.Println("LuckyThaiWithdraw req ------> ")

	// Withdraw API
	// Request URL
	// https://API-DOMAIN/api/bank/agentPay/request
	// Request Method
	// POST
	// Submission Method：application/x-www-form-urlencoded
	// Parameters
	// Parameter	Required	Type	Description	Example
	// clientCode	Yes	String	Store’s code (clientCode)	S001878rm3Nu
	// chainName	Yes	String	Payment Method(default：BANK)	BANK
	// coinUnit	Yes	String	Currency(default：THB)	THB
	// bankCardNum	Yes	String	withdraw Account number	***************
	// bankUserName	Yes	String	withdraw Account holder name	MAHESH KHATIK
	// ifsc	Yes	String	default：IFSC	IFSC
	// bankName	Yes	String	View bank list	KBANK
	// amount	Yes	BigDecimal(10,5)	Request amount	request limit 100—50k
	// clientNo	Yes	String	(Merchant)order number	**************
	// requestTimestamp	Yes	String	Current timestamp of the request (13 digits)	*************
	// callbackurl	Yes	String	Asynchronous callback URL	https://www.yoururl.com/api/pay/callback
	// sign	Yes	String	Request signature (encrypted signature as required, see “sign signature method” for details)	0fcc2443d98af6a48c38ff01e9ab62b7

	// request limit 100—50k "[requestAmount] range [100.0~1010000.0]",

	// ผมอยากเก็บ default เอาไว้กัน Adminตั้งค่าต่ำกว่า payment
	if req.Amount < 100 || req.Amount > 50000 {
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}
	if req.Amount < setting.PaymentWithdrawMinimum || req.Amount > setting.PaymentWithdrawMaximum {
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}

	webhookDomain := os.Getenv("CYBERGAME_WEBHOOK_ENDPOINT")
	callbackurl := fmt.Sprintf("%s/luckyth/callback", webhookDomain)
	apiEndPoint := setting.ApiEndPoint
	clientCode := setting.Username
	privateKey := setting.PrivateKey
	chainName := "BANK"
	coinUnit := "THB"
	ts := time.Now().UnixNano() / int64(time.Millisecond)
	sign := r.LuckyThaiSign(clientCode, chainName, coinUnit, req.ReferenceNo, privateKey, ts)

	// SysLog
	if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
		Name:   "CreateLuckyThaiDeposit.LuckyThaiDeposit",
		Status: "CALL",
		JsonReq: helper.StructJson(map[string]interface{}{
			"apiEndPoint":      apiEndPoint,
			"clientCode":       clientCode,
			"chainName":        chainName,
			"coinUnit":         coinUnit,
			"clientNo":         req.ReferenceNo,
			"requestAmount":    req.Amount,
			"requestTimestamp": ts,
			"privateKey":       privateKey,
			"callbackurl":      callbackurl,
		}),
	}); err != nil {
		log.Println("CreateLuckyThaiDeposit.CreateSysLog", err)
	}

	if callbackurl == "" {
		return nil, errors.New("EMPTY_CALLBACK_URL")
	}

	// POST "https://API-DOMAIN/api/bank/agentPay/request"
	epUrl := fmt.Sprintf("%s/api/bank/agentPay/request", apiEndPoint)
	log.Println("LuckyThaiWithdraw url ------> ", epUrl)

	client := &http.Client{}
	client.Timeout = 3 * time.Second

	payload := strings.NewReader(fmt.Sprintf("clientCode=%v&chainName=%v&coinUnit=%v&bankCardNum=%v&bankUserName=%v&ifsc=%v&bankName=%v&amount=%v&clientNo=%v&requestTimestamp=%v&callbackurl=%v&sign=%v", clientCode, chainName, coinUnit, req.AccountNo, req.Accountname, "IFSC", req.BankCode, req.Amount, req.ReferenceNo, ts, callbackurl, sign))

	reqExternal, _ := http.NewRequest("POST", epUrl, payload)
	reqExternal.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}
	if response.StatusCode != 200 {
		log.Println(response.StatusCode)
		log.Println(string(responseData))
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	}

	var result model.LuckyThaiWithdrawCreateRemoteResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("LuckyThaiWithdraw resp.Body ------> ", string(responseData))
		log.Println("Unmarshal.Err ------> ", errJson)
		return nil, errJson
	}
	if !result.Success || result.Code != 200 {
		log.Println("LuckyThaiWithdraw INVALID_RESPONSE_CODE ------> ", helper.StructJson(result))
		var errResp model.LuckyThaiErrorRemoteResponse
		errJson := json.Unmarshal(responseData, &errResp)
		if errJson != nil {
			log.Println("LuckyThaiWithdraw resp.Body ------> ", string(responseData))
			log.Println("Unmarshal.Err ------> ", errJson)
			return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
		}
		return nil, errors.New(errResp.Message)
	}
	return &result, nil
}

func (r repo) LuckyThaiCheckBalance(setting model.PaygateAccountResponse) (*model.LuckyThaiCheckBalanceRemoteResponse, error) {

	// log.Println("LuckyThaiCheckBalance req ------> ")

	sandBoxMode := false
	ginMode := os.Getenv("GIN_MODE")
	if ginMode == "debug" && setting.ShopName == "DEV_DEMO" {
		sandBoxMode = true
	}
	if sandBoxMode {
		sandboxBalance := model.LuckyThaiCheckBalanceRemoteResponse{
			Success: true,
			Code:    200,
		}
		sandboxBalance.Data.InBalance.Balance = 9999999
		sandboxBalance.Data.OutBalance.Balance = 9999999
		return &sandboxBalance, nil
	}

	// Withdraw Balance Inquiry
	// https://API-DOMAIN/api/shop/info/balance
	// Requst Method GET
	// clientCode	YES	String	shop’s clientCode	S001878rm3Nu
	// coinChain	YES	String	Fixed value	BANK
	// coinUnit	YES	String	Fixed value	THB
	// requestTimestamp	YES	LONG	Timestamp	*************
	// sign	YES	String	MD5 encrypted	0fcc2443d98af6a48c38ff01e9ab62b7
	// sign MD5 Encryption Method
	// md5[clientCode&coinChain&coinUnit&requestTimestamp+privateKey]

	apiEndPoint := setting.ApiEndPoint
	clientCode := setting.Username
	privateKey := setting.PrivateKey
	chainName := "BANK"
	coinUnit := "THB"
	ts := time.Now().UnixNano() / int64(time.Millisecond)
	signStr := fmt.Sprintf("%v&%v&%v&%v%v", clientCode, chainName, coinUnit, ts, privateKey)
	sign := helper.GetMD5Hash(signStr)

	epUrl := fmt.Sprintf("%s/api/shop/info/balance?clientCode=%v&coinChain=%v&coinUnit=%v&requestTimestamp=%v&sign=%v", apiEndPoint, clientCode, chainName, coinUnit, ts, sign)
	log.Println("LuckyThaiCheckBalance url ------> ", epUrl)

	// SysLog
	if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
		Name:   "REPO.LuckyThaiCheckBalance",
		Status: "CALL",
		JsonReq: helper.StructJson(map[string]interface{}{
			"apiEndPoint": apiEndPoint,
		}),
	}); err != nil {
		log.Println("REPO.LuckyThaiCheckBalance.CreateSysLog.error", err)
	}

	client := &http.Client{}
	client.Timeout = 3 * time.Second

	reqExternal, _ := http.NewRequest("GET", epUrl, nil)
	// reqExternal.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}
	if response.StatusCode != 200 {
		log.Println(response.StatusCode)
		log.Println(string(responseData))
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	}

	var result model.LuckyThaiCheckBalanceRemoteResponse

	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("LuckyThaiCheckBalance resp.Body ------> ", string(responseData))
		log.Println("Unmarshal.Err ------> ", errJson)
		return nil, errJson
	}
	if !result.Success || result.Code != 200 {
		log.Println("LuckyThaiCheckBalance INVALID_RESPONSE_CODE ------> ", helper.StructJson(result))
		var errResp model.LuckyThaiErrorRemoteResponse
		errJson := json.Unmarshal(responseData, &errResp)
		if errJson != nil {
			log.Println("LuckyThaiCheckBalance resp.Body ------> ", string(responseData))
			log.Println("Unmarshal.Err ------> ", errJson)
			return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
		}
		return nil, errors.New(errResp.Message)
	}
	return &result, nil
}

func (r repo) LuckyThaiGetOrder(setting model.PaygateAccountResponse, orderNo string) (*model.LuckyThaiGetOrderRemoteResponse, error) {

	// log.Println("LuckyThaiGetOrder req ------> ")

	sandBoxMode := false
	ginMode := os.Getenv("GIN_MODE")
	if ginMode == "debug" && setting.ShopName == "DEV_DEMO" {
		sandBoxMode = true
	}
	if sandBoxMode {
		sandboxBalance := model.LuckyThaiGetOrderRemoteResponse{
			Success: true,
			Code:    200,
		}
		sandboxBalance.Data.OrderNo = orderNo
		return &sandboxBalance, nil
	}

	// Query Order API
	// https://API-DOMAIN/api/coin/agentPay/checkOrder?clientCode=&clientNo=&sign=
	// sign=md5[clientCode&clientNo+pk]

	apiEndPoint := setting.ApiEndPoint
	clientCode := setting.Username
	privateKey := setting.PrivateKey

	signStr := fmt.Sprintf("%v&%v%v", clientCode, orderNo, privateKey)
	sign := helper.GetMD5Hash(signStr)

	epUrl := fmt.Sprintf("%s/api/coin/agentPay/checkOrder?clientCode=%v&clientNo=%v&sign=%v", apiEndPoint, clientCode, orderNo, sign)
	log.Println("LuckyThaiGetOrder url ------> ", epUrl)

	// SysLog
	if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
		Name:   "REPO.LuckyThaiGetOrder",
		Status: "CALL",
		JsonReq: helper.StructJson(map[string]interface{}{
			"apiEndPoint": apiEndPoint,
		}),
	}); err != nil {
		log.Println("REPO.LuckyThaiGetOrder.CreateSysLog.error", err)
	}

	client := &http.Client{}
	client.Timeout = 3 * time.Second

	reqExternal, _ := http.NewRequest("GET", epUrl, nil)
	// reqExternal.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}
	if response.StatusCode != 200 {
		log.Println(response.StatusCode)
		log.Println(string(responseData))
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	}

	var result model.LuckyThaiGetOrderRemoteResponse

	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("LuckyThaiGetOrder resp.Body ------> ", string(responseData))
		log.Println("Unmarshal.Err ------> ", errJson)
		return nil, errJson
	}
	if !result.Success || result.Code != 200 {
		log.Println("LuckyThaiGetOrder INVALID_RESPONSE_CODE ------> ", helper.StructJson(result))
		var errResp model.LuckyThaiErrorRemoteResponse
		errJson := json.Unmarshal(responseData, &errResp)
		if errJson != nil {
			log.Println("LuckyThaiGetOrder resp.Body ------> ", string(responseData))
			log.Println("Unmarshal.Err ------> ", errJson)
			return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
		}
		return nil, errors.New(errResp.Message)
	}
	return &result, nil
}

func (r repo) CreateLuckyThaiWebhook(body model.LuckyThaiWebhookCreateBody) (*int64, error) {

	if err := r.db.Table("paygate_luckyth_webhook").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) GetDbLuckyThaiOrderList(req model.LuckyThaiOrderListRequest) ([]model.LuckyThaiOrderResponse, int64, error) {

	var list []model.LuckyThaiOrderResponse
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("paygate_luckyth_order as tb_order")
	count = count.Select("tb_order.id")
	if req.UserId != nil {
		count = count.Where("tb_order.user_id = ?", req.UserId)
	}
	if req.OrderTypeId != nil {
		count = count.Where("tb_order.order_type_id = ?", req.OrderTypeId)
	}
	if req.OrderNo != "" {
		count = count.Where("tb_order.order_no = ?", req.OrderNo)
	}
	if req.TransactionNo != "" {
		count = count.Where("tb_order.transaction_no = ?", req.TransactionNo)
	}
	if req.Amount != "" {
		count = count.Where("tb_order.amount = ?", req.Amount)
	}
	if req.Status != "" {
		count = count.Where("tb_order.transaction_status = ?", req.Status)
	}

	if err = count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount"
		selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
		selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
		selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

		query := r.db.Table("paygate_luckyth_order as tb_order")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN paygate_luckyth_order_type AS tb_type ON tb_order.order_type_id = tb_type.id")
		if req.UserId != nil {
			query = query.Where("tb_order.user_id = ?", req.UserId)
		}
		if req.OrderTypeId != nil {
			query = query.Where("tb_order.order_type_id = ?", req.OrderTypeId)
		}
		if req.OrderNo != "" {
			query = query.Where("tb_order.order_no = ?", req.OrderNo)
		}
		if req.TransactionNo != "" {
			query = query.Where("tb_order.transaction_no = ?", req.TransactionNo)
		}
		if req.Amount != "" {
			query = query.Where("tb_order.amount = ?", req.Amount)
		}
		if req.Status != "" {
			query = query.Where("tb_order.transaction_status = ?", req.Status)
		}

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetDbLuckyThaiOrderById(id int64) (*model.LuckyThaiOrderResponse, error) {

	var record model.LuckyThaiOrderResponse

	selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount"
	selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
	selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
	selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

	if err := r.db.Table("paygate_luckyth_order as tb_order").
		Select(selectedFields).
		Joins("LEFT JOIN paygate_luckyth_order_type AS tb_type ON tb_order.order_type_id = tb_type.id").
		Where("tb_order.id = ?", id).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetDbLuckyThaiOrderByRefId(refId int64) (*model.LuckyThaiOrderResponse, error) {

	var record model.LuckyThaiOrderResponse

	selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount"
	selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
	selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
	selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

	if err := r.db.Table("paygate_luckyth_order as tb_order").
		Select(selectedFields).
		Joins("LEFT JOIN paygate_luckyth_order_type AS tb_type ON tb_order.order_type_id = tb_type.id").
		Where("tb_order.ref_id = ?", refId).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) CreateDbLuckyThaiOrder(body model.LuckyThaiOrderCreateBody) (*int64, error) {

	if err := r.db.Table("paygate_luckyth_order").Create(&body).Error; err != nil {
		return nil, err
	}

	// Update order_no if Empty in HPG{YYYYMMDD}{ID} //
	// orderNo := fmt.Sprintf("HPG%v%v", time.Now().Format("200601"), body.Id)
	// [20240209] Update order_no if Empty in {AGENT_NAME}{YYMM}{ID} //
	agentName := os.Getenv("PAYGATE_ORDER_PREFIX")
	if ginMode := os.Getenv("GIN_MODE"); ginMode == "debug" {
		agentName = "P2D" // DEVELOPMENT
	}
	if agentName == "" {
		agentName = "P2G" // ** MIN_LEN=10
	} else {
		agentName = strings.ToUpper(agentName)
	}
	orderNo := fmt.Sprintf("%v%v%v", agentName, time.Now().Format("0601"), body.Id)
	if body.OrderNo == "" {
		updateBody := make(map[string]interface{})
		updateBody["order_no"] = orderNo
		if err := r.db.Table("paygate_luckyth_order").Where("id = ?", body.Id).Updates(updateBody).Error; err != nil {
			return nil, err
		}
	}
	return &body.Id, nil
}

func (r repo) UpdateDbLuckyThaiOrderError(id int64, remark string) error {

	updateBody := map[string]interface{}{
		"transaction_status": "ERROR",
		"remark":             remark,
	}
	sql := r.db.Table("paygate_luckyth_order").Where("id = ?", id).Where("transaction_status = ?", "PENDING")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdateDbLuckyThaiOrder(id int64, body model.LuckyThaiOrderUpdateBody) error {

	if err := r.db.Table("paygate_luckyth_order").Where("id = ?", id).Updates(body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) ApproveDbLuckyThaiOrder(id int64, webhookStatus string) error {

	updateBody := make(map[string]interface{})
	updateBody["transaction_status"] = webhookStatus
	updateBody["payment_at"] = time.Now()

	sql := r.db.Table("paygate_luckyth_order").Where("id = ?", id).Where("transaction_status = ?", "WAIT_PAYMENT")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetHengOrderReportList(req model.PaygateOrderReportListRequest) ([]model.HengOrderReportResponse, int64, error) {

	var list []model.HengOrderReportResponse
	var total int64

	dateType, err2 := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err2 != nil {
		return list, 0, err2
	}

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("paygate_heng_order AS tb_log")
	count = count.Select("tb_log.id")
	if req.BankTransactionStatus != "" {
		count = count.Where("tb_log.bank_transaction_status = ?", req.BankTransactionStatus)
	}
	if req.Search != "" {
		count = count.Joins("INNER JOIN user AS tb_user ON tb_log.user_id = tb_user.id")
		searchText := "%" + req.Search + "%"
		count = count.Where("tb_log.transaction_no LIKE ? OR tb_user.member_code LIKE ? OR tb_log.order_no LIKE ?", searchText, searchText, searchText)
	}
	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return list, 0, err
		}
		count = count.Where("tb_log.created_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return list, 0, err
		}
		count = count.Where("tb_log.created_at <=  ?", endDateAtBkk)
	}
	if err := count.Count(&total).Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_log.id AS id, tb_log.transaction_date AS transaction_date, tb_user.member_code AS member_code, tb_log.transaction_no AS transaction_no"
		selectedFields += ", 'HENG' AS merchant_name"
		selectedFields += ", tb_log.order_no AS order_no, tb_log.transaction_status AS transaction_status, tb_log.payment_at AS payment_at"
		selectedFields += ", tb_log.bank_transaction_status AS bank_transaction_status"
		selectedFields += ", tb_log.remark AS remark, tb_log.amount AS amount"
		query := r.db.Table("paygate_heng_order AS tb_log")
		query = query.Select(selectedFields)
		query = query.Joins("INNER JOIN user AS tb_user ON tb_log.user_id = tb_user.id")
		query = query.Joins("LEFT JOIN bank_transaction AS tb_bank ON tb_bank.id = tb_log.bank_transaction_id")
		if req.BankTransactionStatus != "" {
			query = query.Where("tb_log.bank_transaction_status = ?", req.BankTransactionStatus)
		}
		if req.Search != "" {
			searchText := "%" + req.Search + "%"
			query = query.Where("tb_log.transaction_no LIKE ? OR tb_user.member_code LIKE ? OR tb_log.order_no LIKE ?", searchText, searchText, searchText)
		}
		if dateType.DateFrom != "" {
			startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
			if err != nil {
				return list, 0, err
			}
			query = query.Where("tb_log.created_at >= ? ", startDateAtBkk)
		}
		if dateType.DateTo != "" {
			endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
			if err != nil {
				return list, 0, err
			}
			query = query.Where("tb_log.created_at <=  ?", endDateAtBkk)
		}
		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		} else {
			query = query.Order("tb_log.id DESC")
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetHengOrderSuccessPendingCount() (int64, error) {

	var total int64

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Select("paygate_heng_order.id")
	count = count.Table("paygate_heng_order")
	count = count.Where("paygate_heng_order.transaction_status = ?", "SUCCESS")
	count = count.Where("paygate_heng_order.bank_transaction_status = ?", "รอดำเนินการ")
	if err := count.Count(&total).Error; err != nil {
		return 0, err
	}
	return total, nil
}

func (r repo) GetHengCallbackReportResponse(req model.HengCallbackReportListRequest) ([]model.HengCallbackReportResponse, int64, error) {

	var list []model.HengCallbackReportResponse
	var total int64
	var err error

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return list, 0, err
	}

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("paygate_heng_webhook AS tb_log")
	count = count.Select("tb_log.id")
	count = count.Where("tb_log.name = ?", "HENG_CALLBACK")
	if req.Search != "" {
		searchText := "%" + req.Search + "%"
		count = count.Where("tb_log.json_payload LIKE ?", searchText)
	}
	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return list, 0, err
		}
		count = count.Where("tb_log.created_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return list, 0, err
		}
		count = count.Where("tb_log.created_at <=  ?", endDateAtBkk)
	}
	if err = count.Count(&total).Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		tempJsonRecordList := []struct {
			Id          int64     `json:"id"`
			CreatedAt   time.Time `json:"createdAt"`
			JsonPayload string    `json:"jsonPayload"`
		}{}
		selectedFields := "*"
		query := r.db.Table("paygate_heng_webhook AS tb_log")
		query = query.Select(selectedFields)
		query = query.Where("tb_log.name = ?", "HENG_CALLBACK")
		if req.Search != "" {
			searchText := "%" + req.Search + "%"
			query = query.Where("tb_log.json_payload LIKE ?", searchText)
		}
		if dateType.DateFrom != "" {
			startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
			if err != nil {
				return list, 0, err
			}
			query = query.Where("tb_log.created_at >= ? ", startDateAtBkk)
		}
		if dateType.DateTo != "" {
			endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
			if err != nil {
				return list, 0, err
			}
			query = query.Where("tb_log.created_at <=  ?", endDateAtBkk)
		}
		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		} else {
			query = query.Order("tb_log.id DESC")
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Offset(req.Page * req.Limit).
			Scan(&tempJsonRecordList).
			Error; err != nil {
			return nil, total, err
		}

		for _, tempJsonRecord := range tempJsonRecordList {
			var tempCallback model.PaygateHengWebhookResponse
			if err := json.Unmarshal([]byte(tempJsonRecord.JsonPayload), &tempCallback); err != nil {
				return nil, 0, err
			}

			var tempRecord model.HengCallbackReportResponse
			tempRecord.Id = tempJsonRecord.Id
			tempRecord.CreatedAt = tempJsonRecord.CreatedAt
			tempRecord.Reference1 = tempCallback.Data.Reference1
			tempRecord.Reference2 = tempCallback.Data.Reference2
			tempRecord.Amount = tempCallback.Data.Amount
			tempRecord.FromBank = tempCallback.Data.FromBank
			tempRecord.FromName = tempCallback.Data.FromName
			tempRecord.ShortName = tempCallback.Data.ShortName
			tempRecord.BankName = tempCallback.Data.BankName
			list = append(list, tempRecord)
		}

	}
	return list, total, nil
}

func (r repo) GetHengUserById(userId int64) (*model.HengUser, error) {

	var record model.HengUser

	selectedFields := "*"
	if err := r.db.Table("paygate_heng_user as tb_user").
		Select(selectedFields).
		Where("tb_user.user_id = ?", userId).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) CreateHengUser(body model.HengUserCreateBody) (*int64, error) {

	if err := r.db.Table("paygate_heng_user").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) UpdateHengUser(userId int64, body model.HengUserUpdateBody) error {

	if err := r.db.Table("paygate_heng_user").Where("user_id = ?", userId).Updates(body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetHengUserBankDetailById(userId int64) (*model.HengUserBankDetailResponse, error) {

	var record model.HengUserBankDetailResponse

	selectedFields := "user.id AS id, user.member_code AS member_code, user.phone AS phone, user_status.name as status, user.fullname AS fullname"
	selectedFields += ", user.credit AS credit"
	selectedFields += ", user.bank_account AS bank_account, user.bank_id AS bank_id, bank.name AS bank_name, bank.code AS bank_code"
	selectedFields += ", heng_user.account_no AS payment_account_no, heng_user.account_name AS payment_account_name"
	selectedFields += ", heng_user.bank_no AS payment_bank_no, heng_user.bank_code AS payment_bank_code, heng_user.bank_name AS payment_bank_name"
	selectedFields += ", user.created_at AS created_at, user.updated_at AS updated_at"
	if err := r.db.Table("user").
		Select(selectedFields).
		Joins("LEFT JOIN user_status ON user_status.id = user.user_status_id").
		Joins("LEFT JOIN bank ON bank.id = user.bank_id").
		Joins("LEFT JOIN paygate_heng_user AS heng_user ON heng_user.user_id = user.id").
		Where("user.deleted_at IS NULL").
		Where("user.id = ?", userId).
		First(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetRawPayonexPendingDepositOrderById(id int64) (*model.PayonexOrderResponse, error) {

	var record model.PayonexOrderResponse

	selectedFields := "*"

	if err := r.db.Table("paygate_payonex_order as tb_order").
		Select(selectedFields).
		Where("tb_order.id = ?", id).
		// Where("tb_order.transaction_status = ?", "SUCCESS").
		Where("tb_order.bank_transaction_status = ?", "PENDING").
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) IgnoreDbPayonexPendingDepositOrder(id int64, actionBy int64) error {

	updateBody := make(map[string]interface{})
	updateBody["bank_transaction_status"] = "IGNORE"
	updateBody["action_by"] = actionBy

	sql := r.db.Table("paygate_payonex_order").Where("id = ?", id).Where("bank_transaction_status = ?", "PENDING")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetPayonexOrderReportList(req model.PaygateOrderReportListRequest) ([]model.PaygateOrderReportResponse, int64, error) {

	var list []model.PaygateOrderReportResponse
	var total int64

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return list, 0, err
	}

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("paygate_payonex_order AS tb_log")
	count = count.Select("tb_log.id")
	count = count.Where("tb_log.order_type_id = ?", 1) // เฉพาะฝากเงิน
	if req.StatusName != "" {
		if req.StatusName == "WAIT_PAYMENT" {
			count = count.Where("tb_log.transaction_status = 'WAIT_PAYMENT'")
		} else if req.StatusName == "PAID" {
			count = count.Where("tb_log.transaction_status = 'PAID'")
		} else {
			return list, 0, nil
		}
	}
	if req.BankTransactionStatus != "" {
		count = count.Where("tb_log.bank_transaction_status = ?", req.BankTransactionStatus)
	}
	if req.Search != "" {
		count = count.Joins("INNER JOIN user AS tb_user ON tb_log.user_id = tb_user.id")
		searchText := "%" + req.Search + "%"
		count = count.Where("tb_log.transaction_no LIKE ? OR tb_user.member_code LIKE ? OR tb_log.order_no LIKE ?", searchText, searchText, searchText)
	}
	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return list, 0, err
		}
		count = count.Where("tb_log.created_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return list, 0, err
		}
		count = count.Where("tb_log.created_at <=  ?", endDateAtBkk)
	}
	if err := count.Count(&total).Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_log.id AS id, tb_log.created_at AS created_at, tb_user.member_code AS member_code, tb_admin.fullname AS admin_fullname"
		selectedFields += ", 'PAYONEX' AS merchant_name"
		selectedFields += ", tb_log.transaction_no AS transaction_no, tb_log.order_no AS order_no, tb_log.amount AS amount"
		selectedFields += ", tb_log.transaction_status AS transaction_status, tb_log.payment_at AS payment_at"
		selectedFields += ", tb_log.bank_transaction_status AS bank_transaction_status"
		selectedFields += ", tb_log.remark AS remark"
		query := r.db.Table("paygate_payonex_order AS tb_log")
		query = query.Select(selectedFields)
		query = query.Joins("INNER JOIN user AS tb_user ON tb_log.user_id = tb_user.id")
		query = query.Joins("LEFT JOIN admin AS tb_admin ON tb_admin.id = tb_log.action_by")
		query = query.Where("tb_log.order_type_id = ?", 1) // เฉพาะฝากเงิน
		if req.StatusName != "" {
			if req.StatusName == "WAIT_PAYMENT" {
				query = query.Where("tb_log.transaction_status = 'WAIT_PAYMENT'")
			} else if req.StatusName == "PAID" {
				query = query.Where("tb_log.transaction_status = 'PAID'")
			} else {
				return list, 0, nil
			}
		}
		if req.BankTransactionStatus != "" {
			query = query.Where("tb_log.bank_transaction_status = ?", req.BankTransactionStatus)
		}
		if req.Search != "" {
			searchText := "%" + req.Search + "%"
			query = query.Where("tb_log.transaction_no LIKE ? OR tb_user.member_code LIKE ? OR tb_log.order_no LIKE ?", searchText, searchText, searchText)
		}
		if dateType.DateFrom != "" {
			startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
			if err != nil {
				return list, 0, err
			}
			query = query.Where("tb_log.created_at >= ? ", startDateAtBkk)
		}
		if dateType.DateTo != "" {
			endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
			if err != nil {
				return list, 0, err
			}
			query = query.Where("tb_log.created_at <=  ?", endDateAtBkk)
		}
		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		} else {
			query = query.Order("tb_log.id DESC")
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetRawJbpayPendingDepositOrderById(id int64) (*model.JbpayOrderResponse, error) {

	var record model.JbpayOrderResponse

	selectedFields := "*"

	if err := r.db.Table("paygate_jbpay_order as tb_order").
		Select(selectedFields).
		Where("tb_order.id = ?", id).
		// Where("tb_order.transaction_status = ?", "SUCCESS").
		Where("tb_order.bank_transaction_status = ?", "PENDING").
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) IgnoreDbJbpayPendingDepositOrder(id int64, actionBy int64) error {

	updateBody := make(map[string]interface{})
	updateBody["bank_transaction_status"] = "IGNORE"
	updateBody["action_by"] = actionBy

	sql := r.db.Table("paygate_jbpay_order").Where("id = ?", id).Where("bank_transaction_status = ?", "PENDING")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetJbpayOrderReportList(req model.PaygateOrderReportListRequest) ([]model.PaygateOrderReportResponse, int64, error) {

	var list []model.PaygateOrderReportResponse
	var total int64

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return list, 0, err
	}

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("paygate_jbpay_order AS tb_log")
	count = count.Select("tb_log.id")
	count = count.Where("tb_log.order_type_id = ?", 1) // เฉพาะฝากเงิน
	if req.StatusName != "" {
		if req.StatusName == "WAIT_PAYMENT" {
			count = count.Where("tb_log.transaction_status = 'WAIT_PAYMENT'")
		} else if req.StatusName == "PAID" {
			count = count.Where("tb_log.transaction_status = 'PAID'")
		} else {
			return list, 0, nil
		}
	}
	if req.BankTransactionStatus != "" {
		count = count.Where("tb_log.bank_transaction_status = ?", req.BankTransactionStatus)
	}
	if req.Search != "" {
		count = count.Joins("INNER JOIN user AS tb_user ON tb_log.user_id = tb_user.id")
		searchText := "%" + req.Search + "%"
		count = count.Where("tb_log.transaction_no LIKE ? OR tb_user.member_code LIKE ? OR tb_log.order_no LIKE ?", searchText, searchText, searchText)
	}
	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return list, 0, err
		}
		count = count.Where("tb_log.created_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return list, 0, err
		}
		count = count.Where("tb_log.created_at <=  ?", endDateAtBkk)
	}
	if err := count.Count(&total).Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_log.id AS id, tb_log.created_at AS created_at, tb_user.member_code AS member_code, tb_admin.fullname AS admin_fullname"
		selectedFields += ", 'JBPAY' AS merchant_name"
		selectedFields += ", tb_log.transaction_no AS transaction_no, tb_log.order_no AS order_no, tb_log.amount AS amount"
		selectedFields += ", tb_log.transaction_status AS transaction_status, tb_log.payment_at AS payment_at"
		selectedFields += ", tb_log.bank_transaction_status AS bank_transaction_status"
		selectedFields += ", tb_log.remark AS remark"
		query := r.db.Table("paygate_jbpay_order AS tb_log")
		query = query.Select(selectedFields)
		query = query.Joins("INNER JOIN user AS tb_user ON tb_log.user_id = tb_user.id")
		query = query.Joins("LEFT JOIN admin AS tb_admin ON tb_admin.id = tb_log.action_by")
		query = query.Where("tb_log.order_type_id = ?", 1) // เฉพาะฝากเงิน
		if req.StatusName != "" {
			if req.StatusName == "WAIT_PAYMENT" {
				query = query.Where("tb_log.transaction_status = 'WAIT_PAYMENT'")
			} else if req.StatusName == "PAID" {
				query = query.Where("tb_log.transaction_status = 'PAID'")
			} else {
				return list, 0, nil
			}
		}
		if req.BankTransactionStatus != "" {
			query = query.Where("tb_log.bank_transaction_status = ?", req.BankTransactionStatus)
		}
		if req.Search != "" {
			searchText := "%" + req.Search + "%"
			query = query.Where("tb_log.transaction_no LIKE ? OR tb_user.member_code LIKE ? OR tb_log.order_no LIKE ?", searchText, searchText, searchText)
		}
		if dateType.DateFrom != "" {
			startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
			if err != nil {
				return list, 0, err
			}
			query = query.Where("tb_log.created_at >= ? ", startDateAtBkk)
		}
		if dateType.DateTo != "" {
			endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
			if err != nil {
				return list, 0, err
			}
			query = query.Where("tb_log.created_at <=  ?", endDateAtBkk)
		}
		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		} else {
			query = query.Order("tb_log.id DESC")
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetPaygateMerchantOption() ([]model.SelectOptions, error) {

	var list []model.SelectOptions

	selectedFields := "tb_merchant.id AS id, tb_merchant.name AS label, tb_merchant.name AS value"
	query := r.db.Table("paygate_merchant as tb_merchant")
	query = query.Select(selectedFields)

	if err := query.
		Where("tb_merchant.deleted_at IS NULL").
		Scan(&list).
		Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (r repo) GetRawPompayPendingDepositOrderById(id int64) (*model.PompayOrderResponse, error) {

	var record model.PompayOrderResponse

	selectedFields := "*"

	if err := r.db.Table("paygate_pompay_order as tb_order").
		Select(selectedFields).
		Where("tb_order.id = ?", id).
		// Where("tb_order.transaction_status = ?", "SUCCESS").
		Where("tb_order.bank_transaction_status = ?", "PENDING").
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) IgnoreDbPompayPendingDepositOrder(id int64, actionBy int64) error {

	updateBody := make(map[string]interface{})
	updateBody["bank_transaction_status"] = "IGNORE"
	updateBody["action_by"] = actionBy

	sql := r.db.Table("paygate_pompay_order").Where("id = ?", id).Where("bank_transaction_status = ?", "PENDING")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetPompayOrderReportList(req model.PaygateOrderReportListRequest) ([]model.PaygateOrderReportResponse, int64, error) {

	var list []model.PaygateOrderReportResponse
	var total int64

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return list, 0, err
	}

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("paygate_pompay_order AS tb_log")
	count = count.Select("tb_log.id")
	count = count.Where("tb_log.order_type_id = ?", 1) // เฉพาะฝากเงิน
	if req.StatusName != "" {
		if req.StatusName == "WAIT_PAYMENT" {
			count = count.Where("tb_log.transaction_status = 'WAIT_PAYMENT'")
		} else if req.StatusName == "PAID" {
			count = count.Where("tb_log.transaction_status = 'PAID'")
		} else {
			return list, 0, nil
		}
	}
	if req.BankTransactionStatus != "" {
		count = count.Where("tb_log.bank_transaction_status = ?", req.BankTransactionStatus)
	}
	if req.Search != "" {
		count = count.Joins("INNER JOIN user AS tb_user ON tb_log.user_id = tb_user.id")
		searchText := "%" + req.Search + "%"
		count = count.Where("tb_log.transaction_no LIKE ? OR tb_user.member_code LIKE ? OR tb_log.order_no LIKE ?", searchText, searchText, searchText)
	}
	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return list, 0, err
		}
		count = count.Where("tb_log.created_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return list, 0, err
		}
		count = count.Where("tb_log.created_at <=  ?", endDateAtBkk)
	}
	if err := count.Count(&total).Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_log.id AS id, tb_log.created_at AS created_at, tb_user.member_code AS member_code, tb_admin.fullname AS admin_fullname"
		selectedFields += ", 'POMPAY' AS merchant_name"
		selectedFields += ", tb_log.transaction_no AS transaction_no, tb_log.order_no AS order_no, tb_log.amount AS amount"
		selectedFields += ", tb_log.transaction_status AS transaction_status, tb_log.payment_at AS payment_at"
		selectedFields += ", tb_log.bank_transaction_status AS bank_transaction_status"
		selectedFields += ", tb_log.remark AS remark"
		query := r.db.Table("paygate_pompay_order AS tb_log")
		query = query.Select(selectedFields)
		query = query.Joins("INNER JOIN user AS tb_user ON tb_log.user_id = tb_user.id")
		query = query.Joins("LEFT JOIN admin AS tb_admin ON tb_admin.id = tb_log.action_by")
		query = query.Where("tb_log.order_type_id = ?", 1) // เฉพาะฝากเงิน
		if req.StatusName != "" {
			if req.StatusName == "WAIT_PAYMENT" {
				query = query.Where("tb_log.transaction_status = 'WAIT_PAYMENT'")
			} else if req.StatusName == "PAID" {
				query = query.Where("tb_log.transaction_status = 'PAID'")
			} else {
				return list, 0, nil
			}
		}
		if req.BankTransactionStatus != "" {
			query = query.Where("tb_log.bank_transaction_status = ?", req.BankTransactionStatus)
		}
		if req.Search != "" {
			searchText := "%" + req.Search + "%"
			query = query.Where("tb_log.transaction_no LIKE ? OR tb_user.member_code LIKE ? OR tb_log.order_no LIKE ?", searchText, searchText, searchText)
		}
		if dateType.DateFrom != "" {
			startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
			if err != nil {
				return list, 0, err
			}
			query = query.Where("tb_log.created_at >= ? ", startDateAtBkk)
		}
		if dateType.DateTo != "" {
			endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
			if err != nil {
				return list, 0, err
			}
			query = query.Where("tb_log.created_at <=  ?", endDateAtBkk)
		}
		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		} else {
			query = query.Order("tb_log.id DESC")
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetRawPaymentcoPendingDepositOrderById(id int64) (*model.PaymentcoOrderResponse, error) {

	var record model.PaymentcoOrderResponse

	selectedFields := "*"

	if err := r.db.Table("paygate_paymentco_order as tb_order").
		Select(selectedFields).
		Where("tb_order.id = ?", id).
		// Where("tb_order.transaction_status = ?", "SUCCESS").
		Where("tb_order.bank_transaction_status = ?", "PENDING").
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) IgnoreDbPaymentcoPendingDepositOrder(id int64, actionBy int64) error {

	updateBody := make(map[string]interface{})
	updateBody["bank_transaction_status"] = "IGNORE"
	updateBody["action_by"] = actionBy

	sql := r.db.Table("paygate_paymentco_order").Where("id = ?", id).Where("bank_transaction_status = ?", "PENDING")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetPaymentcoOrderReportList(req model.PaygateOrderReportListRequest) ([]model.PaygateOrderReportResponse, int64, error) {

	var list []model.PaygateOrderReportResponse
	var total int64

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return list, 0, err
	}

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("paygate_paymentco_order AS tb_log")
	count = count.Select("tb_log.id")
	count = count.Where("tb_log.order_type_id = ?", 1) // เฉพาะฝากเงิน
	if req.StatusName != "" {
		if req.StatusName == "WAIT_PAYMENT" {
			count = count.Where("tb_log.transaction_status = 'WAIT_PAYMENT'")
		} else if req.StatusName == "PAID" {
			count = count.Where("tb_log.transaction_status = 'PAID'")
		} else {
			return list, 0, nil
		}
	}
	if req.BankTransactionStatus != "" {
		count = count.Where("tb_log.bank_transaction_status = ?", req.BankTransactionStatus)
	}
	if req.Search != "" {
		count = count.Joins("INNER JOIN user AS tb_user ON tb_log.user_id = tb_user.id")
		searchText := "%" + req.Search + "%"
		count = count.Where("tb_log.transaction_no LIKE ? OR tb_user.member_code LIKE ? OR tb_log.order_no LIKE ?", searchText, searchText, searchText)
	}
	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return list, 0, err
		}
		count = count.Where("tb_log.created_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return list, 0, err
		}
		count = count.Where("tb_log.created_at <=  ?", endDateAtBkk)
	}
	if err := count.Count(&total).Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_log.id AS id, tb_log.created_at AS created_at, tb_user.member_code AS member_code, tb_admin.fullname AS admin_fullname"
		selectedFields += ", 'PAYMENTCO' AS merchant_name"
		selectedFields += ", tb_log.transaction_no AS transaction_no, tb_log.order_no AS order_no, tb_log.amount AS amount"
		selectedFields += ", tb_log.transaction_status AS transaction_status, tb_log.payment_at AS payment_at"
		selectedFields += ", tb_log.bank_transaction_status AS bank_transaction_status"
		selectedFields += ", tb_log.remark AS remark"
		query := r.db.Table("paygate_paymentco_order AS tb_log")
		query = query.Select(selectedFields)
		query = query.Joins("INNER JOIN user AS tb_user ON tb_log.user_id = tb_user.id")
		query = query.Joins("LEFT JOIN admin AS tb_admin ON tb_admin.id = tb_log.action_by")
		query = query.Where("tb_log.order_type_id = ?", 1) // เฉพาะฝากเงิน
		if req.StatusName != "" {
			if req.StatusName == "WAIT_PAYMENT" {
				query = query.Where("tb_log.transaction_status = 'WAIT_PAYMENT'")
			} else if req.StatusName == "PAID" {
				query = query.Where("tb_log.transaction_status = 'PAID'")
			} else {
				return list, 0, nil
			}
		}
		if req.BankTransactionStatus != "" {
			query = query.Where("tb_log.bank_transaction_status = ?", req.BankTransactionStatus)
		}
		if req.Search != "" {
			searchText := "%" + req.Search + "%"
			query = query.Where("tb_log.transaction_no LIKE ? OR tb_user.member_code LIKE ? OR tb_log.order_no LIKE ?", searchText, searchText, searchText)
		}
		if dateType.DateFrom != "" {
			startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
			if err != nil {
				return list, 0, err
			}
			query = query.Where("tb_log.created_at >= ? ", startDateAtBkk)
		}
		if dateType.DateTo != "" {
			endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
			if err != nil {
				return list, 0, err
			}
			query = query.Where("tb_log.created_at <=  ?", endDateAtBkk)
		}
		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		} else {
			query = query.Order("tb_log.id DESC")
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetRawZappayPendingDepositOrderById(id int64) (*model.ZappayOrderResponse, error) {

	var record model.ZappayOrderResponse

	selectedFields := "*"

	if err := r.db.Table("paygate_zappay_order as tb_order").
		Select(selectedFields).
		Where("tb_order.id = ?", id).
		// Where("tb_order.transaction_status = ?", "SUCCESS").
		Where("tb_order.bank_transaction_status = ?", "PENDING").
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) IgnoreDbZappayPendingDepositOrder(id int64, actionBy int64) error {

	updateBody := make(map[string]interface{})
	updateBody["bank_transaction_status"] = "IGNORE"
	updateBody["action_by"] = actionBy

	sql := r.db.Table("paygate_zappay_order").Where("id = ?", id).Where("bank_transaction_status = ?", "PENDING")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetZappayOrderReportList(req model.PaygateOrderReportListRequest) ([]model.PaygateOrderReportResponse, int64, error) {

	var list []model.PaygateOrderReportResponse
	var total int64

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return list, 0, err
	}

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("paygate_zappay_order AS tb_log")
	count = count.Select("tb_log.id")
	count = count.Where("tb_log.order_type_id = ?", 1) // เฉพาะฝากเงิน
	if req.StatusName != "" {
		if req.StatusName == "WAIT_PAYMENT" {
			count = count.Where("tb_log.transaction_status = 'WAIT_PAYMENT'")
		} else if req.StatusName == "PAID" {
			count = count.Where("tb_log.transaction_status = 'PAID'")
		} else {
			return list, 0, nil
		}
	}
	if req.BankTransactionStatus != "" {
		count = count.Where("tb_log.bank_transaction_status = ?", req.BankTransactionStatus)
	}
	if req.Search != "" {
		count = count.Joins("INNER JOIN user AS tb_user ON tb_log.user_id = tb_user.id")
		searchText := "%" + req.Search + "%"
		count = count.Where("tb_log.transaction_no LIKE ? OR tb_user.member_code LIKE ? OR tb_log.order_no LIKE ?", searchText, searchText, searchText)
	}
	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return list, 0, err
		}
		count = count.Where("tb_log.created_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return list, 0, err
		}
		count = count.Where("tb_log.created_at <=  ?", endDateAtBkk)
	}
	if err := count.Count(&total).Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_log.id AS id, tb_log.created_at AS created_at, tb_user.member_code AS member_code, tb_admin.fullname AS admin_fullname"
		selectedFields += ", 'ZAPPAY' AS merchant_name"
		selectedFields += ", tb_log.transaction_no AS transaction_no, tb_log.order_no AS order_no, tb_log.amount AS amount"
		selectedFields += ", tb_log.transaction_status AS transaction_status, tb_log.payment_at AS payment_at"
		selectedFields += ", tb_log.bank_transaction_status AS bank_transaction_status"
		selectedFields += ", tb_log.remark AS remark"
		query := r.db.Table("paygate_zappay_order AS tb_log")
		query = query.Select(selectedFields)
		query = query.Joins("INNER JOIN user AS tb_user ON tb_log.user_id = tb_user.id")
		query = query.Joins("LEFT JOIN admin AS tb_admin ON tb_admin.id = tb_log.action_by")
		query = query.Where("tb_log.order_type_id = ?", 1) // เฉพาะฝากเงิน
		if req.StatusName != "" {
			if req.StatusName == "WAIT_PAYMENT" {
				query = query.Where("tb_log.transaction_status = 'WAIT_PAYMENT'")
			} else if req.StatusName == "PAID" {
				query = query.Where("tb_log.transaction_status = 'PAID'")
			} else {
				return list, 0, nil
			}
		}
		if req.BankTransactionStatus != "" {
			query = query.Where("tb_log.bank_transaction_status = ?", req.BankTransactionStatus)
		}
		if req.Search != "" {
			searchText := "%" + req.Search + "%"
			query = query.Where("tb_log.transaction_no LIKE ? OR tb_user.member_code LIKE ? OR tb_log.order_no LIKE ?", searchText, searchText, searchText)
		}
		if dateType.DateFrom != "" {
			startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
			if err != nil {
				return list, 0, err
			}
			query = query.Where("tb_log.created_at >= ? ", startDateAtBkk)
		}
		if dateType.DateTo != "" {
			endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
			if err != nil {
				return list, 0, err
			}
			query = query.Where("tb_log.created_at <=  ?", endDateAtBkk)
		}
		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		} else {
			query = query.Order("tb_log.id DESC")
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetRawOnepayPendingDepositOrderById(id int64) (*model.OnepayOrderResponse, error) {

	var record model.OnepayOrderResponse

	selectedFields := "*"

	if err := r.db.Table("paygate_onepay_order as tb_order").
		Select(selectedFields).
		Where("tb_order.id = ?", id).
		// Where("tb_order.transaction_status = ?", "SUCCESS").
		Where("tb_order.bank_transaction_status = ?", "PENDING").
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) IgnoreDbOnepayPendingDepositOrder(id int64, actionBy int64) error {

	updateBody := make(map[string]interface{})
	updateBody["bank_transaction_status"] = "IGNORE"
	updateBody["action_by"] = actionBy

	sql := r.db.Table("paygate_onepay_order").Where("id = ?", id).Where("bank_transaction_status = ?", "PENDING")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetOnepayOrderReportList(req model.PaygateOrderReportListRequest) ([]model.PaygateOrderReportResponse, int64, error) {

	var list []model.PaygateOrderReportResponse
	var total int64

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return list, 0, err
	}

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("paygate_onepay_order AS tb_log")
	count = count.Select("tb_log.id")
	count = count.Where("tb_log.order_type_id = ?", 1) // เฉพาะฝากเงิน
	if req.StatusName != "" {
		if req.StatusName == "WAIT_PAYMENT" {
			count = count.Where("tb_log.transaction_status = 'WAIT_PAYMENT'")
		} else if req.StatusName == "PAID" {
			count = count.Where("tb_log.transaction_status = 'PAID'")
		} else {
			return list, 0, nil
		}
	}
	if req.BankTransactionStatus != "" {
		count = count.Where("tb_log.bank_transaction_status = ?", req.BankTransactionStatus)
	}
	if req.Search != "" {
		count = count.Joins("INNER JOIN user AS tb_user ON tb_log.user_id = tb_user.id")
		searchText := "%" + req.Search + "%"
		count = count.Where("tb_log.transaction_no LIKE ? OR tb_user.member_code LIKE ? OR tb_log.order_no LIKE ?", searchText, searchText, searchText)
	}
	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return list, 0, err
		}
		count = count.Where("tb_log.created_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return list, 0, err
		}
		count = count.Where("tb_log.created_at <=  ?", endDateAtBkk)
	}
	if err := count.Count(&total).Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_log.id AS id, tb_log.created_at AS created_at, tb_user.member_code AS member_code, tb_admin.fullname AS admin_fullname"
		selectedFields += ", 'ONEPAY' AS merchant_name"
		selectedFields += ", tb_log.transaction_no AS transaction_no, tb_log.order_no AS order_no, tb_log.amount AS amount"
		selectedFields += ", tb_log.transaction_status AS transaction_status, tb_log.payment_at AS payment_at"
		selectedFields += ", tb_log.bank_transaction_status AS bank_transaction_status"
		selectedFields += ", tb_log.remark AS remark"
		query := r.db.Table("paygate_onepay_order AS tb_log")
		query = query.Select(selectedFields)
		query = query.Joins("INNER JOIN user AS tb_user ON tb_log.user_id = tb_user.id")
		query = query.Joins("LEFT JOIN admin AS tb_admin ON tb_admin.id = tb_log.action_by")
		query = query.Where("tb_log.order_type_id = ?", 1) // เฉพาะฝากเงิน
		if req.StatusName != "" {
			if req.StatusName == "WAIT_PAYMENT" {
				query = query.Where("tb_log.transaction_status = 'WAIT_PAYMENT'")
			} else if req.StatusName == "PAID" {
				query = query.Where("tb_log.transaction_status = 'PAID'")
			} else {
				return list, 0, nil
			}
		}
		if req.BankTransactionStatus != "" {
			query = query.Where("tb_log.bank_transaction_status = ?", req.BankTransactionStatus)
		}
		if req.Search != "" {
			searchText := "%" + req.Search + "%"
			query = query.Where("tb_log.transaction_no LIKE ? OR tb_user.member_code LIKE ? OR tb_log.order_no LIKE ?", searchText, searchText, searchText)
		}
		if dateType.DateFrom != "" {
			startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
			if err != nil {
				return list, 0, err
			}
			query = query.Where("tb_log.created_at >= ? ", startDateAtBkk)
		}
		if dateType.DateTo != "" {
			endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
			if err != nil {
				return list, 0, err
			}
			query = query.Where("tb_log.created_at <=  ?", endDateAtBkk)
		}
		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		} else {
			query = query.Order("tb_log.id DESC")
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetRawFlashpayPendingDepositOrderById(id int64) (*model.FlashpayOrderResponse, error) {

	var record model.FlashpayOrderResponse

	selectedFields := "*"

	if err := r.db.Table("paygate_flashpay_order as tb_order").
		Select(selectedFields).
		Where("tb_order.id = ?", id).
		// Where("tb_order.transaction_status = ?", "SUCCESS").
		Where("tb_order.bank_transaction_status = ?", "PENDING").
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) IgnoreDbFlashpayPendingDepositOrder(id int64, actionBy int64) error {

	updateBody := make(map[string]interface{})
	updateBody["bank_transaction_status"] = "IGNORE"
	updateBody["action_by"] = actionBy

	sql := r.db.Table("paygate_flashpay_order").Where("id = ?", id).Where("bank_transaction_status = ?", "PENDING")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetFlashpayOrderReportList(req model.PaygateOrderReportListRequest) ([]model.PaygateOrderReportResponse, int64, error) {

	var list []model.PaygateOrderReportResponse
	var total int64

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return list, 0, err
	}

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("paygate_flashpay_order AS tb_log")
	count = count.Select("tb_log.id")
	count = count.Where("tb_log.order_type_id = ?", 1) // เฉพาะฝากเงิน
	if req.StatusName != "" {
		if req.StatusName == "WAIT_PAYMENT" {
			count = count.Where("tb_log.transaction_status = 'WAIT_PAYMENT'")
		} else if req.StatusName == "PAID" {
			count = count.Where("tb_log.transaction_status = 'PAID'")
		} else {
			return list, 0, nil
		}
	}
	if req.BankTransactionStatus != "" {
		count = count.Where("tb_log.bank_transaction_status = ?", req.BankTransactionStatus)
	}
	if req.Search != "" {
		count = count.Joins("INNER JOIN user AS tb_user ON tb_log.user_id = tb_user.id")
		searchText := "%" + req.Search + "%"
		count = count.Where("tb_log.transaction_no LIKE ? OR tb_user.member_code LIKE ? OR tb_log.order_no LIKE ?", searchText, searchText, searchText)
	}
	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return list, 0, err
		}
		count = count.Where("tb_log.created_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return list, 0, err
		}
		count = count.Where("tb_log.created_at <=  ?", endDateAtBkk)
	}
	if err := count.Count(&total).Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_log.id AS id, tb_log.created_at AS created_at, tb_user.member_code AS member_code, tb_admin.fullname AS admin_fullname"
		selectedFields += ", 'FLASHPAY' AS merchant_name"
		selectedFields += ", tb_log.transaction_no AS transaction_no, tb_log.order_no AS order_no, tb_log.amount AS amount"
		selectedFields += ", tb_log.transaction_status AS transaction_status, tb_log.payment_at AS payment_at"
		selectedFields += ", tb_log.bank_transaction_status AS bank_transaction_status"
		selectedFields += ", tb_log.remark AS remark"
		query := r.db.Table("paygate_flashpay_order AS tb_log")
		query = query.Select(selectedFields)
		query = query.Joins("INNER JOIN user AS tb_user ON tb_log.user_id = tb_user.id")
		query = query.Joins("LEFT JOIN admin AS tb_admin ON tb_admin.id = tb_log.action_by")
		query = query.Where("tb_log.order_type_id = ?", 1) // เฉพาะฝากเงิน
		if req.StatusName != "" {
			if req.StatusName == "WAIT_PAYMENT" {
				query = query.Where("tb_log.transaction_status = 'WAIT_PAYMENT'")
			} else if req.StatusName == "PAID" {
				query = query.Where("tb_log.transaction_status = 'PAID'")
			} else {
				return list, 0, nil
			}
		}
		if req.BankTransactionStatus != "" {
			query = query.Where("tb_log.bank_transaction_status = ?", req.BankTransactionStatus)
		}
		if req.Search != "" {
			searchText := "%" + req.Search + "%"
			query = query.Where("tb_log.transaction_no LIKE ? OR tb_user.member_code LIKE ? OR tb_log.order_no LIKE ?", searchText, searchText, searchText)
		}
		if dateType.DateFrom != "" {
			startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
			if err != nil {
				return list, 0, err
			}
			query = query.Where("tb_log.created_at >= ? ", startDateAtBkk)
		}
		if dateType.DateTo != "" {
			endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
			if err != nil {
				return list, 0, err
			}
			query = query.Where("tb_log.created_at <=  ?", endDateAtBkk)
		}
		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		} else {
			query = query.Order("tb_log.id DESC")
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetRawBizpayPendingDepositOrderById(id int64) (*model.BizpayOrderResponse, error) {

	var record model.BizpayOrderResponse

	selectedFields := "*"

	if err := r.db.Table("paygate_bizpay_order as tb_order").
		Select(selectedFields).
		Where("tb_order.id = ?", id).
		// Where("tb_order.transaction_status = ?", "SUCCESS").
		Where("tb_order.bank_transaction_status = ?", "PENDING").
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) IgnoreDbBizpayPendingDepositOrder(id int64, actionBy int64) error {

	updateBody := make(map[string]interface{})
	updateBody["bank_transaction_status"] = "IGNORE"
	updateBody["action_by"] = actionBy

	sql := r.db.Table("paygate_bizpay_order").Where("id = ?", id).Where("bank_transaction_status = ?", "PENDING")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetBizpayOrderReportList(req model.PaygateOrderReportListRequest) ([]model.PaygateOrderReportResponse, int64, error) {

	var list []model.PaygateOrderReportResponse
	var total int64

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return list, 0, err
	}

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("paygate_bizpay_order AS tb_log")
	count = count.Select("tb_log.id")
	count = count.Where("tb_log.order_type_id = ?", 1) // เฉพาะฝากเงิน
	if req.StatusName != "" {
		if req.StatusName == "WAIT_PAYMENT" {
			count = count.Where("tb_log.transaction_status = 'WAIT_PAYMENT'")
		} else if req.StatusName == "PAID" {
			count = count.Where("tb_log.transaction_status = 'PAID'")
		} else {
			return list, 0, nil
		}
	}
	if req.BankTransactionStatus != "" {
		count = count.Where("tb_log.bank_transaction_status = ?", req.BankTransactionStatus)
	}
	if req.Search != "" {
		count = count.Joins("INNER JOIN user AS tb_user ON tb_log.user_id = tb_user.id")
		searchText := "%" + req.Search + "%"
		count = count.Where("tb_log.transaction_no LIKE ? OR tb_user.member_code LIKE ? OR tb_log.order_no LIKE ?", searchText, searchText, searchText)
	}
	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return list, 0, err
		}
		count = count.Where("tb_log.created_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return list, 0, err
		}
		count = count.Where("tb_log.created_at <=  ?", endDateAtBkk)
	}
	if err := count.Count(&total).Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_log.id AS id, tb_log.created_at AS created_at, tb_user.member_code AS member_code, tb_admin.fullname AS admin_fullname"
		selectedFields += ", 'BIZPAY' AS merchant_name"
		selectedFields += ", tb_log.transaction_no AS transaction_no, tb_log.order_no AS order_no, tb_log.amount AS amount"
		selectedFields += ", tb_log.transaction_status AS transaction_status, tb_log.payment_at AS payment_at"
		selectedFields += ", tb_log.bank_transaction_status AS bank_transaction_status"
		selectedFields += ", tb_log.remark AS remark"
		query := r.db.Table("paygate_bizpay_order AS tb_log")
		query = query.Select(selectedFields)
		query = query.Joins("INNER JOIN user AS tb_user ON tb_log.user_id = tb_user.id")
		query = query.Joins("LEFT JOIN admin AS tb_admin ON tb_admin.id = tb_log.action_by")
		query = query.Where("tb_log.order_type_id = ?", 1) // เฉพาะฝากเงิน
		if req.StatusName != "" {
			if req.StatusName == "WAIT_PAYMENT" {
				query = query.Where("tb_log.transaction_status = 'WAIT_PAYMENT'")
			} else if req.StatusName == "PAID" {
				query = query.Where("tb_log.transaction_status = 'PAID'")
			} else {
				return list, 0, nil
			}
		}
		if req.BankTransactionStatus != "" {
			query = query.Where("tb_log.bank_transaction_status = ?", req.BankTransactionStatus)
		}
		if req.Search != "" {
			searchText := "%" + req.Search + "%"
			query = query.Where("tb_log.transaction_no LIKE ? OR tb_user.member_code LIKE ? OR tb_log.order_no LIKE ?", searchText, searchText, searchText)
		}
		if dateType.DateFrom != "" {
			startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
			if err != nil {
				return list, 0, err
			}
			query = query.Where("tb_log.created_at >= ? ", startDateAtBkk)
		}
		if dateType.DateTo != "" {
			endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
			if err != nil {
				return list, 0, err
			}
			query = query.Where("tb_log.created_at <=  ?", endDateAtBkk)
		}
		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		} else {
			query = query.Order("tb_log.id DESC")
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetRawSugarpayPendingDepositOrderById(id int64) (*model.SugarpayOrderResponse, error) {

	var record model.SugarpayOrderResponse

	selectedFields := "*"

	if err := r.db.Table("paygate_sugarpay_order as tb_order").
		Select(selectedFields).
		Where("tb_order.id = ?", id).
		// Where("tb_order.transaction_status = ?", "SUCCESS").
		Where("tb_order.bank_transaction_status = ?", "PENDING").
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) IgnoreDbSugarpayPendingDepositOrder(id int64, actionBy int64) error {

	updateBody := make(map[string]interface{})
	updateBody["bank_transaction_status"] = "IGNORE"
	updateBody["action_by"] = actionBy

	sql := r.db.Table("paygate_sugarpay_order").Where("id = ?", id).Where("bank_transaction_status = ?", "PENDING")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetSugarpayOrderReportList(req model.PaygateOrderReportListRequest) ([]model.PaygateOrderReportResponse, int64, error) {

	var list []model.PaygateOrderReportResponse
	var total int64

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return list, 0, err
	}

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("paygate_sugarpay_order AS tb_log")
	count = count.Select("tb_log.id")
	count = count.Where("tb_log.order_type_id = ?", 1) // เฉพาะฝากเงิน
	if req.StatusName != "" {
		if req.StatusName == "WAIT_PAYMENT" {
			count = count.Where("tb_log.transaction_status = 'WAIT_PAYMENT'")
		} else if req.StatusName == "PAID" {
			count = count.Where("tb_log.transaction_status = 'PAID'")
		} else {
			return list, 0, nil
		}
	}
	if req.BankTransactionStatus != "" {
		count = count.Where("tb_log.bank_transaction_status = ?", req.BankTransactionStatus)
	}
	if req.Search != "" {
		count = count.Joins("INNER JOIN user AS tb_user ON tb_log.user_id = tb_user.id")
		searchText := "%" + req.Search + "%"
		count = count.Where("tb_log.transaction_no LIKE ? OR tb_user.member_code LIKE ? OR tb_log.order_no LIKE ?", searchText, searchText, searchText)
	}
	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return list, 0, err
		}
		count = count.Where("tb_log.created_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return list, 0, err
		}
		count = count.Where("tb_log.created_at <=  ?", endDateAtBkk)
	}
	if err := count.Count(&total).Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_log.id AS id, tb_log.created_at AS created_at, tb_user.member_code AS member_code, tb_admin.fullname AS admin_fullname"
		selectedFields += ", 'SUGARPAY' AS merchant_name"
		selectedFields += ", tb_log.transaction_no AS transaction_no, tb_log.order_no AS order_no, tb_log.amount AS amount"
		selectedFields += ", tb_log.transaction_status AS transaction_status, tb_log.payment_at AS payment_at"
		selectedFields += ", tb_log.bank_transaction_status AS bank_transaction_status"
		selectedFields += ", tb_log.remark AS remark"
		query := r.db.Table("paygate_sugarpay_order AS tb_log")
		query = query.Select(selectedFields)
		query = query.Joins("INNER JOIN user AS tb_user ON tb_log.user_id = tb_user.id")
		query = query.Joins("LEFT JOIN admin AS tb_admin ON tb_admin.id = tb_log.action_by")
		query = query.Where("tb_log.order_type_id = ?", 1) // เฉพาะฝากเงิน
		if req.StatusName != "" {
			if req.StatusName == "WAIT_PAYMENT" {
				query = query.Where("tb_log.transaction_status = 'WAIT_PAYMENT'")
			} else if req.StatusName == "PAID" {
				query = query.Where("tb_log.transaction_status = 'PAID'")
			} else {
				return list, 0, nil
			}
		}
		if req.BankTransactionStatus != "" {
			query = query.Where("tb_log.bank_transaction_status = ?", req.BankTransactionStatus)
		}
		if req.Search != "" {
			searchText := "%" + req.Search + "%"
			query = query.Where("tb_log.transaction_no LIKE ? OR tb_user.member_code LIKE ? OR tb_log.order_no LIKE ?", searchText, searchText, searchText)
		}
		if dateType.DateFrom != "" {
			startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
			if err != nil {
				return list, 0, err
			}
			query = query.Where("tb_log.created_at >= ? ", startDateAtBkk)
		}
		if dateType.DateTo != "" {
			endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
			if err != nil {
				return list, 0, err
			}
			query = query.Where("tb_log.created_at <=  ?", endDateAtBkk)
		}
		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		} else {
			query = query.Order("tb_log.id DESC")
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetRawZmanpayPendingDepositOrderById(id int64) (*model.ZmanpayOrderResponse, error) {

	var record model.ZmanpayOrderResponse

	selectedFields := "*"

	if err := r.db.Table("paygate_zmanpay_order as tb_order").
		Select(selectedFields).
		Where("tb_order.id = ?", id).
		// Where("tb_order.transaction_status = ?", "SUCCESS").
		Where("tb_order.bank_transaction_status = ?", "PENDING").
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) IgnoreDbZmanpayPendingDepositOrder(id int64, actionBy int64) error {

	updateBody := make(map[string]interface{})
	updateBody["bank_transaction_status"] = "IGNORE"
	updateBody["action_by"] = actionBy

	sql := r.db.Table("paygate_zmanpay_order").Where("id = ?", id).Where("bank_transaction_status = ?", "PENDING")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetZmanpayOrderReportList(req model.PaygateOrderReportListRequest) ([]model.PaygateOrderReportResponse, int64, error) {

	var list []model.PaygateOrderReportResponse
	var total int64

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return list, 0, err
	}

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("paygate_zmanpay_order AS tb_log")
	count = count.Select("tb_log.id")
	count = count.Where("tb_log.order_type_id = ?", 1) // เฉพาะฝากเงิน
	if req.StatusName != "" {
		if req.StatusName == "WAIT_PAYMENT" {
			count = count.Where("tb_log.transaction_status = 'WAIT_PAYMENT'")
		} else if req.StatusName == "PAID" {
			count = count.Where("tb_log.transaction_status = 'PAID'")
		} else {
			return list, 0, nil
		}
	}
	if req.BankTransactionStatus != "" {
		count = count.Where("tb_log.bank_transaction_status = ?", req.BankTransactionStatus)
	}
	if req.Search != "" {
		count = count.Joins("INNER JOIN user AS tb_user ON tb_log.user_id = tb_user.id")
		searchText := "%" + req.Search + "%"
		count = count.Where("tb_log.transaction_no LIKE ? OR tb_user.member_code LIKE ? OR tb_log.order_no LIKE ?", searchText, searchText, searchText)
	}
	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return list, 0, err
		}
		count = count.Where("tb_log.created_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return list, 0, err
		}
		count = count.Where("tb_log.created_at <=  ?", endDateAtBkk)
	}
	if err := count.Count(&total).Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_log.id AS id, tb_log.created_at AS created_at, tb_user.member_code AS member_code, tb_admin.fullname AS admin_fullname"
		selectedFields += ", 'ZMANPAY' AS merchant_name"
		selectedFields += ", tb_log.transaction_no AS transaction_no, tb_log.order_no AS order_no, tb_log.amount AS amount"
		selectedFields += ", tb_log.transaction_status AS transaction_status, tb_log.payment_at AS payment_at"
		selectedFields += ", tb_log.bank_transaction_status AS bank_transaction_status"
		selectedFields += ", tb_log.remark AS remark"
		query := r.db.Table("paygate_zmanpay_order AS tb_log")
		query = query.Select(selectedFields)
		query = query.Joins("INNER JOIN user AS tb_user ON tb_log.user_id = tb_user.id")
		query = query.Joins("LEFT JOIN admin AS tb_admin ON tb_admin.id = tb_log.action_by")
		query = query.Where("tb_log.order_type_id = ?", 1) // เฉพาะฝากเงิน
		if req.StatusName != "" {
			if req.StatusName == "WAIT_PAYMENT" {
				query = query.Where("tb_log.transaction_status = 'WAIT_PAYMENT'")
			} else if req.StatusName == "PAID" {
				query = query.Where("tb_log.transaction_status = 'PAID'")
			} else {
				return list, 0, nil
			}
		}
		if req.BankTransactionStatus != "" {
			query = query.Where("tb_log.bank_transaction_status = ?", req.BankTransactionStatus)
		}
		if req.Search != "" {
			searchText := "%" + req.Search + "%"
			query = query.Where("tb_log.transaction_no LIKE ? OR tb_user.member_code LIKE ? OR tb_log.order_no LIKE ?", searchText, searchText, searchText)
		}
		if dateType.DateFrom != "" {
			startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
			if err != nil {
				return list, 0, err
			}
			query = query.Where("tb_log.created_at >= ? ", startDateAtBkk)
		}
		if dateType.DateTo != "" {
			endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
			if err != nil {
				return list, 0, err
			}
			query = query.Where("tb_log.created_at <=  ?", endDateAtBkk)
		}
		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		} else {
			query = query.Order("tb_log.id DESC")
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetRawPostmanPayPendingDepositOrderById(id int64) (*model.PostmanPayOrderResponse, error) {

	var record model.PostmanPayOrderResponse

	selectedFields := "*"

	if err := r.db.Table("paygate_postmanpay_order as tb_order").
		Select(selectedFields).
		Where("tb_order.id = ?", id).
		// Where("tb_order.transaction_status = ?", "SUCCESS").
		Where("tb_order.bank_transaction_status = ?", "PENDING").
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) IgnoreDbPostmanPayPendingDepositOrder(id int64, actionBy int64) error {

	updateBody := make(map[string]interface{})
	updateBody["bank_transaction_status"] = "IGNORE"
	updateBody["action_by"] = actionBy

	sql := r.db.Table("paygate_postmanpay_order").Where("id = ?", id).Where("bank_transaction_status = ?", "PENDING")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetPostmanPayOrderReportList(req model.PaygateOrderReportListRequest) ([]model.PaygateOrderReportResponse, int64, error) {

	var list []model.PaygateOrderReportResponse
	var total int64

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return list, 0, err
	}

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("paygate_postmanpay_order AS tb_log")
	count = count.Select("tb_log.id")
	count = count.Where("tb_log.order_type_id = ?", 1) // เฉพาะฝากเงิน
	if req.StatusName != "" {
		if req.StatusName == "WAIT_PAYMENT" {
			count = count.Where("tb_log.transaction_status = 'WAIT_PAYMENT'")
		} else if req.StatusName == "PAID" {
			count = count.Where("tb_log.transaction_status = 'PAID'")
		} else {
			return list, 0, nil
		}
	}
	if req.BankTransactionStatus != "" {
		count = count.Where("tb_log.bank_transaction_status = ?", req.BankTransactionStatus)
	}
	if req.Search != "" {
		count = count.Joins("INNER JOIN user AS tb_user ON tb_log.user_id = tb_user.id")
		searchText := "%" + req.Search + "%"
		count = count.Where("tb_log.transaction_no LIKE ? OR tb_user.member_code LIKE ? OR tb_log.order_no LIKE ?", searchText, searchText, searchText)
	}
	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return list, 0, err
		}
		count = count.Where("tb_log.created_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return list, 0, err
		}
		count = count.Where("tb_log.created_at <=  ?", endDateAtBkk)
	}
	if err := count.Count(&total).Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_log.id AS id, tb_log.created_at AS created_at, tb_user.member_code AS member_code, tb_admin.fullname AS admin_fullname"
		selectedFields += ", 'POSTMANPAY' AS merchant_name"
		selectedFields += ", tb_log.transaction_no AS transaction_no, tb_log.order_no AS order_no, tb_log.amount AS amount"
		selectedFields += ", tb_log.transaction_status AS transaction_status, tb_log.payment_at AS payment_at"
		selectedFields += ", tb_log.bank_transaction_status AS bank_transaction_status"
		selectedFields += ", tb_log.remark AS remark"
		query := r.db.Table("paygate_postmanpay_order AS tb_log")
		query = query.Select(selectedFields)
		query = query.Joins("INNER JOIN user AS tb_user ON tb_log.user_id = tb_user.id")
		query = query.Joins("LEFT JOIN admin AS tb_admin ON tb_admin.id = tb_log.action_by")
		query = query.Where("tb_log.order_type_id = ?", 1) // เฉพาะฝากเงิน
		if req.StatusName != "" {
			if req.StatusName == "WAIT_PAYMENT" {
				query = query.Where("tb_log.transaction_status = 'WAIT_PAYMENT'")
			} else if req.StatusName == "PAID" {
				query = query.Where("tb_log.transaction_status = 'PAID'")
			} else {
				return list, 0, nil
			}
		}
		if req.BankTransactionStatus != "" {
			query = query.Where("tb_log.bank_transaction_status = ?", req.BankTransactionStatus)
		}
		if req.Search != "" {
			searchText := "%" + req.Search + "%"
			query = query.Where("tb_log.transaction_no LIKE ? OR tb_user.member_code LIKE ? OR tb_log.order_no LIKE ?", searchText, searchText, searchText)
		}
		if dateType.DateFrom != "" {
			startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
			if err != nil {
				return list, 0, err
			}
			query = query.Where("tb_log.created_at >= ? ", startDateAtBkk)
		}
		if dateType.DateTo != "" {
			endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
			if err != nil {
				return list, 0, err
			}
			query = query.Where("tb_log.created_at <=  ?", endDateAtBkk)
		}
		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		} else {
			query = query.Order("tb_log.id DESC")
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetRawMazepayPendingDepositOrderById(id int64) (*model.MazepayOrderResponse, error) {

	var record model.MazepayOrderResponse

	selectedFields := "*"

	if err := r.db.Table("paygate_mazepay_order as tb_order").
		Select(selectedFields).
		Where("tb_order.id = ?", id).
		// Where("tb_order.transaction_status = ?", "SUCCESS").
		Where("tb_order.bank_transaction_status = ?", "PENDING").
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) IgnoreDbMazepayPendingDepositOrder(id int64, actionBy int64) error {

	updateBody := make(map[string]interface{})
	updateBody["bank_transaction_status"] = "IGNORE"
	updateBody["action_by"] = actionBy

	sql := r.db.Table("paygate_mazepay_order").Where("id = ?", id).Where("bank_transaction_status = ?", "PENDING")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetMazepayOrderReportList(req model.PaygateOrderReportListRequest) ([]model.PaygateOrderReportResponse, int64, error) {

	var list []model.PaygateOrderReportResponse
	var total int64

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return list, 0, err
	}

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("paygate_mazepay_order AS tb_log")
	count = count.Select("tb_log.id")
	count = count.Where("tb_log.order_type_id = ?", 1) // เฉพาะฝากเงิน
	if req.StatusName != "" {
		if req.StatusName == "WAIT_PAYMENT" {
			count = count.Where("tb_log.transaction_status = 'WAIT_PAYMENT'")
		} else if req.StatusName == "PAID" {
			count = count.Where("tb_log.transaction_status = 'PAID'")
		} else {
			return list, 0, nil
		}
	}
	if req.BankTransactionStatus != "" {
		count = count.Where("tb_log.bank_transaction_status = ?", req.BankTransactionStatus)
	}
	if req.Search != "" {
		count = count.Joins("INNER JOIN user AS tb_user ON tb_log.user_id = tb_user.id")
		searchText := "%" + req.Search + "%"
		count = count.Where("tb_log.transaction_no LIKE ? OR tb_user.member_code LIKE ? OR tb_log.order_no LIKE ?", searchText, searchText, searchText)
	}
	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return list, 0, err
		}
		count = count.Where("tb_log.created_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return list, 0, err
		}
		count = count.Where("tb_log.created_at <=  ?", endDateAtBkk)
	}
	if err := count.Count(&total).Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_log.id AS id, tb_log.created_at AS created_at, tb_user.member_code AS member_code, tb_admin.fullname AS admin_fullname"
		selectedFields += ", 'MAZEPAY' AS merchant_name"
		selectedFields += ", tb_log.transaction_no AS transaction_no, tb_log.order_no AS order_no, tb_log.amount AS amount"
		selectedFields += ", tb_log.transaction_status AS transaction_status, tb_log.payment_at AS payment_at"
		selectedFields += ", tb_log.bank_transaction_status AS bank_transaction_status"
		selectedFields += ", tb_log.remark AS remark"
		query := r.db.Table("paygate_mazepay_order AS tb_log")
		query = query.Select(selectedFields)
		query = query.Joins("INNER JOIN user AS tb_user ON tb_log.user_id = tb_user.id")
		query = query.Joins("LEFT JOIN admin AS tb_admin ON tb_admin.id = tb_log.action_by")
		query = query.Where("tb_log.order_type_id = ?", 1) // เฉพาะฝากเงิน
		if req.StatusName != "" {
			if req.StatusName == "WAIT_PAYMENT" {
				query = query.Where("tb_log.transaction_status = 'WAIT_PAYMENT'")
			} else if req.StatusName == "PAID" {
				query = query.Where("tb_log.transaction_status = 'PAID'")
			} else {
				return list, 0, nil
			}
		}
		if req.BankTransactionStatus != "" {
			query = query.Where("tb_log.bank_transaction_status = ?", req.BankTransactionStatus)
		}
		if req.Search != "" {
			searchText := "%" + req.Search + "%"
			query = query.Where("tb_log.transaction_no LIKE ? OR tb_user.member_code LIKE ? OR tb_log.order_no LIKE ?", searchText, searchText, searchText)
		}
		if dateType.DateFrom != "" {
			startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
			if err != nil {
				return list, 0, err
			}
			query = query.Where("tb_log.created_at >= ? ", startDateAtBkk)
		}
		if dateType.DateTo != "" {
			endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
			if err != nil {
				return list, 0, err
			}
			query = query.Where("tb_log.created_at <=  ?", endDateAtBkk)
		}
		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		} else {
			query = query.Order("tb_log.id DESC")
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetRawMeepayPendingDepositOrderById(id int64) (*model.MeepayOrderResponse, error) {

	var record model.MeepayOrderResponse

	selectedFields := "*"

	if err := r.db.Table("paygate_meepay_order as tb_order").
		Select(selectedFields).
		Where("tb_order.id = ?", id).
		// Where("tb_order.transaction_status = ?", "SUCCESS").
		Where("tb_order.bank_transaction_status = ?", "PENDING").
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) IgnoreDbMeepayPendingDepositOrder(id int64, actionBy int64) error {

	updateBody := make(map[string]interface{})
	updateBody["bank_transaction_status"] = "IGNORE"
	updateBody["action_by"] = actionBy

	sql := r.db.Table("paygate_meepay_order").Where("id = ?", id).Where("bank_transaction_status = ?", "PENDING")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetMeepayOrderReportList(req model.PaygateOrderReportListRequest) ([]model.PaygateOrderReportResponse, int64, error) {

	var list []model.PaygateOrderReportResponse
	var total int64

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return list, 0, err
	}

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("paygate_meepay_order AS tb_log")
	count = count.Select("tb_log.id")
	count = count.Where("tb_log.order_type_id = ?", 1) // เฉพาะฝากเงิน
	if req.StatusName != "" {
		if req.StatusName == "WAIT_PAYMENT" {
			count = count.Where("tb_log.transaction_status = 'WAIT_PAYMENT'")
		} else if req.StatusName == "PAID" {
			count = count.Where("tb_log.transaction_status = 'PAID'")
		} else {
			return list, 0, nil
		}
	}
	if req.BankTransactionStatus != "" {
		count = count.Where("tb_log.bank_transaction_status = ?", req.BankTransactionStatus)
	}
	if req.Search != "" {
		count = count.Joins("INNER JOIN user AS tb_user ON tb_log.user_id = tb_user.id")
		searchText := "%" + req.Search + "%"
		count = count.Where("tb_log.transaction_no LIKE ? OR tb_user.member_code LIKE ? OR tb_log.order_no LIKE ?", searchText, searchText, searchText)
	}
	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return list, 0, err
		}
		count = count.Where("tb_log.created_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return list, 0, err
		}
		count = count.Where("tb_log.created_at <=  ?", endDateAtBkk)
	}
	if err := count.Count(&total).Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_log.id AS id, tb_log.created_at AS created_at, tb_user.member_code AS member_code, tb_admin.fullname AS admin_fullname"
		selectedFields += ", 'MEEPAY' AS merchant_name"
		selectedFields += ", tb_log.transaction_no AS transaction_no, tb_log.order_no AS order_no, tb_log.amount AS amount"
		selectedFields += ", tb_log.transaction_status AS transaction_status, tb_log.payment_at AS payment_at"
		selectedFields += ", tb_log.bank_transaction_status AS bank_transaction_status"
		selectedFields += ", tb_log.remark AS remark"
		query := r.db.Table("paygate_meepay_order AS tb_log")
		query = query.Select(selectedFields)
		query = query.Joins("INNER JOIN user AS tb_user ON tb_log.user_id = tb_user.id")
		query = query.Joins("LEFT JOIN admin AS tb_admin ON tb_admin.id = tb_log.action_by")
		query = query.Where("tb_log.order_type_id = ?", 1) // เฉพาะฝากเงิน
		if req.StatusName != "" {
			if req.StatusName == "WAIT_PAYMENT" {
				query = query.Where("tb_log.transaction_status = 'WAIT_PAYMENT'")
			} else if req.StatusName == "PAID" {
				query = query.Where("tb_log.transaction_status = 'PAID'")
			} else {
				return list, 0, nil
			}
		}
		if req.BankTransactionStatus != "" {
			query = query.Where("tb_log.bank_transaction_status = ?", req.BankTransactionStatus)
		}
		if req.Search != "" {
			searchText := "%" + req.Search + "%"
			query = query.Where("tb_log.transaction_no LIKE ? OR tb_user.member_code LIKE ? OR tb_log.order_no LIKE ?", searchText, searchText, searchText)
		}
		if dateType.DateFrom != "" {
			startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
			if err != nil {
				return list, 0, err
			}
			query = query.Where("tb_log.created_at >= ? ", startDateAtBkk)
		}
		if dateType.DateTo != "" {
			endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
			if err != nil {
				return list, 0, err
			}
			query = query.Where("tb_log.created_at <=  ?", endDateAtBkk)
		}
		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		} else {
			query = query.Order("tb_log.id DESC")
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetLastestAnyPaygateDeposit(paymentId int64, userId int64) (*model.LastestAnyPaygateDeposit, error) {

	var record model.LastestAnyPaygateDeposit

	tableName := ""
	switch paymentId {
	case 1:
		tableName = "paygate_heng_order"
	case 2:
		tableName = "paygate_luckyth_order"
	case 3:
		tableName = "paygate_papaya_order"
	case 4:
		tableName = "paygate_payonex_order"
	case 5:
		tableName = "paygate_jbpay_order"
	case 6:
		tableName = "paygate_pompay_order"
	case 7:
		tableName = "paygate_paymentco_order"
	case 8:
		tableName = "paygate_zappay_order"
	case 9:
		tableName = "paygate_onepay_order"
	case 10:
		tableName = "paygate_flashpay_order"
	case 11:
		tableName = "paygate_bizpay_order"
	case 12:
		tableName = "paygate_sugarpay_order"
	case 13:
		tableName = "paygate_zmanpay_order"
	case 14:
		tableName = "paygate_postmanpay_order"
	case 15:
		tableName = "paygate_mazepay_order"
	case 16:
		tableName = "paygate_meepay_order"
	default:
		tableName = ""
	}
	if tableName == "" {
		return nil, fmt.Errorf("INVALID_PAYMENT_ID")
	}

	selectedFields := "tb_order.id AS id, tb_order.user_id AS user_id, tb_order.amount AS amount, tb_order.created_at AS created_at"

	if err := r.db.Table(""+tableName+" as tb_order").
		Select(selectedFields).
		Where("tb_order.user_id = ?", userId).
		Where("tb_order.order_type_id = ?", 1). // เฉพาะฝากเงิน
		Where("tb_order.transaction_status = ?", "WAIT_PAYMENT").
		Order("tb_order.id DESC").
		Take(&record).
		Error; err != nil {
		return nil, err
	}

	// if record.Id == 0 {
	// 	return nil, fmt.Errorf("NOT_FOUND")
	// }
	record.MerchantId = paymentId

	return &record, nil
}

func (r repo) GetPaygateMerchantLimitList() ([]model.PaygateMerchantLimitResponse, error) {

	var list []model.PaygateMerchantLimitResponse

	selectedFields := "tb_merchant.id AS id, tb_merchant.name AS name, tb_merchant.has_deposit AS has_deposit, tb_merchant.has_withdraw AS has_withdraw"
	// selectedFields += ", tb_merchant.payment_withdraw_minimum AS payment_withdraw_minimum, tb_merchant.payment_withdraw_maximum AS payment_withdraw_maximum"
	query := r.db.Table("paygate_merchant as tb_merchant")
	query = query.Select(selectedFields)
	if err := query.
		Where("tb_merchant.deleted_at IS NULL").
		Scan(&list).
		Error; err != nil {
		return nil, err
	}

	for i, record := range list {
		// Set default values for PaymentWithdrawMinimum and PaymentWithdrawMaximum //
		if record.Id == model.PAYGATE_MERCHANT_ID_PAYONEX {
			list[i].PaymentDepositMinimum = model.PAYONEX_DEPOSIT_AMOUNT_MINIMUM
			list[i].PaymentDepositMaximum = model.PAYONEX_DEPOSIT_AMOUNT_MAXIMUM
			list[i].PaymentWithdrawMinimum = model.PAYONEX_WITHDRAW_AMOUNT_MINIMUM
			list[i].PaymentWithdrawMaximum = model.PAYONEX_WITHDRAW_AMOUNT_MAXIMUM
		} else if record.Id == model.PAYGATE_MERCHANT_ID_JBPAY {
			list[i].PaymentDepositMinimum = model.JBPAY_DEPOSIT_AMOUNT_MINIMUM
			list[i].PaymentDepositMaximum = model.JBPAY_DEPOSIT_AMOUNT_MAXIMUM
			list[i].PaymentWithdrawMinimum = model.JBPAY_WITHDRAW_AMOUNT_MINIMUM
			list[i].PaymentWithdrawMaximum = model.JBPAY_WITHDRAW_AMOUNT_MAXIMUM
		} else if record.Id == model.PAYGATE_MERCHANT_ID_POMPAY {
			list[i].PaymentDepositMinimum = model.POMPAY_DEPOSIT_AMOUNT_MINIMUM
			list[i].PaymentDepositMaximum = model.POMPAY_DEPOSIT_AMOUNT_MAXIMUM
			list[i].PaymentWithdrawMinimum = model.POMPAY_WITHDRAW_AMOUNT_MINIMUM
			list[i].PaymentWithdrawMaximum = model.POMPAY_WITHDRAW_AMOUNT_MAXIMUM
		} else if record.Id == model.PAYGATE_MERCHANT_ID_ZAPPAY {
			list[i].PaymentDepositMinimum = model.ZAPPAY_DEPOSIT_AMOUNT_MINIMUM
			list[i].PaymentDepositMaximum = model.ZAPPAY_DEPOSIT_AMOUNT_MAXIMUM
			list[i].PaymentWithdrawMinimum = model.ZAPPAY_WITHDRAW_AMOUNT_MINIMUM
			list[i].PaymentWithdrawMaximum = model.ZAPPAY_WITHDRAW_AMOUNT_MAXIMUM
		} else if record.Id == model.PAYGATE_MERCHANT_ID_BIZPAY {
			list[i].PaymentDepositMinimum = model.BIZPAY_DEFMIN_DEPOSIT_AMOUNT
			list[i].PaymentDepositMaximum = model.BIZPAY_DEFMAX_DEPOSIT_AMOUNT
			list[i].PaymentWithdrawMinimum = model.BIZPAY_DEFMIN_WITHDRAW_AMOUNT
			list[i].PaymentWithdrawMaximum = model.BIZPAY_DEFMAX_WITHDRAW_AMOUNT
		} else if record.Id == model.PAYGATE_MERCHANT_ID_SUGARPAY {
			list[i].PaymentDepositMinimum = model.SUGARPAY_DEFMIN_DEPOSIT_AMOUNT
			list[i].PaymentDepositMaximum = model.SUGARPAY_DEFMAX_DEPOSIT_AMOUNT
			list[i].PaymentWithdrawMinimum = model.SUGARPAY_DEFMIN_WITHDRAW_AMOUNT
			list[i].PaymentWithdrawMaximum = model.SUGARPAY_DEFMAX_WITHDRAW_AMOUNT
		} else if record.Id == model.PAYGATE_MERCHANT_ID_ZMANPAY {
			list[i].PaymentDepositMinimum = model.ZMANPAY_DEFMIN_DEPOSIT_AMOUNT
			list[i].PaymentDepositMaximum = model.ZMANPAY_DEFMAX_DEPOSIT_AMOUNT
			list[i].PaymentWithdrawMinimum = model.ZMANPAY_DEFMIN_WITHDRAW_AMOUNT
			list[i].PaymentWithdrawMaximum = model.ZMANPAY_DEFMAX_WITHDRAW_AMOUNT
		} else if record.Id == model.PAYGATE_MERCHANT_ID_POSTMANPAY {
			list[i].PaymentDepositMinimum = model.POSTMANPAY_DEFMIN_DEPOSIT_AMOUNT
			list[i].PaymentDepositMaximum = model.POSTMANPAY_DEFMAX_DEPOSIT_AMOUNT
			list[i].PaymentWithdrawMinimum = model.POSTMANPAY_DEFMIN_WITHDRAW_AMOUNT
			list[i].PaymentWithdrawMaximum = model.POSTMANPAY_DEFMAX_WITHDRAW_AMOUNT
		} else if record.Id == model.PAYGATE_MERCHANT_ID_MAZEPAY {
			list[i].PaymentDepositMinimum = model.MAZEPAY_DEFMIN_DEPOSIT_AMOUNT
			list[i].PaymentDepositMaximum = model.MAZEPAY_DEFMAX_DEPOSIT_AMOUNT
			list[i].PaymentWithdrawMinimum = model.MAZEPAY_DEFMIN_WITHDRAW_AMOUNT
			list[i].PaymentWithdrawMaximum = model.MAZEPAY_DEFMAX_WITHDRAW_AMOUNT
		} else if record.Id == model.PAYGATE_MERCHANT_ID_MEEPAY {
			list[i].PaymentDepositMinimum = model.MEEPAY_DEFMIN_DEPOSIT_AMOUNT
			list[i].PaymentDepositMaximum = model.MEEPAY_DEFMAX_DEPOSIT_AMOUNT
			list[i].PaymentWithdrawMinimum = model.MEEPAY_DEFMIN_WITHDRAW_AMOUNT
			list[i].PaymentWithdrawMaximum = model.MEEPAY_DEFMAX_WITHDRAW_AMOUNT
		}

		// CALLBACK URL for Copy //
		if record.Id == model.PAYGATE_MERCHANT_ID_JBPAY {
			// webhookRoute.POST("/jbpayment/repay-callback", handler.createJbpayDepositWebhook)
			// webhookRoute.POST("/jbpayment/loan-callback", handler.createJbpayWithdrawWebhook)
			webhookDomain := os.Getenv("CYBERGAME_WEBHOOK_ENDPOINT")
			repayCallbackurl := fmt.Sprintf("%s/jbpayment/repay-callback", webhookDomain)
			list[i].CallBackList = append(list[i].CallBackList, model.PaygateMerchantCallbackUrl{
				Label: "JBPAY_REPAY_CALLBACK",
				Url:   repayCallbackurl,
			})
			// JBPAY_LOAN_CALLBACK
			loanCallbackurl := fmt.Sprintf("%s/jbpayment/loan-callback", webhookDomain)
			list[i].CallBackList = append(list[i].CallBackList, model.PaygateMerchantCallbackUrl{
				Label: "JBPAY_LOAN_CALLBACK",
				Url:   loanCallbackurl,
			})
		} else if record.Id == model.PAYGATE_MERCHANT_ID_ZAPPAY {
			// webhookRoute.POST("/zappay/repay-callback", handler.createZappayDepositWebhook)
			// webhookRoute.POST("/zappay/loan-callback", handler.createZappayWithdrawWebhook)
			webhookDomain := os.Getenv("CYBERGAME_WEBHOOK_ENDPOINT")
			repayCallbackurl := fmt.Sprintf("%s/zappay/repay-callback", webhookDomain)
			list[i].CallBackList = append(list[i].CallBackList, model.PaygateMerchantCallbackUrl{
				Label: "ZAPPAY_REPAY_CALLBACK",
				Url:   repayCallbackurl,
			})
			// ZAPPAY_LOAN_CALLBACK
			loanCallbackurl := fmt.Sprintf("%s/zappay/loan-callback", webhookDomain)
			list[i].CallBackList = append(list[i].CallBackList, model.PaygateMerchantCallbackUrl{
				Label: "ZAPPAY_LOAN_CALLBACK",
				Url:   loanCallbackurl,
			})
		}
	}

	return list, nil
}

func (r repo) GetNonActivePaygateMerchantOption() ([]model.SelectOptions, error) {

	var list []model.SelectOptions

	// activeIds
	var activeProviderIds []int64
	if err := r.db.Table("paygate_account AS tb_account").
		Select("tb_account.provider_id").
		Where("tb_account.deleted_at IS NULL").
		Pluck("tb_account.provider_id", &activeProviderIds).
		Error; err != nil {
		return nil, err
	}

	selectedFields := "tb_merchant.id AS id, tb_merchant.name AS label, tb_merchant.name AS value"
	query := r.db.Table("paygate_merchant as tb_merchant")
	query = query.Select(selectedFields)
	if len(activeProviderIds) > 0 {
		query = query.Where("tb_merchant.id NOT IN (?)", activeProviderIds)
	}
	if err := query.
		Where("tb_merchant.deleted_at IS NULL").
		Scan(&list).
		Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (r repo) GetPaygateWithdrawAccount() (*model.PaygateAccountResponse, error) {

	var record model.PaygateAccountResponse

	selectedFields := "*"
	if err := r.db.Table("paygate_account as tb_merchant").
		Select(selectedFields).
		Where("tb_merchant.is_withdraw_enabled = ?", true).
		Where("tb_merchant.deleted_at IS NULL").
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) SetSingleEnableWithdrawAccount(id int64) error {

	// When enable this account, set all other accounts to false //
	sql := r.db.Table("paygate_account").Where("id != ?", id)
	if err := sql.Updates(map[string]interface{}{"is_withdraw_enabled": false}).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetPaygateMerchantById(id int64) (*model.PaygateMerchantResponse, error) {

	var record model.PaygateMerchantResponse

	selectedFields := "*"
	if err := r.db.Table("paygate_merchant as tb_merchant").
		Select(selectedFields).
		Where("tb_merchant.id = ?", id).
		Where("tb_merchant.deleted_at IS NULL").
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetPaygateAccountById(id int64) (*model.PaygateAccountResponse, error) {

	var record model.PaygateAccountResponse

	selectedFields := "*"
	if err := r.db.Table("paygate_account as tb_merchant").
		Select(selectedFields).
		Where("tb_merchant.id = ?", id).
		Where("tb_merchant.deleted_at IS NULL").
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetPaygateAccountList(req model.PaygateAccountListRequest) ([]model.PaygateAccountResponse, int64, error) {

	var list []model.PaygateAccountResponse
	var total int64

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("paygate_account as tb_merchant")
	count = count.Select("tb_merchant.id")
	if err := count.
		Where("tb_merchant.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "*"
		query := r.db.Table("paygate_account as tb_merchant")
		query = query.Select(selectedFields)

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Where("tb_merchant.deleted_at IS NULL").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}

	return list, total, nil
}

func (r repo) GetPaygateAccountByProviderId(providerId int64) (*model.PaygateAccountResponse, error) {

	var record model.PaygateAccountResponse

	selectedFields := "*"
	if err := r.db.Table("paygate_account as tb_merchant").
		Select(selectedFields).
		Where("tb_merchant.provider_id = ?", providerId).
		Where("tb_merchant.deleted_at IS NULL").
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) CreatePaygateAccount(body model.PaygateAccountCreateBody) (*int64, error) {

	if err := r.db.Table("paygate_account").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) UpdatePaygateAccount(id int64, body model.PaygateAccountUpdateBody) error {

	if err := r.db.Table("paygate_account").Where("id = ?", id).Updates(body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) DeletePaygateAccount(id int64) error {

	if err := r.db.Table("paygate_account").Where("id = ?", id).Delete(&model.PaygateAccount{}).Error; err != nil {
		return err
	}
	return nil
}
