package repository

import (
	"context"
	"cybergame-api/model"
	"io"
	"os"

	"cloud.google.com/go/storage"
	"google.golang.org/api/option"
	"gorm.io/gorm"
)

func NewFileRepository(db *gorm.DB) FileRepository {
	return &repo{db}
}

type FileRepository interface {
	UploadFile(data model.FileUpload) (*string, error)
	DeleteFile(path string) error
}

func (r repo) UploadFile(data model.FileUpload) (*string, error) {

	bucket := os.Getenv("BUCKET_NAME")
	opts := option.WithCredentialsFile("google_service_account.json")

	ctx := context.Background()
	storageClient, err := storage.NewClient(ctx, opts)
	if err != nil {
		return nil, err
	}

	sw := storageClient.Bucket(bucket).Object(data.Path).NewWriter(ctx)
	if _, err := io.Copy(sw, data.File); err != nil {
		return nil, err
	}

	if err := sw.Close(); err != nil {
		return nil, err
	}

	result := sw.Attrs().Name

	return &result, nil
}

func (s repo) DeleteFile(path string) error {

	bucket := os.Getenv("BUCKET_NAME")
	opts := option.WithCredentialsFile("google_service_account.json")

	ctx := context.Background()
	storageClient, err := storage.NewClient(ctx, opts)
	if err != nil {
		return err
	}

	if err := storageClient.Bucket(bucket).Object(path).Delete(ctx); err != nil {
		return err
	}

	return nil
}
