package repository

import (
	"bytes"
	"cybergame-api/helper"
	"cybergame-api/model"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strings"
	"time"

	"gorm.io/gorm"
)

func NewOnepayRepository(db *gorm.DB) OnepayRepository {
	return &repo{db}
}

type OnepayRepository interface {
	GetDb() *gorm.DB
	// Onepay-RD
	// OnepayEncryptRepayDesposit(partnerKey string, amount int64) (*model.OnepayEncryptPayload, error)

	// MERCHANT
	// GetNOTUSEPaygateMerchantById(id int64) (*model.PaygateMerchantNgResponse, error)
	// GetPaygateMerchantList(req model.PaygateMerchantListRequest) ([]model.PaygateMerchantNgResponse, int64, error)
	// CreatePaygateMerchant(body model.PaygateMerchantCreateBody) (*int64, error)
	// UpdatePaygateMerchant(id int64, body model.PaygateMerchantUpdateBody) error
	// DeletePaygateMerchant(id int64) error
	// // SETTING
	// GetPaygateSetting() (*model.PaygateSettingResponse, error)
	// CreatePaygateSetting(body model.PaygateSettingCreateBody) (*int64, error)
	// UpdatePaygateSetting(id int64, body model.PaygateSettingUpdateBody) error
	// DeletePaygateSetting(id int64) error
	// // AdminLog
	// GetPaygateAdminLogById(id int64) (*model.PaygateAdminLogResponse, error)
	// GetPaygateAdminLogList(req model.PaygateAdminLogListRequest) ([]model.PaygateAdminLogResponse, int64, error)
	// CreatePaygateAdminLog(body model.PaygateAdminLogCreateBody) (*int64, error)
	// UpdatePaygateAdminLog(id int64, body model.PaygateAdminLogUpdateBody) error
	// // SystemLog
	CreatePaygateSystemLog(body model.PaygateSystemLogCreateBody) (*int64, error)
	// UpdatePaygateSystemLog(id int64, body model.PaygateSystemLogUpdateBody) error

	// REF-PAYGATE
	GetRawOnepayPendingDepositOrderById(id int64) (*model.OnepayOrderResponse, error)
	GetPaygateAccountByProviderId(id int64) (*model.PaygateAccountResponse, error)
	// Onepay-REMOTE
	OnepayGetToken(setting model.PaygateAccountResponse) (*model.OnepayTokenCreateRemoteResponse, error)
	OnepayDeposit(token string, setting model.PaygateAccountResponse, req model.OnepayDepositCreateRemoteRequest) (*model.OnepayDepositCreateRemoteResponse, error)
	OnepayWithdraw(token string, setting model.PaygateAccountResponse, req model.OnepayWithdrawCreateRemoteRequest) (*model.OnepayWithdrawCreateRemoteResponse, error)
	// OnepayCheckBalance(setting model.PaygateAccountResponse) (*model.OnepayCheckBalanceRemoteResponse, error)
	// OnepayGetOrder(setting model.PaygateAccountResponse, orderNo string) (*model.OnepayGetOrderRemoteResponse, error)
	// OnepayCreateCustomer(setting model.PaygateAccountResponse, req model.OnepayCustomerCreateRemoteRequest) (*model.OnepayCustomerCreateRemoteResponse, error)
	// OnepayUpdateCustomer(setting model.PaygateAccountResponse, req model.OnepayCustomerUpdateRemoteRequest) (*model.OnepayCustomerUpdateRemoteResponse, error)
	// Onepay-Decrypt
	// OnepayDecryptRepayDespositPayload(setting model.PaygateAccountResponse, payload model.OnepayWebhookEncryptPayload) (*model.OnepayWebhookDepositResponse, error)
	// Onepay-DB
	CreateOnepayWebhook(body model.OnepayWebhookCreateBody) (*int64, error)
	GetDbOnepayOrderList(req model.OnepayOrderListRequest) ([]model.OnepayOrderResponse, int64, error)
	GetDbOnepayOrderById(id int64) (*model.OnepayOrderResponse, error)
	GetDbOnepayOrderByRefId(refId int64) (*model.OnepayOrderResponse, error)
	CreateDbOnepayOrder(body model.OnepayOrderCreateBody) (*int64, error)
	UpdateDbOnepayOrderError(id int64, remark string) error
	UpdateDbOnepayOrder(id int64, body model.OnepayOrderUpdateBody) error
	ApproveDbOnepayOrder(id int64, webhookStatus string) error
	UpdateOnepayOrderBankSucess(id int64, transId int64, actionBy int64) error
	// Token
	GetDbOnepayAccessToken() (*model.OnepayToken, error)
	CreateDbOnepayAccessToken(body model.OnepayTokenCreateBody) (*int64, error)
	// Customer
	// GetOnepayCustomerById(id int64) (*model.OnepayCustomerResponse, error)
	// GetOnepayCustomerByUserId(userId int64) (*model.OnepayCustomerResponse, error)
	// CheckOnepayCustomerByUserId(user model.UserBankDetailBody) (*model.OnepayCustomerResponse, error)
	// GetOnepayCustomerList(req model.OnepayCustomerListRequest) ([]model.OnepayCustomerResponse, int64, error)
	// CreateOnepayCustomer(body model.OnepayCustomerCreateBody) (*int64, error)
	// UpdateOnepayCustomer(id int64, body model.OnepayCustomerUpdateBody) error
	// DeleteOnepayCustomer(id int64) error
	// REF-USER
	GetUserById(id int64) (*model.UserResponse, error)
	// REF-MemberCode
	GetUserForGenMember(id int64) (*model.UserDetail, error)
	GetAgentInfo() (*model.AgentInfo, error)
	IncrementTotal() error
	UpdateMemberCode(userId int64, data model.UserUpdateMemberAndRef) error
	AgcRegister(data model.AgcRegister) error
	AmbRegister(data model.AmbRegister) error
	GetMemberById(id int64) (*model.Member, error)
	IsFirstDeposit(userId int64) bool
	UpdateGenMemberCodeToAffilate(userId int64) error
	CreateAffiliateMember(refBy int64, userId int64) error
	// CronjobDeleteOnepayWebhook() error
	// REF-RC
	GetRaceActionIdByActionKey(actionKey string) (*int64, error)
	CreateRaceCondition(body model.RaceActionCreateBody) (int64, error)
	// REF-BankTransaction-Deposit
	GetUserBankDetailById(id int64) (*model.UserBankDetailBody, error)
	GetDepositCurrentProcessingUserPromotion(userId int64) (*model.PromotionWebUserByUserIdResponse, error)
	InsertBankTransaction(data model.BankTransactionCreateBody) (*int64, error)
	IncreaseUserCredit(body model.UserTransactionCreateRequest) (*model.UserTransactionCreateResponse, error)
	UpdateDepositTransactionStatusFromAgent(transId int64, body model.UserTransactionCreateResponse) error
	UpdateAutoProcessTimer(timer string, id int64) error
	// ConfirmDeposit
	GetBankTransactionById(id int64) (*model.BankTransaction, error)
	CreateTransactionAction(data model.CreateBankTransactionActionBody) (*int64, error)
	ConfirmPendingDepositTransaction(id int64, body model.BankDepositTransactionConfirmBody) error
	RollbackTransactionAction(actionId int64) error
	// REF-BANK
	GetLastestBankAdminManualDepositTransaction(userId int64) (*model.BankTransaction, error)
	ShowUserTransaction(req model.UserTransactionShowUpdate) error
	UpdateTransactionStatusTransferingToSuccess(id int64, secondUsed string) error
	RollbackTransactionStatusTransferingToConfirmed(id int64) error
	CreateBankTransactionExternalDetail(body model.BankTransactionExternalDetailCreateRequest) (int64, error)
	UpdateUserTransactionStatus(body model.UpdateUserTransactionStatusRequest) error
	GetUserWithdrawCreditTransactionByRefId(transactionId int64, creditAmount float64) (*model.UserTransaction, error)
	// REF-SysLog
	CreateSystemLog(body model.SystemLogCreateBody) (*int64, error)
	// REF-WebConfiguration
	GetWebConfiguration() (*model.GetWebConfigurationBody, error)

	// [TIER]
	IncreaseUserTierDepositAmount(userId int64, amount float64) error
}

func (r repo) OnepayGetToken(setting model.PaygateAccountResponse) (*model.OnepayTokenCreateRemoteResponse, error) {

	if setting.ApiEndPoint == "" || setting.PartnerKey == "" {
		return nil, errors.New("INVALID_SETTING")
	}

	// use PartnerKey as MerchantAccesskey
	var req model.OnepayTokenCreateRemoteRequest
	req.MerchantAccesskey = setting.PartnerKey

	apiEndPoint := setting.ApiEndPoint
	// POST Endpoint: {{api_endpoint}}/api/v1/get-token
	epUrl := fmt.Sprintf("%s/api/v1/get-token", apiEndPoint)

	// SysLog
	if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
		Name:   "CreateGetOnepayToken.GetOnepayToken",
		Status: "CALL",
		JsonReq: helper.StructJson(map[string]interface{}{
			"epUrl":   epUrl,
			"setting": setting,
			"req":     req,
		}),
	}); err != nil {
		log.Println("CreatePaygateSystemLog.CreateSysLog", err)
	}

	client := &http.Client{}
	client.Timeout = 5 * time.Second

	jsonBody, _ := json.Marshal(req)
	payload := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest("POST", epUrl, payload)
	reqExternal.Header.Set("Content-Type", "application/json")
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}
	if response.StatusCode != 200 && response.StatusCode != 201 {
		log.Println(response.StatusCode)
		log.Println(string(responseData))
		var errMsg2 model.OnepayErrorRemoteResponse
		errJson2 := json.Unmarshal(responseData, &errMsg2)
		if errJson2 == nil {
			return nil, errors.New(errMsg2.Message)
		} else {
			log.Println("Unmarshal.errJson2 ------> ", errJson2)
		}
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	}

	var result model.OnepayTokenCreateRemoteResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("GetOnepayToken resp.Body ------> ", string(responseData))
		log.Println("Unmarshal.Err ------> ", errJson)
		var errMsg1 model.OnepayErrorRemoteResponse
		errJson2 := json.Unmarshal(responseData, &errMsg1)
		if errJson2 != nil {
			log.Println("GetOnepayToken resp.Body ------> ", string(responseData))
			log.Println("Unmarshal.Err ------> ", errJson)
		} else {
			return nil, errors.New(errMsg1.Message)
		}
		return nil, errors.New("INVALID_UNMARSHAL_JSON")
	}

	return &result, nil
}

func (r repo) OnepayDeposit(token string, setting model.PaygateAccountResponse, req model.OnepayDepositCreateRemoteRequest) (*model.OnepayDepositCreateRemoteResponse, error) {

	// ผมอยากเก็บ default เอาไว้กัน Adminตั้งค่าต่ำกว่า payment
	if req.Amount < model.ONEPAY_DEFMIN_DEPOSIT_AMOUNT || req.Amount > model.ONEPAY_DEFMAX_DEPOSIT_AMOUNT {
		log.Println("req.Amount", req.Amount)
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}
	if setting.PaymentDepositMinimum > 0 && req.Amount < setting.PaymentDepositMinimum {
		log.Println("req.Amount", req.Amount, "setting.PaymentDepositMinimum", setting.PaymentDepositMinimum)
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}
	if setting.PaymentDepositMaximum > 0 && req.Amount > setting.PaymentDepositMaximum {
		log.Println("req.Amount", req.Amount, "setting.PaymentDepositMaximum", setting.PaymentDepositMaximum)
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}
	if setting.ApiEndPoint == "" || setting.PartnerKey == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}

	apiEndPoint := setting.ApiEndPoint
	// POST Endpoint: {{api_endpoint}}/api/v1/deposit/create_qr_code
	epUrl := fmt.Sprintf("%s/api/v1/deposit/create_qr_code", apiEndPoint)
	log.Println("OnepayDeposit url ------> ", epUrl)

	// SysLog
	if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
		Name:   "CreateOnepayDeposit.OnepayDeposit",
		Status: "CALL",
		JsonReq: helper.StructJson(map[string]interface{}{
			"epUrl":   epUrl,
			"setting": setting,
			"req":     req,
		}),
	}); err != nil {
		log.Println("CreatePaygateSystemLog.CreateSysLog", err)
	}

	client := &http.Client{}
	client.Timeout = 5 * time.Second

	jsonBody, _ := json.Marshal(req)
	payload := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest("POST", epUrl, payload)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("Authorization", fmt.Sprintf("Bearer %s", token))
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}

	fmt.Println("OnepayDeposit.resp.Body", string(responseData))
	// OnepayDeposit.resp.Body {"message":["Invalid bank code"],"error":"Bad Request","statusCode":400}

	if response.StatusCode != 200 {
		log.Println(response.StatusCode)
		log.Println(string(responseData))
		var errMsg2 model.OnepayErrorRemoteResponse
		errJson2 := json.Unmarshal(responseData, &errMsg2)
		if errJson2 == nil {
			return nil, errors.New(errMsg2.Message)
		} else {
			log.Println("Unmarshal.errJson2 ------> ", errJson2)
		}
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	}

	var result model.OnepayDepositCreateRemoteResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("OnepayDeposit resp.Body ------> ", string(responseData))
		log.Println("Unmarshal.Err ------> ", errJson)
		// TRY-3-err2
		var errMsg3 model.OnepayErrorRemoteResponse
		errJson3 := json.Unmarshal(responseData, &errMsg3)
		if errJson3 == nil {
			return nil, errors.New(errMsg3.Message)
		} else {
			log.Println("Unmarshal.errJson3 ------> ", errJson3)
		}
		return nil, errors.New("INVALID_UNMARSHAL_JSON")
	}

	fmt.Println("OnepayDepositCreateRemoteResponse.result", result)
	// OnepayDeposit.remoteResp {"success":false,"message":"Cannot read properties of undefined (reading 'length')","data":{"qr_code_id":"","qr_code_url":"","created_at":"","qr_code":"","expired_at":"","amount":"","ref_name_th":"","ref_name_en":"","ref_user_id":""}}
	// OnepayDeposit.remoteResp {"success":false,"message":"Cannot read properties of null (reading 'webhook_url')","data":{"qr_code_id":"","qr_code_url":"","created_at":"","qr_code":"","expired_at":"","amount":"","ref_name_th":"","ref_name_en":"","ref_user_id":""}}

	if !result.Success {
		return nil, errors.New(result.Message)
	}

	return &result, nil
}

func (r repo) OnepayWithdraw(token string, setting model.PaygateAccountResponse, req model.OnepayWithdrawCreateRemoteRequest) (*model.OnepayWithdrawCreateRemoteResponse, error) {

	// ผมอยากเก็บ default เอาไว้กัน Adminตั้งค่าต่ำกว่า payment
	// if req.Amount < model.ONEPAY_DEFMIN_WITHDRAW_AMOUNT || req.Amount > model.ONEPAY_DEFMAX_WITHDRAW_AMOUNT {
	// 	log.Println("req.Amount", req.Amount)
	// 	return nil, errors.New("INVALID_AMOUNT_RANGE")
	// }
	// if req.Amount < setting.PaymentWithdrawMinimum || req.Amount > setting.PaymentWithdrawMaximum {
	// 	log.Println("req.Amount", req.Amount, " setting.PaymentWithdrawMinimum", setting.PaymentWithdrawMinimum, " setting.PaymentWithdrawMaximum", setting.PaymentWithdrawMaximum)
	// 	return nil, errors.New("INVALID_AMOUNT_RANGE")
	// }
	if setting.ApiEndPoint == "" || setting.PartnerKey == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}

	apiEndPoint := setting.ApiEndPoint
	// POST Endpoint: {{api_endpoint}}/api/v1/withdraw/create
	epUrl := fmt.Sprintf("%s/api/v1/withdraw/create", apiEndPoint)

	// SysLog
	if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
		Name:   "OnepayWithdraw.OnepayWithdraw",
		Status: "CALL",
		JsonReq: helper.StructJson(map[string]interface{}{
			"epUrl":   epUrl,
			"setting": setting,
			"req":     req,
		}),
	}); err != nil {
		log.Println("CreatePaygateSystemLog.CreateSysLog", err)
	}

	client := &http.Client{}
	client.Timeout = 5 * time.Second

	jsonBody, _ := json.Marshal(req)
	payload := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest("POST", epUrl, payload)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("Authorization", fmt.Sprintf("Bearer %s", token))
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}
	// fmt.Println("OnepayWithdraw.resp.Body", string(responseData))

	if response.StatusCode != 200 {
		log.Println(response.StatusCode)
		log.Println(string(responseData))
		var errMsg3 model.OnepayErrorRemoteResponse
		errJson3 := json.Unmarshal(responseData, &errMsg3)
		if errJson3 == nil {
			return nil, errors.New(errMsg3.Message)
		} else {
			log.Println("Unmarshal.errJson3 ------> ", errJson3)
		}
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	}

	//  {"code":"10002","message":"PARTNER_KEY Invalid","data":""}

	var result model.OnepayWithdrawCreateRemoteResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("OnepayWithdraw resp.Body ------> ", string(responseData))
		log.Println("Unmarshal.Err ------> ", errJson)
		var errMsg3 model.OnepayErrorRemoteResponse
		errJson3 := json.Unmarshal(responseData, &errMsg3)
		if errJson3 == nil {
			return nil, errors.New(errMsg3.Message)
		} else {
			log.Println("Unmarshal.errJson3 ------> ", errJson3)
		}
		return nil, errors.New("INVALID_UNMARSHAL_JSON")
	}

	fmt.Println("OnepayWithdrawCreateRemoteResponse.result", result)

	if !result.Success {
		return nil, errors.New(result.Message)
	}

	return &result, nil
}

func (r repo) CreateOnepayWebhook(body model.OnepayWebhookCreateBody) (*int64, error) {

	if err := r.db.Table("paygate_onepay_webhook").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) GetDbOnepayOrderList(req model.OnepayOrderListRequest) ([]model.OnepayOrderResponse, int64, error) {

	var list []model.OnepayOrderResponse
	var total int64

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("paygate_onepay_order as tb_order")
	count = count.Select("tb_order.id")
	if req.UserId != nil {
		count = count.Where("tb_order.user_id = ?", req.UserId)
	}
	if req.OrderTypeId != nil {
		count = count.Where("tb_order.order_type_id = ?", req.OrderTypeId)
	}
	if req.OrderNo != "" {
		count = count.Where("tb_order.order_no = ?", req.OrderNo)
	}
	if req.TransactionNo != "" {
		count = count.Where("tb_order.transaction_no = ?", req.TransactionNo)
	}
	if req.Amount != "" {
		count = count.Where("tb_order.amount = ?", req.Amount)
	}
	if req.Status != "" {
		count = count.Where("tb_order.transaction_status = ?", req.Status)
	}

	if err := count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount"
		selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
		selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.payment_page_url AS payment_page_url"
		selectedFields += ", tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
		selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

		query := r.db.Table("paygate_onepay_order as tb_order")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN paygate_order_type AS tb_type ON tb_order.order_type_id = tb_type.id")
		if req.UserId != nil {
			query = query.Where("tb_order.user_id = ?", req.UserId)
		}
		if req.OrderTypeId != nil {
			query = query.Where("tb_order.order_type_id = ?", req.OrderTypeId)
		}
		if req.OrderNo != "" {
			query = query.Where("tb_order.order_no = ?", req.OrderNo)
		}
		if req.TransactionNo != "" {
			query = query.Where("tb_order.transaction_no = ?", req.TransactionNo)
		}
		if req.Amount != "" {
			query = query.Where("tb_order.amount = ?", req.Amount)
		}
		if req.Status != "" {
			query = query.Where("tb_order.transaction_status = ?", req.Status)
		}

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetDbOnepayOrderById(id int64) (*model.OnepayOrderResponse, error) {

	var record model.OnepayOrderResponse

	selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount"
	selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
	selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.payment_page_url AS payment_page_url"
	selectedFields += ", tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
	selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

	if err := r.db.Table("paygate_onepay_order as tb_order").
		Select(selectedFields).
		Joins("LEFT JOIN paygate_order_type AS tb_type ON tb_order.order_type_id = tb_type.id").
		Where("tb_order.id = ?", id).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetDbOnepayOrderByRefId(refId int64) (*model.OnepayOrderResponse, error) {

	var record model.OnepayOrderResponse

	selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount"
	selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
	selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.payment_page_url AS payment_page_url"
	selectedFields += ", tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
	selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

	if err := r.db.Table("paygate_onepay_order as tb_order").
		Select(selectedFields).
		Joins("LEFT JOIN paygate_order_type AS tb_type ON tb_order.order_type_id = tb_type.id").
		Where("tb_order.ref_id = ?", refId).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) CreateDbOnepayOrder(body model.OnepayOrderCreateBody) (*int64, error) {

	if err := r.db.Table("paygate_onepay_order").Create(&body).Error; err != nil {
		return nil, err
	}

	// Update order_no if Empty in HPG{YYYYMMDD}{ID} //
	// orderNo := fmt.Sprintf("HPG%v%v", time.Now().Format("200601"), body.Id)
	// [20240209] Update order_no if Empty in {AGENT_NAME}{YYMM}{ID} //
	agentName := os.Getenv("PAYGATE_ORDER_PREFIX")
	if ginMode := os.Getenv("GIN_MODE"); ginMode == "debug" {
		agentName = "P9D" // DEVELOPMENT
	}
	if agentName == "" {
		agentName = "P9G" // ** MIN_LEN=10
	} else {
		agentName = strings.ToUpper(agentName)
	}
	orderNo := fmt.Sprintf("%v%v%v", agentName, time.Now().Format("0601"), body.Id)
	if body.OrderNo == "" {
		updateBody := make(map[string]interface{})
		updateBody["order_no"] = orderNo
		if err := r.db.Table("paygate_onepay_order").Where("id = ?", body.Id).Updates(updateBody).Error; err != nil {
			return nil, err
		}
	}
	return &body.Id, nil
}

func (r repo) UpdateDbOnepayOrderError(id int64, remark string) error {

	updateBody := map[string]interface{}{
		"transaction_status": "ERROR",
		"remark":             remark,
	}
	sql := r.db.Table("paygate_onepay_order").Where("id = ?", id).Where("transaction_status = ?", "PENDING")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdateDbOnepayOrder(id int64, body model.OnepayOrderUpdateBody) error {

	if err := r.db.Table("paygate_onepay_order").Where("id = ?", id).Updates(body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) ApproveDbOnepayOrder(id int64, webhookStatus string) error {

	updateBody := make(map[string]interface{})
	updateBody["transaction_status"] = webhookStatus
	updateBody["payment_at"] = time.Now()

	sql := r.db.Table("paygate_onepay_order").Where("id = ?", id).Where("transaction_status = ?", "WAIT_PAYMENT")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdateOnepayOrderBankSucess(id int64, transId int64, actionBy int64) error {

	updateBody := map[string]interface{}{
		"bank_transaction_id":     transId,
		"bank_transaction_status": "SUCCESS",
		"action_by":               actionBy,
	}

	sql := r.db.Table("paygate_onepay_order").Where("id = ?", id).Where("bank_transaction_status = ?", "PENDING")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetDbOnepayAccessToken() (*model.OnepayToken, error) {

	var result model.OnepayToken
	expireAt := time.Now().Add(time.Minute * 2)

	if err := r.db.Table("paygate_onepay_token").
		Where("expire_at > ?", expireAt).
		Take(&result).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (r repo) CreateDbOnepayAccessToken(body model.OnepayTokenCreateBody) (*int64, error) {

	if err := r.db.Table("paygate_onepay_token").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}
