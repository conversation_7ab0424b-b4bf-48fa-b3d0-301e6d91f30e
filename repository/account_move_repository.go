package repository

import (
	"bytes"
	"cybergame-api/helper"
	"cybergame-api/model"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"time"

	"gorm.io/gorm"
)

func NewAccountMoveRepository(db *gorm.DB) AccountMoveRepository {
	return &repo{db}
}

type AccountMoveRepository interface {
	ParseBodUTC(input string) (*time.Time, error)
	ParseEodUTC(input string) (*time.Time, error)
	// Options
	GetFromAccountOptions() ([]model.SelectOptions, error)
	GetToAccountOptions() ([]model.SelectOptions, error)
	GetTransactionStatusOptions() ([]model.SelectOptions, error)
	// Account
	GetAccountMoveAccountList(req model.AccountMoveAccountListRequest) ([]model.BankAccount, int64, error)
	// BankStatement
	GetExistsBankStatementList(req model.BankStatementExistsListRequest) ([]model.BankStatement, int64, error)
	// Transactions
	GetAccountMoveTransactionById(id int64) (*model.AccountMoveTransactionResponse, error)
	GetAccountMoveTransactionStatusCount(req model.AccountMoveTransactionListRequest) (*model.AccountMoveTransactionStatusCount, error)
	GetAccountMoveTransactionList(req model.AccountMoveTransactionListRequest) ([]model.AccountMoveTransactionResponse, int64, error)
	CreateAccountMoveTransaction(body model.AccountMoveTransactionCreateBody) (*int64, error)
	UpdateAccountMoveTransaction(id int64, body model.AccountMoveTransactionUpdateBody) error
	GetFastBankAccountById(id int64) (*model.BankAccount, error)
	GetAccountMoveTransactionDetail(id int64) (*model.AccountMoveTransactionResponse, error)
	// REF-ACCOUNTING
	GetBankAccountById(id int64) (*model.BankAccount, error)
	HasBankExternalStatements(externalId int64) error
	GetExternalAccountStatements(req model.ExternalStatementListRequest) (*model.ExternalStatementListWithPagination, error)
	GetBankAccountByExternalId(external_id int64) (*model.BankAccount, error)
	GetWebhookStatementByExternalId(external_id int64) (*model.BankStatement, error)
	CreateWebhookLog(data model.WebhookLogCreateBody) (*int64, error)
	GetBankStatementByExternalId(externalId int64) (*model.BankStatement, error)
	GetTransactionByStatementId(statementId int64) (model.BankTransaction, error)
	// FASTBANK
	TransferExternalSystemAccount(body model.ExternalAccountMoveCreateRequest) (*model.ExternalAccountMoveCreateResponse, error)
	GetExternalAccountStatementList(query model.ExternalAccountStatementListRequest) (*model.ExternalAccountStatementListResponse, error)
	GetExternalAccountLogList(query model.ExternalAccountLogListRequest) (*model.ExternalAccountLogListResponse, error)
	// BANK-LOG
	SuccessLog(name string, req interface{}, result interface{}) error
	ErrorLog(name string, req interface{}, result interface{}) error
	// [ADMIN_LOG]
	CreateAdminLog(body model.AdminLogCreateBody) (*int64, error)
}

func (r repo) GetFromAccountOptions() ([]model.SelectOptions, error) {

	options := []model.SelectOptions{}

	// SELECT //
	selectedFields := "id as id, account_name as label"
	sql := r.db.Table("bank_account").Select(selectedFields)
	// [2024-02-19] ยังไม่ได้เชื่อม fast bank ไม่แสดงผลการ์ดทุกที่
	sql = sql.Where("device_uid != ?", "").Where("pin_code != ?", "").Where("external_id IS NOT NULL")
	sql = sql.Where("deleted_at IS NULL")
	if err := sql.Scan(&options).Error; err != nil {
		return nil, err
	}
	return options, nil
}

func (r repo) GetToAccountOptions() ([]model.SelectOptions, error) {

	var list []model.BankAccount
	selectedFields := "accounts.id, accounts.bank_id, accounts.account_type_id, accounts.account_name, accounts.account_number, accounts.account_balance, accounts.device_uid, accounts.pin_code "
	selectedFields += ", accounts.last_conn_update_at, accounts.created_at, accounts.admin_updated_at as updated_at"
	selectedFields += ", banks.name as bank_name, banks.code as bank_code, banks.icon_url as bank_icon_url, banks.type_flag"
	selectedFields += ", account_types.name as account_type_name, account_types.limit_flag"
	query := r.db.Table("bank_account as accounts")
	query = query.Select(selectedFields)
	query = query.Joins("LEFT JOIN bank AS banks ON banks.id = accounts.bank_id")
	query = query.Joins("LEFT JOIN bank_account_type AS account_types ON account_types.id = accounts.account_type_id")

	if err := query.
		Where("deleted_at IS NULL").
		Scan(&list).
		Error; err != nil {
		return nil, err
	}

	options := []model.SelectOptions{}
	for _, item := range list {
		// ทำผ่าน API ไม่ยากให้ front ไล่แก้ option หลายจุด
		// (SCB) acountA 1234
		var setOptions model.SelectOptions
		bankAccountFormat := fmt.Sprintf("(%s) %s %s", item.BankCode, item.AccountName, item.AccountNumber)
		setOptions.Id = item.Id
		setOptions.Label = bankAccountFormat
		setOptions.Value = bankAccountFormat
		options = append(options, setOptions)
	}

	return options, nil

	// // SELECT //
	// selectedFields := "id as id, account_name as label"
	// var sql = r.db.Table("bank_account").Select(selectedFields)
	// if err := sql.
	// 	Where("deleted_at IS NULL").
	// 	Scan(&options).
	// 	Error; err != nil {
	// 	return nil, err
	// }
	// return options, nil
}

func (r repo) GetTransactionStatusOptions() ([]model.SelectOptions, error) {

	options := []model.SelectOptions{}

	// SELECT //
	selectedFields := "id as id, name as label"
	var sql = r.db.Table("account_move_transaction_status").Select(selectedFields)
	if err := sql.
		Scan(&options).
		Error; err != nil {
		return nil, err
	}
	return options, nil
}

func (r repo) GetAccountMoveAccountList(req model.AccountMoveAccountListRequest) ([]model.BankAccount, int64, error) {

	var list []model.BankAccount
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("bank_account as accounts")
	count = count.Select("accounts.id")
	count = count.Where("device_uid != ?", "")
	count = count.Where("pin_code != ?", "")
	count = count.Where("external_id IS NOT NULL")
	count = count.Where("deleted_at IS NULL")
	if err = count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "accounts.id, accounts.bank_id, accounts.account_type_id, accounts.account_name, accounts.account_number, accounts.account_balance, accounts.device_uid, accounts.pin_code "
		selectedFields += ", accounts.last_conn_update_at, accounts.created_at, accounts.admin_updated_at as updated_at"
		selectedFields += ", banks.name as bank_name, banks.code as bank_code, banks.icon_url as bank_icon_url, banks.type_flag"
		selectedFields += ", account_types.name as account_type_name, account_types.limit_flag"
		query := r.db.Table("bank_account as accounts")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN bank AS banks ON banks.id = accounts.bank_id")
		query = query.Joins("LEFT JOIN bank_account_type AS account_types ON account_types.id = accounts.account_type_id")
		query = query.Where("device_uid != ?", "")
		query = query.Where("pin_code != ?", "")
		query = query.Where("external_id IS NOT NULL")
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Where("deleted_at IS NULL").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetExistsBankStatementList(req model.BankStatementExistsListRequest) ([]model.BankStatement, int64, error) {

	var list []model.BankStatement
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("bank_statement as statements")
	count = count.Select("statements.id")
	count = count.Where("statements.account_id = ?", req.AccountId)
	count = count.Where("statements.external_id in ?", req.ExternalIds)

	if err = count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "statements.id, statements.account_id, statements.external_id, statements.amount, statements.detail, statements.from_bank_id, statements.from_account_number"
		selectedFields += ", statements.statement_type_id, statements.transfer_at, statements.statement_status_id, statements.created_at, statements.updated_at, statements.deleted_at"
		query := r.db.Table("bank_statement as statements")
		query = query.Select(selectedFields)
		// WHERE //
		query = query.Where("statements.account_id = ?", req.AccountId)
		query = query.Where("statements.external_id in ?", req.ExternalIds)
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetAccountMoveTransactionById(id int64) (*model.AccountMoveTransactionResponse, error) {

	var record model.AccountMoveTransactionResponse

	selectedFields := "trans.id as id, trans.admin_id as admin_id, trans.from_account_id as from_account_id, trans.to_account_id as to_account_id, trans.amount as amount"
	selectedFields += ", trans.status_id as status_id, trans.transfer_at as transfer_at, trans.remark as remark, trans.created_at as created_at"
	selectedFields += ", statuses.name as status_name"
	selectedFields += ", tb_admin.fullname as admin_name"
	selectedFields += ", from_accounts.account_name as from_account_name, from_accounts.account_number as from_account_number"
	selectedFields += ", to_accounts.bank_id as from_bank_id, from_bank.name as from_bank_name, from_bank.code as from_bank_code"
	selectedFields += ", to_accounts.account_name as to_account_name, to_accounts.account_number as to_account_number"
	selectedFields += ", to_accounts.bank_id as to_bank_id, to_bank.name as to_bank_name, to_bank.code as to_bank_code"
	if err := r.db.Table("account_move_transaction as trans").
		Select(selectedFields).
		Joins("LEFT JOIN account_move_transaction_status as statuses on statuses.id = trans.status_id").
		Joins("LEFT JOIN admin AS tb_admin ON tb_admin.id = trans.admin_id").
		Joins("LEFT JOIN bank_account as from_accounts on from_accounts.id = trans.from_account_id").
		Joins("LEFT JOIN bank as from_bank on from_bank.id = from_accounts.bank_id").
		Joins("LEFT JOIN bank_account as to_accounts on to_accounts.id = trans.to_account_id").
		Joins("LEFT JOIN bank as to_bank on to_bank.id = to_accounts.bank_id").
		Where("trans.id = ?", id).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetAccountMoveTransactionList(req model.AccountMoveTransactionListRequest) ([]model.AccountMoveTransactionResponse, int64, error) {

	var list []model.AccountMoveTransactionResponse
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("account_move_transaction as trans")
	count = count.Select("trans.id")
	if req.StatusId != nil {
		count = count.Where("trans.status_id = ?", req.StatusId)
	}
	if req.FromAccountId != nil {
		count = count.Where("trans.from_account_id = ?", req.FromAccountId)
	}
	if req.ToAccountId != nil {
		count = count.Where("trans.to_account_id = ?", req.ToAccountId)
	}
	if req.TransferDate != "" {
		fromTimeUtc, err := r.ParseBodUTC(req.TransferDate)
		if err != nil {
			return nil, total, err
		}
		count = count.Where("trans.transfer_at >= ?", fromTimeUtc)
		toTimeUtc, err := r.ParseEodUTC(req.TransferDate)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("trans.transfer_at <= ?", toTimeUtc)
	}
	if req.IsSuccess != nil {
		count = count.Where("trans.is_success = ?", req.IsSuccess)
	}

	if err = count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "trans.id as id, trans.admin_id as admin_id, trans.from_account_id as from_account_id, trans.to_account_id as to_account_id, trans.amount as amount"
		selectedFields += ", trans.status_id as status_id, trans.transfer_at as transfer_at, trans.remark as remark, trans.created_at as created_at"
		selectedFields += ", statuses.name as status_name"
		selectedFields += ", tb_admin.fullname as admin_name"
		selectedFields += ", from_accounts.account_name as from_account_name, from_accounts.account_number as from_account_number"
		selectedFields += ", to_accounts.bank_id as from_bank_id, from_bank.name as from_bank_name, from_bank.code as from_bank_code"
		selectedFields += ", to_accounts.account_name as to_account_name, to_accounts.account_number as to_account_number"
		selectedFields += ", to_accounts.bank_id as to_bank_id, to_bank.name as to_bank_name, to_bank.code as to_bank_code"
		query := r.db.Table("account_move_transaction as trans")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN account_move_transaction_status as statuses on statuses.id = trans.status_id")
		query = query.Joins("LEFT JOIN admin AS tb_admin ON tb_admin.id = trans.admin_id")
		query = query.Joins("LEFT JOIN bank_account as from_accounts on from_accounts.id = trans.from_account_id")
		query = query.Joins("LEFT JOIN bank as from_bank on from_bank.id = from_accounts.bank_id")
		query = query.Joins("LEFT JOIN bank_account as to_accounts on to_accounts.id = trans.to_account_id")
		query = query.Joins("LEFT JOIN bank as to_bank on to_bank.id = to_accounts.bank_id")
		// WHERE //
		if req.StatusId != nil {
			query = query.Where("trans.status_id = ?", req.StatusId)
		}
		if req.FromAccountId != nil {
			query = query.Where("trans.from_account_id = ?", req.FromAccountId)
		}
		if req.ToAccountId != nil {
			query = query.Where("trans.to_account_id = ?", req.ToAccountId)
		}
		if req.TransferDate != "" {
			fromTimeUtc, err := r.ParseBodUTC(req.TransferDate)
			if err != nil {
				return nil, total, err
			}
			query = query.Where("trans.transfer_at >= ?", fromTimeUtc)
			toTimeUtc, err := r.ParseEodUTC(req.TransferDate)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("trans.transfer_at <= ?", toTimeUtc)
		}
		if req.IsSuccess != nil {
			query = query.Where("trans.is_success = ?", req.IsSuccess)
		}
		// Sort by ANY //
		// req.SortCol = strings.TrimSpace(req.SortCol)
		// if req.SortCol != "" {
		// 	if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
		// 		req.SortAsc = "DESC"
		// 	} else {
		// 		req.SortAsc = "ASC"
		// 	}
		// 	query = query.Order(req.SortCol + " " + req.SortAsc)
		// }
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}

		query = query.Order("trans.transfer_at DESC")
		if err = query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}

	return list, total, nil
}

func (r repo) GetAccountMoveTransactionStatusCount(req model.AccountMoveTransactionListRequest) (*model.AccountMoveTransactionStatusCount, error) {

	var result model.AccountMoveTransactionStatusCount
	var err error

	// SELECT //
	selectedFields := "COUNT(trans.id) as total_count, SUM(CASE WHEN trans.is_success = 1 THEN 1 ELSE 0 END) as success_count, SUM(CASE WHEN trans.is_success = 0 THEN 1 ELSE 0 END) as fail_count"
	query := r.db.Table("account_move_transaction as trans")
	query = query.Select(selectedFields)
	// WHERE //
	if req.FromAccountId != nil {
		query = query.Where("trans.from_account_id = ?", req.FromAccountId)
	}
	if req.ToAccountId != nil {
		query = query.Where("trans.to_account_id = ?", req.ToAccountId)
	}
	if req.TransferDate != "" {
		fromTimeUtc, err := r.ParseBodUTC(req.TransferDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("trans.transfer_at >= ?", fromTimeUtc)
		toTimeUtc, err := r.ParseEodUTC(req.TransferDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("trans.transfer_at <= ?", toTimeUtc)
	}
	if req.IsSuccess != nil {
		query = query.Where("trans.is_success = ?", req.IsSuccess)
	}

	if err = query.
		Scan(&result).
		Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (r repo) CreateAccountMoveTransaction(body model.AccountMoveTransactionCreateBody) (*int64, error) {

	if err := r.db.Table("account_move_transaction").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) UpdateAccountMoveTransaction(id int64, body model.AccountMoveTransactionUpdateBody) error {

	if err := r.db.Table("account_move_transaction").Where("id = ?", id).Updates(&body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetFastBankAccountById(id int64) (*model.BankAccount, error) {

	var accounting model.BankAccount
	selectedFields := "accounts.id, accounts.bank_id, accounts.account_type_id, accounts.account_name, accounts.account_number, accounts.account_balance, accounts.device_uid, accounts.pin_code "
	selectedFields += ", accounts.last_conn_update_at, accounts.created_at, accounts.updated_at"
	selectedFields += ", banks.name as bank_name, banks.code as bank_code, banks.icon_url as bank_icon_url, banks.type_flag"
	selectedFields += ", account_types.name as account_type_name, account_types.limit_flag"
	selectedFields += ", accounts.connection_status_id, connection_status.name as connection_status_name, accounts.external_id as external_id"
	// selectedFields += ", account_status.id as account_status_id, account_status.name as account_status_name"
	if err := r.db.Table("bank_account as accounts").
		Select(selectedFields).
		Joins("LEFT JOIN bank AS banks ON banks.id = accounts.bank_id").
		Joins("LEFT JOIN bank_account_type AS account_types ON account_types.id = accounts.account_type_id").
		Joins("LEFT JOIN connection_status ON connection_status.id = accounts.connection_status_id").
		// Joins("LEFT JOIN account_status ON account_status.id = accounts.account_status_id").
		Where("accounts.id = ?", id).
		Where("accounts.deleted_at IS NULL").
		First(&accounting).
		Error; err != nil {
		return nil, err
	}
	return &accounting, nil
}

func (r repo) TransferExternalSystemAccount(body model.ExternalAccountMoveCreateRequest) (*model.ExternalAccountMoveCreateResponse, error) {

	client := &http.Client{}

	data, err := json.Marshal(body)
	if err != nil {
		log.Println(err)
		return nil, err
	}

	reqHttp, _ := http.NewRequest("POST", os.Getenv("ACCOUNTING_API_ENDPOINT")+"/api/v2/statement/transfer", bytes.NewBuffer(data))
	reqHttp.Header.Set("apiKey", os.Getenv("ACCOUNTING_API_KEY"))
	reqHttp.Header.Set("Content-Type", "application/json")
	response, err := client.Do(reqHttp)
	if err != nil {
		log.Println(err)
		return nil, err
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
	}

	// if response.StatusCode != 200 {

	// 	var resultErr model.ExternalAccountMoveCreateErrResponse

	// 	log.Println(response)
	// 	log.Println(string(responseData))
	// 	errJson := json.Unmarshal(responseData, &resultErr)
	// 	if errJson != nil {
	// 		return nil, errors.New("CANT_PARSE_RESPONSE_ERROR")
	// 	}
	// 	return nil, errors.New(resultErr.ErrorMessage)
	// }

	// error ใหม่ มี 2 แบบ
	if response.StatusCode != http.StatusOK {
		var resultErr struct {
			Status struct {
				Code        int    `json:"code"`
				Description string `json:"description"`
			} `json:"status"`
		}

		errJson := json.Unmarshal(responseData, &resultErr)
		if errJson == nil && resultErr.Status.Code == 500 && resultErr.Status.Description != "" {
			return nil, errors.New(resultErr.Status.Description)
		}

		var resultErr2 model.ExternalAccountMoveCreateErrResponse
		log.Println(response)
		log.Println(string(responseData))
		errJson2 := json.Unmarshal(responseData, &resultErr2)
		if errJson2 != nil {
			log.Println("Error parsing fallback error response:", errJson2)
			return nil, errors.New("CANT_PARSE_RESPONSE_ERROR")
		}
		return nil, errors.New(resultErr2.ErrorMessage)
	}

	var result model.ExternalAccountMoveCreateResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		return nil, errors.New("CANT_PARSE_RESPONSE_DATA")
	}
	return &result, nil
}

func (r repo) GetExternalAccountStatementList(query model.ExternalAccountStatementListRequest) (*model.ExternalAccountStatementListResponse, error) {

	client := &http.Client{}

	// curl -X GET "https://api.fastbankapi.com/api/v2/statement?accountNo=**********&page=0&size=10&txnCode=all" -H "accept: */*" -H "apiKey: aaaaaa.bbbbbb"
	queryString := fmt.Sprintf("?txnCode=all&accountNo=%s&page=%d&size=%d", query.AccountNumber, query.Page, query.Size)
	url := os.Getenv("ACCOUNTING_API_ENDPOINT") + "/api/v2/statement" + queryString

	reqExternal, _ := http.NewRequest("GET", url, nil)
	reqExternal.Header.Set("apiKey", os.Getenv("ACCOUNTING_API_KEY"))
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("INVALID_RESPONSE_DATA")
	}
	if response.StatusCode != 200 {
		log.Println(response)
		log.Println(string(responseData))
		return nil, errors.New("INVALID_RESPONSE_CODE")
	}

	var result model.ExternalAccountStatementListResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		return nil, errors.New("CANT_PARSE_RESPONSE_DATA")
	}
	return &result, nil
}

func (r repo) GetExternalAccountLogList(query model.ExternalAccountLogListRequest) (*model.ExternalAccountLogListResponse, error) {

	client := &http.Client{}

	// curl -X GET "https://api.fastbankapi.com/api/v2/site/bankAccount/logs?accountNo=**********&page=1&size=10" -H "accept: */*" -H "apiKey: aaaaaa.bbbbbb"
	queryString := fmt.Sprintf("?accountNo=%s&page=%d&size=%d", query.AccountNumber, query.Page, query.Size)
	url := os.Getenv("ACCOUNTING_API_ENDPOINT") + "/api/v2/site/bankAccount/logs" + queryString

	reqExternal, _ := http.NewRequest("GET", url, nil)
	reqExternal.Header.Set("apiKey", os.Getenv("ACCOUNTING_API_KEY"))
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("INVALID_RESPONSE_DATA")
	}
	if response.StatusCode != 200 {
		log.Println(response)
		log.Println(string(responseData))
		return nil, errors.New("INVALID_RESPONSE_CODE")
	}

	var result model.ExternalAccountLogListResponse
	log.Println(string(responseData))
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		return nil, errors.New("CANT_PARSE_RESPONSE_DATA")
	}
	return &result, nil
}

func (r repo) GetExternalAccountStatements(req model.ExternalStatementListRequest) (*model.ExternalStatementListWithPagination, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, err
	}

	// 2024-01-24
	// 24-01-2024

	client := &http.Client{}
	// https://api.fastbankapi.com/api/v2/statement?accountNo=aaaaa&page=0&size=10&txnCode=all
	// curl -X GET "https://api.fastbankapi.com/api/v2/statement?accountNo=hhhhhh&page=0&size=10&txnCode=all" -H "accept: */*" -H "apiKey: aaaaaaaa.bbbbbbbbbbb"
	queryString := fmt.Sprintf("&page=%d&size=%d&txnCode=all", req.Page, req.Limit)
	if req.OfDate != "" {
		queryString += fmt.Sprintf("&date=%s", req.OfDate)
	}
	if req.OfTime != "" {
		queryString += fmt.Sprintf("&time=%s", req.OfTime)
	}
	fullPath := os.Getenv("ACCOUNTING_API_ENDPOINT") + "/api/v2/statement?accountNo=" + req.AccountNumber + queryString

	reqExternal, _ := http.NewRequest("GET", fullPath, nil)
	reqExternal.Header.Set("apiKey", os.Getenv("ACCOUNTING_API_KEY"))
	response, err := client.Do(reqExternal)

	if err != nil {
		log.Print(err.Error())
		return nil, (errors.New("error from external API"))
	}

	if response.StatusCode != 200 {
		log.Println(fullPath, response)
		return nil, (errors.New("error from external API"))
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
	}
	var externalList model.ExternalStatementListWithPagination
	errJson := json.Unmarshal(responseData, &externalList)
	if errJson != nil {
		log.Println(errJson)
		return nil, (errors.New("error from JSON response"))
	}
	return &externalList, nil
}

func (r repo) CreateAdminLog(body model.AdminLogCreateBody) (*int64, error) {

	if err := r.db.Table("admin_log").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) GetAccountMoveTransactionDetail(id int64) (*model.AccountMoveTransactionResponse, error) {

	var record model.AccountMoveTransactionResponse
	var err error

	// SELECT //
	selectedFields := "trans.id as id, trans.admin_id as admin_id, trans.from_account_id as from_account_id, trans.to_account_id as to_account_id, trans.amount as amount"
	selectedFields += ", trans.status_id as status_id, trans.transfer_at as transfer_at, trans.remark as remark, trans.created_at as created_at"
	selectedFields += ", statuses.name as status_name"
	selectedFields += ", tb_admin.fullname as admin_name"
	selectedFields += ", from_accounts.account_name as from_account_name, from_accounts.account_number as from_account_number"
	selectedFields += ", to_accounts.bank_id as from_bank_id, from_bank.name as from_bank_name, from_bank.code as from_bank_code"
	selectedFields += ", to_accounts.account_name as to_account_name, to_accounts.account_number as to_account_number"
	selectedFields += ", to_accounts.bank_id as to_bank_id, to_bank.name as to_bank_name, to_bank.code as to_bank_code"
	query := r.db.Table("account_move_transaction as trans")
	query = query.Select(selectedFields)
	query = query.Joins("LEFT JOIN account_move_transaction_status as statuses on statuses.id = trans.status_id")
	query = query.Joins("LEFT JOIN admin AS tb_admin ON tb_admin.id = trans.admin_id")
	query = query.Joins("LEFT JOIN bank_account as from_accounts on from_accounts.id = trans.from_account_id")
	query = query.Joins("LEFT JOIN bank as from_bank on from_bank.id = from_accounts.bank_id")
	query = query.Joins("LEFT JOIN bank_account as to_accounts on to_accounts.id = trans.to_account_id")
	query = query.Joins("LEFT JOIN bank as to_bank on to_bank.id = to_accounts.bank_id")
	query = query.Where("trans.id = ?", id)

	query = query.Order("trans.transfer_at DESC")
	if err = query.
		Take(&record).
		Error; err != nil {
		return nil, err
	}

	return &record, nil
}
