package repository

import (
	"cybergame-api/model"

	"gorm.io/gorm"
)

func NewSyslogRepository(db *gorm.DB) SyslogRepository {
	return &repo{db}
}

type SyslogRepository interface {
	// [SYSLOG]
	CreateSystemLog(body model.SystemLogCreateBody) (*int64, error)
	SetSystemLogSuccess(body model.SystemLogUpdateBody) error
	SetSystemLogError(body model.SystemLogUpdateBody) error
}

func (r repo) CreateSystemLog(body model.SystemLogCreateBody) (*int64, error) {

	if err := r.db.Table("sys_user_log").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) SetSystemLogSuccess(body model.SystemLogUpdateBody) error {

	updateBody := map[string]interface{}{
		"status":       "SUCCESS",
		"json_input":   body.JsonInput,
		"json_reponse": body.JsonReponse,
	}

	if err := r.db.Table("sys_user_log").Where("id = ?", body.Id).Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) SetSystemLogError(body model.SystemLogUpdateBody) error {

	updateBody := map[string]interface{}{
		"status":       "ERROR",
		"json_input":   body.JsonInput,
		"json_reponse": body.JsonReponse,
	}

	if err := r.db.Table("sys_user_log").Where("id = ?", body.Id).Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}
