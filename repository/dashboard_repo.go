package repository

import (
	"cybergame-api/model"
	"fmt"
	"log"

	"gorm.io/gorm"
)

func NewDashboardRepository(db *gorm.DB) DashboardRepository {
	return &repo{db}
}

type DashboardRepository interface {
	// Bank Total Deposit-Withdraw Report
	GetTotalDepositWithdrawList(req model.ReportBankTotalDepositWithdrawRequest) ([]model.ReportBankTotalDepositWithdrawItem, error)
	// ใช้ของสรุปภาพรวมแทน GetAdminCorpBankSummary() (*model.AdminCorpBankSummaryResponse, error)
	GetReportSummaryListTotal(req model.ReportSummaryRequest) (*model.GetReportSummaryListTotal, error)
	GetAdminCorpPendingTransactionList() ([]model.BankPendingRecordResponse, error)
	// TEST
	GetBankPendingRecordById(id int64) (*model.BankPendingRecordResponse, error)
	GetBankPendingRecordByRefKey(refKey string) (*model.BankPendingRecordResponse, error)
	CreateBankPendingRecord(body model.BankPendingRecordCreateBody) (*int64, error)
	UpdateBankPendingRecord(id int64, body model.BankPendingRecordUpdateBody) error
	DeleteMonthOlderBankPendingRecord() error
	GetRawUserById(id int64) (*model.UserRawResponse, error)
	GetRawBankRecordById(id int64) (*model.RawBankRecord, error)
	// WEBSOCKET
	SendWebSocketUpdateAdminCorpDashboard(id int64) error
	// REF
	GetAdminById(id int64) (*model.Admin, error)
	// REF-Report
	GetDateFromDateType(req model.DateTypeResponse) (*model.DateTypeResponse, error)
	GetUserReportDaily(req model.ReportSummaryRequest) (*model.ReportSummaryResponse, error)
	GetActivitySummaryReportDaily(req model.ReportSummaryRequest) (*model.ReportSummaryResponse, error)
	GetUserTodayPlaylogReportDaily(req model.ReportSummaryRequest) (*model.ReportSummaryResponse, error)
	GetUserCreditReportDaily(req model.ReportSummaryRequest) (*model.ReportSummaryResponse, error)
	GetUserIncomeReportDaily(req model.ReportSummaryRequest) (*model.ReportSummaryResponse, error)
	GetUserAccountingReportDaily(req model.ReportSummaryRequest) (*model.ReportSummaryResponse, error)
}

func (r repo) GetTotalDepositWithdrawList(req model.ReportBankTotalDepositWithdrawRequest) ([]model.ReportBankTotalDepositWithdrawItem, error) {

	var list []model.ReportBankTotalDepositWithdrawItem

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return nil, err
	}

	// 50,15: func (r repo) GetSummaryReportAccountList() ([]model.FastBankAccountResponse, int64, error) {

	// GetBankAccountWithSumWithdrawCreditAmountByAccount
	// GetBankAccountWithSumDepostCreditAmountByAccount
	// sumBankAccountTransaction.TotalWithdraw = withdraw.SumAmount
	// sumBankAccountTransaction.TotalDeposit = deposit.SumAmount

	// to do เพิ่ม เอามาจาก move transaction
	// moveTransWithdraw, _ := repo.GetSumMoveTransactionFromAccountToDay()
	// for i, oldList := range responseList {
	// 	for _, move := range moveTransWithdraw {
	// 		if oldList.Id == move.FromBankId {
	// 			responseList[i].TotalWithdraw = oldList.TotalWithdraw + move.TotalAmount
	// 		}
	// 	}
	// }

	// moveTransDeposit, _ := repo.GetSumMoveTransactionToAccountToDay()
	// for i, oldList := range responseList {
	// 	for _, move := range moveTransDeposit {
	// 		if oldList.Id == move.ToAccountId {
	// 			responseList[i].TotalDeposit = oldList.TotalDeposit + move.TotalAmount
	// 		}
	// 	}
	// }

	accountIds := make(map[int64]int64)
	var rawDepBankTrans []model.ReportBankTotalDepositRaw
	selectedFields := "tb_record.to_account_id AS to_account_id, SUM(tb_record.credit_amount) AS total_deposit"
	selectedFields += ", COUNT(tb_record.id) AS deposit_count"
	query := r.db.Table("bank_transaction AS tb_record")
	query = query.Select(selectedFields)
	query = query.Where("tb_record.transaction_type_id = ?", model.TRANSACTION_TYPE_DEPOSIT)
	query = query.Where("tb_record.transaction_status_id IN (?,?)", model.TRANS_STATUS_WITHDRAW_SUCCESS, model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED)
	// WHERE //
	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return nil, err
		}
		query = query.Where("tb_record.transfer_at >= ?", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return nil, err
		}
		query = query.Where("tb_record.transfer_at <= ?", endDateAtBkk)
	}
	query = query.Where("tb_record.to_account_id IS NOT NULL AND tb_record.to_account_id != 0")
	query = query.Where("tb_record.deleted_at IS NULL")
	query = query.Group("tb_record.to_account_id")
	if err := query.Scan(&rawDepBankTrans).Error; err != nil {
		return nil, err
	}
	for _, raw := range rawDepBankTrans {
		accountIds[raw.ToAccountId] = raw.ToAccountId
	}

	var rawWithBankTrans []model.ReportBankTotalWithdrawRaw
	selectedFields2 := "tb_record.from_account_id AS from_account_id, SUM(tb_record.credit_amount) AS total_withdraw"
	selectedFields2 += ", COUNT(tb_record.id) AS withdraw_count"
	query2 := r.db.Table("bank_transaction AS tb_record")
	query2 = query2.Select(selectedFields2)
	query2 = query2.Where("tb_record.transaction_type_id = ?", model.TRANSACTION_TYPE_WITHDRAW)
	query2 = query2.Where("tb_record.transaction_status_id IN (?,?)", model.TRANS_STATUS_WITHDRAW_SUCCESS, model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED)
	// WHERE //
	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return nil, err
		}
		query2 = query2.Where("tb_record.transfer_at >= ?", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return nil, err
		}
		query2 = query2.Where("tb_record.transfer_at <= ?", endDateAtBkk)
	}
	query2 = query2.Where("tb_record.from_account_id IS NOT NULL AND tb_record.from_account_id != 0")
	query2 = query2.Where("tb_record.deleted_at IS NULL")
	query2 = query2.Group("tb_record.from_account_id")
	if err := query2.Scan(&rawWithBankTrans).Error; err != nil {
		return nil, err
	}
	for _, raw := range rawWithBankTrans {
		accountIds[raw.FromAccountId] = raw.FromAccountId
	}

	// Get AccountMoveTransaction-From
	var moveFromRecord []model.GetSumMoveTransactionFromAccountToDayResponse
	selectedFields4 := "SUM(tb_record.amount) AS total_amount, tb_record.from_account_id AS from_bank_id"
	selectedFields4 += ", COUNT(tb_record.id) AS total_count"
	query4 := r.db.Table("account_move_transaction AS tb_record").
		Select(selectedFields4).
		Where("tb_record.is_success = ?", 1)

	// WHERE //
	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return nil, err
		}
		query4 = query4.Where("tb_record.transfer_at >= ?", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return nil, err
		}
		query4 = query4.Where("tb_record.transfer_at <= ?", endDateAtBkk)
	}
	query4 = query4.Group("tb_record.from_account_id")
	if err := query4.Scan(&moveFromRecord).
		Error; err != nil {
		return nil, err
	}

	// Get AccountMoveTransaction-To
	var moveToRecord []model.GetSumMoveTransactionToAccountToDayResponse
	selectedFields5 := "SUM(tb_record.amount) AS total_amount, tb_record.to_account_id AS to_account_id"
	selectedFields5 += ", COUNT(tb_record.id) AS total_count"
	query5 := r.db.Table("account_move_transaction AS tb_record").
		Select(selectedFields5).
		Where("tb_record.is_success = ?", 1)

	// WHERE //
	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return nil, err
		}
		query5 = query5.Where("tb_record.transfer_at >= ?", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return nil, err
		}
		query5 = query5.Where("tb_record.transfer_at <= ?", endDateAtBkk)
	}
	query5 = query5.Group("tb_record.to_account_id")
	if err := query5.Scan(&moveToRecord).
		Error; err != nil {
		return nil, err
	}

	// GET Account + Bank Info
	accountMap := make(map[int64]model.FastBankAccountResponse)
	if len(accountIds) > 0 {
		var accountList []model.FastBankAccountResponse
		accountIdsSlice := make([]int64, 0, len(accountIds))
		for id := range accountIds {
			accountIdsSlice = append(accountIdsSlice, id)
		}
		selectedFields3 := "tb_account.id AS id, tb_account.account_number AS account_number, tb_account.account_name AS account_name"
		selectedFields3 += ", tb_bank.id AS bank_id, tb_bank.code AS bank_code, tb_bank.name AS bank_name"
		query3 := r.db.Table("bank_account AS tb_account")
		query3 = query3.Select(selectedFields3)
		query3 = query3.Joins("LEFT JOIN bank AS tb_bank ON tb_bank.id = tb_account.bank_id")
		query3 = query3.Where("tb_account.id IN (?)", accountIdsSlice)
		query3 = query3.Where("tb_account.deleted_at IS NULL")
		if err := query3.Scan(&accountList).Error; err != nil {
			return nil, err
		}
		for _, account := range accountList {
			accountMap[account.Id] = account
		}
	}

	// And PaymentGateway Account
	paymentGatewayItem, err := r.GetTotalDepositWithdrawGatewayList(req)
	if err != nil {
		log.Println("GetPaymentGatewayAccountList error:", err)
	}

	// Prepare response list
	// name = {bank_code} {account_name} {account_number}
	for _, accountId := range accountIds {
		if bankInfo, ok := accountMap[accountId]; ok {
			response := model.ReportBankTotalDepositWithdrawItem{
				Name:          fmt.Sprintf("(%s) %s %s", bankInfo.BankCode, bankInfo.AccountName, bankInfo.AccountNumber),
				TotalDeposit:  0,
				DepositCount:  0,
				TotalWithdraw: 0,
				WithdrawCount: 0,
			}
			// Sum up deposit and withdraw amounts
			for _, raw := range rawDepBankTrans {
				if raw.ToAccountId == accountId {
					response.TotalDeposit += raw.TotalDeposit
					response.DepositCount += raw.DepositCount
				}
			}
			for _, raw := range moveToRecord {
				if raw.ToAccountId == accountId {
					response.TotalDeposit += raw.TotalAmount
					response.DepositCount += raw.TotalCount
				}
			}
			for _, raw := range rawWithBankTrans {
				if raw.FromAccountId == accountId {
					response.TotalWithdraw += raw.TotalWithdraw
					response.WithdrawCount += raw.WithdrawCount
				}
			}
			for _, raw := range moveFromRecord {
				if raw.FromBankId == accountId {
					response.TotalWithdraw += raw.TotalAmount
					response.WithdrawCount += raw.TotalCount
				}
			}
			// Append to the response list
			list = append(list, response)
		}
	}
	// Append PaymentGateway Account on bottom.
	for _, gatewayItem := range paymentGatewayItem {
		response := model.ReportBankTotalDepositWithdrawItem{
			Name:          gatewayItem.Name,
			TotalDeposit:  gatewayItem.TotalDeposit,
			DepositCount:  gatewayItem.DepositCount,
			TotalWithdraw: gatewayItem.TotalWithdraw,
			WithdrawCount: gatewayItem.WithdrawCount,
		}
		// Append to the response list
		list = append(list, response)
	}

	return list, nil
}

func (r repo) GetTotalDepositWithdrawGatewayList(req model.ReportBankTotalDepositWithdrawRequest) ([]model.ReportBankTotalDepositWithdrawItem, error) {

	var list []model.ReportBankTotalDepositWithdrawItem

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return nil, err
	}

	// GET Account + Bank Info
	var accountList []model.PaygateAccount

	selectedFields3 := "tb_account.id AS id, tb_account.provider_id AS provider_id, tb_account.name AS name, tb_account.display_name AS display_name"
	query3 := r.db.Table("paygate_account AS tb_account")
	query3 = query3.Select(selectedFields3)
	query3 = query3.Where("tb_account.deleted_at IS NULL")
	if err := query3.Scan(&accountList).Error; err != nil {
		return nil, err
	}

	// Prepare response list
	// name = {name} {display_name}
	for _, account := range accountList {
		if record, err := r.GetTotalDepositWithdrawGatewayByMerchant(account.ProviderId, *dateType); err == nil {
			// ถ้าไม่มียอดเงินฝากถอน ก็ไม่ต้องแสดง
			if record.TotalDeposit == 0 && record.TotalWithdraw == 0 {
				continue
			}
			response := model.ReportBankTotalDepositWithdrawItem{
				Name:          fmt.Sprintf("(%s) %s", account.Name, account.DisplayName),
				TotalDeposit:  record.TotalDeposit,
				DepositCount:  record.DepositCount,
				TotalWithdraw: record.TotalWithdraw,
				WithdrawCount: record.WithdrawCount,
			}
			// Append to the response list
			list = append(list, response)
		}
	}
	return list, nil
}

func (r repo) GetTotalDepositWithdrawGatewayByMerchant(paymentId int64, dateType model.DateTypeResponse) (*model.ReportPaymentTotalDepositRaw, error) {

	var record model.ReportPaymentTotalDepositRaw

	// dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
	// 	DateType: req.DateType,
	// 	DateFrom: req.FromDate,
	// 	DateTo:   req.ToDate,
	// })
	// if err != nil {
	// 	return nil, err
	// }

	tableName := ""
	switch paymentId {
	case 1:
		tableName = "paygate_heng_order"
	case 2:
		tableName = "paygate_luckyth_order"
	case 3:
		tableName = "paygate_papaya_order"
	case 4:
		tableName = "paygate_payonex_order"
	case 5:
		tableName = "paygate_jbpay_order"
	case 6:
		tableName = "paygate_pompay_order"
	case 7:
		tableName = "paygate_paymentco_order"
	case 8:
		tableName = "paygate_zappay_order"
	case 9:
		tableName = "paygate_onepay_order"
	case 10:
		tableName = "paygate_flashpay_order"
	case 11:
		tableName = "paygate_bizpay_order"
	case 12:
		tableName = "paygate_sugarpay_order"
	case 13:
		tableName = "paygate_zmanpay_order"
	case 14:
		tableName = "paygate_postmanpay_order"
	case 15:
		tableName = "paygate_mazepay_order"
	case 16:
		tableName = "paygate_meepay_order"
	default:
		tableName = ""
	}
	if tableName == "" {
		return nil, fmt.Errorf("INVALID_PAYMENT_ID")
	}

	// *withdraw no transfer amount
	selectedFields := "? AS merchant_id, SUM(CASE WHEN tb_record.order_type_id = ? THEN tb_record.transfer_amount END) AS total_deposit"
	selectedFields += ", SUM(CASE WHEN tb_record.order_type_id = ? THEN tb_record.amount END) AS total_withdraw"
	selectedFields += ", COUNT(CASE WHEN tb_record.order_type_id = ? THEN 1 END) AS deposit_count"
	selectedFields += ", COUNT(CASE WHEN tb_record.order_type_id = ? THEN 1 END) AS withdraw_count"
	query := r.db.Table("`" + tableName + "` AS tb_record")
	query = query.Select(selectedFields, paymentId, model.PAYGATE_ORDER_TYPE_DEPOSIT, model.PAYGATE_ORDER_TYPE_WITHDRAW, model.PAYGATE_ORDER_TYPE_DEPOSIT, model.PAYGATE_ORDER_TYPE_WITHDRAW)
	// Support all transaction status
	query = query.Where(r.db.Where("tb_record.transaction_status = 'PAID'").Or("tb_record.transaction_status = 'SUCCESS'").Or("tb_record.bank_transaction_status = 'SUCCESS'"))
	// WHERE //
	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return nil, err
		}
		query = query.Where("tb_record.payment_at >= ?", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return nil, err
		}
		query = query.Where("tb_record.payment_at <= ?", endDateAtBkk)
	}
	if err := query.Scan(&record).Error; err != nil {
		return nil, err
	}

	return &record, nil
}

func (r repo) GetAdminCorpPendingTransactionList() ([]model.BankPendingRecordResponse, error) {

	var list []model.BankPendingRecordResponse
	confMaxRow := 5
	// เอารายการที่ค้างทั้งหมด ถ้ามีเวลา socket_at แล้วคือ หลุดออกจากรายการนี้ไปแล้ว

	selectedFields := "*"

	query := r.db.Table("bank_pending_record AS tb_record")
	query = query.Select(selectedFields)
	query = query.Where("tb_record.socket_at IS NULL")
	// Sort by ANY //
	query = query.Order("id ASC")
	query = query.Limit(confMaxRow)
	if err := query.Scan(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (r *repo) GetBankPendingRecordById(id int64) (*model.BankPendingRecordResponse, error) {

	var record model.BankPendingRecordResponse

	if err := r.db.Table("bank_pending_record").Where("id = ?", id).First(&record).Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r *repo) GetBankPendingRecordByRefKey(refKey string) (*model.BankPendingRecordResponse, error) {

	var record model.BankPendingRecordResponse

	if err := r.db.Table("bank_pending_record").Where("ref_key = ?", refKey).First(&record).Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r *repo) CreateBankPendingRecord(body model.BankPendingRecordCreateBody) (*int64, error) {

	if err := r.db.Table("bank_pending_record").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r *repo) UpdateBankPendingRecord(id int64, body model.BankPendingRecordUpdateBody) error {

	if err := r.db.Table("bank_pending_record").Where("id = ?", id).Updates(body).Error; err != nil {
		return err
	}
	return nil
}

func (r *repo) DeleteMonthOlderBankPendingRecord() error {

	keepDays := 30

	if err := r.db.Table("bank_pending_record").Where("DATEDIFF(NOW(), created_at) > ?", keepDays).Delete(&model.BankPendingRecordResponse{}).Error; err != nil {
		return err
	}
	return nil
}

func (r *repo) GetRawUserById(id int64) (*model.UserRawResponse, error) {

	var user model.UserRawResponse

	if err := r.db.Table("user").Where("id = ?", id).First(&user).Error; err != nil {
		return nil, err
	}
	return &user, nil
}

func (r *repo) GetRawBankRecordById(id int64) (*model.RawBankRecord, error) {

	var record model.RawBankRecord

	selectedFields := "tb_record.id AS id, tb_record.user_id AS user_id, tb_record.transaction_type_id AS transaction_type_id, tb_record.transaction_status_id AS transaction_status_id"
	selectedFields += ", tb_from_bank.code AS from_bank_code, tb_record.from_account_number AS from_account_number"
	selectedFields += ", tb_to_bank.code AS to_bank_code, tb_record.to_account_number AS to_account_number"
	selectedFields += ", tb_record.credit_amount AS credit_amount, tb_record.transfer_at AS transfer_at"
	selectedFields += ", tb_record.created_by_admin_id AS created_by_admin_id, tb_admin_creator.fullname AS created_by_admin_fullname"
	sql := r.db.Table("bank_transaction AS tb_record").Select(selectedFields)
	sql = sql.Where("tb_record.id = ?", id)
	sql = sql.Joins("LEFT JOIN admin AS tb_admin_creator ON tb_admin_creator.id = tb_record.created_by_admin_id")
	sql = sql.Joins("LEFT JOIN bank AS tb_from_bank ON tb_from_bank.id = tb_record.from_bank_id")
	sql = sql.Joins("LEFT JOIN bank AS tb_to_bank ON tb_to_bank.id = tb_record.to_bank_id")
	if err := sql.First(&record).Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r *repo) SendWebSocketUpdateAdminCorpDashboard(id int64) error {

	var record model.BankPendingRecordResponse

	if err := r.db.Table("bank_pending_record").Where("id = ?", id).Take(&record).Error; err != nil {
		return err
	}

	if record.Id == id {
		var payloads []model.WebSocketUpdateAdminDashboardPayload
		payload := model.WebSocketUpdateAdminDashboardPayload{
			Id:            record.Id,
			Status:        record.UpdateStatus,
			AdminFullname: record.UpdateAdminFullname,
			TransferAt:    record.TransferAt,
		}
		payloads = append(payloads, payload)
		// Send WebSocket message
		if err := r.WebSocketUpdateAdminCorpDashboard(payloads); err != nil {
			return err
		}
	} else {
		// fmt.Println(id, helper.StructJson(record))
		log.Println("SendWebSocketUpdateAdminCorpDashboard: record not found or invalid data")
	}

	return nil
}
