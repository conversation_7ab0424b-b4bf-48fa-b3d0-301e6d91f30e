package repository

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"
)

var promotionReturnTurnCuttypeOptions *model.SelectOptionsCache
var promotionReturnTurnSetting *model.PromotionReturnTurnSettingResponse

func NewPromotionReturnTurnRepository(db *gorm.DB) PromotionReturnTurnRepository {
	return &repo{db}
}

type PromotionReturnTurnRepository interface {
	GetDb() *gorm.DB

	// CutType promotion_return_turn_cut_type
	GetReturnTurnCutTypeOptions() ([]model.SelectOptions, error)
	GetReturnTurnCutTypeById(id int64) (*model.SelectOptions, error)
	// Setting promotion_return_turn_setting
	GetReturnTurnSetting() (*model.PromotionReturnTurnSettingResponse, error)
	CreateReturnTurnSetting(body model.PromotionReturnTurnSettingCreateBody) (*int64, error)
	UpdateReturnTurnSetting(id int64, body model.PromotionReturnTurnSettingUpdateBody) error
	// tLog promotion_return_turn
	GetReturnTurnHistoryUserList(req model.PromotionReturnTurnHistoryUserListRequest) ([]model.PromotionReturnTurnHistoryUserResponse, int64, error)
	GetReturnTurnHistoryAllUserList(req model.PromotionReturnTurnHistoryUserListRequest) ([]model.PromotionReturnTurnHistoryUserResponse, int64, error)
	GetTotalUserReturnTurnList(req model.PromotionReturnTurnHistoryUserListRequest) (map[int64]model.PromotionReturnTurnHistoryUserTotalResponse, error)
	GetReturnTurnHistoryUserSummary(req model.PromotionReturnTurnHistoryUserListRequest) (*model.PromotionReturnTurnHistoryUserSummaryResponse, error)
	GetReturnTurnHistoryLogList(req model.PromotionReturnTurnHistoryListRequest) ([]model.PromotionReturnTurnHistoryReponse, int64, error)
	GetCurrentReturnTurnTransactionList(userId int64) ([]model.PromotionReturnTurnTransaction, error)
	GetReturnTurnTransactionList(req model.PromotionReturnTurnTransactionListRequest) ([]model.PromotionReturnTurnTransaction, int64, error)
	GetReturnTurnUncalcTransactionList(req model.PromotionReturnTurnTransactionUncalcListRequest) ([]model.PromotionReturnTurnTransaction, int64, error)
	CreateReturnTurnTransaction(body model.PromotionReturnTurnTransactionCreateBody) (*int64, error)
	GetReturnTurnTransactionListByDailyKeyList(bulkBody map[string]model.PromotionReturnTurnTransactionCreateBody) ([]model.PromotionReturnTurnTransactionDailyKey, int64, error)
	CreateReturnTurnTransactionBulk(bulkBody map[string]model.PromotionReturnTurnTransactionCreateBody) error
	UpdateCalcReturnTurnTransaction(id int64, body model.PromotionReturnTurnTransactionCalcBody) error
	UpdateTakeReturnTurnTransaction(id int64, body model.PromotionReturnTurnTransactionUpdateBody) error
	UpdateExpriedReturnTurnTransaction(id int64, body model.PromotionReturnTurnTransactionUpdateBody) error
	// Other Logic
	CheckReturnTurnSystemHasDailyCuttypeInWeek(statementDate string) (int64, error)
	// REF-Play_log
	GetDailyTotalUserPaylogList(statementDate string) ([]model.PlaylogTotalAmount, error)
	GetWeeklyTotalUserPaylogList(statementDate string) ([]model.PlaylogTotalAmount, error)
	CheckDailyPlayLog(statementDate string) (*model.CronPlayLogCheckReponse, error)
	// REF-USER
	GetUserMemberInfoById(id int64) (*model.UserResponse, error)
	IncreaseUserCredit(body model.UserTransactionCreateRequest) (*model.UserTransactionCreateResponse, error)
	DecreaseUserCredit(body model.UserTransactionCreateRequest) (*model.UserTransactionCreateResponse, error)
	// REF-CRON_ACTION
	GetCronActionById(id int64) (*model.CronAction, error)
	GetCronActionByActionKey(actionKey string) (*model.CronAction, error)
	CreateCronAction(body model.CronActionCreateBody) (int64, error)
	SetSuccessCronAction(id int64, remark string) error
	SetFailCronAction(id int64, remark string) error
	// REF-CRON_STATUS
	UpdateAgcCronCalcStatus(name string, statementDate string, status string) error
	// REF:TURNOVER
	CreateTurnoverUserStatement(body model.TurnoverUserStatementCreateBody) (*int64, error)
	// REF-USER_INCOME
	CreateUserIncomeLog(body model.UserIncomeLogCreateBody) (*int64, error)
	GetMarketingConfigByKey(configKey string, defaultVal string) (*model.MarketingConfig, error)
	GetUserIncomeLogById(id int64) (*model.UserIncomeLogResponse, error)
	ConfirmUserIncomeLog(body model.UserIncomeLogConfirmBody) error

	// WebSocket(reqAlert model.WebScoket) error
	// GetFirstActivityDaily() (*model.GetActivityDailyResponse, error)
	// GetActivityLuckyWheelSetting() (*model.ActivityLuckyWheelSettingResponse, error)

}

func (r repo) GetReturnTurnCutTypeById(id int64) (*model.SelectOptions, error) {

	var record model.SelectOptions

	options, err := r.GetReturnTurnCutTypeOptions()
	if err != nil {
		return nil, err
	}

	for _, v := range options {
		if v.Id == id {
			record = v
			break
		}
	}
	return &record, nil
}

func (r repo) GetReturnTurnCutTypeOptions() ([]model.SelectOptions, error) {

	var options []model.SelectOptions

	if promotionReturnTurnCuttypeOptions != nil {
		return promotionReturnTurnCuttypeOptions.Options, nil
	}

	selectedFields := "id AS id, id AS value, name AS label"
	if err := r.db.Table("promotion_return_turn_cut_type").
		Select(selectedFields).
		Scan(&options).
		Error; err != nil {
		return nil, err
	}

	promotionReturnTurnCuttypeOptions = &model.SelectOptionsCache{
		Options:        options,
		CacheExpiredAt: time.Now().Add(60 * time.Minute),
	}
	return options, nil
}

func (r repo) GetReturnTurnSetting() (*model.PromotionReturnTurnSettingResponse, error) {

	var record model.PromotionReturnTurnSettingResponse

	if promotionReturnTurnSetting != nil && time.Now().Before(promotionReturnTurnSetting.CacheExpiredAt) {
		// fmt.Println("Get Cache:", helper.StructJson(promotionReturnTurnSetting))
		return promotionReturnTurnSetting, nil
	}

	selectedFields := "id, return_type_id, cut_type_id, min_turn_price, max_return_price"
	selectedFields += ", settings.return_sport_percent, settings.return_casino_percent, settings.return_game_percent, settings.return_lottery_percent, settings.return_p2p_percent, settings.return_financial_percent"
	selectedFields += ", credit_expire_days, calc_on_sport, calc_on_casino, calc_on_game, calc_on_lottery, calc_on_p2p, calc_on_financial"
	selectedFields += ", detail, is_enabled, created_at, updated_at"
	if err := r.db.Table("promotion_return_turn_setting as settings").
		Select(selectedFields).
		Take(&record).
		Error; err != nil {
		return nil, err
	}

	// Set Cache
	promotionReturnTurnSetting = &record
	promotionReturnTurnSetting.CacheExpiredAt = time.Now().Add(60 * time.Minute)
	// fmt.Println("Set Cache:", helper.StructJson(promotionReturnTurnSetting))

	return &record, nil
}

func (r repo) CreateReturnTurnSetting(body model.PromotionReturnTurnSettingCreateBody) (*int64, error) {

	if err := r.db.Table("promotion_return_turn_setting").Create(&body).Error; err != nil {
		return nil, err
	}

	// Clear Cache = GetNewData Later
	promotionReturnTurnSetting = nil

	return &body.Id, nil
}

func (r repo) UpdateReturnTurnSetting(id int64, body model.PromotionReturnTurnSettingUpdateBody) error {

	tx := r.db.Begin()

	// เปิดได้ อย่างเดียว จะคืนยอดเสีย หรือ จะคืนยอดเทิร์น
	// [20241028] ยกเลิกการเปิดได้อย่างเดียว จะเป็นการเปิดทั้ง ยอดเสีย และ ยอดเทิร์น พร้อมกันได้
	// if body.IsEnabled != nil && *body.IsEnabled {
	// 	// เปิด ยอดเสีย จะไปปิด ยอดเทิร์น
	// 	updateTurnBody := map[string]interface{}{"is_enabled": false}
	// 	if err := tx.Table("promotion_return_setting").Where("is_enabled = ?", true).Updates(updateTurnBody).Error; err != nil {
	// 		tx.Rollback()
	// 		return err
	// 	}
	// }

	if err := tx.Table("promotion_return_turn_setting").Where("id = ?", id).Updates(body).Error; err != nil {
		tx.Rollback()
		return err
	}
	tx.Commit()

	// Clear Cache = GetNewData Later
	promotionReturnTurnSetting = nil

	return nil
}

func (r repo) GetReturnTurnHistoryAllUserList(req model.PromotionReturnTurnHistoryUserListRequest) ([]model.PromotionReturnTurnHistoryUserResponse, int64, error) {

	var list []model.PromotionReturnTurnHistoryUserResponse
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("user as users")
	count = count.Select("users.id")
	if req.Search != "" {
		search_like := fmt.Sprintf("%%%s%%", req.Search)
		count = count.Where(r.db.Where("users.member_code LIKE ?", search_like).Or("users.username LIKE ?", search_like).Or("users.fullname LIKE ?", search_like))
	}
	if err = count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "users.id as id, users.member_code, users.username, users.fullname"
		query := r.db.Table("user as users")
		query = query.Select(selectedFields)
		if req.Search != "" {
			search_like := fmt.Sprintf("%%%s%%", req.Search)
			query = query.Where(r.db.Where("users.member_code LIKE ?", search_like).Or("users.username LIKE ?", search_like).Or("users.fullname LIKE ?", search_like))
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetTotalUserReturnTurnList(req model.PromotionReturnTurnHistoryUserListRequest) (map[int64]model.PromotionReturnTurnHistoryUserTotalResponse, error) {

	var list []model.PromotionReturnTurnHistoryUserTotalResponse
	var err error

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return nil, err
	}

	// SELECT //
	selectedFields := "tb_log.user_id as user_id, SUM(tb_log.total_turn_amount) as total_turn_amount"
	selectedFields += ", SUM(tb_log.taken_price) as total_taken_price"
	query := r.db.Table("promotion_return_turn as tb_log")
	query = query.Select(selectedFields)
	if req.UserIds != nil && len(req.UserIds) > 0 {
		query = query.Where("tb_log.user_id IN ?", req.UserIds)
	}
	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return nil, err
		}
		query = query.Where("tb_log.created_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return nil, err
		}
		query = query.Where("tb_log.created_at <=  ?", endDateAtBkk)
	}
	// NO_STATUS_NEED query = query.Where("tb_log.status_id != ?", model.PROMOTION_RETURN_STATUS_PENDING)
	if err = query.
		Group("tb_log.user_id").
		Scan(&list).
		Error; err != nil {
		return nil, err
	}

	result := make(map[int64]model.PromotionReturnTurnHistoryUserTotalResponse)
	for _, v := range list {
		result[v.UserId] = v
	}
	return result, nil
}

func (r repo) GetReturnTurnHistoryUserList(req model.PromotionReturnTurnHistoryUserListRequest) ([]model.PromotionReturnTurnHistoryUserResponse, int64, error) {

	var list []model.PromotionReturnTurnHistoryUserResponse
	var total int64
	var err error

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return nil, total, err
	}

	// SELECT //
	selectedFields := "tb_log.user_id as id, SUM(tb_log.total_turn_amount) as total_turn_amount"
	selectedFields += ", SUM(tb_log.taken_price) as total_taken_price"
	query := r.db.Table("promotion_return_turn as tb_log")
	query = query.Select(selectedFields)
	query = query.Joins("INNER JOIN user as tb_user ON tb_user.id = tb_log.user_id")
	if req.UserIds != nil && len(req.UserIds) > 0 {
		query = query.Where("tb_log.user_id IN ?", req.UserIds)
	}
	if req.Search != "" {
		// seach member code from table user
		search_like := fmt.Sprintf("%%%s%%", req.Search)
		query = query.Where(r.db.Where("tb_user.member_code LIKE ?", search_like).Or("tb_user.username LIKE ?", search_like).Or("tb_user.fullname LIKE ?", search_like))
	}
	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return nil, total, err
		}
		query = query.Where("tb_log.created_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return nil, total, err
		}
		query = query.Where("tb_log.created_at <=  ?", endDateAtBkk)
	}
	// NO_STATUS_NEED query = query.Where("tb_log.status_id != ?", model.PROMOTION_RETURN_STATUS_PENDING)
	if err = query.
		Group("tb_log.user_id").
		Scan(&list).
		Error; err != nil {
		return nil, total, err
	}

	// Append user member_code/name
	userIds := make(map[int64]int64)
	for _, v := range list {
		userIds[v.Id] = v.Id
	}
	if len(userIds) > 0 {
		var userList []model.User
		var userMap = make(map[int64]model.User)
		selectedFields2 := "id, member_code, username, fullname"
		query2 := r.db.Table("user")
		query2 = query2.Select(selectedFields2)
		query2 = query2.Where("id IN (?)", helper.MapIdsToInt64Array(userIds))
		if err = query2.
			Scan(&userList).
			Error; err != nil {
			return nil, total, err
		}
		for _, v := range userList {
			userMap[v.Id] = v
		}
		for i, v := range list {
			if user, ok := userMap[v.Id]; ok {
				list[i].MemberCode = *user.MemberCode
				list[i].Username = *user.Username
				list[i].Fullname = user.Fullname
				// Display as Negative Value
				if v.TotalTurnAmount > 0 {
					list[i].TotalTurnAmount = v.TotalTurnAmount * -1
				}
			}
		}
	}

	return list, total, nil
}

func (r repo) GetReturnTurnHistoryUserSummary(req model.PromotionReturnTurnHistoryUserListRequest) (*model.PromotionReturnTurnHistoryUserSummaryResponse, error) {

	var result model.PromotionReturnTurnHistoryUserSummaryResponse
	var err error

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return &result, err
	}

	// SELECT //
	selectedFields := "SUM(tb_log.total_turn_amount) as total_turn_amount, SUM(tb_log.taken_price) as total_taken_price"
	query := r.db.Table("promotion_return_turn as tb_log")
	query = query.Select(selectedFields)
	query = query.Joins("INNER JOIN user as tb_user ON tb_user.id = tb_log.user_id")
	// NO_STATUS_NEED query = query.Where("tb_log.status_id != ?", model.PROMOTION_RETURN_STATUS_PENDING)
	if req.Search != "" {
		// seach member code from table user
		search_like := fmt.Sprintf("%%%s%%", req.Search)
		query = query.Where(r.db.Where("tb_user.member_code LIKE ?", search_like).Or("tb_user.username LIKE ?", search_like).Or("tb_user.fullname LIKE ?", search_like))
	}
	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return &result, err
		}
		query = query.Where("tb_log.created_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return &result, err
		}
		query = query.Where("tb_log.created_at <=  ?", endDateAtBkk)
	}
	if err = query.
		Scan(&result).
		Error; err != nil {
		return nil, err
	}

	result.DateType = dateType.DateType
	result.FromDate = dateType.DateFrom
	result.ToDate = dateType.DateTo
	// Display as Negative Value
	if result.TotalTurnAmount > 0 {
		result.TotalTurnAmount = result.TotalTurnAmount * -1
	}
	return &result, nil
}

func (r repo) GetReturnTurnHistoryLogList(req model.PromotionReturnTurnHistoryListRequest) ([]model.PromotionReturnTurnHistoryReponse, int64, error) {

	var list []model.PromotionReturnTurnHistoryReponse
	var total int64

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("promotion_return_turn as logs")
	count = count.Select("logs.id")
	count = count.Where("logs.status_id != ?", model.PROMOTION_RETURN_STATUS_PENDING)
	if req.UserId != nil {
		count = count.Where("logs.user_id = ?", req.UserId)
	}
	if req.FromDate != "" {
		fromTimeUtc, err := r.ParseBodUTC(req.FromDate)
		if err != nil {
			return nil, total, err
		}
		count = count.Where("logs.of_date >= ?", fromTimeUtc.Format("2006-01-02"))
	}
	if req.ToDate != "" {
		toTimeUtc, err := r.ParseEodUTC(req.ToDate)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("logs.of_date <= ?", toTimeUtc.Format("2006-01-02"))
	}
	if req.StatusId != nil {
		count = count.Where("logs.status_id = ?", req.StatusId)
	}
	if err := count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "logs.id as id, logs.user_id, logs.total_turn_amount, logs.total_turn_sport, logs.total_turn_casino, logs.total_turn_game, logs.total_turn_lottery, logs.total_turn_p2p, logs.total_turn_financial"
		selectedFields += ", logs.of_date, logs.return_price, logs.game_detail"
		selectedFields += ", logs.return_sport_percent, logs.return_casino_percent, logs.return_game_percent, logs.return_lottery_percent, logs.return_p2p_percent, logs.return_financial_percent"
		// if logs.credit_expire_days = 0 return null else return DATE_ADD(logs.of_date, INTERVAL logs.credit_expire_days DAY)
		// selectedFields += ", DATE_ADD(logs.of_date, INTERVAL logs.credit_expire_days DAY) as credit_expire_at"
		selectedFields += ", CASE WHEN logs.credit_expire_days = 0 THEN NULL ELSE DATE_ADD(logs.of_date, INTERVAL logs.credit_expire_days DAY) END as credit_expire_at"
		selectedFields += ", logs.cut_type_id, types.name as cut_type_name"
		selectedFields += ", logs.status_id, status.name as log_status"
		query := r.db.Table("promotion_return_turn as logs")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN promotion_return_turn_cut_type as types ON types.id = logs.cut_type_id")
		query = query.Joins("LEFT JOIN promotion_return_turn_status as status ON status.id = logs.status_id")
		query = query.Where("logs.status_id != ?", model.PROMOTION_RETURN_STATUS_PENDING)
		if req.UserId != nil {
			query = query.Where("logs.user_id = ?", req.UserId)
		}
		if req.FromDate != "" {
			fromTimeUtc, err := r.ParseBodUTC(req.FromDate)
			if err != nil {
				return nil, total, err
			}
			query = query.Where("logs.of_date >= ?", fromTimeUtc.Format("2006-01-02"))
		}
		if req.ToDate != "" {
			toTimeUtc, err := r.ParseEodUTC(req.ToDate)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("logs.of_date <= ?", toTimeUtc.Format("2006-01-02"))
		}
		if req.StatusId != nil {
			query = query.Where("logs.status_id = ?", req.StatusId)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			if req.SortCol == "of_date" {
				query = query.Order("STR_TO_DATE(of_date, '%Y-%m-%d') " + req.SortAsc)
			} else {
				query = query.Order(req.SortCol + " " + req.SortAsc)
			}
		}

		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetCurrentReturnTurnTransactionList(userId int64) ([]model.PromotionReturnTurnTransaction, error) {

	var list []model.PromotionReturnTurnTransaction

	selectedFields := "logs.id, logs.user_id, logs.daily_key, logs.of_date, logs.total_turn_amount, logs.return_type_id, logs.cut_type_id, logs.min_turn_price, logs.max_return_price"
	selectedFields += ", logs.return_sport_percent, logs.return_casino_percent, logs.return_game_percent, logs.return_lottery_percent, logs.return_p2p_percent, logs.return_financial_percent"
	selectedFields += ", logs.credit_expire_days, logs.return_price, logs.calc_at, logs.take_at, logs.taken_price, logs.created_at, logs.updated_at"
	selectedFields += ", logs.total_turn_sport as total_turn_sport, logs.total_turn_casino as total_turn_casino, logs.total_turn_game as total_turn_game"
	selectedFields += ", logs.total_turn_lottery as total_turn_lottery, logs.total_turn_p2p as total_turn_p2p, logs.total_turn_financial as total_turn_financial"
	selectedFields += ", status.id as status_id, status.name as status_name"
	if err := r.db.Table("promotion_return_turn as logs").
		Select(selectedFields).
		Joins("LEFT JOIN promotion_return_turn_status as status ON status.id = logs.status_id").
		Where("logs.user_id = ?", userId).
		Where("logs.status_id = ?", model.PROMOTION_RETURN_STATUS_READY).
		Scan(&list).
		Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (r repo) GetReturnTurnTransactionList(req model.PromotionReturnTurnTransactionListRequest) ([]model.PromotionReturnTurnTransaction, int64, error) {

	var list []model.PromotionReturnTurnTransaction
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("promotion_return_turn as logs")
	count = count.Select("logs.id")
	count = count.Where("logs.user_id = ?", req.UserId)
	if req.StatusId != nil {
		count = count.Where("logs.status_id = ?", req.StatusId)
	}
	if err = count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "logs.id, logs.user_id, logs.daily_key, logs.of_date, logs.total_turn_amount, logs.return_type_id, logs.cut_type_id, logs.min_turn_price, logs.max_return_price"
		selectedFields += ", logs.return_sport_percent, logs.return_casino_percent, logs.return_game_percent, logs.return_lottery_percent, logs.return_p2p_percent, logs.return_financial_percent"
		selectedFields += ", logs.credit_expire_days, logs.return_price, logs.calc_at, logs.take_at, logs.taken_price, logs.created_at, logs.updated_at"
		selectedFields += ", total_turn_sport, total_turn_casino, total_turn_game, total_turn_lottery, total_turn_p2p, total_turn_financial, game_detail"
		selectedFields += ", status.id as status_id, status.name as status_name"
		query := r.db.Table("promotion_return_turn as logs")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN promotion_return_turn_status as status ON status.id = logs.status_id")
		query = query.Where("logs.user_id = ?", req.UserId)
		if req.StatusId != nil {
			query = query.Where("logs.status_id = ?", req.StatusId)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}

		if err = query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetReturnTurnUncalcTransactionList(req model.PromotionReturnTurnTransactionUncalcListRequest) ([]model.PromotionReturnTurnTransaction, int64, error) {

	var list []model.PromotionReturnTurnTransaction
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("promotion_return_turn as logs")
	count = count.Select("logs.id")
	count = count.Where("logs.status_id = ?", model.PROMOTION_RETURN_STATUS_PENDING)
	count = count.Where("logs.calc_at IS NULL")
	if err = count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "id, user_id, total_turn_amount, return_type_id, cut_type_id, min_turn_price, max_return_price, credit_expire_days, return_price, calc_at, take_at, taken_price, created_at, updated_at"
		selectedFields += ", logs.return_sport_percent, logs.return_casino_percent, logs.return_game_percent, logs.return_lottery_percent, logs.return_p2p_percent, logs.return_financial_percent"
		selectedFields += ", total_turn_sport, total_turn_casino, total_turn_game, total_turn_lottery, total_turn_p2p, total_turn_financial, game_detail"
		query := r.db.Table("promotion_return_turn as logs")
		query = query.Select(selectedFields)
		query = query.Where("logs.status_id = ?", model.PROMOTION_RETURN_STATUS_PENDING)
		query = query.Where("logs.calc_at IS NULL")
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetReturnTurnTransactionListByDailyKeyList(bulkBody map[string]model.PromotionReturnTurnTransactionCreateBody) ([]model.PromotionReturnTurnTransactionDailyKey, int64, error) {

	var list []model.PromotionReturnTurnTransactionDailyKey
	var total int64
	var err error

	dailyKeys := make([]string, 0)
	if len(bulkBody) > 0 {
		for k := range bulkBody {
			dailyKeys = append(dailyKeys, k)
		}
	}

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("promotion_return_turn as logs")
	count = count.Select("logs.id")
	count = count.Where("logs.daily_key IN ?", dailyKeys)
	if err = count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "id, user_id, daily_key, total_turn_amount, created_at"
		query := r.db.Table("promotion_return_turn as logs")
		query = query.Select(selectedFields)
		query = query.Where("logs.daily_key IN ?", dailyKeys)
		if err = query.
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) CreateReturnTurnTransaction(body model.PromotionReturnTurnTransactionCreateBody) (*int64, error) {

	if err := r.db.Table("promotion_return_turn").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) CreateReturnTurnTransactionBulk(bulkMap map[string]model.PromotionReturnTurnTransactionCreateBody) error {

	bulkBody := make([]model.PromotionReturnTurnTransactionCreateBody, 0)
	for _, v := range bulkMap {
		bulkBody = append(bulkBody, v)
	}

	if err := r.db.Table("promotion_return_turn").Create(&bulkBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdateCalcReturnTurnTransaction(id int64, body model.PromotionReturnTurnTransactionCalcBody) error {

	if err := r.db.Table("promotion_return_turn").Where("id = ?", id).Where("calc_at IS NULL").Where("status_id = ?", model.PROMOTION_RETURN_STATUS_PENDING).Updates(body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdateTakeReturnTurnTransaction(id int64, body model.PromotionReturnTurnTransactionUpdateBody) error {

	if err := r.db.Table("promotion_return_turn").Where("id = ?", id).Where("take_at IS NULL").Where("status_id = ?", model.PROMOTION_RETURN_STATUS_READY).Updates(body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdateExpriedReturnTurnTransaction(id int64, body model.PromotionReturnTurnTransactionUpdateBody) error {

	if err := r.db.Table("promotion_return_turn").Where("id = ?", id).Where("take_at IS NULL").Where("status_id = ?", model.PROMOTION_RETURN_STATUS_READY).Updates(body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) CheckReturnTurnSystemHasDailyCuttypeInWeek(statementDate string) (int64, error) {

	var total int64

	ofDate, err := time.Parse("2006-01-02", statementDate)
	if err != nil {
		return -2, err
	}

	// Get date from Monday to Sunday that has this day in week
	var days []string
	monday := ofDate.AddDate(0, 0, -int(ofDate.Weekday()))
	if ofDate.Weekday() == time.Sunday {
		// Sunday is ZERO, Check ofDate from SAT-SUN(today) SAME as today as SAT(BEFORE SUN)
		monday = ofDate.AddDate(0, 0, -6)
	}
	for i := 0; i < 7; i++ {
		days = append(days, monday.AddDate(0, 0, i).Format("2006-01-02"))
	}

	if err := r.db.Table("promotion_return_turn as tb_log").
		Select("COUNT(tb_log.id)").
		Where("tb_log.of_date IN ?", days).
		// ANY TYPE Where("tb_log.cut_type_id = ?", model.PROMOTION_RETURN_CUT_TYPE_DAILY).
		Count(&total).
		Error; err != nil {
		return -1, err
	}
	return total, nil
}

func (r repo) WithdrawCommissionFromPromotionReturnTurn(userId int64, amount float64) error {

	tx := r.db.Begin()

	// var commission float64
	// if err := tx.Table("user_affiliate").
	// 	Select("commission_current").
	// 	Where("user_id = ?", userId).
	// 	Take(&commission).
	// 	Error; err != nil {
	// 	tx.Rollback()
	// 	return err
	// }

	commissionTransfer := map[string]interface{}{}
	commissionTransfer["detail"] = "Return turn success"
	commissionTransfer["status"] = true
	commissionTransfer["commission"] = amount
	commissionTransfer["user_id"] = userId
	if err := tx.Table("commission_transfer").
		Create(&commissionTransfer).
		Error; err != nil {
		tx.Rollback()
		return err
	}

	// use affiliate ?
	// if err := tx.Table("user_affiliate").
	// 	Where("user_id = ?", userId).
	// 	Update("commission_current", 0.00).
	// 	Error; err != nil {
	// 	tx.Rollback()
	// 	return err
	// }

	var credit float64

	if err := tx.Table("user").
		Select("credit").
		Where("id = ?", userId).
		Take(&credit).
		Error; err != nil {
		tx.Rollback()
		return err
	}

	user := map[string]interface{}{}
	user["credit"] = gorm.Expr("credit + ?", amount)

	if err := tx.Table("user").
		Where("id = ?", userId).
		Updates(&user).
		Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}
	return nil
}
