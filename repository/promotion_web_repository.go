package repository

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"os"
	"strings"
	"time"

	"gorm.io/gorm"
)

func NewPromotionWebRepository(db *gorm.DB) PromotionWebRepository {
	return &repo{db}
}

type PromotionWebRepository interface {
	GetDb() *gorm.DB
	//promotion option
	GetPromotionWebType() ([]model.PromotionWebTypeResponse, error)
	GetPromotionWebStatus() ([]model.PromotionWebStatusResponse, error)
	GetPromotionWebBonusCondition() ([]model.PromotionWebBonusConditionResponse, error)
	GetPromotionWebBonusType() ([]model.PromotionWebBonusTypeResponse, error)
	GetPromotionWebTurnoverType() ([]model.PromotionWebTurnoverTypeResponse, error)
	GetpromotionWebDateType() ([]model.GetpromotionWebDateTypeResponse, error)
	GetPromotionWebUserStatus() ([]model.GetpromotionWebDateTypeResponse, error)
	GetPromotionWebUserStatusOptions() ([]model.SelectOptions, error)

	//promotion internal and checker
	GetProcessingPromotionWebUserByUserId(userId int64) (*model.PromotionWebUserByUserIdResponse, error)
	GetProcessingPromotionWebUserByPromotionWebUserId(PromotionWebUserId int64) (*model.PromotionWebUserByUserIdResponse, error)
	GetPromotionWebInternalList(req model.PromotionWebGetInternalListRequest) ([]model.PromotionWebGetInternalListResponse, int64, error)
	CheckCollectedUserPromotionWebByUserId(userId int64) ([]model.PromotionWebUserByUserIdResponse, error)
	GetUserPromotionWebInternalListById(promotionWebId []int64, userId int64) ([]model.PromotionWebUserGetListResponse, error)
	GetUserFirstDepositForPromotion(req model.GetUserFirstDepositForPromotion) (*model.GetBankTransactionFirstTimeDepositResponse, error)
	GetDepositMinimumPerDayForPromotion(req model.GetUserDepositWhileInPromotion) ([]model.GetBankTransactionFirstTimeDepositResponse, error)
	GetDepositPerDayForPromotion(req model.GetUserDepositWhileInPromotion) ([]model.GetBankTransactionFirstTimeDepositResponse, error)
	GetUser(id int64) (*model.UserDetail, error)
	AgcSimpleWinLoseSingleByMemberCode(data model.AgcSimpleWinloseSingle) (*model.AgcSimpleWinloseResponse, error)
	GetTodaySumUserPlayLogList(req model.UserTodayPlaylogListRequest) ([]model.UserTodaySumPlaylogReponse, int64, error)
	ExpiredSuccessPromotionWebUserById(req model.SuccessPromotionWebUserByPromotionWebId) error
	CancelPromotionWebUserToSuccess(req model.CancelPromotionWebUserToSuccess) error
	UpdatePromotionWebUserStatus(req model.UpdatePromotionWebUserStatus) error
	GetDepositCurrentProcessingUserPromotion(userId int64) (*model.PromotionWebUserByUserIdResponse, error)
	GetWithdrawCurrentProcessingUserPromotion(userId int64) (*model.PromotionWebUserByUserIdResponse, error)
	UpdatePromotionToBankTransaction(transactionId int64, promotionWebUserId int64) error
	CreateUser(user model.UserFormCreate) (*int64, error)
	GetExpiredPerDayPromotion() ([]model.GetPromtionWebIdToCancel, error)
	ExpiredExpiredPerDayPromotion(promotionWebUserId []int64) error
	CheckWithdrawPromotionWebByIds(id []int64) ([]model.PromotionWebGetByIdResponse, error)
	GetUserAbleWithdrawPertime(req model.GetUserAbleWithdrawPertime) ([]model.GetBankTransactionFirstTimeDepositResponse, error)
	AutoSuccessPromotionOnWithdraw() ([]model.GetPromtionWebIdToCancel, error)
	AutoSuccessPromotionOnWithdrawNextDay(NextDay string, PromotionWebUserIds []int64) ([]model.PromotionWebUserConfirm, error)
	ExpiredSuccessWithdrawPromotionWebUserByIds(promotionWebUserIds []int64) error
	GetUserFirstDepositOfDayForPromotion(req model.GetUserFirstDepositForPromotion) (*model.GetBankTransactionFirstTimeDepositResponse, error)
	GetDepositForPromotionByTime(req model.GetUserDepositWhileInPromotion) ([]model.GetBankTransactionFirstTimeDepositResponse, error)
	CheckUserPromotionOnlyNewMemberFree(startRegisterAt string, promotionId *int64) ([]model.PromotionWebGetByIdResponse, error)
	CountPromotionWebRegisterMemberGetByPromotionId(req model.PromotionWebUserGetListByPromotionWebIdRequest) (*model.PromotionWebRegisterMemberGetTotalResponse, error)
	PromotionWebRegisterMemberCreate(req model.PromotionWebRegisterMemberCreateRequest) (int64, error)
	PromotionWebRegisterMemberUpdate(req model.PromotionWebRegisterMemberUpdateRequest) error
	CheckPromotionRegisterCount(promotionId []int64) ([]model.PromotionWebRegisterMemberGetTotal, error)
	GetUserById(id int64) (*model.UserResponse, error)
	CheckFirstTimeDepositPromotion(userId int64) (*model.GetBankTransactionFirstTimeDepositResponse, error)
	IsFirstDeposit(userId int64) bool
	CreateTurnoverUserStatement(body model.TurnoverUserStatementCreateBody) (*int64, error)
	PromotionWebCheckTurnStatement(req model.PromotionWebCheckTurnStatementRequest) (*model.TurnoverUserStatementResponse, error)
	CreateTurnoverUserWithdrawLog(body model.CreateTurnoverUserWithdrawLog) (*int64, error)
	UpdateTurnoverUserStatement(id int64, body model.TurnoverUserStatementUpdateBody) error
	UpdatePromotionWebUserStatusPassWithDraw(req model.UpdatePromotionWebUserStatus) error
	CheckTurnSuccessOnThisDay(req model.CheckTurnSuccessOnThisDayRequest) (*model.CheckTurnSuccessOnThisDayResponse, error)
	CheckAvalibleCouponAndPromtion(userId int64, typeCheck string) (*model.TurnoverUserStatementResponse, error)
	//promotion web
	GetPromotionWebList(req model.PromotionWebGetListRequest) ([]model.PromotionWebGetListResponse, int64, error)
	CreatePromotionWeb(req model.PromotionWebCreateRequest) (int64, error)
	GetPromotionWebById(id int64) (*model.PromotionWebGetByIdResponse, error)
	UpdatePromotionWeb(req model.PromotionWebUpdateRequest) error
	GetPromotionWebUserToCancel(PromotionWebId int64) ([]model.GetPromtionWebIdToCancel, error)
	CancelPromotionWeb(req model.CancelPromotionWebRequest) error
	DeletePromotionWeb(req model.DeletePromotionWebRequest) error
	GetExpiredPromotionWeb(today string) ([]model.PromotionWebExpired, error)
	PromotionConfirmUpdatePromotionWebUser(confirmId int64, promotionWebUserId int64) error
	CancelPromotionWebUserById(req model.CancelPromotionWebUserById) error
	ExpiredPromotionWebUserByIds(req model.CancelPromotionWebUserByPromotionWebId) error
	GetUserPromotionWebByUserId(userId int64) (*model.PromotionWebUserByUserIdResponse, error)
	GetUserPromotionWebList(req model.PromotionWebUserGetListRequest) ([]model.PromotionWebUserGetListResponse, int64, error)
	GetPromotionWebUserById(req model.GetPromotionWebUserById) (*model.GetPromotionWebUserByIdResponse, error)
	PromotionWebUserGetListByUserId(req model.PromotionWebUserGetListByUserIdRequest) ([]model.PromotionWebUserGetListByUserIdResponse, int64, error)
	PromotionWebGetSildeListOnlyActive() ([]model.PromotionWebGetSildeListOnlyActive, error)
	UploadImageToCloudflare(pathUplaod string, filename string, fileReader io.Reader) (*model.CloudFlareUploadCreateBody, error)
	UpdatePromotionWebPriorityOrderCreate(id int64) error
	SortPromotionWebPriorityOrder(req model.DragSortRequest) error
	// promotion web user
	CreateUserCollectPromotionWeb(req model.PromotionWebUserCreateBody) (int64, error)
	ShowPromotionWebForUser() ([]model.ShowPromotionWebForUserResponse, error)
	// Promotion Report
	GetUserPromotionReportList(req model.GetUserPromotionReportListRequest) ([]model.PromotionWebUserGetListResponse, int64, error)
	//action confirm
	CreatePromotionWebUserConfirmByActionKey(body model.PromotionWebUserConfirmCreateRequest) (int64, error)
	GetPromotionWebUserConfirmByActionKey(actionKey string) (*int64, error)
	DeletePromotionWebUserConfirmByActionKey(actionKey string) error
	GetPromotionWebUserConfirmByPromotionWebUserId(PromotionWebUserIds []int64) ([]model.PromotionWebUserConfirm, error)
	UpdatePromotionWebUserConfirmById(body model.PromotionWebUserConfirmUpdateRequest) error
	FindPromtionConfirmByPromotionWebUserId(promotionWebUserId int64) (*model.PromotionWebUserConfirm, error)

	//log
	PromotionErrorLog(name string, req interface{}, result interface{}) error
	PromotionSuccessLog(name string, req interface{}, result interface{}) error

	//inservice
	ParseBodBkk(input string) (*time.Time, error)
	ParseEodBkk(input string) (*time.Time, error)

	// increase credit
	IncreaseUserCredit(body model.UserTransactionCreateRequest) (*model.UserTransactionCreateResponse, error)
	GetConfiguration() (*model.ConfigurationResponse, error)
	CronjobDeletePromotionWebUserLog() error
	OtherPromotionClearWebUserById(userId int64) error

	PromotionWebSummary(req model.PromotionWebGetListSummaryRequest) (*model.PromotionWebGetListSummaryResponse, error)
	PromotionWebUserSummary(req model.PromotionWebUserGetListSummaryRequest) (*model.PromotionWebUserGetListSummaryResponse, error)

	// s3
	UploadImageToS3(pathUpload string, filename string, fileReader io.Reader) (*model.FileUploadResponse, error)

	// new flow
	CheckRegisterMemberByUserIdAndPromotionId(userId int64, startDate string) (bool, error)
	LockCreditPromotionCreate(req model.LockCreditPromotionCreateRequest) (int64, error)
	LockCreditPromotionUpdate(adminId int64, req model.LockCreditPromotionUpdateRequest) error
	CheckIsLockedCreditPromotionByUserId(userId int64) (bool, error)

	LockCreditWithdrawCreate(req model.LockCreditWithdrawCreateRequest) (int64, error)
	GetLockCreditWithdrawList(req model.GetLockCreditWithdrawListRequest) ([]model.GetLockCreditWithdrawListResponse, int64, error)
	UpdateLockCreditWithdraw(req model.UpdateLockCreditWithdrawRequest) error
	CheckLockCreditWithdrawByUserId(userId int64) ([]model.CheckLockCreditWithdrawByUserId, error)

	CheckAlreadyUserTypePromotionByUserId(userId int64, promotionWebTypeId int64) (bool, error)
	CheckAlreadyLockCreditWithdrawByUserId(userId int64) (bool, error)
	CheckTurnoverStatementByUserIdAndTypeId(userId int64) (bool, error)

	GetAllWithdrawProcessPromotionWebUser() ([]model.GetAllWithdrawProcessPromotionWebUser, error)
	GetAllLockCreditWithdrawByRefId(refId []int64) ([]model.GetAllLockCreditWithdrawByRefId, error)

	ShowPromotionWebForUserHiddenUrl(hiddenUrlLink string) ([]model.ShowPromotionWebForUserResponse, error)
	ShowPromotionWebForPublic() ([]model.ShowPromotionWebForUserResponse, error)
}

func (r repo) GetPromotionWebType() ([]model.PromotionWebTypeResponse, error) {

	var result []model.PromotionWebTypeResponse
	selectedFields := "id, name, label_th, label_en"
	query := r.db.Table("promotion_web_type")
	query = query.Select(selectedFields)
	if err := query.Find(&result).Error; err != nil {
		return nil, err
	}

	return result, nil
}
func (r repo) GetPromotionWebStatus() ([]model.PromotionWebStatusResponse, error) {

	var result []model.PromotionWebStatusResponse
	selectedFields := "id, name, label_th, label_en"
	query := r.db.Table("promotion_web_status")
	query = query.Select(selectedFields)
	if err := query.Find(&result).Error; err != nil {
		return nil, err
	}

	return result, nil
}

func (r repo) GetPromotionWebBonusCondition() ([]model.PromotionWebBonusConditionResponse, error) {

	var result []model.PromotionWebBonusConditionResponse
	selectedFields := "id, syntax, name, label_th, label_en"
	query := r.db.Table("promotion_web_bonus_condition")
	query = query.Select(selectedFields)
	if err := query.Find(&result).Error; err != nil {
		return nil, err
	}

	return result, nil
}

func (r repo) GetPromotionWebBonusType() ([]model.PromotionWebBonusTypeResponse, error) {

	var result []model.PromotionWebBonusTypeResponse
	selectedFields := "id, name, label_th, label_en"
	query := r.db.Table("promotion_web_bonus_type")
	query = query.Select(selectedFields)
	if err := query.Find(&result).Error; err != nil {
		return nil, err
	}

	return result, nil
}

func (r repo) GetPromotionWebTurnoverType() ([]model.PromotionWebTurnoverTypeResponse, error) {

	var result []model.PromotionWebTurnoverTypeResponse
	selectedFields := "id, name, label_th, label_en"
	query := r.db.Table("promotion_web_turnover_type")
	query = query.Select(selectedFields)
	if err := query.Find(&result).Error; err != nil {
		return nil, err
	}

	return result, nil
}

func (r repo) CreatePromotionWeb(req model.PromotionWebCreateRequest) (int64, error) {

	req.UpdatedAt = time.Now().UTC()
	query := r.db.Table("promotion_web")
	query = query.Create(&req)
	if err := query.Error; err != nil {
		return 0, err
	}

	return req.Id, nil
}

func (r repo) GetpromotionWebDateType() ([]model.GetpromotionWebDateTypeResponse, error) {

	var result []model.GetpromotionWebDateTypeResponse
	selectedFields := "id, name, label_th, label_en"
	query := r.db.Table("promotion_web_date_type")
	query = query.Select(selectedFields)
	if err := query.Find(&result).Error; err != nil {
		return nil, err
	}

	return result, nil
}
func (r repo) GetPromotionWebUserStatus() ([]model.GetpromotionWebDateTypeResponse, error) {

	var result []model.GetpromotionWebDateTypeResponse
	selectedFields := "id, name, label_th, label_en"
	query := r.db.Table("promotion_web_user_status")
	query = query.Select(selectedFields)
	if err := query.Find(&result).Error; err != nil {
		return nil, err
	}

	return result, nil
}

func (r repo) GetPromotionWebById(id int64) (*model.PromotionWebGetByIdResponse, error) {

	var result model.PromotionWebGetByIdResponse

	selectedFields := "promotion_web.id AS id, promotion_web.promotion_web_type_id AS promotion_web_type_id, promotion_web_type.label_th AS promotion_web_type_th"
	selectedFields += ", promotion_web.promotion_web_status_id AS promotion_web_status_id, promotion_web_status.label_th AS promotion_web_status_th"
	selectedFields += ", promotion_web.condition_detail AS condition_detail, promotion_web.image_url AS image_url, promotion_web.name AS name"
	selectedFields += ", promotion_web.short_description AS short_description, promotion_web.description AS description, promotion_web.start_date AS start_date"
	selectedFields += ", promotion_web.end_date AS end_date, promotion_web.free_bonus_amount AS free_bonus_amount, promotion_web.privilege_per_day AS privilege_per_day"
	selectedFields += ", promotion_web.able_withdraw_morethan AS able_withdraw_morethan, promotion_web.promotion_web_bonus_condition_id AS promotion_web_bonus_condition_id"
	selectedFields += ", promotion_web_bonus_condition.label_th AS promotion_web_bonus_condition_th, promotion_web_bonus_condition.syntax AS promotion_web_bonus_condition_syntax"
	selectedFields += ", promotion_web.bonus_condition_amount AS bonus_condition_amount, promotion_web.promotion_web_bonus_type_id AS promotion_web_bonus_type_id"
	selectedFields += ", promotion_web_bonus_type.label_th AS promotion_web_bonus_type_th, promotion_web.bonus_type_amount AS bonus_type_amount"
	selectedFields += ", promotion_web.able_withdraw_pertime AS able_withdraw_pertime, promotion_web.promotion_web_turnover_type_id AS promotion_web_turnover_type_id"
	selectedFields += ", promotion_web_turnover_type.label_th AS promotion_web_turnover_type_th, promotion_web.turnover_amount AS turnover_amount"
	selectedFields += ", promotion_web.monday AS monday, promotion_web.tuesday AS tuesday, promotion_web.wednesday AS wednesday"
	selectedFields += ", promotion_web.thursday AS thursday, promotion_web.friday AS friday, promotion_web.saturday AS saturday"
	selectedFields += ", promotion_web.sunday AS sunday, promotion_web.time_start AS time_start, promotion_web.time_end AS time_end"
	selectedFields += ", promotion_web.promotion_web_date_type_id AS promotion_web_date_type_id, promotion_web_date_type.label_th AS promotion_web_date_type_th"
	selectedFields += ", promotion_web.bonus_type_amount_max AS bonus_type_amount_max"

	query := r.db.Table("promotion_web")
	query = query.Select(selectedFields)
	query = query.Joins("LEFT JOIN promotion_web_type ON promotion_web.promotion_web_type_id = promotion_web_type.id")
	query = query.Joins("LEFT JOIN promotion_web_status ON promotion_web.promotion_web_status_id = promotion_web_status.id")
	query = query.Joins("LEFT JOIN promotion_web_bonus_condition ON promotion_web.promotion_web_bonus_condition_id = promotion_web_bonus_condition.id")
	query = query.Joins("LEFT JOIN promotion_web_bonus_type ON promotion_web.promotion_web_bonus_type_id = promotion_web_bonus_type.id")
	query = query.Joins("LEFT JOIN promotion_web_turnover_type ON promotion_web.promotion_web_turnover_type_id = promotion_web_turnover_type.id")
	query = query.Joins("LEFT JOIN promotion_web_date_type ON promotion_web.promotion_web_date_type_id = promotion_web_date_type.id")
	query = query.Where("promotion_web.id = ?", id)
	query = query.Where("promotion_web.deleted_at IS NULL")
	if err := query.First(&result).Error; err != nil {
		return nil, err
	}

	return &result, nil
}

func (r repo) GetPromotionWebInternalList(req model.PromotionWebGetInternalListRequest) ([]model.PromotionWebGetInternalListResponse, int64, error) {

	var list []model.PromotionWebGetInternalListResponse
	var total int64
	var err error

	selectedFields := "promotion_web.id AS id, promotion_web.promotion_web_type_id AS promotion_web_type_id, promotion_web_type.label_th AS promotion_web_type_th"
	selectedFields += ", promotion_web.promotion_web_status_id AS promotion_web_status_id, promotion_web_status.label_th AS promotion_web_status_th"
	selectedFields += ", promotion_web.condition_detail AS condition_detail, promotion_web.image_url AS image_url, promotion_web.name AS name"
	selectedFields += ", promotion_web.short_description AS short_description, promotion_web.description AS description, promotion_web.start_date AS start_date"
	selectedFields += ", promotion_web.end_date AS end_date, promotion_web.free_bonus_amount AS free_bonus_amount, promotion_web.privilege_per_day AS privilege_per_day"
	selectedFields += ", promotion_web.able_withdraw_morethan AS able_withdraw_morethan, promotion_web.promotion_web_bonus_condition_id AS promotion_web_bonus_condition_id"
	selectedFields += ", promotion_web_bonus_condition.label_th AS promotion_web_bonus_condition_th, promotion_web_bonus_condition.syntax AS promotion_web_bonus_condition_syntax"
	selectedFields += ", promotion_web.bonus_condition_amount AS bonus_condition_amount, promotion_web.promotion_web_bonus_type_id AS promotion_web_bonus_type_id"
	selectedFields += ", promotion_web_bonus_type.label_th AS promotion_web_bonus_type_th, promotion_web.bonus_type_amount AS bonus_type_amount"
	selectedFields += ", promotion_web.able_withdraw_pertime AS able_withdraw_pertime, promotion_web.promotion_web_turnover_type_id AS promotion_web_turnover_type_id"
	selectedFields += ", promotion_web_turnover_type.label_th AS promotion_web_turnover_type_th, promotion_web.turnover_amount AS turnover_amount"
	selectedFields += ", promotion_web.monday AS monday, promotion_web.tuesday AS tuesday, promotion_web.wednesday AS wednesday"
	selectedFields += ", promotion_web.thursday AS thursday, promotion_web.friday AS friday, promotion_web.saturday AS saturday"
	selectedFields += ", promotion_web.sunday AS sunday, promotion_web.time_start AS time_start, promotion_web.time_end AS time_end"
	selectedFields += ", promotion_web.promotion_web_date_type_id AS promotion_web_date_type_id, promotion_web_date_type.label_th AS promotion_web_date_type_th"
	selectedFields += ", promotion_web.bonus_type_amount_max AS bonus_type_amount_max"

	count := r.db.Table("promotion_web")
	count = count.Joins("LEFT JOIN promotion_web_type ON promotion_web.promotion_web_type_id = promotion_web_type.id")
	count = count.Joins("LEFT JOIN promotion_web_status ON promotion_web.promotion_web_status_id = promotion_web_status.id")
	count = count.Joins("LEFT JOIN promotion_web_bonus_condition ON promotion_web.promotion_web_bonus_condition_id = promotion_web_bonus_condition.id")
	count = count.Joins("LEFT JOIN promotion_web_bonus_type ON promotion_web.promotion_web_bonus_type_id = promotion_web_bonus_type.id")
	count = count.Joins("LEFT JOIN promotion_web_turnover_type ON promotion_web.promotion_web_turnover_type_id = promotion_web_turnover_type.id")
	count = count.Joins("LEFT JOIN promotion_web_date_type ON promotion_web.promotion_web_date_type_id = promotion_web_date_type.id")
	count = count.Select("promotion_web.id")

	if req.StartDate != "" {
		// startDateAtBkk, err := r.ParseBodBkk(req.StartDate)
		// if err != nil {
		// 	return nil, 0, err
		// }

		count = count.Where("promotion_web.start_date >= ? ", req.StartDate)
		count = count.Where("promotion_web.start_date IS NULL")
	}
	if req.EndDate != "" {
		// endDateAtBkk, err := r.ParseEodBkk(req.EndDate)
		// if err != nil {
		// 	return nil, 0, err
		// }
		count = count.Where("scammer.end_date <=  ?", req.EndDate)
		count = count.Where("promotion_web.end_date IS NULL")
	}

	if req.Search != "" {
		count = count.Where("promotion_web.name LIKE ? OR promotion_web.short_description LIKE ? OR promotion_web.description LIKE ?", "%"+req.Search+"%", "%"+req.Search+"%", "%"+req.Search+"%")
	}

	if err = count.
		Where("promotion_web.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, 0, err
	}

	if total > 0 {

		query := r.db.Table("promotion_web")
		query = query.Joins("LEFT JOIN promotion_web_type ON promotion_web.promotion_web_type_id = promotion_web_type.id")
		query = query.Joins("LEFT JOIN promotion_web_status ON promotion_web.promotion_web_status_id = promotion_web_status.id")
		query = query.Joins("LEFT JOIN promotion_web_bonus_condition ON promotion_web.promotion_web_bonus_condition_id = promotion_web_bonus_condition.id")
		query = query.Joins("LEFT JOIN promotion_web_bonus_type ON promotion_web.promotion_web_bonus_type_id = promotion_web_bonus_type.id")
		query = query.Joins("LEFT JOIN promotion_web_turnover_type ON promotion_web.promotion_web_turnover_type_id = promotion_web_turnover_type.id")
		query = query.Joins("LEFT JOIN promotion_web_date_type ON promotion_web.promotion_web_date_type_id = promotion_web_date_type.id")
		query = query.Select(selectedFields)

		if req.StartDate != "" {
			// startDateAtBkk, err := r.ParseBodBkk(req.StartDate)
			// if err != nil {
			// 	return nil, 0, err
			// }
			query = query.Where("promotion_web.start_date >= ? ", req.StartDate)
			query = query.Where("promotion_web.start_date IS NULL")
		}
		if req.EndDate != "" {
			// endDateAtBkk, err := r.ParseEodBkk(req.EndDate)
			// if err != nil {
			// 	return nil, 0, err
			// }
			query = query.Where("scammer.end_date <=  ?", req.EndDate)
			query = query.Where("promotion_web.end_date IS NULL")
		}

		if req.Search != "" {
			query = query.Where("promotion_web.name LIKE ? OR promotion_web.short_description LIKE ? OR promotion_web.description LIKE ?", "%"+req.Search+"%", "%"+req.Search+"%", "%"+req.Search+"%")
		}

		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}

		if err := query.
			Where("promotion_web.deleted_at IS NULL").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, 0, err
		}
	}

	return list, total, nil
}

func (r repo) UpdatePromotionWeb(req model.PromotionWebUpdateRequest) error {

	query := r.db.Table("promotion_web")
	query = query.Where("id = ?", req.Id)
	query = query.Updates(&req)
	if err := query.Error; err != nil {
		return err
	}

	return nil
}

func (r repo) CancelPromotionWeb(req model.CancelPromotionWebRequest) error {

	updateBody := interface{}(map[string]interface{}{
		"promotion_web_status_id": req.PromotionWebStatusId,
		"canceled_by_admin_id":    req.CanceledByAdminId,
	})

	query := r.db.Table("promotion_web")
	query = query.Where("id = ?", req.Id)
	query = query.Updates(updateBody)
	if err := query.Error; err != nil {
		return err
	}

	return nil
}

func (r repo) GetPromotionWebList(req model.PromotionWebGetListRequest) ([]model.PromotionWebGetListResponse, int64, error) {

	var list []model.PromotionWebGetListResponse
	var total int64
	var err error

	selectedFields := "promotion_web.id AS id, promotion_web.promotion_web_type_id AS promotion_web_type_id, promotion_web_type.label_th AS promotion_web_type_th"
	selectedFields += ", promotion_web.promotion_web_status_id AS promotion_web_status_id, promotion_web_status.label_th AS promotion_web_status_th"
	selectedFields += ", promotion_web.name AS name, promotion_web.start_date AS start_date, promotion_web.end_date AS end_date"

	selectedFields += ", promotion_web.created_by_admin_id AS created_by_admin_id"
	selectedFields += ", CASE WHEN promotion_web.created_by_admin_id = 0 THEN 'อัตโนมัติ' ELSE admincreate.username END AS created_by_admin_name"

	selectedFields += ", promotion_web.updated_by_admin_id AS updated_by_admin_id"
	selectedFields += ", CASE WHEN promotion_web.updated_by_admin_id = 0 THEN 'อัตโนมัติ' ELSE adminupdate.username END AS updated_by_admin_name"

	selectedFields += ", promotion_web.canceled_by_admin_id AS canceled_by_admin_id"
	selectedFields += ", CASE WHEN promotion_web.canceled_by_admin_id = 0 THEN 'อัตโนมัติ' ELSE admincancel.username END AS canceled_by_admin_name"

	selectedFields += ", promotion_web.updated_at AS updated_at, promotion_web.promotion_web_date_type_id AS promotion_web_date_type_id, promotion_web_date_type.label_th AS promotion_web_date_type_th"
	selectedFields += ", promotion_web.time_start AS time_start, promotion_web.time_end AS time_end"

	selectedFields += ", promotion_web.hidden_url_link AS hidden_url_link"

	count := r.db.Table("promotion_web")
	count = count.Joins("LEFT JOIN promotion_web_type ON promotion_web.promotion_web_type_id = promotion_web_type.id")
	count = count.Joins("LEFT JOIN promotion_web_status ON promotion_web.promotion_web_status_id = promotion_web_status.id")
	count = count.Joins("LEFT JOIN admin AS admincreate ON promotion_web.created_by_admin_id = admincreate.id")
	count = count.Joins("LEFT JOIN admin AS adminupdate ON promotion_web.updated_by_admin_id = adminupdate.id")
	count = count.Joins("LEFT JOIN admin AS admincancel ON promotion_web.canceled_by_admin_id = admincancel.id")
	count = count.Joins("LEFT JOIN promotion_web_date_type ON promotion_web.promotion_web_date_type_id = promotion_web_date_type.id")
	count = count.Select("promotion_web.id")

	if req.StartDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.StartDate)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("promotion_web.created_at >= ? ", startDateAtBkk)
		// count = count.Where("promotion_web.start_date IS NULL")
	}
	if req.EndDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.EndDate)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("promotion_web.created_at <=  ?", endDateAtBkk)
		// count = count.Where("promotion_web.end_date IS NULL")
	}

	if req.Search != "" {
		count = count.Where("promotion_web.name LIKE ?", "%"+req.Search+"%")
	}

	if req.PromotionWebStatusId != nil {
		count = count.Where("promotion_web.promotion_web_status_id = ?", req.PromotionWebStatusId)
	}

	if err = count.
		Where("promotion_web.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, 0, err
	}

	if total > 0 {
		query := r.db.Table("promotion_web")
		query = query.Joins("LEFT JOIN promotion_web_type ON promotion_web.promotion_web_type_id = promotion_web_type.id")
		query = query.Joins("LEFT JOIN promotion_web_status ON promotion_web.promotion_web_status_id = promotion_web_status.id")
		query = query.Joins("LEFT JOIN admin AS admincreate ON promotion_web.created_by_admin_id = admincreate.id")
		query = query.Joins("LEFT JOIN admin AS adminupdate ON promotion_web.updated_by_admin_id = adminupdate.id")
		query = query.Joins("LEFT JOIN admin AS admincancel ON promotion_web.canceled_by_admin_id = admincancel.id")
		query = query.Joins("LEFT JOIN promotion_web_date_type ON promotion_web.promotion_web_date_type_id = promotion_web_date_type.id")
		query = query.Select(selectedFields)

		if req.StartDate != "" {
			startDateAtBkk, err := r.ParseBodBkk(req.StartDate)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("promotion_web.created_at >= ? ", startDateAtBkk)
			// query = query.Where("promotion_web.start_date IS NULL")
		}
		if req.EndDate != "" {
			endDateAtBkk, err := r.ParseEodBkk(req.EndDate)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("promotion_web.created_at <=  ?", endDateAtBkk)
			// query = query.Where("promotion_web.end_date IS NULL")
		}

		if req.Search != "" {
			query = query.Where("promotion_web.name LIKE ?", "%"+req.Search+"%")
		}

		if req.PromotionWebStatusId != nil {
			query = query.Where("promotion_web.promotion_web_status_id = ?", req.PromotionWebStatusId)
		}

		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}

		if err := query.
			Order("promotion_web.priority_order ASC").
			Where("promotion_web.deleted_at IS NULL").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, 0, err
		}
	}

	return list, total, nil
}

func (r repo) CreateUserCollectPromotionWeb(req model.PromotionWebUserCreateBody) (int64, error) {

	query := r.db.Table("promotion_web_user")
	query = query.Create(&req)
	if err := query.Error; err != nil {
		return 0, err
	}

	return req.Id, nil
}

func (r repo) GetUserPromotionWebByUserId(userId int64) (*model.PromotionWebUserByUserIdResponse, error) {

	var result model.PromotionWebUserByUserIdResponse
	selectedFields := "promotion_web_user.id AS id, promotion_web_user.promotion_web_id AS promotion_web_id, promotion_web.name AS promotion_name"
	selectedFields += ", promotion_web_user.user_id AS user_id, user.member_code AS member_code, user.fullname AS full_name, user.phone AS phone"
	selectedFields += ", promotion_web_user.promotion_web_user_status_id AS promotion_web_user_status_id, promotion_web_user_status.label_th AS promotion_web_user_status_th"
	selectedFields += ", promotion_web_user.total_amount AS total_amount, promotion_web_user.created_at AS created_at"

	query := r.db.Table("promotion_web_user")
	query = query.Joins("LEFT JOIN promotion_web ON promotion_web_user.promotion_web_id = promotion_web.id")
	query = query.Joins("LEFT JOIN user ON promotion_web_user.user_id = user.id")
	query = query.Joins("LEFT JOIN promotion_web_user_status ON promotion_web_user.promotion_web_user_status_id = promotion_web_user_status.id")
	query = query.Select(selectedFields)
	query = query.Where("promotion_web_user.user_id = ?", userId)
	query = query.Where("promotion_web_user.deleted_at IS NULL")
	if err := query.First(&result).Error; err != nil {
		return nil, err
	}

	return &result, nil
}

func (r repo) GetUserPromotionWebList(req model.PromotionWebUserGetListRequest) ([]model.PromotionWebUserGetListResponse, int64, error) {

	var list []model.PromotionWebUserGetListResponse
	var total int64
	var err error

	selectedFields := "promotion_web_user.id AS id, promotion_web_user.promotion_web_id AS promotion_web_id, promotion_web.name AS promotion_name"
	selectedFields += ", promotion_web_user.user_id AS user_id, user.member_code AS member_code, user.fullname AS full_name, user.phone AS phone"
	selectedFields += ", promotion_web_user.promotion_web_user_status_id AS promotion_web_user_status_id, promotion_web_user_status.label_th AS promotion_web_user_status_th"
	selectedFields += ", promotion_web_user.total_amount AS total_amount, promotion_web_user.created_at AS created_at"
	selectedFields += ", promotion_web_user.canceled_by_admin_id AS canceled_by_admin_id"
	selectedFields += ", promotion_web_user.canceled_at AS canceled_at"
	selectedFields += ", CASE WHEN admincancel.id IS NULL THEN 'อัตโนมัติ' ELSE admincancel.fullname END AS canceled_by_admin_name"
	selectedFields += ", promotion_web_lock_credit.is_locked AS is_locked"
	selectedFields += ", promotion_web_user.approve_credit_by_admin_id AS approve_credit_by_admin_id, adminapprove.fullname AS approve_credit_by_admin_name"
	selectedFields += ", promotion_web_user.approve_credit_at AS approve_credit_at"
	selectedFields += ", promotion_web.able_withdraw_morethan AS able_withdraw_morethan, promotion_web.able_withdraw_pertime AS able_withdraw_pertime"

	count := r.db.Table("promotion_web_user")
	count = count.Joins("LEFT JOIN promotion_web ON promotion_web_user.promotion_web_id = promotion_web.id")
	count = count.Joins("LEFT JOIN user ON promotion_web_user.user_id = user.id")
	count = count.Joins("LEFT JOIN promotion_web_user_status ON promotion_web_user.promotion_web_user_status_id = promotion_web_user_status.id")
	count = count.Joins("LEFT JOIN admin AS admincancel ON promotion_web_user.canceled_by_admin_id = admincancel.id")
	count = count.Joins("LEFT JOIN admin AS adminapprove ON promotion_web_user.approve_credit_by_admin_id = adminapprove.id")
	count = count.Select("promotion_web_user.id")

	if req.PromotionWebId != nil {
		count = count.Where("promotion_web_user.promotion_web_id = ?", req.PromotionWebId)
	}
	// if req.TypeList == "CANCELED" {
	// 	count = count.Where("promotion_web_user.promotion_web_user_status_id = ?", model.PROMOTION_WEB_USER_STATUS_CANCELED)
	// }
	// if req.TypeList == "OTHER" {
	// 	count = count.Where("promotion_web_user.promotion_web_user_status_id != ?", model.PROMOTION_WEB_USER_STATUS_CANCELED)
	// }
	if req.StartDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.StartDate)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("promotion_web_user.created_at >= ? ", startDateAtBkk)
	}
	if req.EndDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.EndDate)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("promotion_web_user.created_at <=  ?", endDateAtBkk)
	}
	if req.PromotionWebUserStatusId != nil {
		count = count.Where("promotion_web_user.promotion_web_user_status_id = ?", req.PromotionWebUserStatusId)
	}

	if req.Search != "" {
		count = count.Where("promotion_web.name LIKE ? OR user.member_code LIKE ? OR user.fullname LIKE ? OR user.phone LIKE ?", "%"+req.Search+"%", "%"+req.Search+"%", "%"+req.Search+"%", "%"+req.Search+"%")
	}

	if err = count.
		Where("promotion_web_user.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, 0, err
	}

	if total > 0 {
		query := r.db.Table("promotion_web_user")
		query = query.Joins("LEFT JOIN promotion_web ON promotion_web_user.promotion_web_id = promotion_web.id")
		query = query.Joins("LEFT JOIN user ON promotion_web_user.user_id = user.id")
		query = query.Joins("LEFT JOIN promotion_web_user_status ON promotion_web_user.promotion_web_user_status_id = promotion_web_user_status.id")
		query = query.Joins("LEFT JOIN admin AS admincancel ON promotion_web_user.canceled_by_admin_id = admincancel.id")
		query = query.Joins("LEFT JOIN promotion_web_lock_credit ON promotion_web_user.user_id = promotion_web_lock_credit.user_id AND promotion_web_user.promotion_web_id = promotion_web_lock_credit.promotion_id")
		query = query.Joins("LEFT JOIN admin AS adminapprove ON promotion_web_user.approve_credit_by_admin_id = adminapprove.id")

		query = query.Select(selectedFields)

		if req.PromotionWebId != nil {
			query = query.Where("promotion_web_user.promotion_web_id = ?", req.PromotionWebId)
		}

		// if req.TypeList == "CANCELED" {
		// 	query = query.Where("promotion_web_user.promotion_web_user_status_id = ?", model.PROMOTION_WEB_USER_STATUS_CANCELED)
		// }
		// if req.TypeList == "OTHER" {
		// 	query = query.Where("promotion_web_user.promotion_web_user_status_id != ?", model.PROMOTION_WEB_USER_STATUS_CANCELED)
		// }

		if req.PromotionWebUserStatusId != nil {
			query = query.Where("promotion_web_user.promotion_web_user_status_id = ?", req.PromotionWebUserStatusId)
		}
		if req.StartDate != "" {
			startDateAtBkk, err := r.ParseBodBkk(req.StartDate)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("promotion_web_user.created_at >= ? ", startDateAtBkk)
		}
		if req.EndDate != "" {
			endDateAtBkk, err := r.ParseEodBkk(req.EndDate)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("promotion_web_user.created_at <=  ?", endDateAtBkk)
		}

		if req.Search != "" {
			query = query.Where("promotion_web.name LIKE ? OR user.member_code LIKE ? OR user.fullname LIKE ? OR user.phone LIKE ?", "%"+req.Search+"%", "%"+req.Search+"%", "%"+req.Search+"%", "%"+req.Search+"%")
		}

		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}

		if err := query.
			Where("promotion_web_user.deleted_at IS NULL").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, 0, err
		}
	}

	return list, total, nil
}

func (r repo) CheckCollectedUserPromotionWebByUserId(userId int64) ([]model.PromotionWebUserByUserIdResponse, error) {

	var result []model.PromotionWebUserByUserIdResponse
	selectedFields := "promotion_web_user.id AS id, promotion_web_user.promotion_web_id AS promotion_web_id, promotion_web.name AS promotion_name"
	selectedFields += ", promotion_web_user.user_id AS user_id, user.member_code AS member_code, user.fullname AS full_name, user.phone AS phone"
	selectedFields += ", promotion_web_user.promotion_web_user_status_id AS promotion_web_user_status_id, promotion_web_user_status.label_th AS promotion_web_user_status_th"
	selectedFields += ", promotion_web_user.total_amount AS total_amount, promotion_web_user.created_at AS created_at"

	query := r.db.Table("promotion_web_user")
	query = query.Joins("LEFT JOIN promotion_web ON promotion_web_user.promotion_web_id = promotion_web.id")
	query = query.Joins("LEFT JOIN user ON promotion_web_user.user_id = user.id")
	query = query.Joins("LEFT JOIN promotion_web_user_status ON promotion_web_user.promotion_web_user_status_id = promotion_web_user_status.id")
	query = query.Select(selectedFields)
	query = query.Where("promotion_web_user.user_id = ?", userId)
	query = query.Where("promotion_web_user.deleted_at IS NULL")
	if err := query.Scan(&result).Error; err != nil {
		return nil, err
	}

	return result, nil
}

func (r repo) CancelPromotionWebUserById(req model.CancelPromotionWebUserById) error {

	statusCanceled := model.PROMOTION_WEB_USER_STATUS_CANCELED

	updateBody := interface{}(map[string]interface{}{
		"promotion_web_user_status_id": statusCanceled,
		"canceled_by_admin_id":         req.CanceledByAdminId,
		"canceled_at":                  time.Now().UTC(),
	})

	query := r.db.Table("promotion_web_user")
	// query = query.Where("promotion_web_id = ?", req.PromotionWebId)
	query = query.Where("promotion_web_user_status_id = ?", model.PROMOTION_WEB_USER_STATUS_ON_PROCESS)
	query = query.Where("id = ?", req.Id)
	query = query.Updates(updateBody)
	if err := query.Error; err != nil {
		return err
	}

	return nil
}

func (r repo) ExpiredPromotionWebUserByIds(req model.CancelPromotionWebUserByPromotionWebId) error {

	statusCanceled := model.PROMOTION_WEB_USER_STATUS_CANCELED

	updateBody := interface{}(map[string]interface{}{
		"promotion_web_user_status_id": statusCanceled,
		"canceled_by_admin_id":         req.CanceledByAdminId,
		"canceled_at":                  req.CanceledAt,
	})

	query := r.db.Table("promotion_web_user")
	query = query.Where("promotion_web_id = ?", req.PromotionWebId)
	query = query.Where("promotion_web_user_status_id = ?", model.PROMOTION_WEB_USER_STATUS_ON_PROCESS)
	query = query.Updates(updateBody)
	if err := query.Error; err != nil {
		return err
	}

	return nil
}
func (r repo) ExpiredExpiredPerDayPromotion(promotionWebUserId []int64) error {

	statusCanceled := model.PROMOTION_WEB_USER_STATUS_CANCELED

	updateBody := interface{}(map[string]interface{}{
		"promotion_web_user_status_id": statusCanceled,
		"canceled_by_admin_id":         0,
		"canceled_at":                  time.Now().UTC(),
	})

	query := r.db.Table("promotion_web_user")
	query = query.Where("id IN ?", promotionWebUserId)
	query = query.Where("promotion_web_user_status_id = ?", model.PROMOTION_WEB_USER_STATUS_ON_PROCESS)
	query = query.Updates(updateBody)
	if err := query.Error; err != nil {
		return err
	}

	return nil
}

func (r repo) ExpiredSuccessPromotionWebUserById(req model.SuccessPromotionWebUserByPromotionWebId) error {

	successStatus := model.PROMOTION_WEB_USER_STATUS_SUCCESS

	updateBody := interface{}(map[string]interface{}{
		"promotion_web_user_status_id": successStatus,
	})

	query := r.db.Table("promotion_web_user")
	// query = query.Where("id = ?", req.Id)
	query = query.Where("promotion_web_id = ?", req.PromotionWebId)
	query = query.Where("promotion_web_user_status_id = ?", model.PROMOTION_WEB_USER_STATUS_ON_WITHDRAW)
	query = query.Updates(updateBody)
	if err := query.Error; err != nil {
		return err
	}

	return nil
}

func (r repo) CancelPromotionWebUserToSuccess(req model.CancelPromotionWebUserToSuccess) error {

	successStatus := model.PROMOTION_WEB_USER_STATUS_SUCCESS

	updateBody := interface{}(map[string]interface{}{
		"promotion_web_user_status_id": successStatus,
		"canceled_by_admin_id":         req.CanceledByAdminId,
		"canceled_at":                  time.Now().UTC(),
	})

	query := r.db.Table("promotion_web_user")
	// query = query.Where("promotion_web_id = ?", req.PromotionWebId)
	query = query.Where("promotion_web_user_status_id IN ?", []int64{model.PROMOTION_WEB_USER_STATUS_ON_WITHDRAW, model.PROMOTION_WEB_USER_STATUS_ON_PROCESS})
	query = query.Where("id = ?", req.Id)
	query = query.Updates(updateBody)
	if err := query.Error; err != nil {
		return err
	}

	return nil
}

func (r repo) GetPromotionWebUserToCancel(PromotionWebId int64) ([]model.GetPromtionWebIdToCancel, error) {

	var list []model.GetPromtionWebIdToCancel
	// PromotionWebUserStatusId
	selectedFields := "promotion_web_user.id AS id, promotion_web_user.promotion_web_user_status_id AS promotion_web_user_status_id"
	query := r.db.Table("promotion_web_user")
	query = query.Select(selectedFields)
	query = query.Where("promotion_web_id = ?", PromotionWebId)

	if err := query.Scan(&list).Error; err != nil {
		return nil, err
	}

	return list, nil
}
func (r repo) GetExpiredPerDayPromotion() ([]model.GetPromtionWebIdToCancel, error) {

	expiredAfterDay := time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02")

	startDateAtBkk, err := r.ParseBodBkk(expiredAfterDay)
	if err != nil {
		return nil, err
	}

	var list []model.GetPromtionWebIdToCancel
	selectedFields := "promotion_web_user.id AS id, promotion_web_user.promotion_web_user_status_id AS promotion_web_user_status_id"
	query := r.db.Table("promotion_web_user")
	query = query.Select(selectedFields)
	query = query.Joins("LEFT JOIN promotion_web ON promotion_web_user.promotion_web_id = promotion_web.id")
	query = query.Where("promotion_web_user.created_at <= ?", startDateAtBkk)
	query = query.Where("promotion_web.promotion_web_type_id IN (?)", []int64{model.PROMOTION_WEB_TYPE_DEPOSIT_PER_DAY, model.PROMOTION_WEB_TYPE_DEPOSIT_MINIMUM_PER_DAY, model.PROMOTION_WEB_TYPE_FIRST_DEPOSIT_OF_DAY})

	if err := query.Scan(&list).Error; err != nil {
		return nil, err
	}

	return list, nil
}
func (r repo) AutoSuccessPromotionOnWithdraw() ([]model.GetPromtionWebIdToCancel, error) {

	var list []model.GetPromtionWebIdToCancel
	// PromotionWebUserStatusId
	selectedFields := "promotion_web_user.id AS id, promotion_web_user.promotion_web_user_status_id AS promotion_web_user_status_id"
	query := r.db.Table("promotion_web_user")
	query = query.Select(selectedFields)

	if err := query.Scan(&list).Error; err != nil {
		return nil, err
	}

	return list, nil
}

func (r repo) GetPromotionWebUserConfirmByActionKey(actionKey string) (*int64, error) {

	var record model.RaceAction
	// GET //
	selectedFields := "id"
	query := r.db.Table("promotion_web_user_confirm as action")
	query = query.Select(selectedFields)
	if err := query.
		Where("action.action_key = ?", actionKey).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record.Id, nil
}

func (r repo) GetPromotionWebUserConfirmByPromotionWebUserId(PromotionWebUserIds []int64) ([]model.PromotionWebUserConfirm, error) {

	var list []model.PromotionWebUserConfirm

	selectedFields := "promotion_web_user_confirm.id AS id, promotion_web_user_confirm.action_key AS action_key, promotion_web_user_confirm.promotion_web_id AS promotion_web_id"
	selectedFields += ", promotion_web_user_confirm.user_id AS user_id, promotion_web_user_confirm.promotion_web_user_id AS promotion_web_user_id"
	selectedFields += ", promotion_web_user_confirm.created_at AS created_at, promotion_web_user_confirm.updated_at AS updated_at, promotion_web_user_confirm.deleted_at AS deleted_at"
	query := r.db.Table("promotion_web_user_confirm")
	query = query.Select(selectedFields)
	query = query.Where("promotion_web_user_id IN ?", PromotionWebUserIds)

	if err := query.Scan(&list).Error; err != nil {
		return nil, err
	}

	return list, nil
}

func (r repo) UpdatePromotionWebUserConfirmById(body model.PromotionWebUserConfirmUpdateRequest) error {

	query := r.db.Table("promotion_web_user_confirm")
	query = query.Where("id = ?", body.Id)
	query = query.Updates(&body)
	if err := query.Error; err != nil {
		return err
	}

	return nil
}

func (r repo) CreatePromotionWebUserConfirmByActionKey(body model.PromotionWebUserConfirmCreateRequest) (int64, error) {

	if err := r.db.Table("promotion_web_user_confirm").Create(&body).Error; err != nil {
		return 0, err
	}
	return body.Id, nil
}

func (r repo) DeletePromotionWebUserConfirmByActionKey(actionKey string) error {

	query := r.db.Table("promotion_web_user_confirm")
	query = query.Where("action_key = ?", actionKey)
	query = query.Delete(&model.PromotionWebUserConfirm{})
	if err := query.Error; err != nil {
		return err
	}
	return nil
}

func (r repo) PromotionErrorLog(name string, req interface{}, result interface{}) error {

	var createLog model.BankTransactionLogCreate
	createLog.Status = "ERROR"
	createLog.JsonRequest = helper.StructJson(req)
	createLog.JsonPayload = helper.StructJson(result)
	createLog.LogType = name
	if err := r.db.Table("promotion_web_user_log").Create(&createLog).Error; err != nil {
		return err
	}
	return nil
}
func (r repo) PromotionSuccessLog(name string, req interface{}, result interface{}) error {

	var createLog model.BankTransactionLogCreate
	createLog.Status = "SUCCESS"
	createLog.JsonRequest = helper.StructJson(req)
	createLog.JsonPayload = helper.StructJson(result)
	createLog.LogType = name
	if err := r.db.Table("promotion_web_user_log").Create(&createLog).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) ShowPromotionWebForUser() ([]model.ShowPromotionWebForUserResponse, error) {

	showListLowerThanEndDate := time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02")

	var result []model.ShowPromotionWebForUserResponse
	selectedFields := "promotion_web.id AS id, promotion_web.promotion_web_type_id AS promotion_web_type_id"
	selectedFields += ", promotion_web_type.label_th AS promotion_web_type_th, promotion_web_type.label_en AS promotion_web_type_en, promotion_web.condition_detail AS condition_detail, promotion_web.image_url AS image_url"
	selectedFields += ", promotion_web.name AS name, promotion_web.short_description AS short_description, promotion_web.description AS description"
	selectedFields += ", promotion_web.promotion_web_date_type_id AS promotion_web_date_type_id"
	selectedFields += ", promotion_web_date_type.label_th AS promotion_web_date_type_th, promotion_web_date_type.label_en AS promotion_web_date_type_en"
	selectedFields += ", promotion_web.start_date AS start_date, promotion_web.end_date AS end_date"
	selectedFields += ", promotion_web.monday AS monday, promotion_web.tuesday AS tuesday, promotion_web.wednesday AS wednesday, promotion_web.thursday AS thursday"
	selectedFields += ", promotion_web.friday AS friday, promotion_web.saturday AS saturday"
	selectedFields += ", promotion_web.sunday AS sunday, promotion_web.time_start AS time_start, promotion_web.time_end AS time_end"
	selectedFields += ", promotion_web_status_id AS promotion_web_status_id"

	query := r.db.Table("promotion_web")
	query = query.Joins("LEFT JOIN promotion_web_type ON promotion_web.promotion_web_type_id = promotion_web_type.id")
	query = query.Joins("LEFT JOIN promotion_web_date_type ON promotion_web.promotion_web_date_type_id = promotion_web_date_type.id")

	query = query.Select(selectedFields)
	query = query.Where("promotion_web.promotion_web_status_id IN ?", []int64{model.PROMOTION_WEB_STATUS_ACTIVE, model.PROMOTION_WEB_STATUS_ONLY_SHOW, model.PROMOTION_WEB_STATUS_ONLY_URL})
	// ถ้าไหนดี

	query = query.Where("promotion_web.start_date <= ? OR promotion_web.start_date IS NULL", showListLowerThanEndDate)

	query = query.Where("promotion_web.end_date >= ? OR promotion_web.end_date IS NULL", showListLowerThanEndDate)

	query = query.Where("promotion_web.deleted_at IS NULL")

	query = query.Order("promotion_web.priority_order ASC")

	if err := query.Scan(&result).Error; err != nil {
		return nil, err
	}

	return result, nil
}

func (r repo) ShowPromotionWebForPublic() ([]model.ShowPromotionWebForUserResponse, error) {

	showListLowerThanEndDate := time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02")

	var result []model.ShowPromotionWebForUserResponse
	selectedFields := "promotion_web.id AS id, promotion_web.promotion_web_type_id AS promotion_web_type_id"
	selectedFields += ", promotion_web_type.label_th AS promotion_web_type_th, promotion_web_type.label_en AS promotion_web_type_en, promotion_web.condition_detail AS condition_detail, promotion_web.image_url AS image_url"
	selectedFields += ", promotion_web.name AS name, promotion_web.short_description AS short_description, promotion_web.description AS description"
	selectedFields += ", promotion_web.promotion_web_date_type_id AS promotion_web_date_type_id"
	selectedFields += ", promotion_web_date_type.label_th AS promotion_web_date_type_th, promotion_web_date_type.label_en AS promotion_web_date_type_en"
	selectedFields += ", promotion_web.start_date AS start_date, promotion_web.end_date AS end_date"
	selectedFields += ", promotion_web.monday AS monday, promotion_web.tuesday AS tuesday, promotion_web.wednesday AS wednesday, promotion_web.thursday AS thursday"
	selectedFields += ", promotion_web.friday AS friday, promotion_web.saturday AS saturday"
	selectedFields += ", promotion_web.sunday AS sunday, promotion_web.time_start AS time_start, promotion_web.time_end AS time_end"
	selectedFields += ", promotion_web_status_id AS promotion_web_status_id"

	query := r.db.Table("promotion_web")
	query = query.Joins("LEFT JOIN promotion_web_type ON promotion_web.promotion_web_type_id = promotion_web_type.id")
	query = query.Joins("LEFT JOIN promotion_web_date_type ON promotion_web.promotion_web_date_type_id = promotion_web_date_type.id")

	query = query.Select(selectedFields)
	query = query.Where("promotion_web.promotion_web_status_id IN ?", []int64{model.PROMOTION_WEB_STATUS_ACTIVE, model.PROMOTION_WEB_STATUS_ONLY_SHOW})
	// ถ้าไหนดี

	query = query.Where("promotion_web.start_date <= ? OR promotion_web.start_date IS NULL", showListLowerThanEndDate)

	query = query.Where("promotion_web.end_date >= ? OR promotion_web.end_date IS NULL", showListLowerThanEndDate)

	query = query.Where("promotion_web.deleted_at IS NULL")

	query = query.Order("promotion_web.priority_order ASC")

	if err := query.Scan(&result).Error; err != nil {
		return nil, err
	}

	return result, nil
}

func (r repo) ShowPromotionWebForUserHiddenUrl(hiddenUrlLink string) ([]model.ShowPromotionWebForUserResponse, error) {

	// showListLowerThanEndDate := time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02")

	var result []model.ShowPromotionWebForUserResponse
	selectedFields := "promotion_web.id AS id, promotion_web.promotion_web_type_id AS promotion_web_type_id"
	selectedFields += ", promotion_web_type.label_th AS promotion_web_type_th, promotion_web_type.label_en AS promotion_web_type_en, promotion_web.condition_detail AS condition_detail, promotion_web.image_url AS image_url"
	selectedFields += ", promotion_web.name AS name, promotion_web.short_description AS short_description, promotion_web.description AS description"
	selectedFields += ", promotion_web.promotion_web_date_type_id AS promotion_web_date_type_id"
	selectedFields += ", promotion_web_date_type.label_th AS promotion_web_date_type_th, promotion_web_date_type.label_en AS promotion_web_date_type_en"
	selectedFields += ", promotion_web.start_date AS start_date, promotion_web.end_date AS end_date"
	selectedFields += ", promotion_web.monday AS monday, promotion_web.tuesday AS tuesday, promotion_web.wednesday AS wednesday, promotion_web.thursday AS thursday"
	selectedFields += ", promotion_web.friday AS friday, promotion_web.saturday AS saturday"
	selectedFields += ", promotion_web.sunday AS sunday, promotion_web.time_start AS time_start, promotion_web.time_end AS time_end"
	selectedFields += ", promotion_web_status_id AS promotion_web_status_id"
	selectedFields += ", promotion_web.hidden_url_link AS hidden_url_link"
	query := r.db.Table("promotion_web")
	query = query.Joins("LEFT JOIN promotion_web_type ON promotion_web.promotion_web_type_id = promotion_web_type.id")
	query = query.Joins("LEFT JOIN promotion_web_date_type ON promotion_web.promotion_web_date_type_id = promotion_web_date_type.id")

	query = query.Select(selectedFields)

	// query = query.Where("promotion_web.start_date <= ? OR promotion_web.start_date IS NULL", showListLowerThanEndDate)

	// query = query.Where("promotion_web.end_date >= ? OR promotion_web.end_date IS NULL", showListLowerThanEndDate)

	query = query.Where("promotion_web.hidden_url_link = ?", hiddenUrlLink)

	query = query.Where("promotion_web.deleted_at IS NULL")

	query = query.Order("promotion_web.priority_order ASC")

	if err := query.Scan(&result).Error; err != nil {
		return nil, err
	}

	return result, nil
}

func (r repo) GetUserPromotionWebInternalListById(promotionWebId []int64, userId int64) ([]model.PromotionWebUserGetListResponse, error) {

	var list []model.PromotionWebUserGetListResponse

	selectedFields := "promotion_web_user.id AS id, promotion_web_user.promotion_web_id AS promotion_web_id, promotion_web.name AS promotion_name"
	selectedFields += ", promotion_web_user.user_id AS user_id, user.member_code AS member_code, user.fullname AS full_name, user.phone AS phone"
	selectedFields += ", promotion_web_user.promotion_web_user_status_id AS promotion_web_user_status_id, promotion_web_user_status.label_th AS promotion_web_user_status_th"
	selectedFields += ", promotion_web_user.total_amount AS total_amount, promotion_web_user.created_at AS created_at"
	selectedFields += ", promotion_web_user.canceled_by_admin_id AS canceled_by_admin_id, admincancel.fullname AS canceled_by_admin_name"
	selectedFields += ", promotion_web_user.canceled_at AS canceled_at"
	selectedFields += ", promotion_web.hidden_url_link AS hidden_url_link"

	query := r.db.Table("promotion_web_user")
	query = query.Joins("LEFT JOIN promotion_web ON promotion_web_user.promotion_web_id = promotion_web.id")
	query = query.Joins("LEFT JOIN user ON promotion_web_user.user_id = user.id")
	query = query.Joins("LEFT JOIN promotion_web_user_status ON promotion_web_user.promotion_web_user_status_id = promotion_web_user_status.id")
	query = query.Joins("LEFT JOIN admin AS admincancel ON promotion_web_user.canceled_by_admin_id = admincancel.id")
	query = query.Select(selectedFields)
	query = query.Where("promotion_web_user.promotion_web_id IN ?", promotionWebId)
	query = query.Where("promotion_web_user.user_id = ?", userId)
	query = query.Where("promotion_web_user.promotion_web_user_status_id IN ?", []int64{model.PROMOTION_WEB_USER_STATUS_ON_PROCESS, model.PROMOTION_WEB_USER_STATUS_SUCCESS, model.PROMOTION_WEB_USER_STATUS_ON_WITHDRAW})

	if err := query.
		Where("promotion_web_user.deleted_at IS NULL").
		Scan(&list).
		Error; err != nil {
		return nil, err
	}

	return list, nil
}

func (r repo) PromotionConfirmUpdatePromotionWebUser(confirmId int64, promotionWebUserId int64) error {

	query := r.db.Table("promotion_web_user_confirm")
	query = query.Where("id = ?", confirmId)
	query = query.Update("promotion_web_user_id", promotionWebUserId)
	if err := query.Error; err != nil {
		return err
	}

	return nil
}

func (r repo) GetExpiredPromotionWeb(StartCleanUpDate string) ([]model.PromotionWebExpired, error) {

	var list []model.PromotionWebExpired
	selectedFields := "promotion_web.id AS id"
	query := r.db.Table("promotion_web")
	query = query.Select(selectedFields)

	query = query.Where("promotion_web.end_date < ?", StartCleanUpDate)
	query = query.Where("promotion_web.promotion_web_date_type_id = ?", model.PROMOTION_WEB_DATE_TYPE_FIXED_DATE)

	if err := query.Scan(&list).Error; err != nil {
		return nil, err
	}

	return list, nil
}

func (r repo) GetProcessingPromotionWebUserByPromotionWebUserId(PromotionWebUserId int64) (*model.PromotionWebUserByUserIdResponse, error) {

	var result model.PromotionWebUserByUserIdResponse
	selectedFields := "promotion_web_user.id AS id, promotion_web_user.promotion_web_id AS promotion_web_id, promotion_web.name AS promotion_name"
	selectedFields += ", promotion_web_user.user_id AS user_id, user.member_code AS member_code, user.fullname AS full_name, user.phone AS phone"
	selectedFields += ", promotion_web_user.promotion_web_user_status_id AS promotion_web_user_status_id, promotion_web_user_status.label_th AS promotion_web_user_status_th"
	selectedFields += ", promotion_web_user.total_amount AS total_amount, promotion_web_user.created_at AS created_at"

	query := r.db.Table("promotion_web_user")
	query = query.Joins("LEFT JOIN promotion_web ON promotion_web_user.promotion_web_id = promotion_web.id")
	query = query.Joins("LEFT JOIN user ON promotion_web_user.user_id = user.id")
	query = query.Joins("LEFT JOIN promotion_web_user_status ON promotion_web_user.promotion_web_user_status_id = promotion_web_user_status.id")
	query = query.Select(selectedFields)
	query = query.Where("promotion_web_user.id = ?", PromotionWebUserId)
	query = query.Where("promotion_web_user.promotion_web_user_status_id = ?", model.PROMOTION_WEB_USER_STATUS_ON_PROCESS)
	query = query.Where("promotion_web_user.deleted_at IS NULL")
	if err := query.First(&result).Error; err != nil {
		return nil, err
	}

	return &result, nil
}
func (r repo) GetProcessingPromotionWebUserByUserId(userId int64) (*model.PromotionWebUserByUserIdResponse, error) {

	var result model.PromotionWebUserByUserIdResponse
	selectedFields := "promotion_web_user.id AS id, promotion_web_user.promotion_web_id AS promotion_web_id, promotion_web.name AS promotion_name"
	selectedFields += ", promotion_web_user.user_id AS user_id, user.member_code AS member_code, user.fullname AS full_name, user.phone AS phone"
	selectedFields += ", promotion_web_user.promotion_web_user_status_id AS promotion_web_user_status_id, promotion_web_user_status.label_th AS promotion_web_user_status_th"
	selectedFields += ", promotion_web_user.total_amount AS total_amount, promotion_web_user.created_at AS created_at"

	query := r.db.Table("promotion_web_user")
	query = query.Joins("LEFT JOIN promotion_web ON promotion_web_user.promotion_web_id = promotion_web.id")
	query = query.Joins("LEFT JOIN user ON promotion_web_user.user_id = user.id")
	query = query.Joins("LEFT JOIN promotion_web_user_status ON promotion_web_user.promotion_web_user_status_id = promotion_web_user_status.id")
	query = query.Select(selectedFields)
	query = query.Where("promotion_web_user.user_id = ?", userId)
	query = query.Where("promotion_web_user.promotion_web_user_status_id = ?", model.PROMOTION_WEB_USER_STATUS_ON_PROCESS)
	query = query.Where("promotion_web_user.deleted_at IS NULL")
	if err := query.First(&result).Error; err != nil {
		return nil, err
	}

	return &result, nil
}

func (r repo) GetUserFirstDepositForPromotion(req model.GetUserFirstDepositForPromotion) (*model.GetBankTransactionFirstTimeDepositResponse, error) {

	// log.Println("GetUserFirstDepositForPromotion req ------> ", helper.StructJson(req))
	var record model.GetBankTransactionFirstTimeDepositResponse
	var err error

	selectedFields := "bank_transaction.id AS id, bank_transaction.user_id AS user_id, user.member_code AS user_member_code, bank_transaction.credit_amount AS credit_amount, bank_transaction.created_at AS transfer_at"

	query := r.db.Table("bank_transaction")
	query = query.Select(selectedFields)
	query = query.Joins("LEFT JOIN user ON user.id = bank_transaction.user_id")
	query = query.Where("bank_transaction.user_id = ?", req.UserId)

	if req.FromTransferDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.FromTransferDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("bank_transaction.created_at >= ?", startDateAtBkk)
	}

	if req.ToTransferDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.ToTransferDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("bank_transaction.created_at <= ?", endDateAtBkk)
	}

	if err = query.
		Where("bank_transaction.is_first_deposit = 1").
		Where("bank_transaction.transaction_status_id = ?", model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED).
		Where("bank_transaction.transaction_type_id = ?", model.TRANSACTION_TYPE_DEPOSIT).
		Where("bank_transaction.promotion_id = ?", req.PromotionId).
		Where("bank_transaction.deleted_at IS NULL").
		Order("bank_transaction.transfer_at DESC").
		First(&record).
		Error; err != nil {
		return nil, err
	}

	return &record, nil
}

func (r repo) GetUserFirstDepositOfDayForPromotion(req model.GetUserFirstDepositForPromotion) (*model.GetBankTransactionFirstTimeDepositResponse, error) {

	// log.Println("GetUserFirstDepositForPromotion req ------> ", helper.StructJson(req))
	var record model.GetBankTransactionFirstTimeDepositResponse
	var err error

	selectedFields := "bank_transaction.id AS id, bank_transaction.user_id AS user_id, user.member_code AS user_member_code, bank_transaction.credit_amount AS credit_amount, bank_transaction.created_at AS transfer_at"

	query := r.db.Table("bank_transaction")
	query = query.Select(selectedFields)
	query = query.Joins("LEFT JOIN user ON user.id = bank_transaction.user_id")
	query = query.Where("bank_transaction.user_id = ?", req.UserId)

	if req.FromTransferDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.FromTransferDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("bank_transaction.created_at >= ?", startDateAtBkk)
	}

	if req.ToTransferDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.ToTransferDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("bank_transaction.created_at <= ?", endDateAtBkk)
	}

	if err = query.
		Where("bank_transaction.transaction_status_id = ?", model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED).
		Where("bank_transaction.transaction_type_id = ?", model.TRANSACTION_TYPE_DEPOSIT).
		Where("bank_transaction.promotion_id = ?", req.PromotionId).
		Where("bank_transaction.deleted_at IS NULL").
		Order("bank_transaction.transfer_at ASC").
		First(&record).
		Error; err != nil {
		return nil, err
	}

	return &record, nil
}

func (r repo) GetUserAbleWithdrawPertime(req model.GetUserAbleWithdrawPertime) ([]model.GetBankTransactionFirstTimeDepositResponse, error) {

	var list []model.GetBankTransactionFirstTimeDepositResponse
	var err error

	selectedFields := "bank_transaction.id AS id, bank_transaction.user_id AS user_id, user.member_code AS user_member_code, bank_transaction.credit_amount AS credit_amount, bank_transaction.created_at AS transfer_at"

	query := r.db.Table("bank_transaction")
	query = query.Select(selectedFields)
	query = query.Joins("LEFT JOIN user ON user.id = bank_transaction.user_id")
	query = query.Where("bank_transaction.user_id = ?", req.UserId)

	if req.FromTransferDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.FromTransferDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("bank_transaction.created_at >= ?", startDateAtBkk)
	}

	if req.ToTransferDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.ToTransferDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("bank_transaction.created_at <= ?", endDateAtBkk)
	}

	if err = query.
		Where("bank_transaction.transaction_status_id = ?", model.TRANS_STATUS_WITHDRAW_SUCCESS).
		Where("bank_transaction.transaction_type_id = ?", model.TRANSACTION_TYPE_WITHDRAW).
		Where("bank_transaction.deleted_at IS NULL").
		Scan(&list).
		Error; err != nil {
		return nil, err
	}

	return list, nil
}

func (r repo) AgcSimpleWinLoseSingleByMemberCode(data model.AgcSimpleWinloseSingle) (*model.AgcSimpleWinloseResponse, error) {

	// log.Println("AgcSimpleWinLose req ------> ", helper.StructJson(data))

	var result model.AgcSimpleWinloseResponse

	url := fmt.Sprintf("%s/reports/simplewinlose", os.Getenv("TRANSFER_API"))
	res, err := helper.Post(url, data)
	if err != nil {
		log.Println("AgcSimpleWinLose error ------> ", helper.StructJson(err))
		return nil, err
	}

	jsonRes, _ := json.Marshal(res)
	if err := json.Unmarshal(jsonRes, &result); err != nil {
		log.Println("AgcSimpleWinLose resp ------> ", helper.StructJson(res))
		return nil, err
	}

	return &result, nil
}

func (r repo) GetPromotionWebUserById(req model.GetPromotionWebUserById) (*model.GetPromotionWebUserByIdResponse, error) {

	var result model.GetPromotionWebUserByIdResponse
	selectedFields := "promotion_web_user.id AS id, promotion_web_user.promotion_web_id AS promotion_web_id, promotion_web.name AS promotion_name"
	selectedFields += ", promotion_web_user.user_id AS user_id, user.member_code AS member_code, user.fullname AS full_name, user.phone AS phone"
	selectedFields += ", promotion_web_user.promotion_web_user_status_id AS promotion_web_user_status_id, promotion_web_user_status.label_th AS promotion_web_user_status_th"
	selectedFields += ", promotion_web_user.total_amount AS total_amount, promotion_web_user.created_at AS created_at"

	query := r.db.Table("promotion_web_user")
	query = query.Joins("LEFT JOIN promotion_web ON promotion_web_user.promotion_web_id = promotion_web.id")
	query = query.Joins("LEFT JOIN user ON promotion_web_user.user_id = user.id")
	query = query.Joins("LEFT JOIN promotion_web_user_status ON promotion_web_user.promotion_web_user_status_id = promotion_web_user_status.id")
	query = query.Select(selectedFields)
	query = query.Where("promotion_web_user.id = ?", req.Id)
	query = query.Where("promotion_web_user.deleted_at IS NULL")
	if err := query.First(&result).Error; err != nil {
		return nil, err
	}

	return &result, nil
}

func (r repo) FindPromtionConfirmByPromotionWebUserId(promotionWebUserId int64) (*model.PromotionWebUserConfirm, error) {

	var result model.PromotionWebUserConfirm
	query := r.db.Table("promotion_web_user_confirm")
	query = query.Where("promotion_web_user_id = ?", promotionWebUserId)
	query = query.Where("deleted_at IS NULL")
	if err := query.First(&result).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (r repo) UpdatePromotionWebUserStatus(req model.UpdatePromotionWebUserStatus) error {

	updateBody := interface{}(map[string]interface{}{
		"promotion_web_user_status_id": req.PromotionWebUserStatusId,
		"total_amount":                 req.TotalAmount,
		"total_deposit_amount":         req.TotalDepositAmount,
	})

	query := r.db.Table("promotion_web_user")
	query = query.Where("id = ?", req.Id)
	query = query.Where("promotion_web_user_status_id = ?", model.PROMOTION_WEB_USER_STATUS_ON_PROCESS)
	query = query.Updates(updateBody)
	if err := query.Error; err != nil {
		return err
	}

	return nil
}
func (r repo) UpdatePromotionWebUserStatusPassWithDraw(req model.UpdatePromotionWebUserStatus) error {

	updateBody := interface{}(map[string]interface{}{
		"promotion_web_user_status_id": req.PromotionWebUserStatusId,
		"total_amount":                 req.TotalAmount,
		"total_deposit_amount":         req.TotalDepositAmount,
	})

	query := r.db.Table("promotion_web_user")
	query = query.Where("id = ?", req.Id)
	query = query.Where("promotion_web_user_status_id = ?", model.PROMOTION_WEB_USER_STATUS_ON_WITHDRAW)
	query = query.Updates(updateBody)
	if err := query.Error; err != nil {
		return err
	}

	return nil
}

func (r repo) GetDepositCurrentProcessingUserPromotion(userId int64) (*model.PromotionWebUserByUserIdResponse, error) {

	// log.Println("GetDepositCurrentProcessingUserPromotion req ------> ", helper.StructJson(userId))
	var result model.PromotionWebUserByUserIdResponse
	selectedFields := "promotion_web_user.id AS id, promotion_web_user.promotion_web_id AS promotion_web_id, promotion_web.name AS promotion_name"
	selectedFields += ", promotion_web_user.user_id AS user_id, user.member_code AS member_code, user.fullname AS full_name, user.phone AS phone"
	selectedFields += ", promotion_web_user.promotion_web_user_status_id AS promotion_web_user_status_id, promotion_web_user_status.label_th AS promotion_web_user_status_th"
	selectedFields += ", promotion_web_user.total_amount AS total_amount, promotion_web_user.created_at AS created_at"

	query := r.db.Table("promotion_web_user")
	query = query.Joins("LEFT JOIN promotion_web ON promotion_web_user.promotion_web_id = promotion_web.id")
	query = query.Joins("LEFT JOIN user ON promotion_web_user.user_id = user.id")
	query = query.Joins("LEFT JOIN promotion_web_user_status ON promotion_web_user.promotion_web_user_status_id = promotion_web_user_status.id")
	query = query.Select(selectedFields)
	query = query.Where("promotion_web_user.user_id = ?", userId)
	query = query.Where("promotion_web_user.promotion_web_user_status_id = ?", model.PROMOTION_WEB_USER_STATUS_ON_PROCESS)
	query = query.Where("promotion_web_user.deleted_at IS NULL")
	if err := query.First(&result).Error; err != nil {
		return nil, err
	}

	return &result, nil
}
func (r repo) GetWithdrawCurrentProcessingUserPromotion(userId int64) (*model.PromotionWebUserByUserIdResponse, error) {
	var result model.PromotionWebUserByUserIdResponse
	selectedFields := "promotion_web_user.id AS id, promotion_web_user.promotion_web_id AS promotion_web_id, promotion_web.name AS promotion_name"
	selectedFields += ", promotion_web_user.user_id AS user_id, user.member_code AS member_code, user.fullname AS full_name, user.phone AS phone"
	selectedFields += ", promotion_web_user.promotion_web_user_status_id AS promotion_web_user_status_id, promotion_web_user_status.label_th AS promotion_web_user_status_th"
	selectedFields += ", promotion_web_user.total_amount AS total_amount, promotion_web_user.created_at AS created_at"

	query := r.db.Table("promotion_web_user")
	query = query.Joins("LEFT JOIN promotion_web ON promotion_web_user.promotion_web_id = promotion_web.id")
	query = query.Joins("LEFT JOIN user ON promotion_web_user.user_id = user.id")
	query = query.Joins("LEFT JOIN promotion_web_user_status ON promotion_web_user.promotion_web_user_status_id = promotion_web_user_status.id")
	query = query.Select(selectedFields)
	query = query.Where("promotion_web_user.user_id = ?", userId)
	query = query.Where("promotion_web_user.promotion_web_user_status_id = ?", model.PROMOTION_WEB_USER_STATUS_ON_WITHDRAW)
	query = query.Where("promotion_web_user.deleted_at IS NULL")
	if err := query.First(&result).Error; err != nil {
		return nil, err
	}

	return &result, nil
}

func (r repo) UpdatePromotionToBankTransaction(transactionId int64, promotionWebUserId int64) error {

	updateBody := interface{}(map[string]interface{}{
		"promotion_id": promotionWebUserId,
	})

	query := r.db.Table("bank_transaction")
	query = query.Where("id = ?", transactionId)
	query = query.Updates(updateBody)
	if err := query.Error; err != nil {
		return err
	}

	return nil
}

func (r repo) CheckWithdrawPromotionWebByIds(id []int64) ([]model.PromotionWebGetByIdResponse, error) {

	var list []model.PromotionWebGetByIdResponse

	selectedFields := "promotion_web.id AS id, promotion_web.promotion_web_type_id AS promotion_web_type_id, promotion_web_type.label_th AS promotion_web_type_th"
	selectedFields += ", promotion_web.promotion_web_status_id AS promotion_web_status_id, promotion_web_status.label_th AS promotion_web_status_th"
	selectedFields += ", promotion_web.condition_detail AS condition_detail, promotion_web.image_url AS image_url, promotion_web.name AS name"
	selectedFields += ", promotion_web.short_description AS short_description, promotion_web.description AS description, promotion_web.start_date AS start_date"
	selectedFields += ", promotion_web.end_date AS end_date, promotion_web.free_bonus_amount AS free_bonus_amount, promotion_web.privilege_per_day AS privilege_per_day"
	selectedFields += ", promotion_web.able_withdraw_morethan AS able_withdraw_morethan, promotion_web.promotion_web_bonus_condition_id AS promotion_web_bonus_condition_id"
	selectedFields += ", promotion_web_bonus_condition.label_th AS promotion_web_bonus_condition_th, promotion_web_bonus_condition.syntax AS promotion_web_bonus_condition_syntax"
	selectedFields += ", promotion_web.bonus_condition_amount AS bonus_condition_amount, promotion_web.promotion_web_bonus_type_id AS promotion_web_bonus_type_id"
	selectedFields += ", promotion_web_bonus_type.label_th AS promotion_web_bonus_type_th, promotion_web.bonus_type_amount AS bonus_type_amount"
	selectedFields += ", promotion_web.able_withdraw_pertime AS able_withdraw_pertime, promotion_web.promotion_web_turnover_type_id AS promotion_web_turnover_type_id"
	selectedFields += ", promotion_web_turnover_type.label_th AS promotion_web_turnover_type_th, promotion_web.turnover_amount AS turnover_amount"
	selectedFields += ", promotion_web.monday AS monday, promotion_web.tuesday AS tuesday, promotion_web.wednesday AS wednesday"
	selectedFields += ", promotion_web.thursday AS thursday, promotion_web.friday AS friday, promotion_web.saturday AS saturday"
	selectedFields += ", promotion_web.sunday AS sunday, promotion_web.time_start AS time_start, promotion_web.time_end AS time_end"
	selectedFields += ", promotion_web.promotion_web_date_type_id AS promotion_web_date_type_id, promotion_web_date_type.label_th AS promotion_web_date_type_th"
	selectedFields += ", promotion_web.bonus_type_amount_max AS bonus_type_amount_max"

	query := r.db.Table("promotion_web")
	query = query.Select(selectedFields)
	query = query.Joins("LEFT JOIN promotion_web_type ON promotion_web.promotion_web_type_id = promotion_web_type.id")
	query = query.Joins("LEFT JOIN promotion_web_status ON promotion_web.promotion_web_status_id = promotion_web_status.id")
	query = query.Joins("LEFT JOIN promotion_web_bonus_condition ON promotion_web.promotion_web_bonus_condition_id = promotion_web_bonus_condition.id")
	query = query.Joins("LEFT JOIN promotion_web_bonus_type ON promotion_web.promotion_web_bonus_type_id = promotion_web_bonus_type.id")
	query = query.Joins("LEFT JOIN promotion_web_turnover_type ON promotion_web.promotion_web_turnover_type_id = promotion_web_turnover_type.id")
	query = query.Joins("LEFT JOIN promotion_web_date_type ON promotion_web.promotion_web_date_type_id = promotion_web_date_type.id")
	query = query.Where("promotion_web.id IN ?", id)
	query = query.Where("promotion_web.able_withdraw_pertime != 0")
	query = query.Where("promotion_web.deleted_at IS NULL")
	if err := query.Scan(&list).Error; err != nil {
		return nil, err
	}

	return list, nil
}

func (r repo) AutoSuccessPromotionOnWithdrawNextDay(NextDay string, PromotionWebUserIds []int64) ([]model.PromotionWebUserConfirm, error) {

	var list []model.PromotionWebUserConfirm

	selectedFields := "promotion_web_user_confirm.id AS id, promotion_web_user_confirm.action_key AS action_key, promotion_web_user_confirm.promotion_web_id AS promotion_web_id"
	selectedFields += ", promotion_web_user_confirm.user_id AS user_id, promotion_web_user_confirm.promotion_web_user_id AS promotion_web_user_id"
	selectedFields += ", promotion_web_user_confirm.created_at AS created_at, promotion_web_user_confirm.updated_at AS updated_at, promotion_web_user_confirm.deleted_at AS deleted_at"
	query := r.db.Table("promotion_web_user_confirm")
	query = query.Select(selectedFields)
	query = query.Where("promotion_web_user_confirm.promotion_web_user_id IN (?)", PromotionWebUserIds)
	query = query.Where("promotion_web_user_confirm.created_at < ?", NextDay)

	if err := query.Scan(&list).Error; err != nil {
		return nil, err
	}

	return list, nil
}

func (r repo) ExpiredSuccessWithdrawPromotionWebUserByIds(promotionWebUserIds []int64) error {
	successStatus := model.PROMOTION_WEB_USER_STATUS_SUCCESS

	updateBody := interface{}(map[string]interface{}{
		"promotion_web_user_status_id": successStatus,
	})

	query := r.db.Table("promotion_web_user")
	query = query.Where("id IN ?", promotionWebUserIds)
	query = query.Where("promotion_web_user_status_id = ?", model.PROMOTION_WEB_USER_STATUS_ON_WITHDRAW)
	query = query.Updates(updateBody)
	if err := query.Error; err != nil {
		return err
	}

	return nil
}

func (r repo) PromotionWebUserGetListByUserId(req model.PromotionWebUserGetListByUserIdRequest) ([]model.PromotionWebUserGetListByUserIdResponse, int64, error) {

	var list []model.PromotionWebUserGetListByUserIdResponse
	var total int64
	var err error

	selectedFields := "promotion_web_user.id AS id, promotion_web_user.promotion_web_id AS promotion_web_id, promotion_web.name AS promotion_name"
	selectedFields += ", promotion_web_user.user_id AS user_id, user.member_code AS member_code, user.fullname AS full_name, user.phone AS phone"
	selectedFields += ", promotion_web_user.promotion_web_user_status_id AS promotion_web_user_status_id, promotion_web_user_status.label_th AS promotion_web_user_status_th"
	selectedFields += ", promotion_web_user.total_amount AS total_amount, promotion_web_user.created_at AS created_at"
	selectedFields += ", promotion_web_user.canceled_by_admin_id AS canceled_by_admin_id"
	selectedFields += ", CASE WHEN promotion_web_user.canceled_by_admin_id = 0 THEN 'อัตโนมัติ' ELSE admin.fullname END AS canceled_by_admin_name"
	count := r.db.Table("promotion_web_user")
	count = count.Joins("LEFT JOIN promotion_web ON promotion_web_user.promotion_web_id = promotion_web.id")
	count = count.Joins("LEFT JOIN user ON promotion_web_user.user_id = user.id")
	count = count.Joins("LEFT JOIN promotion_web_user_status ON promotion_web_user.promotion_web_user_status_id = promotion_web_user_status.id")
	count = count.Joins("LEFT JOIN admin ON promotion_web_user.canceled_by_admin_id = admin.id")
	count = count.Select("promotion_web_user.id")
	count = count.Where("promotion_web_user.user_id = ?", req.UserId)
	count = count.Where("promotion_web_user.deleted_at IS NULL")

	if req.PromotionWebUserStatusId != nil {
		count = count.Where("promotion_web_user.promotion_web_user_status_id = ?", req.PromotionWebUserStatusId)
	}

	if req.OfDate != "" {
		// OfDate is Primary
		startDateAtBkk, err := r.ParseBodBkk(req.OfDate)
		if err != nil {
			return nil, total, err
		}
		count = count.Where("promotion_web_user.created_at >= ? ", startDateAtBkk)
		endDateAtBkk, err := r.ParseEodBkk(req.OfDate)
		if err != nil {
			return nil, total, err
		}
		count = count.Where("promotion_web_user.created_at <=  ?", endDateAtBkk)

	} else {
		action := time.Now().UTC()
		bbkLoc := time.FixedZone("Asia/Bangkok", 7*60*60)
		filterTime := action.In(bbkLoc)
		if req.DateType == "today" {
			req.FromDate = filterTime.Format("2006-01-02")
			req.ToDate = filterTime.Format("2006-01-02")
		} else if req.DateType == "yesterday" {
			req.FromDate = filterTime.AddDate(0, 0, -1).Format("2006-01-02")
			req.ToDate = filterTime.AddDate(0, 0, -1).Format("2006-01-02")
		} else if req.DateType == "this_month" {
			// full of this month
			req.FromDate = filterTime.Format("2006-01") + "-01"
			req.ToDate = filterTime.AddDate(0, 1, -1).Format("2006-01-02")

		}

		if req.FromDate != "" {
			startDateAtBkk, err := r.ParseBodBkk(req.FromDate)
			if err != nil {
				return nil, total, err
			}
			count = count.Where("promotion_web_user.created_at >= ? ", startDateAtBkk)

		}

		if req.ToDate != "" {
			endDateAtBkk, err := r.ParseEodBkk(req.ToDate)
			if err != nil {
				return nil, total, err
			}
			count = count.Where("promotion_web_user.created_at <=  ?", endDateAtBkk)
		}

	}

	if req.Search != "" {
		count = count.Where("promotion_web.name LIKE ? OR user.member_code LIKE ? OR user.fullname LIKE ? OR user.phone LIKE ?", "%"+req.Search+"%", "%"+req.Search+"%", "%"+req.Search+"%", "%"+req.Search+"%")
	}

	if err := count.Count(&total).Error; err != nil {
		return nil, total, err
	}

	if total > 0 {

		query := r.db.Table("promotion_web_user")
		query = query.Joins("LEFT JOIN promotion_web ON promotion_web_user.promotion_web_id = promotion_web.id")
		query = query.Joins("LEFT JOIN user ON promotion_web_user.user_id = user.id")
		query = query.Joins("LEFT JOIN promotion_web_user_status ON promotion_web_user.promotion_web_user_status_id = promotion_web_user_status.id")
		query = query.Joins("LEFT JOIN admin ON promotion_web_user.canceled_by_admin_id = admin.id")
		query = query.Select(selectedFields)
		query = query.Where("promotion_web_user.user_id = ?", req.UserId)
		query = query.Where("promotion_web_user.deleted_at IS NULL")

		if req.PromotionWebUserStatusId != nil {
			query = query.Where("promotion_web_user.promotion_web_user_status_id = ?", req.PromotionWebUserStatusId)
		}

		if req.OfDate != "" {
			// OfDate is Primary
			startDateAtBkk, err := r.ParseBodBkk(req.OfDate)
			if err != nil {
				return nil, total, err
			}
			query = query.Where("promotion_web_user.created_at >= ? ", startDateAtBkk)
			endDateAtBkk, err := r.ParseEodBkk(req.OfDate)
			if err != nil {
				return nil, total, err
			}
			query = query.Where("promotion_web_user.created_at <=  ?", endDateAtBkk)

		} else {
			action := time.Now().UTC()
			bbkLoc := time.FixedZone("Asia/Bangkok", 7*60*60)
			filterTime := action.In(bbkLoc)
			if req.DateType == "today" {
				req.FromDate = filterTime.Format("2006-01-02")
				req.ToDate = filterTime.Format("2006-01-02")
			} else if req.DateType == "yesterday" {
				req.FromDate = filterTime.AddDate(0, 0, -1).Format("2006-01-02")
				req.ToDate = filterTime.AddDate(0, 0, -1).Format("2006-01-02")
			} else if req.DateType == "this_month" {
				// full of this month
				req.FromDate = filterTime.Format("2006-01") + "-01"
				req.ToDate = filterTime.AddDate(0, 1, -1).Format("2006-01-02")

			}

			if req.FromDate != "" {
				startDateAtBkk, err := r.ParseBodBkk(req.FromDate)
				if err != nil {
					return nil, total, err
				}
				query = query.Where("promotion_web_user.created_at >= ? ", startDateAtBkk)

			}

			if req.ToDate != "" {
				endDateAtBkk, err := r.ParseEodBkk(req.ToDate)
				if err != nil {
					return nil, total, err
				}
				query = query.Where("promotion_web_user.created_at <=  ?", endDateAtBkk)
			}

		}

		if req.Search != "" {
			query = query.Where("promotion_web.name LIKE ? OR user.member_code LIKE ? OR user.fullname LIKE ? OR user.phone LIKE ?", "%"+req.Search+"%", "%"+req.Search+"%", "%"+req.Search+"%", "%"+req.Search+"%")
		}

		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)

		}

		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}

		if err = query.
			Where("promotion_web_user.deleted_at IS NULL").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}

	return list, total, err
}

func (r repo) GetDepositMinimumPerDayForPromotion(req model.GetUserDepositWhileInPromotion) ([]model.GetBankTransactionFirstTimeDepositResponse, error) {

	// log.Println("GetUserFirstDepositForPromotion req ------> ", helper.StructJson(req))

	var list []model.GetBankTransactionFirstTimeDepositResponse
	var err error

	selectedFields := "bank_transaction.id AS id, bank_transaction.user_id AS user_id, user.member_code AS user_member_code, bank_transaction.credit_amount AS credit_amount, bank_transaction.created_at AS transfer_at"

	query := r.db.Table("bank_transaction")
	query = query.Select(selectedFields)
	query = query.Joins("LEFT JOIN user ON user.id = bank_transaction.user_id")
	query = query.Where("bank_transaction.user_id = ?", req.UserId)

	if req.FromTransferDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.FromTransferDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("bank_transaction.created_at >= ?", startDateAtBkk)
	}

	if req.ToTransferDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.ToTransferDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("bank_transaction.created_at <= ?", endDateAtBkk)
	}

	if err = query.
		Where("bank_transaction.transaction_status_id = ?", model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED).
		Where("bank_transaction.transaction_type_id = ?", model.TRANSACTION_TYPE_DEPOSIT).
		Where("bank_transaction.promotion_id = ?", req.PromotionId).
		Where("bank_transaction.deleted_at IS NULL").
		Order("bank_transaction.transfer_at DESC").
		Scan(&list).
		Error; err != nil {
		return nil, err
	}

	return list, nil
}
func (r repo) GetDepositPerDayForPromotion(req model.GetUserDepositWhileInPromotion) ([]model.GetBankTransactionFirstTimeDepositResponse, error) {

	// log.Println("GetUserFirstDepositForPromotion req ------> ", helper.StructJson(req))

	var list []model.GetBankTransactionFirstTimeDepositResponse
	var err error

	selectedFields := "bank_transaction.id AS id, bank_transaction.user_id AS user_id, user.member_code AS user_member_code, bank_transaction.credit_amount AS credit_amount, bank_transaction.created_at AS transfer_at"

	query := r.db.Table("bank_transaction")
	query = query.Select(selectedFields)
	query = query.Joins("LEFT JOIN user ON user.id = bank_transaction.user_id")
	query = query.Where("bank_transaction.user_id = ?", req.UserId)

	if req.FromTransferDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.FromTransferDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("bank_transaction.created_at >= ?", startDateAtBkk)
	}

	if req.ToTransferDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.ToTransferDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("bank_transaction.created_at <= ?", endDateAtBkk)
	}

	if err = query.
		Where("bank_transaction.transaction_status_id = ?", model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED).
		Where("bank_transaction.transaction_type_id = ?", model.TRANSACTION_TYPE_DEPOSIT).
		Where("bank_transaction.promotion_id = ?", req.PromotionId).
		Where("bank_transaction.deleted_at IS NULL").
		Order("bank_transaction.transfer_at DESC").
		Scan(&list).
		Error; err != nil {
		return nil, err
	}

	return list, nil
}

func (r repo) PromotionWebGetSildeListOnlyActive() ([]model.PromotionWebGetSildeListOnlyActive, error) {

	var list []model.PromotionWebGetSildeListOnlyActive
	selectedFields := "promotion_web.id AS id, promotion_web.promotion_web_type_id AS promotion_web_type_id, promotion_web_type.label_th AS promotion_web_type_th"
	selectedFields += ", promotion_web.promotion_web_status_id AS promotion_web_status_id, promotion_web_status.label_th AS promotion_web_status_th"
	selectedFields += ", promotion_web.name AS name, promotion_web.promotion_web_date_type_id AS promotion_web_date_type_id"
	selectedFields += ", promotion_web.start_date AS start_date, promotion_web.end_date AS end_date"
	selectedFields += ", promotion_web.time_start AS time_start, promotion_web.time_end AS time_end"

	query := r.db.Table("promotion_web")
	query = query.Joins("LEFT JOIN promotion_web_type ON promotion_web.promotion_web_type_id = promotion_web_type.id")
	query = query.Joins("LEFT JOIN promotion_web_status ON promotion_web.promotion_web_status_id = promotion_web_status.id")
	query = query.Joins("LEFT JOIN promotion_web_date_type ON promotion_web.promotion_web_date_type_id = promotion_web_date_type.id")
	query = query.Select(selectedFields)
	query = query.Where("promotion_web.promotion_web_status_id IN (?)", []int64{model.PROMOTION_WEB_STATUS_ACTIVE, model.PROMOTION_WEB_STATUS_DISABLE_WEB, model.PROMOTION_WEB_STATUS_ONLY_URL})

	if err := query.
		Where("promotion_web.deleted_at IS NULL").
		Scan(&list).
		Error; err != nil {
		return nil, err
	}

	return list, nil
}

func (r repo) GetDepositForPromotionByTime(req model.GetUserDepositWhileInPromotion) ([]model.GetBankTransactionFirstTimeDepositResponse, error) {

	// log.Println("GetUserFirstDepositForPromotion req ------> ", helper.StructJson(req))
	var list []model.GetBankTransactionFirstTimeDepositResponse
	var err error

	selectedFields := "bank_transaction.id AS id, bank_transaction.user_id AS user_id, user.member_code AS user_member_code, bank_transaction.credit_amount AS credit_amount, bank_transaction.created_at AS transfer_at"

	query := r.db.Table("bank_transaction")
	query = query.Select(selectedFields)
	query = query.Joins("LEFT JOIN user ON user.id = bank_transaction.user_id")
	query = query.Where("bank_transaction.user_id = ?", req.UserId)

	if req.FromTransferDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.FromTransferDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("bank_transaction.created_at >= ?", startDateAtBkk)
	}

	if req.ToTransferDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.ToTransferDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("bank_transaction.created_at <= ?", endDateAtBkk)
	}

	if err = query.
		Where("bank_transaction.transaction_status_id = ?", model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED).
		Where("bank_transaction.transaction_type_id = ?", model.TRANSACTION_TYPE_DEPOSIT).
		Where("bank_transaction.promotion_id = ?", req.PromotionId).
		Where("bank_transaction.deleted_at IS NULL").
		Order("bank_transaction.transfer_at DESC").
		Scan(&list).
		Error; err != nil {
		return nil, err
	}

	return list, nil
}

func (r repo) CheckUserPromotionOnlyNewMemberFree(startRegisterAt string, promotionId *int64) ([]model.PromotionWebGetByIdResponse, error) {

	var result []model.PromotionWebGetByIdResponse

	selectedFields := "promotion_web.id AS id, promotion_web.promotion_web_type_id AS promotion_web_type_id, promotion_web_type.label_th AS promotion_web_type_th"
	selectedFields += ", promotion_web.promotion_web_status_id AS promotion_web_status_id, promotion_web_status.label_th AS promotion_web_status_th"
	selectedFields += ", promotion_web.condition_detail AS condition_detail, promotion_web.image_url AS image_url, promotion_web.name AS name"
	selectedFields += ", promotion_web.short_description AS short_description, promotion_web.description AS description, promotion_web.start_date AS start_date"
	selectedFields += ", promotion_web.end_date AS end_date, promotion_web.free_bonus_amount AS free_bonus_amount, promotion_web.privilege_per_day AS privilege_per_day"
	selectedFields += ", promotion_web.able_withdraw_morethan AS able_withdraw_morethan, promotion_web.promotion_web_bonus_condition_id AS promotion_web_bonus_condition_id"
	selectedFields += ", promotion_web_bonus_condition.label_th AS promotion_web_bonus_condition_th, promotion_web_bonus_condition.syntax AS promotion_web_bonus_condition_syntax"
	selectedFields += ", promotion_web.bonus_condition_amount AS bonus_condition_amount, promotion_web.promotion_web_bonus_type_id AS promotion_web_bonus_type_id"
	selectedFields += ", promotion_web_bonus_type.label_th AS promotion_web_bonus_type_th, promotion_web.bonus_type_amount AS bonus_type_amount"
	selectedFields += ", promotion_web.able_withdraw_pertime AS able_withdraw_pertime, promotion_web.promotion_web_turnover_type_id AS promotion_web_turnover_type_id"
	selectedFields += ", promotion_web_turnover_type.label_th AS promotion_web_turnover_type_th, promotion_web.turnover_amount AS turnover_amount"
	selectedFields += ", promotion_web.monday AS monday, promotion_web.tuesday AS tuesday, promotion_web.wednesday AS wednesday"
	selectedFields += ", promotion_web.thursday AS thursday, promotion_web.friday AS friday, promotion_web.saturday AS saturday"
	selectedFields += ", promotion_web.sunday AS sunday, promotion_web.time_start AS time_start, promotion_web.time_end AS time_end"
	selectedFields += ", promotion_web.promotion_web_date_type_id AS promotion_web_date_type_id, promotion_web_date_type.label_th AS promotion_web_date_type_th"
	selectedFields += ", promotion_web.bonus_type_amount_max AS bonus_type_amount_max"

	query := r.db.Table("promotion_web")
	query = query.Select(selectedFields)
	query = query.Joins("LEFT JOIN promotion_web_type ON promotion_web.promotion_web_type_id = promotion_web_type.id")
	query = query.Joins("LEFT JOIN promotion_web_status ON promotion_web.promotion_web_status_id = promotion_web_status.id")
	query = query.Joins("LEFT JOIN promotion_web_bonus_condition ON promotion_web.promotion_web_bonus_condition_id = promotion_web_bonus_condition.id")
	query = query.Joins("LEFT JOIN promotion_web_bonus_type ON promotion_web.promotion_web_bonus_type_id = promotion_web_bonus_type.id")
	query = query.Joins("LEFT JOIN promotion_web_turnover_type ON promotion_web.promotion_web_turnover_type_id = promotion_web_turnover_type.id")
	query = query.Joins("LEFT JOIN promotion_web_date_type ON promotion_web.promotion_web_date_type_id = promotion_web_date_type.id")
	// query = query.Where("promotion_web.id = ?", id)
	query = query.Where("promotion_web.promotion_web_type_id = ?", model.PROMOTION_WEB_TYPE_NEW_MEMBER_FREE)
	query = query.Where("promotion_web.promotion_web_status_id IN (?) ", []int64{model.PROMOTION_WEB_STATUS_ACTIVE, model.PROMOTION_WEB_STATUS_ONLY_URL})
	query = query.Where("promotion_web.start_date <= ? OR promotion_web.start_date IS NULL", startRegisterAt)
	query = query.Where("promotion_web.end_date >= ? OR promotion_web.end_date IS NULL", startRegisterAt)

	if promotionId != nil {
		query = query.Where("promotion_web.id = ?", promotionId)
	}
	// query = query.Where("promotion_web.start_date >= ? OR promotion_web.start_date IS NULL ", startRegisterAt)
	// query = query.Where("promotion_web.end_date <= ? OR promotion_web.end_date IS NULL", startRegisterAt)

	query = query.Where("promotion_web.deleted_at IS NULL")
	if err := query.Scan(&result).Error; err != nil {
		return nil, err
	}

	return result, nil
}

func (r repo) PromotionWebRegisterMemberCreate(req model.PromotionWebRegisterMemberCreateRequest) (int64, error) {

	query := r.db.Table("promotion_web_register_member")
	query = query.Create(&req)
	if err := query.Error; err != nil {
		return 0, err
	}
	return req.Id, nil
}

func (r repo) PromotionWebRegisterMemberUpdate(req model.PromotionWebRegisterMemberUpdateRequest) error {

	updateBody := interface{}(map[string]interface{}{
		"register_status": req.RegisterStatus,
	})

	query := r.db.Table("promotion_web_register_member")
	query = query.Where("id = ?", req.Id)
	query = query.Updates(updateBody)
	if err := query.Error; err != nil {
		return err
	}

	return nil
}

func (r repo) CountPromotionWebRegisterMemberGetByPromotionId(req model.PromotionWebUserGetListByPromotionWebIdRequest) (*model.PromotionWebRegisterMemberGetTotalResponse, error) {

	var countTotal model.PromotionWebRegisterMemberGetTotalResponse

	query := r.db.Table("promotion_web_register_member")
	query = query.Select("COUNT(promotion_web_register_member.id) as already_register")
	query = query.Where("promotion_web_register_member.promotion_id = ?", req.PromotionWebId)
	// count = count.Where("promotion_web_register_member.register_status = ?", model.PROMOTION_WEB_REGISTER_MEMBER_STATUS_REGISTER_CONFIRM)
	if req.FromToday != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.FromToday)
		if err != nil {
			return nil, err
		}
		endDateAtBkk, err := r.ParseEodBkk(req.FromToday)
		if err != nil {
			return nil, err
		}
		query = query.Where("promotion_web_register_member.register_at BETWEEN ? and ?", startDateAtBkk, endDateAtBkk)
	}

	if err := query.Scan(&countTotal).Error; err != nil {
		return nil, err
	}

	return &countTotal, nil
}

func (r repo) CheckPromotionRegisterCount(promotionId []int64) ([]model.PromotionWebRegisterMemberGetTotal, error) {
	var result []model.PromotionWebRegisterMemberGetTotal
	dateNow := time.Now().UTC().Add(time.Hour * 7).Format("2006-01-02")

	// Create a map to store the promotionId with a default value of 0
	promotionIdMap := make(map[int64]bool)
	for _, id := range promotionId {
		promotionIdMap[id] = false
	}

	selectedFields := "promotion_id, COALESCE(COUNT(promotion_web_register_member.id), 0) as already_register"
	query := r.db.Table("promotion_web_register_member")
	query = query.Select(selectedFields)
	query = query.Where("promotion_web_register_member.promotion_id IN (?)", promotionId)
	query = query.Group("promotion_id")

	startDateAtBkk, err := r.ParseBodBkk(dateNow)
	if err != nil {
		return nil, err
	}
	query = query.Where("promotion_web_register_member.created_at >= ?", startDateAtBkk)

	endDateAtBkk, err := r.ParseEodBkk(dateNow)
	if err != nil {
		return nil, err
	}
	query = query.Where("promotion_web_register_member.created_at <= ?", endDateAtBkk)

	if err := query.Scan(&result).Error; err != nil {
		return nil, err
	}

	// Loop through the result and update the map with the retrieved counts
	for _, res := range result {
		promotionIdMap[res.PromotionId] = true
	}

	// Populate the result slice with the updated counts and set Id
	for id, found := range promotionIdMap {
		if !found {
			result = append(result, model.PromotionWebRegisterMemberGetTotal{
				PromotionId:     id,
				AlreadyRegister: 0,
			})
		}
	}

	return result, nil
}

func (r repo) CheckFirstTimeDepositPromotion(userId int64) (*model.GetBankTransactionFirstTimeDepositResponse, error) {

	// var record model.GetBankTransactionFirstTimeDepositResponse
	// var err error

	// selectedFields := "bank_transaction.id AS id, bank_transaction.user_id AS user_id, user.member_code AS user_member_code, bank_transaction.credit_amount AS credit_amount, bank_transaction.created_at AS transfer_at"

	// query := r.db.Table("bank_transaction")
	// query = query.Select(selectedFields)
	// query = query.Joins("LEFT JOIN user ON user.id = bank_transaction.user_id")
	// query = query.Where("bank_transaction.user_id = ?", userId)

	// if err = query.
	// 	Where("bank_transaction.is_first_deposit = 1").
	// 	Where("bank_transaction.transaction_status_id = ?", model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED).
	// 	Where("bank_transaction.transaction_type_id = ?", model.TRANSACTION_TYPE_DEPOSIT).
	// 	Where("bank_transaction.deleted_at IS NULL").
	// 	Order("bank_transaction.transfer_at DESC").
	// 	First(&record).
	// 	Error; err != nil {
	// 	return nil, err
	// }

	// return &record, nil

	var record model.GetBankTransactionFirstTimeDepositResponse
	var err error

	selectedFields := " user_first_deposit.user_id AS user_id, user.member_code AS user_member_code, user_first_deposit.amount AS credit_amount, user_first_deposit.transfer_at AS transfer_at"

	query := r.db.Table("user_first_deposit")
	query = query.Select(selectedFields)
	query = query.Joins("LEFT JOIN user ON user.id = user_first_deposit.user_id")
	query = query.Where("user_first_deposit.user_id = ?", userId)

	if err = query.
		Where("user_first_deposit.deleted_at IS NULL").
		Order("user_first_deposit.transfer_at DESC").
		First(&record).
		Error; err != nil {
		return nil, err
	}

	return &record, nil

}

func (r repo) CronjobDeletePromotionWebUserLog() error {

	CurrentDate := time.Now().UTC().Add(time.Hour * 7).Format("2006-01-02")
	startDateAtBkk, err := r.ParseBodBkk(CurrentDate)
	if err != nil {
		return err
	}

	if err := r.db.Unscoped().Table("promotion_web_user_log").Where("DATEDIFF(?, created_at) > 30", startDateAtBkk).Delete(&model.PromotionWebUserLog{}).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetUserPromotionReportList(req model.GetUserPromotionReportListRequest) ([]model.PromotionWebUserGetListResponse, int64, error) {

	var list []model.PromotionWebUserGetListResponse
	var total int64

	count := r.db.Table("promotion_web_user")
	count = count.Joins("LEFT JOIN promotion_web ON promotion_web_user.promotion_web_id = promotion_web.id")
	count = count.Joins("LEFT JOIN user ON promotion_web_user.user_id = user.id")
	count = count.Joins("LEFT JOIN promotion_web_user_status ON promotion_web_user.promotion_web_user_status_id = promotion_web_user_status.id")
	count = count.Joins("LEFT JOIN admin AS admincancel ON promotion_web_user.canceled_by_admin_id = admincancel.id")
	count = count.Select("promotion_web_user.id")
	if req.StartDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.StartDate)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("promotion_web_user.created_at >= ? ", startDateAtBkk)
	}
	if req.EndDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.EndDate)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("promotion_web_user.created_at <=  ?", endDateAtBkk)
	}
	if req.Search != "" {
		count = count.Where("promotion_web.name LIKE ? OR user.member_code LIKE ? OR user.fullname LIKE ? OR user.phone LIKE ?", "%"+req.Search+"%", "%"+req.Search+"%", "%"+req.Search+"%", "%"+req.Search+"%")
	}
	if err := count.
		Where("promotion_web_user.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, 0, err
	}

	if total > 0 {
		selectedFields := "promotion_web_user.id AS id, promotion_web_user.promotion_web_id AS promotion_web_id, promotion_web.name AS promotion_name"
		selectedFields += ", promotion_web_user.user_id AS user_id, user.member_code AS member_code, user.fullname AS full_name, user.phone AS phone"
		selectedFields += ", promotion_web_user.promotion_web_user_status_id AS promotion_web_user_status_id, promotion_web_user_status.label_th AS promotion_web_user_status_th"
		selectedFields += ", promotion_web_user.total_amount AS total_amount, promotion_web_user.created_at AS created_at"
		selectedFields += ", promotion_web_user.canceled_by_admin_id AS canceled_by_admin_id"
		selectedFields += ", promotion_web_user.canceled_at AS canceled_at"
		selectedFields += ", CASE WHEN admincancel.id IS NULL THEN 'อัตโนมัติ' ELSE admincancel.fullname END AS canceled_by_admin_name"

		query := r.db.Table("promotion_web_user")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN promotion_web ON promotion_web_user.promotion_web_id = promotion_web.id")
		query = query.Joins("LEFT JOIN user ON promotion_web_user.user_id = user.id")
		query = query.Joins("LEFT JOIN promotion_web_user_status ON promotion_web_user.promotion_web_user_status_id = promotion_web_user_status.id")
		query = query.Joins("LEFT JOIN admin AS admincancel ON promotion_web_user.canceled_by_admin_id = admincancel.id")
		if req.StartDate != "" {
			startDateAtBkk, err := r.ParseBodBkk(req.StartDate)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("promotion_web_user.created_at >= ? ", startDateAtBkk)
		}
		if req.EndDate != "" {
			endDateAtBkk, err := r.ParseEodBkk(req.EndDate)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("promotion_web_user.created_at <=  ?", endDateAtBkk)
		}
		if req.Search != "" {
			query = query.Where("promotion_web.name LIKE ? OR user.member_code LIKE ? OR user.fullname LIKE ? OR user.phone LIKE ?", "%"+req.Search+"%", "%"+req.Search+"%", "%"+req.Search+"%", "%"+req.Search+"%")
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Where("promotion_web_user.deleted_at IS NULL").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, 0, err
		}
	}
	return list, total, nil
}

func (r repo) DeletePromotionWeb(req model.DeletePromotionWebRequest) error {

	// soft delete
	updateBody := interface{}(map[string]interface{}{
		"deleted_by_admin_id": req.DeletedByAdminId,
		"deleted_at":          time.Now().UTC(),
	})

	query := r.db.Table("promotion_web")
	query = query.Where("id = ?", req.Id)
	query = query.Updates(updateBody)
	if err := query.Error; err != nil {
		return err
	}
	return nil
}

func (r repo) OtherPromotionClearWebUserById(userId int64) error {

	successStatus := model.PROMOTION_WEB_USER_STATUS_SUCCESS

	updateBody := interface{}(map[string]interface{}{
		"promotion_web_user_status_id": successStatus,
	})

	query := r.db.Table("promotion_web_user")
	query = query.Where("user_id = ?", userId)
	query = query.Where("promotion_web_user_status_id IN (?)", []int64{model.PROMOTION_WEB_USER_STATUS_ON_WITHDRAW, model.PROMOTION_WEB_USER_STATUS_ON_PROCESS})
	query = query.Updates(updateBody)
	if err := query.Error; err != nil {
		return err
	}

	return nil
}

func (r repo) UpdatePromotionWebPriorityOrderCreate(id int64) error {

	updateBody := interface{}(map[string]interface{}{
		"priority_order": id,
	})

	query := r.db.Table("promotion_web")
	query = query.Where("id = ?", id)
	query = query.Updates(updateBody)
	if err := query.Error; err != nil {
		return err
	}
	return nil
}

func (r repo) SortPromotionWebPriorityOrder(req model.DragSortRequest) error {

	var results []model.PrioritySortResponse

	selectedFields := "sort_rows.id, sort_rows.priority_order"
	query := r.db.Table("promotion_web as sort_rows")
	query = query.Select(selectedFields)
	if err := query.
		Where("sort_rows.id IN ?", []int64{req.FromItemId, req.ToItemId}).
		Limit(2).
		Find(&results).
		Error; err != nil {
		return err
	}

	var fromItem *model.PrioritySortResponse
	var toItem *model.PrioritySortResponse
	for _, result := range results {
		if result.Id == req.FromItemId {
			fromItem = &model.PrioritySortResponse{
				Id:            result.Id,
				PriorityOrder: result.PriorityOrder,
			}
		} else if result.Id == req.ToItemId {

			toItem = &model.PrioritySortResponse{
				Id:            result.Id,
				PriorityOrder: result.PriorityOrder,
			}
		}
	}

	if fromItem != nil && toItem != nil {
		// Sort Direction //
		if fromItem.PriorityOrder < toItem.PriorityOrder {
			// Drag down  //
			whereShiftDown := r.db.Where("priority_order > ?", fromItem.PriorityOrder).Where("priority_order <= ?", toItem.PriorityOrder)
			if err := r.db.Table("promotion_web").Where(whereShiftDown).Update("priority_order", gorm.Expr("priority_order - 1")).Error; err != nil {
				return err
			}
			// Go to
			if err := r.db.Table("promotion_web").Where("id = ?", fromItem.Id).Update("priority_order", toItem.PriorityOrder).Error; err != nil {
				return err
			}
		} else if fromItem.PriorityOrder > toItem.PriorityOrder {
			// Drag up = shift up //
			whereShiftDown := r.db.Where("priority_order < ?", fromItem.PriorityOrder).Where("priority_order >= ?", toItem.PriorityOrder)
			if err := r.db.Table("promotion_web").Where(whereShiftDown).Update("priority_order", gorm.Expr("priority_order + 1")).Error; err != nil {
				return err
			}
			// Go to
			if err := r.db.Table("promotion_web").Where("id = ?", fromItem.Id).Update("priority_order", toItem.PriorityOrder).Error; err != nil {
				return err
			}
		}
	}
	return nil
}

func (r repo) PromotionWebSummary(req model.PromotionWebGetListSummaryRequest) (*model.PromotionWebGetListSummaryResponse, error) {

	var result model.PromotionWebGetListSummaryResponse

	selectedFields := "SUM(promotion_web_user.total_amount) AS total_bonus_amount"
	query := r.db.Table("promotion_web_user")
	query = query.Joins("LEFT JOIN promotion_web ON promotion_web_user.promotion_web_id = promotion_web.id")
	query = query.Select(selectedFields)
	if req.StartDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.StartDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("promotion_web.created_at >= ? ", startDateAtBkk)
	}
	if req.EndDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.EndDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("promotion_web.created_at <=  ?", endDateAtBkk)
	}
	if err := query.
		Where("promotion_web_user.deleted_at IS NULL").
		Take(&result).
		Error; err != nil {
		return nil, err
	}

	return &result, nil
}

func (r repo) PromotionWebUserSummary(req model.PromotionWebUserGetListSummaryRequest) (*model.PromotionWebUserGetListSummaryResponse, error) {

	var result model.PromotionWebUserGetListSummaryResponse

	selectedFields := "SUM(promotion_web_user.total_amount) AS total_bonus_amount"
	selectedFields += ", SUM(promotion_web_user.total_deposit_amount) AS total_deposit_amount"

	query := r.db.Table("promotion_web_user")
	query = query.Joins("LEFT JOIN promotion_web ON promotion_web_user.promotion_web_id = promotion_web.id")
	query = query.Select(selectedFields)
	if req.StartDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.StartDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("promotion_web_user.created_at >= ? ", startDateAtBkk)
	}
	if req.EndDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.EndDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("promotion_web_user.created_at <=  ?", endDateAtBkk)
	}
	if req.PromotionWebId != nil {
		query = query.Where("promotion_web_user.promotion_web_id = ?", req.PromotionWebId)
	}

	if err := query.
		Where("promotion_web_user.deleted_at IS NULL").
		Take(&result).
		Error; err != nil {
		return nil, err
	}

	return &result, nil
}

func (r repo) CheckRegisterMemberByUserIdAndPromotionId(userId int64, startDate string) (bool, error) {

	var result model.PromotionWebRegisterMemberCreateRequest

	query := r.db.Table("user")
	query = query.Where("user.id = ?", userId)
	query = query.Where("member_code IS NULL")
	if startDate != "" {
		// 2025 - 01 - 03
		query = query.Where("user.created_at >= ?", startDate)
	}

	if err := query.
		Select("user.id, user.created_at").
		First(&result).
		Error; err != nil {
		return false, err
	}

	return result.Id > 0, nil
}

func (r repo) LockCreditPromotionCreate(req model.LockCreditPromotionCreateRequest) (int64, error) {

	query := r.db.Table("promotion_web_lock_credit")
	query = query.Create(&req)
	if err := query.Error; err != nil {
		return 0, err
	}
	return req.Id, nil
}

func (r repo) LockCreditPromotionUpdate(adminId int64, req model.LockCreditPromotionUpdateRequest) error {

	updateBody := interface{}(map[string]interface{}{
		"is_locked": false,
	})

	// next time use promotion_web_user_id
	query := r.db.Table("promotion_web_lock_credit")
	query = query.Where("user_id = ?", req.UserId)
	query = query.Where("promotion_id = ?", req.PromotionId)
	query = query.Updates(updateBody)
	if err := query.Error; err != nil {
		return err
	}

	updateBody2 := interface{}(map[string]interface{}{
		"approve_credit_by_admin_id": adminId,
		"approve_credit_at":          time.Now().UTC(),
	})

	query2 := r.db.Table("promotion_web_user")
	query2 = query2.Where("user_id = ?", req.UserId)
	query2 = query2.Where("promotion_web_id = ?", req.PromotionId)
	query2 = query2.Updates(updateBody2)
	if err := query2.Error; err != nil {
		return err
	}

	return nil
}

func (r repo) CheckIsLockedCreditPromotionByUserId(userId int64) (bool, error) {

	var count int64
	query := r.db.Table("promotion_web_lock_credit")
	query = query.Select("COUNT(promotion_web_lock_credit.id)")
	query = query.Where("user_id = ?", userId)

	if err := query.
		Where("is_locked = 1").
		Count(&count).
		Error; err != nil {
		return false, err
	}

	return count > 0, nil
}

func (r repo) GetPromotionWebUserStatusOptions() ([]model.SelectOptions, error) {

	options := []model.SelectOptions{}

	// SELECT //
	selectedFields := "id as id, label_th as label"
	sql := r.db.Table("promotion_web_user_status").Select(selectedFields)
	if err := sql.Scan(&options).Error; err != nil {
		return nil, err
	}
	return options, nil
}

func (r repo) LockCreditWithdrawCreate(req model.LockCreditWithdrawCreateRequest) (int64, error) {

	query := r.db.Table("user_withdraw_lock_credit")
	query = query.Create(&req)
	if err := query.Error; err != nil {
		return 0, err
	}
	return req.Id, nil
}

func (r repo) GetLockCreditWithdrawList(req model.GetLockCreditWithdrawListRequest) ([]model.GetLockCreditWithdrawListResponse, int64, error) {

	var list []model.GetLockCreditWithdrawListResponse
	var total int64

	count := r.db.Table("user_withdraw_lock_credit")
	count = count.Select("user_withdraw_lock_credit.id")
	count = count.Joins("LEFT JOIN user ON user_withdraw_lock_credit.user_id = user.id")

	if req.Search != "" {
		count = count.Where("user_withdraw_lock_credit.detail LIKE ? OR user.member_code LIKE ? OR user.fullname LIKE ? OR user.phone LIKE ?", "%"+req.Search+"%", "%"+req.Search+"%", "%"+req.Search+"%", "%"+req.Search+"%")
	}
	if req.StartDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.StartDate)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("user_withdraw_lock_credit.created_at >= ? ", startDateAtBkk)
	}
	if req.EndDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.EndDate)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("user_withdraw_lock_credit.created_at <=  ?", endDateAtBkk)
	}
	if err := count.
		Count(&total).
		Error; err != nil {
		return nil, 0, err
	}

	if total > 0 {
		selectedFields := "user_withdraw_lock_credit.id AS id, user_withdraw_lock_credit.user_id AS user_id, user_withdraw_lock_credit.ref_id AS ref_id"
		selectedFields += ", user_withdraw_lock_credit.detail AS detail, user_withdraw_lock_credit.user_withdraw_lock_credit_type_id AS user_withdraw_lock_credit_type_id"
		selectedFields += ", user_withdraw_lock_credit_type.label_th AS user_withdraw_lock_credit_type_th, user_withdraw_lock_credit.credit_more_than AS credit_more_than"
		selectedFields += ", user_withdraw_lock_credit.allow_withdraw_amount AS allow_withdraw_amount, user_withdraw_lock_credit.withdraw_amount AS withdraw_amount"
		selectedFields += ", user_withdraw_lock_credit.pull_credit_amount AS pull_credit_amount, user_withdraw_lock_credit.is_locked AS is_locked"
		selectedFields += ", user_withdraw_lock_credit.is_pull_credit AS is_pull_credit, user_withdraw_lock_credit.created_at AS created_at"
		selectedFields += ", user_withdraw_lock_credit.approved_at AS approved_at, user_withdraw_lock_credit.approved_by_id AS approved_by_id"
		selectedFields += ", admin.fullname AS apporved_by_name"
		selectedFields += ", user.id as user_id, user.member_code as member_code, user.fullname as fullname, user.phone as phone"

		query := r.db.Table("user_withdraw_lock_credit")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN user ON user.id = user_withdraw_lock_credit.user_id")
		query = query.Joins("LEFT JOIN user_withdraw_lock_credit_type ON user_withdraw_lock_credit.user_withdraw_lock_credit_type_id = user_withdraw_lock_credit_type.id")
		query = query.Joins("LEFT JOIN admin ON user_withdraw_lock_credit.approved_by_id = admin.id")

		if req.Search != "" {
			query = query.Where("user_withdraw_lock_credit.detail LIKE ? OR user.member_code LIKE ? OR user.fullname LIKE ? OR user.phone LIKE ?", "%"+req.Search+"%", "%"+req.Search+"%", "%"+req.Search+"%", "%"+req.Search+"%")
		}
		if req.StartDate != "" {
			startDateAtBkk, err := r.ParseBodBkk(req.StartDate)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("user_withdraw_lock_credit.created_at >= ? ", startDateAtBkk)
		}

		if req.EndDate != "" {
			endDateAtBkk, err := r.ParseEodBkk(req.EndDate)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("user_withdraw_lock_credit.created_at <=  ?", endDateAtBkk)
		}

		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}

		if err := query.
			Order("user_withdraw_lock_credit.created_at DESC").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, 0, err
		}

	}

	return list, total, nil
}

func (r repo) UpdateLockCreditWithdraw(req model.UpdateLockCreditWithdrawRequest) error {

	query := r.db.Table("user_withdraw_lock_credit")
	query = query.Where("id = ?", req.Id)
	query = query.Updates(req)
	if err := query.Error; err != nil {
		return err
	}

	return nil
}

func (r repo) CheckLockCreditWithdrawByUserId(userId int64) ([]model.CheckLockCreditWithdrawByUserId, error) {

	var result []model.CheckLockCreditWithdrawByUserId

	selectedFields := "user_withdraw_lock_credit.id AS id, user_withdraw_lock_credit.user_id AS user_id, user_withdraw_lock_credit.ref_id AS ref_id"
	selectedFields += ", user_withdraw_lock_credit.detail AS detail, user_withdraw_lock_credit.user_withdraw_lock_credit_type_id AS user_withdraw_lock_credit_type_id"
	selectedFields += ", user_withdraw_lock_credit.credit_more_than AS credit_more_than, user_withdraw_lock_credit.allow_withdraw_amount AS allow_withdraw_amount"
	selectedFields += ", user_withdraw_lock_credit.is_locked AS is_locked, user_withdraw_lock_credit.is_pull_credit AS is_pull_credit"

	query := r.db.Table("user_withdraw_lock_credit")
	query = query.Select(selectedFields)
	query = query.Where("user_withdraw_lock_credit.user_id = ?", userId)
	query = query.Where("user_withdraw_lock_credit.is_locked = ?", true)

	if err := query.
		Scan(&result).
		Error; err != nil {
		return nil, err
	}

	return result, nil
}

func (r repo) CheckAlreadyUserTypePromotionByUserId(userId int64, promotionWebTypeId int64) (bool, error) {

	todayDay := time.Now().UTC().Add(time.Hour * 7).Format("2006-01-02")

	startDateAtBkk, err := r.ParseBodBkk(todayDay)
	if err != nil {
		return false, err
	}

	endDateAtBkk, err := r.ParseEodBkk(todayDay)
	if err != nil {
		return false, err
	}

	var result model.PromotionWebUserGetListResponse

	selectedFields := "promotion_web_user.id"

	query := r.db.Table("promotion_web_user")
	query = query.Select(selectedFields)
	query = query.Joins("LEFT JOIN promotion_web ON promotion_web_user.promotion_web_id = promotion_web.id")
	query = query.Where("promotion_web_user.user_id = ?", userId)
	query = query.Where("promotion_web.promotion_web_type_id = ?", promotionWebTypeId)
	query = query.Where("promotion_web_user.created_at >= ?", startDateAtBkk)
	query = query.Where("promotion_web_user.created_at <= ?", endDateAtBkk)

	if err := query.
		Take(&result).
		Error; err != nil {
		log.Println("CheckAlreadyUserTypePromotionByUserId err -----> ", err)
		return false, nil
	}

	return result.Id > 0, nil
}

func (r repo) CheckAlreadyLockCreditWithdrawByUserId(userId int64) (bool, error) {

	var result model.CheckLockCreditWithdrawByUserId

	selectedFields := "user_withdraw_lock_credit.id AS id"
	query := r.db.Table("user_withdraw_lock_credit")
	query = query.Select(selectedFields)
	query = query.Where("user_withdraw_lock_credit.user_id = ?", userId)
	query = query.Where("user_withdraw_lock_credit.is_locked = ?", true)

	if err := query.
		Take(&result).
		Error; err != nil {
		log.Println("CheckAlreadyLockCreditWithdrawByUserId err -----> ", err)
		return false, nil
	}

	return result.Id > 0, nil
}

func (r repo) CheckTurnoverStatementByUserIdAndTypeId(userId int64) (bool, error) {
	var result model.TurnoverUserStatementResponse

	var typeIds = []int64{model.TURN_PROMOTION_SETTING_PLAY_ALL, model.TURN_PROMOTION_SETTING_PLAY_GAME, model.TURN_PROMOTION_SETTING_PLAY_SPORT, model.TURN_PROMOTION_SETTING_PLAY_CASINO, model.TURN_PROMOTION_SETTING_PLAY_P2P, model.TURN_PROMOTION_SETTING_PLAY_LOTTERY, model.TURN_PROMOTION_SETTING_PLAY_FINANCIAL}

	selectedFields := "id"

	query := r.db.Table("turnover_statement")
	query = query.Select(selectedFields)
	query = query.Where("user_id = ?", userId)
	query = query.Where("type_id IN (?)", typeIds)
	query = query.Where("status_id = ?", model.TURNOVER_STATEMENT_STATUS_PENDING)
	query = query.Where("start_turn_amount > 0")

	if err := query.
		Take(&result).
		Error; err != nil {
		log.Println("CheckTurnoverStatementByUserIdAndTypeId err -----> ", err)
		return false, nil
	}

	return result.Id > 0, nil
}

func (r repo) GetAllWithdrawProcessPromotionWebUser() ([]model.GetAllWithdrawProcessPromotionWebUser, error) {

	var result []model.GetAllWithdrawProcessPromotionWebUser

	selectedFields := "promotion_web_user.id AS id"
	selectedFields += ", promotion_web_user.promotion_web_id AS promotion_web_id"
	selectedFields += ", promotion_web.promotion_web_type_id AS promotion_web_type_id"
	selectedFields += ", promotion_web_user.user_id AS user_id"
	selectedFields += ", promotion_web_user.promotion_web_user_status_id AS promotion_web_user_status_id"
	selectedFields += ", promotion_web.able_withdraw_morethan AS able_withdraw_morethan"
	selectedFields += ", promotion_web.able_withdraw_pertime AS able_withdraw_pertime"
	selectedFields += ", promotion_web.name AS promotion_web_name"
	query := r.db.Table("promotion_web_user")
	query = query.Select(selectedFields)
	query = query.Joins("LEFT JOIN promotion_web ON promotion_web_user.promotion_web_id = promotion_web.id")
	query = query.Joins("LEFT JOIN promotion_web_type ON promotion_web.promotion_web_type_id = promotion_web_type.id")
	query = query.Where("promotion_web_user.promotion_web_user_status_id = ?", model.PROMOTION_WEB_USER_STATUS_ON_WITHDRAW)

	if err := query.
		Find(&result).
		Error; err != nil {
		return nil, err
	}

	return result, nil
}

func (r repo) GetAllLockCreditWithdrawByRefId(refId []int64) ([]model.GetAllLockCreditWithdrawByRefId, error) {

	var result []model.GetAllLockCreditWithdrawByRefId

	selectedFields := "user_withdraw_lock_credit.ref_id AS ref_id"

	query := r.db.Table("user_withdraw_lock_credit")
	query = query.Select(selectedFields)
	query = query.Where("user_withdraw_lock_credit.ref_id IN (?)", refId)

	if err := query.
		Scan(&result).
		Error; err != nil {
		return nil, err
	}

	return result, nil
}
