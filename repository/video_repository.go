package repository

import (
	"cybergame-api/model"

	"gorm.io/gorm"
)

func NewVideoRepository(db *gorm.DB) VideoRepository {
	return &repo{db}
}

type VideoRepository interface {
	GetVideoById(videoId int64) (*model.VideoDetail, error)
	GetVideoList(query model.VideoQuery) ([]model.VideoDetail, int, error)
	CreateVideo(video model.VideoBody) error
	UpdateVideo(videoId int64, body model.VideoUpdateBody) error
	DeleteVideo(videoId int64) error
}

func (r repo) GetVideoById(videoId int64) (*model.VideoDetail, error) {

	var result model.VideoDetail

	if err := r.db.Table("video").
		Select("id, title, cover_url, youtube_url, created_at").
		Where("id = ?", videoId).
		Take(&result).
		Error; err != nil {
		return nil, err
	}

	return &result, nil
}

func (r repo) GetVideoList(query model.VideoQuery) ([]model.VideoDetail, int, error) {

	var total int64
	var result []model.VideoDetail

	queryTotal := r.db.Table("video")

	if query.StartAt != "" && query.EndAt != "" {
		queryTotal = queryTotal.Where("created_at BETWEEN ? AND ?", query.StartAt, query.EndAt)
	}

	if err := queryTotal.
		Select("id").
		Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if total == 0 {
		return nil, 0, nil
	}

	queryData := r.db.Table("video")

	if query.StartAt != "" && query.EndAt != "" {
		queryData = queryData.Where("created_at BETWEEN ? AND ?", query.StartAt, query.EndAt)
	}

	if err := queryData.
		Select("id, title, cover_url, youtube_url, created_at").
		Offset(query.Page * query.Limit).
		Limit(query.Limit).
		Order("created_at DESC").
		Scan(&result).
		Error; err != nil {
		return nil, 0, err
	}

	return result, int(total), nil
}

func (r repo) CreateVideo(video model.VideoBody) error {

	if err := r.db.Table("video").Create(&video).Error; err != nil {
		return err
	}

	return nil
}

func (r repo) UpdateVideo(videoId int64, body model.VideoUpdateBody) error {

	update := map[string]interface{}{
		"title":       body.Title,
		"cover_url":   body.CoverUrl,
		"youtube_url": body.YoutubeUrl,
	}

	if err := r.db.Table("video").Where("id = ?", videoId).Updates(update).Error; err != nil {
		return err
	}

	return nil
}

func (r repo) DeleteVideo(videoId int64) error {

	if err := r.db.Table("video").Where("id = ?", videoId).Delete(&model.Video{}).Error; err != nil {
		return err
	}

	return nil
}
