package repository

import (
	"bytes"
	"crypto/sha256"
	"cybergame-api/helper"
	"cybergame-api/model"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strings"
	"time"

	"gorm.io/gorm"
)

func NewPompayRepository(db *gorm.DB) PompayRepository {

	// LOG : [2024-09-30]
	// File : Pompay_Payment_Gateway_Integration_Guide_v2.6_1.pdf

	// 1.REQUIERMENT
	// 1.1 clientId = Merchant’s unique ID, refer to Reseller profile Client ID field
	// 1.2 clientSecret = from https://pompay.asia/reseller/profile

	// 2.FUNCTION
	// 2.1 Deposit = 1.5 Payment Request via API
	// 2.2 Withdraw = todo

	// Latest RD
	// 1. Sorry, some error has occurred  (Error - E4012) . Please try again later.

	return &repo{db}
}

type PompayRepository interface {
	GetDb() *gorm.DB
	// MERCHANT
	// GetNOTUSEPaygateMerchantById(id int64) (*model.PaygateMerchantNgResponse, error)
	// GetPaygateMerchantList(req model.PaygateMerchantListRequest) ([]model.PaygateMerchantNgResponse, int64, error)
	// CreatePaygateMerchant(body model.PaygateMerchantCreateBody) (*int64, error)
	// UpdatePaygateMerchant(id int64, body model.PaygateMerchantUpdateBody) error
	// DeletePaygateMerchant(id int64) error
	// // SETTING
	// GetPaygateSetting() (*model.PaygateSettingResponse, error)
	// CreatePaygateSetting(body model.PaygateSettingCreateBody) (*int64, error)
	// UpdatePaygateSetting(id int64, body model.PaygateSettingUpdateBody) error
	// DeletePaygateSetting(id int64) error
	// Pompay-DB
	CreatePompayWebhook(body model.PompayWebhookCreateBody) (*int64, error)
	GetDbPompayOrderList(req model.PompayOrderListRequest) ([]model.PompayOrderResponse, int64, error)
	GetDbPompayOrderById(id int64) (*model.PompayOrderResponse, error)
	GetDbPompayOrderByRefId(refId int64) (*model.PompayOrderResponse, error)
	CreateDbPompayOrder(body model.PompayOrderCreateBody) (*int64, error)
	UpdateDbPompayOrderError(id int64, remark string) error
	UpdateDbPompayOrder(id int64, body model.PompayOrderUpdateBody) error
	ApproveDbPompayOrder(id int64, webhookStatus string) error
	UpdatePompayOrderBankSucess(id int64, transId int64, actionBy int64) error
	// Pompay-REMOTE
	GetPompayBankName(code string) string
	PompayDeposit(setting model.PaygateAccountResponse, req model.PompayDepositCreateRemoteRequest) (*model.PompayDepositCreateRemoteResponse, error)
	PompayWithdraw(setting model.PaygateAccountResponse, req model.PompayWithdrawCreateRemoteRequest) (*model.PompayWithdrawCreateRemoteResponse, error)
	// REF-PAYGATE
	GetRawPompayPendingDepositOrderById(id int64) (*model.PompayOrderResponse, error)
	GetPaygateAccountByProviderId(id int64) (*model.PaygateAccountResponse, error)
	// REF-Promotion
	GetDepositCurrentProcessingUserPromotion(userId int64) (*model.PromotionWebUserByUserIdResponse, error)
	// REF-User
	GetMemberById(id int64) (*model.Member, error)
	GetUserBankDetailById(userId int64) (*model.UserBankDetailBody, error)
	// REF-UserTransaction
	IsFirstDeposit(userId int64) bool
	IncreaseUserCredit(body model.UserTransactionCreateRequest) (*model.UserTransactionCreateResponse, error)
	UpdateDepositTransactionStatusFromAgent(transId int64, body model.UserTransactionCreateResponse) error
	UpdateAutoProcessTimer(timer string, id int64) error
	ShowUserTransaction(req model.UserTransactionShowUpdate) error
	UpdateTransactionStatusTransferingToSuccess(id int64, secondUsed string) error
	UpdateUserTransactionStatus(body model.UpdateUserTransactionStatusRequest) error
	// REF-BANKING
	GetLastestBankAdminManualDepositTransaction(userId int64) (*model.BankTransaction, error)
	// REF-BankTransaction
	GetBankTransactionById(id int64) (*model.BankTransaction, error)
	InsertBankTransaction(data model.BankTransactionCreateBody) (*int64, error)
	CreateTransactionAction(data model.CreateBankTransactionActionBody) (*int64, error)
	ConfirmPendingDepositTransaction(id int64, body model.BankDepositTransactionConfirmBody) error
	RollbackTransactionAction(actionId int64) error
	GetUserWithdrawCreditTransactionByRefId(transactionId int64, creditAmount float64) (*model.UserTransaction, error)
	// REF-RC
	GetRaceActionIdByActionKey(actionKey string) (*int64, error)
	CreateRaceCondition(body model.RaceActionCreateBody) (int64, error)
	// REF-SystemLog
	CreateSystemLog(body model.SystemLogCreateBody) (*int64, error)
	CreatePaygateSystemLog(body model.PaygateSystemLogCreateBody) (*int64, error)
	// REF-WebConfiguration
	GetWebConfiguration() (*model.GetWebConfigurationBody, error)

	// [TIER]
	IncreaseUserTierDepositAmount(userId int64, amount float64) error
}

func (r repo) PompaySign(signStr string) string {

	// var sign string

	// 1.2 Payment Request Hashing
	// Payment request’s Hash Value should be generated based on the following fields and sequence:
	// HashKey = clientId + transactionId + custName + custSecondaryName + custBank + custMobile +
	// custEmail + amount + returnUrl + callbackUrl + paymentMethod + bankAcc + clientSecret

	// Hash Key Example
	// UPLTRX1234321johndoealexsecscb**********john@gmail.com50.00https://www.merchant.com/retur
	// nhttps://www.merchant.com/transaction/post123456e3323b4dac596077fbc7198df06c52bf20990eb3
	// Hash Value (SHA256)
	// 0c0fd00628b3fa7870e9c185bd9e2cc40bcc29a2f5d5b333c26fdc5f537435c9

	// TEST
	// testStr := fmt.Sprintf("%v%v%v%v%v%v%v%v%v%v%v%v%v", "UP", "LTRX1234321", "johndoe", "alexsec", "scb", "**********", "<EMAIL>", "50.00", "https://www.merchant.com/return", "https://www.merchant.com/transaction/post", "123456", "e3323b4dac596077fbc7198df06c52bf20990eb3")
	// Deposit
	// signStr := fmt.Sprintf("%v%v%v%v%v%v%v%v%v%v%v%v%v", clientId, transactionId, custName, custSecondaryName, custBank, custMobile, custEmail, amount, returnUrl, callbackUrl, paymentMethod, bankAcc, clientSecret)
	// Withdraw
	// signStr := fmt.Sprintf("%v%v%v%v%v%v%v%v%v%v", clientId, transactionId, custName, custBank, custBankAcc, custMobile, custEmail, amount, callbackUrl, clientSecret)

	hasher := sha256.New()
	if _, err := hasher.Write([]byte(signStr)); err != nil {
		return ""
	}
	signByte := hasher.Sum(nil)

	return hex.EncodeToString(signByte)
}

func (r repo) GetPompayBankName(code string) string {

	// Convert code to uppercase
	code = strings.ToUpper(strings.TrimSpace(code))

	// [********] เว็บ Riverclub
	// หน้าฝากของ  Pompay payment
	// ชื่อธนาคาร ขอเพิ่มเป็นภาษาไทยข้างหลังด้วยค่ะทุกธนาคาร
	// ตัวอย่างเช่น KTB ทำเป็นรูปแบบ => KTB (ธนาคารกรุงไทย)

	// Map bank codes to their corresponding Thai bank names
	switch code {
	case "ANZ":
		return "ธนาคาร ANZ (ANZ)"
	case "BAAC":
		return "ธนาคารเพื่อการเกษตรและสหกรณ์การเกษตร (BAAC)"
	case "BCN":
		return "ธนาคารแห่งประเทศจีน (BCN)"
	case "BKB", "BBL":
		return "ธนาคารกรุงเทพ (BBL)"
	case "BNP":
		return "ธนาคารบีเอ็นพี พารีบาส์ (BNP)"
	case "BOA", "BAY":
		return "ธนาคารกรุงศรีอยุธยา (BAY)"
	case "BUS":
		return "ธนาคารแห่งอเมริกา (BUS)"
	case "CIMB":
		return "ธนาคารซีไอเอ็มบี ไทย (CIMB)"
	case "CITI":
		return "ธนาคารซิตี้แบงก์ (CITI)"
	case "DEUT":
		return "ธนาคารดอยซ์แบงก์ (DEUT)"
	case "GHB":
		return "ธนาคารอาคารสงเคราะห์ (GHB)"
	case "GSB":
		return "ธนาคารออมสิน (GSB)"
	case "HKS":
		return "ธนาคารฮ่องกงและเซี่ยงไฮ้ (HKS)"
	case "ICBC":
		return "ธนาคารไอซีบีซี (ICBC)"
	case "INB":
		return "ธนาคารอินเดียโพ้นทะเล (INB)"
	case "ISB":
		return "ธนาคารอิสลามแห่งประเทศไทย (ISB)"
	case "JPC":
		return "ธนาคารเจพีมอร์แกน เชส (JPC)"
	case "KKB", "KBANK":
		return "ธนาคารกสิกรไทย (KBANK)"
	case "KPB":
		return "ธนาคารเกียรตินาคินภัทร (KPB)"
	case "KTB":
		return "ธนาคารกรุงไทย (KTB)"
	case "LHB", "LH":
		return "ธนาคารแลนด์ แอนด์ เฮ้าส์ (LH)"
	case "MIZB":
		return "ธนาคารมิตซูโฮ มิตซุย (MIZB)"
	case "SCB":
		return "ธนาคารไทยพาณิชย์ (SCB)"
	case "SMB":
		return "ธนาคารซูมิโตโม มิตซุย (SMB)"
	case "STCB", "SCBT":
		return "ธนาคารสแตนดาร์ดชาร์เตอร์ด (STCB)"
	case "THCR", "TCRB":
		return "ธนาคารไทยเครดิตเพื่อรายย่อย (THCR)"
	case "TMB", "TTB":
		return "ธนาคารทหารไทยธนชาต (TTB)"
	case "TISCO":
		return "ธนาคารทิสโก้ (TISCO)"
	case "UOB":
		return "ธนาคารยูโอบี (UOB)"
	default:
		return code // Return the original code if not found in the map
	}
}

func (r repo) PompayDeposit(setting model.PaygateAccountResponse, req model.PompayDepositCreateRemoteRequest) (*model.PompayDepositCreateRemoteResponse, error) {

	// log.Println("PompayDeposit req ------> ")

	// 1.5 Payment Request via API
	// 	To start the payment request, Merchant is required to do a HTML form post to the Payment Gateway
	// (https://pompay.asia/v2/api/payment). The fields (all free text) for the form post are as following:

	// Field Name Mandatory
	// {
	// 	"clientId": "mxn_YAK1",
	// 	"transactionId": "REF123456",
	// 	"custName": "Test User",
	// 	"custSecondaryName": "",
	// 	"custMobile": "",
	// 	"custEmail": "",
	// 	"amount": "4.00",
	// 	"returnUrl": "",
	// 	"callbackUrl": "https://dev-api.cbgame88.com/api/webhook/pompay/callback",
	// 	"paymentMethod": "qr",
	// 	"bankAcc": "**********",
	// 	"custBank": "kkb",
	// 	"hashVal": "b6f75967d166f70086833966695aab72dde42be4f10daf7e2269b848a28eac28"
	// }

	// 1.7 Bank List
	// Below are the supported bank list and their corresponding codes:
	// Code Bank
	// anz ANZ Bank (Thai)
	// baac BAAC
	// bcn Bank of China
	// bkb Bangkok Bank
	// bnp BNP Paribas
	// boa Bank of Ayudhya
	// bus Bank of America
	// cimb CIMB Thai
	// citi Citibank
	// deut Deutsche Bank
	// ghb The Government Housing Bank
	// gsb Government Saving Bank
	// hks The Hong Kong and Shanghai
	// icbc ICBS (Thai)
	// inb Indian Overseas Bank
	// isb Islamic Bank
	// jpc JPMorgan Chase Bank
	// kkb Kasikorn Bank
	// kpb Kiatnakin Phatra Bank
	// ktb Krung Thai Bank
	// lhb Land and Houses Bank
	// mizb Mizuho Bank
	// scb Siam Commercial Bank
	// smb Sumitomo Mitsui Banking
	// stcb Standard Chartered Bank
	// thcr The Thai Credit Retail Bank
	// tmb TMB Thanachart Bank
	// tisco Tisco Bank
	// uob United Overseas Bank
	allowBankCode := map[string]string{}
	allowBankCode["anz"] = "anz"
	allowBankCode["baac"] = "baac" // 8.ธกส
	allowBankCode["bcn"] = "bcn"
	allowBankCode["bbl"] = "bkb" // 3.กรุงเทพ
	allowBankCode["bnp"] = "bnp"
	allowBankCode["bay"] = "boa" // 4.กรุงศรี
	allowBankCode["bus"] = "bus"
	allowBankCode["cimb"] = "cimb" // 13.ซีไอเอ็มบี
	allowBankCode["citi"] = "citi" // 18.ซิตี้แบงก์
	allowBankCode["deut"] = "deut"
	allowBankCode["ghb"] = "ghb" // 10.อาคารสงเคราะห์
	allowBankCode["gsb"] = "gsb" // 7.ออมสิน
	allowBankCode["hks"] = "hks"
	allowBankCode["icbc"] = "icbc" // 15.ไอซีบีซี
	allowBankCode["inb"] = "inb"
	allowBankCode["isb"] = "isb"
	allowBankCode["jpc"] = "jpc"
	allowBankCode["kbank"] = "kkb" // 1.กสิกรไทย
	allowBankCode["kpb"] = "kpb"
	allowBankCode["ktb"] = "ktb" // 5.กรุงไทย
	allowBankCode["lh"] = "lhb"  // 12.แลนด์ แอนด์ เฮ้าส์
	allowBankCode["mizb"] = "mizb"
	allowBankCode["scb"] = "scb" // 2.ไทยพาณิชย์
	allowBankCode["smb"] = "smb"
	allowBankCode["scbt"] = "stcb" // 19.สแตนดาร์ดชาร์เตอร์ด
	allowBankCode["tcrb"] = "thcr"
	allowBankCode["ttb"] = "tmb"     // 6.ทีเอ็มบีธนชาต
	allowBankCode["tisco"] = "tisco" // 17.ทิสโก้
	allowBankCode["uob"] = "uob"     // 11.ยูโอบี

	// มีในระบบ แต่ไม่ซัพพอร์ต
	// "9"	"เกียรตินาคิน"	"kkp"	"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/kk.png/public"	"********"	"TH"	"THB"	"2023-09-18 12:00:11"
	// "14"	"เอชเอสบีซี"	"hsbc"	"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/hsbc.png/public"	"********"	"TH"	"THB"	"2023-09-18 12:00:11"
	// "16"	"ธนาคารอิสลาม"	"isbt"	"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/isbt.png/public"	"********"	"TH"	"THB"	"2023-10-30 07:25:21"
	// "20"	"TrueMoney Wallet"	"true"	"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/true.png/public"	"********"	"TH"	"THB"	"2023-10-30 07:25:21"
	// "21"	"ธนาคารภายนอก"	"external"	"https://storage.googleapis.com/cbgame/banks/none.png"	"********"	"TH"	"THB"	"2023-12-21 07:49:30"
	// "50"	"ธนาคารการค้าต่างประเทศลาว (LAK)"	"BCEL"	"https://storage.googleapis.com/cbgame/banks/none.png"	"********"	"LAOS"	"LAK"	"2024-05-10 09:49:43"
	// "51"	"ธนาคารการค้าต่างประเทศลาว (THB)"	"BCELTH"	"https://storage.googleapis.com/cbgame/banks/none.png"	"********"	"LAOS"	"THB"	"2024-05-10 09:49:43"
	// "52"	"ธนาคารพัฒนาลาว"	"LDB"	"https://storage.googleapis.com/cbgame/banks/none.png"	"********"	"LAOS"	"LAK"	"2024-05-10 09:49:43"
	// "53"	"ธนาคารส่งเสริมการเกษตร"	"APB"	"https://storage.googleapis.com/cbgame/banks/none.png"	"********"	"LAOS"	"LAK"	"2024-05-10 09:49:43"
	// "54"	"ธนาคารร่วมธุรกิจลาว"	"BOL"	"https://storage.googleapis.com/cbgame/banks/none.png"	"********"	"LAOS"	"LAK"	"2024-05-10 09:49:43"
	// "55"	"ธนาคารร่วมพัฒนา"	"LDBBLALA XXX"	"https://storage.googleapis.com/cbgame/banks/none.png"	"********"	"LAOS"	"LAK"	"2024-05-10 09:49:43"
	// "56"	"ธนาคาร ST"	"ST"	"https://storage.googleapis.com/cbgame/banks/none.png"	"********"	"LAOS"	"LAK"	"2024-05-10 09:49:43"
	// "57"	"ธนาคาร BIC"	"BIC"	"https://storage.googleapis.com/cbgame/banks/none.png"	"********"	"LAOS"	"LAK"	"2024-05-10 09:49:43"
	// "58"	"ธนาคาร Maruhan Japan"	"Maruhan Japan"	"https://storage.googleapis.com/cbgame/banks/none.png"	"********"	"LAOS"	"LAK"	"2024-05-10 09:49:43"
	// "59"	"ธนาคาร Sacombank"	"Sacombank"	"https://storage.googleapis.com/cbgame/banks/none.png"	"********"	"LAOS"	"LAK"	"2024-05-10 09:49:43"
	// "60"	"ธนาคารแห่งประเทศจีน"	"BKCHTHBK"	"https://storage.googleapis.com/cbgame/banks/none.png"	"********"	"LAOS"	"LAK"	"2024-05-10 09:49:43"
	// "61"	"ธนาคาร Vietin"	"Vietin"	"https://storage.googleapis.com/cbgame/banks/none.png"	"********"	"LAOS"	"LAK"	"2024-05-10 09:49:43"
	// "62"	"ธนาคาร ACLEDA"	"ACLEDA"	"https://storage.googleapis.com/cbgame/banks/none.png"	"********"	"LAOS"	"LAK"	"2024-05-10 09:49:43"

	// todo Check
	// transactionId := req.OrderNo
	// cusName := req.UserFullname
	// amount := req.Amount
	// bankAcc := req.UserAccountNumber
	// custBank := req.UserAccountBank

	// TRIM all Customer input
	req.CustName = strings.TrimSpace(req.CustName)
	req.CustSecondaryName = strings.TrimSpace(req.CustSecondaryName)
	req.CustBank = strings.TrimSpace(req.CustBank)
	req.CustMobile = strings.TrimSpace(req.CustMobile)
	req.CustEmail = strings.TrimSpace(req.CustEmail)

	if _, ok := allowBankCode[req.CustBank]; ok {
		// set Support Bank
		req.CustBank = allowBankCode[req.CustBank]
	} else {
		fmt.Println("PompayDeposit.Unsupported.CustBank ------> ", req.CustBank)
		req.CustBank = "" // Bug on ส่ง KTB = {"errorCode":"E1001","message":"An error occurred! Please contact system administrator!"}
	}

	webhookDomain := os.Getenv("CYBERGAME_WEBHOOK_ENDPOINT")
	clientSecret := setting.SecretKey

	apiEndPoint := setting.ApiEndPoint
	req.ClientId = setting.MerchantId
	req.CallbackUrl = fmt.Sprintf("%s/pompay/callback", webhookDomain)
	// req.PaymentMethod = "qr"
	// ******** แก้ จาก qr เป็น banktransfer paymentMethod
	req.PaymentMethod = "bankTransfer"
	req.HashVal = r.PompaySign(fmt.Sprintf("%v%v%v%v%v%v%v%v%v%v%v%v%v", req.ClientId, req.TransactionId, req.CustName, req.CustSecondaryName, req.CustBank, req.CustMobile, req.CustEmail, req.Amount, req.ReturnUrl, req.CallbackUrl, req.PaymentMethod, req.BankAcc, clientSecret))

	epUrl := fmt.Sprintf("%s/api/payment", apiEndPoint)
	log.Println("PompayDeposit url ------> ", epUrl)

	// SysLog
	if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
		Name:   "CreatePompayDeposit.PompayDeposit",
		Status: "CALL",
		JsonReq: helper.StructJson(map[string]interface{}{
			"apiEndPoint":       apiEndPoint,
			"clientId":          req.ClientId,
			"transactionId":     req.TransactionId,
			"custName":          req.CustName,
			"custSecondaryName": req.CustSecondaryName,
			"custMobile":        req.CustMobile,
			"custEmail":         req.CustEmail,
			"amount":            req.Amount,
			"returnUrl":         req.ReturnUrl,
			"callbackUrl":       req.CallbackUrl,
			"paymentMethod":     req.PaymentMethod,
			"bankAcc":           req.BankAcc,
			"custBank":          req.CustBank,
			"hashVal":           req.HashVal,
		}),
	}); err != nil {
		log.Println("CreatePompayDeposit.CreateSysLog", err)
	}

	client := &http.Client{}
	client.Timeout = 3 * time.Second

	jsonBody, _ := json.Marshal(req)
	reqBody := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest("POST", epUrl, reqBody)
	reqExternal.Header.Set("Content-Type", "application/json")
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}
	// fmt.Println("PompayDeposit1", string(responseData))
	if response.StatusCode == 404 {
		return nil, errors.New("EMDPOINT_404_NOT_FOUND")
	}
	if response.StatusCode != 200 {
		log.Println(response.StatusCode)
		log.Println(string(responseData))
		// 500 {"status":"E4011","message":"Missing clientId"}
		// {"error":"QR payment service for mxn_yak7 is not allowed."}
		// {"status":"M10017","message":"QR payment service for mxn_YAK1 is not allowed."}
		var errResp model.PompayErrorRemoteResponse
		errJson := json.Unmarshal(responseData, &errResp)
		if errJson == nil {
			return nil, errors.New(errResp.Message)
		}
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	}

	// fmt.Println(string(responseData))

	var result model.PompayDepositCreateRemoteResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("PompayDeposit resp.Body ------> ", string(responseData))
		log.Println("Unmarshal.Err ------> ", errJson)
		return nil, errJson
	}

	return &result, nil
}

func (r repo) PompayWithdraw(setting model.PaygateAccountResponse, req model.PompayWithdrawCreateRemoteRequest) (*model.PompayWithdrawCreateRemoteResponse, error) {

	// log.Println("PompayWithdraw req ------> ")

	// 5.1 Payout Request API
	// To start the payout request, Merchant is required to perform the request via a POST request towards our
	// API (https://pompay.asia/v2/payout). The POST fields are as following:

	// TRIM all Customer input
	req.CustName = strings.TrimSpace(req.CustName)
	req.CustBank = strings.TrimSpace(req.CustBank)
	req.CustBankAcc = strings.TrimSpace(req.CustBankAcc)
	req.CustMobile = strings.TrimSpace(req.CustMobile)
	req.CustEmail = strings.TrimSpace(req.CustEmail)

	// 5.1.2 Bank List
	// Below are the supported bank list and their corresponding codes:
	// Code Bank
	// anz ANZ Bank (Thai)
	// baac BAAC
	// bcn Bank of China
	// bkb Bangkok Bank
	// bnp BNP Paribas
	// boa Bank of Ayudhya
	// bus Bank of America
	// cimb CIMB Thai
	// citi Citibank
	// deut Deutsche Bank
	// ghb The Government Housing Bank
	// gsb Government Saving Bank
	// hks The Hong Kong and Shanghai
	// icbc ICBS (Thai)
	// inb Indian Overseas Bank
	// isb Islamic Bank
	// jpc JPMorgan Chase Bank
	// kkb Kasikorn Bank
	// kpb Kiatnakin Phatra Bank
	// ktb Krung Thai Bank
	// lhb Land and Houses Bank
	// mizb Mizuho Bank
	// scb Siam Commercial Bank
	// smb Sumitomo Mitsui Banking
	// stcb Standard Chartered Bank
	// thcr The Thai Credit Retail Bank
	// tmb TMB Thanachart Bank
	// tisco Tisco Bank
	// uob United Overseas Bank
	convertedBankList := map[string]string{
		"kbank": "kkb",
		"bbl":   "bkb",
		"ttb":   "tmb",
		"bay":   "boa",
	}
	if val, ok := convertedBankList[req.CustBank]; ok {
		req.CustBank = val
	}

	webhookDomain := os.Getenv("CYBERGAME_WEBHOOK_ENDPOINT")
	clientSecret := setting.SecretKey

	apiEndPoint := setting.ApiEndPoint
	req.ClientId = setting.MerchantId
	req.CallbackUrl = fmt.Sprintf("%s/pompay/callback", webhookDomain)

	// Hash Key Example (clientId + transactionId + custName + custBank + custBankAcc + custMobile + custEmail + amount + callbackUrl + clientSecret)
	req.HashVal = r.PompaySign(fmt.Sprintf("%v%v%v%v%v%v%v%v%v%v", req.ClientId, req.TransactionId, req.CustName, req.CustBank, req.CustBankAcc, req.CustMobile, req.CustEmail, req.Amount, req.CallbackUrl, clientSecret))

	epUrl := fmt.Sprintf("%s/payout", apiEndPoint)
	// fmt.Println("PompayWithdraw url ------> ", epUrl)

	// SysLog
	if _, err := r.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
		Name:   "CreatePompayWithdraw.PompayWithdraw",
		Status: "CALL",
		JsonReq: helper.StructJson(map[string]interface{}{
			"apiEndPoint":   apiEndPoint,
			"clientId":      req.ClientId,
			"transactionId": req.TransactionId,
			"custName":      req.CustName,
			"custBank":      req.CustBank,
			"custBankAcc":   req.CustBankAcc,
			"custMobile":    req.CustMobile,
			"custEmail":     req.CustEmail,
			"amount":        req.Amount,
			"callbackUrl":   req.CallbackUrl,
			"hashVal":       req.HashVal,
		}),
	}); err != nil {
		log.Println("CreatePompayWithdraw.CreateSysLog", err)
	}

	client := &http.Client{}
	client.Timeout = 3 * time.Second

	jsonBody, _ := json.Marshal(req)
	reqBody := bytes.NewBuffer(jsonBody)
	reqExternal, _ := http.NewRequest("POST", epUrl, reqBody)
	reqExternal.Header.Set("Content-Type", "application/json")
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("PAYGATE_INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_DATA")
	}
	if response.StatusCode == 404 {
		return nil, errors.New("EMDPOINT_404_NOT_FOUND")
	}
	if response.StatusCode != 200 {
		log.Println(response.StatusCode)
		log.Println(string(responseData))
		// 500 {"status":"E4011","message":"Missing clientId"}
		var errResp model.PompayErrorRemoteResponse
		errJson := json.Unmarshal(responseData, &errResp)
		if errJson == nil {
			return nil, errors.New(errResp.Message)
		}
		return nil, errors.New("PAYGATE_INVALID_REMOTE_RESPONSE_CODE")
	}

	// fmt.Println("PompayWithdraw1")
	// fmt.Println(string(responseData))

	var result model.PompayWithdrawCreateRemoteResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("PompayWithdraw resp.Body ------> ", string(responseData))
		log.Println("Unmarshal.Err ------> ", errJson)
		return nil, errJson
	}

	// fmt.Println("PompayWithdraw2")
	// fmt.Println(string(responseData))

	return &result, nil
}

func (r repo) CreatePompayWebhook(body model.PompayWebhookCreateBody) (*int64, error) {

	if err := r.db.Table("paygate_pompay_webhook").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) GetDbPompayOrderList(req model.PompayOrderListRequest) ([]model.PompayOrderResponse, int64, error) {

	var list []model.PompayOrderResponse
	var total int64

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("paygate_pompay_order as tb_order")
	count = count.Select("tb_order.id")
	if req.UserId != nil {
		count = count.Where("tb_order.user_id = ?", req.UserId)
	}
	if req.OrderTypeId != nil {
		count = count.Where("tb_order.order_type_id = ?", req.OrderTypeId)
	}
	if req.OrderNo != "" {
		count = count.Where("tb_order.order_no = ?", req.OrderNo)
	}
	if req.TransactionNo != "" {
		count = count.Where("tb_order.transaction_no = ?", req.TransactionNo)
	}
	if req.Amount != nil {
		// +- 5.00
		minAmount := *req.Amount - float64(5)
		maxAmount := *req.Amount + float64(5)
		count = count.Where("tb_order.amount BETWEEN ? AND ?", minAmount, maxAmount)
	}
	if req.Status != "" {
		count = count.Where("tb_order.transaction_status = ?", req.Status)
	}

	if err := count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount"
		selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
		selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
		selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

		query := r.db.Table("paygate_pompay_order as tb_order")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN paygate_pompay_order_type AS tb_type ON tb_order.order_type_id = tb_type.id")
		if req.UserId != nil {
			query = query.Where("tb_order.user_id = ?", req.UserId)
		}
		if req.OrderTypeId != nil {
			query = query.Where("tb_order.order_type_id = ?", req.OrderTypeId)
		}
		if req.OrderNo != "" {
			query = query.Where("tb_order.order_no = ?", req.OrderNo)
		}
		if req.TransactionNo != "" {
			query = query.Where("tb_order.transaction_no = ?", req.TransactionNo)
		}
		if req.Amount != nil {
			// +- 5.00
			minAmount := *req.Amount - float64(5)
			maxAmount := *req.Amount + float64(5)
			query = query.Where("tb_order.amount BETWEEN ? AND ?", minAmount, maxAmount)
		}
		if req.Status != "" {
			query = query.Where("tb_order.transaction_status = ?", req.Status)
		}

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetDbPompayOrderById(id int64) (*model.PompayOrderResponse, error) {

	var record model.PompayOrderResponse

	selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount"
	selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
	selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
	selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

	if err := r.db.Table("paygate_pompay_order as tb_order").
		Select(selectedFields).
		Joins("LEFT JOIN paygate_pompay_order_type AS tb_type ON tb_order.order_type_id = tb_type.id").
		Where("tb_order.id = ?", id).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetDbPompayOrderByRefId(refId int64) (*model.PompayOrderResponse, error) {

	var record model.PompayOrderResponse

	selectedFields := "tb_order.id AS id, tb_order.user_id as user_id, tb_order.ref_id as ref_id, tb_order.order_no AS order_no, tb_order.amount AS amount"
	selectedFields += ", tb_order.transaction_no AS transaction_no, tb_order.transaction_date AS transaction_date, tb_order.transaction_status AS transaction_status"
	selectedFields += ", tb_order.qr_promptpay AS qr_promptpay, tb_order.created_at AS created_at, tb_order.updated_at AS updated_at"
	selectedFields += ", tb_order.order_type_id AS order_type_id, tb_type.name AS order_type_name"

	if err := r.db.Table("paygate_pompay_order as tb_order").
		Select(selectedFields).
		Joins("LEFT JOIN paygate_pompay_order_type AS tb_type ON tb_order.order_type_id = tb_type.id").
		Where("tb_order.ref_id = ?", refId).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) CreateDbPompayOrder(body model.PompayOrderCreateBody) (*int64, error) {

	if err := r.db.Table("paygate_pompay_order").Create(&body).Error; err != nil {
		return nil, err
	}

	// Update order_no if Empty in HPG{YYYYMMDD}{ID} //
	// orderNo := fmt.Sprintf("HPG%v%v", time.Now().Format("200601"), body.Id)
	// [20240209] Update order_no if Empty in {AGENT_NAME}{YYMM}{ID} //
	agentName := os.Getenv("PAYGATE_ORDER_PREFIX")
	if ginMode := os.Getenv("GIN_MODE"); ginMode == "debug" {
		agentName = "P6D" // DEVELOPMENT
	}
	if agentName == "" {
		agentName = "P6G" // ** MIN_LEN=10
	} else {
		agentName = strings.ToUpper(agentName)
	}
	orderNo := fmt.Sprintf("%v%v%v", agentName, time.Now().Format("0601"), body.Id)
	if body.OrderNo == "" {
		updateBody := make(map[string]interface{})
		updateBody["order_no"] = orderNo
		if err := r.db.Table("paygate_pompay_order").Where("id = ?", body.Id).Updates(updateBody).Error; err != nil {
			return nil, err
		}
	}
	return &body.Id, nil
}

func (r repo) UpdateDbPompayOrderError(id int64, remark string) error {

	updateBody := map[string]interface{}{
		"transaction_status": "ERROR",
		"remark":             remark,
	}
	sql := r.db.Table("paygate_pompay_order").Where("id = ?", id).Where("transaction_status = ?", "PENDING")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdateDbPompayOrder(id int64, body model.PompayOrderUpdateBody) error {

	if err := r.db.Table("paygate_pompay_order").Where("id = ?", id).Updates(body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) ApproveDbPompayOrder(id int64, webhookStatus string) error {

	updateBody := make(map[string]interface{})
	updateBody["transaction_status"] = webhookStatus
	updateBody["payment_at"] = time.Now()

	sql := r.db.Table("paygate_pompay_order").Where("id = ?", id).Where("transaction_status = ?", "WAIT_PAYMENT")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdatePompayOrderBankSucess(id int64, transId int64, actionBy int64) error {

	updateBody := map[string]interface{}{
		"bank_transaction_id":     transId,
		"bank_transaction_status": "SUCCESS",
		"action_by":               actionBy,
	}

	sql := r.db.Table("paygate_pompay_order").Where("id = ?", id).Where("bank_transaction_status = ?", "PENDING")
	if err := sql.Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}
