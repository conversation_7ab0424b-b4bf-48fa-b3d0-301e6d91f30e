package repository

import (
	"cybergame-api/model"

	"gorm.io/gorm"
)

func NewRecommendRepository(db *gorm.DB) RecommendRepository {
	return &repo{db}
}

type RecommendRepository interface {
	GetChannelById(id int64) (*model.Recommend, error)
	GetRecommendList(query model.RecommendQuery) ([]model.RecommendList, int64, error)
	GetListForFront() ([]model.RecommendList, error)
	CheckRecommendExist(id int64) (bool, error)
	CreateRecommend(recommend model.CreateRecommend) error
	UpdateRecommend(id int64, body model.RecommendUpdateRequest) error
	DeleteRecommend(id int64) error
	// User
	CountUserRecommendChannel(id int64) (int64, error)
	// [ADMIN_LOG]
	CreateAdminLog(body model.AdminLogCreateBody) (*int64, error)
}

func (r repo) GetChannelById(id int64) (*model.Recommend, error) {

	var recommend model.Recommend

	if err := r.db.Table("recommend_channel").
		Where("id = ?", id).
		First(&recommend).
		Error; err != nil {
		return nil, err
	}
	return &recommend, nil
}

func (r repo) GetRecommendList(query model.RecommendQuery) ([]model.RecommendList, int64, error) {

	var recommends []model.RecommendList

	var total int64

	queryTotal := r.db.Table("recommend_channel")

	if query.Status != "" {
		queryTotal = queryTotal.Where("status = ?", query.Status)
	}

	if query.Filter != "" {
		queryTotal = queryTotal.Where("title LIKE ?", "%"+query.Filter+"%")
	}

	if err := queryTotal.Table("recommend_channel").
		Select("id").
		Count(&total).
		Error; err != nil {
		return nil, 0, err
	}

	db := r.db.Table("recommend_channel").Select("id, title, graph_color, status, url, created_at")

	if query.Status != "" {
		db = db.Where("status = ?", query.Status)
	}

	if query.Filter != "" {
		db = db.Where("title LIKE ?", "%"+query.Filter+"%")
	}

	if query.Limit > 0 {
		db = db.Limit(query.Limit)
	}

	if err := db.
		Offset(query.Limit * query.Page).
		Scan(&recommends).
		Order("id ASC").
		Error; err != nil {
		return nil, 0, err
	}

	return recommends, total, nil
}

func (r repo) GetListForFront() ([]model.RecommendList, error) {

	var recommends []model.RecommendList

	db := r.db.Table("recommend_channel").Select("id, title, graph_color, status, url, created_at")
	db = db.Where("status = ?", "ACTIVE")

	if err := db.
		Scan(&recommends).
		Order("id ASC").
		Error; err != nil {
		return nil, err
	}

	return recommends, nil
}

func (r repo) CheckRecommendExist(id int64) (bool, error) {

	var total int64

	if err := r.db.Table("recommend_channel").
		Where("id = ?", id).
		Where("status = ?", "ACTIVE").
		Count(&total).
		Error; err != nil {
		return false, err
	}

	return total > 0, nil
}

func (r repo) CreateRecommend(recommend model.CreateRecommend) error {

	if err := r.db.Table("recommend_channel").
		Create(&recommend).
		Error; err != nil {
		return err
	}

	return nil
}

func (r repo) UpdateRecommend(id int64, body model.RecommendUpdateRequest) error {

	if err := r.db.Table("recommend_channel").
		Where("id = ?", id).
		Updates(&body).
		Error; err != nil {
		return err
	}

	return nil
}

func (r repo) DeleteRecommend(id int64) error {

	if err := r.db.Table("recommend_channel").
		Where("id = ?", id).
		Delete(&model.Recommend{}).
		Error; err != nil {
		return err
	}

	return nil
}

func (r repo) CountUserRecommendChannel(id int64) (int64, error) {

	var total int64

	if err := r.db.Table("user AS tb_user").
		Where("tb_user.channel_id = ?", id).
		Count(&total).
		Error; err != nil {
		return 0, err
	}
	return total, nil
}
