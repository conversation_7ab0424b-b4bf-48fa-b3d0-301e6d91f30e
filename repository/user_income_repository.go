package repository

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"log"
	"strings"
	"time"

	"gorm.io/gorm"
)

func NewUserIncomeLogRepository(db *gorm.DB) UserIncomeLogRepository {
	return &repo{db}
}

type UserIncomeLogRepository interface {
	GetUserIncomeLogTypeOptions() ([]model.SelectOptions, error)
	GetUserIncomeLogStatusOptions() ([]model.SelectOptions, error)
	GetUserIncomeLogById(id int64) (*model.UserIncomeLogResponse, error)
	GetUserIncomeLogList(req model.UserIncomeLogListRequest) ([]model.UserIncomeLogResponse, int64, error)
	GetUserIncomePendingCount() (int64, error)
	GetUserIncomePendingList(req model.UserIncomeLogListRequest) ([]model.UserIncomeLogResponse, int64, error)
	GetUserIncomeCompletedLogList(req model.UserIncomeCompletedLogListRequest) ([]model.UserIncomeCompletedLogResponse, int64, error)
	CreateUserIncomeLog(body model.UserIncomeLogCreateBody) (*int64, error)
	ConfirmUserIncomeLog(body model.UserIncomeLogConfirmBody) error
	CancelUserIncomeLog(body model.UserIncomeLogConfirmBody) error

	GetUserIncomeLogListSummary(req model.UserIncomeLogTotalSummaryRequest) (*model.UserIncomeLogTotalSummaryResponse, error)
}

func (r repo) GetUserIncomeLogTypeOptions() ([]model.SelectOptions, error) {

	options := []model.SelectOptions{}

	options = append(options, model.SelectOptions{
		Id:    0,
		Label: "ทั้งหมด",
		Value: "ALL",
	})
	options = append(options, model.SelectOptions{
		Id:    1,
		Label: "คืนยอดเสีย",
		Value: "PROMOTION_RETURN_LOSS",
	})
	options = append(options, model.SelectOptions{
		Id:    1,
		Label: "คืนยอดเทิร์น",
		Value: "PROMOTION_RETURN_TURN",
	})
	options = append(options, model.SelectOptions{
		Id:    2,
		Label: "แนะนำเพื่อน",
		Value: "AFFILIATE",
	})
	return options, nil
}

func (r repo) GetUserIncomeLogStatusOptions() ([]model.SelectOptions, error) {

	options := []model.SelectOptions{}

	options = append(options, model.SelectOptions{
		Id:    0,
		Label: "ทั้งหมด",
		Value: "ALL",
	})
	options = append(options, model.SelectOptions{
		Id:    1,
		Label: "รอกดรับ",
		Value: "READY",
	})
	options = append(options, model.SelectOptions{
		Id:    2,
		Label: "สำเร็จ",
		Value: "COMPLETED",
	})
	return options, nil
}

func (r repo) GetUserIncomeLogById(id int64) (*model.UserIncomeLogResponse, error) {

	var record model.UserIncomeLogResponse

	selectedFields := "logs.id as id, logs.user_id as user_id, users.member_code as member_code, logs.type_id as type_id, logs.ref_id as ref_id, logs.detail as detail, logs.credit_amount as credit_amount, logs.credit_after as credit_after"
	selectedFields += ", IF(logs.transfer_at IS NULL, logs.created_at, logs.transfer_at) as transfer_at"
	selectedFields += ", logs.status_id as status_id, logs.created_at as created_at, logs.updated_at as updated_at"
	selectedFields += ", logs.create_by as create_by, logs.confirm_by as confirm_by, logs.create_by_name as create_by_name, logs.confirm_by_name as confirm_by_name"
	selectedFields += ", types.name as type_name, types.detail as type_detail, statuses.name as status_name, statuses.detail as status_detail"
	if err := r.db.Table("user_income_log as logs").
		Select(selectedFields).
		Joins("LEFT JOIN user as users ON users.id = logs.user_id").
		Joins("LEFT JOIN user_income_log_type as types ON types.id = logs.type_id").
		Joins("LEFT JOIN user_income_log_status as statuses ON statuses.id = logs.status_id").
		Where("logs.id = ?", id).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetUserIncomeLogList(req model.UserIncomeLogListRequest) ([]model.UserIncomeLogResponse, int64, error) {

	var list []model.UserIncomeLogResponse
	var total int64

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("user_income_log as logs")
	count = count.Select("logs.id")
	if req.FromDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.FromDate)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("logs.created_at >= ? ", startDateAtBkk)
	}
	if req.ToDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.ToDate)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("logs.created_at <= ? ", endDateAtBkk)
	}
	if req.MemberCode != "" {
		searchText := "%" + strings.TrimSpace(req.MemberCode) + "%"
		count = count.Joins("LEFT JOIN user as users ON users.id = logs.user_id")
		count = count.Where("users.member_code LIKE ?", searchText)
	}
	if req.TypeName != "" && strings.ToUpper(req.TypeName) != "ALL" {
		if strings.ToUpper(req.TypeName) == "AFFILIATE" {
			count = count.Where("logs.type_id = ?", model.USER_INCOME_TYPE_AFFILIATE)
		} else if strings.ToUpper(req.TypeName) == "PROMOTION_RETURN_LOSS" {
			count = count.Where("logs.type_id = ?", model.USER_INCOME_TYPE_PROMOTION_RETURN_LOSS)
		} else if strings.ToUpper(req.TypeName) == "PROMOTION_RETURN_TURN" {
			count = count.Where("logs.type_id = ?", model.USER_INCOME_TYPE_PROMOTION_RETURN_TURN)
		} else if strings.ToUpper(req.TypeName) == "ALLIANCE" {
			count = count.Where("logs.type_id = ?", model.USER_INCOME_TYPE_ALLIANCE)
		} else if strings.ToUpper(req.TypeName) == "WHEEL_ACTIVITY" {
			count = count.Where("logs.type_id = ?", model.USER_INCOME_TYPE_LUCKY_WHEEL)
		} else {
			log.Println("TODO: no ALLIANCE type")
			// count = count.Where("logs.type_id != ?", model.USER_INCOME_TYPE_ALLIANCE) // no ALLIANCE type
		}
	} else {
		log.Println("TODO: no ALLIANCE type")
		// count = count.Where("logs.type_id != ?", model.USER_INCOME_TYPE_ALLIANCE) // no ALLIANCE type
	}
	if req.StatusId != nil {
		if *req.StatusId != 0 {
			count = count.Where("logs.status_id = ?", req.StatusId)
		}
	}
	if req.StatusName != "" {
		if req.StatusName == "PENDING" {
			count = count.Where("logs.status_id = ?", model.USER_INCOME_STATUS_PENDING)
		} else if req.StatusName == "COMPLETED" {
			count = count.Where("logs.status_id IN (?)", []int64{model.USER_INCOME_STATUS_COMPLETED, model.USER_INCOME_STATUS_CANCELLED})
		}
	}

	if err := count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "logs.id as id, logs.user_id as user_id, users.member_code as member_code, logs.type_id as type_id, logs.ref_id as ref_id, logs.detail as detail, logs.credit_amount as credit_amount, logs.credit_after as credit_after"
		selectedFields += ", IF(logs.transfer_at IS NULL, logs.created_at, logs.transfer_at) as transfer_at"
		selectedFields += ", logs.status_id as status_id, logs.created_at as created_at, logs.updated_at as updated_at"
		selectedFields += ", logs.create_by as create_by, logs.confirm_by as confirm_by, logs.create_by_name as create_by_name, logs.confirm_by_name as confirm_by_name"
		selectedFields += ", types.name as type_name, types.detail as type_detail, statuses.name as status_name, statuses.detail as status_detail"
		query := r.db.Table("user_income_log as logs")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN user as users ON users.id = logs.user_id")
		query = query.Joins("LEFT JOIN user_income_log_type as types ON types.id = logs.type_id")
		query = query.Joins("LEFT JOIN user_income_log_status as statuses ON statuses.id = logs.status_id")
		if req.FromDate != "" {
			startDateAtBkk, err := r.ParseBodBkk(req.FromDate)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("logs.created_at >= ? ", startDateAtBkk)
		}
		if req.ToDate != "" {
			endDateAtBkk, err := r.ParseEodBkk(req.ToDate)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("logs.created_at <= ? ", endDateAtBkk)
		}
		if req.MemberCode != "" {
			searchText := "%" + strings.TrimSpace(req.MemberCode) + "%"
			query = query.Where("users.member_code LIKE ?", searchText)
		}
		if req.TypeName != "" && strings.ToUpper(req.TypeName) != "ALL" {
			if strings.ToUpper(req.TypeName) == "AFFILIATE" {
				query = query.Where("logs.type_id = ?", model.USER_INCOME_TYPE_AFFILIATE)
			} else if strings.ToUpper(req.TypeName) == "PROMOTION_RETURN_LOSS" {
				query = query.Where("logs.type_id = ?", model.USER_INCOME_TYPE_PROMOTION_RETURN_LOSS)
			} else if strings.ToUpper(req.TypeName) == "PROMOTION_RETURN_TURN" {
				query = query.Where("logs.type_id = ?", model.USER_INCOME_TYPE_PROMOTION_RETURN_TURN)
			} else if strings.ToUpper(req.TypeName) == "ALLIANCE" {
				query = query.Where("logs.type_id = ?", model.USER_INCOME_TYPE_ALLIANCE)
			} else if strings.ToUpper(req.TypeName) == "WHEEL_ACTIVITY" {
				query = query.Where("logs.type_id = ?", model.USER_INCOME_TYPE_LUCKY_WHEEL)
			} else {
				log.Println("TODO: no ALLIANCE type")
				// query = query.Where("logs.type_id != ?", model.USER_INCOME_TYPE_ALLIANCE) // no ALLIANCE type
			}
		} else {
			log.Println("TODO: no ALLIANCE type")
			// query = query.Where("logs.type_id != ?", model.USER_INCOME_TYPE_ALLIANCE) // no ALLIANCE type
		}
		if req.StatusId != nil {
			if *req.StatusId != 0 {
				query = query.Where("logs.status_id = ?", req.StatusId)
			}
		}
		if req.StatusName != "" {
			if req.StatusName == "PENDING" {
				query = query.Where("logs.status_id = ?", model.USER_INCOME_STATUS_PENDING)
			} else if req.StatusName == "COMPLETED" {
				query = query.Where("logs.status_id IN (?)", []int64{model.USER_INCOME_STATUS_COMPLETED, model.USER_INCOME_STATUS_CANCELLED})
			}
		}
		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			req.SortCol = helper.ToSnake(req.SortCol)
			// check . if not exist, add it with main table name
			if !strings.Contains(req.SortCol, ".") {
				req.SortCol = "logs." + req.SortCol
			}
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}

	return list, total, nil
}

func (r repo) GetUserIncomeLogListSummary(req model.UserIncomeLogTotalSummaryRequest) (*model.UserIncomeLogTotalSummaryResponse, error) {

	var list *model.UserIncomeLogTotalSummaryResponse

	// SELECT //
	selectedFields := "SUM(logs.credit_amount) as user_income_total_amount"
	selectedFields += ", SUM(CASE WHEN logs.type_id = ? THEN logs.credit_amount ELSE 0 END) as alliance_income_total_amount"
	selectedFields += ", SUM(CASE WHEN logs.type_id = ? THEN logs.credit_amount ELSE 0 END) as affiliate_income_total_amount"
	selectedFields += ", SUM(CASE WHEN logs.type_id = (?) THEN logs.credit_amount ELSE 0 END) as promotion_return_loss_total_amount"
	selectedFields += ", SUM(CASE WHEN logs.type_id = ? THEN logs.credit_amount ELSE 0 END) as lucky_wheel_total_amount"
	selectedFields += ", SUM(CASE WHEN logs.type_id = ? THEN logs.credit_amount ELSE 0 END) as promotion_return_turn_total_amount"

	query := r.db.Table("user_income_log as logs")
	query = query.Select(selectedFields, model.USER_INCOME_TYPE_ALLIANCE, model.USER_INCOME_TYPE_AFFILIATE, model.USER_INCOME_TYPE_PROMOTION_RETURN_LOSS, model.USER_INCOME_TYPE_LUCKY_WHEEL, model.USER_INCOME_TYPE_PROMOTION_RETURN_TURN)
	// P.Mink เอาแค่ที่ success อย่างเดียว
	query = query.Where("logs.status_id IN (?)", []int64{model.USER_INCOME_STATUS_COMPLETED})
	if req.FromDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.FromDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("logs.created_at >= ? ", startDateAtBkk)
	}
	if req.ToDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.ToDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("logs.created_at <= ? ", endDateAtBkk)
	}
	if err := query.Scan(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (r repo) GetUserIncomePendingCount() (int64, error) {

	var total int64

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Select("logs.id")
	count = count.Table("user_income_log as logs")
	count = count.Where("logs.status_id = ?", model.USER_INCOME_STATUS_PENDING)

	if err := count.Count(&total).Error; err != nil {
		return 0, err
	}
	return total, nil
}

func (r repo) GetUserIncomePendingList(req model.UserIncomeLogListRequest) ([]model.UserIncomeLogResponse, int64, error) {

	var list []model.UserIncomeLogResponse
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("user_income_log as logs")
	count = count.Select("logs.id")
	if req.FromDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.FromDate)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("logs.created_at >= ? ", startDateAtBkk)
	}
	if req.ToDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.ToDate)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("logs.created_at <= ? ", endDateAtBkk)
	}
	if req.TypeName != "" && strings.ToUpper(req.TypeName) != "ALL" {
		if strings.ToUpper(req.TypeName) == "AFFILIATE" {
			count = count.Where("logs.type_id = ?", model.USER_INCOME_TYPE_AFFILIATE)
		} else if strings.ToUpper(req.TypeName) == "PROMOTION_RETURN_LOSS" {
			count = count.Where("logs.type_id = ?", model.USER_INCOME_TYPE_PROMOTION_RETURN_LOSS)
		} else if strings.ToUpper(req.TypeName) == "PROMOTION_RETURN_TURN" {
			count = count.Where("logs.type_id = ?", model.USER_INCOME_TYPE_PROMOTION_RETURN_TURN)
		} else {
			count = count.Where("logs.type_id = ?", -1) // noshow
		}
	}
	if req.MemberCode != "" {
		searchText := "%" + strings.TrimSpace(req.MemberCode) + "%"
		count = count.Joins("LEFT JOIN user as users ON users.id = logs.user_id")
		count = count.Where("users.member_code LIKE ?", searchText)
	}
	if req.StatusId != nil {
		if *req.StatusId != 0 {
			count = count.Where("logs.status_id = ?", req.StatusId)
		}
	}
	if req.StatusName != "" {
		if req.StatusName == "PENDING" {
			count = count.Where("logs.status_id = ?", model.USER_INCOME_STATUS_PENDING)
		} else if req.StatusName == "COMPLETED" {
			count = count.Where("logs.status_id IN (?)", []int64{model.USER_INCOME_STATUS_COMPLETED, model.USER_INCOME_STATUS_CANCELLED})
		}
	}
	count = count.Where("logs.status_id = ?", model.USER_INCOME_STATUS_PENDING)

	if err = count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT // created_at == transfer_at
		selectedFields := "logs.id as id, logs.user_id as user_id, users.member_code as member_code, logs.type_id as type_id, logs.ref_id as ref_id, logs.detail as detail, logs.credit_amount as credit_amount, logs.credit_after as credit_after"
		selectedFields += ", IF(logs.transfer_at IS NULL, logs.created_at, logs.transfer_at) as transfer_at"
		selectedFields += ", logs.status_id as status_id, logs.created_at as created_at, logs.updated_at as updated_at"
		selectedFields += ", logs.create_by as create_by, logs.confirm_by as confirm_by, logs.create_by_name as create_by_name, logs.confirm_by_name as confirm_by_name"
		selectedFields += ", types.name as type_name, types.detail as type_detail, statuses.name as status_name, statuses.detail as status_detail"
		query := r.db.Table("user_income_log as logs")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN user as users ON users.id = logs.user_id")
		query = query.Joins("LEFT JOIN user_income_log_type as types ON types.id = logs.type_id")
		query = query.Joins("LEFT JOIN user_income_log_status as statuses ON statuses.id = logs.status_id")
		if req.FromDate != "" {
			startDateAtBkk, err := r.ParseBodBkk(req.FromDate)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("logs.created_at >= ? ", startDateAtBkk)
		}
		if req.ToDate != "" {
			endDateAtBkk, err := r.ParseEodBkk(req.ToDate)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("logs.created_at <= ? ", endDateAtBkk)
		}
		if req.MemberCode != "" {
			searchText := "%" + strings.TrimSpace(req.MemberCode) + "%"
			query = query.Where("users.member_code LIKE ?", searchText)
		}
		if req.TypeName != "" {
			if strings.ToUpper(req.TypeName) == "AFFILIATE" {
				query = query.Where("logs.type_id = ?", model.USER_INCOME_TYPE_AFFILIATE)
			} else if strings.ToUpper(req.TypeName) == "PROMOTION_RETURN_LOSS" {
				query = query.Where("logs.type_id = ?", model.USER_INCOME_TYPE_PROMOTION_RETURN_LOSS)
			} else if strings.ToUpper(req.TypeName) == "PROMOTION_RETURN_TURN" {
				query = query.Where("logs.type_id = ?", model.USER_INCOME_TYPE_PROMOTION_RETURN_TURN)
			} else if strings.ToUpper(req.TypeName) == "ALLIANCE" {
				query = query.Where("logs.type_id = ?", model.USER_INCOME_TYPE_ALLIANCE)
			}
		}
		if req.StatusId != nil {
			if *req.StatusId != 0 {
				query = query.Where("logs.status_id = ?", req.StatusId)
			}
		}
		if req.StatusName != "" {
			if req.StatusName == "PENDING" {
				query = query.Where("logs.status_id = ?", model.USER_INCOME_STATUS_PENDING)
			} else if req.StatusName == "COMPLETED" {
				query = query.Where("logs.status_id IN (?)", []int64{model.USER_INCOME_STATUS_COMPLETED, model.USER_INCOME_STATUS_CANCELLED})
			}
		}
		query = query.Where("logs.status_id = ?", model.USER_INCOME_STATUS_PENDING)

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetUserIncomeCompletedLogList(req model.UserIncomeCompletedLogListRequest) ([]model.UserIncomeCompletedLogResponse, int64, error) {

	var list []model.UserIncomeCompletedLogResponse
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("user_income_log as logs")
	count = count.Select("logs.id")
	count = count.Where("logs.user_id = ?", req.UserId)
	if req.FromDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.FromDate)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("logs.created_at >= ? ", startDateAtBkk)
	}
	if req.ToDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.ToDate)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("logs.created_at <= ? ", endDateAtBkk)
	}
	if req.TypeName != "" {
		if strings.ToUpper(req.TypeName) == "AFFILIATE" {
			count = count.Where("logs.type_id = ?", model.USER_INCOME_TYPE_AFFILIATE)
		} else if strings.ToUpper(req.TypeName) == "PROMOTION_RETURN_LOSS" {
			count = count.Where("logs.type_id = ?", model.USER_INCOME_TYPE_PROMOTION_RETURN_LOSS)
		} else if strings.ToUpper(req.TypeName) == "PROMOTION_RETURN_TURN" {
			count = count.Where("logs.type_id = ?", model.USER_INCOME_TYPE_PROMOTION_RETURN_TURN)
		} else if strings.ToUpper(req.TypeName) == "ALLIANCE" {
			count = count.Where("logs.type_id = ?", model.USER_INCOME_TYPE_ALLIANCE)
		}
	}
	if req.TypeIds != nil {
		count = count.Where("logs.type_id IN ?", req.TypeIds)
	}
	count = count.Where("logs.status_id = ?", model.USER_INCOME_STATUS_COMPLETED)

	if err = count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "logs.id as id, logs.user_id as user_id, users.member_code as member_code, logs.type_id as type_id, logs.ref_id as ref_id, logs.detail as detail, logs.credit_amount as credit_amount, logs.credit_after as credit_after"
		selectedFields += ", logs.transfer_at as transfer_at, logs.status_id as status_id, logs.created_at as created_at, logs.updated_at as updated_at"
		selectedFields += ", logs.create_by as create_by, logs.confirm_by as confirm_by, logs.create_by_name as create_by_name, logs.confirm_by_name as confirm_by_name"
		selectedFields += ", types.name as type_name, types.detail as type_detail, statuses.name as status_name, statuses.detail as status_detail"
		query := r.db.Table("user_income_log as logs")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN user as users ON users.id = logs.user_id")
		query = query.Joins("LEFT JOIN user_income_log_type as types ON types.id = logs.type_id")
		query = query.Joins("LEFT JOIN user_income_log_status as statuses ON statuses.id = logs.status_id")
		query = query.Where("logs.user_id = ?", req.UserId)
		if req.FromDate != "" {
			startDateAtBkk, err := r.ParseBodBkk(req.FromDate)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("logs.created_at >= ? ", startDateAtBkk)
		}
		if req.ToDate != "" {
			endDateAtBkk, err := r.ParseEodBkk(req.ToDate)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("logs.created_at <= ? ", endDateAtBkk)
		}
		if req.TypeName != "" {
			if strings.ToUpper(req.TypeName) == "AFFILIATE" {
				query = query.Where("logs.type_id = ?", model.USER_INCOME_TYPE_AFFILIATE)
			} else if strings.ToUpper(req.TypeName) == "PROMOTION_RETURN_LOSS" {
				query = query.Where("logs.type_id = ?", model.USER_INCOME_TYPE_PROMOTION_RETURN_LOSS)
			} else if strings.ToUpper(req.TypeName) == "PROMOTION_RETURN_TURN" {
				query = query.Where("logs.type_id = ?", model.USER_INCOME_TYPE_PROMOTION_RETURN_TURN)
			} else if strings.ToUpper(req.TypeName) == "ALLIANCE" {
				query = query.Where("logs.type_id = ?", model.USER_INCOME_TYPE_ALLIANCE)
			}
		}
		if req.TypeIds != nil {
			query = query.Where("logs.type_id IN ?", req.TypeIds)
		}
		query = query.Where("logs.status_id = ?", model.USER_INCOME_STATUS_COMPLETED)

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}

	return list, total, nil
}

func (r repo) CreateUserIncomeLog(body model.UserIncomeLogCreateBody) (*int64, error) {

	if err := r.db.Table("user_income_log").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) ConfirmUserIncomeLog(body model.UserIncomeLogConfirmBody) error {

	updateData := map[string]interface{}{
		"credit_after":    body.CreditAfter,
		"transfer_at":     body.TransferAt,
		"status_id":       model.USER_INCOME_STATUS_COMPLETED,
		"confirm_by":      body.ConfirmBy,
		"confirm_by_name": body.ConfirmByName,
	}

	query := r.db.Table("user_income_log").Where("id = ?", body.Id)
	query = query.Where("status_id = ?", model.USER_INCOME_STATUS_PENDING)
	if err := query.Updates(updateData).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) CancelUserIncomeLog(body model.UserIncomeLogConfirmBody) error {

	updateData := map[string]interface{}{
		"status_id":       model.USER_INCOME_STATUS_CANCELLED,
		"confirm_by":      body.ConfirmBy,
		"confirm_by_name": body.ConfirmByName,
	}

	query := r.db.Table("user_income_log").Where("id = ?", body.Id)
	query = query.Where("status_id = ?", model.USER_INCOME_STATUS_PENDING)
	if err := query.Updates(updateData).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetUserWinLoseSummary(req model.UserWinLoseSummaryReportRequest) (*model.UserWinLoseSummaryReportResponse, error) {

	var report model.UserWinLoseSummaryReportResponse
	var err error

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return &report, err
	}

	selectedFields1 := "SUM(logs.turn_total) AS total_turn_over, SUM(logs.win_lose_total) AS total_win_lose, SUM(logs.valid_amount_total) AS total_diff_amount"
	if req.TypeName == "casino" {
		selectedFields1 = "SUM(logs.turn_casino) AS total_turn_over, SUM(logs.win_lose_casino) AS total_win_lose, SUM(logs.valid_amount_casino) AS total_diff_amount"
	} else if req.TypeName == "sport" {
		selectedFields1 = "SUM(logs.turn_sport) AS total_turn_over, SUM(logs.win_lose_sport) AS total_win_lose, SUM(logs.valid_amount_sport) AS total_diff_amount"
	} else if req.TypeName == "game" {
		selectedFields1 = "SUM(logs.turn_game) AS total_turn_over, SUM(logs.win_lose_game) AS total_win_lose, SUM(logs.valid_amount_game) AS total_diff_amount"
	}
	sql := r.db.Table("play_log as logs")
	sql = sql.Select(selectedFields1)
	sql = sql.Joins("LEFT JOIN user as tb_user ON tb_user.id = logs.user_id")

	if req.MemberCode != "" {
		searchText := "%" + strings.TrimSpace(req.MemberCode) + "%"
		sql = sql.Where("tb_user.member_code LIKE ?", searchText)
	}
	if dateType.DateFrom != "" {
		sql = sql.Where("logs.date >= ? ", dateType.DateFrom)
	}
	if dateType.DateTo != "" {
		sql = sql.Where("logs.date <=  ?", dateType.DateTo)
	}
	if err = sql.Scan(&report).Error; err != nil {
		return nil, err
	}

	return &report, nil
}

func (r repo) GetUserWinLoseSummaryList(req model.UserWinLoseSummaryListRequest) ([]model.UserWinLoseSummaryResponse, int64, error) {

	var list []model.UserWinLoseSummaryResponse
	var total int64
	var err error

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return list, total, err
	}

	// Count total records for pagination purposes (without limit and offset) //

	count := r.db.Table("play_log as logs")
	count = count.Select("logs.user_id")
	count = count.Joins("LEFT JOIN user as tb_user ON tb_user.id = logs.user_id")
	count = count.Where("tb_user.member_code != ?", "")
	// count = count.Having(r.db.Where("SUM(logs.turn_total) > 0").Or("SUM(logs.win_lose_total) > 0").Or("SUM(logs.valid_amount_total) > 0"))
	if req.MemberCode != "" {
		searchText := "%" + strings.TrimSpace(req.MemberCode) + "%"
		count = count.Where("tb_user.member_code LIKE ?", searchText)
	}
	if dateType.DateFrom != "" {
		count = count.Where("logs.date >= ? ", dateType.DateFrom)
	}
	if dateType.DateTo != "" {
		count = count.Where("logs.date <=  ?", dateType.DateTo)
	}
	count = count.Group("logs.user_id")
	if err = count.Count(&total).Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "logs.user_id, SUM(logs.turn_total) AS total_turn_over, SUM(logs.win_lose_total) AS total_win_lose, SUM(logs.valid_amount_total) AS diff_amount"
		if req.TypeName == "casino" {
			selectedFields = "logs.user_id, SUM(logs.turn_casino) AS total_turn_over, SUM(logs.win_lose_casino) AS total_win_lose, SUM(logs.valid_amount_casino) AS diff_amount"
		} else if req.TypeName == "sport" {
			selectedFields = "logs.user_id, SUM(logs.turn_sport) AS total_turn_over, SUM(logs.win_lose_sport) AS total_win_lose, SUM(logs.valid_amount_sport) AS diff_amount"
		} else if req.TypeName == "game" {
			selectedFields = "logs.user_id, SUM(logs.turn_game) AS total_turn_over, SUM(logs.win_lose_game) AS total_win_lose, SUM(logs.valid_amount_game) AS diff_amount"
		}
		sql := r.db.Table("play_log as logs")
		sql = sql.Select(selectedFields)
		sql = sql.Joins("LEFT JOIN user as tb_user ON tb_user.id = logs.user_id")
		sql = sql.Where("tb_user.member_code != ?", "")

		if req.MemberCode != "" {
			searchText := "%" + strings.TrimSpace(req.MemberCode) + "%"
			sql = sql.Where("tb_user.member_code LIKE ?", searchText)
		}
		if dateType.DateFrom != "" {
			sql = sql.Where("logs.date >= ? ", dateType.DateFrom)
		}
		if dateType.DateTo != "" {
			sql = sql.Where("logs.date <=  ?", dateType.DateTo)
		}
		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			// "totalTurnOver", "totalValidAmount", "diffAmount", "totalWinLose"}
			allowSortCols := map[string]string{
				"totalTurnOver":    "total_turn_over",
				"totalValidAmount": "diff_amount",
				"diffAmount":       "diff_amount",
				"totalWinLose":     "total_win_lose",
			}
			if colName, ok := allowSortCols[req.SortCol]; ok {
				if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
					req.SortAsc = "DESC"
				} else {
					req.SortAsc = "ASC"
				}
				sql = sql.Order(colName + " " + req.SortAsc)
			}
		}
		if req.Limit > 0 {
			sql = sql.Limit(req.Limit)
		}
		sql = sql.Group("logs.user_id")
		sql = sql.Offset(req.Page * req.Limit)
		if err = sql.Scan(&list).Error; err != nil {
			return nil, 0, err
		}

		// APPEND membercode
		userIds := []int64{}
		for _, v := range list {
			userIds = append(userIds, v.UserId)
		}
		if len(userIds) > 0 {
			// เอามาจาก user
			var userInfo []struct {
				UserId     int64  `json:"userId"`
				MemberCode string `json:"memberCode"`
			}
			selectedFields1 := "tb_user.id as user_id, tb_user.member_code as member_code"
			query1 := r.db.Table("user as tb_user")
			query1 = query1.Select(selectedFields1)
			query1 = query1.Where("tb_user.id IN ?", userIds)
			if err = query1.Scan(&userInfo).Error; err != nil {
				return nil, 0, err
			}

			// MERGE DATA BY USER_ID
			for index, item := range list {
				for _, v2 := range userInfo {
					if item.UserId == v2.UserId {
						list[index].MemberCode = v2.MemberCode
						break
					}
				}
			}
		}

	}
	return list, total, nil
}

func (r repo) GetUserWinLoseDailySummary(req model.UserWinLoseDailySummaryRequest) (*model.UserWinLoseSummaryReportResponse, error) {

	var report model.UserWinLoseSummaryReportResponse
	var err error

	selectedFields1 := "SUM(logs.turn_total) AS total_turn_over, SUM(logs.win_lose_total) AS total_win_lose, SUM(logs.valid_amount_total) AS total_diff_amount"
	if req.TypeName == "casino" {
		selectedFields1 = "SUM(logs.turn_casino) AS total_turn_over, SUM(logs.win_lose_casino) AS total_win_lose, SUM(logs.valid_amount_casino) AS total_diff_amount"
	} else if req.TypeName == "sport" {
		selectedFields1 = "SUM(logs.turn_sport) AS total_turn_over, SUM(logs.win_lose_sport) AS total_win_lose, SUM(logs.valid_amount_sport) AS total_diff_amount"
	} else if req.TypeName == "game" {
		selectedFields1 = "SUM(logs.turn_game) AS total_turn_over, SUM(logs.win_lose_game) AS total_win_lose, SUM(logs.valid_amount_game) AS total_diff_amount"
	}
	sql := r.db.Table("play_log as logs")
	sql = sql.Select(selectedFields1)

	if req.MemberCode != "" {
		searchText := "%" + strings.TrimSpace(req.MemberCode) + "%"
		sql = sql.Where("logs.player LIKE ?", searchText)
	}
	if req.OfDate != "" {
		// ofdate = 2021-01-01 => from 2021-01-01 to 2021-01-31
		startAt, err := time.Parse("2006-01-02", req.OfDate)
		if err != nil {
			return nil, err
		}
		firstOfMonth := time.Date(startAt.Year(), startAt.Month(), 1, 0, 0, 0, 0, time.UTC)
		lastOfMonth := firstOfMonth.AddDate(0, 1, -1)
		sql = sql.Where("logs.date >= ? ", firstOfMonth.Format("2006-01-02"))
		sql = sql.Where("logs.date <=  ?", lastOfMonth.Format("2006-01-02"))
	}
	if err = sql.Scan(&report).Error; err != nil {
		return nil, err
	}

	return &report, nil
}

func (r repo) GetUserWinLoseDailyList(req model.UserWinLoseDailyListRequest) ([]model.UserWinLoseDailyResponse, int64, error) {

	var list []model.UserWinLoseDailyResponse
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //

	count := r.db.Table("play_log as tb_log")
	count = count.Select("tb_log.date")
	if req.MemberCode != "" {
		searchText := "%" + strings.TrimSpace(req.MemberCode) + "%"
		count = count.Where("tb_log.player LIKE ?", searchText)
	}
	if req.OfDate != "" {
		// ofdate = 2021-01-01 => from 2021-01-01 to 2021-01-31
		startAt, err := time.Parse("2006-01-02", req.OfDate)
		if err != nil {
			return nil, 0, err
		}
		firstOfMonth := time.Date(startAt.Year(), startAt.Month(), 1, 0, 0, 0, 0, time.UTC)
		lastOfMonth := firstOfMonth.AddDate(0, 1, -1)
		count = count.Where("tb_log.date >= ? ", firstOfMonth.Format("2006-01-02"))
		count = count.Where("tb_log.date <=  ?", lastOfMonth.Format("2006-01-02"))
	}
	count = count.Group("tb_log.date")
	if err = count.Count(&total).Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_log.date AS of_date, SUM(tb_log.turn_total) AS total_turn_over, SUM(tb_log.win_lose_total) AS total_win_lose, SUM(tb_log.valid_amount_total) AS total_diff_amount"
		if req.TypeName == "casino" {
			selectedFields = "tb_log.date AS of_date, SUM(tb_log.turn_casino) AS total_turn_over, SUM(tb_log.win_lose_casino) AS total_win_lose, SUM(tb_log.valid_amount_casino) AS total_diff_amount"
		} else if req.TypeName == "sport" {
			selectedFields = "tb_log.date AS of_date, SUM(tb_log.turn_sport) AS total_turn_over, SUM(tb_log.win_lose_sport) AS total_win_lose, SUM(tb_log.valid_amount_sport) AS total_diff_amount"
		} else if req.TypeName == "game" {
			selectedFields = "tb_log.date AS of_date, SUM(tb_log.turn_game) AS total_turn_over, SUM(tb_log.win_lose_game) AS total_win_lose, SUM(tb_log.valid_amount_game) AS total_diff_amount"
		}
		sql := r.db.Table("play_log as tb_log")
		sql = sql.Select(selectedFields)

		if req.MemberCode != "" {
			searchText := "%" + strings.TrimSpace(req.MemberCode) + "%"
			sql = sql.Where("tb_log.player LIKE ?", searchText)
		}
		if req.OfDate != "" {
			// ofdate = 2021-01-01 => from 2021-01-01 to 2021-01-31
			startAt, err := time.Parse("2006-01-02", req.OfDate)
			if err != nil {
				return nil, 0, err
			}
			firstOfMonth := time.Date(startAt.Year(), startAt.Month(), 1, 0, 0, 0, 0, time.UTC)
			lastOfMonth := firstOfMonth.AddDate(0, 1, -1)
			sql = sql.Where("tb_log.date >= ? ", firstOfMonth.Format("2006-01-02"))
			sql = sql.Where("tb_log.date <=  ?", lastOfMonth.Format("2006-01-02"))
		}
		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			// "totalTurnOver", "totalValidAmount", "diffAmount", "totalWinLose"}
			allowSortCols := map[string]string{
				"totalTurnOver":    "total_turn_over",
				"totalValidAmount": "total_diff_amount",
				"totalDiffAmount":  "total_diff_amount",
				"totalWinLose":     "total_win_lose",
			}
			if colName, ok := allowSortCols[req.SortCol]; ok {
				if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
					req.SortAsc = "DESC"
				} else {
					req.SortAsc = "ASC"
				}
				sql = sql.Order(colName + " " + req.SortAsc)
			}
		} else {
			sql = sql.Order("tb_log.date ASC")
		}
		if req.Limit > 0 {
			sql = sql.Limit(req.Limit)
		}
		sql = sql.Group("tb_log.date")
		sql = sql.Offset(req.Page * req.Limit)
		if err = sql.Scan(&list).Error; err != nil {
			return nil, 0, err
		}

	}
	return list, total, nil
}

func (r repo) GetUserTodayWinLoseSummary(req model.UserTodayWinLoseSummaryReportRequest) (*model.UserWinLoseSummaryReportResponse, error) {

	var report model.UserWinLoseSummaryReportResponse
	var err error

	actionAt := time.Now().UTC()
	loc := time.FixedZone("UTC+7", 7*60*60)
	dateBkk := actionAt.In(loc).Format("2006-01-02")

	selectedFields1 := "SUM(logs.turn_total) AS total_turn_over, SUM(logs.win_lose_total) AS total_win_lose, SUM(logs.valid_amount_total) AS total_diff_amount"
	if req.TypeName == "casino" {
		selectedFields1 = "SUM(logs.turn_casino) AS total_turn_over, SUM(logs.win_lose_casino) AS total_win_lose, SUM(logs.valid_amount_casino) AS total_diff_amount"
	} else if req.TypeName == "sport" {
		selectedFields1 = "SUM(logs.turn_sport) AS total_turn_over, SUM(logs.win_lose_sport) AS total_win_lose, SUM(logs.valid_amount_sport) AS total_diff_amount"
	} else if req.TypeName == "game" {
		selectedFields1 = "SUM(logs.turn_game) AS total_turn_over, SUM(logs.win_lose_game) AS total_win_lose, SUM(logs.valid_amount_game) AS total_diff_amount"
	}
	sql := r.db.Table("user_today_playlog as logs")
	sql = sql.Select(selectedFields1)
	sql = sql.Joins("LEFT JOIN user as tb_user ON tb_user.id = logs.user_id")
	if req.MemberCode != "" {
		searchText := "%" + strings.TrimSpace(req.MemberCode) + "%"
		sql = sql.Where("tb_user.member_code LIKE ?", searchText)
	}
	sql = sql.Where("logs.statement_date = ? ", dateBkk)
	if err = sql.Scan(&report).Error; err != nil {
		return nil, err
	}
	return &report, nil
}

func (r repo) GetUserTodayWinLoseSummaryList(req model.UserTodayWinLoseSummaryListRequest) ([]model.UserWinLoseSummaryResponse, int64, error) {

	var list []model.UserWinLoseSummaryResponse
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	actionAt := time.Now().UTC()
	loc := time.FixedZone("UTC+7", 7*60*60)
	dateBkk := actionAt.In(loc).Format("2006-01-02")

	count := r.db.Table("user_today_playlog as logs")
	count = count.Select("logs.user_id")
	count = count.Joins("LEFT JOIN user as tb_user ON tb_user.id = logs.user_id")
	count = count.Where("tb_user.member_code != ?", "")
	// count = count.Having(r.db.Where("SUM(logs.turn_total) > 0").Or("SUM(logs.win_lose_total) > 0").Or("SUM(logs.valid_amount_total) > 0"))
	if req.MemberCode != "" {
		searchText := "%" + strings.TrimSpace(req.MemberCode) + "%"
		count = count.Where("tb_user.member_code LIKE ?", searchText)
	}
	count = count.Where("logs.statement_date = ? ", dateBkk)
	count = count.Group("logs.user_id")
	if err = count.Count(&total).Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "logs.user_id, SUM(logs.turn_total) AS total_turn_over, SUM(logs.win_lose_total) AS total_win_lose, SUM(logs.valid_amount_total) AS diff_amount"
		if req.TypeName == "casino" {
			selectedFields = "logs.user_id, SUM(logs.turn_casino) AS total_turn_over, SUM(logs.win_lose_casino) AS total_win_lose, SUM(logs.valid_amount_casino) AS diff_amount"
		} else if req.TypeName == "sport" {
			selectedFields = "logs.user_id, SUM(logs.turn_sport) AS total_turn_over, SUM(logs.win_lose_sport) AS total_win_lose, SUM(logs.valid_amount_sport) AS diff_amount"
		} else if req.TypeName == "game" {
			selectedFields = "logs.user_id, SUM(logs.turn_game) AS total_turn_over, SUM(logs.win_lose_game) AS total_win_lose, SUM(logs.valid_amount_game) AS diff_amount"
		}
		sql := r.db.Table("user_today_playlog as logs")
		sql = sql.Select(selectedFields)
		sql = sql.Joins("LEFT JOIN user as tb_user ON tb_user.id = logs.user_id")
		sql = sql.Where("tb_user.member_code != ?", "")

		if req.MemberCode != "" {
			searchText := "%" + strings.TrimSpace(req.MemberCode) + "%"
			sql = sql.Where("tb_user.member_code LIKE ?", searchText)
		}
		sql = sql.Where("logs.statement_date = ? ", dateBkk)
		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			// "totalTurnOver", "totalValidAmount", "diffAmount", "totalWinLose"}
			allowSortCols := map[string]string{
				"totalTurnOver":    "total_turn_over",
				"totalValidAmount": "diff_amount",
				"diffAmount":       "diff_amount",
				"totalWinLose":     "total_win_lose",
			}
			if colName, ok := allowSortCols[req.SortCol]; ok {
				if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
					req.SortAsc = "DESC"
				} else {
					req.SortAsc = "ASC"
				}
				sql = sql.Order(colName + " " + req.SortAsc)
			}
		}
		if req.Limit > 0 {
			sql = sql.Limit(req.Limit)
		}
		sql = sql.Group("logs.user_id")
		sql = sql.Offset(req.Page * req.Limit)
		if err = sql.Scan(&list).Error; err != nil {
			return nil, 0, err
		}

		// APPEND membercode
		userIds := []int64{}
		for _, v := range list {
			userIds = append(userIds, v.UserId)
		}
		if len(userIds) > 0 {
			// เอามาจาก user
			var userInfo []struct {
				UserId     int64  `json:"userId"`
				MemberCode string `json:"memberCode"`
			}
			selectedFields1 := "tb_user.id as user_id, tb_user.member_code as member_code"
			query1 := r.db.Table("user as tb_user")
			query1 = query1.Select(selectedFields1)
			query1 = query1.Where("tb_user.id IN ?", userIds)
			if err = query1.Scan(&userInfo).Error; err != nil {
				return nil, 0, err
			}

			// MERGE DATA BY USER_ID
			for index, item := range list {
				for _, v2 := range userInfo {
					if item.UserId == v2.UserId {
						list[index].MemberCode = v2.MemberCode
						break
					}
				}
			}
		}

	}
	return list, total, nil
}

func (r repo) GetUserWinLoseSummaryByUserList(req model.UserWinLoseSummaryListRequest) ([]model.UserWinLoseSummaryResponse, int64, error) {

	var list []model.UserWinLoseSummaryResponse
	var total int64
	var err error

	// [20240305]  ปกติข้อมูลจะเอาทุก User รวมถึงข้อมูลที่เป็น 0 ด้วย จึงต้องดึงข้อมูล User ทั้งหมดก่อน
	// ปัญหา จะทำให้ sort ผลรวมจาก play_log ไม่ได้
	// แก้ฟังชั่น แล้ว backup fn นี้ไว้ก่อน เพราะ fn ใหม่ จะไม่แสดง user ที่ไม่มีข้อมูล

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("user as users")
	count = count.Select("users.id")
	count = count.Where("users.member_code != ?", "")
	if req.MemberCode != "" {
		searchText := "%" + strings.TrimSpace(req.MemberCode) + "%"
		count = count.Where("users.member_code LIKE ?", searchText)
	}

	if err = count.
		Where("users.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "users.id as user_id, users.member_code as member_code"
		query := r.db.Table("user as users")
		query = query.Select(selectedFields)
		query = query.Where("users.member_code != ?", "")
		if req.MemberCode != "" {
			searchText := "%" + strings.TrimSpace(req.MemberCode) + "%"
			query = query.Where("users.member_code LIKE ?", searchText)
		}
		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Where("users.deleted_at IS NULL").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}

		// APPEND TOTAL DATA
		userIds := []int64{}
		for _, v := range list {
			userIds = append(userIds, v.UserId)
		}

		// เอามาจาก PlayLog
		var userPlayLog []struct {
			UserId           int64   `json:"userId"`
			TotalTurnOver    float64 `json:"totalTurnOver"`
			TotalWinLose     float64 `json:"totalWinLose"`
			TotalValidAmount float64 `json:"totalValidAmount"`
		}
		selectedFields1 := "logs.user_id, SUM(logs.turn_total) AS total_turn_over, SUM(logs.win_lose_total) AS total_win_lose, SUM(logs.valid_amount_total) AS total_valid_amount"
		if req.TypeName == "casino" {
			selectedFields1 = "logs.user_id, SUM(logs.turn_casino) AS total_turn_over, SUM(logs.win_lose_casino) AS total_win_lose, SUM(logs.valid_amount_casino) AS total_valid_amount"
		} else if req.TypeName == "sport" {
			selectedFields1 = "logs.user_id, SUM(logs.turn_sport) AS total_turn_over, SUM(logs.win_lose_sport) AS total_win_lose, SUM(logs.valid_amount_sport) AS total_valid_amount"
		} else if req.TypeName == "game" {
			selectedFields1 = "logs.user_id, SUM(logs.turn_game) AS total_turn_over, SUM(logs.win_lose_game) AS total_win_lose, SUM(logs.valid_amount_game) AS total_valid_amount"
		}
		query1 := r.db.Table("play_log as logs").
			Select(selectedFields1).
			Where("logs.user_id IN ?", userIds).
			Group("logs.user_id")

		if req.FromDate != "" {
			query1 = query1.Where("logs.date >= ? ", req.FromDate)
		}
		if req.ToDate != "" {
			query1 = query1.Where("logs.date <=  ?", req.ToDate)
		}
		if err = query1.Scan(&userPlayLog).Error; err != nil {
			return nil, 0, err
		}

		// MERGE DATA BY USER_ID
		for index, item := range list {
			for _, v2 := range userPlayLog {
				if item.UserId == v2.UserId {
					list[index].TotalTurnOver = v2.TotalTurnOver
					list[index].TotalWinLose = v2.TotalWinLose
					list[index].DiffAmount = v2.TotalValidAmount
					break
				}
			}
		}

	}
	return list, total, nil
}
