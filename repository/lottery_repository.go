package repository

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"time"

	"gorm.io/gorm"
)

func NewLotteryRepository(db *gorm.DB) LotteryRepository {
	return &repo{db}
}

type LotteryRepository interface {
	// PlayLog
	LotteryGetApistatus(req model.ApiStatusRequest) (*model.ApiStatus, error)
	LotterySimpleWinLose(data model.LotteryPlaylogRequest) (*model.LotteryPlaylogResponse, error)
	InsertLotteryApiStatus(path, date string) error
	InsertLotteryPlayLog(data []model.AgentPlayLog, path string, page int, success bool) error
	LotteryUpdateFailed(id int64, page int) error
	LotteryUpdateSuccess(id int64) error
}

func (r repo) LotteryGetApistatus(req model.ApiStatusRequest) (*model.ApiStatus, error) {

	var record model.ApiStatus

	selectedFields := "statuses.id as id, statuses.path as path, statuses.page as page, statuses.is_failed as is_failed, statuses.is_success as is_success"
	selectedFields += ", statuses.statement_date as statement_date, statuses.created_at as created_at, statuses.updated_at as updated_at"
	if err := r.db.Table("api_status as statuses").
		Select(selectedFields).
		Where("statuses.path = ?", req.Path).
		Where("statuses.statement_date = ?", req.StatementDate).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) InsertLotteryApiStatus(path, date string) error {

	var id int64

	if err := r.db.Table("api_status").
		Select("id").
		Where("statement_date = ?", date).
		Where("path = ?", path).
		Where("is_success = ?", 0).
		Scan(&id).Error; err != nil {
		return err
	}

	// NOT_FOUND
	if id <= 0 {
		data := map[string]interface{}{}
		data["path"] = path
		data["statement_date"] = date
		if err := r.db.Table("api_status").
			Create(&data).Error; err != nil {
			return err
		}
	}
	return nil
}

func (r repo) LotterySimpleWinLose(data model.LotteryPlaylogRequest) (*model.LotteryPlaylogResponse, error) {

	// log.Println("LotterySimpleWinLose req ------> ", helper.StructJson(data))

	var result model.LotteryPlaylogResponse

	playlogEp := os.Getenv("LOTTERY_ENDPOINT")
	playlogKey := os.Getenv("AGENT_NAME")
	if playlogEp == "DEBUG" && playlogKey == "MOCK" {

		if data.Page == 0 {
			for i := 0; i < 10; i++ {
				result.Content = append(result.Content, model.LotteryPlaylogContent{
					UserId:      89181,
					TurnOver:    1300 + float64(i),
					ValidAmount: 1700 + float64(i),
					WinLoss:     500 + float64(i),
				})
			}
		}
		result.NumberOfElements = int64(len(result.Content))
	}
	if playlogEp == "" || playlogKey == "" {
		return &result, nil
	}

	if data.Page > 0 {
		data.Page = data.Page - 1
	}
	if data.Page < 0 {
		data.Page = 0
	}

	// curl -X 'GET' \
	// 'https://api-wallet.lotto29.com/api/v1/cybergame/playLog?date=2024-09-30&size=2&page=0' \
	// -H 'accept: */*' \
	// -H 'api-key: zta68pk5'

	url := fmt.Sprintf("%s/cybergame/playLog?date=%s&size=%d&page=%d", playlogEp, data.Date, data.Size, data.Page)
	fmt.Println("LotterySimpleWinLose.url ------> ", url)

	client := &http.Client{}
	client.Timeout = 3 * time.Second
	reqExternal, _ := http.NewRequest("GET", url, nil)
	reqExternal.Header.Set("Content-Type", "application/json")
	reqExternal.Header.Set("Accept", "application/json")
	reqExternal.Header.Set("api-key", playlogKey)
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println("LotterySimpleWinLose.CLIENT_CALL_ERROR", err.Error())
		return nil, errors.New("CLIENT_CALL_ERROR")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println("LotterySimpleWinLose.RESPONSE_READ_ERROR", err)
		return nil, errors.New("RESPONSE_READ_ERROR")
	}
	if response.StatusCode != 200 {
		log.Println("LotterySimpleWinLose.HTTP_NOT_200", response.StatusCode)
		log.Println("LotterySimpleWinLose.response_data", string(responseData))
		return nil, errors.New("INVALID_RESPONSE_CODE")
	}
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("LotterySimpleWinLose.CANT_PARSE_RESPONSE_DATA ------> ", string(responseData))
		return nil, errors.New("CANT_PARSE_RESPONSE_DATA")
	}
	fmt.Println("LotterySimpleWinLose.result ------> ", helper.StructJson(result))
	return &result, nil
}

func (r repo) InsertLotteryPlayLog(data []model.AgentPlayLog, path string, page int, success bool) error {

	tx := r.db.Begin()

	if err := tx.Table("play_log").
		Create(&data).Error; err != nil {
		tx.Rollback()
		return err
	}

	// obj := map[string]interface{}{}
	// obj["is_failed"] = 0
	// obj["page"] = page

	// if err := tx.Table("api_status").
	// 	Where("path = ?", path).
	// 	Where("is_success = ?", 0).
	// 	Updates(obj).Error; err != nil {
	// 	tx.Rollback()
	// 	return err
	// }

	// if success {
	// 	if err := tx.Table("api_status").
	// 		Where("path = ?", path).
	// 		Where("is_failed = ?", 0).
	// 		Where("is_success = ?", 0).
	// 		Update("is_success", 1).
	// 		Error; err != nil {
	// 		tx.Rollback()
	// 		return err
	// 	}
	// }

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}

	return nil
}

func (r repo) LotteryUpdateSuccess(id int64) error {

	obj := map[string]interface{}{}
	obj["is_success"] = 1

	if err := r.db.Table("api_status").
		Where("id = ?", id).
		Where("is_success = ?", 0).
		Where("is_failed = ?", 0).
		Updates(obj).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) LotteryUpdateFailed(id int64, page int) error {

	obj := map[string]interface{}{}
	obj["is_failed"] = 1
	obj["page"] = page

	if err := r.db.Table("api_status").
		Where("id = ?", id).
		Where("is_success = ?", 0).
		Where("is_failed = ?", 0).
		Updates(obj).Error; err != nil {
		return err
	}
	return nil
}
