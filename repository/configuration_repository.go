package repository

import (
	"cybergame-api/model"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"time"

	"github.com/go-resty/resty/v2"
	"gorm.io/gorm"
)

func NewConfigurationRepository(db *gorm.DB) ConfigurationRepository {
	return &repo{db}
}

var configurationMain *model.ConfigurationResponse
var configurationWeb *model.GetWebConfigurationBody
var configurationLine *model.GetContactConfigWebResponse
var configurationBankLimit *model.BankLimitConfigurationResponse

type ConfigurationRepository interface {
	//configuration web options backoffice
	GetAutoUserApproveType() ([]model.AutoUserApproveType, error)
	GetAutoWithdrawType() ([]model.AutoWithdrawType, error)
	GetTurnWithdrawType() ([]model.TurnWithdrawType, error)

	ClearAllConfigurationCache() error
	// configuration web backoffice
	GetConfiguration() (*model.ConfigurationResponse, error)
	UpdateConfiguration(body model.UpdateConfigurationBody) error
	CreateConfiguration(body model.CreateConfigurationBody) (*int64, error)
	// configuration การตั้งค่าจำกัดการถอนสูงสุด
	GetBankLimitConfiguration() (*model.BankLimitConfigurationResponse, error)
	UpdateBankLimitConfiguration(body model.UpdateBankLimitConfigurationBody) error
	CreateBankLimitConfiguration(body model.CreateBankLimitConfigurationBody) (*int64, error)
	GetUserBankLimit(userId int64) (*model.UserBankLimitResponse, error)
	// web configuration
	GetWebConfiguration() (*model.GetWebConfigurationBody, error)
	GetContactConfig() (*model.GetContactConfigWebResponse, error)
	GetWebBotaccountWebConfigs() (*model.BotAccountConfig, error)

	// cloudflare
	UploadImageToCloudflare(pathUplaod string, filename string, fileReader io.Reader) (*model.CloudFlareUploadCreateBody, error)

	//s3
	UploadImageToS3(pathUpload string, filename string, fileReader io.Reader) (*model.FileUploadResponse, error)

	// agent game priority setting
	GetAgentGamePrioritySettingList(req model.GetAgentGamePrioritySettingListRequest) ([]model.AgentGamePriorityListSetting, int64, error)
	SortGamegentGamePriorityOrder(req model.DragSortRequest) error
	IncrementAgentGameTotalPlayed(vendorCode string) error
	GetAgentGamePrioritySetting(categoryName string, isActiveOnly bool) ([]model.AgentGamePriority, error)
	UpdateAgentGamePrioritySetting(body model.UpdateAgentGamePrioritySettingBody) error

	CreateAdminAction(body model.AdminActionCreateBody) (*int64, error)

	// setting banner
	CreateBannerSetting(body model.CreateBannerSettingBody) error
	UpdateBannerSetting(id int64, body model.BannerSettingUpdateBody) error
	GetPublicBannerSettingList(req model.GetBannerSettingListRequest) ([]model.GetBannerSettingListResponse, error)
	GetBannerSettingList(req model.GetBannerSettingListRequest) ([]model.GetBannerSettingListResponse, error)

	DeleteBannerSetting(body model.DeleteBannerSettingBody) error

	GetConfigurationCheckShowTransactionResponse() (*model.ConfigurationCheckShowTransactionResponse, error)

	// set register config
	GetConfigurationRegisterFormat() ([]model.SelectOptions, error)

	// activity menu
	UpdateActivityMenu(body model.UpdateActivityMenuRequest) error
	SortActivityMenu(req model.DragSortRequest) error
	GetActivityMenu(req model.GetActivityMenuRequest) ([]model.GetActivityMenuResponse, error)
	GetActivityMenuItem(req model.GetActivityMenuItemRequest) (*model.GetActivityMenuResponse, error)

	GetRunningMessage() (*model.RunningMessageResponse, error)
}

func (r *repo) GetAutoUserApproveType() ([]model.AutoUserApproveType, error) {

	var options []model.AutoUserApproveType
	var err error

	selectedFields := "id, name, label_th, label_en"
	sql := r.db.Table("auto_user_approve_type")
	sql = sql.Select(selectedFields)

	if err := sql.Find(&options).Error; err != nil {
		return nil, err
	}
	return options, err
}

func (r *repo) GetAutoWithdrawType() ([]model.AutoWithdrawType, error) {

	var options []model.AutoWithdrawType
	var err error

	selectedFields := "id, name, label_th, label_en"
	sql := r.db.Table("auto_withdraw_type")
	sql = sql.Select(selectedFields)

	if err := sql.Find(&options).Error; err != nil {
		return nil, err
	}
	return options, err
}

func (r *repo) GetTurnWithdrawType() ([]model.TurnWithdrawType, error) {

	var options []model.TurnWithdrawType
	var err error

	selectedFields := "id, name, label_th, label_en"
	sql := r.db.Table("turn_withdraw_type")
	sql = sql.Select(selectedFields)

	if err := sql.Find(&options).Error; err != nil {
		return nil, err
	}
	return options, err
}

func (r *repo) GetConfiguration() (*model.ConfigurationResponse, error) {

	var record model.ConfigurationResponse

	if configurationMain != nil && time.Now().Before(configurationMain.CacheExpiredAt) {
		return configurationMain, nil
	}

	selectedFields := "con.id AS id, con.logo_url AS logo_url, con.background_color AS background_color, con.use_otp_register AS use_otp_register"
	selectedFields += ", con.allow_online_registration AS allow_online_registration, con.allow_online_register_form AS allow_online_register_form, con.check_account_name_fastbank AS check_account_name_fastbank"
	selectedFields += ", con.minimum_deposit AS minimum_deposit, con.id_line AS id_line, con.url_line AS url_line"
	selectedFields += ", upt.id AS auto_user_approve_type_id, upt.name AS auto_user_approve_type, upt.label_th AS auto_user_approve_type_label_th"
	// selectedFields += ", awt.name AS auto_withdraw_type, awt.label_th AS auto_withdraw_type_label_th"
	selectedFields += ", twt.id AS turn_withdraw_type_id, twt.name AS turn_withdraw_type, twt.label_th AS turn_withdraw_type_label_th"
	selectedFields += ", con.web_name AS web_name, con.check_phone_captcha_len AS check_phone_captcha_len, con.open_game_new_tab AS open_game_new_tab, con.show_web_aff_name AS show_web_aff_name"
	selectedFields += ", con.minimum_withdraw AS minimum_withdraw, con.min_first_member_deposit AS min_first_member_deposit"
	selectedFields += ", con.clear_turn_credit_less AS clear_turn_credit_less, con.use_upload_deposit_slip AS use_upload_deposit_slip, con.upload_deposit_slip_type AS upload_deposit_slip_type"
	selectedFields += ", con.use_th_currency AS use_th_currency, con.use_laos_currency AS use_laos_currency"
	selectedFields += ", con.id_whatsapp AS id_whatsapp, con.url_whatsapp AS url_whatsapp"
	selectedFields += ", con.withdraw_maximum_auto AS withdraw_maximum_auto, con.withdraw_maximum AS withdraw_maximum"
	selectedFields += ", con.id_telegram AS id_telegram, con.url_telegram AS url_telegram"
	selectedFields += ", con.is_totp_verify AS is_totp_verify"
	selectedFields += ", con.token_expired_minute AS token_expired_minute"
	selectedFields += ", con.is_show_deposit AS is_show_deposit, con.is_show_withdraw AS is_show_withdraw"
	selectedFields += ", con.configuration_register_format_id AS configuration_register_format_id"

	sql := r.db.Table("configuration_web AS con")
	sql = sql.Joins("JOIN auto_user_approve_type AS upt ON con.auto_user_approve_type_id = upt.id")
	// sql = sql.Joins("JOIN auto_withdraw_type AS awt ON con.auto_withdraw_type_id = awt.id")
	sql = sql.Joins("JOIN turn_withdraw_type AS twt ON con.turn_withdraw_type_id = twt.id")
	sql = sql.Select(selectedFields)

	if err := sql.Take(&record).Error; err != nil {
		if err.Error() == "record not found" {
			autoUserApproveTypeId := int64(2)
			defaultOpen := true
			defaultClose := false
			turnWithdrawTypeId := int64(1)
			minFirstMemberDeposit := int64(0)
			checkPhoneCaptchaLen := int64(0)
			withdrawMaximumAuto := float64(0)
			withdrawMaximum := float64(49999)
			defaultUploadDepositSlipType := model.CONFIG_UPLOAD_DEPOSIT_SLIP_TYPE_DISABLED
			useThCurrency := true
			setIsTotpVerify := false
			setTokenExpiredHour := int64(8)
			setConfigRegisterFormat := int64(1)

			var createBody model.CreateConfigurationBody
			createBody.AutoUserApproveTypeId = &autoUserApproveTypeId
			createBody.UseOtpRegister = &defaultClose
			createBody.TurnWithdrawTypeId = &turnWithdrawTypeId
			createBody.AllowOnlineRegistration = &defaultOpen
			createBody.AllowOnlineRegisterForm = &defaultOpen
			createBody.CheckAccountNameFastbank = &defaultClose
			createBody.MinFirstMemberDeposit = &minFirstMemberDeposit
			createBody.CheckPhoneCaptchaLen = &checkPhoneCaptchaLen
			createBody.OpenGameNewTab = &defaultClose
			createBody.UseUploadDepositSlip = &defaultClose
			createBody.UploadDepositSlipType = &defaultUploadDepositSlipType
			createBody.ShowWebAffName = &defaultClose
			createBody.UseThCurrency = &useThCurrency
			createBody.WithdrawMaximumAuto = &withdrawMaximumAuto
			createBody.WithdrawMaximum = &withdrawMaximum
			createBody.IsTotpVerify = &setIsTotpVerify
			createBody.TokenExpiredMinute = &setTokenExpiredHour
			createBody.IsShowDeposit = &defaultOpen
			createBody.IsShowWithdraw = &defaultOpen
			createBody.ConfigurationRegisterFormatId = &setConfigRegisterFormat

			if err := r.db.Table("configuration_web").Create(&createBody).Error; err != nil {
				return nil, err
			}

			if err := sql.Take(&record).Error; err != nil {
				return nil, err
			}

		}
		return nil, err
	}

	// Set Cache
	configurationMain = &record
	configurationMain.CacheExpiredAt = time.Now().Add(60 * time.Minute)

	return &record, nil
}

func (r *repo) GetUserRegisterConfiguration() (*model.ConfigurationUserRegisterResponse, error) {

	var configuration model.ConfigurationUserRegisterResponse

	selectedFields := "con.id AS id, con.use_otp_register AS use_otp_register, con.allow_online_register_form AS allow_online_register_form, con.check_account_name_fastbank AS check_account_name_fastbank"
	selectedFields += ", con.configuration_register_format_id AS configuration_register_format_id"
	sql := r.db.Table("configuration_web AS con")
	sql = sql.Select(selectedFields)
	if err := sql.Take(&configuration).Error; err != nil {
		return nil, err
	}
	return &configuration, nil
}

func (r *repo) ClearAllConfigurationCache() error {

	// CACHE //
	configurationBankLimit = nil
	// Clear Cache = GetNewData Later
	configurationMain = nil
	configurationWeb = nil
	configurationLine = nil

	// confActivityLuckyWheelSetting
	confActivityLuckyWheelSetting = nil
	confActivityDailyResponse = nil
	promotionReturnCuttypeOptions = nil
	promotionReturnSetting = nil
	promotionReturnTurnCuttypeOptions = nil
	promotionReturnTurnSetting = nil

	// Payment
	// paygateSetting = nil
	// hengMerchant = nil
	// luckyMerchant = nil

	// AF
	afCommissionResponse = nil

	return nil
}

func (r *repo) UpdateConfiguration(body model.UpdateConfigurationBody) error {

	updateData := make(map[string]interface{})

	if body.UseOtpRegister != nil {
		updateData["use_otp_register"] = body.UseOtpRegister
	}
	if body.AllowOnlineRegistration != nil {
		updateData["allow_online_registration"] = body.AllowOnlineRegistration
	}
	if body.AllowOnlineRegisterForm != nil {
		updateData["allow_online_register_form"] = body.AllowOnlineRegisterForm
	}
	if body.CheckAccountNameFastbank != nil {
		updateData["check_account_name_fastbank"] = body.CheckAccountNameFastbank
	}
	if body.LogoUrl != nil {
		updateData["logo_url"] = body.LogoUrl
	}
	if body.WebName != nil {
		updateData["web_name"] = body.WebName
	}
	if body.BackgroundColor != nil {
		updateData["background_color"] = body.BackgroundColor
	}
	if body.AutoUserApproveTypeId != nil {
		updateData["auto_user_approve_type_id"] = body.AutoUserApproveTypeId
	}
	if body.TurnWithdrawTypeId != nil {
		updateData["turn_withdraw_type_id"] = body.TurnWithdrawTypeId
	}
	if body.MinimumDeposit != nil {
		updateData["minimum_deposit"] = body.MinimumDeposit
	}
	if body.MinimumWithdraw != nil {
		updateData["minimum_withdraw"] = body.MinimumWithdraw
	}
	if body.MinFirstMemberDeposit != nil {
		updateData["min_first_member_deposit"] = body.MinFirstMemberDeposit
	}
	if body.IdLine != nil {
		updateData["id_line"] = body.IdLine
	}
	if body.UrlLine != nil {
		updateData["url_line"] = body.UrlLine
	}
	if body.CheckPhoneCaptchaLen != nil {
		updateData["check_phone_captcha_len"] = body.CheckPhoneCaptchaLen
	}
	if body.OpenGameNewTab != nil {
		updateData["open_game_new_tab"] = body.OpenGameNewTab
	}
	if body.ClearTurnCreditLess != nil {
		updateData["clear_turn_credit_less"] = body.ClearTurnCreditLess
	}
	if body.UseUploadDepositSlip != nil {
		updateData["use_upload_deposit_slip"] = body.UseUploadDepositSlip
	}
	if body.UploadDepositSlipType != nil {
		if *body.UploadDepositSlipType == model.CONFIG_UPLOAD_DEPOSIT_SLIP_TYPE_AUTO {
			updateData["upload_deposit_slip_type"] = model.CONFIG_UPLOAD_DEPOSIT_SLIP_TYPE_AUTO
		} else if *body.UploadDepositSlipType == model.CONFIG_UPLOAD_DEPOSIT_SLIP_TYPE_FILE {
			updateData["upload_deposit_slip_type"] = model.CONFIG_UPLOAD_DEPOSIT_SLIP_TYPE_FILE
		} else {
			updateData["upload_deposit_slip_type"] = model.CONFIG_UPLOAD_DEPOSIT_SLIP_TYPE_DISABLED
		}
	}
	if body.ShowWebAffName != nil {
		updateData["show_web_aff_name"] = body.ShowWebAffName
	}
	if body.UseThCurrency != nil {
		updateData["use_th_currency"] = body.UseThCurrency
	}
	if body.UseLaosCurrency != nil {
		updateData["use_laos_currency"] = body.UseLaosCurrency
	}
	if body.IdWhatsapp != nil {
		updateData["id_whatsapp"] = body.IdWhatsapp
	}
	if body.UrlWhatsapp != nil {
		updateData["url_whatsapp"] = body.UrlWhatsapp
	}
	if body.WithdrawMaximumAuto != nil {
		updateData["withdraw_maximum_auto"] = body.WithdrawMaximumAuto
	}
	if body.WithdrawMaximum != nil {
		updateData["withdraw_maximum"] = body.WithdrawMaximum
	}
	if body.IdTelegram != nil {
		updateData["id_telegram"] = body.IdTelegram
	}
	if body.UrlTelegram != nil {
		updateData["url_telegram"] = body.UrlTelegram
	}

	if body.IsTotpVerify != nil {
		updateData["is_totp_verify"] = body.IsTotpVerify
	}

	if body.TokenExpiredMinute != nil {
		updateData["token_expired_minute"] = body.TokenExpiredMinute
	}

	if body.IsShowDeposit != nil {
		updateData["is_show_deposit"] = body.IsShowDeposit
	}

	if body.IsShowWithdraw != nil {
		updateData["is_show_withdraw"] = body.IsShowWithdraw
	}

	if body.ConfigurationRegisterFormatId != nil {
		updateData["configuration_register_format_id"] = body.ConfigurationRegisterFormatId
	}

	if err := r.db.Table("configuration_web").Where("id = ?", body.Id).Updates(updateData).Error; err != nil {
		return err
	}

	// Clear Cache = GetNewData Later
	configurationMain = nil
	configurationWeb = nil
	configurationLine = nil

	return nil
}

func (r *repo) CreateConfiguration(body model.CreateConfigurationBody) (*int64, error) {

	if err := r.db.Table("configuration_web").Create(&body).Error; err != nil {
		return nil, err
	}
	return body.Id, nil
}

func (r *repo) GetWebConfiguration() (*model.GetWebConfigurationBody, error) {

	var record model.GetWebConfigurationBody

	if configurationWeb != nil && time.Now().Before(configurationWeb.CacheExpiredAt) {
		return configurationWeb, nil
	}

	selectedFields := "configuration_web.minimum_deposit AS minimum_deposit, configuration_web.minimum_withdraw AS minimum_withdraw"
	selectedFields += ", configuration_web.min_first_member_deposit, configuration_web.use_upload_deposit_slip, configuration_web.upload_deposit_slip_type"
	sql := r.db.Table("configuration_web")
	sql = sql.Select(selectedFields)
	if err := sql.Take(&record).Error; err != nil {
		return nil, err
	}

	// Set Cache
	configurationWeb = &record
	configurationWeb.CacheExpiredAt = time.Now().Add(60 * time.Minute)

	return &record, nil
}

func (r *repo) GetBotaccountWebConfigs() (*model.BotAccountConfig, error) {

	var list model.BotAccountConfig

	// SELECT //
	selectedFields := "configs.id, configs.config_key, configs.config_val"
	query := r.db.Table("botaccount_config as configs")
	query = query.Select(selectedFields)
	if err := query.
		Where("configs.config_key = ?", "withdraw_max_per_time").
		Where("configs.deleted_at IS NULL").
		Take(&list).
		Error; err != nil {
		return nil, err
	}

	return &list, nil
}

func (r *repo) GetContactConfig() (*model.GetContactConfigWebResponse, error) {

	var record model.GetContactConfigWebResponse

	if configurationLine != nil && time.Now().Before(configurationLine.CacheExpiredAt) {
		return configurationLine, nil
	}

	selectedFields := "configuration_web.id_line AS id_line, configuration_web.url_line AS url_line"
	selectedFields += ", configuration_web.id_whatsapp AS id_whatsapp, configuration_web.url_whatsapp AS url_whatsapp"
	selectedFields += ", configuration_web.id_telegram AS id_telegram, configuration_web.url_telegram AS url_telegram"
	sql := r.db.Table("configuration_web")
	sql = sql.Select(selectedFields)
	if err := sql.Take(&record).Error; err != nil {
		return nil, err
	}

	// Set Cache
	configurationLine = &record
	configurationLine.CacheExpiredAt = time.Now().Add(60 * time.Minute)

	return &record, nil
}

func (r *repo) GetWebBotaccountWebConfigs() (*model.BotAccountConfig, error) {

	var list model.BotAccountConfig

	// SELECT //
	selectedFields := "configs.id, configs.config_key, configs.config_val"
	query := r.db.Table("botaccount_config as configs")
	query = query.Select(selectedFields)
	if err := query.
		Where("configs.config_key = ?", "withdraw_max_per_time").
		Where("configs.deleted_at IS NULL").
		Take(&list).
		Error; err != nil {
		return nil, err
	}
	return &list, nil
}

func (r *repo) GetUserBankLimit(userId int64) (*model.UserBankLimitResponse, error) {

	var record model.UserBankLimitResponse

	actionAtBkk := time.Now().UTC().In(time.FixedZone("UTC+7", 7*60*60))
	ofDate := actionAtBkk.Format("2006-01-02")

	// Only Withdraw
	limitWithDrawStatusList := []int64{
		model.TRANS_STATUS_WITHDRAW_APPROVED,
		model.TRANS_STATUS_WITHDRAW_PENDING,
		model.TRANS_STATUS_WITHDRAW_OVER_BUDGET,
		model.TRANS_STATUS_WITHDRAW_APPROVED,
		model.TRANS_STATUS_WITHDRAW_REJECTED,
		model.TRANS_STATUS_WITHDRAW_FAILED,
		model.TRANS_STATUS_WITHDRAW_SUCCESS,
		model.TRANS_STATUS_WITHDRAW_OVER_MAX,
		// model.TRANS_STATUS_WITHDRAW_CANCELED,
		model.TRANS_STATUS_WITHDRAW_UNSURE,
		model.TRANS_STATUS_WITHDRAW_TRASNFERING,
	}
	selectedFields := "SUM(tb_log.credit_amount) AS current_withdraw_amount"
	selectedFields += ", COUNT(tb_log.id) AS current_withdraw_count"
	sql := r.db.Table("bank_transaction as tb_log")
	sql = sql.Select(selectedFields)
	sql = sql.Where("tb_log.transaction_type_id = ?", model.TRANSACTION_TYPE_WITHDRAW)
	sql = sql.Where("tb_log.transaction_status_id IN (?)", limitWithDrawStatusList)
	sql = sql.Where("tb_log.user_id = ?", userId)
	if ofDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(ofDate)
		if err != nil {
			return nil, err
		}
		sql = sql.Where("tb_log.created_at >= ? ", startDateAtBkk)
		endDateAtBkk, err := r.ParseEodBkk(ofDate)
		if err != nil {
			return nil, err
		}
		sql = sql.Where("tb_log.created_at <=  ?", endDateAtBkk)
	}
	if err := sql.Take(&record).Error; err != nil {
		return nil, err
	}

	record.UserId = userId
	// No config == No Limit **Not Create**
	if setting, err := r.GetBankLimitConfiguration(); err == nil {
		record.MaxUserWithdrawAmount = setting.MaxUserWithdrawAmount
		record.MaxUserWithdrawCount = setting.MaxUserWithdrawCount
	}

	return &record, nil
}

func (r *repo) GetBankLimitConfiguration() (*model.BankLimitConfigurationResponse, error) {

	var record model.BankLimitConfigurationResponse

	if configurationBankLimit != nil {
		return configurationBankLimit, nil
	}

	selectedFields := "*"
	sql := r.db.Table("configuration_bank_limit")
	sql = sql.Select(selectedFields)
	if err := sql.Take(&record).Error; err != nil {
		return nil, err
	}

	// Set Cache
	configurationBankLimit = &record

	return &record, nil
}

func (r *repo) UpdateBankLimitConfiguration(body model.UpdateBankLimitConfigurationBody) error {

	if err := r.db.Table("configuration_bank_limit").Where("id = ?", body.Id).Updates(body).Error; err != nil {
		return err
	}

	// Clear Cache = GetNewData Later
	configurationBankLimit = nil

	return nil
}

func (r *repo) CreateBankLimitConfiguration(body model.CreateBankLimitConfigurationBody) (*int64, error) {

	if err := r.db.Table("configuration_bank_limit").Create(&body).Error; err != nil {
		return nil, err
	}
	return body.Id, nil
}

func (r repo) GetAgentGamePrioritySettingList(req model.GetAgentGamePrioritySettingListRequest) ([]model.AgentGamePriorityListSetting, int64, error) {

	var list []model.AgentGamePriorityListSetting
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("agent_game_priority_setting as tb")
	count = count.Select("tb.id")
	if req.Search != "" {
		count = count.Where("tb.vendor_code = ? OR tb.detail LIKE ?", req.Search, "%"+req.Search+"%")
	}
	if req.CategoryName != "" && req.CategoryName == "POPULAR" {
		count = count.Where("tb.is_popular = ?", 1)

	} else if req.CategoryName != "" {
		count = count.Where("tb.category_name = ?", req.CategoryName)
	}

	if err = count.
		Count(&total).
		Error; err != nil {
		return nil, 0, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb.id AS id, tb.vendor_code AS vendor_code, tb.priority_order AS priority_order"
		selectedFields += ", tb.agent AS agent"
		selectedFields += ", tb.detail AS detail"
		selectedFields += ", tb.category_name AS category_name"
		selectedFields += ", tb.total_played AS total_played, tb.last_played_at AS last_played_at"
		selectedFields += ", tb.created_at AS created_at, tb.updated_at AS updated_at, tb.updated_by_id AS updated_by_id"
		selectedFields += ", tb_admin.username AS updated_by_name"
		selectedFields += ", tb.is_popular AS is_popular, tb.is_show AS is_show"

		query := r.db.Table("agent_game_priority_setting as tb")
		query = query.Joins("LEFT JOIN admin AS tb_admin ON tb.updated_by_id = tb_admin.id")

		query = query.Select(selectedFields)
		if req.Search != "" {
			query = query.Where("tb.vendor_code = ? OR tb.detail LIKE ?", req.Search, "%"+req.Search+"%")
		}
		if req.CategoryName != "" && req.CategoryName == "POPULAR" {
			query = query.Where("tb.is_popular = ?", 1)

		} else if req.CategoryName != "" {
			query = query.Where("tb.category_name = ?", req.CategoryName)
		}

		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}

		if err = query.
			Order("tb.priority_order ASC").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}

	}

	return list, total, nil
}

func (r repo) GetAgentGamePrioritySetting(categoryName string, isActiveOnly bool) ([]model.AgentGamePriority, error) {

	var list []model.AgentGamePriority

	selectedFields := "tb.id AS id, tb.vendor_code AS vendor_code, tb.detail AS detail"
	selectedFields += ", tb.category_name AS category_name, tb.image_name AS image_name"
	selectedFields += ", tb.is_popular AS is_popular, tb.is_show AS is_show"
	selectedFields += ", tb.total_played AS total_played"

	query := r.db.Table("agent_game_priority_setting as tb")

	query = query.Select(selectedFields)

	if categoryName != "" && categoryName == "POPULAR" {
		query = query.Where("tb.is_popular = ?", 1)

	} else if categoryName != "" {
		query = query.Where("tb.category_name = ?", categoryName)
	}

	if isActiveOnly {
		query = query.Where("tb.is_show = ?", 1)
	}

	if err := query.
		Order("tb.priority_order ASC").
		Scan(&list).
		Error; err != nil {
		return nil, err
	}

	return list, nil
}

func (r repo) SortGamegentGamePriorityOrder(req model.DragSortRequest) error {

	var results []model.PrioritySortResponse

	selectedFields := "sort_rows.id, sort_rows.priority_order"
	query := r.db.Table("agent_game_priority_setting as sort_rows")
	query = query.Select(selectedFields)
	if err := query.
		Where("sort_rows.id IN ?", []int64{req.FromItemId, req.ToItemId}).
		Limit(2).
		Find(&results).
		Error; err != nil {
		return err
	}

	var fromItem *model.PrioritySortResponse
	var toItem *model.PrioritySortResponse
	for _, result := range results {
		if result.Id == req.FromItemId {
			fromItem = &model.PrioritySortResponse{
				Id:            result.Id,
				PriorityOrder: result.PriorityOrder,
			}
		} else if result.Id == req.ToItemId {

			toItem = &model.PrioritySortResponse{
				Id:            result.Id,
				PriorityOrder: result.PriorityOrder,
			}
		}
	}
	fmt.Printf("fromItem: %v, toItem: %v", fromItem, toItem)

	if fromItem != nil && toItem != nil {
		// Sort Direction //
		if fromItem.PriorityOrder < toItem.PriorityOrder {
			// Drag down  //
			whereShiftDown := r.db.Where("priority_order > ?", fromItem.PriorityOrder).Where("priority_order <= ?", toItem.PriorityOrder)
			if err := r.db.Table("agent_game_priority_setting").Where(whereShiftDown).Update("priority_order", gorm.Expr("priority_order - 1")).Error; err != nil {
				return err
			}
			// Go to
			if err := r.db.Table("agent_game_priority_setting").Where("id = ?", fromItem.Id).Update("priority_order", toItem.PriorityOrder).Error; err != nil {
				return err
			}
		} else if fromItem.PriorityOrder > toItem.PriorityOrder {
			// Drag up = shift up //
			whereShiftDown := r.db.Where("priority_order < ?", fromItem.PriorityOrder).Where("priority_order >= ?", toItem.PriorityOrder)
			if err := r.db.Table("agent_game_priority_setting").Where(whereShiftDown).Update("priority_order", gorm.Expr("priority_order + 1")).Error; err != nil {
				return err
			}
			// Go to
			if err := r.db.Table("agent_game_priority_setting").Where("id = ?", fromItem.Id).Update("priority_order", toItem.PriorityOrder).Error; err != nil {
				return err
			}
		}
	}
	return nil
}

func (r repo) IncrementAgentGameTotalPlayed(vendorCode string) error {

	if err := r.db.Table("agent_game_priority_setting").
		Where("vendor_code = ?", vendorCode).
		UpdateColumn("total_played", gorm.Expr("total_played + ?", 1)).
		Error; err != nil {
		return err
	}

	// update last pla
	if err := r.db.Table("agent_game_priority_setting").
		Where("vendor_code = ?", vendorCode).
		UpdateColumn("last_played_at", time.Now().UTC()).
		Error; err != nil {
		return err
	}

	return nil
}

func (r repo) UpdateAgentGamePrioritySetting(body model.UpdateAgentGamePrioritySettingBody) error {

	if err := r.db.Table("agent_game_priority_setting").Where("id = ?", body.Id).Updates(body).Error; err != nil {
		return err
	}

	return nil
}

func (r repo) CreateBannerSetting(body model.CreateBannerSettingBody) error {

	if err := r.db.Table("banner_setting").Create(&body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdateBannerSetting(id int64, body model.BannerSettingUpdateBody) error {

	if err := r.db.Table("banner_setting").Where("id = ?", id).Updates(body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetPublicBannerSettingList(req model.GetBannerSettingListRequest) ([]model.GetBannerSettingListResponse, error) {

	var list []model.GetBannerSettingListResponse

	selectedFields := "tb.id AS id, tb.lang AS lang, tb.image_url AS image_url, tb.link_url AS link_url"
	selectedFields += ", tb.is_show_logedin AS is_show_logedin, tb.is_show_public AS is_show_public"
	query := r.db.Table("banner_setting as tb")
	query = query.Select(selectedFields)
	if req.Lang != "" {
		query = query.Where("tb.lang = ?", req.Lang)
	}
	if req.ShowMode == "LOGEDIN" {
		query = query.Where("tb.is_show_logedin = ?", 1)
	} else {
		query = query.Where("tb.is_show_public = ?", 1)
	}
	if err := query.
		Where("tb.deleted_at IS NULL").
		Order("tb.id DESC").
		Scan(&list).
		Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (r repo) GetBannerSettingList(req model.GetBannerSettingListRequest) ([]model.GetBannerSettingListResponse, error) {

	var list []model.GetBannerSettingListResponse

	selectedFields := "tb.id AS id, tb.lang AS lang, tb.image_url AS image_url, tb.link_url AS link_url"
	selectedFields += ", tb.is_show_logedin AS is_show_logedin, tb.is_show_public AS is_show_public"
	query := r.db.Table("banner_setting as tb")
	query = query.Select(selectedFields)
	if req.Lang != "" {
		query = query.Where("tb.lang = ?", req.Lang)
	}
	if err := query.
		Where("tb.deleted_at IS NULL").
		Order("tb.id DESC").
		Scan(&list).
		Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (r repo) DeleteBannerSetting(body model.DeleteBannerSettingBody) error {

	if err := r.db.Table("banner_setting").Where("id = ?", body.Id).Updates(body).Error; err != nil {
		return err
	}

	return nil
}

func (r *repo) GetConfigurationCheckShowTransactionResponse() (*model.ConfigurationCheckShowTransactionResponse, error) {

	var record model.ConfigurationCheckShowTransactionResponse

	selectedFields := "con.is_show_deposit AS is_show_deposit, con.is_show_withdraw AS is_show_withdraw"
	sql := r.db.Table("configuration_web AS con")
	sql = sql.Select(selectedFields)
	if err := sql.Take(&record).Error; err != nil {
		record.IsShowDeposit = true
		record.IsShowWithdraw = true
	}

	return &record, nil
}

func (r repo) GetConfigurationRegisterFormat() ([]model.SelectOptions, error) {

	options := []model.SelectOptions{}

	selectedFields := "id as id, label_th as label"
	var sql = r.db.Table("configuration_register_format").Select(selectedFields)
	if err := sql.
		Scan(&options).
		Error; err != nil {
		return nil, err
	}
	return options, nil
}

func (r repo) UpdateActivityMenu(body model.UpdateActivityMenuRequest) error {

	if err := r.db.Table("activity_menu").Where("id = ?", body.Id).Updates(body).Error; err != nil {
		return err
	}

	return nil
}

func (r repo) SortActivityMenu(req model.DragSortRequest) error {

	tableName := "activity_menu"

	// ===== NO NEED TO EDIT AFTER THIS LINE ===== //

	var list []model.SortOrder
	selectedFields := "id, sort_order"
	query := r.db.Table(tableName)
	query = query.Select(selectedFields)
	if err := query.
		Where("id IN ?", []int64{req.FromItemId, req.ToItemId}).
		Limit(2).
		Find(&list).
		Error; err != nil {
		return err
	}

	var fromItem *model.SortOrder
	var toItem *model.SortOrder
	for _, record := range list {
		if record.Id == req.FromItemId {
			fromItem = &model.SortOrder{
				Id:        record.Id,
				SortOrder: record.SortOrder,
			}
		} else if record.Id == req.ToItemId {
			toItem = &model.SortOrder{
				Id:        record.Id,
				SortOrder: record.SortOrder,
			}
		}
	}

	if fromItem != nil && toItem != nil {
		// Sort Direction //
		if fromItem.SortOrder < toItem.SortOrder {
			// Drag down  //
			whereShiftDown := r.db.Where("sort_order > ?", fromItem.SortOrder).Where("sort_order <= ?", toItem.SortOrder)
			if err := r.db.Table(tableName).Where(whereShiftDown).Update("sort_order", gorm.Expr("sort_order - 1")).Error; err != nil {
				return err
			}
			// Go to
			if err := r.db.Table(tableName).Where("id = ?", fromItem.Id).Update("sort_order", toItem.SortOrder).Error; err != nil {
				return err
			}
		} else if fromItem.SortOrder > toItem.SortOrder {
			// Drag up = shift up //
			whereShiftDown := r.db.Where("sort_order < ?", fromItem.SortOrder).Where("sort_order >= ?", toItem.SortOrder)
			if err := r.db.Table(tableName).Where(whereShiftDown).Update("sort_order", gorm.Expr("sort_order + 1")).Error; err != nil {
				return err
			}
			// Go to
			if err := r.db.Table(tableName).Where("id = ?", fromItem.Id).Update("sort_order", toItem.SortOrder).Error; err != nil {
				return err
			}
		}
	}
	return nil
}

func (r repo) GetActivityMenu(req model.GetActivityMenuRequest) ([]model.GetActivityMenuResponse, error) {

	var list []model.GetActivityMenuResponse

	selectedFields := "id, name, description, image_url AS image_url, label_th AS label_th, label_en AS label_en, sort_order AS sort_order"
	selectedFields += ",  label_en AS activity_type, lang AS lang"
	query := r.db.Table("activity_menu")
	query = query.Select(selectedFields)
	if req.Lang != "" {
		query = query.Where("lang = ?", req.Lang)
	}

	if err := query.
		Order("sort_order ASC").
		Scan(&list).
		Error; err != nil {
		return nil, err
	}

	return list, nil
}

func (r repo) GetActivityMenuItem(req model.GetActivityMenuItemRequest) (*model.GetActivityMenuResponse, error) {

	var record model.GetActivityMenuResponse

	selectedFields := "id, name, description, image_url AS image_url, label_th AS label_th, label_en AS label_en, sort_order AS sort_order"
	selectedFields += ",  label_en AS activity_type, lang AS lang"
	query := r.db.Table("activity_menu")
	query = query.Select(selectedFields)
	if req.Lang != "" {
		query = query.Where("lang = ?", req.Lang)
	}
	if req.ActivityType != "" {
		query = query.Where("label_en = ?", req.ActivityType)
	}
	if err := query.
		Take(&record).
		Error; err != nil {
		return nil, err
	}

	return &record, nil
}

func (r *repo) GetRunningMessage() (*model.RunningMessageResponse, error) {

	client := resty.New()
	endpoint := os.Getenv("MASTER_WEB_ENDPOINT") + "/api/show/send-message/running-message"
	fmt.Printf("Calling GetRunningMessage endpoint: %s\n", endpoint)

	resp, err := client.R().
		SetHeader("Content-Type", "application/json").
		Get(endpoint)
	if err != nil {
		return nil, err
	}

	if resp.IsError() {
		return nil, fmt.Errorf("HTTP error: %s - %s", resp.Status(), resp.String())
	}

	var response model.RunningMessageResponse
	err = json.Unmarshal(resp.Body(), &response)
	if err != nil {
		fmt.Println("Failed to unmarshal response:", string(resp.Body()))
		return nil, err
	}

	return &response, nil
}
