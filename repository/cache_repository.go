package repository

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"os"
	"time"

	"github.com/redis/go-redis/v9"
)

type CacheRepository interface {
	Get(key string, out interface{}) bool
	Set(key string, v interface{}, ttl time.Duration)
	Delete(key string)
}

type Cache struct {
	cache *redis.Client
}

func NewCacheRepository(cache *redis.Client) *Cache {
	return &Cache{
		cache: cache,
	}
}

func (c *Cache) Get(key string, out interface{}) bool {

	ctx := context.Background()
	key = os.Getenv("REDIS_PREFIX_NAME") + key

	str, err := c.cache.Get(ctx, key).Result()
	if err != nil {
		if err != redis.Nil && !errors.Is(err, context.Canceled) {
			log.Println(fmt.Sprintf("cache[%s]: get error: %s", key, err), nil, 0)
		}
		return false
	}

	if err := json.Unmarshal([]byte(str), out); err != nil {
		log.Println(fmt.Sprintf("json unmarshal err:  %s", err), nil, 0)
		return false
	}

	return true
}

func (c *Cache) Set(key string, v interface{}, ttl time.Duration) {

	bs, err := json.Marshal(v)
	if err != nil {
		log.Println(fmt.Sprintf("json unmarshal err:  %s", err), nil, 0)
	}

	ctx := context.Background()
	key = os.Getenv("REDIS_PREFIX_NAME") + key

	if _, err := c.cache.Set(ctx, key, string(bs), ttl).Result(); err != nil {
		log.Println(fmt.Sprintf("cache[%s]: set error: %s", key, err), nil, 0)
	}
}

func (c *Cache) Delete(key string) {

	ctx := context.Background()
	key = os.Getenv("REDIS_PREFIX_NAME") + key

	if _, err := c.cache.Del(ctx, key).Result(); err != nil {
		log.Println(fmt.Sprintf("cache[%s]: delete error: %s", key, err), nil, 0)
	}
}
