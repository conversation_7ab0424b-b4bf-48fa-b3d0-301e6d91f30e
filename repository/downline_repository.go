package repository

import (
	"bytes"
	"cybergame-api/helper"
	"cybergame-api/model"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net/http"
	"os"

	"gorm.io/gorm"
)

func NewDownlineRepository(db *gorm.DB) DownlineRepository {
	return &repo{db}
}

type DownlineRepository interface {
	// Downline Flow
	// 1. (ON load) Get Remote Web Info * every 1 min
	GetWebInfo() (*model.DownlineWebInfo, error)
	GetPaymentInfo() (*model.DownlinePaymentInfo, error)
	// 2. (ON load) Update Local Web Info with Remote Web Info * every 1 min
	UpdateDownlineBalance(req model.DownlineWebBalanceRequest) (*model.DownlineWebInfo, error)
	// 3. (ON action) Submit PAID Local Invoice
	SubmitPaidInvoice(body model.DownlineInvoiceSubmitRequest) (*int64, error)
	// Upline Flow
	// 1. (ON action) Remote updated info - no need to action
	// 2. (ON load) Remote updated invoice status confnirm or reject
	GetConfirmedRemoteInvoiceList(body model.DownlineConfirmedInvoiceListRequest) (model.DownlineConfirmedInvoiceListResponse, error)
	// REF-CONFIG
	GetLocalWebInfo() (*model.WebStatusResponse, error)
	UpdateWebMasterInfo(id int64, updateBody map[string]interface{}) error
	// REF-INVOICE
	GetDownlineInvoiceRequestById(id int64) (*model.DownlineInvoiceSubmitRequest, error)
	GetDownlineRawInvoiceList(req model.DownlineInvoiceListRequest) ([]model.Invoice, int64, error)
	GetInvoiceById(id int64) (*model.InvoiceResponse, error)
	ConfirmInvoice(body model.InvoiceConfirmBody) error
	RejectInvoice(body model.InvoiceConfirmBody) error
	UpdateWebRenewal(updateBody model.WebMasterUpdateWebRenewalBody) error
	UpdateSmsRenewal(updateBody model.WebMasterUpdateSmsRenewalBody) error
	UpdateFastbankRenewal(updateBody model.WebMasterUpdateFastbankRenewalBody) error
	// SCAMMER
	GetScammerById(id int64) (*model.ScammertList, error)
	SubmitScammer(body model.ScammerSubmitRequest) (*int64, error)
	GetScammerNumberList() (*model.ScammerNumberListResponse, error)
	// INVOICE-FROM-REMOTE
	CreateWebRenewalInvoice(body model.InvoiceCreateBody) (*int64, error)
	CreateSmsRenewalInvoice(body model.InvoiceCreateBody) (*int64, error)
	CreateFastbankRenewalInvoice(body model.InvoiceCreateBody) (*int64, error)
	// SCAMMER-FROM-MASTER
	GetMasterScammerList(query model.DownlineScammerequest) (*model.DownlineScammerPaginationResponse, error)
	//Line-Contact
	GetMasterLineContact() (*model.DownlineLineContactResponse, error)
	// REF
	GetUserById(int64) (*model.UserResponse, error)
	// HENG
	CreateInvoiceHengPayment(body model.DownlineInvoiceHengPaymentCreateRequest) (*model.DownlineInvoiceHengPaymentCreateResponse, error)
}

func (r repo) GetDownlineInvoiceRequestById(id int64) (*model.DownlineInvoiceSubmitRequest, error) {

	var record model.DownlineInvoiceSubmitRequest

	selectedFields := "tb_invoice.id as id, tb_invoice.web_id as web_id, tb_invoice.web_name as web_name, tb_invoice.invoice_no as invoice_no"
	selectedFields += ", tb_invoice.invoice_type_id as invoice_type_id"
	selectedFields += ", tb_invoice.invoice_at as invoice_at, tb_invoice.expire_at as expire_at, tb_invoice.paid_at as paid_at, tb_invoice.payment_detail as payment_detail"
	selectedFields += ", tb_invoice.confirm_by as confirm_by, tb_invoice.confirm_at as confirm_at"
	selectedFields += ", tb_invoice.package_id as package_id, tb_invoice.package_detail as package_detail, tb_invoice.renew_days as renew_days, tb_invoice.renew_credit_amount as renew_credit_amount"
	selectedFields += ", tb_invoice.status_id as status_id, tb_invoice.slip_image_path as slip_image_path, tb_invoice.raw_qr_code as raw_qr_code"
	selectedFields += ", tb_invoice.sum_price as sum_price, tb_invoice.vat_percent as vat_percent, tb_invoice.vat_price as vat_price"
	selectedFields += ", tb_invoice.discount_price as discount_price, tb_invoice.total_price as total_price"
	selectedFields += ", tb_invoice.created_at as created_at, tb_invoice.updated_at as updated_at"
	selectedFields += ", tb_webs.name as web_name"
	selectedFields += ", tb_paid_by.fullname as paid_by_name"
	selectedFields += ", tb_create_by.fullname as create_by_name"
	query := r.db.Table("invoice as tb_invoice")
	query = query.Select(selectedFields)
	query = query.Joins("LEFT JOIN renewal_web_master as tb_webs ON tb_webs.id = tb_invoice.web_id")
	query = query.Joins("LEFT JOIN admin as tb_paid_by ON tb_paid_by.id = tb_invoice.paid_by")
	query = query.Joins("LEFT JOIN admin as tb_create_by ON tb_create_by.id = tb_invoice.create_by")
	query = query.Where("tb_invoice.id = ?", id)
	if err := query.Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetDownlineRawInvoiceList(req model.DownlineInvoiceListRequest) ([]model.Invoice, int64, error) {

	var list []model.Invoice
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("invoice as tb_invoice")
	count = count.Select("tb_invoice.id")
	count = count.Where("tb_invoice.status_id = ?", req.StatusId)
	if req.TypeId != nil {
		count = count.Where("tb_invoice.invoice_type_id = ?", req.TypeId)
	}
	if err = count.Count(&total).Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "*"
		query := r.db.Table("invoice as tb_invoice")
		query = query.Select(selectedFields)
		query = query.Where("tb_invoice.status_id = ?", req.StatusId)
		if req.TypeId != nil {
			query = query.Where("tb_invoice.invoice_type_id = ?", req.TypeId)
		}
		if err = query.Scan(&list).Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetWebInfo() (*model.DownlineWebInfo, error) {

	var record model.DownlineWebInfo

	// log.Println("GetWebInfo req ------> ")

	if os.Getenv("MASTER_WEB_ENDPOINT") == "" || os.Getenv("MASTER_WEB_KEY") == "" {
		return nil, errors.New("INVALID_MASTER_CONFIG")
	}

	// (GetWebInfo)
	url := fmt.Sprintf("%s/api/downlines/web-info", os.Getenv("MASTER_WEB_ENDPOINT"))
	apiKey := os.Getenv("MASTER_WEB_KEY")
	url = fmt.Sprintf("%s?apiKey=%s", url, apiKey)

	call := http.Client{}
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	resp, err := call.Do(req)
	if err != nil {
		log.Println("GetWebInfo error ------> ", helper.StructJson(err))
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated && resp.StatusCode != http.StatusOK {
		log.Println("resp.StatusCode", resp.StatusCode)
		var errRes model.SuccessWithData
		if err := json.NewDecoder(resp.Body).Decode(&errRes); err != nil {
			log.Println("ERROR in ERROR.NewDecoder", err)
			if resp.StatusCode == http.StatusNotFound {
				return nil, errors.New("EXTERNAL_DATA_NOT_FOUND")
			} else if resp.StatusCode == http.StatusBadRequest {
				return nil, errors.New("INVALID_EXTERNAL_REQUEST")
			}
			return nil, errors.New("UNKNOW_EXTERNAL_API_ERROR")
		}
		log.Println("GetWebInfo resp.ERROR ------> ", helper.StructJson(errRes))
		return nil, errors.New(errRes.Message)
	}

	if err := json.NewDecoder(resp.Body).Decode(&record); err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetPaymentInfo() (*model.DownlinePaymentInfo, error) {

	var record model.DownlinePaymentInfo

	// log.Println("GetPaymentInfo req ------> ")

	if os.Getenv("MASTER_WEB_ENDPOINT") == "" || os.Getenv("MASTER_WEB_KEY") == "" {
		return nil, errors.New("INVALID_MASTER_CONFIG")
	}

	// (GetPaymentInfo)
	url := fmt.Sprintf("%s/api/downlines/payment-info", os.Getenv("MASTER_WEB_ENDPOINT"))
	apiKey := os.Getenv("MASTER_WEB_KEY")
	url = fmt.Sprintf("%s?apiKey=%s", url, apiKey)

	call := http.Client{}
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	resp, err := call.Do(req)
	if err != nil {
		log.Println("GetPaymentInfo error ------> ", helper.StructJson(err))
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		log.Println("resp.StatusCode", resp.StatusCode)
		var errRes model.SuccessWithData
		if err := json.NewDecoder(resp.Body).Decode(&errRes); err != nil {
			log.Println("ERROR in ERROR.NewDecoder", err)
			if resp.StatusCode == http.StatusNotFound {
				return nil, errors.New("EXTERNAL_DATA_NOT_FOUND")
			} else if resp.StatusCode == http.StatusBadRequest {
				return nil, errors.New("INVALID_EXTERNAL_REQUEST")
			}
			return nil, errors.New("UNKNOW_EXTERNAL_API_ERROR")
		}
		log.Println("GetPaymentInfo resp.ERROR ------> ", helper.StructJson(errRes))
		return nil, errors.New(errRes.Message)
	}

	if err := json.NewDecoder(resp.Body).Decode(&record); err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) UpdateDownlineBalance(body model.DownlineWebBalanceRequest) (*model.DownlineWebInfo, error) {

	var record model.DownlineWebInfo

	// log.Println("UpdateDownlineBalance req ------> ")

	if os.Getenv("MASTER_WEB_ENDPOINT") == "" || os.Getenv("MASTER_WEB_KEY") == "" {
		return nil, errors.New("INVALID_MASTER_CONFIG")
	}

	// (UpdateDownlineBalance)
	url := fmt.Sprintf("%s/api/downlines/web-balance", os.Getenv("MASTER_WEB_ENDPOINT"))
	apiKey := os.Getenv("MASTER_WEB_KEY")
	url = fmt.Sprintf("%s?apiKey=%s", url, apiKey)

	jsonBody, _ := json.Marshal(body)
	reqBody := bytes.NewBuffer(jsonBody)

	call := http.Client{}
	req, err := http.NewRequest("POST", url, reqBody)
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	resp, err := call.Do(req)
	if err != nil {
		log.Println("UpdateDownlineBalance error ------> ", helper.StructJson(err))
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		log.Println("resp.StatusCode", resp.StatusCode)
		var errRes model.SuccessWithData
		if err := json.NewDecoder(resp.Body).Decode(&errRes); err != nil {
			log.Println("ERROR in ERROR.NewDecoder", err)
			if resp.StatusCode == http.StatusNotFound {
				return nil, errors.New("EXTERNAL_DATA_NOT_FOUND")
			} else if resp.StatusCode == http.StatusBadRequest {
				return nil, errors.New("INVALID_EXTERNAL_REQUEST")
			}
			return nil, errors.New("UNKNOW_EXTERNAL_API_ERROR")
		}
		log.Println("UpdateDownlineBalance resp.ERROR ------> ", helper.StructJson(errRes))
		return nil, errors.New(errRes.Message)
	}

	if err := json.NewDecoder(resp.Body).Decode(&record); err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) SubmitPaidInvoice(body model.DownlineInvoiceSubmitRequest) (*int64, error) {

	var record model.SuccessWithId

	if os.Getenv("MASTER_WEB_ENDPOINT") == "" || os.Getenv("MASTER_WEB_KEY") == "" {
		return nil, errors.New("INVALID_MASTER_CONFIG")
	}

	// (submitInvoice)
	url := fmt.Sprintf("%s/api/downlines/submit-invoice", os.Getenv("MASTER_WEB_ENDPOINT"))
	apiKey := os.Getenv("MASTER_WEB_KEY")
	url = fmt.Sprintf("%s?apiKey=%s", url, apiKey)

	jsonBody, _ := json.Marshal(body)
	reqBody := bytes.NewBuffer(jsonBody)

	call := http.Client{}
	req, err := http.NewRequest("POST", url, reqBody)
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	resp, err := call.Do(req)
	if err != nil {
		log.Println("SubmitPaidInvoice error ------> ", helper.StructJson(err))
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		log.Println("resp.StatusCode", resp.StatusCode)
		var errRes model.SuccessWithData
		if err := json.NewDecoder(resp.Body).Decode(&errRes); err != nil {
			log.Println("ERROR in ERROR.NewDecoder", err)
			if resp.StatusCode == http.StatusNotFound {
				return nil, errors.New("EXTERNAL_DATA_NOT_FOUND")
			} else if resp.StatusCode == http.StatusBadRequest {
				return nil, errors.New("INVALID_EXTERNAL_REQUEST")
			}
			return nil, errors.New("UNKNOW_EXTERNAL_API_ERROR")
		}
		log.Println("SubmitPaidInvoice resp.ERROR ------> ", helper.StructJson(errRes))
		return nil, errors.New(errRes.Message)
	}

	if err := json.NewDecoder(resp.Body).Decode(&record); err != nil {
		return nil, err
	}
	return &record.Id, nil
}

func (r repo) GetConfirmedRemoteInvoiceList(body model.DownlineConfirmedInvoiceListRequest) (model.DownlineConfirmedInvoiceListResponse, error) {

	var record model.DownlineConfirmedInvoiceListResponse

	if os.Getenv("MASTER_WEB_ENDPOINT") == "" || os.Getenv("MASTER_WEB_KEY") == "" {
		return record, errors.New("INVALID_MASTER_CONFIG")
	}

	// (GetConfirmedInvoiceList)
	url := fmt.Sprintf("%s/api/downlines/confirmed-invoice-list", os.Getenv("MASTER_WEB_ENDPOINT"))
	apiKey := os.Getenv("MASTER_WEB_KEY")
	url = fmt.Sprintf("%s?apiKey=%s", url, apiKey)

	body.ApiKey = apiKey
	jsonBody, _ := json.Marshal(body)
	reqBody := bytes.NewBuffer(jsonBody)

	call := http.Client{}
	req, err := http.NewRequest("POST", url, reqBody)
	if err != nil {
		return record, err
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	resp, err := call.Do(req)
	if err != nil {
		log.Println("GetConfirmedRemoteInvoiceList error ------> ", helper.StructJson(err))
		return record, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		log.Println("resp.StatusCode", resp.StatusCode)
		var errRes model.SuccessWithData
		if err := json.NewDecoder(resp.Body).Decode(&errRes); err != nil {
			log.Println("ERROR in ERROR.NewDecoder", err)
			if resp.StatusCode == http.StatusNotFound {
				return record, errors.New("EXTERNAL_DATA_NOT_FOUND")
			} else if resp.StatusCode == http.StatusBadRequest {
				return record, errors.New("INVALID_EXTERNAL_REQUEST")
			}
			return record, errors.New("UNKNOW_EXTERNAL_API_ERROR")
		}
		log.Println("GetConfirmedRemoteInvoiceList resp.ERROR ------> ", helper.StructJson(errRes))
		return record, errors.New(errRes.Message)
	}

	if err := json.NewDecoder(resp.Body).Decode(&record); err != nil {
		return record, err
	}
	return record, nil
}

func (r repo) SubmitScammer(body model.ScammerSubmitRequest) (*int64, error) {

	var record model.SuccessWithId

	log.Println("SubmitScammer body ------> ", helper.StructJson(body))

	if os.Getenv("MASTER_WEB_ENDPOINT") == "" || os.Getenv("MASTER_WEB_KEY") == "" {
		return nil, errors.New("INVALID_MASTER_CONFIG")
	}

	// (SubmitScammer)
	url := fmt.Sprintf("%s/api/downlines/submit-scammer", os.Getenv("MASTER_WEB_ENDPOINT"))
	apiKey := os.Getenv("MASTER_WEB_KEY")
	url = fmt.Sprintf("%s?apiKey=%s", url, apiKey)

	jsonBody, _ := json.Marshal(body)
	reqBody := bytes.NewBuffer(jsonBody)

	call := http.Client{}
	req, err := http.NewRequest("POST", url, reqBody)
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	resp, err := call.Do(req)
	if err != nil {
		log.Println("SubmitScammer error ------> ", helper.StructJson(err))
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		log.Println("resp.StatusCode", resp.StatusCode)
		var errRes model.SuccessWithData
		if err := json.NewDecoder(resp.Body).Decode(&errRes); err != nil {
			log.Println("ERROR in ERROR.NewDecoder", err)
			if resp.StatusCode == http.StatusNotFound {
				return nil, errors.New("EXTERNAL_DATA_NOT_FOUND")
			} else if resp.StatusCode == http.StatusBadRequest {
				return nil, errors.New("INVALID_EXTERNAL_REQUEST")
			}
			return nil, errors.New("UNKNOW_EXTERNAL_API_ERROR")
		}
		log.Println("SubmitScammer resp.ERROR ------> ", helper.StructJson(errRes))
		return nil, errors.New(errRes.Message)
	}

	if err := json.NewDecoder(resp.Body).Decode(&record); err != nil {
		return nil, err
	}
	return &record.Id, nil
}

func (r repo) GetScammerNumberList() (*model.ScammerNumberListResponse, error) {

	var record model.ScammerNumberListResponse

	// log.Println("GetScammerNumberList req ------> ")

	if os.Getenv("MASTER_WEB_ENDPOINT") == "" || os.Getenv("MASTER_WEB_KEY") == "" {
		return nil, errors.New("INVALID_MASTER_CONFIG")
	}

	// (getScammerNumberList)/downlines/scammers/number-list
	// (getScammerNumberList) รายการเลขบัญชีมิจฉาชีพ และ หมายเลขโทรศัพท์มิจฉาชีพ
	url := fmt.Sprintf("%s/api/downlines/scammers/number-list", os.Getenv("MASTER_WEB_ENDPOINT"))
	apiKey := os.Getenv("MASTER_WEB_KEY")
	url = fmt.Sprintf("%s?apiKey=%s", url, apiKey)

	call := http.Client{}
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	resp, err := call.Do(req)
	if err != nil {
		log.Println("GetScammerNumberList error ------> ", helper.StructJson(err))
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated && resp.StatusCode != http.StatusOK {
		log.Println("resp.StatusCode", resp.StatusCode)
		var errRes model.SuccessWithData
		if err := json.NewDecoder(resp.Body).Decode(&errRes); err != nil {
			log.Println("ERROR in ERROR.NewDecoder", err)
			if resp.StatusCode == http.StatusNotFound {
				return nil, errors.New("EXTERNAL_DATA_NOT_FOUND")
			} else if resp.StatusCode == http.StatusBadRequest {
				return nil, errors.New("INVALID_EXTERNAL_REQUEST")
			}
			return nil, errors.New("UNKNOW_EXTERNAL_API_ERROR")
		}
		log.Println("GetScammerNumberList resp.ERROR ------> ", helper.StructJson(errRes))
		return nil, errors.New(errRes.Message)
	}

	if err := json.NewDecoder(resp.Body).Decode(&record); err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetMasterScammerList(query model.DownlineScammerequest) (*model.DownlineScammerPaginationResponse, error) {

	var record model.DownlineScammerPaginationResponse

	// log.Println("GetMasterScammerList req ------> ")

	if os.Getenv("MASTER_WEB_ENDPOINT") == "" || os.Getenv("MASTER_WEB_KEY") == "" {
		return nil, errors.New("INVALID_MASTER_CONFIG")
	}

	// (GetDownlineScammerList)
	url := fmt.Sprintf("%s/api/downlines/scammers/downline-list", os.Getenv("MASTER_WEB_ENDPOINT"))
	apiKey := os.Getenv("MASTER_WEB_KEY")
	url = fmt.Sprintf("%s?apiKey=%s", url, apiKey)

	url = fmt.Sprintf("%s&page=%d&limit=%d", url, query.Page, query.Limit)
	if query.Phone != "" {
		url = fmt.Sprintf("%s&phone=%s", url, query.Phone)
	}
	if query.BankAccount != "" {
		url = fmt.Sprintf("%s&bankAccount=%s", url, query.BankAccount)
	}
	if query.AccountName != "" {
		url = fmt.Sprintf("%s&accountName=%s", url, query.AccountName)
	}

	call := http.Client{}
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	resp, err := call.Do(req)
	if err != nil {
		log.Println("GetMasterScammerList error ------> ", helper.StructJson(err))
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated && resp.StatusCode != http.StatusOK {
		log.Println("resp.StatusCode", resp.StatusCode)
		var errRes model.SuccessWithData
		if err := json.NewDecoder(resp.Body).Decode(&errRes); err != nil {
			log.Println("ERROR in ERROR.NewDecoder", err)
			if resp.StatusCode == http.StatusNotFound {
				return nil, errors.New("EXTERNAL_DATA_NOT_FOUND")
			} else if resp.StatusCode == http.StatusBadRequest {
				return nil, errors.New("INVALID_EXTERNAL_REQUEST")
			}
			return nil, errors.New("UNKNOW_EXTERNAL_API_ERROR")
		}
		return nil, errors.New(errRes.Message)
	}

	if err := json.NewDecoder(resp.Body).Decode(&record); err != nil {
		return nil, err
	}

	return &record, nil
}

func (r repo) GetMasterLineContact() (*model.DownlineLineContactResponse, error) {

	var record model.DownlineLineContactResponse

	// log.Println("GetMasterLineContactById req ------> ")

	if os.Getenv("MASTER_WEB_ENDPOINT") == "" || os.Getenv("MASTER_WEB_KEY") == "" {
		return nil, errors.New("INVALID_MASTER_CONFIG")
	}

	url := fmt.Sprintf("%s/api/downlines/line-contact", os.Getenv("MASTER_WEB_ENDPOINT"))
	apiKey := os.Getenv("MASTER_WEB_KEY")
	url = fmt.Sprintf("%s?apiKey=%s", url, apiKey)

	call := http.Client{}
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	resp, err := call.Do(req)
	if err != nil {
		log.Println("GetMasterLineContactById error ------> ", helper.StructJson(err))
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated && resp.StatusCode != http.StatusOK {
		log.Println("resp.StatusCode", resp.StatusCode)
		var errRes model.SuccessWithData
		if err := json.NewDecoder(resp.Body).Decode(&errRes); err != nil {
			log.Println("ERROR in ERROR.NewDecoder", err)
			if resp.StatusCode == http.StatusNotFound {
				return nil, errors.New("EXTERNAL_DATA_NOT_FOUND")
			} else if resp.StatusCode == http.StatusBadRequest {
				return nil, errors.New("INVALID_EXTERNAL_REQUEST")
			}
			return nil, errors.New("UNKNOW_EXTERNAL_API_ERROR")
		}
		return nil, errors.New(errRes.Message)
	}

	if err := json.NewDecoder(resp.Body).Decode(&record); err != nil {
		return nil, err
	}

	return &record, nil
}

func (r repo) CreateInvoiceHengPayment(body model.DownlineInvoiceHengPaymentCreateRequest) (*model.DownlineInvoiceHengPaymentCreateResponse, error) {

	var record model.DownlineInvoiceHengPaymentCreateResponse

	// log.Println("CreateInvoiceHengPayment req ------> ", helper.StructJson(body))

	if os.Getenv("MASTER_WEB_ENDPOINT") == "" || os.Getenv("MASTER_WEB_KEY") == "" {
		return nil, errors.New("INVALID_MASTER_CONFIG")
	}

	// @Param _ query model.DownlineWebInfoRequest true "query"
	// @Param body body model.DownlineCreateHengPaymentRequest true "body"
	// @Success 200 {object} model.PaygateHengOrderQrResponse => DownlineInvoiceHengPaymentCreateResponse
	// @Failure 400 {object} handler.ErrorResponse
	// @Router /downlines/heng/create [post]
	// (createHengPayment) https://stage-api-master.cbgame88.com/api/downlines/heng/create?apiKey=stringstringstringstringstringst
	url := fmt.Sprintf("%s/api/downlines/heng/create", os.Getenv("MASTER_WEB_ENDPOINT"))
	apiKey := os.Getenv("MASTER_WEB_KEY")
	url = fmt.Sprintf("%s?apiKey=%s", url, apiKey)

	jsonBody, _ := json.Marshal(body)
	reqBody := bytes.NewBuffer(jsonBody)

	call := http.Client{}
	req, err := http.NewRequest("POST", url, reqBody)
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	resp, err := call.Do(req)
	if err != nil {
		log.Println("CreateInvoiceHengPayment error ------> ", helper.StructJson(err))
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		log.Println("resp.StatusCode", resp.StatusCode)
		var errRes model.SuccessWithData
		if err := json.NewDecoder(resp.Body).Decode(&errRes); err != nil {
			log.Println("ERROR in ERROR.NewDecoder", err)
			if resp.StatusCode == http.StatusNotFound {
				return nil, errors.New("EXTERNAL_DATA_NOT_FOUND")
			} else if resp.StatusCode == http.StatusBadRequest {
				return nil, errors.New("INVALID_EXTERNAL_REQUEST")
			}
			return nil, errors.New("UNKNOW_EXTERNAL_API_ERROR")
		}
		log.Println("CreateInvoiceHengPayment resp.ERROR ------> ", helper.StructJson(errRes))
		return nil, errors.New(errRes.Message)
	}

	if err := json.NewDecoder(resp.Body).Decode(&record); err != nil {
		log.Println("CreateInvoiceHengPayment Decode.NewDecoder.Err ------> ", err)
		return nil, err
	}
	return &record, nil
}

func (r repo) CreateInvoicePayonexPayment(body model.DownlineInvoicePayonexPaymentCreateRequest) (*model.DownlineInvoicePayonexPaymentCreateResponse, error) {

	var record model.DownlineInvoicePayonexPaymentCreateResponse

	// log.Println("CreateInvoicePayonexPayment req ------> ", helper.StructJson(body))

	if os.Getenv("MASTER_WEB_ENDPOINT") == "" || os.Getenv("MASTER_WEB_KEY") == "" {
		return nil, errors.New("INVALID_MASTER_CONFIG")
	}

	// @Param _ query model.DownlineWebInfoRequest true "query"
	// @Param body body model.DownlineCreatePayonexPaymentRequest true "body"
	// @Success 200 {object} model.PaygatePayonexOrderQrResponse => DownlineInvoicePayonexPaymentCreateResponse
	// @Failure 400 {object} handler.ErrorResponse
	// @Router /downlines/payonex/create [post]
	// (createPayonexPayment) https://stage-api-master.cbgame88.com/api/downlines/payonex/create?apiKey=stringstringstringstringstringst
	url := fmt.Sprintf("%s/api/downlines/payonex/create", os.Getenv("MASTER_WEB_ENDPOINT"))
	apiKey := os.Getenv("MASTER_WEB_KEY")
	url = fmt.Sprintf("%s?apiKey=%s", url, apiKey)

	jsonBody, _ := json.Marshal(body)
	reqBody := bytes.NewBuffer(jsonBody)

	call := http.Client{}
	req, err := http.NewRequest("POST", url, reqBody)
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	resp, err := call.Do(req)
	if err != nil {
		log.Println("CreateInvoicePayonexPayment error ------> ", helper.StructJson(err))
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		log.Println("resp.StatusCode", resp.StatusCode)
		var errRes model.SuccessWithData
		if err := json.NewDecoder(resp.Body).Decode(&errRes); err != nil {
			log.Println("ERROR in ERROR.NewDecoder", err)
			if resp.StatusCode == http.StatusNotFound {
				return nil, errors.New("EXTERNAL_DATA_NOT_FOUND")
			} else if resp.StatusCode == http.StatusBadRequest {
				return nil, errors.New("INVALID_EXTERNAL_REQUEST")
			}
			return nil, errors.New("UNKNOW_EXTERNAL_API_ERROR")
		}
		log.Println("CreateInvoicePayonexPayment resp.ERROR ------> ", helper.StructJson(errRes))
		return nil, errors.New(errRes.Message)
	}

	if err := json.NewDecoder(resp.Body).Decode(&record); err != nil {
		log.Println("CreateInvoicePayonexPayment Decode.NewDecoder.Err ------> ", err)
		return nil, err
	}
	return &record, nil
}
