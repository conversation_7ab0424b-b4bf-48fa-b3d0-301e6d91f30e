package repository

import (
	"cybergame-api/model"
	"errors"

	"gorm.io/gorm"
)

func NewAllianceWhiteRepository(db *gorm.DB) AllianceWhiteRepository {
	return &repo{db}
}

type AllianceWhiteRepository interface {
	AllianceWhiteRegister(body model.UserRegisterForm, afObj model.Affiliate, credit float64, refTypeId int64) (*int64, error)
	CheckRecommendExist(id int64) (bool, error)
	CheckUserByPhone(phone string) (*model.UserResponse, error)
	CheckUserByAccountNo(accountNo string) (*model.UserResponse, error)
	GetUserIdByRef(id int64) (*model.UserGetDataByRef, error)
	UserRegister(body model.UserRegisterForm, afObj model.Affiliate, credit float64, refTypeId int64) (*int64, error)
	// REF-AF
	GetCommissionSetting() (*model.AfCommissionResponse, error)
	// REF-SINGLE
	SetUserSingleSession(userId int64, token string) error
}

func (r repo) AllianceWhiteRegister(body model.UserRegisterForm, afObj model.Affiliate, credit float64, refTypeId int64) (*int64, error) {

	tx := r.db.Begin()

	if err := tx.Table("user").
		Create(&body).
		Error; err != nil {
		tx.Rollback()
		return nil, err
	}
	if body.Id == 0 {
		tx.Rollback()
		return nil, errors.New("CANT_CREATE_USER")
	}

	obj := map[string]interface{}{}
	obj["user_id"] = body.Id

	if err := tx.Table("user_affiliate").
		Create(&obj).
		Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	if refTypeId == model.USER_TYPE_AFFILIATE && afObj.RefId > 0 {
		// Apply AFF Commission
		if err := r.userRegisterApplyAfCommission(tx, body.Id, afObj.RefId, credit); err != nil {
			tx.Rollback()
			return nil, err
		}
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	return &body.Id, nil
}
