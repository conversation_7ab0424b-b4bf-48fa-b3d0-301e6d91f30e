package repository

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"errors"
	"fmt"
	"log"
	"strings"
	"time"

	"gorm.io/gorm"
)

func NewAllianceRepository(db *gorm.DB) AllianceRepository {
	return &repo{db}
}

type AllianceRepository interface {
	GetUserAllianceInfoByRefCode(refCode string) (*model.UserAlliance, error)
	GetUserAllianceInfoBySaleCode(saleCode string) (*model.UserAlliance, error)
	GetUserAllianceInfoList(req model.UserAllianceListRequest) ([]model.UserAlliance, int64, error)

	AlSummary(query model.AllianceSummaryQuery) (*model.AlSummary, error)
	AlGetCommissionSettingUserList(userIds []int64) ([]model.AllianceCommissionSettingUser, error)
	AlGetCommissionSettingUser(userId int64) (*model.AllianceCommissionSettingUser, error)
	AlGetRefIdByUserId(userId int64) (int64, error)
	AlGetTransactionDaily(userId int64, query model.AllianceTransactionDailyQuery) ([]model.AllianceTransaction, int64, error)
	AlGetTransactionSummary(userId int64, query model.AllianceTransactionDailyQuery) (*model.AllianceTransaction, error)
	AlGetTransactionMember(userId int64, query model.AllianceTransactionMemberQuery) ([]model.AllianceTransaction, int64, error)
	GetMemberTotalIncome(req model.AllianceMemberTotalIncomeListRequest) ([]model.AllianceMemberTotalIncomeResponse, int64, error)
	GetMemberTotalIncomeSummary(req model.AllianceMemberTotalIncomeListRequest) (*model.AllianceMemberTotalIncomeResponse, error)
	AlGetFirstDeposit(userId int64, query model.AllianceFirstDepositQuery) ([]model.AllianceFirstDeposit, int64, error)
	GetAlListByMembers(members []string) ([]model.PlayerUserList, error)
	AlCreate(data model.Alliance) error
	AlCreateIncome(list []model.AlCreateIncome) error
	AlUpdateCommissionSetting(data model.AllianceCommissionBody) error
	AlUpdateCommissionSettingUser(userId int64, data model.AllianceCommissionSettingUpdateRequest) error
	AlDownSettingUserToAffiliate(userId int64) error
	AlUpdateCommissionFirstDeposit(userId, refId int64, amount float64) error
	AlIncreaseLinkClick(userId int64) error
	CreateLinkClickLog(refBy int64) error
	GetFirstDepositAllianceList(HeadAllianceId int64, req model.GetFirstDepositAliianceListRequest) ([]model.GetFirstDepositAliianceListResponse, *int64, error)
	GetFirstDepositAllianceSummary(HeadAllianceId int64, req model.GetFirstDepositAliianceSummaryRequest) (*model.GetFirstDepositAllianceSummary, error)
	// Alliance-Report
	GetAllianceUserList(req model.AllianceUserListRequest) ([]model.AllianceUserResponse, int64, error)
	GetAllianceFirstDepositSummary(req model.AllianceFirstDepositListRequest) (*model.AllianceFirstDepositSummaryResponse, error)
	GetAllianceFirstDepositList(req model.AllianceFirstDepositListRequest) ([]model.AllianceUserFirstDepositResponse, int64, error)
	GetAllianceWinLoseHistory(req model.AllianceWinLoseHistoryListRequest) ([]model.AllianceWinLoseHistoryResponse, int64, error)
	GetAllianceWinLoseSummary(req model.AllianceWinLoseHistoryListRequest) (*model.AllianceTotalWinLoseHistoryResponse, error)
	CreateWithdrawAllianceIncomeLog(body model.AllianceIncomeWithdrawCreateBody) (*int64, error)
	GetAllianceIncomeWithdrawHistory(query model.AllianceIncomeWithdrawHistoryListRequest) ([]model.AllianceIncomeWithdrawHistoryResponse, int64, error)
	GetAllianceBankTransaction(req model.AllianceBankTransactionListRequest) ([]model.AllianceBankTransactionResponse, int64, error)
	GetAllianceBankTransactionSummary(req model.AllianceBankTransactionListRequest) (*model.AllianceBankTransactionResponse, error)
	// REF-Admin
	GetAdminById(id int64) (*model.Admin, error)
	GetUserById(id int64) (*model.UserResponse, error)
	// REF-USER_INCOME
	CreateUserIncomeLog(body model.UserIncomeLogCreateBody) (*int64, error)
	GetUserIncomeLogById(id int64) (*model.UserIncomeLogResponse, error)
	GetUserIncomeLogList(req model.UserIncomeLogListRequest) ([]model.UserIncomeLogResponse, int64, error)
	GetUserWinLoseSummaryList(req model.UserWinLoseSummaryListRequest) ([]model.UserWinLoseSummaryResponse, int64, error)
	ConfirmUserIncomeLog(body model.UserIncomeLogConfirmBody) error
	// REF-USER_CREDIT
	IncreaseUserCredit(body model.UserTransactionCreateRequest) (*model.UserTransactionCreateResponse, error)
	// Alliance-Income
	GetAlliancePlayLogList(req model.AlliancePlayLogListRequest) ([]model.AlliancePlayLog, int64, error)
	GetAllianceSumUserPlayLogList(req model.AlliancePlayLogListRequest) ([]model.AllianceUserSumPlayLog, int64, error)
	GetAllianceWinloseIncomeList(req model.AllianceWinLoseHistoryListRequest) ([]model.AllianceWinloseIncome, int64, error)
	UpdateAllianceIncomeTakenList(ids []int64) error
	GetAllianceTotalBonusLogList(req model.AllianceBonusLogListRequest) ([]model.UserTransactionBonusResponse, int64, error)
	CreateAllianceWinloseIncome(body model.AllianceWinloseIncomeCreateBody) (*int64, error)
	GetUserAllianceSettingByUserId(userId int64) (*model.AllianceCommissionSettingUser, error)
	GetAllianceWinloseIncomeById(id int64) (*model.AllianceWinloseIncome, error)
	GetAllianceWinloseIncomeByDailyKey(dailyKey string) (*model.AllianceWinloseIncome, error)
	GetAllianceWinloseIncomeListByDailyKeyList(bulkBody map[string]model.AllianceWinloseIncomeCreateBody) ([]model.AllianceWinloseIncomeDailyKey, int64, error)
	CreateAllianceWinloseIncomeBulk(bulkMap map[string]model.AllianceWinloseIncomeCreateBody) error
	UpdatePendingAllianceWinloseIncome(id int64, body model.AllianceWinlosePendingIncomeUpdateBody) error
	// UpdateTakeAllianceWinloseIncome(id int64, body model.AllianceWinloseIncomeUpdateTakenBody) error
	TestUpdateAllianceWinloseIncome(id int64, amount float64) error
	// New-Alliance Total
	GetAllianceWinLoseList(query model.AllianceWinLoseTotalListRequest) ([]model.AllianceWinLoseTotalResponse, int64, error)
	GetAliasByRef(userId int64) (*model.GetAliasByUserIdResponse, error)
	// REF-MIGRATE
	CreateAffiliateMember(refBy int64, userId int64) error
	// ChangeRefBy-MoveRefBy
	GetUserForGenmemberByUserId(userId int64) (*model.GetUserForGenmemberByUserId, error)
	GetUserForGenmemberByMemberCode(memberCode string) (*model.GetUserForGenmemberByUserId, error)
	// RACE_CONDITION_BLOCKER
	GetRaceActionIdByActionKey(actionKey string) (*int64, error)
	CreateRaceCondition(body model.RaceActionCreateBody) (int64, error)
	// REF
	GetConfiguration() (*model.ConfigurationResponse, error)
	// AFF-MIGRATE
	GetPendingAffiliateIncomeList() ([]model.UserAffIncomeReponse, error)
	CountPendingAffUserIncomeLogList() (int64, error)
	// [ADMIN_LOG]
	CreateAdminLog(body model.AdminLogCreateBody) (*int64, error)
	GetSumAllianceWinLoseTotal(req model.AllianceWinLoseSumTotalRequest) (*model.GetSumAllianceWinLoseTotalResponse, error)
}

func GetDateFromDateType(req model.DateTypeResponse) (*model.DateTypeResponse, error) {

	var result model.DateTypeResponse

	// Query Date today, yesterday, last_week, last_month
	now := time.Now().UTC()
	if req.DateType == "today" {
		req.DateFrom = now.Format("2006-01-02")
		req.DateTo = now.Format("2006-01-02")
	} else if req.DateType == "yesterday" {
		req.DateFrom = now.AddDate(0, 0, -1).Format("2006-01-02")
		req.DateTo = now.AddDate(0, 0, -1).Format("2006-01-02")
	} else if req.DateType == "last_week" {
		// -6 not -7 because today is included
		req.DateFrom = now.AddDate(0, 0, -6).Format("2006-01-02")
		req.DateTo = now.Format("2006-01-02")
	} else if req.DateType == "last_month" {
		// 30 day not exactly 1 month
		// -29 not -30 because today is included
		req.DateFrom = now.AddDate(0, 0, -29).Format("2006-01-02")
		req.DateTo = now.Format("2006-01-02")
	} else if req.DateType == "this_month" {
		// exactly this month
		req.DateFrom = now.Format("2006-01") + "-01"
		req.DateTo = now.AddDate(0, 1, -1).Format("2006-01-02")
	}

	// use Bangkok timezone as new date
	// startDateAtBkk, err := r.ParseBodBkk(req.DateFrom)
	// if err != nil {
	// 	return nil, err
	// }
	// endDateAtBkk, err := r.ParseEodBkk(req.DateTo)
	// if err != nil {
	// 	return nil, err
	// }

	result.DateType = req.DateType
	result.DateFrom = req.DateFrom
	result.DateTo = req.DateTo

	return &result, nil
}

func (r repo) GetDateFromDateType(req model.DateTypeResponse) (*model.DateTypeResponse, error) {

	var result model.DateTypeResponse

	// Query Date today, yesterday, last_week, last_month
	now := time.Now()
	//  || req.DateType == ""  ไม่ได้เพราะค้นหาอย่างอื่น อย่างเดียวก็ได้ ไม่งั้นมันจะหาแค่วันนี้
	if req.DateType == "today" || req.DateType == "day" || req.DateType == "daily" {
		req.DateFrom = now.Format("2006-01-02")
		req.DateTo = now.Format("2006-01-02")
	} else if req.DateType == "yesterday" {
		req.DateFrom = now.AddDate(0, 0, -1).Format("2006-01-02")
		req.DateTo = now.AddDate(0, 0, -1).Format("2006-01-02")
	} else if req.DateType == "last_week" {
		// -6 not -7 because today is included
		req.DateFrom = now.AddDate(0, 0, -6).Format("2006-01-02")
		req.DateTo = now.Format("2006-01-02")
	} else if req.DateType == "last_month" {
		// 30 day not exactly 1 month
		// -29 not -30 because today is included
		req.DateFrom = now.AddDate(0, 0, -29).Format("2006-01-02")
		req.DateTo = now.Format("2006-01-02")
	} else if req.DateType == "this_month" {
		// exactly this month
		firstDayOfMonth, err := time.Parse("2006-01-02", now.Format("2006-01")+"-01")
		if err != nil {
			return nil, err
		}
		lastDayOfMonth := firstDayOfMonth.AddDate(0, 1, -1).Format("2006-01-02")
		req.DateFrom = firstDayOfMonth.Format("2006-01-02")
		req.DateTo = lastDayOfMonth
	}

	// use Bangkok timezone as new date
	// startDateAtBkk, err := r.ParseBodBkk(req.DateFrom)
	// if err != nil {
	// 	return nil, err
	// }
	// endDateAtBkk, err := r.ParseEodBkk(req.DateTo)
	// if err != nil {
	// 	return nil, err
	// }

	result.DateType = req.DateType
	result.DateFrom = req.DateFrom
	result.DateTo = req.DateTo

	return &result, nil
}

func (r repo) GetUserAllianceInfoByRefCode(refCode string) (*model.UserAlliance, error) {

	var alliance model.UserAlliance

	if err := r.db.Table("user_alliance").
		Select("id, user_id, alias, ref_code").
		Where("ref_code = ?", refCode).
		Take(&alliance).
		Error; err != nil {
		return nil, err
	}
	return &alliance, nil
}

func (r repo) GetUserAllianceInfoBySaleCode(saleCode string) (*model.UserAlliance, error) {

	var alliance model.UserAlliance

	if err := r.db.Table("user_alliance").
		Select("id, user_id, alias, ref_code").
		Where("sale_code = ?", saleCode).
		Take(&alliance).
		Error; err != nil {
		return nil, err
	}
	return &alliance, nil
}

func (r repo) GetUserAllianceInfoList(req model.UserAllianceListRequest) ([]model.UserAlliance, int64, error) {

	var list []model.UserAlliance
	var total int64

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("user_alliance as tb_setting")
	count = count.Select("tb_setting.id")
	if len(req.UserIds) > 0 {
		count = count.Where("tb_setting.user_id IN ?", req.UserIds)
	}
	if err := count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "*"
		query := r.db.Table("user_alliance as tb_setting")
		query = query.Select(selectedFields)
		if len(req.UserIds) > 0 {
			query = query.Where("tb_setting.user_id IN ?", req.UserIds)
		}
		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}

	return list, total, nil
}

func (r repo) AlSummary(query model.AllianceSummaryQuery) (*model.AlSummary, error) {

	var data model.AlSummary

	// [********] ดึงข้อมูลหลัก จาก user แทน
	// [20240222] เพิ่มตัวกรองวันที่ โดยที่ MemberRegisterToday กับ MemberOnlineToday จะดึงเฉพาะวันนี้ ไม่่เกี่ยวกับตัวกรองวันที่
	// ส่วนข้อมูลอื่นๆ จะดึงจากวันที่ที่กรอง

	actionAt := time.Now()
	fromDate := actionAt.Format("2006-01-02")
	if query.FromDate != "" {
		fromDate = query.FromDate
	}
	toDate := actionAt.Format("2006-01-02")
	if query.ToDate != "" {
		toDate = query.ToDate
	}

	todayStartAt, err := r.ParseBodBkk(actionAt.Format("2006-01-02"))
	if err != nil {
		return nil, err
	}
	todayEndAt, err := r.ParseEodBkk(actionAt.Format("2006-01-02"))
	if err != nil {
		return nil, err
	}

	if err := r.db.Table("user as tb_user").
		Select("tb_user.id").
		Where("tb_user.ref_by = ?", query.UserId).
		Where("tb_user.created_at BETWEEN ? AND ?", todayStartAt, todayEndAt).
		Count(&data.MemberRegisterToday).
		Error; err != nil {
		return nil, err
	}
	if err := r.db.Table("user as tb_user").
		Select("tb_user.id").
		Where("tb_user.ref_by = ?", query.UserId).
		Where("tb_user.logedin_at BETWEEN ? AND ?", todayStartAt, todayEndAt).
		Count(&data.MemberOnlineToday).Error; err != nil {
		return nil, err
	}

	sql1 := r.db.Table("affiliate_link_click AS tb_click").Select("id").Where("ref_by = ?", query.UserId)
	if query.FromDate != "" {
		startAt, err := r.ParseBodBkk(fromDate)
		if err != nil {
			return nil, err
		}
		sql1 = sql1.Where("tb_click.created_at >= ? ", startAt)
	}
	if query.ToDate != "" {
		endAt, err := r.ParseEodBkk(toDate)
		if err != nil {
			return nil, err
		}
		sql1 = sql1.Where("tb_click.created_at <=  ?", endAt)
	}
	if err := sql1.Count(&data.LinkClickTotal).Error; err != nil {
		return nil, err
	}

	sql2 := r.db.Table("user as tb_user").Select("tb_user.id").Where("tb_user.ref_by = ?", query.UserId).Where("tb_user.member_code IS NULL")
	if query.FromDate != "" {
		startAt, err := r.ParseBodBkk(fromDate)
		if err != nil {
			return nil, err
		}
		sql2 = sql2.Where("created_at >= ? ", startAt)
	}
	if query.ToDate != "" {
		endAt, err := r.ParseEodBkk(toDate)
		if err != nil {
			return nil, err
		}
		sql2 = sql2.Where("created_at <=  ?", endAt)
	}
	if err := sql2.Count(&data.NoMemberCode).Error; err != nil {
		return nil, err
	}

	sql3 := r.db.Table("user as tb_user").Select("tb_user.id").Where("tb_user.ref_by = ?", query.UserId).Where("tb_user.member_code IS NOT NULL")
	if query.FromDate != "" {
		startAt, err := r.ParseBodBkk(fromDate)
		if err != nil {
			return nil, err
		}
		sql3 = sql3.Where("created_at >= ? ", startAt)
	}
	if query.ToDate != "" {
		endAt, err := r.ParseEodBkk(toDate)
		if err != nil {
			return nil, err
		}
		sql3 = sql3.Where("created_at <=  ?", endAt)
	}
	if err := sql3.Count(&data.HaveMemberCode).Error; err != nil {
		return nil, err
	}
	return &data, nil
}

func (r repo) AlGetCommissionSettingUser(userId int64) (*model.AllianceCommissionSettingUser, error) {

	var data model.AllianceCommissionSettingUser

	if err := r.db.Table("user_alliance").
		Select(`
			alias,
			ref_code,
			alliance_percent,
			commission_percent,
			referral_bonus,
			description
		`).
		Where("user_id = ?", userId).
		Scan(&data).
		Error; err != nil {
		return nil, err
	}

	return &data, nil
}

func (r repo) AlGetCommissionSettingUserList(userIds []int64) ([]model.AllianceCommissionSettingUser, error) {

	var data []model.AllianceCommissionSettingUser

	if err := r.db.Table("user_alliance").
		Select(`
			alliance_percent,
			commission_percent,
			user_id
		`).
		Where("user_id IN ?", userIds).
		Scan(&data).
		Error; err != nil {
		return nil, err
	}

	return data, nil
}

func (r repo) AlGetPercentListByUserIds(ids []int64) (*model.AllianceCommissionSettingUser, error) {

	var data model.AllianceCommissionSettingUser

	if err := r.db.Table("user_alliance").
		Select("alliance_percent").
		Where("user_id IN ?", ids).
		Scan(&data).
		Error; err != nil {
		return nil, err
	}

	return &data, nil
}

func (r repo) AlGetRefIdByUserId(userId int64) (int64, error) {

	var refId int64

	if err := r.db.Table("alliance").
		Select("ref_id").
		Where("user_id = ?", userId).
		Scan(&refId).
		Error; err != nil {
		return 0, err
	}

	return refId, nil
}

func (r repo) AlGetTransactionDaily(userId int64, query model.AllianceTransactionDailyQuery) ([]model.AllianceTransaction, int64, error) {

	var total int64
	var list []model.AllianceTransaction

	// [********] Use BBK timezone and confirmed_at (same as reporting)
	selectedFields := "DATE(CONVERT_TZ(tb_transaction.confirmed_at, 'UTC', '+07:00')) as created_at"
	count := r.db.Table("bank_transaction as tb_transaction")
	count = count.Select(selectedFields)
	count = count.Joins("INNER JOIN user as tb_user ON tb_user.id = tb_transaction.user_id")
	if query.From != "" {
		startDateAtBkk, err := r.ParseBodBkk(query.From)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("tb_transaction.confirmed_at >= ? ", startDateAtBkk)
	}
	if query.To != "" {
		endDateAtBkk, err := r.ParseEodBkk(query.To)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("tb_transaction.confirmed_at <=  ?", endDateAtBkk)
	}
	count = count.Where("tb_user.ref_by = ?", userId)
	count = count.Where("tb_transaction.confirmed_at IS NOT NULL")
	count = count.Where("tb_transaction.transaction_status_id IN (?,?)", model.TRANS_STATUS_WITHDRAW_SUCCESS, model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED)
	count = count.Group("DATE(CONVERT_TZ(tb_transaction.confirmed_at, 'UTC', '+07:00'))")
	if err := count.Count(&total).Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		selectedFields := "DATE(CONVERT_TZ(tb_transaction.confirmed_at, 'UTC', '+07:00')) as created_at"
		selectedFields += ", SUM(CASE WHEN tb_transaction.transaction_type_id = 1 THEN tb_transaction.credit_amount ELSE 0 END) AS deposit"
		selectedFields += ", SUM(CASE WHEN tb_transaction.transaction_type_id = 2 THEN tb_transaction.credit_amount ELSE 0 END) AS withdraw"
		selectedFields += ", (SUM(CASE WHEN tb_transaction.transaction_type_id = 1 THEN tb_transaction.credit_amount ELSE 0 END) - SUM(CASE WHEN tb_transaction.transaction_type_id = 2 THEN tb_transaction.credit_amount ELSE 0 END)) AS net_amount"
		exec := r.db.Table("bank_transaction as tb_transaction")
		exec = exec.Select(selectedFields)
		exec = exec.Joins("INNER JOIN user as tb_user ON tb_user.id = tb_transaction.user_id")
		if query.From != "" {
			startDateAtBkk, err := r.ParseBodBkk(query.From)
			if err != nil {
				return nil, 0, err
			}
			exec = exec.Where("tb_transaction.confirmed_at >= ? ", startDateAtBkk)
		}
		if query.To != "" {
			endDateAtBkk, err := r.ParseEodBkk(query.To)
			if err != nil {
				return nil, 0, err
			}
			exec = exec.Where("tb_transaction.confirmed_at <=  ?", endDateAtBkk)
		}

		if query.Limit > 0 {
			exec = exec.Limit(query.Limit)
		}
		exec = exec.Where("tb_user.ref_by = ?", userId)
		exec = exec.Where("tb_transaction.confirmed_at IS NOT NULL")
		exec = exec.Where("tb_transaction.transaction_status_id IN (?,?)", model.TRANS_STATUS_WITHDRAW_SUCCESS, model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED)
		exec = exec.Group("DATE(CONVERT_TZ(tb_transaction.confirmed_at, 'UTC', '+07:00'))")
		exec = exec.Order("DATE(CONVERT_TZ(tb_transaction.confirmed_at, 'UTC', '+07:00')) DESC")
		exec = exec.Offset(query.Limit * query.Page)
		if err := exec.Scan(&list).Error; err != nil {
			return nil, total, err
		}
	}

	return list, total, nil
}

func (r repo) AlGetTransactionSummary(userId int64, query model.AllianceTransactionDailyQuery) (*model.AllianceTransaction, error) {

	var record model.AllianceTransaction

	// [********] Use BBK timezone and confirmed_at (same as reporting)
	selectedFields := "SUM(CASE WHEN tb_transaction.transaction_type_id = 1 THEN tb_transaction.credit_amount ELSE 0 END) AS deposit"
	selectedFields += ", SUM(CASE WHEN tb_transaction.transaction_type_id = 2 THEN tb_transaction.credit_amount ELSE 0 END) AS withdraw"
	selectedFields += ", (SUM(CASE WHEN tb_transaction.transaction_type_id = 1 THEN tb_transaction.credit_amount ELSE 0 END) - SUM(CASE WHEN tb_transaction.transaction_type_id = 2 THEN tb_transaction.credit_amount ELSE 0 END)) AS net_amount"

	exec := r.db.Table("bank_transaction as tb_transaction")
	exec = exec.Select(selectedFields)
	exec = exec.Joins("INNER JOIN user as tb_user ON tb_user.id = tb_transaction.user_id")
	if query.From != "" {
		startDateAtBkk, err := r.ParseBodBkk(query.From)
		if err != nil {
			return nil, err
		}
		exec = exec.Where("tb_transaction.confirmed_at >= ? ", startDateAtBkk)
	}
	if query.To != "" {
		endDateAtBkk, err := r.ParseEodBkk(query.To)
		if err != nil {
			return nil, err
		}
		exec = exec.Where("tb_transaction.confirmed_at <=  ?", endDateAtBkk)
	}
	exec = exec.Where("tb_user.ref_by = ?", userId)
	// [********] Only success transaction
	exec = exec.Where("tb_transaction.transaction_status_id IN (?,?)", model.TRANS_STATUS_WITHDRAW_SUCCESS, model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED)
	exec = exec.Where("tb_transaction.confirmed_at IS NOT NULL")
	if err := exec.Take(&record).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return &record, nil
		}
		return nil, err
	}
	return &record, nil
}

func (r repo) AlGetTransactionMember(userId int64, query model.AllianceTransactionMemberQuery) ([]model.AllianceTransaction, int64, error) {

	var list []model.AllianceTransaction
	var total int64

	// [********] Use BBK timezone and confirmed_at (same as reporting)
	selectedFields := "tb_user.member_code, tb_user.phone"
	count := r.db.Table("bank_transaction as tb_transaction")
	count = count.Select(selectedFields)
	count = count.Joins("INNER JOIN user as tb_user ON tb_user.id = tb_transaction.user_id")
	if query.From != "" {
		startDateAtBkk, err := r.ParseBodBkk(query.From)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("tb_transaction.confirmed_at >= ? ", startDateAtBkk)
	}
	if query.To != "" {
		endDateAtBkk, err := r.ParseEodBkk(query.To)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("tb_transaction.confirmed_at <=  ?", endDateAtBkk)
	}
	if query.MemberCode != "" {
		searchLike := "%" + query.MemberCode + "%"
		count = count.Where("tb_user.member_code LIKE ?", searchLike)
	}
	count = count.Where("tb_user.ref_by = ?", userId)
	count = count.Where("tb_transaction.confirmed_at IS NOT NULL")
	count = count.Where("tb_transaction.transaction_status_id IN (?,?)", model.TRANS_STATUS_WITHDRAW_SUCCESS, model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED)
	count = count.Group("tb_user.member_code, tb_user.phone")
	if err := count.Count(&total).Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		selectedFields := "tb_user.member_code as member_code, tb_user.phone as phone"
		selectedFields += ", SUM(CASE WHEN tb_transaction.transaction_type_id = 1 THEN tb_transaction.credit_amount ELSE 0 END) AS deposit"
		selectedFields += ", SUM(CASE WHEN tb_transaction.transaction_type_id = 2 THEN tb_transaction.credit_amount ELSE 0 END) AS withdraw"
		selectedFields += ", (SUM(CASE WHEN tb_transaction.transaction_type_id = 1 THEN tb_transaction.credit_amount ELSE 0 END) - SUM(CASE WHEN tb_transaction.transaction_type_id = 2 THEN tb_transaction.credit_amount ELSE 0 END)) AS net_amount"
		exec := r.db.Table("bank_transaction as tb_transaction")
		exec = exec.Select(selectedFields)
		exec = exec.Joins("INNER JOIN user as tb_user ON tb_user.id = tb_transaction.user_id")
		if query.From != "" {
			startDateAtBkk, err := r.ParseBodBkk(query.From)
			if err != nil {
				return nil, 0, err
			}
			exec = exec.Where("tb_transaction.confirmed_at >= ? ", startDateAtBkk)
		}
		if query.To != "" {
			endDateAtBkk, err := r.ParseEodBkk(query.To)
			if err != nil {
				return nil, 0, err
			}
			exec = exec.Where("tb_transaction.confirmed_at <=  ?", endDateAtBkk)
		}
		if query.MemberCode != "" {
			searchLike := "%" + query.MemberCode + "%"
			exec = exec.Where("tb_user.member_code LIKE ?", searchLike)
		}
		if query.Limit > 0 {
			exec = exec.Limit(query.Limit)
		}
		exec = exec.Where("tb_user.ref_by = ?", userId)
		exec = exec.Where("tb_transaction.confirmed_at IS NOT NULL")
		exec = exec.Group("tb_user.member_code, tb_user.phone")
		exec = exec.Where("tb_transaction.transaction_status_id IN (?,?)", model.TRANS_STATUS_WITHDRAW_SUCCESS, model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED)
		exec = exec.Offset(query.Limit * query.Page)
		if err := exec.Scan(&list).Error; err != nil {
			return nil, total, err
		}
	}

	return list, total, nil
}

func (r repo) GetMemberTotalIncome(req model.AllianceMemberTotalIncomeListRequest) ([]model.AllianceMemberTotalIncomeResponse, int64, error) {

	var list []model.AllianceMemberTotalIncomeResponse
	var total int64

	// COUNT // User to get real member's count
	selectedFields1 := "tb_user.id"
	count := r.db.Table("user as tb_user")
	count = count.Select(selectedFields1)
	count = count.Where("tb_user.ref_by = ?", req.RefUserId)
	if req.Search != "" {
		searchLike := "%" + req.Search + "%"
		count = count.Where(r.db.Where("tb_user.member_code LIKE ?", searchLike).Or("tb_user.phone LIKE ?", searchLike))
	}
	if err := count.
		Where("tb_user.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_user.id AS user_id"
		selectedFields += ", SUM(tb_incomes.total_play_amount) AS total_play_amount, SUM(tb_incomes.total_win_lose_amount) AS total_win_lose_amount"
		selectedFields += ", SUM(tb_incomes.total_commission) AS total_commission, SUM(tb_incomes.alliance_winlose_amount) AS alliance_winlose_amount"
		selectedFields += ", SUM(tb_incomes.alliance_commission) AS alliance_commission, SUM(tb_incomes.total_paid_bonus) AS total_paid_bonus"
		selectedFields += ", SUM(tb_incomes.alliance_paid_bonus) AS alliance_paid_bonus, SUM(tb_incomes.alliance_income) AS alliance_income"
		query := r.db.Table("user as tb_user")
		query = query.Select(selectedFields)

		// query = query.Joins("LEFT JOIN alliance_winlose_income as tb_incomes ON tb_incomes.user_id = tb_user.id")
		joinCon1 := "LEFT JOIN alliance_winlose_income as tb_incomes ON tb_incomes.user_id = tb_user.id"
		if req.FromDate != "" {
			// of_cut_date [20231227] เปลี่ยนไปดึงจากวันที่เล่นแทน (from layer check) ตารางหน้าบ้านแล้ว
			// query = query.Where("tb_incomes.statement_date >= ? ", req.FromDate)
			joinCon1 += fmt.Sprintf(" AND tb_incomes.statement_date >= '%s'", req.FromDate)
		}
		if req.ToDate != "" {
			// of_cut_date [20231227] เปลี่ยนไปดึงจากวันที่เล่นแทน (from layer check) ตารางหน้าบ้านแล้ว
			// query = query.Where("tb_incomes.statement_date <=  ?", req.ToDate)
			joinCon1 += fmt.Sprintf(" AND tb_incomes.statement_date <= '%s'", req.ToDate)
		}
		query = query.Joins(joinCon1)

		query = query.Where("tb_user.ref_by = ?", req.RefUserId)
		if req.Search != "" {
			searchLike := "%" + req.Search + "%"
			query = query.Where(r.db.Where("tb_user.member_code LIKE ?", searchLike).Or("tb_user.phone LIKE ?", searchLike))
		}

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		query = query.Group("tb_user.id")
		query = query.Order("total_play_amount DESC")
		if err := query.
			Where("tb_user.deleted_at IS NULL").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, 0, err
		}

		// APPEND MEMBER INFO
		userIds := []int64{}
		for _, v := range list {
			userIds = append(userIds, v.UserId)
		}
		var userInfo []struct {
			UserId     int64  `json:"userId"`
			MemberCode string `json:"memberCode"`
		}

		selectedFields1 := "tb_user.id AS user_id, tb_user.member_code AS member_code"
		query1 := r.db.Table("user as tb_user")
		query1 = query1.Select(selectedFields1)
		query1 = query1.Where("tb_user.id IN ?", userIds)
		if err := query1.Scan(&userInfo).Error; err != nil {
			return nil, 0, err
		}

		// MERGE DATA BY USER_ID
		for index, item := range list {
			for _, v2 := range userInfo {
				if item.UserId == v2.UserId {
					list[index].MemberCode = v2.MemberCode
					break
				}
			}
		}
	}
	return list, total, nil
}

func (r repo) GetMemberTotalIncomeSummary(req model.AllianceMemberTotalIncomeListRequest) (*model.AllianceMemberTotalIncomeResponse, error) {

	var record *model.AllianceMemberTotalIncomeResponse

	// SELECT //
	selectedFields1 := "SUM(alliance_incomes.total_play_amount) AS total_play_amount, SUM(alliance_incomes.total_win_lose_amount) AS total_win_lose_amount"
	selectedFields1 += ", SUM(alliance_incomes.total_commission) AS total_commission, SUM(alliance_incomes.alliance_winlose_amount) AS alliance_winlose_amount"
	selectedFields1 += ", SUM(alliance_incomes.alliance_commission) AS alliance_commission, SUM(alliance_incomes.total_paid_bonus) AS total_paid_bonus"
	selectedFields1 += ", SUM(alliance_incomes.alliance_paid_bonus) AS alliance_paid_bonus, SUM(alliance_incomes.alliance_income) AS alliance_income"
	query1 := r.db.Table("alliance_winlose_income as alliance_incomes")
	query1 = query1.Select(selectedFields1)
	query1 = query1.Joins("INNER JOIN user as tb_user ON tb_user.id = alliance_incomes.user_id")
	query1 = query1.Where("tb_user.ref_by = ?", req.RefUserId)

	if req.Search != "" {
		searchLike := "%" + req.Search + "%"
		query1 = query1.Where(r.db.Where("tb_user.member_code LIKE ?", searchLike).Or("tb_user.phone LIKE ?", searchLike))
	}
	if req.FromDate != "" {
		// of_cut_date [20231227] เปลี่ยนไปดึงจากวันที่เล่นแทน (from layer check) ตารางหน้าบ้านแล้ว
		query1 = query1.Where("alliance_incomes.statement_date >= ? ", req.FromDate)
	}
	if req.ToDate != "" {
		// of_cut_date [20231227] เปลี่ยนไปดึงจากวันที่เล่นแทน (from layer check) ตารางหน้าบ้านแล้ว
		query1 = query1.Where("alliance_incomes.statement_date <=  ?", req.ToDate)
	}
	if err := query1.Take(&record).Error; err != nil {
		return nil, err
	}
	record.UserId = req.RefUserId

	return record, nil
}

func (r repo) AlGetFirstDeposit(userId int64, query model.AllianceFirstDepositQuery) ([]model.AllianceFirstDeposit, int64, error) {

	var total int64

	execTotal := r.db.Table("alliance").
		Joins("INNER JOIN bank_transaction bt ON bt.user_id = alliance.user_id").
		Select(`alliance.id`).
		Where("alliance.ref_id = ?", userId).
		Where("bt.is_first_deposit = 1")

	if query.To != nil && query.From != nil {
		execTotal = execTotal.Where("bt.created_at BETWEEN ? AND ?", query.From, query.To)
	}

	if err := execTotal.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total == 0 {
		return nil, total, nil
	}

	var data []model.AllianceFirstDeposit

	exec := r.db.Table("alliance").
		Joins("INNER JOIN bank_transaction bt ON bt.user_id = alliance.user_id").
		Joins("INNER JOIN user ON user.id = alliance.user_id").
		Select(`
			alliance.id,
			user.member_code,
			user.phone,
			bt.credit_amount AS credit,
			bt.created_at
		`).
		Where("alliance.ref_id = ?", userId).
		Where("bt.is_first_deposit = 1")

	if query.To != nil && query.From != nil {
		exec = exec.Where("bt.created_at BETWEEN ? AND ?", query.From, query.To)
	}

	if err := exec.
		Find(&data).
		Order("bt.created_at desc").
		Limit(query.Limit).
		Offset(query.Limit * query.Page).
		Error; err != nil {
		return nil, total, err
	}

	return data, total, nil
}

func (r repo) GetAlListByMembers(members []string) ([]model.PlayerUserList, error) {

	var list []model.PlayerUserList
	var users []model.PlayerUserList

	if err := r.db.Table("user").
		Select("id AS user_id, member_code").
		Where("member_code IN ?", members).
		Scan(&users).Error; err != nil {
		return nil, err
	}

	userId := []int64{}
	for _, v := range users {
		userId = append(userId, v.UserID)
	}

	if err := r.db.Table("alliance").
		Joins("LEFT JOIN user ref ON ref.id = alliance.ref_id").
		Joins("LEFT JOIN user ON user.id = alliance.user_id").
		Select("alliance.user_id, alliance.ref_id, user.member_code").
		Where("alliance.user_id IN ?", userId).
		Scan(&list).Error; err != nil {
		return nil, err
	}

	return list, nil
}

func (r repo) AlCreate(data model.Alliance) error {

	tx := r.db.Begin()

	if err := tx.Table("alliance").
		Create(&data).
		Error; err != nil {
		tx.Rollback()
		return err
	}

	var obj = map[string]interface{}{
		"commission_total":     0.00,
		"commission_current":   0.00,
		"first_deposit_bonus":  0.00,
		"link_click_total":     0,
		"member_total":         0,
		"member_deposit_total": 0,
		"user_id":              data.UserId,
	}

	if err := tx.Table("user_alliance").
		Create(obj).
		Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}

	return nil
}

func (r repo) AlCreateIncome(list []model.AlCreateIncome) error {

	if err := r.db.Table("alliance_income").
		Create(&list).
		Error; err != nil {
		return err
	}

	return nil
}

func (r repo) AlUpdateCommissionSetting(data model.AllianceCommissionBody) error {

	obj := map[string]interface{}{}
	obj["referral_bonus"] = data.ReferralBonus
	obj["description"] = data.Description

	id := 0

	if err := r.db.Table("alliance_commission").
		Select("id").
		Take(&id).
		Error; err != nil {
		return err
	}

	if err := r.db.Table("alliance_commission").
		Where("id = ?", id).
		Updates(obj).
		Error; err != nil {
		return err
	}

	return nil
}

func (r repo) AlUpdateCommissionSettingUser(userId int64, data model.AllianceCommissionSettingUpdateRequest) error {

	exists := 0

	if err := r.db.Table("user").
		Select("id").
		Where("id = ?", userId).
		Take(&exists).
		Error; err != nil {
		return err
	}

	if exists == 0 {
		return errors.New(gorm.ErrRecordNotFound.Error())
	}

	var afObj model.AfSummary
	if err := r.db.Table("user_affiliate").
		Select(`
			link_click_total,
			member_total
		`).
		Where("user_id = ?", userId).
		Take(&afObj).
		Error; err != nil {
		if err != gorm.ErrRecordNotFound {
			return err
		}
	}
	data.LinkClickTotal = afObj.LinkClickTotal
	data.RecommendTotal = afObj.MemberTotal

	// CheckRefCode
	var newRefCode *string
	if data.RefCode != nil {
		// fmt.Println("RefCode", *data.RefCode)
		if *data.RefCode != "" {
			var tempUserAlliance model.UserAlliance
			if err := r.db.Table("user_alliance").
				Select("id, user_id, ref_code").
				Where("ref_code = ?", data.RefCode).
				Where("user_id != ?", userId).
				Take(&tempUserAlliance).
				Error; err != nil {
				if err != gorm.ErrRecordNotFound {
					return err
				}
			}
			if tempUserAlliance.Id != 0 {
				return errors.New("REF_CODE_ALREADY_EXISTS")
			}
		} else {
			// Empty RefCode will be set to NULL
			data.RefCode = nil
			emptyString := ""
			newRefCode = &emptyString
		}
	}

	// CREATE OR UPDATE SETTING
	var id int64
	tx := r.db.Begin()

	if err := tx.Table("user_alliance").
		Select("id").
		Where("user_id = ?", userId).
		Scan(&id).
		Error; err != nil {
		tx.Rollback()
		return nil
	}

	if id == 0 {
		data.UserId = userId
		if err := tx.Table("user_alliance").
			Create(&data).
			Error; err != nil {
			tx.Rollback()
			return err
		}
	} else {
		// make interface
		// fmt.Println("data :", helper.StructJson(data))
		bodyUpdate := map[string]interface{}{}
		bodyUpdate["alias"] = data.Alias
		bodyUpdate["alliance_percent"] = data.AlliancePercent
		bodyUpdate["commission_percent"] = data.CommissionPercent
		bodyUpdate["description"] = data.Description
		bodyUpdate["link_click_total"] = data.LinkClickTotal
		bodyUpdate["recommend_total"] = data.RecommendTotal

		// fmt.Println("bodyUpdate :", helper.StructJson(bodyUpdate))

		if err := tx.Table("user_alliance").
			Where("user_id = ?", userId).
			Updates(&bodyUpdate).
			Error; err != nil {
			tx.Rollback()
			return err
		}
		// SET NULL
		if newRefCode != nil && *newRefCode == "" {
			if err := tx.Table("user_alliance").
				Where("user_id = ?", userId).
				Update("ref_code", nil).
				Error; err != nil {
				tx.Rollback()
				return err
			}
		}
	}

	// SET USER TYPE
	if err := tx.Table("user").
		Where("id = ?", userId).
		Where("user_type_id != ?", model.USER_TYPE_ALLIANCE).
		Update("user_type_id", model.USER_TYPE_ALLIANCE).
		Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}

	return nil
}

func (r repo) AlDownSettingUserToAffiliate(userId int64) error {

	var user *model.GetUserForGenmemberByUserId

	if err := r.db.Table("user").
		Select("id, ref_by, member_code, user_type_id").
		Where("id = ?", userId).
		Where("deleted_at IS NULL").
		Scan(&user).
		Error; err != nil {
		return err
	}
	if user == nil {
		return errors.New(gorm.ErrRecordNotFound.Error())
	}
	if user.UserTypeId != model.USER_TYPE_ALLIANCE {
		return errors.New("USER_IS_NOT_ALLIANCE")
	}

	// SET USER TYPE
	if err := r.db.Table("user").
		Where("id = ?", userId).
		Where("user_type_id = ?", model.USER_TYPE_ALLIANCE).
		Update("user_type_id", model.USER_TYPE_AFFILIATE).
		Error; err != nil {
		return err
	}

	// Migrate Aff Table if not exists
	if err := r.MigrateAllAffiliateMember(userId); err != nil {
		return err
	}

	return nil
}

func (r repo) MigrateAllAffiliateMember(refBy int64) error {

	var memberList []model.User

	if err := r.db.Table("user").
		Select("id, ref_by, member_code, user_type_id").
		Where("ref_by = ?", refBy).
		Where("deleted_at IS NULL").
		Scan(&memberList).
		Error; err != nil {
		return err
	}

	for _, v := range memberList {
		// CHECK IF EXIST //
		var count int64
		if err := r.db.Table("affiliate").Where("ref_id = ?", refBy).Where("user_id = ?", v.Id).Count(&count).Error; err != nil {
			return err
		}
		if count == 0 {
			body := map[string]interface{}{
				"ref_id":  refBy,
				"user_id": v.Id,
			}
			if err := r.db.Table("affiliate").Create(body).Error; err != nil {
				return err
			}
		}
	}

	// update user affiliate member_total
	if err := r.db.Table("user_affiliate").
		Where("user_id = ?", refBy).
		Update("member_total", len(memberList)).
		Error; err != nil {
		return err
	}

	return nil
}

func (r repo) MoveAffiliateUserMember(body model.MoveAffiliateUserMemberBody, userlogInfo model.UserUpdateLogs) error {

	log.Println("MoveAffiliateUserMember.body", helper.StructJson(body))

	var oldAffiliate model.Affiliate
	if err := r.db.Table("affiliate").
		Where("user_id = ?", body.MemberId).
		Where("ref_id = ?", body.OldRefBy).
		Take(&oldAffiliate).
		Error; err != nil {
		log.Println("getOldAff.Notfound", err)
	}

	tx := r.db.Begin()

	var changes model.UserUpdateLogs
	changes.UserId = body.MemberId
	if body.OldRefByMemberCode == "" {
		changes.Description = fmt.Sprintf("ผู้ใช้ %s ย้ายไปต่อหัวใหม่ เป็น %s", body.MemberCode, body.NewRefByMemberCode)
	} else {
		changes.Description = fmt.Sprintf("ผู้ใช้ %s ย้ายไปต่อหัวใหม่ จาก %s เป็น %s", body.MemberCode, body.OldRefByMemberCode, body.NewRefByMemberCode)
	}
	changes.CreatedByUsername = userlogInfo.CreatedByUsername
	changes.Ip = userlogInfo.Ip

	if err := r.db.Table("user").
		Where("id = ?", body.MemberId).
		Update("ref_by", body.NewRefBy).
		Error; err != nil {
		return err
	}

	if oldAffiliate.ID != 0 {
		if err := r.db.Table("affiliate").
			Where("user_id = ?", body.MemberId).
			Where("id = ?", oldAffiliate.ID).
			Update("ref_id", body.NewRefBy).
			Error; err != nil {
			return err
		}
	} else {
		affiliate := map[string]interface{}{}
		affiliate["ref_id"] = body.NewRefBy
		affiliate["user_id"] = body.MemberId
		if err := r.db.Table("affiliate").
			Create(&affiliate).
			Error; err != nil {
			return err
		}
	}

	if err := tx.Table("user_update_log").
		Create(&changes).
		Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}

	// update Both Upline affiliate member_total
	if err := r.updateAffiliateMemberCount(body.NewRefBy); err != nil {
		log.Println("updateAffiliateMemberCount", err)
	}
	if err := r.updateAffiliateMemberCount(body.OldRefBy); err != nil {
		log.Println("updateAffiliateMemberCount", err)
	}

	return nil
}

func (r repo) RemoveAffiliateUserMember(body model.MoveAffiliateUserMemberBody, userlogInfo model.UserUpdateLogs) error {

	log.Println("RemoveAffiliateUserMember.body", helper.StructJson(body))

	// ถ้า REF = 0 ก็ทำงานได้ปกติ

	// ** ตารางเก็บค่าสมัครครั้งแรก ไม่ได้สำคัญ ไม่ต้องเอาออกก็ได้ ถ้าหัวโดนลบ
	var oldAffiliate model.Affiliate
	if err := r.db.Table("affiliate").
		Where("user_id = ?", body.MemberId).
		Where("ref_id = ?", body.OldRefBy).
		Take(&oldAffiliate).
		Error; err != nil {
		log.Println("getOldAff.Notfound", err)
	}

	tx := r.db.Begin()

	if err := r.db.Table("user").
		Where("id = ?", body.MemberId).
		Update("ref_by", nil).
		Error; err != nil {
		return err
	}

	if oldAffiliate.ID != 0 {
		if err := r.db.Table("affiliate").
			Where("user_id = ?", body.MemberId).
			Where("id = ?", oldAffiliate.ID).
			Delete(&model.Affiliate{}).
			Error; err != nil {
			return err
		}
	}

	if body.OldRefByMemberCode != "" {
		var changes model.UserUpdateLogs
		changes.UserId = body.MemberId
		changes.Description = fmt.Sprintf("ลบหัว %s ออกจากผู้ใช้ %s แล้ว", body.OldRefByMemberCode, body.MemberCode)
		changes.CreatedByUsername = userlogInfo.CreatedByUsername
		changes.Ip = userlogInfo.Ip

		if err := tx.Table("user_update_log").
			Create(&changes).
			Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}

	// update Both Upline affiliate member_total
	if err := r.updateAffiliateMemberCount(body.OldRefBy); err != nil {
		log.Println("updateAffiliateMemberCount", err)
	}

	return nil
}

func (r repo) updateAffiliateMemberCount(refBy int64) error {

	var memberCount int64

	if err := r.db.Table("user").
		Select("id, ref_by, member_code, user_type_id").
		Where("ref_by = ?", refBy).
		Where("deleted_at IS NULL").
		Count(&memberCount).
		Error; err != nil {
		return err
	}
	if err := r.db.Table("user_affiliate").
		Where("user_id = ?", refBy).
		Update("member_total", memberCount).
		Error; err != nil {
		return err
	}
	return nil
}

func (r repo) AlUpdateCommissionFirstDeposit(userId, refId int64, amount float64) error {

	tx := r.db.Begin()

	firstDepositObj := map[string]interface{}{}
	firstDepositObj["credit"] = amount
	firstDepositObj["user_id"] = userId
	firstDepositObj["ref_id"] = refId

	if err := tx.Table("alliance_first_deposit").
		Create(&firstDepositObj).
		Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Table("user").
		Where("id = ?", userId).
		Update("user_type_id", model.USER_TYPE_ALLIANCE).
		Error; err != nil {
		return err
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}

	return nil
}

func (r repo) AlIncreaseLinkClick(userId int64) error {

	if err := r.db.Table("user_alliance").
		Where("user_id = ?", userId).
		Update("link_click_total", gorm.Expr("link_click_total + ?", 1)).
		Error; err != nil {
		return err
	}

	return nil
}

func (r repo) GetFirstDepositAllianceList(HeadAllianceId int64, req model.GetFirstDepositAliianceListRequest) ([]model.GetFirstDepositAliianceListResponse, *int64, error) {

	var list []model.GetFirstDepositAliianceListResponse
	var total int64

	// [********] แบบใหม่ ดึงจาก bank_transaction แทน
	// [********] แบบใหม่ ดึงจาก user แทน เพื่อเอาทุกคน

	count := r.db.Table("user as tb_user")
	count = count.Select("tb_user.id")
	count = count.Where("tb_user.ref_by = ?", HeadAllianceId)
	if req.From != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.From)
		if err != nil {
			return nil, nil, err
		}
		count = count.Where("tb_user.created_at >= ? ", startDateAtBkk)
	}
	if req.To != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.To)
		if err != nil {
			return nil, nil, err
		}
		count = count.Where("tb_user.created_at <=  ?", endDateAtBkk)
	}
	if req.Limit > 0 {
		count = count.Limit(req.Limit)
	}
	if err := count.Count(&total).Error; err != nil {
		return nil, nil, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_user.id AS id, tb_user.member_code AS member_code, tb_user.Phone AS phone"
		selectedFields += ", tb_transaction.credit_amount AS credit , tb_user.created_at AS register_date"
		query := r.db.Table("user as tb_user")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN bank_transaction as tb_transaction ON tb_transaction.user_id = tb_user.id AND tb_transaction.is_first_deposit = 1")
		query = query.Where("tb_user.ref_by = ?", HeadAllianceId)
		if req.From != "" {
			startDateAtBkk, err := r.ParseBodBkk(req.From)
			if err != nil {
				return nil, nil, err
			}
			query = query.Where("tb_user.created_at >= ? ", startDateAtBkk)
		}
		if req.To != "" {
			endDateAtBkk, err := r.ParseEodBkk(req.To)
			if err != nil {
				return nil, nil, err
			}
			query = query.Where("tb_user.created_at <=  ?", endDateAtBkk)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		query = query.Order("tb_transaction.credit_amount DESC, tb_user.id ASC")
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, nil, err
		}

		// Apppend Fist Deposit Date
		userIds := []int64{}
		for _, v := range list {
			userIds = append(userIds, v.Id)
		}

		var firstDepositDate []struct {
			UserId      int64     `json:"user_id"`
			ConfirmedAt time.Time `json:"confirmed_at"`
		}
		if err := r.db.Table("bank_transaction").
			Select("user_id, confirmed_at").
			Where("user_id IN ?", userIds).
			Where("is_first_deposit = 1").
			Scan(&firstDepositDate).
			Error; err != nil {
			return nil, nil, err
		}

		for index, item := range list {
			for _, v2 := range firstDepositDate {
				if item.Id == v2.UserId {
					list[index].FirstDepositAt = &v2.ConfirmedAt
					break
				}
			}
		}

	}
	return list, &total, nil
}

func (r repo) GetFirstDepositAllianceSummary(HeadAllianceId int64, req model.GetFirstDepositAliianceSummaryRequest) (*model.GetFirstDepositAllianceSummary, error) {

	// ควร แยก count อย่ารวม
	var data model.GetFirstDepositAllianceSummary

	// [********] แบบใหม่ ดึงจาก user แทน
	countNoMember := r.db.Table("user as tb_user")
	countNoMember = countNoMember.Where("tb_user.ref_by = ?", HeadAllianceId)
	countNoMember = countNoMember.Where("tb_user.member_code IS NULL")
	countNoMember = countNoMember.Select("tb_user.id")
	if req.From != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.From)
		if err != nil {
			return nil, err
		}
		countNoMember = countNoMember.Where("tb_user.created_at >= ? ", startDateAtBkk)
	}
	if req.To != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.To)
		if err != nil {
			return nil, err
		}
		countNoMember = countNoMember.Where("tb_user.created_at <=  ?", endDateAtBkk)
	}
	if err := countNoMember.
		Count(&data.NoMemberCode).
		Error; err != nil {
		return nil, err
	}

	// [********] แบบใหม่ ดึงจาก user แทน later:ดึงรวมได้ทีเดียว
	countMember := r.db.Table("user as tb_user")
	countMember = countMember.Where("tb_user.ref_by = ?", HeadAllianceId)
	countMember = countMember.Where("tb_user.member_code IS NOT NULL")
	countMember = countMember.Select("tb_user.id")
	if req.From != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.From)
		if err != nil {
			return nil, err
		}
		countMember = countMember.Where("tb_user.created_at >= ? ", startDateAtBkk)
	}
	if req.To != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.To)
		if err != nil {
			return nil, err
		}
		countMember = countMember.Where("tb_user.created_at <=  ?", endDateAtBkk)
	}
	if err := countMember.
		Count(&data.HaveMemberCode).
		Error; err != nil {
		return nil, err
	}

	// [********] แบบใหม่ ดึงจาก bank_transaction แทน
	// countFirstDeposit := r.db.Table("bank_transaction as tb_transaction")
	// countFirstDeposit = countFirstDeposit.Joins("LEFT JOIN user as tb_user ON tb_user.id = tb_transaction.user_id")
	// countFirstDeposit = countFirstDeposit.Where("tb_user.ref_by = ?", HeadAllianceId)
	// countFirstDeposit = countFirstDeposit.Where("tb_transaction.is_first_deposit = ?", true)
	// countFirstDeposit = countFirstDeposit.Select("tb_user.id")
	// if req.From != "" {
	// 	startDateAtBkk, err := r.ParseBodBkk(req.From)
	// 	if err != nil {
	// 		return nil, err
	// 	}
	// 	countFirstDeposit = countFirstDeposit.Where("tb_user.created_at >= ? ", startDateAtBkk)
	// }
	// if req.To != "" {
	// 	endDateAtBkk, err := r.ParseEodBkk(req.To)
	// 	if err != nil {
	// 		return nil, err
	// 	}
	// 	countFirstDeposit = countFirstDeposit.Where("tb_user.created_at <=  ?", endDateAtBkk)
	// }

	// if err := countFirstDeposit.
	// 	Count(&data.FirstDepositCount).
	// 	Error; err != nil {
	// 	return nil, err
	// }

	// Count With Sum
	var totalDepositData []struct {
		FirstDepositCount       int64   `json:"first_deposit_count"`
		TotalFirstDepositAmount float64 `json:"total_first_deposit_amount"`
	}
	totalFirstDeposit := r.db.Table("bank_transaction as tb_transaction")
	totalFirstDeposit = totalFirstDeposit.Joins("LEFT JOIN user as tb_user ON tb_user.id = tb_transaction.user_id")
	totalFirstDeposit = totalFirstDeposit.Where("tb_user.ref_by = ?", HeadAllianceId)
	totalFirstDeposit = totalFirstDeposit.Where("tb_transaction.is_first_deposit = ?", true)
	totalFirstDeposit = totalFirstDeposit.Select("COUNT(tb_transaction.id) AS first_deposit_count, SUM(tb_transaction.credit_amount) AS total_first_deposit_amount")
	if req.From != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.From)
		if err != nil {
			return nil, err
		}
		totalFirstDeposit = totalFirstDeposit.Where("tb_transaction.created_at >= ? ", startDateAtBkk)
	}
	if req.To != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.To)
		if err != nil {
			return nil, err
		}
		totalFirstDeposit = totalFirstDeposit.Where("tb_transaction.created_at <=  ?", endDateAtBkk)
	}
	if err := totalFirstDeposit.
		Scan(&totalDepositData).
		Error; err != nil {
		return nil, err
	}

	if len(totalDepositData) > 0 {
		data.FirstDepositCount = totalDepositData[0].FirstDepositCount
		data.TotalFirstDepositAmount = totalDepositData[0].TotalFirstDepositAmount
	}

	return &data, nil
}

func (r repo) GetAllianceUserList(req model.AllianceUserListRequest) ([]model.AllianceUserResponse, int64, error) {

	var list []model.AllianceUserResponse
	var total int64

	// alliance_repository.go:1706 SLOW SQL >= 200ms -- ref_by ไม่เกิน 100 แน่นอน + idx_created_at` ON `bank_transaction` เร็วแล้ว

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return nil, 0, err
	}

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("user as users")
	count = count.Select("users.id")
	count = count.Where("users.user_type_id = ?", model.USER_TYPE_ALLIANCE)
	if req.Search != "" {
		searchLike := "%" + req.Search + "%"
		count = count.Where(r.db.Where("users.member_code LIKE ?", searchLike))
	}
	if err := count.
		Where("users.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "users.id AS user_id, users.member_code AS member_code, users.username AS username, users.fullname AS user_fullname"
		selectedFields += ", alliance_details.alias AS alliance_name, alliance_details.alliance_percent AS alliance_percent"
		selectedFields += ", users.user_type_id AS user_type_id"
		query := r.db.Table("user as users")
		query = query.Joins("LEFT JOIN user_alliance as alliance_details ON alliance_details.user_id = users.id")
		query = query.Select(selectedFields)
		query = query.Where("users.user_type_id = ?", model.USER_TYPE_ALLIANCE)
		if req.Search != "" {
			searchLike := "%" + req.Search + "%"
			query = query.Where(r.db.Where("users.member_code LIKE ?", searchLike))
		}
		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Where("users.deleted_at IS NULL").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, 0, err
		}

		// APPEND TOTAL DATA
		refIds := []int64{}
		for _, v := range list {
			refIds = append(refIds, v.UserId)
		}

		// REF-Clicked in This Time
		var linkLog []struct {
			RefBy      int64 `json:"ref_by"`
			TotalClick int64 `json:"total_click"`
		}
		queryLinkClick := r.db.Table("affiliate_link_click").
			Select("ref_by as ref_by, COUNT(*) AS total_click").
			Where("ref_by IN ?", refIds)

		if dateType.DateFrom != "" {
			startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
			if err != nil {
				return nil, 0, err
			}
			queryLinkClick = queryLinkClick.Where("created_at >= ? ", startDateAtBkk)
		}
		if dateType.DateTo != "" {
			endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
			if err != nil {
				return nil, 0, err
			}
			queryLinkClick = queryLinkClick.Where("created_at <=  ?", endDateAtBkk)
		}
		queryLinkClick = queryLinkClick.Group("ref_by")
		if err := queryLinkClick.Scan(&linkLog).
			Error; err != nil {
			return nil, 0, err
		}

		// user that ref_by me and register(created_at) between start and end date
		var userRegisterLog []struct {
			RefBy               int64 `json:"ref_by"`
			NoMemberCodeTotal   int64 `json:"noMemberCodeTotal"`
			HaveMemberCodeTotal int64 `json:"haveMemberCodeTotal"`
			RecommendTotal      int64 `json:"recommendTotal"`
		}

		selectedFields1 := "ref_by as ref_by, COUNT(CASE WHEN member_code IS NULL THEN 1 END) AS no_member_code_total"
		selectedFields1 += ", COUNT(CASE WHEN member_code IS NOT NULL AND member_code != '' THEN 1 END) AS have_member_code_total"
		selectedFields1 += ", COUNT(*) AS recommend_total"
		query1 := r.db.Table("user").
			Select(selectedFields1).
			Where("ref_by IN ?", refIds).
			Where("deleted_at IS NULL").
			Group("ref_by")

		if dateType.DateFrom != "" {
			startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
			if err != nil {
				return nil, 0, err
			}
			query1 = query1.Where("created_at >= ? ", startDateAtBkk)
		}
		if dateType.DateTo != "" {
			endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
			if err != nil {
				return nil, 0, err
			}
			query1 = query1.Where("created_at <=  ?", endDateAtBkk)
		}

		if err := query1.Scan(&userRegisterLog).Error; err != nil {
			return nil, 0, err
		}

		// get bank_transaction that first_deposit = 1 and user_id in (userIds) and created_at between start and end date
		var userTransaction []struct {
			RefBy              int64   `json:"ref_by"`
			FirstDepositAmount float64 `json:"firstDepositAmount"`
		}
		selectedFields2 := "user.ref_by as ref_by, SUM(logs.credit_amount) AS first_deposit_amount"
		query2 := r.db.Table("bank_transaction as logs").
			Select(selectedFields2).
			Joins("INNER JOIN user ON user.id = logs.user_id").
			Where("logs.transaction_type_id = ?", model.TRANSACTION_TYPE_DEPOSIT).
			Where("logs.is_first_deposit = ?", true).
			Where("user.ref_by IN ?", refIds).
			Group("user.ref_by")

		if dateType.DateFrom != "" {
			startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
			if err != nil {
				return nil, 0, err
			}
			query2 = query2.Where("logs.created_at >= ? ", startDateAtBkk)
		}
		if dateType.DateTo != "" {
			endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
			if err != nil {
				return nil, 0, err
			}
			query2 = query2.Where("logs.created_at <=  ?", endDateAtBkk)
		}
		query2 = query2.Where("user.deleted_at IS NULL")

		if err := query2.Scan(&userTransaction).Error; err != nil {
			return nil, 0, err
		}

		// MERGE DATA BY USER_ID
		allianceUserIds := []int64{}
		for index, item := range list {
			if item.UserTypeId == model.USER_TYPE_ALLIANCE {
				allianceUserIds = append(allianceUserIds, item.UserId)
			}
			list[index].AllianceCode = helper.EncodeData(item.UserId)
			for _, v2 := range linkLog {
				if item.UserId == v2.RefBy {
					list[index].LinkClickTotal = v2.TotalClick
					break
				}
			}
			for _, v2 := range userRegisterLog {
				if item.UserId == v2.RefBy {
					list[index].NoMemberCodeTotal = v2.NoMemberCodeTotal
					list[index].HaveMemberCodeTotal = v2.HaveMemberCodeTotal
					list[index].RecommendTotal = v2.RecommendTotal
					break
				}
			}
			for _, v3 := range userTransaction {
				if item.UserId == v3.RefBy {
					list[index].FirstDepositAmount = v3.FirstDepositAmount
					break
				}
			}
		}

		// REF_CODE
		if len(allianceUserIds) > 0 {
			var query model.UserAllianceListRequest
			query.UserIds = allianceUserIds
			allSettingList, _, err := r.GetUserAllianceInfoList(query)
			if err != nil {
				log.Println("GetUserAllianceInfoList", err)
			}
			for i, v := range list {
				// FOR Alliance ONLY
				if v.UserTypeId == model.USER_TYPE_ALLIANCE {
					for _, v2 := range allSettingList {
						if v.UserId == v2.UserId && v2.RefCode != "" {
							list[i].AllianceCode = v2.RefCode
						}
					}
				}
			}
		}

	}
	return list, total, nil
}

func (r repo) GetAllianceFirstDepositSummary(req model.AllianceFirstDepositListRequest) (*model.AllianceFirstDepositSummaryResponse, error) {

	var record model.AllianceFirstDepositSummaryResponse

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return nil, err
	}

	// SELECT // use User RegisterAt
	selectedFields := "COUNT(*) AS first_deposit_total"
	selectedFields += ", SUM(tb_logs.credit_amount) AS first_deposit_amount"

	query := r.db.Table("bank_transaction as tb_logs")
	query = query.Select(selectedFields)
	query = query.Joins("INNER JOIN user as tb_user ON tb_user.id = tb_logs.user_id")
	query = query.Where("tb_logs.transaction_type_id = ?", model.TRANSACTION_TYPE_DEPOSIT)
	query = query.Where("tb_logs.is_first_deposit = ?", true)
	query = query.Where("tb_user.ref_by = ?", req.RefUserId)
	query = query.Where("tb_user.deleted_at IS NULL")
	// เปลียนจาก createdAt เป็น confirm_at [********] หน้านี้ขอเป็นกรองตามวันเวลา ฝากครั้งแรกค่ะพี่
	// if dateType.DateFrom != "" {
	// 	startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
	// 	if err != nil {
	// 		return nil, err
	// 	}
	// 	query = query.Where("tb_user.created_at >= ? ", startDateAtBkk)
	// }
	// if dateType.DateTo != "" {
	// 	endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
	// 	if err != nil {
	// 		return nil, err
	// 	}
	// 	query = query.Where("tb_user.created_at <=  ?", endDateAtBkk)
	// }
	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return nil, err
		}
		query = query.Where("tb_logs.created_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return nil, err
		}
		query = query.Where("tb_logs.created_at <=  ?", endDateAtBkk)
	}

	query = query.Group("tb_user.ref_by")
	if err := query.Take(&record).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			record.RefUserId = req.RefUserId
			return &record, nil
		}
		return nil, err
	}

	// Get real user count * BUG if no bank_transaction
	var userCountData []struct {
		NoMemberCodeTotal   int64 `json:"noMemberCodeTotal"`
		HaveMemberCodeTotal int64 `json:"haveMemberCodeTotal"`
	}
	selectedFields2 := "tb_user.ref_by AS ref_user_id, COUNT(CASE WHEN tb_user.member_code IS NULL THEN 1 END) AS no_member_code_total"
	selectedFields2 += ", COUNT(CASE WHEN tb_user.member_code IS NOT NULL AND tb_user.member_code != '' THEN 1 END) AS have_member_code_total"
	query2 := r.db.Table("user as tb_user")
	query2 = query2.Select(selectedFields2)
	query2 = query2.Where("tb_user.ref_by = ?", req.RefUserId)
	query2 = query2.Where("tb_user.deleted_at IS NULL")
	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return nil, err
		}
		query2 = query2.Where("tb_user.created_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return nil, err
		}
		query2 = query2.Where("tb_user.created_at <=  ?", endDateAtBkk)
	}
	query2 = query2.Group("tb_user.ref_by")
	if err := query2.Scan(&userCountData).Error; err != nil {
		return nil, err
	}

	if len(userCountData) > 0 {
		record.NoMemberCodeTotal = userCountData[0].NoMemberCodeTotal
		record.HaveMemberCodeTotal = userCountData[0].HaveMemberCodeTotal
	}

	return &record, nil
}

func (r repo) GetAllianceFirstDepositList(req model.AllianceFirstDepositListRequest) ([]model.AllianceUserFirstDepositResponse, int64, error) {

	var list []model.AllianceUserFirstDepositResponse
	var total int64

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return nil, 0, err
	}

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("bank_transaction as logs")
	count = count.Select("logs.id")
	count = count.Joins("INNER JOIN user ON user.id = logs.user_id")
	count = count.Where("logs.transaction_type_id = ?", model.TRANSACTION_TYPE_DEPOSIT)
	count = count.Where("logs.is_first_deposit = ?", true)
	count = count.Where("user.ref_by = ?", req.RefUserId)
	count = count.Where("user.deleted_at IS NULL")

	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("logs.created_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("logs.created_at <=  ?", endDateAtBkk)
	}

	if err := count.Count(&total).Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "user.id AS user_id, user.member_code AS member_code, user.created_at AS register_at"
		selectedFields += ", logs.created_at AS deposit_at, logs.credit_amount AS deposit_amount"
		query := r.db.Table("bank_transaction as logs")
		query = query.Select(selectedFields)
		query = query.Joins("INNER JOIN user ON user.id = logs.user_id")
		query = query.Where("logs.transaction_type_id = ?", model.TRANSACTION_TYPE_DEPOSIT)
		query = query.Where("logs.is_first_deposit = ?", true)
		query = query.Where("user.ref_by = ?", req.RefUserId)
		if dateType.DateFrom != "" {
			startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("logs.created_at >= ? ", startDateAtBkk)
		}
		if dateType.DateTo != "" {
			endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("logs.created_at <=  ?", endDateAtBkk)
		}
		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Where("user.deleted_at IS NULL").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, 0, err
		}
	}
	return list, total, nil
}

func (r repo) CreateWithdrawAllianceIncomeLog(body model.AllianceIncomeWithdrawCreateBody) (*int64, error) {

	if err := r.db.Table("alliance_income_withdraw_log").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) GetAllianceIncomeWithdrawHistory(req model.AllianceIncomeWithdrawHistoryListRequest) ([]model.AllianceIncomeWithdrawHistoryResponse, int64, error) {

	var list []model.AllianceIncomeWithdrawHistoryResponse
	var total int64

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("alliance_income_withdraw_log as logs")
	count = count.Select("logs.id")
	count = count.Where("logs.ref_user_id = ?", req.RefUserId)
	if req.FromDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.FromDate)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("logs.created_at >= ? ", startDateAtBkk)
	}
	if req.ToDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.ToDate)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("logs.created_at <=  ?", endDateAtBkk)
	}
	if err := count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "logs.id AS id, logs.ref_user_id AS ref_user_id, logs.from_date AS from_date, logs.to_date AS to_date"
		selectedFields += ", logs.income_amount AS income_amount, logs.created_at AS created_at, logs.created_by AS created_by"
		selectedFields += ", admins.username AS created_by_username"
		query := r.db.Table("alliance_income_withdraw_log as logs")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN admin as admins ON admins.id = logs.created_by")
		query = query.Where("logs.ref_user_id = ?", req.RefUserId)
		if req.FromDate != "" {
			startDateAtBkk, err := r.ParseBodBkk(req.FromDate)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("logs.created_at >= ? ", startDateAtBkk)
		}
		if req.ToDate != "" {
			endDateAtBkk, err := r.ParseEodBkk(req.ToDate)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("logs.created_at <=  ?", endDateAtBkk)
		}

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		} else {
			query = query.Order("logs.id DESC")
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetAllianceWinloseIncomeList(req model.AllianceWinLoseHistoryListRequest) ([]model.AllianceWinloseIncome, int64, error) {

	var list []model.AllianceWinloseIncome
	var total int64

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("alliance_winlose_income as alliance_incomes")
	count = count.Select("alliance_incomes.id")
	count = count.Joins("LEFT JOIN user as users ON users.id = alliance_incomes.user_id")
	count = count.Where("users.ref_by = ?", req.RefUserId)
	if req.FromDate != "" {
		// of_cut_date [20231227] เปลี่ยนไปดึงจากวันที่เล่นแทน (from layer check)
		count = count.Where("alliance_incomes.statement_date >= ? ", req.FromDate)
	}
	if req.ToDate != "" {
		// of_cut_date [20231227] เปลี่ยนไปดึงจากวันที่เล่นแทน (from layer check)
		count = count.Where("alliance_incomes.statement_date <=  ?", req.ToDate)
	}
	if err := count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "alliance_incomes.id AS id, alliance_incomes.user_id AS user_id, alliance_incomes.status_id AS status_id"
		selectedFields += ", alliance_incomes.daily_key AS daily_key, alliance_incomes.of_date AS of_date, alliance_incomes.statement_date AS statement_date, alliance_incomes.total_play_amount AS total_play_amount"
		selectedFields += ", alliance_incomes.total_win_lose_amount AS total_win_lose_amount, alliance_incomes.commission_percent AS commission_percent"
		selectedFields += ", alliance_incomes.alliance_percent AS alliance_percent, alliance_incomes.total_commission AS total_commission"
		selectedFields += ", alliance_incomes.alliance_winlose_amount AS alliance_winlose_amount, alliance_incomes.alliance_commission AS alliance_commission"
		selectedFields += ", alliance_incomes.total_paid_bonus AS total_paid_bonus, alliance_incomes.alliance_paid_bonus AS alliance_paid_bonus"
		selectedFields += ", alliance_incomes.alliance_income AS alliance_income, alliance_incomes.take_at AS take_at, alliance_incomes.taken_price AS taken_price"
		selectedFields += ", alliance_incomes.created_at AS created_at, alliance_incomes.updated_at AS updated_at"
		query := r.db.Table("alliance_winlose_income as alliance_incomes")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN user as users ON users.id = alliance_incomes.user_id")
		query = query.Where("users.ref_by = ?", req.RefUserId)
		if req.FromDate != "" {
			// of_cut_date  [20231227] เปลี่ยนไปดึงจากวันที่เล่นแทน (from layer check)
			query = query.Where("alliance_incomes.statement_date >= ? ", req.FromDate)
		}
		if req.ToDate != "" {
			// of_cut_date [20231227] เปลี่ยนไปดึงจากวันที่เล่นแทน (from layer check)
			query = query.Where("alliance_incomes.statement_date <=  ?", req.ToDate)
		}

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}

	return list, total, nil
}

func (r repo) UpdateAllianceIncomeTakenList(ids []int64) error {

	updateData := map[string]interface{}{
		"take_at":     time.Now(),
		"taken_price": gorm.Expr("alliance_income"),
	}

	query := r.db.Table("alliance_winlose_income").Where("id IN ?", ids).Where("take_at IS NULL")
	if err := query.Updates(updateData).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetAllianceWinLoseHistory(req model.AllianceWinLoseHistoryListRequest) ([]model.AllianceWinLoseHistoryResponse, int64, error) {

	var list []model.AllianceWinLoseHistoryResponse
	var total int64

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return nil, 0, err
	}

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("alliance_winlose_income as tb_log")
	count = count.Select("tb_log.user_id")
	count = count.Joins("INNER JOIN user as tb_user ON tb_user.id = tb_log.user_id")
	count = count.Where("tb_user.ref_by = ?", req.RefUserId)
	if dateType.DateFrom != "" {
		// of_cut_date [20231227] เปลี่ยนไปดึงจากวันที่เล่นแทน (from layer check)
		count = count.Where("tb_log.statement_date >= ? ", dateType.DateFrom)
	}
	if dateType.DateTo != "" {
		// of_cut_date [20231227] เปลี่ยนไปดึงจากวันที่เล่นแทน (from layer check)
		count = count.Where("tb_log.statement_date <=  ?", dateType.DateTo)
	}
	count = count.Group("tb_log.user_id")
	if err := count.
		Where("tb_user.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields1 := "tb_log.user_id, SUM(tb_log.total_play_amount) AS total_play_amount, SUM(tb_log.total_win_lose_amount) AS total_win_lose_amount"
		selectedFields1 += ", SUM(tb_log.total_commission) AS total_commission, SUM(tb_log.alliance_winlose_amount) AS alliance_winlose_amount"
		selectedFields1 += ", SUM(tb_log.alliance_commission) AS alliance_commission, SUM(tb_log.total_paid_bonus) AS total_paid_bonus"
		selectedFields1 += ", SUM(tb_log.alliance_paid_bonus) AS alliance_paid_bonus, SUM(tb_log.alliance_income) AS alliance_income"
		selectedFields1 += ", SUM(CASE WHEN tb_log.take_at IS NULL THEN tb_log.alliance_income ELSE 0 END) AS alliance_pending_income"
		query := r.db.Table("alliance_winlose_income as tb_log")
		query = query.Select(selectedFields1)
		query = query.Joins("INNER JOIN user as tb_user ON tb_user.id = tb_log.user_id")
		query = query.Where("tb_user.ref_by = ?", req.RefUserId)
		if dateType.DateFrom != "" {
			// of_cut_date [20231227] เปลี่ยนไปดึงจากวันที่เล่นแทน (from layer check)
			query = query.Where("tb_log.statement_date >= ? ", dateType.DateFrom)
		}
		if dateType.DateTo != "" {
			// of_cut_date [20231227] เปลี่ยนไปดึงจากวันที่เล่นแทน (from layer check)
			query = query.Where("tb_log.statement_date <=  ?", dateType.DateTo)
		}
		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		query = query.Group("tb_log.user_id")
		if err := query.
			Where("tb_user.deleted_at IS NULL").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, 0, err
		}

		// APPEND TOTAL DATA *DOWNLINE ONLY*
		userIds := []int64{}
		for _, v := range list {
			userIds = append(userIds, v.UserId)
		}

		// เอามาจาก alliance_winlose_income
		var userPlayLog []struct {
			UserId     int64  `json:"userId"`
			MemberCode string `json:"memberCode"`
			RegisterAt string `json:"registerAt"`
		}

		selectedFields := "users.ref_by AS ref_user_id, users.id AS user_id, users.member_code AS member_code, users.created_at AS register_at"
		query1 := r.db.Table("user as users").
			Select(selectedFields).
			Where("users.id IN ?", userIds).
			Group("users.id")

		if err := query1.Scan(&userPlayLog).Error; err != nil {
			return nil, 0, err
		}

		// MERGE DATA BY USER_ID
		for index, item := range list {
			for _, v2 := range userPlayLog {
				if item.UserId == v2.UserId {
					list[index].MemberCode = v2.MemberCode
					list[index].RegisterAt = v2.RegisterAt
					break
				}
			}
		}
	}
	return list, total, nil
}

func (r repo) GetAllianceUsersWinLoseHistory(req model.AllianceWinLoseHistoryListRequest) ([]model.AllianceWinLoseHistoryResponse, int64, error) {

	var list []model.AllianceWinLoseHistoryResponse
	var total int64

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return nil, 0, err
	}

	// 1.Get Member and Setting
	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("user as users")
	count = count.Select("users.id")
	count = count.Where("users.ref_by = ?", req.RefUserId)
	if err := count.
		Where("users.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "users.ref_by AS ref_user_id, users.id AS user_id, users.member_code AS member_code, users.created_at AS register_at"
		query := r.db.Table("user as users")
		query = query.Select(selectedFields)
		query = query.Where("users.ref_by = ?", req.RefUserId)
		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Where("users.deleted_at IS NULL").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, 0, err
		}

		// APPEND TOTAL DATA *DOWNLINE ONLY*
		userIds := []int64{}
		for _, v := range list {
			userIds = append(userIds, v.UserId)
		}

		// เอามาจาก alliance_winlose_income
		var userPlayLog []struct {
			UserId                int64   `json:"userId"`
			TotalPlayAmount       float64 `json:"totalPlayAmount"`
			TotalWinLoseAmount    float64 `json:"totalWinLoseAmount"`
			TotalCommission       float64 `json:"totalCommission"`
			AllianceWinloseAmount float64 `json:"allianceWinloseAmount"`
			AllianceCommission    float64 `json:"allianceCommission"`
			TotalPaidBonus        float64 `json:"totalPaidBonus"`
			AlliancePaidBonus     float64 `json:"alliancePaidBonus"`
			AllianceIncome        float64 `json:"allianceIncome"`
			AlliancePendingIncome float64 `json:"alliancePendingIncome"`
		}

		selectedFields1 := "alliance_incomes.user_id, SUM(alliance_incomes.total_play_amount) AS total_play_amount, SUM(alliance_incomes.total_win_lose_amount) AS total_win_lose_amount"
		selectedFields1 += ", SUM(alliance_incomes.total_commission) AS total_commission, SUM(alliance_incomes.alliance_winlose_amount) AS alliance_winlose_amount"
		selectedFields1 += ", SUM(alliance_incomes.alliance_commission) AS alliance_commission, SUM(alliance_incomes.total_paid_bonus) AS total_paid_bonus"
		selectedFields1 += ", SUM(alliance_incomes.alliance_paid_bonus) AS alliance_paid_bonus, SUM(alliance_incomes.alliance_income) AS alliance_income"
		selectedFields1 += ", SUM(CASE WHEN alliance_incomes.take_at IS NULL THEN alliance_incomes.alliance_income ELSE 0 END) AS alliance_pending_income"
		query1 := r.db.Table("alliance_winlose_income as alliance_incomes").
			Select(selectedFields1).
			Where("alliance_incomes.user_id IN ?", userIds).
			Group("alliance_incomes.user_id")

		if dateType.DateFrom != "" {
			// of_cut_date [20231227] เปลี่ยนไปดึงจากวันที่เล่นแทน (from layer check)
			query1 = query1.Where("alliance_incomes.statement_date >= ? ", dateType.DateFrom)
		}
		if dateType.DateTo != "" {
			// of_cut_date [20231227] เปลี่ยนไปดึงจากวันที่เล่นแทน (from layer check)
			query1 = query1.Where("alliance_incomes.statement_date <=  ?", dateType.DateTo)
		}
		if err := query1.Scan(&userPlayLog).Error; err != nil {
			return nil, 0, err
		}

		// MERGE DATA BY USER_ID
		for index, item := range list {
			for _, v2 := range userPlayLog {
				if item.UserId == v2.UserId {
					list[index].TotalPlayAmount = v2.TotalPlayAmount
					list[index].TotalWinLoseAmount = v2.TotalWinLoseAmount
					list[index].TotalCommission = v2.TotalCommission
					list[index].AllianceWinloseAmount = v2.AllianceWinloseAmount
					list[index].AllianceCommission = v2.AllianceCommission
					list[index].TotalPaidBonus = v2.TotalPaidBonus
					list[index].AlliancePaidBonus = v2.AlliancePaidBonus
					list[index].AllianceIncome = v2.AllianceIncome
					list[index].AlliancePendingIncome = v2.AlliancePendingIncome
					break
				}
			}
		}
	}
	return list, total, nil
}

func (r repo) GetAllianceWinLoseSummary(req model.AllianceWinLoseHistoryListRequest) (*model.AllianceTotalWinLoseHistoryResponse, error) {

	var record model.AllianceTotalWinLoseHistoryResponse

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return nil, err
	}

	// เอามาจาก alliance_winlose_income
	selectedFields1 := "user.ref_by, SUM(alliance_incomes.total_play_amount) AS total_play_amount, SUM(alliance_incomes.total_win_lose_amount) AS total_win_lose_amount"
	selectedFields1 += ", SUM(alliance_incomes.total_commission) AS total_commission, SUM(alliance_incomes.alliance_winlose_amount) AS alliance_winlose_amount"
	selectedFields1 += ", SUM(alliance_incomes.alliance_commission) AS alliance_commission, SUM(alliance_incomes.total_paid_bonus) AS total_paid_bonus"
	selectedFields1 += ", SUM(alliance_incomes.alliance_paid_bonus) AS alliance_paid_bonus, SUM(alliance_incomes.alliance_income) AS alliance_income"
	selectedFields1 += ", SUM(CASE WHEN alliance_incomes.take_at IS NULL THEN alliance_incomes.alliance_income ELSE 0 END) AS alliance_pending_income"
	query := r.db.Table("alliance_winlose_income as alliance_incomes")
	query = query.Select(selectedFields1)
	query = query.Joins("INNER JOIN user ON user.id = alliance_incomes.user_id")
	query = query.Where("user.ref_by = ?", req.RefUserId)

	if dateType.DateFrom != "" {
		// of_cut_date [20231227] เปลี่ยนไปดึงจากวันที่เล่นแทน (from layer check)
		query = query.Where("alliance_incomes.statement_date >= ? ", dateType.DateFrom)
	}
	if dateType.DateTo != "" {
		// of_cut_date [20231227] เปลี่ยนไปดึงจากวันที่เล่นแทน (from layer check)
		query = query.Where("alliance_incomes.statement_date <=  ?", dateType.DateTo)
	}
	query = query.Group("user.ref_by")
	if err := query.Take(&record).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			record.RefUserId = req.RefUserId
			record.FromDate = dateType.DateFrom
			record.ToDate = dateType.DateTo
			return &record, nil
		}
		return nil, err
	}

	// set info
	record.RefUserId = req.RefUserId
	record.FromDate = dateType.DateFrom
	record.ToDate = dateType.DateTo

	return &record, nil
}

func (r repo) GetAllianceBankTransaction(req model.AllianceBankTransactionListRequest) ([]model.AllianceBankTransactionResponse, int64, error) {

	var list []model.AllianceBankTransactionResponse
	var total int64

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return nil, 0, err
	}

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("user as users")
	count = count.Select("users.id")
	count = count.Where("users.ref_by = ?", req.RefUserId)
	if err := count.
		Where("users.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "users.ref_by AS ref_user_id, users.id AS user_id, users.member_code AS member_code"
		query := r.db.Table("user as users")
		query = query.Select(selectedFields)
		query = query.Where("users.ref_by = ?", req.RefUserId)
		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Where("users.deleted_at IS NULL").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, 0, err
		}

		// APPEND TOTAL DATA *DOWNLINE ONLY*
		userIds := []int64{}
		for _, v := range list {
			userIds = append(userIds, v.UserId)
		}

		// เอามาจาก รายการฝากถอน และ โบนัส
		// 5 DEPOSIT_CREDIT_APPROVED_GAME ฝากสำเร็จ TRANS_STATUS_DEPOSIT_CREDIT_APPROVED
		// Bonus TRANSACTION_TYPE_BONUS int64 = 3 สำเร็จ TRANS_STATUS_DEPOSIT_CREDIT_APPROVED = 5
		// 12 WITHDRAW_SUCCESS ถอนสำเร็จ TRANS_STATUS_WITHDRAW_SUCCESS
		// ยอดสุทธิ = ยอดฝาก - ยอดถอน - โบนัส
		var userDepositTransaction []struct {
			UserId             int64   `json:"user_id"`
			TotalDepositAmount float64 `json:"total_deposit_amount"`
			TotalBonusAmount   float64 `json:"total_bonus_amount"`
		}
		selectedFields1 := "logs.user_id, SUM(logs.credit_amount) AS total_deposit_amount, SUM(logs.bonus_amount) AS total_bonus_amount"
		query1 := r.db.Table("bank_transaction as logs").
			Select(selectedFields1).
			Where("logs.transaction_type_id IN ?", []int64{model.TRANSACTION_TYPE_DEPOSIT, model.TRANSACTION_TYPE_BONUS}).
			Where("logs.transaction_status_id = ?", model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED).
			Where("logs.user_id IN ?", userIds).
			Group("logs.user_id")

		if dateType.DateFrom != "" {
			startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
			if err != nil {
				return nil, 0, err
			}
			query1 = query1.Where("logs.created_at >= ? ", startDateAtBkk)
		}
		if dateType.DateTo != "" {
			endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
			if err != nil {
				return nil, 0, err
			}
			query1 = query1.Where("logs.created_at <=  ?", endDateAtBkk)
		}
		if err := query1.Scan(&userDepositTransaction).Error; err != nil {
			return nil, 0, err
		}

		var userWithdrawTransaction []struct {
			UserId              int64   `json:"user_id"`
			TotalWithdrawAmount float64 `json:"total_withdraw_amount"`
		}
		selectedFields2 := "logs.user_id, SUM(logs.credit_amount) AS total_withdraw_amount"
		query2 := r.db.Table("bank_transaction as logs").
			Select(selectedFields2).
			Where("logs.transaction_type_id = ?", model.TRANSACTION_TYPE_WITHDRAW).
			Where("logs.transaction_status_id = ?", model.TRANS_STATUS_WITHDRAW_SUCCESS).
			Where("logs.user_id IN ?", userIds).
			Group("logs.user_id")

		if dateType.DateFrom != "" {
			startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
			if err != nil {
				return nil, 0, err
			}
			query2 = query2.Where("logs.created_at >= ? ", startDateAtBkk)
		}
		if dateType.DateTo != "" {
			endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
			if err != nil {
				return nil, 0, err
			}
			query2 = query2.Where("logs.created_at <=  ?", endDateAtBkk)
		}
		if err := query2.Scan(&userWithdrawTransaction).Error; err != nil {
			return nil, 0, err
		}

		// MERGE DATA BY USER_ID
		for index, item := range list {
			for _, v2 := range userDepositTransaction {
				if item.UserId == v2.UserId {
					list[index].TotalDepositAmount = v2.TotalDepositAmount
					list[index].TotalBonusAmount = v2.TotalBonusAmount
					break
				}
			}
			for _, v2 := range userWithdrawTransaction {
				if item.UserId == v2.UserId {
					list[index].TotalWithdrawAmount = v2.TotalWithdrawAmount
					break
				}
			}
			// [********] ยอดสุทธิ = ยอดฝาก - ยอดถอน - โบนัส
			list[index].TotalAmount = list[index].TotalDepositAmount - list[index].TotalWithdrawAmount - list[index].TotalBonusAmount
		}
	}
	return list, total, nil
}

func (r repo) GetAllianceBankTransactionSummary(req model.AllianceBankTransactionListRequest) (*model.AllianceBankTransactionResponse, error) {

	var record model.AllianceBankTransactionResponse

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return nil, err
	}

	// เอามาจาก รายการฝากถอน และ โบนัส
	// 5 DEPOSIT_CREDIT_APPROVED_GAME ฝากสำเร็จ TRANS_STATUS_DEPOSIT_CREDIT_APPROVED
	// Bonus TRANSACTION_TYPE_BONUS int64 = 3 สำเร็จ TRANS_STATUS_DEPOSIT_CREDIT_APPROVED = 5
	// 12 WITHDRAW_SUCCESS ถอนสำเร็จ TRANS_STATUS_WITHDRAW_SUCCESS
	// ยอดสุทธิ = ยอดฝาก - ยอดถอน - โบนัส
	var userDepositTransaction struct {
		TotalDepositAmount float64 `json:"total_deposit_amount"`
		TotalBonusAmount   float64 `json:"total_bonus_amount"`
	}
	selectedFields1 := "SUM(logs.credit_amount) AS total_deposit_amount, SUM(logs.bonus_amount) AS total_bonus_amount"
	query1 := r.db.Table("bank_transaction as logs").
		Select(selectedFields1).
		Joins("INNER JOIN user as tb_user ON tb_user.id = logs.user_id").
		Where("logs.transaction_type_id IN ?", []int64{model.TRANSACTION_TYPE_DEPOSIT, model.TRANSACTION_TYPE_BONUS}).
		Where("logs.transaction_status_id = ?", model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED).
		Where("tb_user.ref_by = ?", req.RefUserId)

	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return nil, err
		}
		query1 = query1.Where("logs.created_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return nil, err
		}
		query1 = query1.Where("logs.created_at <=  ?", endDateAtBkk)
	}
	if err := query1.Scan(&userDepositTransaction).Error; err != nil {
		return nil, err
	}

	var userWithdrawTransaction struct {
		TotalWithdrawAmount float64 `json:"total_withdraw_amount"`
	}
	selectedFields2 := "SUM(logs.credit_amount) AS total_withdraw_amount"
	query2 := r.db.Table("bank_transaction as logs").
		Select(selectedFields2).
		Joins("INNER JOIN user as tb_user ON tb_user.id = logs.user_id").
		Where("logs.transaction_type_id = ?", model.TRANSACTION_TYPE_WITHDRAW).
		Where("logs.transaction_status_id = ?", model.TRANS_STATUS_WITHDRAW_SUCCESS).
		Where("tb_user.ref_by = ?", req.RefUserId)

	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return nil, err
		}
		query2 = query2.Where("logs.created_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return nil, err
		}
		query2 = query2.Where("logs.created_at <=  ?", endDateAtBkk)
	}
	if err := query2.Scan(&userWithdrawTransaction).Error; err != nil {
		return nil, err
	}

	// Merge all 3 query into one summary record
	record.TotalDepositAmount = userDepositTransaction.TotalDepositAmount
	record.TotalBonusAmount = userDepositTransaction.TotalBonusAmount
	record.TotalWithdrawAmount = userWithdrawTransaction.TotalWithdrawAmount
	record.TotalAmount = userDepositTransaction.TotalDepositAmount - userWithdrawTransaction.TotalWithdrawAmount - userDepositTransaction.TotalBonusAmount

	return &record, nil
}

func (r repo) CreateLinkClickLog(refBy int64) error {

	body := map[string]interface{}{
		"ref_by": refBy,
	}
	if err := r.db.Table("affiliate_link_click").Create(body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetAlliancePlayLogList(req model.AlliancePlayLogListRequest) ([]model.AlliancePlayLog, int64, error) {

	var list []model.AlliancePlayLog
	var total int64

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("play_log as play_logs")
	count = count.Select("play_logs.id")
	if req.UserId != nil {
		count = count.Where("play_logs.user_id = ?", req.UserId)
	}
	if req.FromDate != "" {
		count = count.Where("play_logs.date >= ? ", req.FromDate)
	}
	if req.ToDate != "" {
		count = count.Where("play_logs.date <=  ?", req.ToDate)
	}
	if err := count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "play_logs.id AS id, play_logs.player AS player, play_logs.turn_sport AS turn_sport, play_logs.turn_casino AS turn_casino"
		selectedFields += ", play_logs.turn_game AS turn_game, play_logs.win_lose_sport AS win_lose_sport, play_logs.win_lose_casino AS win_lose_casino"
		selectedFields += ", play_logs.win_lose_game AS win_lose_game, play_logs.valid_amount_sport AS valid_amount_sport"
		selectedFields += ", play_logs.valid_amount_casino AS valid_amount_casino, play_logs.valid_amount_game AS valid_amount_game"
		selectedFields += ", play_logs.turn_total AS turn_total, play_logs.win_lose_total AS win_lose_total, play_logs.valid_amount_total AS valid_amount_total"
		selectedFields += ", play_logs.user_id AS user_id, alliance_details.alliance_percent AS alliance_percent, alliance_details.commission_percent AS commission_percent"
		selectedFields += ", users.ref_by AS ref_user_id"
		selectedFields += ", play_logs.date AS date, play_logs.created_at AS created_at"
		query := r.db.Table("play_log as play_logs")
		query = query.Joins("INNER JOIN user as users ON users.id = play_logs.user_id")
		query = query.Joins("INNER JOIN user_alliance as alliance_details ON alliance_details.user_id = users.ref_by")
		query = query.Select(selectedFields)
		if req.UserId != nil {
			query = query.Where("play_logs.user_id = ?", req.UserId)
		}
		if req.FromDate != "" {
			query = query.Where("play_logs.date >= ? ", req.FromDate)
		}
		if req.ToDate != "" {
			query = query.Where("play_logs.date <=  ?", req.ToDate)
		}
		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}

	return list, total, nil
}

func (r repo) GetAllianceSumUserPlayLogList(req model.AlliancePlayLogListRequest) ([]model.AllianceUserSumPlayLog, int64, error) {

	var list []model.AllianceUserSumPlayLog
	var total int64

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("play_log as play_logs")
	count = count.Select("play_logs.id")
	// LATER : count = count.Where("tb_user.ref_by != 0").Where("tb_user.deleted_at IS NULL").Where("tb_user.ref_by IS NOT NULL")
	if req.UserId != nil {
		count = count.Where("play_logs.user_id = ?", req.UserId)
	}
	if req.FromDate != "" {
		count = count.Where("play_logs.date >= ? ", req.FromDate)
	}
	if req.ToDate != "" {
		count = count.Where("play_logs.date <=  ?", req.ToDate)
	}
	if err := count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "play_logs.date AS date, SUM(play_logs.turn_sport) AS sum_turn_sport, SUM(play_logs.turn_casino) AS sum_turn_casino"
		selectedFields += ", SUM(play_logs.turn_game) AS sum_turn_game, SUM(play_logs.win_lose_sport) AS sum_win_lose_sport"
		selectedFields += ", SUM(play_logs.win_lose_casino) AS sum_win_lose_casino, SUM(play_logs.win_lose_game) AS sum_win_lose_game"
		selectedFields += ", SUM(play_logs.valid_amount_sport) AS sum_valid_amount_sport, SUM(play_logs.valid_amount_casino) AS sum_valid_amount_casino"
		selectedFields += ", SUM(play_logs.valid_amount_game) AS sum_valid_amount_game, SUM(play_logs.turn_total) AS sum_turn_total"
		selectedFields += ", SUM(play_logs.win_lose_total) AS sum_win_lose_total, SUM(play_logs.valid_amount_total) AS sum_valid_amount_total"
		selectedFields += ", play_logs.user_id AS user_id"
		query := r.db.Table("play_log as play_logs")
		query = query.Select(selectedFields)
		// LATER : count = count.Where("tb_user.ref_by != 0").Where("tb_user.deleted_at IS NULL").Where("tb_user.ref_by IS NOT NULL")
		if req.UserId != nil {
			query = query.Where("play_logs.user_id = ?", req.UserId)
		}
		if req.FromDate != "" {
			query = query.Where("play_logs.date >= ? ", req.FromDate)
		}
		if req.ToDate != "" {
			query = query.Where("play_logs.date <=  ?", req.ToDate)
		}
		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		query = query.Group("play_logs.date, play_logs.user_id")
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}

	// APPEND ALLIANCE_PERCENT AND COMMISSION_PERCENT
	// query = query.Joins("INNER JOIN user_alliance as alliance_details ON alliance_details.user_id = users.ref_by")
	userIds := []int64{}
	for _, v := range list {
		userIds = append(userIds, v.UserId)
	}

	if len(userIds) > 0 {
		var userInfo []struct {
			UserId            int64   `json:"userId"`
			MemberCode        string  `json:"memberCode"`
			RefUserId         int64   `json:"refUserId"`
			AlliancePercent   float64 `json:"alliancePercent"`
			CommissionPercent float64 `json:"commissionPercent"`
		}

		selectedFields1 := "tb_user.id AS user_id, tb_user.member_code AS member_code, tb_user.ref_by AS ref_user_id"
		selectedFields1 += ", tb_alliance_setting.alliance_percent AS alliance_percent, tb_alliance_setting.commission_percent AS commission_percent"
		query1 := r.db.Table("user as tb_user")
		query1 = query1.Select(selectedFields1)
		query1 = query1.Joins("INNER JOIN user_alliance as tb_alliance_setting ON tb_alliance_setting.user_id = tb_user.ref_by")
		query1 = query1.Where("tb_user.id IN ?", userIds)
		if err := query1.Scan(&userInfo).Error; err != nil {
			return nil, 0, err
		}

		// MERGE DATA BY USER_ID
		for index, item := range list {
			for _, v2 := range userInfo {
				if item.UserId == v2.UserId {
					list[index].RefUserId = v2.RefUserId
					list[index].AlliancePercent = v2.AlliancePercent
					list[index].CommissionPercent = v2.CommissionPercent
					break
				}
			}
		}
	}

	return list, total, nil
}

func (r repo) GetAllianceTotalBonusLogList(req model.AllianceBonusLogListRequest) ([]model.UserTransactionBonusResponse, int64, error) {

	var list []model.UserTransactionBonusResponse
	var total int64

	// เปลี่ยนไปดึงทุกคน ที่มี ref_by IS NOT NULL เพื่อที่จะสร้างรายได้ของคนนั้นในวันที่เค้าไม่ได้เล่น
	// if len(req.UserIds) == 0 {
	// 	return nil, total, errors.New("UserIds is required")
	// }

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("user_transaction as logs")
	count = count.Select("logs.id")
	// count = count.Where("logs.user_id IN ?", req.UserIds)
	count = count.Joins("INNER JOIN user as users ON users.id = logs.user_id")
	count = count.Where("users.ref_by IS NOT NULL")
	// ยอดจ่ายโบนัสทั้งหมด มาจากการแจกโบนัสทุกประเภท ปัจจุบันมี
	// -รายการฝากที่มีโบนัส
	// -บึนทึกแจกโบนัส
	// -โบนัสคืนยอดเสีย
	// -โบนัสรายได้แนะนำเพื่อน
	// -โบนัสรายได้พันธมิตร
	// SELECT types.name, SUM(bonus_amount)
	// FROM user_transaction AS tb_log
	// INNER JOIN user ON user.id = tb_log.user_id
	// INNER JOIN user_transaction_type AS types ON types.id = tb_log.type_id
	// WHERE user.ref_by = 16
	// AND tb_log.transfer_at >= '2024-08-01 00:00:00'
	// AND tb_log.transfer_at <= '2024-08-31 16:59:59'
	// GROUP BY types.name
	// "name"	"SUM(bonus_amount)"
	// "DEPOSIT"	"0.00"
	// "WITHDRAW"	"0.00"
	// "CREDIT_TPYE_LUCKY_WHEEL"	"1521.00"
	// "AFFILIATE_INCOME"	"756.21"
	// "TAKE_CREDIT_BACK"	"0.00"
	// "COUPON_CASH"	"2600.00"
	// "BONUS"	"70.81"
	// "CREDIT_TYPE_PROMOTION_WEB"	"2095.00"
	// const (
	// 	CREDIT_TYPE_DEPOSIT               = 1
	// 	CREDIT_TYPE_WITHDRAW              = 2
	// 	CREDIT_TYPE_BONUS                 = 3
	// 	CREDIT_TYPE_PROMOTION_RETURN_LOSS = 4
	// 	CREDIT_TYPE_AFFILIATE_INCOME      = 5
	// 	CREDIT_TYPE_ALLIANCE_INCOME       = 6
	// 	CREDIT_TYPE_TAKE_CREDIT_BACK      = 7
	// 	CREDIT_TYPE_DAILY_ACTIVITY_BONUS  = 8
	// 	CREDIT_TPYE_LUCKY_WHEEL           = 9
	// 	CREDIT_TYPE_PROMOTION_WEB         = 10
	// 	CREDIT_TYPE_COUPON_CASH           = 11
	// 	CREDIT_TYPE_LOTTERY               = 12
	bonusTypeList := []int64{
		model.CREDIT_TYPE_DEPOSIT,
		model.CREDIT_TYPE_BONUS,
		model.CREDIT_TYPE_PROMOTION_RETURN_LOSS,
		model.CREDIT_TYPE_AFFILIATE_INCOME,
		model.CREDIT_TYPE_ALLIANCE_INCOME,
		model.CREDIT_TYPE_DAILY_ACTIVITY_BONUS,
		model.CREDIT_TPYE_LUCKY_WHEEL,
		model.CREDIT_TYPE_PROMOTION_WEB,
		model.CREDIT_TYPE_COUPON_CASH,
		model.CREDIT_TYPE_PROMOTION_RETURN_TURN,
	}
	// ยอดจ่ายโบนัสทั้งหมด มาจากการแจกโบนัสทุกประเภท อัพเดท
	// -รายการฝากที่มีโบนัส
	// -บึนทึกแจกโบนัส
	// -โบนัสคืนยอดเสีย
	// -โบนัสรายได้แนะนำเพื่อน
	// -โบนัสรายได้พันธมิตร
	// -โบนัสกิจกรรมประจำวัน
	// -โบนัสกงล้อ
	// -โบนัสเว็บโปรโมชั่น
	// -โบนัสคูปองเงินสด
	// [20240916] - โบนัสคืนยอดเทิร์น
	count = count.Where("logs.type_id IN (?)", bonusTypeList)
	if req.StatementDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.StatementDate)
		if err != nil {
			return nil, total, err
		}
		count = count.Where("logs.transfer_at >= ? ", startDateAtBkk)
		endDateAtBkk, err := r.ParseEodBkk(req.StatementDate)
		if err != nil {
			return nil, total, err
		}
		count = count.Where("logs.transfer_at <=  ?", endDateAtBkk)
	} else {
		return nil, total, errors.New("StatementDate is required")
	}
	if err := count.
		Where("logs.removed_at IS NULL").
		Where("logs.is_show = ?", true).
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		// SELECT DATE_FORMAT(CONVERT_TZ(tb_log.transfer_at, '+00:00', '+07:00'), '%Y-%m-%d') AS statement_date,  SUM(bonus_amount)
		// FROM user_transaction AS tb_log
		// INNER JOIN user ON user.id = tb_log.user_id
		// WHERE user.ref_by = 16
		// AND tb_log.transfer_at >= '2024-07-31 17:00:00'
		// AND tb_log.transfer_at <= '2024-08-31 16:59:59'
		// GROUP BY statement_date

		// SELECT types.name, SUM(bonus_amount)
		// FROM user_transaction AS tb_log
		// INNER JOIN user ON user.id = tb_log.user_id
		// INNER JOIN user_transaction_type AS types ON types.id = tb_log.type_id
		// WHERE user.ref_by = 16
		// AND tb_log.transfer_at >= '2024-07-31 17:00:00'
		// AND tb_log.transfer_at <= '2024-08-31 16:59:59'
		// GROUP BY types.name

		// SELECT statement_date, SUM(total_paid_bonus)
		// FROM alliance_winlose_income AS tb_log
		// INNER JOIN user ON user.id = tb_log.user_id
		// WHERE  user.ref_by = 16
		// AND tb_log.statement_date >= '2024-08-01'
		// AND tb_log.statement_date <= '2024-08-30'
		// GROuP BY statement_date

		// SELECT tb_user.ref_by as ref_by, SUM(CASE WHEN income_logs.type_id = 5 THEN income_logs.bonus_amount ELSE 0 END) AS affiliate_member_total,
		// SUM(CASE WHEN income_logs.type_id = 4 THEN income_logs.bonus_amount ELSE 0 END) AS promotion_return_loss_total,
		// SUM(CASE WHEN income_logs.type_id = 9 THEN income_logs.bonus_amount ELSE 0 END) AS lucky_wheel_total,
		// SUM(CASE WHEN income_logs.type_id = 11 THEN income_logs.bonus_amount ELSE 0 END) AS coupon_total, SUM(CASE WHEN income_logs.type_id = 3 THEN income_logs.bonus_amount ELSE 0 END) AS credit_bonus_total,
		// SUM(CASE WHEN income_logs.type_id = 10 THEN income_logs.bonus_amount ELSE 0 END) AS promotion_web_total, SUM(CASE WHEN income_logs.type_id = 8 THEN income_logs.bonus_amount ELSE 0 END) AS daily_activity_bonus_total
		// FROM user_transaction as income_logs INNER JOIN user as tb_user ON tb_user.id = income_logs.user_id WHERE tb_user.ref_by IN (16) AND income_logs.transfer_at >= '2024-07-31 17:00:00'  AND income_logs.transfer_at <=  '2024-08-31 17:00:00' GROUP BY `tb_user`.`ref_by`

		selectedFields := "logs.id, logs.direction_id, directions.name as direction_name, logs.account_id, logs.detail, logs.promotion_id"
		selectedFields += ", logs.user_id, users.member_code as user_member_code, users.username, users.fullname as user_fullname"
		selectedFields += ", logs.type_id, types.detail as type_name"
		selectedFields += ", logs.credit_before, logs.credit_back, logs.credit_amount, logs.bonus_amount, logs.credit_after, logs.transfer_at"
		selectedFields += ", logs.create_admin_id, create_admin.username as create_admin_username, logs.confirm_admin_id, confirm_admin.username as confirm_admin_username"
		selectedFields += ", logs.is_adjust_auto, logs.work_seconds, logs.created_at"
		query := r.db.Table("user_transaction as logs")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN user as users ON users.id = logs.user_id")
		query = query.Joins("LEFT JOIN user_transaction_type as types ON types.id = logs.type_id")
		query = query.Joins("LEFT JOIN user_transaction_direction as directions ON directions.id = logs.direction_id")
		query = query.Joins("LEFT JOIN admin as create_admin ON create_admin.id = logs.create_admin_id")
		query = query.Joins("LEFT JOIN admin as confirm_admin ON confirm_admin.id = logs.confirm_admin_id")
		query = query.Where("users.ref_by IS NOT NULL")
		// query = query.Where("logs.user_id IN ?", req.UserIds)
		query = query.Where("logs.type_id IN (?)", bonusTypeList)
		if req.StatementDate != "" {
			// OfDate is Primary
			startDateAtBkk, err := r.ParseBodBkk(req.StatementDate)
			if err != nil {
				return nil, total, err
			}
			query = query.Where("logs.transfer_at >= ? ", startDateAtBkk)
			endDateAtBkk, err := r.ParseEodBkk(req.StatementDate)
			if err != nil {
				return nil, total, err
			}
			query = query.Where("logs.transfer_at <=  ?", endDateAtBkk)
		} else {
			return nil, total, errors.New("StatementDate is required")
		}

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Where("logs.removed_at IS NULL").
			Where("logs.is_show = ?", true).
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}

		// APPEND ALLIANCE_PERCENT AND COMMISSION_PERCENT
		// query = query.Joins("INNER JOIN user_alliance as alliance_details ON alliance_details.user_id = users.ref_by")
		userIds := []int64{}
		for _, v := range list {
			userIds = append(userIds, v.UserId)
		}

		if len(userIds) > 0 {
			var userInfo []struct {
				UserId            int64   `json:"userId"`
				MemberCode        string  `json:"memberCode"`
				RefUserId         int64   `json:"refUserId"`
				AlliancePercent   float64 `json:"alliancePercent"`
				CommissionPercent float64 `json:"commissionPercent"`
			}

			selectedFields1 := "tb_user.id AS user_id, tb_user.member_code AS member_code, tb_user.ref_by AS ref_user_id"
			selectedFields1 += ", tb_alliance_setting.alliance_percent AS alliance_percent, tb_alliance_setting.commission_percent AS commission_percent"
			query1 := r.db.Table("user as tb_user")
			query1 = query1.Select(selectedFields1)
			query1 = query1.Joins("INNER JOIN user_alliance as tb_alliance_setting ON tb_alliance_setting.user_id = tb_user.ref_by")
			query1 = query1.Where("tb_user.id IN ?", userIds)
			if err := query1.Scan(&userInfo).Error; err != nil {
				return nil, 0, err
			}

			// MERGE DATA BY USER_ID
			for index, item := range list {
				for _, v2 := range userInfo {
					if item.UserId == v2.UserId {
						list[index].RefUserId = v2.RefUserId
						list[index].AlliancePercent = v2.AlliancePercent
						list[index].CommissionPercent = v2.CommissionPercent
						break
					}
				}
			}
		}
	}

	return list, total, nil
}

func (r repo) CreateAllianceWinloseIncome(body model.AllianceWinloseIncomeCreateBody) (*int64, error) {

	if err := r.db.Table("alliance_winlose_income").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) GetAllianceWinloseIncomeListByDailyKeyList(bulkBody map[string]model.AllianceWinloseIncomeCreateBody) ([]model.AllianceWinloseIncomeDailyKey, int64, error) {

	var list []model.AllianceWinloseIncomeDailyKey
	var total int64

	dailyKeys := make([]string, 0)
	if len(bulkBody) > 0 {
		for k := range bulkBody {
			dailyKeys = append(dailyKeys, k)
		}
	}

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("alliance_winlose_income as logs")
	count = count.Select("logs.id")
	count = count.Where("logs.daily_key IN ?", dailyKeys)
	if err := count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "id, user_id, daily_key"
		query := r.db.Table("alliance_winlose_income as logs")
		query = query.Select(selectedFields)
		query = query.Where("logs.daily_key IN ?", dailyKeys)
		if err := query.
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetUserAllianceSettingByUserId(userId int64) (*model.AllianceCommissionSettingUser, error) {

	var data model.AllianceCommissionSettingUser

	if err := r.db.Table("user_alliance").
		Select("alliance_percent").
		Where("user_id = ?", userId).
		Take(&data).
		Error; err != nil {
		return nil, err
	}
	return &data, nil
}

func (r repo) GetAllianceWinloseIncomeById(id int64) (*model.AllianceWinloseIncome, error) {

	var record model.AllianceWinloseIncome

	selectedFields := "id, user_id, status_id, daily_key, statement_date, of_date, total_play_amount, total_win_lose_amount"
	selectedFields += ", commission_percent, alliance_percent, total_commission, alliance_winlose_amount, alliance_commission"
	selectedFields += ", total_paid_bonus, alliance_paid_bonus, alliance_income, take_at, taken_price, created_at, updated_at"
	if err := r.db.Table("alliance_winlose_income AS tb_log").
		Select(selectedFields).
		Where("id = ?", id).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetAllianceWinloseIncomeByDailyKey(dailyKey string) (*model.AllianceWinloseIncome, error) {

	var record model.AllianceWinloseIncome

	selectedFields := "id, user_id, status_id, daily_key, statement_date, of_date, total_play_amount, total_win_lose_amount"
	selectedFields += ", commission_percent, alliance_percent, total_commission, alliance_winlose_amount, alliance_commission"
	selectedFields += ", total_paid_bonus, alliance_paid_bonus, alliance_income, take_at, taken_price, created_at, updated_at"
	if err := r.db.Table("alliance_winlose_income AS tb_log").
		Select(selectedFields).
		Where("daily_key = ?", dailyKey).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) CreateAllianceWinloseIncomeBulk(bulkMap map[string]model.AllianceWinloseIncomeCreateBody) error {

	bulkBody := make([]model.AllianceWinloseIncomeCreateBody, 0)
	for _, v := range bulkMap {
		bulkBody = append(bulkBody, v)
	}

	if err := r.db.Table("alliance_winlose_income").Create(&bulkBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdatePendingAllianceWinloseIncome(id int64, body model.AllianceWinlosePendingIncomeUpdateBody) error {

	if err := r.db.Table("alliance_winlose_income").Where("id = ?", id).Where("take_at IS NULL").Updates(body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) TestUpdateAllianceWinloseIncome(id int64, amount float64) error {

	updateBody := map[string]interface{}{
		"take_at":         nil,
		"taken_price":     0,
		"alliance_income": amount,
	}

	if err := r.db.Table("alliance_winlose_income").Where("id = ?", id).Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetAliasByRef(userId int64) (*model.GetAliasByUserIdResponse, error) {

	var record model.GetAliasByUserIdResponse
	query := r.db.Table("user_alliance as tb_alliance")
	query = query.Select("tb_alliance.alias")
	query = query.Joins("INNER JOIN user as tb_user ON tb_user.id = tb_alliance.user_id")
	query = query.Where("tb_alliance.user_id = ?", userId).Where("tb_user.user_type_id = ?", model.USER_TYPE_ALLIANCE)

	if err := query.Take(&record).Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetAllianceWinLoseList(req model.AllianceWinLoseTotalListRequest) ([]model.AllianceWinLoseTotalResponse, int64, error) {

	var list []model.AllianceWinLoseTotalResponse
	var total int64

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return nil, 0, err
	}

	// 1.Get Member and Setting
	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("user as tb_user")
	count = count.Select("tb_user.id")
	count = count.Where("tb_user.user_type_id = ?", model.USER_TYPE_ALLIANCE)
	if err := count.
		Where("tb_user.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_user.id AS user_id, tb_user.member_code AS member_code, tb_user.fullname AS user_fullname"
		query := r.db.Table("user as tb_user")
		query = query.Select(selectedFields)
		query = query.Where("tb_user.user_type_id = ?", model.USER_TYPE_ALLIANCE)
		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Where("tb_user.deleted_at IS NULL").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, 0, err
		}

		// APPEND TOTAL DATA *DOWNLINE ONLY*
		referorIds := []int64{}
		for _, v := range list {
			referorIds = append(referorIds, v.UserId)
		}

		// fmt.Println("referorIds", referorIds)

		// เอามาจาก alliance_winlose_income
		var userPlayLog []struct {
			RefBy                 int64   `json:"refBy"`
			TotalPlayAmount       float64 `json:"totalPlayAmount"`
			TotalWinLoseAmount    float64 `json:"totalWinLoseAmount"`
			TotalCommission       float64 `json:"totalCommission"`
			AllianceWinloseAmount float64 `json:"allianceWinloseAmount"`
			AllianceCommission    float64 `json:"allianceCommission"`
			TotalPaidBonus        float64 `json:"totalPaidBonus"`
			AlliancePaidBonus     float64 `json:"alliancePaidBonus"`
			AllianceIncome        float64 `json:"allianceIncome"`
			AlliancePendingIncome float64 `json:"alliancePendingIncome"`
		}

		selectedFields1 := "tb_user.ref_by as ref_by, SUM(alliance_incomes.total_play_amount) AS total_play_amount, SUM(alliance_incomes.total_win_lose_amount) AS total_win_lose_amount"
		selectedFields1 += ", SUM(alliance_incomes.total_commission) AS total_commission, SUM(alliance_incomes.alliance_winlose_amount) AS alliance_winlose_amount"
		selectedFields1 += ", SUM(alliance_incomes.alliance_commission) AS alliance_commission, SUM(alliance_incomes.total_paid_bonus) AS total_paid_bonus"
		selectedFields1 += ", SUM(alliance_incomes.alliance_paid_bonus) AS alliance_paid_bonus, SUM(alliance_incomes.alliance_income) AS alliance_income"

		query1 := r.db.Table("alliance_winlose_income as alliance_incomes").
			Select(selectedFields1).
			Joins("INNER JOIN user as tb_user ON tb_user.id = alliance_incomes.user_id").
			Where("tb_user.ref_by IN ?", referorIds).
			Group("tb_user.ref_by")

		if dateType.DateFrom != "" {
			// of_cut_date [20231227] เปลี่ยนไปดึงจากวันที่เล่นแทน (from layer check)
			query1 = query1.Where("alliance_incomes.statement_date >= ? ", dateType.DateFrom)
		}
		if dateType.DateTo != "" {
			// of_cut_date [20231227] เปลี่ยนไปดึงจากวันที่เล่นแทน (from layer check)
			query1 = query1.Where("alliance_incomes.statement_date <=  ?", dateType.DateTo)
		}
		if err := query1.Scan(&userPlayLog).Error; err != nil {
			return nil, 0, err
		}

		var userIncomeLog []struct {
			RefBy                    int64   `json:"refBy"`
			AffiliateMemberTotal     float64 `json:"affiliateMemberTotal"`     // โบนัสแนะนำเพื่อน
			PromotionReturnLossTotal float64 `json:"promotionReturnLossTotal"` // โบนัสคืนยอดเสีย
			LuckyWheelTotal          float64 `json:"luckyWheelTotal"`          // กงล้อ
			CouponTotal              float64 `json:"couponTotal"`              // คูปอง
			CreditBonusTotal         float64 `json:"creditBonusTotal"`         // โบนัสเครดิต
			PromotionWebTotal        float64 `json:"promotionWebTotal"`        // โบนัสเว็บ
			PromotionReturnTurnTotal float64 `json:"promotionReturnTurnTotal"` // โบนัสคืนยอดเทิร์น
			DailyActivityBonusTotal  float64 `json:"dailyActivityBonusTotal"`
		}

		// user_income_log  use user_id join user_income_log confirm P.Tula Discord 20240715
		// user_income_log เปลี่ยนไปใช้ user_transaction แทน เพราะไม่มีข้อมูล คูปอง กับ โบนัส และไม่อยากให้ ไป สร้าง ในรายการ user_income_log  (เพราะมันจะไปแสดง หน้ากิจกรรมโบนัส) 20240723

		selectedFields2 := "tb_user.ref_by as ref_by"
		selectedFields2 += ", SUM(CASE WHEN income_logs.type_id = 5 THEN income_logs.bonus_amount ELSE 0 END) AS affiliate_member_total"
		selectedFields2 += ", SUM(CASE WHEN income_logs.type_id = 4 THEN income_logs.bonus_amount ELSE 0 END) AS promotion_return_loss_total"
		selectedFields2 += ", SUM(CASE WHEN income_logs.type_id = 9 THEN income_logs.bonus_amount ELSE 0 END) AS lucky_wheel_total"
		selectedFields2 += ", SUM(CASE WHEN income_logs.type_id = 11 THEN income_logs.bonus_amount ELSE 0 END) AS coupon_total"
		selectedFields2 += ", SUM(CASE WHEN income_logs.type_id = 3 THEN income_logs.bonus_amount ELSE 0 END) AS credit_bonus_total"
		selectedFields2 += ", SUM(CASE WHEN income_logs.type_id = 10 THEN income_logs.bonus_amount ELSE 0 END) AS promotion_web_total"
		selectedFields2 += ", SUM(CASE WHEN income_logs.type_id = 13 THEN income_logs.bonus_amount ELSE 0 END) AS promotion_return_turn_total"
		selectedFields2 += ", SUM(CASE WHEN income_logs.type_id = 8 THEN income_logs.bonus_amount ELSE 0 END) AS daily_activity_bonus_total"

		query2 := r.db.Table("user_transaction as income_logs").
			Select(selectedFields2).
			Joins("INNER JOIN user as tb_user ON tb_user.id = income_logs.user_id").
			Where("tb_user.ref_by IN ?", referorIds).
			Group("tb_user.ref_by")

		if dateType.DateFrom != "" {
			startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
			if err != nil {
				return nil, total, err
			}
			query2 = query2.Where("income_logs.transfer_at >= ? ", startDateAtBkk)
		}
		if dateType.DateTo != "" {
			endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
			if err != nil {
				return nil, total, err
			}
			query2 = query2.Where("income_logs.transfer_at <=  ?", endDateAtBkk)
		}
		if err := query2.Scan(&userIncomeLog).Error; err != nil {
			return nil, 0, err
		}

		// MERGE DATA BY USER_ID
		for index, item := range list {
			for _, v2 := range userPlayLog {
				if item.UserId == v2.RefBy {
					list[index].TotalPlayAmount = v2.TotalPlayAmount
					list[index].TotalWinLoseAmount = v2.TotalWinLoseAmount
					list[index].TotalCommission = v2.TotalCommission
					list[index].AllianceWinloseAmount = v2.AllianceWinloseAmount
					list[index].AllianceCommission = v2.AllianceCommission
					list[index].TotalPaidBonus = v2.TotalPaidBonus
					list[index].AlliancePaidBonus = v2.AlliancePaidBonus
					list[index].AllianceIncome = v2.AllianceIncome
					list[index].FromDate = dateType.DateFrom
					list[index].ToDate = dateType.DateTo
					list[index].AlliancePendingIncome = v2.AlliancePendingIncome
					break
				}
			}
			for _, v3 := range userIncomeLog {
				if item.UserId == v3.RefBy {
					list[index].AffiliateMemberTotal = v3.AffiliateMemberTotal
					list[index].PromotionReturnLossTotal = v3.PromotionReturnLossTotal
					list[index].LuckyWheelTotal = v3.LuckyWheelTotal
					list[index].CouponTotal = v3.CouponTotal
					list[index].CreditBonusTotal = v3.CreditBonusTotal
					list[index].PromotionWebTotal = v3.PromotionWebTotal
					list[index].PromotionReturnTurnTotal = v3.PromotionReturnTurnTotal
					list[index].DailyActivityBonusTotal = v3.DailyActivityBonusTotal
					break
				}
			}
		}

	}
	return list, total, nil
}
func (r repo) GetSumAllianceWinLoseTotal(req model.AllianceWinLoseSumTotalRequest) (*model.GetSumAllianceWinLoseTotalResponse, error) {

	var userPlayLog *model.GetSumAllianceWinLoseTotalResponse
	var list []model.AllianceWinLoseTotalResponse

	// alliance_repository.go:3162 SLOW SQL >= 200ms [3212.954ms] [rows:1] แก้แล้ว INDEX transfer_at ก็เร็วเลย

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return nil, err
	}

	selectedFields := "tb_user.id AS user_id, tb_user.member_code AS member_code, tb_user.fullname AS user_fullname"
	query := r.db.Table("user as tb_user")
	query = query.Select(selectedFields)
	query = query.Where("tb_user.user_type_id = ?", model.USER_TYPE_ALLIANCE)
	// Sort by ANY //
	// req.SortCol = strings.TrimSpace(req.SortCol)
	// if req.SortCol != "" {
	// 	if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
	// 		req.SortAsc = "DESC"
	// 	} else {
	// 		req.SortAsc = "ASC"
	// 	}
	// 	query = query.Order(req.SortCol + " " + req.SortAsc)
	// }
	// if req.Limit > 0 {
	// 	query = query.Limit(req.Limit)
	// }
	if err := query.
		Where("tb_user.deleted_at IS NULL").
		// Offset(req.Page * req.Limit).
		Scan(&list).
		Error; err != nil {
		return nil, err
	}

	// APPEND TOTAL DATA *DOWNLINE ONLY*
	referorIds := []int64{}
	for _, v := range list {
		referorIds = append(referorIds, v.UserId)
	}

	// fmt.Println("referorIds", referorIds)

	selectedFields1 := " SUM(alliance_incomes.total_play_amount) AS total_play_amount, SUM(alliance_incomes.total_win_lose_amount) AS total_win_lose_amount"
	selectedFields1 += ", SUM(alliance_incomes.total_commission) AS total_commission, SUM(alliance_incomes.alliance_winlose_amount) AS alliance_winlose_amount"
	selectedFields1 += ", SUM(alliance_incomes.alliance_commission) AS alliance_commission, SUM(alliance_incomes.total_paid_bonus) AS total_paid_bonus"
	selectedFields1 += ", SUM(alliance_incomes.alliance_paid_bonus) AS alliance_paid_bonus, SUM(alliance_incomes.alliance_income) AS alliance_income"
	selectedFields1 += ", SUM(CASE WHEN alliance_incomes.take_at IS NULL THEN alliance_incomes.alliance_income ELSE 0 END) AS alliance_pending_income"

	query1 := r.db.Table("alliance_winlose_income as alliance_incomes").
		Joins("INNER JOIN user as tb_user ON tb_user.id = alliance_incomes.user_id").
		Where("tb_user.ref_by IN ?", referorIds).
		Select(selectedFields1)

	if dateType.DateFrom != "" {
		// of_cut_date [20231227] เปลี่ยนไปดึงจากวันที่เล่นแทน (from layer check)
		query1 = query1.Where("alliance_incomes.statement_date >= ? ", dateType.DateFrom)
	}
	if dateType.DateTo != "" {
		// of_cut_date [20231227] เปลี่ยนไปดึงจากวันที่เล่นแทน (from layer check)
		query1 = query1.Where("alliance_incomes.statement_date <=  ?", dateType.DateTo)
	}
	if err := query1.Scan(&userPlayLog).Error; err != nil {
		return nil, err
	}

	var userIncomeLog struct {
		RefBy                    int64   `json:"refBy"`
		AffiliateMemberTotal     float64 `json:"affiliateMemberTotal"`     // โบนัสแนะนำเพื่อน
		PromotionReturnLossTotal float64 `json:"promotionReturnLossTotal"` // โบนัสคืนยอดเสีย // มี
		LuckyWheelTotal          float64 `json:"luckyWheelTotal"`          // กงล้อ   // มี
		CouponTotal              float64 `json:"couponTotal"`              // คูปอง   // มี
		CreditBonusTotal         float64 `json:"creditBonusTotal"`         // โบนัสเครดิต  // มี
		PromotionWebTotal        float64 `json:"promotionWebTotal"`        // โปรโมชั่น   //มี
		PromotionReturnTurnTotal float64 `json:"promotionReturnTurnTotal"` // โบนัสคืนยอดเทิร์น
		DailyActivityBonusTotal  float64 `json:"dailyActivityBonusTotal"`  // โบนัสกิจกรรมรายวัน
	}

	// user_income_log  use user_id join user_income_log confirm P.Tula Discord 20240715
	selectedFields2 := "SUM(CASE WHEN income_logs.type_id = 5 THEN income_logs.bonus_amount ELSE 0 END) AS affiliate_member_total"
	selectedFields2 += ", SUM(CASE WHEN income_logs.type_id = 4 THEN income_logs.bonus_amount ELSE 0 END) AS promotion_return_loss_total"
	selectedFields2 += ", SUM(CASE WHEN income_logs.type_id = 9 THEN income_logs.bonus_amount ELSE 0 END) AS lucky_wheel_total"
	selectedFields2 += ", SUM(CASE WHEN income_logs.type_id = 11 THEN income_logs.bonus_amount ELSE 0 END) AS coupon_total"
	selectedFields2 += ", SUM(CASE WHEN income_logs.type_id = 3 THEN income_logs.bonus_amount ELSE 0 END) AS credit_bonus_total"
	selectedFields2 += ", SUM(CASE WHEN income_logs.type_id = 10 THEN income_logs.bonus_amount ELSE 0 END) AS promotion_web_total"
	selectedFields2 += ", SUM(CASE WHEN income_logs.type_id = 13 THEN income_logs.bonus_amount ELSE 0 END) AS promotion_return_turn_total"
	selectedFields2 += ", SUM(CASE WHEN income_logs.type_id = 8 THEN income_logs.bonus_amount ELSE 0 END) AS daily_activity_bonus_total"

	query2 := r.db.Table("user_transaction as income_logs").
		Select(selectedFields2).
		Joins("INNER JOIN user as tb_user ON tb_user.id = income_logs.user_id").
		Where("tb_user.ref_by IN ?", referorIds)

	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return nil, err
		}
		query2 = query2.Where("income_logs.transfer_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return nil, err
		}
		query2 = query2.Where("income_logs.transfer_at <=  ?", endDateAtBkk)
	}

	if err := query2.Scan(&userIncomeLog).Error; err != nil {
		return nil, err
	}

	if userPlayLog != nil {
		userPlayLog.AffiliateMemberTotal = userIncomeLog.AffiliateMemberTotal
		userPlayLog.PromotionReturnLossTotal = userIncomeLog.PromotionReturnLossTotal
		userPlayLog.LuckyWheelTotal = userIncomeLog.LuckyWheelTotal
		userPlayLog.CouponTotal = userIncomeLog.CouponTotal
		userPlayLog.CreditBonusTotal = userIncomeLog.CreditBonusTotal
		userPlayLog.PromotionWebTotal = userIncomeLog.PromotionWebTotal
		userPlayLog.PromotionReturnTurnTotal = userIncomeLog.PromotionReturnTurnTotal
		userPlayLog.DailyActivityBonusTotal = userIncomeLog.DailyActivityBonusTotal
	}

	return userPlayLog, nil
}

func (r repo) CountPendingAffUserIncomeLogList() (int64, error) {

	var total int64

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("affiliate_transaction as tb_log")
	count = count.Select("tb_log.id")
	count = count.Where("tb_log.status_id = ?", model.AFF_TRANSACTION_STATUS_PENDING)
	if err := count.Count(&total).Error; err != nil {
		return -1, err
	}
	return total, nil
}

func (r repo) GetPendingAffiliateIncomeList() ([]model.UserAffIncomeReponse, error) {

	var list []model.UserAffIncomeReponse

	// SELECT //
	selectedFields1 := "tb_user_aff.user_id AS user_id, tb_user_aff.commission_total AS commission_total, tb_user_aff.commission_current AS commission_current"
	selectedFields1 += ", tb_user_aff.first_deposit_bonus AS first_deposit_bonus, tb_user_aff.bonus_share_total AS bonus_share_total"
	selectedFields1 += ", tb_user_aff.commission_sport AS commission_sport, tb_user_aff.commission_casino AS commission_casino"
	selectedFields1 += ", tb_user_aff.commission_game AS commission_game, tb_user_aff.link_click_total AS link_click_total"
	selectedFields1 += ", tb_user_aff.member_total AS member_total, tb_user_aff.member_deposit_total AS member_deposit_total"
	selectedFields1 += ", tb_user_aff.play_balance AS play_balance, tb_user_aff.received_balance AS received_balance"

	query1 := r.db.Table("user_affiliate AS tb_user_aff")
	query1 = query1.Select(selectedFields1)
	query1 = query1.Joins("INNER JOIN user AS tb_user ON tb_user.id = tb_user_aff.user_id")
	query1 = query1.Where("tb_user.user_type_id = ?", model.USER_TYPE_AFFILIATE)
	query1 = query1.Where("tb_user_aff.commission_current > 0")
	if err := query1.Scan(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}
