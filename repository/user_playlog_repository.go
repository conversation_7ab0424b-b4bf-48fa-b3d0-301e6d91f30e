package repository

import (
	"bytes"
	"cybergame-api/helper"
	"cybergame-api/model"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"strings"
	"time"

	"gorm.io/gorm"
)

func NewUserPlaylogRepository(db *gorm.DB) UserPlaylogRepository {
	return &repo{db}
}

type UserPlaylogRepository interface {
	GetDb() *gorm.DB
	// AgentProvider - AGC
	AgcSimpleWinLose(data model.AgcSimpleWinlose) (*model.AgcSimpleWinloseResponse, error)
	LotterySimpleWinLose(data model.LotteryPlaylogRequest) (*model.LotteryPlaylogResponse, error)
	// AgentProvider - AMB
	AmbSimpleWinLose(data model.AmbSimpleWinlose) (*model.AmbSimpleWinloseResponse, error)
	// PLAYLOG
	GetUserPlaylogCompletedStatus(path string, statementDate string) (*model.UserPlaylogStatus, error)
	CreateUserPlaylogStatus(body model.UserPlaylogStatusCreateBody) error
	GetPlayLogKeyList(dailyKeyList []string) ([]string, int64, error)
	CreateUserPlaylogBulk(bodyList map[string]model.UserPlaylogCreateBody, memberList []string) error
	// GetUserPlayLogList(req model.UserPlaylogListRequest) ([]model.UserPlaylogResponse, int64, error)
	GetDownlineUserTotalPlayLogList(req model.UserPlaylogListRequest) ([]model.UserPlaylogTotalResponse, int64, error)
	GetUserAffiliateIncomeKeyList(dailyKeyList []string) ([]string, int64, error)
	CreateUserAffiliateIncomeBulk(bodyList map[string]model.UserAffiliateIncomeCreateBody) error
	CreateUserAffiliateTransactionBulk(bodyList map[string]model.UserAffiliateIncomeTotalResponse) error
	// TODAY PLAYLOG
	GetTodaySumUserPlayLogList(req model.UserTodayPlaylogListRequest) ([]model.UserTodaySumPlaylogReponse, int64, error)
	CreateUserTodayPlaylogBulkDirect(createList map[string]model.UserTodayPlaylogCreateBody) error
	CreateUserTodayPlaylogBulk(bodyList map[string]model.UserTodayPlaylogCreateBody, memberList []string) error
	GetTodayPlayLogKeyList(dailyKeyList []string) ([]model.UserTodayPlaylogResponse, int64, error)
	UpdateUserTodayPlaylog(updateBody model.UserTodayPlaylogUpdateBody) error
	// Webhook Log
	CreateWebhookLog(data model.WebhookLogCreateBody) (*int64, error)
	UpdateWebhookLog(id int64, data model.WebhookLogUpdateBody) error
	// Summary per User
	GetTotalPlaylogSummaryByUserId(userId int64) (*model.UserPlaylogSummary, error)
	GetUserPlaylogSummaryByUserId(userId int64) (*model.UserPlaylogSummaryResponse, error)
	GetUserPlaylogSummaryList(req model.UserPlaylogSummaryListRequest) ([]model.UserPlaylogSummaryResponse, int64, error)
	CreateUserPlaylogSummary(body model.UserPlaylogSummaryCreateBody) (*int64, error)
	UpdateUserPlaylogSummary(id int64, body model.UserPlaylogSummaryUpdateBody) error
	// REF-ACTION
	GetRaceActionByActionKey(actionKey string) (*model.RaceAction, error)
	CreateRaceCondition(body model.RaceActionCreateBody) (int64, error)
	UpdateRaceCondition(id int64, body model.RaceActionUpdateBody) error
	// REPORT SHOW DB
	GetAgcPlaylogStatus(statementDate string) (*model.ReportPlayLogStatusResponse, error)
	GetReportPlayLogStatusResponse(req model.ReportPlayLogStatusRequest) ([]model.ReportPlayLogStatusResponse, int64, error)
	GetReportPlayLogResponse(req model.ReportPlayLogResponseRequest) ([]model.ReportPlayLogResponse, int64, error)
	GetReportPlayLogSummary(req model.ReportPlayLogResponseRequest) (*model.ReportPlayLogSummaryResponse, error)
	// REF-AFF
	MakeReportAffiliateUserList(statementDate string) error
	// PG-HARD
	GetAgentPgHardCallback(req model.AgentPgHardCallbackSummaryRequest) ([]model.AgentPgHardCallbackSummary, error)
	// CTW
	GetAgentCtwCallback(req model.AgentCtwCallbackSummaryRequest) ([]model.AgentCtwCallbackSummary, error)

	// RAW PLAYLOG
	GetAgentCtwCallbackPlayLogList(req model.AgentCallbackPlayLogListRequest) ([]model.AgentCtwCallbackPlayLogListResponse, int64, error)
	GetAgentPgHardCallbackPlayLogList(req model.AgentCallbackPlayLogListRequest) ([]model.AgentPgHardCallbackPlayLogListResponse, int64, error)
	AgcWinloseReport(data model.AgcWinloseReportRequest) (*model.AgcWinloseReportResponse, error)

	// PG HARD PLAY LOG SUMMARY
	GetPgHardCallbackSummary(req model.PgHardCallbackSummaryRequest) ([]model.PgHardCallbackSummaryResponse, error)
	AgentPgHardCallbackSummaryReportList(req model.AgentPgHardCallbackSummaryReportRequest) ([]model.AgentPgHardCallbackSummaryReportResponse, error)
	CreateAgentPgHardCallbackSummaryReport(body []model.CreateAgentPgHardCallbackSummaryReport) error
	UpdateAgentPgHardCallbackSummaryReport(body model.UpdateAgentPgHardCallbackSummaryReport) error
}

func (r repo) GetPlayLogKeyList(dailyKeyList []string) ([]string, int64, error) {

	var list []string
	var total int64
	var err error

	// SELECT //
	query := r.db.Table("user_playlog as tb_playlog")
	query = query.Select("daily_key")
	query = query.Where("tb_playlog.daily_key IN ?", dailyKeyList)
	if err = query.
		Scan(&list).
		Error; err != nil {
		return nil, total, err
	}
	return list, total, nil
}

func (r repo) GetUserListByMemberList(memberList []string) ([]model.UserMemberList, error) {

	var result []model.UserMemberList

	if err := r.db.Table("user").
		Select("id, member_code").
		Where("member_code IN ?", memberList).
		Where("user.deleted_at IS NULL").
		Scan(&result).
		Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (r repo) GetUserListByIds(userIds []int64) ([]model.UserRefNo, error) {

	var result []model.UserRefNo

	if err := r.db.Table("user").
		Select("id, ref_by").
		Where("id IN (?)", userIds).
		Where("user.deleted_at IS NULL").
		Scan(&result).
		Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (r repo) CreateUserPlaylogBulk(bodyList map[string]model.UserPlaylogCreateBody, memberList []string) error {

	var createList []model.UserPlaylogCreateBody

	userMap := make(map[string]model.UserMemberList, 0)
	UserList, err := r.GetUserListByMemberList(memberList)
	if err != nil {
		return err
	}
	for _, v := range UserList {
		userMap[v.MemberCode] = v
	}

	for _, v := range bodyList {
		if _, ok := userMap[v.MemberCode]; ok {
			v.UserId = userMap[v.MemberCode].Id
		}
		// allow user 0
		createList = append(createList, v)
	}
	if len(createList) > 0 {
		if err := r.db.Table("user_playlog").Create(createList).Error; err != nil {
			return err
		}
	}
	return nil
}

func (r repo) CreateUserPlaylogBulkDirect(bodyList map[string]model.UserPlaylogCreateBody) error {

	var createList []model.UserPlaylogCreateBody
	for _, v := range bodyList {
		// allow user 0
		createList = append(createList, v)
	}
	if len(createList) > 0 {
		if err := r.db.Table("user_playlog").Create(createList).Error; err != nil {
			return err
		}
	}
	return nil
}

func (r repo) GetUserPlaylogCompletedStatus(path string, statementDate string) (*model.UserPlaylogStatus, error) {

	var result model.UserPlaylogStatus

	if err := r.db.Table("user_playlog_status").
		Select("*").
		Where("path = ?", path).
		Where("statement_date = ?", statementDate).
		Take(&result).
		Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (r repo) CreateUserPlaylogStatus(body model.UserPlaylogStatusCreateBody) error {

	if err := r.db.Table("user_playlog_status").Create(&body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetUserPlayLogList(req model.UserPlaylogListRequest) ([]model.UserPlaylogResponse, int64, error) {

	var list []model.UserPlaylogResponse
	var total int64

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("user_playlog as tb_logs")
	count = count.Select("tb_logs.id")
	if req.UserId != nil {
		count = count.Where("tb_logs.user_id = ?", req.UserId)
	}
	if req.FromDate != "" {
		count = count.Where("tb_logs.statement_date >= ? ", req.FromDate)
	}
	if req.ToDate != "" {
		count = count.Where("tb_logs.statement_date <=  ?", req.ToDate)
	}
	if err := count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_logs.id, tb_logs.user_id, users.member_code, tb_logs.statement_date as statement_date"
		selectedFields += ", tb_logs.turn_sport, tb_logs.valid_amount_sport, tb_logs.win_lose_sport, tb_logs.turn_casino, tb_logs.valid_amount_casino, tb_logs.win_lose_casino"
		selectedFields += ", tb_logs.turn_game, tb_logs.valid_amount_game, tb_logs.win_lose_game, tb_logs.turn_lottery, tb_logs.valid_amount_lottery, tb_logs.win_lose_lottery"
		selectedFields += ", tb_logs.turn_p2p, tb_logs.valid_amount_p2p, tb_logs.win_lose_p2p, tb_logs.turn_financial, tb_logs.valid_amount_financial, tb_logs.win_lose_financial"
		selectedFields += ", tb_logs.turn_total, tb_logs.win_lose_total, tb_logs.valid_amount_total, tb_logs.created_at"
		selectedFields += ", users.member_code as member_code"
		query := r.db.Table("user_playlog as tb_logs")
		query = query.Joins("INNER JOIN user as users ON users.id = tb_logs.user_id")
		query = query.Select(selectedFields)
		if req.UserId != nil {
			query = query.Where("tb_logs.user_id = ?", req.UserId)
		}
		if req.FromDate != "" {
			query = query.Where("tb_logs.statement_date >= ? ", req.FromDate)
		}
		if req.ToDate != "" {
			query = query.Where("tb_logs.statement_date <=  ?", req.ToDate)
		}
		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}

	return list, total, nil
}

func (r repo) GetDownlineUserTotalPlayLogList(req model.UserPlaylogListRequest) ([]model.UserPlaylogTotalResponse, int64, error) {

	var list []model.UserPlaylogTotalResponse
	var total int64

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("user_playlog as tb_logs")
	count = count.Select("tb_logs.id")
	count = count.Joins("INNER JOIN user as tb_user ON tb_user.id = tb_logs.user_id")
	count = count.Where("tb_user.ref_by != 0").Where("tb_user.deleted_at IS NULL").Where("tb_user.ref_by IS NOT NULL")
	if req.UserId != nil {
		count = count.Where("tb_logs.user_id = ?", req.UserId)
	}
	if req.FromDate != "" {
		count = count.Where("tb_logs.statement_date >= ? ", req.FromDate)
	}
	if req.ToDate != "" {
		count = count.Where("tb_logs.statement_date <=  ?", req.ToDate)
	}
	if err := count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_logs.user_id AS user_id"
		selectedFields += ", SUM(tb_logs.turn_sport) AS turn_sport, SUM(tb_logs.valid_amount_sport) AS valid_amount_sport, SUM(tb_logs.win_lose_sport) AS win_lose_sport"
		selectedFields += ", SUM(tb_logs.turn_casino) AS turn_casino, SUM(tb_logs.valid_amount_casino) AS valid_amount_casino, SUM(tb_logs.win_lose_casino) AS win_lose_casino"
		selectedFields += ", SUM(tb_logs.turn_game) AS turn_game, SUM(tb_logs.valid_amount_game) AS valid_amount_game, SUM(tb_logs.win_lose_game) AS win_lose_game"
		selectedFields += ", SUM(tb_logs.turn_lottery) AS turn_lottery, SUM(tb_logs.valid_amount_lottery) AS valid_amount_lottery, SUM(tb_logs.win_lose_lottery) AS win_lose_lottery"
		selectedFields += ", SUM(tb_logs.turn_p2p) AS turn_p2p, SUM(tb_logs.valid_amount_p2p) AS valid_amount_p2p, SUM(tb_logs.win_lose_p2p) AS win_lose_p2p"
		selectedFields += ", SUM(tb_logs.turn_financial) AS turn_financial, SUM(tb_logs.valid_amount_financial) AS valid_amount_financial, SUM(tb_logs.win_lose_financial) AS win_lose_financial"
		selectedFields += ", SUM(tb_logs.turn_total) AS turn_total, SUM(tb_logs.win_lose_total) AS win_lose_total, SUM(tb_logs.valid_amount_total) AS valid_amount_total"
		query := r.db.Table("user_playlog as tb_logs")
		query = query.Select(selectedFields)
		query = query.Joins("INNER JOIN user as tb_user ON tb_user.id = tb_logs.user_id")
		query = query.Where("tb_user.ref_by != 0").Where("tb_user.deleted_at IS NULL").Where("tb_user.ref_by IS NOT NULL")
		if req.UserId != nil {
			query = query.Where("tb_logs.user_id = ?", req.UserId)
		}
		if req.FromDate != "" {
			query = query.Where("tb_logs.statement_date >= ? ", req.FromDate)
		}
		if req.ToDate != "" {
			query = query.Where("tb_logs.statement_date <=  ?", req.ToDate)
		}
		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		query = query.Group("tb_logs.user_id")
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}

		userIds := make([]int64, 0)
		for _, v := range list {
			userIds = append(userIds, v.UserId)
		}

		// Append User Info
		userMap := make(map[int64]model.UserRefNo, 0)
		UserList, err := r.GetUserListByIds(userIds)
		if err != nil {
			return nil, total, err
		}
		for _, v := range UserList {
			userMap[v.Id] = v
		}
		for i, v := range list {
			if _, ok := userMap[v.UserId]; ok {
				list[i].RefBy = userMap[v.UserId].RefBy
			}
		}
	}

	return list, total, nil
}

func (r repo) GetUserTotalAffIncomeList(req model.UserAffiliateIncomeTotalListRequest) ([]model.UserAffiliateIncomeTotalResponse, int64, error) {

	var list []model.UserAffiliateIncomeTotalResponse
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("user_affiliate_income as tb_logs")
	count = count.Select("tb_user.ref_by")
	count = count.Joins("INNER JOIN user as tb_user ON tb_user.id = tb_logs.user_id")
	if req.RefBy != nil {
		count = count.Where("tb_user.ref_by = ?", req.RefBy)
	} else {
		count = count.Where("tb_user.ref_by != 0")
	}
	if req.UserId != nil {
		count = count.Where("tb_logs.user_id = ?", req.UserId)
	}
	if req.StatementDate != "" {
		count = count.Where("tb_logs.statement_date = ? ", req.StatementDate)
	}
	count = count.Group("tb_user.ref_by")
	if err = count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_user.ref_by AS ref_by, SUM(tb_logs.commission_total) AS commission_total"
		selectedFields += ", SUM(tb_logs.commission_sport) AS commission_sport, SUM(tb_logs.commission_casino) AS commission_casino, SUM(tb_logs.commission_game) AS commission_game"
		selectedFields += ", SUM(tb_logs.commission_lottery) AS commission_lottery, SUM(tb_logs.commission_p2p) AS commission_p2p, SUM(tb_logs.commission_financial) AS commission_financial"
		query := r.db.Table("user_affiliate_income as tb_logs")
		query = query.Select(selectedFields)
		query = query.Joins("INNER JOIN user as tb_user ON tb_user.id = tb_logs.user_id")
		if req.RefBy != nil {
			query = query.Where("tb_user.ref_by = ?", req.RefBy)
		} else {
			query = query.Where("tb_user.ref_by != 0")
		}
		if req.UserId != nil {
			query = query.Where("tb_logs.user_id = ?", req.UserId)
		}
		if req.StatementDate != "" {
			query = query.Where("tb_logs.statement_date = ? ", req.StatementDate)
		}
		// NO Sort NO limit //
		query = query.Group("tb_user.ref_by")
		if err = query.
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetUserTotalAffIncomeListWithLimit(req model.UserAffiliateIncomeTotalListRequest) (map[int64]model.UserAffiliateIncomeTotalResponse, error) {

	result := make(map[int64]model.UserAffiliateIncomeTotalResponse, 0)
	var list []model.UserAffiliateIncomeDownlineResponse

	// SELECT //
	selectedFields := "tb_user.ref_by AS ref_by, tb_user.id AS user_id, (tb_logs.commission_total) AS commission_total"
	selectedFields += ", (tb_logs.commission_sport) AS commission_sport, (tb_logs.commission_casino) AS commission_casino, (tb_logs.commission_game) AS commission_game"
	selectedFields += ", (tb_logs.commission_lottery) AS commission_lottery, (tb_logs.commission_p2p) AS commission_p2p, (tb_logs.commission_financial) AS commission_financial"
	query := r.db.Table("user_affiliate_income as tb_logs")
	query = query.Select(selectedFields)
	query = query.Joins("INNER JOIN user as tb_user ON tb_user.id = tb_logs.user_id")
	if req.RefBy != nil {
		query = query.Where("tb_user.ref_by = ?", req.RefBy)
	} else {
		query = query.Where("tb_user.ref_by != 0")
	}
	if req.UserId != nil {
		query = query.Where("tb_logs.user_id = ?", req.UserId)
	}
	if req.StatementDate != "" {
		query = query.Where("tb_logs.statement_date = ? ", req.StatementDate)
	}
	// NO Sort NO limit //
	if err := query.
		Scan(&list).
		Error; err != nil {
		return nil, err
	}

	if req.LimitAmount > 0 {
		refIds := make(map[int64]int64, 0)
		for _, v := range list {
			if v.RefBy > 0 {
				refIds[v.RefBy] = v.RefBy
			}
		}
		// [20240617] สิ่งที่ต้องการ
		// เพิ่มจำกัดรายได้สูงสุดต่อ 1 ลิ้งก์แนะนำ และช่อง Text area สำหรับกรอกตัวเลขอาราบิก
		// *จำกัดรายได้สูงสุดต่อ 1 ลิ้งก์แนะนำ เกินจำนวนที่ตั้งค่า จะไม่สามารถรับรายได้ได้อีก (0 หมายถึงไม่จำกัด)
		// ตัวอย่างเช่น
		// จำกัดรายได้สูงสุด 100 (รายได้รวมจากทุกลูกลิ้งก์)
		// จำกัดรายได้สูงสุดต่อ 1 ยูส 10 (รายได้สูงสุดที่รับได้จากค่าคอม 1 ลูกลิ้งก์)
		// หมายความว่ารายได้ของหัวลิ้งก์ ต่อ 1 ยูสลูกลิ้งก์ จะรับได้ไม่เกิน 10 เครดิต แต่รับกี่ยูสลูกลิ้งก์ ก็ได้
		downlineTotalIncomeMap := make(map[int64]model.AffiliateDownlineTotalIncomeResponse, 0)
		if req.LimitAmount > 0 {
			downlineTotalIncome, err := r.GetAffiliateDownlineTotalIncomeList(helper.MapIdsToInt64Array(refIds))
			if err != nil {
				return nil, err
			}
			for _, v := range downlineTotalIncome {
				downlineTotalIncomeMap[v.UserId] = v
			}
		}
		for i, v := range list {
			if downlineLimit, ok := downlineTotalIncomeMap[v.UserId]; ok {
				receivableAmount := req.LimitAmount - downlineLimit.TotalCommission
				// fmt.Println("req.LimitAmount", req.LimitAmount)
				// fmt.Println("CommissionTotal", downlineLimit.TotalCommission)
				// fmt.Println("receivableAmount", receivableAmount)
				if receivableAmount > 0 {
					if v.CommissionTotal > receivableAmount {
						list[i].CommissionTotal = receivableAmount
						diff := v.CommissionTotal - receivableAmount
						// fmt.Println("receiveAmount.MAX=", receivableAmount, "diff=", diff)
						// Reduce the commission from sport, casino, game by order to zero, until the diff is zero.
						if diff > 0 && list[i].CommissionSport > 0 {
							if list[i].CommissionSport >= diff {
								list[i].CommissionSport -= diff
								diff = 0
							} else {
								diff -= list[i].CommissionSport
								list[i].CommissionSport = 0
							}
						}
						if diff > 0 && list[i].CommissionCasino > 0 {
							if list[i].CommissionCasino >= diff {
								list[i].CommissionCasino -= diff
								diff = 0
							} else {
								diff -= list[i].CommissionCasino
								list[i].CommissionCasino = 0
							}
						}
						if diff > 0 && list[i].CommissionGame > 0 {
							if list[i].CommissionGame >= diff {
								list[i].CommissionGame -= diff
								diff = 0
							} else {
								diff -= list[i].CommissionGame
								list[i].CommissionGame = 0
							}
						}
						if diff > 0 && list[i].CommissionLottery > 0 {
							if list[i].CommissionLottery >= diff {
								list[i].CommissionLottery -= diff
								diff = 0
							} else {
								diff -= list[i].CommissionLottery
								list[i].CommissionLottery = 0
							}
						}
						if diff > 0 && list[i].CommissionP2p > 0 {
							if list[i].CommissionP2p >= diff {
								list[i].CommissionP2p -= diff
								diff = 0
							} else {
								diff -= list[i].CommissionP2p
								list[i].CommissionP2p = 0
							}
						}
						if diff > 0 && list[i].CommissionFinancial > 0 {
							if list[i].CommissionFinancial >= diff {
								list[i].CommissionFinancial -= diff
								diff = 0
							} else {
								diff -= list[i].CommissionFinancial
								list[i].CommissionFinancial = 0
							}
						}
						if list[i].CommissionTotal != list[i].CommissionSport+list[i].CommissionCasino+list[i].CommissionGame+list[i].CommissionLottery+list[i].CommissionP2p+list[i].CommissionFinancial {
							log.Println("Wrong Downline SUM", helper.StructJson(list[i]))
						}
					} else {
						list[i].CommissionTotal = v.CommissionTotal
						// fmt.Println("receiveAmount=", v.CommissionTotal)
					}
				} else {
					list[i].CommissionTotal = 0
					list[i].CommissionSport = 0
					list[i].CommissionCasino = 0
					list[i].CommissionGame = 0
					list[i].CommissionLottery = 0
					list[i].CommissionP2p = 0
					list[i].CommissionFinancial = 0
					// fmt.Println("receiveAmount.NoMore")
				}
			}
		}
	}

	// SUM + Group By RefBy
	for _, v := range list {
		if item, ok := result[v.RefBy]; ok {
			// Update
			tempItem := model.UserAffiliateIncomeTotalResponse{
				RefBy:               v.RefBy,
				CommissionTotal:     item.CommissionTotal + v.CommissionTotal,
				CommissionSport:     item.CommissionSport + v.CommissionSport,
				CommissionCasino:    item.CommissionCasino + v.CommissionCasino,
				CommissionGame:      item.CommissionGame + v.CommissionGame,
				CommissionLottery:   item.CommissionLottery + v.CommissionLottery,
				CommissionP2p:       item.CommissionP2p + v.CommissionP2p,
				CommissionFinancial: item.CommissionFinancial + v.CommissionFinancial,
			}
			// Append UserList
			tempUserList := item.UserList
			tempUserList = append(tempUserList, v)
			tempItem.UserList = tempUserList
			// SET
			result[v.RefBy] = tempItem
		} else {
			// Create
			tempItem := model.UserAffiliateIncomeTotalResponse{
				RefBy:               v.RefBy,
				CommissionTotal:     v.CommissionTotal,
				CommissionSport:     v.CommissionSport,
				CommissionCasino:    v.CommissionCasino,
				CommissionGame:      v.CommissionGame,
				CommissionLottery:   v.CommissionLottery,
				CommissionP2p:       v.CommissionP2p,
				CommissionFinancial: v.CommissionFinancial,
			}
			// Append UserList
			tempUserList := item.UserList
			tempUserList = append(tempUserList, v)
			tempItem.UserList = tempUserList
			result[v.RefBy] = tempItem
		}
	}

	return result, nil
}

func (r repo) GetUserLevelAffIncomeListWithLimit(req model.UserAffiliateIncomeTotalListRequest, setting model.AfCommissionResponse) (map[int64]model.UserAffiliateIncomeTotalResponse, error) {

	result := make(map[int64]model.UserAffiliateIncomeTotalResponse, 0)

	// fmt.Println("req", helper.StructJson(req))
	// fmt.Println("setting", helper.StructJson(setting))

	// คำนวนใหม่จาก affiliate_commision = setting ใหม่ ตามเลเวล
	// maxLevel = 2 = SHOW DEFAULT+2
	// maxLevel = 3 = SHOW DEFAULT+1+2 (ALL)
	// maxLevel = 1 = SHOW DEFAULT

	var list []model.UserAffiliateIncomeDownlineResponse
	// SELECT //
	selectedFields := "tb_level.level AS level, tb_level.upline_id AS ref_by, tb_level.user_id AS user_id"
	selectedFields += ", CASE WHEN tb_level.level = 3 THEN (tb_log.commission_sport2) ELSE (CASE WHEN tb_level.level = 2 THEN (tb_log.commission_sport1) ELSE (tb_log.commission_sport) END) END AS commission_sport"
	selectedFields += ", CASE WHEN tb_level.level = 3 THEN (tb_log.commission_casino2) ELSE (CASE WHEN tb_level.level = 2 THEN (tb_log.commission_casino1) ELSE (tb_log.commission_casino) END) END AS commission_casino"
	selectedFields += ", CASE WHEN tb_level.level = 3 THEN (tb_log.commission_game2) ELSE (CASE WHEN tb_level.level = 2 THEN (tb_log.commission_game1) ELSE (tb_log.commission_game) END) END AS commission_game"
	selectedFields += ", CASE WHEN tb_level.level = 3 THEN (tb_log.commission_lottery2) ELSE (CASE WHEN tb_level.level = 2 THEN (tb_log.commission_lottery1) ELSE (tb_log.commission_lottery) END) END AS commission_lottery"
	selectedFields += ", CASE WHEN tb_level.level = 3 THEN (tb_log.commission_p2p2) ELSE (CASE WHEN tb_level.level = 2 THEN (tb_log.commission_p2p1) ELSE (tb_log.commission_p2p) END) END AS commission_p2p"
	selectedFields += ", CASE WHEN tb_level.level = 3 THEN (tb_log.commission_financial2) ELSE (CASE WHEN tb_level.level = 2 THEN (tb_log.commission_financial1) ELSE (tb_log.commission_financial) END) END AS commission_financial"

	query := r.db.Table("affiliate_level as tb_level")
	query = query.Select(selectedFields)
	query = query.Joins("INNER JOIN user_affiliate_income as tb_log ON tb_log.user_id = tb_level.user_id")
	if req.RefBy != nil {
		query = query.Where("tb_level.upline_id = ?", req.RefBy)
	} else {
		query = query.Where("tb_level.upline_id != 0")
	}
	if req.UserId != nil {
		query = query.Where("tb_log.user_id = ?", req.UserId)
	}
	if req.StatementDate != "" {
		query = query.Where("tb_log.statement_date = ? ", req.StatementDate)
	}
	if setting.MaxLevel == 2 {
		query = query.Where("tb_level.level IN (1,2)")
	} else if setting.MaxLevel != 3 {
		query = query.Where("tb_level.level = 1") // ONLY_1
	}
	// NO Sort NO limit //
	if err := query.
		Scan(&list).
		Error; err != nil {
		return nil, err
	}

	for i, v := range list {
		// SUM TOTAL COMMISSION HERE
		list[i].CommissionTotal = v.CommissionSport + v.CommissionCasino + v.CommissionGame + v.CommissionLottery + v.CommissionP2p + v.CommissionFinancial
	}

	if req.LimitAmount > 0 {
		refIds := make(map[int64]int64, 0)
		for _, v := range list {
			if v.RefBy > 0 {
				refIds[v.RefBy] = v.RefBy
			}
		}
		// [20240617] สิ่งที่ต้องการ
		// เพิ่มจำกัดรายได้สูงสุดต่อ 1 ลิ้งก์แนะนำ และช่อง Text area สำหรับกรอกตัวเลขอาราบิก
		// *จำกัดรายได้สูงสุดต่อ 1 ลิ้งก์แนะนำ เกินจำนวนที่ตั้งค่า จะไม่สามารถรับรายได้ได้อีก (0 หมายถึงไม่จำกัด)
		// ตัวอย่างเช่น
		// จำกัดรายได้สูงสุด 100 (รายได้รวมจากทุกลูกลิ้งก์)
		// จำกัดรายได้สูงสุดต่อ 1 ยูส 10 (รายได้สูงสุดที่รับได้จากค่าคอม 1 ลูกลิ้งก์)
		// หมายความว่ารายได้ของหัวลิ้งก์ ต่อ 1 ยูสลูกลิ้งก์ จะรับได้ไม่เกิน 10 เครดิต แต่รับกี่ยูสลูกลิ้งก์ ก็ได้
		downlineTotalIncomeMap := make(map[int64]model.AffiliateDownlineTotalIncomeResponse, 0)
		if req.LimitAmount > 0 {
			downlineTotalIncome, err := r.GetAffiliateDownlineTotalIncomeList(helper.MapIdsToInt64Array(refIds))
			if err != nil {
				return nil, err
			}
			for _, v := range downlineTotalIncome {
				downlineTotalIncomeMap[v.UserId] = v
			}
		}
		for i, v := range list {
			if downlineLimit, ok := downlineTotalIncomeMap[v.UserId]; ok {
				// receivableAmount := req.LimitAmount - downlineLimit.TotalCommission
				receivableAmount := helper.SubtractCurrency(req.LimitAmount, downlineLimit.TotalCommission)
				// fmt.Println("req.LimitAmount", req.LimitAmount)
				// fmt.Println("CommissionTotal", downlineLimit.TotalCommission)
				// fmt.Println("receivableAmount", receivableAmount)
				if receivableAmount > 0 {
					if v.CommissionTotal > receivableAmount {
						list[i].CommissionTotal = receivableAmount
						// diff := v.CommissionTotal - receivableAmount
						diff := helper.SubtractCurrency(v.CommissionTotal, receivableAmount)
						// fmt.Println("receiveAmount.MAX=", receivableAmount, "diff=", diff)
						// Reduce the commission from sport, casino, game by order to zero, until the diff is zero.
						if diff > 0 && list[i].CommissionSport > 0 {
							if list[i].CommissionSport >= diff {
								list[i].CommissionSport -= diff
								diff = 0
							} else {
								diff -= list[i].CommissionSport
								list[i].CommissionSport = 0
							}
						}
						if diff > 0 && list[i].CommissionCasino > 0 {
							if list[i].CommissionCasino >= diff {
								list[i].CommissionCasino -= diff
								diff = 0
							} else {
								diff -= list[i].CommissionCasino
								list[i].CommissionCasino = 0
							}
						}
						if diff > 0 && list[i].CommissionGame > 0 {
							if list[i].CommissionGame >= diff {
								list[i].CommissionGame -= diff
								diff = 0
							} else {
								diff -= list[i].CommissionGame
								list[i].CommissionGame = 0
							}
						}
						if diff > 0 && list[i].CommissionLottery > 0 {
							if list[i].CommissionLottery >= diff {
								list[i].CommissionLottery -= diff
								diff = 0
							} else {
								diff -= list[i].CommissionLottery
								list[i].CommissionLottery = 0
							}
						}
						if diff > 0 && list[i].CommissionP2p > 0 {
							if list[i].CommissionP2p >= diff {
								list[i].CommissionP2p -= diff
								diff = 0
							} else {
								diff -= list[i].CommissionP2p
								list[i].CommissionP2p = 0
							}
						}
						if diff > 0 && list[i].CommissionFinancial > 0 {
							if list[i].CommissionFinancial >= diff {
								list[i].CommissionFinancial -= diff
								diff = 0
							} else {
								diff -= list[i].CommissionFinancial
								list[i].CommissionFinancial = 0
							}
						}
						if list[i].CommissionTotal != list[i].CommissionSport+list[i].CommissionCasino+list[i].CommissionGame+list[i].CommissionLottery+list[i].CommissionP2p+list[i].CommissionFinancial {
							log.Println("Wrong Downline SUM", helper.StructJson(list[i]))
						}
					} else {
						list[i].CommissionTotal = v.CommissionTotal
						// fmt.Println("receiveAmount=", v.CommissionTotal)
					}
				} else {
					// fmt.Println("receiveAmount.NoMore")
					list[i].CommissionTotal = 0
					list[i].CommissionSport = 0
					list[i].CommissionCasino = 0
					list[i].CommissionGame = 0
					list[i].CommissionLottery = 0
					list[i].CommissionP2p = 0
					list[i].CommissionFinancial = 0
				}
			}
		}
	}

	// fmt.Println("GetUserLevelAffIncomeListWithLimit.list", helper.StructJson(list))

	// SUM + Group By RefBy
	for _, v := range list {
		if item, ok := result[v.RefBy]; ok {
			// Update
			tempItem := model.UserAffiliateIncomeTotalResponse{
				RefBy:               v.RefBy,
				CommissionTotal:     item.CommissionTotal + v.CommissionTotal,
				CommissionSport:     item.CommissionSport + v.CommissionSport,
				CommissionCasino:    item.CommissionCasino + v.CommissionCasino,
				CommissionGame:      item.CommissionGame + v.CommissionGame,
				CommissionLottery:   item.CommissionLottery + v.CommissionLottery,
				CommissionP2p:       item.CommissionP2p + v.CommissionP2p,
				CommissionFinancial: item.CommissionFinancial + v.CommissionFinancial,
			}
			// Append UserList
			tempUserList := item.UserList
			tempUserList = append(tempUserList, v)
			tempItem.UserList = tempUserList
			// SET
			result[v.RefBy] = tempItem
		} else {
			// Create
			tempItem := model.UserAffiliateIncomeTotalResponse{
				RefBy:               v.RefBy,
				CommissionTotal:     v.CommissionTotal,
				CommissionSport:     v.CommissionSport,
				CommissionCasino:    v.CommissionCasino,
				CommissionGame:      v.CommissionGame,
				CommissionLottery:   v.CommissionLottery,
				CommissionP2p:       v.CommissionP2p,
				CommissionFinancial: v.CommissionFinancial,
			}
			// Append UserList
			tempUserList := item.UserList
			tempUserList = append(tempUserList, v)
			tempItem.UserList = tempUserList
			result[v.RefBy] = tempItem
		}
	}

	return result, nil
}

func (r repo) GetUserAffiliateIncomeKeyList(dailyKeyList []string) ([]string, int64, error) {

	var list []string
	var total int64
	var err error

	// SELECT //
	query := r.db.Table("user_affiliate_income as tb_income")
	query = query.Select("daily_key")
	query = query.Where("tb_income.daily_key IN ?", dailyKeyList)
	if err = query.
		Scan(&list).
		Error; err != nil {
		return nil, total, err
	}
	return list, total, nil
}

func (r repo) CreateUserAffiliateIncomeBulk(bodyList map[string]model.UserAffiliateIncomeCreateBody) error {

	var createList []model.UserAffiliateIncomeCreateBody
	for _, v := range bodyList {
		createList = append(createList, v)
	}
	if len(createList) > 0 {
		// This User ** Income From This User ** for Calculate History Log
		if err := r.db.Table("user_affiliate_income").Create(createList).Error; err != nil {
			return err
		}
	}
	return nil
}

func (r repo) GetUserAffiliateTransactionKeyList(dailyKeyList []string) ([]string, int64, error) {

	var list []string
	var total int64
	var err error

	// SELECT //
	query := r.db.Table("affiliate_transaction as tb_log")
	query = query.Select("tb_log.daily_key")
	query = query.Where("tb_log.daily_key IN (?)", dailyKeyList)
	if err = query.
		Scan(&list).
		Error; err != nil {
		return nil, total, err
	}
	return list, total, nil
}

func (r repo) CreateUserAffiliateTransactionBulk(bodyList map[string]model.UserAffiliateIncomeTotalResponse) error {

	// CONFIG-LIMIT-ALL-AFFCOMMISSION
	afCommissionSetting, err := r.GetCommissionSetting()
	if err != nil {
		return err
	}

	// Check Total Limit
	userAffLimitMap := make(map[int64]model.AffiliateUser, 0)
	if afCommissionSetting.MaxCommission > 0 {
		userIds := make(map[int64]int64, 0)
		for _, v := range bodyList {
			userIds[v.RefBy] = v.RefBy
		}
		if len(userIds) > 0 {
			refUserAffiliateList, err := r.GetUserAffiliateList(helper.MapIdsToInt64Array(userIds))
			if err != nil {
				return err
			}
			for _, userAff := range refUserAffiliateList {
				userAffLimitMap[userAff.UserId] = userAff
			}
		}
	}

	// fmt.Println("bodyList", helper.StructJson(bodyList))
	// fmt.Println("userAffLimitMap", helper.StructJson(userAffLimitMap))

	// Create Per RefBy
	var createList []model.AffTransactionCreateBody
	for i, v := range bodyList {
		var newRow model.AffTransactionCreateBody
		newRow.UserId = v.RefBy
		newRow.DailyKey = v.DailyKey
		amount := v.CommissionTotal
		// IF OVER LIMIT, INSERT ZERO INCOME
		if afCommissionSetting.MaxCommission > 0 {
			if userAff, ok := userAffLimitMap[v.RefBy]; ok {
				if (userAff.TotalWithdraw + userAff.CommissionCurrent) >= afCommissionSetting.MaxCommission {
					amount = 0 // NO MORE
				} else {
					// 10 - (0 + 0) = 10
					canGetAmount := afCommissionSetting.MaxCommission - (userAff.TotalWithdraw + userAff.CommissionCurrent)
					if canGetAmount > 0 {
						if amount > canGetAmount {
							amount = canGetAmount
						}
					} else {
						amount = 0 // NO MORE
					}
				}
			}
		}
		newRow.IncomeAmount = amount
		newRow.TypeId = model.AFF_TRANSACTION_TYPE_PLAY_COMMISION
		newRow.StatusId = model.AFF_TRANSACTION_STATUS_PENDING
		createList = append(createList, newRow)
		// Update bodyList onChange
		if amount > 0 && amount != v.CommissionTotal {
			diff := v.CommissionTotal - amount
			diffTotalUser := v.CommissionTotal - amount
			if diff < 0 {
				log.Println("diff < 0", diff)
			}
			tempBody := bodyList[i]
			tempBody.CommissionTotal = amount
			// Reduce the commission from sport, casino, game by order to zero, until the diff is zero.
			if diff > 0 && tempBody.CommissionSport > 0 {
				if tempBody.CommissionSport >= diff {
					tempBody.CommissionSport -= diff
					diff = 0
				} else {
					diff -= tempBody.CommissionSport
					tempBody.CommissionSport = 0
				}
			}
			if diff > 0 && tempBody.CommissionCasino > 0 {
				if tempBody.CommissionCasino >= diff {
					tempBody.CommissionCasino -= diff
					diff = 0
				} else {
					diff -= tempBody.CommissionCasino
					tempBody.CommissionCasino = 0
				}
			}
			if diff > 0 && tempBody.CommissionGame > 0 {
				if tempBody.CommissionGame >= diff {
					tempBody.CommissionGame -= diff
					diff = 0
				} else {
					diff -= tempBody.CommissionGame
					tempBody.CommissionGame = 0
				}
			}
			if diff > 0 && tempBody.CommissionLottery > 0 {
				if tempBody.CommissionLottery >= diff {
					tempBody.CommissionLottery -= diff
					diff = 0
				} else {
					diff -= tempBody.CommissionLottery
					tempBody.CommissionLottery = 0
				}
			}
			if diff > 0 && tempBody.CommissionP2p > 0 {
				if tempBody.CommissionP2p >= diff {
					tempBody.CommissionP2p -= diff
					diff = 0
				} else {
					diff -= tempBody.CommissionP2p
					tempBody.CommissionP2p = 0
				}
			}
			if diff > 0 && tempBody.CommissionFinancial > 0 {
				if tempBody.CommissionFinancial >= diff {
					tempBody.CommissionFinancial -= diff
					diff = 0
				} else {
					diff -= tempBody.CommissionFinancial
					tempBody.CommissionFinancial = 0
				}
			}
			if tempBody.CommissionTotal != tempBody.CommissionSport+tempBody.CommissionCasino+tempBody.CommissionGame+tempBody.CommissionLottery+tempBody.CommissionP2p+tempBody.CommissionFinancial {
				log.Println("Wrong RefBy SUM", helper.StructJson(tempBody))
			}
			// Reduce UseList ** LATER !!! *** ลดลูกสายตามที่ลด ตามประเภทเกมจริงด้วย ****
			for j, downline := range tempBody.UserList {
				if diffTotalUser > 0 && downline.CommissionTotal > 0 {
					if downline.CommissionTotal >= diffTotalUser {
						tempBody.UserList[j].CommissionTotal -= diffTotalUser
						diffTotalUser = 0
					} else {
						diffTotalUser -= downline.CommissionTotal
						tempBody.UserList[j].CommissionTotal = 0
					}
				}
			}
			bodyList[i] = tempBody
		} else if amount == 0 {
			// fmt.Println("Refby Already Over Limit,", v.RefBy, "amount=", amount)
			tempBody := bodyList[i]
			tempBody.CommissionTotal = 0
			tempBody.CommissionSport = 0
			tempBody.CommissionCasino = 0
			tempBody.CommissionGame = 0
			tempBody.CommissionLottery = 0
			tempBody.CommissionP2p = 0
			tempBody.CommissionFinancial = 0
			// Remove UseList = No Update Any
			tempBody.UserList = nil
			bodyList[i] = tempBody
		}
	}

	// fmt.Println("bodyList2", helper.StructJson(bodyList))

	if len(createList) > 0 {
		// Each RefBy
		var updateDownlineLimitList []model.AffiliateDownlineTotalIncomeIncreaseBody
		for _, row := range bodyList {
			if row.CommissionTotal > 0 {
				// Build BulkData for each Downline.
				for _, downline := range row.UserList {
					// Update
					updateDownlineLimitList = append(updateDownlineLimitList, model.AffiliateDownlineTotalIncomeIncreaseBody{
						Ukey:       fmt.Sprintf("%d-%d", row.RefBy, downline.UserId),
						Commission: downline.CommissionTotal, // use to Increase
					})
				}
			}
		}

		tx := r.db.Begin()
		// For Ref User
		if err := tx.Table("affiliate_transaction").Create(&createList).Error; err != nil {
			tx.Rollback()
			return err
		}
		// For Ref User
		for _, row := range bodyList {
			// Sum Total AFF Income
			if row.RefBy != 0 && row.CommissionTotal > 0 {
				obj := map[string]interface{}{}
				obj["commission_total"] = gorm.Expr("commission_total + ?", row.CommissionTotal)
				obj["commission_current"] = gorm.Expr("commission_current + ?", row.CommissionTotal)
				obj["commission_sport"] = gorm.Expr("commission_sport + ?", row.CommissionSport)
				obj["commission_casino"] = gorm.Expr("commission_casino + ?", row.CommissionCasino)
				obj["commission_game"] = gorm.Expr("commission_game + ?", row.CommissionGame)
				obj["commission_lottery"] = gorm.Expr("commission_lottery + ?", row.CommissionLottery)
				obj["commission_p2p"] = gorm.Expr("commission_p2p + ?", row.CommissionP2p)
				obj["commission_financial"] = gorm.Expr("commission_financial + ?", row.CommissionFinancial)
				// obj["play_balance"] = gorm.Expr("play_balance + ?", u.PlayBalance)
				// obj["received_balance"] = gorm.Expr("received_balance + ?", u.ReceivedBalance)
				if err := tx.Table("user_affiliate").
					Where("user_id = ?", row.RefBy).
					Updates(obj).
					Error; err != nil {
					tx.Rollback()
					return err
				}
			}
		}

		if len(updateDownlineLimitList) > 0 {
			for _, row := range updateDownlineLimitList {
				// Increase the total commission
				if row.Commission > 0 {
					if err := r.db.Table("affiliate_level").Where("ukey = ?", row.Ukey).Update("total_commission", gorm.Expr("total_commission + ?", row.Commission)).Error; err != nil {
						return err
					}
				}
			}
		}
		tx.Commit()
	}
	return nil
}

func (r repo) CreateUserTodayPlaylogBulkDirect(bodyList map[string]model.UserTodayPlaylogCreateBody) error {

	var createList []model.UserTodayPlaylogCreateBody
	for _, v := range bodyList {
		// allow user 0
		createList = append(createList, v)
	}
	if len(createList) > 0 {
		if err := r.db.Table("user_today_playlog").Create(createList).Error; err != nil {
			return err
		}
	}
	return nil
}

func (r repo) CreateUserTodayPlaylogBulk(bodyList map[string]model.UserTodayPlaylogCreateBody, memberList []string) error {

	var createList []model.UserTodayPlaylogCreateBody

	userMap := make(map[string]model.UserMemberList, 0)
	UserList, err := r.GetUserListByMemberList(memberList)
	if err != nil {
		return err
	}
	for _, v := range UserList {
		userMap[v.MemberCode] = v
	}

	for _, v := range bodyList {
		if _, ok := userMap[v.MemberCode]; ok {
			v.UserId = userMap[v.MemberCode].Id
		}
		// allow user 0
		createList = append(createList, v)
	}
	if len(createList) > 0 {
		if err := r.db.Table("user_today_playlog").Create(createList).Error; err != nil {
			return err
		}
	}
	return nil
}

func (r repo) GetTodayPlayLogKeyList(dailyKeyList []string) ([]model.UserTodayPlaylogResponse, int64, error) {

	var list []model.UserTodayPlaylogResponse
	var total int64

	selected_fields := "id, user_id, statement_date, daily_key"
	selected_fields += ", turn_sport, valid_amount_sport, win_lose_sport, turn_casino, valid_amount_casino, win_lose_casino"
	selected_fields += ", turn_game, valid_amount_game, win_lose_game, turn_lottery, valid_amount_lottery, win_lose_lottery"
	selected_fields += ", turn_p2p, valid_amount_p2p, win_lose_p2p, turn_financial, valid_amount_financial, win_lose_financial"
	selected_fields += ", turn_total, win_lose_total, valid_amount_total, created_at"

	// SELECT //
	query := r.db.Table("user_today_playlog as tb_playlog")
	query = query.Select(selected_fields)
	query = query.Where("tb_playlog.daily_key IN ?", dailyKeyList)
	if err := query.
		Scan(&list).
		Error; err != nil {
		return nil, total, err
	}
	return list, total, nil
}

func (r repo) UpdateUserTodayPlaylog(updateBody model.UserTodayPlaylogUpdateBody) error {

	if err := r.db.Table("user_today_playlog").Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetTodaySumUserPlayLogList(req model.UserTodayPlaylogListRequest) ([]model.UserTodaySumPlaylogReponse, int64, error) {

	var list []model.UserTodaySumPlaylogReponse
	var total int64

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("user_today_playlog as tb_playlog")
	count = count.Select("tb_playlog.id")
	if req.UserId != nil {
		count = count.Where("tb_playlog.user_id = ?", req.UserId)
	}
	if req.StatementDate != "" {
		count = count.Where("tb_playlog.statement_date = ? ", req.StatementDate)
	}
	if err := count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_playlog.statement_date AS statement_date, tb_playlog.user_id AS user_id"
		selectedFields += ", SUM(tb_playlog.turn_sport) AS sum_turn_sport, SUM(tb_playlog.win_lose_sport) AS sum_win_lose_sport, SUM(tb_playlog.valid_amount_sport) AS sum_valid_amount_sport"
		selectedFields += ", SUM(tb_playlog.turn_casino) AS sum_turn_casino, SUM(tb_playlog.win_lose_casino) AS sum_win_lose_casino, SUM(tb_playlog.valid_amount_casino) AS sum_valid_amount_casino"
		selectedFields += ", SUM(tb_playlog.turn_game) AS sum_turn_game, SUM(tb_playlog.win_lose_game) AS sum_win_lose_game, SUM(tb_playlog.valid_amount_game) AS sum_valid_amount_game"
		selectedFields += ", SUM(tb_playlog.turn_lottery) AS sum_turn_lottery, SUM(tb_playlog.win_lose_lottery) AS sum_win_lose_lottery, SUM(tb_playlog.valid_amount_lottery) AS sum_valid_amount_lottery"
		selectedFields += ", SUM(tb_playlog.turn_p2p) AS sum_turn_p2p, SUM(tb_playlog.win_lose_p2p) AS sum_win_lose_p2p, SUM(tb_playlog.valid_amount_p2p) AS sum_valid_amount_p2p"
		selectedFields += ", SUM(tb_playlog.turn_financial) AS sum_turn_financial, SUM(tb_playlog.win_lose_financial) AS sum_win_lose_financial, SUM(tb_playlog.valid_amount_financial) AS sum_valid_amount_financial"
		selectedFields += ", SUM(tb_playlog.turn_total) AS sum_turn_total, SUM(tb_playlog.win_lose_total) AS sum_win_lose_total, SUM(tb_playlog.valid_amount_total) AS sum_valid_amount_total"
		query := r.db.Table("user_today_playlog as tb_playlog")
		query = query.Select(selectedFields)
		if req.UserId != nil {
			query = query.Where("tb_playlog.user_id = ?", req.UserId)
		}
		if req.StatementDate != "" {
			query = query.Where("tb_playlog.statement_date = ? ", req.StatementDate)
		}
		if req.StatementDateStart != "" {
			query = query.Where("tb_playlog.statement_date >= ? ", req.StatementDateStart)
		}
		if req.StatementDateEnd != "" {
			query = query.Where("tb_playlog.statement_date <=  ?", req.StatementDateEnd)
		}
		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		query = query.Group("tb_playlog.statement_date, tb_playlog.user_id")
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetTotalPlaylogSummaryByUserId(userId int64) (*model.UserPlaylogSummary, error) {

	var record model.UserPlaylogSummary

	// [20240527] เอาข้อมูลที่เก่าที่สุดก่อน คือ play_log แต่จะไม่ได้ข้อมูลวันนี้
	// later : ข้อมูลตาม playlog ตัดยอดไปก่อน
	// MIGRATE(*run after 12.30) => playlog = oldest - yesterday
	// then 00.00 - 12.30 = ???
	// then 12.32 - 23.59 = ???
	// CRON PLAYLOG = Append Total
	// Runtime = today, append total

	selectedFields := "tb_playlog.user_id AS user_id, MIN(tb_playlog.date) AS playlog_start_date"
	selectedFields += ", SUM(tb_playlog.turn_total) AS total_turn, SUM(tb_playlog.win_lose_total) AS total_win_loss"
	if err := r.db.Table("play_log as tb_playlog").
		Select(selectedFields).
		Where("tb_playlog.user_id = ?", userId).
		Group("tb_playlog.user_id").
		Scan(&record).
		Error; err != nil {
		return nil, err
	}

	return &record, nil
}

func (r repo) GetDailyTotalUserPaylogList(statementDate string) ([]model.PlaylogTotalAmount, error) {

	var list []model.PlaylogTotalAmount

	selectedFields := "users.id as user_id, SUM(logs.win_lose_total) as total_loss_amount"
	selectedFields += ", SUM(logs.win_lose_sport) as total_loss_sport, SUM(logs.win_lose_casino) as total_loss_casino, SUM(logs.win_lose_game) as total_loss_game"
	selectedFields += ", SUM(logs.win_lose_lottery) as total_loss_lottery, SUM(logs.win_lose_p2p) as total_loss_p2p, SUM(logs.win_lose_financial) as total_loss_financial"
	// Use Turn
	selectedFields += ", SUM(logs.turn_total) as total_turn_amount"
	selectedFields += ", SUM(logs.turn_sport) as total_turn_sport, SUM(logs.turn_casino) as total_turn_casino, SUM(logs.turn_game) as total_turn_game"
	selectedFields += ", SUM(logs.turn_lottery) as total_turn_lottery, SUM(logs.turn_p2p) as total_turn_p2p, SUM(logs.turn_financial) as total_turn_financial"
	// Use ValidAmount
	selectedFields += ", SUM(logs.valid_amount_total) as total_valid_amount"
	selectedFields += ", SUM(logs.valid_amount_sport) as total_valid_amount_sport, SUM(logs.valid_amount_casino) as total_valid_amount_casino, SUM(logs.valid_amount_game) as total_valid_amount_game"
	selectedFields += ", SUM(logs.valid_amount_lottery) as total_valid_amount_lottery, SUM(logs.valid_amount_p2p) as total_valid_amount_p2p, SUM(logs.valid_amount_financial) as total_valid_amount_financial"

	if err := r.db.Table("play_log as logs").
		Select(selectedFields).
		Joins("LEFT JOIN user as users ON users.member_code = logs.player").
		Where("logs.date = ?", statementDate).
		Group("users.id").
		Scan(&list).
		Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (r repo) CheckDailyPlayLogNew(statementDate string) (*model.CronPlayLogCheckReponse, error) {

	var record model.CronPlayLogCheckReponse

	// statementDate = "2023-10-25"
	// type CronPlayLogCheckReponse struct {
	// 	IsReady           bool  `json:"isReady"`
	// 	TotalSuccessCount int64 `json:"totalSuccessCount"`
	// 	TotalFailCount    int64 `json:"totalFailCount"`
	// }
	// SELECT SUM(IF(is_success = 1, 1, 0)) AS success_count, SUM(IF(is_success = 0, 1, 0)) AS error_count FROM api_status WHERE statement_date = '2023-10-21';
	// simplewinlose1
	// simplewinlose2
	// simplewinlose4
	// simplewinlose3
	// simplewinlose6
	// simplewinlose7
	// simplewinlose4

	successKeys := []string{"simplewinlose1", "simplewinlose2", "simplewinlose4", "simplewinlose3", "simplewinlose6", "simplewinlose7"}

	selectedFields := "SUM(IF(is_success = 1, 1, 0)) AS total_success_count, SUM(IF(is_success = 0, 1, 0)) AS total_fail_count"
	if err := r.db.Table("api_status as play_logs").
		Select(selectedFields).
		Where("play_logs.statement_date = ?", statementDate).
		Where("play_logs.path IN ?", successKeys).
		Take(&record).
		Error; err != nil {
		return nil, err
	}

	if record.TotalSuccessCount == int64(len(successKeys)) && record.TotalFailCount == 0 {
		record.IsReady = true
	}
	return &record, nil
}

func (r repo) GetWeeklyTotalUserPaylogList(statementDate string) ([]model.PlaylogTotalAmount, error) {

	var list []model.PlaylogTotalAmount

	ofDate, err := time.Parse("2006-01-02", statementDate)
	if err != nil {
		return nil, err
	}

	// Get date from Monday to Sunday that has this day in week
	var days []string
	monday := ofDate.AddDate(0, 0, -int(ofDate.Weekday()))
	if ofDate.Weekday() == time.Sunday {
		// Sunday is ZERO, Check ofDate from SAT-SUN(today) SAME as today as SAT(BEFORE SUN)
		monday = ofDate.AddDate(0, 0, -6)
	}
	for i := 0; i < 7; i++ {
		days = append(days, monday.AddDate(0, 0, i).Format("2006-01-02"))
	}

	selectedFields := "users.id as user_id, SUM(logs.win_lose_total) as total_loss_amount"
	selectedFields += ", SUM(logs.win_lose_sport) as total_loss_sport, SUM(logs.win_lose_casino) as total_loss_casino, SUM(logs.win_lose_game) as total_loss_game"
	selectedFields += ", SUM(logs.win_lose_lottery) as total_loss_lottery, SUM(logs.win_lose_p2p) as total_loss_p2p, SUM(logs.win_lose_financial) as total_loss_financial"
	// Use Turn
	selectedFields += ", SUM(logs.turn_total) as total_turn_amount"
	selectedFields += ", SUM(logs.turn_sport) as total_turn_sport, SUM(logs.turn_casino) as total_turn_casino, SUM(logs.turn_game) as total_turn_game"
	selectedFields += ", SUM(logs.turn_lottery) as total_turn_lottery, SUM(logs.turn_p2p) as total_turn_p2p, SUM(logs.turn_financial) as total_turn_financial"
	// Use ValidAmount
	selectedFields += ", SUM(logs.valid_amount_total) as total_valid_amount"
	selectedFields += ", SUM(logs.valid_amount_sport) as total_valid_amount_sport, SUM(logs.valid_amount_casino) as total_valid_amount_casino, SUM(logs.valid_amount_game) as total_valid_amount_game"
	selectedFields += ", SUM(logs.valid_amount_lottery) as total_valid_amount_lottery, SUM(logs.valid_amount_p2p) as total_valid_amount_p2p, SUM(logs.valid_amount_financial) as total_valid_amount_financial"

	if err := r.db.Table("play_log as logs").
		Select(selectedFields).
		Joins("LEFT JOIN user as users ON users.member_code = logs.player").
		Where("logs.date IN ?", days).
		Group("users.id").
		Scan(&list).
		Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (r repo) GetUserPlaylogSummaryByUserId(userId int64) (*model.UserPlaylogSummaryResponse, error) {

	var record model.UserPlaylogSummaryResponse

	selectedFields := "*"
	if err := r.db.Table("user_playlog_summary as tb_summary").
		Select(selectedFields).
		Where("tb_summary.user_id = ?", userId).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetUserPlaylogSummaryList(req model.UserPlaylogSummaryListRequest) ([]model.UserPlaylogSummaryResponse, int64, error) {

	var list []model.UserPlaylogSummaryResponse
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("user_playlog_summary as tb_summary")
	count = count.Select("tb_summary.id")
	if req.UserId != nil {
		count = count.Where("tb_summary.user_id = ?", req.UserId)
	}
	if err = count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "*"
		query := r.db.Table("user_playlog_summary as tb_summary")
		query = query.Select(selectedFields)
		if req.UserId != nil {
			query = query.Where("tb_summary.user_id = ?", req.UserId)
		}
		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}

	return list, total, nil
}

func (r repo) CreateUserPlaylogSummary(body model.UserPlaylogSummaryCreateBody) (*int64, error) {

	if body.PlaylogStartDate == "" {
		body.PlaylogStartDate = time.Now().Format("2006-01-02")
	}

	if err := r.db.Table("user_playlog_summary").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) UpdateUserPlaylogSummary(id int64, body model.UserPlaylogSummaryUpdateBody) error {

	// FORCED UPDATE
	body.UpdatedAt = time.Now()

	if err := r.db.Table("user_playlog_summary").Where("id = ?", id).Updates(body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetAgcPlaylogStatus(statementDate string) (*model.ReportPlayLogStatusResponse, error) {

	var record model.ReportPlayLogStatusResponse

	selectedFields := "tb_status.statement_date AS statement_date, tb_status.playlog_sport_status AS playlog_sport_status, tb_status.playlog_game_status AS playlog_game_status, tb_status.playlog_casino_status AS playlog_casino_status"
	selectedFields += ", tb_status.playlog_lottery_status AS playlog_lottery_status, tb_status.playlog_p2p_status AS playlog_p2p_status, tb_status.playlog_financial_status AS playlog_financial_status"
	selectedFields += ", tb_status.cut_daily_affiliate_status AS cut_daily_affiliate_status, tb_status.cut_daily_alliance_status AS cut_daily_alliance_status, tb_status.promotion_return_loss_status AS promotion_return_loss_status"
	selectedFields += ", tb_status.remark AS remark, tb_status.created_at AS created_at, tb_status.updated_at AS updated_at"
	if err := r.db.Table("play_log_status AS tb_status").
		Select(selectedFields).
		Where("tb_status.statement_date = ?", statementDate).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetReportPlayLogStatusResponse(req model.ReportPlayLogStatusRequest) ([]model.ReportPlayLogStatusResponse, int64, error) {

	var list []model.ReportPlayLogStatusResponse
	var total int64

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("play_log_status AS tb_status")
	count = count.Select("tb_status.statement_date")
	if err := count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_status.statement_date AS statement_date, tb_status.playlog_sport_status AS playlog_sport_status, tb_status.playlog_game_status AS playlog_game_status, tb_status.playlog_casino_status AS playlog_casino_status"
		selectedFields += ", tb_status.playlog_lottery_status AS playlog_lottery_status, tb_status.playlog_p2p_status AS playlog_p2p_status, tb_status.playlog_financial_status AS playlog_financial_status"
		selectedFields += ", tb_status.cut_daily_affiliate_status AS cut_daily_affiliate_status, tb_status.cut_daily_alliance_status AS cut_daily_alliance_status, tb_status.promotion_return_loss_status AS promotion_return_loss_status"
		selectedFields += ", tb_status.remark AS remark, tb_status.created_at AS created_at, tb_status.updated_at AS updated_at"
		query := r.db.Table("play_log_status AS tb_status")
		query = query.Select(selectedFields)
		query = query.Order("tb_status.statement_date DESC")
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}

	return list, total, nil
}

func (r repo) GetReportPlayLogResponse(req model.ReportPlayLogResponseRequest) ([]model.ReportPlayLogResponse, int64, error) {

	var list []model.ReportPlayLogResponse
	var total int64

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("user_playlog AS tb_playlog")
	count = count.Select("tb_playlog.id")
	count = count.Where("tb_playlog.statement_date = ?", req.StatementDate)
	count = count.Group("tb_playlog.user_id")
	if err := count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_playlog.user_id AS user_id"
		selectedFields += ", SUM(tb_playlog.turn_sport) AS turn_sport, SUM(tb_playlog.win_lose_sport) AS win_lose_sport, SUM(tb_playlog.valid_amount_sport) AS valid_amount_sport"
		selectedFields += ", SUM(tb_playlog.turn_casino) AS turn_casino, SUM(tb_playlog.win_lose_casino) AS win_lose_casino, SUM(tb_playlog.valid_amount_casino) AS valid_amount_casino"
		selectedFields += ", SUM(tb_playlog.turn_game) AS turn_game, SUM(tb_playlog.win_lose_game) AS win_lose_game, SUM(tb_playlog.valid_amount_game) AS valid_amount_game"
		selectedFields += ", SUM(tb_playlog.turn_lottery) AS turn_lottery, SUM(tb_playlog.win_lose_lottery) AS win_lose_lottery, SUM(tb_playlog.valid_amount_lottery) AS valid_amount_lottery"
		selectedFields += ", SUM(tb_playlog.turn_p2p) AS turn_p2p, SUM(tb_playlog.win_lose_p2p) AS win_lose_p2p, SUM(tb_playlog.valid_amount_p2p) AS valid_amount_p2p"
		selectedFields += ", SUM(tb_playlog.turn_financial) AS turn_financial, SUM(tb_playlog.win_lose_financial) AS win_lose_financial, SUM(tb_playlog.valid_amount_financial) AS valid_amount_financial"
		selectedFields += ", SUM(tb_playlog.turn_total) AS turn_total, SUM(tb_playlog.win_lose_total) AS win_lose_total, SUM(tb_playlog.valid_amount_total) AS valid_amount_total"

		query := r.db.Table("user_playlog AS tb_playlog")
		query = query.Select(selectedFields)
		query = query.Where("tb_playlog.statement_date = ?", req.StatementDate)
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		query = query.Group("tb_playlog.user_id")
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}

		// Deposit And withdraw slipUrl
		userIds := make(map[int64]int64)
		for _, v := range list {
			userIds[v.UserId] = v.UserId
		}
		if len(userIds) > 0 {
			maplist := make(map[int64]string, 0)
			var userList []model.User
			mapQuery := r.db.Table("user AS tb_user")
			mapQuery = mapQuery.Select("tb_user.id, tb_user.member_code")
			mapQuery = mapQuery.Where("tb_user.id IN ?", helper.MapIdsToInt64Array(userIds))
			mapQuery = mapQuery.Group("tb_user.id")
			if err := mapQuery.Scan(&userList).Error; err != nil {
				return nil, total, err
			}
			for _, v := range userList {
				if v.MemberCode != nil {
					maplist[v.Id] = *v.MemberCode
				}
			}
			// Append MemberCode and StatementDate
			for i, v := range list {
				if memberCode, ok := maplist[v.UserId]; ok {
					list[i].MemberCode = memberCode
				}
				list[i].StatementDate = req.StatementDate
			}
		}
	}

	return list, total, nil
}

func (r repo) GetReportPlayLogSummary(req model.ReportPlayLogResponseRequest) (*model.ReportPlayLogSummaryResponse, error) {

	var result model.ReportPlayLogSummaryResponse

	// SELECT //
	selectedFields := "SUM(tb_playlog.turn_sport) AS turn_sport, SUM(tb_playlog.win_lose_sport) AS win_lose_sport, SUM(tb_playlog.valid_amount_sport) AS valid_amount_sport"
	selectedFields += ", SUM(tb_playlog.turn_casino) AS turn_casino, SUM(tb_playlog.win_lose_casino) AS win_lose_casino, SUM(tb_playlog.valid_amount_casino) AS valid_amount_casino"
	selectedFields += ", SUM(tb_playlog.turn_game) AS turn_game, SUM(tb_playlog.win_lose_game) AS win_lose_game, SUM(tb_playlog.valid_amount_game) AS valid_amount_game"
	selectedFields += ", SUM(tb_playlog.turn_lottery) AS turn_lottery, SUM(tb_playlog.win_lose_lottery) AS win_lose_lottery, SUM(tb_playlog.valid_amount_lottery) AS valid_amount_lottery"
	selectedFields += ", SUM(tb_playlog.turn_p2p) AS turn_p2p, SUM(tb_playlog.win_lose_p2p) AS win_lose_p2p, SUM(tb_playlog.valid_amount_p2p) AS valid_amount_p2p"
	selectedFields += ", SUM(tb_playlog.turn_financial) AS turn_financial, SUM(tb_playlog.win_lose_financial) AS win_lose_financial, SUM(tb_playlog.valid_amount_financial) AS valid_amount_financial"
	selectedFields += ", SUM(tb_playlog.turn_total) AS turn_total, SUM(tb_playlog.win_lose_total) AS win_lose_total, SUM(tb_playlog.valid_amount_total) AS valid_amount_total"

	query := r.db.Table("user_playlog AS tb_playlog")
	query = query.Select(selectedFields)
	query = query.Where("tb_playlog.statement_date = ?", req.StatementDate)
	if err := query.
		Scan(&result).
		Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (r repo) GetAgentCtwCallbackPlayLogList(req model.AgentCallbackPlayLogListRequest) ([]model.AgentCtwCallbackPlayLogListResponse, int64, error) {

	var list []model.AgentCtwCallbackPlayLogListResponse
	var total int64

	count := r.db.Table("agent_ctw_callback AS tb_callback")
	count = count.Select("tb_callback.id")
	count = count.Where("tb_callback.is_success = ?", 1)
	if req.UserId != nil {
		count = count.Where("tb_callback.user_id = ?", req.UserId)
	}
	if req.FromDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.FromDate)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("tb_callback.created_at >= ?", startDateAtBkk)
	}
	if req.ToDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.ToDate)
		if err != nil {
			return list, 0, err
		}
		count = count.Where("tb_callback.created_at <= ?", endDateAtBkk)
	}
	if err := count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_callback.id AS id, tb_callback.user_id AS user_id, tb_callback.member_code AS member_code"
		selectedFields += ", tb_callback.payoff AS payoff, tb_callback.bet_amount AS bet_amount, tb_callback.winlose_amount AS winlose_amount"
		selectedFields += ", tb_callback.balance AS balance, tb_callback.before_balance AS before_balance, tb_callback.after_balance AS after_balance"
		selectedFields += ", tb_callback.transaction_id AS transaction_id, tb_callback.round_id AS round_id, tb_callback.game_id AS game_id"
		selectedFields += ", tb_callback.callback_reason AS callback_reason, tb_callback.created_at AS created_at"
		query := r.db.Table("agent_ctw_callback AS tb_callback")
		query = query.Select(selectedFields)
		query = query.Where("tb_callback.is_success = ?", 1)
		if req.UserId != nil {
			query = query.Where("tb_callback.user_id = ?", req.UserId)
		}
		if req.FromDate != "" {
			startDateAtBkk, err := r.ParseBodBkk(req.FromDate)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("tb_callback.created_at >= ?", startDateAtBkk)
		}
		if req.ToDate != "" {
			endDateAtBkk, err := r.ParseEodBkk(req.ToDate)
			if err != nil {
				return list, 0, err
			}
			query = query.Where("tb_callback.created_at <= ?", endDateAtBkk)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}

	return list, total, nil
}

func (r repo) GetAgentPgHardCallbackPlayLogList(req model.AgentCallbackPlayLogListRequest) ([]model.AgentPgHardCallbackPlayLogListResponse, int64, error) {

	var list []model.AgentPgHardCallbackPlayLogListResponse
	var total int64

	count := r.db.Table("agent_pg_hard_callback AS tb_callback")
	count = count.Select("tb_callback.id")
	count = count.Where("tb_callback.is_success = ?", 1)
	if req.UserId != nil {
		count = count.Where("tb_callback.user_id = ?", req.UserId)
	}
	if req.FromDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.FromDate)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("tb_callback.created_at >= ?", startDateAtBkk)
	}
	if req.ToDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.ToDate)
		if err != nil {
			return list, 0, err
		}
		count = count.Where("tb_callback.created_at <= ?", endDateAtBkk)
	}
	if err := count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_callback.id AS id, tb_callback.user_id AS user_id, tb_callback.member_code AS member_code"
		selectedFields += ", tb_callback.payoff AS payoff, tb_callback.bet_amount AS bet_amount, tb_callback.winlose_amount AS winlose_amount"
		selectedFields += ", tb_callback.balance AS balance, tb_callback.before_balance AS before_balance, tb_callback.after_balance AS after_balance"
		selectedFields += ", tb_callback.transaction_id AS transaction_id, tb_callback.round_id AS round_id, tb_callback.game_id AS game_id"
		selectedFields += ", tb_callback.game_string_id AS game_string_id, tb_callback.game_name AS game_name"
		selectedFields += ", tb_callback.remark AS remark, tb_callback.created_at AS created_at"
		query := r.db.Table("agent_pg_hard_callback AS tb_callback")
		query = query.Select(selectedFields)
		query = query.Where("tb_callback.is_success = ?", 1)
		if req.UserId != nil {
			query = query.Where("tb_callback.user_id = ?", req.UserId)
		}
		if req.FromDate != "" {
			startDateAtBkk, err := r.ParseBodBkk(req.FromDate)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("tb_callback.created_at >= ?", startDateAtBkk)
		}
		if req.ToDate != "" {
			endDateAtBkk, err := r.ParseEodBkk(req.ToDate)
			if err != nil {
				return list, 0, err
			}
			query = query.Where("tb_callback.created_at <= ?", endDateAtBkk)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}

	return list, total, nil
}

func (r repo) AgcWinloseReport(data model.AgcWinloseReportRequest) (*model.AgcWinloseReportResponse, error) {
	// log.Println("AgcWinloseReport req ------> ", helper.StructJson(data))

	url := "https://api-proxy.cbgame88.com/2all/report/api/winlose/full"

	// Marshal request body
	jsonBody, err := json.Marshal(data)
	if err != nil {
		log.Println("JSON Marshal error:", err)
		return nil, err
	}
	reqBody := bytes.NewBuffer(jsonBody)

	// Create HTTP request
	client := http.Client{}
	req, err := http.NewRequest("POST", url, reqBody)
	if err != nil {
		log.Println("NewRequest error:", err)
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")

	// Perform HTTP request
	resp, err := client.Do(req)
	if err != nil {
		log.Println("AgcWinloseReport HTTP error ------> ", err)
		return nil, err
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Println("ReadAll error:", err)
		return nil, err
	}

	// Unmarshal into your response struct
	var result model.AgcWinloseReportResponse
	if err := json.Unmarshal(body, &result); err != nil {
		log.Println("Unmarshal error:", err)
		return nil, err
	}

	log.Println("AgcWinloseReport response ------> ", helper.StructJson(result))

	return &result, nil
}

func (r repo) AgcTicketReport(data model.AgcTicketsFetchPagingRequest) (*model.AgcTicketReportResponse, error) {

	// log.Println("AgcTicketReport req ------> ", helper.StructJson(data))

	url := "https://api-proxy.cbgame88.com/2all/report/api/tickets/fetchpaging"

	// Marshal request body
	jsonBody, err := json.Marshal(data)
	if err != nil {
		log.Println("JSON Marshal error:", err)
		return nil, err
	}
	reqBody := bytes.NewBuffer(jsonBody)

	// Create HTTP request
	client := http.Client{}
	req, err := http.NewRequest("POST", url, reqBody)
	if err != nil {
		log.Println("NewRequest error:", err)
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")

	// Perform HTTP request
	resp, err := client.Do(req)
	if err != nil {
		log.Println("AgcTicketReport HTTP error ------> ", err)
		return nil, err
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Println("ReadAll error:", err)
		return nil, err
	}

	// Unmarshal into your response struct
	var result model.AgcTicketReportResponse
	if err := json.Unmarshal(body, &result); err != nil {
		log.Println("Unmarshal error:", err)
		return nil, err
	}

	log.Println("AgcTicketReport response ------> ", helper.StructJson(result))

	return &result, nil
}
