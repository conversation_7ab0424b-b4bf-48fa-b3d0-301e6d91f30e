package v2

import (
	"cybergame-api/model"
	"errors"
	"time"

	"gorm.io/gorm"
)

func NewDateTimeRepositoryV2(db *gorm.DB) DateTimeRepositoryV2 {
	return &repo{db}
}

type DateTimeRepositoryV2 interface {
	ParseBomUTC(input string) (*time.Time, error)
	ParseEomUTC(input string) (*time.Time, error)

	GetShopPurchaseChannelList() ([]model.SelectOptions, error)
}

func (r repo) ParseBodBkk(input string) (*time.Time, error) {

	if input == "" || len(input) != 10 {
		return nil, errors.New("INVALID_DATE_STRING_INPUT")
	}

	startDate, err := time.Parse("2006-01-02", input)
	if err != nil {
		return nil, err
	}
	// Set the times to Bkk
	result := startDate.UTC()
	//result = result.Add(time.Hour * -7)
	return &result, nil
}

func (r repo) ParseEodBkk(input string) (*time.Time, error) {

	if input == "" || len(input) != 10 {
		return nil, errors.New("INVALID_DATE_STRING_INPUT")
	}

	// trim input to YYYY-MM-DD
	input = input[:10] + " 23:59:59"

	endDate, err := time.Parse("2006-01-02 15:04:05", input)
	if err != nil {
		return nil, err
	}
	// Set the times to Bkk
	result := endDate.UTC()
	//result = result.Add(time.Hour * -7)
	return &result, nil
}

func (r repo) ParseBotBkk(input string) (*time.Time, error) {

	if input == "" || len(input) != 19 {
		return nil, errors.New("INVALID_TIME_STRING_INPUT")
	}

	startTime, err := time.Parse("2006-01-02 15:04:05", input)
	if err != nil {
		return nil, err
	}
	// Set the times to Bkk
	result := startTime.UTC()
	//result = result.Add(time.Hour * -7)
	return &result, nil
}

func (r repo) ParseEotBkk(input string) (*time.Time, error) {

	if input == "" || len(input) != 19 {
		return nil, errors.New("INVALID_TIME_STRING_INPUT")
	}

	// trim input to YYYY-MM-DD
	input = input[:10] + " 23:59:59"

	endTime, err := time.Parse("2006-01-02 15:04:05", input)
	if err != nil {
		return nil, err
	}
	// Set the times to Bkk
	result := endTime.UTC()
	//result = result.Add(time.Hour * -7)
	return &result, nil
}

func (r repo) ParseBodUTC(input string) (*time.Time, error) {

	if input == "" || len(input) != 10 {
		return nil, errors.New("INVALID_DATE_STRING_INPUT")
	}

	startDate, err := time.Parse("2006-01-02", input)
	if err != nil {
		return nil, err
	}
	// Set the times to UTC
	result := startDate.UTC()
	return &result, nil
}

func (r repo) ParseEodUTC(input string) (*time.Time, error) {

	if input == "" || len(input) != 10 {
		return nil, errors.New("INVALID_DATE_STRING_INPUT")
	}

	// trim input to YYYY-MM-DD
	input = input[:10] + " 23:59:59"

	endDate, err := time.Parse("2006-01-02 15:04:05", input)
	if err != nil {
		return nil, err
	}
	// Set the times to UTC
	result := endDate.UTC()
	return &result, nil
}

func (r repo) ParseBomUTC(input string) (*time.Time, error) {

	if input == "" || len(input) != 10 {
		return nil, errors.New("INVALID_DATE_STRING_INPUT")
	}

	startDate, err := time.Parse("2006-01-02", input)
	if err != nil {
		return nil, err
	}

	currentYear, currentMonth, _ := startDate.Date()
	currentLocation := startDate.Location()

	firstOfMonth := time.Date(currentYear, currentMonth, 1, 0, 0, 0, 0, currentLocation)

	// Set the times to UTC
	result := firstOfMonth.UTC()
	return &result, nil
}

func (r repo) ParseEomUTC(input string) (*time.Time, error) {

	if input == "" || len(input) != 10 {
		return nil, errors.New("INVALID_DATE_STRING_INPUT")
	}

	// trim input to YYYY-MM-DD
	input = input[:10] + " 23:59:59"

	endDate, err := time.Parse("2006-01-02 15:04:05", input)
	if err != nil {
		return nil, err
	}

	currentYear, currentMonth, _ := endDate.Date()
	currentLocation := endDate.Location()

	firstOfMonth := time.Date(currentYear, currentMonth, 1, 0, 0, 0, 0, currentLocation)
	lastOfMonth := firstOfMonth.AddDate(0, 1, -1)

	// Set the times to UTC
	result := lastOfMonth.UTC()
	return &result, nil
}

func (r repo) GetShopPurchaseChannelList() ([]model.SelectOptions, error) {

	options := []model.SelectOptions{}

	// SELECT //
	selectedFields := "id as id, name as label"
	var sql = r.db.Table("cart_purchase_channel").Select(selectedFields)
	if err := sql.
		Scan(&options).
		Error; err != nil {
		return nil, err
	}
	return options, nil
}
