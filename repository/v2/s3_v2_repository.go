package v2

import (
	"bytes"
	model "cybergame-api/model/v2"
	"errors"
	"fmt"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	"gorm.io/gorm"
	"io"
	"log"
	"net/http"
	"os"
)

type S3V2Repository interface {
	UploadImageToS3(pathUpload string, fileReader io.Reader) (*model.ImageUploadResponse, error)
}

func NewS3V2Repository(db *gorm.DB) S3V2Repository {
	return &repo{db}
}

func (r *repo) UploadImageToS3(pathUpload string, fileReader io.Reader) (*model.ImageUploadResponse, error) {
	bucketName := os.Getenv("AWS_S3_BUCKET_NAME")

	randomFilename := generateRandomString()
	fileKey := fmt.Sprintf("%s%s", pathUpload, randomFilename)

	awsRegion := os.Getenv("AWS_S3_BUCKET_REGION")
	awsAccessKey := os.Getenv("AWS_S3_BUCKET_ACCESS_KEY_ID")
	awsSecretKey := os.Getenv("AWS_S3_BUCKET_SECRET_ACCESS_KEY")

	sess, err := session.NewSession(&aws.Config{
		Region:      aws.String(awsRegion),
		Credentials: credentials.NewStaticCredentials(awsAccessKey, awsSecretKey, ""),
	})
	if err != nil {
		log.Println("UploadImageToS3.NewSession.Error:", err)
		return nil, errors.New("failed to create AWS session")
	}

	s3Client := s3.New(sess)

	body := &bytes.Buffer{}
	content, err := io.Copy(body, fileReader)
	if err != nil {
		log.Println("UploadImageToS3.Copy.Error:", err)
		return nil, err
	}

	input := &s3.PutObjectInput{
		Bucket:        aws.String(bucketName),
		Key:           aws.String(fileKey),
		Body:          bytes.NewReader(body.Bytes()),
		ContentLength: aws.Int64(content),
		ContentType:   aws.String(http.DetectContentType(body.Bytes())),
		//ACL:           aws.String("public-read"),
	}

	_, err = s3Client.PutObject(input)
	if err != nil {
		log.Println("UploadImageToS3.PutObject.Error:", err)
		return nil, fmt.Errorf("failed to upload file to S3: %w", err)
	}

	fileUrl := ""
	cloudFrontUrl := os.Getenv("AWS_S3_CLOUDFRONT_URL")
	if cloudFrontUrl == "" {
		// old
		// https://cbgame.s3.ap-southeast-1.amazonaws.com/cbgame/db_brobet/bank-account/2025213_1338732
		//fileUrl = fmt.Sprintf("https://%s.s3.%s.amazonaws.com/%s", bucketName, awsRegion, fileKey)

		// Hardcode.
		// https://cdn.tidtech.net/cbgame/db_brobet/bank-account/2025213_1338732
		fileUrl = fmt.Sprintf("https://cdn.tidtech.net/%s", fileKey)
	} else {
		// ย้ายมาเป็น cloudfront 2025-06-05
		// https://cdn.tidtech.net/cbgame/db_brobet/bank-account/2025213_1338732
		fileUrl = fmt.Sprintf("%s/%s", cloudFrontUrl, fileKey)
	}
	response := &model.ImageUploadResponse{
		ImageUrl: fileUrl,
	}

	return response, nil
}
