package v2

import (
	modelV1 "cybergame-api/model"
	modelV2 "cybergame-api/model/v2"
	"errors"
	"gorm.io/gorm"
	"time"
)

type ActivityDailyAutoCheckinV2Repository interface {
	CheckUserAlreadyCheckin(userId int64) (bool, error)
	CreateDailyAutoCheckinUserV2(body modelV2.CreateUserDailyAutoCheckinV2Request) error
	UpdateDailyAutoCheckinUserV2(body modelV2.UpdateUserDailyAutoCheckinV2Request) error
	GetUserDailyAutoCheckinV2NextNoNumber(user int64) (*modelV2.DailyAutoCheckinV2NextNo, *modelV2.GetDailyAutoCheckinV2Total, error)
	GetUserDailyAutoCheckinReadyClaim(userId int64) (*modelV2.UserDailyAutoCheckinReadyClaimResponse, error)
	GetUserDailyAutoCheckByIdV2(userId int64, id int64) (*modelV2.UserDailyAutoCheckinReadyClaimResponse, *modelV2.GetDailyAutoCheckinV2Total, error)
	GetReportDailyAutoCheckinV2(req modelV2.GetReportDailyAutoCheckinV2Request) ([]modelV2.GetReportDailyAutoCheckinV2Response, int64, error)
	GetDateFromDateType(req modelV2.DateTypeResponse) (*modelV2.DateTypeResponse, error)
}

func NewActivityDailyAutoCheckinV2Repository(db *gorm.DB) ActivityDailyAutoCheckinV2Repository {
	return &repo{db}
}

func (r *repo) CheckUserAlreadyCheckin(userId int64) (bool, error) {
	var result int64
	today := time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02")

	query := r.db.Table("activity_daily_auto_checkin_user_v2")
	query = query.Select("id")
	query = query.Where("user_id = ?", userId)

	startDateAtBkk, err := r.ParseBodBkk(today)
	if err != nil {
		return false, err
	}
	query = query.Where("checkin_at >= ? ", startDateAtBkk)

	EndDateAtBkk, err := r.ParseEodBkk(today)
	if err != nil {
		return false, err
	}
	query = query.Where("checkin_at <= ? ", EndDateAtBkk)

	query = query.Count(&result)
	if err := query.Error; err != nil {
		return false, err
	}

	return result > 0, nil
}

func (r *repo) CreateDailyAutoCheckinUserV2(body modelV2.CreateUserDailyAutoCheckinV2Request) error {
	query := r.db.Table("activity_daily_auto_checkin_user_v2")
	query = query.Create(&body)
	if err := query.Error; err != nil {
		return err
	}
	return nil
}

func (r *repo) UpdateDailyAutoCheckinUserV2(body modelV2.UpdateUserDailyAutoCheckinV2Request) error {
	query := r.db.Table("activity_daily_auto_checkin_user_v2")
	query = query.Where("id = ?", body.Id)
	query = query.Updates(body)
	if err := query.Error; err != nil {
		return err
	}
	return nil
}

func (r *repo) GetUserDailyAutoCheckinV2NextNoNumber(user int64) (*modelV2.DailyAutoCheckinV2NextNo, *modelV2.GetDailyAutoCheckinV2Total, error) {
	// Get the current change count time
	var getDailyAutoCheckinV2Total modelV2.GetDailyAutoCheckinV2Total
	selectedFields := "activity_daily_v2_total.total_able_revice_no, activity_daily_v2_total.change_count_time"
	selectedFields += ", activity_daily_v2_total.condition_amount as condition_amount"
	selectedFields += ", activity_daily_v2_total_condition.id as activity_daily_v2_total_condition_id, activity_daily_v2_total_condition.label_th as activity_daily_v2_total_condition_name"

	query := r.db.Table("activity_daily_v2_total")
	query = query.Select(selectedFields)
	query = query.Joins("LEFT JOIN activity_daily_v2_total_condition ON activity_daily_v2_total_condition.id = activity_daily_v2_total.activity_daily_v2_total_condition_id")
	query = query.Take(&getDailyAutoCheckinV2Total)
	if err := query.Error; err != nil {
		return nil, nil, err
	}

	// check the latest noNumber from the user
	type LatestNoNumber struct {
		Id                       int64   `json:"id"`
		CurrentNoNumber          int64   `json:"currentNoNumber"`
		AccumulatedCreditBalance float64 `json:"creditAmount"`
		ChangeCountTime          int64   `json:"changeCountTime"`
	}

	var latestNoNumber LatestNoNumber
	selectedFields2 := "id, current_no_number, accumulated_credit_balance, change_count_time"

	query2 := r.db.Table("activity_daily_auto_checkin_user_v2")
	query2 = query2.Select(selectedFields2)
	query2 = query2.Where("user_id = ?", user)
	query2 = query2.Where("is_completed = ?", false)
	query2 = query2.Where("change_count_time = ?", getDailyAutoCheckinV2Total.ChangeCountTime)
	query2 = query2.Order("created_at DESC")
	query2 = query2.Take(&latestNoNumber)
	if err := query2.Error; err != nil {
		latestNoNumber.Id = 0
		latestNoNumber.CurrentNoNumber = 0
		latestNoNumber.AccumulatedCreditBalance = 0
		latestNoNumber.ChangeCountTime = getDailyAutoCheckinV2Total.ChangeCountTime
	}

	var nextNoNumber modelV2.DailyAutoCheckinV2NextNo
	//if getDailyAutoCheckinV2Total.TotalAbleReviceNo <= latestNoNumber.CurrentNoNumber {
	if getDailyAutoCheckinV2Total.TotalAbleReviceNo == latestNoNumber.CurrentNoNumber {
		// get the first noNumber by the current change count time
		selectedFields3 := "id, no_number, credit_amount, change_count_time"

		query3 := r.db.Table("activity_daily_v2")
		query3 = query3.Select(selectedFields3)
		query3 = query3.Where("change_count_time = ?", getDailyAutoCheckinV2Total.ChangeCountTime)
		query3 = query3.Where("no_number = ?", 1)
		query3 = query3.Take(&nextNoNumber)
		if err := query3.Error; err != nil {
			return nil, nil, err
		}
	} else {
		// find the next noNumber by the latest noNumber+1
		selectedFields3 := "id, no_number, credit_amount, change_count_time"

		query3 := r.db.Table("activity_daily_v2")
		query3 = query3.Select(selectedFields3)
		query3 = query3.Where("change_count_time = ?", latestNoNumber.ChangeCountTime)
		query3 = query3.Where("no_number = ?", latestNoNumber.CurrentNoNumber+1)
		query3 = query3.Take(&nextNoNumber)
		if err := query3.Error; err != nil {
			return nil, nil, err
		}

		//nextNoNumber.DailyId = latestNoNumber.Id
		//nextNoNumber.CreditAmount = nextNoNumber.CreditAmount + latestNoNumber.AccumulatedCreditBalance
	}

	nextNoNumber.DailyId = latestNoNumber.Id
	nextNoNumber.CreditAmount = nextNoNumber.CreditAmount + latestNoNumber.AccumulatedCreditBalance

	return &nextNoNumber, &getDailyAutoCheckinV2Total, nil
}

func (r *repo) GetUserDailyAutoCheckinReadyClaim(userId int64) (*modelV2.UserDailyAutoCheckinReadyClaimResponse, error) {
	type GetDailyAutoCheckinV2Total struct {
		Id              int64 `json:"id"`
		ChangeCountTime int64 `json:"changeCountTime"`
	}
	var getDailyAutoCheckinV2Total GetDailyAutoCheckinV2Total
	query := r.db.Table("activity_daily_v2_total")
	query = query.Select("activity_daily_v2_total.change_count_time")
	query = query.Take(&getDailyAutoCheckinV2Total)
	if err := query.Error; err != nil {
		return nil, err
	}

	var result []modelV2.UserDailyAutoCheckinReadyClaimResponse
	selectedFields2 := "id, activity_daily_v2_id, current_no_number, change_count_time, accumulated_credit_balance"
	selectedFields2 += ", accumulated_credit_received, is_completed, is_claimed, checkin_at, created_at, updated_at"

	query1 := r.db.Table("activity_daily_auto_checkin_user_v2")
	query1 = query1.Select(selectedFields2)
	query1 = query1.Where("user_id = ?", userId)
	query1 = query1.Where("change_count_time = ?", getDailyAutoCheckinV2Total.ChangeCountTime)
	//query1 = query1.Where("is_claimed = ?", false)
	query1 = query1.Order("id DESC")
	query1 = query1.Limit(1)
	query1 = query1.Scan(&result)
	if err := query1.Error; err != nil {
		return nil, err
	}

	return &result[0], nil
}

func (r *repo) GetUserDailyAutoCheckByIdV2(userId int64, id int64) (*modelV2.UserDailyAutoCheckinReadyClaimResponse, *modelV2.GetDailyAutoCheckinV2Total, error) {
	var result modelV2.UserDailyAutoCheckinReadyClaimResponse
	selectedFields1 := "id, activity_daily_v2_id, current_no_number, change_count_time, accumulated_credit_balance, accumulated_credit_received, is_completed, is_claimed, checkin_at, created_at, updated_at"
	query1 := r.db.Table("activity_daily_auto_checkin_user_v2")
	query1 = query1.Select(selectedFields1)
	query1 = query1.Where("user_id = ?", userId)
	query1 = query1.Where("id = ?", id)
	//query1 = query1.Where("is_claimed = ?", false)
	query1 = query1.Take(&result)
	if query1.Error != nil {
		if errors.Is(query1.Error, gorm.ErrRecordNotFound) {
			return nil, nil, nil
		} else {
			return nil, nil, query1.Error
		}
	}

	var getDailyAutoCheckinV2Total modelV2.GetDailyAutoCheckinV2Total
	selectedFields2 := "activity_daily_v2_total.total_able_revice_no"
	selectedFields2 += ", activity_daily_v2_total.condition_amount as condition_amount"
	selectedFields2 += ", activity_daily_v2_total_condition.id as activity_daily_v2_total_condition_id, activity_daily_v2_total_condition.label_th as activity_daily_v2_total_condition_name"

	query2 := r.db.Table("activity_daily_v2_total")
	query2 = query2.Select(selectedFields2)
	query2 = query2.Joins("LEFT JOIN activity_daily_v2_total_condition ON activity_daily_v2_total_condition.id = activity_daily_v2_total.activity_daily_v2_total_condition_id")
	query2 = query2.Where("change_count_time = ?", result.ChangeCountTime)
	query2 = query2.Take(&getDailyAutoCheckinV2Total)
	if err := query2.Error; err != nil {
		return nil, nil, err
	}

	return &result, &getDailyAutoCheckinV2Total, nil
}

func (r *repo) GetReportDailyAutoCheckinV2(req modelV2.GetReportDailyAutoCheckinV2Request) ([]modelV2.GetReportDailyAutoCheckinV2Response, int64, error) {

	var list []modelV2.GetReportDailyAutoCheckinV2Response
	var total int64
	var err error

	dateType, err := r.GetDateFromDateType(modelV2.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return list, 0, err
	}

	count := r.db.Table("turnover_statement as statements")
	count = count.Select("statements.id")
	count = count.Joins("LEFT JOIN user ON user.id = statements.user_id")
	count = count.Where("statements.type_id = ?", modelV1.TURN_BONUS_ACTIVITY_DAILY_V2)

	if req.MemberCode != "" {
		count = count.Where("user_id.member_code = ?", req.MemberCode)
	}

	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return list, 0, err
		}
		count = count.Where("statements.created_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return list, 0, err
		}
		count = count.Where("statements.created_at <=  ?", endDateAtBkk)
	}
	if err = count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "statements.id as id, statements.user_id as user_id, statements.type_id as type_id, statements.ref_type_id as ref_type_id, statements.status_id as status_id, statements.start_turn_amount as start_turn_amount"
		selectedFields += ", statements.promotion_name as description, statements.bonus_amount as bonus_amount"
		selectedFields += ", statements.start_turn_at as start_turn_at, statements.total_turn_amount as total_turn_amount, statements.end_turn_at as end_turn_at, statements.created_at as created_at, statements.updated_at as updated_at"
		selectedFields += ", types.name as type_name, statuses.name as status_name"
		selectedFields += ", user.member_code as member_code"
		selectedFields += ", user.fullname as fullname, user.username as username"
		selectedFields += ", activity_daily_user.tidturn_percent as tidturn_percent"
		selectedFields += ", activity_daily_user.amount_condition as amount_condition"

		query := r.db.Table("turnover_statement as statements")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN activity_daily_user ON activity_daily_user.id = statements.ref_type_id")

		query = query.Joins("LEFT JOIN turnover_statement_type as types ON types.id = statements.type_id")
		query = query.Joins("LEFT JOIN turnover_statement_status as statuses ON statuses.id = statements.status_id")
		query = query.Joins("LEFT JOIN user ON user.id = statements.user_id")
		query = query.Where("statements.type_id = ?", modelV1.TURN_BONUS_ACTIVITY_DAILY_V2)
		if req.MemberCode != "" {
			query = query.Where("user_id.member_code = ?", req.MemberCode)
		}

		if dateType.DateFrom != "" {
			startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
			if err != nil {
				return list, 0, err
			}
			query = query.Where("statements.created_at >= ? ", startDateAtBkk)
		}
		if dateType.DateTo != "" {
			endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
			if err != nil {
				return list, 0, err
			}
			query = query.Where("statements.created_at <=  ?", endDateAtBkk)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r *repo) GetDateFromDateType(req modelV2.DateTypeResponse) (*modelV2.DateTypeResponse, error) {

	var result modelV2.DateTypeResponse

	// Query Date today, yesterday, last_week, last_month
	now := time.Now().UTC()
	if req.DateType == "today" {
		req.DateFrom = now.Format("2006-01-02")
		req.DateTo = now.Format("2006-01-02")
	} else if req.DateType == "yesterday" {
		req.DateFrom = now.AddDate(0, 0, -1).Format("2006-01-02")
		req.DateTo = now.AddDate(0, 0, -1).Format("2006-01-02")
	} else if req.DateType == "last_week" {
		// -6 not -7 because today is included
		req.DateFrom = now.AddDate(0, 0, -6).Format("2006-01-02")
		req.DateTo = now.Format("2006-01-02")
	} else if req.DateType == "last_month" {
		// 30 day not exactly 1 month
		// -29 not -30 because today is included
		req.DateFrom = now.AddDate(0, 0, -29).Format("2006-01-02")
		req.DateTo = now.Format("2006-01-02")
	} else if req.DateType == "this_month" {
		// exactly this month
		req.DateFrom = now.Format("2006-01") + "-01"
		req.DateTo = now.AddDate(0, 1, -1).Format("2006-01-02")
	}

	// use Bangkok timezone as new date
	// startDateAtBkk, err := r.ParseBodBkk(req.DateFrom)
	// if err != nil {
	// 	return nil, err
	// }
	// endDateAtBkk, err := r.ParseEodBkk(req.DateTo)
	// if err != nil {
	// 	return nil, err
	// }

	result.DateType = req.DateType
	result.DateFrom = req.DateFrom
	result.DateTo = req.DateTo

	return &result, nil
}

func (r *repo) getMemberByIdV2(id int64) (*modelV1.Member, error) {

	var record modelV1.Member

	selectedFields := "users.id, users.member_code, users.username, users.phone, users.fullname, users.credit, users.bank_account, users.bank_id as bank_id"
	selectedFields += ", users.ref_by, users.user_type_id, user_type.name as user_type_name, tb_bank.name as bank_name"
	if err := r.db.Table("user as users").
		Select(selectedFields).
		Joins("LEFT JOIN user_type ON user_type.id = users.user_type_id").
		Joins("LEFT JOIN bank as tb_bank ON tb_bank.id = users.bank_id").
		Where("users.id = ?", id).
		Where("users.deleted_at IS NULL").
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}
