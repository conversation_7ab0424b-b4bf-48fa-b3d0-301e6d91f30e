package v2

import (
	modelV2 "cybergame-api/model/v2"
	"fmt"
	"gorm.io/gorm"
	"time"
)

func NewUserQuestPlayLogV2Repository(db *gorm.DB) UserQuestPlayLogRepository {
	return &repo{db}
}

type UserQuestPlayLogRepository interface {
	CreateUserTodayPlayLogBulkV2(bodyList map[string]modelV2.UserQuestTodayPlayLogCreateBody, memberList []string) error
	GetUserListByMemberListV2(memberList []string) ([]modelV2.UserMemberList, error)
	UpdateUserQuestTodayPlayLogV2(updateBody modelV2.UserQuestTodayPlayLogUpdateBody) error
	GetQuestTodayPlayLogKeyListV2(dailyKeyList []string) ([]modelV2.UserQuestTodayPlayLogV2Response, int64, error)

	// Internal Quest Check
	UserQuestQueryPlayLog(userId int64, req modelV2.UserQuestPlayLogQueryRequest) (bool, error)
	UserQuestTodayCheckin(userId int64) (bool, error)
	UserQuestTodayDeposit(userId int64, req modelV2.UserQuestPlayLogQueryRequest) (bool, error)
}

func (r *repo) CreateUserTodayPlayLogBulkV2(bodyList map[string]modelV2.UserQuestTodayPlayLogCreateBody, memberList []string) error {
	var createList []modelV2.UserQuestTodayPlayLogCreateBody

	userMap := make(map[string]modelV2.UserMemberList, 0)
	UserList, err := r.GetUserListByMemberListV2(memberList)
	if err != nil {
		return err
	}
	for _, v := range UserList {
		userMap[v.MemberCode] = v
	}

	for _, v := range bodyList {
		if _, ok := userMap[v.MemberCode]; ok {
			v.UserId = userMap[v.MemberCode].Id
		}
		// allow user 0
		createList = append(createList, v)
	}
	if len(createList) > 0 {
		if err := r.db.Table("user_quest_today_playlog_v2").Create(createList).Error; err != nil {
			return err
		}
	}
	return nil
}

func (r *repo) GetUserListByMemberListV2(memberList []string) ([]modelV2.UserMemberList, error) {
	var result []modelV2.UserMemberList

	if err := r.db.Table("user").
		Select("id, member_code").
		Where("member_code IN ?", memberList).
		Where("user.deleted_at IS NULL").
		Scan(&result).
		Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (r *repo) UpdateUserQuestTodayPlayLogV2(updateBody modelV2.UserQuestTodayPlayLogUpdateBody) error {

	if err := r.db.Table("user_quest_today_playlog_v2").Updates(updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r *repo) GetQuestTodayPlayLogKeyListV2(dailyKeyList []string) ([]modelV2.UserQuestTodayPlayLogV2Response, int64, error) {
	var list []modelV2.UserQuestTodayPlayLogV2Response
	var total int64

	selected_fields := "id, user_id, statement_date, daily_key"
	selected_fields += ", turn_sport, valid_amount_sport, win_lose_sport, turn_casino, valid_amount_casino, win_lose_casino"
	selected_fields += ", turn_game, valid_amount_game, win_lose_game, turn_lottery, valid_amount_lottery, win_lose_lottery"
	selected_fields += ", turn_p2p, valid_amount_p2p, win_lose_p2p, turn_financial, valid_amount_financial, win_lose_financial"
	selected_fields += ", turn_total, win_lose_total, valid_amount_total, created_at"

	// SELECT //
	query := r.db.Table("user_quest_today_playlog_v2 as tb_playlog")
	query = query.Select(selected_fields)
	query = query.Where("tb_playlog.daily_key IN ?", dailyKeyList)
	if err := query.
		Scan(&list).
		Error; err != nil {
		return nil, total, err
	}
	return list, total, nil
}

func (r *repo) UserQuestQueryPlayLog(userId int64, req modelV2.UserQuestPlayLogQueryRequest) (bool, error) {

	today := time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02")

	startDateAtBkk, err := r.ParseBodBkk(today)
	if err != nil {
		return false, err
	}

	endDateAtBkk, err := r.ParseEodBkk(today)
	if err != nil {
		return false, err
	}

	var conditionPos string
	var conditionNeg string
	switch req.QuestConditionId {
	case modelV2.MIN_MIMUM_CONDITION:
		conditionPos = ">= ?"
		conditionNeg = "<= ?"
	case modelV2.EQUAL_CONDITION:
		conditionPos = "> ?"
		conditionNeg = "< ?"
	default:
		conditionPos = ""
		conditionNeg = ""
	}

	var fieldName string
	switch req.QuestTypeId {
	case modelV2.SPORT_TYPE:
		fieldName = "win_lose_sport"
	case modelV2.CASINO_TYPE:
		fieldName = "win_lose_casino"
	case modelV2.GAME_TYPE:
		fieldName = "win_lose_game"
	case modelV2.LOTTERY_TYPE:
		fieldName = "win_lose_lottery"
	case modelV2.P2P_TYPE:
		fieldName = "win_lose_p2p"
	default:
		return false, nil // No valid type provided
	}

	var count int64
	query := r.db.Table("user_quest_today_playlog_v2")
	query = query.Select("id")
	query = query.Where("user_id = ?", userId)
	query = query.Where("statement_date BETWEEN ? AND ?", startDateAtBkk, endDateAtBkk)

	if conditionPos != "" && conditionNeg != "" {
		query = query.Where(fmt.Sprintf("%s %s OR %s %s", fieldName, conditionPos, fieldName, conditionNeg), req.QuestConditionAmount, -req.QuestConditionAmount)
	}

	query = query.Count(&count)
	if err := query.Error; err != nil {
		return false, err
	}

	return count > 0, nil
}

func (r *repo) UserQuestTodayCheckin(userId int64) (bool, error) {
	// คำนวณวันที่ใน timezone Bangkok
	today := time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02")

	// แปลงวันที่เป็นเวลาเริ่มต้นและสิ้นสุดของวันใน Bangkok
	start, err := r.ParseBodBkk(today)
	if err != nil {
		return false, err
	}
	end, err := r.ParseEodBkk(today)
	if err != nil {
		return false, err
	}

	// นับจำนวน check-in ของ user ในวันนี้
	var count int64
	err = r.db.Table("activity_daily_auto_checkin_user_v2").
		Select("id").
		Where("user_id = ? AND checkin_at BETWEEN ? AND ?", userId, start, end).
		Count(&count).Error

	return count > 0, err
}

func (r *repo) UserQuestTodayDeposit(userId int64, req modelV2.UserQuestPlayLogQueryRequest) (bool, error) {
	today := time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02")

	startDateAtBkk, err := r.ParseBodBkk(today)
	if err != nil {
		return false, err
	}

	endDateAtBkk, err := r.ParseEodBkk(today)
	if err != nil {
		return false, err
	}

	var total float64
	query := r.db.Table("user_transaction")
	query = query.Select("SUM(credit_amount)")
	query = query.Where("user_id = ?", userId)
	query = query.Where("created_at BETWEEN ? AND ?", startDateAtBkk, endDateAtBkk)
	query = query.Where("type_id = ?", 1)
	query = query.Scan(&total)
	if err := query.Error; err != nil {
		return false, err
	}

	var result bool

	if total > 0 {
		switch req.QuestConditionId {
		case modelV2.MIN_MIMUM_CONDITION:
			if total >= req.QuestConditionAmount {
				result = true
			}
		case modelV2.EQUAL_CONDITION:
			if total >= req.QuestConditionAmount {
				result = true
			}
		default:
			result = true
		}
	} else {
		result = false
	}

	return result, nil

	//today := time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02")
	//
	//startDateAtBkk, err := r.ParseBodBkk(today)
	//if err != nil {
	//	return false, err
	//}
	//
	//endDateAtBkk, err := r.ParseEodBkk(today)
	//if err != nil {
	//	return false, err
	//}
	//
	//var condition string
	//switch req.QuestConditionId {
	//case modelV2.MIN_MIMUM_CONDITION:
	//	condition = ">= ?"
	//case modelV2.EQUAL_CONDITION:
	//	condition = "> ?"
	//default:
	//	condition = ""
	//}
	//
	//var count int64
	//query := r.db.Table("user_transaction")
	//query = query.Select("id")
	//query = query.Where("user_id = ?", userId)
	//query = query.Where("created_at BETWEEN ? AND ?", startDateAtBkk, endDateAtBkk)
	//
	//if condition != "" {
	//	query = query.Where(fmt.Sprintf("type_id = ? AND  credit_amount %s", condition), 1, req.QuestConditionAmount)
	//}
	//
	//query = query.Count(&count)
	//if err := query.Error; err != nil {
	//	return false, err
	//}
	//
	//return count > 0, nil
}
