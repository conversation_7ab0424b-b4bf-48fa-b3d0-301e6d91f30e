package v2

import (
	"cybergame-api/helper"
	modelV2 "cybergame-api/model/v2"
	"encoding/json"
	"fmt"
	"gorm.io/gorm"
	"log"
	"os"
)

func NewAgentConnectV2Repository(db *gorm.DB) AgentConnectV2Repository {
	return &repo{db}
}

type AgentConnectV2Repository interface {
	AgcSimpleWinLoseV2(data modelV2.AgcSimpleWinLoseV2) (*modelV2.AgcSimpleWinLoseV2Response, error)
	GetAgentPgHardCallback(req modelV2.AgentPgHardCallbackSummaryV2Request) ([]modelV2.AgentPgHardCallbackSummaryV2, error)
	GetAgentCtwCallbackV2(req modelV2.AgentCtwCallbackSummaryV2Request) ([]modelV2.AgentCtwCallbackSummaryV2, error)
}

func (r *repo) AgcSimpleWinLoseV2(data modelV2.AgcSimpleWinLoseV2) (*modelV2.AgcSimpleWinLoseV2Response, error) {
	log.Println("AgcSimpleWinLose req ------> ", helper.StructJson(data))

	// AgcSimpleWinLose
	var result modelV2.AgcSimpleWinLoseV2Response

	url := fmt.Sprintf("%s/reports/simplewinlose", os.Getenv("TRANSFER_API"))
	res, err := helper.Post(url, data)
	if err != nil {
		log.Println("AgcSimpleWinLose error ------> ", helper.StructJson(err))
		return nil, err
	}

	jsonRes, _ := json.Marshal(res)
	if err := json.Unmarshal(jsonRes, &result); err != nil {
		log.Println("AgcSimpleWinLose resp ------> ", helper.StructJson(res))
		return nil, err
	}

	return &result, nil
}

func (r *repo) GetAgentCtwCallbackV2(req modelV2.AgentCtwCallbackSummaryV2Request) ([]modelV2.AgentCtwCallbackSummaryV2, error) {
	var result []modelV2.AgentCtwCallbackSummaryV2
	selectFields := "user_id, member_code, sum(payoff) as total_payoff, sum(bet_amount) as total_bet"
	selectFields += ", sum(winlose_amount) as total_winlose"
	query := r.db.Table("agent_ctw_callback")
	query = query.Select(selectFields)
	query = query.Where("is_success = ?", 1)
	if req.StatementDate != "" {
	}
	if req.StatementDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.StatementDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("created_at >= ?", startDateAtBkk)

		endDateAtBkk, err := r.ParseEodBkk(req.StatementDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("created_at <= ?", endDateAtBkk)
	}
	query = query.Group("user_id, member_code")
	query = query.Offset((req.PageIndex - 1) * req.PageSize).Limit(req.PageSize)
	query = query.Scan(&result)
	if err := query.Error; err != nil {
		return nil, err
	}

	return result, nil
}

func (r *repo) GetAgentPgHardCallback(req modelV2.AgentPgHardCallbackSummaryV2Request) ([]modelV2.AgentPgHardCallbackSummaryV2, error) {
	var result []modelV2.AgentPgHardCallbackSummaryV2
	selectFields := "user_id, member_code, sum(payoff) as total_payoff, sum(bet_amount) as total_bet"
	selectFields += ", sum(winlose_amount) as total_winlose"
	query := r.db.Table("agent_ctw_callback")
	query = query.Select(selectFields)
	query = query.Where("is_success = ?", 1)
	if req.StatementDate != "" {
	}
	if req.StatementDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.StatementDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("created_at >= ?", startDateAtBkk)

		endDateAtBkk, err := r.ParseEodBkk(req.StatementDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("created_at <= ?", endDateAtBkk)
	}
	query = query.Group("user_id, member_code")
	query = query.Offset((req.PageIndex - 1) * req.PageSize).Limit(req.PageSize)
	query = query.Scan(&result)
	if err := query.Error; err != nil {
		return nil, err
	}

	return result, nil
}
