package v2

import (
	modelV2 "cybergame-api/model/v2"
	"errors"
	"gorm.io/gorm"
	"log"
	"math"
)

type RankingV2Repository interface {
	// Admin
	CreateRankingV2Setting(req modelV2.CreateRankingV2SettingRequest) error
	GetRankingV2Setting() ([]modelV2.RankingV2SettingResponse, error)
	GetUserByRankingId(minScore int64, maxScore int64, req modelV2.RankingListRequest) ([]modelV2.RankingReportV2Response, int64, error)
	UpdateRankingV2Setting(req modelV2.UpdateRankingV2SettingRequest) error
	DeleteRankingV2Setting(id int64) error

	//	Web
	GetTotalRankingV2() ([]modelV2.TotalRankingV2Response, error)
	GetUserRankingV2(userId int64) (*modelV2.UserRankingV2Response, error)
	GetTurnRankingV2() (*modelV2.TurnRankingV2Response, error)

	//	Internal
	GetRankingV2SettingActive() ([]modelV2.RankingV2SettingResponse, error)
	GetRankingV2SettingById(id int64) (*modelV2.RankingV2SettingResponse, error)
	CheckRankingV2SettingExist(id int64) (bool, error)
	CheckRankingNameDuplicate(name string) (bool, error)
	CheckRankingNameDuplicateAndId(name string, id int64) (bool, error)
	GetCurrentSortNumber() (int64, error)
}

func NewRankingV2Repository(db *gorm.DB) RankingV2Repository {
	return &repo{db}
}

func (r *repo) CreateRankingV2Setting(req modelV2.CreateRankingV2SettingRequest) error {
	if err := r.db.Table("ranking_v2").Create(&req).Error; err != nil {
		return err
	}

	return nil
}

func (r *repo) GetRankingV2Setting() ([]modelV2.RankingV2SettingResponse, error) {
	var list []modelV2.RankingV2SettingResponse
	if err := r.db.Table("ranking_v2").Where("is_deleted = ?", false).Order("sort ASC").Scan(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (r *repo) GetUserByRankingId(minScore int64, maxScore int64, req modelV2.RankingListRequest) ([]modelV2.RankingReportV2Response, int64, error) {
	var list []modelV2.RankingReportV2Response
	var total int64
	var err error

	count := r.db.Table("user_playlog")
	count = count.Select("SUM(turn_total) AS total_turn, SUM(win_lose_total) AS total_win_lose")
	count = count.Group("user_id")
	count = count.Having("SUM(turn_total) BETWEEN ? AND ?", minScore, maxScore)
	if err = count.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if total > 0 {

		query := `
			WITH user_summary AS (
				SELECT
					u.member_code,
					u.id AS user_id,
					CAST(SUM(up.turn_total) AS SIGNED) AS total_turn,
					CAST(SUM(up.win_lose_total) AS SIGNED) AS total_win_lose
				FROM user_playlog up
				JOIN user u ON up.user_id = u.id
				GROUP BY up.user_id, u.member_code
				HAVING SUM(turn_total) BETWEEN ? AND ?
			)
			SELECT
				user_id,
				member_code,
				total_turn,
				total_win_lose,
				CASE
					WHEN total_turn = 0 THEN 0
					ELSE ROUND(((total_turn - total_win_lose) / total_turn * 100), 2)
				END AS WinLosePercent,
				RANK() OVER (ORDER BY total_turn DESC) AS ranking_number,
				COUNT(*) OVER () AS total_count
			FROM user_summary
			ORDER BY total_turn DESC
			LIMIT ? OFFSET ?
		`

		result := r.db.Table("user_playlog")

		if req.Limit > 0 {
			limit := req.Limit
			offset := req.Page * req.Limit
			result = result.Raw(query, minScore, maxScore, limit, offset)
		} else {
			result = result.Raw(query, minScore, maxScore, 10, 10)
		}

		if err = result.Scan(&list).Error; err != nil {
			return nil, total, err
		}

	}

	return list, total, nil

}

func (r *repo) UpdateRankingV2Setting(req modelV2.UpdateRankingV2SettingRequest) error {
	if err := r.db.Table("ranking_v2").
		Select("name", "image_url", "min_score", "max_score", "sort", "is_active", "is_deleted").
		Where("id = ?", req.Id).
		Updates(&req).Error; err != nil {
		return err
	}
	return nil
}

func (r *repo) DeleteRankingV2Setting(id int64) error {
	deleted := map[string]interface{}{
		"is_deleted": true,
	}

	if err := r.db.Table("ranking_v2").Where("id = ?", id).Updates(deleted).Error; err != nil {
		return err
	}

	return nil
}

func (r *repo) GetTotalRankingV2() ([]modelV2.TotalRankingV2Response, error) {
	ranking, err := r.GetRankingV2SettingActive()
	if err != nil {
		return nil, err
	}

	if len(ranking) == 0 {
		return nil, nil
	}

	var list []modelV2.TotalRankingV2Response
	var results []struct {
		UserAmount     int64
		TotalPlay      float64
		TotalWins      float64
		WinLosePercent float64
	}
	var totalUsers int64 = 0 // เก็บจำนวนผู้เล่นทั้งหมด

	for _, rank := range ranking {

		var result struct {
			UserAmount     int64
			TotalPlay      float64
			TotalWins      float64
			WinLosePercent float64
		}

		query := `
				WITH grouped_data AS (
					SELECT 
						user_id,
						SUM(turn_total) AS TotalPlay,
						SUM(win_lose_total) AS TotalWins
					FROM 
						user_playlog
					GROUP BY 
						user_id
					HAVING 
						SUM(turn_total) BETWEEN ? AND ?
				)
				SELECT 
					user_id,
					TotalPlay,
					TotalWins,
					COUNT(*) OVER () AS UserAmount,
					ROUND(((TotalPlay - TotalWins) / TotalPlay * 100), 2) AS WinLosePercent
				FROM 
					grouped_data
			`
		queries := r.db.Table("user_playlog")
		queries = queries.Raw(query, rank.MinScore, rank.MaxScore)
		if err := queries.Scan(&result).Error; err != nil {
			return nil, err
		}

		totalUsers += result.UserAmount   // รวมจำนวนผู้เล่นทั้งหมด
		results = append(results, result) // เก็บ result ไว้ใช้ในการคำนวณ

		list = append(list, modelV2.TotalRankingV2Response{
			Name:     rank.Name,
			ImageUrl: rank.ImageUrl,
			//UserAmount: total,
			UserAmount:     result.UserAmount,
			TotalTurn:      result.TotalPlay,
			WinLosePercent: result.WinLosePercent,
			//WinLosePercent: totalWinLosePercent,
		})
	}

	// Step 2: คำนวณ UserPercent
	if totalUsers > 0 {
		for i := range list {
			if list[i].UserAmount > 0 {
				percentage := (float64(list[i].UserAmount) / float64(totalUsers)) * 100
				list[i].UserPercent = math.Round(percentage*100) / 100
			}
		}
	}

	return list, nil
}

func (r *repo) GetUserRankingV2(userId int64) (*modelV2.UserRankingV2Response, error) {

	var summary struct {
		TotalTurn float64
		TotalWins float64
	}

	query1 := r.db.Table("user_playlog")
	query1 = query1.Select("SUM(turn_total) as total_turn, SUM(win_lose_total) as total_wins")
	query1 = query1.Where("user_id = ?", userId)
	query1 = query1.Scan(&summary)
	if query1.Error != nil {
		return nil, query1.Error
	}

	ranking, err := r.GetRankingV2SettingActive()
	if err != nil {
		return nil, err
	}

	if len(ranking) == 0 {
		return nil, nil
	}

	type UserRanking struct {
		Id       int64
		Name     string
		ImageUrl string
		MinScore float32
		MaxScore float32
	}
	var userRanking UserRanking
	for _, rank := range ranking {
		log.Printf("checking rank: %+v", rank)
		if summary.TotalTurn >= float64(rank.MinScore) && summary.TotalTurn <= float64(rank.MaxScore) {
			userRanking.Id = rank.Id
			userRanking.Name = rank.Name
			userRanking.ImageUrl = rank.ImageUrl
			userRanking.MinScore = rank.MinScore
			userRanking.MaxScore = rank.MaxScore
			break
		}
	}

	type RankingReport struct {
		RankingNumber  int64   `json:"rankingNumber"`
		UserID         int64   `json:"userId"`
		MemberCode     string  `json:"memberCode"`
		TotalTurn      int     `json:"totalTurn"`
		TotalWinLose   int     `json:"totalWinLose"`
		WinLosePercent float64 `json:"winLosePercent"`
	}
	var list []RankingReport
	query := `
			WITH user_summary AS (
				SELECT
					u.member_code,
					u.id AS user_id,
					CAST(SUM(up.turn_total) AS SIGNED) AS total_turn,
					CAST(SUM(up.win_lose_total) AS SIGNED) AS total_win_lose
				FROM user_playlog up
				JOIN user u ON up.user_id = u.id
				GROUP BY up.user_id, u.member_code
				HAVING SUM(turn_total) BETWEEN ? AND ?
			)
			SELECT
				user_id,
				member_code,
				total_turn,
				total_win_lose,
				CASE
					WHEN total_turn = 0 THEN 0
					ELSE ROUND(((total_turn - total_win_lose) / total_turn * 100), 2)
				END AS WinLosePercent,
				RANK() OVER (ORDER BY total_turn DESC) AS ranking_number,
				COUNT(*) OVER () AS total_count
			FROM user_summary
			ORDER BY total_turn DESC
		`

	result := r.db.Table("user_playlog")
	result = result.Raw(query, userRanking.MinScore, userRanking.MaxScore)

	if err = result.Scan(&list).Error; err != nil {
		return nil, err
	}

	var userRank modelV2.UserRankingV2Response
	// หาลำดับของ user_id
	for _, rank := range list {
		if rank.UserID == userId {
			userRank.RankingNumber = rank.RankingNumber
			userRank.TotalPlay = float64(rank.TotalWinLose)
			userRank.TotalTurn = float64(rank.TotalTurn)
			userRank.WinLosePercent = rank.WinLosePercent
			break
		}
	}

	userRank.RankingName = userRanking.Name

	return &userRank, nil
}

func (r *repo) GetTurnRankingV2() (*modelV2.TurnRankingV2Response, error) {
	var result *modelV2.TurnRankingV2Response
	if err := r.db.Table(`play_log`).
		Select(`
		SUM(turn_total) AS turn_total_summary,
		SUM(turn_sport) AS turn_sport_summary,
		SUM(turn_casino) AS turn_casino_summary,
		SUM(turn_game) AS turn_game_summary,
		SUM(turn_lottery) AS turn_lottery_summary,
		ROUND((SUM(turn_sport) * 100.0 / NULLIF(SUM(turn_total), 0)), 2) AS turn_sport_percentage,
		ROUND((SUM(turn_casino) * 100.0 / NULLIF(SUM(turn_total), 0)), 2) AS turn_casino_percentage,
		ROUND((SUM(turn_game) * 100.0 / NULLIF(SUM(turn_total), 0)), 2) AS turn_game_percentage,
		ROUND((SUM(turn_lottery) * 100.0 / NULLIF(SUM(turn_total), 0)), 2) AS turn_lottery_percentage
	`).Scan(&result).Error; err != nil {
		return nil, err
	}

	return result, nil
}

func (r *repo) GetRankingV2SettingActive() ([]modelV2.RankingV2SettingResponse, error) {
	var list []modelV2.RankingV2SettingResponse
	if err := r.db.Table("ranking_v2").Where("is_active = ? AND is_deleted = ?", true, false).Order("sort ASC").Scan(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (r *repo) GetRankingV2SettingById(id int64) (*modelV2.RankingV2SettingResponse, error) {
	var ranking modelV2.RankingV2SettingResponse

	result := r.db.Table("ranking_v2")
	result = result.Where("id = ? AND is_deleted = ? AND is_active = ?", id, false, true)
	result = result.First(&ranking)
	if err := result.Error; err != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		} else {
			return nil, err
		}
	}

	return &ranking, nil
}

func (r *repo) CheckRankingV2SettingExist(id int64) (bool, error) {
	var count int64
	if err := r.db.Table("ranking_v2").Where("id = ? AND is_deleted = ?", id, false).Count(&count).Error; err != nil {
		return false, err
	}

	return count > 0, nil
}

func (r *repo) CheckRankingNameDuplicate(name string) (bool, error) {
	var count int64
	if err := r.db.Table("ranking_v2").Where("LOWER(name) LIKE LOWER(?) AND is_deleted = ?", "%"+name+"%", false).Count(&count).Error; err != nil {
		return false, err
	}

	return count > 0, nil
}

func (r *repo) CheckRankingNameDuplicateAndId(name string, id int64) (bool, error) {
	var count int64
	if err := r.db.Table("ranking_v2").Where("LOWER(name) LIKE LOWER(?) AND id != ? AND is_deleted = ?", "%"+name+"%", id, false).Count(&count).Error; err != nil {
		return false, err
	}

	return count > 0, nil
}

func (r *repo) GetCurrentSortNumber() (int64, error) {
	type SortNumber struct {
		Sort int64 `json:"sort"`
	}
	var result SortNumber

	if err := r.db.Table("ranking_v2").Select("MAX(sort) as sort").Scan(&result).Error; err != nil {
		return 0, err
	}

	return result.Sort, nil
}
