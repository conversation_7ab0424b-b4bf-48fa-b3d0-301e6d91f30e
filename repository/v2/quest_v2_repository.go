package v2

import (
	model "cybergame-api/model/v2"
	"errors"
	"fmt"
	"gorm.io/gorm"
	"math/rand"
	"time"
)

type QuestRepository interface {
	// Admin
	CreateQuest(req model.CreateQuest) error
	GetQuest(req model.QuestListRequest) ([]model.Quest, int64, error)
	GetQuestById(id int64) (*model.Quest, error)
	UpdateQuest(req model.UpdateQuest) error
	DeleteQuest(id int64) error
	GetQuestType() ([]model.QuestType, error)

	// Internal
	CheckQuestTypeIdExist(questTypeId int64) (bool, error)
	CheckQuestDuplicateName(name string) (bool, error)
	CheckQuestDuplicateNameAndIdNot(name string, id int64) (bool, error)
}

func NewQuestRepository(db *gorm.DB) QuestRepository {
	return &repo{db}
}

func (r *repo) CreateQuest(req model.CreateQuest) error {
	if err := r.db.Table("quest_type_v2").Where("id = ?", req.QuestTypeId).Limit(1).Find(&model.QuestType{}).Error; err != nil {
		return err
	}

	if err := r.db.Table("quest_v2").Create(&req).Error; err != nil {
		return err
	}

	return nil
}

func (r *repo) GetQuest(req model.QuestListRequest) ([]model.Quest, int64, error) {
	var list []model.Quest
	var total int64
	var err error

	count := r.db.Table("quest_v2")
	count = count.Where("is_deleted = ?", false)
	if err = count.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if total > 0 {
		selectedFields := "quests.id, quests.name, quests.image_url, quests.description, quests.is_deleted, quests.created_at, quests.updated_at"
		selectedFields += ", types.id as quest_type_id, types.name as quest_type_name"
		query := r.db.Table("quest_v2 as quests")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN quest_type_v2 AS types ON types.id = quests.quest_type_id")
		query = query.Where("quests.is_deleted = ?", false)
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err :=
			query.
				Offset(req.Page * req.Limit).
				Scan(&list).
				Error; err != nil {
			return nil, total, err
		}
	}

	return list, total, nil
}

func (r *repo) GetQuestById(id int64) (*model.Quest, error) {
	var quest model.Quest

	query := r.db.Table("quest_v2")
	query = query.Where("id = ?", id)
	query = query.Where("is_deleted = ?", false)
	query = query.First(&quest)
	if err := query.Error; err != nil {
		if errors.Is(query.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		} else {
			return nil, err
		}
	}

	return &quest, nil
}

func (r *repo) UpdateQuest(req model.UpdateQuest) error {
	updateData := map[string]interface{}{
		"name":          req.Name,
		"image_url":     req.ImageUrl,
		"description":   req.Description,
		"quest_type_id": req.QuestTypeId,
		"is_deleted":    req.IsDeleted,
	}
	if err := r.db.Table("quest_v2").
		Where("id = ?", req.Id).
		Updates(updateData).
		Error; err != nil {
		return err
	}
	return nil
}

func (r *repo) DeleteQuest(id int64) error {
	updateData := map[string]interface{}{
		"is_deleted": 1,
	}
	if err := r.db.Table("quest_v2").
		Where("id = ?", id).
		Updates(updateData).
		Error; err != nil {
		return err
	}
	return nil
}

func (r *repo) GetQuestType() ([]model.QuestType, error) {
	var list []model.QuestType

	selectedFields := "id, name, query_table, is_active, is_deleted"
	if err := r.db.Table("quest_type_v2").
		Select(selectedFields).
		Where("is_active = ? AND is_deleted = ?", true, false).
		Scan(&list).
		Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (r *repo) CheckQuestTypeIdExist(questTypeId int64) (bool, error) {
	var result int64
	query := r.db.Table("quest_type_v2")
	query = query.Select("id")
	query = query.Where("id = ?", questTypeId)
	query = query.Where("is_active = ?", true)
	query = query.Where("is_deleted = ?", false)
	query = query.Count(&result)
	if err := query.Error; err != nil {
		return false, err
	}

	return result > 0, nil
}

func (r *repo) CheckQuestDuplicateName(name string) (bool, error) {
	var result int64
	query := r.db.Table("quest_v2")
	query = query.Where("LOWER(name) LIKE LOWER(?)", "%"+name+"%")
	query = query.Where("is_deleted = ?", false)
	query = query.Count(&result)
	if err := query.Error; err != nil {
		return false, err
	}

	return result > 0, nil
}

func (r *repo) CheckQuestDuplicateNameAndIdNot(name string, id int64) (bool, error) {
	var result int64
	query := r.db.Table("quest_v2")
	query = query.Where("LOWER(name) LIKE LOWER(?)", "%"+name+"%")
	query = query.Where("id != ?", id)
	query = query.Where("is_deleted = ?", false)
	query = query.Count(&result)
	if err := query.Error; err != nil {
		return false, err
	}

	return result > 0, nil
}

func generateRandomString() string {
	currentTime := time.Now()
	randomPart := rand.Intn(1000) // Generate a random number between 0 and 999
	return fmt.Sprintf("%d%d%d_%d%d%d", currentTime.Year(), currentTime.Month(), currentTime.Day(), currentTime.Hour(), currentTime.Minute(), randomPart)
}
