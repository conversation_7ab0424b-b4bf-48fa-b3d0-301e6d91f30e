package v2

import (
	model "cybergame-api/model/v2"
	"errors"
	"fmt"
	"gorm.io/gorm"
	"log"
	"time"
)

type DailyQuestRepository interface {
	// Admin
	CreateDailyQuest(req model.CreateDailyQuestRequest) error
	GetDailyQuests(req model.DailyQuestListRequest) ([]model.DailyQuest, int64, error)
	GetDailyQuestById(id int64) (*model.DailyQuest, error)
	UpdateDailyQuest(req model.UpdateDailyQuestRequest) error
	DeleteDailyQuest(id int64) error
	GetDailyQuestCondition() ([]model.DailyQuestCondition, error)

	// Web
	WebGetDailyQuests(userId int64) ([]model.WebDailyQuest, error)
	CreateUserDailyQuest(userId int64, req *model.CreateUserDailyQuestRequest) error
	UpdateUserDailyQuestDetailSuccess(userDailyQuestDetailId int64) error
	UpdateDailyQuestIsActive(id int64, active bool) error
	GetUserDailyQuestList(userId int64, req model.DailyQuestListRequest) ([]model.UserDailyQuestListResponse, int64, error)
	ClaimUserDailyQuestReward(userId int64, req model.ClaimUserDailyQuestReward) error

	// Internal
	CheckQuestIdExist(questId int64) (bool, error)
	CheckDailyQuestExist(dailyQuestId int64) (bool, error)
	CheckUserDailyQuestToday(userId int64, dailyQuestId int64) (bool, error)
	CheckDailyQuestConditionById(id int64) (bool, error)
	GetUserDailyQuestById(userId int64, dailyQuestId int64) (*model.GetUserDailyQuestResponse, error)
	UpdateUserDailyQuestStatus(req model.UpdateUserDailyQuest) error
	UpdateUserDailyQuestClaimStatus(userId int64, dailyQuestId int64) error
	GetUserDailyQuestClaimDetailById(userId int64, id int64) (*model.GetUserDailyQuestClaimDetail, error)
	FindDailyQuestActive() (int64, error)
	CountUserQuestUnfinished() (int64, error)
	GetUserDailyQuestUnfinished(dailyQuestId int64, page int, limit int) ([]*model.GetUserDailyQuestUnfinished, error)
	GetTodayDailyQuest() (*model.GetCheckDailyQuest, error)
}

func NewDailyQuestRepository(db *gorm.DB) DailyQuestRepository {
	return &repo{db}
}

func (r *repo) CreateDailyQuest(req model.CreateDailyQuestRequest) error {
	err := r.db.Transaction(func(tx *gorm.DB) error {
		createdDailyQuest := model.DailyQuest{
			Reward:      req.Reward,
			Description: req.Description,
			StartDate:   req.StartDate,
			EndDate:     req.EndDate,
			StartTime:   req.StartTime,
			EndTime:     req.EndTime,
			IsDeleted:   false,
		}

		if err := tx.Table("daily_quest_v2").
			Create(&createdDailyQuest).
			Error; err != nil {
			return err
		}

		for _, quest := range req.Quests {
			questDetail := map[string]interface{}{
				"daily_quest_id":           createdDailyQuest.Id,
				"quest_id":                 quest.QuestId,
				"daily_quest_condition_id": quest.QuestConditionId,
				"condition_amount":         quest.ConditionAmount,
				"sort":                     quest.Sort,
			}

			if err := tx.Table("daily_quest_detail_v2").Create(&questDetail).Error; err != nil {
				return err
			}
		}
		return nil
	})

	if err != nil {
		return err
	}

	return nil
}

func (r *repo) GetDailyQuests(req model.DailyQuestListRequest) ([]model.DailyQuest, int64, error) {
	var list []model.DailyQuest
	var total int64
	var err error

	count := r.db.Table("daily_quest_v2")
	count = count.Where("is_deleted = ?", false)
	if err = count.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if total > 0 {
		queryDaily := r.db.Table("daily_quest_v2 as daily").
			Select("daily.id, daily.reward, daily.description, daily.start_date, daily.end_date, daily.start_time, daily.end_time, daily.is_active, daily.is_deleted, daily.created_at, daily.updated_at").
			Where("daily.is_deleted = ?", false).
			Order("daily.id ASC")
		if req.Limit > 0 {
			queryDaily = queryDaily.Limit(req.Limit)
		}

		if err = queryDaily.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}

		for index, dailies := range list {
			var questDetails []*model.DailyQuestDetail
			selectedFields := "detail.condition_amount, detail.sort"
			selectedFields += ", quests.id as quest_id, quests.name as quest_name, quests.image_url as quest_image_url"
			selectedFields += ", types.id as quest_type_id, types.name as quest_type_name"
			selectedFields += ", dqc.id as quest_condition_id, dqc.name as quest_condition_name, dqc.label_th as quest_condition_label_th, dqc.label_en as quest_condition_label_en"
			if err = r.db.Table("daily_quest_detail_v2 as detail").
				Select(selectedFields).
				Joins("LEFT JOIN quest_v2 as quests ON quests.id = detail.quest_id").
				Joins("LEFT JOIN quest_type_v2 as types ON types.id = quests.quest_type_id").
				Joins("LEFT JOIN daily_quest_condition_v2 as dqc ON dqc.id = detail.daily_quest_condition_id").
				Where("detail.daily_quest_id = ?", dailies.Id).
				Order("detail.sort ASC").
				Scan(&questDetails).
				Error; err != nil {
				return nil, 0, err
			}
			list[index].Quest = questDetails
		}
	}

	return list, total, nil

}

func (r *repo) GetDailyQuestById(id int64) (*model.DailyQuest, error) {
	var dailyQuest model.DailyQuest
	dailyQuestQuery := r.db.Table("daily_quest_v2 as daily")
	dailyQuestQuery = dailyQuestQuery.Select("daily.id, daily.reward, daily.description, daily.start_date, daily.end_date, daily.start_time, daily.end_time, daily.is_deleted, daily.created_at, daily.updated_at")
	dailyQuestQuery = dailyQuestQuery.Where("daily.id = ? AND daily.is_deleted = ?", id, false)
	dailyQuestQuery = dailyQuestQuery.Order("daily.id ASC")
	dailyQuestQuery = dailyQuestQuery.First(&dailyQuest)
	if err := dailyQuestQuery.Error; err != nil {
		if errors.Is(dailyQuestQuery.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		} else {
			return nil, dailyQuestQuery.Error
		}
	}

	var questDetails []*model.DailyQuestDetail
	selectedFields := "detail.condition_amount, detail.sort"
	selectedFields += ", quests.id as quest_id, quests.name as quest_name, quests.image_url as quest_image_url"
	selectedFields += ", types.id as quest_type_id, types.name as quest_type_name"
	selectedFields += ", dqc.id as quest_condition_id, dqc.name as quest_condition_name, dqc.label_th as quest_condition_label_th, dqc.label_en as quest_condition_label_en"
	if err := r.db.Table("daily_quest_detail_v2 as detail").
		Select(selectedFields).
		Joins("LEFT JOIN quest_v2 as quests ON quests.id = detail.quest_id").
		Joins("LEFT JOIN quest_type_v2 as types ON types.id = quests.quest_type_id").
		Joins("LEFT JOIN daily_quest_condition_v2 as dqc ON dqc.id = detail.daily_quest_condition_id").
		Where("detail.daily_quest_id = ?", dailyQuest.Id).
		Order("detail.sort ASC").
		Scan(&questDetails).
		Error; err != nil {
		return nil, err
	}

	dailyQuest.Quest = questDetails

	return &dailyQuest, nil
}

func (r *repo) UpdateDailyQuest(req model.UpdateDailyQuestRequest) error {
	err := r.db.Transaction(func(tx *gorm.DB) error {
		updateData := map[string]interface{}{
			"reward":      req.Reward,
			"description": req.Description,
			"start_date":  req.StartDate,
			"end_date":    req.EndDate,
			"start_time":  req.StartTime,
			"end_time":    req.EndTime,
			"is_deleted":  req.IsDeleted,
		}

		if err := tx.Table("daily_quest_v2").
			Where("id = ?", req.Id).
			Updates(updateData).
			Error; err != nil {
			return err
		}

		if err := tx.Table("daily_quest_detail_v2").
			Where("daily_quest_id = ?", req.Id).
			Delete(&model.DailyQuestDetail{}).
			Error; err != nil {
			return err
		}

		for _, quest := range req.Quests {
			questDetail := map[string]interface{}{
				"daily_quest_id":           req.Id,
				"quest_id":                 quest.QuestId,
				"daily_quest_condition_id": quest.QuestConditionId,
				"condition_amount":         quest.ConditionAmount,
				"sort":                     quest.Sort,
			}

			if err := tx.Table("daily_quest_detail_v2").Create(&questDetail).Error; err != nil {
				return err
			}
		}
		return nil
	})

	if err != nil {
		return err
	}

	return nil
}

func (r *repo) DeleteDailyQuest(id int64) error {
	updateData := map[string]interface{}{
		"is_deleted": 1,
	}
	if err := r.db.Table("daily_quest_v2").
		Where("id = ?", id).
		Updates(updateData).
		Error; err != nil {
		return err
	}
	return nil
}

func (r *repo) GetDailyQuestCondition() ([]model.DailyQuestCondition, error) {
	var dailyQuestConditions []model.DailyQuestCondition
	selectedFields := "id, name, label_th, label_en"
	query := r.db.Table("daily_quest_condition_v2")
	query = query.Select(selectedFields)
	if err := query.Scan(&dailyQuestConditions).Error; err != nil {
		return nil, err
	}

	return dailyQuestConditions, nil
}

func (r *repo) WebGetDailyQuests(userId int64) ([]model.WebDailyQuest, error) {
	// เตรียมวันที่และเวลาในโซนเวลา BKK
	bkkTime := time.Now().UTC().Add(7 * time.Hour)
	today := bkkTime.Format("2006-01-02")
	now := bkkTime.Format("15:04:05")

	// ดึงข้อมูลเควสต์รายวันที่ยังไม่ถูกลบและอยู่ในช่วงเวลาที่กำหนด
	var dailies []model.WebDailyQuest
	query := r.db.Table("daily_quest_v2 as daily")
	query = query.Select("daily.id, daily.id as daily_quest_id, daily.reward, daily.description, daily.start_date, daily.end_date, daily.start_time, daily.end_time")
	query = query.Where("daily.is_active = ?", true)
	query = query.Where("daily.is_deleted = ?", false)
	query = query.Where("start_date <= ?", today)
	query = query.Where("end_date >= ?", today)
	query = query.Where("start_time <= ?", now)
	query = query.Where("end_time >= ?", now)

	// ดึงข้อมูลเควสต์และเรียงลำดับตาม ID
	if err := query.Order("daily.id ASC").Scan(&dailies).Error; err != nil {
		return nil, err
	}

	// วนลูปเพื่อดึงรายละเอียดเควสต์และสถานะของผู้ใช้
	for i, daily := range dailies {

		//ดึงรายละเอียดเควสต์
		var questDetails []*model.WebDailyQuestDetail
		if err := r.db.Table("daily_quest_detail_v2 as detail").
			Select("detail.condition_amount, detail.sort, quests.id as quest_id, quests.name as quest_name, quests.image_url as quest_image_url, types.id as quest_type_id, types.name as quest_type_name, dqc.id as quest_condition_id, dqc.name as quest_condition_name, dqc.label_th as quest_condition_label_th, dqc.label_en as quest_condition_label_en").
			Joins("LEFT JOIN quest_v2 as quests ON quests.id = detail.quest_id").
			Joins("LEFT JOIN quest_type_v2 as types ON types.id = quests.quest_type_id").
			Joins("LEFT JOIN daily_quest_condition_v2 as dqc ON dqc.id = detail.daily_quest_condition_id").
			Where("detail.daily_quest_id = ?", daily.Id).
			Order("detail.sort ASC").
			Scan(&questDetails).Error; err != nil {
			return nil, err
		}

		// ดึงข้อมูลเควสต์ของผู้ใช้
		var userDailyQuest model.GetUserDailyQuestResponse
		query := r.db.Table("user_daily_quest_v2 as udq").
			Select("udq.id, udq.daily_quest_id, udq.status, udq.is_claim").
			Where("udq.user_id = ? AND udq.daily_quest_id = ?", userId, daily.Id)

		// เพิ่มเงื่อนไขวันที่สร้าง
		if startCreate, err := r.ParseBodBkk(today); err == nil {
			query = query.Where("DATE(udq.created_at) >= ?", startCreate)
		} else {
			return nil, err
		}
		if endCreate, err := r.ParseEodBkk(today); err == nil {
			query = query.Where("DATE(udq.created_at) <= ?", endCreate)
		} else {
			return nil, err
		}

		// ตรวจสอบผลลัพธ์การค้นหา
		if err := query.First(&userDailyQuest).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				userDailyQuest = model.GetUserDailyQuestResponse{
					DailyQuestId: daily.Id,
				}
			} else {
				return nil, err
			}
		}

		// อัปเดตสถานะเควสต์
		if userDailyQuest.Id != 0 {
			var userDetails []model.GetUserDailyQuestDetail

			if err := r.db.Table("user_daily_quest_detail_v2 as udqd").
				Select("udqd.id, udqd.quest_id, udqd.status").
				Where("udqd.user_daily_quest_id = ?", userDailyQuest.Id).
				Order("udqd.id ASC").
				Scan(&userDetails).Error; err != nil {
				return nil, err
			}

			// อัปเดตสถานะใน questDetails
			for _, quest := range questDetails {
				for _, userQuest := range userDetails {
					if quest.QuestId == userQuest.QuestId {

						quest.Status = userQuest.Status
					}
				}
			}

			dailies[i].Status = userDailyQuest.Status
			dailies[i].IsClaim = userDailyQuest.IsClaim

			if userDailyQuest.Status {
				dailies[i].Id = userDailyQuest.Id
			}
		}

		// อัปเดตข้อมูลใน dailies
		dailies[i].Quest = questDetails
	}

	return dailies, nil
}

func (r *repo) CreateUserDailyQuest(userId int64, req *model.CreateUserDailyQuestRequest) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// ใช้ DATE แทน exact timestamp
		today := time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02")

		var existingQuest struct {
			ID int64 `gorm:"column:id"`
		}

		// ค้นหา user_daily_quest ที่มีอยู่แล้ว
		result := tx.Table("user_daily_quest_v2").
			Select("id").
			Where("user_id = ? AND daily_quest_id = ? AND DATE(created_at) = ?",
				userId, req.DailyQuestId, today).
			First(&existingQuest)

		if result.Error != nil && !errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return result.Error
		}

		// ถ้าพบ user_daily_quest ที่มีอยู่แล้ว
		if result.Error == nil {
			log.Printf("user_daily_quest_v2: daily quest already exists id: %s", existingQuest.ID)

			var existing struct {
				QuestId int64 `gorm:"column:quest_id"`
			}

			detailResult := tx.Table("user_daily_quest_detail_v2").
				Select("quest_id").
				Where("user_daily_quest_id = ? AND quest_id = ?", existingQuest.ID, req.QuestId).
				First(&existing)

			if detailResult.Error != nil && !errors.Is(detailResult.Error, gorm.ErrRecordNotFound) {
				return detailResult.Error
			}

			// ถ้ายังไม่มี detail สำหรับ quest นี้ ให้สร้างใหม่
			if errors.Is(detailResult.Error, gorm.ErrRecordNotFound) {
				if err := r.createUserDailyDetail(tx, existingQuest.ID, req.QuestId); err != nil {
					return err
				}
			}

			return nil
		}

		// สร้าง user_daily_quest ใหม่
		type UserDailyQuest struct {
			ID           int64     `gorm:"primaryKey" json:"id" db:"id"`
			UserId       int64     `db:"user_id"`
			DailyQuestId int64     `db:"daily_quest_id"`
			CreatedAt    time.Time `db:"created_at"`
		}

		userDailyQuest := UserDailyQuest{
			UserId:       userId,
			DailyQuestId: req.DailyQuestId,
			CreatedAt:    time.Now().UTC().Add(7 * time.Hour),
		}

		if err := tx.Table("user_daily_quest_v2").Create(&userDailyQuest).Error; err != nil {
			return err
		}

		// สร้าง detail
		if err := r.createUserDailyDetail(tx, userDailyQuest.ID, req.QuestId); err != nil {
			return err
		}

		return nil
	})
}

func (r *repo) UpdateDailyQuestIsActive(id int64, active bool) error {
	type UpdateIsActive struct {
		IsActive bool `json:"is_active"`
	}
	update := UpdateIsActive{
		IsActive: active,
	}

	if err := r.db.Table("daily_quest_v2").
		Where("id = ?", id).
		Select("is_active").
		Updates(update).Error; err != nil {
		return err
	}

	return nil
}

func (r *repo) UpdateUserDailyQuestDetailSuccess(userDailyQuestDetailId int64) error {
	type UpdateStatus struct {
		Status bool `json:"status"`
	}

	var updateStatus UpdateStatus
	updateStatus.Status = true

	if err := r.db.Table("user_daily_quest_detail_v2").
		Where("id = ?", userDailyQuestDetailId).
		Updates(updateStatus).Error; err != nil {
		return err
	}

	return nil
}

func (r *repo) GetUserDailyQuestList(userId int64, req model.DailyQuestListRequest) ([]model.UserDailyQuestListResponse, int64, error) {
	var list []model.UserDailyQuestListResponse
	var total int64
	var err error

	count := r.db.Table("user_daily_quest_v2")
	count = count.Where("user_id = ?", userId)
	if err = count.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if total > 0 {
		query := r.db.Table("user_daily_quest_v2 as udq")
		query = query.Select("udq.id, udq.daily_quest_id, udq.status, udq.is_claim, dq.reward, udq.created_at")
		query = query.Joins("LEFT JOIN daily_quest_v2 as dq ON dq.id = udq.daily_quest_id")
		query = query.Where("udq.user_id = ?", userId)
		query = query.Order("udq.created_at DESC")

		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}

		if err = query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}

		if len(list) != 0 {
			for i, quest := range list {
				var dailyTotalQuest int64
				countDailyTotalQuest := r.db.Table("daily_quest_detail_v2").Select("COUNT(*)").Where("daily_quest_id = ?", quest.DailyQuestId).Count(&dailyTotalQuest)
				if countDailyTotalQuest.Error != nil {
					return nil, 0, countDailyTotalQuest.Error
				}

				type QuestCount struct {
					TrueStatusCount int64 `json:"true_status_count"`
				}
				var results QuestCount
				if err := r.db.Table("user_daily_quest_detail_v2").
					Select("SUM(CASE WHEN status = true THEN 1 ELSE 0 END) as true_status_count").
					Where("user_daily_quest_id = ?", quest.Id).
					Scan(&results).Error; err != nil {
					return nil, 0, err
				}

				progressRatio := fmt.Sprintf("%d/%d", results.TrueStatusCount, dailyTotalQuest)
				list[i].Progress = progressRatio
			}
		} else {
			return nil, 0, nil
		}
	}

	return list, total, nil
}

func (r *repo) ClaimUserDailyQuestReward(userId int64, req model.ClaimUserDailyQuestReward) error {
	type UserDailyQuest struct {
		Id           int64 `json:"id"`
		DailyQuestId int64 `json:"daily_quest_id"`
	}
	var result UserDailyQuest
	query := r.db.Table("user_daily_quest_v2 as udq")
	query = query.Select("udq.id, udq.daily_quest_id")
	query = query.Where("udq.user_id = ? AND udq.id = ?", userId, req.UserDailyQuestId)
	query = query.Where("udq.status = ? AND udq.is_claim = ?", true, false)
	query = query.First(&result)
	if err := query.Error; err != nil {
		if errors.Is(query.Error, gorm.ErrRecordNotFound) {
			return errors.New("USER_DAILY_QUEST_REWARD_NOT_FOUND")
		} else {
			return query.Error
		}
	}

	type DailyQuest struct {
		Id     int64   `json:"id"`
		Reward float64 `json:"reward"`
	}
	var dailyQuest DailyQuest
	query1 := r.db.Table("daily_quest_v2 as dq")
	query1 = query1.Select("dq.id, dq.reward")
	query1 = query1.Where("dq.id = ?", result.DailyQuestId)
	query = query1.First(&dailyQuest)
	if err := query1.Error; err != nil {
		if errors.Is(query1.Error, gorm.ErrRecordNotFound) {
			return errors.New("DAILY_QUEST_REWARD_NOT_FOUND")
		} else {
			return query1.Error
		}
	}

	return nil
}

func (r *repo) CheckQuestIdExist(questId int64) (bool, error) {
	var result int64

	query := r.db.Table("quest_v2")
	query = query.Select("id")
	query = query.Where("id = ?", questId)
	query = query.Where("is_deleted = ?", false)
	query = query.Count(&result)
	if err := query.Error; err != nil {
		return false, err
	}

	return result > 0, nil
}

func (r *repo) CheckDailyQuestExist(dailyQuestId int64) (bool, error) {
	var result int64

	query := r.db.Table("daily_quest_v2")
	query = query.Select("id")
	query = query.Where("id = ?", dailyQuestId)
	query = query.Where("is_deleted = ?", false)
	query = query.Count(&result)
	if err := query.Error; err != nil {
		return false, err
	}

	return result > 0, nil
}

func (r *repo) CheckUserDailyQuestToday(userId int64, dailyQuestId int64) (bool, error) {
	var result int64
	today := time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02")

	query := r.db.Table("user_daily_quest_v2")
	query = query.Select("id")
	query = query.Where("user_id = ?", userId)
	query = query.Where("daily_quest_id = ?", dailyQuestId)

	startDateAtBkk, err := r.ParseBodBkk(today)
	if err != nil {
		return false, err
	}
	query = query.Where("created_at >= ? ", startDateAtBkk)

	EndDateAtBkk, err := r.ParseEodBkk(today)
	if err != nil {
		return false, err
	}
	query = query.Where("created_at <= ? ", EndDateAtBkk)

	query = query.Count(&result)
	if err := query.Error; err != nil {
		return false, err
	}

	return result > 0, nil
}

func (r *repo) CheckDailyQuestConditionById(id int64) (bool, error) {
	var result int64

	query := r.db.Table("daily_quest_condition_v2")
	query = query.Select("id")
	query = query.Where("id = ?", id)
	query = query.Count(&result)
	if err := query.Error; err != nil {
		return false, err
	}

	return result > 0, nil
}

func (r *repo) GetUserDailyQuestById(userId int64, dailyQuestId int64) (*model.GetUserDailyQuestResponse, error) {
	var result model.GetUserDailyQuestResponse
	today := time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02")

	query := r.db.Table("user_daily_quest_v2 as udq")
	query = query.Select("udq.id, udq.daily_quest_id, udq.status, udq.is_claim, dq.reward")
	query = query.Joins("LEFT JOIN daily_quest_v2 as dq ON dq.id = udq.daily_quest_id")
	query = query.Where("udq.user_id = ?", userId)
	query = query.Where("udq.daily_quest_id = ?", dailyQuestId)

	startDateAtBkk, err := r.ParseBodBkk(today)
	if err != nil {
		return nil, err
	}
	query = query.Where("udq.created_at >= ? ", startDateAtBkk)

	EndDateAtBkk, err := r.ParseEodBkk(today)
	if err != nil {
		return nil, err
	}
	query = query.Where("udq.created_at <= ? ", EndDateAtBkk)

	if err := query.First(&result).Error; err != nil {
		if errors.Is(query.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		} else {
			return nil, query.Error
		}
	}

	var questDetails []model.GetUserDailyQuestDetail
	selectedFields := "detail.id, detail.quest_id, detail.status"
	query1 := r.db.Table("user_daily_quest_detail_v2 as detail")
	query1 = query1.Select(selectedFields)
	query1 = query1.Where("detail.user_daily_quest_id = ?", result.Id)
	query1 = query1.Order("detail.quest_id ASC")
	if err := query1.Scan(&questDetails).Error; err != nil {
		return nil, err
	}

	result.Quests = questDetails

	return &result, nil
}

func (r *repo) UpdateUserDailyQuestStatus(req model.UpdateUserDailyQuest) error {
	if err := r.db.Table("user_daily_quest_v2").
		Where("id = ?", req.Id).
		Updates(req).Error; err != nil {
		return err
	}

	return nil
}

func (r *repo) GetUserDailyQuestClaimDetailById(userId int64, id int64) (*model.GetUserDailyQuestClaimDetail, error) {
	var result model.GetUserDailyQuestClaimDetail

	query := r.db.Table("user_daily_quest_v2 as udq")
	query = query.Select("udq.id, udq.daily_quest_id, udq.status, udq.is_claim, dq.reward")
	query = query.Joins("LEFT JOIN daily_quest_v2 as dq ON dq.id = udq.daily_quest_id")
	query = query.Where("udq.user_id = ?", userId)
	query = query.Where("udq.id = ?", id)
	query = query.Where("udq.status = ? AND udq.is_claim = ?", true, false)

	if err := query.First(&result).Error; err != nil {
		if errors.Is(query.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		} else {
			return nil, query.Error
		}
	}

	return &result, nil
}

func (r *repo) UpdateUserDailyQuestClaimStatus(userId int64, id int64) error {
	type UpdateStatus struct {
		IsClaim bool `json:"is_claim"`
	}

	var updateStatus UpdateStatus
	updateStatus.IsClaim = true

	if err := r.db.Table("user_daily_quest_v2").
		Where("id = ? AND user_id = ? AND status = ? AND is_claim = ?", id, userId, true, false).
		Updates(updateStatus).Error; err != nil {
		return err
	}

	return nil
}

func (r *repo) FindDailyQuestActive() (int64, error) {
	type DailyQuestActive struct {
		Id int64 `json:"id"`
	}
	var result DailyQuestActive

	err := r.db.Table("daily_quest_v2").
		Select("id").
		Where("is_active = ?", true).
		Where("is_deleted = ?", false).
		First(&result).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return 0, nil
		}
		return 0, err
	}

	return result.Id, nil
}

func (r *repo) createUserDailyDetail(tx *gorm.DB, userDailyQuestId int64, questId int64) error {
	var createUserDailyQuestDetail model.CreateUserDailyQuestDetail
	createUserDailyQuestDetail.UserDailyQuestId = userDailyQuestId
	createUserDailyQuestDetail.QuestId = questId

	return tx.Table("user_daily_quest_detail_v2").Create(&createUserDailyQuestDetail).Error
}

func (r *repo) CountUserQuestUnfinished() (int64, error) {
	var count int64

	// ใช้ DATE แทน exact timestamp
	today := time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02")

	err := r.db.Table("user_daily_quest_v2").
		Where("status = ? AND DATE(created_at) = ?", false, today).
		Count(&count).Error

	if err != nil {
		return 0, err
	}

	return count, nil
}

func (r *repo) GetUserDailyQuestUnfinished(dailyQuestId int64, page int, limit int) ([]*model.GetUserDailyQuestUnfinished, error) {
	// เตรียมวันที่และเวลาในโซนเวลา BKK
	bkkTime := time.Now().UTC().Add(7 * time.Hour)
	today := bkkTime.Format("2006-01-02")

	var unfinished []*model.GetUserDailyQuestUnfinished
	query := r.db.Table("user_daily_quest_v2 as udq")
	query = query.Select("udq.id, udq.user_id, udq.daily_quest_id, udq.status, udq.is_claim, udq.created_at")
	query = query.Where("DATE(udq.created_at) = ?", today)
	query = query.Where("udq.status = ?", false)

	if limit > 0 {
		query = query.Limit(limit)
	}

	if err := query.Offset(page * limit).Scan(&unfinished).Error; err != nil {
		return nil, err
	}

	if len(unfinished) != 0 {
		// วนลูปเพื่อดึงรายละเอียดเควสต์และสถานะของผู้ใช้
		for i, daily := range unfinished {

			// ดึงรายละเอียดเควสต์
			var details []*model.UserDailyQuestUnfinishedDetail
			if err := r.db.Table("user_daily_quest_detail_v2 as detail").
				Select("detail.id, detail.quest_id, detail.status").
				Where("detail.user_daily_quest_id = ?", daily.Id).
				Order("detail.id ASC").
				Scan(&details).Error; err != nil {
				return nil, err
			}

			if len(details) != 0 {
				unfinished[i].Details = details
			}
		}

	} else {
		return nil, nil
	}

	return unfinished, nil
}

func (r *repo) GetTodayDailyQuest() (*model.GetCheckDailyQuest, error) {
	// เตรียมวันที่และเวลาในโซนเวลา BKK
	bkkTime := time.Now().UTC().Add(7 * time.Hour)
	today := bkkTime.Format("2006-01-02")
	now := bkkTime.Format("15:04:05")

	var daily model.GetCheckDailyQuest
	query := r.db.Table("daily_quest_v2 as dq")
	query = query.Select("dq.id, dq.reward, dq.start_date, dq.end_date, dq.start_time, dq.end_time")
	query = query.Where("dq.is_active = ?", true)
	query = query.Where("dq.is_deleted = ?", false)
	query = query.Where("DATE(dq.start_date) <= ?", today)
	query = query.Where("DATE(dq.end_date) >= ?", today)
	query = query.Where("dq.start_time <= ?", now)
	query = query.Where("dq.end_time >= ?", now)
	query = query.First(&daily)
	if err := query.Error; err != nil {
		if errors.Is(query.Error, gorm.ErrRecordNotFound) {
			return nil, nil // ไม่มีเควสต์รายวันในวันนี้
		} else {
			return nil, query.Error // เกิดข้อผิดพลาดอื่น ๆ
		}
	}

	// ดึงรายละเอียดเควสต์
	var details []*model.GetCheckDailyQuestDetail
	if err := r.db.Table("daily_quest_detail_v2 as detail").
		Select("detail.quest_id, detail.daily_quest_condition_id, condition_amount, quests.quest_type_id as quest_type_id").
		Joins("LEFT JOIN quest_v2 as quests ON quests.id = detail.quest_id").
		Where("detail.daily_quest_id = ?", daily.Id).
		Order("detail.sort ASC").
		Scan(&details).Error; err != nil {
		return nil, err
	}

	if len(details) > 0 {
		daily.Details = details
	}

	return &daily, nil
}
