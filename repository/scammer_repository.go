package repository

import (
	"cybergame-api/model"

	"gorm.io/gorm"
)

func NewScammerRepository(db *gorm.DB) ScammerRepository {
	return &repo{db}
}

type ScammerRepository interface {
	GetScammerList(req model.ScammerQuery) ([]model.ScammertList, *int64, error)
	CreateScammer(body model.CreateScammer) (*int64, error)
	UpdateUserStatus(body model.ScammerUpdateUser) error
	DeleteScammer(id int64) error
	GetScammerById(id int64) (*model.ScammertList, error)
	UpdateDeleteUserStatus(body model.ScammerDelete) error
	GetScammerByUserId(id int64) (*model.ScammertList, error)
	GetScammerSummary() (*model.ScammerSummary, error)
	// REMOTE-SCAMMER
	GetUserById(id int64) (*model.UserResponse, error)
	SubmitScammer(body model.ScammerSubmitRequest) (*int64, error)
	// [ADMIN_LOG]
	CreateAdminLog(body model.AdminLogCreateBody) (*int64, error)
	// REF-ADMIN_ACTION
	CreateAdminAction(body model.AdminActionCreateBody) (*int64, error)
}

func (r repo) CreateScammer(body model.CreateScammer) (*int64, error) {

	if err := r.db.Table("scammer").
		Create(&body).
		Error; err != nil {
		return nil, err
	}

	return &body.Id, nil
}

// func (r repo) DeletdeScammer(id int64) error {

// 	if err := r.db.Table("scammer").
// 		Where("id = ?", id).
// 		Delete(&model.Scammer{}).
// 		Error; err != nil {
// 		return err
// 	}

// 	return nil
// }

func (r repo) GetScammerList(req model.ScammerQuery) ([]model.ScammertList, *int64, error) {

	var scammers []model.ScammertList
	var total int64
	var err error

	selectedFields := "scammer.id as id, scammer.reason as reason, scammer.created_at as created_at"
	selectedFields += ", user.bank_id as bank_id, bank.name as bank_name, user.bank_account as bank_account"
	selectedFields += ", user.phone as phone, user.fullname as fullname , user.id as user_id"

	count := r.db.Table("scammer")
	count = count.Joins("LEFT JOIN user ON scammer.user_id = user.id")
	count = count.Joins("LEFT JOIN bank ON user.bank_id = bank.id")
	count = count.Select("scammer.id")
	if req.From != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.From)
		if err != nil {
			return nil, nil, err
		}
		count = count.Where("scammer.created_at >= ? ", startDateAtBkk)
	}
	if req.To != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.To)
		if err != nil {
			return nil, nil, err
		}
		count = count.Where("scammer.created_at <=  ?", endDateAtBkk)
	}
	if req.BankId != nil {
		count = count.Where("scammer.bank_id = ?", req.BankId)
	}
	if req.Word != "" {
		searchText := "%" + req.Word + "%"
		count = count.Where(r.db.Where("user.fullname LIKE ?", searchText).Or("user.phone LIKE ?", searchText).Or("user.bank_account LIKE ?", searchText))
	}
	if err = count.
		Where("scammer.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, nil, err
	}

	if total > 0 {
		query := r.db.Table("scammer")
		query = query.Joins("LEFT JOIN user ON scammer.user_id = user.id")
		query = query.Joins("LEFT JOIN bank ON user.bank_id = bank.id")
		query = query.Select(selectedFields)

		if req.From != "" {
			startDateAtBkk, err := r.ParseBodBkk(req.From)
			if err != nil {
				return nil, nil, err
			}
			query = query.Where("scammer.created_at >= ? ", startDateAtBkk)
		}
		if req.To != "" {
			endDateAtBkk, err := r.ParseEodBkk(req.To)
			if err != nil {
				return nil, nil, err
			}
			query = query.Where("scammer.created_at <=  ?", endDateAtBkk)
		}
		if req.BankId != nil {
			query = query.Where("scammer.bank_id = ?", req.BankId)
		}
		if req.Word != "" {
			searchText := "%" + req.Word + "%"
			query = query.Where(r.db.Where("user.fullname LIKE ?", searchText).Or("user.phone LIKE ?", searchText).Or("user.bank_account LIKE ?", searchText))
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Where("scammer.deleted_at IS NULL").
			Offset(req.Page * req.Limit).
			Scan(&scammers).
			Error; err != nil {
			return nil, nil, err
		}
	}
	return scammers, &total, nil
}

func (r repo) UpdateUserStatus(body model.ScammerUpdateUser) error {

	if err := r.db.Table("user").Where("id = ?", body.Id).
		Updates(&body).Error; err != nil {
		return err
	}

	return nil
}

func (r repo) DeleteScammer(id int64) error {

	if err := r.db.Table("scammer").
		Where("id = ?", id).
		Delete(&model.Scammer{}).
		Error; err != nil {
		return err
	}

	return nil
}

func (r repo) UpdateDeleteUserStatus(body model.ScammerDelete) error {

	if err := r.db.Table("user").Where("id = ?", body.Id).
		Updates(&body).Error; err != nil {
		return err
	}

	if err := r.db.Table("user").Where("id = ?", body.Id).
		Update("deleted_at", nil).Error; err != nil {
		return err
	}

	return nil
}

func (r repo) GetScammerById(id int64) (*model.ScammertList, error) {

	var scammers model.ScammertList
	selectedFields := "scammer.id as id, scammer.reason as reason, scammer.created_at as created_at"
	selectedFields += ", user.bank_id as bank_id, bank.name as bank_name, user.bank_account as bank_account"
	selectedFields += ", user.phone as phone, user.fullname as fullname , user.id as user_id"

	query := r.db.Table("scammer")
	query = query.Joins("LEFT JOIN user ON scammer.user_id = user.id")
	query = query.Joins("LEFT JOIN bank ON user.bank_id = bank.id")
	query = query.Select(selectedFields)
	query = query.Where("scammer.id = ?", id)
	if err := query.Take(&scammers).Error; err != nil {
		return nil, err
	}
	return &scammers, nil
}
func (r repo) GetScammerByUserId(id int64) (*model.ScammertList, error) {

	var scammers model.ScammertList
	selectedFields := "scammer.id as id, scammer.reason as reason, scammer.created_at as created_at"
	selectedFields += ", user.bank_id as bank_id, bank.name as bank_name, user.bank_account as bank_account"
	selectedFields += ", user.phone as phone, user.fullname as fullname , user.id as user_id"

	query := r.db.Table("scammer")
	query = query.Joins("LEFT JOIN user ON scammer.user_id = user.id")
	query = query.Joins("LEFT JOIN bank ON user.bank_id = bank.id")
	query = query.Select(selectedFields)
	query = query.Where("scammer.user_id = ?", id)
	if err := query.Scan(&scammers).Error; err != nil {
		return nil, err
	}
	return &scammers, nil
}

func (r repo) GetScammerSummary() (*model.ScammerSummary, error) {

	var response model.ScammerSummary
	var err error

	var total int64

	// แจ้งมิจฉาชีพ
	var scammerNumbers model.ScammerNumberListResponse
	if masterResp, err := r.GetScammerNumberList(); err != nil {
		return nil, err
	} else {
		scammerNumbers.PhoneList = masterResp.PhoneList
		scammerNumbers.BankNumberList = masterResp.BankNumberList
	}
	// No Scammer
	if len(scammerNumbers.PhoneList) == 0 && len(scammerNumbers.BankNumberList) == 0 {
		return &response, nil
	}

	execTotal := r.db.Table("user").Select("user.id")

	if len(scammerNumbers.PhoneList) > 0 && len(scammerNumbers.BankNumberList) > 0 {
		execTotal = execTotal.Where(r.db.Where("user.phone IN ?", scammerNumbers.PhoneList).Or("user.bank_account IN ?", scammerNumbers.BankNumberList))
	} else if len(scammerNumbers.PhoneList) > 0 {
		execTotal = execTotal.Where("user.phone IN ?", scammerNumbers.PhoneList)
	} else if len(scammerNumbers.BankNumberList) > 0 {
		execTotal = execTotal.Where("user.bank_account IN ?", scammerNumbers.BankNumberList)
	}

	execTotal = execTotal.Where("user.deleted_at IS NULL")
	if err = execTotal.Count(&total).Error; err != nil {
		return nil, err
	}
	response.TotalCount = total

	return &response, nil
}
