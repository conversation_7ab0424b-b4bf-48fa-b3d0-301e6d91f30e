package repository

import (
	"cybergame-api/model"
	"time"

	"gorm.io/gorm"
)

var confActivityDailyResponse *model.GetActivityDailyResponse

func NewActivityDailyRepository(db *gorm.DB) ActivityDailyRepository {
	return &repo{db}
}

type ActivityDailyRepository interface {
	// option
	GetActivityDailyCondition() ([]model.ActivityDailyConditionResponse, error)
	GetActivityDailyStatus() ([]model.ActivityDailyStatusResponse, error)
	// main
	GetFirstActivityDaily() (*model.GetActivityDailyResponse, error)
	CreateFirstActivityDaily(req model.CreateActivityDailyRequest) (int64, error)
	UpdateFirstActivityDaily(req model.UpdateActivityDailyRequest) error
	// web
	CreateActivityDailyUser(body model.CreateActivityDailyUserBody) (int64, error)
	//internal
	GetActivityDailyUserOnCurrentWeek(userId int64) ([]model.GetActivityDailyUserResponse, error)
	CheckDuplicateActivityDaily(req model.CheckDuplicateActivityDailyRequest) (*model.GetActivityDailyUserResponse, error)
	CheckDailyActivityUserTransactionList(req model.CheckDailyActivityUserTransactionListRequest) ([]model.UserTransaction, error)
	IncreaseUserCredit(body model.UserTransactionCreateRequest) (*model.UserTransactionCreateResponse, error)
	GetUserById(id int64) (*model.UserResponse, error)
	CheckConfirmActivityDailyByDailyKey(dailyKey string) (*model.CheckConfirmActivityDailyByDailyKeyResponse, error)

	CreateTurnoverUserStatement(body model.TurnoverUserStatementCreateBody) (*int64, error)
	UpdateTurnoverUserStatement(id int64, body model.TurnoverUserStatementUpdateBody) error
	CreateTurnoverUserWithdrawLog(body model.CreateTurnoverUserWithdrawLog) (*int64, error)
	GetTurnoverUserActivityDaily(req model.GetTurnoverUserActivityDailyRequest) ([]model.GetTurnoverUserActivityDailyResponse, int64, error)
}

func (r repo) GetActivityDailyCondition() ([]model.ActivityDailyConditionResponse, error) {

	var result []model.ActivityDailyConditionResponse
	selectedFields := "id, name, label_th, label_en"
	query := r.db.Table("activity_daily_condition")
	query = query.Select(selectedFields)
	if err := query.Find(&result).Error; err != nil {
		return nil, err
	}

	return result, nil
}

func (r repo) GetActivityDailyStatus() ([]model.ActivityDailyStatusResponse, error) {

	var result []model.ActivityDailyStatusResponse
	selectedFields := "id, name, label_th, label_en"
	query := r.db.Table("activity_daily_status")
	query = query.Select(selectedFields)
	if err := query.Find(&result).Error; err != nil {
		return nil, err
	}

	return result, nil
}

func (r repo) GetFirstActivityDaily() (*model.GetActivityDailyResponse, error) {

	var record model.GetActivityDailyResponse

	if confActivityDailyResponse != nil && time.Now().Before(confActivityDailyResponse.CacheExpiredAt) {
		return confActivityDailyResponse, nil
	}

	selectedFields := "activity_daily.id AS id, activity_daily.monday_bonus AS monday_bonus, activity_daily.tuesday_bonus AS tuesday_bonus, activity_daily.wednesday_bonus AS wednesday_bonus"
	selectedFields += ", activity_daily.thursday_bonus AS thursday_bonus, activity_daily.friday_bonus AS friday_bonus, activity_daily.saturday_bonus AS saturday_bonus, activity_daily.sunday_bonus AS sunday_bonus"
	selectedFields += ", activity_daily.completed_bonus AS completed_bonus"
	selectedFields += ", activity_daily.activity_daily_condition_id AS activity_daily_condition_id, activity_daily_condition.label_th AS activity_daily_condition_th"
	selectedFields += ", activity_daily.activity_daily_status_id AS activity_daily_status_id, activity_daily_status.label_th AS activity_daily_status_th"
	selectedFields += ", activity_daily.deposit_amount_condition AS deposit_amount_condition"

	query := r.db.Table("activity_daily")
	query = query.Joins("LEFT JOIN activity_daily_condition ON activity_daily.activity_daily_condition_id = activity_daily_condition.id")
	query = query.Joins("LEFT JOIN activity_daily_status ON activity_daily.activity_daily_status_id = activity_daily_status.id")
	query = query.Select(selectedFields)
	if err := query.First(&record).Error; err != nil {
		return nil, err
	}

	// Set Cache
	confActivityDailyResponse = &record
	confActivityDailyResponse.CacheExpiredAt = time.Now().Add(60 * time.Minute)

	return &record, nil
}

func (r repo) CreateFirstActivityDaily(req model.CreateActivityDailyRequest) (int64, error) {

	query := r.db.Table("activity_daily")
	query = query.Create(&req)
	if err := query.Error; err != nil {
		return 0, err
	}

	// Clear Cache = GetNewData Later
	confActivityDailyResponse = nil

	return req.Id, nil
}

func (r repo) UpdateFirstActivityDaily(req model.UpdateActivityDailyRequest) error {

	query := r.db.Table("activity_daily")
	query = query.Where("id = ?", req.Id)
	query = query.Updates(&req)
	if err := query.Error; err != nil {
		return err
	}

	// Clear Cache = GetNewData Later
	confActivityDailyResponse = nil

	return nil
}

func (r repo) CreateActivityDailyUser(body model.CreateActivityDailyUserBody) (int64, error) {

	query := r.db.Table("activity_daily_user")
	query = query.Create(&body)
	if err := query.Error; err != nil {
		return 0, err
	}
	return body.Id, nil
}

func (r repo) GetActivityDailyUserOnCurrentWeek(userId int64) ([]model.GetActivityDailyUserResponse, error) {
	selectedFields := "id, user_id, collected_bonus, collected_date, activity_day_id"
	var result []model.GetActivityDailyUserResponse

	var startDate, endDate time.Time
	referenceDate := time.Now()

	// Check if referenceDate is Sunday
	if referenceDate.Weekday() == time.Sunday {
		// If it's Sunday, set startDate to next Monday and endDate to the following Sunday
		startDate = referenceDate.AddDate(0, 0, -6)
		startDate = startDate.AddDate(0, 0, -int(startDate.Weekday())+1)
		endDate = startDate.AddDate(0, 0, 6)
	} else {
		// If it's not Sunday, proceed with your original logic
		startDate = referenceDate.AddDate(0, 0, -int(referenceDate.Weekday())+1)
		endDate = startDate.AddDate(0, 0, 6)
	}

	stringStartDate := startDate.Format("2006-01-02")
	stringEndDate := endDate.Format("2006-01-02")

	startDateAtBkk, err := r.ParseBodBkk(stringStartDate)
	if err != nil {
		return nil, err
	}

	endDateAtBkk, err := r.ParseEodBkk(stringEndDate)
	if err != nil {
		return nil, err
	}

	query := r.db.Table("activity_daily_user")
	query = query.Select(selectedFields)
	query = query.Where("user_id = ?", userId)
	query = query.Where("created_at BETWEEN ? AND ?", startDateAtBkk, endDateAtBkk)

	if err := query.Scan(&result).Error; err != nil {
		return nil, err
	}

	return result, nil
}

func (r repo) CheckDuplicateActivityDaily(req model.CheckDuplicateActivityDailyRequest) (*model.GetActivityDailyUserResponse, error) {

	var result model.GetActivityDailyUserResponse
	selectedFields := "id, user_id, collected_bonus, collected_date, activity_day_id"
	query := r.db.Table("activity_daily_user")
	query = query.Select(selectedFields)
	query = query.Where("user_id = ?", req.UserId)
	query = query.Where("collected_date LIKE ?", "%"+req.CollectedDateCheck+"%")
	query = query.Where("activity_day_id = ?", req.DayId)

	if err := query.First(&result).Error; err != nil {
		return nil, err
	}

	return &result, nil
}

func (r repo) CheckConfirmActivityDailyByDailyKey(dailyKey string) (*model.CheckConfirmActivityDailyByDailyKeyResponse, error) {

	var result model.CheckConfirmActivityDailyByDailyKeyResponse
	selectedFields := "id, daily_key, user_id, collected_bonus, collected_date, activity_day_id"
	query := r.db.Table("activity_daily_user")
	query = query.Select(selectedFields)
	query = query.Where("daily_key = ?", dailyKey)
	if err := query.First(&result).Error; err != nil {
		return nil, err
	}

	return &result, nil
}

func (r repo) CheckDailyActivityUserTransactionList(req model.CheckDailyActivityUserTransactionListRequest) ([]model.UserTransaction, error) {

	var list []model.UserTransaction
	var err error

	selectedFields := "logs.id AS id, logs.user_id AS user_id, logs.direction_id AS direction_id"
	selectedFields += ", logs.type_id AS type_id, logs.account_id AS account_id, logs.ref_id AS ref_id, logs.detail AS detail"
	selectedFields += ", logs.promotion_id AS promotion_id, logs.credit_before AS credit_before, logs.credit_back AS credit_back"
	selectedFields += ", logs.credit_amount AS credit_amount, logs.bonus_amount AS bonus_amount, logs.credit_after AS credit_after"
	selectedFields += ", logs.transfer_at AS transfer_at, logs.create_admin_id AS create_admin_id, logs.confirm_admin_id AS confirm_admin_id"
	selectedFields += ", logs.is_adjust_auto AS is_adjust_auto, logs.work_seconds AS work_seconds, logs.created_at AS created_at"
	selectedFields += ", logs.is_show AS is_show, logs.remove_admin_id AS remove_admin_id"

	query := r.db.Table("user_transaction as logs")
	query = query.Select(selectedFields)

	if req.FromDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.FromDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("logs.created_at >= ? ", startDateAtBkk)
	}
	if req.ToDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.ToDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("logs.created_at <=  ?", endDateAtBkk)
	}

	if err = query.
		Where("logs.removed_at IS NULL").
		Where("logs.user_id = ?", req.UserId).
		Where("logs.is_show = ?", true).
		Scan(&list).
		Error; err != nil {
		return nil, err
	}

	return list, nil
}

func (r repo) GetTurnoverUserActivityDaily(req model.GetTurnoverUserActivityDailyRequest) ([]model.GetTurnoverUserActivityDailyResponse, int64, error) {

	var list []model.GetTurnoverUserActivityDailyResponse
	var total int64
	var err error

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return list, 0, err
	}

	count := r.db.Table("turnover_statement as statements")
	count = count.Select("statements.id")
	count = count.Joins("LEFT JOIN user ON user.id = statements.user_id")
	count = count.Where("statements.type_id = ?", model.TURN_BONUS_ACTIVITY_DAILY)

	if req.MemberCode != "" {
		count = count.Where("user_id.member_code = ?", req.MemberCode)
	}

	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return list, 0, err
		}
		count = count.Where("statements.created_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return list, 0, err
		}
		count = count.Where("statements.created_at <=  ?", endDateAtBkk)
	}
	if err = count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "statements.id as id, statements.user_id as user_id, statements.type_id as type_id, statements.ref_type_id as ref_type_id, statements.status_id as status_id, statements.start_turn_amount as start_turn_amount"
		selectedFields += ", statements.promotion_name as description, statements.bonus_amount as bonus_amount"
		selectedFields += ", statements.start_turn_at as start_turn_at, statements.total_turn_amount as total_turn_amount, statements.end_turn_at as end_turn_at, statements.created_at as created_at, statements.updated_at as updated_at"
		selectedFields += ", types.name as type_name, statuses.name as status_name"
		selectedFields += ", user.member_code as member_code"
		selectedFields += ", user.fullname as fullname, user.username as username"
		selectedFields += ", activity_daily_user.activity_daily_condition_id as activity_daily_condition_id, activity_daily_condition.label_th as activity_daily_condition_name"
		selectedFields += ", activity_daily_user.tidturn_percent as tidturn_percent"
		selectedFields += ", activity_daily_user.amount_condition as amount_condition"

		query := r.db.Table("turnover_statement as statements")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN activity_daily_user ON activity_daily_user.id = statements.ref_type_id")
		query = query.Joins("LEFT JOIN activity_daily_condition ON activity_daily_condition.id = activity_daily_user.activity_daily_condition_id")

		query = query.Joins("LEFT JOIN turnover_statement_type as types ON types.id = statements.type_id")
		query = query.Joins("LEFT JOIN turnover_statement_status as statuses ON statuses.id = statements.status_id")
		query = query.Joins("LEFT JOIN user ON user.id = statements.user_id")
		query = query.Where("statements.type_id = ?", model.TURN_BONUS_ACTIVITY_DAILY)
		if req.MemberCode != "" {
			query = query.Where("user_id.member_code = ?", req.MemberCode)
		}

		if dateType.DateFrom != "" {
			startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
			if err != nil {
				return list, 0, err
			}
			query = query.Where("statements.created_at >= ? ", startDateAtBkk)
		}
		if dateType.DateTo != "" {
			endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
			if err != nil {
				return list, 0, err
			}
			query = query.Where("statements.created_at <=  ?", endDateAtBkk)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}
