package repository

import (
	"bytes"
	"cybergame-api/helper"
	"cybergame-api/model"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"

	"gorm.io/gorm"
)

func NewOtpRepository(db *gorm.DB) OtpRepository {
	return &repo{db}
}

type OtpRepository interface {
	RequestOtp(phone string) (*model.OtpSendResponse, error)
	VerifyOtp(body model.UserVerifyOtpBody) (*model.OtpVerifyResponse, error)
	// REF-SMSCREDIT
	DecreaseSmsCredit(smsCredit int64) error
	// OTP from our service
	GetCyberOtpKey() string
	SendCyberOtp(body model.SendCyberOtpRequest) (*model.SendCyberOtpResponse, error)
	VerifyCyberOtp(body model.VerifyCyberOtpRequest) (*model.VerifyCyberOtpResponse, error)
}

func (r repo) RequestOtp(phone string) (*model.OtpSendResponse, error) {

	if len(phone) > 3 {
		phone = "66" + phone[1:]
	} else {
		log.Println("INVALID.RequestOtp.Phone: ", phone)
		return nil, fmt.Errorf("INVALID.RequestOtp.Phone: %s", phone)
	}

	url := fmt.Sprintf("%s/otp/requestOTP", os.Getenv("ANTS_API"))

	// client := &http.Client{}
	// client.Timeout = 3 * time.Second

	body := model.OtpRequestBody{
		Otcid:  os.Getenv("ANTS_OTC_ID"),
		Mobile: phone,
	}

	jsonBody, _ := json.Marshal(body)
	reqBody := bytes.NewBuffer(jsonBody)

	call := http.Client{}
	req, err := http.NewRequest("POST", url, reqBody)
	if err != nil {
		return nil, err
	}

	user := os.Getenv("ANTS_USER")
	pass := os.Getenv("ANTS_PASSWORD")

	token := base64.StdEncoding.EncodeToString([]byte(user + ":" + pass))

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Basic "+token)
	req.Header.Set("Accept", "application/json")

	resp, err := call.Do(req)
	if err != nil {
		return nil, err
	}

	defer resp.Body.Close()

	// Convert response body to string
	respBodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	respBodyString := string(respBodyBytes)
	fmt.Println("Response Body: ", respBodyString)
	// Response Body:  {"error":{"message":"Authentication failed","description":"","code":null}}
	// Response Body:  {"otcId":"185e44cd-1032-4b94-b962-33155d682f34","otpId":"cf9a59a0-0e12-43f6-b1e3-a9e9e5a17002","referenceCode":"pJ3Ie8TdZ","success":{"message":"Success","description":"","code":"000"}}

	var res model.OtpSendResponse
	// if err := json.NewDecoder(resp.Body).Decode(&res); err != nil {
	// 	log.Println("RequestOtp.Decode: ", err)
	// 	return nil, err
	// }
	// Use json.Unmarshal instead of json.NewDecoder
	if err := json.Unmarshal(respBodyBytes, &res); err != nil {
		log.Println("RequestOtp.Unmarshal: ", err)
		return nil, err
	}

	fmt.Println("RequestOtp.Response: ", helper.StructJson(res))
	// RequestOtp.Response:  {"otcId":"","otpId":"","referenceCode":"","success":null}

	return &res, nil
}

func (r repo) VerifyOtp(body model.UserVerifyOtpBody) (*model.OtpVerifyResponse, error) {

	url := fmt.Sprintf("%s/otp/verifyOTP", os.Getenv("ANTS_API"))

	// client := &http.Client{}
	// client.Timeout = 3 * time.Second

	otp := model.OtpVerifyBody{
		OtpId:   body.OtpId,
		OtpCode: body.Code,
	}

	jsonBody, _ := json.Marshal(otp)
	reqBody := bytes.NewBuffer(jsonBody)

	call := http.Client{}
	req, err := http.NewRequest("POST", url, reqBody)
	if err != nil {
		return nil, err
	}

	user := os.Getenv("ANTS_USER")
	pass := os.Getenv("ANTS_PASSWORD")

	token := base64.StdEncoding.EncodeToString([]byte(user + ":" + pass))

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Basic "+token)
	req.Header.Set("Accept", "application/json")

	resp, err := call.Do(req)
	if err != nil {
		return nil, err
	}

	defer resp.Body.Close()

	var res model.OtpVerifyResponse
	if err := json.NewDecoder(resp.Body).Decode(&res); err != nil {
		return nil, err
	}

	return &res, nil
}
