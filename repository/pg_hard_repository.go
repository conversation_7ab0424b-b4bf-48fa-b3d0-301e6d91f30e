package repository

import (
	"bytes"
	"cybergame-api/helper"
	"cybergame-api/model"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"time"

	"gorm.io/gorm"
)

func NewPgHardRepository(db *gorm.DB) PgHardRepository {
	return &repo{db}
}

type PgHardRepository interface {
	// game list
	GetPgHardGameList(urlDetail model.CallApiAgentPgHardDetail) ([]model.GetPgHardGameListResponse, error)
	// operator preset settings
	GetPgHardOperatorPreset(urlDetail model.CallApiAgentPgHardDetail) (*model.GetPgOperatorPresetResponseList, error)
	// login game init
	GetPgHardGameSessionInit(reqBody model.GetPgHardGameSessionParams, urlDetail model.CallApiAgentPgHardDetail) (map[string]interface{}, error)

	// internal
	GetMemberCode(id int64) (*string, error)
	GetUserByMemberCode(memberCode string) (*model.GetUserByMemberCode, error)
	DecreaseUserCreditFromOtherAgent(body model.DecreaseUserCreditFromOtherAgentRequest) (*model.UserTransactionCreateResponse, error)
	IncreaseUserCreditFromOtherAgent(body model.IncreaseUserCreditFromOtherAgentRequest) (*model.UserTransactionCreateResponse, error)
	CreateAgentPgHardCallback(body model.CreateAgentPgHardCallbackBody) (int64, error)
	UpdateAgentPgHardCallback(body model.UpdateAgentPgHardCallbackBody) error
	GetAgentPgHardCallback(req model.AgentPgHardCallbackSummaryRequest) ([]model.AgentPgHardCallbackSummary, error)
	GetPgHardCallbackSummary(req model.PgHardCallbackSummaryRequest) ([]model.PgHardCallbackSummaryResponse, error)
	AgentPgHardCallbackSummaryReportList(req model.AgentPgHardCallbackSummaryReportRequest) ([]model.AgentPgHardCallbackSummaryReportResponse, error)
	CreateAgentPgHardCallbackSummaryReport(body []model.CreateAgentPgHardCallbackSummaryReport) error
	UpdateAgentPgHardCallbackSummaryReport(body model.UpdateAgentPgHardCallbackSummaryReport) error

	// admin
	GetAgentPgHardSetting() (*model.GetAgentPgHardSetting, error)
	GetInternalAgentPgHardSetting() (*model.GetInternalAgentPgHardSetting, error)
	UpdateAgentPgHardSetting(body model.UpdateAgentPgHardSetting) error

	// clear cache
	ClearCacheAgentPgHardSetting()
	CronjobDeletePgHardCallBack() error
}

func (r repo) GetPgHardGameList(urlDetail model.CallApiAgentPgHardDetail) ([]model.GetPgHardGameListResponse, error) {
	// เคยเป็น env แล้ว แต่ ให้ปรับไปอยู่ใน db แทน
	pgHardPrivateKey := urlDetail.PgHardPrivateKey
	pgHardUrl := urlDetail.PgHardUrl
	url := fmt.Sprintf("%s/external/games", pgHardUrl)

	requestBody, err := json.Marshal(map[string]interface{}{})
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request body: %w", err)
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", pgHardPrivateKey)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	resp, err := client.Do(req)
	if err != nil {
		log.Println("ERROR.GetPgHardOperatorPreset", err)
		return nil, fmt.Errorf("ค่ายเกมไม่สามารถเชื่อมต่อได้ลองใหม่อีกครั้ง")
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		return nil, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	var games []model.GetPgHardGameListResponse
	decoder := json.NewDecoder(resp.Body)
	if err := decoder.Decode(&games); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return games, nil
}

func (r repo) GetPgHardOperatorPreset(urlDetail model.CallApiAgentPgHardDetail) (*model.GetPgOperatorPresetResponseList, error) {
	// เคยเป็น env แล้ว แต่ ให้ปรับไปอยู่ใน db แทน
	pgHardPrivateKey := urlDetail.PgHardPrivateKey
	pgHardUrl := urlDetail.PgHardUrl
	url := fmt.Sprintf("%s/external/operators/presets", pgHardUrl)

	// Prepare the request body
	requestBody, err := json.Marshal(map[string]interface{}{
		"operatorId": urlDetail.PgHardOperatorId,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request body: %w", err)
	}

	// Create the HTTP request
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	req.Header.Set("Authorization", pgHardPrivateKey)
	req.Header.Set("Content-Type", "application/json")

	// Send the request
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	resp, err := client.Do(req)
	if err != nil {
		log.Println("ERROR.GetPgHardOperatorPreset", err)
		return nil, fmt.Errorf("ค่ายเกมไม่สามารถเชื่อมต่อได้ลองใหม่อีกครั้ง")
	}
	defer resp.Body.Close()

	// Check the response status code
	if resp.StatusCode != http.StatusCreated {
		return nil, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	// Decode the response
	var presetList model.GetPgOperatorPresetResponseList
	decoder := json.NewDecoder(resp.Body)
	if err := decoder.Decode(&presetList); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &presetList, nil
}

// // encode with PG_HARD_OPERATOR_ID
// func (r repo) EncodePgHardOperatorID(userToken string) string {
// 	operatorID := os.s("PG_HARD_OPERATOR_ID")
// 	if operatorID == "" {
// 		fmt.Println("PG_HARD_OPERATOR_ID is not set")
// 		return ""
// 	}

// 	// Concatenate operatorID and userToken
// 	combined := operatorID + userToken

// 	// Generate SHA-256 hash of the combined string
// 	hash := sha256.Sum256([]byte(combined))

// 	// Convert the hash to a hexadecimal string
// 	return hex.EncodeToString(hash[:])
// }

func (r repo) GetPgHardGameSessionInit(reqBody model.GetPgHardGameSessionParams, urlDetail model.CallApiAgentPgHardDetail) (map[string]interface{}, error) {

	log.Println("START LOGIN PLAY TIME ------> " + time.Now().Format("2006-01-02 15:04:05.000"))
	log.Println("reqBody ------> ", helper.StructJson(reqBody))
	log.Println("urlDetail ------> ", helper.StructJson(urlDetail))
	pgHardPrivateKey := urlDetail.PgHardPrivateKey
	pgHardUrl := urlDetail.PgHardUrl
	url := fmt.Sprintf("%s/external/operators/init-session", pgHardUrl)

	requestBody, err := json.Marshal(reqBody)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request body: %w", err)
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", pgHardPrivateKey)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{
		Timeout: 120 * time.Second,
	}

	resp, err := client.Do(req)
	if err != nil {
		log.Println("ERROR.GetPgHardGameSessionInit", err)
		return nil, fmt.Errorf("ค่ายเกมไม่สามารถเชื่อมต่อได้ลองใหม่อีกครั้ง")
	}

	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		bodyBytes, _ := io.ReadAll(resp.Body)
		log.Println("ERROR.GetPgHardGameSessionInit: Status =", resp.StatusCode)
		log.Println("ERROR.GetPgHardGameSessionInit: Body =", string(bodyBytes))
		return nil, fmt.Errorf("ค่ายเกมไม่สามารถเชื่อมต่อได้ลองใหม่อีกครั้ง")
	}

	var session map[string]interface{}
	decoder := json.NewDecoder(resp.Body)
	if err := decoder.Decode(&session); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return session, nil
}

func (r repo) DecreaseUserCreditFromOtherAgent(body model.DecreaseUserCreditFromOtherAgentRequest) (*model.UserTransactionCreateResponse, error) {

	fmt.Printf("START DecreaseUserCreditFromOtherAgent TIME : %s\n", time.Now().Format("2006-01-02 15:04:05.000"))
	var result model.UserTransactionCreateResponse

	user, err := r.GetMemberById(body.UserId)
	if err != nil {
		return nil, err
	}
	fmt.Println("user", helper.StructJson(user))

	var transferAt time.Time
	transferAt = time.Now().UTC()
	// Agent
	agentProvider := os.Getenv("AGENT_PROVIDER")
	if agentProvider == "AMB" {
		agentData := model.AmbWithdraw{}
		agentData.Username = user.MemberCode
		agentData.Balance = body.Amount
		gameRes, err := r.AmbWithdrawAgentFromOtherAgent(agentData)
		if err != nil {
			return nil, err
		}
		result.TransferAt = transferAt
		result.AgentSuccess = true
		result.AgentBeforeAmount = gameRes.Data.BeforeCredit
		result.AgentAfterAmount = gameRes.Data.AfterCredit
	} else {
		agentData := model.TransactionAgentRequest{}
		agentData.UserId = user.Id
		agentData.PlayerName = user.MemberCode
		agentData.Amount = body.Amount
		gameRes, err := r.AgcWithdrawAgentForOtherAgent(agentData)
		if err != nil {
			log.Println("ERROR.DecreaseUserCredit.WithdrawAgent", err)
			return nil, err
		}
		result.TransferAt = transferAt
		result.AgentSuccess = gameRes.Success
		result.AgentBeforeAmount = gameRes.BeforeAmount
		result.AgentAfterAmount = gameRes.AfterAmount
	}
	fmt.Printf("DecreaseUserCreditFromOtherAgent RESULT : %s\n", helper.StructJson(result))
	fmt.Printf("END DecreaseUserCreditFromOtherAgent TIME : %s\n", time.Now().Format("2006-01-02 15:04:05.000"))

	return &result, nil
}

func (r repo) IncreaseUserCreditFromOtherAgent(body model.IncreaseUserCreditFromOtherAgentRequest) (*model.UserTransactionCreateResponse, error) {

	fmt.Printf("START IncreaseUserCreditFromOtherAgent TIME : %s\n", time.Now().Format("2006-01-02 15:04:05.000"))
	var result model.UserTransactionCreateResponse

	var transferAt time.Time
	transferAt = time.Now().UTC()
	totalAmount := body.Amount
	if totalAmount <= 0 {
		return nil, errors.New("INVALID_AMOUNT")
	}
	// [20240301] แก้บัค ทศนิยมเกิน แต่จริงๆส่งทศนิยมได้หลายหลัก แต่เลขทั้งหมดห้ามเกิน 8-9 หลัก
	totalAmountSatang := int64(totalAmount * 100)
	totalAmount = float64(totalAmountSatang) / 100

	user, err := r.getMemberById(body.UserId)
	if err != nil {
		return nil, err
	}
	fmt.Println("user", helper.StructJson(user))

	// Agent
	agentProvider := os.Getenv("AGENT_PROVIDER")
	if agentProvider == "AMB" {
		agentData := model.AmbDeposit{}
		agentData.Username = user.MemberCode
		agentData.Balance = totalAmount
		agentData.IsDp = false // ไม่ติดเทิน
		gameRes, err := r.AmbDepositAgentForOtherAgent(agentData)
		if err != nil {
			return nil, err
		}
		result.TransferAt = transferAt
		result.AgentSuccess = true
		result.AgentBeforeAmount = gameRes.Data.BeforeCredit
		result.AgentAfterAmount = gameRes.Data.AfterCredit
	} else {
		agentData := model.TransactionAgentRequest{}
		agentData.UserId = user.Id
		agentData.PlayerName = user.MemberCode
		agentData.Amount = totalAmount
		gameRes, err := r.AgcDepositAgentForOtherAgent(agentData)
		if err != nil {
			return nil, err
		}
		result.TransferAt = transferAt
		result.AgentSuccess = gameRes.Success
		result.AgentBeforeAmount = gameRes.BeforeAmount
		result.AgentAfterAmount = gameRes.AfterAmount
	}
	fmt.Printf("IncreaseUserCreditFromOtherAgent RESULT : %s\n", helper.StructJson(result))
	fmt.Printf("END IncreaseUserCreditFromOtherAgent TIME : %s\n", time.Now().Format("2006-01-02 15:04:05.000"))

	return &result, nil
}

func (r repo) CreateAgentPgHardCallback(body model.CreateAgentPgHardCallbackBody) (int64, error) {
	query := r.db.Table("agent_pg_hard_callback")
	query = query.Create(&body)
	if err := query.Error; err != nil {
		return 0, err
	}

	return body.Id, nil
}

func (r repo) UpdateAgentPgHardCallback(body model.UpdateAgentPgHardCallbackBody) error {

	query := r.db.Table("agent_pg_hard_callback")
	query = query.Where("id = ?", body.Id)
	query = query.Updates(&body)
	if err := query.Error; err != nil {
		return err
	}

	return nil
}

func (r repo) GetAgentPgHardCallback(req model.AgentPgHardCallbackSummaryRequest) ([]model.AgentPgHardCallbackSummary, error) {

	var result []model.AgentPgHardCallbackSummary
	selectFields := "user_id, member_code, sum(payoff) as total_payoff, sum(bet_amount) as total_bet"
	selectFields += ", sum(winlose_amount) as total_winlose"
	query := r.db.Table("agent_pg_hard_callback")
	query = query.Select(selectFields)
	query = query.Where("is_success = ?", 1)

	if req.StatementDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.StatementDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("created_at >= ?", startDateAtBkk)

		endDateAtBkk, err := r.ParseEodBkk(req.StatementDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("created_at <= ?", endDateAtBkk)
	}
	query = query.Group("user_id, member_code")
	query = query.Offset((req.PageIndex - 1) * req.PageSize).Limit(req.PageSize)
	query = query.Scan(&result)
	if err := query.Error; err != nil {
		return nil, err
	}

	return result, nil
}

func (r repo) GetAgentPgHardSetting() (*model.GetAgentPgHardSetting, error) {

	var result model.GetAgentPgHardSetting
	selectFields := "is_active, pg_hard_preset_id"
	query := r.db.Table("agent_pg_hard_setting")
	query = query.Select(selectFields)
	query = query.Where("id = ?", 1)
	query = query.Take(&result)
	if err := query.Error; err != nil {
		return nil, err
	}

	return &result, nil
}

var saveCacheAgentPgHardSetting *model.GetInternalAgentPgHardSetting

func (r repo) GetInternalAgentPgHardSetting() (*model.GetInternalAgentPgHardSetting, error) {

	if saveCacheAgentPgHardSetting != nil {
		return saveCacheAgentPgHardSetting, nil
	}

	var result model.GetInternalAgentPgHardSetting

	selectFields := "id, is_active, program_allow_use"
	selectFields += ", pg_hard_private_key, pg_hard_operator_id, pg_hard_url, pg_hard_href_back_url"
	selectFields += ", pg_hard_preset_id"

	query := r.db.Table("agent_pg_hard_setting")
	query = query.Where("id = ?", 1)
	query = query.Select(selectFields)
	query = query.Take(&result)
	if err := query.Error; err != nil {
		return nil, err
	}

	saveCacheAgentPgHardSetting = &result

	return &result, nil
}

func (r repo) UpdateAgentPgHardSetting(body model.UpdateAgentPgHardSetting) error {

	query := r.db.Table("agent_pg_hard_setting")
	query = query.Where("id = ?", 1)
	query = query.Updates(&body)
	if err := query.Error; err != nil {
		return err
	}

	saveCacheAgentPgHardSetting = nil

	return nil
}

func (r repo) ClearCacheAgentPgHardSetting() {
	saveCacheAgentPgHardSetting = nil
}

func (r repo) CronjobDeletePgHardCallBack() error {

	CurrentDate := time.Now().UTC().Add(time.Hour * 7).Format("2006-01-02")
	startDateAtBkk, err := r.ParseBodBkk(CurrentDate)
	if err != nil {
		return err
	}

	if err := r.db.Unscoped().Table("agent_pg_hard_callback").Where("DATEDIFF(?, created_at) > 3", startDateAtBkk).Delete(&model.AgentLog{}).Error; err != nil {
		return err
	}

	return nil
}

func (r repo) GetPgHardCallbackSummary(req model.PgHardCallbackSummaryRequest) ([]model.PgHardCallbackSummaryResponse, error) {
	var result []model.PgHardCallbackSummaryResponse

	selectFields := "user_id, member_code, sum(payoff) as total_payoff, sum(bet_amount) as total_bet"
	selectFields += ", sum(payoff) as payoff"
	selectFields += ", sum(bet_amount) as bet_amount"
	selectFields += ", sum(winlose_amount) as winlose_amount"
	selectFields += ", sum(before_balance) as before_balance"
	selectFields += ", sum(after_balance) as after_balance"

	selectFields += ", DATE(CONVERT_TZ(created_at, '+00:00', '+07:00')) as created_at"
	query := r.db.Table("agent_pg_hard_callback")
	query = query.Select(selectFields)
	query = query.Where("is_success = ?", 1)
	if req.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.DateFrom)
		if err != nil {
			return nil, err
		}
		query = query.Where("created_at >= ?", startDateAtBkk)

		endDateAtBkk, err := r.ParseEodBkk(req.DateTo)
		if err != nil {
			return nil, err
		}
		query = query.Where("created_at <= ?", endDateAtBkk)
	}
	query = query.Group("user_id, member_code, DATE(CONVERT_TZ(created_at, '+00:00', '+07:00'))")
	query = query.Scan(&result)
	if err := query.Error; err != nil {
		return nil, err
	}

	return result, nil
}

func (r repo) AgentPgHardCallbackSummaryReportList(req model.AgentPgHardCallbackSummaryReportRequest) ([]model.AgentPgHardCallbackSummaryReportResponse, error) {
	var result []model.AgentPgHardCallbackSummaryReportResponse

	selectFields := "id, user_id, statement_date, member_code"
	selectFields += ", payoff, bet_amount, winlose_amount"
	selectFields += ", before_balance, after_balance"

	query := r.db.Table("agent_pg_hard_callback_summary")
	query = query.Select(selectFields)
	if req.DateFrom != "" {
		query = query.Where("created_at >= ?", req.DateFrom)
	}
	if req.DateTo != "" {
		query = query.Where("created_at <= ?", req.DateTo)
	}
	query = query.Scan(&result)
	if err := query.Error; err != nil {
		return nil, err
	}

	return result, nil
}

func (r repo) CreateAgentPgHardCallbackSummaryReport(body []model.CreateAgentPgHardCallbackSummaryReport) error {
	query := r.db.Table("agent_pg_hard_callback_summary")
	query = query.Create(&body)
	if err := query.Error; err != nil {
		return err
	}

	return nil
}

func (r repo) UpdateAgentPgHardCallbackSummaryReport(body model.UpdateAgentPgHardCallbackSummaryReport) error {
	query := r.db.Table("agent_pg_hard_callback_summary")
	query = query.Where("id = ?", body.Id)
	query = query.Updates(&body)
	if err := query.Error; err != nil {
		return err
	}

	return nil
}
