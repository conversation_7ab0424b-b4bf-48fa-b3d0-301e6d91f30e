package repository

import (
	"cybergame-api/model"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"time"

	"gorm.io/gorm"
)

func NewGormRepository(db *gorm.DB) GormRepository {
	return &repo{db}
}

type GormRepository interface {
	GetTableSchema() ([]model.GormTable, error)
	GetTableSize(database string) ([]model.GormTableSize, error)
	// Setting
	GetGormSetting() (*model.GormSettingResponse, error)
	SetGormSetting(id int64, setting model.GormSettingUpdateRequest) error
	// AutomateCheck
	CompareTableSchema() ([]model.GormTable, error)
	// Remote self
	GetRemoteTableSchema(ep string, key string) ([]model.GormTable, error)
}

func (r repo) GetTableSchema() ([]model.GormTable, error) {

	var result []model.GormTable
	var tables []string

	// All tables
	if err := r.db.Raw("SHOW TABLES").Scan(&tables).Error; err != nil {
		return nil, err
	}

	// for each table get columns
	for _, table := range tables {
		var columns []model.GormColumn
		if err := r.db.Raw("SHOW COLUMNS FROM `" + table + "`").Scan(&columns).Error; err != nil {
			return nil, err
		}
		result = append(result, model.GormTable{
			Name:    table,
			Columns: columns,
		})
	}

	return result, nil
}

func (r repo) GetTableSize(database string) ([]model.GormTableSize, error) {

	var result []model.GormTableSize
	var tables []string

	// All tables
	if err := r.db.Raw("SHOW TABLES").Scan(&tables).Error; err != nil {
		return nil, err
	}
	// analyze each table = UPDATE LATEST SIZE
	for _, table := range tables {
		if err := r.db.Exec("ANALYZE TABLE `" + table + "`").Error; err != nil {
			return nil, err
		}
	}

	sql := "SELECT table_schema as `database`, table_name AS `table`, round(((data_length + index_length) / 1024 / 1024), 2) `size_mb` FROM information_schema.TABLES "
	if database != "" {
		sql += "WHERE table_schema = '" + database + "' "
	}
	sql += "ORDER BY (data_length + index_length) DESC;"
	// All database size
	if err := r.db.Raw(sql).Scan(&result).Error; err != nil {
		return nil, err
	}

	// sql2 = "DELETE FROM webhook_log WHERE created_at < DATE_SUB(NOW(), INTERVAL 60 DAY);"

	return result, nil
}

func (r repo) CompareTableSchema() ([]model.GormTable, error) {

	var result []model.GormTable
	var tables []string

	// All tables
	if err := r.db.Raw("SHOW TABLES").Scan(&tables).Error; err != nil {
		return nil, err
	}

	// for each table get columns
	for _, table := range tables {
		var columns []model.GormColumn
		if err := r.db.Raw("SHOW COLUMNS FROM `" + table + "`").Scan(&columns).Error; err != nil {
			return nil, err
		}
		result = append(result, model.GormTable{
			Name:    table,
			Columns: columns,
		})
	}
	return result, nil
}

func (r *repo) GetGormSetting() (*model.GormSettingResponse, error) {

	var record model.GormSettingResponse

	selectedFields := "*"
	if err := r.db.Table("gorm_setting AS tb_setting").
		Select(selectedFields).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r *repo) SetGormSetting(id int64, body model.GormSettingUpdateRequest) error {

	if err := r.db.Table("gorm_setting").Where("id = ?", id).Updates(body).Error; err != nil {
		return err
	}
	return nil
}

func (r *repo) GetRemoteTableSchema(ep string, key string) ([]model.GormTable, error) {

	// @Router /gorm-comp/table-schema/{gkey} [get]
	epUrl := fmt.Sprintf("%s/api/gorm-comp/table-schema/%s", ep, key)
	log.Println("GetRemoteTableSchema url ------> ", epUrl)

	client := &http.Client{}
	client.Timeout = 30 * time.Second

	reqExternal, _ := http.NewRequest("GET", epUrl, nil)
	reqExternal.Header.Set("Content-Type", "application/json")
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Println(err.Error())
		return nil, errors.New("INVALID_EXTERNAL_CALL")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, errors.New("INVALID_RESPONSE_DATA")
	}

	// fmt.Println("GetRemoteTableSchema.resp.Body", string(responseData))

	if response.StatusCode != 200 {
		log.Println(response.StatusCode)
		log.Println(string(responseData))
		var errMsg2 model.ErrorRemoteResponse
		errJson2 := json.Unmarshal(responseData, &errMsg2)
		if errJson2 == nil {
			return nil, errors.New(errMsg2.Message)
		} else {
			log.Println("GetRemoteTableSchema.Unmarshal.errJson2 ------> ", errJson2)
		}
		return nil, errors.New("INVALID_RESPONSE_CODE")
	}

	var result []model.GormTable
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("GetRemoteTableSchema resp.Body ------> ", string(responseData))
		log.Println("GetRemoteTableSchema.Unmarshal.Err ------> ", errJson)
		// TRY-3-err2
		var errMsg3 model.ErrorRemoteResponse
		errJson3 := json.Unmarshal(responseData, &errMsg3)
		if errJson3 == nil {
			return nil, errors.New(errMsg3.Message)
		} else {
			log.Println("GetRemoteTableSchema.Unmarshal.errJson3 ------> ", errJson3)
		}
		return nil, errors.New("INVALID_UNMARSHAL_JSON")
	}

	// fmt.Println("GetRemoteTableSchema.result", result)

	return result, nil
}
