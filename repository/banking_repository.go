package repository

import (
	"bytes"
	"cybergame-api/helper"
	"cybergame-api/model"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm"
)

func NewBankingRepository(db *gorm.DB) BankingRepository {
	return &repo{db}
}

type BankingRepository interface {
	GetDb() *gorm.DB
	GetUser(id int64) (*model.UserDetail, error)
	GetTransactionType() ([]model.TransactionTypeResponse, error)
	GetStatementType() ([]model.StatementTypeResponse, error)

	GetBankStatementById(id int64) (*model.BankStatement, error)
	GetBankStatements(req model.BankStatementListRequest) (*model.SuccessWithPagination, error)
	GetBankExternalStatements(externalIds []int64) (*model.SuccessWithPagination, error)
	GetBankExternalStatementMaps(externalIds []int64) (map[int64]model.BankStatement, error)
	GetBankStatementByExternalId(externalId int64) (*model.BankStatement, error)
	GetBankStatementSummary(req model.BankStatementListRequest) (*model.BankStatementSummary, error)
	CreateBankStatement(data model.BankStatementCreateBody) (*int64, error)
	UpdateBankStatementStatus(data model.BankStatementUpdateBody) error

	IgnoreStatementOwner(id int64, data model.BankStatementUpdateBody) error
	DeleteBankStatement(id int64) error

	GetBankTransactionById(id int64) (*model.BankTransaction, error)
	GetBankTransactions(req model.BankTransactionListRequest) (*model.SuccessWithPagination, error)
	InsertBankTransaction(data model.BankTransactionCreateBody) (*int64, error)
	GetBankTransactionStatusCount(req model.BankTransactionListRequest) (*model.SuccessWithPagination, error)
	CreateBankDepositTransactionNoOwner(data model.BankTransactionNoOwnerCreateBody) (*int64, error)
	UpdateDepositTransactionOwner(statementid int64, data model.BankTransactionUpdateOwnerBody) (*int64, error)
	CreateBankWithdrawTransaction(data model.BankTransactionCreateBody) (*int64, error)
	// UnusedCreateBonusTransaction(data model.BonusTransactionCreateBody) error
	UpdateBankTransaction(id int64, data interface{}) error
	DeleteBankTransaction(id int64) error

	// IMG
	UploadImageToCloudflare(pathUplaod string, filename string, fileReader io.Reader) (*model.CloudFlareUploadCreateBody, error)

	GetPendingDepositTransactions(req model.PendingDepositTransactionListRequest) (*model.SuccessWithPagination, error)
	GetPendingWithdrawTransactions(req model.PendingWithdrawTransactionListRequest) (*model.SuccessWithPagination, error)
	CreateTransactionAction(data model.CreateBankTransactionActionBody) (*int64, error)
	RollbackTransactionAction(actionId int64) error
	CreateStatementAction(data model.CreateBankStatementActionBody) error
	ConfirmPendingDepositTransaction(id int64, data model.BankDepositTransactionConfirmBody) error
	ConfirmPendingCreditDepositTransaction(id int64, data model.BankDepositTransactionConfirmBody) error
	CheckMemeberHasEnoughtCredit(memberId int64, creditAmount float64) error
	ConfirmPendingWithdrawTransaction(id int64, data model.BankWithdrawTransactionConfirmBody) error
	ConfirmPendingWithdrawTransfer(id int64, data model.BankWithdrawTransactionConfirmBody) error
	CancelPendingTransaction(id int64, data model.BankTransactionCancelBody) error
	// GetFinishedTransactions(req model.FinishedTransactionListRequest) (*model.SuccessWithPagination, error)
	RemoveFinishedTransaction(id int64, data model.BankTransactionRemoveBody) error
	GetRemovedTransactions(req model.RemovedTransactionListRequest) (*model.SuccessWithPagination, error)

	GetMemberById(id int64) (*model.Member, error)
	GetMemberByCode(code string) ([]model.MemberForDropdown, error)
	GetMembers(req model.MemberListRequest) (*model.SuccessWithPagination, error)
	// GetPossibleStatementOwners(req model.MemberPossibleListRequest) (*model.SuccessWithPagination, error)
	GetMemberTransactions(req model.MemberTransactionListRequest) (*model.SuccessWithPagination, error)
	GetMemberTransactionSummary(req model.MemberTransactionListRequest) (*model.MemberTransactionSummary, error)
	IncreaseMemberCredit(body model.MemberStatementCreateBody) error
	DecreaseMemberCredit(body model.MemberStatementCreateBody) error

	TransferExternalAccount(body model.ExternalAccountTransferBody) error
	SetExternalStatementRead(body model.ExternalStatementSetReadBody) error

	GetMemberStatementById(id int64) (*model.MemberStatementResponse, error)
	GetMemberStatements(req model.MemberStatementListRequest) (*model.SuccessWithPagination, error)
	GetMemberStatementTypeByCode(code string) (*model.MemberStatementType, error)
	GetMemberStatementTypes(req model.SimpleListRequest) (*model.SuccessWithPagination, error)
	CreateMemberStatement(data model.MemberStatementCreateBody) (*int64, error)
	//ทำการเช็ค ว่าตรงกับ user คนไหน
	GetTransactionByStatementId(statementId int64) (model.BankTransaction, error)
	GetPossibleOwnersByStatementId(req model.GetPossibleOwnersRequest) ([]model.Member, int64, error)

	// backoffice
	IsFirstDeposit(userId int64) bool
	SetUserFirstDeposit(body model.UserFirstDepositCreateBody, transId *int64) (*int64, error)
	RemoveUserFirstDeposit(userId int64) error

	GetBankTransactionDepositList(req model.GetBankTransactionDepositListRequest) ([]model.GetBankTransactionDepositListResponse, int64, error)
	CreateUserTransaction(body model.CreateUserStatement) (*int64, error)
	GetDepositTransactionById(transId int64) (*model.GetBankTransactionDepositListResponse, error)
	GetBankTransactionWithdrawList(req model.GetBankTransactionWithdrawListRequest) ([]model.GetBankTransactionWithdrawListResponse, int64, error)
	GetTransactionWithdrawById(transId int64) (*model.GetBankTransactionWithdrawListResponse, error)
	UpdateUserTransactionStatus(body model.UpdateUserTransactionStatusRequest) error
	GetUserBankDetailByMemberCode(memberCode string) (*model.UserBankDetailBody, error)
	GetBankTransactionSuccess(req model.GetBankTransactionSuccessListRequest) ([]model.GetBankTransactionSuccessListResponse, int64, error)
	RemovedTransaction(body model.RemovedTransactionBody) error
	GetTransactionRemovedById(id int64) (model.BankTransaction, error)
	RemovedSuccessTransactionList(req model.RequestRemovedSuccessTransactionList) ([]model.RemovedSuccessTransactionList, int64, error)
	UpdateConfirmTransactionRetry(id int64, body model.UpdateConfirmTransactionRetry) error

	//user web
	GetWebUserTransactionList(req model.GetUserTransactionListRequest) ([]model.GetUserTransactionListBody, int64, error)

	//web withdrawal
	GetConfiguration() (*model.ConfigurationResponse, error)
	GetUserBankDetailById(userId int64) (*model.UserBankDetailBody, error)
	GetBankWithdrawConfig(amount float64) (*model.BankAccount, *string, error)

	UpdateAdminAndTransactionStatus(id int64, body model.UpdateConfirmAutoWithdrawBody) error
	UpdateUserTransactionConfirmBy(id int64, body model.UpdateConfirmAutoWithdrawBody) error
	WithdrawWithFastBank(transferBody model.WithdrawTransferFastBankBody) (*model.WithdrawTransferFastBankResponse, error)

	//admin action withdraw
	GetBankAccountById(id int64) (*model.BankAccount, error)
	GetTransactionWithdrawOverMaxById(transactionId int64) (model.BankTransaction, error)
	GetTransactionWithdrawOverBudget(id int64) (model.BankTransaction, error)

	CountDepositStatus(req model.GetBankTransactionDepositCountRequest) ([]model.BankTransactionStatusCount, error)
	CountActiveDepositStatus(req model.GetBankTransactionDepositCountRequest) ([]model.BankTransactionStatusCount, error)
	CountWithdrawStatus(req model.GetBankTransactionWithdrawCountRequest) ([]model.BankTransactionStatusCount, error)
	CountActiveWithdrawStatus(req model.GetBankTransactionWithdrawCountRequest) ([]model.BankTransactionStatusCount, error)

	GetTransactionByIdForNoti(transId int64) (*model.GetBankTransactionDepositListResponse, error)
	GetPendingDespositTransactionWithSlip(userId int64) (*model.BankTransaction, error)
	//web socket
	WebSocket(reqAlert model.WebScoket) error

	UpdateAutoProcessTimer(timer string, id int64) error

	//turn over
	CreateTurnoverUserStatement(body model.TurnoverUserStatementCreateBody) (*int64, error)
	CheckTurnSuccessOnThisDay(req model.CheckTurnSuccessOnThisDayRequest) (*model.CheckTurnSuccessOnThisDayResponse, error)
	SumLastestTurnOver(userId int64) (*model.SumLastestTurnOverReponse, error)
	GetTodaySumUserPlayLogList(req model.UserTodayPlaylogListRequest) ([]model.UserTodaySumPlaylogReponse, int64, error)
	UpdateClearEveryTurnoverUserStatement(userId int64, body model.TurnoverUserStatementUpdateBody) error
	CreateTurnoverUserWithdrawLog(body model.CreateTurnoverUserWithdrawLog) (*int64, error)
	GetTurnOverStatementNotClear(userId int64) []model.GetTurnOverStatementNotClearResponse
	UpdateTurnoverUserStatement(id int64, body model.TurnoverUserStatementUpdateBody) error

	// Update after call agent
	UpdateDepositTransactionStatusFromAgent(transId int64, body model.UserTransactionCreateResponse) error
	UpdateWithdrawTransactionStatusFromAgent(transId int64, body model.UserTransactionCreateResponse) error
	UpdateWithdrawTransactionSuccessPullCreditBack(transId int64, body model.UserTransactionCreateResponse) error

	CheckDuplicateFromWebhook(fromBankAccount string, fromBankId int64) (*model.BankStatement, error)
	CheckDuplicateWithdraw(req model.CheckDuplicateWithdrawRequest) (*model.BankTransaction, error)
	// [********] Check duplicate withdraw proeccing from before CheckDuplicateWithdraw
	CheckUserDuplicateWithdrawProcessing(userId int64) (*model.CheckUserDuplicateWithdrawProcessingResponse, error)

	GetTransactionActionById(actionId int64) (*model.CreateBankTransactionActionBody, error)
	UpdateTransactionIngnoreActionById(actionId int64) error
	GetWebConfiguration() (*model.GetWebConfigurationBody, error)
	UpdateConfirmedByAdminId(id int64, body model.UpdateConfirmedByAdminIdRequest) error

	GetUserWithdrawCreditTransactionByRefId(transactionId int64, creditAmount float64) (*model.UserTransaction, error)

	CreateWithdrawConfirm(body model.CreateWithdrawConfirmBody) (int64, error)
	CheckWithdrawConfirmCurrentTime(req model.CheckWithdrawConfirmBody) (*model.WithdrawConfirm, error)
	CheckWithdrawConfirmConfirmKey(confirmKey string) (*model.WithdrawConfirm, error)
	UpdateWithdrawConfirmTransactionId(confirmId int64, transId int64) error

	// only frist time deposit
	GetBankTransactionFirstTimeDeposit(req model.GetBankTransactionFirstTimeDepositRequest) ([]model.GetBankTransactionFirstTimeDepositResponse, int64, error)

	// promotion
	UpdatePromotionToBankTransaction(transactionId int64, promotionWebUserId int64) error

	//user setting
	TransactionWithdrawToCheckUserConfigList(req model.TransactionWithdrawToCheckUserConfigRequest) ([]model.TransactionWithdrawToCheckUserConfigListResponse, int64, error)

	// retry
	GetUserTransactionByRefId(refId int64) (*model.UserTransaction, error)
	GetRaceActionIdByActionKey(actionKey string) (*int64, error)
	CreateRaceCondition(body model.RaceActionCreateBody) (int64, error)

	// [********] check withdraw between admin
	CheckAdminDuplicateWithdrawList(req model.CheckAdminDuplicateWithdrawListRequest) ([]model.CheckAdminDuplicateWithdrawList, error)
	GetLocalWebInfo() (*model.WebStatusResponse, error)

	// detail
	CreateBankTransactionExternalDetail(body model.BankTransactionExternalDetailCreateRequest) (int64, error)
	GetBankTransactionExternalDetailByBankTransactionId(id int64) ([]model.BankTransactionExternalDetailGetByBankTransactionIdResponse, error)

	UpdateUserTransactionStatementId(id int64, statementId int64) error
	UpdateFromBankAccount(statementId int64, bankAccount string) error
	FindUserDepositInBankTransaction(find model.ValidateQrDataWithBankTransactionBody) ([]model.FindPossibleOwnerByConfirmDeposit, int64, error)

	//slip
	CreateBankTransactionSlip(body model.BankTransactionSlipCreateRequest) (int64, error)
	UpdateBankTransactionSlip(body model.BankTransactionSlipUpdateRequest) error
	CheckScammerSlipRequest(req model.CheckScammerSlipRequest) (*model.CheckScammerSlipResponse, error)
	// Check Manual Deposit will Duplicate
	GetLastestBankAdminManualDepositTransaction(userId int64) (*model.BankTransaction, error)

	// Limit
	GetUserBankLimit(userId int64) (*model.UserBankLimitResponse, error)

	// REF-Paygate
	GetPaygateWithdrawAccount() (*model.PaygateAccountResponse, error)
	// GetNOTUSEPaygateMerchantById(id int64) (*model.PaygateMerchantNgResponse, error)
	GetPaygateAccountByProviderId(id int64) (*model.PaygateAccountResponse, error)
	CreatePaygateSystemLog(body model.PaygateSystemLogCreateBody) (*int64, error)
	// LuckyThai
	CreateDbLuckyThaiOrder(body model.LuckyThaiOrderCreateBody) (*int64, error)
	GetDbLuckyThaiOrderById(id int64) (*model.LuckyThaiOrderResponse, error)
	LuckyThaiCheckBalance(setting model.PaygateAccountResponse) (*model.LuckyThaiCheckBalanceRemoteResponse, error)
	LuckyThaiWithdraw(setting model.PaygateAccountResponse, req model.LuckyThaiWithdrawCreateRemoteRequest) (*model.LuckyThaiWithdrawCreateRemoteResponse, error)
	UpdateDbLuckyThaiOrderError(id int64, remark string) error
	UpdateDbLuckyThaiOrder(id int64, body model.LuckyThaiOrderUpdateBody) error
	LuckyThaiGetOrder(setting model.PaygateAccountResponse, orderNo string) (*model.LuckyThaiGetOrderRemoteResponse, error)
	GetDbLuckyThaiOrderByRefId(refId int64) (*model.LuckyThaiOrderResponse, error)
	// PapayaPay
	CreateDbPapayaPayOrder(body model.PapayaPayOrderCreateBody) (*int64, error)
	GetDbPapayaPayOrderById(id int64) (*model.PapayaPayOrderResponse, error)
	// ไม่มี PapayaPayCheckBalance(setting model.PaygateAccountResponse) (*model.PapayaPayCheckBalanceRemoteResponse, error)
	PapayaPayWithdraw(setting model.PaygateAccountResponse, req model.PapayaPayWithdrawCreateRemoteRequest) (*model.PapayaPayWithdrawCreateRemoteResponse, error)
	UpdateDbPapayaPayOrderError(id int64, remark string) error
	UpdateDbPapayaPayOrder(id int64, body model.PapayaPayOrderUpdateBody) error
	// ไม่มี PapayaPayGetOrder(setting model.PaygateAccountResponse, orderNo string) (*model.PapayaPayGetOrderRemoteResponse, error)
	GetDbPapayaPayOrderByRefId(refId int64) (*model.PapayaPayOrderResponse, error)
	// Payonex
	CreateDbPayonexOrder(body model.PayonexOrderCreateBody) (*int64, error)
	GetDbPayonexOrderById(id int64) (*model.PayonexOrderResponse, error)
	// ไม่มี PayonexCheckBalance(setting model.PaygateAccountResponse) (*model.PayonexCheckBalanceRemoteResponse, error)
	PayonexWithdraw(token string, setting model.PaygateAccountResponse, req model.PayonexWithdrawCreateRemoteRequest) (*model.PayonexWithdrawCreateRemoteResponse, error)
	UpdateDbPayonexOrderError(id int64, remark string) error
	UpdateDbPayonexOrder(id int64, body model.PayonexOrderUpdateBody) error
	// ไม่มี PayonexGetOrder(setting model.PaygateMePaygateAccountResponserchantResponse, orderNo string) (*model.PayonexGetOrderRemoteResponse, error)
	GetDbPayonexOrderByRefId(refId int64) (*model.PayonexOrderResponse, error)
	// Jbpay
	CreateDbJbpayOrder(body model.JbpayOrderCreateBody) (*int64, error)
	GetDbJbpayOrderById(id int64) (*model.JbpayOrderResponse, error)
	JbpayWithdraw(setting model.PaygateAccountResponse, req model.JbpayWithdrawCreateRemoteRequest) (*model.JbpayWithdrawCreateRemoteResponse, error)
	UpdateDbJbpayOrderError(id int64, remark string) error
	UpdateDbJbpayOrder(id int64, body model.JbpayOrderUpdateBody) error
	// REF-Pompay
	CreateDbPompayOrder(body model.PompayOrderCreateBody) (*int64, error)
	GetDbPompayOrderById(id int64) (*model.PompayOrderResponse, error)
	PompayWithdraw(setting model.PaygateAccountResponse, req model.PompayWithdrawCreateRemoteRequest) (*model.PompayWithdrawCreateRemoteResponse, error)
	UpdateDbPompayOrderError(id int64, remark string) error
	UpdateDbPompayOrder(id int64, body model.PompayOrderUpdateBody) error
	// REF-Paymentco
	CreateDbPaymentcoOrder(body model.PaymentcoOrderCreateBody) (*int64, error)
	GetDbPaymentcoOrderById(id int64) (*model.PaymentcoOrderResponse, error)
	PaymentcoWithdraw(setting model.PaygateAccountResponse, req model.PaymentcoWithdrawCreateRemoteRequest) (*model.PaymentcoWithdrawCreateRemoteResponse, error)
	UpdateDbPaymentcoOrderError(id int64, remark string) error
	UpdateDbPaymentcoOrder(id int64, body model.PaymentcoOrderUpdateBody) error
	// Zappay
	CreateDbZappayOrder(body model.ZappayOrderCreateBody) (*int64, error)
	GetDbZappayOrderById(id int64) (*model.ZappayOrderResponse, error)
	ZappayWithdraw(setting model.PaygateAccountResponse, req model.ZappayWithdrawCreateRemoteRequest) (*model.ZappayWithdrawCreateRemoteResponse, error)
	UpdateDbZappayOrderError(id int64, remark string) error
	UpdateDbZappayOrder(id int64, body model.ZappayOrderUpdateBody) error
	// Onepay
	CreateDbOnepayOrder(body model.OnepayOrderCreateBody) (*int64, error)
	GetDbOnepayOrderById(id int64) (*model.OnepayOrderResponse, error)
	OnepayWithdraw(token string, setting model.PaygateAccountResponse, req model.OnepayWithdrawCreateRemoteRequest) (*model.OnepayWithdrawCreateRemoteResponse, error)
	UpdateDbOnepayOrderError(id int64, remark string) error
	UpdateDbOnepayOrder(id int64, body model.OnepayOrderUpdateBody) error
	// Flashpay
	CreateDbFlashpayOrder(body model.FlashpayOrderCreateBody) (*int64, error)
	GetDbFlashpayOrderById(id int64) (*model.FlashpayOrderResponse, error)
	FlashpayWithdraw(setting model.PaygateAccountResponse, req model.FlashpayWithdrawCreateRemoteRequest) (*model.FlashpayWithdrawCreateRemoteResponse, error)
	UpdateDbFlashpayOrderError(id int64, remark string) error
	UpdateDbFlashpayOrder(id int64, body model.FlashpayOrderUpdateBody) error
	// Bizpay
	CreateDbBizpayOrder(body model.BizpayOrderCreateBody) (*int64, error)
	GetDbBizpayOrderById(id int64) (*model.BizpayOrderResponse, error)
	BizpayWithdraw(setting model.PaygateAccountResponse, req model.BizpayWithdrawCreateRemoteRequest) (*model.BizpayWithdrawCreateRemoteResponse, error)
	UpdateDbBizpayOrderError(id int64, remark string) error
	UpdateDbBizpayOrder(id int64, body model.BizpayOrderUpdateBody) error
	// Sugarpay
	CreateDbSugarpayOrder(body model.SugarpayOrderCreateBody) (*int64, error)
	GetDbSugarpayOrderById(id int64) (*model.SugarpayOrderResponse, error)
	SugarpayWithdraw(setting model.PaygateAccountResponse, req model.SugarpayWithdrawCreateRemoteRequest) (*model.SugarpayWithdrawCreateRemoteResponse, error)
	UpdateDbSugarpayOrderError(id int64, remark string) error
	UpdateDbSugarpayOrder(id int64, body model.SugarpayOrderUpdateBody) error
	// Zmanpay
	CreateDbZmanpayOrder(body model.ZmanpayOrderCreateBody) (*int64, error)
	GetDbZmanpayOrderById(id int64) (*model.ZmanpayOrderResponse, error)
	ZmanpayWithdraw(setting model.PaygateAccountResponse, req model.ZmanpayWithdrawCreateRemoteRequest) (*model.ZmanpayWithdrawCreateRemoteResponse, error)
	UpdateDbZmanpayOrderError(id int64, remark string) error
	UpdateDbZmanpayOrder(id int64, body model.ZmanpayOrderUpdateBody) error
	// PostmanPay
	CreateDbPostmanPayOrder(body model.PostmanPayOrderCreateBody) (*int64, error)
	GetDbPostmanPayOrderById(id int64) (*model.PostmanPayOrderResponse, error)
	PostmanPayWithdraw(setting model.PaygateAccountResponse, req model.PostmanPayWithdrawCreateRemoteRequest) (*model.PostmanPayWithdrawCreateRemoteResponse, error)
	UpdateDbPostmanPayOrderError(id int64, remark string) error
	UpdateDbPostmanPayOrder(id int64, body model.PostmanPayOrderUpdateBody) error
	// Mazepay
	CreateDbMazepayOrder(body model.MazepayOrderCreateBody) (*int64, error)
	GetDbMazepayOrderById(id int64) (*model.MazepayOrderResponse, error)
	MazepayWithdraw(setting model.PaygateAccountResponse, req model.MazepayWithdrawCreateRemoteRequest) (*model.MazepayWithdrawCreateRemoteResponse, error)
	UpdateDbMazepayOrderError(id int64, remark string) error
	UpdateDbMazepayOrder(id int64, body model.MazepayOrderUpdateBody) error
	// Meepay
	CreateDbMeepayOrder(body model.MeepayOrderCreateBody) (*int64, error)
	GetDbMeepayOrderById(id int64) (*model.MeepayOrderResponse, error)
	MeepayWithdraw(setting model.PaygateAccountResponse, req model.MeepayWithdrawCreateRemoteRequest) (*model.MeepayWithdrawCreateRemoteResponse, error)
	UpdateDbMeepayOrderError(id int64, remark string) error
	UpdateDbMeepayOrder(id int64, body model.MeepayOrderUpdateBody) error

	GetLaosExchangeCurrency() (*model.GetExchangeRate, error)

	// check account fastbank
	GetAccountInfoFastbank(body model.AccountInfoFastbankRequest) (*model.AccountInfoFastbankResponse, error)

	// clear other promotion status
	OtherPromotionClearWebUserById(userId int64) error

	// deposit sms list
	GetSmsModeDepositList(req model.GetSmsModeDepositListRequest) ([]model.GetSmsModeDepositListResponse, int64, error)
	GetSmsModeDepositById(id int64) (*model.GetSmsModeDepositByIdReponse, error)
	GetAdminById(id int64) (*model.Admin, error)

	// upload
	UploadImageToS3(pathUplod string, filename string, fileReader io.Reader) (*model.FileUploadResponse, error)

	// bot
	AgcGetCredit(data model.AgcBalance) (*model.AgcBalanceResponse, error)
	BotCreateWithdrawPullCreditBack(req model.BotCreateWithdrawPullCreditBackRequest) ([]model.BotCreateWithdrawPullCreditBackGetUser, error)

	// lock credit withdraw
	CheckLockCreditWithdrawByUserId(userId int64) ([]model.CheckLockCreditWithdrawByUserId, error)
	UpdateLockCreditWithdrawAutoUpdated(req model.UpdateLockCreditWithdrawAutoUpdated) error

	// AdminLog
	CreateAdminLog(body model.AdminLogCreateBody) (*int64, error)
}

func (r repo) GetBankTransactionWithStatementList(statementIds []int64) ([]model.BankTransactionStatementResponse, int64, error) {

	var list []model.BankTransactionStatementResponse
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset)
	count := r.db.Table("bank_transaction as transactions")
	count = count.Select("transactions.id")
	count = count.Where("transactions.statement_id IN ?", statementIds)
	if err = count.
		Where("transactions.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, 0, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "transactions.id as id, transactions.user_id as user_id, transactions.statement_id as statement_id, transactions.transaction_type_id as transaction_type_id"
		selectedFields += ", transactions.transaction_status_id as transaction_status_id, transactions.is_auto_credit as is_auto_credit, transactions.created_at as created_at, transactions.updated_at as updated_at"
		selectedFields += ", tb_statement.external_id as external_statement_id"
		query := r.db.Table("bank_transaction as transactions")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN bank_statement AS tb_statement ON tb_statement.id = transactions.statement_id")
		query = query.Where("transactions.statement_id IN ?", statementIds)
		if err = query.
			Where("transactions.deleted_at IS NULL").
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}

	return list, total, nil
}

func (r repo) GetBankStatementById(id int64) (*model.BankStatement, error) {

	var record model.BankStatement

	selectedFields := "statements.id, statements.account_id, statements.external_id, statements.detail, statements.statement_type_id, statements.transfer_at, statements.from_bank_id, statements.from_account_number, statements.amount, statements.statement_status_id, statements.created_at, statements.updated_at"
	selectedFields += ",accounts.account_name, accounts.account_number, accounts.account_type_id, accounts.bank_id"
	selectedFields += ",banks.name as bank_name, banks.code as bank_code, banks.icon_url as bank_icon_url, banks.type_flag as bank_type_flag"
	selectedFields += ",from_banks.name as from_bank_name, from_banks.code as from_bank_code, from_banks.icon_url as from_bank_icon_url"
	if err := r.db.Table("bank_statement as statements").
		Select(selectedFields).
		Joins("LEFT JOIN bank_account AS accounts ON accounts.id = statements.account_id").
		Joins("LEFT JOIN bank AS banks ON banks.id = accounts.bank_id").
		Joins("LEFT JOIN bank AS from_banks ON from_banks.id = statements.from_bank_id").
		Where("statements.id = ?", id).
		Where("statements.deleted_at IS NULL").
		First(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetBankStatements(req model.BankStatementListRequest) (*model.SuccessWithPagination, error) {

	var list []model.BankStatementResponse
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset)
	count := r.db.Table("bank_statement as statements")
	count = count.Joins("LEFT JOIN bank_account AS accounts ON accounts.id = statements.account_id")
	count = count.Joins("LEFT JOIN bank AS banks ON banks.id = accounts.bank_id")
	count = count.Joins("LEFT JOIN bank AS from_banks ON from_banks.id = statements.from_bank_id")
	count = count.Select("statements.id")
	if req.AccountId != "" {
		count = count.Where("statements.account_id = ?", req.AccountId)
	}
	if req.FromTransferDate != "" {
		count = count.Where("statements.transfer_at >= ?", req.FromTransferDate)
	}
	if req.ToTransferDate != "" {
		count = count.Where("statements.transfer_at <= ?", req.ToTransferDate)
	}
	if req.StatementTypeId != nil {
		count = count.Where("statements.statement_type_id = ?", req.StatementTypeId)
	}
	if req.StatementStatusId != nil {
		count = count.Where("statements.statement_status_id = ?", req.StatementStatusId)
	}
	if req.Search != "" {
		search_like := fmt.Sprintf("%%%s%%", req.Search)
		count = count.Where(r.db.Where("accounts.account_name LIKE ?", search_like).Or("accounts.account_number LIKE ?", search_like))
	}

	if err = count.
		Where("statements.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, err
	}
	if total > 0 {
		// SELECT //
		selectedFields := "statements.id, statements.account_id, statements.external_id, statements.detail, statements.statement_type_id, statements.transfer_at, statements.from_bank_id, statements.from_account_number, statements.amount, statements.statement_status_id, statements.created_at, statements.updated_at"
		selectedFields += ",accounts.account_name, accounts.account_number, accounts.account_type_id, accounts.bank_id"
		selectedFields += ",banks.name as bank_name, banks.code as bank_code, banks.icon_url as bank_icon_url, banks.type_flag as bank_type_flag"
		selectedFields += ",from_banks.name as from_bank_name, from_banks.code as from_bank_code, from_banks.icon_url as from_bank_icon_url"
		query := r.db.Table("bank_statement as statements")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN bank_account AS accounts ON accounts.id = statements.account_id")
		query = query.Joins("LEFT JOIN bank AS banks ON banks.id = accounts.bank_id")
		query = query.Joins("LEFT JOIN bank AS from_banks ON from_banks.id = statements.from_bank_id")
		if req.AccountId != "" {
			query = query.Where("statements.account_id = ?", req.AccountId)
		}
		if req.FromTransferDate != "" {
			query = query.Where("statements.transfer_at >= ?", req.FromTransferDate)
		}
		if req.ToTransferDate != "" {
			query = query.Where("statements.transfer_at <= ?", req.ToTransferDate)
		}
		if req.StatementTypeId != nil {
			query = query.Where("statements.statement_type_id = ?", req.StatementTypeId)
		}
		if req.StatementStatusId != nil {
			query = query.Where("statements.statement_status_id = ?", req.StatementStatusId)
		}
		if req.Search != "" {
			search_like := fmt.Sprintf("%%%s%%", req.Search)
			query = query.Where(r.db.Where("accounts.account_name LIKE ?", search_like).Or("accounts.account_number LIKE ?", search_like))
		}

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Where("statements.deleted_at IS NULL").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, err
		}
	}

	// End count total records for pagination purposes (without limit and offset)
	var result model.SuccessWithPagination
	result.List = list
	result.Total = total
	return &result, nil
}

func (r repo) GetBankExternalStatements(externalIds []int64) (*model.SuccessWithPagination, error) {

	var list []model.BankStatementResponse
	var err error

	if len(externalIds) > 0 {
		// SELECT //
		selectedFields := "statements.id, statements.account_id, statements.external_id, statements.detail, statements.statement_type_id, statements.transfer_at, statements.from_bank_id, statements.from_account_number, statements.amount, statements.statement_status_id, statements.created_at, statements.updated_at"
		query := r.db.Table("bank_statement as statements")
		query = query.Select(selectedFields)
		query = query.Where("statements.external_id IN ?", externalIds)
		if err = query.
			Where("statements.deleted_at IS NULL").
			Scan(&list).
			Error; err != nil {
			return nil, err
		}
	}

	// End count total records for pagination purposes (without limit and offset)
	var result model.SuccessWithPagination
	result.List = list
	result.Total = int64(len(list))
	return &result, nil
}

func (r repo) GetBankExternalStatementMaps(externalIds []int64) (map[int64]model.BankStatement, error) {

	var result map[int64]model.BankStatement
	var list []model.BankStatement
	var err error

	if len(externalIds) > 0 {
		// SELECT //
		selectedFields := "statements.id, statements.account_id, statements.external_id, statements.detail, statements.statement_type_id, statements.transfer_at"
		selectedFields += ", statements.from_bank_id, statements.from_account_number, statements.amount, statements.statement_status_id, statements.created_at, statements.updated_at"
		query := r.db.Table("bank_statement as statements")
		query = query.Select(selectedFields)
		query = query.Where("statements.external_id IN ?", externalIds)
		if err = query.
			Where("statements.deleted_at IS NULL").
			Scan(&list).
			Error; err != nil {
			return nil, err
		}
	}

	result = make(map[int64]model.BankStatement)
	for _, item := range list {
		result[item.ExternalId] = item
	}
	return result, nil
}

func (r repo) HasBankExternalStatements(externalId int64) error {

	var total int64 = 1
	var err error

	// Count total records for pagination purposes (without limit and offset)
	count := r.db.Table("bank_statement as statements")
	count = count.Select("statements.id")
	count = count.Where("statements.external_id = ?", externalId)
	if err = count.
		Where("statements.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return err
	}
	if total > 0 {
		return nil
	}
	return errors.New("record not found")
}

func (r repo) GetBankStatementByExternalId(externalId int64) (*model.BankStatement, error) {

	var record model.BankStatement
	var err error

	// Count total records for pagination purposes (without limit and offset)
	count := r.db.Table("bank_statement as statements")
	count = count.Select("*")
	count = count.Where("statements.external_id = ?", externalId)
	if err = count.
		Where("statements.deleted_at IS NULL").
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetBankStatementSummary(req model.BankStatementListRequest) (*model.BankStatementSummary, error) {

	var result model.BankStatementSummary
	var totalPendingStatementCount int64
	var totalPendingDepositCount int64
	var totalPendingWithdrawCount int64
	var err error

	// Count total records for pagination purposes (without limit and offset)
	count := r.db.Table("bank_statement as statements")
	count = count.Joins("LEFT JOIN bank_account AS accounts ON accounts.id = statements.account_id")
	count = count.Select("statements.id")
	count = count.Where("statements.statement_status_id = ?", model.STATEMENT_STATUS_PENDING)
	if req.AccountId != "" {
		count = count.Where("statements.account_id = ?", req.AccountId)
	}
	if req.StatementTypeId != nil {
		count = count.Where("statements.statement_type_id = ?", req.StatementTypeId)
	}
	if req.FromTransferDate != "" {
		count = count.Where("statements.transfer_at >= ?", req.FromTransferDate)
	}
	if req.ToTransferDate != "" {
		count = count.Where("statements.transfer_at <= ?", req.ToTransferDate)
	}
	if req.Search != "" {
		search_like := fmt.Sprintf("%%%s%%", req.Search)
		count = count.Where(r.db.Where("accounts.account_name LIKE ?", search_like).Or("accounts.account_number LIKE ?", search_like))
	}
	if err = count.
		Where("statements.deleted_at IS NULL").
		Count(&totalPendingStatementCount).
		Error; err != nil {
		return nil, err
	}

	// Count total records for pagination purposes (without limit and offset)
	countDeposit := r.db.Table("bank_transaction as transactions")
	countDeposit = countDeposit.Select("transactions.id")
	countDeposit = countDeposit.Where("transactions.transaction_status_id = ?", model.TRANS_STATUS_PENDING)
	countDeposit = countDeposit.Where("transactions.transaction_type_id = ?", model.TRANSACTION_TYPE_DEPOSIT)
	if req.AccountId != "" {
		countDeposit = countDeposit.Where("transactions.to_account_id = ?", req.AccountId)
	}
	if req.FromTransferDate != "" {
		countDeposit = countDeposit.Where("transactions.transfer_at >= ?", req.FromTransferDate)
	}
	if req.ToTransferDate != "" {
		countDeposit = countDeposit.Where("transactions.transfer_at <= ?", req.ToTransferDate)
	}
	if err = countDeposit.
		Where("transactions.deleted_at IS NULL").
		Count(&totalPendingDepositCount).
		Error; err != nil {
		return nil, err
	}

	// Count total records for pagination purposes (without limit and offset)
	countWithdraw := r.db.Table("bank_transaction as transactions")
	countWithdraw = countWithdraw.Select("transactions.id")
	countWithdraw = countWithdraw.Where("transactions.transaction_status_id = ?", model.TRANS_STATUS_PENDING)
	countWithdraw = countWithdraw.Where("transactions.transaction_type_id = ?", model.TRANSACTION_TYPE_WITHDRAW)
	if req.AccountId != "" {
		countWithdraw = countWithdraw.Where("transactions.from_account_id = ?", req.AccountId)
	}
	if req.FromTransferDate != "" {
		countWithdraw = countWithdraw.Where("transactions.transfer_at >= ?", req.FromTransferDate)
	}
	if req.ToTransferDate != "" {
		countWithdraw = countWithdraw.Where("transactions.transfer_at <= ?", req.ToTransferDate)
	}
	if err = countWithdraw.
		Where("transactions.deleted_at IS NULL").
		Count(&totalPendingWithdrawCount).
		Error; err != nil {
		return nil, err
	}

	result.TotalPendingStatementCount = totalPendingStatementCount
	result.TotalPendingDepositCount = totalPendingDepositCount
	result.TotalPendingWithdrawCount = totalPendingWithdrawCount

	return &result, nil
}

func (r repo) CreateBankStatement(data model.BankStatementCreateBody) (*int64, error) {

	if err := r.db.Table("bank_statement").Create(&data).Error; err != nil {
		return nil, err
	}
	return &data.Id, nil
}

func (r repo) UpdateBankStatementStatus(data model.BankStatementUpdateBody) error {

	if err := r.db.Table("bank_statement").Where("id = ?", data.Id).Updates(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) IgnoreStatementOwner(id int64, data model.BankStatementUpdateBody) error {

	if err := r.db.Table("bank_statement").Where("id = ?", id).Updates(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) DeleteBankStatement(id int64) error {

	if err := r.db.Table("bank_statement").Where("id = ?", id).Delete(&model.BankStatement{}).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetBankTransactionById(id int64) (*model.BankTransaction, error) {

	var record model.BankTransaction

	selectedFields := "transactions.id, transactions.user_id, transactions.transaction_type_id, transactions.promotion_id, transactions.from_account_id, transactions.from_bank_id, transactions.from_account_name, transactions.from_account_number, transactions.to_account_id, transactions.to_bank_id, transactions.to_account_name, transactions.to_account_number"
	selectedFields += ", transactions.credit_amount, transactions.credit_back, transactions.over_amount, transactions.deposit_channel, transactions.bonus_amount, transactions.bonus_reason, transactions.before_amount, transactions.after_amount "
	selectedFields += ", transactions.transfer_at, transactions.created_by_admin_id, transactions.transaction_status_id, transactions.is_auto_credit"
	selectedFields += ", transactions.created_at, transactions.updated_at"
	selectedFields += ", from_banks.name as from_bank_name, from_banks.code as from_bank_code, from_banks.icon_url as from_bank_icon_url, from_banks.type_flag as from_bank_type_flag"
	selectedFields += ", to_banks.name as to_bank_name, to_banks.code as to_bank_code, to_banks.icon_url as to_bank_icon_url, to_banks.type_flag as to_bank_type_flag"
	selectedFields += ", users.member_code as member_code, users.username as user_username, users.fullname as user_fullname, users.phone as user_phone"
	selectedFields += ", transactions.statement_id as statement_id"

	if err := r.db.Table("bank_transaction as transactions").
		Select(selectedFields).
		Joins("LEFT JOIN bank AS from_banks ON from_banks.id = transactions.from_bank_id").
		Joins("LEFT JOIN bank AS to_banks ON to_banks.id = transactions.to_bank_id").
		Joins("LEFT JOIN user AS users ON users.id = transactions.user_id").
		Where("transactions.id = ?", id).
		Where("transactions.deleted_at IS NULL").
		First(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetBankTransactionStatusCount(req model.BankTransactionListRequest) (*model.SuccessWithPagination, error) {

	var list []model.BankTransactionStatusCount
	var total int64 = 6
	var err error

	// SELECT //
	selectedFields := "transactions.transaction_status_id, count(*) as count"
	query := r.db.Table("bank_transaction as transactions")
	query = query.Select(selectedFields)
	query = query.Joins("LEFT JOIN bank AS from_banks ON from_banks.id = transactions.from_bank_id")
	query = query.Joins("LEFT JOIN bank AS to_banks ON to_banks.id = transactions.to_bank_id")
	query = query.Joins("LEFT JOIN user AS users ON users.id = transactions.user_id")
	if req.MemberCode != "" {
		query = query.Where("transactions.member_code = ?", req.MemberCode)
	}
	if req.UserId != "" {
		query = query.Where("transactions.user_id = ?", req.UserId)
	}
	if req.FromTransferDate != "" {
		query = query.Where("transactions.transfer_at >= ?", req.FromTransferDate)
	}
	if req.ToTransferDate != "" {
		query = query.Where("transactions.transfer_at <= ?", req.ToTransferDate)
	}
	if req.TransactionType != "" {
		if req.TransactionType == "DEPOSIT" {
			query = query.Where("transactions.transaction_type_id in (?,?)", model.TRANSACTION_TYPE_DEPOSIT, model.TRANSACTION_TYPE_BONUS)
		} else if req.TransactionType == "all_withdraw" {
			// query = query.Where(r.db.Where("transactions.transaction_type_id = ?", model.TRANSACTION_TYPE_WITHDRAW).Or("transactions.transaction_type_id = ?", model.TRANSACTION_TYPE_GETCREDITBACK))
			query = query.Where("transactions.transaction_type_id IN (?)", []int64{model.TRANSACTION_TYPE_WITHDRAW, model.TRANSACTION_TYPE_CREDITBACK, model.TRANSACTION_TYPE_CREDITCANCEL})
		} else {
			query = query.Where("transactions.transaction_type_id = ?", req.TransactionType)
		}
	}
	if req.TransferStatus != "" {
		if req.TransferStatus == "failed" {
			query = query.Where(r.db.Where("transactions.transaction_type_id = ?", "canceled"))
		} else {
			query = query.Where("transactions.transaction_status_id = ?", req.TransferStatus)
		}
	}
	if req.Search != "" {
		search_like := fmt.Sprintf("%%%s%%", req.Search)
		query = query.Where(r.db.Where("transactions.from_account_name LIKE ?", search_like).Or("transactions.from_account_number LIKE ?", search_like).Or("transactions.to_account_name LIKE ?", search_like).Or("transactions.to_account_number LIKE ?", search_like))
	}

	if err = query.
		Where("transactions.deleted_at IS NULL").
		Group("transactions.transaction_status_id").
		Scan(&list).
		Error; err != nil {
		return nil, err
	}

	// End count total records for pagination purposes (without limit and offset)
	var result model.SuccessWithPagination
	result.List = list
	result.Total = total
	return &result, nil
}

func (r repo) CountActiveDepositStatus(req model.GetBankTransactionDepositCountRequest) ([]model.BankTransactionStatusCount, error) {

	var list []model.BankTransactionStatusCount

	// ไม่นับรายการที่สำเร็จ
	// banking_repository.go:705 SLOW SQL >= 200ms
	// statusIds := []int64{
	// 	model.TRANS_STATUS_PENDING,
	// 	model.TRANS_STATUS_DEPOSIT_PENDING_CREDIT,
	// 	model.TRANS_STATUS_DEPOSIT_PENDING_SLIP,
	// 	model.TRANS_STATUS_DEPOSIT_PENDING_MULTIUSER,
	// 	// เยอะ ไม่นับ model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED,
	// 	model.TRANS_STATUS_DEPOSIT_CREDIT_REJECTED,
	// 	model.TRANS_STATUS_DEPOSIT_IGNORE,
	// }

	query := r.db.Table("bank_transaction as transactions")
	query = query.Select("transactions.transaction_status_id, count(*) as count")
	query = query.Where("transactions.transaction_type_id in (?,?)", model.TRANSACTION_TYPE_DEPOSIT, model.TRANSACTION_TYPE_BONUS)
	// query = query.Where("transactions.transaction_status_id IN (?)", statusIds)
	if req.FromTransferDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.FromTransferDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("transactions.transfer_at >= ? ", startDateAtBkk)
	}
	if req.ToTransferDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.ToTransferDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("transactions.transfer_at <= ? ", endDateAtBkk)
	}
	if req.TransactionTypeId != nil {
		query = query.Where("transactions.transaction_type_id = ?", req.TransactionTypeId)
	}
	if req.Search != "" {
		// Join เฉพาะตอนที่จะ Where table User
		query = query.Joins("LEFT JOIN user ON user.id = transactions.user_id")
		search_like := fmt.Sprintf("%%%s%%", req.Search)
		query = query.Where(r.db.Where("user.member_code LIKE ?", search_like).Or("user.username LIKE ?", search_like).Or("user.fullname LIKE ?", search_like).Or("transactions.from_account_number LIKE ?", search_like).Or("transactions.to_account_number LIKE ?", search_like))
	}
	query = query.Group("transactions.transaction_status_id")
	if err := query.
		Where("transactions.deleted_at IS NULL").
		Scan(&list).
		Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (r repo) CountDepositStatus(req model.GetBankTransactionDepositCountRequest) ([]model.BankTransactionStatusCount, error) {

	var list []model.BankTransactionStatusCount

	query := r.db.Table("bank_transaction as transactions")
	query = query.Select("transactions.transaction_status_id, count(*) as count")
	query = query.Where("transactions.transaction_type_id in (?,?)", model.TRANSACTION_TYPE_DEPOSIT, model.TRANSACTION_TYPE_BONUS)
	if req.FromTransferDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.FromTransferDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("transactions.transfer_at >= ? ", startDateAtBkk)
	}
	if req.ToTransferDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.ToTransferDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("transactions.transfer_at <= ? ", endDateAtBkk)
	}
	if req.TransactionTypeId != nil {
		query = query.Where("transactions.transaction_type_id = ?", req.TransactionTypeId)
	}
	if req.Search != "" {
		// Join เฉพาะตอนที่จะ Where table User
		query = query.Joins("LEFT JOIN user ON user.id = transactions.user_id")
		search_like := fmt.Sprintf("%%%s%%", req.Search)
		query = query.Where(r.db.Where("user.member_code LIKE ?", search_like).Or("user.username LIKE ?", search_like).Or("user.fullname LIKE ?", search_like).Or("transactions.from_account_number LIKE ?", search_like).Or("transactions.to_account_number LIKE ?", search_like))
	}
	query = query.Group("transactions.transaction_status_id")
	if err := query.
		Where("transactions.deleted_at IS NULL").
		Scan(&list).
		Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (r repo) CountActiveWithdrawStatus(req model.GetBankTransactionWithdrawCountRequest) ([]model.BankTransactionStatusCount, error) {

	var list []model.BankTransactionStatusCount

	// ไม่นับรายการที่สำเร็จ
	// banking_repository.go:705 SLOW SQL >= 200ms [12809.127ms]
	// statusIds := []int64{
	// 	model.TRANS_STATUS_WITHDRAW_PENDING,
	// 	model.TRANS_STATUS_WITHDRAW_OVER_BUDGET,
	// 	model.TRANS_STATUS_WITHDRAW_APPROVED,
	// 	model.TRANS_STATUS_WITHDRAW_REJECTED,
	// 	model.TRANS_STATUS_WITHDRAW_FAILED,
	// 	// เยอะ ไม่นับ model.TRANS_STATUS_WITHDRAW_SUCCESS,
	// 	model.TRANS_STATUS_WITHDRAW_OVER_MAX,
	// 	model.TRANS_STATUS_WITHDRAW_CANCELED,
	// 	model.TRANS_STATUS_WITHDRAW_UNSURE,
	// 	model.TRANS_STATUS_WITHDRAW_TRASNFERING,
	// }

	query := r.db.Table("bank_transaction as transactions")
	query = query.Select("transactions.transaction_status_id, count(*) as count")
	query = query.Where("transactions.transaction_type_id in (?)", []int64{model.TRANSACTION_TYPE_WITHDRAW, model.TRANSACTION_TYPE_CREDITBACK, model.TRANSACTION_TYPE_CREDITCANCEL})
	// query = query.Where("transactions.transaction_status_id IN (?)", statusIds)
	if req.FromTransferDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.FromTransferDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("transactions.transfer_at >= ? ", startDateAtBkk)
	}
	if req.ToTransferDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.ToTransferDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("transactions.transfer_at <= ? ", endDateAtBkk)
	}
	if req.TransactionTypeId != nil {
		query = query.Where("transactions.transaction_type_id = ?", req.TransactionTypeId)
	}
	if req.Search != "" {
		// Join เฉพาะตอนที่จะ Where table User
		query = query.Joins("LEFT JOIN user ON user.id = transactions.user_id")
		search_like := fmt.Sprintf("%%%s%%", req.Search)
		query = query.Where(r.db.Where("user.member_code LIKE ?", search_like).Or("user.username LIKE ?", search_like).Or("user.fullname LIKE ?", search_like).Or("transactions.from_account_number LIKE ?", search_like).Or("transactions.to_account_number LIKE ?", search_like))
	}
	query = query.Group("transactions.transaction_status_id")
	if err := query.
		Where("transactions.deleted_at IS NULL").
		Scan(&list).
		Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (r repo) CountWithdrawStatus(req model.GetBankTransactionWithdrawCountRequest) ([]model.BankTransactionStatusCount, error) {

	var list []model.BankTransactionStatusCount

	query := r.db.Table("bank_transaction as transactions")
	query = query.Select("transactions.transaction_status_id, count(*) as count")
	query = query.Where("transactions.transaction_type_id IN (?)", []int64{model.TRANSACTION_TYPE_WITHDRAW, model.TRANSACTION_TYPE_CREDITBACK, model.TRANSACTION_TYPE_CREDITCANCEL})
	if req.FromTransferDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.FromTransferDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("transactions.transfer_at >= ? ", startDateAtBkk)
	}
	if req.ToTransferDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.ToTransferDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("transactions.transfer_at <= ? ", endDateAtBkk)
	}
	if req.TransactionTypeId != nil {
		query = query.Where("transactions.transaction_type_id = ?", req.TransactionTypeId)
	}
	if req.Search != "" {
		// Join เฉพาะตอนที่จะ Where table User
		query = query.Joins("LEFT JOIN user ON user.id = transactions.user_id")
		search_like := fmt.Sprintf("%%%s%%", req.Search)
		query = query.Where(r.db.Where("user.member_code LIKE ?", search_like).Or("user.username LIKE ?", search_like).Or("user.fullname LIKE ?", search_like).Or("transactions.from_account_number LIKE ?", search_like).Or("transactions.to_account_number LIKE ?", search_like))
	}
	query = query.Group("transactions.transaction_status_id")
	if err := query.
		Where("transactions.deleted_at IS NULL").
		Scan(&list).
		Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (r repo) GetBankTransactions(req model.BankTransactionListRequest) (*model.SuccessWithPagination, error) {

	var list []model.BankTransactionResponse
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset)
	count := r.db.Table("bank_transaction as transactions")
	count = count.Select("transactions.id")
	if req.MemberCode != "" {
		count = count.Where("transactions.member_code = ?", req.MemberCode)
	}
	if req.UserId != "" {
		count = count.Where("transactions.user_id = ?", req.UserId)
	}
	if req.FromTransferDate != "" {
		count = count.Where("transactions.transfer_at >= ?", req.FromTransferDate)
	}
	if req.ToTransferDate != "" {
		count = count.Where("transactions.transfer_at <= ?", req.ToTransferDate)
	}
	if req.TransactionType != "" {
		if req.TransactionType == "all_deposit" {
			count = count.Where(r.db.Where("transactions.transaction_type_id = ?", model.TRANSACTION_TYPE_DEPOSIT).Or("transactions.transaction_type_id = ?", model.TRANSACTION_TYPE_BONUS))
		} else if req.TransactionType == "all_withdraw" {
			// count = count.Where(r.db.Where("transactions.transaction_type_id = ?", model.TRANSACTION_TYPE_WITHDRAW).Or("transactions.transaction_type_id = ?", model.TRANSACTION_TYPE_GETCREDITBACK))
			count = count.Where("transactions.transaction_type_id IN (?)", []int64{model.TRANSACTION_TYPE_WITHDRAW, model.TRANSACTION_TYPE_CREDITBACK, model.TRANSACTION_TYPE_CREDITCANCEL})
		} else {
			count = count.Where("transactions.transaction_type_id = ?", req.TransactionType)
		}
	}
	// if req.TransferStatus != "" {
	if req.TransferStatus == "failed" {
		count = count.Where(r.db.Where("transactions.transaction_status_id= ?", model.TRANS_STATUS_DEPOSIT_CREDIT_REJECTED))
	}
	//  else {
	// 	count = count.Where("transactions.status = ?", req.TransferStatus)
	// }
	// }
	if req.Search != "" {
		search_like := fmt.Sprintf("%%%s%%", req.Search)
		count = count.Where(r.db.Where("transactions.from_account_name LIKE ?", search_like).Or("transactions.from_account_number LIKE ?", search_like).Or("transactions.to_account_name LIKE ?", search_like).Or("transactions.to_account_number LIKE ?", search_like))
	}

	if err = count.
		Where("transactions.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, err
	}
	if total > 0 {
		// SELECT //
		selectedFields := "transactions.id, transactions.user_id, transactions.transaction_type_id, transactions.promotion_id, transactions.from_account_id, transactions.from_bank_id, transactions.from_account_name, transactions.from_account_number, transactions.to_account_id, transactions.to_bank_id, transactions.to_account_name, transactions.to_account_number"
		selectedFields += ", transactions.credit_amount, transactions.credit_back, transactions.over_amount, transactions.deposit_channel, transactions.bonus_amount, transactions.bonus_reason, transactions.before_amount, transactions.after_amount"
		selectedFields += ", transactions.transfer_at, transactions.created_by_admin_id, transactions.transaction_status_id, transactions.is_auto_credit"
		selectedFields += ", transactions.created_at, transactions.updated_at"
		selectedFields += ", from_banks.name as from_bank_name, from_banks.code as from_bank_code, from_banks.icon_url as from_bank_icon_url, from_banks.type_flag as from_bank_type_flag"
		selectedFields += ", to_banks.name as to_bank_name, to_banks.code as to_bank_code, to_banks.icon_url as to_bank_icon_url, to_banks.type_flag as to_bank_type_flag"
		selectedFields += ", users.member_code as member_code, users.username as user_username, users.fullname as user_fullname, users.phone as user_phone"
		query := r.db.Table("bank_transaction as transactions")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN bank AS from_banks ON from_banks.id = transactions.from_bank_id")
		query = query.Joins("LEFT JOIN bank AS to_banks ON to_banks.id = transactions.to_bank_id")
		query = query.Joins("LEFT JOIN user AS users ON users.id = transactions.user_id")
		if req.MemberCode != "" {
			query = query.Where("transactions.member_code = ?", req.MemberCode)
		}
		if req.UserId != "" {
			query = query.Where("transactions.user_id = ?", req.UserId)
		}
		if req.FromTransferDate != "" {
			query = query.Where("transactions.transfer_at >= ?", req.FromTransferDate)
		}
		if req.ToTransferDate != "" {
			query = query.Where("transactions.transfer_at <= ?", req.ToTransferDate)
		}
		if req.TransactionType != "" {
			if req.TransactionType == "all_deposit" {
				query = query.Where(r.db.Where("transactions.transaction_type_id = ?", model.TRANSACTION_TYPE_DEPOSIT).Or("transactions.transaction_type_id = ?", model.TRANSACTION_TYPE_BONUS))
			} else if req.TransactionType == "all_withdraw" {
				// query = query.Where(r.db.Where("transactions.transaction_type_id = ?", model.TRANSACTION_TYPE_WITHDRAW).Or("transactions.transaction_type_id = ?", model.TRANSACTION_TYPE_GETCREDITBACK))
				query = query.Where("transactions.transaction_type_id IN (?)", []int64{model.TRANSACTION_TYPE_WITHDRAW, model.TRANSACTION_TYPE_CREDITBACK, model.TRANSACTION_TYPE_CREDITCANCEL})
			}
		}
		// if req.TransferStatus != "" {
		if req.TransferStatus == "failed" {
			query = query.Where(r.db.Where("transactions.transaction_status_id = ?", model.TRANS_STATUS_DEPOSIT_CREDIT_REJECTED))
		}
		// 	else {
		// 		query = query.Where("transactions.status = ?", req.TransferStatus)
		// 	}
		// }

		if req.Search != "" {
			search_like := fmt.Sprintf("%%%s%%", req.Search)
			query = query.Where(r.db.Where("transactions.from_account_name LIKE ?", search_like).Or("transactions.from_account_number LIKE ?", search_like).Or("transactions.to_account_name LIKE ?", search_like).Or("transactions.to_account_number LIKE ?", search_like))
		}

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}

		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Where("transactions.deleted_at IS NULL").
			Order("transactions.transfer_at DESC").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, err
		}
	}

	// End count total records for pagination purposes (without limit and offset)
	var result model.SuccessWithPagination
	result.List = list
	result.Total = total
	return &result, nil
}

func (r repo) InsertBankTransaction(data model.BankTransactionCreateBody) (*int64, error) {

	if err := r.db.Table("bank_transaction").Create(&data).Error; err != nil {
		return nil, err
	}
	return &data.Id, nil
}

func (r repo) CreateBankDepositTransactionNoOwner(data model.BankTransactionNoOwnerCreateBody) (*int64, error) {

	if err := r.db.Table("bank_transaction").Create(&data).Error; err != nil {
		return nil, err
	}
	return &data.Id, nil
}

func (r repo) UpdateDepositTransactionOwner(statementid int64, data model.BankTransactionUpdateOwnerBody) (*int64, error) {

	if err := r.db.Table("bank_transaction").Where("statement_id = ?", statementid).Updates(&data).Error; err != nil {
		return nil, err
	}
	// get id from update bank_transaction
	if err := r.db.Table("bank_transaction").Select("id").Where("statement_id = ?", statementid).Scan(&data).Error; err != nil {
		return nil, err
	}
	return &data.Id, nil
}

func (r repo) UnusedCreateBankWithdrawTransactionWithCut(data model.BankTransactionCreateBody) (*int64, error) {

	member, err := r.GetMemberById(data.UserId)
	if err != nil {
		return nil, err
	}

	if data.CreditAmount > 0 && member.Credit >= data.CreditAmount {
		if err := r.db.Transaction(func(tx *gorm.DB) error {
			if err := tx.Table("user").Where("id = ?", data.UserId).UpdateColumn("credit", gorm.Expr("credit - ?", data.CreditAmount)).Error; err != nil {
				return err
			}
			if err := tx.Table("bank_transaction").Create(&data).Error; err != nil {
				return err
			}
			// if creditBalance, err := r.GetMemberCredit(data.UserId); err != nil {
			// 	return err
			// } else if creditBalance <= 0 {
			// 	return fmt.Errorf("ZERO_CREDIT")
			// }
			return nil
		}); err != nil {
			return nil, err
		}
	} else {
		return nil, fmt.Errorf("INSUFFICIENT_CREDIT")
	}
	return &data.Id, nil
}

func (r repo) CreateBankWithdrawTransaction(data model.BankTransactionCreateBody) (*int64, error) {

	if err := r.db.Table("bank_transaction").Create(&data).Error; err != nil {
		return nil, err
	}
	return &data.Id, nil
}

func (r repo) UnusedCreateBonusTransaction(data model.BonusTransactionCreateBody) error {

	if err := r.db.Table("bank_transaction").Create(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdateBankTransaction(id int64, data interface{}) error {

	if err := r.db.Table("bank_transaction").Where("id = ?", id).Updates(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) DeleteBankTransaction(id int64) error {

	if err := r.db.Table("bank_transaction").Where("id = ?", id).Delete(&model.BankTransaction{}).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetPendingDepositTransactions(req model.PendingDepositTransactionListRequest) (*model.SuccessWithPagination, error) {

	var list []model.BankTransactionResponse
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset)
	count := r.db.Table("bank_transaction as transactions")
	count = count.Select("transactions.id")
	count = count.Where("transactions.transaction_type_id = ?", model.TRANSACTION_TYPE_DEPOSIT)
	count = count.Where("transactions.transaction_status_id = ?", model.TRANS_STATUS_PENDING)
	if req.FromTransferDate != "" {
		count = count.Where("transactions.transfer_at >= ?", req.FromTransferDate)
	}
	if req.ToTransferDate != "" {
		count = count.Where("transactions.transfer_at <= ?", req.ToTransferDate)
	}
	if req.Search != "" {
		search_like := fmt.Sprintf("%%%s%%", req.Search)
		count = count.Where("transactions.from_account_name LIKE ?", search_like)
		count = count.Or("transactions.from_account_number LIKE ?", search_like)
		count = count.Or("transactions.to_account_name LIKE ?", search_like)
		count = count.Or("transactions.to_account_number LIKE ?", search_like)
	}

	if err = count.
		Where("transactions.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, err
	}
	if total > 0 {
		// SELECT //
		selectedFields := "transactions.id, transactions.user_id, transactions.transaction_type_id, transactions.promotion_id, transactions.from_account_id, transactions.from_bank_id, transactions.from_account_name, transactions.from_account_number, transactions.to_account_id, transactions.to_bank_id, transactions.to_account_name, transactions.to_account_number"
		selectedFields += ", transactions.credit_amount, transactions.credit_back, transactions.over_amount, transactions.deposit_channel, transactions.bonus_amount, transactions.bonus_reason, transactions.before_amount, transactions.after_amount"
		selectedFields += ", transactions.transfer_at, transactions.created_by_admin_id, transactions.transaction_status_id, transactions.is_auto_credit"
		selectedFields += ", transactions.created_at, transactions.updated_at"
		selectedFields += ", from_banks.name as from_bank_name, from_banks.code as from_bank_code, from_banks.icon_url as from_bank_icon_url, from_banks.type_flag as from_bank_type_flag"
		selectedFields += ", to_banks.name as to_bank_name, to_banks.code as to_bank_code, to_banks.icon_url as to_bank_icon_url, to_banks.type_flag as to_bank_type_flag"
		selectedFields += ", users.member_code as member_code, users.username as user_username, users.fullname as user_fullname, users.phone as user_phone"
		query := r.db.Table("bank_transaction as transactions")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN bank as from_banks ON from_banks.id = transactions.from_bank_id")
		query = query.Joins("LEFT JOIN bank as to_banks ON to_banks.id = transactions.to_bank_id")
		query = query.Joins("LEFT JOIN user as users ON users.id = transactions.user_id")
		query = query.Where("transactions.transaction_type_id = ?", model.TRANSACTION_TYPE_DEPOSIT)
		query = query.Where("transactions.transaction_status_id = ?", model.TRANS_STATUS_PENDING)

		if req.FromTransferDate != "" {
			query = query.Where("transactions.transfer_at >= ?", req.FromTransferDate)
		}
		if req.ToTransferDate != "" {
			query = query.Where("transactions.transfer_at <= ?", req.ToTransferDate)
		}
		if req.Search != "" {
			search_like := fmt.Sprintf("%%%s%%", req.Search)
			query = query.Where("transactions.from_account_name LIKE ?", search_like)
			query = query.Or("transactions.from_account_number LIKE ?", search_like)
			query = query.Or("transactions.to_account_name LIKE ?", search_like)
			query = query.Or("transactions.to_account_number LIKE ?", search_like)
		}

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Where("transactions.deleted_at IS NULL").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, err
		}
	}

	// End count total records for pagination purposes (without limit and offset)
	var result model.SuccessWithPagination
	result.List = list
	result.Total = total
	return &result, nil
}

func (r repo) GetPendingWithdrawTransactions(req model.PendingWithdrawTransactionListRequest) (*model.SuccessWithPagination, error) {

	var list []model.BankTransactionResponse
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset)
	count := r.db.Table("bank_transaction as transactions")
	count = count.Select("transactions.id")
	count = count.Where("transactions.transaction_type_id = ?", model.TRANSACTION_TYPE_WITHDRAW)
	count = count.Where("transactions.transaction_status_id = ?", model.TRANS_STATUS_PENDING)
	if req.FromTransferDate != "" {
		count = count.Where("transactions.transfer_at >= ?", req.FromTransferDate)
	}
	if req.ToTransferDate != "" {
		count = count.Where("transactions.transfer_at <= ?", req.ToTransferDate)
	}
	if req.Search != "" {
		search_like := fmt.Sprintf("%%%s%%", req.Search)
		count = count.Where("transactions.from_account_name LIKE ?", search_like)
		count = count.Or("transactions.from_account_number LIKE ?", search_like)
		count = count.Or("transactions.to_account_name LIKE ?", search_like)
		count = count.Or("transactions.to_account_number LIKE ?", search_like)
	}

	if err = count.
		Where("transactions.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, err
	}
	if total > 0 {
		// SELECT //
		selectedFields := "transactions.id, transactions.user_id, transactions.transaction_type_id, transactions.promotion_id, transactions.from_account_id, transactions.from_bank_id, transactions.from_account_name, transactions.from_account_number, transactions.to_account_id, transactions.to_bank_id, transactions.to_account_name, transactions.to_account_number"
		selectedFields += ", transactions.credit_amount, transactions.credit_back, transactions.over_amount, transactions.deposit_channel, transactions.bonus_amount, transactions.bonus_reason, transactions.before_amount, transactions.after_amount"
		selectedFields += ", transactions.transfer_at, transactions.created_by_admin_id, transactions.transaction_status_id, transactions.is_auto_credit"
		selectedFields += ", transactions.created_at, transactions.updated_at"
		selectedFields += ", from_banks.name as from_bank_name, from_banks.code as from_bank_code, from_banks.icon_url as from_bank_icon_url, from_banks.type_flag as from_bank_type_flag"
		selectedFields += ", to_banks.name as to_bank_name, to_banks.code as to_bank_code, to_banks.icon_url as to_bank_icon_url, to_banks.type_flag as to_bank_type_flag"
		selectedFields += ", users.member_code as member_code, users.username as user_username, users.fullname as user_fullname, users.phone as user_phone"
		query := r.db.Table("bank_transaction as transactions")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN bank as from_banks ON from_banks.id = transactions.from_bank_id")
		query = query.Joins("LEFT JOIN bank as to_banks ON to_banks.id = transactions.to_bank_id")
		query = query.Joins("LEFT JOIN user as users ON users.id = transactions.user_id")
		query = query.Where("transactions.transaction_type_id = ?", model.TRANSACTION_TYPE_WITHDRAW)
		query = query.Where("transactions.transaction_status_id = ?", model.TRANS_STATUS_PENDING)
		if req.FromTransferDate != "" {
			query = query.Where("transactions.transfer_at >= ?", req.FromTransferDate)
		}
		if req.ToTransferDate != "" {
			query = query.Where("transactions.transfer_at <= ?", req.ToTransferDate)
		}
		if req.Search != "" {
			search_like := fmt.Sprintf("%%%s%%", req.Search)
			query = query.Where("transactions.from_account_name LIKE ?", search_like)
			query = query.Or("transactions.from_account_number LIKE ?", search_like)
			query = query.Or("transactions.to_account_name LIKE ?", search_like)
			query = query.Or("transactions.to_account_number LIKE ?", search_like)
		}

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}

		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Where("transactions.deleted_at IS NULL").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, err
		}
	}

	// End count total records for pagination purposes (without limit and offset)
	var result model.SuccessWithPagination
	result.List = list
	result.Total = total
	return &result, nil
}

func (r repo) CancelPendingTransaction(id int64, data model.BankTransactionCancelBody) error {
	//.Where("status = ?", "pending")
	if err := r.db.Table("bank_transaction").Where("id = ?", id).Updates(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) CreateTransactionAction(data model.CreateBankTransactionActionBody) (*int64, error) {
	if err := r.db.Table("bank_transaction_confirm").Create(&data).Error; err != nil {
		return nil, err
	}
	return &data.Id, nil
}

func (r repo) GetTransactionActionById(actionId int64) (*model.CreateBankTransactionActionBody, error) {
	var record model.CreateBankTransactionActionBody

	if err := r.db.Table("bank_transaction_confirm").Where("transaction_id = ?", actionId).First(&record).Error; err != nil {
		return nil, err
	}

	return &record, nil
}

func (r repo) UpdateTransactionIngnoreActionById(id int64) error {
	data := map[string]interface{}{
		"action_key": fmt.Sprintf("CANCEL#%d", id),
	}
	if err := r.db.Table("bank_transaction_confirm").Where("transaction_id = ?", id).Updates(data).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) RollbackTransactionAction(actionId int64) error {
	data := map[string]interface{}{
		"action_key": fmt.Sprintf("ROLLBACK#%d", actionId),
		"deleted_at": time.Now(),
	}
	if err := r.db.Table("bank_transaction_confirm").Where("id = ?", actionId).Updates(data).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) CreateStatementAction(data model.CreateBankStatementActionBody) error {
	if err := r.db.Table("bank_confirm_statement").Create(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) ConfirmPendingDepositTransaction(id int64, body model.BankDepositTransactionConfirmBody) error {

	if err := r.db.Table("bank_transaction").Where("id = ?", id).Updates(body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) ConfirmPendingCreditDepositTransaction(id int64, body model.BankDepositTransactionConfirmBody) error {

	if err := r.db.Table("bank_transaction").Where("id = ?", id).Where("status = ?", "pending_credit").Updates(body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) CheckMemeberHasEnoughtCredit(memberId int64, creditAmount float64) error {

	member, err := r.GetMemberById(memberId)
	if err != nil {
		return err
	}
	if creditAmount <= 0 {
		return fmt.Errorf("INVALID_CREDIT_AMOUNT")
	}
	if member.Credit < creditAmount {
		return fmt.Errorf("INSUFFICIENT_CREDIT")
	}
	return nil
}

func (r repo) ConfirmPendingWithdrawTransaction(id int64, body model.BankWithdrawTransactionConfirmBody) error {

	if err := r.db.Table("bank_transaction").Where("id = ?", id).Updates(&body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) ConfirmPendingWithdrawTransfer(id int64, body model.BankWithdrawTransactionConfirmBody) error {

	if err := r.db.Table("bank_transaction").Where("id = ?", id).Where("status = ?", "pending_transfer").Updates(&body).Error; err != nil {
		return err
	}
	return nil
}

// func (r repo) GetFinishedTransactions(req model.FinishedTransactionListRequest) (*model.SuccessWithPagination, error) {

// 	var list []model.BankTransactionResponse
// 	var total int64
// 	var err error

// 	// Count total records for pagination purposes (without limit and offset)
// 	count := r.db.Table("bank_transaction as transactions")
// 	count = count.Select("transactions.id")
// 	count = count.Where("transactions.transaction_status_id = ?", model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED)
// 	if req.AccountId != "" {
// 		count = count.Where(r.db.Where("transactions.from_account_id = ?", req.AccountId).Or("transactions.to_account_id = ?", req.AccountId))
// 	}
// 	if req.FromTransferDate != "" {
// 		count = count.Where("transactions.transfer_at >= ?", req.FromTransferDate)
// 	}
// 	if req.ToTransferDate != "" {
// 		count = count.Where("transactions.transfer_at <= ?", req.ToTransferDate)
// 	}
// 	if req.TransactionTypeId != nil {
// 		count = count.Where("transactions.transaction_type_id = ?", req.TransactionTypeId)
// 	}
// 	if req.Search != "" {
// 		search_like := fmt.Sprintf("%%%s%%", req.Search)
// 		count = count.Where("transactions.from_account_name LIKE ?", search_like)
// 		count = count.Or("transactions.from_account_number LIKE ?", search_like)
// 		count = count.Or("transactions.to_account_name LIKE ?", search_like)
// 		count = count.Or("transactions.to_account_number LIKE ?", search_like)
// 	}

// 	if err = count.
// 		Where("transactions.deleted_at IS NULL").
// 		Count(&total).
// 		Error; err != nil {
// 		return nil, err
// 	}
// 	if total > 0 {
// 		// SELECT //
// 		selectedFields := "transactions.id, transactions.user_id, transactions.transaction_type_id, transactions.promotion_id, transactions.from_account_id, transactions.from_bank_id, transactions.from_account_name, transactions.from_account_number, transactions.to_account_id, transactions.to_bank_id, transactions.to_account_name, transactions.to_account_number"
// 		selectedFields += ", transactions.credit_amount, transactions.credit_back, transactions.over_amount, transactions.deposit_channel, transactions.bonus_amount, transactions.bonus_reason, transactions.before_amount, transactions.after_amount"
// 		selectedFields += ", transactions.transfer_at, transactions.created_by_admin_id, transactions.transaction_status_id, transactions.is_auto_credit"
// 		selectedFields += ", transactions.created_at, transactions.updated_at"
// 		selectedFields += ", from_banks.name as from_bank_name, from_banks.code as from_bank_code, from_banks.icon_url as from_bank_icon_url, from_banks.type_flag as from_bank_type_flag"
// 		selectedFields += ", to_banks.name as to_bank_name, to_banks.code as to_bank_code, to_banks.icon_url as to_bank_icon_url, to_banks.type_flag as to_bank_type_flag"
// 		selectedFields += ", users.member_code as member_code, users.username as user_username, users.fullname as user_fullname, users.phone as user_phone"
// 		query := r.db.Table("bank_transaction as transactions")
// 		query = query.Select(selectedFields)
// 		query = query.Joins("LEFT JOIN bank as from_banks ON from_banks.id = transactions.from_bank_id")
// 		query = query.Joins("LEFT JOIN bank as to_banks ON to_banks.id = transactions.to_bank_id")
// 		query = query.Joins("LEFT JOIN user as users ON users.id = transactions.user_id")
// 		query = query.Where("transactions.transaction_status_id = ?", model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED)
// 		if req.AccountId != "" {
// 			query = query.Where(r.db.Where("transactions.from_account_id = ?", req.AccountId).Or("transactions.to_account_id = ?", req.AccountId))
// 		}
// 		if req.FromTransferDate != "" {
// 			query = query.Where("transactions.transfer_at >= ?", req.FromTransferDate)
// 		}
// 		if req.ToTransferDate != "" {
// 			query = query.Where("transactions.transfer_at <= ?", req.ToTransferDate)
// 		}
// 		if req.TransactionTypeId != nil {
// 			query = query.Where("transactions.transaction_type_id = ?", req.TransactionTypeId)
// 		}
// 		if req.Search != "" {
// 			search_like := fmt.Sprintf("%%%s%%", req.Search)
// 			query = query.Where("transactions.from_account_name LIKE ?", search_like)
// 			query = query.Or("transactions.from_account_number LIKE ?", search_like)
// 			query = query.Or("transactions.to_account_name LIKE ?", search_like)
// 			query = query.Or("transactions.to_account_number LIKE ?", search_like)
// 		}

// 		// Sort by ANY //
// 		req.SortCol = strings.TrimSpace(req.SortCol)
// 		if req.SortCol != "" {
// 			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
// 				req.SortAsc = "DESC"
// 			} else {
// 				req.SortAsc = "ASC"
// 			}
// 			query = query.Order(req.SortCol + " " + req.SortAsc)
// 		}

// 		if req.Limit > 0 {
// 			query = query.Limit(req.Limit)
// 		}
// 		if err = query.
// 			Where("transactions.deleted_at IS NULL").
// 			Offset(req.Page * req.Limit).
// 			Scan(&list).
// 			Error; err != nil {
// 			return nil, err
// 		}
// 	}

// 	// End count total records for pagination purposes (without limit and offset)
// 	var result model.SuccessWithPagination
// 	result.List = list
// 	result.Total = total
// 	return &result, nil
// }

func (r repo) RemoveFinishedTransaction(id int64, data model.BankTransactionRemoveBody) error {
	if err := r.db.Table("bank_transaction").Where("id = ?", id).Updates(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetRemovedTransactions(req model.RemovedTransactionListRequest) (*model.SuccessWithPagination, error) {

	var list []model.BankTransactionResponse
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset)
	count := r.db.Table("bank_transaction as transactions")
	count = count.Select("transactions.id")
	if req.FromTransferDate != "" {
		count = count.Where("transactions.transfer_at >= ?", req.FromTransferDate)
	}
	if req.ToTransferDate != "" {
		count = count.Where("transactions.transfer_at <= ?", req.ToTransferDate)
	}
	if req.TransactionTypeId != nil {
		count = count.Where("transactions.transaction_type_id = ?", req.TransactionTypeId)
	}
	if req.Search != "" {
		search_like := fmt.Sprintf("%%%s%%", req.Search)
		count = count.Where("transactions.from_account_name LIKE ?", search_like)
		count = count.Or("transactions.from_account_number LIKE ?", search_like)
		count = count.Or("transactions.to_account_name LIKE ?", search_like)
		count = count.Or("transactions.to_account_number LIKE ?", search_like)
	}

	if err = count.
		Where("transactions.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, err
	}
	if total > 0 {
		// SELECT //
		selectedFields := "transactions.id, transactions.user_id, transactions.transaction_type_id, transactions.promotion_id, transactions.from_account_id, transactions.from_bank_id, transactions.from_account_name, transactions.from_account_number, transactions.to_account_id, transactions.to_bank_id, transactions.to_account_name, transactions.to_account_number"
		selectedFields += ", transactions.credit_amount, transactions.credit_back, transactions.over_amount, transactions.deposit_channel, transactions.bonus_amount, transactions.bonus_reason, transactions.before_amount, transactions.after_amount"
		selectedFields += ", transactions.transfer_at, transactions.created_by_admin_id, transactions.transaction_status_id, transactions.is_auto_credit"
		selectedFields += ", transactions.created_at, transactions.updated_at"
		selectedFields += ", from_banks.name as from_bank_name, from_banks.code as from_bank_code, from_banks.icon_url as from_bank_icon_url, from_banks.type_flag as from_bank_type_flag"
		selectedFields += ", to_banks.name as to_bank_name, to_banks.code as to_bank_code, to_banks.icon_url as to_bank_icon_url, to_banks.type_flag as to_bank_type_flag"
		selectedFields += ", users.member_code as member_code, users.username as user_username, users.fullname as user_fullname, users.phone as user_phone"
		query := r.db.Table("bank_transaction as transactions")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN bank as from_banks ON from_banks.id = transactions.from_bank_id")
		query = query.Joins("LEFT JOIN bank as to_banks ON to_banks.id = transactions.to_bank_id")
		query = query.Joins("LEFT JOIN user as users ON users.id = transactions.user_id")
		if req.FromTransferDate != "" {
			query = query.Where("transactions.transfer_at >= ?", req.FromTransferDate)
		}
		if req.ToTransferDate != "" {
			query = query.Where("transactions.transfer_at <= ?", req.ToTransferDate)
		}
		if req.TransactionTypeId != nil {
			query = query.Where("transactions.transaction_type_id = ?", req.TransactionTypeId)
		}
		if req.Search != "" {
			search_like := fmt.Sprintf("%%%s%%", req.Search)
			query = query.Where("transactions.from_account_name LIKE ?", search_like)
			query = query.Or("transactions.from_account_number LIKE ?", search_like)
			query = query.Or("transactions.to_account_name LIKE ?", search_like)
			query = query.Or("transactions.to_account_number LIKE ?", search_like)
		}

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}

		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Where("transactions.deleted_at IS NULL").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, err
		}
	}

	// End count total records for pagination purposes (without limit and offset)
	var result model.SuccessWithPagination
	result.List = list
	result.Total = total
	return &result, nil
}

func (r repo) GetMemberById(id int64) (*model.Member, error) {

	var record model.Member

	selectedFields := "users.id, users.member_code, users.username, users.phone, users.fullname, users.credit, users.bank_account, users.bank_id as bank_id"
	selectedFields += ", users.ref_by, users.user_type_id, user_type.name as user_type_name, tb_bank.name as bank_name"
	if err := r.db.Table("user as users").
		Joins("LEFT JOIN user_type ON user_type.id = users.user_type_id").
		Joins("LEFT JOIN bank as tb_bank ON tb_bank.id = users.bank_id").
		Select(selectedFields).
		Where("users.id = ?", id).
		Where("users.deleted_at IS NULL").
		First(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetMemberCredit(id int64) (float64, error) {

	var record model.Member

	selectedFields := "users.id, users.credit"
	if err := r.db.Table("user as users").
		Select(selectedFields).
		Where("users.id = ?", id).
		Where("users.deleted_at IS NULL").
		First(&record).
		Error; err != nil {
		return 0, err
	}
	return record.Credit, nil
}

func (r repo) GetMemberByCode(memberCode string) ([]model.MemberForDropdown, error) {

	var record []model.MemberForDropdown
	showLimit := 30

	// 2024/08/28 16:45:55 /app/repository/banking_repository.go:1641 SLOW SQL >= 200ms
	// [242.231ms] [rows:1] SELECT users.id, users.member_code, users.bank_id, users.bank_account, bank.name AS bank_name, bank.code AS bank_code, fullname as fullname FROM user as users LEFT JOIN bank ON bank.id = users.bank_id WHERE users.member_code LIKE '%zta68pk52000259%' AND users.deleted_at IS NULL
	// [GIN] 2024/08/28 - 16:45:55 | 200 |  242.494913ms |     10.104.0.16 | GET      "/api/banking/member/info/zta68pk52000259"

	selectedFields := "users.id, users.member_code, users.bank_id, users.bank_account, bank.name AS bank_name, bank.code AS bank_code"
	selectedFields += ", fullname as fullname"

	if err := r.db.Table("user as users").
		Joins("LEFT JOIN bank ON bank.id = users.bank_id").
		Select(selectedFields).
		Where("users.member_code LIKE ?", "%"+memberCode+"%").
		Where("users.deleted_at IS NULL").
		Limit(showLimit).
		Scan(&record).
		Error; err != nil {
		return nil, err
	}
	return record, nil
}

func (r repo) GetMembers(req model.MemberListRequest) (*model.SuccessWithPagination, error) {

	var list []model.Member
	var total int64

	// Count total records for pagination purposes (without limit and offset)
	count := r.db.Table("user as users")
	count = count.Joins("LEFT JOIN bank ON bank.id = users.bank_id")
	count = count.Select("users.id")
	if req.Search != "" {
		search_like := fmt.Sprintf("%%%s%%", req.Search)
		count = count.Where(r.db.Where("users.username LIKE ?", search_like).Or("users.phone LIKE ?", search_like).Or("users.fullname LIKE ?", search_like).Or("bank.name LIKE ?", search_like).Or("users.bank_account LIKE ?", search_like).Or("users.member_code LIKE ?", search_like))
	}
	if err := count.
		Where("users.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "users.id, users.member_code, users.username, users.fullname, users.credit, bank.name AS bank_name, users.bank_account, recommend_channel.title AS channel, users.true_wallet, users.note, users.created_at"
		selectedFields += ", users.ref_by, users.user_type_id, user_type.name as user_type_name"
		query := r.db.Table("user as users")
		query = query.Joins("LEFT JOIN bank ON bank.id = users.bank_id")
		query = query.Joins("LEFT JOIN recommend_channel ON recommend_channel.id = users.channel_id")
		query = query.Joins("LEFT JOIN user_type ON user_type.id = users.user_type_id")
		query = query.Select(selectedFields)
		if req.Search != "" {
			search_like := fmt.Sprintf("%%%s%%", req.Search)
			query = query.Where(r.db.Where("users.username LIKE ?", search_like).Or("users.phone LIKE ?", search_like).Or("users.fullname LIKE ?", search_like).Or("bank.name LIKE ?", search_like).Or("users.bank_account LIKE ?", search_like).Or("users.member_code LIKE ?", search_like))
		}

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}

		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Where("users.deleted_at IS NULL").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, err
		}
	}

	// End count total records for pagination purposes (without limit and offset)
	var result model.SuccessWithPagination
	result.List = list
	result.Total = total
	return &result, nil
}

func (r repo) GetMemberTransactions(req model.MemberTransactionListRequest) (*model.SuccessWithPagination, error) {

	var list []model.MemberTransaction
	var total int64
	var err error

	// Count total records for pagination purposes (without limit and offset)
	count := r.db.Table("bank_transaction as transactions")
	count = count.Select("transactions.id")
	if req.UserId != "" {
		count = count.Where("transactions.user_id = ?", req.UserId)
	}
	if req.FromTransferDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.FromTransferDate)
		if err != nil {
			return nil, err
		}
		count = count.Where("transactions.transfer_at >= ? ", startDateAtBkk)
	}
	if req.ToTransferDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.ToTransferDate)
		if err != nil {
			return nil, err
		}
		count = count.Where("transactions.transfer_at <=  ?", endDateAtBkk)
	}
	if req.TransactionTypeId != nil {
		count = count.Where("transactions.transaction_type_id = ?", req.TransactionTypeId)
	}
	if req.Search != "" {
		search_like := fmt.Sprintf("%%%s%%", req.Search)
		count = count.Where(r.db.Where("transactions.member_code LIKE ?", search_like).Or("transactions.from_account_name LIKE ?", search_like).Or("transactions.from_account_number LIKE ?", search_like).Or("transactions.to_account_name LIKE ?", search_like).Or("transactions.to_account_number LIKE ?", search_like))
	}

	if err = count.
		Where("transactions.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, err
	}
	if total > 0 {
		// SELECT //
		selectedFields := "transactions.id, transactions.user_id, transactions.transaction_type_id, transactions.promotion_id, transactions.from_account_id, transactions.from_bank_id, transactions.from_account_name, transactions.from_account_number, transactions.to_account_id, transactions.to_bank_id, transactions.to_account_name, transactions.to_account_number"
		selectedFields += ", transactions.credit_amount, transactions.credit_back, transactions.over_amount, transactions.deposit_channel, transactions.bonus_amount, transactions.bonus_reason, transactions.before_amount, transactions.after_amount"
		selectedFields += ", transactions.transfer_at, transactions.created_by_admin_id, transactions.transaction_status_id, transactions.is_auto_credit"
		selectedFields += ", transactions.created_at, transactions.updated_at"
		selectedFields += ", from_banks.name as from_bank_name, from_banks.code as from_bank_code, from_banks.icon_url as from_bank_icon_url, from_banks.type_flag as from_bank_type_flag"
		selectedFields += ", to_banks.name as to_bank_name, to_banks.code as to_bank_code, to_banks.icon_url as to_bank_icon_url, to_banks.type_flag as to_bank_type_flag"
		selectedFields += ", users.member_code as member_code, users.username as user_username, users.fullname as user_fullname, users.phone as user_phone"
		selectedFields += ", transaction_type.label_th as transaction_type_th, transaction_type.label_en as transaction_type_en"
		selectedFields += ", CASE WHEN transactions.created_by_admin_id = 0 THEN 'อัตโนมัติ' ELSE admincreate.username END AS created_by_username"
		selectedFields += ", CASE WHEN transactions.confirmed_by_admin_id = 0 THEN 'อัตโนมัติ' ELSE adminconfirmed.username END AS confirmed_by_username"

		query := r.db.Table("bank_transaction as transactions")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN bank as from_banks ON from_banks.id = transactions.from_bank_id")
		query = query.Joins("LEFT JOIN bank as to_banks ON to_banks.id = transactions.to_bank_id")
		query = query.Joins("LEFT JOIN user as users ON users.id = transactions.user_id")
		query = query.Joins("LEFT JOIN transaction_type ON transaction_type.id = transactions.transaction_type_id")
		query = query.Joins("LEFT JOIN admin as admincreate ON admincreate.id = transactions.created_by_admin_id")
		query = query.Joins("LEFT JOIN admin as adminconfirmed ON adminconfirmed.id = transactions.confirmed_by_admin_id")

		if req.UserId != "" {
			query = query.Where("transactions.user_id = ?", req.UserId)
		}
		if req.FromTransferDate != "" {
			startDateAtBkk, err := r.ParseBodBkk(req.FromTransferDate)
			if err != nil {
				return nil, err
			}
			query = query.Where("transactions.transfer_at >= ? ", startDateAtBkk)
		}
		if req.ToTransferDate != "" {
			endDateAtBkk, err := r.ParseEodBkk(req.ToTransferDate)
			if err != nil {
				return nil, err
			}
			query = query.Where("transactions.transfer_at <=  ?", endDateAtBkk)
		}
		if req.TransactionTypeId != nil {
			query = query.Where("transactions.transaction_type_id = ?", req.TransactionTypeId)
		}
		if req.Search != "" {
			search_like := fmt.Sprintf("%%%s%%", req.Search)
			query = query.Where(r.db.Where("transactions.member_code LIKE ?", search_like).Or("transactions.from_account_name LIKE ?", search_like).Or("transactions.from_account_number LIKE ?", search_like).Or("transactions.to_account_name LIKE ?", search_like).Or("transactions.to_account_number LIKE ?", search_like))
		}

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}

		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Where("transactions.deleted_at IS NULL").
			Order("transactions.transfer_at DESC").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, err
		}
	}

	// End count total records for pagination purposes (without limit and offset)
	var result model.SuccessWithPagination
	result.List = list
	result.Total = total
	return &result, nil
}

func (r repo) GetMemberTransactionSummary(req model.MemberTransactionListRequest) (*model.MemberTransactionSummary, error) {

	var result model.MemberTransactionSummary
	var err error

	// SELECT //
	selectedFields := "SUM(CASE WHEN transaction_type_id = ? THEN credit_amount ELSE 0 END) AS total_deposit_amount,"
	selectedFields += " SUM(CASE WHEN transaction_type_id = ? THEN credit_amount ELSE 0 END) AS total_withdraw_amount,"
	selectedFields += " SUM(CASE WHEN transaction_type_id = ? THEN bonus_amount ELSE 0 END) AS total_bonus_amount"

	query := r.db.Table("bank_transaction as transactions")
	query = query.Select(selectedFields, model.TRANSACTION_TYPE_DEPOSIT, model.TRANSACTION_TYPE_WITHDRAW, model.TRANSACTION_TYPE_BONUS)
	query = query.Joins("LEFT JOIN bank as from_banks ON from_banks.id = transactions.from_bank_id")
	query = query.Joins("LEFT JOIN bank as to_banks ON to_banks.id = transactions.to_bank_id")

	if req.UserId != "" {
		query = query.Where("transactions.user_id = ?", req.UserId)
	}
	if req.FromTransferDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.FromTransferDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("transactions.transfer_at >= ? ", startDateAtBkk)
	}
	if req.ToTransferDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.ToTransferDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("transactions.transfer_at <=  ?", endDateAtBkk)
	}

	if req.Search != "" {
		searchLike := fmt.Sprintf("%%%s%%", req.Search)
		query = query.Where(r.db.Where("transactions.member_code LIKE ?", searchLike).
			Or("transactions.from_account_name LIKE ?", searchLike).
			Or("transactions.from_account_number LIKE ?", searchLike).
			Or("transactions.to_account_name LIKE ?", searchLike).
			Or("transactions.to_account_number LIKE ?", searchLike))
	}

	if err = query.
		Where("transactions.deleted_at IS NULL").
		Scan(&result).
		Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (r repo) IncreaseMemberCredit(body model.MemberStatementCreateBody) error {

	member, err := r.GetMemberById(body.UserId)
	if err != nil {
		return err
	}

	if err := r.db.Transaction(func(tx *gorm.DB) error {
		data := map[string]interface{}{
			"user_id":           member.Id,
			"statement_type_id": body.StatementTypeId,
			"transfer_at":       time.Now(),
			"info":              body.Info,
			"before_balance":    member.Credit,
			"amount":            body.Amount,
			"after_balance":     member.Credit + body.Amount,
		}
		if err := r.db.Table("User_statements").Create(&data).Error; err != nil {
			return err
		}
		// if err := tx.Table("user").Where("id = ?", member.Id).UpdateColumn("credit", gorm.Expr("credit + ?", body.Amount)).Error; err != nil {
		// 	return err
		// }

		return nil // COMMIT
	}); err != nil {
		return err
	}
	return nil
}

func (r repo) CreateUserTransaction(body model.CreateUserStatement) (*int64, error) {
	if err := r.db.Table("bank_transactions").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) DecreaseMemberCredit(body model.MemberStatementCreateBody) error {

	member, err := r.GetMemberById(body.UserId)
	if err != nil {
		return err
	}
	if body.Amount > 0 && member.Credit >= body.Amount {
		if err := r.db.Transaction(func(tx *gorm.DB) error {
			data := map[string]interface{}{
				"user_id":           member.Id,
				"statement_type_id": body.StatementTypeId,
				"transfer_at":       time.Now(),
				"info":              body.Info,
				"before_balance":    member.Credit,
				"amount":            body.Amount * -1,
				"after_balance":     member.Credit + body.Amount,
			}
			if err := r.db.Table("User_statements").Create(&data).Error; err != nil {
				return err
			}
			if err := tx.Table("user").Where("id = ?", member.Id).UpdateColumn("credit", gorm.Expr("credit - ?", body.Amount)).Error; err != nil {
				return err
			}

			return nil // COMMIT
		}); err != nil {
			return err
		}
	} else {
		return fmt.Errorf("NOT_ENOUGH_CREDIT")
	}
	return nil
}

func (r repo) TransferExternalAccount(body model.ExternalAccountTransferBody) error {

	client := &http.Client{}
	// curl -X POST "https://api.fastbankapi.com/api/v2/statement/transfer" -H "accept: */*" -H "apiKey: xxxxxxxxxx.yyyyyyyyyyy"
	//-H "Content-Type: application/json" -d "{ \"accountFrom\": \"aaaaaaaaaaaaaaaa\", \"accountTo\": \"bbbbbbbbbbbbbb\", \"amount\": \"8\", \"bankCode\": \"bay\", \"pin\": \"ccccc\"}"
	data, _ := json.Marshal(body)
	reqHttp, _ := http.NewRequest("POST", os.Getenv("ACCOUNTING_API_ENDPOINT")+"/api/v2/statement/transfer", bytes.NewBuffer(data))
	reqHttp.Header.Set("apiKey", os.Getenv("ACCOUNTING_API_KEY"))
	reqHttp.Header.Set("Content-Type", "application/json")
	response, err := client.Do(reqHttp)
	if err != nil {
		log.Print(err.Error())
		return fmt.Errorf("EXTERNAL_API_ERROR")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
	}
	// log.Println("response", string(responseData))

	if response.StatusCode != 200 {
		var errorModel model.ExternalAccountError
		errJson := json.Unmarshal(responseData, &errorModel)
		if errJson != nil {
			return fmt.Errorf("EXTERNAL_REPONSE_JSON_ERROR")
		}
		log.Println("errorModel", errorModel)
		if errorModel.Error != "" {
			return fmt.Errorf("%s", errorModel.Error)
		}
		return fmt.Errorf("EXTERNAL_API_ERROR")
	}
	return nil
}

func (r repo) SetExternalStatementRead(body model.ExternalStatementSetReadBody) error {

	log.Println("SetExternalStatementRead.req", helper.StructJson(body))

	client := &http.Client{}
	// curl -X POST "https://api.fastbankapi.com/api/v2/statement/update-read" -H "accept: */*"
	// -H "apiKey: ccccccccccccccccc.dddddddddddddddddd" -H "Content-Type: application/json"
	// -d "{ \"accountNo\": \"aaaaaaaaaaa\", \"statementId\": bbbbbbbbbbbbbbbbbb, \"usedCredit\": true}"
	data, _ := json.Marshal(body)
	reqHttp, _ := http.NewRequest("POST", os.Getenv("ACCOUNTING_API_ENDPOINT")+"/api/v2/statement/update-read", bytes.NewBuffer(data))
	reqHttp.Header.Set("apiKey", os.Getenv("ACCOUNTING_API_KEY"))
	reqHttp.Header.Set("Content-Type", "application/json")
	response, err := client.Do(reqHttp)
	if err != nil {
		log.Print(err.Error())
		return fmt.Errorf("EXTERNAL_API_ERROR")
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
	}
	log.Println("SetExternalStatementRead.response", string(responseData))
	// NO ERROR
	// {
	// 	"message": "update success",
	// 	"success": true
	// }
	// {
	// 	"message": "รายการนี้เคยอ่านไปแล้ว",
	// 	"success": false
	// }

	return nil
}

func (r repo) GetMemberStatementTypeByCode(code string) (*model.MemberStatementType, error) {
	var record model.MemberStatementType
	selectedFields := "types.id, types.code, types.name, types.created_at, types.updated_at"
	if err := r.db.Table("user_statement_status as types").
		Select(selectedFields).
		Where("types.code = ?", code).
		Where("types.deleted_at IS NULL").
		First(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetMemberStatementTypes(req model.SimpleListRequest) (*model.SuccessWithPagination, error) {

	var list []model.MemberStatementType
	var total int64
	var err error

	// COUNT total records for pagination purposes (without limit and offset)
	count := r.db.Table("user_statement_status as types")
	count = count.Select("types.id")
	if req.Search != "" {
		search_like := fmt.Sprintf("%%%s%%", req.Search)
		count = count.Where(r.db.Where("types.name LIKE ?", search_like))
	}
	if err = count.
		Where("types.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "types.id, types.code, types.name, types.created_at, types.updated_at"
		query := r.db.Table("user_statement_status as types")
		query = query.Select(selectedFields)
		if req.Search != "" {
			search_like := fmt.Sprintf("%%%s%%", req.Search)
			query = query.Where(r.db.Where("types.name LIKE ?", search_like))
		}

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}

		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Where("types.deleted_at IS NULL").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, err
		}
	}

	// End count total records for pagination purposes (without limit and offset)
	var result model.SuccessWithPagination
	result.List = list
	result.Total = total
	return &result, nil
}

func (r repo) GetMemberStatementById(id int64) (*model.MemberStatementResponse, error) {
	var record model.MemberStatementResponse
	selectedFields := "statements.id, statements.user_id, statements.statement_type_id, statements.transfer_at, statements.Info, statements.before_balance, statements.amount, statements.after_balance, statements.created_at, statements.updated_at"
	selectedFields += ",statement_types.name as statement_type_name"
	selectedFields += ",users.member_code as member_code, users.username as user_username, users.fullname as user_fullname"
	if err := r.db.Table("User_statements as statements").
		Select(selectedFields).
		Joins("LEFT JOIN user_statement_status AS statement_types ON statement_types.id = statements.statement_type_id").
		Joins("LEFT JOIN user AS users ON users.id = statements.user_id").
		Where("statements.id = ?", id).
		Where("statements.deleted_at IS NULL").
		First(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetMemberStatements(req model.MemberStatementListRequest) (*model.SuccessWithPagination, error) {

	var list []model.MemberStatementResponse
	var total int64
	var err error

	// COUNT total records for pagination purposes (without limit and offset)
	count := r.db.Table("User_statements as statements")
	count = count.Joins("LEFT JOIN user_statement_status AS statement_types ON statement_types.id = statements.statement_type_id")
	count = count.Joins("LEFT JOIN user AS users ON users.id = statements.user_id")
	count = count.Select("statements.id")
	if req.UserId != "" {
		count = count.Where("statements.user_id = ?", req.UserId)
	}
	if req.FromTransferDate != "" {
		count = count.Where("statements.transfer_at >= ?", req.FromTransferDate)
	}
	if req.ToTransferDate != "" {
		count = count.Where("statements.transfer_at <= ?", req.ToTransferDate)
	}
	if req.StatementTypeId != nil {
		count = count.Where("statements.statement_type_id = ?", req.StatementTypeId)
	}
	if req.Search != "" {
		search_like := fmt.Sprintf("%%%s%%", req.Search)
		count = count.Where(r.db.Where("users.member_code LIKE ?", search_like).Or("users.username LIKE ?", search_like).Or("users.fullname LIKE ?", search_like))
	}
	if err = count.
		Where("statements.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "statements.id, statements.user_id, statements.statement_type_id, statements.transfer_at, statements.Info, statements.before_balance, statements.amount, statements.after_balance, statements.created_at, statements.updated_at"
		selectedFields += ",statement_types.name as statement_type_name"
		selectedFields += ",users.member_code as member_code, users.username as user_username, users.fullname as user_fullname"
		query := r.db.Table("User_statements as statements")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN user_statement_status AS statement_types ON statement_types.id = statements.statement_type_id")
		query = query.Joins("LEFT JOIN user AS users ON users.id = statements.user_id")
		if req.UserId != "" {
			query = query.Where("statements.user_id = ?", req.UserId)
		}
		if req.FromTransferDate != "" {
			query = query.Where("statements.transfer_at >= ?", req.FromTransferDate)
		}
		if req.ToTransferDate != "" {
			query = query.Where("statements.transfer_at <= ?", req.ToTransferDate)
		}
		if req.StatementTypeId != nil {
			query = query.Where("statements.statement_type_id = ?", req.StatementTypeId)
		}
		if req.Search != "" {
			search_like := fmt.Sprintf("%%%s%%", req.Search)
			query = query.Where(r.db.Where("users.member_code LIKE ?", search_like).Or("users.username LIKE ?", search_like).Or("users.fullname LIKE ?", search_like))
		}

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Where("statements.deleted_at IS NULL").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, err
		}
	}

	// End count total records for pagination purposes (without limit and offset)
	var result model.SuccessWithPagination
	result.List = list
	result.Total = total
	return &result, nil
}

func (r repo) CreateMemberStatement(data model.MemberStatementCreateBody) (*int64, error) {
	if err := r.db.Table("User_statements").Create(&data).Error; err != nil {
		return nil, err
	}
	return &data.Id, nil
}

func (r repo) GetTransactionType() ([]model.TransactionTypeResponse, error) {
	var record []model.TransactionTypeResponse
	selectedFields := "id, name, label_th, label_en"
	if err := r.db.Table("transaction_type").
		Select(selectedFields).
		Where("deleted_at IS NULL").
		Scan(&record).
		Error; err != nil {
		return record, err
	}
	return record, nil
}

func (r repo) GetStatementType() ([]model.StatementTypeResponse, error) {
	var list []model.StatementTypeResponse
	selectedFields := "id, name, label_th, label_en"
	if err := r.db.Table("statement_type").
		Select(selectedFields).
		Where("deleted_at IS NULL").
		Scan(&list).
		Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (r repo) GetTransactionByStatementId(statementId int64) (model.BankTransaction, error) {

	var record model.BankTransaction

	selectedFields := "log.id as id, log.transaction_type_id as transaction_type_id"
	selectedFields += ", from_account_id, from_bank_id, from_banks.code as from_bank_code, from_banks.name as from_bank_name, log.from_account_name, log.from_account_number"
	selectedFields += ", to_account_id, to_bank_id, to_banks.code as to_bank_code, to_banks.name as to_bank_name, log.to_account_name, log.to_account_number"
	selectedFields += ", log.credit_amount as credit_amount, log.credit_back as credit_back, log.over_amount as over_amount, log.deposit_channel as deposit_channel, log.bonus_amount as bonus_amount, log.bonus_reason as bonus_reason"
	selectedFields += ", log.before_amount as before_amount, log.after_amount as after_amount, log.transfer_at as transfer_at"
	selectedFields += ", log.created_by_admin_id as created_by_admin_id, log.transaction_status_id as transaction_status_id, log.is_auto_credit as is_auto_credit, log.created_at as created_at, log.updated_at as updated_at"
	if err := r.db.Table("bank_transaction as log").
		Select(selectedFields).
		Joins("LEFT JOIN bank as from_banks ON from_banks.id = log.from_bank_id").
		Joins("LEFT JOIN bank as to_banks ON to_banks.id = log.to_bank_id").
		Where("statement_id = ?", statementId).
		Where("deleted_at IS NULL").
		Take(&record).
		Error; err != nil {
		return record, err
	}
	return record, nil
}
func (r repo) GetTransactionRemovedById(id int64) (model.BankTransaction, error) {

	var record model.BankTransaction
	selectedFields := "id, transaction_type_id, from_account_id, from_bank_id, from_account_name, from_account_number, to_account_id, to_bank_id, to_account_name, to_account_number, credit_amount, credit_back, over_amount, deposit_channel, bonus_amount, bonus_reason, before_amount, after_amount, transfer_at, created_by_admin_id, transaction_status_id, is_auto_credit, created_at, updated_at"
	if err := r.db.Table("bank_transaction").
		Select(selectedFields).
		Where("bank_transaction.id = ?", id).
		Where("deleted_at IS NULL").
		First(&record).
		Error; err != nil {
		return record, err
	}
	return record, nil
}

func (r repo) GetTransactionWithdrawOverBudget(transactionId int64) (model.BankTransaction, error) {

	// [********] เพิ่มสถานะ อยู่ในระหว่างการโอนเงิน TRANS_STATUS_WITHDRAW_TRASNFERING

	var record model.BankTransaction

	selectedFields := "id, user_id, transaction_type_id, from_account_id, from_bank_id, from_account_name, from_account_number, to_account_id, to_bank_id, to_account_name, to_account_number, credit_amount, credit_back, over_amount, deposit_channel, bonus_amount, bonus_reason, before_amount, after_amount, transfer_at, created_by_admin_id, transaction_status_id, is_auto_credit, created_at, updated_at"
	selectedFields += ", statement_id"
	if err := r.db.Table("bank_transaction").
		Select(selectedFields).
		Where("id = ?", transactionId).
		// Where("transaction_status_id in (?,?,?,?,?,?,?)", model.TRANS_STATUS_WITHDRAW_OVER_BUDGET, model.TRANS_STATUS_WITHDRAW_OVER_MAX, model.TRANS_STATUS_WITHDRAW_PENDING, model.TRANS_STATUS_WITHDRAW_FAILED, model.TRANS_STATUS_WITHDRAW_REJECTED, model.TRANS_STATUS_WITHDRAW_PENDING, model.TRANS_STATUS_WITHDRAW_APPROVED).
		Where("transaction_status_id IN (?)", []int64{model.TRANS_STATUS_WITHDRAW_OVER_BUDGET, model.TRANS_STATUS_WITHDRAW_OVER_MAX, model.TRANS_STATUS_WITHDRAW_PENDING, model.TRANS_STATUS_WITHDRAW_FAILED, model.TRANS_STATUS_WITHDRAW_REJECTED, model.TRANS_STATUS_WITHDRAW_PENDING, model.TRANS_STATUS_WITHDRAW_APPROVED, model.TRANS_STATUS_WITHDRAW_UNSURE, model.TRANS_STATUS_WITHDRAW_TRASNFERING}).
		Where("deleted_at IS NULL").
		Scan(&record).
		Error; err != nil {
		return record, err
	}
	return record, nil
}

func (r repo) GetTransactionWithdrawOverMaxById(transactionId int64) (model.BankTransaction, error) {

	// [********] เพิ่มสถานะ อยู่ในระหว่างการโอนเงิน TRANS_STATUS_WITHDRAW_TRASNFERING

	var record model.BankTransaction

	selectedFields := "id, user_id, transaction_type_id, from_account_id, from_bank_id, from_account_name, from_account_number, to_account_id, to_bank_id, to_account_name, to_account_number, credit_amount, credit_back, over_amount, deposit_channel, bonus_amount, bonus_reason, before_amount, after_amount, transfer_at, created_by_admin_id, transaction_status_id, is_auto_credit, created_at, updated_at"
	if err := r.db.Table("bank_transaction").
		Select(selectedFields).
		Where("id = ?", transactionId).
		// Where("transaction_status_id in (?,?,?,?,?)", model.TRANS_STATUS_WITHDRAW_OVER_MAX, model.TRANS_STATUS_WITHDRAW_FAILED, model.TRANS_STATUS_WITHDRAW_REJECTED, model.TRANS_STATUS_WITHDRAW_PENDING, model.TRANS_STATUS_WITHDRAW_APPROVED).
		Where("transaction_status_id IN (?)", []int64{model.TRANS_STATUS_WITHDRAW_OVER_MAX, model.TRANS_STATUS_WITHDRAW_FAILED, model.TRANS_STATUS_WITHDRAW_REJECTED, model.TRANS_STATUS_WITHDRAW_PENDING, model.TRANS_STATUS_WITHDRAW_APPROVED, model.TRANS_STATUS_WITHDRAW_UNSURE, model.TRANS_STATUS_WITHDRAW_TRASNFERING}).
		Where("deleted_at IS NULL").
		Scan(&record).
		Error; err != nil {
		return record, err
	}
	return record, nil
}

func (r repo) GetPossibleOwnersByStatementId(req model.GetPossibleOwnersRequest) ([]model.Member, int64, error) {

	var list []model.Member
	var total int64
	var err error

	// KBANK don't have account number else   && req.FromBankId == model.BANK_ID_KBANK (case maybe other bank transfer)
	if req.FromAccountNumber == "" {
		return list, total, nil
	}

	// Count the total number of matching users first.
	count := r.db.Table("user")
	count = count.Select("user.id")

	if req.ToBankId != model.BANK_ID_GSB {
		count = count.Where("user.bank_id = ?", req.FromBankId)
	}
	if req.ToBankId == model.BANK_ID_KBANK {
		count = count.Where("user.bank_account LIKE ?", "%"+req.FromAccountNumber+"%")
	} else if req.ToBankId == model.BANK_ID_TRUE && req.FromBankId == model.BANK_ID_TRUE {
		// 2024/05/08 แยกวิธีเช็ค true confirm P.lay
		// 2024/11/11 เปลี่ยน เอา phone true ออก OR user.phone = ?
		count = count.Where("user.bank_account = ?", req.FromAccountNumber)
	} else if req.ToBankId == model.BANK_ID_GSB || req.ToBankId == model.BANK_ID_TRUE {
		count = count.Where("user.bank_account = ?", req.FromAccountNumber)

	} else {
		count = count.Where("user.bank_account LIKE ?", "%"+req.FromAccountNumber)
	}
	count = count.Where("user.deleted_at IS NULL")
	if err = count.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if total > 0 {
		selectedFields := "user.id, user.ref_by, user.member_code, user.username, user.fullname, user.credit, bank.name AS bank_name, user.bank_account, user.Phone"
		selectedFields += ", user.user_type_id, user_type.name as user_type_name"
		selectedFields += ", user.bank_id"
		// Retrieve the matching users' details.
		query := r.db.Table("user as user")
		query = query.Joins("LEFT JOIN bank ON bank.id = user.bank_id")
		query = query.Joins("LEFT JOIN recommend_channel ON recommend_channel.id = user.channel_id")
		query = query.Joins("LEFT JOIN user_type ON user_type.id = user.user_type_id")
		query = query.Select(selectedFields)

		if req.ToBankId != model.BANK_ID_GSB {
			query = query.Where("user.bank_id = ?", req.FromBankId)
		}
		if req.ToBankId == model.BANK_ID_KBANK {
			query = query.Where("user.bank_account LIKE ?", "%"+req.FromAccountNumber+"%")
		} else if req.ToBankId == model.BANK_ID_TRUE && req.FromBankId == model.BANK_ID_TRUE {
			// 2024/05/08 แยกวิธีเช็ค true confirm P.lay
			query = query.Where("user.bank_account = ?", req.FromAccountNumber)
		} else if req.ToBankId == model.BANK_ID_GSB || req.ToBankId == model.BANK_ID_TRUE {
			query = query.Where("user.bank_account = ?", req.FromAccountNumber)

		} else {
			query = query.Where("user.bank_account LIKE ?", "%"+req.FromAccountNumber)
		}
		query = query.Where("user.deleted_at IS NULL")
		if err := query.Find(&list).Error; err != nil {
			return nil, 0, err
		}
	}

	return list, total, nil
}

func (r repo) GetBankTransactionDepositList(req model.GetBankTransactionDepositListRequest) ([]model.GetBankTransactionDepositListResponse, int64, error) {

	// DEPOSIT AND BONUS ONLY
	var list []model.GetBankTransactionDepositListResponse
	var total int64

	// ถ้ากรองวันแล้วเร็ว เอา Fix ออกก่อน
	// total := int64(1000)

	count := r.db.Table("bank_transaction")
	count = count.Select("bank_transaction.id")

	if req.FromTransferDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.FromTransferDate)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("bank_transaction.transfer_at >= ? ", startDateAtBkk)
	}
	if req.ToTransferDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.ToTransferDate)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("bank_transaction.transfer_at <=  ?", endDateAtBkk)
	}
	if req.TransactionTypeId != nil {
		count = count.Where("bank_transaction.transaction_type_id = ?", req.TransactionTypeId)
	}
	if req.Search != "" {
		// Join เฉพาะตอนที่จะ Where table User
		count = count.Joins("LEFT JOIN user ON user.id = bank_transaction.user_id")
		search_like := fmt.Sprintf("%%%s%%", req.Search)
		count = count.Where(r.db.Where("user.member_code LIKE ?", search_like).Or("user.username LIKE ?", search_like).Or("user.fullname LIKE ?", search_like).Or("bank_transaction.from_account_number LIKE ?", search_like).Or("bank_transaction.to_account_number LIKE ?", search_like))
	}
	if req.ActionByAdminId != nil {
		count = count.Where("bank_transaction.confirmed_by_admin_id = ? OR bank_transaction.created_by_admin_id = ?", req.ActionByAdminId, req.ActionByAdminId)
	}
	if req.ActionByAdminType != "" && req.ActionByAdminType == "ALL" {
		count = count.Where("bank_transaction.confirmed_by_admin_id IS NOT NULL OR bank_transaction.created_by_admin_id IS NOT NULL")
	}
	if req.TransactionStatus != "" {
		// Split the comma-separated string into an array of integers
		statusIds := strings.Split(req.TransactionStatus, ",")
		intStatusIds := make([]int64, len(statusIds))
		for i, idStr := range statusIds {
			id, err := strconv.ParseInt(idStr, 10, 64)
			if err != nil {
				// Handle the error, e.g., return an error response
				return nil, 0, err
			}
			intStatusIds[i] = id
		}
		count = count.Where("bank_transaction.transaction_status_id IN (?)", intStatusIds)
	}
	if err := count.
		Where("bank_transaction.transaction_type_id IN (?, ?)", model.TRANSACTION_TYPE_BONUS, model.TRANSACTION_TYPE_DEPOSIT).
		Where("bank_transaction.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, 0, err
	}

	if total > 0 {
		// Snake case ใข้ไม่ได้ครับ ?? ผมลองละ
		selectedFields := "bank_transaction.id AS id, bank_transaction.statement_id AS statement_id, bank_transaction.promotion_id AS promotion_id, bank_transaction.credit_amount AS credit_amount, bank_transaction.credit_back AS credit_back, bank_transaction.deposit_channel AS deposit_channel, bank_transaction.over_amount AS over_amount, bank_transaction.bonus_amount AS bonus_amount"
		selectedFields += ", bank_transaction.from_account_id AS from_account_id, bank_transaction.from_bank_id AS from_bank_id, bank_transaction.from_account_number AS from_account_number,from_bank.name AS from_bank_name"
		selectedFields += ", bank_transaction.is_auto_credit AS is_auto_credit, bank_transaction.transfer_at AS transfer_at"
		selectedFields += ", bank_transaction.slip_img_url"
		selectedFields += ", user.id AS user_id, user.member_code AS user_member_code, user.fullname AS user_fullname"
		selectedFields += ", user.phone AS username" // use phone instead of username
		selectedFields += ", transaction_type.id AS transaction_type_id, transaction_type.label_th AS transaction_type_th, transaction_type.label_en AS transaction_type_en"
		selectedFields += ", bank_account.id AS to_account_id, bank_account.bank_id AS to_bank_id, to_bank.name AS to_bank_name, bank_account.account_name AS to_account_name, bank_account.account_number AS to_account_number"
		selectedFields += ", transaction_status.id AS transaction_status_id, transaction_status.label_th AS transaction_status_th, transaction_status.label_en AS transaction_status_en"
		selectedFields += ", bank_transaction.confirmed_at AS confirmed_at, bank_transaction.confirmed_by_admin_id AS confirmed_by_admin_id"
		selectedFields += ", from_bank.icon_url AS from_bank_icon_url, to_bank.icon_url AS to_bank_icon_url"
		selectedFields += ",bank_transaction.cancel_remark AS cancel_remark"
		selectedFields += ", bank_statement.detail AS detail"
		selectedFields += ", CASE WHEN bank_transaction.created_by_admin_id = 0 THEN 'อัตโนมัติ' ELSE admincreate.username END AS created_by_username"
		selectedFields += ", CASE WHEN bank_transaction.confirmed_by_admin_id = 0 THEN 'อัตโนมัติ' ELSE adminconfirmed.username END AS confirmed_by_username"
		selectedFields += ", bank_transaction.confirmed_at AS confirmed_at"
		// OriData
		selectedFields += ", bank_transaction.from_account_name AS from_account_name2, bank_transaction.from_account_number AS from_account_number2"
		selectedFields += ", bank_transaction.to_account_name AS to_account_name2, bank_transaction.to_account_number AS to_account_number2"

		// LAOS
		selectedFields += ", bank_transaction.currency_amount AS currency_amount"

		query := r.db.Table("bank_transaction")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN user ON user.id = bank_transaction.user_id")
		query = query.Joins("LEFT JOIN transaction_type ON transaction_type.id = bank_transaction.transaction_type_id")
		query = query.Joins("LEFT JOIN bank_statement ON bank_statement.id = bank_transaction.statement_id")
		query = query.Joins("LEFT JOIN bank_account ON bank_account.id = bank_transaction.to_account_id")
		query = query.Joins("LEFT JOIN bank AS to_bank ON to_bank.id = bank_transaction.to_bank_id")
		query = query.Joins("LEFT JOIN bank AS from_bank ON from_bank.id = bank_transaction.from_bank_id")
		query = query.Joins("LEFT JOIN transaction_status ON transaction_status.id = bank_transaction.transaction_status_id")
		query = query.Joins("LEFT JOIN admin AS admincreate ON admincreate.id = bank_transaction.created_by_admin_id")
		query = query.Joins("LEFT JOIN admin AS adminconfirmed ON adminconfirmed.id = bank_transaction.confirmed_by_admin_id")

		if req.FromTransferDate != "" {
			startDateAtBkk, err := r.ParseBodBkk(req.FromTransferDate)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("bank_transaction.transfer_at >= ? ", startDateAtBkk)
		}
		if req.ToTransferDate != "" {
			endDateAtBkk, err := r.ParseEodBkk(req.ToTransferDate)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("bank_transaction.transfer_at <=  ?", endDateAtBkk)
		}
		if req.TransactionTypeId != nil {
			query = query.Where("bank_transaction.transaction_type_id = ?", req.TransactionTypeId)
		}
		if req.Search != "" {
			search_like := fmt.Sprintf("%%%s%%", req.Search)
			query = query.Where(r.db.Where("user.member_code LIKE ?", search_like).Or("user.username LIKE ?", search_like).Or("user.fullname LIKE ?", search_like).Or("bank_transaction.from_account_number LIKE ?", search_like).Or("bank_transaction.to_account_number LIKE ?", search_like))
		}

		if req.ActionByAdminId != nil {
			query = query.Where("bank_transaction.confirmed_by_admin_id = ? OR bank_transaction.created_by_admin_id = ?", req.ActionByAdminId, req.ActionByAdminId)
		}
		if req.ActionByAdminType != "" && req.ActionByAdminType == "ALL" {
			query = query.Where("bank_transaction.confirmed_by_admin_id IS NOT NULL OR bank_transaction.created_by_admin_id IS NOT NULL")
		}

		if req.TransactionStatus != "" {
			// Split the comma-separated string into an array of integers
			statusIds := strings.Split(req.TransactionStatus, ",")
			intStatusIds := make([]int64, len(statusIds))

			for i, idStr := range statusIds {
				id, err := strconv.ParseInt(idStr, 10, 64)
				if err != nil {
					// Handle the error, e.g., return an error response
					return nil, 0, err
				}
				intStatusIds[i] = id
			}

			query = query.Where("bank_transaction.transaction_status_id IN (?)", intStatusIds)
		}

		//pagination
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Where("bank_transaction.transaction_type_id IN (?, ?)", model.TRANSACTION_TYPE_BONUS, model.TRANSACTION_TYPE_DEPOSIT).
			Where("bank_transaction.deleted_at IS NULL").
			Order("bank_transaction.transfer_at DESC").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, 0, err
		}

	}
	return list, total, nil
}

func (r repo) GetDepositTransactionById(transId int64) (*model.GetBankTransactionDepositListResponse, error) {

	var record model.GetBankTransactionDepositListResponse
	// Snake case ใข้ไม่ได้ครับ ?? ผมลองละ
	selectedFields := "bank_transaction.id AS id, bank_transaction.statement_id AS statement_id, bank_transaction.promotion_id AS promotion_id, bank_transaction.credit_amount AS credit_amount, bank_transaction.credit_back AS credit_back, bank_transaction.deposit_channel AS deposit_channel, bank_transaction.over_amount AS over_amount, bank_transaction.bonus_amount AS bonus_amount"
	selectedFields += ", bank_transaction.from_account_id AS from_account_id, bank_transaction.from_bank_id AS from_bank_id, bank_transaction.from_account_number AS from_account_number,from_bank.name AS from_bank_name"
	selectedFields += ", bank_transaction.is_auto_credit AS is_auto_credit, bank_transaction.transfer_at AS transfer_at"
	selectedFields += ", bank_transaction.slip_img_url"
	selectedFields += ", user.id AS user_id, user.member_code AS user_member_code, user.fullname AS user_fullname"
	selectedFields += ", user.phone AS username" // use phone instead of username
	selectedFields += ", transaction_type.id AS transaction_type_id, transaction_type.label_th AS transaction_type_th, transaction_type.label_en AS transaction_type_en"
	selectedFields += ", bank_account.id AS to_account_id, bank_account.bank_id AS to_bank_id, to_bank.name AS to_bank_name, bank_account.account_name AS to_account_name, bank_account.account_number AS to_account_number"
	selectedFields += ", transaction_status.id AS transaction_status_id, transaction_status.label_th AS transaction_status_th, transaction_status.label_en AS transaction_status_en"
	selectedFields += ", bank_transaction.confirmed_at AS confirmed_at, bank_transaction.confirmed_by_admin_id AS confirmed_by_admin_id"
	selectedFields += ", from_bank.icon_url AS from_bank_icon_url, to_bank.icon_url AS to_bank_icon_url"
	selectedFields += ", CASE WHEN bank_transaction.created_by_admin_id = 0 THEN 'อัตโนมัติ' ELSE admincreate.username END AS created_by_username"
	selectedFields += ", CASE WHEN bank_transaction.confirmed_by_admin_id = 0 THEN 'อัตโนมัติ' ELSE adminconfirmed.username END AS confirmed_by_username"
	// OriData
	selectedFields += ", bank_transaction.to_account_name AS to_account_name2, bank_transaction.to_account_number AS to_account_number2"

	if err := r.db.Table("bank_transaction").
		Select(selectedFields).
		Joins("LEFT JOIN user ON user.id = bank_transaction.user_id").
		Joins("LEFT JOIN transaction_type ON transaction_type.id = bank_transaction.transaction_type_id").
		Joins("LEFT JOIN bank_account ON bank_account.id = bank_transaction.to_account_id").
		Joins("LEFT JOIN bank AS to_bank ON to_bank.id = bank_transaction.to_bank_id").
		Joins("LEFT JOIN bank AS from_bank ON from_bank.id = bank_transaction.from_bank_id").
		Joins("LEFT JOIN transaction_status ON transaction_status.id = bank_transaction.transaction_status_id").
		Joins("LEFT JOIN admin AS admincreate ON admincreate.id = bank_transaction.created_by_admin_id").
		Joins("LEFT JOIN admin AS adminconfirmed ON adminconfirmed.id = bank_transaction.confirmed_by_admin_id").
		Where("bank_transaction.transaction_type_id IN (?, ?)", model.TRANSACTION_TYPE_BONUS, model.TRANSACTION_TYPE_DEPOSIT).
		Where("bank_transaction.id = ?", transId).
		Where("bank_transaction.deleted_at IS NULL").
		Scan(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil

}

func (r repo) GetPendingDespositTransactionWithSlip(userId int64) (*model.BankTransaction, error) {

	var record model.BankTransaction

	selectedFields := "*"
	if err := r.db.Table("bank_transaction").
		Select(selectedFields).
		Where("transaction_type_id = ?", model.TRANSACTION_TYPE_DEPOSIT).
		Where("bank_transaction.transaction_status_id = ?", model.TRANS_STATUS_PENDING).
		Where("bank_transaction.user_id = ?", userId).
		Where("bank_transaction.deposit_channel = ?", "UPLOAD_SLIP").
		Where("bank_transaction.deleted_at IS NULL").
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetTransactionByIdForNoti(transId int64) (*model.GetBankTransactionDepositListResponse, error) {

	var record model.GetBankTransactionDepositListResponse
	// Snake case ใข้ไม่ได้ครับ ?? ผมลองละ
	selectedFields := "bank_transaction.id AS id, bank_transaction.statement_id AS statement_id, bank_transaction.promotion_id AS promotion_id, bank_transaction.credit_amount AS credit_amount, bank_transaction.credit_back AS credit_back, bank_transaction.deposit_channel AS deposit_channel, bank_transaction.over_amount AS over_amount, bank_transaction.bonus_amount AS bonus_amount"
	selectedFields += ", bank_transaction.from_account_id AS from_account_id, bank_transaction.from_bank_id AS from_bank_id, bank_transaction.from_account_number AS from_account_number,from_bank.name AS from_bank_name"
	selectedFields += ", bank_transaction.is_auto_credit AS is_auto_credit, bank_transaction.transfer_at AS transfer_at"
	selectedFields += ", bank_transaction.slip_img_url"
	selectedFields += ", user.id AS user_id, user.member_code AS user_member_code, user.fullname AS user_fullname"
	selectedFields += ", user.phone AS username" // use phone instead of username
	selectedFields += ", transaction_type.id AS transaction_type_id, transaction_type.label_th AS transaction_type_th, transaction_type.label_en AS transaction_type_en"
	selectedFields += ", bank_account.id AS to_account_id, bank_account.bank_id AS to_bank_id, to_bank.name AS to_bank_name, bank_account.account_name AS to_account_name, bank_account.account_number AS to_account_number"
	selectedFields += ", transaction_status.id AS transaction_status_id, transaction_status.label_th AS transaction_status_th, transaction_status.label_en AS transaction_status_en"
	selectedFields += ", bank_transaction.confirmed_at AS confirmed_at, bank_transaction.confirmed_by_admin_id AS confirmed_by_admin_id"
	selectedFields += ", from_bank.icon_url AS from_bank_icon_url, to_bank.icon_url AS to_bank_icon_url"
	selectedFields += ", bank_transaction.after_amount AS after_amount, bank_transaction.before_amount AS before_amount"
	selectedFields += ", CASE WHEN bank_transaction.created_by_admin_id = 0 THEN 'อัตโนมัติ' ELSE admincreate.username END AS created_by_username"
	selectedFields += ", CASE WHEN bank_transaction.confirmed_by_admin_id = 0 THEN 'อัตโนมัติ' ELSE adminconfirmed.username END AS confirmed_by_username"
	// OriData
	selectedFields += ", bank_transaction.from_account_name AS from_account_name2, bank_transaction.from_account_number AS from_account_number2"
	selectedFields += ", bank_transaction.to_account_name AS to_account_name2, bank_transaction.to_account_number AS to_account_number2"

	if err := r.db.Table("bank_transaction").
		Select(selectedFields).
		Joins("LEFT JOIN user ON user.id = bank_transaction.user_id").
		Joins("LEFT JOIN transaction_type ON transaction_type.id = bank_transaction.transaction_type_id").
		Joins("LEFT JOIN bank_account ON bank_account.id = bank_transaction.to_account_id").
		Joins("LEFT JOIN bank AS to_bank ON to_bank.id = bank_transaction.to_bank_id").
		Joins("LEFT JOIN bank AS from_bank ON from_bank.id = bank_transaction.from_bank_id").
		Joins("LEFT JOIN transaction_status ON transaction_status.id = bank_transaction.transaction_status_id").
		Joins("LEFT JOIN admin AS admincreate ON admincreate.id = bank_transaction.created_by_admin_id").
		Joins("LEFT JOIN admin AS adminconfirmed ON adminconfirmed.id = bank_transaction.confirmed_by_admin_id").
		Where("bank_transaction.id = ?", transId).
		Where("bank_transaction.deleted_at IS NULL").
		Scan(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetWebUserTransactionList(req model.GetUserTransactionListRequest) ([]model.GetUserTransactionListBody, int64, error) {

	var list []model.GetUserTransactionListBody
	var total int64
	var err error

	count := r.db.Table("bank_transaction")
	count = count.Select("bank_transaction.id")
	count = count.Joins("LEFT JOIN transaction_type ON transaction_type.id = bank_transaction.transaction_type_id")
	// UNUSED count = count.Joins("LEFT JOIN transaction_status ON transaction_status.id = bank_transaction.transaction_status_id")
	if req.FromTransferDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.FromTransferDate)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("bank_transaction.transfer_at >= ? ", startDateAtBkk)
	}
	if req.ToTransferDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.ToTransferDate)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("bank_transaction.transfer_at <=  ?", endDateAtBkk)
	}
	if req.TransactionTypeId != nil {
		count = count.Where("bank_transaction.transaction_type_id = ?", req.TransactionTypeId)
	} else {
		count = count.Where("bank_transaction.transaction_type_id IN (?, ?)", model.TRANSACTION_TYPE_WITHDRAW, model.TRANSACTION_TYPE_DEPOSIT)
	}
	if err = count.
		Where("bank_transaction.deleted_at IS NULL").
		Where("user_id = ?", req.UserId).
		Count(&total).
		Error; err != nil {
		return nil, 0, err
	}

	if total > 0 {
		selectedFields := "bank_transaction.id AS id, bank_transaction.statement_id AS statement_id "
		selectedFields += ",bank_transaction.credit_amount AS credit_amount, bank_transaction.before_amount AS before_amount, bank_transaction.after_amount AS after_amount"
		selectedFields += ",bank_transaction.transfer_at AS transfer_at"
		selectedFields += ", transaction_status.id AS transaction_status_id, transaction_status.label_th AS transaction_status_th"
		selectedFields += ", transaction_type.id AS transaction_type_id, transaction_type.label_th AS transaction_type_th"

		query := r.db.Table("bank_transaction")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN transaction_type ON transaction_type.id = bank_transaction.transaction_type_id")
		query = query.Joins("LEFT JOIN transaction_status ON transaction_status.id = bank_transaction.transaction_status_id")
		if req.FromTransferDate != "" {
			startDateAtBkk, err := r.ParseBodBkk(req.FromTransferDate)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("bank_transaction.transfer_at >= ? ", startDateAtBkk)
		}
		if req.ToTransferDate != "" {
			endDateAtBkk, err := r.ParseEodBkk(req.ToTransferDate)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("bank_transaction.transfer_at <=  ?", endDateAtBkk)
		}
		if req.TransactionTypeId != nil {
			query = query.Where("bank_transaction.transaction_type_id = ?", req.TransactionTypeId)
		} else {
			query = query.Where("bank_transaction.transaction_type_id IN (?, ?)", model.TRANSACTION_TYPE_WITHDRAW, model.TRANSACTION_TYPE_DEPOSIT)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Where("bank_transaction.deleted_at IS NULL").
			Where("user_id = ?", req.UserId).
			Order("bank_transaction.transfer_at DESC").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, 0, err
		}
	}
	return list, total, nil
}

func (r repo) GetUserBankDetailById(userId int64) (*model.UserBankDetailBody, error) {

	var record model.UserBankDetailBody

	selectedFields := "user.id AS id, user.member_code AS member_code, user.phone AS phone, user_status.name as status, user.fullname AS fullname"
	selectedFields += ", user.credit AS credit"
	selectedFields += ", user.bank_account AS bank_account, user.bank_id AS bank_id, bank.name AS bank_name, bank.code AS bank_code"
	if err := r.db.Table("user").
		Select(selectedFields).
		Joins("LEFT JOIN user_status ON user_status.id = user.user_status_id").
		Joins("LEFT JOIN bank ON bank.id = user.bank_id").
		Where("user.deleted_at IS NULL").
		Where("user.id = ?", userId).
		First(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}
func (r repo) GetUserBankDetailByMemberCode(memberCode string) (*model.UserBankDetailBody, error) {
	var record model.UserBankDetailBody

	selectedFields := "user.id AS id, user.member_code AS member_code, user.phone AS phone, user_status.name as status, user.fullname AS fullname"
	selectedFields += ", user.credit AS credit, user.bank_account AS bank_account"
	selectedFields += ", user.bank_id AS bank_id, bank.name AS bank_name, bank.code AS bank_code"
	if err := r.db.Table("user").
		Select(selectedFields).
		Joins("LEFT JOIN user_status ON user_status.id = user.user_status_id").
		Joins("LEFT JOIN bank ON bank.id = user.bank_id").
		Where("user.deleted_at IS NULL").
		Where("user.member_code = ?", memberCode).
		First(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetBankWithdrawConfig(amount float64) (*model.BankAccount, *string, error) {

	// ยังไม่ได้กลับมาเก็บ code err
	var record model.BankAccount
	status := model.AUTO_WITHDRAW
	selectedFields := "bank_account.id AS id, bank_account.pin_code AS pin_code, bank_account.account_name AS account_name, bank_account.account_number AS account_number"
	selectedFields += ", bank_account.bank_id AS bank_id, bank.code AS bank_code, bank.name AS bank_name"

	// First Query
	r.db.Table("bank_account").
		Select(selectedFields).
		Joins("LEFT JOIN bank ON bank.id = bank_account.bank_id").
		Joins("LEFT JOIN bank_account_type ON bank_account_type.id = bank_account.account_type_id").
		Where("bank_account_type.id in (?,?)", model.BANK_ACCOUNT_TYPE_WITHDRAW_ONLY, model.BANK_ACCOUNT_TYPE_BOTH).
		Where("bank_account.account_balance >= ?", amount).
		// Where("bank_account.auto_withdraw_max_amount >= ?", amount).
		// เปลี่ยน เป็นเอามาจาก config แทน
		Where("bank_account.deleted_at IS NULL").
		First(&record)

	if record.Id == 0 {
		status = model.OUT_OF_CONFIG_AMOUNT
		// Second Query
		r.db.Table("bank_account").
			Select(selectedFields).
			Joins("LEFT JOIN bank ON bank.id = bank_account.bank_id").
			Joins("LEFT JOIN bank_account_type ON bank_account_type.id = bank_account.account_type_id").
			Where("bank_account_type.id in (?,?)", model.BANK_ACCOUNT_TYPE_WITHDRAW_ONLY, model.BANK_ACCOUNT_TYPE_BOTH).
			Where("bank_account.account_balance >= ?", amount).
			// Where("bank_account.auto_withdraw_max_amount <= ?", amount).
			// เปลี่ยน เป็นเอามาจาก config แทน
			Where("bank_account.deleted_at IS NULL").
			First(&record)
	}

	return &record, &status, nil
}

func (r repo) WithdrawWithFastBank(transferBody model.WithdrawTransferFastBankBody) (*model.WithdrawTransferFastBankResponse, error) {

	log.Println("WithdrawWithFastBank", helper.StructJson(transferBody))

	// Construct the API URL
	url := os.Getenv("ACCOUNTING_API_ENDPOINT") + "/api/v2/statement/transfer"

	// Convert the transferBody struct to JSON
	requestData, err := json.Marshal(transferBody)
	if err != nil {
		return nil, err
	}
	log.Println("WithdrawWithFastBank.requestData", string(requestData))
	// Create a new HTTP client
	client := &http.Client{}

	// Create a POST request with the JSON data
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(requestData))
	if err != nil {
		log.Println("err", err)
		return nil, err
	}

	// Set the "apiKey" header using the environment variable
	req.Header.Set("apiKey", os.Getenv("ACCOUNTING_API_KEY"))
	req.Header.Set("Content-Type", "application/json")

	// Send the request
	response, err := client.Do(req)
	if err != nil {
		log.Println("WithdrawWithFastBank.err", err)
		return nil, err
	}
	defer response.Body.Close()

	responseBodyBytes, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println("WithdrawWithFastBank.err", err)
		return nil, err
	}

	log.Println("WithdrawWithFastBank.responseBody", string(responseBodyBytes))
	// Parse the JSON response into the WithdrawTransferFastBankResponse struct

	// Create a buffer with the response body bytes
	responseBuffer := bytes.NewBuffer(responseBodyBytes)

	// Parse the JSON response into a map
	var responseBodyWithDescription model.WithdrawTransferFastBankResponse
	var responseBodyMap map[string]interface{}
	err = json.Unmarshal(responseBodyBytes, &responseBodyMap)
	if err != nil {
		responseBodyWithDescription.Status.Code = 500
		responseBodyWithDescription.Status.Header = "ไม่สามารถดึงข้อมูลได้"
		responseBodyWithDescription.Status.Description = "ไม่สามารถยืนยันรายการถอนได้ โปรดตรวจสอบสเตทเม้นธนาคารว่ามีเงินออกหรือไม่ หากระบบพบยอดโอนออกไปแล้ว จะอัพเดตสถานะเป็นสำเร็จในภายหลังอัตโนมัติ"
		return &responseBodyWithDescription, fmt.Errorf("ERROR: %v, STRUCT %v", err, helper.StructJson(responseBodyMap))
	}

	var responseBodyWithError model.WithdrawTransferFastBankBadResponse

	if _, ok := responseBodyMap["status"]; ok {
		// Decode into WithdrawTransferFastBankResponse struct
		err = json.NewDecoder(responseBuffer).Decode(&responseBodyWithDescription)
		if err != nil {
			log.Println("WithdrawWithFastBank.err.responseBodyWithDescription", err)
			responseBodyWithDescription.Status.Code = 500
			responseBodyWithDescription.Status.Header = "ไม่สามารถดึงข้อมูลได้"
			responseBodyWithDescription.Status.Description = "ไม่สามารถยืนยันรายการถอนได้ โปรดตรวจสอบสเตทเม้นธนาคารว่ามีเงินออกหรือไม่ หากระบบพบยอดโอนออกไปแล้ว จะอัพเดตสถานะเป็นสำเร็จในภายหลังอัตโนมัติ"
			return &responseBodyWithDescription, err
		}
	} else if _, ok := responseBodyMap["errorMessage"]; ok {
		// Decode into WithdrawTransferFastBankBadResponse struct
		err = json.NewDecoder(responseBuffer).Decode(&responseBodyWithError)
		if err != nil {
			log.Println("WithdrawWithFastBank.err.responseBodyWithError", err)
			responseBodyWithDescription.Status.Code = 500
			responseBodyWithDescription.Status.Header = "ไม่สามารถดึงข้อมูลได้"
			responseBodyWithDescription.Status.Description = fmt.Sprintf("%v", helper.StructJson(responseBodyMap))
			return &responseBodyWithDescription, err
		}
		responseBodyWithDescription.Status.Code = 500
		responseBodyWithDescription.Status.Description = responseBodyWithError.ErrorMessage
	} else {
		responseBodyWithDescription.Status.Code = 500
		responseBodyWithDescription.Status.Header = "new error"
		responseBodyWithDescription.Status.Description = fmt.Sprintf("%v", helper.StructJson(responseBodyMap))
	}

	log.Println("WithdrawWithFastBank.responseBody", responseBodyWithDescription)
	// Check the response status code
	if response.StatusCode != http.StatusOK {
		responseBodyWithDescription.Status.Code = 500
		return &responseBodyWithDescription, fmt.Errorf("FASTBANK RESPONSE %s", helper.StructJson(response.StatusCode))
	}
	// Return the parsed response data
	return &responseBodyWithDescription, nil
}

func (r repo) UpdateAdminAndTransactionStatus(id int64, body model.UpdateConfirmAutoWithdrawBody) error {

	if err := r.db.Table("bank_transaction").Where("id = ?", id).Updates(&body).Error; err != nil {
		return err
	}

	return nil
}

func (r repo) UpdateUserTransactionConfirmBy(id int64, body model.UpdateConfirmAutoWithdrawBody) error {

	if err := r.db.Table("user_transaction").Where("ref_id = ?", id).Where("type_id = ?", model.TRANSACTION_TYPE_WITHDRAW).Updates(map[string]interface{}{
		"confirm_admin_id": body.ConfirmedByAdminId,
	}).Error; err != nil {
		return err
	}

	return nil
}

func (r repo) UpdateTransactionStatusTransferingToSuccess(id int64, secondUsed string) error {

	// Just Success Status
	updateBody := map[string]interface{}{
		"transaction_status_id": model.TRANS_STATUS_WITHDRAW_SUCCESS,
	}
	if secondUsed != "" {
		updateBody["auto_process_timer"] = secondUsed
	} else {
		// set default ไม่อยากให้เป็น 0 ตามพี่มิงค์ ของ การ์ด 2024/05/09
		updateBody["auto_process_timer"] = "1"
	}
	if len(secondUsed) > 8 {
		updateBody["auto_process_timer"] = "9999.99"
	}
	if err := r.db.Table("bank_transaction").Where("id = ?", id).Where("transaction_status_id = ?", model.TRANS_STATUS_WITHDRAW_TRASNFERING).Updates(&updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) RollbackTransactionStatusTransferingToConfirmed(id int64) error {

	// Just Rollback Status
	updateBody := map[string]interface{}{
		// "transaction_status_id": model.TRANS_STATUS_WITHDRAW_APPROVED,
		"transaction_status_id": model.TRANS_STATUS_WITHDRAW_UNSURE, // int64 = 16 // ไม่แน่ใจ ว่าผ่านหรือไม่ == ให้เช็คแล้วกดดำเนินการเอง ถ้าเป็น 16 จะแสดงเหตุผลให้ด้วย
	}
	if err := r.db.Table("bank_transaction").Where("id = ?", id).Where("transaction_status_id = ?", model.TRANS_STATUS_WITHDRAW_TRASNFERING).Updates(&updateBody).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetBankTransactionWithdrawList(req model.GetBankTransactionWithdrawListRequest) ([]model.GetBankTransactionWithdrawListResponse, int64, error) {

	var list []model.GetBankTransactionWithdrawListResponse
	var total int64

	// ถ้ากรองวันแล้วเร็ว เอา Fix ออกก่อน
	// total := int64(1000)

	count := r.db.Table("bank_transaction")
	count = count.Select("bank_transaction.id")
	if req.FromTransferDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.FromTransferDate)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("bank_transaction.transfer_at >= ? ", startDateAtBkk)
	}
	if req.ToTransferDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.ToTransferDate)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("bank_transaction.transfer_at <=  ?", endDateAtBkk)
	}
	if req.TransactionTypeId != nil {
		count = count.Where("bank_transaction.transaction_type_id = ?", req.TransactionTypeId)
	}
	if req.Search != "" {
		// Join เฉพาะตอนที่จะ Where table User
		count = count.Joins("LEFT JOIN user ON user.id = bank_transaction.user_id")
		search_like := fmt.Sprintf("%%%s%%", req.Search)
		count = count.Where(r.db.Where("user.member_code LIKE ?", search_like).Or("user.username LIKE ?", search_like).Or("user.fullname LIKE ?", search_like).Or("bank_transaction.from_account_number LIKE ?", search_like).Or("bank_transaction.to_account_number LIKE ?", search_like))
	}
	if req.ActionByAdminId != nil {
		count = count.Where("bank_transaction.confirmed_by_admin_id = ? OR bank_transaction.created_by_admin_id = ?", req.ActionByAdminId, req.ActionByAdminId)
	}
	if req.ActionByAdminType != "" && req.ActionByAdminType == "ALL" {
		count = count.Where("bank_transaction.confirmed_by_admin_id IS NOT NULL OR bank_transaction.created_by_admin_id IS NOT NULL")
	}

	if req.TransactionStatus != "" {
		// Split the comma-separated string into an array of integers
		statusIds := strings.Split(req.TransactionStatus, ",")
		intStatusIds := make([]int64, len(statusIds))

		for i, idStr := range statusIds {
			id, err := strconv.ParseInt(idStr, 10, 64)
			if err != nil {
				// Handle the error, e.g., return an error response
				return nil, 0, err
			}
			intStatusIds[i] = id
		}
		count = count.Where("bank_transaction.transaction_status_id IN (?)", intStatusIds)
	}
	if err := count.
		Where("bank_transaction.transaction_type_id IN (?)", []int64{model.TRANSACTION_TYPE_WITHDRAW, model.TRANSACTION_TYPE_CREDITBACK, model.TRANSACTION_TYPE_CREDITCANCEL}).
		Where("bank_transaction.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, 0, err
	}

	if total > 0 {
		// Snake case ใข้ไม่ได้ครับ ?? ผมลองละ
		selectedFields := "bank_transaction.id AS id, bank_transaction.statement_id AS statement_id, bank_transaction.promotion_id AS promotion_id, bank_transaction.credit_amount AS credit_amount, bank_transaction.credit_back AS credit_back, bank_transaction.deposit_channel AS deposit_channel, bank_transaction.over_amount AS over_amount, bank_transaction.bonus_amount AS bonus_amount"
		selectedFields += ", bank_transaction.from_account_id AS from_account_id, bank_transaction.from_bank_id AS from_bank_id, bank_transaction.from_account_number AS from_account_number,from_bank.name AS from_bank_name"
		selectedFields += ", bank_transaction.is_auto_credit AS is_auto_credit, bank_transaction.transfer_at AS transfer_at"
		selectedFields += ", bank_transaction.slip_img_url"
		selectedFields += ", user.id AS user_id, user.member_code AS user_member_code, user.fullname AS user_fullname"
		selectedFields += ", user.phone AS username" // use phone instead of username
		selectedFields += ", transaction_type.id AS transaction_type_id, transaction_type.label_th AS transaction_type_th, transaction_type.label_en AS transaction_type_en"
		selectedFields += ", bank_account.id AS to_account_id, bank_transaction.to_bank_id AS to_bank_id, to_bank.name AS to_bank_name,user.fullname AS to_account_name, bank_transaction.to_account_number AS to_account_number"
		selectedFields += ", transaction_status.id AS transaction_status_id, transaction_status.label_th AS transaction_status_th, transaction_status.label_en AS transaction_status_en"
		selectedFields += ", bank_transaction.confirmed_at AS confirmed_at, bank_transaction.confirmed_by_admin_id AS confirmed_by_admin_id"
		selectedFields += ", from_bank.icon_url AS from_bank_icon_url, to_bank.icon_url AS to_bank_icon_url"
		selectedFields += ", bank_transaction.cancel_remark AS cancel_remark"
		selectedFields += ", CASE WHEN bank_transaction.created_by_admin_id = 0 THEN 'อัตโนมัติ' ELSE admincreate.username END AS created_by_username"
		selectedFields += ", CASE WHEN bank_transaction.confirmed_by_admin_id = 0 THEN 'อัตโนมัติ' ELSE adminconfirmed.username END AS confirmed_by_username"
		// CreditBackReason if case of creditback from bonus_reason column.
		selectedFields += ", CASE WHEN bank_transaction.transaction_type_id IN (?) THEN bank_transaction.bonus_reason ELSE NULL END AS credit_back_reason"
		// original account
		selectedFields += ", bank_transaction.from_account_name AS from_account_name2, bank_transaction.from_account_number AS from_account_number2"
		selectedFields += ", bank_transaction.from_account_name as payment_type"
		selectedFields += ", bank_transaction.canceled_at AS canceled_at"
		selectedFields += ", bank_transaction.created_at AS created_at"

		query := r.db.Table("bank_transaction")
		query = query.Select(selectedFields, []int64{model.TRANSACTION_TYPE_CREDITBACK, model.TRANSACTION_TYPE_CREDITCANCEL})
		query = query.Joins("LEFT JOIN user ON user.id = bank_transaction.user_id")
		query = query.Joins("LEFT JOIN transaction_type ON transaction_type.id = bank_transaction.transaction_type_id")
		query = query.Joins("LEFT JOIN bank_account ON bank_account.id = bank_transaction.to_account_id")
		query = query.Joins("LEFT JOIN bank AS to_bank ON to_bank.id = bank_transaction.to_bank_id")
		query = query.Joins("LEFT JOIN bank AS from_bank ON from_bank.id = bank_transaction.from_bank_id")
		query = query.Joins("LEFT JOIN transaction_status ON transaction_status.id = bank_transaction.transaction_status_id")
		query = query.Joins("LEFT JOIN admin ON admin.id = bank_transaction.created_by_admin_id")
		query = query.Joins("LEFT JOIN admin AS admincreate ON admincreate.id = bank_transaction.created_by_admin_id")
		query = query.Joins("LEFT JOIN admin AS adminconfirmed ON adminconfirmed.id = bank_transaction.confirmed_by_admin_id")

		if req.FromTransferDate != "" {
			startDateAtBkk, err := r.ParseBodBkk(req.FromTransferDate)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("bank_transaction.transfer_at >= ? ", startDateAtBkk)
		}
		if req.ToTransferDate != "" {
			endDateAtBkk, err := r.ParseEodBkk(req.ToTransferDate)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("bank_transaction.transfer_at <=  ?", endDateAtBkk)
		}
		if req.TransactionTypeId != nil {
			query = query.Where("bank_transaction.transaction_type_id = ?", req.TransactionTypeId)
		}
		if req.Search != "" {
			search_like := fmt.Sprintf("%%%s%%", req.Search)
			query = query.Where(r.db.Where("user.member_code LIKE ?", search_like).Or("user.username LIKE ?", search_like).Or("user.fullname LIKE ?", search_like).Or("bank_transaction.from_account_number LIKE ?", search_like).Or("bank_transaction.to_account_number LIKE ?", search_like))
		}
		if req.ActionByAdminId != nil {
			query = query.Where("bank_transaction.confirmed_by_admin_id = ? OR bank_transaction.created_by_admin_id = ?", req.ActionByAdminId, req.ActionByAdminId)
		}
		if req.ActionByAdminType != "" && req.ActionByAdminType == "ALL" {
			query = query.Where("bank_transaction.confirmed_by_admin_id IS NOT NULL OR bank_transaction.created_by_admin_id IS NOT NULL")
		}

		if req.TransactionStatus != "" {
			// Split the comma-separated string into an array of integers
			statusIds := strings.Split(req.TransactionStatus, ",")
			intStatusIds := make([]int64, len(statusIds))

			for i, idStr := range statusIds {
				id, err := strconv.ParseInt(idStr, 10, 64)
				if err != nil {
					// Handle the error, e.g., return an error response
					return nil, 0, err
				}
				intStatusIds[i] = id
			}
			query = query.Where("bank_transaction.transaction_status_id IN (?)", intStatusIds)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Where("bank_transaction.transaction_type_id IN (?)", []int64{model.TRANSACTION_TYPE_WITHDRAW, model.TRANSACTION_TYPE_CREDITBACK, model.TRANSACTION_TYPE_CREDITCANCEL}).
			Where("bank_transaction.deleted_at IS NULL").
			Order("bank_transaction.transfer_at DESC").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, 0, err
		}
	}
	return list, total, nil
}

func (r repo) GetTransactionWithdrawById(transId int64) (*model.GetBankTransactionWithdrawListResponse, error) {

	var record model.GetBankTransactionWithdrawListResponse

	selectedFields := "bank_transaction.id AS id, bank_transaction.statement_id AS statement_id, bank_transaction.promotion_id AS promotion_id, bank_transaction.credit_amount AS credit_amount, bank_transaction.credit_back AS credit_back, bank_transaction.deposit_channel AS deposit_channel, bank_transaction.over_amount AS over_amount, bank_transaction.bonus_amount AS bonus_amount"
	selectedFields += ", bank_transaction.from_account_id AS from_account_id, bank_transaction.from_bank_id AS from_bank_id, bank_transaction.from_account_number AS from_account_number,from_bank.name AS from_bank_name"
	selectedFields += ", bank_transaction.is_auto_credit AS is_auto_credit, bank_transaction.transfer_at AS transfer_at"
	selectedFields += ", bank_transaction.slip_img_url"
	selectedFields += ", user.id AS user_id, user.member_code AS user_member_code, user.fullname AS user_fullname"
	selectedFields += ", user.phone AS username" // use phone instead of username
	selectedFields += ", transaction_type.id AS transaction_type_id, transaction_type.label_th AS transaction_type_th, transaction_type.label_en AS transaction_type_en"
	selectedFields += ", bank_account.id AS to_account_id, bank_transaction.to_bank_id AS to_bank_id, to_bank.name AS to_bank_name, user.fullname AS to_account_name, bank_transaction.to_account_number AS to_account_number"
	selectedFields += ", transaction_status.id AS transaction_status_id, transaction_status.label_th AS transaction_status_th, transaction_status.label_en AS transaction_status_en"
	selectedFields += ", bank_transaction.confirmed_at AS confirmed_at, bank_transaction.confirmed_by_admin_id AS confirmed_by_admin_id"
	selectedFields += ", from_bank.icon_url AS from_bank_icon_url, to_bank.icon_url AS to_bank_icon_url"
	selectedFields += ", CASE WHEN bank_transaction.created_by_admin_id = 0 THEN 'อัตโนมัติ' ELSE admincreate.username END AS created_by_username"
	selectedFields += ", CASE WHEN bank_transaction.confirmed_by_admin_id = 0 THEN 'อัตโนมัติ' ELSE adminconfirmed.username END AS confirmed_by_username"
	if err := r.db.Table("bank_transaction").
		Select(selectedFields).
		Joins("LEFT JOIN user ON user.id = bank_transaction.user_id").
		Joins("LEFT JOIN transaction_type ON transaction_type.id = bank_transaction.transaction_type_id").
		Joins("LEFT JOIN bank_account ON bank_account.id = bank_transaction.to_account_id").
		Joins("LEFT JOIN bank AS to_bank ON to_bank.id = bank_transaction.to_bank_id").
		Joins("LEFT JOIN bank AS from_bank ON from_bank.id = bank_transaction.from_bank_id").
		Joins("LEFT JOIN transaction_status ON transaction_status.id = bank_transaction.transaction_status_id").
		Joins("LEFT JOIN admin AS admincreate ON admincreate.id = bank_transaction.created_by_admin_id").
		Joins("LEFT JOIN admin AS adminconfirmed ON adminconfirmed.id = bank_transaction.confirmed_by_admin_id").
		Where("bank_transaction.transaction_type_id IN (?)", []int64{model.TRANSACTION_TYPE_WITHDRAW, model.TRANSACTION_TYPE_CREDITBACK, model.TRANSACTION_TYPE_CREDITCANCEL}).
		Where("bank_transaction.id = ?", transId).
		Where("bank_transaction.deleted_at IS NULL").
		Scan(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil

}

func (r repo) UpdateUserTransactionStatus(body model.UpdateUserTransactionStatusRequest) error {
	if err := r.db.Table("bank_transaction").Where("id = ?", body.Id).Updates(&body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetBankTransactionSuccess(req model.GetBankTransactionSuccessListRequest) ([]model.GetBankTransactionSuccessListResponse, int64, error) {

	var list []model.GetBankTransactionSuccessListResponse
	var total int64
	var err error

	selectedFields := "bank_transaction.id AS id, bank_transaction.statement_id AS statement_id"
	selectedFields += ", bank_transaction.promotion_id AS promotion_id "
	selectedFields += ", bank_transaction.credit_back AS credit_back, bank_transaction.credit_amount AS credit_amount, bank_transaction.bonus_amount AS bonus_amount, bank_transaction.after_amount AS after_amount"
	selectedFields += ", bank_transaction.transfer_at AS transfer_at, bank_transaction.created_at AS created_at"
	selectedFields += ", user.id AS user_id, user.member_code AS user_member_code, user.fullname AS user_fullname"
	selectedFields += ", user.phone AS username" // use phone instead of username
	selectedFields += ", transaction_type.id AS transaction_type_id, transaction_type.label_th AS transaction_type_th, transaction_type.label_en AS transaction_type_en"
	selectedFields += ", bank_transaction.is_auto_credit AS is_auto_credit, bank_transaction.auto_process_timer AS auto_process_timer"
	selectedFields += ", CASE WHEN bank_transaction.created_by_admin_id = 0 THEN 'อัตโนมัติ' ELSE admincreate.username END AS created_by_username"
	selectedFields += ", CASE WHEN bank_transaction.confirmed_by_admin_id = 0 THEN 'อัตโนมัติ' ELSE adminconfirmed.username END AS confirmed_by_username"

	count := r.db.Table("bank_transaction")
	count = count.Select("bank_transaction.id")
	count = count.Joins("LEFT JOIN user ON user.id = bank_transaction.user_id")
	count = count.Joins("LEFT JOIN transaction_type ON transaction_type.id = bank_transaction.transaction_type_id")
	count = count.Joins("LEFT JOIN transaction_status ON transaction_status.id = bank_transaction.transaction_status_id")
	count = count.Joins("LEFT JOIN admin AS admincreate ON admincreate.id = bank_transaction.created_by_admin_id")
	count = count.Joins("LEFT JOIN admin AS adminconfirmed ON adminconfirmed.id = bank_transaction.confirmed_by_admin_id")

	if req.TransferDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.TransferDate)
		if err != nil {
			return nil, 0, err
		}
		endDateAtBkk, err := r.ParseEodBkk(req.TransferDate)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("bank_transaction.transfer_at between ? and ?", startDateAtBkk, endDateAtBkk)
	}

	if req.TransactionTypeId != nil {
		count = count.Where("bank_transaction.transaction_type_id = ?", req.TransactionTypeId)
	}

	if req.BankAccountId != nil {
		count = count.Where("bank_transaction.from_account_id = ? OR bank_transaction.to_account_id = ?", req.BankAccountId, req.BankAccountId)
	}

	if req.OrderBy != "" {
		count = count.Order(req.OrderBy + " DESC")
	}

	if err = count.
		Where("bank_transaction.transaction_status_id in (?,?)", model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED, model.TRANS_STATUS_WITHDRAW_SUCCESS).
		Where("bank_transaction.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, 0, err
	}

	if total > 0 {
		query := r.db.Table("bank_transaction")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN user ON user.id = bank_transaction.user_id")
		query = query.Joins("LEFT JOIN transaction_type ON transaction_type.id = bank_transaction.transaction_type_id")
		query = query.Joins("LEFT JOIN transaction_status ON transaction_status.id = bank_transaction.transaction_status_id")

		if req.TransferDate != "" {
			startDateAtBkk, err := r.ParseBodBkk(req.TransferDate)
			if err != nil {
				return nil, 0, err
			}
			endDateAtBkk, err := r.ParseEodBkk(req.TransferDate)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("bank_transaction.transfer_at between ? and ?", startDateAtBkk, endDateAtBkk)
		}

		if req.TransactionTypeId != nil {
			query = query.Where("bank_transaction.transaction_type_id = ?", req.TransactionTypeId)
		}

		if req.BankAccountId != nil {
			query = query.Where("bank_transaction.from_account_id = ? OR bank_transaction.to_account_id = ?", req.BankAccountId, req.BankAccountId)
		}

		if req.OrderBy != "" {
			query = query.Order(req.OrderBy + " DESC")
		} else {
			query = query.Order("bank_transaction.transfer_at DESC")
		}

		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}

		if err = query.
			Where("bank_transaction.transaction_status_id in (?,?)", model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED, model.TRANS_STATUS_WITHDRAW_SUCCESS).
			Where("bank_transaction.deleted_at IS NULL").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, 0, err
		}
	}

	return list, total, nil
}

func (r repo) IsFirstDeposit(userId int64) bool {

	resultFalse := false // Always return false if not sure
	var count int64

	// -- todo Remove this query after clear bank_transaction
	if err := r.db.Table("bank_transaction").Where("user_id = ?", userId).
		Where("transaction_type_id = ?", model.TRANSACTION_TYPE_DEPOSIT).
		Where("is_first_deposit = ?", 1).
		Where("deleted_at IS NULL").
		Count(&count).Error; err != nil {
		return resultFalse
	}
	if count > 0 {
		// has from bank_transaction
		return resultFalse
	}
	// -- todo Remove this query after clear bank_transaction

	// bank_transaction.is_first_deposit => user_first_deposit.user_id
	if err := r.db.Table("user_first_deposit").Where("user_id = ?", userId).Count(&count).Error; err != nil {
		return resultFalse
	}
	if count == 0 {
		// Sure not has from user_first_deposit = true = first deposit
		return true
	}

	return resultFalse
}

func (r repo) SetUserFirstDeposit(body model.UserFirstDepositCreateBody, transId *int64) (*int64, error) {

	if err := r.db.Table("user_first_deposit").Create(&body).Error; err != nil {
		return nil, err
	}

	// -- todo Remove this query after clear bank_transaction
	// UpdateIsFirstDeposit(tranId int64) error
	if err := r.db.Table("bank_transaction").Where("id = ?", transId).Update("is_first_deposit", 1).Error; err != nil {
		return nil, err
	}
	// -- todo Remove this query after clear bank_transaction

	return &body.Id, nil
}

func (r repo) RemoveUserFirstDeposit(userId int64) error {

	if err := r.db.Table("user_first_deposit").Where("user_id = ?", userId).Delete(&model.UserFirstDeposit{}).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) RemovedTransaction(body model.RemovedTransactionBody) error {

	if err := r.db.Table("bank_transaction").Where("id = ?", body.Id).Updates(&body).Error; err != nil {
		return err
	}

	return nil
}

func (r repo) RemovedSuccessTransactionList(req model.RequestRemovedSuccessTransactionList) ([]model.RemovedSuccessTransactionList, int64, error) {
	var list []model.RemovedSuccessTransactionList
	var total int64
	var err error

	selectedFields := "bank_transaction.id AS id, bank_transaction.statement_id AS statement_id, bank_transaction.member_code AS user_member_code"
	selectedFields += ", bank_transaction.from_bank_id AS from_bank_id, from_bank.name AS from_bank_name, bank_transaction.from_account_number AS from_account_number"
	selectedFields += ", bank_transaction.to_bank_id AS to_bank_id, to_bank.name AS to_bank_name, bank_transaction.to_account_number AS to_account_number"
	selectedFields += ", bank_transaction.transfer_at AS transfer_at"
	selectedFields += ", bank_transaction.credit_amount AS credit_amount, bank_transaction.bonus_amount AS bonus_amount"
	selectedFields += ", bank_transaction.created_at AS created_at"
	selectedFields += ", transaction_type.id AS transaction_type_id, transaction_type.label_th AS transaction_type_th, transaction_type.label_en AS transaction_type_en"
	selectedFields += ", from_bank.icon_url AS from_bank_icon_url, to_bank.icon_url AS to_bank_icon_url"

	count := r.db.Table("bank_transaction")
	count = count.Select("bank_transaction.id")
	count = count.Joins("LEFT JOIN transaction_type ON transaction_type.id = bank_transaction.transaction_type_id")
	count = count.Joins("LEFT JOIN bank AS from_bank ON from_bank.id = bank_transaction.from_bank_id")
	count = count.Joins("LEFT JOIN bank AS to_bank ON to_bank.id = bank_transaction.to_bank_id")

	if err = count.
		Where("bank_transaction.deleted_at IS NOT NULL").
		Count(&total).
		Error; err != nil {
		return nil, 0, err
	}

	if total > 0 {
		query := r.db.Table("bank_transaction")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN transaction_type ON transaction_type.id = bank_transaction.transaction_type_id")
		query = query.Joins("LEFT JOIN bank AS from_bank ON from_bank.id = bank_transaction.from_bank_id")
		query = query.Joins("LEFT JOIN bank AS to_bank ON to_bank.id = bank_transaction.to_bank_id")

		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}

		if err = query.
			Where("bank_transaction.deleted_at IS NOT NULL").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, 0, err
		}
	}

	return list, total, nil
}

func (r repo) UpdateAutoProcessTimer(timer string, id int64) error {

	if err := r.db.Table("bank_transaction").Where("id = ?", id).Update("auto_process_timer", timer).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) UpdateDepositTransactionStatusFromAgent(transId int64, body model.UserTransactionCreateResponse) error {

	// tran := tx.Table("bank_transaction").
	// 	Where("id = ?", trxId).
	// 	Where("user_id = ?", userId)

	// if data.Status {

	// 	obj := model.UserUpdateBankTransaction{}
	// 	obj.BeforeAmount = data.OldCredit
	// 	obj.AfterAmount = data.Credit

	// 	if tranType == "deposit" {
	// 		obj.TransactionStatusId = model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED
	// 	} else {
	// 		obj.TransactionStatusId = model.TRANS_STATUS_WITHDRAW_SUCCESS
	// 	}

	// 	tran = tran.Updates(obj)
	// } else {

	// 	var status int64

	// 	if tranType == "deposit" {
	// 		status = model.TRANS_STATUS_DEPOSIT_CREDIT_REJECTED
	// 	} else {
	// 		status = model.TRANS_STATUS_WITHDRAW_REJECTED
	// 	}

	// 	tran = tran.Update("transaction_status_id", status)
	// }

	// if tran.Error != nil {
	// 	tx.Rollback()
	// 	return nil, tran.Error
	// }
	if body.AgentSuccess {
		updateData := map[string]interface{}{
			"transaction_status_id": model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED,
			"before_amount":         body.AgentBeforeAmount,
			"after_amount":          body.AgentAfterAmount,
		}
		if err := r.db.Table("bank_transaction").Where("id = ?", transId).Updates(updateData).Error; err != nil {
			return err
		}
	} else {
		updateData := map[string]interface{}{
			"transaction_status_id": model.TRANS_STATUS_DEPOSIT_CREDIT_REJECTED,
		}
		if err := r.db.Table("bank_transaction").Where("id = ?", transId).Updates(updateData).Error; err != nil {
			return err
		}
	}
	return nil
}

func (r repo) UpdateWithdrawTransactionStatusFromAgent(transId int64, body model.UserTransactionCreateResponse) error {

	if body.AgentSuccess {
		updateData := map[string]interface{}{
			"transaction_status_id": model.TRANS_STATUS_WITHDRAW_APPROVED,
			"before_amount":         body.AgentBeforeAmount,
			"after_amount":          body.AgentAfterAmount,
		}
		if err := r.db.Table("bank_transaction").Where("id = ?", transId).Updates(updateData).Error; err != nil {
			return err
		}
	} else {
		updateData := map[string]interface{}{
			"transaction_status_id": model.TRANS_STATUS_WITHDRAW_REJECTED,
		}
		if err := r.db.Table("bank_transaction").Where("id = ?", transId).Updates(updateData).Error; err != nil {
			return err
		}
	}
	return nil
}

func (r repo) UpdateWithdrawTransactionSuccessPullCreditBack(transId int64, body model.UserTransactionCreateResponse) error {

	if body.AgentSuccess {
		updateData := map[string]interface{}{
			"transaction_status_id": model.TRANS_STATUS_WITHDRAW_SUCCESS,
			"before_amount":         body.AgentBeforeAmount,
			"after_amount":          body.AgentAfterAmount,
		}
		if err := r.db.Table("bank_transaction").Where("id = ?", transId).Updates(updateData).Error; err != nil {
			return err
		}
	} else {
		updateData := map[string]interface{}{
			"transaction_status_id": model.TRANS_STATUS_WITHDRAW_REJECTED,
		}
		if err := r.db.Table("bank_transaction").Where("id = ?", transId).Updates(updateData).Error; err != nil {
			return err
		}
	}
	return nil
}

func (r repo) CheckDuplicateFromWebhook(fromBankAccount string, fromBankId int64) (*model.BankStatement, error) {

	if len(fromBankAccount) > 4 {
		fromBankAccount = fromBankAccount[len(fromBankAccount)-4:]
	}

	var record model.BankStatement
	selectedFields := "statements.id, statements.external_id, statements.account_id, statements.detail, statements.statement_type_id, statements.transfer_at, statements.amount, statements.statement_status_id, statements.created_at, statements.updated_at"
	if err := r.db.Table("bank_statement as statements").
		Select(selectedFields).
		Where("statements.detail LIKE ?", "%"+fromBankAccount+"%").
		Where("statements.from_bank_id = ?", fromBankId).
		Where("statements.external_id IS NOT NULL").
		Where("statements.deleted_at IS NULL").
		Order("statements.id DESC").
		First(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) CheckDuplicateWithdraw(req model.CheckDuplicateWithdrawRequest) (*model.BankTransaction, error) {

	// [********] เพิ่มสถานะ อยู่ในระหว่างการโอนเงิน TRANS_STATUS_WITHDRAW_TRASNFERING

	var record model.BankTransaction

	selectedFields := "transactions.id AS id, transactions.user_id AS user_id, transactions.member_code AS member_code, transactions.credit_amount AS credit_amount, transactions.transfer_at AS transfer_at"

	query := r.db.Table("bank_transaction as transactions")
	query = query.Select(selectedFields)
	query = query.Where("transactions.credit_amount = ?", req.Amount)
	query = query.Where("transactions.user_id = ?", req.UserId)
	query = query.Where("transactions.transaction_status_id IN (?,?,?,?,?)", model.TRANS_STATUS_WITHDRAW_OVER_MAX, model.TRANS_STATUS_WITHDRAW_APPROVED, model.TRANS_STATUS_WITHDRAW_OVER_BUDGET, model.TRANS_STATUS_WITHDRAW_PENDING, model.TRANS_STATUS_WITHDRAW_UNSURE, model.TRANS_STATUS_WITHDRAW_TRASNFERING)

	if err := query.
		Where("transactions.deleted_at IS NULL").
		First(&record).
		Error; err != nil {
		return nil, err
	}

	return &record, nil
}

func (r repo) CheckUserDuplicateWithdrawProcessing(userId int64) (*model.CheckUserDuplicateWithdrawProcessingResponse, error) {

	// [********] แก้ไขเพิ่มเต็ม ย้านจาก CheckDuplicateWithdraw มาเปลี่ยน flow เป็น CheckUserDuplicateWithdrawProeccing
	// [********] เพิ่มสถานะ อยู่ในระหว่างการโอนเงิน TRANS_STATUS_WITHDRAW_TRASNFERING

	var record model.CheckUserDuplicateWithdrawProcessingResponse

	selectedFields := "transactions.id AS id, transactions.credit_amount AS credit_amount, transactions.created_at AS created_at"

	query := r.db.Table("bank_transaction as transactions")
	query = query.Select(selectedFields)
	query = query.Where("transactions.user_id = ?", userId)
	// [*********] add checker
	query = query.Where("transactions.transaction_status_id IN (?,?,?,?,?,?)", model.TRANS_STATUS_WITHDRAW_OVER_MAX, model.TRANS_STATUS_WITHDRAW_APPROVED, model.TRANS_STATUS_WITHDRAW_OVER_BUDGET, model.TRANS_STATUS_WITHDRAW_PENDING, model.TRANS_STATUS_WITHDRAW_UNSURE, model.TRANS_STATUS_WITHDRAW_TRASNFERING)
	//OR use but not reflexible query = query.Where("transactions.transaction_status_id NOT IN (?,?,?)", model.TRANS_STATUS_WITHDRAW_SUCCESS, model.TRANS_STATUS_WITHDRAW_CANCELED, model.TRANS_STATUS_WITHDRAW_REJECTED)
	query = query.Where("transactions.transaction_type_id = ?", model.TRANSACTION_TYPE_WITHDRAW)
	if err := query.
		Where("transactions.deleted_at IS NULL").
		Take(&record).
		Error; err != nil {
		return nil, err
	}

	return &record, nil
}

func (r repo) UpdateConfirmedByAdminId(id int64, body model.UpdateConfirmedByAdminIdRequest) error {

	if err := r.db.Table("user_transaction").Where("ref_id = ?", id).Updates(&body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetUserWithdrawCreditTransactionByRefId(transactionId int64, creditAmount float64) (*model.UserTransaction, error) {

	var record model.UserTransaction

	selectedFields := "logs.id, logs.user_id, logs.direction_id, logs.type_id, logs.account_id, logs.ref_id, logs.detail"
	selectedFields += ", logs.promotion_id, logs.credit_before, logs.credit_back, logs.credit_amount, logs.bonus_amount, logs.credit_after"
	selectedFields += ", logs.transfer_at, logs.create_admin_id, logs.confirm_admin_id, logs.is_adjust_auto, logs.work_seconds"
	selectedFields += ", logs.created_at, logs.is_show, logs.removed_at, logs.remove_admin_id"

	query := r.db.Table("user_transaction as logs")
	query = query.Select(selectedFields)
	if err := query.
		Where("logs.ref_id = ?", transactionId).
		Where("logs.removed_at IS NULL").
		Where("logs.is_show = ?", false).
		Where("logs.credit_amount = ?", creditAmount).
		Where("logs.type_id = ?", model.TRANSACTION_TYPE_WITHDRAW).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) CreateWithdrawConfirm(body model.CreateWithdrawConfirmBody) (int64, error) {
	if err := r.db.Table("bank_transaction_withdraw_confirm").Create(&body).Error; err != nil {
		return 0, err
	}
	return body.Id, nil
}

func (r repo) CheckWithdrawConfirmCurrentTime(req model.CheckWithdrawConfirmBody) (*model.WithdrawConfirm, error) {

	var record model.WithdrawConfirm
	startCreatedAt := req.CreatedAt.Add(-1 * time.Minute)
	endCreatedAt := req.CreatedAt.Add(1 * time.Minute)

	selectedFields := "confirm.id AS id, confirm.confirm_key AS confirm_key, confirm.user_id AS user_id, confirm.bank_transaction_id AS bank_transaction_id, confirm.created_at AS created_at"
	query := r.db.Table("bank_transaction_withdraw_confirm as confirm")
	query = query.Select(selectedFields)
	query = query.Where("confirm.created_at BETWEEN ? AND ?", startCreatedAt, endCreatedAt)
	query = query.Where("confirm.user_id = ?", req.UserId)

	if err := query.
		Where("confirm.deleted_at IS NULL").
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil

}

func (r repo) CheckWithdrawConfirmConfirmKey(confirmKey string) (*model.WithdrawConfirm, error) {

	var record model.WithdrawConfirm

	selectedFields := "confirm.id AS id, confirm.confirm_key AS confirm_key, confirm.user_id AS user_id, confirm.bank_transaction_id AS bank_transaction_id, confirm.created_at AS created_at"
	query := r.db.Table("bank_transaction_withdraw_confirm as confirm")
	query = query.Select(selectedFields)
	query = query.Where("confirm.confirm_key = ?", confirmKey)

	if err := query.
		Where("confirm.deleted_at IS NULL").
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil

}

func (r repo) UpdateWithdrawConfirmTransactionId(confirmId int64, transId int64) error {
	updateData := map[string]interface{}{
		"bank_transaction_id": transId,
	}
	if err := r.db.Table("bank_transaction_withdraw_confirm").Where("id = ?", confirmId).Updates(updateData).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetBankTransactionFirstTimeDeposit(req model.GetBankTransactionFirstTimeDepositRequest) ([]model.GetBankTransactionFirstTimeDepositResponse, int64, error) {

	var list []model.GetBankTransactionFirstTimeDepositResponse
	var total int64

	// [set time type]
	now := time.Now()
	if req.FromTransferDate == "" && req.ToTransferDate == "" {
		if req.DateType == "daily" {
			req.FromTransferDate = now.Format("2006-01-02")
			req.ToTransferDate = now.Format("2006-01-02")
		} else if req.DateType == "yesterday" {
			req.FromTransferDate = now.AddDate(0, 0, -1).Format("2006-01-02")
			req.ToTransferDate = now.AddDate(0, 0, -1).Format("2006-01-02")
		} else if req.DateType == "last_week" {
			req.FromTransferDate = now.AddDate(0, 0, -7).Format("2006-01-02")
			req.ToTransferDate = now.Format("2006-01-02")
		} else if req.DateType == "last_month" {
			req.FromTransferDate = now.AddDate(0, 0, -30).Format("2006-01-02")
			req.ToTransferDate = now.Format("2006-01-02")
		}
	}

	// [count]
	count := r.db.Table("bank_transaction as bank_transaction")
	count = count.Select("bank_transaction.id")
	if req.MemberCode != "" {
		count = count.Where("bank_transaction.member_code = ?", req.MemberCode)
	}

	if req.FromTransferDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.FromTransferDate)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("bank_transaction.transfer_at >= ?", startDateAtBkk)
	}

	if req.ToTransferDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.ToTransferDate)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("bank_transaction.transfer_at <= ?", endDateAtBkk)
	}

	if req.Search != "" {
		search_like := fmt.Sprintf("%%%s%%", req.Search)
		count = count.Where(r.db.Where("bank_transaction.from_account_name LIKE ?", search_like).Or("bank_transaction.from_account_number LIKE ?", search_like).Or("bank_transaction.to_account_name LIKE ?", search_like).Or("bank_transaction.to_account_number LIKE ?", search_like))
	}
	if err := count.
		Where("bank_transaction.is_first_deposit = 1").
		Where("bank_transaction.transaction_status_id = ?", model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED).
		Where("bank_transaction.transaction_type_id = ?", model.TRANSACTION_TYPE_DEPOSIT).
		Where("bank_transaction.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, 0, err
	}

	if total > 0 {
		// SELECT //
		// [********] เอาแค่ที่จะใช้ ออกแบบไปก่อน design มา
		// selectedFields := "bank_transaction.id AS id, bank_transaction.statement_id AS statement_id, bank_transaction.promotion_id AS promotion_id, bank_transaction.credit_amount AS credit_amount, bank_transaction.credit_back AS credit_back, bank_transaction.deposit_channel AS deposit_channel, bank_transaction.over_amount AS over_amount, bank_transaction.bonus_amount AS bonus_amount"
		// selectedFields += ", bank_transaction.from_account_id AS from_account_id, bank_transaction.from_bank_id AS from_bank_id, bank_transaction.from_account_number AS from_account_number,from_bank.name AS from_bank_name"
		// selectedFields += ", bank_transaction.is_auto_credit AS is_auto_credit, bank_transaction.transfer_at AS transfer_at"
		// selectedFields += ", user.id AS user_id, user.member_code AS user_member_code, user.fullname AS user_fullname"
		// selectedFields += ", user.phone AS username" // use phone instead of username
		// selectedFields += ", transaction_type.id AS transaction_type_id, transaction_type.label_th AS transaction_type_th, transaction_type.label_en AS transaction_type_en"
		// selectedFields += ", bank_account.id AS to_account_id, bank_account.bank_id AS to_bank_id, to_bank.name AS to_bank_name, bank_account.account_name AS to_account_name, bank_account.account_number AS to_account_number"
		// selectedFields += ", transaction_status.id AS transaction_status_id, transaction_status.label_th AS transaction_status_th, transaction_status.label_en AS transaction_status_en"
		// selectedFields += ", bank_transaction.confirmed_at AS confirmed_at, bank_transaction.confirmed_by_admin_id AS confirmed_by_admin_id"
		// selectedFields += ", from_bank.icon_url AS from_bank_icon_url, to_bank.icon_url AS to_bank_icon_url"
		// selectedFields += ",bank_transaction.cancel_remark AS cancel_remark"
		// selectedFields += ", bank_statement.detail AS detail"
		// selectedFields += ", CASE WHEN bank_transaction.created_by_admin_id = 0 THEN 'อัตโนมัติ' ELSE admincreate.username END AS created_by_username"
		// selectedFields += ", CASE WHEN bank_transaction.confirmed_by_admin_id = 0 THEN 'อัตโนมัติ' ELSE adminconfirmed.username END AS confirmed_by_username"

		selectedFields := "bank_transaction.id AS id, bank_transaction.user_id AS user_id, user.member_code AS user_member_code, bank_transaction.credit_amount AS credit_amount, bank_transaction.transfer_at AS transfer_at"
		selectedFields += ", user.created_at AS register_at"

		query := r.db.Table("bank_transaction")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN user ON user.id = bank_transaction.user_id")
		// query = query.Joins("LEFT JOIN transaction_type ON transaction_type.id = bank_transaction.transaction_type_id")
		// query = query.Joins("LEFT JOIN bank_statement ON bank_statement.id = bank_transaction.statement_id")
		// query = query.Joins("LEFT JOIN bank_account ON bank_account.id = bank_transaction.to_account_id")
		// query = query.Joins("LEFT JOIN bank AS to_bank ON to_bank.id = bank_transaction.to_bank_id")
		// query = query.Joins("LEFT JOIN bank AS from_bank ON from_bank.id = bank_transaction.from_bank_id")
		// query = query.Joins("LEFT JOIN transaction_status ON transaction_status.id = bank_transaction.transaction_status_id")
		// query = query.Joins("LEFT JOIN admin AS admincreate ON admincreate.id = bank_transaction.created_by_admin_id")
		// query = query.Joins("LEFT JOIN admin AS adminconfirmed ON adminconfirmed.id = bank_transaction.confirmed_by_admin_id")

		if req.MemberCode != "" {
			query = query.Where("bank_transaction.member_code = ?", req.MemberCode)
		}

		if req.FromTransferDate != "" {
			startDateAtBkk, err := r.ParseBodBkk(req.FromTransferDate)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("bank_transaction.transfer_at >= ?", startDateAtBkk)
		}

		if req.ToTransferDate != "" {
			endDateAtBkk, err := r.ParseEodBkk(req.ToTransferDate)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("bank_transaction.transfer_at <= ?", endDateAtBkk)
		}

		if req.Search != "" {
			search_like := fmt.Sprintf("%%%s%%", req.Search)
			query = query.Where(r.db.Where("bank_transaction.from_account_name LIKE ?", search_like).Or("bank_transaction.from_account_number LIKE ?", search_like).Or("bank_transaction.to_account_name LIKE ?", search_like).Or("bank_transaction.to_account_number LIKE ?", search_like))
		}

		// Sort by ANY //
		req.SortCol = strings.TrimSpace(req.SortCol)
		if req.SortCol != "" {
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				req.SortAsc = "DESC"
			} else {
				req.SortAsc = "ASC"
			}
			query = query.Order(req.SortCol + " " + req.SortAsc)
		}

		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err := query.
			Where("bank_transaction.is_first_deposit = 1").
			Where("bank_transaction.transaction_status_id = ?", model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED).
			Where("bank_transaction.transaction_type_id = ?", model.TRANSACTION_TYPE_DEPOSIT).
			Where("bank_transaction.deleted_at IS NULL").
			Order("bank_transaction.transfer_at DESC").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, 0, err
		}
	}

	// End count total records for pagination purposes (without limit and offset)
	return list, total, nil
}

func (r repo) GetBankTransactionWithdrawToCheckUserConfig(req model.TransactionWithdrawToCheckUserConfigRequest) (*model.TransactionWithdrawToCheckUserConfigResponse, error) {

	var record model.TransactionWithdrawToCheckUserConfigResponse
	selectedFields := "SUM(bank_transaction.credit_amount) AS credit_amount_sum"
	query := r.db.Table("bank_transaction")
	query = query.Select(selectedFields)
	query = query.Where("bank_transaction.user_id = ?", req.UserId)
	query = query.Where("bank_transaction.transaction_type_id = ?", model.TRANSACTION_TYPE_WITHDRAW)
	query = query.Where("bank_transaction.transaction_status_id = ?", model.TRANS_STATUS_WITHDRAW_SUCCESS)

	if req.FromTransferDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.FromTransferDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("bank_transaction.transfer_at >= ?", startDateAtBkk)
	}

	if req.ToTransferDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.ToTransferDate)
		if err != nil {
			return nil, err
		}
		query = query.Where("bank_transaction.transfer_at <= ?", endDateAtBkk)
	}

	if err := query.
		Where("bank_transaction.deleted_at IS NULL").
		Scan(&record).
		Error; err != nil {
		return nil, err
	}

	return &record, nil
}
func (r repo) TransactionWithdrawToCheckUserConfigList(req model.TransactionWithdrawToCheckUserConfigRequest) ([]model.TransactionWithdrawToCheckUserConfigListResponse, int64, error) {

	var list []model.TransactionWithdrawToCheckUserConfigListResponse
	var total int64
	var err error

	count := r.db.Table("bank_transaction")
	count = count.Select("bank_transaction.id")
	count = count.Where("bank_transaction.user_id = ?", req.UserId)
	count = count.Where("bank_transaction.transaction_type_id = ?", model.TRANSACTION_TYPE_WITHDRAW)
	count = count.Where("bank_transaction.transaction_status_id = ?", model.TRANS_STATUS_WITHDRAW_SUCCESS)
	if req.FromTransferDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.FromTransferDate)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("bank_transaction.transfer_at >= ?", startDateAtBkk)
	}

	if req.ToTransferDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.ToTransferDate)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("bank_transaction.transfer_at <= ?", endDateAtBkk)
	}

	if err = count.
		Where("bank_transaction.deleted_at IS NULL").
		Count(&total).
		Error; err != nil {
		return nil, 0, err
	}

	if total > 0 {

		selectedFields := "bank_transaction.id AS id, bank_transaction.credit_amount AS credit_amount"
		query := r.db.Table("bank_transaction")
		query = query.Select(selectedFields)
		query = query.Where("bank_transaction.user_id = ?", req.UserId)
		query = query.Where("bank_transaction.transaction_type_id = ?", model.TRANSACTION_TYPE_WITHDRAW)
		query = query.Where("bank_transaction.transaction_status_id = ?", model.TRANS_STATUS_WITHDRAW_SUCCESS)

		if req.FromTransferDate != "" {
			startDateAtBkk, err := r.ParseBodBkk(req.FromTransferDate)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("bank_transaction.transfer_at >= ?", startDateAtBkk)
		}

		if req.ToTransferDate != "" {
			endDateAtBkk, err := r.ParseEodBkk(req.ToTransferDate)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("bank_transaction.transfer_at <= ?", endDateAtBkk)
		}

		if err := query.
			Where("bank_transaction.deleted_at IS NULL").
			Scan(&list).
			Error; err != nil {
			return nil, 0, err
		}

	}

	return list, total, nil
}

func (r repo) GetUserTransactionByRefId(refId int64) (*model.UserTransaction, error) {

	var record model.UserTransaction
	selectedFields := "logs.id"

	query := r.db.Table("user_transaction as logs")
	query = query.Select(selectedFields)
	if err := query.
		Where("logs.ref_id = ?", refId).
		Where("logs.removed_at IS NULL").
		First(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) UpdateConfirmTransactionRetry(id int64, body model.UpdateConfirmTransactionRetry) error {

	if err := r.db.Table("bank_transaction").Where("id = ?", id).Updates(&body).Error; err != nil {
		return err
	}

	return nil
}

func (r repo) CheckAdminDuplicateWithdrawList(req model.CheckAdminDuplicateWithdrawListRequest) ([]model.CheckAdminDuplicateWithdrawList, error) {

	timeNow := time.Now().UTC()
	var list []model.CheckAdminDuplicateWithdrawList
	startTransferAt := timeNow.Add(-2 * time.Minute)
	endTransferAt := timeNow.Add(2 * time.Minute)

	selectedFields := "transactions.id AS id, transactions.user_id AS user_id ,transactions.member_code AS member_code"
	selectedFields += ", transactions.credit_amount AS credit_amount, transactions.transfer_at AS transfer_at"
	query := r.db.Table("bank_transaction as transactions")
	query = query.Select(selectedFields)
	query = query.Where("transactions.user_id = ?", req.UserId)
	query = query.Where("transactions.credit_amount = ?", req.CreditAmount)
	query = query.Where("transactions.transfer_at BETWEEN ? AND ?", startTransferAt, endTransferAt)
	query = query.Where("transactions.transaction_type_id = ?", model.TRANSACTION_TYPE_WITHDRAW)

	if err := query.
		Where("transactions.deleted_at IS NULL").
		Scan(&list).
		Error; err != nil {
		return nil, err
	}

	return list, nil
}

func (r repo) CreateBankTransactionExternalDetail(body model.BankTransactionExternalDetailCreateRequest) (int64, error) {
	if err := r.db.Table("bank_transaction_external_detail").Create(&body).Error; err != nil {
		return 0, err
	}
	return body.Id, nil
}

func (r repo) GetBankTransactionExternalDetailByBankTransactionId(id int64) ([]model.BankTransactionExternalDetailGetByBankTransactionIdResponse, error) {

	var list []model.BankTransactionExternalDetailGetByBankTransactionIdResponse
	selectedFields := "id, bank_transaction_id, detail, error_code, created_at"
	query := r.db.Table("bank_transaction_external_detail")
	query = query.Select(selectedFields)
	query = query.Where("bank_transaction_id = ?", id)
	if err := query.
		Scan(&list).
		Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (r repo) CreateBankTransactionSlip(body model.BankTransactionSlipCreateRequest) (int64, error) {
	if err := r.db.Table("bank_transaction_slip").Create(&body).Error; err != nil {
		return 0, err
	}
	return body.Id, nil
}

func (r repo) UpdateBankTransactionSlip(body model.BankTransactionSlipUpdateRequest) error {
	if err := r.db.Table("bank_transaction_slip").Where("id = ?", body.Id).Updates(&body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) CheckScammerSlipRequest(req model.CheckScammerSlipRequest) (*model.CheckScammerSlipResponse, error) {

	todayDateCheckScammer := time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02")

	startBodBkk, err := r.ParseBodBkk(todayDateCheckScammer)
	if err != nil {
		return nil, err
	}

	endEodBkk, err := r.ParseEodBkk(todayDateCheckScammer)
	if err != nil {
		return nil, err
	}

	var record model.CheckScammerSlipResponse
	// CountTodaySent int64 `json:"countTodaySent"`
	selectedFields := "COUNT(bank_transaction_slip.id) AS count_today_sent"
	query := r.db.Table("bank_transaction_slip")
	query = query.Select(selectedFields)
	query = query.Where("bank_transaction_slip.created_at BETWEEN ? AND ?", startBodBkk, endEodBkk)
	query = query.Where("bank_transaction_slip.user_id = ?", req.UserId)
	query = query.Where("bank_transaction_slip.status = ?", 2)

	if err := query.
		Scan(&record).
		Error; err != nil {
		return nil, err
	}

	return &record, nil

}

func (r repo) GetLastestBankAdminManualDepositTransaction(userId int64) (*model.BankTransaction, error) {

	var record model.BankTransaction

	selectedFields := "*"
	if err := r.db.Table("bank_transaction").
		Select(selectedFields).
		Where("bank_transaction.transaction_type_id = ?", model.TRANSACTION_TYPE_DEPOSIT).
		Where("bank_transaction.transaction_status_id = ?", model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED).
		Where("bank_transaction.created_by_admin_id != ?", 0).Where("bank_transaction.confirmed_by_admin_id != ?", 0).
		Where("bank_transaction.user_id = ?", userId).
		Order("bank_transaction.id DESC").
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetAccountInfoFastbank(body model.AccountInfoFastbankRequest) (*model.AccountInfoFastbankResponse, error) {
	client := &http.Client{}

	// Marshal the request body to JSON
	data, err := json.Marshal(body)
	if err != nil {
		log.Println("Failed to marshal request body:", err)
		return nil, fmt.Errorf("INTERNAL_ERROR")
	}

	// Create a new HTTP request
	reqHttp, err := http.NewRequest("POST", os.Getenv("ACCOUNTING_API_ENDPOINT")+"/api/v2/statement/accountInfo", bytes.NewBuffer(data))
	if err != nil {
		log.Println("Failed to create HTTP request:", err)
		return nil, fmt.Errorf("INTERNAL_ERROR")
	}
	reqHttp.Header.Set("apiKey", os.Getenv("ACCOUNTING_API_KEY"))
	reqHttp.Header.Set("Content-Type", "application/json")

	// Send the HTTP request
	response, err := client.Do(reqHttp)
	if err != nil {
		log.Println("HTTP request failed:", err)
		return nil, fmt.Errorf("EXTERNAL_API_ERROR")
	}
	defer response.Body.Close()

	// Read the response body
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println("Failed to read response body:", err)
		return nil, fmt.Errorf("EXTERNAL_API_ERROR")
	}

	// Check for non-200 status codes
	if response.StatusCode != http.StatusOK {
		var apiError model.AccountInfoFastbankDataError
		// มี error อื่นด้วย todo
		fmt.Println("responseData", string(responseData)) // For debugging purposes

		// Attempt to decode the error response into the first error struct
		if err := json.NewDecoder(bytes.NewReader(responseData)).Decode(&apiError); err != nil {
			log.Println("Failed to unmarshal into AccountInfoFastbankDataError:", err)
			return nil, fmt.Errorf("API_ERROR: %s", string(responseData))
		}

		// Return the error message from the first error struct
		return nil, fmt.Errorf("API_ERROR: %s", string(responseData))
	}

	// Unmarshal the response data into the response model
	var res model.AccountInfoFastbankResponse
	err = json.Unmarshal(responseData, &res)
	if err != nil {
		log.Println("Failed to unmarshal response body:", err)
		return nil, fmt.Errorf("EXTERNAL_API_ERROR")
	}

	return &res, nil
}

func (r repo) GetSmsModeDepositList(req model.GetSmsModeDepositListRequest) ([]model.GetSmsModeDepositListResponse, int64, error) {

	var list []model.GetSmsModeDepositListResponse
	var total int64
	var err error

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})

	if err != nil {
		return nil, 0, err
	}

	count := r.db.Table("paygate_smsmode_deposit")
	count = count.Select("paygate_smsmode_deposit.id")
	count = count.Joins("LEFT JOIN user AS tb_user ON tb_user.id = paygate_smsmode_deposit.user_id")
	if req.MemberCode != "" {
		count = count.Where("tb_user.member_code = ?", req.MemberCode)
	}

	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("paygate_smsmode_deposit.created_at >= ?", startDateAtBkk)
	}

	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("paygate_smsmode_deposit.created_at <= ?", endDateAtBkk)
	}

	lowerStatus := strings.ToLower(req.Status)
	if req.Status != "" || req.Status != "all" {
		if lowerStatus == "paid" {
			count = count.Where("paygate_smsmode_deposit.transaction_status = ?", "PAID")
		} else if lowerStatus == "pending" {
			count = count.Where("paygate_smsmode_deposit.transaction_status = ?", "WAITING_CONFIRM")
		} else if lowerStatus == "failed" {
			count = count.Where("paygate_smsmode_deposit.transaction_status = ? OR paygate_smsmode_deposit.transaction_status = ?", "ERROR", "CANCELED")
		}
	}

	if req.Search != "" {
		searchLike := fmt.Sprintf("%%%s%%", req.Search)
		count = count.Where(r.db.Where("paygate_smsmode_deposit.account_from LIKE ? OR paygate_smsmode_deposit.bank_account_no LIKE ? OR tb_user.member_code LIKE ? OR tb_user.username LIKE ? OR tb_user.fullname LIKE ?", searchLike, searchLike, searchLike, searchLike, searchLike))
	}

	if err = count.
		Count(&total).
		Error; err != nil {
		return nil, 0, err
	}

	if total > 0 {

		selectedFields := "paygate_smsmode_deposit.id AS id, paygate_smsmode_deposit.user_id AS user_id, tb_user.member_code AS member_code"
		selectedFields += ", paygate_smsmode_deposit.from_bank_id AS from_bank_id, paygate_smsmode_deposit.ref_id AS ref_id, paygate_smsmode_deposit.account_from AS account_from"
		selectedFields += ", paygate_smsmode_deposit.bank_account_id AS bank_account_id, paygate_smsmode_deposit.bank_account_no AS bank_account_no, paygate_smsmode_deposit.bank_code AS bank_code"
		selectedFields += ", paygate_smsmode_deposit.order_no AS order_no, paygate_smsmode_deposit.amount AS amount, paygate_smsmode_deposit.transfer_amount AS transfer_amount"
		selectedFields += ", paygate_smsmode_deposit.transaction_no AS transaction_no, paygate_smsmode_deposit.transaction_date AS transaction_date"
		selectedFields += ", paygate_smsmode_deposit.transaction_status AS transaction_status, paygate_smsmode_deposit.payment_at AS payment_at, paygate_smsmode_deposit.remark AS remark"
		selectedFields += ", paygate_smsmode_deposit.created_at AS created_at, paygate_smsmode_deposit.updated_at AS updated_at"
		selectedFields += ", paygate_smsmode_deposit.detail AS detail"
		selectedFields += ", tb_user.username AS username, tb_user.fullname AS full_name"
		selectedFields += ", paygate_smsmode_deposit.confirmed_by_admin_name AS confirmed_by_admin_name"
		query := r.db.Table("paygate_smsmode_deposit")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN user AS tb_user ON tb_user.id = paygate_smsmode_deposit.user_id")

		if req.MemberCode != "" {
			query = query.Where("tb_user.member_code = ?", req.MemberCode)
		}

		if dateType.DateFrom != "" {
			startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("paygate_smsmode_deposit.created_at >= ?", startDateAtBkk)
		}

		if dateType.DateTo != "" {
			endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("paygate_smsmode_deposit.created_at <= ?", endDateAtBkk)
		}

		lowerStatus := strings.ToLower(req.Status)
		if req.Status != "" || req.Status != "all" {
			if lowerStatus == "paid" {
				query = query.Where("paygate_smsmode_deposit.transaction_status = ?", "PAID")
			} else if lowerStatus == "pending" {
				query = query.Where("paygate_smsmode_deposit.transaction_status = ?", "WAITING_CONFIRM")
			} else if lowerStatus == "failed" {
				query = query.Where("paygate_smsmode_deposit.transaction_status = ? OR paygate_smsmode_deposit.transaction_status = ?", "ERROR", "CANCELED")
			}
		}

		if req.Search != "" {
			searchLike := fmt.Sprintf("%%%s%%", req.Search)
			query = query.Where(r.db.Where("paygate_smsmode_deposit.account_from LIKE ? OR paygate_smsmode_deposit.bank_account_no LIKE ? OR tb_user.member_code LIKE ? OR tb_user.username LIKE ? OR tb_user.fullname LIKE ?", searchLike, searchLike, searchLike, searchLike, searchLike))
		}

		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}

		if err = query.
			Order("paygate_smsmode_deposit.created_at DESC").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, 0, err
		}

	}

	return list, total, nil
}

func (r repo) GetSmsModeDepositById(id int64) (*model.GetSmsModeDepositByIdReponse, error) {

	var record model.GetSmsModeDepositByIdReponse
	selectedFields := "paygate_smsmode_deposit.id AS id, paygate_smsmode_deposit.user_id AS user_id, tb_user.member_code AS member_code"
	selectedFields += ", paygate_smsmode_deposit.from_bank_id AS from_bank_id, paygate_smsmode_deposit.ref_id AS ref_id, paygate_smsmode_deposit.account_from AS account_from"
	selectedFields += ", paygate_smsmode_deposit.bank_account_id AS bank_account_id, paygate_smsmode_deposit.bank_account_no AS bank_account_no, paygate_smsmode_deposit.bank_code AS bank_code"
	selectedFields += ", paygate_smsmode_deposit.order_no AS order_no, paygate_smsmode_deposit.amount AS amount, paygate_smsmode_deposit.transfer_amount AS transfer_amount"
	selectedFields += ", paygate_smsmode_deposit.transaction_no AS transaction_no, paygate_smsmode_deposit.transaction_date AS transaction_date"
	selectedFields += ", paygate_smsmode_deposit.transaction_status AS transaction_status, paygate_smsmode_deposit.payment_at AS payment_at, paygate_smsmode_deposit.remark AS remark"
	selectedFields += ", paygate_smsmode_deposit.created_at AS created_at, paygate_smsmode_deposit.updated_at AS updated_at"
	selectedFields += ", paygate_smsmode_deposit.detail AS detail"
	selectedFields += ", tb_user.username AS username, tb_user.fullname AS full_name"

	query := r.db.Table("paygate_smsmode_deposit")
	query = query.Joins("LEFT JOIN user AS tb_user ON tb_user.id = paygate_smsmode_deposit.user_id")
	query = query.Select(selectedFields)
	query = query.Where("paygate_smsmode_deposit.id = ?", id)
	if err := query.
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) BotCreateWithdrawPullCreditBack(req model.BotCreateWithdrawPullCreditBackRequest) ([]model.BotCreateWithdrawPullCreditBackGetUser, error) {

	var user []model.BotCreateWithdrawPullCreditBackGetUser
	selectedFields := "user.id AS user_id, user.member_code AS member_code"
	query := r.db.Table("user")
	query = query.Select(selectedFields)
	query = query.Joins("JOIN promotion_web_user ON promotion_web_user.user_id = user.id")
	query = query.Joins("JOIN promotion_web ON promotion_web.id = promotion_web_user.promotion_web_id")
	query = query.Where("promotion_web.promotion_web_type_id = ?", model.PROMOTION_WEB_TYPE_NEW_MEMBER_FREE)
	query = query.Where("member_code IS NOT NULL")

	if req.DateFrom != nil {
		query = query.Where("user.created_at >= ?", req.DateFrom)
	} else {
		return nil, errors.New("DateFrom is required")
	}

	if req.DateTo != nil {
		query = query.Where("user.created_at <= ?", req.DateTo)
	} else {
		return nil, errors.New("DateTo is required")
	}

	if req.Limit > 0 {
		query = query.Limit(req.Limit)
	}

	if err := query.
		Where("user.deleted_at IS NULL").
		Offset(req.Page * req.Limit).
		Scan(&user).
		Error; err != nil {
		return nil, err
	}

	return user, nil
}

func (r repo) UpdateLockCreditWithdrawAutoUpdated(req model.UpdateLockCreditWithdrawAutoUpdated) error {

	query := r.db.Table("user_withdraw_lock_credit")
	query = query.Where("id = ?", req.Id)
	query = query.Updates(req)
	if err := query.Error; err != nil {
		return err
	}

	return nil
}
