package repository

import (
	"cybergame-api/model"
	"time"

	"gorm.io/gorm"
)

func NewActivityDailyV2Repository(db *gorm.DB) ActivityDailyV2Repository {
	return &repo{db}
}

type ActivityDailyV2Repository interface {
	// option
	GetActivityDailyV2TotalConditionOptions() ([]model.SelectOptions, error)

	// bof
	GetChangeCountTime() (*model.GetActivityDailyV2TotalChangeCountTime, error)
	CreateActivityDailyV2Total(bodyTotal model.CreateActivityDailyV2TotalBody, bodyItem []model.CreateActivityDailyV2Body) error
	GetActivityDailyV2() (*model.GetActivityDailyV2TotalResponse, error)

	// web
	WebGetActivityDailyV2() ([]model.WebActivityDailyV2Detail, *model.GetActivityDailyV2Total, error)
	CheckUserActivityDailyV2AlreadyRevice(userId int64) (bool, error)
	GetUserActivityDailyV2NextNoNumber(user int64) (*model.ActivityDailyV2UserNextNo, *model.GetActivityDailyV2Total, error)
	CreateActivityDailyV2User(body model.CreateActivityDailyV2User) (int64, error)
	IncreaseUserCredit(body model.UserTransactionCreateRequest) (*model.UserTransactionCreateResponse, error)

	CreateTurnoverUserStatement(body model.TurnoverUserStatementCreateBody) (*int64, error)
	UpdateTurnoverUserStatement(id int64, body model.TurnoverUserStatementUpdateBody) error
	CreateTurnoverUserWithdrawLog(body model.CreateTurnoverUserWithdrawLog) (*int64, error)
	GetTurnoverUserActivityDaily(req model.GetTurnoverUserActivityDailyRequest) ([]model.GetTurnoverUserActivityDailyResponse, int64, error)

	GetRaceActionIdByActionKey(actionKey string) (*int64, error)
	CreateRaceCondition(body model.RaceActionCreateBody) (int64, error)

	GetTurnoverUserActivityDailyV2(req model.GetTurnoverUserActivityDailyV2Request) ([]model.GetTurnoverUserActivityDailyV2Response, int64, error)

	GetUserById(id int64) (*model.UserResponse, error)

	GetUserTodayDepositForActivityDaily(userId int64) ([]model.GetUserTodayDepositForActivityDaily, error)
}

func (r repo) CreateActivityDailyV2Total(bodyTotal model.CreateActivityDailyV2TotalBody, bodyItem []model.CreateActivityDailyV2Body) error {

	query := r.db.Table("activity_daily_v2_total")
	query = query.Where("id = ?", 1)
	query = query.Updates(&bodyTotal)
	if err := query.Error; err != nil {
		return err
	}

	query2 := r.db.Table("activity_daily_v2")
	query2 = query2.Create(&bodyItem)
	if err := query2.Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetChangeCountTime() (*model.GetActivityDailyV2TotalChangeCountTime, error) {

	var result model.GetActivityDailyV2TotalChangeCountTime
	selectedFields := "change_count_time"

	query := r.db.Table("activity_daily_v2_total")
	query = query.Select(selectedFields)
	query = query.Where("id = ?", 1)
	query = query.Take(&result)
	if err := query.Error; err != nil {
		return nil, err
	}

	return &result, nil
}

func (r repo) GetActivityDailyV2() (*model.GetActivityDailyV2TotalResponse, error) {

	type GetActivityDailyV2Total struct {
		TotalAbleReviceNo                 int64   `json:"totalAbleReviceNo"`
		ChangeCountTime                   int64   `json:"changeCountTime"`
		ActivityDailyV2TotalConditionId   int64   `json:"activityDailyV2TotalConditionId"`
		ActivityDailyV2TotalConditionName string  `json:"activityDailyV2TotalConditionName"`
		ConditionAmount                   float64 `json:"conditionAmount"`
		IsActive                          bool    `json:"isActive"`
	}

	var getActivityDailyV2Total GetActivityDailyV2Total
	selectedFields2 := "activity_daily_v2_total.total_able_revice_no, activity_daily_v2_total.change_count_time, activity_daily_v2_total.is_active"
	selectedFields2 += ", activity_daily_v2_total.condition_amount as condition_amount"
	selectedFields2 += ", activity_daily_v2_total_condition.id as activity_daily_v2_total_condition_id, activity_daily_v2_total_condition.label_th as activity_daily_v2_total_condition_name"

	query := r.db.Table("activity_daily_v2_total")
	query = query.Select(selectedFields2)
	query = query.Joins("LEFT JOIN activity_daily_v2_total_condition ON activity_daily_v2_total_condition.id = activity_daily_v2_total.activity_daily_v2_total_condition_id")
	query = query.Take(&getActivityDailyV2Total)
	if err := query.Error; err != nil {
		return nil, err
	}

	var resultDetail []model.ActivityDailyV2Detail
	selectedFields := "id, no_number, credit_amount, is_higlight"

	query2 := r.db.Table("activity_daily_v2")
	query2 = query2.Select(selectedFields)
	query2 = query2.Where("change_count_time = ?", getActivityDailyV2Total.ChangeCountTime)
	query2 = query2.Scan(&resultDetail)
	if err := query2.Error; err != nil {
		return nil, err
	}

	var result model.GetActivityDailyV2TotalResponse

	result.ActivityDailyV2TotalConditionId = getActivityDailyV2Total.ActivityDailyV2TotalConditionId
	result.ActivityDailyV2TotalConditionName = getActivityDailyV2Total.ActivityDailyV2TotalConditionName
	result.ConditionAmount = getActivityDailyV2Total.ConditionAmount
	result.TotalAbleReviceNo = getActivityDailyV2Total.TotalAbleReviceNo
	result.ActivityDailyV2Detail = resultDetail
	result.IsActive = getActivityDailyV2Total.IsActive

	return &result, nil
}

func (r repo) WebGetActivityDailyV2() ([]model.WebActivityDailyV2Detail, *model.GetActivityDailyV2Total, error) {

	// type GetActivityDailyV2Total struct {
	// 	TotalAbleReviceNo                 int64  `json:"totalAbleReviceNo"`
	// 	ActivityDailyV2TotalConditionId   int64  `json:"activityDailyV2TotalConditionId"`
	// 	ActivityDailyV2TotalConditionName string `json:"activityDailyV2TotalConditionName"`
	// 	ChangeCountTime                   int64  `json:"changeCountTime"`
	// }

	var getActivityDailyV2Total model.GetActivityDailyV2Total
	// selectedFields2 := "total_able_revice_no, change_count_time"
	selectedFields2 := "activity_daily_v2_total.total_able_revice_no, activity_daily_v2_total.change_count_time"
	selectedFields2 += ", activity_daily_v2_total.condition_amount as condition_amount"
	selectedFields2 += ", activity_daily_v2_total_condition.id as activity_daily_v2_total_condition_id, activity_daily_v2_total_condition.label_th as activity_daily_v2_total_condition_name"

	query := r.db.Table("activity_daily_v2_total")
	query = query.Joins("LEFT JOIN activity_daily_v2_total_condition ON activity_daily_v2_total_condition.id = activity_daily_v2_total.activity_daily_v2_total_condition_id")
	query = query.Select(selectedFields2)
	query = query.Take(&getActivityDailyV2Total)
	if err := query.Error; err != nil {
		return nil, nil, err
	}

	var resultDetail []model.WebActivityDailyV2Detail
	selectedFields := "id, no_number, credit_amount, is_higlight"

	query2 := r.db.Table("activity_daily_v2")
	query2 = query2.Select(selectedFields)
	query2 = query2.Where("change_count_time = ?", getActivityDailyV2Total.ChangeCountTime)
	query2 = query2.Scan(&resultDetail)
	if err := query2.Error; err != nil {
		return nil, nil, err
	}

	return resultDetail, &getActivityDailyV2Total, nil
}

func (r repo) CheckUserActivityDailyV2AlreadyRevice(userId int64) (bool, error) {

	var result int64
	today := time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02")

	query := r.db.Table("activity_daily_v2_user")
	query = query.Select("id")
	query = query.Where("user_id = ?", userId)

	startDateAtBkk, err := r.ParseBodBkk(today)
	if err != nil {
		return false, err
	}
	query = query.Where("received_at >= ? ", startDateAtBkk)

	EndDateAtBkk, err := r.ParseEodBkk(today)
	if err != nil {
		return false, err
	}
	query = query.Where("received_at <= ? ", EndDateAtBkk)

	query = query.Count(&result)
	if err := query.Error; err != nil {
		return false, err
	}

	return result > 0, nil
}

func (r repo) GetUserActivityDailyV2NextNoNumber(user int64) (*model.ActivityDailyV2UserNextNo, *model.GetActivityDailyV2Total, error) {

	// Get the current change count time

	var getActivityDailyV2Total model.GetActivityDailyV2Total
	selectedFields := "activity_daily_v2_total.total_able_revice_no, activity_daily_v2_total.change_count_time"
	selectedFields += ", activity_daily_v2_total.condition_amount as condition_amount"
	selectedFields += ", activity_daily_v2_total_condition.id as activity_daily_v2_total_condition_id, activity_daily_v2_total_condition.label_th as activity_daily_v2_total_condition_name"

	query := r.db.Table("activity_daily_v2_total")
	query = query.Select(selectedFields)
	query = query.Joins("LEFT JOIN activity_daily_v2_total_condition ON activity_daily_v2_total_condition.id = activity_daily_v2_total.activity_daily_v2_total_condition_id")
	query = query.Take(&getActivityDailyV2Total)
	if err := query.Error; err != nil {
		return nil, nil, err
	}

	// check the lastest noNumber from the user
	type LastestNoNumber struct {
		ChangeCountTime int64 `json:"changeCountTime"`
		NoNumber        int64 `json:"noNumber"`
	}

	var lastestNoNumber LastestNoNumber
	selectedFields2 := "change_count_time, no_number"

	query2 := r.db.Table("activity_daily_v2_user")
	query2 = query2.Select(selectedFields2)
	query2 = query2.Where("user_id = ?", user)
	query2 = query2.Where("change_count_time = ?", getActivityDailyV2Total.ChangeCountTime)
	query2 = query2.Order("created_at DESC")
	query2 = query2.Take(&lastestNoNumber)
	if err := query2.Error; err != nil {
		lastestNoNumber.ChangeCountTime = getActivityDailyV2Total.ChangeCountTime
		lastestNoNumber.NoNumber = 0
	}

	var nextNoNumber model.ActivityDailyV2UserNextNo
	if getActivityDailyV2Total.TotalAbleReviceNo == lastestNoNumber.NoNumber {
		// get the first noNumber by the current change count time
		selectedFields3 := "id, change_count_time, no_number, credit_amount"

		query3 := r.db.Table("activity_daily_v2")
		query3 = query3.Select(selectedFields3)
		query3 = query3.Where("change_count_time = ?", getActivityDailyV2Total.ChangeCountTime)
		query3 = query3.Where("no_number = ?", 1)
		query3 = query3.Take(&nextNoNumber)
		if err := query3.Error; err != nil {
			return nil, nil, err
		}
	} else {
		// find the next noNumber by the lastest noNumber+1
		selectedFields3 := "id, change_count_time, no_number, credit_amount"

		query3 := r.db.Table("activity_daily_v2")
		query3 = query3.Select(selectedFields3)
		query3 = query3.Where("change_count_time = ?", lastestNoNumber.ChangeCountTime)
		query3 = query3.Where("no_number = ?", lastestNoNumber.NoNumber+1)
		query3 = query3.Take(&nextNoNumber)
		if err := query3.Error; err != nil {
			return nil, nil, err
		}
	}

	return &nextNoNumber, &getActivityDailyV2Total, nil
}

func (r repo) CreateActivityDailyV2User(body model.CreateActivityDailyV2User) (int64, error) {

	query := r.db.Table("activity_daily_v2_user")
	query = query.Create(&body)
	if err := query.Error; err != nil {
		return 0, err
	}
	return body.Id, nil
}

func (r repo) GetTurnoverUserActivityDailyV2(req model.GetTurnoverUserActivityDailyV2Request) ([]model.GetTurnoverUserActivityDailyV2Response, int64, error) {

	var list []model.GetTurnoverUserActivityDailyV2Response
	var total int64
	var err error

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return list, 0, err
	}

	count := r.db.Table("turnover_statement as statements")
	count = count.Select("statements.id")
	count = count.Joins("LEFT JOIN user ON user.id = statements.user_id")
	count = count.Where("statements.type_id = ?", model.TURN_BONUS_ACTIVITY_DAILY_V2)

	if req.MemberCode != "" {
		count = count.Where("user_id.member_code = ?", req.MemberCode)
	}

	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return list, 0, err
		}
		count = count.Where("statements.created_at >= ? ", startDateAtBkk)
	}
	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return list, 0, err
		}
		count = count.Where("statements.created_at <=  ?", endDateAtBkk)
	}
	if err = count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "statements.id as id, statements.user_id as user_id, statements.type_id as type_id, statements.ref_type_id as ref_type_id, statements.status_id as status_id, statements.start_turn_amount as start_turn_amount"
		selectedFields += ", statements.promotion_name as description, statements.bonus_amount as bonus_amount"
		selectedFields += ", statements.start_turn_at as start_turn_at, statements.total_turn_amount as total_turn_amount, statements.end_turn_at as end_turn_at, statements.created_at as created_at, statements.updated_at as updated_at"
		selectedFields += ", types.name as type_name, statuses.name as status_name"
		selectedFields += ", user.member_code as member_code"
		selectedFields += ", user.fullname as fullname, user.username as username"
		selectedFields += ", activity_daily_user.tidturn_percent as tidturn_percent"
		selectedFields += ", activity_daily_user.amount_condition as amount_condition"

		query := r.db.Table("turnover_statement as statements")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN activity_daily_user ON activity_daily_user.id = statements.ref_type_id")

		query = query.Joins("LEFT JOIN turnover_statement_type as types ON types.id = statements.type_id")
		query = query.Joins("LEFT JOIN turnover_statement_status as statuses ON statuses.id = statements.status_id")
		query = query.Joins("LEFT JOIN user ON user.id = statements.user_id")
		query = query.Where("statements.type_id = ?", model.TURN_BONUS_ACTIVITY_DAILY_V2)
		if req.MemberCode != "" {
			query = query.Where("user_id.member_code = ?", req.MemberCode)
		}

		if dateType.DateFrom != "" {
			startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
			if err != nil {
				return list, 0, err
			}
			query = query.Where("statements.created_at >= ? ", startDateAtBkk)
		}
		if dateType.DateTo != "" {
			endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
			if err != nil {
				return list, 0, err
			}
			query = query.Where("statements.created_at <=  ?", endDateAtBkk)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}
		if err = query.
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}
	return list, total, nil
}

func (r repo) GetActivityDailyV2IsActive() (*model.GetActivityDailyV2IsActive, error) {

	var result model.GetActivityDailyV2IsActive
	selectedFields := "is_active"

	query := r.db.Table("activity_daily_v2_total")
	query = query.Select(selectedFields)
	query = query.Where("id = ?", 1)
	query = query.Take(&result)
	if err := query.Error; err != nil {
		return nil, err
	}

	return &result, nil
}

func (r repo) GetActivityDailyV2TotalConditionOptions() ([]model.SelectOptions, error) {

	var result []model.SelectOptions
	selectedFields := "activity_daily_v2_total_condition.id as id,activity_daily_v2_total_condition.id as value, activity_daily_v2_total_condition.label_th as label"

	query := r.db.Table("activity_daily_v2_total_condition")
	query = query.Select(selectedFields)
	query = query.Scan(&result)
	if err := query.Error; err != nil {
		return nil, err
	}

	return result, nil
}

func (r repo) GetUserTodayDepositForActivityDaily(userId int64) ([]model.GetUserTodayDepositForActivityDaily, error) {

	todayTime := time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02")
	var result []model.GetUserTodayDepositForActivityDaily
	selectedFields := "id, user_id, credit_amount, transfer_at"

	query := r.db.Table("user_transaction")
	query = query.Select(selectedFields)
	query = query.Where("user_id = ?", userId)
	query = query.Where("direction_id = ?", 1)
	query = query.Where("type_id = ?", 1)
	query = query.Where("is_show = ?", 1)

	startDateAtBkk, err := r.ParseBodBkk(todayTime)
	if err != nil {
		return nil, err
	}
	query = query.Where("transfer_at >= ? ", startDateAtBkk)

	endDateAtBkk, err := r.ParseEodBkk(todayTime)
	if err != nil {
		return nil, err
	}
	query = query.Where("transfer_at <= ? ", endDateAtBkk)

	query = query.Scan(&result)
	if err := query.Error; err != nil {
		return nil, err
	}

	return result, nil

}
