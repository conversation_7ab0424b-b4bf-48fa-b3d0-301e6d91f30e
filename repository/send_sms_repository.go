package repository

import (
	"bytes"
	"cybergame-api/helper"
	"cybergame-api/model"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"

	"gorm.io/gorm"
)

func NewSendSmsRepository(db *gorm.DB) SendSmsRepository {
	return &repo{db}
}

type SendSmsRepository interface {
	DecreaseSmsCredit(smsCredit int64) error
	GetLocalWebInfo() (*model.WebStatusResponse, error)

	GetSendSmsUserInactive(id []int64) ([]model.GetSendSmsUserInactiveResponse, error)
	CreateSendSmsUserInactive(body []model.CreateSendSmsBody) error
	GetSendSmsSenderName(id int64) (*model.GetSendSmsSenderNameResponse, error)
	SentBulkSms(body model.SendCyberSmsRequest) (*model.SendCyberSmsResponse, error)
	BulkSmsStatus(body model.BulkStatisSmsCyberRequest) (*model.SendSmsCyberResponse, error)
	UpdateSendSmsStatus(body model.UpdateSendSmsBody) error

	GetSendSmsList(req model.GetSendSmsListRequest) ([]model.SendSmsListResponse, int64, error)
	GetSendSmsSenderNameOption() ([]model.SelectOptions, error)

	// OTP from our service
	GetCyberOtpKey() string
	SendCyberOtp(body model.SendCyberOtpRequest) (*model.SendCyberOtpResponse, error)
	VerifyCyberOtp(body model.VerifyCyberOtpRequest) (*model.VerifyCyberOtpResponse, error)
}

func (r repo) GetSendSmsUserInactive(id []int64) ([]model.GetSendSmsUserInactiveResponse, error) {

	var admin []model.GetSendSmsUserInactiveResponse
	selectedFields := "user.id as id, user.phone"

	if err := r.db.Table("user").
		Select(selectedFields).
		Where("user.id IN (?)", id).
		Where("user.deleted_at IS NULL").
		Scan(&admin).
		Error; err != nil {
		return nil, err
	}

	return admin, nil
}

func (r repo) CreateSendSmsUserInactive(body []model.CreateSendSmsBody) error {
	const chunkSize = 500

	for i := 0; i < len(body); i += chunkSize {
		end := i + chunkSize
		if end > len(body) {
			end = len(body)
		}

		chunk := body[i:end]

		// Create a chunk of records
		if err := r.db.Table("send_sms").Create(&chunk).Error; err != nil {
			return err
		}
	}

	return nil
}

func (r repo) GetSendSmsSenderName(id int64) (*model.GetSendSmsSenderNameResponse, error) {

	var senderName model.GetSendSmsSenderNameResponse
	selectedFields := "send_sms_sender_name.id as id, send_sms_sender_name.sender_name"

	if err := r.db.Table("send_sms_sender_name").
		Select(selectedFields).
		Where("send_sms_sender_name.is_sender_name_active = 1").
		Take(&senderName).
		Error; err != nil {
		return nil, err
	}

	return &senderName, nil
}

func (r repo) SentBulkSms(body model.SendCyberSmsRequest) (*model.SendCyberSmsResponse, error) {
	var response model.SendCyberSmsResponse

	// Retrieve environment variables
	url := os.Getenv("CYBERSMS_ENDPOINT")
	headerBearerToken := os.Getenv("CYBERSMS_API_KEY")
	if url == "" || headerBearerToken == "" {
		fmt.Print(" headerBearerToken :", headerBearerToken)
		return nil, errors.New("missing required environment variables")
	}
	url = os.Getenv("CYBERSMS_ENDPOINT") + "/sms/send-bulk"
	fmt.Print(" url :", url)

	// Convert request body to JSON
	requestBody, err := json.Marshal(body)
	if err != nil {
		response.Message = "failed to marshal request body"
		return nil, err
	}

	fmt.Println(" body : ", helper.StructJson(body))

	// Create HTTP request
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(requestBody))
	if err != nil {
		response.Message = "failed to create HTTP request"
		return nil, err
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+headerBearerToken)

	// Perform HTTP request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		response.Message = "failed to perform HTTP request"
		return nil, err
	}
	defer resp.Body.Close()

	// Convert response body to string
	respBodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	respBodyString := string(respBodyBytes)
	fmt.Println("SentBulkSms.Response Body: ", respBodyString)

	// Check response status code
	if resp.StatusCode != http.StatusCreated {
		type errorResponseSms struct {
			Message string `json:"message"`
			Data    string `json:"data"`
		}

		var errorResponse errorResponseSms
		if err := json.NewDecoder(resp.Body).Decode(&errorResponse); err != nil {
			response.Message = "failed to decode error response body"
			return &response, errors.New(errorResponse.Message)
		}

		// error
		fmt.Println(" errorResponse : ", resp)
		response.Message = errorResponse.Message
		return &response, errors.New(errorResponse.Message)
	}

	// Decode response body
	// if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
	// 	response.Message = "failed to decode response body"
	// 	return nil, err
	// }

	// Use json.Unmarshal instead of json.NewDecoder
	if err := json.Unmarshal(respBodyBytes, &response); err != nil {
		log.Println("Error.SentBulkSms.Unmarshal: ", err)
		return nil, err
	}

	return &response, nil
}

func (r repo) BulkSmsStatus(body model.BulkStatisSmsCyberRequest) (*model.SendSmsCyberResponse, error) {

	// Retrieve environment variables
	url := os.Getenv("CYBERSMS_ENDPOINT") + "/sms/bulk-status"
	headerBearerToken := os.Getenv("CYBERSMS_API_KEY")

	if url == "" || headerBearerToken == "" {
		return nil, errors.New("missing required environment variables")
	}

	// Convert request body to JSON
	requestBody, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}

	// Create HTTP request
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, err
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+headerBearerToken)

	// Perform HTTP request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}

	// Decode response body
	var response model.SendSmsCyberResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, err
	}

	return &response, nil
}

func (r repo) UpdateSendSmsStatus(body model.UpdateSendSmsBody) error {

	if err := r.db.Table("send_sms").
		Where("send_sms.ref_id = ?", body.RefId).
		Updates(body).
		Error; err != nil {
		return err
	}

	return nil
}

func (r repo) GetSendSmsList(req model.GetSendSmsListRequest) ([]model.SendSmsListResponse, int64, error) {

	var list []model.SendSmsListResponse
	var total int64
	var err error

	dateType, err := r.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return nil, 0, err
	}

	// Count total records for pagination purposes (without limit and offset) //
	count := r.db.Table("send_sms as tb_sms")
	count = count.Select("tb_sms.id")
	if req.Status != "" {
		count = count.Where("tb_sms.status = ?", req.Status)
	}
	if req.Phone != "" {
		count = count.Where("tb_sms.phone LIKE ?", "%"+req.Phone+"%")
	}
	if req.SenderNameId != nil {
		count = count.Where("tb_sms.sender_name_id = ?", req.SenderNameId)
	}

	if dateType.DateFrom != "" {
		startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("tb_sms.created_at >= ? ", startDateAtBkk)
	}

	if dateType.DateTo != "" {
		endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("tb_sms.created_at <=  ?", endDateAtBkk)
	}

	if err = count.
		Count(&total).
		Error; err != nil {
		return nil, total, err
	}

	if total > 0 {
		// SELECT //
		selectedFields := "tb_sms.id AS id, tb_sms.user_id AS user_id, tb_sms.phone AS phone"
		selectedFields += ", tb_sms.ref_id AS ref_id"
		selectedFields += ", tb_sms.sender_name_id AS sender_name_id, tb_sms.message AS message, tb_sms.credit_sms_amount AS credit_sms_amount"
		selectedFields += ", tb_sms.status AS status, tb_sms.created_at AS created_at, tb_sms.created_by_id AS created_by_id"
		selectedFields += ", tb_sender_name.sender_name AS sender_name"
		selectedFields += ", tb_user.fullname AS user_fullname"
		selectedFields += ", tb_admin.username AS created_by_name"

		query := r.db.Table("send_sms as tb_sms")
		query = query.Joins("JOIN send_sms_sender_name AS tb_sender_name ON tb_sms.sender_name_id = tb_sender_name.id")
		query = query.Joins("JOIN user AS tb_user ON tb_sms.user_id = tb_user.id")
		query = query.Joins("JOIN admin AS tb_admin ON tb_sms.created_by_id = tb_admin.id")

		query = query.Select(selectedFields)
		if req.Status != "" {
			query = query.Where("tb_sms.status = ?", req.Status)
		}
		if req.Phone != "" {
			query = query.Where("tb_sms.phone LIKE ?", "%"+req.Phone+"%")
		}
		if req.SenderNameId != nil {
			query = query.Where("tb_sms.sender_name_id = ?", req.SenderNameId)
		}

		if dateType.DateFrom != "" {
			startDateAtBkk, err := r.ParseBodBkk(dateType.DateFrom)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("tb_sms.created_at >= ? ", startDateAtBkk)
		}

		if dateType.DateTo != "" {
			endDateAtBkk, err := r.ParseEodBkk(dateType.DateTo)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("tb_sms.created_at <=  ?", endDateAtBkk)
		}

		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}

		if err = query.
			Order("tb_sms.created_at DESC").
			Offset(req.Page * req.Limit).
			Scan(&list).
			Error; err != nil {
			return nil, total, err
		}
	}

	return list, total, nil
}

func (r repo) GetSendSmsSenderNameOption() ([]model.SelectOptions, error) {

	var option []model.SelectOptions
	selectedFields := "send_sms_sender_name.id as id, send_sms_sender_name.sender_name as label"

	if err := r.db.Table("send_sms_sender_name").
		Select(selectedFields).
		Where("send_sms_sender_name.is_sender_name_active = 1").
		Scan(&option).
		Error; err != nil {
		return nil, err
	}

	return option, nil
}

func (r repo) GetCyberOtpKey() string {

	var otpKey string

	sql := r.db.Select("otp_key")
	sql = sql.Table("configuration_web")
	sql = sql.Limit(1)
	if err := sql.Scan(&otpKey).Error; err != nil {
		log.Println("GetCyberOtpKey.Scan: ", err)
		return ""
	}
	return otpKey
}

func (r repo) SendCyberOtp(body model.SendCyberOtpRequest) (*model.SendCyberOtpResponse, error) {

	var response model.SendCyberOtpResponse

	// Retrieve environment variables
	url := os.Getenv("CYBERSMS_ENDPOINT")
	headerBearerToken := os.Getenv("CYBERSMS_API_KEY")
	if url == "" || headerBearerToken == "" {
		log.Print(" headerBearerToken :", headerBearerToken)
		return nil, errors.New("missing required environment variables")
	}
	// POST https://api.cyberrichsms.com/api/v1/otp/send
	url = os.Getenv("CYBERSMS_ENDPOINT") + "/otp/send"
	fmt.Print("SentUserOtp.url :", url)

	// Convert request body to JSON
	requestBody, err := json.Marshal(body)
	if err != nil {
		response.Message = "failed to marshal request body"
		return nil, err
	}
	fmt.Println(" body : ", helper.StructJson(body))

	// Create HTTP request
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(requestBody))
	if err != nil {
		response.Message = "failed to create HTTP request"
		return nil, err
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+headerBearerToken)

	// Perform HTTP request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		response.Message = "failed to perform HTTP request"
		return nil, err
	}
	defer resp.Body.Close()

	// Convert response body to string
	respBodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	respBodyString := string(respBodyBytes)
	fmt.Println("SentUserOtp.Response Body: ", respBodyString)

	// Check response status code
	if resp.StatusCode != http.StatusCreated {
		type errorResponseSms struct {
			Message string `json:"message"`
			Data    string `json:"data"`
		}
		var errorResponse errorResponseSms
		if err := json.Unmarshal(respBodyBytes, &errorResponse); err != nil {
			log.Println("Error.SentUserOtp.Unmarshal: ", err)
			return nil, err
		}
		// error
		log.Println("SentUserOtp.errorResponse : ", resp)
		response.Message = errorResponse.Message
		return &response, errors.New(errorResponse.Message)
	}

	// Use json.Unmarshal instead of json.NewDecoder
	if err := json.Unmarshal(respBodyBytes, &response); err != nil {
		log.Println("Error.SentUserOtp.Unmarshal: ", err)
		return nil, err
	}
	return &response, nil
}

func (r repo) VerifyCyberOtp(body model.VerifyCyberOtpRequest) (*model.VerifyCyberOtpResponse, error) {

	var response model.VerifyCyberOtpResponse

	// Retrieve environment variables
	url := os.Getenv("CYBERSMS_ENDPOINT")
	headerBearerToken := os.Getenv("CYBERSMS_API_KEY")
	if url == "" || headerBearerToken == "" {
		log.Print(" headerBearerToken :", headerBearerToken)
		return nil, errors.New("missing required environment variables")
	}
	// POST https://api.cyberrichsms.com/api/v1/otp/verify
	url = os.Getenv("CYBERSMS_ENDPOINT") + "/otp/verify"
	fmt.Print("VerifyCyberOtp.url :", url)

	// Convert request body to JSON
	requestBody, err := json.Marshal(body)
	if err != nil {
		response.Message = "failed to marshal request body"
		return nil, err
	}
	fmt.Println(" body : ", helper.StructJson(body))

	// Create HTTP request
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(requestBody))
	if err != nil {
		response.Message = "failed to create HTTP request"
		return nil, err
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+headerBearerToken)

	// Perform HTTP request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		response.Message = "failed to perform HTTP request"
		return nil, err
	}
	defer resp.Body.Close()

	// Convert response body to string
	respBodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	respBodyString := string(respBodyBytes)
	fmt.Println("VerifyCyberOtp.Response Body: ", respBodyString)

	// Check response status code
	if resp.StatusCode != http.StatusCreated {
		type errorResponseSms struct {
			Message string `json:"message"`
			Data    string `json:"data"`
		}
		var errorResponse errorResponseSms
		if err := json.Unmarshal(respBodyBytes, &errorResponse); err != nil {
			log.Println("Error.VerifyCyberOtp.Unmarshal: ", err)
			return nil, err
		}
		// error
		log.Println("VerifyCyberOtp.errorResponse : ", resp)
		response.Message = errorResponse.Message
		return &response, errors.New(errorResponse.Message)
	}
	// {
	// 	"otpCode": "430239",
	// 	"otpId": "a5053091-b8eb-4ed4-a464-ce5863587c62"
	//   }
	// {
	//     "message": "success",
	//     "data": true
	// }
	// Use json.Unmarshal instead of json.NewDecoder
	if err := json.Unmarshal(respBodyBytes, &response); err != nil {
		log.Println("Error.VerifyCyberOtp.Unmarshal: ", err)
		return nil, err
	}
	return &response, nil
}
