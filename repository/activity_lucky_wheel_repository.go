package repository

import (
	"cybergame-api/model"
	"fmt"
	"io"
	"time"

	"gorm.io/gorm"
)

var confActivityLuckyWheelSetting *model.ActivityLuckyWheelSettingResponse

func NewActivityLuckyWheelRepository(db *gorm.DB) ActivityLuckyWheelRepository {
	return &repo{db}
}

type ActivityLuckyWheelRepository interface {
	GetDb() *gorm.DB
	GetActivityLuckyWheelSetting() (*model.ActivityLuckyWheelSettingResponse, error)
	CreateActivityLuckyWheelSetting(body model.ActivityLuckyWheelSettingCreateBody) (*int64, error)
	UpdateActivityLuckyWheelSetting(id int64, body model.ActivityLuckyWheelSettingUpdateBody) error
	GetActivityLuckyWheelList() ([]model.ActivityLuckyWheelResponse, error)
	UpdateActivityLuckyWheel(body model.ActivityLuckyWheelUpdateBody) error
	GetReportLuckyWheelRoundList(req model.LuckyweelSummaryListRequest) ([]model.LuckyweelSummaryListBody, int64, error)
	GetReportLuckyWheelRoundByUserId(req model.LuckyweelSummaryByUserIdRequest) ([]model.LuckyweelSummaryByUserIdResponse, int64, error)
	GetMemberCodeUser(id int64) (*string, error)
	CreateActivityLuckyWheelRound(body model.ActivityLuckyWheelRoundBody) (*int64, error)
	CreateActivityLuckyWheelRoundUser(body []model.ActivityLuckyWheelRoundUserBody) error
	GetConditionType() ([]model.ConditionTypeResponse, error)
	GetActivityLuckyWheelUserList() ([]model.ActivityLuckyWheelUserResponse, error)
	GetAmountRoundUser(userId int64) (int64, error)
	GetAmountRoundUsedUser(userId int64) (int64, error)
	GetAmountRoundReceiveUser(userId int64) (int64, error)

	// Dunk
	GetActivityLuckyWheelRoundUser(userId int64) (*model.CheckLuckyWheelRoundCurrentUserResponse, error)
	ClearExpiredRoundByUserId(userId int64) error
	UpdateActivityLuckyWheelRoundUser(body model.UpdateActivityLuckyWheelRoundUserRequest) error
	GetMarketingConfigByKey(key string, defaultVal string) (*model.MarketingConfig, error)
	IncreaseUserCredit(body model.UserTransactionCreateRequest) (*model.UserTransactionCreateResponse, error)
	GetActivityLuckyWheelRoundUserById(id int64) (*model.ActivityLuckyWheelRoundUserResponse, error)
	GetActivityLuckyWheelRoundUserByInComeId(id int64) (*model.ActivityLuckyWheelRoundUserResponse, error)
	ClearExpiredRoundByOneMonth() error
	GetListActivityLuckyWheelRoundByIds(Ids []int64) ([]model.LuckyweelRoundListResponse, error)
	CreateActivityLuckyWheelRoundConfirm(body model.ActivityLuckyWheelRoundConfirmCreateRequest) (int64, error)
	GetActivityLuckyWheelRoundConfirmByKey(Key string) (*model.ActivityLuckyWheelRoundConfirmResponse, error)
	RollBackConfirmActionConfirmActivityLuckyWheel(id int64) error
	RollBackActivityLuckyWheelUser(id int64) error
	GetRaceActionIdByActionKey(actionKey string) (*int64, error)
	CreateRaceCondition(body model.RaceActionCreateBody) (int64, error)
	GetActivityLuckyWheelSettingAmountSpin() ([]model.SelectOptions, error)
	GetActivityLuckyWheelSettingImageSpin(spinId int64) (*model.GetActivityLuckyWheelSettingImageSpinResponse, error)
	UpdateActivityLuckyWheelSettingImageSpin(body model.ActivityLuckyWheelSettingImageSpinUpdateBody) error
	UploadImageToS3(pathUpload string, filename string, fileReader io.Reader) (*model.FileUploadResponse, error)

	//tula
	GetUserMemberInfoById(id int64) (*model.UserResponse, error)
	CreateUserIncomeLog(body model.UserIncomeLogCreateBody) (*int64, error)
	UpdateTakeReturnTransaction(id int64, body model.PromotionReturnTransactionUpdateBody) error
	GetUserIncomeLogById(id int64) (*model.UserIncomeLogResponse, error)
	ConfirmUserIncomeLog(body model.UserIncomeLogConfirmBody) error
}

func (r repo) GetActivityLuckyWheelSetting() (*model.ActivityLuckyWheelSettingResponse, error) {

	var record model.ActivityLuckyWheelSettingResponse

	if confActivityLuckyWheelSetting != nil && time.Now().Before(confActivityLuckyWheelSetting.CacheExpiredAt) {
		return confActivityLuckyWheelSetting, nil
	}

	// selectedFields := "id, condition_id, lose_per_roll, max_roll_per_day, cumulative_expired_days, is_enabled, created_at, updated_at"
	// selectedFields += ", activity_lucky_wheel_setting_amount_spin_id"
	selectedFields := "settings.id as id, settings.condition_id as condition_id, settings.lose_per_roll as lose_per_roll, settings.max_roll_per_day as max_roll_per_day"
	selectedFields += ", settings.cumulative_expired_days as cumulative_expired_days, settings.is_enabled as is_enabled"
	selectedFields += ", settings.created_at as created_at, settings.updated_at as updated_at"
	selectedFields += ", settings.activity_lucky_wheel_setting_amount_spin_id as activity_lucky_wheel_setting_amount_spin_id"
	selectedFields += ", amount_spin.name as activity_lucky_wheel_setting_amount_spin_name"

	if err := r.db.Table("activity_lucky_wheel_setting as settings").
		Select(selectedFields).
		Joins("LEFT JOIN activity_lucky_wheel_setting_amount_spin as amount_spin ON settings.activity_lucky_wheel_setting_amount_spin_id = amount_spin.id").
		Take(&record).
		Error; err != nil {
		return nil, err
	}

	// Set Cache
	confActivityLuckyWheelSetting = &record
	confActivityLuckyWheelSetting.CacheExpiredAt = time.Now().Add(60 * time.Minute)

	return &record, nil
}

func (r repo) CreateActivityLuckyWheelSetting(body model.ActivityLuckyWheelSettingCreateBody) (*int64, error) {

	if err := r.db.Table("activity_lucky_wheel_setting").Create(&body).Error; err != nil {
		return nil, err
	}

	// Clear Cache = GetNewData Later
	confActivityLuckyWheelSetting = nil

	return &body.Id, nil
}

func (r repo) UpdateActivityLuckyWheelSetting(id int64, body model.ActivityLuckyWheelSettingUpdateBody) error {

	updateData := map[string]interface{}{
		"condition_id":     body.ConditionId,
		"lose_per_roll":    body.LosePerRoll,
		"max_roll_per_day": body.MaxRollPerDay,
		"activity_lucky_wheel_setting_amount_spin_id": body.ActivityLuckyWheelSettingAmountSpinId,
		"cumulative_expired_days":                     body.CumulativeExpiredDays,
		"is_enabled":                                  body.IsEnabled,
	}
	if err := r.db.Table("activity_lucky_wheel_setting").Where("id = ?", id).Updates(updateData).Error; err != nil {
		return err
	}

	// Clear Cache = GetNewData Later
	confActivityLuckyWheelSetting = nil

	return nil
}

func (r repo) GetActivityLuckyWheelList() ([]model.ActivityLuckyWheelResponse, error) {

	var list []model.ActivityLuckyWheelResponse

	selectedFields := "id, position, message, minimum_reward, hex_background_color, percent_win, created_at, updated_at"
	selectedFields += ", font_color"
	if err := r.db.Table("activity_lucky_wheel").
		Select(selectedFields).
		Scan(&list).
		Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (r repo) UpdateActivityLuckyWheel(body model.ActivityLuckyWheelUpdateBody) error {
	// fmt.Println("body", &body)
	// bodyUpdate := map[string]interface{}{
	// 	"message":              body.Message,
	// 	"minimum_reward":       body.MinimumReward,
	// 	"hex_background_color": body.HexBackgroundColor,
	// 	"font_color":           body.FontColor,
	// 	"percent_win":          body.PercentWin,
	// }

	// fmt.Println("bodyUpdate", &bodyUpdate)

	query := r.db.Table("activity_lucky_wheel")
	query = query.Where("id = ?", body.Id)
	query = query.Updates(body)
	if err := query.Error; err != nil {
		return err
	}
	return nil

}

func (r repo) GetReportLuckyWheelRoundList(req model.LuckyweelSummaryListRequest) ([]model.LuckyweelSummaryListBody, int64, error) {

	var list []model.LuckyweelSummaryListBody
	var total int64

	selectedFields := "activity_lucky_wheel_round_user.lucky_wheel_round_id as lucky_wheel_round_id"
	selectedFields += ", sum(activity_lucky_wheel_round_user.reward) as total_reward"
	selectedFields += ", count(activity_lucky_wheel_round_user.id) as lucky_spin_count"
	selectedFields += ", activity_lucky_wheel_round_user.status_id as status_id"
	selectedFields += ", activity_lucky_wheel_round_user.user_id as user_id"
	selectedFields += ", activity_lucky_wheel_round_user.received_date as received_date"

	count := r.db.Table("activity_lucky_wheel_round_user")
	count = count.Select(selectedFields)
	count = count.Joins("LEFT JOIN user on user.id = activity_lucky_wheel_round_user.user_id")

	count = count.Group("activity_lucky_wheel_round_user.user_id, activity_lucky_wheel_round_user.status_id, activity_lucky_wheel_round_user.lucky_wheel_round_id,activity_lucky_wheel_round_user.received_date")

	if req.Search != "" {
		count = count.Where("user.member_code LIKE ? OR activity_lucky_wheel_round_user.condition_description LIKE ?", "%"+req.Search+"%", "%"+req.Search+"%")
	}
	if req.FromDate != "" {
		startDateAtBkk, err := r.ParseBodBkk(req.FromDate)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("activity_lucky_wheel_round_user.received_date >= ?", startDateAtBkk)
	}
	if req.ToDate != "" {
		endDateAtBkk, err := r.ParseEodBkk(req.ToDate)
		if err != nil {
			return nil, 0, err
		}
		count = count.Where("activity_lucky_wheel_round_user.received_date <= ?", endDateAtBkk)
	}

	if err := count.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if total > 0 {
		query := r.db.Table("activity_lucky_wheel_round_user")
		query = query.Select(selectedFields)
		query = query.Joins("LEFT JOIN user on user.id = activity_lucky_wheel_round_user.user_id")
		// query = query.Group("activity_lucky_wheel_round_user.user_id, activity_lucky_wheel_round_user.condition_description, activity_lucky_wheel_round_user.condition_amount,  activity_lucky_wheel_round_user.status_id, activity_lucky_wheel_round_user.lucky_wheel_round_id, activity_lucky_wheel_round_user.condition_id,activity_lucky_wheel_round_user.id")
		query = query.Group("activity_lucky_wheel_round_user.user_id, activity_lucky_wheel_round_user.status_id, activity_lucky_wheel_round_user.lucky_wheel_round_id,activity_lucky_wheel_round_user.received_date")

		if req.Search != "" {
			query = query.Where("user.member_code LIKE ? OR activity_lucky_wheel_round_user.condition_description LIKE ?", "%"+req.Search+"%", "%"+req.Search+"%")
		}
		if req.FromDate != "" {
			startDateAtBkk, err := r.ParseBodBkk(req.FromDate)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("activity_lucky_wheel_round_user.received_date >= ?", startDateAtBkk)
		}
		if req.ToDate != "" {
			endDateAtBkk, err := r.ParseEodBkk(req.ToDate)
			if err != nil {
				return nil, 0, err
			}
			query = query.Where("activity_lucky_wheel_round_user.received_date <= ?", endDateAtBkk)
		}
		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}

		if err := query.Order("activity_lucky_wheel_round_user.received_date desc").Offset(req.Page * req.Limit).Scan(&list).Error; err != nil {
			return nil, 0, err
		}

	}

	userIds := []int64{}
	wheelIs := []int64{}

	for _, v := range list {
		userIds = append(userIds, v.UserId)
		wheelIs = append(wheelIs, v.LuckyWheelRoundId)
	}

	var wheelIdsInfo []model.LuckyWheelRoundInfo
	query1 := r.db.Table("activity_lucky_wheel_round_user as tb_log")
	query1 = query1.Select("tb_log.condition_id, tb_log.condition_description, tb_log.condition_amount, tb_log.lucky_wheel_round_id")
	query1 = query1.Where("tb_log.lucky_wheel_round_id IN ?", wheelIs)
	if err := query1.Scan(&wheelIdsInfo).Error; err != nil {
		return nil, 0, err
	}
	wheelMap := map[int64]model.LuckyWheelRoundInfo{}
	for _, v := range wheelIdsInfo {
		wheelMap[v.LuckyWheelRoundId] = v
	}

	var userInfos []model.UserResponse
	var query2 = r.db.Table("user as tb_user")
	query2 = query2.Select("tb_user.id, tb_user.member_code")
	query2 = query2.Where("tb_user.id IN ?", userIds)
	if err := query2.Scan(&userInfos).Error; err != nil {
		return nil, 0, err
	}
	userMap := map[int64]model.UserResponse{}
	for _, v := range userInfos {
		userMap[v.Id] = v
	}

	for i, v := range list {
		if _, ok := userMap[v.UserId]; ok {
			list[i].MemberCode = *userMap[v.UserId].MemberCode
		}
		if _, ok := wheelMap[v.LuckyWheelRoundId]; ok {
			list[i].ConditionId = wheelMap[v.LuckyWheelRoundId].ConditionId
			if v.StatusId == model.ACTIVITY_LUCKY_WHEEL_ROUND_STATUS_EXPIRED {
				list[i].ConditionDescription = "EXPRIED"
			} else {
				list[i].ConditionDescription = wheelMap[v.LuckyWheelRoundId].ConditionDescription
			}
			list[i].ConditionAmount = wheelMap[v.LuckyWheelRoundId].ConditionAmount
		}
	}

	return list, total, nil
}

func (r repo) GetReportLuckyWheelRoundByUserId(req model.LuckyweelSummaryByUserIdRequest) ([]model.LuckyweelSummaryByUserIdResponse, int64, error) {
	var list []model.LuckyweelSummaryByUserIdResponse
	var total int64

	selectedFields := "id, user_id, reward, received_date, rotated_date"

	count := r.db.Table("activity_lucky_wheel_round_user")
	count = count.Select(selectedFields)
	count = count.Where("user_id = ?", req.UserId)
	count = count.Where("reward IS NOT NULL")

	if err := count.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if total > 0 {
		query := r.db.Table("activity_lucky_wheel_round_user")
		query = query.Select(selectedFields)
		query = query.Where("user_id = ?", req.UserId)
		query = query.Where("reward IS NOT NULL")

		if req.Limit > 0 {
			query = query.Limit(req.Limit)
		}

		if err := query.Offset(req.Page * req.Limit).Scan(&list).Error; err != nil {
			return nil, 0, err
		}

	}

	return list, total, nil
}

func (r repo) GetMemberCodeUser(userId int64) (*string, error) {

	var memberCode string

	if err := r.db.Table("user").
		Select("member_code").
		Where("id = ?", userId).
		Where("user.deleted_at IS NULL").
		Take(&memberCode).
		Error; err != nil {
		return nil, err
	}

	return &memberCode, nil
}

func (r repo) CreateActivityLuckyWheelRound(body model.ActivityLuckyWheelRoundBody) (*int64, error) {
	if err := r.db.Table("activity_lucky_wheel_round").Create(&body).Error; err != nil {
		return nil, err
	}
	return &body.Id, nil
}

func (r repo) CreateActivityLuckyWheelRoundUser(body []model.ActivityLuckyWheelRoundUserBody) error {

	if err := r.db.Table("activity_lucky_wheel_round_user").Create(&body).Error; err != nil {
		return err
	}
	return nil
}

func (r repo) GetActivityLuckyWheelRoundUserById(id int64) (*model.ActivityLuckyWheelRoundUserResponse, error) {

	var record model.ActivityLuckyWheelRoundUserResponse

	selectedFields := "id, user_id, lucky_wheel_id, reward, condition_id, condition_amount, status_id, received_date, expired_date, rotated_date"
	if err := r.db.Table("activity_lucky_wheel_round_user").
		Select(selectedFields).
		Where("id = ?", id).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetActivityLuckyWheelRoundUserByInComeId(id int64) (*model.ActivityLuckyWheelRoundUserResponse, error) {

	var record model.ActivityLuckyWheelRoundUserResponse

	selectedFields := "id, user_id, lucky_wheel_id, reward, condition_id, condition_amount, status_id, received_date, expired_date, rotated_date"
	if err := r.db.Table("activity_lucky_wheel_round_user").
		Select(selectedFields).
		Where("user_income_id = ?", id).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetConditionType() ([]model.ConditionTypeResponse, error) {

	var list []model.ConditionTypeResponse

	selectedFields := "id, name, label_th, label_en"
	if err := r.db.Table("activity_lucky_wheel_condition").
		Select(selectedFields).
		Scan(&list).
		Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (r repo) GetActivityLuckyWheelUserList() ([]model.ActivityLuckyWheelUserResponse, error) {

	var list []model.ActivityLuckyWheelUserResponse

	selectedFields := "id, position, message, hex_background_color, font_color"
	if err := r.db.Table("activity_lucky_wheel").
		Select(selectedFields).
		Scan(&list).
		Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (r repo) GetAmountRoundUser(userId int64) (int64, error) {

	var total int64
	timeToday := time.Now().UTC()
	dateAddSeven := timeToday.Add(7 * time.Hour).Format("2006-01-02")
	addDateAtBkk, err := r.ParseBodBkk(dateAddSeven)
	if err != nil {
		return 0, err
	}

	selectedFields := "id"
	if err := r.db.Table("activity_lucky_wheel_round_user").
		Select(selectedFields).
		Where("expired_date > ? OR expired_date IS NULL", addDateAtBkk).
		Where("rotated_date IS NULL").
		Where("user_id = ?", userId).
		Where("status_id = ?", model.ACTIVITY_LUCKY_WHEEL_ROUND_STATUS_ACTIVE).
		Where("lucky_wheel_id IS NULL").
		Where("reward IS NULL").
		Order("id asc").
		Count(&total).
		Error; err != nil {
		return 0, err
	}

	return total, nil
}

func (r repo) GetAmountRoundUsedUser(userId int64) (int64, error) {
	var total int64
	timeToday := time.Now().UTC()
	dateAddSeven := timeToday.Add(7 * time.Hour).Format("2006-01-02")
	addDateAtBkk, err := r.ParseBodBkk(dateAddSeven)
	if err != nil {
		return 0, err
	}

	selectedFields := "id"
	if err := r.db.Table("activity_lucky_wheel_round_user").
		Select(selectedFields).
		Where("expired_date > ? OR expired_date IS NULL", addDateAtBkk).
		Where("rotated_date >= ?", addDateAtBkk).
		Where("user_id = ?", userId).
		Where("status_id != ?", model.ACTIVITY_LUCKY_WHEEL_ROUND_STATUS_ACTIVE).
		Where("lucky_wheel_id IS NOT NULL").
		Where("reward IS NOT NULL").
		Order("id asc").
		Count(&total).
		Error; err != nil {
		return 0, err
	}

	return total, nil
}

func (r repo) GetActivityLuckyWheelRoundUser(userId int64) (*model.CheckLuckyWheelRoundCurrentUserResponse, error) {
	var record model.CheckLuckyWheelRoundCurrentUserResponse

	selectedFields := "id, user_id, lucky_wheel_id, reward, condition_id, condition_amount, status_id, received_date, expired_date, rotated_date"

	query := r.db.Table("activity_lucky_wheel_round_user")
	query = query.Select(selectedFields)
	// SLOW SQL >= 200ms index user_id
	query = query.Where("user_id = ?", userId)
	query = query.Where("status_id = ?", model.ACTIVITY_LUCKY_WHEEL_ROUND_STATUS_ACTIVE)
	query = query.Where("lucky_wheel_id IS NULL")
	query = query.Where("reward IS NULL")
	query = query.Order("id asc")
	query = query.First(&record)
	if err := query.Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) UpdateActivityLuckyWheelRoundUser(body model.UpdateActivityLuckyWheelRoundUserRequest) error {

	query := r.db.Table("activity_lucky_wheel_round_user")
	query = query.Where("id = ?", body.Id)
	query = query.Updates(&body)
	if err := query.Error; err != nil {
		return err
	}
	return nil

}

func (r repo) ClearExpiredRoundByUserId(userId int64) error {

	dbTimeExpired := time.Now().UTC().Format("2006-01-02 15:04:05")

	bodyUpdate := map[string]interface{}{
		"status_id":             model.ACTIVITY_LUCKY_WHEEL_ROUND_STATUS_EXPIRED,
		"condition_description": "EXPRIED",
	}

	query := r.db.Table("activity_lucky_wheel_round_user")
	query = query.Where("user_id = ?", userId)
	query = query.Where("status_id = ?", model.ACTIVITY_LUCKY_WHEEL_ROUND_STATUS_ACTIVE)
	query = query.Where("lucky_wheel_id IS NULL")
	query = query.Where("reward IS NULL")
	query = query.Where("expired_date < ?", dbTimeExpired)
	query = query.Updates(bodyUpdate)
	if err := query.Error; err != nil {
		return err
	}
	return nil
}

func (r repo) ClearExpiredRoundByOneMonth() error {

	bkkStartDate := time.Now().UTC().Format("2006-01-02 15:04:05")
	bkkEndDate := time.Now().UTC().AddDate(0, 0, -7).Format("2006-01-02 15:04:05")

	bodyUpdate := map[string]interface{}{
		"status_id":             model.ACTIVITY_LUCKY_WHEEL_ROUND_STATUS_EXPIRED,
		"condition_description": "EXPRIED",
	}

	query := r.db.Table("activity_lucky_wheel_round_user")
	query = query.Where("status_id = ?", model.ACTIVITY_LUCKY_WHEEL_ROUND_STATUS_ACTIVE)
	query = query.Where("lucky_wheel_id IS NULL")
	query = query.Where("reward IS NULL")
	query = query.Where("expired_date < ?", bkkStartDate)
	query = query.Where("expired_date > ?", bkkEndDate)
	query = query.Updates(bodyUpdate)
	if err := query.Error; err != nil {
		return err
	}

	return nil
}

func (r repo) GetListActivityLuckyWheelRoundByIds(Ids []int64) ([]model.LuckyweelRoundListResponse, error) {

	var list []model.LuckyweelRoundListResponse

	selectedFields := "id , user_id, received, condition_id, condition_amount, received_date, expired_date, created_at, updated_at"

	query := r.db.Table("activity_lucky_wheel_round")
	query = query.Select(selectedFields)
	query = query.Where("id IN ?", Ids)

	if err := query.Scan(&list).Error; err != nil {
		return nil, err
	}

	return list, nil

}

func (r repo) CreateActivityLuckyWheelRoundConfirm(body model.ActivityLuckyWheelRoundConfirmCreateRequest) (int64, error) {

	if err := r.db.Table("activity_lucky_wheel_round_confirm").Create(&body).Error; err != nil {
		return 0, err
	}
	return body.Id, nil
}

func (r repo) GetActivityLuckyWheelRoundConfirmByKey(Key string) (*model.ActivityLuckyWheelRoundConfirmResponse, error) {

	var record model.ActivityLuckyWheelRoundConfirmResponse

	selectedFields := "id, action_key, user_id, lucky_wheel_user_id, condition_amount"
	if err := r.db.Table("activity_lucky_wheel_round_confirm").
		Select(selectedFields).
		Where("action_key = ?", Key).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) GetAmountRoundReceiveUser(userId int64) (int64, error) {
	var total int64
	timeToday := time.Now().UTC()
	dateAddSeven := timeToday.Add(7 * time.Hour).Format("2006-01-02")
	addDateAtBkk, err := r.ParseBodBkk(dateAddSeven)
	if err != nil {
		return 0, err
	}

	selectedFields := "id, received_date"
	if err := r.db.Table("activity_lucky_wheel_round_user").
		Select(selectedFields).
		Where("received_date >= ?", addDateAtBkk).
		Where("user_id = ?", userId).
		Order("id asc").
		Count(&total).
		Error; err != nil {
		return 0, err
	}

	return total, nil
}

func (r repo) RollBackConfirmActionConfirmActivityLuckyWheel(id int64) error {

	data := map[string]interface{}{
		"action_key": fmt.Sprintf("ROLLBACK_%d", id),
		"deleted_at": time.Now().UTC(),
	}

	query := r.db.Table("activity_lucky_wheel_round_confirm")
	query = query.Where("id = ?", id)
	query = query.Updates(data)
	if err := query.Error; err != nil {
		return err
	}
	return nil

}

func (r repo) RollBackActivityLuckyWheelUser(id int64) error {

	data := map[string]interface{}{
		"status_id": model.ACTIVITY_LUCKY_WHEEL_ROUND_STATUS_PRIZE_CLAIMED,
	}

	query := r.db.Table("activity_lucky_wheel_round_user")
	query = query.Where("id = ?", id)
	query = query.Updates(data)
	if err := query.Error; err != nil {
		return err
	}
	return nil

}

func (r repo) GetActivityLuckyWheelSettingAmountSpin() ([]model.SelectOptions, error) {

	options := []model.SelectOptions{}

	selectedFields := "id as id, name as label, name as value"
	var sql = r.db.Table("activity_lucky_wheel_setting_amount_spin").Select(selectedFields)
	if err := sql.
		Scan(&options).
		Error; err != nil {
		return nil, err
	}
	return options, nil
}

func (r repo) GetActivityLuckyWheelSettingImageSpin(spinId int64) (*model.GetActivityLuckyWheelSettingImageSpinResponse, error) {

	var record model.GetActivityLuckyWheelSettingImageSpinResponse

	selectedFields := "id, activity_lucky_wheel_setting_amount_spin_id, image_main_spin_url, image_arrow_spin_url, image_button_spin_url"
	if err := r.db.Table("activity_lucky_wheel_setting_image_spin").
		Select(selectedFields).
		Where("activity_lucky_wheel_setting_amount_spin_id = ?", spinId).
		Take(&record).
		Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (r repo) UpdateActivityLuckyWheelSettingImageSpin(body model.ActivityLuckyWheelSettingImageSpinUpdateBody) error {

	query := r.db.Table("activity_lucky_wheel_setting_image_spin")
	query = query.Where("activity_lucky_wheel_setting_amount_spin_id = ?", body.ActivityLuckyWheelSettingAmountSpinId)
	query = query.Updates(&body)
	if err := query.Error; err != nil {
		return err
	}
	return nil

}
