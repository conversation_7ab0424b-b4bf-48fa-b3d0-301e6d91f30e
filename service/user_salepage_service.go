package service

import (
	"cybergame-api/model"
	"cybergame-api/repository"
	"errors"
	"fmt"
	"log"
	"net/http"
	"os"

	"gorm.io/gorm"
)

type UserSalepageService interface {
	GetUserSalepageStatSummary(userId int64) (*model.UserSalepageStatSummaryResponse, error)
	GetUserSalepageInfo(userId int64) (*model.UserSalepageResponse, error)
	UpdateUserSalepageInfo(req model.UserSalepageUpdateRequest) error
	// GetUserSalepageStatByDailyKey(dailyKey string) (*model.UserSalepageStatResponse, error)
	GetCustomSalepageBlockList(req model.CustomSalepageBlockListRequest) ([]model.CustomSalepageBlockResponse, error)
	GetCustomSalepageBlockById(req model.GetByIdUserRequest) (*model.CustomSalepageBlockResponse, error)
	UpdateCustomSalepageBlock(req model.CustomSalepageBlockUpdateRequest) error
	SortCustomSalepageBlock(req model.DragSortRequest, userId int64) error
	UploadImageToS3CustomSalePage(imageFileBody *http.Request) (*model.FileUploadResponse, error)
	// CreateUserSalepageStat(body model.UserSalepageStatCreateBody) (*int64, error)
	// WEB
	GetWebCustomSalepageBlockList(code string) ([]model.CustomSalepageBlockResponse, error)
	IncreaseTodayLinkClick(req model.PostUserSalepageRequest) error
	IncreaseTodayMemberRegisterClick(req model.PostUserSalepageRequest) error
	IncreaseTodayAdminClick(req model.PostUserSalepageRequest) error
}

type userSalepageService struct {
	repo repository.UserSalepageRepository
}

func NewUserSalepageService(
	repo repository.UserSalepageRepository,
) *userSalepageService {
	return &userSalepageService{repo}
}

func (s *userSalepageService) GetUserSalepageStatSummary(userId int64) (*model.UserSalepageStatSummaryResponse, error) {

	// Always return a data.
	var stat model.UserSalepageStatSummaryResponse
	stat.UserId = userId

	if data, err := s.repo.GetUserSalepageStatSummary(userId); err == nil {
		stat.TotalLinkClickCount = data.TotalLinkClickCount
		stat.TodayMemberRegisterCount = data.TodayMemberRegisterCount
		stat.TotalMemberRegisterCount = data.TotalMemberRegisterCount
		stat.TodayAdminClickCount = data.TodayAdminClickCount
		stat.TotalAdminClickCount = data.TotalAdminClickCount
	}
	return &stat, nil
}

func (s *userSalepageService) GetUserSalepageInfo(userId int64) (*model.UserSalepageResponse, error) {

	data, err := s.repo.GetUserSalepageInfo(userId)
	if err != nil {
		return nil, err
	}
	return data, nil
}

func (s *userSalepageService) UpdateUserSalepageInfo(req model.UserSalepageUpdateRequest) error {

	// check unique saleCode that not same with current user
	if req.SaleCode != nil {
		if *req.SaleCode == "" {
			return errors.New("กรุณากรอกรหัสผู้ใช้")
		}
		if err := s.repo.CheckuserSalepageCodeUnique(req.UserId, *req.SaleCode); err != nil {
			return errors.New("รหัสผู้ใช้ซ้ำ")
		}
	}

	if err := s.repo.UpdateUserSalepageInfo(req); err != nil {
		return err
	}
	return nil
}

func (s *userSalepageService) GetCustomSalepageBlockList(req model.CustomSalepageBlockListRequest) ([]model.CustomSalepageBlockResponse, error) {

	data, err := s.repo.GetCustomSalepageBlockList(req)
	if err != nil {
		return nil, err
	}
	return data, nil
}

func (s *userSalepageService) GetCustomSalepageBlockById(req model.GetByIdUserRequest) (*model.CustomSalepageBlockResponse, error) {

	record, err := s.repo.GetCustomSalepageBlockById(req.Id)
	if err != nil {
		log.Println("UpdateCustomSalepageBlock.ERROR= ", err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("ไม่พบข้อมูล")
		}
		return nil, err
	}
	if record.UserId != req.UserId {
		log.Println("UpdateCustomSalepageBlock.INVALID_OWNER, userId=", req.UserId, " record.UserId=", record.UserId)
		return nil, errors.New("ไม่พบข้อมูล")
	}

	return record, nil
}

func (s *userSalepageService) UpdateCustomSalepageBlock(req model.CustomSalepageBlockUpdateRequest) error {

	// Check userOwner
	record, err := s.GetCustomSalepageBlockById(model.GetByIdUserRequest{Id: req.Id, UserId: req.UserId})
	if err != nil {
		return err
	}

	var body model.CustomSalepageBlockUpdateBody
	body.IsShow = req.IsShow
	// body.SortOrder = req.SortOrder
	body.ValueName = req.ValueName
	body.ValueColor = req.ValueColor
	body.ValueImage1 = req.ValueImage1
	body.ValueUrl1 = req.ValueUrl1
	body.ValueImage2 = req.ValueImage2
	body.ValueUrl2 = req.ValueUrl2
	body.ValueImage3 = req.ValueImage3
	body.ValueUrl3 = req.ValueUrl3
	if err := s.repo.UpdateCustomSalepageBlock(record.Id, body); err != nil {
		return err
	}
	return nil
}

func (s *userSalepageService) SortCustomSalepageBlock(req model.DragSortRequest, userId int64) error {

	// Check userOwner
	record1, err := s.GetCustomSalepageBlockById(model.GetByIdUserRequest{Id: req.FromItemId, UserId: userId})
	if err != nil {
		log.Panicln("SortCustomSalepageBlock.GetCustomSalepageBlockById1.ERROR= ", err)
		return badRequest("ไม่พบข้อมูล")
	}
	record2, err := s.GetCustomSalepageBlockById(model.GetByIdUserRequest{Id: req.ToItemId, UserId: userId})
	if err != nil {
		log.Panicln("SortCustomSalepageBlock.GetCustomSalepageBlockById2.ERROR= ", err)
		return badRequest("ไม่พบข้อมูล")
	}

	var body model.DragSortRequest
	body.FromItemId = record1.Id
	body.ToItemId = record2.Id
	if err := s.repo.SortCustomSalepageBlock(body); err != nil {
		return err
	}
	return nil
}

func (s *userSalepageService) UploadImageToS3CustomSalePage(imageFileBody *http.Request) (*model.FileUploadResponse, error) {

	// Set S3 path
	dbName := os.Getenv("DB_NAME")
	pathName := fmt.Sprintf("cbgame/%v/salepage/", dbName)

	fileReader, newFileName, err := imageFileBody.FormFile("file")
	if err != nil {
		log.Println("UploadImageToS3CustomSalePage.FormFile.ERROR", err)
		return nil, err
	}
	filename := &newFileName.Filename

	var newImageId *model.FileUploadResponse
	fileData, err := s.repo.UploadImageToS3(pathName, *filename, fileReader)
	if err != nil {
		log.Println("UploadImageToS3CustomSalePage.UploadImageToS3.ERROR", err)
		return nil, badRequest("UNABLE_TO_UPLOAD_FILE")
	}

	newImageId = &model.FileUploadResponse{
		ImageUrl: fileData.ImageUrl,
	}
	return newImageId, nil
}

func (s *userSalepageService) GetWebCustomSalepageBlockList(code string) ([]model.CustomSalepageBlockResponse, error) {

	var result []model.CustomSalepageBlockResponse

	user, err := s.repo.GetUserIdFromSalepageCode(code)
	if err != nil {
		log.Println("GetWebCustomSalepageBlockList.GetUserIdFromSalepageCode.ERROR= ", err)
		return result, nil
	}

	data, err := s.repo.GetCustomSalepageBlockList(model.CustomSalepageBlockListRequest{
		UserId: user,
	})
	if err != nil {
		log.Println("GetWebCustomSalepageBlockList.GetCustomSalepageBlockList.ERROR= ", err)
		return result, nil
	}
	for _, item := range data {
		if item.IsShow {
			result = append(result, model.CustomSalepageBlockResponse{
				Id:          item.Id,
				UserId:      item.UserId,
				Name:        item.Name,
				Label:       item.Label,
				SortOrder:   item.SortOrder,
				ValueName:   item.ValueName,
				ValueColor:  item.ValueColor,
				ValueImage1: item.ValueImage1,
				ValueUrl1:   item.ValueUrl1,
				ValueImage2: item.ValueImage2,
				ValueUrl2:   item.ValueUrl2,
				ValueImage3: item.ValueImage3,
				ValueUrl3:   item.ValueUrl3,
			})
		}
	}
	return result, nil
}

func (s *userSalepageService) IncreaseTodayLinkClick(req model.PostUserSalepageRequest) error {

	userId, err := s.repo.GetUserIdFromSalepageCode(req.SaleCode)
	if err != nil {
		return errors.New("ไม่พบรหัสผู้ใช้")
	}

	if err := s.repo.IncreaseTodayLinkClick(userId); err != nil {
		return err
	}
	return nil
}

func (s *userSalepageService) IncreaseTodayMemberRegisterClick(req model.PostUserSalepageRequest) error {

	userId, err := s.repo.GetUserIdFromSalepageCode(req.SaleCode)
	if err != nil {
		return errors.New("ไม่พบรหัสผู้ใช้")
	}

	if err := s.repo.IncreaseTodayMemberRegisterClick(userId); err != nil {
		return err
	}
	return nil
}

func (s *userSalepageService) IncreaseTodayAdminClick(req model.PostUserSalepageRequest) error {

	userId, err := s.repo.GetUserIdFromSalepageCode(req.SaleCode)
	if err != nil {
		return errors.New("ไม่พบรหัสผู้ใช้")
	}

	if err := s.repo.IncreaseTodayAdminClick(userId); err != nil {
		return err
	}
	return nil
}
