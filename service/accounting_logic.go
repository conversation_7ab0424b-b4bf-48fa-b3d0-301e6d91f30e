package service

import (
	"cybergame-api/model"
	"cybergame-api/repository"
)

func (s accountingService) CheckFirstMinimunDeposit(userId int64, amount float64, configWeb model.GetWebConfigurationBody) error {

	// Flow เดิม เช็คจาก MemberCode ที่ได้รับมา
	// if possibleOwner.MemberCode == "" && configWeb.MinFirstMemberDeposit > 0 {
	// 	// -FLOW ฝากครั้งแรก 100 + (ได้รับ USER ID), ฝากครั้งต่อไป 1 +
	// 	if data.Amount < float64(configWeb.MinFirstMemberDeposit) {
	// 		log.Println("CreateBankStatementFromWebhookAndAuto.LOW_NOT_MEMBER_FIRST_DEPOSIT", helper.Struct<PERSON>son(data), configWeb, possibleOwner)
	// 		return nil, badRequest("LOW_NOT_MEMBER_FIRST_DEPOSIT")
	// 	}
	// }

	return CheckFirstMinimunDeposit(s.repo, userId, amount, configWeb)
}

func CheckFirstMinimunDeposit(repo repository.AccountingRepository, userId int64, amount float64, configWeb model.GetWebConfigurationBody) error {

	// [********] ฝากครั้งแรกขั้นต่ำ ปรับ flow เช็ค user ให้เช็คจากรายการฝากครั้งแรกจริงๆ ไม่เช็คจากรหัสที่ได้รับ
	if configWeb.MinFirstMemberDeposit > 0 {
		isFirstDeposit := repo.IsFirstDeposit(userId)
		if isFirstDeposit && amount < float64(configWeb.MinFirstMemberDeposit) {
			return badRequest("MEMBER_FIRST_DEPOSIT_NOTMET_MINIMUM")
		}
	}
	return nil
}
