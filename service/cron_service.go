package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"math"
	"os"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm"
)

type CronService interface {
	CronSimpleWinLose() error
	CronSimpleWinLoseByDate(statementDate string) error
	CronCreateAllianceWinloseIncome(req model.CronAllianceIncomeCalcRequest) error
	CronRecalcAllianceWinloseIncome(req model.CronAllianceIncomeRecalcRequest) error
	ShowAllianceWinloseIncome(incomeId int64) (*model.AllianceWinloseIncome, *model.AllianceWinloseIncomeCreateBody, error)
	// MOCK+TEST
	CreateSystemLog(name string, req interface{}) error
	TestAddAllianceWinloseIncome(req model.TestAddAllianceIncomeRequest) error
	CronExpireAffiliateTransaction() error
	ViewAgcSimpleWinLoseGreen(req model.ViewAgcSimpleWinLoseListRequest) (*model.AgcSimpleWinloseResponse, error)
	ViewAgcSimpleWinLoseTidtech(req model.ViewAgcSimpleWinLoseListRequest) (*model.AgcSimpleWinloseResponse, error)
	// DELETE LOG
	CronDeleteLog() (string, error)
	// Manual Work
	CronCalculateAfAlOnly(statementDate string) (string, error)
	CronMigrateOldAff() ([]model.UserAffIncomeReponse, model.CronMigrateOldAffResponse, error)
	MigrateOldAgcPlaylogStatus() error
	// AffLevel
	MigrateAffiliateUserAllLevel(userId int64) error
	MigrateAffiliateTotalMember(refBy int64) error
	// Lottery-Playlog
	CreateLotteryPlayLogWebhook(req model.LotteryWebhookRequest) error
}

type cronService struct {
	repo   repository.AgentInfoRepository
	afRepo repository.AffiliateRepository
	alRepo repository.AllianceRepository
}

func NewCronService(
	repo repository.AgentInfoRepository,
	afRepo repository.AffiliateRepository,
	alRepo repository.AllianceRepository,
) CronService {
	return &cronService{repo, afRepo, alRepo}
}

func RacingRunSimplePlaylog(repo repository.AgentInfoRepository, agentName string, fnName string, adminId int64) (*int64, error) {

	actionAt := time.Now().UTC()
	racingKey := "SIMPLE_PLAYLOG"

	var createBody model.RaceActionCreateBody
	createBody.Name = fnName
	createBody.JsonRequest = helper.StructJson(map[string]interface{}{"agentName": agentName, "adminId": adminId})
	createBody.Status = "PENDING"

	// KEY = SIMPLE_PLAYLOG_{AGENT_NAME} = RUN ONCE PER 30 MINUTES or OLDWORK is completed
	createBody.ActionKey = fmt.Sprintf("%s_%s", racingKey, agentName)
	createBody.UnlockAt = actionAt.Add(time.Minute * 30) // Max run is around 10-15 minutes, So 30 minutes is enough
	if oldWork, err := repo.GetRaceActionByActionKey(createBody.ActionKey); err != nil {
		log.Println("RacingRunSimplePlaylog.ERROR.GetRaceActionByActionKey", err)
		if err != gorm.ErrRecordNotFound {
			return nil, internalServerError(errors.New("CANNOT_CHECK_ACTION"))
		}
		// CONTINUE..
	} else {
		if oldWork.Id > 0 && actionAt.After(oldWork.UnlockAt) {
			// UPDATE
			canceledKey := fmt.Sprintf("%s_DONE_%d", racingKey, oldWork.Id)
			canceledStatus := "TIMEOUT"
			var updateBody model.RaceActionUpdateBody
			updateBody.ActionKey = &canceledKey
			updateBody.Status = &canceledStatus
			if err := repo.UpdateRaceCondition(oldWork.Id, updateBody); err != nil {
				log.Println("RacingRunSimplePlaylog.ERROR.UpdateRaceCondition", err)
				return nil, internalServerError(errors.New("WORK_IN_ACTION"))
			}
			// CONTINUE..
		} else {
			return nil, internalServerError(errors.New("WORK_IN_ACTION"))
		}
	}

	// CONTINUE.. only not found will be created
	actionId, err := repo.CreateRaceCondition(createBody)
	if err != nil {
		log.Println("RacingRunSimplePlaylog.ERROR.CreateRaceCondition", err)
		return nil, internalServerError(errors.New("CANNOT_CREATE_ACTION"))
	}
	return &actionId, nil
}

func (s *cronService) CronSimpleWinLose() error {

	actionAt := time.Now()
	statementAt := actionAt.AddDate(0, 0, -1)
	statementDate := fmt.Sprintf("%v-%02d-%02d", statementAt.Year(), int(statementAt.Month()), statementAt.Day())
	agentProvider := os.Getenv("AGENT_PROVIDER")

	// -- GAME sport(1) casino(2) game(4) Then Add more column by lottery(3), p2p(6), Financial(7)
	// -- ** (5) ไม่มีห้ามดึง = พัง
	productIds := []int{model.AGENT_PRODUCT_SPORT, model.AGENT_PRODUCT_CASINO, model.AGENT_PRODUCT_GAME, model.AGENT_PRODUCT_LOTTERY, model.AGENT_PRODUCT_P2P, model.AGENT_PRODUCT_FINANCIAL}
	hasExternalLotteryApi := os.Getenv("LOTTERY_ENDPOINT") != "" && os.Getenv("AGENT_NAME") != ""

	// RACE_CONDITION
	racingKey := "SIMPLE_PLAYLOG"
	actionId, err := RacingRunSimplePlaylog(s.repo, os.Getenv("AGENT_NAME"), "CronSimpleWinLose", -2)
	if err != nil {
		return err
	}

	go func() {
		if agentProvider == "AMB" {
			// AMB+OLD_PLAYLOG
			createAmbSimpleWinLose(s.repo, productIds, statementDate)
			runAmbSimpleWinLose(s.repo, model.AGENT_PRODUCT_SPORT, statementDate)
			runAmbSimpleWinLose(s.repo, model.AGENT_PRODUCT_CASINO, statementDate)
			runAmbSimpleWinLose(s.repo, model.AGENT_PRODUCT_GAME, statementDate)
			runAmbSimpleWinLose(s.repo, model.AGENT_PRODUCT_LOTTERY, statementDate)
			runAmbSimpleWinLose(s.repo, model.AGENT_PRODUCT_P2P, statementDate)
			runAmbSimpleWinLose(s.repo, model.AGENT_PRODUCT_FINANCIAL, statementDate)
			// todo Last Run
			// CronCalculateAfAlOnly(s.repo, statementDate)
		} else {
			// AGC+OLD_PLAYLOG
			createAgcSimpleWinLoseStatus(s.repo, productIds, statementDate)
			runOldAgcSimpleWinLose(s.repo, model.AGENT_PRODUCT_SPORT, statementDate)
			runOldAgcSimpleWinLose(s.repo, model.AGENT_PRODUCT_CASINO, statementDate)
			runOldAgcSimpleWinLose(s.repo, model.AGENT_PRODUCT_GAME, statementDate)
			if hasExternalLotteryApi {
				runLotteryPlaylog(s.repo, statementDate)
			} else {
				runOldAgcSimpleWinLose(s.repo, model.AGENT_PRODUCT_LOTTERY, statementDate)
			}
			runOldAgcSimpleWinLose(s.repo, model.AGENT_PRODUCT_P2P, statementDate)
			runOldAgcSimpleWinLose(s.repo, model.AGENT_PRODUCT_FINANCIAL, statementDate)
			runOldPgHardToAgcSimpleWinLose(s.repo, model.AGENT_PRODUCT_GAME, statementDate)
			runOldAgentCtwToAgcSimpleWinLose(s.repo, model.AGENT_PRODUCT_GAME, statementDate)
			// AGC+USER_PLAYLOG+USER_AFF_INCOME
			runAgcSimpleWinLose(s.repo, model.AGENT_PRODUCT_SPORT, statementDate, false)
			runAgcSimpleWinLose(s.repo, model.AGENT_PRODUCT_CASINO, statementDate, false)
			runAgcSimpleWinLose(s.repo, model.AGENT_PRODUCT_GAME, statementDate, false)
			if hasExternalLotteryApi {
				runLotteryUserPlaylog(s.repo, model.AGENT_PRODUCT_LOTTERY, statementDate, false)
			} else {
				runAgcSimpleWinLose(s.repo, model.AGENT_PRODUCT_LOTTERY, statementDate, false)
			}
			runAgcSimpleWinLose(s.repo, model.AGENT_PRODUCT_P2P, statementDate, false)
			runAgcSimpleWinLose(s.repo, model.AGENT_PRODUCT_FINANCIAL, statementDate, false)
			runPgHardToAgcSimpleWinLose(s.repo, model.AGENT_PRODUCT_GAME, statementDate, true)
			runAgentCtwToAgcSimpleWinLose(s.repo, model.AGENT_PRODUCT_GAME, statementDate, true)
			// Last Run
			CronCalculateAfAlOnly(s.repo, statementDate)
		}
		// RACE_CONDITION UPDATE
		successKey := fmt.Sprintf("%s_DONE_%d", racingKey, *actionId)
		successStatus := "SUCCESS"
		var updateBody model.RaceActionUpdateBody
		updateBody.ActionKey = &successKey
		updateBody.Status = &successStatus
		if err := s.repo.UpdateRaceCondition(*actionId, updateBody); err != nil {
			log.Println("CronSimpleWinLose.ERROR.UpdateRaceCondition", err)
			log.Println("Finish CronSimpleWinLose But ERROR UpdateRaceCondition")
		}
		log.Println("Finish CronSimpleWinLose statementDate=", statementDate)
	}()

	return nil
}

func (s *cronService) CronSimpleWinLoseByDate(statementDate string) error {

	agentProvider := os.Getenv("AGENT_PROVIDER")

	// -- GAME sport(1) casino(2) game(4) Then Add more column by lottery(3), p2p(6), Financial(7)
	// -- ** (5) ไม่มีห้ามดึง = พัง
	productIds := []int{model.AGENT_PRODUCT_SPORT, model.AGENT_PRODUCT_CASINO, model.AGENT_PRODUCT_GAME, model.AGENT_PRODUCT_LOTTERY, model.AGENT_PRODUCT_P2P, model.AGENT_PRODUCT_FINANCIAL}
	hasExternalLotteryApi := os.Getenv("LOTTERY_ENDPOINT") != "" && os.Getenv("AGENT_NAME") != ""

	// RACE_CONDITION
	racingKey := "SIMPLE_PLAYLOG"
	actionId, err := RacingRunSimplePlaylog(s.repo, os.Getenv("AGENT_NAME"), "CronSimpleWinLoseByDate", -1)
	if err != nil {
		return err
	}

	go func() {
		if agentProvider == "AMB" {
			// AMB+OLD_PLAYLOG
			createAmbSimpleWinLose(s.repo, productIds, statementDate)
			runAmbSimpleWinLose(s.repo, model.AGENT_PRODUCT_SPORT, statementDate)
			runAmbSimpleWinLose(s.repo, model.AGENT_PRODUCT_CASINO, statementDate)
			runAmbSimpleWinLose(s.repo, model.AGENT_PRODUCT_GAME, statementDate)
			runAmbSimpleWinLose(s.repo, model.AGENT_PRODUCT_LOTTERY, statementDate)
			runAmbSimpleWinLose(s.repo, model.AGENT_PRODUCT_P2P, statementDate)
			runAmbSimpleWinLose(s.repo, model.AGENT_PRODUCT_FINANCIAL, statementDate)
		} else {
			// AGC+OLD_PLAYLOG
			createAgcSimpleWinLoseStatus(s.repo, productIds, statementDate)
			runOldAgcSimpleWinLose(s.repo, model.AGENT_PRODUCT_SPORT, statementDate)
			runOldAgcSimpleWinLose(s.repo, model.AGENT_PRODUCT_CASINO, statementDate)
			runOldAgcSimpleWinLose(s.repo, model.AGENT_PRODUCT_GAME, statementDate)
			if hasExternalLotteryApi {
				runLotteryPlaylog(s.repo, statementDate)
			} else {
				runOldAgcSimpleWinLose(s.repo, model.AGENT_PRODUCT_LOTTERY, statementDate)
			}
			runOldAgcSimpleWinLose(s.repo, model.AGENT_PRODUCT_P2P, statementDate)
			runOldAgcSimpleWinLose(s.repo, model.AGENT_PRODUCT_FINANCIAL, statementDate)
			runOldPgHardToAgcSimpleWinLose(s.repo, model.AGENT_PRODUCT_GAME, statementDate)
			runOldAgentCtwToAgcSimpleWinLose(s.repo, model.AGENT_PRODUCT_GAME, statementDate)
			// AGC+USER_PLAYLOG+USER_AFF_INCOME
			runAgcSimpleWinLose(s.repo, model.AGENT_PRODUCT_SPORT, statementDate, false)
			runAgcSimpleWinLose(s.repo, model.AGENT_PRODUCT_CASINO, statementDate, false)
			runAgcSimpleWinLose(s.repo, model.AGENT_PRODUCT_GAME, statementDate, false)
			if hasExternalLotteryApi {
				runLotteryUserPlaylog(s.repo, model.AGENT_PRODUCT_LOTTERY, statementDate, false)
			} else {
				runAgcSimpleWinLose(s.repo, model.AGENT_PRODUCT_LOTTERY, statementDate, false)
			}
			runAgcSimpleWinLose(s.repo, model.AGENT_PRODUCT_P2P, statementDate, false)
			runAgcSimpleWinLose(s.repo, model.AGENT_PRODUCT_FINANCIAL, statementDate, false)
			runPgHardToAgcSimpleWinLose(s.repo, model.AGENT_PRODUCT_GAME, statementDate, true)
			runAgentCtwToAgcSimpleWinLose(s.repo, model.AGENT_PRODUCT_GAME, statementDate, true)
			// Last Run
			CronCalculateAfAlOnly(s.repo, statementDate)
		}
		// RACE_CONDITION UPDATE
		successKey := fmt.Sprintf("%s_DONE_%d", racingKey, *actionId)
		successStatus := "SUCCESS"
		var updateBody model.RaceActionUpdateBody
		updateBody.ActionKey = &successKey
		updateBody.Status = &successStatus
		if err := s.repo.UpdateRaceCondition(*actionId, updateBody); err != nil {
			log.Println("CronSimpleWinLoseByDate.ERROR.UpdateRaceCondition", err)
			log.Println("Finish CronSimpleWinLoseByDate But ERROR UpdateRaceCondition")
		}
		log.Println("Finish CronSimpleWinLoseByDate statementDate=", statementDate)
	}()

	return nil
}

func CronRerunFailWinLoseByDate(repo repository.AgentInfoRepository, statementDate string, adminId int64) error {

	agentProvider := os.Getenv("AGENT_PROVIDER")

	// -- GAME sport(1) casino(2) game(4) Then Add more column by lottery(3), p2p(6), Financial(7)
	// -- ** (5) ไม่มีห้ามดึง = พัง
	productIds := []int{model.AGENT_PRODUCT_SPORT, model.AGENT_PRODUCT_CASINO, model.AGENT_PRODUCT_GAME, model.AGENT_PRODUCT_LOTTERY, model.AGENT_PRODUCT_P2P, model.AGENT_PRODUCT_FINANCIAL}
	hasExternalLotteryApi := os.Getenv("LOTTERY_ENDPOINT") != "" && os.Getenv("AGENT_NAME") != ""

	// RACE_CONDITION
	racingKey := "SIMPLE_PLAYLOG"
	actionId, err := RacingRunSimplePlaylog(repo, os.Getenv("AGENT_NAME"), "CronRerunFailWinLoseByDate", adminId)
	if err != nil {
		return err
	}

	// RESET fail DB
	failCount := 0
	// failCount, err := repo.AgcCountFailApiStatus(statementDate)
	// if err != nil {
	// 	// RACE_CONDITION UPDATE
	// 	successKey := fmt.Sprintf("%s_DONE_%d", racingKey, *actionId)
	// 	successStatus := "FAILED"
	// 	var updateBody model.RaceActionUpdateBody
	// 	updateBody.ActionKey = &successKey
	// 	updateBody.Status = &successStatus
	// 	if err := repo.UpdateRaceCondition(*actionId, updateBody); err != nil {
	// 		log.Println("CronRerunFailWinLoseByDate.ERROR.UpdateRaceCondition", err)
	// 		log.Println("Finish CronRerunFailWinLoseByDate But ERROR UpdateRaceCondition")
	// 	}
	// 	log.Println("ERROR CronRerunFailWinLoseByDate statementDate=", statementDate)
	// 	return err
	// }
	// if failCount == 0 {
	// 	// RACE_CONDITION UPDATE
	// 	successKey := fmt.Sprintf("%s_DONE_%d", racingKey, *actionId)
	// 	successStatus := "SUCCESS"
	// 	var updateBody model.RaceActionUpdateBody
	// 	updateBody.ActionKey = &successKey
	// 	updateBody.Status = &successStatus
	// 	if err := repo.UpdateRaceCondition(*actionId, updateBody); err != nil {
	// 		log.Println("CronRerunFailWinLoseByDate.ERROR.UpdateRaceCondition", err)
	// 		log.Println("Finish CronRerunFailWinLoseByDate But ERROR UpdateRaceCondition")
	// 	}
	// 	log.Println("NO FAIL CronRerunFailWinLoseByDate statementDate=", statementDate)
	// 	return nil
	// }

	playlogStatus, err := repo.GetAgcPlaylogStatus(statementDate)
	if err != nil {
		log.Println("CronRerunFailWinLoseByDate.ERROR.GetAgcPlaylogStatus", err)
		return err
	}
	if playlogStatus.PlaylogSportStatus != "DONE" || playlogStatus.PlaylogCasinoStatus != "DONE" || playlogStatus.PlaylogGameStatus != "DONE" {
		failCount++
	}
	if playlogStatus.PlaylogLotteryStatus != "DONE" || playlogStatus.PlaylogP2pStatus != "DONE" || playlogStatus.PlaylogFinancialStatus != "DONE" {
		failCount++
	}
	if failCount > 0 {
		if err := repo.AgcResetFailApiStatus(statementDate); err != nil {
			return err
		}
	}

	go func() {
		if failCount > 0 {
			if agentProvider == "AMB" {
				// AMB+OLD_PLAYLOG
				createAmbSimpleWinLose(repo, productIds, statementDate)
				runAmbSimpleWinLose(repo, model.AGENT_PRODUCT_SPORT, statementDate)
				runAmbSimpleWinLose(repo, model.AGENT_PRODUCT_CASINO, statementDate)
				runAmbSimpleWinLose(repo, model.AGENT_PRODUCT_GAME, statementDate)
				runAmbSimpleWinLose(repo, model.AGENT_PRODUCT_LOTTERY, statementDate)
				runAmbSimpleWinLose(repo, model.AGENT_PRODUCT_P2P, statementDate)
				runAmbSimpleWinLose(repo, model.AGENT_PRODUCT_FINANCIAL, statementDate)
			} else {
				// AGC+OLD_PLAYLOG
				createAgcSimpleWinLoseStatus(repo, productIds, statementDate)
				runOldAgcSimpleWinLose(repo, model.AGENT_PRODUCT_SPORT, statementDate)
				runOldAgcSimpleWinLose(repo, model.AGENT_PRODUCT_CASINO, statementDate)
				runOldAgcSimpleWinLose(repo, model.AGENT_PRODUCT_GAME, statementDate)
				if hasExternalLotteryApi {
					runLotteryPlaylog(repo, statementDate)
				} else {
					runOldAgcSimpleWinLose(repo, model.AGENT_PRODUCT_LOTTERY, statementDate)
				}
				runOldAgcSimpleWinLose(repo, model.AGENT_PRODUCT_P2P, statementDate)
				runOldAgcSimpleWinLose(repo, model.AGENT_PRODUCT_FINANCIAL, statementDate)
				runOldPgHardToAgcSimpleWinLose(repo, model.AGENT_PRODUCT_GAME, statementDate)
				runOldAgentCtwToAgcSimpleWinLose(repo, model.AGENT_PRODUCT_GAME, statementDate)
				// AGC+USER_PLAYLOG+USER_AFF_INCOME
				runAgcSimpleWinLose(repo, model.AGENT_PRODUCT_SPORT, statementDate, false)
				runAgcSimpleWinLose(repo, model.AGENT_PRODUCT_CASINO, statementDate, false)
				runAgcSimpleWinLose(repo, model.AGENT_PRODUCT_GAME, statementDate, false)
				if hasExternalLotteryApi {
					runLotteryUserPlaylog(repo, model.AGENT_PRODUCT_LOTTERY, statementDate, false)
				} else {
					runAgcSimpleWinLose(repo, model.AGENT_PRODUCT_LOTTERY, statementDate, false)
				}
				runAgcSimpleWinLose(repo, model.AGENT_PRODUCT_P2P, statementDate, false)
				runAgcSimpleWinLose(repo, model.AGENT_PRODUCT_FINANCIAL, statementDate, false)
				runPgHardToAgcSimpleWinLose(repo, model.AGENT_PRODUCT_GAME, statementDate, true)
				runAgentCtwToAgcSimpleWinLose(repo, model.AGENT_PRODUCT_GAME, statementDate, true)
			}
		}

		// Last Run
		if playlogStatus.CutDailyAffiliateStatus != "DONE" {
			if err := calculateAf(repo, statementDate); err != nil {
				log.Println("calculateAf.ERROR", err)
			}
		}
		if playlogStatus.CutDailyAllianceStatus != "DONE" {
			if err := calculateAl(repo, statementDate); err != nil {
				log.Println("calculateAl.ERROR", err)
			}
		}

		if playlogStatus.PromotionReturnLossStatus != "DONE" {
			// PROMOTION_RETURN_LOSS - This may only CREATE
			repoPromotionReturnLoss := repository.NewPromotionReturnRepository(repo.GetDb())
			if err := CronCutReturnLossByStatementDate(repoPromotionReturnLoss, statementDate); err != nil {
				log.Println("CronCutReturnLossByDate.ERROR", err)
				if err := repo.UpdateAgcCronCalcStatus("PROMOTION_RETURN_LOSS", statementDate, "ERROR"); err != nil {
					log.Println(err)
				}
			}
			// CALC Again
			if err := CronCutReturnLossByStatementDate(repoPromotionReturnLoss, statementDate); err != nil {
				log.Println("CronCutReturnLossByDate.ERROR", err)
				if err := repo.UpdateAgcCronCalcStatus("PROMOTION_RETURN_LOSS", statementDate, "ERROR"); err != nil {
					log.Println(err)
				}
			}
			// PROMOTION_RETURN_TURN - This may only CREATE
			repoPromotionReturnTurn := repository.NewPromotionReturnTurnRepository(repo.GetDb())
			if err := CronCutReturnTurnByStatementDate(repoPromotionReturnTurn, statementDate); err != nil {
				log.Println("CronCutReturnLossByDate.ERROR", err)
				if err := repo.UpdateAgcCronCalcStatus("PROMOTION_RETURN_LOSS", statementDate, "ERROR"); err != nil {
					log.Println(err)
				}
			}
			// CALC Again
			if err := CronCutReturnTurnByStatementDate(repoPromotionReturnTurn, statementDate); err != nil {
				log.Println("CronCutReturnLossByDate.ERROR", err)
				if err := repo.UpdateAgcCronCalcStatus("PROMOTION_RETURN_LOSS", statementDate, "ERROR"); err != nil {
					log.Println(err)
				}
			}
		}

		// RACE_CONDITION UPDATE
		successKey := fmt.Sprintf("%s_DONE_%d", racingKey, *actionId)
		successStatus := "SUCCESS"
		var updateBody model.RaceActionUpdateBody
		updateBody.ActionKey = &successKey
		updateBody.Status = &successStatus
		if err := repo.UpdateRaceCondition(*actionId, updateBody); err != nil {
			log.Println("CronRerunFailWinLoseByDate.ERROR.UpdateRaceCondition", err)
			log.Println("Finish CronRerunFailWinLoseByDate But ERROR UpdateRaceCondition")
		}
		log.Println("Finish CronRerunFailWinLoseByDate statementDate=", statementDate)
	}()

	return nil
}

func createAgcSimpleWinLoseStatus(repo repository.AgentInfoRepository, productIds []int, statementDate string) {

	log.Printf("[%v] createAgcSimpleWinLose : %v", statementDate, len(productIds))

	if err := repo.InsertAgcPlaylogStatus(helper.StructJson(productIds), statementDate); err != nil {
		log.Println("createAgcSimpleWinLose.InsertAgcPlaylogStatus ERROR=", err)
	}

	for _, productId := range productIds {
		var logQuery model.ApiStatusRequest
		logQuery.Path = fmt.Sprintf("simplewinlose%v", productId)
		logQuery.StatementDate = statementDate
		// INSERT IF NOT EXIST
		if _, err := repo.AgcGetApistatus(logQuery); err != nil {
			if err.Error() == "record not found" {
				if err := repo.InsertAgcApiStatus(logQuery.Path, logQuery.StatementDate); err != nil {
					log.Println(err)
					return
				}
			} else {
				log.Println(err)
			}
		}
	}

	// ADD [PGHARD KEY]
	var logQuery model.ApiStatusRequest
	logQuery.Path = fmt.Sprintf("simplewinlosePGHard%v", model.AGENT_PRODUCT_GAME)
	logQuery.StatementDate = statementDate
	// INSERT IF NOT EXIST
	if _, err := repo.AgcGetApistatus(logQuery); err != nil {
		if err.Error() == "record not found" {
			if err := repo.InsertAgcApiStatus(logQuery.Path, logQuery.StatementDate); err != nil {
				log.Println(err)
				return
			}
		} else {
			log.Println(err)
		}
	}

	log.Println("Finish createAgcSimpleWinLose")
}

func runOldAgcSimpleWinLose(repo repository.AgentInfoRepository, productId int, statementDate string) {

	actionAt := time.Now()
	path := fmt.Sprintf("simplewinlose%v", productId)
	log.Printf("Running runOldAgcSimpleWinLose %v date %v", path, statementDate)

	agentName := os.Getenv("AGENT_NAME")
	body := model.AgcSimpleWinlose{}
	body.StartDate = statementDate
	body.EndDate = statementDate
	body.AgentName = agentName
	body.MemberName = agentName
	body.Products = []int{productId}
	body.PageSize = 250
	// body.PageIndex = 1
	body.TimeStamp = int(actionAt.Unix())
	body.Sign = helper.CreateSign(os.Getenv("AGENT_KEY"), agentName, actionAt)

	var logQuery model.ApiStatusRequest
	logQuery.Path = path
	logQuery.StatementDate = statementDate
	apiStatus, err := repo.AgcGetApistatus(logQuery)
	if err != nil {
		log.Println(err)
		return
	}
	if apiStatus.IsFailed == 1 || apiStatus.IsSuccess == 1 {
		log.Println("runOldAgcSimpleWinLose ", path, " Already run.")
		return
	}

	isHasError := false
	var logs []model.AgentPlayLog
	members := []string{}
	for i := 0; ; i++ {

		body.PageIndex = i + 1

		time.Sleep(5 * time.Second)
		list, err := repo.AgcSimpleWinLose(body)
		if err != nil {
			isHasError = true
			if err2 := repo.AgcUpdateFailed(apiStatus.Id, i); err2 != nil {
				log.Println(err2)
			}
			log.Println(err)
			break
		}

		if list.Error != nil && list.Error.Code != 0 {
			// [2024-09-09] ดึงต่อ ถ้าเป็น request frequency limit - TULA
			if list.Error.Code == -15 {
				// request frequency limit is 3 seconds (-15)
				log.Println("runOldAgcSimpleWinLose.List.ERROR", list.Error, ", RETRY PAGE=", body.PageIndex)
				i--
				time.Sleep(3 * time.Second)
				continue
			} else {
				log.Println("runOldAgcSimpleWinLose.List.ERROR", list.Error)
				isHasError = true
				log.Println(list.Error.Code, list.Error.Message)
				if err := repo.AgcUpdateFailed(apiStatus.Id, i); err != nil {
					log.Println(err)
				}
				break
			}
		}

		if len(list.Result.Records) <= 0 {
			log.Println("ZERO==GOTALL")
			break
		}

		var obj model.AgentPlayLog
		obj.Date = body.StartDate
		for _, j := range list.Result.Records {
			//reset
			obj.TurnSport = 0
			obj.WinLoseSport = 0
			obj.TurnCasino = 0
			obj.WinLoseCasino = 0
			obj.TurnGame = 0
			obj.WinLoseGame = 0

			obj.TurnLottery = 0
			obj.WinLoseLottery = 0
			obj.TurnP2p = 0
			obj.WinLoseP2p = 0
			obj.TurnFinancial = 0
			obj.WinLoseFinancial = 0

			obj.ValidAmountSport = 0
			obj.ValidAmountCasino = 0
			obj.ValidAmountGame = 0
			obj.ValidAmountLottery = 0
			obj.ValidAmountP2p = 0
			obj.ValidAmountFinancial = 0

			obj.TurnTotal = 0
			obj.WinLoseTotal = 0
			obj.ValidAmountTotal = 0

			members = append(members, j.UserName)
			obj.Player = j.UserName

			if productId == model.AGENT_PRODUCT_SPORT {
				obj.TurnSport = j.TurnOver
				obj.WinLoseSport = j.Payout
				obj.ValidAmountSport = j.ValidAmount
			} else if productId == model.AGENT_PRODUCT_CASINO {
				obj.TurnCasino = j.TurnOver
				obj.WinLoseCasino = j.Payout
				obj.ValidAmountCasino = j.ValidAmount
			} else if productId == model.AGENT_PRODUCT_GAME {
				obj.TurnGame = j.TurnOver
				obj.WinLoseGame = j.Payout
				obj.ValidAmountGame = j.ValidAmount
			} else if productId == model.AGENT_PRODUCT_LOTTERY {
				obj.TurnLottery = j.TurnOver
				obj.WinLoseLottery = j.Payout
				obj.ValidAmountLottery = j.ValidAmount
			} else if productId == model.AGENT_PRODUCT_P2P {
				obj.TurnP2p = j.TurnOver
				obj.WinLoseP2p = j.Payout
				obj.ValidAmountP2p = j.ValidAmount
			} else if productId == model.AGENT_PRODUCT_FINANCIAL {
				obj.TurnFinancial = j.TurnOver
				obj.WinLoseFinancial = j.Payout
				obj.ValidAmountFinancial = j.ValidAmount
			}

			obj.TurnTotal = obj.TurnSport + obj.TurnCasino + obj.TurnGame + obj.TurnLottery + obj.TurnP2p + obj.TurnFinancial
			obj.WinLoseTotal = obj.WinLoseSport + obj.WinLoseCasino + obj.WinLoseGame + obj.WinLoseLottery + obj.WinLoseP2p + obj.WinLoseFinancial
			obj.ValidAmountTotal = obj.ValidAmountSport + obj.ValidAmountCasino + obj.ValidAmountGame + obj.ValidAmountLottery + obj.ValidAmountP2p + obj.ValidAmountFinancial
			logs = append(logs, obj)
		}

		// Bulk insert Every Page.
		if len(members) > 0 {
			userList, err := repo.GetUserIdByMemberList(members)
			if err != nil {
				log.Println(err)
			}
			if len(members) > 0 {
				for i, v := range logs {
					for _, j := range userList {
						if v.Player == j.MemberCode {
							logs[i].UserID = j.Id
							break
						}
					}
				}
				if err = repo.InsertAgcPlayLog(logs, path, body.PageIndex, true); err != nil {
					isHasError = true
					if err := repo.AgcUpdateFailed(apiStatus.Id, apiStatus.Page); err != nil {
						log.Println(err)
					}
				}
			}
			// RESET
			logs = []model.AgentPlayLog{}
			members = []string{}
		}
	}

	if len(members) > 0 {
		userList, err := repo.GetUserIdByMemberList(members)
		if err != nil {
			log.Println(err)
		}
		if len(members) > 0 {
			for i, v := range logs {
				for _, j := range userList {
					if v.Player == j.MemberCode {
						logs[i].UserID = j.Id
						break
					}
				}
			}
			if err = repo.InsertAgcPlayLog(logs, path, body.PageIndex, true); err != nil {
				isHasError = true
				if err := repo.AgcUpdateFailed(apiStatus.Id, apiStatus.Page); err != nil {
					log.Println(err)
				}
			}
		}
	}

	time.Sleep(5 * time.Second)
	if !isHasError {
		if err := repo.AgcUpdateSuccess(apiStatus.Id); err != nil {
			log.Println(err)
		}
		// if err := s.repo.UpdateAgcPlaylogStatus(productId, statementDate, "DONE"); err != nil {
		// 	log.Println(err)
		// }
	}

	log.Println("Finish runOldAgcSimpleWinLose : ", path)
}

func runOldPgHardToAgcSimpleWinLose(repo repository.AgentInfoRepository, productId int, statementDate string) {

	path := fmt.Sprintf("simplewinlosePGHard%v", productId)
	log.Printf("Running simplewinlosePGHard %v date %v", path, statementDate)

	var logQuery model.ApiStatusRequest
	logQuery.Path = path
	logQuery.StatementDate = statementDate
	apiStatus, err := repo.AgcGetApistatus(logQuery)
	if err != nil {
		log.Println(err)
		return
	}
	if apiStatus.IsFailed == 1 || apiStatus.IsSuccess == 1 {
		log.Println("simplewinlosePGHard ", path, " Already run.")
		return
	}

	isHasError := false
	var logs []model.AgentPlayLog
	members := []string{}
	var body model.AgentPgHardCallbackSummaryRequest
	body.StatementDate = statementDate
	body.PageSize = 250

	for i := 0; ; i++ {

		body.PageIndex = i + 1
		time.Sleep(5 * time.Second)
		list, err := repo.GetAgentPgHardCallback(body)
		if err != nil {
			isHasError = true
			if err2 := repo.AgcUpdateFailed(apiStatus.Id, i); err2 != nil {
				log.Println(err2)
			}
			log.Println(err)
			break
		}
		// type AgentPgHardCallbackSummary struct {
		// 	UserID      int64   `json:"userId"`
		// 	MemberCode  string  `json:"memberCode"`
		// 	TotalPayoff float64 `json:"totalPayoff"`
		// 	TotalBet    float64 `json:"totalBet"`
		// }

		if len(list) <= 0 {
			log.Println("ZERO==GOTALL")
			break
		}

		var obj model.AgentPlayLog
		obj.Date = statementDate
		for _, j := range list {
			//reset
			obj.TurnSport = 0
			obj.WinLoseSport = 0
			obj.TurnCasino = 0
			obj.WinLoseCasino = 0
			obj.TurnGame = 0
			obj.WinLoseGame = 0

			obj.TurnLottery = 0
			obj.WinLoseLottery = 0
			obj.TurnP2p = 0
			obj.WinLoseP2p = 0
			obj.TurnFinancial = 0
			obj.WinLoseFinancial = 0

			obj.ValidAmountSport = 0
			obj.ValidAmountCasino = 0
			obj.ValidAmountGame = 0
			obj.ValidAmountLottery = 0
			obj.ValidAmountP2p = 0
			obj.ValidAmountFinancial = 0

			obj.TurnTotal = 0
			obj.WinLoseTotal = 0
			obj.ValidAmountTotal = 0

			members = append(members, j.MemberCode)
			obj.Player = j.MemberCode
			if productId == model.AGENT_PRODUCT_GAME {
				obj.TurnGame = j.TotalBet
				obj.WinLoseGame = j.TotalWinlose
				obj.ValidAmountGame = j.TotalBet
			}

			obj.TurnTotal = obj.TurnSport + obj.TurnCasino + obj.TurnGame + obj.TurnLottery + obj.TurnP2p + obj.TurnFinancial
			obj.WinLoseTotal = obj.WinLoseSport + obj.WinLoseCasino + obj.WinLoseGame + obj.WinLoseLottery + obj.WinLoseP2p + obj.WinLoseFinancial
			obj.ValidAmountTotal = obj.ValidAmountSport + obj.ValidAmountCasino + obj.ValidAmountGame + obj.ValidAmountLottery + obj.ValidAmountP2p + obj.ValidAmountFinancial
			logs = append(logs, obj)
		}

		// Bulk insert Every Page.
		if len(members) > 0 {
			userList, err := repo.GetUserIdByMemberList(members)
			if err != nil {
				log.Println(err)
			}
			if len(members) > 0 {
				for i, v := range logs {
					for _, j := range userList {
						if v.Player == j.MemberCode {
							logs[i].UserID = j.Id
							break
						}
					}
				}
				if err = repo.InsertAgcPlayLog(logs, path, body.PageIndex, true); err != nil {
					isHasError = true
					if err := repo.AgcUpdateFailed(apiStatus.Id, apiStatus.Page); err != nil {
						log.Println(err)
					}
				}
			}
			// RESET
			logs = []model.AgentPlayLog{}
			members = []string{}
		}
	}

	if len(members) > 0 {
		userList, err := repo.GetUserIdByMemberList(members)
		if err != nil {
			log.Println(err)
		}
		if len(members) > 0 {
			for i, v := range logs {
				for _, j := range userList {
					if v.Player == j.MemberCode {
						logs[i].UserID = j.Id
						break
					}
				}
			}
			if err = repo.InsertAgcPlayLog(logs, path, body.PageIndex, true); err != nil {
				isHasError = true
				if err := repo.AgcUpdateFailed(apiStatus.Id, apiStatus.Page); err != nil {
					log.Println(err)
				}
			}
		}
	}

	time.Sleep(5 * time.Second)
	if !isHasError {
		if err := repo.AgcUpdateSuccess(apiStatus.Id); err != nil {
			log.Println(err)
		}
		// if err := s.repo.UpdateAgcPlaylogStatus(productId, statementDate, "DONE"); err != nil {
		// 	log.Println(err)
		// }
	}

	log.Println("Finish runOldAgcSimpleWinLose : ", path)
}

func (s *cronService) ViewAgcSimpleWinLoseGreen(req model.ViewAgcSimpleWinLoseListRequest) (*model.AgcSimpleWinloseResponse, error) {

	actionAt := time.Now()
	// -- GAME sport(1) casino(2) game(4) Then Add more column by lottery(3), p2p(6), Financial(7)
	// -- ** (5) ไม่มีห้ามดึง = พัง
	productIds := []int{model.AGENT_PRODUCT_SPORT, model.AGENT_PRODUCT_CASINO, model.AGENT_PRODUCT_GAME, model.AGENT_PRODUCT_LOTTERY, model.AGENT_PRODUCT_P2P, model.AGENT_PRODUCT_FINANCIAL}
	hasExternalLotteryApi := os.Getenv("LOTTERY_ENDPOINT") != "" && os.Getenv("AGENT_NAME") != ""

	if hasExternalLotteryApi {
		body := model.LotteryPlaylogRequest{}
		body.Date = req.StatementDate
		body.Size = req.PageSize
		// AGENT start at 1, LOTTERY start at 0
		body.Page = req.PageIndex
		list, err := s.repo.LotterySimpleWinLose(body)
		if err != nil {
			log.Println(err)
		}
		var response model.AgcSimpleWinloseResponse
		for _, v := range list.Content {
			var obj model.SimpleWinloseRecord
			obj.UserName = fmt.Sprintf("%d", v.UserId)
			obj.TurnOver = v.TurnOver
			obj.Payout = v.WinLoss
			obj.ValidAmount = v.ValidAmount
			response.Result.Records = append(response.Result.Records, obj)
		}
		return &response, nil
	}

	body := model.AgcSimpleWinlose{}
	body.StartDate = req.StatementDate
	body.EndDate = req.StatementDate
	body.AgentName = req.AgentName
	body.MemberName = req.MemberCode
	body.Products = productIds
	body.PageSize = req.PageSize
	body.PageIndex = req.PageIndex
	body.TimeStamp = int(actionAt.Unix())
	body.Sign = helper.CreateSign(req.AgentKey, req.AgentName, actionAt)

	list, err := s.repo.AgcSimpleWinLose(body)
	if err != nil {
		log.Println(err)
	}
	return list, nil
}

func (s *cronService) ViewAgcSimpleWinLoseTidtech(req model.ViewAgcSimpleWinLoseListRequest) (*model.AgcSimpleWinloseResponse, error) {

	actionAt := time.Now()
	// -- GAME sport(1) casino(2) game(4) Then Add more column by lottery(3), p2p(6), Financial(7)
	// -- ** (5) ไม่มีห้ามดึง = พัง
	productIds := []int{model.AGENT_PRODUCT_SPORT, model.AGENT_PRODUCT_CASINO, model.AGENT_PRODUCT_GAME, model.AGENT_PRODUCT_LOTTERY, model.AGENT_PRODUCT_P2P, model.AGENT_PRODUCT_FINANCIAL}
	hasExternalLotteryApi := os.Getenv("LOTTERY_ENDPOINT") != "" && os.Getenv("AGENT_NAME") != ""

	if hasExternalLotteryApi {
		body := model.LotteryPlaylogRequest{}
		body.Date = req.StatementDate
		body.Size = req.PageSize
		// AGENT start at 1, LOTTERY start at 0
		body.Page = req.PageIndex
		list, err := s.repo.LotterySimpleWinLose(body)
		if err != nil {
			log.Println(err)
		}
		var response model.AgcSimpleWinloseResponse
		for _, v := range list.Content {
			var obj model.SimpleWinloseRecord
			obj.UserName = fmt.Sprintf("%d", v.UserId)
			obj.TurnOver = v.TurnOver
			obj.Payout = v.WinLoss
			obj.ValidAmount = v.ValidAmount
			response.Result.Records = append(response.Result.Records, obj)
		}
		return &response, nil
	}

	body := model.AgcSimpleWinlose{}
	body.StartDate = req.StatementDate
	body.EndDate = req.StatementDate
	body.AgentName = req.AgentName
	body.MemberName = req.MemberCode
	body.Products = productIds
	body.PageSize = req.PageSize
	body.PageIndex = req.PageIndex
	body.TimeStamp = int(actionAt.Unix())
	body.Sign = helper.CreateSign(req.AgentKey, req.AgentName, actionAt)
	list, err := s.repo.AgcSimpleWinLoseTidtech(body)
	if err != nil {
		log.Println(err)
	}

	return list, nil
}

func createAmbSimpleWinLose(repo repository.AgentInfoRepository, productIds []int, statementDate string) {

	log.Printf("[%v] createAmbSimpleWinLose : %v", statementDate, len(productIds))

	for _, productId := range productIds {
		var logQuery model.ApiStatusRequest
		logQuery.Path = fmt.Sprintf("simplewinlose%v", productId)
		logQuery.StatementDate = statementDate
		// INSERT IF NOT EXIST
		if _, err := repo.AgcGetApistatus(logQuery); err != nil {
			if err.Error() == "record not found" {
				if err := repo.InsertAgcApiStatus(logQuery.Path, logQuery.StatementDate); err != nil {
					log.Println(err)
					return
				}
			} else {
				log.Println(err)
			}
		}
	}
	log.Println("Finish createAmbSimpleWinLose")
}

func runAgcSimpleWinLose(repo repository.AgentInfoRepository, productId int, statementDate string, force bool) {

	actionAt := time.Now()
	path := fmt.Sprintf("simplewinlose%v", productId)
	completedPath := fmt.Sprintf("completed_%v", productId)
	var cronError error
	log.Printf("Running runAgcSimpleWinLose-%v date %v", path, statementDate)

	// RUN AFTER 12.30
	bbkLoc := time.FixedZone("Asia/Bangkok", 7*60*60)
	runTime := actionAt.In(bbkLoc)
	if runTime.Hour() < 12 && runTime.Minute() < 30 {
		return
	}

	// get completed run
	completedStatus, err := repo.GetUserPlaylogCompletedStatus(completedPath, statementDate)
	if err != nil {
		if err.Error() != "record not found" {
			log.Println(err)
			return
		}
	}
	if completedStatus != nil {
		if err := repo.UpdateAgcPlaylogStatus(productId, statementDate, "DONE"); err != nil {
			log.Println(err)
		}
	}
	if completedStatus != nil && !force {
		// log.Printf(path + " was Completed")
		log.Printf("runAgcSimpleWinLose-%v was already Completed", path)
		return
	}

	agentName := os.Getenv("AGENT_NAME")
	body := model.AgcSimpleWinlose{}
	body.StartDate = statementDate
	body.EndDate = statementDate
	body.AgentName = agentName
	body.MemberName = agentName
	body.Products = []int{productId}
	body.PageSize = 250
	// body.PageIndex = 1
	body.TimeStamp = int(actionAt.Unix())
	body.Sign = helper.CreateSign(os.Getenv("AGENT_KEY"), agentName, actionAt)

	// var logQuery model.ApiStatusRequest
	// logQuery.Path = path
	// logQuery.StatementDate = statementDate
	// apiStatus, err := s.repo.GetApistatus(logQuery)
	// if err != nil {
	// 	log.Println(err)
	// 	return
	// }
	// if apiStatus.IsFailed == 1 || apiStatus.IsSuccess == 1 {
	// 	log.Printf("apiStatus was Completed")
	// 	return
	// }

	isHasError := false
	var createList = make(map[string]model.UserPlaylogCreateBody, 0)
	var uniqueList []string
	var memberCodeList []string
	var totalCreated int64
	for i := 0; ; i++ {

		body.PageIndex = i + 1

		time.Sleep(5 * time.Second)
		list, err := repo.AgcSimpleWinLose(body)
		// INSERT AGENT LOG FIRST !
		if errOnAgcSimpleWinLose := repo.CreateUserPlaylogStatus(model.UserPlaylogStatusCreateBody{
			StatementDate:  statementDate,
			Path:           path,
			Page:           body.PageIndex,
			OutMessage:     list.Message,
			OutTotal:       int64(list.Result.Total),
			OutJsonError:   helper.StructJson(list.Error),
			OutJsonSummary: helper.StructJson(list.Result.Summary),
			OutTargetUrl:   helper.StructJson(list.TargetUrl),
		}); errOnAgcSimpleWinLose != nil {
			log.Println(errOnAgcSimpleWinLose)
			cronError = errOnAgcSimpleWinLose
		}
		if err != nil {
			cronError = err
			log.Println(err)
			break
		}

		if list.Error != nil && list.Error.Code != 0 {
			// [2024-09-09] ดึงต่อ ถ้าเป็น request frequency limit - TULA
			if list.Error.Code == -15 {
				// request frequency limit is 3 seconds (-15)
				log.Println("runOldAgcSimpleWinLose.List.ERROR", list.Error, ", RETRY PAGE=", body.PageIndex)
				i--
				time.Sleep(3 * time.Second)
				continue
			} else {
				log.Println("runOldAgcSimpleWinLose.List.ERROR", list.Error)
				cronError = err
				break
			}
		}

		if len(list.Result.Records) <= 0 {
			log.Println("ZERO==GOTALL")
			break
		}

		for _, j := range list.Result.Records {

			var tempRow model.UserPlaylogCreateBody
			// DAILY_KEY = {memberCode}_{statementDate}_{productId}
			uniqueKey := fmt.Sprintf("%v_%v_%v", j.UserName, strings.Replace(statementDate, "-", "", -1), productId)
			tempRow.StatementDate = body.StartDate
			tempRow.DailyKey = uniqueKey

			memberCodeList = append(memberCodeList, j.UserName)
			tempRow.MemberCode = j.UserName

			if productId == model.AGENT_PRODUCT_SPORT {
				tempRow.TurnSport = j.TurnOver
				tempRow.WinLoseSport = j.Payout
				tempRow.ValidAmountSport = j.ValidAmount
			} else if productId == model.AGENT_PRODUCT_CASINO {
				tempRow.TurnCasino = j.TurnOver
				tempRow.WinLoseCasino = j.Payout
				tempRow.ValidAmountCasino = j.ValidAmount
			} else if productId == model.AGENT_PRODUCT_GAME {
				tempRow.TurnGame = j.TurnOver
				tempRow.WinLoseGame = j.Payout
				tempRow.ValidAmountGame = j.ValidAmount
			} else if productId == model.AGENT_PRODUCT_LOTTERY {
				tempRow.TurnLottery = j.TurnOver
				tempRow.WinLoseLottery = j.Payout
				tempRow.ValidAmountLottery = j.ValidAmount
			} else if productId == model.AGENT_PRODUCT_P2P {
				tempRow.TurnP2p = j.TurnOver
				tempRow.WinLoseP2p = j.Payout
				tempRow.ValidAmountP2p = j.ValidAmount
			} else if productId == model.AGENT_PRODUCT_FINANCIAL {
				tempRow.TurnFinancial = j.TurnOver
				tempRow.WinLoseFinancial = j.Payout
				tempRow.ValidAmountFinancial = j.ValidAmount
			}

			tempRow.TurnTotal = tempRow.TurnSport + tempRow.TurnCasino + tempRow.TurnGame + tempRow.TurnLottery + tempRow.TurnP2p + tempRow.TurnFinancial
			tempRow.WinLoseTotal = tempRow.WinLoseSport + tempRow.WinLoseCasino + tempRow.WinLoseGame + tempRow.WinLoseLottery + tempRow.WinLoseP2p + tempRow.WinLoseFinancial
			tempRow.ValidAmountTotal = tempRow.ValidAmountSport + tempRow.ValidAmountCasino + tempRow.ValidAmountGame + tempRow.ValidAmountLottery + tempRow.ValidAmountP2p + tempRow.ValidAmountFinancial
			createList[uniqueKey] = tempRow

			// Bulk insert
			if len(createList) >= 250 {
				// check exists
				for k := range createList {
					uniqueList = append(uniqueList, k)
				}
				if dbList, _, err := repo.GetPlayLogKeyList(uniqueList); err != nil {
					cronError = err // cant check
				} else {
					// if exists, remove from createList
					for _, dbKey := range dbList {
						delete(createList, dbKey)
					}
					if len(createList) > 0 {
						if err := repo.CreateUserPlaylogBulk(createList, memberCodeList); err != nil {
							log.Println(err)
							cronError = err
							continue
						}
						if err := repo.IncreaseUserTotalTurnForUserTier(createList, memberCodeList); err != nil {
							log.Println(err)
						}
						totalCreated += int64(len(createList))
					}
				}
				createList = make(map[string]model.UserPlaylogCreateBody, 0)
				uniqueList = make([]string, 0)
				memberCodeList = make([]string, 0)
			}
		}
	}

	// LEFTOVER
	if len(createList) > 0 {
		// check exists
		for k := range createList {
			uniqueList = append(uniqueList, k)
		}
		if dbList, _, err := repo.GetPlayLogKeyList(uniqueList); err != nil {
			cronError = err // cant check
		} else {
			// if exists, remove from createList
			for _, dbKey := range dbList {
				delete(createList, dbKey)
			}
			if len(createList) > 0 {
				if err := repo.CreateUserPlaylogBulk(createList, memberCodeList); err != nil {
					log.Println(err)
				}
				if err := repo.IncreaseUserTotalTurnForUserTier(createList, memberCodeList); err != nil {
					log.Println(err)
				}
				totalCreated += int64(len(createList))
			}
		}
	}

	if !isHasError {
		// INSERT AGENT LOG FIRST !
		if err := repo.CreateUserPlaylogStatus(model.UserPlaylogStatusCreateBody{
			StatementDate: statementDate,
			Path:          completedPath,
			Page:          body.PageIndex,
			OutTotal:      totalCreated,
			OutJsonError:  helper.StructJson(cronError),
		}); err != nil {
			log.Println(err)
		}
		if err := repo.UpdateAgcPlaylogStatus(productId, statementDate, "DONE"); err != nil {
			log.Println(err)
		}
	} else {
		if err := repo.UpdateAgcPlaylogStatus(productId, statementDate, "ERROR"); err != nil {
			log.Println(err)
		}
	}

	log.Println("Finish runAgcSimpleWinLose : ", path)
}

func runPgHardToAgcSimpleWinLose(repo repository.AgentInfoRepository, productId int, statementDate string, force bool) {

	actionAt := time.Now()
	path := fmt.Sprintf("simplewinlosePGHard%v", productId)
	completedPath := fmt.Sprintf("completed_%v", productId)
	var cronError error
	log.Printf("Running runPgHardToAgcSimpleWinLose-%v date %v", path, statementDate)

	// RUN AFTER 12.30
	bbkLoc := time.FixedZone("Asia/Bangkok", 7*60*60)
	runTime := actionAt.In(bbkLoc)
	if runTime.Hour() < 12 && runTime.Minute() < 30 {
		return
	}

	// get completed run
	completedStatus, err := repo.GetUserPlaylogCompletedStatus(completedPath, statementDate)
	if err != nil {
		if err.Error() != "record not found" {
			log.Println(err)
			return
		}
	}
	if completedStatus != nil {
		if err := repo.UpdateAgcPlaylogStatus(productId, statementDate, "DONE"); err != nil {
			log.Println(err)
		}
	}
	if completedStatus != nil && !force {
		// log.Printf(path + " was Completed")
		log.Printf("runPgHardToAgcSimpleWinLose-%v was already Completed", path)
		return
	}

	// var logQuery model.ApiStatusRequest
	// logQuery.Path = path
	// logQuery.StatementDate = statementDate
	// apiStatus, err := s.repo.GetApistatus(logQuery)
	// if err != nil {
	// 	log.Println(err)
	// 	return
	// }
	// if apiStatus.IsFailed == 1 || apiStatus.IsSuccess == 1 {
	// 	log.Printf("apiStatus was Completed")
	// 	return
	// }

	isHasError := false
	var createList = make(map[string]model.UserPlaylogCreateBody, 0)
	var uniqueList []string
	var memberCodeList []string
	var totalCreated int64

	var body model.AgentPgHardCallbackSummaryRequest
	body.StatementDate = statementDate
	body.PageSize = 250

	for i := 0; ; i++ {

		body.PageIndex = i + 1

		time.Sleep(5 * time.Second)
		list, err := repo.GetAgentPgHardCallback(body)
		// INSERT AGENT LOG FIRST !
		if errOnAgcSimpleWinLose := repo.CreateUserPlaylogStatus(model.UserPlaylogStatusCreateBody{
			StatementDate: statementDate,
			Path:          path,
			Page:          body.PageIndex,
			// OutMessage:     list.Message,
			// OutTotal:       int64(list.Result.Total),
			// OutJsonError:   helper.StructJson(list.Error),
			// OutJsonSummary: helper.StructJson(list.Result.Summary),
			// OutTargetUrl:   helper.StructJson(list.TargetUrl),
		}); errOnAgcSimpleWinLose != nil {
			log.Println(errOnAgcSimpleWinLose)
			cronError = errOnAgcSimpleWinLose
		}
		if err != nil {
			cronError = err
			log.Println(err)
			break
		}

		if len(list) <= 0 {
			log.Println("ZERO==GOTALL")
			break
		}

		for _, j := range list {

			var tempRow model.UserPlaylogCreateBody
			// DAILY_KEY = {memberCode}_{statementDate}_{productId}
			uniqueKey := fmt.Sprintf("%v_%v_PGH%v", j.MemberCode, strings.Replace(statementDate, "-", "", -1), productId)
			tempRow.StatementDate = statementDate
			tempRow.DailyKey = uniqueKey

			memberCodeList = append(memberCodeList, j.MemberCode)
			tempRow.MemberCode = j.MemberCode

			tempRow.TurnGame = j.TotalBet
			tempRow.WinLoseGame = j.TotalWinlose
			tempRow.ValidAmountGame = j.TotalBet

			tempRow.TurnTotal = tempRow.TurnSport + tempRow.TurnCasino + tempRow.TurnGame + tempRow.TurnLottery + tempRow.TurnP2p + tempRow.TurnFinancial
			tempRow.WinLoseTotal = tempRow.WinLoseSport + tempRow.WinLoseCasino + tempRow.WinLoseGame + tempRow.WinLoseLottery + tempRow.WinLoseP2p + tempRow.WinLoseFinancial
			tempRow.ValidAmountTotal = tempRow.ValidAmountSport + tempRow.ValidAmountCasino + tempRow.ValidAmountGame + tempRow.ValidAmountLottery + tempRow.ValidAmountP2p + tempRow.ValidAmountFinancial
			createList[uniqueKey] = tempRow

			// Bulk insert
			if len(createList) >= 250 {
				// check exists
				for k := range createList {
					uniqueList = append(uniqueList, k)
				}
				if dbList, _, err := repo.GetPlayLogKeyList(uniqueList); err != nil {
					cronError = err // cant check
				} else {
					// if exists, remove from createList
					for _, dbKey := range dbList {
						delete(createList, dbKey)
					}
					if len(createList) > 0 {
						if err := repo.CreateUserPlaylogBulk(createList, memberCodeList); err != nil {
							log.Println(err)
							cronError = err
							continue
						}
						if err := repo.IncreaseUserTotalTurnForUserTier(createList, memberCodeList); err != nil {
							log.Println(err)
						}
						totalCreated += int64(len(createList))
					}
				}
				createList = make(map[string]model.UserPlaylogCreateBody, 0)
				uniqueList = make([]string, 0)
				memberCodeList = make([]string, 0)
			}
		}
	}

	// LEFTOVER
	if len(createList) > 0 {
		// check exists
		for k := range createList {
			uniqueList = append(uniqueList, k)
		}
		if dbList, _, err := repo.GetPlayLogKeyList(uniqueList); err != nil {
			cronError = err // cant check
		} else {
			// if exists, remove from createList
			for _, dbKey := range dbList {
				delete(createList, dbKey)
			}
			if len(createList) > 0 {
				if err := repo.CreateUserPlaylogBulk(createList, memberCodeList); err != nil {
					log.Println(err)
				}
				if err := repo.IncreaseUserTotalTurnForUserTier(createList, memberCodeList); err != nil {
					log.Println(err)
				}
				totalCreated += int64(len(createList))
			}
		}
	}

	if !isHasError {
		// INSERT AGENT LOG FIRST !
		if err := repo.CreateUserPlaylogStatus(model.UserPlaylogStatusCreateBody{
			StatementDate: statementDate,
			Path:          completedPath,
			Page:          body.PageIndex,
			OutTotal:      totalCreated,
			OutJsonError:  helper.StructJson(cronError),
		}); err != nil {
			log.Println(err)
		}
		if err := repo.UpdateAgcPlaylogStatus(productId, statementDate, "DONE"); err != nil {
			log.Println(err)
		}
	} else {
		if err := repo.UpdateAgcPlaylogStatus(productId, statementDate, "ERROR"); err != nil {
			log.Println(err)
		}
	}

	log.Println("Finish runAgcSimpleWinLose : ", path)
}

func runAmbSimpleWinLose(repo repository.AgentInfoRepository, productId int, statementDate string) {

	path := fmt.Sprintf("simplewinlose%v", productId)
	log.Printf("Running runAmbSimpleWinLose %v date %v", path, statementDate)

	statementAt, err := time.Parse("2006-01-02", statementDate)
	if err != nil {
		log.Println(err)
		return
	}

	// 1,2,4 = []string{"SLOT", "CASINO", "ESPORT"}
	body := model.AmbSimpleWinlose{}
	body.BetType = []string{"normal", "comboStep", "step", "Casino", "Slot", "Lotto", "Keno", "Trade", "Card", "Poker", "m2", "esport", "cock"}
	if productId == model.AGENT_AMB_PRODUCT_GAME {
		body.BetType = []string{"Slot"}
	} else if productId == model.AGENT_AMB_PRODUCT_CASINO {
		body.BetType = []string{"Casino"}
	} else if productId == model.AGENT_AMB_PRODUCT_SPORT {
		body.BetType = []string{"esport"}
	}

	// if productId == model.AGENT_AMB_PRODUCT_LOTTERY {
	// 	body.BetType = []string{"Lottery"}
	// } else if productId == model.AGENT_AMB_PRODUCT_P2P {
	// 	body.BetType = []string{"P2p"}
	// } else if productId == model.AGENT_AMB_PRODUCT_FINANCIAL {
	// 	body.BetType = []string{"financial"}
	// }

	// "startDate": "19-12-2023",
	// "endDate": "20-12-2023",
	body.StartDate = statementAt.Format("02-01-2006")
	body.EndDate = statementAt.AddDate(0, 0, 1).Format("02-01-2006")

	var logQuery model.ApiStatusRequest
	logQuery.Path = path
	logQuery.StatementDate = statementDate
	apiStatus, err := repo.AgcGetApistatus(logQuery)
	if err != nil {
		log.Println(err)
		return
	}
	if apiStatus.IsFailed == 1 || apiStatus.IsSuccess == 1 {
		log.Printf("apiStatus was Completed")
		return
	}

	// ====================
	isHasError := false
	members := []string{}

	time.Sleep(5 * time.Second)
	list, err := repo.AmbSimpleWinLose(body)
	if err != nil {
		log.Println(err)
		isHasError = true
		if err := repo.AgcUpdateFailed(apiStatus.Id, apiStatus.Page); err != nil {
			log.Println(err)
		}
		return
	}

	var playlogs []model.AgentPlayLog
	for _, j := range list.Data {

		turnTotal, _ := strconv.ParseFloat(j.BetAmt.NumberDecimal, 64)
		winLoseTotal, _ := strconv.ParseFloat(j.WinLose.NumberDecimal, 64)
		validAmountTotal, _ := strconv.ParseFloat(j.ValidAmt.NumberDecimal, 64)

		newRow := model.AgentPlayLog{}
		newRow.Date = statementDate
		newRow.Player = j.MemberCode
		newRow.TurnTotal = turnTotal
		newRow.WinLoseTotal = winLoseTotal
		newRow.ValidAmountTotal = validAmountTotal
		members = append(members, j.MemberCode)

		if productId == model.AGENT_AMB_PRODUCT_GAME {
			newRow.TurnGame = newRow.TurnTotal
			newRow.WinLoseGame = newRow.WinLoseTotal
			newRow.ValidAmountGame = newRow.ValidAmountTotal
		} else if productId == model.AGENT_AMB_PRODUCT_CASINO {
			newRow.TurnCasino = newRow.TurnTotal
			newRow.WinLoseCasino = newRow.WinLoseTotal
			newRow.ValidAmountCasino = newRow.ValidAmountTotal
		} else if productId == model.AGENT_AMB_PRODUCT_SPORT {
			newRow.TurnSport = newRow.TurnTotal
			newRow.WinLoseSport = newRow.WinLoseTotal
			newRow.ValidAmountSport = newRow.ValidAmountTotal
		}
		// AMB has no lottery, p2p, financial
		// else if productId == model.AGENT_AMB_PRODUCT_LOTTERY {
		// 	newRow.TurnLottery = newRow.TurnTotal
		// 	newRow.WinLoseLottery = newRow.WinLoseTotal
		// 	newRow.ValidAmountLottery = newRow.ValidAmountTotal
		// } else if productId == model.AGENT_AMB_PRODUCT_P2P {
		// 	newRow.TurnP2p = newRow.TurnTotal
		// 	newRow.WinLoseP2p = newRow.WinLoseTotal
		// 	newRow.ValidAmountP2p = newRow.ValidAmountTotal
		// } else if productId == model.AGENT_AMB_PRODUCT_FINANCIAL {
		// 	newRow.TurnFinancial = newRow.TurnTotal
		// 	newRow.WinLoseFinancial = newRow.WinLoseTotal
		// 	newRow.ValidAmountFinancial = newRow.ValidAmountTotal
		// }

		newRow.TurnTotal = newRow.TurnSport + newRow.TurnCasino + newRow.TurnGame + newRow.TurnLottery + newRow.TurnP2p + newRow.TurnFinancial
		newRow.WinLoseTotal = newRow.WinLoseSport + newRow.WinLoseCasino + newRow.WinLoseGame + newRow.WinLoseLottery + newRow.WinLoseP2p + newRow.WinLoseFinancial
		newRow.ValidAmountTotal = newRow.ValidAmountSport + newRow.ValidAmountCasino + newRow.ValidAmountGame + newRow.ValidAmountLottery + newRow.ValidAmountP2p + newRow.ValidAmountFinancial

		playlogs = append(playlogs, newRow)
	}

	memberCodeMap := make(map[string]int64)
	if len(members) > 0 {
		userList, err := repo.GetUserIdByMemberList(members)
		if err != nil {
			log.Println(err)
		}
		if len(members) > 0 {
			for _, v := range userList {
				memberCodeMap[v.MemberCode] = v.Id
			}
			for i, v := range playlogs {
				if userId, ok := memberCodeMap[v.Player]; ok {
					playlogs[i].UserID = userId
				}
			}
		}
	}

	// NEW AGENT PLAYLOG
	if len(playlogs) > 0 {
		if err = repo.InsertAmbPlayLog(playlogs); err != nil {
			isHasError = true
			if err := repo.AgcUpdateFailed(apiStatus.Id, apiStatus.Page); err != nil {
				log.Println(err)
			}
		}
	}

	time.Sleep(5 * time.Second)
	if !isHasError {
		if err := repo.AgcUpdateSuccess(apiStatus.Id); err != nil {
			log.Println(err)
		}
		if err := repo.UpdateAgcPlaylogStatus(productId, statementDate, "DONE"); err != nil {
			log.Println(err)
		}
	} else {
		if err := repo.UpdateAgcPlaylogStatus(productId, statementDate, "ERROR"); err != nil {
			log.Println(err)
		}
	}

	log.Println("Finish runAmbSimpleWinLose : ", productId)
}

func runLotteryPlaylog(repo repository.AgentInfoRepository, statementDate string) {

	// actionAt := time.Now()
	productId := model.AGENT_PRODUCT_LOTTERY
	path := fmt.Sprintf("simplewinlose%v", productId)
	log.Printf("Running runLotteryPlaylog %v date %v", path, statementDate)

	// agentName := os.Getenv("AGENT_NAME")
	body := model.LotteryPlaylogRequest{}
	body.Date = statementDate
	// body.Products = []int{productId}
	body.Size = 250
	// body.Page = 1

	var logQuery model.ApiStatusRequest
	logQuery.Path = path
	logQuery.StatementDate = statementDate
	apiStatus, err := repo.AgcGetApistatus(logQuery)
	if err != nil {
		log.Println(err)
		return
	}
	if apiStatus.IsFailed == 1 || apiStatus.IsSuccess == 1 {
		log.Println("runLotteryPlaylog ", path, " Already run.")
		return
	}

	isHasError := false
	var logs []model.AgentPlayLog
	userIds := make(map[int64]int64)
	for i := 0; ; i++ {

		// AGENT start at 1, LOTTERY start at 0
		body.Page = i + 1

		time.Sleep(5 * time.Second)
		list, err := repo.LotterySimpleWinLose(body)
		if err != nil {
			isHasError = true
			if err2 := repo.AgcUpdateFailed(apiStatus.Id, i); err2 != nil {
				log.Println(err2)
			}
			log.Println(err)
			break
		}

		if len(list.Content) <= 0 {
			log.Println("ZERO==GOTALL")
			break
		}

		var obj model.AgentPlayLog
		obj.Date = body.Date
		for _, j := range list.Content {
			//reset
			obj.TurnSport = 0
			obj.WinLoseSport = 0
			obj.TurnCasino = 0
			obj.WinLoseCasino = 0
			obj.TurnGame = 0
			obj.WinLoseGame = 0

			obj.TurnLottery = 0
			obj.WinLoseLottery = 0
			obj.TurnP2p = 0
			obj.WinLoseP2p = 0
			obj.TurnFinancial = 0
			obj.WinLoseFinancial = 0

			obj.ValidAmountSport = 0
			obj.ValidAmountCasino = 0
			obj.ValidAmountGame = 0
			obj.ValidAmountLottery = 0
			obj.ValidAmountP2p = 0
			obj.ValidAmountFinancial = 0

			obj.TurnTotal = 0
			obj.WinLoseTotal = 0
			obj.ValidAmountTotal = 0

			userIds[j.UserId] = j.UserId
			// obj.Player = j.UserName
			obj.UserID = j.UserId

			if productId == model.AGENT_PRODUCT_SPORT {
				obj.TurnSport = j.TurnOver
				obj.WinLoseSport = j.WinLoss
				obj.ValidAmountSport = j.ValidAmount
			} else if productId == model.AGENT_PRODUCT_CASINO {
				obj.TurnCasino = j.TurnOver
				obj.WinLoseCasino = j.WinLoss
				obj.ValidAmountCasino = j.ValidAmount
			} else if productId == model.AGENT_PRODUCT_GAME {
				obj.TurnGame = j.TurnOver
				obj.WinLoseGame = j.WinLoss
				obj.ValidAmountGame = j.ValidAmount
			} else if productId == model.AGENT_PRODUCT_LOTTERY {
				obj.TurnLottery = j.TurnOver
				obj.WinLoseLottery = j.WinLoss
				obj.ValidAmountLottery = j.ValidAmount
			} else if productId == model.AGENT_PRODUCT_P2P {
				obj.TurnP2p = j.TurnOver
				obj.WinLoseP2p = j.WinLoss
				obj.ValidAmountP2p = j.ValidAmount
			} else if productId == model.AGENT_PRODUCT_FINANCIAL {
				obj.TurnFinancial = j.TurnOver
				obj.WinLoseFinancial = j.WinLoss
				obj.ValidAmountFinancial = j.ValidAmount
			}

			obj.TurnTotal = obj.TurnSport + obj.TurnCasino + obj.TurnGame + obj.TurnLottery + obj.TurnP2p + obj.TurnFinancial
			obj.WinLoseTotal = obj.WinLoseSport + obj.WinLoseCasino + obj.WinLoseGame + obj.WinLoseLottery + obj.WinLoseP2p + obj.WinLoseFinancial
			obj.ValidAmountTotal = obj.ValidAmountSport + obj.ValidAmountCasino + obj.ValidAmountGame + obj.ValidAmountLottery + obj.ValidAmountP2p + obj.ValidAmountFinancial
			logs = append(logs, obj)
		}

		// Bulk insert Every Page.
		if len(userIds) > 0 {
			userList, err := repo.GetUserMemberByUserId(helper.MapIdsToInt64Array(userIds))
			if err != nil {
				log.Println(err)
			}
			if len(userIds) > 0 {
				for i, v := range logs {
					for _, j := range userList {
						if v.UserID == j.Id {
							logs[i].Player = j.MemberCode
							break
						}
					}
				}
				if err = repo.InsertAgcPlayLog(logs, path, body.Page, true); err != nil {
					isHasError = true
					if err := repo.AgcUpdateFailed(apiStatus.Id, apiStatus.Page); err != nil {
						log.Println(err)
					}
				}
			}
			// RESET
			logs = []model.AgentPlayLog{}
			userIds = make(map[int64]int64)
		}
	}

	if len(userIds) > 0 {
		userList, err := repo.GetUserMemberByUserId(helper.MapIdsToInt64Array(userIds))
		if err != nil {
			log.Println(err)
		}
		if len(userIds) > 0 {
			for i, v := range logs {
				for _, j := range userList {
					if v.Player == j.MemberCode {
						logs[i].UserID = j.Id
						break
					}
				}
			}
			if err = repo.InsertAgcPlayLog(logs, path, body.Page, true); err != nil {
				isHasError = true
				if err := repo.AgcUpdateFailed(apiStatus.Id, apiStatus.Page); err != nil {
					log.Println(err)
				}
			}
		}
	}

	time.Sleep(5 * time.Second)
	if !isHasError {
		if err := repo.AgcUpdateSuccess(apiStatus.Id); err != nil {
			log.Println(err)
		}
		// if err := s.repo.UpdateAgcPlaylogStatus(productId, statementDate, "DONE"); err != nil {
		// 	log.Println(err)
		// }
	}

	log.Println("Finish runLotteryPlaylog : ", path)
}

func runLotteryUserPlaylog(repo repository.AgentInfoRepository, productId int, statementDate string, force bool) {

	var cronError error

	actionAt := time.Now()
	path := fmt.Sprintf("simplewinlose%v", productId)
	completedPath := fmt.Sprintf("completed_%v", productId)
	log.Printf("Running runLotteryPlaylog %v date %v", path, statementDate)

	// RUN AFTER 12.30
	bbkLoc := time.FixedZone("Asia/Bangkok", 7*60*60)
	runTime := actionAt.In(bbkLoc)
	if runTime.Hour() < 12 && runTime.Minute() < 30 {
		return
	}

	// get completed run
	completedStatus, err := repo.GetUserPlaylogCompletedStatus(completedPath, statementDate)
	if err != nil {
		if err.Error() != "record not found" {
			log.Println(err)
			return
		}
	}
	if completedStatus != nil {
		if err := repo.UpdateAgcPlaylogStatus(productId, statementDate, "DONE"); err != nil {
			log.Println(err)
		}
	}
	if completedStatus != nil && !force {
		// log.Printf(path + " was Completed")
		log.Printf("runLotteryPlaylog-%v was already Completed", path)
		return
	}

	// agentName := os.Getenv("AGENT_NAME")
	body := model.LotteryPlaylogRequest{}
	body.Date = statementDate
	body.Size = 250
	// body.Page = 1

	isHasError := false
	var createList = make(map[string]model.UserPlaylogCreateBody, 0)
	var uniqueList []string
	// var memberCodeList []string
	var totalCreated int64
	for i := 0; ; i++ {

		// AGENT start at 1, LOTTERY start at 0
		body.Page = i + 1

		time.Sleep(5 * time.Second)
		list, err := repo.LotterySimpleWinLose(body)
		// INSERT AGENT LOG FIRST !
		if errOnLotterySimpleWinLose := repo.CreateUserPlaylogStatus(model.UserPlaylogStatusCreateBody{
			StatementDate: statementDate,
			Path:          path,
			Page:          body.Page,
			// OutTotal:      list.NumberOfElements,
			// OutMessage:    list.Message,
			// OutJsonError:  helper.StructJson(list.Error),
			// OutJsonSummary: helper.StructJson(list.Result.Summary),
			// OutTargetUrl:   helper.StructJson(list.TargetUrl),
		}); errOnLotterySimpleWinLose != nil {
			log.Println(errOnLotterySimpleWinLose)
			cronError = errOnLotterySimpleWinLose
		}
		if err != nil {
			cronError = err
			log.Println(err)
			break
		}

		if len(list.Content) <= 0 {
			log.Println("ZERO==GOTALL")
			break
		}

		for _, j := range list.Content {

			var tempRow model.UserPlaylogCreateBody
			// DAILY_KEY = {memberCode}_{statementDate}_{productId}
			uniqueKey := fmt.Sprintf("%v_%v_%v", j.UserId, strings.Replace(statementDate, "-", "", -1), productId)
			tempRow.StatementDate = body.Date
			tempRow.DailyKey = uniqueKey

			// memberCodeList = append(memberCodeList, j.MemberCode)
			// tempRow.MemberCode = j.MemberCode
			tempRow.UserId = j.UserId

			// } else if productId == model.AGENT_PRODUCT_LOTTERY {
			tempRow.TurnLottery = j.TurnOver
			tempRow.WinLoseLottery = j.WinLoss
			tempRow.ValidAmountLottery = j.ValidAmount

			tempRow.TurnTotal = tempRow.TurnSport + tempRow.TurnCasino + tempRow.TurnGame + tempRow.TurnLottery + tempRow.TurnP2p + tempRow.TurnFinancial
			tempRow.WinLoseTotal = tempRow.WinLoseSport + tempRow.WinLoseCasino + tempRow.WinLoseGame + tempRow.WinLoseLottery + tempRow.WinLoseP2p + tempRow.WinLoseFinancial
			tempRow.ValidAmountTotal = tempRow.ValidAmountSport + tempRow.ValidAmountCasino + tempRow.ValidAmountGame + tempRow.ValidAmountLottery + tempRow.ValidAmountP2p + tempRow.ValidAmountFinancial
			createList[uniqueKey] = tempRow

			// Bulk insert
			if len(createList) >= 250 {
				// check exists
				for k := range createList {
					uniqueList = append(uniqueList, k)
				}
				if dbList, _, err := repo.GetPlayLogKeyList(uniqueList); err != nil {
					cronError = err // cant check
				} else {
					// if exists, remove from createList
					for _, dbKey := range dbList {
						delete(createList, dbKey)
					}
					if len(createList) > 0 {
						if err := repo.CreateUserPlaylogBulkDirect(createList); err != nil {
							log.Println(err)
							cronError = err
							continue
						}
						if err := repo.IncreaseUserTotalTurnForUserTierDirect(createList); err != nil {
							log.Println(err)
						}
						totalCreated += int64(len(createList))
					}
				}
				createList = make(map[string]model.UserPlaylogCreateBody, 0)
				uniqueList = make([]string, 0)
				// memberCodeList = make([]string, 0)
			}
		}
	}

	// LEFTOVER
	if len(createList) > 0 {
		// check exists
		for k := range createList {
			uniqueList = append(uniqueList, k)
		}
		if dbList, _, err := repo.GetPlayLogKeyList(uniqueList); err != nil {
			cronError = err // cant check
		} else {
			// if exists, remove from createList
			for _, dbKey := range dbList {
				delete(createList, dbKey)
			}
			if len(createList) > 0 {
				if err := repo.CreateUserPlaylogBulkDirect(createList); err != nil {
					log.Println(err)
				}
				if err := repo.IncreaseUserTotalTurnForUserTierDirect(createList); err != nil {
					log.Println(err)
				}
				totalCreated += int64(len(createList))
			}
		}
	}

	if !isHasError {
		// INSERT AGENT LOG FIRST !
		if err := repo.CreateUserPlaylogStatus(model.UserPlaylogStatusCreateBody{
			StatementDate: statementDate,
			Path:          completedPath,
			Page:          body.Page,
			OutTotal:      totalCreated,
			OutJsonError:  helper.StructJson(cronError),
		}); err != nil {
			log.Println(err)
		}
		if err := repo.UpdateAgcPlaylogStatus(productId, statementDate, "DONE"); err != nil {
			log.Println(err)
		}
	} else {
		if err := repo.UpdateAgcPlaylogStatus(productId, statementDate, "ERROR"); err != nil {
			log.Println(err)
		}
	}

	log.Println("Finish runLotteryPlaylog : ", path)
}

func calculateAf(repo repository.AgentInfoRepository, statementDate string) error {

	log.Println("calculateAf() statementDate=", statementDate)

	cronStatus := "DONE"

	// ** All of These function is Create Unique record / create when not exists only **
	// UNIQUED, NO DUPLICATE RECORD only CREATE and UPDATE to the latest value.

	// 1.Real Affiliate Income = For How Aff Commission(GAME) Calculate = ONLY INSERT COMPLETED DATA READY to CHECK and SUM
	var req model.CronAllianceIncomeCalcRequest
	req.StatementDate = statementDate
	if err := CronCreateAffiliateIncome(repo, req); err != nil {
		cronStatus = "ERROR"
		log.Println(err)
	}

	// 2.Affiliate Transaction for Taking Commission and Show as Transaction with Total(SUM) of Commission
	if err := CronCreateAffiliateTransaction(repo, req); err != nil {
		cronStatus = "ERROR"
		log.Println(err)
	}

	// 3.Set Expire Date for Affiliate Transaction
	if err := CronExpireAffiliateTransaction(repo); err != nil {
		cronStatus = "ERROR"
		log.Println(err)
	}

	// Update CRON_STATUS
	if err := repo.UpdateAgcCronCalcStatus("AFFILIATE", statementDate, cronStatus); err != nil {
		log.Println(err)
	}

	return nil
}

func calculateAl(repo repository.AgentInfoRepository, statementDate string) error {

	log.Println("calculateAl() for date:", statementDate)
	cronStatus := "DONE"

	// Real Alliance Income + Update ToLastest If any Value Changed. **NO DUPLICATE**
	var req model.CronAllianceIncomeCalcRequest
	req.StatementDate = statementDate
	if err := CronCreateAllianceWinloseIncome(repo, req); err != nil {
		cronStatus = "ERROR"
		log.Println(err)
	}

	// CRON_STATUS
	if err := repo.UpdateAgcCronCalcStatus("ALLIANCE", statementDate, cronStatus); err != nil {
		log.Println(err)
	}

	return nil
}

func CronCreateAffiliateIncome(repo repository.AgentInfoRepository, req model.CronAllianceIncomeCalcRequest) error {

	var cronError error

	actionAt := time.Now()
	statementDate := actionAt.AddDate(0, 0, -1)
	if req.StatementDate != "" {
		statementAt, _ := time.Parse("2006-01-02", req.StatementDate)
		statementDate = statementAt
	}

	affComSetting, err := repo.GetCommissionSetting()
	if err != nil {
		log.Println(err)
		time.Sleep(5 * time.Second)
	}

	// fmt.Println("CronCreateAffiliateIncome.statementDate", statementDate)

	// Get PlayLog by ofDate ** Only user that has ref_by **
	var query model.UserPlaylogListRequest
	query.FromDate = statementDate.Format("2006-01-02")
	query.ToDate = statementDate.Format("2006-01-02")
	playLogList, _, err := repo.GetDownlineUserTotalPlayLogList(query) // user_playlog
	if err != nil {
		return err
	}
	// fmt.Println("CronCreateAffiliateIncome.playLogList.LEN", len(playLogList))

	var createList = make(map[string]model.UserAffiliateIncomeCreateBody, 0)
	var uniqueList []string
	for _, playrow := range playLogList {

		// check usertype and refby

		var tempRow model.UserAffiliateIncomeCreateBody
		// use join instead tempRow.RefBy = playrow.RefBy
		tempRow.UserId = playrow.UserId
		tempRow.StatementDate = statementDate.Format("2006-01-02")
		uniqueKey := fmt.Sprintf("D%sU%d", statementDate.Format("20060102"), playrow.UserId)
		uniqueList = append(uniqueList, uniqueKey)
		tempRow.DailyKey = uniqueKey

		// Create all Level All percent percent1 percent2
		tempRow.PercentCasino = affComSetting.Casino
		tempRow.PercentCasino1 = affComSetting.Casino1
		tempRow.PercentCasino2 = affComSetting.Casino2
		tempRow.PercentSport = affComSetting.Sport
		tempRow.PercentSport1 = affComSetting.Sport1
		tempRow.PercentSport2 = affComSetting.Sport2
		tempRow.PercentGame = affComSetting.Slot
		tempRow.PercentGame1 = affComSetting.Slot1
		tempRow.PercentGame2 = affComSetting.Slot2
		tempRow.PercentLottery = affComSetting.Lottery
		tempRow.PercentLottery1 = affComSetting.Lottery1
		tempRow.PercentLottery2 = affComSetting.Lottery2
		tempRow.PercentP2p = affComSetting.P2p
		tempRow.PercentP2p1 = affComSetting.P2p1
		tempRow.PercentP2p2 = affComSetting.P2p2
		tempRow.PercentFinancial = affComSetting.Financial
		tempRow.PercentFinancial1 = affComSetting.Financial1
		tempRow.PercentFinancial2 = affComSetting.Financial2

		// [241202] 1.ไม่ใช้ Turn + ใช้ ValidAmount แทน tempRow.TurnSport = playrow.TurnSport
		// tempRow.TurnSport = playrow.TurnSport
		// tempRow.TurnSport = playrow.ValidAmountSport
		// [250428] ตัวเลือก จะใช้ WINLOSS หรือ TURNOVER
		if affComSetting.CommissionFrom == "WINLOSS" {
			if playrow.WinLoseSport < 0 {
				// ติดลบ แปลว่าเสีย -50 = ยอดเสีย 50
				tempRow.TurnSport = math.Abs(playrow.WinLoseSport)
			}
		} else {
			tempRow.TurnSport = playrow.ValidAmountSport
		}
		tempRow.CommissionSport = math.Floor(tempRow.TurnSport*affComSetting.Sport) / 100
		tempRow.CommissionSport1 = math.Floor(tempRow.TurnSport*affComSetting.Sport1) / 100
		tempRow.CommissionSport2 = math.Floor(tempRow.TurnSport*affComSetting.Sport2) / 100
		// [241202] 1.ไม่ใช้ Turn + ใช้ ValidAmount แทน tempRow.TurnSport = playrow.TurnCasino
		// tempRow.TurnCasino = playrow.ValidAmountCasino
		// [250428] ตัวเลือก จะใช้ WINLOSS หรือ TURNOVER
		if affComSetting.CommissionFrom == "WINLOSS" {
			if playrow.WinLoseCasino < 0 {
				// ติดลบ แปลว่าเสีย -50 = ยอดเสีย 50
				tempRow.TurnCasino = math.Abs(playrow.WinLoseCasino)
			}
		} else {
			tempRow.TurnCasino = playrow.ValidAmountCasino
		}
		tempRow.CommissionCasino = math.Floor(tempRow.TurnCasino*affComSetting.Casino) / 100
		tempRow.CommissionCasino1 = math.Floor(tempRow.TurnCasino*affComSetting.Casino1) / 100
		tempRow.CommissionCasino2 = math.Floor(tempRow.TurnCasino*affComSetting.Casino2) / 100
		// [241202] 1.ไม่ใช้ Turn + ใช้ ValidAmount แทน tempRow.TurnSport = playrow.TurnGame
		// tempRow.TurnGame = playrow.ValidAmountGame
		// [250428] ตัวเลือก จะใช้ WINLOSS หรือ TURNOVER
		if affComSetting.CommissionFrom == "WINLOSS" {
			if playrow.WinLoseGame < 0 {
				// ติดลบ แปลว่าเสีย -50 = ยอดเสีย 50
				tempRow.TurnGame = math.Abs(playrow.WinLoseGame)
			}
		} else {
			tempRow.TurnGame = playrow.ValidAmountGame
		}
		tempRow.CommissionGame = math.Floor(tempRow.TurnGame*affComSetting.Slot) / 100
		tempRow.CommissionGame1 = math.Floor(tempRow.TurnGame*affComSetting.Slot1) / 100
		tempRow.CommissionGame2 = math.Floor(tempRow.TurnGame*affComSetting.Slot2) / 100
		// [241202] 1.ไม่ใช้ Turn + ใช้ ValidAmount แทน tempRow.TurnSport = playrow.TurnLottery
		// tempRow.TurnLottery = playrow.ValidAmountLottery
		// [250428] ตัวเลือก จะใช้ WINLOSS หรือ TURNOVER
		if affComSetting.CommissionFrom == "WINLOSS" {
			if playrow.WinLoseLottery < 0 {
				// ติดลบ แปลว่าเสีย -50 = ยอดเสีย 50
				tempRow.TurnLottery = math.Abs(playrow.WinLoseLottery)
			}
		} else {
			tempRow.TurnLottery = playrow.ValidAmountLottery
		}
		tempRow.CommissionLottery = math.Floor(tempRow.TurnLottery*affComSetting.Lottery) / 100
		tempRow.CommissionLottery1 = math.Floor(tempRow.TurnLottery*affComSetting.Lottery1) / 100
		tempRow.CommissionLottery2 = math.Floor(tempRow.TurnLottery*affComSetting.Lottery2) / 100
		// [241202] 1.ไม่ใช้ Turn + ใช้ ValidAmount แทน tempRow.TurnSport = playrow.TurnP2p
		// tempRow.TurnP2p = playrow.ValidAmountP2p
		// [250428] ตัวเลือก จะใช้ WINLOSS หรือ TURNOVER
		if affComSetting.CommissionFrom == "WINLOSS" {
			if playrow.WinLoseP2p < 0 {
				// ติดลบ แปลว่าเสีย -50 = ยอดเสีย 50
				tempRow.TurnP2p = math.Abs(playrow.WinLoseP2p)
			}
		} else {
			tempRow.TurnP2p = playrow.ValidAmountP2p
		}
		tempRow.CommissionP2p = math.Floor(tempRow.TurnP2p*affComSetting.P2p) / 100
		tempRow.CommissionP2p1 = math.Floor(tempRow.TurnP2p*affComSetting.P2p1) / 100
		tempRow.CommissionP2p2 = math.Floor(tempRow.TurnP2p*affComSetting.P2p2) / 100
		// [241202] 1.ไม่ใช้ Turn + ใช้ ValidAmount แทน tempRow.TurnSport = playrow.TurnFinancial
		// tempRow.TurnFinancial = playrow.ValidAmountFinancial
		// [250428] ตัวเลือก จะใช้ WINLOSS หรือ TURNOVER
		if playrow.WinLoseFinancial < 0 {
			// ติดลบ แปลว่าเสีย -50 = ยอดเสีย 50
			tempRow.TurnFinancial = math.Abs(playrow.WinLoseFinancial)
		} else {
			tempRow.TurnFinancial = playrow.ValidAmountFinancial
		}
		tempRow.CommissionFinancial = math.Floor(tempRow.TurnFinancial*affComSetting.Financial) / 100
		tempRow.CommissionFinancial1 = math.Floor(tempRow.TurnFinancial*affComSetting.Financial1) / 100
		tempRow.CommissionFinancial2 = math.Floor(tempRow.TurnFinancial*affComSetting.Financial2) / 100

		tempRow.TurnTotal = tempRow.TurnCasino + tempRow.TurnGame + tempRow.TurnSport + tempRow.TurnLottery + tempRow.TurnP2p + tempRow.TurnFinancial
		tempRow.CommissionTotal = tempRow.CommissionCasino + tempRow.CommissionGame + tempRow.CommissionSport + tempRow.CommissionLottery + tempRow.CommissionP2p + tempRow.CommissionFinancial
		createList[uniqueKey] = tempRow

		// Bulk insert
		if len(createList) >= 250 {
			// check exists
			for k := range createList {
				uniqueList = append(uniqueList, k)
			}
			if dbList, _, err := repo.GetUserAffiliateIncomeKeyList(uniqueList); err != nil {
				cronError = err // cant check
			} else {
				// if exists, remove from createList
				for _, dbKey := range dbList {
					delete(createList, dbKey)
				}
				if len(createList) > 0 {
					err = repo.CreateUserAffiliateIncomeBulk(createList)
					if err != nil {
						log.Println(err)
						cronError = err
					}
				}
			}
			createList = make(map[string]model.UserAffiliateIncomeCreateBody, 0)
			uniqueList = make([]string, 0)
		}
	}

	// LEFT_OVER
	if len(createList) > 0 {
		// check exists
		for k := range createList {
			uniqueList = append(uniqueList, k)
		}
		if dbList, _, err := repo.GetUserAffiliateIncomeKeyList(uniqueList); err != nil {
			cronError = err // cant check
		} else {
			// if exists, remove from createList
			for _, dbKey := range dbList {
				delete(createList, dbKey)
			}
			if len(createList) > 0 {
				err = repo.CreateUserAffiliateIncomeBulk(createList)
				if err != nil {
					log.Println(err)
					cronError = err
				}
			}
		}
	}

	if cronError != nil {
		log.Println(cronError)
	}

	return nil
}

func CronCreateAffiliateTransaction(repo repository.AgentInfoRepository, req model.CronAllianceIncomeCalcRequest) error {

	var cronError error

	actionAt := time.Now()
	statementDate := actionAt.AddDate(0, 0, -1)
	if req.StatementDate != "" {
		statementAt, _ := time.Parse("2006-01-02", req.StatementDate)
		statementDate = statementAt
	}

	// This User ** Income From This User ** for Calculate History Log
	// if err := tx.Table("user_affiliate_income").Create(createList).Error; err != nil {
	// 	tx.Rollback()
	// 	return err
	// }
	// GetUserTotalAffIncomeList(req model.UserAffiliateIncomeTotalListRequest) ([]model.UserAffiliateIncomeTotalResponse, int64, error)

	// CONFIG-LIMIT-ALL-AFFCOMMISSION
	afCommissionSetting, err := repo.GetCommissionSetting()
	if err != nil {
		return err
	}

	// Get PlayLog by ofDate Grouped By RefBy
	var query model.UserAffiliateIncomeTotalListRequest
	query.StatementDate = statementDate.Format("2006-01-02")

	// [20240617] สิ่งที่ต้องการ
	// เพิ่มจำกัดรายได้สูงสุดต่อ 1 ลิ้งก์แนะนำ และช่อง Text area สำหรับกรอกตัวเลขอาราบิก
	// *จำกัดรายได้สูงสุดต่อ 1 ลิ้งก์แนะนำ เกินจำนวนที่ตั้งค่า จะไม่สามารถรับรายได้ได้อีก (0 หมายถึงไม่จำกัด)
	// ตัวอย่างเช่น
	// จำกัดรายได้สูงสุด 100 (รายได้รวมจากทุกลูกลิ้งก์)
	// จำกัดรายได้สูงสุดต่อ 1 ยูส 10 (รายได้สูงสุดที่รับได้จากค่าคอม 1 ลูกลิ้งก์)
	// หมายความว่ารายได้ของหัวลิ้งก์ ต่อ 1 ยูสลูกลิ้งก์ จะรับได้ไม่เกิน 10 เครดิต แต่รับกี่ยูสลูกลิ้งก์ ก็ได้
	if afCommissionSetting.MaxCommissionPerLine > 0 {
		query.LimitAmount = afCommissionSetting.MaxCommissionPerLine
	}

	// Get Each Ref total commission with UserList
	// GetUserTotalAffIncomeListWithLimit => GetUserLevelAffIncomeListWithLimit
	incomeList, err := repo.GetUserLevelAffIncomeListWithLimit(query, *afCommissionSetting)
	if err != nil {
		return err
	}
	// fmt.Println("GetUserTotalAffIncomeListWithLimit.", helper.StructJson(query), " count=", len(incomeList))
	// fmt.Println("GetUserTotalAffIncomeListWithLimit.", helper.StructJson(incomeList))

	var createList = make(map[string]model.UserAffiliateIncomeTotalResponse, 0)
	var uniqueList []string
	for _, totalRefIncomeRow := range incomeList {

		uniqueKey := fmt.Sprintf("D%sU%d", statementDate.Format("060102"), totalRefIncomeRow.RefBy)
		uniqueList = append(uniqueList, uniqueKey)
		totalRefIncomeRow.DailyKey = uniqueKey

		// No need to calculate
		createList[uniqueKey] = totalRefIncomeRow

		// Bulk insert
		if len(createList) >= 250 {
			// check exists
			for k := range createList {
				uniqueList = append(uniqueList, k)
			}
			if dbList, _, err := repo.GetUserAffiliateTransactionKeyList(uniqueList); err != nil {
				cronError = err // cant check
			} else {
				// if exists, remove from createList
				for _, dbKey := range dbList {
					delete(createList, dbKey)
				}
				if len(createList) > 0 {
					err = repo.CreateUserAffiliateTransactionBulk(createList)
					if err != nil {
						log.Println(err)
						cronError = err
					}
				}
			}
			createList = make(map[string]model.UserAffiliateIncomeTotalResponse, 0)
			uniqueList = make([]string, 0)
		}
	}

	// LEFT_OVER
	if len(createList) > 0 {
		// check exists
		for k := range createList {
			uniqueList = append(uniqueList, k)
		}
		if dbList, _, err := repo.GetUserAffiliateTransactionKeyList(uniqueList); err != nil {
			cronError = err // cant check
		} else {
			// if exists, remove from createList
			for _, dbKey := range dbList {
				delete(createList, dbKey)
			}
			if len(createList) > 0 {
				err = repo.CreateUserAffiliateTransactionBulk(createList)
				if err != nil {
					log.Println(err)
					cronError = err
				}
			}
		}
	}

	if cronError != nil {
		log.Println(cronError)
	}

	return nil
}

func (s cronService) CronExpireAffiliateTransaction() error {

	return CronExpireAffiliateTransaction(s.repo)
}

func CronExpireAffiliateTransaction(repo repository.AgentInfoRepository) error {

	// actionAt := time.Now()

	affComSetting, err := repo.GetCommissionSetting()
	if err != nil {
		log.Println(err)
		time.Sleep(5 * time.Second)
	}

	// log.Println("CronExpireAffiliateTransaction.actionAt", actionAt)
	// log.Println("CronExpireAffiliateTransaction.affComSetting.CollectableDays", affComSetting.CollectableDays)

	// QUERY CHECK
	// SELECT * FROM user_affiliate WHERE commission_total > 0 ;
	// SELECT * FROM user_affiliate WHERE commission_total > 0 AND commission_current != commission_total ;
	// SELECT * FROM user_affiliate WHERE commission_total > 0 AND commission_total != (first_deposit_bonus + bonus_share_total + commission_sport + commission_casino + commission_game);
	// SELECT * FROM user_affiliate WHERE commission_total > 0 AND (commission_current = commission_total AND commission_total = (first_deposit_bonus + bonus_share_total + commission_sport + commission_casino + commission_game));

	// SELECT * FROM user_affiliate AS tb_user_aff WHERE tb_user_aff.commission_current > 0;
	// SELECT SUM(commission_current) FROM user_affiliate WHERE commission_current > 0;
	// SELECT SUM(income_amount) FROM affiliate_transaction WHERE status_id = 1;
	// SELECT * FROM user_affiliate WHERE commission_current < 0;

	if affComSetting.CollectableDays > 0 {
		// 0 = no expired
		// 1 = expired if created_at < now - 1 day ex. 240108 12.00 < 240109-1 12.30
		// 2 = expired if created_at < now - 2 day
		if err := repo.ExpireUserAffiliateTransaction(affComSetting.CollectableDays); err != nil {
			return err
		}
	}

	return nil
}

func (s cronService) CronCreateAllianceWinloseIncome(req model.CronAllianceIncomeCalcRequest) error {

	return CronCreateAllianceWinloseIncome(s.repo, req)
}

func CronCreateAllianceWinloseIncome(repo repository.AgentInfoRepository, req model.CronAllianceIncomeCalcRequest) error {

	// Call recalulate
	var req2 model.CronAllianceIncomeCalcRequest
	req2.StatementDate = req.StatementDate
	if err := cronRecalcAllianceWinloseIncome(repo, req2); err != nil {
		log.Println(err)
	}
	return nil
}

func (s cronService) CronRecalcAllianceWinloseIncome(req model.CronAllianceIncomeRecalcRequest) error {

	// [20231228] From To date range

	dateList := make([]string, 0)
	if req.FromStatementDate != "" && req.ToStatementDate != "" {
		fromDate, _ := time.Parse("2006-01-02", req.FromStatementDate)
		toDate, _ := time.Parse("2006-01-02", req.ToStatementDate)
		for d := fromDate; d.Before(toDate); d = d.AddDate(0, 0, 1) {
			dateList = append(dateList, d.Format("2006-01-02"))
		}
		// last date
		dateList = append(dateList, toDate.Format("2006-01-02"))
	} else {
		return internalServerError(fmt.Errorf("FromDate and ToDate is required"))
	}

	if len(dateList) > 0 && len(dateList) < 100 {
		for _, statementDate := range dateList {
			var req model.CronAllianceIncomeCalcRequest
			req.StatementDate = statementDate
			if err := cronRecalcAllianceWinloseIncome(s.repo, req); err != nil {
				log.Println(err)
			}
		}
	} else {
		log.Println("CronRecalcAllianceWinloseIncome.dateList.INVALID", dateList)
	}

	return nil
}

func cronRecalcAllianceWinloseIncome(repo repository.AgentInfoRepository, req model.CronAllianceIncomeCalcRequest) error {

	// [20231227] แก้ไขข้อมูล เดิมให้ถูกต้อง

	// VALIDATE Get PlayLog by ofDate
	actionAt := time.Now()
	statementDate := actionAt.AddDate(0, 0, -1)
	if req.StatementDate != "" {
		statementAt, _ := time.Parse("2006-01-02", req.StatementDate)
		statementDate = statementAt             // วันที่เล่น
		actionAt = statementAt.AddDate(0, 0, 1) // วันที่ตัดยอด
	}

	var query model.AlliancePlayLogListRequest
	query.FromDate = statementDate.Format("2006-01-02")
	query.ToDate = statementDate.Format("2006-01-02")
	playlogList, total, err := repo.GetAllianceSumUserPlayLogList(query)
	if err != nil {
		return err
	}

	if total > 0 {

		// Get User's Bonus Log by ofDate
		userIds := make([]int64, 0)
		for _, row := range playlogList {
			if row.UserId > 0 {
				userIds = append(userIds, row.UserId)
			}
		}
		if len(userIds) == 0 {
			log.Println("No users to calculate")
			return nil
		}

		var query2 model.CronAllianceIncomeBonusListequest
		query2.UserIds = userIds
		query2.StatementDate = statementDate.Format("2006-01-02")
		userBonuslogList, err := buildBonusLog(repo, query2)
		if err != nil {
			return err
		}

		createList := make(map[string]model.AllianceWinloseIncomeCreateBody)
		for _, row := range playlogList {
			// NOT CALCULATE IF USERID < 1 | NO_USER
			if row.UserId < 1 {
				continue
			}
			// in format D20231114U177
			dailyKey := fmt.Sprintf("D%sU%d", statementDate.Format("20060102"), row.UserId)
			if _, ok := createList[dailyKey]; !ok {
				// [241202] 2. Alliance เปลี่ยนจาก Turn ไปเป็น ValidAmount
				newRow := model.AllianceWinloseIncomeCreateBody{
					DailyKey:      dailyKey,
					UserId:        row.UserId,
					StatementDate: statementDate.Format("2006-01-02"), // วันที่เล่น
					OfDate:        actionAt.Format("2006-01-02"),      // วันที่ตัดยอด
					// [241202] 2. Alliance เปลี่ยนจาก Turn ไปเป็น ValidAmount TotalPlayAmount: row.SumTurnTotal, // playlog
					TotalPlayAmount:    row.SumValidAmountTotal, // playlog
					TotalWinLoseAmount: row.SumWinLoseTotal,     // playlog
					AlliancePercent:    row.AlliancePercent,
					CommissionPercent:  row.CommissionPercent,
				}
				// Calc per row [20231120] ใส่สูตรรายได้
				// A1.TotalPlayAmount
				// 1.TotalWinLoseAmount
				// A2.TotalCommission = A1. * CommissionPercent(0.03%)
				newRow.TotalCommission = newRow.TotalPlayAmount * (newRow.CommissionPercent / 100)
				// 2.AllianceWinloseAmount = 1. * AlliancePercent(30%)
				if newRow.TotalWinLoseAmount < 0 {
					// They lose money, you get commission
					newRow.AllianceWinloseAmount = (math.Abs(newRow.TotalWinLoseAmount) * (newRow.AlliancePercent / 100))
				} else {
					// They win money, you lose commission (negative)
					newRow.AllianceWinloseAmount = -1 * (math.Abs(newRow.TotalWinLoseAmount) * (newRow.AlliancePercent / 100))
				}
				// A3. AllianceCommission = A2. - AlliancePercent(30%)
				newRow.AllianceCommission = newRow.TotalCommission - (newRow.TotalCommission * (newRow.AlliancePercent / 100))
				// 3.TotalPaidBonus from total user's bonus log from user_transaction
				newRow.TotalPaidBonus = 0
				if userBonuslog, ok := (*userBonuslogList)[row.UserId]; ok {
					// 0.01 หาย เพราะ transfer_at มาทีหลัง
					newRow.TotalPaidBonus = -1 * userBonuslog.TotalBonusAmount
					// Remove used bonuslog
					delete(*userBonuslogList, row.UserId)
				}
				// 4.AlliancePaidBonus = 3. * AlliancePercent(30%)
				newRow.AlliancePaidBonus = newRow.TotalPaidBonus * (newRow.AlliancePercent / 100)
				// 5.AllianceIncome = 2. + A3. + 4.
				newRow.AllianceIncome = newRow.AllianceWinloseAmount + newRow.AllianceCommission + newRow.AlliancePaidBonus
				// END
				createList[dailyKey] = newRow
			}
			if len(createList) > 500 {
				// check exists
				dbList, _, err := repo.GetAllianceWinloseIncomeListByDailyKeyList(createList)
				if err != nil {
					// cant check
					return internalServerError(err)
				}
				// if exists, remove from createList
				for _, dbItem := range dbList {
					// Update if exists
					if newRow, ok := createList[dbItem.DailyKey]; ok {
						var updateRow model.AllianceWinlosePendingIncomeUpdateBody
						updateRow.TotalPlayAmount = newRow.TotalPlayAmount
						updateRow.TotalWinLoseAmount = newRow.TotalWinLoseAmount
						updateRow.CommissionPercent = newRow.CommissionPercent
						updateRow.AlliancePercent = newRow.AlliancePercent
						updateRow.TotalCommission = newRow.TotalCommission
						updateRow.AllianceWinloseAmount = newRow.AllianceWinloseAmount
						updateRow.AllianceCommission = newRow.AllianceCommission
						updateRow.TotalPaidBonus = newRow.TotalPaidBonus
						updateRow.AlliancePaidBonus = newRow.AlliancePaidBonus
						updateRow.AllianceIncome = newRow.AllianceIncome
						if err := repo.UpdatePendingAllianceWinloseIncome(dbItem.Id, updateRow); err != nil {
							log.Println(err)
						}
					}
					delete(createList, dbItem.DailyKey)
				}
				if len(createList) > 0 {
					if err := repo.CreateAllianceWinloseIncomeBulk(createList); err != nil {
						return err
					}
				}
				createList = make(map[string]model.AllianceWinloseIncomeCreateBody)
			}
		}
		// LEFTOVER
		if len(createList) > 0 {
			// check exists
			dbList, _, err := repo.GetAllianceWinloseIncomeListByDailyKeyList(createList)
			if err != nil {
				// cant check
				return internalServerError(err)
			}
			// if exists, remove from createList
			for _, dbItem := range dbList {
				// Update if exists
				if newRow, ok := createList[dbItem.DailyKey]; ok {
					var updateRow model.AllianceWinlosePendingIncomeUpdateBody
					updateRow.TotalPlayAmount = newRow.TotalPlayAmount
					updateRow.TotalWinLoseAmount = newRow.TotalWinLoseAmount
					updateRow.CommissionPercent = newRow.CommissionPercent
					updateRow.AlliancePercent = newRow.AlliancePercent
					updateRow.TotalCommission = newRow.TotalCommission
					updateRow.AllianceWinloseAmount = newRow.AllianceWinloseAmount
					updateRow.AllianceCommission = newRow.AllianceCommission
					updateRow.TotalPaidBonus = newRow.TotalPaidBonus
					updateRow.AlliancePaidBonus = newRow.AlliancePaidBonus
					updateRow.AllianceIncome = newRow.AllianceIncome
					if err := repo.UpdatePendingAllianceWinloseIncome(dbItem.Id, updateRow); err != nil {
						log.Println(err)
					}
				}
				delete(createList, dbItem.DailyKey)
			}
			if len(createList) > 0 {
				if err := repo.CreateAllianceWinloseIncomeBulk(createList); err != nil {
					return err
				}
			}
		}

		// Then Calc For user has Bonus but no playlog
		if userBonuslogList != nil && len(*userBonuslogList) > 0 {
			if err := cronRecalcAllianceWinloseIncomeFromBonus(repo, actionAt, statementDate, *userBonuslogList); err != nil {
				log.Println(err)
			}
		}

	}
	return nil
}

func (s cronService) CompareAllianceWinlosePendingIncomeUpdateBody(localData model.AllianceWinloseIncomeDailyKey, createRow model.AllianceWinloseIncomeCreateBody) bool {

	// Later : golang fastest way to compare struct
	// updateRow.TotalPlayAmount = newRow.TotalPlayAmount
	// updateRow.TotalWinLoseAmount = newRow.TotalWinLoseAmount
	// updateRow.CommissionPercent = newRow.CommissionPercent
	// updateRow.AlliancePercent = newRow.AlliancePercent
	// updateRow.TotalCommission = newRow.TotalCommission
	// updateRow.AllianceWinloseAmount = newRow.AllianceWinloseAmount
	// updateRow.AllianceCommission = newRow.AllianceCommission
	// updateRow.TotalPaidBonus = newRow.TotalPaidBonus
	// updateRow.AlliancePaidBonus = newRow.AlliancePaidBonus
	// updateRow.AllianceIncome = newRow.AllianceIncome
	localRow := model.AllianceWinloseIncomeCreateBody{
		// TotalPlayAmount:       localData.TotalPlayAmount,
		// TotalWinLoseAmount:    localData.TotalWinLoseAmount,
		// CommissionPercent:     localData.CommissionPercent,
		// AlliancePercent:       localData.AlliancePercent,
		// TotalCommission:       localData.TotalCommission,
		// AllianceWinloseAmount: localData.AllianceWinloseAmount,
		// AllianceCommission:    localData.AllianceCommission,
		// TotalPaidBonus:        localData.TotalPaidBonus,
		// AlliancePaidBonus:     localData.AlliancePaidBonus,
		// AllianceIncome:        localData.AllianceIncome,
	}
	newRow := model.AllianceWinloseIncomeCreateBody{
		TotalPlayAmount:       createRow.TotalPlayAmount,
		TotalWinLoseAmount:    createRow.TotalWinLoseAmount,
		CommissionPercent:     createRow.CommissionPercent,
		AlliancePercent:       createRow.AlliancePercent,
		TotalCommission:       createRow.TotalCommission,
		AllianceWinloseAmount: createRow.AllianceWinloseAmount,
		AllianceCommission:    createRow.AllianceCommission,
		TotalPaidBonus:        createRow.TotalPaidBonus,
		AlliancePaidBonus:     createRow.AlliancePaidBonus,
		AllianceIncome:        createRow.AllianceIncome,
	}

	localJson := helper.StructJson(localRow)
	remoteJson := helper.StructJson(newRow)
	// later : golang fastest way to compare json
	return localJson != remoteJson
}

func cronRecalcAllianceWinloseIncomeFromBonus(repo repository.AgentInfoRepository, actionAt time.Time, statementDate time.Time, bonusLogList map[int64]model.AllianceBonusLog) error {

	// Then Calc For user has Bonus but no playlog
	createFromBonusList := make(map[string]model.AllianceWinloseIncomeCreateBody)
	for _, row := range bonusLogList {
		// NOT CALCULATE IF USERID < 1 | NO_USER
		if row.UserId < 1 {
			continue
		}
		// in format D20231114U177
		dailyKey := fmt.Sprintf("D%sU%d", statementDate.Format("20060102"), row.UserId)
		if _, ok := createFromBonusList[dailyKey]; !ok {
			newRow := model.AllianceWinloseIncomeCreateBody{
				DailyKey:           dailyKey,
				UserId:             row.UserId,
				StatementDate:      statementDate.Format("2006-01-02"), // วันที่เล่น
				OfDate:             actionAt.Format("2006-01-02"),      // วันที่ตัดยอด
				TotalPlayAmount:    0,                                  // playlog
				TotalWinLoseAmount: 0,                                  // playlog
				AlliancePercent:    row.AlliancePercent,
				CommissionPercent:  row.CommissionPercent,
			}
			// Calc per row [20231120] ใส่สูตรรายได้
			// A1.TotalPlayAmount
			// 1.TotalWinLoseAmount
			// A2.TotalCommission = A1. * CommissionPercent(0.03%)
			newRow.TotalCommission = newRow.TotalPlayAmount * (newRow.CommissionPercent / 100)
			// 2.AllianceWinloseAmount = 1. * AlliancePercent(30%)
			if newRow.TotalWinLoseAmount < 0 {
				// They lose money, you get commission
				newRow.AllianceWinloseAmount = (math.Abs(newRow.TotalWinLoseAmount) * (newRow.AlliancePercent / 100))
			} else {
				// They win money, you lose commission (negative)
				newRow.AllianceWinloseAmount = -1 * (math.Abs(newRow.TotalWinLoseAmount) * (newRow.AlliancePercent / 100))
			}
			// A3. AllianceCommission = A2. - AlliancePercent(30%)
			newRow.AllianceCommission = newRow.TotalCommission - (newRow.TotalCommission * (newRow.AlliancePercent / 100))
			// 3.TotalPaidBonus from total user's bonus log from user_transaction
			newRow.TotalPaidBonus = 0
			if userBonuslog, ok := (bonusLogList)[row.UserId]; ok {
				newRow.TotalPaidBonus = -1 * userBonuslog.TotalBonusAmount
			}
			// 4.AlliancePaidBonus = 3. * AlliancePercent(30%)
			newRow.AlliancePaidBonus = newRow.TotalPaidBonus * (newRow.AlliancePercent / 100)
			// 5.AllianceIncome = 2. + A3. + 4.
			newRow.AllianceIncome = newRow.AllianceWinloseAmount + newRow.AllianceCommission + newRow.AlliancePaidBonus
			// END
			createFromBonusList[dailyKey] = newRow
		}
		if len(createFromBonusList) > 500 {
			// check exists
			dbList, _, err := repo.GetAllianceWinloseIncomeListByDailyKeyList(createFromBonusList)
			if err != nil {
				// cant check
				return internalServerError(err)
			}
			// if exists, remove from createFromBonusList
			for _, dbItem := range dbList {
				// Update if exists
				if newRow, ok := createFromBonusList[dbItem.DailyKey]; ok {
					var updateRow model.AllianceWinlosePendingIncomeUpdateBody
					updateRow.TotalPlayAmount = newRow.TotalPlayAmount
					updateRow.TotalWinLoseAmount = newRow.TotalWinLoseAmount
					updateRow.CommissionPercent = newRow.CommissionPercent
					updateRow.AlliancePercent = newRow.AlliancePercent
					updateRow.TotalCommission = newRow.TotalCommission
					updateRow.AllianceWinloseAmount = newRow.AllianceWinloseAmount
					updateRow.AllianceCommission = newRow.AllianceCommission
					updateRow.TotalPaidBonus = newRow.TotalPaidBonus
					updateRow.AlliancePaidBonus = newRow.AlliancePaidBonus
					updateRow.AllianceIncome = newRow.AllianceIncome
					if err := repo.UpdatePendingAllianceWinloseIncome(dbItem.Id, updateRow); err != nil {
						log.Println(err)
					}
				}
				delete(createFromBonusList, dbItem.DailyKey)
			}
			if len(createFromBonusList) > 0 {
				if err := repo.CreateAllianceWinloseIncomeBulk(createFromBonusList); err != nil {
					return err
				}
			}
			createFromBonusList = make(map[string]model.AllianceWinloseIncomeCreateBody)
		}
	}
	// LEFTOVER
	if len(createFromBonusList) > 0 {
		// check exists
		dbList, _, err := repo.GetAllianceWinloseIncomeListByDailyKeyList(createFromBonusList)
		if err != nil {
			// cant check
			return internalServerError(err)
		}
		// if exists, remove from createFromBonusList
		for _, dbItem := range dbList {
			// Update if exists
			if newRow, ok := createFromBonusList[dbItem.DailyKey]; ok {
				var updateRow model.AllianceWinlosePendingIncomeUpdateBody
				updateRow.TotalPlayAmount = newRow.TotalPlayAmount
				updateRow.TotalWinLoseAmount = newRow.TotalWinLoseAmount
				updateRow.CommissionPercent = newRow.CommissionPercent
				updateRow.AlliancePercent = newRow.AlliancePercent
				updateRow.TotalCommission = newRow.TotalCommission
				updateRow.AllianceWinloseAmount = newRow.AllianceWinloseAmount
				updateRow.AllianceCommission = newRow.AllianceCommission
				updateRow.TotalPaidBonus = newRow.TotalPaidBonus
				updateRow.AlliancePaidBonus = newRow.AlliancePaidBonus
				updateRow.AllianceIncome = newRow.AllianceIncome
				if err := repo.UpdatePendingAllianceWinloseIncome(dbItem.Id, updateRow); err != nil {
					log.Println(err)
				}
			}
			delete(createFromBonusList, dbItem.DailyKey)
		}
		if len(createFromBonusList) > 0 {
			if err := repo.CreateAllianceWinloseIncomeBulk(createFromBonusList); err != nil {
				return err
			}
		}
	}

	return nil
}

func (s cronService) ShowAllianceWinloseIncome(incomeId int64) (*model.AllianceWinloseIncome, *model.AllianceWinloseIncomeCreateBody, error) {

	oldRow, err := s.alRepo.GetAllianceWinloseIncomeById(incomeId)
	if err != nil {
		return nil, nil, err
	}

	setting, err := s.alRepo.GetUserAllianceSettingByUserId(oldRow.UserId)
	if err != nil {
		return nil, nil, err
	}

	newRow := model.AllianceWinloseIncomeCreateBody{
		DailyKey:           oldRow.DailyKey,
		UserId:             oldRow.UserId,
		StatementDate:      oldRow.StatementDate,
		OfDate:             oldRow.OfDate,
		TotalPlayAmount:    oldRow.TotalPlayAmount,
		TotalWinLoseAmount: oldRow.TotalWinLoseAmount,
		AlliancePercent:    setting.AlliancePercent,
		CommissionPercent:  setting.CommissionPercent,
	}
	// Calc per row [20231120] ใส่สูตรรายได้
	// A1.TotalPlayAmount
	// 1.TotalWinLoseAmount
	// A2.TotalCommission = A1. * CommissionPercent(0.03%)
	newRow.TotalCommission = newRow.TotalPlayAmount * (newRow.CommissionPercent / 100)
	// 2.AllianceWinloseAmount = 1. * AlliancePercent(30%)
	if newRow.TotalWinLoseAmount < 0 {
		// They lose money, you get commission
		newRow.AllianceWinloseAmount = (math.Abs(newRow.TotalWinLoseAmount) * (newRow.AlliancePercent / 100))
	} else {
		// They win money, you lose commission (negative)
		newRow.AllianceWinloseAmount = -1 * (math.Abs(newRow.TotalWinLoseAmount) * (newRow.AlliancePercent / 100))
	}
	// A3. AllianceCommission = A2. - AlliancePercent(30%)
	newRow.AllianceCommission = newRow.TotalCommission - (newRow.TotalCommission * (newRow.AlliancePercent / 100))
	// 3.TotalPaidBonus from total user's bonus log from user_transaction
	newRow.TotalPaidBonus = 0
	// if userBonuslog, ok := (*userBonuslogList)[row.UserId]; ok {
	// 	newRow.TotalPaidBonus = -1 * userBonuslog.TotalBonusAmount
	// }
	// 4.AlliancePaidBonus = 3. * AlliancePercent(30%)
	newRow.AlliancePaidBonus = newRow.TotalPaidBonus * (newRow.AlliancePercent / 100)
	// 5.AllianceIncome = 2. + A3. + 4.
	newRow.AllianceIncome = newRow.AllianceWinloseAmount + newRow.AllianceCommission + newRow.AlliancePaidBonus
	// END

	return oldRow, &newRow, nil
}

func buildBonusLog(repo repository.AgentInfoRepository, req model.CronAllianceIncomeBonusListequest) (*map[int64]model.AllianceBonusLog, error) {

	result := make(map[int64]model.AllianceBonusLog)

	// Get PlayLog by ofDate
	actionAt := time.Now()
	statementDate := actionAt.AddDate(0, 0, -1)
	if req.StatementDate != "" {
		statementAt, _ := time.Parse("2006-01-02", req.StatementDate)
		statementDate = statementAt
	}

	var query model.AllianceBonusLogListRequest
	// query.UserIds = req.UserIds
	query.StatementDate = statementDate.Format("2006-01-02")
	bonusLogList, total, err := repo.GetAllianceTotalBonusLogList(query)
	if err != nil {
		return nil, err
	}

	if total > 0 {
		for _, row := range bonusLogList {
			bonusAmount := row.BonusAmount
			if row.TypeId == model.CREDIT_TYPE_DEPOSIT && row.BonusAmount > 0 {
				bonusAmount = row.BonusAmount
			} else if row.TypeId == model.CREDIT_TYPE_PROMOTION_RETURN_LOSS && row.CreditAmount > 0 {
				bonusAmount = row.CreditAmount // Compatible
			} else if row.TypeId == model.CREDIT_TYPE_PROMOTION_RETURN_LOSS && row.BonusAmount > 0 {
				bonusAmount = row.BonusAmount
			} else if row.TypeId == model.CREDIT_TYPE_PROMOTION_RETURN_TURN && row.BonusAmount > 0 {
				bonusAmount = row.BonusAmount
			}
			// Only add if has bonusAmount
			if bonusAmount > 0 {
				if _, ok := result[row.UserId]; !ok {
					result[row.UserId] = model.AllianceBonusLog{
						UserId:            row.UserId,
						TotalBonusAmount:  bonusAmount,
						StatementDate:     statementDate.Format("2006-01-02"),
						RefUserId:         row.RefUserId,
						AlliancePercent:   row.AlliancePercent,
						CommissionPercent: row.CommissionPercent,
					}
				} else {
					// Add bonusAmount
					result[row.UserId] = model.AllianceBonusLog{
						UserId:            row.UserId,
						TotalBonusAmount:  result[row.UserId].TotalBonusAmount + bonusAmount,
						StatementDate:     statementDate.Format("2006-01-02"),
						RefUserId:         row.RefUserId,
						AlliancePercent:   row.AlliancePercent,
						CommissionPercent: row.CommissionPercent,
					}
				}
			}
		}
	}

	return &result, nil
}

func (s cronService) TestAddAllianceWinloseIncome(req model.TestAddAllianceIncomeRequest) error {

	// VALIDATE Get PlayLog by ofDate
	actionAt := time.Now()
	statementDate := actionAt.AddDate(0, 0, -1)
	if req.StatementDate != "" {
		statementAt, _ := time.Parse("2006-01-02", req.StatementDate)
		statementDate = statementAt             // วันที่เล่น
		actionAt = statementAt.AddDate(0, 0, 1) // วันที่ตัดยอด
	}

	createList := make(map[string]model.AllianceWinloseIncomeCreateBody)

	// NOT CALCULATE IF USERID < 1 | NO_USER
	if req.UserId < 1 {
		return nil
	}
	// in format D20231114U177
	dailyKey := fmt.Sprintf("D%sU%d", statementDate.Format("20060102"), req.UserId)

	oldRow, err := s.alRepo.GetAllianceWinloseIncomeByDailyKey(dailyKey)
	if err != nil && err.Error() == "record not found" {
		newRow := model.AllianceWinloseIncomeCreateBody{
			DailyKey:           dailyKey,
			UserId:             req.UserId,
			StatementDate:      statementDate.Format("2006-01-02"), // วันที่เล่น
			OfDate:             actionAt.Format("2006-01-02"),      // วันที่ตัดยอด
			TotalPlayAmount:    11,                                 // playlog
			TotalWinLoseAmount: 22,                                 // playlog
			AlliancePercent:    33,
			CommissionPercent:  44,
		}
		newRow.AllianceIncome = req.IncomeAmount
		createList[dailyKey] = newRow
	} else {
		// Update if exists
		if err := s.alRepo.TestUpdateAllianceWinloseIncome(oldRow.Id, req.IncomeAmount); err != nil {
			log.Println(err)
		}
	}

	if len(createList) > 0 {
		if err := s.alRepo.CreateAllianceWinloseIncomeBulk(createList); err != nil {
			return err
		}
	}

	return nil
}

func (s *cronService) CreateSystemLog(name string, req interface{}) error {

	// [SYSLOG] INIT
	_, err := s.repo.CreateSystemLog(model.SystemLogCreateBody{
		Name:   name,
		Status: "HACKING",
		JsonRequest: helper.StructJson(map[string]interface{}{
			"req": req,
		}),
	})
	if err != nil {
		log.Println(name, "CreateSystemLog.ERROR", err.Error())
	}
	return nil
}

func (s *cronService) CronDeleteLog() (string, error) {

	var messages []string

	// DELETE AGENT LOG
	err := s.repo.CronjobDeleteAgentLog()
	if err != nil {
		messages = append(messages, "CronjobDeleteAgentLog.ERROR")
	} else {
		messages = append(messages, "CronjobDeleteAgentLog.SUCCESS")
	}

	// DELETE BANK TRANSACTION LOG
	err = s.repo.CronjobDeleteBankTransactionLog()
	if err != nil {
		messages = append(messages, "CronjobDeleteBankTransactionLog.ERROR")
	} else {
		messages = append(messages, "CronjobDeleteBankTransactionLog.SUCCESS")
	}

	// DELETE PAYGATE HENG WEBHOOK
	err = s.repo.CronjobDeletePaygateHengWebhook()
	if err != nil {
		messages = append(messages, "CronjobDeletePaygateHengWebhook.ERROR")
	} else {
		messages = append(messages, "CronjobDeletePaygateHengWebhook.SUCCESS")
	}
	// DELETE PROMOTION WEB USER LOG
	err = s.repo.CronjobDeletePromotionWebUserLog()
	if err != nil {
		messages = append(messages, "CronjobDeletePromotionWebUserLog.ERROR")
	} else {
		messages = append(messages, "CronjobDeletePromotionWebUserLog.SUCCESS")
	}

	// DELETE USER LOGIN LOG
	err = s.repo.CronjobDeleteUserLoginLog()
	if err != nil {
		messages = append(messages, "CronjobDeleteUserLoginLog.ERROR")
	} else {
		messages = append(messages, "CronjobDeleteUserLoginLog.SUCCESS")
	}

	// DELETE WEBHOOK LOG
	err = s.repo.CronjobDeleteWebHookLog()
	if err != nil {
		messages = append(messages, "CronjobDeleteWebHookLog.ERROR")
	} else {
		messages = append(messages, "CronjobDeleteWebHookLog.SUCCESS")
	}

	// CronjobDeletePgHardCallBack() error
	err = s.repo.CronjobDeletePgHardCallBack()
	if err != nil {
		messages = append(messages, "CronjobDeletePgHardCallBack.ERROR")
	} else {
		messages = append(messages, "CronjobDeletePgHardCallBack.SUCCESS")
	}

	var errorMessage string
	if len(messages) > 0 {
		// messages have "ERROR" will save in errorMessage
		for _, v := range messages {
			if strings.Contains(v, "ERROR") {
				errorMessage += v + " || "
			}
		}
	}

	var responseMessage string
	if errorMessage != "" {
		responseMessage = errorMessage
	} else {
		responseMessage = "SUCCESS"
	}

	return responseMessage, nil
}

func (s cronService) CronCalculateAfAlOnly(statementDate string) (string, error) {

	return CronCalculateAfAlOnly(s.repo, statementDate)
}

func CronCalculateAfAlOnly(repo repository.AgentInfoRepository, statementDate string) (string, error) {

	if err := calculateAf(repo, statementDate); err != nil {
		log.Println("calculateAf.ERROR", err)
	}
	if err := calculateAl(repo, statementDate); err != nil {
		log.Println("calculateAl.ERROR", err)
	}

	return statementDate, nil
}

func (s *cronService) CronMigrateOldAff() ([]model.UserAffIncomeReponse, model.CronMigrateOldAffResponse, error) {

	var result model.CronMigrateOldAffResponse

	// statementDate := "2024-03-01" ถ้ามี รายการที่ Pending จะไม่สามารถทำได้ ให้ไป Expire มือ ในฐานข้อมูลเดิมก่อน
	if calcCount, err := s.repo.CountPendingAffUserIncomeLogList(); err != nil {
		return nil, result, err
	} else if calcCount > 0 {
		return nil, result, fmt.Errorf("CronMigrateOldAff.ERROR: Already Migrated")
	}

	// Get pending User Affiliate List with CommissionTotal > 0
	list, err := s.repo.GetPendingAffiliateIncomeList()
	if err != nil {
		return nil, result, err
	}

	for _, row := range list {
		// Check Each Customer
		fmt.Println("row", row.UserId, helper.StructJson(row))
		// SELECT * FROM user_affiliate WHERE commission_total > 0 AND (commission_current == commission_total AND commission_total = (first_deposit_bonus + bonus_share_total + commission_sport + commission_casino + commission_game));
		if row.CommissionCurrent == row.CommissionTotal && row.CommissionTotal == (row.FirstDepositBonus+row.BonusShareTotal+row.CommissionSport+row.CommissionCasino+row.CommissionGame+row.CommissionLottery+row.CommissionP2p+row.CommissionFinancial) {
			result.MatchTotalCount++
			// ยอดตรง ยังไม่เคยรับ แยกได้
			if err := s.repo.MigrateAffTransactionFull(row); err != nil {
				result.TotalErrorCount++
				log.Println("MigrateAffTransactionFull.ERROR", err)
			}
		} else {
			result.NotMatchCount++
			// รับแล้ว รวมไว้เป็น 1 รายการ ให้ตรงกับยอดรวม
			if err := s.repo.MigrateAffTransactionEtc(row); err != nil {
				result.TotalErrorCount++
				log.Println("MigrateAffTransactionEtc.ERROR", err)
			}
		}
		result.TotalMigrated++
	}

	return list, result, nil
}

func (s *cronService) MigrateOldAgcPlaylogStatus() error {

	// only old game
	productIds := []int{model.AGENT_PRODUCT_SPORT, model.AGENT_PRODUCT_CASINO, model.AGENT_PRODUCT_GAME}

	startAt, err := time.Parse("2006-01-02", "2024-08-01")
	if err != nil {
		return err
	}

	// loop 1 month
	for i := 0; i < 70; i++ {
		startDate := startAt.AddDate(0, 0, +i).Format("2006-01-02")
		log.Println("MigrateOldAgcPlaylogStatus.startDate =", startDate)
		// not today
		if startDate == time.Now().Format("2006-01-02") || startDate == time.Now().AddDate(0, 0, -1).Format("2006-01-02") {
			log.Println("MigrateOldAgcPlaylogStatus.StopAtToday")
			return nil
		}
		if err := s.repo.MigrateOldAgcPlaylogStatus(helper.StructJson(productIds), startDate); err != nil {
			log.Println("MigrateOldAgcPlaylogStatus.MigrateOldAgcPlaylogStatus ERROR=", err)
		}
	}

	log.Println("Finish MigrateOldAgcPlaylogStatus")
	return nil
}

func (s cronService) MigrateAffiliateUserAllLevel(userId int64) error {

	// only old game
	if err := s.repo.MigrateAffiliateUserAllLevel(userId); err != nil {
		log.Println("MigrateAffiliateUserAllLevel ERROR=", err)
		return err
	}
	return nil
}

func (s cronService) MigrateAffiliateTotalMember(refBy int64) error {

	// member_deposit_total == user_first_deposit.user_id

	if err := s.repo.UpdateTotalMemberFirstDeposit(refBy); err != nil {
		log.Println("MigrateAffiliateTotalMember.UpdateTotalMemberFirstDeposit ERROR=", err)
		return err
	}

	return nil
}

func (s cronService) CreateLotteryPlayLogWebhook(req model.LotteryWebhookRequest) error {

	var createBody model.WebhookLogCreateBody
	createBody.LogType = "LOTTERY_PLAYLOG_CALLBACK"
	createBody.JsonPayload = req.JsonPayload
	if _, err := s.repo.CreateWebhookLog(createBody); err != nil {
		return internalServerError(err)
	}

	// Unmarshal into Array
	var records []model.LotteryWebhookPlaylogRecord
	if err := json.Unmarshal([]byte(req.JsonPayload), &records); err != nil {
		log.Println("CreateLotteryPlayLogWebhook.ERROR.Unmarshal", err)
		return internalServerError(err)
	}

	if len(records) > 0 {
		if err := s.CreateTodayWinLoseLottery(records); err != nil {
			log.Println("CreateLotteryPlayLogWebhook.ERROR.CreateTodayWinLoseLottery", err)
			return internalServerError(err)
		}
	}

	return nil
}

func (s cronService) CreateTodayWinLoseLottery(records []model.LotteryWebhookPlaylogRecord) error {

	actionAt := time.Now().UTC()
	bkkLoc := time.FixedZone("Asia/Bangkok", 7*60*60)
	actionInBkk := actionAt.In(bkkLoc)
	statementDate := actionInBkk.Format("2006-01-02")
	productId := model.AGENT_PRODUCT_LOTTERY
	path := "CreateTodayWinLoseLottery"

	var cronError error
	var createList = make(map[string]model.UserTodayPlaylogCreateBody, 0)
	var uniqueList []string
	var totalCreated int64
	for _, j := range records {

		var tempRow model.UserTodayPlaylogCreateBody
		// {memberCode}_{statementDate}_{productId}
		uniqueKey := fmt.Sprintf("%v_%v_%v", j.UserId, strings.Replace(statementDate, "-", "", -1), productId)
		tempRow.StatementDate = statementDate
		tempRow.DailyKey = uniqueKey
		tempRow.UserId = j.UserId

		// } else if productId == model.AGENT_PRODUCT_LOTTERY {
		tempRow.TurnLottery = j.TurnOver
		tempRow.WinLoseLottery = j.WinLoss
		tempRow.ValidAmountLottery = j.ValidAmount

		tempRow.TurnTotal = tempRow.TurnSport + tempRow.TurnCasino + tempRow.TurnGame + tempRow.TurnLottery + tempRow.TurnP2p + tempRow.TurnFinancial
		tempRow.WinLoseTotal = tempRow.WinLoseSport + tempRow.WinLoseCasino + tempRow.WinLoseGame + tempRow.WinLoseLottery + tempRow.WinLoseP2p + tempRow.WinLoseFinancial
		tempRow.ValidAmountTotal = tempRow.ValidAmountSport + tempRow.ValidAmountCasino + tempRow.ValidAmountGame + tempRow.ValidAmountLottery + tempRow.ValidAmountP2p + tempRow.ValidAmountFinancial
		createList[uniqueKey] = tempRow

		// Bulk insert
		if len(createList) >= 100 {
			// check exists
			for k := range createList {
				uniqueList = append(uniqueList, k)
			}
			if dbList, _, err := s.repo.GetTodayPlayLogKeyList(uniqueList); err != nil {
				cronError = err // cant check
			} else {
				// if exists, remove from createList
				for index, dbReord := range dbList {
					dbKey := dbReord.DailyKey
					err := s.UpdateOnlyUserTodayLotteryPlaylog(createList[dbKey], dbList[index])
					if err != nil {
						log.Println(err)
						cronError = err
					}
					delete(createList, dbKey)
				}
				if len(createList) > 0 {
					err := s.repo.CreateUserTodayPlaylogBulkDirect(createList)
					if err != nil {
						log.Println(err)
						cronError = err
						continue
					}
					totalCreated += int64(len(createList))
				}
			}
			createList = make(map[string]model.UserTodayPlaylogCreateBody, 0)
			uniqueList = make([]string, 0)
		}
	}

	// fmt.Println("createList", helper.StructJson(createList))

	// LEFTOVER
	if len(createList) > 0 {
		// check exists
		for k := range createList {
			uniqueList = append(uniqueList, k)
		}
		if dbList, _, err := s.repo.GetTodayPlayLogKeyList(uniqueList); err != nil {
			cronError = err // cant check
		} else {
			// if exists, remove from createList
			for index, dbReord := range dbList {
				dbKey := dbReord.DailyKey
				err := s.UpdateOnlyUserTodayLotteryPlaylog(createList[dbKey], dbList[index])
				if err != nil {
					log.Println(err)
					cronError = err
				}
				delete(createList, dbKey)
			}
			if len(createList) > 0 {
				// [20240408] DISABLE LOG
				// for _, row := range createList {
				// 	// LOG_ON_DIFF
				// 	var body model.WebhookLogCreateBody
				// 	body.JsonRequest = helper.StructJson(row)
				// 	body.JsonPayload = "{}"
				// 	body.LogType = "CreateUserTodayPlaylogBulk"
				// 	body.Status = row.DailyKey
				// 	if _, err := s.repo.CreateWebhookLog(body); err != nil {
				// 		log.Println("CreateWebhookLog", err)
				// 	}
				// }
				err := s.repo.CreateUserTodayPlaylogBulkDirect(createList)
				if err != nil {
					log.Println(err)
					cronError = err
				}
				totalCreated += int64(len(createList))
			}
		}
		totalCreated += int64(len(createList))
	}

	if cronError != nil {
		log.Println("runAgcTodayWinLoseLottery : ", path, "ERROR", cronError)
	}
	log.Println("Finish runAgcTodayWinLoseLottery : ", path)
	return cronError
}

func (s cronService) UpdateOnlyUserTodayLotteryPlaylog(createBody model.UserTodayPlaylogCreateBody, dbRecord model.UserTodayPlaylogResponse) error {

	// Check If Lottery is Changed, Update Lottery and Total
	// fmt.Println("createBody", helper.StructJson(createBody))
	// fmt.Println("dbRecord", helper.StructJson(dbRecord))

	createRow := model.UserTodayPlaylogResponse{
		Id:                 dbRecord.Id,
		TurnLottery:        createBody.TurnLottery,
		WinLoseLottery:     createBody.WinLoseLottery,
		ValidAmountLottery: createBody.ValidAmountLottery,
	}
	dbRow := model.UserTodayPlaylogResponse{
		Id:                 dbRecord.Id,
		TurnLottery:        dbRecord.TurnLottery,
		WinLoseLottery:     dbRecord.WinLoseLottery,
		ValidAmountLottery: dbRecord.ValidAmountLottery,
	}
	localJson := helper.StructJson(createRow)
	remoteJson := helper.StructJson(dbRow)
	// log.Println("localJson", localJson)
	// log.Println("remoteJson", remoteJson)
	// later : golang fastest way to compare json
	if localJson != remoteJson {
		// LOG_ON_DIFF
		var updateBody model.UserTodayPlaylogUpdateBody
		updateBody.Id = dbRecord.Id
		updateBody.TurnLottery = &createBody.TurnLottery
		updateBody.WinLoseLottery = &createBody.WinLoseLottery
		updateBody.ValidAmountLottery = &createBody.ValidAmountLottery
		// Total
		turnTotal := dbRecord.TurnSport + dbRecord.TurnCasino + dbRecord.TurnGame + createBody.TurnLottery + dbRecord.TurnP2p + dbRecord.TurnFinancial
		updateBody.TurnTotal = &turnTotal
		winLoseTotal := dbRecord.WinLoseSport + dbRecord.WinLoseCasino + dbRecord.WinLoseGame + createBody.WinLoseLottery + dbRecord.WinLoseP2p + dbRecord.WinLoseFinancial
		updateBody.WinLoseTotal = &winLoseTotal
		validAmountTotal := dbRecord.ValidAmountSport + dbRecord.ValidAmountCasino + dbRecord.ValidAmountGame + createBody.ValidAmountLottery + dbRecord.ValidAmountP2p + dbRecord.ValidAmountFinancial
		updateBody.ValidAmountTotal = &validAmountTotal
		if err := s.repo.UpdateUserTodayPlaylog(updateBody); err != nil {
			log.Println(err)
		}
	}
	// log.Println("UpdateUserTodayPlaylog : No need update")
	return nil
}

func runOldAgentCtwToAgcSimpleWinLose(repo repository.AgentInfoRepository, productId int, statementDate string) {

	path := fmt.Sprintf("simplewinloseCTW%v", productId)
	log.Printf("Running simplewinloseCTW %v date %v", path, statementDate)

	var logQuery model.ApiStatusRequest
	logQuery.Path = path
	logQuery.StatementDate = statementDate
	apiStatus, err := repo.AgcGetApistatus(logQuery)
	if err != nil {
		log.Println(err)
		return
	}
	if apiStatus.IsFailed == 1 || apiStatus.IsSuccess == 1 {
		log.Println("simplewinloseCTW ", path, " Already run.")
		return
	}

	isHasError := false
	var logs []model.AgentPlayLog
	members := []string{}
	var body model.AgentCtwCallbackSummaryRequest
	body.StatementDate = statementDate
	body.PageSize = 250

	for i := 0; ; i++ {

		body.PageIndex = i + 1
		time.Sleep(5 * time.Second)
		list, err := repo.GetAgentCtwCallback(body)
		if err != nil {
			isHasError = true
			if err2 := repo.AgcUpdateFailed(apiStatus.Id, i); err2 != nil {
				log.Println(err2)
			}
			log.Println(err)
			break
		}

		if len(list) <= 0 {
			log.Println("ZERO==GOTALL")
			break
		}

		var obj model.AgentPlayLog
		obj.Date = statementDate
		for _, j := range list {
			//reset
			obj.TurnSport = 0
			obj.WinLoseSport = 0
			obj.TurnCasino = 0
			obj.WinLoseCasino = 0
			obj.TurnGame = 0
			obj.WinLoseGame = 0

			obj.TurnLottery = 0
			obj.WinLoseLottery = 0
			obj.TurnP2p = 0
			obj.WinLoseP2p = 0
			obj.TurnFinancial = 0
			obj.WinLoseFinancial = 0

			obj.ValidAmountSport = 0
			obj.ValidAmountCasino = 0
			obj.ValidAmountGame = 0
			obj.ValidAmountLottery = 0
			obj.ValidAmountP2p = 0
			obj.ValidAmountFinancial = 0

			obj.TurnTotal = 0
			obj.WinLoseTotal = 0
			obj.ValidAmountTotal = 0

			members = append(members, j.MemberCode)
			obj.Player = j.MemberCode
			if productId == model.AGENT_PRODUCT_GAME {
				obj.TurnGame = j.TotalBet
				obj.WinLoseGame = j.TotalWinlose
				obj.ValidAmountGame = j.TotalBet
			}

			obj.TurnTotal = obj.TurnSport + obj.TurnCasino + obj.TurnGame + obj.TurnLottery + obj.TurnP2p + obj.TurnFinancial
			obj.WinLoseTotal = obj.WinLoseSport + obj.WinLoseCasino + obj.WinLoseGame + obj.WinLoseLottery + obj.WinLoseP2p + obj.WinLoseFinancial
			obj.ValidAmountTotal = obj.ValidAmountSport + obj.ValidAmountCasino + obj.ValidAmountGame + obj.ValidAmountLottery + obj.ValidAmountP2p + obj.ValidAmountFinancial
			logs = append(logs, obj)
		}

		// Bulk insert Every Page.
		if len(members) > 0 {
			userList, err := repo.GetUserIdByMemberList(members)
			if err != nil {
				log.Println(err)
			}
			if len(members) > 0 {
				for i, v := range logs {
					for _, j := range userList {
						if v.Player == j.MemberCode {
							logs[i].UserID = j.Id
							break
						}
					}
				}
				if err = repo.InsertAgcPlayLog(logs, path, body.PageIndex, true); err != nil {
					isHasError = true
					if err := repo.AgcUpdateFailed(apiStatus.Id, apiStatus.Page); err != nil {
						log.Println(err)
					}
				}
			}
			// RESET
			logs = []model.AgentPlayLog{}
			members = []string{}
		}
	}

	if len(members) > 0 {
		userList, err := repo.GetUserIdByMemberList(members)
		if err != nil {
			log.Println(err)
		}
		if len(members) > 0 {
			for i, v := range logs {
				for _, j := range userList {
					if v.Player == j.MemberCode {
						logs[i].UserID = j.Id
						break
					}
				}
			}
			if err = repo.InsertAgcPlayLog(logs, path, body.PageIndex, true); err != nil {
				isHasError = true
				if err := repo.AgcUpdateFailed(apiStatus.Id, apiStatus.Page); err != nil {
					log.Println(err)
				}
			}
		}
	}

	time.Sleep(5 * time.Second)
	if !isHasError {
		if err := repo.AgcUpdateSuccess(apiStatus.Id); err != nil {
			log.Println(err)
		}
		// if err := s.repo.UpdateAgcPlaylogStatus(productId, statementDate, "DONE"); err != nil {
		// 	log.Println(err)
		// }
	}

	log.Println("Finish runOldAgcSimpleWinLose : ", path)
}

func runAgentCtwToAgcSimpleWinLose(repo repository.AgentInfoRepository, productId int, statementDate string, force bool) {

	actionAt := time.Now()
	path := fmt.Sprintf("simplewinloseAgentCtw%v", productId)
	completedPath := fmt.Sprintf("completed_%v", productId)
	var cronError error
	log.Printf("Running runAgentCtwToAgcSimpleWinLose-%v date %v", path, statementDate)

	// RUN AFTER 12.30
	bbkLoc := time.FixedZone("Asia/Bangkok", 7*60*60)
	runTime := actionAt.In(bbkLoc)
	if runTime.Hour() < 12 && runTime.Minute() < 30 {
		return
	}

	// get completed run
	completedStatus, err := repo.GetUserPlaylogCompletedStatus(completedPath, statementDate)
	if err != nil {
		if err.Error() != "record not found" {
			log.Println(err)
			return
		}
	}
	if completedStatus != nil {
		if err := repo.UpdateAgcPlaylogStatus(productId, statementDate, "DONE"); err != nil {
			log.Println(err)
		}
	}
	if completedStatus != nil && !force {
		// log.Printf(path + " was Completed")
		log.Printf("runAgentCtwToAgcSimpleWinLose-%v was already Completed", path)
		return
	}

	// var logQuery model.ApiStatusRequest
	// logQuery.Path = path
	// logQuery.StatementDate = statementDate
	// apiStatus, err := s.repo.GetApistatus(logQuery)
	// if err != nil {
	// 	log.Println(err)
	// 	return
	// }
	// if apiStatus.IsFailed == 1 || apiStatus.IsSuccess == 1 {
	// 	log.Printf("apiStatus was Completed")
	// 	return
	// }

	isHasError := false
	var createList = make(map[string]model.UserPlaylogCreateBody, 0)
	var uniqueList []string
	var memberCodeList []string
	var totalCreated int64

	var body model.AgentCtwCallbackSummaryRequest
	body.StatementDate = statementDate
	body.PageSize = 250

	for i := 0; ; i++ {

		body.PageIndex = i + 1

		time.Sleep(5 * time.Second)
		list, err := repo.GetAgentCtwCallback(body)
		// INSERT AGENT LOG FIRST !
		if errOnAgcSimpleWinLose := repo.CreateUserPlaylogStatus(model.UserPlaylogStatusCreateBody{
			StatementDate: statementDate,
			Path:          path,
			Page:          body.PageIndex,
			// OutMessage:     list.Message,
			// OutTotal:       int64(list.Result.Total),
			// OutJsonError:   helper.StructJson(list.Error),
			// OutJsonSummary: helper.StructJson(list.Result.Summary),
			// OutTargetUrl:   helper.StructJson(list.TargetUrl),
		}); errOnAgcSimpleWinLose != nil {
			log.Println(errOnAgcSimpleWinLose)
			cronError = errOnAgcSimpleWinLose
		}
		if err != nil {
			cronError = err
			log.Println(err)
			break
		}

		if len(list) <= 0 {
			log.Println("ZERO==GOTALL")
			break
		}

		for _, j := range list {

			var tempRow model.UserPlaylogCreateBody
			// DAILY_KEY = {memberCode}_{statementDate}_{productId}
			uniqueKey := fmt.Sprintf("%v_%v_CTW%v", j.MemberCode, strings.Replace(statementDate, "-", "", -1), productId)
			tempRow.StatementDate = statementDate
			tempRow.DailyKey = uniqueKey

			memberCodeList = append(memberCodeList, j.MemberCode)
			tempRow.MemberCode = j.MemberCode

			tempRow.TurnGame = j.TotalBet
			tempRow.WinLoseGame = j.TotalWinlose
			tempRow.ValidAmountGame = j.TotalBet

			tempRow.TurnTotal = tempRow.TurnSport + tempRow.TurnCasino + tempRow.TurnGame + tempRow.TurnLottery + tempRow.TurnP2p + tempRow.TurnFinancial
			tempRow.WinLoseTotal = tempRow.WinLoseSport + tempRow.WinLoseCasino + tempRow.WinLoseGame + tempRow.WinLoseLottery + tempRow.WinLoseP2p + tempRow.WinLoseFinancial
			tempRow.ValidAmountTotal = tempRow.ValidAmountSport + tempRow.ValidAmountCasino + tempRow.ValidAmountGame + tempRow.ValidAmountLottery + tempRow.ValidAmountP2p + tempRow.ValidAmountFinancial
			createList[uniqueKey] = tempRow

			// Bulk insert
			if len(createList) >= 250 {
				// check exists
				for k := range createList {
					uniqueList = append(uniqueList, k)
				}
				if dbList, _, err := repo.GetPlayLogKeyList(uniqueList); err != nil {
					cronError = err // cant check
				} else {
					// if exists, remove from createList
					for _, dbKey := range dbList {
						delete(createList, dbKey)
					}
					if len(createList) > 0 {
						if err := repo.CreateUserPlaylogBulk(createList, memberCodeList); err != nil {
							log.Println(err)
							cronError = err
							continue
						}
						if err := repo.IncreaseUserTotalTurnForUserTier(createList, memberCodeList); err != nil {
							log.Println(err)
						}
						totalCreated += int64(len(createList))
					}
				}
				createList = make(map[string]model.UserPlaylogCreateBody, 0)
				uniqueList = make([]string, 0)
				memberCodeList = make([]string, 0)
			}
		}
	}

	// LEFTOVER
	if len(createList) > 0 {
		// check exists
		for k := range createList {
			uniqueList = append(uniqueList, k)
		}
		if dbList, _, err := repo.GetPlayLogKeyList(uniqueList); err != nil {
			cronError = err // cant check
		} else {
			// if exists, remove from createList
			for _, dbKey := range dbList {
				delete(createList, dbKey)
			}
			if len(createList) > 0 {
				if err := repo.CreateUserPlaylogBulk(createList, memberCodeList); err != nil {
					log.Println(err)
				}
				if err := repo.IncreaseUserTotalTurnForUserTier(createList, memberCodeList); err != nil {
					log.Println(err)
				}
				totalCreated += int64(len(createList))
			}
		}
	}

	if !isHasError {
		// INSERT AGENT LOG FIRST !
		if err := repo.CreateUserPlaylogStatus(model.UserPlaylogStatusCreateBody{
			StatementDate: statementDate,
			Path:          completedPath,
			Page:          body.PageIndex,
			OutTotal:      totalCreated,
			OutJsonError:  helper.StructJson(cronError),
		}); err != nil {
			log.Println(err)
		}
		if err := repo.UpdateAgcPlaylogStatus(productId, statementDate, "DONE"); err != nil {
			log.Println(err)
		}
	} else {
		if err := repo.UpdateAgcPlaylogStatus(productId, statementDate, "ERROR"); err != nil {
			log.Println(err)
		}
	}

	log.Println("Finish runAgcSimpleWinLose : ", path)
}
