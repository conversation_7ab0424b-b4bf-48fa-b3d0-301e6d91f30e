package service

import (
	"bytes"
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"image/png"
	"log"
	"os"
	"strings"
	"time"

	"github.com/skip2/go-qrcode"
	"gorm.io/gorm"
)

type BizpayService interface {
	// Bizpay
	CreateBizpayWebhook(req model.BizpayWebhookRequest) (*int64, error)
	GetBizpayWebDepositAccount() (*model.BizpayCustomerDepositInfo, error)
	CreateBizpayDeposit(req model.BizpayDepositCreateRequest) (*model.BizpayOrderWebResponse, error)
	CreateBizpayWithdraw(req model.BizpayWithdrawCreateRequest) (*int64, error)
	// BizpayCheckBalance() (*model.BizpayCheckBalanceRemoteResponse, error)
	CancelWithdrawFromBizpay(transId int64, adminId int64) error
	CreateBizpayDepositWebhook(req model.BizpayWebhookRequest) (*int64, error)
	CreateBizpayWithdrawWebhook(req model.BizpayWebhookRequest) (*int64, error)
	// SysLog
	CreateSystemLog(name string, req interface{}) error
}

type paygateBizpayService struct {
	sharedDb                  *gorm.DB
	repo                      repository.BizpayRepository
	activityLuckyWheelService ActivityLuckyWheelService
	promotionWebService       PromotionWebService
	notiService               NotificationService
}

func NewBizpayService(
	sharedDb *gorm.DB,
	repo repository.BizpayRepository,
	activityLuckyWheelService ActivityLuckyWheelService,
	promotionWebService PromotionWebService,
	notiService NotificationService,
) BizpayService {
	return &paygateBizpayService{sharedDb, repo, activityLuckyWheelService, promotionWebService, notiService}
}

func (s paygateBizpayService) ConfirmDepositTransaction(id int64, req model.BankConfirmDepositRequest) error {

	record, err := s.repo.GetBankTransactionById(id)
	if err != nil {
		return internalServerError(err)
	}
	if record.TransactionStatusId != model.TRANS_STATUS_PENDING {
		return badRequest("Transaction is not pending")
	}
	if record.TransactionTypeId != model.TRANSACTION_TYPE_DEPOSIT && record.TransactionTypeId != model.TRANSACTION_TYPE_BONUS {
		return badRequest("Transaction is not deposit")
	}
	jsonBefore, _ := json.Marshal(record)

	var updateData model.BankDepositTransactionConfirmBody
	updateData.TransactionStatusId = model.TRANS_STATUS_DEPOSIT_PENDING_CREDIT
	updateData.ConfirmedAt = req.ConfirmedAt
	updateData.ConfirmedByAdminId = req.ConfirmedByAdminId

	// if record.IsAutoCredit && record.TransferAt != nil {
	// 	seconds := time.Now().UTC().Sub(record.TransferAt.UTC()).Seconds()
	// 	autoProcessTimer := fmt.Sprintf("%.2f", seconds)
	// 	updateData.AutoProcessTimer = &autoProcessTimer
	// }

	var createBody model.CreateBankTransactionActionBody
	createBody.ActionKey = fmt.Sprintf("DCF_STATE#%d", record.Id)
	createBody.TransactionId = record.Id
	createBody.UserId = record.UserId
	createBody.TransactionTypeId = record.TransactionTypeId
	createBody.FromAccountId = record.FromAccountId
	createBody.ToAccountId = record.ToAccountId
	createBody.JsonBefore = string(jsonBefore)
	if req.TransferAt == nil {
		createBody.TransferAt = record.TransferAt
	} else {
		TransferAt := req.TransferAt
		createBody.TransferAt = TransferAt
		updateData.TransferAt = *TransferAt
	}
	if req.SlipUrl != nil {
		createBody.SlipUrl = *req.SlipUrl
	}
	createBody.CreditAmount = record.CreditAmount
	if req.BonusAmount != nil {
		createBody.BonusAmount = *req.BonusAmount
	}
	createBody.ConfirmedAt = req.ConfirmedAt
	createBody.ConfirmedByAdminId = req.ConfirmedByAdminId
	if actionId, err := s.repo.CreateTransactionAction(createBody); err == nil {
		// do nothing ?
		if err := s.repo.ConfirmPendingDepositTransaction(id, updateData); err != nil {
			if err := s.repo.RollbackTransactionAction(*actionId); err == nil {
				return internalServerError(err)
			}
			return internalServerError(err)
		}
	} else {
		return internalServerError(err)
	}
	return nil
}

func (s paygateBizpayService) CreateBizpayWebhook(req model.BizpayWebhookRequest) (*int64, error) {

	var createBody model.BizpayWebhookCreateBody
	createBody.Name = "BIZPAY_CALLBACK"
	createBody.JsonPayload = req.JsonPayload
	insertId, err := s.repo.CreateBizpayWebhook(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// just for log
	// MainFlow use Deposit and Withdraw webhook.
	var remoteResp model.BizpayWebhookResponse
	errJson := json.Unmarshal([]byte(req.JsonPayload), &remoteResp)
	if errJson != nil {
		log.Println("Error unmarshal json", errJson)
		return nil, internalServerError(errJson)
	}
	log.Println("CreateBizpayWebhook.BizpayWebhookResponse=", helper.StructJson(remoteResp))

	return insertId, nil
}

func (s paygateBizpayService) GetBizpayWebDepositAccount() (*model.BizpayCustomerDepositInfo, error) {

	var result model.BizpayCustomerDepositInfo

	pgAccount, err := s.GetBizpayAccount()
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}
	if !pgAccount.IsDepositEnabled {
		return nil, badRequest("PAYGATE_DEPOSIT_NOT_ENABLED")
	}

	result.Name = pgAccount.Name
	if pgAccount.DisplayName == "" {
		result.Name = "เติมเงินผ่าน QR Code"
	} else {
		result.DisplayName = pgAccount.DisplayName
	}
	result.ShopName = pgAccount.ShopName
	result.MinAmount = model.BIZPAY_DEFMIN_DEPOSIT_AMOUNT
	result.MaxAmount = model.BIZPAY_DEFMAX_DEPOSIT_AMOUNT

	return &result, nil
}

func (s paygateBizpayService) GetBizpayAccount() (*model.PaygateAccountResponse, error) {

	return GetBizpayAccount(s.repo)
}

func GetBizpayAccount(repo repository.BizpayRepository) (*model.PaygateAccountResponse, error) {

	pgAccount, err := repo.GetPaygateAccountByProviderId(model.PAYGATE_MERCHANT_ID_BIZPAY)
	if err != nil {
		return nil, internalServerError(err)
	}
	return pgAccount, nil
}

func GetBizpayCustomerBank(bankCode string) (string, error) {

	remoteBankCode := ""
	uBankCode := strings.ToUpper(strings.TrimSpace(bankCode))

	switch uBankCode {
	case "000", "PROMPTPAY":
		remoteBankCode = "000"
	case "002", "BBL":
		remoteBankCode = "BBL" // "3" "กรุงเทพ" "bbl"
	case "004", "KBANK":
		remoteBankCode = "KBANK" // "1" "กสิกรไทย" "kbank"
	case "006", "KTB":
		remoteBankCode = "KTB" // "5"	"กรุงไทย"	"ktb"
	case "011", "TMB":
		remoteBankCode = "TMB"
	case "014", "SCB":
		remoteBankCode = "SCB" // "2" "ไทยพาณิชย์" "scb"
	case "022", "CIMB":
		remoteBankCode = "CIMB" // "13"	"ซีไอเอ็มบี"	"cimb"
	case "024", "UOB":
		remoteBankCode = "UOB" // "11"	"ยูโอบี"	"uob"
	case "025", "BAY":
		remoteBankCode = "BAY" // "4"	"กรุงศรี"	"bay"
	case "030", "GSB":
		remoteBankCode = "GSB" // "7"	"ออมสิน"	"gsb"
	case "033", "GHB":
		remoteBankCode = "GHB" // "10"	"อาคารสงเคราะห์"	"ghb"
	case "034", "BAAC":
		remoteBankCode = "BAAC" // "8"	"ธกส"	"baac"
	case "067", "TISCO":
		remoteBankCode = "TISCO" // // "17"	"ทิสโก้"	"tisco"
	case "069", "KKP":
		remoteBankCode = "KKP" // "9"	"เกียรตินาคิน"	"kkp"
	case "071", "TCRB":
		remoteBankCode = "TCRB" // ไทยเครดิต
	case "073", "LHBANK", "LH":
		remoteBankCode = "LHBANK" //"12"	"แลนด์ แอนด์ เฮ้าส์"	"lh"
	case "074", "TTB":
		remoteBankCode = "TTB" // "6"	"ทีเอ็มบีธนชาต"	"ttb"
	case "USDT":
		remoteBankCode = "USDT"
	default:
		// // "14"	"เอชเอสบีซี"	"hsbc"
		// "15"	"ไอซีบีซี"	"icbc"
		// "16"	"ธนาคารอิสลาม"	"isbt"
		// "18"	"ซิตี้แบงก์"	"citi"
		// "19"	"สแตนดาร์ดชาร์เตอร์ด"	"scbt"
		// และอื่นๆ หลังจากนี้ทั้งหมด
		return "", errors.New("USER_BANK_NOT_SUPPORTED")
	}

	return remoteBankCode, nil
}

func (s paygateBizpayService) CreateBizpayDeposit(req model.BizpayDepositCreateRequest) (*model.BizpayOrderWebResponse, error) {

	var result model.BizpayOrderWebResponse

	// Ruled by Provider
	if req.Amount < model.BIZPAY_DEFMIN_DEPOSIT_AMOUNT || req.Amount > model.BIZPAY_DEFMAX_DEPOSIT_AMOUNT {
		log.Println("req.Amount", req.Amount)
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}

	pgAccount, err := s.GetBizpayAccount()
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}
	if !pgAccount.IsDepositEnabled {
		return nil, badRequest("PAYGATE_DEPOSIT_NOT_ENABLED")
	}
	// Prerequisites
	if pgAccount.ApiEndPoint == "" || pgAccount.PartnerKey == "" || pgAccount.AccessKey == "" || pgAccount.SecretKey == "" || pgAccount.MerchantId == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}

	// [********] get previse deposit order in last 5 minutes
	if pOrder, err := s.repo.CheckBizpayDepositOrderInLast5Minutes(req.UserId, req.Amount); err != nil {
		if err != gorm.ErrRecordNotFound {
			return nil, internalServerError(err)
		}
	} else if pOrder != nil {
		actionAtUtc := time.Now().UTC()
		if pOrder.CreatedAt.Add(time.Minute * 5).After(actionAtUtc) {
			result.UserId = pOrder.UserId
			result.OrderNo = pOrder.OrderNo
			result.Amount = pOrder.Amount
			result.TransferAmount = pOrder.TransferAmount
			result.TransactionStatus = *pOrder.TransactionStatus
			result.QrCode = pOrder.QrPromptpay
			// result.PaymentPageUrl = pOrder.PaymentPageUrl
			result.CreatedAt = pOrder.CreatedAt

			imgData, err := qrcode.Encode(pOrder.QrPromptpay, qrcode.Medium, 256)
			if err != nil {
				// return nil, fmt.Errorf("unable to encode png: %w", err)
				return &result, nil
			}
			// encode to base64
			img, err := png.Decode(bytes.NewReader(imgData))
			if err != nil {
				// return nil, fmt.Errorf("unable to decode jpeg: %w", err)
				return &result, nil
			}
			var buf bytes.Buffer
			if err := png.Encode(&buf, img); err != nil {
				// return nil, fmt.Errorf("unable to encode png: %w", err)
				return &result, nil
			}
			result.QrBase64 = base64.StdEncoding.EncodeToString(buf.Bytes())
			return &result, nil
		}
	}

	webhookDomain := os.Getenv("CYBERGAME_WEBHOOK_ENDPOINT")
	if webhookDomain == "" {
		return nil, errors.New("EMPTY_ENV_CYBERGAME_WEBHOOK_ENDPOINT")
	}

	user, err := s.repo.GetUserBankDetailById(req.UserId)
	if err != nil {
		return nil, badRequest("INVALID_USER")
	}
	if user == nil || user.BankAccount == "" || user.BankCode == "" {
		return nil, badRequest("INVALID_USER_ACCOUNT")
	}
	remoteBankCode, err := GetBizpayCustomerBank(user.BankCode)
	if err != nil || remoteBankCode == "" {
		return nil, badRequest("USER_BANK_NOT_SUPPORTED")
	}

	accountingRepo := repository.NewAccountingRepository(s.repo.GetDb())
	configWeb, err := s.repo.GetWebConfiguration()
	if err != nil {
		log.Println("CreateBizpayDeposit.GetWebConfiguration", err)
		return nil, badRequest("WEB_CONFIG_NOT_FOUND")
	}
	if user.Id != 0 && configWeb != nil {
		// [********] ฝากครั้งแรกขั้นต่ำ ปรับ flow เช็ค user ให้เช็คจากรายการฝากครั้งแรกจริงๆ ไม่เช็คจากรหัสที่ได้รับ
		if err := CheckFirstMinimunDeposit(accountingRepo, user.Id, req.Amount, *configWeb); err != nil {
			return nil, badRequest(fmt.Sprintf("ฝากครั้งแรกขั้นต่ำ %d บาท", configWeb.MinFirstMemberDeposit))
		}
	} else {
		log.Println("CreateBizpayDeposit.CheckFirstMinimunDeposit", "CANT_CHECK_FIRST_MINIMUM_DEPOSIT_ON_EMPTY_CONFIG")
	}

	// ===========================================================================================
	var createBody model.BizpayOrderCreateBody
	createBody.UserId = req.UserId
	createBody.OrderTypeId = model.BIZPAY_ORDER_TYPE_DEPOSIT
	createBody.OrderNo = "" // Autogen
	createBody.Amount = req.Amount
	createBody.TransactionStatus = "PENDING"
	insertId, err := s.repo.CreateDbBizpayOrder(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	// reget Order
	pendingOrder, err := s.repo.GetDbBizpayOrderById(*insertId)
	if err != nil {
		// SET AS ERROR
		remark := "Error GetDbBizpayOrderById, " + err.Error()
		if err := s.repo.UpdateDbBizpayOrderError(*insertId, remark); err != nil {
			log.Println("CreateBizpayDeposit.UpdateDbBizpayOrderError", err)
		}
		return nil, internalServerError(err)
	}
	// Create BIZPAY Order
	var remoteRequest model.BizpayDepositCreateRemoteRequest
	remoteRequest.TransactionId = pendingOrder.OrderNo
	remoteRequest.BankAccountNumber = user.BankAccount
	remoteRequest.BankName = remoteBankCode
	remoteRequest.Name = user.Fullname
	remoteRequest.Amount = req.Amount
	remoteRequest.CallbackUrl = fmt.Sprintf("%s/bizpay/dep-callback", webhookDomain)
	remoteRequest.Type = "QR"
	remoteResp, err := s.repo.BizpayDeposit(*pgAccount, remoteRequest)
	if err != nil {
		// SET AS ERROR
		remark := "Error BizpayDeposit, " + err.Error()
		if err := s.repo.UpdateDbBizpayOrderError(*insertId, remark); err != nil {
			log.Println("BizpayDeposit.UpdateDbBizpayOrderError", err)
		}
		// SysLog At REPO
		return nil, internalServerError(err)
	}

	fmt.Println("BizpayDeposit.remoteResp", helper.StructJson(remoteResp))

	// Parse Float Amount
	// transferAmount, err := strconv.ParseFloat(remoteResp.Data.Amount, 64)
	// if err != nil {
	// 	// SET AS ERROR
	// 	remark := "Error ParseFloat Amount, " + err.Error()
	// 	if err := s.repo.UpdateDbBizpayOrderError(*insertId, remark); err != nil {
	// 		log.Println("CreateBizpayDeposit.UpdateDbBizpayOrderError", err)
	// 	}
	// 	return nil, internalServerError(err)
	// }
	transferAmount := remoteResp.Data.DepositAmount

	// onCreate Success
	var updateBody model.BizpayOrderUpdateBody
	updateBody.TransactionNo = remoteResp.Data.ReferenceId
	updateBody.TransactionDate = time.Now().UTC()
	updateBody.TransactionStatus = "WAIT_PAYMENT"
	updateBody.TransferAmount = transferAmount
	updateBody.QrPromptpay = remoteResp.Data.Qrcode
	// updateBody.PaymentPageUrl = remoteResp.Data.Redirect
	if err := s.repo.UpdateDbBizpayOrder(*insertId, updateBody); err != nil {
		// SET AS ERROR
		remark := "Error UpdateDbBizpayOrder, " + err.Error()
		if err := s.repo.UpdateDbBizpayOrderError(*insertId, remark); err != nil {
			log.Println("CreateBizpayDeposit.UpdateDbBizpayOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	waitPayOrder, err := s.repo.GetDbBizpayOrderById(*insertId)
	if err != nil {
		return nil, internalServerError(err)
	}

	result.UserId = waitPayOrder.UserId
	result.OrderNo = waitPayOrder.OrderNo
	result.Amount = waitPayOrder.Amount
	result.TransferAmount = transferAmount
	result.TransactionStatus = *waitPayOrder.TransactionStatus
	result.QrCode = waitPayOrder.QrPromptpay
	// result.PaymentPageUrl = waitPayOrder.PaymentPageUrl
	result.CreatedAt = waitPayOrder.CreatedAt

	imgData, err := qrcode.Encode(waitPayOrder.QrPromptpay, qrcode.Medium, 256)
	if err != nil {
		// return nil, fmt.Errorf("unable to encode png: %w", err)
		return &result, nil
	}
	// encode to base64
	img, err := png.Decode(bytes.NewReader(imgData))
	if err != nil {
		// return nil, fmt.Errorf("unable to decode jpeg: %w", err)
		return &result, nil
	}
	var buf bytes.Buffer
	if err := png.Encode(&buf, img); err != nil {
		// return nil, fmt.Errorf("unable to encode png: %w", err)
		return &result, nil
	}
	result.QrBase64 = base64.StdEncoding.EncodeToString(buf.Bytes())

	return &result, nil
}

func (s paygateBizpayService) CreateBizpayWithdraw(req model.BizpayWithdrawCreateRequest) (*int64, error) {

	// Ruled by Provider
	// - loan trade amount must greater than 100
	if req.Amount < model.BIZPAY_DEFMIN_WITHDRAW_AMOUNT || req.Amount > model.BIZPAY_DEFMAX_WITHDRAW_AMOUNT {
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}
	// (ปิดถอนช่วง 23:00-00:30 น. รวม 1.30 ชม.) **ยังไม่คอนเฟิมว่าปิดถอนช่วงเวลาไหน
	// actionAtUtc := time.Now().UTC()
	// bkkLoc := time.FixedZone("Asia/Bangkok", 7*60*60)
	// actionAtBkk := actionAtUtc.In(bkkLoc)
	// if actionAtBkk.Hour() >= 23 || (actionAtBkk.Hour() == 0 && actionAtBkk.Minute() <= 30) {
	// 	log.Println("withdrawWithBizpay", "WITHDRAW_NOT_AVAILABLE (ปิดถอนช่วง 23:00-00:30 น. รวม 1.30 ชม.)", actionAtBkk)
	// 	return nil, errors.New("WITHDRAW_NOT_AVAILABLE")
	// }

	pgAccount, err := s.GetBizpayAccount()
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}
	if !pgAccount.IsWithdrawEnabled {
		return nil, badRequest("PAYGATE_WITHDRAW_NOT_ENABLED")
	}
	// Prerequisites
	if pgAccount.ApiEndPoint == "" || pgAccount.PartnerKey == "" || pgAccount.AccessKey == "" || pgAccount.SecretKey == "" || pgAccount.MerchantId == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}

	user, err := s.repo.GetUserBankDetailById(req.UserId)
	if err != nil {
		return nil, badRequest("INVALID_USER")
	}
	if user == nil || user.BankAccount == "" || user.BankCode == "" {
		return nil, badRequest("INVALID_USER_ACCOUNT")
	}

	webhookDomain := os.Getenv("CYBERGAME_WEBHOOK_ENDPOINT")
	if webhookDomain == "" {
		return nil, errors.New("EMPTY_ENV_CYBERGAME_WEBHOOK_ENDPOINT")
	}

	withdrawBankCode, err := GetBizpayCustomerBank(user.BankCode)
	if err != nil || withdrawBankCode == "" {
		return nil, badRequest("USER_BANK_NOT_SUPPORTED")
	}

	// token, err := s.GetBizpayAccessToken(pgAccount)
	// if err != nil {
	// 	// return nil, internalServerError(errors.New("INVALID_ACCESS_TOKEN"))
	// 	return nil, err
	// }

	// ===========================================================================================
	// CREATE Order
	var createBody model.BizpayOrderCreateBody
	createBody.RefId = &req.RefId
	createBody.UserId = req.UserId
	createBody.OrderTypeId = model.BIZPAY_ORDER_TYPE_WITHDRAW
	createBody.OrderNo = "" // Autogen
	createBody.Amount = req.Amount
	createBody.TransactionStatus = "PENDING"
	insertId, err := s.repo.CreateDbBizpayOrder(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	// reget Order
	pendingOrder, err := s.repo.GetDbBizpayOrderById(*insertId)
	if err != nil {
		// SET AS ERROR
		remark := "Error GetDbBizpayOrderById, " + err.Error()
		if err := s.repo.UpdateDbBizpayOrderError(*insertId, remark); err != nil {
			log.Println("CreateBizpayWithdraw.UpdateDbBizpayOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// Create BIZPAY Order
	var remoteRequest model.BizpayWithdrawCreateRemoteRequest
	remoteRequest.TransactionId = pendingOrder.OrderNo
	remoteRequest.Amount = pendingOrder.Amount
	remoteRequest.BankAccountNumber = user.BankAccount
	remoteRequest.BankName = withdrawBankCode
	remoteRequest.Name = user.Fullname
	remoteRequest.Phone = user.Phone
	remoteRequest.CallbackUrl = fmt.Sprintf("%s/bizpay/wid-callback", webhookDomain)
	remoteResp, err := s.repo.BizpayWithdraw(*pgAccount, remoteRequest)
	if err != nil {
		// SET AS ERROR
		remark := "Error BizpayWithdraw, " + err.Error()
		if err := s.repo.UpdateDbBizpayOrderError(*insertId, remark); err != nil {
			log.Println("BizpayWithdraw.UpdateDbBizpayOrderError", err)
		}
		// SysLog At REPO
		return nil, internalServerError(err)
	}

	// fmt.Println("CreateBizpayWithdraw.remoteResp", helper.StructJson(remoteResp))

	if remoteResp.Data.ReferenceId == "" {
		// SET AS ERROR
		remark := remoteResp.Message
		if len(remark) == 0 {
			remark = "UNKNOW_ERROR from withdrawWithBizpay"
		}
		if err := s.repo.UpdateDbBizpayOrderError(*insertId, remark); err != nil {
			log.Println("BizpayWithdraw.UpdateDbBizpayOrderError", err)
		}
		// SysLog
		if _, err := s.repo.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "CreateBizpayWithdraw.BizpayWithdraw",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(remoteRequest),
			JsonResponse: helper.StructJson(remoteResp),
		}); err != nil {
			log.Println("CreateBizpayWithdraw.CreateSysLog", err)
		}
		return nil, internalServerError(fmt.Errorf("UNKNOW_ERROR"))
	}

	// onCreate Success
	var updateBody model.BizpayOrderUpdateBody
	updateBody.TransactionNo = remoteResp.Data.ReferenceId
	updateBody.TransactionDate = time.Now().UTC()
	updateBody.TransactionStatus = "WAIT_PAYMENT"
	updateBody.QrPromptpay = remoteResp.Message
	if err := s.repo.UpdateDbBizpayOrder(*insertId, updateBody); err != nil {
		// SET AS ERROR
		remark := "Error UpdateDbBizpayOrder, " + err.Error()
		if err := s.repo.UpdateDbBizpayOrderError(*insertId, remark); err != nil {
			log.Println("CreateBizpayWithdraw.UpdateDbBizpayOrderError", err)
		}
		return nil, internalServerError(err)
	}
	return insertId, nil
}

func CreateCustomerDepositFromBizpayOrder(repo repository.BizpayRepository, orderId int64, adminId int64) (*int64, error) {

	// ** ทุกรายการจะต้องเช็คสถานะใหม่ทั้งหมด เพราะ admin แย่งกดอนุมัติได้
	item, err := repo.GetRawBizpayPendingDepositOrderById(orderId)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, badRequest("ORDER_NOT_FOUND")
		}
		return nil, internalServerError(err)
	}

	// Check BankTransactionStatus = 'PENDING'
	if item.BankTransactionStatus != "PENDING" {
		return nil, badRequest("ORDER_NOT_PENDING")
	}

	return createCustomerDepositFromBizpay(repo, *item, adminId)
}

func createCustomerDepositFromBizpay(repo repository.BizpayRepository, item model.BizpayOrderResponse, adminId int64) (*int64, error) {

	var externalNoti model.NotifyExternalNotificationRequest
	actionAt := time.Now().UTC()
	// confirmByUserId := int64(0)

	depositAmount := item.Amount

	user, err := repo.GetUserBankDetailById(item.UserId)
	if err != nil {
		log.Println("createCustomerDepositFromBizpay.GetUserBankDetailByMemberCode", err)
		return nil, internalServerError(err)
	}

	var promotionWebUserId int64
	if getPromotionWebUser, err := repo.GetDepositCurrentProcessingUserPromotion(item.UserId); err != nil {
		log.Println("createCustomerDepositFromBizpay.GetDepositCurrentProcessingUserPromotion", err)
	} else if getPromotionWebUser != nil {
		promotionWebUserId = getPromotionWebUser.Id
	}

	// MemberCode
	if user.MemberCode == "" {
		memberCode, err := GenUniqueUserMemberCode(repository.NewUserRepository(repo.GetDb()), user.Id)
		if err != nil {
			log.Println("createCustomerDepositFromBizpay.GenUniqueUserMemberCode", err)
			return nil, internalServerError(err)
		}
		user.MemberCode = *memberCode
	}

	// layer — 2024-04-02 at 10:06 AM
	// ถ้าแก้ตามนี้แล้วน่าจะไม่เจอ ถ้าเคสกันซ้ำเราครอบคลุม
	// @Dunk @TULA
	// กันซ้ำคือ heng ไปซ้ำกับแอดมินเติมมือ
	// ----
	// เงื่อนไขกันซ้ำ
	// 1 หารายการฝากสำเร็จล่าสุดของยูสนั้น
	// 2 ถ้ามีรายการล่าสุดที่เป็นเติมมือ
	// -เช็คเวลาเติม ต้องมากกว่าเวลาโอนในสลิป
	// -ยอดเงินเท่ากัน
	// **ถ้าเข้าเงื่อนไขทั้งหมด คือแสดงว่า webhook เข้าทีหลังแอดมินเติมมือ
	// ต้อง bypass ไม่ให้เติมอีก และอัพเดท status ว่าจับคู่ได่แล้ว
	// ---
	// วิธีนี้น่าจะใช้ได้กับทุก webhook (heng, fastbank) ที่มีเวลาโอนสลิปมาให้
	duplicateRemark := "INIT"
	hasDuplicateDeposit := false
	manualDeposit, err := repo.GetLastestBankAdminManualDepositTransaction(user.Id)
	if err != nil {
		if err.Error() != recordNotFound {
			log.Println("createCustomerDepositFromBizpay.GetLastestBankAdminManualDepositTransaction", err)
			return nil, internalServerError(err)
		}
		// NOT_FOUND = ถ้าไม่เจอ ให้ทำต่อ
		duplicateRemark = "OK_NO_MANUAL_DEPOSIT"
	}
	if manualDeposit != nil && item.TransactionDate != nil {
		if manualDeposit.TransferAt.After(*item.TransactionDate) {
			// ถ้าเวลาเติมมือมากกว่าเวลาโอนในสลิป
			if manualDeposit.CreditAmount == depositAmount {
				// ถ้ายอดเงินเท่ากัน
				duplicateRemark = "STOP_MANUAL_DEPOSIT_AFTER_AND_SAME_AMOUNT"
				hasDuplicateDeposit = true
			} else {
				duplicateRemark = "OK_MANUAL_DEPOSIT_AMOUNT_NOT_MATCH"
			}
		} else {
			duplicateRemark = "OK_MANUAL_DEPOSIT_BEFORE_WITHDRAW"
		}
	}
	if duplicateRemark != "INIT" {
		// WebhookLog
		var createBody2 model.BizpayWebhookCreateBody
		createBody2.Name = "BIZPAY_DEPOSIT_DUPLICATE_CHECKER"
		createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
			"item":                item,
			"method":              "createCustomerDeposit",
			"hasDuplicateDeposit": hasDuplicateDeposit,
			"error":               duplicateRemark,
			"manualDeposit":       manualDeposit,
		})
		if _, err = repo.CreateBizpayWebhook(createBody2); err != nil {
			log.Println("Error CreateBizpayWebhook.CreateBizpayWebhook", err)
		}
		if hasDuplicateDeposit {
			return nil, internalServerError(fmt.Errorf("DUPLICATE_DEPOSIT"))
		}
	}

	pgAccount, err := GetBizpayAccount(repo)
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}

	// ===================================================
	paygateRepo := repository.NewPaymentGatewayRepository(repo.GetDb())
	accountingRepo := repository.NewAccountingRepository(repo.GetDb())
	luckyWheelRepo := repository.NewActivityLuckyWheelRepository(repo.GetDb())
	promotionWebRepo := repository.NewPromotionWebRepository(repo.GetDb())
	notiRepo := repository.NewNotificationRepository(repo.GetDb())

	// Affiliate + Alliance Income
	member, err := repo.GetMemberById(user.Id)
	if err != nil {
		return nil, internalServerError(err)
	}
	if member.UserTypeName == "NONE" {
		// ถ้าฝากครั้งแรก จะได้เป็น AFFILIATE + REFER COMMISION
		if err := UserFirstDepositCommission(accountingRepo, *member, depositAmount); err != nil {
			log.Println("CreateFristTimeDepositRecord.UserFirstDepositCommission.ERROR", err)
			return nil, internalServerError(err)
		}
	}

	isAutoDeposit := true // Auto ฝาก นับตั้งแต่ได้รับยอดฝาก จนได้ยืนยันเครดิต
	if adminId != 0 {
		isAutoDeposit = false
	}

	// CREATE BANK TRANSACTION
	var createBankTransaction model.BankTransactionCreateBody
	createBankTransaction.MemberCode = user.MemberCode
	createBankTransaction.UserId = user.Id
	// createBankTransaction.StatementId = 0
	createBankTransaction.TransactionTypeId = model.TRANSACTION_TYPE_DEPOSIT
	// createBankTransaction.FromAccountName = "" // empty?
	createBankTransaction.ToAccountName = &pgAccount.TypeName
	createBankTransaction.ToAccountNumber = &pgAccount.ShopName
	createBankTransaction.PromotionId = &promotionWebUserId
	createBankTransaction.CreditAmount = depositAmount
	// createBankTransaction.BonusAmount = 0
	// createBankTransaction.BonusReason = nil
	createBankTransaction.DepositChannel = "BIZPAY PAYMENT GATEWAY"
	createBankTransaction.TransactionStatusId = model.TRANS_STATUS_PENDING
	createBankTransaction.CreatedByAdminId = 0
	createBankTransaction.IsAutoCredit = isAutoDeposit
	createBankTransaction.TransferAt = &actionAt
	// create transaction
	transId, err := repo.InsertBankTransaction(createBankTransaction)
	if err != nil {
		log.Println("createCustomerDepositFromBizpay.InsertBankTransaction", err)
		return nil, internalServerError(err)
	}

	// var actionStatement model.BankStatementMatchRequest
	// actionStatement.StatementId = *bankstatementId
	// actionStatement.UserId = user.Id
	// actionStatement.ConfirmedAt = time
	// actionStatement.ConfirmedByAdminId = &req.CreateByUserId
	// if err := s.serviceAccounting.SetStatementOwnerMatched(*bankstatementId, actionStatement, model.USE_ENDING_NOTI); err != nil {
	// 	log.Println("createCustomerDepositFromBizpay.SetStatementOwnerMatched", err)
	// 	return nil, internalServerError(err)
	// }

	// DECREASE FASTBANK CREDIT 2.FROM create auto DEPOSIT
	// if err := repoAccounting.DecreaseFastbankCredit(1); err != nil {
	// 	log.Println("createCustomerDepositFromBizpay.DECREASE_FASTBANK_CREDIT_ERROR", err)
	// }

	var confirmDeposit model.BankConfirmDepositRequest
	confirmDeposit.TransferAt = &actionAt
	confirmDeposit.ConfirmedAt = actionAt
	confirmDeposit.ConfirmedByAdminId = &adminId
	if err := confirmDepositTransaction(paygateRepo, *transId, confirmDeposit); err != nil {
		log.Println("createCustomerDepositFromBizpay.confirmDepositTransaction", err)
		return nil, internalServerError(err)
	}

	// UPDATE HENG ORDER - BANK SUCCESS
	if err := repo.UpdateBizpayOrderBankSucess(item.Id, *transId, adminId); err != nil {
		log.Println("createCustomerDepositFromBizpay.UpdatePaygateOrderBankSucess", err)
	}

	isFirstDeposit := repo.IsFirstDeposit(user.Id)
	if isFirstDeposit {
		var bonusReq model.UserFirstDepositCreateRequest
		bonusReq.UserId = user.Id
		bonusReq.TransactionId = transId
		bonusReq.TransferAt = actionAt
		bonusReq.Amount = depositAmount
		bonusReq.Remark = "createCustomerDepositFromBizpay"
		if err := SetFirstDepositBonus(accountingRepo, isFirstDeposit, bonusReq); err != nil {
			webhookLogMessage := fmt.Sprintf("createCustomerDepositFromBizpay.userFirstDepositBonus, ERROR: %s", err)
			log.Println("SetFirstDepositBonus", webhookLogMessage)
			// return nil, errors.New(webhookLogMessage)
		}
	}

	// [USER_CREDIT]
	var userCreditReq model.UserTransactionCreateRequest
	userCreditReq.UserId = user.Id
	userCreditReq.RefId = transId
	userCreditReq.TransferAt = &actionAt
	userCreditReq.PromotionId = &promotionWebUserId
	userCreditReq.TypeId = model.CREDIT_TYPE_DEPOSIT
	// userCreditReq.AccountId = &bankAccount.Id
	userCreditReq.Detail = "BIZPAY PAYMENT GATEWAY"
	userCreditReq.Amount = depositAmount
	userCreditReq.CreateBy = &adminId
	userCreditReq.ConfirmBy = &adminId
	userCreditReq.IsAdjustAuto = isAutoDeposit
	userCreditReq.PaymentMerchatId = &pgAccount.ProviderId
	if agentResp, err := repo.IncreaseUserCredit(userCreditReq); err != nil {
		log.Println("createCustomerDepositFromBizpay.IncreaseUserCredit", err)
		return nil, internalServerError(err)
	} else {
		// AGENT_SUCCESS
		if err := repo.UpdateDepositTransactionStatusFromAgent(*transId, *agentResp); err != nil {
			log.Println("createCustomerDepositFromBizpay.UpdateDeporsitTransactionStatusFromAgent", err)
		}
	}

	// [Lucky Wheel] ตอนฝากละได้โบนัส
	var luckyWheelBody model.ActivityLuckyWheelRoundUserRequest
	luckyWheelBody.UserId = user.Id
	luckyWheelBody.ConditionId = model.ACTIVITY_LUCKY_WHEEL_SETTING_CONDITION_DEPOSIT
	luckyWheelBody.ConditionAmount = depositAmount
	if err := CreateRoundActivityLuckyWheel(luckyWheelRepo, luckyWheelBody); err != nil {
		log.Println("createCustomerDepositFromBizpay.CreateRoundActivityLuckyWheel", err)
	}

	// [TIER]
	if err := repo.IncreaseUserTierDepositAmount(user.Id, depositAmount); err != nil {
		log.Println("MatchStatementOwner.IncreaseUserTierDepositAmount", err)
	}

	var checkUserPromotionBody model.CheckUserPromotionBody
	checkUserPromotionBody.UserId = user.Id
	_, err = CheckUserPromotion(promotionWebRepo, checkUserPromotionBody)
	if err != nil {
		log.Println("createCustomerDepositFromBizpay.CheckUserPromotion", err)
	}
	// [turnOver] ถ้ามีการฝากเงิน ให้สร้าง TurnOver
	turnoverShare := repository.NewTurnoverRepository(repo.GetDb())
	if err := CreateTurnOverFromSuccessDeposit(turnoverShare, user.Id, depositAmount, *transId); err != nil {
		log.Println("createCustomerDeposit.CreateTurnOverFromSuccessDeposit", err)
	}

	// ===================================================

	// [ notify]
	notiAtUtc := time.Now().UTC()
	externalNoti.TypeNotify = model.IsDepositAfterCredit
	externalNoti.TransId = transId
	externalNoti.Amount = depositAmount
	externalNoti.MemberCode = user.MemberCode
	externalNoti.UserCredit = user.Credit + depositAmount
	externalNoti.ConfirmedByAdminId = 0
	externalNoti.TransferDateTime = notiAtUtc.Add(7 * time.Hour).Format("2006-01-02 15:04:05")
	externalNoti.ActionTime = notiAtUtc.Add(7 * time.Hour).Format("2006-01-02 15:04:05")
	endTime := notiAtUtc
	elapsed := endTime.Sub(actionAt)
	elapsedSeconds := elapsed.Seconds()
	timeElapsed := fmt.Sprintf("%.2f", elapsedSeconds)
	externalNoti.TimerCounter = timeElapsed

	externalNoti.WebScoket.UserID = user.Id
	externalNoti.WebScoket.Amount = depositAmount
	externalNoti.WebScoket.MemberCode = user.MemberCode
	externalNoti.WebScoket.AlertType = "DEPOSIT"
	if err := repo.UpdateAutoProcessTimer(timeElapsed, *transId); err != nil {
		return nil, nil
	}
	if err := ExternalNotification(notiRepo, externalNoti); err != nil {
		log.Println("FailedNotify", err)
	}
	return nil, nil
}

func approveCustomerWithdrawFromBizpay(repo repository.BizpayRepository, transId int64) (*int64, error) {

	withdrawTrans, err := repo.GetBankTransactionById(transId)
	if err != nil {
		log.Println("approveCustomerWithdrawFromBizpay.GetUserBankDetailById", err)
		return nil, internalServerError(err)
	}

	// ============================= ON_SUCCESS =================================
	confirmAtUtc := time.Now().UTC()
	if withdrawTrans.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		// [create transaction action success]
		var createConfirm model.CreateSuccessTransferWithdrawRequest
		createConfirm.TransactionId = withdrawTrans.Id
		createConfirm.ConfirmedAt = confirmAtUtc
		var setIdAuto int64 = 0
		createConfirm.ConfirmedByAdminId = &setIdAuto
		if _, err := CreateSuccessTransferWithdraw(repository.NewBankingRepository(repo.GetDb()), createConfirm); err != nil {
			log.Println("approveCustomerWithdrawFromBizpay.CreateSuccessTransferWithdraw", err)
			return nil, nil
		}
		// [Show Withdraw USER_TRANSACTION]
		var showUserTrans model.UserTransactionShowUpdate
		showUserTrans.TransactionId = withdrawTrans.Id
		showUserTrans.ConfirmedAt = &createConfirm.ConfirmedAt
		showUserTrans.ConfirmAdminId = createConfirm.ConfirmedByAdminId
		if err := repo.ShowUserTransaction(showUserTrans); err != nil {
			log.Println("approveCustomerWithdrawFromBizpay.ShowUserTransaction", err)
			return nil, nil
		}
		// [update transaction status]
		secondUsed := "0.0" // timing
		if withdrawTrans.IsAutoCredit && withdrawTrans.TransferAt != nil {
			seconds := confirmAtUtc.Sub(withdrawTrans.TransferAt.UTC()).Seconds()
			secondUsed = fmt.Sprintf("%.2f", seconds)
		}
		if err := repo.UpdateTransactionStatusTransferingToSuccess(withdrawTrans.Id, secondUsed); err != nil {
			log.Println("approveCustomerWithdrawFromBizpay.UpdateTransactionStatusTransferingToSuccess", err)
			return nil, internalServerError(err)
		}

		// [notify SUCCESS]
		var externalNoti model.NotifyExternalNotificationRequest
		notiRepo := repository.NewNotificationRepository(repo.GetDb())
		externalNoti.TypeNotify = model.IsWithdrawalCreditSuccess
		externalNoti.TransId = &transId
		externalNoti.Amount = withdrawTrans.CreditAmount
		externalNoti.MemberCode = withdrawTrans.MemberCode
		externalNoti.UserCredit = withdrawTrans.CreditAmount
		externalNoti.ConfirmedByAdminId = 0
		externalNoti.TimerCounter = secondUsed
		externalNoti.TransferDateTime = withdrawTrans.TransferAt.Add(7 * time.Hour).Format("2006-01-02 15:04:05")
		externalNoti.ActionTime = confirmAtUtc.Add(7 * time.Hour).Format("2006-01-02 15:04:05")
		if err := ExternalNotification(notiRepo, externalNoti); err != nil {
			log.Println("FailedNotify", err)
		}

	}
	return nil, nil
}

// func rollbackCustomerWithdrawFromBizpay(repo repository.BizpayRepository, transId int64) (*int64, error) {

// 	withdrawTrans, err := repo.GetBankTransactionById(transId)
// 	if err != nil {
// 		log.Println("rollbackCustomerWithdrawFromBizpay.GetBankTransactionById", err)
// 		return nil, internalServerError(err)
// 	}

// 	// ============================= ON_SUCCESS =================================
// 	if withdrawTrans.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_TRASNFERING {
// 		// [update transaction status]
// 		if err := repo.RollbackTransactionStatusTransferingToConfirmed(withdrawTrans.Id); err != nil {
// 			log.Println("approveCustomerWithdrawFromBizpay.RollbackTransactionStatusTransferingToConfirmed", err)
// 			return nil, internalServerError(err)
// 		}
// 	}
// 	return nil, nil
// }

func (s paygateBizpayService) CreateSystemLog(name string, req interface{}) error {

	// [SYSLOG] HACKED
	_, err := s.repo.CreateSystemLog(model.SystemLogCreateBody{
		Name:   name,
		Status: "HACKING",
		JsonRequest: helper.StructJson(map[string]interface{}{
			"req": req,
		}),
	})
	if err != nil {
		log.Println(name, "CreateSystemLog.ERROR", err.Error())
	}
	return nil
}

func (s paygateBizpayService) CancelWithdrawFromBizpay(transId int64, adminId int64) error {

	withdrawTrans, err := s.repo.GetBankTransactionById(transId)
	if err != nil {
		log.Println("rollbackWithdrawFromBizpay.GetBankTransactionById", err)
		return internalServerError(err)
	}
	if withdrawTrans.TransactionStatusId != model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		return internalServerError(errors.New("ไม่อยู๋ในสถานะที่สามารถยกเลิกได้"))
	}

	user, err := s.repo.GetUserBankDetailById(withdrawTrans.UserId)
	if err != nil {
		return internalServerError(err)
	}

	paygateOrder, err := s.repo.GetDbBizpayOrderByRefId(transId)
	if err != nil {
		log.Println("CancelWithdrawFromBizpay.GetDbBizpayOrderByRefId", err)
		return internalServerError(err)
	}
	waitStatus := "ERROR"
	if paygateOrder.TransactionStatus == nil || *paygateOrder.TransactionStatus != waitStatus {
		return internalServerError(errors.New("ไม่อยู๋ในสถานะที่สามารถยกเลิกได้"))
	}

	returnAmount := withdrawTrans.CreditAmount

	// race condition deposit
	actionAt := time.Now().UTC()
	var createBody model.RaceActionCreateBody
	createBody.Name = "ADMIN_CANCEL_WITHDRAW"
	createBody.JsonRequest = helper.StructJson(map[string]interface{}{
		"transId":      transId,
		"returnAmount": returnAmount,
	})
	createBody.Status = "PENDING"
	createBody.ActionKey = fmt.Sprintf("WITHDRAW_CANCEL_T%sU%dID%d", actionAt.Format("*********4"), user.Id, transId)
	createBody.UnlockAt = actionAt.Add(time.Second * 1)
	if _, err := s.repo.GetRaceActionIdByActionKey(createBody.ActionKey); err != nil {
		log.Println("CancelWithdrawFromBizpay.GetRaceActionIdByActionKey", err)
		if err != gorm.ErrRecordNotFound {
			return internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
		}
	} else {
		return internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
	}

	// create race condition
	actionId, err := s.repo.CreateRaceCondition(createBody)
	if err != nil {
		log.Println("CancelWithdrawFromBizpay.ERROR.CreateRaceCondition", err)
		return internalServerError(errors.New("CANNOT_CREATE_ACTION_LUCKY_WHEEL_ROUND"))
	}
	if actionId == 0 {
		return internalServerError(errors.New("CANNOT_CREATE_ACTION_LUCKY_WHEEL_ROUND"))
	}

	// SET ORDER AS ERROR
	remark := "Admin Cancel Withdraw"
	if err := s.repo.UpdateDbBizpayOrderError(paygateOrder.Id, remark); err != nil {
		log.Println("CreateBizpayWithdraw.UpdateDbBizpayOrderError", err)
	}

	if withdrawTrans.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		// [********] check user credit transaction ว่าตรงไหมกับเงื่อนไข (isShow = false)
		userTransaction, err := s.repo.GetUserWithdrawCreditTransactionByRefId(withdrawTrans.Id, returnAmount)
		if err != nil {
			if err.Error() == recordNotFound {
				log.Println("CancelWithdrawCredit.GetUserCreditTransactionById", err)
				return badRequest("ไม่ตรงกับเงือนไข")
			}
			return internalServerError(err)
		}
		if userTransaction != nil {
			var userCreditReq model.UserTransactionCreateRequest
			userCreditReq.UserId = user.Id
			userCreditReq.TransferAt = withdrawTrans.TransferAt
			userCreditReq.RefId = &withdrawTrans.Id
			userCreditReq.TypeId = model.CREDIT_TYPE_DEPOSIT
			userCreditReq.Amount = returnAmount
			IsShow := false
			userCreditReq.IsShow = &IsShow
			userCreditReq.ConfirmBy = &adminId
			if agentResp, err := s.repo.IncreaseUserCredit(userCreditReq); err != nil {
				log.Println("CancelWithdrawCredit.IncreaseUserCredit", err)
				return internalServerError(err)
			} else if !agentResp.AgentSuccess {
				// [********] error from agent will be in failed
				// if err := s.repoAccounting.UpdateDeporsitTransactionStatusFromAgent(*transId, *agentResp); err != nil {
				//     log.Println("CancelWithdrawCredit.UpdateDeporsitTransactionStatusFromAgent", err)
				// }
				// Set FastBank-READ ** ไม่มี เพราะเป็นการสร้างรายการฝากเอง
				agentFail := errors.New("ทำรายการเครดิตไม่สำเร็จ")
				log.Println("CancelWithdrawCredit.IncreaseUserCredit", agentFail)
				return internalServerError(agentFail)
			}
		}
	}

	// remark := "ADMIN_CANCEL_WITHDRAW"
	var updateApprovedBy model.UpdateUserTransactionStatusRequest
	updateApprovedBy.Id = withdrawTrans.Id
	updateApprovedBy.CanceledAt = actionAt
	updateApprovedBy.CanceledByAdminId = adminId
	updateApprovedBy.TransactionStatusId = model.TRANS_STATUS_WITHDRAW_CANCELED
	updateApprovedBy.CancelRemark = &remark
	err = s.repo.UpdateUserTransactionStatus(updateApprovedBy)
	if err != nil {
		return internalServerError(err)
	}

	var createConfirm model.CreateSuccessTransferWithdrawRequest
	createConfirm.TransactionId = withdrawTrans.Id
	createConfirm.ConfirmedAt = actionAt
	createConfirm.ConfirmedByAdminId = &adminId
	if _, err := CreateCanceledTransferWithdraw(repository.NewBankingRepository(s.sharedDb), createConfirm); err != nil {
		return err
	}

	return nil
}

func (s paygateBizpayService) CreateBizpayDepositWebhook(req model.BizpayWebhookRequest) (*int64, error) {

	var createBody model.BizpayWebhookCreateBody
	createBody.Name = "BIZPAY_DEPOSIT_CALLBACK"
	createBody.JsonPayload = req.JsonPayload
	insertId, err := s.repo.CreateBizpayWebhook(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// DEPOSIT
	// {
	// 	"referenceId": "{{referenceId}}", // The unique reference ID for the transaction
	// 	"transactionId": "{{transactionId}}", // The unique transaction ID
	// 	"clientId": "{{CLIENT_ID}}", // The client's unique identifier
	// 	"merchantId": "{{MERCHANT_ID}}", // The merchant's unique identifier
	// 	"walletId": "{{WALLET_ID}}", // The wallet ID associated with the transaction
	// 	"bankCode": "074", // The bank code (e.g., TTB)
	// 	"bankName": "TTB", // The bank name (e.g., TTB)
	// 	"bankAccountNumber": "62xxxxxx40", // The bank account number involved in the transaction
	// 	"bankAccountName": "", // The bank account holder's name (if available)
	// 	"amount": 400.64, // The transaction amount
	// 	"status": "completed", // The status of the transaction (e.g., "completed","expired")
	// 	"timestamp": **********, // Unix timestamp of when the transaction was initiated
	// 	"matchTimestamp": *************, // Unix timestamp of when the transaction was matched
	// 	"type": "QR", // The type of transaction (e.g., QR)
	// 	"hash": "U2FsdGVkX1/Z6EHf3XX6hmzlnK7TvYoLKsUlyR0F47LIERkRq2fKKhKwpwq3j9wu" // AES-encrypted hash of the transactionId
	// }
	var remoteResp model.BizpayDepositWebhookResponse
	errJson := json.Unmarshal([]byte(req.JsonPayload), &remoteResp)
	if errJson != nil {
		log.Println("Error unmarshal json", errJson)
		return nil, internalServerError(errJson)
	}

	if remoteResp.ReferenceId == "" || remoteResp.TransactionId == "" {
		log.Println("CreateBizpayDepositWebhook.NoRef", helper.StructJson(remoteResp))
		return nil, badRequest("BIZPAY_DEPOSIT_HOOK_NOREF")
	}

	// Check Response Status.
	successStatus := strings.ToUpper(strings.TrimSpace(remoteResp.Status))
	respStatusKey := "U"
	if successStatus == "COMPLETED" {
		successStatus = "PAID" // Always use PAID
		respStatusKey = "P"    // completed ได้ครั้งเดียว
	} else if successStatus == "EXPIRED" {
		respStatusKey = "D"
	} else {
		log.Println("CreateBizpayDepositWebhook.remoteResp.desc.ELSE", successStatus)
		// เก็บตามจริง successStatus = "ERROR"
		respStatusKey = "E"
	}

	// Service Race Condition by Ref1(MchOrderNo) + perStatus
	acAtUtc := time.Now().UTC()
	var rcCreateBody model.RaceActionCreateBody
	rcCreateBody.Name = "CreateBizpayDepositWebhook"
	rcCreateBody.JsonRequest = helper.StructJson(req)
	rcCreateBody.Status = "PENDING"
	// ActionKey = Ref2-MINUTE
	rcCreateBody.ActionKey = fmt.Sprintf("PG11D%s-%s%s", acAtUtc.Format("*********4"), remoteResp.TransactionId, respStatusKey)
	rcCreateBody.UnlockAt = acAtUtc.Add(time.Second * 1)
	if _, err := s.repo.CreateRaceCondition(rcCreateBody); err != nil {
		return insertId, internalServerError(err)
	}

	// Get Posible Order
	var query model.BizpayOrderListRequest
	query.OrderNo = remoteResp.TransactionId
	query.TransactionNo = remoteResp.ReferenceId
	// query.Amount = fmt.Sprintf("%f", remoteResp.Amount)
	query.Status = "WAIT_PAYMENT"
	query.Limit = 1
	list, _, err := s.repo.GetDbBizpayOrderList(query)
	if err != nil {
		return nil, internalServerError(err)
	}

	// fmt.Println("BizpayDecryptRepayDespositPayload.list", helper.StructJson(list))

	// ** ไม่ได้เช็ค amount เพราะจะมีระบบที่ใช้ ทศนิยม และไม่เท่ากัน แต่ก็ควรเช็คแบบ +- 10%

	if len(list) > 0 {
		for _, item := range list {

			// Payment Success, Create Deposit if UserId is not null
			if item.UserId > 0 && item.OrderTypeId == model.BIZPAY_ORDER_TYPE_DEPOSIT {
				if successStatus == "PAID" {
					// Update Order
					if err := s.repo.ApproveDbBizpayOrder(item.Id, successStatus); err != nil {
						return nil, internalServerError(err)
					}
					if _, err := CreateCustomerDepositFromBizpayOrder(s.repo, item.Id, 0); err != nil {
						// WebhookLog
						var createBody2 model.BizpayWebhookCreateBody
						createBody2.Name = "BIZPAY_DEPOSIT_ERROR"
						createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
							"item":   item,
							"method": "createCustomerDepositFromBizpay",
							"error":  err.Error(),
						})
						if _, err = s.repo.CreateBizpayWebhook(createBody2); err != nil {
							log.Println("Error CreateBizpayWebhook.createCustomerDepositFromBizpay", err)
						}
					}
				}
			} else if item.UserId > 0 && item.OrderTypeId == model.BIZPAY_ORDER_TYPE_WITHDRAW {
				if successStatus == "PAID" {
					// Update Order
					if err := s.repo.ApproveDbBizpayOrder(item.Id, successStatus); err != nil {
						return nil, internalServerError(err)
					}
					if _, err := approveCustomerWithdrawFromBizpay(s.repo, *item.RefId); err != nil {
						// WebhookLog
						var createBody2 model.BizpayWebhookCreateBody
						createBody2.Name = "BIZPAY_WITHDRAW_ERROR"
						createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
							"item":   item,
							"method": "approveCustomerWithdrawFromBizpay",
							"error":  err.Error(),
						})
						if _, err = s.repo.CreateBizpayWebhook(createBody2); err != nil {
							log.Println("Error approveCustomerWithdrawFromBizpay.CreateBizpayWebhook", err)
						}
					}
				} else if successStatus == "FAILED" || successStatus == "REJECTED" || successStatus == "EXPIRED" {
					// Update Order
					if err := s.repo.ApproveDbBizpayOrder(item.Id, successStatus); err != nil {
						return nil, internalServerError(err)
					}
					if err := s.cancelWithdrawFromBizpayWebhookError(item); err != nil {
						log.Println("Error UpdateDbBizpayOrderError", err)
					}
				}
			} else {
				log.Println("ApproveDbBizpayOrder.WithoutItemUpdated", helper.StructJson(item))
			}
		}
	}
	return insertId, nil
}

func (s paygateBizpayService) CreateBizpayWithdrawWebhook(req model.BizpayWebhookRequest) (*int64, error) {

	var createBody model.BizpayWebhookCreateBody
	createBody.Name = "BIZPAY_WITHDRAW_CALLBACK"
	createBody.JsonPayload = req.JsonPayload
	insertId, err := s.repo.CreateBizpayWebhook(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// WITHDRAW
	//	{
	//		"referenceId":"{{referrencId}}",
	//	    "transactionId":"{{transactionId}}",
	//	    "status":"completed",  // pending, processing, failed, rejected, completed
	//	    "message":"Some Message",
	//	    "qrcode":"0046000600000101030140225202312086ZwVuYndMYCSnMTv65102TH9104B07F", // This qrcode will send if payout is completed, Qrcode created by Bank.
	//	    "hash": "xxxxxxx"  // AES hashing transactionId by key that create by api_key + secret_key
	//	}

	var remoteResp model.BizpayWithdrawWebhookResponse
	errJson := json.Unmarshal([]byte(req.JsonPayload), &remoteResp)
	if errJson != nil {
		log.Println("Error unmarshal json", errJson)
		return nil, internalServerError(errJson)
	}

	// fmt.Println("BizpayDecryptRepayDespositPayload.remoteResp", helper.StructJson(remoteResp))

	// Check Response Status.
	successStatus := strings.ToUpper(strings.TrimSpace(remoteResp.Status))
	respStatusKey := "U"
	if successStatus == "COMPLETED" {
		successStatus = "PAID" // Always use PAID
		respStatusKey = "P"    // completed ได้ครั้งเดียว
	} else if successStatus == "PENDING" {
		respStatusKey = "W"
	} else if successStatus == "PROCESSING" {
		respStatusKey = "B"
	} else if successStatus == "FAILED" {
		respStatusKey = "F"
	} else if successStatus == "REJECTED" {
		respStatusKey = "R"
	} else if successStatus == "EXPIRED" {
		respStatusKey = "D"
	} else {
		log.Println("CreateBizpayWithdrawWebhook.remoteResp.desc.ELSE", successStatus)
		// เก็บตามจริง successStatus = "ERROR"
		respStatusKey = "E"
	}

	// Service Race Condition by Ref1(MchOrderNo)
	acAtUtc := time.Now().UTC()
	var rcCreateBody model.RaceActionCreateBody
	rcCreateBody.Name = "CreateBizpayWithdrawWebhook"
	rcCreateBody.JsonRequest = helper.StructJson(req)
	rcCreateBody.Status = "PENDING"
	// ActionKey = Ref2-MINUTE
	rcCreateBody.ActionKey = fmt.Sprintf("PG11W%s-%s%s", acAtUtc.Format("*********4"), remoteResp.TransactionId, respStatusKey)
	rcCreateBody.UnlockAt = acAtUtc.Add(time.Second * 1)
	if _, err := s.repo.CreateRaceCondition(rcCreateBody); err != nil {
		return insertId, internalServerError(err)
	}

	// Get Posible Order
	var query model.BizpayOrderListRequest
	query.OrderNo = remoteResp.TransactionId
	query.TransactionNo = remoteResp.ReferenceId
	// query.Amount = fmt.Sprintf("%f", remoteResp.Amount)
	query.Status = "WAIT_PAYMENT"
	query.Limit = 1
	list, _, err := s.repo.GetDbBizpayOrderList(query)
	if err != nil {
		return nil, internalServerError(err)
	}

	// ** ไม่ได้เช็ค amount เพราะจะมีระบบที่ใช้ ทศนิยม และไม่เท่ากัน แต่ก็ควรเช็คแบบ +- 10%

	if len(list) > 0 {
		for _, item := range list {

			// Payment Success, Create Deposit if UserId is not null
			if item.UserId > 0 && item.OrderTypeId == model.BIZPAY_ORDER_TYPE_DEPOSIT {
				if successStatus == "PAID" {
					// Update Order
					if err := s.repo.ApproveDbBizpayOrder(item.Id, successStatus); err != nil {
						return nil, internalServerError(err)
					}
					if _, err := CreateCustomerDepositFromBizpayOrder(s.repo, item.Id, 0); err != nil {
						// WebhookLog
						var createBody2 model.BizpayWebhookCreateBody
						createBody2.Name = "BIZPAY_DEPOSIT_ERROR"
						createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
							"item":   item,
							"method": "createCustomerDepositFromBizpay",
							"error":  err.Error(),
						})
						if _, err = s.repo.CreateBizpayWebhook(createBody2); err != nil {
							log.Println("Error CreateBizpayWebhook.createCustomerDepositFromBizpay", err)
						}
					}
				}
			} else if item.UserId > 0 && item.OrderTypeId == model.BIZPAY_ORDER_TYPE_WITHDRAW {
				if successStatus == "PAID" {
					// Update Order
					if err := s.repo.ApproveDbBizpayOrder(item.Id, successStatus); err != nil {
						return nil, internalServerError(err)
					}
					if _, err := approveCustomerWithdrawFromBizpay(s.repo, *item.RefId); err != nil {
						// WebhookLog
						var createBody2 model.BizpayWebhookCreateBody
						createBody2.Name = "BIZPAY_WITHDRAW_ERROR"
						createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
							"item":   item,
							"method": "approveCustomerWithdrawFromBizpay",
							"error":  err.Error(),
						})
						if _, err = s.repo.CreateBizpayWebhook(createBody2); err != nil {
							log.Println("Error approveCustomerWithdrawFromBizpay.CreateBizpayWebhook", err)
						}
					}
				} else if successStatus == "FAILED" || successStatus == "REJECTED" || successStatus == "EXPIRED" {
					// Update Order
					if err := s.repo.ApproveDbBizpayOrder(item.Id, successStatus); err != nil {
						return nil, internalServerError(err)
					}
					if err := s.cancelWithdrawFromBizpayWebhookError(item); err != nil {
						log.Println("Error UpdateDbBizpayOrderError", err)
					}
				}
			} else {
				log.Println("ApproveDbBizpayOrder.WithoutItemUpdated", helper.StructJson(item))
			}
		}
	}
	return insertId, nil
}

func (s paygateBizpayService) cancelWithdrawFromBizpayWebhookError(pgOrder model.BizpayOrderResponse) error {

	adminId := int64(1)

	withdrawTrans, err := s.repo.GetBankTransactionById(*pgOrder.RefId)
	if err != nil {
		log.Println("rollbackWithdrawFromBizpay.GetBankTransactionById", err)
		return internalServerError(err)
	}
	if withdrawTrans.TransactionStatusId != model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		return internalServerError(errors.New("ไม่อยู๋ในสถานะที่สามารถยกเลิกได้"))
	}

	user, err := s.repo.GetUserBankDetailById(withdrawTrans.UserId)
	if err != nil {
		return internalServerError(err)
	}

	paygateOrder, err := s.repo.GetDbBizpayOrderByRefId(withdrawTrans.Id)
	if err != nil {
		log.Println("CancelWithdrawFromBizpay.GetDbBizpayOrderByRefId", err)
		return internalServerError(err)
	}
	waitStatus := "ERROR"
	if paygateOrder.TransactionStatus == nil || *paygateOrder.TransactionStatus != waitStatus {
		return internalServerError(errors.New("ไม่อยู๋ในสถานะที่สามารถยกเลิกได้"))
	}

	returnAmount := withdrawTrans.CreditAmount

	// race condition deposit
	actionAt := time.Now().UTC()
	var createBody model.RaceActionCreateBody
	createBody.Name = "ADMIN_CANCEL_WITHDRAW"
	createBody.JsonRequest = helper.StructJson(map[string]interface{}{
		"transId":      withdrawTrans.Id,
		"returnAmount": returnAmount,
	})
	createBody.Status = "PENDING"
	createBody.ActionKey = fmt.Sprintf("WITHDRAW_CANCEL_T%sU%dID%d", actionAt.Format("*********4"), user.Id, withdrawTrans.Id)
	createBody.UnlockAt = actionAt.Add(time.Second * 1)
	if _, err := s.repo.GetRaceActionIdByActionKey(createBody.ActionKey); err != nil {
		log.Println("CancelWithdrawFromBizpay.GetRaceActionIdByActionKey", err)
		if err != gorm.ErrRecordNotFound {
			return internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
		}
	} else {
		return internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
	}

	// create race condition
	actionId, err := s.repo.CreateRaceCondition(createBody)
	if err != nil {
		log.Println("CancelWithdrawFromBizpay.ERROR.CreateRaceCondition", err)
		return internalServerError(errors.New("CANNOT_CREATE_ACTION_LUCKY_WHEEL_ROUND"))
	}
	if actionId == 0 {
		return internalServerError(errors.New("CANNOT_CREATE_ACTION_LUCKY_WHEEL_ROUND"))
	}

	// SET ORDER AS ERROR
	remark := "Payment Cancel Withdraw"
	if err := s.repo.UpdateDbBizpayOrderError(paygateOrder.Id, remark); err != nil {
		log.Println("CreateBizpayWithdraw.UpdateDbBizpayOrderError", err)
	}

	if withdrawTrans.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		// [********] check user credit transaction ว่าตรงไหมกับเงื่อนไข (isShow = false)
		userTransaction, err := s.repo.GetUserWithdrawCreditTransactionByRefId(withdrawTrans.Id, returnAmount)
		if err != nil {
			if err.Error() == recordNotFound {
				log.Println("CancelWithdrawCredit.GetUserCreditTransactionById", err)
				return badRequest("ไม่ตรงกับเงือนไข")
			}
			return internalServerError(err)
		}
		if userTransaction != nil {
			var userCreditReq model.UserTransactionCreateRequest
			userCreditReq.UserId = user.Id
			userCreditReq.TransferAt = withdrawTrans.TransferAt
			userCreditReq.RefId = &withdrawTrans.Id
			userCreditReq.TypeId = model.CREDIT_TYPE_DEPOSIT
			userCreditReq.Amount = returnAmount
			IsShow := false
			userCreditReq.IsShow = &IsShow
			userCreditReq.ConfirmBy = &adminId
			if agentResp, err := s.repo.IncreaseUserCredit(userCreditReq); err != nil {
				log.Println("CancelWithdrawCredit.IncreaseUserCredit", err)
				return internalServerError(err)
			} else if !agentResp.AgentSuccess {
				// [********] error from agent will be in failed
				// if err := s.repoAccounting.UpdateDeporsitTransactionStatusFromAgent(*transId, *agentResp); err != nil {
				//     log.Println("CancelWithdrawCredit.UpdateDeporsitTransactionStatusFromAgent", err)
				// }
				// Set FastBank-READ ** ไม่มี เพราะเป็นการสร้างรายการฝากเอง
				agentFail := errors.New("ทำรายการเครดิตไม่สำเร็จ")
				log.Println("CancelWithdrawCredit.IncreaseUserCredit", agentFail)
				return internalServerError(agentFail)
			}
		}
	}

	// remark := "ADMIN_CANCEL_WITHDRAW"
	var updateApprovedBy model.UpdateUserTransactionStatusRequest
	updateApprovedBy.Id = withdrawTrans.Id
	updateApprovedBy.CanceledAt = actionAt
	updateApprovedBy.CanceledByAdminId = adminId
	updateApprovedBy.TransactionStatusId = model.TRANS_STATUS_WITHDRAW_CANCELED
	updateApprovedBy.CancelRemark = &remark
	err = s.repo.UpdateUserTransactionStatus(updateApprovedBy)
	if err != nil {
		return internalServerError(err)
	}

	var createConfirm model.CreateSuccessTransferWithdrawRequest
	createConfirm.TransactionId = withdrawTrans.Id
	createConfirm.ConfirmedAt = actionAt
	createConfirm.ConfirmedByAdminId = &adminId
	if _, err := CreateCanceledTransferWithdraw(repository.NewBankingRepository(s.sharedDb), createConfirm); err != nil {
		return err
	}

	return nil
}
