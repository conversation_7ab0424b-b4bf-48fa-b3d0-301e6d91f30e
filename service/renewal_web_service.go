package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"errors"
	"fmt"
	"log"
	"math"
	"net/http"
	"os"
	"time"
)

type RenewalWebService interface {
	// MyWeb
	GetLocalWebInfo() (*model.WebStatusResponse, error)
	// Web Renewal
	GetWebRenewalPackageList(req model.WebRenewalPackageListRequest) (*model.SuccessWithPagination, error)
	GetWebRenewalPackageById(id int64) (*model.WebRenewalPackageResponse, error)
	BuyWebRenewalPackage(req model.WebRenewalBuyRequest) (*int64, error)
	// Web Renewal
	GetSmsRenewalPackageList(req model.SmsRenewalPackageListRequest) (*model.SuccessWithPagination, error)
	GetSmsRenewalPackageById(id int64) (*model.SmsRenewalPackageResponse, error)
	BuySmsRenewalPackage(req model.SmsRenewalBuyRequest) (*int64, error)
	// Web Renewal
	GetFastbankRenewalPackageList(req model.FastbankRenewalPackageListRequest) (*model.SuccessWithPagination, error)
	GetFastbankRenewalPackageById(id int64) (*model.FastbankRenewalPackageResponse, error)
	BuyFastbankRenewalPackage(req model.FastbankRenewalBuyRequest) (*int64, error)
	// Invoice - from renewal web sms and fastbank credit
	GetInvoiceList(req model.InvoiceListRequest) (*model.SuccessWithPagination, error)
	GetInvoiceById(id int64) (*model.InvoiceResponse, error)
	ViewBillById(id int64) (*model.InvoiceBillResponse, error)
	ViewReceiptById(id int64) (*model.InvoiceReceiptResponse, error)
	ConfirmPayment(req model.InvoiceConfirmPaymentRequest) error
	// use master TestConfirmInvoice(req model.InvoiceConfirmRequest) error
	// Pay by HENG
	CreateInvoiceHengPayment(req model.InvoiceHengPaymentRequest) (*model.DownlineInvoiceHengPaymentCreateResponse, error)
	CreateInvoicePayonexPayment(req model.InvoicePayonexPaymentRequest) (*model.DownlineInvoicePayonexPaymentCreateResponse, error)
	// Cloudflare-Upload
	UploadInvoiceSlipToCloudFlare(imageFileBody *http.Request) (*model.FileUploadResponse, error)
	// S3-Upload
	UploadImageToS3InvoiceSlip(imageFileBody *http.Request) (*model.FileUploadResponse, error)
}

type renewalWebService struct {
	repo            repository.RenewalWebRepository
	downlineService DownlineService
}

func NewRenewalWebService(
	repo repository.RenewalWebRepository,
	downlineService DownlineService,
) RenewalWebService {
	return &renewalWebService{repo, downlineService}
}

func (s renewalWebService) GetLocalWebInfo() (*model.WebStatusResponse, error) {

	web, err := s.repo.GetLocalWebInfo()
	if err != nil {
		if err.Error() == recordNotFound {
			// CREATE NEW
			defaultName := os.Getenv("DOMAIN_NAME")
			var createBody model.WebMasterCreateBody
			createBody.Name = defaultName
			createBody.WebDomain = defaultName
			createBody.WebExpiredDate = time.Now().Format("2006-01-02")
			createBody.SmsExpiredDate = time.Now().Format("2006-01-02")
			createBody.FastbankExpiredDate = time.Now().Format("2006-01-02")
			if _, err := s.repo.CreateWebStatus(createBody); err != nil {
				return nil, internalServerError(err)
			}
			// REGET
			record2, err := s.repo.GetLocalWebInfo()
			if err != nil {
				return nil, internalServerError(err)
			}
			web = record2
		} else {
			return nil, internalServerError(err)
		}
	}

	// Sync with Remote Master
	webinfo, err := s.downlineService.UpdateDownlineBalance()
	if err == nil {
		web = webinfo
	}

	// Set FastBank Package Type
	web.FastBankPackageType = "CREDIT"
	// Check Buffe Credit //
	if web.FastbankFreeStartDate != "" && web.FastbankFreeEndDate != "" {
		actionAt := time.Now()
		buffeStartAt := actionAt.AddDate(1, 2, 3) // MOCK INVALID DATE
		buffeEndAt := actionAt.AddDate(1, 2, 3)   // MOCK INVALID DATE
		if dbStartAt, err := time.Parse("2006-01-02T00:00:00Z", web.FastbankFreeStartDate); err == nil {
			buffeStartAt = dbStartAt
		}
		if dbEndAt, err := time.Parse("2006-01-02T00:00:00Z", web.FastbankFreeEndDate); err == nil {
			buffeEndAt = dbEndAt.AddDate(0, 0, 1) // END AT 23:59:59
		}
		// log.Println("buffeStartAt", buffeStartAt)
		// log.Println("buffeEndAt", buffeEndAt)
		if actionAt.After(buffeStartAt) && actionAt.Before(buffeEndAt) {
			web.FastBankPackageType = "FREE"
		}
	}

	// [********] Get config web otp
	// GetConfiguration() (*model.ConfigurationResponse, error)
	configuration, err := s.repo.GetConfiguration()
	if err != nil {
		return nil, internalServerError(err)
	}

	web.UseOtpRegister = configuration.UseOtpRegister

	return web, nil
}

func (s renewalWebService) GetWebRenewalPackageList(req model.WebRenewalPackageListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	list, total, err := s.repo.GetWebRenewalPackageList(req)
	if err != nil {
		return nil, internalServerError(err)
	}

	// [********] เงื่อนไขการต่ออายุ
	// LimitAdminCount        int        `json:"ผู้ใช้งาน"`
	// LimitUserCount         int        `json:"จำนวนสมาชิก"`
	// LimitTransactionCount  int        `json:"ไม่ได้ใช้"`
	// LimitTransactionAmount float64    `json:"ยอดฝาก"`
	// LimitBankAccountCount  int        `json:"จำนวนบช ฝาก"`
	// บช AGENT ไม่มี ไม่จำกัด เพราะแต่ละเว็บมี 1 AGENT

	adminCount, err := s.repo.GetWebAdminCount()
	if err != nil {

		return nil, errors.New("FAILED_TOGET_PACKAGE")
	}
	userCount, err := s.repo.GetWebUserCount()
	if err != nil {

		return nil, errors.New("FAILED_TOGET_PACKAGE")
	}
	bankAccountInfo, err := s.repo.GetBankAccountInfo()
	if err != nil {

		return nil, errors.New("FAILED_TOGET_PACKAGE")
	}

	// Disable Overuse Package
	for i, pack := range list {
		list[i].IsEnabled = true
		if pack.LimitAdminCount > 0 && adminCount > int64(pack.LimitAdminCount) {
			fmt.Println("adminCount", adminCount, "pack.LimitAdminCount", pack.LimitAdminCount)
			list[i].IsEnabled = false
			list[i].LimitDetail = fmt.Sprintf("ผู้ใช้งานเกิน %d คน (ปัจจุบัน %d คน)", pack.LimitAdminCount, adminCount)
			continue
		}
		if pack.LimitUserCount > 0 && userCount > int64(pack.LimitUserCount) {
			fmt.Println("userCount", userCount, "pack.LimitUserCount", pack.LimitUserCount)
			list[i].IsEnabled = false
			list[i].LimitDetail = fmt.Sprintf("จำนวนสมาชิกเกิน %d คน (ปัจจุบัน %d คน)", pack.LimitUserCount, userCount)
			continue
		}
		if pack.LimitBankAccountCount > 0 && (bankAccountInfo.DepositAccountCount) > (pack.LimitBankAccountCount) {
			fmt.Println("bankAccountInfo", helper.StructJson(bankAccountInfo))
			fmt.Println("LimitBankAccountCount", pack.LimitBankAccountCount)
			list[i].IsEnabled = false
			list[i].LimitDetail = fmt.Sprintf("จำนวนบช ฝากเกิน %d บช (ปัจจุบัน %d บช)", pack.LimitBankAccountCount, bankAccountInfo.DepositAccountCount)
			continue
		}
		if pack.LimitTransactionAmount > 0 && (bankAccountInfo.LastMonthDepositPrice) > (pack.LimitTransactionAmount) {
			fmt.Println("bankAccountInfo", helper.StructJson(bankAccountInfo))
			fmt.Println("LimitTransactionAmount", pack.LimitTransactionAmount)
			list[i].IsEnabled = false
			list[i].LimitDetail = fmt.Sprintf("ยอดฝากเกิน %s บาท (ปัจจุบัน %s บาท)", helper.Format(int64(pack.LimitTransactionAmount)), helper.Format(int64(bankAccountInfo.LastMonthDepositPrice)))
			continue
		}
	}

	// Master Web Discount BuyWebPackageDiscount
	// ** ไม่แสดงราคาลดที่ Package ** เพราะจะลดต่อครั้งที่ซื้อ **
	// webInfo, err := s.repo.GetWebInfo()
	// if err != nil {
	// 	log.Println("GetLocalWebInfo", err.Error())
	// }
	// if webInfo.BuyWebPackageDiscount > 0 {
	// 	// ลดราคา web Package ทุก Package
	// 	for i, pack := range list {
	// 		totalPrice := pack.PricePerMonth - webInfo.BuyWebPackageDiscount
	// 		if totalPrice < 0 {
	// 			totalPrice = 0
	// 		}
	// 		list[i].PricePerMonth = totalPrice
	// 	}
	// }

	return &model.SuccessWithPagination{
		List:  list,
		Total: total,
	}, nil
}

func (s renewalWebService) GetWebRenewalPackageById(id int64) (*model.WebRenewalPackageResponse, error) {

	record, err := s.repo.GetWebRenewalPackageById(id)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(recordNotFound)
		}
		return nil, internalServerError(err)
	}
	return record, nil
}

func (s renewalWebService) BuyWebRenewalPackage(req model.WebRenewalBuyRequest) (*int64, error) {

	actionAt := time.Now()
	if req.RenewalMonths < 1 || req.RenewalMonths > 12 {
		return nil, badRequest("INVALID_RENEWAL_MONTHS")
	}

	var createBody model.InvoiceCreateBody

	myWeb, err := s.GetLocalWebInfo()
	if err != nil {
		return nil, internalServerError(err)
	}

	packageInfo, err := s.repo.GetWebRenewalPackageById(req.PackageId)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound("PACKAGE_NOT_FOUND")
		}
		return nil, internalServerError(err)
	}

	// [********] เงื่อนไขการต่ออายุ
	adminCount, err := s.repo.GetWebAdminCount()
	if err != nil {
		return nil, errors.New("FAILED_TOGET_PACKAGE")
	}
	userCount, err := s.repo.GetWebUserCount()
	if err != nil {
		return nil, errors.New("FAILED_TOGET_PACKAGE")
	}
	bankAccountInfo, err := s.repo.GetBankAccountInfo()
	if err != nil {
		return nil, errors.New("FAILED_TOGET_PACKAGE")
	}

	// Disable Overuse Package
	if packageInfo.LimitAdminCount > 0 && adminCount > int64(packageInfo.LimitAdminCount) {
		return nil, errors.New("CANT_USE_THIS_PACKAGE")
	}
	if packageInfo.LimitUserCount > 0 && userCount > int64(packageInfo.LimitUserCount) {
		return nil, errors.New("CANT_USE_THIS_PACKAGE")
	}
	if packageInfo.LimitBankAccountCount > 0 && (bankAccountInfo.DepositAccountCount) > (packageInfo.LimitBankAccountCount) {
		return nil, errors.New("CANT_USE_THIS_PACKAGE")
	}
	if packageInfo.LimitTransactionAmount > 0 && (bankAccountInfo.LastMonthDepositPrice) > (packageInfo.LimitTransactionAmount) {
		return nil, errors.New("CANT_USE_THIS_PACKAGE")
	}

	// ** Discount per Buy ** Master Web Discount BuyWebPackageDiscount
	discountPriceSatang := 0.0
	webInfo, err := s.repo.GetWebInfo()
	if err != nil {
		log.Println("GetLocalWebInfo", err.Error())
	}
	if webInfo.BuyWebPackageDiscount > 0 {
		// ลดราคา web Package ทุก Package
		discountPriceSatang = webInfo.BuyWebPackageDiscount
		if discountPriceSatang <= 0 {
			discountPriceSatang = 0
		}
		discountPriceSatang = discountPriceSatang * 100
	}

	// [********] เอา VAT ออก
	vatPercent := 0.0
	sumPriceSatang := math.Floor(packageInfo.PricePerMonth*100) * (float64(req.RenewalMonths))
	vatPriceSatang := sumPriceSatang * (vatPercent / 100)

	// Check Product Price ** Discount per Buy **
	if (sumPriceSatang - discountPriceSatang) <= 0 {
		return nil, errors.New("CANT_USE_THIS_PACKAGE_FOR_FREE")
	}

	createBody.WebId = myWeb.Id
	createBody.WebName = myWeb.Name
	createBody.CreateBy = req.AdminId
	createBody.InvoiceNo = "INV" + actionAt.Format("06") // INVYYYY000000
	createBody.InvoiceTypeId = model.INVOICE_TYPE_WEB_RENEWAL
	createBody.PackageId = packageInfo.Id
	createBody.RenewDays = int(req.RenewalMonths * 30)
	createBody.PackageDetail = fmt.Sprintf("ต่ออายุ %s จำนวน %d เดือน", packageInfo.Name, req.RenewalMonths)
	createBody.BankId = req.BankId
	createBody.AccountNo = req.AccountNo
	createBody.AccountName = req.AccountName
	createBody.InvoiceAt = actionAt
	createBody.ExpireAt = actionAt.AddDate(0, 0, 7) // DEF
	createBody.PaymentDetail = req.PaymentDetail
	createBody.StatusId = model.INVOICE_STATUS_WAIT_PAYMENT
	createBody.SumPrice = sumPriceSatang / 100
	createBody.VatPercent = vatPercent
	createBody.VatPrice = vatPriceSatang / 100
	createBody.DiscountPrice = discountPriceSatang / 100
	createBody.TotalPrice = ((sumPriceSatang - discountPriceSatang) + vatPriceSatang) / 100
	insertId, err := s.repo.CreateWebRenewalInvoice(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}
	return insertId, nil
}

func (s renewalWebService) GetSmsRenewalPackageList(req model.SmsRenewalPackageListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	list, total, err := s.repo.GetSmsRenewalPackageList(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	return &model.SuccessWithPagination{
		List:  list,
		Total: total,
	}, nil
}

func (s renewalWebService) GetSmsRenewalPackageById(id int64) (*model.SmsRenewalPackageResponse, error) {

	record, err := s.repo.GetSmsRenewalPackageById(id)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(recordNotFound)
		}
		return nil, internalServerError(err)
	}
	return record, nil
}

func (s renewalWebService) BuySmsRenewalPackage(req model.SmsRenewalBuyRequest) (*int64, error) {

	actionAt := time.Now()

	myWeb, err := s.GetLocalWebInfo()
	if err != nil {
		return nil, internalServerError(err)
	}

	packageInfo, err := s.repo.GetSmsRenewalPackageById(req.PackageId)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound("PACKAGE_NOT_FOUND")
		}
		return nil, internalServerError(err)
	}

	// [********] เอา VAT ออก
	vatPercent := 0.0
	sumPriceSatang := packageInfo.Price * 100
	vatPriceSatang := sumPriceSatang * (vatPercent / 100)

	var createBody model.InvoiceCreateBody
	createBody.WebId = myWeb.Id
	createBody.WebName = myWeb.Name
	createBody.CreateBy = req.AdminId
	createBody.InvoiceNo = "INV" + actionAt.Format("06") // INVYYYY000000
	createBody.InvoiceTypeId = model.INVOICE_TYPE_SMS_RENEWAL
	createBody.PackageId = packageInfo.Id
	createBody.RenewCreditAmount = packageInfo.RenewalCredits
	createBody.PackageDetail = fmt.Sprintf("เติมเครดิต SMS %s จำนวน %d เครดิต", packageInfo.Name, packageInfo.RenewalCredits)
	createBody.BankId = req.BankId
	createBody.AccountNo = req.AccountNo
	createBody.AccountName = req.AccountName
	createBody.InvoiceAt = actionAt
	createBody.ExpireAt = actionAt.AddDate(0, 0, 7) // DEF
	createBody.PaymentDetail = req.PaymentDetail
	createBody.StatusId = model.INVOICE_STATUS_WAIT_PAYMENT
	createBody.SumPrice = sumPriceSatang / 100
	createBody.VatPercent = vatPercent
	createBody.VatPrice = vatPriceSatang / 100
	createBody.TotalPrice = (sumPriceSatang + vatPriceSatang) / 100
	insertId, err := s.repo.CreateSmsRenewalInvoice(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}
	return insertId, nil
}

func (s renewalWebService) GetFastbankRenewalPackageList(req model.FastbankRenewalPackageListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	list, total, err := s.repo.GetFastbankRenewalPackageList(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	return &model.SuccessWithPagination{
		List:  list,
		Total: total,
	}, nil
}

func (s renewalWebService) GetFastbankRenewalPackageById(id int64) (*model.FastbankRenewalPackageResponse, error) {

	record, err := s.repo.GetFastbankRenewalPackageById(id)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(recordNotFound)
		}
		return nil, internalServerError(err)
	}
	return record, nil
}

func (s renewalWebService) BuyFastbankRenewalPackage(req model.FastbankRenewalBuyRequest) (*int64, error) {

	actionAt := time.Now()

	myWeb, err := s.GetLocalWebInfo()
	if err != nil {
		return nil, internalServerError(err)
	}

	packageInfo, err := s.repo.GetFastbankRenewalPackageById(req.PackageId)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound("PACKAGE_NOT_FOUND")
		}
		return nil, internalServerError(err)
	}

	// [********] เอา VAT ออก
	vatPercent := 0.0
	sumPriceSatang := packageInfo.Price * 100
	vatPriceSatang := sumPriceSatang * (vatPercent / 100)

	var createBody model.InvoiceCreateBody
	createBody.WebId = myWeb.Id
	createBody.WebName = myWeb.Name
	createBody.CreateBy = req.AdminId
	createBody.InvoiceNo = "INV" + actionAt.Format("06") // INVYYYY000000
	createBody.InvoiceTypeId = model.INVOICE_TYPE_FASTBANK_RENEWAL
	createBody.PackageId = packageInfo.Id
	createBody.RenewCreditAmount = packageInfo.RenewalCredits
	createBody.PackageDetail = fmt.Sprintf("เติมเครดิตธนาคาร %s จำนวน %d เครดิต", packageInfo.Name, packageInfo.RenewalCredits)
	createBody.BankId = req.BankId
	createBody.AccountNo = req.AccountNo
	createBody.AccountName = req.AccountName
	createBody.InvoiceAt = actionAt
	createBody.ExpireAt = actionAt.AddDate(0, 0, 7) // DEF
	createBody.PaymentDetail = req.PaymentDetail
	createBody.StatusId = model.INVOICE_STATUS_WAIT_PAYMENT
	createBody.SumPrice = sumPriceSatang / 100
	createBody.VatPercent = vatPercent
	createBody.VatPrice = vatPriceSatang / 100
	createBody.TotalPrice = (sumPriceSatang + vatPriceSatang) / 100
	insertId, err := s.repo.CreateFastbankRenewalInvoice(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}
	return insertId, nil
}

func (s renewalWebService) GetInvoiceList(req model.InvoiceListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	list, total, err := s.repo.GetInvoiceList(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	return &model.SuccessWithPagination{
		List:  list,
		Total: total,
	}, nil
}

func (s renewalWebService) GetInvoiceById(id int64) (*model.InvoiceResponse, error) {

	record, err := s.repo.GetInvoiceById(id)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(recordNotFound)
		}
		return nil, internalServerError(err)
	}
	return record, nil
}

func (s renewalWebService) ViewBillById(id int64) (*model.InvoiceBillResponse, error) {

	record, err := s.repo.ViewBillById(id)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(recordNotFound)
		}
		return nil, internalServerError(err)
	}
	return record, nil
}

func (s renewalWebService) ViewReceiptById(id int64) (*model.InvoiceReceiptResponse, error) {

	record, err := s.repo.ViewReceiptById(id)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(recordNotFound)
		}
		return nil, internalServerError(err)
	}
	return record, nil
}

func (s renewalWebService) ConfirmPayment(req model.InvoiceConfirmPaymentRequest) error {

	invoice, err := s.repo.GetInvoiceById(req.InvoiceId)
	if err != nil {
		if err.Error() == recordNotFound {
			return notFound("INVOICE_NOT_FOUND")
		}
		return internalServerError(err)
	}

	if invoice.StatusId == model.INVOICE_STATUS_WAIT_PAYMENT {
		actionAt := time.Now()
		if req.PaidAt == nil {
			req.PaidAt = &actionAt
		}
		var confirmBody model.InvoiceConfirmPaymentBody
		confirmBody.Id = req.InvoiceId
		confirmBody.PaidAt = *req.PaidAt
		confirmBody.PaidBy = req.AdminId
		confirmBody.SlipImagePath = req.SlipImagePath
		confirmBody.RawQrCode = req.RawQrCode
		if err := s.repo.ConfirmPayment(confirmBody); err != nil {
			return internalServerError(err)
		}

		// SYNC WITH REMOTE
		if _, err := s.downlineService.SubmitInvoice(invoice.Id); err != nil {
			log.Println("downlineService.SubmitInvoice", err.Error())
		} else {
			log.Println("downlineService.SubmitInvoice", "success")
		}

	} else {
		return badRequest("INVOICE_STATUS_NOT_WAIT_PAYMENT")
	}
	return nil
}

func (s renewalWebService) ConfirmInvoice(req model.InvoiceConfirmRequest) error {

	invoice, err := s.repo.GetInvoiceById(req.InvoiceId)
	if err != nil {
		if err.Error() == recordNotFound {
			return notFound("INVOICE_NOT_FOUND")
		}
		return internalServerError(err)
	}

	myWeb, err := s.GetLocalWebInfo()
	if err != nil {
		return internalServerError(err)
	}
	webExpireAt, err := time.Parse("2006-01-02T15:04:05Z", myWeb.WebExpiredDate)
	if err != nil {
		return internalServerError(err)
	}
	smsExpireAt, err := time.Parse("2006-01-02T15:04:05Z", myWeb.SmsExpiredDate)
	if err != nil {
		return internalServerError(err)
	}
	fastbankExpireAt, err := time.Parse("2006-01-02T15:04:05Z", myWeb.FastbankExpiredDate)
	if err != nil {
		return internalServerError(err)
	}

	if invoice.StatusId == model.INVOICE_STATUS_WAIT_CONFIRM {
		actionAt := time.Now()

		var confirmBody model.InvoiceConfirmBody
		confirmBody.Id = req.InvoiceId
		confirmBody.ConfirmAt = actionAt
		confirmBody.ConfirmByName = "dev-mock"
		if err := s.repo.ConfirmInvoice(confirmBody); err != nil {
			return internalServerError(err)
		}

		// UPDATE WEB BY INVOICE TYPE
		if invoice.InvoiceTypeId == model.INVOICE_TYPE_WEB_RENEWAL {
			if invoice.RenewDays > 0 {
				webExpireAt = webExpireAt.AddDate(0, 0, invoice.RenewDays)
			}
			var updateBody model.WebMasterUpdateWebRenewalBody
			updateBody.WebId = invoice.WebId
			updateBody.WebExpiredDate = webExpireAt.Format("2006-01-02")
			updateBody.CurrentWebPackageId = invoice.PackageId
			if err := s.repo.UpdateWebRenewal(updateBody); err != nil {
				return internalServerError(err)
			}
		} else if invoice.InvoiceTypeId == model.INVOICE_TYPE_SMS_RENEWAL {
			if invoice.RenewDays > 0 {
				smsExpireAt = smsExpireAt.AddDate(0, 0, invoice.RenewDays)
			}
			var updateBody model.WebMasterUpdateSmsRenewalBody
			updateBody.WebId = invoice.WebId
			updateBody.SmsCredit = int64(invoice.RenewCreditAmount)
			updateBody.SmsExpiredDate = smsExpireAt.Format("2006-01-02")
			updateBody.CurrentSmsPackageId = invoice.PackageId
			if err := s.repo.UpdateSmsRenewal(updateBody); err != nil {
				return internalServerError(err)
			}
		} else if invoice.InvoiceTypeId == model.INVOICE_TYPE_FASTBANK_RENEWAL {
			if invoice.RenewDays > 0 {
				fastbankExpireAt = fastbankExpireAt.AddDate(0, 0, invoice.RenewDays)
			}
			var updateBody model.WebMasterUpdateFastbankRenewalBody
			updateBody.WebId = invoice.WebId
			updateBody.CurrentFastbankPackageId = invoice.PackageId
			updateBody.FastbankExpiredDate = fastbankExpireAt.Format("2006-01-02")
			updateBody.FastbankCredit = int64(invoice.RenewCreditAmount)
			if err := s.repo.UpdateFastbankRenewal(updateBody); err != nil {
				return internalServerError(err)
			}
		}
	} else {
		return badRequest("INVOICE_STATUS_NOT_WAIT_CONFIRM")
	}
	return nil
}

func (s renewalWebService) CreateInvoiceHengPayment(req model.InvoiceHengPaymentRequest) (*model.DownlineInvoiceHengPaymentCreateResponse, error) {

	invoice, err := s.repo.GetInvoiceById(req.InvoiceId)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound("INVOICE_NOT_FOUND")
		}
		return nil, internalServerError(err)
	}
	if invoice.StatusId != model.INVOICE_STATUS_WAIT_PAYMENT {
		return nil, badRequest("INVOICE_STATUS_NOT_WAIT_PAYMENT")
	}

	// onCreate Success
	var downlineReqBody model.DownlineInvoiceHengPaymentCreateRequest
	downlineReqBody.InvoiceId = invoice.Id
	downlineReqBody.Amount = invoice.TotalPrice
	qrData, err := s.repo.CreateInvoiceHengPayment(downlineReqBody)
	if err != nil {
		return nil, internalServerError(err)
	}
	return qrData, nil
}

func (s renewalWebService) CreateInvoicePayonexPayment(req model.InvoicePayonexPaymentRequest) (*model.DownlineInvoicePayonexPaymentCreateResponse, error) {

	invoice, err := s.repo.GetInvoiceById(req.InvoiceId)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound("INVOICE_NOT_FOUND")
		}
		return nil, internalServerError(err)
	}
	if invoice.StatusId != model.INVOICE_STATUS_WAIT_PAYMENT {
		return nil, badRequest("INVOICE_STATUS_NOT_WAIT_PAYMENT")
	}

	// onCreate Success
	var downlineReqBody model.DownlineInvoicePayonexPaymentCreateRequest
	downlineReqBody.InvoiceId = invoice.Id
	downlineReqBody.BankCode = invoice.BankCode
	downlineReqBody.AccountNo = invoice.AccountNo
	downlineReqBody.AccountName = invoice.AccountName
	downlineReqBody.Amount = invoice.TotalPrice
	qrData, err := s.repo.CreateInvoicePayonexPayment(downlineReqBody)
	if err != nil {
		return nil, internalServerError(err)
	}
	return qrData, nil
}

func (s renewalWebService) UploadInvoiceSlipToCloudFlare(imageFileBody *http.Request) (*model.FileUploadResponse, error) {

	fileReader, newFileName, err := imageFileBody.FormFile("file")
	if err != nil {
		log.Println("UploadInvoiceSlipToCloudFlare.ERROR.FormFile", err)
		return nil, err
	}

	filename := &newFileName.Filename
	folderName := "inv-slip"
	dbName := os.Getenv("DB_NAME")

	// [set imageCloudFlarePathName]
	pathName := fmt.Sprintf("cbgame/%v/%s/upload/image/", dbName, folderName)
	//! ส่ง Id กับไฟล์ reader
	var newImageId *model.FileUploadResponse
	fileData, err := s.repo.UploadImageToCloudflare(pathName, *filename, fileReader)
	if err != nil {
		log.Println("UploadInvoiceSlipToCloudFlare.ERROR.UploadImageToCloudflare", err)
		return nil, badRequest("UNABLE_TO_UPLOAD_FILE")
	}

	newImageId = &model.FileUploadResponse{
		ImageUrl: fileData.FileUrl,
	}
	return newImageId, nil
}

func (s *renewalWebService) UploadImageToS3InvoiceSlip(imageFileBody *http.Request) (*model.FileUploadResponse, error) {

	fileReader, newFileName, err := imageFileBody.FormFile("file")
	if err != nil {
		log.Println("UploadFileS3.ERROR", err)
		return nil, err
	}

	filename := &newFileName.Filename

	dbName := os.Getenv("DB_NAME")

	pathName := fmt.Sprintf("cbgame/%v/invoice-slip/", dbName)
	var newImageId *model.FileUploadResponse
	fileData, err := s.repo.UploadImageToS3(pathName, *filename, fileReader)
	if err != nil {
		log.Println("UploadFileS3.ERROR", err)
		return nil, badRequest("UNABLE_TO_UPLOAD_FILE")
	}

	newImageId = &model.FileUploadResponse{
		ImageUrl: fileData.ImageUrl,
	}

	return newImageId, nil
}
