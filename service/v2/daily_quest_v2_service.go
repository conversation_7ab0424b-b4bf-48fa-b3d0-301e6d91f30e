package v2

import (
	"cybergame-api/helper"
	modelV1 "cybergame-api/model"
	model "cybergame-api/model/v2"
	repositoryV1 "cybergame-api/repository"
	repositoryV2 "cybergame-api/repository/v2"
	serviceV1 "cybergame-api/service"
	"errors"
	"fmt"
	"gorm.io/gorm"
	"log"
	"math"
	"os"
	"sync"
	"time"
)

type DailyQuestService interface {
	// Admin
	CreateDailyQuest(req model.CreateDailyQuestRequest) error
	GetDailyQuests(req model.DailyQuestListRequest) (*model.SuccessWithPagination, error)
	GetDailyQuestById(id int64) (*model.DailyQuest, error)
	UpdateDailyQuest(req model.UpdateDailyQuestRequest) error
	UpdateDailyQuestActive(req model.UpdateDailyQuestActiveRequest) error
	DeleteDailyQuest(id int64) error
	GetDailyQuestCondition() ([]model.DailyQuestCondition, error)

	// Job
	CheckUserQuest() error
	CheckUserQuestByDate(statementDate string) error

	// Web
	WebGetDailyQuests(userId int64) ([]model.WebDailyQuest, error)
	WebGetUserDailyQuestList(userId int64, req model.DailyQuestListRequest) (*model.SuccessWithPagination, error)
	CreateUserDailyQuest(userId int64, req *model.CreateUserDailyQuestRequest) error
	ClaimUserDailyQuestReward(userId int64, req model.ClaimUserDailyQuestReward) error
	CreateTurnOverFromDailyQuestV2(userId int64, refId int64, noNumber int64, bonusAmount float64) error
}

type dailyQuestService struct {
	shareDb              *gorm.DB
	dailyQuestRepo       repositoryV2.DailyQuestRepository
	cronRepository       repositoryV1.CronRepository
	userCreditRepository repositoryV1.UserCreditRepository
	userQuestPlayLogRepo repositoryV2.UserQuestPlayLogRepository
	turnoverRepository   repositoryV1.TurnoverRepository
	agentInfoRepository  repositoryV1.AgentInfoRepository
}

func NewDailyQuestService(
	shareDb *gorm.DB,
	dailyQuestRepo repositoryV2.DailyQuestRepository,
	cronRepository repositoryV1.CronRepository,
	userQuestPlayLogRepo repositoryV2.UserQuestPlayLogRepository,
	userCreditRepository repositoryV1.UserCreditRepository,
	turnoverRepository repositoryV1.TurnoverRepository,
	agentInfoRepository repositoryV1.AgentInfoRepository,
) DailyQuestService {
	return &dailyQuestService{
		shareDb:              shareDb,
		dailyQuestRepo:       dailyQuestRepo,
		cronRepository:       cronRepository,
		userQuestPlayLogRepo: userQuestPlayLogRepo,
		userCreditRepository: userCreditRepository,
		turnoverRepository:   turnoverRepository,
		agentInfoRepository:  agentInfoRepository,
	}
}

func RacingRunCheckUserQuest(repo repositoryV1.AgentInfoRepository, agentName string, fnName string, adminId int64) (*int64, error) {

	actionAt := time.Now().UTC()
	racingKey := "CHECK_USER_QUEST_V2"

	var createBody modelV1.RaceActionCreateBody
	createBody.Name = fnName
	createBody.JsonRequest = helper.StructJson(map[string]interface{}{"agentName": agentName, "adminId": adminId})
	createBody.Status = "PENDING"

	createBody.ActionKey = fmt.Sprintf("%s_%s", racingKey, agentName)
	createBody.UnlockAt = actionAt.Add(time.Minute * 15) // Max run is around 10-15 minutes, So 30 minutes is enough
	if oldWork, err := repo.GetRaceActionByActionKey(createBody.ActionKey); err != nil {
		log.Println("RacingCheckRunUserQuest.ERROR.GetRaceActionByActionKey", err)
		if err != gorm.ErrRecordNotFound {
			return nil, internalServerError(errors.New("CANNOT_CHECK_ACTION"))
		}
		// CONTINUE..
	} else {
		if oldWork.Id > 0 && actionAt.After(oldWork.UnlockAt) {
			// UPDATE
			canceledKey := fmt.Sprintf("%s_DONE_%d", racingKey, oldWork.Id)
			canceledStatus := "TIMEOUT"
			var updateBody modelV1.RaceActionUpdateBody
			updateBody.ActionKey = &canceledKey
			updateBody.Status = &canceledStatus
			if err := repo.UpdateRaceCondition(oldWork.Id, updateBody); err != nil {
				log.Println("RacingCheckRunUserQuest.ERROR.UpdateRaceCondition", err)
				return nil, internalServerError(errors.New("WORK_IN_ACTION"))
			}
			// CONTINUE..
		} else {
			return nil, internalServerError(errors.New("WORK_IN_ACTION"))
		}
	}

	// CONTINUE.. only not found will be created
	actionId, err := repo.CreateRaceCondition(createBody)
	if err != nil {
		log.Println("RacingCheckRunUserQuest.ERROR.CreateRaceCondition", err)
		return nil, internalServerError(errors.New("CANNOT_CREATE_ACTION"))
	}
	return &actionId, nil
}

// Admin
func (s *dailyQuestService) CreateDailyQuest(req model.CreateDailyQuestRequest) error {
	// ตรวจสอบความถูกต้องของ Quest และ Condition ใน request
	for _, quest := range req.Quests {
		// ตรวจสอบว่า Quest ID มีอยู่จริง
		if exists, err := s.dailyQuestRepo.CheckQuestIdExist(quest.QuestId); err != nil {
			return internalServerError(err)
		} else if !exists {
			return badRequest(fmt.Sprintf("quest ID:%d not found", quest.QuestId))
		}

		// ตรวจสอบว่า Condition ID มีอยู่จริง
		if exists, err := s.dailyQuestRepo.CheckDailyQuestConditionById(quest.QuestConditionId); err != nil {
			return internalServerError(err)
		} else if !exists {
			return badRequest(fmt.Sprintf("daily quest condition ID:%d not found", quest.QuestConditionId))
		}
	}

	// บันทึก Daily Quest ลงใน repository
	if err := s.dailyQuestRepo.CreateDailyQuest(req); err != nil {
		return internalServerError(err)
	}

	return nil
}

func (s *dailyQuestService) GetDailyQuests(req model.DailyQuestListRequest) (*model.SuccessWithPagination, error) {
	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	list, total, err := s.dailyQuestRepo.GetDailyQuests(req)
	if err != nil {
		return nil, internalServerError(err)
	}

	return &model.SuccessWithPagination{Message: "Success", List: list, Total: total}, nil
}

func (s *dailyQuestService) GetDailyQuestById(id int64) (*model.DailyQuest, error) {
	quest, err := s.dailyQuestRepo.GetDailyQuestById(id)
	if err != nil {
		return nil, internalServerError(err)
	}
	if quest == nil {
		return nil, notFound(fmt.Sprintf("daily quest ID:%d not found", id))
	}
	return quest, nil
}

func (s *dailyQuestService) UpdateDailyQuest(req model.UpdateDailyQuestRequest) error {
	// Check if the daily quest exists
	dailyQuest, err := s.dailyQuestRepo.GetDailyQuestById(req.Id)
	if err != nil {
		return internalServerError(err)
	}
	if dailyQuest == nil {
		return notFound(fmt.Sprintf("daily quest ID:%d not found", req.Id))
	}

	// Check if the daily quest already exists
	for _, quest := range req.Quests {
		checkQuest, err := s.dailyQuestRepo.CheckQuestIdExist(quest.QuestId)
		if err != nil {
			return internalServerError(err)
		}
		if !checkQuest {
			return badRequest(fmt.Sprintf("quest ID:%d not found", quest.QuestId))
		}

		checkCondition, err := s.dailyQuestRepo.CheckDailyQuestConditionById(quest.QuestConditionId)
		if err != nil {
			return internalServerError(err)
		}
		if !checkCondition {
			return badRequest(fmt.Sprintf("daily quest condition ID:%d not found", quest.QuestConditionId))
		}
	}

	if err := s.dailyQuestRepo.UpdateDailyQuest(req); err != nil {
		return internalServerError(err)
	}

	return nil
}

func (s *dailyQuestService) UpdateDailyQuestActive(req model.UpdateDailyQuestActiveRequest) error {
	// Check if the daily quest exists
	dailyQuest, err := s.dailyQuestRepo.GetDailyQuestById(req.Id)
	if err != nil {
		return err
	}
	if dailyQuest == nil {
		return notFound(fmt.Sprintf("daily quest ID:%d not found", req.Id))
	}

	if req.IsActive {

		dailyActive, err := s.dailyQuestRepo.FindDailyQuestActive()
		if err != nil {
			return err
		}

		if dailyActive != 0 && dailyActive != req.Id {
			if err := s.dailyQuestRepo.UpdateDailyQuestIsActive(dailyActive, false); err != nil {
				return err
			}
		}
	}

	if err := s.dailyQuestRepo.UpdateDailyQuestIsActive(req.Id, req.IsActive); err != nil {
		return err
	}

	return nil
}

func (s *dailyQuestService) DeleteDailyQuest(id int64) error {
	dailyQuest, err := s.dailyQuestRepo.GetDailyQuestById(id)
	if err != nil {
		return internalServerError(err)
	}
	if dailyQuest == nil {
		return notFound(fmt.Sprintf("daily quest ID:%d not found", id))
	}

	if err := s.dailyQuestRepo.DeleteDailyQuest(id); err != nil {
		return internalServerError(err)
	}
	return nil
}

func (s *dailyQuestService) GetDailyQuestCondition() ([]model.DailyQuestCondition, error) {
	dailyQuestCondition, err := s.dailyQuestRepo.GetDailyQuestCondition()
	if err != nil {
		return nil, internalServerError(err)
	}
	if dailyQuestCondition == nil {
		return nil, notFound("daily quest condition not found")
	}

	return dailyQuestCondition, nil
}

// Job
func (s *dailyQuestService) CheckUserQuest() error {

	todayDailyQuest, err := s.dailyQuestRepo.GetTodayDailyQuest()
	if err != nil {
		return internalServerError(err)
	}
	if todayDailyQuest == nil {
		log.Println("No active daily quest found for today.")
		return nil
	}

	counter, err := s.dailyQuestRepo.CountUserQuestUnfinished()
	if err != nil {
		return internalServerError(err)
	}
	if counter == 0 {
		log.Println("No unfinished daily quests found for any users.")
		return nil
	}

	log.Printf("Total unfinished daily quests: %d", counter)

	limit := int64(500) // จำนวนผู้ใช้ที่ต้องการอัปเดตในแต่ละรอบ
	jobRound := int(math.Ceil(float64(counter) / float64(limit)))

	log.Printf("Job round: %d, limit: %d", jobRound, limit)

	actionAt := time.Now()
	statementAt := actionAt.AddDate(0, 0, 0)
	statementDate := fmt.Sprintf("%v-%02d-%02d", statementAt.Year(), int(statementAt.Month()), statementAt.Day())

	// RACE_CONDITION
	racingKey := "CHECK_USER_QUEST_V2"
	actionId, err := RacingRunCheckUserQuest(s.agentInfoRepository, os.Getenv("AGENT_NAME"), "CronCheckUserQuest", -2)
	if err != nil {
		return err
	}

	go func() {
		var wg sync.WaitGroup
		wg.Add(jobRound)

		for i := int64(0); i < int64(jobRound); i++ {
			go func(round int64) {
				defer wg.Done()

				log.Printf("Processing round %d of %d for daily quests", round+1, jobRound)

				page := round // เริ่มต้นที่ 1 เพราะ pagination เริ่มที่ 1

				userUnFinished, err := s.dailyQuestRepo.GetUserDailyQuestUnfinished(todayDailyQuest.Id, int(page), int(limit))
				if err != nil {
					log.Printf("Error fetching user IDs for daily quests: %v", err)
					return
				}
				if len(userUnFinished) == 0 {
					log.Println("No users found with unfinished daily quests.")
					return
				}

				// วนลูปตรวจสอบและอัปเดตเควสต์ที่ยังไม่สำเร็จ
				for _, user := range userUnFinished {

					if user.Status {
						continue
					}
					for _, detail := range user.Details {
						if detail.Status {
							continue
						}

						var questDetails *model.GetCheckDailyQuestDetail
						for _, item := range todayDailyQuest.Details {
							if item.QuestId == detail.QuestId {
								questDetails = item
								break
							}
						}

						// ตรวจสอบเงื่อนไขเควสต์ตามประเภท
						var conditionRequest model.UserQuestPlayLogQueryRequest
						conditionRequest.QuestTypeId = questDetails.QuestTypeId
						conditionRequest.QuestConditionId = questDetails.DailyQuestConditionId
						conditionRequest.QuestConditionAmount = questDetails.ConditionAmount

						result := s.checkQuestCondition(user.UserId, conditionRequest)
						if !result {
							continue
						}

						log.Printf("User ID %d completed quest ID %d for daily quest ID %d", user.UserId, detail.QuestId, todayDailyQuest.Id)

						// อัปเดตสถานะเควสต์เมื่อสำเร็จ
						req := model.UpdateUserDailyQuestDetails{
							UserDailyQuestDetailId: detail.Id,
							UserId:                 user.UserId,
							DailyQuestId:           todayDailyQuest.Id,
							Counter:                int64(len(todayDailyQuest.Details)),
						}
						if err := s.updateUserDailyQuestDetails(req); err != nil {
							log.Printf("Error updating user daily quest details for user ID %d: %v", user.UserId, err)
							continue
						}
					}

					// Check if the user daily quest is success
					if err := s.updateUserDailyQuestStatus(user.UserId, todayDailyQuest.Id, int64(len(todayDailyQuest.Details))); err != nil {
						log.Printf("Error updating user daily quest status for user ID %d: %v", user.UserId, err)
						continue
					}
				}
			}(i)
		}

		wg.Wait()

		// RACE_CONDITION UPDATE
		successKey := fmt.Sprintf("%s_DONE_%d", racingKey, *actionId)
		successStatus := "SUCCESS"
		var updateBody modelV1.RaceActionUpdateBody
		updateBody.ActionKey = &successKey
		updateBody.Status = &successStatus
		if err := s.agentInfoRepository.UpdateRaceCondition(*actionId, updateBody); err != nil {
			log.Println("CronCheckUserQuest.ERROR.UpdateRaceCondition", err)
			log.Println("Finish CronCheckUserQuest But ERROR UpdateRaceCondition")
		}
		log.Println("Finish CronCheckUserQuest statementDate=", statementDate)
	}()

	return nil
}

func (s *dailyQuestService) CheckUserQuestByDate(statementDate string) error {

	todayDailyQuest, err := s.dailyQuestRepo.GetTodayDailyQuest()
	if err != nil {
		return internalServerError(err)
	}
	if todayDailyQuest == nil {
		log.Println("No active daily quest found for today.")
		return nil
	}

	counter, err := s.dailyQuestRepo.CountUserQuestUnfinished()
	if err != nil {
		return internalServerError(err)
	}
	if counter == 0 {
		log.Println("No unfinished daily quests found for any users.")
		return nil
	}

	log.Printf("Total unfinished daily quests: %d", counter)

	limit := int64(500) // จำนวนผู้ใช้ที่ต้องการอัปเดตในแต่ละรอบ
	jobRound := int(math.Ceil(float64(counter) / float64(limit)))

	log.Printf("Job round: %d, limit: %d", jobRound, limit)

	// RACE_CONDITION
	racingKey := "CHECK_USER_QUEST_V2"
	actionId, err := RacingRunCheckUserQuest(s.agentInfoRepository, os.Getenv("AGENT_NAME"), "CronCheckUserQuestByDate", -2)
	if err != nil {
		return err
	}

	go func() {
		var wg sync.WaitGroup
		wg.Add(jobRound)

		for i := int64(0); i < int64(jobRound); i++ {
			go func(round int64) {
				defer wg.Done()

				log.Printf("Processing round %d of %d for daily quests", round+1, jobRound)

				page := round // เริ่มต้นที่ 1 เพราะ pagination เริ่มที่ 1

				userUnFinished, err := s.dailyQuestRepo.GetUserDailyQuestUnfinished(todayDailyQuest.Id, int(page), int(limit))
				if err != nil {
					log.Printf("Error fetching user IDs for daily quests: %v", err)
					return
				}
				if len(userUnFinished) == 0 {
					log.Println("No users found with unfinished daily quests.")
					return
				}

				// วนลูปตรวจสอบและอัปเดตเควสต์ที่ยังไม่สำเร็จ
				for _, user := range userUnFinished {

					if user.Status {
						continue
					}
					for _, detail := range user.Details {
						if detail.Status {
							continue
						}

						var questDetails *model.GetCheckDailyQuestDetail
						for _, item := range todayDailyQuest.Details {
							if item.QuestId == detail.QuestId {
								questDetails = item
								break
							}
						}

						// ตรวจสอบเงื่อนไขเควสต์ตามประเภท
						var conditionRequest model.UserQuestPlayLogQueryRequest
						conditionRequest.QuestTypeId = questDetails.QuestTypeId
						conditionRequest.QuestConditionId = questDetails.DailyQuestConditionId
						conditionRequest.QuestConditionAmount = questDetails.ConditionAmount

						result := s.checkQuestCondition(user.UserId, conditionRequest)
						if !result {
							continue
						}

						// อัปเดตสถานะเควสต์เมื่อสำเร็จ
						req := model.UpdateUserDailyQuestDetails{
							UserDailyQuestDetailId: detail.Id,
							UserId:                 user.UserId,
							DailyQuestId:           todayDailyQuest.Id,
						}
						if err := s.updateUserDailyQuestDetails(req); err != nil {
							log.Printf("Error updating user daily quest details for user ID %d: %v", user.UserId, err)
							continue
						}
					}
				}
			}(i)
		}

		wg.Wait()

		// RACE_CONDITION UPDATE
		successKey := fmt.Sprintf("%s_DONE_%d", racingKey, *actionId)
		successStatus := "SUCCESS"
		var updateBody modelV1.RaceActionUpdateBody
		updateBody.ActionKey = &successKey
		updateBody.Status = &successStatus
		if err := s.agentInfoRepository.UpdateRaceCondition(*actionId, updateBody); err != nil {
			log.Println("CronCheckUserQuestByDate.ERROR.UpdateRaceCondition", err)
			log.Println("Finish CronCheckUserQuestByDate But ERROR UpdateRaceCondition")
		}
		log.Println("Finish CronCheckUserQuestByDate statementDate=", statementDate)
	}()

	return nil
}

// Web
func (s *dailyQuestService) WebGetDailyQuests(userId int64) ([]model.WebDailyQuest, error) {
	dailyQuests, err := s.dailyQuestRepo.WebGetDailyQuests(userId)
	if err != nil {
		return nil, internalServerError(err)
	}
	return dailyQuests, nil
}

func (s *dailyQuestService) WebGetUserDailyQuestList(userId int64, req model.DailyQuestListRequest) (*model.SuccessWithPagination, error) {
	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	list, total, err := s.dailyQuestRepo.GetUserDailyQuestList(userId, req)
	if err != nil {
		return nil, internalServerError(err)
	}

	return &model.SuccessWithPagination{Message: "Success", List: list, Total: total}, nil
}

func (s *dailyQuestService) CreateUserDailyQuest(userId int64, req *model.CreateUserDailyQuestRequest) error {

	if dailyExists, err := s.dailyQuestRepo.CheckDailyQuestExist(req.DailyQuestId); err != nil {
		return internalServerError(err)
	} else if !dailyExists {
		return badRequest(fmt.Sprintf("daily quest ID:%d not found", req.DailyQuestId))
	}

	if questExists, err := s.dailyQuestRepo.CheckQuestIdExist(req.QuestId); err != nil {
		return internalServerError(err)
	} else if !questExists {
		return badRequest(fmt.Sprintf("daily quest ID:%d not found", req.QuestId))
	}

	if err := s.dailyQuestRepo.CreateUserDailyQuest(userId, req); err != nil {
		return internalServerError(err)
	}

	return nil
}

func (s *dailyQuestService) ClaimUserDailyQuestReward(userId int64, req model.ClaimUserDailyQuestReward) error {
	// ดึงข้อมูลเควสประจำวันของผู้ใช้
	userDailyQuest, err := s.dailyQuestRepo.GetUserDailyQuestClaimDetailById(userId, req.UserDailyQuestId)
	if err != nil {
		log.Println("Daily Quest GetUserDailyQuestClaimDetailById", err)
		return err
	}
	if userDailyQuest == nil {
		log.Println("Daily Quest GetUserDailyQuestClaimDetailById userDailyQuest is nil")
		return notFound(fmt.Sprintf("user daily quest ID:%d not found", req.UserDailyQuestId))
	}

	// กำหนดวันที่รับเควส (UTC +7) เพื่อให้วนกลับมาเริ่มวันแรกได้
	dateReceived := time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02")

	// race condition sigle request
	type JsonRequest struct {
		UserId int64 `json:"userId"`
	}
	jsonRequest := JsonRequest{
		UserId: userId,
	}

	// สร้าง race condition action
	createBody := modelV1.RaceActionCreateBody{
		Name:        "DAILY_QUEST_V2",
		JsonRequest: helper.StructJson(jsonRequest),
		Status:      "SUCCESS",
		ActionKey:   fmt.Sprintf("DAILY_QUEST_V2_CLAIM_U%d_DATE_%s", userId, dateReceived),
		UnlockAt:    time.Now().UTC().Add(time.Second),
	}

	// ตรวจสอบว่า action นี้ถูกสร้างไปแล้วหรือไม่
	if _, err := s.cronRepository.GetRaceActionIdByActionKey(createBody.ActionKey); err != nil {
		if err != gorm.ErrRecordNotFound {
			log.Println("Daily Quest GetRaceActionIdByActionKey 1", err)
			return internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
		}
	} else {
		log.Println("Daily Quest .GetRaceActionIdByActionKey 2", err)
		return internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
	}

	actionId, err := s.cronRepository.CreateRaceCondition(createBody)
	if err != nil {
		log.Println(" Daily Quest CreateRaceCondition", err)
		return badRequest("PLEASE_WAIT_ONE_MINUTE_YOU_JUST_CLICKED_A_MOMENT_AGO_PLEASE_CHECK_THE_STATEMENT_AGAIN")
	}
	if actionId == 0 {
		log.Println("Daily Quest CreateRaceCondition actionId is 0")
		return badRequest("PLEASE_WAIT_ONE_MINUTE_YOU_JUST_CLICKED_A_MOMENT_AGO_PLEASE_CHECK_THE_STATEMENT_AGAIN")
	}

	//// เพิ่มเครดิตให้ผู้ใช้
	userCreditReq := modelV1.UserTransactionCreateRequest{
		RefId:       &userDailyQuest.Id,
		UserId:      userId,
		TypeId:      modelV1.CREDIT_TYPE_DAILY_QUEST_BONUS,
		BonusAmount: userDailyQuest.Reward,
		Detail:      "โบนัสเควสประจำวัน v2",
		StartWorkAt: time.Now().UTC(),
	}
	_, err = s.userCreditRepository.IncreaseUserCredit(userCreditReq)
	if err != nil {
		log.Println("Daily Quest IncreaseUserCredit", err)
		return err
	} else {
		// สร้าง turnover จากเควสประจำวัน
		err := s.CreateTurnOverFromDailyQuestV2(userId, userDailyQuest.Id, userDailyQuest.Id, userDailyQuest.Reward)
		if err != nil {
			log.Println("Daily Quest CreateTurnOverFromDailyQuestV2", err)
			return err
		}
	}

	// อัพเดทสถานะเควสประจำวัน
	return s.dailyQuestRepo.UpdateUserDailyQuestClaimStatus(userId, userDailyQuest.Id)
}

func (s *dailyQuestService) CreateTurnOverFromDailyQuestV2(userId int64, refId int64, noNumber int64, bonusAmount float64) error {
	// ดึงการตั้งค่าเทิร์นโอเวอร์
	turnSetting, err := serviceV1.GetTurnoverSetting(repositoryV1.NewTurnoverRepository(s.shareDb))
	if err != nil {
		return nil
	}

	// คำนวณยอดเทิร์นโอเวอร์ตามเปอร์เซ็นต์ (ถ้ามี)
	turnAmount := 0.0
	if turnSetting.TidturnDailyQuestV2Percent > 0 {
		turnAmount = math.Ceil(bonusAmount * float64(turnSetting.TidturnDailyQuestV2Percent) / 100)
	}

	// เตรียมข้อมูลสำหรับสร้างรายการเทิร์นโอเวอร์
	now := time.Now().UTC()
	createBody := modelV1.TurnoverUserStatementCreateBody{
		UserId:          userId,
		RefTypeId:       refId,
		TypeId:          modelV1.TURN_BONUS_DAILY_QUEST_V2,
		Name:            modelV1.TURNOVER_CATE_DAILY_QUEST_V2,
		PromotionName:   fmt.Sprintf("รายได้ทำเควสประวัน v2 ID: %d", noNumber),
		BonusAmount:     bonusAmount,
		StatusId:        modelV1.TURNOVER_STATEMENT_STATUS_PENDING,
		StartTurnAmount: turnAmount,
		StartTurnAt:     &now,
		TotalTurnAmount: turnAmount,
	}

	// สร้างรายการเทิร์นโอเวอร์
	createdTurnId, err := s.turnoverRepository.CreateTurnoverUserStatement(createBody)
	if err != nil {
		return err
	}

	// ถ้าไม่มีเทิร์นโอเวอร์ (turnAmount = 0) อัปเดตสถานะเป็น COMPLETED และสร้าง Withdraw Log
	if turnAmount == 0 {
		// อัปเดตสถานะเทิร์นโอเวอร์เป็น COMPLETED
		updateBody := modelV1.TurnoverUserStatementUpdateBody{
			StatusId:        modelV1.TURNOVER_STATEMENT_STATUS_COMPLETED,
			TotalTurnAmount: &turnAmount,
			EndTurnAt:       &now,
		}
		if err := s.turnoverRepository.UpdateTurnoverUserStatement(*createdTurnId, updateBody); err != nil {
			log.Printf("Failed to update turnover statement: %v", err)
		}

		// สร้าง Withdraw Log
		withdrawLog := modelV1.CreateTurnoverUserWithdrawLog{
			UserId:             userId,
			LogKey:             fmt.Sprintf("BONUS_DAILY_QUEST_V2_U%d_D%s", userId, now.Format("20060102150405")),
			TotalWithdrawPrice: 0,
			CurrentTurn:        0,
			PlayTotal:          0,
			LastPlayY:          0,
			LastTotalX:         0,
		}
		if _, err := s.turnoverRepository.CreateTurnoverUserWithdrawLog(withdrawLog); err != nil {
			log.Printf("Failed to create turnover withdraw log: %v", err)
		}
	}

	return nil
}

func (s *dailyQuestService) checkQuestCondition(userId int64, req model.UserQuestPlayLogQueryRequest) bool {
	switch req.QuestTypeId {
	case 1:
		// ตรวจสอบ today check in
		log.Printf("Checking user daily quest for QuestTypeId: %d", req.QuestTypeId)
		check, err := s.userQuestPlayLogRepo.UserQuestTodayCheckin(userId)
		if err != nil {
			log.Printf("Error checking user daily quest: %v", err)
			return false
		}
		return check
	case 2:
		// ตรวจสอบ today deposit
		log.Printf("Checking user daily quest for QuestTypeId: %d", req.QuestTypeId)
		check, err := s.userQuestPlayLogRepo.UserQuestTodayDeposit(userId, req)
		if err != nil {
			log.Printf("Error checking user daily quest: %v", err)
			return false
		}
		return check
	case 3, 4, 5, 6, 7:
		// ตรวจสอบ log การเล่น
		log.Printf("Checking user daily quest for QuestTypeId: %d", req.QuestTypeId)
		check, err := s.userQuestPlayLogRepo.UserQuestQueryPlayLog(userId, req)
		if err != nil {
			log.Printf("Error checking quest log: %v", err)
			return false
		}
		return check
	default:
		return false
	}
}

func (s *dailyQuestService) updateUserDailyQuestDetails(req model.UpdateUserDailyQuestDetails) error {
	if err := s.dailyQuestRepo.UpdateUserDailyQuestDetailSuccess(req.UserDailyQuestDetailId); err != nil {
		return internalServerError(err)
	}

	//// Check if the user daily quest is success
	//if err := s.updateUserDailyQuestStatus(req.UserId, req.DailyQuestId, req.Counter); err != nil {
	//	return err
	//}

	return nil
}

func (s *dailyQuestService) updateUserDailyQuestStatus(userId, dailyQuestId int64, counter int64) error {
	// ดึงข้อมูล UserDailyQuest ตาม userId และ dailyQuestId
	userDailyQuests, err := s.dailyQuestRepo.GetUserDailyQuestById(userId, dailyQuestId)
	if err != nil {
		return internalServerError(err)
	}
	if userDailyQuests == nil || len(userDailyQuests.Quests) == 0 {
		return notFound(fmt.Sprintf("user daily quest ID:%d not found", dailyQuestId))
	}

	// ตรวจสอบว่า counter ตรงกับจำนวน Quest ที่มีอยู่ใน UserDailyQuest
	if len(userDailyQuests.Quests) == int(counter) {

		// ตรวจสอบว่า Quest ทุกอันมี status เป็น true
		for _, quest := range userDailyQuests.Quests {
			if !quest.Status {
				return nil // ออกทันทีหากพบ Quest ที่ status เป็น false
			}
		}

		// อัปเดตสถานะ UserDailyQuest เป็น true
		updateUserDailyQuest := model.UpdateUserDailyQuest{
			Id:     userDailyQuests.Id,
			Status: true,
		}
		if err := s.dailyQuestRepo.UpdateUserDailyQuestStatus(updateUserDailyQuest); err != nil {
			return err
		}
	}

	return nil
}
