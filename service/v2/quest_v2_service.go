package v2

import (
	"cybergame-api/helper"
	model "cybergame-api/model/v2"
	repository "cybergame-api/repository/v2"
	"fmt"
	"log"
	"net/http"
	"os"
)

type QuestService interface {
	// QuestType
	GetQuestType() ([]model.QuestType, error)

	// Quest
	CreateQuest(req model.CreateQuest) error
	GetQuest(req model.QuestListRequest) (*model.SuccessWithPagination, error)
	UpdateQuest(req model.UpdateQuest) error
	DeleteQuest(id int64) error
	UploadImageToS3Quest(imageFileBody *http.Request) (*model.ImageUploadResponse, error)
}

type questService struct {
	questRepo repository.QuestRepository
	s3V2Repo  repository.S3V2Repository
}

func NewQuestService(
	questRepo repository.QuestRepository,
	s3V2Repo repository.S3V2Repository,
) QuestService {
	return &questService{questRepo, s3V2Repo}
}

func (s *questService) CreateQuest(req model.CreateQuest) error {
	// Check if quest type id already exists
	questType, err := s.questRepo.CheckQuestTypeIdExist(req.QuestTypeId)
	if err != nil {
		return internalServerError(err)
	}
	if !questType {
		return notFound(fmt.Sprintf("Quest type id %d not found", req.QuestTypeId))
	}

	// Check if quest name duplicate
	duplicate, err := s.questRepo.CheckQuestDuplicateName(req.Name)
	if err != nil {
		return internalServerError(err)
	}
	if duplicate {
		return badRequest(fmt.Sprintf("Quest name %s already exists", req.Name))
	}

	if err = s.questRepo.CreateQuest(req); err != nil {
		return internalServerError(err)
	}

	return nil
}

func (s *questService) GetQuest(req model.QuestListRequest) (*model.SuccessWithPagination, error) {
	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	list, total, err := s.questRepo.GetQuest(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	return &model.SuccessWithPagination{
		List:  list,
		Total: total,
	}, nil
}

func (s *questService) UpdateQuest(req model.UpdateQuest) error {
	// Check if quest type id already exists
	questType, err := s.questRepo.CheckQuestTypeIdExist(req.QuestTypeId)
	if err != nil {
		return internalServerError(err)
	}
	if !questType {
		return notFound(fmt.Sprintf("Quest type id %d not found", req.QuestTypeId))
	}

	// Check if quest name duplicate
	name, err := s.questRepo.CheckQuestDuplicateNameAndIdNot(req.Name, req.Id)
	if err != nil {
		return internalServerError(err)
	}
	if name {
		return badRequest(fmt.Sprintf("Quest name %s already exists", req.Name))
	}

	if err := s.questRepo.UpdateQuest(req); err != nil {
		return internalServerError(err)
	}
	return nil
}

func (s *questService) DeleteQuest(id int64) error {
	// Check if quest id already exists
	quest, err := s.questRepo.GetQuestById(id)
	if err != nil {
		return internalServerError(err)
	}
	if quest == nil {
		return notFound(fmt.Sprintf("Quest id %d not found", id))
	}

	if err := s.questRepo.DeleteQuest(id); err != nil {
		return internalServerError(err)
	}
	return nil
}

func (s *questService) GetQuestType() ([]model.QuestType, error) {
	result, err := s.questRepo.GetQuestType()
	if err != nil {
		return nil, internalServerError(err)
	}
	return result, nil
}

func (s *questService) UploadImageToS3Quest(imageFileBody *http.Request) (*model.ImageUploadResponse, error) {

	fileReader, _, err := imageFileBody.FormFile("file")
	if err != nil {
		log.Println("UploadFileS3.ERROR", err)
		return nil, err
	}

	//filename := &newFileName.Filename

	dbName := os.Getenv("DB_NAME")

	pathName := fmt.Sprintf("%v/cbgame/quest/", dbName)
	var newImageId *model.ImageUploadResponse
	fileData, err := s.s3V2Repo.UploadImageToS3(pathName, fileReader)
	if err != nil {
		log.Println("UploadFileS3.ERROR", err)
		return nil, badRequest("UNABLE_TO_UPLOAD_FILE")
	}

	newImageId = &model.ImageUploadResponse{
		ImageUrl: fileData.ImageUrl,
	}

	return newImageId, nil
}
