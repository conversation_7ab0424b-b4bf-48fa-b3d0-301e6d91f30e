package v2

import (
	"cybergame-api/helper"
	modelV1 "cybergame-api/model"
	modelV2 "cybergame-api/model/v2"
	"cybergame-api/repository"
	repositoryV2 "cybergame-api/repository/v2"
	"errors"
	"fmt"
	"gorm.io/gorm"
	"log"
	"os"
	"strings"
	"sync"
	"time"
)

type UserQuestPlayLogV2Service interface {
	GetUserQuestPlayLogs() error
	GetUserQuestPlayLogsByDate(statementDate string) error
	runAgcTodayWinLoseV2(productId int, statementDate string, force bool) error
	runPgHardToAgcTodayWinLoseV2(productId int, statementDate string, force bool) error
	runAgentCtwToAgcTodayWinLose(productId int, statementDate string, force bool) error
	UpdateUserQuestTodayPlayLogV2(createBody modelV2.UserQuestTodayPlayLogCreateBody, dbRecord modelV2.UserQuestTodayPlayLogV2Response) error
}

type userQuestPlayLogV2Service struct {
	agentInfoRepository  repository.AgentInfoRepository
	userQuestPlayLogRepo repositoryV2.UserQuestPlayLogRepository
	agentConnectV2Repo   repositoryV2.AgentConnectV2Repository
	dailyQuestRepo       repositoryV2.DailyQuestRepository
}

func NewUserQuestPlayLogV2Service(
	agentInfoRepository repository.AgentInfoRepository,
	userQuestPlayLogRepo repositoryV2.UserQuestPlayLogRepository,
	agentConnectV2Repo repositoryV2.AgentConnectV2Repository,
	dailyQuestRepo repositoryV2.DailyQuestRepository,
) UserQuestPlayLogV2Service {
	return &userQuestPlayLogV2Service{
		agentInfoRepository:  agentInfoRepository,
		userQuestPlayLogRepo: userQuestPlayLogRepo,
		agentConnectV2Repo:   agentConnectV2Repo,
		dailyQuestRepo:       dailyQuestRepo,
	}
}

func RacingRunUserQuestPlagLogs(repo repository.AgentInfoRepository, agentName string, fnName string, adminId int64) (*int64, error) {

	actionAt := time.Now().UTC()
	racingKey := "USER_QUEST_PLAYLOG_V2"

	var createBody modelV1.RaceActionCreateBody
	createBody.Name = fnName
	createBody.JsonRequest = helper.StructJson(map[string]interface{}{"agentName": agentName, "adminId": adminId})
	createBody.Status = "PENDING"

	createBody.ActionKey = fmt.Sprintf("%s_%s", racingKey, agentName)
	createBody.UnlockAt = actionAt.Add(time.Minute * 30) // Max run is around 10-15 minutes, So 30 minutes is enough

	if oldWork, err := repo.GetRaceActionByActionKey(createBody.ActionKey); err != nil {
		log.Println("RacingRunUserQuestPlayLogs.ERROR.GetRaceActionByActionKey", err)
		if err != gorm.ErrRecordNotFound {
			return nil, internalServerError(errors.New("CANNOT_CHECK_ACTION"))
		}
		// CONTINUE..
	} else {
		if oldWork.Id > 0 && actionAt.After(oldWork.UnlockAt) {
			// UPDATE
			canceledKey := fmt.Sprintf("%s_DONE_%d", racingKey, oldWork.Id)
			canceledStatus := "TIMEOUT"
			var updateBody modelV1.RaceActionUpdateBody
			updateBody.ActionKey = &canceledKey
			updateBody.Status = &canceledStatus
			if err := repo.UpdateRaceCondition(oldWork.Id, updateBody); err != nil {
				log.Println("RacingRunUserQuestPlayLogs.ERROR.UpdateRaceCondition", err)
				return nil, internalServerError(errors.New("WORK_IN_ACTION"))
			}
			// CONTINUE..
		} else {
			return nil, internalServerError(errors.New("WORK_IN_ACTION"))
		}
	}

	// CONTINUE.. only not found will be created
	actionId, err := repo.CreateRaceCondition(createBody)
	if err != nil {
		log.Println("RacingRunUserQuestPlayLogs.ERROR.CreateRaceCondition", err)
		return nil, internalServerError(errors.New("CANNOT_CREATE_ACTION"))
	}
	return &actionId, nil
}

func (s *userQuestPlayLogV2Service) GetUserQuestPlayLogs() error {
	actionAt := time.Now()
	statementAt := actionAt.AddDate(0, 0, 0)
	statementDate := fmt.Sprintf("%v-%02d-%02d", statementAt.Year(), int(statementAt.Month()), statementAt.Day())
	//statementDate := "2025-07-02"

	// todo AGC + AMB
	// -- GAME sport(1) casino(2) game(4) Then Add more column by lottery(3), p2p(6), Financial(7)
	// -- ** (5) ไม่มีห้ามดึง = พัง
	productIds := []int{
		modelV1.AGENT_PRODUCT_SPORT,
		modelV1.AGENT_PRODUCT_CASINO,
		modelV1.AGENT_PRODUCT_GAME,
		modelV1.AGENT_PRODUCT_LOTTERY,
		modelV1.AGENT_PRODUCT_P2P,
		modelV1.AGENT_PRODUCT_FINANCIAL,
	}
	hasExternalLotteryApi := os.Getenv("LOTTERY_ENDPOINT") != "" && os.Getenv("AGENT_NAME") != ""

	// RACE_CONDITION
	racingKey := "USER_QUEST_PLAYLOG_V2"
	actionId, err := RacingRunUserQuestPlagLogs(s.agentInfoRepository, os.Getenv("AGENT_NAME"), "CronUserQuestPlayLogsV2", -2)
	if err != nil {
		return err
	}

	go func() {
		var wg sync.WaitGroup
		wg.Add(3)

		go func() {
			defer wg.Done()
			for _, productId := range productIds {
				if productId == modelV1.AGENT_PRODUCT_LOTTERY && hasExternalLotteryApi {
					log.Println("SKIP EXTERNAL LOTTERY, Use CALLBACK")
				} else {
					log.Println("runAgcTodayWinLoseV2", productId, statementDate)
					s.runAgcTodayWinLoseV2(productId, statementDate, false)
				}
			}
		}()

		// RUN ได้เลย ตลอด ไม่ต้องรอ
		go func() {
			defer wg.Done()
			log.Println("runPgHardToAgcTodayWinLoseV2", modelV1.AGENT_PRODUCT_GAME, statementDate)
			s.runPgHardToAgcTodayWinLoseV2(modelV1.AGENT_PRODUCT_GAME, statementDate, false)
		}()

		go func() {
			defer wg.Done()
			log.Println("runAgentCtwToAgcTodayWinLose", modelV1.AGENT_PRODUCT_GAME, statementDate)
			s.runAgentCtwToAgcTodayWinLose(modelV1.AGENT_PRODUCT_GAME, statementDate, false)
		}()

		wg.Wait()

		// RACE_CONDITION UPDATE
		successKey := fmt.Sprintf("%s_DONE_%d", racingKey, *actionId)
		successStatus := "SUCCESS"
		var updateBody modelV1.RaceActionUpdateBody
		updateBody.ActionKey = &successKey
		updateBody.Status = &successStatus
		if err := s.agentInfoRepository.UpdateRaceCondition(*actionId, updateBody); err != nil {
			log.Println("CronUserQuestPlayLogs.ERROR.UpdateRaceCondition", err)
			log.Println("Finish CronUserQuestPlayLogs But ERROR UpdateRaceCondition")
		}
		log.Println("Finish CronUserQuestPlayLogs statementDate=", statementDate)
	}()
	return nil
}

func (s *userQuestPlayLogV2Service) GetUserQuestPlayLogsByDate(statementDate string) error {

	// -- GAME sport(1) casino(2) game(4) Then Add more column by lottery(3), p2p(6), Financial(7)
	// -- ** (5) ไม่มีห้ามดึง = พัง
	productIds := []int{
		modelV1.AGENT_PRODUCT_SPORT,
		modelV1.AGENT_PRODUCT_CASINO,
		modelV1.AGENT_PRODUCT_GAME,
		modelV1.AGENT_PRODUCT_LOTTERY,
		modelV1.AGENT_PRODUCT_P2P,
		modelV1.AGENT_PRODUCT_FINANCIAL,
	}
	hasExternalLotteryApi := os.Getenv("LOTTERY_ENDPOINT") != "" && os.Getenv("AGENT_NAME") != ""

	// RACE_CONDITION
	racingKey := "USER_QUEST_PLAYLOG_V2"
	actionId, err := RacingRunUserQuestPlagLogs(s.agentInfoRepository, os.Getenv("AGENT_NAME"), "CronUserQuestPlayLogsV2ByDate", -1)
	if err != nil {
		return err
	}

	go func() {
		var wg sync.WaitGroup
		wg.Add(3)

		go func() {
			defer wg.Done()
			for _, productId := range productIds {
				if productId == modelV1.AGENT_PRODUCT_LOTTERY && hasExternalLotteryApi {
					log.Println("SKIP EXTERNAL LOTTERY, Use CALLBACK")
				} else {
					s.runAgcTodayWinLoseV2(productId, statementDate, false)
				}
			}
		}()

		// RUN ได้เลย ตลอด ไม่ต้องรอ
		go func() {
			defer wg.Done()
			s.runPgHardToAgcTodayWinLoseV2(modelV1.AGENT_PRODUCT_GAME, statementDate, false)
		}()

		go func() {
			defer wg.Done()
			s.runAgentCtwToAgcTodayWinLose(modelV1.AGENT_PRODUCT_GAME, statementDate, false)
		}()

		wg.Wait()

		// RACE_CONDITION UPDATE
		successKey := fmt.Sprintf("%s_DONE_%d", racingKey, *actionId)
		successStatus := "SUCCESS"
		var updateBody modelV1.RaceActionUpdateBody
		updateBody.ActionKey = &successKey
		updateBody.Status = &successStatus
		if err := s.agentInfoRepository.UpdateRaceCondition(*actionId, updateBody); err != nil {
			log.Println("CronUserQuestPlayLogsByDate.ERROR.UpdateRaceCondition", err)
			log.Println("Finish CronUserQuestPlayLogsByDate But ERROR UpdateRaceCondition")
		}
		log.Println("Finish CronUserQuestPlayLogsByDate statementDate=", statementDate)
	}()

	return nil
}

func (s *userQuestPlayLogV2Service) runAgcTodayWinLoseV2(productId int, statementDate string, force bool) error {
	actionAt := time.Now().UTC()
	path := fmt.Sprintf("questTodaywinlose%v force=%v", productId, force)
	// completedPath := fmt.Sprintf("completed_%v", productId)
	var cronError error
	log.Printf("Running runQuestTodayWinLose %v date %v", path, statementDate)

	// DONOT RUN DURING 12.20 - 12.40
	// [2024-09-09] 12.40 => Extend to 12.01 - 12.59 when large rows(>7000) was 8min on LOCAL-DEV
	bbkLoc := time.FixedZone("Asia/Bangkok", 7*60*60)
	runTime := actionAt.In(bbkLoc)
	if runTime.Hour() == 12 && runTime.Minute() >= 01 && runTime.Minute() <= 59 {
		log.Printf("Skip runTodayWinLose %v date %v", path, statementDate)
		return nil
	}

	agentName := os.Getenv("AGENT_NAME")
	body := modelV2.AgcSimpleWinLoseV2{}
	body.StartDate = statementDate
	body.EndDate = statementDate
	body.AgentName = agentName
	body.MemberName = agentName
	body.Products = []int{productId}
	body.PageSize = 250
	body.TimeStamp = int(actionAt.Unix())
	body.Sign = helper.CreateSign(os.Getenv("AGENT_KEY"), agentName, actionAt)

	var createList = make(map[string]modelV2.UserQuestTodayPlayLogCreateBody, 0)
	var uniqueList []string
	var memberCodeList []string
	var totalCreated int64
	for i := 0; ; i++ {

		body.PageIndex = i + 1

		time.Sleep(5 * time.Second)
		list, err := s.agentConnectV2Repo.AgcSimpleWinLoseV2(body)
		if err != nil {
			cronError = err
			log.Println("GetExternalPlaylogByDate.ERROR", err)
			break
		}

		if list.Error != nil && list.Error.Code != 0 {
			cronError = errors.New(list.Error.Message)
			if list.Error.Code == -15 {
				// request frequency limit is 3 seconds (-15)
				log.Println("List.ERROR", list.Error, ", RETRY PAGE=", body.PageIndex)
				i--
				time.Sleep(3 * time.Second)
				continue
			} else {
				log.Println("List.ERROR", list.Error)
				break
			}
		}

		fmt.Println("runQuestAgcTodayWinLose.list.Result.Records.LEN", len(list.Result.Records))
		if len(list.Result.Records) <= 0 {
			log.Println("ZERO==GOTALL")
			break
		}

		for _, j := range list.Result.Records {

			log.Println("GetExternalPlaylogByDate.INFO", j)

			var tempRow modelV2.UserQuestTodayPlayLogCreateBody
			// {memberCode}_{statementDate}_{productId}
			uniqueKey := fmt.Sprintf("%v_%v_%v", j.UserName, strings.Replace(statementDate, "-", "", -1), productId)
			tempRow.StatementDate = body.StartDate
			tempRow.DailyKey = uniqueKey

			memberCodeList = append(memberCodeList, j.UserName)
			tempRow.MemberCode = j.UserName

			if productId == modelV1.AGENT_PRODUCT_SPORT {
				tempRow.TurnSport = j.TurnOver
				tempRow.WinLoseSport = j.Payout
				tempRow.ValidAmountSport = j.ValidAmount
			} else if productId == modelV1.AGENT_PRODUCT_CASINO {
				tempRow.TurnCasino = j.TurnOver
				tempRow.WinLoseCasino = j.Payout
				tempRow.ValidAmountCasino = j.ValidAmount
			} else if productId == modelV1.AGENT_PRODUCT_GAME {
				tempRow.TurnGame = j.TurnOver
				tempRow.WinLoseGame = j.Payout
				tempRow.ValidAmountGame = j.ValidAmount
			} else if productId == modelV1.AGENT_PRODUCT_LOTTERY {
				tempRow.TurnLottery = j.TurnOver
				tempRow.WinLoseLottery = j.Payout
				tempRow.ValidAmountLottery = j.ValidAmount
			} else if productId == modelV1.AGENT_PRODUCT_P2P {
				tempRow.TurnP2p = j.TurnOver
				tempRow.WinLoseP2p = j.Payout
				tempRow.ValidAmountP2p = j.ValidAmount
			} else if productId == modelV1.AGENT_PRODUCT_FINANCIAL {
				tempRow.TurnFinancial = j.TurnOver
				tempRow.WinLoseFinancial = j.Payout
				tempRow.ValidAmountFinancial = j.ValidAmount
			}

			tempRow.TurnTotal = tempRow.TurnSport + tempRow.TurnCasino + tempRow.TurnGame + tempRow.TurnLottery + tempRow.TurnP2p + tempRow.TurnFinancial
			tempRow.WinLoseTotal = tempRow.WinLoseSport + tempRow.WinLoseCasino + tempRow.WinLoseGame + tempRow.WinLoseLottery + tempRow.WinLoseP2p + tempRow.WinLoseFinancial
			tempRow.ValidAmountTotal = tempRow.ValidAmountSport + tempRow.ValidAmountCasino + tempRow.ValidAmountGame + tempRow.ValidAmountLottery + tempRow.ValidAmountP2p + tempRow.ValidAmountFinancial
			createList[uniqueKey] = tempRow

			// Bulk insert
			if len(createList) >= 100 {
				// check exists
				for k := range createList {
					uniqueList = append(uniqueList, k)
				}
				if dbList, _, err := s.userQuestPlayLogRepo.GetQuestTodayPlayLogKeyListV2(uniqueList); err != nil {
					cronError = err // cant check
				} else {
					// if exists, remove from createList
					for index, dbReord := range dbList {
						dbKey := dbReord.DailyKey
						err := s.UpdateUserQuestTodayPlayLogV2(createList[dbKey], dbList[index])
						if err != nil {
							log.Println(err)
							cronError = err
						}
						delete(createList, dbKey)
					}
					if len(createList) > 0 {
						err := s.userQuestPlayLogRepo.CreateUserTodayPlayLogBulkV2(createList, memberCodeList)
						if err != nil {
							log.Println(err)
							cronError = err
							continue
						}
						totalCreated += int64(len(createList))
					}
				}
				createList = make(map[string]modelV2.UserQuestTodayPlayLogCreateBody, 0)
				uniqueList = make([]string, 0)
				memberCodeList = make([]string, 0)
			}
		}
	}

	// LEFTOVER
	if len(createList) > 0 {
		// check exists
		for k := range createList {
			uniqueList = append(uniqueList, k)
		}
		if dbList, _, err := s.userQuestPlayLogRepo.GetQuestTodayPlayLogKeyListV2(uniqueList); err != nil {
			cronError = err // cant check
		} else {
			// if exists, remove from createList
			for index, dbReord := range dbList {
				dbKey := dbReord.DailyKey
				err := s.UpdateUserQuestTodayPlayLogV2(createList[dbKey], dbList[index])
				if err != nil {
					log.Println(err)
					cronError = err
				}
				delete(createList, dbKey)
			}

			if len(createList) > 0 {
				err := s.userQuestPlayLogRepo.CreateUserTodayPlayLogBulkV2(createList, memberCodeList)
				if err != nil {
					log.Println(err)
					cronError = err
				}
				totalCreated += int64(len(createList))
			}
		}
		totalCreated += int64(len(createList))
	}

	if cronError != nil {
		log.Println("runQuestTodayWinLose : ", path, "ERROR", cronError)
	}
	log.Println("Finish runQuestTodayWinLose : ", path)
	return cronError
}

func (s *userQuestPlayLogV2Service) runPgHardToAgcTodayWinLoseV2(productId int, statementDate string, force bool) error {

	actionAt := time.Now().UTC()
	path := fmt.Sprintf("runQuestPgHardToAgcTodayWinLose%v force=%v", productId, force)

	var cronError error
	log.Printf("Running runQuestPgHardToAgcTodayWinLose %v date %v", path, statementDate)

	// DONOT RUN DURING 12.20 - 12.40
	// [2024-09-09] 12.40 => Extend to 12.01 - 12.59 when large rows(>7000) was 8min on LOCAL-DEV
	bbkLoc := time.FixedZone("Asia/Bangkok", 7*60*60)
	runTime := actionAt.In(bbkLoc)
	if runTime.Hour() == 12 && runTime.Minute() >= 01 && runTime.Minute() <= 59 {
		log.Printf("Skip runPgHardToAgcTodayWinLose %v date %v", path, statementDate)
		return nil
	}

	var body modelV2.AgentPgHardCallbackSummaryV2Request
	body.StatementDate = statementDate
	body.PageSize = 250

	// isHasError := false
	var createList = make(map[string]modelV2.UserQuestTodayPlayLogCreateBody, 0)
	var uniqueList []string
	var memberCodeList []string
	var totalCreated int64
	for i := 0; ; i++ {

		body.PageIndex = i + 1

		// log.Println("GetExternalPlaylogByDate.WAIT", body)
		time.Sleep(5 * time.Second)
		list, err := s.agentConnectV2Repo.GetAgentPgHardCallback(body)
		if err != nil {
			cronError = err
			log.Println("GetExternalPlaylogByDate.ERROR", err)
			break
		}

		fmt.Println("runQuestPgHardToAgcTodayWinLose.list.Result.Records.LEN", len(list))
		if len(list) <= 0 {
			log.Println("ZERO==GOTALL")
			break
		}

		for _, j := range list {

			var tempRow modelV2.UserQuestTodayPlayLogCreateBody
			// {memberCode}_{statementDate}_{productId}
			uniqueKey := fmt.Sprintf("%v_%v_PGH%v", j.MemberCode, strings.Replace(statementDate, "-", "", -1), productId)

			tempRow.StatementDate = statementDate
			tempRow.DailyKey = uniqueKey

			memberCodeList = append(memberCodeList, j.MemberCode)
			tempRow.MemberCode = j.MemberCode

			tempRow.TurnGame = j.TotalBet
			tempRow.WinLoseGame = j.TotalWinlose
			tempRow.ValidAmountGame = j.TotalBet

			tempRow.TurnTotal = tempRow.TurnSport + tempRow.TurnCasino + tempRow.TurnGame + tempRow.TurnLottery + tempRow.TurnP2p + tempRow.TurnFinancial
			tempRow.WinLoseTotal = tempRow.WinLoseSport + tempRow.WinLoseCasino + tempRow.WinLoseGame + tempRow.WinLoseLottery + tempRow.WinLoseP2p + tempRow.WinLoseFinancial
			tempRow.ValidAmountTotal = tempRow.ValidAmountSport + tempRow.ValidAmountCasino + tempRow.ValidAmountGame + tempRow.ValidAmountLottery + tempRow.ValidAmountP2p + tempRow.ValidAmountFinancial
			createList[uniqueKey] = tempRow

			// Bulk insert
			if len(createList) >= 100 {
				// check exists
				for k := range createList {
					uniqueList = append(uniqueList, k)
				}
				if dbList, _, err := s.userQuestPlayLogRepo.GetQuestTodayPlayLogKeyListV2(uniqueList); err != nil {
					cronError = err // cant check
				} else {
					// if exists, remove from createList
					for index, dbReord := range dbList {
						dbKey := dbReord.DailyKey
						err := s.UpdateUserQuestTodayPlayLogV2(createList[dbKey], dbList[index])
						if err != nil {
							log.Println(err)
							cronError = err
						}
						delete(createList, dbKey)
					}
					if len(createList) > 0 {
						err := s.userQuestPlayLogRepo.CreateUserTodayPlayLogBulkV2(createList, memberCodeList)
						if err != nil {
							log.Println(err)
							cronError = err
							continue
						}
						totalCreated += int64(len(createList))
					}
				}
				createList = make(map[string]modelV2.UserQuestTodayPlayLogCreateBody, 0)
				uniqueList = make([]string, 0)
				memberCodeList = make([]string, 0)
			}
		}
	}

	// LEFTOVER
	if len(createList) > 0 {
		// check exists
		for k := range createList {
			uniqueList = append(uniqueList, k)
		}
		if dbList, _, err := s.userQuestPlayLogRepo.GetQuestTodayPlayLogKeyListV2(uniqueList); err != nil {
			cronError = err // cant check
		} else {
			// if exists, remove from createList
			for index, dbReord := range dbList {
				dbKey := dbReord.DailyKey
				err := s.UpdateUserQuestTodayPlayLogV2(createList[dbKey], dbList[index])
				if err != nil {
					log.Println(err)
					cronError = err
				}
				delete(createList, dbKey)
			}
			if len(createList) > 0 {
				err := s.userQuestPlayLogRepo.CreateUserTodayPlayLogBulkV2(createList, memberCodeList)
				if err != nil {
					log.Println(err)
					cronError = err
				}
				totalCreated += int64(len(createList))
			}
		}
		totalCreated += int64(len(createList))
	}

	if cronError != nil {
		log.Println("runQuestPgHardToAgcTodayWinLose : ", path, "ERROR", cronError)
	}
	log.Println("Finish runQuestPgHardToAgcTodayWinLose : ", path)
	return cronError
}

func (s *userQuestPlayLogV2Service) runAgentCtwToAgcTodayWinLose(productId int, statementDate string, force bool) error {

	actionAt := time.Now().UTC()
	path := fmt.Sprintf("runQuestAgentCtwToAgcTodayWinLose%v force=%v", productId, force)
	// completedPath := fmt.Sprintf("completed_%v", productId)
	var cronError error
	log.Printf("Running runQuestAgentCtwToAgcTodayWinLose %v date %v", path, statementDate)

	// DONOT RUN DURING 12.20 - 12.40
	// [2024-09-09] 12.40 => Extend to 12.01 - 12.59 when large rows(>7000) was 8min on LOCAL-DEV
	bbkLoc := time.FixedZone("Asia/Bangkok", 7*60*60)
	runTime := actionAt.In(bbkLoc)
	if runTime.Hour() == 12 && runTime.Minute() >= 01 && runTime.Minute() <= 59 {
		log.Printf("Skip runAgentCtwToAgcTodayWinLose %v date %v", path, statementDate)
		return nil
	}

	var body modelV2.AgentCtwCallbackSummaryV2Request
	body.StatementDate = statementDate
	body.PageSize = 250

	// isHasError := false
	var createList = make(map[string]modelV2.UserQuestTodayPlayLogCreateBody, 0)
	var uniqueList []string
	var memberCodeList []string
	var totalCreated int64
	for i := 0; ; i++ {

		body.PageIndex = i + 1

		// log.Println("GetExternalPlaylogByDate.WAIT", body)
		time.Sleep(5 * time.Second)
		list, err := s.agentConnectV2Repo.GetAgentCtwCallbackV2(body)
		if err != nil {
			cronError = err
			log.Println("GetExternalPlaylogByDate.ERROR", err)
			break
		}

		fmt.Println("runQuestAgentCtwToAgcTodayWinLose.list.Result.Records.LEN", len(list))
		if len(list) <= 0 {
			log.Println("ZERO==GOTALL")
			break
		}

		for _, j := range list {

			var tempRow modelV2.UserQuestTodayPlayLogCreateBody
			// {memberCode}_{statementDate}_{productId}
			uniqueKey := fmt.Sprintf("%v_%v_CTW%v", j.MemberCode, strings.Replace(statementDate, "-", "", -1), productId)

			tempRow.StatementDate = statementDate
			tempRow.DailyKey = uniqueKey

			memberCodeList = append(memberCodeList, j.MemberCode)
			tempRow.MemberCode = j.MemberCode

			tempRow.TurnGame = j.TotalBet
			tempRow.WinLoseGame = j.TotalWinlose
			tempRow.ValidAmountGame = j.TotalBet

			tempRow.TurnTotal = tempRow.TurnSport + tempRow.TurnCasino + tempRow.TurnGame + tempRow.TurnLottery + tempRow.TurnP2p + tempRow.TurnFinancial
			tempRow.WinLoseTotal = tempRow.WinLoseSport + tempRow.WinLoseCasino + tempRow.WinLoseGame + tempRow.WinLoseLottery + tempRow.WinLoseP2p + tempRow.WinLoseFinancial
			tempRow.ValidAmountTotal = tempRow.ValidAmountSport + tempRow.ValidAmountCasino + tempRow.ValidAmountGame + tempRow.ValidAmountLottery + tempRow.ValidAmountP2p + tempRow.ValidAmountFinancial
			createList[uniqueKey] = tempRow

			// Bulk insert
			if len(createList) >= 100 {
				// check exists
				for k := range createList {
					uniqueList = append(uniqueList, k)
				}
				if dbList, _, err := s.userQuestPlayLogRepo.GetQuestTodayPlayLogKeyListV2(uniqueList); err != nil {
					cronError = err // cant check
				} else {
					// if exists, remove from createList
					for index, dbReord := range dbList {
						dbKey := dbReord.DailyKey
						err := s.UpdateUserQuestTodayPlayLogV2(createList[dbKey], dbList[index])
						if err != nil {
							log.Println(err)
							cronError = err
						}
						delete(createList, dbKey)
					}
					if len(createList) > 0 {
						err := s.userQuestPlayLogRepo.CreateUserTodayPlayLogBulkV2(createList, memberCodeList)
						if err != nil {
							log.Println(err)
							cronError = err
							continue
						}
						totalCreated += int64(len(createList))
					}
				}
				createList = make(map[string]modelV2.UserQuestTodayPlayLogCreateBody, 0)
				uniqueList = make([]string, 0)
				memberCodeList = make([]string, 0)
			}
		}
	}

	// LEFTOVER
	if len(createList) > 0 {
		// check exists
		for k := range createList {
			uniqueList = append(uniqueList, k)
		}
		if dbList, _, err := s.userQuestPlayLogRepo.GetQuestTodayPlayLogKeyListV2(uniqueList); err != nil {
			cronError = err // cant check
		} else {
			// if exists, remove from createList
			for index, dbReord := range dbList {
				dbKey := dbReord.DailyKey
				err := s.UpdateUserQuestTodayPlayLogV2(createList[dbKey], dbList[index])
				if err != nil {
					log.Println(err)
					cronError = err
				}
				delete(createList, dbKey)
			}
			if len(createList) > 0 {
				err := s.userQuestPlayLogRepo.CreateUserTodayPlayLogBulkV2(createList, memberCodeList)
				if err != nil {
					log.Println(err)
					cronError = err
				}
				totalCreated += int64(len(createList))
			}
		}
		totalCreated += int64(len(createList))
	}

	if cronError != nil {
		log.Println("runQuestCTWToAgcTodayWinLose : ", path, "ERROR", cronError)
	}
	log.Println("Finish runQuestCTWToAgcTodayWinLose : ", path)
	return cronError
}

func (s *userQuestPlayLogV2Service) UpdateUserQuestTodayPlayLogV2(createBody modelV2.UserQuestTodayPlayLogCreateBody, dbRecord modelV2.UserQuestTodayPlayLogV2Response) error {
	createRow := modelV2.UserQuestTodayPlayLogV2Response{
		Id:                   dbRecord.Id,
		TurnSport:            createBody.TurnSport,
		ValidAmountSport:     createBody.ValidAmountSport,
		WinLoseSport:         createBody.WinLoseSport,
		TurnCasino:           createBody.TurnCasino,
		WinLoseCasino:        createBody.WinLoseCasino,
		ValidAmountCasino:    createBody.ValidAmountCasino,
		TurnGame:             createBody.TurnGame,
		WinLoseGame:          createBody.WinLoseGame,
		ValidAmountGame:      createBody.ValidAmountGame,
		TurnLottery:          createBody.TurnLottery,
		WinLoseLottery:       createBody.WinLoseLottery,
		ValidAmountLottery:   createBody.ValidAmountLottery,
		TurnP2p:              createBody.TurnP2p,
		WinLoseP2p:           createBody.WinLoseP2p,
		ValidAmountP2p:       createBody.ValidAmountP2p,
		TurnFinancial:        createBody.TurnFinancial,
		WinLoseFinancial:     createBody.WinLoseFinancial,
		ValidAmountFinancial: createBody.ValidAmountFinancial,
		TurnTotal:            createBody.TurnTotal,
		WinLoseTotal:         createBody.WinLoseTotal,
		ValidAmountTotal:     createBody.ValidAmountTotal,
	}

	dbRow := modelV2.UserQuestTodayPlayLogV2Response{
		Id:                   dbRecord.Id,
		TurnSport:            dbRecord.TurnSport,
		ValidAmountSport:     dbRecord.ValidAmountSport,
		WinLoseSport:         dbRecord.WinLoseSport,
		TurnCasino:           dbRecord.TurnCasino,
		WinLoseCasino:        dbRecord.WinLoseCasino,
		ValidAmountCasino:    dbRecord.ValidAmountCasino,
		TurnGame:             dbRecord.TurnGame,
		WinLoseGame:          dbRecord.WinLoseGame,
		ValidAmountGame:      dbRecord.ValidAmountGame,
		TurnLottery:          dbRecord.TurnLottery,
		WinLoseLottery:       dbRecord.WinLoseLottery,
		ValidAmountLottery:   dbRecord.ValidAmountLottery,
		TurnP2p:              dbRecord.TurnP2p,
		WinLoseP2p:           dbRecord.WinLoseP2p,
		ValidAmountP2p:       dbRecord.ValidAmountP2p,
		TurnFinancial:        dbRecord.TurnFinancial,
		WinLoseFinancial:     dbRecord.WinLoseFinancial,
		ValidAmountFinancial: dbRecord.ValidAmountFinancial,
		TurnTotal:            dbRecord.TurnTotal,
		WinLoseTotal:         dbRecord.WinLoseTotal,
		ValidAmountTotal:     dbRecord.ValidAmountTotal,
	}

	localJson := helper.StructJson(createRow)
	remoteJson := helper.StructJson(dbRow)

	if localJson != remoteJson {
		// LOG_ON_DIFF
		updateBody := modelV2.UserQuestTodayPlayLogUpdateBody{
			Id:                   dbRecord.Id,
			TurnSport:            &createBody.TurnSport,
			ValidAmountSport:     &createBody.ValidAmountSport,
			WinLoseSport:         &createBody.WinLoseSport,
			TurnCasino:           &createBody.TurnCasino,
			WinLoseCasino:        &createBody.WinLoseCasino,
			ValidAmountCasino:    &createBody.ValidAmountCasino,
			TurnGame:             &createBody.TurnGame,
			WinLoseGame:          &createBody.WinLoseGame,
			ValidAmountGame:      &createBody.ValidAmountGame,
			TurnLottery:          &createBody.TurnLottery,
			WinLoseLottery:       &createBody.WinLoseLottery,
			ValidAmountLottery:   &createBody.ValidAmountLottery,
			TurnP2p:              &createBody.TurnP2p,
			WinLoseP2p:           &createBody.WinLoseP2p,
			ValidAmountP2p:       &createBody.ValidAmountP2p,
			TurnFinancial:        &createBody.TurnFinancial,
			WinLoseFinancial:     &createBody.WinLoseFinancial,
			ValidAmountFinancial: &createBody.ValidAmountFinancial,
			TurnTotal:            &createBody.TurnTotal,
			WinLoseTotal:         &createBody.WinLoseTotal,
			ValidAmountTotal:     &createBody.ValidAmountTotal,
		}
		if err := s.userQuestPlayLogRepo.UpdateUserQuestTodayPlayLogV2(updateBody); err != nil {
			log.Println(err)
		}
	}

	return nil
}
