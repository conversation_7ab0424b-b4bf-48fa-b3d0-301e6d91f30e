package v2

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	modelV2 "cybergame-api/model/v2"
	repositoryV2 "cybergame-api/repository/v2"
	"fmt"
	"log"
	"net/http"
)

type RankingV2Service interface {
	// Admin
	CreateRankingV2Setting(req modelV2.CreateRankingV2SettingRequest) error
	GetRankingV2Setting() ([]modelV2.RankingV2SettingResponse, error)
	GetRankingReportByRankingId(rankingId int64, req modelV2.RankingListRequest) (*model.SuccessWithPagination, error)
	UpdateRankingV2Setting(req modelV2.UpdateRankingV2SettingRequest) error
	DeleteRankingV2Setting(id int64) error

	// Web
	GetTotalRankingV2() ([]modelV2.TotalRankingV2Response, error)
	GetUserRankingV2(userId int64) (*modelV2.UserRankingV2Response, error)
	GetTurnRankingV2() (*modelV2.TurnRankingV2Response, error)
	UploadImageToS3Ranking(imageFileBody *http.Request) (*modelV2.ImageUploadResponse, error)
}

type rankingV2Service struct {
	rankingV2Repo repositoryV2.RankingV2Repository
	s3V2Repo      repositoryV2.S3V2Repository
}

func NewRankingV2Service(
	rankingV2Repo repositoryV2.RankingV2Repository,
	s3V2Repo repositoryV2.S3V2Repository,
) RankingV2Service {
	return &rankingV2Service{rankingV2Repo, s3V2Repo}
}

func (s *rankingV2Service) CreateRankingV2Setting(req modelV2.CreateRankingV2SettingRequest) error {
	isDuplicate, err := s.rankingV2Repo.CheckRankingNameDuplicate(req.Name)
	if err != nil {
		return err
	}
	if isDuplicate {
		return badRequest("Ranking name already exists")
	}

	currentSortNumber, err := s.rankingV2Repo.GetCurrentSortNumber()
	if err != nil {
		return err
	}

	req.Sort = currentSortNumber + 1
	if err := s.rankingV2Repo.CreateRankingV2Setting(req); err != nil {
		return err
	}

	return nil
}

func (s *rankingV2Service) GetRankingV2Setting() ([]modelV2.RankingV2SettingResponse, error) {
	list, err := s.rankingV2Repo.GetRankingV2Setting()
	if err != nil {
		return nil, err
	}

	return list, nil
}

func (s *rankingV2Service) GetRankingReportByRankingId(rankingId int64, req modelV2.RankingListRequest) (*model.SuccessWithPagination, error) {
	ranking, err := s.rankingV2Repo.GetRankingV2SettingById(rankingId)
	if err != nil {
		return nil, err
	}
	if ranking == nil {
		return nil, notFound(fmt.Sprintf("Ranking with id %d not found", rankingId))
	}

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	list, total, err := s.rankingV2Repo.GetUserByRankingId(int64(ranking.MinScore), int64(ranking.MaxScore), req)
	if err != nil {
		return nil, err
	}

	return &model.SuccessWithPagination{Message: "Success", List: list, Total: total}, nil
}

func (s *rankingV2Service) UpdateRankingV2Setting(req modelV2.UpdateRankingV2SettingRequest) error {
	isDuplicate, err := s.rankingV2Repo.CheckRankingNameDuplicateAndId(req.Name, req.Id)
	if err != nil {
		return err
	}
	if isDuplicate {
		return badRequest("Ranking name already exists")
	}

	allRankings, err := s.GetRankingV2Setting()
	if err != nil {
		return err
	}

	isSortUsed := false
	var existingSorts []int64
	var duplicateSort modelV2.RankingV2SettingResponse

	for _, ranking := range allRankings {
		if ranking.Id != req.Id && ranking.Sort == req.Sort {
			isSortUsed = true
			duplicateSort = ranking
		}
		if ranking.Id != req.Id {
			existingSorts = append(existingSorts, ranking.Sort)
		}
	}

	if isSortUsed && len(existingSorts) != 0 {

		minSort := existingSorts[0]
		maxSort := existingSorts[0]

		for _, sort := range existingSorts {
			if sort < minSort {
				minSort = sort
			}
			if sort > maxSort {
				maxSort = sort
			}
		}

		sortMap := make(map[int64]bool)
		for _, sort := range existingSorts {
			sortMap[sort] = true
		}

		availableSort := maxSort + 1 // default ใช้ max+1

		for i := minSort; i <= maxSort; i++ {
			if !sortMap[i] {
				availableSort = i
				break
			}
		}

		updateSort := modelV2.UpdateRankingV2SettingRequest{
			Id:        duplicateSort.Id,
			Name:      duplicateSort.Name,
			ImageUrl:  duplicateSort.ImageUrl,
			MinScore:  duplicateSort.MinScore,
			MaxScore:  duplicateSort.MaxScore,
			Sort:      availableSort,
			IsActive:  duplicateSort.IsActive,
			IsDeleted: duplicateSort.IsDeleted,
		}

		err = s.rankingV2Repo.UpdateRankingV2Setting(updateSort)
		if err != nil {
			return err
		}
	}

	if err := s.rankingV2Repo.UpdateRankingV2Setting(req); err != nil {
		return err
	}

	return nil
}

func (s *rankingV2Service) DeleteRankingV2Setting(id int64) error {
	isExist, err := s.rankingV2Repo.CheckRankingV2SettingExist(id)
	if err != nil {
		return err
	}
	if !isExist {
		return notFound(fmt.Sprintf("Ranking with id %d not found", id))
	}

	if err := s.rankingV2Repo.DeleteRankingV2Setting(id); err != nil {
		return err
	}

	return nil
}

func (s *rankingV2Service) GetTotalRankingV2() ([]modelV2.TotalRankingV2Response, error) {
	list, err := s.rankingV2Repo.GetTotalRankingV2()
	if err != nil {
		return nil, err
	}

	return list, nil
}

func (s *rankingV2Service) GetUserRankingV2(userId int64) (*modelV2.UserRankingV2Response, error) {
	userRanking, err := s.rankingV2Repo.GetUserRankingV2(userId)
	if err != nil {
		return nil, err
	}

	return userRanking, nil
}

func (s *rankingV2Service) GetTurnRankingV2() (*modelV2.TurnRankingV2Response, error) {
	list, err := s.rankingV2Repo.GetTurnRankingV2()
	if err != nil {
		return nil, err
	}

	return list, nil
}

func (s *rankingV2Service) UploadImageToS3Ranking(imageFileBody *http.Request) (*modelV2.ImageUploadResponse, error) {

	fileReader, _, err := imageFileBody.FormFile("file")
	if err != nil {
		log.Println("UploadFileS3.ERROR", err)
		return nil, err
	}

	//dbName := os.Getenv("DB_NAME")
	//pathName := fmt.Sprintf("%v/cbgame/ranking/", dbName)

	pathName := "tidtech/5monkey/common/images/"
	var newImageId *modelV2.ImageUploadResponse
	fileData, err := s.s3V2Repo.UploadImageToS3(pathName, fileReader)
	if err != nil {
		log.Println("UploadFileS3.ERROR", err)
		return nil, badRequest("UNABLE_TO_UPLOAD_FILE")
	}

	newImageId = &modelV2.ImageUploadResponse{
		ImageUrl: fileData.ImageUrl,
	}

	return newImageId, nil
}
