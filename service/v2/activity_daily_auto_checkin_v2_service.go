package v2

import (
	"cybergame-api/helper"
	modelV1 "cybergame-api/model"
	modelV2 "cybergame-api/model/v2"
	repositoryV1 "cybergame-api/repository"
	repositoryV2 "cybergame-api/repository/v2"
	serviceV1 "cybergame-api/service"
	"errors"
	"fmt"
	"gorm.io/gorm"
	"log"
	"math"
	"time"
)

type ActivityDailyAutoCheckinV2Service interface {
	CreateUserDailyAutoCheckinV2(userId int64) error
	GetUserDailyAutoCheckinReadyClaimV2(userId int64) (*modelV2.UserDailyAutoCheckinReadyClaimResponse, error)
	CreateUserDailyAutoCheckinClaimRewardV2(userId int64, dailyId int64) error
	CreateTurnOverFromActivityDailyV2(userId int64, bonusAmount float64, refId int64, noNumber int64) error
	GetReportDailyAutoCheckinV2(req modelV2.GetReportDailyAutoCheckinV2Request) (*modelV2.SuccessWithPagination, error)
}

type activityDailyAutoCheckinV2Service struct {
	shareDb                              *gorm.DB
	activityDailyAutoCheckinV2Repository repositoryV2.ActivityDailyAutoCheckinV2Repository
	userRepository                       repositoryV1.UserRepository
	activityDailyV2Repository            repositoryV1.ActivityDailyV2Repository
	cronRepository                       repositoryV1.CronRepository
	userCreditRepository                 repositoryV1.UserCreditRepository
	turnoverRepository                   repositoryV1.TurnoverRepository
}

func NewActivityDailyAutoCheckinV2Service(
	shareDb *gorm.DB,
	activityDailyAutoCheckinV2Repository repositoryV2.ActivityDailyAutoCheckinV2Repository,
	userRepository repositoryV1.UserRepository,
	activityDailyV2Repository repositoryV1.ActivityDailyV2Repository,
	cronRepository repositoryV1.CronRepository,
	userCreditRepository repositoryV1.UserCreditRepository,
	turnoverRepository repositoryV1.TurnoverRepository,
) ActivityDailyAutoCheckinV2Service {
	return &activityDailyAutoCheckinV2Service{
		shareDb:                              shareDb,
		activityDailyAutoCheckinV2Repository: activityDailyAutoCheckinV2Repository,
		userRepository:                       userRepository,
		activityDailyV2Repository:            activityDailyV2Repository,
		cronRepository:                       cronRepository,
		userCreditRepository:                 userCreditRepository,
		turnoverRepository:                   turnoverRepository,
	}
}

func (s *activityDailyAutoCheckinV2Service) CreateUserDailyAutoCheckinV2(userId int64) error {
	// check on that day checkin or not
	checkUserAlreadyCheckin, err := s.activityDailyAutoCheckinV2Repository.CheckUserAlreadyCheckin(userId)
	if err != nil {
		return err
	}

	if checkUserAlreadyCheckin {
		return errors.New("USER_ALREADY_CHECKIN_TO_DAY")
	}

	// check what is the next noNumber
	getUserDailyAutoCheckinV2NextNoNumber, detail, err := s.activityDailyAutoCheckinV2Repository.GetUserDailyAutoCheckinV2NextNoNumber(userId)
	if err != nil {
		return err
	}

	// ทำแบบนี้เพราะสามารถกลับมากดวันที่แรกได้เมื่อครบทั้งหมด
	checkinAt := time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02")

	if getUserDailyAutoCheckinV2NextNoNumber.NoNumber == 1 {
		// Create User Daily Auto Check-in
		var createUserDailyAutoCheckin modelV2.CreateUserDailyAutoCheckinV2Request
		createUserDailyAutoCheckin.ActivityDailyV2Id = getUserDailyAutoCheckinV2NextNoNumber.Id
		createUserDailyAutoCheckin.CurrentNoNumber = getUserDailyAutoCheckinV2NextNoNumber.NoNumber
		createUserDailyAutoCheckin.ChangeCountTime = getUserDailyAutoCheckinV2NextNoNumber.ChangeCountTime
		createUserDailyAutoCheckin.UserId = userId
		createUserDailyAutoCheckin.AccumulatedCreditBalance = getUserDailyAutoCheckinV2NextNoNumber.CreditAmount
		createUserDailyAutoCheckin.CheckinAt = checkinAt
		err = s.activityDailyAutoCheckinV2Repository.CreateDailyAutoCheckinUserV2(createUserDailyAutoCheckin)
		if err != nil {
			return err
		}

	} else {
		// Update User Daily Auto Check-in
		var updateUserDailyAutoCheckin modelV2.UpdateUserDailyAutoCheckinV2Request
		updateUserDailyAutoCheckin.Id = getUserDailyAutoCheckinV2NextNoNumber.DailyId
		updateUserDailyAutoCheckin.ActivityDailyV2Id = getUserDailyAutoCheckinV2NextNoNumber.Id
		updateUserDailyAutoCheckin.CurrentNoNumber = getUserDailyAutoCheckinV2NextNoNumber.NoNumber
		updateUserDailyAutoCheckin.ChangeCountTime = getUserDailyAutoCheckinV2NextNoNumber.ChangeCountTime
		updateUserDailyAutoCheckin.UserId = userId
		updateUserDailyAutoCheckin.AccumulatedCreditBalance = getUserDailyAutoCheckinV2NextNoNumber.CreditAmount
		updateUserDailyAutoCheckin.CheckinAt = checkinAt

		if getUserDailyAutoCheckinV2NextNoNumber.NoNumber == detail.TotalAbleReviceNo {
			updateUserDailyAutoCheckin.IsCompleted = true
		}

		err = s.activityDailyAutoCheckinV2Repository.UpdateDailyAutoCheckinUserV2(updateUserDailyAutoCheckin)
		if err != nil {
			return err
		}
	}

	return nil
}

func (s *activityDailyAutoCheckinV2Service) GetUserDailyAutoCheckinReadyClaimV2(userId int64) (*modelV2.UserDailyAutoCheckinReadyClaimResponse, error) {
	res, err := s.activityDailyAutoCheckinV2Repository.GetUserDailyAutoCheckinReadyClaim(userId)
	if err != nil {
		return nil, err
	}

	return res, nil
}

func (s *activityDailyAutoCheckinV2Service) CreateUserDailyAutoCheckinClaimRewardV2(userId int64, dailyId int64) error {
	user, err := s.userRepository.GetUserById(userId)
	if err != nil {
		return err
	}
	if user.MemberCode == nil {
		return badRequest("USER_NOT_MEMBER_PLEASE_DEPOSIT")
	}

	// ทำแบบนี้เพราะสามารถกลับมากดวันที่แรกได้เมื่อครบทั้งหมด
	dateReceived := time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02")

	// Get User Daily Auto Check-in and activity daily detail
	userDailyAutoCheckin, detail, err := s.activityDailyAutoCheckinV2Repository.GetUserDailyAutoCheckByIdV2(userId, dailyId)
	if err != nil {
		return err
	}

	if userDailyAutoCheckin == nil {
		return errors.New(fmt.Sprintf("credit claim ID:%d not found", dailyId))
	}

	// Check daily check-in conditions
	var findHighestCreditAmount float64 // ฝากขั้นต่ำ
	var findSumCreditAmount float64     // ฝากรวม (ตามยอด)
	if detail.ActivityDailyV2TotalConditionId != modelV1.ACTIVITY_DAILY_V2_TOTAL_CONDITION_NO_CONDITION {
		getUserTodayDepositForDailyAutoCheckinV2, err := s.activityDailyV2Repository.GetUserTodayDepositForActivityDaily(userId)
		if err != nil {
			return err
		}

		for _, item := range getUserTodayDepositForDailyAutoCheckinV2 {
			findSumCreditAmount += item.CreditAmount
			if item.CreditAmount > findHighestCreditAmount {
				findHighestCreditAmount = item.CreditAmount
			}
		}
	}
	if detail.ActivityDailyV2TotalConditionId == modelV1.ACTIVITY_DAILY_V2_TOTAL_CONDITION_MIN_DEPOSIT {
		if findHighestCreditAmount < detail.ConditionAmount {
			return badRequest("INSUFFICIENT_DEPOSIT_AMOUNT")
		}
	} else if detail.ActivityDailyV2TotalConditionId == modelV1.ACTIVITY_DAILY_V2_TOTAL_CONDITION_OVERALL_DEPOSIT {
		if findSumCreditAmount < detail.ConditionAmount {
			return badRequest("INSUFFICIENT_DEPOSIT_AMOUNT")
		}
	}

	// race condition sigle request
	type JsonRequest struct {
		UserId int64 `json:"-"`
	}
	var jsonRequest JsonRequest
	jsonRequest.UserId = userId

	var createBody modelV1.RaceActionCreateBody
	createBody.Name = "ACTIVITY_DAILY_V2"
	createBody.JsonRequest = helper.StructJson(jsonRequest)
	createBody.Status = "SUCCESS"
	createBody.ActionKey = fmt.Sprintf("ACTIVITY_DAILY_V2_U%d_DATE_%s", userId, dateReceived)
	createBody.UnlockAt = time.Now().UTC().Add(time.Second * 1)

	if _, err := s.cronRepository.GetRaceActionIdByActionKey(createBody.ActionKey); err != nil {
		log.Println("Daily Checkin CreateBankStatementFromWebhookAndAuto.GetRaceActionIdByActionKey", err)
		if err != gorm.ErrRecordNotFound {
			return internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
		}
	} else {
		return internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
	}

	actionId, err := s.cronRepository.CreateRaceCondition(createBody)
	if err != nil {
		log.Println("Daily Checkin CreateBankStatementFromWebhookAndAuto.CreateRaceCondition", err)
		return internalServerError(errors.New("CANNOT_CREATE_ACTION"))
	}
	if actionId == 0 {
		return badRequest("PLEASE_WAIT_ONE_MINUTE_YOU_JUST_CLICKED_A_MOMENT_AGO_PLEASE_CHECK_THE_STATEMENT_AGAIN")
	}

	// Update User Daily Auto Check-in to claimed
	totalReceived := userDailyAutoCheckin.AccumulatedCreditBalance - userDailyAutoCheckin.AccumulatedCreditReceived
	if totalReceived == 0 {
		return badRequest("ไม่มีเครดิตให้รับ")
	}

	var updateUserDailyAutoCheckin modelV2.UpdateUserDailyAutoCheckinV2Request
	updateUserDailyAutoCheckin.Id = userDailyAutoCheckin.Id
	updateUserDailyAutoCheckin.AccumulatedCreditReceived = userDailyAutoCheckin.AccumulatedCreditReceived + totalReceived
	updateUserDailyAutoCheckin.ClaimAt = time.Now().UTC()
	if err = s.activityDailyAutoCheckinV2Repository.UpdateDailyAutoCheckinUserV2(updateUserDailyAutoCheckin); err != nil {
		return err
	}

	// increase user credit
	var userCreditReq modelV1.UserTransactionCreateRequest
	userCreditReq.RefId = &userDailyAutoCheckin.Id
	userCreditReq.UserId = userId
	userCreditReq.TypeId = modelV1.CREDIT_TYPE_DAILY_ACTIVITY_BONUS // เพิ่ม type
	userCreditReq.BonusAmount = totalReceived
	userCreditReq.Detail = "โบนัสกิจกรรมรายวัน v2"
	userCreditReq.StartWorkAt = time.Now().UTC() // เริ่มนับตอนกดยินยัน
	_, err = s.userCreditRepository.IncreaseUserCredit(userCreditReq)
	if err != nil {
		return err
	} else {
		err := s.CreateTurnOverFromActivityDailyV2(userId, totalReceived, userDailyAutoCheckin.Id, userDailyAutoCheckin.Id)
		if err != nil {
			return err
		}
	}

	return nil
}

func (s *activityDailyAutoCheckinV2Service) CreateTurnOverFromActivityDailyV2(userId int64, bonusAmount float64, refId int64, noNumber int64) error {

	// serviceTurnover
	checkTurn, err := serviceV1.GetTurnoverSetting(repositoryV1.NewTurnoverRepository(s.shareDb))
	if err != nil {
		return nil
	}

	if checkTurn.TidturnActivityDailyV2Percent > 0 {

		tidTurn := (bonusAmount * float64(checkTurn.TidturnActivityDailyV2Percent) / 100)

		tidTurn = math.Ceil(tidTurn)

		createdTime := time.Now().UTC()
		var createBody modelV1.TurnoverUserStatementCreateBody
		createBody.UserId = userId
		createBody.RefTypeId = refId
		createBody.TypeId = modelV1.TURN_BONUS_ACTIVITY_DAILY_V2
		createBody.Name = modelV1.TURNOVER_CATE_ACTIVITY_DAILY_V2
		createBody.PromotionName = fmt.Sprintf("รายได้กิจกรรมรายวัน auto v2 ครั้งที่: %d", noNumber)
		createBody.BonusAmount = bonusAmount
		createBody.StatusId = modelV1.TURNOVER_STATEMENT_STATUS_PENDING
		createBody.StartTurnAmount = tidTurn
		createBody.StartTurnAt = &createdTime
		createBody.TotalTurnAmount = tidTurn
		if _, err := s.turnoverRepository.CreateTurnoverUserStatement(createBody); err != nil {
			return err
		}
	} else {
		createdTime := time.Now().UTC()
		var createBody modelV1.TurnoverUserStatementCreateBody
		createBody.UserId = userId
		createBody.RefTypeId = refId
		createBody.TypeId = modelV1.TURN_BONUS_ACTIVITY_DAILY_V2
		createBody.Name = modelV1.TURNOVER_CATE_ACTIVITY_DAILY_V2
		createBody.PromotionName = fmt.Sprintf("รายได้กิจกรรมรายวัน v2 auto ID: %d", noNumber)
		createBody.BonusAmount = 0
		createBody.StatusId = modelV1.TURNOVER_STATEMENT_STATUS_PENDING
		createBody.StartTurnAmount = 0
		createBody.StartTurnAt = &createdTime
		createBody.TotalTurnAmount = 0
		craeteTurnId, err := s.turnoverRepository.CreateTurnoverUserStatement(createBody)
		if err != nil {
			return err
		}

		var updateTurnoverUserStatement modelV1.TurnoverUserStatementUpdateBody
		setTotalTurnAmount := 0.0
		setTimeTurnAt := time.Now().UTC()
		updateTurnoverUserStatement.StatusId = modelV1.TURNOVER_STATEMENT_STATUS_COMPLETED
		updateTurnoverUserStatement.TotalTurnAmount = &setTotalTurnAmount
		updateTurnoverUserStatement.EndTurnAt = &setTimeTurnAt
		if err := s.turnoverRepository.UpdateTurnoverUserStatement(*craeteTurnId, updateTurnoverUserStatement); err != nil {
			log.Println("UpdateTurnoverUserStatement", err)
		}

		// create turnover withdraw log
		var createTurnoverWithDrawLog modelV1.CreateTurnoverUserWithdrawLog
		createTurnoverWithDrawLog.UserId = userId
		createTurnoverWithDrawLog.LogKey = fmt.Sprintf("BONUS_ACTIVITY_DAILY_V2_U%d_D%s", userId, time.Now().UTC().Format("20060102150405"))
		createTurnoverWithDrawLog.TotalWithdrawPrice = 0
		createTurnoverWithDrawLog.CurrentTurn = 0
		createTurnoverWithDrawLog.PlayTotal = 0
		createTurnoverWithDrawLog.LastPlayY = 0
		createTurnoverWithDrawLog.LastTotalX = 0
		if _, err = s.turnoverRepository.CreateTurnoverUserWithdrawLog(createTurnoverWithDrawLog); err != nil {
			log.Println("CreateTurnOverFromActivityDailyV2.CreateTurnoverUserWithdrawLog", err)
		}

	}

	return nil
}

func (s *activityDailyAutoCheckinV2Service) GetReportDailyAutoCheckinV2(req modelV2.GetReportDailyAutoCheckinV2Request) (*modelV2.SuccessWithPagination, error) {

	if err := helper.Pagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	turnoverUserActivityDaily, total, err := s.activityDailyAutoCheckinV2Repository.GetReportDailyAutoCheckinV2(req)
	if err != nil {
		return nil, err
	}

	var successWithPagination modelV2.SuccessWithPagination
	successWithPagination.List = turnoverUserActivityDaily
	successWithPagination.Total = total
	successWithPagination.Message = "success"

	return &successWithPagination, nil
}
