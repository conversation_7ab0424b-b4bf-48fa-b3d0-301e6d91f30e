package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"errors"
	"os"
	"strings"
)

type AllianceWhiteService interface {
	UserRegister(user model.AllianceMemberRegister) (*model.UserVerifyOtpResponse, error)
}

type allianceWhiteService struct {
	repo repository.AllianceWhiteRepository
}

func NewAllianceWhiteService(
	repo repository.AllianceWhiteRepository,
) AllianceWhiteService {
	return allianceWhiteService{repo}
}

func (s allianceWhiteService) UserRegister(body model.AllianceMemberRegister) (*model.UserVerifyOtpResponse, error) {

	registerPhone := strings.TrimSpace(body.Phone)

	// // OTP Setting
	// configuration, err := s.repo.GetUserRegisterConfiguration()
	// if err != nil {
	// 	return nil, err
	// }
	// if configuration.UseOtpRegister {
	// 	// later : VerifyOtpRegister(body model.UserVerifyOtpBody) without remote call to prevent timeout error
	// 	if err := s.VerifyLocalOtpRegister(model.UserVerifyOtpBody{
	// 		Phone: registerPhone,
	// 		OtpId: body.OtpId,
	// 		Code:  body.Code,
	// 	}); err != nil {
	// 		return nil, err
	// 	}
	// }

	if user, err := s.repo.CheckUserByPhone(registerPhone); err != nil {
		return nil, internalServerError(err)
	} else if user != nil {
		return nil, badRequest(userExist)
	}

	if accountUser, err := s.repo.CheckUserByAccountNo(body.BankAccount); err != nil {
		return nil, err
	} else if accountUser != nil {
		return nil, badRequest(userAccountNoExist)
	}

	// if otp.AntsOtpId != body.OtpId {
	// 	return nil, notFound(userOtpNotMatch)
	// }

	// if otp.VerifiedAt != nil {
	// 	return nil, badRequest(userOtpUsed)
	// }

	if body.ChannelId != nil {
		haveChannel, err := s.repo.CheckRecommendExist(*body.ChannelId)
		if err != nil {
			return nil, badRequest(err.Error())
		}
		if !haveChannel {
			return nil, notFound(channelInvalid)
		}
	}

	// if os.Getenv("APP_ENV") != "local" {

	// verifyOtpData := model.UserVerifyOtpBody{
	// 	OtpId: body.OtpId,
	// 	Code:  body.Code,
	// }

	// result, err := s.otpRepo.VerifyOtp(verifyOtpData)
	// if err != nil {
	// 	return nil, internalServerError(err)
	// }

	// if result != nil {

	// 	if !result.Result || result.IsErrorCount {
	// 		return nil, badRequest(userOtpNotMatch)
	// 	}

	// 	if result.IsExprCode {
	// 		return nil, badRequest(userOtpExpired)
	// 	}
	// }
	// }

	hashedPassword, err := helper.GenUserPassword(body.Password)
	if err != nil {
		return nil, internalServerError(err)
	}

	var newUser model.UserRegisterForm
	newUser.Username = registerPhone
	newUser.Phone = registerPhone
	newUser.Password = hashedPassword
	newUser.Fullname = body.Fullname
	newUser.BankAccount = body.BankAccount
	newUser.BankId = body.BankId
	if body.ChannelId != nil {
		newUser.ChannelId = *body.ChannelId
	}
	newUser.LineId = body.LineId
	// newUser.Encrypt = helper.Encode(body.Password)
	// P.layer ******** แก้ให้ gen เองจาก API
	passwordAgentGame := helper.Encode(os.Getenv("AGENT_NAME") + "Aa01")
	newUser.Encrypt = passwordAgentGame

	newUser.IpRegistered = body.IpRegistered

	splitFullname := strings.Split(body.Fullname, " ")
	var firstname, lastname *string
	if len(splitFullname) == 2 {
		firstname = &splitFullname[0]
		lastname = &splitFullname[1]
		newUser.Firstname = *firstname
		newUser.Lastname = *lastname
	}

	if len(splitFullname) == 3 {
		firstname = &splitFullname[1]
		lastname = &splitFullname[2]
		newUser.Firstname = *firstname
		newUser.Lastname = *lastname
	}

	var credit float64
	var afObj model.Affiliate
	var refTypeId int64

	if body.RefBy != nil && *body.RefBy != 0 {
		ref, err := s.repo.GetUserIdByRef(*body.RefBy)
		if err != nil {
			return nil, errors.New("INVALID_REF_ID")
		}
		if ref != nil {
			refTypeId = ref.UserTypeId
			afObj.RefId = ref.UserId
			newUser.RefBy = &ref.UserId
			credit, err = s.calCommissionRegister()
			if err != nil {
				return nil, internalServerError(err)
			}
		}
	}

	insertId, err := s.repo.UserRegister(newUser, afObj, credit, refTypeId)
	if err != nil {
		return nil, internalServerError(err)
	}

	userObj := model.UserResponse{
		Id:         *insertId,
		Phone:      registerPhone,
		Fullname:   newUser.Fullname,
		MemberCode: nil, // ไม่มี
	}

	token, err := helper.CreateJWTUser(userObj)
	if err != nil {
		return nil, internalServerError(err)
	}

	// Single Session
	if err := s.repo.SetUserSingleSession(*insertId, token); err != nil {
		return nil, internalServerError(err)
	}

	response := model.UserVerifyOtpResponse{
		Token: token,
	}

	// var noti model.NotifyLineRequest
	// noti.TypeNotify = model.IsMemberRegistration
	// noti.Phone = registerPhone
	// if err := s.notiLine.ExternalNotification(noti); err != nil {
	// 	return nil, nil
	// }

	// [********] socket
	// var socketMessage model.WebScoket
	// bank, _ := s.repo.GetBankById(body.BankId)
	// if bank != nil {
	// 	socketMessage.BankAccount = body.BankAccount
	// } else {
	// 	socketMessage.BankAccount = fmt.Sprintf("%s %s", bank.Name, body.BankAccount)
	// }

	// socketMessage.PhoneNumber = body.Phone
	// socketMessage.FullName = body.Fullname
	// socketMessage.AlertType = "NEW_MEMBER"
	// if err := s.repo.WebSocket(socketMessage); err != nil {
	// 	return nil, nil
	// }

	return &response, nil
}

func (s allianceWhiteService) calCommissionRegister() (float64, error) {

	data, err := s.repo.GetCommissionSetting()
	if err != nil {
		return 0, internalServerError(err)
	}

	var commission float64 = data.ReferralBonus

	return commission, nil
}
