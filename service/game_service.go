package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"os"
	"sort"
	"strconv"
	"strings"
	"time"
)

type GameService interface {
	GameGetProviders() []model.GameProvider
	GameGetMainCategory() ([]model.GameCategory, error)
	GameGetCategory(cateName string) (*model.GameCategoryResponse, error)
	GameGetList(req model.AgentGameListRequest) ([]model.GameDetail, error)
	GamePlay(body model.AgcPlayBody) (*model.AgcPlayResponse, error)
	GamePlayHtml(body model.AgcPlayHtmlRequest) (string, error)
	TestLoginGame(body model.AgcTestLoginRequest) (string, error)
	AgcVendorMaintenanceList() (*model.AgcVendorMaintenanceListResponse, error)
	// Config
	GetGameConfiguration() (*model.GameConfiguration, error)

	// Cache
	ClearGameCache() error
}

type gameService struct {
	repo repository.AgentInfoRepository
}

func NewGameService(
	repo repository.AgentInfoRepository,
) GameService {
	return &gameService{repo}
}

var gamelistResponse = make(map[string]model.GamelistResponse)
var categorylistResponse = make(map[string]model.GameCategoryResponse)

func (s gameService) ClearGameCache() error {
	gamelistResponse = make(map[string]model.GamelistResponse)
	categorylistResponse = make(map[string]model.GameCategoryResponse)
	return nil
}

func (s gameService) GetGameConfiguration() (*model.GameConfiguration, error) {

	var result model.GameConfiguration

	setting, err := s.repo.GetConfiguration()
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound("SETTING_NOT_FOUND")
		}
		return nil, err
	}

	result.OpenGameNewTab = setting.OpenGameNewTab

	return &result, nil
}

func (s gameService) GetSimilarImage(vendorName string) (map[string]string, error) {

	result := make(map[string]string)

	jsonData, _ := os.Open("json/vendor.json")
	byteValue, err := io.ReadAll(jsonData)
	if err != nil {
		log.Println(err)
		return nil, err
	}

	var vendorList map[string][]model.JsonVendor
	if err := json.Unmarshal(byteValue, &vendorList); err != nil {
		log.Println(err)
		return nil, err
	}

	// get list by category name
	for _, games := range vendorList {
		for _, game := range games {
			key := strings.ToUpper(fmt.Sprintf("%s_%s", game.Category, game.VendorCode))
			result[key] = game.ImageName
			if len(game.VendorAlias) > 0 {
				for _, alias := range game.VendorAlias {
					key = strings.ToUpper(fmt.Sprintf("%s_%s", game.Category, alias))
					result[key] = game.ImageName
				}
			}
		}
	}

	return result, nil
}

func (s gameService) GameGetProviders() []model.GameProvider {

	var list []model.GameProvider

	agentProvider := os.Getenv("AGENT_PROVIDER")
	if agentProvider == "AMB" {
		providerList, err := s.repo.AmbGetGameProviderList(model.AmbGameProviderListRequest{})
		if err != nil {
			return nil
		}
		for _, category := range providerList.Data {
			// Each Category
			for _, game := range category {
				list = append(list, model.GameProvider{
					VendorName: game.ProviderName,
					VendorCode: game.Provider,
				})
			}
		}
	} else {
		gameProvider := os.Getenv("GAME_PROVIDER")
		splitList := strings.Split(gameProvider, ",")
		for _, file := range splitList {
			splitName := strings.Split(file, ":")
			list = append(list, model.GameProvider{
				VendorName: splitName[1],
				VendorCode: splitName[0],
			})
		}
	}
	return list
}

func (s gameService) GameGetMainCategory() ([]model.GameCategory, error) {

	var result []model.GameCategory

	agentProvider := os.Getenv("AGENT_PROVIDER")
	if agentProvider == "AMB" {
		providerList, err := s.repo.AmbGetGameProviderList(model.AmbGameProviderListRequest{})
		if err != nil {
			return nil, err
		}
		for _, category := range providerList.Data {
			// Each Category
			for _, game := range category {
				result = append(result, model.GameCategory{
					Name:       game.ProviderName,
					VendorCode: game.Provider,
					ImageName:  helper.ToSnake(game.ProviderName),
					ImageUrl:   "",                // ใช้รูปเดียวกับ JSON game.LogoURL,
					Category:   game.ProviderType, // Name,Tab,gameType,providerType ??
				})
			}
		}
	} else {
		// POPULAR, SPORT, CASINO, SLOT
		result = append(result, model.GameCategory{
			Name:      "POPULAR",
			ImageName: "popular",
			Category:  "POPULAR",
		})
		result = append(result, model.GameCategory{
			Name:      "SPORT",
			ImageName: "sport",
			Category:  "SPORT",
		})
		result = append(result, model.GameCategory{
			Name:      "CASINO",
			ImageName: "casino",
			Category:  "CASINO",
		})
		result = append(result, model.GameCategory{
			Name:      "SLOT",
			ImageName: "slot",
			Category:  "SLOT",
		})
	}
	return result, nil
}

func (s gameService) GameGetList(req model.AgentGameListRequest) ([]model.GameDetail, error) {

	var response []model.GameDetail
	sortMap := map[string]model.GameDetail{}
	var sortKeys []string

	agentProvider := os.Getenv("AGENT_PROVIDER")
	if agentProvider == "AMB" {
		if req.Category == "POPULAR" {
			// BUG: ไม่มี POPULAR ใน AMB = SHOW ALL == GET ALL
			avaliableCategory := []string{"SLOT", "CASINO", "SPORT"}
			for _, category := range avaliableCategory {
				gameInfo, err := s.repo.AmbGetGameList(model.AmbGameListRequest{
					Prefix: req.Vendor,
					Tab:    category,
				})
				if err != nil {
					return nil, err
				}

				for _, game := range gameInfo.Data {
					for _, gameDetail := range game.Games {
						if strings.ToLower(gameDetail.Status) == "active" {
							sortKey := gameDetail.GameName + gameDetail.Id
							sortMap[sortKey] = model.GameDetail{
								VendorCode: game.Provider,
								GameName:   gameDetail.GameName,
								GameCode:   gameDetail.Id,
								ImageUrl:   gameDetail.Image.Horizontal,
								Image:      gameDetail.Image.Vertical,
							}
							sortKeys = append(sortKeys, sortKey)
						}
					}
				}
			}
		} else {
			// Single category for CASINO, SLOT, SPORT
			gameInfo, err := s.repo.AmbGetGameList(model.AmbGameListRequest{
				Prefix: req.Vendor,
				Tab:    req.Category,
			})
			if err != nil {
				return nil, err
			}

			for _, game := range gameInfo.Data {
				for _, gameDetail := range game.Games {
					if strings.ToLower(gameDetail.Status) == "active" {
						sortKey := gameDetail.GameName + gameDetail.Id
						sortMap[sortKey] = model.GameDetail{
							VendorCode: game.Provider,
							GameName:   gameDetail.GameName,
							GameCode:   gameDetail.Id,
							ImageUrl:   gameDetail.Image.Horizontal,
							Image:      gameDetail.Image.Vertical,
						}
						sortKeys = append(sortKeys, sortKey)
					}
				}
			}
		}
		// Make Array
		if len(sortKeys) > 0 {
			sort.Strings(sortKeys)
			for _, k := range sortKeys {
				if _, ok := sortMap[k]; ok {
					response = append(response, sortMap[k])
				}
			}
		}
	} else {

		for key, value := range gamelistResponse {
			if time.Now().UTC().After(value.ExpiredAt) {
				delete(gamelistResponse, key)
			}
		}

		cachedGames, ok := gamelistResponse[req.Vendor]
		if ok {
			// Return the cached list directly
			for _, game := range cachedGames.Result {
				response = append(response, model.GameDetail{
					VendorCode: game.VendorCode,
					GameName:   game.GameName,
					GameCode:   game.GameCode,
					ImageUrl:   game.ImageUrl,
					Image:      game.Image,
				})
			}
			return response, nil
		}

		// ** Call API **
		var reqList model.GetAgentGameRequestList
		reqList.AgentType = os.Getenv("AGENT_TYPE")
		reqList.VendorCode = req.Vendor

		listGame, err := s.repo.GetAgentGameListFromMaster(reqList)
		if err != nil {
			return nil, err
		}

		if listGame == nil || len(listGame) <= 0 {
			return nil, nil
		}

		// P.Makold confirm 2025-01-24
		newList := []model.GameDetail{}
		// AGENT_NAME=zta68n9
		getAgentName := os.Getenv("AGENT_NAME")
		for _, game := range listGame {
			// winlotto close this game only cq9 gamecode mr.bean
			if req.Vendor == "CQ9" && game.GameCode == "61" && getAgentName == "zta68n9" {
				continue
			} else {
				newList = append(newList, game)
			}
		}

		expirationTime := time.Now().UTC().Add(1 * time.Minute)
		gamelistResponse[req.Vendor] = model.GamelistResponse{
			Result:    newList,
			ExpiredAt: expirationTime,
		}

		response = newList

		return response, nil
		// return s.GetAgcGameList(req.Vendor)
	}

	return response, nil
}

func (s gameService) GetAgcGameListBackUp(providerName string) ([]model.GameDetail, error) {

	// อะไรที่ผมไม่เห็น ใน vendor จะ comment ทิ้ง
	switch providerName {
	case "BS":
		list, err := codeIsInt("json/betsoft.json")
		if err != nil {
			return nil, err
		}
		return list, nil
	// case "BGE":
	// 	list, err := codeIsInt("json/bge.json")
	// 	if err != nil {
	// 		return nil, err
	// 	}
	// 	return list, nil
	// case "BNG":
	// 	list, err := codeIsStringAndHaveHttps("json/bng.json")
	// 	if err != nil {
	// 		return nil, err
	// 	}
	// 	return list, nil
	case "CQ9":
		list, err := codeIsString("json/cq9.json")
		if err != nil {
			return nil, err
		}
		return list, nil
	// case "DragonGaming":
	// 	list, err := codeIsString("json/dragongaming.json")
	// 	if err != nil {
	// 		return nil, err
	// 	}
	// 	return list, nil
	case "DT":
		list, err := codeIsString("json/dreamtech.json")
		if err != nil {
			return nil, err
		}
		return list, nil
	case "FUN":
		list, err := codeIsString("json/fungaming.json")
		if err != nil {
			return nil, err
		}
		return list, nil
	case "Genesis":
		list, err := codeIsString("json/genesis.json")
		if err != nil {
			return nil, err
		}
		return list, nil
	case "Habanero":

		list, err := codeIsString("json/habanero.json")
		if err != nil {
			return nil, err
		}

		return list, nil

	// case "iconic":

	// 	list, err := codeIsString("json/iconic.json")
	// 	if err != nil {
	// 		return nil, err
	// 	}

	// 	return list, nil

	case "JOKER":

		list, err := codeIsStringAndHaveHttps("json/joker.json")
		if err != nil {
			return nil, err
		}

		return list, nil

	case "Mario":

		list, err := codeIsString("json/mario.json")
		if err != nil {
			return nil, err
		}

		return list, nil

	case "MG":

		list, err := codeIsString("json/microgaming.json")
		if err != nil {
			return nil, err
		}

		return list, nil

	case "MX":

		list, err := codeIsString("json/pragmatic.json")
		if err != nil {
			return nil, err
		}

		return list, nil

	case "PNG":

		list, err := codeIsStringAndHaveUrl("json/png.json")
		if err != nil {
			return nil, err
		}

		return list, nil

	// case "JUST_NO_GAMELIST_PGSOFT":
	// 	// ลิสนี้ดึงมาแล้ว แต่ลูกค้าบอกว่าไม่ถูก ที่มันมี panda worior อยู่
	// 	list, err := codeIsPgsoft("json/pgs.json")
	// 	if err != nil {
	// 		return nil, err
	// 	}

	// 	return list, nil

	// case "ptgame":

	// 	list, err := codeIsStringAndHaveHttps("json/ptgame.json")
	// 	if err != nil {
	// 		return nil, err
	// 	}

	// 	return list, nil

	// case "RG":

	// 	list, err := codeIsIntAndImgNoDomain("json/relaxgaming.json")
	// 	if err != nil {
	// 		return nil, err
	// 	}

	// 	return list, nil

	//case "SAE":
	//
	//	list, err := codeIsStringAndHaveHttps("json/sae.json")
	//	if err != nil {
	//		return nil, err
	//	}
	//
	//	return list, nil

	// case "PT":

	// 	list, err := codeIsString("json/skywind.json")
	// 	if err != nil {
	// 		return nil, err
	// 	}

	// 	return list, nil

	case "VT":

		list, err := codeIsStringAndImgNoDomain("json/vt.json")
		if err != nil {
			return nil, err
		}

		return list, nil

	// case "YGG":

	// 	list, err := codeIsIntAndImgNoDomain("json/ygg.json")
	// 	if err != nil {
	// 		return nil, err
	// 	}

	// 	return list, nil

	case "SPADE":

		list, err := codeIsString("json/spadegaming.json")
		if err != nil {
			return nil, err
		}

		return list, nil

	default:
		return nil, nil
	}
}

func (s gameService) GameGetCategory(cateName string) (*model.GameCategoryResponse, error) {

	var result model.GameCategoryResponse
	sortMap := map[string]model.GameCategory{}
	var sortKeys []string

	// ** ONLY (slot, casino, sport) AVAILABLE **

	agentProvider := os.Getenv("AGENT_PROVIDER")
	if agentProvider == "AMB" {

		if strings.ToLower(cateName) == "sport" {
			cateName = "ESPORT"
		}

		imageKeyList, err := s.GetSimilarImage("BG")
		if err != nil {
			return nil, err
		}

		providerList, err := s.repo.AmbGetGameProviderList(model.AmbGameProviderListRequest{})
		if err != nil {
			return nil, err
		}
		for tab, category := range providerList.Data {
			// Each Category (slot, casino, sport)
			curTabName := strings.ToLower(tab)
			if curTabName == "slot" || curTabName == "casino" || curTabName == "esport" {
				for _, game := range category {
					// later what POPULAR is // && game.ProviderTier == "exclusive"
					imgPath := helper.ToSnake(game.ProviderName) // DEFAULT
					imgKey := strings.ToUpper(fmt.Sprintf("%s_%s", curTabName, game.Provider))
					if _, ok := imageKeyList[imgKey]; ok {
						// fmt.Println("Has imgKey", imgKey)
						imgPath = imageKeyList[imgKey]
						// fmt.Println("NO-imgPath", imgKey, imgPath)
					}
					if cateName == "POPULAR" {
						sortKey := game.ProviderName + game.Provider
						sortMap[sortKey] = model.GameCategory{
							Name:       game.ProviderName,
							VendorCode: game.Provider,
							ImageName:  imgPath,
							ImageUrl:   "",                // game.LogoURL,
							Category:   game.ProviderType, // Name,Tab,gameType,providerType ??
						}
						sortKeys = append(sortKeys, sortKey)
					} else if game.ProviderType == cateName {
						sortKey := game.ProviderName + game.Provider
						sortMap[sortKey] = model.GameCategory{
							Name:       game.ProviderName,
							VendorCode: game.Provider,
							ImageName:  imgPath,
							ImageUrl:   "",                // game.LogoURL,
							Category:   game.ProviderType, // Name,Tab,gameType,providerType ??
						}
						sortKeys = append(sortKeys, sortKey)
					}
				}
			}
		}
		// Make Array
		if len(sortKeys) > 0 {
			sort.Strings(sortKeys)
			for _, k := range sortKeys {
				if _, ok := sortMap[k]; ok {
					result.Result = append(result.Result, sortMap[k])
				}
			}
		}
	} else {

		// Clean expired cache
		for key, value := range categorylistResponse {
			if time.Now().UTC().After(value.ExpiredAt) {
				delete(categorylistResponse, key)
			}
		}

		// var req model.GetAgentGameCategoryListFromMasterRequest
		// req.AgentType = os.Getenv("AGENT_TYPE")
		// req.CategoryName = cateName

		cachedCategories, ok := categorylistResponse[cateName]
		if ok {
			result = cachedCategories
			return &cachedCategories, nil
		}

		// Fetch priority settings only active game true
		gamePriorityList, err := s.repo.GetAgentGamePrioritySetting(cateName, true)
		if err != nil {
			return nil, err
		}

		getInternalAgentPgHardSetting, err := s.repo.GetInternalAgentPgHardSetting()
		if err != nil {
			return nil, err
		}

		getInternalAgentCtwSetting, err := s.repo.GetInternalAgentCtwSetting()
		if err != nil {
			return nil, err
		}

		var listGameCategory []model.GameCategory
		for _, game := range gamePriorityList {
			var gameCategory model.GameCategory
			gameCategory.Name = game.VendorCode
			gameCategory.VendorCode = game.VendorCode
			gameCategory.ImageName = game.ImageName
			gameCategory.Category = game.CategoryName
			gameCategory.IsActive = game.IsShow // is active ใช้ สำหรับ BOF
			gameCategory.TotalPlayed = game.TotalPlayed
			gameCategory.IsGameMaintain = false // todo today

			// AGENT PG HARD
			gameCategory.IsUseNewPgh = getInternalAgentPgHardSetting.ProgramAllowUse == "ALLOW_USE" && game.VendorCode == "PGSOFT" && getInternalAgentPgHardSetting.IsActive

			// AGENT CTW
			if getInternalAgentCtwSetting.IsActive && getInternalAgentCtwSetting.ProgramAllowUse == "ALLOW_USE" {
				if game.VendorCode == "PGSOFT" || game.VendorCode == "MX" || game.VendorCode == "JiliGames" {
					// ปิด เมื่อ มี PGSOFT แล้ว p.mink confirm
					if game.VendorCode == "PGSOFT" && getInternalAgentPgHardSetting.ProgramAllowUse == "ALLOW_USE" && getInternalAgentPgHardSetting.IsActive {
						gameCategory.IsUseNewAgentCtw = false
					} else {
						gameCategory.IsUseNewAgentCtw = true
					}
				}
			}

			// IsUseNewAgentCtw
			listGameCategory = append(listGameCategory, gameCategory)
		}

		expirationTime := time.Now().UTC().Add(1 * time.Minute)
		categorylistResponse[cateName] = model.GameCategoryResponse{
			Result:    listGameCategory,
			ExpiredAt: expirationTime,
		}
		result = categorylistResponse[cateName]
	}

	return &result, nil
}

func codeIsInt(path string) ([]model.GameDetail, error) {

	jsonData, _ := os.Open(path)

	byteValue, err := io.ReadAll(jsonData)
	if err != nil {
		return nil, err
	}

	var gameList []model.GameDetailBetSoft
	if err := json.Unmarshal(byteValue, &gameList); err != nil {
		return nil, err
	}

	var response []model.GameDetail

	for i := 0; i < len(gameList); i++ {

		conv := strconv.Itoa(gameList[i].GameCode)

		response = append(response, model.GameDetail{
			GameName: gameList[i].GameName,
			GameCode: conv,
			ImageUrl: gameList[i].ImageIcon,
		})
	}

	return response, nil
}

func codeIsPgsoft(path string) ([]model.GameDetail, error) {

	jsonData, _ := os.Open(path)

	byteValue, err := io.ReadAll(jsonData)
	if err != nil {
		return nil, err
	}

	var gameList []model.GameDetailPgsoft
	if err := json.Unmarshal(byteValue, &gameList); err != nil {
		return nil, err
	}

	var response []model.GameDetail

	for i := 0; i < len(gameList); i++ {
		response = append(response, model.GameDetail{
			GameName: gameList[i].GameName,
			GameCode: gameList[i].GameCode,
			ImageUrl: gameList[i].GameImg,
		})
	}

	return response, nil
}

func (s gameService) GamePlay(body model.AgcPlayBody) (*model.AgcPlayResponse, error) {

	user, err := s.repo.FrontGetUser(body.UserId)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(userNotFound)
		}
		return nil, err
	}
	// CheckIsLockedCreditPromotionByUserId(userId int64) (bool, error)
	isLocked, err := s.repo.CheckIsLockedCreditPromotionByUserId(body.UserId)
	if err != nil {
		return nil, err
	}

	if isLocked {
		return nil, badRequest("USER_CREDIT_LOCKED_FROM_PROMOTION")
	}

	obj := map[string]interface{}{}
	obj["Vendor"] = body.Vendor
	obj["Lang"] = body.Lang
	obj["Browser"] = body.Browser
	obj["GameCode"] = body.GameCode

	if body.Vendor == "CQ9" || body.Vendor == "CQ9LIVE" {
		obj["GameHall"] = "CQ9"
	} else if body.Vendor == "PGSOFT" {
		// [20231227] ปรับ PGSOFT เป็นเกมลิส
		// [20231228] ปรับ PGSOFT ไม่จำเป็นต้องมีลิส แล้วเล่นได้เลย
		// แล้วก็ทำ API GET RESPONSE HTML
		obj["GameCode"] = "redirect:PGSOFT"
		// GameProvider string  `json:"gameProvider"`
		// GameUrl      string  `json:"gameUrl"`
		// MaxPerBet    float64 `json:"maxPerBet"`
		// GameToken    string  `json:"gameToken"`

		// https://dev-socket.cbgame88.com/send-deposit
		apiEnpoint := "/api/v1/games/play-html"

		playResult := model.AgcPlayResponse{}
		playResult.GameProvider = body.Vendor
		playResult.GameUrl = apiEnpoint
		playResult.MaxPerBet = 10000
		playResult.GameToken = "redirect:PGSOFT"
		return &playResult, nil
	} else if body.Vendor == "SAE" || body.Vendor == "SA" || body.Vendor == "BG" {
		obj["GameCode"] = ""
	} else if body.Vendor == "PPL" {
		obj["GameCode"] = "101"
	} else {
		obj["GameCode"] = body.GameCode
		obj["Vendor"] = body.Vendor
	}

	gameObj := obj

	playResult := model.AgcPlayResponse{}
	domainName := os.Getenv("DOMAIN_NAME")
	agentName := os.Getenv("AGENT_NAME")
	password, err := helper.Decode(user.Encrypt)
	if err != nil {
		return nil, internalServerError(err)
	}
	timeNow := time.Now()

	agentProvider := os.Getenv("AGENT_PROVIDER")
	if agentProvider == "AMB" {
		ambStartGameRequest := model.AmbStartGameRequest{
			Username:    user.MemberCode,
			GameID:      body.GameCode,
			Provider:    body.Vendor,
			RedirectUrl: body.GameToken, // login as token
			Language:    body.Lang,
			Tab:         body.CategoryName,
			Device:      body.Browser,
			Ip:          body.Ip,
		}
		data, err := s.repo.AmbPlay(ambStartGameRequest)
		if err != nil {
			log.Println("AmbPlay.ERROR", err)
			return nil, badRequest("GAME_NOT_AVAILABLE")
		}
		playResult.GameProvider = agentProvider
		// if data.Result.Data != "" {
		// 	playResult.GameUrl = strings.ReplaceAll(data.Result.Data, "////", "//")
		// 	playResult.MaxPerBet = data.Result.Settings.MaxPerBet
		// }
		playResult.GameUrl = data.Data.Uri
		// if loginResult != nil && body.GameToken != loginResult.Token {
		// 	playResult.GameToken = loginResult.Token
		// }
	} else {
		// fmt.Println("AGC.gameObj", helper.StructJson(gameObj))
		data, err := s.repo.AgcPlay(body.GameToken, gameObj)
		if err != nil {
			log.Println("AgcPlay.ERROR", err)
			return nil, badRequest("GAME_NOT_AVAILABLE")
		}
		// fmt.Println("AGC.play.data", helper.StructJson(data))
		if data.Error.Code != 0 {
			message := fmt.Sprintf("Code: %v %v", data.Error.Code, data.Error.Message)
			log.Println("AgcPlay.data.Error.Code", message)
			return nil, badRequest("GAME_NOT_AVAILABLE")
		}
		var loginResult *model.AgcLoginResponse
		if data.Result.Data == "" {
			sign := agentName + user.MemberCode + *password
			agentData := model.AgcLogin{}
			agentData.Username = user.MemberCode
			agentData.Partner = agentName
			agentData.Timestamp = timeNow.Unix()
			agentData.Sign = helper.CreateSign(os.Getenv("AGENT_KEY"), sign, timeNow)
			agentData.Domain = domainName
			agentData.Lang = "th-th"
			agentData.IsMobile = false
			agentData.Ip = body.Ip
			loginResult, err = s.repo.AgcLogin(agentData)
			if err != nil {
				return nil, badRequest("GAME_NOT_AVAILABLE")
			}
			data, _ = s.repo.AgcPlay(loginResult.Token, gameObj)
		}
		playResult.GameProvider = agentProvider
		if data.Result.Data != "" {
			playResult.GameUrl = strings.ReplaceAll(data.Result.Data, "////", "//")
			playResult.MaxPerBet = data.Result.Settings.MaxPerBet
		}
		if body.Vendor == "MX" || body.Vendor == "PPL" {
			playResult.GameUrl = data.Result.Metadata
		}
		if loginResult != nil && body.GameToken != loginResult.Token {
			playResult.GameToken = loginResult.Token
		} else {
			playResult.GameToken = body.GameToken
		}

	}

	return &playResult, nil
}

func (s gameService) GamePlayHtml(body model.AgcPlayHtmlRequest) (string, error) {

	actionAt := time.Now()

	user, err := s.repo.FrontGetUser(body.UserId)
	if err != nil {
		if err.Error() == recordNotFound {
			return "", notFound(userNotFound)
		}
		return "", err
	}

	html := "<h1>Not Found</h1>"

	agentProvider := os.Getenv("AGENT_PROVIDER")
	if agentProvider == "AMB" {
		log.Println("NO_CASE")
	} else {

		obj := map[string]interface{}{}
		obj["Vendor"] = body.Vendor
		obj["Lang"] = body.Lang
		obj["Browser"] = body.Browser
		obj["GameCode"] = ""

		gameObj := obj
		if body.Vendor != "PGSOFT" {
			return "ERROR:NOT_FOUND", internalServerError(err)
		}

		domainName := os.Getenv("DOMAIN_NAME")
		agentName := os.Getenv("AGENT_NAME")
		password, err := helper.Decode(user.Encrypt)
		if err != nil {
			return "ERROR:INVALID_USER", internalServerError(err)
		}
		// PLAY = New Login
		agentData := model.AgcLogin{}
		agentData.Username = user.MemberCode
		agentData.Partner = agentName
		agentData.Timestamp = actionAt.Unix()
		agentData.Sign = helper.CreateSign(os.Getenv("AGENT_KEY"), agentName+user.MemberCode+*password, actionAt)
		agentData.Domain = domainName
		agentData.Lang = "th-th"
		agentData.IsMobile = false
		agentData.Ip = body.Ip
		loginResult, err := s.repo.AgcLogin(agentData)
		if err != nil {
			log.Println("AgcLogin.ERROR.CANT_LOGIN", helper.StructJson(loginResult))
			return "ERROR:CANT_LOGIN", internalServerError(err)
		}
		if loginResult == nil || loginResult.Token == "" {
			log.Println("AgcLogin.ERROR.CANT_LOGIN", helper.StructJson(loginResult))
			return "ERROR:CANT_LOGIN", errors.New("INVALID_LOGIN_RESULT")
		}
		// fmt.Println("AgcLogin", helper.StructJson(loginResult))
		playResp, err := s.repo.AgcPlay(loginResult.Token, gameObj)
		if err != nil {
			return "ERROR:CANT_PLAY", internalServerError(err)
		}
		if playResp.Error.Code != 0 {
			message := fmt.Sprintf("Code: %v %v", playResp.Error.Code, playResp.Error.Message)
			return "ERROR:", internalServerError(errors.New(message))
		}
		// fmt.Println("AgcPlay", helper.StructJson(playResp))
		html = playResp.Result.Data
	}

	return html, nil
}

func (s gameService) TestLoginGame(body model.AgcTestLoginRequest) (string, error) {

	actionAt := time.Now()

	user, err := s.repo.FrontGetUser(body.UserId)
	if err != nil {
		if err.Error() == recordNotFound {
			return "", notFound(userNotFound)
		}
		return "", err
	}

	html := "<h1>Not Found</h1>"

	agentProvider := os.Getenv("AGENT_PROVIDER")
	if agentProvider == "AMB" {
		log.Println("NO_CASE")
	} else {

		obj := map[string]interface{}{}
		obj["Vendor"] = body.Vendor
		obj["Lang"] = body.Lang
		obj["Browser"] = body.Browser
		obj["GameCode"] = ""

		// gameObj := obj
		// if body.Vendor != "PGSOFT" {
		// 	return "ERROR:NOT_FOUND", internalServerError(err)
		// }

		domainName := os.Getenv("DOMAIN_NAME")
		agentName := os.Getenv("AGENT_NAME")
		password, err := helper.Decode(user.Encrypt)
		if err != nil {
			return "ERROR:INVALID_USER", internalServerError(err)
		}
		// PLAY = New Login
		agentData := model.AgcLogin{}
		agentData.Username = user.MemberCode
		agentData.Partner = agentName
		agentData.Timestamp = actionAt.Unix()
		agentData.Sign = helper.CreateSign(os.Getenv("AGENT_KEY"), agentName+user.MemberCode+*password, actionAt)
		agentData.Domain = domainName
		agentData.Lang = "th-th"
		agentData.IsMobile = false
		agentData.Ip = body.Ip
		loginResult, err := s.repo.AgcLogin(agentData)
		if err != nil {
			log.Println("AgcLogin.ERROR.CANT_LOGIN", helper.StructJson(loginResult))
			return "ERROR:CANT_LOGIN", internalServerError(err)
		}
		if loginResult == nil || loginResult.Token == "" {
			log.Println("AgcLogin.ERROR.CANT_LOGIN", helper.StructJson(loginResult))
			return "ERROR:CANT_LOGIN", errors.New("INVALID_LOGIN_RESULT")
		}
		// fmt.Println("AgcLogin", helper.StructJson(loginResult))

		html = loginResult.Token
	}

	return html, nil
}

func codeIsIntAndImgNoDomain(path string) ([]model.GameDetail, error) {

	jsonData, _ := os.Open(path)

	byteValue, err := io.ReadAll(jsonData)
	if err != nil {
		return nil, err
	}

	var gameList []model.GameDetailBetSoft
	if err := json.Unmarshal(byteValue, &gameList); err != nil {
		return nil, err
	}

	var response []model.GameDetail

	for i := 0; i < len(gameList); i++ {

		conv := strconv.Itoa(gameList[i].GameCode)

		response = append(response, model.GameDetail{
			GameName: gameList[i].GameName,
			GameCode: conv,
			ImageUrl: os.Getenv("MEDIA_URL") + gameList[i].ImageIcon,
		})
	}

	return response, nil
}

func codeIsString(path string) ([]model.GameDetail, error) {

	jsonData, _ := os.Open(path)

	byteValue, err := io.ReadAll(jsonData)
	if err != nil {
		return nil, err
	}

	var gameList []model.GameDetailBng
	if err := json.Unmarshal(byteValue, &gameList); err != nil {
		return nil, err
	}

	var response []model.GameDetail

	for i := 0; i < len(gameList); i++ {
		response = append(response, model.GameDetail{
			GameName: gameList[i].GameName,
			GameCode: gameList[i].GameCode,
			ImageUrl: gameList[i].ImageIcon,
		})
	}

	return response, nil
}

func codeIsStringAndImgNoDomain(path string) ([]model.GameDetail, error) {

	jsonData, _ := os.Open(path)

	byteValue, err := io.ReadAll(jsonData)
	if err != nil {
		return nil, err
	}

	var gameList []model.GameDetailBng
	if err := json.Unmarshal(byteValue, &gameList); err != nil {
		return nil, err
	}

	var response []model.GameDetail

	for i := 0; i < len(gameList); i++ {
		imgPath := os.Getenv("MEDIA_URL") + gameList[i].GameCode + ".jpg"
		if gameList[i].ImageIcon != "" {
			imgPath = os.Getenv("MEDIA_URL") + gameList[i].ImageIcon
		}
		response = append(response, model.GameDetail{
			GameName: gameList[i].GameName,
			GameCode: gameList[i].GameCode,
			ImageUrl: imgPath,
		})
	}

	return response, nil
}

func codeIsStringAndHaveHttps(path string) ([]model.GameDetail, error) {

	jsonData, _ := os.Open(path)

	byteValue, err := io.ReadAll(jsonData)
	if err != nil {
		return nil, err
	}

	var gameList []model.GameDetailBng
	if err := json.Unmarshal(byteValue, &gameList); err != nil {
		return nil, err
	}

	var response []model.GameDetail

	for i := 0; i < len(gameList); i++ {
		response = append(response, model.GameDetail{
			GameName: gameList[i].GameName,
			GameCode: gameList[i].GameCode,
			ImageUrl: "https:" + gameList[i].ImageIcon,
		})
	}

	return response, nil
}

func codeIsStringAndHaveUrl(path string) ([]model.GameDetail, error) {

	jsonData, _ := os.Open(path)

	byteValue, err := io.ReadAll(jsonData)
	if err != nil {
		return nil, err
	}

	var gameList []model.GameDetailBng
	if err := json.Unmarshal(byteValue, &gameList); err != nil {
		return nil, err
	}

	var response []model.GameDetail

	for i := 0; i < len(gameList); i++ {
		response = append(response, model.GameDetail{
			GameName: gameList[i].GameName,
			GameCode: gameList[i].GameCode,
			ImageUrl: gameList[i].Image,
		})
	}

	return response, nil
}

func (s gameService) AgcVendorMaintenanceList() (*model.AgcVendorMaintenanceListResponse, error) {

	userRandom, err := s.repo.RandomUserWithMemberForGetStatusMatainace()
	if err != nil {
		return nil, err
	}
	user, err := s.repo.FrontGetUser(userRandom.Id)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(userNotFound)
		}
		return nil, err
	}
	agentName := os.Getenv("AGENT_NAME")
	password, err := helper.Decode(user.Encrypt)
	if err != nil {
		return nil, internalServerError(err)
	}
	timeNow := time.Now()

	var agentData model.AgcVendorMaintenanceList
	sign := agentName + user.MemberCode + *password
	// sign := agentName
	// fmt.Println("AGC.sign", agentName, user.MemberCode, *password)
	agentData.Sign = helper.CreateSign(os.Getenv("AGENT_KEY"), sign, timeNow)
	agentData.Partner = agentName
	agentData.TimeStamp = timeNow.Unix()
	// fmt.Println("AGC.agentData", helper.StructJson(agentData))
	getAgentList, err := s.repo.AgcVendorMaintenanceList(agentData)
	if err != nil {
		return nil, err
	}

	return getAgentList, nil
}
