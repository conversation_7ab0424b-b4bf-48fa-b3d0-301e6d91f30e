package service

import (
	"cybergame-api/model"
	"cybergame-api/repository"
	"fmt"
	"log"
)

type TelegramService interface {
	// Telegram
	GetTelegramAccessToken() (*model.TelegramAccessTokenResponse, error)
	UpdateTelegramAccessToken(token string) error
	GetTelegramUpdate() (*model.TelegramResponse, error)
}

type telegramService struct {
	repo repository.TelegramRepository
}

func NewTelegramService(
	repo repository.TelegramRepository,
) TelegramService {
	return &telegramService{repo}
}

func (s telegramService) GetTelegramUpdate() (*model.TelegramResponse, error) {

	return cronReadTelegramUpdateList(s.repo)
}

func (s telegramService) GetTelegramAccessToken() (*model.TelegramAccessTokenResponse, error) {

	token, err := s.repo.GetTelegramAccessToken()
	if err != nil {
		return nil, internalServerError(err)
	}
	return token, nil
}

func (s telegramService) UpdateTelegramAccessToken(token string) error {

	if err := s.repo.UpdateTelegramAccessToken(token); err != nil {
		return internalServerError(err)
	}
	return nil
}

func cronReadTelegramUpdateList(repo repository.TelegramRepository) (*model.TelegramResponse, error) {

	lastestUpdateUid, err := repo.GetWebhookLastUpdateUid()
	if err != nil {
		return nil, internalServerError(err)
	}

	// ใช้อันเดียวไปก่อน
	accessToken, err := repo.GetTelegramAccessToken()
	if err != nil {
		log.Println("cronReadTelegramUpdateList.GetTelegramAccessToken err ------> ", err)
		return nil, internalServerError(err)
	}
	if accessToken.AccessToken == "" {
		log.Println("cronReadTelegramUpdateList.GetTelegramAccessToken AccessToken is empty")
		return nil, internalServerError(fmt.Errorf("TELEGRAM_TOKEN_NOT_SET"))
	}

	// fmt.Println("cronReadTelegramUpdateList.GetTelegramAccessToken ------> ", helper.StructJson(accessToken))
	if lastestUpdateUid != nil {
		log.Println("cronReadTelegramUpdateList.lastestUpdateUid ------> ", *lastestUpdateUid)
	} else {
		log.Println("cronReadTelegramUpdateList.lastestUpdateUid ------> ", "nil")
	}

	if lastestUpdateUid != nil {
		lastUpdateUid := int64(0)
		// Remote API call to get telegram updates
		// Plus 1 to get the next update, All previous updates will be forgotten.
		lastUpdateUid = *lastestUpdateUid + 1
		record, err := repo.GetTelegramUpdateList(accessToken.AccessToken, lastUpdateUid)
		if err != nil {
			log.Println("cronReadTelegramUpdateList.GetTelegramUpdateList err ------> ", err)
			return nil, internalServerError(err)
		}
		lastUpdateUid = 0 // reset
		if record != nil {
			for _, v := range record.Result {
				// log.Println("GetTelegramUpdateList res ------> ", helper.StructJson(v))
				if len(v.Message.Entities) > 0 {
					if v.Message.Entities[0].Type == "bot_command" {
						if v.Message.Text == "/chatid" {
							// Response Chat Id
							chatUid := v.Message.Chat.Id
							chatTitle := v.Message.Chat.Title
							updateuid := v.UpdateId
							// add chatid to chat_token then reply token to user
							token, err := repo.GetChatToken(updateuid, v)
							if err != nil {
								return nil, internalServerError(err)
							}
							if token != "" {
								var chatInfo model.TelegramChatTokenResponse
								chatInfo.ChatId = chatUid
								chatInfo.AccessToken = accessToken.AccessToken
								if _, err := repo.SendTelegramMessage(chatInfo, fmt.Sprintf("Your chat %s(%d) \n คัดลอก chat-token ด้านล่างไปกรอกในตั้งค่าระบบแจ้งเตือน \n%s", chatTitle, chatUid, token)); err != nil {
									return nil, internalServerError(err)
								}
							}
						}
					}
				}
				lastUpdateUid = v.UpdateId
			}
		}
		if lastUpdateUid > 0 {
			// Save last update id
			if err := repo.SaveWebhookLastUpdateUid(lastUpdateUid); err != nil {
				return nil, internalServerError(err)
			}
		}
	} else {
		log.Println("cronReadTelegramUpdateList.lastestUpdateUid ------> ", "nil")
	}

	return nil, nil
}
