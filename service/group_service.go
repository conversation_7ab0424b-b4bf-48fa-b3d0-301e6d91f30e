package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"fmt"
	"strings"
)

type GroupService interface {
	Create(user model.CreateGroup) error
}

type groupService struct {
	repo        repository.GroupRepository
	perRepo     repository.PermissionRepository
	adminAction AdminActionService
}

func NewGroupService(
	repo repository.GroupRepository,
	perRepo repository.PermissionRepository,
	adminAction AdminActionService,
) GroupService {
	return &groupService{repo, perRepo, adminAction}
}

func (s *groupService) Create(data model.CreateGroup) error {

	var group model.Group
	group.Name = data.Name

	perIdExists, err := s.perRepo.CheckPerListExist(data.PermissionIds)
	if err != nil {
		return internalServerError(err)
	}

	var idNotFound []string
	for _, j := range data.PermissionIds {

		exist := false

		for _, k := range perIdExists {
			if j == k {
				exist = true
			}
		}

		if !exist {
			idNotFound = append(idNotFound, fmt.Sprintf("%v", j))
		}
	}

	if len(idNotFound) > 0 {
		return badRequest(fmt.Sprintf("Permission id %s not found", strings.Join(idNotFound, ",")))
	}

	if err := s.repo.CreateGroup(group, data.PermissionIds); err != nil {
		return err
	}

	// [ADMIN_ACTION] SUCCESS เพิ่มกลุ่มผู้ใช้งานใหม่
	var adminActionCreateBody model.AdminActionCreateRequest
	adminActionCreateBody.AdminId = data.CreateBy
	adminActionCreateBody.TypeId = model.ADMIN_ACTION_GROUP_MANAGE
	adminActionCreateBody.Detail = fmt.Sprintf("เพิ่มกลุ่มผู้ใช้งานใหม่ %s", data.Name)
	adminActionCreateBody.JsonInput = helper.StructJson(data)
	// adminActionCreateBody.JsonOutput = helper.StructJson()
	if _, err := s.adminAction.CreateSuccessAdminAction(adminActionCreateBody); err != nil {
		return err
	}

	return nil
}
