package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"fmt"
	"log"
	"time"

	"gorm.io/gorm"
)

type AccountMoveService interface {
	GetFromAccountOptions() ([]model.SelectOptions, error)
	GetToAccountOptions() ([]model.SelectOptions, error)
	GetTransactionStatusOptions() ([]model.SelectOptions, error)
	GetAccountMoveAccountList(req model.AccountMoveAccountListRequest) (*model.SuccessWithPagination, error)
	GetAccountMoveTransactionById(req model.GetByIdRequest) (*model.AccountMoveTransactionResponse, error)
	GetAccountMoveTransactionStatusCount(req model.AccountMoveTransactionListRequest) (*model.AccountMoveTransactionStatusCount, error)
	GetAccountMoveTransactionList(req model.AccountMoveTransactionListRequest) (*model.SuccessWithPagination, error)
	CreateAccountMoveTransaction(req model.AccountMoveTransactionCreateRequest) (*int64, error)
	HookFastBankStatement(req model.AccountMoveWebhookCreateRequest) (*int64, error)
	GetAccountMoveStatementList(req model.AccountMoveStatementListRequest) (*model.SuccessWithPagination, error)
	GetAccountMoveLogList(req model.AccountMoveLogListRequest) (*model.SuccessWithPagination, error)
	// [ADMIN_LOG]
	LogAdmin(fn string, adminId int64, req interface{}) error
}

type accountMoveService struct {
	sharedDb          *gorm.DB
	repo              repository.AccountMoveRepository
	accountingService AccountingService
	adminAction       AdminActionService
}

func NewAccountMoveService(
	sharedDb *gorm.DB,
	repo repository.AccountMoveRepository,
	accountingService AccountingService,
	adminAction AdminActionService,
) AccountMoveService {
	return &accountMoveService{sharedDb, repo, accountingService, adminAction}
}

func (s *accountMoveService) GetFromAccountOptions() ([]model.SelectOptions, error) {

	list, err := s.repo.GetFromAccountOptions()
	if err != nil {
		return nil, internalServerError(err)
	}
	return list, nil
}

func (s *accountMoveService) GetToAccountOptions() ([]model.SelectOptions, error) {

	list, err := s.repo.GetToAccountOptions()
	if err != nil {
		return nil, internalServerError(err)
	}
	return list, nil
}

func (s *accountMoveService) GetTransactionStatusOptions() ([]model.SelectOptions, error) {

	list, err := s.repo.GetTransactionStatusOptions()
	if err != nil {
		return nil, internalServerError(err)
	}
	return list, nil
}

func (s *accountMoveService) GetAccountMoveAccountList(req model.AccountMoveAccountListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	list, total, err := s.repo.GetAccountMoveAccountList(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	return &model.SuccessWithPagination{
		List:  list,
		Total: total,
	}, nil
}

func (s *accountMoveService) GetAccountMoveTransactionById(req model.GetByIdRequest) (*model.AccountMoveTransactionResponse, error) {

	record, err := s.repo.GetAccountMoveTransactionById(req.Id)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(recordNotFound)
		}
		return nil, internalServerError(err)
	}
	return record, nil
}

func (s *accountMoveService) GetAccountMoveTransactionStatusCount(req model.AccountMoveTransactionListRequest) (*model.AccountMoveTransactionStatusCount, error) {

	data, err := s.repo.GetAccountMoveTransactionStatusCount(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	return data, nil
}

func (s *accountMoveService) GetAccountMoveTransactionList(req model.AccountMoveTransactionListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	list, total, err := s.repo.GetAccountMoveTransactionList(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	return &model.SuccessWithPagination{
		List:  list,
		Total: total,
	}, nil
}

func (s *accountMoveService) CreateAccountMoveTransaction(req model.AccountMoveTransactionCreateRequest) (*int64, error) {

	if req.Amount < 1 {
		return nil, badRequest("INVALID_TRANSFER_AMOUNT")
	}

	fromAccount, err := s.repo.GetFastBankAccountById(req.FromAccountId)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound("FROM_ACCOUNT_NOT_FOUND")
		}
		return nil, internalServerError(err)
	}
	if fromAccount.DeviceUid == "" || fromAccount.PinCode == "" || fromAccount.ExternalId == "" {
		return nil, badRequest("FROM_ACCOUNT_NOT_FASTBANK")
	}

	toAccount, err := s.repo.GetBankAccountById(req.ToAccountId)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound("TO_ACCOUNT_NOT_FOUND")
		}
		return nil, internalServerError(err)
	}
	if fromAccount.Id == toAccount.Id || fromAccount.AccountNumber == toAccount.AccountNumber {
		return nil, badRequest("CANT_NOT_BE_SAME_ACCOUNT")
	}

	transferAmount := req.Amount
	// if fromAccount.AccountBalance < transferAmount {
	// 	// [ADMIN_ACTION] ERROR
	// 	var adminActionCreateBody model.AdminActionCreateRequest
	// 	adminActionCreateBody.AdminId = req.AdminId
	// 	adminActionCreateBody.TypeId = model.ADMIN_ACTION_MOVE_ACCOUNT_MONEY
	// 	adminActionCreateBody.Detail = fmt.Sprintf("โอนเงินจากธนาคาร %s : %s ไปยัง %s : %s จำนวน %.2f บาท (ไม่สามารถทำรายการได้ในขณะนี้)", fromAccount.BankName, fromAccount.AccountNumber, toAccount.BankName, toAccount.AccountNumber, transferAmount)
	// 	adminActionCreateBody.JsonInput = helper.StructJson(req)
	// 	adminActionCreateBody.JsonOutput = helper.StructJson(fromAccount)
	// 	if _, err := s.adminAction.CreateFailedAdminAction(adminActionCreateBody); err != nil {
	// 		return nil, internalServerError(err)
	// 	}
	// 	return nil, badRequest("INSUFFICIENT_BALANCE")
	// }

	var createBody model.AccountMoveTransactionCreateBody
	createBody.AdminId = req.AdminId
	createBody.FromAccountId = fromAccount.Id
	createBody.ToAccountId = toAccount.Id
	createBody.Amount = transferAmount
	createBody.StatusId = model.ACCOUNT_MOVE_TRANS_PENDING
	createBody.Remark = req.Remark
	insertId, err := s.repo.CreateAccountMoveTransaction(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	transferAt := time.Now()
	// pin = Encrypt PIN ที่ได้จาก response ตอน crate หรือ update bankAccount
	// bankCode = code ธนาคารปลายทาง
	var transferRequest model.ExternalAccountMoveCreateRequest
	transferRequest.AccountFrom = fromAccount.AccountNumber
	transferRequest.AccountTo = toAccount.AccountNumber
	transferRequest.Amount = transferAmount
	transferRequest.BankCode = toAccount.BankCode
	transferRequest.Pin = fromAccount.PinCode
	transferResult, err := s.repo.TransferExternalSystemAccount(transferRequest)
	if err != nil {
		var updateBody model.AccountMoveTransactionUpdateBody
		updateBody.StatusId = model.ACCOUNT_MOVE_TRANS_FAIL
		updateBody.TransferAt = &transferAt
		// if transferResult != nil {
		// 	updateBody.JsonFastbankResp = helper.StructJson(transferResult)
		// } else {
		updateBody.JsonFastbankResp = helper.StructJson(err.Error())
		// }
		if err := s.repo.UpdateAccountMoveTransaction(*insertId, updateBody); err != nil {
			return nil, internalServerError(err)
		}
		// [ADMIN_ACTION] ERROR
		var adminActionCreateBody model.AdminActionCreateRequest
		adminActionCreateBody.AdminId = req.AdminId
		adminActionCreateBody.TypeId = model.ADMIN_ACTION_MOVE_ACCOUNT_MONEY
		adminActionCreateBody.RefObjectId = *insertId
		// โอนเงินจากธนาคาร KTB : ********** ไปยัง SCB : ********** จำนวน 5 บาท (ไม่สามารถทำรายการได้ในขณะนี้)
		adminActionCreateBody.Detail = fmt.Sprintf("โอนเงินจากธนาคาร %s : %s ไปยัง %s : %s จำนวน %.2f บาท (ไม่สามารถทำรายการได้ในขณะนี้)", fromAccount.BankName, fromAccount.AccountNumber, toAccount.BankName, toAccount.AccountNumber, transferAmount)
		adminActionCreateBody.JsonInput = helper.StructJson(req)
		adminActionCreateBody.JsonOutput = helper.StructJson(struct {
			errors     interface{}
			updateBody interface{}
		}{
			errors:     err.Error(),
			updateBody: updateBody,
		})
		if _, err := s.adminAction.CreateFailedAdminAction(adminActionCreateBody); err != nil {
			return nil, internalServerError(err)
		}
		return nil, internalServerError(err)
	}

	var updateBody model.AccountMoveTransactionUpdateBody
	updateBody.StatusId = model.ACCOUNT_MOVE_TRANS_SUCCESS
	updateBody.TransferAt = &transferAt
	updateBody.JsonFastbankResp = helper.StructJson(transferResult)
	updateBody.IsSuccess = true
	if err := s.repo.UpdateAccountMoveTransaction(*insertId, updateBody); err != nil {
		return nil, internalServerError(err)
	}

	// NOTIFY
	// [notify SUCCESS]
	var externalNoti model.NotifyExternalNotificationRequest
	notiRepo := repository.NewNotificationRepository(s.sharedDb)
	externalNoti.TypeNotify = model.MoveMoney
	externalNoti.TransId = insertId
	if err := ExternalNotification(notiRepo, externalNoti); err != nil {
		log.Println("FailedNotify", err)
	}

	// [ADMIN_ACTION] SUCCESS
	var adminActionCreateBody model.AdminActionCreateRequest
	adminActionCreateBody.AdminId = req.AdminId
	adminActionCreateBody.TypeId = model.ADMIN_ACTION_MOVE_ACCOUNT_MONEY
	adminActionCreateBody.RefObjectId = *insertId
	// โอนเงินจากธนาคาร SCB : ********** ไปยัง KTB : ********** จำนวน 5 บาท (สำเร็จ)
	adminActionCreateBody.Detail = fmt.Sprintf("โอนเงินจากธนาคาร %s : %s ไปยัง %s : %s จำนวน %.2f บาท (สำเร็จ)", fromAccount.BankName, fromAccount.AccountNumber, toAccount.BankName, toAccount.AccountNumber, transferAmount)
	adminActionCreateBody.JsonInput = helper.StructJson(req)
	adminActionCreateBody.JsonOutput = helper.StructJson(updateBody)
	if _, err := s.adminAction.CreateSuccessAdminAction(adminActionCreateBody); err != nil {
		return nil, internalServerError(err)
	}
	return insertId, nil
}

func (s *accountMoveService) HookFastBankStatement(req model.AccountMoveWebhookCreateRequest) (*int64, error) {

	var result int64

	debugReq := map[string]interface{}{
		"req": req,
	}

	// Account id
	systemAccount, err := s.repo.GetBankAccountById(req.AccountId)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(bankAccountNotFound)
		}
		return nil, internalServerError(err)
	}

	statement, statementErr := s.repo.GetBankStatementByExternalId(req.StatementId)

	// INIT LOG
	if err := s.repo.SuccessLog("HookFastBankStatement.REQUEST", debugReq, map[string]interface{}{
		"statement": statement,
	}); err != nil {
		log.Println("HookFastBankStatement.SuccessLog1.ERROR", err)
	}

	// ตรวจสอบว่ามี statement นี้อยู่แล้วหรือไม่
	if statementErr != nil {
		if statementErr.Error() == recordNotFound {
			// ยังไม่มีใน bank_statement
			log.Println("AddOldStatementToBankStatement, externalId: ", req.StatementId)
			debugReq["case"] = "AddOldStatementToBankStatement"

			// Recheck Time
			checkOfDate1, err1 := time.Parse("02-01-2006", req.OfDate)
			if err1 != nil {
				checkOfDate2, err2 := time.Parse("2006-01-02", req.OfDate)
				if err2 != nil {
					log.Println("ERROR, AddAccountStatementToWebhook.Parse.OfDate", err2.Error())
					return nil, internalServerError(err2)
				}
				req.OfDate = checkOfDate2.Format("2006-01-02")
			} else {
				req.OfDate = checkOfDate1.Format("2006-01-02")
			}

			var query model.ExternalStatementListRequest
			query.AccountNumber = systemAccount.AccountNumber
			query.OfDate = req.OfDate
			query.OfTime = req.OfTime // check time +7
			query.Page = 1
			query.Limit = 100
			records, err := s.repo.GetExternalAccountStatements(query)
			if err != nil {
				return nil, internalServerError(err)
			}
			isExistsInRemoteStatement := false
			for _, record := range records.Content {
				if record.Id == req.StatementId {
					log.Println("Found remote statement, externalId: ", req.StatementId)
					debugReq["remote_found"] = record
					isExistsInRemoteStatement = true
					if insertId, err := s.createWebHookFromNewStatementAndAuto(record, &req.AdminId); err == nil {
						result = *insertId
					} else {
						log.Println("ERROR, AddAccountStatementToWebhook.createWebHookFromNewStatement", err.Error())
						return nil, internalServerError(err)
					}
					break
				}
			}
			if !isExistsInRemoteStatement {
				log.Println("GetExternalAccountStatements", helper.StructJson(records))
				return nil, notFound("FASTBANK_STATEMENT_NOT_FOUND")
			}
			debugReq["case"] = "AddOldStatementToBankStatement"
		} else {
			log.Panicln("ERROR, AddAccountStatementToWebhook.GetBankStatementByExternalId", statementErr.Error())
			debugReq["result"] = result
		}
		// RE-GET
		statement2, statementErr2 := s.repo.GetBankStatementByExternalId(req.StatementId)
		if statementErr2 != nil {
			return nil, internalServerError(statementErr2)
		} else {
			statement = statement2
		}
	}

	// ตรวจสอบว่ามี bank_transaction นี้อยู่แล้วหรือไม่
	if _, err := s.repo.GetTransactionByStatementId(statement.Id); err != nil {
		if err.Error() == recordNotFound {
			log.Println("AddOldStatementToBankTransaction, statementId: ", statement.Id)
			debugReq["case"] = "AddNewStatementToBankStatement"

			var bodyCreateState model.BankStatementCreateBody
			bodyCreateState.Id = statement.Id
			bodyCreateState.AccountId = systemAccount.Id
			bodyCreateState.ExternalId = &req.StatementId
			bodyCreateState.StatementTypeId = statement.StatementTypeId
			bodyCreateState.Amount = statement.Amount
			bodyCreateState.FromBankId = statement.FromBankId
			bodyCreateState.FromAccountNumber = statement.FromAccountNumber
			bodyCreateState.Detail = statement.Detail
			bodyCreateState.TransferAt = statement.TransferAt
			bodyCreateState.StatementStatusId = model.STATEMENT_STATUS_PENDING

			if err := s.accountingService.MatchBankStatementFromFastbankStatementNotAuto(*systemAccount, bodyCreateState); err != nil {
				log.Println("ERROR, AddAccountStatementToWebhook.matchBankStatementFromExternalStatement", err.Error())
				// ERROR
				if err := s.repo.ErrorLog("AddAccountStatementToWebhook.ERROR", debugReq, err.Error()); err != nil {
					log.Println("HookFastBankStatement.ERROR.ErrorLog", err)
				}
				return nil, internalServerError(err)
			}

			// MAKE IT TO MATCH
		} else {
			log.Panicln("ERROR, AddAccountStatementToWebhook.GetBankTransactionByStatementId", err.Error())
			debugReq["case"] = "ERROR, AddAccountStatementToWebhook.GetBankTransactionByStatementId"
		}
	} else {
		log.Println("TRANSACTION_ALREADY_EXIST")
		debugReq["case"] = "TRANSACTION_ALREADY_EXIST"
	}

	// SUCCESS
	if err := s.repo.SuccessLog("AddAccountStatementToWebhook.RESULT", debugReq, statement); err != nil {
		log.Println("HookFastBankStatement.ERROR.SuccessLog", err)
	}
	return &result, nil
}

func (s *accountMoveService) createWebHookFromNewStatementAndAuto(record model.ExternalStatement, adminId *int64) (*int64, error) {

	var result int64

	var webhookData model.WebhookStatement
	webhookData.Id = record.Id
	// unused webhookData.CustomerId = systemAccount.CustomerId
	// unused webhookData.ClientId = systemAccount.ClientId
	// unused webhookData.ClientName = systemAccount.ClientName
	webhookData.BankAccountId = record.BankAccountId
	webhookData.BankCode = record.BankCode
	webhookData.Amount = record.Amount
	// ** must use +07:00
	dateTime, err := time.Parse("2006-01-02 15:04:05", record.DateTime)
	if err != nil {
		log.Println("Parse.dateTime", err)
		return nil, internalServerError(err)
	}
	webhookData.DateTime = dateTime.Add(time.Hour * -7) // ** must use +07:00
	// rawDateTime, err := time.Parse("2006-01-02T15:04:05+07:00", record.RawDateTime)
	// if err != nil {
	// 	log.Println("Parse.rawDateTime", err)
	// 	return nil, internalServerError(err)
	// }
	webhookData.RawDateTime = record.RawDateTime
	webhookData.Info = record.Info
	webhookData.ChannelCode = record.ChannelCode
	webhookData.ChannelDescription = record.ChannelDescription
	webhookData.TxnCode = record.TxnCode
	webhookData.TxnDescription = record.TxnDescription
	webhookData.Checksum = record.Checksum
	webhookData.IsRead = record.IsRead
	webhookData.CreatedDate = record.CreatedDate
	webhookData.UpdatedDate = record.UpdatedDate

	// AUTO MATCH
	// if err := s.accountingService.CreateBankStatementFromWebhookAndAuto(webhookData); err == nil {
	// 	result = record.Id
	// } else {
	// 	log.Println("ERROR, createWebHookFromExistsStatement.CreateBankStatementFromWebhook", err.Error())
	// 	return nil, internalServerError(err)
	// }

	// NOTAUTO
	// 2024/05/13 มีการปรับ จาก ไม่ auto ให้เป็น auto เหมือนเดิม หลังจาก คุยกับ P.Lay And P.Tula
	if insertId, err := s.accountingService.CreateBankStatementFromWebhookAndAuto(webhookData, adminId); err == nil {
		result = *insertId
	} else {
		log.Println("ERROR, createWebHookFromExistsStatement.CreateBankStatementFromWebhook", err.Error())
		return nil, internalServerError(err)
	}

	return &result, nil
}

func (s *accountMoveService) GetAccountMoveStatementList(req model.AccountMoveStatementListRequest) (*model.SuccessWithPagination, error) {

	var query model.ExternalAccountStatementListRequest
	fastBankAccount, err := s.repo.GetFastBankAccountById(req.AccountId)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound("FASTBANK_ACCOUNT_NOT_FOUND")
		}
		return nil, internalServerError(err)
	}
	query.AccountNumber = fastBankAccount.AccountNumber
	query.Page = req.Page - 1
	query.Size = req.Limit
	result, err := s.repo.GetExternalAccountStatementList(query)
	if err != nil {
		return nil, internalServerError(err)
	}

	// Flag IsHasWebhookStatement
	// use bulk query for performance
	statementIds := make([]int64, 0)
	for _, record := range result.Content {
		statementIds = append(statementIds, record.Id)
	}
	var queryExistStatement model.BankStatementExistsListRequest
	queryExistStatement.AccountId = req.AccountId
	queryExistStatement.ExternalIds = statementIds
	queryExistStatement.Page = 1
	queryExistStatement.Limit = 0
	statementList, _, err := s.repo.GetExistsBankStatementList(queryExistStatement)
	if err != nil {
		return nil, internalServerError(err)
	}
	// Set
	for i, record := range result.Content {
		for _, statement := range statementList {
			if record.Id == statement.ExternalId {
				result.Content[i].IsHasWebhookStatement = true
				break
			}
		}
	}

	return &model.SuccessWithPagination{
		List:  result.Content,
		Total: result.TotalElements,
	}, nil
}

func (s *accountMoveService) GetAccountMoveLogList(req model.AccountMoveLogListRequest) (*model.SuccessWithPagination, error) {

	var query model.ExternalAccountLogListRequest
	fastBankAccount, err := s.repo.GetFastBankAccountById(req.AccountId)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound("FASTBANK_ACCOUNT_NOT_FOUND")
		}
		return nil, internalServerError(err)
	}
	query.AccountNumber = fastBankAccount.AccountNumber
	query.Page = req.Page - 1
	query.Size = req.Limit

	result, err := s.repo.GetExternalAccountLogList(query)
	if err != nil {
		return nil, internalServerError(err)
	}
	return &model.SuccessWithPagination{
		List:  result.Content,
		Total: result.TotalElements,
	}, nil
}

func (s *accountMoveService) LogAdmin(name string, adminId int64, req interface{}) error {

	var createBody model.AdminLogCreateBody
	createBody.Name = name
	createBody.AdminId = adminId
	createBody.JsonReq = helper.StructJson(req)
	if _, err := s.repo.CreateAdminLog(createBody); err != nil {
		return internalServerError(err)
	}
	return nil
}
