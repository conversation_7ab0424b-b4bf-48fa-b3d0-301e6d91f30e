package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"errors"
	"fmt"
	"log"
	"math"
	"time"

	"gorm.io/gorm"
)

type AllianceService interface {
	AlSummary(query model.AllianceSummaryQuery) (*model.AlSummary, error)
	AlGetCommissionSettingUser(userId int64) (*model.AllianceCommissionSettingUser, error)
	AlGetTransactionDaily(userId int64, query model.AllianceTransactionDailyQuery) (*model.SuccessWithPagination, error)
	AlGetTransactionSummary(userId int64, query model.AllianceTransactionDailyQuery) (*model.AllianceTransaction, error)
	AlGetTransactionMember(userId int64, query model.AllianceTransactionMemberQuery) (*model.SuccessWithPagination, error)
	GetMemberTotalIncome(req model.AllianceMemberTotalIncomeListRequest) (*model.SuccessWithPagination, error)
	GetMemberTotalIncomeSummary(req model.AllianceMemberTotalIncomeListRequest) (*model.AllianceMemberTotalIncomeResponse, error)
	AlGetFirstDeposit(userId int64, query model.AllianceFirstDepositQuery) (*model.AllianceFirstDepositResponse, error)
	AlUpdateCommissionSetting(data model.AllianceCommissionBody) error
	AlUpdateCommissionSettingUser(userId int64, data model.AllianceCommissionSettingUpdateRequest) error
	AlDownSettingUserToAffiliate(userId int64) error
	AlUpdateCommissionFirstDeposit(userId int64, amount float64) error
	NoUseAlUpdateCommission(userId int64, amount float64) error
	// WithdrawCommission(userId int64, memberCode string) error
	GetFirstDepositAllianceList(HeadAllianceId int64, req model.GetFirstDepositAliianceListRequest) (*model.SuccessWithPagination, error)
	GetFirstDepositAllianceSummary(HeadAllianceId int64, req model.GetFirstDepositAliianceSummaryRequest) (*model.GetFirstDepositAllianceSummary, error)
	// Alliance-Report
	GetAllianceUserList(req model.AllianceUserListRequest) ([]model.AllianceUserResponse, int64, error)
	GetAllianceFirstDepositSummary(req model.AllianceFirstDepositListRequest) (*model.AllianceFirstDepositSummaryResponse, error)
	GetAllianceFirstDepositList(req model.AllianceFirstDepositListRequest) ([]model.AllianceUserFirstDepositResponse, int64, error)
	GetAllianceWinLoseHistory(req model.AllianceWinLoseHistoryListRequest) (*model.AllianceWinLoseHistoryResponseWithPagination, error)
	GetAllianceWinLoseSummary(query model.AllianceWinLoseHistoryListRequest) (*model.AllianceTotalWinLoseHistoryResponse, error)
	RacingWithdrawAllianceIncome(req model.AllianceIncomeWithdrawCreateRequest) (*int64, error)
	WithdrawAllianceIncome(req model.AllianceIncomeWithdrawCreateRequest) error
	ConfirmAllianceIncome(incomeId int64, adminId int64) error
	GetAllianceIncomeWithdrawHistory(query model.AllianceIncomeWithdrawHistoryListRequest) ([]model.AllianceIncomeWithdrawHistoryResponse, int64, error)
	GetAllianceBankTransaction(req model.AllianceBankTransactionListRequest) (*model.AllianceBankTransactionResponseWithPagination, error)
	GetAllianceBankTransactionSummary(query model.AllianceBankTransactionListRequest) (*model.AllianceBankTransactionResponse, error)

	GetAliasByRef(req model.GetAliasByUserIdRequest) (*model.GetAliasByUserIdResponse, error)
	// New-Alliance
	GetAllianceWinLoseList(query model.AllianceWinLoseTotalListRequest) ([]model.AllianceWinLoseTotalResponse, int64, error)
	// [ADMIN_LOG]
	LogAdmin(name string, adminId int64, req interface{}) error

	GetSumAllianceWinLoseTotal(req model.AllianceWinLoseSumTotalRequest) (*model.GetSumAllianceWinLoseTotalResponse, error)
}

type AllianceServiceRepos struct {
	repo    repository.AllianceRepository
	shareDb *gorm.DB
	agRepo  repository.AgentConnectRepository
}

func NewAllianceService(
	repo repository.AllianceRepository,
	shareDb *gorm.DB,
	agRepo repository.AgentConnectRepository,
) AllianceService {
	return &AllianceServiceRepos{repo, shareDb, agRepo}
}

func (s AllianceServiceRepos) AlSummary(query model.AllianceSummaryQuery) (*model.AlSummary, error) {

	data, err := s.repo.AlSummary(query)
	if err != nil {
		return nil, err
	}

	data.RecommendTotal = data.HaveMemberCode + data.NoMemberCode

	return data, nil
}

func (s AllianceServiceRepos) AlGetCommissionSettingUser(userId int64) (*model.AllianceCommissionSettingUser, error) {

	data, err := s.repo.AlGetCommissionSettingUser(userId)
	if err != nil {
		return nil, err
	}

	return data, nil
}

func (s AllianceServiceRepos) AlGetTransactionDaily(userId int64, query model.AllianceTransactionDailyQuery) (*model.SuccessWithPagination, error) {

	if err := helper.UnlimitPagination(&query.Page, &query.Limit); err != nil {
		return nil, err
	}

	list, total, err := s.repo.AlGetTransactionDaily(userId, query)
	if err != nil && err.Error() != "record not found" {
		return nil, err
	}

	var result model.SuccessWithPagination
	result.List = list
	result.Total = total

	return &result, nil
}

func (s AllianceServiceRepos) AlGetTransactionSummary(userId int64, query model.AllianceTransactionDailyQuery) (*model.AllianceTransaction, error) {

	record, err := s.repo.AlGetTransactionSummary(userId, query)
	if err != nil {
		return nil, err
	}
	return record, nil
}

func (s AllianceServiceRepos) AlGetTransactionMember(userId int64, query model.AllianceTransactionMemberQuery) (*model.SuccessWithPagination, error) {

	if err := helper.Pagination(&query.Page, &query.Limit); err != nil {
		return nil, err
	}

	list, total, err := s.repo.AlGetTransactionMember(userId, query)
	if err != nil && err.Error() != "record not found" {
		return nil, err
	}

	// censor phone
	for i, v := range list {
		list[i].Phone = helper.CensorPhone(v.Phone)
	}

	var resp model.SuccessWithPagination
	resp.List = list
	resp.Total = total

	return &resp, nil
}

func (s AllianceServiceRepos) GetMemberTotalIncome(req model.AllianceMemberTotalIncomeListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, err
	}

	list, total, err := s.repo.GetMemberTotalIncome(req)
	if err != nil {
		return nil, err
	}
	return &model.SuccessWithPagination{List: list, Total: total}, nil
}

func (s AllianceServiceRepos) GetMemberTotalIncomeSummary(req model.AllianceMemberTotalIncomeListRequest) (*model.AllianceMemberTotalIncomeResponse, error) {

	record, err := s.repo.GetMemberTotalIncomeSummary(req)
	if err != nil {
		return nil, err
	}
	return record, nil
}

func (s AllianceServiceRepos) AlGetFirstDeposit(userId int64, query model.AllianceFirstDepositQuery) (*model.AllianceFirstDepositResponse, error) {

	if err := helper.Pagination(&query.Page, &query.Limit); err != nil {
		return nil, err
	}

	list, total, err := s.repo.AlGetFirstDeposit(userId, query)
	if err != nil && err.Error() != "record not found" {
		return nil, err
	}

	var result model.AllianceFirstDepositResponse
	result.Result = list
	result.Total = total

	return &result, nil
}

func (s AllianceServiceRepos) AlUpdateCommissionSetting(data model.AllianceCommissionBody) error {

	if err := s.repo.AlUpdateCommissionSetting(data); err != nil {
		return err
	}

	return nil
}

func (s AllianceServiceRepos) AlUpdateCommissionSettingUser(userId int64, data model.AllianceCommissionSettingUpdateRequest) error {

	if err := s.repo.AlUpdateCommissionSettingUser(userId, data); err != nil {
		return err
	}
	return nil
}

func (s AllianceServiceRepos) AlDownSettingUserToAffiliate(userId int64) error {

	if err := s.repo.AlDownSettingUserToAffiliate(userId); err != nil {
		return err
	}
	return nil
}

func (s AllianceServiceRepos) AlUpdateCommissionFirstDeposit(userId int64, amount float64) error {

	refId, err := s.repo.AlGetRefIdByUserId(userId)
	if err != nil {
		return internalServerError(err)
	}

	if refId < 1 {
		return nil
	}

	if err := s.repo.AlUpdateCommissionFirstDeposit(userId, refId, amount); err != nil {
		return internalServerError(err)
	}

	return nil
}

func AlUpdateCommissionFirstDeposit(repo repository.AllianceRepository, userId int64, amount float64) error {

	refId, err := repo.AlGetRefIdByUserId(userId)
	if err != nil {
		return internalServerError(err)
	}

	if refId < 1 {
		return nil
	}

	if err := repo.AlUpdateCommissionFirstDeposit(userId, refId, amount); err != nil {
		return internalServerError(err)
	}

	return nil
}

func (s AllianceServiceRepos) NoUseAlUpdateCommission(userId int64, amount float64) error {

	// refId, err := s.repo.AlGetRefIdByUserId(userId)
	// if err != nil {
	// 	return internalServerError(err)
	// }

	// if refId < 1 {
	// 	return nil
	// }

	// commissionSettingUser, err := s.repo.AlGetCommissionSettingUser(userId)
	// if err != nil {
	// 	return internalServerError(err)
	// }

	// if commissionSettingUser == nil || commissionSettingUser.CommissionPercent < 1 {
	// 	return nil
	// }

	// var commission float64

	// commission = amount * commissionSettingUser.CommissionPercent / 100
	// if commission < 1 {
	// 	return nil
	// }

	// commission = math.Floor(commission)

	// if err := s.repo.AlUpdateCommission(refId, commission); err != nil {
	// 	return internalServerError(err)
	// }

	return nil
}

func (s AllianceServiceRepos) GetFirstDepositAllianceList(HeadAllianceId int64, req model.GetFirstDepositAliianceListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.Pagination(&req.Page, &req.Limit); err != nil {
		return nil, err
	}

	list, total, err := s.repo.GetFirstDepositAllianceList(HeadAllianceId, req)
	if err != nil {
		return nil, err
	}

	// censor phone
	for i, v := range list {
		list[i].Phone = helper.CensorPhone(v.Phone)
	}

	var result model.SuccessWithPagination
	result.List = list
	result.Total = *total

	return &result, nil
}

func (s AllianceServiceRepos) GetFirstDepositAllianceSummary(HeadAllianceId int64, req model.GetFirstDepositAliianceSummaryRequest) (*model.GetFirstDepositAllianceSummary, error) {

	data, err := s.repo.GetFirstDepositAllianceSummary(HeadAllianceId, req)
	if err != nil {
		return nil, err
	}

	data.RecommendTotal = data.HaveMemberCode + data.NoMemberCode

	return data, nil
}

func (s AllianceServiceRepos) GetAllianceUserList(query model.AllianceUserListRequest) ([]model.AllianceUserResponse, int64, error) {

	if err := helper.UnlimitPagination(&query.Page, &query.Limit); err != nil {
		return nil, 0, err
	}

	list, total, err := s.repo.GetAllianceUserList(query)
	if err != nil {
		return nil, 0, err
	}
	return list, total, nil
}

func (s AllianceServiceRepos) GetAllianceFirstDepositSummary(req model.AllianceFirstDepositListRequest) (*model.AllianceFirstDepositSummaryResponse, error) {

	summary, err := s.repo.GetAllianceFirstDepositSummary(req)
	if err != nil {
		return nil, err
	}
	return summary, nil
}

func (s AllianceServiceRepos) GetAllianceFirstDepositList(req model.AllianceFirstDepositListRequest) ([]model.AllianceUserFirstDepositResponse, int64, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, 0, err
	}

	list, total, err := s.repo.GetAllianceFirstDepositList(req)
	if err != nil {
		return nil, 0, err
	}
	return list, total, nil
}

func (s AllianceServiceRepos) GetAllianceWinLoseHistory(query model.AllianceWinLoseHistoryListRequest) (*model.AllianceWinLoseHistoryResponseWithPagination, error) {

	var result model.AllianceWinLoseHistoryResponseWithPagination

	refUser, err := s.repo.GetUserById(query.RefUserId)
	if err != nil {
		return nil, err
	}
	result.MemberCode = *refUser.MemberCode

	if err := helper.UnlimitPagination(&query.Page, &query.Limit); err != nil {
		return nil, err
	}
	list, total, err := s.repo.GetAllianceWinLoseHistory(query)
	if err != nil {
		return nil, err
	}
	result.List = list
	result.Total = total

	return &result, nil
}

func (s AllianceServiceRepos) GetAllianceWinLoseSummary(query model.AllianceWinLoseHistoryListRequest) (*model.AllianceTotalWinLoseHistoryResponse, error) {

	record, err := s.repo.GetAllianceWinLoseSummary(query)
	if err != nil {
		return nil, err
	}
	return record, nil
}

func (s AllianceServiceRepos) RacingWithdrawAllianceIncome(req model.AllianceIncomeWithdrawCreateRequest) (*int64, error) {

	actionAt := time.Now()

	var createBody model.RaceActionCreateBody
	createBody.Name = "โยกเคลียร์ยอดรายได้พันธมิตร"
	createBody.JsonRequest = helper.StructJson(req)
	createBody.Status = "PENDING"

	// KEY = WITHDRAW_ALLIANCE_INCOME_{total_second}_{ref_user_id}
	timing := actionAt.Format("200601021504")
	// only one action per five second
	timing += fmt.Sprintf("%02d", int64(math.Floor(float64(actionAt.Second()/5.0)))*5)

	createBody.ActionKey = fmt.Sprintf("WITHDRAW_ALLIANCE_INCOME_%s_%d", timing, req.RefUserId)
	createBody.UnlockAt = actionAt.Add(time.Second * 1)
	if _, err := s.repo.GetRaceActionIdByActionKey(createBody.ActionKey); err != nil {
		log.Println("RacingWithdrawAllianceIncome.ERROR.GetRaceActionByActionKey", err)
		if err != gorm.ErrRecordNotFound {
			return nil, internalServerError(errors.New("CANNOT_CHECK_ACTION"))
		}
	} else {
		return nil, internalServerError(errors.New("WORK_IN_ACTION"))
	}

	// Go for create, not check exists
	actionId, err := s.repo.CreateRaceCondition(createBody)
	if err != nil {
		log.Println("RacingWithdrawAllianceIncome.ERROR.CreateRaceCondition", err)
		return nil, internalServerError(errors.New("CANNOT_CREATE_ACTION"))
	}

	return &actionId, nil
}

func (s AllianceServiceRepos) WithdrawAllianceIncome(req model.AllianceIncomeWithdrawCreateRequest) error {

	actionAt := time.Now()

	// flow validate and sum income to be withdraw
	var query model.AllianceWinLoseHistoryListRequest
	query.RefUserId = req.RefUserId
	query.FromDate = req.FromDate
	query.ToDate = req.ToDate

	var pendingIncomeIds []int64
	var summaryData model.AllianceTotalWinLoseHistoryResponse
	summaryData.RefUserId = req.RefUserId
	summaryData.FromDate = req.FromDate
	summaryData.ToDate = req.ToDate
	summaryData.TotalPlayAmount = 0
	summaryData.TotalWinLoseAmount = 0
	summaryData.TotalCommission = 0
	summaryData.AllianceWinloseAmount = 0
	summaryData.AllianceCommission = 0
	summaryData.TotalPaidBonus = 0
	summaryData.AlliancePaidBonus = 0
	summaryData.AllianceIncome = 0
	summaryData.AlliancePendingIncome = 0

	list, total, err := s.repo.GetAllianceWinloseIncomeList(query)
	if err != nil {
		return err
	}
	if total < 1 {
		return errors.New("NO_INCOME_TRANSACTION_TO_WITHDRAW")
	}

	for _, row := range list {
		if row.TakeAt == nil {
			summaryData.TotalPlayAmount += row.TotalPlayAmount
			summaryData.TotalWinLoseAmount += row.TotalWinLoseAmount
			summaryData.TotalCommission += row.TotalCommission
			summaryData.AllianceWinloseAmount += row.AllianceWinloseAmount
			summaryData.AllianceCommission += row.AllianceCommission
			summaryData.TotalPaidBonus += row.TotalPaidBonus
			summaryData.AlliancePaidBonus += row.AlliancePaidBonus
			summaryData.AllianceIncome += row.AllianceIncome
			summaryData.AlliancePendingIncome += row.AllianceIncome
			pendingIncomeIds = append(pendingIncomeIds, row.Id)
		}
	}
	if len(pendingIncomeIds) < 1 {
		return errors.New("NO_PENDIND_TRANSACTION_TO_WITHDRAW")
	}

	if math.Round(summaryData.AllianceIncome*100) != math.Round(req.IncomeAmount*100) {
		log.Printf("INCOME_AMOUNT_NOT_MATCH: %f != %f", summaryData.AllianceIncome, req.IncomeAmount)
		return errors.New("INCOME_AMOUNT_NOT_MATCH")
	}

	// CreatedBy - Admin
	admin, err := s.repo.GetAdminById(req.CreatedBy)
	if err != nil {
		return err
	}
	// Update IncomeList Status
	if err := s.repo.UpdateAllianceIncomeTakenList(pendingIncomeIds); err != nil {
		return err
	}
	// Save Income Log
	var createBody model.AllianceIncomeWithdrawCreateBody
	createBody.RefUserId = req.RefUserId
	createBody.FromDate = req.FromDate
	createBody.ToDate = req.ToDate
	createBody.IncomeAmount = summaryData.AllianceIncome
	createBody.CreatedBy = admin.Id
	if _, err := s.repo.CreateWithdrawAllianceIncomeLog(createBody); err != nil {
		return err
	}

	// [UserIncome]
	var incomeLogCreateBody model.UserIncomeLogCreateBody
	incomeLogCreateBody.UserId = req.RefUserId
	incomeLogCreateBody.TypeId = model.USER_INCOME_TYPE_ALLIANCE
	incomeLogCreateBody.Detail = fmt.Sprintf("แจกโบนัสรายได้พันธมิตร %s - %s", summaryData.FromDate, summaryData.ToDate)
	incomeLogCreateBody.CreditAmount = summaryData.AllianceIncome
	incomeLogCreateBody.CreditAfter = 0
	incomeLogCreateBody.TransferAt = &actionAt
	incomeLogCreateBody.StatusId = model.USER_INCOME_STATUS_PENDING // เพิ่มเงื่อนไข ถ้าเป็นโอนเครดิต ให้เป็น confirm ทันที
	incomeLogCreateBody.CreateBy = admin.Id
	incomeLogCreateBody.CreateByName = admin.Fullname
	incomeLogId, err := s.repo.CreateUserIncomeLog(incomeLogCreateBody)
	if err != nil {
		return err
	}

	// [UserIncome]
	var confirmIncomeBody model.UserIncomeLogConfirmBody
	confirmIncomeBody.Id = *incomeLogId
	confirmIncomeBody.CreditAfter = 0
	// Auto Confirm By Admin - Admin is creater
	confirmIncomeBody.TransferAt = actionAt
	confirmIncomeBody.ConfirmBy = admin.Id
	confirmIncomeBody.ConfirmByName = admin.Fullname

	// f

	// [********] โยกเคลียร์ยอดพันธมิตร แบบโอนเครดิตได้ด้วย
	if req.TransferType == "credit" {
		var userCreditReq model.UserTransactionCreateRequest
		userCreditReq.RefId = incomeLogId
		userCreditReq.UserId = req.RefUserId // หัว
		userCreditReq.TransferAt = &actionAt
		userCreditReq.TypeId = model.CREDIT_TYPE_ALLIANCE_INCOME
		userCreditReq.BonusAmount = summaryData.AllianceIncome // [20240104] change from creditAmount to bonusAmount
		IsShow := true
		userCreditReq.IsShow = &IsShow
		userCreditReq.ConfirmBy = &admin.Id
		if agentResp, err := s.repo.IncreaseUserCredit(userCreditReq); err != nil {
			log.Println("WithdrawAllianceIncome.IncreaseUserCredit", err)
			return internalServerError(err)
		} else if agentResp.AgentSuccess {
			confirmIncomeBody.CreditAfter = agentResp.AgentAfterAmount
		} else {
			agentFail := errors.New("ทำรายการเครดิตไม่สำเร็จ")
			log.Println("WithdrawAllianceIncome.IncreaseUserCredit", agentFail)
			return internalServerError(agentFail)
		}
	}

	if err := s.repo.ConfirmUserIncomeLog(confirmIncomeBody); err != nil {
		// later : handle error ?
		return internalServerError(err)
	}

	// AddTo BankPendingRecord -> รายได้พันธมิตร ** ไม่มี **
	// dashboardRepo := repository.NewDashboardRepository(s.shareDb)
	// if err := ConfirmBankPendingRecordFromAny(dashboardRepo, model.BANK_PENDING_RECORD_TYPE_ALLIANCE_INCOME, income.Id, actionAt, admin.Username); err != nil {
	// 	log.Println("WithdrawAllianceIncome.ConfirmBankPendingRecordFromAny", err)
	// }

	return nil
}

func (s AllianceServiceRepos) ConfirmAllianceIncome(incomeId int64, adminId int64) error {

	actionAt := time.Now()

	income, err := s.repo.GetUserIncomeLogById(incomeId)
	if err != nil {
		return err
	}
	if income.StatusId != model.USER_INCOME_STATUS_PENDING {
		return errors.New("INCOME_STATUS_NOT_PENDING")
	}

	// Confirm By Admin
	// admin, err := s.repo.GetAdminById(adminId)
	// if err != nil {
	// 	log.Println("ConfirmAllianceIncome.GetAdminById", err)
	// 	return err
	// }

	// [UserIncome]
	var confirmIncomeBody model.UserIncomeLogConfirmBody
	confirmIncomeBody.Id = income.Id
	confirmIncomeBody.CreditAfter = 0
	// Auto Confirm By Admin - Admin is creater
	confirmIncomeBody.TransferAt = actionAt
	confirmIncomeBody.ConfirmBy = 0
	confirmIncomeBody.ConfirmByName = "System"

	// [********] โยกเคลียร์ยอดพันธมิตร แบบโอนเครดิตได้ด้วย

	var userCreditReq model.UserTransactionCreateRequest
	userCreditReq.RefId = &income.Id
	userCreditReq.UserId = income.UserId
	userCreditReq.TransferAt = &actionAt
	userCreditReq.TypeId = model.CREDIT_TYPE_ALLIANCE_INCOME
	userCreditReq.BonusAmount = income.CreditAmount // [20240104] change from creditAmount to bonusAmount
	IsShow := true
	userCreditReq.IsShow = &IsShow
	// userCreditReq.ConfirmBy = &admin.Id
	if agentResp, err := s.repo.IncreaseUserCredit(userCreditReq); err != nil {
		log.Println("ConfirmAllianceIncome.IncreaseUserCredit", err)
		return internalServerError(err)
	} else if agentResp.AgentSuccess {
		confirmIncomeBody.CreditAfter = agentResp.AgentAfterAmount
	} else {
		agentFail := errors.New("ทำรายการเครดิตไม่สำเร็จ")
		log.Println("ConfirmAllianceIncome.IncreaseUserCredit", agentFail)
		return internalServerError(agentFail)
	}

	if err := s.repo.ConfirmUserIncomeLog(confirmIncomeBody); err != nil {
		log.Println("ConfirmAllianceIncome.ConfirmUserIncomeLog", err)
		return internalServerError(err)
	}

	// AddTo BankPendingRecord -> รายได้พันธมิตร ** ไม่มี **
	// dashboardRepo := repository.NewDashboardRepository(s.shareDb)
	// if err := ConfirmBankPendingRecordFromAny(dashboardRepo, model.BANK_PENDING_RECORD_TYPE_ALLIANCE_INCOME, income.Id, actionAt, admin.Username); err != nil {
	// 	log.Println("ConfirmAllianceIncome.ConfirmBankPendingRecordFromAny", err)
	// }

	return nil
}

func (s AllianceServiceRepos) GetAllianceIncomeWithdrawHistory(query model.AllianceIncomeWithdrawHistoryListRequest) ([]model.AllianceIncomeWithdrawHistoryResponse, int64, error) {

	if err := helper.UnlimitPagination(&query.Page, &query.Limit); err != nil {
		return nil, 0, err
	}

	list, total, err := s.repo.GetAllianceIncomeWithdrawHistory(query)
	if err != nil {
		return nil, 0, err
	}
	return list, total, nil
}

func (s AllianceServiceRepos) GetAllianceBankTransaction(query model.AllianceBankTransactionListRequest) (*model.AllianceBankTransactionResponseWithPagination, error) {

	var result model.AllianceBankTransactionResponseWithPagination

	refUser, err := s.repo.GetUserById(query.RefUserId)
	if err != nil {
		return nil, err
	}
	result.MemberCode = *refUser.MemberCode

	if err := helper.UnlimitPagination(&query.Page, &query.Limit); err != nil {
		return nil, err
	}

	list, total, err := s.repo.GetAllianceBankTransaction(query)
	if err != nil {
		return nil, err
	}
	result.List = list
	result.Total = total

	return &result, nil
}

func (s AllianceServiceRepos) GetAllianceBankTransactionSummary(query model.AllianceBankTransactionListRequest) (*model.AllianceBankTransactionResponse, error) {

	summary, err := s.repo.GetAllianceBankTransactionSummary(query)
	if err != nil {
		return nil, err
	}
	return summary, nil
}

func (s AllianceServiceRepos) GetUserIncomeLogList(query model.UserIncomeLogListRequest) ([]model.UserIncomeLogResponse, int64, error) {

	if err := helper.Pagination(&query.Page, &query.Limit); err != nil {
		return nil, 0, err
	}

	list, total, err := s.repo.GetUserIncomeLogList(query)
	if err != nil && err.Error() != "record not found" {
		return nil, 0, err
	}
	return list, total, nil
}

func (s AllianceServiceRepos) GetUserWinLoseSummaryList(query model.UserWinLoseSummaryListRequest) ([]model.UserWinLoseSummaryResponse, int64, error) {

	if err := helper.Pagination(&query.Page, &query.Limit); err != nil {
		return nil, 0, err
	}

	list, total, err := s.repo.GetUserWinLoseSummaryList(query)
	if err != nil && err.Error() != "record not found" {
		return nil, 0, err
	}
	return list, total, nil
}

func (s AllianceServiceRepos) GetAliasByRef(req model.GetAliasByUserIdRequest) (*model.GetAliasByUserIdResponse, error) {

	var result model.GetAliasByUserIdResponse

	// WebConfig - ShowWebAffName
	webConfig, err := s.repo.GetConfiguration()
	if err != nil {
		return nil, err
	}
	if !webConfig.ShowWebAffName {
		// fmt.Println("ShowWebAffName", helper.StructJson(webConfig))
		return &model.GetAliasByUserIdResponse{}, nil
	}

	if req.RefCode != "" {
		userAllianceInfo, err := s.repo.GetUserAllianceInfoByRefCode(req.RefCode)
		if err != nil {
			return &model.GetAliasByUserIdResponse{}, nil
		}
		result.Alias = userAllianceInfo.Alias
	} else if req.Ref != "" {
		// No Error for WEB
		alias, err := s.repo.GetAliasByRef(helper.DecodeData(req.Ref))
		if err != nil {
			return &model.GetAliasByUserIdResponse{}, nil
		}
		result.Alias = alias.Alias
	} else if req.SaleCode != "" {
		userAllianceInfo, err := s.repo.GetUserAllianceInfoByRefCode(req.SaleCode)
		if err != nil {
			return &model.GetAliasByUserIdResponse{}, nil
		}
		result.Alias = userAllianceInfo.Alias
	}

	return &result, nil
}

func (s AllianceServiceRepos) GetAllianceWinLoseList(query model.AllianceWinLoseTotalListRequest) ([]model.AllianceWinLoseTotalResponse, int64, error) {

	if err := helper.UnlimitPagination(&query.Page, &query.Limit); err != nil {
		return nil, 0, err
	}

	list, total, err := s.repo.GetAllianceWinLoseList(query)
	if err != nil {
		return nil, 0, err
	}
	return list, total, nil
}

func (s AllianceServiceRepos) LogAdmin(name string, adminId int64, req interface{}) error {

	var createBody model.AdminLogCreateBody
	createBody.Name = name
	createBody.AdminId = adminId
	createBody.JsonReq = helper.StructJson(req)
	if _, err := s.repo.CreateAdminLog(createBody); err != nil {
		return internalServerError(err)
	}
	return nil
}

func (s AllianceServiceRepos) GetSumAllianceWinLoseTotal(req model.AllianceWinLoseSumTotalRequest) (*model.GetSumAllianceWinLoseTotalResponse, error) {

	record, err := s.repo.GetSumAllianceWinLoseTotal(req)
	if err != nil {
		return nil, err
	}
	return record, nil
}
