package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"fmt"
	"log"
	"math"
	"os"
	"time"

	"gorm.io/gorm"
)

type PgHardService interface {
	// game list
	GetPgHardGameList() ([]model.GetPgHardGameListResponse, error)
	// operator preset settings
	GetPgHardOperatorPreset() (*model.GetPgOperatorPresetResponseList, error)
	// login game init
	GetPgHardGameSessionInit(reqBody model.GetPgHardGame) (map[string]interface{}, error)

	// callback
	CallBackPgHardCheckBalance(reqBody model.CallBackPgHardCheckBalanceRequest, headerKey string) (*model.CallBackPgHardCheckBalanceResponse, error)
	CallBackPgHardGameSettleBet(reqBody model.CallBackPgHardGameSettleBetRequest, headerKey string) (*model.CallBackPgHardGameSettleBetResponse, error)

	// test
	GetAgentPgHardCallback(req model.AgentPgHardCallbackSummaryRequest) ([]model.AgentPgHardCallbackSummary, error)

	// admin
	GetAgentPgHardSetting() (*model.GetAgentPgHardSettingResponse, error)
	UpdateAgentPgHardSetting(body model.UpdateAgentPgHardSetting) error
	AdminGetPgHardOperatorPreset() (*model.GetPgOperatorPresetResponseList, error)
	GetPgHardCallbackSummary(req model.PgHardCallbackSummaryRequest) ([]model.PgHardCallbackSummaryResponse, error)
}

type pgHardService struct {
	repo        repository.PgHardRepository
	sharedDb    *gorm.DB
	serviceGame GameService
}

func NewPgHardService(
	repo repository.PgHardRepository,
	sharedDb *gorm.DB,
	serviceGame GameService,
) PgHardService {
	return &pgHardService{repo, sharedDb, serviceGame}
}

func (s pgHardService) GetPgHardGameList() ([]model.GetPgHardGameListResponse, error) {

	getInternalAgentPgHardSetting, err := s.repo.GetInternalAgentPgHardSetting()
	if err != nil {
		return nil, err
	}

	if getInternalAgentPgHardSetting.ProgramAllowUse == "NOT_ALLOW_USE" || !getInternalAgentPgHardSetting.IsActive {
		return nil, badRequest("NOT_ALLOW_USE_PG_SETTING_PLEASE_CONTACT_ADMIN")
	}

	var urlDetail model.CallApiAgentPgHardDetail
	urlDetail.PgHardPrivateKey = getInternalAgentPgHardSetting.PgHardPrivateKey
	urlDetail.PgHardOperatorId = getInternalAgentPgHardSetting.PgHardOperatorId
	urlDetail.PgHardUrl = getInternalAgentPgHardSetting.PgHardUrl

	getGameList, err := s.repo.GetPgHardGameList(urlDetail)
	if err != nil {
		return nil, err
	}

	return getGameList, nil
}

func (s pgHardService) GetPgHardOperatorPreset() (*model.GetPgOperatorPresetResponseList, error) {

	s.repo.ClearCacheAgentPgHardSetting()
	getInternalAgentPgHardSetting, err := s.repo.GetInternalAgentPgHardSetting()
	if err != nil {
		return nil, err
	}

	// if getInternalAgentPgHardSetting.ProgramAllowUse == "NOT_ALLOW_USE" || !getInternalAgentPgHardSetting.IsActive {
	// 	return nil, badRequest("NOT_ALLOW_USE_PG_SETTING_PLEASE_CONTACT_ADMIN")
	// }

	var urlDetail model.CallApiAgentPgHardDetail
	urlDetail.PgHardPrivateKey = getInternalAgentPgHardSetting.PgHardPrivateKey
	urlDetail.PgHardOperatorId = getInternalAgentPgHardSetting.PgHardOperatorId
	urlDetail.PgHardUrl = getInternalAgentPgHardSetting.PgHardUrl

	return s.repo.GetPgHardOperatorPreset(urlDetail)
}

func (s pgHardService) GetPgHardGameSessionInit(reqBody model.GetPgHardGame) (map[string]interface{}, error) {

	fmt.Printf("START LOGIN GetPgHardGameSessionInit TIME : %s\n", time.Now().Format("2006-01-02 15:04:05.000"))
	getInternalAgentPgHardSetting, err := s.repo.GetInternalAgentPgHardSetting()
	if err != nil {
		return nil, err
	}

	var urlDetail model.CallApiAgentPgHardDetail
	urlDetail.PgHardPrivateKey = getInternalAgentPgHardSetting.PgHardPrivateKey
	urlDetail.PgHardOperatorId = getInternalAgentPgHardSetting.PgHardOperatorId
	urlDetail.PgHardUrl = getInternalAgentPgHardSetting.PgHardUrl

	if getInternalAgentPgHardSetting.ProgramAllowUse == "NOT_ALLOW_USE" || !getInternalAgentPgHardSetting.IsActive {
		return nil, badRequest("NOT_ALLOW_USE_PG_SETTING_PLEASE_CONTACT_ADMIN")
	}

	// get member code
	getMemberCode, err := s.repo.GetMemberCode(reqBody.UserId)
	if err != nil {
		return nil, err
	}

	fmt.Println("user membercode :", *getMemberCode)
	// 4bHD2yHvx5fxaXkUo4/CgDjKo2z89BOsCa9/oW+xpg==
	// ลอง แล้ว agent ไม่สามารถเข้าได้
	// userToken, err := helper.EncryptPgHard(*getMemberCode)
	// if err != nil {
	// 	return nil, err
	// }

	// login game init
	var loginGameInit model.GetPgHardGameSessionParams
	loginGameInit.OperatorId = getInternalAgentPgHardSetting.PgHardOperatorId
	// loginGameInit.UserToken = userToken
	loginGameInit.UserToken = *getMemberCode
	loginGameInit.GameId = reqBody.GameId
	presetID := getInternalAgentPgHardSetting.PgHardPresetId
	loginGameInit.PresetId = presetID
	loginGameInit.PoolId = ""
	loginGameInit.UsePool = false
	loginGameInit.HrefBackURL = getInternalAgentPgHardSetting.PgHardHrefBackUrl
	fmt.Println("loginGameInit", helper.StructJson(loginGameInit))
	loginResponse, err := s.repo.GetPgHardGameSessionInit(loginGameInit, urlDetail)
	if err != nil {
		return nil, err
	}
	fmt.Printf("END LOGIN GetPgHardGameSessionInit TIME : %s\n", time.Now().Format("2006-01-02 15:04:05.000"))

	return loginResponse, nil
}

func (s pgHardService) CallBackPgHardCheckBalance(reqBody model.CallBackPgHardCheckBalanceRequest, headerKey string) (*model.CallBackPgHardCheckBalanceResponse, error) {

	fmt.Printf("START CallBackPgHardCheckBalance TIME : %s\n", time.Now().Format("2006-01-02 15:04:05.000"))
	fmt.Println("REQ CallBackPgHardCheckBalance :", helper.StructJson(reqBody))

	getInternalAgentPgHardSetting, err := s.repo.GetInternalAgentPgHardSetting()
	if err != nil {
		return nil, err
	}

	if getInternalAgentPgHardSetting.ProgramAllowUse == "NOT_ALLOW_USE" || !getInternalAgentPgHardSetting.IsActive {
		return nil, badRequest("NOT_ALLOW_USE_PG_SETTING_PLEASE_CONTACT_ADMIN")
	}

	if headerKey != getInternalAgentPgHardSetting.PgHardPrivateKey {
		return nil, badRequest("INVALID_PG_HARD_PRIVATE_KEY")
	}

	// 4bHD2yHvx5fxaXkUo4/CgDjKo2z89BOsCa9/oW+xpg==
	// ลอง แล้ว agent ไม่สามารถเข้าได้
	// userToken, err := helper.DecryptPgHard(reqBody.UserToken)
	// if err != nil {
	// 	return nil, err
	// }
	getUserMemberCode, err := s.repo.GetUserByMemberCode(reqBody.UserToken)
	if err != nil {
		return nil, err
	}
	getUserDetail, err := GetUser(repository.NewUserRepository(s.sharedDb), getUserMemberCode.Id)
	if err != nil {
		return nil, err
	}

	var response model.CallBackPgHardCheckBalanceResponse
	response.Username = getUserDetail.MemberCode
	response.Currency = "thb"
	response.Balance = getUserDetail.Credit
	fmt.Printf("END CallBackPgHardCheckBalance TIME : %s\n", time.Now().Format("2006-01-02 15:04:05.000"))
	return &response, nil
}

func (s pgHardService) CallBackPgHardGameSettleBet(reqBody model.CallBackPgHardGameSettleBetRequest, headerKey string) (*model.CallBackPgHardGameSettleBetResponse, error) {

	fmt.Printf("START CallBackPgHardGameSettleBet TIME : %s\n", time.Now().Format("2006-01-02 15:04:05.000"))
	fmt.Println("user membercode :", reqBody.UserToken)
	fmt.Println("REQ CallBackPgHardGameSettleBet :", helper.StructJson(reqBody))
	// {
	// 	"operatorId": "4175bc01-99f8-4b79-9a46-5076a3cfd2f9",
	// 	"transactions": {
	// 	  "transactionId": "e249b7c9-07de-48e5-a231-bba811fb6131",
	// 	  "payoff": 0, //จ่าย
	// 	  "betAmount": 1 // แทง
	// 	},
	// 	"userToken": "zta68pk52001281",
	// 	"roundId": "181526817378983266859772",
	// 	"gameId": "1815268",
	// 	"gameStringId": "oishi-delights",
	// 	"gameName": "Oishi Delights"
	//   }

	// 4bHD2yHvx5fxaXkUo4/CgDjKo2z89BOsCa9/oW+xpg==
	// ลอง แล้ว agent ไม่สามารถเข้าได้
	// userToken, err := helper.DecryptPgHard(reqBody.UserToken)
	// if err != nil {
	// 	return nil, err
	// }

	getInternalAgentPgHardSetting, err := s.repo.GetInternalAgentPgHardSetting()
	if err != nil {
		return nil, err
	}

	if getInternalAgentPgHardSetting.ProgramAllowUse == "NOT_ALLOW_USE" || !getInternalAgentPgHardSetting.IsActive {
		return nil, badRequest("NOT_ALLOW_USE_PG_SETTING_PLEASE_CONTACT_ADMIN")
	}

	if headerKey != getInternalAgentPgHardSetting.PgHardPrivateKey {
		return nil, badRequest("INVALID_PG_HARD_PRIVATE_KEY")
	}

	getUserMemberCode, err := s.repo.GetUserByMemberCode(reqBody.UserToken)
	if err != nil {
		// 404 Error: ในกรณีที่ไม่มีข้อมูล User (User Not Found)
		return nil, badRequest("USER_NOT_FOUND")
	}

	getUserDetail, err := GetUser(repository.NewUserRepository(s.sharedDb), getUserMemberCode.Id)
	if err != nil {
		return nil, err
	}
	var createAgentPgHardCallbackBody model.CreateAgentPgHardCallbackBody
	createAgentPgHardCallbackBody.UserId = getUserMemberCode.Id
	createAgentPgHardCallbackBody.MemberCode = reqBody.UserToken
	createAgentPgHardCallbackBody.TransactionId = reqBody.Transactions.TransactionId
	if reqBody.Transactions.Payoff == reqBody.Transactions.BetAmount {
		// ถ้าเท่ากันจะไม่นับ เป็น turn จะให้เป็น 0 P.Lay confirm
		createAgentPgHardCallbackBody.Payoff = 0
		createAgentPgHardCallbackBody.BetAmount = 0
	} else {
		createAgentPgHardCallbackBody.Payoff = reqBody.Transactions.Payoff
		createAgentPgHardCallbackBody.BetAmount = reqBody.Transactions.BetAmount
	}
	createAgentPgHardCallbackBody.WinloseAmount = 0
	createAgentPgHardCallbackBody.Balance = getUserDetail.Credit
	createAgentPgHardCallbackBody.BeforeBalance = getUserDetail.Credit
	createAgentPgHardCallbackBody.AfterBalance = getUserDetail.Credit
	createAgentPgHardCallbackBody.RoundId = reqBody.RoundId
	createAgentPgHardCallbackBody.GameId = reqBody.GameId
	createAgentPgHardCallbackBody.GameStringId = reqBody.GameStringId
	createAgentPgHardCallbackBody.GameName = reqBody.GameName
	createAgentPgHardCallbackBody.Remark = "CREATE"
	createCallbackId, err := s.repo.CreateAgentPgHardCallback(createAgentPgHardCallbackBody)
	if err != nil {
		return nil, err
	}
	var updateAgentPgHardCallbackBody model.UpdateAgentPgHardCallbackBody
	updateAgentPgHardCallbackBody.Id = createCallbackId
	if getUserDetail.Credit < reqBody.Transactions.BetAmount {
		// 400 Error: ในกรณีที่ User มีเงินไม่พอ (Insufficient Funds)
		updateAgentPgHardCallbackBody.Remark = "Insufficient Funds"
		if err := s.repo.UpdateAgentPgHardCallback(updateAgentPgHardCallbackBody); err != nil {
			log.Println(err)
		}
		return nil, badRequest("Insufficient Funds")
	}

	// เจอ เคส agent ส่งมา "betAmount":3.0000000000000004 ทำให้ error
	playedAmount := reqBody.Transactions.Payoff - reqBody.Transactions.BetAmount
	// ถ้า 1-3.0000000000000004 = -2.0000000000000004 เลยต้องปัดเศษ
	playedAmount = math.Round(playedAmount*100) / 100
	fmt.Println("playedAmount", playedAmount)
	var currnecntCredit *model.UserTransactionCreateResponse
	beforeAmount := getUserDetail.Credit
	afterAmount := getUserDetail.Credit
	if playedAmount > 0 {
		var increaseUserCredit model.IncreaseUserCreditFromOtherAgentRequest
		increaseUserCredit.UserId = getUserMemberCode.Id
		increaseUserCredit.Amount = playedAmount
		currnecntCredit, err = s.repo.IncreaseUserCreditFromOtherAgent(increaseUserCredit)
		if err != nil {
			updateAgentPgHardCallbackBody.Remark = fmt.Sprintf("IncreaseUserCreditFromOtherAgent Error: %v", err)
			if err := s.repo.UpdateAgentPgHardCallback(updateAgentPgHardCallbackBody); err != nil {
				log.Println(err)
			}
			return nil, err
		}
		beforeAmount = currnecntCredit.AgentBeforeAmount
		afterAmount = currnecntCredit.AgentAfterAmount
	} else if playedAmount < 0 {
		var decreaseUserCredit model.DecreaseUserCreditFromOtherAgentRequest
		decreaseUserCredit.UserId = getUserMemberCode.Id
		decreaseUserCredit.Amount = -playedAmount // ต้องเป็นบวก
		currnecntCredit, err = s.repo.DecreaseUserCreditFromOtherAgent(decreaseUserCredit)
		if err != nil {
			updateAgentPgHardCallbackBody.Remark = fmt.Sprintf("DecreaseUserCreditFromOtherAgent Error: %v", err)
			if err := s.repo.UpdateAgentPgHardCallback(updateAgentPgHardCallbackBody); err != nil {
				log.Println(err)
			}
			return nil, err
		}
		beforeAmount = currnecntCredit.AgentBeforeAmount
		afterAmount = currnecntCredit.AgentAfterAmount
	}

	// update callback
	updateAgentPgHardCallbackBody.Remark = "SUCCESS"
	updateAgentPgHardCallbackBody.BeforeBalance = beforeAmount
	updateAgentPgHardCallbackBody.AfterBalance = afterAmount
	updateAgentPgHardCallbackBody.WinloseAmount = playedAmount
	updateAgentPgHardCallbackBody.IsSuccess = true
	if err := s.repo.UpdateAgentPgHardCallback(updateAgentPgHardCallbackBody); err != nil {
		log.Println(err)
	}

	var response model.CallBackPgHardGameSettleBetResponse
	response.Username = getUserDetail.MemberCode
	response.Currency = "thb"
	response.BalanceBefore = beforeAmount
	response.BalanceAfter = afterAmount
	fmt.Println("response", helper.StructJson(response))
	fmt.Printf("END CallBackPgHardGameSettleBet TIME : %s\n", time.Now().Format("2006-01-02 15:04:05.000"))
	return &response, nil
}

func (s pgHardService) GetAgentPgHardCallback(req model.AgentPgHardCallbackSummaryRequest) ([]model.AgentPgHardCallbackSummary, error) {
	return s.repo.GetAgentPgHardCallback(req)
}

func (s pgHardService) GetAgentPgHardSetting() (*model.GetAgentPgHardSettingResponse, error) {

	s.serviceGame.ClearGameCache()
	s.repo.ClearCacheAgentPgHardSetting()

	getInternalAgentPgHardSetting, err := s.repo.GetInternalAgentPgHardSetting()
	if err != nil {
		return nil, err
	}

	if getInternalAgentPgHardSetting.ProgramAllowUse == "NOT_ALLOW_USE" {
		// badRequest("กรุณาติดต่อเจ้าหน้าที่ ให้ set ระบบ")
		return nil, badRequest("NOT_ALLOW_USE_PG_SETTING_PLEASE_CONTACT_ADMIN")
	}

	// if getAgentPgHardSetting.PgHardPresetId != "" && getAgentPgHardSetting. != getInternalAgentPgHardSetting.PgHardPresetId {

	var getNotePreset string
	if getInternalAgentPgHardSetting.PgHardOperatorId != "" && getInternalAgentPgHardSetting.PgHardPrivateKey != "" && getInternalAgentPgHardSetting.PgHardPresetId != "" {
		getPgHardOperatorPreset, err := s.GetPgHardOperatorPreset()
		if err != nil {
			fmt.Println("GetPgHardOperatorPreset", err)
		}

		if getPgHardOperatorPreset != nil {
			if len(getPgHardOperatorPreset.Presets) > 0 {
				for _, v := range getPgHardOperatorPreset.Presets {
					if v.Id == getInternalAgentPgHardSetting.PgHardPresetId {
						getNotePreset = v.Note
						break
					}
				}
			}
		}
	}
	var response model.GetAgentPgHardSettingResponse
	response.Description = getNotePreset
	response.PgHardPrivateKey = getInternalAgentPgHardSetting.PgHardPrivateKey
	response.PgHardOperatorId = getInternalAgentPgHardSetting.PgHardOperatorId
	response.PgHardPresetId = getInternalAgentPgHardSetting.PgHardPresetId
	response.IsActive = getInternalAgentPgHardSetting.IsActive

	return &response, nil
}

func (s pgHardService) UpdateAgentPgHardSetting(body model.UpdateAgentPgHardSetting) error {
	s.serviceGame.ClearGameCache()
	s.repo.ClearCacheAgentPgHardSetting()

	// p.lay confirm But dev-web for testing will not work
	body.PgHardHrefBackUrl = "https://" + os.Getenv("DOMAIN_NAME")
	return s.repo.UpdateAgentPgHardSetting(body)
}

func (s pgHardService) AdminGetPgHardOperatorPreset() (*model.GetPgOperatorPresetResponseList, error) {

	s.repo.ClearCacheAgentPgHardSetting()
	getInternalAgentPgHardSetting, err := s.repo.GetInternalAgentPgHardSetting()
	if err != nil {
		return nil, err
	}

	if getInternalAgentPgHardSetting.PgHardOperatorId == "" && getInternalAgentPgHardSetting.PgHardPrivateKey == "" {
		return nil, badRequest("SET_OPERATOR_ID_AND_PRIVATE_KEY")
	}

	if getInternalAgentPgHardSetting.ProgramAllowUse == "NOT_ALLOW_USE" {
		return nil, badRequest("NOT_ALLOW_USE_PG_SETTING_PLEASE_CONTACT_ADMIN")
	}

	var urlDetail model.CallApiAgentPgHardDetail
	urlDetail.PgHardPrivateKey = getInternalAgentPgHardSetting.PgHardPrivateKey
	urlDetail.PgHardOperatorId = getInternalAgentPgHardSetting.PgHardOperatorId
	urlDetail.PgHardUrl = getInternalAgentPgHardSetting.PgHardUrl
	response, err := s.repo.GetPgHardOperatorPreset(urlDetail)
	if err != nil {
		return nil, badRequest("NOT_FOUND_PG_OPERATOR_PRESET_CHECK_PG_SETTING")
	}

	if response != nil && len(response.Presets) == 0 {
		return nil, badRequest("NO_PG_PRESET_FOUND")
	}

	return response, nil
}

func (s pgHardService) GetPgHardCallbackSummary(req model.PgHardCallbackSummaryRequest) ([]model.PgHardCallbackSummaryResponse, error) {

	getList, err := s.repo.GetPgHardCallbackSummary(req)
	if err != nil {
		return nil, err
	}

	var reqOldList model.AgentPgHardCallbackSummaryReportRequest
	reqOldList.DateFrom = req.DateFrom
	reqOldList.DateTo = req.DateTo
	oldList, err := s.repo.AgentPgHardCallbackSummaryReportList(reqOldList)
	if err != nil {
		return nil, err
	}

	// Step 1: Create a map of existing data using key "userId_statementDate"
	oldDataMap := make(map[string]model.AgentPgHardCallbackSummaryReportResponse)
	for _, old := range oldList {
		key := fmt.Sprintf("%d_%s", old.UserID, old.StatementDate)
		oldDataMap[key] = old
	}

	var createList []model.CreateAgentPgHardCallbackSummaryReport
	var updateList []model.UpdateAgentPgHardCallbackSummaryReport

	// Step 2: Loop through new data and decide create or update
	for _, newItem := range getList {
		statementDate := newItem.CreatedAt.Format("2006-01-02") // Ensure date is in YYYY-MM-DD format
		key := fmt.Sprintf("%d_%s", newItem.UserId, statementDate)
		// check old and new diff
		if newItem.BetAmount == oldDataMap[key].BetAmount &&
			newItem.Payoff == oldDataMap[key].Payoff &&
			newItem.WinloseAmount == oldDataMap[key].WinloseAmount &&
			newItem.BeforeBalance == oldDataMap[key].BeforeBalance &&
			newItem.AfterBalance == oldDataMap[key].AfterBalance {
			// Skip if no changes
			continue
		}

		if oldItem, exists := oldDataMap[key]; exists {
			// Prepare for update
			updateList = append(updateList, model.UpdateAgentPgHardCallbackSummaryReport{
				Id:            oldItem.Id,
				UserId:        newItem.UserId,
				StatementDate: statementDate,
				MemberCode:    newItem.MemberCode,
				BetAmount:     newItem.BetAmount,
				Payoff:        newItem.Payoff,
				WinloseAmount: newItem.WinloseAmount,
				BeforeBalance: newItem.BeforeBalance,
				AfterBalance:  newItem.AfterBalance,
			})
		} else {
			// Prepare for create
			createList = append(createList, model.CreateAgentPgHardCallbackSummaryReport{
				UserId:        newItem.UserId,
				StatementDate: statementDate,
				MemberCode:    newItem.MemberCode,
				BetAmount:     newItem.BetAmount,
				Payoff:        newItem.Payoff,
				WinloseAmount: newItem.WinloseAmount,
				BeforeBalance: newItem.BeforeBalance,
				AfterBalance:  newItem.AfterBalance,
			})
		}
	}

	// Step 3: Execute create and update operations
	if len(createList) > 0 {
		if err := s.repo.CreateAgentPgHardCallbackSummaryReport(createList); err != nil {
			return nil, err
		}
	}

	for _, updateItem := range updateList {
		if err := s.repo.UpdateAgentPgHardCallbackSummaryReport(updateItem); err != nil {
			return nil, err
		}
	}

	return getList, nil
}
