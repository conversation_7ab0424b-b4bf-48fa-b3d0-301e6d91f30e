package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"errors"
	"fmt"
	"log"
	"math"
	"strings"
	"time"

	"gorm.io/gorm"
)

type TurnoverService interface {
	GetExternalUserTurnover(query model.TurnoverUserCurrentAmountRequest) error
	CheckUserTurnover(userId int64) (*model.TurnoverUserCurrentResponse, error)
	GetUserTurnoverList(req model.TurnoverUserStatementListRequest) (*model.SuccessWithPagination, error)
	CreateUserTurnover(req model.TurnoverUserCreateRequest) (*int64, error)
	DecreaseUserTurnover(req model.TurnoverUserDecreaseRequest) error
	// todo TURNOVER ENDING SYSTEM
	SetWithdrawTurnover(req model.TurnoverUserWithdrawRequest) error
	CheckTurnSuccessOnThisDay(req model.CheckTurnSuccessOnThisDayRequest) (*model.CheckTurnSuccessOnThisDayResponse, error)
	// SETTING
	GetTurnoverSetting() (*model.TurnoverSettingResponse, error)
	SetTurnoverSetting(req model.TurnoverSettingUpdateRequest) error
	// NEW TURN LIST
	UserTurnOverPendingList(req model.GetUserTurnOverStartmentListRequest) (*model.GetUserTurnOverStartmentListResponse, error)
	TurnOverPendingList(req model.GetTurnOverStartmentListRequest) (*model.SuccessWithPagination, error)

	AvaliableGamePlayCheckTurnOver(req model.AvaliableGamePlayCheckTurnOverRequest) error
}

type turnoverService struct {
	repo repository.TurnoverRepository
}

func NewTurnoverService(
	repo repository.TurnoverRepository,
) TurnoverService {
	return &turnoverService{repo}
}

func (s *turnoverService) GetUserTurnoverList(req model.TurnoverUserStatementListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	list, total, err := s.repo.GetTurnoverUserStatementList(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	return &model.SuccessWithPagination{
		List:  list,
		Total: total,
	}, nil
}

func (s *turnoverService) CreateUserTurnover(req model.TurnoverUserCreateRequest) (*int64, error) {

	actionAt := time.Now()

	var createBody model.TurnoverUserStatementCreateBody
	createBody.UserId = req.UserId
	createBody.TypeId = req.TypeId
	createBody.RefTypeId = req.RefTypeId
	createBody.Name = req.Name
	createBody.PromotionName = req.PromotionName
	createBody.BonusAmount = req.BonusAmount
	createBody.StatusId = model.TURNOVER_STATEMENT_STATUS_PENDING
	createBody.StartTurnAmount = req.Amount
	createBody.StartTurnAt = &actionAt
	createBody.TotalTurnAmount = req.Amount

	insertId, err := s.repo.CreateTurnoverUserStatement(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}
	return insertId, nil
}

func (s *turnoverService) DecreaseUserTurnover(req model.TurnoverUserDecreaseRequest) error {

	isActive := true
	dbTurnList, _, err := s.repo.GetTurnoverUserStatementList(model.TurnoverUserStatementListRequest{
		UserId:   req.UserId,
		IsActive: &isActive,
		Page:     0,
		Limit:    0,
		SortCol:  "id",
		SortAsc:  "asc",
	})
	if err != nil {
		return internalServerError(err)
	}

	totalAmount := req.Amount
	if len(dbTurnList) > 0 && totalAmount > 0 {
		for _, item := range dbTurnList {
			if item.StatusId == model.TURNOVER_STATEMENT_STATUS_PENDING && item.TotalTurnAmount > 0 {
				if item.TotalTurnAmount > totalAmount {
					decreaseAmount := totalAmount
					totalLeftAmount := item.TotalTurnAmount - decreaseAmount
					// Update TurnoverStatement with left amount //
					var updateBody model.TurnoverUserStatementUpdateBody
					updateBody.TotalTurnAmount = &totalLeftAmount
					if err := s.repo.UpdateTurnoverUserStatement(item.Id, updateBody); err != nil {
						return internalServerError(err)
					}
					totalAmount = totalAmount - decreaseAmount
				} else {
					decreaseAmount := item.TotalTurnAmount
					totalLeftAmount := item.TotalTurnAmount - decreaseAmount
					// Update TurnoverStatement with Completed //
					var updateBody model.TurnoverUserStatementUpdateBody
					updateBody.TotalTurnAmount = &totalLeftAmount
					if totalLeftAmount == 0 {
						updateBody.StatusId = model.TURNOVER_STATEMENT_STATUS_COMPLETED
						actionAt := time.Now()
						updateBody.EndTurnAt = &actionAt
					}
					if err := s.repo.UpdateTurnoverUserStatement(item.Id, updateBody); err != nil {
						return internalServerError(err)
					}
					totalAmount = totalAmount - decreaseAmount
				}
			}
			// check totalAmount //
			if totalAmount <= 0 {
				break
			}
		}
		if totalAmount > 0 {
			// Recursion until totalAmount = 0 //
			var newReq model.TurnoverUserDecreaseRequest
			newReq.UserId = req.UserId
			newReq.Amount = totalAmount
			if err := s.DecreaseUserTurnover(newReq); err != nil {
				return internalServerError(err)
			}
		}
	}

	return nil
}

func (s *turnoverService) GetExternalUserTurnover(query model.TurnoverUserCurrentAmountRequest) error {

	user, err := s.repo.GetUserMemberInfoById(query.UserId)
	if err != nil {
		return err
	}
	query.PlayerName = *user.MemberCode

	data, err := s.repo.GetAgentPlayerTurnover(query)
	if err != nil {
		return err
	}

	if helper.StructJson(data.Error) != "0" {
		return badRequest(data.Message)
	}
	return nil
}

func (s *turnoverService) CheckUserTurnover(userId int64) (*model.TurnoverUserCurrentResponse, error) {

	var result model.TurnoverUserCurrentResponse
	result.IsHasTurnover = true

	// MOCK Allow All user as NO TURNOVER //
	user, err := s.repo.GetUserMemberInfoById(userId)
	if err != nil {
		return nil, err
	}
	result.UserId = user.Id
	result.MemberCode = *user.MemberCode
	result.IsHasTurnover = false
	return &result, nil
}

func (s *turnoverService) CheckUserTurnoverForReal(userId int64) (*model.TurnoverUserCurrentResponse, error) {

	var result model.TurnoverUserCurrentResponse
	result.IsHasTurnover = true

	user, err := s.repo.GetUserMemberInfoById(userId)
	if err != nil {
		return nil, err
	}
	result.UserId = user.Id
	result.MemberCode = *user.MemberCode

	// SUM Check real time use FirstTurnOverDate and TotalTurnAmount //
	startTurnAt, totalTurnAmount, err := s.repo.GetUserTurnoverInfo(user.Id)
	if err != nil {
		return nil, internalServerError(err)
	}
	result.TurnoverAmount = totalTurnAmount
	if startTurnAt != nil {
		var query model.TurnoverUserCurrentAmountRequest
		query.UserId = user.Id
		query.From = startTurnAt.Format("2006-01-02")
		query.To = time.Now().Format("2006-01-02")
		query.PlayerName = *user.MemberCode
		playerData, err := s.repo.GetAgentPlayerTurnover(query)
		if err != nil {
			return nil, err
		}
		if helper.StructJson(playerData.Error) != "0" {
			return nil, badRequest(playerData.Message)
		}
		result.FromDate = query.From
		result.ToDate = query.To
		result.PlayedAmount = playerData.TurnOver
		// check turn
		if result.TurnoverAmount >= result.PlayedAmount {
			result.IsHasTurnover = true
		} else {
			result.IsHasTurnover = false
		}
	} else {
		// later : what if db list FAILED ?? //
		result.IsHasTurnover = false
	}
	return &result, nil
}

func (s *turnoverService) NewFormularCheckUserTurnover(userId int64) (*model.TurnoverUserCurrentResponse, error) {

	var result model.TurnoverUserCurrentResponse
	result.IsHasTurnover = true

	user, err := s.repo.GetUserMemberInfoById(userId)
	if err != nil {
		return nil, err
	}
	result.UserId = user.Id
	result.MemberCode = *user.MemberCode

	//
	startTurnAt, totalTurnAmount, err := s.repo.GetUserTurnoverInfo(user.Id)
	if err != nil {
		return nil, internalServerError(err)
	}
	result.TurnoverAmount = totalTurnAmount
	if startTurnAt != nil {
		actionAt := time.Now()
		beforeWithdrawAmount := 0.0
		leftoverAmount := 0.0

		// Build Today WithdrawInfo
		_, err := s.getTodayWithdrawTurnoverInfo(*user, actionAt)
		if err != nil {
			if err.Error() != "record not found" {
				return nil, err
			}
		}
		// beforeWithdrawAmount = withdrawInfo.CurrentPlayingAmount
		// // todo get last + today exists
		// leftoverAmount = withdrawInfo.TotalLeftAmount

		// AgentInfo
		var query model.TurnoverUserCurrentAmountRequest
		query.UserId = user.Id
		// query.From = startTurnAt.Format("2006-01-02")
		query.From = actionAt.Format("2006-01-02") // today only
		query.To = actionAt.Format("2006-01-02")
		query.PlayerName = *user.MemberCode
		playerData, err := s.repo.GetAgentPlayerTurnover(query)
		if err != nil {
			return nil, err
		}
		if helper.StructJson(playerData.Error) != "0" {
			return nil, badRequest(playerData.Message)
		}
		result.FromDate = query.From
		result.ToDate = query.To
		result.PlayedAmount = playerData.TurnOver
		// SET_X
		realPlayAmount := (result.PlayedAmount - beforeWithdrawAmount) + leftoverAmount
		// check turn
		if result.TurnoverAmount <= realPlayAmount {
			result.IsHasTurnover = false
			// SET_X
			result.TotalLeftAmount = realPlayAmount - result.TurnoverAmount
			// SET_Y
			result.CurrentPlayingAmount = result.PlayedAmount
		} else {
			result.IsHasTurnover = true
			// SET_X
			result.TotalLeftAmount = (result.PlayedAmount - beforeWithdrawAmount) + leftoverAmount
			// SET_Y
			result.CurrentPlayingAmount = beforeWithdrawAmount
		}
	} else {
		// no data is no turnover
		result.IsHasTurnover = false
	}
	return &result, nil
}

func (s *turnoverService) getTodayWithdrawTurnoverInfo(user model.UserResponse, actionAt time.Time) (*model.TurnoverWithdrawLog, error) {

	// Today WithdrawInfo one LogKey per Day = {userId}_{of_date}
	logKey := fmt.Sprintf("%d_%s", user.Id, actionAt.Format("2006-01-02"))
	withdrawInfo, err := s.repo.GetTurnoverWithdrawLog(logKey)
	if err != nil {
		if err.Error() == "record not found" {
			// Transfer data from yesterday //
			lastLog, err := s.repo.GetUserTurnoverBeforeWithdrawInfo(user.Id, logKey)
			if err != nil {
				return nil, err
			}
			// ตัดยอดตอนเรียก
			// AgentInfo of Yesterday
			var query model.TurnoverUserCurrentAmountRequest
			query.UserId = user.Id
			query.From = lastLog.OfDate
			query.To = lastLog.OfDate
			query.PlayerName = *user.MemberCode
			playerData, err := s.repo.GetAgentPlayerTurnover(query)
			if err != nil {
				// todo TURNOVER ENDING SYSTEM {
				//   "message": "request frequency limit is 10 seconds (-15)",
				//   "data": null
				// }
				return nil, err
			}
			if helper.StructJson(playerData.Error) != "0" {
				return nil, badRequest(playerData.Message)
			}

			// Update LastLog // todo TURNOVER ENDING SYSTEM
			lastLog.LastTotalX = playerData.TurnOver

			// INSERT Daily WithdrawInfo //
			var createBody model.TurnoverWithdrawLogCreateBody
			createBody.UserId = user.Id
			createBody.LogKey = logKey
			createBody.LastTotalX = lastLog.LastTotalX
			if _, err := s.repo.CreateTurnoverUserWithdraw(createBody); err != nil {
				return nil, err
			}
			// reget //
			withdrawInfo, err = s.repo.GetTurnoverWithdrawLog(logKey)
			if err != nil {
				return nil, err
			}
		} else {
			return nil, err
		}
	}
	return withdrawInfo, nil
}

func (s *turnoverService) SetWithdrawTurnover(req model.TurnoverUserWithdrawRequest) error {

	turnoverInfo, err := s.CheckUserTurnover(req.UserId)
	if err != nil {
		return internalServerError(err)
	}
	fmt.Println(helper.StructJson(turnoverInfo))
	if turnoverInfo.IsHasTurnover {
		return badRequest("Can not withdraw")
	}

	isActive := true
	dbTurnList, _, err := s.repo.GetTurnoverUserStatementList(model.TurnoverUserStatementListRequest{
		UserId:   req.UserId,
		IsActive: &isActive,
		Page:     0,
		Limit:    0,
		SortCol:  "id",
		SortAsc:  "asc",
	})
	if err != nil {
		return internalServerError(err)
	}

	// totalAmount := req.Amount
	totalAmount := turnoverInfo.TurnoverAmount
	if len(dbTurnList) > 0 && totalAmount > 0 {
		for _, item := range dbTurnList {
			if item.StatusId == model.TURNOVER_STATEMENT_STATUS_PENDING && item.TotalTurnAmount > 0 {
				if item.TotalTurnAmount > totalAmount {
					decreaseAmount := totalAmount
					totalLeftAmount := item.TotalTurnAmount - decreaseAmount
					// Update TurnoverStatement with left amount //
					var updateBody model.TurnoverUserStatementUpdateBody
					updateBody.TotalTurnAmount = &totalLeftAmount
					if err := s.repo.UpdateTurnoverUserStatement(item.Id, updateBody); err != nil {
						return internalServerError(err)
					}
					totalAmount = totalAmount - decreaseAmount
				} else {
					decreaseAmount := item.TotalTurnAmount
					totalLeftAmount := item.TotalTurnAmount - decreaseAmount
					// Update TurnoverStatement with Completed //
					var updateBody model.TurnoverUserStatementUpdateBody
					updateBody.TotalTurnAmount = &totalLeftAmount
					if totalLeftAmount == 0 {
						updateBody.StatusId = model.TURNOVER_STATEMENT_STATUS_COMPLETED
						actionAt := time.Now()
						updateBody.EndTurnAt = &actionAt
					}
					if err := s.repo.UpdateTurnoverUserStatement(item.Id, updateBody); err != nil {
						return internalServerError(err)
					}
					totalAmount = totalAmount - decreaseAmount
				}
			}
			// check totalAmount //
			if totalAmount <= 0 {
				break
			}
		}
		if totalAmount > 0 {
			// Recursion until totalAmount = 0 //
			var newReq model.TurnoverUserDecreaseRequest
			newReq.UserId = req.UserId
			newReq.Amount = totalAmount
			if err := s.DecreaseUserTurnover(newReq); err != nil {
				return internalServerError(err)
			}
		}
	}

	// INSERT WithdrawInfo //
	// var createBody model.TurnoverWithdrawLogCreateBody
	// createBody.UserId = req.UserId
	// createBody.OfDate = turnoverInfo.ToDate
	// createBody.WithdrawPrice = req.Amount
	// createBody.CurrentPlayingAmount = turnoverInfo.CurrentPlayingAmount
	// createBody.TotalLeftAmount = turnoverInfo.TotalLeftAmount
	// if _, err := s.repo.CreateTurnoverUserWithdraw(createBody); err != nil {
	// 	return err
	// }
	return nil
}

func (s *turnoverService) CronCutTurnover() error {

	// CONFIG
	cronName := "TURNOVER_CUT"

	// useAction
	actionTime := time.Now()
	var createBody model.CronActionCreateBody
	createBody.Status = "PENDING"
	// Day by day at 11.00 to 23.59 Daily
	if actionTime.Hour() < 11 {
		return nil
	}
	// KEY = SYNC_PRORETURNLOSS_20060102_{hour}{minute}{second}
	createBody.ActionKey = fmt.Sprintf("%s_%s", cronName, actionTime.Format("20060102_150405"))
	createBody.UnlockAt = actionTime.Add(time.Minute * 1)
	if _, err := s.repo.GetCronActionByActionKey(createBody.ActionKey); err != nil {
		if err != gorm.ErrRecordNotFound {
			return nil
		}
	} else {
		return nil
	}

	// Go for create, not check exists
	actionId, err := s.repo.CreateCronAction(createBody)
	if err != nil {
		log.Println("CronCutTurnover.ERROR.CreateCronAction", err)
		return internalServerError(errors.New("WORK_IN_ACTION"))
	}

	debugResult := make(map[string]interface{}, 0)

	// =========== DO_WORK ===========

	// 1. GET turnovered user = 4

	// 2. GET AGENT per user = 4 * 10sec < 1 min

	// 3. DECREASE user turnover

	// =========== SUCCESS ===========
	if err := s.repo.SetSuccessCronAction(actionId, helper.StructJson(debugResult)); err != nil {
		log.Println("CronCutTurnover.ERROR.SetSuccessCronAction", err)
	}
	return nil
}

func (s *turnoverService) CheckTurnSuccessOnThisDay(req model.CheckTurnSuccessOnThisDayRequest) (*model.CheckTurnSuccessOnThisDayResponse, error) {
	response, err := s.repo.CheckTurnSuccessOnThisDay(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	return response, nil
}

func (s turnoverService) GetTurnoverSetting() (*model.TurnoverSettingResponse, error) {

	record, err := GetTurnoverSetting(s.repo)
	if err != nil {
		return nil, err
	}
	return record, nil
}

func GetTurnoverSetting(repo repository.TurnoverRepository) (*model.TurnoverSettingResponse, error) {

	record, err := repo.GetTurnoverSetting()
	if err != nil {
		if err.Error() == recordNotFound {
			// Create
			var createBody model.TurnoverSettingCreateBody
			createBody.TidturnManualBonusPercent = 100
			createBody.TidturnLinkRegisterPercent = 100
			createBody.TidturnFirstDepositPercent = 100
			createBody.TidturnAffCommissionPercent = 100
			createBody.TidturnReturnLossPercent = 100
			createBody.TidturnReturnTurnPercent = 100
			createBody.TidturnActivityDailyPercent = 100
			createBody.TidturnSuccessDepositPercent = 0
			createBody.TidturnActivityDailyV2Percent = 100

			if _, err := repo.CreateTurnoverSetting(createBody); err != nil {
				return nil, internalServerError(err)
			}
			// REGET
			record, err = repo.GetTurnoverSetting()
			if err != nil {
				return nil, internalServerError(err)
			}
		}
		return nil, internalServerError(err)
	}
	return record, nil
}

func (s turnoverService) SetTurnoverSetting(req model.TurnoverSettingUpdateRequest) error {

	setting, err := s.GetTurnoverSetting()
	if err != nil {
		return internalServerError(err)
	}

	var body model.TurnoverSettingUpdateBody
	body.TidturnManualBonusPercent = req.TidturnManualBonusPercent
	body.TidturnLinkRegisterPercent = req.TidturnLinkRegisterPercent
	body.TidturnFirstDepositPercent = req.TidturnFirstDepositPercent
	body.TidturnAffCommissionPercent = req.TidturnAffCommissionPercent
	body.TidturnReturnLossPercent = req.TidturnReturnLossPercent
	body.TidturnReturnTurnPercent = req.TidturnReturnTurnPercent
	body.TidturnActivityDailyPercent = req.TidturnActivityDailyPercent
	body.TidturnSuccessDepositPercent = req.TidturnSuccessDepositPercent
	body.TidturnActivityDailyV2Percent = req.TidturnActivityDailyV2Percent
	if err := s.repo.UpdateTurnoverSetting(setting.Id, body); err != nil {
		return internalServerError(err)
	}

	// [ADMIN_ACTION] SUCCESS แก้ไขข้อมูลผู้ใช้งาน {name}
	var adminActionCreateBody model.AdminActionCreateBody
	adminActionCreateBody.AdminId = req.UpdateBy
	adminActionCreateBody.TypeId = model.ADMIN_ACTION_MANAGE_TIDTURN
	adminActionCreateBody.Detail = "แก้ไขข้อมูล % เทิร์นโอเวอร์"
	adminActionCreateBody.IsSuccess = true
	adminActionCreateBody.IsShow = true
	adminActionCreateBody.JsonInput = helper.StructJson(setting)
	adminActionCreateBody.JsonOutput = helper.StructJson(req)
	if _, err := s.repo.CreateAdminAction(adminActionCreateBody); err != nil {
		return err
	}
	return nil
}

func (s turnoverService) UserTurnOverPendingList(req model.GetUserTurnOverStartmentListRequest) (*model.GetUserTurnOverStartmentListResponse, error) {

	return UserTurnOverPendingList(s.repo, req)

}

func UserTurnOverPendingList(repo repository.TurnoverRepository, req model.GetUserTurnOverStartmentListRequest) (*model.GetUserTurnOverStartmentListResponse, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	req.IsPending = "PENDING"
	pendingList, total, err := repo.GetUserTurnOverStartmentListPendingOnly(req)
	if err != nil {
		return nil, internalServerError(err)
	}

	if len(pendingList) == 0 {
		return &model.GetUserTurnOverStartmentListResponse{
			List:    []model.GetUserTurnOverStartmentList{},
			Total:   0,
			Message: "TRUNOVER_PENDING_LIST_SUCCESS",
		}, nil
	}

	// ให้เริ่มด้วยวันที่ ต่ำสุด
	var lowestTurnOverDate time.Time
	for _, item := range pendingList {
		if lowestTurnOverDate.IsZero() {
			lowestTurnOverDate = *item.StartTurnAt
		} else {
			if item.StartTurnAt.Before(lowestTurnOverDate) {
				lowestTurnOverDate = *item.StartTurnAt
			}
		}
	}

	fmt.Println("lowestTurnOverDate", lowestTurnOverDate)

	var completeListReq model.GetUserTurnOverStartmentListRequest
	completeListReq.UserId = req.UserId
	completeListReq.StartDate = lowestTurnOverDate.Add(time.Hour * 7).Format("2006-01-02")
	completeListReq.EndDate = req.EndDate
	completeListReq.IsPending = "COMPELETED_AND_PENDING"
	completeList, _, err := repo.GetUserTurnOverStartmentListPendingOnly(completeListReq)
	if err != nil {
		return nil, internalServerError(err)
	}
	fmt.Println("completeList", completeList)

	var UserTodayPlaylogListRequest model.UserTodayPlaylogListRequest
	UserTodayPlaylogListRequest.UserId = &req.UserId
	UserTodayPlaylogListRequest.StatementDateStart = lowestTurnOverDate.Add(7 * time.Hour).Format("2006-01-02")

	UserTodayPlaylogList, _, err := repo.GetTodaySumUserPlayLogList(UserTodayPlaylogListRequest)
	if err != nil {
		return nil, internalServerError(err)
	}

	var reponseList []model.GetUserTurnOverStartmentList

	var sumUpPlayLogGame float64
	var sumUpPlayLogCasino float64
	var sumUpPlayLogSport float64
	var sumUpPlayTotallog float64
	var sumUpPlayLogP2p float64
	var sumUpPlayLogLottery float64
	var sumUpPlayLogFinancial float64

	for _, usageBaseList := range pendingList {

		if usageBaseList.StatusId == model.TURNOVER_STATEMENT_STATUS_PENDING {
			sumAllAfterStartDateTurnover := usageBaseList.StartTurnAmount
			for _, completeItem := range completeList {
				// After begin of to day time now
				// currentTime := time.Now().UTC().Add(time.Hour * 7)
				// beginningOfDay := time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), 0, 0, 0, 0, currentTime.Location())
				if completeItem.EndTurnAt != nil {
					// this need to add to sumAllAfterStartDateTurnover
					sumAllAfterStartDateTurnover += completeItem.StartTurnAmount
				}
			}
			fmt.Println("sumAllAfterStartDateTurnover", sumAllAfterStartDateTurnover)

			// playLogs ห้ามย้ายออกนอก loop นี้ เนื้องด้วย แต่ละ กิจกรรมมีช่วงเวลารับที่ต่างกัน
			searchPLaylog := usageBaseList.StartTurnAt.Add(time.Hour * 7).Format("2006-01-02")
			// var userPlaylogGame float64
			// var userPlaylogCasino float64
			// var userPlaylogSport float64
			// var userPlaylogTotal float64

			// for _, playlog := range UserTodayPlaylogList {
			// 	if playlog.StatementDate >= searchPLaylog {
			// 		userPlaylogGame += playlog.SumTurnGame + playlog.SumTurnP2p
			// 		userPlaylogCasino += playlog.SumTurnCasino
			// 		userPlaylogSport += playlog.SumTurnSport
			// 		userPlaylogTotal += playlog.SumTurnGame + playlog.SumTurnCasino + playlog.SumTurnSport + playlog.SumTurnLottery + playlog.SumTurnP2p + playlog.SumTurnFinancial
			// 	}
			// }

			var userPlaylogGame float64
			var userPlaylogCasino float64
			var userPlaylogSport float64
			var userPlaylogTotal float64
			var userPlaylogP2p float64
			var userPlaylogLottery float64
			var userPlaylogFinancial float64

			for _, playlog := range UserTodayPlaylogList {
				if playlog.StatementDate >= searchPLaylog {
					userPlaylogGame += playlog.SumTurnGame
					userPlaylogCasino += playlog.SumTurnCasino
					userPlaylogSport += playlog.SumTurnSport
					userPlaylogP2p += playlog.SumTurnP2p
					userPlaylogLottery += playlog.SumTurnLottery
					userPlaylogFinancial += playlog.SumTurnFinancial
					userPlaylogTotal += playlog.SumTurnGame + playlog.SumTurnCasino + playlog.SumTurnSport + playlog.SumTurnLottery + playlog.SumTurnP2p + playlog.SumTurnFinancial
				}
			}
			var response model.GetUserTurnOverStartmentList
			response.Id = usageBaseList.Id
			response.UserId = usageBaseList.UserId
			response.TypeId = usageBaseList.TypeId
			response.Name = usageBaseList.Name
			response.PromotionName = usageBaseList.PromotionName
			response.StartTurnAmount = usageBaseList.StartTurnAmount
			response.StartTurnAt = usageBaseList.StartTurnAt
			response.TotalTurnAmount = usageBaseList.TotalTurnAmount
			// todo ขึ้น อยู่ กับ type
			var roundUpNeedToPlay float64
			if model.TURN_PROMOTION_SETTING_PLAY_GAME == usageBaseList.TypeId {
				roundUpNeedToPlay = sumAllAfterStartDateTurnover + sumUpPlayLogGame - userPlaylogGame
				sumUpPlayLogGame += usageBaseList.StartTurnAmount

			} else if model.TURN_PROMOTION_SETTING_PLAY_CASINO == usageBaseList.TypeId {
				roundUpNeedToPlay = sumAllAfterStartDateTurnover + sumUpPlayLogCasino - userPlaylogCasino
				sumUpPlayLogCasino += usageBaseList.StartTurnAmount
			} else if model.TURN_PROMOTION_SETTING_PLAY_SPORT == usageBaseList.TypeId {
				roundUpNeedToPlay = sumAllAfterStartDateTurnover + sumUpPlayLogSport - userPlaylogSport
				sumUpPlayLogSport += usageBaseList.StartTurnAmount
			} else if model.TURN_PROMOTION_SETTING_PLAY_P2P == usageBaseList.TypeId {
				roundUpNeedToPlay = sumAllAfterStartDateTurnover + sumUpPlayLogP2p - userPlaylogP2p
				sumUpPlayLogP2p += usageBaseList.StartTurnAmount
			} else if model.TURN_PROMOTION_SETTING_PLAY_LOTTERY == usageBaseList.TypeId {
				roundUpNeedToPlay = sumAllAfterStartDateTurnover + sumUpPlayLogLottery - userPlaylogLottery
				sumUpPlayLogLottery += usageBaseList.StartTurnAmount
			} else if model.TURN_PROMOTION_SETTING_PLAY_FINANCIAL == usageBaseList.TypeId {
				roundUpNeedToPlay = sumAllAfterStartDateTurnover + sumUpPlayLogFinancial - userPlaylogFinancial
				sumUpPlayLogFinancial += usageBaseList.StartTurnAmount
			} else {
				roundUpNeedToPlay = sumAllAfterStartDateTurnover + sumUpPlayTotallog - userPlaylogTotal
				sumUpPlayTotallog += usageBaseList.StartTurnAmount
			}

			if roundUpNeedToPlay < 0 {
				roundUpNeedToPlay = 0
			} else if roundUpNeedToPlay > usageBaseList.StartTurnAmount {
				roundUpNeedToPlay = usageBaseList.StartTurnAmount
			}
			response.NeedToPlay = math.Ceil(roundUpNeedToPlay)

			response.StatusId = usageBaseList.StatusId

			reponseList = append(reponseList, response)

		}
	}

	var response model.GetUserTurnOverStartmentListResponse
	response.List = reponseList
	response.Total = total
	response.Message = "TRUNOVER_PENDING_LIST_SUCCESS"

	return &response, nil
}

func (s turnoverService) TurnOverPendingList(req model.GetTurnOverStartmentListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	list, total, err := s.repo.TurnOverPendingList(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	var response model.SuccessWithPagination
	response.List = list
	response.Total = total
	response.Message = "TRUNOVER_PENDING_LIST_SUCCESS"

	return &response, nil
}

func MakeUserTidTurnPromotionReturnLoss(repo repository.TurnoverRepository, userId int64, returnAmount float64) error {

	actionAt := time.Now().UTC()
	// no setting = no turn
	checkTurn, err := GetTurnoverSetting(repo)
	if err != nil {
		log.Println("MakeUserTidTurnPromotionReturnLoss.ERROR.GetTurnoverSetting", err)
		return nil
	}

	if checkTurn.TidturnReturnLossPercent > 0 {
		// [turnOver] ตอนรับเงินคืน
		bonusAmount := returnAmount
		turnoverAmount := returnAmount * float64(checkTurn.TidturnReturnLossPercent) / 100
		var createBody model.TurnoverUserStatementCreateBody
		createBody.UserId = userId
		createBody.TypeId = model.TURNOVER_TYPE_PROMOTION_RETURN_LOSS
		// มีหลายไอดี แปะไม่ได้ createBody.RefTypeId = req.RefTypeId
		// PromotionName = "คืนยอดเสีย 10%"
		// รับรวมทีละหลายรายการ ใส่ชื่อ % ไม่ได้ percentAmount := math.Floor(trans.ReturnPercent*100) / 100
		createBody.Name = model.TURNOVER_CATE_RETURN_LOSS
		createBody.PromotionName = "คืนยอดเสีย"
		createBody.BonusAmount = bonusAmount
		createBody.StatusId = model.TURNOVER_STATEMENT_STATUS_PENDING
		createBody.StartTurnAmount = turnoverAmount
		createBody.StartTurnAt = &actionAt
		createBody.TotalTurnAmount = turnoverAmount
		if _, err := repo.CreateTurnoverUserStatement(createBody); err != nil {
			// later : handle error ?
			log.Println("MakeUserTidTurnPromotionReturnLoss.ERROR.CreateTurnoverUserStatement", err)
		}
	}
	return nil
}

func MakeUserTidTurnPromotionReturnTurn(repo repository.TurnoverRepository, userId int64, returnAmount float64) error {

	actionAt := time.Now().UTC()

	// no setting = no turn
	checkTurn, err := GetTurnoverSetting(repo)
	if err != nil {
		log.Println("MakeUserTidTurnPromotionReturnTurn.ERROR.GetTurnoverSetting", err)
		return nil
	}

	if checkTurn.TidturnReturnTurnPercent > 0 {
		// [turnOver] ตอนรับเงินคืน
		bonusAmount := returnAmount
		turnoverAmount := returnAmount * float64(checkTurn.TidturnReturnTurnPercent) / 100
		var createBody model.TurnoverUserStatementCreateBody
		createBody.UserId = userId
		createBody.TypeId = model.TURNOVER_TYPE_PROMOTION_RETURN_TURN
		// มีหลายไอดี แปะไม่ได้ createBody.RefTypeId = req.RefTypeId
		// PromotionName = "คืนยอดเสีย 10%"
		// รับรวมทีละหลายรายการ ใส่ชื่อ % ไม่ได้ percentAmount := math.Floor(trans.ReturnPercent*100) / 100
		createBody.Name = model.TURNOVER_CATE_RETURN_TURN
		createBody.PromotionName = "คืนยอดคอมมิชชั่น"
		createBody.BonusAmount = bonusAmount
		createBody.StatusId = model.TURNOVER_STATEMENT_STATUS_PENDING
		createBody.StartTurnAmount = turnoverAmount
		createBody.StartTurnAt = &actionAt
		createBody.TotalTurnAmount = turnoverAmount
		if _, err := repo.CreateTurnoverUserStatement(createBody); err != nil {
			// later : handle error ?
			log.Println("MakeUserTidTurnPromotionReturnTurn.ERROR.CreateTurnoverUserStatement", err)
		}
	}
	return nil
}

func CreateTurnOverFromSuccessDeposit(repo repository.TurnoverRepository, userId int64, bonusAmount float64, turnoverRefId int64) error {

	checkTurn, err := GetTurnoverSetting(repo)
	if err != nil {
		return nil
	}

	if checkTurn.TidturnSuccessDepositPercent > 0 {

		tidTurn := (bonusAmount * float64(checkTurn.TidturnSuccessDepositPercent) / 100)
		tidTurn = math.Ceil(tidTurn)

		createdTime := time.Now().UTC()
		var createBody model.TurnoverUserStatementCreateBody
		createBody.UserId = userId
		createBody.RefTypeId = turnoverRefId
		createBody.TypeId = model.TURNOVER_TYPE_SUCCESS_DEPOSIT
		createBody.Name = model.TURNOVER_CATE_SUCCESS_DEPOSIT
		createBody.PromotionName = model.TURNOVER_CATE_SUCCESS_DEPOSIT
		createBody.BonusAmount = bonusAmount
		createBody.StatusId = model.TURNOVER_STATEMENT_STATUS_PENDING
		createBody.StartTurnAmount = tidTurn
		createBody.StartTurnAt = &createdTime
		createBody.TotalTurnAmount = tidTurn
		if _, err := repo.CreateTurnoverUserStatement(createBody); err != nil {
			return err
		}
	} else {
		createdTime := time.Now().UTC()
		var createBody model.TurnoverUserStatementCreateBody
		createBody.UserId = userId
		createBody.RefTypeId = turnoverRefId
		createBody.TypeId = model.TURNOVER_TYPE_SUCCESS_DEPOSIT
		createBody.Name = model.TURNOVER_CATE_SUCCESS_DEPOSIT
		createBody.PromotionName = model.TURNOVER_CATE_SUCCESS_DEPOSIT
		createBody.BonusAmount = 0
		createBody.StatusId = model.TURNOVER_STATEMENT_STATUS_PENDING
		createBody.StartTurnAmount = 0
		createBody.StartTurnAt = &createdTime
		createBody.TotalTurnAmount = 0
		craeteTurnId, err := repo.CreateTurnoverUserStatement(createBody)
		if err != nil {
			return err
		}

		var updateTurnoverUserStatement model.TurnoverUserStatementUpdateBody
		setTotalTurnAmount := 0.0
		setTimeTurnAt := time.Now().UTC()
		updateTurnoverUserStatement.StatusId = model.TURNOVER_STATEMENT_STATUS_COMPLETED
		updateTurnoverUserStatement.TotalTurnAmount = &setTotalTurnAmount
		updateTurnoverUserStatement.EndTurnAt = &setTimeTurnAt
		if err := repo.UpdateTurnoverUserStatement(*craeteTurnId, updateTurnoverUserStatement); err != nil {
			log.Println("UpdateTurnoverUserStatement", err)
		}

		// create turnover withdraw log
		var createTurnoverWithDrawLog model.CreateTurnoverUserWithdrawLog
		createTurnoverWithDrawLog.UserId = userId
		createTurnoverWithDrawLog.LogKey = fmt.Sprintf("TURNDEPOSIT_U%d_D%s", userId, time.Now().UTC().Format("20060102150405"))
		createTurnoverWithDrawLog.TotalWithdrawPrice = 0
		createTurnoverWithDrawLog.CurrentTurn = 0
		createTurnoverWithDrawLog.PlayTotal = 0
		createTurnoverWithDrawLog.LastPlayY = 0
		createTurnoverWithDrawLog.LastTotalX = 0
		if _, err = repo.CreateTurnoverUserWithdrawLog(createTurnoverWithDrawLog); err != nil {
			log.Println("CreateTurnOverFromSuccessDeposit.CreateTurnoverUserWithdrawLog", err)
		}
	}

	return nil
}

func (s *turnoverService) AvaliableGamePlayCheckTurnOver(req model.AvaliableGamePlayCheckTurnOverRequest) error {
	// func CheckOtherExistTurnOver(repoBanking repository.BankingRepository, userId int64) (string, float64, error)

	_, _, err := CheckOtherExistTurnOver(repository.NewBankingRepository(s.repo.GetDb()), req.UserId)
	if err != nil {
		return nil
	}

	var getTurnOverPendingRequestCheck model.GetUserTurnOverStartmentListRequest
	getTurnOverPendingRequestCheck.UserId = req.UserId
	getTurnOverPendingRequestCheck.IsPending = "PENDING"
	getTurnOverPendingList, err := s.UserTurnOverPendingList(getTurnOverPendingRequestCheck)
	if err != nil {
		return nil
	}

	if len(getTurnOverPendingList.List) > 0 {
		var getNotPassType []string
		for _, getTurnOverPending := range getTurnOverPendingList.List {
			if getTurnOverPending.NeedToPlay <= 0 {
				continue
			}

			switch getTurnOverPending.TypeId {
			case model.TURN_PROMOTION_SETTING_PLAY_GAME:
				getNotPassType = append(getNotPassType, "SLOT")
			case model.TURN_PROMOTION_SETTING_PLAY_SPORT:
				getNotPassType = append(getNotPassType, "SPORT")
			case model.TURN_PROMOTION_SETTING_PLAY_CASINO:
				getNotPassType = append(getNotPassType, "CASINO")
			case model.TURN_PROMOTION_SETTING_PLAY_P2P:
				getNotPassType = append(getNotPassType, "P2P")
			case model.TURN_PROMOTION_SETTING_PLAY_LOTTERY:
				getNotPassType = append(getNotPassType, "LOTTO")
			case model.TURN_PROMOTION_SETTING_PLAY_FINANCIAL:
				getNotPassType = append(getNotPassType, "FINANCIAL")
			}
		}

		if len(getNotPassType) > 0 {
			req.Category = strings.ToUpper(req.Category)
			for _, notPassed := range getNotPassType {
				if strings.EqualFold(req.Category, notPassed) {
					return nil
				}
			}

			return fmt.Errorf("ALLOW_GAMEPLAY_%s", getNotPassType[0])
		}
	}

	return nil
}
