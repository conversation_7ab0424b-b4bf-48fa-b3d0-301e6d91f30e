package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"os"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm"
)

type OnepayService interface {
	// Onepay
	CreateOnepayWebhook(req model.OnepayWebhookRequest) (*int64, error)
	GetOnepayWebDepositAccount() (*model.OnepayCustomerDepositInfo, error)
	CreateOnepayDeposit(req model.OnepayDepositCreateRequest) (*model.OnepayOrderWebResponse, error)
	CreateOnepayWithdraw(req model.OnepayWithdrawCreateRequest) (*int64, error)
	// OnepayCheckBalance() (*model.OnepayCheckBalanceRemoteResponse, error)
	CancelWithdrawFromOnepay(transId int64, adminId int64) error
	CreateOnepayDepositWebhook(req model.OnepayWebhookRequest) (*int64, error)
	CreateOnepayWithdrawWebhook(req model.OnepayWebhookRequest) (*int64, error)
	// SysLog
	CreateSystemLog(name string, req interface{}) error
}

type paygateOnepayService struct {
	sharedDb                  *gorm.DB
	repo                      repository.OnepayRepository
	activityLuckyWheelService ActivityLuckyWheelService
	promotionWebService       PromotionWebService
	notiService               NotificationService
}

func NewOnepayService(
	sharedDb *gorm.DB,
	repo repository.OnepayRepository,
	activityLuckyWheelService ActivityLuckyWheelService,
	promotionWebService PromotionWebService,
	notiService NotificationService,
) OnepayService {
	return &paygateOnepayService{sharedDb, repo, activityLuckyWheelService, promotionWebService, notiService}
}

func (s paygateOnepayService) GetOnepayAccessToken(pgAccount *model.PaygateAccountResponse) (string, error) {

	return GetOnepayAccessToken(s.repo, pgAccount)
}

func GetOnepayAccessToken(repo repository.OnepayRepository, pgAccount *model.PaygateAccountResponse) (string, error) {

	if pgAccount == nil {
		newSetting, err := GetOnepayAccount(repo)
		if err != nil {
			return "", internalServerError(err)
		}
		pgAccount = newSetting
	}
	if pgAccount == nil {
		return "", badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}
	if pgAccount.ApiEndPoint == "" || pgAccount.PartnerKey == "" {
		return "", badRequest("INVALID_MERCHANT_ACCESS_KEY_OR_SECRET")
	}

	// use DB cache if not expired
	accessToken, err := repo.GetDbOnepayAccessToken()
	if err != nil {
		if err.Error() == recordNotFound {
			data, err := repo.OnepayGetToken(*pgAccount)
			if err != nil {
				return "", internalServerError(err)
			}
			accessToken := data.Token
			// Save Cache
			var createBody model.OnepayTokenCreateBody
			createBody.AccessToken = accessToken
			createBody.ExpireAt = time.Now().Add(time.Minute * 15)
			createBody.CreateBy = 1
			if _, err = repo.CreateDbOnepayAccessToken(createBody); err != nil {
				return "", internalServerError(err)
			}
			// Save Response as Webhook
			var createBody2 model.OnepayWebhookCreateBody
			createBody2.Name = "ONEPAY_LOGEDIN"
			createBody2.JsonPayload = helper.StructJson(data)
			if _, err = repo.CreateOnepayWebhook(createBody2); err != nil {
				log.Println("Error GetOnepayAccessToken.CreateOnepayWebhook", err)
			}
			return accessToken, nil
		}
		return "", internalServerError(err)
	}
	return accessToken.AccessToken, nil
}

func (s paygateOnepayService) ConfirmDepositTransaction(id int64, req model.BankConfirmDepositRequest) error {

	record, err := s.repo.GetBankTransactionById(id)
	if err != nil {
		return internalServerError(err)
	}
	if record.TransactionStatusId != model.TRANS_STATUS_PENDING {
		return badRequest("Transaction is not pending")
	}
	if record.TransactionTypeId != model.TRANSACTION_TYPE_DEPOSIT && record.TransactionTypeId != model.TRANSACTION_TYPE_BONUS {
		return badRequest("Transaction is not deposit")
	}
	jsonBefore, _ := json.Marshal(record)

	var updateData model.BankDepositTransactionConfirmBody
	updateData.TransactionStatusId = model.TRANS_STATUS_DEPOSIT_PENDING_CREDIT
	updateData.ConfirmedAt = req.ConfirmedAt
	updateData.ConfirmedByAdminId = req.ConfirmedByAdminId

	// if record.IsAutoCredit && record.TransferAt != nil {
	// 	seconds := time.Now().UTC().Sub(record.TransferAt.UTC()).Seconds()
	// 	autoProcessTimer := fmt.Sprintf("%.2f", seconds)
	// 	updateData.AutoProcessTimer = &autoProcessTimer
	// }

	var createBody model.CreateBankTransactionActionBody
	createBody.ActionKey = fmt.Sprintf("DCF_STATE#%d", record.Id)
	createBody.TransactionId = record.Id
	createBody.UserId = record.UserId
	createBody.TransactionTypeId = record.TransactionTypeId
	createBody.FromAccountId = record.FromAccountId
	createBody.ToAccountId = record.ToAccountId
	createBody.JsonBefore = string(jsonBefore)
	if req.TransferAt == nil {
		createBody.TransferAt = record.TransferAt
	} else {
		TransferAt := req.TransferAt
		createBody.TransferAt = TransferAt
		updateData.TransferAt = *TransferAt
	}
	if req.SlipUrl != nil {
		createBody.SlipUrl = *req.SlipUrl
	}
	createBody.CreditAmount = record.CreditAmount
	if req.BonusAmount != nil {
		createBody.BonusAmount = *req.BonusAmount
	}
	createBody.ConfirmedAt = req.ConfirmedAt
	createBody.ConfirmedByAdminId = req.ConfirmedByAdminId
	if actionId, err := s.repo.CreateTransactionAction(createBody); err == nil {
		// do nothing ?
		if err := s.repo.ConfirmPendingDepositTransaction(id, updateData); err != nil {
			if err := s.repo.RollbackTransactionAction(*actionId); err == nil {
				return internalServerError(err)
			}
			return internalServerError(err)
		}
	} else {
		return internalServerError(err)
	}
	return nil
}

func (s paygateOnepayService) CreateOnepayWebhook(req model.OnepayWebhookRequest) (*int64, error) {

	var createBody model.OnepayWebhookCreateBody
	createBody.Name = "ONEPAY_CALLBACK"
	createBody.JsonPayload = req.JsonPayload
	insertId, err := s.repo.CreateOnepayWebhook(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// just for log use Deposit and Withdraw webhook.
	var remoteResp model.OnepayWebhookResponse
	errJson := json.Unmarshal([]byte(req.JsonPayload), &remoteResp)
	if errJson != nil {
		log.Println("Error unmarshal json", errJson)
		return nil, internalServerError(errJson)
	}
	log.Println("CreateOnepayWebhook.OnepayWebhookResponse=", helper.StructJson(remoteResp))

	return insertId, nil
}

func (s paygateOnepayService) GetOnepayWebDepositAccount() (*model.OnepayCustomerDepositInfo, error) {

	var result model.OnepayCustomerDepositInfo

	pgAccount, err := s.GetOnepayAccount()
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}
	if !pgAccount.IsDepositEnabled {
		return nil, badRequest("PAYGATE_DEPOSIT_NOT_ENABLED")
	}

	result.Name = pgAccount.Name
	if pgAccount.DisplayName == "" {
		result.Name = "เติมเงินผ่าน QR Code"
	} else {
		result.DisplayName = pgAccount.DisplayName
	}
	result.ShopName = pgAccount.ShopName
	result.MinAmount = 10
	result.MaxAmount = 50000

	return &result, nil
}

func (s paygateOnepayService) GetOnepayAccount() (*model.PaygateAccountResponse, error) {

	return GetOnepayAccount(s.repo)
}

func GetOnepayAccount(repo repository.OnepayRepository) (*model.PaygateAccountResponse, error) {

	pgAccount, err := repo.GetPaygateAccountByProviderId(model.PAYGATE_MERCHANT_ID_ONEPAY)
	if err != nil {
		return nil, internalServerError(err)
	}
	return pgAccount, nil
}

func GetOnepayCustomerBank(bankCode string) (string, error) {

	// bank_code	short_name_en	bank_name_thai
	// 002	BBL	ธนาคารกรุงเทพ
	// 004	KBANK	ธนาคารกสิกรไทย
	// 006	KTB	ธนาคารกรุงไทย
	// 011	TMB	ธนาคารทหารไทย
	// 014	SCB	ธนาคารไทยพาณิชย์
	// 017	CITI N.A	ซิตี้แบงก์ เอ็น.เอ.
	// 020	SCBT	ธนาคารสแตนดาร์ดชาร์เตอร์ด (ไทย)
	// 022	CIMB	ธนาคาร ซีไอเอ็มบี
	// 024	UOB	ธนาคารยูโอบี
	// 025	BAY	ธนาคารกรุงศรีอยุธยา
	// 030	GOV	ธนาคารออมสิน
	// 031	HSBC	ธนาคารฮ่องกงและเซี่ยงไฮ้แบงกิ้งคอร์ปอเรชั่น จำกัด
	// 033	GHB	ธนาคารอาคารสงเคราะห์
	// 034	AGRI	ธนาคารเพื่อการเกษตรและสหกรณ์การเกษตร
	// 039	MHCB	ธนาคารมิซูโฮ
	// 065	TBANK	ธนาคารธนชาต
	// 066	ISBT	ธนาคารอิสลามแห่งประเทศไทย
	// 067	TISCO	ธนาคารทิสโก้
	// 069	KK	ธนาคารเกียรตินาคิน
	// 070	ACL	ธนาคารเอซีแอล
	// 071	TCRB	ธนาคารไทยเครดิต เพื่อรายย่อย
	// 073	LHBANK	ธนาคารแลนด์ แอนด์ เฮ้าส์
	// 079	ANZ	ธนาคารเอเอ็นแซด (ไทย)
	// 080	SMTB	ธนาคารซูมิโตโม มิตซุย ทรัสต์ (ไทย)
	// 098	SMEB	ธนาคารพัฒนาวิสาหกิจขนาดกลางและขนาดย่อมแห่งประเทศไทย
	remoteBankCode := ""
	// todo
	uBankCode := strings.ToUpper(strings.TrimSpace(bankCode))

	switch uBankCode {
	case "BBL":
		remoteBankCode = "002"
	case "KBANK":
		remoteBankCode = "004"
	case "KTB":
		remoteBankCode = "006"
	case "TMB":
		remoteBankCode = "011"
	case "SCB":
		remoteBankCode = "014"
	case "CITI":
		remoteBankCode = "017"
	case "SCBT":
		remoteBankCode = "020"
	case "CIMB":
		remoteBankCode = "022"
	case "UOB":
		remoteBankCode = "024"
	case "BAY":
		remoteBankCode = "025"
	case "GOV":
		remoteBankCode = "030"
	case "HSBC":
		remoteBankCode = "031"
	case "GHB":
		remoteBankCode = "033"
	case "AGRI":
		remoteBankCode = "034"
	case "MHCB":
		remoteBankCode = "039"
	case "TBANK":
		remoteBankCode = "065"
	case "ISBT":
		remoteBankCode = "066"
	case "TISCO":
		remoteBankCode = "067"
	case "KK":
		remoteBankCode = "069"
	case "ACL":
		remoteBankCode = "070"
	case "TCRB":
		remoteBankCode = "071"
	case "LHBANK":
		remoteBankCode = "073"
	case "ANZ":
		remoteBankCode = "079"
	case "SMTB":
		remoteBankCode = "080"
	case "SMEB":
		remoteBankCode = "098"
	default:
		return "", errors.New("USER_BANK_NOT_SUPPORTED")
	}

	return remoteBankCode, nil
}

func (s paygateOnepayService) CreateOnepayDeposit(req model.OnepayDepositCreateRequest) (*model.OnepayOrderWebResponse, error) {

	var result model.OnepayOrderWebResponse

	// Ruled by Provider
	if req.Amount < model.ONEPAY_DEFMIN_DEPOSIT_AMOUNT || req.Amount > model.ONEPAY_DEFMAX_DEPOSIT_AMOUNT {
		log.Println("req.Amount", req.Amount)
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}

	pgAccount, err := s.GetOnepayAccount()
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}
	if !pgAccount.IsDepositEnabled {
		return nil, badRequest("PAYGATE_DEPOSIT_NOT_ENABLED")
	}
	if pgAccount.ApiEndPoint == "" || pgAccount.PartnerKey == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}

	webhookDomain := os.Getenv("CYBERGAME_WEBHOOK_ENDPOINT")
	if webhookDomain == "" {
		return nil, errors.New("EMPTY_ENV_CYBERGAME_WEBHOOK_ENDPOINT")
	}

	user, err := s.repo.GetUserBankDetailById(req.UserId)
	if err != nil {
		return nil, badRequest("INVALID_USER")
	}
	if user == nil || user.BankAccount == "" || user.BankCode == "" {
		return nil, badRequest("INVALID_USER_ACCOUNT")
	}

	accountingRepo := repository.NewAccountingRepository(s.repo.GetDb())
	configWeb, err := s.repo.GetWebConfiguration()
	if err != nil {
		log.Println("CreateOnepayDeposit.GetWebConfiguration", err)
		return nil, badRequest("WEB_CONFIG_NOT_FOUND")
	}
	if user.Id != 0 && configWeb != nil {
		// [********] ฝากครั้งแรกขั้นต่ำ ปรับ flow เช็ค user ให้เช็คจากรายการฝากครั้งแรกจริงๆ ไม่เช็คจากรหัสที่ได้รับ
		if err := CheckFirstMinimunDeposit(accountingRepo, user.Id, req.Amount, *configWeb); err != nil {
			return nil, badRequest(fmt.Sprintf("ฝากครั้งแรกขั้นต่ำ %d บาท", configWeb.MinFirstMemberDeposit))
		}
	} else {
		log.Println("CreateOnepayDeposit.CheckFirstMinimunDeposit", "CANT_CHECK_FIRST_MINIMUM_DEPOSIT_ON_EMPTY_CONFIG")
	}

	depBankCode, err := GetOnepayCustomerBank(user.BankCode)
	if err != nil || depBankCode == "" {
		return nil, badRequest("USER_BANK_NOT_SUPPORTED")
	}

	token, err := s.GetOnepayAccessToken(pgAccount)
	if err != nil {
		// return nil, internalServerError(errors.New("INVALID_ACCESS_TOKEN"))
		return nil, err
	}

	// ===========================================================================================
	var createBody model.OnepayOrderCreateBody
	createBody.UserId = req.UserId
	createBody.OrderTypeId = model.ONEPAY_ORDER_TYPE_DEPOSIT
	createBody.OrderNo = "" // Autogen
	createBody.Amount = req.Amount
	createBody.TransactionStatus = "PENDING"
	insertId, err := s.repo.CreateDbOnepayOrder(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	// reget Order
	pendingOrder, err := s.repo.GetDbOnepayOrderById(*insertId)
	if err != nil {
		// SET AS ERROR
		remark := "Error GetDbOnepayOrderById, " + err.Error()
		if err := s.repo.UpdateDbOnepayOrderError(*insertId, remark); err != nil {
			log.Println("CreateOnepayDeposit.UpdateDbOnepayOrderError", err)
		}
		return nil, internalServerError(err)
	}
	// Create ONEPAY Order
	var remoteRequest model.OnepayDepositCreateRemoteRequest
	remoteRequest.OrderId = pendingOrder.OrderNo
	remoteRequest.Amount = pendingOrder.Amount
	remoteRequest.RefAccount = user.BankAccount
	remoteRequest.RefBankCode = depBankCode // kbank => 001
	remoteRequest.RefNameTh = user.Fullname
	remoteRequest.RefNameEn = user.Fullname
	remoteRequest.RefUserId = fmt.Sprintf("%d", user.Id)
	remoteRequest.Ref1 = "REF11"
	remoteRequest.Ref2 = "REF22"
	remoteRequest.CallbackUrl = fmt.Sprintf("%s/onepay/dep-callback", webhookDomain)
	remoteResp, err := s.repo.OnepayDeposit(token, *pgAccount, remoteRequest)
	if err != nil {
		// SET AS ERROR
		remark := "Error OnepayDeposit, " + err.Error()
		if err := s.repo.UpdateDbOnepayOrderError(*insertId, remark); err != nil {
			log.Println("OnepayDeposit.UpdateDbOnepayOrderError", err)
		}
		// SysLog
		if _, err := s.repo.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "CreateOnepayDeposit.OnepayDeposit",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(remoteRequest),
			JsonResponse: helper.StructJson(map[string]interface{}{"error": err.Error()}),
		}); err != nil {
			log.Println("CreateOnepayDeposit.CreateSysLog", err)
		}
		return nil, internalServerError(err)
	}

	fmt.Println("OnepayDeposit.remoteResp", helper.StructJson(remoteResp))

	// Parse Float Amount
	transferAmount, err := strconv.ParseFloat(remoteResp.Data.Amount, 64)
	if err != nil {
		// SET AS ERROR
		remark := "Error ParseFloat Amount, " + err.Error()
		if err := s.repo.UpdateDbOnepayOrderError(*insertId, remark); err != nil {
			log.Println("CreateOnepayDeposit.UpdateDbOnepayOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// onCreate Success
	var updateBody model.OnepayOrderUpdateBody
	updateBody.TransactionNo = remoteResp.Data.TxnNo
	updateBody.TransactionDate = time.Now().UTC()
	updateBody.TransactionStatus = "WAIT_PAYMENT"
	updateBody.TransferAmount = transferAmount
	updateBody.QrPromptpay = remoteResp.Data.QrCode // todo QR string?
	// updateBody.PaymentPageUrl = remoteResp.Data.Redirect
	if err := s.repo.UpdateDbOnepayOrder(*insertId, updateBody); err != nil {
		// SET AS ERROR
		remark := "Error UpdateDbOnepayOrder, " + err.Error()
		if err := s.repo.UpdateDbOnepayOrderError(*insertId, remark); err != nil {
			log.Println("CreateOnepayDeposit.UpdateDbOnepayOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	waitPayOrder, err := s.repo.GetDbOnepayOrderById(*insertId)
	if err != nil {
		return nil, internalServerError(err)
	}

	result.UserId = waitPayOrder.UserId
	result.OrderNo = waitPayOrder.OrderNo
	result.Amount = waitPayOrder.Amount
	result.TransferAmount = transferAmount
	result.TransactionStatus = *waitPayOrder.TransactionStatus
	result.QrCode = waitPayOrder.QrPromptpay
	// result.PaymentPageUrl = waitPayOrder.PaymentPageUrl
	result.CreatedAt = waitPayOrder.CreatedAt

	// imgData, err := qrcode.Encode(waitPayOrder.QrPromptpay, qrcode.Medium, 256)
	// if err != nil {
	// 	// return nil, fmt.Errorf("unable to encode png: %w", err)
	// 	return &result, nil
	// }
	// // encode to base64
	// img, err := png.Decode(bytes.NewReader(imgData))
	// if err != nil {
	// 	// return nil, fmt.Errorf("unable to decode jpeg: %w", err)
	// 	return &result, nil
	// }
	// var buf bytes.Buffer
	// if err := png.Encode(&buf, img); err != nil {
	// 	// return nil, fmt.Errorf("unable to encode png: %w", err)
	// 	return &result, nil
	// }
	result.QrBase64 = waitPayOrder.QrPromptpay

	return &result, nil
}

func (s paygateOnepayService) CreateOnepayWithdraw(req model.OnepayWithdrawCreateRequest) (*int64, error) {

	// Ruled by Provider
	// - loan trade amount must greater than 100
	if req.Amount < model.ONEPAY_DEFMIN_WITHDRAW_AMOUNT || req.Amount > model.ONEPAY_DEFMAX_WITHDRAW_AMOUNT {
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}
	// (ปิดถอนช่วง 23:00-00:30 น. รวม 1.30 ชม.) **ยังไม่คอนเฟิมว่าปิดถอนช่วงเวลาไหน
	// actionAtUtc := time.Now().UTC()
	// bkkLoc := time.FixedZone("Asia/Bangkok", 7*60*60)
	// actionAtBkk := actionAtUtc.In(bkkLoc)
	// if actionAtBkk.Hour() >= 23 || (actionAtBkk.Hour() == 0 && actionAtBkk.Minute() <= 30) {
	// 	log.Println("withdrawWithOnepay", "WITHDRAW_NOT_AVAILABLE (ปิดถอนช่วง 23:00-00:30 น. รวม 1.30 ชม.)", actionAtBkk)
	// 	return nil, errors.New("WITHDRAW_NOT_AVAILABLE")
	// }

	pgAccount, err := s.GetOnepayAccount()
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}
	if !pgAccount.IsWithdrawEnabled {
		return nil, badRequest("PAYGATE_WITHDRAW_NOT_ENABLED")
	}
	if pgAccount.ApiEndPoint == "" || pgAccount.PartnerKey == "" || pgAccount.LoanAppId == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}

	user, err := s.repo.GetUserBankDetailById(req.UserId)
	if err != nil {
		return nil, badRequest("INVALID_USER")
	}
	if user == nil || user.BankAccount == "" || user.BankCode == "" {
		return nil, badRequest("INVALID_USER_ACCOUNT")
	}

	webhookDomain := os.Getenv("CYBERGAME_WEBHOOK_ENDPOINT")
	if webhookDomain == "" {
		return nil, errors.New("EMPTY_ENV_CYBERGAME_WEBHOOK_ENDPOINT")
	}

	withdrawBankCode, err := GetOnepayCustomerBank(user.BankCode)
	if err != nil || withdrawBankCode == "" {
		return nil, badRequest("USER_BANK_NOT_SUPPORTED")
	}

	token, err := s.GetOnepayAccessToken(pgAccount)
	if err != nil {
		// return nil, internalServerError(errors.New("INVALID_ACCESS_TOKEN"))
		return nil, err
	}

	// ===========================================================================================
	// CREATE Order
	var createBody model.OnepayOrderCreateBody
	createBody.RefId = &req.RefId
	createBody.UserId = req.UserId
	createBody.OrderTypeId = model.ONEPAY_ORDER_TYPE_WITHDRAW
	createBody.OrderNo = "" // Autogen
	createBody.Amount = req.Amount
	createBody.TransactionStatus = "PENDING"
	insertId, err := s.repo.CreateDbOnepayOrder(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	// reget Order
	pendingOrder, err := s.repo.GetDbOnepayOrderById(*insertId)
	if err != nil {
		// SET AS ERROR
		remark := "Error GetDbOnepayOrderById, " + err.Error()
		if err := s.repo.UpdateDbOnepayOrderError(*insertId, remark); err != nil {
			log.Println("CreateOnepayWithdraw.UpdateDbOnepayOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// Create ONEPAY Order
	var remoteRequest model.OnepayWithdrawCreateRemoteRequest
	remoteRequest.OrderId = pendingOrder.OrderNo
	remoteRequest.Amount = pendingOrder.Amount
	remoteRequest.BankAccNo = user.BankAccount
	remoteRequest.BankCode = withdrawBankCode
	remoteRequest.BankAccName = user.Fullname
	// รหัส Pin Code ที่ใส่ยืนยันรายการเมื่อรายการถอนมียอดถอนมากกว่าขั้นต่ำ
	remoteRequest.WithdrawCode = pgAccount.LoanAppId
	remoteRequest.CallbackUrl = fmt.Sprintf("%s/onepay/wid-callback", webhookDomain)
	remoteResp, err := s.repo.OnepayWithdraw(token, *pgAccount, remoteRequest)
	if err != nil {
		// SET AS ERROR
		remark := "Error OnepayWithdraw, " + err.Error()
		if err := s.repo.UpdateDbOnepayOrderError(*insertId, remark); err != nil {
			log.Println("OnepayWithdraw.UpdateDbOnepayOrderError", err)
		}
		// SysLog
		if _, err := s.repo.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "CreateOnepayWithdraw.OnepayWithdraw",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(remoteRequest),
			JsonResponse: helper.StructJson(map[string]interface{}{"error": err.Error()}),
		}); err != nil {
			log.Println("CreateOnepayWithdraw.CreateSysLog", err)
		}
		return nil, internalServerError(err)
	}

	// fmt.Println("CreateOnepayWithdraw.remoteResp", helper.StructJson(remoteResp))

	if remoteResp.Data.TxnNo == "" {
		// SET AS ERROR
		remark := remoteResp.Message
		if len(remark) == 0 {
			remark = "UNKNOW_ERROR from withdrawWithOnepay"
		}
		if err := s.repo.UpdateDbOnepayOrderError(*insertId, remark); err != nil {
			log.Println("OnepayWithdraw.UpdateDbOnepayOrderError", err)
		}
		// SysLog
		if _, err := s.repo.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "CreateOnepayWithdraw.OnepayWithdraw",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(remoteRequest),
			JsonResponse: helper.StructJson(remoteResp),
		}); err != nil {
			log.Println("CreateOnepayWithdraw.CreateSysLog", err)
		}
		return nil, internalServerError(fmt.Errorf("UNKNOW_ERROR"))
	}

	// onCreate Success
	var updateBody model.OnepayOrderUpdateBody
	updateBody.TransactionNo = remoteResp.Data.TxnNo
	updateBody.TransactionDate = time.Now().UTC()
	updateBody.TransactionStatus = "WAIT_PAYMENT"
	updateBody.QrPromptpay = remoteResp.Message
	if err := s.repo.UpdateDbOnepayOrder(*insertId, updateBody); err != nil {
		// SET AS ERROR
		remark := "Error UpdateDbOnepayOrder, " + err.Error()
		if err := s.repo.UpdateDbOnepayOrderError(*insertId, remark); err != nil {
			log.Println("CreateOnepayWithdraw.UpdateDbOnepayOrderError", err)
		}
		return nil, internalServerError(err)
	}
	return insertId, nil
}

func CreateCustomerDepositFromOnepayOrder(repo repository.OnepayRepository, orderId int64, adminId int64) (*int64, error) {

	// ** ทุกรายการจะต้องเช็คสถานะใหม่ทั้งหมด เพราะ admin แย่งกดอนุมัติได้
	item, err := repo.GetRawOnepayPendingDepositOrderById(orderId)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, badRequest("ORDER_NOT_FOUND")
		}
		return nil, internalServerError(err)
	}

	// Check BankTransactionStatus = 'PENDING'
	if item.BankTransactionStatus != "PENDING" {
		return nil, badRequest("ORDER_NOT_PENDING")
	}

	return createCustomerDepositFromOnepay(repo, *item, adminId)
}

func createCustomerDepositFromOnepay(repo repository.OnepayRepository, item model.OnepayOrderResponse, adminId int64) (*int64, error) {

	var externalNoti model.NotifyExternalNotificationRequest
	actionAt := time.Now().UTC()
	// confirmByUserId := int64(0)

	depositAmount := item.Amount

	user, err := repo.GetUserBankDetailById(item.UserId)
	if err != nil {
		log.Println("createCustomerDepositFromOnepay.GetUserBankDetailByMemberCode", err)
		return nil, internalServerError(err)
	}

	var promotionWebUserId int64
	if getPromotionWebUser, err := repo.GetDepositCurrentProcessingUserPromotion(item.UserId); err != nil {
		log.Println("createCustomerDepositFromOnepay.GetDepositCurrentProcessingUserPromotion", err)
	} else if getPromotionWebUser != nil {
		promotionWebUserId = getPromotionWebUser.Id
	}

	// MemberCode
	if user.MemberCode == "" {
		memberCode, err := GenUniqueUserMemberCode(repository.NewUserRepository(repo.GetDb()), user.Id)
		if err != nil {
			log.Println("createCustomerDepositFromOnepay.GenUniqueUserMemberCode", err)
			return nil, internalServerError(err)
		}
		user.MemberCode = *memberCode
	}

	// layer — 2024-04-02 at 10:06 AM
	// ถ้าแก้ตามนี้แล้วน่าจะไม่เจอ ถ้าเคสกันซ้ำเราครอบคลุม
	// @Dunk @TULA
	// กันซ้ำคือ heng ไปซ้ำกับแอดมินเติมมือ
	// ----
	// เงื่อนไขกันซ้ำ
	// 1 หารายการฝากสำเร็จล่าสุดของยูสนั้น
	// 2 ถ้ามีรายการล่าสุดที่เป็นเติมมือ
	// -เช็คเวลาเติม ต้องมากกว่าเวลาโอนในสลิป
	// -ยอดเงินเท่ากัน
	// **ถ้าเข้าเงื่อนไขทั้งหมด คือแสดงว่า webhook เข้าทีหลังแอดมินเติมมือ
	// ต้อง bypass ไม่ให้เติมอีก และอัพเดท status ว่าจับคู่ได่แล้ว
	// ---
	// วิธีนี้น่าจะใช้ได้กับทุก webhook (heng, fastbank) ที่มีเวลาโอนสลิปมาให้
	duplicateRemark := "INIT"
	hasDuplicateDeposit := false
	manualDeposit, err := repo.GetLastestBankAdminManualDepositTransaction(user.Id)
	if err != nil {
		if err.Error() != recordNotFound {
			log.Println("createCustomerDepositFromOnepay.GetLastestBankAdminManualDepositTransaction", err)
			return nil, internalServerError(err)
		}
		// NOT_FOUND = ถ้าไม่เจอ ให้ทำต่อ
		duplicateRemark = "OK_NO_MANUAL_DEPOSIT"
	}
	if manualDeposit != nil && item.TransactionDate != nil {
		if manualDeposit.TransferAt.After(*item.TransactionDate) {
			// ถ้าเวลาเติมมือมากกว่าเวลาโอนในสลิป
			if manualDeposit.CreditAmount == depositAmount {
				// ถ้ายอดเงินเท่ากัน
				duplicateRemark = "STOP_MANUAL_DEPOSIT_AFTER_AND_SAME_AMOUNT"
				hasDuplicateDeposit = true
			} else {
				duplicateRemark = "OK_MANUAL_DEPOSIT_AMOUNT_NOT_MATCH"
			}
		} else {
			duplicateRemark = "OK_MANUAL_DEPOSIT_BEFORE_WITHDRAW"
		}
	}
	if duplicateRemark != "INIT" {
		// WebhookLog
		var createBody2 model.OnepayWebhookCreateBody
		createBody2.Name = "ONEPAY_DEPOSIT_DUPLICATE_CHECKER"
		createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
			"item":                item,
			"method":              "createCustomerDeposit",
			"hasDuplicateDeposit": hasDuplicateDeposit,
			"error":               duplicateRemark,
			"manualDeposit":       manualDeposit,
		})
		if _, err = repo.CreateOnepayWebhook(createBody2); err != nil {
			log.Println("Error CreateOnepayWebhook.CreateOnepayWebhook", err)
		}
		if hasDuplicateDeposit {
			return nil, internalServerError(fmt.Errorf("DUPLICATE_DEPOSIT"))
		}
	}

	pgAccount, err := GetOnepayAccount(repo)
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}

	// ===================================================
	paygateRepo := repository.NewPaymentGatewayRepository(repo.GetDb())
	accountingRepo := repository.NewAccountingRepository(repo.GetDb())
	luckyWheelRepo := repository.NewActivityLuckyWheelRepository(repo.GetDb())
	promotionWebRepo := repository.NewPromotionWebRepository(repo.GetDb())
	notiRepo := repository.NewNotificationRepository(repo.GetDb())

	// Affiliate + Alliance Income
	member, err := repo.GetMemberById(user.Id)
	if err != nil {
		return nil, internalServerError(err)
	}
	if member.UserTypeName == "NONE" {
		// ถ้าฝากครั้งแรก จะได้เป็น AFFILIATE + REFER COMMISION
		if err := UserFirstDepositCommission(accountingRepo, *member, depositAmount); err != nil {
			log.Println("CreateFristTimeDepositRecord.UserFirstDepositCommission.ERROR", err)
			return nil, internalServerError(err)
		}
	}

	isAutoDeposit := true // Auto ฝาก นับตั้งแต่ได้รับยอดฝาก จนได้ยืนยันเครดิต
	if adminId != 0 {
		isAutoDeposit = false
	}

	// CREATE BANK TRANSACTION
	var createBankTransaction model.BankTransactionCreateBody
	createBankTransaction.MemberCode = user.MemberCode
	createBankTransaction.UserId = user.Id
	// createBankTransaction.StatementId = 0
	createBankTransaction.TransactionTypeId = model.TRANSACTION_TYPE_DEPOSIT
	// createBankTransaction.FromAccountName = "" // empty?
	createBankTransaction.ToAccountName = &pgAccount.TypeName
	createBankTransaction.ToAccountNumber = &pgAccount.ShopName
	createBankTransaction.PromotionId = &promotionWebUserId
	createBankTransaction.CreditAmount = depositAmount
	// createBankTransaction.BonusAmount = 0
	// createBankTransaction.BonusReason = nil
	createBankTransaction.DepositChannel = "ONEPAY PAYMENT GATEWAY"
	createBankTransaction.TransactionStatusId = model.TRANS_STATUS_PENDING
	createBankTransaction.CreatedByAdminId = 0
	createBankTransaction.IsAutoCredit = isAutoDeposit
	createBankTransaction.TransferAt = &actionAt
	// create transaction
	transId, err := repo.InsertBankTransaction(createBankTransaction)
	if err != nil {
		log.Println("createCustomerDepositFromOnepay.InsertBankTransaction", err)
		return nil, internalServerError(err)
	}

	// var actionStatement model.BankStatementMatchRequest
	// actionStatement.StatementId = *bankstatementId
	// actionStatement.UserId = user.Id
	// actionStatement.ConfirmedAt = time
	// actionStatement.ConfirmedByAdminId = &req.CreateByUserId
	// if err := s.serviceAccounting.SetStatementOwnerMatched(*bankstatementId, actionStatement, model.USE_ENDING_NOTI); err != nil {
	// 	log.Println("createCustomerDepositFromOnepay.SetStatementOwnerMatched", err)
	// 	return nil, internalServerError(err)
	// }

	// DECREASE FASTBANK CREDIT 2.FROM create auto DEPOSIT
	// if err := repoAccounting.DecreaseFastbankCredit(1); err != nil {
	// 	log.Println("createCustomerDepositFromOnepay.DECREASE_FASTBANK_CREDIT_ERROR", err)
	// }

	var confirmDeposit model.BankConfirmDepositRequest
	confirmDeposit.TransferAt = &actionAt
	confirmDeposit.ConfirmedAt = actionAt
	confirmDeposit.ConfirmedByAdminId = &adminId
	if err := confirmDepositTransaction(paygateRepo, *transId, confirmDeposit); err != nil {
		log.Println("createCustomerDepositFromOnepay.confirmDepositTransaction", err)
		return nil, internalServerError(err)
	}

	// UPDATE HENG ORDER - BANK SUCCESS
	if err := repo.UpdateOnepayOrderBankSucess(item.Id, *transId, adminId); err != nil {
		log.Println("createCustomerDepositFromOnepay.UpdatePaygateOrderBankSucess", err)
	}

	isFirstDeposit := repo.IsFirstDeposit(user.Id)
	if isFirstDeposit {
		var bonusReq model.UserFirstDepositCreateRequest
		bonusReq.UserId = user.Id
		bonusReq.TransactionId = transId
		bonusReq.TransferAt = actionAt
		bonusReq.Amount = depositAmount
		bonusReq.Remark = "createCustomerDepositFromOnepay"
		if err := SetFirstDepositBonus(accountingRepo, isFirstDeposit, bonusReq); err != nil {
			webhookLogMessage := fmt.Sprintf("createCustomerDepositFromOnepay.userFirstDepositBonus, ERROR: %s", err)
			log.Println("SetFirstDepositBonus", webhookLogMessage)
			// return nil, errors.New(webhookLogMessage)
		}
	}

	// [USER_CREDIT]
	var userCreditReq model.UserTransactionCreateRequest
	userCreditReq.UserId = user.Id
	userCreditReq.RefId = transId
	userCreditReq.TransferAt = &actionAt
	userCreditReq.PromotionId = &promotionWebUserId
	userCreditReq.TypeId = model.CREDIT_TYPE_DEPOSIT
	// userCreditReq.AccountId = &bankAccount.Id
	userCreditReq.Detail = "ONEPAY PAYMENT GATEWAY"
	userCreditReq.Amount = depositAmount
	userCreditReq.CreateBy = &adminId
	userCreditReq.ConfirmBy = &adminId
	userCreditReq.IsAdjustAuto = isAutoDeposit
	userCreditReq.PaymentMerchatId = &pgAccount.ProviderId
	if agentResp, err := repo.IncreaseUserCredit(userCreditReq); err != nil {
		log.Println("createCustomerDepositFromOnepay.IncreaseUserCredit", err)
		return nil, internalServerError(err)
	} else {
		// AGENT_SUCCESS
		if err := repo.UpdateDepositTransactionStatusFromAgent(*transId, *agentResp); err != nil {
			log.Println("createCustomerDepositFromOnepay.UpdateDeporsitTransactionStatusFromAgent", err)
		}
	}

	// [Lucky Wheel] ตอนฝากละได้โบนัส
	var luckyWheelBody model.ActivityLuckyWheelRoundUserRequest
	luckyWheelBody.UserId = user.Id
	luckyWheelBody.ConditionId = model.ACTIVITY_LUCKY_WHEEL_SETTING_CONDITION_DEPOSIT
	luckyWheelBody.ConditionAmount = depositAmount
	if err := CreateRoundActivityLuckyWheel(luckyWheelRepo, luckyWheelBody); err != nil {
		log.Println("createCustomerDepositFromOnepay.CreateRoundActivityLuckyWheel", err)
	}

	// [TIER]
	if err := repo.IncreaseUserTierDepositAmount(user.Id, depositAmount); err != nil {
		log.Println("MatchStatementOwner.IncreaseUserTierDepositAmount", err)
	}

	var checkUserPromotionBody model.CheckUserPromotionBody
	checkUserPromotionBody.UserId = user.Id
	_, err = CheckUserPromotion(promotionWebRepo, checkUserPromotionBody)
	if err != nil {
		log.Println("createCustomerDepositFromOnepay.CheckUserPromotion", err)
	}
	// [turnOver] ถ้ามีการฝากเงิน ให้สร้าง TurnOver
	turnoverShare := repository.NewTurnoverRepository(repo.GetDb())
	if err := CreateTurnOverFromSuccessDeposit(turnoverShare, user.Id, depositAmount, *transId); err != nil {
		log.Println("createCustomerDeposit.CreateTurnOverFromSuccessDeposit", err)
	}

	// ===================================================

	// [ notify]
	notiAtUtc := time.Now().UTC()
	externalNoti.TypeNotify = model.IsDepositAfterCredit
	externalNoti.TransId = transId
	externalNoti.Amount = depositAmount
	externalNoti.MemberCode = user.MemberCode
	externalNoti.UserCredit = user.Credit + depositAmount
	externalNoti.ConfirmedByAdminId = 0
	externalNoti.TransferDateTime = notiAtUtc.Add(7 * time.Hour).Format("2006-01-02 15:04:05")
	externalNoti.ActionTime = notiAtUtc.Add(7 * time.Hour).Format("2006-01-02 15:04:05")
	endTime := notiAtUtc
	elapsed := endTime.Sub(actionAt)
	elapsedSeconds := elapsed.Seconds()
	timeElapsed := fmt.Sprintf("%.2f", elapsedSeconds)
	externalNoti.TimerCounter = timeElapsed

	externalNoti.WebScoket.UserID = user.Id
	externalNoti.WebScoket.Amount = depositAmount
	externalNoti.WebScoket.MemberCode = user.MemberCode
	externalNoti.WebScoket.AlertType = "DEPOSIT"
	if err := repo.UpdateAutoProcessTimer(timeElapsed, *transId); err != nil {
		return nil, nil
	}
	if err := ExternalNotification(notiRepo, externalNoti); err != nil {
		log.Println("FailedNotify", err)
	}
	return nil, nil
}

func approveCustomerWithdrawFromOnepay(repo repository.OnepayRepository, transId int64) (*int64, error) {

	withdrawTrans, err := repo.GetBankTransactionById(transId)
	if err != nil {
		log.Println("approveCustomerWithdrawFromOnepay.GetUserBankDetailById", err)
		return nil, internalServerError(err)
	}

	// ============================= ON_SUCCESS =================================
	confirmAtUtc := time.Now().UTC()
	if withdrawTrans.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		// [create transaction action success]
		var createConfirm model.CreateSuccessTransferWithdrawRequest
		createConfirm.TransactionId = withdrawTrans.Id
		createConfirm.ConfirmedAt = confirmAtUtc
		var setIdAuto int64 = 0
		createConfirm.ConfirmedByAdminId = &setIdAuto
		if _, err := CreateSuccessTransferWithdraw(repository.NewBankingRepository(repo.GetDb()), createConfirm); err != nil {
			log.Println("approveCustomerWithdrawFromOnepay.CreateSuccessTransferWithdraw", err)
			return nil, nil
		}
		// [Show Withdraw USER_TRANSACTION]
		var showUserTrans model.UserTransactionShowUpdate
		showUserTrans.TransactionId = withdrawTrans.Id
		showUserTrans.ConfirmedAt = &createConfirm.ConfirmedAt
		showUserTrans.ConfirmAdminId = createConfirm.ConfirmedByAdminId
		if err := repo.ShowUserTransaction(showUserTrans); err != nil {
			log.Println("approveCustomerWithdrawFromOnepay.ShowUserTransaction", err)
			return nil, nil
		}
		// [update transaction status]
		secondUsed := "0.0" // timing
		if withdrawTrans.IsAutoCredit && withdrawTrans.TransferAt != nil {
			seconds := confirmAtUtc.Sub(withdrawTrans.TransferAt.UTC()).Seconds()
			secondUsed = fmt.Sprintf("%.2f", seconds)
		}
		if err := repo.UpdateTransactionStatusTransferingToSuccess(withdrawTrans.Id, secondUsed); err != nil {
			log.Println("approveCustomerWithdrawFromOnepay.UpdateTransactionStatusTransferingToSuccess", err)
			return nil, internalServerError(err)
		}

		// [notify SUCCESS]
		var externalNoti model.NotifyExternalNotificationRequest
		notiRepo := repository.NewNotificationRepository(repo.GetDb())
		externalNoti.TypeNotify = model.IsWithdrawalCreditSuccess
		externalNoti.TransId = &transId
		externalNoti.Amount = withdrawTrans.CreditAmount
		externalNoti.MemberCode = withdrawTrans.MemberCode
		externalNoti.UserCredit = withdrawTrans.CreditAmount
		externalNoti.ConfirmedByAdminId = 0
		externalNoti.TimerCounter = secondUsed
		externalNoti.TransferDateTime = withdrawTrans.TransferAt.Add(7 * time.Hour).Format("2006-01-02 15:04:05")
		externalNoti.ActionTime = confirmAtUtc.Add(7 * time.Hour).Format("2006-01-02 15:04:05")
		if err := ExternalNotification(notiRepo, externalNoti); err != nil {
			log.Println("FailedNotify", err)
		}

	}
	return nil, nil
}

// func rollbackCustomerWithdrawFromOnepay(repo repository.OnepayRepository, transId int64) (*int64, error) {

// 	withdrawTrans, err := repo.GetBankTransactionById(transId)
// 	if err != nil {
// 		log.Println("rollbackCustomerWithdrawFromOnepay.GetBankTransactionById", err)
// 		return nil, internalServerError(err)
// 	}

// 	// ============================= ON_SUCCESS =================================
// 	if withdrawTrans.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_TRASNFERING {
// 		// [update transaction status]
// 		if err := repo.RollbackTransactionStatusTransferingToConfirmed(withdrawTrans.Id); err != nil {
// 			log.Println("approveCustomerWithdrawFromOnepay.RollbackTransactionStatusTransferingToConfirmed", err)
// 			return nil, internalServerError(err)
// 		}
// 	}
// 	return nil, nil
// }

func (s paygateOnepayService) CreateSystemLog(name string, req interface{}) error {

	// [SYSLOG] HACKED
	_, err := s.repo.CreateSystemLog(model.SystemLogCreateBody{
		Name:   name,
		Status: "HACKING",
		JsonRequest: helper.StructJson(map[string]interface{}{
			"req": req,
		}),
	})
	if err != nil {
		log.Println(name, "CreateSystemLog.ERROR", err.Error())
	}
	return nil
}

func (s paygateOnepayService) CancelWithdrawFromOnepay(transId int64, adminId int64) error {

	withdrawTrans, err := s.repo.GetBankTransactionById(transId)
	if err != nil {
		log.Println("rollbackWithdrawFromOnepay.GetBankTransactionById", err)
		return internalServerError(err)
	}
	if withdrawTrans.TransactionStatusId != model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		return internalServerError(errors.New("ไม่อยู๋ในสถานะที่สามารถยกเลิกได้"))
	}

	user, err := s.repo.GetUserBankDetailById(withdrawTrans.UserId)
	if err != nil {
		return internalServerError(err)
	}

	paygateOrder, err := s.repo.GetDbOnepayOrderByRefId(transId)
	if err != nil {
		log.Println("CancelWithdrawFromOnepay.GetDbOnepayOrderByRefId", err)
		return internalServerError(err)
	}
	waitStatus := "ERROR"
	if paygateOrder.TransactionStatus == nil || *paygateOrder.TransactionStatus != waitStatus {
		return internalServerError(errors.New("ไม่อยู๋ในสถานะที่สามารถยกเลิกได้"))
	}

	returnAmount := withdrawTrans.CreditAmount

	// race condition deposit
	actionAt := time.Now().UTC()
	var createBody model.RaceActionCreateBody
	createBody.Name = "ADMIN_CANCEL_WITHDRAW"
	createBody.JsonRequest = helper.StructJson(map[string]interface{}{
		"transId":      transId,
		"returnAmount": returnAmount,
	})
	createBody.Status = "PENDING"
	createBody.ActionKey = fmt.Sprintf("WITHDRAW_CANCEL_T%sU%dID%d", actionAt.Format("**********"), user.Id, transId)
	createBody.UnlockAt = actionAt.Add(time.Second * 1)
	if _, err := s.repo.GetRaceActionIdByActionKey(createBody.ActionKey); err != nil {
		log.Println("CancelWithdrawFromOnepay.GetRaceActionIdByActionKey", err)
		if err != gorm.ErrRecordNotFound {
			return internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
		}
	} else {
		return internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
	}

	// create race condition
	actionId, err := s.repo.CreateRaceCondition(createBody)
	if err != nil {
		log.Println("CancelWithdrawFromOnepay.ERROR.CreateRaceCondition", err)
		return internalServerError(errors.New("CANNOT_CREATE_ACTION_LUCKY_WHEEL_ROUND"))
	}
	if actionId == 0 {
		return internalServerError(errors.New("CANNOT_CREATE_ACTION_LUCKY_WHEEL_ROUND"))
	}

	// SET ORDER AS ERROR
	remark := "Admin Cancel Withdraw"
	if err := s.repo.UpdateDbOnepayOrderError(paygateOrder.Id, remark); err != nil {
		log.Println("CreateOnepayWithdraw.UpdateDbOnepayOrderError", err)
	}

	if withdrawTrans.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		// [********] check user credit transaction ว่าตรงไหมกับเงื่อนไข (isShow = false)
		userTransaction, err := s.repo.GetUserWithdrawCreditTransactionByRefId(withdrawTrans.Id, returnAmount)
		if err != nil {
			if err.Error() == recordNotFound {
				log.Println("CancelWithdrawCredit.GetUserCreditTransactionById", err)
				return badRequest("ไม่ตรงกับเงือนไข")
			}
			return internalServerError(err)
		}
		if userTransaction != nil {
			var userCreditReq model.UserTransactionCreateRequest
			userCreditReq.UserId = user.Id
			userCreditReq.TransferAt = withdrawTrans.TransferAt
			userCreditReq.RefId = &withdrawTrans.Id
			userCreditReq.TypeId = model.CREDIT_TYPE_DEPOSIT
			userCreditReq.Amount = returnAmount
			IsShow := false
			userCreditReq.IsShow = &IsShow
			userCreditReq.ConfirmBy = &adminId
			if agentResp, err := s.repo.IncreaseUserCredit(userCreditReq); err != nil {
				log.Println("CancelWithdrawCredit.IncreaseUserCredit", err)
				return internalServerError(err)
			} else if !agentResp.AgentSuccess {
				// [********] error from agent will be in failed
				// if err := s.repoAccounting.UpdateDeporsitTransactionStatusFromAgent(*transId, *agentResp); err != nil {
				//     log.Println("CancelWithdrawCredit.UpdateDeporsitTransactionStatusFromAgent", err)
				// }
				// Set FastBank-READ ** ไม่มี เพราะเป็นการสร้างรายการฝากเอง
				agentFail := errors.New("ทำรายการเครดิตไม่สำเร็จ")
				log.Println("CancelWithdrawCredit.IncreaseUserCredit", agentFail)
				return internalServerError(agentFail)
			}
		}
	}

	// remark := "ADMIN_CANCEL_WITHDRAW"
	var updateApprovedBy model.UpdateUserTransactionStatusRequest
	updateApprovedBy.Id = withdrawTrans.Id
	updateApprovedBy.CanceledAt = actionAt
	updateApprovedBy.CanceledByAdminId = adminId
	updateApprovedBy.TransactionStatusId = model.TRANS_STATUS_WITHDRAW_CANCELED
	updateApprovedBy.CancelRemark = &remark
	err = s.repo.UpdateUserTransactionStatus(updateApprovedBy)
	if err != nil {
		return internalServerError(err)
	}

	var createConfirm model.CreateSuccessTransferWithdrawRequest
	createConfirm.TransactionId = withdrawTrans.Id
	createConfirm.ConfirmedAt = actionAt
	createConfirm.ConfirmedByAdminId = &adminId
	if _, err := CreateCanceledTransferWithdraw(repository.NewBankingRepository(s.sharedDb), createConfirm); err != nil {
		return err
	}

	return nil
}

func (s paygateOnepayService) CreateOnepayDepositWebhook(req model.OnepayWebhookRequest) (*int64, error) {

	var createBody model.OnepayWebhookCreateBody
	createBody.Name = "ONEPAY_DEPOSIT_CALLBACK"
	createBody.JsonPayload = req.JsonPayload
	insertId, err := s.repo.CreateOnepayWebhook(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	var remoteResp model.OnepayDepositWebhookResponse
	errJson := json.Unmarshal([]byte(req.JsonPayload), &remoteResp)
	if errJson != nil {
		log.Println("Error unmarshal json", errJson)
		return nil, internalServerError(errJson)
	}

	// fmt.Println("CreateOnepayDepositWebhook.payload", helper.StructJson(remoteResp))

	if strings.ToLower(remoteResp.Type) != "deposit" {
		log.Println("CreateOnepayDepositWebhook.TypeNotDeposit", helper.StructJson(remoteResp))
		return nil, internalServerError(errors.New("TypeNotDeposit"))
	}
	if remoteResp.TxnNo == "" || remoteResp.TxnRefOrderId == "" {
		log.Println("CreateOnepayDepositWebhook.NoRef", helper.StructJson(remoteResp))
		return nil, badRequest("ONEPAY_DEPOSIT_HOOK_NOREF")
	}

	// Service Race Condition by Ref1(MchOrderNo)
	acAtUtc := time.Now().UTC()
	var rcCreateBody model.RaceActionCreateBody
	rcCreateBody.Name = "CreateOnepayDepositWebhook"
	rcCreateBody.JsonRequest = helper.StructJson(req)
	rcCreateBody.Status = "PENDING"
	// ActionKey = Ref2-MINUTE
	rcCreateBody.ActionKey = fmt.Sprintf("PG9D%s-%s", acAtUtc.Format("**********"), remoteResp.TxnNo)
	rcCreateBody.UnlockAt = acAtUtc.Add(time.Second * 1)
	if _, err := s.repo.CreateRaceCondition(rcCreateBody); err != nil {
		return insertId, internalServerError(err)
	}

	// Get Posible Order
	var query model.OnepayOrderListRequest
	query.OrderNo = remoteResp.TxnRefOrderId
	query.TransactionNo = remoteResp.TxnNo
	// query.Amount = fmt.Sprintf("%f", remoteResp.Amount)
	query.Status = "WAIT_PAYMENT"
	query.Limit = 1
	list, _, err := s.repo.GetDbOnepayOrderList(query)
	if err != nil {
		return nil, internalServerError(err)
	}

	// fmt.Println("OnepayDecryptRepayDespositPayload.list", helper.StructJson(list))

	// ** ไม่ได้เช็ค amount เพราะจะมีระบบที่ใช้ ทศนิยม และไม่เท่ากัน แต่ก็ควรเช็คแบบ +- 10%

	if len(list) > 0 {
		for _, item := range list {
			// Update Order
			// "success": true,
			// "status_des": "success_deposit",
			// "type": "deposit",
			successStatus := strings.ToUpper(strings.ToUpper(remoteResp.StatusDes))
			if remoteResp.Success {
				successStatus = "PAID"
			} else {
				log.Println("CreateOnepayDepositWebhook.remoteResp.desc", successStatus)
				successStatus = "ERROR"
			}
			if err := s.repo.ApproveDbOnepayOrder(item.Id, successStatus); err != nil {
				return nil, internalServerError(err)
			}
			// Payment Success, Create Deposit if UserId is not null
			if item.UserId > 0 && item.OrderTypeId == model.ONEPAY_ORDER_TYPE_DEPOSIT {
				if successStatus == "PAID" {
					if _, err := CreateCustomerDepositFromOnepayOrder(s.repo, item.Id, 0); err != nil {
						// WebhookLog
						var createBody2 model.OnepayWebhookCreateBody
						createBody2.Name = "ONEPAY_DEPOSIT_ERROR"
						createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
							"item":   item,
							"method": "createCustomerDepositFromOnepay",
							"error":  err.Error(),
						})
						if _, err = s.repo.CreateOnepayWebhook(createBody2); err != nil {
							log.Println("Error CreateOnepayWebhook.createCustomerDepositFromOnepay", err)
						}
					}
				}
			} else if item.UserId > 0 && item.OrderTypeId == model.ONEPAY_ORDER_TYPE_WITHDRAW {
				if successStatus == "PAID" {
					if _, err := approveCustomerWithdrawFromOnepay(s.repo, *item.RefId); err != nil {
						// WebhookLog
						var createBody2 model.OnepayWebhookCreateBody
						createBody2.Name = "ONEPAY_WITHDRAW_ERROR"
						createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
							"item":   item,
							"method": "approveCustomerWithdrawFromOnepay",
							"error":  err.Error(),
						})
						if _, err = s.repo.CreateOnepayWebhook(createBody2); err != nil {
							log.Println("Error approveCustomerWithdrawFromOnepay.CreateOnepayWebhook", err)
						}
					}
				} else if successStatus == "ERROR" || successStatus == "CANCEL" {
					if err := s.cancelWithdrawFromOnepayWebhookError(item); err != nil {
						log.Println("Error UpdateDbOnepayOrderError", err)
					}
				}
			} else {
				log.Println("ApproveDbOnepayOrder.WithoutItemUpdated", helper.StructJson(item))
			}
		}
	}
	return insertId, nil
}

func (s paygateOnepayService) CreateOnepayWithdrawWebhook(req model.OnepayWebhookRequest) (*int64, error) {

	var createBody model.OnepayWebhookCreateBody
	createBody.Name = "ONEPAY_WITHDRAW_CALLBACK"
	createBody.JsonPayload = req.JsonPayload
	insertId, err := s.repo.CreateOnepayWebhook(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// WITHDRAW
	var remoteResp model.OnepayWithdrawWebhookResponse
	errJson := json.Unmarshal([]byte(req.JsonPayload), &remoteResp)
	if errJson != nil {
		log.Println("Error unmarshal json", errJson)
		return nil, internalServerError(errJson)
	}

	fmt.Println("OnepayDecryptRepayDespositPayload.remoteResp", helper.StructJson(remoteResp))

	if strings.ToLower(remoteResp.Type) != "withdraw" {
		log.Println("CreateOnepayWithdrawWebhook.TypeNotWithdraw", helper.StructJson(remoteResp))
		return nil, internalServerError(errors.New("TypeNotWithdraw"))
	}

	// Service Race Condition by Ref1(MchOrderNo)
	acAtUtc := time.Now().UTC()
	var rcCreateBody model.RaceActionCreateBody
	rcCreateBody.Name = "CreateOnepayWithdrawWebhook"
	rcCreateBody.JsonRequest = helper.StructJson(req)
	rcCreateBody.Status = "PENDING"
	// ActionKey = Ref2-MINUTE
	rcCreateBody.ActionKey = fmt.Sprintf("PG9W%s-%s", acAtUtc.Format("**********"), remoteResp.TxnRefId)
	rcCreateBody.UnlockAt = acAtUtc.Add(time.Second * 1)
	if _, err := s.repo.CreateRaceCondition(rcCreateBody); err != nil {
		return insertId, internalServerError(err)
	}

	// Get Posible Order
	var query model.OnepayOrderListRequest
	query.OrderNo = remoteResp.TxnRefOrderId
	query.TransactionNo = remoteResp.TxnRefId
	// query.Amount = fmt.Sprintf("%f", remoteResp.Amount)
	query.Status = "WAIT_PAYMENT"
	query.Limit = 1
	list, _, err := s.repo.GetDbOnepayOrderList(query)
	if err != nil {
		return nil, internalServerError(err)
	}

	// ** ไม่ได้เช็ค amount เพราะจะมีระบบที่ใช้ ทศนิยม และไม่เท่ากัน แต่ก็ควรเช็คแบบ +- 10%

	if len(list) > 0 {
		for _, item := range list {
			// Update Order
			// "success": true,
			// "status_des": "success_deposit",
			// "type": "deposit",
			successStatus := strings.ToUpper(remoteResp.StatusDes)
			if remoteResp.Success {
				successStatus = "PAID"
			} else {
				log.Println("CreateOnepayWithdrawWebhook.remoteResp.desc", successStatus)
				successStatus = "ERROR"
			}
			if err := s.repo.ApproveDbOnepayOrder(item.Id, successStatus); err != nil {
				return nil, internalServerError(err)
			}
			// Payment Success, Create Deposit if UserId is not null
			if item.UserId > 0 && item.OrderTypeId == model.ONEPAY_ORDER_TYPE_DEPOSIT {
				if successStatus == "PAID" {
					if _, err := CreateCustomerDepositFromOnepayOrder(s.repo, item.Id, 0); err != nil {
						// WebhookLog
						var createBody2 model.OnepayWebhookCreateBody
						createBody2.Name = "ONEPAY_DEPOSIT_ERROR"
						createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
							"item":   item,
							"method": "createCustomerDepositFromOnepay",
							"error":  err.Error(),
						})
						if _, err = s.repo.CreateOnepayWebhook(createBody2); err != nil {
							log.Println("Error CreateOnepayWebhook.createCustomerDepositFromOnepay", err)
						}
					}
				}
			} else if item.UserId > 0 && item.OrderTypeId == model.ONEPAY_ORDER_TYPE_WITHDRAW {
				if successStatus == "PAID" {
					if _, err := approveCustomerWithdrawFromOnepay(s.repo, *item.RefId); err != nil {
						// WebhookLog
						var createBody2 model.OnepayWebhookCreateBody
						createBody2.Name = "ONEPAY_WITHDRAW_ERROR"
						createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
							"item":   item,
							"method": "approveCustomerWithdrawFromOnepay",
							"error":  err.Error(),
						})
						if _, err = s.repo.CreateOnepayWebhook(createBody2); err != nil {
							log.Println("Error approveCustomerWithdrawFromOnepay.CreateOnepayWebhook", err)
						}
					}
				} else if successStatus == "ERROR" || successStatus == "CANCEL" {
					if err := s.cancelWithdrawFromOnepayWebhookError(item); err != nil {
						log.Println("Error UpdateDbOnepayOrderError", err)
					}
				}
			} else {
				log.Println("ApproveDbOnepayOrder.WithoutItemUpdated", helper.StructJson(item))
			}
		}
	}
	return insertId, nil
}

func (s paygateOnepayService) cancelWithdrawFromOnepayWebhookError(pgOrder model.OnepayOrderResponse) error {

	adminId := int64(1)

	withdrawTrans, err := s.repo.GetBankTransactionById(*pgOrder.RefId)
	if err != nil {
		log.Println("rollbackWithdrawFromOnepay.GetBankTransactionById", err)
		return internalServerError(err)
	}
	if withdrawTrans.TransactionStatusId != model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		return internalServerError(errors.New("ไม่อยู๋ในสถานะที่สามารถยกเลิกได้"))
	}

	user, err := s.repo.GetUserBankDetailById(withdrawTrans.UserId)
	if err != nil {
		return internalServerError(err)
	}

	paygateOrder, err := s.repo.GetDbOnepayOrderByRefId(withdrawTrans.Id)
	if err != nil {
		log.Println("CancelWithdrawFromOnepay.GetDbOnepayOrderByRefId", err)
		return internalServerError(err)
	}
	waitStatus := "ERROR"
	if paygateOrder.TransactionStatus == nil || *paygateOrder.TransactionStatus != waitStatus {
		return internalServerError(errors.New("ไม่อยู๋ในสถานะที่สามารถยกเลิกได้"))
	}

	returnAmount := withdrawTrans.CreditAmount

	// race condition deposit
	actionAt := time.Now().UTC()
	var createBody model.RaceActionCreateBody
	createBody.Name = "ADMIN_CANCEL_WITHDRAW"
	createBody.JsonRequest = helper.StructJson(map[string]interface{}{
		"transId":      withdrawTrans.Id,
		"returnAmount": returnAmount,
	})
	createBody.Status = "PENDING"
	createBody.ActionKey = fmt.Sprintf("WITHDRAW_CANCEL_T%sU%dID%d", actionAt.Format("**********"), user.Id, withdrawTrans.Id)
	createBody.UnlockAt = actionAt.Add(time.Second * 1)
	if _, err := s.repo.GetRaceActionIdByActionKey(createBody.ActionKey); err != nil {
		log.Println("CancelWithdrawFromOnepay.GetRaceActionIdByActionKey", err)
		if err != gorm.ErrRecordNotFound {
			return internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
		}
	} else {
		return internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
	}

	// create race condition
	actionId, err := s.repo.CreateRaceCondition(createBody)
	if err != nil {
		log.Println("CancelWithdrawFromOnepay.ERROR.CreateRaceCondition", err)
		return internalServerError(errors.New("CANNOT_CREATE_ACTION_LUCKY_WHEEL_ROUND"))
	}
	if actionId == 0 {
		return internalServerError(errors.New("CANNOT_CREATE_ACTION_LUCKY_WHEEL_ROUND"))
	}

	// SET ORDER AS ERROR
	remark := "Payment Cancel Withdraw"
	if err := s.repo.UpdateDbOnepayOrderError(paygateOrder.Id, remark); err != nil {
		log.Println("CreateOnepayWithdraw.UpdateDbOnepayOrderError", err)
	}

	if withdrawTrans.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		// [********] check user credit transaction ว่าตรงไหมกับเงื่อนไข (isShow = false)
		userTransaction, err := s.repo.GetUserWithdrawCreditTransactionByRefId(withdrawTrans.Id, returnAmount)
		if err != nil {
			if err.Error() == recordNotFound {
				log.Println("CancelWithdrawCredit.GetUserCreditTransactionById", err)
				return badRequest("ไม่ตรงกับเงือนไข")
			}
			return internalServerError(err)
		}
		if userTransaction != nil {
			var userCreditReq model.UserTransactionCreateRequest
			userCreditReq.UserId = user.Id
			userCreditReq.TransferAt = withdrawTrans.TransferAt
			userCreditReq.RefId = &withdrawTrans.Id
			userCreditReq.TypeId = model.CREDIT_TYPE_DEPOSIT
			userCreditReq.Amount = returnAmount
			IsShow := false
			userCreditReq.IsShow = &IsShow
			userCreditReq.ConfirmBy = &adminId
			if agentResp, err := s.repo.IncreaseUserCredit(userCreditReq); err != nil {
				log.Println("CancelWithdrawCredit.IncreaseUserCredit", err)
				return internalServerError(err)
			} else if !agentResp.AgentSuccess {
				// [********] error from agent will be in failed
				// if err := s.repoAccounting.UpdateDeporsitTransactionStatusFromAgent(*transId, *agentResp); err != nil {
				//     log.Println("CancelWithdrawCredit.UpdateDeporsitTransactionStatusFromAgent", err)
				// }
				// Set FastBank-READ ** ไม่มี เพราะเป็นการสร้างรายการฝากเอง
				agentFail := errors.New("ทำรายการเครดิตไม่สำเร็จ")
				log.Println("CancelWithdrawCredit.IncreaseUserCredit", agentFail)
				return internalServerError(agentFail)
			}
		}
	}

	// remark := "ADMIN_CANCEL_WITHDRAW"
	var updateApprovedBy model.UpdateUserTransactionStatusRequest
	updateApprovedBy.Id = withdrawTrans.Id
	updateApprovedBy.CanceledAt = actionAt
	updateApprovedBy.CanceledByAdminId = adminId
	updateApprovedBy.TransactionStatusId = model.TRANS_STATUS_WITHDRAW_CANCELED
	updateApprovedBy.CancelRemark = &remark
	err = s.repo.UpdateUserTransactionStatus(updateApprovedBy)
	if err != nil {
		return internalServerError(err)
	}

	var createConfirm model.CreateSuccessTransferWithdrawRequest
	createConfirm.TransactionId = withdrawTrans.Id
	createConfirm.ConfirmedAt = actionAt
	createConfirm.ConfirmedByAdminId = &adminId
	if _, err := CreateCanceledTransferWithdraw(repository.NewBankingRepository(s.sharedDb), createConfirm); err != nil {
		return err
	}

	return nil
}
