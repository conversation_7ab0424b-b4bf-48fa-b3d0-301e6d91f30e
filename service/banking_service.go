package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"math"
	"net/http"
	"os"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"gorm.io/gorm"
)

type BankingService interface {
	GetTransactionType() ([]model.TransactionTypeResponse, error)
	GetStatementType() ([]model.StatementTypeResponse, error)

	GetBankStatementById(req model.GetByIdRequest) (*model.BankStatement, error)
	GetBankStatements(req model.BankStatementListRequest) (*model.SuccessWithPagination, error)
	GetBankStatementSummary(req model.BankStatementListRequest) (*model.BankStatementSummary, error)
	CreateBankStatement(data model.BankStatementCreateBody) error
	MatchStatementOwner(req model.BankStatementMatchRequest) error
	IgnoreStatementOwner(id int64, req model.BankStatementMatchRequest) error
	DeleteBankStatement(id int64) error

	GetBankTransactionById(req model.BankTransactionGetRequest) (*model.BankTransaction, error)
	GetBankTransactions(req model.BankTransactionListRequest) (*model.SuccessWithPagination, error)

	GetBankDepositTransStatusCounts(req model.GetBankTransactionDepositCountRequest) (*model.BankDepositTransStatusCounts, error)
	GetBankWithdrawTransStatusCounts(req model.GetBankTransactionWithdrawCountRequest) (*model.BankWithdrawTransStatusCounts, error)

	ConfirmDepositTransaction(id int64, req model.BankConfirmDepositRequest) error
	ConfirmDepositCredit(id int64, req model.BankConfirmDepositRequest) error
	ConfirmWithdrawTransaction(id int64, req model.BankConfirmCreditWithdrawRequest) error
	ConfirmWithdrawTransfer(id int64, req model.BankConfirmTransferWithdrawRequest) error
	CancelPendingTransaction(id int64, data model.BankTransactionCancelBody) error

	GetMemberByCode(code string) (*model.MemberForDropdownResponse, error)
	GetMembers(req model.MemberListRequest) (*model.SuccessWithPagination, error)
	GetMemberTransactions(req model.MemberTransactionListRequest) (*model.SuccessWithPagination, error)
	GetMemberTransactionSummary(req model.MemberTransactionListRequest) (*model.MemberTransactionSummary, error)
	GetMemberStatementById(req model.GetByIdRequest) (*model.MemberStatementResponse, error)
	GetMemberStatements(req model.MemberStatementListRequest) (*model.SuccessWithPagination, error)
	ProcessMemberDepositCredit(userId int64, amount float64) error
	ProcessMemberWithdrawCredit(userId int64, amount float64) error
	ProcessMemberBonusCredit(userId int64, amount float64) error
	ProcessMemberGetbackCredit(userId int64, amount float64) error

	// back office
	GetPossibleOwnersByStatementId(id int64) (*model.SuccessWithPagination, error)
	GetBankTransactionDepositList(req model.GetBankTransactionDepositListRequest) (*model.SuccessWithPagination, error)
	GetDepositTransactionById(transId int64) (*model.GetBankTransactionDepositListResponse, error)
	GetTransactionWithdrawById(transId int64) (*model.GetBankTransactionWithdrawListResponse, error)
	UpdateIgnoreDeposit(req model.CreateIgnoredTransacionRequest) (*int64, error)
	UploadImageToCloudflareDepositSlip(imageFileBody *http.Request) (*model.FileUploadResponse, error)
	UploadImageToCloudflareBonusSlip(imageFileBody *http.Request) (*model.FileUploadResponse, error)
	CreateDepositRecord(req model.CreateDepositRecordRequest) (*int64, error)
	CreateFreeBonus(req model.CreateFreeBonusRequest) (*int64, error)
	// GetBankTransactionSuccess(req model.GetBankTransactionSuccessListRequest) (*model.SuccessWithPagination, error)
	// RemovedTransaction(id int64, req model.RemovedTransactionRequest) error
	// RemovedSuccessTransactionList(req model.RequestRemovedSuccessTransactionList) (*model.SuccessWithPagination, error)
	UploadImageToCloudflareWithdrawSlip(imageFileBody *http.Request) (*model.FileUploadResponse, error)

	// web user
	GetWebUserTransactionList(req model.GetUserTransactionListRequest) (*model.SuccessWithPagination, error)

	//web withdraw
	UserCreateWithdrawTransaction(req model.UserCreateWithdrawTransactionRequest) (*model.FastbankWithdrawTransactionResponse, error)

	//withdraw admin
	CreateWithdrawRecord(req model.CreateWithdrawRecordRequest) (*model.FastbankWithdrawTransactionResponse, error)
	CreateWithdrawPullCreditBack(req model.CreateWithdrawPullCreditBackRequest) (*int64, error)
	CreateUserCancelCredit(req model.CreateUserCancelCreditRequest) (*int64, error)
	//back office ปุ่ม action withdraw
	CreateAutoWithdraw(req model.CreateAutoWithdrawRequest) (*model.FastbankWithdrawTransactionResponse, error)
	CreateTransWithdrawWithSelectedAccount(req model.CreateTransWithdrawWithSelectedAccountRequest) (*model.FastbankWithdrawTransactionResponse, error)
	CreateTransWithdrawWithExternalAccount(req model.CreateTransWithdrawWithExternalAccountRequest) (*int64, error)
	CreateTransWithdrawWithManualAccount(req model.CreateTransWithdrawWithManualAccountRequest) (*int64, error)
	GetBankTransactionWithdrawList(req model.GetBankTransactionWithdrawListRequest) (*model.SuccessWithPagination, error)
	CancelWithdrawCredit(req model.CancelWithdrawCreditRequest) (*int64, error)
	CheckTransferingWithdraw(req model.WithdrawCheckTransferingRequest) (model.WithdrawCheckTransferingResponse, error)
	CreateFristTimeDepositRecord(req model.CreateFristTimeDepositRecordRequest) (*int64, error)
	ConfirmWithdrawTransactionAnyStatus(req model.CreateAutoWithdrawRequest) error
	ContinueConfirmWithdrawPaymentOnly(req model.CreateAutoWithdrawRequest) (*int64, error)
	// TEST
	IsFirstDeposit(userId int64) (bool, error)
	SendWebSocket(userId int64, amount float64, membercode string, alertType string) error

	// Check Duplicate web user
	WebCheckUserDuplicateWithdrawProeccing(userId int64) (*model.CheckUserDuplicateWithdrawResponse, error)

	// FirstDeposit
	GetBankTransactionFirstTimeDeposit(req model.GetBankTransactionFirstTimeDepositRequest) (*model.SuccessWithPagination, error)

	// retry
	RetryDepositAgent(req model.RetryDepositAgentRequest) (string, error)

	CheckAdminDuplicateWithdrawList(req model.CheckAdminDuplicateWithdrawListRequest) ([]model.CheckAdminDuplicateWithdrawList, error)

	//detail
	GetBankTransactionExternalDetailByBankTransactionId(id int64) ([]model.BankTransactionExternalDetailGetByBankTransactionIdResponse, error)

	UserDepositByUploadFile(body model.CreateDepositFromUserUploadRequest) (*int64, error)
	UserDepositBySlip(body model.CreateDepositConfirmedFromUserRequest) (*int64, error)
	CheckScammerSlipRequest(req model.CheckScammerSlipRequest) *model.CheckScammerSlipResponse

	//test
	CheckOtherExistTurnOver(userId int64) (string, float64, error)
	TurnOverWithdrawChecker(userId int64, amount float64) error

	// cancel all turn
	CanceledAllTurn(req model.ClearAllTurnOver) error
	EmptyUserTurnList(req model.EmptyUserTurnListRequest) error
	// laos bank
	CreateDepositLaosBankRecord(req model.CreateDepositLaosBankRecordRequest) (*int64, error)

	// check account fastbank
	GetAccountInfoFastbank(body model.AccountInfoFastbankRequest) (*model.AccountInfoFastbankResponse, error)

	// deposit smsmode list
	GetSmsModeDepositList(req model.GetSmsModeDepositListRequest) (*model.SuccessWithPagination, error)
	AdminConfirmDepositSmsMode(req model.AdminConfirmDepositSmsModeRequest) (*int64, error)
	AdminCancelDepositSmsMode(req model.AdminConfirmCanceledSmsModeRequest) (*int64, error)

	// upload s3
	UploadImageToS3DepositSlip(imageFileBody *http.Request) (*model.FileUploadResponse, error)
	UploadImageToS3BonusSlip(imageFileBody *http.Request) (*model.FileUploadResponse, error)
	UploadImageToS3WithdrawSlip(imageFileBody *http.Request) (*model.FileUploadResponse, error)

	// bot
	BotCreateWithdrawPullCreditBack(req model.BotCreateWithdrawPullCreditBackRequest) (*model.BotCreateWithdrawPullCreditBackResponse, error)
}

type bankingService struct {
	repoBanking               repository.BankingRepository
	repoAccounting            repository.AccountingRepository
	repoAgentConnect          repository.AgentConnectRepository
	serviceAccounting         AccountingService
	serviceUser               UserService
	serviceNoti               NotificationService
	serviceAf                 AffiliateService
	serviceTurnover           TurnoverService
	serviceAl                 AllianceService
	activityLuckyWheelService ActivityLuckyWheelService
	promotionWebService       PromotionWebService
	couponCashService         CouponCashService
	SharedDb                  *gorm.DB
}

func NewBankingService(
	repoBanking repository.BankingRepository,
	repoAccounting repository.AccountingRepository,
	repoAgentConnect repository.AgentConnectRepository,
	serviceAccounting AccountingService,
	serviceUser UserService,
	serviceNoti NotificationService,
	serviceAf AffiliateService,
	serviceTurnover TurnoverService,
	serviceAl AllianceService,
	serviceActivityLuckyWheel ActivityLuckyWheelService,
	servicePromotionWeb PromotionWebService,
	serviceCouponCash CouponCashService,
	SharedDb *gorm.DB,
) BankingService {
	return &bankingService{repoBanking, repoAccounting, repoAgentConnect, serviceAccounting, serviceUser, serviceNoti, serviceAf, serviceTurnover, serviceAl, serviceActivityLuckyWheel, servicePromotionWeb, serviceCouponCash, SharedDb}
}

func (s *bankingService) IsFirstDeposit(userId int64) (bool, error) {

	// IsFirstDeposit
	// bank_transaction.is_first_deposit => user_first_deposit.user_id
	result := s.repoBanking.IsFirstDeposit(userId)

	return result, nil
}

func (s *bankingService) GetBankStatementById(req model.GetByIdRequest) (*model.BankStatement, error) {

	record, err := s.repoBanking.GetBankStatementById(req.Id)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(bankStatementferNotFound)
		}
		return nil, internalServerError(err)
	}
	return record, nil
}

func (s *bankingService) GetBankStatements(req model.BankStatementListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}
	records, err := s.repoBanking.GetBankStatements(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	return records, nil
}

func (s *bankingService) GetBankStatementSummary(req model.BankStatementListRequest) (*model.BankStatementSummary, error) {

	records, err := s.repoBanking.GetBankStatementSummary(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	return records, nil
}

func (s *bankingService) CreateBankStatement(data model.BankStatementCreateBody) error {

	toAccount, err := s.repoAccounting.GetBankAccountById(data.AccountId)
	if err != nil {
		log.Println(err)
		return badRequest("Invalid Bank Account")
	}

	var body model.BankStatementCreateBody
	body.AccountId = toAccount.Id
	if data.StatementTypeId == model.STATEMENT_TYPE_TRANSFER_IN {
		body.Amount = data.Amount
	} else if data.StatementTypeId == model.STATEMENT_TYPE_TRANSFER_OUT {
		body.Amount = data.Amount * -1
	} else {
		return badRequest("Invalid Transfer Type")
	}
	body.Detail = data.Detail
	body.StatementTypeId = data.StatementTypeId
	body.TransferAt = data.TransferAt
	body.StatementStatusId = model.STATEMENT_STATUS_PENDING
	if _, err = s.repoBanking.CreateBankStatement(body); err != nil {
		return internalServerError(err)
	}
	return nil
}

func (s *bankingService) MatchStatementOwner(req model.BankStatementMatchRequest) error {

	startProcess := time.Now()
	statement, err := s.repoBanking.GetBankStatementById(req.StatementId)
	if err != nil {
		return internalServerError(err)
	}
	if statement.StatementStatusId != model.STATEMENT_STATUS_PENDING {
		return badRequest("Statement is not pending")
	}
	member, err := s.repoBanking.GetMemberById(req.UserId)
	if err != nil {
		return badRequest("Invalid Member")
	}
	log.Println("MatchStatementOwner.member", helper.StructJson(member))

	trans, err := s.repoBanking.GetTransactionByStatementId(statement.Id)
	if err != nil {
		return internalServerError(err)
	}

	if member.MemberCode == "" {
		memberCode, err := s.serviceUser.GenUniqueUserMemberCode(member.Id)
		if err != nil {
			return internalServerError(err)
		}
		member.MemberCode = *memberCode
	}

	if member.UserTypeName == "ALLIANCE" {
		if err := s.serviceAl.NoUseAlUpdateCommission(member.Id, statement.Amount); err != nil {
			return internalServerError(err)
		}
	} else if member.UserTypeName == "NONE" {
		// ถ้าฝากครั้งแรก จะได้เป็น AFFILIATE + REFER COMMISION
		if err := s.serviceAccounting.UserFirstDepositCommission(*member, statement.Amount); err != nil {
			log.Println("MatchStatementOwner.UserFirstDepositCommission.ERROR", err)
			return internalServerError(err)
		}
	}

	jsonBefore, _ := json.Marshal(statement)

	// TransAction
	var createBody model.CreateBankStatementActionBody
	createBody.StatementId = statement.Id
	createBody.UserId = member.Id
	createBody.ActionType = "CONFIRMED"
	createBody.AccountId = statement.AccountId
	createBody.JsonBefore = string(jsonBefore)
	createBody.ConfirmedAt = req.ConfirmedAt
	createBody.ConfirmedByAdminId = req.ConfirmedByAdminId

	var lineResultCredit float64
	if err := s.repoBanking.CreateStatementAction(createBody); err == nil {
		updateBodyStatment := model.BankStatementUpdateBody{
			Id:                statement.Id,
			StatementStatusId: model.STATEMENT_STATUS_CONFIRMED,
		}
		if err := s.repoBanking.UpdateBankStatementStatus(updateBodyStatment); err != nil {
			return internalServerError(err)
		}
		// ConfirmDepositTransaction try use only this
		statementbody := model.BankStatementCreateBody{
			Id:                statement.Id,
			AccountId:         statement.AccountId,
			ExternalId:        &statement.ExternalId,
			Amount:            statement.Amount,
			Detail:            statement.Detail,
			FromBankId:        statement.FromBankId,
			FromAccountNumber: statement.FromAccountNumber,
			StatementTypeId:   statement.StatementTypeId,
			TransferAt:        statement.TransferAt,
			StatementStatusId: statement.StatementTypeId,
		}
		var admin model.ApprovedByAdmin

		admin.Id = *req.ConfirmedByAdminId

		// เปลี่ยนเป็น update โดยการหาจาก statment id ใน bank_transaction where statement.Id = statementId
		tranId, err := s.serviceAccounting.UpdateDepositTransactionOwner(*member, statementbody, admin)
		if err != nil {
			return internalServerError(err)
		}

		// gameRes, err := s.repoAgentConnect.DepositAgent(member.MemberCode, member.Id, statement.Amount+trans.BonusAmount, *tranId)
		// if err != nil {
		// 	return internalServerError(err)
		// }

		// ปกติ
		var confirmTransReq model.BankConfirmDepositRequest
		depositTime := time.Now()
		confirmTransReq.ConfirmedAt = depositTime
		var setIdAuto int64 = 0
		if req.ConfirmedByAdminId != nil {
			confirmTransReq.ConfirmedByAdminId = req.ConfirmedByAdminId
		} else {
			confirmTransReq.ConfirmedByAdminId = &setIdAuto
		}
		confirmTransReq.TransferAt = &statement.TransferAt

		if err := s.ConfirmDepositTransaction(*tranId, confirmTransReq); err != nil {
			log.Print(err.Error())
			return internalServerError(err)
		}

		var promotionWebUserId int64
		GetUserPromotion, _ := s.promotionWebService.GetDepositCurrentProcessingUserPromotion(member.Id)
		if GetUserPromotion != nil {
			promotionWebUserId = GetUserPromotion.Id

			errUpdate := s.repoBanking.UpdatePromotionToBankTransaction(*tranId, promotionWebUserId)
			if errUpdate != nil {
				log.Println("MatchStatementOwner.UpdatePromotionToBankTransaction", err)
			}
		}

		var userCreditReq model.UserTransactionCreateRequest
		userCreditReq.UserId = member.Id
		userCreditReq.RefId = tranId
		userCreditReq.TransferAt = &statement.TransferAt
		userCreditReq.PromotionId = &promotionWebUserId
		userCreditReq.TypeId = model.CREDIT_TYPE_DEPOSIT
		userCreditReq.AccountId = &trans.ToAccountId
		userCreditReq.Detail = fmt.Sprintf("%s:%s => %s:%s", trans.FromBankName, trans.FromAccountNumber, trans.ToBankName, trans.ToAccountName)
		userCreditReq.Amount = statement.Amount
		userCreditReq.BonusAmount = trans.BonusAmount
		userCreditReq.ConfirmBy = req.ConfirmedByAdminId
		// userCreditReq.CreateBy = &trans.CreatedByAdminId
		if agentResp, err := s.repoAccounting.IncreaseUserCredit(userCreditReq); err != nil {
			var updateStatement model.BankStatementUpdateBody
			updateStatement.Id = statement.Id
			updateStatement.StatementStatusId = model.STATEMENT_STATUS_PENDING
			if err := s.repoAccounting.UpdateBankStatementStatus(updateStatement); err != nil {
				log.Println("MatchStatementOwner.UpdateBankStatementStatus", err)
			}
			log.Println("MatchStatementOwner.IncreaseUserCredit", err)
			return internalServerError(err)
		} else {
			// FASTBANK_SUCCESS
			lineResultCredit = agentResp.AgentAfterAmount
			if err := s.repoAccounting.UpdateDepositTransactionStatusFromAgent(*tranId, *agentResp); err != nil {
				log.Println(err)
			}
			// Set FastBank-READ
			var setReadRequest model.ExternalStatementSetReadBody
			setReadRequest.AccountNo = trans.ToAccountNumber
			setReadRequest.StatementId = statement.ExternalId
			setReadRequest.UsedCredit = true
			if err := s.repoAccounting.SetExternalStatementRead(setReadRequest); err != nil {
				log.Println("CreateBankStatementFromWebhook.SetExternalStatementRead", err)
			}
		}

		isFirstDeposit := s.repoBanking.IsFirstDeposit(member.Id)
		if isFirstDeposit {
			// [********] ไม่มีโบนัสฝากครั้งแรก
			// [turnOver] ตอนฝากละได้โบนัส
			// actionAt := time.Now()
			// bonusAmount := statement.Amount
			// turnOverAmount := statement.Amount
			// var createBody model.TurnoverUserStatementCreateBody
			// createBody.UserId = member.Id
			// createBody.TypeId = model.TURNOVER_TYPE_PROMOTION_FIRST_DEPOSIT
			// createBody.RefTypeId = *tranId
			// createBody.Name = model.TURNOVER_CATE_FIRST_DEPOSIT
			// createBody.PromotionName = "โบนัสฝากครั้งแรก"
			// createBody.BonusAmount = bonusAmount
			// if bonusAmount > 0 {
			// 	createBody.StatusId = model.TURNOVER_STATEMENT_STATUS_PENDING
			// } else {
			// 	createBody.StatusId = model.TURNOVER_STATEMENT_STATUS_COMPLETED
			// }
			// createBody.StartTurnAmount = turnOverAmount
			// createBody.StartTurnAt = &actionAt
			// createBody.TotalTurnAmount = turnOverAmount
			// if _, err := s.repoBanking.CreateTurnoverUserStatement(createBody); err != nil {
			// 	return internalServerError(err)
			// }

			var createFirstDeposit model.UserFirstDepositCreateBody
			createFirstDeposit.UserId = member.Id
			createFirstDeposit.TransferAt = statement.TransferAt
			createFirstDeposit.Amount = statement.Amount
			createFirstDeposit.Remark = "MatchStatementOwner"
			if _, err := s.repoBanking.SetUserFirstDeposit(createFirstDeposit, tranId); err != nil {
				log.Println("MatchStatementOwner.SetUserFirstDeposit", err)
			}
		}

		// confirm by admin
		var updateApprovedBy model.UpdateConfirmAutoWithdrawBody
		updateApprovedBy.ConfirmedAt = time.Now()
		updateApprovedBy.ConfirmedByAdminId = req.ConfirmedByAdminId
		if req.ConfirmedByAdminId != nil {
			updateApprovedBy.ConfirmedByAdminId = req.ConfirmedByAdminId
		} else {
			updateApprovedBy.ConfirmedByAdminId = &setIdAuto
		}
		err = s.repoBanking.UpdateAdminAndTransactionStatus(*tranId, updateApprovedBy)
		if err != nil {
			return nil
		}
		// ===========================================================
		// [PromtionChecker]
		var checkUserPromotionBody model.CheckUserPromotionBody
		checkUserPromotionBody.UserId = member.Id
		_, err = s.promotionWebService.CheckUserPromotion(checkUserPromotionBody)
		if err != nil {
			log.Println("MatchStatementOwner.CheckUserPromotion", err)
		}
		// [Lucky Wheel] ตอนฝากละได้โบนัส
		var luckyWheelBody model.ActivityLuckyWheelRoundUserRequest
		luckyWheelBody.UserId = member.Id
		luckyWheelBody.ConditionId = model.ACTIVITY_LUCKY_WHEEL_SETTING_CONDITION_DEPOSIT
		luckyWheelBody.ConditionAmount = statement.Amount
		if err := s.activityLuckyWheelService.CreateRoundActivityLuckyWheel(luckyWheelBody); err != nil {
			log.Println("MatchStatementOwner.CreateRoundActivityLuckyWheel", err)
		}

		// [TIER]
		if err := s.repoAccounting.IncreaseUserTierDepositAmount(member.Id, statement.Amount); err != nil {
			log.Println("MatchStatementOwner.IncreaseUserTierDepositAmount", err)
		}

		// [turnOver] ถ้ามีการฝากเงิน ให้สร้าง TurnOver
		if err := CreateTurnOverFromSuccessDeposit(repository.NewTurnoverRepository(s.SharedDb), member.Id, statement.Amount, *tranId); err != nil {
			log.Println("CreateBankStatementFromWebhookAndAuto.CreateTurnOverFromSuccessDeposit", err)
		}

		// end - start
		endTime := time.Now()
		elapsed := endTime.Sub(startProcess)
		elapsedSeconds := elapsed.Seconds()
		timeElapsed := fmt.Sprintf("%.2f", elapsedSeconds)

		var externalNoti model.NotifyExternalNotificationRequest
		externalNoti.TypeNotify = model.IsDepositAfterCredit
		externalNoti.TransId = tranId
		externalNoti.Amount = statement.Amount
		externalNoti.MemberCode = member.MemberCode
		externalNoti.UserCredit = lineResultCredit
		externalNoti.TransferDateTime = statement.TransferAt.Add(7 * time.Hour).Format("2006-01-02 15:04:05")
		externalNoti.ActionTime = time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02 15:04:05")

		externalNoti.WebScoket.UserID = member.Id
		externalNoti.WebScoket.Amount = statement.Amount + trans.BonusAmount
		externalNoti.WebScoket.MemberCode = member.MemberCode
		externalNoti.WebScoket.AlertType = "DEPOSIT"
		var setConfirmId int64
		if req.ConfirmedByAdminId != nil {
			setConfirmId = *req.ConfirmedByAdminId
		} else {
			setConfirmId = 0
		}
		externalNoti.ConfirmedByAdminId = setConfirmId
		externalNoti.TimerCounter = timeElapsed
		if err := s.serviceNoti.ExternalNotification(externalNoti); err != nil {
			return nil
		}

	} else {
		return internalServerError(err)
	}
	return nil
}

func (s *bankingService) IgnoreStatementOwner(id int64, req model.BankStatementMatchRequest) error {

	statement, err := s.repoBanking.GetBankStatementById(id)
	if err != nil {
		return internalServerError(err)
	}
	if statement.StatementStatusId != model.STATEMENT_STATUS_PENDING {
		return badRequest("Statement is not pending")
	}

	var body model.BankStatementUpdateBody
	body.StatementStatusId = model.STATEMENT_STATUS_IGNORED
	jsonBefore, _ := json.Marshal(statement)

	// TransAction
	var createBody model.CreateBankStatementActionBody
	createBody.StatementId = statement.Id
	createBody.ActionType = "IGNORED"
	createBody.AccountId = statement.AccountId
	createBody.JsonBefore = string(jsonBefore)
	createBody.ConfirmedAt = req.ConfirmedAt
	createBody.ConfirmedByAdminId = req.ConfirmedByAdminId
	if err := s.repoBanking.CreateStatementAction(createBody); err == nil {
		if err := s.repoBanking.IgnoreStatementOwner(id, body); err != nil {
			return internalServerError(err)
		}
	} else {
		return internalServerError(err)
	}
	return nil
}

func (s *bankingService) DeleteBankStatement(id int64) error {

	_, err := s.repoBanking.GetBankStatementById(id)
	if err != nil {
		return internalServerError(err)
	}

	if err := s.repoBanking.DeleteBankStatement(id); err != nil {
		return internalServerError(err)
	}
	return nil
}

func (s *bankingService) GetBankTransactionById(req model.BankTransactionGetRequest) (*model.BankTransaction, error) {

	record, err := s.repoBanking.GetBankTransactionById(req.Id)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(bankTransactionferNotFound)
		}
		return nil, internalServerError(err)
	}
	return record, nil
}

func (s *bankingService) GetBankTransactions(req model.BankTransactionListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	records, err := s.repoBanking.GetBankTransactions(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	return records, nil
}

func (s *bankingService) GetBankDepositTransStatusCounts(req model.GetBankTransactionDepositCountRequest) (*model.BankDepositTransStatusCounts, error) {

	records, err := s.repoBanking.CountActiveDepositStatus(req)
	if err != nil {
		return nil, internalServerError(err)
	}

	var result model.BankDepositTransStatusCounts
	result.AllCount = 0
	result.PendingAllCount = 0
	result.PendingCount = 0
	result.PendingCreditCount = 0
	result.CreditApprovedCount = 0
	result.CreditRejected = 0

	for _, record := range records {
		result.AllCount += record.Count
		if record.TransactionStatusId == model.TRANS_STATUS_PENDING {
			result.PendingAllCount += record.Count
			result.PendingCount += record.Count

		} else if record.TransactionStatusId == model.TRANS_STATUS_DEPOSIT_PENDING_CREDIT {
			result.PendingAllCount += record.Count
			result.PendingCreditCount += record.Count
		} else if record.TransactionStatusId == model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED {
			result.CreditApprovedCount += record.Count

		} else if record.TransactionStatusId == model.TRANS_STATUS_DEPOSIT_CREDIT_REJECTED {
			result.CreditRejected += record.Count

		} else if record.TransactionStatusId == model.TRANS_STATUS_DEPOSIT_PENDING_SLIP {
			result.PendingAllCount += record.Count
			result.PendingCount += record.Count

		} else if record.TransactionStatusId == model.TRANS_STATUS_DEPOSIT_IGNORE {
			result.CreditRejected += record.Count
		} else if record.TransactionStatusId == model.TRANS_STATUS_DEPOSIT_PENDING_MULTIUSER {
			result.PendingAllCount += record.Count
			result.PendingCount += record.Count
		}
	}
	return &result, nil
}

func (s *bankingService) GetBankWithdrawTransStatusCounts(req model.GetBankTransactionWithdrawCountRequest) (*model.BankWithdrawTransStatusCounts, error) {

	records, err := s.repoBanking.CountActiveWithdrawStatus(req)
	if err != nil {
		return nil, internalServerError(err)
	}

	var result model.BankWithdrawTransStatusCounts
	result.AllCount = 0
	result.PendingAllCount = 0
	result.PendingCreditCount = 0
	result.PendingTransferCount = 0
	result.SuccessCount = 0
	result.FailedCount = 0
	result.CanceledCount = 0
	result.TransferIngCount = 0

	for _, record := range records {
		result.AllCount += record.Count
		if record.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_PENDING {
			result.PendingAllCount += record.Count
			result.PendingCreditCount += record.Count
		} else if record.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_OVER_BUDGET {
			result.PendingAllCount += record.Count
			result.PendingTransferCount += record.Count
		} else if record.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_APPROVED {
			result.PendingAllCount += record.Count
			result.PendingTransferCount += record.Count
		} else if record.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_REJECTED {
			result.FailedCount += record.Count
		} else if record.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_CANCELED {
			result.CanceledCount += record.Count
		} else if record.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_FAILED {
			result.FailedCount += record.Count
		} else if record.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_SUCCESS {
			result.SuccessCount += record.Count
		} else if record.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_OVER_MAX {
			result.PendingAllCount += record.Count
			result.PendingTransferCount += record.Count
		} else if record.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_UNSURE {
			result.PendingAllCount += record.Count
			result.PendingTransferCount += record.Count
		} else if record.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_TRASNFERING {
			result.PendingAllCount += record.Count
			result.TransferIngCount += record.Count
		}
	}

	return &result, nil
}

func (s *bankingService) GetNewAutoWithdrawCondition(body model.BankTransactionCreateBody, fromAccount model.BankAccount) (*model.BankAutoWithdrawCondition, error) {

	var autoWithdrawCondition model.BankAutoWithdrawCondition
	autoWithdrawCondition.UserId = body.UserId
	autoWithdrawCondition.TransStatusId = body.TransactionStatusId
	autoWithdrawCondition.CreditAmount = body.CreditAmount
	autoWithdrawCondition.FromAccountId = fromAccount.Id

	// if fromAccount.AutoWithdrawCreditFlag == "auto" {
	// 	autoWithdrawCondition.AutoWithdrawConfirmFlag = "auto"
	// }
	// if fromAccount.AutoWithdrawCreditFlag == "auto" {
	// 	autoWithdrawCondition.AutoWithdrawCreditFlag = "auto"
	// }

	return &autoWithdrawCondition, nil
}

func (s *bankingService) GetAutoWithdrawCondition(body model.BankTransaction, fromAccount model.BankAccount) (*model.BankAutoWithdrawCondition, error) {

	var autoWithdrawCondition model.BankAutoWithdrawCondition
	autoWithdrawCondition.UserId = body.UserId
	autoWithdrawCondition.TransStatusId = body.TransactionStatusId
	autoWithdrawCondition.CreditAmount = body.CreditAmount
	autoWithdrawCondition.FromAccountId = fromAccount.Id

	// if fromAccount.AutoWithdrawCreditFlag == "auto" {
	// 	autoWithdrawCondition.AutoWithdrawConfirmFlag = "auto"
	// }
	// if fromAccount.AutoWithdrawCreditFlag == "auto" {
	// 	autoWithdrawCondition.AutoWithdrawCreditFlag = "auto"
	// }

	return &autoWithdrawCondition, nil
}

func (s *bankingService) SetAutoWithdrawCondition(transId int64, curCondition *model.BankAutoWithdrawCondition) error {

	// Fix Same request on auto+auto
	transaction, err := s.repoBanking.GetBankTransactionById(transId)
	if err != nil {
		return internalServerError(err)
	}
	if transaction.TransactionTypeId != model.TRANSACTION_TYPE_WITHDRAW && transaction.TransactionTypeId != model.TRANSACTION_TYPE_CREDITBACK && transaction.TransactionTypeId != model.TRANSACTION_TYPE_CREDITCANCEL {
		return badRequest("Transaction is not withdraw")
	}
	curCondition.TransId = transaction.Id
	curCondition.UserId = transaction.UserId
	curCondition.TransStatusId = transaction.TransactionStatusId
	curCondition.CreditAmount = transaction.CreditAmount
	curCondition.FromAccountId = transaction.FromAccountId

	// if fromAccount.AutoWithdrawCreditFlag == "auto" {
	// 	autoWithdrawCondition.AutoWithdrawConfirmFlag = "auto"
	// }
	// if fromAccount.AutoWithdrawCreditFlag == "auto" {
	// 	autoWithdrawCondition.AutoWithdrawCreditFlag = "auto"
	// }

	return nil
}

func (s *bankingService) ProcessAutoWithdrawCondition(req model.BankAutoWithdrawCondition) error {
	var setIdAuto int64 = 0
	if req.TransStatusId == model.TRANS_STATUS_PENDING {
		var confirmReq model.BankConfirmCreditWithdrawRequest
		confirmReq.FromAccountId = &req.FromAccountId
		confirmReq.CreditAmount = &req.CreditAmount
		confirmReq.ConfirmedAt = time.Now()
		confirmReq.ConfirmedByAdminId = &setIdAuto
		actionErr := s.ConfirmWithdrawTransaction(req.TransId, confirmReq)
		if actionErr != nil {
			return internalServerError(actionErr)
		}
	} else if req.TransStatusId == model.TRANS_STATUS_DEPOSIT_PENDING_CREDIT {
		var confirmReq model.BankConfirmCreditWithdrawRequest
		confirmReq.FromAccountId = &req.FromAccountId
		confirmReq.CreditAmount = &req.CreditAmount
		confirmReq.ConfirmedAt = time.Now()
		confirmReq.ConfirmedByAdminId = &setIdAuto
		actionErr := s.ConfirmWithdrawTransaction(req.TransId, confirmReq)
		if actionErr != nil {
			return internalServerError(actionErr)
		}
	} else if req.TransStatusId == model.TRANS_STATUS_WITHDRAW_PENDING {
		var confirmReq model.BankConfirmTransferWithdrawRequest
		confirmReq.FromAccountId = &req.FromAccountId
		confirmReq.ConfirmedAt = time.Now()
		confirmReq.ConfirmedByAdminId = &setIdAuto
		actionErr := s.ConfirmWithdrawTransfer(req.TransId, confirmReq)
		if actionErr != nil {
			return internalServerError(actionErr)
		}
	}
	return nil
}

func (s *bankingService) CancelPendingTransaction(id int64, data model.BankTransactionCancelBody) error {

	transaction, err := s.repoBanking.GetBankTransactionById(id)
	if err != nil {
		return internalServerError(err)
	}
	if transaction.TransactionStatusId != model.TRANS_STATUS_PENDING && transaction.TransactionStatusId != model.TRANS_STATUS_DEPOSIT_PENDING_CREDIT && transaction.TransactionStatusId != model.TRANS_STATUS_WITHDRAW_PENDING {
		return badRequest("Transaction is not pending")
	}
	jsonBefore, _ := json.Marshal(transaction)
	var createBody model.CreateBankTransactionActionBody
	if transaction.TransactionTypeId == model.TRANSACTION_TYPE_DEPOSIT {
		createBody.ActionKey = fmt.Sprintf("CANCEL#%d", transaction.Id)
	} else if transaction.TransactionTypeId == model.TRANSACTION_TYPE_WITHDRAW {
		createBody.ActionKey = fmt.Sprintf("CANCEL#%d", transaction.Id)
	} else {
		// BONUS + GETBACK
		createBody.ActionKey = fmt.Sprintf("CANCEL#%d", transaction.Id)
	}
	createBody.TransactionId = id
	createBody.UserId = transaction.UserId
	createBody.TransactionTypeId = transaction.TransactionTypeId
	createBody.FromAccountId = transaction.FromAccountId
	createBody.ToAccountId = transaction.ToAccountId
	createBody.JsonBefore = string(jsonBefore)
	createBody.TransferAt = transaction.TransferAt
	createBody.ConfirmedAt = data.CanceledAt
	createBody.ConfirmedByAdminId = &data.CanceledByAdminId
	if actionId, err := s.repoBanking.CreateTransactionAction(createBody); err == nil {

		if transaction.TransactionTypeId == model.TRANSACTION_TYPE_DEPOSIT {
			// DO_NOTHING
		} else if transaction.TransactionTypeId == model.TRANSACTION_TYPE_WITHDRAW {
			// RETURN_CREDIT
			if err := s.increaseMemberCredit(transaction.UserId, transaction.CreditAmount, "withdraw", "คืนเครดิตจากการถอนไม่สำเร็จ"); err != nil {
				if err := s.repoBanking.RollbackTransactionAction(*actionId); err != nil {
					return internalServerError(err)
				}
				return internalServerError(err)
			}
		} else if transaction.TransactionTypeId == model.TRANSACTION_TYPE_BONUS {
			// DO_NOTHING
		} else if transaction.TransactionTypeId == model.TRANSACTION_TYPE_CREDITBACK {
			// RETURN_CREDIT
			if err := s.increaseMemberCredit(transaction.UserId, transaction.CreditAmount, "getcreditback", "คืนเครดิตจากรายการที่ไม่สำเร็จ"); err != nil {
				if err := s.repoBanking.RollbackTransactionAction(*actionId); err != nil {
					return internalServerError(err)
				}
				return internalServerError(err)
			}
		} else if transaction.TransactionTypeId == model.TRANSACTION_TYPE_CREDITCANCEL {
			// RETURN_CREDIT
			if err := s.increaseMemberCredit(transaction.UserId, transaction.CreditAmount, "getcreditback", "คืนเครดิตจากรายการที่ไม่สำเร็จ"); err != nil {
				if err := s.repoBanking.RollbackTransactionAction(*actionId); err != nil {
					return internalServerError(err)
				}
				return internalServerError(err)
			}
		}

		if err := s.repoBanking.CancelPendingTransaction(id, data); err != nil {
			if err := s.repoBanking.RollbackTransactionAction(*actionId); err != nil {
				return internalServerError(err)
			}
			return internalServerError(err)
		}

	} else {
		return internalServerError(err)
	}
	return nil
}

func (s *bankingService) ConfirmDepositTransaction(id int64, req model.BankConfirmDepositRequest) error {

	actionAtUtc := time.Now().UTC()

	record, err := s.repoBanking.GetBankTransactionById(id)
	if err != nil {
		return internalServerError(err)
	}
	if record.TransactionStatusId != model.TRANS_STATUS_PENDING {
		return badRequest("Transaction is not pending")
	}
	if record.TransactionTypeId != model.TRANSACTION_TYPE_DEPOSIT && record.TransactionTypeId != model.TRANSACTION_TYPE_BONUS {
		return badRequest("Transaction is not deposit")
	}
	jsonBefore, _ := json.Marshal(record)

	// Admin Confirm
	admin, err := s.repoBanking.GetAdminById(*req.ConfirmedByAdminId)
	if err != nil {
		return internalServerError(err)
	}

	var updateData model.BankDepositTransactionConfirmBody
	updateData.TransactionStatusId = model.TRANS_STATUS_DEPOSIT_PENDING_CREDIT
	updateData.ConfirmedAt = req.ConfirmedAt
	updateData.ConfirmedByAdminId = req.ConfirmedByAdminId
	// updateData.BonusAmount = req.BonusAmount

	var createBody model.CreateBankTransactionActionBody
	createBody.ActionKey = fmt.Sprintf("DCF_STATE#%d", record.Id)
	createBody.TransactionId = record.Id
	createBody.UserId = record.UserId
	createBody.TransactionTypeId = record.TransactionTypeId
	createBody.FromAccountId = record.FromAccountId
	createBody.ToAccountId = record.ToAccountId
	createBody.JsonBefore = string(jsonBefore)
	if req.TransferAt == nil {
		createBody.TransferAt = record.TransferAt
	} else {
		TransferAt := req.TransferAt
		createBody.TransferAt = TransferAt
		updateData.TransferAt = *TransferAt
	}
	if req.SlipUrl != nil {
		createBody.SlipUrl = *req.SlipUrl
	}
	createBody.CreditAmount = record.CreditAmount
	if req.BonusAmount != nil {
		createBody.BonusAmount = *req.BonusAmount
	}
	createBody.ConfirmedAt = req.ConfirmedAt
	createBody.ConfirmedByAdminId = req.ConfirmedByAdminId
	if actionId, err := s.repoBanking.CreateTransactionAction(createBody); err == nil {
		// do nothing ?
		if err := s.repoBanking.ConfirmPendingDepositTransaction(id, updateData); err != nil {
			if err := s.repoBanking.RollbackTransactionAction(*actionId); err != nil {
				return internalServerError(err)
			}
			return internalServerError(err)
		}
	} else {
		return internalServerError(err)
	}

	// ===========================================================
	// RemoveFrom BankPendingRecord -> รายการฝาก อนุมัติรายการฝากโดยการจับชื่อผู้ใช้
	dashboardRepo := repository.NewDashboardRepository(s.SharedDb)
	if err := ConfirmBankPendingRecordFromAny(dashboardRepo, record.TransactionTypeId, record.Id, actionAtUtc, admin.Fullname); err != nil {
		log.Println("UpdateIgnoreDeposit.ConfirmBankPendingRecordFromAny.ERROR=", err)
	}

	if record.IsAutoCredit {
		// isAUtoCredit with same request
		if err := s.ConfirmDepositCredit(id, req); err != nil {
			return internalServerError(err)
		}
	}
	return nil
}

func (s *bankingService) ConfirmDepositCredit(id int64, req model.BankConfirmDepositRequest) error {

	record, err := s.repoBanking.GetBankTransactionById(id)
	if err != nil {
		return internalServerError(err)
	}
	if record.TransactionTypeId != model.TRANSACTION_TYPE_DEPOSIT && record.TransactionTypeId != model.TRANSACTION_TYPE_BONUS {
		return badRequest("Transaction is not deposit")
	}
	if record.TransactionStatusId != model.TRANS_STATUS_DEPOSIT_PENDING_CREDIT {
		return badRequest("Transaction is not pending")
	}
	jsonBefore, _ := json.Marshal(record)

	var updateData model.BankDepositTransactionConfirmBody
	updateData.TransactionStatusId = model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED
	updateData.ConfirmedAt = req.ConfirmedAt
	updateData.ConfirmedByAdminId = req.ConfirmedByAdminId
	if req.BonusAmount != nil {
		updateData.BonusAmount = *req.BonusAmount
		record.BonusAmount = *req.BonusAmount
	}

	var createBody model.CreateBankTransactionActionBody
	createBody.ActionKey = fmt.Sprintf("DCF_CREDIT#%d", record.Id)
	createBody.TransactionId = record.Id
	createBody.UserId = record.UserId
	createBody.TransactionTypeId = record.TransactionTypeId
	createBody.FromAccountId = record.FromAccountId
	createBody.ToAccountId = record.ToAccountId
	createBody.JsonBefore = string(jsonBefore)
	if req.TransferAt == nil {
		createBody.TransferAt = record.TransferAt
	} else {
		TransferAt := req.TransferAt
		createBody.TransferAt = TransferAt
		updateData.TransferAt = *TransferAt
	}
	if req.SlipUrl != nil {
		createBody.SlipUrl = *req.SlipUrl
	}
	createBody.CreditAmount = record.CreditAmount
	if req.BonusAmount != nil {
		createBody.BonusAmount = *req.BonusAmount
	}
	createBody.ConfirmedAt = req.ConfirmedAt
	createBody.ConfirmedByAdminId = req.ConfirmedByAdminId

	if _, err := s.repoBanking.CreateTransactionAction(createBody); err == nil {
		log.Println("ConfirmPendingTransaction updateData:", helper.StructJson(updateData))
		if err := s.increaseMemberCredit(record.UserId, record.CreditAmount, "deposit", "ฝากเครดิต"); err != nil {
			// if err := s.repoBanking.RollbackTransactionAction(*actionId); err != nil {
			// 	return internalServerError(err)
			// }
			return internalServerError(err)
		}
		if record.BonusAmount > 0 {
			if err := s.increaseMemberCredit(record.UserId, record.BonusAmount, "deposit", "ได้รับโบนัสจากการฝากเครดิต"); err != nil {
				// if err := s.repoBanking.RollbackTransactionAction(*actionId); err != nil {
				// 	return internalServerError(err)
				// }
				return internalServerError(err)
			}
		}
		if err := s.repoBanking.ConfirmPendingCreditDepositTransaction(id, updateData); err != nil {
			// if err := s.repoBanking.RollbackTransactionAction(*actionId); err != nil {
			// 	return internalServerError(err)
			// }
			return internalServerError(err)
		}
	} else {
		return internalServerError(err)
	}
	return nil
}

func (s *bankingService) increaseMemberCredit(userId int64, creditAmount float64, statementTypeName string, info string) error {

	StatementTypeId, err := s.repoBanking.GetMemberStatementTypeByCode(statementTypeName)
	if err != nil {
		return badRequest("Invalid Type")
	}

	var body model.MemberStatementCreateBody
	body.UserId = userId
	body.StatementTypeId = StatementTypeId.Id
	body.Info = info
	body.Amount = creditAmount
	if err := s.repoBanking.IncreaseMemberCredit(body); err != nil {
		return internalServerError(err)
	}
	return nil
}

func (s *bankingService) decreaseMemberCredit(userId int64, creditAmount float64, statementTypeName string, info string) error {

	StatementTypeId, err := s.repoBanking.GetMemberStatementTypeByCode(statementTypeName)
	if err != nil {
		return badRequest("Invalid Type")
	}

	var body model.MemberStatementCreateBody
	body.UserId = userId
	body.StatementTypeId = StatementTypeId.Id
	body.Info = info
	body.Amount = creditAmount
	if err := s.repoBanking.DecreaseMemberCredit(body); err != nil {
		return internalServerError(err)
	}
	return nil
}

func (s *bankingService) ContinueAutoWithdrawTransaction(id int64) error {

	record, err := s.repoBanking.GetBankTransactionById(id)
	if err != nil {
		return internalServerError(err)
	}
	if record.TransactionTypeId != model.TRANSACTION_TYPE_WITHDRAW && record.TransactionTypeId != model.TRANSACTION_TYPE_CREDITBACK || record.TransactionTypeId != model.TRANSACTION_TYPE_CREDITCANCEL {
		return badRequest("Transaction is not withdraw")
	}
	var fromAccountId = record.FromAccountId

	var autoWithdrawCondition *model.BankAutoWithdrawCondition
	systemAccount, err := s.repoAccounting.GetWithdrawAccountById(fromAccountId)
	if err == nil {
		if condition, err := s.GetAutoWithdrawCondition(*record, *systemAccount); err == nil {
			autoWithdrawCondition = condition
		}
	}

	if autoWithdrawCondition != nil {
		err := s.SetAutoWithdrawCondition(record.Id, autoWithdrawCondition)
		log.Println(err)
		if err := s.ProcessAutoWithdrawCondition(*autoWithdrawCondition); err != nil {
			return internalServerError(err)
		}
	}
	return nil
}

func (s *bankingService) ConfirmWithdrawTransaction(id int64, req model.BankConfirmCreditWithdrawRequest) error {

	record, err := s.repoBanking.GetBankTransactionById(id)
	if err != nil {
		return internalServerError(err)
	}
	if record.TransactionTypeId != model.TRANSACTION_TYPE_WITHDRAW && record.TransactionTypeId != model.TRANSACTION_TYPE_CREDITBACK || record.TransactionTypeId != model.TRANSACTION_TYPE_CREDITCANCEL {
		return badRequest("Transaction is not withdraw")
	}
	var fromAccountId = record.FromAccountId
	var updateData model.BankWithdrawTransactionConfirmBody
	if record.TransactionTypeId == model.TRANSACTION_TYPE_CREDITBACK {
		updateData.TransactionStatusId = model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED
	} else if record.TransactionTypeId == model.TRANSACTION_TYPE_CREDITCANCEL {
		updateData.TransactionStatusId = model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED
	} else {
		updateData.TransactionStatusId = model.TRANS_STATUS_WITHDRAW_PENDING // รอโอน
	}
	updateData.ConfirmedAt = req.ConfirmedAt
	updateData.ConfirmedByAdminId = req.ConfirmedByAdminId
	if record.TransactionStatusId == model.TRANS_STATUS_PENDING || record.TransactionStatusId == model.TRANS_STATUS_DEPOSIT_PENDING_CREDIT {
		if req.FromAccountId != nil {
			fromAccount, err := s.repoAccounting.GetBankAccountById(*req.FromAccountId)
			if err != nil {
				return badRequest("Invalid Bank Account")
			}
			fromAccountId = fromAccount.Id
			updateData.FromAccountId = &fromAccount.Id
		}
		if req.CreditAmount != nil {
			updateData.CreditAmount = *req.CreditAmount
		} else {
			updateData.CreditAmount = record.CreditAmount
		}
	} else {
		return badRequest("Transaction is not pending")
	}
	jsonBefore, _ := json.Marshal(record)

	// Check Credit/Balance
	if err := s.repoBanking.CheckMemeberHasEnoughtCredit(record.UserId, record.CreditAmount); err != nil {
		return internalServerError(err)
	}

	var autoWithdrawCondition *model.BankAutoWithdrawCondition
	systemAccount, err := s.repoAccounting.GetWithdrawAccountById(fromAccountId)
	if err == nil {
		if condition, err := s.GetAutoWithdrawCondition(*record, *systemAccount); err == nil {
			autoWithdrawCondition = condition
		}
	}

	var createBody model.CreateBankTransactionActionBody
	createBody.ActionKey = fmt.Sprintf("CFW_CREDIT#%d", record.Id)
	createBody.TransactionId = record.Id
	createBody.UserId = record.UserId
	createBody.TransactionTypeId = record.TransactionTypeId
	createBody.FromAccountId = record.FromAccountId
	createBody.ToAccountId = record.ToAccountId
	createBody.JsonBefore = string(jsonBefore)
	createBody.TransferAt = record.TransferAt
	createBody.CreditAmount = updateData.CreditAmount
	createBody.ConfirmedAt = req.ConfirmedAt
	createBody.ConfirmedByAdminId = req.ConfirmedByAdminId
	// BOF : transaction
	if actionId, err := s.repoBanking.CreateTransactionAction(createBody); err == nil {
		if err := s.decreaseMemberCredit(record.UserId, record.CreditAmount, "withdraw", "ถอนเครดิต"); err != nil {
			if err := s.repoBanking.RollbackTransactionAction(*actionId); err != nil {
				return internalServerError(err)
			}
			return internalServerError(err)
		}
		if err := s.repoBanking.ConfirmPendingWithdrawTransaction(id, updateData); err != nil {
			if err := s.repoBanking.RollbackTransactionAction(*actionId); err != nil {
				return internalServerError(err)
			}
			return internalServerError(err)
		}
		// EOF : transaction
		if autoWithdrawCondition != nil {
			err := s.SetAutoWithdrawCondition(record.Id, autoWithdrawCondition)
			log.Println(err)
			if err := s.ProcessAutoWithdrawCondition(*autoWithdrawCondition); err != nil {
				return internalServerError(err)
			}
		}
	} else {
		return internalServerError(err)
	}

	return nil
}

func (s *bankingService) ConfirmWithdrawTransfer(id int64, req model.BankConfirmTransferWithdrawRequest) error {

	record, err := s.repoBanking.GetBankTransactionById(id)
	if err != nil {
		return internalServerError(err)
	}
	if record.TransactionTypeId != model.TRANSACTION_TYPE_WITHDRAW {
		return badRequest("Transaction is not withdraw")
	}
	var fromAccountId = record.FromAccountId
	var updateData model.BankWithdrawTransactionConfirmBody
	updateData.TransactionStatusId = model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED
	updateData.ConfirmedAt = req.ConfirmedAt
	updateData.ConfirmedByAdminId = req.ConfirmedByAdminId
	if record.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_PENDING {
		if req.FromAccountId != nil {
			fromAccount, err := s.repoAccounting.GetBankAccountById(*req.FromAccountId)
			if err != nil {
				return badRequest("Invalid Bank Account")
			}
			fromAccountId = fromAccount.Id
			updateData.FromAccountId = &fromAccountId
		}
	} else {
		return badRequest("Transaction is not transfer pending")
	}
	jsonBefore, _ := json.Marshal(record)

	systemAccount, err := s.repoAccounting.GetWithdrawAccountById(fromAccountId)
	if err != nil {
		if err.Error() == recordNotFound {
			return notFound("Bank Account not found")
		}
		return internalServerError(err)
	}

	var createBody model.CreateBankTransactionActionBody
	createBody.ActionKey = fmt.Sprintf("CFW_TRASFER#%d", record.Id)
	createBody.TransactionId = record.Id
	createBody.UserId = record.UserId
	createBody.TransactionTypeId = record.TransactionTypeId
	createBody.FromAccountId = record.FromAccountId
	createBody.ToAccountId = record.ToAccountId
	createBody.JsonBefore = string(jsonBefore)
	createBody.TransferAt = record.TransferAt
	createBody.CreditAmount = record.CreditAmount
	createBody.ConfirmedAt = req.ConfirmedAt
	createBody.ConfirmedByAdminId = req.ConfirmedByAdminId
	// BOF : transaction
	if actionId, err := s.repoBanking.CreateTransactionAction(createBody); err == nil {
		allow_withdraw_from_account := false
		allow_withdraw_from_account_key := "allow_withdraw_from_account"
		var query model.BotAccountConfigListRequest
		query.SearchKey = &allow_withdraw_from_account_key
		config, _ := s.repoAccounting.GetBotaccountConfigByKey(query)

		if config != nil && config.ConfigVal == "all" {
			allow_withdraw_from_account = true
		}
		if allow_withdraw_from_account {
			// ExternalTransfer
			var body model.ExternalAccountTransferBody
			body.AccountForm = systemAccount.AccountNumber
			body.AccountTo = record.ToAccountNumber
			body.Amount = strconv.FormatFloat(record.CreditAmount, 'E', -1, 64)
			body.BankCode = record.ToBankCode
			body.Pin = systemAccount.PinCode
			if err := s.repoBanking.TransferExternalAccount(body); err != nil {
				if err := s.repoBanking.RollbackTransactionAction(*actionId); err != nil {
					return internalServerError(err)
				}
				return internalServerError(err)
			}

		}
		updateData.TransferAt = time.Now()
		if err := s.repoBanking.ConfirmPendingWithdrawTransfer(id, updateData); err != nil {
			if err := s.repoBanking.RollbackTransactionAction(*actionId); err != nil {
				return internalServerError(err)
			}
			return internalServerError(err)
		}
		// EOF : commit transaction
	} else {
		return internalServerError(err)
	}
	return nil
}

// func (s *bankingService) GetFinishedTransactions(req model.FinishedTransactionListRequest) (*model.SuccessWithPagination, error) {

// 	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
// 		return nil, badRequest(err.Error())
// 	}
// 	records, err := s.repoBanking.GetFinishedTransactions(req)
// 	if err != nil {
// 		return nil, internalServerError(err)
// 	}
// 	return records, nil
// }

// func (s *bankingService) RemoveFinishedTransaction(id int64, data model.BankTransactionRemoveBody) error {

// 	record, err := s.repoBanking.GetBankTransactionById(id)
// 	if err != nil {
// 		return internalServerError(err)
// 	}
// 	if record.TransactionStatusId != model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED {
// 		return badRequest("Transaction is not finished")
// 	}

// 	if err := s.repoBanking.RemoveFinishedTransaction(id, data); err != nil {
// 		return internalServerError(err)
// 	}
// 	return nil
// }

// func (s *bankingService) GetRemovedTransactions(req model.RemovedTransactionListRequest) (*model.SuccessWithPagination, error) {

// 	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
// 		return nil, badRequest(err.Error())
// 	}
// 	records, err := s.repoBanking.GetRemovedTransactions(req)
// 	if err != nil {
// 		return nil, internalServerError(err)
// 	}
// 	return records, nil
// }

func (s *bankingService) GetMemberByCode(code string) (*model.MemberForDropdownResponse, error) {

	if code == "" {
		return nil, badRequest("Code is required")
	}

	records, err := s.repoBanking.GetMemberByCode(code)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(memberNotFound)
		}
		return nil, internalServerError(err)
	}

	var result model.MemberForDropdownResponse
	result.Result = records

	return &result, nil
}

func (s *bankingService) GetMembers(req model.MemberListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	records, err := s.repoBanking.GetMembers(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	return records, nil
}

// func (s *bankingService) GetPossibleStatementOwners(req model.MemberPossibleListRequest) (*model.SuccessWithPagination, error) {

// 	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
// 		return nil, badRequest(err.Error())
// 	}

// 	statement, err := s.repoBanking.GetBankStatementById(req.UnknownStatementId)
// 	if err != nil {
// 		if err.Error() == recordNotFound {
// 			return nil, notFound(bankStatementferNotFound)
// 		}
// 		return nil, internalServerError(err)
// 	}
// 	req.UserBankCode = &statement.FromBankCode
// 	req.UserAccountNumber = &statement.FromAccountNumber

// 	records, err := s.repoBanking.GetPossibleStatementOwners(req)
// 	if err != nil {
// 		return nil, internalServerError(err)
// 	}
// 	return records, nil
// }

func (s *bankingService) GetMemberTransactions(req model.MemberTransactionListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}
	records, err := s.repoBanking.GetMemberTransactions(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	return records, nil
}

func (s *bankingService) GetMemberTransactionSummary(req model.MemberTransactionListRequest) (*model.MemberTransactionSummary, error) {

	result, err := s.repoBanking.GetMemberTransactionSummary(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	return result, nil
}

func (s *bankingService) MatchDepositTransaction(id int64, req model.BankConfirmDepositRequest) error {

	record, err := s.repoBanking.GetBankTransactionById(id)
	if err != nil {
		return internalServerError(err)
	}
	if record.TransactionStatusId != model.TRANS_STATUS_PENDING {
		return badRequest("Transaction is not pending")
	}
	if record.TransactionTypeId != model.TRANSACTION_TYPE_DEPOSIT && record.TransactionTypeId != model.TRANSACTION_TYPE_BONUS {
		return badRequest("Transaction is not deposit")
	}
	if err := s.ConfirmDepositTransaction(record.UserId, req); err != nil {
		return internalServerError(err)
	}
	// // no need if IsAutoCredit = true if err := s.ConfirmDepositCredit(record.UserId, req); err != nil {
	// 	return internalServerError(err)
	// }
	return nil
}

func (s *bankingService) GetMemberStatementTypeByCode(name string) (*model.MemberStatementType, error) {

	record, err := s.repoBanking.GetMemberStatementTypeByCode(name)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(bankStatementferNotFound)
		}
		return nil, internalServerError(err)
	}
	return record, nil
}

func (s *bankingService) GetMemberStatementTypes(req model.SimpleListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}
	records, err := s.repoBanking.GetMemberStatementTypes(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	return records, nil
}

func (s *bankingService) GetMemberStatementById(req model.GetByIdRequest) (*model.MemberStatementResponse, error) {

	record, err := s.repoBanking.GetMemberStatementById(req.Id)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(bankStatementferNotFound)
		}
		return nil, internalServerError(err)
	}
	return record, nil
}

func (s *bankingService) GetMemberStatements(req model.MemberStatementListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}
	records, err := s.repoBanking.GetMemberStatements(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	return records, nil
}

func (s *bankingService) ProcessMemberDepositCredit(userId int64, amount float64) error {

	statementCode := "deposit"
	var body model.MemberStatementCreateBody

	member, err := s.repoBanking.GetMemberById(userId)
	if err != nil {
		return badRequest("Invalid Member")
	}

	StatementTypeId, err := s.repoBanking.GetMemberStatementTypeByCode(statementCode)
	if err != nil {
		return badRequest("Invalid Type")
	}

	body.UserId = member.Id
	body.StatementTypeId = StatementTypeId.Id
	body.Info = "ฝากเครดิต"
	body.Amount = amount
	if err := s.repoBanking.IncreaseMemberCredit(body); err != nil {
		return internalServerError(err)
	}
	return nil
}

func (s *bankingService) ProcessMemberWithdrawCredit(userId int64, amount float64) error {

	statementCode := "withdraw"
	var body model.MemberStatementCreateBody

	member, err := s.repoBanking.GetMemberById(userId)
	if err != nil {
		return badRequest("Invalid Member")
	}

	StatementTypeId, err := s.repoBanking.GetMemberStatementTypeByCode(statementCode)
	if err != nil {
		return badRequest("Invalid Type")
	}

	body.UserId = member.Id
	body.StatementTypeId = StatementTypeId.Id
	body.Info = "ถอนเครดิต"
	body.Amount = amount
	if err := s.repoBanking.DecreaseMemberCredit(body); err != nil {
		return internalServerError(err)
	}
	return nil
}

func (s *bankingService) ProcessMemberBonusCredit(userId int64, amount float64) error {

	statementCode := "bonus"
	var body model.MemberStatementCreateBody

	member, err := s.repoBanking.GetMemberById(userId)
	if err != nil {
		return badRequest("Invalid Member")
	}

	StatementTypeId, err := s.repoBanking.GetMemberStatementTypeByCode(statementCode)
	if err != nil {
		return badRequest("Invalid Type")
	}

	body.UserId = member.Id
	body.StatementTypeId = StatementTypeId.Id
	body.Info = "ได้รับโบนัส"
	body.Amount = amount
	if err := s.repoBanking.IncreaseMemberCredit(body); err != nil {
		return internalServerError(err)
	}
	return nil
}

func (s *bankingService) ProcessMemberGetbackCredit(userId int64, amount float64) error {

	statementCode := "getcreditback"
	var body model.MemberStatementCreateBody

	member, err := s.repoBanking.GetMemberById(userId)
	if err != nil {
		return badRequest("Invalid Member")
	}

	StatementTypeId, err := s.repoBanking.GetMemberStatementTypeByCode(statementCode)
	if err != nil {
		return badRequest("Invalid Type")
	}

	body.UserId = member.Id
	body.StatementTypeId = StatementTypeId.Id
	body.Info = "ถูกดึงเครดิตคืน"
	body.Amount = amount
	if err := s.repoBanking.DecreaseMemberCredit(body); err != nil {
		return internalServerError(err)
	}
	return nil
}

func (s *bankingService) GetTransactionType() ([]model.TransactionTypeResponse, error) {
	result, err := s.repoBanking.GetTransactionType()
	if err != nil {
		return nil, internalServerError(err)
	}
	return result, nil
}

func (s *bankingService) GetStatementType() ([]model.StatementTypeResponse, error) {
	result, err := s.repoBanking.GetStatementType()
	if err != nil {
		return nil, internalServerError(err)
	}
	return result, nil

}

func (s *bankingService) GetPossibleOwnersByStatementId(id int64) (*model.SuccessWithPagination, error) {

	trans, err := s.repoBanking.GetTransactionByStatementId(id)
	if err != nil {
		return nil, internalServerError(err)
	}

	var body model.GetPossibleOwnersRequest
	body.FromAccountNumber = trans.FromAccountNumber
	body.ToBankId = trans.ToBankId
	body.FromBankId = trans.FromBankId
	list, total, err := s.repoBanking.GetPossibleOwnersByStatementId(body)
	if err != nil {
		return nil, internalServerError(err)
	}
	var result model.SuccessWithPagination
	result.List = list
	result.Total = total
	return &result, nil

}

func (s *bankingService) GetBankTransactionDepositList(req model.GetBankTransactionDepositListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.Pagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	tranStatusPending := strconv.FormatInt(model.TRANS_STATUS_PENDING, 10)                        // defualt และ คนฝากเงินเลขไม่ตรง
	tranStatusPendingCredit := strconv.FormatInt(model.TRANS_STATUS_DEPOSIT_PENDING_CREDIT, 10)   // รอเครดิตจากเกม
	tranStatusCreditApproved := strconv.FormatInt(model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED, 10) // อนุมัติแล้ว (เกมเครดิต)
	tranStatusCreditRejected := strconv.FormatInt(model.TRANS_STATUS_DEPOSIT_CREDIT_REJECTED, 10) // ไม่อนุมัติ (เกมครดิต)
	tranStatusPendingSlip := strconv.FormatInt(model.TRANS_STATUS_DEPOSIT_PENDING_SLIP, 10)       // แจ้งฝาก slip เข้ามาก่อน webhook
	tranStatusIgnore := strconv.FormatInt(model.TRANS_STATUS_DEPOSIT_IGNORE, 10)                  // เพิกเเฉย
	tranStatusMultiUser := strconv.FormatInt(model.TRANS_STATUS_DEPOSIT_PENDING_MULTIUSER, 10)    // รายการมียูสเหมือนกัน

	if req.TransactionStatus == "PENDING_ALL" {
		req.TransactionStatus = fmt.Sprintf("%v,%v,%v", tranStatusPending, tranStatusPendingCredit, tranStatusPendingSlip)
	}
	if req.TransactionStatus == "PENDING" {
		req.TransactionStatus = fmt.Sprintf("%v,%v,%v", tranStatusPending, tranStatusPendingSlip, tranStatusMultiUser)
	}
	if req.TransactionStatus == "PENDING_CREDIT" {
		req.TransactionStatus = tranStatusPendingCredit
	}
	if req.TransactionStatus == "CREDIT_APPROVED" {
		req.TransactionStatus = tranStatusCreditApproved
	}
	if req.TransactionStatus == "CREDIT_REJECTED" {
		req.TransactionStatus = fmt.Sprintf("%v,%v", tranStatusCreditRejected, tranStatusIgnore)
	}

	result, total, err := s.repoBanking.GetBankTransactionDepositList(req)
	if err != nil {
		return nil, internalServerError(err)
	}

	for i, record := range result {
		item := &result[i]
		if record.TransactionStatusId == model.TRANS_STATUS_PENDING || record.TransactionStatusId == model.TRANS_STATUS_DEPOSIT_PENDING_SLIP || record.TransactionStatusId == model.TRANS_STATUS_DEPOSIT_PENDING_MULTIUSER {
			item.Status = "PENDING"
		}
		if record.TransactionStatusId == model.TRANS_STATUS_DEPOSIT_PENDING_CREDIT {
			item.Status = "PENDING_CREDIT"
		}
		if record.TransactionStatusId == model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED {
			item.Status = "CREDIT_APPROVED"
		}
		if record.TransactionStatusId == model.TRANS_STATUS_DEPOSIT_CREDIT_REJECTED || record.TransactionStatusId == model.TRANS_STATUS_DEPOSIT_IGNORE {
			item.Status = "CREDIT_REJECTED"
		}
		// Set Payment GatewayInfo
		isUsePayGate := (record.ToAccountName2 == model.PAYGATE_MERCHANT_TYPE_HENG) || (record.ToAccountName2 == model.PAYGATE_MERCHANT_TYPE_LUCKYTHAI) || (record.ToAccountName2 == model.PAYGATE_MERCHANT_TYPE_PAPAYAPAY)
		isUsePayGate = isUsePayGate || (record.ToAccountName2 == model.PAYGATE_MERCHANT_TYPE_PAYONEX) || (record.ToAccountName2 == model.PAYGATE_MERCHANT_TYPE_JBPAY) || (record.ToAccountName2 == model.PAYGATE_MERCHANT_TYPE_POMPAY)
		isUsePayGate = isUsePayGate || (record.ToAccountName2 == model.PAYGATE_MERCHANT_TYPE_PAYMENTCO) || (record.ToAccountName2 == model.PAYGATE_MERCHANT_TYPE_ZAPPAY) || (record.ToAccountName2 == model.PAYGATE_MERCHANT_TYPE_ONEPAY)
		isUsePayGate = isUsePayGate || (record.ToAccountName2 == model.PAYGATE_MERCHANT_TYPE_FLASHPAY) || (record.ToAccountName2 == model.PAYGATE_MERCHANT_TYPE_BIZPAY) || (record.ToAccountName2 == model.PAYGATE_MERCHANT_TYPE_SUGARPAY)
		isUsePayGate = isUsePayGate || (record.ToAccountName2 == model.PAYGATE_MERCHANT_TYPE_ZMANPAY) || (record.ToAccountName2 == model.PAYGATE_MERCHANT_TYPE_POSTMANPAY) || (record.ToAccountName2 == model.PAYGATE_MERCHANT_TYPE_MAZEPAY)
		isUsePayGate = isUsePayGate || (record.ToAccountName2 == model.PAYGATE_MERCHANT_TYPE_MEEPAY)
		if record.ToAccountId == 0 && isUsePayGate {
			// unknown item.FromBankName = ""
			if record.FromAccountNumber == "" {
				item.FromAccountNumber = record.FromAccountName2
			}
			// Swap
			item.ToBankName = record.ToAccountName2
			item.ToAccountNumber = record.ToAccountNumber2
		}
	}

	var response model.SuccessWithPagination
	response.List = result
	response.Total = total
	return &response, nil
}

func (s *bankingService) GetWebUserTransactionList(req model.GetUserTransactionListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.Pagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	result, total, err := s.repoBanking.GetWebUserTransactionList(req)
	if err != nil {
		return nil, internalServerError(err)
	}

	var response []model.GetUserTransactionListResponse

	for _, v := range result {
		var transactionStatusName string
		// withdraw [ย้าย statis ********][ model.TRANS_STATUS_WITHDRAW_FAILED, model.TRANS_STATUS_WITHDRAW_REJECTED]

		switch v.TransactionStatusId {
		case model.TRANS_STATUS_PENDING, model.TRANS_STATUS_DEPOSIT_PENDING_CREDIT, model.TRANS_STATUS_DEPOSIT_PENDING_MULTIUSER, model.TRANS_STATUS_DEPOSIT_PENDING_SLIP:
			transactionStatusName = "รอตรวจสอบ"
		case model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED:
			transactionStatusName = "เสร็จสิ้น"
		case model.TRANS_STATUS_DEPOSIT_CREDIT_REJECTED:
			transactionStatusName = "ไม่สำเร็จ"
		case model.TRANS_STATUS_WITHDRAW_PENDING, model.TRANS_STATUS_WITHDRAW_OVER_BUDGET, model.TRANS_STATUS_WITHDRAW_OVER_MAX, model.TRANS_STATUS_WITHDRAW_APPROVED, model.TRANS_STATUS_WITHDRAW_FAILED, model.TRANS_STATUS_WITHDRAW_UNSURE:
			transactionStatusName = "รอตรวจสอบ"
		case model.TRANS_STATUS_WITHDRAW_TRASNFERING:
			transactionStatusName = "กำลังโอน"
		case model.TRANS_STATUS_WITHDRAW_SUCCESS:
			transactionStatusName = "เสร็จสิ้น"
		case model.TRANS_STATUS_WITHDRAW_CANCELED, model.TRANS_STATUS_WITHDRAW_REJECTED:
			transactionStatusName = "ไม่สำเร็จ"
		case model.TRANS_STATUS_DEPOSIT_IGNORE:
			transactionStatusName = "ไม่สำเร็จ"
		}

		response = append(response, model.GetUserTransactionListResponse{
			Id:                    v.Id,
			StatementId:           v.StatementId,
			CreditAmount:          v.CreditAmount,
			BeforeAmount:          v.BeforeAmount,
			AfterAmount:           v.AfterAmount,
			TransferAt:            v.TransferAt,
			TransactionStatusId:   v.TransactionStatusId,
			TransactionStatusName: transactionStatusName,
			TransactionTypeId:     v.TransactionTypeId,
			TransactionTypeTh:     v.TransactionTypeTh,
		})
	}

	return &model.SuccessWithPagination{
		List:  response,
		Total: total,
	}, nil
}

func (s bankingService) CheckPaymentGatewayAvailable(req model.UserCreateWithdrawTransactionRequest) (*int64, error) {

	// [********] If Payment Gateway is available, use it
	startProcess := time.Now()

	// Select Single Paygate Account for Withdraw.
	pgWdAccount, err := s.repoBanking.GetPaygateWithdrawAccount()
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound("PAYMENT_WITHDRAWAL_NOT_ENABLED")
		}
		return nil, internalServerError(err)
	}
	if pgWdAccount == nil {
		return nil, notFound("PAYMENT_WITHDRAWAL_NOT_ENABLED")
	}
	// if paygate.Status != "ACTIVE" {
	// 	return nil, badRequest("PAYGATE_NOT_ACTIVE")
	// }
	if !pgWdAccount.HasWithdraw || !pgWdAccount.IsWithdrawEnabled {
		return nil, badRequest("WITHDRAW_NOT_AVAILABLE")
	}
	if req.Amount < pgWdAccount.PaymentWithdrawMinimum || req.Amount > pgWdAccount.PaymentWithdrawMaximum {
		return nil, badRequest("INVALID_AMOUNT")
	}

	// [Pull turn over]
	// turnOver, err := s.serviceTurnover.CheckUserTurnover(req.UserId)
	// if err != nil {
	// 	log.Println("CheckPaymentGatewayAvailable.CheckUserTurnover", err)
	// 	return nil, err
	// }
	// if turnOver.IsHasTurnover {
	// 	return nil, badRequest("ไม่สามารถถอนได้ กรุณาตรวจสอบยอดเทิร์นโอเวอร์ของท่าน")
	// }
	// // [Pull user]
	user, err := s.repoBanking.GetUserBankDetailById(req.UserId)
	if err != nil {
		log.Println("CheckPaymentGatewayAvailable.GetUserBankDetailById", err)
		return nil, internalServerError(err)
	}

	// [Check User Credit]
	// [CURRENT_USER_CREDIT] get latest credit from Agent
	userCreditInfo, err := s.serviceUser.GetUser(user.Id)
	if err != nil {
		log.Println("CheckPaymentGatewayAvailable.GetUser", err)
		return nil, badRequest("ยอดเงินในเกมไม่พอ")
	}
	if userCreditInfo.Credit < req.Amount {
		return nil, badRequest("ยอดเงินในเกมไม่พอ")
	}

	// [Web config minimum withdraw]
	getWebConfig, err := s.repoBanking.GetWebConfiguration()
	if err != nil {
		statusLog := "ERROR"
		jsonPayLoad := fmt.Sprintf("USER_TRY_TO_CREATE_WITHDRAW_TRANSACTION_BUT_GET_WEB_CONFIG_ERROR:%v", err)
		jsonRequest := helper.StructJson(req)
		logType := "ERROR_USER_TRANSACTION"
		var createLog model.BankTransactionLogCreateRequest
		createLog.Status = &statusLog
		createLog.JsonPayload = &jsonPayLoad
		createLog.JsonRequest = &jsonRequest
		createLog.LogType = &logType
		if _, err := s.serviceAccounting.CreateBankTransactionLog(createLog); err != nil {
			return nil, nil
		}
		log.Println("CheckPaymentGatewayAvailable.GetWebConfiguration", err)
		return nil, internalServerError(err)
	}
	var checkRequest model.CheckDuplicateWithdrawRequest
	checkRequest.UserId = req.UserId
	checkRequest.Amount = req.Amount
	if getWebConfig.MinimumWithdraw > req.Amount {
		statusLog := "ERROR"
		jsonPayLoad := fmt.Sprintf("USER_CREATE_WITHDRAW_BUT_AMOUNT_LESS_THAN_MINIMUM:%v", getWebConfig.MinimumWithdraw)
		jsonRequest := helper.StructJson(checkRequest)
		logType := "ERROR_USER_TRANSACTION"
		var createLog model.BankTransactionLogCreateRequest
		createLog.Status = &statusLog
		createLog.JsonPayload = &jsonPayLoad
		createLog.JsonRequest = &jsonRequest
		createLog.LogType = &logType
		if _, err := s.serviceAccounting.CreateBankTransactionLog(createLog); err != nil {
			return nil, nil
		}
		return nil, badRequest("WITHDRAW_AMOUNT_LESS_THAN_MINIMUM")
	}

	// duplicate transaction [********] Dunk ไม่ให้ทำรายการยอดเงินเดิมซ้ำ
	// duplicate transaction [********] Dunk เปลี่ยน เป็น flow ที่ไม่ให้ทำรายการซ้ำได้ถ้า มีรายการอยู่แล้ว
	// [Check Duplicate Withdraw]
	duplicateWithdrawTrans, _ := s.repoBanking.CheckUserDuplicateWithdrawProcessing(req.UserId)
	if duplicateWithdrawTrans != nil && duplicateWithdrawTrans.Id > 0 {
		statusLog := "SUCCESS"
		jsonPayLoad := fmt.Sprintf("USER_TRY_TO_CREATE_DUPLICATE_WITHDRAW_TRANSACTION:%v", duplicateWithdrawTrans.Id)
		jsonRequest := helper.StructJson(checkRequest)
		logType := "DUPLICATE_USER_TRANSACTION"
		var createLog model.BankTransactionLogCreateRequest
		createLog.Status = &statusLog
		createLog.JsonPayload = &jsonPayLoad
		createLog.JsonRequest = &jsonRequest
		createLog.LogType = &logType
		if _, err := s.serviceAccounting.CreateBankTransactionLog(createLog); err != nil {
			return nil, nil
		}
		return nil, badRequest("DUPLICATE_USER_TRANSACTION")
	}

	// ย้ายไป เช็ค ที่  UserCreateWithdrawTransaction
	// // [Check TurnOver Withdraw]
	// if err := s.TurnOverWithdrawChecker(user.Id, req.Amount); err != nil {
	// 	statusLog := "ERROR"
	// 	jsonPayLoad := fmt.Sprintf("USER_TRY_TO_CREATE_WITHDRAW_TRANSACTION_BUT_CHECK_TURNOVER_ERROR:%v", err)
	// 	jsonRequest := helper.StructJson(checkRequest)
	// 	logType := "ERROR_USER_TRANSACTION"
	// 	var createLog model.BankTransactionLogCreateRequest
	// 	createLog.Status = &statusLog
	// 	createLog.JsonPayload = &jsonPayLoad
	// 	createLog.JsonRequest = &jsonRequest
	// 	createLog.LogType = &logType
	// 	if _, err := s.serviceAccounting.CreateBankTransactionLog(createLog); err != nil {
	// 		return nil, nil
	// 	}
	// 	return nil, err
	// }

	// [Find Validated Bank Account]
	// var bankAccount model.BankAccount
	// var validatedBank *model.PriorityValidation
	// // [********] To do Dunk Add True user can only withdraw with true bank account
	// if user.BankId == model.BANK_ID_TRUE {
	// 	// GetBankAccountOnlyTrueAccountForWithdraw() (*model.BankAccount, error)
	// 	trueBankAccount, err := s.repoAccounting.GetBankAccountOnlyTrueAccountForWithdraw()
	// 	if err != nil {
	// 		log.Println("CheckPaymentGatewayAvailable.GetBankAccountOnlyTrueAccountForWithdraw", err)
	// 		// return internalServerError(err)
	// 	}
	// 	if trueBankAccount != nil {
	// 		if validatedBank == nil {
	// 			validatedBank = &model.PriorityValidation{}
	// 		}
	// 		validatedBank.BankAccountId = trueBankAccount.Id
	// 		logUpTrue := []interface{}{trueBankAccount, "validatedBank", validatedBank}
	// 		validatedBank.ValidateLog = logUpTrue
	// 	}
	// } else {
	// 	// [********] Disable Bank Check - Let user create
	// 	validatedBank, err = s.serviceAccounting.BankAccountWithDrawPriorityValidation(req.Amount)
	// 	if err != nil {
	// 		log.Println("CheckPaymentGatewayAvailable.BankAccountWithDrawPriorityValidation", err)
	// 		// return badRequest("ไม่สามารถถอนได้ กรุณาแจ้ง Admin")
	// 	}
	// }
	// // [Check Validated Bank Account or Not]
	// if validatedBank != nil {

	// 	if validatedBank.BankAccountId == 0 {
	// 		newBankAccount, err := s.repoAccounting.GetBankAccountForWithdraw()
	// 		if err != nil {
	// 			log.Println("CheckPaymentGatewayAvailable.GetBankAccountForWithdraw", err)
	// 			// return internalServerError(err)
	// 		}
	// 		bankAccount = *newBankAccount
	// 		statusLog := "SUCCESS"
	// 		jsonPayLoad := helper.StructJson(validatedBank.ValidateLog)
	// 		jsonRequest := fmt.Sprintf("LAST USED : %v VALIDATE :%v", helper.StructJson(bankAccount), helper.StructJson(checkRequest))
	// 		logType := "VALIDATE_USER_WITHDRAW"
	// 		var createLog model.BankTransactionLogCreateRequest
	// 		createLog.Status = &statusLog
	// 		createLog.JsonPayload = &jsonPayLoad
	// 		createLog.JsonRequest = &jsonRequest
	// 		createLog.LogType = &logType
	// 		if _, err := s.serviceAccounting.CreateBankTransactionLog(createLog); err != nil {
	// 			return nil
	// 		}
	// 	} else {
	// 		newBankAccount, err := s.repoAccounting.GetBankAccountById(validatedBank.BankAccountId)
	// 		if err != nil {
	// 			log.Println("CheckPaymentGatewayAvailable.GetBankAccountById", err)
	// 			// return internalServerError(err)
	// 		}
	// 		bankAccount = *newBankAccount
	// 	}

	// 	if bankAccount.ConnectionStatusId == model.CONNECTION_CONNECTED {
	// 		validatedBank.Status = model.AUTO_WITHDRAW
	// 	} else {
	// 		validatedBank.Status = model.CLOSE_AUTO_WITHDRAW
	// 	}
	// } else {
	// 	validatedBank = &model.PriorityValidation{}
	// 	validatedBank.Status = model.CLOSE_AUTO_WITHDRAW
	// 	bankAccount.AccountName = "ไม่พบบัญชีธนาคาร"
	// 	bankAccount.AccountNumber = "ไม่พบบัญชีธนาคาร"
	// }
	// // [********] true not allow decimal with scb
	// if user.BankId == model.BANK_ID_TRUE && bankAccount.BankId == model.BANK_ID_SCB {
	// 	if req.Amount != float64(int(req.Amount)) {
	// 		return badRequest("WITHDRAW_AMOUNT_NOT_ALLOW_DECIMAL")
	// 	}
	// }

	// [Check Confirm Key]
	confirmTimeNow := time.Now()
	confirmKey := fmt.Sprintf("%v%v", confirmTimeNow.Format("************"), user.Id)
	checkFromConfirmKey, _ := s.repoBanking.CheckWithdrawConfirmConfirmKey(confirmKey)
	if checkFromConfirmKey != nil && checkFromConfirmKey.Id > 0 {
		log.Println("CheckPaymentGatewayAvailable.CheckWithdrawConfirmConfirmKey", err)
		return nil, badRequest("WITHDRAW_ALREADY_IN_PROCESSING_WAIT_FOR_CONFIRM")
	}
	var checkWithdrawConfirm model.CheckWithdrawConfirmBody
	checkWithdrawConfirm.UserId = user.Id
	checkWithdrawConfirm.CreatedAt = confirmTimeNow
	check, _ := s.repoBanking.CheckWithdrawConfirmCurrentTime(checkWithdrawConfirm)
	if check != nil && check.Id > 0 {
		log.Println("CheckPaymentGatewayAvailable.CheckWithdrawConfirmCurrentTime", err)
		return nil, badRequest("WITHDRAW_ALREADY_IN_PROCESSING_WAIT_FOR_CONFIRM")
	}
	var createConfirmWithdraw model.CreateWithdrawConfirmBody
	createConfirmWithdraw.UserId = user.Id
	createConfirmWithdraw.ConfirmKey = confirmKey
	insertConfirmWithdraw, err := s.repoBanking.CreateWithdrawConfirm(createConfirmWithdraw)
	if err != nil {
		log.Println("CheckPaymentGatewayAvailable.CreateWithdrawConfirm", err)
		return nil, badRequest("WITHDRAW_ALREADY_IN_PROCESSING")
	}
	if insertConfirmWithdraw == 0 {
		log.Println("CheckPaymentGatewayAvailable.CreateWithdrawConfirm", err)
		return nil, badRequest("WITHDRAW_ALREADY_IN_PROCESSING")
	}

	isAutoWithdraw := true // Auto ถอน นับตั้งแต่กดถอน trasnferAt จนได้รับเงิน update to success

	// [create transaction]
	trasnferAt := time.Now()
	var createBankTransaction model.BankTransactionCreateBody
	createBankTransaction.MemberCode = user.MemberCode
	createBankTransaction.UserId = user.Id
	createBankTransaction.TransactionTypeId = model.TRANSACTION_TYPE_WITHDRAW
	// createBankTransaction.FromAccountId = &bankAccount.Id
	// createBankTransaction.FromBankId = &bankAccount.BankId
	createBankTransaction.FromAccountName = &pgWdAccount.TypeName
	createBankTransaction.FromAccountNumber = &pgWdAccount.ShopName
	createBankTransaction.ToBankId = &user.BankId
	createBankTransaction.ToAccountName = &user.Fullname
	createBankTransaction.ToAccountNumber = &user.BankAccount
	createBankTransaction.CreatedByAdminId = 0
	createBankTransaction.TransactionStatusId = model.TRANS_STATUS_WITHDRAW_PENDING
	createBankTransaction.CreditAmount = req.Amount
	createBankTransaction.CreditBack = 0
	createBankTransaction.OverAmount = 0
	createBankTransaction.BonusAmount = 0
	createBankTransaction.BeforeAmount = 0
	createBankTransaction.AfterAmount = 0
	createBankTransaction.IsAutoCredit = isAutoWithdraw
	createBankTransaction.TransferAt = &trasnferAt
	transId, err := s.repoBanking.InsertBankTransaction(createBankTransaction)
	if err != nil {
		log.Println("CheckPaymentGatewayAvailable.InsertBankTransaction", err)
		return nil, internalServerError(err)
	}
	if err := s.repoBanking.UpdateWithdrawConfirmTransactionId(insertConfirmWithdraw, *transId); err != nil {
		log.Println("CheckPaymentGatewayAvailable.UpdateWithdrawConfirmTransactionId", err)
	}
	// [promotion]
	var promotionWebUserId int64
	GetUserPromotion, _ := s.promotionWebService.GetWithdrawCurrentProcessingUserPromotion(user.Id)
	if GetUserPromotion != nil {
		promotionWebUserId = GetUserPromotion.Id

		errUpdate := s.repoBanking.UpdatePromotionToBankTransaction(*transId, promotionWebUserId)
		if errUpdate != nil {
			log.Println("CheckPaymentGatewayAvailable.UpdatePromotionToBankTransaction", err)
		}
	}
	// [Decrease User Credit]
	var userCreditReq model.UserTransactionCreateRequest
	userCreditReq.UserId = user.Id
	userCreditReq.TypeId = model.CREDIT_TYPE_WITHDRAW
	// userCreditReq.AccountId = &bankAccount.Id
	userCreditReq.PromotionId = &promotionWebUserId
	userCreditReq.Detail = fmt.Sprintf("%s => %s:%s", pgWdAccount.TypeName, user.BankName, user.Fullname)
	userCreditReq.Amount = req.Amount
	userCreditReq.StartWorkAt = req.ActionAt // เริ่มนับตอนกดขอถอน
	userCreditReq.RefId = transId
	userCreditReq.IsAdjustAuto = isAutoWithdraw
	isShow := false
	userCreditReq.IsShow = &isShow
	userCreditReq.PaymentMerchatId = &pgWdAccount.ProviderId
	agentResp, err := s.repoAccounting.DecreaseUserCredit(userCreditReq)
	if err != nil {
		// [********] Confirm from P.layer if error from agent will be in failed
		log.Println("CreateWithdrawRecord.DecreaseUserCredit", err)
		var updateAgentError model.UpdateConfirmAutoWithdrawBody
		statusError := model.TRANS_STATUS_WITHDRAW_REJECTED
		confirmedByAuto := int64(0)
		updateAgentError.TransactionStatusId = &statusError
		updateAgentError.ConfirmedByAdminId = &confirmedByAuto
		if err := s.repoBanking.UpdateAdminAndTransactionStatus(*transId, updateAgentError); err != nil {
			return transId, internalServerError(err)
		}
		return transId, internalServerError(err)
	}
	if err := s.repoAccounting.UpdateWithdrawTransactionStatusFromAgent(*transId, *agentResp); err != nil {
		log.Println("CheckPaymentGatewayAvailable.UpdateWithdrawTransactionStatusFromAgent", err)
		return transId, internalServerError(err)
	}

	// [Check User Setting Withdraw]
	// userSettingWithdraw, err := s.UserSettingWithdraw(user.Id, req.Amount)
	// if err != nil {
	// 	log.Println("CheckPaymentGatewayAvailable.UserSettingWithdraw", err)
	// 	logtype := "ERROR_USER_TRANSACTION"
	// 	logstatus := "ERROR"
	// 	logpayLoad := fmt.Sprintf("ERROR_USER_TRANSACTION:%v", err)
	// 	logrequest := helper.StructJson(req)
	// 	var createLog model.BankTransactionLogCreateRequest
	// 	createLog.Status = &logstatus
	// 	createLog.JsonPayload = &logpayLoad
	// 	createLog.JsonRequest = &logrequest
	// 	createLog.LogType = &logtype
	// 	if _, err := s.serviceAccounting.CreateBankTransactionLog(createLog); err != nil {
	// 		return nil
	// 	}
	// }

	// [********]fastbank credit check balance to continue unuse func or not auto
	// getWebLocal, _ := s.repoBanking.GetLocalWebInfo()
	// if getWebLocal != nil {
	// 	if getWebLocal.FastbankCreditBalance <= -1000 {
	// 		log.Println("CheckPaymentGatewayAvailable.WEB_OUT_OF_CREDIT")
	// 		validatedBank.Status = model.CLOSE_AUTO_WITHDRAW
	// 	}
	// }
	//[P.mink confirm ********] not auto if not found after validate

	// if req.Amount > bankAccount.AutoWithdrawMaximum {
	// 	validatedBank.Status = model.CLOSE_AUTO_WITHDRAW
	// }

	// // check auto withdraw again after validate
	// if req.Amount > bankAccount.AutoWithdrawMaximum {
	// 	validatedBank.Status = model.CLOSE_AUTO_WITHDRAW
	// }

	checkAuto := true
	getConfigWeb, _ := s.repoBanking.GetConfiguration()
	if getConfigWeb != nil {
		// [********] อาจจะไม่ต้องมีเพราะ สุดท้ายก็ปล่อยเข้ามาอยู่ดี ให้ ไปสร้าง รายการ
		// if req.Amount < getConfigWeb.WithdrawMaximum {
		// 	checkAuto = false
		// }
		// check auto withdraw again after validate
		if req.Amount > getConfigWeb.WithdrawMaximumAuto {
			checkAuto = false
		}
	} else {
		checkAuto = false
	}

	// fmt.Print("checkAuto", checkAuto)
	// fmt.Print("req.Amoun", req.Amount)
	// fmt.Print("getConfigWeb.WithdrawMaximum", getConfigWeb.WithdrawMaximum)
	// fmt.Print("getConfigWeb.WithdrawMaximumAuto", getConfigWeb.WithdrawMaximumAuto)

	// [Check Auto Withdraw only if pass validate]
	var externalNoti model.NotifyExternalNotificationRequest
	var setIdAuto int64 = 0
	var statusId int64
	if checkAuto {
		if agentResp.AgentSuccess {
			if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_HENG {
				return transId, badRequest("NO_HENG_WITHDRAW")
			} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_LUCKYTHAI {
				var luckyWithDrawRequest model.UserCreatePaygateLuckyWithdrawRequest // * Can use same model
				luckyWithDrawRequest.RefId = transId
				luckyWithDrawRequest.UserId = user.Id
				luckyWithDrawRequest.BankCode = user.BankCode
				luckyWithDrawRequest.AccountNo = user.BankAccount
				luckyWithDrawRequest.Accountname = user.Fullname
				luckyWithDrawRequest.Amount = req.Amount
				if _, err := s.withdrawWithLucky(*pgWdAccount, luckyWithDrawRequest); err != nil {
					return transId, err
				}
				statusId = model.TRANS_STATUS_WITHDRAW_TRASNFERING
			} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_PAPAYAPAY {
				var luckyWithDrawRequest model.UserCreatePaygateLuckyWithdrawRequest // * Can use same model
				luckyWithDrawRequest.RefId = transId
				luckyWithDrawRequest.UserId = user.Id
				luckyWithDrawRequest.BankCode = user.BankCode
				luckyWithDrawRequest.AccountNo = user.BankAccount
				luckyWithDrawRequest.Accountname = user.Fullname
				luckyWithDrawRequest.Amount = req.Amount
				if _, err := s.withdrawWithPapaya(*pgWdAccount, luckyWithDrawRequest); err != nil {
					return transId, err
				}
				statusId = model.TRANS_STATUS_WITHDRAW_TRASNFERING
			} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_PAYONEX {
				var payonexWithDrawRequest model.UserCreatePaygateLuckyWithdrawRequest // * Can use same model
				payonexWithDrawRequest.RefId = transId
				payonexWithDrawRequest.UserId = user.Id
				payonexWithDrawRequest.BankCode = user.BankCode
				payonexWithDrawRequest.AccountNo = user.BankAccount
				payonexWithDrawRequest.Accountname = user.Fullname
				payonexWithDrawRequest.Amount = req.Amount
				if _, err := s.withdrawWithPayonex(*pgWdAccount, payonexWithDrawRequest); err != nil {
					return transId, err
				}
				statusId = model.TRANS_STATUS_WITHDRAW_TRASNFERING
			} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_JBPAY {
				var payonexWithDrawRequest model.UserCreatePaygateLuckyWithdrawRequest // * Can use same model
				payonexWithDrawRequest.RefId = transId
				payonexWithDrawRequest.UserId = user.Id
				payonexWithDrawRequest.BankCode = user.BankCode
				payonexWithDrawRequest.AccountNo = user.BankAccount
				payonexWithDrawRequest.Accountname = user.Fullname
				payonexWithDrawRequest.Amount = req.Amount
				if _, err := s.withdrawWithJbpay(*pgWdAccount, payonexWithDrawRequest); err != nil {
					return transId, err
				}
				statusId = model.TRANS_STATUS_WITHDRAW_TRASNFERING
			} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_POMPAY {
				var payonexWithDrawRequest model.UserCreatePaygateLuckyWithdrawRequest // * Can use same model
				payonexWithDrawRequest.RefId = transId
				payonexWithDrawRequest.UserId = user.Id
				payonexWithDrawRequest.BankCode = user.BankCode
				payonexWithDrawRequest.AccountNo = user.BankAccount
				payonexWithDrawRequest.Accountname = user.Fullname
				payonexWithDrawRequest.Amount = req.Amount
				if _, err := s.withdrawWithPompay(*pgWdAccount, payonexWithDrawRequest); err != nil {
					return transId, err
				}
				statusId = model.TRANS_STATUS_WITHDRAW_TRASNFERING
			} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_PAYMENTCO {
				var payonexWithDrawRequest model.UserCreatePaygateLuckyWithdrawRequest // * Can use same model
				payonexWithDrawRequest.RefId = transId
				payonexWithDrawRequest.UserId = user.Id
				payonexWithDrawRequest.BankCode = user.BankCode
				payonexWithDrawRequest.AccountNo = user.BankAccount
				payonexWithDrawRequest.Accountname = user.Fullname
				payonexWithDrawRequest.Amount = req.Amount
				if _, err := s.withdrawWithPaymentco(*pgWdAccount, payonexWithDrawRequest); err != nil {
					return transId, err
				}
				statusId = model.TRANS_STATUS_WITHDRAW_TRASNFERING
			} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_ZAPPAY {
				var payonexWithDrawRequest model.UserCreatePaygateLuckyWithdrawRequest // * Can use same model
				payonexWithDrawRequest.RefId = transId
				payonexWithDrawRequest.UserId = user.Id
				payonexWithDrawRequest.BankCode = user.BankCode
				payonexWithDrawRequest.AccountNo = user.BankAccount
				payonexWithDrawRequest.Accountname = user.Fullname
				payonexWithDrawRequest.Amount = req.Amount
				if _, err := s.withdrawWithZappay(*pgWdAccount, payonexWithDrawRequest); err != nil {
					return transId, err
				}
				statusId = model.TRANS_STATUS_WITHDRAW_TRASNFERING
			} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_ONEPAY {
				var payonexWithDrawRequest model.UserCreatePaygateLuckyWithdrawRequest // * Can use same model
				payonexWithDrawRequest.RefId = transId
				payonexWithDrawRequest.UserId = user.Id
				payonexWithDrawRequest.BankCode = user.BankCode
				payonexWithDrawRequest.AccountNo = user.BankAccount
				payonexWithDrawRequest.Accountname = user.Fullname
				payonexWithDrawRequest.Amount = req.Amount
				if _, err := s.withdrawWithOnepay(*pgWdAccount, payonexWithDrawRequest); err != nil {
					return transId, err
				}
				statusId = model.TRANS_STATUS_WITHDRAW_TRASNFERING
			} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_FLASHPAY {
				var payonexWithDrawRequest model.UserCreatePaygateLuckyWithdrawRequest // * Can use same model
				payonexWithDrawRequest.RefId = transId
				payonexWithDrawRequest.UserId = user.Id
				payonexWithDrawRequest.BankCode = user.BankCode
				payonexWithDrawRequest.AccountNo = user.BankAccount
				payonexWithDrawRequest.Accountname = user.Fullname
				payonexWithDrawRequest.Amount = req.Amount
				if _, err := s.withdrawWithFlashpay(*pgWdAccount, payonexWithDrawRequest); err != nil {
					return transId, err
				}
				statusId = model.TRANS_STATUS_WITHDRAW_TRASNFERING
			} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_BIZPAY {
				var payonexWithDrawRequest model.UserCreatePaygateLuckyWithdrawRequest // * Can use same model
				payonexWithDrawRequest.RefId = transId
				payonexWithDrawRequest.UserId = user.Id
				payonexWithDrawRequest.BankCode = user.BankCode
				payonexWithDrawRequest.AccountNo = user.BankAccount
				payonexWithDrawRequest.Accountname = user.Fullname
				payonexWithDrawRequest.Amount = req.Amount
				if _, err := s.withdrawWithBizpay(*pgWdAccount, payonexWithDrawRequest); err != nil {
					return transId, err
				}
				statusId = model.TRANS_STATUS_WITHDRAW_TRASNFERING
			} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_SUGARPAY {
				var payonexWithDrawRequest model.UserCreatePaygateLuckyWithdrawRequest // * Can use same model
				payonexWithDrawRequest.RefId = transId
				payonexWithDrawRequest.UserId = user.Id
				payonexWithDrawRequest.BankCode = user.BankCode
				payonexWithDrawRequest.AccountNo = user.BankAccount
				payonexWithDrawRequest.Accountname = user.Fullname
				payonexWithDrawRequest.Amount = req.Amount
				if _, err := s.withdrawWithSugarpay(*pgWdAccount, payonexWithDrawRequest); err != nil {
					return transId, err
				}
				statusId = model.TRANS_STATUS_WITHDRAW_TRASNFERING
			} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_ZMANPAY {
				var payonexWithDrawRequest model.UserCreatePaygateLuckyWithdrawRequest // * Can use same model
				payonexWithDrawRequest.RefId = transId
				payonexWithDrawRequest.UserId = user.Id
				payonexWithDrawRequest.BankCode = user.BankCode
				payonexWithDrawRequest.AccountNo = user.BankAccount
				payonexWithDrawRequest.Accountname = user.Fullname
				payonexWithDrawRequest.Amount = req.Amount
				if _, err := s.withdrawWithZmanpay(*pgWdAccount, payonexWithDrawRequest); err != nil {
					return transId, err
				}
				statusId = model.TRANS_STATUS_WITHDRAW_TRASNFERING
			} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_POSTMANPAY {
				var payonexWithDrawRequest model.UserCreatePaygateLuckyWithdrawRequest // * Can use same model
				payonexWithDrawRequest.RefId = transId
				payonexWithDrawRequest.UserId = user.Id
				payonexWithDrawRequest.BankCode = user.BankCode
				payonexWithDrawRequest.AccountNo = user.BankAccount
				payonexWithDrawRequest.Accountname = user.Fullname
				payonexWithDrawRequest.Amount = req.Amount
				if _, err := s.withdrawWithPostmanPay(*pgWdAccount, payonexWithDrawRequest); err != nil {
					return transId, err
				}
				statusId = model.TRANS_STATUS_WITHDRAW_TRASNFERING
			} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_MAZEPAY {
				var payonexWithDrawRequest model.UserCreatePaygateLuckyWithdrawRequest // * Can use same model
				payonexWithDrawRequest.RefId = transId
				payonexWithDrawRequest.UserId = user.Id
				payonexWithDrawRequest.BankCode = user.BankCode
				payonexWithDrawRequest.AccountNo = user.BankAccount
				payonexWithDrawRequest.Accountname = user.Fullname
				payonexWithDrawRequest.Amount = req.Amount
				if _, err := s.withdrawWithMazepay(*pgWdAccount, payonexWithDrawRequest); err != nil {
					return transId, err
				}
				statusId = model.TRANS_STATUS_WITHDRAW_TRASNFERING
			} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_MEEPAY {
				var payonexWithDrawRequest model.UserCreatePaygateLuckyWithdrawRequest // * Can use same model
				payonexWithDrawRequest.RefId = transId
				payonexWithDrawRequest.UserId = user.Id
				payonexWithDrawRequest.BankCode = user.BankCode
				payonexWithDrawRequest.AccountNo = user.BankAccount
				payonexWithDrawRequest.Accountname = user.Fullname
				payonexWithDrawRequest.Amount = req.Amount
				if _, err := s.withdrawWithMeepay(*pgWdAccount, payonexWithDrawRequest); err != nil {
					return transId, err
				}
				statusId = model.TRANS_STATUS_WITHDRAW_TRASNFERING
			}

		}

		// [ Notify]
		endProcess := time.Now()
		elapsed := endProcess.Sub(startProcess)
		elapsedSeconds := elapsed.Seconds()
		timeElapsed := fmt.Sprintf("%.2f", elapsedSeconds)
		externalNoti.TransId = transId
		externalNoti.Amount = req.Amount
		externalNoti.TypeNotify = model.IsWithdrawalAwaitingTransfer
		externalNoti.MemberCode = user.MemberCode
		externalNoti.UserCredit = agentResp.AgentAfterAmount
		externalNoti.ConfirmedByAdminId = setIdAuto
		externalNoti.TimerCounter = timeElapsed
		externalNoti.WebScoket.UserID = user.Id
		externalNoti.WebScoket.Amount = req.Amount
		externalNoti.WebScoket.MemberCode = user.MemberCode
		externalNoti.WebScoket.AlertType = "WITHDRAW"

	} else {

		statusId = model.TRANS_STATUS_WITHDRAW_APPROVED

		// [ Notify]
		endProcess := time.Now()
		elapsed := endProcess.Sub(startProcess)
		elapsedSeconds := elapsed.Seconds()
		timeElapsed := fmt.Sprintf("%.2f", elapsedSeconds)
		externalNoti.TransId = transId
		externalNoti.Amount = req.Amount
		externalNoti.MemberCode = user.MemberCode
		externalNoti.UserCredit = agentResp.AgentAfterAmount
		externalNoti.TimerCounter = timeElapsed
		externalNoti.TypeNotify = model.IsWithdrawalAwaitingTransfer
		externalNoti.WebScoket.UserID = user.Id
		externalNoti.WebScoket.Amount = req.Amount
		externalNoti.WebScoket.MemberCode = user.MemberCode
		externalNoti.WebScoket.AlertType = "WITHDRAW"
		// if err := s.serviceNoti.ExternalNotification(externalNoti); err != nil {
		// 	log.Println("FailedNotify", err)
		// }
	}

	// else if validatedBank.Status == model.OUT_OF_CONFIG_AMOUNT {
	// 	statusId = model.TRANS_STATUS_WITHDRAW_OVER_MAX
	// 	externalNoti.TypeNotify = model.IsWithdrawalAwaitingTransfer
	// } else if bankAccount.AutoWithdrawTypeId == model.CONFIG_WEB_MANUAL_WITHDRAW || userSettingWithdraw == model.USER_WITHDRAW_SETTING_UNPASS || validatedBank.Status == model.CLOSE_AUTO_WITHDRAW {
	// 	statusId = model.TRANS_STATUS_WITHDRAW_APPROVED
	// 	externalNoti.TypeNotify = model.IsWithdrawalAwaitingTransfer
	// } else {
	// 	statusId = model.TRANS_STATUS_WITHDRAW_OVER_BUDGET
	// 	externalNoti.TypeNotify = model.IsWithdrawalAwaitingTransfer
	// }

	// ============================= ON_SUCCESS =================================

	// [update transaction status]
	var updateApprovedBy model.UpdateConfirmAutoWithdrawBody
	updateApprovedBy.TransactionStatusId = &statusId
	updateApprovedBy.ConfirmedAt = time.Now()
	updateApprovedBy.ConfirmedByAdminId = &setIdAuto
	if err := s.repoBanking.UpdateAdminAndTransactionStatus(*transId, updateApprovedBy); err != nil {
		log.Println("CheckPaymentGatewayAvailable.UpdateAdminAndTransactionStatus", err)
		return transId, internalServerError(err)
	}

	// [ Notify]
	if err := s.serviceNoti.ExternalNotification(externalNoti); err != nil {
		log.Println("FailedNotify", err)
	}
	return transId, nil
}

func (s bankingService) withdrawWithLucky(pgWdAccount model.PaygateAccountResponse, req model.UserCreatePaygateLuckyWithdrawRequest) (*int64, error) {

	sandBoxMode := false
	ginMode := os.Getenv("GIN_MODE")
	if ginMode == "debug" && pgWdAccount.ShopName == "DEV_DEMO" {
		sandBoxMode = true
	}

	// Bank Abbreviations = user.BankCode Support ?
	// KBANK	Kasikorn Bank
	// SCB	SCB
	// BBL	Bangkok Bank
	// KTB	KTB
	// TTB	TMB THANACHART BANK
	// CITI	CITIBANK
	// SCBT	STANDARD CHARTERED BANK (THAI)
	// CIMB	CIMB THAI BANK
	// UOBT	UNITED OVERSEAS BANK (THAI)
	// BAY	BANK OF AYUDHYA
	// GOV	GOVERNMENT SAVINGS BANK
	// GHB	THE GOVERNMENT HOUSING BANK
	// BAAC	BANK FOR AGRICULTURE AND AGRICULTURAL COOPERATIVES
	// BOC	BANK OF CHINA (THAI)
	// ISBT	ISLAMIC BANK OF THAILAND
	// KK	KIATNAKIN PHATRA BANK
	// ICBC	INDUSTRIAL AND COMMERCIAL BANK OF CHINA (THAI)
	// LHBANK	LAND AND HOUSES BANK

	reqBankCode := req.BankCode
	// rename bank code SYS=>LUCKYTH
	if reqBankCode == "BSB" {
		reqBankCode = "GOV"
	} else if reqBankCode == "UOB" {
		reqBankCode = "UOBT"
	} else if reqBankCode == "LH" {
		reqBankCode = "LHBANK"
	}
	acceptBankCodeList := []string{"KBANK", "SCB", "BBL", "BAY", "KTB", "TTB", "GOV", "BAAC", "KK", "GHB", "UOBT", "LHBANK", "CIMB", "ICBC", "ISBT", "CITI", "SCBT", "BOC"}
	if !helper.StringInArray(strings.ToUpper(reqBankCode), acceptBankCodeList) {
		return nil, badRequest("BANK_CODE_NOT_SUPPORT")
	}
	reqAccountNumber := helper.StripAllButNumbers(req.AccountNo)
	if reqAccountNumber == "" {
		return nil, badRequest("INVALID_ACCOUNT_NUMBER")
	}

	// ===========================================================================================
	// CREATE Order
	var createBody model.LuckyThaiOrderCreateBody
	createBody.RefId = req.RefId
	createBody.UserId = req.UserId
	createBody.OrderTypeId = model.PAYGATE_ORDER_TYPE_WITHDRAW
	createBody.OrderNo = "" // Autogen
	createBody.Amount = req.Amount
	createBody.TransactionStatus = "PENDING"
	insertId, err := s.repoBanking.CreateDbLuckyThaiOrder(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	// reget Order
	pendingOrder, err := s.repoBanking.GetDbLuckyThaiOrderById(*insertId)
	if err != nil {
		// SET AS ERROR
		remark := "Error GetDbLuckyThaiOrderById, " + err.Error()
		if err := s.repoBanking.UpdateDbLuckyThaiOrderError(*insertId, remark); err != nil {
			log.Println("CreateLuckyThaiWithdraw.UpdateDbLuckyThaiOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// Create LUCKYTH Order
	var remoteRequest model.LuckyThaiWithdrawCreateRemoteRequest
	remoteRequest.ReferenceNo = pendingOrder.OrderNo
	remoteRequest.BankCode = reqBankCode
	remoteRequest.AccountNo = reqAccountNumber
	remoteRequest.Accountname = req.Accountname
	remoteRequest.Amount = pendingOrder.Amount
	if sandBoxMode {
		// onCreate Success
		var updateBody model.LuckyThaiOrderUpdateBody
		updateBody.TransactionNo = helper.AlphaNumerics(7)
		updateBody.TransactionDate = time.Now()
		updateBody.TransactionStatus = "WAIT_PAYMENT"
		updateBody.QrPromptpay = "https://dev-admin.cbgame88.com"
		if err := s.repoBanking.UpdateDbLuckyThaiOrder(*insertId, updateBody); err != nil {
			// SET AS ERROR
			remark := "Error UpdateDbLuckyThaiOrder, " + err.Error()
			if err := s.repoBanking.UpdateDbLuckyThaiOrderError(*insertId, remark); err != nil {
				log.Println("CreateLuckyThaiWithdraw.UpdateDbLuckyThaiOrderError", err)
			}
			return nil, internalServerError(err)
		}
		return insertId, nil
	}
	remoteResp, err := s.repoBanking.LuckyThaiWithdraw(pgWdAccount, remoteRequest)
	if err != nil {
		// SET AS ERROR
		remark := "Error LuckyThaiWithdraw, " + err.Error()
		if err := s.repoBanking.UpdateDbLuckyThaiOrderError(*insertId, remark); err != nil {
			log.Println("LuckyThaiWithdraw.UpdateDbLuckyThaiOrderError", err)
		}
		// SysLog
		if _, err := s.repoBanking.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "CreateLuckyThaiWithdraw.LuckyThaiWithdraw",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(remoteRequest),
			JsonResponse: helper.StructJson(map[string]interface{}{"error": err.Error()}),
		}); err != nil {
			log.Println("CreateLuckyThaiWithdraw.CreateSysLog", err)
		}
		return nil, internalServerError(err)
	}

	// fmt.Println("CreateLuckyThaiWithdraw.remoteResp", helper.StructJson(remoteResp))

	// if remoteResp.Status.Code != "000" {
	// 	// SET AS ERROR
	// 	remark := "Error remoteResp.Status.Code != 000, remoteResp:" + helper.StructJson(remoteResp)
	// 	if err := s.repo.UpdateDbLuckyThaiOrderError(*insertId, remark); err != nil {
	// 		log.Println("CreateLuckyThaiWithdraw.UpdateDbLuckyThaiOrderError", err)
	// 	}
	// 	return nil, internalServerError(fmt.Errorf(remoteResp.Status.Message))
	// }

	// {
	// 	"success": true,
	// 	"code": 200,
	// 	"data": {
	// 		"orderNo": "DFBANKTHB000749T1713432982603RHlAxY",
	// 		"receiveAddr": "kbank|**********|dev 5002|IFSC",
	// 		"chainName": "BANK",
	// 		"coinUnit": "THB",
	// 		"requestAmount": 103,
	// 		"status": "PAYING",
	// 		"payUrl": "",
	// 		"hrefbackurl": "",
	// 		"sign": "fafc2e502ee92954dc3e6ddd59a5213f"
	// 	}
	// }

	// onCreate Success
	var updateBody model.LuckyThaiOrderUpdateBody
	updateBody.TransactionNo = remoteResp.Data.OrderNo
	updateBody.TransactionDate = time.Now()
	updateBody.TransactionStatus = "WAIT_PAYMENT"
	updateBody.QrPromptpay = remoteResp.Data.ReceiveAddr
	if err := s.repoBanking.UpdateDbLuckyThaiOrder(*insertId, updateBody); err != nil {
		// SET AS ERROR
		remark := "Error UpdateDbLuckyThaiOrder, " + err.Error()
		if err := s.repoBanking.UpdateDbLuckyThaiOrderError(*insertId, remark); err != nil {
			log.Println("CreateLuckyThaiWithdraw.UpdateDbLuckyThaiOrderError", err)
		}
		return nil, internalServerError(err)
	}
	return insertId, nil
}

func (s bankingService) withdrawWithPapaya(pgWdAccount model.PaygateAccountResponse, req model.UserCreatePaygateLuckyWithdrawRequest) (*int64, error) {

	// sandBoxMode := false
	// ginMode := os.Getenv("GIN_MODE")
	// if ginMode == "debug" && pgWdAccount.ShopName == "DEV_DEMO" {
	// 	sandBoxMode = true
	// }

	if !pgWdAccount.IsWithdrawEnabled {
		return nil, badRequest("PAYGATE_WITHDRAW_NOT_ENABLED")
	}

	user, err := s.repoBanking.GetUserBankDetailById(req.UserId)
	if err != nil {
		return nil, badRequest("INVALID_USER")
	}
	if user == nil || user.BankAccount == "" || user.BankCode == "" {
		return nil, badRequest("INVALID_USER_ACCOUNT")
	}

	reqAccountNumber := helper.StripAllButNumbers(user.BankAccount)
	if reqAccountNumber == "" {
		return nil, badRequest("INVALID_ACCOUNT_NUMBER")
	}
	// Bank Abbreviations = user.BankCode Support ?
	// Bank Code	Bank Name (Thai)	Bank Name (English)	Short Name
	// 001	ธนาคารแห่งประเทศไทย	BANK OF THAILAND	BOT
	// 002	ธนาคารกรุงเทพ จำกัด (มหาชน)	BANGKOK BANK PUBLIC COMPANY LTD.	BBL
	// 004	ธนาคารกสิกรไทย จำกัด (มหาชน)	KASIKORNBANK PUBLIC COMPANY LIMITED	KBANK
	// 006	ธนาคารกรุงไทย จำกัด (มหาชน)	KRUNG THAI BANK PUBLIC COMPANY LTD.	KTB
	// 011	ธนาคารทหารไทยธนชาต จำกัด (มหาชน)	TMB THANACHART BANK PUBLIC COMPANY LIMITED	TTB TMB
	// 014	ธนาคารไทยพาณิชย์ จำกัด (มหาชน)	SIAM COMMERCIAL BANK PUBLIC COMPANY LTD.	SCB
	// 017	ธนาคารซิตี้แบงก์ เอ็น.เอ.	CITIBANK, N.A.	CITI
	// 020	ธนาคารสแตนดาร์ดชาร์เตอร์ด (ไทย) จำกัด (มหาชน)	STANDARD CHARTERED BANK (THAI) PUBLIC COMPANY LIMITED	StandardCB
	// 022	ธนาคารซีไอเอ็มบี ไทย จำกัด (มหาชน)	CIMB THAI BANK PUBLIC COMPANY LIMITED	CIMB
	// 024	ธนาคารยูโอบี จำกัด (มหาชน)	UNITED OVERSEAS BANK (THAI) PUBLIC COMPANY LIMITED	UOB
	// 025	ธนาคารกรุงศรีอยุธยา จำกัด (มหาชน)	BANK OF AYUDHYA PUBLIC COMPANY LTD.	BAY
	// 030	ธนาคารออมสิน	GOVERNMENT SAVINGS BANK	GSB
	// 033	ธนาคารอาคารสงเคราะห์	THE GOVERNMENT HOUSING BANK	GHB
	// 034	ธนาคารเพื่อการเกษตรและสหกรณ์การเกษตร	BANK FOR AGRICULTURE AND AGRICULTURAL COOPERATIVES	BAAC
	// 052	ธนาคารแห่งประเทศจีน (ไทย) จำกัด (มหาชน)	BANK OF CHINA (THAI) PUBLIC COMPANY LIMITED	BOC
	// 066	ธนาคารอิสลามแห่งประเทศไทย	ISLAMIC BANK OF THAILAND	IBT
	// 067	ธนาคารทิสโก้ จำกัด (มหาชน)	TISCO BANK PUBLIC COMPANY LIMITED	TISCO
	// 069	ธนาคารเกียรตินาคินภัทร จำกัด (มหาชน)	KIATNAKIN PHATRA BANK PUBLIC COMPANY LIMITED	KKP
	// 070	ธนาคารไอซีบีซี (ไทย) จำกัด (มหาชน)	INDUSTRIAL AND COMMERCIAL BANK OF CHINA (THAI) PUBLIC COMPANY LIMITED	ICBC
	// 073	ธนาคารแลนด์ แอนด์ เฮ้าส์ จำกัด (มหาชน)	LAND AND HOUSES BANK PUBLIC COMPANY LIMITED	LHB
	acceptBankCodeList := []string{"BOT", "BBL", "KBANK", "KTB", "TTB", "SCB", "CITI", "StandardCB", "CIMB", "UOB", "BAY", "GSB", "GHB", "BAAC", "BOC", "IBT", "TISCO", "KKP", "ICBC", "LHBANK"}
	bankCodeMap := map[string]string{
		"BOT":        "001",
		"BBL":        "002",
		"KBANK":      "004",
		"KTB":        "006",
		"TTB":        "011",
		"SCB":        "014",
		"CITI":       "017",
		"StandardCB": "020",
		"CIMB":       "022",
		"UOB":        "024",
		"BAY":        "025",
		"GSB":        "030",
		"GHB":        "033",
		"BAAC":       "034",
		"BOC":        "052",
		"IBT":        "066",
		"TISCO":      "067",
		"KKP":        "069",
		"ICBC":       "070",
		"LHBANK":     "073",
	}

	reqBankCode := user.BankCode
	// rename bank code tb_bank=>PAPAYAPAY
	if reqBankCode == "LH" {
		reqBankCode = "LHBANK"
	}
	if !helper.StringInArray(strings.ToUpper(reqBankCode), acceptBankCodeList) {
		return nil, badRequest("BANK_CODE_NOT_SUPPORT")
	}
	ppyBankCode, ok := bankCodeMap[strings.ToUpper(reqBankCode)]
	if !ok {
		return nil, badRequest("BANK_CODE_NOT_SUPPORT")
	}

	// ===========================================================================================
	// CREATE Order
	var createBody model.PapayaPayOrderCreateBody
	createBody.RefId = req.RefId
	createBody.UserId = req.UserId
	createBody.OrderTypeId = model.PAPAYAPAY_ORDER_TYPE_WITHDRAW
	createBody.OrderNo = "" // Autogen
	createBody.Amount = req.Amount
	createBody.TransactionStatus = "PENDING"
	insertId, err := s.repoBanking.CreateDbPapayaPayOrder(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	// reget Order
	pendingOrder, err := s.repoBanking.GetDbPapayaPayOrderById(*insertId)
	if err != nil {
		// SET AS ERROR
		remark := "Error GetDbPapayaPayOrderById, " + err.Error()
		if err := s.repoBanking.UpdateDbPapayaPayOrderError(*insertId, remark); err != nil {
			log.Println("withdrawWithPapaya.UpdateDbPapayaPayOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// Create PAPAYAPAY Order
	var remoteRequest model.PapayaPayWithdrawCreateRemoteRequest
	remoteRequest.CurrencyCode = "THB"
	remoteRequest.FundOutPaymentReference = pendingOrder.OrderNo
	remoteRequest.FundOutDescription = "Withdrawal"
	remoteRequest.AccountName = user.Fullname
	remoteRequest.AccountNumber = reqAccountNumber
	remoteRequest.BankCode = ppyBankCode
	remoteRequest.Amount = pendingOrder.Amount
	remoteResp, err := s.repoBanking.PapayaPayWithdraw(pgWdAccount, remoteRequest)
	if err != nil {
		// SET AS ERROR
		remark := "Error PapayaPayWithdraw, " + err.Error()
		if err := s.repoBanking.UpdateDbPapayaPayOrderError(*insertId, remark); err != nil {
			log.Println("PapayaPayWithdraw.UpdateDbPapayaPayOrderError", err)
		}
		// SysLog
		if _, err := s.repoBanking.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "withdrawWithPapaya.PapayaPayWithdraw",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(remoteRequest),
			JsonResponse: helper.StructJson(map[string]interface{}{"error": err.Error()}),
		}); err != nil {
			log.Println("withdrawWithPapaya.CreateSysLog", err)
		}
		return nil, internalServerError(err)
	}

	// fmt.Println("withdrawWithPapaya.remoteResp", helper.StructJson(remoteResp))

	if remoteResp.StatusCode != 200 || remoteResp.Data.FundOutStatus != "PROCESSING" {
		// SET AS ERROR
		remark := remoteResp.Data.Message
		if len(remark) == 0 {
			remark = "UNKNOW_ERROR from withdrawWithPapayaPay"
		}
		if err := s.repoBanking.UpdateDbPapayaPayOrderError(*insertId, remark); err != nil {
			log.Println("PapayaPayWithdraw.UpdateDbPapayaPayOrderError", err)
		}
		// SysLog
		if _, err := s.repoBanking.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "withdrawWithPapaya.PapayaPayWithdraw",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(remoteRequest),
			JsonResponse: helper.StructJson(remoteResp),
		}); err != nil {
			log.Println("withdrawWithPapaya.CreateSysLog", err)
		}
		return nil, internalServerError(fmt.Errorf("UNKNOW_ERROR"))
	}

	// onCreate Success
	var updateBody model.PapayaPayOrderUpdateBody
	updateBody.TransactionNo = remoteResp.Data.Data.TransactionRef1
	updateBody.TransactionDate = time.Now()
	updateBody.TransactionStatus = "WAIT_PAYMENT"
	updateBody.QrPromptpay = remoteResp.Data.Data.FundOutCallbackStatus
	if err := s.repoBanking.UpdateDbPapayaPayOrder(*insertId, updateBody); err != nil {
		// SET AS ERROR
		remark := "Error UpdateDbPapayaPayOrder, " + err.Error()
		if err := s.repoBanking.UpdateDbPapayaPayOrderError(*insertId, remark); err != nil {
			log.Println("withdrawWithPapaya.UpdateDbPapayaPayOrderError", err)
		}
		return nil, internalServerError(err)
	}
	return insertId, nil
}

func (s bankingService) withdrawWithPayonex(pgWdAccount model.PaygateAccountResponse, req model.UserCreatePaygateLuckyWithdrawRequest) (*int64, error) {

	// sandBoxMode := false
	// ginMode := os.Getenv("GIN_MODE")
	// if ginMode == "debug" && pgWdAccount.ShopName == "DEV_DEMO" {
	// 	sandBoxMode = true
	// }

	if !pgWdAccount.IsWithdrawEnabled {
		return nil, badRequest("PAYGATE_WITHDRAW_NOT_ENABLED")
	}

	// ผมอยากเก็บ default เอาไว้กัน Admin ตั้งค่าต่ำกว่า payment
	if req.Amount < model.PAYONEX_WITHDRAW_AMOUNT_MINIMUM {
		return nil, badRequest("WITHDRAW_AMOUNT_MINIMUM_500")
	}
	if req.Amount < pgWdAccount.PaymentWithdrawMinimum {
		return nil, badRequest("WITHDRAW_AMOUNT_MINIMUM")
	}

	user, err := s.repoBanking.GetUserBankDetailById(req.UserId)
	if err != nil {
		return nil, badRequest("INVALID_USER")
	}
	if user == nil || user.BankAccount == "" || user.BankCode == "" {
		return nil, badRequest("INVALID_USER_ACCOUNT")
	}

	// get Token without PaygateAccount.
	token, err := GetPayonexAccessToken(repository.NewPayonexRepository(s.SharedDb), nil)
	if err != nil {
		log.Println("withdrawWithPayonex.GetPayonexAccessToken.ERROR", err)
		return nil, err
	}

	// PayonexUser
	payonexCustomer, err := CheckPayonexCustomerByUserId(repository.NewPayonexRepository(s.SharedDb), *user)
	if err != nil {
		log.Println("withdrawWithPayonex.CheckPayonexCustomerByUserId.ERROR", err)
		return nil, badRequest("INVALID_PAYGATE_USER")
	}
	if payonexCustomer.CustomerUuid == "" {
		log.Println("withdrawWithPayonex.CheckPayonexCustomerByUserId.ERROR", "EMPTY_PAYGATE_USER_UUID")
		return nil, badRequest("EMPTY_PAYGATE_USER")
	}

	// todo check User info VS CustomerInfo

	// ===========================================================================================
	// CREATE Order
	var createBody model.PayonexOrderCreateBody
	createBody.RefId = req.RefId
	createBody.UserId = req.UserId
	createBody.OrderTypeId = model.PAYONEX_ORDER_TYPE_WITHDRAW
	createBody.OrderNo = "" // Autogen
	createBody.Amount = req.Amount
	createBody.TransactionStatus = "PENDING"
	insertId, err := s.repoBanking.CreateDbPayonexOrder(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	// reget Order
	pendingOrder, err := s.repoBanking.GetDbPayonexOrderById(*insertId)
	if err != nil {
		// SET AS ERROR
		remark := "Error GetDbPayonexOrderById, " + err.Error()
		if err := s.repoBanking.UpdateDbPayonexOrderError(*insertId, remark); err != nil {
			log.Println("withdrawWithPayonex.UpdateDbPayonexOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// Create PAYONEX Order
	var remoteRequest model.PayonexWithdrawCreateRemoteRequest
	remoteRequest.CustomerUuid = payonexCustomer.CustomerUuid
	remoteRequest.ReferenceId = pendingOrder.OrderNo
	remoteRequest.Amount = pendingOrder.Amount
	remoteRequest.Note = "AutoWithdrawal"
	remoteRequest.Remark = ""
	remoteResp, err := s.repoBanking.PayonexWithdraw(token, pgWdAccount, remoteRequest)
	if err != nil {
		// SET AS ERROR
		remark := "Error PayonexWithdraw, " + err.Error()
		if err := s.repoBanking.UpdateDbPayonexOrderError(*insertId, remark); err != nil {
			log.Println("PayonexWithdraw.UpdateDbPayonexOrderError", err)
		}
		// SysLog
		if _, err := s.repoBanking.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "withdrawWithPayonex.PayonexWithdraw",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(remoteRequest),
			JsonResponse: helper.StructJson(map[string]interface{}{"error": err.Error()}),
		}); err != nil {
			log.Println("withdrawWithPayonex.CreateSysLog", err)
		}
		return nil, internalServerError(err)
	}

	// fmt.Println("withdrawWithPayonex.remoteResp", helper.StructJson(remoteResp))

	if !remoteResp.Success || remoteResp.Data.Uuid == "" {
		// SET AS ERROR
		remark := remoteResp.Message
		if len(remark) == 0 {
			remark = "UNKNOW_ERROR from withdrawWithPayonex"
		}
		if err := s.repoBanking.UpdateDbPayonexOrderError(*insertId, remark); err != nil {
			log.Println("PayonexWithdraw.UpdateDbPayonexOrderError", err)
		}
		// SysLog
		if _, err := s.repoBanking.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "withdrawWithPayonex.PayonexWithdraw",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(remoteRequest),
			JsonResponse: helper.StructJson(remoteResp),
		}); err != nil {
			log.Println("withdrawWithPayonex.CreateSysLog", err)
		}
		return nil, internalServerError(fmt.Errorf("UNKNOW_ERROR"))
	}

	// onCreate Success
	var updateBody model.PayonexOrderUpdateBody
	updateBody.TransactionNo = remoteResp.Data.Uuid
	updateBody.TransactionDate = time.Now()
	updateBody.TransactionStatus = "WAIT_PAYMENT"
	updateBody.QrPromptpay = remoteResp.Message
	if err := s.repoBanking.UpdateDbPayonexOrder(*insertId, updateBody); err != nil {
		// SET AS ERROR
		remark := "Error UpdateDbPayonexOrder, " + err.Error()
		if err := s.repoBanking.UpdateDbPayonexOrderError(*insertId, remark); err != nil {
			log.Println("withdrawWithPayonex.UpdateDbPayonexOrderError", err)
		}
		return nil, internalServerError(err)
	}
	return insertId, nil
}

func (s bankingService) withdrawWithJbpay(pgWdAccount model.PaygateAccountResponse, req model.UserCreatePaygateLuckyWithdrawRequest) (*int64, error) {

	if !pgWdAccount.IsWithdrawEnabled {
		return nil, badRequest("PAYGATE_WITHDRAW_NOT_ENABLED")
	}

	// ผมอยากเก็บ default เอาไว้กัน Adminตั้งค่าต่ำกว่า payment
	if req.Amount < 100 {
		return nil, badRequest("WITHDRAW_AMOUNT_MINIMUM_100")
	}
	if req.Amount < pgWdAccount.PaymentWithdrawMinimum {
		return nil, badRequest("WITHDRAW_AMOUNT_MINIMUM")
	}
	if pgWdAccount.PartnerKey == "" || pgWdAccount.LoanAppId == "" || pgWdAccount.MerchantId == "" || pgWdAccount.Token == "" || pgWdAccount.AesKey == "" || pgWdAccount.ApiEndPoint == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}

	user, err := s.repoBanking.GetUserBankDetailById(req.UserId)
	if err != nil {
		return nil, badRequest("INVALID_USER")
	}
	if user == nil || user.BankAccount == "" || user.BankCode == "" {
		return nil, badRequest("INVALID_USER_ACCOUNT")
	}

	// remoteRequest.UserAccountBank = user.BankCode // todo validate ?
	withdrawBankCode := user.BankCode
	// user mobile must start with prefix 66 or 856
	withdrawPhone := user.Phone
	if len(withdrawPhone) > 0 && strings.HasPrefix(withdrawPhone, "0") {
		withdrawPhone = "66" + withdrawPhone[1:]
	}

	// ===========================================================================================
	// CREATE Order
	var createBody model.JbpayOrderCreateBody
	createBody.RefId = req.RefId
	createBody.UserId = req.UserId
	createBody.OrderTypeId = model.JBPAY_ORDER_TYPE_WITHDRAW
	createBody.OrderNo = "" // Autogen
	createBody.Amount = req.Amount
	createBody.TransactionStatus = "PENDING"
	insertId, err := s.repoBanking.CreateDbJbpayOrder(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	// reget Order
	pendingOrder, err := s.repoBanking.GetDbJbpayOrderById(*insertId)
	if err != nil {
		// SET AS ERROR
		remark := "Error GetDbJbpayOrderById, " + err.Error()
		if err := s.repoBanking.UpdateDbJbpayOrderError(*insertId, remark); err != nil {
			log.Println("withdrawWithJbpay.UpdateDbJbpayOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// Create JBPAY Order
	var remoteRequest model.JbpayWithdrawCreateRemoteRequest
	remoteRequest.OrderNo = pendingOrder.OrderNo
	remoteRequest.Amount = pendingOrder.Amount
	remoteRequest.UserFullname = user.Fullname
	remoteRequest.UserMobile = withdrawPhone
	remoteRequest.UserAccountNumber = user.BankAccount
	remoteRequest.UserAccountBank = withdrawBankCode
	remoteResp, err := s.repoBanking.JbpayWithdraw(pgWdAccount, remoteRequest)
	if err != nil {
		// SET AS ERROR
		remark := "Error JbpayWithdraw, " + err.Error()
		if err := s.repoBanking.UpdateDbJbpayOrderError(*insertId, remark); err != nil {
			log.Println("JbpayWithdraw.UpdateDbJbpayOrderError", err)
		}
		// SysLog
		if _, err := s.repoBanking.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "withdrawWithJbpay.JbpayWithdraw",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(remoteRequest),
			JsonResponse: helper.StructJson(map[string]interface{}{"error": err.Error()}),
		}); err != nil {
			log.Println("withdrawWithJbpay.CreateSysLog", err)
		}
		return nil, internalServerError(err)
	}

	// fmt.Println("withdrawWithJbpay.remoteResp", helper.StructJson(remoteResp))

	if remoteResp.Code != "0" {
		// SET AS ERROR
		remark := remoteResp.Message
		if len(remark) == 0 {
			remark = "UNKNOW_ERROR from withdrawWithJbpay"
		}
		if err := s.repoBanking.UpdateDbJbpayOrderError(*insertId, remark); err != nil {
			log.Println("JbpayWithdraw.UpdateDbJbpayOrderError", err)
		}
		// SysLog
		if _, err := s.repoBanking.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "withdrawWithJbpay.JbpayWithdraw",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(remoteRequest),
			JsonResponse: helper.StructJson(remoteResp),
		}); err != nil {
			log.Println("withdrawWithJbpay.CreateSysLog", err)
		}
		return nil, internalServerError(fmt.Errorf("UNKNOW_ERROR"))
	}

	// onCreate Success
	var updateBody model.JbpayOrderUpdateBody
	updateBody.TransactionNo = remoteResp.Data.OrderNo
	updateBody.TransactionDate = time.Now()
	updateBody.TransactionStatus = "WAIT_PAYMENT"
	updateBody.QrPromptpay = remoteResp.Message
	if err := s.repoBanking.UpdateDbJbpayOrder(*insertId, updateBody); err != nil {
		// SET AS ERROR
		remark := "Error UpdateDbJbpayOrder, " + err.Error()
		if err := s.repoBanking.UpdateDbJbpayOrderError(*insertId, remark); err != nil {
			log.Println("withdrawWithJbpay.UpdateDbJbpayOrderError", err)
		}
		return nil, internalServerError(err)
	}
	return insertId, nil
}

func (s bankingService) withdrawWithPompay(pgWdAccount model.PaygateAccountResponse, req model.UserCreatePaygateLuckyWithdrawRequest) (*int64, error) {

	if !pgWdAccount.IsWithdrawEnabled {
		return nil, badRequest("PAYGATE_WITHDRAW_NOT_ENABLED")
	}

	// ผมอยากเก็บ default เอาไว้กัน Adminตั้งค่าต่ำกว่า payment
	if req.Amount < 100 {
		return nil, badRequest("WITHDRAW_AMOUNT_MINIMUM_100")
	}
	if req.Amount < pgWdAccount.PaymentWithdrawMinimum {
		return nil, badRequest("WITHDRAW_AMOUNT_MINIMUM")
	}
	if req.Amount > pgWdAccount.PaymentWithdrawMaximum {
		return nil, badRequest("WITHDRAW_AMOUNT_MAXIMUM")
	}
	if pgWdAccount.ApiEndPoint == "" || pgWdAccount.SecretKey == "" || pgWdAccount.MerchantId == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}

	user, err := s.repoBanking.GetUserBankDetailById(req.UserId)
	if err != nil {
		return nil, badRequest("INVALID_USER")
	}
	if user == nil || user.BankAccount == "" || user.BankCode == "" {
		return nil, badRequest("INVALID_USER_ACCOUNT")
	}

	// remoteRequest.UserAccountBank = user.BankCode // todo validate ?
	withdrawBankCode := user.BankCode
	// user mobile must start with prefix 66 or 856
	withdrawPhone := user.Phone
	if len(withdrawPhone) > 0 && strings.HasPrefix(withdrawPhone, "0") {
		withdrawPhone = "66" + withdrawPhone[1:]
	}

	// ===========================================================================================
	// CREATE Order
	var createBody model.PompayOrderCreateBody
	createBody.RefId = req.RefId
	createBody.UserId = req.UserId
	createBody.OrderTypeId = model.POMPAY_ORDER_TYPE_WITHDRAW
	createBody.OrderNo = "" // Autogen
	createBody.Amount = req.Amount
	createBody.TransactionStatus = "PENDING"
	insertId, err := s.repoBanking.CreateDbPompayOrder(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	// reget Order
	pendingOrder, err := s.repoBanking.GetDbPompayOrderById(*insertId)
	if err != nil {
		// SET AS ERROR
		remark := "Error GetDbPompayOrderById, " + err.Error()
		if err := s.repoBanking.UpdateDbPompayOrderError(*insertId, remark); err != nil {
			log.Println("withdrawWithPompay.UpdateDbPompayOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// Create POMPAY Order
	var remoteRequest model.PompayWithdrawCreateRemoteRequest
	remoteRequest.TransactionId = pendingOrder.OrderNo
	remoteRequest.Amount = fmt.Sprintf("%.2f", req.Amount)
	remoteRequest.CustName = user.Fullname
	remoteRequest.CustMobile = withdrawPhone
	remoteRequest.CustBank = withdrawBankCode
	remoteRequest.CustBankAcc = user.BankAccount
	remoteResp, err := s.repoBanking.PompayWithdraw(pgWdAccount, remoteRequest)
	if err != nil {
		// SET AS ERROR
		remark := "Error PompayWithdraw, " + err.Error()
		if err := s.repoBanking.UpdateDbPompayOrderError(*insertId, remark); err != nil {
			log.Println("PompayWithdraw.UpdateDbPompayOrderError", err)
		}
		// SysLog
		if _, err := s.repoBanking.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "withdrawWithPompay.PompayWithdraw",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(remoteRequest),
			JsonResponse: helper.StructJson(map[string]interface{}{"error": err.Error()}),
		}); err != nil {
			log.Println("withdrawWithPompay.CreateSysLog", err)
		}
		return nil, internalServerError(err)
	}

	// fmt.Println("withdrawWithPompay.remoteResp", helper.StructJson(remoteResp))

	if remoteResp.Data.ReferenceId == "" || remoteResp.Data.TransactionId == "" {
		// SET AS ERROR
		remark := remoteResp.Message
		if len(remark) == 0 {
			remark = "UNKNOW_ERROR from withdrawWithPompay"
		}
		if err := s.repoBanking.UpdateDbPompayOrderError(*insertId, remark); err != nil {
			log.Println("PompayWithdraw.UpdateDbPompayOrderError", err)
		}
		// SysLog
		if _, err := s.repoBanking.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "withdrawWithPompay.PompayWithdraw",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(remoteRequest),
			JsonResponse: helper.StructJson(remoteResp),
		}); err != nil {
			log.Println("withdrawWithPompay.CreateSysLog", err)
		}
		return nil, internalServerError(fmt.Errorf("UNKNOW_ERROR"))
	}

	// onCreate Success
	var updateBody model.PompayOrderUpdateBody
	updateBody.TransactionNo = remoteResp.Data.ReferenceId
	updateBody.TransactionDate = time.Now()
	updateBody.TransactionStatus = "WAIT_PAYMENT"
	updateBody.QrPromptpay = remoteResp.Message
	if err := s.repoBanking.UpdateDbPompayOrder(*insertId, updateBody); err != nil {
		// SET AS ERROR
		remark := "Error UpdateDbPompayOrder, " + err.Error()
		if err := s.repoBanking.UpdateDbPompayOrderError(*insertId, remark); err != nil {
			log.Println("withdrawWithPompay.UpdateDbPompayOrderError", err)
		}
		return nil, internalServerError(err)
	}
	return insertId, nil
}

func (s bankingService) withdrawWithPaymentco(pgWdAccount model.PaygateAccountResponse, req model.UserCreatePaygateLuckyWithdrawRequest) (*int64, error) {

	if !pgWdAccount.IsWithdrawEnabled {
		return nil, badRequest("PAYGATE_WITHDRAW_NOT_ENABLED")
	}

	// ผมอยากเก็บ default เอาไว้กัน Adminตั้งค่าต่ำกว่า payment
	if req.Amount < 100 {
		return nil, badRequest("WITHDRAW_AMOUNT_MINIMUM_100")
	}
	if req.Amount < pgWdAccount.PaymentWithdrawMinimum {
		return nil, badRequest("WITHDRAW_AMOUNT_MINIMUM")
	}
	if req.Amount > pgWdAccount.PaymentWithdrawMaximum {
		return nil, badRequest("WITHDRAW_AMOUNT_MAXIMUM")
	}
	if pgWdAccount.ApiEndPoint == "" || pgWdAccount.SecretKey == "" || pgWdAccount.MerchantId == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}

	user, err := s.repoBanking.GetUserBankDetailById(req.UserId)
	if err != nil {
		return nil, badRequest("INVALID_USER")
	}
	if user == nil || user.BankAccount == "" || user.BankCode == "" {
		return nil, badRequest("INVALID_USER_ACCOUNT")
	}

	// remoteRequest.UserAccountBank = user.BankCode // todo validate ?
	withdrawBankCode := user.BankCode
	// user mobile must start with prefix 66 or 856
	// withdrawPhone := user.Phone
	// if len(withdrawPhone) > 0 && strings.HasPrefix(withdrawPhone, "0") {
	// 	withdrawPhone = "66" + withdrawPhone[1:]
	// }

	// ===========================================================================================
	// CREATE Order
	var createBody model.PaymentcoOrderCreateBody
	createBody.RefId = req.RefId
	createBody.UserId = req.UserId
	createBody.OrderTypeId = model.PAYMENTCO_ORDER_TYPE_WITHDRAW
	createBody.OrderNo = "" // Autogen
	createBody.Amount = req.Amount
	createBody.TransactionStatus = "PENDING"
	insertId, err := s.repoBanking.CreateDbPaymentcoOrder(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	// reget Order
	pendingOrder, err := s.repoBanking.GetDbPaymentcoOrderById(*insertId)
	if err != nil {
		// SET AS ERROR
		remark := "Error GetDbPaymentcoOrderById, " + err.Error()
		if err := s.repoBanking.UpdateDbPaymentcoOrderError(*insertId, remark); err != nil {
			log.Println("withdrawWithPaymentco.UpdateDbPaymentcoOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// Create PAYMENTCO Order
	var remoteRequest model.PaymentcoWithdrawCreateRemoteRequest
	remoteRequest.OrderId = pendingOrder.OrderNo
	remoteRequest.Amount = fmt.Sprintf("%.2f", req.Amount)
	remoteRequest.AccountName = user.Fullname
	remoteRequest.BankName = withdrawBankCode
	remoteRequest.AccountNo = user.BankAccount
	remoteResp, err := s.repoBanking.PaymentcoWithdraw(pgWdAccount, remoteRequest)
	if err != nil {
		// SET AS ERROR
		remark := "Error PaymentcoWithdraw, " + err.Error()
		if err := s.repoBanking.UpdateDbPaymentcoOrderError(*insertId, remark); err != nil {
			log.Println("PaymentcoWithdraw.UpdateDbPaymentcoOrderError", err)
		}
		// SysLog
		if _, err := s.repoBanking.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "withdrawWithPaymentco.PaymentcoWithdraw",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(remoteRequest),
			JsonResponse: helper.StructJson(map[string]interface{}{"error": err.Error()}),
		}); err != nil {
			log.Println("withdrawWithPaymentco.CreateSysLog", err)
		}
		return nil, internalServerError(err)
	}

	// fmt.Println("withdrawWithPaymentco.remoteResp", helper.StructJson(remoteResp))

	if remoteResp.Data.ReferenceId == "" || remoteResp.Data.TransactionId == "" {
		// SET AS ERROR
		remark := remoteResp.Message
		if len(remark) == 0 {
			remark = "UNKNOW_ERROR from withdrawWithPaymentco"
		}
		if err := s.repoBanking.UpdateDbPaymentcoOrderError(*insertId, remark); err != nil {
			log.Println("PaymentcoWithdraw.UpdateDbPaymentcoOrderError", err)
		}
		// SysLog
		if _, err := s.repoBanking.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "withdrawWithPaymentco.PaymentcoWithdraw",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(remoteRequest),
			JsonResponse: helper.StructJson(remoteResp),
		}); err != nil {
			log.Println("withdrawWithPaymentco.CreateSysLog", err)
		}
		return nil, internalServerError(fmt.Errorf("UNKNOW_ERROR"))
	}

	// onCreate Success
	var updateBody model.PaymentcoOrderUpdateBody
	updateBody.TransactionNo = remoteResp.Data.ReferenceId
	updateBody.TransactionDate = time.Now()
	updateBody.TransactionStatus = "WAIT_PAYMENT"
	updateBody.QrPromptpay = remoteResp.Message
	if err := s.repoBanking.UpdateDbPaymentcoOrder(*insertId, updateBody); err != nil {
		// SET AS ERROR
		remark := "Error UpdateDbPaymentcoOrder, " + err.Error()
		if err := s.repoBanking.UpdateDbPaymentcoOrderError(*insertId, remark); err != nil {
			log.Println("withdrawWithPaymentco.UpdateDbPaymentcoOrderError", err)
		}
		return nil, internalServerError(err)
	}
	return insertId, nil
}

func (s bankingService) withdrawWithZappay(pgWdAccount model.PaygateAccountResponse, req model.UserCreatePaygateLuckyWithdrawRequest) (*int64, error) {

	if !pgWdAccount.IsWithdrawEnabled {
		return nil, badRequest("PAYGATE_WITHDRAW_NOT_ENABLED")
	}

	// ผมอยากเก็บ default เอาไว้กัน Adminตั้งค่าต่ำกว่า payment
	if req.Amount < 100 {
		return nil, badRequest("WITHDRAW_AMOUNT_MINIMUM_100")
	}
	if req.Amount < pgWdAccount.PaymentWithdrawMinimum {
		return nil, badRequest("WITHDRAW_AMOUNT_MINIMUM")
	}
	if pgWdAccount.PartnerKey == "" || pgWdAccount.LoanAppId == "" || pgWdAccount.MerchantId == "" || pgWdAccount.Token == "" || pgWdAccount.AesKey == "" || pgWdAccount.ApiEndPoint == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}

	user, err := s.repoBanking.GetUserBankDetailById(req.UserId)
	if err != nil {
		return nil, badRequest("INVALID_USER")
	}
	if user == nil || user.BankAccount == "" || user.BankCode == "" {
		return nil, badRequest("INVALID_USER_ACCOUNT")
	}

	// remoteRequest.UserAccountBank = user.BankCode // todo validate ?
	withdrawBankCode := user.BankCode
	// user mobile must start with prefix 66 or 856
	withdrawPhone := user.Phone
	if len(withdrawPhone) > 0 && strings.HasPrefix(withdrawPhone, "0") {
		withdrawPhone = "66" + withdrawPhone[1:]
	}

	// ===========================================================================================
	// CREATE Order
	var createBody model.ZappayOrderCreateBody
	createBody.RefId = req.RefId
	createBody.UserId = req.UserId
	createBody.OrderTypeId = model.ZAPPAY_ORDER_TYPE_WITHDRAW
	createBody.OrderNo = "" // Autogen
	createBody.Amount = req.Amount
	createBody.TransactionStatus = "PENDING"
	insertId, err := s.repoBanking.CreateDbZappayOrder(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	// reget Order
	pendingOrder, err := s.repoBanking.GetDbZappayOrderById(*insertId)
	if err != nil {
		// SET AS ERROR
		remark := "Error GetDbZappayOrderById, " + err.Error()
		if err := s.repoBanking.UpdateDbZappayOrderError(*insertId, remark); err != nil {
			log.Println("withdrawWithZappay.UpdateDbZappayOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// Create ZAPPAY Order
	var remoteRequest model.ZappayWithdrawCreateRemoteRequest
	remoteRequest.OrderNo = pendingOrder.OrderNo
	remoteRequest.Amount = pendingOrder.Amount
	remoteRequest.UserFullname = user.Fullname
	remoteRequest.UserMobile = withdrawPhone
	remoteRequest.UserAccountNumber = user.BankAccount
	remoteRequest.UserAccountBank = withdrawBankCode
	remoteResp, err := s.repoBanking.ZappayWithdraw(pgWdAccount, remoteRequest)
	if err != nil {
		// SET AS ERROR
		remark := "Error ZappayWithdraw, " + err.Error()
		if err := s.repoBanking.UpdateDbZappayOrderError(*insertId, remark); err != nil {
			log.Println("ZappayWithdraw.UpdateDbZappayOrderError", err)
		}
		// SysLog
		if _, err := s.repoBanking.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "withdrawWithZappay.ZappayWithdraw",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(remoteRequest),
			JsonResponse: helper.StructJson(map[string]interface{}{"error": err.Error()}),
		}); err != nil {
			log.Println("withdrawWithZappay.CreateSysLog", err)
		}
		return nil, internalServerError(err)
	}

	// fmt.Println("withdrawWithZappay.remoteResp", helper.StructJson(remoteResp))

	if remoteResp.Code != "0" {
		// SET AS ERROR
		remark := remoteResp.Message
		if len(remark) == 0 {
			remark = "UNKNOW_ERROR from withdrawWithZappay"
		}
		if err := s.repoBanking.UpdateDbZappayOrderError(*insertId, remark); err != nil {
			log.Println("ZappayWithdraw.UpdateDbZappayOrderError", err)
		}
		// SysLog
		if _, err := s.repoBanking.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "withdrawWithZappay.ZappayWithdraw",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(remoteRequest),
			JsonResponse: helper.StructJson(remoteResp),
		}); err != nil {
			log.Println("withdrawWithZappay.CreateSysLog", err)
		}
		return nil, internalServerError(fmt.Errorf("UNKNOW_ERROR"))
	}

	// onCreate Success
	var updateBody model.ZappayOrderUpdateBody
	updateBody.TransactionNo = remoteResp.Data.OrderNo
	updateBody.TransactionDate = time.Now()
	updateBody.TransactionStatus = "WAIT_PAYMENT"
	updateBody.QrPromptpay = remoteResp.Message
	if err := s.repoBanking.UpdateDbZappayOrder(*insertId, updateBody); err != nil {
		// SET AS ERROR
		remark := "Error UpdateDbZappayOrder, " + err.Error()
		if err := s.repoBanking.UpdateDbZappayOrderError(*insertId, remark); err != nil {
			log.Println("withdrawWithZappay.UpdateDbZappayOrderError", err)
		}
		return nil, internalServerError(err)
	}
	return insertId, nil
}

func (s bankingService) withdrawWithOnepay(pgWdAccount model.PaygateAccountResponse, req model.UserCreatePaygateLuckyWithdrawRequest) (*int64, error) {

	// ผมอยากเก็บ default เอาไว้กัน Adminตั้งค่าต่ำกว่า payment
	if req.Amount < model.ONEPAY_DEFMIN_WITHDRAW_AMOUNT {
		return nil, badRequest("WITHDRAW_AMOUNT_DEFMIN")
	}
	if !pgWdAccount.IsWithdrawEnabled {
		return nil, badRequest("PAYGATE_WITHDRAW_NOT_ENABLED")
	}
	if req.Amount < pgWdAccount.PaymentWithdrawMinimum {
		return nil, badRequest("WITHDRAW_AMOUNT_MINIMUM")
	}
	if pgWdAccount.ApiEndPoint == "" || pgWdAccount.PartnerKey == "" || pgWdAccount.LoanAppId == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}
	// (ปิดถอนช่วง 23:00-00:30 น. รวม 1.30 ชม.) **ยังไม่คอนเฟิมว่าปิดถอนช่วงเวลาไหน
	// actionAtUtc := time.Now().UTC()
	// bkkLoc := time.FixedZone("Asia/Bangkok", 7*60*60)
	// actionAtBkk := actionAtUtc.In(bkkLoc)
	// if actionAtBkk.Hour() >= 23 || (actionAtBkk.Hour() == 0 && actionAtBkk.Minute() <= 30) {
	// 	log.Println("withdrawWithOnepay", "WITHDRAW_NOT_AVAILABLE (ปิดถอนช่วง 23:00-00:30 น. รวม 1.30 ชม.)", actionAtBkk)
	// 	return nil, errors.New("WITHDRAW_NOT_AVAILABLE")
	// }

	user, err := s.repoBanking.GetUserBankDetailById(req.UserId)
	if err != nil {
		return nil, badRequest("INVALID_USER")
	}
	if user == nil || user.BankAccount == "" || user.BankCode == "" {
		return nil, badRequest("INVALID_USER_ACCOUNT")
	}

	webhookDomain := os.Getenv("CYBERGAME_WEBHOOK_ENDPOINT")
	if webhookDomain == "" {
		return nil, errors.New("EMPTY_ENV_CYBERGAME_WEBHOOK_ENDPOINT")
	}

	withdrawBankCode, err := GetOnepayCustomerBank(user.BankCode)
	if err != nil || withdrawBankCode == "" {
		return nil, badRequest("USER_BANK_NOT_SUPPORTED")
	}

	token, err := GetOnepayAccessToken(repository.NewOnepayRepository(s.SharedDb), nil)
	if err != nil {
		// return nil, internalServerError(errors.New("INVALID_ACCESS_TOKEN"))
		return nil, err
	}

	// ===========================================================================================
	// CREATE Order
	var createBody model.OnepayOrderCreateBody
	createBody.RefId = req.RefId
	createBody.UserId = req.UserId
	createBody.OrderTypeId = model.ONEPAY_ORDER_TYPE_WITHDRAW
	createBody.OrderNo = "" // Autogen
	createBody.Amount = req.Amount
	createBody.TransactionStatus = "PENDING"
	insertId, err := s.repoBanking.CreateDbOnepayOrder(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	// reget Order
	pendingOrder, err := s.repoBanking.GetDbOnepayOrderById(*insertId)
	if err != nil {
		// SET AS ERROR
		remark := "Error GetDbOnepayOrderById, " + err.Error()
		if err := s.repoBanking.UpdateDbOnepayOrderError(*insertId, remark); err != nil {
			log.Println("withdrawWithOnepay.UpdateDbOnepayOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// Create ONEPAY Order
	var remoteRequest model.OnepayWithdrawCreateRemoteRequest
	remoteRequest.OrderId = pendingOrder.OrderNo
	remoteRequest.Amount = pendingOrder.Amount
	remoteRequest.BankAccNo = user.BankAccount
	remoteRequest.BankCode = withdrawBankCode
	remoteRequest.BankAccName = user.Fullname
	// รหัส Pin Code ที่ใส่ยืนยันรายการเมื่อรายการถอนมียอดถอนมากกว่าขั้นต่ำ
	remoteRequest.WithdrawCode = pgWdAccount.LoanAppId
	remoteRequest.CallbackUrl = fmt.Sprintf("%s/onepay/wid-callback", webhookDomain)
	remoteResp, err := s.repoBanking.OnepayWithdraw(token, pgWdAccount, remoteRequest)
	if err != nil {
		// SET AS ERROR
		remark := "Error OnepayWithdraw, " + err.Error()
		if err := s.repoBanking.UpdateDbOnepayOrderError(*insertId, remark); err != nil {
			log.Println("OnepayWithdraw.UpdateDbOnepayOrderError", err)
		}
		// SysLog
		if _, err := s.repoBanking.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "withdrawWithOnepay.OnepayWithdraw",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(remoteRequest),
			JsonResponse: helper.StructJson(map[string]interface{}{"error": err.Error()}),
		}); err != nil {
			log.Println("withdrawWithOnepay.CreateSysLog", err)
		}
		return nil, internalServerError(err)
	}

	// fmt.Println("withdrawWithOnepay.remoteResp", helper.StructJson(remoteResp))

	// if remoteResp.Code != "0" {
	// 	// SET AS ERROR
	// 	remark := remoteResp.Message
	// 	if len(remark) == 0 {
	// 		remark = "UNKNOW_ERROR from withdrawWithOnepay"
	// 	}
	// 	if err := s.repoBanking.UpdateDbOnepayOrderError(*insertId, remark); err != nil {
	// 		log.Println("OnepayWithdraw.UpdateDbOnepayOrderError", err)
	// 	}
	// 	// SysLog
	// 	if _, err := s.repoBanking.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
	// 		Name:         "withdrawWithOnepay.OnepayWithdraw",
	// 		Status:       "ERROR",
	// 		JsonReq:      helper.StructJson(remoteRequest),
	// 		JsonResponse: helper.StructJson(remoteResp),
	// 	}); err != nil {
	// 		log.Println("withdrawWithOnepay.CreateSysLog", err)
	// 	}
	// 	return nil, internalServerError(fmt.Errorf("UNKNOW_ERROR"))
	// }

	// onCreate Success
	var updateBody model.OnepayOrderUpdateBody
	updateBody.TransactionNo = remoteResp.Data.TxnNo
	updateBody.TransactionDate = time.Now().UTC()
	updateBody.TransactionStatus = "WAIT_PAYMENT"
	updateBody.QrPromptpay = remoteResp.Message
	if err := s.repoBanking.UpdateDbOnepayOrder(*insertId, updateBody); err != nil {
		// SET AS ERROR
		remark := "Error UpdateDbOnepayOrder, " + err.Error()
		if err := s.repoBanking.UpdateDbOnepayOrderError(*insertId, remark); err != nil {
			log.Println("withdrawWithOnepay.UpdateDbOnepayOrderError", err)
		}
		return nil, internalServerError(err)
	}
	return insertId, nil
}

func (s bankingService) withdrawWithFlashpay(pgWdAccount model.PaygateAccountResponse, req model.UserCreatePaygateLuckyWithdrawRequest) (*int64, error) {

	// ผมอยากเก็บ default เอาไว้กัน Adminตั้งค่าต่ำกว่า payment
	if req.Amount < model.FLASHPAY_DEFMIN_WITHDRAW_AMOUNT {
		return nil, badRequest("WITHDRAW_AMOUNT_DEFMIN")
	}
	if !pgWdAccount.IsWithdrawEnabled {
		return nil, badRequest("PAYGATE_WITHDRAW_NOT_ENABLED")
	}
	if req.Amount < pgWdAccount.PaymentWithdrawMinimum {
		return nil, badRequest("WITHDRAW_AMOUNT_MINIMUM")
	}
	// Prerequisites
	if pgWdAccount.ApiEndPoint == "" || pgWdAccount.PartnerKey == "" || pgWdAccount.AccessKey == "" || pgWdAccount.SecretKey == "" || pgWdAccount.MerchantId == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}
	// (ปิดถอนช่วง 23:00-00:30 น. รวม 1.30 ชม.) **ยังไม่คอนเฟิมว่าปิดถอนช่วงเวลาไหน
	// actionAtUtc := time.Now().UTC()
	// bkkLoc := time.FixedZone("Asia/Bangkok", 7*60*60)
	// actionAtBkk := actionAtUtc.In(bkkLoc)
	// if actionAtBkk.Hour() >= 23 || (actionAtBkk.Hour() == 0 && actionAtBkk.Minute() <= 30) {
	// 	log.Println("withdrawWithFlashpay", "WITHDRAW_NOT_AVAILABLE (ปิดถอนช่วง 23:00-00:30 น. รวม 1.30 ชม.)", actionAtBkk)
	// 	return nil, errors.New("WITHDRAW_NOT_AVAILABLE")
	// }

	user, err := s.repoBanking.GetUserBankDetailById(req.UserId)
	if err != nil {
		return nil, badRequest("INVALID_USER")
	}
	if user == nil || user.BankAccount == "" || user.BankCode == "" {
		return nil, badRequest("INVALID_USER_ACCOUNT")
	}

	webhookDomain := os.Getenv("CYBERGAME_WEBHOOK_ENDPOINT")
	if webhookDomain == "" {
		return nil, errors.New("EMPTY_ENV_CYBERGAME_WEBHOOK_ENDPOINT")
	}

	withdrawBankCode, err := GetFlashpayCustomerBank(user.BankCode)
	if err != nil || withdrawBankCode == "" {
		return nil, badRequest("USER_BANK_NOT_SUPPORTED")
	}

	// token, err := GetFlashpayAccessToken(repository.NewFlashpayRepository(s.SharedDb), nil)
	// if err != nil {
	// 	// return nil, internalServerError(errors.New("INVALID_ACCESS_TOKEN"))
	// 	return nil, err
	// }

	// ===========================================================================================
	// CREATE Order
	var createBody model.FlashpayOrderCreateBody
	createBody.RefId = req.RefId
	createBody.UserId = req.UserId
	createBody.OrderTypeId = model.FLASHPAY_ORDER_TYPE_WITHDRAW
	createBody.OrderNo = "" // Autogen
	createBody.Amount = req.Amount
	createBody.TransactionStatus = "PENDING"
	insertId, err := s.repoBanking.CreateDbFlashpayOrder(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	// reget Order
	pendingOrder, err := s.repoBanking.GetDbFlashpayOrderById(*insertId)
	if err != nil {
		// SET AS ERROR
		remark := "Error GetDbFlashpayOrderById, " + err.Error()
		if err := s.repoBanking.UpdateDbFlashpayOrderError(*insertId, remark); err != nil {
			log.Println("withdrawWithFlashpay.UpdateDbFlashpayOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// Create FLASHPAY Order
	var remoteRequest model.FlashpayWithdrawCreateRemoteRequest
	remoteRequest.TransactionId = pendingOrder.OrderNo
	remoteRequest.Amount = pendingOrder.Amount
	remoteRequest.BankAccountNumber = user.BankAccount
	remoteRequest.BankName = withdrawBankCode
	remoteRequest.Name = user.Fullname
	remoteRequest.Phone = user.Phone
	remoteRequest.CallbackUrl = fmt.Sprintf("%s/flashpay/wid-callback", webhookDomain)
	remoteResp, err := s.repoBanking.FlashpayWithdraw(pgWdAccount, remoteRequest)
	if err != nil {
		// SET AS ERROR
		remark := "Error FlashpayWithdraw, " + err.Error()
		if err := s.repoBanking.UpdateDbFlashpayOrderError(*insertId, remark); err != nil {
			log.Println("FlashpayWithdraw.UpdateDbFlashpayOrderError", err)
		}
		// SysLog At REPO
		return nil, internalServerError(err)
	}

	// fmt.Println("withdrawWithFlashpay.remoteResp", helper.StructJson(remoteResp))

	// if remoteResp.Code != "0" {
	// 	// SET AS ERROR
	// 	remark := remoteResp.Message
	// 	if len(remark) == 0 {
	// 		remark = "UNKNOW_ERROR from withdrawWithFlashpay"
	// 	}
	// 	if err := s.repoBanking.UpdateDbFlashpayOrderError(*insertId, remark); err != nil {
	// 		log.Println("FlashpayWithdraw.UpdateDbFlashpayOrderError", err)
	// 	}
	// 	// SysLog
	// 	if _, err := s.repoBanking.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
	// 		Name:         "withdrawWithFlashpay.FlashpayWithdraw",
	// 		Status:       "ERROR",
	// 		JsonReq:      helper.StructJson(remoteRequest),
	// 		JsonResponse: helper.StructJson(remoteResp),
	// 	}); err != nil {
	// 		log.Println("withdrawWithFlashpay.CreateSysLog", err)
	// 	}
	// 	return nil, internalServerError(fmt.Errorf("UNKNOW_ERROR"))
	// }

	// onCreate Success
	var updateBody model.FlashpayOrderUpdateBody
	updateBody.TransactionNo = remoteResp.Data.ReferenceId
	updateBody.TransactionDate = time.Now().UTC()
	updateBody.TransactionStatus = "WAIT_PAYMENT"
	updateBody.QrPromptpay = remoteResp.Message
	if err := s.repoBanking.UpdateDbFlashpayOrder(*insertId, updateBody); err != nil {
		// SET AS ERROR
		remark := "Error UpdateDbFlashpayOrder, " + err.Error()
		if err := s.repoBanking.UpdateDbFlashpayOrderError(*insertId, remark); err != nil {
			log.Println("withdrawWithFlashpay.UpdateDbFlashpayOrderError", err)
		}
		return nil, internalServerError(err)
	}
	return insertId, nil
}

func (s bankingService) withdrawWithBizpay(pgWdAccount model.PaygateAccountResponse, req model.UserCreatePaygateLuckyWithdrawRequest) (*int64, error) {

	// ผมอยากเก็บ default เอาไว้กัน Adminตั้งค่าต่ำกว่า payment
	if req.Amount < model.BIZPAY_DEFMIN_WITHDRAW_AMOUNT {
		return nil, badRequest("WITHDRAW_AMOUNT_DEFMIN")
	}
	if !pgWdAccount.IsWithdrawEnabled {
		return nil, badRequest("PAYGATE_WITHDRAW_NOT_ENABLED")
	}
	if req.Amount < pgWdAccount.PaymentWithdrawMinimum {
		return nil, badRequest("WITHDRAW_AMOUNT_MINIMUM")
	}
	// Prerequisites
	if pgWdAccount.ApiEndPoint == "" || pgWdAccount.PartnerKey == "" || pgWdAccount.AccessKey == "" || pgWdAccount.SecretKey == "" || pgWdAccount.MerchantId == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}
	// (ปิดถอนช่วง 23:00-00:30 น. รวม 1.30 ชม.) **ยังไม่คอนเฟิมว่าปิดถอนช่วงเวลาไหน
	// actionAtUtc := time.Now().UTC()
	// bkkLoc := time.FixedZone("Asia/Bangkok", 7*60*60)
	// actionAtBkk := actionAtUtc.In(bkkLoc)
	// if actionAtBkk.Hour() >= 23 || (actionAtBkk.Hour() == 0 && actionAtBkk.Minute() <= 30) {
	// 	log.Println("withdrawWithBizpay", "WITHDRAW_NOT_AVAILABLE (ปิดถอนช่วง 23:00-00:30 น. รวม 1.30 ชม.)", actionAtBkk)
	// 	return nil, errors.New("WITHDRAW_NOT_AVAILABLE")
	// }

	user, err := s.repoBanking.GetUserBankDetailById(req.UserId)
	if err != nil {
		return nil, badRequest("INVALID_USER")
	}
	if user == nil || user.BankAccount == "" || user.BankCode == "" {
		return nil, badRequest("INVALID_USER_ACCOUNT")
	}

	webhookDomain := os.Getenv("CYBERGAME_WEBHOOK_ENDPOINT")
	if webhookDomain == "" {
		return nil, errors.New("EMPTY_ENV_CYBERGAME_WEBHOOK_ENDPOINT")
	}

	withdrawBankCode, err := GetBizpayCustomerBank(user.BankCode)
	if err != nil || withdrawBankCode == "" {
		return nil, badRequest("USER_BANK_NOT_SUPPORTED")
	}

	// token, err := GetBizpayAccessToken(repository.NewBizpayRepository(s.SharedDb), nil)
	// if err != nil {
	// 	// return nil, internalServerError(errors.New("INVALID_ACCESS_TOKEN"))
	// 	return nil, err
	// }

	// ===========================================================================================
	// CREATE Order
	var createBody model.BizpayOrderCreateBody
	createBody.RefId = req.RefId
	createBody.UserId = req.UserId
	createBody.OrderTypeId = model.BIZPAY_ORDER_TYPE_WITHDRAW
	createBody.OrderNo = "" // Autogen
	createBody.Amount = req.Amount
	createBody.TransactionStatus = "PENDING"
	insertId, err := s.repoBanking.CreateDbBizpayOrder(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	// reget Order
	pendingOrder, err := s.repoBanking.GetDbBizpayOrderById(*insertId)
	if err != nil {
		// SET AS ERROR
		remark := "Error GetDbBizpayOrderById, " + err.Error()
		if err := s.repoBanking.UpdateDbBizpayOrderError(*insertId, remark); err != nil {
			log.Println("withdrawWithBizpay.UpdateDbBizpayOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// Create BIZPAY Order
	var remoteRequest model.BizpayWithdrawCreateRemoteRequest
	remoteRequest.TransactionId = pendingOrder.OrderNo
	remoteRequest.Amount = pendingOrder.Amount
	remoteRequest.BankAccountNumber = user.BankAccount
	remoteRequest.BankName = withdrawBankCode
	remoteRequest.Name = user.Fullname
	remoteRequest.Phone = user.Phone
	remoteRequest.CallbackUrl = fmt.Sprintf("%s/bizpay/wid-callback", webhookDomain)
	remoteResp, err := s.repoBanking.BizpayWithdraw(pgWdAccount, remoteRequest)
	if err != nil {
		// SET AS ERROR
		remark := "Error BizpayWithdraw, " + err.Error()
		if err := s.repoBanking.UpdateDbBizpayOrderError(*insertId, remark); err != nil {
			log.Println("BizpayWithdraw.UpdateDbBizpayOrderError", err)
		}
		// SysLog At REPO
		return nil, internalServerError(err)
	}

	// fmt.Println("withdrawWithBizpay.remoteResp", helper.StructJson(remoteResp))

	// if remoteResp.Code != "0" {
	// 	// SET AS ERROR
	// 	remark := remoteResp.Message
	// 	if len(remark) == 0 {
	// 		remark = "UNKNOW_ERROR from withdrawWithBizpay"
	// 	}
	// 	if err := s.repoBanking.UpdateDbBizpayOrderError(*insertId, remark); err != nil {
	// 		log.Println("BizpayWithdraw.UpdateDbBizpayOrderError", err)
	// 	}
	// 	// SysLog
	// 	if _, err := s.repoBanking.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
	// 		Name:         "withdrawWithBizpay.BizpayWithdraw",
	// 		Status:       "ERROR",
	// 		JsonReq:      helper.StructJson(remoteRequest),
	// 		JsonResponse: helper.StructJson(remoteResp),
	// 	}); err != nil {
	// 		log.Println("withdrawWithBizpay.CreateSysLog", err)
	// 	}
	// 	return nil, internalServerError(fmt.Errorf("UNKNOW_ERROR"))
	// }

	// onCreate Success
	var updateBody model.BizpayOrderUpdateBody
	updateBody.TransactionNo = remoteResp.Data.ReferenceId
	updateBody.TransactionDate = time.Now().UTC()
	updateBody.TransactionStatus = "WAIT_PAYMENT"
	updateBody.QrPromptpay = remoteResp.Message
	if err := s.repoBanking.UpdateDbBizpayOrder(*insertId, updateBody); err != nil {
		// SET AS ERROR
		remark := "Error UpdateDbBizpayOrder, " + err.Error()
		if err := s.repoBanking.UpdateDbBizpayOrderError(*insertId, remark); err != nil {
			log.Println("withdrawWithBizpay.UpdateDbBizpayOrderError", err)
		}
		return nil, internalServerError(err)
	}
	return insertId, nil
}

func (s bankingService) withdrawWithSugarpay(pgWdAccount model.PaygateAccountResponse, req model.UserCreatePaygateLuckyWithdrawRequest) (*int64, error) {

	// ผมอยากเก็บ default เอาไว้กัน Adminตั้งค่าต่ำกว่า payment
	if req.Amount < model.SUGARPAY_DEFMIN_WITHDRAW_AMOUNT {
		return nil, badRequest("WITHDRAW_AMOUNT_DEFMIN")
	}
	if !pgWdAccount.IsWithdrawEnabled {
		return nil, badRequest("PAYGATE_WITHDRAW_NOT_ENABLED")
	}
	if req.Amount < pgWdAccount.PaymentWithdrawMinimum {
		return nil, badRequest("WITHDRAW_AMOUNT_MINIMUM")
	}
	// PrerequisitesSugarpay merchant.PartnerKey == "" || || merchant.MerchantId == ""
	if pgWdAccount.ApiEndPoint == "" || pgWdAccount.AccessKey == "" || pgWdAccount.SecretKey == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}
	// (ปิดถอนช่วง 23:00-00:30 น. รวม 1.30 ชม.) **ยังไม่คอนเฟิมว่าปิดถอนช่วงเวลาไหน
	// actionAtUtc := time.Now().UTC()
	// bkkLoc := time.FixedZone("Asia/Bangkok", 7*60*60)
	// actionAtBkk := actionAtUtc.In(bkkLoc)
	// if actionAtBkk.Hour() >= 23 || (actionAtBkk.Hour() == 0 && actionAtBkk.Minute() <= 30) {
	// 	log.Println("withdrawWithSugarpay", "WITHDRAW_NOT_AVAILABLE (ปิดถอนช่วง 23:00-00:30 น. รวม 1.30 ชม.)", actionAtBkk)
	// 	return nil, errors.New("WITHDRAW_NOT_AVAILABLE")
	// }

	user, err := s.repoBanking.GetUserBankDetailById(req.UserId)
	if err != nil {
		return nil, badRequest("INVALID_USER")
	}
	if user == nil || user.BankAccount == "" || user.BankCode == "" {
		return nil, badRequest("INVALID_USER_ACCOUNT")
	}

	webhookDomain := os.Getenv("CYBERGAME_WEBHOOK_ENDPOINT")
	if webhookDomain == "" {
		return nil, errors.New("EMPTY_ENV_CYBERGAME_WEBHOOK_ENDPOINT")
	}

	withdrawBankCode, err := GetSugarpayCustomerBank(user.BankCode)
	if err != nil || withdrawBankCode == "" {
		return nil, badRequest("USER_BANK_NOT_SUPPORTED")
	}

	// ===========================================================================================
	// CREATE Order
	var createBody model.SugarpayOrderCreateBody
	createBody.RefId = req.RefId
	createBody.UserId = req.UserId
	createBody.OrderTypeId = model.SUGARPAY_ORDER_TYPE_WITHDRAW
	createBody.OrderNo = "" // Autogen
	createBody.Amount = req.Amount
	createBody.TransactionStatus = "PENDING"
	insertId, err := s.repoBanking.CreateDbSugarpayOrder(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	// reget Order
	pendingOrder, err := s.repoBanking.GetDbSugarpayOrderById(*insertId)
	if err != nil {
		// SET AS ERROR
		remark := "Error GetDbSugarpayOrderById, " + err.Error()
		if err := s.repoBanking.UpdateDbSugarpayOrderError(*insertId, remark); err != nil {
			log.Println("withdrawWithSugarpay.UpdateDbSugarpayOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// Create SUGARPAY Order
	var remoteRequest model.SugarpayWithdrawCreateRemoteRequest
	remoteRequest.OrderId = pendingOrder.OrderNo
	remoteRequest.Amount = pendingOrder.Amount
	remoteRequest.ToAccountNo = user.BankAccount
	remoteRequest.ToBankCode = withdrawBankCode
	remoteRequest.ToName = user.Fullname
	remoteRequest.CallbackUrl = fmt.Sprintf("%s/sugarpay/wid-callback", webhookDomain)
	remoteResp, err := s.repoBanking.SugarpayWithdraw(pgWdAccount, remoteRequest)
	if err != nil {
		// SET AS ERROR
		remark := "Error SugarpayWithdraw, " + err.Error()
		if err := s.repoBanking.UpdateDbSugarpayOrderError(*insertId, remark); err != nil {
			log.Println("SugarpayWithdraw.UpdateDbSugarpayOrderError", err)
		}
		// SysLog At REPO
		return nil, internalServerError(err)
	}

	fmt.Println("withdrawWithSugarpay.remoteResp", helper.StructJson(remoteResp))

	if remoteResp.Result.RefId == "" || remoteResp.Result.OrderId == "" {
		// SET AS ERROR
		remark := remoteResp.Message
		if len(remark) == 0 {
			remark = "UNKNOW_ERROR from withdrawWithSugarpay"
		}
		if err := s.repoBanking.UpdateDbSugarpayOrderError(*insertId, remark); err != nil {
			log.Println("SugarpayWithdraw.UpdateDbSugarpayOrderError", err)
		}
		// SysLog
		if _, err := s.repoBanking.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "withdrawWithSugarpay.SugarpayWithdraw",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(remoteRequest),
			JsonResponse: helper.StructJson(remoteResp),
		}); err != nil {
			log.Println("withdrawWithSugarpay.CreateSysLog", err)
		}
		return nil, internalServerError(fmt.Errorf("UNKNOW_ERROR"))
	}

	// onCreate Success
	var updateBody model.SugarpayOrderUpdateBody
	updateBody.TransactionNo = remoteResp.Result.RefId
	updateBody.TransactionDate = time.Now().UTC()
	updateBody.TransactionStatus = "WAIT_PAYMENT"
	updateBody.QrPromptpay = remoteResp.Message
	if err := s.repoBanking.UpdateDbSugarpayOrder(*insertId, updateBody); err != nil {
		// SET AS ERROR
		remark := "Error UpdateDbSugarpayOrder, " + err.Error()
		if err := s.repoBanking.UpdateDbSugarpayOrderError(*insertId, remark); err != nil {
			log.Println("withdrawWithSugarpay.UpdateDbSugarpayOrderError", err)
		}
		return nil, internalServerError(err)
	}
	return insertId, nil
}

func (s bankingService) withdrawWithZmanpay(pgWdAccount model.PaygateAccountResponse, req model.UserCreatePaygateLuckyWithdrawRequest) (*int64, error) {

	// ผมอยากเก็บ default เอาไว้กัน Adminตั้งค่าต่ำกว่า payment
	if req.Amount < model.ZMANPAY_DEFMIN_WITHDRAW_AMOUNT {
		return nil, badRequest("WITHDRAW_AMOUNT_DEFMIN")
	}
	if !pgWdAccount.IsWithdrawEnabled {
		return nil, badRequest("PAYGATE_WITHDRAW_NOT_ENABLED")
	}
	if req.Amount < pgWdAccount.PaymentWithdrawMinimum {
		return nil, badRequest("WITHDRAW_AMOUNT_MINIMUM")
	}
	// PrerequisitesZmanpay merchant.PartnerKey == "" || || merchant.MerchantId == ""
	if pgWdAccount.ApiEndPoint == "" || pgWdAccount.AccessKey == "" || pgWdAccount.SecretKey == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}

	user, err := s.repoBanking.GetUserBankDetailById(req.UserId)
	if err != nil {
		return nil, badRequest("INVALID_USER")
	}
	if user == nil || user.BankAccount == "" || user.BankCode == "" {
		return nil, badRequest("INVALID_USER_ACCOUNT")
	}

	webhookDomain := os.Getenv("CYBERGAME_WEBHOOK_ENDPOINT")
	if webhookDomain == "" {
		return nil, errors.New("EMPTY_ENV_CYBERGAME_WEBHOOK_ENDPOINT")
	}

	withdrawBankCode, err := GetZmanpayCustomerBankUuid(user.BankCode)
	if err != nil || withdrawBankCode == "" {
		return nil, badRequest("USER_BANK_NOT_SUPPORTED")
	}

	// Payment Extra Data
	// 1.CheckZmanpayCustomerByUserId
	payGateRepo := repository.NewZmanpayRepository(s.SharedDb)
	payCustomer, err := CheckZmanpayCustomerByUserId(payGateRepo, *user)
	if err != nil {
		log.Println("withdrawWithZmanpay.CheckZmanpayCustomerByUserId.ERROR", err)
		return nil, badRequest("INVALID_PAYGATE_USER")
	}
	if payCustomer.CustomerUuid == "" {
		log.Println("withdrawWithZmanpay.CheckZmanpayCustomerByUserId.ERROR", "EMPTY_PAYGATE_USER_UUID")
		return nil, badRequest("EMPTY_PAYGATE_USER")
	}

	// ===========================================================================================
	// CREATE Order
	var createBody model.ZmanpayOrderCreateBody
	createBody.RefId = req.RefId
	createBody.UserId = req.UserId
	createBody.OrderTypeId = model.ZMANPAY_ORDER_TYPE_WITHDRAW
	createBody.OrderNo = "" // Autogen
	createBody.Amount = req.Amount
	createBody.TransactionStatus = "PENDING"
	insertId, err := s.repoBanking.CreateDbZmanpayOrder(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	// reget Order
	pendingOrder, err := s.repoBanking.GetDbZmanpayOrderById(*insertId)
	if err != nil {
		// SET AS ERROR
		remark := "Error GetDbZmanpayOrderById, " + err.Error()
		if err := s.repoBanking.UpdateDbZmanpayOrderError(*insertId, remark); err != nil {
			log.Println("withdrawWithZmanpay.UpdateDbZmanpayOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// Create ZMANPAY Order
	var remoteRequest model.ZmanpayWithdrawCreateRemoteRequest
	remoteRequest.CustomerAccountUuid = payCustomer.CustomerUuid
	remoteRequest.Amount = pendingOrder.Amount
	remoteRequest.Currency = "thb"
	remoteRequest.CallbackUrl = fmt.Sprintf("%s/zmanpay/wid-callback", webhookDomain)
	remoteRequest.MerchantOrderId = pendingOrder.OrderNo
	remoteResp, err := s.repoBanking.ZmanpayWithdraw(pgWdAccount, remoteRequest)
	if err != nil {
		// SET AS ERROR
		remark := "Error ZmanpayWithdraw, " + err.Error()
		if err := s.repoBanking.UpdateDbZmanpayOrderError(*insertId, remark); err != nil {
			log.Println("withdrawWithZmanpay.UpdateDbZmanpayOrderError", err)
		}
		// SysLog At REPO
		return nil, internalServerError(err)
	}

	// fmt.Println("withdrawWithZmanpay.remoteResp", helper.StructJson(remoteResp))

	if remoteResp.Code != "ZAP20000" && remoteResp.Code != "20000" {
		log.Println("withdrawWithZmanpay.remoteResp", helper.StructJson(remoteResp))
		// SET AS ERROR
		remark := remoteResp.Message
		if len(remark) == 0 {
			remark = "UNKNOW_ERROR from withdrawWithZmanpay"
		}
		if err := s.repoBanking.UpdateDbZmanpayOrderError(*insertId, remark); err != nil {
			log.Println("withdrawWithZmanpay.UpdateDbZmanpayOrderError", err)
		}
		// SysLog
		if _, err := s.repoBanking.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "withdrawWithZmanpay.ZmanpayWithdraw",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(remoteRequest),
			JsonResponse: helper.StructJson(remoteResp),
		}); err != nil {
			log.Println("withdrawWithZmanpay.CreateSysLog", err)
		}
		return nil, internalServerError(fmt.Errorf("UNKNOW_ERROR"))
	}

	// onCreate Success
	var updateBody model.ZmanpayOrderUpdateBody
	updateBody.TransactionNo = remoteResp.Data.Uuid
	updateBody.TransactionDate = time.Now().UTC()
	updateBody.TransactionStatus = "WAIT_PAYMENT"
	updateBody.QrPromptpay = remoteResp.Message
	if err := s.repoBanking.UpdateDbZmanpayOrder(*insertId, updateBody); err != nil {
		// SET AS ERROR
		remark := "Error UpdateDbZmanpayOrder, " + err.Error()
		if err := s.repoBanking.UpdateDbZmanpayOrderError(*insertId, remark); err != nil {
			log.Println("withdrawWithZmanpay.UpdateDbZmanpayOrderError", err)
		}
		return nil, internalServerError(err)
	}
	return insertId, nil
}

func (s bankingService) withdrawWithPostmanPay(pgWdAccount model.PaygateAccountResponse, req model.UserCreatePaygateLuckyWithdrawRequest) (*int64, error) {

	// ผมอยากเก็บ default เอาไว้กัน Adminตั้งค่าต่ำกว่า payment
	if req.Amount < model.POSTMANPAY_DEFMIN_WITHDRAW_AMOUNT {
		return nil, badRequest("WITHDRAW_AMOUNT_DEFMIN")
	}
	if !pgWdAccount.IsWithdrawEnabled {
		return nil, badRequest("PAYGATE_WITHDRAW_NOT_ENABLED")
	}
	if req.Amount < pgWdAccount.PaymentWithdrawMinimum {
		return nil, badRequest("WITHDRAW_AMOUNT_MINIMUM")
	}
	// [Prerequisites] EP + CLIENTID + APIKEY + SECRET + MERCHANTID
	if pgWdAccount.ApiEndPoint == "" || pgWdAccount.PartnerKey == "" || pgWdAccount.AccessKey == "" || pgWdAccount.SecretKey == "" || pgWdAccount.MerchantId == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}

	user, err := s.repoBanking.GetUserBankDetailById(req.UserId)
	if err != nil {
		return nil, badRequest("INVALID_USER")
	}
	if user == nil || user.BankAccount == "" || user.BankCode == "" {
		return nil, badRequest("INVALID_USER_ACCOUNT")
	}

	webhookDomain := os.Getenv("CYBERGAME_WEBHOOK_ENDPOINT")
	if webhookDomain == "" {
		return nil, errors.New("EMPTY_ENV_CYBERGAME_WEBHOOK_ENDPOINT")
	}

	withdrawBankCode, err := GetPostmanPayCustomerBank(user.BankCode)
	if err != nil || withdrawBankCode == "" {
		return nil, badRequest("USER_BANK_NOT_SUPPORTED")
	}

	// ===========================================================================================
	// CREATE Order
	var createBody model.PostmanPayOrderCreateBody
	createBody.RefId = req.RefId
	createBody.UserId = req.UserId
	createBody.OrderTypeId = model.POSTMANPAY_ORDER_TYPE_WITHDRAW
	createBody.OrderNo = "" // Autogen
	createBody.Amount = req.Amount
	createBody.TransactionStatus = "PENDING"
	insertId, err := s.repoBanking.CreateDbPostmanPayOrder(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	// reget Order
	pendingOrder, err := s.repoBanking.GetDbPostmanPayOrderById(*insertId)
	if err != nil {
		// SET AS ERROR
		remark := "Error GetDbPostmanPayOrderById, " + err.Error()
		if err := s.repoBanking.UpdateDbPostmanPayOrderError(*insertId, remark); err != nil {
			log.Println("withdrawWithPostmanPay.UpdateDbPostmanPayOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// Create POSTMANPAY Order
	var remoteRequest model.PostmanPayWithdrawCreateRemoteRequest
	remoteRequest.TransactionId = pendingOrder.OrderNo
	remoteRequest.Amount = pendingOrder.Amount
	remoteRequest.BankAccountNumber = user.BankAccount
	remoteRequest.BankName = withdrawBankCode
	remoteRequest.Name = user.Fullname
	remoteRequest.Phone = user.Phone
	remoteRequest.CallbackUrl = fmt.Sprintf("%s/postmanpay/wid-callback", webhookDomain)
	remoteResp, err := s.repoBanking.PostmanPayWithdraw(pgWdAccount, remoteRequest)
	if err != nil {
		// SET AS ERROR
		remark := "Error PostmanPayWithdraw, " + err.Error()
		if err := s.repoBanking.UpdateDbPostmanPayOrderError(*insertId, remark); err != nil {
			log.Println("PostmanPayWithdraw.UpdateDbPostmanPayOrderError", err)
		}
		// SysLog At REPO
		return nil, internalServerError(err)
	}

	fmt.Println("withdrawWithPostmanPay.remoteResp", helper.StructJson(remoteResp))

	// onCreate Success
	var updateBody model.PostmanPayOrderUpdateBody
	updateBody.TransactionNo = remoteResp.Data.ReferenceId
	updateBody.TransactionDate = time.Now().UTC()
	updateBody.TransactionStatus = "WAIT_PAYMENT"
	updateBody.QrPromptpay = remoteResp.Message
	if err := s.repoBanking.UpdateDbPostmanPayOrder(*insertId, updateBody); err != nil {
		// SET AS ERROR
		remark := "Error UpdateDbPostmanPayOrder, " + err.Error()
		if err := s.repoBanking.UpdateDbPostmanPayOrderError(*insertId, remark); err != nil {
			log.Println("withdrawWithPostmanPay.UpdateDbPostmanPayOrderError", err)
		}
		return nil, internalServerError(err)
	}
	return insertId, nil
}

func (s bankingService) withdrawWithMazepay(pgWdAccount model.PaygateAccountResponse, req model.UserCreatePaygateLuckyWithdrawRequest) (*int64, error) {

	// ผมอยากเก็บ default เอาไว้กัน Adminตั้งค่าต่ำกว่า payment
	if req.Amount < model.MAZEPAY_DEFMIN_WITHDRAW_AMOUNT {
		return nil, badRequest("WITHDRAW_AMOUNT_DEFMIN")
	}
	if !pgWdAccount.IsWithdrawEnabled {
		return nil, badRequest("PAYGATE_WITHDRAW_NOT_ENABLED")
	}
	if req.Amount < pgWdAccount.PaymentWithdrawMinimum {
		return nil, badRequest("WITHDRAW_AMOUNT_MINIMUM")
	}
	// PrerequisitesMazepay merchant.PartnerKey == "" || || merchant.MerchantId == ""
	if pgWdAccount.ApiEndPoint == "" || pgWdAccount.AccessKey == "" || pgWdAccount.SecretKey == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}

	user, err := s.repoBanking.GetUserBankDetailById(req.UserId)
	if err != nil {
		return nil, badRequest("INVALID_USER")
	}
	if user == nil || user.BankAccount == "" || user.BankCode == "" {
		return nil, badRequest("INVALID_USER_ACCOUNT")
	}

	webhookDomain := os.Getenv("CYBERGAME_WEBHOOK_ENDPOINT")
	if webhookDomain == "" {
		return nil, errors.New("EMPTY_ENV_CYBERGAME_WEBHOOK_ENDPOINT")
	}

	withdrawBankCode, err := GetMazepayCustomerBankUuid(user.BankCode)
	if err != nil || withdrawBankCode == "" {
		return nil, badRequest("USER_BANK_NOT_SUPPORTED")
	}

	// Payment Extra Data
	// 1.CheckMazepayCustomerByUserId
	payGateRepo := repository.NewMazepayRepository(s.SharedDb)
	payCustomer, err := CheckMazepayCustomerByUserId(payGateRepo, *user)
	if err != nil {
		log.Println("withdrawWithMazepay.CheckMazepayCustomerByUserId.ERROR", err)
		return nil, badRequest("INVALID_PAYGATE_USER")
	}
	if payCustomer.CustomerUuid == "" {
		log.Println("withdrawWithMazepay.CheckMazepayCustomerByUserId.ERROR", "EMPTY_PAYGATE_USER_UUID")
		return nil, badRequest("EMPTY_PAYGATE_USER")
	}

	// ===========================================================================================
	// CREATE Order
	var createBody model.MazepayOrderCreateBody
	createBody.RefId = req.RefId
	createBody.UserId = req.UserId
	createBody.OrderTypeId = model.MAZEPAY_ORDER_TYPE_WITHDRAW
	createBody.OrderNo = "" // Autogen
	createBody.Amount = req.Amount
	createBody.TransactionStatus = "PENDING"
	insertId, err := s.repoBanking.CreateDbMazepayOrder(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	// reget Order
	pendingOrder, err := s.repoBanking.GetDbMazepayOrderById(*insertId)
	if err != nil {
		// SET AS ERROR
		remark := "Error GetDbMazepayOrderById, " + err.Error()
		if err := s.repoBanking.UpdateDbMazepayOrderError(*insertId, remark); err != nil {
			log.Println("withdrawWithMazepay.UpdateDbMazepayOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// Create MAZEPAY Order
	var remoteRequest model.MazepayWithdrawCreateRemoteRequest
	remoteRequest.CustomerAccountUuid = payCustomer.CustomerUuid
	remoteRequest.Amount = pendingOrder.Amount
	remoteRequest.Currency = "thb"
	remoteRequest.CallbackUrl = fmt.Sprintf("%s/mazepay/wid-callback", webhookDomain)
	remoteRequest.MerchantOrderId = pendingOrder.OrderNo
	remoteResp, err := s.repoBanking.MazepayWithdraw(pgWdAccount, remoteRequest)
	if err != nil {
		// SET AS ERROR
		remark := "Error MazepayWithdraw, " + err.Error()
		if err := s.repoBanking.UpdateDbMazepayOrderError(*insertId, remark); err != nil {
			log.Println("withdrawWithMazepay.UpdateDbMazepayOrderError", err)
		}
		// SysLog At REPO
		return nil, internalServerError(err)
	}

	// fmt.Println("withdrawWithMazepay.remoteResp", helper.StructJson(remoteResp))

	if remoteResp.Code != "ZAP20000" && remoteResp.Code != "20000" {
		log.Println("withdrawWithMazepay.remoteResp", helper.StructJson(remoteResp))
		// SET AS ERROR
		remark := remoteResp.Message
		if len(remark) == 0 {
			remark = "UNKNOW_ERROR from withdrawWithMazepay"
		}
		if err := s.repoBanking.UpdateDbMazepayOrderError(*insertId, remark); err != nil {
			log.Println("withdrawWithMazepay.UpdateDbMazepayOrderError", err)
		}
		// SysLog
		if _, err := s.repoBanking.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "withdrawWithMazepay.MazepayWithdraw",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(remoteRequest),
			JsonResponse: helper.StructJson(remoteResp),
		}); err != nil {
			log.Println("withdrawWithMazepay.CreateSysLog", err)
		}
		return nil, internalServerError(fmt.Errorf("UNKNOW_ERROR"))
	}

	// onCreate Success
	var updateBody model.MazepayOrderUpdateBody
	updateBody.TransactionNo = remoteResp.Data.Uuid
	updateBody.TransactionDate = time.Now().UTC()
	updateBody.TransactionStatus = "WAIT_PAYMENT"
	updateBody.QrPromptpay = remoteResp.Message
	if err := s.repoBanking.UpdateDbMazepayOrder(*insertId, updateBody); err != nil {
		// SET AS ERROR
		remark := "Error UpdateDbMazepayOrder, " + err.Error()
		if err := s.repoBanking.UpdateDbMazepayOrderError(*insertId, remark); err != nil {
			log.Println("withdrawWithMazepay.UpdateDbMazepayOrderError", err)
		}
		return nil, internalServerError(err)
	}
	return insertId, nil
}

func (s bankingService) withdrawWithMeepay(pgWdAccount model.PaygateAccountResponse, req model.UserCreatePaygateLuckyWithdrawRequest) (*int64, error) {

	// ผมอยากเก็บ default เอาไว้กัน Adminตั้งค่าต่ำกว่า payment
	if req.Amount < model.MEEPAY_DEFMIN_WITHDRAW_AMOUNT {
		return nil, badRequest("WITHDRAW_AMOUNT_DEFMIN")
	}
	if !pgWdAccount.IsWithdrawEnabled {
		return nil, badRequest("PAYGATE_WITHDRAW_NOT_ENABLED")
	}
	if req.Amount < pgWdAccount.PaymentWithdrawMinimum {
		return nil, badRequest("WITHDRAW_AMOUNT_MINIMUM")
	}
	// PrerequisitesMeepay
	if pgWdAccount.ApiEndPoint == "" || pgWdAccount.MerchantId == "" || pgWdAccount.SecretKey == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}

	user, err := s.repoBanking.GetUserBankDetailById(req.UserId)
	if err != nil {
		return nil, badRequest("INVALID_USER")
	}
	if user == nil || user.BankAccount == "" || user.BankCode == "" {
		return nil, badRequest("INVALID_USER_ACCOUNT")
	}
	withdrawBankCode, err := GetMeepayCustomerBankUuid(user.BankCode)
	if err != nil || withdrawBankCode == "" {
		return nil, badRequest("USER_BANK_NOT_SUPPORTED")
	}

	webhookDomain := os.Getenv("CYBERGAME_WEBHOOK_ENDPOINT")
	if webhookDomain == "" {
		return nil, errors.New("EMPTY_ENV_CYBERGAME_WEBHOOK_ENDPOINT")
	}

	// Payment Extra Data
	// NOPE

	// ===========================================================================================
	// CREATE Order
	var createBody model.MeepayOrderCreateBody
	createBody.RefId = req.RefId
	createBody.UserId = req.UserId
	createBody.OrderTypeId = model.MEEPAY_ORDER_TYPE_WITHDRAW
	createBody.OrderNo = "" // Autogen
	createBody.Amount = req.Amount
	createBody.TransactionStatus = "" // Autogen
	insertId, err := s.repoBanking.CreateDbMeepayOrder(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	// reget Order
	pendingOrder, err := s.repoBanking.GetDbMeepayOrderById(*insertId)
	if err != nil {
		// SET AS ERROR
		remark := "Error GetDbMeepayOrderById, " + err.Error()
		if err := s.repoBanking.UpdateDbMeepayOrderError(*insertId, remark); err != nil {
			log.Println("withdrawWithMeepay.UpdateDbMeepayOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// Create MEEPAY Order
	var remoteRequest model.MeepayWithdrawCreateRemoteRequest
	remoteRequest.Amount = req.Amount
	remoteRequest.Reference = pendingOrder.OrderNo
	remoteRequest.CallbackUrl = fmt.Sprintf("%s/meepay/wid-callback", webhookDomain)
	remoteRequest.BankId = user.BankAccount
	remoteRequest.BankName = user.Fullname
	remoteRequest.BankCode = withdrawBankCode
	remoteResp, err := s.repoBanking.MeepayWithdraw(pgWdAccount, remoteRequest)
	if err != nil {
		// SET AS ERROR
		remark := "Error MeepayWithdraw, " + err.Error()
		if err := s.repoBanking.UpdateDbMeepayOrderError(*insertId, remark); err != nil {
			log.Println("withdrawWithMeepay.UpdateDbMeepayOrderError", err)
		}
		// SysLog At REPO
		return nil, internalServerError(err)
	}

	fmt.Println("withdrawWithMeepay.remoteResp=", helper.StructJson(remoteResp))

	if strings.ToLower(remoteResp.Status) != "succeeded" {
		log.Println("withdrawWithMeepay.remoteResp=", helper.StructJson(remoteResp))
		// SET AS ERROR
		remark := remoteResp.Message
		if len(remark) == 0 {
			remark = "UNKNOW_ERROR from withdrawWithMeepay"
		}
		if err := s.repoBanking.UpdateDbMeepayOrderError(*insertId, remark); err != nil {
			log.Println("withdrawWithMeepay.UpdateDbMeepayOrderError", err)
		}
		// SysLog
		if _, err := s.repoBanking.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "withdrawWithMeepay.MeepayWithdraw",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(remoteRequest),
			JsonResponse: helper.StructJson(remoteResp),
		}); err != nil {
			log.Println("withdrawWithMeepay.CreateSysLog", err)
		}
		return nil, internalServerError(fmt.Errorf("UNKNOW_ERROR"))
	}

	// onCreate Success
	var updateBody model.MeepayOrderUpdateBody
	updateBody.TransactionNo = remoteResp.TransactionId
	updateBody.TransactionDate = time.Now().UTC()
	updateBody.TransactionStatus = model.MEEPAY_ORDER_STATUS_WAIT_PAYMENT
	updateBody.QrPromptpay = remoteResp.Message
	if err := s.repoBanking.UpdateDbMeepayOrder(*insertId, updateBody); err != nil {
		log.Println("withdrawWithMeepay.UpdateDbMeepayOrder.ERROR=", err)
		// SET AS ERROR
		remark := "Error UpdateDbMeepayOrder, " + err.Error()
		if err := s.repoBanking.UpdateDbMeepayOrderError(*insertId, remark); err != nil {
			log.Println("withdrawWithMeepay.UpdateDbMeepayOrderError.ERROR=", err)
		}
		return nil, internalServerError(err)
	}
	return insertId, nil
}

func (s *bankingService) UserCreateWithdrawTransaction(req model.UserCreateWithdrawTransactionRequest) (*model.FastbankWithdrawTransactionResponse, error) {

	var startProcess time.Time
	// Limit-Withdraw-Per-Day, Max User Withdraw Per Day
	userBankLimit, err := s.repoBanking.GetUserBankLimit(req.UserId)
	if err != nil {
		return nil, err
	}
	if userBankLimit.MaxUserWithdrawAmount > 0 && userBankLimit.MaxUserWithdrawAmount < (req.Amount+userBankLimit.CurrentWithdrawAmount) {
		return nil, badRequest("MAX_USER_WITHDRAW_PER_DAY")
	}
	if userBankLimit.MaxUserWithdrawCount > 0 && userBankLimit.MaxUserWithdrawCount < (userBankLimit.CurrentWithdrawCount+1) {
		return nil, badRequest("MAX_USER_WITHDRAW_COUNT_PER_DAY")
	}

	// ปรับ ให้เช็ค turn ก่อน เพราะ จะมี เงื่อนไขถอน logic ในการหา ยอด เครดิตที่สามารถ ถอนได้จริงๆ
	// [Check TurnOver Withdraw]
	if err := s.TurnOverWithdrawChecker(req.UserId, req.Amount); err != nil {
		statusLog := "ERROR"
		jsonPayLoad := fmt.Sprintf("USER_TRY_TO_CREATE_WITHDRAW_TRANSACTION_BUT_CHECK_TURNOVER_ERROR:%v", err)
		jsonRequest := helper.StructJson(req)
		logType := "ERROR_USER_TRANSACTION"
		var createLog model.BankTransactionLogCreateRequest
		createLog.Status = &statusLog
		createLog.JsonPayload = &jsonPayLoad
		createLog.JsonRequest = &jsonRequest
		createLog.LogType = &logType
		if _, err := s.serviceAccounting.CreateBankTransactionLog(createLog); err != nil {
			return nil, nil
		}
		return nil, err
	}

	// [limit credit] makold มีการดึงเครดิตกลับ และ เอา ยอดของ จำกัดถอนมา ถอนแทน
	fmt.Println("UserCreateWithdrawTransaction.CheckAllowWithdrawCreditCondition")
	currentAbleWithdrawCredit, err := s.CheckAllowWithdrawCreditCondition(req.UserId, req.Amount)
	if err != nil {
		fmt.Println("UserCreateWithdrawTransaction.CheckAllowWithdrawCreditCondition", err)
		return nil, err
	}
	if currentAbleWithdrawCredit > 0 {
		req.Amount = currentAbleWithdrawCredit
	}
	fmt.Println("UserCreateWithdrawTransaction.CheckAllowWithdrawCreditCondition", req.Amount)

	// [********] If Payment Gateway is available, use it
	payGateTransId, err := s.CheckPaymentGatewayAvailable(req)
	if err != nil {
		log.Println("UserCreateWithdrawTransaction.PaymentGatewayNotAvailable", err)
		// If no Transaction Created (no payGateTransId), Flow will continue to create transaction
	} else {
		// onSuccess
		return nil, nil
	}
	if payGateTransId != nil {
		// onPaygateError with payGateTransId ==> get bank transaction that waiting for action.
		bankTrans, err := s.repoBanking.GetBankTransactionById(*payGateTransId)
		if err != nil {
			log.Println("UserCreateWithdrawTransaction.GetBankTransactionById", err)
			return nil, internalServerError(err)
		}
		// Do no create transaction again
		var externalNoti model.NotifyExternalNotificationRequest
		externalNoti.TypeNotify = model.IsWithdrawalAwaitingTransfer
		externalNoti.TransId = payGateTransId
		externalNoti.Amount = req.Amount
		externalNoti.MemberCode = bankTrans.MemberCode
		externalNoti.UserCredit = bankTrans.AfterAmount
		externalNoti.ConfirmedByAdminId = 0
		endProcess := time.Now()
		elapsed := endProcess.Sub(startProcess)
		elapsedSeconds := elapsed.Seconds()
		timeElapsed := fmt.Sprintf("%.2f", elapsedSeconds)
		externalNoti.TimerCounter = timeElapsed
		externalNoti.WebScoket.UserID = bankTrans.UserId
		externalNoti.WebScoket.Amount = req.Amount
		externalNoti.WebScoket.MemberCode = bankTrans.MemberCode
		externalNoti.WebScoket.AlertType = "WITHDRAW"
		if err := s.serviceNoti.ExternalNotification(externalNoti); err != nil {
			log.Println("FailedNotify", err)
		}
		return nil, nil
	}

	// [Pull turn over]
	// turnOver, err := s.serviceTurnover.CheckUserTurnover(req.UserId)
	// if err != nil {
	// 	log.Println("UserCreateWithdrawTransaction.CheckUserTurnover", err)
	// 	return nil, err
	// }
	// if turnOver.IsHasTurnover {
	// 	return nil, badRequest("ไม่สามารถถอนได้ กรุณาตรวจสอบยอดเทิร์นโอเวอร์ของท่าน")
	// }
	// // [Pull user]
	user, err := s.repoBanking.GetUserBankDetailById(req.UserId)
	if err != nil {
		log.Println("UserCreateWithdrawTransaction.GetUserBankDetailById", err)
		return nil, internalServerError(err)
	}

	// [Check User Credit]
	// [CURRENT_USER_CREDIT] get latest credit from Agent
	userCreditInfo, err := s.serviceUser.GetUser(user.Id)
	if err != nil {
		log.Println("UserCreateWithdrawTransaction.GetUser", err)
		return nil, badRequest("ยอดเงินในเกมไม่พอ")
	}
	if userCreditInfo.Credit < req.Amount {
		return nil, badRequest("ยอดเงินในเกมไม่พอ")
	}

	// [Web config minimum withdraw]
	getWebConfig, err := s.repoBanking.GetWebConfiguration()
	if err != nil {
		statusLog := "ERROR"
		jsonPayLoad := fmt.Sprintf("USER_TRY_TO_CREATE_WITHDRAW_TRANSACTION_BUT_GET_WEB_CONFIG_ERROR:%v", err)
		jsonRequest := helper.StructJson(req)
		logType := "ERROR_USER_TRANSACTION"
		var createLog model.BankTransactionLogCreateRequest
		createLog.Status = &statusLog
		createLog.JsonPayload = &jsonPayLoad
		createLog.JsonRequest = &jsonRequest
		createLog.LogType = &logType
		if _, err := s.serviceAccounting.CreateBankTransactionLog(createLog); err != nil {
			return nil, nil
		}
		log.Println("UserCreateWithdrawTransaction.GetWebConfiguration", err)
		return nil, internalServerError(err)
	}
	var checkRequest model.CheckDuplicateWithdrawRequest
	checkRequest.UserId = req.UserId
	checkRequest.Amount = req.Amount
	if getWebConfig.MinimumWithdraw > req.Amount {
		statusLog := "ERROR"
		jsonPayLoad := fmt.Sprintf("USER_CREATE_WITHDRAW_BUT_AMOUNT_LESS_THAN_MINIMUM:%v", getWebConfig.MinimumWithdraw)
		jsonRequest := helper.StructJson(checkRequest)
		logType := "ERROR_USER_TRANSACTION"
		var createLog model.BankTransactionLogCreateRequest
		createLog.Status = &statusLog
		createLog.JsonPayload = &jsonPayLoad
		createLog.JsonRequest = &jsonRequest
		createLog.LogType = &logType
		if _, err := s.serviceAccounting.CreateBankTransactionLog(createLog); err != nil {
			return nil, nil
		}
		return nil, badRequest("WITHDRAW_AMOUNT_LESS_THAN_MINIMUM")
	}

	// duplicate transaction [********] Dunk ไม่ให้ทำรายการยอดเงินเดิมซ้ำ
	// duplicate transaction [********] Dunk เปลี่ยน เป็น flow ที่ไม่ให้ทำรายการซ้ำได้ถ้า มีรายการอยู่แล้ว
	// [Check Duplicate Withdraw]
	duplicateWithdrawTrans, _ := s.repoBanking.CheckUserDuplicateWithdrawProcessing(req.UserId)
	if duplicateWithdrawTrans != nil && duplicateWithdrawTrans.Id > 0 {
		statusLog := "SUCCESS"
		jsonPayLoad := fmt.Sprintf("USER_TRY_TO_CREATE_DUPLICATE_WITHDRAW_TRANSACTION:%v", duplicateWithdrawTrans.Id)
		jsonRequest := helper.StructJson(checkRequest)
		logType := "DUPLICATE_USER_TRANSACTION"
		var createLog model.BankTransactionLogCreateRequest
		createLog.Status = &statusLog
		createLog.JsonPayload = &jsonPayLoad
		createLog.JsonRequest = &jsonRequest
		createLog.LogType = &logType
		if _, err := s.serviceAccounting.CreateBankTransactionLog(createLog); err != nil {
			return nil, nil
		}
		return nil, badRequest("DUPLICATE_USER_TRANSACTION")
	}

	// [Find Validated Bank Account]
	var bankAccount model.BankAccount
	var validatedBank *model.PriorityValidation
	// [********] To do Dunk Add True user can only withdraw with true bank account
	if user.BankId == model.BANK_ID_TRUE {
		// GetBankAccountOnlyTrueAccountForWithdraw() (*model.BankAccount, error)
		trueBankAccount, err := s.repoAccounting.GetBankAccountOnlyTrueAccountForWithdraw()
		if err != nil {
			log.Println("UserCreateWithdrawTransaction.GetBankAccountOnlyTrueAccountForWithdraw", err)
			// return nil, internalServerError(err)
		}
		if trueBankAccount != nil {
			if validatedBank == nil {
				validatedBank = &model.PriorityValidation{}
			}
			validatedBank.BankAccountId = trueBankAccount.Id
			logUpTrue := []interface{}{trueBankAccount, "validatedBank", validatedBank}
			validatedBank.ValidateLog = logUpTrue
		}
	} else {
		// [********] Disable Bank Check - Let user create
		validatedBank, err = s.serviceAccounting.BankAccountWithDrawPriorityValidation(req.Amount)
		if err != nil {
			log.Println("UserCreateWithdrawTransaction.BankAccountWithDrawPriorityValidation", err)
			// return nil, badRequest("ไม่สามารถถอนได้ กรุณาแจ้ง Admin")
		}
	}
	// [Check Validated Bank Account or Not]
	if validatedBank != nil {

		if validatedBank.BankAccountId == 0 {
			newBankAccount, err := s.repoAccounting.GetBankAccountForWithdraw()
			if err != nil {
				log.Println("UserCreateWithdrawTransaction.GetBankAccountForWithdraw", err)
				// return nil, internalServerError(err)
			}
			bankAccount = *newBankAccount
			statusLog := "SUCCESS"
			jsonPayLoad := helper.StructJson(validatedBank.ValidateLog)
			jsonRequest := fmt.Sprintf("LAST USED : %v VALIDATE :%v", helper.StructJson(bankAccount), helper.StructJson(checkRequest))
			logType := "VALIDATE_USER_WITHDRAW"
			var createLog model.BankTransactionLogCreateRequest
			createLog.Status = &statusLog
			createLog.JsonPayload = &jsonPayLoad
			createLog.JsonRequest = &jsonRequest
			createLog.LogType = &logType
			if _, err := s.serviceAccounting.CreateBankTransactionLog(createLog); err != nil {
				return nil, nil
			}
		} else {
			newBankAccount, err := s.repoAccounting.GetBankAccountById(validatedBank.BankAccountId)
			if err != nil {
				log.Println("UserCreateWithdrawTransaction.GetBankAccountById", err)
				// return nil, internalServerError(err)
			}
			bankAccount = *newBankAccount
		}

		if bankAccount.ConnectionStatusId == model.CONNECTION_CONNECTED {
			validatedBank.Status = model.AUTO_WITHDRAW
		} else {
			validatedBank.Status = model.CLOSE_AUTO_WITHDRAW
		}
	} else {
		validatedBank = &model.PriorityValidation{}
		validatedBank.Status = model.CLOSE_AUTO_WITHDRAW
		bankAccount.AccountName = "ไม่พบบัญชีธนาคาร"
		bankAccount.AccountNumber = "ไม่พบบัญชีธนาคาร"
	}
	// [********] true not allow decimal with scb
	if user.BankId == model.BANK_ID_TRUE && bankAccount.BankId == model.BANK_ID_SCB {
		if req.Amount != float64(int(req.Amount)) {
			return nil, badRequest("WITHDRAW_AMOUNT_NOT_ALLOW_DECIMAL")
		}
	}

	// [Check Confirm Key]
	confirmTimeNow := time.Now()
	confirmKey := fmt.Sprintf("%v%v", confirmTimeNow.Format("************"), user.Id)
	checkFromConfirmKey, _ := s.repoBanking.CheckWithdrawConfirmConfirmKey(confirmKey)
	if checkFromConfirmKey != nil && checkFromConfirmKey.Id > 0 {
		log.Println("UserCreateWithdrawTransaction.CheckWithdrawConfirmConfirmKey", err)
		return nil, badRequest("WITHDRAW_ALREADY_IN_PROCESSING_WAIT_FOR_CONFIRM")
	}
	var checkWithdrawConfirm model.CheckWithdrawConfirmBody
	checkWithdrawConfirm.UserId = user.Id
	checkWithdrawConfirm.CreatedAt = confirmTimeNow
	check, _ := s.repoBanking.CheckWithdrawConfirmCurrentTime(checkWithdrawConfirm)
	if check != nil && check.Id > 0 {
		log.Println("UserCreateWithdrawTransaction.CheckWithdrawConfirmCurrentTime", err)
		return nil, badRequest("WITHDRAW_ALREADY_IN_PROCESSING_WAIT_FOR_CONFIRM")
	}
	var createConfirmWithdraw model.CreateWithdrawConfirmBody
	createConfirmWithdraw.UserId = user.Id
	createConfirmWithdraw.ConfirmKey = confirmKey
	insertConfirmWithdraw, err := s.repoBanking.CreateWithdrawConfirm(createConfirmWithdraw)
	if err != nil {
		log.Println("UserCreateWithdrawTransaction.CreateWithdrawConfirm", err)
		return nil, badRequest("WITHDRAW_ALREADY_IN_PROCESSING")
	}
	if insertConfirmWithdraw == 0 {
		log.Println("UserCreateWithdrawTransaction.CreateWithdrawConfirm", err)
		return nil, badRequest("WITHDRAW_ALREADY_IN_PROCESSING")
	}

	// [create transaction]
	timeNow := time.Now()
	var createBankTransaction model.BankTransactionCreateBody
	createBankTransaction.MemberCode = user.MemberCode
	createBankTransaction.UserId = user.Id
	createBankTransaction.TransactionTypeId = model.TRANSACTION_TYPE_WITHDRAW
	createBankTransaction.FromAccountId = &bankAccount.Id
	createBankTransaction.FromBankId = &bankAccount.BankId
	createBankTransaction.FromAccountName = &bankAccount.AccountName
	createBankTransaction.FromAccountNumber = &bankAccount.AccountNumber
	createBankTransaction.ToBankId = &user.BankId
	createBankTransaction.ToAccountName = &user.Fullname
	createBankTransaction.ToAccountNumber = &user.BankAccount
	createBankTransaction.CreatedByAdminId = 0
	createBankTransaction.TransactionStatusId = model.TRANS_STATUS_WITHDRAW_PENDING
	createBankTransaction.CreditAmount = req.Amount
	createBankTransaction.CreditBack = 0
	createBankTransaction.OverAmount = 0
	createBankTransaction.BonusAmount = 0
	createBankTransaction.BeforeAmount = 0
	createBankTransaction.AfterAmount = 0
	createBankTransaction.TransferAt = &timeNow
	transId, err := s.repoBanking.InsertBankTransaction(createBankTransaction)
	if err != nil {
		log.Println("UserCreateWithdrawTransaction.InsertBankTransaction", err)
		return nil, internalServerError(err)
	}
	if err := s.repoBanking.UpdateWithdrawConfirmTransactionId(insertConfirmWithdraw, *transId); err != nil {
		log.Println("UserCreateWithdrawTransaction.UpdateWithdrawConfirmTransactionId", err)
	}
	// [promotion]
	var promotionWebUserId int64
	GetUserPromotion, _ := s.promotionWebService.GetWithdrawCurrentProcessingUserPromotion(user.Id)
	if GetUserPromotion != nil {
		promotionWebUserId = GetUserPromotion.Id

		errUpdate := s.repoBanking.UpdatePromotionToBankTransaction(*transId, promotionWebUserId)
		if errUpdate != nil {
			log.Println("UserCreateWithdrawTransaction.UpdatePromotionToBankTransaction", err)
		}
	}
	// [Decrease User Credit]
	var userCreditReq model.UserTransactionCreateRequest
	userCreditReq.UserId = user.Id
	userCreditReq.TypeId = model.CREDIT_TYPE_WITHDRAW
	userCreditReq.AccountId = &bankAccount.Id
	userCreditReq.PromotionId = &promotionWebUserId
	userCreditReq.Detail = fmt.Sprintf("%s:%s => %s:%s", bankAccount.BankName, bankAccount.AccountNumber, user.BankName, user.Fullname)
	userCreditReq.Amount = req.Amount
	userCreditReq.StartWorkAt = req.ActionAt // เริ่มนับตอนกดขอถอน
	userCreditReq.RefId = transId
	isShow := false
	userCreditReq.IsShow = &isShow
	agentResp, err := s.repoAccounting.DecreaseUserCredit(userCreditReq)
	if err != nil {
		// [********] Confirm from P.layer if error from agent will be in failed
		log.Println("CreateWithdrawRecord.DecreaseUserCredit", err)
		var updateAgentError model.UpdateConfirmAutoWithdrawBody
		statusError := model.TRANS_STATUS_WITHDRAW_REJECTED
		confirmedByAuto := int64(0)
		updateAgentError.TransactionStatusId = &statusError
		updateAgentError.ConfirmedByAdminId = &confirmedByAuto
		if err := s.repoBanking.UpdateAdminAndTransactionStatus(*transId, updateAgentError); err != nil {
			return nil, internalServerError(err)
		}
		return nil, internalServerError(err)
	}
	if err := s.repoAccounting.UpdateWithdrawTransactionStatusFromAgent(*transId, *agentResp); err != nil {
		log.Println("UserCreateWithdrawTransaction.UpdateWithdrawTransactionStatusFromAgent", err)
		return nil, internalServerError(err)
	}

	// [Check User Setting Withdraw]
	userSettingWithdraw, err := s.UserSettingWithdraw(user.Id, req.Amount)
	if err != nil {
		log.Println("UserCreateWithdrawTransaction.UserSettingWithdraw", err)
		logtype := "ERROR_USER_TRANSACTION"
		logstatus := "ERROR"
		logpayLoad := fmt.Sprintf("ERROR_USER_TRANSACTION:%v", err)
		logrequest := helper.StructJson(req)
		var createLog model.BankTransactionLogCreateRequest
		createLog.Status = &logstatus
		createLog.JsonPayload = &logpayLoad
		createLog.JsonRequest = &logrequest
		createLog.LogType = &logtype
		if _, err := s.serviceAccounting.CreateBankTransactionLog(createLog); err != nil {
			return nil, nil
		}
	}
	// [********]fastbank credit check balance to continue unuse func or not auto
	getWebLocal, _ := s.repoBanking.GetLocalWebInfo()
	if getWebLocal != nil {
		if getWebLocal.FastbankCreditBalance <= -1000 {
			log.Println("UserCreateWithdrawTransaction.WEB_OUT_OF_CREDIT")
			validatedBank.Status = model.CLOSE_AUTO_WITHDRAW
		}
	}
	//[P.mink confirm ********] not auto if not found after validate
	// [********] เปลี่ยนจาก bank account ไปเป็น config web
	getConfigWeb, _ := s.repoBanking.GetConfiguration()
	if getConfigWeb != nil {
		// [********] อาจจะไม่ต้องมีเพราะ สุดท้ายก็ปล่อยเข้ามาอยู่ดี ให้ ไปสร้าง รายการ
		// if req.Amount < getConfigWeb.WithdrawMaximum {
		// 	validatedBank.Status = model.CLOSE_AUTO_WITHDRAW
		// }
		// check auto withdraw again after validate
		if req.Amount > getConfigWeb.WithdrawMaximumAuto {
			validatedBank.Status = model.CLOSE_AUTO_WITHDRAW
		}
	} else {
		validatedBank.Status = model.CLOSE_AUTO_WITHDRAW
	}

	// [Check Auto Withdraw only if pass validate]
	var statusId int64
	var externalNoti model.NotifyExternalNotificationRequest
	approvedTime := time.Now().UTC()
	if validatedBank.Status == model.AUTO_WITHDRAW && bankAccount.AutoWithdrawTypeId == model.CONFIG_WEB_AUTO_WITHDRAW && userSettingWithdraw == model.USER_WITHDRAW_SETTING_PASS_AUTO {
		if agentResp.AgentSuccess {
			var transferBody model.WithdrawTransferFastBankBody
			if req.Amount == float64(int(req.Amount)) {
				transferBody.Amount = strconv.FormatFloat(req.Amount, 'f', 2, 64)
			} else {
				transferBody.Amount = strconv.FormatFloat(req.Amount, 'f', -1, 64)
			}
			transferBody.AccountFrom = bankAccount.AccountNumber
			transferBody.AccountTo = user.BankAccount
			transferBody.BankCode = user.BankCode
			transferBody.Pin = bankAccount.PinCode

			// [fast bank withdraw]
			fastbank, err := s.repoBanking.WithdrawWithFastBank(transferBody)
			statusLog := "SUCCESS"
			jsonPayLoad := fmt.Sprintf("WITHDRAW_TO_FROM_FASTBANK: %s FASTBANK RES :%s, ERROR :%s", helper.StructJson(transferBody), helper.StructJson(fastbank), err)
			jsonRequest := fmt.Sprintf(" BANK_TRANS_ID %v ", *transId)
			logType := "UserCreateWithdrawTransaction"
			var createLog model.BankTransactionLogCreateRequest
			createLog.Status = &statusLog
			createLog.JsonPayload = &jsonPayLoad
			createLog.JsonRequest = &jsonRequest
			createLog.LogType = &logType
			if _, err := s.serviceAccounting.CreateBankTransactionLog(createLog); err != nil {
				log.Println("UserCreateWithdrawTransaction.CreateBankTransactionLog", err)
			}
			if err == nil && fastbank.Status.Code != 500 {
				statusId = model.TRANS_STATUS_WITHDRAW_SUCCESS
				externalNoti.TypeNotify = model.IsWithdrawalCreditSuccess
				externalNoti.TransferDateTime = time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02 15:04:05")
				externalNoti.ActionTime = time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02 15:04:05")
				// [create transaction action success]
				var createConfirm model.CreateSuccessTransferWithdrawRequest
				createConfirm.TransactionId = *transId
				createConfirm.ConfirmedAt = time.Now()
				var setIdAuto int64 = 0
				createConfirm.ConfirmedByAdminId = &setIdAuto
				if _, err := s.CreateSuccessTransferWithdraw(createConfirm); err != nil {
					log.Println("UserCreateWithdrawTransaction.CreateSuccessTransferWithdraw", err)
					return nil, nil
				}

				// [Show Withdraw USER_TRANSACTION]
				var showUserTrans model.UserTransactionShowUpdate
				showUserTrans.TransactionId = *transId
				showUserTrans.ConfirmedAt = &createConfirm.ConfirmedAt
				showUserTrans.ConfirmAdminId = createConfirm.ConfirmedByAdminId
				showUserTrans.TransferAt = &approvedTime
				if err := s.repoAccounting.ShowUserTransaction(showUserTrans); err != nil {
					log.Println("UserCreateWithdrawTransaction.ShowUserTransaction", err)
					return nil, nil
				}

				// Create detail
				var createBankTransactionExternalDetail model.BankTransactionExternalDetailCreateRequest
				createBankTransactionExternalDetail.BankTransactionId = *transId
				createBankTransactionExternalDetail.Detail = "SUCCESS"
				createBankTransactionExternalDetail.ErrorCode = 0
				if _, err := s.repoBanking.CreateBankTransactionExternalDetail(createBankTransactionExternalDetail); err != nil {
					return nil, nil
				}

				// [DECREASE FASTBANK CREDIT]
				if err := s.repoAccounting.DecreaseFastbankCredit(1); err != nil {
					log.Println("UserCreateWithdrawTransaction.DECREASE_FASTBANK_CREDIT_ERROR", err)
				}
			} else {
				// [FASTBANK_FAILED]
				var responseFastbank model.FastbankWithdrawTransactionResponse
				if fastbank != nil {
					responseFastbank.Status = int64(fastbank.Status.Code)
					if fastbank.Status.Description != "" {
						responseFastbank.Message = fastbank.Status.Description
					} else {
						responseFastbank.Message = fmt.Sprintf("Error: %s", err)
					}
				} else {
					responseFastbank.Status = 0
					responseFastbank.Message = fmt.Sprintf("Error: %s", err)
				}

				statusId = model.TRANS_STATUS_WITHDRAW_UNSURE
				externalNoti.TypeNotify = model.IsWithdrawalCreditFailed

				// Create detail
				var createBankTransactionExternalDetail model.BankTransactionExternalDetailCreateRequest
				createBankTransactionExternalDetail.BankTransactionId = *transId
				createBankTransactionExternalDetail.Detail = responseFastbank.Message
				createBankTransactionExternalDetail.ErrorCode = responseFastbank.Status
				if _, err := s.repoBanking.CreateBankTransactionExternalDetail(createBankTransactionExternalDetail); err != nil {
					return nil, nil
				}

			}
		}

	} else if validatedBank.Status == model.OUT_OF_CONFIG_AMOUNT {
		statusId = model.TRANS_STATUS_WITHDRAW_OVER_MAX
		externalNoti.TypeNotify = model.IsWithdrawalAwaitingTransfer
	} else if bankAccount.AutoWithdrawTypeId == model.CONFIG_WEB_MANUAL_WITHDRAW || userSettingWithdraw == model.USER_WITHDRAW_SETTING_UNPASS || validatedBank.Status == model.CLOSE_AUTO_WITHDRAW {
		statusId = model.TRANS_STATUS_WITHDRAW_APPROVED
		externalNoti.TypeNotify = model.IsWithdrawalAwaitingTransfer
	} else {
		statusId = model.TRANS_STATUS_WITHDRAW_OVER_BUDGET
		externalNoti.TypeNotify = model.IsWithdrawalAwaitingTransfer
	}

	//[update transaction status]
	var updateApprovedBy model.UpdateConfirmAutoWithdrawBody
	updateApprovedBy.TransactionStatusId = &statusId
	updateApprovedBy.ConfirmedAt = approvedTime
	var setIdAuto int64 = 0
	updateApprovedBy.ConfirmedByAdminId = &setIdAuto
	if statusId == model.TRANS_STATUS_WITHDRAW_SUCCESS {
		updateApprovedBy.TransferAt = &approvedTime
	}
	err = s.repoBanking.UpdateAdminAndTransactionStatus(*transId, updateApprovedBy)
	if err != nil {
		log.Println("UserCreateWithdrawTransaction.UpdateAdminAndTransactionStatus", err)
		return nil, internalServerError(err)
	}

	// AddTo BankPendingRecord -> รายการถอน
	dashboardRepo := repository.NewDashboardRepository(s.SharedDb)
	if err := AddBankPendingRecordFromWithdraw(dashboardRepo, *transId); err != nil {
		log.Println("UserCreateWithdrawTransaction.AddBankPendingRecordFromWithdraw", err)
	}

	// [ Notify]
	endProcess := time.Now()
	elapsed := endProcess.Sub(startProcess)
	elapsedSeconds := elapsed.Seconds()
	timeElapsed := fmt.Sprintf("%.2f", elapsedSeconds)
	externalNoti.TransId = transId
	externalNoti.Amount = req.Amount
	externalNoti.MemberCode = user.MemberCode
	externalNoti.UserCredit = agentResp.AgentAfterAmount
	externalNoti.ConfirmedByAdminId = 0
	externalNoti.TimerCounter = timeElapsed
	externalNoti.WebScoket.UserID = user.Id
	externalNoti.WebScoket.Amount = req.Amount
	externalNoti.WebScoket.MemberCode = user.MemberCode
	externalNoti.WebScoket.AlertType = "WITHDRAW"
	if err := s.serviceNoti.ExternalNotification(externalNoti); err != nil {
		log.Println("FailedNotify", err)
	}

	return nil, nil
}

func (s *bankingService) TurnOverWithdrawChecker(userId int64, amount float64) error {

	turnOverStatementNotClear := s.repoBanking.GetTurnOverStatementNotClear(userId)
	// var response []model.GetTurnOverStatementNotClearResponse
	mapUniqueResp := make(map[string]model.GetTurnOverStatementNotClearResponse)
	if len(turnOverStatementNotClear) > 0 {

		for _, v := range turnOverStatementNotClear {
			if v.Name == model.TURNOVER_CATE_FIRST_DEPOSIT || v.PromotionName == model.TURNOVER_CATE_FIRST_DEPOSIT {
				v.Name = "TURNOVER_CATE_FIRST_DEPOSIT"
			} else if v.Name == model.TURNOVER_CATE_BONUS || v.PromotionName == model.TURNOVER_CATE_BONUS {
				v.Name = "TURNOVER_CATE_BONUS"
			} else if v.Name == model.TURNOVER_CATE_ACTIVITY || v.PromotionName == model.TURNOVER_CATE_ACTIVITY {
				v.Name = "TURNOVER_CATE_ACTIVITY"
			} else if v.Name == model.TURNOVER_CATE_RETURN_LOSS || v.PromotionName == model.TURNOVER_CATE_RETURN_LOSS {
				v.Name = "TURNOVER_CATE_RETURN_LOSS"
			} else if v.Name == model.TURNOVER_CATE_RETURN_TURN || v.PromotionName == model.TURNOVER_CATE_RETURN_TURN {
				v.Name = "TURNOVER_CATE_RETURN_TURN"
			} else if v.Name == model.TURNOVER_CATE_PROMOTION || v.PromotionName == model.TURNOVER_CATE_PROMOTION {
				v.Name = "TURNOVER_CATE_PROMOTION"
			} else if v.Name == model.TURNOVER_CATE_LINK_AFFILIATE || v.PromotionName == model.TURNOVER_CATE_LINK_AFFILIATE {
				v.Name = "TURNOVER_CATE_LINK_AFFILIATE"
			} else if v.Name == model.TURNOVER_CATE_AFF_TYPE_NEW_REGISTER || v.PromotionName == model.TURNOVER_CATE_AFF_TYPE_NEW_REGISTER {
				v.Name = "TURNOVER_CATE_AFF_TYPE_NEW_REGISTER"
			} else if v.Name == model.TURNOVER_CATE_AFF_TYPE_FIRST_DEPOSIT || v.PromotionName == model.TURNOVER_CATE_AFF_TYPE_FIRST_DEPOSIT {
				v.Name = "TURNOVER_CATE_AFF_TYPE_FIRST_DEPOSIT"
			} else if v.Name == model.TURNOVER_CATE_AFF_TYPE_PLAY_COMMISSION || v.PromotionName == model.TURNOVER_CATE_AFF_TYPE_PLAY_COMMISSION {
				v.Name = "TURNOVER_CATE_AFF_TYPE_PLAY_COMMISSION"
			} else if v.Name == model.TURNOVER_CATE_ACTIVITY_DAILY || v.PromotionName == model.TURNOVER_CATE_ACTIVITY_DAILY {
				v.Name = "TURNOVER_CATE_ACTIVITY_DAILY"
			} else if v.Name == model.TURNOVER_CATE_SUCCESS_DEPOSIT || v.PromotionName == model.TURNOVER_CATE_SUCCESS_DEPOSIT {
				v.Name = "TURNOVER_CATE_SUCCESS_DEPOSIT"
			} else if v.Name == model.TURNOVER_CATE_ACTIVITY_DAILY_V2 || v.PromotionName == model.TURNOVER_CATE_ACTIVITY_DAILY_V2 {
				v.Name = "TURNOVER_CATE_ACTIVITY_DAILY_V2"
			}
			// to do กิจกรรมรายวัน
			// response = append(response, model.GetTurnOverStatementNotClearResponse{
			// 	Id:            v.Id,
			// 	Name:          v.Name,
			// 	PromotionName: v.PromotionName,
			// })
			mapUniqueResp[v.Name] = model.GetTurnOverStatementNotClearResponse{
				Id:            v.Id,
				Name:          v.Name,
				PromotionName: v.PromotionName,
			}
		}
	}
	checkToNextCondition := true
	// ก่อนแก้ อ่านก่อน !!!!!! [confirm แล้ว 25/04/2024]
	// 2 flow นี้จะต้อง เคลียให้เสร็จก่อนถึงจะไปตรวจสอบเงื่อนไขอื่นๆ เนื่องด้วย มีเวลาที่กด รับ ถ้า รับ bouns ติด turn 5 เมื่อวานมียอดเ turn เล่น 50 พอมากดรับ promotion ติด20 มันจะต้องไม่ผ่าน
	// [เหตุผลที่ต้องทำแบบนี้ เพราะ เรื่อง วันที่ แต่ละเงื่อนไขการรับ + เพราะ โปรโมชั่น set ว่า ยอดเงินหลังถอนต้องเหลือเท่าไหร่]

	// fmt.Println("CheckPromotionWithdraw")

	// [Check Promotion Withdraw]
	// promotionWithdrawAble, _ := s.promotionWebService.CheckPromotionWithdraw(userId, amount)
	// if promotionWithdrawAble != "" {
	// 	if promotionWithdrawAble == model.NOT_PASS_TO_WITHDRAW {
	// 		checkToNextCondition = false
	// 		// return badRequest("PROMOTION_NOT_PASS_CONDITION_WITHDRAW")
	// 		log.Println("PROMOTION_NOT_PASS_CONDITION_WITHDRAW USERID:", userId)
	// 		if len(response) == 0 {
	// 			// ติดเคสถอน
	// 			response = append(response, model.GetTurnOverStatementNotClearResponse{
	// 				Id:            0,
	// 				Name:          model.TURNOVER_CATE_PROMOTION,
	// 				PromotionName: "TURNOVER_CATE_PROMOTION",
	// 			})
	// 		}
	// 		return errorWithData("TURNOVER_NOT_PASS_CONDITION_WITHDRAW", response)
	// 	}
	// }

	if checkToNextCondition {
		// fmt.Println("CheckCouponTurnOverWithdraw")
		// [Check Coupon Turn Over Withdraw]
		couponTurnOverWithdrawAble, _, _ := s.couponCashService.CheckCouponTurnOverWithdraw(userId, amount)
		if couponTurnOverWithdrawAble != "" {
			if couponTurnOverWithdrawAble == model.COUPON_TURNOVER_FAIL {
				checkToNextCondition = false
				// return errorWithData("COUPON_NOT_PASS_CONDITION_WITHDRAW", amount)
				log.Println("COUPON_NOT_PASS_CONDITION_WITHDRAW USERID:", userId)
				var uniResp []model.GetTurnOverStatementNotClearResponse
				for _, v := range mapUniqueResp {
					uniResp = append(uniResp, v)
				}
				return errorWithData("TURNOVER_NOT_PASS_CONDITION_WITHDRAW", uniResp)
			}
		}
	}

	if checkToNextCondition {
		// fmt.Println("CheckOtherExistTurnOver")
		// [clear turn over อื่นๆ ]
		otherTurnOverWithdrawAble, _, _ := s.CheckOtherExistTurnOver(userId)
		if otherTurnOverWithdrawAble == "NOT_PASS_OHTER_TURN_OVER" {
			// return errorWithData("TURNOVER_NOT_PASS_CONDITION_WITHDRAW", amount)
			log.Println("TURNOVER_NOT_PASS_CONDITION_WITHDRAW USERID:", userId)
			var uniResp []model.GetTurnOverStatementNotClearResponse
			for _, v := range mapUniqueResp {
				uniResp = append(uniResp, v)
			}
			return errorWithData("TURNOVER_NOT_PASS_CONDITION_WITHDRAW", uniResp)
		}
	}

	return nil
}

func CreateSuccessTransferWithdraw(repo repository.BankingRepository, req model.CreateSuccessTransferWithdrawRequest) (*int64, error) {

	record, err := repo.GetTransactionWithdrawById(req.TransactionId)
	if err != nil {
		return nil, internalServerError(err)
	}
	jsonBefore, _ := json.Marshal(record)
	var createBody model.CreateBankTransactionActionBody
	createBody.ActionKey = fmt.Sprintf("CFW_TRASFER#%d", req.TransactionId)
	createBody.TransactionId = req.TransactionId
	createBody.UserId = record.UserId
	createBody.TransactionTypeId = record.TransactionTypeId
	createBody.FromAccountId = record.FromAccountId
	createBody.ToAccountId = record.ToAccountId
	createBody.JsonBefore = string(jsonBefore)
	createBody.TransferAt = &record.TransferAt
	createBody.CreditAmount = record.CreditAmount
	createBody.ConfirmedAt = req.ConfirmedAt
	createBody.ConfirmedByAdminId = req.ConfirmedByAdminId
	// BOF : transaction
	if _, err := repo.CreateTransactionAction(createBody); err != nil {
		return nil, internalServerError(err)
	}
	return nil, nil
}

func (s *bankingService) CreateSuccessTransferWithdraw(req model.CreateSuccessTransferWithdrawRequest) (*int64, error) {

	record, err := s.repoBanking.GetTransactionWithdrawById(req.TransactionId)
	if err != nil {
		return nil, internalServerError(err)
	}
	jsonBefore, _ := json.Marshal(record)
	var createBody model.CreateBankTransactionActionBody
	createBody.ActionKey = fmt.Sprintf("CFW_TRASFER#%d", req.TransactionId)
	createBody.TransactionId = req.TransactionId
	createBody.UserId = record.UserId
	createBody.TransactionTypeId = record.TransactionTypeId
	createBody.FromAccountId = record.FromAccountId
	createBody.ToAccountId = record.ToAccountId
	createBody.JsonBefore = string(jsonBefore)
	createBody.TransferAt = &record.TransferAt
	createBody.CreditAmount = record.CreditAmount
	createBody.ConfirmedAt = req.ConfirmedAt
	createBody.ConfirmedByAdminId = req.ConfirmedByAdminId
	// BOF : transaction
	if _, err := s.repoBanking.CreateTransactionAction(createBody); err != nil {
		return nil, internalServerError(err)
	}
	return nil, nil
}

func (s *bankingService) CreateAutoWithdraw(req model.CreateAutoWithdrawRequest) (*model.FastbankWithdrawTransactionResponse, error) {

	startProcess := time.Now()
	// GetTransactionByTransId(transactionId int64) (model.BankTransaction, error)

	// [********]fastbank credit check balance to continue unuse func or not auto
	getWebLocal, _ := s.repoBanking.GetLocalWebInfo()
	if getWebLocal != nil {
		if getWebLocal.FastbankCreditBalance <= -1000 {
			log.Println("CreateAutoWithdraw.WEB_OUT_OF_CREDIT")
			return nil, badRequest("ยอดเงินเว็บหมด อายุ")
		}
	}

	admin, err := s.repoBanking.GetAdminById(*req.ConfirmedByAdminId)
	if err != nil {
		log.Println("CreateAutoWithdraw.GetAdminById.ERROR=", err)
		return nil, internalServerError(err)
	}

	// var transaction model.BankTransaction
	transaction, err := s.repoBanking.GetTransactionWithdrawOverMaxById(req.TransactionId)
	if err != nil {
		return nil, badRequest("ไม่ตรงกับเงือนไข")
	}
	if transaction.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_CANCELED {
		return nil, badRequest("ไม่สามารถทำรายการได้ เนื่องจากถูกยกเลิกไปแล้ว")
	}
	if transaction.Id == 0 {
		return nil, badRequest("ไม่พบรายการที่ต้องการ")
	}

	user, err := s.repoBanking.GetUserBankDetailById(transaction.UserId)
	if err != nil {
		return nil, internalServerError(err)
	}

	// GetBankAccountById(id int64) (*model.BankAccount, error)
	bankAccount, err := s.repoBanking.GetBankAccountById(transaction.FromAccountId)
	if err != nil {
		return nil, internalServerError(err)
	}

	// [race condition withdraw]
	actionAt := time.Now().UTC().Format("************")
	var createBody model.RaceActionCreateBody
	createBody.Name = "AUTO_WITHDRAW"
	createBody.JsonRequest = fmt.Sprintf("req transactionId : %d", req.TransactionId)
	createBody.Status = "PENDING"
	createBody.ActionKey = fmt.Sprintf("WITHDRAW_T%s_U%d_CD%f", actionAt, user.Id, transaction.CreditAmount)
	createBody.UnlockAt = time.Now().UTC().Add(time.Second * 1)
	if _, err := s.repoBanking.GetRaceActionIdByActionKey(createBody.ActionKey); err != nil {
		log.Println("CreateBankStatementFromWebhookAndAuto.GetRaceActionIdByActionKey", err)
		if err != gorm.ErrRecordNotFound {
			return nil, internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
		}
	} else {
		return nil, internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
	}

	// create race condition
	actionId, err := s.repoBanking.CreateRaceCondition(createBody)
	if err != nil {
		log.Println("CreateBankStatementFromWebhookAndAuto.CreateRaceCondition", err)
		return nil, internalServerError(errors.New("CANNOT_CREATE_ACTION"))
	}

	if actionId == 0 {
		return nil, badRequest("PLEASE_WAIT_ONE_MINUTE_YOU_JUST_CLICKED_A_MOMENT_AGO_PLEASE_CHECK_THE_STATEMENT_AGAIN")
	}

	var statusId int64
	var transferBody model.WithdrawTransferFastBankBody
	transferBody.AccountFrom = bankAccount.AccountNumber
	transferBody.AccountTo = user.BankAccount

	if transaction.CreditAmount == float64(int(transaction.CreditAmount)) {
		// If the amount has no decimal part, convert it to two decimal places
		transferBody.Amount = strconv.FormatFloat(transaction.CreditAmount, 'f', 2, 64)
	} else {
		// If the amount already has decimals, leave it unchanged
		transferBody.Amount = strconv.FormatFloat(transaction.CreditAmount, 'f', -1, 64)
	}

	var externalNoti model.NotifyExternalNotificationRequest
	transferBody.BankCode = user.BankCode
	transferBody.Pin = bankAccount.PinCode
	//7.4 โอนเงิน fastbank
	var responseFastbank model.FastbankWithdrawTransactionResponse
	responseFastbank.Id = req.TransactionId
	fastbank, err := s.repoBanking.WithdrawWithFastBank(transferBody)
	//7.5 เมื่อเสร็จสิ้นก็ update transaction status if fastbank success
	statusLog := "SUCCESS"
	jsonPayLoad := fmt.Sprintf("WITHDRAW_TO_FROM_FASTBANK: %s FASTBANK RES :%s, ERROR :%s", helper.StructJson(transferBody), helper.StructJson(fastbank), err)
	jsonRequest := fmt.Sprintf(" BANK_TRANS_ID %v ", transaction.Id)
	logType := "CreateAutoWithdraw"
	var createLog model.BankTransactionLogCreateRequest
	createLog.Status = &statusLog
	createLog.JsonPayload = &jsonPayLoad
	createLog.JsonRequest = &jsonRequest
	createLog.LogType = &logType

	approvedTime := time.Now().UTC()
	if _, err := s.serviceAccounting.CreateBankTransactionLog(createLog); err != nil {
		log.Println("CreateAutoWithdraw.CreateBankTransactionLog", err)
	}

	if err == nil && fastbank.Status.Code != 500 {
		responseFastbank.Message = "Created success"
		responseFastbank.Status = 200

		statusId = model.TRANS_STATUS_WITHDRAW_SUCCESS
		var createConfirm model.CreateSuccessTransferWithdrawRequest
		createConfirm.TransactionId = req.TransactionId
		createConfirm.ConfirmedAt = time.Now()
		createConfirm.ConfirmedByAdminId = req.ConfirmedByAdminId
		if _, err := s.CreateSuccessTransferWithdraw(createConfirm); err != nil {
			return nil, nil
		}

		// Show Withdraw USER_TRANSACTION
		var showUserTrans model.UserTransactionShowUpdate
		showUserTrans.TransactionId = createConfirm.TransactionId
		showUserTrans.ConfirmedAt = &createConfirm.ConfirmedAt
		showUserTrans.ConfirmAdminId = createConfirm.ConfirmedByAdminId
		showUserTrans.TransferAt = &approvedTime
		if err := s.repoAccounting.ShowUserTransaction(showUserTrans); err != nil {
			return nil, nil
		}

		// DECREASE FASTBANK CREDIT
		if err := s.repoAccounting.DecreaseFastbankCredit(1); err != nil {
			log.Println("CreateAutoWithdraw.DECREASE_FASTBANK_CREDIT_ERROR", err)
		}
		externalNoti.TypeNotify = model.IsWithdrawalCreditSuccess
		externalNoti.TransferDateTime = time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02 15:04:05")
		externalNoti.ActionTime = time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02 15:04:05")

		// Create detail
		var createBankTransactionExternalDetail model.BankTransactionExternalDetailCreateRequest
		createBankTransactionExternalDetail.BankTransactionId = req.TransactionId
		createBankTransactionExternalDetail.Detail = "SUCCESS"
		createBankTransactionExternalDetail.ErrorCode = 0
		if _, err := s.repoBanking.CreateBankTransactionExternalDetail(createBankTransactionExternalDetail); err != nil {
			return nil, nil
		}

	} else {
		if fastbank != nil {
			responseFastbank.Status = int64(fastbank.Status.Code)
			if fastbank.Status.Description != "" {
				responseFastbank.Message = fastbank.Status.Description
			} else {
				responseFastbank.Message = fmt.Sprintf("Error: %s", err)
			}
		} else {
			responseFastbank.Status = 0
			responseFastbank.Message = fmt.Sprintf("Error: %s", err)
		}
		statusId = model.TRANS_STATUS_WITHDRAW_UNSURE
		externalNoti.TypeNotify = model.IsWithdrawalCreditFailed

		// Create detail
		var createBankTransactionExternalDetail model.BankTransactionExternalDetailCreateRequest
		createBankTransactionExternalDetail.BankTransactionId = req.TransactionId
		createBankTransactionExternalDetail.Detail = responseFastbank.Message
		createBankTransactionExternalDetail.ErrorCode = responseFastbank.Status
		if _, err := s.repoBanking.CreateBankTransactionExternalDetail(createBankTransactionExternalDetail); err != nil {
			return nil, nil
		}
	}

	var updateApprovedBy model.UpdateConfirmAutoWithdrawBody
	updateApprovedBy.TransactionStatusId = &statusId
	updateApprovedBy.ConfirmedAt = time.Now()
	updateApprovedBy.ConfirmedByAdminId = req.ConfirmedByAdminId
	if statusId == model.TRANS_STATUS_WITHDRAW_SUCCESS {
		updateApprovedBy.TransferAt = &approvedTime
	}
	errUpdateAdminAnd := s.repoBanking.UpdateAdminAndTransactionStatus(req.TransactionId, updateApprovedBy)
	if errUpdateAdminAnd != nil {
		log.Println("CreateAutoWithdraw.UpdateAdminAndTransactionStatus", errUpdateAdminAnd)
	}

	var updateApprovedByAdmin model.UpdateConfirmedByAdminIdRequest
	updateApprovedByAdmin.ConfirmAdminId = req.ConfirmedByAdminId
	errUpdateConfirmed := s.repoBanking.UpdateConfirmedByAdminId(req.TransactionId, updateApprovedByAdmin)
	if errUpdateConfirmed != nil {
		log.Println("CreateAutoWithdraw.UpdateConfirmedByAdminId", errUpdateConfirmed)
	}

	end := time.Now()
	elapsed := end.Sub(startProcess)
	externalNoti.TransId = &req.TransactionId
	externalNoti.Amount = transaction.CreditAmount
	externalNoti.MemberCode = user.MemberCode
	externalNoti.UserCredit = transaction.AfterAmount
	externalNoti.ConfirmedByAdminId = *req.ConfirmedByAdminId
	externalNoti.TimerCounter = fmt.Sprintf("%.2f", elapsed.Seconds())
	if err := s.serviceNoti.ExternalNotification(externalNoti); err != nil {
		log.Println("FailedNotify", err)
	}

	// ===========================================================
	// RemoveFrom BankPendingRecord -> รายการถอน อนุมัติรายการถอน แบบคลิกยืนยันและโอนเงินออโต้
	dashboardRepo := repository.NewDashboardRepository(s.SharedDb)
	if err := ConfirmBankPendingRecordFromAny(dashboardRepo, transaction.TransactionTypeId, transaction.Id, startProcess, admin.Fullname); err != nil {
		log.Println("CreateTransWithdrawWithManualAccount.ConfirmBankPendingRecordFromAny.ERROR=", err)
	}

	return &responseFastbank, nil
}

func (s *bankingService) CreateTransWithdrawWithSelectedAccount(req model.CreateTransWithdrawWithSelectedAccountRequest) (*model.FastbankWithdrawTransactionResponse, error) {

	startProcess := time.Now()

	// [********]fastbank credit check balance to continue unuse func or not auto
	getWebLocal, _ := s.repoBanking.GetLocalWebInfo()
	if getWebLocal != nil {
		if getWebLocal.FastbankCreditBalance <= -1000 {
			log.Println("CreateTransWithdrawWithSelectedAccount.WEB_OUT_OF_CREDIT")
			return nil, badRequest("ยอดเงินเว็บไม่เพียงพอ")
		}
	}

	admin, err := s.repoBanking.GetAdminById(*req.ConfirmedByAdminId)
	if err != nil {
		log.Println("CreateTransWithdrawWithSelectedAccount.GetAdminById.ERROR=", err)
		return nil, internalServerError(err)
	}

	transaction, err := s.repoBanking.GetTransactionWithdrawOverBudget(req.TransactionId)
	if err != nil {
		log.Println("CreateTransWithdrawWithSelectedAccount.GetTransactionWithdrawOverBudget.ERROR=", err)
		return nil, internalServerError(err)
	}
	if transaction.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_CANCELED {
		return nil, badRequest("ไม่สามารถทำรายการได้ เนื่องจากถูกยกเลิกไปแล้ว")
	}

	user, err := s.repoBanking.GetUserBankDetailById(transaction.UserId)
	if err != nil {
		return nil, internalServerError(err)
	}
	// อันนี้จะเลือกบัณชีที่เลือกมา
	bankAccount, err := s.repoBanking.GetBankAccountById(req.BankAccountId)
	if err != nil {
		return nil, internalServerError(err)
	}
	// [race condition withdraw]
	actionAt := time.Now().UTC().Format("************")
	var createBody model.RaceActionCreateBody
	createBody.Name = "AUTO_WITHDRAW"
	createBody.JsonRequest = fmt.Sprintf("req transactionId : %d", req.TransactionId)
	createBody.Status = "PENDING"
	createBody.ActionKey = fmt.Sprintf("WITHDRAW_T%s_U%d_CD%f", actionAt, user.Id, transaction.CreditAmount)
	createBody.UnlockAt = time.Now().UTC().Add(time.Second * 1)
	if _, err := s.repoBanking.GetRaceActionIdByActionKey(createBody.ActionKey); err != nil {
		log.Println("CreateTransWithdrawWithSelectedAccount.GetRaceActionIdByActionKey", err)
		if err != gorm.ErrRecordNotFound {
			return nil, internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
		}
	} else {
		return nil, internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
	}

	// create race condition
	actionId, err := s.repoBanking.CreateRaceCondition(createBody)
	if err != nil {
		log.Println("CreateTransWithdrawWithSelectedAccount.CreateRaceCondition", err)
		return nil, internalServerError(errors.New("CANNOT_CREATE_ACTION"))
	}

	if actionId == 0 {
		return nil, badRequest("PLEASE_WAIT_ONE_MINUTE_YOU_JUST_CLICKED_A_MOMENT_AGO_PLEASE_CHECK_THE_STATEMENT_AGAIN")
	}

	var statusId int64

	var transferBody model.WithdrawTransferFastBankBody
	transferBody.AccountFrom = bankAccount.AccountNumber
	transferBody.AccountTo = user.BankAccount
	if transaction.CreditAmount == float64(int(transaction.CreditAmount)) {
		// If the amount has no decimal part, convert it to two decimal places
		transferBody.Amount = strconv.FormatFloat(transaction.CreditAmount, 'f', 2, 64)
	} else {
		// If the amount already has decimals, leave it unchanged
		transferBody.Amount = strconv.FormatFloat(transaction.CreditAmount, 'f', -1, 64)
	}

	transferBody.BankCode = user.BankCode
	transferBody.Pin = bankAccount.PinCode

	//7.4 โอนเงิน fastbank
	var failedFromFastbank int64
	var responseFastbank model.FastbankWithdrawTransactionResponse
	responseFastbank.Id = req.TransactionId
	approvedTime := time.Now().UTC()
	// if bank account is manual bank will auto withdraw to success
	if !bankAccount.IsManualBank {

		fastbank, err := s.repoBanking.WithdrawWithFastBank(transferBody)
		statusLog := "SUCCESS"
		jsonPayLoad := fmt.Sprintf("WITHDRAW_TO_FROM_FASTBANK: %s FASTBANK RES :%s, ERROR :%s", helper.StructJson(transferBody), helper.StructJson(fastbank), err)
		jsonRequest := fmt.Sprintf(" BANK_TRANS_ID %v ", transaction.Id)
		logType := "CreateTransWithdrawWithSelectedAccount"
		var createLog model.BankTransactionLogCreateRequest
		createLog.Status = &statusLog
		createLog.JsonPayload = &jsonPayLoad
		createLog.JsonRequest = &jsonRequest
		createLog.LogType = &logType
		if _, err := s.serviceAccounting.CreateBankTransactionLog(createLog); err != nil {
			log.Println("CreateTransWithdrawWithSelectedAccount.CreateBankTransactionLog", err)
		}

		//7.5 เมื่อเสร็จสิ้นก็ update transaction status if fastbank success
		if err == nil && fastbank.Status.Code != 500 {

			failedFromFastbank = 0

			responseFastbank.Message = "Created success"
			responseFastbank.Status = 200
			statusId = model.TRANS_STATUS_WITHDRAW_SUCCESS
			var createConfirm model.CreateSuccessTransferWithdrawRequest
			createConfirm.TransactionId = req.TransactionId
			createConfirm.ConfirmedAt = time.Now()
			createConfirm.ConfirmedByAdminId = req.ConfirmedByAdminId
			if _, err := s.CreateSuccessTransferWithdraw(createConfirm); err != nil {
				return nil, nil
			}

			// DECREASE FASTBANK CREDIT
			if err := s.repoAccounting.DecreaseFastbankCredit(1); err != nil {
				log.Println("CreateTransWithdrawWithSelectedAccount.DECREASE_FASTBANK_CREDIT_ERROR", err)
			}

			endProcess := time.Now()
			elapsed := endProcess.Sub(startProcess)
			elapsedSeconds := elapsed.Seconds()
			timeElapsed := fmt.Sprintf("%.2f", elapsedSeconds)

			var externalNoti model.NotifyExternalNotificationRequest
			externalNoti.TypeNotify = model.IsWithdrawalCreditSuccess
			externalNoti.TransId = &req.TransactionId
			externalNoti.Amount = transaction.CreditAmount
			externalNoti.MemberCode = user.MemberCode
			externalNoti.UserCredit = transaction.AfterAmount
			externalNoti.ConfirmedByAdminId = *req.ConfirmedByAdminId
			externalNoti.TimerCounter = timeElapsed
			externalNoti.TransferDateTime = transaction.TransferAt.Add(7 * time.Hour).Format("2006-01-02 15:04:05")
			externalNoti.ActionTime = time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02 15:04:05")

			if err := s.serviceNoti.ExternalNotification(externalNoti); err != nil {
				log.Println("CreateTransWithdrawWithSelectedAccount.FailedNotify", err)
			}

			// Create detail
			var createBankTransactionExternalDetail model.BankTransactionExternalDetailCreateRequest
			createBankTransactionExternalDetail.BankTransactionId = req.TransactionId
			createBankTransactionExternalDetail.Detail = "SUCCESS"
			createBankTransactionExternalDetail.ErrorCode = 0
			if _, err := s.repoBanking.CreateBankTransactionExternalDetail(createBankTransactionExternalDetail); err != nil {
				return nil, nil
			}

		} else {
			failedFromFastbank = 1
			if fastbank != nil {
				responseFastbank.Status = int64(fastbank.Status.Code)
				if fastbank.Status.Description != "" {
					responseFastbank.Message = fastbank.Status.Description
				} else {
					responseFastbank.Message = fmt.Sprintf("Error: %s", err)
				}
			} else {
				responseFastbank.Status = 0
				responseFastbank.Message = fmt.Sprintf("Error: %s", err)
			}

			statusId = model.TRANS_STATUS_WITHDRAW_UNSURE
			// Create detail
			var createBankTransactionExternalDetail model.BankTransactionExternalDetailCreateRequest
			createBankTransactionExternalDetail.BankTransactionId = req.TransactionId
			createBankTransactionExternalDetail.Detail = responseFastbank.Message
			createBankTransactionExternalDetail.ErrorCode = responseFastbank.Status
			if _, err := s.repoBanking.CreateBankTransactionExternalDetail(createBankTransactionExternalDetail); err != nil {
				return nil, nil
			}
		}
	} else {
		failedFromFastbank = 0

		responseFastbank.Message = "Created Maunal success"
		responseFastbank.Status = 200
		statusId = model.TRANS_STATUS_WITHDRAW_SUCCESS
		var createConfirm model.CreateSuccessTransferWithdrawRequest
		createConfirm.TransactionId = req.TransactionId
		createConfirm.ConfirmedAt = time.Now()
		createConfirm.ConfirmedByAdminId = req.ConfirmedByAdminId
		if _, err := s.CreateSuccessTransferWithdraw(createConfirm); err != nil {
			return nil, nil
		}

		statusId = model.TRANS_STATUS_WITHDRAW_SUCCESS

		endProcess := time.Now()
		elapsed := endProcess.Sub(startProcess)
		elapsedSeconds := elapsed.Seconds()
		timeElapsed := fmt.Sprintf("%.2f", elapsedSeconds)

		var externalNoti model.NotifyExternalNotificationRequest
		externalNoti.TypeNotify = model.IsWithdrawalCreditSuccess
		externalNoti.TransId = &req.TransactionId
		externalNoti.Amount = transaction.CreditAmount
		externalNoti.MemberCode = user.MemberCode
		externalNoti.UserCredit = transaction.AfterAmount
		externalNoti.ConfirmedByAdminId = *req.ConfirmedByAdminId
		externalNoti.TimerCounter = timeElapsed
		externalNoti.TransferDateTime = transaction.TransferAt.Add(7 * time.Hour).Format("2006-01-02 15:04:05")
		externalNoti.ActionTime = time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02 15:04:05")

		if err := s.serviceNoti.ExternalNotification(externalNoti); err != nil {
			log.Println("CreateTransWithdrawWithSelectedAccount.FailedNotify", err)
		}

	}
	var updateApprovedBy model.UpdateConfirmAutoWithdrawBody
	updateApprovedBy.TransactionStatusId = &statusId
	updateApprovedBy.ConfirmedAt = time.Now()
	updateApprovedBy.ConfirmedByAdminId = req.ConfirmedByAdminId
	updateApprovedBy.FromAccountId = &req.BankAccountId
	updateApprovedBy.FromBankId = &bankAccount.BankId
	updateApprovedBy.FromAccountName = &bankAccount.AccountName
	updateApprovedBy.FromAccountNumber = &bankAccount.AccountNumber
	if statusId == model.TRANS_STATUS_WITHDRAW_SUCCESS {
		updateApprovedBy.TransferAt = &approvedTime
	}
	err = s.repoBanking.UpdateAdminAndTransactionStatus(req.TransactionId, updateApprovedBy)
	if err != nil {
		log.Println("CreateTransWithdrawWithSelectedAccount.UpdateAdminAndTransactionStatus", err)
	}

	// 2024/05/08 Show Withdraw USER_TRANSACTION ย้ายมาตรงนี้จะได้ให้ admin โชว์ถูก account ล่าสุดที่จะโอน Confirm P.mink
	var showUserTrans model.UserTransactionShowUpdate
	showUserTrans.TransactionId = req.TransactionId
	showUserTrans.ConfirmedAt = &updateApprovedBy.ConfirmedAt
	showUserTrans.ConfirmAdminId = req.ConfirmedByAdminId
	showUserTrans.Detail = fmt.Sprintf("%s:%s => %s:%s", bankAccount.BankName, bankAccount.AccountNumber, user.BankName, user.Fullname)
	showUserTrans.AccountId = &req.BankAccountId
	showUserTrans.FailedAddCredit = &failedFromFastbank
	showUserTrans.TransferAt = &approvedTime
	if err := s.repoAccounting.ShowUserTransaction(showUserTrans); err != nil {
		return nil, nil
	}

	// ===========================================================
	// RemoveFrom BankPendingRecord -> รายการถอน อนุมัติรายการถอน แบบเปลี่ยนบช.ธนาคารถอนออก
	dashboardRepo := repository.NewDashboardRepository(s.SharedDb)
	if err := ConfirmBankPendingRecordFromAny(dashboardRepo, transaction.TransactionTypeId, transaction.Id, startProcess, admin.Fullname); err != nil {
		log.Println("CreateTransWithdrawWithSelectedAccount.ConfirmBankPendingRecordFromAny.ERROR=", err)
	}

	return &responseFastbank, nil
}

func (s *bankingService) CreateTransWithdrawWithExternalAccount(req model.CreateTransWithdrawWithExternalAccountRequest) (*int64, error) {

	// [********] Todo เอา API เส้นนี้ออก ไม่ได้ใช้ คลิกยืนยัน แล้ว
	startProcess := time.Now()
	var err error

	var updateApprovedBy model.UpdateConfirmAutoWithdrawBody
	statusId := model.TRANS_STATUS_WITHDRAW_SUCCESS
	updateApprovedBy.TransactionStatusId = &statusId
	updateApprovedBy.ConfirmedAt = time.Now()
	updateApprovedBy.ConfirmedByAdminId = req.ConfirmedByAdminId
	externalId := int64(0)
	externalBankId := model.BANK_ID_EXTERNAL
	externalAccount := "บัญชีธนาคาร ภายนอก"
	updateApprovedBy.FromAccountId = &externalId
	updateApprovedBy.FromBankId = &externalBankId
	updateApprovedBy.FromAccountName = &externalAccount
	updateApprovedBy.FromAccountNumber = &externalAccount

	transaction, err := s.repoBanking.GetTransactionByIdForNoti(req.TransactionId)
	if err != nil {
		return nil, internalServerError(err)
	}

	if transaction.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_CANCELED {
		return nil, badRequest("ไม่สามารถทำรายการได้ เนื่องจากถูกยกเลิกไปแล้ว")
	}

	// race condition deposit
	actionAt := time.Now().UTC().Format("************")
	var createBody model.RaceActionCreateBody
	createBody.Name = "ADMIN_APPROVE_EXTERNAL_WITHDRAW"
	createBody.JsonRequest = helper.StructJson(req)
	createBody.Status = "PENDING"
	createBody.ActionKey = fmt.Sprintf("WITHDRAW_ADMIN_T%s_U%d_CD%f", actionAt, transaction.UserId, transaction.CreditAmount)
	createBody.UnlockAt = time.Now().UTC().Add(time.Second * 1)
	if _, err := s.repoBanking.GetRaceActionIdByActionKey(createBody.ActionKey); err != nil {
		log.Println("CreateTransWithdrawWithExternalAccount.GetRaceActionIdByActionKey", err)
		if err != gorm.ErrRecordNotFound {
			return nil, internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
		}
	} else {
		return nil, internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
	}

	err = s.repoBanking.UpdateAdminAndTransactionStatus(req.TransactionId, updateApprovedBy)
	if err != nil {
		return nil, internalServerError(err)
	}
	var createConfirm model.CreateSuccessTransferWithdrawRequest
	createConfirm.TransactionId = req.TransactionId
	createConfirm.ConfirmedAt = time.Now()
	createConfirm.ConfirmedByAdminId = req.ConfirmedByAdminId
	if _, err := s.CreateSuccessTransferWithdraw(createConfirm); err != nil {
		return nil, nil
	}

	user, err := s.repoBanking.GetUserBankDetailByMemberCode(transaction.UserMemberCode)
	if err != nil {
		log.Println("CreateTransWithdrawWithExternalAccount.GetUserBankDetailByMemberCode", err)
		return nil, internalServerError(err)
	}
	// Show Withdraw USER_TRANSACTION
	var showUserTrans model.UserTransactionShowUpdate
	showUserTrans.TransactionId = createConfirm.TransactionId
	showUserTrans.ConfirmedAt = &createConfirm.ConfirmedAt
	showUserTrans.ConfirmAdminId = createConfirm.ConfirmedByAdminId
	setRemovedExternal := int64(1)
	showUserTrans.RemovedExternal = &setRemovedExternal
	showUserTrans.Detail = fmt.Sprintf("บัญชีธนาคาร ภายนอก => %s:%s", user.BankName, user.BankAccount)
	if err := s.repoAccounting.ShowUserTransaction(showUserTrans); err != nil {
		return nil, nil
	}

	var updateApprovedByAdmin model.UpdateConfirmedByAdminIdRequest
	updateApprovedByAdmin.ConfirmAdminId = req.ConfirmedByAdminId
	errUpdateConfirmed := s.repoBanking.UpdateConfirmedByAdminId(req.TransactionId, updateApprovedByAdmin)
	if errUpdateConfirmed != nil {
		log.Println("CreateTransWithdrawWithExternalAccount.UpdateConfirmedByAdminId", errUpdateConfirmed)
	}

	endProcess := time.Now()
	elapsed := endProcess.Sub(startProcess)
	elapsedSeconds := elapsed.Seconds()
	timeElapsed := fmt.Sprintf("%.2f", elapsedSeconds)

	var externalNoti model.NotifyExternalNotificationRequest
	externalNoti.TypeNotify = model.IsWithdrawalCreditSuccess
	externalNoti.TransId = &req.TransactionId
	externalNoti.Amount = transaction.CreditAmount
	externalNoti.MemberCode = user.MemberCode
	externalNoti.UserCredit = transaction.AfterAmount
	externalNoti.ConfirmedByAdminId = *req.ConfirmedByAdminId
	externalNoti.TimerCounter = timeElapsed
	externalNoti.WebScoket.UserID = user.Id
	externalNoti.WebScoket.Amount = transaction.CreditAmount
	externalNoti.WebScoket.MemberCode = user.MemberCode
	externalNoti.WebScoket.AlertType = "WITHDRAW"
	externalNoti.TransferDateTime = transaction.TransferAt.Add(7 * time.Hour).Format("2006-01-02 15:04:05")
	externalNoti.ActionTime = time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02 15:04:05")

	if err := s.serviceNoti.ExternalNotification(externalNoti); err != nil {
		log.Println("FailedNotify", err)
	}

	return nil, nil
}

func (s *bankingService) CreateTransWithdrawWithManualAccount(req model.CreateTransWithdrawWithManualAccountRequest) (*int64, error) {

	// [********] แก้ไขจาก คลิกยืนยัน.CreateTransWithdrawWithExternalAccount เป็น โอนมือ.CreateTransWithdrawWithManualAccount
	// เพิ่มการกรอกข้อมูล บัญชีธนาคาร ภายนอก และ อัพโหลดสลิปด้วย
	startProcess := time.Now()
	statusId := model.TRANS_STATUS_WITHDRAW_SUCCESS
	externalId := int64(0)
	if req.BankId == nil {
		externalBankId := model.BANK_ID_EXTERNAL
		req.BankId = &externalBankId
	}
	if req.AccountName == nil {
		externalAccount := "บัญชีธนาคาร ภายนอก"
		req.AccountName = &externalAccount
	}
	if req.AccountNumber == nil {
		externalAccount := "บัญชีธนาคาร ภายนอก"
		req.AccountNumber = &externalAccount
	}

	admin, err := s.repoBanking.GetAdminById(*req.ConfirmedByAdminId)
	if err != nil {
		log.Println("CreateTransWithdrawWithManualAccount.GetAdminById.ERROR=", err)
		return nil, internalServerError(err)
	}

	transaction, err := s.repoBanking.GetTransactionByIdForNoti(req.TransactionId)
	if err != nil {
		log.Println("CreateTransWithdrawWithManualAccount.GetTransactionByIdForNoti.ERROR=", err)
		return nil, internalServerError(err)
	}
	// PENDING_ALL = TRANS_STATUS_WITHDRAW_PENDING TRANS_STATUS_WITHDRAW_OVER_BUDGET TRANS_STATUS_WITHDRAW_OVER_MAX TRANS_STATUS_WITHDRAW_APPROVED TRANS_STATUS_WITHDRAW_UNSURE
	validStatus := map[int64]bool{
		model.TRANS_STATUS_WITHDRAW_PENDING:     true,
		model.TRANS_STATUS_WITHDRAW_OVER_BUDGET: true,
		model.TRANS_STATUS_WITHDRAW_OVER_MAX:    true,
		model.TRANS_STATUS_WITHDRAW_APPROVED:    true,
		model.TRANS_STATUS_WITHDRAW_UNSURE:      true,
	}
	if _, ok := validStatus[transaction.TransactionStatusId]; !ok {
		log.Println("CreateTransWithdrawWithManualAccount.TransactionStatusId=", transaction.TransactionStatusId, " ไม่สามารถทำรายการได้ เนื่องจากไม่ใช่สถานะรอยืนยัน")
		return nil, badRequest("ไม่สามารถทำรายการได้ เนื่องจากไม่ใช่สถานะรอยืนยัน")
	}

	user, err := s.repoBanking.GetUserBankDetailByMemberCode(transaction.UserMemberCode)
	if err != nil {
		log.Println("CreateTransWithdrawWithManualAccount.GetUserBankDetailByMemberCode", err)
		return nil, internalServerError(err)
	}

	// =================== DONE Checking

	// race condition withdraw
	actionAt := time.Now().UTC().Format("************")
	var createBody model.RaceActionCreateBody
	createBody.Name = "ADMIN_APPROVE_MANUAL_WITHDRAW"
	createBody.JsonRequest = helper.StructJson(req)
	createBody.Status = "PENDING"
	createBody.ActionKey = fmt.Sprintf("WITHDRAW_ADMIN_T%s_U%d_CD%f", actionAt, transaction.UserId, transaction.CreditAmount)
	createBody.UnlockAt = time.Now().UTC().Add(time.Second * 1)
	if _, err := s.repoBanking.GetRaceActionIdByActionKey(createBody.ActionKey); err != nil {
		log.Println("CreateTransWithdrawWithManualAccount.GetRaceActionIdByActionKey.ERROR=", err)
		if err != gorm.ErrRecordNotFound {
			return nil, internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
		}
	} else {
		log.Println("CreateTransWithdrawWithManualAccount.GetRaceActionIdByActionKey", "createBody=", createBody, "already exists")
		return nil, internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
	}

	// =================== DONE Racing

	var updateApprovedBy model.UpdateConfirmAutoWithdrawBody
	updateApprovedBy.TransactionStatusId = &statusId
	updateApprovedBy.ConfirmedAt = time.Now()
	updateApprovedBy.ConfirmedByAdminId = req.ConfirmedByAdminId
	updateApprovedBy.FromAccountId = &externalId
	updateApprovedBy.FromBankId = req.BankId
	updateApprovedBy.FromAccountNumber = req.AccountNumber
	updateApprovedBy.FromAccountName = req.AccountName
	updateApprovedBy.SlipImgUrl = req.SlipImgUrl
	if err = s.repoBanking.UpdateAdminAndTransactionStatus(req.TransactionId, updateApprovedBy); err != nil {
		log.Println("CreateTransWithdrawWithManualAccount.UpdateAdminAndTransactionStatus.ERROR=", err)
		return nil, internalServerError(err)
	}
	var createConfirm model.CreateSuccessTransferWithdrawRequest
	createConfirm.TransactionId = req.TransactionId
	createConfirm.ConfirmedAt = time.Now()
	createConfirm.ConfirmedByAdminId = req.ConfirmedByAdminId
	if _, err := s.CreateSuccessTransferWithdraw(createConfirm); err != nil {
		log.Println("CreateTransWithdrawWithManualAccount.CreateSuccessTransferWithdraw.ERROR=", err)
		return nil, nil
	}

	// Show Withdraw USER_TRANSACTION
	var showUserTrans model.UserTransactionShowUpdate
	showUserTrans.TransactionId = createConfirm.TransactionId
	showUserTrans.ConfirmedAt = &createConfirm.ConfirmedAt
	showUserTrans.ConfirmAdminId = createConfirm.ConfirmedByAdminId
	setRemovedExternal := int64(1)
	showUserTrans.RemovedExternal = &setRemovedExternal
	showUserTrans.Detail = fmt.Sprintf("บัญชีธนาคาร ภายนอก => %s:%s", user.BankName, user.BankAccount)
	if err := s.repoAccounting.ShowUserTransaction(showUserTrans); err != nil {
		log.Println("CreateTransWithdrawWithManualAccount.ShowUserTransaction.ERROR=", err)
		return nil, nil
	}

	var updateApprovedByAdmin model.UpdateConfirmedByAdminIdRequest
	updateApprovedByAdmin.ConfirmAdminId = req.ConfirmedByAdminId
	errUpdateConfirmed := s.repoBanking.UpdateConfirmedByAdminId(req.TransactionId, updateApprovedByAdmin)
	if errUpdateConfirmed != nil {
		log.Println("CreateTransWithdrawWithManualAccount.UpdateConfirmedByAdminId", errUpdateConfirmed)
	}

	// ===========================================================
	// RemoveFrom BankPendingRecord -> รายการถอน อนุมัติรายการถอนโดย โอนมือ
	dashboardRepo := repository.NewDashboardRepository(s.SharedDb)
	if err := ConfirmBankPendingRecordFromAny(dashboardRepo, transaction.TransactionTypeId, transaction.Id, startProcess, admin.Fullname); err != nil {
		log.Println("CreateTransWithdrawWithManualAccount.ConfirmBankPendingRecordFromAny.ERROR=", err)
	}

	endProcess := time.Now()
	elapsed := endProcess.Sub(startProcess)
	elapsedSeconds := elapsed.Seconds()
	timeElapsed := fmt.Sprintf("%.2f", elapsedSeconds)

	var externalNoti model.NotifyExternalNotificationRequest
	externalNoti.TypeNotify = model.IsWithdrawalCreditSuccess
	externalNoti.TransId = &req.TransactionId
	externalNoti.Amount = transaction.CreditAmount
	externalNoti.MemberCode = user.MemberCode
	externalNoti.UserCredit = transaction.AfterAmount
	externalNoti.ConfirmedByAdminId = *req.ConfirmedByAdminId
	externalNoti.TimerCounter = timeElapsed
	externalNoti.WebScoket.UserID = user.Id
	externalNoti.WebScoket.Amount = transaction.CreditAmount
	externalNoti.WebScoket.MemberCode = user.MemberCode
	externalNoti.WebScoket.AlertType = "WITHDRAW"
	externalNoti.TransferDateTime = transaction.TransferAt.Add(7 * time.Hour).Format("2006-01-02 15:04:05")
	externalNoti.ActionTime = time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02 15:04:05")
	if err := s.serviceNoti.ExternalNotification(externalNoti); err != nil {
		log.Println("CreateTransWithdrawWithManualAccount.ExternalNotification.FailedNotify=", err)
	}

	return nil, nil
}

func (s *bankingService) GetBankTransactionWithdrawList(req model.GetBankTransactionWithdrawListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.Pagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	tranStatusPending := strconv.FormatInt(model.TRANS_STATUS_WITHDRAW_PENDING, 10)
	tranStatusPendingOverBudget := strconv.FormatInt(model.TRANS_STATUS_WITHDRAW_OVER_BUDGET, 10)
	tranStatusApprove := strconv.FormatInt(model.TRANS_STATUS_WITHDRAW_APPROVED, 10)
	tranStatusRejected := strconv.FormatInt(model.TRANS_STATUS_WITHDRAW_REJECTED, 10)
	tranStatusCanceled := strconv.FormatInt(model.TRANS_STATUS_WITHDRAW_CANCELED, 10)
	tranStatusfailed := strconv.FormatInt(model.TRANS_STATUS_WITHDRAW_FAILED, 10)
	tranStatusSuccess := strconv.FormatInt(model.TRANS_STATUS_WITHDRAW_SUCCESS, 10)
	tranStatusOverMax := strconv.FormatInt(model.TRANS_STATUS_WITHDRAW_OVER_MAX, 10)
	tranStatusUnsure := strconv.FormatInt(model.TRANS_STATUS_WITHDRAW_UNSURE, 10)
	transStatusTransfering := strconv.FormatInt(model.TRANS_STATUS_WITHDRAW_TRASNFERING, 10)

	if req.TransactionStatus == "PENDING_ALL" {
		req.TransactionStatus = fmt.Sprintf("%v,%v,%v,%v,%v", tranStatusPending, tranStatusPendingOverBudget, tranStatusOverMax, tranStatusApprove, tranStatusUnsure)
	}
	if req.TransactionStatus == "PENDING_CREDIT" {
		req.TransactionStatus = tranStatusPending
	}
	if req.TransactionStatus == "PENDING_TRANSFER" {
		req.TransactionStatus = fmt.Sprintf("%v,%v,%v,%v", tranStatusPendingOverBudget, tranStatusOverMax, tranStatusApprove, tranStatusUnsure)
	}
	if req.TransactionStatus == "TRANSFERING" {
		req.TransactionStatus = transStatusTransfering
	}
	if req.TransactionStatus == "SUCCESS" {
		req.TransactionStatus = tranStatusSuccess
	}
	if req.TransactionStatus == "FAILED" {
		req.TransactionStatus = fmt.Sprintf("%v,%v", tranStatusfailed, tranStatusRejected)
	}
	if req.TransactionStatus == "CANCELED" {
		req.TransactionStatus = tranStatusCanceled
	}

	result, total, err := s.repoBanking.GetBankTransactionWithdrawList(req)
	if err != nil {
		return nil, internalServerError(err)
	}

	// loop main status
	for i, record := range result {
		item := &result[i]
		// [********] mink confirm no bank account in tb not allow checking auto
		if record.FromBankId == 0 {
			item.IsAllowAuto = false
		} else {
			item.IsAllowAuto = true
		}
		// if merchantName == record.PaymentType { bank_transaction.from_account_name as payment_type"
		item.IsPayment = s.CheckIsTransferNameisPaymentGateway(record.PaymentType)
		if record.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_PENDING {
			item.Status = "PENDING_CREDIT"
			item.ConfirmedAt = nil
		}
		if record.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_OVER_BUDGET || record.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_OVER_MAX || record.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_APPROVED || record.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_UNSURE {
			item.Status = "PENDING_TRANSFER"
			item.ConfirmedAt = nil
		}
		if record.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_TRASNFERING {
			item.Status = "TRANSFERING"
			item.ConfirmedAt = nil
		}
		if record.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_SUCCESS {
			item.Status = "SUCCESS"
		}
		if record.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_REJECTED || record.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_FAILED {
			item.Status = "FAILED"
		}
		if record.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_CANCELED {
			item.Status = "CANCELED"
		}
		// Set Payment GatewayInfo
		if record.FromAccountName2 == model.PAYGATE_MERCHANT_TYPE_HENG || record.FromAccountName2 == model.PAYGATE_MERCHANT_TYPE_LUCKYTHAI || record.FromAccountName2 == model.PAYGATE_MERCHANT_TYPE_PAPAYAPAY {
			item.FromBankName = record.FromAccountName2
		}
		if record.FromAccountName2 == model.PAYGATE_MERCHANT_TYPE_PAYONEX || record.FromAccountName2 == model.PAYGATE_MERCHANT_TYPE_JBPAY || record.FromAccountName2 == model.PAYGATE_MERCHANT_TYPE_POMPAY {
			item.FromBankName = record.FromAccountName2
		}
		if record.FromAccountName2 == model.PAYGATE_MERCHANT_TYPE_PAYMENTCO || record.FromAccountName2 == model.PAYGATE_MERCHANT_TYPE_ZAPPAY || record.FromAccountName2 == model.PAYGATE_MERCHANT_TYPE_ONEPAY {
			item.FromBankName = record.FromAccountName2
		}
		if record.FromAccountName2 == model.PAYGATE_MERCHANT_TYPE_FLASHPAY || record.FromAccountName2 == model.PAYGATE_MERCHANT_TYPE_BIZPAY || record.FromAccountName2 == model.PAYGATE_MERCHANT_TYPE_SUGARPAY {
			item.FromBankName = record.FromAccountName2
		}
		if record.FromAccountName2 == model.PAYGATE_MERCHANT_TYPE_ZMANPAY || record.FromAccountName2 == model.PAYGATE_MERCHANT_TYPE_POSTMANPAY || record.FromAccountName2 == model.PAYGATE_MERCHANT_TYPE_MAZEPAY {
			item.FromBankName = record.FromAccountName2
		}
		if record.FromAccountName2 == model.PAYGATE_MERCHANT_TYPE_MEEPAY {
			item.FromBankName = record.FromAccountName2
		}
	}
	var response model.SuccessWithPagination
	response.List = result
	response.Total = total
	return &response, nil
}

func (s *bankingService) CheckIsTransferNameisPaymentGateway(name string) bool {

	// if merchantName == record.PaymentType { bank_transaction.from_account_name as payment_type"
	// createBankTransaction.FromAccountName = &pgWdAccount.TypeName
	switch name {
	case model.PAYGATE_MERCHANT_TYPE_HENG:
		return true
	case model.PAYGATE_MERCHANT_TYPE_LUCKYTHAI:
		return true
	case model.PAYGATE_MERCHANT_TYPE_PAPAYAPAY:
		return true
	case model.PAYGATE_MERCHANT_TYPE_PAYONEX:
		return true
	case model.PAYGATE_MERCHANT_TYPE_JBPAY:
		return true
	case model.PAYGATE_MERCHANT_TYPE_POMPAY:
		return true
	case model.PAYGATE_MERCHANT_TYPE_PAYMENTCO:
		return true
	case model.PAYGATE_MERCHANT_TYPE_ZAPPAY:
		return true
	case model.PAYGATE_MERCHANT_TYPE_ONEPAY:
		return true
	case model.PAYGATE_MERCHANT_TYPE_FLASHPAY:
		return true
	case model.PAYGATE_MERCHANT_TYPE_BIZPAY:
		return true
	case model.PAYGATE_MERCHANT_TYPE_SUGARPAY:
		return true
	case model.PAYGATE_MERCHANT_TYPE_ZMANPAY:
		return true
	case model.PAYGATE_MERCHANT_TYPE_POSTMANPAY:
		return true
	case model.PAYGATE_MERCHANT_TYPE_MAZEPAY:
		return true
	case model.PAYGATE_MERCHANT_TYPE_MEEPAY:
		return true
	}
	return false
}

func (s *bankingService) GetDepositTransactionById(transId int64) (*model.GetBankTransactionDepositListResponse, error) {

	result, err := s.repoBanking.GetDepositTransactionById(transId)
	if err != nil {
		return nil, internalServerError(err)
	}
	return result, nil
}

func (s *bankingService) GetTransactionWithdrawById(transId int64) (*model.GetBankTransactionWithdrawListResponse, error) {

	result, err := s.repoBanking.GetTransactionWithdrawById(transId)
	if err != nil {
		return nil, internalServerError(err)
	}
	return result, nil
}

func (s *bankingService) CancelWithdrawCredit(req model.CancelWithdrawCreditRequest) (*int64, error) {

	startProcess := time.Now().UTC()

	admin, err := s.repoBanking.GetAdminById(*req.ConfirmedByAdminId)
	if err != nil {
		log.Println("CancelWithdrawCredit.GetAdminById.ERROR=", err)
		return nil, internalServerError(err)
	}

	transaction, err := s.repoBanking.GetTransactionWithdrawOverBudget(req.TransactionId)
	if err != nil {
		log.Println("CancelWithdrawCredit.GetTransactionWithdrawOverBudget.ERROR=", err, "ไม่ตรงกับเงือนไข")
		return nil, badRequest("ไม่ตรงกับเงือนไข")
	}
	user, err := s.repoBanking.GetUserBankDetailById(transaction.UserId)
	if err != nil {
		log.Println("CancelWithdrawCredit.GetUserBankDetailById.ERROR=", err)
		return nil, internalServerError(err)
	}

	// แก้บัค ให้ admin สามารถยกเลิกการดึงเครดิทกลับได้
	returnAmount := transaction.CreditAmount
	if transaction.TransactionTypeId == model.TRANSACTION_TYPE_CREDITBACK {
		returnAmount = transaction.CreditBack
	} else if transaction.TransactionTypeId == model.TRANSACTION_TYPE_CREDITCANCEL {
		returnAmount = transaction.CreditBack
	}

	// race condition deposit
	actionAt := startProcess.UTC().Format("************")
	var createBody model.RaceActionCreateBody
	createBody.Name = "ADMIN_CANCEL_WITHDRAW"
	createBody.JsonRequest = helper.StructJson(req)
	createBody.Status = "PENDING"
	createBody.ActionKey = fmt.Sprintf("WITHDRAW_ADMIN_T%s_U%d_CD%f", actionAt, user.Id, returnAmount)
	createBody.UnlockAt = startProcess.UTC().Add(time.Second * 1)
	if _, err := s.repoBanking.GetRaceActionIdByActionKey(createBody.ActionKey); err != nil {
		log.Println("CreateBankStatementFromWebhookAndAuto.GetRaceActionIdByActionKey", err)
		if err != gorm.ErrRecordNotFound {
			return nil, internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
		}
	} else {
		log.Println("CancelWithdrawCredit.GetRaceActionIdByActionKey", "createBody=", createBody, "already exists")
		return nil, internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
	}

	// [********] only cancel withdraw credit with rejected will not return credit
	if transaction.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_APPROVED || transaction.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_FAILED || transaction.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_SUCCESS || transaction.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_OVER_BUDGET || transaction.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_OVER_MAX || transaction.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_UNSURE {

		// [********] check user credit transaction ว่าตรงไหมกับเงื่อนไข (isShow = false)
		userTransaction, err := s.repoBanking.GetUserWithdrawCreditTransactionByRefId(transaction.Id, transaction.CreditAmount)
		if err != nil {
			if err.Error() == recordNotFound {
				log.Println("CancelWithdrawCredit.GetUserCreditTransactionById", err)
				return nil, badRequest("ไม่ตรงกับเงือนไข")
			}
			return nil, internalServerError(err)
		}
		if userTransaction != nil {
			var userCreditReq model.UserTransactionCreateRequest
			userCreditReq.UserId = user.Id
			userCreditReq.TransferAt = transaction.TransferAt
			userCreditReq.RefId = &transaction.Id
			userCreditReq.TypeId = model.CREDIT_TYPE_DEPOSIT
			userCreditReq.Amount = returnAmount
			IsShow := false
			userCreditReq.IsShow = &IsShow
			userCreditReq.ConfirmBy = req.ConfirmedByAdminId
			if agentResp, err := s.repoAccounting.IncreaseUserCredit(userCreditReq); err != nil {
				log.Println("CancelWithdrawCredit.IncreaseUserCredit", err)
				return nil, internalServerError(err)
			} else if !agentResp.AgentSuccess {
				// [********] error from agent will be in failed
				// if err := s.repoAccounting.UpdateDeporsitTransactionStatusFromAgent(*transId, *agentResp); err != nil {
				//     log.Println("CancelWithdrawCredit.UpdateDeporsitTransactionStatusFromAgent", err)
				// }
				// Set FastBank-READ ** ไม่มี เพราะเป็นการสร้างรายการฝากเอง
				agentFail := errors.New("ทำรายการเครดิตไม่สำเร็จ")
				log.Println("CancelWithdrawCredit.IncreaseUserCredit", agentFail)
				return nil, internalServerError(agentFail)
			}
		}
	}

	var updateApprovedBy model.UpdateUserTransactionStatusRequest
	updateApprovedBy.Id = req.TransactionId
	updateApprovedBy.CanceledAt = startProcess
	updateApprovedBy.CanceledByAdminId = *req.ConfirmedByAdminId
	updateApprovedBy.TransactionStatusId = model.TRANS_STATUS_WITHDRAW_CANCELED
	updateApprovedBy.CancelRemark = &req.CancelRemark
	// 	ConfirmedAt         *time.Time `json:"confirmedAt"`
	// ConfirmedByAdminId  *int64     `json:"confirmedByAdminId"`
	updateApprovedBy.ConfirmedAt = &req.ConfirmedAt
	updateApprovedBy.ConfirmedByAdminId = req.ConfirmedByAdminId
	err = s.repoBanking.UpdateUserTransactionStatus(updateApprovedBy)
	if err != nil {
		return nil, internalServerError(err)
	}

	var createConfirm model.CreateSuccessTransferWithdrawRequest
	createConfirm.TransactionId = req.TransactionId
	createConfirm.ConfirmedAt = startProcess
	createConfirm.ConfirmedByAdminId = req.ConfirmedByAdminId
	if _, err := s.CreateCanceledTransferWithdraw(createConfirm); err != nil {
		return nil, err
	}

	// ===========================================================
	// RemoveFrom BankPendingRecord -> รายการถอน ไม่อนุมัติรายการถอนโดย การกดยกเลิกรายการ
	dashboardRepo := repository.NewDashboardRepository(s.SharedDb)
	if err := ConfirmBankPendingRecordFromAny(dashboardRepo, transaction.TransactionTypeId, transaction.Id, startProcess, admin.Fullname); err != nil {
		log.Println("CreateTransWithdrawWithManualAccount.ConfirmBankPendingRecordFromAny.ERROR=", err)
	}

	return nil, err
}

func (s *bankingService) CheckTransferingWithdraw(req model.WithdrawCheckTransferingRequest) (model.WithdrawCheckTransferingResponse, error) {

	var result model.WithdrawCheckTransferingResponse

	transaction, err := s.repoBanking.GetTransactionWithdrawById(req.TransactionId)
	if err != nil {
		return result, badRequest("INVALID_TRANSACTION")
	}

	result.TransactionId = transaction.Id
	result.Status = "TRANSFERING"

	if transaction.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		// Send to PaymentGateway
		return CheckTransferingWithdraw(repository.NewPaymentGatewayRepository(s.SharedDb), transaction.Id)
	} else if transaction.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_SUCCESS {
		result.Status = "SUCCESS"
	} else {
		result.Status = "FAILED"
	}
	return result, err
}

func CreateCanceledTransferWithdraw(repoBanking repository.BankingRepository, req model.CreateSuccessTransferWithdrawRequest) (*int64, error) {

	record, err := repoBanking.GetTransactionWithdrawById(req.TransactionId)
	if err != nil {
		return nil, internalServerError(err)
	}
	transactionAction, err := repoBanking.GetTransactionActionById(req.TransactionId)
	if transactionAction == nil && err != nil {
		jsonBefore, _ := json.Marshal(record)
		var createBody model.CreateBankTransactionActionBody
		createBody.ActionKey = fmt.Sprintf("CANCEL#%d", req.TransactionId)
		createBody.TransactionId = req.TransactionId
		createBody.UserId = record.UserId
		createBody.TransactionTypeId = record.TransactionTypeId
		createBody.FromAccountId = record.FromAccountId
		createBody.ToAccountId = record.ToAccountId
		createBody.JsonBefore = string(jsonBefore)
		createBody.TransferAt = &record.TransferAt
		createBody.CreditAmount = record.CreditAmount
		createBody.ConfirmedAt = req.ConfirmedAt
		createBody.ConfirmedByAdminId = req.ConfirmedByAdminId
		// BOF : transaction
		if _, err := repoBanking.CreateTransactionAction(createBody); err != nil {
			return nil, internalServerError(err)
		}
	} else {
		if err := repoBanking.UpdateTransactionIngnoreActionById(req.TransactionId); err != nil {
			return nil, internalServerError(err)
		}
	}

	return nil, nil
}

func (s *bankingService) CreateCanceledTransferWithdraw(req model.CreateSuccessTransferWithdrawRequest) (*int64, error) {

	record, err := s.repoBanking.GetTransactionWithdrawById(req.TransactionId)
	if err != nil {
		return nil, internalServerError(err)
	}
	transactionAction, err := s.repoBanking.GetTransactionActionById(req.TransactionId)
	if transactionAction == nil && err != nil {
		jsonBefore, _ := json.Marshal(record)
		var createBody model.CreateBankTransactionActionBody
		createBody.ActionKey = fmt.Sprintf("CANCEL#%d", req.TransactionId)
		createBody.TransactionId = req.TransactionId
		createBody.UserId = record.UserId
		createBody.TransactionTypeId = record.TransactionTypeId
		createBody.FromAccountId = record.FromAccountId
		createBody.ToAccountId = record.ToAccountId
		createBody.JsonBefore = string(jsonBefore)
		createBody.TransferAt = &record.TransferAt
		createBody.CreditAmount = record.CreditAmount
		createBody.ConfirmedAt = req.ConfirmedAt
		createBody.ConfirmedByAdminId = req.ConfirmedByAdminId
		// BOF : transaction
		if _, err := s.repoBanking.CreateTransactionAction(createBody); err != nil {
			return nil, internalServerError(err)
		}
	} else {
		if err := s.repoBanking.UpdateTransactionIngnoreActionById(req.TransactionId); err != nil {
			return nil, internalServerError(err)
		}
	}

	return nil, nil
}

func (s *bankingService) CreateCanceledTransferDeposit(req model.CreateSuccessTransferWithdrawRequest) (*int64, error) {

	record, err := s.repoBanking.GetDepositTransactionById(req.TransactionId)
	if err != nil {
		return nil, internalServerError(err)
	}

	// GetTransactionActionById
	transactionAction, err := s.repoBanking.GetTransactionActionById(req.TransactionId)
	if transactionAction == nil && err != nil {
		jsonBefore, _ := json.Marshal(record)
		var createBody model.CreateBankTransactionActionBody
		createBody.ActionKey = fmt.Sprintf("CANCEL#%d", req.TransactionId)
		createBody.TransactionId = req.TransactionId
		createBody.UserId = record.UserId
		createBody.TransactionTypeId = record.TransactionTypeId
		createBody.FromAccountId = record.FromAccountId
		createBody.ToAccountId = record.ToAccountId
		createBody.JsonBefore = string(jsonBefore)
		createBody.TransferAt = &record.TransferAt
		createBody.CreditAmount = record.CreditAmount
		createBody.ConfirmedAt = req.ConfirmedAt
		createBody.ConfirmedByAdminId = req.ConfirmedByAdminId
		// BOF : transaction
		if _, err := s.repoBanking.CreateTransactionAction(createBody); err != nil {
			return nil, internalServerError(err)
		}
	} else {
		if err := s.repoBanking.UpdateTransactionIngnoreActionById(req.TransactionId); err != nil {
			return nil, internalServerError(err)
		}
	}
	return nil, nil
}

func (s *bankingService) UpdateIgnoreDeposit(req model.CreateIgnoredTransacionRequest) (*int64, error) {

	actionAtUtc := time.Now().UTC()

	record, err := s.repoBanking.GetDepositTransactionById(req.TransactionId)
	if err != nil {
		log.Println("UpdateIgnoreDeposit.GetDepositTransactionById.ERROR=", err)
		return nil, internalServerError(err)
	}

	admin, err := s.repoBanking.GetAdminById(*req.ConfirmedByAdminId)
	if err != nil {
		log.Println("UpdateIgnoreDeposit.GetAdminById.ERROR=", err)
		return nil, internalServerError(err)
	}

	var updateApprovedBy model.UpdateUserTransactionStatusRequest
	updateApprovedBy.Id = req.TransactionId
	updateApprovedBy.CanceledAt = actionAtUtc
	updateApprovedBy.CanceledByAdminId = *req.ConfirmedByAdminId
	updateApprovedBy.TransactionStatusId = model.TRANS_STATUS_DEPOSIT_IGNORE
	updateApprovedBy.CancelRemark = &req.CancelRemark
	updateApprovedBy.ConfirmedAt = &req.ConfirmedAt
	updateApprovedBy.ConfirmedByAdminId = req.ConfirmedByAdminId
	err = s.repoBanking.UpdateUserTransactionStatus(updateApprovedBy)
	if err != nil {
		log.Println("UpdateIgnoreDeposit.UpdateUserTransactionStatus.ERROR=", err)
		return nil, internalServerError(err)
	}

	var ignoreStatementBody model.BankStatementUpdateBody
	ignoreStatementBody.Id = record.StatementId
	ignoreStatementBody.StatementStatusId = model.STATEMENT_STATUS_IGNORED
	err = s.repoBanking.UpdateBankStatementStatus(ignoreStatementBody)
	if err != nil {
		log.Println("UpdateIgnoreDeposit.UpdateBankStatementStatus.ERROR=", err)
		return nil, internalServerError(err)
	}

	// CreateCanceledTransferDeposit
	var createConfirm model.CreateSuccessTransferWithdrawRequest
	createConfirm.TransactionId = req.TransactionId
	createConfirm.ConfirmedAt = time.Now()
	createConfirm.ConfirmedByAdminId = req.ConfirmedByAdminId
	if _, err := s.CreateCanceledTransferDeposit(createConfirm); err != nil {
		log.Println("UpdateIgnoreDeposit.CreateCanceledTransferDeposit.ERROR=", err)
		return nil, err
	}

	// ===========================================================
	// RemoveFrom BankPendingRecord -> รายการฝาก ยกเลิก เพิกเฉย รายการฝากที่เข้ามาด้วยเหตุผลต่างๆ
	dashboardRepo := repository.NewDashboardRepository(s.SharedDb)
	if err := RejectBankPendingRecordFromAny(dashboardRepo, record.TransactionTypeId, record.Id, actionAtUtc, admin.Fullname); err != nil {
		log.Println("UpdateIgnoreDeposit.RejectBankPendingRecordFromAny.ERROR=", err)
	}

	// here
	return nil, err
}

func (s *bankingService) UploadImageToCloudflareDepositSlip(imageFileBody *http.Request) (*model.FileUploadResponse, error) {

	fileReader, newFileName, err := imageFileBody.FormFile("file")
	if err != nil {
		log.Println("UploadImageToCloudflareDepositSlip.ERROR.FormFile", err)
		return nil, err
	}

	filename := &newFileName.Filename
	dbName := os.Getenv("DB_NAME")

	// [set imageCloudFlarePathName]
	pathName := fmt.Sprintf("cbgame/%v/banking/upload/deposit-slip/", dbName)
	// ส่ง Id กับไฟล์ reader
	var newImageId *model.FileUploadResponse
	fileData, err := s.repoBanking.UploadImageToCloudflare(pathName, *filename, fileReader)
	if err != nil {
		log.Println("UploadImageToCloudflareDepositSlip.ERROR.UploadImageToCloudflare", err)
		return nil, badRequest("UNABLE_TO_UPLOAD_FILE")
	}

	newImageId = &model.FileUploadResponse{
		ImageUrl: fileData.FileUrl,
	}
	return newImageId, nil
}
func (s *bankingService) UploadImageToCloudflareBonusSlip(imageFileBody *http.Request) (*model.FileUploadResponse, error) {

	fileReader, newFileName, err := imageFileBody.FormFile("file")
	if err != nil {
		log.Println("UploadImageToCloudflareBonusSlip.ERROR.FormFile", err)
		return nil, err
	}

	filename := &newFileName.Filename
	dbName := os.Getenv("DB_NAME")

	// [set imageCloudFlarePathName]
	pathName := fmt.Sprintf("cbgame/%v/banking/upload/bonus-slip/", dbName)
	// ส่ง Id กับไฟล์ reader
	var newImageId *model.FileUploadResponse
	fileData, err := s.repoBanking.UploadImageToCloudflare(pathName, *filename, fileReader)
	if err != nil {
		log.Println("UploadImageToCloudflareBonusSlip.ERROR.UploadImageToCloudflare", err)
		return nil, badRequest("UNABLE_TO_UPLOAD_FILE")
	}

	newImageId = &model.FileUploadResponse{
		ImageUrl: fileData.FileUrl,
	}
	return newImageId, nil
}

func (s *bankingService) UploadImageToCloudflareWithdrawSlip(imageFileBody *http.Request) (*model.FileUploadResponse, error) {

	fileReader, newFileName, err := imageFileBody.FormFile("file")
	if err != nil {
		log.Println("UploadImageToCloudflareWithdrawSlip.ERROR.FormFile", err)
		return nil, err
	}

	filename := &newFileName.Filename
	dbName := os.Getenv("DB_NAME")

	// [set imageCloudFlarePathName]
	pathName := fmt.Sprintf("cbgame/%v/banking/upload/withdraw-slip/", dbName)
	// ส่ง Id กับไฟล์ reader
	var newImageId *model.FileUploadResponse
	fileData, err := s.repoBanking.UploadImageToCloudflare(pathName, *filename, fileReader)
	if err != nil {
		log.Println("UploadImageToCloudflareWithdrawSlip.ERROR.UploadImageToCloudflare", err)
		return nil, badRequest("UNABLE_TO_UPLOAD_FILE")
	}

	newImageId = &model.FileUploadResponse{
		ImageUrl: fileData.FileUrl,
	}
	return newImageId, nil
}

func (s *bankingService) CreateDepositRecord(req model.CreateDepositRecordRequest) (*int64, error) {

	startProcess := time.Now()
	var transId *int64

	// [********]fastbank credit check balance to continue unuse func or not auto
	getWebLocal, _ := s.repoBanking.GetLocalWebInfo()
	if getWebLocal != nil {
		if getWebLocal.FastbankCreditBalance <= -1000 {
			log.Println("CreateDepositRecord.WEB_OUT_OF_CREDIT")
			return nil, badRequest("ยอดเงินเว็บไม่เพียงพอ")
		}
	}

	var user *model.UserBankDetailBody
	var err error
	if req.MemberCode != "" {
		user, err = s.repoBanking.GetUserBankDetailByMemberCode(req.MemberCode)
		if err != nil {
			log.Println("CreateDepositRecord.GetUserBankDetailByMemberCode", err)
			return nil, internalServerError(err)
		}
	} else {
		member, err := s.repoBanking.GetMemberById(req.UserId)
		if err != nil {
			return nil, internalServerError(err)
		}

		if member.MemberCode == "" {
			memberCode, err := s.serviceUser.GenUniqueUserMemberCode(member.Id)
			if err != nil {
				log.Println("CreateDepositRecord.GenUniqueUserMemberCode", err)
				return nil, internalServerError(err)
			}
			member.MemberCode = *memberCode

			// Affiliate + Alliance Income
			totalAmount := req.CreditAmount + req.BonusAmount
			if member.UserTypeName == "NONE" {
				// ถ้าฝากครั้งแรก จะได้เป็น AFFILIATE + REFER COMMISION
				if err := s.serviceAccounting.UserFirstDepositCommission(*member, totalAmount); err != nil {
					log.Println("CreateDepositRecord.UserFirstDepositCommission.ERROR", err)
					return nil, internalServerError(err)
				}
			} else if member.UserTypeName == "ALLIANCE" {
				// REFER COMMISION
				if err := s.serviceAl.NoUseAlUpdateCommission(member.Id, totalAmount); err != nil {
					log.Println("CreateDepositRecord.AlUpdateCommission.ERROR", err)
					return nil, internalServerError(err)
				}
			}
		}

		user, err = s.repoBanking.GetUserBankDetailByMemberCode(member.MemberCode)
		if err != nil {
			log.Println("CreateDepositRecord.GetUserBankDetailByMemberCode", err)
			return nil, internalServerError(err)
		}

	}

	if user == nil {
		return nil, badRequest("MEMBER_NOT_FOUND")
	}

	bankAccount, err := s.repoBanking.GetBankAccountById(req.ToAccountId)
	if err != nil {
		log.Println("CreateDepositRecord.GetBankAccountById", err, req.ToAccountId)
		return nil, internalServerError(err)
	}

	var checkDuplicate model.CheckDuplicateWebhookAndAdminRecord
	checkDuplicate.FromAccountNumber = req.FromAccountNumber
	checkDuplicate.FromBankId = user.BankId
	checkDuplicate.Amount = req.CreditAmount
	checkDuplicate.TransactionAt = req.TransferAt
	checkDuplicate.CheckFromWhere = "ADMIN"
	checkDuplicate.MemberCode = req.MemberCode
	checkDuplicate.ToBankId = &bankAccount.BankId
	duplicateFromAdminRecord, _ := s.repoAccounting.CheckDuplicateWebhookAndAdminRecord2(checkDuplicate)
	if duplicateFromAdminRecord.Id != 0 {
		statusLog := "SUCCESS"
		jsonPayLoad := fmt.Sprintf("DUPLICATE WITH ADMIN BANK TRANS ID: %v ", duplicateFromAdminRecord.Id)
		jsonRequest := helper.StructJson(req)
		logType := "DUPLICATE_ADMIN_WITH_WEBHOOK"
		var createLog model.BankTransactionLogCreateRequest
		createLog.Status = &statusLog
		createLog.JsonPayload = &jsonPayLoad
		createLog.JsonRequest = &jsonRequest
		createLog.LogType = &logType

		if _, err := s.serviceAccounting.CreateBankTransactionLog(createLog); err != nil {
			return nil, nil
		}

		log.Println("CreateDepositRecord.duplicateFromRecord", helper.StructJson(duplicateFromAdminRecord))
		return nil, badRequest("DUPLICATE_WITH_ADMIN_RECORD")
	}
	//[30/11/2023] ไม่ต้องดักเงื่อนไขนี้แล้ว
	// var minDeposit int64
	// config, err := s.repoBanking.GetConfiguration()
	// if err == nil {
	// 	if config.MinimumDeposit == 0 {
	// 		minDeposit = 100
	// 	} else {
	// 		minDeposit = config.MinimumDeposit
	// 	}
	// 	if minDeposit > int64(req.CreditAmount) {
	// 		return nil, badRequest("LESS_THAN_CONFIG")
	// 	}
	// }
	// race condition deposit
	actionAt := time.Now().UTC().Format("************")
	var createBody model.RaceActionCreateBody
	createBody.Name = "ADMIN_CREATE_DEPOSIT"
	createBody.JsonRequest = helper.StructJson(req)
	createBody.Status = "PENDING"
	createBody.ActionKey = fmt.Sprintf("DEPOSIT_T%s_U%d_CD%f", actionAt, user.Id, req.CreditAmount)
	createBody.UnlockAt = time.Now().UTC().Add(time.Second * 1)
	if _, err := s.repoBanking.GetRaceActionIdByActionKey(createBody.ActionKey); err != nil {
		log.Println("CreateBankStatementFromWebhookAndAuto.GetRaceActionIdByActionKey", err)
		if err != gorm.ErrRecordNotFound {
			return nil, internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
		}
	} else {
		return nil, internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
	}

	// create race condition
	actionId, err := s.repoBanking.CreateRaceCondition(createBody)
	if err != nil {
		log.Println("CreateBankStatementFromWebhookAndAuto.CreateRaceCondition", err)
		return nil, internalServerError(errors.New("CANNOT_CREATE_ACTION"))
	}

	if actionId != 0 {

		// create statement
		var createStatement model.BankStatementCreateBody
		createStatement.Amount = req.CreditAmount
		createStatement.AccountId = bankAccount.Id
		createStatement.Detail = req.FromAccountNumber + " " + req.BonusReason
		createStatement.FromBankId = user.BankId
		createStatement.FromAccountNumber = user.BankAccount
		createStatement.StatementTypeId = model.STATEMENT_TYPE_TRANSFER_IN
		createStatement.TransferAt = req.TransferAt
		createStatement.StatementStatusId = model.STATEMENT_STATUS_PENDING
		bankstatementId, err := s.repoBanking.CreateBankStatement(createStatement)
		if err != nil {
			log.Println("CreateDepositRecord.CreateBankStatement", err)
			return nil, internalServerError(err)
		}

		var externalNoti model.NotifyExternalNotificationRequest

		if req.Auto {
			var promotionWebUserId int64
			GetUserPromotion, _ := s.promotionWebService.GetDepositCurrentProcessingUserPromotion(user.Id)
			if GetUserPromotion != nil {
				promotionWebUserId = GetUserPromotion.Id
			}
			var createBankTransaction model.BankTransactionCreateBody
			createBankTransaction.MemberCode = user.MemberCode
			createBankTransaction.UserId = user.Id
			createBankTransaction.StatementId = bankstatementId
			createBankTransaction.TransactionTypeId = model.TRANSACTION_TYPE_DEPOSIT
			createBankTransaction.FromAccountNumber = &user.BankAccount
			createBankTransaction.PromotionId = &promotionWebUserId
			createBankTransaction.FromBankId = &user.BankId
			createBankTransaction.ToAccountId = &bankAccount.Id
			createBankTransaction.ToAccountNumber = &bankAccount.AccountNumber
			createBankTransaction.ToBankId = &bankAccount.BankId
			createBankTransaction.ToAccountNumber = &bankAccount.AccountNumber
			createBankTransaction.CreditAmount = req.CreditAmount
			createBankTransaction.BonusAmount = req.BonusAmount
			createBankTransaction.BonusReason = req.BonusReason
			createBankTransaction.DepositChannel = req.DepositChannel
			createBankTransaction.TransactionStatusId = model.TRANS_STATUS_PENDING
			createBankTransaction.CreatedByAdminId = req.CreateByAdminId
			createBankTransaction.SlipImgUrl = req.SlipImgUrl
			createBankTransaction.TransferAt = &req.TransferAt
			// createBankTransaction.IsAutoCredit = true แอดมินเป็นคนกด
			//2/10/66
			// create transaction
			transId, err = s.repoBanking.InsertBankTransaction(createBankTransaction)
			if err != nil {
				log.Println("CreateDepositRecord.InsertBankTransaction", err)
				return nil, internalServerError(err)
			}
			actionDateTime := time.Now()
			var actionStatement model.BankStatementMatchRequest
			actionStatement.StatementId = *bankstatementId
			actionStatement.UserId = user.Id
			actionStatement.ConfirmedAt = actionDateTime
			actionStatement.ConfirmedByAdminId = &req.CreateByAdminId
			if err := s.serviceAccounting.SetStatementOwnerMatched(*bankstatementId, actionStatement, model.USE_ENDING_NOTI); err != nil {
				log.Println("CreateDepositRecord.SetStatementOwnerMatched", err)
				return nil, internalServerError(err)
			}

			// DECREASE FASTBANK CREDIT 2.FROM create auto DEPOSIT
			if err := s.repoAccounting.DecreaseFastbankCredit(1); err != nil {
				log.Println("CreateDepositRecord.DECREASE_FASTBANK_CREDIT_ERROR", err)
			}

			var confirmDeposit model.BankConfirmDepositRequest
			confirmDeposit.TransferAt = &req.TransferAt
			confirmDeposit.BonusAmount = &req.BonusAmount
			confirmDeposit.ConfirmedAt = actionDateTime
			confirmDeposit.ConfirmedByAdminId = &req.CreateByAdminId
			if err := s.ConfirmDepositTransaction(*transId, confirmDeposit); err != nil {
				log.Println("CreateDepositRecord.ConfirmDepositTransaction", err)
				return nil, internalServerError(err)
			}
			// [promotion]
			var userCreditReq model.UserTransactionCreateRequest
			userCreditReq.UserId = user.Id
			userCreditReq.RefId = transId
			userCreditReq.TransferAt = &req.TransferAt
			userCreditReq.PromotionId = &promotionWebUserId
			userCreditReq.TypeId = model.CREDIT_TYPE_DEPOSIT
			userCreditReq.AccountId = &bankAccount.Id
			userCreditReq.Detail = fmt.Sprintf("%s:%s => %s:%s", user.BankName, user.BankAccount, bankAccount.BankName, bankAccount.AccountName)
			userCreditReq.Amount = req.CreditAmount
			userCreditReq.BonusAmount = req.BonusAmount
			userCreditReq.CreateBy = &req.CreateByAdminId
			userCreditReq.ConfirmBy = &req.CreateByAdminId
			userCreditReq.StartWorkAt = actionDateTime
			if agentResp, err := s.repoAccounting.IncreaseUserCredit(userCreditReq); err != nil {
				if bankstatementId != nil {
					var updateStatement model.BankStatementUpdateBody
					updateStatement.Id = *bankstatementId
					updateStatement.StatementStatusId = model.STATEMENT_STATUS_PENDING
					if err := s.repoAccounting.UpdateBankStatementStatus(updateStatement); err != nil {
						log.Println("CreateDepositRecord.UpdateBankStatementStatus", err)
					}
				}
				log.Println("CreateDepositRecord.IncreaseUserCredit", err)
				return nil, internalServerError(err)
			} else {
				// FASTBANK_SUCCESS
				if err := s.repoAccounting.UpdateDepositTransactionStatusFromAgent(*transId, *agentResp); err != nil {
					log.Println("CreateDepositRecord.UpdateDeporsitTransactionStatusFromAgent", err)
				}
				// Set FastBank-READ ** ไม่มี เพราะเป็นการสร้างรายการฝากเอง
			}

			isFirstDeposit := s.repoBanking.IsFirstDeposit(user.Id)
			if isFirstDeposit {
				var createFirstDeposit model.UserFirstDepositCreateBody
				createFirstDeposit.UserId = user.Id
				createFirstDeposit.TransferAt = req.TransferAt
				createFirstDeposit.Amount = req.CreditAmount
				createFirstDeposit.Remark = "CreateDepositRecord"
				if _, err := s.repoBanking.SetUserFirstDeposit(createFirstDeposit, transId); err != nil {
					log.Println("CreateDepositRecord.SetUserFirstDeposit", err)
				}
			}

			// [Lucky Wheel] ตอนฝากละได้โบนัส
			var luckyWheelBody model.ActivityLuckyWheelRoundUserRequest
			luckyWheelBody.UserId = user.Id
			luckyWheelBody.ConditionId = model.ACTIVITY_LUCKY_WHEEL_SETTING_CONDITION_DEPOSIT
			luckyWheelBody.ConditionAmount = req.CreditAmount
			if err := s.activityLuckyWheelService.CreateRoundActivityLuckyWheel(luckyWheelBody); err != nil {
				log.Println("CreateDepositRecord.CreateRoundActivityLuckyWheel", err)
			}

			// [TIER]
			if err := s.repoAccounting.IncreaseUserTierDepositAmount(user.Id, req.CreditAmount); err != nil {
				log.Println("CreateDepositRecord.IncreaseUserTierDepositAmount", err)
			}

			var checkUserPromotionBody model.CheckUserPromotionBody
			checkUserPromotionBody.UserId = user.Id
			_, err = s.promotionWebService.CheckUserPromotion(checkUserPromotionBody)
			if err != nil {
				log.Println("CreateDepositRecord.CheckUserPromotion", err)
			}

			// [turnOver] ถ้ามีการฝากเงิน ให้สร้าง TurnOver
			if err := CreateTurnOverFromSuccessDeposit(repository.NewTurnoverRepository(s.SharedDb), user.Id, req.CreditAmount, *transId); err != nil {
				log.Println("CreateDepositRecord.CreateTurnOverFromSuccessDeposit", err)
			}

			// AUTO ไม่ต้องสร้าง AddTo BankPendingRecord -> รายการฝาก
			dashboardRepo := repository.NewDashboardRepository(s.SharedDb)
			if err := AddBankPendingRecordFromDeposit(dashboardRepo, *transId); err != nil {
				log.Println("CreateDepositRecord.AddBankPendingRecordFromDeposit", err)
			}

			// [line notify]
			externalNoti.TypeNotify = model.IsDepositAfterCredit
			externalNoti.TransId = transId
			externalNoti.Amount = req.CreditAmount
			externalNoti.MemberCode = user.MemberCode
			externalNoti.BonusCredit = &req.BonusAmount
			externalNoti.UserCredit = user.Credit + req.CreditAmount
			externalNoti.ConfirmedByAdminId = req.CreateByAdminId
			externalNoti.TransferDateTime = req.TransferAt.Add(7 * time.Hour).Format("2006-01-02 15:04:05")
			externalNoti.ActionTime = time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02 15:04:05")

		} else {
			var createBankTransaction model.BankTransactionCreateBody
			createBankTransaction.MemberCode = user.MemberCode
			createBankTransaction.UserId = user.Id
			createBankTransaction.StatementId = bankstatementId
			createBankTransaction.TransactionTypeId = model.TRANSACTION_TYPE_DEPOSIT
			createBankTransaction.FromBankId = &user.BankId
			createBankTransaction.FromAccountNumber = &user.BankAccount
			createBankTransaction.ToAccountId = &bankAccount.Id
			createBankTransaction.ToAccountNumber = &bankAccount.AccountNumber
			createBankTransaction.ToBankId = &bankAccount.BankId
			createBankTransaction.ToAccountNumber = &bankAccount.AccountNumber
			createBankTransaction.CreditAmount = req.CreditAmount
			createBankTransaction.BonusAmount = req.BonusAmount
			createBankTransaction.BonusReason = req.BonusReason
			createBankTransaction.DepositChannel = req.DepositChannel
			createBankTransaction.TransactionStatusId = model.TRANS_STATUS_PENDING
			createBankTransaction.SlipImgUrl = req.SlipImgUrl
			createBankTransaction.CreatedByAdminId = req.CreateByAdminId
			createBankTransaction.TransferAt = &req.TransferAt
			//2/10/66 || // 25/12/2023 ต้องการ แจ้ง เตือนทั้ง 2 แบบ เหมือนเดิม
			// create transaction
			transId, err = s.repoBanking.InsertBankTransaction(createBankTransaction)
			if err != nil {
				log.Println("CreateDepositRecord.InsertBankTransaction", err)
				return nil, internalServerError(err)
			}

			if transId != nil {
				// AddTo BankPendingRecord -> รายการฝาก
				dashboardRepo := repository.NewDashboardRepository(s.SharedDb)
				if err := AddBankPendingRecordFromDeposit(dashboardRepo, *transId); err != nil {
					log.Println("CreateDepositRecord.AddBankPendingRecordFromDeposit", err)
				}
			}

			externalNoti.TypeNotify = model.IsDepositBeforeCredit
			externalNoti.TransId = transId
			externalNoti.Amount = req.CreditAmount
			externalNoti.MemberCode = user.MemberCode
			externalNoti.BonusCredit = &req.BonusAmount
			externalNoti.UserCredit = user.Credit + req.CreditAmount
			externalNoti.ConfirmedByAdminId = req.CreateByAdminId
		}

		// [Line Notify]
		endTime := time.Now()
		elapsed := endTime.Sub(startProcess)
		elapsedSeconds := elapsed.Seconds()
		timeElapsed := fmt.Sprintf("%.2f", elapsedSeconds)
		externalNoti.TimerCounter = timeElapsed
		// UpdateAutoProcessTimer(timer string, id int64) error
		if err := s.repoBanking.UpdateAutoProcessTimer(timeElapsed, *transId); err != nil {
			return nil, nil
		}
		if err := s.serviceNoti.ExternalNotification(externalNoti); err != nil {
			log.Println("FailedNotify", err)
		}
	}
	return transId, nil
}

func (s *bankingService) CreateFreeBonus(req model.CreateFreeBonusRequest) (*int64, error) {

	user, err := s.repoBanking.GetUserBankDetailByMemberCode(req.MemberCode)
	if err != nil {
		return nil, internalServerError(err)
	}

	// create transaction
	var createBankTransaction model.BankTransactionCreateBody
	createBankTransaction.MemberCode = user.MemberCode
	createBankTransaction.UserId = user.Id
	createBankTransaction.TransactionTypeId = model.TRANSACTION_TYPE_BONUS
	createBankTransaction.BonusAmount = req.BonusAmount
	createBankTransaction.BonusReason = req.BonusReason
	createBankTransaction.TransactionStatusId = model.TRANS_STATUS_PENDING
	createBankTransaction.TransferAt = &req.TransferAt
	createBankTransaction.CreatedByAdminId = *req.ConfirmedByAdminId
	createBankTransaction.SlipImgUrl = req.SlipImgUrl
	//2/10/66
	// create transaction
	transId, err := s.repoBanking.InsertBankTransaction(createBankTransaction)
	if err != nil {
		log.Println("CreateFreeBonus.InsertBankTransaction.ERROR=", err)
		return nil, internalServerError(err)
	}

	// confirm by
	var confirmDeposit model.BankConfirmDepositRequest
	confirmDeposit.TransferAt = &req.TransferAt
	confirmDeposit.BonusAmount = &req.BonusAmount
	confirmDeposit.ConfirmedAt = time.Now()
	confirmDeposit.ConfirmedByAdminId = req.ConfirmedByAdminId
	if err := s.ConfirmDepositTransaction(*transId, confirmDeposit); err != nil {
		return nil, internalServerError(err)
	}

	// gameRes, err := s.repoAgentConnect.DepositAgent(user.MemberCode, user.Id, req.BonusAmount, *transId)
	// if err != nil {
	// 	return nil, internalServerError(err)
	// }
	var userCreditReq model.UserTransactionCreateRequest
	userCreditReq.UserId = user.Id
	userCreditReq.TransferAt = &req.TransferAt
	userCreditReq.RefId = transId
	userCreditReq.TypeId = model.CREDIT_TYPE_BONUS
	userCreditReq.BonusAmount = req.BonusAmount
	userCreditReq.CreateBy = req.ConfirmedByAdminId
	userCreditReq.ConfirmBy = req.ConfirmedByAdminId
	userCreditReq.Detail = req.BonusReason
	if agentResp, err := s.repoAccounting.IncreaseUserCredit(userCreditReq); err != nil {
		log.Println("CreateFreeBonus.IncreaseUserCredit", err)
		return nil, internalServerError(err)
	} else {
		if err := s.repoAccounting.UpdateDepositTransactionStatusFromAgent(*transId, *agentResp); err != nil {
			log.Println(err)
		}
	}

	//turn
	if err := s.CreateTurnOverFromBonus(user.Id, req.BonusAmount, *transId); err != nil {
		log.Println("CreateFreeBonus.CreateTurnOverFromBonus", err)
	}

	var updateApprovedBy model.UpdateConfirmAutoWithdrawBody
	updateApprovedBy.ConfirmedAt = req.ConfirmedAt
	updateApprovedBy.ConfirmedByAdminId = req.ConfirmedByAdminId

	err = s.repoBanking.UpdateAdminAndTransactionStatus(*transId, updateApprovedBy)
	if err != nil {
		return nil, internalServerError(err)
	}

	var externalNoti model.NotifyExternalNotificationRequest
	externalNoti.TypeNotify = model.BounsNotification
	externalNoti.TransId = transId
	externalNoti.MemberCode = user.MemberCode
	externalNoti.BonusCredit = &req.BonusAmount
	externalNoti.UserCredit = user.Credit + req.BonusAmount
	externalNoti.ConfirmedByAdminId = *req.ConfirmedByAdminId
	if err := s.serviceNoti.ExternalNotification(externalNoti); err != nil {
		log.Println("FailedNotify", err)
	}

	return nil, nil
}

func (s *bankingService) CreateWithdrawRecord(req model.CreateWithdrawRecordRequest) (*model.FastbankWithdrawTransactionResponse, error) {

	startProcess := time.Now()
	// USER
	user, err := s.repoBanking.GetUserBankDetailByMemberCode(req.MemberCode)
	if err != nil {
		return nil, internalServerError(err)
	}
	// TURN_OVER
	turnOver, err := s.serviceTurnover.CheckUserTurnover(user.Id)
	if err != nil {
		return nil, err
	}
	if turnOver.IsHasTurnover {
		return nil, badRequest("ไม่สามารถถอนได้ กรุณาตรวจสอบยอดเทิร์นโอเวอร์ของท่าน")
	}
	// auto updated
	checkerIsAuto, err := s.serviceAccounting.BankAccountWithDrawValidation(req.ToBankId, req.Amount)
	if err != nil {
		return nil, badRequest("ไม่สามารถถอนได้ กรุณาตรวจสอบเลขบัญชีธนาคารของท่าน")
	}

	// if checkerIsAuto.Status != model.AUTO_WITHDRAW {
	// 	return nil, badRequest("ไม่สามารถถอนได้ กรุณาตรวจสอบเลขบัญชีธนาคารของท่าน")
	// }

	// duplicate transaction [********] Dunk ไม่ให้ทำรายการยอดเงินเดิมซ้ำ + [********] ไม่เอา flow ที่เช็คจากรายการในดำเนินการ
	// CheckDuplicateWithdraw
	var checkRequest model.CheckDuplicateWithdrawRequest
	checkRequest.UserId = user.Id
	checkRequest.Amount = req.Amount
	duplicateWithdrawTrans, _ := s.repoBanking.CheckDuplicateWithdraw(checkRequest)
	if duplicateWithdrawTrans != nil {
		statusLog := "SUCCESS"
		jsonPayLoad := fmt.Sprintf("ADMIN_TRY_TO_CREATE_DUPLICATE_WITHDRAW_TRANSACTION:%v", duplicateWithdrawTrans.Id)
		jsonRequest := helper.StructJson(checkRequest)
		logType := "DUPLICATE_USER_TRANSACTION"
		var createLog model.BankTransactionLogCreateRequest
		createLog.Status = &statusLog
		createLog.JsonPayload = &jsonPayLoad
		createLog.JsonRequest = &jsonRequest
		createLog.LogType = &logType

		if _, err := s.serviceAccounting.CreateBankTransactionLog(createLog); err != nil {
			return nil, nil
		}
		return nil, badRequest("DUPLICATE_USER_TRANSACTION")
	}

	if checkerIsAuto.BankAccountId == 0 {
		statusLog := "SUCCESS"
		jsonPayLoad := helper.StructJson(checkerIsAuto)
		jsonRequest := helper.StructJson(checkRequest)
		logType := "ADMIN_VALIDATE_USER_WITHDRAW"
		var createLog model.BankTransactionLogCreateRequest
		createLog.Status = &statusLog
		createLog.JsonPayload = &jsonPayLoad
		createLog.JsonRequest = &jsonRequest
		createLog.LogType = &logType
		if _, err := s.serviceAccounting.CreateBankTransactionLog(createLog); err != nil {
			return nil, nil
		}
	}

	// [******** Check Confirm Key] กัน แอดมินกดซ้ำกันเอง(พี่มิงค์)
	confirmTimeNow := time.Now()
	confirmKey := fmt.Sprintf("%v%v", confirmTimeNow.Format("************"), user.Id)
	checkFromConfirmKey, _ := s.repoBanking.CheckWithdrawConfirmConfirmKey(confirmKey)
	if checkFromConfirmKey != nil && checkFromConfirmKey.Id > 0 {
		log.Println("CreateWithdrawRecord.CheckWithdrawConfirmConfirmKey", err)
		return nil, badRequest("มีรายการถอนในช่วงเวลานี้แล้ว")
	}
	var createConfirmWithdraw model.CreateWithdrawConfirmBody
	createConfirmWithdraw.UserId = user.Id
	createConfirmWithdraw.ConfirmKey = confirmKey
	insertConfirmWithdraw, err := s.repoBanking.CreateWithdrawConfirm(createConfirmWithdraw)
	if err != nil {
		log.Println("CreateWithdrawRecord.CreateWithdrawConfirm", err)
		return nil, badRequest("มีรายการถอนในช่วงเวลานี้แล้ว")
	}
	if insertConfirmWithdraw == 0 {
		log.Println("CreateWithdrawRecord.CreateWithdrawConfirm", err)
		return nil, badRequest("มีรายการถอนในช่วงเวลานี้แล้ว")
	}

	// SYSTEM_BANK_ACCOUNT
	bankAccount, err := s.repoBanking.GetBankAccountById(req.ToBankId)
	if err != nil {
		return nil, internalServerError(err)
	}

	// [********] true not allow decimal with scb
	if user.BankId == model.BANK_ID_TRUE && bankAccount.BankId == model.BANK_ID_SCB {
		if req.Amount != float64(int(req.Amount)) {
			return nil, badRequest("WITHDRAW_AMOUNT_NOT_ALLOW_DECIMAL")
		}
	}
	// [CURRENT_USER_CREDIT] get latest credit from Agent
	userCreditInfo, err := s.serviceUser.GetUser(user.Id)
	if err != nil {
		log.Println("CreateWithdrawRecord.GetUser", err)
		return nil, badRequest("ยอดเงินในเกมไม่พอ")
	}
	if userCreditInfo.Credit < req.Amount {
		return nil, badRequest("ยอดเงินในเกมไม่พอ")
	}

	// ----- READY TO CREATE TRANSACTION -----
	transferAt := time.Now()
	var createBankTransaction model.BankTransactionCreateBody
	createBankTransaction.MemberCode = user.MemberCode
	createBankTransaction.UserId = user.Id
	createBankTransaction.TransactionTypeId = model.TRANSACTION_TYPE_WITHDRAW
	createBankTransaction.FromAccountId = &bankAccount.Id
	createBankTransaction.FromBankId = &bankAccount.BankId
	createBankTransaction.FromAccountName = &bankAccount.AccountName
	createBankTransaction.FromAccountNumber = &bankAccount.AccountNumber
	createBankTransaction.ToBankId = &user.BankId
	createBankTransaction.ToAccountName = &user.Fullname
	createBankTransaction.ToAccountNumber = &user.BankAccount
	createBankTransaction.CreatedByAdminId = *req.ConfirmedByAdminId
	createBankTransaction.TransactionStatusId = model.TRANS_STATUS_WITHDRAW_PENDING
	createBankTransaction.CreditAmount = float64(req.Amount)
	createBankTransaction.CreditBack = 0
	createBankTransaction.OverAmount = 0
	createBankTransaction.BonusAmount = 0
	createBankTransaction.BeforeAmount = 0
	createBankTransaction.AfterAmount = 0
	createBankTransaction.TransferAt = &transferAt
	createBankTransaction.CreatedByAdminId = *req.ConfirmedByAdminId
	transId, err := s.repoBanking.InsertBankTransaction(createBankTransaction)
	if err != nil {
		log.Println("CreateWithdrawRecord.InsertBankTransaction.ERROR=", err)
		return nil, internalServerError(err)
	}
	// [promotion]
	var promotionWebUserId int64
	GetUserPromotion, _ := s.promotionWebService.GetWithdrawCurrentProcessingUserPromotion(user.Id)
	if GetUserPromotion != nil {
		promotionWebUserId = GetUserPromotion.Id

		errUpdate := s.repoBanking.UpdatePromotionToBankTransaction(*transId, promotionWebUserId)
		if errUpdate != nil {
			log.Println("CreateWithdrawRecord.UpdatePromotionToBankTransaction", err)
		}
	}
	// DECREASE AGENT CREDIT
	var userCreditReq model.UserTransactionCreateRequest
	userCreditReq.UserId = user.Id
	userCreditReq.PromotionId = &promotionWebUserId
	userCreditReq.TypeId = model.CREDIT_TYPE_WITHDRAW
	userCreditReq.AccountId = &bankAccount.Id
	userCreditReq.Detail = fmt.Sprintf("%s:%s => %s:%s", bankAccount.BankName, bankAccount.AccountNumber, user.BankName, user.Fullname)
	userCreditReq.Amount = req.Amount
	userCreditReq.StartWorkAt = req.ConfirmedAt // เริ่มนับตอนกดขอถอน
	userCreditReq.RefId = transId
	isShow := false
	userCreditReq.IsShow = &isShow
	userCreditReq.CreateBy = req.ConfirmedByAdminId
	userCreditReq.ConfirmBy = req.ConfirmedByAdminId
	agentResp, err := s.repoAccounting.DecreaseUserCredit(userCreditReq)
	if err != nil {
		// [********] Confirm from P.layer if error from agent will be in failed
		log.Println("CreateWithdrawRecord.DecreaseUserCredit", err)
		var updateAgentError model.UpdateConfirmAutoWithdrawBody
		statusError := model.TRANS_STATUS_WITHDRAW_REJECTED
		confirmedByAuto := int64(0)
		updateAgentError.TransactionStatusId = &statusError
		updateAgentError.ConfirmedByAdminId = &confirmedByAuto
		if err := s.repoBanking.UpdateAdminAndTransactionStatus(*transId, updateAgentError); err != nil {
			return nil, internalServerError(err)
		}
		return nil, internalServerError(err)
	}
	if err := s.repoAccounting.UpdateWithdrawTransactionStatusFromAgent(*transId, *agentResp); err != nil {
		log.Println(err)
	}

	var statusId int64
	var externalNoti model.NotifyExternalNotificationRequest
	var responseFastbank model.FastbankWithdrawTransactionResponse
	approvedTime := time.Now().UTC()
	responseFastbank.Id = *transId
	if checkerIsAuto.Status == model.AUTO_WITHDRAW {

		if agentResp.AgentSuccess {
			var transferBody model.WithdrawTransferFastBankBody
			if req.Amount == float64(int(req.Amount)) {
				// If the amount has no decimal part, convert it to two decimal places
				transferBody.Amount = strconv.FormatFloat(req.Amount, 'f', 2, 64)
			} else {
				// If the amount already has decimals, leave it unchanged
				transferBody.Amount = strconv.FormatFloat(req.Amount, 'f', -1, 64)
			}
			// transferBody.Amount = strconv.FormatFloat(float64(req.Amount), 'E', -1, 64)
			transferBody.AccountFrom = bankAccount.AccountNumber
			transferBody.AccountTo = user.BankAccount
			transferBody.BankCode = user.BankCode
			transferBody.Pin = bankAccount.PinCode

			//7.4 โอนเงิน fastbank
			fastbank, err := s.repoBanking.WithdrawWithFastBank(transferBody)
			statusLog := "SUCCESS"
			jsonPayLoad := fmt.Sprintf("WITHDRAW_TO_FROM_FASTBANK: %s FASTBANK RES :%s, ERROR :%s", helper.StructJson(transferBody), helper.StructJson(fastbank), err)
			jsonRequest := fmt.Sprintf(" BANK_TRANS_ID %v ", *transId)
			logType := "CreateWithdrawRecord"
			var createLog model.BankTransactionLogCreateRequest
			createLog.Status = &statusLog
			createLog.JsonPayload = &jsonPayLoad
			createLog.JsonRequest = &jsonRequest
			createLog.LogType = &logType
			if _, err := s.serviceAccounting.CreateBankTransactionLog(createLog); err != nil {
				log.Println("CreateWithdrawRecord.CreateBankTransactionLog", err)
			}

			if err == nil && fastbank.Status.Code != 500 {
				responseFastbank.Message = "Created success"
				responseFastbank.Status = 200
				statusId = model.TRANS_STATUS_WITHDRAW_SUCCESS
				externalNoti.TypeNotify = model.IsWithdrawalCreditSuccess
				externalNoti.TransferDateTime = time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02 15:04:05")
				externalNoti.ActionTime = time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02 15:04:05")
				// CreateSuccessTransferWithdraw(transId int64) (*int64, error)
				var createConfirm model.CreateSuccessTransferWithdrawRequest
				createConfirm.TransactionId = *transId
				createConfirm.ConfirmedAt = req.ConfirmedAt
				createConfirm.ConfirmedByAdminId = req.ConfirmedByAdminId
				if _, err := s.CreateSuccessTransferWithdraw(createConfirm); err != nil {
					return nil, nil
				}

				// Show Withdraw USER_TRANSACTION
				var showUserTrans model.UserTransactionShowUpdate
				showUserTrans.TransactionId = createConfirm.TransactionId
				showUserTrans.ConfirmedAt = &createConfirm.ConfirmedAt
				showUserTrans.ConfirmAdminId = createConfirm.ConfirmedByAdminId
				showUserTrans.TransferAt = &approvedTime
				if err := s.repoAccounting.ShowUserTransaction(showUserTrans); err != nil {
					return nil, nil
				}

				// DECREASE FASTBANK CREDIT
				if err := s.repoAccounting.DecreaseFastbankCredit(1); err != nil {
					log.Println("CreateWithdrawRecord.DECREASE_FASTBANK_CREDIT_ERROR", err)
				}

				// Create detail
				var createBankTransactionExternalDetail model.BankTransactionExternalDetailCreateRequest
				createBankTransactionExternalDetail.BankTransactionId = *transId
				createBankTransactionExternalDetail.Detail = "SUCCESS"
				createBankTransactionExternalDetail.ErrorCode = 0
				if _, err := s.repoBanking.CreateBankTransactionExternalDetail(createBankTransactionExternalDetail); err != nil {
					return nil, nil
				}

			} else {
				if fastbank != nil {
					responseFastbank.Status = int64(fastbank.Status.Code)
					if fastbank.Status.Description != "" {
						responseFastbank.Message = fastbank.Status.Description
					} else {
						responseFastbank.Message = fmt.Sprintf("Error: %s", err)
					}
				} else {
					responseFastbank.Status = 0
					responseFastbank.Message = fmt.Sprintf("Error: %s", err)
				}

				statusId = model.TRANS_STATUS_WITHDRAW_UNSURE
				externalNoti.TypeNotify = model.IsWithdrawalCreditFailed

				// Create detail
				var createBankTransactionExternalDetail model.BankTransactionExternalDetailCreateRequest
				createBankTransactionExternalDetail.BankTransactionId = *transId
				createBankTransactionExternalDetail.Detail = responseFastbank.Message
				createBankTransactionExternalDetail.ErrorCode = responseFastbank.Status
				if _, err := s.repoBanking.CreateBankTransactionExternalDetail(createBankTransactionExternalDetail); err != nil {
					return nil, nil
				}

			}
		}
	} else if checkerIsAuto.Status == model.OUT_OF_CONFIG_AMOUNT {
		responseFastbank.Message = "WITHDRAW_OVER_MAX"
		responseFastbank.Status = 500
		statusId = model.TRANS_STATUS_WITHDRAW_OVER_MAX
		externalNoti.TypeNotify = model.IsWithdrawalAwaitingTransfer

	} else {
		responseFastbank.Message = "WITHDRAW_OVER_BUDGET"
		responseFastbank.Status = 500
		statusId = model.TRANS_STATUS_WITHDRAW_OVER_BUDGET
		externalNoti.TypeNotify = model.IsWithdrawalAwaitingTransfer

	}

	// 8.update transaction status
	var updateApprovedBy model.UpdateConfirmAutoWithdrawBody
	updateApprovedBy.TransactionStatusId = &statusId
	updateApprovedBy.ConfirmedAt = req.ConfirmedAt
	updateApprovedBy.ConfirmedByAdminId = req.ConfirmedByAdminId
	if statusId == model.TRANS_STATUS_WITHDRAW_SUCCESS {
		updateApprovedBy.TransferAt = &approvedTime
	}
	err = s.repoBanking.UpdateAdminAndTransactionStatus(*transId, updateApprovedBy)
	if err != nil {
		return nil, internalServerError(err)
	}

	if transId != nil {
		// AddTo BankPendingRecord -> รายการถอน
		dashboardRepo := repository.NewDashboardRepository(s.SharedDb)
		if err := AddBankPendingRecordFromWithdraw(dashboardRepo, *transId); err != nil {
			log.Println("UserCreateWithdrawTransaction.AddBankPendingRecordFromWithdraw", err)
		}
	}

	endProcess := time.Now()
	elapsed := endProcess.Sub(startProcess)
	elapsedSeconds := elapsed.Seconds()
	timeElapsed := fmt.Sprintf("%.2f", elapsedSeconds)

	externalNoti.TransId = transId
	externalNoti.Amount = req.Amount
	externalNoti.MemberCode = user.MemberCode
	externalNoti.UserCredit = agentResp.AgentAfterAmount
	externalNoti.ConfirmedByAdminId = *req.ConfirmedByAdminId
	externalNoti.TimerCounter = timeElapsed

	externalNoti.WebScoket.UserID = user.Id
	externalNoti.WebScoket.Amount = req.Amount
	externalNoti.WebScoket.MemberCode = user.MemberCode
	externalNoti.WebScoket.AlertType = "WITHDRAW"
	if err := s.serviceNoti.ExternalNotification(externalNoti); err != nil {
		log.Println("FailedNotify", err)
	}

	return &responseFastbank, nil
}

func (s *bankingService) CreateWithdrawPullCreditBack(req model.CreateWithdrawPullCreditBackRequest) (*int64, error) {

	user, err := s.repoBanking.GetUserBankDetailByMemberCode(req.MemberCode)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, badRequest("USER_NOT_FOUND")
		}
		return nil, internalServerError(err)
	}

	// [CURRENT_USER_CREDIT] get latest credit from Agent
	userCreditInfo, err := s.serviceUser.GetUser(user.Id)
	if err != nil {
		log.Println("CreateWithdrawPullCreditBack.GetUser", err)
		return nil, badRequest("ยอดเงินในเกมไม่พอ")
	}
	if userCreditInfo.Credit < req.Amount {
		return nil, badRequest("ยอดเงินในเกมไม่พอ")
	}

	var createBankTransaction model.BankTransactionCreateBody
	createBankTransaction.MemberCode = user.MemberCode
	createBankTransaction.UserId = user.Id
	createBankTransaction.TransactionTypeId = model.TRANSACTION_TYPE_CREDITBACK
	createBankTransaction.CreditBack = req.Amount
	createBankTransaction.TransactionStatusId = model.TRANS_STATUS_WITHDRAW_PENDING
	createBankTransaction.TransferAt = &req.ConfirmedAt
	createBankTransaction.CreatedByAdminId = *req.ConfirmedByAdminId
	createBankTransaction.BonusReason = req.Remark // ใช้ช่องนี้ในการแจ้งเหตุผล

	//2/10/66
	// create transaction
	transId, err := s.repoBanking.InsertBankTransaction(createBankTransaction)
	if err != nil {
		log.Println("CreateWithdrawPullCreditBack.InsertBankTransaction", err)
		return nil, internalServerError(err)
	}

	// gameRes, err := s.repoAgentConnect.WithdrawAgent(user.MemberCode, user.Id, req.Amount, *transId)
	// if err != nil {
	// 	return nil, internalServerError(err)
	// }
	var userCreditReq model.UserTransactionCreateRequest
	userCreditReq.RefId = transId
	userCreditReq.UserId = user.Id
	userCreditReq.TypeId = model.CREDIT_TYPE_TAKE_CREDIT_BACK
	userCreditReq.Amount = req.Amount
	userCreditReq.CreateBy = req.ConfirmedByAdminId
	userCreditReq.ConfirmBy = req.ConfirmedByAdminId
	userCreditReq.StartWorkAt = req.ConfirmedAt // เริ่มนับตอนกดคอนเฟิม
	userCreditReq.Detail = req.Remark
	agentResp, err := s.repoAccounting.DecreaseUserCredit(userCreditReq)
	if err != nil {
		// [********] Confirm from P.layer if error from agent will be in failed
		log.Println("CreateWithdrawPullCreditBack.DecreaseUserCredit", err)
		var updateAgentError model.UpdateConfirmAutoWithdrawBody
		statusError := model.TRANS_STATUS_WITHDRAW_REJECTED
		confirmedByAuto := int64(0)
		updateAgentError.TransactionStatusId = &statusError
		updateAgentError.ConfirmedByAdminId = &confirmedByAuto
		if err := s.repoBanking.UpdateAdminAndTransactionStatus(*transId, updateAgentError); err != nil {
			return nil, internalServerError(err)
		}
		return nil, internalServerError(err)
	} else {
		if err := s.repoBanking.UpdateWithdrawTransactionSuccessPullCreditBack(*transId, *agentResp); err != nil {
			log.Println(err)
		}
	}

	// CreateSuccessTransferWithdraw
	var createConfirm model.CreateSuccessTransferWithdrawRequest
	createConfirm.TransactionId = *transId
	createConfirm.ConfirmedAt = req.ConfirmedAt
	createConfirm.ConfirmedByAdminId = req.ConfirmedByAdminId
	if _, err = s.CreateSuccessTransferWithdraw(createConfirm); err != nil {
		return nil, internalServerError(err)
	}

	// update bank_transaction confirmed by
	var updateApprovedBy model.UpdateConfirmAutoWithdrawBody
	updateApprovedBy.ConfirmedAt = req.ConfirmedAt
	updateApprovedBy.ConfirmedByAdminId = req.ConfirmedByAdminId
	if err = s.repoBanking.UpdateAdminAndTransactionStatus(*transId, updateApprovedBy); err != nil {
		return nil, internalServerError(err)
	}

	var externalNoti model.NotifyExternalNotificationRequest
	externalNoti.TypeNotify = model.PullCreditBackNotification
	externalNoti.TransId = transId
	externalNoti.MemberCode = user.MemberCode
	externalNoti.Amount = req.Amount
	externalNoti.UserCredit = agentResp.AgentAfterAmount
	externalNoti.ConfirmedByAdminId = *req.ConfirmedByAdminId
	if err := s.serviceNoti.ExternalNotification(externalNoti); err != nil {
		log.Println("FailedNotify", err)
	}

	return nil, nil
}

func (s *bankingService) CreateUserCancelCredit(req model.CreateUserCancelCreditRequest) (*int64, error) {

	user, err := s.repoBanking.GetUserBankDetailByMemberCode(req.MemberCode)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, badRequest("USER_NOT_FOUND")
		}
		return nil, internalServerError(err)
	}

	// [CURRENT_USER_CREDIT] get latest credit from Agent
	userCreditInfo, err := s.serviceUser.GetUser(user.Id)
	if err != nil {
		log.Println("CreateUserCancelCredit.GetUser", err)
		return nil, badRequest("ยอดเงินในเกมไม่พอ")
	}
	if userCreditInfo.Credit < req.Amount {
		return nil, badRequest("ยอดเงินในเกมไม่พอ")
	}

	var createBankTransaction model.BankTransactionCreateBody
	createBankTransaction.MemberCode = user.MemberCode
	createBankTransaction.UserId = user.Id
	createBankTransaction.TransactionTypeId = model.TRANSACTION_TYPE_CREDITCANCEL
	createBankTransaction.CreditBack = req.Amount
	createBankTransaction.TransactionStatusId = model.TRANS_STATUS_WITHDRAW_PENDING
	createBankTransaction.TransferAt = &req.ConfirmedAt
	createBankTransaction.CreatedByAdminId = *req.ConfirmedByAdminId
	createBankTransaction.BonusReason = req.Remark // ใช้ช่องนี้ในการแจ้งเหตุผล

	// create transaction
	transId, err := s.repoBanking.InsertBankTransaction(createBankTransaction)
	if err != nil {
		log.Println("CreateUserCancelCredit.InsertBankTransaction.ERROR=", err)
		return nil, internalServerError(err)
	}

	var userCreditReq model.UserTransactionCreateRequest
	userCreditReq.RefId = transId
	userCreditReq.UserId = user.Id
	userCreditReq.TypeId = model.CREDIT_TYPE_CANCEL_CREDIT
	userCreditReq.Amount = req.Amount
	userCreditReq.CreateBy = req.ConfirmedByAdminId
	userCreditReq.ConfirmBy = req.ConfirmedByAdminId
	userCreditReq.StartWorkAt = req.ConfirmedAt // เริ่มนับตอนกดคอนเฟิม
	userCreditReq.Detail = req.Remark
	agentResp, err := s.repoAccounting.DecreaseUserCredit(userCreditReq)
	if err != nil {
		// [********] Confirm from P.layer if error from agent will be in failed
		log.Println("CreateUserCancelCredit.DecreaseUserCredit", err)
		var updateAgentError model.UpdateConfirmAutoWithdrawBody
		statusError := model.TRANS_STATUS_WITHDRAW_REJECTED
		confirmedByAuto := int64(0)
		updateAgentError.TransactionStatusId = &statusError
		updateAgentError.ConfirmedByAdminId = &confirmedByAuto
		if err := s.repoBanking.UpdateAdminAndTransactionStatus(*transId, updateAgentError); err != nil {
			return nil, internalServerError(err)
		}
		return nil, internalServerError(err)
	} else {
		if err := s.repoBanking.UpdateWithdrawTransactionSuccessPullCreditBack(*transId, *agentResp); err != nil {
			log.Println(err)
		}
	}

	// CreateSuccessTransferWithdraw
	var createConfirm model.CreateSuccessTransferWithdrawRequest
	createConfirm.TransactionId = *transId
	createConfirm.ConfirmedAt = req.ConfirmedAt
	createConfirm.ConfirmedByAdminId = req.ConfirmedByAdminId
	if _, err = s.CreateSuccessTransferWithdraw(createConfirm); err != nil {
		return nil, internalServerError(err)
	}

	// update bank_transaction confirmed by
	var updateApprovedBy model.UpdateConfirmAutoWithdrawBody
	updateApprovedBy.ConfirmedAt = req.ConfirmedAt
	updateApprovedBy.ConfirmedByAdminId = req.ConfirmedByAdminId
	if err = s.repoBanking.UpdateAdminAndTransactionStatus(*transId, updateApprovedBy); err != nil {
		return nil, internalServerError(err)
	}

	var externalNoti model.NotifyExternalNotificationRequest
	externalNoti.TypeNotify = model.PullCreditBackNotification
	externalNoti.TransId = transId
	externalNoti.MemberCode = user.MemberCode
	externalNoti.Amount = req.Amount
	externalNoti.UserCredit = agentResp.AgentAfterAmount
	externalNoti.ConfirmedByAdminId = *req.ConfirmedByAdminId
	if err := s.serviceNoti.ExternalNotification(externalNoti); err != nil {
		log.Println("CreateUserCancelCredit.FailedNotify", err)
	}

	return nil, nil
}

func (s *bankingService) SendWebSocket(userId int64, amount float64, membercode string, alertType string) error {

	var socketMessage model.WebScoket
	socketMessage.UserID = userId
	socketMessage.Amount = amount
	socketMessage.MemberCode = membercode
	socketMessage.AlertType = alertType
	if err := s.repoBanking.WebSocket(socketMessage); err != nil {
		return internalServerError(err)
	}
	return nil
}

func (s *bankingService) CreateFristTimeDepositRecord(req model.CreateFristTimeDepositRecordRequest) (*int64, error) {

	startProcess := time.Now()

	// log.Println("CreateFristTimeDepositRecord", helper.StructJson(req))

	member, err := s.repoBanking.GetMemberById(req.UserId)
	if err != nil {
		return nil, internalServerError(err)
	}
	// log.Println("CreateFristTimeDepositRecord.member", helper.StructJson(member))

	totalAmount := req.CreditAmount + req.BonusAmount

	if member.MemberCode == "" {
		memberCode, err := s.serviceUser.GenUniqueUserMemberCode(member.Id)
		if err != nil {
			log.Println("CreateFristTimeDepositRecord.GenUniqueUserMemberCode", err)
			return nil, internalServerError(err)
		}
		member.MemberCode = *memberCode
	}

	// Affiliate + Alliance Income
	if member.UserTypeName == "NONE" {
		// ถ้าฝากครั้งแรก จะได้เป็น AFFILIATE + REFER COMMISION
		if err := s.serviceAccounting.UserFirstDepositCommission(*member, totalAmount); err != nil {
			log.Println("CreateFristTimeDepositRecord.UserFirstDepositCommission.ERROR", err)
			return nil, internalServerError(err)
		}
	} else if member.UserTypeName == "ALLIANCE" {
		// REFER COMMISION
		if err := s.serviceAl.NoUseAlUpdateCommission(member.Id, totalAmount); err != nil {
			log.Println("CreateFristTimeDepositRecord.AlUpdateCommission.ERROR", err)
			return nil, internalServerError(err)
		}
	}

	bankAccount, err := s.repoBanking.GetBankAccountById(req.ToAccountId)
	if err != nil {
		log.Println("CreateFristTimeDepositRecord.GetBankAccountById", err, req.ToAccountId)
		return nil, internalServerError(err)
	}

	var checkDuplicate model.CheckDuplicateWebhookAndAdminRecord
	checkDuplicate.FromAccountNumber = req.FromAccountNumber
	checkDuplicate.FromBankId = member.BankId
	checkDuplicate.Amount = req.CreditAmount
	checkDuplicate.TransactionAt = req.TransferAt
	checkDuplicate.MemberCode = member.MemberCode
	checkDuplicate.CheckFromWhere = "ADMIN"
	checkDuplicate.ToBankId = &bankAccount.BankId
	duplicateFromAdminRecord, _ := s.repoAccounting.CheckDuplicateWebhookAndAdminRecord2(checkDuplicate)
	if duplicateFromAdminRecord.Id != 0 {
		statusLog := "SUCCESS"
		jsonPayLoad := fmt.Sprintf("DUPLICATE WITH ADMIN BANK TRANS ID: %v ", duplicateFromAdminRecord.Id)
		jsonRequest := helper.StructJson(req)
		logType := "DUPLICATE_ADMIN_WITH_WEBHOOK"
		var createLog model.BankTransactionLogCreateRequest
		createLog.Status = &statusLog
		createLog.JsonPayload = &jsonPayLoad
		createLog.JsonRequest = &jsonRequest
		createLog.LogType = &logType

		if _, err := s.serviceAccounting.CreateBankTransactionLog(createLog); err != nil {
			return nil, nil
		}

		log.Println("CreateFristTimeDepositRecord.duplicateFromRecord", helper.StructJson(duplicateFromAdminRecord))
		return nil, badRequest("DUPLICATE_WITH_ADMIN_RECORD")
	}
	//[30/11/2023] ไม่ต้องดักเงื่อนไขนี้แล้ว
	// var minDeposit int64
	// config, err := s.repoBanking.GetConfiguration()
	// if err == nil {
	// 	if config.MinimumDeposit == 0 {
	// 		minDeposit = 100
	// 	} else {
	// 		minDeposit = config.MinimumDeposit
	// 	}
	// 	if minDeposit > int64(req.CreditAmount) {
	// 		return nil, badRequest("LESS_THAN_CONFIG")
	// 	}
	// }
	// create statement

	// race condition
	actionAt := time.Now().UTC().Format("************")
	var createBody model.RaceActionCreateBody
	createBody.Name = "ADMIN_CREATE_FIRST_TIME_DEPOSIT"
	createBody.JsonRequest = helper.StructJson(req)
	createBody.Status = "PENDING"
	createBody.ActionKey = fmt.Sprintf("DEPOSIT_T%s_U%d_CD%f", actionAt, req.UserId, req.CreditAmount)
	createBody.UnlockAt = time.Now().UTC().Add(time.Second * 1)
	if _, err := s.repoBanking.GetRaceActionIdByActionKey(createBody.ActionKey); err != nil {
		log.Println("CreateFristTimeDepositRecord.GetRaceActionIdByActionKey", err)
		if err != gorm.ErrRecordNotFound {
			return nil, internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
		}
	} else {
		return nil, internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
	}

	// create race condition
	actionId, err := s.repoBanking.CreateRaceCondition(createBody)
	if err != nil {
		log.Println("CreateFristTimeDepositRecord.CreateRaceCondition", err)
		return nil, internalServerError(errors.New("CANNOT_CREATE_ACTION"))
	}

	if actionId != 0 {
		var createStatement model.BankStatementCreateBody
		createStatement.Amount = req.CreditAmount
		createStatement.AccountId = bankAccount.Id
		createStatement.Detail = req.FromAccountNumber + " " + req.BonusReason
		createStatement.FromBankId = member.BankId
		createStatement.FromAccountNumber = member.BankAccount
		createStatement.StatementTypeId = model.STATEMENT_TYPE_TRANSFER_IN
		createStatement.TransferAt = req.TransferAt
		createStatement.StatementStatusId = model.STATEMENT_STATUS_PENDING
		bankstatementId, err := s.repoBanking.CreateBankStatement(createStatement)
		if err != nil {
			log.Println("CreateFristTimeDepositRecord.CreateBankStatement", err)
			return nil, internalServerError(err)
		}
		var externalNoti model.NotifyExternalNotificationRequest
		var transId *int64
		if req.Auto {
			// [promotion]
			var promotionWebUserId int64
			GetUserPromotion, _ := s.promotionWebService.GetDepositCurrentProcessingUserPromotion(member.Id)
			if GetUserPromotion != nil {
				promotionWebUserId = GetUserPromotion.Id

			}
			var createBankTransaction model.BankTransactionCreateBody
			createBankTransaction.MemberCode = member.MemberCode
			createBankTransaction.UserId = member.Id
			createBankTransaction.StatementId = bankstatementId
			createBankTransaction.TransactionTypeId = model.TRANSACTION_TYPE_DEPOSIT
			createBankTransaction.PromotionId = &promotionWebUserId
			createBankTransaction.FromAccountNumber = &member.BankAccount
			createBankTransaction.FromBankId = &member.BankId
			createBankTransaction.ToAccountId = &bankAccount.Id
			createBankTransaction.ToAccountNumber = &bankAccount.AccountNumber
			createBankTransaction.ToBankId = &bankAccount.BankId
			createBankTransaction.ToAccountNumber = &bankAccount.AccountNumber
			createBankTransaction.CreditAmount = req.CreditAmount
			createBankTransaction.BonusAmount = req.BonusAmount
			createBankTransaction.BonusReason = req.BonusReason
			createBankTransaction.DepositChannel = req.DepositChannel
			createBankTransaction.TransactionStatusId = model.TRANS_STATUS_PENDING
			createBankTransaction.CreatedByAdminId = req.CreateByUserId
			createBankTransaction.TransferAt = &req.TransferAt
			//2/10/66
			// create transaction
			transId, err = s.repoBanking.InsertBankTransaction(createBankTransaction)
			if err != nil {
				log.Println("CreateFristTimeDepositRecord.InsertBankTransaction", err)
				return nil, internalServerError(err)
			}

			var actionStatement model.BankStatementMatchRequest
			actionStatement.StatementId = *bankstatementId
			actionStatement.UserId = member.Id
			actionStatement.ConfirmedAt = startProcess
			actionStatement.ConfirmedByAdminId = &req.CreateByUserId
			if err := s.serviceAccounting.SetStatementOwnerMatched(*bankstatementId, actionStatement, model.USE_ENDING_NOTI); err != nil {
				log.Println("CreateFristTimeDepositRecord.SetStatementOwnerMatched", err)
				return nil, internalServerError(err)
			}

			// DECREASE FASTBANK CREDIT 3.FROM create auto FIRST.DEPOSIT
			// func นี้ไม่ต้องหักเพราะไม่ได้เข้าผ่าน fastbank
			// if err := s.repoAccounting.DecreaseFastbankCredit(1); err != nil {
			// 	log.Println("CreateFristTimeDepositRecord.DECREASE_FASTBANK_CREDIT_ERROR", err)
			// }

			var confirmDeposit model.BankConfirmDepositRequest
			confirmDeposit.TransferAt = &req.TransferAt
			confirmDeposit.BonusAmount = &req.BonusAmount
			confirmDeposit.ConfirmedAt = startProcess
			confirmDeposit.ConfirmedByAdminId = &req.CreateByUserId
			if err := s.ConfirmDepositTransaction(*transId, confirmDeposit); err != nil {
				log.Println("CreateFristTimeDepositRecord.ConfirmDepositTransaction", err)
				return nil, internalServerError(err)
			}

			var userCreditReq model.UserTransactionCreateRequest
			userCreditReq.UserId = member.Id
			userCreditReq.TransferAt = &req.TransferAt
			userCreditReq.RefId = transId
			userCreditReq.PromotionId = &promotionWebUserId
			userCreditReq.TypeId = model.CREDIT_TYPE_DEPOSIT
			userCreditReq.AccountId = &bankAccount.Id
			userCreditReq.Detail = fmt.Sprintf("%s:%s => %s:%s", member.BankName, member.BankAccount, bankAccount.BankName, bankAccount.AccountName)
			userCreditReq.Amount = req.CreditAmount
			userCreditReq.BonusAmount = req.BonusAmount
			userCreditReq.CreateBy = &req.CreateByUserId
			userCreditReq.ConfirmBy = &req.CreateByUserId
			if agentResp, err := s.repoAccounting.IncreaseUserCredit(userCreditReq); err != nil {
				if bankstatementId != nil {
					var updateStatement model.BankStatementUpdateBody
					updateStatement.Id = *bankstatementId
					updateStatement.StatementStatusId = model.STATEMENT_STATUS_PENDING
					if err := s.repoAccounting.UpdateBankStatementStatus(updateStatement); err != nil {
						log.Println("CreateFristTimeDepositRecord.UpdateBankStatementStatus", err)
					}
				}
				log.Println("CreateFristTimeDepositRecord.IncreaseUserCredit", err)
				return nil, internalServerError(err)
			} else {
				// FASTBANK_SUCCESS
				if err := s.repoAccounting.UpdateDepositTransactionStatusFromAgent(*transId, *agentResp); err != nil {
					log.Println("CreateFristTimeDepositRecord.UpdateDeporsitTransactionStatusFromAgent", err)
				}
				// Set FastBank-READ ** ไม่มี เพราะเป็นการสร้างรายการฝากเอง
			}
			// [Lucky Wheel] ตอนฝากละได้โบนัส
			var luckyWheelBody model.ActivityLuckyWheelRoundUserRequest
			luckyWheelBody.UserId = member.Id
			luckyWheelBody.ConditionId = model.ACTIVITY_LUCKY_WHEEL_SETTING_CONDITION_DEPOSIT
			luckyWheelBody.ConditionAmount = req.CreditAmount
			if err := s.activityLuckyWheelService.CreateRoundActivityLuckyWheel(luckyWheelBody); err != nil {
				log.Println("CreateFristTimeDepositRecord.CreateRoundActivityLuckyWheel", err)
			}

			// [TIER]
			if err := s.repoAccounting.IncreaseUserTierDepositAmount(member.Id, req.CreditAmount); err != nil {
				log.Println("MatchStatementOwner.IncreaseUserTierDepositAmount", err)
			}

			// [turnOver] ถ้ามีการฝากเงิน ให้สร้าง TurnOver
			if err := CreateTurnOverFromSuccessDeposit(repository.NewTurnoverRepository(s.SharedDb), member.Id, req.CreditAmount, *transId); err != nil {
				log.Println("CreateBankStatementFromWebhookAndAuto.CreateTurnOverFromSuccessDeposit", err)
			}

			isFirstDeposit := s.repoBanking.IsFirstDeposit(member.Id)
			if isFirstDeposit {
				// [********] ไม่มีโบนัสฝากครั้งแรก
				// [turnOver] ตอนฝากละได้โบนัส
				// bonusAmount := req.BonusAmount
				// turnOverAmount := req.BonusAmount
				// var createBody model.TurnoverUserStatementCreateBody
				// createBody.UserId = member.Id
				// createBody.TypeId = model.TURNOVER_TYPE_PROMOTION_FIRST_DEPOSIT
				// createBody.RefTypeId = *transId
				// createBody.Name = model.TURNOVER_CATE_FIRST_DEPOSIT
				// createBody.PromotionName = "โบนัสฝากครั้งแรก"
				// createBody.BonusAmount = bonusAmount
				// if bonusAmount > 0 {
				// 	createBody.StatusId = model.TURNOVER_STATEMENT_STATUS_PENDING
				// } else {
				// 	createBody.StatusId = model.TURNOVER_STATEMENT_STATUS_COMPLETED
				// }
				// createBody.StartTurnAmount = turnOverAmount
				// createBody.StartTurnAt = &startProcess
				// createBody.TotalTurnAmount = turnOverAmount
				// if _, err := s.repoBanking.CreateTurnoverUserStatement(createBody); err != nil {
				// 	return nil, internalServerError(err)
				// }
				var createFirstDeposit model.UserFirstDepositCreateBody
				createFirstDeposit.UserId = member.Id
				createFirstDeposit.TransferAt = req.TransferAt
				createFirstDeposit.Amount = req.CreditAmount
				createFirstDeposit.Remark = "CreateFristTimeDepositRecord"
				if _, err := s.repoBanking.SetUserFirstDeposit(createFirstDeposit, transId); err != nil {
					log.Println("CreateFristTimeDepositRecord.SetUserFirstDeposit", err)
				}
			}

			if transId != nil {
				// AddTo BankPendingRecord -> รายการฝาก
				dashboardRepo := repository.NewDashboardRepository(s.SharedDb)
				if err := AddBankPendingRecordFromDeposit(dashboardRepo, *transId); err != nil {
					log.Println("CreateFristTimeDepositRecord.AUTO.AddBankPendingRecordFromDeposit.ERROR=", err)
				}
			}

			var checkUserPromotionBody model.CheckUserPromotionBody
			checkUserPromotionBody.UserId = member.Id
			_, err = s.promotionWebService.CheckUserPromotion(checkUserPromotionBody)
			if err != nil {
				log.Println("CreateFristTimeDepositRecord.CheckUserPromotion", err)
			}
			externalNoti.TypeNotify = model.IsDepositAfterCredit
			externalNoti.TransId = transId
			externalNoti.Amount = req.CreditAmount
			externalNoti.MemberCode = member.MemberCode
			externalNoti.BonusCredit = &req.BonusAmount
			externalNoti.UserCredit = totalAmount
			externalNoti.ConfirmedByAdminId = req.CreateByUserId
			externalNoti.TransferDateTime = req.TransferAt.Add(7 * time.Hour).Format("2006-01-02 15:04:05")
			externalNoti.ActionTime = time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02 15:04:05")

		} else {
			var createBankTransaction model.BankTransactionCreateBody
			createBankTransaction.MemberCode = member.MemberCode
			createBankTransaction.UserId = member.Id
			createBankTransaction.StatementId = bankstatementId
			createBankTransaction.TransactionTypeId = model.TRANSACTION_TYPE_DEPOSIT
			createBankTransaction.FromBankId = &member.BankId
			createBankTransaction.FromAccountNumber = &member.BankAccount
			createBankTransaction.ToAccountId = &bankAccount.Id
			createBankTransaction.ToAccountNumber = &bankAccount.AccountNumber
			createBankTransaction.ToBankId = &bankAccount.BankId
			createBankTransaction.ToAccountNumber = &bankAccount.AccountNumber
			createBankTransaction.CreditAmount = req.CreditAmount
			createBankTransaction.BonusAmount = req.BonusAmount
			createBankTransaction.BonusReason = req.BonusReason
			createBankTransaction.DepositChannel = req.DepositChannel
			createBankTransaction.TransactionStatusId = model.TRANS_STATUS_PENDING
			createBankTransaction.CreatedByAdminId = req.CreateByUserId
			createBankTransaction.TransferAt = &req.TransferAt
			//2/10/66
			// create transaction
			transId, err = s.repoBanking.InsertBankTransaction(createBankTransaction)
			if err != nil {
				log.Println("CreateFristTimeDepositRecord.InsertBankTransaction.ERROR=", err)
				return nil, internalServerError(err)
			}

			if transId != nil {
				// AddTo BankPendingRecord -> รายการฝาก
				dashboardRepo := repository.NewDashboardRepository(s.SharedDb)
				if err := AddBankPendingRecordFromDeposit(dashboardRepo, *transId); err != nil {
					log.Println("CreateFristTimeDepositRecord.NOT_AUTO.AddBankPendingRecordFromDeposit.ERROR=", err)
				}
			}

			externalNoti.TypeNotify = model.IsDepositBeforeCredit
			externalNoti.TransId = transId
			externalNoti.Amount = req.CreditAmount
			externalNoti.MemberCode = member.MemberCode
			externalNoti.BonusCredit = &req.BonusAmount
			externalNoti.UserCredit = totalAmount
			externalNoti.ConfirmedByAdminId = req.CreateByUserId
		}

		endProcess := time.Now()
		elapsed := endProcess.Sub(startProcess)
		elapsedSeconds := elapsed.Seconds()
		timeElapsed := fmt.Sprintf("%.2f", elapsedSeconds)

		externalNoti.TimerCounter = timeElapsed
		if err := s.serviceNoti.ExternalNotification(externalNoti); err != nil {
			log.Println("FailedNotify", err)
		}
	}
	return nil, nil
}

func (s *bankingService) WebCheckUserDuplicateWithdrawProeccing(userId int64) (*model.CheckUserDuplicateWithdrawResponse, error) {

	var response model.CheckUserDuplicateWithdrawResponse
	getDuplicateWithdraw, err := s.repoBanking.CheckUserDuplicateWithdrawProcessing(userId)
	if err != nil {
		response.Id = 0
		response.CreditAmount = 0
		response.CreatedAt = time.Now()
		response.Avaliable = true
	} else {
		response.Id = getDuplicateWithdraw.Id
		response.CreditAmount = getDuplicateWithdraw.CreditAmount
		response.CreatedAt = getDuplicateWithdraw.CreatedAt
		response.Avaliable = false
	}

	return &response, nil
}

func (s *bankingService) GetBankTransactionFirstTimeDeposit(req model.GetBankTransactionFirstTimeDepositRequest) (*model.SuccessWithPagination, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}
	list, total, err := s.repoBanking.GetBankTransactionFirstTimeDeposit(req)
	if err != nil {
		return nil, internalServerError(err)
	}

	var result model.SuccessWithPagination
	result.List = list
	result.Total = total
	return &result, nil
}

func (s *bankingService) UserSettingWithdraw(userId int64, creditWithDraw float64) (string, error) {

	var result string
	var req model.UserWithdrawSettingGetByUserIdRequest
	req.UserId = userId
	getUserSetting, err := s.serviceUser.UserWithdrawSettingGetByUserId(req)
	if err != nil {
		return result, internalServerError(err)
	}
	var reqWithdraw model.TransactionWithdrawToCheckUserConfigRequest
	reqWithdraw.UserId = userId
	reqWithdraw.FromTransferDate = time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02")
	reqWithdraw.ToTransferDate = time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02")

	withdrawTran, totalWithdraw, err := s.repoBanking.TransactionWithdrawToCheckUserConfigList(reqWithdraw)
	if err != nil {
		return result, internalServerError(err)
	}

	totalAmount := float64(0) + creditWithDraw
	if len(withdrawTran) > 0 {
		for _, v := range withdrawTran {
			totalAmount += v.CreditAmount
		}
	}

	if getUserSetting.AccumulatedAmount <= totalAmount {
		result = model.USER_WITHDRAW_SETTING_UNPASS
		return result, nil
	} else {
		result = model.USER_WITHDRAW_SETTING_PASS_AUTO
	}

	maximumTimePerDay := int64(getUserSetting.MaximumTimePerDay)

	if maximumTimePerDay <= totalWithdraw {
		result = model.USER_WITHDRAW_SETTING_UNPASS
		return result, nil
	} else {
		result = model.USER_WITHDRAW_SETTING_PASS_AUTO
	}

	return result, nil
}

func (s *bankingService) RetryDepositAgent(req model.RetryDepositAgentRequest) (string, error) {
	message := "FAILED_RETRY_DEPOSIT_AGENT"
	startWorkAt := time.Now().UTC()
	//get transaction check First
	getTransaction, err := s.repoBanking.GetBankTransactionById(req.RefId)
	if err != nil {
		return "", internalServerError(err)
	}
	// find user transaction
	getUserTransaction, _ := s.repoBanking.GetUserTransactionByRefId(getTransaction.Id)
	if getUserTransaction != nil {
		statusLog := "ERROR"
		jsonPayLoad := fmt.Sprintf("TRANSACTION_ID:%v", getTransaction.Id)
		jsonRequest := helper.StructJson(req)
		logType := "RETRY_DEPOSIT_AGENT_ALREADY_EXIST"
		var createLog model.BankTransactionLogCreateRequest
		createLog.Status = &statusLog
		createLog.JsonPayload = &jsonPayLoad
		createLog.JsonRequest = &jsonRequest
		createLog.LogType = &logType

		if _, err := s.serviceAccounting.CreateBankTransactionLog(createLog); err != nil {
			return "", nil
		}
		return "", badRequest("TRANSACTION_ALREADY_EXIST")
	}

	actionAt := time.Now().UTC().Format("************")
	var createBody model.RaceActionCreateBody
	createBody.Name = "ทำการลองยิง agent ใหม่"
	createBody.JsonRequest = helper.StructJson(req)
	createBody.Status = "PENDING"
	createBody.ActionKey = fmt.Sprintf("RETRY_DEPOSIT_AGENT_%s_%d", actionAt, req.RefId)
	createBody.UnlockAt = time.Now().UTC().Add(time.Second * 1)
	if _, err := s.repoBanking.GetRaceActionIdByActionKey(createBody.ActionKey); err != nil {
		log.Println("RetryDepositAgent.ERROR.GetRaceActionByActionKey", err)
		if err != gorm.ErrRecordNotFound {
			return "", internalServerError(errors.New("CANNOT_CHECK_ACTION"))
		}
	} else {
		return "", internalServerError(errors.New("WORK_IN_ACTION"))
	}

	// create race condition
	actionId, err := s.repoBanking.CreateRaceCondition(createBody)
	if err != nil {
		log.Println("RetryDepositAgent.ERROR.CreateRaceCondition", err)
		return "", internalServerError(errors.New("CANNOT_CREATE_ACTION"))
	}
	if actionId != 0 {
		// increase credit
		var userCreditReq model.UserTransactionCreateRequest
		userCreditReq.UserId = getTransaction.UserId
		userCreditReq.TypeId = model.CREDIT_TYPE_DEPOSIT
		userCreditReq.AccountId = &getTransaction.ToAccountId
		userCreditReq.Detail = fmt.Sprintf("%s:%s => %s:%s", getTransaction.FromBankName, getTransaction.FromAccountNumber, getTransaction.ToBankName, getTransaction.ToAccountName)
		userCreditReq.Amount = getTransaction.CreditAmount
		userCreditReq.StartWorkAt = startWorkAt
		userCreditReq.RefId = &getTransaction.Id
		userCreditReq.TransferAt = getTransaction.TransferAt
		userCreditReq.CreateBy = &getTransaction.CreatedByAdminId
		userCreditReq.ConfirmBy = req.ConfirmedByAdminId
		if agentResp, err := s.repoAccounting.IncreaseUserCredit(userCreditReq); err != nil {
			if getTransaction.StatementId > 0 {
				var updateStatement model.BankStatementUpdateBody
				updateStatement.Id = getTransaction.StatementId
				updateStatement.StatementStatusId = model.STATEMENT_STATUS_PENDING
				if err := s.repoAccounting.UpdateBankStatementStatus(updateStatement); err != nil {
					log.Println("CreateBankStatementFromWebhookAndAuto.UpdateBankStatementStatus", err)
				}
			}
			log.Println("RetryDepositAgent.ERROR.IncreaseUserCredit", err)
			return "", internalServerError(err)
		} else if agentResp != nil {
			if err := s.repoAccounting.UpdateDepositTransactionStatusFromAgent(getTransaction.Id, *agentResp); err != nil {
				log.Println("RetryDepositAgent.ERROR.UpdateDepositTransactionStatusFromAgent", err)
			}
			message = "SUCCESS_RETRY_DEPOSIT_AGENT"

			var updateConfirmTransactionRetry model.UpdateConfirmTransactionRetry
			updateConfirmTransactionRetry.ConfirmedAt = startWorkAt
			updateConfirmTransactionRetry.ConfirmedByAdminId = req.ConfirmedByAdminId

			if err := s.repoBanking.UpdateConfirmTransactionRetry(getTransaction.Id, updateConfirmTransactionRetry); err != nil {
				log.Println("RetryDepositAgent.ERROR.UpdateConfirmTransactionRetry", err)
			}

		}
	}

	return message, nil
}

func (s *bankingService) CheckAdminDuplicateWithdrawList(req model.CheckAdminDuplicateWithdrawListRequest) ([]model.CheckAdminDuplicateWithdrawList, error) {

	var list []model.CheckAdminDuplicateWithdrawList
	var err error
	list, err = s.repoBanking.CheckAdminDuplicateWithdrawList(req)
	if err != nil {
		return list, nil
	}

	// CheckDuplicateWithdraw(req model.CheckDuplicateWithdrawRequest) (*model.BankTransaction, error)
	var CheckDuplicateWithdrawRequest model.CheckDuplicateWithdrawRequest
	CheckDuplicateWithdrawRequest.UserId = req.UserId
	CheckDuplicateWithdrawRequest.Amount = req.CreditAmount
	listChecker, _ := s.repoBanking.CheckDuplicateWithdraw(CheckDuplicateWithdrawRequest)
	if listChecker != nil && listChecker.Id != 0 {
		var checkAdminDuplicateWithdrawList model.CheckAdminDuplicateWithdrawList
		checkAdminDuplicateWithdrawList.Id = listChecker.Id
		checkAdminDuplicateWithdrawList.CreditAmount = listChecker.CreditAmount
		checkAdminDuplicateWithdrawList.UserId = listChecker.UserId
		checkAdminDuplicateWithdrawList.MemberCode = listChecker.MemberCode
		checkAdminDuplicateWithdrawList.TransferAt = *listChecker.TransferAt
		list = append(list, checkAdminDuplicateWithdrawList)
	}
	return list, nil
}

func (s *bankingService) GetBankTransactionExternalDetailByBankTransactionId(id int64) ([]model.BankTransactionExternalDetailGetByBankTransactionIdResponse, error) {

	var list []model.BankTransactionExternalDetailGetByBankTransactionIdResponse
	var err error
	list, err = s.repoBanking.GetBankTransactionExternalDetailByBankTransactionId(id)
	if err != nil {
		return list, nil
	}
	return list, nil
}

func (s *bankingService) ConfirmWithdrawTransactionAnyStatus(req model.CreateAutoWithdrawRequest) error {

	startProcess := time.Now()

	getWebLocal, _ := s.repoBanking.GetLocalWebInfo()
	if getWebLocal != nil {
		if getWebLocal.FastbankCreditBalance <= -1000 {
			log.Println("ConfirmWithdrawTransactionAnyStatus.WEB_OUT_OF_CREDIT")
			return badRequest("ยอดเงินเว็บหมด อายุ")
		}
	}

	admin, err := s.repoBanking.GetAdminById(*req.ConfirmedByAdminId)
	if err != nil {
		log.Println("ConfirmWithdrawTransactionAnyStatus.GetAdminById.ERROR=", err)
		return internalServerError(err)
	}

	// var transaction model.BankTransaction
	transaction, err := s.repoBanking.GetTransactionWithdrawOverMaxById(req.TransactionId)
	if err != nil {
		return badRequest("ไม่ตรงกับเงือนไข")
	}
	if transaction.Id == 0 {
		return badRequest("ไม่พบรายการที่ต้องการ")
	}

	// fmt.Println("transaction", helper.StructJson(transaction))
	user, err := s.repoBanking.GetUserBankDetailById(transaction.UserId)
	if err != nil {
		return internalServerError(err)
	}

	// GetBankAccountById(id int64) (*model.BankAccount, error)
	// bankAccount, err := s.repoBanking.GetBankAccountById(transaction.FromAccountId)
	// if err != nil {
	// 	return internalServerError(err)
	// }

	// [race condition withdraw]
	actionAt := time.Now().UTC().Format("************")
	var createBody model.RaceActionCreateBody
	createBody.Name = "CONFIRM"
	createBody.JsonRequest = fmt.Sprintf("req transactionId : %d", req.TransactionId)
	createBody.Status = "PENDING"
	createBody.ActionKey = fmt.Sprintf("WITHDRAW_T%s_U%d_CD%f", actionAt, user.Id, transaction.CreditAmount)
	createBody.UnlockAt = time.Now().UTC().Add(time.Second * 1)
	if _, err := s.repoBanking.GetRaceActionIdByActionKey(createBody.ActionKey); err != nil {
		log.Println("ConfirmWithdrawTransactionAnyStatus.GetRaceActionIdByActionKey", err)
		if err != gorm.ErrRecordNotFound {
			return internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
		}
	} else {
		return internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
	}

	// create race condition
	actionId, err := s.repoBanking.CreateRaceCondition(createBody)
	if err != nil {
		log.Println("ConfirmWithdrawTransactionAnyStatus.CreateRaceCondition", err)
		return internalServerError(errors.New("CANNOT_CREATE_ACTION"))
	}

	if actionId == 0 {
		return badRequest("PLEASE_WAIT_ONE_MINUTE_YOU_JUST_CLICKED_A_MOMENT_AGO_PLEASE_CHECK_THE_STATEMENT_AGAIN")
	}

	var statusId int64
	// var transferBody model.WithdrawTransferFastBankBody
	// transferBody.AccountFrom = bankAccount.AccountNumber
	// transferBody.AccountTo = user.BankAccount

	// if transaction.CreditAmount == float64(int(transaction.CreditAmount)) {
	// 	// If the amount has no decimal part, convert it to two decimal places
	// 	transferBody.Amount = strconv.FormatFloat(transaction.CreditAmount, 'f', 2, 64)
	// } else {
	// 	// If the amount already has decimals, leave it unchanged
	// 	transferBody.Amount = strconv.FormatFloat(transaction.CreditAmount, 'f', -1, 64)
	// }

	var externalNoti model.NotifyExternalNotificationRequest
	// transferBody.BankCode = user.BankCode
	// transferBody.Pin = bankAccount.PinCode
	//7.4 โอนเงิน fastbank
	//7.5 เมื่อเสร็จสิ้นก็ update transaction status if fastbank success
	statusLog := "SUCCESS"
	jsonPayLoad := "WITHDRAW_CONFIRM"
	jsonRequest := fmt.Sprintf(" BANK_TRANS_ID %v ", transaction.Id)
	logType := "WITHDRAW_CONFIRM"
	var createLog model.BankTransactionLogCreateRequest
	createLog.Status = &statusLog
	createLog.JsonPayload = &jsonPayLoad
	createLog.JsonRequest = &jsonRequest
	createLog.LogType = &logType
	if _, err := s.serviceAccounting.CreateBankTransactionLog(createLog); err != nil {
		log.Println("ConfirmWithdrawTransactionAnyStatus.CreateBankTransactionLog", err)
	}

	// by bass action key cause of fast bank error
	statusId = model.TRANS_STATUS_WITHDRAW_SUCCESS
	// var createConfirm model.CreateSuccessTransferWithdrawRequest
	// createConfirm.TransactionId = req.TransactionId
	// createConfirm.ConfirmedAt = time.Now()
	// createConfirm.ConfirmedByAdminId = req.ConfirmedByAdminId
	// if _, err := s.CreateSuccessTransferWithdraw(createConfirm); err != nil {
	// 	return internalServerError(err)
	// }

	// Show Withdraw USER_TRANSACTION
	var showUserTrans model.UserTransactionShowUpdate
	showUserTrans.TransactionId = req.TransactionId
	showUserTrans.ConfirmedAt = &startProcess
	showUserTrans.ConfirmAdminId = req.ConfirmedByAdminId
	if err := s.repoAccounting.ShowUserTransaction(showUserTrans); err != nil {
		return internalServerError(err)
	}

	// DECREASE FASTBANK CREDIT
	if err := s.repoAccounting.DecreaseFastbankCredit(1); err != nil {
		log.Println("ConfirmWithdrawTransactionAnyStatus.DECREASE_FASTBANK_CREDIT_ERROR", err)
	}
	externalNoti.TypeNotify = model.IsWithdrawalCreditSuccess
	externalNoti.TransferDateTime = time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02 15:04:05")
	externalNoti.ActionTime = time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02 15:04:05")

	var updateApprovedBy model.UpdateConfirmAutoWithdrawBody
	updateApprovedBy.TransactionStatusId = &statusId
	updateApprovedBy.ConfirmedAt = time.Now()
	updateApprovedBy.ConfirmedByAdminId = req.ConfirmedByAdminId

	errUpdateAdminAnd := s.repoBanking.UpdateAdminAndTransactionStatus(req.TransactionId, updateApprovedBy)
	if errUpdateAdminAnd != nil {
		log.Println("ConfirmWithdrawTransactionAnyStatus.UpdateAdminAndTransactionStatus", errUpdateAdminAnd)
	}

	var updateApprovedByAdmin model.UpdateConfirmedByAdminIdRequest
	updateApprovedByAdmin.ConfirmAdminId = req.ConfirmedByAdminId
	errUpdateConfirmed := s.repoBanking.UpdateConfirmedByAdminId(req.TransactionId, updateApprovedByAdmin)
	if errUpdateConfirmed != nil {
		log.Println("ConfirmWithdrawTransactionAnyStatus.UpdateConfirmedByAdminId", errUpdateConfirmed)
	}

	// Create detail
	var createBankTransactionExternalDetail model.BankTransactionExternalDetailCreateRequest
	createBankTransactionExternalDetail.BankTransactionId = req.TransactionId
	createBankTransactionExternalDetail.Detail = "SUCCESS"
	createBankTransactionExternalDetail.ErrorCode = 0
	if _, err := s.repoBanking.CreateBankTransactionExternalDetail(createBankTransactionExternalDetail); err != nil {
		return nil
	}

	end := time.Now()
	elapsed := end.Sub(startProcess)
	externalNoti.TransId = &req.TransactionId
	externalNoti.Amount = transaction.CreditAmount
	externalNoti.MemberCode = user.MemberCode
	externalNoti.UserCredit = transaction.AfterAmount
	externalNoti.ConfirmedByAdminId = *req.ConfirmedByAdminId
	externalNoti.TimerCounter = fmt.Sprintf("%.2f", elapsed.Seconds())
	if err := s.serviceNoti.ExternalNotification(externalNoti); err != nil {
		log.Println("ConfirmWithdrawTransactionAnyStatus.FailedNotify", err)
	}

	// ===========================================================
	// RemoveFrom BankPendingRecord -> รายการถอน อนุมัติรายการถอน แบบปุ่มสำเร็จ(โอนลม)
	dashboardRepo := repository.NewDashboardRepository(s.SharedDb)
	if err := ConfirmBankPendingRecordFromAny(dashboardRepo, transaction.TransactionTypeId, transaction.Id, startProcess, admin.Fullname); err != nil {
		log.Println("ConfirmWithdrawTransactionAnyStatus.ConfirmBankPendingRecordFromAny.ERROR=", err)
	}

	return nil
}

func (s *bankingService) UserDepositByUploadFile(req model.CreateDepositFromUserUploadRequest) (*int64, error) {

	actionAt := time.Now().UTC()

	user, err := s.repoBanking.GetUserBankDetailById(req.UserId)
	if err != nil {
		log.Println("UserDepositByUploadFile.GetUserBankDetailByMemberCode", err)
		return nil, internalServerError(err)
	}

	bankAccount, err := s.repoBanking.GetBankAccountById(req.BankId)
	if err != nil {
		return nil, internalServerError(err)
	}

	// Check ThisUser Pending Slip
	if _, err := s.repoBanking.GetPendingDespositTransactionWithSlip(user.Id); err != nil {
		if err != gorm.ErrRecordNotFound {
			log.Println("UserDepositByUploadFile.CheckPendingSlip", err)
			return nil, badRequest("มีรายการฝากเงินรอดำเนินการอยู่")
		}
	} else {
		log.Println("UserDepositByUploadFile.CheckPendingSlip", err)
		return nil, badRequest("มีรายการฝากเงินรอดำเนินการอยู่")
	}

	// ========================================

	var createBody model.RaceActionCreateBody
	createBody.Name = "USER_DEPOSIT_BY_SLIP"
	createBody.JsonRequest = helper.StructJson(req)
	createBody.Status = "PENDING"
	createBody.ActionKey = fmt.Sprintf("DEPOSIT_T%s_U%d_CD%f", actionAt.Format("************"), user.Id, req.Amount)
	createBody.UnlockAt = time.Now().UTC().Add(time.Second * 1)
	if _, err := s.repoBanking.GetRaceActionIdByActionKey(createBody.ActionKey); err != nil {
		log.Println("CreateBankStatementFromWebhookAndAuto.GetRaceActionIdByActionKey", err)
		if err != gorm.ErrRecordNotFound {
			return nil, internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
		}
	} else {
		return nil, internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
	}

	// create race condition
	if _, err := s.repoBanking.CreateRaceCondition(createBody); err != nil {
		log.Println("CreateBankStatementFromWebhookAndAuto.CreateRaceCondition", err)
		return nil, internalServerError(errors.New("CANNOT_CREATE_ACTION"))
	}

	// ========================================

	var checkDuplicate model.CheckDuplicateWebhookAndAdminRecord
	checkDuplicate.FromAccountNumber = user.BankAccount
	checkDuplicate.FromBankId = user.BankId
	checkDuplicate.Amount = req.Amount
	checkDuplicate.TransactionAt = req.DepositedAt
	checkDuplicate.CheckFromWhere = "ADMIN"
	checkDuplicate.MemberCode = user.MemberCode
	checkDuplicate.ToBankId = &bankAccount.BankId
	duplicateFromAdminRecord, _ := s.repoAccounting.CheckDuplicateWebhookAndAdminRecord2(checkDuplicate)
	if duplicateFromAdminRecord.Id != 0 {
		statusLog := "SUCCESS"
		jsonPayLoad := fmt.Sprintf("DUPLICATE WITH ADMIN BANK TRANS ID: %v ", duplicateFromAdminRecord.Id)
		jsonRequest := helper.StructJson(req)
		logType := "DUPLICATE_ADMIN_WITH_WEBHOOK"
		var createLog model.BankTransactionLogCreateRequest
		createLog.Status = &statusLog
		createLog.JsonPayload = &jsonPayLoad
		createLog.JsonRequest = &jsonRequest
		createLog.LogType = &logType

		if _, err := s.serviceAccounting.CreateBankTransactionLog(createLog); err != nil {
			return nil, nil
		}

		log.Println("UserDepositByUploadFile.duplicateFromRecord", helper.StructJson(duplicateFromAdminRecord))
		return nil, badRequest("DUPLICATE_WITH_ADMIN_RECORD")
	}

	// create statement
	var createStatement model.BankStatementCreateBody
	createStatement.Amount = req.Amount
	createStatement.AccountId = bankAccount.Id
	createStatement.Detail = fmt.Sprintf("%s:%s => %s:%s", user.BankName, user.BankAccount, bankAccount.BankName, bankAccount.AccountName)
	createStatement.FromBankId = user.BankId
	createStatement.FromAccountNumber = user.BankAccount
	createStatement.StatementTypeId = model.STATEMENT_TYPE_TRANSFER_IN
	createStatement.TransferAt = req.DepositedAt
	createStatement.StatementStatusId = model.STATEMENT_STATUS_PENDING
	bankstatementId, err := s.repoBanking.CreateBankStatement(createStatement)
	if err != nil {
		log.Println("UserDepositByUploadFile.CreateBankStatement", err)
		return nil, internalServerError(err)
	}

	var promotionWebUserId int64
	GetUserPromotion, _ := s.promotionWebService.GetDepositCurrentProcessingUserPromotion(user.Id)
	if GetUserPromotion != nil {
		promotionWebUserId = GetUserPromotion.Id
	}

	// create transaction
	var createBankTransaction model.BankTransactionCreateBody
	createBankTransaction.MemberCode = user.MemberCode
	createBankTransaction.UserId = user.Id
	createBankTransaction.StatementId = bankstatementId
	createBankTransaction.TransactionTypeId = model.TRANSACTION_TYPE_DEPOSIT
	createBankTransaction.FromAccountNumber = &user.BankAccount
	createBankTransaction.PromotionId = &promotionWebUserId
	createBankTransaction.FromBankId = &user.BankId
	createBankTransaction.ToAccountId = &bankAccount.Id
	createBankTransaction.ToAccountNumber = &bankAccount.AccountNumber
	createBankTransaction.ToBankId = &bankAccount.BankId
	createBankTransaction.ToAccountNumber = &bankAccount.AccountNumber
	createBankTransaction.CreditAmount = req.Amount
	createBankTransaction.DepositChannel = "UPLOAD_SLIP"
	createBankTransaction.SlipImgUrl = req.SlipImgUrl
	createBankTransaction.TransactionStatusId = model.TRANS_STATUS_PENDING
	createBankTransaction.TransferAt = &req.DepositedAt
	// createBankTransaction.IsAutoCredit = true แอดมินเป็นคนกด
	transId, err := s.repoBanking.InsertBankTransaction(createBankTransaction)
	if err != nil {
		log.Println("UserDepositByUploadFile.InsertBankTransaction.ERROR=", err)
		return nil, internalServerError(err)
	}

	// [Lucky Wheel] ตอนฝากละได้โบนัส [2024/12/28 ยังเป็น pending อยู่]
	// var luckyWheelBody model.ActivityLuckyWheelRoundUserRequest
	// luckyWheelBody.UserId = user.Id
	// luckyWheelBody.ConditionId = model.ACTIVITY_LUCKY_WHEEL_SETTING_CONDITION_DEPOSIT
	// luckyWheelBody.ConditionAmount = req.Amount
	// if err := s.activityLuckyWheelService.CreateRoundActivityLuckyWheel(luckyWheelBody); err != nil {
	// 	log.Println("CreateFristTimeDepositRecord.CreateRoundActivityLuckyWheel", err)
	// }

	// [turnOver] ถ้ามีการฝากเงิน ให้สร้าง TurnOver
	if err := CreateTurnOverFromSuccessDeposit(repository.NewTurnoverRepository(s.SharedDb), user.Id, req.Amount, *transId); err != nil {
		log.Println("UserDepositByUploadFile.CreateTurnOverFromSuccessDeposit", err)
	}

	if transId != nil {
		// AddTo BankPendingRecord -> รายการฝาก
		dashboardRepo := repository.NewDashboardRepository(s.SharedDb)
		if err := AddBankPendingRecordFromDeposit(dashboardRepo, *transId); err != nil {
			log.Println("UserDepositByUploadFile.AddBankPendingRecordFromDeposit", err)
		}
	}

	// var socketMessage model.WebScoket
	// socketMessage.UserID = user.Id
	// socketMessage.Amount = body.Amount
	// socketMessage.MemberCode = user.MemberCode
	// socketMessage.AlertType = "DEPOSIT"
	// if err := s.repoAccounting.WebSocket(socketMessage); err != nil {
	// 	return nil, nil
	// }

	return transId, nil
}

func (s *bankingService) UserDepositBySlip(body model.CreateDepositConfirmedFromUserRequest) (*int64, error) {

	startProcess := time.Now()
	// [********]fastbank credit check balance to continue unuse func or not auto
	getWebLocal, _ := s.repoBanking.GetLocalWebInfo()
	if getWebLocal != nil {
		if getWebLocal.FastbankCreditBalance <= -1000 {
			log.Println("UserDepositBySlip.WEB_OUT_OF_CREDIT")
			return nil, badRequest("CONTACT_ADMIN_FOR_MANUAL_DEPOSIT")
		}
	}
	// GetUserBankDetailByMemberCode(memberCode string) (*model.UserBankDetailBody, error)
	user, err := s.repoBanking.GetUserBankDetailById(body.UserId)
	if err != nil {
		log.Println("UserDepositBySlip.GetUserBankDetailByMemberCode", err)
		return nil, internalServerError(err)
	}

	actionAt := time.Now().UTC().Format("************")
	var createBody model.RaceActionCreateBody
	createBody.Name = "USER_DEPOSIT_BY_SLIP"
	createBody.JsonRequest = helper.StructJson(body)
	createBody.Status = "PENDING"
	createBody.ActionKey = fmt.Sprintf("DEPOSIT_T%s_U%d_CD%f", actionAt, user.Id, body.Amount)
	createBody.UnlockAt = time.Now().UTC().Add(time.Second * 1)
	if _, err := s.repoBanking.GetRaceActionIdByActionKey(createBody.ActionKey); err != nil {
		log.Println("CreateBankStatementFromWebhookAndAuto.GetRaceActionIdByActionKey", err)
		if err != gorm.ErrRecordNotFound {
			return nil, internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
		}
	} else {
		return nil, internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
	}

	// create race condition
	actionId, err := s.repoBanking.CreateRaceCondition(createBody)
	if err != nil {
		log.Println("CreateBankStatementFromWebhookAndAuto.CreateRaceCondition", err)
		return nil, internalServerError(errors.New("CANNOT_CREATE_ACTION"))
	}

	if actionId != 0 {
		// CheckScammer
		var checkScammerSlipRequest model.CheckScammerSlipRequest
		checkScammerSlipRequest.UserId = body.UserId
		checkScammerSlipResponse, _ := s.repoBanking.CheckScammerSlipRequest(checkScammerSlipRequest)
		if checkScammerSlipResponse != nil && checkScammerSlipResponse.CountTodaySent > 5 {
			return nil, badRequest("SENT_SLIP_MORE_THAN_5_TIMES_TODAY")
		}

		bankAccount, err := s.repoBanking.GetBankAccountById(body.BankId)
		if err != nil {
			return nil, internalServerError(err)
		}

		// Get the bank account info from the QR code
		qrData, err := VerifyQrDataKTB(body.RawQrCode)
		if err != nil {
			var createBankTransactionSlip model.BankTransactionSlipCreateRequest
			createBankTransactionSlip.UserId = body.UserId
			createBankTransactionSlip.Status = 1
			createBankTransactionSlip.TransactionId = 0
			createBankTransactionSlip.RawQrCode = nil
			createBankTransactionSlip.FromAccountNumber = qrData.FromAccount
			createBankTransactionSlip.FromAccountName = qrData.FromAccountName
			createBankTransactionSlip.FromBankName = qrData.FromBankName
			createBankTransactionSlip.ToAccountNumber = qrData.ToAccount
			createBankTransactionSlip.ToAccountName = qrData.ToAccountName
			createBankTransactionSlip.Amount = qrData.Amount
			createBankTransactionSlip.TransactionDate = time.Now().UTC()
			createBankTransactionSlip.Remark = "UNABLE_TO_VERIFY_QR_CODE"
			_, err := s.repoBanking.CreateBankTransactionSlip(createBankTransactionSlip)
			if err != nil {
				return nil, badRequest("INVALID_QR_CODE")
			}
			return nil, badRequest("INVALID_QR_CODE")
		}

		passValidate := true
		var errorValidate []string
		// [Bank]
		cutStringToaccountNumber, _ := extractAndFilterAccountNumber(qrData.ToAccount)
		if cutStringToaccountNumber == "UNMATCH" {
			qrData.ToAccount = ""
			messageError := fmt.Sprintf("extract ไม่ได้ qrData.ToAccount:%s", qrData.ToAccount)
			passValidate = false
			errorValidate = append(errorValidate, messageError)
		} else {
			qrData.ToAccount = cutStringToaccountNumber
		}
		isBankAccountNumberRequiredIn := strings.Contains(bankAccount.AccountNumber, qrData.ToAccount)
		if !isBankAccountNumberRequiredIn {
			passValidate = false
			messageError := fmt.Sprintf("bankAccount.AccountNumber:%s กับ qrData.ToAccount:%s ไม่ตรง", bankAccount.AccountNumber, qrData.ToAccount)
			errorValidate = append(errorValidate, messageError)
		}
		// fmt.Println("qrData.ToAccountName", qrData.ToAccountName)
		// qrData.ToAccountName = cutNamePrefix(qrData.ToAccountName)
		// fmt.Println("qrData.ToAccountName After", qrData.ToAccountName)

		bankNameParts := strings.Split(bankAccount.AccountName, " ")
		var bankAccountNameCheck string
		if len(bankNameParts) > 1 {
			bankAccountNameCheck = bankNameParts[1]
		} else {
			bankAccountNameCheck = bankNameParts[0]
		}

		// Check if qrData.ToAccountName contains bankAccountNameCheck
		if !strings.Contains(qrData.ToAccountName, bankAccountNameCheck) {
			passValidate = false
			messageError := fmt.Sprintf("bankAccount.AccountName:%s กับ qrData.ToAccountName:%s ไม่ตรง", bankAccount.AccountName, qrData.ToAccountName)
			errorValidate = append(errorValidate, messageError)
		}

		if body.Amount != qrData.Amount {
			passValidate = false
			messageError := fmt.Sprintf("amount:%f กับ qrData.Amount:%f ไม่ตรง", body.Amount, qrData.Amount)
			errorValidate = append(errorValidate, messageError)
		}

		// [USER Bank]
		cutStringFromaccountNumber, _ := extractAndFilterAccountNumber(qrData.FromAccount)
		if cutStringFromaccountNumber == "UNMATCH" {
			passValidate = false
			messageError := fmt.Sprintf("extract ไม่ได้ qrData.FromAccount:%s", qrData.FromAccount)
			qrData.FromAccount = ""
			errorValidate = append(errorValidate, messageError)
		} else {
			qrData.FromAccount = cutStringFromaccountNumber
		}
		if qrData.FromAccount == user.BankAccount {
			passValidate = true
		} else {
			if !strings.Contains(user.BankAccount, qrData.FromAccount) {
				passValidate = false
				messageError := fmt.Sprintf("user.BankAccount:%s กับ qrData.FromAccount:%s ไม่ตรง", user.BankAccount, qrData.FromAccount)
				errorValidate = append(errorValidate, messageError)
			}

			fromAccountNamePart := strings.Split(qrData.FromAccountName, " ")
			var fromAccountNameCheck string
			if len(fromAccountNamePart) > 1 {
				bankAccountNameCheck = fromAccountNamePart[1]
			} else {
				bankAccountNameCheck = fromAccountNamePart[0]
			}
			if !strings.Contains(user.Fullname, fromAccountNameCheck) {
				passValidate = false
				messageError := fmt.Sprintf("user.Fullname:%s กับ qrData.FromAccountName:%s ไม่ตรง", user.Fullname, qrData.FromAccountName)
				errorValidate = append(errorValidate, messageError)
			}
		}
		if user.BankName != qrData.FromBankName {
			passValidate = false
			messageError := fmt.Sprintf("user.BankName:%s กับ qrData.FromBankName:%s ไม่ตรง", user.BankName, qrData.FromBankName)
			errorValidate = append(errorValidate, messageError)
		}
		qrDataDateTime, _ := convertStringToTime(qrData.TransactionDateTime)

		failedStatus := 2
		successStatus := 3
		var createBankTransactionSlip model.BankTransactionSlipCreateRequest
		createBankTransactionSlip.UserId = body.UserId
		createBankTransactionSlip.Status = 1
		createBankTransactionSlip.TransactionId = 0
		createBankTransactionSlip.RawQrCode = &body.RawQrCode
		createBankTransactionSlip.FromAccountNumber = qrData.FromAccount
		createBankTransactionSlip.FromAccountName = qrData.FromAccountName
		createBankTransactionSlip.FromBankName = qrData.FromBankName
		createBankTransactionSlip.ToAccountNumber = qrData.ToAccount
		createBankTransactionSlip.ToAccountName = qrData.ToAccountName
		createBankTransactionSlip.Amount = qrData.Amount
		createBankTransactionSlip.TransactionDate = qrDataDateTime
		createBankTransactionSlip.Remark = "PENDING"

		createSlipId, err := s.repoBanking.CreateBankTransactionSlip(createBankTransactionSlip)
		if err != nil {
			createBankTransactionSlip.RawQrCode = nil
			createBankTransactionSlip.Remark = "SCAMMER"
			createBankTransactionSlip.Status = failedStatus
			s.repoBanking.CreateBankTransactionSlip(createBankTransactionSlip)
			return nil, badRequest("DEPOSIT_SLIP_CREATE_FAILED")
		}

		var failErrorMessage string
		for _, v := range errorValidate {
			failErrorMessage += v + " และ "
		}

		if !passValidate {
			var updateBankTransactionSlip model.BankTransactionSlipUpdateRequest
			updateBankTransactionSlip.Id = createSlipId
			updateBankTransactionSlip.Status = &failedStatus
			updateBankTransactionSlip.Remark = &failErrorMessage

			if err := s.repoBanking.UpdateBankTransactionSlip(updateBankTransactionSlip); err != nil {
				return nil, internalServerError(err)
			}
			return nil, badRequest("SLIP_VALIDATE_FAILED")
		}

		var validateBody model.ValidateQrDataWithBankTransactionBody
		validateBody.FromBankId = &user.BankId
		validateBody.ToBankAccountId = bankAccount.Id
		validateBody.TransferAmount = body.Amount
		validateBody.FromBankAccount = cutStringFromaccountNumber
		validateBody.TransferDate = qrDataDateTime
		possibleStatement, total, err := s.repoBanking.FindUserDepositInBankTransaction(validateBody)
		if err == nil && total == 1 {
			for _, v := range possibleStatement {
				// update the Bank Transaction by v id
				if v.TransactionStatusId == model.TRANS_STATUS_PENDING {

					var updateBankTransaction model.BankStatementMatchRequest
					updateBankTransaction.StatementId = v.StatementId
					updateBankTransaction.UserId = body.UserId
					updateBankTransaction.ConfirmedAt = time.Now()
					var setIdAuto int64 = 0
					updateBankTransaction.ConfirmedByAdminId = &setIdAuto

					if err := s.MatchStatementOwner(updateBankTransaction); err != nil {
						return nil, internalServerError(err)
					}
					// if err := s.repoBanking.UpdateUserTransactionStatementId(********, v.StatementId); err != nil {
					// 	return nil, internalServerError(err)
					// }
					if err := s.repoBanking.UpdateFromBankAccount(v.StatementId, user.BankAccount); err != nil {
						return nil, internalServerError(err)
					}

					var updateBankTransactionSlip model.BankTransactionSlipUpdateRequest
					updateBankTransactionSlip.Id = createSlipId
					updateBankTransactionSlip.Status = &successStatus
					createBankTransactionSlip.TransactionId = v.StatementId
					remark := fmt.Sprintf("MATCHED_ADMIN_TRANSACTION_OR_WEBHOOK %d", v.Id)
					updateBankTransactionSlip.Remark = &remark

					if err := s.repoBanking.UpdateBankTransactionSlip(updateBankTransactionSlip); err != nil {
						return nil, internalServerError(err)
					}

					return nil, nil

				} else {
					var updateBankTransactionSlip model.BankTransactionSlipUpdateRequest
					updateBankTransactionSlip.Id = createSlipId
					updateBankTransactionSlip.Status = &successStatus
					createBankTransactionSlip.TransactionId = v.StatementId
					remark := fmt.Sprintf("ALREADY_MATCHED WITH STATEMENT ID %d", v.Id)
					updateBankTransactionSlip.Remark = &remark

					if err := s.repoBanking.UpdateBankTransactionSlip(updateBankTransactionSlip); err != nil {
						return nil, internalServerError(err)
					}
					return nil, badRequest("ALREADY_MATCHED")
				}
			}
		} else {

			if user.MemberCode == "" {
				memberCode, err := s.serviceUser.GenUniqueUserMemberCode(user.Id)
				if err != nil {
					log.Println("UserDepositBySlip.GenUniqueUserMemberCode", err)
					return nil, internalServerError(err)
				}
				user.MemberCode = *memberCode
			}
			member, err := s.repoBanking.GetMemberById(user.Id)
			if err != nil {
				return nil, internalServerError(err)
			}
			// Affiliate + Alliance Income
			totalAmount := body.Amount
			if member.UserTypeName == "NONE" {
				// ถ้าฝากครั้งแรก จะได้เป็น AFFILIATE + REFER COMMISION
				if err := s.serviceAccounting.UserFirstDepositCommission(*member, totalAmount); err != nil {
					log.Println("UserDepositBySlip.UserFirstDepositCommission.ERROR", err)
					return nil, internalServerError(err)
				}
			} else if member.UserTypeName == "ALLIANCE" {
				// REFER COMMISION
				if err := s.serviceAl.NoUseAlUpdateCommission(member.Id, totalAmount); err != nil {
					log.Println("UserDepositBySlip.NoUseAlUpdateCommission.ERROR", err)
					return nil, internalServerError(err)
				}
			}

			var checkDuplicate model.CheckDuplicateWebhookAndAdminRecord
			checkDuplicate.FromAccountNumber = user.BankAccount
			checkDuplicate.FromBankId = user.BankId
			checkDuplicate.Amount = body.Amount
			checkDuplicate.TransactionAt = qrDataDateTime
			checkDuplicate.CheckFromWhere = "ADMIN"
			checkDuplicate.MemberCode = user.MemberCode
			checkDuplicate.ToBankId = &bankAccount.BankId
			duplicateFromAdminRecord, _ := s.repoAccounting.CheckDuplicateWebhookAndAdminRecord2(checkDuplicate)
			if duplicateFromAdminRecord.Id != 0 {
				statusLog := "SUCCESS"
				jsonPayLoad := fmt.Sprintf("DUPLICATE WITH ADMIN BANK TRANS ID: %v ", duplicateFromAdminRecord.Id)
				jsonRequest := helper.StructJson(body)
				logType := "DUPLICATE_ADMIN_WITH_WEBHOOK"
				var createLog model.BankTransactionLogCreateRequest
				createLog.Status = &statusLog
				createLog.JsonPayload = &jsonPayLoad
				createLog.JsonRequest = &jsonRequest
				createLog.LogType = &logType

				if _, err := s.serviceAccounting.CreateBankTransactionLog(createLog); err != nil {
					return nil, nil
				}

				log.Println("UserDepositBySlip.duplicateFromRecord", helper.StructJson(duplicateFromAdminRecord))
				return nil, badRequest("DUPLICATE_WITH_ADMIN_RECORD")
			}
			//[30/11/2023] ไม่ต้องดักเงื่อนไขนี้แล้ว
			// var minDeposit int64
			// config, err := s.repoBanking.GetConfiguration()
			// if err == nil {
			// 	if config.MinimumDeposit == 0 {
			// 		minDeposit = 100
			// 	} else {
			// 		minDeposit = config.MinimumDeposit
			// 	}
			// 	if minDeposit > int64(req.CreditAmount) {
			// 		return nil, badRequest("LESS_THAN_CONFIG")
			// 	}
			// }
			// race condition deposit

			// create statement
			var createStatement model.BankStatementCreateBody
			createStatement.Amount = body.Amount
			createStatement.AccountId = bankAccount.Id
			createStatement.Detail = fmt.Sprintf("%s:%s => %s:%s", user.BankName, user.BankAccount, bankAccount.BankName, bankAccount.AccountName)
			createStatement.FromBankId = user.BankId
			createStatement.FromAccountNumber = user.BankAccount
			createStatement.StatementTypeId = model.STATEMENT_TYPE_TRANSFER_IN
			createStatement.TransferAt = qrDataDateTime
			createStatement.StatementStatusId = model.STATEMENT_STATUS_PENDING
			bankstatementId, err := s.repoBanking.CreateBankStatement(createStatement)
			if err != nil {
				log.Println("UserDepositBySlip.CreateBankStatement", err)
				return nil, internalServerError(err)
			}

			var externalNoti model.NotifyExternalNotificationRequest
			var transId *int64
			// if req.Auto {
			var promotionWebUserId int64
			GetUserPromotion, _ := s.promotionWebService.GetDepositCurrentProcessingUserPromotion(user.Id)
			if GetUserPromotion != nil {
				promotionWebUserId = GetUserPromotion.Id
			}
			var createBankTransaction model.BankTransactionCreateBody
			createBankTransaction.MemberCode = user.MemberCode
			createBankTransaction.UserId = user.Id
			createBankTransaction.StatementId = bankstatementId
			createBankTransaction.TransactionTypeId = model.TRANSACTION_TYPE_DEPOSIT
			createBankTransaction.FromAccountNumber = &user.BankAccount
			createBankTransaction.PromotionId = &promotionWebUserId
			createBankTransaction.FromBankId = &user.BankId
			createBankTransaction.ToAccountId = &bankAccount.Id
			createBankTransaction.ToAccountNumber = &bankAccount.AccountNumber
			createBankTransaction.ToBankId = &bankAccount.BankId
			createBankTransaction.ToAccountNumber = &bankAccount.AccountNumber
			createBankTransaction.CreditAmount = body.Amount
			createBankTransaction.DepositChannel = "SLIP" // แก้แล้วไปแก้ที่ CheckDuplicateWebhookAndAdminRecord
			createBankTransaction.TransactionStatusId = model.TRANS_STATUS_PENDING
			createBankTransaction.TransferAt = &qrDataDateTime
			// createBankTransaction.IsAutoCredit = true แอดมินเป็นคนกด
			//2/10/66
			// create transaction
			transId, err = s.repoBanking.InsertBankTransaction(createBankTransaction)
			if err != nil {
				log.Println("UserDepositBySlip.InsertBankTransaction.ERROR=", err)
				return nil, internalServerError(err)
			}
			actionDateTime := time.Now()
			var actionStatement model.BankStatementMatchRequest
			actionStatement.StatementId = *bankstatementId
			actionStatement.UserId = user.Id
			actionStatement.ConfirmedAt = actionDateTime
			setIdAuto := int64(0)
			actionStatement.ConfirmedByAdminId = &setIdAuto
			if err := s.serviceAccounting.SetStatementOwnerMatched(*bankstatementId, actionStatement, model.USE_ENDING_NOTI); err != nil {
				log.Println("UserDepositBySlip.SetStatementOwnerMatched", err)
				return nil, internalServerError(err)
			}

			// DECREASE FASTBANK CREDIT 2.FROM create auto DEPOSIT
			if err := s.repoAccounting.DecreaseFastbankCredit(1); err != nil {
				log.Println("UserDepositBySlip.DECREASE_FASTBANK_CREDIT_ERROR", err)
			}

			var confirmDeposit model.BankConfirmDepositRequest
			confirmDeposit.TransferAt = &qrDataDateTime
			confirmDeposit.ConfirmedAt = actionDateTime
			confirmDeposit.ConfirmedByAdminId = &setIdAuto
			if err := s.ConfirmDepositTransaction(*transId, confirmDeposit); err != nil {
				log.Println("UserDepositBySlip.ConfirmDepositTransaction", err)
				return nil, internalServerError(err)
			}
			// [promotion]
			var userCreditReq model.UserTransactionCreateRequest
			userCreditReq.UserId = user.Id
			userCreditReq.RefId = transId
			userCreditReq.TransferAt = &qrDataDateTime
			userCreditReq.PromotionId = &promotionWebUserId
			userCreditReq.TypeId = model.CREDIT_TYPE_DEPOSIT
			userCreditReq.AccountId = &bankAccount.Id
			userCreditReq.Detail = fmt.Sprintf("%s:%s => %s:%s", user.BankName, user.BankAccount, bankAccount.BankName, bankAccount.AccountName)
			userCreditReq.Amount = body.Amount
			userCreditReq.CreateBy = &setIdAuto
			userCreditReq.ConfirmBy = &setIdAuto
			if agentResp, err := s.repoAccounting.IncreaseUserCredit(userCreditReq); err != nil {
				log.Println("UserDepositBySlip.IncreaseUserCredit", err)
				if bankstatementId != nil {
					var updateStatement model.BankStatementUpdateBody
					updateStatement.Id = *bankstatementId
					updateStatement.StatementStatusId = model.STATEMENT_STATUS_PENDING
					if err := s.repoAccounting.UpdateBankStatementStatus(updateStatement); err != nil {
						log.Println("UserDepositBySlip.UpdateBankStatementStatus", err)
					}
				}
				return nil, internalServerError(err)
			} else {
				// FASTBANK_SUCCESS
				if err := s.repoAccounting.UpdateDepositTransactionStatusFromAgent(*transId, *agentResp); err != nil {
					log.Println("UserDepositBySlip.UpdateDeporsitTransactionStatusFromAgent", err)
				}
				// Set FastBank-READ ** ไม่มี เพราะเป็นการสร้างรายการฝากเอง
			}

			isFirstDeposit := s.repoBanking.IsFirstDeposit(user.Id)
			if isFirstDeposit {
				var createFirstDeposit model.UserFirstDepositCreateBody
				createFirstDeposit.UserId = user.Id
				createFirstDeposit.TransferAt = qrDataDateTime
				createFirstDeposit.Amount = body.Amount
				createFirstDeposit.Remark = "UserDepositBySlip"
				if _, err := s.repoBanking.SetUserFirstDeposit(createFirstDeposit, transId); err != nil {
					log.Println("UserDepositBySlip.SetUserFirstDeposit", err)
				}
			}

			// [Lucky Wheel] ตอนฝากละได้โบนัส
			var luckyWheelBody model.ActivityLuckyWheelRoundUserRequest
			luckyWheelBody.UserId = user.Id
			luckyWheelBody.ConditionId = model.ACTIVITY_LUCKY_WHEEL_SETTING_CONDITION_DEPOSIT
			luckyWheelBody.ConditionAmount = body.Amount
			if err := s.activityLuckyWheelService.CreateRoundActivityLuckyWheel(luckyWheelBody); err != nil {
				log.Println("UserDepositBySlip.CreateRoundActivityLuckyWheel", err)
			}

			// [TIER]
			if err := s.repoAccounting.IncreaseUserTierDepositAmount(user.Id, body.Amount); err != nil {
				log.Println("MatchStatementOwner.IncreaseUserTierDepositAmount", err)
			}

			// [turnOver] ถ้ามีการฝากเงิน ให้สร้าง TurnOver
			if err := CreateTurnOverFromSuccessDeposit(repository.NewTurnoverRepository(s.SharedDb), user.Id, body.Amount, *transId); err != nil {
				log.Println("CreateBankStatementFromWebhookAndAuto.CreateTurnOverFromSuccessDeposit", err)
			}
			var checkUserPromotionBody model.CheckUserPromotionBody
			checkUserPromotionBody.UserId = user.Id
			if _, err = s.promotionWebService.CheckUserPromotion(checkUserPromotionBody); err != nil {
				log.Println("UserDepositBySlip.CheckUserPromotion", err)
			}

			// } else {
			// 	var createBankTransaction model.BankTransactionCreateBody
			// 	createBankTransaction.MemberCode = user.MemberCode
			// 	createBankTransaction.UserId = user.Id
			// 	createBankTransaction.StatementId = bankstatementId
			// 	createBankTransaction.TransactionTypeId = model.TRANSACTION_TYPE_DEPOSIT
			// 	createBankTransaction.FromBankId = &user.BankId
			// 	createBankTransaction.FromAccountNumber = &user.BankAccount
			// 	createBankTransaction.ToAccountId = &bankAccount.Id
			// 	createBankTransaction.ToAccountNumber = &bankAccount.AccountNumber
			// 	createBankTransaction.ToBankId = &bankAccount.BankId
			// 	createBankTransaction.ToAccountNumber = &bankAccount.AccountNumber
			// 	createBankTransaction.CreditAmount = req.CreditAmount
			// 	createBankTransaction.BonusAmount = req.BonusAmount
			// 	createBankTransaction.BonusReason = &req.BonusReason
			// 	createBankTransaction.DepositChannel = req.DepositChannel
			// 	createBankTransaction.TransactionStatusId = model.TRANS_STATUS_PENDING
			// 	createBankTransaction.CreatedByAdminId = req.CreateByUserId
			// 	createBankTransaction.TransferAt = &req.TransferAt
			// 	//2/10/66 || // 25/12/2023 ต้องการ แจ้ง เตือนทั้ง 2 แบบ เหมือนเดิม
			// 	// create transaction
			// 	transId, err = s.repoBanking.CreateBankTransaction(createBankTransaction)
			// 	if err != nil {
			// 		return nil, internalServerError(err)
			// 	}

			// 	externalNoti.TypeNotify = model.IsDepositBeforeCredit
			// 	externalNoti.TransId = transId
			// 	externalNoti.Amount = req.CreditAmount
			// 	externalNoti.MemberCode = user.MemberCode
			// 	externalNoti.BonusCredit = &req.BonusAmount
			// 	externalNoti.UserCredit = user.Credit + req.CreditAmount
			// 	externalNoti.ConfirmedByAdminId = req.CreateByUserId
			// }

			// [ Notify]
			endProcess := time.Now()
			// [line notify]
			externalNoti.TypeNotify = model.IsDepositAfterCredit
			externalNoti.TransId = transId
			externalNoti.Amount = body.Amount
			externalNoti.MemberCode = user.MemberCode
			externalNoti.UserCredit = user.Credit + body.Amount
			externalNoti.ConfirmedByAdminId = setIdAuto
			externalNoti.TransferDateTime = qrDataDateTime.Format("2006-01-02 15:04:05")
			externalNoti.ActionTime = time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02 15:04:05")

			externalNoti.WebScoket.UserID = user.Id
			externalNoti.WebScoket.Amount = body.Amount
			externalNoti.WebScoket.MemberCode = user.MemberCode
			externalNoti.WebScoket.AlertType = "DEPOSIT"
			elapsed := endProcess.Sub(startProcess)
			elapsedSeconds := elapsed.Seconds()

			timeElapsed := fmt.Sprintf("%.2f", elapsedSeconds)

			if err := s.serviceNoti.ExternalNotification(externalNoti); err != nil {
				log.Println("FailedNotify", err)
			}
			if err := s.repoBanking.UpdateAutoProcessTimer(timeElapsed, *transId); err != nil {
				return nil, nil
			}

			// var socketMessage model.WebScoket
			// socketMessage.UserID = user.Id
			// socketMessage.Amount = body.Amount
			// socketMessage.MemberCode = user.MemberCode
			// socketMessage.AlertType = "DEPOSIT"
			// if err := s.repoAccounting.WebSocket(socketMessage); err != nil {
			// 	return nil, nil
			// }
			var updateBankTransactionSlip model.BankTransactionSlipUpdateRequest
			updateBankTransactionSlip.Id = createSlipId
			updateBankTransactionSlip.Status = &successStatus
			createBankTransactionSlip.TransactionId = *transId
			remark := fmt.Sprintf("ALREADY_MATCHED WITH STATEMENT ID %d", *transId)
			updateBankTransactionSlip.Remark = &remark

			if err := s.repoBanking.UpdateBankTransactionSlip(updateBankTransactionSlip); err != nil {
				return nil, internalServerError(err)
			}

		}
	}
	return nil, nil
}

func extractAndFilterAccountNumber(fromAccount string) (string, error) {
	// Remove all hyphens from the string
	fromAccount = strings.ReplaceAll(fromAccount, "-", "")

	// Define a regular expression to match digits with at least 4 digits
	re := regexp.MustCompile(`\d{4,}`)

	// Find all matches of the pattern in the fromAccount string
	matches := re.FindAllString(fromAccount, -1)

	// If no match is found, return an empty string
	if matches == nil {
		return "", nil
	}

	// Concatenate the matched numeric sequences into a single string
	result := ""
	for _, match := range matches {
		result += match
	}
	return result, nil
}

func cutNamePrefix(name string) string {
	prefixes := []string{"นาย", "นาง", "นางสาว"}

	// Split the name into words
	words := strings.Fields(name)

	// Check only the first word for prefixes
	if len(words) > 0 {
		for _, prefix := range prefixes {
			if strings.HasPrefix(words[0], prefix) {
				// Remove the prefix from the first word and trim leading space
				words[0] = strings.TrimSpace(strings.TrimPrefix(words[0], prefix))
				break
			}
		}
	}

	// Join the words back into a single string and trim leading/trailing spaces
	return strings.TrimSpace(strings.Join(words, " "))
}

func convertStringToTime(dateTimeStr string) (time.Time, error) {
	// Parse the string into a time.Time value
	return time.Parse(time.RFC3339, dateTimeStr)
}

func (s *bankingService) CheckScammerSlipRequest(req model.CheckScammerSlipRequest) *model.CheckScammerSlipResponse {

	record, err := s.repoBanking.CheckScammerSlipRequest(req)
	if err != nil {
		return nil
	}

	return record
}

func (s *bankingService) CreateTurnOverFromBonus(userId int64, bonusAmount float64, transactionId int64) error {

	// GetTurnoverSetting() (*model.TurnoverSettingResponse, error)
	// serviceTurnover
	checkTurn, err := s.serviceTurnover.GetTurnoverSetting()
	if err != nil {
		return nil
	}
	if checkTurn.TidturnManualBonusPercent > 0 {

		tidTurn := (bonusAmount * float64(checkTurn.TidturnManualBonusPercent) / 100)

		tidTurn = math.Ceil(tidTurn)

		createdTime := time.Now().UTC()
		var createBody model.TurnoverUserStatementCreateBody
		createBody.UserId = userId
		createBody.RefTypeId = transactionId
		createBody.TypeId = model.TURN_BONUS_BY_ADMIN
		createBody.Name = model.TURNOVER_CATE_BONUS
		createBody.PromotionName = "บันทึกแจกโบนัส"
		createBody.BonusAmount = bonusAmount
		createBody.StatusId = model.TURNOVER_STATEMENT_STATUS_PENDING
		createBody.StartTurnAmount = tidTurn
		createBody.StartTurnAt = &createdTime
		createBody.TotalTurnAmount = tidTurn
		if _, err := s.repoBanking.CreateTurnoverUserStatement(createBody); err != nil {
			return err
		}
	} else {
		createdTime := time.Now().UTC()
		var createBody model.TurnoverUserStatementCreateBody
		createBody.UserId = userId
		createBody.RefTypeId = transactionId
		createBody.TypeId = model.TURN_BONUS_BY_ADMIN
		createBody.Name = model.TURNOVER_CATE_BONUS
		createBody.PromotionName = "บันทึกแจกโบนัส"
		createBody.BonusAmount = 0
		createBody.StatusId = model.TURNOVER_STATEMENT_STATUS_PENDING
		createBody.StartTurnAmount = 0
		createBody.StartTurnAt = &createdTime
		createBody.TotalTurnAmount = 0
		craeteTurnId, err := s.repoBanking.CreateTurnoverUserStatement(createBody)
		if err != nil {
			return err
		}

		var updateTurnoverUserStatement model.TurnoverUserStatementUpdateBody
		setTotalTurnAmount := 0.0
		setTimeTurnAt := time.Now().UTC()
		updateTurnoverUserStatement.StatusId = model.TURNOVER_STATEMENT_STATUS_COMPLETED
		updateTurnoverUserStatement.TotalTurnAmount = &setTotalTurnAmount
		updateTurnoverUserStatement.EndTurnAt = &setTimeTurnAt
		if err := s.repoBanking.UpdateTurnoverUserStatement(*craeteTurnId, updateTurnoverUserStatement); err != nil {
			log.Println("UpdateTurnoverUserStatement", err)
		}

		// create turnover withdraw log
		var createTurnoverWithDrawLog model.CreateTurnoverUserWithdrawLog
		createTurnoverWithDrawLog.UserId = userId
		createTurnoverWithDrawLog.LogKey = fmt.Sprintf("BONUS_BY_ADMIN_U%d_D%s", userId, time.Now().UTC().Format("************05"))
		createTurnoverWithDrawLog.TotalWithdrawPrice = 0
		createTurnoverWithDrawLog.CurrentTurn = 0
		createTurnoverWithDrawLog.PlayTotal = 0
		createTurnoverWithDrawLog.LastPlayY = 0
		createTurnoverWithDrawLog.LastTotalX = 0
		if _, err = s.repoBanking.CreateTurnoverUserWithdrawLog(createTurnoverWithDrawLog); err != nil {
			log.Println("CheckCouponTurnOverWithdraw.CreateTurnoverUserWithdrawLog", err)
		}

	}

	return nil
}
func (s *bankingService) CheckOtherExistTurnOver(userId int64) (string, float64, error) {

	return CheckOtherExistTurnOver(s.repoBanking, userId)

}

func CheckOtherExistTurnOver(repoBanking repository.BankingRepository, userId int64) (string, float64, error) {

	chcekPassTurnOver := "PASS_OHTER_TURN_OVER"
	var turnCalculate float64
	passAllTurnOver := false

	getUser, err := repoBanking.GetUser(userId)
	if err != nil {
		log.Println("CheckOtherExistTurnOver.GetUser", err)
		return chcekPassTurnOver, turnCalculate, err
	}

	configuration, _ := repoBanking.GetConfiguration()
	if configuration != nil && getUser.Credit < configuration.ClearTurnCreditLess {
		passAllTurnOver = true
	}
	fmt.Println("gpassAllTurnOver", passAllTurnOver)
	if !passAllTurnOver {

		// UserTurnOverPendingList(req model.GetUserTurnOverStartmentListRequest) (*model.SuccessWithPagination, error)
		var getTurnOverPendingRequestCheck model.GetUserTurnOverStartmentListRequest
		getTurnOverPendingRequestCheck.UserId = userId
		// func UserTurnOverPendingList(repo repository.TurnoverRepository, req model.GetUserTurnOverStartmentListRequest) (*model.GetUserTurnOverStartmentListResponse, error)
		getTurnOverPendingList, err := UserTurnOverPendingList(repository.NewTurnoverRepository(repoBanking.GetDb()), getTurnOverPendingRequestCheck)
		if err != nil {
			log.Println("UserTurnOverPendingList", err)
			return chcekPassTurnOver, turnCalculate, err
		}

		if len(getTurnOverPendingList.List) > 0 {
			var findNotPass bool
			var needToPlayTotal float64
			for _, v := range getTurnOverPendingList.List {
				if v.NeedToPlay > 0 {
					findNotPass = true
				}
			}

			if findNotPass {
				chcekPassTurnOver := "NOT_PASS_OHTER_TURN_OVER"
				return chcekPassTurnOver, needToPlayTotal, nil
			}
		} else {
			// No Turn
			return chcekPassTurnOver, turnCalculate, nil
		}

		// to do clear all turnover
		var updateTurnoverUserStatement model.TurnoverUserStatementUpdateBody
		setTotalTurnAmount := 0.0
		setTimeTurnAtEnd := time.Now().UTC()
		updateTurnoverUserStatement.StatusId = model.TURNOVER_STATEMENT_STATUS_COMPLETED
		updateTurnoverUserStatement.TotalTurnAmount = &setTotalTurnAmount
		updateTurnoverUserStatement.EndTurnAt = &setTimeTurnAtEnd
		// if swith this func up need to change UpdateClearEveryTurnoverUserStatement but category [not switch yet]
		if err := repoBanking.UpdateClearEveryTurnoverUserStatement(userId, updateTurnoverUserStatement); err != nil {
			log.Println("UpdateTurnoverUserStatement", err)
		}

		// other exist promotion
		repoBanking.OtherPromotionClearWebUserById(userId)

		// create turnover withdraw log
		var createTurnoverWithDrawLog model.CreateTurnoverUserWithdrawLog
		createTurnoverWithDrawLog.UserId = userId
		createTurnoverWithDrawLog.LogKey = fmt.Sprintf("OTHER_TURN_OVER_CANCELED_U%d_D%s", userId, time.Now().UTC().Format("************05"))
		createTurnoverWithDrawLog.TotalWithdrawPrice = 0
		createTurnoverWithDrawLog.CurrentTurn = 0
		createTurnoverWithDrawLog.PlayTotal = 0
		createTurnoverWithDrawLog.LastPlayY = 0
		createTurnoverWithDrawLog.LastTotalX = 0
		if _, err := repoBanking.CreateTurnoverUserWithdrawLog(createTurnoverWithDrawLog); err != nil {
			log.Println("CreateTurnoverUserWithdrawLog", err)
		}

	} else {

		var getTurnOverPendingRequestCheck model.GetUserTurnOverStartmentListRequest
		getTurnOverPendingRequestCheck.UserId = userId
		getTurnOverPendingList, err := UserTurnOverPendingList(repository.NewTurnoverRepository(repoBanking.GetDb()), getTurnOverPendingRequestCheck)
		if err != nil {
			log.Println("UserTurnOverPendingList", err)
			return chcekPassTurnOver, turnCalculate, err
		}
		if len(getTurnOverPendingList.List) == 0 {
			return chcekPassTurnOver, turnCalculate, nil
		}

		var updateTurnoverUserStatement model.TurnoverUserStatementUpdateBody
		setTotalTurnAmount := 0.0
		setTimeTurnAt := time.Now().UTC()
		updateTurnoverUserStatement.StatusId = model.TURNOVER_STATEMENT_STATUS_COMPLETED
		updateTurnoverUserStatement.TotalTurnAmount = &setTotalTurnAmount
		updateTurnoverUserStatement.EndTurnAt = &setTimeTurnAt
		if err := repoBanking.UpdateClearEveryTurnoverUserStatement(userId, updateTurnoverUserStatement); err != nil {
			log.Println("UpdateTurnoverUserStatement", err)
		}

		// other exist promotion
		repoBanking.OtherPromotionClearWebUserById(userId)

		// create turnover withdraw log
		var createTurnoverWithDrawLog model.CreateTurnoverUserWithdrawLog
		createTurnoverWithDrawLog.UserId = userId
		createTurnoverWithDrawLog.LogKey = fmt.Sprintf("OTHER_TURN_OVER_CANCELED_U%d_D%s", userId, time.Now().UTC().Format("************05"))
		createTurnoverWithDrawLog.TotalWithdrawPrice = 0
		createTurnoverWithDrawLog.CurrentTurn = 0
		createTurnoverWithDrawLog.PlayTotal = 0
		createTurnoverWithDrawLog.LastPlayY = 0
		createTurnoverWithDrawLog.LastTotalX = 0
		if _, err := repoBanking.CreateTurnoverUserWithdrawLog(createTurnoverWithDrawLog); err != nil {

			log.Println("CreateTurnoverUserWithdrawLog", err)
		}
	}
	return chcekPassTurnOver, turnCalculate, nil
}

// func (s *bankingService) CheckOtherExistTurnOverBackUp(userId int64) (string, float64, error) {

// 	chcekPassTurnOver := "PASS_OHTER_TURN_OVER"
// 	var turnCalculate float64
// 	passAllTurnOver := false

// 	getUser, err := s.repoBanking.GetUser(userId)
// 	if err != nil {
// 		log.Println("CheckOtherExistTurnOver.GetUser", err)
// 		return chcekPassTurnOver, turnCalculate, err
// 	}

// 	configuration, _ := s.repoBanking.GetConfiguration()
// 	if configuration != nil && getUser.Credit < configuration.ClearTurnCreditLess {
// 		passAllTurnOver = true
// 	}

// 	if !passAllTurnOver {
// 		// if swith this func up need to change SumLastestTurnOver but category  [not switch yet]
// 		totalSumBeginOfdateToToday, err := s.repoBanking.SumLastestTurnOverBackUp(userId)
// 		if err != nil {
// 			// no turnover
// 			return chcekPassTurnOver, turnCalculate, nil
// 		}
// 		var setTotalTurnAmount float64
// 		var setTimeTurnAt time.Time
// 		// fmt.Println("totalSumBeginOfdateToToday", totalSumBeginOfdateToToday)
// 		if totalSumBeginOfdateToToday == nil {
// 			setTimeTurnAt = time.Now().UTC()
// 			setTotalTurnAmount = 0
// 		} else {
// 			setTimeTurnAt = totalSumBeginOfdateToToday.CheckOnDay
// 			setTotalTurnAmount = totalSumBeginOfdateToToday.TotalToplayAmount
// 		}

// 		var checkTurnSuccessOnThisDay model.CheckTurnSuccessOnThisDayRequest
// 		checkTurnSuccessOnThisDay.UserId = userId
// 		checkTurnSuccessOnThisDay.StartDate = setTimeTurnAt.Add(7 * time.Hour).Format("2006-01-02")
// 		getTurnSuccessOnThisDay, err := s.repoBanking.CheckTurnSuccessOnThisDay(checkTurnSuccessOnThisDay)
// 		if err != nil {
// 			return chcekPassTurnOver, turnCalculate, err
// 		}
// 		if getTurnSuccessOnThisDay != nil && getTurnSuccessOnThisDay.SumStartTurnAmount > 0 {
// 			turnCalculate += getTurnSuccessOnThisDay.SumStartTurnAmount
// 		}

// 		turnCalculate = turnCalculate + setTotalTurnAmount

// 		var UserTodayPlaylogListRequest model.UserTodayPlaylogListRequest
// 		UserTodayPlaylogListRequest.UserId = &userId
// 		UserTodayPlaylogListRequest.StatementDateStart = setTimeTurnAt.Add(7 * time.Hour).Format("2006-01-02")
// 		UserTodayPlaylogList, _, err := s.repoBanking.GetTodaySumUserPlayLogList(UserTodayPlaylogListRequest)
// 		if err != nil {
// 			return chcekPassTurnOver, turnCalculate, err
// 		}

// 		var userPlaylogTotal float64
// 		for _, v := range UserTodayPlaylogList {
// 			userPlaylogTotal += v.SumTurnGame + v.SumTurnCasino + v.SumTurnSport
// 		}

// 		fmt.Print("====turnCalculate total ====")
// 		fmt.Print(turnCalculate, userPlaylogTotal)
// 		if turnCalculate <= userPlaylogTotal {
// 			// to do clear all turnover
// 			var updateTurnoverUserStatement model.TurnoverUserStatementUpdateBody
// 			setTotalTurnAmount := 0.0
// 			setTimeTurnAt := time.Now().UTC()
// 			updateTurnoverUserStatement.StatusId = model.TURNOVER_STATEMENT_STATUS_COMPLETED
// 			updateTurnoverUserStatement.TotalTurnAmount = &setTotalTurnAmount
// 			updateTurnoverUserStatement.EndTurnAt = &setTimeTurnAt
// 			// if swith this func up need to change UpdateClearEveryTurnoverUserStatement but category [not switch yet]
// 			if err := s.repoBanking.UpdateClearEveryTurnoverUserStatement(userId, updateTurnoverUserStatement); err != nil {
// 				log.Println("UpdateTurnoverUserStatement", err)
// 			}

// 			// other exist promotion
// 			s.repoBanking.OtherPromotionClearWebUserById(userId)

// 			// create turnover withdraw log
// 			var createTurnoverWithDrawLog model.CreateTurnoverUserWithdrawLog
// 			createTurnoverWithDrawLog.UserId = userId
// 			createTurnoverWithDrawLog.LogKey = fmt.Sprintf("OTHER_TURN_OVER_CANCELED_U%d_D%s", userId, time.Now().UTC().Format("************05"))
// 			createTurnoverWithDrawLog.TotalWithdrawPrice = 0
// 			createTurnoverWithDrawLog.CurrentTurn = 0
// 			createTurnoverWithDrawLog.PlayTotal = 0
// 			createTurnoverWithDrawLog.LastPlayY = 0
// 			createTurnoverWithDrawLog.LastTotalX = 0
// 			_, err := s.repoBanking.CreateTurnoverUserWithdrawLog(createTurnoverWithDrawLog)
// 			if err != nil {

// 				log.Println("CreateTurnoverUserWithdrawLog", err)
// 			}

// 		} else {
// 			chcekPassTurnOver = "NOT_PASS_OHTER_TURN_OVER"
// 			needToPlay := turnCalculate - userPlaylogTotal
// 			return chcekPassTurnOver, needToPlay, nil
// 		}
// 	} else {
// 		var updateTurnoverUserStatement model.TurnoverUserStatementUpdateBody
// 		setTotalTurnAmount := 0.0
// 		setTimeTurnAt := time.Now().UTC()
// 		updateTurnoverUserStatement.StatusId = model.TURNOVER_STATEMENT_STATUS_COMPLETED
// 		updateTurnoverUserStatement.TotalTurnAmount = &setTotalTurnAmount
// 		updateTurnoverUserStatement.EndTurnAt = &setTimeTurnAt
// 		if err := s.repoBanking.UpdateClearEveryTurnoverUserStatement(userId, updateTurnoverUserStatement); err != nil {
// 			log.Println("UpdateTurnoverUserStatement", err)
// 		}

// 		// other exist promotion
// 		s.repoBanking.OtherPromotionClearWebUserById(userId)

// 		// create turnover withdraw log
// 		var createTurnoverWithDrawLog model.CreateTurnoverUserWithdrawLog
// 		createTurnoverWithDrawLog.UserId = userId
// 		createTurnoverWithDrawLog.LogKey = fmt.Sprintf("OTHER_TURN_OVER_CANCELED_U%d_D%s", userId, time.Now().UTC().Format("************05"))
// 		createTurnoverWithDrawLog.TotalWithdrawPrice = 0
// 		createTurnoverWithDrawLog.CurrentTurn = 0
// 		createTurnoverWithDrawLog.PlayTotal = 0
// 		createTurnoverWithDrawLog.LastPlayY = 0
// 		createTurnoverWithDrawLog.LastTotalX = 0
// 		_, err := s.repoBanking.CreateTurnoverUserWithdrawLog(createTurnoverWithDrawLog)
// 		if err != nil {

// 			log.Println("CreateTurnoverUserWithdrawLog", err)
// 		}
// 	}
// 	return chcekPassTurnOver, turnCalculate, nil
// }

func (s *bankingService) CanceledAllTurn(req model.ClearAllTurnOver) error {

	// [ADMIN_LOG] Add Log
	var createBody model.AdminLogCreateBody
	createBody.Name = "EmptyUserTurnList"
	createBody.AdminId = req.AdminId
	createBody.JsonReq = helper.StructJson(req)
	if _, err := s.repoBanking.CreateAdminLog(createBody); err != nil {
		log.Println("EmptyUserTurnList.CreateAdminLog.ERROR", err)
	}

	// [Check Promotion Turn Over Withdraw]
	s.promotionWebService.CanceledAllTurnPromotion(req.UserId)

	// [Check Coupon Turn Over Withdraw]
	s.couponCashService.CanceledAllTurnCoupon(req.UserId)

	// [clear turn over อื่นๆ ]
	var updateTurnoverUserStatement model.TurnoverUserStatementUpdateBody
	setTotalTurnAmount := 0.0
	setTimeTurnAt := time.Now().UTC()
	updateTurnoverUserStatement.StatusId = model.TURNOVER_STATEMENT_STATUS_CANCELED
	updateTurnoverUserStatement.TotalTurnAmount = &setTotalTurnAmount
	updateTurnoverUserStatement.EndTurnAt = &setTimeTurnAt
	if err := s.repoBanking.UpdateClearEveryTurnoverUserStatement(req.UserId, updateTurnoverUserStatement); err != nil {
		log.Println("UpdateTurnoverUserStatement", err)
	}

	// create turnover withdraw log
	var createTurnoverWithDrawLog model.CreateTurnoverUserWithdrawLog
	createTurnoverWithDrawLog.UserId = req.UserId
	createTurnoverWithDrawLog.LogKey = fmt.Sprintf("OTHER_TURN_OVER_CANCELED_U%d_D%s", req.UserId, time.Now().UTC().Format("************05"))
	createTurnoverWithDrawLog.TotalWithdrawPrice = 0
	createTurnoverWithDrawLog.CurrentTurn = 0
	createTurnoverWithDrawLog.PlayTotal = 0
	createTurnoverWithDrawLog.LastPlayY = 0
	createTurnoverWithDrawLog.LastTotalX = 0
	_, err := s.repoBanking.CreateTurnoverUserWithdrawLog(createTurnoverWithDrawLog)
	if err != nil {

		log.Println("CreateTurnoverUserWithdrawLog", err)
	}
	return nil
}

func (s *bankingService) EmptyUserTurnList(req model.EmptyUserTurnListRequest) error {

	// [ADMIN_LOG] Add Log
	var createBody model.AdminLogCreateBody
	createBody.Name = "EmptyUserTurnList"
	createBody.AdminId = req.AdminId
	createBody.JsonReq = helper.StructJson(req)
	if _, err := s.repoBanking.CreateAdminLog(createBody); err != nil {
		log.Println("EmptyUserTurnList.CreateAdminLog.ERROR", err)
	}

	for _, userId := range req.UserId {
		// [Check Promotion Turn Over Withdraw]
		s.promotionWebService.CanceledAllTurnPromotion(userId)

		// [Check Coupon Turn Over Withdraw]
		s.couponCashService.CanceledAllTurnCoupon(userId)

		// [clear turn over อื่นๆ ]
		var updateTurnoverUserStatement model.TurnoverUserStatementUpdateBody
		setTotalTurnAmount := 0.0
		setTimeTurnAt := time.Now().UTC()
		updateTurnoverUserStatement.StatusId = model.TURNOVER_STATEMENT_STATUS_CANCELED
		updateTurnoverUserStatement.TotalTurnAmount = &setTotalTurnAmount
		updateTurnoverUserStatement.EndTurnAt = &setTimeTurnAt
		if err := s.repoBanking.UpdateClearEveryTurnoverUserStatement(userId, updateTurnoverUserStatement); err != nil {
			log.Println("EmptyUserTurnList.UpdateTurnoverUserStatement", err)
		}

		// create turnover withdraw log
		var createTurnoverWithDrawLog model.CreateTurnoverUserWithdrawLog
		createTurnoverWithDrawLog.UserId = userId
		createTurnoverWithDrawLog.LogKey = fmt.Sprintf("OTHER_TURN_OVER_CANCELED_U%d_D%s", userId, time.Now().UTC().Format("************05"))
		createTurnoverWithDrawLog.TotalWithdrawPrice = 0
		createTurnoverWithDrawLog.CurrentTurn = 0
		createTurnoverWithDrawLog.PlayTotal = 0
		createTurnoverWithDrawLog.LastPlayY = 0
		createTurnoverWithDrawLog.LastTotalX = 0
		if _, err := s.repoBanking.CreateTurnoverUserWithdrawLog(createTurnoverWithDrawLog); err != nil {
			log.Println("EmptyUserTurnList.CreateTurnoverUserWithdrawLog", err)
		}
	}
	return nil
}

func (s *bankingService) CreateDepositLaosBankRecord(req model.CreateDepositLaosBankRecordRequest) (*int64, error) {

	transferAt := time.Now().UTC() // confirm จาก พี่่เลย์ใน discord 2024/05/10
	startProcess := time.Now().UTC()
	// [********]fastbank credit check balance to continue unuse func or not auto
	getWebLocal, _ := s.repoBanking.GetLocalWebInfo()
	if getWebLocal != nil {
		if getWebLocal.FastbankCreditBalance <= -1000 {
			log.Println("CreateDepositLaosBankRecord.WEB_OUT_OF_CREDIT")
			return nil, badRequest("WEB_OUT_OF_CREDIT_CONTRACT_ADMIN")
		}
	}
	member, err := s.repoBanking.GetMemberById(req.UserId)
	if err != nil {
		return nil, internalServerError(err)
	}
	if member.MemberCode == "" {
		memberCode, err := s.serviceUser.GenUniqueUserMemberCode(member.Id)
		if err != nil {
			log.Println("CreateDepositLaosBankRecord.GenUniqueUserMemberCode", err)
			return nil, badRequest("GEN_MEMBER_CODE_ERROR")
		}
		member.MemberCode = *memberCode
	}

	bankAccount, err := s.repoBanking.GetBankAccountById(req.ToAccountId)
	if err != nil {
		log.Println("CreateDepositLaosBankRecord.GetBankAccountById", err, req.ToAccountId)
		return nil, badRequest("TO_BANK_ACCOUNT_NOT_FOUND")
	}

	var createBankTransaction model.BankTransactionCreateBody

	setCurrencyAmount := req.CreditAmount
	if bankAccount.BankUseCurrency == "LAK" {
		createBankTransaction.CurrencyAmount = &setCurrencyAmount
		getLaos, err := s.repoBanking.GetLaosExchangeCurrency()
		if err != nil {
			log.Println("CreateDepositLaosBankRecord.GetLaosExchangeCurrency", err)
		}
		if getLaos.ExchangeRate > 0 {
			req.CreditAmount = req.CreditAmount / getLaos.ExchangeRate
		}
	}

	totalAmount := req.CreditAmount

	// Affiliate + Alliance Income
	if member.UserTypeName == "NONE" {
		// ถ้าฝากครั้งแรก จะได้เป็น AFFILIATE + REFER COMMISION
		if err := s.serviceAccounting.UserFirstDepositCommission(*member, totalAmount); err != nil {
			log.Println("CreateDepositLaosBankRecord.UserFirstDepositCommission.ERROR", err)
			return nil, internalServerError(err)
		}
	} else if member.UserTypeName == "ALLIANCE" {
		// REFER COMMISION
		if err := s.serviceAl.NoUseAlUpdateCommission(member.Id, totalAmount); err != nil {
			log.Println("CreateDepositLaosBankRecord.AlUpdateCommission.ERROR", err)
			return nil, internalServerError(err)
		}
	}

	var checkDuplicate model.CheckDuplicateWebhookAndAdminRecord
	checkDuplicate.FromAccountNumber = member.BankAccount
	checkDuplicate.FromBankId = member.BankId
	checkDuplicate.Amount = req.CreditAmount
	checkDuplicate.TransactionAt = transferAt
	checkDuplicate.CheckFromWhere = "ADMIN" // to do ยังไม่ชัวร์ เพราะยเรื่องเวลา P.Lay บอกให้ ทำ แบบนี้
	checkDuplicate.MemberCode = member.MemberCode
	checkDuplicate.ToBankId = &bankAccount.BankId
	duplicateFromAdminRecord, _ := s.repoAccounting.CheckDuplicateWebhookAndAdminRecord2(checkDuplicate)
	if duplicateFromAdminRecord.Id != 0 {
		statusLog := "SUCCESS"
		jsonPayLoad := fmt.Sprintf("DUPLICATE WITH ADMIN BANK TRANS ID: %v ", duplicateFromAdminRecord.Id)
		jsonRequest := helper.StructJson(req)
		logType := "DUPLICATE_ADMIN_WITH_WEBHOOK"
		var createLog model.BankTransactionLogCreateRequest
		createLog.Status = &statusLog
		createLog.JsonPayload = &jsonPayLoad
		createLog.JsonRequest = &jsonRequest
		createLog.LogType = &logType

		if _, err := s.serviceAccounting.CreateBankTransactionLog(createLog); err != nil {
			return nil, nil
		}

		log.Println("CreateDepositLaosBankRecord.duplicateFromRecord", helper.StructJson(duplicateFromAdminRecord))
		return nil, badRequest("DUPLICATE_WITH_ADMIN_RECORD")
	}

	actionAt := time.Now().UTC().Format("************")
	var createBody model.RaceActionCreateBody
	createBody.Name = "USER_CREATE_DEPOSIT_LAOS_BANK"
	createBody.JsonRequest = helper.StructJson(req)
	createBody.Status = "PENDING"
	createBody.ActionKey = fmt.Sprintf("DEPOSIT_T%s_U%d_CD%f", actionAt, member.Id, req.CreditAmount)
	createBody.UnlockAt = time.Now().UTC().Add(time.Second * 1)
	if _, err := s.repoBanking.GetRaceActionIdByActionKey(createBody.ActionKey); err != nil {
		log.Println("CreateDepositLaosBankRecord.GetRaceActionIdByActionKey", err)
		if err != gorm.ErrRecordNotFound {
			return nil, internalServerError(errors.New("PLEASE_TRY_AGAIN_1_MINUTE"))
		}
	} else {
		return nil, internalServerError(errors.New("PLEASE_TRY_AGAIN_1_MINUTE"))
	}

	// create race condition
	actionId, err := s.repoBanking.CreateRaceCondition(createBody)
	if err != nil {
		log.Println("CreateDepositLaosBankRecord.CreateRaceCondition", err)
		return nil, internalServerError(errors.New("CANNOT_CREATE_ACTION"))
	}

	if actionId != 0 {

		// create statement
		var createStatement model.BankStatementCreateBody
		createStatement.Amount = req.CreditAmount
		createStatement.AccountId = bankAccount.Id
		createStatement.Detail = member.BankAccount
		createStatement.FromBankId = member.BankId
		createStatement.FromAccountNumber = member.BankAccount
		createStatement.StatementTypeId = model.STATEMENT_TYPE_TRANSFER_IN
		createStatement.TransferAt = transferAt
		createStatement.StatementStatusId = model.STATEMENT_STATUS_PENDING
		bankstatementId, err := s.repoBanking.CreateBankStatement(createStatement)
		if err != nil {
			log.Println("CreateDepositLaosBankRecord.CreateBankStatement", err)
			return nil, internalServerError(err)
		}

		var externalNoti model.NotifyExternalNotificationRequest
		var transId *int64

		createBankTransaction.MemberCode = member.MemberCode
		createBankTransaction.UserId = member.Id
		createBankTransaction.StatementId = bankstatementId
		createBankTransaction.TransactionTypeId = model.TRANSACTION_TYPE_DEPOSIT
		createBankTransaction.FromBankId = &member.BankId
		createBankTransaction.FromAccountNumber = &member.BankAccount
		createBankTransaction.ToAccountId = &bankAccount.Id
		createBankTransaction.ToAccountNumber = &bankAccount.AccountNumber
		createBankTransaction.ToBankId = &bankAccount.BankId
		createBankTransaction.ToAccountNumber = &bankAccount.AccountNumber
		createBankTransaction.CreditAmount = req.CreditAmount
		createBankTransaction.DepositChannel = "เลือกช่องทางฝากลาว"
		createBankTransaction.TransactionStatusId = model.TRANS_STATUS_PENDING
		createBankTransaction.CreatedByAdminId = 0
		createBankTransaction.TransferAt = &transferAt
		//2/10/66 || // 25/12/2023 ต้องการ แจ้ง เตือนทั้ง 2 แบบ เหมือนเดิม
		// create transaction
		transId, err = s.repoBanking.InsertBankTransaction(createBankTransaction)
		if err != nil {
			log.Println("CreateDepositLaosBankRecord.InsertBankTransaction.ERROR=", err)
			return nil, internalServerError(err)
		}

		if transId != nil {
			// AddTo BankPendingRecord -> รายการฝาก
			dashboardRepo := repository.NewDashboardRepository(s.SharedDb)
			if err := AddBankPendingRecordFromDeposit(dashboardRepo, *transId); err != nil {
				log.Println("CreateDepositLaosBankRecord.AddBankPendingRecordFromDeposit", err)
			}
		}

		externalNoti.TypeNotify = model.IsDepositBeforeCredit
		externalNoti.TransId = transId
		externalNoti.Amount = req.CreditAmount
		externalNoti.MemberCode = member.MemberCode
		externalNoti.UserCredit = member.Credit + req.CreditAmount
		externalNoti.ConfirmedByAdminId = 0

		// [Line Notify]
		endTime := time.Now()
		elapsed := endTime.Sub(startProcess)
		elapsedSeconds := elapsed.Seconds()
		timeElapsed := fmt.Sprintf("%.2f", elapsedSeconds)
		externalNoti.TimerCounter = timeElapsed
		// UpdateAutoProcessTimer(timer string, id int64) error
		if err := s.repoBanking.UpdateAutoProcessTimer(timeElapsed, *transId); err != nil {
			return nil, nil
		}
		if err := s.serviceNoti.ExternalNotification(externalNoti); err != nil {
			log.Println("FailedNotify", err)
		}
	}
	return nil, nil
}

func (s *bankingService) GetAccountInfoFastbank(body model.AccountInfoFastbankRequest) (*model.AccountInfoFastbankResponse, error) {
	return s.repoBanking.GetAccountInfoFastbank(body)
}

func (s *bankingService) ContinueConfirmWithdrawPaymentOnly(req model.CreateAutoWithdrawRequest) (*int64, error) {

	startProcess := time.Now()

	var transaction model.BankTransaction
	transaction, err := s.repoBanking.GetTransactionWithdrawOverMaxById(req.TransactionId)
	if err != nil {
		return nil, badRequest("ไม่ตรงกับเงือนไข")
	}
	if transaction.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_CANCELED {
		return nil, badRequest("ไม่สามารถทำรายการได้ เนื่องจากถูกยกเลิกไปแล้ว")
	}
	if transaction.Id == 0 {
		return nil, badRequest("ไม่พบรายการที่ต้องการ")
	}
	transId := transaction.Id
	// [race condition withdraw]
	// actionAt := time.Now().UTC().Format("************")
	var createBody model.RaceActionCreateBody
	createBody.Name = "ContinueConfirmWithdrawPaymentOnly"
	createBody.JsonRequest = fmt.Sprintf("req transactionId : %d", req.TransactionId)
	createBody.Status = "PENDING"
	// ******** P.mink confrim ปุ่มนี้กดได้แค่ 1 ครั้งเท่านั้น
	createBody.ActionKey = fmt.Sprintf("WITHDRAW_ID%d_U%d_CD%f", req.TransactionId, transaction.UserId, transaction.CreditAmount)
	createBody.UnlockAt = time.Now().UTC().Add(time.Second * 1)
	if _, err := s.repoBanking.GetRaceActionIdByActionKey(createBody.ActionKey); err != nil {
		log.Println("ContinueConfirmWithdrawPaymentOnly.GetRaceActionIdByActionKey", err)
		if err != gorm.ErrRecordNotFound {
			return nil, internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
		}
	} else {
		return nil, internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
	}

	// create race condition
	actionId, err := s.repoBanking.CreateRaceCondition(createBody)
	if err != nil {
		log.Println("ContinueConfirmWithdrawPaymentOnly.CreateRaceCondition", err)
		return nil, internalServerError(errors.New("CANNOT_CREATE_ACTION"))
	}

	if actionId == 0 {
		return nil, badRequest("PLEASE_WAIT_ONE_MINUTE_YOU_JUST_CLICKED_A_MOMENT_AGO_PLEASE_CHECK_THE_STATEMENT_AGAIN")
	}

	pgWdAccount, err := s.repoBanking.GetPaygateWithdrawAccount()
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound("PAYMENT_WITHDRAWAL_NOT_ENABLED")
		}
		return nil, internalServerError(err)
	}
	if pgWdAccount == nil {
		return nil, notFound("PAYMENT_WITHDRAWAL_NOT_ENABLED")
	}
	if !pgWdAccount.HasWithdraw || !pgWdAccount.IsWithdrawEnabled {
		return nil, badRequest("WITHDRAW_NOT_AVAILABLE")
	}
	// merchant, err := s.repoBanking.GetPaygateMerchantById(paygate.MerchantId)
	// if err != nil {
	// 	if err.Error() == recordNotFound {
	// 		return nil, notFound("MERCHANT_NOT_FOUND")
	// 	}
	// 	return nil, internalServerError(err)
	// }
	if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_HENG {
		return nil, badRequest("NO_HENG_WITHDRAW")

		// ทำเป็น else เลยก็ได้ เพราะใช้ จาก merchant แทน
		// if req.Amount < 100 || req.Amount > 50000 {
	} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_LUCKYTHAI {
		// 	return badRequest("INVALID_AMOUNT")
		// }
		if transaction.CreditAmount < pgWdAccount.PaymentWithdrawMinimum || transaction.CreditAmount > pgWdAccount.PaymentWithdrawMaximum {
			return nil, badRequest("INVALID_AMOUNT")
		}
	} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_PAPAYAPAY {
		// if req.Amount < 100 || req.Amount > 2000000 {
		// 	return badRequest("INVALID_AMOUNT")
		// }
		if transaction.CreditAmount < pgWdAccount.PaymentWithdrawMinimum || transaction.CreditAmount > pgWdAccount.PaymentWithdrawMaximum {
			return nil, badRequest("INVALID_AMOUNT")
		}
	} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_PAYONEX {
		// ฝากขั้นต่ำ 20 , ถอนขั้นต่ำ 100 ครับ
		// if req.Amount < 100 || req.Amount > 2000000 {
		// 	return badRequest("INVALID_AMOUNT")
		// }
		if transaction.CreditAmount < pgWdAccount.PaymentWithdrawMinimum || transaction.CreditAmount > pgWdAccount.PaymentWithdrawMaximum {
			return nil, badRequest("INVALID_AMOUNT")
		}
	} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_JBPAY {
		// ฝากขั้นต่ำ 50 , ถอนขั้นต่ำ 100 ครับ
		// if req.Amount < 100 || req.Amount > 2000000 {
		// 	return badRequest("INVALID_AMOUNT")
		// }
		if transaction.CreditAmount < pgWdAccount.PaymentWithdrawMinimum || transaction.CreditAmount > pgWdAccount.PaymentWithdrawMaximum {
			return nil, badRequest("INVALID_AMOUNT")
		}
	} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_POMPAY {
		if transaction.CreditAmount < pgWdAccount.PaymentWithdrawMinimum || transaction.CreditAmount > pgWdAccount.PaymentWithdrawMaximum {
			return nil, badRequest("INVALID_AMOUNT")
		}
	} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_PAYMENTCO {
		if transaction.CreditAmount < pgWdAccount.PaymentWithdrawMinimum || transaction.CreditAmount > pgWdAccount.PaymentWithdrawMaximum {
			return nil, badRequest("INVALID_AMOUNT")
		}
	} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_ZAPPAY {
		// ฝากขั้นต่ำ 50 , ถอนขั้นต่ำ 100 ครับ
		// if req.Amount < 100 || req.Amount > 2000000 {
		// 	return badRequest("INVALID_AMOUNT")
		// }
		if transaction.CreditAmount < pgWdAccount.PaymentWithdrawMinimum || transaction.CreditAmount > pgWdAccount.PaymentWithdrawMaximum {
			return nil, badRequest("INVALID_AMOUNT")
		}
	} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_ONEPAY {
		// Deposit Rate (Amount) 10-50,000
		// Withdraw Rate (Amount) 100-100,000
		if transaction.CreditAmount < pgWdAccount.PaymentWithdrawMinimum || transaction.CreditAmount > pgWdAccount.PaymentWithdrawMaximum {
			return nil, badRequest("INVALID_AMOUNT")
		}
	} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_FLASHPAY {
		// Deposit Rate (Amount) 20
		// Withdraw Rate (Amount) 50
		if transaction.CreditAmount < pgWdAccount.PaymentWithdrawMinimum || transaction.CreditAmount > pgWdAccount.PaymentWithdrawMaximum {
			return nil, badRequest("INVALID_AMOUNT")
		}
	} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_BIZPAY {
		if transaction.CreditAmount < pgWdAccount.PaymentWithdrawMinimum || transaction.CreditAmount > pgWdAccount.PaymentWithdrawMaximum {
			return nil, badRequest("INVALID_AMOUNT")
		}
	} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_SUGARPAY {
		if transaction.CreditAmount < pgWdAccount.PaymentWithdrawMinimum || transaction.CreditAmount > pgWdAccount.PaymentWithdrawMaximum {
			return nil, badRequest("INVALID_AMOUNT")
		}
	} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_ZMANPAY {
		if transaction.CreditAmount < pgWdAccount.PaymentWithdrawMinimum || transaction.CreditAmount > pgWdAccount.PaymentWithdrawMaximum {
			return nil, badRequest("INVALID_AMOUNT")
		}
	} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_POSTMANPAY {
		if transaction.CreditAmount < pgWdAccount.PaymentWithdrawMinimum || transaction.CreditAmount > pgWdAccount.PaymentWithdrawMaximum {
			return nil, badRequest("INVALID_AMOUNT")
		}
	} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_MAZEPAY {
		if transaction.CreditAmount < pgWdAccount.PaymentWithdrawMinimum || transaction.CreditAmount > pgWdAccount.PaymentWithdrawMaximum {
			return nil, badRequest("INVALID_AMOUNT")
		}
	} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_MEEPAY {
		if transaction.CreditAmount < pgWdAccount.PaymentWithdrawMinimum || transaction.CreditAmount > pgWdAccount.PaymentWithdrawMaximum {
			return nil, badRequest("INVALID_AMOUNT")
		}
	}

	// Check Remote Account Balance
	if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_LUCKYTHAI {
		remoteAccount, err := s.repoBanking.LuckyThaiCheckBalance(*pgWdAccount)
		if err != nil {
			return nil, internalServerError(err)
		}
		log.Println("ContinueConfirmWithdrawPaymentOnly", helper.StructJson(req), helper.StructJson(remoteAccount))
		if remoteAccount.Data.OutBalance.Balance < transaction.CreditAmount {
			return nil, badRequest("PRECHECK_INSUFFICIENT_BALANCE")
		}
	}

	user, err := s.repoBanking.GetUserBankDetailById(transaction.UserId)
	if err != nil {
		log.Println("ContinueConfirmWithdrawPaymentOnly.GetUserBankDetailById", err)
		return nil, internalServerError(err)
	}

	var externalNoti model.NotifyExternalNotificationRequest
	var setCreatedId int64 = 0
	if req.ConfirmedByAdminId != nil {
		setCreatedId = *req.ConfirmedByAdminId
	}
	var statusId int64
	if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_HENG {
		return &transId, badRequest("NO_HENG_WITHDRAW")
	} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_LUCKYTHAI {
		var luckyWithDrawRequest model.UserCreatePaygateLuckyWithdrawRequest // * Can use same model
		luckyWithDrawRequest.RefId = &transId
		luckyWithDrawRequest.UserId = transaction.UserId
		luckyWithDrawRequest.BankCode = user.BankCode
		luckyWithDrawRequest.AccountNo = user.BankAccount
		luckyWithDrawRequest.Accountname = user.Fullname
		luckyWithDrawRequest.Amount = transaction.CreditAmount
		if _, err := s.withdrawWithLucky(*pgWdAccount, luckyWithDrawRequest); err != nil {
			log.Println("ContinueConfirmWithdrawPaymentOnly.withdrawWithLucky.ERROR=", err)
			return &transId, err
		}
		statusId = model.TRANS_STATUS_WITHDRAW_TRASNFERING
	} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_PAPAYAPAY {
		var luckyWithDrawRequest model.UserCreatePaygateLuckyWithdrawRequest // * Can use same model
		luckyWithDrawRequest.RefId = &transId
		luckyWithDrawRequest.UserId = user.Id
		luckyWithDrawRequest.BankCode = user.BankCode
		luckyWithDrawRequest.AccountNo = user.BankAccount
		luckyWithDrawRequest.Accountname = user.Fullname
		luckyWithDrawRequest.Amount = transaction.CreditAmount
		if _, err := s.withdrawWithPapaya(*pgWdAccount, luckyWithDrawRequest); err != nil {
			log.Println("ContinueConfirmWithdrawPaymentOnly.withdrawWithPapaya.ERROR=", err)
			return &transId, err
		}
		statusId = model.TRANS_STATUS_WITHDRAW_TRASNFERING
	} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_PAYONEX {
		var payonexWithDrawRequest model.UserCreatePaygateLuckyWithdrawRequest // * Can use same model
		payonexWithDrawRequest.RefId = &transId
		payonexWithDrawRequest.UserId = user.Id
		payonexWithDrawRequest.BankCode = user.BankCode
		payonexWithDrawRequest.AccountNo = user.BankAccount
		payonexWithDrawRequest.Accountname = user.Fullname
		payonexWithDrawRequest.Amount = transaction.CreditAmount
		if _, err := s.withdrawWithPayonex(*pgWdAccount, payonexWithDrawRequest); err != nil {
			log.Println("ContinueConfirmWithdrawPaymentOnly.withdrawWithPayonex.ERROR=", err)
			return nil, badRequest("ทาง PAYMENT ไม่สามารถทำรายการได้")
		}
		statusId = model.TRANS_STATUS_WITHDRAW_TRASNFERING
	} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_JBPAY {
		var payonexWithDrawRequest model.UserCreatePaygateLuckyWithdrawRequest // * Can use same model
		payonexWithDrawRequest.RefId = &transId
		payonexWithDrawRequest.UserId = user.Id
		payonexWithDrawRequest.BankCode = user.BankCode
		payonexWithDrawRequest.AccountNo = user.BankAccount
		payonexWithDrawRequest.Accountname = user.Fullname
		payonexWithDrawRequest.Amount = transaction.CreditAmount
		if _, err := s.withdrawWithJbpay(*pgWdAccount, payonexWithDrawRequest); err != nil {
			log.Println("ContinueConfirmWithdrawPaymentOnly.withdrawWithJbpay.ERROR=", err)
			return nil, badRequest("ทาง PAYMENT ไม่สามารถทำรายการได้")
		}
		statusId = model.TRANS_STATUS_WITHDRAW_TRASNFERING
	} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_POMPAY {
		var payonexWithDrawRequest model.UserCreatePaygateLuckyWithdrawRequest // * Can use same model
		payonexWithDrawRequest.RefId = &transId
		payonexWithDrawRequest.UserId = user.Id
		payonexWithDrawRequest.BankCode = user.BankCode
		payonexWithDrawRequest.AccountNo = user.BankAccount
		payonexWithDrawRequest.Accountname = user.Fullname
		payonexWithDrawRequest.Amount = transaction.CreditAmount
		if _, err := s.withdrawWithPompay(*pgWdAccount, payonexWithDrawRequest); err != nil {
			log.Println("ContinueConfirmWithdrawPaymentOnly.withdrawWithPompay.ERROR=", err)
			return nil, badRequest("ทาง PAYMENT ไม่สามารถทำรายการได้")
		}
		statusId = model.TRANS_STATUS_WITHDRAW_TRASNFERING
	} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_PAYMENTCO {
		var payonexWithDrawRequest model.UserCreatePaygateLuckyWithdrawRequest // * Can use same model
		payonexWithDrawRequest.RefId = &transId
		payonexWithDrawRequest.UserId = user.Id
		payonexWithDrawRequest.BankCode = user.BankCode
		payonexWithDrawRequest.AccountNo = user.BankAccount
		payonexWithDrawRequest.Accountname = user.Fullname
		payonexWithDrawRequest.Amount = transaction.CreditAmount
		if _, err := s.withdrawWithPaymentco(*pgWdAccount, payonexWithDrawRequest); err != nil {
			log.Println("ContinueConfirmWithdrawPaymentOnly.withdrawWithPaymentco.ERROR=", err)
			return nil, badRequest("ทาง PAYMENT ไม่สามารถทำรายการได้")
		}
		statusId = model.TRANS_STATUS_WITHDRAW_TRASNFERING
	} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_ZAPPAY {
		var payonexWithDrawRequest model.UserCreatePaygateLuckyWithdrawRequest // * Can use same model
		payonexWithDrawRequest.RefId = &transId
		payonexWithDrawRequest.UserId = user.Id
		payonexWithDrawRequest.BankCode = user.BankCode
		payonexWithDrawRequest.AccountNo = user.BankAccount
		payonexWithDrawRequest.Accountname = user.Fullname
		payonexWithDrawRequest.Amount = transaction.CreditAmount
		if _, err := s.withdrawWithZappay(*pgWdAccount, payonexWithDrawRequest); err != nil {
			log.Println("ContinueConfirmWithdrawPaymentOnly.withdrawWithZappay.ERROR=", err)
			return nil, badRequest("ทาง PAYMENT ไม่สามารถทำรายการได้")
		}
		statusId = model.TRANS_STATUS_WITHDRAW_TRASNFERING
	} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_ONEPAY {
		var payonexWithDrawRequest model.UserCreatePaygateLuckyWithdrawRequest // * Can use same model
		payonexWithDrawRequest.RefId = &transId
		payonexWithDrawRequest.UserId = user.Id
		payonexWithDrawRequest.BankCode = user.BankCode
		payonexWithDrawRequest.AccountNo = user.BankAccount
		payonexWithDrawRequest.Accountname = user.Fullname
		payonexWithDrawRequest.Amount = transaction.CreditAmount
		if _, err := s.withdrawWithOnepay(*pgWdAccount, payonexWithDrawRequest); err != nil {
			log.Println("ContinueConfirmWithdrawPaymentOnly.withdrawWithOnepay.ERROR=", err)
			return nil, badRequest("ทาง PAYMENT ไม่สามารถทำรายการได้")
		}
		statusId = model.TRANS_STATUS_WITHDRAW_TRASNFERING
	} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_FLASHPAY {
		var payonexWithDrawRequest model.UserCreatePaygateLuckyWithdrawRequest // * Can use same model
		payonexWithDrawRequest.RefId = &transId
		payonexWithDrawRequest.UserId = user.Id
		payonexWithDrawRequest.BankCode = user.BankCode
		payonexWithDrawRequest.AccountNo = user.BankAccount
		payonexWithDrawRequest.Accountname = user.Fullname
		payonexWithDrawRequest.Amount = transaction.CreditAmount
		if _, err := s.withdrawWithFlashpay(*pgWdAccount, payonexWithDrawRequest); err != nil {
			log.Println("ContinueConfirmWithdrawPaymentOnly.withdrawWithFlashpay.ERROR=", err)
			return nil, badRequest("ทาง PAYMENT ไม่สามารถทำรายการได้")
		}
		statusId = model.TRANS_STATUS_WITHDRAW_TRASNFERING
	} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_BIZPAY {
		var payonexWithDrawRequest model.UserCreatePaygateLuckyWithdrawRequest // * Can use same model
		payonexWithDrawRequest.RefId = &transId
		payonexWithDrawRequest.UserId = user.Id
		payonexWithDrawRequest.BankCode = user.BankCode
		payonexWithDrawRequest.AccountNo = user.BankAccount
		payonexWithDrawRequest.Accountname = user.Fullname
		payonexWithDrawRequest.Amount = transaction.CreditAmount
		if _, err := s.withdrawWithBizpay(*pgWdAccount, payonexWithDrawRequest); err != nil {
			log.Println("ContinueConfirmWithdrawPaymentOnly.withdrawWithBizpay.ERROR=", err)
			return nil, badRequest("ทาง PAYMENT ไม่สามารถทำรายการได้")
		}
		statusId = model.TRANS_STATUS_WITHDRAW_TRASNFERING
	} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_SUGARPAY {
		var payonexWithDrawRequest model.UserCreatePaygateLuckyWithdrawRequest // * Can use same model
		payonexWithDrawRequest.RefId = &transId
		payonexWithDrawRequest.UserId = user.Id
		payonexWithDrawRequest.BankCode = user.BankCode
		payonexWithDrawRequest.AccountNo = user.BankAccount
		payonexWithDrawRequest.Accountname = user.Fullname
		payonexWithDrawRequest.Amount = transaction.CreditAmount
		if _, err := s.withdrawWithSugarpay(*pgWdAccount, payonexWithDrawRequest); err != nil {
			log.Println("ContinueConfirmWithdrawPaymentOnly.withdrawWithSugarpay.ERROR=", err)
			return nil, badRequest("ทาง PAYMENT ไม่สามารถทำรายการได้")
		}
		statusId = model.TRANS_STATUS_WITHDRAW_TRASNFERING
	} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_ZMANPAY {
		var payonexWithDrawRequest model.UserCreatePaygateLuckyWithdrawRequest // * Can use same model
		payonexWithDrawRequest.RefId = &transId
		payonexWithDrawRequest.UserId = user.Id
		payonexWithDrawRequest.BankCode = user.BankCode
		payonexWithDrawRequest.AccountNo = user.BankAccount
		payonexWithDrawRequest.Accountname = user.Fullname
		payonexWithDrawRequest.Amount = transaction.CreditAmount
		if _, err := s.withdrawWithZmanpay(*pgWdAccount, payonexWithDrawRequest); err != nil {
			log.Println("ContinueConfirmWithdrawPaymentOnly.withdrawWithZmanpay", err)
			return nil, badRequest("ทาง PAYMENT ไม่สามารถทำรายการได้")
		}
		statusId = model.TRANS_STATUS_WITHDRAW_TRASNFERING
	} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_POSTMANPAY {
		var payonexWithDrawRequest model.UserCreatePaygateLuckyWithdrawRequest // * Can use same model
		payonexWithDrawRequest.RefId = &transId
		payonexWithDrawRequest.UserId = user.Id
		payonexWithDrawRequest.BankCode = user.BankCode
		payonexWithDrawRequest.AccountNo = user.BankAccount
		payonexWithDrawRequest.Accountname = user.Fullname
		payonexWithDrawRequest.Amount = transaction.CreditAmount
		if _, err := s.withdrawWithPostmanPay(*pgWdAccount, payonexWithDrawRequest); err != nil {
			log.Println("ContinueConfirmWithdrawPaymentOnly.withdrawWithPostmanPay.ERROR=", err)
			return nil, badRequest("ทาง PAYMENT ไม่สามารถทำรายการได้")
		}
		statusId = model.TRANS_STATUS_WITHDRAW_TRASNFERING
	} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_MAZEPAY {
		var payonexWithDrawRequest model.UserCreatePaygateLuckyWithdrawRequest // * Can use same model
		payonexWithDrawRequest.RefId = &transId
		payonexWithDrawRequest.UserId = user.Id
		payonexWithDrawRequest.BankCode = user.BankCode
		payonexWithDrawRequest.AccountNo = user.BankAccount
		payonexWithDrawRequest.Accountname = user.Fullname
		payonexWithDrawRequest.Amount = transaction.CreditAmount
		if _, err := s.withdrawWithMazepay(*pgWdAccount, payonexWithDrawRequest); err != nil {
			log.Println("ContinueConfirmWithdrawPaymentOnly.withdrawWithMazepay", err)
			return nil, badRequest("ทาง PAYMENT ไม่สามารถทำรายการได้")
		}
		statusId = model.TRANS_STATUS_WITHDRAW_TRASNFERING
	} else if pgWdAccount.ProviderId == model.PAYGATE_MERCHANT_ID_MEEPAY {
		var payonexWithDrawRequest model.UserCreatePaygateLuckyWithdrawRequest // * Can use same model
		payonexWithDrawRequest.RefId = &transId
		payonexWithDrawRequest.UserId = user.Id
		payonexWithDrawRequest.BankCode = user.BankCode
		payonexWithDrawRequest.AccountNo = user.BankAccount
		payonexWithDrawRequest.Accountname = user.Fullname
		payonexWithDrawRequest.Amount = transaction.CreditAmount
		if _, err := s.withdrawWithMeepay(*pgWdAccount, payonexWithDrawRequest); err != nil {
			log.Println("ContinueConfirmWithdrawPaymentOnly.withdrawWithMeepay", err)
			return nil, badRequest("ทาง PAYMENT ไม่สามารถทำรายการได้")
		}
		statusId = model.TRANS_STATUS_WITHDRAW_TRASNFERING
	}

	statusLog := "SUCCESS"
	jsonPayLoad := fmt.Sprintf("WITHDRAW_TRANSACTION_ID %v ", transaction.Id)
	jsonRequest := fmt.Sprintf(" BANK_TRANS_ID %v ", transaction.Id)
	logType := "ContinueConfirmWithdrawPaymentOnly"
	var createLog model.BankTransactionLogCreateRequest
	createLog.Status = &statusLog
	createLog.JsonPayload = &jsonPayLoad
	createLog.JsonRequest = &jsonRequest
	createLog.LogType = &logType
	if _, err := s.serviceAccounting.CreateBankTransactionLog(createLog); err != nil {
		log.Println("ContinueConfirmWithdrawPaymentOnly.CreateBankTransactionLog", err)
	}

	// [ Notify]
	endProcess := time.Now()
	elapsed := endProcess.Sub(startProcess)
	elapsedSeconds := elapsed.Seconds()
	timeElapsed := fmt.Sprintf("%.2f", elapsedSeconds)
	externalNoti.TransId = &transId
	externalNoti.Amount = transaction.CreditAmount
	externalNoti.MemberCode = user.MemberCode
	externalNoti.UserCredit = user.Credit
	externalNoti.ConfirmedByAdminId = setCreatedId
	externalNoti.TimerCounter = timeElapsed
	externalNoti.WebScoket.UserID = user.Id
	externalNoti.WebScoket.Amount = transaction.CreditAmount
	externalNoti.WebScoket.MemberCode = user.MemberCode
	externalNoti.WebScoket.AlertType = "WITHDRAW"

	// [update transaction status]
	var updateApprovedBy model.UpdateConfirmAutoWithdrawBody
	updateApprovedBy.TransactionStatusId = &statusId
	updateApprovedBy.ConfirmedAt = time.Now()
	updateApprovedBy.ConfirmedByAdminId = &setCreatedId
	if err := s.repoBanking.UpdateAdminAndTransactionStatus(transId, updateApprovedBy); err != nil {
		log.Println("ContinueConfirmWithdrawPaymentOnly.UpdateAdminAndTransactionStatus", err)
		return &transId, internalServerError(err)
	}

	if err := s.repoBanking.UpdateUserTransactionConfirmBy(transId, updateApprovedBy); err != nil {
		log.Println("ContinueConfirmWithdrawPaymentOnly.UpdateUserTransactionConfirmBy", err)
	}
	// [ Notify]
	if err := s.serviceNoti.ExternalNotification(externalNoti); err != nil {
		log.Println("FailedNotify", err)
	}

	return &transId, nil
}

func (s *bankingService) GetSmsModeDepositList(req model.GetSmsModeDepositListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.Pagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	list, total, err := s.repoBanking.GetSmsModeDepositList(req)
	if err != nil {
		return nil, internalServerError(err)
	}

	return &model.SuccessWithPagination{
		Total: total,
		List:  list,
	}, nil
}

func (s *bankingService) AdminConfirmDepositSmsMode(req model.AdminConfirmDepositSmsModeRequest) (*int64, error) {

	smsDepositDetail, err := s.repoBanking.GetSmsModeDepositById(req.PaygateSmsModeDepositId)
	if err != nil {
		return nil, internalServerError(err)
	}

	if smsDepositDetail == nil {
		return nil, badRequest("ไม่พบข้อมูลการฝากเงิน")
	}

	actionAt := time.Now().UTC().Format("************")
	var createBody model.RaceActionCreateBody
	createBody.Name = "ADMIN_CREATE_SMS-DEPOSIT"
	createBody.JsonRequest = helper.StructJson(req)
	createBody.Status = "PENDING"
	createBody.ActionKey = fmt.Sprintf("DEPOSITSMS_T%s_U%d_CD%f", actionAt, smsDepositDetail.UserId, smsDepositDetail.TransferAmount)
	createBody.UnlockAt = time.Now().UTC().Add(time.Second * 1)
	if _, err := s.repoBanking.GetRaceActionIdByActionKey(createBody.ActionKey); err != nil {
		log.Println("AdminConfirmDepositSmsMode.GetRaceActionIdByActionKey", err)
		if err != gorm.ErrRecordNotFound {
			return nil, badRequest("PLEASE_WAIT_ONE_MINUTE")
		}
	} else {
		return nil, internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
	}
	// create race condition
	actionId, err := s.repoBanking.CreateRaceCondition(createBody)
	if err != nil {
		log.Println("CreateBankStatementFromWebhookAndAuto.CreateRaceCondition", err)
		return nil, internalServerError(errors.New("CANNOT_CREATE_ACTION"))
	}
	if actionId != 0 {

		// validate smsDepositDetail
		if smsDepositDetail.TransactionStatus == "PAID" {
			return nil, badRequest("ไม่สามารถทำรายการได้ เนื่องจากสถานะไม่ถูกต้อง")
		}

		// get admin
		admin, err := s.repoBanking.GetAdminById(*req.CreateByAdminId)
		if err != nil {
			return nil, internalServerError(err)
		}

		// update smsDepositDetail
		var updateConfirmSmsModeDeposit model.UpdateConfirmSmsModeDepositBody
		updateConfirmSmsModeDeposit.Id = &smsDepositDetail.Id
		success := "PAID"
		updateConfirmSmsModeDeposit.TransactionStatus = &success
		updateConfirmSmsModeDeposit.PaymentAt = &smsDepositDetail.TransactionDate
		if admin != nil {
			updateConfirmSmsModeDeposit.ConfirmedByAdminId = req.CreateByAdminId
			updateConfirmSmsModeDeposit.ConfirmedByAdminName = &admin.Fullname
		}
		if err := s.repoAccounting.UpdateConfirmSmsModeDeposit(updateConfirmSmsModeDeposit); err != nil {
			return nil, nil
		}

		var createDeposit model.CreateDepositRecordRequest
		createDeposit.UserId = smsDepositDetail.UserId
		createDeposit.CreditAmount = smsDepositDetail.TransferAmount
		createDeposit.DepositChannel = "SMS-MODE"
		createDeposit.FromAccountNumber = smsDepositDetail.AccountFrom
		createDeposit.ToAccountId = smsDepositDetail.BankAccountId
		createDeposit.TransferAt = smsDepositDetail.TransactionDate
		createDeposit.Auto = true
		if req.CreateByAdminId != nil {
			createDeposit.CreateByAdminId = *req.CreateByAdminId
		}
		createDeposit.CreateAt = time.Now().UTC()

		createDepositRecordId, err := s.CreateDepositRecord(createDeposit)
		if err != nil {
			return nil, internalServerError(err)
		}

		var updateConfirm model.UpdateConfirmSmsModeDepositBody
		updateConfirm.Id = &smsDepositDetail.Id
		updateConfirm.RefId = createDepositRecordId
		if err := s.repoAccounting.UpdateConfirmSmsModeDeposit(updateConfirm); err != nil {
			return nil, nil
		}
		return createDepositRecordId, nil
	}

	return nil, badRequest("PLEASE_WAIT_ONE_MINUTE_YOU_JUST_CLICKED_A_MOMENT_AGO_PLEASE_CHECK_THE_STATEMENT_AGAIN")
}

func (s *bankingService) AdminCancelDepositSmsMode(req model.AdminConfirmCanceledSmsModeRequest) (*int64, error) {

	smsDepositDetail, err := s.repoBanking.GetSmsModeDepositById(req.PaygateSmsModeCanceledId)
	if err != nil {
		return nil, internalServerError(err)
	}

	if smsDepositDetail == nil {
		return nil, badRequest("ไม่พบข้อมูลการฝากเงิน")
	}

	actionAt := time.Now().UTC().Format("************")
	var createBody model.RaceActionCreateBody
	createBody.Name = "ADMIN_CANCELED_SMS-DEPOSIT"
	createBody.JsonRequest = helper.StructJson(req)
	createBody.Status = "PENDING"
	createBody.ActionKey = fmt.Sprintf("FAILEDSMS_T%s_U%d_CD%f", actionAt, smsDepositDetail.UserId, smsDepositDetail.TransferAmount)
	createBody.UnlockAt = time.Now().UTC().Add(time.Second * 1)
	if _, err := s.repoBanking.GetRaceActionIdByActionKey(createBody.ActionKey); err != nil {
		log.Println("AdminConfirmDepositSmsMode.GetRaceActionIdByActionKey", err)
		if err != gorm.ErrRecordNotFound {
			return nil, badRequest("PLEASE_WAIT_ONE_MINUTE")
		}
	} else {
		return nil, internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
	}
	// create race condition
	actionId, err := s.repoBanking.CreateRaceCondition(createBody)
	if err != nil {
		log.Println("CreateBankStatementFromWebhookAndAuto.CreateRaceCondition", err)
		return nil, internalServerError(errors.New("CANNOT_CREATE_ACTION"))
	}
	if actionId != 0 {
		// validate smsDepositDetail
		if smsDepositDetail.TransactionStatus == "PAID" || smsDepositDetail.TransactionStatus == "CANCELED" {
			return nil, badRequest("ไม่สามารถทำรายการได้ เนื่องจากสถานะไม่ถูกต้อง")
		}

		// get admin
		admin, err := s.repoBanking.GetAdminById(*req.CreateByAdminId)
		if err != nil {
			return nil, internalServerError(err)
		}

		// update smsDepositDetail
		var updateConfirmSmsModeDeposit model.UpdateConfirmSmsModeDepositBody
		updateConfirmSmsModeDeposit.Id = &smsDepositDetail.Id
		success := "CANCELED"
		updateConfirmSmsModeDeposit.TransactionStatus = &success
		updateConfirmSmsModeDeposit.PaymentAt = &smsDepositDetail.TransactionDate
		if admin != nil {
			updateConfirmSmsModeDeposit.ConfirmedByAdminId = req.CreateByAdminId
			updateConfirmSmsModeDeposit.ConfirmedByAdminName = &admin.Fullname
		}
		if err := s.repoAccounting.UpdateConfirmSmsModeDeposit(updateConfirmSmsModeDeposit); err != nil {
			return nil, nil
		}
	}

	return nil, nil
}

func (s *bankingService) UploadImageToS3DepositSlip(imageFileBody *http.Request) (*model.FileUploadResponse, error) {

	fileReader, newFileName, err := imageFileBody.FormFile("file")
	if err != nil {
		log.Println("UploadFileS3.ERROR", err)
		return nil, err
	}

	filename := &newFileName.Filename

	dbName := os.Getenv("DB_NAME")

	pathName := fmt.Sprintf("cbgame/%v/deposit-slip/", dbName)
	var newImageId *model.FileUploadResponse
	fileData, err := s.repoBanking.UploadImageToS3(pathName, *filename, fileReader)
	if err != nil {
		log.Println("UploadFileS3.ERROR", err)
		return nil, badRequest("UNABLE_TO_UPLOAD_FILE")
	}

	newImageId = &model.FileUploadResponse{
		ImageUrl: fileData.ImageUrl,
	}

	return newImageId, nil
}

func (s *bankingService) UploadImageToS3BonusSlip(imageFileBody *http.Request) (*model.FileUploadResponse, error) {

	fileReader, newFileName, err := imageFileBody.FormFile("file")
	if err != nil {
		log.Println("UploadFileS3.ERROR", err)
		return nil, err
	}

	filename := &newFileName.Filename

	dbName := os.Getenv("DB_NAME")

	pathName := fmt.Sprintf("cbgame/%v/bonus-slip/", dbName)
	var newImageId *model.FileUploadResponse
	fileData, err := s.repoBanking.UploadImageToS3(pathName, *filename, fileReader)
	if err != nil {
		log.Println("UploadFileS3.ERROR", err)
		return nil, badRequest("UNABLE_TO_UPLOAD_FILE")
	}

	newImageId = &model.FileUploadResponse{
		ImageUrl: fileData.ImageUrl,
	}

	return newImageId, nil
}

func (s *bankingService) UploadImageToS3WithdrawSlip(imageFileBody *http.Request) (*model.FileUploadResponse, error) {

	fileReader, newFileName, err := imageFileBody.FormFile("file")
	if err != nil {
		log.Println("UploadFileS3.ERROR", err)
		return nil, err
	}

	filename := &newFileName.Filename

	dbName := os.Getenv("DB_NAME")

	pathName := fmt.Sprintf("cbgame/%v/withdraw-slip/", dbName)
	var newImageId *model.FileUploadResponse
	fileData, err := s.repoBanking.UploadImageToS3(pathName, *filename, fileReader)
	if err != nil {
		log.Println("UploadFileS3.ERROR", err)
		return nil, badRequest("UNABLE_TO_UPLOAD_FILE")
	}

	newImageId = &model.FileUploadResponse{
		ImageUrl: fileData.ImageUrl,
	}

	return newImageId, nil
}

func (s *bankingService) BotCreateWithdrawPullCreditBackV1(req model.BotCreateWithdrawPullCreditBackRequest) (*model.BotCreateWithdrawPullCreditBackResponse, error) {

	if err := helper.Pagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	fmt.Println("BotCreateWithdrawPullCreditBack", helper.StructJson(req))
	getUser, err := s.repoBanking.BotCreateWithdrawPullCreditBack(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	fmt.Println("BotCreateWithdrawPullCreditBack", helper.StructJson(getUser))

	// type BotCreateWithdrawPullCreditBackResponse struct {
	// 	Total                                 int                                     `json:"total"`
	// 	CountError                            int                                     `json:"countError"`
	// 	CountSuccess                          int                                     `json:"countSuccess"`
	// 	BotCreateWithdrawPullCreditBackDetail []BotCreateWithdrawPullCreditBackDetail `json:"botCreateWithdrawPullCreditBackDetail"`
	// }
	// type BotCreateWithdrawPullCreditBackDetail struct {
	// 	MemberCode string `json:"memberCode"`
	// 	Message    error  `json:"message"`
	// }

	var countError int
	var countSuccess int
	var totalUser int

	var responseDetails []model.BotCreateWithdrawPullCreditBackDetail

	totalUser = len(getUser)
	for _, userDetail := range getUser {

		var responseDetail model.BotCreateWithdrawPullCreditBackDetail
		responseDetail.MemberCode = userDetail.MemberCode

		user, err := s.repoBanking.GetUserBankDetailByMemberCode(userDetail.MemberCode)
		if err != nil {
			responseDetail.Message = fmt.Sprintf(" %v", err)
			responseDetails = append(responseDetails, responseDetail)
			countError++
			continue
		}

		agentName := os.Getenv("AGENT_NAME")
		agentData := model.AgcBalance{}
		agentData.Agentname = agentName
		agentData.PlayerName = user.MemberCode
		agentData.Timestamp = int64(time.Now().Unix())
		agentData.Sign = helper.CreateSign(os.Getenv("AGENT_KEY"), agentData.Agentname+user.MemberCode, time.Now())
		agent, err := s.repoBanking.AgcGetCredit(agentData)
		if err != nil {
			responseDetail.Message = fmt.Sprintf(" %v", err)
			responseDetails = append(responseDetails, responseDetail)
			countError++
			continue
		}

		if agent.Balance <= 0 {
			responseDetail.Message = fmt.Sprintf(" %v", err)
			responseDetails = append(responseDetails, responseDetail)
			countSuccess++
			continue
		}

		var setDataCreditPullBack model.CreateWithdrawPullCreditBackRequest
		setDataCreditPullBack.Amount = agent.Balance
		setDataCreditPullBack.MemberCode = user.MemberCode
		setDataCreditPullBack.ConfirmedAt = time.Now()
		setDataCreditPullBack.ConfirmedByAdminId = &req.CreateByAdminId
		setDataCreditPullBack.Remark = "BotCreateWithdrawPullCreditBackV1"

		// [CURRENT_USER_CREDIT] get latest credit from Agent
		userCreditInfo, err := s.serviceUser.GetUser(user.Id)
		if err != nil {
			responseDetail.Message = fmt.Sprintf(" %v", err)
			responseDetails = append(responseDetails, responseDetail)
			countError++
			continue
		}
		if userCreditInfo.Credit < setDataCreditPullBack.Amount {
			responseDetail.Message = fmt.Sprintf(" %v", err)
			responseDetails = append(responseDetails, responseDetail)
			countError++
			continue
		}

		var createBankTransaction model.BankTransactionCreateBody
		createBankTransaction.MemberCode = setDataCreditPullBack.MemberCode
		createBankTransaction.UserId = user.Id
		createBankTransaction.TransactionTypeId = model.TRANSACTION_TYPE_CREDITBACK
		createBankTransaction.CreditBack = setDataCreditPullBack.Amount
		createBankTransaction.TransactionStatusId = model.TRANS_STATUS_WITHDRAW_PENDING
		createBankTransaction.TransferAt = &setDataCreditPullBack.ConfirmedAt
		createBankTransaction.CreatedByAdminId = *setDataCreditPullBack.ConfirmedByAdminId

		//2/10/66
		// create transaction
		transId, err := s.repoBanking.InsertBankTransaction(createBankTransaction)
		if err != nil {
			log.Println("CreateWithdrawPullCreditBack.InsertBankTransaction.ERROR=", err)
			responseDetail.Message = fmt.Sprintf(" %v", err)
			responseDetails = append(responseDetails, responseDetail)
			countError++
			continue
		}

		// gameRes, err := s.repoAgentConnect.WithdrawAgent(user.MemberCode, user.Id, req.Amount, *transId)
		// if err != nil {
		// 	return nil, internalServerError(err)
		// }
		var userCreditReq model.UserTransactionCreateRequest
		userCreditReq.RefId = transId
		userCreditReq.UserId = user.Id
		userCreditReq.TypeId = model.CREDIT_TYPE_TAKE_CREDIT_BACK
		userCreditReq.Amount = setDataCreditPullBack.Amount
		userCreditReq.CreateBy = setDataCreditPullBack.ConfirmedByAdminId
		userCreditReq.ConfirmBy = setDataCreditPullBack.ConfirmedByAdminId
		userCreditReq.StartWorkAt = setDataCreditPullBack.ConfirmedAt
		agentResp, err := s.repoAccounting.DecreaseUserCredit(userCreditReq)
		if err != nil {
			// [********] Confirm from P.layer if error from agent will be in failed
			log.Println("CreateWithdrawPullCreditBack.DecreaseUserCredit", err)
			var updateAgentError model.UpdateConfirmAutoWithdrawBody
			statusError := model.TRANS_STATUS_WITHDRAW_REJECTED
			confirmedByAuto := int64(0)
			updateAgentError.TransactionStatusId = &statusError
			updateAgentError.ConfirmedByAdminId = &confirmedByAuto
			if err := s.repoBanking.UpdateAdminAndTransactionStatus(*transId, updateAgentError); err != nil {
				responseDetail.Message = fmt.Sprintf(" %v", err)
				responseDetails = append(responseDetails, responseDetail)
				countError++
				continue
			}
			responseDetail.Message = fmt.Sprintf(" %v", err)
			responseDetails = append(responseDetails, responseDetail)
			countError++
			continue
		} else {
			if err := s.repoBanking.UpdateWithdrawTransactionSuccessPullCreditBack(*transId, *agentResp); err != nil {
				log.Println(err)
			}
		}
		// CreateSuccessTransferWithdraw
		var createConfirm model.CreateSuccessTransferWithdrawRequest
		createConfirm.TransactionId = *transId
		createConfirm.ConfirmedAt = setDataCreditPullBack.ConfirmedAt
		createConfirm.ConfirmedByAdminId = setDataCreditPullBack.ConfirmedByAdminId
		_, err = s.CreateSuccessTransferWithdraw(createConfirm)
		if err != nil {
			responseDetail.Message = fmt.Sprintf(" %v", err)
			responseDetails = append(responseDetails, responseDetail)
			countError++
			continue
		}

		// update bank_transaction confirmed by
		var updateApprovedBy model.UpdateConfirmAutoWithdrawBody
		updateApprovedBy.ConfirmedAt = setDataCreditPullBack.ConfirmedAt
		updateApprovedBy.ConfirmedByAdminId = setDataCreditPullBack.ConfirmedByAdminId
		if err = s.repoBanking.UpdateAdminAndTransactionStatus(*transId, updateApprovedBy); err != nil {
			responseDetail.Message = fmt.Sprintf(" %v", err)
			responseDetails = append(responseDetails, responseDetail)
			countError++
			continue
		}
		responseDetail.Message = "SUCCESS"
		responseDetails = append(responseDetails, responseDetail)
		countSuccess++
	}

	var response model.BotCreateWithdrawPullCreditBackResponse
	response.Total = totalUser
	response.CountError = countError
	response.CountSuccess = countSuccess
	response.BotCreateWithdrawPullCreditBackDetail = responseDetails

	return &response, nil
}

func (s *bankingService) BotCreateWithdrawPullCreditBack(req model.BotCreateWithdrawPullCreditBackRequest) (*model.BotCreateWithdrawPullCreditBackResponse, error) {
	if err := helper.Pagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	getUser, err := s.repoBanking.BotCreateWithdrawPullCreditBack(req)
	if err != nil {
		return nil, internalServerError(err)
	}

	var countError int32
	var countSuccess int32
	var responseDetails []model.BotCreateWithdrawPullCreditBackDetail
	var mu sync.Mutex // Protect shared resources

	var wg sync.WaitGroup
	totalUser := len(getUser)

	for _, userDetail := range getUser {
		wg.Add(1)

		go func(userDetail model.BotCreateWithdrawPullCreditBackGetUser) {
			defer wg.Done()

			var responseDetail model.BotCreateWithdrawPullCreditBackDetail
			responseDetail.MemberCode = userDetail.MemberCode

			// Step 1: Get User Bank Details
			user, err := s.repoBanking.GetUserBankDetailByMemberCode(userDetail.MemberCode)
			if err != nil {
				responseDetail.Message = fmt.Sprintf("Error getting user bank details: %v", err)
				mu.Lock()
				responseDetails = append(responseDetails, responseDetail)
				atomic.AddInt32(&countError, 1)
				mu.Unlock()
				return
			}

			// Step 2: Get Agent Credit
			agentName := os.Getenv("AGENT_NAME")
			agentData := model.AgcBalance{
				Agentname:  agentName,
				PlayerName: user.MemberCode,
				Timestamp:  int64(time.Now().Unix()),
				Sign:       helper.CreateSign(os.Getenv("AGENT_KEY"), agentName+user.MemberCode, time.Now()),
			}
			agent, err := s.repoBanking.AgcGetCredit(agentData)
			if err != nil {
				responseDetail.Message = fmt.Sprintf("Error getting agent credit: %v", err)
				mu.Lock()
				responseDetails = append(responseDetails, responseDetail)
				atomic.AddInt32(&countError, 1)
				mu.Unlock()
				return
			}

			if agent.Balance <= 0 {
				responseDetail.Message = "No balance available"
				mu.Lock()
				responseDetails = append(responseDetails, responseDetail)
				atomic.AddInt32(&countSuccess, 1)
				mu.Unlock()
				return
			}

			// Step 3: Validate and Process Transaction
			var setDataCreditPullBack model.CreateWithdrawPullCreditBackRequest
			setDataCreditPullBack.Amount = agent.Balance
			setDataCreditPullBack.MemberCode = user.MemberCode
			setDataCreditPullBack.ConfirmedAt = time.Now()
			setDataCreditPullBack.ConfirmedByAdminId = &req.CreateByAdminId
			setDataCreditPullBack.Remark = "BotCreateWithdrawPullCreditBack"

			// Validate User Credit
			userCreditInfo, err := s.serviceUser.GetUser(user.Id)
			if err != nil || userCreditInfo.Credit < setDataCreditPullBack.Amount {
				responseDetail.Message = fmt.Sprintf("Error validating user credit: %v", err)
				mu.Lock()
				responseDetails = append(responseDetails, responseDetail)
				atomic.AddInt32(&countError, 1)
				mu.Unlock()
				return
			}

			// Create Bank Transaction
			var createBankTransaction model.BankTransactionCreateBody
			createBankTransaction.MemberCode = setDataCreditPullBack.MemberCode
			createBankTransaction.UserId = user.Id
			createBankTransaction.TransactionTypeId = model.TRANSACTION_TYPE_CREDITBACK
			createBankTransaction.CreditBack = setDataCreditPullBack.Amount
			createBankTransaction.TransactionStatusId = model.TRANS_STATUS_WITHDRAW_PENDING
			createBankTransaction.TransferAt = &setDataCreditPullBack.ConfirmedAt
			createBankTransaction.CreatedByAdminId = *setDataCreditPullBack.ConfirmedByAdminId

			transId, err := s.repoBanking.InsertBankTransaction(createBankTransaction)
			if err != nil {
				log.Println("BotCreateWithdrawPullCreditBack.InsertBankTransaction.ERROR=", err)
				responseDetail.Message = fmt.Sprintf("Error creating bank transaction: %v", err)
				mu.Lock()
				responseDetails = append(responseDetails, responseDetail)
				atomic.AddInt32(&countError, 1)
				mu.Unlock()
				return
			}

			// Decrease User Credit
			var userCreditReq model.UserTransactionCreateRequest
			userCreditReq.RefId = transId
			userCreditReq.UserId = user.Id
			userCreditReq.TypeId = model.CREDIT_TYPE_TAKE_CREDIT_BACK
			userCreditReq.Amount = setDataCreditPullBack.Amount
			userCreditReq.CreateBy = setDataCreditPullBack.ConfirmedByAdminId
			userCreditReq.ConfirmBy = setDataCreditPullBack.ConfirmedByAdminId
			userCreditReq.StartWorkAt = setDataCreditPullBack.ConfirmedAt

			agentResp, err := s.repoAccounting.DecreaseUserCredit(userCreditReq)
			if err != nil {
				log.Println("Error decreasing user credit:", err)
				var updateAgentError model.UpdateConfirmAutoWithdrawBody
				statusError := model.TRANS_STATUS_WITHDRAW_REJECTED
				confirmedByAuto := int64(0)
				updateAgentError.TransactionStatusId = &statusError
				updateAgentError.ConfirmedByAdminId = &confirmedByAuto
				if updateErr := s.repoBanking.UpdateAdminAndTransactionStatus(*transId, updateAgentError); updateErr != nil {
					responseDetail.Message = fmt.Sprintf("Error updating transaction status: %v", updateErr)
				}
				mu.Lock()
				responseDetails = append(responseDetails, responseDetail)
				atomic.AddInt32(&countError, 1)
				mu.Unlock()
				return
			}

			// Update Transaction Success
			if err := s.repoBanking.UpdateWithdrawTransactionSuccessPullCreditBack(*transId, *agentResp); err != nil {
				log.Println("Error updating withdraw transaction:", err)
			}

			// Finalize Success Transfer
			var createConfirm model.CreateSuccessTransferWithdrawRequest
			createConfirm.TransactionId = *transId
			createConfirm.ConfirmedAt = setDataCreditPullBack.ConfirmedAt
			createConfirm.ConfirmedByAdminId = setDataCreditPullBack.ConfirmedByAdminId
			if _, err = s.CreateSuccessTransferWithdraw(createConfirm); err != nil {
				responseDetail.Message = fmt.Sprintf("Error confirming success transfer: %v", err)
				mu.Lock()
				responseDetails = append(responseDetails, responseDetail)
				atomic.AddInt32(&countError, 1)
				mu.Unlock()
				return
			}

			responseDetail.Message = "SUCCESS"
			mu.Lock()
			responseDetails = append(responseDetails, responseDetail)
			atomic.AddInt32(&countSuccess, 1)
			mu.Unlock()
		}(userDetail) // Pass userDetail as model.BotCreateWithdrawPullCreditBackGetUser
	}

	wg.Wait()

	// Prepare final response
	response := &model.BotCreateWithdrawPullCreditBackResponse{
		Total:                                 totalUser,
		CountError:                            int(countError),
		CountSuccess:                          int(countSuccess),
		BotCreateWithdrawPullCreditBackDetail: responseDetails,
	}

	return response, nil
}

func (s *bankingService) CheckAllowWithdrawCreditCondition(userId int64, withdrawAmount float64) (float64, error) {

	// ตอนนี้ func นี้ไม่ได้ลองรับ stack การดึงกลับ เครดิต requriement ไม่มีบอก แต่ ทำ เผื่อ ในอนาคตว่าจะ stack อาจต้องมาปรับใหม่เพิ่ม
	ableWithdrawAmount := withdrawAmount
	// [Get all lock withdraw]
	// CheckLockCreditWithdrawByUserId(userId int64) ([]model.CheckLockCreditWithdrawByUserId, error)
	getLockWithdraw, err := s.repoBanking.CheckLockCreditWithdrawByUserId(userId)
	if err != nil {
		return 0, nil
	}
	if len(getLockWithdraw) == 0 {
		return ableWithdrawAmount, nil
	}

	// [Get User Credit]
	getUser, err := s.serviceUser.GetUser(userId)
	if err != nil {
		return 0, nil
	}

	var totalCreditMoreThan float64
	var totalAllowWithdrawAmount float64
	for _, lockWithdraw := range getLockWithdraw {
		totalCreditMoreThan += lockWithdraw.CreditMoreThan
		totalAllowWithdrawAmount += lockWithdraw.AllowWithdrawAmount
		// ต้องคืนแค่ เครดิต จำกัดของ ตัวเองเท่านั้น
		// if lockWithdraw.IsPullCredit && withdrawAmount > lockWithdraw.AllowWithdrawAmount {
		// 	return 0, errors.New("NOT_PASS_ALLOW_WITHDRAW_AMOUNT")
		// }
	}

	// ตั้งค่าเป็น 0 ไม่ต้อง
	if totalCreditMoreThan > 0 {
		// ไม่ต้องเช็คว่า getUser.Credit - withdrawAmount P.makold confirm
		if getUser.Credit < totalCreditMoreThan {
			return 0, errors.New("NOT_PASS_CREDIT_MORE_THAN")
		}
	}
	// ตั้งค่าเป็น 0 ไม่ต้อง เช็ค
	if totalAllowWithdrawAmount > 0 {
		if withdrawAmount > totalAllowWithdrawAmount {
			return 0, errors.New("NOT_PASS_ALLOW_WITHDRAW_AMOUNT")
		}
	}
	setIsLocked := false
	for _, lockWithdraw := range getLockWithdraw {
		if lockWithdraw.IsPullCredit {
			var creditPullBackAmount float64
			// ยอดถอน >  จำกัดยอดถอน (เครดิต)
			// case นี้จะไม่มีวันเจอ
			// ตั้งค่าเป็น 0 ไม่ต้อง เช็ค
			if lockWithdraw.AllowWithdrawAmount > 0 {
				// getUser.Credit 1500
				// withdrawAmount 1200 > 100
				if withdrawAmount > lockWithdraw.AllowWithdrawAmount {
					// creditPullBackAmount = 1500-100 = 140
					creditPullBackAmount = getUser.Credit - lockWithdraw.AllowWithdrawAmount
					// ableWithdrawAmount = lockWithdraw.AllowWithdrawAmount 100
					ableWithdrawAmount = lockWithdraw.AllowWithdrawAmount
				}
				// getUser.Credit 1500
				// withdrawAmount 50 > 100
				if withdrawAmount <= lockWithdraw.AllowWithdrawAmount {
					// creditPullBackAmount = 1500 - 100 = 1400
					creditPullBackAmount = getUser.Credit - lockWithdraw.AllowWithdrawAmount
					// ableWithdrawAmount = 50
					ableWithdrawAmount = withdrawAmount
				}

				if creditPullBackAmount > 0 {
					// pull credit back auto makold บอกเอาไปแสดง list
					var createWithdrawPullCreditBack model.CreateWithdrawPullCreditBackRequest
					createWithdrawPullCreditBack.Amount = creditPullBackAmount
					createWithdrawPullCreditBack.MemberCode = getUser.MemberCode
					createWithdrawPullCreditBack.ConfirmedAt = time.Now().UTC()
					setIdAuto := int64(0)
					createWithdrawPullCreditBack.ConfirmedByAdminId = &setIdAuto
					createWithdrawPullCreditBack.Remark = "CheckAllowWithdrawCreditCondition"

					_, err := s.CreateWithdrawPullCreditBack(createWithdrawPullCreditBack)
					if err != nil {
						return 0, nil
					}
				}

			}
			var updateLockCreditWithdrawAutoUpdated model.UpdateLockCreditWithdrawAutoUpdated
			updateLockCreditWithdrawAutoUpdated.Id = lockWithdraw.Id
			updateLockCreditWithdrawAutoUpdated.WithdrawAmount = withdrawAmount
			updateLockCreditWithdrawAutoUpdated.PullCreditAmount = creditPullBackAmount
			updateLockCreditWithdrawAutoUpdated.IsLocked = &setIsLocked
			if err := s.repoBanking.UpdateLockCreditWithdrawAutoUpdated(updateLockCreditWithdrawAutoUpdated); err != nil {
				return 0, nil
			}

		} else {
			// update normal
			var updateLockCreditWithdrawAutoUpdated model.UpdateLockCreditWithdrawAutoUpdated
			updateLockCreditWithdrawAutoUpdated.Id = lockWithdraw.Id
			updateLockCreditWithdrawAutoUpdated.IsLocked = &setIsLocked
			if err := s.repoBanking.UpdateLockCreditWithdrawAutoUpdated(updateLockCreditWithdrawAutoUpdated); err != nil {
				return 0, nil
			}
		}
	}
	return ableWithdrawAmount, nil
}
