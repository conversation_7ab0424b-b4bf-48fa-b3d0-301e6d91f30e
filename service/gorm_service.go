package service

import (
	"cybergame-api/model"
	"cybergame-api/repository"
	"log"
	"os"
	"strings"
)

type GormService interface {
	// GORM
	GetTableSchema() ([]model.GormTable, error)
	GetTableSize() ([]model.GormTableSize, error)
	// Setting
	GetGormSetting() (*model.GormSettingResponse, error)
	SetGormSetting(setting model.GormSettingUpdateRequest) error
	// Compare
	CompareTableSchema(req model.GormTableCompareRequest) ([]model.GormTable, error)
}

type gormService struct {
	repo repository.GormRepository
}

func NewGormService(
	repo repository.GormRepository,
) GormService {
	return &gormService{repo}
}

func (s *gormService) GetTableSchema() ([]model.GormTable, error) {

	tables, err := s.repo.GetTableSchema()
	if err != nil {
		return nil, internalServerError(err)
	}
	return tables, nil
}

func (s *gormService) GetTableSize() ([]model.GormTableSize, error) {

	tablesSize, err := s.repo.GetTableSize(os.Getenv("DB_NAME"))
	if err != nil {
		return nil, internalServerError(err)
	}
	return tablesSize, nil
}

func (s *gormService) GetGormSetting() (*model.GormSettingResponse, error) {

	setting, err := s.repo.GetGormSetting()
	if err != nil {
		return nil, internalServerError(err)
	}
	return setting, nil
}

func (s *gormService) SetGormSetting(req model.GormSettingUpdateRequest) error {

	setting, err := s.repo.GetGormSetting()
	if err != nil {
		return internalServerError(err)
	}

	if err := s.repo.SetGormSetting(setting.Id, req); err != nil {
		return internalServerError(err)
	}
	return nil
}

func (s *gormService) CompareTableSchema(req model.GormTableCompareRequest) ([]model.GormTable, error) {

	if len(req.List) == 0 {
		return nil, badRequest("list is empty")
	}

	// validate each endpoint [domainname] = table
	mapTable := make(map[string][]model.GormTable)
	for _, item := range req.List {
		// remove https:// and http:// from domain name
		// remove www. from domain name
		domainName := item.Endpoint
		domainName = strings.Replace(domainName, "https://", "", -1)
		domainName = strings.Replace(domainName, "http://", "", -1)
		domainName = strings.Replace(domainName, "www.", "", -1)

		remoteSchema, err := s.repo.GetRemoteTableSchema(item.Endpoint, item.ApiKey)
		if err != nil {
			log.Println("CompareTableSchema.GetRemoteTableSchema.ERROR", err)
			continue
		}
		mapTable[domainName] = remoteSchema
	}

	// Compare each table with the same name
	// if each endpoint has same table schema == remove from mapTable
	var result []model.GormTable
	for _, item := range mapTable {
		// todo
		for _, table := range item {
			result = append(result, table)
		}
	}

	return result, nil
}
