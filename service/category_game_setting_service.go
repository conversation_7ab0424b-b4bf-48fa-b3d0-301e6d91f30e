package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"fmt"
	"log"
)

type CategoryGameSettingService interface {
	UpdateCategoryGameSetting(body model.UpdateCategoryGameSettingBody) error
	GetCategoryGameSetting() (*model.GetCategoryGameSettingResponse, error)
	WebGetCategoryGameSetting() (*model.GetCategoryGameSettingResponse, error)

	// v2
	GetCategoryGameSettingV2() ([]model.GetCategoryGameSettingV2, error)
	SortGetCategoryGameSettingV2(req model.DragSortRequest) error
	WebGetCategoryGameSettingV2() ([]model.GetCategoryGameSettingV2, error)
	UpdateCategoryGameSettingV2(body model.UpdateCategoryGameSettingV2) error
}

type categoryGameSettingService struct {
	repo repository.CategoryGameSettingRepository
}

func NewCategoryGameSettingService(
	repo repository.CategoryGameSettingRepository,
) CategoryGameSettingService {
	return &categoryGameSettingService{repo}
}

func (s categoryGameSettingService) UpdateCategoryGameSetting(body model.UpdateCategoryGameSettingBody) error {

	// GetCategoryGameSetting() (*model.GetCategoryGameSettingResponse, error)
	data, err := s.GetCategoryGameSetting()
	if err != nil {
		log.Println(err)
	}

	if err := s.repo.UpdateCategoryGameSetting(body); err != nil {
		return err
	}

	var adminActionCreateBody model.AdminActionCreateBody
	adminActionCreateBody.AdminId = body.UpdatedById
	adminActionCreateBody.TypeId = model.ADMIN_ACTION_CATEGORY_GAME_SETTING
	adminActionCreateBody.Detail = fmt.Sprintf("แก้ไขข้อมูล เปิด/ปิด category game")
	adminActionCreateBody.IsSuccess = true
	adminActionCreateBody.IsShow = true
	adminActionCreateBody.JsonInput = helper.StructJson(data)
	adminActionCreateBody.JsonOutput = helper.StructJson(body)
	if _, err := s.repo.CreateAdminAction(adminActionCreateBody); err != nil {
		return err
	}

	return nil
}

func (s categoryGameSettingService) GetCategoryGameSetting() (*model.GetCategoryGameSettingResponse, error) {

	record, err := s.repo.GetCategoryGameSetting()
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(recordNotFound)
		}
		return nil, internalServerError(err)
	}
	return record, nil
}

func (s categoryGameSettingService) WebGetCategoryGameSetting() (*model.GetCategoryGameSettingResponse, error) {

	record, err := s.GetCategoryGameSetting()
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(recordNotFound)
		}
		return nil, internalServerError(err)
	}

	return record, nil
}

func (s categoryGameSettingService) GetCategoryGameSettingV2() ([]model.GetCategoryGameSettingV2, error) {

	record, err := s.repo.GetCategoryGameSettingV2()
	if err != nil {
		return nil, internalServerError(err)
	}

	return record, nil
}

func (s categoryGameSettingService) SortGetCategoryGameSettingV2(req model.DragSortRequest) error {

	return s.repo.SortGetCategoryGameSettingV2(req)
}

func (s categoryGameSettingService) WebGetCategoryGameSettingV2() ([]model.GetCategoryGameSettingV2, error) {

	record, err := s.repo.WebGetCategoryGameSettingV2()
	if err != nil {
		return nil, internalServerError(err)
	}

	return record, nil
}

func (s categoryGameSettingService) UpdateCategoryGameSettingV2(body model.UpdateCategoryGameSettingV2) error {

	if err := s.repo.UpdateCategoryGameSettingV2(body); err != nil {
		return internalServerError(err)
	}

	return nil
}
