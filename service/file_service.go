package service

import (
	"bytes"
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"fmt"
	"net/http"
	"os"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/gocarina/gocsv"
	"github.com/tealeg/xlsx"
)

type FileService interface {
	GetMaxUploadBytes() int64

	UploadMedia(req *http.Request) (*model.FileUploadResponse, error)
	UploadFile(req *http.Request) (*model.FileUploadResponse, error)
	// DeleteFile(path string) error
	ServeExcel(c *gin.Context) error
	ServeCsv(c *gin.Context) error
}

type fileService struct {
	repo repository.FileRepository
}

func NewFileService(
	repo repository.FileRepository,
) FileService {
	return &fileService{repo}
}

func (s *fileService) GetMaxUploadBytes() int64 {

	maxSize := (os.Getenv("BUCKET_UPLOAD_MAX_SIZE"))
	maxBytes := int64(2 * 1000 * 1000) // 2MB

	// converse MB to byte
	if newLimitBytes, err := strconv.ParseInt(helper.StripAllButNumbers(maxSize), 10, 64); err == nil {
		if helper.EndsWith(maxSize, "MB") {
			newLimitBytes = newLimitBytes * 1000 * 1000
		} else if helper.EndsWith(maxSize, "KB") {
			newLimitBytes = newLimitBytes * 1000
		}
		if newLimitBytes > maxBytes {
			maxBytes = newLimitBytes
		}
	}
	return maxBytes
}

func (s *fileService) UploadMedia(req *http.Request) (*model.FileUploadResponse, error) {

	bucketDir := os.Getenv("BUCKET_UPLOAD_DIR")
	bucket := os.Getenv("BUCKET_NAME")
	dir := req.FormValue("directory")

	// 413 Request Entity Too Large
	// maxBytes := h.fileService.GetMaxUploadBytes()
	// Fix 413 Request Entity Too Large
	// c.Request.Body = http.MaxBytesReader(c.Writer, c.Request.Body, maxBytes)
	// ** IF 413 Payload Too Large is found.
	// ** Tell DevOp to increase the limit
	// exceeds the client_max_body_size directive in the Nginx configuration.

	file, _, err := req.FormFile("file")
	if err != nil {
		if err.Error() == "http: request body too large" {
			return nil, internalServerError(err)
		}
		return nil, internalServerError(err)
	}
	defer file.Close()

	// getExtension := strings.Split(fileHeader.Filename, ".")
	// newFileName := fmt.Sprintf("%s.%s", helper.AlphaNumerics(32), getExtension[len(getExtension)-1])
	newFileName := helper.AlphaNumerics(32)
	path := fmt.Sprintf("%s/%s/%s", bucketDir, dir, newFileName)
	upload := model.FileUpload{
		Path: path,
		File: file,
	}

	fileName, err := s.repo.UploadFile(upload)
	if err != nil {
		return nil, internalServerError(err)
	}

	path = fmt.Sprintf("https://storage.googleapis.com/%s/%s", bucket, *fileName)
	result := model.FileUploadResponse{
		ImageUrl: path,
	}
	return &result, nil
}

func (s *fileService) ServeExcel(c *gin.Context) error {

	file := xlsx.NewFile()
	sheet, err := file.AddSheet("sheet")
	if err != nil {
		return err
	}
	// ORDER BY Field name
	row1 := sheet.AddRow()
	cellA1 := row1.AddCell()
	cellA1.Value = "MESSAGE"
	cellB1 := row1.AddCell()
	cellB1.Value = "PHONE"
	row2 := sheet.AddRow()
	cellA2 := row2.AddCell()
	cellA2.Value = "this is campaign content"
	cellB2 := row2.AddCell()
	cellB2.Value = "0800000000"

	var b bytes.Buffer
	if err := file.Write(&b); err != nil {
		return err
	}
	// downloadName := time.Now().UTC().Format("data-20060102150405.xlsx")
	downloadName := ("sms_template.xlsx")
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Disposition", "attachment; filename="+downloadName)
	c.Data(http.StatusOK, "application/octet-stream", b.Bytes())
	return nil
}

func (s *fileService) ServeCsv(c *gin.Context) error {

	templateData := []*model.SmsCampaignExcelBody{
		{
			Phone:   "0800000000",
			Message: "this is campaign content",
		},
	}

	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Disposition", "attachment; filename="+"sms_template.csv")
	// c.Data(http.StatusOK, "application/octet-stream", c.Writer.Flush())
	// Use this to save the CSV back to the file
	if err := gocsv.Marshal(templateData, c.Writer); err != nil {
		return err
	}
	return nil
}

func (s *fileService) UploadFile(req *http.Request) (*model.FileUploadResponse, error) {

	bucketDir := os.Getenv("BUCKET_UPLOAD_DIR")
	bucket := os.Getenv("BUCKET_NAME")
	// dir := req.FormValue("directory")

	file, _, err := req.FormFile("file")
	if err != nil {
		if err.Error() == "http: request body too large" {
			return nil, internalServerError(err)
		}
		return nil, internalServerError(err)
	}
	defer file.Close()

	// getExtension := strings.Split(fileHeader.Filename, ".")
	// newFileName := fmt.Sprintf("%s.%s", helper.AlphaNumerics(32), getExtension[len(getExtension)-1])
	newFileName := helper.AlphaNumerics(32)
	// path := fmt.Sprintf("%s/%s/%s", bucketDir, dir, newFileName)
	path := fmt.Sprintf("%s/%s", bucketDir, newFileName)
	upload := model.FileUpload{
		Path: path,
		File: file,
	}

	fileName, err := s.repo.UploadFile(upload)
	if err != nil {
		return nil, internalServerError(err)
	}

	path = fmt.Sprintf("https://storage.googleapis.com/%s/%s", bucket, *fileName)
	result := model.FileUploadResponse{
		ImageUrl: path,
	}
	return &result, nil
}
