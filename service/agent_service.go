package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"
)

type AgentService interface {
	// Service
	AgentRegister(req model.AgentF888RegisterRequest) (*model.AgentF888RegisterResponse, error)
	AgentLogin(req model.AgentF888LoginRequest) (*model.AgentF888LoginResponse, error)
	AgentChangePassword(req model.AgentF888ChangePasswordRequest) (*model.AgentF888ChangePasswordResponse, error)
	GetAgentGameList(req model.AgentF888GameListRequest) (*model.AgentF888GameListResponse, error)
	GetAgentGameUrl(req model.AgentF888GameUrlRequest) (*model.AgentF888GameUrlResponse, error)
	AgentDeposit(req model.AgentF888DepositRequest) (*model.AgentF888DepositResponse, error)
	AgentWithdraw(req model.AgentF888WithdrawRequest) (*model.AgentF888WithdrawResponse, error)
	GetAgentPlayLog(req model.AgentF888PlayLogRequest) (*model.AgentF888PlayLogResponse, error)
	// HOOK
	HookF888CbGame(req model.HookF888CbgameRequest) (*model.HookF888CbgameResponse, error)
	// EP
	EndpointAuth001(userId int64, req model.F888Auth001Request) (*model.F888Auth001Response, error)
	EndpointGame001(userId int64, req model.F888Game001Request) (*model.F888Game001Response, error)
	EndpointGame002(userId int64, req model.F888Game002Request) (*model.F888Game002Response, error)
}

type agentService struct {
	repoAgentF888 repository.AgentF888Repository
}

func NewAgentService(
	repoAgentF888 repository.AgentF888Repository,
) AgentService {
	return &agentService{repoAgentF888}
}

func (s agentService) AgentRegister(req model.AgentF888RegisterRequest) (*model.AgentF888RegisterResponse, error) {

	// check memberCode exist
	if _, err := s.repoAgentF888.GetAgf888UserByMemberCode(req.MemberCode); err != nil {
		if err != gorm.ErrRecordNotFound {
			return nil, badRequest("MEMBERCODE_EXIST") // CANT_CHECK
		}
	} else {
		return nil, badRequest("MEMBERCODE_EXIST")
	}

	// only Store in Agf88-DB
	var createBody model.Agf888UserCreateBody
	createBody.MemberCode = req.MemberCode
	createBody.Fullname = req.Fullname
	createBody.Remark = req.Remark
	insertId, err := s.repoAgentF888.CreateAgf888User(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	var result model.AgentF888RegisterResponse
	result.Id = *insertId
	result.MemberCode = req.MemberCode

	return &result, nil
}

func (s agentService) AgentLogin(req model.AgentF888LoginRequest) (*model.AgentF888LoginResponse, error) {

	user, err := s.repoAgentF888.GetAgf888UserByMemberCode(req.MemberCode)
	if err != nil {
		return nil, badRequest("MEMBER_NOT_FOUND")
	}

	// RemoteEndpoint
	var remoteReq model.F888Auth001Request
	remoteReq.Username = user.MemberCode
	resp, err := s.repoAgentF888.EndpointAuth001(remoteReq)
	if err != nil {
		return nil, internalServerError(err)
	}

	var result model.AgentF888LoginResponse
	result.Id = user.Id
	result.MemberCode = user.MemberCode
	result.Credit = user.Credit
	result.GameToken = resp.Data.Token

	return &result, nil
}

func (s agentService) AgentChangePassword(req model.AgentF888ChangePasswordRequest) (*model.AgentF888ChangePasswordResponse, error) {

	//todo

	return nil, nil
}

func (s agentService) GetAgentGameList(req model.AgentF888GameListRequest) (*model.AgentF888GameListResponse, error) {

	// todo

	return nil, nil
}

func (s agentService) GetAgentGameUrl(req model.AgentF888GameUrlRequest) (*model.AgentF888GameUrlResponse, error) {

	// todo

	return nil, nil
}

func (s agentService) AgentDeposit(req model.AgentF888DepositRequest) (*model.AgentF888DepositResponse, error) {

	actionAt := time.Now().UTC()

	if req.Amount <= 0 {
		return nil, badRequest("INVALID_AMOUNT")
	}

	user, err := s.repoAgentF888.GetAgf888UserByMemberCode(req.MemberCode)
	if err != nil {
		return nil, badRequest("MEMBER_NOT_FOUND")
	}

	var createBody model.Agf888UserTransactionCreateBody
	createBody.UserId = user.Id
	createBody.TypeId = model.AGF888_USER_TRANSACTION_TYPE_DEPOSIT
	createBody.Ref1 = req.MemberCode
	createBody.Ref2 = fmt.Sprintf("USER_%s", req.MemberCode)
	createBody.Detail = "AGENT_DEPOSIT"
	createBody.TransferAt = actionAt
	createBody.CreditBefore = user.Credit
	createBody.CreditAmount = req.Amount
	createBody.CreditAfter = user.Credit + req.Amount
	balance, err := s.repoAgentF888.Agf888IncreaseUserCredit(createBody)
	if err != nil {
		return nil, badRequest("TRANSACTION_FAILED")
	}

	var result model.AgentF888DepositResponse
	result.MemberCode = user.MemberCode
	result.Balance = balance

	return &result, nil
}

func (s agentService) AgentWithdraw(req model.AgentF888WithdrawRequest) (*model.AgentF888WithdrawResponse, error) {

	actionAt := time.Now().UTC()

	if req.Amount <= 0 {
		return nil, badRequest("INVALID_AMOUNT")
	}

	user, err := s.repoAgentF888.GetAgf888UserByMemberCode(req.MemberCode)
	if err != nil {
		return nil, badRequest("MEMBER_NOT_FOUND")
	}

	withdrawAmount := req.Amount
	if user.Credit < withdrawAmount {
		return nil, badRequest("INSUFFICIENT_BALANCE")
	}

	var createBody model.Agf888UserTransactionCreateBody
	createBody.UserId = user.Id
	createBody.TypeId = model.AGF888_USER_TRANSACTION_TYPE_WITHDRAW
	createBody.Ref1 = req.MemberCode
	createBody.Ref2 = fmt.Sprintf("USER_%s", req.MemberCode)
	createBody.Detail = "AGENT_WITHDRAW"
	createBody.TransferAt = actionAt
	createBody.CreditBefore = user.Credit
	createBody.CreditAmount = req.Amount
	createBody.CreditAfter = user.Credit - req.Amount
	balance, err := s.repoAgentF888.Agf888DecreaseUserCredit(createBody)
	if err != nil {
		return nil, badRequest("TRANSACTION_FAILED")
	}

	var result model.AgentF888WithdrawResponse
	result.MemberCode = user.MemberCode
	result.Balance = balance

	return &result, nil
}

func (s agentService) GetAgentPlayLog(req model.AgentF888PlayLogRequest) (*model.AgentF888PlayLogResponse, error) {

	// todo

	return nil, nil
}

func (s agentService) HookF888CbGame(req model.HookF888CbgameRequest) (*model.HookF888CbgameResponse, error) {

	var nospacejson interface{}
	if errJson := json.Unmarshal([]byte(req.JsonPayload), &nospacejson); errJson == nil {
		req.JsonPayload = helper.StructJson(nospacejson)
	}

	var createBody model.HookF888CbgameCreateBody
	createBody.Name = "HOOK_F888_CBGAME"
	createBody.JsonPayload = req.JsonPayload
	if _, err := s.repoAgentF888.CreateF888Webhook(createBody); err != nil {
		return nil, internalServerError(err)
	}

	// todo - do something with JsonPayload HookF888CbgameBody
	jsonByte := []byte(req.JsonPayload)
	var body model.HookF888CbgameBody
	errJson := json.Unmarshal(jsonByte, &body)
	if errJson != nil {
		return nil, errors.New("CANT_PARSE_BODY_DATA")
	}

	// fmt.Println("body", helper.StructJson(body))

	// todo - validate body.Hashed 241218 เช็คแล้วไม่ตรง ไว้ทำทีหลัง todo
	// Format of of it are
	// (JSON String of Data) + API Secret Key
	// agentSecret := os.Getenv("AGF88_API_SECRET")
	// agentSecret = "1bc6be78da79a44cd723a4259a19b59488814c50c8114ffd80e12897216c9123"
	// // {betId’: ‘test-bet-id’, ‘amount’:‘1000.00’}
	// // sampleBody := `{"betId": "test-bet-id", "amount": "1000.00"}`
	// // payloadHash := helper.HashSHA256(+"1bc6be78da79a44cd723a4259a19b59488814c50c8114ffd80e12897216c9123")
	// pl := helper.StructJson(body.Data.Payload)
	// pl = `{"betId": "test-bet-id", "amount": "1000.00"}`
	// pl = `{‘betId’: ‘test-bet-id’, ‘amount’:‘1000.00’}`

	// fmt.Println("DEBUG.pl", pl)
	// payloadHash := helper.HashSHA256(pl + agentSecret)
	// fmt.Println("DEBUG.payloadHash", payloadHash)
	// fmt.Println("DEBUG.body.hashed", body.Hashed)
	// if payloadHash != body.Hashed {
	// 	return nil, badRequest("INVALID_HASHED")
	// }

	var result model.HookF888CbgameResponse

	// todo return user info
	result.Data.Username = body.Data.Payload.Username
	result.Data.Balance = 12.34
	result.Data.Currency = "thb"

	return &result, nil
}

func (s agentService) EndpointAuth001(userId int64, req model.F888Auth001Request) (*model.F888Auth001Response, error) {

	if req.Username == "" {
		return nil, badRequest("EMPTY_USERNAME")
	}

	// todo userId

	resp, err := s.repoAgentF888.EndpointAuth001(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	return resp, nil
}

func (s agentService) EndpointGame001(userId int64, req model.F888Game001Request) (*model.F888Game001Response, error) {

	if req.Username == "" {
		return nil, badRequest("EMPTY_USERNAME")
	}

	userToken := req.Username // todo userId

	resp, err := s.repoAgentF888.EndpointGame001(userToken)
	if err != nil {
		return nil, internalServerError(err)
	}
	return resp, nil
}

func (s agentService) EndpointGame002(userId int64, req model.F888Game002Request) (*model.F888Game002Response, error) {

	userToken := "req.Username" // todo userId

	resp, err := s.repoAgentF888.EndpointGame002(userToken, req)
	if err != nil {
		return nil, internalServerError(err)
	}
	return resp, nil
}
