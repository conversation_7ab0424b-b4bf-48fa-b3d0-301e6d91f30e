package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"time"

	"gorm.io/gorm"
)

func NewGameOrderService(
	gameOrderRepository repository.GameOrderRepository,
	sharedDb *gorm.DB,
) GameOrderService {
	return &gameOrderService{gameOrderRepository, sharedDb}
}

type gameOrderService struct {
	repo     repository.GameOrderRepository
	sharedDb *gorm.DB
}

type GameOrderService interface {
	CreateBetOrderLottery(req model.CreateBetOrderLotteryRequest) (*model.CreateOrderLotteryResponse, error)
	CreateWinOrderLottery(req model.CreateWinOrderLotteryRequest) (*model.CreateOrderLotteryResponse, error)
	CreateRefundOrderLottery(req model.CreateRefundOrderLotteryRequest) (*model.CreateOrderLotteryResponse, error)
	CreateCommissionOrderLottery(req model.CreateCommissionOrderLotteryRequest) (*model.CreateOrderLotteryResponse, error)
	GameOrderEncodeExample(req model.SetSentOrderLotteryEncodeRequest) (string, error)
	GameOrderDecodeExample(req string) (*model.SetSentOrderLotteryEncodeRequest, error)

	//  main outsource
	CreateGameOrder(req model.CreateOrderRequest) (*model.CreateOrderLotteryResponse, error)
}

func (s *gameOrderService) CreateBetOrderLottery(req model.CreateBetOrderLotteryRequest) (*model.CreateOrderLotteryResponse, error) {
	// todo เก็บ error ส่งกลับไป เป็น model ตอนนี้ ใส่ไว้ใน error ครบแล้ว เผื่อ external lotto จะ เอา เป็น code หรือ อื่นๆ

	// [CHECK EXIST REF1NO]
	checkExistRef1No, _ := s.repo.GetCheckRef1No(req.Ref1No)
	if checkExistRef1No != nil {
		var createOrderLogBody model.CreateOrderLogRequest
		createOrderLogBody.JsonReq = fmt.Sprintln("request: ", helper.StructJson(req), "checkExistRef1No: ", helper.StructJson(checkExistRef1No))
		createOrderLogBody.Remark = "[FAIL] CreateBetOrderLottery.DUPLICATE_REF1NO"
		if err := s.repo.CreateOrderLog(createOrderLogBody); err != nil {
			return nil, badRequest("LOG_FAILED")
		}
		return nil, badRequest("DUPLICATE_REF1NO")
	}

	// 1. [race condition]
	onUseCategory := model.ORDER_CATEGORY_LOTTERY
	onUseType := model.ORDER_TYPE_LOTTERY_BET
	// U = user C = category T = type D = date
	createOrderRef2No := fmt.Sprintf("U%dC%dT%d-REF1NO-%s", req.UserId, onUseCategory, onUseType, req.Ref1No)
	// new action key confirm 2025-02-15
	newActionKey := fmt.Sprintf("REF1NO-%s-D%s", req.Ref1No, time.Now().UTC().Format("20060102150405"))
	getOrderRaceCondition, _ := s.repo.GetOrderRaceConditionByActionKey(newActionKey)
	if getOrderRaceCondition != nil {
		return nil, badRequest("DUPLICATE_RACE_CONDITION")
	}

	var createOrderRaceConditionBody model.CreateOrderRaceConditionBody
	createOrderRaceConditionBody.ActionKey = newActionKey
	raceConditionId, err := s.repo.CreateOrderRaceCondition(createOrderRaceConditionBody)
	if err != nil {
		return nil, badRequest("RACE_CONDITION")
	}

	if raceConditionId > 0 {
		// 2. [Check user balance]
		getUser, _ := GetUser(repository.NewUserRepository(s.sharedDb), req.UserId)
		if getUser == nil {
			// [Log]
			var createOrderLogBody model.CreateOrderLogRequest
			createOrderLogBody.JsonReq = fmt.Sprintln("request: ", helper.StructJson(req), "userId: ", req.UserId)
			createOrderLogBody.Remark = "[FAIL] CreateBetOrderLottery.USER_NOT_FOUND"
			if err := s.repo.CreateOrderLog(createOrderLogBody); err != nil {
				return nil, badRequest("LOG_FAILED")
			}
			return nil, badRequest("USER_NOT_FOUND")
		}
		if getUser.Credit < req.Amount {
			// [Log]
			var createOrderLogBody model.CreateOrderLogRequest
			createOrderLogBody.JsonReq = fmt.Sprintln("request: ", helper.StructJson(req), "getUser.credit: ", getUser.Credit, "Bet: ", req.Amount, "userId: ", req.UserId)
			createOrderLogBody.Remark = "[FAIL] CreateBetOrderLottery.INSUFFICIENT_BALANCE"
			if err := s.repo.CreateOrderLog(createOrderLogBody); err != nil {
				return nil, badRequest("LOG_FAILED")
			}
			return nil, badRequest("INSUFFICIENT_BALANCE")
		}

		// 3. [Create order]
		var createOrderLotteryBody model.CreateOrderLotteryBody
		createOrderLotteryBody.Ref1No = req.Ref1No
		createOrderLotteryBody.Ref2No = createOrderRef2No
		createOrderLotteryBody.UserId = req.UserId
		createOrderLotteryBody.Amount = req.Amount
		createOrderLotteryBody.OrderCategoryId = onUseCategory
		createOrderLotteryBody.OrderTypeId = onUseType
		createOrderLotteryBody.OrderStatusId = model.ORDER_STATUS_PENDING
		orderId, err := s.repo.CreateOrderLottery(createOrderLotteryBody)
		if err != nil {
			// [Log]
			var createOrderLogBody model.CreateOrderLogRequest
			// createOrderLogBody.JsonReq = fmt.Sprintln("request", req, "createOrderLotteryBody", createOrderLotteryBody, "error", err)
			createOrderLogBody.JsonReq = fmt.Sprintln("request: ", helper.StructJson(req), "createOrderLotteryBody: ", helper.StructJson(createOrderLotteryBody), "error: ", err)
			createOrderLogBody.Remark = "[FAIL] CreateBetOrderLottery.CREATE_ORDER_FAIDED"
			if err := s.repo.CreateOrderLog(createOrderLogBody); err != nil {
				return nil, badRequest("LOG_FAILED")
			}
			return nil, badRequest("CREATE_ORDER_FAIDED")
		}

		// 4 [Decrease User Credit]
		var userCreditReq model.UserTransactionCreateRequest
		userCreditReq.UserId = req.UserId
		userCreditReq.TypeId = model.CREDIT_TYPE_LOTTERY
		userCreditReq.Detail = model.ORDER_TYPE_LOTTERY_BET_TH
		userCreditReq.Amount = req.Amount
		userCreditReq.RefId = &orderId
		userCreditReq.StartWorkAt = time.Now().UTC()
		isShow := false
		userCreditReq.IsShow = &isShow

		agentResp, err := s.repo.DecreaseUserCredit(userCreditReq)
		if err != nil {
			// 4.1 [SET ORDER STATUS FAILED]
			var updateOrderLotteryStatusBody model.UpdateOrderLotteryStatusRequest
			updateOrderLotteryStatusBody.Id = orderId
			statusFail := model.ORDER_STATUS_FAILED
			// ทำการ set ref external เป็น failed จะ ได้ failed ซ้ำกันได้ เมื่อมี request เข้ามาใหม่
			ref1NoFail := fmt.Sprintf("FAILED%d-%s", orderId, req.Ref1No)
			updateOrderLotteryStatusBody.OrderStatusId = &statusFail
			updateOrderLotteryStatusBody.Ref1No = &ref1NoFail
			if err := s.repo.UpdateOrderLotteryStatus(updateOrderLotteryStatusBody); err != nil {
				// [Log]
				var createOrderLogBody model.CreateOrderLogRequest
				createOrderLogBody.OrderId = orderId
				createOrderLogBody.JsonReq = fmt.Sprintln("request: ", helper.StructJson(req), "userCreditReq: ", helper.StructJson(createOrderLotteryBody), "error: ", err)
				createOrderLogBody.Remark = "[FAIL] CreateBetOrderLottery.UPDATE_ORDER_STATUS_FAILED"
				if err := s.repo.CreateOrderLog(createOrderLogBody); err != nil {
					return nil, badRequest("LOG_FAILED")
				}
				return nil, badRequest("UPDATE_ORDER_STATUS_FAILED")
			}

			var createOrderLogBody model.CreateOrderLogRequest
			// [Log]
			createOrderLogBody.OrderId = orderId
			createOrderLogBody.JsonReq = fmt.Sprintln("request: ", helper.StructJson(req), "userCreditReq: ", helper.StructJson(createOrderLotteryBody), "error: ", err)
			createOrderLogBody.Remark = "[FAIL] CreateBetOrderLottery.DECREASE_USER_CREDIT_FAILED"
			if err := s.repo.CreateOrderLog(createOrderLogBody); err != nil {
				return nil, badRequest("LOG_FAILED")
			}
			return nil, err
		}

		if agentResp.AgentSuccess {
			// 4.2 [ SET ORDER STATUS SUCCESS]
			var updateOrderLotteryStatusBody model.UpdateOrderLotteryStatusRequest
			updateOrderLotteryStatusBody.Id = orderId
			statusFail := model.ORDER_STATUS_SUCCESS
			updateOrderLotteryStatusBody.OrderStatusId = &statusFail
			if err := s.repo.UpdateOrderLotteryStatus(updateOrderLotteryStatusBody); err != nil {
				// [Log]
				var createOrderLogBody model.CreateOrderLogRequest
				createOrderLogBody.OrderId = orderId
				createOrderLogBody.JsonReq = fmt.Sprintln("request: ", helper.StructJson(req), "userCreditReq: ", helper.StructJson(createOrderLotteryBody), "error: ", err)
				createOrderLogBody.Remark = "[FAIL] CreateBetOrderLottery.UPDATE_ORDER_STATUS_FAILED"
				if err := s.repo.CreateOrderLog(createOrderLogBody); err != nil {
					return nil, badRequest("LOG_FAILED")
				}
				return nil, badRequest("UPDATE_ORDER_STATUS_FAILED")
			}
		}

		var response model.CreateOrderLotteryResponse
		response.Id = orderId
		response.Ref2No = createOrderRef2No
		response.Message = "SUCCESS_LOTTERY_BET"
		return &response, nil
	}

	return nil, badRequest("UNABLE_TO_CREATE_ORDER")
}

func (s *gameOrderService) CreateWinOrderLottery(req model.CreateWinOrderLotteryRequest) (*model.CreateOrderLotteryResponse, error) {
	// return nil, nil

	// [CHECK EXIST REF1NO]
	checkExistRef1No, _ := s.repo.GetCheckRef1No(req.Ref1No)
	if checkExistRef1No != nil {
		var createOrderLogBody model.CreateOrderLogRequest
		createOrderLogBody.JsonReq = fmt.Sprintln("request: ", helper.StructJson(req), "checkExistRef1No: ", helper.StructJson(checkExistRef1No))
		createOrderLogBody.Remark = "[FAIL] CreateWinOrderLottery.DUPLICATE_REF1NO"
		if err := s.repo.CreateOrderLog(createOrderLogBody); err != nil {
			return nil, badRequest("LOG_FAILED")
		}
		return nil, badRequest("DUPLICATE_REF1NO")
	}

	// 1. [race condition]
	onUseCategory := model.ORDER_CATEGORY_LOTTERY
	onUseType := model.ORDER_TYPE_LOTTERY_WIN
	// U = user C = category T = type D = date
	createOrderRef2No := fmt.Sprintf("U%dC%dT%d-REF1NO-%s", req.UserId, onUseCategory, onUseType, req.Ref1No)
	// new action key confirm 2025-02-15
	newActionKey := fmt.Sprintf("REF1NO-%s-D%s", req.Ref1No, time.Now().UTC().Format("20060102150405"))
	getOrderRaceCondition, _ := s.repo.GetOrderRaceConditionByActionKey(newActionKey)
	if getOrderRaceCondition != nil {
		return nil, badRequest("DUPLICATE_RACE_CONDITION")
	}

	var createOrderRaceConditionBody model.CreateOrderRaceConditionBody
	createOrderRaceConditionBody.ActionKey = newActionKey
	raceConditionId, err := s.repo.CreateOrderRaceCondition(createOrderRaceConditionBody)
	if err != nil {
		return nil, badRequest("RACE_CONDITION")
	}

	if raceConditionId > 0 {

		// 2. [Check user balance]
		getUser, _ := GetUser(repository.NewUserRepository(s.sharedDb), req.UserId)
		if getUser == nil {
			// [Log]
			var createOrderLogBody model.CreateOrderLogRequest
			createOrderLogBody.JsonReq = fmt.Sprintln("request: ", helper.StructJson(req), "userId: ", req.UserId)
			createOrderLogBody.Remark = "[FAIL] CreateWinOrderLottery.USER_NOT_FOUND"
			if err := s.repo.CreateOrderLog(createOrderLogBody); err != nil {
				return nil, badRequest("LOG_FAILED")
			}
			return nil, badRequest("USER_NOT_FOUND")
		}

		var createOrderLotteryBody model.CreateOrderLotteryBody
		createOrderLotteryBody.Ref1No = req.Ref1No
		createOrderLotteryBody.Ref2No = createOrderRef2No
		createOrderLotteryBody.UserId = req.UserId
		createOrderLotteryBody.Amount = req.Amount
		createOrderLotteryBody.OrderCategoryId = onUseCategory
		createOrderLotteryBody.OrderTypeId = onUseType
		createOrderLotteryBody.OrderStatusId = model.ORDER_STATUS_PENDING
		orderId, err := s.repo.CreateOrderLottery(createOrderLotteryBody)
		if err != nil {
			// [Log]
			var createOrderLogBody model.CreateOrderLogRequest
			createOrderLogBody.JsonReq = fmt.Sprintln("request", req, "createOrderLotteryBody", createOrderLotteryBody, "error", err)
			createOrderLogBody.Remark = "[FAIL] CreateBetOrderLottery.CREATE_ORDER_FAIDED"
			if err := s.repo.CreateOrderLog(createOrderLogBody); err != nil {
				return nil, badRequest("LOG_FAILED")
			}
			return nil, badRequest("CREATE_ORDER_FAIDED")
		}

		// 4 [Increase User Credit]
		var userCreditReq model.UserTransactionCreateRequest
		userCreditReq.UserId = req.UserId
		userCreditReq.TypeId = model.CREDIT_TYPE_LOTTERY
		userCreditReq.Detail = model.ORDER_TYPE_LOTTERY_WIN_TH
		// userCreditReq.Amount = req.Amount หรือ จะใช้ userCreditReq.BonusAmount
		userCreditReq.BonusAmount = req.Amount
		userCreditReq.RefId = &orderId
		userCreditReq.StartWorkAt = time.Now().UTC()

		agentResp, err := s.repo.IncreaseUserCredit(userCreditReq)
		if err != nil {
			// 4.1 [SET ORDER STATUS FAILED]
			var updateOrderLotteryStatusBody model.UpdateOrderLotteryStatusRequest
			updateOrderLotteryStatusBody.Id = orderId
			statusFail := model.ORDER_STATUS_FAILED
			// ทำการ set ref external เป็น failed จะ ได้ failed ซ้ำกันได้ เมื่อมี request เข้ามาใหม่
			ref1NoFail := fmt.Sprintf("FAILED%d-%s", orderId, req.Ref1No)
			updateOrderLotteryStatusBody.OrderStatusId = &statusFail
			updateOrderLotteryStatusBody.Ref1No = &ref1NoFail
			if err := s.repo.UpdateOrderLotteryStatus(updateOrderLotteryStatusBody); err != nil {
				// [Log]
				var createOrderLogBody model.CreateOrderLogRequest
				createOrderLogBody.OrderId = orderId
				createOrderLogBody.JsonReq = fmt.Sprintln("request: ", helper.StructJson(req), "userCreditReq: ", helper.StructJson(createOrderLotteryBody), "error: ", err)
				createOrderLogBody.Remark = "[FAIL] CreateWinOrderLottery.UPDATE_ORDER_STATUS_FAILED"
				if err := s.repo.CreateOrderLog(createOrderLogBody); err != nil {
					return nil, badRequest("LOG_FAILED")
				}
				return nil, badRequest("UPDATE_ORDER_STATUS_FAILED")
			}
			var createOrderLogBody model.CreateOrderLogRequest
			createOrderLogBody.OrderId = orderId
			createOrderLogBody.JsonReq = fmt.Sprintln("request: ", helper.StructJson(req), "userCreditReq: ", helper.StructJson(createOrderLotteryBody), "error: ", err)
			createOrderLogBody.Remark = "[FAIL] CreateWinOrderLottery.INCREASE_USER_CREDIT_FAILED"
			if err := s.repo.CreateOrderLog(createOrderLogBody); err != nil {
				return nil, badRequest("LOG_FAILED")
			}
			return nil, err
		}

		if agentResp.AgentSuccess {
			// 4.2 [ SET ORDER STATUS SUCCESS]
			var updateOrderLotteryStatusBody model.UpdateOrderLotteryStatusRequest
			updateOrderLotteryStatusBody.Id = orderId
			statusFail := model.ORDER_STATUS_SUCCESS
			updateOrderLotteryStatusBody.OrderStatusId = &statusFail
			if err := s.repo.UpdateOrderLotteryStatus(updateOrderLotteryStatusBody); err != nil {
				// [Log]
				var createOrderLogBody model.CreateOrderLogRequest
				createOrderLogBody.OrderId = orderId
				createOrderLogBody.JsonReq = fmt.Sprintln("request: ", helper.StructJson(req), "userCreditReq: ", helper.StructJson(createOrderLotteryBody), "error: ", err)
				createOrderLogBody.Remark = "[FAIL] CreateWinOrderLottery.UPDATE_ORDER_STATUS_FAILED"
				if err := s.repo.CreateOrderLog(createOrderLogBody); err != nil {
					return nil, badRequest("LOG_FAILED")
				}
				return nil, badRequest("UPDATE_ORDER_STATUS_FAILED")
			}
		}

		var response model.CreateOrderLotteryResponse
		response.Id = orderId
		response.Ref2No = createOrderRef2No
		response.Message = "SUCCESS_LOTTERY_WIN"
		return &response, nil

	}

	return nil, badRequest("UNABLE_TO_CREATE_ORDER")
}

func (s *gameOrderService) CreateRefundOrderLottery(req model.CreateRefundOrderLotteryRequest) (*model.CreateOrderLotteryResponse, error) {

	// [TODO : CHECK EXIST REF1NO ไม่แน่ใจว่า ตอนคืนจะส่ง RefCode เดิมมาไหม ถ้าส่งมา จะ ไป update ให้ แทน create]
	checkExistRef1No, _ := s.repo.GetCheckRef1No(req.Ref1No)
	if checkExistRef1No != nil {
		var createOrderLogBody model.CreateOrderLogRequest
		createOrderLogBody.JsonReq = fmt.Sprintln("request", req, "checkExistRef1No", checkExistRef1No)
		createOrderLogBody.Remark = "CreateRefundOrderLottery.DUPLICATE_REF1NO"
		if err := s.repo.CreateOrderLog(createOrderLogBody); err != nil {
			return nil, badRequest("LOG_FAILED")
		}
		return nil, badRequest("DUPLICATE_REF1NO")
	}

	// 1. [race condition]
	onUseCategory := model.ORDER_CATEGORY_LOTTERY
	onUseType := model.ORDER_TYPE_LOTTERY_REFUND
	// U = user C = category T = type D = date
	createOrderRef2No := fmt.Sprintf("U%dC%dT%d-REF1NO-%s", req.UserId, onUseCategory, onUseType, req.Ref1No)
	// new action key confirm 2025-02-15
	newActionKey := fmt.Sprintf("REF1NO-%s-D%s", req.Ref1No, time.Now().UTC().Format("20060102150405"))
	getOrderRaceCondition, _ := s.repo.GetOrderRaceConditionByActionKey(newActionKey)
	if getOrderRaceCondition != nil {
		return nil, badRequest("DUPLICATE_RACE_CONDITION")
	}

	var createOrderRaceConditionBody model.CreateOrderRaceConditionBody
	createOrderRaceConditionBody.ActionKey = newActionKey
	raceConditionId, err := s.repo.CreateOrderRaceCondition(createOrderRaceConditionBody)
	if err != nil {
		return nil, badRequest("RACE_CONDITION")
	}

	if raceConditionId > 0 {

		// 2. [Check user balance]
		getUser, _ := GetUser(repository.NewUserRepository(s.sharedDb), req.UserId)
		if getUser == nil {
			// [Log]
			var createOrderLogBody model.CreateOrderLogRequest
			createOrderLogBody.JsonReq = fmt.Sprintln("request", helper.StructJson(req), "userId", req.UserId)
			createOrderLogBody.Remark = "[FAIL] CreateRefundOrderLottery.USER_NOT_FOUND"
			if err := s.repo.CreateOrderLog(createOrderLogBody); err != nil {
				return nil, badRequest("LOG_FAILED")
			}
			return nil, badRequest("USER_NOT_FOUND")
		}

		var createOrderLotteryBody model.CreateOrderLotteryBody
		createOrderLotteryBody.Ref1No = req.Ref1No
		createOrderLotteryBody.Ref2No = createOrderRef2No
		createOrderLotteryBody.UserId = req.UserId
		createOrderLotteryBody.Amount = req.Amount
		createOrderLotteryBody.OrderCategoryId = onUseCategory
		createOrderLotteryBody.OrderTypeId = onUseType
		createOrderLotteryBody.OrderStatusId = model.ORDER_STATUS_PENDING
		orderId, err := s.repo.CreateOrderLottery(createOrderLotteryBody)
		if err != nil {
			// [Log]
			var createOrderLogBody model.CreateOrderLogRequest
			createOrderLogBody.JsonReq = fmt.Sprintln("request", helper.StructJson(req), "createOrderLotteryBody", helper.StructJson(createOrderLotteryBody), "error", err)
			createOrderLogBody.Remark = "[FAIL] CreateRefundOrderLottery.CREATE_ORDER_FAIDED"
			if err := s.repo.CreateOrderLog(createOrderLogBody); err != nil {
				return nil, badRequest("LOG_FAILED")
			}
			return nil, badRequest("CREATE_ORDER_FAIDED")
		}

		// 4 [Increase User Credit]
		var userCreditReq model.UserTransactionCreateRequest
		userCreditReq.UserId = req.UserId
		userCreditReq.TypeId = model.CREDIT_TYPE_LOTTERY
		userCreditReq.Detail = model.ORDER_TYPE_LOTTERY_REFUND_TH
		// userCreditReq.Amount = req.Amount หรือ จะใช้ userCreditReq.BonusAmount
		userCreditReq.BonusAmount = req.Amount
		userCreditReq.RefId = &orderId
		userCreditReq.StartWorkAt = time.Now().UTC()

		agentResp, err := s.repo.IncreaseUserCredit(userCreditReq)
		if err != nil {
			// 4.1 [SET ORDER STATUS FAILED]
			var updateOrderLotteryStatusBody model.UpdateOrderLotteryStatusRequest
			updateOrderLotteryStatusBody.Id = orderId
			statusFail := model.ORDER_STATUS_FAILED
			// ทำการ set ref external เป็น failed จะ ได้ failed ซ้ำกันได้ เมื่อมี request เข้ามาใหม่
			ref1NoFail := fmt.Sprintf("FAILED%d-%s", orderId, req.Ref1No)
			updateOrderLotteryStatusBody.OrderStatusId = &statusFail
			updateOrderLotteryStatusBody.Ref1No = &ref1NoFail
			if err := s.repo.UpdateOrderLotteryStatus(updateOrderLotteryStatusBody); err != nil {
				// [Log]
				var createOrderLogBody model.CreateOrderLogRequest
				createOrderLogBody.OrderId = orderId
				createOrderLogBody.JsonReq = fmt.Sprintln("request", helper.StructJson(req), "userCreditReq", helper.StructJson(createOrderLotteryBody), "error", err)
				createOrderLogBody.Remark = "[FAIL] CreateRefundOrderLottery.UPDATE_ORDER_STATUS_FAILED"
				if err := s.repo.CreateOrderLog(createOrderLogBody); err != nil {
					return nil, badRequest("LOG_FAILED")
				}
				return nil, badRequest("UPDATE_ORDER_STATUS_FAILED")
			}
			var createOrderLogBody model.CreateOrderLogRequest
			createOrderLogBody.OrderId = orderId
			createOrderLogBody.JsonReq = fmt.Sprintln("request", helper.StructJson(req), "userCreditReq", helper.StructJson(createOrderLotteryBody), "error", err)
			createOrderLogBody.Remark = "[FAIL] CreateRefundOrderLottery.INCREASE_USER_CREDIT_FAILED"
			if err := s.repo.CreateOrderLog(createOrderLogBody); err != nil {
				return nil, badRequest("LOG_FAILED")
			}
			return nil, err
		}

		if agentResp.AgentSuccess {
			// 4.2 [ SET ORDER STATUS SUCCESS]
			var updateOrderLotteryStatusBody model.UpdateOrderLotteryStatusRequest
			updateOrderLotteryStatusBody.Id = orderId
			statusFail := model.ORDER_STATUS_SUCCESS
			updateOrderLotteryStatusBody.OrderStatusId = &statusFail
			if err := s.repo.UpdateOrderLotteryStatus(updateOrderLotteryStatusBody); err != nil {
				// [Log]
				var createOrderLogBody model.CreateOrderLogRequest
				createOrderLogBody.OrderId = orderId
				createOrderLogBody.JsonReq = fmt.Sprintln("request", helper.StructJson(req), "userCreditReq", helper.StructJson(createOrderLotteryBody), "error", err)
				createOrderLogBody.Remark = "[FAIL] CreateRefundOrderLottery.UPDATE_ORDER_STATUS_FAILED"
				if err := s.repo.CreateOrderLog(createOrderLogBody); err != nil {
					return nil, badRequest("LOG_FAILED")
				}
				return nil, badRequest("UPDATE_ORDER_STATUS_FAILED")
			}
		}

		var response model.CreateOrderLotteryResponse
		response.Id = orderId
		response.Ref2No = createOrderRef2No
		response.Message = "SUCCESS_LOTTERY_REFUND"
		return &response, nil

	}

	return nil, badRequest("UNABLE_TO_CREATE_ORDER")
}

func (s *gameOrderService) CreateCommissionOrderLottery(req model.CreateCommissionOrderLotteryRequest) (*model.CreateOrderLotteryResponse, error) {

	checkExistRef1No, _ := s.repo.GetCheckRef1No(req.Ref1No)
	if checkExistRef1No != nil {
		var createOrderLogBody model.CreateOrderLogRequest
		createOrderLogBody.JsonReq = fmt.Sprintln("request: ", helper.StructJson(req), "checkExistRef1No: ", helper.StructJson(checkExistRef1No))
		createOrderLogBody.Remark = "[FAIL] CreateCommissionOrderLottery.DUPLICATE_REF1NO"
		if err := s.repo.CreateOrderLog(createOrderLogBody); err != nil {
			return nil, badRequest("LOG_FAILED")
		}
		return nil, badRequest("DUPLICATE_REF1NO")
	}

	// 1. [race condition]
	onUseCategory := model.ORDER_CATEGORY_LOTTERY
	onUseType := model.ORDER_TYPE_LOTTERY_COMMISSION
	// U = user C = category T = type D = date
	createOrderRef2No := fmt.Sprintf("U%dC%dT%d-REF1NO-%s", req.UserId, onUseCategory, onUseType, req.Ref1No)
	// new action key confirm 2025-02-15
	newActionKey := fmt.Sprintf("REF1NO-%s-D%s", req.Ref1No, time.Now().UTC().Format("20060102150405"))
	getOrderRaceCondition, _ := s.repo.GetOrderRaceConditionByActionKey(newActionKey)
	if getOrderRaceCondition != nil {
		return nil, badRequest("DUPLICATE_RACE_CONDITION")
	}

	var createOrderRaceConditionBody model.CreateOrderRaceConditionBody
	createOrderRaceConditionBody.ActionKey = newActionKey
	raceConditionId, err := s.repo.CreateOrderRaceCondition(createOrderRaceConditionBody)
	if err != nil {
		return nil, badRequest("RACE_CONDITION")
	}

	if raceConditionId > 0 {

		// 2. [Check user balance]
		getUser, _ := GetUser(repository.NewUserRepository(s.sharedDb), req.UserId)
		if getUser == nil {
			// [Log]
			var createOrderLogBody model.CreateOrderLogRequest
			createOrderLogBody.JsonReq = fmt.Sprintln("request: ", helper.StructJson(req), "userId", req.UserId)
			createOrderLogBody.Remark = "[FAIL] CreateCommissionOrderLottery.USER_NOT_FOUND"
			if err := s.repo.CreateOrderLog(createOrderLogBody); err != nil {
				return nil, badRequest("LOG_FAILED")
			}
			return nil, badRequest("USER_NOT_FOUND")
		}

		var createOrderLotteryBody model.CreateOrderLotteryBody
		createOrderLotteryBody.Ref1No = req.Ref1No
		createOrderLotteryBody.Ref2No = createOrderRef2No
		createOrderLotteryBody.UserId = req.UserId
		createOrderLotteryBody.Amount = req.Amount
		createOrderLotteryBody.OrderCategoryId = onUseCategory
		createOrderLotteryBody.OrderTypeId = onUseType
		createOrderLotteryBody.OrderStatusId = model.ORDER_STATUS_PENDING
		orderId, err := s.repo.CreateOrderLottery(createOrderLotteryBody)
		if err != nil {
			// [Log]
			var createOrderLogBody model.CreateOrderLogRequest
			createOrderLogBody.JsonReq = fmt.Sprintln("request: ", helper.StructJson(req), "createOrderLotteryBody", helper.StructJson(createOrderLotteryBody), "error", err)
			createOrderLogBody.Remark = "[FAIL] CreateCommissionOrderLottery.CREATE_ORDER_FAIDED"
			if err := s.repo.CreateOrderLog(createOrderLogBody); err != nil {
				return nil, badRequest("LOG_FAILED")
			}
			return nil, badRequest("CREATE_ORDER_FAIDED")
		}

		// 4 [Increase User Credit]
		var userCreditReq model.UserTransactionCreateRequest
		userCreditReq.UserId = req.UserId
		userCreditReq.TypeId = model.CREDIT_TYPE_LOTTERY
		userCreditReq.Detail = model.ORDER_TYPE_LOTTERY_COMMISSION_TH
		// userCreditReq.Amount = req.Amount หรือ จะใช้ userCreditReq.BonusAmount
		userCreditReq.BonusAmount = req.Amount
		userCreditReq.RefId = &orderId
		userCreditReq.StartWorkAt = time.Now().UTC()

		agentResp, err := s.repo.IncreaseUserCredit(userCreditReq)
		if err != nil {
			// 4.1 [SET ORDER STATUS FAILED]
			var updateOrderLotteryStatusBody model.UpdateOrderLotteryStatusRequest
			updateOrderLotteryStatusBody.Id = orderId
			statusFail := model.ORDER_STATUS_FAILED
			// ทำการ set ref external เป็น failed จะ ได้ failed ซ้ำกันได้ เมื่อมี request เข้ามาใหม่
			ref1NoFail := fmt.Sprintf("FAILED%d-%s", orderId, req.Ref1No)
			updateOrderLotteryStatusBody.OrderStatusId = &statusFail
			updateOrderLotteryStatusBody.Ref1No = &ref1NoFail
			if err := s.repo.UpdateOrderLotteryStatus(updateOrderLotteryStatusBody); err != nil {
				// [Log]
				var createOrderLogBody model.CreateOrderLogRequest
				createOrderLogBody.OrderId = orderId
				createOrderLogBody.JsonReq = fmt.Sprintln("request: ", helper.StructJson(req), "userCreditReq", helper.StructJson(createOrderLotteryBody), "error", err)
				createOrderLogBody.Remark = "[FAIL] CreateCommissionOrderLottery.UPDATE_ORDER_STATUS_FAILED"
				if err := s.repo.CreateOrderLog(createOrderLogBody); err != nil {
					return nil, badRequest("LOG_FAILED")
				}
				return nil, badRequest("UPDATE_ORDER_STATUS_FAILED")
			}
			var createOrderLogBody model.CreateOrderLogRequest
			createOrderLogBody.OrderId = orderId
			createOrderLogBody.JsonReq = fmt.Sprintln("request: ", helper.StructJson(req), "userCreditReq", helper.StructJson(createOrderLotteryBody), "error", err)
			createOrderLogBody.Remark = "[FAIL] CreateCommissionOrderLottery.INCREASE_USER_CREDIT_FAILED"
			if err := s.repo.CreateOrderLog(createOrderLogBody); err != nil {
				return nil, badRequest("LOG_FAILED")
			}
			return nil, err
		}

		if agentResp.AgentSuccess {
			// 4.2 [ SET ORDER STATUS SUCCESS]
			var updateOrderLotteryStatusBody model.UpdateOrderLotteryStatusRequest
			updateOrderLotteryStatusBody.Id = orderId
			statusFail := model.ORDER_STATUS_SUCCESS
			updateOrderLotteryStatusBody.OrderStatusId = &statusFail
			if err := s.repo.UpdateOrderLotteryStatus(updateOrderLotteryStatusBody); err != nil {
				// [Log]
				var createOrderLogBody model.CreateOrderLogRequest
				createOrderLogBody.OrderId = orderId
				// createOrderLogBody.JsonReq = fmt.Sprintln("request", req, "userCreditReq", createOrderLotteryBody, "error", err)
				createOrderLogBody.JsonReq = fmt.Sprintln("request: ", helper.StructJson(req), "userCreditReq", helper.StructJson(createOrderLotteryBody), "error", err)
				createOrderLogBody.Remark = "[FAIL] CreateCommissionOrderLottery.UPDATE_ORDER_STATUS_FAILED"
				if err := s.repo.CreateOrderLog(createOrderLogBody); err != nil {
					return nil, badRequest("LOG_FAILED")
				}
				return nil, badRequest("UPDATE_ORDER_STATUS_FAILED")
			}
		}

		var response model.CreateOrderLotteryResponse
		response.Id = orderId
		response.Ref2No = createOrderRef2No
		response.Message = "SUCCESS_LOTTERY_COMMISSION"
		return &response, nil

	}

	return nil, badRequest("UNABLE_TO_CREATE_ORDER")
}

func (s *gameOrderService) GameOrderEncodeExample(req model.SetSentOrderLotteryEncodeRequest) (string, error) {
	// [Marshal JSON]
	requestData, err := json.Marshal(req)
	if err != nil {
		return "", badRequest("JSON_MARSHAL_FAILED")
	}
	// [Encode Base64]
	encodedData := base64.StdEncoding.EncodeToString(requestData)

	// [Encode Base64 with secret key]
	getEnvSecretKey := os.Getenv("SECRET_KEY_ORDER_GAME")
	encoded := base64.StdEncoding.EncodeToString([]byte(getEnvSecretKey + string(encodedData)))

	return encoded, nil
}

func (s *gameOrderService) GameOrderDecodeExample(req string) (*model.SetSentOrderLotteryEncodeRequest, error) {

	// [Decode Base64]
	decodedData, err := base64.StdEncoding.DecodeString(req)
	if err != nil {
		return nil, badRequest("DECODE_FAILED")
	}

	// [Decode Base64 with secret key]
	getEnvSecretKey := os.Getenv("SECRET_KEY_ORDER_GAME")
	decodedDataWithoutSecretKey := decodedData[len(getEnvSecretKey):]

	// [Decode JSON]
	originalData, err := base64.StdEncoding.DecodeString(string(decodedDataWithoutSecretKey))
	if err != nil {
		return nil, badRequest("DECODE_FAILED")
	}

	// [Unmarshal JSON]
	var request model.SetSentOrderLotteryEncodeRequest
	if err := json.Unmarshal(originalData, &request); err != nil {
		return nil, badRequest("JSON_UNMARSHAL_FAILED")
	}

	return &request, nil
}

func (s *gameOrderService) CreateGameOrder(req model.CreateOrderRequest) (*model.CreateOrderLotteryResponse, error) {

	// [Decode Base64]
	decodedData, err := base64.StdEncoding.DecodeString(req.Apikey)
	if err != nil {
		return nil, badRequest("DECODE_FAILED")
	}

	// [Decode Base64 with secret key]
	getEnvSecretKey := os.Getenv("SECRET_KEY_ORDER_GAME")
	decodedDataWithoutSecretKey := decodedData[len(getEnvSecretKey):]

	// [Decode JSON]
	originalData, err := base64.StdEncoding.DecodeString(string(decodedDataWithoutSecretKey))
	if err != nil {
		return nil, badRequest("DECODE_FAILED")
	}

	// [Unmarshal JSON]
	var orderBody model.OrderDecodeBody
	if err := json.Unmarshal(originalData, &orderBody); err != nil {
		return nil, badRequest("JSON_UNMARSHAL_FAILED")
	}

	// [CHECK TIME]
	if time.Now().UTC().Sub(orderBody.DateTimeNow) > 5*time.Minute {
		return nil, badRequest("OUT_OF_TIME")
	}

	lowcaseOrderCategory := strings.ToLower(orderBody.Category)
	if lowcaseOrderCategory == "lottery" {

		//switch case type
		lowcaseOrderType := strings.ToLower(orderBody.Type)
		switch lowcaseOrderType {
		case "bet":
			var createBetOrderLotteryReq model.CreateBetOrderLotteryRequest
			createBetOrderLotteryReq.Ref1No = orderBody.Ref1No
			createBetOrderLotteryReq.UserId = orderBody.UserId
			createBetOrderLotteryReq.Amount = orderBody.Amount
			return s.CreateBetOrderLottery(createBetOrderLotteryReq)
		case "win":
			var createWinOrderLotteryReq model.CreateWinOrderLotteryRequest
			createWinOrderLotteryReq.Ref1No = orderBody.Ref1No
			createWinOrderLotteryReq.UserId = orderBody.UserId
			createWinOrderLotteryReq.Amount = orderBody.Amount
			return s.CreateWinOrderLottery(createWinOrderLotteryReq)
		case "refund":
			var createRefundOrderLotteryReq model.CreateRefundOrderLotteryRequest
			createRefundOrderLotteryReq.Ref1No = orderBody.Ref1No
			createRefundOrderLotteryReq.UserId = orderBody.UserId
			createRefundOrderLotteryReq.Amount = orderBody.Amount
			return s.CreateRefundOrderLottery(createRefundOrderLotteryReq)
		case "commission":
			var createCommissionOrderLotteryReq model.CreateCommissionOrderLotteryRequest
			createCommissionOrderLotteryReq.Ref1No = orderBody.Ref1No
			createCommissionOrderLotteryReq.UserId = orderBody.UserId
			createCommissionOrderLotteryReq.Amount = orderBody.Amount
			return s.CreateCommissionOrderLottery(createCommissionOrderLotteryReq)
		default:
			return nil, badRequest("INVALID_ORDER_TYPE")
		}

	}

	return nil, badRequest("INVALID_ORDER_CATEGORY")

}
