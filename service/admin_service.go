package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"os"
	"strings"

	"github.com/golang-jwt/jwt"
	"gorm.io/gorm"
)

type AdminService interface {
	GetAdmin(id int64) (*model.AdminDetail, error)
	GetAdminList(query model.AdminListQuery) (*model.SuccessWithPagination, error)
	GetGroup(id int) (*model.AdminGroupPermissionResponse, error)
	GetGroupList(query model.AdminGroupQuery) (*model.SuccessWithPagination, error)
	LoginAdmin(data model.LoginAdmin) (*model.LoginResponse, error)
	CreateAdmin(user model.CreateAdmin) error
	CreateGroup(data *model.AdminCreateGroup) error
	UpdateAdmin(adminId int64, data model.AdminUpdateRequest) (error, []string)
	UpdateGroup(req model.AdminUpdateGroup) error
	ResetPassword(adminId int64, body model.AdminUpdatePassword) error
	DeleteGroup(req model.DeleteGroup) error
	DeletePermission(perm model.DeletePermission) error
	DeleteAdmin(model.AdminDeleteRequest) error

	GetAdminLoginLogList(query model.AdminLoginLogListRequest) ([]model.AdminLoginLogResponse, int64, error)

	TotpGererateSecret(req model.TotpGererateSecret) (*model.TotpGererateSecretResponse, error)
	TotpVerifyCheck(req model.TotpVerifyCheck) (*model.TotpVerifyResponse, error)
	CheckCurrentAdminId(input any) (*int64, error)
	TotpReset(req model.TotpResetRequest) error
	GetAdminDetailTotp() (*model.GetAdminDetailTotp, error)

	AdminRefreshToken(req model.AdminRefreshTokenRequest) (*model.AdminRefreshToken, error)
	CreateFailedAdminAction(req model.AdminActionCreateRequest) (*int64, error)

	GetAdminOptionList() ([]model.GetAdminOptionList, error)
}

type adminService struct {
	repo        repository.AdminRepository
	perRepo     repository.PermissionRepository
	groupRepo   repository.GroupRepository
	adminAction AdminActionService
}

func NewAdminService(
	repo repository.AdminRepository,
	perRepo repository.PermissionRepository,
	groupRepo repository.GroupRepository,
	adminAction AdminActionService,
) AdminService {
	return &adminService{repo, perRepo, groupRepo, adminAction}
}

func (s *adminService) GetAdmin(id int64) (*model.AdminDetail, error) {

	admin, perList, group, totp, err := s.repo.GetAdmin(id)
	if err != nil {

		if err.Error() == recordNotFound {
			return nil, notFound(adminNotFound)
		}

		return nil, err
	}

	webSetting, err := s.repo.GetLoginAdminWebSetting(admin.Id)
	if err != nil {
		if err.Error() == recordNotFound {
			log.Println("GetLoginAdminWebSetting", err)
		}
		// return nil, err
		webSetting.TokenExpiredMinute = 1440
	}

	var result model.AdminDetail
	result.Id = admin.Id
	result.Username = admin.Username
	result.Fullname = admin.Fullname
	result.Phone = admin.Phone
	result.Email = admin.Email
	result.Status = admin.Status
	result.Role = admin.Role
	result.PermissionList = perList
	result.IsVerifyTotp = admin.IsVerifyTotp
	if totp != nil {
		result.WebSettingTotp = totp.IsTotpVerify
	}
	if group != nil {
		result.Group = group
	}
	result.TokenExpiredMinute = webSetting.TokenExpiredMinute

	return &result, nil
}

func (s *adminService) GetAdminDetailTotp() (*model.GetAdminDetailTotp, error) {

	totp, err := s.repo.GetAdminTotpSetting()
	if err != nil {

		if err.Error() == recordNotFound {
			return nil, notFound(adminNotFound)
		}

		return nil, err
	}
	var result model.GetAdminDetailTotp
	result.WebSettingTotp = totp.IsTotpVerify

	return &result, nil
}

func (s *adminService) GetAdminList(query model.AdminListQuery) (*model.SuccessWithPagination, error) {

	if err := helper.Pagination(&query.Page, &query.Limit); err != nil {
		return nil, err
	}

	list, total, err := s.repo.GetAdminList(query)
	if err != nil {
		return nil, err
	}

	result := &model.SuccessWithPagination{
		Message: "Success",
		List:    list,
		Total:   *total,
	}

	return result, nil
}

func (s *adminService) GetGroup(id int) (*model.AdminGroupPermissionResponse, error) {

	group, err := s.repo.GetGroup(id)
	if err != nil {

		if err.Error() == recordNotFound {
			return nil, notFound(adminGroupNotFound)
		}

		return nil, err
	}

	return group, nil
}

func (s *adminService) GetGroupList(query model.AdminGroupQuery) (*model.SuccessWithPagination, error) {

	if err := helper.Pagination(&query.Page, &query.Limit); err != nil {
		return nil, err
	}

	list, total, err := s.repo.GetGroupList(query)
	if err != nil {
		return nil, err
	}

	result := &model.SuccessWithPagination{
		Message: "Success",
		List:    list,
		Total:   *total,
	}

	return result, nil
}

func (s *adminService) LoginAdmin(data model.LoginAdmin) (*model.LoginResponse, error) {

	var response model.LoginResponse
	admin, err := s.repo.GetAdminByUsername(data.Username)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(adminLoginFailed)
		}
		return nil, internalServerError(err)
	}
	if admin == nil {
		return nil, badRequest(adminLoginFailed)
	}

	// CLOSE
	webInfo, err := s.repo.GetWebInfo()
	if err != nil {
		log.Println("GetWebInfo", err)
		return nil, internalServerError(errors.New("CAN_NOT_GET_WEB_INFO"))
	}
	if !webInfo.IsBackEnabled {
		log.Println("BACKOFFICE_IS_DISABLED", helper.StructJson(webInfo))
		return nil, badRequest("BACKOFFICE_IS_DISABLED")
	}

	// AUTO set password for first login for Admin only
	if admin.Password == "" && data.Password != "" && admin.Id == 1 {
		// upadate password
		var resetPassBody model.AdminUpdatePassword
		if newPasword, err := helper.GenAdminPassword(data.Password); err != nil {
			log.Println("GenAdminPassword", err)
			return nil, internalServerError(errors.New("CAN_NOT_GEN_PASSWORD"))
		} else {
			resetPassBody.Password = newPasword
		}
		if err := s.repo.UpdatePassword(admin.Id, resetPassBody); err != nil {
			log.Println("ResetAdminPassword", err)
			return nil, internalServerError(errors.New("CAN_NOT_RESET_PASSWORD"))
		}
		// reget
		if admin2, err := s.repo.GetAdminByUsername(data.Username); err != nil {
			log.Println("GetAdminByUsername", err)
			if err.Error() == "record not found" {
				return nil, notFound("RECORD_NOT_FOUND")
			}
			return nil, internalServerError(err)
		} else {
			admin = admin2
		}
	}

	if admin.Status != "ACTIVE" {
		return nil, badRequest(adminDeactive)
	}

	if err := helper.CompareAdminPassword(data.Password, admin.Password); err != nil {
		return nil, badRequest(adminLoginFailed)
	}

	// ปล่อยให้ log captcha ทำงานไปก่อน
	webSetting, err := s.repo.GetLoginAdminWebSetting(admin.Id)
	if err != nil {
		if err.Error() == recordNotFound {
			log.Println("GetLoginAdminWebSetting", err)
		}
		return nil, err
	}
	if webSetting.IsTotpVerify {
		response.Id = &admin.Id
		response.IsVerifyTotp = &admin.IsVerifyTotp
		response.WebSettingTotp = &webSetting.IsTotpVerify
		response.Token = ""
		return &response, nil
	}

	// [ADMIN_ACTION] SUCCESS "Login"
	var adminActionCreateBody model.AdminActionCreateRequest
	adminActionCreateBody.AdminId = admin.Id
	adminActionCreateBody.TypeId = model.ADMIN_ACTION_LOGIN
	adminActionCreateBody.Detail = "Login"
	// Build JsonInput
	var inputBody model.LoginAdminBody
	inputBody.Username = data.Username
	inputBody.Password = "*******"
	inputBody.CaptchaId = data.CaptchaId
	inputBody.CaptchaValue = data.CaptchaValue
	inputBody.IpAddress = data.IpAddress
	inputBody.Browser = data.Browser
	inputBody.Device = data.Device
	inputBody.Agent = data.Agent
	adminActionCreateBody.JsonInput = helper.StructJson(inputBody)
	adminActionCreateBody.JsonOutput = helper.StructJson(admin.Username)
	if _, err := s.adminAction.CreateSuccessAdminAction(adminActionCreateBody); err != nil {
		return nil, internalServerError(err)
	}

	token, err := helper.CreateJWTAdmin(*admin, webSetting.TokenExpiredMinute)
	if err != nil {
		return nil, internalServerError(err)
	}

	refreshToken, err := helper.CreateJWTRefreshTokenAdmin(*admin)
	if err != nil {
		return nil, internalServerError(err)
	}

	response.RefreshToken = refreshToken
	response.Token = token
	// Single Session
	ssMode := os.Getenv("SINGLE_SESSION")
	if strings.Contains(ssMode, "admin") {
		if err := s.repo.SetAdminSingleSession(admin.Id, token); err != nil {
			return nil, internalServerError(err)
		}
	}

	return &response, nil
}

func (s *adminService) CreateAdmin(data model.CreateAdmin) error {

	username, err := s.repo.CheckAdmin(data.Username)
	if err != nil {
		return err
	}

	if username {
		return badRequest(adminExist)
	}

	if data.Phone != "" && !helper.CheckPhone(data.Phone) {
		return badRequest(adminPhoneInvalid)
	}

	if data.Email != "" && !helper.CheckEmail(data.Email) {
		return badRequest(adminEmailInvalid)
	}

	checkGroup, err := s.groupRepo.CheckGroupExist(data.AdminGroupId)
	if err != nil {
		return internalServerError(err)
	}

	if !checkGroup {
		return badRequest(adminGroupNotFound)
	}

	hashedPassword, err := helper.GenAdminPassword(data.Password)
	if err != nil {
		return internalServerError(err)
	}

	newUser := model.Admin{}
	newUser.Email = data.Email
	newUser.Username = data.Username
	newUser.Fullname = data.Fullname
	newUser.Password = string(hashedPassword)
	newUser.Role = "ADMIN"
	newUser.Status = data.Status
	newUser.Phone = data.Phone
	newUser.AdminGroupId = data.AdminGroupId

	splitFullname := strings.Split(data.Fullname, " ")
	var firstname, lastname *string
	if len(splitFullname) == 2 {
		firstname = &splitFullname[0]
		lastname = &splitFullname[1]
		newUser.Firstname = *firstname
		newUser.Lastname = *lastname
	}

	if len(splitFullname) == 3 {
		firstname = &splitFullname[1]
		lastname = &splitFullname[2]
		newUser.Firstname = *firstname
		newUser.Lastname = *lastname
	}

	createAdminErr := s.repo.CreateAdmin(newUser)

	// [ADMIN_ACTION] SUCCESS เพิ่มผู้ใช้งาน {name}
	var adminActionCreateBody model.AdminActionCreateRequest
	adminActionCreateBody.AdminId = data.CreateBy
	adminActionCreateBody.TypeId = model.ADMIN_ACTION_MANAGE
	adminActionCreateBody.Detail = fmt.Sprintf("เพิ่มผู้ใช้งาน %s", data.Username)
	adminActionCreateBody.JsonInput = helper.StructJson(data)
	if _, err := s.adminAction.CreateSuccessAdminAction(adminActionCreateBody); err != nil {
		return err
	}

	return createAdminErr
}

func (s *adminService) CreateGroup(data *model.AdminCreateGroup) error {

	checkGroup, err := s.groupRepo.CheckGroupExist(data.GroupId)
	if err != nil {
		return internalServerError(err)
	}

	if !checkGroup {
		return badRequest(adminGroupNotFound)
	}

	checkPermission, err := s.perRepo.CheckPerListExist(data.PermissionIds)
	if err != nil {
		return internalServerError(err)
	}

	var idNotFound []string
	for _, j := range data.PermissionIds {

		exist := false

		for _, k := range checkPermission {
			if j == k {
				exist = true
			}
		}

		if !exist {
			idNotFound = append(idNotFound, fmt.Sprintf("%d", j))
		}
	}

	if len(idNotFound) > 0 {
		return badRequest(fmt.Sprintf("Permission id %s not found", strings.Join(idNotFound, ",")))
	}

	var list []model.AdminPermissionList

	for _, id := range data.PermissionIds {
		list = append(list, model.AdminPermissionList{
			GroupId:      data.GroupId,
			PermissionId: id,
		})
	}

	if err := s.repo.CreateGroupAdmin(list); err != nil {
		return err
	}

	return nil
}

func (s *adminService) UpdateAdmin(adminId int64, body model.AdminUpdateRequest) (error, []string) {

	var data model.UpdateAdmin

	if adminId == 1 {
		// ไม่สามารถแก้ไขข้อมูลผู้ใช้งานนี้ได้
		if _, err := s.repo.CreateSystemLog(model.SystemLogCreateBody{
			Name:   "UPDATE_SUPER_ADMIN",
			Status: "HACKING",
			JsonRequest: helper.StructJson(map[string]interface{}{
				"adminId": adminId,
				"body":    body,
			}),
		}); err != nil {
			log.Println("UpdateAdmin.CreateSystemLog", err)
		}
		return badRequest(accessDenied), nil
	}

	adminData, err := s.repo.GetAdminById(adminId)
	if err != nil {
		return internalServerError(err), nil
	}

	if body.AdminGroupId != nil {
		checkGroup, err := s.groupRepo.CheckGroupExist(*body.AdminGroupId)
		if err != nil {
			return internalServerError(err), nil
		}

		if !checkGroup {
			return notFound(adminGroupNotFound), nil
		}

		data.AdminGroupId = body.AdminGroupId
	}

	var oldGroupId *int64

	if body.AdminGroupId != nil {

		getGroupId, err := s.repo.GetAdminGroup(adminId)
		if err != nil {
			return internalServerError(err), nil
		}

		oldGroupId = &getGroupId.AdminGroupId
	}

	if body.Phone != "" && !helper.CheckPhone(body.Phone) {
		return badRequest(adminPhoneInvalid), nil
	}

	if body.Email != "" && !helper.CheckEmail(body.Email) {
		return badRequest(adminEmailInvalid), nil
	}

	data.Phone = body.Phone
	data.Email = &body.Email
	data.Status = body.Status
	data.Fullname = body.Fullname

	splitFullname := strings.Split(body.Fullname, " ")
	if len(splitFullname) == 2 {
		data.Firstname = splitFullname[0]
		data.Lastname = splitFullname[1]
	}

	if len(splitFullname) == 3 {
		data.Firstname = splitFullname[1]
		data.Lastname = splitFullname[2]
	}

	result := s.repo.UpdateAdmin(adminId, oldGroupId, data)

	// [ADMIN_ACTION] SUCCESS แก้ไขข้อมูลผู้ใช้งาน {name}
	var adminActionCreateBody model.AdminActionCreateRequest
	adminActionCreateBody.AdminId = body.UpdateBy
	adminActionCreateBody.TypeId = model.ADMIN_ACTION_MANAGE
	adminActionCreateBody.Detail = fmt.Sprintf("แก้ไขข้อมูลผู้ใช้งาน %s", adminData.Username)
	adminActionCreateBody.JsonInput = helper.StructJson(body)
	adminActionCreateBody.JsonOutput = helper.StructJson(data)
	if _, err := s.adminAction.CreateSuccessAdminAction(adminActionCreateBody); err != nil {
		return err, nil
	}

	return result, nil
}

func (s *adminService) UpdateGroup(req model.AdminUpdateGroup) error {

	checkGroup, err := s.groupRepo.CheckGroupExist(req.GroupId)
	if err != nil {
		return internalServerError(err)
	}
	if !checkGroup {
		return badRequest(adminGroupNotFound)
	}

	checkPermission, err := s.perRepo.CheckPerListExist(req.PermissionIds)
	if err != nil {
		return internalServerError(err)
	}

	var idNotFound []string
	for _, j := range req.PermissionIds {

		exist := false

		for _, k := range checkPermission {
			if j == k {
				exist = true
			}
		}

		if !exist {
			idNotFound = append(idNotFound, fmt.Sprintf("%d", j))
		}
	}

	if len(idNotFound) > 0 {
		return badRequest(fmt.Sprintf("ไม่พบ Permission id %s", strings.Join(idNotFound, ",")))
	}

	var list []model.AdminPermissionList

	for _, id := range req.PermissionIds {
		list = append(list, model.AdminPermissionList{
			GroupId:      req.GroupId,
			PermissionId: id,
		})
	}

	if err := s.repo.UpdateGroup(req.GroupId, req.Name, list); err != nil {
		return err
	}

	// [ADMIN_ACTION] SUCCESS แก้ไขข้อมูลกลุ่มผู้ใช้งาน ชื่อกลุ่ม {name}
	var adminActionCreateBody model.AdminActionCreateRequest
	adminActionCreateBody.AdminId = req.UpdateBy
	adminActionCreateBody.TypeId = model.ADMIN_ACTION_GROUP_MANAGE
	adminActionCreateBody.Detail = fmt.Sprintf("แก้ไขข้อมูลกลุ่มผู้ใช้งาน %s", req.Name)
	adminActionCreateBody.JsonInput = helper.StructJson(req)
	if _, err := s.adminAction.CreateSuccessAdminAction(adminActionCreateBody); err != nil {
		return nil
	}

	return nil
}

func (s *adminService) ResetPassword(adminId int64, body model.AdminUpdatePassword) error {

	adminData, err := s.repo.GetAdminById(adminId)
	if err != nil {
		if err.Error() == recordNotFound {
			return notFound(adminNotFound)
		}
		return internalServerError(err)
	}

	newPasword, err := helper.GenAdminPassword(body.Password)
	if err != nil {
		return internalServerError(err)
	}
	body.Password = newPasword

	if err := s.repo.UpdatePassword(adminId, body); err != nil {
		return err
	}

	// [ADMIN_ACTION] SUCCESS แก้ไขข้อมูลรหัสผ่านของผู้ใช้งาน {name}
	var adminActionCreateBody model.AdminActionCreateRequest
	adminActionCreateBody.AdminId = body.UpdateBy
	adminActionCreateBody.TypeId = model.ADMIN_ACTION_MANAGE
	adminActionCreateBody.Detail = fmt.Sprintf("แก้ไขข้อมูลรหัสผ่านของผู้ใช้งาน %s", adminData.Username)
	adminActionCreateBody.JsonInput = helper.StructJson(body)
	if _, err := s.adminAction.CreateSuccessAdminAction(adminActionCreateBody); err != nil {
		return nil
	}

	return nil
}

func (s *adminService) DeleteGroup(req model.DeleteGroup) error {

	group, err := s.groupRepo.GetAdminGroupById(req.Id)
	if err != nil {
		if err.Error() == recordNotFound {
			return notFound(adminGroupNotFound)
		}
		return err
	}

	if group.AdminCount > 0 {
		return badRequest("ยังมีผู้ใช้งานอยู่ในกลุ่มนี้ กรุณาย้ายกลุ่มผู้ใช้งานออกก่อน")
	}

	if err := s.groupRepo.DeleteGroup(req.Id); err != nil {
		return err
	}

	// [ADMIN_ACTION] SUCCESS ลบข้อมูลกลุ่มผู้ใช้งาน ชื่อกลุ่ม {name}
	var adminActionCreateBody model.AdminActionCreateRequest
	adminActionCreateBody.AdminId = req.DeleteBy
	adminActionCreateBody.TypeId = model.ADMIN_ACTION_GROUP_MANAGE
	adminActionCreateBody.Detail = fmt.Sprintf("ลบข้อมูลกลุ่มผู้ใช้งาน %s", group.Name)
	adminActionCreateBody.JsonInput = helper.StructJson(req)
	if _, err := s.adminAction.CreateSuccessAdminAction(adminActionCreateBody); err != nil {
		return nil
	}

	return nil
}

func (s *adminService) DeletePermission(perm model.DeletePermission) error {

	if err := s.perRepo.DeletePermission(perm); err != nil {
		return err
	}

	return nil
}

func (s *adminService) DeleteAdmin(req model.AdminDeleteRequest) error {

	if req.AdminId == 1 {
		// ไม่สามารถแก้ไขข้อมูลผู้ใช้งานนี้ได้
		if _, err := s.repo.CreateSystemLog(model.SystemLogCreateBody{
			Name:   "DELETE_SUPER_ADMIN",
			Status: "HACKING",
			JsonRequest: helper.StructJson(map[string]interface{}{
				"req": req,
			}),
		}); err != nil {
			log.Println("DeleteAdmin.CreateSystemLog", err)
		}
		return badRequest(accessDenied)
	}

	adminData, err := s.repo.GetAdminById(req.AdminId)
	if err != nil {
		if err.Error() == recordNotFound {
			return notFound(adminNotFound)
		}
		return internalServerError(err)
	}

	if err := s.repo.DeleteAdmin(req.AdminId); err != nil {
		return err
	}

	// [ADMIN_ACTION] SUCCESS ลบข้อมูลผู้ใช้งาน {name}
	var adminActionCreateBody model.AdminActionCreateRequest
	adminActionCreateBody.AdminId = req.UpdateBy
	adminActionCreateBody.TypeId = model.ADMIN_ACTION_MANAGE
	adminActionCreateBody.Detail = fmt.Sprintf("ลบข้อมูลผู้ใช้งาน %s", adminData.Username)
	adminActionCreateBody.JsonInput = helper.StructJson(req)
	if _, err := s.adminAction.CreateSuccessAdminAction(adminActionCreateBody); err != nil {
		return nil
	}

	return nil
}

func (s *adminService) GetAdminLoginLogList(query model.AdminLoginLogListRequest) ([]model.AdminLoginLogResponse, int64, error) {

	if err := helper.UnlimitPagination(&query.Page, &query.Limit); err != nil {
		return nil, 0, err
	}

	list, total, err := s.repo.GetAdminLoginLogList(query)
	if err != nil {
		return nil, 0, err
	}

	// build response from json_input
	var result []model.AdminLoginLogResponse
	for _, item := range list {
		newRow := model.AdminLoginLogResponse{}
		newRow.Id = item.Id
		newRow.CreateAt = item.CreatedAt

		var output model.LoginAdminBody
		if err := json.Unmarshal([]byte(item.JsonInput), &output); err == nil {
			newRow.Username = output.Username
			newRow.Browser = output.Browser
			newRow.Device = output.Device
			newRow.IpAddress = output.IpAddress
		}
		result = append(result, newRow)
	}

	return result, total, nil
}

func (s *adminService) TotpGererateSecret(req model.TotpGererateSecret) (*model.TotpGererateSecretResponse, error) {

	response, err := s.repo.TotpGererateSecret(req)
	if err != nil {
		return nil, err
	}

	return response, nil
}

func (s *adminService) TotpVerifyCheck(req model.TotpVerifyCheck) (*model.TotpVerifyResponse, error) {

	verify, err := s.repo.TotpVerifyCheck(req)
	if err != nil {
		return nil, err
	}
	var response model.TotpVerifyResponse
	response.TotpVerify = verify

	if verify {
		admin, err := s.repo.GetAdminById(req.AdminId)
		if err != nil {
			return nil, internalServerError(err)
		}

		// [ADMIN_ACTION] SUCCESS "Login"
		var adminActionCreateBody model.AdminActionCreateRequest
		adminActionCreateBody.AdminId = admin.Id
		adminActionCreateBody.TypeId = model.ADMIN_ACTION_LOGIN
		adminActionCreateBody.Detail = "Login"
		adminActionCreateBody.JsonOutput = helper.StructJson(admin.Username)
		if _, err := s.adminAction.CreateSuccessAdminAction(adminActionCreateBody); err != nil {
			return nil, internalServerError(err)
		}

		webSetting, err := s.repo.GetLoginAdminWebSetting(admin.Id)
		if err != nil {
			if err.Error() == recordNotFound {
				log.Println("GetLoginAdminWebSetting", err)
			}
			return nil, err
		}

		token, err := helper.CreateJWTAdmin(*admin, webSetting.TokenExpiredMinute)
		if err != nil {
			return nil, internalServerError(err)
		}
		response.Token = token
		refreshToken, err := helper.CreateJWTRefreshTokenAdmin(*admin)
		if err != nil {
			return nil, internalServerError(err)
		}
		response.RefreshToken = refreshToken

		// Single Session
		ssMode := os.Getenv("SINGLE_SESSION")
		if strings.Contains(ssMode, "admin") {
			if err := s.repo.SetAdminSingleSession(admin.Id, token); err != nil {
				return nil, internalServerError(err)
			}
		}
	}

	return &response, nil
}

func (s *adminService) CheckCurrentAdminId(input any) (*int64, error) {

	// input := c.MustGet("adminId")
	if input == nil {
		return nil, badRequest(invalidCurrentAdminId)
	}
	var adminId = int64(input.(float64))
	if adminId <= 0 {
		return nil, badRequest(invalidCurrentAdminId)
	}
	return &adminId, nil
}

func (s *adminService) TotpReset(req model.TotpResetRequest) error {

	// if req.AdminId == req.UpdatedById {
	// 	return badRequest("ไม่สามารถ Reset 2FA ของตัวเองได้")
	// }

	if err := s.repo.TotpReset(req.AdminId); err != nil {
		return err
	}

	// get admin data
	adminData, err := s.repo.GetAdminById(req.AdminId)
	if err != nil {
		return err
	}

	// [ADMIN_ACTION] SUCCESS {name} อัพเดท การตั้งค่าจำกัดการถอนสูงสุด
	var adminActionCreateBody model.AdminActionCreateRequest
	adminActionCreateBody.AdminId = req.UpdatedById
	adminActionCreateBody.TypeId = model.ADMIN_ACTION_ACCOUNT_MANAGE
	adminActionCreateBody.Detail = fmt.Sprintf("Reset 2FA ของผู้ใช้งาน %s", adminData.Username)
	adminActionCreateBody.JsonInput = helper.StructJson(req)
	if _, err := s.adminAction.CreateSuccessAdminAction(adminActionCreateBody); err != nil {
		return nil
	}

	return nil
}

func (s *adminService) AdminRefreshToken(req model.AdminRefreshTokenRequest) (*model.AdminRefreshToken, error) {

	// admin, err := s.repo.GetAdminById(req.AdminId)
	// if err != nil {
	// 	return nil, err
	// }

	salt := "_REFRESH_TOKEN_ADMIN"
	claims, err := jwt.ParseWithClaims(req.RefreshToken, jwt.MapClaims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(os.Getenv("JWT_SECRET_ADMIN") + salt), nil
	})
	if err != nil {
		log.Println("ERROR.AdminRefreshToken.ParseWithClaims", err)
		return nil, unauthorized("UNAUTHORIZED")
	}

	if claims.Claims.(jwt.MapClaims)["adminId"] == nil {
		return nil, unauthorized("UNAUTHORIZED")
	}
	adminId := claims.Claims.(jwt.MapClaims)["adminId"].(float64)

	adminDetail, err := s.repo.GetAdminById(helper.ConvertIdAnyToInt64(adminId))
	if err != nil {
		if err.Error() == gorm.ErrRecordNotFound.Error() {
			return nil, notFound("ADMIN_NOT_FOUND")
		}
		return nil, internalServerError(err)
	}
	if adminDetail == nil {
		return nil, notFound("ADMIN_NOT_FOUND")
	}

	webSetting, err := s.repo.GetLoginAdminWebSetting(adminDetail.Id)
	if err != nil {
		if err.Error() == recordNotFound {
			log.Println("GetLoginAdminWebSetting", err)
		}
		return nil, err
	}

	token, err := helper.CreateJWTAdmin(*adminDetail, webSetting.TokenExpiredMinute)
	if err != nil {
		return nil, internalServerError(err)
	}

	refreshToken, err := helper.CreateJWTRefreshTokenAdmin(*adminDetail)
	if err != nil {
		return nil, internalServerError(err)
	}

	// Single Session
	ssMode := os.Getenv("SINGLE_SESSION")
	if strings.Contains(ssMode, "admin") {
		if err := s.repo.SetAdminSingleSession(adminDetail.Id, token); err != nil {
			return nil, internalServerError(err)
		}
	}

	var response model.AdminRefreshToken
	response.Token = token
	response.RefreshToken = refreshToken
	return &response, nil
}

func (s *adminService) CreateFailedAdminAction(req model.AdminActionCreateRequest) (*int64, error) {

	var createBody model.AdminActionCreateBody
	createBody.AdminId = req.AdminId
	createBody.TypeId = req.TypeId
	createBody.RefObjectId = req.RefObjectId
	createBody.Detail = req.Detail
	createBody.JsonInput = req.JsonInput
	createBody.JsonOutput = req.JsonOutput
	insertId, err := s.repo.CreateAdminAction(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}
	return insertId, nil
}

func (s *adminService) GetAdminOptionList() ([]model.GetAdminOptionList, error) {

	list, err := s.repo.GetAdminOptionList()
	if err != nil {
		return nil, err
	}
	return list, nil
}
