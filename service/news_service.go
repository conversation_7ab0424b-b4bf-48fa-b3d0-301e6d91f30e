package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"fmt"
	"log"
	"os"
	"strings"
	"time"
)

type NewsService interface {
	NewsGetList(query model.NewsQuery) (*model.NewsListResponse, error)
	NewsGetHighlightList() (*model.NewsListResponse, error)
	NewsDetail(newsId int64) (*model.NewsDetail, error)
	NewsCreate(news model.NewsBody) error
	NewsUpdate(newsId int64, body model.NewsUpdateBody) error
	NewsUpdateSortOrder(list model.NewsSortBody) error
	NewsDelete(newsId int64) error
}

type NewsServiceRepos struct {
	repo      repository.NewsRepository
	FileRepo  repository.FileRepository
	CacheRepo repository.CacheRepository
}

func NewNewsService(
	repo repository.NewsRepository,
	FileRepo repository.FileRepository,
	CacheRepo repository.CacheRepository,
) NewsService {
	return &NewsServiceRepos{repo, FileRepo, CacheRepo}
}

func (s NewsServiceRepos) NewsGetList(query model.NewsQuery) (*model.NewsListResponse, error) {

	if err := helper.Pagination(&query.Page, &query.Limit); err != nil {
		return nil, err
	}

	response := &model.NewsListResponse{}

	// if query.Page == 0 && query.Limit == 3 {
	// 	if s.CacheRepo.Get("news_list-3", &response) {
	// 		return response, nil
	// 	}
	// }

	// if query.Page == 0 && query.Limit == 6 {
	// 	if s.CacheRepo.Get("news_list-6", &response) {
	// 		return response, nil
	// 	}
	// }

	newsList, total, err := s.repo.GetNewsList(query)
	if err != nil {

		if err.Error() == recordNotFound {
			return nil, notFound(newsNotFound)
		}

		return nil, internalServerError(err)
	}

	response.List = newsList
	response.Total = total

	// if query.Page == 0 && query.Limit == 3 {
	// 	s.CacheRepo.Set("news_list-3", response, 0)
	// }

	// if query.Page == 0 && query.Limit == 6 {
	// 	s.CacheRepo.Set("news_list-6", response, 0)
	// }

	return response, nil
}

func (s NewsServiceRepos) NewsGetHighlightList() (*model.NewsListResponse, error) {

	response := &model.NewsListResponse{}

	// if s.CacheRepo.Get("news_highlight_list", &response) {
	// 	return response, nil
	// }

	newsList, err := s.repo.GetNewsHighLightList()
	if err != nil {

		if err.Error() == recordNotFound {
			return nil, notFound(newsNotFound)
		}

		return nil, internalServerError(err)
	}

	response.List = newsList
	response.Total = len(newsList)

	// s.CacheRepo.Set("news_highlight_list", response, 0)

	return response, nil
}

func (s NewsServiceRepos) NewsDetail(newsId int64) (*model.NewsDetail, error) {

	news, err := s.repo.GetNewsById(newsId)
	if err != nil {

		if err.Error() == recordNotFound {
			return nil, notFound(newsNotFound)
		}

		return nil, internalServerError(err)
	}

	return news, nil
}

func (s NewsServiceRepos) NewsCreate(body model.NewsBody) error {

	if body.CreatedBy == 0 {
		return badRequest(newsCreatedByNotZero)
	}

	if body.CreatedAt == "" {
		body.CreatedAt = time.Now().Format("2006-01-02 15:04")
	}

	if _, err := time.Parse("2006-01-02 15:04", body.CreatedAt); err != nil {
		return badRequest(newsDateFormatIsIncorrect)
	}

	if body.IsHighlight {

		count, err := s.repo.CountNewsHightlight(nil)
		if err != nil {
			return internalServerError(err)
		}

		if count >= 3 {
			return badRequest(newsNotOverThree)
		}
	}

	if err := s.repo.CreateNews(body); err != nil {
		return internalServerError(err)
	}

	s.CacheRepo.Delete("news_list")
	s.CacheRepo.Delete("news_highlight_list")

	return nil
}

func (s NewsServiceRepos) NewsUpdate(newsId int64, body model.NewsUpdateBody) error {

	if body.IsHighlight {

		count, err := s.repo.CountNewsHightlight(&newsId)
		if err != nil {
			return internalServerError(err)
		}

		if count >= 3 {
			return badRequest(newsNotOverThree)
		}
	}

	getCoverUrl, err := s.repo.GetCoverUrlByNewsId(newsId)
	if err != nil {
		return err
	}

	if getCoverUrl != "" && getCoverUrl != body.CoverUrl {
		s.deleteCoverUrl(getCoverUrl, newsId)
	}

	if err := s.repo.UpdateNews(newsId, body); err != nil {
		return internalServerError(err)
	}

	s.CacheRepo.Delete("news_list")
	s.CacheRepo.Delete("news_highlight_list")

	return nil
}

func (s NewsServiceRepos) NewsDelete(newsId int64) error {

	getCoverUrl, err := s.repo.GetCoverUrlByNewsId(newsId)
	if err != nil {
		return err
	}

	s.deleteCoverUrl(getCoverUrl, newsId)

	if err := s.repo.DeleteNews(newsId); err != nil {
		return internalServerError(err)
	}

	s.CacheRepo.Delete("news_list")
	s.CacheRepo.Delete("news_highlight_list")

	return nil
}

func (s NewsServiceRepos) NewsUpdateSortOrder(list model.NewsSortBody) error {

	if len(list.List) == 0 {
		return badRequest(newsListIsRequire)
	}

	for _, item := range list.List {
		if item.SortOrder == 0 {
			return badRequest(newsSortOrderNotZero)
		}
	}

	if err := s.repo.UpdateNewsSortOrder(list); err != nil {
		return internalServerError(err)
	}

	s.CacheRepo.Delete("news_list")
	s.CacheRepo.Delete("news_highlight_list")

	return nil
}

func (s NewsServiceRepos) deleteCoverUrl(oldCoverUrl string, newsId int64) {

	bucketName := os.Getenv("BUCKET_NAME")

	if oldCoverUrl != "" && helper.FindString("https://storage.googleapis.com", oldCoverUrl) {

		url := fmt.Sprintf("https://storage.googleapis.com/%s/", bucketName)

		slicePath := strings.Split(oldCoverUrl, url)
		slicePath[1] = strings.Replace(slicePath[1], "%2F", "/", -1)
		slicePath[1] = strings.Replace(slicePath[1], "%20", " ", -1)
		if err := s.FileRepo.DeleteFile(slicePath[1]); err != nil {
			log.Println("delete file error : ", oldCoverUrl)
			return
		}
	}
}
