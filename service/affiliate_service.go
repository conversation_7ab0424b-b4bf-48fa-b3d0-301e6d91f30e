package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"math"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm"
)

// var cacheAffiliateUserSummary *model.AffiliateUserSummaryResponse

type AffiliateService interface {
	Summary(userId int64) (*model.AfSummary, error)
	GetCommissionSetting() (*model.AfCommissionResponse, error)
	// GetCommissionByUserId(userId int64) (*model.AfCommissionForUserResponse, error)
	GetActiveAffMemberByLevel(req model.AffMemberListRequest) (*model.SuccessWithPagination, error)
	GetIncome(userId int64, req model.AffMemberListRequest) (*model.SuccessWithPagination, error)
	GetUserIncomeWebLogList(req model.UserIncomeWebLogListRequest) (*model.UserIncomeWebLogListResponse, error)
	GetUserIncomeCompletedLogList(query model.UserIncomeCompletedLogListRequest) ([]model.UserIncomeCompletedLogResponse, int64, error)
	GetAfRegisterBonusType() (*model.AfRegisterBonusTypeResponse, error)
	GetAfRegisterBonusOption() (*model.AfRegisterBonusOptionResponse, error)
	GetAfReport(query model.AfReportQuery) (*model.AfReportResponse, error)
	UpdateCommission(data model.AfCommissionUpdateRequest) error
	WithdrawCommission(userId int64) error
	UpdateMemberFirstDeposit(member model.Member, amount float64) error
	UpdateCommissionFirstDeposit(member model.Member, amount float64) error
	// User-Related
	GetUserAffiliateMemberList(query model.AffiliateMemberListRequest) ([]model.AffiliateMemberResponse, int64, error)
	// Report
	GetAffiliateUserList(query model.AffiliateUserListRequest) ([]model.AffiliateUserResponse, int64, error)
	GetAffiliateUserSummary(req model.AffiliateUserSummaryRequest) (*model.AffiliateUserSummaryResponse, error)
	GetAffiliateUserSummaryRealTime(req model.AffiliateUserSummaryRequest) (*model.AffiliateUserSummaryResponse, error)
	GetAffiliateDepositPlayList(query model.AffiliateDepositPlayListRequest) ([]model.AffiliateUserDepositPlayResponse, int64, error)
	GetAffiliateDepositPlayCountLevel(query model.AffiliateDepositPlayListRequest) (*model.AffiliateUserDownlineCountResponse, error)
	// DUMP-REPORT
	MakeReportAffiliateUserList(query model.AffiliateUserListRequest) error
	// test
	GetAffTransactionListSummary(req model.AffTransactionListRequest) (*model.AffTransactionSummaryResponse, error)
	// Web
	GetWebAffiliateCommissionDetail() (*model.GetWebAffiliateCommissionDetailResponse, error)
}

type AffiliateServiceRepos struct {
	shareDb     *gorm.DB
	repo        repository.AffiliateRepository
	agRepo      repository.AgentConnectRepository
	serviceNoti NotificationService
}

func NewAffiliateService(
	shareDb *gorm.DB,
	repo repository.AffiliateRepository,
	agRepo repository.AgentConnectRepository,
	serviceNoti NotificationService,
) AffiliateService {
	return &AffiliateServiceRepos{shareDb, repo, agRepo, serviceNoti}
}

func (s AffiliateServiceRepos) Summary(userId int64) (*model.AfSummary, error) {

	var result model.AfSummary

	user, err := s.repo.GetUserForGenmemberByUserId(userId)
	if err != nil {
		log.Println("Summary.GetUserForGenmemberByUserId", err)
		return &result, nil
	}

	if user.UserTypeId == model.USER_TYPE_AFFILIATE {
		if err := s.repo.CreateMissingAffiliate(userId); err != nil {
			log.Println("Summary.CreateMissingAffiliate", err)
		}
	}

	// Allow once per day
	actionAt := time.Now().UTC()
	var createBody model.RaceActionCreateBody
	createBody.Name = "UpAffSum"
	createBody.JsonRequest = helper.StructJson(struct {
		UserId int64
	}{
		UserId: userId,
	})
	createBody.Status = "PENDING"
	createBody.ActionKey = fmt.Sprintf("UpAffSum%s_U%d", actionAt.Format("060102"), user.Id)
	createBody.UnlockAt = actionAt.Add(time.Second * 1)
	if _, err := s.repo.GetRaceActionIdByActionKey(createBody.ActionKey); err != nil {
		if err == gorm.ErrRecordNotFound {
			// create race condition
			if _, err := s.repo.CreateRaceCondition(createBody); err == nil {
				if err := s.repo.UpdateTotalMemberFirstDeposit(userId); err != nil {
					log.Println("Summary.UpdateTotalMemberFirstDeposit.ERROR=", err)
				}
			} else {
				log.Println("Summary.CreateRaceCondition.ERROR=", err)
			}
		}
	} else {
		log.Println("Summary.GetRaceActionIdByActionKe.ERROR=", err)
	}

	response, err := s.repo.GetUserAffSummary(userId)
	if err != nil && err.Error() != "record not found" {
		log.Println("Summary.GetUserAffSummary.ERROR=", err)
		return &result, nil
	}

	// getCommistion, err := s.repo.GetCommissionSetting()
	// if err != nil {
	// 	log.Println("Summary.GetCommissionSetting", err)
	// 	return &result, nil
	// }

	// totalSport := data.CommissionSport
	// totalCasino := data.CommissionCasino
	// totalSlot := data.CommissionGame
	// total := totalSport + totalCasino + totalSlot + float64(data.BonusShareTotal) + float64(data.FirstDepositBonus)

	// response := model.AfSummary{
	// 	CommissionSport:    data.CommissionSport,
	// 	CommissionCasino:   data.CommissionCasino,
	// 	CommissionGame:     data.CommissionGame,
	// 	CommissionTotal:    total,
	// 	PercentSport:       getCommistion.Sport,
	// 	PercentCasino:      getCommistion.Casino,
	// 	PercentGame:        getCommistion.Slot,
	// 	UserId:             data.UserId,
	// 	CommissionCurrent:  data.CommissionCurrent,
	// 	BonusShareTotal:    data.BonusShareTotal,
	// 	FirstDepositBonus:  data.FirstDepositBonus,
	// 	LinkClickTotal:     data.LinkClickTotal,
	// 	MemberTotal:        data.MemberTotal,
	// 	MemberDepositTotal: data.MemberDepositTotal,
	// }

	return response, nil
}

func (s AffiliateServiceRepos) GetCommissionSetting() (*model.AfCommissionResponse, error) {

	data, err := s.repo.GetCommissionSetting()
	if err != nil && err.Error() != "record not found" {
		return nil, err
	}
	return data, nil
}

func (s AffiliateServiceRepos) GetActiveAffMemberByLevel(req model.AffMemberListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.Pagination(&req.Page, &req.Limit); err != nil {
		return nil, err
	}

	list, total, err := s.repo.GetActiveAffMemberByLevel(req)
	if err != nil && err.Error() != "record not found" {
		return nil, err
	}

	// MemberCode := []string{}
	// for _, v := range list {
	// 	MemberCode = append(MemberCode, v.MemberCode)
	// }

	// listLog, err := s.repo.GetPlayLog(MemberCode)
	// if err != nil && err.Error() != "record not found" {
	// 	return nil, err
	// }

	// for i, k := range list {
	// 	for _, v := range listLog {
	// 		if k.MemberCode == v.MemberCode {
	// 			list[i].PlayBalance += v.PlayBalance
	// 		}
	// 	}
	// }

	return &model.SuccessWithPagination{
		List:  list,
		Total: total,
	}, nil
}

func (s AffiliateServiceRepos) GetIncome(userId int64, req model.AffMemberListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.Pagination(&req.Page, &req.Limit); err != nil {
		return nil, err
	}

	req.RefBy = &userId
	list, total, err := s.repo.GetIncome(req)
	if err != nil && err.Error() != "record not found" {
		return nil, err
	}

	var result model.SuccessWithPagination
	result.List = list
	result.Total = total

	return &result, nil
}

func (s AffiliateServiceRepos) GetUserIncomeWebLogList(query model.UserIncomeWebLogListRequest) (*model.UserIncomeWebLogListResponse, error) {

	if err := helper.Pagination(&query.Page, &query.Limit); err != nil {
		return nil, err
	}

	result, err := s.repo.GetUserIncomeWebLogList(query)
	if err != nil && err.Error() != "record not found" {
		return nil, err
	}
	return result, nil
}

func (s AffiliateServiceRepos) GetUserIncomeCompletedLogList(query model.UserIncomeCompletedLogListRequest) ([]model.UserIncomeCompletedLogResponse, int64, error) {

	if err := helper.Pagination(&query.Page, &query.Limit); err != nil {
		return nil, 0, err
	}

	if query.TypeName == "" || strings.ToUpper(query.TypeName) == "ALL" {
		// Change type by user_type_id
		user, err := s.repo.GetRefUserByRefId(query.UserId)
		if err != nil {
			return nil, 0, err
		}
		if user.UserTypeId == model.USER_TYPE_AFFILIATE {
			query.TypeIds = []int64{model.USER_INCOME_TYPE_AFFILIATE, model.USER_INCOME_TYPE_PROMOTION_RETURN_LOSS, model.USER_INCOME_TYPE_PROMOTION_RETURN_TURN}
		} else if user.UserTypeId == model.USER_TYPE_ALLIANCE {
			query.TypeIds = []int64{model.USER_INCOME_TYPE_ALLIANCE, model.USER_INCOME_TYPE_PROMOTION_RETURN_LOSS, model.USER_INCOME_TYPE_PROMOTION_RETURN_TURN}
		}
	}

	list, total, err := s.repo.GetUserIncomeCompletedLogList(query)
	if err != nil && err.Error() != "record not found" {
		return nil, 0, err
	}
	return list, total, nil
}

func (s AffiliateServiceRepos) GetAfRegisterBonusType() (*model.AfRegisterBonusTypeResponse, error) {

	list, err := s.repo.GetAfRegisterBonusType()
	if err != nil && err.Error() != "record not found" {
		return nil, err
	}

	var result model.AfRegisterBonusTypeResponse
	result.Result = list

	return &result, nil
}

func (s AffiliateServiceRepos) GetAfRegisterBonusOption() (*model.AfRegisterBonusOptionResponse, error) {

	list, err := s.repo.GetAfRegisterBonusOption()
	if err != nil && err.Error() != "record not found" {
		return nil, err
	}

	var result model.AfRegisterBonusOptionResponse
	result.Result = list

	return &result, nil
}

func (s AffiliateServiceRepos) GetAfReport(query model.AfReportQuery) (*model.AfReportResponse, error) {

	if err := helper.Pagination(&query.Page, &query.Limit); err != nil {
		return nil, err
	}

	list, total, err := s.repo.GetReport(query)
	if err != nil && err.Error() != "record not found" {
		return nil, err
	}

	var result model.AfReportResponse
	result.Result = list
	result.Total = *total

	return &result, nil
}

func (s AffiliateServiceRepos) UpdateCommission(data model.AfCommissionUpdateRequest) error {

	if err := s.repo.UpdateCommission(data); err != nil {
		return err
	}

	result, err := s.repo.GetCommissionSetting()
	if err != nil && err.Error() != "record not found" {
		fmt.Println("No Commission Setting")
	}

	// [ADMIN_ACTION] SUCCESS แก้ไขข้อมูลผู้ใช้งาน {name}
	var adminActionCreateBody model.AdminActionCreateBody
	adminActionCreateBody.AdminId = data.UpdateById
	adminActionCreateBody.TypeId = model.ADMIN_ACTION_COMMISION_MANAGE
	adminActionCreateBody.Detail = "แก้ไขข้อมูล Commission"
	adminActionCreateBody.IsSuccess = true
	adminActionCreateBody.IsShow = true
	adminActionCreateBody.JsonInput = helper.StructJson(data)
	adminActionCreateBody.JsonOutput = helper.StructJson(result)
	if _, err := s.repo.CreateAdminAction(adminActionCreateBody); err != nil {
		return err
	}
	return nil
}

func (s AffiliateServiceRepos) WithdrawCommission(userId int64) error {

	startTime := time.Now()

	// [UserInfo]
	user, err := s.repo.GetUserMemberInfoById(userId)
	if err != nil {
		return internalServerError(err)
	}

	setting, err := s.repo.GetCommissionSetting()
	if err != nil {
		return internalServerError(err)
	}

	commission, err := s.repo.GetCommissionUser(userId)
	if err != nil {
		return internalServerError(err)
	}

	debugReq := map[string]interface{}{
		"userId":     userId,
		"memberCode": user.MemberCode,
		"startTime":  startTime,
	}

	// ถอนทั้งหมด
	commissionAmount := commission.CommissionCurrent
	// totalCommissionSatang := int64(0)

	var myIncomeQuery model.AffTransactionListRequest
	myIncomeQuery.UserId = &userId
	// myIncomeQuery.TypeId = ALL
	pendingStatus := model.AFF_TRANSACTION_STATUS_PENDING
	myIncomeQuery.StatusId = &pendingStatus
	pendingIncomeList, _, err := s.repo.GetAffTransactionList(myIncomeQuery)
	if err != nil {
		return internalServerError(err)
	}
	pendingTransIds := []int64{}
	for _, v := range pendingIncomeList {
		pendingTransIds = append(pendingTransIds, v.Id)
	}

	// Old
	// var commissionPlayOnlySatang int64
	// for _, v := range pendingIncomeList {
	// 	pendingTransIds = append(pendingTransIds, v.Id)
	// Fix bug 18.06*100 = 1805.9999999999998
	// totalCommissionSatang += int64(math.Ceil(v.IncomeAmount * 100))
	// if v.TypeId == model.AFF_TRANSACTION_TYPE_PLAY_COMMISION {
	// 	commissionPlayOnlySatang += int64(math.Ceil(v.IncomeAmount * 100))
	// }
	// }
	// if commissionAmount != float64(totalCommissionSatang)/100 {
	// 	// later check ???
	// 	log.Println("commissionAmount:", commissionAmount, "is not equal to totalCommissionSatang:", totalCommissionSatang)
	// }
	// commissionAmount = float64(totalCommissionSatang) / 100

	// New
	pendingIncomeListSummary, err := s.repo.GetAffTransactionListSummary(myIncomeQuery)
	if err != nil {
		return internalServerError(err)
	}
	if commissionAmount != pendingIncomeListSummary.TotalAmount {
		// later check ???
		log.Println("commissionAmount:", commissionAmount, "is not equal to totalCommissionSatang:", pendingIncomeListSummary.TotalAmount)
	}
	commissionAmount = pendingIncomeListSummary.TotalAmount

	// Check Min Setting
	if pendingIncomeListSummary.TotalAmount < float64(setting.CommissionWithdrawMin) {
		log.Println("current commission:", commissionAmount, "is less than min withdraw:", setting.CommissionWithdrawMin)
		return internalServerError(errors.New(commissionNotEnough))
	}

	config, err := s.GetUserIncomeMaxAmountConfig()
	if err != nil {
		return internalServerError(err)
	}

	// RaceCondition rcKey is YYMMDD-HHMMU{userId}
	var rcCreateBody model.AffTransactionWithdrawCreateBody
	rcCreateBody.RcKey = fmt.Sprintf("%sU%d", time.Now().Format("060102-1504"), userId)
	rcCreateBody.UserId = userId
	rcCreateBody.WithdrawAmount = commissionAmount
	rcCreateBody.JsonRelatedIds = helper.StructJson(pendingTransIds)

	rcId, err := s.repo.CreateAffTransactionWithdraw(rcCreateBody)
	if err != nil {
		return internalServerError(err)
	}

	// ===========================================================

	// [AffTransaction] set PENDING to waitConfirm
	if err := s.repo.SetPendingAffTransactionList(pendingTransIds); err != nil {
		return internalServerError(err)
	}

	var incomeLogCreateBody model.UserIncomeLogCreateBody
	incomeLogCreateBody.RefId = rcId
	incomeLogCreateBody.UserId = user.Id
	incomeLogCreateBody.TypeId = model.USER_INCOME_TYPE_AFFILIATE
	incomeLogCreateBody.Detail = "แจกโบนัสรายได้แนะนำเพื่อน"
	incomeLogCreateBody.CreditAmount = commissionAmount
	incomeLogCreateBody.StatusId = model.USER_INCOME_STATUS_PENDING
	incomeLogCreateBody.CreateBy = user.Id
	incomeLogCreateBody.CreateByName = user.Fullname
	incomeId, err := s.repo.CreateUserIncomeLog(incomeLogCreateBody)
	if err != nil {
		return err
	}

	// RESET COMMISSION TO ZERO
	if err := s.repo.WithdrawTotalCommission(userId, commissionAmount); err != nil {
		if err := s.repo.ErrorLog("WithdrawCommission.WithdrawTotalCommission", debugReq, err); err != nil {
			log.Println("WithdrawCommission.WithdrawTotalCommission", err)
		}
		return internalServerError(err)
	}

	// OverAmount will stay PENDING in user income
	incomeType := fmt.Sprintf("waitApproveincome: %v config %v", commissionAmount, helper.StructJson(config))
	if commissionAmount <= config.UserIncomeMaxAmount {
		// AUTO confirm on small amount
		if err := s.autoConfirmUserTakeAffiliateAmount(*incomeId, user.Id); err != nil {
			if err := s.repo.ErrorLog("WithdrawCommission.autoConfirmUserTakeAffiliateAmount", debugReq, err); err != nil {
				log.Println("WithdrawCommission.autoConfirmUserTakeAffiliateAmount", err)
			}
			return internalServerError(err)
		}
		// onTransfered
		incomeType = fmt.Sprintf("autoApproveincome: %v config %v", commissionAmount, helper.StructJson(config))
	} else {
		// onIncomePending
		// AddTo BankPendingRecord -> แจกโบนัสแนะนำเพื่อน
		dashboardRepo := repository.NewDashboardRepository(s.shareDb)
		if err := AddBankPendingRecordFromAffiliate(dashboardRepo, *incomeId, user.Id, startTime, commissionAmount, "อัตโนมัติ"); err != nil {
			log.Println("WithdrawCommission.AddBankPendingRecordFromAffiliate", err)
		}

		suggestAuto := int64(0)
		// EXTERNAL NOTI
		var externalNoti model.NotifyExternalNotificationRequest
		externalNoti.TypeNotify = model.ActitvityBeforeBonus
		externalNoti.MemberCode = *user.MemberCode
		externalNoti.BonusCredit = &commissionAmount
		externalNoti.UserCredit = user.Credit + commissionAmount
		externalNoti.ConfirmedByAdminId = suggestAuto
		externalNoti.WebScoket.UserID = user.Id
		externalNoti.WebScoket.FullName = user.Fullname
		externalNoti.WebScoket.PhoneNumber = user.Phone
		externalNoti.WebScoket.AlertType = "PENDING_BONUS_ACTIVITY"
		if err := s.serviceNoti.ExternalNotification(externalNoti); err != nil {
			log.Println("WithdrawCommission.ExternalNotification.ERROR", err)
		}
	}

	// SUCCESS LOG
	if err := s.repo.SuccessLog("WithdrawCommission.SUCCESS", debugReq, incomeType); err != nil {
		log.Println("WithdrawCommission.SUCCESS", err)
	}
	return nil
}

func (s *AffiliateServiceRepos) GetUserIncomeMaxAmountConfig() (model.MarketingConfigResponse, error) {

	var record model.MarketingConfigResponse
	record.UserIncomeMaxAmount = 500

	// TRY to get config, Default on error
	config, err := s.repo.GetMarketingConfigByKey("AUTO_USER_INCOME_MAX_AMOUNT", "500")
	if err == nil {
		// record.UserIncomeMaxAmount = config.ConfigVal convert to float64
		tempFloat, err := strconv.ParseFloat(config.ConfigVal, 64)
		if err == nil {
			record.UserIncomeMaxAmount = tempFloat
		}
	}
	return record, nil
}

func (s *AffiliateServiceRepos) autoConfirmUserTakeAffiliateAmount(incomeId int64, confirmBy int64) error {

	actionAt := time.Now()

	// 1. CONFIRMING is to set User Credit

	income, err := s.repo.GetUserIncomeLogById(incomeId)
	if err != nil {
		return internalServerError(err)
	}
	if income.StatusId == model.USER_INCOME_STATUS_PENDING && income.ConfirmBy == nil {

		returnAmount := income.CreditAmount
		if returnAmount > 0 {
			user, err := s.repo.GetUserMemberInfoById(income.UserId)
			if err != nil {
				return internalServerError(err)
			}
			// [USER_CREDIT] Just only go to user credit
			var userCreditReq model.UserTransactionCreateRequest
			userCreditReq.RefId = &income.Id
			userCreditReq.UserId = income.UserId
			userCreditReq.TypeId = model.CREDIT_TYPE_AFFILIATE_INCOME
			userCreditReq.BonusAmount = returnAmount
			userCreditReq.StartWorkAt = actionAt // เริ่มนับตอนกดยินยัน
			creditTransferResp, err := s.repo.IncreaseUserCredit(userCreditReq)
			if err != nil {
				return internalServerError(err)
			}
			// [UserIncome]
			var confirmBody model.UserIncomeLogConfirmBody
			confirmBody.Id = income.Id
			confirmBody.CreditAfter = creditTransferResp.AgentAfterAmount
			confirmBody.TransferAt = creditTransferResp.TransferAt
			confirmBody.ConfirmBy = confirmBy
			confirmBody.ConfirmByName = *user.Username
			if err := s.repo.ConfirmUserIncomeLog(confirmBody); err != nil {
				return internalServerError(err)
			}
			// Update Transaction
			if rcWithdraw, err := s.repo.GetAffTransactionWithdrawById(*income.RefId); err == nil {
				// [AffTransaction] set waitConfirm to TRANSFERRED
				// unmarchel rcWithdraw.JsonRelatedIds to []int64
				waitIds := make([]int64, 0)
				if errJson := json.Unmarshal([]byte(rcWithdraw.JsonRelatedIds), &waitIds); errJson == nil {
					if err := s.repo.SetConfirmAffTransactionList(waitIds); err != nil {
						return internalServerError(err)
					}
				}
				if len(waitIds) > 0 {
					// get related income by type
					commissionPlayOnly, err := s.repo.GetTotalAffTransactionPlayCommission(waitIds)
					// [turnOver] only PlayingCommission, other will be imeediately.
					if err == nil && commissionPlayOnly > 0 {
						// CreateTurnOverFromAffPlayCommission
						if err := CreateTurnOverFromAffPlayCommission(s.repo, user.Id, commissionPlayOnly, income.Id); err != nil {
							if err := s.repo.ErrorLog("autoConfirmUserTakeAffiliateAmount.CreateTurnOverFromAffPlayCommission", nil, err); err != nil {
								log.Println("autoConfirmUserTakeAffiliateAmount.ErrorLog.ERROR=", err)
							}
						}
					} else {
						log.Println("autoConfirmUserTakeAffiliateAmount.GetTotalAffTransactionPlayCommission.ERROR=", err)
					}
				}
			}
			// [LINE]
			var externalNoti model.NotifyExternalNotificationRequest
			externalNoti.TypeNotify = model.ActitvityAfterBonus
			externalNoti.MemberCode = *user.MemberCode
			externalNoti.BonusCredit = &returnAmount
			externalNoti.UserCredit = user.Credit + returnAmount
			suggestAuto := int64(0)
			externalNoti.ConfirmedByAdminId = suggestAuto
			if err := s.serviceNoti.ExternalNotification(externalNoti); err != nil {
				log.Println("autoConfirmUserTakeAffiliateAmount.ExternalNotification.ERROR=", err)
			}
		} else {
			return notFound("NO_RETURN_DATA")
		}
	} else {
		return badRequest("INVALID_TRANSACTION_STATUS")
	}
	return nil
}

func (s AffiliateServiceRepos) UpdateMemberFirstDeposit(member model.Member, amount float64) error {

	if err := s.repo.IncreaseMemberFirstDeposit(member.Id, *member.RefBy); err != nil {
		log.Println("UpdateMemberFirstDeposit.IncreaseMemberFirstDeposit", err)
		return internalServerError(err)
	}
	return nil
}

func UpdateMemberFirstDeposit(repo repository.AffiliateRepository, member model.Member, amount float64) error {

	if err := repo.IncreaseMemberFirstDeposit(member.Id, *member.RefBy); err != nil {
		log.Println("UpdateMemberFirstDeposit.IncreaseMemberFirstDeposit", err)
		return internalServerError(err)
	}
	return nil
}

func (s AffiliateServiceRepos) UpdateCommissionFirstDeposit(member model.Member, amount float64) error {

	debugReq := map[string]interface{}{
		"member": member,
		"amount": amount,
	}

	// refId, err := s.repo.GetRefIdByUserId(userId)
	// if err != nil {
	// 	if err := s.repo.ErrorLog("UpdateCommissionFirstDeposit.GetRefIdByUserId", debugReq, err); err != nil {
	// 		log.Println("UpdateCommissionFirstDeposit.GetRefIdByUserId", err)
	// 	}
	// 	return internalServerError(err)
	// }
	debugReq["refId"] = member.RefBy

	setting, err := s.repo.GetCommissionSetting()
	if err != nil {
		if err := s.repo.ErrorLog("UpdateCommissionFirstDeposit.GetCommissionSetting", debugReq, err); err != nil {
			log.Println("UpdateCommissionFirstDeposit.GetCommissionSetting", err)
		}
		return internalServerError(err)
	}

	if setting != nil {
		setting.Description = "FIRST_DEPOSIT"
	}
	debugReq["setting"] = setting
	// ถ้าไม่มีข้อมูล หรือ Setting ตั้งค่าได้ 0 จะไม่ทำ
	if setting == nil {
		if err := s.repo.ErrorLog("UpdateCommissionFirstDeposit.VALIDATE", debugReq, "SKIP DUE NO SETTING"); err != nil {
			log.Println("UpdateCommissionFirstDeposit.VALIDATE", err)
		}
		return nil
	}
	if setting.RegisterBonusOptionName == "CREDIT" && setting.RegisterBonusCredit <= 0 {
		if err := s.repo.ErrorLog("UpdateCommissionFirstDeposit.VALIDATE", debugReq, "SKIP DUE MAX_CREDIT_IS_ZERO"); err != nil {
			log.Println("UpdateCommissionFirstDeposit.VALIDATE", err)
		}
		return nil
	}
	if setting.RegisterBonusOptionName == "DEPOSIT_PERCENT" && setting.RegisterBonusMaxPercent <= 0 {
		if err := s.repo.ErrorLog("UpdateCommissionFirstDeposit.VALIDATE", debugReq, "SKIP DUE MAX_PERCENT_IS_ZERO"); err != nil {
			log.Println("UpdateCommissionFirstDeposit.VALIDATE", err)
		}
		return nil
	}

	var commissionAmount float64
	// *** [20231205] ถ้ายอดฝากไม่ถึง จะไม่เปลี่ยน
	if setting.RegisterBonusTypeName == "FIRST_DEPOSIT" && int64(amount) >= setting.RegisterBonusMin {

		if setting.RegisterBonusOptionName == "CREDIT" {
			commissionAmount = float64(setting.RegisterBonusCredit)
		}

		if setting.RegisterBonusOptionName == "DEPOSIT_PERCENT" {
			commissionAmount = amount * float64(setting.RegisterBonusCredit) / 100
			if commissionAmount > float64(setting.RegisterBonusMaxPercent) {
				commissionAmount = float64(setting.RegisterBonusMaxPercent)
			}
		}
		debugReq["commission"] = commissionAmount

		if commissionAmount < 1 {
			if err := s.repo.ErrorLog("UpdateCommissionFirstDeposit.VALIDATE_COMMISSION_AMOUNT", debugReq, "SKIP DUE COMMISSION_IS_LESS_THAN_ONE"); err != nil {
				log.Println("UpdateCommissionFirstDeposit.VALIDATE_COMMISSION_AMOUNT", err)
			}
			return nil
		}

		commissionAmount = math.Floor(commissionAmount)
		debugReq["commission"] = commissionAmount

		if err := s.repo.UpdateCommissionFirstDeposit(member.Id, *member.RefBy, commissionAmount); err != nil {
			if err := s.repo.ErrorLog("UpdateCommissionFirstDeposit.GetCommissionSetting", debugReq, err); err != nil {
				log.Println("UpdateCommissionFirstDeposit.GetCommissionSetting", err)
			}
			return internalServerError(err)
		}
		debugReq["UpdateCommissionFirstDeposit"] = "UPDATED"

		if commissionAmount > 0 && *member.RefBy > 0 {
			// CreateTurnOverFromAFFFristDeposit
			if err := s.CreateTurnOverFromAffFirstDeposit(*member.RefBy, commissionAmount, member.Id); err != nil {
				if err := s.repo.ErrorLog("UpdateCommissionFirstDeposit.CreateTurnOverFromAFFFristDeposit", debugReq, err); err != nil {
					log.Println("UpdateCommissionFirstDeposit.CreateTurnOverFromAFFFristDeposit", err)
				}
			}
		}

	} else {
		if err := s.repo.ErrorLog("UpdateCommissionFirstDeposit.VALIDATE", debugReq, "SKIP DUE NOT_FIRST_DEPOSIT_OR_MIN_AMOUNT_IS_LESS_THAN_MIN"); err != nil {
			log.Println("UpdateCommissionFirstDeposit.VALIDATE", err)
		}
	}

	// SUCCESS
	if err := s.repo.SuccessLog("UpdateCommissionFirstDeposit.SUCCESS", debugReq, commissionAmount); err != nil {
		log.Println("UpdateCommissionFirstDeposit.SUCCESS", err)
	}
	return nil
}

func UpdateCommissionFirstDeposit(repo repository.AffiliateRepository, member model.Member, amount float64) error {

	debugReq := map[string]interface{}{
		"member": member,
		"amount": amount,
	}

	// refId, err := s.repo.GetRefIdByUserId(userId)
	// if err != nil {
	// 	if err := s.repo.ErrorLog("UpdateCommissionFirstDeposit.GetRefIdByUserId", debugReq, err); err != nil {
	// 		log.Println("UpdateCommissionFirstDeposit.GetRefIdByUserId", err)
	// 	}
	// 	return internalServerError(err)
	// }
	debugReq["refId"] = member.RefBy

	setting, err := repo.GetCommissionSetting()
	if err != nil {
		if err := repo.ErrorLog("UpdateCommissionFirstDeposit.GetCommissionSetting", debugReq, err); err != nil {
			log.Println("UpdateCommissionFirstDeposit.GetCommissionSetting", err)
		}
		return internalServerError(err)
	}

	if setting != nil {
		setting.Description = "FIRST_DEPOSIT"
	}
	debugReq["setting"] = setting
	// ถ้าไม่มีข้อมูล หรือ Setting ตั้งค่าได้ 0 จะไม่ทำ
	if setting == nil {
		if err := repo.ErrorLog("UpdateCommissionFirstDeposit.VALIDATE", debugReq, "SKIP DUE NO SETTING"); err != nil {
			log.Println("UpdateCommissionFirstDeposit.VALIDATE", err)
		}
		return nil
	}
	if setting.RegisterBonusOptionName == "CREDIT" && setting.RegisterBonusCredit <= 0 {
		if err := repo.ErrorLog("UpdateCommissionFirstDeposit.VALIDATE", debugReq, "SKIP DUE MAX_CREDIT_IS_ZERO"); err != nil {
			log.Println("UpdateCommissionFirstDeposit.VALIDATE", err)
		}
		return nil
	}
	if setting.RegisterBonusOptionName == "DEPOSIT_PERCENT" && setting.RegisterBonusMaxPercent <= 0 {
		if err := repo.ErrorLog("UpdateCommissionFirstDeposit.VALIDATE", debugReq, "SKIP DUE MAX_PERCENT_IS_ZERO"); err != nil {
			log.Println("UpdateCommissionFirstDeposit.VALIDATE", err)
		}
		return nil
	}

	var commissionAmount float64
	// *** [20231205] ถ้ายอดฝากไม่ถึง จะไม่เปลี่ยน
	if setting.RegisterBonusTypeName == "FIRST_DEPOSIT" && int64(amount) >= setting.RegisterBonusMin {

		if setting.RegisterBonusOptionName == "CREDIT" {
			commissionAmount = float64(setting.RegisterBonusCredit)
		}

		if setting.RegisterBonusOptionName == "DEPOSIT_PERCENT" {
			commissionAmount = amount * float64(setting.RegisterBonusCredit) / 100
			if commissionAmount > float64(setting.RegisterBonusMaxPercent) {
				commissionAmount = float64(setting.RegisterBonusMaxPercent)
			}
		}
		debugReq["commission"] = commissionAmount

		if commissionAmount < 1 {
			if err := repo.ErrorLog("UpdateCommissionFirstDeposit.VALIDATE_COMMISSION_AMOUNT", debugReq, "SKIP DUE COMMISSION_IS_LESS_THAN_ONE"); err != nil {
				log.Println("UpdateCommissionFirstDeposit.VALIDATE_COMMISSION_AMOUNT", err)
			}
			return nil
		}

		commissionAmount = math.Floor(commissionAmount)
		debugReq["commission"] = commissionAmount

		if err := repo.UpdateCommissionFirstDeposit(member.Id, *member.RefBy, commissionAmount); err != nil {
			if err := repo.ErrorLog("UpdateCommissionFirstDeposit.GetCommissionSetting", debugReq, err); err != nil {
				log.Println("UpdateCommissionFirstDeposit.GetCommissionSetting", err)
			}
			return internalServerError(err)
		}
		debugReq["UpdateCommissionFirstDeposit"] = "UPDATED"

		if commissionAmount > 0 && *member.RefBy > 0 {
			// CreateTurnOverFromAFFFristDeposit
			if err := CreateTurnOverFromAffFirstDeposit(repo, *member.RefBy, commissionAmount, member.Id); err != nil {
				if err := repo.ErrorLog("UpdateCommissionFirstDeposit.CreateTurnOverFromAFFFristDeposit", debugReq, err); err != nil {
					log.Println("UpdateCommissionFirstDeposit.CreateTurnOverFromAFFFristDeposit", err)
				}
			}
		}

	} else {
		if err := repo.ErrorLog("UpdateCommissionFirstDeposit.VALIDATE", debugReq, "SKIP DUE NOT_FIRST_DEPOSIT_OR_MIN_AMOUNT_IS_LESS_THAN_MIN"); err != nil {
			log.Println("UpdateCommissionFirstDeposit.VALIDATE", err)
		}
	}

	// SUCCESS
	if err := repo.SuccessLog("UpdateCommissionFirstDeposit.SUCCESS", debugReq, commissionAmount); err != nil {
		log.Println("UpdateCommissionFirstDeposit.SUCCESS", err)
	}
	return nil
}

func (s AffiliateServiceRepos) GetUserAffiliateMemberList(query model.AffiliateMemberListRequest) ([]model.AffiliateMemberResponse, int64, error) {

	if err := helper.UnlimitPagination(&query.Page, &query.Limit); err != nil {
		return nil, 0, err
	}

	list, total, err := s.repo.GetUserAffiliateMemberList(query)
	if err != nil {
		return nil, 0, err
	}

	return list, total, nil
}

func (s AffiliateServiceRepos) MakeReportAffiliateUserList(query model.AffiliateUserListRequest) error {

	dateType, err := s.repo.GetDateFromDateType(model.DateTypeResponse{
		DateType: query.DateType,
		DateFrom: query.FromDate,
		DateTo:   query.ToDate,
	})
	if err != nil {
		return err
	}

	startAt, err := time.Parse("2006-01-02", dateType.DateFrom)
	if err != nil {
		return err
	}
	endAt, err := time.Parse("2006-01-02", dateType.DateTo)
	if err != nil {
		return err
	}

	// Loop DateFrom to DateTo max 100 days
	for i := 0; i < 100; i++ {
		statementAt := startAt.AddDate(0, 0, i)
		if statementAt.After(endAt) {
			break
		}
		statementDate := statementAt.Format("2006-01-02")
		if _, err := time.Parse("2006-01-02", statementDate); err != nil {
			log.Println("realtimeAffiliateUserList.ERROR.Parse", err, statementDate)
			return err
		}
		// RACE_CONDITION
		actionId, err := RacingRunAffiliateUserList(s.repo, statementDate)
		if err != nil || actionId == nil {
			log.Println("realtimeAffiliateUserList.ERROR.racingRunAffiliateUserList", err)
			continue // return err
		}
		// Do Work
		if err := s.repo.MakeReportAffiliateUserList(statementDate); err != nil {
			log.Println("realtimeAffiliateUserList.MakeReportAffiliateUserList", err)
			continue // return err
		} else {
			// RACE_SUCCESS
			if err := RacingRunAffiliateUserListSuccess(s.repo, *actionId); err != nil {
				log.Println("realtimeAffiliateUserList.ERROR.racingRunAffiliateUserListSuccess", err)
				continue // return err
			}
		}
		// Sleep 1 second
		time.Sleep(1 * time.Second)
	}

	return nil
}

func (s AffiliateServiceRepos) goTodayAffiliateUserList() {

	// Today in BKK Only
	loc := time.FixedZone("Asia/Bangkok", 7*60*60)
	statementDate := time.Now().In(loc).Format("2006-01-02")

	// RACE_CONDITION
	actionId, err := RacingRunAffiliateUserList(s.repo, statementDate)
	if err != nil || actionId == nil {
		log.Println("realtimeAffiliateUserList.ERROR.racingRunAffiliateUserList", err)
		return
	}
	// Do Work
	if err := s.repo.MakeReportAffiliateUserList(statementDate); err != nil {
		log.Println("realtimeAffiliateUserList.MakeReportAffiliateUserList", err)
	} else {
		// RACE_SUCCESS
		if err := RacingRunAffiliateUserListSuccess(s.repo, *actionId); err != nil {
			log.Println("realtimeAffiliateUserList.ERROR.racingRunAffiliateUserListSuccess", err)
		}
	}
}

func RacingRunAffiliateUserList(repo repository.AffiliateRepository, statementDate string) (*int64, error) {

	// RUN ONCE PER HOURS
	// RUNNING KEY = REPORT_AFF_USER_{STATEMENTDATE} = REPORT_AFF_USER_241231
	// Max run is around 2 minutes for 90000 users (LOCAL)
	actionAt := time.Now().UTC()
	fnName := "REPORT_AFF_USER"
	actionKey := fmt.Sprintf("%s_%s_%s", fnName, statementDate, actionAt.Format("15"))
	// END OF MODIFIABLE CONFIG

	var createBody model.RaceActionCreateBody
	createBody.Name = fnName
	createBody.JsonRequest = helper.StructJson(map[string]interface{}{"statementDate": statementDate})
	createBody.Status = "PENDING"
	createBody.ActionKey = actionKey
	createBody.UnlockAt = actionAt.Add(time.Minute * 60)
	if oldWork, err := repo.GetRaceActionByActionKey(createBody.ActionKey); err != nil {
		if err != gorm.ErrRecordNotFound {
			return nil, internalServerError(errors.New("CANNOT_CHECK_ACTION"))
		}
	} else {
		if oldWork.Id > 0 && actionAt.After(oldWork.UnlockAt) {
			// UPDATE
			canceledKey := fmt.Sprintf("%s_DONE_%d", fnName, oldWork.Id)
			canceledStatus := "TIMEOUT"
			var updateBody model.RaceActionUpdateBody
			updateBody.ActionKey = &canceledKey
			updateBody.Status = &canceledStatus
			if err := repo.UpdateRaceCondition(oldWork.Id, updateBody); err != nil {
				log.Println("racingRunAffiliateUserList.ERROR.UpdateRaceCondition", err)
				return nil, internalServerError(errors.New("WORK_IN_ACTION"))
			}
		} else {
			return nil, internalServerError(errors.New("WORK_IN_ACTION"))
		}
	}

	// ================================ USAGE ================================
	// // RACE_CONDITION
	// actionId, err := s.racingRunAffiliateUserList("2024-12-31")
	// if err != nil || actionId == nil {
	// 	return err
	// }
	// 	.... DO SOMETHING
	// // RACE_SUCCESS
	// if err := s.racingRunAffiliateUserListSuccess(*actionId); err != nil {
	// 	log.Println("realtimeAffiliateUserList.ERROR.racingRunAffiliateUserListSuccess", err)
	// }
	// ================================ USAGE ================================

	// only not found will be created
	actionId, err := repo.CreateRaceCondition(createBody)
	if err != nil {
		log.Println("racingRunAffiliateUserList.ERROR.CreateRaceCondition", err)
		return nil, internalServerError(errors.New("CANNOT_CREATE_ACTION"))
	}
	return &actionId, nil
}

func RacingRunAffiliateUserListSuccess(repo repository.AffiliateRepository, actionId int64) error {

	// RACE_SUCCESS
	// successKey := fmt.Sprintf("TODAY_PLAYLOG_DONE_%d", actionId)
	successStatus := "SUCCESS"
	var updateBody model.RaceActionUpdateBody
	// updateBody.ActionKey = &successKey
	updateBody.Status = &successStatus
	if err := repo.UpdateRaceCondition(actionId, updateBody); err != nil {
		log.Println("racingRunAffiliateUserListSuccess.ERROR.UpdateRaceCondition", err)
	}
	return nil
}

func (s AffiliateServiceRepos) GetAffiliateUserList(query model.AffiliateUserListRequest) ([]model.AffiliateUserResponse, int64, error) {

	if err := helper.UnlimitPagination(&query.Page, &query.Limit); err != nil {
		return nil, 0, err
	}

	// [2024-10-15] GetAffiliateUserList => GetAffiliateActiveUserList
	list, total, err := s.repo.GetAffiliateActiveUserList(query)
	if err != nil {
		return nil, 0, err
	}
	return list, total, nil
}

func (s AffiliateServiceRepos) GetAffiliateDepositPlayList(query model.AffiliateDepositPlayListRequest) ([]model.AffiliateUserDepositPlayResponse, int64, error) {

	if err := helper.UnlimitPagination(&query.Page, &query.Limit); err != nil {
		return nil, 0, err
	}

	list, total, err := s.repo.GetAffiliateDepositPlayList(query)
	if err != nil {
		return nil, 0, err
	}
	return list, total, nil
}

func (s AffiliateServiceRepos) GetAffiliateDepositPlayCountLevel(req model.AffiliateDepositPlayListRequest) (*model.AffiliateUserDownlineCountResponse, error) {

	data, err := s.repo.GetAffiliateDepositPlayCountLevel(req)
	if err != nil {
		return nil, err
	}
	return data, nil
}

func (s AffiliateServiceRepos) CreateTurnOverFromAffFirstDeposit(userId int64, bonusAmount float64, turnoverRefId int64) error {

	err := CreateTurnOverFromAffFirstDeposit(s.repo, userId, bonusAmount, turnoverRefId)
	if err != nil {
		return err
	}
	return nil
}

func CreateTurnOverFromAffFirstDeposit(repo repository.AffiliateRepository, userId int64, bonusAmount float64, turnoverRefId int64) error {

	checkTurn, err := GetTurnoverSetting(repository.NewTurnoverRepository(repo.GetDb()))
	if err != nil {
		return nil
	}
	if checkTurn.TidturnFirstDepositPercent > 0 {

		tidTurn := (bonusAmount * float64(checkTurn.TidturnManualBonusPercent) / 100)

		tidTurn = math.Ceil(tidTurn)

		createdTime := time.Now().UTC()
		var createBody model.TurnoverUserStatementCreateBody
		createBody.UserId = userId
		createBody.RefTypeId = turnoverRefId
		createBody.TypeId = model.TURN_BONUS_AFF_TYPE_FIRST_DEPOSIT
		createBody.Name = model.TURNOVER_CATE_AFF_TYPE_FIRST_DEPOSIT
		createBody.PromotionName = model.TURNOVER_CATE_AFF_TYPE_FIRST_DEPOSIT
		createBody.BonusAmount = bonusAmount
		createBody.StatusId = model.TURNOVER_STATEMENT_STATUS_PENDING
		createBody.StartTurnAmount = tidTurn
		createBody.StartTurnAt = &createdTime
		createBody.TotalTurnAmount = tidTurn
		if _, err := repo.CreateTurnoverUserStatement(createBody); err != nil {
			return err
		}
	} else {
		createdTime := time.Now().UTC()
		var createBody model.TurnoverUserStatementCreateBody
		createBody.UserId = userId
		createBody.RefTypeId = turnoverRefId
		createBody.TypeId = model.TURN_BONUS_AFF_TYPE_FIRST_DEPOSIT
		createBody.Name = model.TURNOVER_CATE_AFF_TYPE_FIRST_DEPOSIT
		createBody.PromotionName = model.TURNOVER_CATE_AFF_TYPE_FIRST_DEPOSIT
		createBody.BonusAmount = 0
		createBody.StatusId = model.TURNOVER_STATEMENT_STATUS_PENDING
		createBody.StartTurnAmount = 0
		createBody.StartTurnAt = &createdTime
		createBody.TotalTurnAmount = 0
		craeteTurnId, err := repo.CreateTurnoverUserStatement(createBody)
		if err != nil {
			return err
		}

		var updateTurnoverUserStatement model.TurnoverUserStatementUpdateBody
		setTotalTurnAmount := 0.0
		setTimeTurnAt := time.Now().UTC()
		updateTurnoverUserStatement.StatusId = model.TURNOVER_STATEMENT_STATUS_COMPLETED
		updateTurnoverUserStatement.TotalTurnAmount = &setTotalTurnAmount
		updateTurnoverUserStatement.EndTurnAt = &setTimeTurnAt
		if err := repo.UpdateTurnoverUserStatement(*craeteTurnId, updateTurnoverUserStatement); err != nil {
			log.Println("UpdateTurnoverUserStatement", err)
		}

		// create turnover withdraw log
		var createTurnoverWithDrawLog model.CreateTurnoverUserWithdrawLog
		createTurnoverWithDrawLog.UserId = userId
		createTurnoverWithDrawLog.LogKey = fmt.Sprintf("AFF_FIRST_DEPOSIT_U%d_D%s", userId, time.Now().UTC().Format("20060102150405"))
		createTurnoverWithDrawLog.TotalWithdrawPrice = 0
		createTurnoverWithDrawLog.CurrentTurn = 0
		createTurnoverWithDrawLog.PlayTotal = 0
		createTurnoverWithDrawLog.LastPlayY = 0
		createTurnoverWithDrawLog.LastTotalX = 0
		if _, err = repo.CreateTurnoverUserWithdrawLog(createTurnoverWithDrawLog); err != nil {
			log.Println("CheckCouponTurnOverWithdraw.CreateTurnoverUserWithdrawLog", err)
		}

	}

	return nil
}

func CreateTurnOverFromAffPlayCommission(repo repository.AffiliateRepository, userId int64, bonusAmount float64, turnoverRefId int64) error {

	checkTurn, err := GetTurnoverSetting(repository.NewTurnoverRepository(repo.GetDb()))
	if err != nil {
		return nil
	}

	if checkTurn.TidturnAffCommissionPercent > 0 {

		tidTurn := (bonusAmount * float64(checkTurn.TidturnManualBonusPercent) / 100)
		tidTurn = math.Ceil(tidTurn)

		createdTime := time.Now().UTC()
		var createBody model.TurnoverUserStatementCreateBody
		createBody.UserId = userId
		createBody.RefTypeId = turnoverRefId
		createBody.TypeId = model.TURN_BONUS_AFF_TYPE_PLAY_COMMISSION
		createBody.Name = model.TURNOVER_CATE_AFF_TYPE_PLAY_COMMISSION
		createBody.PromotionName = model.TURNOVER_CATE_AFF_TYPE_PLAY_COMMISSION
		createBody.BonusAmount = bonusAmount
		createBody.StatusId = model.TURNOVER_STATEMENT_STATUS_PENDING
		createBody.StartTurnAmount = tidTurn
		createBody.StartTurnAt = &createdTime
		createBody.TotalTurnAmount = tidTurn
		if _, err := repo.CreateTurnoverUserStatement(createBody); err != nil {
			return err
		}
	} else {
		createdTime := time.Now().UTC()
		var createBody model.TurnoverUserStatementCreateBody
		createBody.UserId = userId
		createBody.RefTypeId = turnoverRefId
		createBody.TypeId = model.TURN_BONUS_AFF_TYPE_PLAY_COMMISSION
		createBody.Name = model.TURNOVER_CATE_AFF_TYPE_PLAY_COMMISSION
		createBody.PromotionName = model.TURNOVER_CATE_AFF_TYPE_PLAY_COMMISSION
		createBody.BonusAmount = 0
		createBody.StatusId = model.TURNOVER_STATEMENT_STATUS_PENDING
		createBody.StartTurnAmount = 0
		createBody.StartTurnAt = &createdTime
		createBody.TotalTurnAmount = 0
		craeteTurnId, err := repo.CreateTurnoverUserStatement(createBody)
		if err != nil {
			return err
		}

		var updateTurnoverUserStatement model.TurnoverUserStatementUpdateBody
		setTotalTurnAmount := 0.0
		setTimeTurnAt := time.Now().UTC()
		updateTurnoverUserStatement.StatusId = model.TURNOVER_STATEMENT_STATUS_COMPLETED
		updateTurnoverUserStatement.TotalTurnAmount = &setTotalTurnAmount
		updateTurnoverUserStatement.EndTurnAt = &setTimeTurnAt
		if err := repo.UpdateTurnoverUserStatement(*craeteTurnId, updateTurnoverUserStatement); err != nil {
			log.Println("UpdateTurnoverUserStatement", err)
		}

		// create turnover withdraw log
		var createTurnoverWithDrawLog model.CreateTurnoverUserWithdrawLog
		createTurnoverWithDrawLog.UserId = userId
		createTurnoverWithDrawLog.LogKey = fmt.Sprintf("AFF_PLAY_COMMISSION_U%d_D%s", userId, time.Now().UTC().Format("20060102150405"))
		createTurnoverWithDrawLog.TotalWithdrawPrice = 0
		createTurnoverWithDrawLog.CurrentTurn = 0
		createTurnoverWithDrawLog.PlayTotal = 0
		createTurnoverWithDrawLog.LastPlayY = 0
		createTurnoverWithDrawLog.LastTotalX = 0
		if _, err = repo.CreateTurnoverUserWithdrawLog(createTurnoverWithDrawLog); err != nil {
			log.Println("CheckCouponTurnOverWithdraw.CreateTurnoverUserWithdrawLog", err)
		}
	}

	return nil
}

func (s AffiliateServiceRepos) GetAffiliateUserSummary(req model.AffiliateUserSummaryRequest) (*model.AffiliateUserSummaryResponse, error) {

	// เอาออกเลยไม่ cache แล้ว
	// if cacheAffiliateUserSummary != nil && cacheAffiliateUserSummary.CacheExpireAt.After(time.Now()) {
	// 	return cacheAffiliateUserSummary, nil
	// }

	result, err := s.repo.GetAffiliateUserSummary(req)
	if err != nil {
		return nil, err
	}

	// result.CacheExpireAt = time.Now().Add(1 * time.Minute)
	// cacheAffiliateUserSummary = result

	return result, nil
}

func (s AffiliateServiceRepos) GetAffiliateUserSummaryRealTime(req model.AffiliateUserSummaryRequest) (*model.AffiliateUserSummaryResponse, error) {

	// เอาออกเลยไม่ cache แล้ว
	// if cacheAffiliateUserSummary != nil && cacheAffiliateUserSummary.CacheExpireAt.After(time.Now()) {
	// 	return cacheAffiliateUserSummary, nil
	// }

	result, err := s.repo.GetAffiliateUserSummaryRealTime(req)
	if err != nil {
		return nil, err
	}

	// result.CacheExpireAt = time.Now().Add(1 * time.Minute)
	// cacheAffiliateUserSummary = result

	return result, nil
}

func (s AffiliateServiceRepos) GetAffTransactionListSummary(req model.AffTransactionListRequest) (*model.AffTransactionSummaryResponse, error) {

	result, err := s.repo.GetAffTransactionListSummary(req)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (s AffiliateServiceRepos) GetWebAffiliateCommissionDetail() (*model.GetWebAffiliateCommissionDetailResponse, error) {

	result, err := s.repo.GetWebAffiliateCommissionDetail()
	if err != nil {
		return nil, err
	}
	return result, nil
}
