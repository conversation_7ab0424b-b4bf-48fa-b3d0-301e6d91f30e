package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"fmt"
	"log"
	"net/http"
	"os"
)

type WebPopupService interface {
	GetWebPopupById(req model.GetByIdRequest) (*model.WebPopupResponse, error)
	GetWebPopupList(req model.WebPopupListRequest) (*model.SuccessWithPagination, error)
	CreateWebPopup(req model.WebPopupCreateRequest) (*int64, error)
	UpdateWebPopup(id int64, req model.WebPopupUpdateRequest) error
	SortOrderWebPopup(req model.DragSortRequest) error
	DeleteWebPopup(id int64) error
	UploadImageToCloudflareWebpop(imageFileBody *http.Request) (*model.FileUploadResponse, error)
	UploadImageToS3Webpop(imageFileBody *http.Request) (*model.FileUploadResponse, error)

	GetAllUploads() (*model.GetAllUploadsResponse, error)
}

type webPopupService struct {
	repo repository.WebPopupRepository
}

func NewWebPopupService(
	repo repository.WebPopupRepository,
) WebPopupService {
	return &webPopupService{repo}
}

func (s webPopupService) GetWebPopupById(req model.GetByIdRequest) (*model.WebPopupResponse, error) {

	record, err := s.repo.GetWebPopupById(req.Id)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(recordNotFound)
		}
		return nil, internalServerError(err)
	}
	return record, nil
}

func (s webPopupService) GetWebPopupList(req model.WebPopupListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	list, total, err := s.repo.GetWebPopupList(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	return &model.SuccessWithPagination{
		List:  list,
		Total: total,
	}, nil
}

func (s webPopupService) CreateWebPopup(req model.WebPopupCreateRequest) (*int64, error) {

	// [20240104] Max 20 records
	var query model.WebPopupListRequest
	query.Page = 1
	query.Limit = 1
	_, total, err := s.repo.GetWebPopupList(query)
	if err != nil {
		return nil, internalServerError(err)
	}
	if total >= 20 {
		return nil, badRequest("MAX_20_RECORDS")
	}

	var createBody model.WebPopupCreateBody
	createBody.ImgPath = req.ImgPath
	if req.IsShow != nil {
		createBody.IsShow = *req.IsShow
	}
	insertId, err := s.repo.CreateWebPopup(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}
	return insertId, nil
}

func (s webPopupService) UpdateWebPopup(id int64, req model.WebPopupUpdateRequest) error {

	if _, err := s.repo.GetWebPopupById(id); err != nil {
		return internalServerError(err)
	}

	var body model.WebPopupUpdateBody
	body.IsShow = req.IsShow
	if err := s.repo.UpdateWebPopup(id, body); err != nil {
		return internalServerError(err)
	}
	return nil
}

func (s webPopupService) SortOrderWebPopup(req model.DragSortRequest) error {

	// Drag Sort
	if err := s.repo.SortOrderWebPopup(req); err != nil {
		return internalServerError(err)
	}
	return nil
}

func (s webPopupService) DeleteWebPopup(id int64) error {

	detail, err := s.repo.GetWebPopupById(id)
	if err != nil {
		return internalServerError(err)
	}

	// DeleteUploadImages
	if detail.ImgPath != "" {
		if err := s.repo.DeleteUploadImages(detail.ImgPath); err != nil {
			return internalServerError(err)
		}
	}
	if err := s.repo.DeleteWebPopup(id); err != nil {
		return internalServerError(err)
	}
	return nil
}

func (s webPopupService) UploadImageToCloudflareWebpop(imageFileBody *http.Request) (*model.FileUploadResponse, error) {

	fileReader, newFileName, err := imageFileBody.FormFile("file")
	if err != nil {
		log.Println("UploadFileCloudFlare.ERROR", err)
		return nil, err
	}

	filename := &newFileName.Filename

	dbName := os.Getenv("DB_NAME")

	// [set imageCloudFlarePathName]
	pathName := fmt.Sprintf("cbgame/%v/web-popup/upload/image/", dbName)
	//! ส่ง Id กับไฟล์ reader
	var newImageId *model.FileUploadResponse
	fileData, err := s.repo.UploadImageToCloudflare(pathName, *filename, fileReader)
	if err != nil {
		log.Println("UploadFileCloudFlare.ERROR", err)
		return nil, badRequest("UNABLE_TO_UPLOAD_FILE")
	}

	newImageId = &model.FileUploadResponse{
		ImageUrl: fileData.FileUrl,
	}

	return newImageId, nil
}

func (s *webPopupService) UploadImageToS3Webpop(imageFileBody *http.Request) (*model.FileUploadResponse, error) {

	fileReader, newFileName, err := imageFileBody.FormFile("file")
	if err != nil {
		log.Println("UploadFileS3.ERROR", err)
		return nil, err
	}

	filename := &newFileName.Filename

	dbName := os.Getenv("DB_NAME")

	pathName := fmt.Sprintf("cbgame/%v/bank-account/", dbName)
	var newImageId *model.FileUploadResponse
	fileData, err := s.repo.UploadImageToS3(pathName, *filename, fileReader)
	if err != nil {
		log.Println("UploadFileS3.ERROR", err)
		return nil, badRequest("UNABLE_TO_UPLOAD_FILE")
	}

	newImageId = &model.FileUploadResponse{
		ImageUrl: fileData.ImageUrl,
	}

	return newImageId, nil
}

func (s webPopupService) GetAllUploads() (*model.GetAllUploadsResponse, error) {

	// uploads, err := s.repo.GetAllUploads()
	// if err != nil {
	// 	return nil, internalServerError(err)
	// }

	// DownloadAllSource() error
	err := s.repo.DownloadAllSource()
	if err != nil {
		return nil, internalServerError(err)
	}

	return nil, nil
}
