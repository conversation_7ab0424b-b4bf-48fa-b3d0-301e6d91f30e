package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"fmt"
	"log"
	"math"
	"time"
)

type NotificationService interface {
	// TelegramToken
	GetTelegramAccessToken() (*model.TelegramAccessTokenResponse, error)
	SetTelegramAccessToken(req model.TelegramAccessTokenUpdateRequest) error

	// notification options
	GetNotificationType() ([]model.ConfigurationNotificationType, error)

	// notification configuration
	GetConfigurationNotification() (*model.ConfigurationNotification, error)
	UpdateConfigurationNotification(req model.UpdateConfigurationNotificationRequest) error

	// line notification
	ExternalNotification(req model.NotifyExternalNotificationRequest) error
	// LineNotification(req model.NotifyLineRequest) error

	// new backoffice notification
	CreateConfigurationBackofficeNotification(body model.CreateConfigurationBackofficeNotificationBody) (*int64, error)
	GetConfigurationBackofficeNotification() (*model.GetConfigurationBackofficeNotificationResponse, error)

	// notification token
	CreateConfigurationNotificationToken(body []model.CreateConfigurationNotificationTokenBody) error
	UpdateConfigurationNotificationToken(body model.UpdateConfigurationNotificationTokenBody) error
	DeleteConfigurationNotificationToken(id int64) error
	GetConfigurationNotificationTokenById(id int64) (*model.GetConfigurationNotificationTokenResponse, error)
	GetConfigurationNotificationTokenList(req model.GetConfigurationNotificationTokenList) ([]model.GetConfigurationNotificationTokenResponse, error)

	// external notification
	CreateConfigurationExternalNotificationWithToken(req model.CreateConfigurationExternalNotificationRequest) (*int64, error)
	DeleteConfigurationExternalNotification(id int64) error
	GetConfigurationExternalNotificationList(req model.GetConfigurationExternalNotificationListRequest) (*model.SuccessWithPagination, error)
	GetConfigurationExternalNotificationById(id int64) (*model.GetConfigurationExternalNotificationByIdResponse, error)
	UpdateConfigurationExternalNotification(req model.UpdateConfigurationExternalNotificationRequest) error

	TestNotiMessage(token string, typeNoti string) error
	AutoCreateBackUpTokenInUse() error
	UpdateOldTokenToLine() error

	//clear cache
	ClearNotificationCache() error

	// summary
	GetUserNewMemberForNoti() ([]model.UserNewMemberCountResponse, error)

	// Cronjob
	GetTransactionDailySummaryReportNotification() (string, error)
	GetTransactionHourSummaryReportNotification() (string, error)
	GetAffiliateSummaryReportNotification() (string, error)
}

type notificationService struct {
	repo repository.NotificationRepository
}

func NewNotificationService(
	repo repository.NotificationRepository,
) NotificationService {
	return &notificationService{repo}
}

var cacheOldTokens = make(map[int64]model.CacheConfigurationNotificationToken)

func (s *notificationService) GetTelegramAccessToken() (*model.TelegramAccessTokenResponse, error) {

	token, err := s.repo.GetTelegramAccessToken()
	if err != nil {
		return nil, internalServerError(err)
	}
	return token, nil
}

func (s *notificationService) SetTelegramAccessToken(req model.TelegramAccessTokenUpdateRequest) error {

	if err := s.repo.UpdateTelegramAccessToken(req.AccessToken); err != nil {
		return internalServerError(err)
	}
	return nil
}

func (s *notificationService) GetNotificationType() ([]model.ConfigurationNotificationType, error) {

	options, err := s.repo.GetNotificationType()
	if err != nil {
		return nil, err
	}

	return options, nil
}

func (s *notificationService) GetConfigurationNotification() (*model.ConfigurationNotification, error) {

	configurationNotification, err := s.repo.GetConfigurationNotification()
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(recordNotFound)
		}
		return nil, internalServerError(err)
	}

	return configurationNotification, nil
}

func (s *notificationService) UpdateConfigurationNotification(req model.UpdateConfigurationNotificationRequest) error {

	record, err := s.repo.GetConfigurationNotification()
	if err != nil {
		_, err = s.repo.CreateConfigurationNotification(model.CreateConfigurationNotificationBody{
			CreditAbove:                     *req.CreditAbove,
			LineToken:                       *req.LineToken,
			IsMemberRegistration:            *req.IsMemberRegistration,
			IsDepositBeforeCredit:           *req.IsDepositBeforeCredit,
			IsDepositAfterCredit:            *req.IsDepositAfterCredit,
			IsWithdrawalCreditSuccess:       *req.IsWithdrawalCreditSuccess,
			IsWithdrawalAwaitingTransfer:    *req.IsWithdrawalAwaitingTransfer,
			IsWithdrawalCreditFailed:        *req.IsWithdrawalCreditFailed,
			IsDepositBonus:                  *req.IsDepositBonus,
			IsPullCreditBack:                *req.IsPullCreditBack,
			IsActitvityBeforeBonus:          *req.IsActitvityBeforeBonus,
			IsActitvityAfterBonus:           *req.IsActitvityAfterBonus,
			IsPromotionBonus:                *req.IsPromotionBonus,
			ConfigurationNotificationTypeId: *req.ConfigurationNotificationTypeId,
		})
		return err
	}

	if record != nil {
		var body model.UpdateConfigurationNotificationBody
		body.Id = &record.Id
		if req.CreditAbove != nil {
			body.CreditAbove = req.CreditAbove
		} else {
			body.CreditAbove = &record.CreditAbove
		}
		if req.LineToken != nil {
			body.LineToken = req.LineToken
		} else {
			body.LineToken = &record.LineToken
		}
		if req.IsMemberRegistration != nil {
			body.IsMemberRegistration = req.IsMemberRegistration
		} else {
			body.IsMemberRegistration = &record.IsMemberRegistration
		}
		if req.IsDepositBeforeCredit != nil {
			body.IsDepositBeforeCredit = req.IsDepositBeforeCredit
		} else {
			body.IsDepositBeforeCredit = &record.IsDepositBeforeCredit
		}
		if req.IsDepositAfterCredit != nil {
			body.IsDepositAfterCredit = req.IsDepositAfterCredit
		} else {
			body.IsDepositAfterCredit = &record.IsDepositAfterCredit
		}
		if req.IsWithdrawalCreditSuccess != nil {
			body.IsWithdrawalCreditSuccess = req.IsWithdrawalCreditSuccess
		} else {
			body.IsWithdrawalCreditSuccess = &record.IsWithdrawalCreditSuccess
		}
		if req.IsWithdrawalAwaitingTransfer != nil {
			body.IsWithdrawalAwaitingTransfer = req.IsWithdrawalAwaitingTransfer
		} else {
			body.IsWithdrawalAwaitingTransfer = &record.IsWithdrawalAwaitingTransfer
		}
		if req.IsWithdrawalCreditFailed != nil {
			body.IsWithdrawalCreditFailed = req.IsWithdrawalCreditFailed
		} else {
			body.IsWithdrawalCreditFailed = &record.IsWithdrawalCreditFailed
		}
		if req.ConfigurationNotificationTypeId != nil {
			body.ConfigurationNotificationTypeId = req.ConfigurationNotificationTypeId
		} else {
			body.ConfigurationNotificationTypeId = &record.ConfigurationNotificationTypeId
		}
		if req.IsDepositBonus != nil {
			body.IsDepositBonus = req.IsDepositBonus
		} else {
			body.IsDepositBonus = &record.IsDepositBonus
		}
		if req.IsPullCreditBack != nil {
			body.IsPullCreditBack = req.IsPullCreditBack
		} else {
			body.IsPullCreditBack = &record.IsPullCreditBack
		}
		if req.IsActitvityBeforeBonus != nil {
			body.IsActitvityBeforeBonus = req.IsActitvityBeforeBonus
		} else {
			body.IsActitvityBeforeBonus = &record.IsActitvityBeforeBonus
		}

		if req.IsActitvityAfterBonus != nil {
			body.IsActitvityAfterBonus = req.IsActitvityAfterBonus
		} else {
			body.IsActitvityAfterBonus = &record.IsActitvityAfterBonus
		}
		if req.IsPromotionBonus != nil {
			body.IsPromotionBonus = req.IsPromotionBonus
		} else {
			body.IsPromotionBonus = &record.IsPromotionBonus
		}

		err = s.repo.UpdateConfigurationNotification(body)
		if err != nil {
			return err
		}
	}

	return nil
}

func (s *notificationService) ExternalNotification(req model.NotifyExternalNotificationRequest) error {

	if err := ExternalNotification(s.repo, req); err != nil {
		return err
	}
	return nil
}

func ExternalNotification(repo repository.NotificationRepository, req model.NotifyExternalNotificationRequest) error {
	configWeb, err := repo.GetConfiguration()
	if err != nil {
		return nil
	}

	var adminName string
	if req.ConfirmedByAdminId != 0 {
		admin, _, _, _, err := repo.GetAdmin(req.ConfirmedByAdminId)
		if err != nil {
			return nil
		}
		if admin != nil {
			adminName = admin.Username
		}
	} else {
		adminName = "BOT_AUTO"
	}

	if req.TimerCounter == "" {
		req.TimerCounter = "1"
	}
	// get external notification
	// GetConfigurationNotificationToken() (*model.GetConfigurationNotificationTokenResponse, error)
	getExternalConfig, err := repo.GetConfigurationExternalNotification()
	if err != nil {
		return nil
	}

	if req.BonusCredit == nil {
		req.BonusCredit = new(float64)
		*req.BonusCredit = 0
	}
	if req.Amount != 0 {
		req.Amount = math.Round(float64(int(req.Amount*100))) / 100
	}
	if req.UserCredit != 0 {
		req.UserCredit = math.Round(req.UserCredit*100) / 100
	}

	getBackofficeNoti, _ := GetConfigurationBackofficeNotification(repo)
	if getBackofficeNoti != nil {
		if getBackofficeNoti.BackofficeIsOn {
			sentNoti := false
			if req.WebScoket.AlertType == "NEW_MEMBER" && getBackofficeNoti.BackofficeNewMember {
				sentNoti = true
			}
			if req.WebScoket.AlertType == "DEPOSIT" && getBackofficeNoti.BackofficeDeposit {
				sentNoti = true
			}
			if req.WebScoket.AlertType == "WITHDRAW" && getBackofficeNoti.BackofficeWithdraw {
				sentNoti = true
			}
			if req.WebScoket.AlertType == "PENDING_BONUS_ACTIVITY" && getBackofficeNoti.BackofficeBonus {
				sentNoti = true
			}

			if sentNoti {
				if err := repo.WebSocket(req.WebScoket); err != nil {
					log.Println("ExternalNotification.WebSocketWithdraw", err)
					return nil
				}
			}
		}
	}
	go func() {
		var getAllExternalToken []model.GetConfigurationNotificationTokenResponse
		if len(getExternalConfig) > 0 {
			for _, configNoti := range getExternalConfig {
				getAllExternalToken, err = GetConfigurationNotificationTokenByExternalId(repo, configNoti.Id)
				if err != nil {
					return
				}
				if len(getAllExternalToken) > 0 {
					for _, token := range getAllExternalToken {

						// sumAmount := req.Amount + *req.BonusCredit

						// if float64(record.CreditAbove) > sumAmount && req.TypeNotify != model.IsMemberRegistration {
						// 	return nil
						// }

						var notiMessage string
						var typeMessage string
						sentNotiLine := false
						sentNotiTelegram := false

						// line
						var userNewMember *model.UserNewMemberCountResponse
						if req.TypeNotify == model.IsMemberRegistration && configNoti.LineNewMember {
							notiMessage = model.IsMemberRegistrationMessage

							userNewMember, err = repo.GetUserNewMemberForNoti()
							if err != nil {
								log.Println("LineNotification.GetUserNewMemberForNoti", err)
							}
							typeMessage = "REGISTER_MESSAGE"

							sentNotiLine = true

						}
						if req.TypeNotify == model.IsDepositBeforeCredit && configNoti.LineBeforeDeposit {
							notiMessage = model.IsDepositBeforeCreditMessage
							typeMessage = "DEPOSIT_MESSAGE"
							sentNotiLine = true

						}
						if req.TypeNotify == model.IsDepositAfterCredit && configNoti.LineAfterDeposit {
							notiMessage = model.IsDepositAfterCreditMessage
							typeMessage = "DEPOSIT_MESSAGE_SUCESS"
							sentNotiLine = true

						}
						if req.TypeNotify == model.IsWithdrawalCreditSuccess && configNoti.LineWithdrawSuccess {
							notiMessage = model.IsWithdrawalCreditSuccessMessage
							typeMessage = "WITHDRAW_MESSAGE_SUCCESS"
							sentNotiLine = true

						}
						if req.TypeNotify == model.IsWithdrawalAwaitingTransfer && configNoti.LineWithdrawPending {
							notiMessage = model.IsWithdrawalAwaitingTransferMessage
							typeMessage = "WITHDRAW_MESSAGE"
							sentNotiLine = true

						}
						if req.TypeNotify == model.IsWithdrawalCreditFailed && configNoti.LineWithdrawFailed {
							notiMessage = model.IsWithdrawalCreditFailedMessage
							typeMessage = "WITHDRAW_MESSAGE"
							sentNotiLine = true

						}
						if req.TypeNotify == model.BounsNotification && configNoti.LineBonus {
							notiMessage = model.BounsNotificationMessage
							typeMessage = "BONUS_MESSAGE"
							sentNotiLine = true

						}
						if req.TypeNotify == model.PullCreditBackNotification && configNoti.LinePullCredit {
							notiMessage = model.PullCreditBackNotificationMessage
							typeMessage = "PULL_CREDIT_BACK_MESSAGE"
							sentNotiLine = true

						}
						if req.TypeNotify == model.ActitvityBeforeBonus && configNoti.LineActivityBeforeBonus {
							notiMessage = model.ActitvityBeforeBonusMessage
							typeMessage = "ACTIVITY_MESSAGE"
							sentNotiLine = true

						}
						if req.TypeNotify == model.ActitvityAfterBonus && configNoti.LineActivityAfterBonus {
							notiMessage = model.ActitvityAfterBonusMessage
							typeMessage = "ACTIVITY_MESSAGE"
							sentNotiLine = true

						}
						if req.TypeNotify == model.PromotionBonus && configNoti.LinePromotion {
							notiMessage = model.PromotionBonusMessage
							typeMessage = "PROMOTION_MESSAGE"
							sentNotiLine = true
						}

						if req.TypeNotify == model.MoveMoney && configNoti.LineMoveMoney {
							// notiMessage = model.MoveMoneyMessage
							typeMessage = "MOVE_MONEY_MESSAGE"
							sentNotiLine = true
						}
						if req.TypeNotify == model.TransactionHourSummary && configNoti.LineTransactionHourSummary {
							// notiMessage = model.TransactionHourSummaryMessage
							typeMessage = "TRANSACTION_HOUR_SUMMARY_MESSAGE"
							sentNotiLine = true
						}
						if req.TypeNotify == model.AffiliateDailySummary && configNoti.LineAffiliateDailySummary {
							// notiMessage = model.AffiliateDailySummaryMessage
							typeMessage = "AFFILIATE_DAILY_SUMMARY_MESSAGE"
							sentNotiLine = true
						}
						if req.TypeNotify == model.TransactionDailySummary && configNoti.LineTransactionDailySummary {
							// notiMessage = model.TransactionDailySummaryMessage
							typeMessage = "TRANSACTION_DAILY_SUMMARY_MESSAGE"
							sentNotiLine = true
						}

						// telegram
						if req.TypeNotify == model.IsMemberRegistration && configNoti.TelegramNewMember {
							notiMessage = model.IsMemberRegistrationMessage

							userNewMember, err = repo.GetUserNewMemberForNoti()
							if err != nil {
								log.Println("LineNotification.GetUserNewMemberForNoti", err)
							}

							typeMessage = "REGISTER_MESSAGE"
							sentNotiTelegram = true

						}
						if req.TypeNotify == model.IsDepositBeforeCredit && configNoti.TelegramBeforeDeposit {
							notiMessage = model.IsDepositBeforeCreditMessage
							typeMessage = "DEPOSIT_MESSAGE"
							sentNotiTelegram = true

						}
						if req.TypeNotify == model.IsDepositAfterCredit && configNoti.TelegramAfterDeposit {
							notiMessage = model.IsDepositAfterCreditMessage
							typeMessage = "DEPOSIT_MESSAGE_SUCESS"
							sentNotiTelegram = true

						}
						if req.TypeNotify == model.IsWithdrawalCreditSuccess && configNoti.TelegramWithdrawSuccess {
							notiMessage = model.IsWithdrawalCreditSuccessMessage
							typeMessage = "WITHDRAW_MESSAGE_SUCCESS"
							sentNotiTelegram = true

						}
						if req.TypeNotify == model.IsWithdrawalAwaitingTransfer && configNoti.TelegramWithdrawPending {
							notiMessage = model.IsWithdrawalAwaitingTransferMessage
							typeMessage = "WITHDRAW_MESSAGE"
							sentNotiTelegram = true

						}
						if req.TypeNotify == model.IsWithdrawalCreditFailed && configNoti.TelegramWithdrawFailed {
							notiMessage = model.IsWithdrawalCreditFailedMessage
							typeMessage = "WITHDRAW_MESSAGE"
							sentNotiTelegram = true

						}
						if req.TypeNotify == model.BounsNotification && configNoti.TelegramBonus {
							notiMessage = model.BounsNotificationMessage
							typeMessage = "BONUS_MESSAGE"
							sentNotiTelegram = true

						}

						if req.TypeNotify == model.PullCreditBackNotification && configNoti.TelegramPullCredit {
							notiMessage = model.PullCreditBackNotificationMessage
							typeMessage = "PULL_CREDIT_BACK_MESSAGE"
							sentNotiTelegram = true

						}

						if req.TypeNotify == model.ActitvityBeforeBonus && configNoti.TelegramActivityBeforeBonus {
							notiMessage = model.ActitvityBeforeBonusMessage
							typeMessage = "ACTIVITY_MESSAGE"
							sentNotiTelegram = true

						}

						if req.TypeNotify == model.ActitvityAfterBonus && configNoti.TelegramActivityAfterBonus {
							notiMessage = model.ActitvityAfterBonusMessage
							typeMessage = "ACTIVITY_MESSAGE"
							sentNotiTelegram = true

						}

						if req.TypeNotify == model.PromotionBonus && configNoti.TelegramPromotion {
							notiMessage = model.PromotionBonusMessage
							typeMessage = "PROMOTION_MESSAGE"
							sentNotiTelegram = true
						}

						if req.TypeNotify == model.MoveMoney && configNoti.TelegramMoveMoney {
							// notiMessage = model.MoveMoneyMessage
							typeMessage = "MOVE_MONEY_MESSAGE"
							sentNotiTelegram = true
						}
						if req.TypeNotify == model.TransactionHourSummary && configNoti.TelegramTransactionHourSummary {
							// notiMessage = model.TransactionHourSummaryMessage
							typeMessage = "TRANSACTION_HOUR_SUMMARY_MESSAGE"
							sentNotiTelegram = true
						}
						if req.TypeNotify == model.AffiliateDailySummary && configNoti.TelegramAffiliateDailySummary {
							// notiMessage = model.AffiliateDailySummaryMessage
							typeMessage = "AFFILIATE_DAILY_SUMMARY_MESSAGE"
							sentNotiTelegram = true
						}
						if req.TypeNotify == model.TransactionDailySummary && configNoti.TelegramTransactionDailySummary {
							// notiMessage = model.TransactionDailySummaryMessage
							typeMessage = "TRANSACTION_DAILY_SUMMARY_MESSAGE"
							sentNotiTelegram = true
						}

						message := ""
						if typeMessage == "DEPOSIT_MESSAGE" {
							if *req.BonusCredit != 0 {
								message = fmt.Sprintf("%s : ไอดี : %v \nจำนวนฝาก :%v \nจำนวนโบนัส :%v \nจำนวนคงเหลือ : %v \nทำรายการเรื่อง : %s \nทำรายการโดย : %s | %s วิ", configWeb.WebName, req.MemberCode, req.Amount, *req.BonusCredit, req.UserCredit, notiMessage, adminName, req.TimerCounter)
							} else {
								message = fmt.Sprintf("%s : ไอดี : %v \nจำนวนฝาก :%v \nจำนวนคงเหลือ : %v \nทำรายการเรื่อง : %s \nทำรายการโดย : %s | %s วิ", configWeb.WebName, req.MemberCode, req.Amount, req.UserCredit, notiMessage, adminName, req.TimerCounter)
							}
						}

						if typeMessage == "DEPOSIT_MESSAGE_SUCESS" {

							if *req.BonusCredit != 0 {
								message = fmt.Sprintf("%s : ไอดี : %v \nจำนวนฝาก :%v \nจำนวนโบนัส :%v \nจำนวนคงเหลือ : %v \nทำรายการเรื่อง : %s \nทำรายการโดย : %s | %s วิ \n เวลาโอน : %s \n เวลาทำรายการฝากสำเร็จ : %s", configWeb.WebName, req.MemberCode, req.Amount, *req.BonusCredit, req.UserCredit, notiMessage, adminName, req.TimerCounter, req.TransferDateTime, req.ActionTime)
							} else {
								message = fmt.Sprintf("%s : ไอดี : %v \nจำนวนฝาก :%v \nจำนวนคงเหลือ : %v \nทำรายการเรื่อง : %s \nทำรายการโดย : %s | %s วิ \n เวลาโอน : %s \n เวลาทำรายการฝากสำเร็จ : %s", configWeb.WebName, req.MemberCode, req.Amount, req.UserCredit, notiMessage, adminName, req.TimerCounter, req.TransferDateTime, req.ActionTime)
							}

							var isFirstTime bool
							if req.TransId != nil {
								isFirstTime, err = repo.GetFirstTimeDepositForNoti(*req.TransId)
								if err != nil {
									log.Println("ExternalNotification.GetFirstTimeDepositForNoti", err)
								}
							}

							if isFirstTime {
								message = fmt.Sprintf("%s \n รายการนี้ฝากเครั้งแรก", message)
							}

						}

						if typeMessage == "WITHDRAW_MESSAGE" {
							message = fmt.Sprintf("%s : ไอดี : %v \nจำนวนถอน :%v \nจำนวนคงเหลือ : %v \nทำรายการเรื่อง : %s \nทำรายการโดย : %s", configWeb.WebName, req.MemberCode, req.Amount, req.UserCredit, notiMessage, adminName)
						}

						if typeMessage == "WITHDRAW_MESSAGE_SUCCESS" {
							message = fmt.Sprintf("%s : ไอดี : %v \nจำนวนถอน :%v \nจำนวนคงเหลือ : %v \nทำรายการเรื่อง : %s \nทำรายการโดย : %s \nเวลาแจ้งถอน : %s \nเวลาทำรายการถอนสำเร็จ : %s", configWeb.WebName, req.MemberCode, req.Amount, req.UserCredit, notiMessage, adminName, req.TransferDateTime, req.ActionTime)
						}

						if typeMessage == "REGISTER_MESSAGE" {

							if req.RefType == "" || req.RefType == "สมัครเอง" {
								message = fmt.Sprintf("เบอร์โทร : %v \nทำรายการเรื่อง : %s \nสมัครใหม่แบบรับ member : %v \nสมัครใหม่ยังไม่เป็น member : %v \nรวมยูสสมัครวันนี้ : %v", req.Phone, notiMessage, userNewMember.UserWithoutMemberCode, userNewMember.UserWithMemberCode, userNewMember.Total)
							} else {
								if req.RefAlias == "" {
									message = fmt.Sprintf("เบอร์โทร : %v \nทำรายการเรื่อง : %s \nประเภทการสมัคร : %s \nสมัครต่อจาก member : %s \nสมัครต่อจาก ชื่อ : %s \nสมัครต่อจาก เบอร์โทร : %s \nสมัครใหม่แบบรับ member :  %v \nสมัครใหม่ยังไม่เป็น member : %v \nรวมยูสสมัครวันนี้ : %v", req.Phone, notiMessage, req.RefType, req.RefMemberCode, req.RefUsername, req.RefPhone, userNewMember.UserWithoutMemberCode, userNewMember.UserWithMemberCode, userNewMember.Total)
								} else {
									message = fmt.Sprintf("เบอร์โทร : %v \nทำรายการเรื่อง : %s \nประเภทการสมัคร : %s \nสมัครต่อจาก member : %s \nสมัครต่อจาก ชื่อ : %s \nสมัครต่อจาก เบอร์โทร : %s \nสมัครต่อจาก นามแฝง : %s \nสมัครใหม่แบบรับ member :  %v \nสมัครใหม่ยังไม่เป็น member : %v \nรวมยูสสมัครวันนี้ : %v", req.Phone, notiMessage, req.RefType, req.RefMemberCode, req.RefUsername, req.RefPhone, req.RefAlias, userNewMember.UserWithoutMemberCode, userNewMember.UserWithMemberCode, userNewMember.Total)
								}
							}

							var AmountFirstTimeDeposit int64
							AmountFirstTimeDeposit, err = repo.GetFirstTimeDepositATodayAmountForNoti()
							if err != nil {
								log.Println("ExternalNotification.GetFirstTimeDepositATodayAmountForNoti", err)
							}

							if AmountFirstTimeDeposit != 0 {
								message = fmt.Sprintf("%s \n จำนวนยอดฝากครั้งแรก : %v", message, AmountFirstTimeDeposit)
							}

						}

						if typeMessage == "BONUS_MESSAGE" {
							message = fmt.Sprintf("%s : ไอดี : %v \nจำนวนโบนัส :%v \nจำนวนคงเหลือ : %v \nทำรายการเรื่อง : %s \nทำรายการโดย : %s", configWeb.WebName, req.MemberCode, *req.BonusCredit, req.UserCredit, notiMessage, adminName)
						}

						if typeMessage == "PULL_CREDIT_BACK_MESSAGE" {
							message = fmt.Sprintf("%s : ไอดี : %v \nจำนวนถอน :%v \nจำนวนคงเหลือ : %v \nทำรายการเรื่อง : %s \nทำรายการโดย : %s", configWeb.WebName, req.MemberCode, req.Amount, req.UserCredit, notiMessage, adminName)
						}

						if typeMessage == "ACTIVITY_MESSAGE" {
							message = fmt.Sprintf("%s : ไอดี : %v \nได้รับ : %v \nจำนวนคงเหลือ : %v \nทำรายการเรื่อง : %s \nทำรายการโดย : %s", configWeb.WebName, req.MemberCode, *req.BonusCredit, req.UserCredit, notiMessage, adminName)
						}

						if typeMessage == "PROMOTION_MESSAGE" {
							message = fmt.Sprintf("%s : ไอดี : %v \nได้รับ : %v \nทำรายการเรื่อง : %s \nทำรายการโดย : %s", configWeb.WebName, req.MemberCode, req.UserCredit, notiMessage, adminName)
						}

						if typeMessage == "MOVE_MONEY_MESSAGE" {
							if req.TransId != nil {
								moveMoney, err := repo.GetAccountMoveTransactionDetail(*req.TransId)
								if err != nil {
									log.Println("ExternalNotification.GetAccountMoveTransactionDetail", err)
								} else {
									message = fmt.Sprintf("======รายการโยกเงิน=======\nเวลา : %s \nจำนวนเงิน : %v \nจาก บ/ช : %s - %s (%s) \nเข้า บ/ช : %s - %s (%s) \nผู้ทำรายการ : %s \nหมายเหตุ : %s \nสถานะ : %s", moveMoney.TransferAt.Add(7*time.Hour).Format("2006-01-02 15:04:05"), moveMoney.Amount, moveMoney.FromBankCode, moveMoney.FromAccountNumber, moveMoney.FromAccountName, moveMoney.ToBankCode, moveMoney.ToAccountNumber, moveMoney.ToAccountName, moveMoney.AdminName, moveMoney.Remark, moveMoney.StatusName)
								}
							}
						}

						if typeMessage == "TRANSACTION_HOUR_SUMMARY_MESSAGE" {
							message = req.CronjobMessage
						}

						if typeMessage == "AFFILIATE_DAILY_SUMMARY_MESSAGE" {
							message = req.CronjobMessage
						}

						if typeMessage == "TRANSACTION_DAILY_SUMMARY_MESSAGE" {
							message = req.CronjobMessage
						}

						//set sent token
						if token.Token != "" {
							if sentNotiLine {
								if token.ConfigurationTokenTypeId == model.NOTI_TOKEN_TYPE_LINE && configNoti.LineIsOn {
									if err = repo.SendLineNotification(message, token.Token); err != nil {
										log.Println("ExternalNotification.SendLineNotification", err)
									}
								}
							}
							// telegram
							if sentNotiTelegram {
								if token.ConfigurationTokenTypeId == model.NOTI_TOKEN_TYPE_TELEGRAM && configNoti.TelegramIsOn {
									chatInfo, err := repo.GetTelegramChatTokenByToken(token.Token)
									if err != nil {
										log.Println("ExternalNotification.GetTelegramChatTokenByToken", err)
									} else if chatInfo != nil {
										if _, err = repo.SendTelegramMessage(*chatInfo, message); err != nil {
											log.Println("ExternalNotification.SendTelegramMessage", err)
										}
									} else {
										log.Println("ExternalNotification.GetTelegramChatTokenByToken chatInfo is nil")
									}
								}
							}
						}

					}
				}
			}
		}
	}()
	return nil
}

func (s *notificationService) TestNotiMessage(token string, typeNoti string) error {

	message := fmt.Sprintf("ทดสอบการส่งข้อความแจ้งเตือน จากเว็บไซต์ ไปที่ %s ของ token %s", typeNoti, token)

	if typeNoti == "TELEGRAM" {
		chatInfo, err := s.repo.GetTelegramChatTokenByToken(token)
		if err != nil {
			log.Println("TestNotiMessage.GetTelegramChatTokenByToken", err)
			return err
		}
		if chatInfo != nil {
			if _, err = s.repo.SendTelegramMessage(*chatInfo, message); err != nil {
				log.Println("TestNotiMessage.SendTelegramMessage", err)
				return err
			}
		} else {
			log.Println("TestNotiMessage.GetTelegramChatTokenByToken chatInfo is nil")
			return fmt.Errorf("CHAT_NOT_FOUND")
		}
	}
	if typeNoti == "LINE" {
		err := s.repo.SendLineNotification(message, token)
		if err != nil {
			return err
		}
	}
	return nil
}

func (s *notificationService) CreateConfigurationBackofficeNotification(body model.CreateConfigurationBackofficeNotificationBody) (*int64, error) {

	record, err := s.repo.GetConfigurationBackofficeNotification()
	if err != nil {
		_, err = s.repo.CreateConfigurationBackofficeNotification(model.CreateConfigurationBackofficeNotificationBody{
			BackofficeIsOn:             body.BackofficeIsOn,
			BackofficeNewMember:        body.BackofficeNewMember,
			BackofficeDeposit:          body.BackofficeDeposit,
			BackofficeWithdraw:         body.BackofficeWithdraw,
			BackofficeBonus:            body.BackofficeBonus,
			BackofficeSoundOnNewMember: body.BackofficeSoundOnNewMember,
			BackofficeSoundOnDeposit:   body.BackofficeSoundOnDeposit,
			BackofficeSoundOnWithdraw:  body.BackofficeSoundOnWithdraw,
			BackofficeSoundOnBonus:     body.BackofficeSoundOnBonus,
		})
		return nil, err
	}

	if record != nil {
		var updateBody model.UpdateConfigurationBackofficeNotificationBody
		updateBody.Id = &record.Id
		updateBody.BackofficeBonus = body.BackofficeBonus
		updateBody.BackofficeDeposit = body.BackofficeDeposit
		updateBody.BackofficeIsOn = body.BackofficeIsOn
		updateBody.BackofficeNewMember = body.BackofficeNewMember
		updateBody.BackofficeWithdraw = body.BackofficeWithdraw
		updateBody.BackofficeSoundOnNewMember = body.BackofficeSoundOnNewMember
		updateBody.BackofficeSoundOnDeposit = body.BackofficeSoundOnDeposit
		updateBody.BackofficeSoundOnWithdraw = body.BackofficeSoundOnWithdraw
		updateBody.BackofficeSoundOnBonus = body.BackofficeSoundOnBonus

		err = s.repo.UpdateConfigurationBackofficeNotification(updateBody)
		if err != nil {
			return nil, err
		}
	}

	return nil, nil
}

func (s *notificationService) GetConfigurationBackofficeNotification() (*model.GetConfigurationBackofficeNotificationResponse, error) {

	configurationResponse, err := GetConfigurationBackofficeNotification(s.repo)
	if err != nil {
		return nil, internalServerError(err)
	}

	return configurationResponse, nil
}

func GetConfigurationBackofficeNotification(sharedbRepo repository.NotificationRepository) (*model.GetConfigurationBackofficeNotificationResponse, error) {

	configurationResponse, err := sharedbRepo.GetConfigurationBackofficeNotification()
	if err != nil {
		if err.Error() == recordNotFound {
			//create
			var createBody model.CreateConfigurationBackofficeNotificationBody
			setalltrue := true
			createBody.BackofficeIsOn = &setalltrue
			createBody.BackofficeNewMember = &setalltrue
			createBody.BackofficeDeposit = &setalltrue
			createBody.BackofficeWithdraw = &setalltrue
			createBody.BackofficeBonus = &setalltrue
			createBody.BackofficeSoundOnNewMember = &setalltrue
			createBody.BackofficeSoundOnDeposit = &setalltrue
			createBody.BackofficeSoundOnWithdraw = &setalltrue
			createBody.BackofficeSoundOnBonus = &setalltrue

			getSetCreateBody, err := sharedbRepo.CreateConfigurationBackofficeNotification(createBody)
			if err != nil {
				return nil, err
			}
			var response model.GetConfigurationBackofficeNotificationResponse
			response.Id = *getSetCreateBody
			response.BackofficeIsOn = *createBody.BackofficeIsOn
			response.BackofficeNewMember = *createBody.BackofficeNewMember
			response.BackofficeDeposit = *createBody.BackofficeDeposit
			response.BackofficeWithdraw = *createBody.BackofficeWithdraw
			response.BackofficeBonus = *createBody.BackofficeBonus
			response.BackofficeSoundOnNewMember = *createBody.BackofficeSoundOnNewMember
			response.BackofficeSoundOnDeposit = *createBody.BackofficeSoundOnDeposit
			response.BackofficeSoundOnWithdraw = *createBody.BackofficeSoundOnWithdraw
			response.BackofficeSoundOnBonus = *createBody.BackofficeSoundOnBonus

			return &response, nil
		}
		return nil, internalServerError(err)
	}

	return configurationResponse, nil
}

func (s *notificationService) CreateConfigurationNotificationToken(body []model.CreateConfigurationNotificationTokenBody) error {
	err := s.repo.CreateConfigurationNotificationToken(body)
	if err != nil {
		return err
	}

	cacheOldTokens = make(map[int64]model.CacheConfigurationNotificationToken)

	return nil
}

func (s *notificationService) UpdateConfigurationNotificationToken(body model.UpdateConfigurationNotificationTokenBody) error {
	err := s.repo.UpdateConfigurationNotificationToken(body)
	if err != nil {
		return err
	}

	cacheOldTokens = make(map[int64]model.CacheConfigurationNotificationToken)
	return nil
}

func (s *notificationService) DeleteConfigurationNotificationToken(id int64) error {
	err := s.repo.DeleteConfigurationNotificationToken(id)
	if err != nil {
		return err
	}
	cacheOldTokens = make(map[int64]model.CacheConfigurationNotificationToken)

	return nil
}

func (s *notificationService) GetConfigurationNotificationTokenById(id int64) (*model.GetConfigurationNotificationTokenResponse, error) {

	configurationResponse, err := GetConfigurationNotificationTokenById(s.repo, id)
	if err != nil {
		return nil, internalServerError(err)
	}

	return configurationResponse, nil
}
func GetConfigurationNotificationTokenById(sharedbRepo repository.NotificationRepository, id int64) (*model.GetConfigurationNotificationTokenResponse, error) {

	configurationResponse, err := sharedbRepo.GetConfigurationNotificationTokenById(id)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(recordNotFound)
		}
		return nil, internalServerError(err)
	}

	return configurationResponse, nil
}

func (s *notificationService) GetConfigurationNotificationTokenByExternalId(externalId int64) ([]model.GetConfigurationNotificationTokenResponse, error) {

	configurationResponse, err := GetConfigurationNotificationTokenByExternalId(s.repo, externalId)
	if err != nil {
		return nil, internalServerError(err)
	}

	return configurationResponse, nil
}

func GetConfigurationNotificationTokenByExternalId(sharedbRepo repository.NotificationRepository, externalId int64) ([]model.GetConfigurationNotificationTokenResponse, error) {
	var configurationResponse []model.GetConfigurationNotificationTokenResponse
	var err error

	cachedToken, ok := cacheOldTokens[externalId]
	if ok {
		for _, token := range cachedToken.Result {
			configurationResponse = append(configurationResponse, model.GetConfigurationNotificationTokenResponse{
				Id:                                  token.Id,
				ConfigurationExternalNotificationId: token.ConfigurationExternalNotificationId,
				Token:                               token.Token,
				ConfigurationTokenTypeId:            token.ConfigurationTokenTypeId,
			})
		}
		return configurationResponse, nil
	}

	configurationResponse, err = sharedbRepo.GetConfigurationNotificationTokenByExternalId(externalId)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(recordNotFound)
		}
		return nil, internalServerError(err)
	}

	// set cache
	cacheOldTokens[externalId] = model.CacheConfigurationNotificationToken{
		Result: configurationResponse,
	}

	return configurationResponse, nil
}

func (s *notificationService) GetConfigurationNotificationTokenList(req model.GetConfigurationNotificationTokenList) ([]model.GetConfigurationNotificationTokenResponse, error) {

	configurationResponse, err := s.repo.GetConfigurationNotificationTokenList(req)
	if err != nil {
		return nil, internalServerError(err)
	}

	return configurationResponse, nil
}

func (s *notificationService) CreateConfigurationExternalNotificationWithToken(req model.CreateConfigurationExternalNotificationRequest) (*int64, error) {

	cacheOldTokens = make(map[int64]model.CacheConfigurationNotificationToken)

	var body model.CreateConfigurationExternalNotificationBody
	body.NotificationName = req.NotificationName
	body.TelegramIsOn = req.TelegramIsOn
	body.TelegramNewMember = req.TelegramNewMember
	body.TelegramBeforeDeposit = req.TelegramBeforeDeposit
	body.TelegramAfterDeposit = req.TelegramAfterDeposit
	body.TelegramWithdrawSuccess = req.TelegramWithdrawSuccess
	body.TelegramWithdrawPending = req.TelegramWithdrawPending
	body.TelegramWithdrawFailed = req.TelegramWithdrawFailed
	body.TelegramPullCredit = req.TelegramPullCredit
	body.TelegramBonus = req.TelegramBonus
	body.TelegramPromotion = req.TelegramPromotion
	body.TelegramActivityBeforeBonus = req.TelegramActivityBeforeBonus
	body.TelegramActivityAfterBonus = req.TelegramActivityAfterBonus
	body.LineIsOn = req.LineIsOn
	body.LineNewMember = req.LineNewMember
	body.LineBeforeDeposit = req.LineBeforeDeposit
	body.LineAfterDeposit = req.LineAfterDeposit
	body.LineWithdrawSuccess = req.LineWithdrawSuccess
	body.LineWithdrawPending = req.LineWithdrawPending
	body.LineWithdrawFailed = req.LineWithdrawFailed
	body.LinePullCredit = req.LinePullCredit
	body.LineBonus = req.LineBonus
	body.LinePromotion = req.LinePromotion
	body.LineActivityBeforeBonus = req.LineActivityBeforeBonus
	body.LineActivityAfterBonus = req.LineActivityAfterBonus

	body.TelegramMoveMoney = req.TelegramMoveMoney
	body.LineMoveMoney = req.LineMoveMoney
	body.TelegramTransactionHourSummary = req.TelegramTransactionHourSummary
	body.LineTransactionHourSummary = req.LineTransactionHourSummary
	body.TelegramAffiliateDailySummary = req.TelegramAffiliateDailySummary
	body.LineAffiliateDailySummary = req.LineAffiliateDailySummary
	body.TelegramTransactionDailySummary = req.TelegramTransactionDailySummary
	body.LineTransactionDailySummary = req.LineTransactionDailySummary

	insertId, err := s.repo.CreateConfigurationExternalNotification(body)
	if err != nil {
		return nil, err
	}

	// create token
	var tokenBody []model.CreateConfigurationNotificationTokenBody
	for _, token := range req.Tokens {
		var tokenBodyItem model.CreateConfigurationNotificationTokenBody
		tokenBodyItem.ConfigurationExternalNotificationId = *insertId
		tokenBodyItem.Token = token.Token
		tokenBodyItem.ConfigurationTokenTypeId = token.ConfigurationTokenTypeId
		tokenBody = append(tokenBody, tokenBodyItem)
	}

	if len(tokenBody) > 0 {
		err = s.repo.CreateConfigurationNotificationToken(tokenBody)
		if err != nil {
			return nil, err
		}
	}

	return insertId, nil
}

func (s *notificationService) DeleteConfigurationExternalNotification(id int64) error {

	cacheOldTokens = make(map[int64]model.CacheConfigurationNotificationToken)
	err := s.repo.DeleteConfigurationExternalNotification(id)
	if err != nil {
		return err
	}

	err = s.repo.DeleteConfigurationNotificationTokenWithConfigId(id)
	if err != nil {
		return err
	}

	return nil
}

func (s *notificationService) GetConfigurationExternalNotificationList(req model.GetConfigurationExternalNotificationListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	configurationResponse, total, err := s.repo.GetConfigurationExternalNotificationList(req)
	if err != nil {
		return nil, internalServerError(err)
	}

	var response model.SuccessWithPagination
	response.Message = "success"
	response.List = configurationResponse
	response.Total = total

	return &response, nil
}

func (s *notificationService) GetConfigurationExternalNotificationById(id int64) (*model.GetConfigurationExternalNotificationByIdResponse, error) {

	configurationResponse, err := s.repo.GetConfigurationExternalNotificationById(id)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(recordNotFound)
		}
		return nil, internalServerError(err)
	}

	var reqToken model.GetConfigurationNotificationTokenList
	reqToken.ConfigurationExternalNotificationId = &configurationResponse.Id
	tokenList, err := s.GetConfigurationNotificationTokenList(reqToken)
	if err != nil {
		return nil, internalServerError(err)
	}

	var response model.GetConfigurationExternalNotificationByIdResponse
	response.Id = configurationResponse.Id
	response.NotificationName = configurationResponse.NotificationName
	response.TelegramIsOn = configurationResponse.TelegramIsOn
	response.TelegramNewMember = configurationResponse.TelegramNewMember
	response.TelegramBeforeDeposit = configurationResponse.TelegramBeforeDeposit
	response.TelegramAfterDeposit = configurationResponse.TelegramAfterDeposit
	response.TelegramWithdrawSuccess = configurationResponse.TelegramWithdrawSuccess
	response.TelegramWithdrawPending = configurationResponse.TelegramWithdrawPending
	response.TelegramWithdrawFailed = configurationResponse.TelegramWithdrawFailed
	response.TelegramPullCredit = configurationResponse.TelegramPullCredit
	response.TelegramBonus = configurationResponse.TelegramBonus
	response.TelegramPromotion = configurationResponse.TelegramPromotion
	response.TelegramActivityBeforeBonus = configurationResponse.TelegramActivityBeforeBonus
	response.TelegramActivityAfterBonus = configurationResponse.TelegramActivityAfterBonus
	response.LineIsOn = configurationResponse.LineIsOn
	response.LineNewMember = configurationResponse.LineNewMember
	response.LineBeforeDeposit = configurationResponse.LineBeforeDeposit
	response.LineAfterDeposit = configurationResponse.LineAfterDeposit
	response.LineWithdrawSuccess = configurationResponse.LineWithdrawSuccess
	response.LineWithdrawPending = configurationResponse.LineWithdrawPending
	response.LineWithdrawFailed = configurationResponse.LineWithdrawFailed
	response.LinePullCredit = configurationResponse.LinePullCredit
	response.LineBonus = configurationResponse.LineBonus
	response.LinePromotion = configurationResponse.LinePromotion
	response.LineActivityBeforeBonus = configurationResponse.LineActivityBeforeBonus
	response.LineActivityAfterBonus = configurationResponse.LineActivityAfterBonus

	response.TelegramMoveMoney = configurationResponse.TelegramMoveMoney
	response.LineMoveMoney = configurationResponse.LineMoveMoney
	response.TelegramTransactionHourSummary = configurationResponse.TelegramTransactionHourSummary
	response.LineTransactionHourSummary = configurationResponse.LineTransactionHourSummary
	response.TelegramAffiliateDailySummary = configurationResponse.TelegramAffiliateDailySummary
	response.LineAffiliateDailySummary = configurationResponse.LineAffiliateDailySummary
	response.TelegramTransactionDailySummary = configurationResponse.TelegramTransactionDailySummary
	response.LineTransactionDailySummary = configurationResponse.LineTransactionDailySummary

	response.Tokens = tokenList

	return &response, nil
}

func (s *notificationService) UpdateConfigurationExternalNotification(req model.UpdateConfigurationExternalNotificationRequest) error {

	cacheOldTokens = make(map[int64]model.CacheConfigurationNotificationToken)

	// update external notification
	var body model.UpdateConfigurationExternalNotificationBody
	body.Id = req.Id
	if req.NotificationName != nil {
		body.NotificationName = req.NotificationName
	}
	if req.TelegramIsOn != nil {
		body.TelegramIsOn = req.TelegramIsOn
	}
	if req.TelegramNewMember != nil {
		body.TelegramNewMember = req.TelegramNewMember
	}
	if req.TelegramBeforeDeposit != nil {
		body.TelegramBeforeDeposit = req.TelegramBeforeDeposit
	}
	if req.TelegramAfterDeposit != nil {
		body.TelegramAfterDeposit = req.TelegramAfterDeposit
	}
	if req.TelegramWithdrawSuccess != nil {
		body.TelegramWithdrawSuccess = req.TelegramWithdrawSuccess
	}
	if req.TelegramWithdrawPending != nil {
		body.TelegramWithdrawPending = req.TelegramWithdrawPending
	}
	if req.TelegramWithdrawFailed != nil {
		body.TelegramWithdrawFailed = req.TelegramWithdrawFailed
	}
	if req.TelegramPullCredit != nil {
		body.TelegramPullCredit = req.TelegramPullCredit
	}
	if req.TelegramBonus != nil {
		body.TelegramBonus = req.TelegramBonus
	}
	if req.TelegramPromotion != nil {
		body.TelegramPromotion = req.TelegramPromotion
	}
	if req.TelegramActivityBeforeBonus != nil {
		body.TelegramActivityBeforeBonus = req.TelegramActivityBeforeBonus
	}
	if req.TelegramActivityAfterBonus != nil {
		body.TelegramActivityAfterBonus = req.TelegramActivityAfterBonus
	}
	if req.LineIsOn != nil {
		body.LineIsOn = req.LineIsOn
	}
	if req.LineNewMember != nil {
		body.LineNewMember = req.LineNewMember
	}
	if req.LineBeforeDeposit != nil {
		body.LineBeforeDeposit = req.LineBeforeDeposit
	}
	if req.LineAfterDeposit != nil {
		body.LineAfterDeposit = req.LineAfterDeposit
	}
	if req.LineWithdrawSuccess != nil {
		body.LineWithdrawSuccess = req.LineWithdrawSuccess
	}
	if req.LineWithdrawPending != nil {
		body.LineWithdrawPending = req.LineWithdrawPending
	}
	if req.LineWithdrawFailed != nil {
		body.LineWithdrawFailed = req.LineWithdrawFailed
	}
	if req.LinePullCredit != nil {
		body.LinePullCredit = req.LinePullCredit
	}
	if req.LineBonus != nil {
		body.LineBonus = req.LineBonus
	}
	if req.LinePromotion != nil {
		body.LinePromotion = req.LinePromotion
	}
	if req.LineActivityBeforeBonus != nil {
		body.LineActivityBeforeBonus = req.LineActivityBeforeBonus
	}
	if req.LineActivityAfterBonus != nil {
		body.LineActivityAfterBonus = req.LineActivityAfterBonus
	}

	if req.TelegramMoveMoney != nil {
		body.TelegramMoveMoney = req.TelegramMoveMoney
	}
	if req.LineMoveMoney != nil {
		body.LineMoveMoney = req.LineMoveMoney
	}
	if req.TelegramTransactionHourSummary != nil {
		body.TelegramTransactionHourSummary = req.TelegramTransactionHourSummary
	}
	if req.LineTransactionHourSummary != nil {
		body.LineTransactionHourSummary = req.LineTransactionHourSummary
	}
	if req.TelegramAffiliateDailySummary != nil {
		body.TelegramAffiliateDailySummary = req.TelegramAffiliateDailySummary
	}
	if req.LineAffiliateDailySummary != nil {
		body.LineAffiliateDailySummary = req.LineAffiliateDailySummary
	}
	if req.TelegramTransactionDailySummary != nil {
		body.TelegramTransactionDailySummary = req.TelegramTransactionDailySummary
	}
	if req.LineTransactionDailySummary != nil {
		body.LineTransactionDailySummary = req.LineTransactionDailySummary
	}

	err := s.repo.UpdateConfigurationExternalNotification(body)
	if err != nil {
		return err
	}

	// update token if exist token and create token if not exist token
	// [change][front end ไม่อยาก ส่ง id มา update]
	// if len(req.Tokens) > 0 {
	// 	var tokenBody []model.CreateConfigurationNotificationTokenBody
	// 	for _, token := range req.Tokens {
	// 		if token.Id != nil {
	// 			var updateTokenBody model.UpdateConfigurationNotificationTokenBody
	// 			updateTokenBody.ConfigurationExternalNotificationId = &req.Id
	// 			if token.Token != nil {
	// 				updateTokenBody.Token = token.Token
	// 			}
	// 			if token.ConfigurationTokenTypeId != nil {
	// 				updateTokenBody.ConfigurationTokenTypeId = &req.Id
	// 			}
	// 			updateTokenBody.Id = *token.Id

	// 			err = s.repo.UpdateConfigurationNotificationToken(updateTokenBody)
	// 			if err != nil {
	// 				return err
	// 			}
	// 		} else {
	// 			var tokenBodyItem model.CreateConfigurationNotificationTokenBody
	// 			tokenBodyItem.ConfigurationExternalNotificationId = req.Id
	// 			tokenBodyItem.Token = *token.Token
	// 			tokenBodyItem.ConfigurationTokenTypeId = req.Id
	// 			tokenBody = append(tokenBody, tokenBodyItem)
	// 		}
	// 	}

	// 	if len(tokenBody) > 0 {
	// 		err = s.repo.CreateConfigurationNotificationToken(tokenBody)
	// 		if err != nil {
	// 			return err
	// 		}
	// 	}
	// }

	err = s.repo.DeleteConfigurationNotificationTokenWithConfigId(req.Id)
	if err != nil {
		return err
	}

	var createTokenBody []model.CreateConfigurationNotificationTokenBody
	if len(req.Tokens) > 0 {
		for _, token := range req.Tokens {
			var tokenBodyItem model.CreateConfigurationNotificationTokenBody
			tokenBodyItem.Token = *token.Token
			tokenBodyItem.ConfigurationTokenTypeId = *token.ConfigurationTokenTypeId
			tokenBodyItem.ConfigurationExternalNotificationId = req.Id

			createTokenBody = append(createTokenBody, tokenBodyItem)
		}
	}

	if len(createTokenBody) > 0 {
		err = s.repo.CreateConfigurationNotificationToken(createTokenBody)
		if err != nil {
			return err
		}
	}

	return nil
}

func (s *notificationService) AutoCreateBackUpTokenInUse() error {
	// get old notification
	oldNotification, err := s.repo.AutoCreateBackUpTokenInUse()
	if err != nil {
		return err
	}

	// GetConfigurationExternalNotification() ([]model.GetConfigurationExternalNotificationByIdBody, error)
	getNowNotification, err := s.repo.GetConfigurationExternalNotificationInternal()
	if err != nil {
		return err
	}

	if len(getNowNotification) > 0 {
		return nil
	}

	if oldNotification.LineToken != "" {
		var body model.CreateConfigurationExternalNotificationRequest

		body.NotificationName = "BACUP_OLDNOTI"
		body.TelegramIsOn = false
		body.TelegramNewMember = false
		body.TelegramBeforeDeposit = false
		body.TelegramAfterDeposit = false
		body.TelegramWithdrawSuccess = false
		body.TelegramWithdrawPending = false
		body.TelegramWithdrawFailed = false
		body.TelegramPullCredit = false
		body.TelegramBonus = false
		body.TelegramPromotion = false
		body.TelegramActivityBeforeBonus = false
		body.TelegramActivityAfterBonus = false
		body.LineIsOn = true
		body.LineNewMember = true
		body.LineBeforeDeposit = true
		body.LineAfterDeposit = true
		body.LineWithdrawSuccess = true
		body.LineWithdrawPending = true
		body.LineWithdrawFailed = true
		body.LinePullCredit = true
		body.LineBonus = true
		body.LinePromotion = true
		body.LineActivityBeforeBonus = true
		body.LineActivityAfterBonus = true
		body.Tokens = []model.CreateConfigurationNotificationTokenBody{
			{
				Token:                    oldNotification.LineToken,
				ConfigurationTokenTypeId: model.NOTI_TOKEN_TYPE_LINE,
			},
		}

		_, err = s.CreateConfigurationExternalNotificationWithToken(body)
		if err != nil {
			return err
		}

	}

	return nil
}

func (s *notificationService) UpdateOldTokenToLine() error {
	oldNotification, err := s.repo.AutoCreateBackUpTokenInUse()
	if err != nil {
		return err
	}

	if oldNotification.LineToken != "" {
		err = s.repo.UpdateOldTokenToLine(oldNotification.LineToken)
		if err != nil {
			return err
		}
	}

	//clear
	cacheOldTokens = make(map[int64]model.CacheConfigurationNotificationToken)
	return nil
}

func (s *notificationService) ClearNotificationCache() error {

	cacheOldTokens = make(map[int64]model.CacheConfigurationNotificationToken)
	s.repo.ClearNotificationCache()
	return nil
}

func (s *notificationService) GetUserNewMemberForNoti() ([]model.UserNewMemberCountResponse, error) {
	// get user new member
	userNewMember, err := s.repo.GetUserNewMemberForNoti()
	if err != nil {
		return nil, err
	}

	notiMessage := fmt.Sprintf("สมัครใหม่แบบรับ member : %v \nสมัครใหม่ยังไม่เป็น member : %v \nรวมยูสสมัครวันนี้ : %v", userNewMember.UserWithoutMemberCode, userNewMember.UserWithMemberCode, userNewMember.Total)

	// get all external notification
	getExternalConfig, err := s.repo.GetConfigurationExternalNotificationInternal()
	if err != nil {
		return nil, err
	}

	go func() {
		var getAllExternalToken []model.GetConfigurationNotificationTokenResponse
		if len(getExternalConfig) > 0 {
			for _, configNoti := range getExternalConfig {
				getAllExternalToken, err = s.GetConfigurationNotificationTokenByExternalId(configNoti.Id)
				if err != nil {
					return
				}
				if len(getAllExternalToken) > 0 {
					for _, token := range getAllExternalToken {

						// sumAmount := req.Amount + *req.BonusCredit

						// if float64(record.CreditAbove) > sumAmount && req.TypeNotify != model.IsMemberRegistration {
						// 	return nil
						// }

						sentNotiLine := false
						sentNotiTelegram := false

						if configNoti.LineIsOn {
							sentNotiLine = true
						}

						if configNoti.TelegramIsOn {
							sentNotiTelegram = true
						}

						//set sent token
						if token.Token != "" {
							if sentNotiLine {
								if token.ConfigurationTokenTypeId == model.NOTI_TOKEN_TYPE_LINE && configNoti.LineIsOn {
									if err = s.repo.SendLineNotification(notiMessage, token.Token); err != nil {
										log.Println("ExternalNotification.SendLineNotification", err)
									}
								}
							}

							if sentNotiTelegram {
								if token.ConfigurationTokenTypeId == model.NOTI_TOKEN_TYPE_TELEGRAM && configNoti.TelegramIsOn {
									chatInfo, err := s.repo.GetTelegramChatTokenByToken(token.Token)
									if err != nil {
										log.Println("ExternalNotification.GetTelegramChatTokenByToken", err)
									} else if chatInfo != nil {
										if _, err = s.repo.SendTelegramMessage(*chatInfo, notiMessage); err != nil {
											log.Println("ExternalNotification.SendTelegramMessage", err)
										}
									} else {
										log.Println("ExternalNotification.GetTelegramChatTokenByToken chatInfo is nil")
									}
								}
							}
						}

					}
				}
			}
		}
	}()

	return nil, nil
}

func (s *notificationService) GetTransactionDailySummaryReportNotification() (string, error) {

	// cron run at 00:01 Bangkok time
	// summary report yesterday
	todayCurrentDateTime := time.Now().UTC().Add(7 * time.Hour)
	useYesterDayDate := todayCurrentDateTime.AddDate(0, 0, -1).Format("2006-01-02")

	// use Bangkok timezone as new date
	startDateAtBkk, err := s.repo.ParseBodBkk(useYesterDayDate)
	if err != nil {
		return "", err
	}
	endDateAtBkk, err := s.repo.ParseEodBkk(useYesterDayDate)
	if err != nil {
		return "", err
	}

	transactionSummaryReport, err := s.repo.GetTransactionSummaryReportNotification(*startDateAtBkk, *endDateAtBkk)
	if err != nil {
		return "", err
	}

	var message string
	message += fmt.Sprintf("====== สรุปผลรายวัน ======\n")
	message += fmt.Sprintf("วันที่: %s ถึง %s\n", startDateAtBkk.Add(7*time.Hour).Format("2006-01-02 15:04:05"), endDateAtBkk.Add(7*time.Hour).Format("2006-01-02 15:04:05"))
	message += fmt.Sprintf("รายการฝาก: %d\n", transactionSummaryReport.TotalDepositUserCount)
	message += fmt.Sprintf("ยอดฝากรวม: %.2f\n", transactionSummaryReport.TotalDepositPrice)
	message += fmt.Sprintf("รายการถอน: %d\n", transactionSummaryReport.TotalWithdrawUserCount)
	message += fmt.Sprintf("ยอดถอนรวม: %.2f\n", transactionSummaryReport.TotalWithdrawPrice)
	message += fmt.Sprintf("กำไร: %.2f\n", transactionSummaryReport.TotalBankProfit)
	message += fmt.Sprintf("สมัคร: %d\n", transactionSummaryReport.TotalNewUserCount)
	message += fmt.Sprintf("รายการสมัครฝาก 100 ขึ้นไป: %d\n", transactionSummaryReport.TotalDepositAboveHundredCount)
	message += fmt.Sprintf("โบนัส : %.2f\n", transactionSummaryReport.TotalActivityBonusPrice)

	bankAccountSummaryReport, err := s.repo.GetBankTransactionSummaryReportNotification(*startDateAtBkk, *endDateAtBkk)
	if err != nil {
		return "", err
	}

	message += fmt.Sprintf("\n")
	for _, bankAccount := range bankAccountSummaryReport {
		message += fmt.Sprintf("ธนาคาร: %s\n", bankAccount.BankCode)
		message += fmt.Sprintf("เลขที่บัญชี: %s\n", bankAccount.AccountNumber)
		message += fmt.Sprintf("ชื่อบัญชี: %s\n", bankAccount.AccountName)
		message += fmt.Sprintf("บช.ฝาก : %.2f\n", bankAccount.TotalDepositPrice)
		message += fmt.Sprintf("บช.ถอน : %.2f\n", bankAccount.TotalWithdrawPrice)
		message += fmt.Sprintf("\n")
	}

	var reqSentNoti model.NotifyExternalNotificationRequest
	reqSentNoti.CronjobMessage = message
	reqSentNoti.TypeNotify = model.TransactionDailySummary
	sentErr := s.ExternalNotification(reqSentNoti)
	if sentErr != nil {
		return "", err
	}

	return message, nil
}

func (s *notificationService) GetTransactionHourSummaryReportNotification() (string, error) {

	// cron run every hour
	// summary report today
	todayCurrentDateTime := time.Now().UTC().Add(7 * time.Hour)
	// start

	// 00:00:00 to 00:30:00
	// -1 hour
	// จะได้ตัดยอด ครบวัน
	if todayCurrentDateTime.Hour() == 0 && todayCurrentDateTime.Minute() < 30 {
		todayCurrentDateTime = todayCurrentDateTime.Add(-1 * time.Hour)
	}

	startDateAtBkk, err := s.repo.ParseBodBkk(todayCurrentDateTime.Format("2006-01-02"))
	if err != nil {
		return "", err
	}

	//end
	endDate := time.Now().UTC()

	transactionSummaryReport, err := s.repo.GetTransactionSummaryReportNotification(*startDateAtBkk, endDate)
	if err != nil {
		return "", err
	}

	var message string
	message += fmt.Sprintf("====== สรุปผลรายชั่วโมง ======\n")
	message += fmt.Sprintf("วันที่: %s ถึง %s\n", startDateAtBkk.Add(7*time.Hour).Format("2006-01-02 15:04:05"), endDate.Add(7*time.Hour).Format("2006-01-02 15:04:05"))
	message += fmt.Sprintf("รายการฝาก: %d\n", transactionSummaryReport.TotalDepositUserCount)
	message += fmt.Sprintf("ยอดฝากรวม: %.2f\n", transactionSummaryReport.TotalDepositPrice)
	message += fmt.Sprintf("รายการถอน: %d\n", transactionSummaryReport.TotalWithdrawUserCount)
	message += fmt.Sprintf("ยอดถอนรวม: %.2f\n", transactionSummaryReport.TotalWithdrawPrice)
	message += fmt.Sprintf("กำไร: %.2f\n", transactionSummaryReport.TotalBankProfit)
	message += fmt.Sprintf("สมัคร: %d\n", transactionSummaryReport.TotalNewUserCount)
	message += fmt.Sprintf("รายการสมัครฝาก 100 ขึ้นไป: %d\n", transactionSummaryReport.TotalDepositAboveHundredCount)
	message += fmt.Sprintf("โบนัส : %.2f\n", transactionSummaryReport.TotalActivityBonusPrice)

	var reqSentNoti model.NotifyExternalNotificationRequest
	reqSentNoti.CronjobMessage = message
	reqSentNoti.TypeNotify = model.TransactionHourSummary
	sentErr := s.ExternalNotification(reqSentNoti)
	if sentErr != nil {
		return "", err
	}

	return message, nil
}

func (s *notificationService) GetAffiliateSummaryReportNotification() (string, error) {

	// cron run at 00:01 Bangkok time
	// summary report yesterday
	todayCurrentDateTime := time.Now().UTC().Add(7 * time.Hour)
	useYesterDayDate := todayCurrentDateTime.AddDate(0, 0, -1).Format("2006-01-02")

	// use Bangkok timezone as new date
	startDateAtBkk, err := s.repo.ParseBodBkk(useYesterDayDate)
	if err != nil {
		return "", err
	}
	endDateAtBkk, err := s.repo.ParseEodBkk(useYesterDayDate)
	if err != nil {
		return "", err
	}

	alliance, err := s.repo.GetAllianceUserSummaryReportNotification(*startDateAtBkk, *endDateAtBkk)
	if err != nil {
		return "", err
	}

	var message string
	message += fmt.Sprintf("======รวมยูสจากระบบพันธมิตรรายวัน ======\n")
	message += fmt.Sprintf("วันที่: %s ถึง %s\n", startDateAtBkk.Add(7*time.Hour).Format("2006-01-02 15:04:05"), endDateAtBkk.Add(7*time.Hour).Format("2006-01-02 15:04:05"))
	message += fmt.Sprintf("\n")
	for _, allianceUser := range alliance {
		message += fmt.Sprintf("user: %s\n", allianceUser.MemberCode)
		message += fmt.Sprintf("ชื่อ: %s\n", allianceUser.UserFullname)
		message += fmt.Sprintf("นามแฝง : %s\n", allianceUser.AllianceName)
		message += fmt.Sprintf("จำนวนยูสที่ฝาก : %d\n", allianceUser.TotalCountDownUserDeposit)
		message += fmt.Sprintf("\n")
	}

	if len(alliance) > 0 {
		var reqSentNoti model.NotifyExternalNotificationRequest
		reqSentNoti.CronjobMessage = message
		reqSentNoti.TypeNotify = model.AffiliateDailySummary
		sentErr := s.ExternalNotification(reqSentNoti)
		if sentErr != nil {
			return "", err
		}
	}

	return message, nil
}
