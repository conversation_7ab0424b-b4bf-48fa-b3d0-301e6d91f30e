package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"log"
)

type WebService interface {
	IncreaseRefCount(req model.GetAliasByUserIdRequest) error
}

type webService struct {
	afRepo   repository.AffiliateRepository
	alRepo   repository.AllianceRepository
	userRepo repository.UserRepository
}

func NewWebService(
	afRepo repository.AffiliateRepository,
	alRepo repository.AllianceRepository,
	userRepo repository.UserRepository,
) WebService {
	return &webService{afRepo, alRepo, userRepo}
}

func (s *webService) IncreaseRefCount(req model.GetAliasByUserIdRequest) error {

	userType := ""
	userId := int64(0)

	if req.RefCode != "" {
		// GtByRefCode
		userAllianceInfo, err := s.alRepo.GetUserAllianceInfoByRefCode(req.RefCode)
		if err != nil {
			return internalServerError(err)
		}
		// Get user type
		userType1, err := s.userRepo.GetRefTypeByRef(userAllianceInfo.UserId)
		if err != nil {
			return internalServerError(err)
		}
		userType = userType1
		userId = userAllianceInfo.UserId
	} else if req.Ref != "" {
		// Get user type
		userType1, err := s.userRepo.GetRefTypeByRef(helper.DecodeData(req.Ref))
		if err != nil {
			return internalServerError(err)
		}
		userType = userType1
		userId = helper.DecodeData(req.Ref)
	}

	if userType == "AFFILIATE" {
		// For Total Click
		if err := s.afRepo.IncreaseLinkClick(userId); err != nil {
			return internalServerError(err)
		}
		// Log for daily reporting
		if err := s.alRepo.CreateLinkClickLog(userId); err != nil {
			log.Println("Error.CreateLinkClickLog", err)
		}
	} else if userType == "ALLIANCE" {
		// For Total Click
		if err := s.alRepo.AlIncreaseLinkClick(userId); err != nil {
			return internalServerError(err)
		}
		// Log for daily reporting
		if err := s.alRepo.CreateLinkClickLog(userId); err != nil {
			log.Println("Error.CreateLinkClickLog", err)
		}
	}

	return nil
}
