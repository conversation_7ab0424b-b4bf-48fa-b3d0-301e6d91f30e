package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"log"
	"time"
)

type DownlineService interface {
	GetWebInfo() (*model.DownlineWebInfo, error)
	GetPaymentInfo() (*model.DownlinePaymentInfo, error)
	UpdateDownlineBalance() (*model.WebStatusResponse, error)
	SubmitInvoice(id int64) (*int64, error)
	SubmitScammer(id int64) (*int64, error)
	UpdatePendingInvoiceList() error
	CreateInvoiceFromRemote(body model.DownlineInvoiceSubmitRequest) (*model.DownlineInvoiceSubmitResponse, error)
	// WEB
	GetPublicWebInfo() (*model.PublicWebInfo, error)
	// SCAMMER
	GetMasterScammerList(query model.DownlineScammerequest) (*model.DownlineScammerPaginationResponse, error)
	//Line - Contact
	GetMasterLineContact() (*model.DownlineLineContactResponse, error)
}

type downlineService struct {
	repo repository.DownlineRepository
}

func NewDownlineService(
	repo repository.DownlineRepository,
) DownlineService {
	return &downlineService{repo}
}

func (s downlineService) GetWebInfo() (*model.DownlineWebInfo, error) {

	record, err := s.repo.GetWebInfo()
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(recordNotFound)
		}
		return nil, internalServerError(err)
	}
	return record, nil
}

func (s downlineService) GetPaymentInfo() (*model.DownlinePaymentInfo, error) {

	record, err := s.repo.GetPaymentInfo()
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(recordNotFound)
		}
		return nil, internalServerError(err)
	}
	return record, nil
}

func (s downlineService) UpdateDownlineBalance() (*model.WebStatusResponse, error) {

	localInfo, err := s.repo.GetLocalWebInfo()
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(recordNotFound)
		}
		return nil, internalServerError(err)
	}

	// Update Every 1 minute
	if localInfo.LastRemoteUpdateAt != nil {
		diffSecs := time.Since(*localInfo.LastRemoteUpdateAt).Seconds()
		// fmt.Println("UpdateDownlineBalance.diffSecs", diffSecs)
		if diffSecs < 30 {
			return localInfo, err
		}
	}

	var body model.DownlineWebBalanceRequest
	body.SmsCreditBalance = localInfo.SmsCreditBalance
	body.FastbankCreditBalance = localInfo.FastbankCreditBalance

	record, err := s.repo.UpdateDownlineBalance(body)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(recordNotFound)
		}
		return nil, internalServerError(err)
	}

	localExpireAt, err := time.Parse("2006-01-02T15:04:05Z", localInfo.WebExpiredDate)
	if err != nil {
		return nil, internalServerError(err)
	}
	remoteExpireAt, err := time.Parse("2006-01-02T15:04:05Z", record.WebExpiredDate)
	if err != nil {
		return nil, internalServerError(err)
	}

	// UPDATE CLIENT
	localDiff := map[string]interface{}{
		"name":                   localInfo.Name,
		"web_domain":             localInfo.WebDomain,
		"is_front_enabled":       localInfo.IsFrontEnabled,
		"is_back_enabled":        localInfo.IsBackEnabled,
		"maintenance_message":    localInfo.MaintenanceMessage,
		"payment_detail":         localInfo.PaymentDetail,
		"current_web_package_id": localInfo.CurrentWebPackageId,
		"web_expired_date":       localExpireAt.Format("2006-01-02"),
		// FreeBuffeFastBank
		"fastbank_free_start_date": localInfo.FastbankFreeStartDate,
		"fastbank_free_end_date":   localInfo.FastbankFreeEndDate,
	}
	// **** มันจะเอามาอัพเดทให้แค่นี้ ****
	remoteDiff := map[string]interface{}{
		"name":                   record.Name,
		"web_domain":             record.WebDomain,
		"is_front_enabled":       record.IsFrontEnabled,
		"is_back_enabled":        record.IsBackEnabled,
		"maintenance_message":    record.MaintenanceMessage,
		"payment_detail":         record.PaymentDetail,
		"current_web_package_id": record.CurrentWebPackageId,
		"web_expired_date":       remoteExpireAt.Format("2006-01-02"),
		// FreeBuffeFastBank
		"fastbank_free_start_date": record.FastbankFreeStartDate,
		"fastbank_free_end_date":   record.FastbankFreeEndDate,
	}
	// log.Println("UpdateDownlineBalance.localDiff", helper.StructJson(localDiff))
	// log.Println("UpdateDownlineBalance.remoteDiff", helper.StructJson(remoteDiff))

	if helper.StructJson(localDiff) != helper.StructJson(remoteDiff) {
		// Recheck Date String
		if remoteDiff["fastbank_free_start_date"] == "" {
			remoteDiff["fastbank_free_start_date"] = nil
		} else if len(remoteDiff["fastbank_free_start_date"].(string)) > 10 {
			remoteDiff["fastbank_free_start_date"] = remoteDiff["fastbank_free_start_date"].(string)[0:10]
		}
		if remoteDiff["fastbank_free_end_date"] == "" {
			remoteDiff["fastbank_free_end_date"] = nil
		} else if len(remoteDiff["fastbank_free_end_date"].(string)) > 10 {
			remoteDiff["fastbank_free_end_date"] = remoteDiff["fastbank_free_end_date"].(string)[0:10]
		}
		// Update local info
		remoteDiff["last_remote_update_at"] = time.Now()
		if err := s.repo.UpdateWebMasterInfo(localInfo.Id, remoteDiff); err != nil {
			return nil, internalServerError(err)
		}
	} else {
		// Nothing Change
		onlyTimeDiff := map[string]interface{}{
			"last_remote_update_at": time.Now(),
		}
		if err := s.repo.UpdateWebMasterInfo(localInfo.Id, onlyTimeDiff); err != nil {
			return nil, internalServerError(err)
		}
	}

	// ALSO, Approve Pending Invoice
	if err := s.UpdatePendingInvoiceList(); err != nil {
		log.Println("UpdateDownlineBalance.UpdatePendingInvoiceList.ERROR", err)
	}
	// REGET, local info
	localInfo2, err := s.repo.GetLocalWebInfo()
	if err != nil {
		log.Println("UpdateDownlineBalance.GetLocalWebInfo2.ERROR", err)
	} else {
		localInfo = localInfo2
	}

	return localInfo, nil
}

func (s downlineService) SubmitInvoice(id int64) (*int64, error) {

	invoice, err := s.repo.GetDownlineInvoiceRequestById(id)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(recordNotFound)
		}
		return nil, internalServerError(err)
	}

	if invoice.StatusId != model.INVOICE_STATUS_WAIT_CONFIRM {
		return nil, badRequest("INVALID_INVOICE_STATUS")
	}

	insertId, err := s.repo.SubmitPaidInvoice(*invoice)
	if err != nil {
		return nil, internalServerError(err)
	}
	return insertId, nil
}

func (s downlineService) SubmitScammer(id int64) (*int64, error) {

	scammer, err := s.repo.GetScammerById(id)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(recordNotFound)
		}
		return nil, internalServerError(err)
	}

	var remoteReq model.ScammerSubmitRequest
	remoteReq.Id = scammer.Id
	remoteReq.Phone = scammer.Phone
	remoteReq.BankName = scammer.BankName
	remoteReq.BankAccount = scammer.BankAccount
	remoteReq.Reason = scammer.Reason
	remoteReq.AccountName = scammer.Fullname
	insertId, err := s.repo.SubmitScammer(remoteReq)
	if err != nil {
		return nil, internalServerError(err)
	}
	return insertId, nil
}

func (s downlineService) UpdatePendingInvoiceList() error {

	var query model.DownlineInvoiceListRequest
	query.StatusId = model.INVOICE_STATUS_WAIT_CONFIRM

	pendingList, total, err := s.repo.GetDownlineRawInvoiceList(query)
	if err != nil {
		return internalServerError(err)
	}

	if total > 0 {
		pendingIds := make([]int, 0)
		for _, invoice := range pendingList {
			pendingIds = append(pendingIds, int(invoice.Id))
		}
		if len(pendingIds) > 0 {
			var body model.DownlineConfirmedInvoiceListRequest
			body.PendingIds = pendingIds
			resp, err := s.repo.GetConfirmedRemoteInvoiceList(body)
			if err != nil {
				return internalServerError(err)
			}
			for _, invoice := range resp.List {
				if invoice.StatusId == model.INVOICE_STATUS_COMPLETED {
					if err := s.confirmedLocalInvoice(invoice); err != nil {
						log.Println("UpdatePendingInvoiceList.confirmedLocalInvoice.ERROR", err)
					}
				} else if invoice.StatusId == model.INVOICE_STATUS_REJECTED {
					if err := s.rejectLocalInvoice(invoice); err != nil {
						log.Println("UpdatePendingInvoiceList.rejectLocalInvoice.ERROR", err)
					}
				}
			}
		}
	}
	return nil
}

func (s downlineService) confirmedLocalInvoice(remoteInvoice model.DownlineInvoiceResponse) error {

	invoice, err := s.repo.GetInvoiceById(remoteInvoice.DownlineInvoiceId)
	if err != nil {
		if err.Error() == recordNotFound {
			return notFound("INVOICE_NOT_FOUND")
		}
		return internalServerError(err)
	}

	myWeb, err := s.repo.GetLocalWebInfo()
	if err != nil {
		return internalServerError(err)
	}
	smsExpireAt, err := time.Parse("2006-01-02T15:04:05Z", myWeb.SmsExpiredDate)
	if err != nil {
		return internalServerError(err)
	}
	fastbankExpireAt, err := time.Parse("2006-01-02T15:04:05Z", myWeb.FastbankExpiredDate)
	if err != nil {
		return internalServerError(err)
	}

	if invoice.StatusId == model.INVOICE_STATUS_WAIT_CONFIRM {
		actionAt := time.Now()

		var confirmBody model.InvoiceConfirmBody
		confirmBody.Id = invoice.Id
		confirmBody.ConfirmAt = actionAt
		confirmBody.ConfirmByName = remoteInvoice.ConfirmByName
		confirmBody.DiscountPrice = remoteInvoice.DiscountPrice
		confirmBody.TotalPrice = remoteInvoice.TotalPrice
		if err := s.repo.ConfirmInvoice(confirmBody); err != nil {
			return internalServerError(err)
		}

		// UPDATE WEB BY INVOICE TYPE
		if invoice.InvoiceTypeId == model.INVOICE_TYPE_WEB_RENEWAL {
			// use Master Data
			log.Println("INVOICE_TYPE_WEB_RENEWAL=DO_NOTHING")
		} else if invoice.InvoiceTypeId == model.INVOICE_TYPE_SMS_RENEWAL {
			// Increase SMS Credit
			if invoice.RenewDays > 0 {
				smsExpireAt = smsExpireAt.AddDate(0, 0, invoice.RenewDays)
			}
			var updateBody model.WebMasterUpdateSmsRenewalBody
			updateBody.WebId = invoice.WebId
			updateBody.CurrentSmsPackageId = invoice.PackageId
			updateBody.SmsExpiredDate = smsExpireAt.Format("2006-01-02")
			// Increase SMS Credit
			updateBody.SmsCredit = int64(invoice.RenewCreditAmount)
			if err := s.repo.UpdateSmsRenewal(updateBody); err != nil {
				return internalServerError(err)
			}
		} else if invoice.InvoiceTypeId == model.INVOICE_TYPE_FASTBANK_RENEWAL {

			if invoice.RenewDays > 0 {
				fastbankExpireAt = fastbankExpireAt.AddDate(0, 0, invoice.RenewDays)
			}
			var updateBody model.WebMasterUpdateFastbankRenewalBody
			updateBody.WebId = invoice.WebId
			updateBody.CurrentFastbankPackageId = invoice.PackageId
			updateBody.FastbankExpiredDate = fastbankExpireAt.Format("2006-01-02")
			// Increase Fastbank Credit
			updateBody.FastbankCredit = int64(invoice.RenewCreditAmount)
			if err := s.repo.UpdateFastbankRenewal(updateBody); err != nil {
				return internalServerError(err)
			}
		}
	} else {
		return badRequest("INVOICE_STATUS_NOT_WAIT_CONFIRM")
	}
	return nil
}

func (s downlineService) rejectLocalInvoice(remoteInvoice model.DownlineInvoiceResponse) error {

	invoice, err := s.repo.GetInvoiceById(remoteInvoice.DownlineInvoiceId)
	if err != nil {
		if err.Error() == recordNotFound {
			return notFound("INVOICE_NOT_FOUND")
		}
		return internalServerError(err)
	}

	if invoice.StatusId == model.INVOICE_STATUS_WAIT_CONFIRM {
		actionAt := time.Now()

		var confirmBody model.InvoiceConfirmBody
		confirmBody.Id = invoice.Id
		confirmBody.ConfirmAt = actionAt
		confirmBody.ConfirmByName = remoteInvoice.ConfirmByName
		if err := s.repo.RejectInvoice(confirmBody); err != nil {
			return internalServerError(err)
		}
	} else {
		return badRequest("INVOICE_STATUS_NOT_WAIT_CONFIRM")
	}
	return nil
}

func (s downlineService) CreateInvoiceFromRemote(body model.DownlineInvoiceSubmitRequest) (*model.DownlineInvoiceSubmitResponse, error) {

	log.Println("CreateInvoiceFromRemote.body", helper.StructJson(body))

	return s.BuyPackageFromRemote(body)
}

func (s downlineService) BuyPackageFromRemote(body model.DownlineInvoiceSubmitRequest) (*model.DownlineInvoiceSubmitResponse, error) {

	actionAt := time.Now()

	invoice, err := s.repo.GetDownlineInvoiceRequestById(body.Id)
	if err != nil {
		if err.Error() != recordNotFound {
			return nil, internalServerError(err)
		}
	}
	if invoice != nil {
		log.Println("BuyPackageFromRemote.INVOICE_ALREADY_EXIST")
		return nil, badRequest("INVOICE_ALREADY_EXIST")
	}

	myWeb, err := s.repo.GetLocalWebInfo()
	if err != nil {
		return nil, internalServerError(err)
	}
	smsExpireAt, err := time.Parse("2006-01-02T15:04:05Z", myWeb.SmsExpiredDate)
	if err != nil {
		return nil, internalServerError(err)
	}
	fastbankExpireAt, err := time.Parse("2006-01-02T15:04:05Z", myWeb.FastbankExpiredDate)
	if err != nil {
		return nil, internalServerError(err)
	}

	masterId := int64(0)
	masterName := "MASTER"

	var createBody model.InvoiceCreateBody
	createBody.WebId = myWeb.Id
	createBody.WebName = myWeb.Name
	createBody.CreateBy = body.CreateBy
	createBody.InvoiceNo = "INV" + actionAt.Format("06") // TEMP INVYYYY000000
	createBody.InvoiceTypeId = body.InvoiceTypeId
	createBody.PackageId = body.PackageId
	createBody.RenewDays = body.RenewDays
	createBody.RenewCreditAmount = body.RenewCreditAmount
	createBody.PackageDetail = body.PackageDetail
	createBody.InvoiceAt = *body.InvoiceAt
	createBody.ExpireAt = *body.ExpireAt
	createBody.PaymentDetail = body.PaymentDetail
	createBody.PaidAt = body.PaidAt
	createBody.PaidBy = &masterId
	createBody.StatusId = model.INVOICE_STATUS_COMPLETED // Already Completed from Master
	createBody.ConfirmBy = &masterId
	createBody.ConfirmAt = &actionAt
	createBody.ConfirmByName = &masterName
	createBody.SumPrice = body.SumPrice
	createBody.VatPercent = body.VatPercent
	createBody.VatPrice = body.VatPrice
	createBody.DiscountPrice = body.DiscountPrice
	createBody.TotalPrice = body.TotalPrice
	insertId, err := s.repo.CreateWebRenewalInvoice(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// ====================================================

	newInvoice, err := s.repo.GetInvoiceById(*insertId)
	if err != nil {
		return nil, internalServerError(err)
	}

	// UPDATE WEB BY INVOICE TYPE
	if newInvoice.InvoiceTypeId == model.INVOICE_TYPE_WEB_RENEWAL {
		// use Master Data
		log.Println("BuyPackageFromRemote.INVOICE_TYPE_WEB_RENEWAL=DO_NOTHING")
	} else if newInvoice.InvoiceTypeId == model.INVOICE_TYPE_SMS_RENEWAL {
		// Increase SMS Credit
		if newInvoice.RenewDays > 0 {
			smsExpireAt = smsExpireAt.AddDate(0, 0, newInvoice.RenewDays)
		}
		var updateBody model.WebMasterUpdateSmsRenewalBody
		updateBody.WebId = newInvoice.WebId
		updateBody.CurrentSmsPackageId = newInvoice.PackageId
		updateBody.SmsExpiredDate = smsExpireAt.Format("2006-01-02")
		// Increase SMS Credit
		updateBody.SmsCredit = int64(newInvoice.RenewCreditAmount)
		if err := s.repo.UpdateSmsRenewal(updateBody); err != nil {
			return nil, internalServerError(err)
		}
	} else if newInvoice.InvoiceTypeId == model.INVOICE_TYPE_FASTBANK_RENEWAL {

		if newInvoice.RenewDays > 0 {
			fastbankExpireAt = fastbankExpireAt.AddDate(0, 0, newInvoice.RenewDays)
		}
		var updateBody model.WebMasterUpdateFastbankRenewalBody
		updateBody.WebId = newInvoice.WebId
		updateBody.CurrentFastbankPackageId = newInvoice.PackageId
		updateBody.FastbankExpiredDate = fastbankExpireAt.Format("2006-01-02")
		// Increase Fastbank Credit
		updateBody.FastbankCredit = int64(newInvoice.RenewCreditAmount)
		if err := s.repo.UpdateFastbankRenewal(updateBody); err != nil {
			return nil, internalServerError(err)
		}
	}

	var resp model.DownlineInvoiceSubmitResponse
	resp.Id = *insertId
	resp.WebId = newInvoice.WebId
	resp.InvoiceNo = newInvoice.InvoiceNo

	return &resp, nil
}

func (s downlineService) GetPublicWebInfo() (*model.PublicWebInfo, error) {

	webInfo, err := s.repo.GetWebInfo()
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(recordNotFound)
		}
		return nil, internalServerError(err)
	}

	var web model.PublicWebInfo
	web.Name = webInfo.Name
	web.WebDomain = webInfo.WebDomain
	web.IsFrontEnabled = webInfo.IsFrontEnabled
	web.IsBackEnabled = webInfo.IsBackEnabled

	return &web, nil
}

func (s downlineService) GetMasterScammerList(query model.DownlineScammerequest) (*model.DownlineScammerPaginationResponse, error) {

	userInfo, err := s.repo.GetUserById(query.UserId)
	if err != nil {
		return nil, internalServerError(err)
	}
	if userInfo == nil || userInfo.Id == 0 {
		return nil, badRequest("USER_NOT_FOUND")
	}

	var newData model.DownlineScammerequest
	newData.Page = query.Page
	newData.Limit = query.Limit
	newData.Phone = userInfo.Phone
	newData.BankAccount = userInfo.BankAccount
	newData.AccountName = userInfo.Fullname

	result, err := s.repo.GetMasterScammerList(newData)
	if err != nil {
		return nil, internalServerError(err)
	}

	return result, nil
}

func (s downlineService) GetMasterLineContact() (*model.DownlineLineContactResponse, error) {

	result, err := s.repo.GetMasterLineContact()
	if err != nil {
		return nil, internalServerError(err)
	}
	return result, nil
}
