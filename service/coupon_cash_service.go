package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"fmt"
	"log"
	"time"
)

func NewCouponCashService(
	repo repository.CouponCashRepository,
	serviceUser UserService,
) CouponCashService {
	return &couponCashService{repo, serviceUser}
}

type couponCashService struct {
	repo        repository.CouponCashRepository
	serviceUser UserService
}

type CouponCashService interface {
	//option
	GetCouponCashStatus() ([]model.CouponCashStatus, error)
	GetCouponCashUserStatus() ([]model.CouponCashUserStatus, error)
	// main
	CreateCouponCash(req model.CouponCashCreateRequest) (int64, error)
	GetCouponCashList(req model.GetCouponCashListRequest) (*model.SuccessWithPagination, error)
	GetCouponCashUserList(req model.GetCouponCashUserListRequest) (*model.SuccessWithPagination, error)
	UserUseCouponCash(req model.UserUseCouponCashRequest) (string, error)
	DeleteMainCouponCashUserById(body model.SoftDeleteCouponCashUserByCouponCashId) error
	DeleteCouponCashUserById(body model.SoftDeleteCouponCashUserById) error
	CheckCouponTurnOverWithdraw(userId int64, withdrawAmount float64) (string, float64, error)
	CanceledAllTurnCoupon(userId int64) error

	CouponCashSummary(body model.CouponCashSummaryRequest) (*model.CouponCashSummaryResponse, error)
	CouponCashUserSummary(req model.CouponCashUserSummaryRequest) (*model.CouponCashUserSummaryResponse, error)
}

func (s couponCashService) GetCouponCashStatus() ([]model.CouponCashStatus, error) {
	list, err := s.repo.GetCouponCashStatus()
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s couponCashService) GetCouponCashUserStatus() ([]model.CouponCashUserStatus, error) {
	list, err := s.repo.GetCouponCashUserStatus()
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s couponCashService) CreateCouponCash(req model.CouponCashCreateRequest) (int64, error) {
	var cleanUpData = make(map[string]interface{})

	req.CouponCashStatusId = model.COUPON_ACTIVE
	if req.CouponTurnover != nil && *req.CouponTurnover > 0 {
		req.TurnoverToPlay = float64(*req.CouponTurnover) * req.CouponBonus
	} else {
		req.TurnoverToPlay = 0
		setCouponTurnover := int64(0)
		req.CouponTurnover = &setCouponTurnover
	}
	id, err := s.repo.CreateCouponCash(req)
	if err != nil {
		return 0, err
	}
	cleanUpData["rollbackCouponCashById"] = id
	// CreateCouponCashUser(reqChunk []model.CouponCashCreateCouponCashUserBody) error
	reqChunk := []model.CouponCashCreateCouponCashUserBody{}
	for i := int64(0); i < req.CouponTotal; i++ {
		reqChunk = append(reqChunk, model.CouponCashCreateCouponCashUserBody{
			CouponCashId:           id,
			GenerateKey:            helper.GenerateRandomStringAndNumber(13),
			CouponCashUserStatusId: model.COUPON_USER_PENDING,
			CreatedByAdminId:       req.CreatedByAdminId,
		})
	}
	if err := s.repo.CreateCouponCashUser(reqChunk); err != nil {
		if err := s.CleanUpCouponCash(cleanUpData); err != nil {
			return 0, err
		}

		return 0, err
	}

	return id, nil
}

func (s *couponCashService) CleanUpCouponCash(cleanUpData map[string]interface{}) error {

	for k, v := range cleanUpData {
		switch k {
		case "rollbackCouponCashUserByCouponCashId":
			if err := s.repo.DeleteCouponCashUserByCouponCashId(v.(int64)); err != nil {
				return err
			}
		case "rollbackCouponCashById":
			if err := s.repo.DeleteCouponCashById(v.(int64)); err != nil {
				return err
			}

		}
	}
	return nil
}

func (s *couponCashService) GetCouponCashList(req model.GetCouponCashListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	list, total, err := s.repo.GetCouponCashList(req)
	if err != nil {
		return nil, err
	}

	var res model.SuccessWithPagination
	res.List = list
	res.Total = total
	return &res, nil
}

func (s *couponCashService) GetCouponCashUserList(req model.GetCouponCashUserListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	list, total, err := s.repo.GetCouponCashUserList(req)
	if err != nil {
		return nil, err
	}

	var res model.SuccessWithPagination
	res.List = list
	res.Total = total
	return &res, nil
}

func (s *couponCashService) UserUseCouponCash(req model.UserUseCouponCashRequest) (string, error) {
	message := "COUPON_CASH_USER_FAIL"
	actionAt := time.Now().UTC()

	avalibleCoupon, _ := s.repo.CheckAvalibleCouponAndPromtion(req.UserId, "COUPON_CASH")
	if avalibleCoupon != nil && avalibleCoupon.Id > 0 {
		return "", badRequest("TURN_OVER_IN_PROMOTION")
	}

	// check coupon cash user status
	couponCashUser, err := s.repo.GetCouponCashUserByGenerateKey(req.GenerateKey)
	if err != nil {
		return "", err
	}
	if couponCashUser.CouponCashUserStatusId != model.COUPON_USER_PENDING {
		return "", badRequest("COUPON_USER_STATUS_NOT_PENDING_OR_USED")
	}

	getCouponCash, err := s.repo.GetCouponCashById(couponCashUser.CouponCashId)
	if err != nil {
		return "", err
	}

	if getCouponCash.CouponCashStatusId != model.COUPON_ACTIVE {
		return "", badRequest("COUPON_CASH_STATUS_NOT_ACTIVE")
	}

	// get confirm key checker
	var couponCashUserConfirm model.CouponCashUserConfirmCreateRequest
	couponCashUserConfirm.CouponCashUserId = couponCashUser.Id
	couponCashUserConfirm.UserId = req.UserId
	couponCashUserConfirm.ConfirmKey = fmt.Sprintf("CONFIRM_%d", couponCashUser.Id)
	checkConfirmId, _ := s.repo.GetCouponCashUserConfirm(couponCashUserConfirm.ConfirmKey)
	if checkConfirmId > 0 {
		return "", badRequest("COUPON_CASH_CONFIRM_KEY_EXIST")
	}
	confirmId, err := s.repo.CouponCashUserConfirmCreate(couponCashUserConfirm)
	if err != nil {
		return "", err
	}
	if confirmId > 0 {
		rollBackAction := fmt.Sprintf("CONFIRM_FAILED_%d", couponCashUser.Id)

		getUser, _ := s.repo.GetUserById(req.UserId)
		if getUser != nil {
			if getUser.MemberCode == nil {
				memberCode, err := s.serviceUser.GenUniqueUserMemberCode(req.UserId)
				if err != nil {
					log.Println("CreateDepositRecord.GenUniqueUserMemberCode", err)
					// CouponCashUserConfirmRollback(confirmKeyId int64, newNewconfirmKey string) error
					if err := s.repo.CouponCashUserConfirmRollback(confirmId, rollBackAction); err != nil {
						return "", err
					}
					return "", err
				}
				getUser.MemberCode = memberCode
			}

		}

		var userCreditReq model.UserTransactionCreateRequest
		userCreditReq.RefId = &couponCashUser.Id
		userCreditReq.UserId = req.UserId
		userCreditReq.TypeId = model.CREDIT_TYPE_COUPON_CASH
		userCreditReq.BonusAmount = getCouponCash.CouponBonus
		userCreditReq.StartWorkAt = actionAt // เริ่มนับตอนกดยินยัน
		userCreditReq.Detail = "คูปองเงินสด"
		creditTransferResp, err := s.repo.IncreaseUserCredit(userCreditReq)
		if err != nil {
			if err := s.repo.CouponCashUserConfirmRollback(confirmId, rollBackAction); err != nil {
				return "", err
			}
			return "", internalServerError(err)
		}

		if creditTransferResp.AgentSuccess {
			var updateCouponCashUserBody model.UpdateCouponCashUserFromUserBody
			if getCouponCash.CouponTurnover > 0 {
				updateCouponCashUserBody.GenerateKey = req.GenerateKey
				updateCouponCashUserBody.UserId = req.UserId
				updateCouponCashUserBody.CouponCashUserStatusId = model.COUPON_USER_RECEIVE_ON_TURNOVER
				updateCouponCashUserBody.UserReceiveAt = time.Now().UTC()
				if err := s.repo.UpdateCouponCashUserFromUser(updateCouponCashUserBody); err != nil {
					if err := s.repo.CouponCashUserConfirmRollback(confirmId, rollBackAction); err != nil {
						return "", err
					}
					return "", err
				}

				// [Create Turnover User Statement]
				actionAt := time.Now().UTC()
				bonusAmount := getCouponCash.CouponBonus
				turnOverAmount := getCouponCash.TurnoverToPlay + 1 // มาจาก ต้องเล่นเท่ากับจำนวนติด [20240507] พี่ตุลาconfirm
				var createBody model.TurnoverUserStatementCreateBody
				createBody.UserId = req.UserId
				// แยก status ออก เพราะ requriement บอก โปร ติิดเทิร์นห้ามรับซ้ำ แต่ คูปองเงินสด ไม่ต้องเช็ค เวลาเข็คเทิร์นจะไม่ต้อง งง
				createBody.TypeId = model.TURN_SETTING_COUPON_ALL
				createBody.Name = model.TURNOVER_CATE_ACTIVITY
				createBody.PromotionName = "คูปองเงินสด"
				createBody.RefTypeId = couponCashUser.Id
				createBody.BonusAmount = bonusAmount
				createBody.StatusId = model.TURNOVER_STATEMENT_STATUS_PENDING
				createBody.StartTurnAmount = turnOverAmount
				createBody.StartTurnAt = &actionAt
				createBody.TotalTurnAmount = turnOverAmount
				if _, err := s.repo.CreateTurnoverUserStatement(createBody); err != nil {
					return "", err
				}

				// getUser, _ := s.repo.GetUserById(req.UserId)
				// if getUser != nil {
				// 	var incomeLogCreateBody model.UserIncomeLogCreateBody
				// 	incomeLogCreateBody.UserId = req.UserId
				// 	incomeLogCreateBody.TypeId = model.USER_INCOME_TYPE_COUPON
				// 	incomeLogCreateBody.Detail = "รายได้จากกิจกรรมคูปอง"
				// 	incomeLogCreateBody.CreditAmount = bonusAmount
				// 	incomeLogCreateBody.StatusId = model.USER_INCOME_STATUS_COMPLETED // กดแล้วได้เงินเลย
				// 	incomeLogCreateBody.CreateBy = req.UserId
				// 	incomeLogCreateBody.CreateByName = getUser.Firstname
				// 	setBotId := int64(0)
				// 	incomeLogCreateBody.ConfirmBy = &setBotId
				// 	incomeLogCreateBody.ConfirmByName = "ออโต้"
				// 	_, err = s.repo.CreateUserIncomeLog(incomeLogCreateBody)
				// 	if err != nil {
				// 		return "", err
				// 	}
				// }

			} else {
				updateCouponCashUserBody.GenerateKey = req.GenerateKey
				updateCouponCashUserBody.UserId = req.UserId
				updateCouponCashUserBody.CouponCashUserStatusId = model.COUPON_USER_SUCCESS
				updateCouponCashUserBody.UserReceiveAt = time.Now().UTC()
				if err := s.repo.UpdateCouponCashUserFromUser(updateCouponCashUserBody); err != nil {
					if err := s.repo.CouponCashUserConfirmRollback(confirmId, rollBackAction); err != nil {
						return "", err
					}
					return "", err
				}
			}
			message = "COUPON_CASH_USER_SUCCESS"
		}
	}
	return message, nil
}

func (s *couponCashService) DeleteMainCouponCashUserById(body model.SoftDeleteCouponCashUserByCouponCashId) error {
	getCouponCash, err := s.repo.GetCouponCashById(body.Id)
	if err != nil {
		if err.Error() == recordNotFound {
			return badRequest("COUPON_CASH_NOT_FOUND")
		}
		return err
	}
	if getCouponCash.CouponCashStatusId == model.COUPON_DELETE {
		return badRequest("COUPON_CASH_STATUS_ALREADY_NOT_ACTIVE")
	}
	if err := s.repo.SoftDeleteCouponCashById(body); err != nil {
		return err
	}
	if err := s.repo.SoftDeleteCouponCashUserByCouponCashId(body); err != nil {
		return err
	}
	return nil
}

func (s *couponCashService) DeleteCouponCashUserById(body model.SoftDeleteCouponCashUserById) error {
	// GetCouponCashUserById(id int64) (*model.CouponCashUser, error)
	getCouponCashUser, err := s.repo.GetCouponCashUserById(body.Id)
	if err != nil {
		if err.Error() == recordNotFound {
			return badRequest("COUPON_CASH_USER_NOT_FOUND")
		}
		return err
	}

	if getCouponCashUser.CouponCashUserStatusId == model.COUPON_USER_PENDING {
		if err := s.repo.DeleteCouponCashUserById(body); err != nil {
			return err
		}
	} else if getCouponCashUser.CouponCashUserStatusId == model.COUPON_USER_RECEIVE_ON_TURNOVER {
		var couponCashUserCheckTurn model.CouponCashUserDeletedTurnStatementRequest
		couponCashUserCheckTurn.UserId = getCouponCashUser.UserId
		couponCashUserCheckTurn.RefTypeId = getCouponCashUser.Id

		getCouponCashUserCheckTurnStatement, _ := s.repo.CouponCashUserCheckTurnStatementDeleted(couponCashUserCheckTurn)

		if getCouponCashUserCheckTurnStatement != nil {

			var updateTurnoverUserStatement model.TurnoverUserStatementUpdateBody
			setTotalTurnAmount := 0.0
			setTimeTurnAt := time.Now().UTC()
			updateTurnoverUserStatement.StatusId = model.TURNOVER_STATEMENT_STATUS_CANCELED
			updateTurnoverUserStatement.TotalTurnAmount = &setTotalTurnAmount
			updateTurnoverUserStatement.EndTurnAt = &setTimeTurnAt
			if err := s.repo.UpdateTurnoverUserStatement(getCouponCashUserCheckTurnStatement.Id, updateTurnoverUserStatement); err != nil {
				log.Println("UpdateTurnoverUserStatement", err)
			}

			// create turnover withdraw log
			var createTurnoverWithDrawLog model.CreateTurnoverUserWithdrawLog
			createTurnoverWithDrawLog.UserId = getCouponCashUser.UserId
			createTurnoverWithDrawLog.LogKey = fmt.Sprintf("COUPON_CASH_ID%dU%dCANCELED%dd%s", getCouponCashUser.Id, getCouponCashUser.UserId, getCouponCashUser.Id, time.Now().UTC().Format("20060102150405"))
			createTurnoverWithDrawLog.TotalWithdrawPrice = 0
			createTurnoverWithDrawLog.CurrentTurn = 0
			createTurnoverWithDrawLog.PlayTotal = 0
			createTurnoverWithDrawLog.LastPlayY = 0
			createTurnoverWithDrawLog.LastTotalX = 0
			_, err := s.repo.CreateTurnoverUserWithdrawLog(createTurnoverWithDrawLog)
			if err != nil {

				log.Println("CreateTurnoverUserWithdrawLog", err)
			}

			// update coupon cash user status
			if err := s.repo.UpdateCouponCashToSuccessWithId(getCouponCashUser.Id); err != nil {
				log.Println("UpdateCouponCashToSuccessWithId", err)
			}
		}

	} else {
		return badRequest("COUPON_CASH_USER_STATUS_NOT_PENDING")
	}
	return nil
}

func (s *couponCashService) CheckCouponTurnOverWithdraw(userId int64, withdrawAmount float64) (string, float64, error) {
	message := model.COUPON_TURNOVER_PASS
	turnoverAmount := float64(0)

	// ClearWithdrawCouponCashUserUpdateToSuccess(userId int64) error
	if err := s.repo.ClearWithdrawCouponCashUserUpdateToSuccess(userId); err != nil {
		log.Println("ClearWithdrawCouponCashUserUpdateToSuccess", err)
	}

	// get coupon cash user on withdraw trunover
	couponCashUser, err := s.repo.GetCouponCashUserWithdrawTurnOverByUserId(userId)
	if err != nil {
		return "", turnoverAmount, err
	}
	if couponCashUser != nil {
		var turnCalculate float64
		var fillterCouponLowestDate = time.Now().UTC()
		var saveCouponCashUserId []int64
		for _, v := range couponCashUser {
			saveCouponCashUserId = append(saveCouponCashUserId, v.Id)
		}
		var couponCashUserCheckTurn model.CouponCashUserCheckTurnStatementRequest
		couponCashUserCheckTurn.UserId = userId
		couponCashUserCheckTurn.RefTypeId = saveCouponCashUserId

		getCouponCashUserCheckTurnStatement, err := s.repo.CouponCashUserCheckTurnStatement(couponCashUserCheckTurn)
		if err != nil {
			return "", turnoverAmount, err
		}

		if len(getCouponCashUserCheckTurnStatement) > 0 {
			for _, v := range getCouponCashUserCheckTurnStatement {
				turnCalculate += v.TotalTurnAmount
				if v.EndTurnAt != nil && v.EndTurnAt.Before(fillterCouponLowestDate) {
					fillterCouponLowestDate = v.CreatedAt
				}
			}
		}

		startSearchDateFrom := fillterCouponLowestDate.Add(7 * time.Hour).Format("2006-01-02")
		var UserTodayPlaylogListRequest model.UserTodayPlaylogListRequest
		UserTodayPlaylogListRequest.UserId = &userId
		UserTodayPlaylogListRequest.StatementDateStart = startSearchDateFrom
		UserTodayPlaylogList, _, err := s.repo.GetTodaySumUserPlayLogList(UserTodayPlaylogListRequest)
		if err != nil {
			return "", turnoverAmount, err
		}

		var userPlaylogGame float64
		var userPlaylogCasino float64
		var userPlaylogSport float64
		var userPlaylogTotal float64
		// check turn success on this day
		var checkTurnSuccessOnThisDay model.CheckTurnSuccessOnThisDayRequest
		checkTurnSuccessOnThisDay.UserId = userId
		checkTurnSuccessOnThisDay.StartDate = startSearchDateFrom
		getTurnSuccessOnThisDay, err := s.repo.CheckTurnSuccessOnThisDay(checkTurnSuccessOnThisDay)
		if err != nil {
			return "", turnoverAmount, err
		}
		if getTurnSuccessOnThisDay != nil && getTurnSuccessOnThisDay.SumTotalTurnAllAmount > 0 {
			turnCalculate += getTurnSuccessOnThisDay.SumTotalTurnAllAmount
		}

		for _, v := range UserTodayPlaylogList {
			userPlaylogGame += v.SumTurnGame
			userPlaylogCasino += v.SumTurnCasino
			userPlaylogSport += v.SumTurnSport
			userPlaylogTotal += v.SumTurnGame + v.SumTurnCasino + v.SumTurnSport
		}

		if len(UserTodayPlaylogList) > 0 {

			if userPlaylogTotal >= turnCalculate {
				message = model.COUPON_TURNOVER_PASS

				// clear turn over
				for _, v := range getCouponCashUserCheckTurnStatement {
					var updateTurnoverUserStatement model.TurnoverUserStatementUpdateBody
					setTotalTurnAmount := 0.0
					setTimeTurnAt := time.Now().UTC()
					updateTurnoverUserStatement.StatusId = model.TURNOVER_STATEMENT_STATUS_COMPLETED
					updateTurnoverUserStatement.TotalTurnAmount = &setTotalTurnAmount
					updateTurnoverUserStatement.EndTurnAt = &setTimeTurnAt
					if err := s.repo.UpdateTurnoverUserStatement(v.Id, updateTurnoverUserStatement); err != nil {
						log.Println("UpdateTurnoverUserStatement", err)
					}

					// create turnover withdraw log
					var createTurnoverWithDrawLog model.CreateTurnoverUserWithdrawLog
					createTurnoverWithDrawLog.UserId = userId
					createTurnoverWithDrawLog.LogKey = fmt.Sprintf("COUPON_CASH_ID%dU%dd%s", v.RefTypeId, userId, time.Now().UTC().Format("20060102150405"))
					createTurnoverWithDrawLog.TotalWithdrawPrice = withdrawAmount
					createTurnoverWithDrawLog.CurrentTurn = turnCalculate
					createTurnoverWithDrawLog.PlayTotal = userPlaylogTotal
					createTurnoverWithDrawLog.LastPlayY = userPlaylogTotal
					createTurnoverWithDrawLog.LastTotalX = userPlaylogTotal
					_, err := s.repo.CreateTurnoverUserWithdrawLog(createTurnoverWithDrawLog)
					if err != nil {

						log.Println("CheckCouponTurnOverWithdraw.CreateTurnoverUserWithdrawLog", err)
					}

					// update coupon cash user status
					if err := s.repo.UpdateCouponCashToSuccessWithId(v.RefTypeId); err != nil {
						log.Println("CheckCouponTurnOverWithdraw.UpdateCouponCashToSuccessWithId", err)
					}

				}

			} else {

				getUser, err := s.repo.GetUser(userId)
				if err != nil {
					log.Println("CheckCouponTurnOverWithdraw.CheckPromotionWithdraw.GetUser", err)
					return "", turnoverAmount, err
				}

				configuration, _ := s.repo.GetConfiguration()
				if configuration != nil {
					if getUser.Credit < configuration.ClearTurnCreditLess {
						message = model.COUPON_TURNOVER_PASS

						// clear turn over
						for _, v := range getCouponCashUserCheckTurnStatement {
							var updateTurnoverUserStatement model.TurnoverUserStatementUpdateBody
							setTotalTurnAmount := 0.0
							setTimeTurnAt := time.Now().UTC()
							updateTurnoverUserStatement.StatusId = model.TURNOVER_STATEMENT_STATUS_CANCELED
							updateTurnoverUserStatement.TotalTurnAmount = &setTotalTurnAmount
							updateTurnoverUserStatement.EndTurnAt = &setTimeTurnAt
							if err := s.repo.UpdateTurnoverUserStatement(v.Id, updateTurnoverUserStatement); err != nil {
								log.Println("CheckCouponTurnOverWithdraw.UpdateTurnoverUserStatement", err)
							}

							// create turnover withdraw log
							var createTurnoverWithDrawLog model.CreateTurnoverUserWithdrawLog
							createTurnoverWithDrawLog.UserId = userId
							createTurnoverWithDrawLog.LogKey = fmt.Sprintf("COUPON_CASH_ID%dU%dCLEAR%s", v.RefTypeId, userId, time.Now().UTC().Format("20060102150405"))
							createTurnoverWithDrawLog.TotalWithdrawPrice = withdrawAmount
							createTurnoverWithDrawLog.CurrentTurn = turnCalculate
							createTurnoverWithDrawLog.PlayTotal = userPlaylogTotal
							createTurnoverWithDrawLog.LastPlayY = userPlaylogTotal
							createTurnoverWithDrawLog.LastTotalX = userPlaylogTotal
							_, err := s.repo.CreateTurnoverUserWithdrawLog(createTurnoverWithDrawLog)
							if err != nil {

								log.Println("CheckCouponTurnOverWithdraw.CreateTurnoverUserWithdrawLog", err)
							}

							// update coupon cash user status
							if err := s.repo.UpdateCouponCashToSuccessWithId(v.RefTypeId); err != nil {
								log.Println("CheckCouponTurnOverWithdraw.UpdateCouponCashToSuccessWithId", err)
							}
						}

					} else {
						message = model.COUPON_TURNOVER_FAIL
						// set ให้เป็น 1 เพราะจะต้องเล่นเให้ได้มากกว่า
						turnoverAmount = turnCalculate - userPlaylogTotal
					}
				}

			}
		} else {
			message = model.COUPON_TURNOVER_FAIL
			// set ให้เป็น 1 เพราะจะต้องเล่นเให้ได้มากกว่า
			turnoverAmount = turnCalculate
			getUser, err := s.repo.GetUser(userId)
			if err != nil {
				log.Println("CheckCouponTurnOverWithdraw.CheckPromotionWithdraw.GetUser", err)
				return "", turnoverAmount, err
			}

			configuration, _ := s.repo.GetConfiguration()
			if configuration != nil {
				if getUser.Credit < configuration.ClearTurnCreditLess {
					message = model.COUPON_TURNOVER_PASS

					// clear turn over
					for _, v := range getCouponCashUserCheckTurnStatement {
						var updateTurnoverUserStatement model.TurnoverUserStatementUpdateBody
						setTotalTurnAmount := 0.0
						setTimeTurnAt := time.Now().UTC()
						updateTurnoverUserStatement.StatusId = model.TURNOVER_STATEMENT_STATUS_CANCELED
						updateTurnoverUserStatement.TotalTurnAmount = &setTotalTurnAmount
						updateTurnoverUserStatement.EndTurnAt = &setTimeTurnAt
						if err := s.repo.UpdateTurnoverUserStatement(v.Id, updateTurnoverUserStatement); err != nil {
							log.Println("CheckCouponTurnOverWithdraw.UpdateTurnoverUserStatement", err)
						}

						// create turnover withdraw log
						var createTurnoverWithDrawLog model.CreateTurnoverUserWithdrawLog
						createTurnoverWithDrawLog.UserId = userId
						createTurnoverWithDrawLog.LogKey = fmt.Sprintf("COUPON_CASH_ID%dU%dCLEAR%s", v.RefTypeId, userId, time.Now().UTC().Format("20060102150405"))
						createTurnoverWithDrawLog.TotalWithdrawPrice = withdrawAmount
						createTurnoverWithDrawLog.CurrentTurn = turnCalculate
						createTurnoverWithDrawLog.PlayTotal = userPlaylogTotal
						createTurnoverWithDrawLog.LastPlayY = userPlaylogTotal
						createTurnoverWithDrawLog.LastTotalX = userPlaylogTotal
						_, err := s.repo.CreateTurnoverUserWithdrawLog(createTurnoverWithDrawLog)
						if err != nil {

							log.Println("CheckCouponTurnOverWithdraw.CreateTurnoverUserWithdrawLog", err)
						}

						// update coupon cash user status
						if err := s.repo.UpdateCouponCashToSuccessWithId(v.RefTypeId); err != nil {
							log.Println("CheckCouponTurnOverWithdraw.UpdateCouponCashToSuccessWithId", err)
						}
					}

				} else {
					message = model.COUPON_TURNOVER_FAIL
					// set ให้เป็น 1 เพราะจะต้องเล่นเให้ได้มากกว่า
					turnoverAmount = turnCalculate - userPlaylogTotal
				}
			}
		}

	} else {
		return message, turnoverAmount, nil
	}
	return message, turnoverAmount, nil
}

func (s *couponCashService) CanceledAllTurnCoupon(userId int64) error {

	// ClearWithdrawCouponCashUserUpdateToSuccess(userId int64) error
	if err := s.repo.ClearWithdrawCouponCashUserUpdateToSuccess(userId); err != nil {
		log.Println("ClearWithdrawCouponCashUserUpdateToSuccess", err)
	}

	// get coupon cash user on withdraw trunover
	couponCashUser, err := s.repo.GetCouponCashUserWithdrawTurnOverByUserId(userId)
	if err != nil {
		return nil
	}
	if couponCashUser != nil {
		var saveCouponCashUserId []int64
		for _, v := range couponCashUser {
			saveCouponCashUserId = append(saveCouponCashUserId, v.Id)
		}
		var couponCashUserCheckTurn model.CouponCashUserCheckTurnStatementRequest
		couponCashUserCheckTurn.UserId = userId
		couponCashUserCheckTurn.RefTypeId = saveCouponCashUserId

		getCouponCashUserCheckTurnStatement, err := s.repo.CouponCashUserCheckTurnStatement(couponCashUserCheckTurn)
		if err != nil {
			return nil
		}

		// clear turn over
		for _, v := range getCouponCashUserCheckTurnStatement {
			var updateTurnoverUserStatement model.TurnoverUserStatementUpdateBody
			setTotalTurnAmount := 0.0
			setTimeTurnAt := time.Now().UTC()
			updateTurnoverUserStatement.StatusId = model.TURNOVER_STATEMENT_STATUS_CANCELED
			updateTurnoverUserStatement.TotalTurnAmount = &setTotalTurnAmount
			updateTurnoverUserStatement.EndTurnAt = &setTimeTurnAt
			if err := s.repo.UpdateTurnoverUserStatement(v.Id, updateTurnoverUserStatement); err != nil {
				log.Println("CheckCouponTurnOverWithdraw.UpdateTurnoverUserStatement", err)
			}

			// create turnover withdraw log
			var createTurnoverWithDrawLog model.CreateTurnoverUserWithdrawLog
			createTurnoverWithDrawLog.UserId = userId
			createTurnoverWithDrawLog.LogKey = fmt.Sprintf("COUPON_CASH_ID%dU%dCLEAR%s", v.RefTypeId, userId, time.Now().UTC().Format("20060102150405"))
			createTurnoverWithDrawLog.TotalWithdrawPrice = 0
			createTurnoverWithDrawLog.CurrentTurn = 0
			createTurnoverWithDrawLog.PlayTotal = 0
			createTurnoverWithDrawLog.LastPlayY = 0
			createTurnoverWithDrawLog.LastTotalX = 0
			_, err := s.repo.CreateTurnoverUserWithdrawLog(createTurnoverWithDrawLog)
			if err != nil {

				log.Println("CheckCouponTurnOverWithdraw.CreateTurnoverUserWithdrawLog", err)
			}

			// update coupon cash user status
			if err := s.repo.UpdateCouponCashToSuccessWithId(v.RefTypeId); err != nil {
				log.Println("CheckCouponTurnOverWithdraw.UpdateCouponCashToSuccessWithId", err)
			}
		}

	}
	return nil
}

func (s *couponCashService) CouponCashSummary(req model.CouponCashSummaryRequest) (*model.CouponCashSummaryResponse, error) {

	record, err := s.repo.CouponCashSummary(req)
	if err != nil {
		return nil, err
	}
	return record, nil
}
func (s *couponCashService) CouponCashUserSummary(req model.CouponCashUserSummaryRequest) (*model.CouponCashUserSummaryResponse, error) {

	record, err := s.repo.CouponCashUserSummary(req)
	if err != nil {
		return nil, err
	}
	return record, nil
}
