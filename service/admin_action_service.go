package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
)

type AdminActionService interface {
	GetAdminActionLogList(req model.AdminActionLogListRequest) (*model.SuccessWithPagination, error)
	CreateSuccessAdminAction(req model.AdminActionCreateRequest) (*int64, error)
	CreateFailedAdminAction(req model.AdminActionCreateRequest) (*int64, error)
	// Options
	GetAdminActionTypeOptions() ([]model.SelectOptions, error)
}

type adminActionService struct {
	repo repository.AdminActionRepository
}

func NewAdminActionService(
	repo repository.AdminActionRepository,
) AdminActionService {
	return &adminActionService{repo}
}

func (s *adminActionService) GetAdminActionTypeOptions() ([]model.SelectOptions, error) {

	list, err := s.repo.GetAdminActionTypeOptions()
	if err != nil {
		return nil, internalServerError(err)
	}
	return list, nil
}

func (s *adminActionService) GetAdminActionLogList(req model.AdminActionLogListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	list, total, err := s.repo.GetAdminActionLogList(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	return &model.SuccessWithPagination{
		List:  list,
		Total: total,
	}, nil
}

func (s *adminActionService) CreateSuccessAdminAction(req model.AdminActionCreateRequest) (*int64, error) {

	var createBody model.AdminActionCreateBody
	createBody.AdminId = req.AdminId
	createBody.TypeId = req.TypeId
	createBody.IsSuccess = true
	createBody.IsShow = true
	createBody.RefObjectId = req.RefObjectId
	createBody.Detail = req.Detail
	createBody.JsonInput = req.JsonInput
	createBody.JsonOutput = req.JsonOutput
	insertId, err := s.repo.CreateAdminAction(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}
	return insertId, nil
}

func (s *adminActionService) CreateFailedAdminAction(req model.AdminActionCreateRequest) (*int64, error) {

	var createBody model.AdminActionCreateBody
	createBody.AdminId = req.AdminId
	createBody.TypeId = req.TypeId
	createBody.RefObjectId = req.RefObjectId
	createBody.Detail = req.Detail
	createBody.JsonInput = req.JsonInput
	createBody.JsonOutput = req.JsonOutput
	insertId, err := s.repo.CreateAdminAction(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}
	return insertId, nil
}
