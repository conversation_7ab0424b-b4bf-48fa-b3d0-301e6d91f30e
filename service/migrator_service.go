package service

import (
	"crypto/rc4"
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"fmt"
	"log"
	"strings"
	"time"
)

type MigratorService interface {
	MigratorUsers() error
	MigratorPartners() error
	MigratorUserAffiliate() (*model.MigratorResponse, error)
	MigratorUserMissingAffiliate() (*model.MigratorResponse, error)
	MigratorUserFirstDepositTransaction() (*model.MigratorResponse, error)
	MigratorSetOldUserFirstDeposit(req model.MigratorOldUserListRequest) (*model.MigratorOldUserFirstDepResponse, error)
	// Bcel
	MigratorBcelUsers() error
}

type migratorService struct {
	repo repository.MigratorRepository
}

func NewMigratorService(
	repo repository.MigratorRepository,
) MigratorService {
	return &migratorService{repo}
}

func (s *migratorService) Rc4Encrypt(plaintext string, secret string) (string, error) {

	var result string
	// ENCRYPT
	c, err := rc4.NewCipher([]byte(secret))
	if err != nil {
		log.Fatalln(err)
	}
	src := []byte(plaintext)
	// fmt.Println("Plaintext: ", src)

	dst := make([]byte, len(src))
	c.XORKeyStream(dst, src)
	// fmt.Println("Ciphertext: ", dst)
	result = string(dst)

	return result, err
}

func (s *migratorService) Rc4Decrypt(passPhase string, secret string) (string, error) {

	var result string
	// DECRYPT
	c2, err := rc4.NewCipher([]byte(secret))
	if err != nil {
		log.Fatalln(err)
	}

	dst := []byte(passPhase)
	src2 := make([]byte, len(dst))
	c2.XORKeyStream(src2, dst)
	// fmt.Println("Plaintext': ", src2)
	result = string(src2)
	return result, err
}

func (s *migratorService) rc4DecryptHexToByte(rawHex string, secret string) (string, error) {

	var result string
	// DECRYPT
	c2, err := rc4.NewCipher([]byte(secret))
	if err != nil {
		log.Fatalln(err)
	}

	// rawHex := "c05ee87ee0982086"
	dst := make([]byte, len(rawHex)/2)
	if _, err := fmt.Sscanf(rawHex, "%x", &dst); err != nil {
		return "", err
	}

	src2 := make([]byte, len(dst))
	c2.XORKeyStream(src2, dst)
	// fmt.Println("Plaintext': ", src2)
	result = string(src2)
	return result, err
}

func (s *migratorService) MigratorBankId(oldBankId int64) int64 {

	// convert from
	// CASE WHEN t.mem_bank = '1'THEN 'กรุงเทพ' WHEN t.mem_bank =
	// '2'THEN 'กรุงศรีอยุธยา' WHEN t.mem_bank =
	// '3'THEN 'กรุงไทย' WHEN t.mem_bank =
	// '4'THEN 'กสิกรไทย' WHEN t.mem_bank =
	// '5'THEN 'ไทยพาณิชย์' WHEN t.mem_bank =
	// '6'THEN 'ทหารไทยธนชาต' WHEN t.mem_bank =
	// '7'THEN 'ออมสิน' WHEN t.mem_bank =
	// '8'THEN 'ธนชาต' WHEN t.mem_bank =
	// '9'THEN 'เพื่อการเกษตรและสหกรณ์'  WHEN t.mem_bank =
	// '10'THEN 'ยูโอบี'  WHEN t.mem_bank =
	// '24'THEN 'ซีไอเอ็มบี'  WHEN t.mem_bank =
	// '17'THEN 'ธนาคารอาคารสงเคราะห์'  WHEN t.mem_bank =
	// '18'THEN 'ธนาคารอิสลามแห่งประเทศไทย'  WHEN t.mem_bank =
	// '19'THEN 'ทิสโก้'  WHEN t.mem_bank =
	// '20'THEN 'ซิตี้แบงก์'  WHEN t.mem_bank =
	// '21'THEN 'เกียรตินาคิน'  WHEN t.mem_bank =
	// '11'THEN 'สแตนดาร์ดชาร์เตอร์ด'  WHEN t.mem_bank =
	// '14'THEN 'แลนด์ แอนด์ เฮ้าส์'  WHEN t.mem_bank =
	// '15'THEN 'TrueMoney Wallet'   WHEN t.mem_bank =
	// '16'THEN'ซีไอเอ็มบี'
	// ELSE '-'
	// END as ธนาคาร,

	// into new bank id
	// INSERT INTO `bank` (`id`, `name`, `code`, `icon_url`, `type_flag`, `created_at`) VALUES
	// 	(1, 'กสิกรไทย', 'kbank', '', '********', '2023-10-27 17:19:18'),
	// 	(2, 'ไทยพาณิชย์', 'scb', '', '********', '2023-10-27 17:19:18'),
	// 	(3, 'กรุงเทพ', 'bbl', '', '********', '2023-10-27 17:19:18'),
	// 	(4, 'กรุงศรี', 'bay', '', '********', '2023-10-27 17:19:18'),
	// 	(5, 'กรุงไทย', 'ktb', '', '********', '2023-10-27 17:19:18'),
	// 	(6, 'ทีเอ็มบีธนชาต', 'ttb', '', '********', '2023-10-27 17:19:18'),
	// 	(7, 'ออมสิน', 'gsb', '', '********', '2023-10-27 17:19:18'),
	// 	(8, 'ธกส', 'baac', '', '********', '2023-10-27 17:19:18'),
	// 	(9, 'เกียรตินาคิน', 'kk', '', '********', '2023-10-27 17:19:18'),
	// 	(10, 'อาคารสงเคราะห์', 'ghb', '', '********', '2023-10-27 17:19:18'),
	// 	(11, 'ยูโอบี', 'uob', '', '********', '2023-10-27 17:19:18'),
	// 	(12, 'แลนด์ แอนด์ เฮ้าส์', 'lh', '', '********', '2023-10-27 17:19:18'),
	// 	(13, 'ซีไอเอ็มบี', 'cimb', '', '********', '2023-10-27 17:19:18'),
	// 	(14, 'เอชเอสบีซี', 'hsbc', '', '********', '2023-10-27 17:19:18'),
	// 	(15, 'ไอซีบีซี', 'icbc', '', '********', '2023-10-27 17:19:18');
	// 	(16, 'ธนาคารอิสลาม', 'isbt', '', '********', '2023-10-30 17:19:18');
	// 	(17, 'ทิสโก้', 'tisco', '', '********', '2023-10-30 17:19:18');
	// 	(18, 'ซิตี้แบงก์', 'citi', '', '********', '2023-10-30 17:19:18');
	// 	(19, 'สแตนดาร์ดชาร์เตอร์ด', 'scbt', '', '********', '2023-10-30 17:19:18');
	// 	(20, 'TrueMoney Wallet', 'true', '', '********', '2023-10-30 17:19:18');
	newBankId := int64(0)
	switch oldBankId {
	case 1:
		// กรุงเทพ => กรุงเทพ(3)
		newBankId = 3
	case 2:
		// กรุงศรีอยุธยา => กรุงศรี(4)
		newBankId = 4
	case 3:
		// กรุงไทย => กรุงไทย(5)
		newBankId = 5
	case 4:
		// กสิกรไทย => กสิกรไทย(1)
		newBankId = 1
	case 5:
		// ไทยพาณิชย์ => ไทยพาณิชย์(2)
		newBankId = 2
	case 6:
		// ทหารไทยธนชาต => ทีเอ็มบีธนชาต(6)
		newBankId = 6
	case 7:
		// ออมสิน => ออมสิน(7)
		newBankId = 7
	case 8:
		// ธนชาต => ทีเอ็มบีธนชาต(6)
		newBankId = 6
	case 9:
		// เพื่อการเกษตรและสหกรณ์ => ธกส(8)
		newBankId = 8
	case 10:
		// ยูโอบี => ยูโอบี(11)
		newBankId = 11
	case 11:
		// สแตนดาร์ดชาร์เตอร์ด =>  สแตนดาร์ดชาร์เตอร์ด(19)
		newBankId = 19
	case 14:
		// แลนด์ แอนด์ เฮ้าส์ => แลนด์ แอนด์ เฮ้าส์(12)
		newBankId = 12
	case 15:
		// TrueMoney Wallet => TrueMoney Wallet(20)
		newBankId = 20
	case 16:
		// ซีไอเอ็มบี => ซีไอเอ็มบี(13)
		newBankId = 13
	case 17:
		// ธนาคารอาคารสงเคราะห์ => อาคารสงเคราะห์(10)
		newBankId = 10
	case 18:
		// ธนาคารอิสลามแห่งประเทศไทย => ธนาคารอิสลาม(16)
		newBankId = 16
	case 19:
		// ทิสโก้ => ทิสโก้(17)
		newBankId = 17
	case 20:
		// ซิตี้แบงก์ => ซิตี้แบงก์(18)
		newBankId = 18
	case 21:
		// เกียรตินาคิน => เกียรตินาคิน(9)
		newBankId = 9
	case 24:
		// ซีไอเอ็มบี => ซีไอเอ็มบี(13)
		newBankId = 13
	}

	return newBankId
}

func (s *migratorService) MigratorUsers() error {

	rcSecret := "#$%^&DFs18Ga0f69d6af0e9adfH6565a"

	var query model.MigratorUserListRequest
	list, _, err := s.repo.GetPendingOldUserList(query)
	if err != nil {
		log.Println(err)
		return err
	}

	var createList = make(map[string]model.MigratorUserCreateBody, 0)
	var phoneList []string
	for _, v := range list {
		plainPassword, err := s.rc4DecryptHexToByte(v.Password, rcSecret)
		if err != nil {
			log.Println(err)
			continue
		}

		hashedPassword, err := helper.GenUserPassword(plainPassword)
		if err != nil {
			log.Println(err)
			continue
		}

		// P.layer ******** แก้ให้ gen เองจาก API แต่ case migration จะไม่แก้ โอกาศ env ไม่มี
		encrypt := helper.Encode(plainPassword)
		verifiedAt := time.Now()
		newBankId := s.MigratorBankId(v.BankId)

		phone := strings.TrimSpace(v.Phone)
		updateAt := verifiedAt
		if v.UpdatedAt != nil {
			updateAt = *v.UpdatedAt
		}
		lastActionAt := updateAt
		if v.LogedinAt != nil {
			lastActionAt = *v.LogedinAt
		}
		newUser := model.MigratorUserCreateBody{
			Id:           v.Id,
			MemberCode:   v.MemberCode,
			RefBy:        v.RefBy,
			Username:     v.Username,
			Phone:        phone,
			Password:     hashedPassword,
			Fullname:     v.Fullname,
			Firstname:    v.Firstname,
			Lastname:     v.Lastname,
			UserStatusId: v.UserStatusId,
			UserTypeId:   model.USER_TYPE_AFFILIATE, // ทุกคนมี member และแชร์ลิ้งได้
			BankAccount:  v.BankAccount,
			BankId:       newBankId,
			ChannelId:    v.ChannelId,
			LineId:       v.LineId,
			Encrypt:      encrypt,
			VerifiedAt:   verifiedAt,
			LastActionAt: lastActionAt,
			CreatedAt:    v.CreatedAt,
			UpdatedAt:    updateAt,
		}

		phoneList = append(phoneList, phone)
		createList[phone] = newUser

		if len(createList) >= 100 {
			// check exists

			for k := range createList {
				phoneList = append(phoneList, k)
			}
			dbList, _, err := s.repo.GetUserListByPhoneList(phoneList)
			if err != nil {
				// cant check
				return internalServerError(err)
			}
			// if exists, remove from createList
			for _, dbItem := range dbList {
				delete(createList, dbItem.Phone)
			}
			if len(createList) > 0 {
				err = s.repo.CreateTransferUserBulk(createList)
				if err != nil {
					log.Println(err)
					continue
				}
			}
			createList = make(map[string]model.MigratorUserCreateBody, 0)
			phoneList = make([]string, 0)
		}
	}
	// LEFT OVER
	if len(createList) > 0 {
		// check exists
		for k := range createList {
			phoneList = append(phoneList, k)
		}
		dbList, _, err := s.repo.GetUserListByPhoneList(phoneList)
		if err != nil {
			// cant check
			return internalServerError(err)
		}
		// if exists, remove from createList
		for _, dbItem := range dbList {
			delete(createList, dbItem.Phone)
		}
		if len(createList) > 0 {
			err = s.repo.CreateTransferUserBulk(createList)
			if err != nil {
				log.Println(err)
			}
		}
	}

	return nil
}

func (s *migratorService) MigratorUserAffiliate() (*model.MigratorResponse, error) {

	var result model.MigratorResponse

	// select all user that has ref_by
	var query model.UserHasRefByListRequest
	userList, _, err := s.repo.GetUserHasRefList(query)
	if err != nil {
		return nil, err
	}

	// 1. check tb_affiliate(member) user exist
	for _, v := range userList {
		if v.RefBy > 0 {
			refUser, err := s.repo.GetRefUserByRefId(v.RefBy)
			if err != nil {
				result.ErrorCount++
			}
			if refUser.UserTypeId == model.USER_TYPE_AFFILIATE {
				// CREATE affiliate IF NOT EXIST
				if err := s.repo.CreateAffiliateMember(v.RefBy, v.Id); err != nil {
					result.ErrorCount++
				} else {
					result.SuccessCount++
				}
			} else if refUser.UserTypeId == model.USER_TYPE_ALLIANCE {
				// CREATE alliance IF NOT EXIST
				if err := s.repo.CreateAllianceMember(v.RefBy, v.Id); err != nil {
					result.ErrorCount++
				} else {
					result.SuccessCount++
				}
			}
		}
	}

	// select all user that user_type_id = 2
	var query2 model.UserHasRefByListRequest
	affUserList, _, err := s.repo.GetAffiliateRefererList(query2)
	if err != nil {
		return nil, err
	}

	for _, v := range affUserList {
		// 3. update user_affiliate(Referer Income)
		if err := s.repo.UpdateAffiliateSummary(v.RefBy); err != nil {
			result.ErrorCount++
		} else {
			result.SuccessCount++
		}
	}

	return &result, nil
}

func (s *migratorService) MigratorPartners() error {

	var query model.MigratorPartnerListRequest
	list, _, err := s.repo.GetPendingOldPartnerList(query)
	if err != nil {
		return err
	}

	// later : bulk insert
	// from old db to user_alliance table by member_code
	// v.MpartSport อันนี้ % พันธมิตร => alliance_percent
	// v.MpartComSport อันนี้คอมพันธมิตร => commission_percent
	for _, v := range list {
		user, err := s.repo.GetOldUserByMemberCode(v.MpartMemCode)
		if err != nil {
			log.Println(err)
			continue
		}
		var createBody = map[string]interface{}{
			"alias":              v.MpartMemCode,
			"alliance_percent":   v.MpartSport,
			"commission_percent": v.MpartComSport,
			"user_id":            user.Id,
		}
		if err := s.repo.CreateUserAlliance(createBody); err != nil {
			log.Println(err)
			continue
		}
		// set user_type_id = 3 if user is partner/alliance
		var updateBody = map[string]interface{}{
			"user_type_id": model.USER_TYPE_ALLIANCE,
		}
		if err := s.repo.UpdateTransferUser(user.Id, updateBody); err != nil {
			log.Println(err)
			continue
		}
	}

	return nil
}

func (s *migratorService) MigratorUserMissingAffiliate() (*model.MigratorResponse, error) {

	var result model.MigratorResponse

	// select all user that has ref_by
	var query model.UserHasRefByListRequest
	query.Limit = 1000
	query.Page = 0
	userList, total, err := s.repo.GetMissingAffiliateUserList(query)
	if err != nil {
		return nil, err
	}
	result.SuccessCount = total

	// 1. check tb_affiliate(member) user exist
	for _, v := range userList {
		if v.UserTypeId == model.USER_TYPE_AFFILIATE {
			if err := s.repo.UpdateAffiliateSummary(v.Id); err != nil {
				result.ErrorCount++
			} else {
				result.SuccessCount++
			}
		}
	}

	return &result, nil
}

func (s *migratorService) MigratorUserFirstDepositTransaction() (*model.MigratorResponse, error) {

	var result model.MigratorResponse

	// select all user that has ref_by
	var query model.UserHasRefByListRequest
	query.Limit = 1000
	query.Page = 0
	transactionList, total, err := s.repo.GetMissingFirstTransactionList(query)
	if err != nil {
		return nil, err
	}
	result.DataCount = total

	// fmt.Println("userList", userList)

	// 1. Match by user_id and transfer_at and credit_amount
	for _, v := range transactionList {
		if err := s.repo.MatchUserTransactionAsFirstDeposit(v); err != nil {
			result.ErrorCount++
		} else {
			result.SuccessCount++
		}
	}

	return &result, nil
}

func (s *migratorService) MigratorSetOldUserFirstDeposit(query model.MigratorOldUserListRequest) (*model.MigratorOldUserFirstDepResponse, error) {

	var result model.MigratorOldUserFirstDepResponse

	query.Limit = 0
	query.Page = 0
	userList, _, err := s.repo.GetOldUserMemberList(query)
	if err != nil {
		return nil, err
	}

	var userIds []int64
	for _, v := range userList {
		userIds = append(userIds, v.Id)
	}
	result.DataCount = int64(len(userIds))

	// if len(userIds) > 0 {
	// 	list, err := s.repo.GetOldDepositInfoList(userIds)
	// 	if err != nil {
	// 		return nil, err
	// 	}
	// 	fmt.Println("list", helper.StructJson(list))
	// }

	// var infoList []model.MigratorOldUserDeposit
	// for _, user := range userList {
	// 	if info, err := s.repo.GetOldDepositInfoList(user.Id); err != nil {
	// 		return nil, err
	// 	} else {
	// 		infoList = append(infoList, *info)
	// 	}
	// }

	var infoList []model.MigratorOldUserDeposit
	for _, user := range userList {
		if info, err := s.repo.SetOldDepositInfoList(user.Id); err != nil {
			infoList = append(infoList, model.MigratorOldUserDeposit{
				UserId:          user.Id,
				DepositCount:    -1,
				HasFirstDeposit: false,
				FirstDepositId:  -1,
				NewDepositId:    -1,
			})
			result.ErrorCount++
		} else {
			infoList = append(infoList, *info)
			result.SuccessCount++
		}
	}
	result.Data = infoList

	return &result, nil
}

func (s *migratorService) MigratorBcelUsers() error {

	var query model.MigratorUserListRequest
	list, _, err := s.repo.GetPendingBcelUserList(query)
	if err != nil {
		log.Println(err)
		return err
	}

	var createList = make(map[string]model.MigratorUserCreateBody, 0)
	var phoneList []string
	for _, v := range list {

		plainPassword := v.ImiPassword
		hashedPassword := v.Password

		// P.layer ******** แก้ให้ gen เองจาก API แต่ case migration จะไม่แก้ โอกาศ env ไม่มี
		encrypt := helper.Encode(plainPassword)
		verifiedAt := time.Now()
		newBankId := int64(8899) // bcel

		phone := strings.TrimSpace(v.Phone)
		updateAt := verifiedAt
		if v.UpdatedAt != nil {
			updateAt = *v.UpdatedAt
		}
		lastActionAt := updateAt
		if v.LogedinAt != nil {
			lastActionAt = *v.LogedinAt
		}
		newUser := model.MigratorUserCreateBody{
			Id:           v.Id,
			MemberCode:   v.MemberCode,
			RefBy:        v.RefBy,
			Username:     v.Username,
			Phone:        phone,
			Password:     hashedPassword,
			Fullname:     v.Fullname,
			Firstname:    v.Firstname,
			Lastname:     v.Lastname,
			UserStatusId: v.UserStatusId,
			UserTypeId:   model.USER_TYPE_AFFILIATE, // ทุกคนมี member และแชร์ลิ้งได้
			BankAccount:  v.BankAccount,
			BankId:       newBankId,
			ChannelId:    v.ChannelId,
			LineId:       v.LineId,
			Encrypt:      encrypt,
			VerifiedAt:   verifiedAt,
			LastActionAt: lastActionAt,
			CreatedAt:    v.CreatedAt,
			UpdatedAt:    updateAt,
		}

		phoneList = append(phoneList, phone)
		createList[phone] = newUser

		if len(createList) >= 100 {
			// check exists

			for k := range createList {
				phoneList = append(phoneList, k)
			}
			dbList, _, err := s.repo.GetUserListByPhoneList(phoneList)
			if err != nil {
				// cant check
				return internalServerError(err)
			}
			// if exists, remove from createList
			for _, dbItem := range dbList {
				delete(createList, dbItem.Phone)
			}
			if len(createList) > 0 {
				err = s.repo.CreateTransferUserBulk(createList)
				if err != nil {
					log.Println(err)
					continue
				}
			}
			createList = make(map[string]model.MigratorUserCreateBody, 0)
			phoneList = make([]string, 0)
		}
	}
	// LEFT OVER
	if len(createList) > 0 {
		// check exists
		for k := range createList {
			phoneList = append(phoneList, k)
		}
		dbList, _, err := s.repo.GetUserListByPhoneList(phoneList)
		if err != nil {
			// cant check
			return internalServerError(err)
		}
		// if exists, remove from createList
		for _, dbItem := range dbList {
			delete(createList, dbItem.Phone)
		}
		if len(createList) > 0 {
			err = s.repo.CreateTransferUserBulk(createList)
			if err != nil {
				log.Println(err)
			}
		}
	}

	return nil
}
