package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"fmt"
	"log"
	"math"
	"math/rand"
	"net/http"
	"os"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"gorm.io/gorm"
)

type PromotionWebService interface {
	//promotion option
	GetPromotionWebType() ([]model.PromotionWebTypeResponse, error)
	GetPromotionWebStatus(req model.PromotionWebOptionRequest) ([]model.PromotionWebStatusResponse, error)
	GetPromotionWebBonusCondition() ([]model.PromotionWebBonusConditionResponse, error)
	GetPromotionWebBonusType() ([]model.PromotionWebBonusTypeResponse, error)
	GetPromotionWebTurnoverType() ([]model.PromotionWebTurnoverTypeResponse, error)
	GetpromotionWebDateType() ([]model.GetpromotionWebDateTypeResponse, error)
	GetPromotionWebUserStatus() ([]model.GetpromotionWebDateTypeResponse, error)
	//promotion web
	GetPromotionWebList(req model.PromotionWebGetListRequest) (*model.SuccessWithPagination, error)
	CreatePromotionWeb(req model.PromotionWebCreateRequest) (int64, error)
	GetPromotionWebById(id int64) (*model.PromotionWebGetByIdResponse, error)
	UpdatePromotionWeb(req model.PromotionWebUpdateRequest) error
	CancelPromotionWeb(req model.CancelPromotionWebRequest, expiredOrNot string) error
	GetUserPromotionWebByUserId(userId int64) (*model.PromotionWebUserByUserIdResponse, error)
	GetUserPromotionWebList(req model.PromotionWebUserGetListRequest) (*model.SuccessWithPagination, error)
	PromotionWebGetSildeListOnlyActive() ([]model.PromotionWebGetSildeListOnlyActive, error)
	UploadPromotionCoverToCloudFlare(imageFileBody *http.Request) (*model.FileUploadResponse, error)
	SortPromotionWebPriorityOrder(req model.DragSortRequest) error
	// Promotion-Report
	GetUserPromotionReportList(req model.GetUserPromotionReportListRequest) (*model.SuccessWithPagination, error)
	//promotion web user
	CreateUserCollectPromotionWeb(req model.PromotionWebUserCreateRequest) (int64, error)
	ShowPromotionWebForUser(userId int64) ([]model.ShowPromotionWebForUserResponse, error)
	ShowPromotionWebForUserById(userId int64, reqId int64) (*model.ShowPromotionWebForUserResponse, error)
	CancelPromotionWebUserById(req model.CancelPromotionWebUserById) error
	PromotionWebUserGetListByUserId(req model.PromotionWebUserGetListByUserIdRequest) (*model.SuccessWithPagination, error)
	GetPromotionWebUserWinLoseSummaryCurrentUser(promotionWebUserId int64) (*model.PromotionWebUserCurrentWinLostResponse, error)
	//promotion web internal ไว้ใช้ภายใน
	GetPromotionWebInternalList(req model.PromotionWebGetInternalListRequest) (*model.SuccessWithPagination, error)
	SingleWinLoseByMember(memberCode model.AgcSimpleWinloseRequest) (*model.AgcSimpleWinloseResponse, error)
	GetDepositCurrentProcessingUserPromotion(userId int64) (*model.PromotionWebUserByUserIdResponse, error)
	GetWithdrawCurrentProcessingUserPromotion(userId int64) (*model.PromotionWebUserByUserIdResponse, error)

	//public
	ShowPromotionWebForPublic() ([]model.ShowPromotionWebForUserResponse, error)

	// user for other service deposit and withdraw
	CheckUserPromotion(body model.CheckUserPromotionBody) (string, error)
	CheckPromotionWithdraw(userId int64, creditWithdraw float64) (string, error)
	AutoGenerateUserForTest() (*model.UserFormCreate, error)
	CheckUserPromotionOnlyNewMemberFree(body model.CheckUserPromotionBody) (string, error)
	// AutoGenerateUserUntilColletedPromotion() (*model.UserFormCreate, error)

	//Main turnover clear
	CanceledAllTurnPromotion(userId int64) error

	DeletePromotionWeb(req model.DeletePromotionWebRequest) error

	PromotionWebSummary(req model.PromotionWebGetListSummaryRequest) (*model.PromotionWebGetListSummaryResponse, error)
	PromotionWebUserSummary(req model.PromotionWebUserGetListSummaryRequest) (*model.PromotionWebUserGetListSummaryResponse, error)

	UploadImageToS3PromotionCover(imageFileBody *http.Request) (*model.FileUploadResponse, error)
	LockCreditPromotionUpdate(adminId int64, req model.LockCreditPromotionUpdateRequest) error
	CheckIsLockedCreditPromotionByUserId(userId int64) (bool, error)
	CheckIsLockedCreditPromotion(userId int64) (*model.LockCreditPromotionUpdateResposnse, error)

	GetPromotionWebUserStatusOptions() ([]model.SelectOptions, error)
	GetLockCreditWithdrawList(req model.GetLockCreditWithdrawListRequest) (*model.SuccessWithPagination, error)
	UnLockCreditWithdraw(req model.UpdateLockCreditWithdrawRequest) error

	CheckLockCreditWithdrawByUserId(userId int64) ([]model.CheckLockCreditWithdrawByUserId, error)
	MigrateBackUpLockCreditBack() (*model.MigrateBackUpLockCreditBackResponse, error)

	ShowPromotionWebForHiddenUrl(userId int64, hiddenUrlLink string) (*model.ShowPromotionWebForUserResponse, error)

	ShowPromotionWebById(reqId int64) (*model.PromotionWebGetByIdResponse, error)
}

type promotionWebService struct {
	repo        repository.PromotionWebRepository
	shareDb     *gorm.DB
	serviceNoti NotificationService
	serviceUser UserService
}

func NewPromotionWebService(
	repo repository.PromotionWebRepository,
	shareDb *gorm.DB,
	serviceNoti NotificationService,
	serviceUser UserService,
) PromotionWebService {
	return &promotionWebService{repo, shareDb, serviceNoti, serviceUser}
}

func (s promotionWebService) GetPromotionWebType() ([]model.PromotionWebTypeResponse, error) {

	option, err := s.repo.GetPromotionWebType()
	if err != nil {
		return nil, err
	}
	// open all type 20240205
	// var filteredOption []model.PromotionWebTypeResponse
	// for _, v := range option {
	// 	if v.Id == model.PROMOTION_WEB_TYPE_NEW_MEMBER_CONDITION || v.Id == model.PROMOTION_WEB_TYPE_FIRST_DEPOSIT || v.Id == model.PROMOTION_WEB_TYPE_DEPOSIT_MINIMUM_PER_DAY || v.Id == model.PROMOTION_WEB_TYPE_DEPOSIT_PER_DAY || v.Id == model.PROMOTION_WEB_TYPE_FIRST_DEPOSIT_OF_DAY || v.Id == model.PROMOTION_WEB_TYPE_DEPOSIT_BY_TIME {
	// 		filteredOption = append(filteredOption, v)
	// 	}
	// }

	return option, nil
}

func (s promotionWebService) GetPromotionWebStatus(req model.PromotionWebOptionRequest) ([]model.PromotionWebStatusResponse, error) {

	option, err := s.repo.GetPromotionWebStatus()
	if err != nil {
		return nil, err
	}

	// [2023/12/13] เปลี่ยนแบบเอา ครบทุกอัน
	selected := strings.ToUpper(req.Selected)
	if selected == "ALL" {
		return option, nil
	}

	var filteredOption []model.PromotionWebStatusResponse
	for _, v := range option {
		if v.Id != model.PROMOTION_WEB_STATUS_CANCELED {
			filteredOption = append(filteredOption, v)
		}
	}

	return filteredOption, nil
}

func (s promotionWebService) GetPromotionWebBonusCondition() ([]model.PromotionWebBonusConditionResponse, error) {

	option, err := s.repo.GetPromotionWebBonusCondition()
	if err != nil {
		return nil, err
	}
	return option, nil
}

func (s promotionWebService) GetPromotionWebBonusType() ([]model.PromotionWebBonusTypeResponse, error) {

	option, err := s.repo.GetPromotionWebBonusType()
	if err != nil {
		return nil, err
	}
	return option, nil
}

func (s promotionWebService) GetPromotionWebTurnoverType() ([]model.PromotionWebTurnoverTypeResponse, error) {

	option, err := s.repo.GetPromotionWebTurnoverType()
	if err != nil {
		return nil, err
	}

	// PROMOTION_WEB_TURN_OVER_TYPE_ALL       = int64(1)
	// PROMOTION_WEB_TURN_OVER_TYPE_SPORT     = int64(2)
	// PROMOTION_WEB_TURN_OVER_TYPE_CASINO    = int64(3)
	// PROMOTION_WEB_TURN_OVER_TYPE_SLOT      = int64(4)
	// PROMOTION_WEB_TURN_OVER_TYPE_P2P       = int64(5) // close
	// PROMOTION_WEB_TURN_OVER_TYPE_LOTTERY   = int64(6)
	// PROMOTION_WEB_TURN_OVER_TYPE_FINANCIAL = int64(7) // close

	// 	โปรโมชั่นจะ ปิดประเภท P2P  ไม่ให้เลือก

	// - เนื่องด้วย P2P จะดึง เครดิต ออกจาก agent หมดเลยย
	// - ทำให้ พอ เครดิตต่ำกว่า  ยอดในตัว จะ ล้างเทิร์น ทันที

	var result []model.PromotionWebTurnoverTypeResponse
	for _, v := range option {
		if v.Id == model.PROMOTION_WEB_TURN_OVER_TYPE_ALL || v.Id == model.PROMOTION_WEB_TURN_OVER_TYPE_SPORT || v.Id == model.PROMOTION_WEB_TURN_OVER_TYPE_CASINO || v.Id == model.PROMOTION_WEB_TURN_OVER_TYPE_SLOT || v.Id == model.PROMOTION_WEB_TURN_OVER_TYPE_LOTTERY {
			result = append(result, v)
		}
	}

	return result, nil
}

func (s promotionWebService) GetpromotionWebDateType() ([]model.GetpromotionWebDateTypeResponse, error) {

	option, err := s.repo.GetpromotionWebDateType()
	if err != nil {
		return nil, err
	}
	return option, nil
}

func (s promotionWebService) CreatePromotionWeb(req model.PromotionWebCreateRequest) (int64, error) {

	id, err := s.repo.CreatePromotionWeb(req)
	if err != nil {
		return 0, err
	}

	// [update priority order]
	if err := s.repo.UpdatePromotionWebPriorityOrderCreate(id); err != nil {
		return 0, err
	}

	return id, nil
}

func (s promotionWebService) GetPromotionWebById(id int64) (*model.PromotionWebGetByIdResponse, error) {

	promotionWeb, err := s.repo.GetPromotionWebById(id)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(recordNotFound)
		}
		return nil, internalServerError(err)
	}

	return promotionWeb, nil
}

func (s promotionWebService) GetPromotionWebUserStatus() ([]model.GetpromotionWebDateTypeResponse, error) {
	list, err := s.repo.GetPromotionWebUserStatus()
	if err != nil {
		return nil, err
	}

	return list, nil
}

func (s promotionWebService) GetPromotionWebInternalList(req model.PromotionWebGetInternalListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.Pagination(&req.Page, &req.Limit); err != nil {
		return nil, err
	}

	promotionWeb, total, err := s.repo.GetPromotionWebInternalList(req)
	if err != nil {
		return nil, err
	}

	var response model.SuccessWithPagination
	response.List = promotionWeb
	response.Total = total

	return &response, nil
}

func (s promotionWebService) UpdatePromotionWeb(req model.PromotionWebUpdateRequest) error {

	if err := s.repo.UpdatePromotionWeb(req); err != nil {
		return err
	}

	checkStatusCancel := model.PROMOTION_WEB_STATUS_CANCELED
	if req.PromotionWebStatusId != nil && req.PromotionWebStatusId == &checkStatusCancel {
		// CancelPromotionWeb
		var cancelPromotionWebBody model.CancelPromotionWebRequest
		cancelPromotionWebBody.CanceledByAdminId = req.UpdatedByAdminId
		cancelPromotionWebBody.PromotionWebStatusId = model.PROMOTION_WEB_STATUS_CANCELED
		cancelPromotionWebBody.CanceledAt = time.Now().UTC()
		cancelPromotionWebBody.Id = req.Id
		if err := s.repo.CancelPromotionWeb(cancelPromotionWebBody); err != nil {
			return err
		}
	}

	return nil
}

func (s promotionWebService) CancelPromotionWeb(req model.CancelPromotionWebRequest, expiredOrNot string) error {

	if req.PromotionWebStatusId == 0 {
		req.PromotionWebStatusId = model.PROMOTION_WEB_STATUS_CANCELED
	}
	if err := s.repo.CancelPromotionWeb(req); err != nil {
		return err
	}

	promotionWebUserToCancel, err := s.repo.GetPromotionWebUserToCancel(req.Id)
	if err != nil {
		return err
	}

	var promotionWebUserIdCancel []int64
	var promotionWebUserConfirmIdWithdrawConfirm []int64
	for _, v := range promotionWebUserToCancel {
		if v.PromotionWebUserStatusId == model.PROMOTION_WEB_USER_STATUS_ON_PROCESS {
			promotionWebUserIdCancel = append(promotionWebUserIdCancel, v.Id)
		}
		if v.PromotionWebUserStatusId == model.PROMOTION_WEB_USER_STATUS_ON_WITHDRAW {
			promotionWebUserConfirmIdWithdrawConfirm = append(promotionWebUserConfirmIdWithdrawConfirm, v.Id)
		}
	}

	if len(promotionWebUserIdCancel) > 0 {
		// get promotion web user confirm
		promotionWebUserConfirm, err := s.repo.GetPromotionWebUserConfirmByPromotionWebUserId(promotionWebUserIdCancel)
		if err != nil {
			return err
		}

		// update confirm action key
		for _, v := range promotionWebUserConfirm {
			var updateConfirm model.PromotionWebUserConfirmUpdateRequest
			updateConfirm.Id = v.Id
			updateConfirm.ActionKey = fmt.Sprintf("P%dU%d#C%d", v.PromotionWebId, v.UserId, v.PromotionWebUserId)
			if err := s.repo.UpdatePromotionWebUserConfirmById(updateConfirm); err != nil {
				return err
			}
		}

		// update promotion web user
		var updatePromotionUser model.CancelPromotionWebUserByPromotionWebId
		updatePromotionUser.PromotionWebId = req.Id
		updatePromotionUser.CanceledByAdminId = req.CanceledByAdminId
		updatePromotionUser.CanceledAt = req.CanceledAt
		if err := s.repo.ExpiredPromotionWebUserByIds(updatePromotionUser); err != nil {
			return err
		}

	}
	// [ที่ทำ แบบนี้เพราะ มีการเรียกอันเดียวกัน แต่ ที่ ติดเทิร์น จะติดไปเรื่อยๆ]
	if expiredOrNot == "EXPIRED_NOT_CANCELED" {
		return nil
	}
	if len(promotionWebUserConfirmIdWithdrawConfirm) > 0 {
		// get promotion web user confirm
		promotionWebUserConfirm, err := s.repo.GetPromotionWebUserConfirmByPromotionWebUserId(promotionWebUserConfirmIdWithdrawConfirm)
		if err != nil {
			return err
		}

		// update confirm action key
		for _, v := range promotionWebUserConfirm {
			var updateConfirm model.PromotionWebUserConfirmUpdateRequest
			updateConfirm.Id = v.Id
			updateConfirm.ActionKey = fmt.Sprintf("P%dU%d#S%d", v.PromotionWebId, v.UserId, v.Id)
			if err := s.repo.UpdatePromotionWebUserConfirmById(updateConfirm); err != nil {
				return err
			}

			var promotionWebCheckTurnStatement model.PromotionWebCheckTurnStatementRequest
			promotionWebCheckTurnStatement.UserId = v.UserId
			promotionWebCheckTurnStatement.RefTypeId = v.PromotionWebUserId
			turnStatement, _ := s.repo.PromotionWebCheckTurnStatement(promotionWebCheckTurnStatement)
			if turnStatement != nil {
				var updateTurnoverUserStatement model.TurnoverUserStatementUpdateBody
				setTotalTurnAmount := 0.0
				setTimeTurnAt := time.Now().UTC()
				updateTurnoverUserStatement.StatusId = model.TURNOVER_STATEMENT_STATUS_CANCELED
				updateTurnoverUserStatement.TotalTurnAmount = &setTotalTurnAmount
				updateTurnoverUserStatement.EndTurnAt = &setTimeTurnAt
				if err := s.repo.UpdateTurnoverUserStatement(turnStatement.Id, updateTurnoverUserStatement); err != nil {
					logName := "CancelPromotionWeb.UPDATE_TURNOVER_USER_STATEMENT"
					logReq := helper.StructJson(updateTurnoverUserStatement)
					logResult := fmt.Sprintf("message : %s , promotionBonusCredit : %f, err : %s", "update turnover user statement", turnStatement.BonusAmount, err)
					if err := s.repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
						return err
					}
					return err
				}
			}
		}
	}
	// update promotion web user
	var updatePromotionUser model.SuccessPromotionWebUserByPromotionWebId
	updatePromotionUser.PromotionWebId = req.Id
	if err := s.repo.ExpiredSuccessPromotionWebUserById(updatePromotionUser); err != nil {
		return err
	}

	return nil
}

func (s promotionWebService) AutoCancelPromotionWebTypeAllPerDay() error {
	// [if skip on day of type PROMOTION_WEB_TYPE_DEPOSIT_MINIMUM_PER_DAY and PROMOTION_WEB_TYPE_DEPOSIT_PER_DAY will expired]

	promotionWebUserToCancel, err := s.repo.GetExpiredPerDayPromotion()
	if err != nil {
		return err
	}

	if len(promotionWebUserToCancel) > 0 {
		var promotionWebUserIdCancel []int64
		for _, v := range promotionWebUserToCancel {
			if v.PromotionWebUserStatusId == model.PROMOTION_WEB_USER_STATUS_ON_PROCESS {
				promotionWebUserIdCancel = append(promotionWebUserIdCancel, v.Id)
			}
		}

		if len(promotionWebUserIdCancel) > 0 {
			// get promotion web user confirm
			promotionWebUserConfirm, err := s.repo.GetPromotionWebUserConfirmByPromotionWebUserId(promotionWebUserIdCancel)
			if err != nil {
				return err
			}

			// update confirm action key
			for _, v := range promotionWebUserConfirm {
				var updateConfirm model.PromotionWebUserConfirmUpdateRequest
				updateConfirm.Id = v.Id
				updateConfirm.ActionKey = fmt.Sprintf("P%dU%d#C%d", v.PromotionWebId, v.UserId, v.PromotionWebUserId)
				if err := s.repo.UpdatePromotionWebUserConfirmById(updateConfirm); err != nil {
					return err
				}
			}

			if err := s.repo.ExpiredExpiredPerDayPromotion(promotionWebUserIdCancel); err != nil {
				return err
			}

		}
	}

	return nil
}

func (s promotionWebService) AutoSuccessPromotionOnWithdraw() error {

	promotionWebUser, err := s.repo.AutoSuccessPromotionOnWithdraw()
	if err != nil {
		return err
	}

	var promotionWebUserConfirmIdWithdrawConfirm []int64
	for _, v := range promotionWebUser {
		if v.PromotionWebUserStatusId == model.PROMOTION_WEB_USER_STATUS_ON_WITHDRAW {
			promotionWebUserConfirmIdWithdrawConfirm = append(promotionWebUserConfirmIdWithdrawConfirm, v.Id)
		}
	}

	if len(promotionWebUserConfirmIdWithdrawConfirm) > 0 {
		// get promotion web user confirm
		// AutoSuccessPromotionOnWithdrawNextDay(NextDay string, PromotionWebUserIds []int64) ([]model.PromotionWebUserConfirm, error)
		checkStartDate := time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02")

		promotionWebUserConfirm, err := s.repo.AutoSuccessPromotionOnWithdrawNextDay(checkStartDate, promotionWebUserConfirmIdWithdrawConfirm)
		if err != nil {
			return err
		}
		var promotionWebUserId []int64
		// update confirm action key
		for _, v := range promotionWebUserConfirm {
			var updateConfirm model.PromotionWebUserConfirmUpdateRequest
			updateConfirm.Id = v.Id
			updateConfirm.ActionKey = fmt.Sprintf("P%dU%d#S%d", v.PromotionWebId, v.UserId, v.Id)
			if err := s.repo.UpdatePromotionWebUserConfirmById(updateConfirm); err != nil {
				return err
			}

			promotionWebUserId = append(promotionWebUserId, v.PromotionWebUserId)
		}

		if err := s.repo.ExpiredSuccessWithdrawPromotionWebUserByIds(promotionWebUserId); err != nil {
			return err
		}
	}

	return nil
}

func (s promotionWebService) GetPromotionWebList(req model.PromotionWebGetListRequest) (*model.SuccessWithPagination, error) {
	// UpdateExpiredPromotionWeb
	if err := s.UpdateExpiredPromotionWeb(); err != nil {
		return nil, nil
	}

	if err := helper.Pagination(&req.Page, &req.Limit); err != nil {
		return nil, err
	}

	list, total, err := s.repo.GetPromotionWebList(req)
	if err != nil {
		return nil, err
	}

	var response model.SuccessWithPagination
	response.List = list
	response.Total = total

	return &response, nil
}

func (s promotionWebService) CreateUserCollectPromotionWeb(req model.PromotionWebUserCreateRequest) (int64, error) {

	insertId, err := CreateUserCollectPromotionWeb(s.repo, req)
	if err != nil {
		return 0, err
	}

	return insertId, nil
}

func CreateUserCollectPromotionWeb(repo repository.PromotionWebRepository, req model.PromotionWebUserCreateRequest) (int64, error) {

	promotionWeb, err := repo.GetPromotionWebById(req.PromotionWebId)
	if err != nil {
		return 0, err
	}

	// [P.makold] change Requirement no stack if want to stack promotion Removed this
	// ชนิด เดียวกัน ซ่ำกันไม่ได้
	// แต่ต่างชนิด กดได้
	// if promotionWeb.PromotionWebTypeId != model.PROMOTION_WEB_TYPE_DEPOSIT_BY_TIME {

	checkIsAlreadyUserTypePromotion, err := repo.CheckAlreadyUserTypePromotionByUserId(req.UserId, promotionWeb.PromotionWebTypeId)
	if err != nil {
		return 0, err
	}

	if checkIsAlreadyUserTypePromotion {
		return 0, badRequest("ALREADY_USE_THIS_TYPE_PROMOTION_TODAY")
	}

	// }
	// [P.makold] change Requirement no stack with lock credit if want to stack promotion Removed this
	checkIsAlreadyLockCreditWithdraw, err := repo.CheckAlreadyLockCreditWithdrawByUserId(req.UserId)
	if err != nil {
		return 0, err
	}
	if checkIsAlreadyLockCreditWithdraw {
		return 0, badRequest("PROMOTION_NOTPASS_LOCK_CREDIT_WITHDRAW")
	}
	// [P.makold] change Requirement no stack with turn if want to stack promotion Removed this
	checkTurnoverStatement, err := repo.CheckTurnoverStatementByUserIdAndTypeId(req.UserId)
	if err != nil {
		return 0, err
	}
	if checkTurnoverStatement {
		return 0, badRequest("PROMOTION_NOTPASS_TURN_CREDIT_WITHDRAW ")
	}

	if promotionWeb.PromotionWebTypeId == model.PROMOTION_WEB_TYPE_NEW_MEMBER_FREE {
		// CheckUserPromotionOnlyNewMemberFreeV2(repo repository.PromotionWebRepository, body model.CheckUserPromotionBody) (string, error)
		var checkNewMember model.CheckUserPromotionBody
		checkNewMember.UserId = req.UserId
		checkNewMember.ImmediateWithPromotionUserId = &req.PromotionWebId
		if _, err := CheckUserPromotionOnlyNewMemberFreeV2(repo, checkNewMember); err != nil {
			log.Println("CheckUserPromotionOnlyNewMemberFree.error", err)
			return 0, err
		}
		return 0, err
	}
	insertId, err := CreateUserCollectPromotionWebNormal(repo, req)
	if err != nil {
		return 0, err
	}

	return insertId, nil
}

func CreateUserCollectPromotionWebNormal(repo repository.PromotionWebRepository, req model.PromotionWebUserCreateRequest) (int64, error) {

	todayDate, _ := GetDateToDay()

	// get promotion web
	promotionWeb, err := repo.GetPromotionWebById(req.PromotionWebId)
	if err != nil {
		return 0, err
	}

	// [first time promotion register]
	// if promotionWeb.PromotionWebTypeId == model.PROMOTION_WEB_TYPE_NEW_MEMBER_FREE {
	// 	var checkNewMember model.CheckUserPromotionBody
	// 	checkNewMember.UserId = req.UserId
	// 	checkNewMember.ImmediateWithPromotionUserId = &req.PromotionWebId
	// 	if _, err := CheckUserPromotionOnlyNewMemberFreeV2(repo, checkNewMember); err != nil {
	// 		log.Println("CheckUserPromotionOnlyNewMemberFree.error", err)
	// 	}

	// 	return 0, err
	// }

	avalibleCoupon, _ := repo.CheckAvalibleCouponAndPromtion(req.UserId, "PROMOTION")
	if avalibleCoupon != nil && avalibleCoupon.Id > 0 {
		return 0, badRequest("TURN_OVER_IN_COUPON")
	}

	// [get promotion by user id]
	promotionWebUserList, _ := repo.CheckCollectedUserPromotionWebByUserId(req.UserId)
	// [check promotion web user status]
	if len(promotionWebUserList) > 0 {
		for _, v := range promotionWebUserList {
			if v.PromotionWebUserStatusId == model.PROMOTION_WEB_USER_STATUS_ON_PROCESS || v.PromotionWebUserStatusId == model.PROMOTION_WEB_USER_STATUS_ON_WITHDRAW {
				logName := "CreateUserCollectPromotionWeb.ALREADY_ON_PROCESS_PROMOTION"
				logReq := req
				logResult := fmt.Sprintf("P%dU%d", v.PromotionWebId, v.UserId)
				if err := repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
					return 0, err
				}
				return 0, badRequest("ALREADY_ON_PROCESS_PROMOTION")
			}

			// [checker]
			if v.PromotionWebUserStatusId == model.PROMOTION_WEB_USER_STATUS_ON_PROCESS && v.PromotionWebId == req.PromotionWebId && v.UserId == req.UserId {
				logName := "CreateUserCollectPromotionWeb.ALREADY_ON_PROCESS_PROMOTION"
				logReq := req
				logResult := fmt.Sprintf("P%dU%d", v.PromotionWebId, v.UserId)
				if err := repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
					return 0, err
				}
				return 0, badRequest("ALREADY_ON_PROCESS_PROMOTION")
			}

			// [check success promotion that can collect only 1 time]
			if v.PromotionWebId == req.PromotionWebId && v.UserId == req.UserId && v.CreatedAt.Add(7*time.Hour).Format("2006-01-02") < todayDate.UTC().Add(7*time.Hour).Format("2006-01-02") && v.PromotionWebUserStatusId == model.PROMOTION_WEB_USER_STATUS_SUCCESS {
				if promotionWeb.PromotionWebTypeId == model.PROMOTION_WEB_TYPE_FIRST_DEPOSIT {
					logName := "CreateUserCollectPromotionWeb.PROMOTION_WEB_TYPE_FIRST_DEPOSIT.ALREADY_SUCCESS_PROMOTION"
					logReq := req
					logResult := fmt.Sprintf("P%dU%d", v.PromotionWebId, v.UserId)
					if err := repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
						return 0, err
					}
					return 0, badRequest("ALREADY_SUCCESS_PROMOTION")
				}
				if promotionWeb.PromotionWebTypeId == model.PROMOTION_WEB_TYPE_NEW_MEMBER_CONDITION {
					logName := "CreateUserCollectPromotionWeb.PROMOTION_WEB_TYPE_NEW_MEMBER_CONDITION.ALREADY_SUCCESS_PROMOTION"
					logReq := req
					logResult := fmt.Sprintf("P%dU%d", v.PromotionWebId, v.UserId)
					if err := repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
						return 0, err
					}
					return 0, badRequest("ALREADY_SUCCESS_PROMOTION")
				}
				if promotionWeb.PromotionWebTypeId == model.PROMOTION_WEB_TYPE_NEW_MEMBER_FREE {
					logName := "CreateUserCollectPromotionWeb.PROMOTION_WEB_TYPE_NEW_MEMBER_FREE.ALREADY_SUCCESS_PROMOTION"
					logReq := req
					logResult := fmt.Sprintf("P%dU%d", v.PromotionWebId, v.UserId)
					if err := repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
						return 0, err
					}
					return 0, badRequest("ALREADY_SUCCESS_PROMOTION")
				}
			}
		}
	}
	// [validate promotion web]

	if promotionWeb.PromotionWebStatusId == model.PROMOTION_WEB_STATUS_CANCELED {
		return 0, badRequest("CreateUserCollectPromotionWeb.THIS_PROMOTION_WEB_NOT_AVAILABLE")
	}

	// [first time deposit check]
	if promotionWeb.PromotionWebTypeId == model.PROMOTION_WEB_TYPE_NEW_MEMBER_CONDITION {

		user, _ := repo.GetUserById(req.UserId)
		if err != nil {
			return 0, err
		}

		if user != nil && user.MemberCode != nil {
			logName := "CreateUserCollectPromotionWeb.ALREADY_HAVE_MEMBER_CODE"
			logReq := req
			logResult := fmt.Sprintf("P%dU%d", promotionWeb.Id, req.UserId)
			if err := repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
				return 0, err
			}
			return 0, badRequest("YOU_ALREADY_HAVE_CREDIT")
		}

	}

	if promotionWeb.PromotionWebTypeId == model.PROMOTION_WEB_TYPE_FIRST_DEPOSIT {

		// get first deposit
		var getFirstDeposit model.GetUserFirstDepositForPromotion
		getFirstDeposit.UserId = req.UserId

		firstDeposit, _ := repo.CheckFirstTimeDepositPromotion(req.UserId)

		if firstDeposit != nil {
			logName := "CreateUserCollectPromotionWeb.ALREADY_FIRST_DEPOSIT"
			logReq := req
			logResult := fmt.Sprintf("P%dU%d", promotionWeb.Id, req.UserId)
			if err := repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
				return 0, err
			}
			return 0, badRequest("YOU_ALREADY_HAVE_CREDIT")
		}
	}

	// [member free credit will not be able to collect promotion here]
	// if req.Newmember == nil {
	// if promotionWeb.PromotionWebTypeId == model.PROMOTION_WEB_TYPE_NEW_MEMBER_FREE {
	// 	return 0, badRequest("CreateUserCollectPromotionWeb.NEW_MEMBER_CONDITION_NOT_ALLOW_TO_COLLECT_PROMOTION")
	// }
	// }

	// [time to collect promotion]
	if promotionWeb.PromotionWebDateTypeId == model.PROMOTION_WEB_DATE_TYPE_FIXED_DATE {
		startDate, endDate, err := ConvertStartAndEndDate(repo, promotionWeb.StartDate, promotionWeb.EndDate)
		if err != nil {
			return 0, err
		}

		if todayDate.Before(*startDate) && todayDate.After(*endDate) {
			logName := "CreateUserCollectPromotionWeb.PROMOTION_NOT_IN_TIME"
			logReq := req
			logResult := fmt.Sprintf("P%dU%d", promotionWeb.Id, req.UserId)
			if err := repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
				return 0, err
			}
			return 0, badRequest("CreateUserCollectPromotionWeb.PROMOTION_NOT_IN_TIME")
		}
	}

	// [action key]
	unqiueActionKey := fmt.Sprintf("P%dU%d", req.PromotionWebId, req.UserId)
	// [check action key]
	promotionWebUserConfirm, _ := repo.GetPromotionWebUserConfirmByActionKey(unqiueActionKey)
	if promotionWebUserConfirm != nil {
		logName := "CreateUserCollectPromotionWeb.ALREADY_COLLECTED_PROMOTION_CONFIRM"
		logReq := req
		logResult := fmt.Sprintf("find action key: %s, id: %d", unqiueActionKey, promotionWebUserConfirm)
		if err := repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
			return 0, err
		}
		return 0, badRequest("ALREADY_COLLECTED_PROMOTION_CONFIRM")
	}
	// [create action key race condition]
	var createInsertConfirm model.PromotionWebUserConfirmCreateRequest
	createInsertConfirm.ActionKey = unqiueActionKey
	createInsertConfirm.PromotionWebId = req.PromotionWebId
	createInsertConfirm.UserId = req.UserId
	insertConfirm, err := repo.CreatePromotionWebUserConfirmByActionKey(createInsertConfirm)
	if err != nil {
		logName := "CreateUserCollectPromotionWeb.USER_TRY_RACE_CONDITION"
		logReq := req
		logResult := createInsertConfirm
		if err := repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
			return 0, err
		}
		return 0, badRequest("ALREADY_COLLECTED_PROMOTION_CONFIRM")
	}

	var createdId int64
	if insertConfirm > 0 {
		// [create]
		var createBody model.PromotionWebUserCreateBody
		createBody.UserId = req.UserId
		createBody.PromotionWebId = req.PromotionWebId
		createBody.PromotionWebUserStatusId = model.PROMOTION_WEB_USER_STATUS_ON_PROCESS
		createBody.TotalAmount = 0
		createBody.TotalDepositAmount = 0
		createdId, err = repo.CreateUserCollectPromotionWeb(createBody)
		if err != nil {
			return 0, err
		}

		if err := repo.PromotionConfirmUpdatePromotionWebUser(insertConfirm, createdId); err != nil {
			return 0, err
		}

	}

	return createdId, nil
}

func (s promotionWebService) GetUserPromotionWebByUserId(userId int64) (*model.PromotionWebUserByUserIdResponse, error) {

	promotionWebUser, err := s.repo.GetUserPromotionWebByUserId(userId)
	if err != nil {
		return nil, err
	}

	return promotionWebUser, nil
}

func (s promotionWebService) GetUserPromotionWebList(req model.PromotionWebUserGetListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.Pagination(&req.Page, &req.Limit); err != nil {
		return nil, err
	}

	list, total, err := s.repo.GetUserPromotionWebList(req)
	if err != nil {
		return nil, err
	}

	var response model.SuccessWithPagination
	response.List = list
	response.Total = total
	response.Message = "success"

	return &response, nil
}

func (s promotionWebService) PromotionWebGetSildeListOnlyActive() ([]model.PromotionWebGetSildeListOnlyActive, error) {

	list, err := s.repo.PromotionWebGetSildeListOnlyActive()
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s promotionWebService) UploadPromotionCoverToCloudFlare(imageFileBody *http.Request) (*model.FileUploadResponse, error) {

	fileReader, newFileName, err := imageFileBody.FormFile("file")
	if err != nil {
		log.Println("UploadPromotionCoverToCloudFlare.ERROR.FormFile", err)
		return nil, err
	}

	filename := &newFileName.Filename
	folderName := "promo-cover"
	dbName := os.Getenv("DB_NAME")

	// [set imageCloudFlarePathName]
	pathName := fmt.Sprintf("cbgame/%v/%s/upload/image/", dbName, folderName)
	//! ส่ง Id กับไฟล์ reader
	var newImageId *model.FileUploadResponse
	fileData, err := s.repo.UploadImageToCloudflare(pathName, *filename, fileReader)
	if err != nil {
		log.Println("UploadPromotionCoverToCloudFlare.ERROR.UploadImageToCloudflare", err)
		return nil, badRequest("UNABLE_TO_UPLOAD_FILE")
	}

	newImageId = &model.FileUploadResponse{
		ImageUrl: fileData.FileUrl,
	}
	return newImageId, nil
}

func (s promotionWebService) ShowPromotionWebForUser(userId int64) ([]model.ShowPromotionWebForUserResponse, error) {

	showPromotionWebForUserAll, err := s.ShowPromotionWebForUserAll(userId)
	if err != nil {
		return nil, err
	}

	var showPromotionWebUser []model.ShowPromotionWebForUserResponse
	for _, v := range showPromotionWebForUserAll {
		if v.PromotionWebStatusId == model.PROMOTION_WEB_STATUS_ACTIVE || v.PromotionWebStatusId == model.PROMOTION_WEB_STATUS_ONLY_SHOW {
			showPromotionWebUser = append(showPromotionWebUser, v)
		}
	}

	return showPromotionWebUser, nil
}

func (s promotionWebService) ShowPromotionWebForUserAll(userId int64) ([]model.ShowPromotionWebForUserResponse, error) {

	// [Expired]
	if err := s.UpdateExpiredPromotionWeb(); err != nil {
		return nil, nil
	}

	// [promotion web]
	showPromotionWeb, err := s.repo.ShowPromotionWebForUser()
	if err != nil {
		return nil, err
	}

	// [promotion web user]
	var promotionWebId []int64
	for _, v := range showPromotionWeb {
		promotionWebId = append(promotionWebId, v.Id)
	}

	// [promotion web user only status success and on process] // กันcase ที่เคยยกเลิกและกดรับมาใหม่
	promotionWebUser, err := s.repo.GetUserPromotionWebInternalListById(promotionWebId, userId)
	if err != nil {
		return nil, err
	}

	// [set UserStatusWithPromotion]
	// GetDateToDay
	todayDate, _ := GetDateToDay()
	var showPromotionWebUser []model.ShowPromotionWebForUserResponse

	// ผมงานแร่งและร้อนแรง เพราะ object ทับกัน แต่ go ไม่มี pop ผมเลย ใช้ chat gpt ยังไม่ได้มา refactor code
	// Keep track of PromotionWebIds that have been processed
	var promotionWebUserFillter []model.PromotionWebUserGetListResponse
	// Map to store latest entries for each PromotionWebId
	latestEntries := make(map[int64]model.PromotionWebUserGetListResponse)

	// Iterate through promotionWebUser to find the latest entries for each PromotionWebId
	for _, v := range promotionWebUser {
		// Check if we already have an entry for this PromotionWebId
		if entry, exists := latestEntries[v.PromotionWebId]; exists {
			// Check the priority of the current entry against the existing one
			switch v.PromotionWebUserStatusId {
			case model.PROMOTION_WEB_USER_STATUS_ON_PROCESS:
				// If current entry has higher priority, replace the existing one
				if entry.PromotionWebUserStatusId != model.PROMOTION_WEB_USER_STATUS_ON_PROCESS {
					latestEntries[v.PromotionWebId] = v
				}
			case model.PROMOTION_WEB_USER_STATUS_ON_WITHDRAW:
				// If current entry has higher priority and the existing one is not PROMOTION_WEB_USER_STATUS_ON_PROCESS, replace it
				if entry.PromotionWebUserStatusId != model.PROMOTION_WEB_USER_STATUS_ON_PROCESS &&
					entry.PromotionWebUserStatusId != model.PROMOTION_WEB_USER_STATUS_ON_WITHDRAW {
					latestEntries[v.PromotionWebId] = v
				}
			case model.PROMOTION_WEB_USER_STATUS_SUCCESS:
				// If existing entry is not ON_PROCESS or ON_WITHDRAW, replace it
				if entry.PromotionWebUserStatusId != model.PROMOTION_WEB_USER_STATUS_ON_PROCESS &&
					entry.PromotionWebUserStatusId != model.PROMOTION_WEB_USER_STATUS_ON_WITHDRAW {
					latestEntries[v.PromotionWebId] = v
				}
			}
		} else {
			// If no existing entry for this PromotionWebId, add the current entry
			latestEntries[v.PromotionWebId] = v
		}
	}

	// Convert latestEntries map to slice for the filtered result
	for _, v := range latestEntries {
		promotionWebUserFillter = append(promotionWebUserFillter, v)
	}

	isFirstDeposit := s.repo.IsFirstDeposit(userId)

	for _, v := range showPromotionWeb {

		var startDate *time.Time
		var endDate *time.Time
		if v.StartDate != "" || v.EndDate != "" {
			startDate, endDate, err = ConvertStartAndEndDate(s.repo, v.StartDate, v.EndDate)
			if err != nil {
				return nil, badRequest("INVALID_DATE")
			}
		}
		// checkRegister, _ := s.repo.CheckRegisterMemberByUserIdAndPromotionId(userId, v.StartDate)

		if promotionWebUserFillter != nil {
			var found bool
			for _, v2 := range promotionWebUserFillter {

				if v.Id == v2.PromotionWebId {

					if v2.CreatedAt.Add(7*time.Hour).Format("2006-01-02") < todayDate.Add(7*time.Hour).Format("2006-01-02") && v2.PromotionWebUserStatusId == model.PROMOTION_WEB_USER_STATUS_SUCCESS {
						found = true
						if v.PromotionWebStatusId == model.PROMOTION_WEB_STATUS_ONLY_SHOW {
							v.UserStatusWithPromotion = "SHOW_ONLY"
						} else if v.PromotionWebTypeId == model.PROMOTION_WEB_TYPE_DEPOSIT_MINIMUM_PER_DAY {
							v.UserStatusWithPromotion = "AVAILABLE"
						} else if v.PromotionWebTypeId == model.PROMOTION_WEB_TYPE_DEPOSIT_PER_DAY {
							v.UserStatusWithPromotion = "AVAILABLE"
						} else if v.PromotionWebTypeId == model.PROMOTION_WEB_TYPE_DEPOSIT_BY_TIME {
							v.UserStatusWithPromotion = "AVAILABLE"
						} else if v.PromotionWebTypeId == model.PROMOTION_WEB_TYPE_FIRST_DEPOSIT_OF_DAY {
							v.UserStatusWithPromotion = "AVAILABLE"
						} else if v.PromotionWebTypeId == model.PROMOTION_WEB_TYPE_FIRST_DEPOSIT {
							v.UserStatusWithPromotion = "SUCCESS"
						} else {
							v.UserStatusWithPromotion = "SUCCESS"
						}
					} else if startDate != nil && todayDate.After(*startDate) && endDate != nil && todayDate.Before(*endDate) && v.PromotionWebDateTypeId == model.PROMOTION_WEB_DATE_TYPE_FIXED_DATE {
						found = true
						if v.PromotionWebStatusId == model.PROMOTION_WEB_STATUS_ONLY_SHOW {
							v.UserStatusWithPromotion = "SHOW_ONLY"
						} else if v2.PromotionWebUserStatusId == model.PROMOTION_WEB_USER_STATUS_SUCCESS {
							v.UserStatusWithPromotion = "SUCCESS"
						} else if v2.PromotionWebUserStatusId == model.PROMOTION_WEB_USER_STATUS_ON_PROCESS || v2.PromotionWebUserStatusId == model.PROMOTION_WEB_USER_STATUS_ON_WITHDRAW {
							v.UserStatusWithPromotion = "ON_PROCESS"
						} else if v.PromotionWebTypeId == model.PROMOTION_WEB_TYPE_FIRST_DEPOSIT && v2.PromotionWebUserStatusId == model.PROMOTION_WEB_USER_STATUS_SUCCESS {
							v.UserStatusWithPromotion = "SUCCESS"
						} else {
							v.UserStatusWithPromotion = "AVAILABLE"
						}
					} else {
						found = true
						if v.PromotionWebStatusId == model.PROMOTION_WEB_STATUS_ONLY_SHOW {
							v.UserStatusWithPromotion = "SHOW_ONLY"
						} else if v2.PromotionWebUserStatusId == model.PROMOTION_WEB_USER_STATUS_SUCCESS {
							v.UserStatusWithPromotion = "SUCCESS"
						} else if v2.PromotionWebUserStatusId == model.PROMOTION_WEB_USER_STATUS_ON_PROCESS || v2.PromotionWebUserStatusId == model.PROMOTION_WEB_USER_STATUS_ON_WITHDRAW {
							v.UserStatusWithPromotion = "ON_PROCESS"
						} else if v.PromotionWebTypeId == model.PROMOTION_WEB_TYPE_FIRST_DEPOSIT && v2.PromotionWebUserStatusId == model.PROMOTION_WEB_USER_STATUS_SUCCESS {
							v.UserStatusWithPromotion = "SUCCESS"
						} else {
							v.UserStatusWithPromotion = "AVAILABLE"
						}
					}
					break
				} else {
					found = true
					if v.PromotionWebStatusId == model.PROMOTION_WEB_STATUS_ONLY_SHOW {
						v.UserStatusWithPromotion = "SHOW_ONLY"
					} else if v.PromotionWebTypeId == model.PROMOTION_WEB_TYPE_NEW_MEMBER_FREE {
						v.UserStatusWithPromotion = "SHOW_ONLY"
					} else if !isFirstDeposit && v.PromotionWebTypeId == model.PROMOTION_WEB_TYPE_FIRST_DEPOSIT {
						v.UserStatusWithPromotion = "HIDE"
					} else if startDate != nil && todayDate.After(*startDate) && endDate != nil && todayDate.Before(*endDate) && v.PromotionWebDateTypeId == model.PROMOTION_WEB_DATE_TYPE_FIXED_DATE {
						v.UserStatusWithPromotion = "AVAILABLE"
					} else if v.PromotionWebDateTypeId == model.PROMOTION_WEB_DATE_TYPE_NON_FIXED_DATE {
						v.UserStatusWithPromotion = "AVAILABLE"
					} else {
						v.UserStatusWithPromotion = "NOT_AVAILABLE"
					}
				}
			}
			if found {
				showPromotionWebUser = append(showPromotionWebUser, v)
				continue
			}
		} else {
			if v.PromotionWebStatusId == model.PROMOTION_WEB_STATUS_ONLY_SHOW {
				v.UserStatusWithPromotion = "SHOW_ONLY"
			} else if v.PromotionWebTypeId == model.PROMOTION_WEB_TYPE_NEW_MEMBER_FREE { // && (checkRegister)
				v.UserStatusWithPromotion = "AVAILABLE"
			} else if !isFirstDeposit && v.PromotionWebTypeId == model.PROMOTION_WEB_TYPE_FIRST_DEPOSIT {
				v.UserStatusWithPromotion = "HIDE"
			} else if startDate != nil && todayDate.After(*startDate) && endDate != nil && todayDate.Before(*endDate) && v.PromotionWebDateTypeId == model.PROMOTION_WEB_DATE_TYPE_FIXED_DATE {
				v.UserStatusWithPromotion = "AVAILABLE"
			} else if v.PromotionWebDateTypeId == model.PROMOTION_WEB_DATE_TYPE_NON_FIXED_DATE {
				v.UserStatusWithPromotion = "AVAILABLE"
			} else {
				v.UserStatusWithPromotion = "NOT_AVAILABLE"
			}

			showPromotionWebUser = append(showPromotionWebUser, v)
		}

	}

	return showPromotionWebUser, nil
}

func (s promotionWebService) ShowPromotionWebForPublic() ([]model.ShowPromotionWebForUserResponse, error) {

	// [Expired]
	if err := s.UpdateExpiredPromotionWeb(); err != nil {
		return nil, nil
	}

	// [promotion web]
	showPromotionWeb, err := s.repo.ShowPromotionWebForPublic()
	if err != nil {
		return nil, err
	}

	return showPromotionWeb, nil
}

func GetDateToDay() (time.Time, error) {
	todayDate := time.Now().UTC()
	return todayDate, nil
}

func ConvertStartAndEndDate(repo repository.PromotionWebRepository, startDate string, endDate string) (*time.Time, *time.Time, error) {

	formattedStartDate, err := ConvertToFormattedDate(startDate)
	if err != nil {
		return nil, nil, err
	}

	formattedEndDate, err := ConvertToFormattedDate(endDate)
	if err != nil {
		return nil, nil, err
	}

	// Parse the formatted dates
	startDateFormat, err := repo.ParseBodBkk(formattedStartDate)
	if err != nil {
		return nil, nil, err
	}

	endDateFormat, err := repo.ParseEodBkk(formattedEndDate)
	if err != nil {
		return nil, nil, err
	}

	return startDateFormat, endDateFormat, nil
}

func ConvertToFormattedDate(dateStr string) (string, error) {
	parsedTime, err := time.Parse(time.RFC3339, dateStr)
	if err != nil {
		return "", err
	}

	// Format the time as "2006-01-02"
	formattedString := parsedTime.Format("2006-01-02")
	return formattedString, nil
}

func (s promotionWebService) UpdateExpiredPromotionWeb() error {
	// add the type of promotion web ที่จะทำ มา เพิ่่มใน  promotionWeb จบเลย ก่อนเข้า loop
	// [อันนี้ไปทำ ยกเลิก แค่ ของ type สะสม รายวัน]
	if err := s.AutoCancelPromotionWebTypeAllPerDay(); err != nil {
		logName := "UpdateExpiredPromotionWeb.AutoCancelPromotionWebTypeAllPerDay"
		logReq := "AutoCancelPromotionWebTypeAllPerDay"
		logResult := err
		if err := s.repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
			return err
		}
	}

	// [อันนี้ไปทำ ยกเลิก ตามวันหมดอายุ]
	expiredFromDate := time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02")
	promotionWeb, err := s.repo.GetExpiredPromotionWeb(expiredFromDate)
	if err != nil {
		return err
	}
	for _, v := range promotionWeb {

		var cancelPromotionWebBody model.CancelPromotionWebRequest

		cancelPromotionWebBody.CanceledByAdminId = 0
		// พี่มิงค์ บอก ให้ เปลี่ยนเป็นไม่แสดง แทนการยกเลิก
		cancelPromotionWebBody.PromotionWebStatusId = model.PROMOTION_WEB_STATUS_DISABLE_WEB
		cancelPromotionWebBody.CanceledAt = time.Now().UTC()
		cancelPromotionWebBody.Id = v.Id
		if err := s.CancelPromotionWeb(cancelPromotionWebBody, "EXPIRED_NOT_CANCELED"); err != nil {
			return err
		}
	}

	// [อันนี้คือ ทำการสำเร็จรายการที่ค้างถอนข้าม 1 วัน ตาม b3]
	// [20240220] เป็นการเปลี่ยน flow ถ้าติดถอนก็จะติดไปเรื่อยๆ
	// if err := s.AutoSuccessPromotionOnWithdraw(); err != nil {
	// 	logName := "UpdateExpiredPromotionWeb.AutoSuccessPromotionOnWithdraw"
	// 	logReq := "AutoSuccessPromotionOnWithdraw"
	// 	logResult := err
	// 	if err := s.repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
	// 		return err
	// 	}
	// }
	return nil
}

// func (s promotionWebService) UpdateExpiredPromotionWebBackupBeforeNewFlow() error {
// 	//  add the type of promotion web ที่จะทำ มา เพิ่่มใน  promotionWeb จบเลย ก่อนเข้า loop
// 	// [อันนี้ไปทำ ยกเลิก แค่ ของ type สะสม รายวัน]
// 	if err := s.AutoCancelPromotionWebTypeAllPerDay(); err != nil {
// 		logName := "UpdateExpiredPromotionWeb.AutoCancelPromotionWebTypeAllPerDay"
// 		logReq := "AutoCancelPromotionWebTypeAllPerDay"
// 		logResult := err
// 		if err := s.repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
// 			return err
// 		}
// 	}

// 	// [อันนี้ไปทำ ยกเลิก ตามวันหมดอายุ]
// 	expiredFromDate := time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02")
// 	promotionWeb, err := s.repo.GetExpiredPromotionWeb(expiredFromDate)
// 	if err != nil {
// 		return err
// 	}
// 	for _, v := range promotionWeb {

// 		var cancelPromotionWebBody model.CancelPromotionWebRequest

// 		cancelPromotionWebBody.CanceledByAdminId = 0
// 		cancelPromotionWebBody.PromotionWebStatusId = model.PROMOTION_WEB_STATUS_CANCELED
// 		cancelPromotionWebBody.Id = v.Id
// 		if err := s.CancelPromotionWeb(cancelPromotionWebBody); err != nil {
// 			return err
// 		}
// 	}

// 	// [อันนี้คือ ทำการสำเร็จรายการที่ค้างถอนข้าม 1 วัน ตาม b3]
// 	if err := s.AutoSuccessPromotionOnWithdraw(); err != nil {
// 		logName := "UpdateExpiredPromotionWeb.AutoSuccessPromotionOnWithdraw"
// 		logReq := "AutoSuccessPromotionOnWithdraw"
// 		logResult := err
// 		if err := s.repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
// 			return err
// 		}
// 	}
// 	return nil
// }

func (s promotionWebService) CancelPromotionWebUserById(req model.CancelPromotionWebUserById) error {

	var updateConfirm model.PromotionWebUserConfirmUpdateRequest

	// GetPromotionWebUserById(req model.GetPromotionWebUserById) (*model.GetPromotionWebUserByIdResponse, error)
	var reqGetPromotionWebUser model.GetPromotionWebUserById
	reqGetPromotionWebUser.Id = req.Id
	PromotionUser, err := s.repo.GetPromotionWebUserById(reqGetPromotionWebUser)
	if err != nil {
		if err.Error() == recordNotFound {
			return notFound(recordNotFound)
		}
		return err
	}

	if PromotionUser.PromotionWebUserStatusId == model.PROMOTION_WEB_USER_STATUS_SUCCESS {
		return badRequest("PROMOTION_WEB_USER_STATUS_SUCCESS")

	} else if PromotionUser.PromotionWebUserStatusId == model.PROMOTION_WEB_USER_STATUS_ON_WITHDRAW {

		actionkey := fmt.Sprintf("P%dU%d#WS%s", PromotionUser.PromotionWebId, PromotionUser.UserId, time.Now().UTC().Add(7*time.Hour).Format("2006-01-02"))
		promotionWebUserConfirm, err := s.repo.GetPromotionWebUserConfirmByActionKey(actionkey)
		if err != nil {
			if err.Error() == recordNotFound {
				return notFound(recordNotFound)
			}
			return err
		}

		updateConfirm.Id = *promotionWebUserConfirm
		updateConfirm.ActionKey = fmt.Sprintf("P%dU%d#S%d", PromotionUser.PromotionWebId, PromotionUser.UserId, *promotionWebUserConfirm)
		if err := s.repo.UpdatePromotionWebUserConfirmById(updateConfirm); err != nil {
			return err
		}
		// CancelPromotionWebUserToSuccess(req model.CancelPromotionWebUserToSuccess) error
		var updateBody model.CancelPromotionWebUserToSuccess
		updateBody.Id = req.Id
		if req.CanceledByAdminId != 0 {
			updateBody.CanceledByAdminId = req.CanceledByAdminId
		} else {
			updateBody.CanceledByAdminId = 0
		}
		updateBody.CanceledAt = time.Now().UTC()
		if err := s.repo.CancelPromotionWebUserToSuccess(updateBody); err != nil {
			return err
		}

		var promotionWebCheckTurnStatement model.PromotionWebCheckTurnStatementRequest
		promotionWebCheckTurnStatement.UserId = PromotionUser.UserId
		promotionWebCheckTurnStatement.RefTypeId = PromotionUser.Id
		turnStatement, _ := s.repo.PromotionWebCheckTurnStatement(promotionWebCheckTurnStatement)
		if turnStatement != nil {
			var updateTurnoverUserStatement model.TurnoverUserStatementUpdateBody
			setTotalTurnAmount := 0.0
			setTimeTurnAt := time.Now().UTC()
			updateTurnoverUserStatement.StatusId = model.TURNOVER_STATEMENT_STATUS_CANCELED
			updateTurnoverUserStatement.TotalTurnAmount = &setTotalTurnAmount
			updateTurnoverUserStatement.EndTurnAt = &setTimeTurnAt
			if err := s.repo.UpdateTurnoverUserStatement(turnStatement.Id, updateTurnoverUserStatement); err != nil {
				logName := "CancelPromotionWebUserById.UpdateTurnoverUserStatement"
				logReq := helper.StructJson(updateTurnoverUserStatement)
				logResult := fmt.Sprintf("message : %s , promotionBonusCredit : %f, err : %s", "update turnover user statement", turnStatement.BonusAmount, err)
				if err := s.repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
					return err
				}
				return err
			}
		}
	} else {

		actionkey := fmt.Sprintf("P%dU%d", PromotionUser.PromotionWebId, PromotionUser.UserId)
		promotionWebUserConfirm, err := s.repo.GetPromotionWebUserConfirmByActionKey(actionkey)
		if err != nil {
			if err.Error() == recordNotFound {
				return notFound(recordNotFound)
			}
			return err
		}

		updateConfirm.Id = *promotionWebUserConfirm
		updateConfirm.ActionKey = fmt.Sprintf("P%dU%d#C%d", PromotionUser.PromotionWebId, PromotionUser.UserId, req.Id)
		if err := s.repo.UpdatePromotionWebUserConfirmById(updateConfirm); err != nil {
			return err
		}

		if err := s.repo.CancelPromotionWebUserById(req); err != nil {
			return err
		}
	}
	return nil
}

func (s promotionWebService) ShowPromotionWebForUserById(userId int64, reqId int64) (*model.ShowPromotionWebForUserResponse, error) {

	getPromotionWeb, err := s.ShowPromotionWebForUserAll(userId)
	if err != nil {
		return nil, err
	}

	var showPromotionWebUser model.ShowPromotionWebForUserResponse
	for _, v := range getPromotionWeb {
		if v.Id == reqId {
			showPromotionWebUser = v
		}
	}

	return &showPromotionWebUser, nil
}

func (s promotionWebService) ShowPromotionWebById(reqId int64) (*model.PromotionWebGetByIdResponse, error) {

	promotionWeb, err := s.repo.GetPromotionWebById(reqId)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(recordNotFound)
		}
		return nil, internalServerError(err)
	}
	return promotionWeb, nil
}

func (s promotionWebService) ShowPromotionWebForHiddenUrl(userId int64, hiddenUrlLink string) (*model.ShowPromotionWebForUserResponse, error) {

	getPromotionWeb, err := s.ShowPromotionWebForHiddenUrlConfig(userId, hiddenUrlLink)
	if err != nil {
		return nil, err
	}

	if len(getPromotionWeb) == 0 {
		return nil, notFound("PROMOTION_NOT_FOUND")
	}

	var showPromotionWebUser model.ShowPromotionWebForUserResponse
	for _, v := range getPromotionWeb {
		if v.HiddenUrlLink == hiddenUrlLink {
			showPromotionWebUser = v
		}
	}

	return &showPromotionWebUser, nil
}

func (s promotionWebService) ShowPromotionWebForHiddenUrlConfig(userId int64, hiddenUrlLink string) ([]model.ShowPromotionWebForUserResponse, error) {

	// [Expired]
	if err := s.UpdateExpiredPromotionWeb(); err != nil {
		return nil, nil
	}
	// [promotion web]
	showPromotionWeb, err := s.repo.ShowPromotionWebForUserHiddenUrl(hiddenUrlLink)
	if err != nil {
		return nil, err
	}

	// [promotion web user]
	var promotionWebId []int64
	for _, v := range showPromotionWeb {
		promotionWebId = append(promotionWebId, v.Id)
	}

	// [promotion web user only status success and on process] // กันcase ที่เคยยกเลิกและกดรับมาใหม่
	promotionWebUser, err := s.repo.GetUserPromotionWebInternalListById(promotionWebId, userId)
	if err != nil {
		return nil, err
	}

	// [set UserStatusWithPromotion]
	// GetDateToDay
	todayDate, _ := GetDateToDay()
	var showPromotionWebUser []model.ShowPromotionWebForUserResponse

	// ผมงานแร่งและร้อนแรง เพราะ object ทับกัน แต่ go ไม่มี pop ผมเลย ใช้ chat gpt ยังไม่ได้มา refactor code
	// Keep track of PromotionWebIds that have been processed
	var promotionWebUserFillter []model.PromotionWebUserGetListResponse
	// Map to store latest entries for each PromotionWebId
	latestEntries := make(map[int64]model.PromotionWebUserGetListResponse)

	// Iterate through promotionWebUser to find the latest entries for each PromotionWebId
	for _, v := range promotionWebUser {
		// Check if we already have an entry for this PromotionWebId
		if entry, exists := latestEntries[v.PromotionWebId]; exists {
			// Check the priority of the current entry against the existing one
			switch v.PromotionWebUserStatusId {
			case model.PROMOTION_WEB_USER_STATUS_ON_PROCESS:
				// If current entry has higher priority, replace the existing one
				if entry.PromotionWebUserStatusId != model.PROMOTION_WEB_USER_STATUS_ON_PROCESS {
					latestEntries[v.PromotionWebId] = v
				}
			case model.PROMOTION_WEB_USER_STATUS_ON_WITHDRAW:
				// If current entry has higher priority and the existing one is not PROMOTION_WEB_USER_STATUS_ON_PROCESS, replace it
				if entry.PromotionWebUserStatusId != model.PROMOTION_WEB_USER_STATUS_ON_PROCESS &&
					entry.PromotionWebUserStatusId != model.PROMOTION_WEB_USER_STATUS_ON_WITHDRAW {
					latestEntries[v.PromotionWebId] = v
				}
			case model.PROMOTION_WEB_USER_STATUS_SUCCESS:
				// If existing entry is not ON_PROCESS or ON_WITHDRAW, replace it
				if entry.PromotionWebUserStatusId != model.PROMOTION_WEB_USER_STATUS_ON_PROCESS &&
					entry.PromotionWebUserStatusId != model.PROMOTION_WEB_USER_STATUS_ON_WITHDRAW {
					latestEntries[v.PromotionWebId] = v
				}
			}
		} else {
			// If no existing entry for this PromotionWebId, add the current entry
			latestEntries[v.PromotionWebId] = v
		}
	}

	// Convert latestEntries map to slice for the filtered result
	for _, v := range latestEntries {
		promotionWebUserFillter = append(promotionWebUserFillter, v)
	}

	isFirstDeposit := s.repo.IsFirstDeposit(userId)

	for _, v := range showPromotionWeb {
		fmt.Println("v", v)
		var startDate *time.Time
		var endDate *time.Time
		if v.StartDate != "" || v.EndDate != "" {
			startDate, endDate, err = ConvertStartAndEndDate(s.repo, v.StartDate, v.EndDate)
			if err != nil {
				return nil, badRequest("INVALID_DATE")
			}
		}
		// checkRegister, _ := s.repo.CheckRegisterMemberByUserIdAndPromotionId(userId, v.StartDate)

		if promotionWebUserFillter != nil {
			var found bool
			for _, v2 := range promotionWebUserFillter {

				if v.Id == v2.PromotionWebId {

					if v2.CreatedAt.Add(7*time.Hour).Format("2006-01-02") < todayDate.Add(7*time.Hour).Format("2006-01-02") && v2.PromotionWebUserStatusId == model.PROMOTION_WEB_USER_STATUS_SUCCESS {
						found = true
						if v.PromotionWebStatusId == model.PROMOTION_WEB_STATUS_ONLY_SHOW {
							v.UserStatusWithPromotion = "SHOW_ONLY"
						} else if v.PromotionWebTypeId == model.PROMOTION_WEB_TYPE_DEPOSIT_MINIMUM_PER_DAY {
							v.UserStatusWithPromotion = "AVAILABLE"
						} else if v.PromotionWebTypeId == model.PROMOTION_WEB_TYPE_DEPOSIT_PER_DAY {
							v.UserStatusWithPromotion = "AVAILABLE"
						} else if v.PromotionWebTypeId == model.PROMOTION_WEB_TYPE_DEPOSIT_BY_TIME {
							v.UserStatusWithPromotion = "AVAILABLE"
						} else if v.PromotionWebTypeId == model.PROMOTION_WEB_TYPE_FIRST_DEPOSIT_OF_DAY {
							v.UserStatusWithPromotion = "AVAILABLE"
						} else if v.PromotionWebTypeId == model.PROMOTION_WEB_TYPE_FIRST_DEPOSIT {
							v.UserStatusWithPromotion = "SUCCESS"
						} else {
							v.UserStatusWithPromotion = "SUCCESS"
						}
					} else if startDate != nil && todayDate.After(*startDate) && endDate != nil && todayDate.Before(*endDate) && v.PromotionWebDateTypeId == model.PROMOTION_WEB_DATE_TYPE_FIXED_DATE {
						found = true
						if v.PromotionWebStatusId == model.PROMOTION_WEB_STATUS_ONLY_SHOW {
							v.UserStatusWithPromotion = "SHOW_ONLY"
						} else if v2.PromotionWebUserStatusId == model.PROMOTION_WEB_USER_STATUS_SUCCESS {
							v.UserStatusWithPromotion = "SUCCESS"
						} else if v2.PromotionWebUserStatusId == model.PROMOTION_WEB_USER_STATUS_ON_PROCESS || v2.PromotionWebUserStatusId == model.PROMOTION_WEB_USER_STATUS_ON_WITHDRAW {
							v.UserStatusWithPromotion = "ON_PROCESS"
						} else if v.PromotionWebTypeId == model.PROMOTION_WEB_TYPE_FIRST_DEPOSIT && v2.PromotionWebUserStatusId == model.PROMOTION_WEB_USER_STATUS_SUCCESS {
							v.UserStatusWithPromotion = "SUCCESS"
						} else {
							v.UserStatusWithPromotion = "AVAILABLE"
						}
					} else {
						found = true
						if v.PromotionWebStatusId == model.PROMOTION_WEB_STATUS_ONLY_SHOW {
							v.UserStatusWithPromotion = "SHOW_ONLY"
						} else if v2.PromotionWebUserStatusId == model.PROMOTION_WEB_USER_STATUS_SUCCESS {
							v.UserStatusWithPromotion = "SUCCESS"
						} else if v2.PromotionWebUserStatusId == model.PROMOTION_WEB_USER_STATUS_ON_PROCESS || v2.PromotionWebUserStatusId == model.PROMOTION_WEB_USER_STATUS_ON_WITHDRAW {
							v.UserStatusWithPromotion = "ON_PROCESS"
						} else if v.PromotionWebTypeId == model.PROMOTION_WEB_TYPE_FIRST_DEPOSIT && v2.PromotionWebUserStatusId == model.PROMOTION_WEB_USER_STATUS_SUCCESS {
							v.UserStatusWithPromotion = "SUCCESS"
						} else {
							v.UserStatusWithPromotion = "AVAILABLE"
						}
					}
					break
				} else {
					found = true
					if v.PromotionWebStatusId == model.PROMOTION_WEB_STATUS_ONLY_SHOW {
						v.UserStatusWithPromotion = "SHOW_ONLY"
					} else if v.PromotionWebTypeId == model.PROMOTION_WEB_TYPE_NEW_MEMBER_FREE {
						v.UserStatusWithPromotion = "SHOW_ONLY"
					} else if !isFirstDeposit && v.PromotionWebTypeId == model.PROMOTION_WEB_TYPE_FIRST_DEPOSIT {
						v.UserStatusWithPromotion = "HIDE"
					} else if startDate != nil && todayDate.After(*startDate) && endDate != nil && todayDate.Before(*endDate) && v.PromotionWebDateTypeId == model.PROMOTION_WEB_DATE_TYPE_FIXED_DATE {
						v.UserStatusWithPromotion = "AVAILABLE"
					} else if v.PromotionWebDateTypeId == model.PROMOTION_WEB_DATE_TYPE_NON_FIXED_DATE {
						v.UserStatusWithPromotion = "AVAILABLE"
					} else {
						v.UserStatusWithPromotion = "NOT_AVAILABLE"
					}
				}
			}
			if found {
				showPromotionWebUser = append(showPromotionWebUser, v)
				continue
			}
		} else {
			if v.PromotionWebStatusId == model.PROMOTION_WEB_STATUS_ONLY_SHOW {
				v.UserStatusWithPromotion = "SHOW_ONLY"
			} else if v.PromotionWebTypeId == model.PROMOTION_WEB_TYPE_NEW_MEMBER_FREE { // && (checkRegister)
				v.UserStatusWithPromotion = "AVAILABLE"
			} else if !isFirstDeposit && v.PromotionWebTypeId == model.PROMOTION_WEB_TYPE_FIRST_DEPOSIT {
				v.UserStatusWithPromotion = "HIDE"
			} else if startDate != nil && todayDate.After(*startDate) && endDate != nil && todayDate.Before(*endDate) && v.PromotionWebDateTypeId == model.PROMOTION_WEB_DATE_TYPE_FIXED_DATE {
				v.UserStatusWithPromotion = "AVAILABLE"
			} else if v.PromotionWebDateTypeId == model.PROMOTION_WEB_DATE_TYPE_NON_FIXED_DATE {
				v.UserStatusWithPromotion = "AVAILABLE"
			} else {
				v.UserStatusWithPromotion = "NOT_AVAILABLE"
			}

			showPromotionWebUser = append(showPromotionWebUser, v)
		}

	}

	return showPromotionWebUser, nil
}
func (s promotionWebService) CheckUserPromotion(body model.CheckUserPromotionBody) (string, error) {

	return CheckUserPromotion(s.repo, body)
}
func (s promotionWebService) CreateTurnOverPromotionWebUser(createdBody model.CreateTurnOverPromotionWebUser, promotionWebBody *model.PromotionWebGetByIdResponse) error {

	return CreateTurnOverPromotionWebUser(s.repo, createdBody, promotionWebBody)
}

func CreateTurnOverPromotionWebUser(repo repository.PromotionWebRepository, createdBody model.CreateTurnOverPromotionWebUser, promotionWebBody *model.PromotionWebGetByIdResponse) error {

	var selectUserPromptionCaseBody model.SelectUserPromptionCase
	selectUserPromptionCaseBody.UserId = createdBody.UserId
	selectUserPromptionCaseBody.PromotionWebTypeId = promotionWebBody.PromotionWebTypeId
	selectUserPromptionCaseBody.PromotionWebId = promotionWebBody.Id
	selectUserPromptionCaseBody.PromotionWebUserId = createdBody.PromotionWebUserId

	var calculatedFreeCreditDeposit float64
	calculatedFreeCreditDeposit, _ = SelectWitDrawUserPromptionCase(repo, selectUserPromptionCaseBody)

	// set ค่า แรกของ จำนวนฝาก
	depositCreditAmount := calculatedFreeCreditDeposit
	if promotionWebBody.PromotionWebBonusTypeId == model.PROMOTION_WEB_BONUS_TYPE_PERCENT {
		// ใช้ยอดฝาก มาคำนาน
		calculatedFreeCreditDeposit = promotionWebBody.BonusTypeAmount * (calculatedFreeCreditDeposit / 100)
		calculatedFreeCreditDeposit = math.Round(calculatedFreeCreditDeposit*100) / 100
		// ถอนสูงสุดเป็น %
		if calculatedFreeCreditDeposit > promotionWebBody.BonusTypeAmountMax && promotionWebBody.BonusTypeAmountMax != 0 {
			calculatedFreeCreditDeposit = promotionWebBody.BonusTypeAmountMax
		}

	} else if promotionWebBody.PromotionWebBonusTypeId == model.PROMOTION_WEB_BONUS_TYPE_FIXED_RATE {
		calculatedFreeCreditDeposit = promotionWebBody.BonusTypeAmount
	}

	// [เอายอด จำนวนเครดิตฝาก + เครดิตแจกที่คำนวนแล้ว * เทิร์นโอเวอร์]
	turnCal := (depositCreditAmount + calculatedFreeCreditDeposit) * promotionWebBody.TurnoverAmount
	turnCal = math.Round(turnCal*100) / 100

	// [Create]
	actionAt := time.Now().UTC()
	bonusAmount := calculatedFreeCreditDeposit
	turnOverAmount := turnCal + 1
	if promotionWebBody.PromotionWebTypeId == model.PROMOTION_WEB_TYPE_NEW_MEMBER_FREE {
		turnOverAmount = 0
	}
	var createBody model.TurnoverUserStatementCreateBody
	createBody.UserId = createdBody.UserId

	if promotionWebBody.PromotionWebTurnoverTypeId == model.PROMOTION_WEB_TURN_OVER_TYPE_ALL {
		createBody.TypeId = model.TURN_PROMOTION_SETTING_PLAY_ALL
	}

	if promotionWebBody.PromotionWebTurnoverTypeId == model.PROMOTION_WEB_TURN_OVER_TYPE_SPORT {
		createBody.TypeId = model.TURN_PROMOTION_SETTING_PLAY_SPORT
	}

	if promotionWebBody.PromotionWebTurnoverTypeId == model.PROMOTION_WEB_TURN_OVER_TYPE_CASINO {
		createBody.TypeId = model.TURN_PROMOTION_SETTING_PLAY_CASINO
	}

	if promotionWebBody.PromotionWebTurnoverTypeId == model.PROMOTION_WEB_TURN_OVER_TYPE_SLOT {
		createBody.TypeId = model.TURN_PROMOTION_SETTING_PLAY_GAME
	}

	if promotionWebBody.PromotionWebTurnoverTypeId == model.PROMOTION_WEB_TURN_OVER_TYPE_P2P {
		createBody.TypeId = model.TURN_PROMOTION_SETTING_PLAY_P2P
	}

	if promotionWebBody.PromotionWebTurnoverTypeId == model.PROMOTION_WEB_TURN_OVER_TYPE_LOTTERY {
		createBody.TypeId = model.TURN_PROMOTION_SETTING_PLAY_LOTTERY
	}

	if promotionWebBody.PromotionWebTurnoverTypeId == model.PROMOTION_WEB_TURN_OVER_TYPE_FINANCIAL {
		createBody.TypeId = model.TURN_PROMOTION_SETTING_PLAY_FINANCIAL
	}

	// หลัง +1 ไป แล้ว ถ้ายังน้อยกว่า 0+1 = 1 ให้เป็น 0 ไปเลย
	var checkTurnOnDefaultAddOne float64
	if turnOverAmount <= 1 {
		checkTurnOnDefaultAddOne = 0
	} else {
		checkTurnOnDefaultAddOne = turnOverAmount
	}
	createBody.Name = model.TURNOVER_CATE_PROMOTION
	createBody.PromotionName = promotionWebBody.PromotionWebTypeTh
	createBody.RefTypeId = createdBody.PromotionWebUserId
	createBody.BonusAmount = bonusAmount
	createBody.StatusId = model.TURNOVER_STATEMENT_STATUS_PENDING
	createBody.StartTurnAmount = checkTurnOnDefaultAddOne
	createBody.StartTurnAt = &actionAt
	createBody.TotalTurnAmount = 0
	createBody.TotalTurnAmount = checkTurnOnDefaultAddOne
	if _, err := repo.CreateTurnoverUserStatement(createBody); err != nil {
		return internalServerError(err)
	}

	return nil
}

func CheckUserPromotion(repo repository.PromotionWebRepository, body model.CheckUserPromotionBody) (string, error) {
	actionAt := time.Now().UTC()
	//[Get]
	var message string
	var promotionBonusCredit float64
	var totalDepositAmount float64
	var err error

	// get promotion web user from promotion web id and user id
	var getPromotionWebUser *model.PromotionWebUserByUserIdResponse
	if body.ImmediateWithPromotionUserId != nil && *body.ImmediateWithPromotionUserId != 0 {
		getPromotionWebUser, _ = repo.GetProcessingPromotionWebUserByPromotionWebUserId(*body.ImmediateWithPromotionUserId)
		if getPromotionWebUser == nil {
			logName := "CheckUserPromotion.PROMOTION_USER_NOT_FOUND_THAT_ON_PROCESS"
			logReq := helper.StructJson(body)
			logResult := "Can't find promotion web user that on process"
			if err := repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
				return "", err
			}
			return "", badRequest("PROMOTION_USER_NOT_FOUND_THAT_ON_PROCESS")
		}
	} else {
		getPromotionWebUser, _ = repo.GetProcessingPromotionWebUserByUserId(body.UserId)
		if getPromotionWebUser == nil {
			logName := "CheckUserPromotion.PROMOTION_USER_NOT_FOUND_THAT_ON_PROCESS"
			logReq := helper.StructJson(body)
			logResult := "Can't find promotion web user that on process"
			if err := repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
				return "", err
			}
			return "", badRequest("PROMOTION_USER_NOT_FOUND_THAT_ON_PROCESS")
		}
	}

	// get promotion web from user id Only status on process
	getPromotionWeb, _ := repo.GetPromotionWebById(getPromotionWebUser.PromotionWebId)
	if getPromotionWeb == nil {
		logName := "CheckUserPromotion.PROMOTION_WEB_NOT_FOUND_THAT_ON_PROCESS"
		logReq := helper.StructJson(body)
		logResult := helper.StructJson(getPromotionWebUser)
		if err := repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
			return "", err
		}
		return "", badRequest("PROMOTION_WEB_NOT_FOUND_THAT_ON_PROCESS")
	}

	if getPromotionWeb.PromotionWebStatusId == model.PROMOTION_WEB_STATUS_CANCELED {
		logName := "CheckUserPromotion.THIS_PROMOTION_WEB_NOT_AVAILABLE"
		logReq := fmt.Sprintf("promotion status : %d ", getPromotionWeb.PromotionWebStatusId)
		logResult := helper.StructJson(getPromotionWeb)
		if err := repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
			return "", err
		}
		return "", badRequest("THIS_PROMOTION_WEB_NOT_AVAILABLE")
	}

	// [Validate]
	// check expire promotion web
	if getPromotionWeb.PromotionWebDateTypeId == model.PROMOTION_WEB_DATE_TYPE_FIXED_DATE {
		todayDate, _ := GetDateToDay()
		startDate, endDate, err := ConvertStartAndEndDate(repo, getPromotionWeb.StartDate, getPromotionWeb.EndDate)
		if err != nil {
			logName := "CheckUserPromotion.INVALID_DATE"
			logReq := helper.StructJson(body)
			logResult := helper.StructJson(getPromotionWeb)
			if err := repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
				return "", err
			}
			return "", err
		}
		// !!!! อันนี้คือนอกเวลาโปรโมชั่น
		if todayDate.Before(*startDate) && todayDate.After(*endDate) {
			logName := "CheckUserPromotion.PROMOTION_NOT_IN_TIME"
			logReq := helper.StructJson(body)
			logResult := helper.StructJson(getPromotionWeb)
			if err := repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
				return "", err
			}
			return "", badRequest("PROMOTION_NOT_IN_TIME")
		}
	}

	// [Case]
	var selectUserPromptionCaseBody model.SelectUserPromptionCase
	selectUserPromptionCaseBody.UserId = body.UserId
	// selectUserPromptionCaseBody.CreditAmount = *body.CreditAmount
	selectUserPromptionCaseBody.PromotionWebTypeId = getPromotionWeb.PromotionWebTypeId
	selectUserPromptionCaseBody.PromotionWebId = getPromotionWeb.Id
	selectUserPromptionCaseBody.PromotionWebUserId = getPromotionWebUser.Id

	message, promotionBonusCredit, totalDepositAmount, err = SelectUserPromptionCase(repo, selectUserPromptionCaseBody)
	if err != nil {
		logName := "CheckUserPromotion.SELECT_USER_PROMOTION_CASE_ERROR"
		logReq := helper.StructJson(selectUserPromptionCaseBody)
		logResult := err
		if err := repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
			return "", err
		}
		return "", err
	}

	checkerPassCondition := false
	if message == model.PASS_PROMOTION || message == model.PASS_TO_WITHDRAW {
		if message == model.PASS_PROMOTION {
			fmt.Println("PASS_PROMOTION")
			// [PASS_PROMOTION]
			// update promotion web user confirm action key where getPromotionWebUser.Id set #C
			getPromotionWebUserConfirm, err := repo.FindPromtionConfirmByPromotionWebUserId(getPromotionWebUser.Id)
			if err != nil {
				logName := "CheckUserPromotion.PROMOTION_USER_NOT_FOUND_THAT_ON_PROCESS"
				logReq := helper.StructJson(selectUserPromptionCaseBody)
				logResult := fmt.Sprintf("message : %s , promotionBonusCredit : %f, err : %s", message, promotionBonusCredit, err)
				if err := repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
					return "", err
				}
				return "", err
			}
			var updatePromotionWebUserConfirm model.PromotionWebUserConfirmUpdateRequest
			updatePromotionWebUserConfirm.Id = getPromotionWebUserConfirm.Id
			updatePromotionWebUserConfirm.ActionKey = fmt.Sprintf("P%dU%d#S%d", getPromotionWebUser.PromotionWebId, getPromotionWebUser.UserId, getPromotionWebUserConfirm.Id)
			if err := repo.UpdatePromotionWebUserConfirmById(updatePromotionWebUserConfirm); err != nil {
				logName := "CheckUserPromotion.UPDATE_PROMOTION_WEB_USER_CONFIRM_BY_ID_ERROR"
				logReq := helper.StructJson(updatePromotionWebUserConfirm)
				logResult := fmt.Sprintf("message : %s , promotionBonusCredit : %f, err : %s", message, promotionBonusCredit, err)
				if err := repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
					return "", err
				}
				return "", err
			}
			// update promotion web user status to success
			var updatePromotionWebUserStatus model.UpdatePromotionWebUserStatus
			updatePromotionWebUserStatus.Id = getPromotionWebUser.Id
			updatePromotionWebUserStatus.PromotionWebUserStatusId = model.PROMOTION_WEB_USER_STATUS_SUCCESS
			updatePromotionWebUserStatus.TotalAmount = promotionBonusCredit
			updatePromotionWebUserStatus.TotalDepositAmount = totalDepositAmount
			if err := repo.UpdatePromotionWebUserStatus(updatePromotionWebUserStatus); err != nil {
				logName := "CheckUserPromotion.UPDATE_PROMOTION_WEB_USER_STATUS_ERROR"
				logReq := helper.StructJson(updatePromotionWebUserStatus)
				logResult := fmt.Sprintf("message : %s , promotionBonusCredit : %f, err : %s", message, promotionBonusCredit, err)
				if err := repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
					return "", err
				}
				return "", err
			}
			checkerPassCondition = true
		}
		if message == model.PASS_TO_WITHDRAW {
			fmt.Println("PASS_TO_WITHDRAW")
			// [PASS_TO_WITHDRAW]
			// update promotion web user confirm action key where getPromotionWebUser.Id set #WS
			getPromotionWebUserConfirm, err := repo.FindPromtionConfirmByPromotionWebUserId(getPromotionWebUser.Id)
			if err != nil {
				logName := "CheckUserPromotion.PROMOTION_USER_NOT_FOUND_THAT_ON_PROCESS"
				logReq := helper.StructJson(selectUserPromptionCaseBody)
				logResult := fmt.Sprintf("message : %s , promotionBonusCredit : %f, err : %s", message, promotionBonusCredit, err)
				if err := repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
					return "", err
				}
				return "", err
			}
			var updatePromotionWebUserConfirm model.PromotionWebUserConfirmUpdateRequest
			updatePromotionWebUserConfirm.Id = getPromotionWebUserConfirm.Id
			updatePromotionWebUserConfirm.ActionKey = fmt.Sprintf("P%dU%d#WS%s", getPromotionWebUser.PromotionWebId, getPromotionWebUser.UserId, time.Now().UTC().Add(7*time.Hour).Format("2006-01-02"))
			if err := repo.UpdatePromotionWebUserConfirmById(updatePromotionWebUserConfirm); err != nil {
				logName := "CheckUserPromotion.UPDATE_PROMOTION_WEB_USER_CONFIRM_BY_ID_ERROR"
				logReq := helper.StructJson(updatePromotionWebUserConfirm)
				logResult := fmt.Sprintf("message : %s , promotionBonusCredit : %f, err : %s", message, promotionBonusCredit, err)
				if err := repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
					return "", err
				}
				return "", err
			}
			// update promotion web user status to withdraw
			var updatePromotionWebUserStatus model.UpdatePromotionWebUserStatus
			updatePromotionWebUserStatus.Id = getPromotionWebUser.Id
			// updatePromotionWebUserStatus.PromotionWebUserStatusId = model.PROMOTION_WEB_USER_STATUS_ON_WITHDRAW
			updatePromotionWebUserStatus.PromotionWebUserStatusId = model.PROMOTION_WEB_USER_STATUS_SUCCESS
			updatePromotionWebUserStatus.TotalAmount = promotionBonusCredit
			updatePromotionWebUserStatus.TotalDepositAmount = totalDepositAmount
			if err := repo.UpdatePromotionWebUserStatus(updatePromotionWebUserStatus); err != nil {
				logName := "CheckUserPromotion.UPDATE_PROMOTION_WEB_USER_STATUS_ERROR"
				logReq := helper.StructJson(updatePromotionWebUserStatus)
				logResult := fmt.Sprintf("message : %s , promotionBonusCredit : %f, err : %s", message, promotionBonusCredit, err)
				if err := repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
					return "", err
				}
				return "", err
			}

			// [LOCK WITHDRAW CREDIT]
			if getPromotionWeb.AbleWithdrawMorethan > 0 || getPromotionWeb.AbleWithdrawPertime > 0 {
				var lockCreditWithdraw model.LockCreditWithdrawCreateRequest
				lockCreditWithdraw.UserId = getPromotionWebUser.UserId
				lockCreditWithdraw.RefId = getPromotionWebUser.Id
				lockCreditWithdraw.Detail = getPromotionWeb.Name
				lockCreditWithdraw.UserWithdrawLockCreditTypeId = model.USER_WITHDRAW_LOCK_CREDIT_TYPE_PROMOTION
				lockCreditWithdraw.CreditMoreThan = getPromotionWeb.AbleWithdrawMorethan
				lockCreditWithdraw.AllowWithdrawAmount = getPromotionWeb.AbleWithdrawPertime
				lockCreditWithdraw.IsLocked = true
				if getPromotionWeb.PromotionWebTypeId == model.PROMOTION_WEB_TYPE_NEW_MEMBER_FREE {
					lockCreditWithdraw.IsPullCredit = true
				}

				if _, err := repo.LockCreditWithdrawCreate(lockCreditWithdraw); err != nil {
					logName := "CheckUserPromotion.LockCreditWithdrawCreate"
					logReq := helper.StructJson(lockCreditWithdraw)
					logResult := fmt.Sprintf("message : %s , promotionBonusCredit : %f, err : %s", message, promotionBonusCredit, err)
					if err := repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
						return "", err
					}
					return "", err
				}
			}

			// To do Create TurnOver ตรงนี้ แค่ 1 ที่
			// CreateTurnOverPromotionWebUser(createdBody model.CreateTurnOverPromotionWebUser) error
			var createdBody model.CreateTurnOverPromotionWebUser
			createdBody.UserId = getPromotionWebUser.UserId
			createdBody.PromotionWebId = getPromotionWebUser.PromotionWebId
			createdBody.PromotionWebUserId = getPromotionWebUser.Id
			createdBody.PromotionWebTypeId = getPromotionWeb.PromotionWebTypeId
			if err := CreateTurnOverPromotionWebUser(repo, createdBody, getPromotionWeb); err != nil {
				logName := "CheckUserPromotion.CreateTurnOverPromotionWebUser"
				logReq := helper.StructJson(createdBody)
				logResult := fmt.Sprintf("message : %s , promotionBonusCredit : %f, err : %s", message, promotionBonusCredit, err)
				if err := repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
					return "", err
				}
				return "", err
			}

			checkerPassCondition = true
		}

		if checkerPassCondition {
			getUser, _ := repo.GetUserById(getPromotionWebUser.UserId)
			if getUser != nil {
				if getUser.MemberCode == nil {
					memberCode, err := GenUniqueUserMemberCode(repository.NewUserRepository(repo.GetDb()), getPromotionWebUser.UserId)
					if err != nil {
						log.Println("CreateDepositRecord.GenUniqueUserMemberCode", err)
						return "", err
					}
					getUser.MemberCode = memberCode
				}
				// [INCREASE CREDIT]
				var userCreditReq model.UserTransactionCreateRequest
				userCreditReq.RefId = &getPromotionWebUser.Id
				userCreditReq.UserId = getPromotionWebUser.UserId
				userCreditReq.TypeId = model.CREDIT_TYPE_PROMOTION_WEB // เพิ่ม type
				userCreditReq.BonusAmount = promotionBonusCredit
				userCreditReq.Detail = getPromotionWeb.PromotionWebTypeTh
				userCreditReq.StartWorkAt = actionAt // เริ่มนับตอนกดยินยัน
				_, err = repo.IncreaseUserCredit(userCreditReq)
				if err != nil {
					return "", err
				}

				// LOCK DOWN NEW MEMBER CREDIT
				if getPromotionWeb.PromotionWebTypeId == model.PROMOTION_WEB_TYPE_NEW_MEMBER_FREE {
					var lockCreditPromotion model.LockCreditPromotionCreateRequest
					lockCreditPromotion.UserId = getPromotionWebUser.UserId
					lockCreditPromotion.PromotionId = getPromotionWebUser.PromotionWebId
					lockCreditPromotion.BonusAmount = promotionBonusCredit
					lockCreditPromotion.IsLocked = true
					lockCreditPromotion.PromotionWebUserId = getPromotionWebUser.Id
					if _, err := repo.LockCreditPromotionCreate(lockCreditPromotion); err != nil {
						return "", err
					}
				}

				//  EXTERNAL NOTI
				var externalNoti model.NotifyExternalNotificationRequest
				externalNoti.TypeNotify = model.PromotionBonus
				externalNoti.MemberCode = *getUser.MemberCode
				externalNoti.BonusCredit = &promotionBonusCredit
				externalNoti.UserCredit = promotionBonusCredit
				suggestAuto := int64(0)
				externalNoti.ConfirmedByAdminId = suggestAuto

				if err := ExternalNotification(repository.NewNotificationRepository(repo.GetDb()), externalNoti); err != nil {
					return "", nil
				}
			}

		}

	}
	// else คือไม่ผ่านเงื่อนไข ไม่ต้องทำไร

	return message, nil
}

func SelectUserPromptionCase(repo repository.PromotionWebRepository, body model.SelectUserPromptionCase) (string, float64, float64, error) {
	message := model.NOT_PASS_PROMOTION
	var promotionCredit float64
	var totalDepositAmount float64
	var err error
	// UserId int64 `json:"userId"`
	// PromotionWebTypeId int64 `json:"promotionWebTypeId"`
	// PromotionWebId     int64 `json:"promotionWebId"`

	// [switch case] ที่ comment ห้าม ลบ
	switch body.PromotionWebTypeId {
	case model.PROMOTION_WEB_TYPE_NEW_MEMBER_FREE:
		message, promotionCredit, totalDepositAmount, err = PromotionWebTypeNewMemberFree(repo, body.PromotionWebId, body.UserId, body.PromotionWebUserId)
		if err != nil {
			return "", 0, 0, err
		}

	case model.PROMOTION_WEB_TYPE_NEW_MEMBER_CONDITION:
		message, promotionCredit, totalDepositAmount, err = PromotionWebTypeNewMemberCondition(repo, body.PromotionWebId, body.UserId, body.PromotionWebUserId)
		if err != nil {
			return "", 0, 0, err
		}

	case model.PROMOTION_WEB_TYPE_DEPOSIT_MINIMUM_PER_DAY:
		message, promotionCredit, totalDepositAmount, err = PromotionWebTypeDepositMinimumPerDay(repo, body.PromotionWebId, body.UserId, body.PromotionWebUserId)
		if err != nil {
			return "", 0, 0, err
		}

	case model.PROMOTION_WEB_TYPE_FIRST_DEPOSIT:
		message, promotionCredit, totalDepositAmount, err = PromotionWebTypeFirstDeposit(repo, body.PromotionWebId, body.UserId, body.PromotionWebUserId)
		if err != nil {
			return "", 0, 0, err
		}

	case model.PROMOTION_WEB_TYPE_DEPOSIT_PER_DAY:
		message, promotionCredit, totalDepositAmount, err = PromotionWebTypeDepositPerDay(repo, body.PromotionWebId, body.UserId, body.PromotionWebUserId)
		if err != nil {
			return "", 0, 0, err
		}

	case model.PROMOTION_WEB_TYPE_DEPOSIT_BY_TIME:
		message, promotionCredit, totalDepositAmount, err = PromotionWebTypeDepositByTime(repo, body.PromotionWebId, body.UserId, body.PromotionWebUserId)
		if err != nil {
			return "", 0, 0, err
		}

	case model.PROMOTION_WEB_TYPE_FIRST_DEPOSIT_OF_DAY:
		message, promotionCredit, totalDepositAmount, err = PromotionWebTypeFirstDepositOfDay(repo, body.PromotionWebId, body.UserId, body.PromotionWebUserId)
		if err != nil {
			return "", 0, 0, err
		}
	}
	return message, promotionCredit, totalDepositAmount, nil
}

func PromotionWebTypeNewMemberFree(repo repository.PromotionWebRepository, promotionWebId int64, userId int64, promotionWebUserId int64) (string, float64, float64, error) {
	message := model.PASS_PROMOTION
	logResultCal := make(map[string]interface{})
	promotionWebBody, _ := repo.GetPromotionWebById(promotionWebId)
	if promotionWebBody == nil {
		return "", 0, 0, badRequest("PROMOTION_WEB_NOT_FOUND_THAT_ON_PROCESS")
	}

	promotionCreditAmount := promotionWebBody.FreeBonusAmount

	if message == model.PASS_PROMOTION {
		// [เงื่อนไขการถอน จำกัดยอดถอน] ต้องมา set ในนี้ในกรณีที่ต้องผ่านเงื่อนไขถอน และมี การตั้งค่านี้ จะทำให้ติดโปร ถอน
		// [ set :withdraw condition ว่าติดเงื่อนไขถอน]
		if promotionWebBody.AbleWithdrawPertime != 0 || promotionWebBody.AbleWithdrawMorethan != 0 || promotionWebBody.TurnoverAmount != 0 {
			message = model.PASS_TO_WITHDRAW
		}

		logName := "NewMemberFree"
		logReq := helper.StructJson(promotionWebBody)
		logResult := logResultCal
		err := repo.PromotionSuccessLog(logName, logReq, logResult)
		if err != nil {
			return "", 0, 0, err
		}
	}
	return message, promotionCreditAmount, 0, nil
}

func PromotionWebTypeNewMemberCondition(repo repository.PromotionWebRepository, promotionWebId int64, userId int64, promotionWebUserId int64) (string, float64, float64, error) {
	message := model.NOT_PASS_PROMOTION
	var totalCreditAmount float64
	var promotionCreditAmount float64
	var err error

	// [Get Promotion Web]
	getPromotionWeb, _ := repo.GetPromotionWebById(promotionWebId)
	if getPromotionWeb == nil {
		return "", 0, 0, badRequest("PROMOTION_WEB_NOT_FOUND_THAT_ON_PROCESS")
	}

	// [Get User First Deposit]
	var getFirstDeposit model.GetUserFirstDepositForPromotion
	getFirstDeposit.UserId = userId
	if getPromotionWeb.PromotionWebDateTypeId == model.PROMOTION_WEB_DATE_TYPE_FIXED_DATE {
		formattedStartDate, err := ConvertToFormattedDate(getPromotionWeb.StartDate)
		if err != nil {
			return "", 0, 0, err
		}

		formattedEndDate, err := ConvertToFormattedDate(getPromotionWeb.EndDate)
		if err != nil {
			return "", 0, 0, err
		}

		getFirstDeposit.FromTransferDate = formattedStartDate
		getFirstDeposit.ToTransferDate = formattedEndDate

	}
	getFirstDeposit.PromotionId = promotionWebUserId
	getUserFirstDeposit, _ := repo.GetUserFirstDepositForPromotion(getFirstDeposit)
	if getUserFirstDeposit != nil {
		totalCreditAmount = getUserFirstDeposit.CreditAmount

		// [Check User First Deposit set totalCreditAmount]
		message, promotionCreditAmount, err = ConditionPromotionSameUsage(repo, *getPromotionWeb, totalCreditAmount, userId)
		if err != nil {
			return "", promotionCreditAmount, totalCreditAmount, err
		}

		// [Check User First Deposit set totalCreditAmount]
	}
	return message, promotionCreditAmount, totalCreditAmount, err
}

func PromotionWebTypeDepositMinimumPerDay(repo repository.PromotionWebRepository, promotionWebId int64, userId int64, promotionWebUserId int64) (string, float64, float64, error) {
	message := model.NOT_PASS_PROMOTION
	var totalCreditAmount float64
	var promotionCreditAmount float64
	var err error

	// [Get Promotion Web]
	getPromotionWeb, _ := repo.GetPromotionWebById(promotionWebId)
	if getPromotionWeb == nil {
		return "", 0, 0, badRequest("PROMOTION_WEB_NOT_FOUND_THAT_ON_PROCESS")
	}

	// [Get User First Deposit]
	var getDeposit model.GetUserDepositWhileInPromotion
	getDeposit.UserId = userId
	if getPromotionWeb.PromotionWebDateTypeId == model.PROMOTION_WEB_DATE_TYPE_FIXED_DATE {
		formattedStartDate, err := ConvertToFormattedDate(getPromotionWeb.StartDate)
		if err != nil {
			return "", 0, 0, err
		}

		formattedEndDate, err := ConvertToFormattedDate(getPromotionWeb.EndDate)
		if err != nil {
			return "", 0, 0, err
		}

		getDeposit.FromTransferDate = formattedStartDate
		getDeposit.ToTransferDate = formattedEndDate

	}
	getDeposit.PromotionId = promotionWebUserId
	getUserDeposit, _ := repo.GetDepositMinimumPerDayForPromotion(getDeposit)
	if getUserDeposit != nil {
		// getUserDeposit
		for _, v := range getUserDeposit {
			switch getPromotionWeb.PromotionWebBonusConditionId {
			case model.PROMOTION_WEB_BONUS_CONDITION_MORE_THAN_OR_EQUAL:
				if v.CreditAmount >= getPromotionWeb.BonusConditionAmount {
					totalCreditAmount = v.CreditAmount
					goto OuterLoop
				}
			case model.PROMOTION_WEB_BONUS_CONDITION_LESS_THAN_OR_EQUAL:
				if v.CreditAmount <= getPromotionWeb.BonusConditionAmount {
					totalCreditAmount = v.CreditAmount
					goto OuterLoop
				}
			}
		}
	OuterLoop:
		if totalCreditAmount != 0 {
			message, promotionCreditAmount, err = ConditionPromotionSameUsage(repo, *getPromotionWeb, totalCreditAmount, userId)
			if err != nil {
				return message, promotionCreditAmount, totalCreditAmount, err
			}
		}
	}
	return message, promotionCreditAmount, totalCreditAmount, err
}

func PromotionWebTypeFirstDeposit(repo repository.PromotionWebRepository, promotionWebId int64, userId int64, promotionWebUserId int64) (string, float64, float64, error) {
	message := model.NOT_PASS_PROMOTION
	var totalCreditAmount float64
	var promotionCreditAmount float64
	var err error

	// [Get Promotion Web]
	getPromotionWeb, _ := repo.GetPromotionWebById(promotionWebId)
	if getPromotionWeb == nil {
		return "", 0, 0, badRequest("PROMOTION_WEB_NOT_FOUND_THAT_ON_PROCESS")
	}

	// [Get User First Deposit]
	var getFirstDeposit model.GetUserFirstDepositForPromotion
	getFirstDeposit.UserId = userId
	if getPromotionWeb.PromotionWebDateTypeId == model.PROMOTION_WEB_DATE_TYPE_FIXED_DATE {
		formattedStartDate, err := ConvertToFormattedDate(getPromotionWeb.StartDate)
		if err != nil {
			return "", 0, 0, err
		}

		formattedEndDate, err := ConvertToFormattedDate(getPromotionWeb.EndDate)
		if err != nil {
			return "", 0, 0, err
		}

		getFirstDeposit.FromTransferDate = formattedStartDate
		getFirstDeposit.ToTransferDate = formattedEndDate

	}
	getFirstDeposit.PromotionId = promotionWebUserId
	getUserFirstDeposit, _ := repo.GetUserFirstDepositForPromotion(getFirstDeposit)
	if getUserFirstDeposit != nil {
		totalCreditAmount = getUserFirstDeposit.CreditAmount

		// [Check User First Deposit set totalCreditAmount]
		message, promotionCreditAmount, err = ConditionPromotionSameUsage(repo, *getPromotionWeb, totalCreditAmount, userId)
		if err != nil {
			return "", promotionCreditAmount, totalCreditAmount, err
		}

		// [Check User First Deposit set totalCreditAmount]
	}
	return message, promotionCreditAmount, totalCreditAmount, err
}

func PromotionWebTypeFirstDepositOfDay(repo repository.PromotionWebRepository, promotionWebId int64, userId int64, promotionWebUserId int64) (string, float64, float64, error) {
	message := model.NOT_PASS_PROMOTION
	var totalCreditAmount float64
	var promotionCreditAmount float64
	var err error

	// [Get Promotion Web]
	getPromotionWeb, _ := repo.GetPromotionWebById(promotionWebId)
	if getPromotionWeb == nil {
		return "", 0, 0, badRequest("PROMOTION_WEB_NOT_FOUND_THAT_ON_PROCESS")
	}

	// [Get User First Deposit]
	var getFirstDepositofDay model.GetUserFirstDepositForPromotion
	getFirstDepositofDay.UserId = userId
	if getPromotionWeb.PromotionWebDateTypeId == model.PROMOTION_WEB_DATE_TYPE_FIXED_DATE {
		formattedStartDate, err := ConvertToFormattedDate(getPromotionWeb.StartDate)
		if err != nil {
			return "", 0, 0, err
		}

		formattedEndDate, err := ConvertToFormattedDate(getPromotionWeb.EndDate)
		if err != nil {
			return "", 0, 0, err
		}

		getFirstDepositofDay.FromTransferDate = formattedStartDate
		getFirstDepositofDay.ToTransferDate = formattedEndDate

	}
	getFirstDepositofDay.PromotionId = promotionWebUserId
	getUserFirstDepositOfDay, _ := repo.GetUserFirstDepositOfDayForPromotion(getFirstDepositofDay)
	if getUserFirstDepositOfDay != nil {
		totalCreditAmount = getUserFirstDepositOfDay.CreditAmount

		// [Check User First Deposit set totalCreditAmount]
		message, promotionCreditAmount, err = ConditionPromotionSameUsage(repo, *getPromotionWeb, totalCreditAmount, userId)
		if err != nil {
			return "", promotionCreditAmount, totalCreditAmount, err
		}

		// [Check User First Deposit set totalCreditAmount]
	}
	return message, promotionCreditAmount, totalCreditAmount, err
}

func PromotionWebTypeDepositPerDay(repo repository.PromotionWebRepository, promotionWebId int64, userId int64, promotionWebUserId int64) (string, float64, float64, error) {
	message := model.NOT_PASS_PROMOTION
	var totalCreditAmount float64
	var promotionCreditAmount float64
	var err error

	// [Get Promotion Web]
	getPromotionWeb, _ := repo.GetPromotionWebById(promotionWebId)
	if getPromotionWeb == nil {
		return "", 0, 0, badRequest("PROMOTION_WEB_NOT_FOUND_THAT_ON_PROCESS")
	}

	// [Get User First Deposit]
	var getDeposit model.GetUserDepositWhileInPromotion
	getDeposit.UserId = userId
	if getPromotionWeb.PromotionWebDateTypeId == model.PROMOTION_WEB_DATE_TYPE_FIXED_DATE {
		formattedStartDate, err := ConvertToFormattedDate(getPromotionWeb.StartDate)
		if err != nil {
			return "", 0, 0, err
		}

		formattedEndDate, err := ConvertToFormattedDate(getPromotionWeb.EndDate)
		if err != nil {
			return "", 0, 0, err
		}

		getDeposit.FromTransferDate = formattedStartDate
		getDeposit.ToTransferDate = formattedEndDate

	}
	getDeposit.PromotionId = promotionWebUserId
	getUserDeposit, _ := repo.GetDepositPerDayForPromotion(getDeposit)
	if getUserDeposit != nil {
		// getUserDeposit
		for _, v := range getUserDeposit {
			totalCreditAmount += v.CreditAmount
		}
		// [Check User First Deposit set totalCreditAmount]
		message, promotionCreditAmount, err = ConditionPromotionSameUsage(repo, *getPromotionWeb, totalCreditAmount, userId)
		if err != nil {
			return "", promotionCreditAmount, totalCreditAmount, err
		}

		// [Check User First Deposit set totalCreditAmount]
	}
	return message, promotionCreditAmount, totalCreditAmount, err
}

func PromotionWebTypeDepositByTime(repo repository.PromotionWebRepository, promotionWebId int64, userId int64, promotionWebUserId int64) (string, float64, float64, error) {
	message := model.NOT_PASS_PROMOTION
	var totalCreditAmount float64
	var promotionCreditAmount float64
	var err error

	// [Get Promotion Web]
	getPromotionWeb, _ := repo.GetPromotionWebById(promotionWebId)
	if getPromotionWeb == nil {
		return "", 0, 0, badRequest("PROMOTION_WEB_NOT_FOUND_THAT_ON_PROCESS")
	}

	// [Get User First Deposit]
	var getDeposit model.GetUserDepositWhileInPromotion
	getDeposit.UserId = userId
	if getPromotionWeb.PromotionWebDateTypeId == model.PROMOTION_WEB_DATE_TYPE_FIXED_DATE {
		formattedStartDate, err := ConvertToFormattedDate(getPromotionWeb.StartDate)
		if err != nil {
			return "", 0, 0, err
		}

		formattedEndDate, err := ConvertToFormattedDate(getPromotionWeb.EndDate)
		if err != nil {
			return "", 0, 0, err
		}

		getDeposit.FromTransferDate = formattedStartDate
		getDeposit.ToTransferDate = formattedEndDate

	}
	getDeposit.PromotionId = promotionWebUserId
	getUserDeposit, _ := repo.GetDepositForPromotionByTime(getDeposit)
	if getUserDeposit != nil {
		// getUserDeposit
		for _, v := range getUserDeposit {
			transactionTransferAt := v.TransferAt.Add(7 * time.Hour)
			_, dayTransferString := DayForPromotionTransfer(transactionTransferAt)
			if dayTransferString != "" {
				dayTransferString = strings.ToUpper(dayTransferString)
			}
			transactionTransferTime := transactionTransferAt.Format("15:04:05")
			if getPromotionWeb.TimeStart != "" && getPromotionWeb.TimeEnd != "" {
				if dayTransferString == "MONDAY" && getPromotionWeb.Monday {
					if transactionTransferTime >= getPromotionWeb.TimeStart && transactionTransferTime <= getPromotionWeb.TimeEnd {
						totalCreditAmount += v.CreditAmount
					}
				}

				if dayTransferString == "TUESDAY" && getPromotionWeb.Tuesday {
					if transactionTransferTime >= getPromotionWeb.TimeStart && transactionTransferTime <= getPromotionWeb.TimeEnd {
						totalCreditAmount += v.CreditAmount
					}
				}

				if dayTransferString == "WEDNESDAY" && getPromotionWeb.Wednesday {
					if transactionTransferTime >= getPromotionWeb.TimeStart && transactionTransferTime <= getPromotionWeb.TimeEnd {
						totalCreditAmount += v.CreditAmount
					}
				}

				if dayTransferString == "THURSDAY" && getPromotionWeb.Thursday {
					if transactionTransferTime >= getPromotionWeb.TimeStart && transactionTransferTime <= getPromotionWeb.TimeEnd {
						totalCreditAmount += v.CreditAmount
					}
				}

				if dayTransferString == "FRIDAY" && getPromotionWeb.Friday {
					if transactionTransferTime >= getPromotionWeb.TimeStart && transactionTransferTime <= getPromotionWeb.TimeEnd {
						totalCreditAmount += v.CreditAmount
					}
				}

				if dayTransferString == "SATURDAY" && getPromotionWeb.Saturday {
					if transactionTransferTime >= getPromotionWeb.TimeStart && transactionTransferTime <= getPromotionWeb.TimeEnd {
						totalCreditAmount += v.CreditAmount
					}
				}

				if dayTransferString == "SUNDAY" && getPromotionWeb.Sunday {
					if transactionTransferTime >= getPromotionWeb.TimeStart && transactionTransferTime <= getPromotionWeb.TimeEnd {
						totalCreditAmount += v.CreditAmount
					}
				}
			} else {
				totalCreditAmount += v.CreditAmount
			}
		}
		if totalCreditAmount != 0 {
			message, promotionCreditAmount, err = ConditionPromotionSameUsage(repo, *getPromotionWeb, totalCreditAmount, userId)
			if err != nil {
				return message, promotionCreditAmount, totalCreditAmount, err
			}
		}
	}
	return message, promotionCreditAmount, totalCreditAmount, err
}

func ConditionPromotionSameUsage(repo repository.PromotionWebRepository, promotionWebBody model.PromotionWebGetByIdResponse, totalCreditAmount float64, userId int64) (string, float64, error) {
	message := model.NOT_PASS_PROMOTION
	logResultCal := make(map[string]interface{})
	// [เงื่อนไข เครดิตฝาก]
	// 1 && 50
	if promotionWebBody.PromotionWebBonusConditionId != 0 && promotionWebBody.BonusConditionAmount != 0 {
		// 1
		if promotionWebBody.PromotionWebBonusConditionId == model.PROMOTION_WEB_BONUS_CONDITION_MORE_THAN_OR_EQUAL {
			//4000 > 50
			if totalCreditAmount >= promotionWebBody.BonusConditionAmount {
				message = model.PASS_PROMOTION
			} else {
				message = model.NOT_PASS_PROMOTION
			}
		}
		if promotionWebBody.PromotionWebBonusConditionId == model.PROMOTION_WEB_BONUS_CONDITION_LESS_THAN_OR_EQUAL {
			if totalCreditAmount <= promotionWebBody.BonusConditionAmount {
				message = model.PASS_PROMOTION
			} else {
				message = model.NOT_PASS_PROMOTION
			}
		}
		logResultCal["promotionId"] = promotionWebBody.Id
		logResultCal["totalCreditAmount"] = totalCreditAmount
		logResultCal["userId"] = userId
		logResultCal["PromotionWebBonusConditionId"] = promotionWebBody.PromotionWebBonusConditionId
		logResultCal["BonusConditionAmount"] = promotionWebBody.BonusConditionAmount
	} else {
		message = model.NOT_PASS_PROMOTION
		return message, 0, nil
	}

	// [จำนวนเครดิตแจก]
	// [ยอดที่จะให้ ลูกค้า][calculate the bonus user will get] [ยอดนี้คือ ยอด จากเงื่อนไข เครดิตฝากและ เครดิตแจก]
	var promotionCreditAmount float64
	if promotionWebBody.PromotionWebBonusTypeId == model.PROMOTION_WEB_BONUS_TYPE_PERCENT {
		// ใช้ยอดฝาก มาคำนาน
		// promotionCreditAmount = promotionWebBody.BonusTypeAmount * (promotionWebBody.BonusConditionAmount / 100)
		promotionCreditAmount = promotionWebBody.BonusTypeAmount * (totalCreditAmount / 100)
		promotionCreditAmount = math.Round(promotionCreditAmount*100) / 100
		if promotionCreditAmount > promotionWebBody.BonusTypeAmountMax && promotionWebBody.BonusTypeAmountMax != 0 {
			promotionCreditAmount = promotionWebBody.BonusTypeAmountMax
		}
		// ใช้ยอดตั้งค่ามาคำนาน
	} else if promotionWebBody.PromotionWebBonusTypeId == model.PROMOTION_WEB_BONUS_TYPE_FIXED_RATE {
		logResultCal["BonusTypeAmount"] = promotionWebBody.BonusTypeAmount
		promotionCreditAmount = promotionWebBody.BonusTypeAmount
	}
	logResultCal["promotionCreditAmount"] = promotionCreditAmount

	if message == model.PASS_PROMOTION {
		// [เงื่อนไขการถอน จำกัดยอดถอน] ต้องมา set ในนี้ในกรณีที่ต้องผ่านเงื่อนไขถอน และมี การตั้งค่านี้ จะทำให้ติดโปร ถอน
		// [ set :withdraw condition ว่าติดเงื่อนไขถอน]
		// 0 || 0 || 3
		if promotionWebBody.AbleWithdrawPertime != 0 || promotionWebBody.AbleWithdrawMorethan != 0 || promotionWebBody.TurnoverAmount != 0 {
			message = model.PASS_TO_WITHDRAW
		}

		logName := "ConditionPromotionSameUsage.PASS_PROMOTION"
		logReq := helper.StructJson(promotionWebBody)
		logResult := logResultCal
		err := repo.PromotionSuccessLog(logName, logReq, logResult)
		if err != nil {
			return "", 0, err
		}
	}
	return message, promotionCreditAmount, nil
}

func (s promotionWebService) SingleWinLoseByMember(req model.AgcSimpleWinloseRequest) (*model.AgcSimpleWinloseResponse, error) {

	statementDate := time.Now().UTC().Format("2006-01-02")
	if req.AddTime != nil {
		statementDate = time.Now().Add(time.Duration(*req.AddTime) * time.Hour).UTC().Format("2006-01-02")
	}

	if req.MinusTime != nil {
		statementDate = time.Now().Add(-time.Duration(*req.MinusTime) * time.Hour).UTC().Format("2006-01-02")
	}

	actionAt := time.Now().UTC()
	// -- GAME sport(1) casino(2) game(4) Then Add more column by lottery(3), p2p(6), Financial(7)
	// -- ** (5) ไม่มีห้ามดึง = พัง
	productIds := []int{model.AGENT_PRODUCT_SPORT, model.AGENT_PRODUCT_CASINO, model.AGENT_PRODUCT_GAME, model.AGENT_PRODUCT_LOTTERY, model.AGENT_PRODUCT_P2P, model.AGENT_PRODUCT_FINANCIAL}
	// hasExternalLotteryApi := os.Getenv("LOTTERY_ENDPOINT") != "" && os.Getenv("AGENT_NAME") != ""

	agentName := os.Getenv("AGENT_NAME")
	body := model.AgcSimpleWinloseSingle{}
	if req.StartDate != "" {
		body.StartDate = req.StartDate
	} else {
		body.StartDate = statementDate
	}
	if req.EndDate != "" {
		body.EndDate = req.EndDate
	} else {

		body.EndDate = statementDate
	}
	body.AgentName = agentName
	body.MemberName = agentName
	// slot1 casino2  esport4
	body.Products = productIds
	body.PageSize = 100
	body.PageIndex = 1
	if req.MemberCode != "" {
		body.PlayerName = req.MemberCode
	}
	// body.PageIndex = 1
	body.TimeStamp = int(actionAt.Unix())
	body.Sign = helper.CreateSign(os.Getenv("AGENT_KEY"), agentName, actionAt)
	list, err := s.repo.AgcSimpleWinLoseSingleByMemberCode(body)
	if err != nil {
		return nil, err
	}

	return list, nil
}

func (s promotionWebService) GetDepositCurrentProcessingUserPromotion(userId int64) (*model.PromotionWebUserByUserIdResponse, error) {
	getPromotionWebUser, err := s.repo.GetDepositCurrentProcessingUserPromotion(userId)
	if err != nil {
		return nil, err
	}

	return getPromotionWebUser, nil
}
func (s promotionWebService) GetWithdrawCurrentProcessingUserPromotion(userId int64) (*model.PromotionWebUserByUserIdResponse, error) {
	getPromotionWebUser, err := s.repo.GetWithdrawCurrentProcessingUserPromotion(userId)
	if err != nil {
		return nil, err
	}

	return getPromotionWebUser, nil
}

func (s promotionWebService) AutoGenerateUserForTest() (*model.UserFormCreate, error) {
	timeNow := time.Now().UTC()
	//auto mock data
	hashedPassword, err := helper.GenUserPassword("Tar12345678")
	if err != nil {
		return nil, err
	}
	var userCreateRequest model.UserFormCreate
	randomeNumber := rand.Intn(**********)
	randomeNumberString := strconv.Itoa(randomeNumber)
	userCreateRequest.Phone = randomeNumberString
	userCreateRequest.Password = hashedPassword
	userCreateRequest.Fullname = "auto mock"
	userCreateRequest.ChannelId = 3
	userCreateRequest.BankId = 1
	userCreateRequest.LineId = "auto mock data"
	userCreateRequest.BankAccount = randomeNumberString
	userCreateRequest.UserStatusId = model.USER_STATUS_ACTIVE
	userCreateRequest.Username = randomeNumberString
	userCreateRequest.Firstname = "auto"
	userCreateRequest.Lastname = "mock"
	userCreateRequest.Encrypt = helper.Encode("Tar12345678")
	userCreateRequest.VerifiedAt = &timeNow
	userCreateRequest.CreatedBy = 1
	userCreateRequest.UpdatedAt = &timeNow

	_, err = s.repo.CreateUser(userCreateRequest)
	if err != nil {
		return nil, err
	}

	return &userCreateRequest, nil
}

func (s promotionWebService) CheckPromotionWithdraw(userId int64, creditWithdraw float64) (string, error) {
	var message string
	logResultCal := make(map[string]interface{})
	// CONTINUE_TO_WITHDRAW = "CONTINUE_TO_WITHDRAW"
	// NOT_PASS_TO_WITHDRAW = "NOT_PASS_TO_WITHDRAW"
	// [Get Promotion Web]
	getPromotionWebUser, err := s.repo.GetWithdrawCurrentProcessingUserPromotion(userId)
	if err != nil {
		log.Println("CheckPromotionWithdraw.GetWithdrawCurrentProcessingUserPromotion", err)
		if err.Error() == recordNotFound {
			return model.CONTINUE_TO_WITHDRAW, nil
		}
		return "", err
	}

	promotionWebBody, err := s.repo.GetPromotionWebById(getPromotionWebUser.PromotionWebId)
	if err != nil {
		log.Println("CheckPromotionWithdraw.GetPromotionWebById", err)
		return "", err
	}

	getUser, err := s.repo.GetUser(userId)
	if err != nil {
		log.Println("CheckPromotionWithdraw.GetUser", err)
		return "", err
	}
	// [เงื่อนไขการถอน ยอดคงเหลือมากกว่า]
	if promotionWebBody.AbleWithdrawMorethan != 0 {
		if getUser.Credit+creditWithdraw > promotionWebBody.AbleWithdrawMorethan {
			message = model.CONTINUE_TO_WITHDRAW
		} else {
			log.Println("เงื่อนไขการถอน ยอดคงเหลือมากกว่า getUser.Credit  > promotionWebBody.AbleWithdrawMorethan", getUser.Credit, promotionWebBody.AbleWithdrawMorethan)
			message = model.NOT_PASS_TO_WITHDRAW
			return message, nil
		}
		logResultCal["getUserCredit"] = getUser.Credit
		logResultCal["AbleWithdrawMorethan"] = promotionWebBody.AbleWithdrawMorethan

	}

	//[เงื่อนไขการถอน จำกัดยอดถอน]
	if promotionWebBody.AbleWithdrawPertime != 0 {

		var withdrawTransactionPromotion model.GetUserAbleWithdrawPertime
		withdrawTransactionPromotion.UserId = userId
		if promotionWebBody.PromotionWebDateTypeId == model.PROMOTION_WEB_DATE_TYPE_FIXED_DATE {
			formattedStartDate, err := ConvertToFormattedDate(promotionWebBody.StartDate)
			if err != nil {
				return "", err
			}

			formattedEndDate, err := ConvertToFormattedDate(promotionWebBody.EndDate)
			if err != nil {
				return "", err
			}

			withdrawTransactionPromotion.FromTransferDate = formattedStartDate
			withdrawTransactionPromotion.ToTransferDate = formattedEndDate

		}
		withdrawTransaction, _ := s.repo.GetUserAbleWithdrawPertime(withdrawTransactionPromotion)
		totalWithDraw := 0.0
		for _, v := range withdrawTransaction {
			totalWithDraw += v.CreditAmount
		}

		log.Println("เงื่อนไขการถอน จำกัดยอดถอน totalWithDraw , promotionWebBody.AbleWithdrawPertime ,creditWithdraw", totalWithDraw, promotionWebBody.AbleWithdrawPertime, creditWithdraw)

		if withdrawTransaction != nil {
			if promotionWebBody.AbleWithdrawPertime > totalWithDraw+creditWithdraw {
				message = model.CONTINUE_TO_WITHDRAW
			} else {
				message = model.NOT_PASS_TO_WITHDRAW
				return message, nil
			}
		} else {
			if promotionWebBody.AbleWithdrawPertime > creditWithdraw {
				message = model.CONTINUE_TO_WITHDRAW
			} else {
				message = model.NOT_PASS_TO_WITHDRAW
				return message, nil
			}
		}
		logResultCal["creditWithdraw"] = creditWithdraw
		logResultCal["AbleWithdrawPertime"] = promotionWebBody.AbleWithdrawPertime
	}

	var promotionWebCheckTurnStatement model.PromotionWebCheckTurnStatementRequest
	var playTotalLog float64
	var turnCal float64
	var setTurnStatementId int64
	promotionWebCheckTurnStatement.UserId = userId
	promotionWebCheckTurnStatement.RefTypeId = getPromotionWebUser.Id
	turnStatement, _ := s.repo.PromotionWebCheckTurnStatement(promotionWebCheckTurnStatement)
	if turnStatement != nil {
		turnCal = turnStatement.TotalTurnAmount
		setTurnStatementId = turnStatement.Id
	} else {
		turnCal = 0.0
	}
	// [เงื่อนไขการถอน ยอดเทิร์นโอเวอร์]
	if promotionWebBody.TurnoverAmount != 0 {
		// clear turn from config
		configuration, _ := s.repo.GetConfiguration()
		if configuration != nil && (getUser.Credit < configuration.ClearTurnCreditLess) {
			getPromotionWebUserConfirm, err := s.repo.FindPromtionConfirmByPromotionWebUserId(getPromotionWebUser.Id)
			if err != nil {
				logName := "CheckPromotionWithdraw.CLEAR.FIND_PROMOTION_WEB_USER_CONFIRM_BY_PROMOTION_WEB_USER_ID_ERROR"
				logReq := helper.StructJson(getPromotionWebUser)
				logResult := fmt.Sprintf("message : %s , promotionBonusCredit : %f, err : %s", message, creditWithdraw, err)
				if err := s.repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
					return "", err
				}
				return "", err
			}
			var updatePromotionWebUserConfirm model.PromotionWebUserConfirmUpdateRequest
			updatePromotionWebUserConfirm.Id = getPromotionWebUserConfirm.Id
			updatePromotionWebUserConfirm.ActionKey = fmt.Sprintf("P%dU%d#S%d", getPromotionWebUser.PromotionWebId, getPromotionWebUser.UserId, getPromotionWebUserConfirm.Id)
			if err := s.repo.UpdatePromotionWebUserConfirmById(updatePromotionWebUserConfirm); err != nil {
				logName := "CheckPromotionWithdraw.CLEAR.UPDATE_PROMOTION_WEB_USER_CONFIRM_BY_ID_ERROR"
				logReq := helper.StructJson(updatePromotionWebUserConfirm)
				logResult := fmt.Sprintf("message : %s , promotionBonusCredit : %f, err : %s", message, creditWithdraw, err)
				if err := s.repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
					return "", err
				}
				return "", err
			}
			// update promotion web user status to success
			var updatePromotionWebUserStatus model.UpdatePromotionWebUserStatus
			updatePromotionWebUserStatus.Id = getPromotionWebUser.Id
			updatePromotionWebUserStatus.PromotionWebUserStatusId = model.PROMOTION_WEB_USER_STATUS_SUCCESS
			updatePromotionWebUserStatus.TotalAmount = creditWithdraw
			// updatePromotionWebUserStatus.TotalDepositAmount = totalDepositAmount
			if err := s.repo.UpdatePromotionWebUserStatusPassWithDraw(updatePromotionWebUserStatus); err != nil {
				logName := "CheckPromotionWithdraw.CLEAR.UPDATE_PROMOTION_WEB_USER_STATUS_ERROR"
				logReq := helper.StructJson(updatePromotionWebUserStatus)
				logResult := fmt.Sprintf("message : %s , promotionBonusCredit : %f, err : %s", message, creditWithdraw, err)
				if err := s.repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
					return "", err
				}
				return "", err
			}
			// create log withdraw
			// CreateTurnoverUserWithdrawLog(body model.CreateTurnoverUserWithdrawLog) (*int64, error)
			var createTurnoverWithDrawLog model.CreateTurnoverUserWithdrawLog
			createTurnoverWithDrawLog.UserId = userId
			createTurnoverWithDrawLog.LogKey = fmt.Sprintf("PROMOTION_TL%dU%dCLEAR%s", setTurnStatementId, userId, time.Now().UTC().Format("20060102150405"))
			createTurnoverWithDrawLog.TotalWithdrawPrice = creditWithdraw
			createTurnoverWithDrawLog.CurrentTurn = turnCal
			createTurnoverWithDrawLog.PlayTotal = playTotalLog
			createTurnoverWithDrawLog.LastPlayY = playTotalLog
			createTurnoverWithDrawLog.LastTotalX = playTotalLog
			createLogWithdraw, err := s.repo.CreateTurnoverUserWithdrawLog(createTurnoverWithDrawLog)
			if err != nil {
				logName := "CheckPromotionWithdraw.CLEAR.CREATE_TURNOVER_USER_WITHDRAW_FOR_PROMOTION_ERROR"
				logReq := helper.StructJson(createTurnoverWithDrawLog)
				logResult := fmt.Sprintf("message : %s , promotionBonusCredit : %f, err : %s", message, creditWithdraw, err)
				if err := s.repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
					return "", err
				}
				return "", err
			}

			if createLogWithdraw != nil {
				var updateTurnoverUserStatement model.TurnoverUserStatementUpdateBody
				setTotalTurnAmount := 0.0
				setTimeTurnAt := time.Now().UTC()
				updateTurnoverUserStatement.StatusId = model.TURNOVER_STATEMENT_STATUS_CANCELED
				updateTurnoverUserStatement.TotalTurnAmount = &setTotalTurnAmount
				updateTurnoverUserStatement.EndTurnAt = &setTimeTurnAt
				if err := s.repo.UpdateTurnoverUserStatement(setTurnStatementId, updateTurnoverUserStatement); err != nil {
					logName := "CheckPromotionWithdraw.CLEAR.UPDATE_TURNOVER_USER_STATEMENT_ERROR"
					logReq := helper.StructJson(updateTurnoverUserStatement)
					logResult := fmt.Sprintf("message : %s , promotionBonusCredit : %f, err : %s", message, creditWithdraw, err)
					if err := s.repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
						return "", err
					}
					return "", err
				}
			}

			logName := "CheckPromotionWithdraw.CLEAR.PASS_PROMOTION"
			logReq := helper.StructJson(promotionWebBody)
			logResult := logResultCal
			if err := s.repo.PromotionSuccessLog(logName, logReq, logResult); err != nil {
				return "", err
			}

			message = model.CONTINUE_TO_WITHDRAW
			return message, nil
		} else {
			createDatePromotionWebUser := getPromotionWebUser.CreatedAt.Add(7 * time.Hour).Format("2006-01-02")
			var UserTodayPlaylogListRequest model.UserTodayPlaylogListRequest
			UserTodayPlaylogListRequest.UserId = &userId
			UserTodayPlaylogListRequest.StatementDateStart = createDatePromotionWebUser

			UserTodayPlaylogList, _, err := s.repo.GetTodaySumUserPlayLogList(UserTodayPlaylogListRequest)
			if err != nil {
				return "", err
			}

			var userPlaylogGame float64
			var userPlaylogCasino float64
			var userPlaylogSport float64
			var userPlaylogTotal float64
			var userPlaylogP2p float64
			var userPlaylogLottery float64
			var userPlaylogFinancial float64

			for _, v := range UserTodayPlaylogList {
				userPlaylogGame += v.SumTurnGame
				userPlaylogCasino += v.SumTurnCasino
				userPlaylogSport += v.SumTurnSport
				userPlaylogP2p += v.SumTurnP2p
				userPlaylogLottery += v.SumTurnLottery
				userPlaylogFinancial += v.SumTurnFinancial
				userPlaylogTotal += v.SumTurnGame + v.SumTurnCasino + v.SumTurnSport + v.SumTurnLottery + v.SumTurnP2p + v.SumTurnFinancial
			}

			// check turn success on this day
			var checkTurnSuccessOnThisDay model.CheckTurnSuccessOnThisDayRequest
			checkTurnSuccessOnThisDay.UserId = userId
			checkTurnSuccessOnThisDay.StartDate = createDatePromotionWebUser
			getTurnSuccessOnThisDay, err := s.repo.CheckTurnSuccessOnThisDay(checkTurnSuccessOnThisDay)
			if err != nil {
				return "", err
			}
			if getTurnSuccessOnThisDay != nil && getTurnSuccessOnThisDay.SumTotalTurnAllAmount > 0 {
				turnCal += getTurnSuccessOnThisDay.SumTotalTurnAllAmount
			}

			// continue cal
			if promotionWebBody.PromotionWebTurnoverTypeId == model.PROMOTION_WEB_TURN_OVER_TYPE_ALL {

				if userPlaylogTotal >= turnCal {
					message = model.CONTINUE_TO_WITHDRAW
					playTotalLog = userPlaylogTotal
				} else {
					message = model.NOT_PASS_TO_WITHDRAW
					return message, nil
				}
			}

			if promotionWebBody.PromotionWebTurnoverTypeId == model.PROMOTION_WEB_TURN_OVER_TYPE_SPORT {
				if userPlaylogSport >= turnCal {
					message = model.CONTINUE_TO_WITHDRAW
					playTotalLog = userPlaylogTotal
				} else {
					message = model.NOT_PASS_TO_WITHDRAW
					return message, nil
				}
			}

			if promotionWebBody.PromotionWebTurnoverTypeId == model.PROMOTION_WEB_TURN_OVER_TYPE_CASINO {
				if userPlaylogCasino >= turnCal {
					message = model.CONTINUE_TO_WITHDRAW
					playTotalLog = userPlaylogTotal
				} else {
					message = model.NOT_PASS_TO_WITHDRAW
					return message, nil
				}
			}

			if promotionWebBody.PromotionWebTurnoverTypeId == model.PROMOTION_WEB_TURN_OVER_TYPE_SLOT {
				if userPlaylogGame >= turnCal {
					message = model.CONTINUE_TO_WITHDRAW
					playTotalLog = userPlaylogTotal
				} else {
					message = model.NOT_PASS_TO_WITHDRAW
					return message, nil
				}
			}

			if promotionWebBody.PromotionWebTurnoverTypeId == model.PROMOTION_WEB_TURN_OVER_TYPE_P2P {
				if userPlaylogP2p >= turnCal {
					message = model.CONTINUE_TO_WITHDRAW
					playTotalLog = userPlaylogTotal
				} else {
					message = model.NOT_PASS_TO_WITHDRAW
					return message, nil
				}
			}

			if promotionWebBody.PromotionWebTurnoverTypeId == model.PROMOTION_WEB_TURN_OVER_TYPE_LOTTERY {
				if userPlaylogLottery >= turnCal {
					message = model.CONTINUE_TO_WITHDRAW
					playTotalLog = userPlaylogTotal
				} else {
					message = model.NOT_PASS_TO_WITHDRAW
					return message, nil
				}
			}

			if promotionWebBody.PromotionWebTurnoverTypeId == model.PROMOTION_WEB_TURN_OVER_TYPE_FINANCIAL {
				if userPlaylogFinancial >= turnCal {
					message = model.CONTINUE_TO_WITHDRAW
					playTotalLog = userPlaylogTotal
				} else {
					message = model.NOT_PASS_TO_WITHDRAW
					return message, nil
				}
			}

			logResultCal["PromotionWebTurnoverTypeId"] = promotionWebBody.PromotionWebTurnoverTypeId
			logResultCal["turnCal"] = turnCal
			logResultCal["userPlaylogTotal"] = userPlaylogTotal
			logResultCal["userPlaylogSport"] = userPlaylogSport
			logResultCal["userPlaylogCasino"] = userPlaylogCasino
			logResultCal["userPlaylogGame"] = userPlaylogGame
		}
	}

	if message == model.CONTINUE_TO_WITHDRAW {
		// to do
		// confirm  promotion web user
		getPromotionWebUserConfirm, err := s.repo.FindPromtionConfirmByPromotionWebUserId(getPromotionWebUser.Id)
		if err != nil {
			logName := "CheckPromotionWithdraw.FIND_PROMOTION_WEB_USER_CONFIRM_BY_PROMOTION_WEB_USER_ID_ERROR"
			logReq := helper.StructJson(getPromotionWebUser)
			logResult := fmt.Sprintf("message : %s , promotionBonusCredit : %f, err : %s", message, creditWithdraw, err)
			if err := s.repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
				return "", err
			}
			return "", err
		}
		var updatePromotionWebUserConfirm model.PromotionWebUserConfirmUpdateRequest
		updatePromotionWebUserConfirm.Id = getPromotionWebUserConfirm.Id
		updatePromotionWebUserConfirm.ActionKey = fmt.Sprintf("P%dU%d#S%d", getPromotionWebUser.PromotionWebId, getPromotionWebUser.UserId, getPromotionWebUserConfirm.Id)
		if err := s.repo.UpdatePromotionWebUserConfirmById(updatePromotionWebUserConfirm); err != nil {
			logName := "CheckPromotionWithdraw.UPDATE_PROMOTION_WEB_USER_CONFIRM_BY_ID_ERROR"
			logReq := helper.StructJson(updatePromotionWebUserConfirm)
			logResult := fmt.Sprintf("message : %s , promotionBonusCredit : %f, err : %s", message, creditWithdraw, err)
			if err := s.repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
				return "", err
			}
			return "", err
		}
		// update promotion web user status to success
		var updatePromotionWebUserStatus model.UpdatePromotionWebUserStatus
		updatePromotionWebUserStatus.Id = getPromotionWebUser.Id
		updatePromotionWebUserStatus.PromotionWebUserStatusId = model.PROMOTION_WEB_USER_STATUS_SUCCESS
		updatePromotionWebUserStatus.TotalAmount = creditWithdraw
		if err := s.repo.UpdatePromotionWebUserStatusPassWithDraw(updatePromotionWebUserStatus); err != nil {
			logName := "CheckPromotionWithdraw.UPDATE_PROMOTION_WEB_USER_STATUS_ERROR"
			logReq := helper.StructJson(updatePromotionWebUserStatus)
			logResult := fmt.Sprintf("message : %s , promotionBonusCredit : %f, err : %s", message, creditWithdraw, err)
			if err := s.repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
				return "", err
			}
			return "", err
		}
		// create log withdraw
		// CreateTurnoverUserWithdrawLog(body model.CreateTurnoverUserWithdrawLog) (*int64, error)
		var createTurnoverWithDrawLog model.CreateTurnoverUserWithdrawLog
		createTurnoverWithDrawLog.UserId = userId
		createTurnoverWithDrawLog.LogKey = fmt.Sprintf("PROMOTION_TL%dU%dd%s", setTurnStatementId, userId, time.Now().UTC().Format("20060102150405"))
		createTurnoverWithDrawLog.TotalWithdrawPrice = creditWithdraw
		createTurnoverWithDrawLog.CurrentTurn = turnCal
		createTurnoverWithDrawLog.PlayTotal = playTotalLog
		createTurnoverWithDrawLog.LastPlayY = playTotalLog
		createTurnoverWithDrawLog.LastTotalX = playTotalLog
		createLogWithdraw, err := s.repo.CreateTurnoverUserWithdrawLog(createTurnoverWithDrawLog)
		if err != nil {
			logName := "CheckPromotionWithdraw.CREATE_TURNOVER_USER_WITHDRAW_FOR_PROMOTION_ERROR"
			logReq := helper.StructJson(createTurnoverWithDrawLog)
			logResult := fmt.Sprintf("message : %s , promotionBonusCredit : %f, err : %s", message, creditWithdraw, err)
			if err := s.repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
				return "", err
			}
			return "", err
		}

		if createLogWithdraw != nil {
			var updateTurnoverUserStatement model.TurnoverUserStatementUpdateBody
			setTotalTurnAmount := 0.0
			setTimeTurnAt := time.Now().UTC()
			updateTurnoverUserStatement.StatusId = model.TURNOVER_STATEMENT_STATUS_COMPLETED
			updateTurnoverUserStatement.TotalTurnAmount = &setTotalTurnAmount
			updateTurnoverUserStatement.EndTurnAt = &setTimeTurnAt
			if err := s.repo.UpdateTurnoverUserStatement(setTurnStatementId, updateTurnoverUserStatement); err != nil {
				logName := "CheckPromotionWithdraw.UPDATE_TURNOVER_USER_STATEMENT_ERROR"
				logReq := helper.StructJson(updateTurnoverUserStatement)
				logResult := fmt.Sprintf("message : %s , promotionBonusCredit : %f, err : %s", message, creditWithdraw, err)
				if err := s.repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
					return "", err
				}
				return "", err
			}
		}

		logName := "CheckPromotionWithdraw.PASS_PROMOTION"
		logReq := helper.StructJson(promotionWebBody)
		logResult := logResultCal
		if err := s.repo.PromotionSuccessLog(logName, logReq, logResult); err != nil {
			return "", err
		}
	} else {
		message = model.NOT_PASS_TO_WITHDRAW
		return message, nil

	}

	return message, nil
}

func SelectWitDrawUserPromptionCase(repo repository.PromotionWebRepository, body model.SelectUserPromptionCase) (float64, error) {
	var promotionCredit float64
	var err error

	// [switch case] ที่ comment ห้าม ลบ
	switch body.PromotionWebTypeId {
	//ไม่ต้องทำ เพราะมันต่างจากอันอื่นที่ไม่มี ยอดมาฝาก
	// case model.PROMOTION_WEB_TYPE_NEW_MEMBER_FREE:
	// 	promotionCredit, err = s.PromotionWebTypeWithdrawNewMemberFree(body.PromotionWebId, body.UserId, body.PromotionWebUserId)
	// 	if err != nil {
	// 		return 0, err
	// 	}

	case model.PROMOTION_WEB_TYPE_NEW_MEMBER_CONDITION:
		promotionCredit, err = PromotionWebTypeWithdrawNewMemberCondition(repo, body.PromotionWebId, body.UserId, body.PromotionWebUserId)
		if err != nil {
			return 0, err
		}

	case model.PROMOTION_WEB_TYPE_DEPOSIT_MINIMUM_PER_DAY:
		promotionCredit, err = PromotionWebTypeWithdrawDepositMinimumPerDay(repo, body.PromotionWebId, body.UserId, body.PromotionWebUserId)
		if err != nil {
			return 0, err
		}

	case model.PROMOTION_WEB_TYPE_FIRST_DEPOSIT:
		promotionCredit, err = PromotionWebTypeWithdrawFirstDeposit(repo, body.PromotionWebId, body.UserId, body.PromotionWebUserId)
		if err != nil {
			return 0, err
		}

	case model.PROMOTION_WEB_TYPE_DEPOSIT_PER_DAY:
		promotionCredit, err = PromotionWebTypeWithdrawDepositPerDay(repo, body.PromotionWebId, body.UserId, body.PromotionWebUserId)
		if err != nil {
			return 0, err
		}

	case model.PROMOTION_WEB_TYPE_DEPOSIT_BY_TIME:
		promotionCredit, err = PromotionWebTypeWithdrawDepositByTime(repo, body.PromotionWebId, body.UserId, body.PromotionWebUserId)
		if err != nil {
			return 0, err
		}

	case model.PROMOTION_WEB_TYPE_FIRST_DEPOSIT_OF_DAY:
		promotionCredit, err = PromotionWebTypeWithdrawFirstDepositOfDay(repo, body.PromotionWebId, body.UserId, body.PromotionWebUserId)
		if err != nil {
			return 0, err
		}

	}
	return promotionCredit, nil
}

func (s promotionWebService) SelectWitDrawUserPromptionCase(body model.SelectUserPromptionCase) (float64, error) {
	return SelectWitDrawUserPromptionCase(s.repo, body)
}

func PromotionWebTypeWithdrawFirstDeposit(repo repository.PromotionWebRepository, promotionWebId int64, userId int64, promotionWebUserId int64) (float64, error) {
	var promotionCredit float64

	// [Get Promotion Web]
	getPromotionWeb, _ := repo.GetPromotionWebById(promotionWebId)
	if getPromotionWeb == nil {
		return 0, badRequest("PROMOTION_WEB_NOT_FOUND_THAT_ON_PROCESS")
	}

	// [Get User First Deposit]
	var getFirstDeposit model.GetUserFirstDepositForPromotion
	getFirstDeposit.UserId = userId
	if getPromotionWeb.PromotionWebDateTypeId == model.PROMOTION_WEB_DATE_TYPE_FIXED_DATE {
		formattedStartDate, err := ConvertToFormattedDate(getPromotionWeb.StartDate)
		if err != nil {
			return 0, err
		}

		formattedEndDate, err := ConvertToFormattedDate(getPromotionWeb.EndDate)
		if err != nil {
			return 0, err
		}

		getFirstDeposit.FromTransferDate = formattedStartDate
		getFirstDeposit.ToTransferDate = formattedEndDate

	}
	getFirstDeposit.PromotionId = promotionWebUserId
	getUserFirstDeposit, _ := repo.GetUserFirstDepositForPromotion(getFirstDeposit)
	if getUserFirstDeposit != nil {
		promotionCredit = getUserFirstDeposit.CreditAmount

		// [Check User First Deposit set totalCreditAmount]
	}
	return promotionCredit, nil
}

func PromotionWebTypeWithdrawNewMemberCondition(repo repository.PromotionWebRepository, promotionWebId int64, userId int64, promotionWebUserId int64) (float64, error) {
	var promotionCredit float64

	// [Get Promotion Web]
	getPromotionWeb, _ := repo.GetPromotionWebById(promotionWebId)
	if getPromotionWeb == nil {
		return 0, badRequest("PROMOTION_WEB_NOT_FOUND_THAT_ON_PROCESS")
	}

	// [Get User First Deposit]
	var getFirstDeposit model.GetUserFirstDepositForPromotion
	getFirstDeposit.UserId = userId
	if getPromotionWeb.PromotionWebDateTypeId == model.PROMOTION_WEB_DATE_TYPE_FIXED_DATE {
		formattedStartDate, err := ConvertToFormattedDate(getPromotionWeb.StartDate)
		if err != nil {
			return 0, err
		}

		formattedEndDate, err := ConvertToFormattedDate(getPromotionWeb.EndDate)
		if err != nil {
			return 0, err
		}

		getFirstDeposit.FromTransferDate = formattedStartDate
		getFirstDeposit.ToTransferDate = formattedEndDate

	}
	getFirstDeposit.PromotionId = promotionWebUserId
	getUserFirstDeposit, _ := repo.GetUserFirstDepositForPromotion(getFirstDeposit)
	if getUserFirstDeposit != nil {
		promotionCredit = getUserFirstDeposit.CreditAmount

		// [Check User First Deposit set totalCreditAmount]
	}
	return promotionCredit, nil
}

func (s promotionWebService) PromotionWebUserGetListByUserId(req model.PromotionWebUserGetListByUserIdRequest) (*model.SuccessWithPagination, error) {
	// [Expired]
	if err := s.UpdateExpiredPromotionWeb(); err != nil {
		return nil, nil
	}

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	list, total, err := s.repo.PromotionWebUserGetListByUserId(req)
	if err != nil {
		return nil, err
	}

	var res model.SuccessWithPagination
	res.List = list
	res.Total = total
	return &res, nil
}

func (s promotionWebService) GetPromotionWebUserWinLoseSummaryCurrentUser(promotionWebUserId int64) (*model.PromotionWebUserCurrentWinLostResponse, error) {
	var getPromotionWebUserById model.GetPromotionWebUserById
	getPromotionWebUserById.Id = promotionWebUserId
	getPromotionWebUser, err := s.repo.GetPromotionWebUserById(getPromotionWebUserById)
	if err != nil {
		return nil, err
	}

	promotionWebBody, err := s.repo.GetPromotionWebById(getPromotionWebUser.PromotionWebId)
	if err != nil {
		log.Println("CheckPromotionWithdraw.GetPromotionWebById", err)
		return nil, err
	}

	//[เงื่อนไขการถอน จำกัดยอดถอน]
	var promotionWebUserCurrentWinLostResponse model.PromotionWebUserCurrentWinLostResponse
	promotionWebUserCurrentWinLostResponse.PromotionWebUserId = getPromotionWebUser.Id

	// [เงื่อนไขการถอน ยอดเทิร์นโอเวอร์]
	if promotionWebBody.TurnoverAmount != 0 {
		todayDate := time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02")
		// GetTodaySumUserPlayLogList(req model.UserTodayPlaylogListRequest) ([]model.UserTodaySumPlaylogReponse, int64, error)
		var UserTodayPlaylogListRequest model.UserTodayPlaylogListRequest
		UserTodayPlaylogListRequest.UserId = &getPromotionWebUser.UserId
		UserTodayPlaylogListRequest.StatementDateStart = getPromotionWebUser.CreatedAt.Add(7 * time.Hour).Format("2006-01-02")
		UserTodayPlaylogListRequest.StatementDateEnd = todayDate

		UserTodayPlaylogList, _, err := s.repo.GetTodaySumUserPlayLogList(UserTodayPlaylogListRequest)
		if err != nil {
			return nil, err
		}

		var userPlaylogGame float64
		var userPlaylogCasino float64
		var userPlaylogSport float64
		var userPlaylogTotal float64
		var userPlaylogP2p float64
		var userPlaylogLottery float64
		var userPlaylogFinancial float64
		for _, v := range UserTodayPlaylogList {
			userPlaylogGame += v.SumTurnGame
			userPlaylogCasino += v.SumTurnCasino
			userPlaylogSport += v.SumTurnSport
			userPlaylogP2p += v.SumTurnP2p
			userPlaylogLottery += v.SumTurnLottery
			userPlaylogFinancial += v.SumTurnFinancial
			userPlaylogTotal += v.SumTurnGame + v.SumTurnCasino + v.SumTurnSport + v.SumTurnLottery + v.SumTurnP2p + v.SumTurnFinancial
		}

		// SelectWitDrawUserPromptionCase(body model.SelectUserPromptionCase) (float64, error)
		var selectUserPromptionCaseBody model.SelectUserPromptionCase
		selectUserPromptionCaseBody.UserId = getPromotionWebUser.UserId
		selectUserPromptionCaseBody.PromotionWebTypeId = promotionWebBody.PromotionWebTypeId
		selectUserPromptionCaseBody.PromotionWebId = promotionWebBody.Id
		selectUserPromptionCaseBody.PromotionWebUserId = getPromotionWebUser.Id

		var calculatedFreeCreditDeposit float64
		calculatedFreeCreditDeposit, _ = s.SelectWitDrawUserPromptionCase(selectUserPromptionCaseBody)
		if err != nil {
			return nil, err
		}
		if calculatedFreeCreditDeposit == 0 {
			return &promotionWebUserCurrentWinLostResponse, nil
		}
		// set ค่า แรกของ จำนวนฝาก
		depositCreditAmount := calculatedFreeCreditDeposit

		if promotionWebBody.PromotionWebBonusTypeId == model.PROMOTION_WEB_BONUS_TYPE_PERCENT {
			// ใช้ยอดฝาก มาคำนาน
			calculatedFreeCreditDeposit = promotionWebBody.BonusTypeAmount * (calculatedFreeCreditDeposit / 100)
			calculatedFreeCreditDeposit = math.Round(calculatedFreeCreditDeposit*100) / 100
			// ถอนสูงสุดเป็น %
			if calculatedFreeCreditDeposit > promotionWebBody.BonusTypeAmountMax && promotionWebBody.BonusTypeAmountMax != 0 {
				calculatedFreeCreditDeposit = promotionWebBody.BonusTypeAmountMax
			}

		} else if promotionWebBody.PromotionWebBonusTypeId == model.PROMOTION_WEB_BONUS_TYPE_FIXED_RATE {
			calculatedFreeCreditDeposit = promotionWebBody.BonusTypeAmount
		}

		// [เอายอด จำนวนเครดิตฝาก + เครดิตแจกที่คำนวนแล้ว * เทิร์นโอเวอร์]
		turnCal := (depositCreditAmount + calculatedFreeCreditDeposit) * promotionWebBody.TurnoverAmount
		turnCal = math.Round(turnCal*100) / 100

		promotionWebUserCurrentWinLostResponse.TurnCalculation = turnCal
		promotionWebUserCurrentWinLostResponse.UserPlaylogTotal = userPlaylogTotal
		promotionWebUserCurrentWinLostResponse.UserPlaylogSport = userPlaylogSport
		promotionWebUserCurrentWinLostResponse.UserPlaylogCasino = userPlaylogCasino
		promotionWebUserCurrentWinLostResponse.UserPlaylogGame = userPlaylogGame
		promotionWebUserCurrentWinLostResponse.UserPlaylogP2p = userPlaylogP2p
		promotionWebUserCurrentWinLostResponse.UserPlaylogLottery = userPlaylogLottery
		promotionWebUserCurrentWinLostResponse.UserPlaylogFinancial = userPlaylogFinancial

	} else {
		return &promotionWebUserCurrentWinLostResponse, nil
	}

	return &promotionWebUserCurrentWinLostResponse, nil
}

func PromotionWebTypeWithdrawDepositMinimumPerDay(repo repository.PromotionWebRepository, promotionWebId int64, userId int64, promotionWebUserId int64) (float64, error) {
	var promotionCredit float64

	// [Get Promotion Web]
	getPromotionWeb, _ := repo.GetPromotionWebById(promotionWebId)
	if getPromotionWeb == nil {
		return 0, badRequest("PROMOTION_WEB_NOT_FOUND_THAT_ON_PROCESS")
	}

	// [Get User First Deposit]
	var getDeposit model.GetUserDepositWhileInPromotion
	getDeposit.UserId = userId
	if getPromotionWeb.PromotionWebDateTypeId == model.PROMOTION_WEB_DATE_TYPE_FIXED_DATE {
		formattedStartDate, err := ConvertToFormattedDate(getPromotionWeb.StartDate)
		if err != nil {
			return 0, err
		}

		formattedEndDate, err := ConvertToFormattedDate(getPromotionWeb.EndDate)
		if err != nil {
			return 0, err
		}

		getDeposit.FromTransferDate = formattedStartDate
		getDeposit.ToTransferDate = formattedEndDate

	}
	getDeposit.PromotionId = promotionWebUserId
	getUserDeposit, _ := repo.GetDepositMinimumPerDayForPromotion(getDeposit)
	var totalCreditAmount float64
	if getUserDeposit != nil {
		// getUserDeposit
		for _, v := range getUserDeposit {
			switch getPromotionWeb.PromotionWebBonusConditionId {
			case model.PROMOTION_WEB_BONUS_CONDITION_MORE_THAN_OR_EQUAL:
				if v.CreditAmount >= getPromotionWeb.BonusConditionAmount {
					totalCreditAmount = v.CreditAmount
					goto OuterLoop
				}
			case model.PROMOTION_WEB_BONUS_CONDITION_LESS_THAN_OR_EQUAL:
				if v.CreditAmount <= getPromotionWeb.BonusConditionAmount {
					totalCreditAmount = v.CreditAmount
					goto OuterLoop
				}
			}
		}
	OuterLoop:
		if totalCreditAmount != 0 {
			promotionCredit = totalCreditAmount
		}
	}
	return promotionCredit, nil
}

func PromotionWebTypeWithdrawDepositPerDay(repo repository.PromotionWebRepository, promotionWebId int64, userId int64, promotionWebUserId int64) (float64, error) {
	var promotionCredit float64

	// [Get Promotion Web]
	getPromotionWeb, _ := repo.GetPromotionWebById(promotionWebId)
	if getPromotionWeb == nil {
		return 0, badRequest("PROMOTION_WEB_NOT_FOUND_THAT_ON_PROCESS")
	}

	// [Get User First Deposit]
	var getDeposit model.GetUserDepositWhileInPromotion
	getDeposit.UserId = userId
	if getPromotionWeb.PromotionWebDateTypeId == model.PROMOTION_WEB_DATE_TYPE_FIXED_DATE {
		formattedStartDate, err := ConvertToFormattedDate(getPromotionWeb.StartDate)
		if err != nil {
			return 0, err
		}

		formattedEndDate, err := ConvertToFormattedDate(getPromotionWeb.EndDate)
		if err != nil {
			return 0, err
		}

		getDeposit.FromTransferDate = formattedStartDate
		getDeposit.ToTransferDate = formattedEndDate

	}
	getDeposit.PromotionId = promotionWebUserId
	getUserDeposit, _ := repo.GetDepositPerDayForPromotion(getDeposit)
	var totalCreditAmount float64
	if getUserDeposit != nil {
		// getUserDeposit
		for _, v := range getUserDeposit {
			totalCreditAmount += v.CreditAmount
		}

		promotionCredit = totalCreditAmount

	}
	return promotionCredit, nil
}

func PromotionWebTypeWithdrawFirstDepositOfDay(repo repository.PromotionWebRepository, promotionWebId int64, userId int64, promotionWebUserId int64) (float64, error) {
	var promotionCredit float64

	// [Get Promotion Web]
	getPromotionWeb, _ := repo.GetPromotionWebById(promotionWebId)
	if getPromotionWeb == nil {
		return 0, badRequest("PROMOTION_WEB_NOT_FOUND_THAT_ON_PROCESS")
	}

	// [Get User First Deposit]
	var getFirstDepositOfDay model.GetUserFirstDepositForPromotion
	getFirstDepositOfDay.UserId = userId
	if getPromotionWeb.PromotionWebDateTypeId == model.PROMOTION_WEB_DATE_TYPE_FIXED_DATE {
		formattedStartDate, err := ConvertToFormattedDate(getPromotionWeb.StartDate)
		if err != nil {
			return 0, err
		}

		formattedEndDate, err := ConvertToFormattedDate(getPromotionWeb.EndDate)
		if err != nil {
			return 0, err
		}

		getFirstDepositOfDay.FromTransferDate = formattedStartDate
		getFirstDepositOfDay.ToTransferDate = formattedEndDate

	}
	getFirstDepositOfDay.PromotionId = promotionWebUserId
	getUserFirstDeposit, _ := repo.GetUserFirstDepositOfDayForPromotion(getFirstDepositOfDay)
	if getUserFirstDeposit != nil {
		promotionCredit = getUserFirstDeposit.CreditAmount

		// [Check User First Deposit set totalCreditAmount]
	}
	return promotionCredit, nil
}

func DayForPromotionTransfer(reqTransferDateAt time.Time) (int64, string) {

	var currentDayToModelDay int64
	// [ต้องบวกให้หน้าบ้าน]
	transferAt := reqTransferDateAt
	transferDateName := transferAt.Weekday().String()
	dayUpper := strings.ToUpper(transferDateName)
	if dayUpper == "MONDAY" {
		currentDayToModelDay = model.ACTIVITY_DAY_MONDAY
	} else if dayUpper == "TUESDAY" {
		currentDayToModelDay = model.ACTIVITY_DAY_TUESDAY
	} else if dayUpper == "WEDNESDAY" {
		currentDayToModelDay = model.ACTIVITY_DAY_WEDNESDAY
	} else if dayUpper == "THURSDAY" {
		currentDayToModelDay = model.ACTIVITY_DAY_THURSDAY
	} else if dayUpper == "FRIDAY" {
		currentDayToModelDay = model.ACTIVITY_DAY_FRIDAY
	} else if dayUpper == "SATURDAY" {
		currentDayToModelDay = model.ACTIVITY_DAY_SATURDAY
	} else if dayUpper == "SUNDAY" {
		currentDayToModelDay = model.ACTIVITY_DAY_SUNDAY
	}

	return currentDayToModelDay, dayUpper
}

func PromotionWebTypeWithdrawDepositByTime(repo repository.PromotionWebRepository, promotionWebId int64, userId int64, promotionWebUserId int64) (float64, error) {
	var promotionCredit float64

	// [Get Promotion Web]
	getPromotionWeb, _ := repo.GetPromotionWebById(promotionWebId)
	if getPromotionWeb == nil {
		return 0, badRequest("PROMOTION_WEB_NOT_FOUND_THAT_ON_PROCESS")
	}

	// [Get User Deposit]
	var getDeposit model.GetUserDepositWhileInPromotion
	getDeposit.UserId = userId
	if getPromotionWeb.PromotionWebDateTypeId == model.PROMOTION_WEB_DATE_TYPE_FIXED_DATE {
		formattedStartDate, err := ConvertToFormattedDate(getPromotionWeb.StartDate)
		if err != nil {
			return 0, err
		}

		formattedEndDate, err := ConvertToFormattedDate(getPromotionWeb.EndDate)
		if err != nil {
			return 0, err
		}

		getDeposit.FromTransferDate = formattedStartDate
		getDeposit.ToTransferDate = formattedEndDate

	}
	getDeposit.PromotionId = promotionWebUserId
	getUserDeposit, _ := repo.GetDepositForPromotionByTime(getDeposit)
	if getUserDeposit != nil {
		// getUserDeposit
		for _, v := range getUserDeposit {
			transactionTransferAt := v.TransferAt.Add(7 * time.Hour)
			_, dayTransferString := DayForPromotionTransfer(transactionTransferAt)
			if dayTransferString != "" {
				dayTransferString = strings.ToUpper(dayTransferString)
			}
			transactionTransferTime := transactionTransferAt.Format("15:04:05")
			if getPromotionWeb.TimeStart != "" && getPromotionWeb.TimeEnd != "" {
				if dayTransferString == "MONDAY" && getPromotionWeb.Monday {
					if transactionTransferTime >= getPromotionWeb.TimeStart && transactionTransferTime <= getPromotionWeb.TimeEnd {
						promotionCredit += v.CreditAmount
					}
				}

				if dayTransferString == "TUESDAY" && getPromotionWeb.Tuesday {
					if transactionTransferTime >= getPromotionWeb.TimeStart && transactionTransferTime <= getPromotionWeb.TimeEnd {
						promotionCredit += v.CreditAmount
					}
				}

				if dayTransferString == "WEDNESDAY" && getPromotionWeb.Wednesday {
					if transactionTransferTime >= getPromotionWeb.TimeStart && transactionTransferTime <= getPromotionWeb.TimeEnd {
						promotionCredit += v.CreditAmount
					}
				}

				if dayTransferString == "THURSDAY" && getPromotionWeb.Thursday {
					if transactionTransferTime >= getPromotionWeb.TimeStart && transactionTransferTime <= getPromotionWeb.TimeEnd {
						promotionCredit += v.CreditAmount
					}
				}

				if dayTransferString == "FRIDAY" && getPromotionWeb.Friday {
					if transactionTransferTime >= getPromotionWeb.TimeStart && transactionTransferTime <= getPromotionWeb.TimeEnd {
						promotionCredit += v.CreditAmount
					}
				}

				if dayTransferString == "SATURDAY" && getPromotionWeb.Saturday {
					if transactionTransferTime >= getPromotionWeb.TimeStart && transactionTransferTime <= getPromotionWeb.TimeEnd {
						promotionCredit += v.CreditAmount
					}
				}

				if dayTransferString == "SUNDAY" && getPromotionWeb.Sunday {
					if transactionTransferTime >= getPromotionWeb.TimeStart && transactionTransferTime <= getPromotionWeb.TimeEnd {
						promotionCredit += v.CreditAmount
					}
				}
			} else {
				promotionCredit += v.CreditAmount
			}
		}

	}
	return promotionCredit, nil
}

// func (s promotionWebService) PromotionWebTypeWithdrawNewMemberFree(promotionWebId int64, userId int64, promotionWebUserId int64) (float64, error) {
// 	var promotionCredit float64

// 	// [no need to check for turn over]
// 	promotionCredit = 0

// 	return promotionCredit, nil
// }

func (s promotionWebService) CheckUserPromotionOnlyNewMemberFree(body model.CheckUserPromotionBody) (string, error) {
	message, err := CheckUserPromotion(s.repo, body)
	if err != nil {
		return "", err
	}
	return message, nil
}

// func CheckUserPromotionOnlyNewMemberFree(repo repository.PromotionWebRepository, body model.CheckUserPromotionBody) (string, error) {
// 	var message string
// 	message = model.NOT_PASS_PROMOTION
// 	actionTimeRegisterAt := time.Now().UTC()
// 	startRegisterAt := actionTimeRegisterAt.Add(7 * time.Hour).Format("2006-01-02")
// 	var err error

// 	findPromotionNewMemberAll, err := repo.CheckUserPromotionOnlyNewMemberFree(startRegisterAt, body.ImmediateWithPromotionUserId)
// 	if err != nil {
// 		if err.Error() == recordNotFound {
// 			return "", notFound(recordNotFound)
// 		}
// 		return "", err
// 	}
// 	var findPromotionRegisterId []int64
// 	for _, v := range findPromotionNewMemberAll {
// 		findPromotionRegisterId = append(findPromotionRegisterId, v.Id)
// 	}

// 	// CheckPromotionRegisterCount(promotionId []int64) ([]model.PromotionWebRegisterMemberGetTotal, error)
// 	getPromtionRegisterMember, err := repo.CheckPromotionRegisterCount(findPromotionRegisterId)
// 	if err != nil {
// 		return "", err
// 	}

// 	var findPromotionNewMember model.PromotionWebGetByIdResponse
// 	// Iterate through each promotion in findPromotionNewMemberAll
// 	for _, allPromotionMemberFree := range findPromotionNewMemberAll {
// 		// Check if the promotion is found in getPromtionRegisterMember
// 		for _, alreadyRegister := range getPromtionRegisterMember {
// 			if alreadyRegister.PromotionId == allPromotionMemberFree.Id && (allPromotionMemberFree.PrivilegePerDay-alreadyRegister.AlreadyRegister > 0) {
// 				findPromotionNewMember = allPromotionMemberFree
// 				break // Move to the next promotion if this one is already registered
// 			}
// 		}
// 	}

// 	// check promotionWebId if it still available how many person per day
// 	var checkAvailablePromotionToday model.PromotionWebUserGetListByPromotionWebIdRequest
// 	checkAvailablePromotionToday.PromotionWebId = findPromotionNewMember.Id
// 	checkAvailablePromotionToday.FromToday = startRegisterAt

// 	countPromotionWebRegisterMember, err := repo.CountPromotionWebRegisterMemberGetByPromotionId(checkAvailablePromotionToday)
// 	if err != nil {
// 		return "", err
// 	}
// 	if countPromotionWebRegisterMember.AlreadyRegister >= findPromotionNewMember.PrivilegePerDay {
// 		return "", badRequest("PROMOTION_WEB_NOT_AVAILABLE")
// 	}
// 	// create register member
// 	var createRegisterMember model.PromotionWebRegisterMemberCreateRequest
// 	createRegisterMember.UserId = body.UserId
// 	createRegisterMember.PromotionId = findPromotionNewMember.Id
// 	createRegisterMember.RegisterStatus = model.PROMOTION_WEB_REGISTER_MEMBER_STATUS_REGISTER_PENDING
// 	createRegisterMember.RegisterAt = actionTimeRegisterAt
// 	createdRegisterMember, err := repo.PromotionWebRegisterMemberCreate(createRegisterMember)
// 	if err != nil {
// 		return "", err
// 	}

// 	// CreateUserCollectPromotionWeb
// 	var createBody model.PromotionWebUserCreateRequest
// 	createBody.UserId = body.UserId
// 	createBody.PromotionWebId = findPromotionNewMember.Id
// 	setNemMember := int64(1)
// 	createBody.Newmember = &setNemMember
// 	createdPromotionUserId, err := CreateUserCollectPromotionWeb(repo, createBody)
// 	if err != nil {

// 		return "", err
// 	}
// 	if createdPromotionUserId > 0 {
// 		// CheckUserPromotion
// 		var checkBody model.CheckUserPromotionBody
// 		checkBody.UserId = body.UserId
// 		checkBody.ImmediateWithPromotionUserId = &createdPromotionUserId
// 		message, err = CheckUserPromotion(repo, checkBody)
// 		if err != nil {
// 			return "", err
// 		}
// 		// update register member
// 		if message == model.PASS_PROMOTION {
// 			var updateRegisterMember model.PromotionWebRegisterMemberUpdateRequest
// 			updateRegisterMember.Id = createdRegisterMember
// 			success := model.PROMOTION_WEB_REGISTER_MEMBER_STATUS_REGISTER_CONFIRM
// 			updateRegisterMember.RegisterStatus = &success
// 			if err := repo.PromotionWebRegisterMemberUpdate(updateRegisterMember); err != nil {
// 				return "", err
// 			}
// 		}
// 	}
// 	return message, nil
// }

func CheckUserPromotionOnlyNewMemberFreeV2(repo repository.PromotionWebRepository, body model.CheckUserPromotionBody) (string, error) {
	var message string
	message = model.NOT_PASS_PROMOTION
	actionTimeRegisterAt := time.Now().UTC()
	startRegisterAt := actionTimeRegisterAt.Add(7 * time.Hour).Format("2006-01-02")
	var err error

	findPromotionNewMemberAll, err := repo.CheckUserPromotionOnlyNewMemberFree(startRegisterAt, body.ImmediateWithPromotionUserId)
	if err != nil {
		if err.Error() == recordNotFound {
			return "", notFound(recordNotFound)
		}
		return "", err
	}
	var findPromotionRegisterId []int64
	for _, v := range findPromotionNewMemberAll {
		findPromotionRegisterId = append(findPromotionRegisterId, v.Id)
	}

	// CheckPromotionRegisterCount(promotionId []int64) ([]model.PromotionWebRegisterMemberGetTotal, error)
	getPromtionRegisterMember, err := repo.CheckPromotionRegisterCount(findPromotionRegisterId)
	if err != nil {
		return "", err
	}

	var findPromotionNewMember model.PromotionWebGetByIdResponse
	// Iterate through each promotion in findPromotionNewMemberAll
	for _, allPromotionMemberFree := range findPromotionNewMemberAll {
		// Check if the promotion is found in getPromtionRegisterMember
		for _, alreadyRegister := range getPromtionRegisterMember {
			if alreadyRegister.PromotionId == allPromotionMemberFree.Id && (allPromotionMemberFree.PrivilegePerDay-alreadyRegister.AlreadyRegister > 0) {
				findPromotionNewMember = allPromotionMemberFree
				break // Move to the next promotion if this one is already registered
			}
		}
	}

	// check promotionWebId if it still available how many person per day
	var checkAvailablePromotionToday model.PromotionWebUserGetListByPromotionWebIdRequest
	checkAvailablePromotionToday.PromotionWebId = findPromotionNewMember.Id
	// new flow startRegisterAt use todayinstead mink confirm
	checkAvailablePromotionToday.FromToday = time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02")

	countPromotionWebRegisterMember, err := repo.CountPromotionWebRegisterMemberGetByPromotionId(checkAvailablePromotionToday)
	if err != nil {
		return "", err
	}
	if countPromotionWebRegisterMember.AlreadyRegister >= findPromotionNewMember.PrivilegePerDay {
		return "", badRequest("OUT_OF_PRIVILEGE_PER_DAY_QUOTA")
	}

	// Check register member after promotion create
	checkRegisterMember, err := repo.CheckRegisterMemberByUserIdAndPromotionId(body.UserId, findPromotionNewMember.StartDate)
	if err != nil {
		return "", badRequest("PROMOTION_WEB_NOT_AVAILABLE")
	}

	if !checkRegisterMember {
		return "", badRequest("NOT_NEW_MEMBER")
	}

	// create register member
	var createRegisterMember model.PromotionWebRegisterMemberCreateRequest
	createRegisterMember.UserId = body.UserId
	createRegisterMember.PromotionId = findPromotionNewMember.Id
	createRegisterMember.RegisterStatus = model.PROMOTION_WEB_REGISTER_MEMBER_STATUS_REGISTER_PENDING
	createRegisterMember.RegisterAt = actionTimeRegisterAt
	createdRegisterMember, err := repo.PromotionWebRegisterMemberCreate(createRegisterMember)
	if err != nil {
		return "", err
	}

	// CreateUserCollectPromotionWeb
	var createBody model.PromotionWebUserCreateRequest
	createBody.UserId = body.UserId
	createBody.PromotionWebId = findPromotionNewMember.Id
	setNemMember := int64(1)
	createBody.Newmember = &setNemMember
	createdPromotionUserId, err := CreateUserCollectPromotionWebNormal(repo, createBody)
	if err != nil {

		return "", err
	}
	if createdPromotionUserId > 0 {
		// CheckUserPromotion
		var checkBody model.CheckUserPromotionBody
		checkBody.UserId = body.UserId
		checkBody.ImmediateWithPromotionUserId = &createdPromotionUserId
		message, err = CheckUserPromotion(repo, checkBody)
		if err != nil {
			return "", err
		}
		// update register member
		if message == model.PASS_PROMOTION {
			var updateRegisterMember model.PromotionWebRegisterMemberUpdateRequest
			updateRegisterMember.Id = createdRegisterMember
			success := model.PROMOTION_WEB_REGISTER_MEMBER_STATUS_REGISTER_CONFIRM
			updateRegisterMember.RegisterStatus = &success
			if err := repo.PromotionWebRegisterMemberUpdate(updateRegisterMember); err != nil {
				return "", err
			}
		}
	}
	return message, nil
}

func (s promotionWebService) CanceledAllTurnPromotion(userId int64) error {
	var message string
	setClearZero := 0.0
	logResultCal := make(map[string]interface{})
	// CONTINUE_TO_WITHDRAW = "CONTINUE_TO_WITHDRAW"
	// NOT_PASS_TO_WITHDRAW = "NOT_PASS_TO_WITHDRAW"
	// [Get Promotion Web]
	getPromotionWebUser, err := s.repo.GetWithdrawCurrentProcessingUserPromotion(userId)
	if err != nil {
		log.Println("CanceledAllTurnPromotion.GetWithdrawCurrentProcessingUserPromotion", err)
		if err.Error() == recordNotFound {
			return nil
		}
		return nil
	}

	promotionWebBody, err := s.repo.GetPromotionWebById(getPromotionWebUser.PromotionWebId)
	if err != nil {
		log.Println("CanceledAllTurnPromotion.GetPromotionWebById", err)
		return nil
	}

	getPromotionWebUserConfirm, err := s.repo.FindPromtionConfirmByPromotionWebUserId(getPromotionWebUser.Id)
	if err != nil {
		logName := "CanceledAllTurnPromotion.CLEAR.FIND_PROMOTION_WEB_USER_CONFIRM_BY_PROMOTION_WEB_USER_ID_ERROR"
		logReq := helper.StructJson(getPromotionWebUser)
		logResult := fmt.Sprintf("message : %s , promotionBonusCredit : %f, err : %s", message, setClearZero, err)
		if err := s.repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
			return nil
		}
		return nil
	}
	var updatePromotionWebUserConfirm model.PromotionWebUserConfirmUpdateRequest
	updatePromotionWebUserConfirm.Id = getPromotionWebUserConfirm.Id
	updatePromotionWebUserConfirm.ActionKey = fmt.Sprintf("P%dU%d#S%d", getPromotionWebUser.PromotionWebId, getPromotionWebUser.UserId, getPromotionWebUserConfirm.Id)
	if err := s.repo.UpdatePromotionWebUserConfirmById(updatePromotionWebUserConfirm); err != nil {
		logName := "CanceledAllTurnPromotion.CLEAR.UPDATE_PROMOTION_WEB_USER_CONFIRM_BY_ID_ERROR"
		logReq := helper.StructJson(updatePromotionWebUserConfirm)
		logResult := fmt.Sprintf("message : %s , promotionBonusCredit : %f, err : %s", message, setClearZero, err)
		if err := s.repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
			return nil
		}
		return nil
	}
	// update promotion web user status to success
	var updatePromotionWebUserStatus model.UpdatePromotionWebUserStatus
	updatePromotionWebUserStatus.Id = getPromotionWebUser.Id
	updatePromotionWebUserStatus.PromotionWebUserStatusId = model.PROMOTION_WEB_USER_STATUS_SUCCESS
	updatePromotionWebUserStatus.TotalAmount = 0
	updatePromotionWebUserStatus.TotalDepositAmount = 0
	if err := s.repo.UpdatePromotionWebUserStatusPassWithDraw(updatePromotionWebUserStatus); err != nil {
		logName := "CanceledAllTurnPromotion.CLEAR.UPDATE_PROMOTION_WEB_USER_STATUS_ERROR"
		logReq := helper.StructJson(updatePromotionWebUserStatus)
		logResult := fmt.Sprintf("message : %s , promotionBonusCredit : %f, err : %s", message, setClearZero, err)
		if err := s.repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
			return nil
		}
		return nil
	}
	// create log withdraw
	// CreateTurnoverUserWithdrawLog(body model.CreateTurnoverUserWithdrawLog) (*int64, error)
	var createTurnoverWithDrawLog model.CreateTurnoverUserWithdrawLog
	createTurnoverWithDrawLog.UserId = userId
	createTurnoverWithDrawLog.LogKey = fmt.Sprintf("MAINPROMTION_ADMINCLEAR_D%s", time.Now().UTC().Format("20060102150405"))
	createTurnoverWithDrawLog.TotalWithdrawPrice = 0
	createTurnoverWithDrawLog.CurrentTurn = 0
	createTurnoverWithDrawLog.PlayTotal = 0
	createTurnoverWithDrawLog.LastPlayY = 0
	createTurnoverWithDrawLog.LastTotalX = 0
	if _, err := s.repo.CreateTurnoverUserWithdrawLog(createTurnoverWithDrawLog); err != nil {
		logName := "CanceledAllTurnPromotion.CLEAR.CREATE_TURNOVER_USER_WITHDRAW_FOR_PROMOTION_ERROR"
		logReq := helper.StructJson(createTurnoverWithDrawLog)
		logResult := fmt.Sprintf("message : %s , promotionBonusCredit : %f, err : %s", message, setClearZero, err)
		if err := s.repo.PromotionErrorLog(logName, logReq, logResult); err != nil {
			return nil
		}
		return nil
	}
	logName := "CanceledAllTurnPromotion.CLEAR.PASS_PROMOTION"
	logReq := helper.StructJson(promotionWebBody)
	logResult := logResultCal
	if err := s.repo.PromotionSuccessLog(logName, logReq, logResult); err != nil {
		return nil
	}

	return nil
}

func (s promotionWebService) GetUserPromotionReportList(req model.GetUserPromotionReportListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.Pagination(&req.Page, &req.Limit); err != nil {
		return nil, err
	}

	list, total, err := s.repo.GetUserPromotionReportList(req)
	if err != nil {
		return nil, err
	}

	var response model.SuccessWithPagination
	response.List = list
	response.Total = total
	response.Message = "success"

	return &response, nil
}

func (s promotionWebService) DeletePromotionWeb(req model.DeletePromotionWebRequest) error {

	var cancelPromotionWebReq model.CancelPromotionWebRequest
	cancelPromotionWebReq.Id = req.Id
	cancelPromotionWebReq.CanceledByAdminId = req.DeletedByAdminId
	cancelPromotionWebReq.CanceledAt = req.DeletedAt
	cancelPromotionWebReq.PromotionWebStatusId = req.PromotionWebStatusId

	// flow same as CancelPromotionWeb
	if err := s.CancelPromotionWeb(cancelPromotionWebReq, "CANCEL"); err != nil {
		return err
	}

	// add delete promotion web
	if err := s.repo.DeletePromotionWeb(req); err != nil {
		return err
	}

	return nil
}

func (s promotionWebService) SortPromotionWebPriorityOrder(req model.DragSortRequest) error {
	if err := s.repo.SortPromotionWebPriorityOrder(req); err != nil {
		return err
	}

	return nil
}

func (s promotionWebService) PromotionWebSummary(req model.PromotionWebGetListSummaryRequest) (*model.PromotionWebGetListSummaryResponse, error) {

	response, err := s.repo.PromotionWebSummary(req)
	if err != nil {
		return nil, err
	}

	return response, nil
}

func (s promotionWebService) PromotionWebUserSummary(req model.PromotionWebUserGetListSummaryRequest) (*model.PromotionWebUserGetListSummaryResponse, error) {

	response, err := s.repo.PromotionWebUserSummary(req)
	if err != nil {
		return nil, err
	}

	return response, nil
}

func (s *promotionWebService) UploadImageToS3PromotionCover(imageFileBody *http.Request) (*model.FileUploadResponse, error) {

	fileReader, newFileName, err := imageFileBody.FormFile("file")
	if err != nil {
		log.Println("UploadFileS3.ERROR", err)
		return nil, err
	}

	filename := &newFileName.Filename

	dbName := os.Getenv("DB_NAME")

	pathName := fmt.Sprintf("cbgame/%v/promotion-cover/", dbName)
	var newImageId *model.FileUploadResponse
	fileData, err := s.repo.UploadImageToS3(pathName, *filename, fileReader)
	if err != nil {
		log.Println("UploadFileS3.ERROR", err)
		return nil, badRequest("UNABLE_TO_UPLOAD_FILE")
	}

	newImageId = &model.FileUploadResponse{
		ImageUrl: fileData.ImageUrl,
	}

	return newImageId, nil
}

func (s promotionWebService) LockCreditPromotionUpdate(adminId int64, req model.LockCreditPromotionUpdateRequest) error {
	if err := s.repo.LockCreditPromotionUpdate(adminId, req); err != nil {
		return err
	}

	return nil
}

func (s promotionWebService) CheckIsLockedCreditPromotionByUserId(userId int64) (bool, error) {

	isLocked, err := s.repo.CheckIsLockedCreditPromotionByUserId(userId)
	if err != nil {
		return false, err
	}

	return isLocked, nil
}
func (s promotionWebService) CheckIsLockedCreditPromotion(userId int64) (*model.LockCreditPromotionUpdateResposnse, error) {

	var response model.LockCreditPromotionUpdateResposnse
	isLocked, err := s.repo.CheckIsLockedCreditPromotionByUserId(userId)
	if err != nil {
		return &response, err
	}
	response.IsLockedCredit = isLocked

	return &response, nil
}

func (s promotionWebService) GetPromotionWebUserStatusOptions() ([]model.SelectOptions, error) {

	options, err := s.repo.GetPromotionWebUserStatusOptions()
	if err != nil {
		return nil, err
	}

	return options, nil
}

func (s promotionWebService) GetLockCreditWithdrawList(req model.GetLockCreditWithdrawListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.Pagination(&req.Page, &req.Limit); err != nil {
		return nil, err
	}

	response, total, err := s.repo.GetLockCreditWithdrawList(req)
	if err != nil {
		return nil, err
	}

	return &model.SuccessWithPagination{
		List:  response,
		Total: total,
	}, nil
}

func (s promotionWebService) UnLockCreditWithdraw(req model.UpdateLockCreditWithdrawRequest) error {

	setUnLock := false
	req.IsLocked = &setUnLock
	req.ApprovedAt = time.Now().UTC()
	if err := s.repo.UpdateLockCreditWithdraw(req); err != nil {
		return err
	}

	return nil
}

func (s promotionWebService) CheckLockCreditWithdrawByUserId(userId int64) ([]model.CheckLockCreditWithdrawByUserId, error) {

	response, err := s.repo.CheckLockCreditWithdrawByUserId(userId)
	if err != nil {
		return nil, err
	}

	return response, nil
}

func (s promotionWebService) MigrateBackUpLockCreditBack() (*model.MigrateBackUpLockCreditBackResponse, error) {
	getAllWithdrawProcessPromotionWebUser, err := s.repo.GetAllWithdrawProcessPromotionWebUser()
	if err != nil {
		return nil, err
	}

	var refIds []int64
	for _, v := range getAllWithdrawProcessPromotionWebUser {
		refIds = append(refIds, v.Id)
	}

	existingLockCredits, err := s.repo.GetAllLockCreditWithdrawByRefId(refIds)
	if err != nil {
		return nil, err
	}

	existingRefIdMap := make(map[int64]bool)
	for _, record := range existingLockCredits {
		existingRefIdMap[record.RefId] = true
	}

	var total int
	var countSuccess, countError int64
	var wg sync.WaitGroup

	errorChan := make(chan string, len(getAllWithdrawProcessPromotionWebUser))

	for _, user := range getAllWithdrawProcessPromotionWebUser {
		total++
		if existingRefIdMap[user.Id] {
			continue
		}

		wg.Add(1)

		go func(user model.GetAllWithdrawProcessPromotionWebUser) {
			defer wg.Done()
			if user.AbleWithdrawMorethan > 0 || user.AbleWithdrawPertime > 0 {
				lockCreditWithdraw := model.LockCreditWithdrawCreateRequest{
					UserId:                       user.UserId,
					RefId:                        user.Id,
					Detail:                       user.PromotionWebName,
					UserWithdrawLockCreditTypeId: model.USER_WITHDRAW_LOCK_CREDIT_TYPE_PROMOTION,
					CreditMoreThan:               user.AbleWithdrawMorethan,
					AllowWithdrawAmount:          user.AbleWithdrawPertime,
					IsLocked:                     true,
					IsPullCredit:                 user.PromotionWebTypeId == model.PROMOTION_WEB_TYPE_NEW_MEMBER_FREE,
				}

				if _, err := s.repo.LockCreditWithdrawCreate(lockCreditWithdraw); err != nil {
					errorChan <- fmt.Sprintf("Failed to create lock credit for UserId %d, RefId %d: %v", user.UserId, user.Id, err)
					atomic.AddInt64(&countError, 1)
					return
				}
			}
			var updatePromotionWebUserStatus model.UpdatePromotionWebUserStatus
			updatePromotionWebUserStatus.Id = user.Id
			updatePromotionWebUserStatus.PromotionWebUserStatusId = model.PROMOTION_WEB_USER_STATUS_SUCCESS
			updatePromotionWebUserStatus.TotalAmount = 0
			updatePromotionWebUserStatus.TotalDepositAmount = 0
			if err := s.repo.UpdatePromotionWebUserStatusPassWithDraw(updatePromotionWebUserStatus); err != nil {
				errorChan <- fmt.Sprintf("Failed to create lock credit for UserId %d, RefId %d: %v", user.UserId, user.Id, err)
				atomic.AddInt64(&countError, 1)
				return
			}

			atomic.AddInt64(&countSuccess, 1)
		}(user)
	}

	wg.Wait()

	close(errorChan)

	for errLog := range errorChan {
		fmt.Println(errLog)
	}

	response := &model.MigrateBackUpLockCreditBackResponse{
		Total:        total,
		CountError:   int(countError),
		CountSuccess: int(countSuccess),
	}

	return response, nil
}
