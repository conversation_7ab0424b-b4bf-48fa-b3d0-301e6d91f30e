package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"fmt"
	"log"
	"net/http"
	"os"
	"strconv"

	"gorm.io/gorm"
)

type ConfigurationService interface {
	ClearAllConfigurationCache() error
	// configuration web options
	GetAutoUserApproveType() ([]model.AutoUserApproveType, error)
	GetAutoWithdrawType() ([]model.AutoWithdrawType, error)
	GetTurnWithdrawType() ([]model.TurnWithdrawType, error)

	// configuration web
	GetConfiguration() (*model.ConfigurationResponse, error)
	UpdateConfiguration(body model.UpdateConfigurationRequest) error

	// web configuration
	GetWebConfiguration(userId int64) (*model.GetWebConfigurationResponse, error)
	GetContactConfig() (*model.GetContactConfigWebResponse, error)

	UploadImageToCloudflareConfigLogo(imageFileBody *http.Request) (*model.FileUploadResponse, error)

	// configuration bank limit
	GetBankLimitConfiguration() (*model.BankLimitConfigurationResponse, error)
	UpdateBankLimitConfiguration(body model.UpdateBankLimitConfigurationRequest) error
	GetUserBankLimit(userId int64) (*model.UserBankLimitResponse, error)

	//s3
	UploadImageToS3ConfigLogo(imageFileBody *http.Request) (*model.FileUploadResponse, error)
	UploadImageToS3ConfigBanner(imageFileBody *http.Request) (*model.FileUploadResponse, error)

	// agent game priority setting
	GetAgentGamePrioritySettingList(req model.GetAgentGamePrioritySettingListRequest) (*model.SuccessWithPagination, error)
	SortGamegentGamePriorityOrder(req model.DragSortRequest, adminUpdateID int64) error
	IncrementAgentGameTotalPlayed(vendorCode string) error
	UpdateAgentGamePrioritySetting(body model.UpdateAgentGamePrioritySettingBody) error

	// banner setting
	CreateBannerSetting(req model.CreateBannerSettingRequest) error
	UpdateBannerSetting(id int64, req model.BannerSettingUpdateRequest) error
	GetPublicBannerSettingList(req model.GetBannerSettingListRequest) ([]model.GetBannerSettingListResponse, error)
	GetBannerSettingList(req model.GetBannerSettingListRequest) ([]model.GetBannerSettingListResponse, error)
	DeleteBannerSetting(req model.DeleteBannerSettingRequest) error

	GetConfigurationCheckShowTransactionResponse() (*model.ConfigurationCheckShowTransactionResponse, error)

	GetConfigurationRegisterFormat() ([]model.SelectOptions, error)
	WebGetConfigurationRegisterFormat() (*model.WebGetConfigurationRegisterFormatResponse, error)

	// activity menu
	UpdateActivityMenu(body model.UpdateActivityMenuRequest) error
	SortActivityMenu(req model.DragSortRequest) error
	UploadImageActivityMenu(imageFileBody *http.Request) (*model.FileUploadResponse, error)
	GetActivityMenu(req model.GetActivityMenuRequest) ([]model.GetActivityMenuResponse, error)
	GetActivityMenuItem(req model.GetActivityMenuItemRequest) (*model.GetActivityMenuResponse, error)

	// running text
	GetRunningMessage() (*model.RunningMessageResponse, error)
}

type configurationService struct {
	repo        repository.ConfigurationRepository
	adminAction AdminActionService
}

func NewConfigurationService(
	repo repository.ConfigurationRepository,
	adminAction AdminActionService,
) ConfigurationService {
	return &configurationService{repo, adminAction}
}

func (s *configurationService) ClearAllConfigurationCache() error {

	if err := s.repo.ClearAllConfigurationCache(); err != nil {
		return err
	}
	return nil
}

func (s *configurationService) GetAutoUserApproveType() ([]model.AutoUserApproveType, error) {

	options, err := s.repo.GetAutoUserApproveType()
	if err != nil {
		return nil, err
	}

	return options, nil
}

func (s *configurationService) GetAutoWithdrawType() ([]model.AutoWithdrawType, error) {

	options, err := s.repo.GetAutoWithdrawType()
	if err != nil {
		return nil, err
	}

	return options, nil
}

func (s *configurationService) GetTurnWithdrawType() ([]model.TurnWithdrawType, error) {

	options, err := s.repo.GetTurnWithdrawType()
	if err != nil {
		return nil, err
	}

	return options, nil
}

func (s *configurationService) GetConfiguration() (*model.ConfigurationResponse, error) {

	configuration, err := s.repo.GetConfiguration()
	if err != nil {
		// if err.Error() == "record not found" {
		// 	autoUserApproveTypeId := int64(2)
		// 	defaultOpen := true
		// 	defaultClose := false
		// 	turnWithdrawTypeId := int64(1)
		// 	minFirstMemberDeposit := int64(0)
		// 	checkPhoneCaptchaLen := int64(0)
		// 	withdrawMaximumAuto := float64(0)
		// 	withdrawMaximum := float64(49999)
		// 	defaultUploadDepositSlipType := model.CONFIG_UPLOAD_DEPOSIT_SLIP_TYPE_DISABLED
		// 	useThCurrency := true
		// 	_, err = s.repo.CreateConfiguration(model.CreateConfigurationBody{
		// 		AutoUserApproveTypeId:    &autoUserApproveTypeId,
		// 		UseOtpRegister:           &defaultClose,
		// 		TurnWithdrawTypeId:       &turnWithdrawTypeId,
		// 		AllowOnlineRegistration:  &defaultOpen,
		// 		AllowOnlineRegisterForm:  &defaultOpen,
		// 		CheckAccountNameFastbank: &defaultClose,
		// 		MinFirstMemberDeposit:    &minFirstMemberDeposit,
		// 		CheckPhoneCaptchaLen:     &checkPhoneCaptchaLen,
		// 		OpenGameNewTab:           &defaultClose,
		// 		UseUploadDepositSlip:     &defaultClose,
		// 		UploadDepositSlipType:    &defaultUploadDepositSlipType,
		// 		ShowWebAffName:           &defaultClose,
		// 		UseThCurrency:            &useThCurrency,
		// 		WithdrawMaximumAuto:      &withdrawMaximumAuto,
		// 		WithdrawMaximum:          &withdrawMaximum,
		// 	})
		// 	if err != nil {
		// 		return nil, err
		// 	}
		// 	configuration2, err := s.repo.GetConfiguration()
		// 	if err != nil {
		// 		return nil, err
		// 	}
		// 	return configuration2, nil
		// }
		return nil, err
	}

	return configuration, nil
}

func (s *configurationService) UpdateConfiguration(req model.UpdateConfigurationRequest) error {

	record, err := s.repo.GetConfiguration()
	if err != nil {
		_, err = s.repo.CreateConfiguration(model.CreateConfigurationBody{
			LogoUrl:                       req.LogoUrl,
			WebName:                       req.WebName,
			BackgroundColor:               req.BackgroundColor,
			AutoUserApproveTypeId:         req.AutoUserApproveTypeId,
			UseOtpRegister:                req.UseOtpRegister,
			TurnWithdrawTypeId:            req.TurnWithdrawTypeId,
			AllowOnlineRegistration:       req.AllowOnlineRegistration,
			AllowOnlineRegisterForm:       req.AllowOnlineRegisterForm,
			CheckAccountNameFastbank:      req.CheckAccountNameFastbank,
			IsShowDeposit:                 req.IsShowDeposit,
			IsShowWithdraw:                req.IsShowWithdraw,
			MinimumDeposit:                req.MinimumDeposit,
			MinimumWithdraw:               req.MinimumWithdraw,
			MinFirstMemberDeposit:         req.MinFirstMemberDeposit,
			IdLine:                        req.IdLine,
			UrlLine:                       req.UrlLine,
			ClearTurnCreditLess:           req.ClearTurnCreditLess,
			UseUploadDepositSlip:          req.UseUploadDepositSlip,
			UploadDepositSlipType:         req.UploadDepositSlipType,
			ShowWebAffName:                req.ShowWebAffName,
			UseThCurrency:                 req.UseThCurrency,
			UseLaosCurrency:               req.UseLaosCurrency,
			IdWhatsapp:                    req.IdWhatsapp,
			UrlWhatsapp:                   req.UrlWhatsapp,
			IdTelegram:                    req.IdTelegram,
			UrlTelegram:                   req.UrlTelegram,
			WithdrawMaximumAuto:           req.WithdrawMaximumAuto,
			WithdrawMaximum:               req.WithdrawMaximum,
			IsTotpVerify:                  req.IsTotpVerify,
			TokenExpiredMinute:            req.TokenExpiredMinute,
			OpenGameNewTab:                req.OpenGameNewTab,
			ConfigurationRegisterFormatId: req.ConfigurationRegisterFormatId,
		})
		if err != nil {
			return err
		}
		return err
	}

	if record != nil {
		var body model.UpdateConfigurationBody
		body.Id = record.Id
		body.LogoUrl = req.LogoUrl
		body.WebName = req.WebName
		body.BackgroundColor = req.BackgroundColor
		body.AutoUserApproveTypeId = req.AutoUserApproveTypeId
		body.UseOtpRegister = req.UseOtpRegister
		body.TurnWithdrawTypeId = req.TurnWithdrawTypeId
		body.AllowOnlineRegistration = req.AllowOnlineRegistration
		body.AllowOnlineRegisterForm = req.AllowOnlineRegisterForm
		body.CheckAccountNameFastbank = req.CheckAccountNameFastbank
		body.IsShowDeposit = req.IsShowDeposit
		body.IsShowWithdraw = req.IsShowWithdraw
		body.MinimumDeposit = req.MinimumDeposit
		body.MinimumWithdraw = req.MinimumWithdraw
		body.MinFirstMemberDeposit = req.MinFirstMemberDeposit
		body.IdLine = req.IdLine
		body.UrlLine = req.UrlLine
		body.CheckPhoneCaptchaLen = req.CheckPhoneCaptchaLen
		body.ClearTurnCreditLess = req.ClearTurnCreditLess
		body.UseUploadDepositSlip = req.UseUploadDepositSlip
		body.UploadDepositSlipType = req.UploadDepositSlipType
		body.ShowWebAffName = req.ShowWebAffName
		body.UseThCurrency = req.UseThCurrency
		body.UseLaosCurrency = req.UseLaosCurrency
		body.IdWhatsapp = req.IdWhatsapp
		body.UrlWhatsapp = req.UrlWhatsapp
		body.IdTelegram = req.IdTelegram
		body.UrlTelegram = req.UrlTelegram
		body.WithdrawMaximumAuto = req.WithdrawMaximumAuto
		body.WithdrawMaximum = req.WithdrawMaximum
		body.IsTotpVerify = req.IsTotpVerify
		body.TokenExpiredMinute = req.TokenExpiredMinute
		body.OpenGameNewTab = req.OpenGameNewTab
		body.ConfigurationRegisterFormatId = req.ConfigurationRegisterFormatId
		err = s.repo.UpdateConfiguration(body)
		if err != nil {
			return err
		}

		// [ADMIN_ACTION] SUCCESS {name} อัพเดท web setting
		var adminActionCreateBody model.AdminActionCreateRequest
		adminActionCreateBody.AdminId = req.UpdateBy
		adminActionCreateBody.TypeId = model.ADMIN_ACTION_MANAGE_APPLICATION
		adminActionCreateBody.Detail = "อัพเดท web setting"
		adminActionCreateBody.JsonInput = helper.StructJson(req)
		if _, err := s.adminAction.CreateSuccessAdminAction(adminActionCreateBody); err != nil {
			return nil
		}

		// P.Mink ข้อเพิ่มแยก 2024/09/24
		if req.IsTotpVerify != nil && *req.IsTotpVerify == false && record.IsTotpVerify == true {
			// [ADMIN_ACTION] SUCCESS {name} ปิดการใช้งาน Two-Factor Authentication
			var adminActionCreateBody model.AdminActionCreateRequest
			adminActionCreateBody.AdminId = req.UpdateBy
			adminActionCreateBody.TypeId = model.ADMIN_ACTION_MANAGE_APPLICATION
			adminActionCreateBody.Detail = "ปิดการใช้งาน Two-Factor Authentication"
			adminActionCreateBody.JsonInput = helper.StructJson(req)
			if _, err := s.adminAction.CreateSuccessAdminAction(adminActionCreateBody); err != nil {
				return nil
			}
		}
	}

	return nil
}

func (s *configurationService) GetWebConfiguration(userId int64) (*model.GetWebConfigurationResponse, error) {

	// configuration, err := s.repo.GetWebConfiguration()
	// if err != nil {
	// 	return nil, err
	// }

	configuration, err := s.repo.GetConfiguration()
	if err != nil {
		return nil, err
	}
	configurationBot, err := s.repo.GetWebBotaccountWebConfigs()
	if err != nil {
		return nil, err
	}

	var response model.GetWebConfigurationResponse
	response.MinimumDeposit = configuration.MinimumDeposit
	response.MinimumWithdraw = configuration.MinimumWithdraw
	response.MinFirstMemberDeposit = configuration.MinFirstMemberDeposit
	if configurationBot != nil {
		withdrawConfigWeb, err := strconv.ParseFloat(configurationBot.ConfigVal, 64)
		if err != nil {
			// Handle the error if the conversion fails
			return nil, err
		}
		response.MaximumWithdraw = withdrawConfigWeb
	} else {
		response.MaximumWithdraw = 49999.00
	}
	response.UseUploadDepositSlip = configuration.UseUploadDepositSlip
	response.UploadDepositSlipType = configuration.UploadDepositSlipType

	// Max User Withdraw Per Day
	bankLimitConfiguration, err := s.repo.GetUserBankLimit(userId)
	if err != nil {
		return nil, err
	}
	response.MaximumWithdrawPerDay = bankLimitConfiguration.MaxUserWithdrawAmount
	response.CurrentWithdrawPerDay = bankLimitConfiguration.CurrentWithdrawAmount
	response.MaximumWithdrawCount = bankLimitConfiguration.MaxUserWithdrawCount
	response.CurrentWithdrawCount = bankLimitConfiguration.CurrentWithdrawCount

	return &response, nil
}

func (s *configurationService) GetContactConfig() (*model.GetContactConfigWebResponse, error) {

	line, err := s.repo.GetContactConfig()
	if err != nil {
		return nil, badRequest("ยังไม่ได้ตั้งค่า Contact ")
	}

	return line, nil
}

func (s configurationService) UploadImageToCloudflareConfigLogo(imageFileBody *http.Request) (*model.FileUploadResponse, error) {

	fileReader, newFileName, err := imageFileBody.FormFile("file")
	if err != nil {
		log.Println("UploadFileCloudFlare.ERROR", err)
		return nil, err
	}

	filename := &newFileName.Filename

	dbName := os.Getenv("DB_NAME")

	// [set imageCloudFlarePathName]
	pathName := fmt.Sprintf("cbgame/%v/configlogo/upload/image/", dbName)
	//! ส่ง Id กับไฟล์ reader
	var newImageId *model.FileUploadResponse
	fileData, err := s.repo.UploadImageToCloudflare(pathName, *filename, fileReader)
	if err != nil {
		log.Println("UploadFileCloudFlare.ERROR", err)
		return nil, badRequest("UNABLE_TO_UPLOAD_FILE")
	}

	newImageId = &model.FileUploadResponse{
		ImageUrl: fileData.FileUrl,
	}

	return newImageId, nil
}

func (s *configurationService) GetUserBankLimit(userId int64) (*model.UserBankLimitResponse, error) {

	data, err := s.repo.GetUserBankLimit(userId)
	if err != nil {

		return nil, err
	}
	return data, nil
}

func (s *configurationService) GetBankLimitConfiguration() (*model.BankLimitConfigurationResponse, error) {

	configuration, err := s.repo.GetBankLimitConfiguration()
	if err != nil {
		if err.Error() == gorm.ErrRecordNotFound.Error() {
			_, err = s.repo.CreateBankLimitConfiguration(model.CreateBankLimitConfigurationBody{
				MaxUserWithdrawAmount: 0,
				MaxUserWithdrawCount:  0,
			})
			if err != nil {
				return nil, err
			}
			// REGET
			configuration2, err := s.repo.GetBankLimitConfiguration()
			if err != nil {
				return nil, err
			}
			return configuration2, nil
		}
		return nil, err
	}
	return configuration, nil
}

func (s *configurationService) UpdateBankLimitConfiguration(req model.UpdateBankLimitConfigurationRequest) error {

	record, err := s.GetBankLimitConfiguration()
	if err != nil {
		return err
	}

	if record != nil {
		var body model.UpdateBankLimitConfigurationBody
		body.Id = record.Id
		body.MaxUserWithdrawAmount = req.MaxUserWithdrawAmount
		body.MaxUserWithdrawCount = req.MaxUserWithdrawCount
		err = s.repo.UpdateBankLimitConfiguration(body)
		if err != nil {
			return err
		}

		// [ADMIN_ACTION] SUCCESS {name} อัพเดท การตั้งค่าจำกัดการถอนสูงสุด
		var adminActionCreateBody model.AdminActionCreateRequest
		adminActionCreateBody.AdminId = req.UpdateBy
		adminActionCreateBody.TypeId = model.ADMIN_ACTION_MANAGE_APPLICATION
		adminActionCreateBody.Detail = "อัพเดท การตั้งค่าจำกัดการถอนสูงสุด"
		adminActionCreateBody.JsonInput = helper.StructJson(req)
		if _, err := s.adminAction.CreateSuccessAdminAction(adminActionCreateBody); err != nil {
			return nil
		}
	}

	return nil
}

func (s *configurationService) UploadImageToS3ConfigLogo(imageFileBody *http.Request) (*model.FileUploadResponse, error) {

	fileReader, newFileName, err := imageFileBody.FormFile("file")
	if err != nil {
		log.Println("UploadFileS3.ERROR", err)
		return nil, err
	}

	filename := &newFileName.Filename

	dbName := os.Getenv("DB_NAME")

	pathName := fmt.Sprintf("cbgame/%v/config-logo/", dbName)
	var newImageId *model.FileUploadResponse
	fileData, err := s.repo.UploadImageToS3(pathName, *filename, fileReader)
	if err != nil {
		log.Println("UploadFileS3.ERROR", err)
		return nil, badRequest("UNABLE_TO_UPLOAD_FILE")
	}

	newImageId = &model.FileUploadResponse{
		ImageUrl: fileData.ImageUrl,
	}

	return newImageId, nil
}

func (s configurationService) GetAgentGamePrioritySettingList(req model.GetAgentGamePrioritySettingListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.Pagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	gamePrioritySettingList, total, err := s.repo.GetAgentGamePrioritySettingList(req)
	if err != nil {
		return nil, err
	}

	return &model.SuccessWithPagination{
		Total: total,
		List:  gamePrioritySettingList,
	}, nil
}

func (s configurationService) SortGamegentGamePriorityOrder(req model.DragSortRequest, adminUpdateID int64) error {

	if err := s.repo.SortGamegentGamePriorityOrder(req); err != nil {
		return err
	}

	var adminActionCreateBody model.AdminActionCreateBody
	adminActionCreateBody.AdminId = adminUpdateID
	adminActionCreateBody.TypeId = model.ADMIN_ACTION_GAME_PRIORITY_SETTING
	adminActionCreateBody.Detail = fmt.Sprintf("แก้ไขข้อมูล ลำดับเกมส์")
	adminActionCreateBody.IsSuccess = true
	adminActionCreateBody.IsShow = true
	adminActionCreateBody.JsonInput = helper.StructJson(req)
	adminActionCreateBody.JsonOutput = "update success"
	if _, err := s.repo.CreateAdminAction(adminActionCreateBody); err != nil {
		return err
	}
	return nil
}

func (s configurationService) IncrementAgentGameTotalPlayed(vendorCode string) error {

	if err := s.repo.IncrementAgentGameTotalPlayed(vendorCode); err != nil {
		return err
	}

	return nil
}

func (s configurationService) UpdateAgentGamePrioritySetting(body model.UpdateAgentGamePrioritySettingBody) error {

	if err := s.repo.UpdateAgentGamePrioritySetting(body); err != nil {
		return err
	}

	return nil
}

func (s configurationService) CreateBannerSetting(req model.CreateBannerSettingRequest) error {

	var body model.CreateBannerSettingBody
	body.Lang = req.Lang
	body.ImageUrl = req.ImageUrl
	body.LinkUrl = req.LinkUrl
	body.IsShowPublic = req.IsShowPublic
	body.IsShowLogedin = req.IsShowLogedin
	body.CreatedByID = req.CreatedByID
	if err := s.repo.CreateBannerSetting(body); err != nil {
		return err
	}

	return nil
}

func (s configurationService) UpdateBannerSetting(id int64, req model.BannerSettingUpdateRequest) error {

	var body model.BannerSettingUpdateBody
	body.ImageUrl = req.ImageUrl
	body.LinkUrl = req.LinkUrl
	body.IsShowPublic = req.IsShowPublic
	body.IsShowLogedin = req.IsShowLogedin
	if err := s.repo.UpdateBannerSetting(id, body); err != nil {
		return err
	}

	return nil
}

func (s configurationService) GetPublicBannerSettingList(req model.GetBannerSettingListRequest) ([]model.GetBannerSettingListResponse, error) {

	bannerSettingList, err := s.repo.GetPublicBannerSettingList(req)
	if err != nil {
		return nil, err
	}
	return bannerSettingList, nil
}

func (s configurationService) GetBannerSettingList(req model.GetBannerSettingListRequest) ([]model.GetBannerSettingListResponse, error) {

	bannerSettingList, err := s.repo.GetBannerSettingList(req)
	if err != nil {
		return nil, err
	}
	return bannerSettingList, nil
}

func (s configurationService) DeleteBannerSetting(req model.DeleteBannerSettingRequest) error {

	for _, v := range req.Id {
		body := model.DeleteBannerSettingBody{
			Id:          v,
			DeletedAt:   req.DeletedAt,
			DeletedByID: req.DeletedByID,
		}
		if err := s.repo.DeleteBannerSetting(body); err != nil {
			return err
		}
	}

	return nil
}

func (s *configurationService) UploadImageToS3ConfigBanner(imageFileBody *http.Request) (*model.FileUploadResponse, error) {

	fileReader, newFileName, err := imageFileBody.FormFile("file")
	if err != nil {
		log.Println("UploadFileS3.ERROR", err)
		return nil, err
	}

	filename := &newFileName.Filename

	dbName := os.Getenv("DB_NAME")

	pathName := fmt.Sprintf("cbgame/%v/config-banner/", dbName)
	var newImageId *model.FileUploadResponse
	fileData, err := s.repo.UploadImageToS3(pathName, *filename, fileReader)
	if err != nil {
		log.Println("UploadFileS3.ERROR", err)
		return nil, badRequest("UNABLE_TO_UPLOAD_FILE")
	}

	newImageId = &model.FileUploadResponse{
		ImageUrl: fileData.ImageUrl,
	}

	return newImageId, nil
}

func (s *configurationService) GetConfigurationCheckShowTransactionResponse() (*model.ConfigurationCheckShowTransactionResponse, error) {
	configuration, err := s.repo.GetConfigurationCheckShowTransactionResponse()
	if err != nil {
		return nil, err
	}
	return configuration, nil
}

func (s *configurationService) GetConfigurationRegisterFormat() ([]model.SelectOptions, error) {

	options, err := s.repo.GetConfigurationRegisterFormat()
	if err != nil {
		return nil, err
	}
	return options, nil

}

func (s *configurationService) WebGetConfigurationRegisterFormat() (*model.WebGetConfigurationRegisterFormatResponse, error) {

	configuration, err := s.repo.GetConfiguration()
	if err != nil {
		return nil, err
	}

	var res model.WebGetConfigurationRegisterFormatResponse
	res.ConfigurationRegisterFormatId = configuration.ConfigurationRegisterFormatId

	return &res, nil
}

func (s *configurationService) UpdateActivityMenu(body model.UpdateActivityMenuRequest) error {

	if err := s.repo.UpdateActivityMenu(body); err != nil {
		return err
	}

	// // [ADMIN_ACTION] TODO NOT CONFIRM
	// var adminActionCreateBody model.AdminActionCreateRequest
	// adminActionCreateBody.AdminId = body.UpdateBy
	// adminActionCreateBody.TypeId = model.ADMIN_ACTION_MANAGE_APPLICATION
	// adminActionCreateBody.Detail = "อัพเดท Activity Menu"
	// adminActionCreateBody.JsonInput = helper.StructJson(body)
	// if _, err := s.adminAction.CreateSuccessAdminAction(adminActionCreateBody); err != nil {
	// 	return nil
	// }

	return nil
}

func (s *configurationService) SortActivityMenu(req model.DragSortRequest) error {

	if err := s.repo.SortActivityMenu(req); err != nil {
		return err
	}

	return nil
}

func (s *configurationService) UploadImageActivityMenu(imageFileBody *http.Request) (*model.FileUploadResponse, error) {

	fileReader, newFileName, err := imageFileBody.FormFile("file")
	if err != nil {
		log.Println("UploadFileS3.ERROR", err)
		return nil, err
	}

	filename := &newFileName.Filename

	dbName := os.Getenv("DB_NAME")

	pathName := fmt.Sprintf("cbgame/%v/activity-menu/", dbName)
	var newImageId *model.FileUploadResponse
	fileData, err := s.repo.UploadImageToS3(pathName, *filename, fileReader)
	if err != nil {
		log.Println("UploadFileS3.ERROR", err)
		return nil, badRequest("UNABLE_TO_UPLOAD_FILE")
	}

	newImageId = &model.FileUploadResponse{
		ImageUrl: fileData.ImageUrl,
	}

	return newImageId, nil
}

func (s *configurationService) GetActivityMenu(req model.GetActivityMenuRequest) ([]model.GetActivityMenuResponse, error) {

	activityMenuList, err := s.repo.GetActivityMenu(req)
	if err != nil {
		return nil, err
	}

	return activityMenuList, nil
}

func (s *configurationService) GetActivityMenuItem(req model.GetActivityMenuItemRequest) (*model.GetActivityMenuResponse, error) {

	activityMenu, err := s.repo.GetActivityMenuItem(req)
	if err != nil {
		return nil, err
	}

	return activityMenu, nil
}

func (s *configurationService) GetRunningMessage() (*model.RunningMessageResponse, error) {

	runningMessage, err := s.repo.GetRunningMessage()
	if err != nil {
		var runningMessage model.RunningMessageResponse
		runningMessage.Message = ""
		return &runningMessage, nil
	}

	return runningMessage, nil
}
