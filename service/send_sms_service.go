package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"errors"
	"log"
)

type SendSmsService interface {
	SendSmsUserInactive(req model.CreateSendSmsRequest) error
	GetSendSmsList(req model.GetSendSmsListRequest) (*model.SuccessWithPagination, error)
	GetSendSmsSenderName() ([]model.SelectOptions, error)
}

type sendSmsService struct {
	repo repository.SendSmsRepository
}

func NewSendSmsService(
	repo repository.SendSmsRepository,
) SendSmsService {
	return &sendSmsService{repo}
}

func (s *sendSmsService) SendSmsUserInactive(req model.CreateSendSmsRequest) error {

	getWebLocal, _ := s.repo.GetLocalWebInfo()
	if getWebLocal != nil {
		if getWebLocal.SmsCreditBalance <= -1000 {
			log.Println("SendSmsUserInactive.WEB_OUT_OF_CREDIT")
			return badRequest("WEB_OUT_OF_CREDIT_SMS")
		}
	}

	userDetails, err := s.repo.GetSendSmsUserInactive(req.UserID)
	if err != nil {
		return err
	}

	var sendCyberSmsRequest model.SendCyberSmsRequest
	sendCyberSmsRequest.Message = req.Message

	senderName, err := s.repo.GetSendSmsSenderName(req.SenderNameId)
	if err != nil {
		return err
	}

	sendCyberSmsRequest.SenderName = senderName.SenderName
	for _, user := range userDetails {
		sendCyberSmsRequest.Phones = append(sendCyberSmsRequest.Phones, user.Phone)
	}

	sendCyberSmsResponse, err := s.repo.SentBulkSms(sendCyberSmsRequest)
	if err != nil {
		if sendCyberSmsResponse != nil {
			return errors.New(sendCyberSmsResponse.Message)
		} else {
			return err
		}
	}

	var createSmsSendbodys []model.CreateSendSmsBody
	if sendCyberSmsResponse != nil && len(sendCyberSmsResponse.Data) > 0 {

		if err := s.repo.DecreaseSmsCredit(int64(len(userDetails))); err != nil {
			log.Println("SendSmsUserInactive.DECREASE_SMS_CREDIT_ERROR", err)
		}

		for _, smsRes := range sendCyberSmsResponse.Data {
			for _, user := range userDetails {
				if smsRes.Phone == user.Phone {
					var createSmsSendbody model.CreateSendSmsBody
					createSmsSendbody.UserId = user.ID
					createSmsSendbody.RefId = smsRes.ID
					createSmsSendbody.Phone = user.Phone
					createSmsSendbody.SenderNameId = req.SenderNameId
					createSmsSendbody.Message = req.Message
					createSmsSendbody.CreditSmsAmount = 1

					switch smsRes.Status {
					case model.SMS_LOG_STATUS_COMPLETED, model.SMS_LOG_STATUS_DELIVERED:
						createSmsSendbody.Status = model.SEND_SMS_ACCEPTED
					case model.SMS_LOG_STATUS_UNDELIVERED, model.SMS_LOG_STATUS_EXPIRED, model.SMS_LOG_STATUS_REJECTED:
						createSmsSendbody.Status = model.SEND_SMS_UNDELIVERED
					case model.SMS_LOG_STATUS_BLACKLIST:
						createSmsSendbody.Status = model.SEND_SMS_BLACKLIST
					default:
						createSmsSendbody.Status = model.SEND_SMS_PENDING
					}
					createSmsSendbody.LogStatus = smsRes.Status
					createSmsSendbody.CreatedById = req.CreatedById

					createSmsSendbodys = append(createSmsSendbodys, createSmsSendbody)
				}
			}
		}
	}

	if err := s.repo.CreateSendSmsUserInactive(createSmsSendbodys); err != nil {
		return err
	}

	return nil
}

func (s *sendSmsService) GetSendSmsList(req model.GetSendSmsListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.Pagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	list, total, err := s.repo.GetSendSmsList(req)
	if err != nil {
		return nil, internalServerError(err)
	}

	var getPendingRefId model.BulkStatisSmsCyberRequest
	listIndexMap := make(map[int64]*model.SendSmsListResponse) // Map for quick lookup

	for i := range list {
		listIndexMap[list[i].RefId] = &list[i]
		if list[i].Status == model.SEND_SMS_PENDING {
			getPendingRefId.SmsId = append(getPendingRefId.SmsId, list[i].RefId)
		}
	}

	if len(getPendingRefId.SmsId) > 0 {
		sendCyberSmsResponse, err := s.repo.BulkSmsStatus(getPendingRefId)
		if err != nil {
			return nil, internalServerError(err)
		}

		for _, smsRes := range sendCyberSmsResponse.Data {
			if item, exists := listIndexMap[smsRes.ID]; exists {
				switch smsRes.Status {
				case model.SMS_LOG_STATUS_COMPLETED, model.SMS_LOG_STATUS_DELIVERED:
					item.Status = model.SEND_SMS_ACCEPTED
				case model.SMS_LOG_STATUS_UNDELIVERED, model.SMS_LOG_STATUS_EXPIRED, model.SMS_LOG_STATUS_REJECTED:
					item.Status = model.SEND_SMS_UNDELIVERED
				case model.SMS_LOG_STATUS_BLACKLIST:
					item.Status = model.SEND_SMS_BLACKLIST
				default:
					item.Status = model.SEND_SMS_PENDING
				}

				updateSendSmsBody := model.UpdateSendSmsBody{
					RefId:     smsRes.ID,
					Status:    item.Status,
					LogStatus: smsRes.Status,
				}
				if err := s.repo.UpdateSendSmsStatus(updateSendSmsBody); err != nil {
					return nil, internalServerError(err)
				}
			}
		}
	}

	return &model.SuccessWithPagination{
		List:  list,
		Total: total,
	}, nil
}

func (s *sendSmsService) GetSendSmsSenderName() ([]model.SelectOptions, error) {

	senderName, err := s.repo.GetSendSmsSenderNameOption()
	if err != nil {
		return nil, internalServerError(err)
	}
	return senderName, nil
}
