package service

import (
	"bytes"
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"math"
	"net/http"
	"os"
	"regexp"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm"
)

type AccountingService interface {
	GetConnectionStatus() ([]model.ConnectionStatusResponse, error)
	GetAccountStatus() ([]model.AccountStatusResponse, error)

	CheckCurrentAdminId(input any) (*int64, error)
	CheckCurrentUsername(input any) (*string, error)
	CheckConfirmationPassword(req model.ConfirmRequest) (*bool, error)

	GetBanks(req model.BankListRequest) (*model.SuccessWithPagination, error)
	GetAccountTypes(req model.AccountTypeListRequest) (*model.SuccessWithPagination, error)

	//list fast bank update
	GetBankAccounts(req model.BankAccountListRequest) (*model.SuccessWithPagination, error)
	GetFastBankAccountList(req model.BankAccountListRequest) (*model.SuccessWithPagination, error)
	//list normal
	GetBankAccountList(req model.BankAccountListRequest) (*model.SuccessWithPagination, error)
	GetBankAccountById(req model.BankGetByIdRequest) (*model.BankAccount, error)
	GetBankAccountPriorities() (*model.SuccessWithPagination, error)
	CreateBankAccount(body model.BankAccountCreateRequest) error
	UpdateBankAccount(id int64, req model.BankAccountUpdateRequest) error
	DeleteBankAccount(req model.BankAccountDeleteRequest) error
	UpdateBankAccountPriority(list model.PriorityWithdrawstructRequest) error
	UpdateBankAccountIsShowBank(req model.UpdateBankAccountIsShowBankRequest) error

	SortBankAccountList(req model.DragSortRequest) error
	GetTransactionById(req model.BankGetByIdRequest) (*model.BankAccountTransaction, error)
	GetTransactions(req model.BankAccountTransactionListRequest) (*model.SuccessWithPagination, error)
	CreateTransaction(body model.BankAccountTransactionBody, adminId int64) error
	UpdateTransaction(id int64, body model.BankAccountTransactionBody) error
	DeleteTransaction(id int64) error

	GetTransferById(req model.BankGetByIdRequest) (*model.BankAccountTransfer, error)
	GetTransfers(req model.BankAccountTransferListRequest) (*model.SuccessWithPagination, error)
	CreateTransfer(body model.BankAccountTransferBody) error
	ConfirmTransfer(id int64, actorId int64) error
	DeleteTransfer(id int64) error

	GetAccountStatements(req model.BankAccountStatementListRequest) (*model.SuccessWithPagination, error)
	GetExternalStatementWithTransactionList(req model.ExternalStatementWithTransactionListRequest) ([]model.ExternalStatementWithTransactionReponse, int64, error)
	GetExternalUnreadStatementWithTransactionList(req model.ExternalStatementWithTransactionListRequest) ([]model.ExternalStatementWithTransactionReponse, int64, error)
	GetAccountStatementById(req model.BankGetByIdRequest) (*model.BankStatement, error)
	// [********] UNUSED AddAccountStatementToWebhook(req model.RecheckWebhookRequest) error
	MatchBankStatementFromFastbankStatementNotAuto(systemAccount model.BankAccount, bodyCreateState model.BankStatementCreateBody) error

	GetAccountNoFromWebhook(bankCode string, info string) (string, error)
	GetExternalSettings() (*model.ExternalSettings, error)
	GetCustomerAccountsInfo(req model.CustomerAccountInfoRequest) (*model.CustomerAccountInfo, error)
	GetExternalAccounts() (*model.SuccessWithPagination, error)
	GetExternalAccountBalance(req model.ExternalAccountStatusRequest) (*model.ExternalAccountBalance, error)
	GetExternalAccountStatus(req model.ExternalAccountStatusRequest) (*model.GetExternalAccountStatus, error)
	CreateExternalAccount(body model.ExternalAccountCreateBody) (*model.ExternalAccountCreateResponse, error)
	UpdateExternalAccount(body model.ExternalAccountUpdateBody) (*model.ExternalAccountCreateResponse, error)
	EnableExternalAccount(req model.BankAccountToggleFastbankRequest) (*model.ExternalAccountStatus, error)
	DeleteExternalAccount(req model.ExternalAccountStatusRequest) error
	TransferExternalAccount(req model.ExternalAccountTransferRequest) error
	// WEBHOOK
	CreateBankStatementFromWebhookOnly(data model.WebhookStatement) (*int64, error)
	CreateBankStatementFromWebhookAndAuto(data model.WebhookStatement, adminId *int64) (*int64, error)

	CreateBotaccountConfig(body model.BotAccountConfigCreateBody) error
	GetExternalAccountLogs(req model.ExternalStatementListRequest) (*model.SuccessWithPagination, error)
	GetExternalAccountStatements(req model.ExternalStatementListRequest) (*model.SuccessWithPagination, error)
	GetExternalAccountStatementByTimestamp(req model.ExternalStatementListRequest) (*model.SuccessWithPagination, error)
	CreateWebhookLog(logType string, jsonRequest string) (*int64, error)
	SetSuccessWebhookLog(id int64, jsonPayload string) error
	SetFailedWebhookLog(id int64, logStatus string) error
	UserFirstDepositCommission(member model.Member, depositAmount float64) error

	CreateDepositTransaction(possibleOwner model.Member, bodyCreateState model.BankStatementCreateBody, admin model.ApprovedByAdmin) (*int64, error)
	ConfirmDepositTransaction(id int64, req model.BankConfirmDepositRequest) error
	CreateDepositTransactionNoOwner(bodyCreateState model.BankStatementCreateBody, admin model.ApprovedByAdmin, transType int64) (*int64, error)
	CreateBankTransactionNoOwner(data model.BankTransactionNoOwnerCreateBody) (*int64, error)
	UpdateDepositTransactionOwner(possibleOwner model.Member, bodyCreateState model.BankStatementCreateBody, admin model.ApprovedByAdmin) (*int64, error)
	SetStatementOwnerMatched(id int64, req model.BankStatementMatchRequest, accessNoti string) error

	GetWebBankDepositAccount(userId int64) ([]model.WebBankAccountResponse, error)
	GetActiveDepositBankAccountList() ([]model.WebBankAccountResponse, error)

	//priority withdraw
	BankAccountWithDrawPriorityValidation(amount float64) (*model.PriorityValidation, error)
	GetBankAccountForValidation() ([]model.BankAccountResponse, error)
	BankAccountWithDrawValidation(bankId int64, amount float64) (*model.PriorityValidation, error)

	CheckDepositAccountMoveTransaction(check model.CheckDepositAccountMoveTransaction) (bool, error)

	CreateBankTransactionLog(req model.BankTransactionLogCreateRequest) (*int64, error)
	//config
	GetBankWithdrawMaxConfig() (*float64, error)

	GetSummaryReportAccountList() ([]model.FastBankAccountAndDailySummaryResponse, error)

	// test
	ExternalMatchWithdrawTransaction(statementBody model.BankStatementCreateBody) error
	TestUnmarshal(responseData []byte) (interface{}, error)
	// REPORT - graph
	GetBankTransactionSummaryGraph2(req model.BankTransactionGraph2Request) (*model.BankTransactionGraph2Response, error)
	// [ADMIN_LOG]
	LogAdmin(name string, adminId int64, req interface{}) error
	CreateSuccessAdminAction(req model.AdminActionCreateRequest) (*int64, error)
	// exchage
	GetExchangeCurrencyList(req model.GetExchangeCurrencyListRequest) ([]model.GetExchangeCurrencyListResponse, error)
	CreateExchangeRate(req []model.CreateExchangeRateRequest, createdByAdmin int64) error
	GetExchangeRateList() ([]model.GetExchangeRateList, error)
	GetUserExchangeRateList() ([]model.GetExchangeRateList, error)
	GetLaosExchangeCurrency() (*model.GetExchangeRate, error)
	GetExchangeUpdateLogList(req model.GetExchangeUpdateLogListRequest) (*model.SuccessWithPagination, error)

	//sms mode
	CreateSmsModeFastbankDeposit(body model.CreateSmsModeDepositFastbankRequest) (*model.CreateSmsModeDepositFastbankResponse, error)

	// backup
	MigrateOldBankAccount() error

	// upload s3
	UploadImageToS3BankAccountQr(imageFileBody *http.Request) (*model.FileUploadResponse, error)

	// account
	TotalBankStatementSummary(req model.TotalBankStatementRequest) (*model.TotalBankTransactionSummaryResponse, error)
}

type accountingService struct {
	repo                      repository.AccountingRepository
	userService               UserService
	gameService               GameService
	agentRepo                 repository.AgentConnectRepository
	notiService               NotificationService
	affiliateService          AffiliateService
	allianceService           AllianceService
	adminAction               AdminActionService
	activityLuckyWheelService ActivityLuckyWheelService
	promotionWebService       PromotionWebService
	shareDb                   *gorm.DB
}

func NewAccountingService(
	repo repository.AccountingRepository,
	userService UserService,
	gameService GameService,
	agentRepo repository.AgentConnectRepository,
	notiService NotificationService,
	affiliateService AffiliateService,
	allianceService AllianceService,
	adminAction AdminActionService,
	activityLuckyWheelService ActivityLuckyWheelService,
	promotionWebService PromotionWebService,
	shareDb *gorm.DB,
) AccountingService {
	return &accountingService{repo, userService, gameService, agentRepo, notiService, affiliateService, allianceService, adminAction, activityLuckyWheelService, promotionWebService, shareDb}
}

func (s *accountingService) TestUnmarshal(responseData []byte) (interface{}, error) {

	var result model.JbpayDepositCreateRemoteResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		log.Println("JbpayDeposit resp.Body ------> ", string(responseData))
		log.Println("Unmarshal.errJson ------> ", errJson)
		// TRY-1
		var result1 model.JbpayDepositCreateRemote2Response
		errJson1 := json.Unmarshal(responseData, &result1)
		if errJson1 == nil {
			// Convert to model.JbpayDepositCreateRemoteResponse
			result = model.JbpayDepositCreateRemoteResponse{
				Code:    result1.Code,
				Message: result1.Message,
				Data: model.JbpayDepositCreateRemoteResponseData{
					OrderNo:            result1.Data.OrderNo,
					MchOrderNo:         result1.Data.MchOrderNo,
					ExpirationDate:     result1.Data.ExpirationDate,
					Status:             result1.Data.Status,
					Amount:             result1.Data.Amount,
					OriginCode:         result1.Data.OriginCode,
					Redirect:           result1.Data.Redirect,
					BankCardName:       result1.Data.BankCardName,
					BankDiscountAmount: result1.Data.BankDiscountAmount,
					SubBankName:        result1.Data.SubBankName,
					BankName:           result1.Data.BankName,
					AccountNumber:      result1.Data.AccountNumber,
				},
			}
			return &result, nil
		} else {
			log.Println("Unmarshal.errJson1 ------> ", errJson1)
		}
		// TRY-2-err1
		var errMsg2 model.JbpayErrorStringRemoteResponse
		errJson2 := json.Unmarshal(responseData, &errMsg2)
		if errJson2 == nil {
			return nil, errors.New(errMsg2.Message)
		} else {
			log.Println("Unmarshal.errJson2 ------> ", errJson2)
		}
		// TRY-3-err2
		var errMsg3 model.JbpayErrorRemoteResponse
		errJson3 := json.Unmarshal(responseData, &errMsg3)
		if errJson3 == nil {
			return nil, errors.New(errMsg3.Message)
		} else {
			log.Println("Unmarshal.errJson3 ------> ", errJson3)
		}
		return nil, errors.New("INVALID_UNMARSHAL_JSON")
	}

	return result, nil
}

func (s *accountingService) GetConnectionStatus() ([]model.ConnectionStatusResponse, error) {
	list, err := s.repo.GetConnectionStatus()
	if err != nil {
		return nil, internalServerError(err)
	}
	return list, nil
}

func (s *accountingService) GetAccountStatus() ([]model.AccountStatusResponse, error) {
	list, err := s.repo.GetAccountStatus()
	if err != nil {
		return nil, internalServerError(err)
	}
	return list, nil
}

func (s *accountingService) CheckCurrentAdminId(input any) (*int64, error) {

	// input := c.MustGet("adminId")
	if input == nil {
		return nil, badRequest(invalidCurrentAdminId)
	}
	var adminId = int64(input.(float64))
	if adminId <= 0 {
		return nil, badRequest(invalidCurrentAdminId)
	}
	return &adminId, nil
}

func (s *accountingService) CheckCurrentUsername(input any) (*string, error) {

	// input := c.MustGet("username")
	if input == nil {
		return nil, badRequest(invalidCurrentAdminId)
	}
	var username = input.(string)
	// if username == "" {
	// 	return nil, badRequest(invalidCurrentAdminId)
	// }
	return &username, nil
}

func (s *accountingService) CheckConfirmationPassword(req model.ConfirmRequest) (*bool, error) {

	user, err := s.repo.GetAdminById(req.UserId)
	if err != nil {
		log.Println(req)
		return nil, notFound(invalidConfirmation)
	}
	if user == nil {
		return nil, badRequest(invalidConfirmation)
	}
	if err := helper.CompareAdminPassword(req.Password, user.Password); err != nil {
		return nil, badRequest(invalidConfirmation)
	}
	token := true
	return &token, nil
}

func (s *accountingService) GetBanks(req model.BankListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	records, err := s.repo.GetBanks(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	return records, nil
}

func (s *accountingService) GetAccountTypes(req model.AccountTypeListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	records, err := s.repo.GetAccountTypes(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	return records, nil
}

func (s *accountingService) GetBankAccountById(req model.BankGetByIdRequest) (*model.BankAccount, error) {

	// ต้องไปดู flow ว่าเกิดไรขึ้น พอ เปิดใช้ function นี้ แล้ว มันจะเป็นอะไรบ้าง
	// err := s.UpdateBankAccountBalanceById(req.Id)
	// if err != nil {
	// 	return nil, internalServerError(err)
	// }

	record, err := s.repo.GetBankAccountById(req.Id)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(bankAccountNotFound)
		}
		return nil, internalServerError(err)
	}
	return record, nil
}

func (s *accountingService) GetBankAccounts(req model.BankAccountListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	_, err := s.UpdateAllBankAccountBotStatus()
	if err != nil {
		return nil, nil
	}

	list, err := s.repo.GetBankAccounts(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	return list, nil
}

func (s *accountingService) GetFastBankAccountList(req model.BankAccountListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	// NOT UPDATE, Just INFO
	// err := s.UpdateAllBankAccountBotStatus()
	// if err != nil {
	// 	return nil, nil
	// }

	list, total, err := s.repo.GetFastBankAccountList(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	return &model.SuccessWithPagination{List: list, Total: total}, nil
}

func (s *accountingService) GetBankAccountForValidation() ([]model.BankAccountResponse, error) {

	_, err := s.UpdateAllBankAccountBotStatus()
	if err != nil {
		return nil, nil
	}

	list, err := s.repo.GetBankAccountForValidation()
	if err != nil {
		return nil, internalServerError(err)
	}
	return list, nil
}

func (s *accountingService) GetBankAccountList(req model.BankAccountListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}
	list, err := s.repo.GetBankAccounts(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	return list, nil
}

func (s *accountingService) GetBankAccountPriorities() (*model.SuccessWithPagination, error) {

	list, err := s.repo.GetBankAccountPriorities()
	if err != nil {
		return nil, internalServerError(err)
	}
	return list, nil
}

func (s *accountingService) CreateBankAccount(body model.BankAccountCreateRequest) error {

	bank, err := s.repo.GetBankById(body.BankId)
	if err != nil {
		log.Println(err)
		if err.Error() == recordNotFound {
			return notFound(bankNotFound)
		}
		return badRequest("Invalid Bank")
	}

	// ต้องดักกัน คนยิง postman เข้ามาแก้เพราะ หน้าบ้านจะส่งตัวแปรนั้นตาม field มาอยู่ ถ้าไม่ใช้ก็เอาออก
	// configBankMax, err := s.GetExternalAccountConfig("withdraw_max_per_time")
	// if err != nil {
	// 	return nil
	// }
	// perTimeFloat, err := strconv.ParseFloat(configBankMax.ConfigVal, 64)
	// if err != nil {
	// 	return nil
	// }
	// if body.BankWithdrawMaximum != perTimeFloat {
	// 	return badRequest("Bank Withdraw Maximum ต้องเท่ากัน " + configBankMax.ConfigVal)
	// }

	accountType, err := s.repo.GetAccounTypeById(body.AccountTypeId)
	if err != nil {
		log.Println(err)
		return badRequest("Invalid Account Type")
	}

	acNo := helper.StripAllButNumbers(body.AccountNumber)
	exist, err := s.repo.HasBankAccount(acNo)
	if err != nil {
		log.Println(err)
		return internalServerError(err)
	}
	if exist {
		return badRequest("Account already exist")
	}
	// const (
	// 	KBANK        = int64(1)
	// 	SCB          = int64(2)
	// 	BBL          = int64(3)
	// 	BAY          = int64(4)
	// 	KT           = int64(5)
	// 	TTB          = int64(6)
	// 	GSB          = int64(7)
	// 	BAAC         = int64(8)
	// 	KK           = int64(9)
	// 	GHB          = int64(10)
	// 	UOB          = int64(11)
	// 	LH           = int64(12)
	// 	CIMB         = int64(13)
	// 	HSBC         = int64(14)
	// 	ICBC         = int64(15)
	// 	ISBT         = int64(16)
	// 	TISCO        = int64(17)
	// 	CITI         = int64(18)
	// 	SCBT         = int64(19)
	// 	TRUE         = int64(20)
	// 	EXTERNAL     = int64(21)
	// 	UNKNOWN_BANK = int64(100)
	// )

	var createBody model.BankAccountCreateBody
	createBody.BankId = bank.Id
	createBody.AccountName = body.AccountName
	createBody.AccountNumber = acNo
	createBody.DeviceUid = body.DeviceUid
	createBody.AccountBalance = 0
	createBody.ConnectionStatusId = model.CONNECTION_DISCONNECTED
	createBody.PinCode = body.PinCode
	createBody.AdminUpdatedAt = time.Now()
	createBody.AutoWithdrawTypeId = body.AutoWithdrawTypeId
	createBody.IsManualBank = body.IsManualBank
	createBody.ShowBankDepositOverDueTime = body.ShowBankDepositOverDueTime
	createBody.ShowBankDepositMaxDueTime = body.ShowBankDepositMaxDueTime
	// createBody.BankWithdrawMaximum = body.BankWithdrawMaximum
	// createBody.AutoWithdrawMaximum = body.AutoWithdrawMaximum

	// P.MINK confirm กับพี่มิงค์
	if body.SmsMode && body.BankId != model.BANK_ID_SCB {
		createBody.AccountTypeId = model.BANK_ACCOUNT_TYPE_DEPOSIT_ONLY
	} else {
		createBody.AccountTypeId = accountType.Id
	}

	// || body.BankId == model.BANK_ID_SCB
	// 2024/11/15 P.layer เอากลับมา scb แล้วไปดักตอน web hook เข้า + ตอนแสดง list scb จาก smsmode ให้ เป็น api mode ปกติ
	if body.BankId == model.BANK_ID_BBL || body.BankId == model.BANK_ID_BAY || body.BankId == model.BANK_ID_TTB || body.BankId == model.BANK_ID_KKP || body.BankId == model.BANK_ID_KBANK || body.BankId == model.BANK_ID_KTB || body.BankId == model.BANK_ID_SCB {
		createBody.SmsMode = body.SmsMode
	} else {
		createBody.SmsMode = false
	}

	if body.AccountTypeId == model.BANK_ACCOUNT_TYPE_HOLD {
		createBody.IsManualBank = true
		createBody.SmsMode = false
		createBody.AutoWithdrawTypeId = model.CONFIG_WEB_MANUAL_WITHDRAW
	}

	createBody.ImageUrl = body.ImageUrl
	CreatedbankAccountId, err := s.repo.CreateBankAccount(createBody)
	if err != nil {
		_, webhookErr := s.CreateWebhookLog("CreateBankAccount.CreateBankAccount, ERROR", helper.StructJson(struct {
			data model.BankAccountCreateBody
			err  error
		}{createBody, err}))
		log.Print(webhookErr)
		return internalServerError(err)
	}

	// 2024/17/09 show to bank p.Layer and p.Mink confirm no need to default when create
	var showBankBody []model.CreateBankAccountShowBank
	if len(body.ShowToBank) > 0 {
		for _, bankId := range body.ShowToBank {
			showBankBody = append(showBankBody, model.CreateBankAccountShowBank{
				BankAccountId: *CreatedbankAccountId,
				BankId:        bankId,
			})
		}
		if err := s.repo.CreateBankAccountShowBank(showBankBody); err != nil {
			return internalServerError(err)
		}
	}

	// FASTBANK
	var updateBody model.BankAccountUpdateBody
	if s.IsAllowCreateExternalAccount(acNo) && body.DeviceUid != "" && body.PinCode != "" && !s.HasExternalAccount(acNo) || body.SmsMode || s.IsAllowCreateExternalAccount(acNo) && body.DeviceUid != "" && body.BankId == model.BANK_ID_TRUE {
		var createExternalBody model.ExternalAccountCreateBody
		createExternalBody.AccountNo = acNo
		createExternalBody.BankCode = bank.Code
		createExternalBody.DeviceId = body.DeviceUid
		// ไม่ได้ใช้ createExternalBody.Password = data.Password
		createExternalBody.Pin = &body.PinCode
		createExternalBody.AccountName = body.AccountName
		// ไม่ได้ใช้ createExternalBody.Username = data.Username
		// ไม่ได้ใช้ createExternalBody.WebhookNotifyUrl = os.Getenv("ACCOUNTING_LOCAL_WEBHOOK_ENDPOINT") + "/api/accounting/webhooks/noti"
		createExternalBody.WebhookUrl = os.Getenv("ACCOUNTING_LOCAL_WEBHOOK_ENDPOINT") + "/api/accounting/webhooks/action"
		createExternalBody.SmsMode = body.SmsMode

		if createResp, err := s.CreateExternalAccount(createExternalBody); err != nil {
			logType := "CreateBankAccount.CreateExternalAccount, ERROR"
			jsonRequest := fmt.Sprintf("CreateExternalAccount: %s", helper.StructJson(createExternalBody))
			_, webhookErr := s.CreateWebhookLog(logType, jsonRequest)
			log.Print(webhookErr)
			return nil
		} else {
			logType := "CreateBankAccount.CreateExternalAccount, SUCCESS"
			jsonRequest := fmt.Sprintf("CreateExternalAccount: %s", helper.StructJson(createExternalBody))
			_, webhookErr := s.CreateWebhookLog(logType, jsonRequest)
			log.Print(webhookErr)
			var statusId int64
			if createResp.Enable {
				statusId = model.CONNECTION_CONNECTED
			} else {
				statusId = model.CONNECTION_DISCONNECTED
			}
			//update bank account
			updateBody.ExternalId = &createResp.Id
			updateBody.PinCode = &createResp.Pin
			updateBody.ConnectionStatusId = &statusId
		}
	}

	time := time.Now()
	updateBody.LastConnUpdateAt = &time
	updateBody.AccountPriorityWithdraw = CreatedbankAccountId
	if err := s.repo.UpdateBankAccount(*CreatedbankAccountId, updateBody); err != nil {
		return internalServerError(err)
	}

	// [ADMIN_ACTION] SUCCESS เพิ่มรายการธนาคาร SCB ชื่อบัญชี อัครเดช ชัยยา
	var adminActionCreateBody model.AdminActionCreateRequest
	adminActionCreateBody.AdminId = body.CreateBy
	adminActionCreateBody.TypeId = model.ADMIN_ACTION_ACCOUNT_MANAGE
	adminActionCreateBody.Detail = fmt.Sprintf("เพิ่มรายการธนาคาร %s ชื่อบัญชี %s", bank.Name, body.AccountName)
	adminActionCreateBody.JsonInput = helper.StructJson(body)
	adminActionCreateBody.JsonOutput = helper.StructJson(struct{ createdId int64 }{
		createdId: *CreatedbankAccountId,
	})
	if _, err := s.adminAction.CreateSuccessAdminAction(adminActionCreateBody); err != nil {
		return err
	}

	return nil
}

// func (s *accountingService) UpdateBankAccount(id int64, req model.BankAccountUpdateRequest) error {

// 	account, err := s.repo.GetBankAccountById(id)
// 	if err != nil {
// 		return internalServerError(err)
// 	}

// 	var updateBody model.BankAccountUpdateBody
// 	var updateExBody model.ExternalAccountUpdateBody
// 	// isAllowNoMainWithdraw := true
// 	// onMainWithdrawChange := false

// 	// Validate
// 	if req.BankId != nil && account.BankId != *req.BankId {
// 		bank, err := s.repo.GetBankById(*req.BankId)
// 		if err != nil {
// 			log.Println(err)
// 			if err.Error() == recordNotFound {
// 				return notFound(bankNotFound)
// 			}
// 			return badRequest("Invalid Bank")
// 		}
// 		updateBody.BankId = &bank.Id
// 		updateExBody.BankCode = &bank.Code
// 	}
// 	if req.AccountTypeId != nil && account.AccountTypeId != *req.AccountTypeId {
// 		accountType, err := s.repo.GetAccounTypeById(*req.AccountTypeId)
// 		if err != nil {
// 			log.Println(err)
// 			return badRequest("Invalid Account Type")
// 		}
// 		updateBody.AccountTypeId = &accountType.Id
// 	}
// 	if req.AccountName != nil && account.AccountName != *req.AccountName {
// 		updateBody.AccountName = req.AccountName
// 		updateExBody.AccountName = req.AccountName
// 	}
// 	if req.AccountNumber != nil && account.AccountNumber != *req.AccountNumber {
// 		acNo := helper.StripAllButNumbers(*req.AccountNumber)
// 		if acNo != "" {
// 			check, err := s.repo.HasBankAccount(acNo)
// 			if err != nil {
// 				return internalServerError(err)
// 			}
// 			if !check {
// 				log.Println(acNo)
// 				return notFound("Account already exist")
// 			}
// 			updateBody.AccountNumber = &acNo
// 			account.AccountNumber = acNo
// 		} else {
// 			updateBody.AccountNumber = &account.AccountNumber
// 		}
// 	}
// 	if req.DeviceUid != nil && account.DeviceUid != *req.DeviceUid {
// 		updateBody.DeviceUid = req.DeviceUid
// 		updateExBody.DeviceId = &account.DeviceUid
// 	}
// 	if req.PinCode != nil {
// 		// updateBody.PinCode = req.PinCode
// 		updateExBody.Pin = req.PinCode
// 	}
// 	if s.IsAllowCreateExternalAccount(account.AccountNumber) {
// 		// if _, err := s.HasExternalAccountConfig("allow_external_account_number", acNo); err != nil {
// 		// 	return nil
// 		// }

// 		if updateExBody.DeviceId == nil {
// 			updateExBody.DeviceId = &account.DeviceUid
// 		}
// 		// if updateExBody.Pin == nil {
// 		// 	updateExBody.Pin = &account.PinCode
// 		// }

// 		// Create if not exist
// 		if !s.HasExternalAccount(account.AccountNumber) {
// 			var createExternalBody model.ExternalAccountCreateBody
// 			createExternalBody.AccountNo = account.AccountNumber
// 			createExternalBody.BankCode = account.BankCode
// 			createExternalBody.DeviceId = *updateExBody.DeviceId
// 			createExternalBody.AccountName = account.AccountName
// 			// ไม่ได้ใช้ createExternalBody.Password = data.Password
// 			if updateExBody.Pin != nil {
// 				createExternalBody.Pin = *updateExBody.Pin
// 			} else {
// 				createExternalBody.Pin = account.PinCode
// 			}
// 			// ไม่ได้ใช้ createExternalBody.Username = data.Username
// 			// ไม่ได้ใช้ createExternalBody.WebhookNotifyUrl = os.Getenv("ACCOUNTING_LOCAL_WEBHOOK_ENDPOINT") + "/api/accounting/webhooks/noti"
// 			createExternalBody.WebhookUrl = os.Getenv("ACCOUNTING_LOCAL_WEBHOOK_ENDPOINT") + "/api/accounting/webhooks/action"
// 			if createResp, err := s.CreateExternalAccount(createExternalBody); err != nil {
// 				_, webhookErr := s.CreateWebhookLog("UpdateBankAccount.CreateExternalAccount, ERROR", helper.StructJson(struct {
// 					req model.BankAccountUpdateRequest
// 					err error
// 				}{req, err}))
// 				log.Print(webhookErr)
// 				return internalServerError(err)
// 			} else {
// 				// Update EncryptionPin
// 				updateBody.PinCode = &createResp.Pin
// 				updateBody.ExternalId = &createResp.Id
// 				var statusId int64
// 				if createResp.Enable {
// 					statusId = model.CONNECTION_CONNECTED
// 				} else {
// 					statusId = model.CONNECTION_DISCONNECTED
// 				}
// 				updateBody.ConnectionStatusId = &statusId
// 			}
// 		} else {
// 			updateExBody.AccountNo = &account.AccountNumber
// 			// ไม่ได้ใช้ updateExBody.WebhookNotifyUrl = os.Getenv("ACCOUNTING_LOCAL_WEBHOOK_ENDPOINT") + "/api/accounting/webhooks/noti"
// 			path := os.Getenv("ACCOUNTING_LOCAL_WEBHOOK_ENDPOINT") + "/api/accounting/webhooks/action"
// 			updateExBody.WebhookUrl = &path
// 			if externalCreateResp, err := s.UpdateExternalAccount(updateExBody); err != nil {
// 				_, webhookErr := s.CreateWebhookLog("UpdateBankAccount, ERROR", helper.StructJson(struct {
// 					id  int64
// 					req model.BankAccountUpdateRequest
// 					err error
// 				}{id, req, err}))
// 				log.Print(webhookErr)
// 				return internalServerError(err)
// 			} else {
// 				// Update EncryptionPin
// 				updateBody.PinCode = &externalCreateResp.Pin
// 				updateBody.ExternalId = &externalCreateResp.Id
// 				// เปิดใช้ได้ ถ้า design แก้
// 				// if *req.ConnectionStatus {
// 				// 	var enableReq model.ExternalAccountEnableRequest
// 				// 	if *req.ConnectionStatus {
// 				// 		enableReq.AccountNo = account.AccountNumber
// 				// 		enableReq.Enable = true

// 				// 	} else {
// 				// 		enableReq.AccountNo = account.AccountNumber
// 				// 		enableReq.Enable = false
// 				// 	}

// 				// 	if _, err := s.EnableExternalAccount(enableReq); err != nil {
// 				// 		_, webhookErr := s.CreateWebhookLog("UpdateBankAccount.EnableExternalAccount, ERROR", helper.StructJson(struct {
// 				// 			req model.ExternalAccountEnableRequest
// 				// 			err error
// 				// 		}{enableReq, err}))
// 				// 		log.Print(webhookErr)
// 				// 		return internalServerError(err)
// 				// 	}

// 				// }
// 			}

// 		}
// 	}

// 	// if onMainWithdrawChange {
// 	// 	if err := s.repo.ResetMainWithdrawBankAccount(); err != nil {
// 	// 		return internalServerError(err)
// 	// 	}
// 	// }
// 	updateBody.AccountPriorityWithdraw = &account.AccountPriorityWithdraw
// 	updateBody.AdminUpdatedAt = time.Now()
// 	if err := s.repo.UpdateBankAccount(account.Id, updateBody); err != nil {
// 		return internalServerError(err)
// 	}
// 	return nil
// }

func (s *accountingService) UpdateBankAccount(id int64, req model.BankAccountUpdateRequest) error {

	account, err := s.repo.GetBankAccountById(id)
	if err != nil {
		return internalServerError(err)
	}
	// พักเงิน ไม่สามารถมี device กับ pin มา
	if req.AccountTypeId != nil && *req.AccountTypeId == model.BANK_ACCOUNT_TYPE_HOLD && req.PinCode != nil && req.DeviceUid != nil || req.AccountTypeId != nil && *req.AccountTypeId == model.BANK_ACCOUNT_TYPE_HOLD && req.PinCode != nil || req.AccountTypeId != nil && *req.AccountTypeId == model.BANK_ACCOUNT_TYPE_HOLD && req.DeviceUid != nil {
		return badRequest("พักเงินไม่ให้ใส่ DeviceUid หรือ PinCode")
	}
	// ถัาเปลี่ยน บช จากพักเงิน เป็นอย่างอื่นต้องมี Pin กับ Device
	if req.AccountTypeId != nil && *req.AccountTypeId != model.BANK_ACCOUNT_TYPE_HOLD && account.AccountTypeId == model.BANK_ACCOUNT_TYPE_HOLD && req.PinCode == nil && req.DeviceUid == nil {
		return badRequest("พักเงินไม่ให้ใส่ DeviceUid หรือ PinCode")
	}

	// if req.AccountTypeId != nil && *req.AccountTypeId != model.BANK_ACCOUNT_TYPE_HOLD && req.PinCode == nil && req.DeviceUid == nil {
	// 	return badRequest("บช ตัองมี Device หรือ Pin")
	// }
	// ไม่สามารถ เปลี่ยนบชฝากถอนเป็นพักเงินได้
	if req.AccountTypeId != nil && *req.AccountTypeId == model.BANK_ACCOUNT_TYPE_HOLD && account.AccountTypeId != model.BANK_ACCOUNT_TYPE_HOLD {
		return badRequest("ไม่สามารถเปลี่ยนบชฝากถอนเป็นพักเงินได้")
	}

	var updateBody model.BankAccountUpdateBody
	var updateExBody model.ExternalAccountUpdateBody
	if req.AccountName != nil {
		updateBody.AccountName = req.AccountName
		updateExBody.AccountName = *req.AccountName
	} else {
		updateBody.AccountName = &account.AccountName
		updateExBody.AccountName = account.AccountName
	}

	if req.AccountTypeId != nil {
		accountType, err := s.repo.GetAccounTypeById(*req.AccountTypeId)
		if err != nil {
			log.Println(err)
			return badRequest("Invalid Account Type")
		}
		updateBody.AccountTypeId = &accountType.Id
	} else {
		updateBody.AccountTypeId = &account.AccountTypeId
	}

	if req.BankId != nil {
		bank, err := s.repo.GetBankById(*req.BankId)
		if err != nil {
			log.Println(err)
			if err.Error() == recordNotFound {
				return notFound(bankNotFound)
			}
			return badRequest("Invalid Bank")
		}
		if account.AccountTypeId == model.BANK_ACCOUNT_TYPE_HOLD {
			updateBody.BankId = req.BankId
			updateExBody.BankCode = bank.Code
		} else {
			updateBody.BankId = &account.BankId
			updateExBody.BankCode = account.BankCode
		}
	} else {
		updateBody.BankId = &account.BankId
		updateExBody.BankCode = account.BankCode
	}
	if req.AccountNumber != nil {
		acNo := helper.StripAllButNumbers(*req.AccountNumber)
		if acNo == "" {
			return badRequest("Invalid Account Number")
		}
		if account.AccountTypeId == model.BANK_ACCOUNT_TYPE_HOLD {
			updateBody.AccountNumber = req.AccountNumber
			updateExBody.AccountNo = *req.AccountNumber
		} else {
			updateBody.AccountNumber = &account.AccountNumber
			updateExBody.AccountNo = account.AccountNumber
		}

	} else {
		updateBody.AccountNumber = &account.AccountNumber
		updateExBody.AccountNo = account.AccountNumber
	}

	if req.DeviceUid != nil {
		updateBody.DeviceUid = req.DeviceUid
		updateExBody.DeviceId = *req.DeviceUid
	} else {
		updateBody.DeviceUid = &account.DeviceUid
		updateExBody.DeviceId = account.DeviceUid
	}

	if req.IsShowFront != nil {
		updateBody.IsShowFront = req.IsShowFront
	}

	if req.AutoWithdrawTypeId != nil {
		updateBody.AutoWithdrawTypeId = req.AutoWithdrawTypeId
	} else {
		updateBody.AutoWithdrawTypeId = &account.AutoWithdrawTypeId
	}

	// if req.BankWithdrawMaximum != nil {
	// 	updateBody.BankWithdrawMaximum = req.BankWithdrawMaximum
	// } else {
	// 	updateBody.BankWithdrawMaximum = &account.BankWithdrawMaximum
	// }

	// if req.AutoWithdrawMaximum != nil {
	// 	updateBody.AutoWithdrawMaximum = req.AutoWithdrawMaximum
	// } else {
	// 	updateBody.AutoWithdrawMaximum = &account.AutoWithdrawMaximum
	// }

	if req.SmsMode != nil && *req.SmsMode {
		if req.BankId != nil {
			// /accounting/bankaccounts/allow-sms-mode
			// P.layer ******** || *req.BankId == model.BANK_ID_SCB ต่างจากอันอื่น
			// 2024/11/15 P.layer เอากลับมา scb แล้วไปดักตอน web hook เข้า + ตอนแสดง list scb จาก smsmode ให้ เป็น api mode ปกติ
			if *req.BankId == model.BANK_ID_BBL || *req.BankId == model.BANK_ID_BAY || *req.BankId == model.BANK_ID_TTB || *req.BankId == model.BANK_ID_KKP || *req.BankId == model.BANK_ID_KBANK || *req.BankId == model.BANK_ID_KTB || *req.BankId == model.BANK_ID_SCB {
				updateBody.SmsMode = req.SmsMode
				updateExBody.SmsMode = *req.SmsMode
			}
			// P.layer ******** || account.BankId == model.BANK_ID_SCB scb can be different than other banks
			// 2024/11/15 P.layer เอากลับมา scb แล้วไปดักตอน web hook เข้า + ตอนแสดง list scb จาก smsmode ให้ เป็น api mode ปกติ
		} else if account.BankId == model.BANK_ID_BBL || account.BankId == model.BANK_ID_BAY || account.BankId == model.BANK_ID_TTB || account.BankId == model.BANK_ID_KKP || account.BankId == model.BANK_ID_KBANK || account.BankId == model.BANK_ID_KTB || account.BankId == model.BANK_ID_SCB {
			updateBody.SmsMode = req.SmsMode
			updateExBody.SmsMode = *req.SmsMode
		}
	} else {
		updateBody.SmsMode = &account.SmsMode
		updateExBody.SmsMode = account.SmsMode
	}

	// ถ้าเป็น sms mode ให้ถอนเท่านั้น P.mink ********
	if updateBody.SmsMode != nil && *updateBody.SmsMode {
		if updateBody.BankId != nil && *updateBody.BankId == model.BANK_ID_SCB || account.BankId == model.BANK_ID_SCB {
			updateBody.AccountTypeId = req.AccountTypeId
		} else {
			depositType := model.BANK_ACCOUNT_TYPE_DEPOSIT_ONLY
			updateBody.AccountTypeId = &depositType
		}
	} else {
		updateBody.AccountTypeId = req.AccountTypeId
	}

	// IsManualBank
	if req.IsManualBank != nil {
		updateBody.IsManualBank = req.IsManualBank
	} else {
		updateBody.IsManualBank = &account.IsManualBank
	}

	if updateBody.AutoWithdrawTypeId != nil && *updateBody.AutoWithdrawTypeId == model.BANK_ACCOUNT_TYPE_HOLD {
		setIsManualBank := true
		updateBody.IsManualBank = &setIsManualBank
		setSmsMode := false
		updateBody.SmsMode = &setSmsMode
		setAutoWithdrawTypeId := model.CONFIG_WEB_MANUAL_WITHDRAW
		updateBody.AutoWithdrawTypeId = &setAutoWithdrawTypeId
	}

	if req.ImageUrl != nil {
		updateBody.ImageUrl = req.ImageUrl
	} else {
		updateBody.ImageUrl = &account.ImageUrl
	}

	if req.ShowBankDepositOverDueTime != nil {
		updateBody.ShowBankDepositOverDueTime = req.ShowBankDepositOverDueTime
	} else {
		updateBody.ShowBankDepositOverDueTime = &account.ShowBankDepositOverDueTime
	}

	if req.ShowBankDepositMaxDueTime != nil {
		updateBody.ShowBankDepositMaxDueTime = req.ShowBankDepositMaxDueTime
	} else {
		updateBody.ShowBankDepositMaxDueTime = &account.ShowBankDepositMaxDueTime
	}

	// fmt.Println("*req.SmsMode", *req.SmsMode, "account.SmsMode", account.SmsMode, "updateBody.SmsMode", updateBody.SmsMode, "updateExBody.SmsMode", updateExBody.SmsMode)

	// Allow Update System Bank Account but with error
	var externalErr error
	if req.AccountTypeId != nil && *req.AccountTypeId != model.BANK_ACCOUNT_TYPE_HOLD || account.AccountTypeId != model.BANK_ACCOUNT_TYPE_HOLD || req.IsManualBank != nil && *req.IsManualBank == false {
		// ควร อยู่ && ใน if บนแยกก่อนเอาไว้เช็ค
		if req.PinCode != nil || req.SmsMode != nil && *req.SmsMode {
			updateBody.PinCode = req.PinCode
			if s.IsAllowCreateExternalAccount(account.AccountNumber) {
				// CREATE OR UPDATE EXISTING
				if !s.HasExternalAccount(account.AccountNumber) {
					var createExternalBody model.ExternalAccountCreateBody
					createExternalBody.AccountNo = updateExBody.AccountNo
					createExternalBody.BankCode = updateExBody.BankCode
					createExternalBody.DeviceId = updateExBody.DeviceId
					createExternalBody.AccountName = updateExBody.AccountName
					createExternalBody.Pin = updateBody.PinCode
					createExternalBody.WebhookUrl = os.Getenv("ACCOUNTING_LOCAL_WEBHOOK_ENDPOINT") + "/api/accounting/webhooks/action"
					createExternalBody.SmsMode = updateExBody.SmsMode
					if createResp, err := s.CreateExternalAccount(createExternalBody); err != nil {
						// logType string, jsonRequest string
						logType := "UpdateBankAccount.CreateExternalAccount, ERROR"
						jsonRequest := fmt.Sprintf("createExternalBody: %v", helper.StructJson(createExternalBody)) + fmt.Sprintf("createResp: %v", createResp) + fmt.Sprintf("err: %v", err)
						_, webhookErr := s.CreateWebhookLog(logType, jsonRequest)
						log.Print(webhookErr)
						return internalServerError(err)
					} else {
						// Update EncryptionPin
						updateBody.PinCode = &createResp.Pin
						updateBody.ExternalId = &createResp.Id
						var statusId int64
						if createResp.Enable {
							statusId = model.CONNECTION_CONNECTED
						} else {
							statusId = model.CONNECTION_DISCONNECTED
						}
						updateBody.ConnectionStatusId = &statusId
					}
				} else {
					path := os.Getenv("ACCOUNTING_LOCAL_WEBHOOK_ENDPOINT") + "/api/accounting/webhooks/action"
					updateExBody.WebhookUrl = path
					// updateExBody.Pin = *req.PinCode
					if req.PinCode != nil {
						updateExBody.Pin = *req.PinCode
					}
					if externalCreateResp, err := s.UpdateExternalAccount(updateExBody); err != nil {
						logType := "UpdateBankAccount.UpdateExternalAccount"
						jsonRequest := fmt.Sprintf("updateExBody: %v", helper.StructJson(updateExBody)) + fmt.Sprintf("externalCreateResp: %v", externalCreateResp) + fmt.Sprintf("err: %v", err)
						_, webhookErr := s.CreateWebhookLog(logType, jsonRequest)
						log.Print(webhookErr)
						externalErr = err
						statusId := model.CONNECTION_DISCONNECTED
						updateBody.ConnectionStatusId = &statusId
					} else {
						updateBody.PinCode = &externalCreateResp.Pin
						updateBody.ExternalId = &externalCreateResp.Id
						var statusId int64
						if externalCreateResp.Enable {
							statusId = model.CONNECTION_CONNECTED
						} else {
							statusId = model.CONNECTION_DISCONNECTED
						}
						updateBody.ConnectionStatusId = &statusId
					}

				}
			}
		}
	}

	updateBody.AccountPriorityWithdraw = &account.AccountPriorityWithdraw
	updateBody.AdminUpdatedAt = time.Now()
	if err := s.repo.UpdateBankAccount(account.Id, updateBody); err != nil {
		return internalServerError(err)
	}
	// อาจต้องทำ delete ถ้า update account number เพราะ fastbank update ด้วย account no

	// 2024/17/09 show to bank p.Layer and p.Mink confirm update show bank

	// if req.IsShowFront == nil {
	// 	always remove delete
	if err := s.repo.DeleteBankAccountShowBankByBankAccountId(account.Id); err != nil {
		return internalServerError(err)
	}
	// create
	if len(req.ShowToBank) > 0 {
		var showBankBody []model.CreateBankAccountShowBank
		for _, bankId := range req.ShowToBank {
			showBankBody = append(showBankBody, model.CreateBankAccountShowBank{
				BankAccountId: account.Id,
				BankId:        bankId,
			})
		}
		if err := s.repo.CreateBankAccountShowBank(showBankBody); err != nil {
			return internalServerError(err)
		}
	}
	// }

	// [ADMIN_ACTION] SUCCESS แก้ไขรายการธนาคาร KTB ชื่อบัญชี มนัส มานพ
	var adminActionCreateBody model.AdminActionCreateRequest
	adminActionCreateBody.AdminId = req.UpdateBy
	adminActionCreateBody.TypeId = model.ADMIN_ACTION_ACCOUNT_MANAGE
	adminActionCreateBody.Detail = fmt.Sprintf("แก้ไขรายการธนาคาร %s ชื่อบัญชี %s", account.BankName, account.AccountName)
	adminActionCreateBody.JsonInput = helper.StructJson(req)
	if _, err := s.adminAction.CreateSuccessAdminAction(adminActionCreateBody); err != nil {
		return err
	}

	if externalErr != nil {
		return externalErr
	}
	return nil
}

func (s *accountingService) UpdateBankAccountBalanceById(id int64) error {

	account, err := s.repo.GetBankAccountById(id)
	if err != nil {
		return internalServerError(err)
	}
	now := time.Now()
	if account.LastConnUpdateAt != nil {
		if now.Sub(*account.LastConnUpdateAt).Seconds() < 30 {
			return nil
		}
	}

	statusActiveFlag := model.CONNECTION_CONNECTED
	statusDisconnectedFlag := model.CONNECTION_DISCONNECTED
	var updateData model.BankAccountUpdateBody
	updateData.LastConnUpdateAt = &now
	updateData.ConnectionStatusId = &statusDisconnectedFlag

	if account.DeviceUid != "" && account.PinCode != "" {
		var query model.ExternalAccountStatusRequest
		query.AccountNumber = account.AccountNumber
		statusResp, err := s.GetExternalAccountStatus(query)
		if err != nil {
			return internalServerError(err)
		}

		if statusResp.Status == "online" {
			updateData.ConnectionStatusId = &statusActiveFlag
		} else {
			log.Println("statusResp", statusResp)
			updateData.ConnectionStatusId = &statusDisconnectedFlag
		}
		balaceResp, err := s.GetExternalAccountBalance(query)
		if err != nil {
			return internalServerError(err)
		}
		if balaceResp.AccountNo == account.AccountNumber {
			balance, _ := strconv.ParseFloat(strings.TrimSpace(balaceResp.AccountBalance), 64)
			updateData.AccountBalance = &balance
		} else {
			log.Println("ERROR, balaceResp: ", balaceResp)
			return internalServerError(err)
		}
	}

	if err := s.repo.UpdateBankAccount(id, updateData); err != nil {
		return internalServerError(err)
	}
	return nil
}

func (s *accountingService) UpdateAllBankAccountBotStatus() ([]model.UpdateAllBankAccountBotStatusResponse, error) {
	var query model.BankAccountListRequest
	query.Limit = 100
	query.Page = 0
	accounts, err := s.repo.GetBotBankAccounts(query)
	if err != nil {
		return nil, internalServerError(err)
	}
	now := time.Now()
	errorMinDelay := time.Now().Add(time.Minute * 5)
	statusActiveFlag := model.CONNECTION_CONNECTED
	statusDisconnectedFlag := model.CONNECTION_DISCONNECTED
	var botAccountListResponse []model.UpdateAllBankAccountBotStatusResponse
	for _, account := range accounts.List.([]model.BankAccountResponse) {
		var botAccountResponse model.UpdateAllBankAccountBotStatusResponse
		if account.LastConnUpdateAt != nil {
			if now.Sub(*account.LastConnUpdateAt).Seconds() < 30 {
				continue
			}
		}
		var data model.BankAccountUpdateBody
		data.LastConnUpdateAt = &now
		data.ConnectionStatusId = &statusDisconnectedFlag
		// data.AccountBalance = 0

		// FASTBANK
		var query model.ExternalAccountStatusRequest
		query.AccountNumber = account.AccountNumber
		statusResp, err := s.GetExternalAccountStatus(query)
		if err != nil {
			data.LastConnUpdateAt = &errorMinDelay
			// log.Println("ERROR", err.Error())
			offline := model.CONNECTION_DISCONNECTED
			data.ConnectionStatusId = &offline
			botAccountResponse.Enable = false
			botAccountResponse.Status = "offline"
			botAccountResponse.VerifyLogin = false
		}
		if statusResp != nil {
			botAccountResponse.Enable = statusResp.Enable
			botAccountResponse.Status = statusResp.Status
			botAccountResponse.VerifyLogin = statusResp.VerifyLogin

			if statusResp.Status == "online" {
				data.ConnectionStatusId = &statusActiveFlag
			} else {
				data.ConnectionStatusId = &statusDisconnectedFlag
			}
		} else {
			botAccountResponse.Enable = false
			botAccountResponse.Status = "offline"
			botAccountResponse.VerifyLogin = false
		}
		log.Println("statusResp", statusResp)
		balaceResp, err := s.GetExternalAccountBalance(query)
		if err == nil {
			if balaceResp != nil && balaceResp.AccountNo == account.AccountNumber {
				log.Println("balaceResp", helper.StructJson(balaceResp))
				log.Println("account", helper.StructJson(account))

				// Remove commas from the AccountBalance string
				accountBalanceStr := strings.Replace(balaceResp.AccountBalance, ",", "", -1)
				balance, err := strconv.ParseFloat(strings.TrimSpace(accountBalanceStr), 64)
				if err != nil {
					// Handle the error if the conversion fails
					log.Println("Error parsing account balance: ", err)
				} else {
					data.AccountBalance = &balance
				}
			} else {
				data.LastConnUpdateAt = &errorMinDelay
				// log.Println("ERROR, balaceResp: ", balaceResp)
			}
		}

		if err := s.repo.UpdateBankAccount(account.Id, data); err != nil {
			log.Println("ERROR, UPDATE ", err.Error())
		}

		botAccountResponse.Id = account.Id
		botAccountResponse.BankId = account.BankId
		botAccountResponse.BankCode = account.BankCode
		botAccountResponse.AccountName = account.AccountName
		botAccountResponse.AccountNumber = account.AccountNumber
		botAccountListResponse = append(botAccountListResponse, botAccountResponse)
	}

	return botAccountListResponse, nil
}

func (s *accountingService) DeleteBankAccount(req model.BankAccountDeleteRequest) error {

	account, err := s.repo.GetBankAccountById(req.AccountId)
	if err != nil {
		return internalServerError(err)
	}

	var updateBody model.BankAccountDeleteBody
	// Prevent UNIQUE account number reINSERT
	updateBody.AccountNumber = fmt.Sprintf("%s_del%d", account.AccountNumber, account.Id)
	updateBody.DeletedAt = time.Now()
	updateBody.ConnectionStatusId = model.CONNECTION_DISCONNECTED
	if err := s.repo.DeleteBankAccount(account.Id, updateBody); err != nil {
		return internalServerError(err)
	}

	var query model.ExternalAccountStatusRequest
	query.AccountNumber = account.AccountNumber
	if err := s.DeleteExternalAccount(query); err != nil {
		log.Println("DeleteExternalAccount", err)
	}

	// [ADMIN_ACTION] SUCCESS ลบรายการธนาคาร  SCB ชื่อบัญชี สมภา มาลา
	var adminActionCreateBody model.AdminActionCreateRequest
	adminActionCreateBody.AdminId = req.UpdateBy
	adminActionCreateBody.TypeId = model.ADMIN_ACTION_ACCOUNT_MANAGE
	adminActionCreateBody.Detail = fmt.Sprintf("ลบรายการธนาคาร %s ชื่อบัญชี %s", account.BankName, account.AccountName)
	adminActionCreateBody.JsonInput = helper.StructJson(req)
	if _, err := s.adminAction.CreateSuccessAdminAction(adminActionCreateBody); err != nil {
		return err
	}

	return nil
}

func (s *accountingService) GetTransactionById(req model.BankGetByIdRequest) (*model.BankAccountTransaction, error) {

	record, err := s.repo.GetTransactionById(req.Id)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(transactionNotFound)
		}
		return nil, internalServerError(err)
	}
	return record, nil
}

func (s *accountingService) GetTransactions(req model.BankAccountTransactionListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}
	list, err := s.repo.GetTransactions(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	return list, nil
}

func (s *accountingService) CreateTransaction(body model.BankAccountTransactionBody, adminId int64) error {

	account, err := s.repo.GetBankAccountById(body.AccountId)
	if err != nil {
		log.Println(err)
		return badRequest("Invalid Bank Account")
	}

	var transaction model.BankAccountTransactionBody
	transaction.AccountId = account.Id
	transaction.Description = body.Description
	transaction.TransactionTypeId = body.TransactionTypeId
	transaction.Amount = body.Amount
	transaction.TransferAt = body.TransferAt
	transaction.CreatedByUsername = body.CreatedByUsername

	if err := s.repo.CreateTransaction(transaction); err != nil {
		return internalServerError(err)
	}

	// [ADMIN_ACTION] SUCCESS สร้างรายการบันทึกธุรกรรมเงินสด
	var adminActionCreateBody model.AdminActionCreateRequest
	adminActionCreateBody.AdminId = adminId
	adminActionCreateBody.TypeId = model.ADMIN_ACTION_ACCOUNT_MANAGE
	adminActionCreateBody.Detail = fmt.Sprintf("สร้างรายการบันทึกธุรกรรมเงินสด บัญชี %s", account.AccountName)
	adminActionCreateBody.JsonInput = helper.StructJson(body)
	if _, err := s.adminAction.CreateSuccessAdminAction(adminActionCreateBody); err != nil {
		return err
	}

	return nil
}

func (s *accountingService) UpdateTransaction(id int64, body model.BankAccountTransactionBody) error {

	_, err := s.repo.GetTransactionById(id)
	if err != nil {
		return internalServerError(err)
	}

	// no Update
	return notFound("Function not found")
}

func (s *accountingService) DeleteTransaction(id int64) error {

	_, err := s.repo.GetTransactionById(id)
	if err != nil {
		return internalServerError(err)
	}

	if err := s.repo.DeleteTransaction(id); err != nil {
		return internalServerError(err)
	}
	return nil
}

func (s *accountingService) GetTransferById(req model.BankGetByIdRequest) (*model.BankAccountTransfer, error) {

	record, err := s.repo.GetTransferById(req.Id)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(transferNotFound)
		}
		return nil, internalServerError(err)
	}
	return record, nil
}

func (s *accountingService) GetTransfers(req model.BankAccountTransferListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}
	list, err := s.repo.GetTransfers(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	return list, nil
}

func (s *accountingService) CreateTransfer(body model.BankAccountTransferBody) error {

	fromAccount, err := s.repo.GetBankAccountById(body.FromAccountId)
	if err != nil {
		log.Println(err)
		return badRequest("Invalid source Bank Account")
	}

	toAccount, err := s.repo.GetBankAccountById(body.ToAccountId)
	if err != nil {
		log.Println(err)
		return badRequest("Invalid destination Bank Account")
	}

	var createBody model.BankAccountTransferBody
	createBody.FromAccountId = fromAccount.Id
	createBody.FromBankId = fromAccount.BankId
	createBody.FromAccountName = fromAccount.AccountName
	createBody.FromAccountNumber = fromAccount.AccountNumber
	createBody.ToAccountId = toAccount.Id
	createBody.ToBankId = toAccount.BankId
	createBody.ToAccountName = toAccount.AccountName
	createBody.ToAccountNumber = toAccount.AccountNumber
	createBody.Amount = body.Amount
	createBody.TransferAt = body.TransferAt
	createBody.CreatedByUsername = body.CreatedByUsername
	createBody.StatementStatusId = model.STATEMENT_STATUS_PENDING
	if err := s.repo.CreateTransfer(createBody); err != nil {
		return internalServerError(err)
	}
	return nil
}

func (s *accountingService) ConfirmTransfer(id int64, actorId int64) error {

	transfer, err := s.repo.GetTransferById(id)
	if err != nil {
		return internalServerError(err)
	}

	if transfer.Status == "pending" {
		var body model.BankAccountTransferConfirmBody
		body.StatementStatusId = model.STATEMENT_STATUS_CONFIRMED
		body.ConfirmedAt = time.Now()
		body.ConfirmedByAdminId = &actorId
		if err := s.repo.ConfirmTransfer(id, body); err != nil {
			return internalServerError(err)
		}
	} else {
		return badRequest("Transfer not in pending status")
	}
	return nil
}

func (s *accountingService) DeleteTransfer(id int64) error {

	_, err := s.repo.GetTransferById(id)
	if err != nil {
		return internalServerError(err)
	}

	if err := s.repo.DeleteTransfer(id); err != nil {
		return internalServerError(err)
	}
	return nil
}

func (s *accountingService) GetAccountStatements(req model.BankAccountStatementListRequest) (*model.SuccessWithPagination, error) {

	systemAccount, err := s.repo.GetBankAccountById(req.AccountId)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(bankAccountNotFound)
		}
		return nil, internalServerError(err)
	}

	var query model.ExternalStatementListRequest
	query.AccountNumber = systemAccount.AccountNumber
	query.Page = req.Page
	query.Limit = req.Limit
	records, err := s.GetExternalAccountStatements(query)
	if err != nil {
		return &model.SuccessWithPagination{}, nil
		// onError return Empty | return nil, internalServerError(err)
	}

	var externalIds []int64
	for _, record := range records.List.([]model.ExternalStatement) {
		externalIds = append(externalIds, record.Id)
	}

	dbStatements, err := s.repo.GetBankExternalStatements(externalIds)
	if err != nil {
		return records, nil
		// onError return Empty | return nil, internalServerError(err)
	}

	for i, record := range records.List.([]model.ExternalStatement) {
		var exist *model.BankStatementResponse = nil
		for _, dbRecord := range dbStatements.List.([]model.BankStatementResponse) {
			if record.Id == dbRecord.ExternalId {
				exist = &dbRecord
				break
			}
		}
		tempRecord := records.List.([]model.ExternalStatement)[i]
		tempRecord.AccountDetail = fmt.Sprintf("%s: %s", systemAccount.BankName, systemAccount.AccountName)
		// "createdDate": "2023-05-08 11:17:05",
		// "dateTime": "2023-05-08 11:15:37",
		// tempRecord.DateTime = strings.Replace(tempRecord.DateTime, " ", "T", 1) + "Z"
		// "updatedDate": "2023-05-08 11:17:05"
		if record.TxnCode == "X1" || record.TxnCode == "CR" {
			tempRecord.StatementTypeId = model.STATEMENT_TYPE_TRANSFER_IN
			tempRecord.Amount = record.Amount
		} else if record.TxnCode == "X2" || record.TxnCode == "DR" || record.TxnCode == "C2" {
			tempRecord.StatementTypeId = model.STATEMENT_TYPE_TRANSFER_OUT
			tempRecord.Amount = record.Amount * -1
		}
		if exist != nil {
			tempRecord.StatementStatusId = exist.StatementStatusId
		} else {
			tempRecord.StatementStatusId = 0
		}
		records.List.([]model.ExternalStatement)[i] = tempRecord
	}
	return records, nil
}

func (s accountingService) GetExternalStatementWithTransactionList(req model.ExternalStatementWithTransactionListRequest) ([]model.ExternalStatementWithTransactionReponse, int64, error) {

	var result []model.ExternalStatementWithTransactionReponse
	var total int64 = 0

	// ** แก้ GetExternalUnreadStatementWithTransactionList ด้วย ** //

	systemAccount, err := s.repo.GetBankAccountById(req.AccountId)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, 0, notFound(bankAccountNotFound)
		}
		return nil, 0, internalServerError(err)
	}

	// txtCode {X1=ผาก scb , X2=ถอน scb, CR= ฝาก kbank, DR= ถอน kbank, IORSDT= ฝาก ktb, NBSWT= ถอน ktb, all=ทั้งหมด}
	// (1, 'กสิกรไทย', 'kbank', 'https://storage.googleapis.com/cbgame/banks/kbank.png', '********', '2023-09-18 12:00:11'),
	// (2, 'ไทยพาณิชย์', 'scb', 'https://storage.googleapis.com/cbgame/banks/scb.png', '********', '2023-09-18 12:00:11'),
	// (5, 'กรุงไทย', 'ktb', 'https://storage.googleapis.com/cbgame/banks/ktb.png', '********', '2023-09-18 12:00:11'),
	// (4, 'กรุงศรี', 'bay', 'https://storage.googleapis.com/cbgame/banks/bay.png', '********', '2023-09-18 12:00:11'),
	// if data.TxnCode == "X1" || data.TxnCode == "CR" || data.TxnCode == "IORSDT" || data.TxnCode == "NBSDT" {
	// if data.TxnCode == "X2" || data.TxnCode == "DR" || data.TxnCode == "NBSWT" || data.TxnCode == "IORSWT" {
	txnCode := ""
	if req.DirectionId != nil {
		bankCodeList := map[int64]map[int64]string{
			1: {1: "CR", 2: "DR"},        // KBANK
			2: {1: "X1", 2: "X2"},        // SCB
			5: {1: "IORSDT", 2: "NBSWT"}, // KTB
			4: {1: "NBSDT", 2: "IORSWT"}, // BAY
		}
		if val, ok := bankCodeList[systemAccount.BankId]; ok {
			if val2, ok2 := val[*req.DirectionId]; ok2 {
				txnCode = val2
			}
		}
	}

	var query model.ExternalStatementListRequest
	query.AccountNumber = systemAccount.AccountNumber
	query.OfDate = req.OfDate
	if txnCode != "" {
		query.TxnCode = txnCode
	}
	query.Page = req.Page
	query.Limit = req.Limit
	records, err := s.GetExternalAccountStatements(query)
	if err != nil {
		return nil, 0, nil
	}

	var externalIds []int64
	for _, record := range records.List.([]model.ExternalStatement) {
		externalIds = append(externalIds, record.Id)
	}

	dbStatements, err := s.repo.GetBankExternalStatements(externalIds)
	if err != nil {
		return nil, 0, internalServerError(err)
	}

	var statementTransactionMap = make(map[int64]model.BankTransactionStatementResponse)
	var statementIds []int64
	for _, dbRecord := range dbStatements.List.([]model.BankStatementResponse) {
		statementIds = append(statementIds, dbRecord.Id)
	}
	if len(statementIds) > 0 {
		dbTransaction, total, err := s.repo.GetBankTransactionWithStatementList(statementIds)
		if err != nil {
			return nil, 0, internalServerError(err)
		}
		if total > 0 {
			for _, record := range dbTransaction {
				statementTransactionMap[record.ExternalStatementId] = record
			}
		}
	}

	for _, record := range records.List.([]model.ExternalStatement) {

		var tempRow model.ExternalStatementWithTransactionReponse
		tempRow.Id = record.Id
		// tempRow.CreatedDate = record.CreatedDate
		tempRow.TxnCode = record.TxnCode
		tempRow.TxnDescription = record.TxnDescription
		tempRow.Amount = record.Amount
		tempRow.Info = record.Info
		// tempRow.AccountDetail = fmt.Sprintf("%s: %s", systemAccount.BankName, systemAccount.AccountName)
		tempBankTransferAt, err := time.Parse("2006-01-02 15:04:05", record.DateTime)

		tempBankTransferAt = tempBankTransferAt.Add(time.Hour * -7).UTC()
		if err == nil {
			tempRow.DateTime = tempBankTransferAt // BankTime
		} else {
			log.Println("GetExternalStatementWithTransactionList.", err)
		}

		tempRow.TxnCode = strings.ToUpper(tempRow.TxnCode)

		if tempRow.TxnCode == "X1" || tempRow.TxnCode == "CR" || tempRow.TxnCode == "IORSDT" || tempRow.TxnCode == "NBSDT" {
			tempRow.StatementTypeId = model.STATEMENT_TYPE_TRANSFER_IN
			if val, ok := statementTransactionMap[record.Id]; ok {
				tempRow.TransactionAt = &val.CreatedAt
				tempRow.WorkSeconds = int64(math.Abs(tempBankTransferAt.Sub(*tempRow.TransactionAt).Seconds()))
				// เพิ่มคอลัมน์ ระยะเวลาการดึง และแถบสี
				// สีเขียว (น้อยกว่าหรือเท่ากับ 1 นาที)
				// สีเหลือง (มากกว่า 1 นาที แต่ไม่เกิน 3 นาที)
				// สีแดง (มากกว่า 3 นาที)
				if tempRow.WorkSeconds < 60 {
					tempRow.WorkStatusCode = "GREEN"
				} else if tempRow.WorkSeconds <= 180 {
					tempRow.WorkStatusCode = "YELLOW"
				} else {
					tempRow.WorkStatusCode = "RED"
				}
				// มีสถานะเฉพาะ ฝาก ทั้งหมด ตรวจยอดแล้ว รอเช็คสถานะ
				if val.TransactionTypeId == model.TRANSACTION_TYPE_DEPOSIT {
					if val.TransactionStatusId == model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED {
						tempRow.StatusName = "ตรวจยอดแล้ว"
					} else {
						tempRow.StatusName = "รอเช็คสถานะ"
					}
				}
			} else {
				tempRow.StatementStatusId = 1 // ให้กดใหม่ ถ้ายังไม่มี Transaction
			}
		} else if tempRow.TxnCode == "X2" || tempRow.TxnCode == "DR" || tempRow.TxnCode == "NBSWT" || tempRow.TxnCode == "IORSWT" {
			tempRow.StatementTypeId = model.STATEMENT_TYPE_TRANSFER_OUT
			tempRow.Amount = -1 * record.Amount
		}
		result = append(result, tempRow)
	}

	total = records.Total

	return result, total, nil
}

func (s accountingService) GetExternalUnreadStatementWithTransactionList(req model.ExternalStatementWithTransactionListRequest) ([]model.ExternalStatementWithTransactionReponse, int64, error) {

	var result []model.ExternalStatementWithTransactionReponse
	var total int64 = 0

	// ** แก้ GetExternalStatementWithTransactionList ด้วย ** //

	systemAccount, err := s.repo.GetBankAccountById(req.AccountId)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, 0, notFound(bankAccountNotFound)
		}
		return nil, 0, internalServerError(err)
	}

	// txtCode {X1=ผาก scb , X2=ถอน scb, CR= ฝาก kbank, DR= ถอน kbank, IORSDT= ฝาก ktb, NBSWT= ถอน ktb, all=ทั้งหมด}
	// (1, 'กสิกรไทย', 'kbank', 'https://storage.googleapis.com/cbgame/banks/kbank.png', '********', '2023-09-18 12:00:11'),
	// (2, 'ไทยพาณิชย์', 'scb', 'https://storage.googleapis.com/cbgame/banks/scb.png', '********', '2023-09-18 12:00:11'),
	// (5, 'กรุงไทย', 'ktb', 'https://storage.googleapis.com/cbgame/banks/ktb.png', '********', '2023-09-18 12:00:11'),
	// (4, 'กรุงศรี', 'bay', 'https://storage.googleapis.com/cbgame/banks/bay.png', '********', '2023-09-18 12:00:11'),
	// if data.TxnCode == "X1" || data.TxnCode == "CR" || data.TxnCode == "IORSDT" || data.TxnCode == "NBSDT" {
	// if data.TxnCode == "X2" || data.TxnCode == "DR" || data.TxnCode == "NBSWT" || data.TxnCode == "IORSWT" {
	txnCode := ""
	if req.DirectionId != nil {
		bankCodeList := map[int64]map[int64]string{
			1: {1: "CR", 2: "DR"},        // KBANK
			2: {1: "X1", 2: "X2"},        // SCB
			5: {1: "IORSDT", 2: "NBSWT"}, // KTB
			4: {1: "NBSDT", 2: "IORSWT"}, // BAY
		}
		if val, ok := bankCodeList[systemAccount.BankId]; ok {
			if val2, ok2 := val[*req.DirectionId]; ok2 {
				txnCode = val2
			}
		}
	}

	var query model.ExternalStatementListRequest
	query.AccountNumber = systemAccount.AccountNumber
	query.OfDate = req.OfDate
	if txnCode != "" {
		query.TxnCode = txnCode
	}
	query.Page = req.Page
	query.Limit = req.Limit
	records, err := s.GetExternalUnreadAccountStatements(query)
	if err != nil {
		return nil, 0, nil
	}

	var externalIds []int64
	for _, record := range records.List.([]model.ExternalStatement) {
		externalIds = append(externalIds, record.Id)
	}

	dbStatements, err := s.repo.GetBankExternalStatements(externalIds)
	if err != nil {
		return nil, 0, internalServerError(err)
	}

	var statementTransactionMap = make(map[int64]model.BankTransactionStatementResponse)
	var statementIds []int64
	for _, dbRecord := range dbStatements.List.([]model.BankStatementResponse) {
		statementIds = append(statementIds, dbRecord.Id)
	}
	if len(statementIds) > 0 {
		dbTransaction, total, err := s.repo.GetBankTransactionWithStatementList(statementIds)
		if err != nil {
			return nil, 0, internalServerError(err)
		}
		if total > 0 {
			for _, record := range dbTransaction {
				statementTransactionMap[record.ExternalStatementId] = record
			}
		}
	}

	for _, record := range records.List.([]model.ExternalStatement) {

		var tempRow model.ExternalStatementWithTransactionReponse
		tempRow.Id = record.Id
		// tempRow.CreatedDate = record.CreatedDate
		tempRow.TxnCode = record.TxnCode
		tempRow.TxnDescription = record.TxnDescription
		tempRow.Amount = record.Amount
		tempRow.Info = record.Info
		// tempRow.AccountDetail = fmt.Sprintf("%s: %s", systemAccount.BankName, systemAccount.AccountName)
		tempBankTransferAt, err := time.Parse("2006-01-02 15:04:05", record.DateTime)
		tempBankTransferAt = tempBankTransferAt.Add(time.Hour * -7).UTC()
		if err == nil {
			if systemAccount.BankId == model.BANK_ID_SCB && (tempBankTransferAt.Add(7*time.Hour).Hour() == 23) {
				// [********] scb will show -1 day on hour 23
				tempRow.DateTime = tempBankTransferAt.AddDate(0, 0, -1)
			} else {
				tempRow.DateTime = tempBankTransferAt // BankTime
			}
		} else {
			log.Println("GetExternalStatementWithTransactionList.", err)
		}

		tempRow.TxnCode = strings.ToUpper(tempRow.TxnCode)

		if tempRow.TxnCode == "X1" || tempRow.TxnCode == "CR" || tempRow.TxnCode == "IORSDT" || tempRow.TxnCode == "NBSDT" {
			tempRow.StatementTypeId = model.STATEMENT_TYPE_TRANSFER_IN
			if val, ok := statementTransactionMap[record.Id]; ok {
				tempRow.TransactionAt = &val.CreatedAt
				tempRow.WorkSeconds = int64(math.Abs(tempBankTransferAt.Sub(*tempRow.TransactionAt).Seconds()))
				// เพิ่มคอลัมน์ ระยะเวลาการดึง และแถบสี
				// สีเขียว (น้อยกว่าหรือเท่ากับ 1 นาที)
				// สีเหลือง (มากกว่า 1 นาที แต่ไม่เกิน 3 นาที)
				// สีแดง (มากกว่า 3 นาที)
				if tempRow.WorkSeconds < 60 {
					tempRow.WorkStatusCode = "GREEN"
				} else if tempRow.WorkSeconds <= 180 {
					tempRow.WorkStatusCode = "YELLOW"
				} else {
					tempRow.WorkStatusCode = "RED"
				}
				// มีสถานะเฉพาะ ฝาก ทั้งหมด ตรวจยอดแล้ว รอเช็คสถานะ
				if val.TransactionTypeId == model.TRANSACTION_TYPE_DEPOSIT {
					if val.TransactionStatusId == model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED {
						tempRow.StatusName = "ตรวจยอดแล้ว"
					} else {
						tempRow.StatusName = "รอเช็คสถานะ"
					}
				}
			} else {
				tempRow.StatementStatusId = 1 // ให้กดใหม่ ถ้ายังไม่มี Transaction
			}
		} else if tempRow.TxnCode == "X2" || tempRow.TxnCode == "DR" || tempRow.TxnCode == "NBSWT" || tempRow.TxnCode == "IORSWT" {
			tempRow.StatementTypeId = model.STATEMENT_TYPE_TRANSFER_OUT
			tempRow.Amount = -1 * record.Amount
		}
		result = append(result, tempRow)
	}

	total = records.Total

	return result, total, nil
}

func (s *accountingService) GetAccountStatementById(req model.BankGetByIdRequest) (*model.BankStatement, error) {

	record, err := s.repo.GetBankStatementById(req.Id)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(err.Error())
		}
		return nil, internalServerError(err)
	}
	return record, nil
}

func (s *accountingService) AddAccountStatementToWebhook(req model.RecheckWebhookRequest) error {

	// [********] UNUSED AddAccountStatementToWebhook(req model.RecheckWebhookRequest) error

	debugReq := map[string]interface{}{
		"req": req,
	}

	systemAccount, err := s.repo.GetBankAccountById(req.AccountId)
	if err != nil {
		if err.Error() == recordNotFound {
			return notFound(bankAccountNotFound)
		}
		return internalServerError(err)
	}

	statement, statementErr := s.repo.GetBankStatementByExternalId(req.ExternalId)

	// INIT LOG
	if err := s.repo.SuccessLog("AddAccountStatementToWebhook.REQUEST", debugReq, statement); err != nil {
		log.Println("AddAccountStatementToWebhook.SuccessLog.ERROR", err)
	}

	// ตรวจสอบว่ามี statement นี้อยู่แล้วหรือไม่
	if statementErr != nil {
		if statementErr.Error() == recordNotFound {
			// ยังไม่มีใน bank_statement
			log.Println("AddOldStatementToBankStatement, externalId: ", req.ExternalId)
			debugReq["case"] = "AddOldStatementToBankStatement"
			var query model.ExternalStatementListRequest
			query.AccountNumber = systemAccount.AccountNumber
			query.OfDateTime = req.OfDateTime
			query.Page = 1
			query.Limit = 100
			records, err := s.GetExternalAccountStatements(query)
			if err != nil {
				return internalServerError(err)
			}
			// ยิงของรายการวันนี้มาทั้งหมด
			for _, record := range records.List.([]model.ExternalStatement) {
				if record.Id == req.ExternalId {
					err := s.createBankStatementFromExternalStatement(record)
					if err == nil {
						continue
					}
				}
			}
		} else {
			log.Panicln("ERROR, AddAccountStatementToWebhook.GetBankStatementByExternalId", statementErr.Error())
			debugReq["case"] = "AddOldStatementToBankStatement"
		}
	} else {
		// มีใน bank_statement แล้ว
		// ตรวจสอบว่ามี bank_transaction นี้อยู่แล้วหรือไม่
		if _, err := s.repo.GetTransactionByStatementId(statement.Id); err != nil {
			if err.Error() == recordNotFound {
				log.Println("AddOldStatementToBankTransaction, statementId: ", statement.Id)
				debugReq["case"] = "AddNewStatementToBankStatement"

				var bodyCreateState model.BankStatementCreateBody
				bodyCreateState.Id = statement.Id
				bodyCreateState.AccountId = systemAccount.Id
				bodyCreateState.ExternalId = &req.ExternalId
				bodyCreateState.StatementTypeId = statement.StatementTypeId
				bodyCreateState.Amount = statement.Amount
				bodyCreateState.FromBankId = statement.FromBankId
				bodyCreateState.FromAccountNumber = statement.FromAccountNumber
				bodyCreateState.Detail = statement.Detail
				bodyCreateState.TransferAt = statement.TransferAt
				bodyCreateState.StatementStatusId = model.STATEMENT_STATUS_PENDING

				if err := s.matchBankStatementFromExternalStatement(*systemAccount, bodyCreateState); err != nil {
					log.Println("ERROR, AddAccountStatementToWebhook.matchBankStatementFromExternalStatement", err.Error())
					// ERROR
					if err := s.repo.ErrorLog("AddAccountStatementToWebhook.RESULT", debugReq, err); err != nil {
						log.Println("AddAccountStatementToWebhook.ErrorLog.ERROR", err)
					}
					return internalServerError(err)
				}
			} else {
				log.Panicln("ERROR, AddAccountStatementToWebhook.GetBankTransactionByStatementId", err.Error())
				debugReq["case"] = "ERROR, AddAccountStatementToWebhook.GetBankTransactionByStatementId"
			}
		} else {
			log.Println("TRANSACTION_ALREADY_EXIST")
			debugReq["case"] = "TRANSACTION_ALREADY_EXIST"
		}
	}

	// SUCCESS
	if err := s.repo.SuccessLog("AddAccountStatementToWebhook.RESULT", debugReq, statement); err != nil {
		log.Println("AddAccountStatementToWebhook.SuccessLog.ERROR", err)
	}
	return nil
}

func (s *accountingService) GetExternalSettings() (*model.ExternalSettings, error) {

	var body model.ExternalSettings
	body.ApiEndpoint = os.Getenv("ACCOUNTING_API_ENDPOINT")
	body.ApiKey = os.Getenv("ACCOUNTING_API_KEY")
	body.LocalWebhookEndpoint = os.Getenv("ACCOUNTING_LOCAL_WEBHOOK_ENDPOINT")

	return &body, nil
}

func (s *accountingService) HasExternalAccount(accountNumber string) bool {

	data, err := s.GetExternalAccounts()
	if err != nil {
		return true
	}
	for _, account := range data.List.([]model.ExternalAccount) {
		if account.AccountNo == accountNumber {
			return true
		}
	}
	return false
}

func (s *accountingService) GetExternalAccountConfig(key string) (*model.BotAccountConfig, error) {

	var query model.BotAccountConfigListRequest
	query.SearchKey = &key

	data, err := s.repo.GetBotaccountConfigs(query)
	if err != nil {
		return nil, internalServerError(err)
	}

	for _, record := range data.List.([]model.BotAccountConfig) {
		if record.ConfigKey == key {
			return &record, nil
		}
	}
	return nil, notFound("Config not found")
}

func (s *accountingService) IsAllowCreateExternalAccount(accountNumber string) bool {
	allowCreateExternalAccount := false
	config, _ := s.GetExternalAccountConfig("allow_create_external_account")
	if config != nil {
		if config.ConfigVal == "list" {
			accountConfig, errConfig := s.HasExternalAccountConfig("allow_external_account_number", accountNumber)
			if errConfig != nil {
				return false
			}
			if accountConfig.ConfigVal == accountNumber {
				allowCreateExternalAccount = true
			}
		} else if config.ConfigVal == "all" {
			allowCreateExternalAccount = true
		}
	}
	return allowCreateExternalAccount
}

func (s *accountingService) HasExternalAccountConfig(key string, value string) (*model.BotAccountConfig, error) {

	var query model.BotAccountConfigListRequest
	query.SearchKey = &key
	query.SearchValue = &value

	data, err := s.repo.GetBotaccountConfigs(query)
	if err != nil {
		return nil, internalServerError(err)
	}

	for _, record := range data.List.([]model.BotAccountConfig) {
		if record.ConfigKey == key {
			return &record, nil
		}
	}
	return nil, notFound("Config not found")
}

func (s *accountingService) GetCustomerAccountsInfo(req model.CustomerAccountInfoRequest) (*model.CustomerAccountInfo, error) {

	botAccount, err := s.repo.GetActiveExternalAccount()
	if err != nil {
		return nil, internalServerError(err)
	}
	req.AccountFrom = botAccount.AccountNumber
	// b, err := json.Marshal(req)
	// if err != nil {
	// 	log.Println(err)
	// 	return nil, internalServerError(errors.New("Error from JSON"))
	// }
	// log.Println(string(b))

	client := &http.Client{}
	// curl -X POST "https://api.fastbankapi.com/api/v2/statement/verifyTransfer" -H "accept: */*" -H "apiKey: aa.bb" -H "Content-Type: application/json" -d "{ \"accountFrom\": \"cccc\", \"accountTo\": \"dddd\", \"bankCode\": \"bay\"}"
	data, _ := json.Marshal(req)
	reqExternal, _ := http.NewRequest("POST", os.Getenv("ACCOUNTING_API_ENDPOINT")+"/api/v2/statement/verifyTransfer", bytes.NewBuffer(data))
	reqExternal.Header.Set("apiKey", os.Getenv("ACCOUNTING_API_KEY"))
	reqExternal.Header.Set("Content-Type", "application/json")
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Print(err.Error())
		return nil, internalServerError(errors.New("Error from client Do"))
	}

	if response.StatusCode != 200 {
		log.Println(response)
		return nil, internalServerError(errors.New("Error from external API"))
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
	}
	var result model.CustomerAccountInfoReponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		return nil, internalServerError(errors.New("Error from JSON response"))
	}
	return &result.Data, nil
}

func (s *accountingService) GetExternalAccounts() (*model.SuccessWithPagination, error) {

	client := &http.Client{}
	reqExternal, _ := http.NewRequest("GET", os.Getenv("ACCOUNTING_API_ENDPOINT")+"/api/v2/site/bankAccount", nil)
	reqExternal.Header.Set("apiKey", os.Getenv("ACCOUNTING_API_KEY"))
	response, err := client.Do(reqExternal)

	if err != nil {
		log.Print(err.Error())
		return nil, internalServerError(errors.New("Error from external API"))
	}

	if response.StatusCode != 200 {
		log.Println(response)
		return nil, internalServerError(errors.New("Error from external API"))
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
	}
	var list []model.ExternalAccount
	errJson := json.Unmarshal(responseData, &list)
	if errJson != nil {
		return nil, internalServerError(errors.New("Error from JSON response"))
	}

	// End count total records for pagination purposes (without limit and offset) //
	var result model.SuccessWithPagination
	result.List = list
	result.Total = int64(len(list))
	return &result, nil
}

func (s *accountingService) GetExternalAccountBalance(query model.ExternalAccountStatusRequest) (*model.ExternalAccountBalance, error) {

	client := &http.Client{}
	// curl -X GET "https://api.fastbankapi.com/api/v2/statement/balance?accountNo=hhhhhh" -H "accept: */*" -H "apiKey: aaaaaa.bbbbbb"
	reqExternal, _ := http.NewRequest("GET", os.Getenv("ACCOUNTING_API_ENDPOINT")+"/api/v2/statement/balance?accountNo="+query.AccountNumber, nil)
	reqExternal.Header.Set("apiKey", os.Getenv("ACCOUNTING_API_KEY"))
	response, err := client.Do(reqExternal)

	if err != nil {
		log.Print(err.Error())
		return nil, internalServerError(errors.New("Error from external API"))
	}

	if response.StatusCode != 200 {
		log.Println(response)
		return nil, internalServerError(errors.New("Error from external API"))
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
	}
	var result model.ExternalAccountBalance
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		return nil, internalServerError(errors.New("Error from JSON response"))
	}
	if result.AccountNo != query.AccountNumber {
		if _, err := s.CreateWebhookLog("GetExternalAccountBalance, ERROR", string(responseData)); err != nil {
			log.Print(err)
		}
		return nil, notFound("Bank account not found")
	}
	return &result, nil
}

func (s *accountingService) GetExternalAccountStatus(query model.ExternalAccountStatusRequest) (*model.GetExternalAccountStatus, error) {

	client := &http.Client{}
	reqExternal, _ := http.NewRequest("GET", os.Getenv("ACCOUNTING_API_ENDPOINT")+"/api/v2/site/bank-status?accountNo="+query.AccountNumber, nil)
	reqExternal.Header.Set("apiKey", os.Getenv("ACCOUNTING_API_KEY"))
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Print(err.Error())
		return nil, internalServerError(errors.New("Error from external API"))
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Print(err.Error())
		return nil, internalServerError(errors.New("Error from external API"))
	}

	if response.StatusCode != 200 {
		if _, err := s.CreateWebhookLog("GetExternalAccountStatus, ERROR", helper.StructJson(struct {
			query        model.ExternalAccountStatusRequest
			responseJson string
		}{query, string(responseData)})); err != nil {
			log.Print(err)
		}
		return nil, notFound("External account not found")
	}

	var result model.GetExternalAccountStatus
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		return nil, internalServerError(errors.New("Error from JSON response"))
	}
	return &result, nil
}

func (s *accountingService) CreateExternalAccount(body model.ExternalAccountCreateBody) (*model.ExternalAccountCreateResponse, error) {

	log.Println("CreateExternalAccount, body:", helper.StructJson(body))

	client := &http.Client{}
	data, _ := json.Marshal(body)
	path := os.Getenv("ACCOUNTING_API_ENDPOINT") + "/api/v2/site/bankAccount"

	reqExternal, _ := http.NewRequest("POST", path, bytes.NewBuffer(data))
	reqExternal.Header.Set("apiKey", os.Getenv("ACCOUNTING_API_KEY"))
	reqExternal.Header.Set("Content-Type", "application/json")
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Print(err.Error())
		return nil, internalServerError(errors.New("Error from client Do"))
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Print(err.Error())
		return nil, internalServerError(errors.New("Error from io ReadAll"))
	}
	if response.StatusCode != 200 {
		log.Println("CreateExternalAccount, StatusCode:", response.StatusCode)
		log.Println("CreateExternalAccount, responseData:", string(responseData))
		if _, err := s.CreateWebhookLog("CreateExternalAccount, ERROR", string(responseData)+helper.StructJson(body)); err != nil {
			log.Print(err)
		}
		return nil, internalServerError(errors.New("ไม่สำเร็จจากธนาคาร :" + string(responseData)))
	}

	var result model.ExternalAccountCreateResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		return nil, internalServerError(errors.New("Error from JSON response"))
	}
	jsonResult, err := json.Marshal(result)
	if err == nil {
		_, err := s.CreateWebhookLog("CreateExternalAccount, SUCCESS", string(jsonResult))
		log.Print(err)
	}
	return &result, nil
}

func (s *accountingService) UpdateExternalAccount(body model.ExternalAccountUpdateBody) (*model.ExternalAccountCreateResponse, error) {
	jsonString := helper.StructJson(body)
	jsonString = strings.ReplaceAll(jsonString, "null", `""`)

	client := &http.Client{}
	data := []byte(jsonString)
	reqExternal, _ := http.NewRequest("PUT", os.Getenv("ACCOUNTING_API_ENDPOINT")+"/api/v2/site/bankAccount", bytes.NewBuffer(data))
	reqExternal.Header.Set("apiKey", os.Getenv("ACCOUNTING_API_KEY"))
	reqExternal.Header.Set("Content-Type", "application/json")
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Print(err.Error())
		return nil, internalServerError(errors.New("Error from client Do"))
	}
	defer response.Body.Close() // Ensure the response body is closed when done with it

	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
		return nil, internalServerError(errors.New("Error from io ReadAll"))
	}
	if response.StatusCode != 200 {
		if _, err := s.CreateWebhookLog("UpdateExternalAccount, ERROR", string(responseData)); err != nil {
			log.Print(err)
		}
		return nil, internalServerError(fmt.Errorf("Error from external API: %s", string(responseData)))
	}
	var result model.ExternalAccountCreateResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		return nil, internalServerError(errors.New("Error from JSON response"))
	}
	jsonResult, err := json.Marshal(result)
	if err == nil {
		_, webhookErr := s.CreateWebhookLog("UpdateExternalAccount, SUCCESS", string(jsonResult))
		log.Print(webhookErr)
	}
	return &result, nil
}

func (s *accountingService) DeleteExternalAccount(query model.ExternalAccountStatusRequest) error {

	client := &http.Client{}
	reqExternal, _ := http.NewRequest("DELETE", os.Getenv("ACCOUNTING_API_ENDPOINT")+"/api/v2/site/bankAccount/"+query.AccountNumber, nil)
	reqExternal.Header.Set("apiKey", os.Getenv("ACCOUNTING_API_KEY"))
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Print(err.Error())
		return internalServerError(errors.New("Error from client Do"))
	}
	if response.StatusCode != 200 {
		log.Println(response)
		return internalServerError(errors.New("Error from external API"))
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
	}
	_, webhookErr := s.CreateWebhookLog("DeleteExternalAccount, responseData:", string(responseData))
	log.Print(webhookErr)
	return nil
}

func (s *accountingService) enableExternalAccount(req model.ExternalAccountEnableRequest) (*model.ExternalAccountStatus, error) {

	client := &http.Client{}
	// curl -X POST "https://api.fastbankapi.com/api/v2/site/enable-bank" -H "accept: */*" -H "apiKey: 123" -H "Content-Type: application/json" -d "{ \"accountNo\": \"string\", \"enable\": true}"
	data, _ := json.Marshal(req)
	reqExternal, _ := http.NewRequest("POST", os.Getenv("ACCOUNTING_API_ENDPOINT")+"/api/v2/site/enable-bank", bytes.NewBuffer(data))
	reqExternal.Header.Set("apiKey", os.Getenv("ACCOUNTING_API_KEY"))
	reqExternal.Header.Set("Content-Type", "application/json")
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Print(err.Error())
		return nil, internalServerError(errors.New("Error from client Do"))
	}
	if response.StatusCode != 200 {
		log.Println(response)
		return nil, internalServerError(errors.New("Error from external API"))
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
	}
	// log.Println("EnableExternalAccount:", string(responseData))
	// {"success":true,"enable":true,"status":"online"}
	// {"success":true,"enable":false,"status":"offline"}
	var result model.ExternalAccountStatus
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		return nil, internalServerError(errors.New("Error from JSON response"))
	}
	return &result, nil
}

func (s *accountingService) EnableExternalAccount(req model.BankAccountToggleFastbankRequest) (*model.ExternalAccountStatus, error) {

	bankAccount, err := s.repo.GetBankAccountByAccountNumber(req.AccountNo)
	if err != nil {
		return nil, internalServerError(errors.New("Error from database"))
	}

	fastBankReq := model.ExternalAccountEnableRequest{
		AccountNo: bankAccount.AccountNumber,
		Enable:    req.Enable,
	}
	fastBankResp, err := s.enableExternalAccount(fastBankReq)
	if err != nil {
		return nil, internalServerError(errors.New("Error from external API"))
	}

	var bankAccountBody model.BankAccountUpdateBody
	var statusId int64
	if fastBankResp.Enable {
		statusId = model.CONNECTION_CONNECTED
	} else {
		statusId = model.CONNECTION_DISCONNECTED
	}
	bankAccountBody.ConnectionStatusId = &statusId
	if err := s.repo.UpdateBankAccount(bankAccount.Id, bankAccountBody); err != nil {
		return nil, internalServerError(errors.New("Error from database"))
	}

	// [ADMIN_ACTION] SUCCESS ปิดการใช้งานธนาคาร SCB ชื่อบัญชี สมภา มาลา, เปิดการใช้งานธนาคาร SCB ชื่อบัญชี สมภา มาลา
	var adminActionCreateBody model.AdminActionCreateRequest
	adminActionCreateBody.AdminId = req.UpdateBy
	adminActionCreateBody.TypeId = model.ADMIN_ACTION_ACCOUNT_MANAGE
	if req.Enable {
		adminActionCreateBody.Detail = fmt.Sprintf("เปิดการใช้งานธนาคาร %s ชื่อบัญชี %s", bankAccount.BankName, bankAccount.AccountName)
	} else {
		adminActionCreateBody.Detail = fmt.Sprintf("ปิดการใช้งานธนาคาร %s ชื่อบัญชี %s", bankAccount.BankName, bankAccount.AccountName)
	}
	adminActionCreateBody.JsonInput = helper.StructJson(req)
	if _, err := s.adminAction.CreateSuccessAdminAction(adminActionCreateBody); err != nil {
		return nil, err
	}

	return fastBankResp, nil
}

func (s *accountingService) GetExternalAccountLogs(req model.ExternalStatementListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	client := &http.Client{}
	// curl -X GET "https://api.fastbankapi.com/api/v2/site/bankAccount/logs?accountNo=aaaaaaaaaaaaaa&page=0&size=10" -H "accept: */*" -H "apiKey: xxxxxxxxxx.yyyyyyyyyyy"
	queryString := fmt.Sprintf("&page=%d&size=%d", req.Page, req.Limit)
	fullPath := os.Getenv("ACCOUNTING_API_ENDPOINT") + "/api/v2/site/bankAccount/logs?accountNo=" + req.AccountNumber + queryString
	reqExternal, _ := http.NewRequest("GET", fullPath, nil)
	reqExternal.Header.Set("apiKey", os.Getenv("ACCOUNTING_API_KEY"))
	response, err := client.Do(reqExternal)

	if err != nil {
		log.Print(err.Error())

	}

	if response.StatusCode != 200 {
		log.Println(response)
		return nil, internalServerError(errors.New("Error from external API"))
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
	}
	var externalList model.ExternalListWithPagination
	errJson := json.Unmarshal(responseData, &externalList)
	if errJson != nil {
		return nil, internalServerError(errors.New("Error from JSON response"))
	}
	// log.Println("response", string(responseData))

	// End count total records for pagination purposes (without limit and offset) //
	var result model.SuccessWithPagination
	result.List = externalList.Content
	result.Total = externalList.TotalElements
	return &result, nil
}

func (s *accountingService) GetExternalAccountStatements(req model.ExternalStatementListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	client := &http.Client{}
	// https://api.fastbankapi.com/api/v2/statement?accountNo=aaaaa&page=0&size=10&txnCode=all
	// curl -X GET "https://api.fastbankapi.com/api/v2/statement?accountNo=hhhhhh&page=0&size=10&txnCode=all" -H "accept: */*" -H "apiKey: aaaaaaaa.bbbbbbbbbbb"
	queryString := fmt.Sprintf("&page=%d&size=%d", req.Page, req.Limit)
	if req.OfDate != "" {
		queryString += fmt.Sprintf("&date=%s", req.OfDate)
	}
	if req.TxnCode != "" {
		queryString += fmt.Sprintf("&txnCode=%s", req.TxnCode)
	}
	fullPath := os.Getenv("ACCOUNTING_API_ENDPOINT") + "/api/v2/statement?accountNo=" + req.AccountNumber + queryString
	reqExternal, _ := http.NewRequest("GET", fullPath, nil)
	reqExternal.Header.Set("apiKey", os.Getenv("ACCOUNTING_API_KEY"))
	response, err := client.Do(reqExternal)

	if err != nil {
		log.Print(err.Error())
		return nil, internalServerError(errors.New("Error from client Do"))
	}

	if response.StatusCode != 200 {
		log.Println(fullPath, response)
		return nil, internalServerError(errors.New("Error from external API"))
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
	}
	var externalList model.ExternalStatementListWithPagination
	errJson := json.Unmarshal(responseData, &externalList)
	if errJson != nil {
		log.Println(errJson)
		return nil, internalServerError(errors.New("Error from JSON response"))
	}
	// log.Println("response", string(responseData))

	// End count total records for pagination purposes (without limit and offset) //
	var result model.SuccessWithPagination
	result.List = externalList.Content
	result.Total = externalList.TotalElements
	return &result, nil
}

func (s *accountingService) GetExternalUnreadAccountStatements(req model.ExternalStatementListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	client := &http.Client{}
	// curl -X GET "https://api.fastbankapi.com/api/v2/statement/unread?accountNo=aaaaaaaaaaa&page=0&size=10&sort=desc"
	// -H "accept: */*" -H "apiKey: bbbbbbbbbbbb.cccccccccccc"
	queryString := fmt.Sprintf("&page=%d&size=%d", req.Page, req.Limit)
	if req.OfDate != "" {
		queryString += fmt.Sprintf("&date=%s", req.OfDate)
	}
	if req.TxnCode != "" {
		queryString += fmt.Sprintf("&txnCode=%s", req.TxnCode)
	}
	fullPath := os.Getenv("ACCOUNTING_API_ENDPOINT") + "/api/v2/statement/unread?accountNo=" + req.AccountNumber + queryString

	reqExternal, _ := http.NewRequest("GET", fullPath, nil)
	reqExternal.Header.Set("apiKey", os.Getenv("ACCOUNTING_API_KEY"))
	response, err := client.Do(reqExternal)

	if err != nil {
		log.Print(err.Error())
		return nil, internalServerError(errors.New("Error from client Do"))
	}

	if response.StatusCode != 200 {
		log.Println(fullPath, response)
		return nil, internalServerError(errors.New("Error from external API"))
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
	}
	var externalList model.ExternalStatementListWithPagination
	errJson := json.Unmarshal(responseData, &externalList)
	if errJson != nil {
		log.Println(errJson)
		return nil, internalServerError(errors.New("Error from JSON response"))
	}

	// End count total records for pagination purposes (without limit and offset) //
	var result model.SuccessWithPagination
	result.List = externalList.Content
	result.Total = externalList.TotalElements
	return &result, nil
}

func (s *accountingService) GetExternalAccountStatementByTimestamp(req model.ExternalStatementListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	client := &http.Client{}
	// https://api.fastbankapi.com/api/v2/statement?accountNo=aaaaa&date=2023-05-08%2011%3A15%3A37&page=0&size=10&txnCode=all
	// curl -X GET "https://api.fastbankapi.com/api/v2/statement?accountNo=aaaaa&date=2023-05-08%2011%3A15%3A37&page=0&size=10&txnCode=all" -H "accept: */*" -H "apiKey: bbbbb.cccccc"
	queryString := fmt.Sprintf("&page=%d&size=%d&txnCode=all", req.Page, req.Limit)
	if req.OfDateTime != "" {
		queryString += fmt.Sprintf("&date=%s", req.OfDateTime)
	}
	fullPath := os.Getenv("ACCOUNTING_API_ENDPOINT") + "/api/v2/statement?accountNo=" + req.AccountNumber + queryString
	reqExternal, _ := http.NewRequest("GET", fullPath, nil)
	reqExternal.Header.Set("apiKey", os.Getenv("ACCOUNTING_API_KEY"))
	response, err := client.Do(reqExternal)

	if err != nil {
		log.Print(err.Error())
		return nil, internalServerError(errors.New("Error from client Do"))
	}

	if response.StatusCode != 200 {
		log.Println(fullPath, response)
		return nil, internalServerError(errors.New("Error from external API"))
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
	}
	var externalList model.ExternalStatementListWithPagination
	errJson := json.Unmarshal(responseData, &externalList)
	if errJson != nil {
		log.Println(errJson)
		return nil, internalServerError(errors.New("Error from JSON response"))
	}
	// log.Println("response", string(responseData))

	// End count total records for pagination purposes (without limit and offset) //
	var result model.SuccessWithPagination
	result.List = externalList.Content
	result.Total = externalList.TotalElements
	return &result, nil
}

func (s *accountingService) TransferExternalAccount(req model.ExternalAccountTransferRequest) error {

	var body model.ExternalAccountTransferBody
	systemAccount, err := s.repo.GetBankAccountById(req.SystemAccountId)
	if err != nil {
		if err.Error() == recordNotFound {
			return notFound(bankAccountNotFound)
		}
		return internalServerError(err)
	}
	body.AccountForm = systemAccount.AccountNumber
	body.AccountTo = req.AccountNumber
	body.Amount = req.Amount
	body.BankCode = req.BankCode
	body.Pin = systemAccount.PinCode

	client := &http.Client{}
	// curl -X POST "https://api.fastbankapi.com/api/v2/statement/transfer" -H "accept: */*" -H "apiKey: xxxxxxxxxx.yyyyyyyyyyy"
	//-H "Content-Type: application/json" -d "{ \"accountFrom\": \"aaaaaaaaaaaaaaaa\", \"accountTo\": \"bbbbbbbbbbbbbb\", \"amount\": \"8\", \"bankCode\": \"bay\", \"pin\": \"ccccc\"}"
	data, _ := json.Marshal(body)
	reqHttp, _ := http.NewRequest("POST", os.Getenv("ACCOUNTING_API_ENDPOINT")+"/api/v2/statement/transfer", bytes.NewBuffer(data))
	reqHttp.Header.Set("apiKey", os.Getenv("ACCOUNTING_API_KEY"))
	reqHttp.Header.Set("Content-Type", "application/json")
	response, err := client.Do(reqHttp)
	if err != nil {
		log.Print(err.Error())
		return internalServerError(errors.New("Error from client Do"))
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Println(err)
	}
	// log.Println("response", string(responseData))

	if response.StatusCode != 200 {
		var errorModel model.ExternalAccountError
		errJson := json.Unmarshal(responseData, &errorModel)
		if errJson != nil {
			return internalServerError(errors.New("Error from JSON response"))
		}
		log.Println("errorModel", errorModel)
		if errorModel.Error != "" {
			return internalServerError(errors.New(errorModel.Error))
		}
		return internalServerError(errors.New("Error from external API"))
	}
	return nil
}

func (s *accountingService) CreateBankStatementFromWebhookOnly(data model.WebhookStatement) (*int64, error) {

	fmt.Println("CreateBankStatementFromWebhookOnly : statement Begin log", helper.StructJson(data))

	// [Bank account check]
	systemAccount, err := s.repo.GetBankAccountByExternalId(data.BankAccountId)
	if err != nil {
		log.Println(err)
		return nil, badRequest("Invalid Bank Account")
	}

	configWeb, err := s.repo.GetWebConfiguration()
	if err != nil {
		log.Println(err)
		return nil, badRequest("WEB_CONFIG_NOT_FOUND")
	}

	// [Check Exist Statement]
	_, errOldStatement := s.repo.GetWebhookStatementByExternalId(data.Id)
	if errOldStatement != nil && errOldStatement.Error() == recordNotFound {

		// [Create Statement : Check TxnCode]
		var bodyCreateState model.BankStatementCreateBody
		bodyCreateState.AccountId = systemAccount.Id
		bodyCreateState.ExternalId = &data.Id
		data.TxnCode = strings.ToUpper(data.TxnCode)
		if data.TxnCode == "X1" || data.TxnCode == "CR" || data.TxnCode == "IORSDT" || data.TxnCode == "NBSDT" {
			bodyCreateState.StatementTypeId = model.STATEMENT_TYPE_TRANSFER_IN
			bodyCreateState.Amount = data.Amount
			// เช็ตยอดเงินขั้นต่ำ ** มันก็เช็คทุกรายการ ไม่ได้เช็คแค่ฝากนะ
			minDepositAmount := float64(configWeb.MinimumDeposit)
			if minDepositAmount > data.Amount {
				// P.mink 2024/09/25 ตั้งค่าฝากขั้นต่ำที่ xx.xx บาท
				messageMinimumDeposit := fmt.Sprintf("ตั้งค่าฝากขั้นต่ำที่ %.2f บาท", minDepositAmount)
				return nil, badRequest(messageMinimumDeposit)
			}
		} else if data.TxnCode == "X2" || data.TxnCode == "DR" || data.TxnCode == "NBSWT" || data.TxnCode == "IORSWT" {
			bodyCreateState.StatementTypeId = model.STATEMENT_TYPE_TRANSFER_OUT
			bodyCreateState.Amount = data.Amount * -1
		} else {
			if _, err := s.CreateWebhookLog("unsupport TxnCode found, WebhookStatement:", data.TxnCode); err != nil {
				log.Print(err)
			}
			return nil, badRequest("Invalid TxnCode")
		}
		// [Create Statement : Check User Bank]
		var bank *model.BankResponse
		bank, err = s.GetBankFromWebhook(data.Info)
		if err != nil {
			// GetBankByCodeWithFlag(code string) (*model.BankResponse, error)
			bank, err = s.repo.GetBankByCodeWithFlag(data.BankCode)
			if err != nil {
				webhookLogMessage := fmt.Sprintf("GetBankByCodeWithFlag, ERROR: %s", err)
				return nil, errors.New(webhookLogMessage)
			}
		}
		bodyCreateState.FromBankId = bank.Id

		// [Create Statement : Extract Account Number ]
		accountNumber, _ := s.ExtractAccountNumber(data.Info, bank.Code)
		if accountNumber == "UNMATCH" {
			bodyCreateState.FromAccountNumber = ""
		} else {
			bodyCreateState.FromAccountNumber = accountNumber
		}

		// [Create Statement : Check Is It Account Move Transaction]
		var check model.CheckDepositAccountMoveTransaction
		check.FromAccountBankId = bank.Id
		check.FromAccountNumber = bodyCreateState.FromAccountNumber
		check.ToAccountId = systemAccount.Id
		check.ToAccountBankId = systemAccount.BankId
		isCheckAccountMoveTran, err := s.CheckDepositAccountMoveTransaction(check)
		if err != nil {
			return nil, nil
		}
		if isCheckAccountMoveTran {
			return nil, badRequest("Account move transaction")
		}

		// [Create Statement : Create Statement]
		bodyCreateState.Detail = data.TxnDescription + " " + data.Info
		bodyCreateState.TransferAt = data.DateTime
		bodyCreateState.StatementStatusId = model.STATEMENT_STATUS_PENDING
		insertId, err := s.repo.CreateWebhookStatement(bodyCreateState)
		if err != nil {
			webhookLogMessage := fmt.Sprintf("CreateWebhookStatement, ERROR: %s", err)
			return nil, errors.New(webhookLogMessage)
		}
		return insertId, nil
	}
	return nil, errors.New("statement already exists")
}

func (s *accountingService) CreateBankStatementFromWebhookAndAuto(data model.WebhookStatement, adminId *int64) (*int64, error) {

	uppercaseBankCode := strings.ToUpper(data.BankCode)
	// 2024/11/15 P.layer เอากลับมา scb แล้วไปดักตอน web hook เข้า + ตอนแสดง list scb จาก smsmode ให้ เป็น api mode ปกติ
	if data.IsRead && uppercaseBankCode != "SCB" {
		// P.Lay ******** ถ้าเป็น is Read จะ map จาก paygate_smsmode_deposit จาก info ผมเลยต้องดักออกไปเรียกอีกที่เลย
		// s.CreateBankStatementFromWebhookAndAutoSmsMode(data)
		transactionId, err := s.CreateBankStatementFromWebhookAndAutoSmsMode(data)
		if err != nil {
			log.Println(err)
			return nil, err
		}
		return transactionId, nil
	}

	var statementId int64
	fmt.Println("CreateBankStatementFromWebhookAndAuto : statement Begin log", helper.StructJson(data))
	startTime := time.Now()
	// [Bank account check]
	systemAccount, err := s.repo.GetBankAccountByExternalId(data.BankAccountId)
	if err != nil {
		log.Println(err)
		return nil, badRequest("Invalid Bank Account")
	}

	// [********]fastbank credit check balance to continue unuse func or not auto
	getWebLocal, _ := s.repo.GetLocalWebInfo()
	if getWebLocal != nil {
		if getWebLocal.FastbankCreditBalance <= -1000 {
			log.Println("CreateBankStatementFromWebhookAndAuto.WEB_OUT_OF_CREDIT")
			return nil, badRequest("WEB_OUT_OF_CREDIT")
		}
	}

	configWeb, err := s.repo.GetWebConfiguration()
	if err != nil {
		log.Println(err)
		return nil, badRequest("WEB_CONFIG_NOT_FOUND")
	}

	// [Check Exist Statement]
	_, errOldStatement := s.repo.GetWebhookStatementByExternalId(data.Id)
	if errOldStatement != nil && errOldStatement.Error() == recordNotFound {

		// [Create Statement : Check TxnCode]
		var bodyCreateState model.BankStatementCreateBody
		bodyCreateState.AccountId = systemAccount.Id
		bodyCreateState.ExternalId = &data.Id
		data.TxnCode = strings.ToUpper(data.TxnCode)
		if data.TxnCode == "X1" || data.TxnCode == "CR" || data.TxnCode == "IORSDT" || data.TxnCode == "NBSDT" {
			//[ย้ายมาตรงนี้เพราะ จะเช็คยอดถอน] เช็ตยอดเงินขั้นต่ำ ** มันก็เช็คทุกรายการ ไม่ได้เช็คแค่ฝากนะ
			minDepositAmount := float64(configWeb.MinimumDeposit)
			if minDepositAmount > data.Amount {
				// P.mink 2024/09/25 ตั้งค่าฝากขั้นต่ำที่ xx.xx บาท
				messageMinimumDeposit := fmt.Sprintf("ตั้งค่าฝากขั้นต่ำที่ %.2f บาท", minDepositAmount)
				return nil, badRequest(messageMinimumDeposit)
			}
			bodyCreateState.StatementTypeId = model.STATEMENT_TYPE_TRANSFER_IN
			bodyCreateState.Amount = data.Amount
		} else if data.TxnCode == "X2" || data.TxnCode == "DR" || data.TxnCode == "NBSWT" || data.TxnCode == "IORSWT" {
			bodyCreateState.StatementTypeId = model.STATEMENT_TYPE_TRANSFER_OUT
			bodyCreateState.Amount = data.Amount * -1
		} else {
			if _, err := s.CreateWebhookLog("unsupport TxnCode found, WebhookStatement:", data.TxnCode); err != nil {
				log.Print(err)
			}
			return nil, badRequest("Invalid TxnCode")
		}
		// [Create Statement : Check User Bank]
		var bank *model.BankResponse
		bank, err = s.GetBankFromWebhook(data.Info)
		if err != nil {
			// GetBankByCodeWithFlag(code string) (*model.BankResponse, error)
			bank, err = s.repo.GetBankByCodeWithFlag(data.BankCode)
			if err != nil {
				webhookLogMessage := fmt.Sprintf("GetBankByCodeWithFlag, ERROR: %s", err)
				return nil, errors.New(webhookLogMessage)
			}

		}
		bodyCreateState.FromBankId = bank.Id

		// [Create Statement : Extract Account Number ]
		accountNumber, _ := s.ExtractAccountNumber(data.Info, bank.Code)
		if accountNumber == "UNMATCH" {
			bodyCreateState.FromAccountNumber = ""
		} else {
			bodyCreateState.FromAccountNumber = accountNumber
		}

		// [Create Statement : Check Is It Account Move Transaction]
		var check model.CheckDepositAccountMoveTransaction
		check.FromAccountBankId = bank.Id
		check.FromAccountNumber = bodyCreateState.FromAccountNumber
		check.ToAccountId = systemAccount.Id
		check.ToAccountBankId = systemAccount.BankId
		isCheckAccountMoveTran, err := s.CheckDepositAccountMoveTransaction(check)
		if err != nil {
			return nil, nil
		}
		if isCheckAccountMoveTran {
			return nil, badRequest("Account move transaction")
		}

		// [Create Statement : Create Statement]
		bodyCreateState.Detail = data.TxnDescription + " " + data.Info
		// [********] Confirm by P.lay SCB Set -1 day when 23.00 - 00.00
		if !systemAccount.SmsMode {
			timeDateStatementSCB := data.DateTime.Add(7 * time.Hour).Hour()
			if systemAccount.BankId == model.BANK_ID_SCB && (timeDateStatementSCB == 23) {
				data.DateTime = data.DateTime.AddDate(0, 0, -1)
			}
		}

		bodyCreateState.TransferAt = data.DateTime
		bodyCreateState.StatementStatusId = model.STATEMENT_STATUS_PENDING
		insertId, err := s.repo.CreateWebhookStatement(bodyCreateState)
		if err != nil {
			webhookLogMessage := fmt.Sprintf("CreateWebhookStatement, ERROR: %s", err)
			return nil, errors.New(webhookLogMessage)
		}

		// 2024-10-24 P.Lay เวลาโอนเงินจริงเทียบกับ เวลาปัจจุบัน ถ้าเกิน 1 ชั่วโมง ไม่เข้า auto เพราะ fastbank โอนjob มาอีกรอบ10รายการแรกหลัง crash
		// admin By pass
		// 2025-03-23 Change 1 hour to 15 min
		if adminId == nil {
			if time.Now().UTC().Sub(bodyCreateState.TransferAt) > 15*time.Minute {
				return nil, errors.New("More than 1 hour webhook")
			}
		}

		statementId = *insertId
		bodyCreateState.Id = *insertId
		if bodyCreateState.StatementTypeId == model.STATEMENT_TYPE_TRANSFER_IN {
			// ===========================================================
			// [Create Transaction : Check Duplicate]

			// [Create Transaction : Reget Statement]
			statement, err := s.repo.GetBankStatementById(*insertId)
			if err != nil {
				return nil, err
			}

			// [Create Transaction : Double Check Possible Owner]
			var total int64
			var records []model.Member
			var reqPosibleList model.GetPossibleOwnersRequest
			reqPosibleList.FromBankId = statement.FromBankId
			reqPosibleList.ToBankId = systemAccount.BankId
			reqPosibleList.FromAccountNumber = statement.FromAccountNumber

			if accountNumber != "UNMATCH" {

				records, total, err = s.repo.GetPossibleOwnersByStatementId(reqPosibleList)
				if err != nil {
					return nil, nil
				}

				debugReq := map[string]interface{}{
					"possibleStatement": records,
				}

				if total > 1 {
					filterRecord, filterTotal, err := s.TryToFilterPossibleOwner2(records, bodyCreateState.Detail)
					if err != nil {
						// return nil
					}
					if filterTotal == 1 {
						debugReq["filterRecord"] = filterRecord
						records = filterRecord
						total = filterTotal
					}
				}
				debugReq["statementId"] = bodyCreateState.Id
				// log
				if err := s.repo.SuccessLog("GetPossibleOwners.RESULT", reqPosibleList, debugReq); err != nil {
					log.Println("UnableToMatchStatement", err)
				}

				fmt.Println("CreateBankStatementFromWebhookAndAuto : statement GetPossibleOwnersByStatementId", helper.StructJson(records))
			}

			// [Set Confirmed By Bot]
			// admin := model.ApprovedByAdmin{
			// 	Id: 0,
			// }
			var admin model.ApprovedByAdmin
			if adminId != nil {
				admin.Id = *adminId
			} else {
				admin.Id = 0
			}

			var lineResultCredit float64
			// [Create Transaction : Create Transaction]
			if total == 1 {
				// Create Transaction + Create New MemberCode
				// Confirmed + MatchOwner
				// Increase Credit
				// Create Commission + Turnover
				// WebSocket
				// Line Notify
				for _, possibleOwner := range records {

					if possibleOwner.MemberCode != "" {
						// [********] ออมสินต้องให้ match แล้วครับ มา set ค่า ใหม่
						bodyCreateState.FromAccountNumber = possibleOwner.BankAccount
						bodyCreateState.FromBankId = possibleOwner.BankId

						// ปรับ flow ตามพี่เลย์บอก ความจริงให้เข้าไปสร้าง แต่ไม่ auto แต่ผม จะ หยุด ไม่ให้เกิดเคสเดิมที่เข้า 2 รายการ
						var checkDuplicate model.CheckDuplicateWebhookAndAdminRecord
						checkDuplicate.FromAccountNumber = bodyCreateState.FromAccountNumber
						checkDuplicate.FromBankId = bodyCreateState.FromBankId
						checkDuplicate.Amount = bodyCreateState.Amount
						checkDuplicate.TransactionAt = bodyCreateState.TransferAt
						checkDuplicate.MemberCode = possibleOwner.MemberCode
						checkDuplicate.CheckFromWhere = "WEBHOOK"
						checkDuplicate.ToBankId = &systemAccount.BankId
						duplicateFromAdminRecord, _ := s.repo.CheckDuplicateWebhookAndAdminRecord2(checkDuplicate)
						if duplicateFromAdminRecord.Id != 0 {
							// update statement Id in to transaction
							err := s.repo.UpdateTransactionDuplicateWithExternalMatch(duplicateFromAdminRecord.Id, *insertId)
							if err != nil {
								return nil, nil
							}

							statusLog := "SUCCESS"
							jsonPayLoad := fmt.Sprintf("DUPLICATE WITH ADMIN BANK TRANS ID: %v  AND STATEMENT ID: %v ", duplicateFromAdminRecord.Id, *insertId)
							jsonRequest := helper.StructJson(data)
							logType := "DUPLICATE_WITH_ADMIN_RECORD"
							var createLog model.BankTransactionLogCreateRequest
							createLog.Status = &statusLog
							createLog.JsonPayload = &jsonPayLoad
							createLog.JsonRequest = &jsonRequest
							createLog.LogType = &logType

							if _, err := s.CreateBankTransactionLog(createLog); err != nil {
								return nil, err
							}
							log.Println("GetWebhookStatementByExternalId.duplicateFromRecord", helper.StructJson(duplicateFromAdminRecord))
							return nil, badRequest("พบรายการซ้ำกับรายการที่มีอยู่ในระบบ")
						}
					}

					// [Create Transaction : Check Statement Type Transfer In only]

					if possibleOwner.Id != 0 && configWeb != nil {
						// [********] ฝากครั้งแรกขั้นต่ำ ปรับ flow เช็ค user ให้เช็คจากรายการฝากครั้งแรกจริงๆ ไม่เช็คจากรหัสที่ได้รับ
						if err := s.CheckFirstMinimunDeposit(possibleOwner.Id, data.Amount, *configWeb); err != nil {
							return nil, badRequest("LOW_NOT_MEMBER_FIRST_DEPOSIT")
						}
					} else {
						log.Println("CreateBankStatementFromWebhookAndAuto.CheckFirstMinimunDeposit", "CANT_CHECK_FIRST_MINIMUM_DEPOSIT_ON_EMPTY_CONFIG")
					}

					// ===========================================================
					// [Create Transaction + Create New MemberCode]
					// auto_user_approve_type_id
					// 1 รับ user ทันทีหลังสมัครสมาชิก
					// 2 รับ user หลังจากฝากครั้งแรก
					// ** แต่ไม่จำเป็นต้องเช็ค config เราจะบังคับให้มี member code ทุกครั้งที่มีการฝากเข้ามา ถ้ายังไม่มีให้สร้างให้ใหม่
					// race condition deposit
					actionAt := startTime.UTC().Format("************")
					var createBody model.RaceActionCreateBody
					createBody.Name = "CreateBankStatementFromWebhookAndAuto"
					createBody.JsonRequest = helper.StructJson(bodyCreateState.Id)
					createBody.Status = "PENDING"
					createBody.ActionKey = fmt.Sprintf("DEPOSIT_T%s_U%d_CD%f", actionAt, possibleOwner.Id, data.Amount)
					createBody.UnlockAt = time.Now().UTC().Add(time.Second * 1)
					if _, err := s.repo.GetRaceActionIdByActionKey(createBody.ActionKey); err != gorm.ErrRecordNotFound {
						log.Println("CreateBankStatementFromWebhookAndAuto.GetRaceActionIdByActionKey", err)
					}
					// create race condition
					actionId, err := s.repo.CreateRaceCondition(createBody)
					if err != nil {
						log.Println("CreateBankStatementFromWebhookAndAuto.CreateRaceCondition", err)
					}
					if actionId > 0 {

						if possibleOwner.MemberCode == "" {
							memberCode, err := s.userService.GenUniqueUserMemberCode(possibleOwner.Id)
							if err != nil {
								log.Println("CreateBankStatementFromWebhookAndAuto.GenUniqueUserMemberCode", err)
								webhookLogMessage := fmt.Sprintf("GenUniqueUserMemberCode, ERROR: %s", err)
								return nil, errors.New(webhookLogMessage)
							}
							possibleOwner.MemberCode = *memberCode
						}

						// [Create Transaction : Create Transaction]
						// CreateBankStatementFromWebhookAndAuto.createAutoDepositTransaction#1.transId
						transId, err := s.createAutoDepositTransaction(possibleOwner, bodyCreateState, admin)
						if err != nil {
							webhookLogMessage := fmt.Sprintf("CreateAutoDepositTransaction, ERROR: %s", err)
							return nil, errors.New(webhookLogMessage)
						}

						// DECREASE FASTBANK CREDIT 1.FROM WEBHOOK+MATCHED
						if err := s.repo.DecreaseFastbankCredit(1); err != nil {
							log.Println("UserCreateWithdrawTransaction.DECREASE_FASTBANK_CREDIT_ERROR", err)
						}

						// ===========================================================

						// [Confirmed : MatchOwner]
						confirmedAt := time.Now()
						var statementMatchRequest model.BankStatementMatchRequest
						statementMatchRequest.ConfirmedAt = confirmedAt
						statementMatchRequest.UserId = possibleOwner.Id
						var setIdAuto int64 = 0
						statementMatchRequest.ConfirmedByAdminId = &setIdAuto
						if adminId != nil {
							statementMatchRequest.ConfirmedByAdminId = adminId
						}
						if err := s.SetStatementOwnerMatched(statement.Id, statementMatchRequest, model.USE_ENDING_NOTI); err != nil {
							webhookLogMessage := fmt.Sprintf("SetStatementOwnerMatched, ERROR: %s", err)
							return nil, errors.New(webhookLogMessage)
						}
						// ===========================================================
						// [Get promotion]
						var promotionWebUserId int64
						GetUserPromotion, _ := s.promotionWebService.GetDepositCurrentProcessingUserPromotion(possibleOwner.Id)
						if GetUserPromotion != nil {
							promotionWebUserId = GetUserPromotion.Id

							errUpdate := s.repo.UpdatePromotionToBankTransaction(*transId, promotionWebUserId)
							if errUpdate != nil {
								log.Println("CreateBankStatementFromWebhookAndAuto.UpdatePromotionToBankTransaction", err)
							}
						}

						// [Increase Credit]
						var userCreditReq model.UserTransactionCreateRequest
						userCreditReq.UserId = possibleOwner.Id
						userCreditReq.TypeId = model.CREDIT_TYPE_DEPOSIT
						userCreditReq.RefId = transId
						userCreditReq.PromotionId = &promotionWebUserId
						userCreditReq.Amount = statement.Amount
						userCreditReq.AccountId = &systemAccount.Id

						userCreditReq.Detail = fmt.Sprintf("%s:%s => %s:%s", possibleOwner.BankName, statement.FromAccountNumber, systemAccount.BankName, systemAccount.AccountName)
						userCreditReq.IsAdjustAuto = true
						// autoBotId := int64(0)
						userCreditReq.TransferAt = &bodyCreateState.TransferAt
						userCreditReq.CreateBy = &admin.Id
						userCreditReq.ConfirmBy = &admin.Id
						userCreditReq.StartWorkAt = startTime // เริ่มนับตอนได้รับ Webhook
						if agentResp, err := s.repo.IncreaseUserCredit(userCreditReq); err != nil {
							// rollback status statement
							var updateStatement model.BankStatementUpdateBody
							updateStatement.Id = bodyCreateState.Id
							updateStatement.StatementStatusId = model.STATEMENT_STATUS_PENDING
							if err := s.repo.UpdateBankStatementStatus(updateStatement); err != nil {
								log.Println("CreateBankStatementFromWebhookAndAuto.UpdateBankStatementStatus", err)
							}

							webhookLogMessage := fmt.Sprintf("IncreaseUserCredit, ERROR: %s", err)
							return nil, errors.New(webhookLogMessage)
						} else {
							// Panic ErrorCheck
							if transId == nil {
								log.Println("CreateBankStatementFromWebhookAndAuto.transId is nil")
							}
							if agentResp == nil {
								log.Println("CreateBankStatementFromWebhookAndAuto.agentResp is nil")
							}
							if transId != nil && agentResp != nil {
								// FASTBANK_SUCCESS
								lineResultCredit = agentResp.AgentAfterAmount
								if err := s.repo.UpdateDepositTransactionStatusFromAgent(*transId, *agentResp); err != nil {
									log.Println("CreateBankStatementFromWebhookAndAuto.UpdateDepositTransactionStatusFromAgent", err)
								}
							} else {
								log.Println("CreateBankStatementFromWebhookAndAuto.UpdateDepositTransactionStatusFromAgent.SKIP_WHEN_NULL")
							}

							// Set FastBank-READ
							var setReadRequest model.ExternalStatementSetReadBody
							setReadRequest.AccountNo = systemAccount.AccountNumber
							setReadRequest.StatementId = statement.ExternalId
							setReadRequest.UsedCredit = true
							if err := s.repo.SetExternalStatementRead(setReadRequest); err != nil {
								log.Println("CreateBankStatementFromWebhookAndAuto.SetExternalStatementRead", err)
							}
						}
						// ===========================================================
						isFirstDeposit := s.repo.IsFirstDeposit(possibleOwner.Id)
						if isFirstDeposit {
							// [Create Commission + Turnover]
							// if err := s.userFirstDepositBonus(bodyCreateState.Amount, possibleOwner.Id, *transId); err != nil {
							// 	webhookLogMessage := fmt.Sprintf("CreateBankStatementFromWebhookAndAuto.userFirstDepositBonus, ERROR: %s", err)
							// 	return nil, errors.New(webhookLogMessage)
							// }
							var bonusReq model.UserFirstDepositCreateRequest
							bonusReq.UserId = possibleOwner.Id
							bonusReq.TransactionId = transId
							bonusReq.TransferAt = bodyCreateState.TransferAt
							bonusReq.Amount = bodyCreateState.Amount
							bonusReq.Remark = "CreateBankStatementFromWebhookAndAuto"
							if err := SetFirstDepositBonus(s.repo, isFirstDeposit, bonusReq); err != nil {
								webhookLogMessage := fmt.Sprintf("CreateBankStatementFromWebhookAndAuto.userFirstDepositBonus, ERROR: %s", err)
								log.Println("SetFirstDepositBonus", webhookLogMessage)
								// return nil, errors.New(webhookLogMessage)
							}
						} else {
							// [Create Commission EVERY DEPOSIT]
							if possibleOwner.UserTypeName == "ALLIANCE" {
								if err := s.allianceService.NoUseAlUpdateCommission(possibleOwner.Id, bodyCreateState.Amount); err != nil {
									log.Println("CreateBankStatementFromWebhookAndAuto.AlUpdateCommission", err)
									webhookLogMessage := fmt.Sprintf("AlUpdateCommission, ERROR: %s", err)
									return nil, errors.New(webhookLogMessage)
								}
							}
						}
						// ===========================================================
						// [PromtionChecker]
						var checkUserPromotionBody model.CheckUserPromotionBody
						checkUserPromotionBody.UserId = possibleOwner.Id
						_, err = s.promotionWebService.CheckUserPromotion(checkUserPromotionBody)
						if err != nil {
							log.Println("CreateBankStatementFromWebhookAndAuto.CheckUserPromotion", err)
						}
						// ===========================================================
						// [Lucky Wheel]
						var luckyWheelBody model.ActivityLuckyWheelRoundUserRequest
						luckyWheelBody.UserId = possibleOwner.Id
						luckyWheelBody.ConditionId = model.ACTIVITY_LUCKY_WHEEL_SETTING_CONDITION_DEPOSIT
						luckyWheelBody.ConditionAmount = bodyCreateState.Amount
						if err := s.activityLuckyWheelService.CreateRoundActivityLuckyWheel(luckyWheelBody); err != nil {
							log.Println("CreateBankStatementFromWebhookAndAuto.CreateRoundActivityLuckyWheel", err)
						}
						// ===========================================================
						// [turnOver] ถ้ามีการฝากเงิน ให้สร้าง TurnOver
						if err := CreateTurnOverFromSuccessDeposit(repository.NewTurnoverRepository(s.shareDb), possibleOwner.Id, bodyCreateState.Amount, *transId); err != nil {
							log.Println("CreateBankStatementFromWebhookAndAuto.CreateTurnOverFromSuccessDeposit", err)
						}
						// ===========================================================
						// [TIER]
						if err := s.repo.IncreaseUserTierDepositAmount(possibleOwner.Id, bodyCreateState.Amount); err != nil {
							log.Println("CreateBankStatementFromWebhookAndAuto.IncreaseUserTierDepositAmount", err)
						}
						// ===========================================================
						// [Notify]
						endTime := time.Now()
						elapsed := endTime.Sub(startTime)
						elapsedSeconds := elapsed.Seconds()
						timeElapsed := fmt.Sprintf("%.2f", elapsedSeconds)
						// UpdateAutoProcessTimer(timer string, id int64) error
						if err := s.repo.UpdateAutoProcessTimer(timeElapsed, *transId); err != nil {
							webhookLogMessage := fmt.Sprintf("UpdateAutoProcessTimer, ERROR: %s", err)
							return nil, errors.New(webhookLogMessage)
						}
						var externalNoti model.NotifyExternalNotificationRequest
						externalNoti.TypeNotify = model.IsDepositAfterCredit
						externalNoti.Amount = statement.Amount
						externalNoti.MemberCode = possibleOwner.MemberCode
						externalNoti.UserCredit = lineResultCredit
						externalNoti.ConfirmedByAdminId = 0
						externalNoti.TimerCounter = timeElapsed
						externalNoti.TransferDateTime = bodyCreateState.TransferAt.Add(time.Hour * 7).Format("2006-01-02 15:04:05")
						externalNoti.ActionTime = time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02 15:04:05")
						externalNoti.WebScoket.UserID = possibleOwner.Id
						externalNoti.WebScoket.Amount = statement.Amount
						externalNoti.WebScoket.MemberCode = possibleOwner.MemberCode
						externalNoti.WebScoket.AlertType = "DEPOSIT"
						if err := s.notiService.ExternalNotification(externalNoti); err != nil {
							log.Println("FailedNotify", err)
						}
					} else {
						// check dupicate and make it only in transaction not auto
						if _, err := s.CreateDepositTransactionNoOwner(bodyCreateState, admin, model.TRANS_STATUS_PENDING); err != nil {
							webhookLogMessage := fmt.Sprintf("DuplicateCreateDepositTransactionNoOwner, ERROR: %s", err)
							return nil, errors.New(webhookLogMessage)
						}
					}
				}
			} else if total > 1 {
				// [Create Transaction : Create Transaction To Many Possible Owner]
				if _, err := s.CreateDepositTransactionNoOwner(bodyCreateState, admin, model.TRANS_STATUS_DEPOSIT_PENDING_MULTIUSER); err != nil {
					webhookLogMessage := fmt.Sprintf("CreateDepositTransactionNoOwner, ERROR: %s", err)
					return nil, errors.New(webhookLogMessage)
				}

				// DECREASE FASTBANK CREDIT 4.FROM WEBHOOK+MORE POSSIBLE OWNER
				if err := s.repo.DecreaseFastbankCredit(1); err != nil {
					log.Println("UserCreateWithdrawTransaction.DECREASE_FASTBANK_CREDIT_ERROR", err)
				}

				return nil, errors.New("too many possible owners")
			} else {
				// [Create Transaction : Create Transaction No Possible Owner]
				if _, err := s.CreateDepositTransactionNoOwner(bodyCreateState, admin, model.TRANS_STATUS_PENDING); err != nil {
					webhookLogMessage := fmt.Sprintf("CreateDepositTransactionNoOwner, ERROR: %s", err)
					return nil, errors.New(webhookLogMessage)
				}

				// DECREASE FASTBANK CREDIT 4.FROM WEBHOOK+MORE POSSIBLE OWNER
				if err := s.repo.DecreaseFastbankCredit(1); err != nil {
					log.Println("UserCreateWithdrawTransaction.DECREASE_FASTBANK_CREDIT_ERROR", err)
				}

				return nil, errors.New("no possible owners")
			}
		}

		if bodyCreateState.StatementTypeId == model.STATEMENT_TYPE_TRANSFER_OUT {
			// ExternalMatchWithdrawTransaction(bodyCreateState model.BankStatementCreateBody) error
			if err := s.ExternalMatchWithdrawTransaction(bodyCreateState); err != nil {
				webhookLogMessage := fmt.Sprintf("ExternalMatchWithdrawTransaction, ERROR: %s", err)
				log.Println(webhookLogMessage)
			}
		}
	} else {
		// [Create Statement : Check Exist Statement]
		return nil, errors.New("statement already exists")
	}

	return &statementId, nil
}

func (s *accountingService) MatchBankStatementFromFastbankStatementNotAuto(systemAccount model.BankAccount, bodyCreateState model.BankStatementCreateBody) error {

	// ===========================================================
	// startTime := time.Now()

	// [********]fastbank credit check balance to continue unuse func or not auto
	getWebLocal, _ := s.repo.GetLocalWebInfo()
	if getWebLocal != nil {
		if getWebLocal.FastbankCreditBalance <= -1000 {
			log.Println("MatchBankStatementFromFastbankStatementNotAuto.WEB_OUT_OF_CREDIT")
			return badRequest("WEB_OUT_OF_CREDIT")
		}
	}
	// [Create Transaction : Check Duplicate]
	var checkDuplicate model.CheckDuplicateWebhookAndAdminRecord
	checkDuplicate.FromAccountNumber = bodyCreateState.FromAccountNumber
	checkDuplicate.FromBankId = bodyCreateState.FromBankId
	checkDuplicate.Amount = bodyCreateState.Amount
	checkDuplicate.TransactionAt = bodyCreateState.TransferAt
	checkDuplicate.CheckFromWhere = "WEBHOOK"
	checkDuplicate.ToBankId = &systemAccount.BankId
	duplicateFromAdminRecord, _ := s.repo.CheckDuplicateWebhookAndAdminRecord2(checkDuplicate)
	if duplicateFromAdminRecord.Id != 0 {
		// update statement Id in to transaction
		err := s.repo.UpdateTransactionDuplicateWithExternalMatch(duplicateFromAdminRecord.Id, bodyCreateState.Id)
		if err != nil {
			return nil
		}

		statusLog := "SUCCESS"
		jsonPayLoad := fmt.Sprintf("DUPLICATE WITH ADMIN BANK TRANS ID: %v  AND STATEMENT ID: %v ", duplicateFromAdminRecord.Id, bodyCreateState.Id)
		jsonRequest := helper.StructJson(bodyCreateState)
		logType := "DUPLICATE_WITH_ADMIN_RECORD"
		var createLog model.BankTransactionLogCreateRequest
		createLog.Status = &statusLog
		createLog.JsonPayload = &jsonPayLoad
		createLog.JsonRequest = &jsonRequest
		createLog.LogType = &logType

		if _, err := s.CreateBankTransactionLog(createLog); err != nil {
			return err
		}
		log.Println("GetWebhookStatementByExternalId.duplicateFromRecord", helper.StructJson(duplicateFromAdminRecord))
		return badRequest("พบรายการซ้ำกับรายการที่มีอยู่ในระบบ")
	}

	// [Create Transaction : Reget Statement]
	statement, err := s.repo.GetBankStatementById(bodyCreateState.Id)
	if err != nil {
		return err
	}

	// [Create Transaction : Double Check Possible Owner]
	var total int64
	var records []model.Member
	var reqPosibleList model.GetPossibleOwnersRequest
	reqPosibleList.FromBankId = statement.FromBankId
	reqPosibleList.ToBankId = systemAccount.BankId
	reqPosibleList.FromAccountNumber = statement.FromAccountNumber

	if statement.FromAccountNumber != "UNMATCH" {
		records, total, err = s.repo.GetPossibleOwnersByStatementId(reqPosibleList)
		if err != nil {
			return nil
		}
		if total > 1 {
			filterRecord, filterTotal, err := s.TryToFilterPossibleOwner(records, bodyCreateState.Detail)
			if err != nil {
				return nil
			}
			if filterTotal == 1 {
				records = filterRecord
				total = filterTotal
			}
		}
	}

	// [Set Confirmed By Bot]
	admin := model.ApprovedByAdmin{
		Id: 0,
	}
	var lineResultCredit float64
	// [Create Transaction : Create Transaction]
	if total == 1 {
		// Create Transaction + Create New MemberCode
		// Confirmed + MatchOwner
		// Increase Credit
		// Create Commission + Turnover
		// WebSocket
		// Line Notify
		for _, possibleOwner := range records {
			// [Create Transaction : Check Statement Type Transfer In only]
			if bodyCreateState.StatementTypeId == model.STATEMENT_TYPE_TRANSFER_IN {

				// ===========================================================
				// [Create Transaction + Create New MemberCode]
				// auto_user_approve_type_id
				// 1 รับ user ทันทีหลังสมัครสมาชิก
				// 2 รับ user หลังจากฝากครั้งแรก
				// ** แต่ไม่จำเป็นต้องเช็ค config เราจะบังคับให้มี member code ทุกครั้งที่มีการฝากเข้ามา ถ้ายังไม่มีให้สร้างให้ใหม่
				if possibleOwner.MemberCode == "" {
					memberCode, err := s.userService.GenUniqueUserMemberCode(possibleOwner.Id)
					if err != nil {
						log.Println("CreateBankStatementFromWebhook.GenUniqueUserMemberCode", err)
						webhookLogMessage := fmt.Sprintf("GenUniqueUserMemberCode, ERROR: %s", err)
						return errors.New(webhookLogMessage)
					}
					possibleOwner.MemberCode = *memberCode
				}

				// [Create Transaction : Create Transaction]
				// transId, err := s.createAutoDepositTransaction(possibleOwner, bodyCreateState, admin)
				// [********] สร้างรายการให้แอดมินกดเอง ไม่ Auto แล้ว
				// MatchBankStatementFromFastbankStatementNotAuto.CreateDepositTransaction#1.transId
				if _, err := s.CreateDepositTransaction(possibleOwner, bodyCreateState, admin); err != nil {
					webhookLogMessage := fmt.Sprintf("CreateDepositTransaction, ERROR: %s", err)
					return errors.New(webhookLogMessage)
				}

				// DECREASE FASTBANK CREDIT 1.FROM WEBHOOK+MATCHED
				// if err := s.repo.DecreaseFastbankCredit(1); err != nil {
				// 	log.Println("UserCreateWithdrawTransaction.DECREASE_FASTBANK_CREDIT_ERROR", err)
				// }

				// // ===========================================================

				// // [Confirmed : MatchOwner]
				// confirmedAt := time.Now()
				// var statementMatchRequest model.BankStatementMatchRequest
				// statementMatchRequest.ConfirmedAt = confirmedAt
				// statementMatchRequest.UserId = possibleOwner.Id
				// var setIdAuto int64 = 0
				// statementMatchRequest.ConfirmedByAdminId = &setIdAuto
				// if err := s.SetStatementOwnerMatched(statement.Id, statementMatchRequest, model.USE_ENDING_NOTI); err != nil {
				// 	webhookLogMessage := fmt.Sprintf("SetStatementOwnerMatched, ERROR: %s", err)
				// 	return errors.New(webhookLogMessage)
				// }
				// // [Get promotion]
				// var promotionWebUserId int64
				// GetUserPromotion, _ := s.promotionWebService.GetDepositCurrentProcessingUserPromotion(possibleOwner.Id)
				// if GetUserPromotion != nil {
				// 	promotionWebUserId = GetUserPromotion.Id
				// [turnOver] ถ้ามีการฝากเงิน ให้สร้าง TurnOver
				// if err := CreateTurnOverFromSuccessDeposit(repository.NewTurnoverRepository(s.shareDb), user.Id, bodyCreateState.Amount, *transId); err != nil {
				// 	log.Println("CreateBankStatementFromWebhookAndAuto.CreateTurnOverFromSuccessDeposit", err)
				// }

				// 	errUpdate := s.repo.UpdatePromotionToBankTransaction(*transId, promotionWebUserId)
				// 	if errUpdate != nil {
				// 		log.Println("MatchBankStatementFromFastbankStatement.UpdatePromotionToBankTransaction", err)
				// 	}
				// }
				// // ===========================================================
				// // [Increase Credit]
				// var userCreditReq model.UserTransactionCreateRequest
				// userCreditReq.UserId = possibleOwner.Id
				// userCreditReq.TypeId = model.CREDIT_TYPE_DEPOSIT
				// userCreditReq.RefId = transId
				// userCreditReq.Amount = statement.Amount
				// userCreditReq.PromotionId = &promotionWebUserId
				// userCreditReq.AccountId = &systemAccount.Id
				// userCreditReq.Detail = fmt.Sprintf("%s:%s => %s:%s", statement.FromBankName, statement.FromAccountNumber, systemAccount.BankName, systemAccount.AccountName)
				// userCreditReq.IsAdjustAuto = true
				// autoBotId := int64(0)
				// userCreditReq.TransferAt = &bodyCreateState.TransferAt
				// userCreditReq.CreateBy = &autoBotId
				// userCreditReq.ConfirmBy = &autoBotId
				// userCreditReq.StartWorkAt = startTime // เริ่มนับตอนได้รับ Webhook
				// if agentResp, err := s.repo.IncreaseUserCredit(userCreditReq); err != nil {
				// 	webhookLogMessage := fmt.Sprintf("IncreaseUserCredit, ERROR: %s", err)
				// 	return errors.New(webhookLogMessage)
				// } else {
				// 	// FASTBANK_SUCCESS
				// 	lineResultCredit = agentResp.AgentAfterAmount
				// 	if err := s.repo.UpdateDepositTransactionStatusFromAgent(*transId, *agentResp); err != nil {
				// 		log.Println("CreateBankStatementFromWebhook.UpdateDeporsitTransactionStatusFromAgent", err)
				// 	}
				// 	// Set FastBank-READ
				// 	var setReadRequest model.ExternalStatementSetReadBody
				// 	setReadRequest.AccountNo = systemAccount.AccountNumber
				// 	setReadRequest.StatementId = statement.ExternalId
				// 	setReadRequest.UsedCredit = true
				// 	if err := s.repo.SetExternalStatementRead(setReadRequest); err != nil {
				// 		log.Println("CreateBankStatementFromWebhook.SetExternalStatementRead", err)
				// 	}
				// }
				// // ===========================================================
				// var isFirstDeposit = false
				// checkUserFirstDeposit, err := s.repo.IsFirstDeposit(possibleOwner.Id)
				// if err != nil {
				// 	log.Println("CreateBankStatementFromWebhook.checkUserFirstDeposit.ERROR", err)
				// } else {
				// 	isFirstDeposit = checkUserFirstDeposit
				// }
				// // [Create Commission + Turnover]
				// if isFirstDeposit {
				// 	if err := s.userFirstDepositBonus(bodyCreateState.Amount, possibleOwner.Id, *transId); err != nil {
				// 		webhookLogMessage := fmt.Sprintf("CreateBankStatementFromWebhook.userFirstDepositBonus, ERROR: %s", err)
				// 		return errors.New(webhookLogMessage)
				// 	}
				// } else {
				// 	// [Create Commission EVERY DEPOSIT]
				// 	if possibleOwner.UserTypeName == "ALLIANCE" {
				// 		if err := s.allianceService.AlUpdateCommission(possibleOwner.Id, bodyCreateState.Amount); err != nil {
				// 			log.Println("CreateBankStatementFromWebhook.AlUpdateCommission", err)
				// 			webhookLogMessage := fmt.Sprintf("AlUpdateCommission, ERROR: %s", err)
				// 			return errors.New(webhookLogMessage)
				// 		}
				// 	}
				// }
				// // ===========================================================
				// // [PromtionChecker]
				// var checkUserPromotionBody model.CheckUserPromotionBody
				// checkUserPromotionBody.UserId = possibleOwner.Id
				// _, err = s.promotionWebService.CheckUserPromotion(checkUserPromotionBody)
				// if err != nil {
				// 	log.Println("CreateBankStatementFromWebhook.CheckUserPromotion", err)
				// }
				// // ===========================================================
				// // [Lucky Wheel]
				// var luckyWheelBody model.ActivityLuckyWheelRoundUserRequest
				// luckyWheelBody.UserId = possibleOwner.Id
				// luckyWheelBody.ConditionId = model.ACTIVITY_LUCKY_WHEEL_SETTING_CONDITION_DEPOSIT
				// luckyWheelBody.ConditionAmount = bodyCreateState.Amount
				// if err := s.activityLuckyWheelService.CreateRoundActivityLuckyWheel(luckyWheelBody); err != nil {
				// 	log.Println("CreateBankStatementFromWebhook.CreateRoundActivityLuckyWheel", err)
				// }

				// ===========================================================

				// [Notify]
				// endTime := time.Now()
				// elapsed := endTime.Sub(startTime)
				// elapsedSeconds := elapsed.Seconds()
				// timeElapsed := fmt.Sprintf("%.2f", elapsedSeconds)
				// // UpdateAutoProcessTimer(timer string, id int64) error
				// if err := s.repo.UpdateAutoProcessTimer(timeElapsed, *transId); err != nil {
				// 	webhookLogMessage := fmt.Sprintf("UpdateAutoProcessTimer, ERROR: %s", err)
				// 	return errors.New(webhookLogMessage)
				// }
				var externalNoti model.NotifyExternalNotificationRequest
				externalNoti.TypeNotify = model.IsDepositAfterCredit
				externalNoti.Amount = statement.Amount
				externalNoti.MemberCode = possibleOwner.MemberCode
				externalNoti.UserCredit = lineResultCredit
				externalNoti.ConfirmedByAdminId = 0
				externalNoti.TimerCounter = "0"
				externalNoti.TransferDateTime = bodyCreateState.TransferAt.Add(time.Hour * 7).Format("2006-01-02 15:04:05")
				externalNoti.ActionTime = time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02 15:04:05")
				externalNoti.WebScoket.UserID = possibleOwner.Id
				externalNoti.WebScoket.Amount = statement.Amount
				externalNoti.WebScoket.MemberCode = possibleOwner.MemberCode
				externalNoti.WebScoket.AlertType = "DEPOSIT"
				if err := s.notiService.ExternalNotification(externalNoti); err != nil {
					log.Println("FailedNotify", err)
				}
			}
		}
	} else if total > 1 {
		// [Create Transaction : Create Transaction To Many Possible Owner]
		if _, err := s.CreateDepositTransactionNoOwner(bodyCreateState, admin, model.TRANS_STATUS_DEPOSIT_PENDING_MULTIUSER); err != nil {
			webhookLogMessage := fmt.Sprintf("CreateDepositTransactionNoOwner, ERROR: %s", err)
			return errors.New(webhookLogMessage)
		}

		// DECREASE FASTBANK CREDIT 4.FROM WEBHOOK+MORE POSSIBLE OWNER
		if err := s.repo.DecreaseFastbankCredit(1); err != nil {
			log.Println("UserCreateWithdrawTransaction.DECREASE_FASTBANK_CREDIT_ERROR", err)
		}

		return errors.New("too many possible owners")
	} else {
		// [Create Transaction : Create Transaction No Possible Owner]
		if _, err := s.CreateDepositTransactionNoOwner(bodyCreateState, admin, model.TRANS_STATUS_PENDING); err != nil {
			webhookLogMessage := fmt.Sprintf("CreateDepositTransactionNoOwner, ERROR: %s", err)
			return errors.New(webhookLogMessage)
		}
		return errors.New("no possible owners")
	}

	return nil
}

func (s *accountingService) TryToFilterPossibleOwner(body []model.Member, detail string) ([]model.Member, int64, error) {
	var count int64
	var possibleMembers []model.Member

	for _, possibleOwner := range body {
		var firstName string
		possibleOwner.Fullname = strings.ToLower(possibleOwner.Fullname)
		nameParts := strings.Fields(possibleOwner.Fullname)
		if len(nameParts) > 0 {
			// Use the first part (first name) as the name to match
			firstName = nameParts[0]
		} else {
			firstName = possibleOwner.Fullname
		}
		pattern := regexp.MustCompile(`(?i)` + regexp.QuoteMeta(firstName))

		detail = strings.ToLower(detail)
		if pattern.MatchString(detail) {
			possibleMembers = append(possibleMembers, possibleOwner)
		}
		count++
	}
	if len(possibleMembers) != 1 {
		return body, count, nil
	} else {
		count = 1
	}
	return possibleMembers, count, nil
}

func (s *accountingService) TryToFilterPossibleOwner2(body []model.Member, detail string) ([]model.Member, int64, error) {
	var count int64
	var possibleMembers []model.Member

	// Normalize the detail string for consistent comparison
	normalizedDetail := normalizeName(strings.ToLower(detail))

	for _, possibleOwner := range body {
		// Normalize the full name of the member
		normalizedName := normalizeName(strings.ToLower(possibleOwner.Fullname))

		// Use a regular expression to match the normalized name against the detail
		pattern := regexp.MustCompile(`(?i)` + regexp.QuoteMeta(normalizedName))

		if pattern.MatchString(normalizedDetail) {
			// If a match is found, add to the possible members
			possibleMembers = append(possibleMembers, possibleOwner)
		}
		count++
	}

	// If there's exactly one match, return it with the count set to 1
	if len(possibleMembers) == 1 {
		count = 1
		return possibleMembers, count, nil
	}

	// Otherwise, return the original list and the count
	return body, count, nil
}

func normalizeName(name string) string {
	// Precompiled regex patterns for performance
	var prefixRegex = regexp.MustCompile(`(?i)^(นาย|นาง|นางสาว|MISS|MR\.|MRS\.)\s*`)
	var specialCharRegex = regexp.MustCompile(`[^\p{L}\s]`)

	// Remove prefixes like "นาย", "MISS", etc.
	name = prefixRegex.ReplaceAllString(name, "")

	// Remove special characters such as ++, etc.
	name = specialCharRegex.ReplaceAllString(name, "")

	// Trim excess whitespace
	return strings.Join(strings.Fields(name), " ")
}

func (s *accountingService) UserFirstDepositCommission(member model.Member, depositAmount float64) error {

	var err error

	// 'NONE','AFFILIATE','ALLIANCE'
	switch member.UserTypeName {
	case "ALLIANCE":
		if err = s.allianceService.AlUpdateCommissionFirstDeposit(member.Id, depositAmount); err != nil {
			return internalServerError(err)
		}
	default:
		// กันว่าถ้าไม่มีค่าไรเลยให้เป็น AF CreateAffiliateMember
		// ถ้าฝากครั้งแรก จะได้เป็น AFFILIATE
		if err = s.repo.UpdateGenMemberCodeToAffilate(member.Id); err != nil {
			return internalServerError(err)
		}
		if member.RefBy != nil && *member.RefBy != 0 {
			if err := s.repo.CreateAffiliateMember(*member.RefBy, member.Id); err != nil {
				return internalServerError(err)
			}
			// WithoutSetting
			if err := s.affiliateService.UpdateMemberFirstDeposit(member, depositAmount); err != nil {
				return internalServerError(err)
			}
			// WithSettingCheck
			if err = s.affiliateService.UpdateCommissionFirstDeposit(member, depositAmount); err != nil {
				return internalServerError(err)
			}
		}
	}
	return err
}

func SetFirstDepositBonus(repo repository.AccountingRepository, isFirstDeposit bool, req model.UserFirstDepositCreateRequest) error {

	// *** MUST CHECK isFirstDeposit := s.repoBanking.IsFirstDeposit(userId) BEFORE CALL THIS FUNC ***
	if !isFirstDeposit {
		return nil
	}

	// check al + affiliate
	member, err := repo.GetMemberById(req.UserId)
	if err != nil {
		return badRequest("Invalid Member")
	}

	// Set IsFirstDeposit
	var createFirstDeposit model.UserFirstDepositCreateBody
	createFirstDeposit.UserId = member.Id
	createFirstDeposit.TransferAt = req.TransferAt
	createFirstDeposit.Amount = req.Amount
	createFirstDeposit.Remark = req.Remark
	if _, err := repo.SetUserFirstDeposit(createFirstDeposit, req.TransactionId); err != nil {
		log.Println("SetFirstDepositBonus.SetUserFirstDeposit", err)
	}

	if err := UserFirstDepositCommission(repo, *member, req.Amount); err != nil {
		log.Println("SetFirstDepositBonus.UserFirstDepositCommission", err)
		webhookLogMessage := fmt.Sprintf("UserFirstDepositCommission, ERROR: %s", err)
		return errors.New(webhookLogMessage)
	}

	// [********] ไม่มีโบนัสฝากครั้งแรก
	// [turnOver] ตอนฝากละได้โบนัส
	// actionAt := time.Now()
	// bonusAmount := depositAmount
	// turnOverAmount := depositAmount
	// var createBody model.TurnoverUserStatementCreateBody
	// createBody.UserId = userId
	// createBody.TypeId = model.TURNOVER_TYPE_PROMOTION_FIRST_DEPOSIT
	// createBody.RefTypeId = transacitonId
	// createBody.Name = model.TURNOVER_CATE_FIRST_DEPOSIT
	// createBody.PromotionName = "โบนัสฝากครั้งแรก"
	// createBody.BonusAmount = bonusAmount
	// if bonusAmount > 0 {
	// 	createBody.StatusId = model.TURNOVER_STATEMENT_STATUS_PENDING
	// } else {
	// 	createBody.StatusId = model.TURNOVER_STATEMENT_STATUS_COMPLETED
	// }
	// createBody.StartTurnAmount = turnOverAmount
	// createBody.StartTurnAt = &actionAt
	// createBody.TotalTurnAmount = turnOverAmount
	// if _, err := s.repo.CreateTurnoverUserStatement(createBody); err != nil {
	// 	webhookLogMessage := fmt.Sprintf("CreateTurnoverUserStatement, ERROR: %s", err)
	// 	return errors.New(webhookLogMessage)
	// }

	return nil
}

func UserFirstDepositCommission(repo repository.AccountingRepository, member model.Member, depositAmount float64) error {

	// 'NONE','AFFILIATE','ALLIANCE'
	switch member.UserTypeName {
	case "ALLIANCE":
		if err := AlUpdateCommissionFirstDeposit(repository.NewAllianceRepository(repo.GetDb()), member.Id, depositAmount); err != nil {
			return internalServerError(err)
		}
	default:
		// กันว่าถ้าไม่มีค่าไรเลยให้เป็น AF CreateAffiliateMember
		// ถ้าฝากครั้งแรก จะได้เป็น AFFILIATE
		if err := repo.UpdateGenMemberCodeToAffilate(member.Id); err != nil {
			return internalServerError(err)
		}
		if member.RefBy != nil && *member.RefBy != 0 {
			if err := repo.CreateAffiliateMember(*member.RefBy, member.Id); err != nil {
				return internalServerError(err)
			}
			// WithoutSetting
			if err := UpdateMemberFirstDeposit(repository.NewAffiliateRepository(repo.GetDb()), member, depositAmount); err != nil {
				return internalServerError(err)
			}
			// WithSettingCheck
			if err := UpdateCommissionFirstDeposit(repository.NewAffiliateRepository(repo.GetDb()), member, depositAmount); err != nil {
				return internalServerError(err)
			}
		}
	}
	return nil
}

func (s *accountingService) CheckDepositAccountMoveTransaction(checkAccount model.CheckDepositAccountMoveTransaction) (bool, error) {

	var check bool
	totalUser, _ := s.repo.CheckUserByBankAccountNumber(checkAccount.FromAccountNumber, checkAccount.FromAccountBankId)
	check = false
	if totalUser == 0 {
		// ถ้าไม่มี user ที่ตรง กับ bank.id  และ acountnumber จะให้ทำการ ดูก่อนว่าเป็น โยกเงินหรือไม่ถ้าไม่ตรงก็ทำต่อ
		totalMoveTrans, _ := s.repo.FindMoveTransaction(checkAccount)
		if totalMoveTrans > 0 {
			check = true
			return check, nil
		} else {
			check = false
			return check, nil
		}
	}

	return check, nil
}

func (s *accountingService) createBankStatementFromExternalStatement(data model.ExternalStatement) error {

	systemAccount, err := s.repo.GetBankAccountByExternalId(data.BankAccountId)
	if err != nil {
		log.Println(err)
		return badRequest("Invalid Bank Account")
	}

	_, errOldStatement := s.repo.GetWebhookStatementByExternalId(data.Id)
	if errOldStatement != nil && errOldStatement.Error() == recordNotFound {
		var bodyCreateState model.BankStatementCreateBody
		bodyCreateState.AccountId = systemAccount.Id
		bodyCreateState.ExternalId = &data.Id
		if data.TxnCode == "X1" || data.TxnCode == "CR" {
			bodyCreateState.StatementTypeId = model.STATEMENT_TYPE_TRANSFER_IN
			bodyCreateState.Amount = data.Amount
		} else if data.TxnCode == "X2" || data.TxnCode == "DR" {
			bodyCreateState.StatementTypeId = model.STATEMENT_TYPE_TRANSFER_OUT
			bodyCreateState.Amount = data.Amount * -1
		} else {
			// s.CreateWebhookLog("unsupport TxnCode found, WebhookStatement:", helper.StructJson(struct{ data model.WebhookStatement }{data}))
			return badRequest("Invalid TxnCode")
		}

		bank, err := s.GetBankFromWebhook(data.Info)
		if err != nil {
			log.Println("CreateBankStatementFromExternalStatement.GetBankFromWebhook", err)
		}
		bodyCreateState.FromBankId = bank.Id
		accountNumber, _ := s.GetAccountNoFromWebhook(bank.Code, data.Info)
		bodyCreateState.FromAccountNumber = accountNumber

		bodyCreateState.Detail = data.TxnDescription + " " + data.Info
		// tempDateTime := strings.Replace(data.DateTime, " ", "T", 1) + "Z"
		// unixTimeUTC := time.Unix(**********, 0)               //gives unix time stamp in utc
		// unitTimeInRFC3339 := unixTimeUTC.Format(time.RFC3339) // converts utc time to RFC3339 format
		// timeParseLayout := "DateTime"
		tempDateTime, _ := time.Parse("2006-01-02 15:04:05", data.DateTime)
		// log.Println(data.DateTime, tempDateTime)
		bodyCreateState.TransferAt = tempDateTime
		bodyCreateState.StatementStatusId = model.STATEMENT_STATUS_PENDING

		insertId, err := s.repo.CreateWebhookStatement(bodyCreateState)
		if err != nil {
			return internalServerError(err)
		}
		bodyCreateState.Id = *insertId // make sure

		// ===========================================================

		if err := s.matchBankStatementFromExternalStatement(*systemAccount, bodyCreateState); err != nil {
			log.Println("ERROR, createBankStatementFromExternalStatement.matchBankStatementFromExternalStatement", err.Error())
			return internalServerError(err)
		}
	}

	return nil
}

func (s *accountingService) matchBankStatementFromExternalStatement(systemAccount model.BankAccount, bodyCreateState model.BankStatementCreateBody) error {

	actionAt := time.Now()
	// Auto Match if posible user == 1
	statement, err := s.repo.GetBankStatementById(bodyCreateState.Id)
	if err != nil {
		return nil
	}
	var reqPosibleList model.GetPossibleOwnersRequest
	reqPosibleList.FromBankId = statement.FromBankId
	reqPosibleList.ToBankId = systemAccount.BankId
	reqPosibleList.FromAccountNumber = statement.FromAccountNumber
	records, total, err := s.repo.GetPossibleOwnersByStatementId(reqPosibleList)
	if err != nil {
		return nil
	}
	if total == 1 {
		for _, possibleOwner := range records {
			log.Println("Has Possible Owner", helper.StructJson(possibleOwner))
			// Auto create transaction
			if bodyCreateState.StatementTypeId == model.STATEMENT_TYPE_TRANSFER_IN {
				// AutoDeposit
				// if systemAccount.AutoCreditFlag == "auto" {
				admin := model.ApprovedByAdmin{
					Id: 0,
				}
				// matchBankStatementFromExternalStatement.createAutoDepositTransaction#2.transId
				_, err := s.createAutoDepositTransaction(possibleOwner, bodyCreateState, admin)
				if err != nil {
					log.Println("CreateBankStatementFromExternalStatement.CreateAutoDepositTransaction", err)
					return nil
				}
				var statementMatchRequest model.BankStatementMatchRequest
				statementMatchRequest.UserId = possibleOwner.Id
				statementMatchRequest.ConfirmedAt = actionAt
				var setIdAuto int64 = 0
				statementMatchRequest.ConfirmedByAdminId = &setIdAuto
				if err := s.SetStatementOwnerMatched(statement.Id, statementMatchRequest, model.USE_MATCH_NOTI); err != nil {
					log.Println("CreateBankStatementFromExternalStatement.SetStatementOwnerMatched", err)
					return nil
				}
			}

			if bodyCreateState.StatementTypeId == model.STATEMENT_TYPE_TRANSFER_OUT {
				// Auto ignore, no need to match
				var statementMatchRequest model.BankStatementMatchRequest
				statementMatchRequest.ConfirmedAt = actionAt
				var setIdAuto int64 = 0
				statementMatchRequest.ConfirmedByAdminId = &setIdAuto
				if err := s.IgnoreStatementOwner(statement.Id, statementMatchRequest); err != nil {
					log.Println("CreateBankStatementFromExternalStatement.IgnoreStatementOwner", err)
					return nil
				}
			}
		}
	} else {
		log.Println("matchBankStatementFromExternalStatement.total", total)
	}

	return nil
}

func (s *accountingService) createAutoDepositTransaction(possibleOwner model.Member, bodyCreateState model.BankStatementCreateBody, admin model.ApprovedByAdmin) (*int64, error) {

	var transId *int64
	var err error

	// CreateBankStatementFromWebhookAndAuto.createAutoDepositTransaction#1.transId
	// matchBankStatementFromExternalStatement.createAutoDepositTransaction#2.transId
	// CreateBankStatementFromWebhookAndAutoSmsMode.createAutoDepositTransaction#3.transId

	if bodyCreateState.StatementTypeId == model.STATEMENT_TYPE_TRANSFER_IN {
		var createDepositBody model.BankTransactionCreateBody
		createDepositBody.MemberCode = possibleOwner.MemberCode
		createDepositBody.TransactionTypeId = model.TRANSACTION_TYPE_DEPOSIT
		createDepositBody.CreditAmount = bodyCreateState.Amount
		createDepositBody.TransferAt = &bodyCreateState.TransferAt
		createDepositBody.CreatedByAdminId = admin.Id
		createDepositBody.IsAutoCredit = true
		createDepositBody.StatementId = &bodyCreateState.Id

		// bodyCreateState.AccountId = systemAccount.Id == createDepositBody.ToAccountId = &systemAccount.Id
		createDepositBody.ToAccountId = &bodyCreateState.AccountId
		transId, err = s.createBankTransaction(createDepositBody)
		if err != nil {
			log.Print(err.Error())
			return nil, internalServerError(err)
		}
		var confirmTransReq model.BankConfirmDepositRequest
		confirmTransReq.ConfirmedAt = time.Now()
		var setIdAuto int64 = 0
		confirmTransReq.ConfirmedByAdminId = &setIdAuto
		confirmTransReq.TransferAt = &bodyCreateState.TransferAt
		// confirmTransReq.BonusAmount = 0
		if err := s.ConfirmDepositTransaction(*transId, confirmTransReq); err != nil {
			log.Print(err.Error())
			return nil, internalServerError(err)
		}
		// no need if IsAutoCredit = true err := s.ConfirmDepositCredit(*transId, confirmTransReq); err != nil {
		// 	log.Print(err.Error())
		// 	return internalServerError(err)
		// }
		// var statementMatchRequest model.BankStatementMatchRequest
		// statementMatchRequest.UserId = possibleOwner.Id
		// if err := s.MatchStatementOwner(statement.Id, statementMatchRequest); err != nil {
		// 	// return internalServerError(err)
		// 	return nil
		// }
	}

	return transId, nil
}

func (s *accountingService) CreateDepositTransaction(possibleOwner model.Member, bodyCreateState model.BankStatementCreateBody, admin model.ApprovedByAdmin) (*int64, error) {

	// MatchBankStatementFromFastbankStatementNotAuto.CreateDepositTransaction#1.transId

	var tranId *int64
	var err error
	if bodyCreateState.StatementTypeId == model.STATEMENT_TYPE_TRANSFER_IN {
		var createDepositBody model.BankTransactionCreateBody
		createDepositBody.MemberCode = possibleOwner.MemberCode
		createDepositBody.TransactionTypeId = model.TRANSACTION_TYPE_DEPOSIT
		createDepositBody.CreditAmount = bodyCreateState.Amount
		createDepositBody.TransferAt = &bodyCreateState.TransferAt
		createDepositBody.CreatedByAdminId = admin.Id
		createDepositBody.TransactionStatusId = model.TRANS_STATUS_PENDING
		createDepositBody.IsAutoCredit = false
		createDepositBody.StatementId = &bodyCreateState.Id

		// bodyCreateState.AccountId = systemAccount.Id == createDepositBody.ToAccountId = &systemAccount.Id
		createDepositBody.ToAccountId = &bodyCreateState.AccountId
		tranId, err = s.createBankTransaction(createDepositBody)
		if err != nil {
			log.Print(err.Error())
			return nil, internalServerError(err)
		}
		// var confirmTransReq model.BankConfirmDepositRequest
		// confirmTransReq.ConfirmedAt = time.Now()
		// confirmTransReq.ConfirmedByAdminId = 0
		// confirmTransReq.ConfirmedByUsername = "อัตโนมัติ"
		// confirmTransReq.TransferAt = &bodyCreateState.TransferAt
		// // confirmTransReq.BonusAmount = 0
		// if err := s.ConfirmDepositTransaction(*transId, confirmTransReq); err != nil {
		// 	log.Print(err.Error())
		// 	return nil, internalServerError(err)
		// }
		// if err := s.ConfirmDepositCredit(*transId, confirmTransReq); err != nil {
		// 	log.Print(err.Error())
		// 	return internalServerError(err)
		// }
		// var statementMatchRequest model.BankStatementMatchRequest
		// statementMatchRequest.UserId = possibleOwner.Id
		// if err := s.MatchStatementOwner(statement.Id, statementMatchRequest); err != nil {
		// 	// return internalServerError(err)
		// 	return nil
		// }
	}

	return tranId, nil
}

func (s *accountingService) CreateDepositTransactionNoOwner(bodyCreateState model.BankStatementCreateBody, admin model.ApprovedByAdmin, transType int64) (*int64, error) {

	startProcess := time.Now()
	result := int64(0)

	if bodyCreateState.StatementTypeId == model.STATEMENT_TYPE_TRANSFER_IN {
		var createDepositBody model.BankTransactionNoOwnerCreateBody
		createDepositBody.TransactionTypeId = model.TRANSACTION_TYPE_DEPOSIT
		createDepositBody.CreditAmount = bodyCreateState.Amount
		createDepositBody.TransferAt = &bodyCreateState.TransferAt
		createDepositBody.CreatedByAdminId = admin.Id
		createDepositBody.IsAutoCredit = false
		createDepositBody.ToAccountId = &bodyCreateState.AccountId
		createDepositBody.StatementId = bodyCreateState.Id
		createDepositBody.FromAccountNumber = bodyCreateState.FromAccountNumber
		createDepositBody.FromBankId = bodyCreateState.FromBankId
		createDepositBody.TransactionStatusId = transType
		// ผมยังไม่ได้แก้ reateDepositBody.TransactionStatusId ให้
		tranId, err := s.CreateBankTransactionNoOwner(createDepositBody)
		if err != nil {
			log.Print("CreateDepositTransactionNoOwner.CreateBankTransactionNoOwner.ERROR=", err)
			return nil, internalServerError(err)
		}
		result = *tranId
		endProcess := time.Now()
		elapsed := endProcess.Sub(startProcess)
		elapsedSeconds := elapsed.Seconds()
		timeElapsed := fmt.Sprintf("%.2f", elapsedSeconds)

		// ===========================================================
		// AddTo BankPendingRecord -> รายการฝาก
		dashboardRepo := repository.NewDashboardRepository(s.shareDb)
		if err := AddBankPendingRecordFromDeposit(dashboardRepo, *tranId); err != nil {
			log.Println("CreateDepositTransactionNoOwner.AddBankPendingRecordFromDeposit", err)
		}

		var externalNoti model.NotifyExternalNotificationRequest
		externalNoti.TypeNotify = model.IsDepositBeforeCredit
		externalNoti.TransId = tranId
		externalNoti.Amount = bodyCreateState.Amount
		externalNoti.MemberCode = "ไม่ทราบ"
		externalNoti.UserCredit = 0
		externalNoti.ConfirmedByAdminId = admin.Id
		externalNoti.TimerCounter = timeElapsed
		if err := s.notiService.ExternalNotification(externalNoti); err != nil {
			log.Println("FailedNotify", err)
		}
	}
	return &result, nil
}

func (s *accountingService) UpdateDepositTransactionOwner(possibleOwner model.Member, bodyCreateState model.BankStatementCreateBody, admin model.ApprovedByAdmin) (*int64, error) {

	var tranId *int64
	var err error

	if bodyCreateState.StatementTypeId == model.STATEMENT_TYPE_TRANSFER_IN {
		var createDepositBody model.BankTransactionUpdateOwnerBody
		createDepositBody.MemberCode = possibleOwner.MemberCode
		createDepositBody.TransactionTypeId = model.TRANSACTION_TYPE_DEPOSIT
		createDepositBody.CreatedByAdminId = admin.Id
		createDepositBody.TransactionStatusId = model.TRANS_STATUS_PENDING

		// bodyCreateState.AccountId = systemAccount.Id == createDepositBody.ToAccountId = &systemAccount.Id
		tranId, err = s.UpdateBankTransactionOwner(bodyCreateState.Id, createDepositBody)
		if err != nil {
			log.Print(err.Error())
			return nil, internalServerError(err)
		}

	}

	return tranId, nil
}

func (s *accountingService) createBankTransaction(data model.BankTransactionCreateBody) (*int64, error) {

	// createAutoDepositTransaction
	// CreateDepositTransaction

	var transId *int64
	var body model.BankTransactionCreateBody
	body.TransferAt = data.TransferAt
	body.CreatedByAdminId = data.CreatedByAdminId
	if body.TransactionStatusId == 0 {
		body.TransactionStatusId = model.TRANS_STATUS_PENDING
	}

	if data.TransactionTypeId == model.TRANSACTION_TYPE_DEPOSIT {
		member, err := s.repo.GetUserByMemberCode(data.MemberCode)
		if err != nil {
			log.Println(err)
			return nil, badRequest("Invalid Member code")
		}
		bank, err := s.repo.GetBankByCode(member.BankCode)
		if err != nil {
			log.Println(err)
			return nil, badRequest("Invalid User Bank")
		}
		body.MemberCode = *member.MemberCode
		body.UserId = member.Id
		body.CreditAmount = data.CreditAmount
		body.TransactionTypeId = data.TransactionTypeId
		body.DepositChannel = data.DepositChannel
		body.OverAmount = data.OverAmount
		body.IsAutoCredit = data.IsAutoCredit

		body.FromBankId = &bank.Id
		body.FromAccountName = &member.Fullname
		body.FromAccountNumber = &member.BankAccount
		if data.ToAccountId == nil {
			return nil, badRequest("Input Bank Account")
		}
		toAccount, err := s.repo.GetDepositAccountById(*data.ToAccountId)
		if err != nil {
			log.Println(err)
			return nil, badRequest("Invalid Bank Account")
		}
		body.ToAccountId = &toAccount.Id
		body.ToBankId = &toAccount.BankId
		body.ToAccountName = &toAccount.AccountName
		body.ToAccountNumber = &toAccount.AccountNumber

		body.PromotionId = data.PromotionId
		body.StatementId = data.StatementId

		if insertId, err := s.repo.InsertBankTransaction(body); err == nil {
			transId = insertId
		} else {
			return nil, internalServerError(err)
		}
	} else if data.TransactionTypeId == model.TRANSACTION_TYPE_WITHDRAW {
		member, err := s.repo.GetUserByMemberCode(data.MemberCode)
		if err != nil {
			log.Println(err)
			return nil, badRequest("Invalid Member code")
		}
		bank, err := s.repo.GetBankByCode(member.BankCode)
		if err != nil {
			log.Println(err)
			return nil, badRequest("Invalid User Bank")
		}
		body.MemberCode = *member.MemberCode
		body.UserId = member.Id
		body.CreditAmount = data.CreditAmount
		body.TransactionTypeId = data.TransactionTypeId

		// Withdraw SystemAccount is not requried
		if data.FromAccountId != nil {
			fromAccount, err := s.repo.GetWithdrawAccountById(*data.FromAccountId)
			if err != nil {
				log.Println(err)
				return nil, badRequest("Invalid Bank Account")
			}
			body.FromAccountId = &fromAccount.Id
			body.FromBankId = &fromAccount.BankId
			body.FromAccountName = &fromAccount.AccountName
			body.FromAccountNumber = &fromAccount.AccountNumber
		}

		body.ToBankId = &bank.Id
		body.ToAccountName = &member.Fullname
		body.ToAccountNumber = &member.BankAccount
		body.TransactionStatusId = model.TRANS_STATUS_PENDING // == pending_credit

		if insertId, err := s.repo.CreateBankWithdrawTransaction(body); err == nil {
			transId = insertId
		} else {
			return nil, internalServerError(err)
		}
	} else {
		return nil, badRequest("Invalid Transfer Type")
	}

	return transId, nil
}

func (s *accountingService) CreateBankTransactionNoOwner(data model.BankTransactionNoOwnerCreateBody) (*int64, error) {

	var transId *int64
	var body model.BankTransactionNoOwnerCreateBody
	body.TransferAt = data.TransferAt
	body.CreatedByAdminId = data.CreatedByAdminId
	body.TransactionStatusId = data.TransactionStatusId
	body.StatementId = data.StatementId
	body.FromAccountNumber = data.FromAccountNumber
	body.FromBankId = data.FromBankId

	if data.TransactionTypeId == model.TRANSACTION_TYPE_DEPOSIT {

		body.CreditAmount = data.CreditAmount
		body.TransactionTypeId = data.TransactionTypeId
		body.DepositChannel = data.DepositChannel
		body.OverAmount = data.OverAmount
		body.IsAutoCredit = data.IsAutoCredit
		if data.ToAccountId == nil {
			return nil, badRequest("Input Bank Account")
		}
		toAccount, err := s.repo.GetDepositAccountById(*data.ToAccountId)
		if err != nil {
			log.Println(err)
			return nil, badRequest("Invalid Bank Account")
		}
		body.ToAccountId = &toAccount.Id
		body.ToBankId = &toAccount.BankId
		body.ToAccountName = &toAccount.AccountName
		body.ToAccountNumber = &toAccount.AccountNumber
		body.PromotionId = data.PromotionId
		body.StatementId = data.StatementId
		if insertId, err := s.repo.CreateBankDepositTransactionNoOwner(body); err == nil {
			transId = insertId
		} else {
			return nil, internalServerError(err)
		}
	}

	return transId, nil
}

func (s *accountingService) UpdateBankTransactionOwner(statementId int64, data model.BankTransactionUpdateOwnerBody) (*int64, error) {
	var transId *int64
	var body model.BankTransactionUpdateOwnerBody
	// body.CreatedByAdminId = data.CreatedByAdminId
	// body.CreatedByUsername = data.CreatedByUsername
	body.TransactionStatusId = model.TRANS_STATUS_PENDING

	if data.TransactionTypeId == model.TRANSACTION_TYPE_DEPOSIT {

		member, err := s.repo.GetUserByMemberCode(data.MemberCode)
		if err != nil {
			log.Println(err)
			return nil, badRequest("Invalid Member code")
		}

		bank, err := s.repo.GetBankByCode(member.BankCode)
		if err != nil {
			log.Println(err)
			return nil, badRequest("Invalid User Bank")
		}
		body.MemberCode = *member.MemberCode
		body.UserId = member.Id

		body.TransactionTypeId = data.TransactionTypeId

		body.FromBankId = &bank.Id
		body.FromAccountName = &member.Fullname
		body.FromAccountNumber = &member.BankAccount

		body.PromotionId = data.PromotionId

		insertId, err := s.repo.UpdateDepositTransactionOwner(statementId, body)
		if err == nil {
			transId = insertId
		} else {
			return nil, internalServerError(err)
		}

	}

	return transId, nil
}

func (s *accountingService) SetStatementOwnerMatched(id int64, req model.BankStatementMatchRequest, accessNoti string) error {

	startProcess := time.Now()
	statement, err := s.repo.GetBankStatementById(id)
	if err != nil {
		return internalServerError(err)
	}
	if statement.StatementStatusId != model.STATEMENT_STATUS_PENDING {
		return badRequest("Statement is not pending")
	}
	member, err := s.repo.GetMemberById(req.UserId)
	if err != nil {
		return badRequest("Invalid Member")
	}
	jsonBefore, _ := json.Marshal(statement)

	// TransAction
	var createBody model.CreateBankStatementActionBody
	createBody.StatementId = statement.Id
	createBody.UserId = member.Id
	createBody.ActionType = "CONFIRMED"
	createBody.AccountId = statement.AccountId
	createBody.JsonBefore = string(jsonBefore)
	createBody.ConfirmedAt = req.ConfirmedAt
	createBody.ConfirmedByAdminId = req.ConfirmedByAdminId
	if err := s.repo.CreateStatementAction(createBody); err == nil {
		var body model.BankStatementUpdateBody
		body.StatementStatusId = model.STATEMENT_STATUS_CONFIRMED
		body.Id = statement.Id
		if err := s.repo.UpdateBankStatementStatus(body); err != nil {
			return internalServerError(err)
		}
	} else {
		return internalServerError(err)
	}

	if accessNoti != model.USE_ENDING_NOTI {
		endProcess := time.Now()
		elapsed := endProcess.Sub(startProcess)
		elapsedSeconds := elapsed.Seconds()
		timeElapsed := fmt.Sprintf("%.2f", elapsedSeconds)

		var externalNoti model.NotifyExternalNotificationRequest
		externalNoti.TypeNotify = model.IsDepositAfterCredit
		externalNoti.Amount = statement.Amount
		externalNoti.MemberCode = member.MemberCode
		externalNoti.UserCredit = member.Credit + statement.Amount
		externalNoti.ConfirmedByAdminId = *req.ConfirmedByAdminId
		externalNoti.TimerCounter = timeElapsed
		externalNoti.TransferDateTime = statement.TransferAt.Add(time.Hour * 7).Format("2006-01-02 15:04:05")
		externalNoti.ActionTime = time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02 15:04:05")
		if err := s.notiService.ExternalNotification(externalNoti); err != nil {
			log.Println("FailedNotify", err)
		}
	}
	return nil
}

func (s *accountingService) IgnoreStatementOwner(id int64, req model.BankStatementMatchRequest) error {

	statement, err := s.repo.GetBankStatementById(id)
	if err != nil {
		return internalServerError(err)
	}
	if statement.StatementStatusId != model.STATEMENT_STATUS_PENDING {
		return badRequest("Statement is not pending")
	}

	var body model.BankStatementUpdateBody
	body.StatementStatusId = model.STATEMENT_STATUS_IGNORED
	jsonBefore, _ := json.Marshal(statement)

	// TransAction
	var createBody model.CreateBankStatementActionBody
	createBody.StatementId = statement.Id
	createBody.ActionType = "IGNORED"
	createBody.AccountId = statement.AccountId
	createBody.JsonBefore = string(jsonBefore)
	createBody.ConfirmedAt = req.ConfirmedAt
	createBody.ConfirmedByAdminId = req.ConfirmedByAdminId
	if err := s.repo.CreateStatementAction(createBody); err == nil {
		if err := s.repo.IgnoreStatementOwner(id, body); err != nil {
			return internalServerError(err)
		}
	} else {
		return internalServerError(err)
	}
	return nil
}

func (s *accountingService) ConfirmDepositTransaction(id int64, req model.BankConfirmDepositRequest) error {

	record, err := s.repo.GetBankTransactionById(id)
	if err != nil {
		return internalServerError(err)
	}
	if record.TransactionStatusId != model.TRANS_STATUS_PENDING {
		return badRequest("Transaction is not pending")
	}
	if record.TransactionTypeId != model.TRANSACTION_TYPE_DEPOSIT && record.TransactionTypeId != model.TRANSACTION_TYPE_BONUS {
		return badRequest("Transaction is not deposit")
	}
	jsonBefore, _ := json.Marshal(record)

	var updateData model.BankDepositTransactionConfirmBody
	updateData.TransactionStatusId = model.TRANS_STATUS_DEPOSIT_PENDING_CREDIT
	updateData.ConfirmedAt = req.ConfirmedAt
	updateData.ConfirmedByAdminId = req.ConfirmedByAdminId
	// updateData.BonusAmount = req.BonusAmount

	var createBody model.CreateBankTransactionActionBody
	createBody.ActionKey = fmt.Sprintf("DCF_STATE#%d", record.Id)
	createBody.TransactionId = record.Id
	createBody.UserId = record.UserId
	createBody.TransactionTypeId = record.TransactionTypeId
	createBody.FromAccountId = record.FromAccountId
	createBody.ToAccountId = record.ToAccountId
	createBody.JsonBefore = string(jsonBefore)
	if req.TransferAt == nil {
		createBody.TransferAt = record.TransferAt
	} else {
		TransferAt := req.TransferAt
		createBody.TransferAt = TransferAt
		updateData.TransferAt = *TransferAt
	}
	if req.SlipUrl != nil {
		createBody.SlipUrl = *req.SlipUrl
	}
	createBody.CreditAmount = record.CreditAmount
	if req.BonusAmount != nil {
		createBody.BonusAmount = *req.BonusAmount
	}
	createBody.ConfirmedAt = req.ConfirmedAt
	createBody.ConfirmedByAdminId = req.ConfirmedByAdminId
	if actionId, err := s.repo.CreateTransactionAction(createBody); err == nil {
		// do nothing ?
		if err := s.repo.ConfirmPendingDepositTransaction(id, updateData); err != nil {
			if err := s.repo.RollbackTransactionAction(*actionId); err == nil {
				return internalServerError(err)
			}
			return internalServerError(err)
		}
	} else {
		return internalServerError(err)
	}
	return nil
}

func (s *accountingService) ConfirmDepositCredit(id int64, req model.BankConfirmDepositRequest) error {

	record, err := s.repo.GetBankTransactionById(id)
	if err != nil {
		return internalServerError(err)
	}
	if record.TransactionTypeId != model.TRANSACTION_TYPE_DEPOSIT && record.TransactionTypeId != model.TRANSACTION_TYPE_BONUS {
		return badRequest("Transaction is not deposit")
	}
	if record.TransactionStatusId != model.TRANS_STATUS_DEPOSIT_PENDING_CREDIT {
		return badRequest("Transaction is not pending")
	}
	jsonBefore, _ := json.Marshal(record)

	var updateData model.BankDepositTransactionConfirmBody
	updateData.TransactionStatusId = model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED

	updateData.ConfirmedAt = req.ConfirmedAt
	updateData.ConfirmedByAdminId = req.ConfirmedByAdminId
	if req.BonusAmount != nil {
		updateData.BonusAmount = *req.BonusAmount
		record.BonusAmount = *req.BonusAmount
	}

	var createBody model.CreateBankTransactionActionBody
	createBody.ActionKey = fmt.Sprintf("DCF_CREDIT#%d", record.Id)
	createBody.TransactionId = record.Id
	createBody.UserId = record.UserId
	createBody.TransactionTypeId = record.TransactionTypeId
	createBody.FromAccountId = record.FromAccountId
	createBody.ToAccountId = record.ToAccountId
	createBody.JsonBefore = string(jsonBefore)
	if req.TransferAt == nil {
		createBody.TransferAt = record.TransferAt
	} else {
		TransferAt := req.TransferAt
		createBody.TransferAt = TransferAt
		updateData.TransferAt = *TransferAt
	}
	if req.SlipUrl != nil {
		createBody.SlipUrl = *req.SlipUrl
	}
	createBody.CreditAmount = record.CreditAmount
	if req.BonusAmount != nil {
		createBody.BonusAmount = *req.BonusAmount
	}
	createBody.ConfirmedAt = req.ConfirmedAt
	createBody.ConfirmedByAdminId = req.ConfirmedByAdminId

	if _, err := s.repo.CreateTransactionAction(createBody); err == nil {
		log.Println("ConfirmPendingTransaction updateData:", helper.StructJson(updateData))
		if err := s.increaseMemberCreditFromDeposit(record.UserId, record.CreditAmount, "ฝากเงิน"); err != nil {
			return internalServerError(err)
		}
		if record.BonusAmount > 0 {
			if err := s.increaseMemberCreditFromBonus(record.UserId, record.CreditAmount, "ได้รับโบนัสจากการฝากเงิน"); err != nil {
				return internalServerError(err)
			}
		}
		if err := s.repo.ConfirmPendingCreditDepositTransaction(id, updateData); err != nil {
			return internalServerError(err)
		}
	} else {
		return internalServerError(err)
	}
	return nil
}

func (s *accountingService) increaseMemberCreditFromDeposit(userId int64, creditAmount float64, info string) error {

	StatementTypeId, err := s.repo.GetMemberStatementTypeByCode("deposit")
	if err != nil {
		return badRequest("Invalid Type")
	}

	var body model.MemberStatementCreateBody
	body.UserId = userId
	body.StatementTypeId = StatementTypeId.Id
	body.Info = info
	body.Amount = creditAmount
	if err := s.repo.IncreaseMemberCredit(body); err != nil {
		return internalServerError(err)
	}
	return nil
}

func (s *accountingService) increaseMemberCreditFromBonus(userId int64, creditAmount float64, info string) error {

	StatementTypeId, err := s.repo.GetMemberStatementTypeByCode("bonus")
	if err != nil {
		return badRequest("Invalid Type")
	}

	var body model.MemberStatementCreateBody
	body.UserId = userId
	body.StatementTypeId = StatementTypeId.Id
	body.Info = info
	body.Amount = creditAmount
	if err := s.repo.IncreaseMemberCredit(body); err != nil {
		return internalServerError(err)
	}
	return nil
}

func (s *accountingService) GetBankFromWebhookInfo(info string) (*model.BankResponse, error) {

	// sample : "กรุงศรีอยุธยา (BAY) /X123456",
	// log.Print("หาธนาคาร getBankIdFromStatementInformation.kt info:", info)
	infoStr := strings.ToLower(info)
	// log.Print("หาธนาคาร getBankIdFromStatementInformation.kt infoStr:", infoStr)

	var req model.BankListRequest
	banks, err := s.repo.GetBanks(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	//  [P.layer confirm hardcode sms from scb sent the for mat kbnk]
	if strings.Contains(infoStr, "kbnk") {
		infoStr = "kbank"
	}

	for _, bank := range banks.List.([]model.BankResponse) {

		if strings.Contains(infoStr, bank.Code) {
			return &bank, nil
		} else if strings.Contains(infoStr, bank.Name) {
			return &bank, nil
		}
	}
	return nil, badRequest("Bank not found")
}

func (s *accountingService) GetBankFromWebhook(info string) (*model.BankResponse, error) {

	bank, err := s.GetBankFromWebhookInfo(info)
	if err != nil {
		return nil, internalServerError(err)
	}
	return bank, nil
}

func (s *accountingService) GetAccountNoFromWebhook(bankCode string, info string) (string, error) {
	infoStr := strings.ToLower(info)

	if bankCode == "scb" {
		// check smsMode
		if strings.Contains(infoStr, "/x") {
			infoStrings := strings.Split(infoStr, "/x")
			if len(infoStrings) > 1 && len(infoStrings[1]) >= 6 {
				possibleAccountNumber, err := s.GetAnyAccountNoNumber(infoStrings[1])
				if possibleAccountNumber != "" {
					return possibleAccountNumber, nil
				}
				if err != nil {
					// Get the last 6 characters instead
					length := len(infoStrings[1])
					if length >= 6 {
						return infoStrings[1][length-6:], nil
					}
					return infoStrings[1], nil
				}
			}
		} else {
			infoStrings := strings.Split(infoStr, " x")
			if len(infoStrings) > 1 && len(infoStrings[1]) >= 4 {
				possibleAccountNumber, err := s.GetAnyAccountNoNumber(infoStrings[1])
				if possibleAccountNumber != "" {
					return possibleAccountNumber, nil
				}
				if err != nil {
					// Get the last 6 characters instead
					length := len(infoStrings[1])
					if length >= 6 {
						return infoStrings[1][length-6:], nil
					}
					return infoStrings[1], nil
				}
			}
		}

	} else {
		// other still use /x
		infoStrings := strings.Split(infoStr, "/x")
		if len(infoStrings) > 1 && len(infoStrings[1]) >= 6 {
			possibleAccountNumber, err := s.GetAnyAccountNoNumber(infoStrings[1])
			if possibleAccountNumber != "" {
				return possibleAccountNumber, nil
			}
			if err != nil {
				// Get the last 6 characters instead
				length := len(infoStrings[1])
				if length >= 6 {
					return infoStrings[1][length-6:], nil
				}
				return infoStrings[1], nil
			}
		}
	}
	return "", badRequest("AccountNo not found")
}

func (s *accountingService) ExtractAccountNumber(inputStr string, bankCode string) (string, error) {
	fmt.Print("ExtractAccountNumber inputStr:", inputStr, " bankCode:", bankCode)
	// [Check ว่ามีบชตรงกับอันไหนเอา เอาเลขไปเช็ค]
	// [********] ตามพี่เลย์มีมาให้ bankCodeToBankNum  เลขไม่ครบเลยต้องหา วิธีดักเอง
	bankCode = strings.ToUpper(bankCode)
	var bankCodeToBankNum = map[string]string{
		"KBANK":   "004",
		"SCB":     "014",
		"BBL":     "002",
		"BAY":     "025",
		"KTB":     "006",
		"TBANK":   "001",
		"TTB":     "011",
		"GSB":     "030",
		"BAAC":    "034",
		"KKP":     "069",
		"KK":      "001",
		"GHB":     "033",
		"UOB":     "024",
		"LH":      "001",
		"CIMB":    "022",
		"HSBC":    "023",
		"ICBC":    "001",
		"ISBT":    "001",
		"TISCO":   "001",
		"CITI":    "001",
		"SCBT":    "001",
		"TRUE":    "044",
		"UNKNOWN": "000",
	}
	code, exists := bankCodeToBankNum[bankCode]
	if !exists {
		return "", nil
	}

	// [Case true ต้อง replace - ก่อน แล้วเอาแต่ตัวเลข]
	if bankCode == "TRUE" || bankCode == "unknown" {
		//replace remove - from inputStr
		cleanedNumber := strings.ReplaceAll(inputStr, "-", "")
		trueGetter, err := s.GetAnyAccountNoNumber(cleanedNumber)
		if trueGetter != "" {
			return trueGetter, nil
		} else {
			return "UNMATCH", err
		}
	}

	// [Case อื่นๆ]
	// [Match เอา pattern ตาม code มาเช็ค ถ้าเจอ ตัดเอาเลขไปเช็ค เช่น kbank 004-]
	regexPattern := code + `-\d+`
	regex := regexp.MustCompile(regexPattern)
	matches := regex.FindStringSubmatch(inputStr)
	if len(matches) > 0 {
		accountNumber := strings.Split(matches[0], "-")[1]
		return accountNumber, nil
	} else {
		// [case ที่ ไม่มีพวกเลข xxx- ส่งมาให้ใน matches จะมาเลข ด้วยหาจาก pattern เก่าก่อน]
		accountNumber, err := s.GetAccountNoFromWebhook(bankCode, inputStr)
		if accountNumber != "" {

			return accountNumber, nil
		}
		// [case ที่ ไม่มีพวกเลข xxx- ส่งมาให้ใน matches จะมาเลข ด้วยหาจาก pattern และถ้าไม่เจอ อีก จะเอา เลขที่ยาวติดกันเกิน 4 ตัวเป็นต้นไปใช้]
		if accountNumber == "" {
			var possibleAccountNumber string
			possibleAccountNumber, err = s.GetAnyAccountNoNumber(inputStr)
			if possibleAccountNumber != "" {
				return possibleAccountNumber, nil
			} else {
				return "UNMATCH", err
			}
		}
		if err != nil {
			return "", err
		}

	}

	return "", nil
}

func (s *accountingService) ThisShouldWorkEveryExtractAccountNumber(inputStr string, bankCode string) (string, error) {
	// CleanUp
	cleanedNumber := strings.ReplaceAll(inputStr, "-", "")
	cleanedNumber = strings.ReplaceAll(cleanedNumber, "x", "")
	// Extract any number in len more than 4
	extractedNumber, err := s.GetAnyAccountNoNumber(cleanedNumber)
	if err != nil {
		// Error occurred during extraction
		return "", err
	}

	// If an account number is extracted, return it
	if extractedNumber != "" {
		return extractedNumber, nil
	}

	// No account number was extracted
	return "UNMATCH", nil
}

func (s *accountingService) GetAnyAccountNoNumber(inputStr string) (string, error) {

	if strings.Contains(inputStr, "sms :") || strings.Contains(inputStr, "SMS :") {
		return "UNMATCH", nil
	}

	// Define the regular expression pattern to match numeric sequences with more than 3 digits
	re := regexp.MustCompile(`\d{4,}`)

	// Find all matches in the input string
	matches := re.FindAllString(inputStr, -1)

	if matches == nil {
		return "", nil
	}

	// Join the matched numeric sequences into a single string
	result := ""
	for _, match := range matches {
		result += match
	}

	fmt.Println("GetAnyAccountNoNumber result:", result)
	return result, nil
}

func (s *accountingService) CreateWebhookLog(logType string, jsonRequest string) (*int64, error) {

	var body model.WebhookLogCreateBody
	body.JsonRequest = jsonRequest
	body.JsonPayload = "{}"
	body.LogType = logType
	body.Status = "pending_webook"

	insertId, err := s.repo.CreateWebhookLog(body)
	if err != nil {
		return nil, internalServerError(err)
	}
	return insertId, nil
}

func (s *accountingService) SetSuccessWebhookLog(id int64, jsonPayload string) error {

	var body model.WebhookLogUpdateBody
	body.JsonPayload = jsonPayload
	body.Status = "success"
	if err := s.repo.UpdateWebhookLog(id, body); err != nil {
		return internalServerError(err)
	}
	return nil
}

func (s *accountingService) SetFailedWebhookLog(id int64, jsonPayload string) error {

	var body model.WebhookLogUpdateBody
	body.JsonPayload = jsonPayload
	body.Status = "failed"
	if err := s.repo.UpdateWebhookLog(id, body); err != nil {
		return internalServerError(err)
	}
	return nil
}

func (s *accountingService) CreateBotaccountConfig(data model.BotAccountConfigCreateBody) error {

	if err := s.repo.CreateBotaccountConfig(data); err != nil {
		return internalServerError(err)
	}
	return nil
}

func (s *accountingService) SortBankAccountList(req model.DragSortRequest) error {

	if err := s.repo.SortBankAccountList(req); err != nil {
		return internalServerError(err)
	}
	return nil

}
func (s *accountingService) UpdateBankAccountPriority(list model.PriorityWithdrawstructRequest) error {

	if len(list.List) == 0 {
		return badRequest("Invalid List Length")
	}

	for _, item := range list.List {
		if item.AccountPriorityWithdraw == 0 {
			return badRequest("Invalid AccountPriorityWithdraw")
		}
	}

	if err := s.repo.UpdateBankAccountPriority(list); err != nil {
		return internalServerError(err)
	}
	return nil
}

func (s *accountingService) GetWebBankDepositAccount(userId int64) ([]model.WebBankAccountResponse, error) {

	getUser, err := s.repo.GetUserBankIdByUserId(userId)
	if err != nil {
		return nil, internalServerError(err)
	}

	accountBanks, err := s.repo.GetWebBankDepositAccount()
	if err != nil {
		return nil, internalServerError(err)
	}

	// fmt.Println("accountBanks:", helper.StructJson(accountBanks))

	var newAccountBankList []model.WebBankAccountResponse
	for _, accountBankFindAllowBank := range accountBanks {
		if accountBankFindAllowBank.ShowBankDepositOverDueTime == 0 {
			newAccountBankList = append(newAccountBankList, accountBankFindAllowBank)
		} else {
			// getUserTransactionCount, err := s.repo.GetUserTransactionCountDepositTimeByUser(userId, accountBankFindAllowBank.Id)
			getUserTransactionCount, err := s.repo.GetUserTransactionCountDepositTimeByUser(userId)
			if err != nil {
				continue
			} else {
				if getUserTransactionCount.BankDepositTime >= accountBankFindAllowBank.ShowBankDepositOverDueTime && getUserTransactionCount.BankDepositTime <= accountBankFindAllowBank.ShowBankDepositMaxDueTime {
					newAccountBankList = append(newAccountBankList, accountBankFindAllowBank)
				}
			}
		}
	}

	webConfig, err := s.repo.GetConfiguration()
	if err != nil {
		return nil, internalServerError(err)
	}

	var banksId []int64
	for i := range newAccountBankList {
		banksId = append(banksId, newAccountBankList[i].Id)
	}

	getToShowBank, err := s.repo.GetBankAccountShowBankByBankAccountId(banksId)
	if err != nil {
		return nil, internalServerError(err)
	}

	var response []model.WebBankAccountResponse

	for i := range newAccountBankList {
		for j := range getToShowBank {
			if getUser.BankId != getToShowBank[j].BankId {
				continue
			}
			if newAccountBankList[i].Id != getToShowBank[j].BankAccountId {
				continue
			}
			newAccountBankList[i].MinimumDeposit = float64(webConfig.MinimumDeposit)
			newAccountBankList[i].MinimumWithdraw = webConfig.MinimumWithdraw
			// 2024/11/15 P.layer เอากลับมา scb แล้วไปดักตอน web hook เข้า + ตอนแสดง list scb จาก smsmode ให้ เป็น api mode ปกติ
			if newAccountBankList[i].BankId == model.BANK_ID_SCB {
				newAccountBankList[i].SmsMode = false
			}
			response = append(response, newAccountBankList[i])

		}
	}

	return response, nil
}

func (s *accountingService) GetActiveDepositBankAccountList() ([]model.WebBankAccountResponse, error) {

	banks, err := s.repo.GetActiveDepositBankAccountList()
	if err != nil {
		return nil, internalServerError(err)
	}
	return banks, nil
}

func (s *accountingService) BankAccountWithDrawPriorityValidation(amount float64) (*model.PriorityValidation, error) {

	// get config withdraw_max_per_time เปลี่ยนตามพี่ มิงค์ให้ user กำหนดเองได้
	// perTime, err := s.GetExternalAccountConfig("withdraw_max_per_time")
	// if err != nil {
	// 	return nil, internalServerError(err)
	// }
	bankAccountLimit, err := s.GetExternalAccountConfig("withdraw_bankaccount_limit")
	if err != nil {
		return nil, internalServerError(err)
	}
	// webConfig, err := s.repo.GetConfiguration()
	// if err != nil {
	// 	return nil, internalServerError(err)
	// }
	// รวม sum จาก bank_account
	sumBankAccount, err := s.repo.GetBankAccountWithSumWithdrawCreditAmount()
	if err != nil {
		return nil, internalServerError(err)
	}
	// get balance from list bank account และ priority
	bankAccounts, err := s.GetBankAccountForValidation()
	if err != nil {
		return nil, internalServerError(err)
	}
	if bankAccounts == nil {
		//create web hook log
		if _, err := s.CreateWebhookLog("ไม่มีบัญชีธนาคาร ถอน", ""); err != nil {
			return nil, internalServerError(err)
		}
		return nil, badRequest("ไม่มีบัญชีธนาคาร ถอน")
	}
	// [********] P.layer โทรมาบอก + P.mink confirm เปลี่ยนจาก bank account ไปเป็น config web
	getConfigWeb, _ := s.repo.GetConfiguration()

	// map sumBankAccount.FromAccountNumber with bankAccounts.AccountNumber and put in model.BankAccountDetailAndSumAmountListBody
	var bankAccountDetailAndSumAmountList []model.BankAccountDetailAndSumAmountListBody

	for _, item := range bankAccounts {
		if item.AccountTypeId == model.BANK_ACCOUNT_TYPE_DEPOSIT_ONLY || item.AccountTypeId == model.BANK_ACCOUNT_TYPE_HOLD || item.ConnectionStatusId == model.CONNECTION_DISCONNECTED {
			continue
		}
		if sumBankAccount != nil {
			for _, item2 := range sumBankAccount {
				if item.AccountNumber == item2.FromAccountNumber {

					if item.AccountNumber == item2.FromAccountNumber {
						var bankAccountDetailAndSumAmount model.BankAccountDetailAndSumAmountListBody
						bankAccountDetailAndSumAmount.Id = item.Id
						bankAccountDetailAndSumAmount.AccountNumber = item.AccountNumber
						bankAccountDetailAndSumAmount.SumAmount = item2.SumAmount
						bankAccountDetailAndSumAmount.ConnectionStatusId = item.ConnectionStatusId
						bankAccountDetailAndSumAmount.AccountBalance = item.AccountBalance
						bankAccountDetailAndSumAmount.AccountPriorityWithdraw = item.AccountPriorityWithdraw
						bankAccountDetailAndSumAmount.BankWithdrawMaximum = getConfigWeb.WithdrawMaximum
						bankAccountDetailAndSumAmount.AutoWithdrawMaximum = getConfigWeb.WithdrawMaximumAuto
						bankAccountDetailAndSumAmountList = append(bankAccountDetailAndSumAmountList, bankAccountDetailAndSumAmount)
					}
				} else {
					var bankAccountDetailAndSumAmount model.BankAccountDetailAndSumAmountListBody
					bankAccountDetailAndSumAmount.Id = item.Id
					bankAccountDetailAndSumAmount.AccountNumber = item.AccountNumber
					bankAccountDetailAndSumAmount.SumAmount = 0
					bankAccountDetailAndSumAmount.ConnectionStatusId = item.ConnectionStatusId
					bankAccountDetailAndSumAmount.AccountBalance = item.AccountBalance
					bankAccountDetailAndSumAmount.AccountPriorityWithdraw = item.AccountPriorityWithdraw
					bankAccountDetailAndSumAmount.BankWithdrawMaximum = getConfigWeb.WithdrawMaximum
					bankAccountDetailAndSumAmount.AutoWithdrawMaximum = getConfigWeb.WithdrawMaximumAuto
					bankAccountDetailAndSumAmountList = append(bankAccountDetailAndSumAmountList, bankAccountDetailAndSumAmount)

				}
			}
		} else {
			var bankAccountDetailAndSumAmount model.BankAccountDetailAndSumAmountListBody
			bankAccountDetailAndSumAmount.Id = item.Id
			bankAccountDetailAndSumAmount.AccountNumber = item.AccountNumber
			bankAccountDetailAndSumAmount.SumAmount = 0
			bankAccountDetailAndSumAmount.ConnectionStatusId = item.ConnectionStatusId
			bankAccountDetailAndSumAmount.AccountBalance = item.AccountBalance
			bankAccountDetailAndSumAmount.AccountPriorityWithdraw = item.AccountPriorityWithdraw
			bankAccountDetailAndSumAmount.BankWithdrawMaximum = getConfigWeb.WithdrawMaximum
			bankAccountDetailAndSumAmount.AutoWithdrawMaximum = getConfigWeb.WithdrawMaximumAuto
			bankAccountDetailAndSumAmountList = append(bankAccountDetailAndSumAmountList, bankAccountDetailAndSumAmount)
		}
	}

	var responseValidation model.PriorityValidation
	var status string
	var BankAccountId int64

	bankAccountLimitFloat, err := strconv.ParseFloat(bankAccountLimit.ConfigVal, 64)
	if err != nil {
		// Handle the error if the conversion fails
		return nil, err
	}

	// perTimeFloat, err := strconv.ParseFloat(perTime.ConfigVal, 64)
	// if err != nil {
	// 	// Handle the error if the conversion fails
	// 	return nil, err
	// }

	// already priority sort ready to loop
	// create interface to collect bankAccountDetailAndSumAmountList
	var validateLogs []interface{}
	for _, item := range bankAccountDetailAndSumAmountList {

		// [********] log validate
		validateLog := map[string]interface{}{
			"amount":             amount,
			"BankId":             item.Id, // Fixing typo "BamkId" to "BankId"
			"AmountExceedsLimit": fmt.Sprintf("item.SumAmount %v + amount %v less bankAccountLimitFloat %v", item.SumAmount, amount, bankAccountLimitFloat),
			"LimitPerTime":       fmt.Sprintf("amount %v less= AutoWithdrawMaximum %v", amount, item.AutoWithdrawMaximum),
			"MaximumLimit":       fmt.Sprintf("amount %v more= AutoWithdrawMaximum %v and amount %v less= BankWithdrawMaximum %v", amount, item.AutoWithdrawMaximum, amount, item.BankWithdrawMaximum),
		}
		validateLogs = append(validateLogs, validateLog)
		// AmountExceedsLimit มาจาก config
		if item.SumAmount+amount > bankAccountLimitFloat {
			// Move to the next account if the limit is exceeded
			continue
		}
		// [********] Disable Balance Check
		// if amount > item.AccountBalance {
		// 	continue
		// }

		// LimitPerTime มาจาก auto หรือไม่ จากการตั้งค่า บช
		if amount <= item.AutoWithdrawMaximum {
			BankAccountId = item.Id
			status = model.AUTO_WITHDRAW
			break
			// MaximumLimit เกินถอนสูงสุด 50000
		} else if amount >= item.AutoWithdrawMaximum && amount <= item.BankWithdrawMaximum {
			BankAccountId = item.Id
			status = model.OUT_OF_CONFIG_AMOUNT
			break
		} else {
			continue
		}
		// You've found a suitable account, so you can break the loop
	}

	responseValidation.ValidateLog = validateLogs
	responseValidation.Status = status
	responseValidation.BankAccountId = BankAccountId
	return &responseValidation, nil
}

func (s *accountingService) BankAccountWithDrawValidation(bankId int64, amount float64) (*model.PriorityValidation, error) {

	//get config
	// perTime, err := s.GetExternalAccountConfig("withdraw_max_per_time")
	// if err != nil {
	// 	return nil, internalServerError(err)
	// }
	bankAccountLimit, err := s.GetExternalAccountConfig("withdraw_bankaccount_limit")
	if err != nil {
		return nil, internalServerError(err)
	}
	// webConfig, err := s.repo.GetConfiguration()
	// if err != nil {
	// 	return nil, internalServerError(err)
	// }

	// UPDATE BALNACE FROM FASBANK
	// [********] Disable Balance Check
	// err = s.UpdateBankAccountBalanceById(bankId)
	// if err != nil {
	// 	return nil, internalServerError(err)
	// }

	bankAccount, err := s.repo.GetBankAccountById(bankId)
	if err != nil {
		return nil, internalServerError(err)
	}
	if bankAccount.AccountTypeId == model.BANK_ACCOUNT_TYPE_DEPOSIT_ONLY || bankAccount.AccountTypeId == model.BANK_ACCOUNT_TYPE_HOLD {
		return nil, badRequest("Invalid Account Type")
	}

	sumBankAccount, err := s.repo.GetBankAccountWithWithdrawSumCreditAmountByAccountNumber(bankAccount.AccountNumber)
	if err != nil {
		return nil, internalServerError(err)
	}

	var responseValidation model.PriorityValidation
	var status string
	var BankAccountId int64

	bankAccountLimitFloat, err := strconv.ParseFloat(bankAccountLimit.ConfigVal, 64)
	if err != nil {
		return nil, err
	}

	// perTimeFloat, err := strconv.ParseFloat(perTime.ConfigVal, 64)
	// if err != nil {
	// 	return nil, err
	// }
	// If sumBankAccount is nil, assume SumAmount is 0
	getConfigWeb, _ := s.repo.GetConfiguration()
	var sumAmount float64
	var validateLogs []interface{}
	log := map[string]interface{}{
		"amount":             amount,
		"BankId":             bankId,
		"AmountExceedsLimit": fmt.Sprintf("item.SumAmount %v + amount %v less bankAccountLimitFloat %v", sumAmount, amount, bankAccountLimitFloat),
		"LimitPerTime":       fmt.Sprintf("amount %v less= AutoWithdrawMaximum %v", amount, getConfigWeb.WithdrawMaximumAuto),
		"MaximumLimit":       fmt.Sprintf("amount %v more= AutoWithdrawMaximum %v and amount %v less= BankWithdrawMaximum %v", amount, getConfigWeb.WithdrawMaximumAuto, amount, getConfigWeb.WithdrawMaximum),
	}

	// Append the log to the slice
	validateLogs = append(validateLogs, log)

	sumAmount = 0
	if sumBankAccount != nil {
		sumAmount = sumBankAccount.SumAmount
	}

	if sumAmount+amount > bankAccountLimitFloat {
		return nil, badRequest("Limit is exceeded")
	}
	// [********] Disable Balance Check
	// if amount > bankAccount.AccountBalance {
	// 	return nil, badRequest("Amount is exceeded")
	// }

	// if amount <= float64(bankAccount.AutoWithdrawMaximum) {
	// 	BankAccountId = bankAccount.Id
	// 	status = model.AUTO_WITHDRAW
	// } else if amount >= float64(bankAccount.AutoWithdrawMaximum) && amount <= bankAccount.BankWithdrawMaximum {
	// 	BankAccountId = bankAccount.Id
	// 	status = model.OUT_OF_CONFIG_AMOUNT
	// } else {
	// 	return nil, badRequest("Out of config amount")
	// }

	// [********] P.layer โทรมาบอก + P.mink confirm เปลี่ยนจาก bank account ไปเป็น config web
	if amount <= float64(getConfigWeb.WithdrawMaximumAuto) {
		BankAccountId = bankAccount.Id
		status = model.AUTO_WITHDRAW
	} else if amount >= float64(getConfigWeb.WithdrawMaximumAuto) && amount <= getConfigWeb.WithdrawMaximum {
		BankAccountId = bankAccount.Id
		status = model.OUT_OF_CONFIG_AMOUNT
	} else {
		return nil, badRequest("Out of config amount")
	}

	responseValidation.ValidateLog = validateLogs
	responseValidation.Status = status
	responseValidation.BankAccountId = BankAccountId
	return &responseValidation, nil
}

func (s *accountingService) CreateBankTransactionLog(req model.BankTransactionLogCreateRequest) (*int64, error) {

	var body model.BankTransactionLogCreate
	if req.JsonPayload != nil {
		body.JsonPayload = *req.JsonPayload
	} else {
		body.JsonPayload = "{}"
	}
	if req.JsonRequest != nil {
		body.JsonRequest = *req.JsonRequest
	} else {
		body.JsonRequest = "{}"
	}
	if req.LogType != nil {
		body.LogType = *req.LogType
	} else {
		body.LogType = "LOG_ACTION"
	}
	if req.Status != nil {
		body.Status = *req.Status
	} else {
		body.Status = "PENDING_WEBHOOK"
	}

	_, err := s.repo.CreateBankTransactionLog(body)
	if err != nil {
		return nil, internalServerError(err)
	}
	return nil, nil
}

func (s *accountingService) GetBankWithdrawMaxConfig() (*float64, error) {

	configBankMax, err := s.GetExternalAccountConfig("withdraw_max_per_time")
	if err != nil {
		return nil, internalServerError(err)
	}
	perTimeFloat, err := strconv.ParseFloat(configBankMax.ConfigVal, 64)
	if err != nil {
		return nil, err
	}

	return &perTimeFloat, nil
}

func (s *accountingService) GetSummaryReportAccountList() ([]model.FastBankAccountAndDailySummaryResponse, error) {

	botAccountCheckVerify, err := s.UpdateAllBankAccountBotStatus()
	if err != nil {
		return nil, nil
	}

	list, _, err := s.repo.GetSummaryReportAccountList()
	if err != nil {
		return nil, internalServerError(err)
	}

	var bankAccountList []string
	for _, item := range list {
		bankAccountList = append(bankAccountList, item.AccountNumber)
	}

	sumBankAccountWithdraw, err := s.repo.GetBankAccountWithSumWithdrawCreditAmountByAccount(bankAccountList)
	if err != nil {
		return nil, internalServerError(err)
	}
	// GetBankAccountWithSumDepostCreditAmount()([]model.BankAccountAndSumAmount, error)
	sumBankAccountDeposit, err := s.repo.GetBankAccountWithSumDepostCreditAmountByAccount(bankAccountList)
	if err != nil {
		return nil, internalServerError(err)
	}

	// for loop sum with same bank account number sumBankAccountWithdraw and sumBankAccountDeposit
	var responseList []model.FastBankAccountAndDailySummaryResponse
	for _, list := range list {
		for _, withdraw := range sumBankAccountWithdraw {
			for _, deposit := range sumBankAccountDeposit {

				if list.AccountNumber == withdraw.FromAccountNumber && withdraw.FromAccountNumber == deposit.ToAccountNumber || list.AccountNumber == deposit.ToAccountNumber && deposit.ToAccountNumber == withdraw.FromAccountNumber {

					var sumBankAccountTransaction model.FastBankAccountAndDailySummaryResponse
					sumBankAccountTransaction.Id = list.Id
					sumBankAccountTransaction.BankId = list.BankId
					sumBankAccountTransaction.BankCode = list.BankCode
					sumBankAccountTransaction.BankName = list.BankName
					sumBankAccountTransaction.BankIconUrl = list.BankIconUrl
					sumBankAccountTransaction.AccountTypeId = list.AccountTypeId
					sumBankAccountTransaction.AccountTypeName = list.AccountTypeName
					sumBankAccountTransaction.AccountName = list.AccountName
					sumBankAccountTransaction.AccountNumber = list.AccountNumber
					sumBankAccountTransaction.AccountBalance = list.AccountBalance
					sumBankAccountTransaction.AccountPriorityWithdraw = list.AccountPriorityWithdraw
					sumBankAccountTransaction.AccountStatusName = list.AccountStatusName
					sumBankAccountTransaction.DeviceUid = list.DeviceUid
					sumBankAccountTransaction.PinCode = list.PinCode
					sumBankAccountTransaction.ConnectionStatusId = list.ConnectionStatusId
					sumBankAccountTransaction.ConnectionStatusName = list.ConnectionStatusName
					sumBankAccountTransaction.LastConnUpdateAt = list.LastConnUpdateAt
					sumBankAccountTransaction.CreatedAt = list.CreatedAt
					sumBankAccountTransaction.UpdatedAt = list.UpdatedAt
					sumBankAccountTransaction.TotalWithdraw = withdraw.SumAmount
					sumBankAccountTransaction.TotalDeposit = deposit.SumAmount
					sumBankAccountTransaction.SmsMode = list.SmsMode
					// sumBankAccountTransaction.VerifyLoginBot = bot.VerifyLogin

					responseList = append(responseList, sumBankAccountTransaction)
				}

			}
		}
	}

	// to do เพิ่ม เอามาจาก move transaction
	moveTransWithdraw, _ := s.repo.GetSumMoveTransactionFromAccountToDay()
	for i, oldList := range responseList {
		for _, move := range moveTransWithdraw {
			if oldList.Id == move.FromBankId {
				responseList[i].TotalWithdraw = oldList.TotalWithdraw + move.TotalAmount
			}
		}
	}

	moveTransDeposit, _ := s.repo.GetSumMoveTransactionToAccountToDay()
	for i, oldList := range responseList {
		for _, move := range moveTransDeposit {
			if oldList.Id == move.ToAccountId {
				responseList[i].TotalDeposit = oldList.TotalDeposit + move.TotalAmount
			}
		}
	}

	for i, oldList := range responseList {
		for _, bot := range botAccountCheckVerify {
			if oldList.Id == bot.Id {
				responseList[i].VerifyLoginBot = bot.VerifyLogin
			}
		}
	}

	return responseList, nil
}

func (s *accountingService) ExternalMatchWithdrawTransaction(statementBody model.BankStatementCreateBody) error {

	// [********]fastbank credit check balance to continue unuse func or not auto
	getWebLocal, _ := s.repo.GetLocalWebInfo()
	if getWebLocal != nil {
		if getWebLocal.FastbankCreditBalance <= -1000 {
			log.Println("ExternalMatchWithdrawTransaction.WEB_OUT_OF_CREDIT")
			return badRequest("ยอดเงินระบบเว็บไม่เพียงพอ")
		}
	}
	//todo dunk
	startProcess := time.Now()
	// [get transaction withdraw with statementBody]
	var req model.ExternalCheckWithdrawTransaction
	req.Amount = statementBody.Amount
	if req.Amount < 0 {
		req.Amount = req.Amount * -1
	}

	// [สบับกัน เป็นถอน]
	req.ToBankId = statementBody.FromBankId
	req.ToAccountNumber = statementBody.FromAccountNumber
	req.FromAccountId = statementBody.AccountId
	req.TransactionAt = statementBody.TransferAt
	isTrue := false
	if statementBody.FromBankId == model.BANK_ID_TRUE {
		isTrue = true
	}
	//******** confirm isTrueMoney by p.lay
	transaction, err := s.repo.ExternalCheckWithdrawTransaction(isTrue, req)
	if err != nil {
		if err.Error() == recordNotFound {
			log.Println("ไม่พบ transaction ที่ต้องการ")
			return nil
		}
		return internalServerError(err)
	}
	// else just update match Id
	if transaction.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_SUCCESS {
		err := s.repo.UpdateTransactionDuplicateWithExternalMatch(transaction.Id, statementBody.Id)
		if err != nil {
			return nil
		}
	} else {

		// [create transaction action success]
		var createConfirm model.CreateSuccessTransferWithdrawRequest
		createConfirm.TransactionId = transaction.Id
		createConfirm.ConfirmedAt = time.Now()
		var setIdAuto int64 = 0
		createConfirm.ConfirmedByAdminId = &setIdAuto
		if _, err := s.CreateRetrySuccessTransferWithdraw(createConfirm); err != nil {
			log.Println("UserCreateWithdrawTransaction.CreateRetrySuccessTransferWithdraw", err)
			return nil
		}

		// [Show Withdraw USER_TRANSACTION]
		var showUserTrans model.UserTransactionShowUpdate
		showUserTrans.TransactionId = transaction.Id
		showUserTrans.ConfirmedAt = &createConfirm.ConfirmedAt
		showUserTrans.ConfirmAdminId = &setIdAuto
		if err := s.repo.ShowUserTransaction(showUserTrans); err != nil {
			log.Println("UserCreateWithdrawTransaction.ShowUserTransaction", err)
			return nil
		}

		// [DECREASE FASTBANK CREDIT]
		if err := s.repo.DecreaseFastbankCredit(1); err != nil {
			log.Println("UserCreateWithdrawTransaction.DECREASE_FASTBANK_CREDIT_ERROR", err)
		}
		statusId := model.TRANS_STATUS_WITHDRAW_SUCCESS
		var updateApprovedBy model.UpdateConfirmAutoWithdrawBody
		updateApprovedBy.TransactionStatusId = &statusId
		updateApprovedBy.ConfirmedAt = time.Now()
		updateApprovedBy.ConfirmedByAdminId = &setIdAuto

		errUpdateAdminAnd := s.repo.UpdateAdminAndTransactionStatus(transaction.Id, updateApprovedBy)
		if errUpdateAdminAnd != nil {
			log.Println("CreateAutoWithdraw.UpdateAdminAndTransactionStatus", errUpdateAdminAnd)
		}

		var updateApprovedByAdmin model.UpdateConfirmedByAdminIdRequest
		updateApprovedByAdmin.ConfirmAdminId = &setIdAuto
		errUpdateConfirmed := s.repo.UpdateConfirmedByAdminId(transaction.Id, updateApprovedByAdmin)
		if errUpdateConfirmed != nil {
			log.Println("CreateAutoWithdraw.UpdateConfirmedByAdminId", errUpdateConfirmed)
		}
		// [Notify]
		var externalNoti model.NotifyExternalNotificationRequest

		endProcess := time.Now()
		elapsed := endProcess.Sub(startProcess)
		elapsedSeconds := elapsed.Seconds()
		timeElapsed := fmt.Sprintf("%.2f", elapsedSeconds)
		externalNoti.TypeNotify = model.IsWithdrawalCreditSuccess
		externalNoti.TransId = &transaction.Id
		externalNoti.Amount = req.Amount
		externalNoti.MemberCode = transaction.UserMemberCode
		externalNoti.UserCredit = transaction.AfterAmount
		externalNoti.ConfirmedByAdminId = setIdAuto
		externalNoti.TimerCounter = timeElapsed
		externalNoti.TransferDateTime = transaction.TransferAt.Add(7 * time.Hour).Format("2006-01-02 15:04:05")
		externalNoti.ActionTime = time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02 15:04:05")

		externalNoti.WebScoket.UserID = transaction.UserId
		externalNoti.WebScoket.Amount = transaction.CreditAmount
		externalNoti.WebScoket.MemberCode = transaction.UserMemberCode
		externalNoti.WebScoket.AlertType = "WITHDRAW"
		if err := s.notiService.ExternalNotification(externalNoti); err != nil {
			log.Println("FailedNotify", err)
		}

		// Create detail
		var createBankTransactionExternalDetail model.BankTransactionExternalDetailCreateRequest
		createBankTransactionExternalDetail.BankTransactionId = transaction.Id
		createBankTransactionExternalDetail.Detail = "SUCCESS"
		createBankTransactionExternalDetail.ErrorCode = 0
		if _, err := s.repo.CreateBankTransactionExternalDetail(createBankTransactionExternalDetail); err != nil {
			return nil
		}

		err := s.repo.UpdateTransactionDuplicateWithExternalMatch(transaction.Id, statementBody.Id)
		if err != nil {
			return nil
		}

	}
	return nil
}

func (s *accountingService) CreateRetrySuccessTransferWithdraw(req model.CreateSuccessTransferWithdrawRequest) (*int64, error) {

	record, err := s.repo.GetTransactionWithdrawById(req.TransactionId)
	if err != nil {
		return nil, internalServerError(err)
	}
	jsonBefore, _ := json.Marshal(record)
	var createBody model.CreateBankTransactionActionBody
	createBody.ActionKey = fmt.Sprintf("CFW_TRASFER_REMATCH#%d", req.TransactionId)
	createBody.TransactionId = req.TransactionId
	createBody.UserId = record.UserId
	createBody.TransactionTypeId = record.TransactionTypeId
	createBody.FromAccountId = record.FromAccountId
	createBody.ToAccountId = record.ToAccountId
	createBody.JsonBefore = string(jsonBefore)
	createBody.TransferAt = &record.TransferAt
	createBody.CreditAmount = record.CreditAmount
	createBody.ConfirmedAt = req.ConfirmedAt
	createBody.ConfirmedByAdminId = req.ConfirmedByAdminId
	// BOF : transaction
	if _, err := s.repo.CreateTransactionAction(createBody); err != nil {
		return nil, internalServerError(err)
	}
	return nil, nil
}

func (s accountingService) GetBankTransactionSummaryGraph2(req model.BankTransactionGraph2Request) (*model.BankTransactionGraph2Response, error) {

	data, err := s.repo.GetBankTransactionSummaryGraph2(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	return data, nil
}

func (s accountingService) LogAdmin(name string, adminId int64, req interface{}) error {

	var createBody model.AdminLogCreateBody
	createBody.Name = name
	createBody.AdminId = adminId
	createBody.JsonReq = helper.StructJson(req)
	if _, err := s.repo.CreateAdminLog(createBody); err != nil {
		return internalServerError(err)
	}
	return nil
}

func (s *accountingService) CreateSuccessAdminAction(req model.AdminActionCreateRequest) (*int64, error) {

	var createBody model.AdminActionCreateBody
	createBody.AdminId = req.AdminId
	createBody.TypeId = req.TypeId
	createBody.IsSuccess = true
	createBody.IsShow = true
	createBody.RefObjectId = req.RefObjectId
	createBody.Detail = req.Detail
	createBody.JsonInput = req.JsonInput
	createBody.JsonOutput = req.JsonOutput
	insertId, err := s.repo.CreateAdminAction(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}
	return insertId, nil
}

func (s accountingService) GetExchangeCurrencyList(req model.GetExchangeCurrencyListRequest) ([]model.GetExchangeCurrencyListResponse, error) {

	data, err := s.repo.GetExchangeCurrencyList(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	return data, nil
}

func (s accountingService) CreateExchangeRate(req []model.CreateExchangeRateRequest, createdByAdmin int64) error {

	// to do validate ให้ดีกว่านี้ 0.25 P ไม่ทัน ทั้ง feat ของ currency และ ของ exchange rate laos + transaciton
	for _, item := range req {
		if item.ExchangeCurrencyId == 1 {
			return badRequest("Invalid ExchangeCurrencyId")
		}
	}

	adminDetail, err := s.repo.GetAdminById(createdByAdmin)
	if err != nil {
		log.Println(req)
		return internalServerError(err)
	}

	exchangeCurrencyList, err := s.repo.GetExchangeCurrencyInternalList()
	if err != nil {
		return internalServerError(err)
	}

	var createExchangeUpdateLog []model.CreateExchangeUpdateLogRequest
	for _, createItemReq := range req {
		for _, currency := range exchangeCurrencyList {
			if createItemReq.ExchangeCurrencyId == currency.Id {
				var create model.CreateExchangeUpdateLogRequest
				create.ExchangeCurrencyId = createItemReq.ExchangeCurrencyId
				create.DefaultMainRate = 1
				create.ExchangeRate = createItemReq.ExchangeRate
				create.CurrencyNameTh = currency.CurrencyNameTh
				create.CurrencyNameEn = currency.CurrencyNameEn
				create.CreatedAdminId = createdByAdmin
				create.CreatedAdminName = adminDetail.Fullname
				create.MaxRateDeposit = createItemReq.MaxRateDeposit
				createExchangeUpdateLog = append(createExchangeUpdateLog, create)
			}
		}
	}

	if err := s.repo.CreateExchangeUpdateLog(createExchangeUpdateLog); err != nil {
		return internalServerError(err)
	}

	if err = s.repo.DeleteAllExchangeRate(); err != nil {
		return internalServerError(err)
	}

	create := s.repo.CreateExchangeRate(req)
	if create != nil {
		return internalServerError(create)
	}

	return nil
}

func (s accountingService) GetExchangeRateList() ([]model.GetExchangeRateList, error) {

	data, err := s.repo.GetExchangeRateList("ONLY_SETTING_ABLE")
	if err != nil {
		return nil, internalServerError(err)
	}
	return data, nil
}

func (s accountingService) GetUserExchangeRateList() ([]model.GetExchangeRateList, error) {

	data, err := s.repo.GetExchangeRateList("ALL")
	if err != nil {
		return nil, internalServerError(err)
	}
	return data, nil
}

func (s accountingService) GetLaosExchangeCurrency() (*model.GetExchangeRate, error) {

	data, err := s.repo.GetLaosExchangeCurrency()
	if err != nil {
		return nil, internalServerError(err)
	}

	return data, nil
}

func (s accountingService) GetExchangeUpdateLogList(req model.GetExchangeUpdateLogListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.Pagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	list, total, err := s.repo.GetExchangeUpdateLogList(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	var response model.SuccessWithPagination
	response.List = list
	response.Total = total
	response.Message = "SUCCESS"
	return &response, nil
}

func (s *accountingService) CreateSmsModeFastbankDeposit(body model.CreateSmsModeDepositFastbankRequest) (*model.CreateSmsModeDepositFastbankResponse, error) {

	// [GET BANK ACCOUNT]
	if body.Amount == 0 {
		return nil, badRequest("AMOUNT_IS_ZERO")
	}

	bankAccount, err := s.repo.GetBankAccountById(body.AccountId)
	if err != nil {
		return nil, internalServerError(err)
	}

	if !bankAccount.SmsMode {
		return nil, badRequest("THIS_ACCOUNT_NOT_SUPPORT_SMS_MODE")
	}

	// [GET USER]
	user, err := s.repo.GetUser(body.UserId)
	if err != nil {
		return nil, internalServerError(err)
	}

	// [********] P.layer use old transaction to block same multiple deposit
	if pOrder, err := s.repo.CheckSmsModeDepositOrderInLastOrder(body.UserId, body.AccountId); err != nil {
		if err != gorm.ErrRecordNotFound {
			log.Println("CheckSmsModeDepositOrderInLast5Minutes.NO_ORDER_FOUND_BEFORE", err)
		}
	} else if pOrder != nil {
		actionAtUtc := time.Now().UTC()
		if pOrder.Amount == body.Amount {
			if pOrder.CreatedAt.Add(time.Minute * 10).After(actionAtUtc) {
				var result model.CreateSmsModeDepositFastbankResponse
				result.Message = "SUCCESS"
				result.TransferAmount = pOrder.TransferAmount
				return &result, nil
			}
		}
	}

	// [GET COUNT ORDER]
	getCountOrder, err := s.repo.CountDBSmsModeDeposit()
	if err != nil {
		return nil, internalServerError(err)
	}
	// [GET ENV]
	// dataBaseName := os.Getenv("DB_NAME")

	// [CREATE ORDER ]
	var createOrder model.CreateSmsModeDepositBody
	createOrder.UserId = user.Id
	createOrder.FromBankId = user.BankId
	createOrder.AccountFrom = user.BankAccount
	createOrder.BankAccountId = bankAccount.Id
	createOrder.BankAccountNo = bankAccount.AccountNumber
	createOrder.BankCode = bankAccount.BankCode
	createOrder.RefId = nil
	// SMS-userphone-runningnumber P.layer ********
	createOrder.OrderNo = fmt.Sprintf("SMS-P%s-R%d", user.Phone, getCountOrder+1)
	createOrder.Detail = fmt.Sprintf("%s:%s => %s:%s", user.BankName, user.BankAccount, bankAccount.BankName, bankAccount.AccountNumber)
	createOrder.Amount = body.Amount
	createOrder.TransactionStatus = "PENDING"
	orderId, err := s.repo.CreateDBSmsModeDeposit(createOrder)
	if err != nil {
		return nil, internalServerError(err)
	}

	var response model.CreateSmsModeDepositFastbankResponse
	if orderId != nil && *orderId > 0 {

		// [CREATE SMS MODE FASTBANK DEPOSIT]
		var createSmsModeFastbankDeposit model.CreateSmsModeFastbankDepositBody
		createSmsModeFastbankDeposit.AccountFrom = user.BankAccount
		createSmsModeFastbankDeposit.Amount = body.Amount
		createSmsModeFastbankDeposit.BankAccountNo = bankAccount.AccountNumber
		createSmsModeFastbankDeposit.BankCode = bankAccount.BankCode
		createSmsModeFastbankDeposit.RefNo = createOrder.OrderNo

		// [CREATE SMS MODE FASTBANK DEPOSIT]

		var updateSmsModeDeposit model.UpdateSmsModeDepositBody
		externalCreateDeposit, err := s.ExternalSmsModeFastbankDeposit(createSmsModeFastbankDeposit)
		if err != nil {
			updateSmsModeDeposit.Id = *orderId
			updateSmsModeDeposit.TransactionNo = ""
			updateSmsModeDeposit.TransactionDate = time.Now().UTC()
			updateSmsModeDeposit.TransactionStatus = "ERROR"
			updateSmsModeDeposit.TransferAmount = 0
			updateSmsModeDeposit.Remark = fmt.Sprintf("ERROR : %s", err.Error())
			if err := s.repo.UpdateDBSmsModeDeposit(updateSmsModeDeposit); err != nil {
				return nil, internalServerError(err)
			}
			return nil, badRequest(err.Error())
		} else {
			updateSmsModeDeposit.Id = *orderId
			updateSmsModeDeposit.TransactionNo = fmt.Sprintf("%d", externalCreateDeposit.Id)
			updateSmsModeDeposit.TransactionDate = time.Now().UTC()
			updateSmsModeDeposit.TransactionStatus = "WAITING_CONFIRM"
			updateSmsModeDeposit.TransferAmount = externalCreateDeposit.AmountWithDecimal
			updateSmsModeDeposit.Remark = ""
			if err := s.repo.UpdateDBSmsModeDeposit(updateSmsModeDeposit); err != nil {
				return nil, internalServerError(err)
			}
		}

		// fmt.Println("CreateSmsModeFastbankDeposit", helper.StructJson(externalCreateDeposit))

		response.Message = "SUCCESS"
		response.TransferAmount = externalCreateDeposit.AmountWithDecimal
		return &response, nil
	}
	return nil, internalServerError(errors.New("ไม่สามารถสร้างรายการได้"))
}

func (s *accountingService) ExternalSmsModeFastbankDeposit(body model.CreateSmsModeFastbankDepositBody) (*model.CreateSmsModeFastbankDepositResponse, error) {

	log.Println("CreateSmsModeFastbankDeposit", helper.StructJson(body))

	client := &http.Client{}
	data, _ := json.Marshal(body)
	path := os.Getenv("ACCOUNTING_API_ENDPOINT") + "/api/v2/statement/depositSms"

	reqExternal, _ := http.NewRequest("POST", path, bytes.NewBuffer(data))
	reqExternal.Header.Set("apiKey", os.Getenv("ACCOUNTING_API_KEY"))
	reqExternal.Header.Set("Content-Type", "application/json")
	response, err := client.Do(reqExternal)
	if err != nil {
		log.Print(err.Error())
		return nil, internalServerError(errors.New("Error from client"))
	}
	responseData, err := io.ReadAll(response.Body)
	if err != nil {
		log.Print(err.Error())
		return nil, internalServerError(errors.New("Error from io ReadAll"))
	}
	if response.StatusCode != 200 {
		log.Println("CreateSmsModeFastbankDeposit, StatusCode:", response.StatusCode)
		log.Println("CreateSmsModeFastbankDeposit, responseData:", string(responseData))
		if _, err := s.CreateWebhookLog("CreateSmsModeFastbankDeposit, ERROR", string(responseData)+helper.StructJson(body)); err != nil {
			log.Print(err)
		}
		var resultError model.CreateSmsModeFastbankDepositErrorResponse
		errJson := json.Unmarshal(responseData, &resultError)
		if errJson != nil {
			var resultError2 model.CreateSmsModeFastbankDepositErrorResponse2
			errJson2 := json.Unmarshal(responseData, &resultError2)
			if errJson2 != nil {
				return nil, internalServerError(errJson2)
			}
			_, err := json.Marshal(resultError2)
			if err != nil {
				return nil, internalServerError(err)
			}
			if resultError2.Error == "Internal Server Error" {
				return nil, internalServerError(errors.New("ระบบธนาคารไม่สามารถให้บริการได้ในขณะนี้"))
			}
			return nil, badRequest(resultError2.Error)
		}
		_, err := json.Marshal(resultError)
		if err != nil {
			return nil, internalServerError(err)
		}

		return nil, badRequest(resultError.ErrorMessage)
	}
	fmt.Print("CreateSmsModeFastbankDeposit, responseData:", string(responseData))

	var result model.CreateSmsModeFastbankDepositResponse
	errJson := json.Unmarshal(responseData, &result)
	if errJson != nil {
		_, err := s.CreateWebhookLog("CreateSmsModeFastbankDeposit, FAIL", errJson.Error())
		log.Print(err)
	}
	jsonResult, err := json.Marshal(result)
	if err == nil {
		_, err := s.CreateWebhookLog("CreateSmsModeFastbankDeposit, SUCCESS", string(jsonResult))
		log.Print(err)
	}
	return &result, nil
}

func (s *accountingService) CreateBankStatementFromWebhookAndAutoSmsMode(data model.WebhookStatement) (*int64, error) {

	var statementId int64
	fmt.Println("CreateBankStatementFromWebhookAndAutoSmsMode : statement Begin log", helper.StructJson(data))
	startTime := time.Now()
	// [Bank account check]
	systemAccount, err := s.repo.GetBankAccountByExternalId(data.BankAccountId)
	if err != nil {
		log.Println(err)
		return nil, badRequest("Invalid Bank Account")
	}

	// [********]fastbank credit check balance to continue unuse func or not auto
	getWebLocal, _ := s.repo.GetLocalWebInfo()
	if getWebLocal != nil {
		if getWebLocal.FastbankCreditBalance <= -1000 {
			log.Println("CreateBankStatementFromWebhookAndAutoSmsMode.WEB_OUT_OF_CREDIT")
			return nil, badRequest("WEB_OUT_OF_CREDIT")
		}
	}

	configWeb, err := s.repo.GetWebConfiguration()
	if err != nil {
		log.Println(err)
		return nil, badRequest("WEB_CONFIG_NOT_FOUND")
	}

	// [Check Exist Statement]
	_, errOldStatement := s.repo.GetWebhookStatementByExternalId(data.Id)
	if errOldStatement != nil && errOldStatement.Error() == recordNotFound {

		// [Create Statement : Check TxnCode]
		var bodyCreateState model.BankStatementCreateBody
		bodyCreateState.AccountId = systemAccount.Id
		bodyCreateState.ExternalId = &data.Id
		data.TxnCode = strings.ToUpper(data.TxnCode)
		if data.TxnCode == "X1" || data.TxnCode == "CR" || data.TxnCode == "IORSDT" || data.TxnCode == "NBSDT" {
			//[ย้ายมาตรงนี้เพราะ จะเช็คยอดถอน] เช็ตยอดเงินขั้นต่ำ ** มันก็เช็คทุกรายการ ไม่ได้เช็คแค่ฝากนะ
			minDepositAmount := float64(configWeb.MinimumDeposit)
			if minDepositAmount > data.Amount {
				// P.mink 2024/09/25 ตั้งค่าฝากขั้นต่ำที่ xx.xx บาท
				messageMinimumDeposit := fmt.Sprintf("ตั้งค่าฝากขั้นต่ำที่ %.2f บาท", minDepositAmount)
				return nil, badRequest(messageMinimumDeposit)
			}
			bodyCreateState.StatementTypeId = model.STATEMENT_TYPE_TRANSFER_IN
			bodyCreateState.Amount = data.Amount
		} else if data.TxnCode == "X2" || data.TxnCode == "DR" || data.TxnCode == "NBSWT" || data.TxnCode == "IORSWT" {
			bodyCreateState.StatementTypeId = model.STATEMENT_TYPE_TRANSFER_OUT
			bodyCreateState.Amount = data.Amount * -1
		} else {
			if _, err := s.CreateWebhookLog("unsupport TxnCode found, WebhookStatement:", data.TxnCode); err != nil {
				log.Print(err)
			}
			return nil, badRequest("Invalid TxnCode")
		}

		// [Get smsModeTransaction]
		smsModeTransaction, err := s.repo.GetSmsModeDepositFromOrder(data.Info)
		if err != nil {
			return nil, badRequest("REF_SMSMODE_TRANSACTION_NOT_FOUND")
		}
		if smsModeTransaction != nil {

			if smsModeTransaction.TransactionStatus == "PAID" {
				if _, err := s.CreateWebhookLog("TRANSACTION_ALREADY_PAID", helper.StructJson(smsModeTransaction)); err != nil {
					log.Print(err)
				}
				return nil, badRequest("TRANSACTION_ALREADY_PAID")
			}

			// confrim paygate_smsmode_deposit
			var updateConfirmSmsModeDeposit model.UpdateConfirmSmsModeDepositBody
			updateConfirmSmsModeDeposit.Id = &smsModeTransaction.Id
			success := "PAID"
			updateConfirmSmsModeDeposit.TransactionStatus = &success
			updateConfirmSmsModeDeposit.PaymentAt = &data.DateTime
			if err := s.repo.UpdateConfirmSmsModeDeposit(updateConfirmSmsModeDeposit); err != nil {
				return nil, nil
			}

			bodyCreateState.FromAccountNumber = smsModeTransaction.AccountFrom
		} else {
			bodyCreateState.FromAccountNumber = ""
		}

		// Get User
		user, err := s.repo.GetMemberById(smsModeTransaction.UserId)
		if err != nil {
			return nil, nil
		}
		if user == nil {
			return nil, badRequest("Invalid User")
		}
		bodyCreateState.FromBankId = user.BankId

		// [Create Statement : Check Is It Account Move Transaction]
		var check model.CheckDepositAccountMoveTransaction
		check.FromAccountBankId = user.BankId
		check.FromAccountNumber = user.BankAccount
		check.ToAccountId = systemAccount.Id
		check.ToAccountBankId = systemAccount.BankId
		isCheckAccountMoveTran, err := s.CheckDepositAccountMoveTransaction(check)
		if err != nil {
			return nil, nil
		}
		if isCheckAccountMoveTran {
			return nil, badRequest("Account move transaction")
		}

		// [Create Statement : Create Statement]
		// use transaction instead of statement
		bodyCreateState.Detail = data.TxnDescription + " " + data.ChannelDescription
		// [********] Confirm by P.lay SCB Set -1 day when 23.00 - 00.00
		// [********] Datำ ใน smsmode ไม่ผิด
		// timeDateStatementSCB := data.DateTime.Add(7 * time.Hour).Hour()
		// if systemAccount.BankId == model.BANK_ID_SCB && (timeDateStatementSCB == 23) {
		// 	data.DateTime = data.DateTime.AddDate(0, 0, -1)
		// }
		bodyCreateState.TransferAt = data.DateTime
		bodyCreateState.StatementStatusId = model.STATEMENT_STATUS_PENDING
		insertId, err := s.repo.CreateWebhookStatement(bodyCreateState)
		if err != nil {
			webhookLogMessage := fmt.Sprintf("CreateWebhookStatement, ERROR: %s", err)
			return nil, errors.New(webhookLogMessage)
		}
		statementId = *insertId
		bodyCreateState.Id = *insertId
		if bodyCreateState.StatementTypeId == model.STATEMENT_TYPE_TRANSFER_IN {
			// ===========================================================
			// [Create Transaction : Check Duplicate]

			// [Create Transaction : Reget Statement]
			statement, err := s.repo.GetBankStatementById(*insertId)
			if err != nil {
				return nil, err
			}

			// [Create Transaction : Double Check Possible Owner]
			// var total int64
			// var records []model.Member
			// var reqPosibleList model.GetPossibleOwnersRequest
			// reqPosibleList.FromBankId = statement.FromBankId
			// reqPosibleList.ToBankId = systemAccount.BankId
			// reqPosibleList.FromAccountNumber = statement.FromAccountNumber

			// [Set Confirmed By Bot]
			admin := model.ApprovedByAdmin{
				Id: 0,
			}
			var lineResultCredit float64
			// [Create Transaction : Create Transaction]

			// Create Transaction + Create New MemberCode
			// Confirmed + MatchOwner
			// Increase Credit
			// Create Commission + Turnover
			// WebSocket
			// Line Notify

			if user.MemberCode != "" {
				// [********] ออมสินต้องให้ match แล้วครับ มา set ค่า ใหม่
				bodyCreateState.FromAccountNumber = user.BankAccount
				bodyCreateState.FromBankId = user.BankId

				// ปรับ flow ตามพี่เลย์บอก ความจริงให้เข้าไปสร้าง แต่ไม่ auto แต่ผม จะ หยุด ไม่ให้เกิดเคสเดิมที่เข้า 2 รายการ
				var checkDuplicate model.CheckDuplicateWebhookAndAdminRecord
				checkDuplicate.FromAccountNumber = bodyCreateState.FromAccountNumber
				checkDuplicate.FromBankId = bodyCreateState.FromBankId
				checkDuplicate.Amount = bodyCreateState.Amount
				checkDuplicate.TransactionAt = bodyCreateState.TransferAt
				checkDuplicate.MemberCode = user.MemberCode
				checkDuplicate.CheckFromWhere = "WEBHOOK"
				checkDuplicate.ToBankId = &systemAccount.BankId
				duplicateFromAdminRecord, _ := s.repo.CheckDuplicateWebhookAndAdminRecord2(checkDuplicate)
				if duplicateFromAdminRecord.Id != 0 {
					// update statement Id in to transaction
					err := s.repo.UpdateTransactionDuplicateWithExternalMatch(duplicateFromAdminRecord.Id, *insertId)
					if err != nil {
						return nil, nil
					}

					statusLog := "SUCCESS"
					jsonPayLoad := fmt.Sprintf("DUPLICATE WITH ADMIN BANK TRANS ID: %v  AND STATEMENT ID: %v ", duplicateFromAdminRecord.Id, *insertId)
					jsonRequest := helper.StructJson(data)
					logType := "DUPLICATE_WITH_ADMIN_RECORD"
					var createLog model.BankTransactionLogCreateRequest
					createLog.Status = &statusLog
					createLog.JsonPayload = &jsonPayLoad
					createLog.JsonRequest = &jsonRequest
					createLog.LogType = &logType

					if _, err := s.CreateBankTransactionLog(createLog); err != nil {
						return nil, err
					}
					log.Println("GetWebhookStatementByExternalId.duplicateFromRecord", helper.StructJson(duplicateFromAdminRecord))
					return nil, badRequest("พบรายการซ้ำกับรายการที่มีอยู่ในระบบ")
				}
			}

			// [Create Transaction : Check Statement Type Transfer In only]

			if user.Id != 0 && configWeb != nil {
				// [********] ฝากครั้งแรกขั้นต่ำ ปรับ flow เช็ค user ให้เช็คจากรายการฝากครั้งแรกจริงๆ ไม่เช็คจากรหัสที่ได้รับ
				if err := s.CheckFirstMinimunDeposit(user.Id, data.Amount, *configWeb); err != nil {
					return nil, badRequest("LOW_NOT_MEMBER_FIRST_DEPOSIT")
				}
			} else {
				log.Println("CreateBankStatementFromWebhookAndAutoSmsMode.CheckFirstMinimunDeposit", "CANT_CHECK_FIRST_MINIMUM_DEPOSIT_ON_EMPTY_CONFIG")
			}

			// ===========================================================
			// [Create Transaction + Create New MemberCode]
			// auto_user_approve_type_id
			// 1 รับ user ทันทีหลังสมัครสมาชิก
			// 2 รับ user หลังจากฝากครั้งแรก
			// ** แต่ไม่จำเป็นต้องเช็ค config เราจะบังคับให้มี member code ทุกครั้งที่มีการฝากเข้ามา ถ้ายังไม่มีให้สร้างให้ใหม่
			// race condition deposit
			actionAt := startTime.UTC().Format("************")
			var createBody model.RaceActionCreateBody
			createBody.Name = "CreateBankStatementFromWebhookAndAutoSmsMode"
			createBody.JsonRequest = helper.StructJson(bodyCreateState.Id)
			createBody.Status = "PENDING"
			createBody.ActionKey = fmt.Sprintf("DEPOSIT_T%s_U%d_CD%f", actionAt, user.Id, data.Amount)
			createBody.UnlockAt = time.Now().UTC().Add(time.Second * 1)
			if _, err := s.repo.GetRaceActionIdByActionKey(createBody.ActionKey); err != gorm.ErrRecordNotFound {
				log.Println("CreateBankStatementFromWebhookAndAutoSmsMode.GetRaceActionIdByActionKey", err)
			}
			// create race condition
			actionId, err := s.repo.CreateRaceCondition(createBody)
			if err != nil {
				log.Println("CreateBankStatementFromWebhookAndAutoSmsMode.CreateRaceCondition", err)
			}
			if actionId > 0 {

				if user.MemberCode == "" {
					memberCode, err := s.userService.GenUniqueUserMemberCode(user.Id)
					if err != nil {
						log.Println("CreateBankStatementFromWebhookAndAutoSmsMode.GenUniqueUserMemberCode", err)
						webhookLogMessage := fmt.Sprintf("GenUniqueUserMemberCode, ERROR: %s", err)
						return nil, errors.New(webhookLogMessage)
					}
					user.MemberCode = *memberCode
				}

				// [Create Transaction : Create Transaction]
				// CreateBankStatementFromWebhookAndAutoSmsMode.createAutoDepositTransaction#3.transId
				transId, err := s.createAutoDepositTransaction(*user, bodyCreateState, admin)
				if err != nil {
					webhookLogMessage := fmt.Sprintf("CreateAutoDepositTransaction, ERROR: %s", err)
					return nil, errors.New(webhookLogMessage)
				}

				// DECREASE FASTBANK CREDIT 1.FROM WEBHOOK+MATCHED
				if err := s.repo.DecreaseFastbankCredit(1); err != nil {
					log.Println("UserCreateWithdrawTransaction.DECREASE_FASTBANK_CREDIT_ERROR", err)
				}

				// ===========================================================

				// [Confirmed : MatchOwner]
				confirmedAt := time.Now()
				var statementMatchRequest model.BankStatementMatchRequest
				statementMatchRequest.ConfirmedAt = confirmedAt
				statementMatchRequest.UserId = user.Id
				var setIdAuto int64 = 0
				statementMatchRequest.ConfirmedByAdminId = &setIdAuto
				if err := s.SetStatementOwnerMatched(statement.Id, statementMatchRequest, model.USE_ENDING_NOTI); err != nil {
					webhookLogMessage := fmt.Sprintf("SetStatementOwnerMatched, ERROR: %s", err)
					return nil, errors.New(webhookLogMessage)
				}
				// ===========================================================
				// [Get promotion]
				var promotionWebUserId int64
				GetUserPromotion, _ := s.promotionWebService.GetDepositCurrentProcessingUserPromotion(user.Id)
				if GetUserPromotion != nil {
					promotionWebUserId = GetUserPromotion.Id

					errUpdate := s.repo.UpdatePromotionToBankTransaction(*transId, promotionWebUserId)
					if errUpdate != nil {
						log.Println("CreateBankStatementFromWebhookAndAutoSmsMode.UpdatePromotionToBankTransaction", err)
					}
				}

				// [Increase Credit]
				var userCreditReq model.UserTransactionCreateRequest
				userCreditReq.UserId = user.Id
				userCreditReq.TypeId = model.CREDIT_TYPE_DEPOSIT
				userCreditReq.RefId = transId
				userCreditReq.PromotionId = &promotionWebUserId
				userCreditReq.Amount = statement.Amount
				userCreditReq.AccountId = &systemAccount.Id

				userCreditReq.Detail = fmt.Sprintf("%s:%s => %s:%s", user.BankName, statement.FromAccountNumber, systemAccount.BankName, systemAccount.AccountNumber)
				userCreditReq.IsAdjustAuto = true
				autoBotId := int64(0)
				userCreditReq.TransferAt = &bodyCreateState.TransferAt
				userCreditReq.CreateBy = &autoBotId
				userCreditReq.ConfirmBy = &autoBotId
				userCreditReq.StartWorkAt = startTime // เริ่มนับตอนได้รับ Webhook
				if agentResp, err := s.repo.IncreaseUserCredit(userCreditReq); err != nil {
					webhookLogMessage := fmt.Sprintf("IncreaseUserCredit, ERROR: %s", err)
					return nil, errors.New(webhookLogMessage)
				} else {
					// FASTBANK_SUCCESS
					lineResultCredit = agentResp.AgentAfterAmount
					if err := s.repo.UpdateDepositTransactionStatusFromAgent(*transId, *agentResp); err != nil {
						log.Println("CreateBankStatementFromWebhookAndAutoSmsMode.UpdateDeporsitTransactionStatusFromAgent", err)
					}
					// Set FastBank-READ
					var setReadRequest model.ExternalStatementSetReadBody
					setReadRequest.AccountNo = systemAccount.AccountNumber
					setReadRequest.StatementId = statement.ExternalId
					setReadRequest.UsedCredit = true
					if err := s.repo.SetExternalStatementRead(setReadRequest); err != nil {
						log.Println("CreateBankStatementFromWebhookAndAutoSmsMode.SetExternalStatementRead", err)
					}
				}
				// ===========================================================
				isFirstDeposit := s.repo.IsFirstDeposit(user.Id)
				if isFirstDeposit {
					// [Create Commission + Turnover]
					// if err := s.userFirstDepositBonus(bodyCreateState.Amount, user.Id, *transId); err != nil {
					// 	webhookLogMessage := fmt.Sprintf("CreateBankStatementFromWebhookAndAutoSmsMode.userFirstDepositBonus, ERROR: %s", err)
					// 	return nil, errors.New(webhookLogMessage)
					// }
					var bonusReq model.UserFirstDepositCreateRequest
					bonusReq.UserId = user.Id
					bonusReq.TransactionId = transId
					bonusReq.TransferAt = bodyCreateState.TransferAt
					bonusReq.Amount = bodyCreateState.Amount
					bonusReq.Remark = "CreateBankStatementFromWebhookAndAutoSmsMode"
					if err := SetFirstDepositBonus(s.repo, isFirstDeposit, bonusReq); err != nil {
						webhookLogMessage := fmt.Sprintf("CreateBankStatementFromWebhookAndAutoSmsMode.userFirstDepositBonus, ERROR: %s", err)
						log.Println("SetFirstDepositBonus", webhookLogMessage)
						// return nil, errors.New(webhookLogMessage)
					}
				} else {
					// [Create Commission EVERY DEPOSIT]
					if user.UserTypeName == "ALLIANCE" {
						if err := s.allianceService.NoUseAlUpdateCommission(user.Id, bodyCreateState.Amount); err != nil {
							log.Println("CreateBankStatementFromWebhookAndAutoSmsMode.AlUpdateCommission", err)
							webhookLogMessage := fmt.Sprintf("AlUpdateCommission, ERROR: %s", err)
							return nil, errors.New(webhookLogMessage)
						}
					}
				}
				// ===========================================================
				// [PromtionChecker]
				var checkUserPromotionBody model.CheckUserPromotionBody
				checkUserPromotionBody.UserId = user.Id
				_, err = s.promotionWebService.CheckUserPromotion(checkUserPromotionBody)
				if err != nil {
					log.Println("CreateBankStatementFromWebhookAndAutoSmsMode.CheckUserPromotion", err)
				}
				// ===========================================================
				// [Lucky Wheel]
				var luckyWheelBody model.ActivityLuckyWheelRoundUserRequest
				luckyWheelBody.UserId = user.Id
				luckyWheelBody.ConditionId = model.ACTIVITY_LUCKY_WHEEL_SETTING_CONDITION_DEPOSIT
				luckyWheelBody.ConditionAmount = bodyCreateState.Amount
				if err := s.activityLuckyWheelService.CreateRoundActivityLuckyWheel(luckyWheelBody); err != nil {
					log.Println("CreateBankStatementFromWebhookAndAutoSmsMode.CreateRoundActivityLuckyWheel", err)
				}
				// [turnOver] ถ้ามีการฝากเงิน ให้สร้าง TurnOver
				if err := CreateTurnOverFromSuccessDeposit(repository.NewTurnoverRepository(s.shareDb), user.Id, bodyCreateState.Amount, *transId); err != nil {
					log.Println("CreateBankStatementFromWebhookAndAuto.CreateTurnOverFromSuccessDeposit", err)
				}

				// ===========================================================
				// [TIER]
				if err := s.repo.IncreaseUserTierDepositAmount(user.Id, bodyCreateState.Amount); err != nil {
					log.Println("CreateBankStatementFromWebhookAndAuto.IncreaseUserTierDepositAmount", err)
				}
				// ===========================================================
				var updateConfirm model.UpdateConfirmSmsModeDepositBody
				updateConfirm.Id = &smsModeTransaction.Id
				updateConfirm.RefId = transId
				if err := s.repo.UpdateConfirmSmsModeDeposit(updateConfirm); err != nil {
					return nil, nil
				}
				// ===========================================================
				// [Notify]
				endTime := time.Now()
				elapsed := endTime.Sub(startTime)
				elapsedSeconds := elapsed.Seconds()
				timeElapsed := fmt.Sprintf("%.2f", elapsedSeconds)
				// UpdateAutoProcessTimer(timer string, id int64) error
				if err := s.repo.UpdateAutoProcessTimer(timeElapsed, *transId); err != nil {
					webhookLogMessage := fmt.Sprintf("UpdateAutoProcessTimer, ERROR: %s", err)
					return nil, errors.New(webhookLogMessage)
				}
				var externalNoti model.NotifyExternalNotificationRequest
				externalNoti.TypeNotify = model.IsDepositAfterCredit
				externalNoti.Amount = statement.Amount
				externalNoti.MemberCode = user.MemberCode
				externalNoti.UserCredit = lineResultCredit
				externalNoti.ConfirmedByAdminId = 0
				externalNoti.TimerCounter = timeElapsed
				externalNoti.TransferDateTime = bodyCreateState.TransferAt.Add(time.Hour * 7).Format("2006-01-02 15:04:05")
				externalNoti.ActionTime = time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02 15:04:05")

				externalNoti.WebScoket.UserID = user.Id
				externalNoti.WebScoket.Amount = statement.Amount
				externalNoti.WebScoket.MemberCode = user.MemberCode
				externalNoti.WebScoket.AlertType = "DEPOSIT"
				if err := s.notiService.ExternalNotification(externalNoti); err != nil {
					log.Println("FailedNotify", err)
				}
			} else {
				// check dupicate and make it only in transaction not auto
				if _, err := s.CreateDepositTransactionNoOwner(bodyCreateState, admin, model.TRANS_STATUS_PENDING); err != nil {
					webhookLogMessage := fmt.Sprintf("DuplicateCreateDepositTransactionNoOwner, ERROR: %s", err)
					return nil, errors.New(webhookLogMessage)
				}
			}

		}

		if bodyCreateState.StatementTypeId == model.STATEMENT_TYPE_TRANSFER_OUT {
			// ExternalMatchWithdrawTransaction(bodyCreateState model.BankStatementCreateBody) error
			if err := s.ExternalMatchWithdrawTransaction(bodyCreateState); err != nil {
				webhookLogMessage := fmt.Sprintf("ExternalMatchWithdrawTransaction, ERROR: %s", err)
				log.Println(webhookLogMessage)
			}
		}
	} else {
		// [Create Statement : Check Exist Statement]
		return nil, errors.New("statement already exists")
	}

	return &statementId, nil
}

func (s *accountingService) MigrateOldBankAccount() error {

	bankAccount, err := s.repo.GetBankAccountMigration()
	if err != nil {
		return internalServerError(err)
	}

	bankCurrency, err := s.repo.GetBankCurreny()
	if err != nil {
		return internalServerError(err)
	}

	getBankAccountShowBankByBank, err := s.repo.GetBankAccountShowBankByBank()
	if err != nil {
		return internalServerError(err)
	}

	if len(getBankAccountShowBankByBank) > 0 {
		for _, item := range getBankAccountShowBankByBank {
			for _, bankAccountItem := range bankAccount {
				if item.BankAccountId == bankAccountItem {
					if err := s.repo.DeleteBankAccountShowBankByBankAccountId(item.BankAccountId); err != nil {
						return internalServerError(err)
					}
				}
			}
		}
	}

	var showBankBody []model.CreateBankAccountShowBank
	for _, bankAccountItem := range bankAccount {
		for _, bankCurrencyItem := range bankCurrency {
			showBankBody = append(showBankBody, model.CreateBankAccountShowBank{
				BankAccountId: bankAccountItem,
				BankId:        bankCurrencyItem.Id,
			})
		}
	}

	if err := s.repo.CreateBankAccountShowBank(showBankBody); err != nil {
		return internalServerError(err)
	}

	return nil
}

func (s *accountingService) UploadImageToCloudflareBankAccountQr(imageFileBody *http.Request) (*model.FileUploadResponse, error) {

	fileReader, newFileName, err := imageFileBody.FormFile("file")
	if err != nil {
		log.Println("UploadFileCloudFlare.ERROR", err)
		return nil, err
	}

	filename := &newFileName.Filename

	dbName := os.Getenv("DB_NAME")

	// [set imageCloudFlarePathName]
	pathName := fmt.Sprintf("cbgame/%v/bank-account/upload/image/", dbName)
	//! ส่ง Id กับไฟล์ reader
	var newImageId *model.FileUploadResponse
	fileData, err := s.repo.UploadImageToCloudflare(pathName, *filename, fileReader)
	if err != nil {
		log.Println("UploadFileCloudFlare.ERROR", err)
		return nil, badRequest("UNABLE_TO_UPLOAD_FILE")
	}

	newImageId = &model.FileUploadResponse{
		ImageUrl: fileData.FileUrl,
	}

	return newImageId, nil
}

func (s *accountingService) UploadImageToS3BankAccountQr(imageFileBody *http.Request) (*model.FileUploadResponse, error) {

	fileReader, newFileName, err := imageFileBody.FormFile("file")
	if err != nil {
		log.Println("UploadFileS3.ERROR", err)
		return nil, err
	}

	filename := &newFileName.Filename

	dbName := os.Getenv("DB_NAME")

	pathName := fmt.Sprintf("cbgame/%v/bank-account/", dbName)
	var newImageId *model.FileUploadResponse
	fileData, err := s.repo.UploadImageToS3(pathName, *filename, fileReader)
	if err != nil {
		log.Println("UploadFileS3.ERROR", err)
		return nil, badRequest("UNABLE_TO_UPLOAD_FILE")
	}

	newImageId = &model.FileUploadResponse{
		ImageUrl: fileData.ImageUrl,
	}

	return newImageId, nil
}

func (s *accountingService) UpdateBankAccountIsShowBank(req model.UpdateBankAccountIsShowBankRequest) error {

	account, err := s.repo.GetBankAccountById(req.Id)
	if err != nil {
		return internalServerError(err)
	}

	showTxt := "ไม่แสดง"
	if req.IsShowFront != nil && *req.IsShowFront {
		showTxt = "ให้แสดงที่หน้าเว็บ"
	}

	// [ADMIN_ACTION] 2025-05-06 เพิ่ม log admin action
	var adminActionCreateBody model.AdminActionCreateRequest
	adminActionCreateBody.AdminId = req.AdminId
	adminActionCreateBody.TypeId = model.ADMIN_ACTION_ACCOUNT_MANAGE
	adminActionCreateBody.Detail = fmt.Sprintf("ปรับการแสดงผล %s ชื่อบัญชี %s", showTxt, account.AccountName)
	adminActionCreateBody.JsonInput = helper.StructJson(req)
	if _, err := s.adminAction.CreateSuccessAdminAction(adminActionCreateBody); err != nil {
		log.Println("UpdateBankAccountIsShowBank.CreateSuccessAdminAction.ERROR", err)
	}

	var query model.UpdateBankAccountIsShowBankRequest
	query.Id = req.Id
	query.IsShowFront = req.IsShowFront
	if err := s.repo.UpdateBankAccountIsShowBank(query); err != nil {
		return internalServerError(err)
	}
	return nil
}

func (s *accountingService) TotalBankStatementSummary(req model.TotalBankStatementRequest) (*model.TotalBankTransactionSummaryResponse, error) {

	record, err := s.repo.TotalBankStatementSummary(req)
	if err != nil {
		return nil, internalServerError(err)
	}

	total := record.TotalAdminDepositAmount + record.TotalAutoDepositAmount
	if total == record.TotalDepositAmount {
		record.IsTotalMatchTotal = true
	}

	return record, nil
}
