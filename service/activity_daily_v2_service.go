package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"errors"
	"fmt"
	"log"
	"math"
	"time"

	"gorm.io/gorm"
)

type ActivityDailyV2Service interface {
	// options
	GetActivityDailyV2TotalConditionOptions() ([]model.SelectOptions, error)
	// main
	CreateActivityDailyV2Total(req model.CreateActivityDailyV2Request) error
	GetTurnoverUserActivityDailyV2(req model.GetTurnoverUserActivityDailyV2Request) (*model.SuccessWithPagination, error)

	GetActivityDailyV2() (*model.GetActivityDailyV2TotalResponse, error)
	UserReviceActivityDailyV2(req model.UserReviceActivityDailyV2Request) error
	WebGetActivityDailyV2(userId int64) (*model.WebGetActivityDailyV2Response, error)
}

type activityDailyV2Service struct {
	shareDb *gorm.DB
	repo    repository.ActivityDailyV2Repository
}

func NewActivityDailyV2Service(
	shareDb *gorm.DB,
	repo repository.ActivityDailyV2Repository,
) ActivityDailyV2Service {
	return &activityDailyV2Service{shareDb, repo}
}

func (s *activityDailyV2Service) CreateActivityDailyV2Total(req model.CreateActivityDailyV2Request) error {

	getChangeCountTime, err := s.repo.GetChangeCountTime()
	if err != nil {
		return err
	}

	if int(req.TotalAbleReviceNo) != len(req.ActivityDailyV2Detail) {
		return errors.New("จำนวนตั้งค่าไม่ตรงกับจำนวนข้อมูลที่ส่งมา")
	}

	newChangeCountTime := getChangeCountTime.ChangeCountTime + 1

	var createActivityDailyV2TotalBody model.CreateActivityDailyV2TotalBody
	createActivityDailyV2TotalBody.TotalAbleReviceNo = req.TotalAbleReviceNo
	createActivityDailyV2TotalBody.ChangeCountTime = newChangeCountTime
	createActivityDailyV2TotalBody.ActivityDailyV2TotalConditionId = req.ActivityDailyV2TotalConditionId
	createActivityDailyV2TotalBody.ConditionAmount = req.ConditionAmount
	createActivityDailyV2TotalBody.IsActive = req.IsActive
	createActivityDailyV2TotalBody.UpdatedAt = &req.UpdatedAt
	createActivityDailyV2TotalBody.UpdatedById = &req.UpdatedById

	var createActivityDailyV2Body []model.CreateActivityDailyV2Body
	for _, item := range req.ActivityDailyV2Detail {
		var createActivityDailyV2 model.CreateActivityDailyV2Body
		createActivityDailyV2.ChangeCountTime = createActivityDailyV2TotalBody.ChangeCountTime
		createActivityDailyV2.NoNumber = item.NoNumber
		createActivityDailyV2.CreditAmount = item.CreditAmount
		createActivityDailyV2.IsHiglight = item.IsHiglight
		createActivityDailyV2.CreatedAt = req.UpdatedAt
		createActivityDailyV2.CreatedById = req.UpdatedById
		createActivityDailyV2Body = append(createActivityDailyV2Body, createActivityDailyV2)
	}

	if err := s.repo.CreateActivityDailyV2Total(createActivityDailyV2TotalBody, createActivityDailyV2Body); err != nil {
		return err
	}

	return nil
}

func (s *activityDailyV2Service) GetActivityDailyV2() (*model.GetActivityDailyV2TotalResponse, error) {

	getActivityDailyV2, err := s.repo.GetActivityDailyV2()
	if err != nil {
		return nil, err
	}

	return getActivityDailyV2, nil
}

func (s *activityDailyV2Service) WebGetActivityDailyV2(userId int64) (*model.WebGetActivityDailyV2Response, error) {

	listWebActivityV2, detail, err := s.repo.WebGetActivityDailyV2()
	if err != nil {
		return nil, err
	}

	getNextNoNumber, _, err := s.repo.GetUserActivityDailyV2NextNoNumber(userId)
	if err != nil {
		return nil, err
	}

	var result []model.WebGetActivityDailyV2ListResponse
	for _, item := range listWebActivityV2 {
		var webActivityDailyV2 model.WebGetActivityDailyV2ListResponse
		webActivityDailyV2.NoNumber = item.NoNumber
		webActivityDailyV2.CreditAmount = item.CreditAmount
		webActivityDailyV2.IsHiglight = item.IsHiglight
		if item.NoNumber < getNextNoNumber.NoNumber {
			webActivityDailyV2.AlreadyReviced = true
			webActivityDailyV2.TodayAvailable = false
		} else if item.NoNumber == getNextNoNumber.NoNumber {
			webActivityDailyV2.AlreadyReviced = false
			webActivityDailyV2.TodayAvailable = true
		}
		result = append(result, webActivityDailyV2)
	}

	var fristNoNumberHighlight int
	if len(listWebActivityV2) > 0 && listWebActivityV2[0].NoNumber == 1 {
		listWebActivityV2[0].IsHiglight = 1
		fristNoNumberHighlight = listWebActivityV2[0].IsHiglight

	}

	percentage, last, next := calculatePercentage(listWebActivityV2, getNextNoNumber.NoNumber)

	var webGetActivityDailyV2Response model.WebGetActivityDailyV2Response
	webGetActivityDailyV2Response.PercentCal = int(percentage)
	webGetActivityDailyV2Response.WebGetActivityDailyV2ListResponse = result
	if last == 1 {
		webGetActivityDailyV2Response.LastHighlight = int64(fristNoNumberHighlight)
	} else {

		webGetActivityDailyV2Response.LastHighlight = last
	}
	webGetActivityDailyV2Response.NexHighlight = next

	webGetActivityDailyV2Response.ActivityDailyV2TotalConditionId = detail.ActivityDailyV2TotalConditionId
	webGetActivityDailyV2Response.ConditionAmount = detail.ConditionAmount
	webGetActivityDailyV2Response.ActivityDailyV2TotalConditionName = detail.ActivityDailyV2TotalConditionName

	return &webGetActivityDailyV2Response, nil
}

func calculatePercentage(listWebActivityV2 []model.WebActivityDailyV2Detail, getNextNoNumber int64) (float64, int64, int64) {
	adjustedNoNumber := getNextNoNumber

	var lastHighlight, nextHighlight int64
	var foundLast, foundNext bool

	// Iterate backward to find the last highlight before adjustedNoNumber
	for i := len(listWebActivityV2) - 1; i >= 0; i-- {
		if listWebActivityV2[i].NoNumber <= adjustedNoNumber && listWebActivityV2[i].IsHiglight == 1 {
			lastHighlight = listWebActivityV2[i].NoNumber
			foundLast = true
			break
		}
	}

	// Iterate forward to find the next highlight after adjustedNoNumber
	for i := 0; i < len(listWebActivityV2); i++ {
		if listWebActivityV2[i].NoNumber > adjustedNoNumber && listWebActivityV2[i].IsHiglight == 1 {
			nextHighlight = listWebActivityV2[i].NoNumber
			foundNext = true
			break
		}
	}

	// If both highlights are found, calculate the percentage
	if foundLast && foundNext {
		rangeDistance := float64(nextHighlight - lastHighlight)
		currentPosition := float64(adjustedNoNumber - lastHighlight)
		percentage := ((currentPosition + 1) / (rangeDistance + 1)) * 100
		if lastHighlight == adjustedNoNumber && adjustedNoNumber != 1 {
			percentage = 100
		}

		return math.Round(percentage*100) / 100, lastHighlight, nextHighlight // Round to 2 decimal places
	}

	// If no valid percentage can be calculated
	return 0, 0, 0
}

func (s *activityDailyV2Service) UserReviceActivityDailyV2(req model.UserReviceActivityDailyV2Request) error {

	user, err := s.repo.GetUserById(req.UserId)
	if err != nil {
		return err
	}
	if user.MemberCode == nil {
		return badRequest("USER_NOT_MEMBER_PLEASE_DEPOSIT")
	}

	// ทำแบบนี้เพราะสามารถกลับมากดวันที่แรกได้เมื่อครบทั้งหมด
	dateReceived := time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02")
	// check on that day have revice or not
	checkUserActivityDailyV2AlreadyRevice, err := s.repo.CheckUserActivityDailyV2AlreadyRevice(req.UserId)
	if err != nil {
		return err
	}

	if checkUserActivityDailyV2AlreadyRevice {
		return errors.New("USER_ALREADY_REVICE_TO_DAY")
	}

	// check what is the next noNumber
	getUserActivityDailyV2NextNoNumber, detail, err := s.repo.GetUserActivityDailyV2NextNoNumber(req.UserId)
	if err != nil {
		return err
	}

	var findHighestCreditAmount float64 // ฝากขั้นต่ำ
	var findSumCreditAmount float64     // ฝากรวม (ตามยอด)
	if detail.ActivityDailyV2TotalConditionId != model.ACTIVITY_DAILY_V2_TOTAL_CONDITION_NO_CONDITION {
		getUserTodayDepositForActivityDaily, err := s.repo.GetUserTodayDepositForActivityDaily(req.UserId)
		if err != nil {
			return err
		}

		for _, item := range getUserTodayDepositForActivityDaily {
			findSumCreditAmount += item.CreditAmount
			if item.CreditAmount > findHighestCreditAmount {
				findHighestCreditAmount = item.CreditAmount
			}
		}
	}
	if detail.ActivityDailyV2TotalConditionId == model.ACTIVITY_DAILY_V2_TOTAL_CONDITION_MIN_DEPOSIT {
		if findHighestCreditAmount < detail.ConditionAmount {
			return badRequest("INSUFFICIENT_DEPOSIT_AMOUNT")
		}
	} else if detail.ActivityDailyV2TotalConditionId == model.ACTIVITY_DAILY_V2_TOTAL_CONDITION_OVERALL_DEPOSIT {
		if findSumCreditAmount < detail.ConditionAmount {
			return badRequest("INSUFFICIENT_DEPOSIT_AMOUNT")
		}
	}

	// race condition sigle request
	var createBody model.RaceActionCreateBody
	createBody.Name = "ACTIVITY_DAILY_V2"
	createBody.JsonRequest = helper.StructJson(req)
	createBody.Status = "SUCCESS"
	createBody.ActionKey = fmt.Sprintf("ACTIVITY_DAILY_V2_U%d_DATE_%s", req.UserId, dateReceived)
	createBody.UnlockAt = time.Now().UTC().Add(time.Second * 1)
	if _, err := s.repo.GetRaceActionIdByActionKey(createBody.ActionKey); err != nil {
		log.Println("CreateBankStatementFromWebhookAndAuto.GetRaceActionIdByActionKey", err)
		if err != gorm.ErrRecordNotFound {
			return internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
		}
	} else {
		return internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
	}
	actionId, err := s.repo.CreateRaceCondition(createBody)
	if err != nil {
		log.Println("CreateBankStatementFromWebhookAndAuto.CreateRaceCondition", err)
		return internalServerError(errors.New("CANNOT_CREATE_ACTION"))
	}
	if actionId == 0 {
		return badRequest("PLEASE_WAIT_ONE_MINUTE_YOU_JUST_CLICKED_A_MOMENT_AGO_PLEASE_CHECK_THE_STATEMENT_AGAIN")
	}

	// create user revice
	var createActivityDailyV2User model.CreateActivityDailyV2User
	createActivityDailyV2User.ActivityDailyV2Id = getUserActivityDailyV2NextNoNumber.Id
	createActivityDailyV2User.ChangeCountTime = getUserActivityDailyV2NextNoNumber.ChangeCountTime
	createActivityDailyV2User.NoNumber = getUserActivityDailyV2NextNoNumber.NoNumber
	createActivityDailyV2User.UserId = req.UserId
	createActivityDailyV2User.CreditAmount = getUserActivityDailyV2NextNoNumber.CreditAmount
	createActivityDailyV2User.ReceivedAt = time.Now().UTC()
	createActivityDailyV2User.DateReceived = dateReceived
	insertId, err := s.repo.CreateActivityDailyV2User(createActivityDailyV2User)
	if err != nil {
		return err
	}

	// increase user credit
	if getUserActivityDailyV2NextNoNumber.CreditAmount > 0 {
		var userCreditReq model.UserTransactionCreateRequest
		userCreditReq.RefId = &insertId
		userCreditReq.UserId = req.UserId
		userCreditReq.TypeId = model.CREDIT_TYPE_DAILY_ACTIVITY_BONUS // เพิ่ม type
		userCreditReq.BonusAmount = getUserActivityDailyV2NextNoNumber.CreditAmount
		userCreditReq.Detail = "โบนัสกิจกรรมรายวัน v2"
		userCreditReq.StartWorkAt = time.Now().UTC() // เริ่มนับตอนกดยินยัน
		_, err = s.repo.IncreaseUserCredit(userCreditReq)
		if err != nil {
			return err
		} else {
			err := s.CreateTurnOverFromActivityDailyV2(req.UserId, getUserActivityDailyV2NextNoNumber.CreditAmount, insertId, getUserActivityDailyV2NextNoNumber.NoNumber)
			if err != nil {
				return err
			}
		}
	}

	return nil
}

func (s *activityDailyV2Service) CreateTurnOverFromActivityDailyV2(userId int64, bonusAmount float64, refId int64, noNumber int64) error {

	// serviceTurnover
	checkTurn, err := GetTurnoverSetting(repository.NewTurnoverRepository(s.shareDb))
	if err != nil {
		return nil
	}
	if checkTurn.TidturnActivityDailyV2Percent > 0 {

		tidTurn := (bonusAmount * float64(checkTurn.TidturnActivityDailyV2Percent) / 100)

		tidTurn = math.Ceil(tidTurn)

		createdTime := time.Now().UTC()
		var createBody model.TurnoverUserStatementCreateBody
		createBody.UserId = userId
		createBody.RefTypeId = refId
		createBody.TypeId = model.TURN_BONUS_ACTIVITY_DAILY_V2
		createBody.Name = model.TURNOVER_CATE_ACTIVITY_DAILY_V2
		createBody.PromotionName = fmt.Sprintf("รายได้กิจกรรมรายวัน v2 ครั้งที่: %d", noNumber)
		createBody.BonusAmount = bonusAmount
		createBody.StatusId = model.TURNOVER_STATEMENT_STATUS_PENDING
		createBody.StartTurnAmount = tidTurn
		createBody.StartTurnAt = &createdTime
		createBody.TotalTurnAmount = tidTurn
		if _, err := s.repo.CreateTurnoverUserStatement(createBody); err != nil {
			return err
		}
	} else {
		createdTime := time.Now().UTC()
		var createBody model.TurnoverUserStatementCreateBody
		createBody.UserId = userId
		createBody.RefTypeId = refId
		createBody.TypeId = model.TURN_BONUS_ACTIVITY_DAILY_V2
		createBody.Name = model.TURNOVER_CATE_ACTIVITY_DAILY_V2
		createBody.PromotionName = fmt.Sprintf("รายได้กิจกรรมรายวัน v2 ครั้งที่: %d", noNumber)
		createBody.BonusAmount = 0
		createBody.StatusId = model.TURNOVER_STATEMENT_STATUS_PENDING
		createBody.StartTurnAmount = 0
		createBody.StartTurnAt = &createdTime
		createBody.TotalTurnAmount = 0
		craeteTurnId, err := s.repo.CreateTurnoverUserStatement(createBody)
		if err != nil {
			return err
		}

		var updateTurnoverUserStatement model.TurnoverUserStatementUpdateBody
		setTotalTurnAmount := 0.0
		setTimeTurnAt := time.Now().UTC()
		updateTurnoverUserStatement.StatusId = model.TURNOVER_STATEMENT_STATUS_COMPLETED
		updateTurnoverUserStatement.TotalTurnAmount = &setTotalTurnAmount
		updateTurnoverUserStatement.EndTurnAt = &setTimeTurnAt
		if err := s.repo.UpdateTurnoverUserStatement(*craeteTurnId, updateTurnoverUserStatement); err != nil {
			log.Println("UpdateTurnoverUserStatement", err)
		}

		// create turnover withdraw log
		var createTurnoverWithDrawLog model.CreateTurnoverUserWithdrawLog
		createTurnoverWithDrawLog.UserId = userId
		createTurnoverWithDrawLog.LogKey = fmt.Sprintf("BONUS_ACTIVITY_DAILY_V2_U%d_D%s", userId, time.Now().UTC().Format("20060102150405"))
		createTurnoverWithDrawLog.TotalWithdrawPrice = 0
		createTurnoverWithDrawLog.CurrentTurn = 0
		createTurnoverWithDrawLog.PlayTotal = 0
		createTurnoverWithDrawLog.LastPlayY = 0
		createTurnoverWithDrawLog.LastTotalX = 0
		if _, err = s.repo.CreateTurnoverUserWithdrawLog(createTurnoverWithDrawLog); err != nil {
			log.Println("CreateTurnOverFromActivityDailyV2.CreateTurnoverUserWithdrawLog", err)
		}

	}

	return nil
}

func (s *activityDailyV2Service) GetTurnoverUserActivityDailyV2(req model.GetTurnoverUserActivityDailyV2Request) (*model.SuccessWithPagination, error) {

	if err := helper.Pagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	turnoverUserActivityDaily, total, err := s.repo.GetTurnoverUserActivityDailyV2(req)
	if err != nil {
		return nil, err
	}

	var successWithPagination model.SuccessWithPagination
	successWithPagination.List = turnoverUserActivityDaily
	successWithPagination.Total = total
	successWithPagination.Message = "success"

	return &successWithPagination, nil

}

func (s *activityDailyV2Service) GetActivityDailyV2TotalConditionOptions() ([]model.SelectOptions, error) {

	option, err := s.repo.GetActivityDailyV2TotalConditionOptions()
	if err != nil {
		return nil, err
	}
	return option, nil
}
