package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"errors"
)

type RecommendService interface {
	GetRecommendList(query model.RecommendQuery) ([]model.RecommendList, int64, error)
	GetListForFront() (*model.RecommendListResponse, error)
	CreateRecommend(user model.CreateRecommend) error
	UpdateRecommend(id int64, body model.RecommendUpdateRequest) error
	DeleteRecommend(id int64) error
	// [ADMIN_LOG]
	LogAdmin(name string, adminId int64, req interface{}) error
}

type recommendService struct {
	repo repository.RecommendRepository
}

func NewRecommendService(
	repo repository.RecommendRepository,
) RecommendService {
	return &recommendService{repo}
}

func (s recommendService) GetRecommendList(query model.RecommendQuery) ([]model.RecommendList, int64, error) {

	if err := helper.Pagination(&query.Page, &query.Limit); err != nil {
		return nil, 0, err
	}

	return s.repo.GetRecommendList(query)
}

func (s recommendService) GetListForFront() (*model.RecommendListResponse, error) {

	list, err := s.repo.GetListForFront()
	if err != nil {
		return nil, err
	}

	var result model.RecommendListResponse
	result.Result = list

	return &result, nil
}

func (s recommendService) CreateRecommend(body model.CreateRecommend) error {

	if err := s.repo.CreateRecommend(body); err != nil {
		return err
	}
	return nil
}

func (s recommendService) UpdateRecommend(id int64, body model.RecommendUpdateRequest) error {

	if err := s.repo.UpdateRecommend(id, body); err != nil {
		return err
	}
	return nil
}

func (s recommendService) DeleteRecommend(id int64) error {

	if _, err := s.repo.GetChannelById(id); err != nil {
		return errors.New("CHANNEL_NOT_FOUND")
	}

	// check if user has this recommend
	count, err := s.repo.CountUserRecommendChannel(id)
	if err != nil {
		return err
	}

	if count == 0 {
		if err := s.repo.DeleteRecommend(id); err != nil {
			return err
		}
	} else {
		return errors.New("CANT_DELETE_USING_CHANNEL")
	}
	return nil
}

func (s recommendService) LogAdmin(name string, adminId int64, req interface{}) error {

	var createBody model.AdminLogCreateBody
	createBody.Name = name
	createBody.AdminId = adminId
	createBody.JsonReq = helper.StructJson(req)
	if _, err := s.repo.CreateAdminLog(createBody); err != nil {
		return internalServerError(err)
	}
	return nil
}
