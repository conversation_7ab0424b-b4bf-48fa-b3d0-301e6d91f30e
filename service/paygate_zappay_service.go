package service

import (
	"bytes"
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"image/png"
	"log"
	"strings"
	"time"

	"github.com/skip2/go-qrcode"
	"gorm.io/gorm"
)

type ZappayService interface {
	// Zappay
	CreateZappayWebhook(req model.ZappayWebhookRequest) (*int64, error)
	GetZappayWebDepositAccount() (*model.ZappayCustomerDepositInfo, error)
	CreateZappayDeposit(req model.ZappayDepositCreateRequest) (*model.ZappayOrderWebResponse, error)
	CreateZappayWithdraw(req model.ZappayWithdrawCreateRequest) (*int64, error)
	// ZappayCheckBalance() (*model.ZappayCheckBalanceRemoteResponse, error)
	CancelWithdrawFromZappay(transId int64, adminId int64) error
	CreateZappayDepositWebhook(req model.ZappayWebhookRequest) (*int64, error)
	CreateZappayWithdrawWebhook(req model.ZappayWebhookRequest) (*int64, error)
	// SysLog
	CreateSystemLog(name string, req interface{}) error
}

type paygateZappayService struct {
	sharedDb                  *gorm.DB
	repo                      repository.ZappayRepository
	activityLuckyWheelService ActivityLuckyWheelService
	promotionWebService       PromotionWebService
	notiService               NotificationService
}

func NewZappayService(
	sharedDb *gorm.DB,
	repo repository.ZappayRepository,
	activityLuckyWheelService ActivityLuckyWheelService,
	promotionWebService PromotionWebService,
	notiService NotificationService,
) ZappayService {
	return &paygateZappayService{sharedDb, repo, activityLuckyWheelService, promotionWebService, notiService}
}

func (s paygateZappayService) ConfirmDepositTransaction(id int64, req model.BankConfirmDepositRequest) error {

	record, err := s.repo.GetBankTransactionById(id)
	if err != nil {
		return internalServerError(err)
	}
	if record.TransactionStatusId != model.TRANS_STATUS_PENDING {
		return badRequest("Transaction is not pending")
	}
	if record.TransactionTypeId != model.TRANSACTION_TYPE_DEPOSIT && record.TransactionTypeId != model.TRANSACTION_TYPE_BONUS {
		return badRequest("Transaction is not deposit")
	}
	jsonBefore, _ := json.Marshal(record)

	var updateData model.BankDepositTransactionConfirmBody
	updateData.TransactionStatusId = model.TRANS_STATUS_DEPOSIT_PENDING_CREDIT
	updateData.ConfirmedAt = req.ConfirmedAt
	updateData.ConfirmedByAdminId = req.ConfirmedByAdminId

	// if record.IsAutoCredit && record.TransferAt != nil {
	// 	seconds := time.Now().UTC().Sub(record.TransferAt.UTC()).Seconds()
	// 	autoProcessTimer := fmt.Sprintf("%.2f", seconds)
	// 	updateData.AutoProcessTimer = &autoProcessTimer
	// }

	var createBody model.CreateBankTransactionActionBody
	createBody.ActionKey = fmt.Sprintf("DCF_STATE#%d", record.Id)
	createBody.TransactionId = record.Id
	createBody.UserId = record.UserId
	createBody.TransactionTypeId = record.TransactionTypeId
	createBody.FromAccountId = record.FromAccountId
	createBody.ToAccountId = record.ToAccountId
	createBody.JsonBefore = string(jsonBefore)
	if req.TransferAt == nil {
		createBody.TransferAt = record.TransferAt
	} else {
		TransferAt := req.TransferAt
		createBody.TransferAt = TransferAt
		updateData.TransferAt = *TransferAt
	}
	if req.SlipUrl != nil {
		createBody.SlipUrl = *req.SlipUrl
	}
	createBody.CreditAmount = record.CreditAmount
	if req.BonusAmount != nil {
		createBody.BonusAmount = *req.BonusAmount
	}
	createBody.ConfirmedAt = req.ConfirmedAt
	createBody.ConfirmedByAdminId = req.ConfirmedByAdminId
	if actionId, err := s.repo.CreateTransactionAction(createBody); err == nil {
		// do nothing ?
		if err := s.repo.ConfirmPendingDepositTransaction(id, updateData); err != nil {
			if err := s.repo.RollbackTransactionAction(*actionId); err == nil {
				return internalServerError(err)
			}
			return internalServerError(err)
		}
	} else {
		return internalServerError(err)
	}
	return nil
}

func (s paygateZappayService) CreateZappayWebhook(req model.ZappayWebhookRequest) (*int64, error) {

	var createBody model.ZappayWebhookCreateBody
	createBody.Name = "ZAPPAY_FUNDIN_CALLBACK"
	createBody.JsonPayload = req.JsonPayload
	insertId, err := s.repo.CreateZappayWebhook(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// DEPOSIT
	// {
	// 	"data": {
	// 		"en_data": "XDPfi/Yoz+EHaME/OKwiDzdo15ARU2YqTdL6k0LMtIOQWF8tJaA6JwDdDJitqn4tWiTlvbVUD9e92hiG+SF05/YqKUBHtXhoTOIRZFV/GBFUsCGUi0jQ3+nVLtgpmHnLKj0+63Y+mC9WEGyrxgAS+8MqF1uhKXhqM881vnAWIpTuJvRLpgC5CabezyqJUfrhcu9lx9KT1UFCKGj9M3vBmI9Vw4NxalVYZgb2VDaqBlcqe4viSqc+hXQOQEhkDLA40qkNDIr0ZAbxqHJ+0nSEJeXPqJf1rQWO+Ohv2AC+8Gklh0Gl+5mevkCQhgDnM/pAnRg552xXzH9Cd7rxZzKVb/jlVfcQj2HQfiGcA2AOAGqyRziscgRFpKS8s6MjzGyyLpf1lB+hvVlqe1btZWQ3sbNZmYXATeXqTSAfqiNld9MJcB2IzJQ+HFLlHHKdxgzW",
	// 		"partner_key": "fnFq51Tk_R6j8FZIZC8I_ngCdDeMEbib",
	// 		"mch_order_no": "signer_tr_960195036"
	// 	}
	// }
	// {
	// 	"data": {
	// 		"en_data": "N1ajoGWgtL/wWgteVv6RK4lXVSNeCAdPk+zSpekrE3woPSl72BmLVWPqiotFA2M4oo5MqDYWAhE94dNb66esQjeE7Ff2GFz5tTIAf85pD3zX1W7w1NU8MAmR8TwAuy0F3JHhkNHqmvp2jgm1LRfWEHwm8fB7r4i852N+8WeI+wjb2HmMeWkgskzq3+EQJ1x3v9n9AIzOIkp0WGWXevo6bSbBVWcqEm3eoqf1IN56JCuXHhf8v4nzXn+IrNKBt1C14gPzDIdCMNLsPtKeZpyeZfz4acVzKfyJS7gDjkt/CUis/KHmr5Q+qcwL5bhSMrUVSd7E63nGl8Z0pjXozqWwYznL6Ncs1JurlRBk5RSmcEWfXmrXMXAPkgsRIia8uoOa6FbGX8dX5VZxOaPjzXqEiq4GULu8/ZfajEdQQWDl1xjyNnXmZtR9AJDxsdxVSi7Q",
	// 		"partner_key": "fnFq51Tk_R6j8FZIZC8I_ngCdDeMEbib",
	// 		"mch_order_no": "signer_tr_276889882"
	// 	}
	// }

	var remoteResp model.ZappayWebhookEncryptPayload
	errJson := json.Unmarshal([]byte(req.JsonPayload), &remoteResp)
	if errJson != nil {
		log.Println("Error unmarshal json", errJson)
		return nil, internalServerError(errJson)
	}

	// Later : check partner_key ???
	if remoteResp.Data.MchOrderNo == "" || remoteResp.Data.PartnerKey == "" {
		return nil, internalServerError(fmt.Errorf("INVALID_REFERENCE"))
	}

	// Service Race Condition by Ref1(MchOrderNo)
	var rcCreateBody model.RaceActionCreateBody
	rcCreateBody.Name = "CreateZappayWebhook"
	rcCreateBody.JsonRequest = helper.StructJson(req)
	rcCreateBody.Status = "PENDING"
	// ActionKey = Ref2-MINUTE
	rcCreateBody.ActionKey = fmt.Sprintf("%s-%s", remoteResp.Data.MchOrderNo, time.Now().Format("**********"))
	rcCreateBody.UnlockAt = time.Now().UTC().Add(time.Second * 1)
	if _, err := s.repo.CreateRaceCondition(rcCreateBody); err != nil {
		return insertId, internalServerError(err)
	}

	// Get Setting + Decrypt
	pgAccount, err := s.GetZappayAccount()
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}
	payload, err := s.repo.ZappayDecryptRepayDespositPayload(*pgAccount, remoteResp)
	if err != nil {
		return nil, internalServerError(err)
	}

	// fmt.Println("ZappayDecryptRepayDespositPayload.payload", helper.StructJson(payload))

	// Get Posible Order
	var query model.ZappayOrderListRequest
	query.OrderNo = payload.MchOrderNo
	query.TransactionNo = payload.OrderNo
	// query.Amount = fmt.Sprintf("%f", remoteResp.Amount)
	query.Status = "WAIT_PAYMENT"
	query.Limit = 1
	list, _, err := s.repo.GetDbZappayOrderList(query)
	if err != nil {
		return nil, internalServerError(err)
	}

	// fmt.Println("ZappayDecryptRepayDespositPayload.list", helper.StructJson(list))

	if len(list) > 0 {
		for _, item := range list {
			// Update Order
			// 2-1. Order Status
			// status code Notes
			// 0 Processing
			// 1 success
			// 2 failed
			// 3 Cancellation, transaction failed（Only for Payment）
			successStatus := strings.ToUpper(fmt.Sprintf("%d", payload.Status))
			if successStatus == "1" {
				successStatus = "PAID"
			} else if successStatus == "2" {
				successStatus = "ERROR"
			} else if successStatus == "3" {
				successStatus = "CANCEL"
			}
			if err := s.repo.ApproveDbZappayOrder(item.Id, successStatus); err != nil {
				return nil, internalServerError(err)
			}
			// Payment Success, Create Deposit if UserId is not null
			if item.UserId > 0 && item.OrderTypeId == model.ZAPPAY_ORDER_TYPE_DEPOSIT {
				if successStatus == "PAID" {
					if _, err := CreateCustomerDepositFromZappayOrder(s.repo, item.Id, 0); err != nil {
						// WebhookLog
						var createBody2 model.ZappayWebhookCreateBody
						createBody2.Name = "ZAPPAY_DEPOSIT_ERROR"
						createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
							"item":   item,
							"method": "createCustomerDepositFromZappay",
							"error":  err.Error(),
						})
						if _, err = s.repo.CreateZappayWebhook(createBody2); err != nil {
							log.Println("Error CreateZappayWebhook.createCustomerDepositFromZappay", err)
						}
					}
				}
			} else if item.UserId > 0 && item.OrderTypeId == model.ZAPPAY_ORDER_TYPE_WITHDRAW {
				if successStatus == "PAID" {
					if _, err := approveCustomerWithdrawFromZappay(s.repo, *item.RefId); err != nil {
						// WebhookLog
						var createBody2 model.ZappayWebhookCreateBody
						createBody2.Name = "ZAPPAY_WITHDRAW_ERROR"
						createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
							"item":   item,
							"method": "approveCustomerWithdrawFromZappay",
							"error":  err.Error(),
						})
						if _, err = s.repo.CreateZappayWebhook(createBody2); err != nil {
							log.Println("Error approveCustomerWithdrawFromZappay.CreateZappayWebhook", err)
						}
					}
				} else if successStatus == "ERROR" || successStatus == "CANCEL" {
					if err := s.cancelWithdrawFromZappayWebhookError(item); err != nil {
						log.Println("Error UpdateDbZappayOrderError", err)
					}
				}
			} else {
				log.Println("ApproveDbZappayOrder.WithoutItemUpdated", helper.StructJson(item))
			}
		}
	}
	return insertId, nil
}

func (s paygateZappayService) GetZappayWebDepositAccount() (*model.ZappayCustomerDepositInfo, error) {

	var result model.ZappayCustomerDepositInfo

	pgAccount, err := s.GetZappayAccount()
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}
	if !pgAccount.IsDepositEnabled {
		return nil, badRequest("PAYGATE_DEPOSIT_NOT_ENABLED")
	}

	result.Name = pgAccount.Name
	if pgAccount.DisplayName == "" {
		result.Name = "เติมเงินผ่าน QR Code"
	} else {
		result.DisplayName = pgAccount.DisplayName
	}
	result.ShopName = pgAccount.ShopName
	result.MinAmount = 100
	result.MaxAmount = 200000

	return &result, nil
}

func (s paygateZappayService) GetZappayAccount() (*model.PaygateAccountResponse, error) {

	return GetZappayAccount(s.repo)
}

func GetZappayAccount(repo repository.ZappayRepository) (*model.PaygateAccountResponse, error) {

	pgAccount, err := repo.GetPaygateAccountByProviderId(model.PAYGATE_MERCHANT_ID_ZAPPAY)
	if err != nil {
		return nil, internalServerError(err)
	}
	return pgAccount, nil
}

func (s paygateZappayService) CreateZappayDeposit(req model.ZappayDepositCreateRequest) (*model.ZappayOrderWebResponse, error) {

	var result model.ZappayOrderWebResponse

	// Ruled by Provider
	if req.Amount < 50 || req.Amount > 200000 {
		log.Println("req.Amount", req.Amount)
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}

	pgAccount, err := s.GetZappayAccount()
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}
	if !pgAccount.IsDepositEnabled {
		return nil, badRequest("PAYGATE_DEPOSIT_NOT_ENABLED")
	}
	if pgAccount.PartnerKey == "" || pgAccount.LoanAppId == "" || pgAccount.MerchantId == "" || pgAccount.Token == "" || pgAccount.AesKey == "" || pgAccount.ApiEndPoint == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}

	// [********] get previse deposit order in last 5 minutes
	if pOrder, err := s.repo.CheckZappayDepositOrderInLast5Minutes(req.UserId, req.Amount); err != nil {
		if err != gorm.ErrRecordNotFound {
			return nil, internalServerError(err)
		}
	} else if pOrder != nil {
		actionAtUtc := time.Now().UTC()
		if pOrder.CreatedAt.Add(time.Minute * 5).After(actionAtUtc) {
			result.UserId = pOrder.UserId
			result.OrderNo = pOrder.OrderNo
			result.Amount = pOrder.Amount
			result.TransferAmount = pOrder.TransferAmount
			result.TransactionStatus = *pOrder.TransactionStatus
			result.QrCode = pOrder.QrPromptpay
			// ไม่ส่งไปให้ Frontend จะไม่ใช้ระบบเปิดหน้าเว็บจ่ายเงิน เพราะไม่มี redirect กลับมา
			// เมื่อไม่ส่งให้ Frontend จะใช้ QRCode ในการจ่ายเงิน อย่างเดียว
			// [20240829] at 8:50 AM
			// @TULA zappay เปลี่ยนแบบ redirect กลับมาเป็น qr code
			// result.PaymentPageUrl = pOrder.PaymentPageUrl
			result.CreatedAt = pOrder.CreatedAt

			imgData, err := qrcode.Encode(pOrder.QrPromptpay, qrcode.Medium, 256)
			if err != nil {
				// return nil, fmt.Errorf("unable to encode png: %w", err)
				return &result, nil
			}
			// encode to base64
			img, err := png.Decode(bytes.NewReader(imgData))
			if err != nil {
				// return nil, fmt.Errorf("unable to decode jpeg: %w", err)
				return &result, nil
			}
			var buf bytes.Buffer
			if err := png.Encode(&buf, img); err != nil {
				// return nil, fmt.Errorf("unable to encode png: %w", err)
				return &result, nil
			}
			result.QrBase64 = base64.StdEncoding.EncodeToString(buf.Bytes())
			return &result, nil
		}
	}

	user, err := s.repo.GetUserBankDetailById(req.UserId)
	if err != nil {
		return nil, badRequest("INVALID_USER")
	}
	if user == nil || user.BankAccount == "" || user.BankCode == "" {
		return nil, badRequest("INVALID_USER_ACCOUNT")
	}

	accountingRepo := repository.NewAccountingRepository(s.repo.GetDb())
	configWeb, err := s.repo.GetWebConfiguration()
	if err != nil {
		log.Println("CreateZappayDeposit.GetWebConfiguration", err)
		return nil, badRequest("WEB_CONFIG_NOT_FOUND")
	}
	if user.Id != 0 && configWeb != nil {
		// [********] ฝากครั้งแรกขั้นต่ำ ปรับ flow เช็ค user ให้เช็คจากรายการฝากครั้งแรกจริงๆ ไม่เช็คจากรหัสที่ได้รับ
		if err := CheckFirstMinimunDeposit(accountingRepo, user.Id, req.Amount, *configWeb); err != nil {
			return nil, badRequest(fmt.Sprintf("ฝากครั้งแรกขั้นต่ำ %d บาท", configWeb.MinFirstMemberDeposit))
		}
	} else {
		log.Println("CreateZappayDeposit.CheckFirstMinimunDeposit", "CANT_CHECK_FIRST_MINIMUM_DEPOSIT_ON_EMPTY_CONFIG")
	}

	// ===========================================================================================
	var createBody model.ZappayOrderCreateBody
	createBody.UserId = req.UserId
	createBody.OrderTypeId = model.ZAPPAY_ORDER_TYPE_DEPOSIT
	createBody.OrderNo = "" // Autogen
	createBody.Amount = req.Amount
	createBody.TransactionStatus = "PENDING"
	insertId, err := s.repo.CreateDbZappayOrder(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	// reget Order
	pendingOrder, err := s.repo.GetDbZappayOrderById(*insertId)
	if err != nil {
		// SET AS ERROR
		remark := "Error GetDbZappayOrderById, " + err.Error()
		if err := s.repo.UpdateDbZappayOrderError(*insertId, remark); err != nil {
			log.Println("CreateZappayDeposit.UpdateDbZappayOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// Create ZAPPAY Order
	var remoteRequest model.ZappayDepositCreateRemoteRequest
	remoteRequest.OrderNo = pendingOrder.OrderNo
	// BANK == Both transfer and QR
	remoteRequest.BankCode = "BANK" // ลูกค้าให้ใช้ สองแบบ โอนเงิน และ QR
	// Qr only
	// remoteRequest.BankCode = "BIND-PROMPTPAY"
	remoteRequest.Amount = pendingOrder.Amount
	remoteRequest.UserFullname = user.Fullname
	remoteRequest.UserMobile = user.Phone
	remoteRequest.UserAccountNumber = user.BankAccount
	remoteRequest.UserAccountBank = user.BankCode // todo validate ?
	remoteResp, err := s.repo.ZappayDeposit(*pgAccount, remoteRequest)
	if err != nil {
		// SET AS ERROR
		remark := "Error ZappayDeposit, " + err.Error()
		if err := s.repo.UpdateDbZappayOrderError(*insertId, remark); err != nil {
			log.Println("ZappayDeposit.UpdateDbZappayOrderError", err)
		}
		// SysLog
		if _, err := s.repo.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "CreateZappayDeposit.ZappayDeposit",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(remoteRequest),
			JsonResponse: helper.StructJson(map[string]interface{}{"error": err.Error()}),
		}); err != nil {
			log.Println("CreateZappayDeposit.CreateSysLog", err)
		}
		return nil, internalServerError(err)
	}

	// fmt.Println("ZappayDeposit.remoteResp", helper.StructJson(remoteResp))

	// Parse Float Amount
	// transferAmount, err := strconv.ParseFloat(remoteResp.Data.Amount, 64)
	// if err != nil {
	// 	// SET AS ERROR
	// 	remark := "Error ParseFloat Amount, " + err.Error()
	// 	if err := s.repo.UpdateDbZappayOrderError(*insertId, remark); err != nil {
	// 		log.Println("CreateZappayDeposit.UpdateDbZappayOrderError", err)
	// 	}
	// 	return nil, internalServerError(err)
	// }

	// onCreate Success
	var updateBody model.ZappayOrderUpdateBody
	updateBody.TransactionNo = remoteResp.Data.OrderNo
	updateBody.TransactionDate = time.Now()
	updateBody.TransactionStatus = "WAIT_PAYMENT"
	updateBody.TransferAmount = remoteResp.Data.Amount
	updateBody.QrPromptpay = remoteResp.Data.OriginCode // todo QR string?
	updateBody.PaymentPageUrl = remoteResp.Data.Redirect
	if err := s.repo.UpdateDbZappayOrder(*insertId, updateBody); err != nil {
		// SET AS ERROR
		remark := "Error UpdateDbZappayOrder, " + err.Error()
		if err := s.repo.UpdateDbZappayOrderError(*insertId, remark); err != nil {
			log.Println("CreateZappayDeposit.UpdateDbZappayOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	waitPayOrder, err := s.repo.GetDbZappayOrderById(*insertId)
	if err != nil {
		return nil, internalServerError(err)
	}

	result.UserId = waitPayOrder.UserId
	result.OrderNo = waitPayOrder.OrderNo
	result.Amount = waitPayOrder.Amount
	result.TransferAmount = remoteResp.Data.Amount
	result.TransactionStatus = *waitPayOrder.TransactionStatus
	result.QrCode = waitPayOrder.QrPromptpay
	// ไม่ส่งไปให้ Frontend จะไม่ใช้ระบบเปิดหน้าเว็บจ่ายเงิน เพราะไม่มี redirect กลับมา
	// เมื่อไม่ส่งให้ Frontend จะใช้ QRCode ในการจ่ายเงิน อย่างเดียว
	// [20240829] at 8:50 AM
	// @TULA zappay เปลี่ยนแบบ redirect กลับมาเป็น qr code
	// result.PaymentPageUrl = waitPayOrder.PaymentPageUrl
	result.CreatedAt = waitPayOrder.CreatedAt

	imgData, err := qrcode.Encode(waitPayOrder.QrPromptpay, qrcode.Medium, 256)
	if err != nil {
		// return nil, fmt.Errorf("unable to encode png: %w", err)
		return &result, nil
	}
	// encode to base64
	img, err := png.Decode(bytes.NewReader(imgData))
	if err != nil {
		// return nil, fmt.Errorf("unable to decode jpeg: %w", err)
		return &result, nil
	}
	var buf bytes.Buffer
	if err := png.Encode(&buf, img); err != nil {
		// return nil, fmt.Errorf("unable to encode png: %w", err)
		return &result, nil
	}
	result.QrBase64 = base64.StdEncoding.EncodeToString(buf.Bytes())

	return &result, nil
}

func (s paygateZappayService) CreateZappayWithdraw(req model.ZappayWithdrawCreateRequest) (*int64, error) {

	// Ruled by Provider
	// - loan trade amount must greater than 100
	if req.Amount < 100 || req.Amount > 200000 {
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}

	pgAccount, err := s.GetZappayAccount()
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}
	if !pgAccount.IsWithdrawEnabled {
		return nil, badRequest("PAYGATE_WITHDRAW_NOT_ENABLED")
	}
	if pgAccount.PartnerKey == "" || pgAccount.LoanAppId == "" || pgAccount.MerchantId == "" || pgAccount.Token == "" || pgAccount.AesKey == "" || pgAccount.ApiEndPoint == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}

	user, err := s.repo.GetUserBankDetailById(req.UserId)
	if err != nil {
		return nil, badRequest("INVALID_USER")
	}
	if user == nil || user.BankAccount == "" || user.BankCode == "" {
		return nil, badRequest("INVALID_USER_ACCOUNT")
	}

	// remoteRequest.UserAccountBank = user.BankCode // todo validate ?
	withdrawBankCode := user.BankCode
	// user mobile must start with prefix 66 or 856
	withdrawPhone := user.Phone
	if len(withdrawPhone) > 0 && strings.HasPrefix(withdrawPhone, "0") {
		withdrawPhone = "66" + withdrawPhone[1:]
	}

	// ===========================================================================================
	// CREATE Order
	var createBody model.ZappayOrderCreateBody
	createBody.RefId = &req.RefId
	createBody.UserId = req.UserId
	createBody.OrderTypeId = model.ZAPPAY_ORDER_TYPE_WITHDRAW
	createBody.OrderNo = "" // Autogen
	createBody.Amount = req.Amount
	createBody.TransactionStatus = "PENDING"
	insertId, err := s.repo.CreateDbZappayOrder(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	// reget Order
	pendingOrder, err := s.repo.GetDbZappayOrderById(*insertId)
	if err != nil {
		// SET AS ERROR
		remark := "Error GetDbZappayOrderById, " + err.Error()
		if err := s.repo.UpdateDbZappayOrderError(*insertId, remark); err != nil {
			log.Println("CreateZappayWithdraw.UpdateDbZappayOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// Create ZAPPAY Order
	var remoteRequest model.ZappayWithdrawCreateRemoteRequest
	remoteRequest.OrderNo = pendingOrder.OrderNo
	remoteRequest.Amount = pendingOrder.Amount
	remoteRequest.UserFullname = user.Fullname
	remoteRequest.UserMobile = withdrawPhone
	remoteRequest.UserAccountNumber = user.BankAccount
	remoteRequest.UserAccountBank = withdrawBankCode
	remoteResp, err := s.repo.ZappayWithdraw(*pgAccount, remoteRequest)
	if err != nil {
		// SET AS ERROR
		remark := "Error ZappayWithdraw, " + err.Error()
		if err := s.repo.UpdateDbZappayOrderError(*insertId, remark); err != nil {
			log.Println("ZappayWithdraw.UpdateDbZappayOrderError", err)
		}
		// SysLog
		if _, err := s.repo.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "CreateZappayWithdraw.ZappayWithdraw",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(remoteRequest),
			JsonResponse: helper.StructJson(map[string]interface{}{"error": err.Error()}),
		}); err != nil {
			log.Println("CreateZappayWithdraw.CreateSysLog", err)
		}
		return nil, internalServerError(err)
	}

	fmt.Println("CreateZappayWithdraw.remoteResp", helper.StructJson(remoteResp))

	if remoteResp.Code != "0" {
		// SET AS ERROR
		remark := remoteResp.Message
		if len(remark) == 0 {
			remark = "UNKNOW_ERROR from withdrawWithZappay"
		}
		if err := s.repo.UpdateDbZappayOrderError(*insertId, remark); err != nil {
			log.Println("ZappayWithdraw.UpdateDbZappayOrderError", err)
		}
		// SysLog
		if _, err := s.repo.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "CreateZappayWithdraw.ZappayWithdraw",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(remoteRequest),
			JsonResponse: helper.StructJson(remoteResp),
		}); err != nil {
			log.Println("CreateZappayWithdraw.CreateSysLog", err)
		}
		return nil, internalServerError(fmt.Errorf("UNKNOW_ERROR"))
	}

	// onCreate Success
	var updateBody model.ZappayOrderUpdateBody
	updateBody.TransactionNo = remoteResp.Data.OrderNo
	updateBody.TransactionDate = time.Now()
	updateBody.TransactionStatus = "WAIT_PAYMENT"
	updateBody.QrPromptpay = remoteResp.Message
	if err := s.repo.UpdateDbZappayOrder(*insertId, updateBody); err != nil {
		// SET AS ERROR
		remark := "Error UpdateDbZappayOrder, " + err.Error()
		if err := s.repo.UpdateDbZappayOrderError(*insertId, remark); err != nil {
			log.Println("CreateZappayWithdraw.UpdateDbZappayOrderError", err)
		}
		return nil, internalServerError(err)
	}
	return insertId, nil
}

func CreateCustomerDepositFromZappayOrder(repo repository.ZappayRepository, orderId int64, adminId int64) (*int64, error) {

	// ** ทุกรายการจะต้องเช็คสถานะใหม่ทั้งหมด เพราะ admin แย่งกดอนุมัติได้
	item, err := repo.GetRawZappayPendingDepositOrderById(orderId)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, badRequest("ORDER_NOT_FOUND")
		}
		return nil, internalServerError(err)
	}

	// Check BankTransactionStatus = 'PENDING'
	if item.BankTransactionStatus != "PENDING" {
		return nil, badRequest("ORDER_NOT_PENDING")
	}

	return createCustomerDepositFromZappay(repo, *item, adminId)
}

func createCustomerDepositFromZappay(repo repository.ZappayRepository, item model.ZappayOrderResponse, adminId int64) (*int64, error) {

	var externalNoti model.NotifyExternalNotificationRequest
	actionAt := time.Now()
	// confirmByUserId := int64(0)

	depositAmount := item.Amount

	user, err := repo.GetUserBankDetailById(item.UserId)
	if err != nil {
		log.Println("createCustomerDepositFromZappay.GetUserBankDetailByMemberCode", err)
		return nil, internalServerError(err)
	}

	var promotionWebUserId int64
	if getPromotionWebUser, err := repo.GetDepositCurrentProcessingUserPromotion(item.UserId); err != nil {
		log.Println("createCustomerDepositFromZappay.GetDepositCurrentProcessingUserPromotion", err)
	} else if getPromotionWebUser != nil {
		promotionWebUserId = getPromotionWebUser.Id
	}

	// MemberCode
	if user.MemberCode == "" {
		memberCode, err := GenUniqueUserMemberCode(repository.NewUserRepository(repo.GetDb()), user.Id)
		if err != nil {
			log.Println("createCustomerDepositFromZappay.GenUniqueUserMemberCode", err)
			return nil, internalServerError(err)
		}
		user.MemberCode = *memberCode
	}

	// layer — 2024-04-02 at 10:06 AM
	// ถ้าแก้ตามนี้แล้วน่าจะไม่เจอ ถ้าเคสกันซ้ำเราครอบคลุม
	// @Dunk @TULA
	// กันซ้ำคือ heng ไปซ้ำกับแอดมินเติมมือ
	// ----
	// เงื่อนไขกันซ้ำ
	// 1 หารายการฝากสำเร็จล่าสุดของยูสนั้น
	// 2 ถ้ามีรายการล่าสุดที่เป็นเติมมือ
	// -เช็คเวลาเติม ต้องมากกว่าเวลาโอนในสลิป
	// -ยอดเงินเท่ากัน
	// **ถ้าเข้าเงื่อนไขทั้งหมด คือแสดงว่า webhook เข้าทีหลังแอดมินเติมมือ
	// ต้อง bypass ไม่ให้เติมอีก และอัพเดท status ว่าจับคู่ได่แล้ว
	// ---
	// วิธีนี้น่าจะใช้ได้กับทุก webhook (heng, fastbank) ที่มีเวลาโอนสลิปมาให้
	duplicateRemark := "INIT"
	hasDuplicateDeposit := false
	manualDeposit, err := repo.GetLastestBankAdminManualDepositTransaction(user.Id)
	if err != nil {
		if err.Error() != recordNotFound {
			log.Println("createCustomerDepositFromZappay.GetLastestBankAdminManualDepositTransaction", err)
			return nil, internalServerError(err)
		}
		// NOT_FOUND = ถ้าไม่เจอ ให้ทำต่อ
		duplicateRemark = "OK_NO_MANUAL_DEPOSIT"
	}
	if manualDeposit != nil && item.TransactionDate != nil {
		if manualDeposit.TransferAt.After(*item.TransactionDate) {
			// ถ้าเวลาเติมมือมากกว่าเวลาโอนในสลิป
			if manualDeposit.CreditAmount == depositAmount {
				// ถ้ายอดเงินเท่ากัน
				duplicateRemark = "STOP_MANUAL_DEPOSIT_AFTER_AND_SAME_AMOUNT"
				hasDuplicateDeposit = true
			} else {
				duplicateRemark = "OK_MANUAL_DEPOSIT_AMOUNT_NOT_MATCH"
			}
		} else {
			duplicateRemark = "OK_MANUAL_DEPOSIT_BEFORE_WITHDRAW"
		}
	}
	if duplicateRemark != "INIT" {
		// WebhookLog
		var createBody2 model.ZappayWebhookCreateBody
		createBody2.Name = "ZAPPAY_DEPOSIT_DUPLICATE_CHECKER"
		createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
			"item":                item,
			"method":              "createCustomerDeposit",
			"hasDuplicateDeposit": hasDuplicateDeposit,
			"error":               duplicateRemark,
			"manualDeposit":       manualDeposit,
		})
		if _, err = repo.CreateZappayWebhook(createBody2); err != nil {
			log.Println("Error CreateZappayWebhook.CreateZappayWebhook", err)
		}
		if hasDuplicateDeposit {
			return nil, internalServerError(fmt.Errorf("DUPLICATE_DEPOSIT"))
		}
	}

	pgAccount, err := GetZappayAccount(repo)
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}

	// ===================================================
	paygateRepo := repository.NewPaymentGatewayRepository(repo.GetDb())
	accountingRepo := repository.NewAccountingRepository(repo.GetDb())
	luckyWheelRepo := repository.NewActivityLuckyWheelRepository(repo.GetDb())
	promotionWebRepo := repository.NewPromotionWebRepository(repo.GetDb())
	notiRepo := repository.NewNotificationRepository(repo.GetDb())

	// Affiliate + Alliance Income
	member, err := repo.GetMemberById(user.Id)
	if err != nil {
		return nil, internalServerError(err)
	}
	if member.UserTypeName == "NONE" {
		// ถ้าฝากครั้งแรก จะได้เป็น AFFILIATE + REFER COMMISION
		if err := UserFirstDepositCommission(accountingRepo, *member, depositAmount); err != nil {
			log.Println("CreateFristTimeDepositRecord.UserFirstDepositCommission.ERROR", err)
			return nil, internalServerError(err)
		}
	}

	isAutoDeposit := true // Auto ฝาก นับตั้งแต่ได้รับยอดฝาก จนได้ยืนยันเครดิต
	if adminId != 0 {
		isAutoDeposit = false
	}

	// CREATE BANK TRANSACTION
	var createBankTransaction model.BankTransactionCreateBody
	createBankTransaction.MemberCode = user.MemberCode
	createBankTransaction.UserId = user.Id
	// createBankTransaction.StatementId = 0
	createBankTransaction.TransactionTypeId = model.TRANSACTION_TYPE_DEPOSIT
	// createBankTransaction.FromAccountName = "" // empty?
	createBankTransaction.ToAccountName = &pgAccount.TypeName
	createBankTransaction.ToAccountNumber = &pgAccount.ShopName
	createBankTransaction.PromotionId = &promotionWebUserId
	createBankTransaction.CreditAmount = depositAmount
	// createBankTransaction.BonusAmount = 0
	// createBankTransaction.BonusReason = nil
	createBankTransaction.DepositChannel = "ZAPPAY PAYMENT GATEWAY"
	createBankTransaction.TransactionStatusId = model.TRANS_STATUS_PENDING
	createBankTransaction.CreatedByAdminId = 0
	createBankTransaction.IsAutoCredit = isAutoDeposit
	createBankTransaction.TransferAt = &actionAt
	// create transaction
	transId, err := repo.InsertBankTransaction(createBankTransaction)
	if err != nil {
		log.Println("createCustomerDepositFromZappay.InsertBankTransaction", err)
		return nil, internalServerError(err)
	}

	// var actionStatement model.BankStatementMatchRequest
	// actionStatement.StatementId = *bankstatementId
	// actionStatement.UserId = user.Id
	// actionStatement.ConfirmedAt = time
	// actionStatement.ConfirmedByAdminId = &req.CreateByUserId
	// if err := s.serviceAccounting.SetStatementOwnerMatched(*bankstatementId, actionStatement, model.USE_ENDING_NOTI); err != nil {
	// 	log.Println("createCustomerDepositFromZappay.SetStatementOwnerMatched", err)
	// 	return nil, internalServerError(err)
	// }

	// DECREASE FASTBANK CREDIT 2.FROM create auto DEPOSIT
	// if err := repoAccounting.DecreaseFastbankCredit(1); err != nil {
	// 	log.Println("createCustomerDepositFromZappay.DECREASE_FASTBANK_CREDIT_ERROR", err)
	// }

	var confirmDeposit model.BankConfirmDepositRequest
	confirmDeposit.TransferAt = &actionAt
	confirmDeposit.ConfirmedAt = actionAt
	confirmDeposit.ConfirmedByAdminId = &adminId
	if err := confirmDepositTransaction(paygateRepo, *transId, confirmDeposit); err != nil {
		log.Println("createCustomerDepositFromZappay.confirmDepositTransaction", err)
		return nil, internalServerError(err)
	}

	// UPDATE HENG ORDER - BANK SUCCESS
	if err := repo.UpdateZappayOrderBankSucess(item.Id, *transId, adminId); err != nil {
		log.Println("createCustomerDepositFromZappay.UpdatePaygateOrderBankSucess", err)
	}

	isFirstDeposit := repo.IsFirstDeposit(user.Id)
	if isFirstDeposit {
		var bonusReq model.UserFirstDepositCreateRequest
		bonusReq.UserId = user.Id
		bonusReq.TransactionId = transId
		bonusReq.TransferAt = actionAt
		bonusReq.Amount = depositAmount
		bonusReq.Remark = "createCustomerDepositFromZappay"
		if err := SetFirstDepositBonus(accountingRepo, isFirstDeposit, bonusReq); err != nil {
			webhookLogMessage := fmt.Sprintf("createCustomerDepositFromZappay.userFirstDepositBonus, ERROR: %s", err)
			log.Println("SetFirstDepositBonus", webhookLogMessage)
			// return nil, errors.New(webhookLogMessage)
		}
	}

	// [USER_CREDIT]
	var userCreditReq model.UserTransactionCreateRequest
	userCreditReq.UserId = user.Id
	userCreditReq.RefId = transId
	userCreditReq.TransferAt = &actionAt
	userCreditReq.PromotionId = &promotionWebUserId
	userCreditReq.TypeId = model.CREDIT_TYPE_DEPOSIT
	// userCreditReq.AccountId = &bankAccount.Id
	userCreditReq.Detail = "ZAPPAY PAYMENT GATEWAY"
	userCreditReq.Amount = depositAmount
	userCreditReq.CreateBy = &adminId
	userCreditReq.ConfirmBy = &adminId
	userCreditReq.IsAdjustAuto = isAutoDeposit
	userCreditReq.PaymentMerchatId = &pgAccount.ProviderId
	if agentResp, err := repo.IncreaseUserCredit(userCreditReq); err != nil {
		log.Println("createCustomerDepositFromZappay.IncreaseUserCredit", err)
		return nil, internalServerError(err)
	} else {
		// AGENT_SUCCESS
		if err := repo.UpdateDepositTransactionStatusFromAgent(*transId, *agentResp); err != nil {
			log.Println("createCustomerDepositFromZappay.UpdateDeporsitTransactionStatusFromAgent", err)
		}
	}

	// [Lucky Wheel] ตอนฝากละได้โบนัส
	var luckyWheelBody model.ActivityLuckyWheelRoundUserRequest
	luckyWheelBody.UserId = user.Id
	luckyWheelBody.ConditionId = model.ACTIVITY_LUCKY_WHEEL_SETTING_CONDITION_DEPOSIT
	luckyWheelBody.ConditionAmount = depositAmount
	if err := CreateRoundActivityLuckyWheel(luckyWheelRepo, luckyWheelBody); err != nil {
		log.Println("createCustomerDepositFromZappay.CreateRoundActivityLuckyWheel", err)
	}

	// [TIER]
	if err := repo.IncreaseUserTierDepositAmount(user.Id, depositAmount); err != nil {
		log.Println("MatchStatementOwner.IncreaseUserTierDepositAmount", err)
	}

	var checkUserPromotionBody model.CheckUserPromotionBody
	checkUserPromotionBody.UserId = user.Id
	_, err = CheckUserPromotion(promotionWebRepo, checkUserPromotionBody)
	if err != nil {
		log.Println("createCustomerDepositFromZappay.CheckUserPromotion", err)
	}
	// [turnOver] ถ้ามีการฝากเงิน ให้สร้าง TurnOver
	turnoverShare := repository.NewTurnoverRepository(repo.GetDb())
	if err := CreateTurnOverFromSuccessDeposit(turnoverShare, user.Id, depositAmount, *transId); err != nil {
		log.Println("createCustomerDeposit.CreateTurnOverFromSuccessDeposit", err)
	}

	// ===================================================

	// [ notify]
	externalNoti.TypeNotify = model.IsDepositAfterCredit
	externalNoti.TransId = transId
	externalNoti.Amount = depositAmount
	externalNoti.MemberCode = user.MemberCode
	externalNoti.UserCredit = user.Credit + depositAmount
	externalNoti.ConfirmedByAdminId = 0
	externalNoti.TransferDateTime = time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02 15:04:05")
	externalNoti.ActionTime = time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02 15:04:05")
	endTime := time.Now()
	elapsed := endTime.Sub(actionAt)
	elapsedSeconds := elapsed.Seconds()
	timeElapsed := fmt.Sprintf("%.2f", elapsedSeconds)
	externalNoti.TimerCounter = timeElapsed

	externalNoti.WebScoket.UserID = user.Id
	externalNoti.WebScoket.Amount = depositAmount
	externalNoti.WebScoket.MemberCode = user.MemberCode
	externalNoti.WebScoket.AlertType = "DEPOSIT"
	if err := repo.UpdateAutoProcessTimer(timeElapsed, *transId); err != nil {
		return nil, nil
	}
	if err := ExternalNotification(notiRepo, externalNoti); err != nil {
		log.Println("FailedNotify", err)
	}
	return nil, nil
}

func approveCustomerWithdrawFromZappay(repo repository.ZappayRepository, transId int64) (*int64, error) {

	withdrawTrans, err := repo.GetBankTransactionById(transId)
	if err != nil {
		log.Println("approveCustomerWithdrawFromZappay.GetUserBankDetailById", err)
		return nil, internalServerError(err)
	}

	// ============================= ON_SUCCESS =================================
	if withdrawTrans.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		// [create transaction action success]
		var createConfirm model.CreateSuccessTransferWithdrawRequest
		createConfirm.TransactionId = withdrawTrans.Id
		createConfirm.ConfirmedAt = time.Now()
		var setIdAuto int64 = 0
		createConfirm.ConfirmedByAdminId = &setIdAuto
		if _, err := CreateSuccessTransferWithdraw(repository.NewBankingRepository(repo.GetDb()), createConfirm); err != nil {
			log.Println("approveCustomerWithdrawFromZappay.CreateSuccessTransferWithdraw", err)
			return nil, nil
		}
		// [Show Withdraw USER_TRANSACTION]
		var showUserTrans model.UserTransactionShowUpdate
		showUserTrans.TransactionId = withdrawTrans.Id
		showUserTrans.ConfirmedAt = &createConfirm.ConfirmedAt
		showUserTrans.ConfirmAdminId = createConfirm.ConfirmedByAdminId
		if err := repo.ShowUserTransaction(showUserTrans); err != nil {
			log.Println("approveCustomerWithdrawFromZappay.ShowUserTransaction", err)
			return nil, nil
		}
		// [update transaction status]
		secondUsed := "0.0" // timing
		if withdrawTrans.IsAutoCredit && withdrawTrans.TransferAt != nil {
			seconds := time.Now().UTC().Sub(withdrawTrans.TransferAt.UTC()).Seconds()
			secondUsed = fmt.Sprintf("%.2f", seconds)
		}
		if err := repo.UpdateTransactionStatusTransferingToSuccess(withdrawTrans.Id, secondUsed); err != nil {
			log.Println("approveCustomerWithdrawFromZappay.UpdateTransactionStatusTransferingToSuccess", err)
			return nil, internalServerError(err)
		}

		// [notify SUCCESS]
		var externalNoti model.NotifyExternalNotificationRequest
		notiRepo := repository.NewNotificationRepository(repo.GetDb())
		externalNoti.TypeNotify = model.IsWithdrawalCreditSuccess
		externalNoti.TransId = &transId
		externalNoti.Amount = withdrawTrans.CreditAmount
		externalNoti.MemberCode = withdrawTrans.MemberCode
		externalNoti.UserCredit = withdrawTrans.CreditAmount
		externalNoti.ConfirmedByAdminId = 0
		externalNoti.TimerCounter = secondUsed
		externalNoti.TransferDateTime = withdrawTrans.TransferAt.Add(7 * time.Hour).Format("2006-01-02 15:04:05")
		externalNoti.ActionTime = time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02 15:04:05")
		if err := ExternalNotification(notiRepo, externalNoti); err != nil {
			log.Println("FailedNotify", err)
		}

	}
	return nil, nil
}

// func rollbackCustomerWithdrawFromZappay(repo repository.ZappayRepository, transId int64) (*int64, error) {

// 	withdrawTrans, err := repo.GetBankTransactionById(transId)
// 	if err != nil {
// 		log.Println("rollbackCustomerWithdrawFromZappay.GetBankTransactionById", err)
// 		return nil, internalServerError(err)
// 	}

// 	// ============================= ON_SUCCESS =================================
// 	if withdrawTrans.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_TRASNFERING {
// 		// [update transaction status]
// 		if err := repo.RollbackTransactionStatusTransferingToConfirmed(withdrawTrans.Id); err != nil {
// 			log.Println("approveCustomerWithdrawFromZappay.RollbackTransactionStatusTransferingToConfirmed", err)
// 			return nil, internalServerError(err)
// 		}
// 	}
// 	return nil, nil
// }

func (s paygateZappayService) CreateSystemLog(name string, req interface{}) error {

	// [SYSLOG] HACKED
	_, err := s.repo.CreateSystemLog(model.SystemLogCreateBody{
		Name:   name,
		Status: "HACKING",
		JsonRequest: helper.StructJson(map[string]interface{}{
			"req": req,
		}),
	})
	if err != nil {
		log.Println(name, "CreateSystemLog.ERROR", err.Error())
	}
	return nil
}

func (s paygateZappayService) CancelWithdrawFromZappay(transId int64, adminId int64) error {

	withdrawTrans, err := s.repo.GetBankTransactionById(transId)
	if err != nil {
		log.Println("rollbackWithdrawFromZappay.GetBankTransactionById", err)
		return internalServerError(err)
	}
	if withdrawTrans.TransactionStatusId != model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		return internalServerError(errors.New("ไม่อยู๋ในสถานะที่สามารถยกเลิกได้"))
	}

	user, err := s.repo.GetUserBankDetailById(withdrawTrans.UserId)
	if err != nil {
		return internalServerError(err)
	}

	paygateOrder, err := s.repo.GetDbZappayOrderByRefId(transId)
	if err != nil {
		log.Println("CancelWithdrawFromZappay.GetDbZappayOrderByRefId", err)
		return internalServerError(err)
	}
	waitStatus := "ERROR"
	if paygateOrder.TransactionStatus == nil || *paygateOrder.TransactionStatus != waitStatus {
		return internalServerError(errors.New("ไม่อยู๋ในสถานะที่สามารถยกเลิกได้"))
	}

	returnAmount := withdrawTrans.CreditAmount

	// race condition deposit
	actionAt := time.Now().UTC().Format("20**********")
	var createBody model.RaceActionCreateBody
	createBody.Name = "ADMIN_CANCEL_WITHDRAW"
	createBody.JsonRequest = helper.StructJson(map[string]interface{}{
		"transId":      transId,
		"returnAmount": returnAmount,
	})
	createBody.Status = "PENDING"
	createBody.ActionKey = fmt.Sprintf("WITHDRAW_CANCEL_T%sU%dID%d", actionAt, user.Id, transId)
	createBody.UnlockAt = time.Now().UTC().Add(time.Second * 1)
	if _, err := s.repo.GetRaceActionIdByActionKey(createBody.ActionKey); err != nil {
		log.Println("CancelWithdrawFromZappay.GetRaceActionIdByActionKey", err)
		if err != gorm.ErrRecordNotFound {
			return internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
		}
	} else {
		return internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
	}

	// create race condition
	actionId, err := s.repo.CreateRaceCondition(createBody)
	if err != nil {
		log.Println("CancelWithdrawFromZappay.ERROR.CreateRaceCondition", err)
		return internalServerError(errors.New("CANNOT_CREATE_ACTION_LUCKY_WHEEL_ROUND"))
	}
	if actionId == 0 {
		return internalServerError(errors.New("CANNOT_CREATE_ACTION_LUCKY_WHEEL_ROUND"))
	}

	// SET ORDER AS ERROR
	remark := "Admin Cancel Withdraw"
	if err := s.repo.UpdateDbZappayOrderError(paygateOrder.Id, remark); err != nil {
		log.Println("CreateZappayWithdraw.UpdateDbZappayOrderError", err)
	}

	if withdrawTrans.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		// [********] check user credit transaction ว่าตรงไหมกับเงื่อนไข (isShow = false)
		userTransaction, err := s.repo.GetUserWithdrawCreditTransactionByRefId(withdrawTrans.Id, returnAmount)
		if err != nil {
			if err.Error() == recordNotFound {
				log.Println("CancelWithdrawCredit.GetUserCreditTransactionById", err)
				return badRequest("ไม่ตรงกับเงือนไข")
			}
			return internalServerError(err)
		}
		if userTransaction != nil {
			var userCreditReq model.UserTransactionCreateRequest
			userCreditReq.UserId = user.Id
			userCreditReq.TransferAt = withdrawTrans.TransferAt
			userCreditReq.RefId = &withdrawTrans.Id
			userCreditReq.TypeId = model.CREDIT_TYPE_DEPOSIT
			userCreditReq.Amount = returnAmount
			IsShow := false
			userCreditReq.IsShow = &IsShow
			userCreditReq.ConfirmBy = &adminId
			if agentResp, err := s.repo.IncreaseUserCredit(userCreditReq); err != nil {
				log.Println("CancelWithdrawCredit.IncreaseUserCredit", err)
				return internalServerError(err)
			} else if !agentResp.AgentSuccess {
				// [********] error from agent will be in failed
				// if err := s.repoAccounting.UpdateDeporsitTransactionStatusFromAgent(*transId, *agentResp); err != nil {
				//     log.Println("CancelWithdrawCredit.UpdateDeporsitTransactionStatusFromAgent", err)
				// }
				// Set FastBank-READ ** ไม่มี เพราะเป็นการสร้างรายการฝากเอง
				agentFail := errors.New("ทำรายการเครดิตไม่สำเร็จ")
				log.Println("CancelWithdrawCredit.IncreaseUserCredit", agentFail)
				return internalServerError(agentFail)
			}
		}
	}

	// remark := "ADMIN_CANCEL_WITHDRAW"
	var updateApprovedBy model.UpdateUserTransactionStatusRequest
	updateApprovedBy.Id = withdrawTrans.Id
	updateApprovedBy.CanceledAt = time.Now()
	updateApprovedBy.CanceledByAdminId = adminId
	updateApprovedBy.TransactionStatusId = model.TRANS_STATUS_WITHDRAW_CANCELED
	updateApprovedBy.CancelRemark = &remark
	err = s.repo.UpdateUserTransactionStatus(updateApprovedBy)
	if err != nil {
		return internalServerError(err)
	}

	var createConfirm model.CreateSuccessTransferWithdrawRequest
	createConfirm.TransactionId = withdrawTrans.Id
	createConfirm.ConfirmedAt = time.Now()
	createConfirm.ConfirmedByAdminId = &adminId
	if _, err := CreateCanceledTransferWithdraw(repository.NewBankingRepository(s.sharedDb), createConfirm); err != nil {
		return err
	}

	return nil
}

func (s paygateZappayService) CreateZappayDepositWebhook(req model.ZappayWebhookRequest) (*int64, error) {

	var createBody model.ZappayWebhookCreateBody
	createBody.Name = "ZAPPAY_REPAY_CALLBACK"
	createBody.JsonPayload = req.JsonPayload
	insertId, err := s.repo.CreateZappayWebhook(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// DEPOSIT
	// {
	// 	"data": {
	// 		"en_data": "XDPfi/Yoz+EHaME/OKwiDzdo15ARU2YqTdL6k0LMtIOQWF8tJaA6JwDdDJitqn4tWiTlvbVUD9e92hiG+SF05/YqKUBHtXhoTOIRZFV/GBFUsCGUi0jQ3+nVLtgpmHnLKj0+63Y+mC9WEGyrxgAS+8MqF1uhKXhqM881vnAWIpTuJvRLpgC5CabezyqJUfrhcu9lx9KT1UFCKGj9M3vBmI9Vw4NxalVYZgb2VDaqBlcqe4viSqc+hXQOQEhkDLA40qkNDIr0ZAbxqHJ+0nSEJeXPqJf1rQWO+Ohv2AC+8Gklh0Gl+5mevkCQhgDnM/pAnRg552xXzH9Cd7rxZzKVb/jlVfcQj2HQfiGcA2AOAGqyRziscgRFpKS8s6MjzGyyLpf1lB+hvVlqe1btZWQ3sbNZmYXATeXqTSAfqiNld9MJcB2IzJQ+HFLlHHKdxgzW",
	// 		"partner_key": "fnFq51Tk_R6j8FZIZC8I_ngCdDeMEbib",
	// 		"mch_order_no": "signer_tr_960195036"
	// 	}
	// }
	// {
	// 	"data": {
	// 		"en_data": "N1ajoGWgtL/wWgteVv6RK4lXVSNeCAdPk+zSpekrE3woPSl72BmLVWPqiotFA2M4oo5MqDYWAhE94dNb66esQjeE7Ff2GFz5tTIAf85pD3zX1W7w1NU8MAmR8TwAuy0F3JHhkNHqmvp2jgm1LRfWEHwm8fB7r4i852N+8WeI+wjb2HmMeWkgskzq3+EQJ1x3v9n9AIzOIkp0WGWXevo6bSbBVWcqEm3eoqf1IN56JCuXHhf8v4nzXn+IrNKBt1C14gPzDIdCMNLsPtKeZpyeZfz4acVzKfyJS7gDjkt/CUis/KHmr5Q+qcwL5bhSMrUVSd7E63nGl8Z0pjXozqWwYznL6Ncs1JurlRBk5RSmcEWfXmrXMXAPkgsRIia8uoOa6FbGX8dX5VZxOaPjzXqEiq4GULu8/ZfajEdQQWDl1xjyNnXmZtR9AJDxsdxVSi7Q",
	// 		"partner_key": "fnFq51Tk_R6j8FZIZC8I_ngCdDeMEbib",
	// 		"mch_order_no": "signer_tr_276889882"
	// 	}
	// }

	var remoteResp model.ZappayWebhookEncryptPayload
	errJson := json.Unmarshal([]byte(req.JsonPayload), &remoteResp)
	if errJson != nil {
		log.Println("Error unmarshal json", errJson)
		return nil, internalServerError(errJson)
	}

	// Later : check partner_key ???
	if remoteResp.Data.MchOrderNo == "" || remoteResp.Data.PartnerKey == "" {
		return nil, internalServerError(fmt.Errorf("INVALID_REFERENCE"))
	}

	// Service Race Condition by Ref1(MchOrderNo)
	var rcCreateBody model.RaceActionCreateBody
	rcCreateBody.Name = "CreateZappayWebhook"
	rcCreateBody.JsonRequest = helper.StructJson(req)
	rcCreateBody.Status = "PENDING"
	// ActionKey = Ref2-MINUTE
	rcCreateBody.ActionKey = fmt.Sprintf("%s-%s", remoteResp.Data.MchOrderNo, time.Now().Format("**********"))
	rcCreateBody.UnlockAt = time.Now().UTC().Add(time.Second * 1)
	if _, err := s.repo.CreateRaceCondition(rcCreateBody); err != nil {
		return insertId, internalServerError(err)
	}

	// Get Setting + Decrypt
	pgAccount, err := s.GetZappayAccount()
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}
	payload, err := s.repo.ZappayDecryptRepayDespositPayload(*pgAccount, remoteResp)
	if err != nil {
		return nil, internalServerError(err)
	}

	// fmt.Println("ZappayDecryptRepayDespositPayload.payload", helper.StructJson(payload))

	// Get Posible Order
	var query model.ZappayOrderListRequest
	query.OrderNo = payload.MchOrderNo
	query.TransactionNo = payload.OrderNo
	// query.Amount = fmt.Sprintf("%f", remoteResp.Amount)
	query.Status = "WAIT_PAYMENT"
	query.Limit = 1
	list, _, err := s.repo.GetDbZappayOrderList(query)
	if err != nil {
		return nil, internalServerError(err)
	}

	// fmt.Println("ZappayDecryptRepayDespositPayload.list", helper.StructJson(list))

	if len(list) > 0 {
		for _, item := range list {
			// Update Order
			// 2-1. Order Status
			// status code Notes
			// 0 Processing
			// 1 success
			// 2 failed
			// 3 Cancellation, transaction failed（Only for Payment）
			successStatus := strings.ToUpper(fmt.Sprintf("%d", payload.Status))
			if successStatus == "1" {
				successStatus = "PAID"
			} else if successStatus == "2" {
				successStatus = "ERROR"
			} else if successStatus == "3" {
				successStatus = "CANCEL"
			}
			if err := s.repo.ApproveDbZappayOrder(item.Id, successStatus); err != nil {
				return nil, internalServerError(err)
			}
			// Payment Success, Create Deposit if UserId is not null
			if item.UserId > 0 && item.OrderTypeId == model.ZAPPAY_ORDER_TYPE_DEPOSIT {
				if successStatus == "PAID" {
					if _, err := CreateCustomerDepositFromZappayOrder(s.repo, item.Id, 0); err != nil {
						// WebhookLog
						var createBody2 model.ZappayWebhookCreateBody
						createBody2.Name = "ZAPPAY_DEPOSIT_ERROR"
						createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
							"item":   item,
							"method": "createCustomerDepositFromZappay",
							"error":  err.Error(),
						})
						if _, err = s.repo.CreateZappayWebhook(createBody2); err != nil {
							log.Println("Error CreateZappayWebhook.createCustomerDepositFromZappay", err)
						}
					}
				}
			} else if item.UserId > 0 && item.OrderTypeId == model.ZAPPAY_ORDER_TYPE_WITHDRAW {
				if successStatus == "PAID" {
					if _, err := approveCustomerWithdrawFromZappay(s.repo, *item.RefId); err != nil {
						// WebhookLog
						var createBody2 model.ZappayWebhookCreateBody
						createBody2.Name = "ZAPPAY_WITHDRAW_ERROR"
						createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
							"item":   item,
							"method": "approveCustomerWithdrawFromZappay",
							"error":  err.Error(),
						})
						if _, err = s.repo.CreateZappayWebhook(createBody2); err != nil {
							log.Println("Error approveCustomerWithdrawFromZappay.CreateZappayWebhook", err)
						}
					}
				} else if successStatus == "ERROR" || successStatus == "CANCEL" {
					if err := s.cancelWithdrawFromZappayWebhookError(item); err != nil {
						log.Println("Error UpdateDbZappayOrderError", err)
					}
				}
			} else {
				log.Println("ApproveDbZappayOrder.WithoutItemUpdated", helper.StructJson(item))
			}
		}
	}
	return insertId, nil
}

func (s paygateZappayService) CreateZappayWithdrawWebhook(req model.ZappayWebhookRequest) (*int64, error) {

	var createBody model.ZappayWebhookCreateBody
	createBody.Name = "ZAPPAY_LOAN_CALLBACK"
	createBody.JsonPayload = req.JsonPayload
	insertId, err := s.repo.CreateZappayWebhook(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// WITHDRAW
	var remoteResp model.ZappayWebhookEncryptPayload
	errJson := json.Unmarshal([]byte(req.JsonPayload), &remoteResp)
	if errJson != nil {
		log.Println("Error unmarshal json", errJson)
		return nil, internalServerError(errJson)
	}

	// Later : check partner_key ???
	if remoteResp.Data.MchOrderNo == "" || remoteResp.Data.PartnerKey == "" {
		return nil, internalServerError(fmt.Errorf("INVALID_REFERENCE"))
	}

	// Service Race Condition by Ref1(MchOrderNo)
	var rcCreateBody model.RaceActionCreateBody
	rcCreateBody.Name = "CreateZappayWebhook"
	rcCreateBody.JsonRequest = helper.StructJson(req)
	rcCreateBody.Status = "PENDING"
	// ActionKey = Ref2-MINUTE
	rcCreateBody.ActionKey = fmt.Sprintf("%s-%s", remoteResp.Data.MchOrderNo, time.Now().Format("**********"))
	rcCreateBody.UnlockAt = time.Now().UTC().Add(time.Second * 1)
	if _, err := s.repo.CreateRaceCondition(rcCreateBody); err != nil {
		return insertId, internalServerError(err)
	}

	// Get Setting + Decrypt
	pgAccount, err := s.GetZappayAccount()
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}
	payload, err := s.repo.ZappayDecryptRepayDespositPayload(*pgAccount, remoteResp)
	if err != nil {
		return nil, internalServerError(err)
	}

	// fmt.Println("ZappayDecryptRepayDespositPayload.payload", helper.StructJson(payload))

	// Get Posible Order
	var query model.ZappayOrderListRequest
	query.OrderNo = payload.MchOrderNo
	query.TransactionNo = payload.OrderNo
	// query.Amount = fmt.Sprintf("%f", remoteResp.Amount)
	query.Status = "WAIT_PAYMENT"
	query.Limit = 1
	list, _, err := s.repo.GetDbZappayOrderList(query)
	if err != nil {
		return nil, internalServerError(err)
	}

	// fmt.Println("ZappayDecryptRepayDespositPayload.list", helper.StructJson(list))

	if len(list) > 0 {
		for _, item := range list {
			// Update Order
			// 2-1. Order Status
			// status code Notes
			// 0 Processing
			// 1 success
			// 2 failed
			// 3 Cancellation, transaction failed（Only for Payment）
			successStatus := strings.ToUpper(fmt.Sprintf("%d", payload.Status))
			if successStatus == "1" {
				successStatus = "PAID"
			} else if successStatus == "2" {
				successStatus = "ERROR"
			} else if successStatus == "3" {
				successStatus = "CANCEL"
			}
			if err := s.repo.ApproveDbZappayOrder(item.Id, successStatus); err != nil {
				return nil, internalServerError(err)
			}
			// Payment Success, Create Deposit if UserId is not null
			if item.UserId > 0 && item.OrderTypeId == model.ZAPPAY_ORDER_TYPE_DEPOSIT {
				if successStatus == "PAID" {
					if _, err := CreateCustomerDepositFromZappayOrder(s.repo, item.Id, 0); err != nil {
						// WebhookLog
						var createBody2 model.ZappayWebhookCreateBody
						createBody2.Name = "ZAPPAY_DEPOSIT_ERROR"
						createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
							"item":   item,
							"method": "createCustomerDepositFromZappay",
							"error":  err.Error(),
						})
						if _, err = s.repo.CreateZappayWebhook(createBody2); err != nil {
							log.Println("Error CreateZappayWebhook.createCustomerDepositFromZappay", err)
						}
					}
				}
			} else if item.UserId > 0 && item.OrderTypeId == model.ZAPPAY_ORDER_TYPE_WITHDRAW {
				if successStatus == "PAID" {
					if _, err := approveCustomerWithdrawFromZappay(s.repo, *item.RefId); err != nil {
						// WebhookLog
						var createBody2 model.ZappayWebhookCreateBody
						createBody2.Name = "ZAPPAY_WITHDRAW_ERROR"
						createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
							"item":   item,
							"method": "approveCustomerWithdrawFromZappay",
							"error":  err.Error(),
						})
						if _, err = s.repo.CreateZappayWebhook(createBody2); err != nil {
							log.Println("Error approveCustomerWithdrawFromZappay.CreateZappayWebhook", err)
						}
					}
				} else if successStatus == "ERROR" || successStatus == "CANCEL" {
					if err := s.cancelWithdrawFromZappayWebhookError(item); err != nil {
						log.Println("Error UpdateDbZappayOrderError", err)
					}
				}
			} else {
				log.Println("ApproveDbZappayOrder.WithoutItemUpdated", helper.StructJson(item))
			}
		}
	}
	return insertId, nil
}

func (s paygateZappayService) cancelWithdrawFromZappayWebhookError(payonexOrder model.ZappayOrderResponse) error {

	adminId := int64(1)

	withdrawTrans, err := s.repo.GetBankTransactionById(*payonexOrder.RefId)
	if err != nil {
		log.Println("rollbackWithdrawFromZappay.GetBankTransactionById", err)
		return internalServerError(err)
	}
	if withdrawTrans.TransactionStatusId != model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		return internalServerError(errors.New("ไม่อยู๋ในสถานะที่สามารถยกเลิกได้"))
	}

	user, err := s.repo.GetUserBankDetailById(withdrawTrans.UserId)
	if err != nil {
		return internalServerError(err)
	}

	paygateOrder, err := s.repo.GetDbZappayOrderByRefId(withdrawTrans.Id)
	if err != nil {
		log.Println("CancelWithdrawFromZappay.GetDbZappayOrderByRefId", err)
		return internalServerError(err)
	}
	waitStatus := "ERROR"
	if paygateOrder.TransactionStatus == nil || *paygateOrder.TransactionStatus != waitStatus {
		return internalServerError(errors.New("ไม่อยู๋ในสถานะที่สามารถยกเลิกได้"))
	}

	returnAmount := withdrawTrans.CreditAmount

	// race condition deposit
	actionAt := time.Now().UTC().Format("20**********")
	var createBody model.RaceActionCreateBody
	createBody.Name = "ADMIN_CANCEL_WITHDRAW"
	createBody.JsonRequest = helper.StructJson(map[string]interface{}{
		"transId":      withdrawTrans.Id,
		"returnAmount": returnAmount,
	})
	createBody.Status = "PENDING"
	createBody.ActionKey = fmt.Sprintf("WITHDRAW_CANCEL_T%sU%dID%d", actionAt, user.Id, withdrawTrans.Id)
	createBody.UnlockAt = time.Now().UTC().Add(time.Second * 1)
	if _, err := s.repo.GetRaceActionIdByActionKey(createBody.ActionKey); err != nil {
		log.Println("CancelWithdrawFromZappay.GetRaceActionIdByActionKey", err)
		if err != gorm.ErrRecordNotFound {
			return internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
		}
	} else {
		return internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
	}

	// create race condition
	actionId, err := s.repo.CreateRaceCondition(createBody)
	if err != nil {
		log.Println("CancelWithdrawFromZappay.ERROR.CreateRaceCondition", err)
		return internalServerError(errors.New("CANNOT_CREATE_ACTION_LUCKY_WHEEL_ROUND"))
	}
	if actionId == 0 {
		return internalServerError(errors.New("CANNOT_CREATE_ACTION_LUCKY_WHEEL_ROUND"))
	}

	// SET ORDER AS ERROR
	remark := "Payment Cancel Withdraw"
	if err := s.repo.UpdateDbZappayOrderError(paygateOrder.Id, remark); err != nil {
		log.Println("CreateZappayWithdraw.UpdateDbZappayOrderError", err)
	}

	if withdrawTrans.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		// [********] check user credit transaction ว่าตรงไหมกับเงื่อนไข (isShow = false)
		userTransaction, err := s.repo.GetUserWithdrawCreditTransactionByRefId(withdrawTrans.Id, returnAmount)
		if err != nil {
			if err.Error() == recordNotFound {
				log.Println("CancelWithdrawCredit.GetUserCreditTransactionById", err)
				return badRequest("ไม่ตรงกับเงือนไข")
			}
			return internalServerError(err)
		}
		if userTransaction != nil {
			var userCreditReq model.UserTransactionCreateRequest
			userCreditReq.UserId = user.Id
			userCreditReq.TransferAt = withdrawTrans.TransferAt
			userCreditReq.RefId = &withdrawTrans.Id
			userCreditReq.TypeId = model.CREDIT_TYPE_DEPOSIT
			userCreditReq.Amount = returnAmount
			IsShow := false
			userCreditReq.IsShow = &IsShow
			userCreditReq.ConfirmBy = &adminId
			if agentResp, err := s.repo.IncreaseUserCredit(userCreditReq); err != nil {
				log.Println("CancelWithdrawCredit.IncreaseUserCredit", err)
				return internalServerError(err)
			} else if !agentResp.AgentSuccess {
				// [********] error from agent will be in failed
				// if err := s.repoAccounting.UpdateDeporsitTransactionStatusFromAgent(*transId, *agentResp); err != nil {
				//     log.Println("CancelWithdrawCredit.UpdateDeporsitTransactionStatusFromAgent", err)
				// }
				// Set FastBank-READ ** ไม่มี เพราะเป็นการสร้างรายการฝากเอง
				agentFail := errors.New("ทำรายการเครดิตไม่สำเร็จ")
				log.Println("CancelWithdrawCredit.IncreaseUserCredit", agentFail)
				return internalServerError(agentFail)
			}
		}
	}

	// remark := "ADMIN_CANCEL_WITHDRAW"
	var updateApprovedBy model.UpdateUserTransactionStatusRequest
	updateApprovedBy.Id = withdrawTrans.Id
	updateApprovedBy.CanceledAt = time.Now()
	updateApprovedBy.CanceledByAdminId = adminId
	updateApprovedBy.TransactionStatusId = model.TRANS_STATUS_WITHDRAW_CANCELED
	updateApprovedBy.CancelRemark = &remark
	err = s.repo.UpdateUserTransactionStatus(updateApprovedBy)
	if err != nil {
		return internalServerError(err)
	}

	var createConfirm model.CreateSuccessTransferWithdrawRequest
	createConfirm.TransactionId = withdrawTrans.Id
	createConfirm.ConfirmedAt = time.Now()
	createConfirm.ConfirmedByAdminId = &adminId
	if _, err := CreateCanceledTransferWithdraw(repository.NewBankingRepository(s.sharedDb), createConfirm); err != nil {
		return err
	}

	return nil
}
