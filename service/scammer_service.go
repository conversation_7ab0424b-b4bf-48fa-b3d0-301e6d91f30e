package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"fmt"
	"log"
	"time"
)

type ScammerService interface {
	GetScammerList(req model.ScammerQuery) (*model.SuccessWithPagination, error)
	CreateScammer(req model.CreateScammer, adminId int64) (*int64, error)
	DeleteScammer(id int64, adminId int64) error
	GetScammerSummary() (*model.ScammerSummary, error)
	// [ADMIN_LOG]
	LogAdmin(name string, adminId int64, req interface{}) error
}

type scammerService struct {
	repo repository.ScammerRepository
}

func NewScammerService(
	repo repository.ScammerRepository,
) ScammerService {
	return &scammerService{repo}
}

func (s *scammerService) GetScammerList(req model.ScammerQuery) (*model.SuccessWithPagination, error) {

	if err := helper.Pagination(&req.Page, &req.Limit); err != nil {
		return nil, err
	}

	scammers, total, err := s.repo.GetScammerList(req)
	if err != nil {
		return nil, err
	}

	var response model.SuccessWithPagination
	response.List = scammers
	response.Total = *total

	return &response, nil
}

func (s *scammerService) CreateScammer(req model.CreateScammer, adminId int64) (*int64, error) {

	var cleanUpData = make(map[string]interface{})

	scammer, err := s.repo.GetScammerByUserId(req.UserId)
	if err != nil {
		log.Println(err)
		return nil, err
	}
	if scammer.Id != 0 {
		return nil, badRequest("USER_ALREADY_SCAMMER")
	}

	if user, err := s.repo.GetUserById(req.UserId); err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound("USER_NOT_FOUND")
		}
		return nil, err
	} else if user == nil {
		return nil, notFound("USER_NOT_FOUND")
	}

	createId, err := s.repo.CreateScammer(req)
	if err != nil {
		return nil, err
	}
	cleanUpData["CreatedScammerId"] = createId

	// SET-USER
	var updateBody model.ScammerUpdateUser
	updateBody.Id = req.UserId
	status := model.USER_STATUS_DEACTIVE
	updateBody.UserStatusId = status
	time := time.Now()
	updateBody.DeletedAt = &time
	if err := s.repo.UpdateUserStatus(updateBody); err != nil {
		return nil, err
	}

	// SET-REMOTE-USER
	if _, err = s.SubmitScammer(*createId); err != nil {
		log.Println("SubmitScammer", err)
	}

	// [ADMIN_ACTION] SUCCESS บันทึกรายงานมิจฉาชีพ ผู้ใช้งาน {name}
	var adminActionCreateBody model.AdminActionCreateRequest
	adminActionCreateBody.AdminId = adminId
	adminActionCreateBody.TypeId = model.ADMIN_ACTION_MANAGE_USER
	adminActionCreateBody.Detail = fmt.Sprintf("บันทึกรายงานมิจฉาชีพ ผู้ใช้งาน %s", scammer.Fullname)
	adminActionCreateBody.JsonInput = helper.StructJson(req)
	if _, err := s.CreateSuccessAdminAction(adminActionCreateBody); err != nil {
		log.Println("UserWithdrawSettingCreate.CreateSuccessAdminAction", err)
	}

	return createId, nil
}

func (s scammerService) SubmitScammer(id int64) (*int64, error) {

	scammer, err := s.repo.GetScammerById(id)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(recordNotFound)
		}
		return nil, internalServerError(err)
	}

	var remoteReq model.ScammerSubmitRequest
	remoteReq.Id = scammer.Id
	remoteReq.Phone = scammer.Phone
	remoteReq.BankName = scammer.BankName
	remoteReq.BankAccount = scammer.BankAccount
	remoteReq.Reason = scammer.Reason
	remoteReq.AccountName = scammer.Fullname
	insertId, err := s.repo.SubmitScammer(remoteReq)
	if err != nil {
		return nil, internalServerError(err)
	}
	return insertId, nil
}

func (s *scammerService) CleanUpScammer(cleanUpData map[string]interface{}) error {

	for k, v := range cleanUpData {
		switch k {
		case "CreatedScammerId":
			if err := s.repo.DeleteScammer(v.(int64)); err != nil {
				return err
			}

		}
	}
	return nil
}

func (s *scammerService) DeleteScammer(id int64, adminId int64) error {

	scammer, err := s.repo.GetScammerById(id)
	if err != nil {
		if err.Error() == "record not found" {
			return notFound("Scammer not found")
		} else {
			return err
		}
	}

	if err := s.repo.DeleteScammer(id); err != nil {
		return err
	}

	var updateBody model.ScammerDelete
	updateBody.Id = scammer.UserId
	status := model.USER_STATUS_ACTIVE
	updateBody.UserStatusId = status
	if err := s.repo.UpdateDeleteUserStatus(updateBody); err != nil {
		return err
	}

	// [ADMIN_ACTION] SUCCESS ยกเลิกการเป็นมิจฉาชีพ ผู้ใช้งาน {name}
	var adminActionCreateBody model.AdminActionCreateRequest
	adminActionCreateBody.AdminId = adminId
	adminActionCreateBody.TypeId = model.ADMIN_ACTION_MANAGE_USER
	adminActionCreateBody.Detail = fmt.Sprintf("ยกเลิกการเป็นมิจฉาชีพ ผู้ใช้งาน %s", scammer.Fullname)
	adminActionCreateBody.JsonInput = helper.StructJson(id)
	if _, err := s.CreateSuccessAdminAction(adminActionCreateBody); err != nil {
		log.Println("UserWithdrawSettingCreate.CreateSuccessAdminAction", err)
	}

	return nil
}

func (s *scammerService) CreateSuccessAdminAction(req model.AdminActionCreateRequest) (*int64, error) {

	var createBody model.AdminActionCreateBody
	createBody.AdminId = req.AdminId
	createBody.TypeId = req.TypeId
	createBody.IsSuccess = true
	createBody.IsShow = true
	createBody.RefObjectId = req.RefObjectId
	createBody.Detail = req.Detail
	createBody.JsonInput = req.JsonInput
	createBody.JsonOutput = req.JsonOutput
	insertId, err := s.repo.CreateAdminAction(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}
	return insertId, nil
}

func (s *scammerService) GetScammerSummary() (*model.ScammerSummary, error) {

	var response model.ScammerSummary

	// [2024-08-26] ปิดเนื่องจากใช้ CPU เยอะ
	// result, err := s.repo.GetScammerSummary()
	// if err != nil {
	// 	return nil, err
	// }
	// response.TotalCount = result.TotalCount

	return &response, nil
}

func (s scammerService) LogAdmin(name string, adminId int64, req interface{}) error {

	var createBody model.AdminLogCreateBody
	createBody.Name = name
	createBody.AdminId = adminId
	createBody.JsonReq = helper.StructJson(req)
	if _, err := s.repo.CreateAdminLog(createBody); err != nil {
		return internalServerError(err)
	}
	return nil
}
