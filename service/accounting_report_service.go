package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"errors"
	"fmt"
	"log"
	"time"
)

type AccountingReportService interface {
	// Summary report
	GetSummaryReportAccountList() ([]model.FastBankAccountResponse, error)
	GetSummaryReportDaily(req model.ReportSummaryRequest) (*model.ReportSummaryResponse, error)
	ManualBackupSummaryReportDaily(req model.ReportSummaryRequest) ([]model.CreateReportSummary, error)
	GetUserCreditSummaryTotal() (*model.TotalSumUserActiveCredit, error)
	ManualBackupSummaryReportDailyReRun(createBody model.CreateReportSummaryDashboardRerun) error
	CheckAlreadyExistReportSummaryDashboardRerun() (bool, error)
}

type accountingReportService struct {
	repo repository.AccountingReportRepository
}

func NewAccountingReportService(
	repo repository.AccountingReportRepository,
) AccountingReportService {
	return &accountingReportService{repo}
}

func (s *accountingReportService) GetSummaryReportAccountList() ([]model.FastBankAccountResponse, error) {

	list, _, err := s.repo.GetSummaryReportAccountList()
	if err != nil {
		return nil, internalServerError(err)
	}
	return list, nil
}

func (s *accountingReportService) GetSummaryReportDailyNew(req model.ReportSummaryRequest) (*model.ReportSummaryResponse, error) {

	var report model.ReportSummaryResponse
	report.DateType = req.DateType

	// if userReport, err := s.repo.GetUserReportDaily(req); err == nil {
	// 	report.TotalUserCount = userReport.TotalUserCount
	// 	report.DateFrom = userReport.DateFrom
	// 	report.DateTo = userReport.DateTo
	// }

	// รอเทสแล้วจะเอาไปใช้ แทน
	getReportSummaryList, err := s.repo.GetReportSummaryListTotal(req)
	if err != nil {
		return nil, internalServerError(err)
	}

	// report.TotalNewUserCount = getReportSummaryList.TotalNewUserCount
	// report.TotalActiveUserCount = getReportSummaryList.TotalActiveUserCount
	report.TotalFirstDepositPrice = getReportSummaryList.TotalFirstDepositPrice
	report.TotalFirstDepositUserCount = getReportSummaryList.TotalFirstDepositUserCount
	report.TotalDepositPrice = getReportSummaryList.TotalDepositPrice
	report.TotalDepositUserCount = getReportSummaryList.TotalDepositUserCount
	report.TotalWithdrawPrice = getReportSummaryList.TotalWithdrawPrice
	report.TotalWithdrawUserCount = getReportSummaryList.TotalWithdrawUserCount
	report.TotalBankProfit = getReportSummaryList.TotalBankProfit
	report.TotalAffiliatePrice = getReportSummaryList.TotalAffiliatePrice
	report.TotalAlliancePrice = getReportSummaryList.TotalAlliancePrice
	report.TotalReturnLossTakenPrice = getReportSummaryList.TotalReturnLossTakenPrice
	report.TotalReturnTurnTakenPrice = getReportSummaryList.TotalReturnTurnTakenPrice
	report.TotalCreditBackPrice = getReportSummaryList.TotalCreditBackPrice
	report.TotalCreditBackCount = getReportSummaryList.TotalCreditBackCount
	report.TotalTurn = getReportSummaryList.TotalTurn
	report.TotalWinlose = getReportSummaryList.TotalWinlose
	report.TotalProfitPrice = getReportSummaryList.TotalProfitPrice
	report.TotalBonusPrice = getReportSummaryList.TotalBonusPrice
	report.TotalBonusCount = getReportSummaryList.TotalBonusCount
	report.TotalPromotionWebCredit = getReportSummaryList.TotalPromotionWebCredit
	report.TotalPromotionReturnLoss = getReportSummaryList.TotalPromotionReturnLoss
	report.TotalActivityLuckyWheel = getReportSummaryList.TotalActivityLuckyWheel
	report.TotalCheckInBonus = getReportSummaryList.TotalCheckInBonus
	report.TotalPromotionCashCoupon = getReportSummaryList.TotalPromotionCashCoupon
	report.TotalAdminCreateBonus = getReportSummaryList.TotalAdminCreateBonus
	report.TotalPromotionReturnTurn = getReportSummaryList.TotalPromotionReturnTurn

	// [********] ยอดฝาก และ ยอดฝากครั้งแรก จะถูกหักไปด้วยยอดดึงเครดิตกลับ.
	// ลบแต่ยอด สุทธิ จาก ยอดดึงเครดิตกลับ
	// [********] report.TotalBankProfit -= report.TotalCreditBackPrice
	// ยืนยันวันที่ 18/04/25
	// ก่อนหน้านี้พี่เลย์แจ้งปรับ (การ์ดสีฟ้า) คือยอดฝาก - ยอดถอน - ยอดดึงเครดิตกลับ = ยอดสุทธิ (ฝาก-ถอน)
	// ปรับให้เป็นเหมือนเดิมคือ ยอดฝาก - ยอดถอน = ยอดสุทธิ (ฝาก-ถอน)

	return &report, nil
}

func (s *accountingReportService) GetSummaryReportDaily(req model.ReportSummaryRequest) (*model.ReportSummaryResponse, error) {

	dateType, err := s.repo.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.DateFrom,
		DateTo:   req.DateTo,
	})
	if err != nil {
		return nil, err
	}

	currentDate := time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02")

	startDate, _ := time.Parse("2006-01-02", dateType.DateFrom)
	endDate, _ := time.Parse("2006-01-02", dateType.DateTo)
	currentDateParsed, _ := time.Parse("2006-01-02", currentDate)

	reportNew := &model.ReportSummaryResponse{}

	if startDate.Before(currentDateParsed) {
		// Adjust DateTo only if it is after the current date
		adjustedDateTo := dateType.DateTo
		if endDate.After(currentDateParsed) || endDate.Equal(currentDateParsed) {
			adjustedDateTo = currentDateParsed.AddDate(0, 0, -1).Format("2006-01-02")
		}

		newRequest := model.ReportSummaryRequest{
			DateFrom: dateType.DateFrom,
			DateTo:   adjustedDateTo,
		}

		reportNew, err = s.GetSummaryReportDailyNew(newRequest)
		if err != nil {
			return nil, err
		}
	}

	if userReport, err := s.repo.GetUserReportDaily(req); err == nil {
		reportNew.TotalNewUserCount = userReport.TotalNewUserCount
		reportNew.TotalUserCount = userReport.TotalUserCount
		// ยอดใช้งาน [********] นับเฉพาะ Users ที่มีรหัสสมาชิกแล้วเท่านั้น
		reportNew.TotalActiveUserCount = userReport.TotalActiveUserCount
		reportNew.DateFrom = userReport.DateFrom
		reportNew.DateTo = userReport.DateTo
	}

	if endDate.Equal(currentDateParsed) || startDate.Equal(currentDateParsed) || endDate.After(currentDateParsed) {
		oldRequest := model.ReportSummaryRequest{
			DateFrom: currentDate,
			DateTo:   dateType.DateTo,
		}

		reportOld, err := s.GetSummaryReportDailyOld(oldRequest)
		if err != nil {
			return nil, err
		}

		if reportNew != nil {
			// reportNew.TotalUserCount = reportOld.TotalUserCount
			// reportNew.TotalNewUserCount += reportOld.TotalNewUserCount
			// reportNew.TotalActiveUserCount += reportOld.TotalActiveUserCount
			reportNew.TotalFirstDepositPrice += reportOld.TotalFirstDepositPrice
			reportNew.TotalFirstDepositUserCount += reportOld.TotalFirstDepositUserCount
			reportNew.TotalDepositPrice += reportOld.TotalDepositPrice
			reportNew.TotalDepositUserCount += reportOld.TotalDepositUserCount
			reportNew.TotalWithdrawPrice += reportOld.TotalWithdrawPrice
			reportNew.TotalWithdrawUserCount += reportOld.TotalWithdrawUserCount
			reportNew.TotalBankProfit += reportOld.TotalBankProfit
			reportNew.TotalAffiliatePrice += reportOld.TotalAffiliatePrice
			reportNew.TotalAlliancePrice += reportOld.TotalAlliancePrice
			reportNew.TotalReturnLossTakenPrice += reportOld.TotalReturnLossTakenPrice
			reportNew.TotalReturnTurnTakenPrice += reportOld.TotalReturnTurnTakenPrice
			reportNew.TotalCreditBackPrice += reportOld.TotalCreditBackPrice
			reportNew.TotalCreditBackCount += reportOld.TotalCreditBackCount
			reportNew.TotalTurn += reportOld.TotalTurn
			reportNew.TotalWinlose += reportOld.TotalWinlose
			reportNew.TotalProfitPrice += reportOld.TotalProfitPrice
			reportNew.TotalBonusPrice += reportOld.TotalBonusPrice
			reportNew.TotalBonusCount += reportOld.TotalBonusCount
			reportNew.TotalPromotionWebCredit += reportOld.TotalPromotionWebCredit
			reportNew.TotalPromotionReturnLoss += reportOld.TotalPromotionReturnLoss
			reportNew.TotalActivityLuckyWheel += reportOld.TotalActivityLuckyWheel
			reportNew.TotalCheckInBonus += reportOld.TotalCheckInBonus
			reportNew.TotalPromotionCashCoupon += reportOld.TotalPromotionCashCoupon
			reportNew.TotalAdminCreateBonus += reportOld.TotalAdminCreateBonus
			reportNew.TotalPromotionReturnTurn += reportOld.TotalPromotionReturnTurn

			// [********] ยอดฝาก และ ยอดฝากครั้งแรก จะถูกหักไปด้วยยอดดึงเครดิตกลับ.
			// ลบแต่ยอด สุทธิ จาก ยอดดึงเครดิตกลับ
			// reportNew.TotalBankProfit -= reportNew.TotalCreditBackPrice
			// ยืนยันวันที่ 18/04/25
			// ก่อนหน้านี้พี่เลย์แจ้งปรับ (การ์ดสีฟ้า) คือยอดฝาก - ยอดถอน - ยอดดึงเครดิตกลับ = ยอดสุทธิ (ฝาก-ถอน)
			// ปรับให้เป็นเหมือนเดิมคือ ยอดฝาก - ยอดถอน = ยอดสุทธิ (ฝาก-ถอน)
		}

		if reportNew != nil {
			reportNew.DateType = req.DateType
			reportNew.DateFrom = req.DateFrom
			reportNew.DateTo = req.DateTo
			return reportNew, nil
		}
		reportOld.DateType = req.DateType
		reportOld.DateFrom = req.DateFrom
		reportOld.DateTo = req.DateTo
		return reportOld, nil
	}

	reportNew.DateType = req.DateType
	reportNew.DateFrom = req.DateFrom
	reportNew.DateTo = req.DateTo
	return reportNew, nil
}

// func CompareStructs(old interface{}, new interface{}) {
// เอาไว้เช็ค 2 struct ว่ามีค่าต่างกันหรือไม่
// 	oldVal := reflect.ValueOf(old)
// 	newVal := reflect.ValueOf(new)

// 	// Ensure we are dealing with structs
// 	if oldVal.Kind() == reflect.Struct && newVal.Kind() == reflect.Struct {
// 		for i := 0; i < oldVal.NumField(); i++ {
// 			oldField := oldVal.Field(i)
// 			newField := newVal.Field(i)

// 			// Compare field values
// 			if !reflect.DeepEqual(oldField.Interface(), newField.Interface()) {
// 				fieldName := oldVal.Type().Field(i).Name
// 				fmt.Printf("Difference in %s: old=%v, new=%v\n", fieldName, oldField.Interface(), newField.Interface())
// 			}
// 		}
// 	} else {
// 		fmt.Println("Both inputs must be structs")
// 	}
// }

func (s *accountingReportService) GetSummaryReportDailyOld(req model.ReportSummaryRequest) (*model.ReportSummaryResponse, error) {

	var report model.ReportSummaryResponse
	report.DateType = req.DateType

	// if userReport, err := s.repo.GetUserReportDaily(req); err == nil {
	// 	report.TotalNewUserCount = userReport.TotalNewUserCount
	// 	report.TotalUserCount = userReport.TotalUserCount
	// 	// ยอดใช้งาน [********] นับเฉพาะ Users ที่มีรหัสสมาชิกแล้วเท่านั้น
	// 	report.TotalActiveUserCount = userReport.TotalActiveUserCount
	// 	report.DateFrom = userReport.DateFrom
	// 	report.DateTo = userReport.DateTo
	// }
	if depositReport, err := s.repo.GetUserAccountingReportDaily(req); err == nil {
		report.TotalFirstDepositPrice = depositReport.TotalFirstDepositPrice
		report.TotalFirstDepositUserCount = depositReport.TotalFirstDepositUserCount
		report.TotalDepositPrice = depositReport.TotalDepositPrice
		report.TotalDepositUserCount = depositReport.TotalDepositUserCount
		report.TotalWithdrawPrice = depositReport.TotalWithdrawPrice
		report.TotalWithdrawUserCount = depositReport.TotalWithdrawUserCount
		report.TotalBankProfit = depositReport.TotalBankProfit
	}
	if incomeReport, err := s.repo.GetUserIncomeReportDaily(req); err == nil {
		report.TotalAffiliatePrice = incomeReport.TotalAffiliatePrice
		report.TotalAlliancePrice = incomeReport.TotalAlliancePrice
		report.TotalReturnLossTakenPrice = incomeReport.TotalReturnLossTakenPrice
		report.TotalReturnTurnTakenPrice = incomeReport.TotalReturnTurnTakenPrice
	}
	// if promotionReport, err := s.repo.GetPromotionReportDaily(req); err == nil {
	// report.TotalBonusPrice += promotionReport.TotalReturnLossTakenPrice //= 30
	// report.TotalBonusPrice += promotionReport.TotalAccountingBonusPrice //= Bonus 10
	// report.TotalBonusPrice += promotionReport.TotalAffiliateBonusPrice
	// later : more promotion ? [********] ผมมาเพิ่มรายการกิจกรรมรับโบนัสรายวันกับกงล้อ
	// [********] โบนัสทุกอย่างนับจาก user_transaction ล้วนๆ ทุกประเภท
	// report.TotalBonusPrice += promotionReport.TotalActivityBonusPrice // 40
	// }
	if creditReport, err := s.repo.GetUserCreditReportDaily(req); err == nil {
		report.TotalCreditBackPrice = creditReport.TotalCreditBackPrice
		report.TotalCreditBackCount = creditReport.TotalCreditBackCount
	}
	if playlogReport, err := s.repo.GetUserTodayPlaylogReportDaily(req); err == nil {
		// fmt.Println("playlogReport", helper.StructJson(playlogReport))
		report.TotalTurn = playlogReport.TotalTurn
		// "totalWinlose": -20 == ลูกค้าเสีย 20 report จะเป็น 20
		// ถ้า ลูกค้าได้ 20 report จะเป็น -20
		report.TotalWinlose = helper.Float64ToDecimal(playlogReport.TotalWinlose)
		report.TotalProfitPrice = playlogReport.TotalWinlose * -1
	}
	// [20240521] เปลี่ยนวิธีการคำนวน เป็น lose - win - commission

	// P.Mink บอกให้ เอา ลบ ค่าคอมมิชชั่น ออกจาก เพราะ web solo งง
	// totalCommissionPrice := report.TotalAffiliatePrice + report.TotalAlliancePrice
	// report.TotalProfitPrice = helper.Float64ToDecimal(report.TotalProfitPrice - totalCommissionPrice)
	report.TotalProfitPrice = helper.Float64ToDecimal(report.TotalProfitPrice)

	if getAcivity, err := s.repo.GetActivitySummaryReportDaily(req); err == nil {
		report.TotalPromotionWebCredit = getAcivity.TotalPromotionWebCredit
		report.TotalPromotionReturnLoss = getAcivity.TotalPromotionReturnLoss
		report.TotalActivityLuckyWheel = getAcivity.TotalActivityLuckyWheel
		report.TotalCheckInBonus = getAcivity.TotalCheckInBonus
		report.TotalPromotionCashCoupon = getAcivity.TotalPromotionCashCoupon
		report.TotalAdminCreateBonus = getAcivity.TotalAdminCreateBonus
		report.TotalPromotionReturnTurn = getAcivity.TotalPromotionReturnTurn
		// SEVEN_USER_BONUS_INCOME ** no getAcivity.TotalBonusPrice +
		report.TotalBonusPrice += getAcivity.TotalPromotionWebCredit + getAcivity.TotalPromotionReturnLoss + getAcivity.TotalActivityLuckyWheel + getAcivity.TotalCheckInBonus + getAcivity.TotalPromotionCashCoupon + getAcivity.TotalAdminCreateBonus + getAcivity.TotalPromotionReturnTurn
		report.TotalBonusCount += getAcivity.TotalBonusCount
	}

	return &report, nil
}

func (s *accountingReportService) ManualBackupSummaryReportDailyReRun(createBody model.CreateReportSummaryDashboardRerun) error {

	// max 3 days
	todayBkkTime := time.Now().UTC().Add(7 * time.Hour)
	if createBody.StartDate == "" {
		setStartDate := todayBkkTime.AddDate(0, 0, -3).Format("2006-01-02")
		createBody.StartDate = setStartDate
	}

	if createBody.EndDate == "" {
		setEndDate := todayBkkTime.Format("2006-01-02")
		createBody.EndDate = setEndDate
	}

	if createBody.StartDate != "" && createBody.EndDate != "" {
		start, err := time.Parse("2006-01-02", createBody.StartDate)
		if err != nil {
			return errors.New("invalid StartDate format, expected YYYY-MM-DD")
		}

		end, err := time.Parse("2006-01-02", createBody.EndDate)
		if err != nil {
			return errors.New("invalid EndDate format, expected YYYY-MM-DD")
		}

		// Check that StartDate is before or equal to EndDate
		if start.After(end) {
			return errors.New("StartDate cannot be after EndDate")
		}

		// Calculate the difference in days
		duration := end.Sub(start).Hours() / 24
		if duration > 2 {
			return errors.New("date range cannot exceed 3 days")
		}
	}

	// date with only hour
	createBody.ActionKey = time.Now().UTC().Format("2006-01-02 15:00:00")
	exist, err := s.repo.CheckAlreadyExistReportSummaryDashboardRerun(createBody.ActionKey)
	if err != nil {
		return err
	}
	if exist {
		return badRequest("ALREADY_RUN_REPORT_EXIST")
	}

	if err := s.repo.CreateReportSummaryDashboardRerun(createBody); err != nil {
		return internalServerError(err)
	}

	var req model.ReportSummaryRequest
	req.DateFrom = createBody.StartDate
	req.DateTo = createBody.EndDate

	_, err = s.ManualBackupSummaryReportDaily(req)
	if err != nil {
		return err
	}

	return nil
}

func (s *accountingReportService) CheckAlreadyExistReportSummaryDashboardRerun() (bool, error) {

	// date with only hour
	actionKey := time.Now().UTC().Format("2006-01-02 15:00:00")
	exist, err := s.repo.CheckAlreadyExistReportSummaryDashboardRerun(actionKey)
	if err != nil {
		return false, err
	}
	return exist, nil
}

func (s *accountingReportService) ManualBackupSummaryReportDaily(req model.ReportSummaryRequest) ([]model.CreateReportSummary, error) {

	// 2024/12/03 Admin confirm อยากได้ แบบ real time ต้องดึงทั้ง cronjob และ real time ของวันปัจจุบัน
	if req.DateType != "" {
		return nil, badRequest("Cannot use dateType in this endpoint")
	}

	var reportList []model.CreateReportSummary

	var startDate time.Time
	var err error
	if req.DateFrom != "" {
		startDate, err = time.Parse("2006-01-02", req.DateFrom)
		if err != nil {
			log.Fatal(err)
		}
		startDate = startDate.UTC().Add(7 * time.Hour) // Adjust to UTC+7 if necessary
	} else {
		startDate = startDate.UTC().Add(7 * time.Hour) // Adjust to UTC+7 if necessary
		req.DateFrom = startDate.Format("2006-01-02")
	}

	var endDate time.Time
	if req.DateTo != "" {
		endDate, err = time.Parse("2006-01-02", req.DateTo)
		if err != nil {
			log.Fatal(err)
		}
		endDate = endDate.UTC().Add(7 * time.Hour) // Adjust to UTC+7 if necessary
	} else {
		endDate = time.Now().UTC().Add(7 * time.Hour) // Adjust to UTC+7 if necessary
		req.DateTo = endDate.Format("2006-01-02")
	}

	// fmt.Println("startDate", startDate)
	// fmt.Println("endDate", endDate)

	days := int(endDate.Sub(startDate).Hours() / 24)

	for i := 0; i <= days; i++ {
		date := startDate.AddDate(0, 0, i).Format("2006-01-02")
		reportList = append(reportList, model.CreateReportSummary{
			CreatedDate: date,
		})
	}

	userReportNewUser, userReport, err := s.repo.ManualBackupGetUserReportDaily(req)
	if err != nil {
		return nil, internalServerError(err)
	}

	depositReportFirstDeposit, depositReportDeposit, depositReportWithdraw, cancelCreditReport, err := s.repo.ManualBackupGetUserAccountingReportDaily(req)
	if err != nil {
		return nil, internalServerError(err)
	}

	incomeReport, err := s.repo.ManualBackupGetUserIncomeReportDaily(req)
	if err != nil {
		return nil, internalServerError(err)
	}

	creditPullBack, err := s.repo.ManualBackupGetUserCreditReportDaily(req)
	if err != nil {
		return nil, internalServerError(err)
	}

	playlogReport, err := s.repo.ManualBackupGetUserTodayPlaylogReportDaily(req)
	if err != nil {
		if err.Error() != "record not found" {
			fmt.Println("err", err)
		} else {
			return nil, internalServerError(err)
		}
	}

	activity, err := s.repo.ManualBackupGetActivitySummaryReportDaily(req)
	if err != nil {
		return nil, internalServerError(err)
	}

	for i, report := range reportList {

		for _, newUser := range userReportNewUser {
			if report.CreatedDate == newUser.CreatedDate.Format("2006-01-02") {
				reportList[i].TotalNewUserCount = newUser.TotalNewUserCount
			}
		}

		for _, user := range userReport {
			if report.CreatedDate == user.CreatedDate.Format("2006-01-02") {
				reportList[i].TotalActiveUserCount = user.TotalActiveUserCount
			}
		}

		for _, firstDeposit := range depositReportFirstDeposit {
			if report.CreatedDate == firstDeposit.CreatedDate.Format("2006-01-02") {
				reportList[i].TotalFirstDepositPrice = firstDeposit.TotalFirstDepositPrice
				reportList[i].TotalFirstDepositUserCount = firstDeposit.TotalFirstDepositUserCount
			}
		}

		for _, deposit := range depositReportDeposit {
			if report.CreatedDate == deposit.CreatedDate.Format("2006-01-02") {
				reportList[i].TotalDepositPrice += deposit.TotalDepositPrice
				reportList[i].TotalDepositUserCount = deposit.TotalDepositUserCount
			}
		}
		// [20250422] ยกเลิกเติมเครดิต = หักลบออกจากยอดฝาก
		for _, cancelCredit := range cancelCreditReport {
			if report.CreatedDate == cancelCredit.CreatedDate.Format("2006-01-02") {
				reportList[i].TotalDepositPrice -= cancelCredit.TotalCancelCreditBack
			}
		}

		for _, withdraw := range depositReportWithdraw {
			if report.CreatedDate == withdraw.CreatedDate.Format("2006-01-02") {
				reportList[i].TotalWithdrawPrice = withdraw.TotalWithdrawPrice
				reportList[i].TotalWithdrawUserCount = withdraw.TotalWithdrawUserCount
			}
		}

		// totalBankProfit
		for _, deposit := range depositReportDeposit {
			if report.CreatedDate == deposit.CreatedDate.Format("2006-01-02") {
				// ใช้ที่หักลบไปแทน reportList[i].TotalBankProfit = deposit.TotalDepositPrice
				reportList[i].TotalBankProfit = reportList[i].TotalDepositPrice
			}
		}
		for _, withdraw := range depositReportWithdraw {
			if report.CreatedDate == withdraw.CreatedDate.Format("2006-01-02") {
				if reportList[i].TotalBankProfit == 0 {
					reportList[i].TotalBankProfit = -withdraw.TotalWithdrawPrice
				} else {
					reportList[i].TotalBankProfit -= withdraw.TotalWithdrawPrice
				}
			}
		}

		for _, income := range incomeReport {
			if report.CreatedDate == income.CreatedDate.Format("2006-01-02") {
				reportList[i].TotalAffiliatePrice = income.TotalAffiliatePrice
				reportList[i].TotalAlliancePrice = income.TotalAlliancePrice
				reportList[i].TotalReturnLossTakenPrice = income.TotalReturnLossTakenPrice
				reportList[i].TotalReturnTurnTakenPrice = income.TotalReturnTurnTakenPrice
			}
		}

		for _, credit := range creditPullBack {
			if report.CreatedDate == credit.CreatedDate.Format("2006-01-02") {
				reportList[i].TotalCreditBackPrice = credit.TotalCreditBackPrice
				reportList[i].TotalCreditBackCount = credit.TotalCreditBackCount
			}
		}

		for _, playlog := range playlogReport {
			if report.CreatedDate == playlog.CreatedDate.Format("2006-01-02") {
				reportList[i].TotalTurn = playlog.TotalTurn
				reportList[i].TotalWinlose = helper.Float64ToDecimal(playlog.TotalWinLose)
				reportList[i].TotalProfitPrice = playlog.TotalWinLose * -1
				reportList[i].TotalProfitPrice = helper.Float64ToDecimal(reportList[i].TotalProfitPrice)

				// P.Mink บอกให้ เอา ลบ ค่าคอมมิชชั่น ออกจาก เพราะ web solo งง
				// for _, incomeLog := range incomeReport {
				// totalCommissionPrice := incomeLog.TotalAffiliatePrice + incomeLog.TotalAlliancePrice
				// reportList[i].TotalProfitPrice = helper.Float64ToDecimal(reportList[i].TotalProfitPrice - totalCommissionPrice)
				// }
			}
		}

		for _, act := range activity {
			if report.CreatedDate == act.CreatedDate.Format("2006-01-02") {
				reportList[i].TotalPromotionWebCredit = act.TotalPromotionWebCredit
				reportList[i].TotalPromotionReturnLoss = act.TotalPromotionReturnLoss
				reportList[i].TotalActivityLuckyWheel = act.TotalActivityLuckyWheel
				reportList[i].TotalCheckInBonus = act.TotalCheckInBonus
				reportList[i].TotalPromotionCashCoupon = act.TotalPromotionCashCoupon
				reportList[i].TotalAdminCreateBonus = act.TotalAdminCreateBonus
				reportList[i].TotalPromotionReturnTurn = act.TotalPromotionReturnTurn
				// SEVEN_USER_BONUS_INCOME
				reportList[i].TotalBonusPrice += act.TotalPromotionWebCredit + act.TotalPromotionReturnLoss + act.TotalActivityLuckyWheel + act.TotalCheckInBonus + act.TotalPromotionCashCoupon + act.TotalAdminCreateBonus + act.TotalPromotionReturnTurn
				reportList[i].TotalBonusCount += act.TotalBonusCount
			}
		}
	}

	getReportSummaryList, err := s.repo.GetReportSummaryList(req)
	if err != nil {
		return nil, internalServerError(err)
	}

	var createBody []model.CreateReportSummary
	for _, report := range reportList {
		found := false
		for _, getReport := range getReportSummaryList {
			if report.CreatedDate == getReport.CreatedDate {
				found = true
				// if report.TotalNewUserCount != getReport.TotalNewUserCount || report.TotalActiveUserCount != getReport.TotalActiveUserCount || report.TotalFirstDepositPrice != getReport.TotalFirstDepositPrice || report.TotalFirstDepositUserCount != getReport.TotalFirstDepositUserCount || report.TotalDepositPrice != getReport.TotalDepositPrice || report.TotalDepositUserCount != getReport.TotalDepositUserCount || report.TotalWithdrawPrice != getReport.TotalWithdrawPrice || report.TotalWithdrawUserCount != getReport.TotalWithdrawUserCount || report.TotalAffiliatePrice != getReport.TotalAffiliatePrice || report.TotalAlliancePrice != getReport.TotalAlliancePrice || report.TotalReturnLossTakenPrice != getReport.TotalReturnLossTakenPrice || report.TotalCreditBackPrice != getReport.TotalCreditBackPrice || report.TotalTurn != getReport.TotalTurn || report.TotalWinlose != getReport.TotalWinlose || report.TotalProfitPrice != getReport.TotalProfitPrice || report.TotalBonusPrice != getReport.TotalBonusPrice || report.TotalReturnTurnTakenPrice != getReport.TotalReturnTurnTakenPrice || report.TotalPromotionReturnTurn != getReport.TotalPromotionReturnTurn || report.TotalBankProfit != getReport.TotalBankProfit {
				// update report
				updateBody := model.UpdateReportSummary{
					CreatedDate:                report.CreatedDate,
					TotalNewUserCount:          &report.TotalNewUserCount,
					TotalActiveUserCount:       &report.TotalActiveUserCount,
					TotalFirstDepositPrice:     &report.TotalFirstDepositPrice,
					TotalFirstDepositUserCount: &report.TotalFirstDepositUserCount,
					TotalDepositUserCount:      &report.TotalDepositUserCount,
					TotalWithdrawUserCount:     &report.TotalWithdrawUserCount,
					TotalBonusPrice:            &report.TotalBonusPrice,
					TotalBonusCount:            &report.TotalBonusCount,
					TotalProfitPrice:           &report.TotalProfitPrice,
					TotalDepositPrice:          &report.TotalDepositPrice,
					TotalWithdrawPrice:         &report.TotalWithdrawPrice,
					TotalAffiliatePrice:        &report.TotalAffiliatePrice,
					TotalAlliancePrice:         &report.TotalAlliancePrice,
					TotalReturnLossTakenPrice:  &report.TotalReturnLossTakenPrice,
					TotalReturnTurnTakenPrice:  &report.TotalReturnTurnTakenPrice,
					TotalCreditBackPrice:       &report.TotalCreditBackPrice,
					TotalCreditBackCount:       &report.TotalCreditBackCount,
					TotalTurn:                  &report.TotalTurn,
					TotalWinlose:               &report.TotalWinlose,
					TotalBankProfit:            &report.TotalBankProfit,
					TotalPromotionWebCredit:    &report.TotalPromotionWebCredit,
					TotalPromotionReturnLoss:   &report.TotalPromotionReturnLoss,
					TotalActivityLuckyWheel:    &report.TotalActivityLuckyWheel,
					TotalCheckInBonus:          &report.TotalCheckInBonus,
					TotalPromotionCashCoupon:   &report.TotalPromotionCashCoupon,
					TotalAdminCreateBonus:      &report.TotalAdminCreateBonus,
					TotalPromotionReturnTurn:   &report.TotalPromotionReturnTurn,
					UpdatedAt:                  time.Now().UTC(),
				}
				if err := s.repo.UpdateReportByCronjob(updateBody); err != nil {
					return nil, internalServerError(err)
				}
				// }
			}
		}
		if !found {
			// create report
			createBody = append(createBody, report)
		}
	}
	if len(createBody) > 0 {
		if err := s.repo.CreateReportByCronjob(createBody); err != nil {
			return nil, internalServerError(err)
		}
	}

	return reportList, nil
}

func (s *accountingReportService) GetUserCreditSummaryTotal() (*model.TotalSumUserActiveCredit, error) {

	creditReport, err := s.repo.GetUserCreditSummaryTotal()
	if err != nil {
		var creditReport model.TotalSumUserActiveCredit
		creditReport.TotalUserActiveCredit = 0

		return &creditReport, nil
	}

	return creditReport, nil

}
