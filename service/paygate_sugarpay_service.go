package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"os"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm"
)

type SugarpayService interface {
	// Sugarpay
	// CreateSugarpayWebhook(req model.SugarpayWebhookRequest) (*int64, error)
	GetSugarpayWebDepositAccount() (*model.SugarpayCustomerDepositInfo, error)
	CreateSugarpayDeposit(req model.SugarpayDepositCreateRequest) (*model.SugarpayOrderWebResponse, error)
	CreateSugarpayWithdraw(req model.SugarpayWithdrawCreateRequest) (*int64, error)
	// SugarpayCheckBalance() (*model.SugarpayCheckBalanceRemoteResponse, error)
	CancelWithdrawFromSugarpay(transId int64, adminId int64) error
	CreateSugarpayDepositWebhook(req model.SugarpayWebhookRequest) (*int64, error)
	CreateSugarpayWithdrawWebhook(req model.SugarpayWebhookRequest) (*int64, error)
	// SysLog
	CreateSystemLog(name string, req interface{}) error
}

type paygateSugarpayService struct {
	sharedDb                  *gorm.DB
	repo                      repository.SugarpayRepository
	activityLuckyWheelService ActivityLuckyWheelService
	promotionWebService       PromotionWebService
	notiService               NotificationService
}

func NewSugarpayService(
	sharedDb *gorm.DB,
	repo repository.SugarpayRepository,
	activityLuckyWheelService ActivityLuckyWheelService,
	promotionWebService PromotionWebService,
	notiService NotificationService,
) SugarpayService {
	return &paygateSugarpayService{sharedDb, repo, activityLuckyWheelService, promotionWebService, notiService}
}

func (s paygateSugarpayService) ConfirmDepositTransaction(id int64, req model.BankConfirmDepositRequest) error {

	record, err := s.repo.GetBankTransactionById(id)
	if err != nil {
		return internalServerError(err)
	}
	if record.TransactionStatusId != model.TRANS_STATUS_PENDING {
		return badRequest("Transaction is not pending")
	}
	if record.TransactionTypeId != model.TRANSACTION_TYPE_DEPOSIT && record.TransactionTypeId != model.TRANSACTION_TYPE_BONUS {
		return badRequest("Transaction is not deposit")
	}
	jsonBefore, _ := json.Marshal(record)

	var updateData model.BankDepositTransactionConfirmBody
	updateData.TransactionStatusId = model.TRANS_STATUS_DEPOSIT_PENDING_CREDIT
	updateData.ConfirmedAt = req.ConfirmedAt
	updateData.ConfirmedByAdminId = req.ConfirmedByAdminId

	// if record.IsAutoCredit && record.TransferAt != nil {
	// 	seconds := time.Now().UTC().Sub(record.TransferAt.UTC()).Seconds()
	// 	autoProcessTimer := fmt.Sprintf("%.2f", seconds)
	// 	updateData.AutoProcessTimer = &autoProcessTimer
	// }

	var createBody model.CreateBankTransactionActionBody
	createBody.ActionKey = fmt.Sprintf("DCF_STATE#%d", record.Id)
	createBody.TransactionId = record.Id
	createBody.UserId = record.UserId
	createBody.TransactionTypeId = record.TransactionTypeId
	createBody.FromAccountId = record.FromAccountId
	createBody.ToAccountId = record.ToAccountId
	createBody.JsonBefore = string(jsonBefore)
	if req.TransferAt == nil {
		createBody.TransferAt = record.TransferAt
	} else {
		TransferAt := req.TransferAt
		createBody.TransferAt = TransferAt
		updateData.TransferAt = *TransferAt
	}
	if req.SlipUrl != nil {
		createBody.SlipUrl = *req.SlipUrl
	}
	createBody.CreditAmount = record.CreditAmount
	if req.BonusAmount != nil {
		createBody.BonusAmount = *req.BonusAmount
	}
	createBody.ConfirmedAt = req.ConfirmedAt
	createBody.ConfirmedByAdminId = req.ConfirmedByAdminId
	if actionId, err := s.repo.CreateTransactionAction(createBody); err == nil {
		// do nothing ?
		if err := s.repo.ConfirmPendingDepositTransaction(id, updateData); err != nil {
			if err := s.repo.RollbackTransactionAction(*actionId); err == nil {
				return internalServerError(err)
			}
			return internalServerError(err)
		}
	} else {
		return internalServerError(err)
	}
	return nil
}

func (s paygateSugarpayService) GetSugarpayWebDepositAccount() (*model.SugarpayCustomerDepositInfo, error) {

	var result model.SugarpayCustomerDepositInfo

	pgAccount, err := s.GetSugarpayAccount()
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}
	if !pgAccount.IsDepositEnabled {
		return nil, badRequest("PAYGATE_DEPOSIT_NOT_ENABLED")
	}

	result.Name = pgAccount.Name
	if pgAccount.DisplayName == "" {
		result.Name = "เติมเงินผ่าน QR Code"
	} else {
		result.DisplayName = pgAccount.DisplayName
	}
	result.ShopName = pgAccount.ShopName
	result.MinAmount = model.SUGARPAY_DEFMIN_DEPOSIT_AMOUNT
	result.MaxAmount = model.SUGARPAY_DEFMAX_DEPOSIT_AMOUNT

	return &result, nil
}

func (s paygateSugarpayService) GetSugarpayAccount() (*model.PaygateAccountResponse, error) {

	return GetSugarpayAccount(s.repo)
}

func GetSugarpayAccount(repo repository.SugarpayRepository) (*model.PaygateAccountResponse, error) {

	pgAccount, err := repo.GetPaygateAccountByProviderId(model.PAYGATE_MERCHANT_ID_SUGARPAY)
	if err != nil {
		return nil, internalServerError(err)
	}
	return pgAccount, nil
}

func GetSugarpayCustomerBank(bankCode string) (string, error) {

	remoteBankCode := ""
	uBankCode := strings.ToUpper(strings.TrimSpace(bankCode))

	// Bank Code
	// "bankCode": "002" , "bankName" : "BBL"
	// "bankCode": "004" , "bankName" : "KBANK"
	// "bankCode": "006" , "bankName" : "KTB"
	// "bankCode": "011" , "bankName" : "TTB"
	// "bankCode": "014" , "bankName" : "SCB"
	// "bankCode": "017" , "bankName" : "CITI"
	// "bankCode": "018" , "bankName" : "SMBC"
	// "bankCode": "020" , "bankName" : "SCBT"
	// "bankCode": "022" , "bankName" : "CIMB"
	// "bankCode": "024" , "bankName" : "UOB"
	// "bankCode": "025" , "bankName" : "BAY"
	// "bankCode": "030" , "bankName" : "GSB"
	// "bankCode": "031" , "bankName" : "HSBC"
	// "bankCode": "032" , "bankName" : "DB"
	// "bankCode": "033" , "bankName" : "GHB"
	// "bankCode": "034" , "bankName" : "BAAC"
	// "bankCode": "039" , "bankName" : "MHCB"
	// "bankCode": "066" , "bankName" : "ISBT"
	// "bankCode": "067" , "bankName" : "TISCO"
	// "bankCode": "069" , "bankName" : "KKP"
	// "bankCode": "070" , "bankName" : "ICBC"
	// "bankCode": "071" , "bankName" : "TCRB"
	// "bankCode": "073" , "bankName" : "LHBANK"
	// "bankCode": "801" , "bankName" : "TRUE"
	switch uBankCode {
	case "002", "BBL":
		remoteBankCode = "BBL" // "3" "กรุงเทพ" "bbl"
	case "004", "KBANK":
		remoteBankCode = "KBANK" // "1" "กสิกรไทย" "kbank"
	case "006", "KTB":
		remoteBankCode = "KTB" // "5"	"กรุงไทย"	"ktb"
	case "011", "TMB":
		remoteBankCode = "TMB"
	case "014", "SCB":
		remoteBankCode = "SCB" // "2" "ไทยพาณิชย์" "scb"
	case "020", "SCBT":
		remoteBankCode = "SCBT" // "19"	"สแตนดาร์ดชาร์เตอร์ด"	"scbt"
	case "022", "CIMB":
		remoteBankCode = "CIMB" // "13"	"ซีไอเอ็มบี"	"cimb"
	case "024", "UOB":
		remoteBankCode = "UOB" // "11"	"ยูโอบี"	"uob"
	case "025", "BAY":
		remoteBankCode = "BAY" // "4"	"กรุงศรี"	"bay"
	case "030", "GSB":
		remoteBankCode = "GSB" // "7"	"ออมสิน"	"gsb"
	case "031", "HSBC":
		remoteBankCode = "HSBC" // "14"	"เอชเอสบีซี"	"hsbc"
	case "033", "GHB":
		remoteBankCode = "GHB" // "10"	"อาคารสงเคราะห์"	"ghb"
	case "034", "BAAC":
		remoteBankCode = "BAAC" // "8"	"ธกส"	"baac"
	case "039", "MHCB":
		remoteBankCode = "MHCB" // ไม่มีในระบบ
	case "066", "ISBT":
		remoteBankCode = "ISBT" // "16"	"ธนาคารอิสลาม"	"isbt"
	case "067", "TISCO":
		remoteBankCode = "TISCO" // "17"	"ทิสโก้"	"tisco"
	case "069", "KKP":
		remoteBankCode = "KKP" // "9"	"เกียรตินาคิน"	"kkp"
	case "070", "ICBC":
		remoteBankCode = "ICBC" // "15"	"ไอซีบีซี"	"icbc"
	case "071", "TCRB":
		remoteBankCode = "TCRB" // ไทยเครดิต
	case "073", "LHBANK", "LH":
		remoteBankCode = "LHBANK" //"12"	"แลนด์ แอนด์ เฮ้าส์"	"lh"
	case "074", "TTB":
		remoteBankCode = "TTB" // "6"	"ทีเอ็มบีธนชาต"	"ttb"
	default:
		// "18"	"ซิตี้แบงก์"	"citi"
		// และอื่นๆ หลังจากนี้ทั้งหมด
		return "", errors.New("USER_BANK_NOT_SUPPORTED")
	}

	return remoteBankCode, nil
}

func (s paygateSugarpayService) CreateSugarpayDeposit(req model.SugarpayDepositCreateRequest) (*model.SugarpayOrderWebResponse, error) {

	var result model.SugarpayOrderWebResponse

	// Ruled by Provider
	if req.Amount < model.SUGARPAY_DEFMIN_DEPOSIT_AMOUNT || req.Amount > model.SUGARPAY_DEFMAX_DEPOSIT_AMOUNT {
		log.Println("req.Amount", req.Amount)
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}

	pgAccount, err := s.GetSugarpayAccount()
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}
	if !pgAccount.IsDepositEnabled {
		return nil, badRequest("PAYGATE_DEPOSIT_NOT_ENABLED")
	}
	// PrerequisitesSugarpay
	if pgAccount.ApiEndPoint == "" || pgAccount.AccessKey == "" || pgAccount.SecretKey == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}

	// [********] get previse deposit order in last 5 minutes
	if pOrder, err := s.repo.CheckSugarpayDepositOrderInLast5Minutes(req.UserId); err != nil {
		if err != gorm.ErrRecordNotFound {
			return nil, internalServerError(err)
		}
	} else if pOrder != nil && pOrder.Amount == req.Amount {
		actionAtUtc := time.Now().UTC()
		if pOrder.CreatedAt.Add(time.Minute * 5).After(actionAtUtc) {
			result.UserId = pOrder.UserId
			result.OrderNo = pOrder.OrderNo
			result.Amount = pOrder.Amount
			result.TransferAmount = pOrder.TransferAmount
			result.TransactionStatus = *pOrder.TransactionStatus
			// result.QrCode = pOrder.QrPromptpay
			result.QrUrl = pOrder.QrPromptpay
			// result.PaymentPageUrl = pOrder.PaymentPageUrl
			result.CreatedAt = pOrder.CreatedAt

			// imgData, err := qrcode.Encode(pOrder.QrPromptpay, qrcode.Medium, 256)
			// if err != nil {
			// 	// return nil, fmt.Errorf("unable to encode png: %w", err)
			// 	return &result, nil
			// }
			// // encode to base64
			// img, err := png.Decode(bytes.NewReader(imgData))
			// if err != nil {
			// 	// return nil, fmt.Errorf("unable to decode jpeg: %w", err)
			// 	return &result, nil
			// }
			// var buf bytes.Buffer
			// if err := png.Encode(&buf, img); err != nil {
			// 	// return nil, fmt.Errorf("unable to encode png: %w", err)
			// 	return &result, nil
			// }
			// result.QrBase64 = base64.StdEncoding.EncodeToString(buf.Bytes())
			return &result, nil
		}
	}

	webhookDomain := os.Getenv("CYBERGAME_WEBHOOK_ENDPOINT")
	if webhookDomain == "" {
		return nil, errors.New("EMPTY_ENV_CYBERGAME_WEBHOOK_ENDPOINT")
	}

	user, err := s.repo.GetUserBankDetailById(req.UserId)
	if err != nil {
		return nil, badRequest("INVALID_USER")
	}
	if user == nil || user.BankAccount == "" || user.BankCode == "" {
		return nil, badRequest("INVALID_USER_ACCOUNT")
	}
	remoteBankCode, err := GetSugarpayCustomerBank(user.BankCode)
	if err != nil || remoteBankCode == "" {
		return nil, badRequest("USER_BANK_NOT_SUPPORTED")
	}

	accountingRepo := repository.NewAccountingRepository(s.repo.GetDb())
	configWeb, err := s.repo.GetWebConfiguration()
	if err != nil {
		log.Println("CreateSugarpayDeposit.GetWebConfiguration", err)
		return nil, badRequest("WEB_CONFIG_NOT_FOUND")
	}
	if user.Id != 0 && configWeb != nil {
		// [********] ฝากครั้งแรกขั้นต่ำ ปรับ flow เช็ค user ให้เช็คจากรายการฝากครั้งแรกจริงๆ ไม่เช็คจากรหัสที่ได้รับ
		if err := CheckFirstMinimunDeposit(accountingRepo, user.Id, req.Amount, *configWeb); err != nil {
			return nil, badRequest(fmt.Sprintf("ฝากครั้งแรกขั้นต่ำ %d บาท", configWeb.MinFirstMemberDeposit))
		}
	} else {
		log.Println("CreateSugarpayDeposit.CheckFirstMinimunDeposit", "CANT_CHECK_FIRST_MINIMUM_DEPOSIT_ON_EMPTY_CONFIG")
	}

	// ===========================================================================================
	var createBody model.SugarpayOrderCreateBody
	createBody.UserId = req.UserId
	createBody.OrderTypeId = model.SUGARPAY_ORDER_TYPE_DEPOSIT
	createBody.OrderNo = "" // Autogen
	createBody.Amount = req.Amount
	createBody.TransactionStatus = "PENDING"
	insertId, err := s.repo.CreateDbSugarpayOrder(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	// reget Order
	pendingOrder, err := s.repo.GetDbSugarpayOrderById(*insertId)
	if err != nil {
		// SET AS ERROR
		remark := "Error GetDbSugarpayOrderById, " + err.Error()
		if err := s.repo.UpdateDbSugarpayOrderError(*insertId, remark); err != nil {
			log.Println("CreateSugarpayDeposit.UpdateDbSugarpayOrderError", err)
		}
		return nil, internalServerError(err)
	}
	// Create SUGARPAY Order
	var remoteRequest model.SugarpayDepositCreateRemoteRequest
	remoteRequest.OrderId = pendingOrder.OrderNo
	remoteRequest.Amount = req.Amount
	remoteRequest.RefName = user.Fullname
	remoteRequest.RefAccountNo = user.BankAccount
	remoteRequest.RefBankCode = remoteBankCode
	remoteRequest.UserId = user.MemberCode
	remoteRequest.CallbackUrl = fmt.Sprintf("%s/sugarpay/dep-callback", webhookDomain)
	remoteResp, err := s.repo.SugarpayDeposit(*pgAccount, remoteRequest)
	if err != nil {
		// SET AS ERROR
		remark := "Error SugarpayDeposit, " + err.Error()
		if err := s.repo.UpdateDbSugarpayOrderError(*insertId, remark); err != nil {
			log.Println("SugarpayDeposit.UpdateDbSugarpayOrderError", err)
		}
		// SysLog At REPO
		return nil, internalServerError(err)
	}

	fmt.Println("SugarpayDeposit.remoteResp", helper.StructJson(remoteResp))

	if remoteResp.Error != "0" && remoteResp.Code != "200" {
		// SET AS ERROR
		remark := remoteResp.Message
		if len(remark) == 0 {
			remark = "UNKNOW_ERROR from depositWithSugarpay"
		}
		if err := s.repo.UpdateDbSugarpayOrderError(*insertId, remark); err != nil {
			log.Println("SugarpayDeposit.UpdateDbSugarpayOrderError", err)
		}
		// SysLog
		if _, err := s.repo.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "CreateSugarpayDeposit.SugarpayDeposit",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(remoteRequest),
			JsonResponse: helper.StructJson(remoteResp),
		}); err != nil {
			log.Println("CreateSugarpayDeposit.CreateSysLog", err)
		}
		return nil, internalServerError(fmt.Errorf(remark))
	}

	// Parse Float Amount
	transferAmount, err := strconv.ParseFloat(remoteResp.Result.Amount, 64)
	if err != nil {
		// SET AS ERROR
		remark := "Error ParseFloat Amount, " + err.Error()
		if err := s.repo.UpdateDbSugarpayOrderError(*insertId, remark); err != nil {
			log.Println("CreateSugarpayDeposit.UpdateDbSugarpayOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// onCreate Success
	var updateBody model.SugarpayOrderUpdateBody
	updateBody.TransactionNo = remoteResp.Result.RefId
	updateBody.TransactionDate = time.Now().UTC()
	updateBody.TransactionStatus = "WAIT_PAYMENT"
	updateBody.TransferAmount = transferAmount
	updateBody.QrPromptpay = remoteResp.Result.Image
	// updateBody.PaymentPageUrl = remoteResp.Data.Redirect
	if err := s.repo.UpdateDbSugarpayOrder(*insertId, updateBody); err != nil {
		// SET AS ERROR
		remark := "Error UpdateDbSugarpayOrder, " + err.Error()
		if err := s.repo.UpdateDbSugarpayOrderError(*insertId, remark); err != nil {
			log.Println("CreateSugarpayDeposit.UpdateDbSugarpayOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	waitPayOrder, err := s.repo.GetDbSugarpayOrderById(*insertId)
	if err != nil {
		return nil, internalServerError(err)
	}

	result.UserId = waitPayOrder.UserId
	result.OrderNo = waitPayOrder.OrderNo
	result.Amount = waitPayOrder.Amount
	result.TransferAmount = transferAmount
	result.TransactionStatus = *waitPayOrder.TransactionStatus
	// result.QrCode = waitPayOrder.QrPromptpay
	result.QrUrl = waitPayOrder.QrPromptpay
	// result.PaymentPageUrl = waitPayOrder.PaymentPageUrl
	result.CreatedAt = waitPayOrder.CreatedAt

	// imgData, err := qrcode.Encode(waitPayOrder.QrPromptpay, qrcode.Medium, 256)
	// if err != nil {
	// 	// return nil, fmt.Errorf("unable to encode png: %w", err)
	// 	return &result, nil
	// }
	// // encode to base64
	// img, err := png.Decode(bytes.NewReader(imgData))
	// if err != nil {
	// 	// return nil, fmt.Errorf("unable to decode jpeg: %w", err)
	// 	return &result, nil
	// }
	// var buf bytes.Buffer
	// if err := png.Encode(&buf, img); err != nil {
	// 	// return nil, fmt.Errorf("unable to encode png: %w", err)
	// 	return &result, nil
	// }
	// result.QrBase64 = base64.StdEncoding.EncodeToString(buf.Bytes())

	return &result, nil
}

func (s paygateSugarpayService) CreateSugarpayWithdraw(req model.SugarpayWithdrawCreateRequest) (*int64, error) {

	// Ruled by Provider
	if req.Amount < model.SUGARPAY_DEFMIN_WITHDRAW_AMOUNT || req.Amount > model.SUGARPAY_DEFMAX_WITHDRAW_AMOUNT {
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}

	pgAccount, err := s.GetSugarpayAccount()
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}
	if !pgAccount.IsWithdrawEnabled {
		return nil, badRequest("PAYGATE_WITHDRAW_NOT_ENABLED")
	}
	// PrerequisitesSugarpay
	if pgAccount.ApiEndPoint == "" || pgAccount.AccessKey == "" || pgAccount.SecretKey == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}
	//  || pgAccount.LoanAppId == "" No Api Required

	user, err := s.repo.GetUserBankDetailById(req.UserId)
	if err != nil {
		return nil, badRequest("INVALID_USER")
	}
	if user == nil || user.BankAccount == "" || user.BankCode == "" {
		return nil, badRequest("INVALID_USER_ACCOUNT")
	}

	webhookDomain := os.Getenv("CYBERGAME_WEBHOOK_ENDPOINT")
	if webhookDomain == "" {
		return nil, errors.New("EMPTY_ENV_CYBERGAME_WEBHOOK_ENDPOINT")
	}

	withdrawBankCode, err := GetSugarpayCustomerBank(user.BankCode)
	if err != nil || withdrawBankCode == "" {
		return nil, badRequest("USER_BANK_NOT_SUPPORTED")
	}

	// ===========================================================================================
	// CREATE Order
	var createBody model.SugarpayOrderCreateBody
	createBody.RefId = &req.RefId
	createBody.UserId = req.UserId
	createBody.OrderTypeId = model.SUGARPAY_ORDER_TYPE_WITHDRAW
	createBody.OrderNo = "" // Autogen
	createBody.Amount = req.Amount
	createBody.TransactionStatus = "PENDING"
	insertId, err := s.repo.CreateDbSugarpayOrder(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	// reget Order
	pendingOrder, err := s.repo.GetDbSugarpayOrderById(*insertId)
	if err != nil {
		// SET AS ERROR
		remark := "Error GetDbSugarpayOrderById, " + err.Error()
		if err := s.repo.UpdateDbSugarpayOrderError(*insertId, remark); err != nil {
			log.Println("CreateSugarpayWithdraw.UpdateDbSugarpayOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// Create SUGARPAY Order
	var remoteRequest model.SugarpayWithdrawCreateRemoteRequest
	remoteRequest.OrderId = pendingOrder.OrderNo
	remoteRequest.Amount = pendingOrder.Amount
	remoteRequest.ToAccountNo = user.BankAccount
	remoteRequest.ToBankCode = withdrawBankCode
	remoteRequest.ToName = user.Fullname
	remoteRequest.CallbackUrl = fmt.Sprintf("%s/sugarpay/wid-callback", webhookDomain)
	remoteResp, err := s.repo.SugarpayWithdraw(*pgAccount, remoteRequest)
	if err != nil {
		// SET AS ERROR
		remark := "Error SugarpayWithdraw, " + err.Error()
		if err := s.repo.UpdateDbSugarpayOrderError(*insertId, remark); err != nil {
			log.Println("SugarpayWithdraw.UpdateDbSugarpayOrderError", err)
		}
		// SysLog At REPO
		return nil, internalServerError(err)
	}

	// fmt.Println("CreateSugarpayWithdraw.remoteResp", helper.StructJson(remoteResp))

	if remoteResp.Result.RefId == "" || remoteResp.Result.OrderId == "" {
		// SET AS ERROR
		remark := remoteResp.Message
		if len(remark) == 0 {
			remark = "UNKNOW_ERROR from withdrawWithSugarpay"
		}
		if err := s.repo.UpdateDbSugarpayOrderError(*insertId, remark); err != nil {
			log.Println("SugarpayWithdraw.UpdateDbSugarpayOrderError", err)
		}
		// SysLog
		if _, err := s.repo.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "CreateSugarpayWithdraw.SugarpayWithdraw",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(remoteRequest),
			JsonResponse: helper.StructJson(remoteResp),
		}); err != nil {
			log.Println("CreateSugarpayWithdraw.CreateSysLog", err)
		}
		return nil, internalServerError(fmt.Errorf(remark))
	}

	// onCreate Success
	var updateBody model.SugarpayOrderUpdateBody
	updateBody.TransactionNo = remoteResp.Result.RefId
	updateBody.TransactionDate = time.Now().UTC()
	updateBody.TransactionStatus = "WAIT_PAYMENT"
	updateBody.QrPromptpay = remoteResp.Message
	if err := s.repo.UpdateDbSugarpayOrder(*insertId, updateBody); err != nil {
		// SET AS ERROR
		remark := "Error UpdateDbSugarpayOrder, " + err.Error()
		if err := s.repo.UpdateDbSugarpayOrderError(*insertId, remark); err != nil {
			log.Println("CreateSugarpayWithdraw.UpdateDbSugarpayOrderError", err)
		}
		return nil, internalServerError(err)
	}
	return insertId, nil
}

func CreateCustomerDepositFromSugarpayOrder(repo repository.SugarpayRepository, orderId int64, adminId int64) (*int64, error) {

	// ** ทุกรายการจะต้องเช็คสถานะใหม่ทั้งหมด เพราะ admin แย่งกดอนุมัติได้
	item, err := repo.GetRawSugarpayPendingDepositOrderById(orderId)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, badRequest("ORDER_NOT_FOUND")
		}
		return nil, internalServerError(err)
	}

	// Check BankTransactionStatus = 'PENDING'
	if item.BankTransactionStatus != "PENDING" {
		return nil, badRequest("ORDER_NOT_PENDING")
	}

	return createCustomerDepositFromSugarpay(repo, *item, adminId)
}

func createCustomerDepositFromSugarpay(repo repository.SugarpayRepository, item model.SugarpayOrderResponse, adminId int64) (*int64, error) {

	var externalNoti model.NotifyExternalNotificationRequest
	actionAt := time.Now().UTC()
	// confirmByUserId := int64(0)

	depositAmount := item.Amount

	user, err := repo.GetUserBankDetailById(item.UserId)
	if err != nil {
		log.Println("createCustomerDepositFromSugarpay.GetUserBankDetailByMemberCode", err)
		return nil, internalServerError(err)
	}

	var promotionWebUserId int64
	if getPromotionWebUser, err := repo.GetDepositCurrentProcessingUserPromotion(item.UserId); err != nil {
		log.Println("createCustomerDepositFromSugarpay.GetDepositCurrentProcessingUserPromotion", err)
	} else if getPromotionWebUser != nil {
		promotionWebUserId = getPromotionWebUser.Id
	}

	// MemberCode
	if user.MemberCode == "" {
		memberCode, err := GenUniqueUserMemberCode(repository.NewUserRepository(repo.GetDb()), user.Id)
		if err != nil {
			log.Println("createCustomerDepositFromSugarpay.GenUniqueUserMemberCode", err)
			return nil, internalServerError(err)
		}
		user.MemberCode = *memberCode
	}

	// layer — 2024-04-02 at 10:06 AM
	// ถ้าแก้ตามนี้แล้วน่าจะไม่เจอ ถ้าเคสกันซ้ำเราครอบคลุม
	// @Dunk @TULA
	// กันซ้ำคือ heng ไปซ้ำกับแอดมินเติมมือ
	// ----
	// เงื่อนไขกันซ้ำ
	// 1 หารายการฝากสำเร็จล่าสุดของยูสนั้น
	// 2 ถ้ามีรายการล่าสุดที่เป็นเติมมือ
	// -เช็คเวลาเติม ต้องมากกว่าเวลาโอนในสลิป
	// -ยอดเงินเท่ากัน
	// **ถ้าเข้าเงื่อนไขทั้งหมด คือแสดงว่า webhook เข้าทีหลังแอดมินเติมมือ
	// ต้อง bypass ไม่ให้เติมอีก และอัพเดท status ว่าจับคู่ได่แล้ว
	// ---
	// วิธีนี้น่าจะใช้ได้กับทุก webhook (heng, fastbank) ที่มีเวลาโอนสลิปมาให้
	duplicateRemark := "INIT"
	hasDuplicateDeposit := false
	manualDeposit, err := repo.GetLastestBankAdminManualDepositTransaction(user.Id)
	if err != nil {
		if err.Error() != recordNotFound {
			log.Println("createCustomerDepositFromSugarpay.GetLastestBankAdminManualDepositTransaction", err)
			return nil, internalServerError(err)
		}
		// NOT_FOUND = ถ้าไม่เจอ ให้ทำต่อ
		duplicateRemark = "OK_NO_MANUAL_DEPOSIT"
	}
	if manualDeposit != nil && item.TransactionDate != nil {
		if manualDeposit.TransferAt.After(*item.TransactionDate) {
			// ถ้าเวลาเติมมือมากกว่าเวลาโอนในสลิป
			if manualDeposit.CreditAmount == depositAmount {
				// ถ้ายอดเงินเท่ากัน
				duplicateRemark = "STOP_MANUAL_DEPOSIT_AFTER_AND_SAME_AMOUNT"
				hasDuplicateDeposit = true
			} else {
				duplicateRemark = "OK_MANUAL_DEPOSIT_AMOUNT_NOT_MATCH"
			}
		} else {
			duplicateRemark = "OK_MANUAL_DEPOSIT_BEFORE_WITHDRAW"
		}
	}
	if duplicateRemark != "INIT" {
		// WebhookLog
		var createBody2 model.SugarpayWebhookCreateBody
		createBody2.Name = "SUGARPAY_DEPOSIT_DUPLICATE_CHECKER"
		createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
			"item":                item,
			"method":              "createCustomerDeposit",
			"hasDuplicateDeposit": hasDuplicateDeposit,
			"error":               duplicateRemark,
			"manualDeposit":       manualDeposit,
		})
		if _, err = repo.CreateSugarpayWebhook(createBody2); err != nil {
			log.Println("Error CreateSugarpayWebhook.CreateSugarpayWebhook", err)
		}
		if hasDuplicateDeposit {
			return nil, internalServerError(fmt.Errorf("DUPLICATE_DEPOSIT"))
		}
	}

	pgAccount, err := GetSugarpayAccount(repo)
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}

	// ===================================================
	paygateRepo := repository.NewPaymentGatewayRepository(repo.GetDb())
	accountingRepo := repository.NewAccountingRepository(repo.GetDb())
	luckyWheelRepo := repository.NewActivityLuckyWheelRepository(repo.GetDb())
	promotionWebRepo := repository.NewPromotionWebRepository(repo.GetDb())
	notiRepo := repository.NewNotificationRepository(repo.GetDb())

	// Affiliate + Alliance Income
	member, err := repo.GetMemberById(user.Id)
	if err != nil {
		return nil, internalServerError(err)
	}
	if member.UserTypeName == "NONE" {
		// ถ้าฝากครั้งแรก จะได้เป็น AFFILIATE + REFER COMMISION
		if err := UserFirstDepositCommission(accountingRepo, *member, depositAmount); err != nil {
			log.Println("CreateFristTimeDepositRecord.UserFirstDepositCommission.ERROR", err)
			return nil, internalServerError(err)
		}
	}

	isAutoDeposit := true // Auto ฝาก นับตั้งแต่ได้รับยอดฝาก จนได้ยืนยันเครดิต
	if adminId != 0 {
		isAutoDeposit = false
	}

	// CREATE BANK TRANSACTION
	var createBankTransaction model.BankTransactionCreateBody
	createBankTransaction.MemberCode = user.MemberCode
	createBankTransaction.UserId = user.Id
	// createBankTransaction.StatementId = 0
	createBankTransaction.TransactionTypeId = model.TRANSACTION_TYPE_DEPOSIT
	// createBankTransaction.FromAccountName = "" // empty?
	createBankTransaction.ToAccountName = &pgAccount.TypeName
	createBankTransaction.ToAccountNumber = &pgAccount.ShopName
	createBankTransaction.PromotionId = &promotionWebUserId
	createBankTransaction.CreditAmount = depositAmount
	// createBankTransaction.BonusAmount = 0
	// createBankTransaction.BonusReason = nil
	createBankTransaction.DepositChannel = "SUGARPAY PAYMENT GATEWAY"
	createBankTransaction.TransactionStatusId = model.TRANS_STATUS_PENDING
	createBankTransaction.CreatedByAdminId = 0
	createBankTransaction.IsAutoCredit = isAutoDeposit
	createBankTransaction.TransferAt = &actionAt
	// create transaction
	transId, err := repo.InsertBankTransaction(createBankTransaction)
	if err != nil {
		log.Println("createCustomerDepositFromSugarpay.InsertBankTransaction", err)
		return nil, internalServerError(err)
	}

	// var actionStatement model.BankStatementMatchRequest
	// actionStatement.StatementId = *bankstatementId
	// actionStatement.UserId = user.Id
	// actionStatement.ConfirmedAt = time
	// actionStatement.ConfirmedByAdminId = &req.CreateByUserId
	// if err := s.serviceAccounting.SetStatementOwnerMatched(*bankstatementId, actionStatement, model.USE_ENDING_NOTI); err != nil {
	// 	log.Println("createCustomerDepositFromSugarpay.SetStatementOwnerMatched", err)
	// 	return nil, internalServerError(err)
	// }

	// DECREASE FASTBANK CREDIT 2.FROM create auto DEPOSIT
	// if err := repoAccounting.DecreaseFastbankCredit(1); err != nil {
	// 	log.Println("createCustomerDepositFromSugarpay.DECREASE_FASTBANK_CREDIT_ERROR", err)
	// }

	var confirmDeposit model.BankConfirmDepositRequest
	confirmDeposit.TransferAt = &actionAt
	confirmDeposit.ConfirmedAt = actionAt
	confirmDeposit.ConfirmedByAdminId = &adminId
	if err := confirmDepositTransaction(paygateRepo, *transId, confirmDeposit); err != nil {
		log.Println("createCustomerDepositFromSugarpay.confirmDepositTransaction", err)
		return nil, internalServerError(err)
	}

	// UPDATE HENG ORDER - BANK SUCCESS
	if err := repo.UpdateSugarpayOrderBankSucess(item.Id, *transId, adminId); err != nil {
		log.Println("createCustomerDepositFromSugarpay.UpdatePaygateOrderBankSucess", err)
	}

	isFirstDeposit := repo.IsFirstDeposit(user.Id)
	if isFirstDeposit {
		var bonusReq model.UserFirstDepositCreateRequest
		bonusReq.UserId = user.Id
		bonusReq.TransactionId = transId
		bonusReq.TransferAt = actionAt
		bonusReq.Amount = depositAmount
		bonusReq.Remark = "createCustomerDepositFromSugarpay"
		if err := SetFirstDepositBonus(accountingRepo, isFirstDeposit, bonusReq); err != nil {
			webhookLogMessage := fmt.Sprintf("createCustomerDepositFromSugarpay.userFirstDepositBonus, ERROR: %s", err)
			log.Println("SetFirstDepositBonus", webhookLogMessage)
			// return nil, errors.New(webhookLogMessage)
		}
	}

	// [USER_CREDIT]
	var userCreditReq model.UserTransactionCreateRequest
	userCreditReq.UserId = user.Id
	userCreditReq.RefId = transId
	userCreditReq.TransferAt = &actionAt
	userCreditReq.PromotionId = &promotionWebUserId
	userCreditReq.TypeId = model.CREDIT_TYPE_DEPOSIT
	// userCreditReq.AccountId = &bankAccount.Id
	userCreditReq.Detail = "SUGARPAY PAYMENT GATEWAY"
	userCreditReq.Amount = depositAmount
	userCreditReq.CreateBy = &adminId
	userCreditReq.ConfirmBy = &adminId
	userCreditReq.IsAdjustAuto = isAutoDeposit
	userCreditReq.PaymentMerchatId = &pgAccount.ProviderId
	if agentResp, err := repo.IncreaseUserCredit(userCreditReq); err != nil {
		log.Println("createCustomerDepositFromSugarpay.IncreaseUserCredit", err)
		return nil, internalServerError(err)
	} else {
		// AGENT_SUCCESS
		if err := repo.UpdateDepositTransactionStatusFromAgent(*transId, *agentResp); err != nil {
			log.Println("createCustomerDepositFromSugarpay.UpdateDeporsitTransactionStatusFromAgent", err)
		}
	}

	// [Lucky Wheel] ตอนฝากละได้โบนัส
	var luckyWheelBody model.ActivityLuckyWheelRoundUserRequest
	luckyWheelBody.UserId = user.Id
	luckyWheelBody.ConditionId = model.ACTIVITY_LUCKY_WHEEL_SETTING_CONDITION_DEPOSIT
	luckyWheelBody.ConditionAmount = depositAmount
	if err := CreateRoundActivityLuckyWheel(luckyWheelRepo, luckyWheelBody); err != nil {
		log.Println("createCustomerDepositFromSugarpay.CreateRoundActivityLuckyWheel", err)
	}

	// [TIER]
	if err := repo.IncreaseUserTierDepositAmount(user.Id, depositAmount); err != nil {
		log.Println("MatchStatementOwner.IncreaseUserTierDepositAmount", err)
	}

	var checkUserPromotionBody model.CheckUserPromotionBody
	checkUserPromotionBody.UserId = user.Id
	_, err = CheckUserPromotion(promotionWebRepo, checkUserPromotionBody)
	if err != nil {
		log.Println("createCustomerDepositFromSugarpay.CheckUserPromotion", err)
	}
	// [turnOver] ถ้ามีการฝากเงิน ให้สร้าง TurnOver
	turnoverShare := repository.NewTurnoverRepository(repo.GetDb())
	if err := CreateTurnOverFromSuccessDeposit(turnoverShare, user.Id, depositAmount, *transId); err != nil {
		log.Println("createCustomerDeposit.CreateTurnOverFromSuccessDeposit", err)
	}

	// ===================================================

	// [ notify]
	notiAtUtc := time.Now().UTC()
	externalNoti.TypeNotify = model.IsDepositAfterCredit
	externalNoti.TransId = transId
	externalNoti.Amount = depositAmount
	externalNoti.MemberCode = user.MemberCode
	externalNoti.UserCredit = user.Credit + depositAmount
	externalNoti.ConfirmedByAdminId = 0
	externalNoti.TransferDateTime = notiAtUtc.Add(7 * time.Hour).Format("2006-01-02 15:04:05")
	externalNoti.ActionTime = notiAtUtc.Add(7 * time.Hour).Format("2006-01-02 15:04:05")
	endTime := notiAtUtc
	elapsed := endTime.Sub(actionAt)
	elapsedSeconds := elapsed.Seconds()
	timeElapsed := fmt.Sprintf("%.2f", elapsedSeconds)
	externalNoti.TimerCounter = timeElapsed

	externalNoti.WebScoket.UserID = user.Id
	externalNoti.WebScoket.Amount = depositAmount
	externalNoti.WebScoket.MemberCode = user.MemberCode
	externalNoti.WebScoket.AlertType = "DEPOSIT"
	if err := repo.UpdateAutoProcessTimer(timeElapsed, *transId); err != nil {
		return nil, nil
	}
	if err := ExternalNotification(notiRepo, externalNoti); err != nil {
		log.Println("FailedNotify", err)
	}
	return nil, nil
}

func approveCustomerWithdrawFromSugarpay(repo repository.SugarpayRepository, transId int64) (*int64, error) {

	withdrawTrans, err := repo.GetBankTransactionById(transId)
	if err != nil {
		log.Println("approveCustomerWithdrawFromSugarpay.GetUserBankDetailById", err)
		return nil, internalServerError(err)
	}

	// ============================= ON_SUCCESS =================================
	confirmAtUtc := time.Now().UTC()
	if withdrawTrans.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		// [create transaction action success]
		var createConfirm model.CreateSuccessTransferWithdrawRequest
		createConfirm.TransactionId = withdrawTrans.Id
		createConfirm.ConfirmedAt = confirmAtUtc
		var setIdAuto int64 = 0
		createConfirm.ConfirmedByAdminId = &setIdAuto
		if _, err := CreateSuccessTransferWithdraw(repository.NewBankingRepository(repo.GetDb()), createConfirm); err != nil {
			log.Println("approveCustomerWithdrawFromSugarpay.CreateSuccessTransferWithdraw", err)
			return nil, nil
		}
		// [Show Withdraw USER_TRANSACTION]
		var showUserTrans model.UserTransactionShowUpdate
		showUserTrans.TransactionId = withdrawTrans.Id
		showUserTrans.ConfirmedAt = &createConfirm.ConfirmedAt
		showUserTrans.ConfirmAdminId = createConfirm.ConfirmedByAdminId
		if err := repo.ShowUserTransaction(showUserTrans); err != nil {
			log.Println("approveCustomerWithdrawFromSugarpay.ShowUserTransaction", err)
			return nil, nil
		}
		// [update transaction status]
		secondUsed := "0.0" // timing
		if withdrawTrans.IsAutoCredit && withdrawTrans.TransferAt != nil {
			seconds := confirmAtUtc.Sub(withdrawTrans.TransferAt.UTC()).Seconds()
			secondUsed = fmt.Sprintf("%.2f", seconds)
		}
		if err := repo.UpdateTransactionStatusTransferingToSuccess(withdrawTrans.Id, secondUsed); err != nil {
			log.Println("approveCustomerWithdrawFromSugarpay.UpdateTransactionStatusTransferingToSuccess", err)
			return nil, internalServerError(err)
		}

		// [notify SUCCESS]
		var externalNoti model.NotifyExternalNotificationRequest
		notiRepo := repository.NewNotificationRepository(repo.GetDb())
		externalNoti.TypeNotify = model.IsWithdrawalCreditSuccess
		externalNoti.TransId = &transId
		externalNoti.Amount = withdrawTrans.CreditAmount
		externalNoti.MemberCode = withdrawTrans.MemberCode
		externalNoti.UserCredit = withdrawTrans.CreditAmount
		externalNoti.ConfirmedByAdminId = 0
		externalNoti.TimerCounter = secondUsed
		externalNoti.TransferDateTime = withdrawTrans.TransferAt.Add(7 * time.Hour).Format("2006-01-02 15:04:05")
		externalNoti.ActionTime = confirmAtUtc.Add(7 * time.Hour).Format("2006-01-02 15:04:05")
		if err := ExternalNotification(notiRepo, externalNoti); err != nil {
			log.Println("FailedNotify", err)
		}

	}
	return nil, nil
}

// func rollbackCustomerWithdrawFromSugarpay(repo repository.SugarpayRepository, transId int64) (*int64, error) {

// 	withdrawTrans, err := repo.GetBankTransactionById(transId)
// 	if err != nil {
// 		log.Println("rollbackCustomerWithdrawFromSugarpay.GetBankTransactionById", err)
// 		return nil, internalServerError(err)
// 	}

// 	// ============================= ON_SUCCESS =================================
// 	if withdrawTrans.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_TRASNFERING {
// 		// [update transaction status]
// 		if err := repo.RollbackTransactionStatusTransferingToConfirmed(withdrawTrans.Id); err != nil {
// 			log.Println("approveCustomerWithdrawFromSugarpay.RollbackTransactionStatusTransferingToConfirmed", err)
// 			return nil, internalServerError(err)
// 		}
// 	}
// 	return nil, nil
// }

func (s paygateSugarpayService) CreateSystemLog(name string, req interface{}) error {

	// [SYSLOG] HACKED
	_, err := s.repo.CreateSystemLog(model.SystemLogCreateBody{
		Name:   name,
		Status: "HACKING",
		JsonRequest: helper.StructJson(map[string]interface{}{
			"req": req,
		}),
	})
	if err != nil {
		log.Println(name, "CreateSystemLog.ERROR", err.Error())
	}
	return nil
}

func (s paygateSugarpayService) CancelWithdrawFromSugarpay(transId int64, adminId int64) error {

	withdrawTrans, err := s.repo.GetBankTransactionById(transId)
	if err != nil {
		log.Println("rollbackWithdrawFromSugarpay.GetBankTransactionById", err)
		return internalServerError(err)
	}
	if withdrawTrans.TransactionStatusId != model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		return internalServerError(errors.New("ไม่อยู๋ในสถานะที่สามารถยกเลิกได้"))
	}

	user, err := s.repo.GetUserBankDetailById(withdrawTrans.UserId)
	if err != nil {
		return internalServerError(err)
	}

	paygateOrder, err := s.repo.GetDbSugarpayOrderByRefId(transId)
	if err != nil {
		log.Println("CancelWithdrawFromSugarpay.GetDbSugarpayOrderByRefId", err)
		return internalServerError(err)
	}
	waitStatus := "ERROR"
	if paygateOrder.TransactionStatus == nil || *paygateOrder.TransactionStatus != waitStatus {
		return internalServerError(errors.New("ไม่อยู๋ในสถานะที่สามารถยกเลิกได้"))
	}

	returnAmount := withdrawTrans.CreditAmount

	// race condition deposit
	actionAt := time.Now().UTC()
	var createBody model.RaceActionCreateBody
	createBody.Name = "ADMIN_CANCEL_WITHDRAW"
	createBody.JsonRequest = helper.StructJson(map[string]interface{}{
		"transId":      transId,
		"returnAmount": returnAmount,
	})
	createBody.Status = "PENDING"
	createBody.ActionKey = fmt.Sprintf("WITHDRAW_CANCEL_T%sU%dID%d", actionAt.Format("**********"), user.Id, transId)
	createBody.UnlockAt = actionAt.Add(time.Second * 1)
	if _, err := s.repo.GetRaceActionIdByActionKey(createBody.ActionKey); err != nil {
		log.Println("CancelWithdrawFromSugarpay.GetRaceActionIdByActionKey", err)
		if err != gorm.ErrRecordNotFound {
			return internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
		}
	} else {
		return internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
	}

	// create race condition
	actionId, err := s.repo.CreateRaceCondition(createBody)
	if err != nil {
		log.Println("CancelWithdrawFromSugarpay.ERROR.CreateRaceCondition", err)
		return internalServerError(errors.New("CANNOT_CREATE_ACTION_LUCKY_WHEEL_ROUND"))
	}
	if actionId == 0 {
		return internalServerError(errors.New("CANNOT_CREATE_ACTION_LUCKY_WHEEL_ROUND"))
	}

	// SET ORDER AS ERROR
	remark := "Admin Cancel Withdraw"
	if err := s.repo.UpdateDbSugarpayOrderError(paygateOrder.Id, remark); err != nil {
		log.Println("CreateSugarpayWithdraw.UpdateDbSugarpayOrderError", err)
	}

	if withdrawTrans.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		// [********] check user credit transaction ว่าตรงไหมกับเงื่อนไข (isShow = false)
		userTransaction, err := s.repo.GetUserWithdrawCreditTransactionByRefId(withdrawTrans.Id, returnAmount)
		if err != nil {
			if err.Error() == recordNotFound {
				log.Println("CancelWithdrawCredit.GetUserCreditTransactionById", err)
				return badRequest("ไม่ตรงกับเงือนไข")
			}
			return internalServerError(err)
		}
		if userTransaction != nil {
			var userCreditReq model.UserTransactionCreateRequest
			userCreditReq.UserId = user.Id
			userCreditReq.TransferAt = withdrawTrans.TransferAt
			userCreditReq.RefId = &withdrawTrans.Id
			userCreditReq.TypeId = model.CREDIT_TYPE_DEPOSIT
			userCreditReq.Amount = returnAmount
			IsShow := false
			userCreditReq.IsShow = &IsShow
			userCreditReq.ConfirmBy = &adminId
			if agentResp, err := s.repo.IncreaseUserCredit(userCreditReq); err != nil {
				log.Println("CancelWithdrawCredit.IncreaseUserCredit", err)
				return internalServerError(err)
			} else if !agentResp.AgentSuccess {
				// [********] error from agent will be in failed
				// if err := s.repoAccounting.UpdateDeporsitTransactionStatusFromAgent(*transId, *agentResp); err != nil {
				//     log.Println("CancelWithdrawCredit.UpdateDeporsitTransactionStatusFromAgent", err)
				// }
				// Set FastBank-READ ** ไม่มี เพราะเป็นการสร้างรายการฝากเอง
				agentFail := errors.New("ทำรายการเครดิตไม่สำเร็จ")
				log.Println("CancelWithdrawCredit.IncreaseUserCredit", agentFail)
				return internalServerError(agentFail)
			}
		}
	}

	// remark := "ADMIN_CANCEL_WITHDRAW"
	var updateApprovedBy model.UpdateUserTransactionStatusRequest
	updateApprovedBy.Id = withdrawTrans.Id
	updateApprovedBy.CanceledAt = actionAt
	updateApprovedBy.CanceledByAdminId = adminId
	updateApprovedBy.TransactionStatusId = model.TRANS_STATUS_WITHDRAW_CANCELED
	updateApprovedBy.CancelRemark = &remark
	err = s.repo.UpdateUserTransactionStatus(updateApprovedBy)
	if err != nil {
		return internalServerError(err)
	}

	var createConfirm model.CreateSuccessTransferWithdrawRequest
	createConfirm.TransactionId = withdrawTrans.Id
	createConfirm.ConfirmedAt = actionAt
	createConfirm.ConfirmedByAdminId = &adminId
	if _, err := CreateCanceledTransferWithdraw(repository.NewBankingRepository(s.sharedDb), createConfirm); err != nil {
		return err
	}

	return nil
}

func (s paygateSugarpayService) CreateSugarpayDepositWebhook(req model.SugarpayWebhookRequest) (*int64, error) {

	var createBody model.SugarpayWebhookCreateBody
	createBody.Name = "SUGARPAY_DEPOSIT_CALLBACK"
	createBody.JsonPayload = req.JsonPayload
	insertId, err := s.repo.CreateSugarpayWebhook(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// DEPOSIT
	// {
	// 	"type": "DEPOSIT",
	// 	"status": "SUCCESS",
	// 	"status_code": "DEPOSIT_AUTO",
	// 	"agent_confirm": "CONFIRM",
	// 	"stm_ref_id": "d6c623ab-d62e812c-5597a81f-7bca944a",
	// 	"stm_date": "YYYY-MM-DD hh:mm:ss",
	// 	"stm_amount": "100.50",
	// 	"stm_bank_name": "SCB",
	// 	"stm_account_no": "**********",
	// 	"stm_remark": "TR fr 004-********** โอนเงินเข้าพร้อมเพย์",
	// 	"txn_ref_id": "ca3c3757-ffa8-49db-89df-e314cc5ecf60",
	// 	"txn_order_id": "xxxxxxxx",
	// 	"txn_user_id": "xxxxxxxx",
	// 	"deposit_balance": "100.00",
	// 	"withdraw_balance": "0.00",
	// 	"remark": "",
	// 	"signature": "d95ba17d99c862a44ebb6f1c3039e6b4",
	//  }
	var remoteResp model.SugarpayDepositWebhookResponse
	errJson := json.Unmarshal([]byte(req.JsonPayload), &remoteResp)
	if errJson != nil {
		log.Println("Error unmarshal json", errJson)
		return nil, internalServerError(errJson)
	}

	if remoteResp.TxnRefId == "" || remoteResp.TxnOrderId == "" {
		log.Println("CreateSugarpayDepositWebhook.NoRef", helper.StructJson(remoteResp))
		return nil, badRequest("SUGARPAY_DEPOSIT_HOOK_NOREF")
	}

	// Check Response Status.
	// 	type: "DEPOSIT", "WITHDRAW"
	// status: "SUCCESS", "FAILED"
	// status_code:
	// For Deposit:
	// DEPOSIT_NOT_MATCH : Unable to match the QR code transaction. (จับคู่รายการ QR Code ไม่ได้)
	// DEPOSIT_AUTO : Successfully matched the QR code transaction. (จับคู่รายการ QR Code เรียบร้อย)
	// DEPOSIT_AUTO_CUSTOMER : Successfully matched the transaction based on the customer's account number available in the system. (จับคู่รายการจากเลขบัญชีลูกค้าที่มีในระบบได้)
	// TIMEOUT : The QR code has expired and is no longer valid for use. (QR Code หมดอายุการใช้งาน)
	// CUST_CREATE_NEW_QR : (For status FAILED) This transaction QRCode failed because customer create new qr code.
	successStatus := strings.ToUpper(strings.TrimSpace(remoteResp.Status))
	if successStatus == "SUCCESS" {
		successStatus = "SUCCESS" // Always use SUCCESS
	} else {
		log.Println("CreateSugarpayDepositWebhook.remoteResp.desc.ELSE", successStatus)
		// เก็บตามจริง successStatus = "ERROR"
	}

	// Service Race Condition by Ref1(MchOrderNo) + perStatus
	acAtUtc := time.Now().UTC()
	var rcCreateBody model.RaceActionCreateBody
	rcCreateBody.Name = "CreateSugarpayDepositWebhook"
	rcCreateBody.JsonRequest = helper.StructJson(req)
	rcCreateBody.Status = "PENDING"
	// ActionKey = Ref2-MINUTE
	rcCreateBody.ActionKey = fmt.Sprintf("PG12D%s-%s", acAtUtc.Format("**********"), remoteResp.TxnRefId)
	rcCreateBody.UnlockAt = acAtUtc.Add(time.Second * 1)
	if _, err := s.repo.CreateRaceCondition(rcCreateBody); err != nil {
		return insertId, internalServerError(err)
	}

	// Get Posible Order
	var query model.SugarpayOrderListRequest
	query.OrderNo = remoteResp.TxnOrderId
	query.TransactionNo = remoteResp.TxnRefId
	// query.Amount = fmt.Sprintf("%f", remoteResp.Amount)
	query.Status = "WAIT_PAYMENT"
	query.Limit = 1
	list, _, err := s.repo.GetDbSugarpayOrderList(query)
	if err != nil {
		return nil, internalServerError(err)
	}

	// fmt.Println("SugarpayDecryptRepayDespositPayload.list", helper.StructJson(list))

	// ** ไม่ได้เช็ค amount เพราะจะมีระบบที่ใช้ ทศนิยม และไม่เท่ากัน แต่ก็ควรเช็คแบบ +- 10%

	if len(list) > 0 {
		for _, item := range list {

			// Payment Success, Create Deposit if UserId is not null
			if item.UserId > 0 && item.OrderTypeId == model.SUGARPAY_ORDER_TYPE_DEPOSIT {
				if successStatus == "SUCCESS" {
					// Update Order
					if err := s.repo.ApproveDbSugarpayOrder(item.Id, successStatus); err != nil {
						return nil, internalServerError(err)
					}
					if _, err := CreateCustomerDepositFromSugarpayOrder(s.repo, item.Id, 0); err != nil {
						// WebhookLog
						var createBody2 model.SugarpayWebhookCreateBody
						createBody2.Name = "SUGARPAY_DEPOSIT_ERROR"
						createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
							"item":   item,
							"method": "createCustomerDepositFromSugarpay",
							"error":  err.Error(),
						})
						if _, err = s.repo.CreateSugarpayWebhook(createBody2); err != nil {
							log.Println("Error CreateSugarpayWebhook.createCustomerDepositFromSugarpay", err)
						}
					}
				}
			} else if item.UserId > 0 && item.OrderTypeId == model.SUGARPAY_ORDER_TYPE_WITHDRAW {
				if successStatus == "SUCCESS" {
					// Update Order
					if err := s.repo.ApproveDbSugarpayOrder(item.Id, successStatus); err != nil {
						return nil, internalServerError(err)
					}
					if _, err := approveCustomerWithdrawFromSugarpay(s.repo, *item.RefId); err != nil {
						// WebhookLog
						var createBody2 model.SugarpayWebhookCreateBody
						createBody2.Name = "SUGARPAY_WITHDRAW_ERROR"
						createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
							"item":   item,
							"method": "approveCustomerWithdrawFromSugarpay",
							"error":  err.Error(),
						})
						if _, err = s.repo.CreateSugarpayWebhook(createBody2); err != nil {
							log.Println("Error approveCustomerWithdrawFromSugarpay.CreateSugarpayWebhook", err)
						}
					}
				} else if successStatus == "FAILED" || successStatus == "REJECTED" || successStatus == "EXPIRED" {
					// Update Order
					if err := s.repo.ApproveDbSugarpayOrder(item.Id, successStatus); err != nil {
						return nil, internalServerError(err)
					}
					if err := s.cancelWithdrawFromSugarpayWebhookError(item); err != nil {
						log.Println("Error UpdateDbSugarpayOrderError", err)
					}
				}
			} else {
				log.Println("ApproveDbSugarpayOrder.WithoutItemUpdated", helper.StructJson(item))
			}
		}
	}
	return insertId, nil
}

func (s paygateSugarpayService) CreateSugarpayWithdrawWebhook(req model.SugarpayWebhookRequest) (*int64, error) {

	var createBody model.SugarpayWebhookCreateBody
	createBody.Name = "SUGARPAY_WITHDRAW_CALLBACK"
	createBody.JsonPayload = req.JsonPayload
	insertId, err := s.repo.CreateSugarpayWebhook(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// WITHDRAW
	// {
	// 	type: 'WITHDRAW',
	// 	status: 'SUCCESS',
	// 	status_code: 'OK',
	// 	stm_ref_id: '',
	// 	stm_date: '2023-10-27 14:00:00',
	// 	stm_amount: '1000.00',
	// 	stm_bank_name: '',
	// 	stm_bank_code: '',
	// 	stm_last_4account: '',
	// 	stm_remark: '',
	// 	txn_ref_id: 'xxxxxxxx-xxxxxxxx-xxxxxxxx-xxxxxxxx',
	// 	txn_order_id: 'xxxxxxxx',
	// 	txn_user_id: '',
	// 	timestamp: '2023-10-27 14:02:46',
	// 	account_no: '**********',
	// 	account_bank_name: 'SCB',
	// 	agent_confirm: '',
	// 	stm_account_no: '',
	// 	deposit_balance: '1.00',
	// 	withdraw_balance: '1.00',
	// 	remark: '',
	// 	signature: 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
	//   }

	var remoteResp model.SugarpayWithdrawWebhookResponse
	errJson := json.Unmarshal([]byte(req.JsonPayload), &remoteResp)
	if errJson != nil {
		log.Println("Error unmarshal json", errJson)
		return nil, internalServerError(errJson)
	}

	// Check Response Status.
	// type: "WITHDRAW"
	// status: "SUCCESS", "FAILED"
	// status_code:
	// For Withdraw:
	// ACCOUNT_NUMBER_WRONG : Incorrect account number. (เลขบัญชีผิดพลาด)
	// BANK_CODE_WRONG : Incorrect bank code. (รหัสธนาคารผิดพลาด)
	// BANK_TRANSFER_FAIL : The bank transaction has failed. (ธนาคารทำรายการล้มเหลว)
	// TIMEOUT : The transaction time has expired. (หมดเวลาทำรายการ)
	// OK : Withdrawal completed successfully. (ถอนเรียบร้อย)
	successStatus := strings.ToUpper(strings.TrimSpace(remoteResp.Status))
	if successStatus == "SUCCESS" {
		successStatus = "SUCCESS" // Always use SUCCESS
	} else {
		log.Println("CreateSugarpayWithdrawWebhook.remoteResp.desc.ELSE", successStatus, remoteResp.StatusCode)
		// เก็บตามจริง successStatus = "ERROR"
	}

	// Service Race Condition by Ref1(MchOrderNo)
	acAtUtc := time.Now().UTC()
	var rcCreateBody model.RaceActionCreateBody
	rcCreateBody.Name = "CreateSugarpayWithdrawWebhook"
	rcCreateBody.JsonRequest = helper.StructJson(req)
	rcCreateBody.Status = "PENDING"
	// ActionKey = Ref2-MINUTE
	rcCreateBody.ActionKey = fmt.Sprintf("PG12W%s-%s", acAtUtc.Format("**********"), remoteResp.TxnRefId)
	rcCreateBody.UnlockAt = acAtUtc.Add(time.Second * 1)
	if _, err := s.repo.CreateRaceCondition(rcCreateBody); err != nil {
		return insertId, internalServerError(err)
	}

	// Get Posible Order
	var query model.SugarpayOrderListRequest
	query.OrderNo = remoteResp.TxnOrderId
	query.TransactionNo = remoteResp.TxnRefId
	// query.Amount = fmt.Sprintf("%f", remoteResp.Amount)
	query.Status = "WAIT_PAYMENT"
	query.Limit = 1
	list, _, err := s.repo.GetDbSugarpayOrderList(query)
	if err != nil {
		return nil, internalServerError(err)
	}

	// ** ไม่ได้เช็ค amount เพราะจะมีระบบที่ใช้ ทศนิยม และไม่เท่ากัน แต่ก็ควรเช็คแบบ +- 10%

	if len(list) > 0 {
		for _, item := range list {

			// Payment Success, Create Deposit if UserId is not null
			if item.UserId > 0 && item.OrderTypeId == model.SUGARPAY_ORDER_TYPE_DEPOSIT {
				if successStatus == "SUCCESS" {
					// Update Order
					if err := s.repo.ApproveDbSugarpayOrder(item.Id, successStatus); err != nil {
						return nil, internalServerError(err)
					}
					if _, err := CreateCustomerDepositFromSugarpayOrder(s.repo, item.Id, 0); err != nil {
						// WebhookLog
						var createBody2 model.SugarpayWebhookCreateBody
						createBody2.Name = "SUGARPAY_DEPOSIT_ERROR"
						createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
							"item":   item,
							"method": "createCustomerDepositFromSugarpay",
							"error":  err.Error(),
						})
						if _, err = s.repo.CreateSugarpayWebhook(createBody2); err != nil {
							log.Println("Error CreateSugarpayWebhook.createCustomerDepositFromSugarpay", err)
						}
					}
				}
			} else if item.UserId > 0 && item.OrderTypeId == model.SUGARPAY_ORDER_TYPE_WITHDRAW {
				if successStatus == "SUCCESS" {
					// Update Order
					if err := s.repo.ApproveDbSugarpayOrder(item.Id, successStatus); err != nil {
						return nil, internalServerError(err)
					}
					if _, err := approveCustomerWithdrawFromSugarpay(s.repo, *item.RefId); err != nil {
						// WebhookLog
						var createBody2 model.SugarpayWebhookCreateBody
						createBody2.Name = "SUGARPAY_WITHDRAW_ERROR"
						createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
							"item":   item,
							"method": "approveCustomerWithdrawFromSugarpay",
							"error":  err.Error(),
						})
						if _, err = s.repo.CreateSugarpayWebhook(createBody2); err != nil {
							log.Println("Error approveCustomerWithdrawFromSugarpay.CreateSugarpayWebhook", err)
						}
					}
				} else if successStatus == "FAILED" || successStatus == "REJECTED" || successStatus == "EXPIRED" {
					// Update Order
					if err := s.repo.ApproveDbSugarpayOrder(item.Id, successStatus); err != nil {
						return nil, internalServerError(err)
					}
					if err := s.cancelWithdrawFromSugarpayWebhookError(item); err != nil {
						log.Println("Error UpdateDbSugarpayOrderError", err)
					}
				}
			} else {
				log.Println("ApproveDbSugarpayOrder.WithoutItemUpdated", helper.StructJson(item))
			}
		}
	}
	return insertId, nil
}

func (s paygateSugarpayService) cancelWithdrawFromSugarpayWebhookError(pgOrder model.SugarpayOrderResponse) error {

	adminId := int64(1)

	withdrawTrans, err := s.repo.GetBankTransactionById(*pgOrder.RefId)
	if err != nil {
		log.Println("rollbackWithdrawFromSugarpay.GetBankTransactionById", err)
		return internalServerError(err)
	}
	if withdrawTrans.TransactionStatusId != model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		return internalServerError(errors.New("ไม่อยู๋ในสถานะที่สามารถยกเลิกได้"))
	}

	user, err := s.repo.GetUserBankDetailById(withdrawTrans.UserId)
	if err != nil {
		return internalServerError(err)
	}

	paygateOrder, err := s.repo.GetDbSugarpayOrderByRefId(withdrawTrans.Id)
	if err != nil {
		log.Println("CancelWithdrawFromSugarpay.GetDbSugarpayOrderByRefId", err)
		return internalServerError(err)
	}
	waitStatus := "ERROR"
	if paygateOrder.TransactionStatus == nil || *paygateOrder.TransactionStatus != waitStatus {
		return internalServerError(errors.New("ไม่อยู๋ในสถานะที่สามารถยกเลิกได้"))
	}

	returnAmount := withdrawTrans.CreditAmount

	// race condition deposit
	actionAt := time.Now().UTC()
	var createBody model.RaceActionCreateBody
	createBody.Name = "ADMIN_CANCEL_WITHDRAW"
	createBody.JsonRequest = helper.StructJson(map[string]interface{}{
		"transId":      withdrawTrans.Id,
		"returnAmount": returnAmount,
	})
	createBody.Status = "PENDING"
	createBody.ActionKey = fmt.Sprintf("WITHDRAW_CANCEL_T%sU%dID%d", actionAt.Format("**********"), user.Id, withdrawTrans.Id)
	createBody.UnlockAt = actionAt.Add(time.Second * 1)
	if _, err := s.repo.GetRaceActionIdByActionKey(createBody.ActionKey); err != nil {
		log.Println("CancelWithdrawFromSugarpay.GetRaceActionIdByActionKey", err)
		if err != gorm.ErrRecordNotFound {
			return internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
		}
	} else {
		return internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
	}

	// create race condition
	actionId, err := s.repo.CreateRaceCondition(createBody)
	if err != nil {
		log.Println("CancelWithdrawFromSugarpay.ERROR.CreateRaceCondition", err)
		return internalServerError(errors.New("CANNOT_CREATE_ACTION_LUCKY_WHEEL_ROUND"))
	}
	if actionId == 0 {
		return internalServerError(errors.New("CANNOT_CREATE_ACTION_LUCKY_WHEEL_ROUND"))
	}

	// SET ORDER AS ERROR
	remark := "Payment Cancel Withdraw"
	if err := s.repo.UpdateDbSugarpayOrderError(paygateOrder.Id, remark); err != nil {
		log.Println("CreateSugarpayWithdraw.UpdateDbSugarpayOrderError", err)
	}

	if withdrawTrans.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		// [********] check user credit transaction ว่าตรงไหมกับเงื่อนไข (isShow = false)
		userTransaction, err := s.repo.GetUserWithdrawCreditTransactionByRefId(withdrawTrans.Id, returnAmount)
		if err != nil {
			if err.Error() == recordNotFound {
				log.Println("CancelWithdrawCredit.GetUserCreditTransactionById", err)
				return badRequest("ไม่ตรงกับเงือนไข")
			}
			return internalServerError(err)
		}
		if userTransaction != nil {
			var userCreditReq model.UserTransactionCreateRequest
			userCreditReq.UserId = user.Id
			userCreditReq.TransferAt = withdrawTrans.TransferAt
			userCreditReq.RefId = &withdrawTrans.Id
			userCreditReq.TypeId = model.CREDIT_TYPE_DEPOSIT
			userCreditReq.Amount = returnAmount
			IsShow := false
			userCreditReq.IsShow = &IsShow
			userCreditReq.ConfirmBy = &adminId
			if agentResp, err := s.repo.IncreaseUserCredit(userCreditReq); err != nil {
				log.Println("CancelWithdrawCredit.IncreaseUserCredit", err)
				return internalServerError(err)
			} else if !agentResp.AgentSuccess {
				// [********] error from agent will be in failed
				// if err := s.repoAccounting.UpdateDeporsitTransactionStatusFromAgent(*transId, *agentResp); err != nil {
				//     log.Println("CancelWithdrawCredit.UpdateDeporsitTransactionStatusFromAgent", err)
				// }
				// Set FastBank-READ ** ไม่มี เพราะเป็นการสร้างรายการฝากเอง
				agentFail := errors.New("ทำรายการเครดิตไม่สำเร็จ")
				log.Println("CancelWithdrawCredit.IncreaseUserCredit", agentFail)
				return internalServerError(agentFail)
			}
		}
	}

	// remark := "ADMIN_CANCEL_WITHDRAW"
	var updateApprovedBy model.UpdateUserTransactionStatusRequest
	updateApprovedBy.Id = withdrawTrans.Id
	updateApprovedBy.CanceledAt = actionAt
	updateApprovedBy.CanceledByAdminId = adminId
	updateApprovedBy.TransactionStatusId = model.TRANS_STATUS_WITHDRAW_CANCELED
	updateApprovedBy.CancelRemark = &remark
	err = s.repo.UpdateUserTransactionStatus(updateApprovedBy)
	if err != nil {
		return internalServerError(err)
	}

	var createConfirm model.CreateSuccessTransferWithdrawRequest
	createConfirm.TransactionId = withdrawTrans.Id
	createConfirm.ConfirmedAt = actionAt
	createConfirm.ConfirmedByAdminId = &adminId
	if _, err := CreateCanceledTransferWithdraw(repository.NewBankingRepository(s.sharedDb), createConfirm); err != nil {
		return err
	}

	return nil
}
