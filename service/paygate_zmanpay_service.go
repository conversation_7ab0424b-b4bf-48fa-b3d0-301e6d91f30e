package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"gorm.io/gorm"
)

type ZmanpayService interface {
	// Zmanpay
	// CreateZmanpayWebhook(req model.ZmanpayWebhookRequest) (*int64, error)
	GetZmanpayWebDepositAccount() (*model.ZmanpayCustomerDepositInfo, error)
	CreateZmanpayDeposit(req model.ZmanpayDepositCreateRequest) (*model.ZmanpayOrderWebResponse, error)
	CreateZmanpayWithdraw(req model.ZmanpayWithdrawCreateRequest) (*int64, error)
	// ZmanpayCheckBalance() (*model.ZmanpayCheckBalanceRemoteResponse, error)
	CancelWithdrawFromZmanpay(transId int64, adminId int64) error
	CreateZmanpayDepositWebhook(req model.ZmanpayWebhookRequest) (*int64, error)
	CreateZmanpayWithdrawWebhook(req model.ZmanpayWebhookRequest) (*int64, error)
	// ORDER
	GetPendingZmanpayDepositOrder(userId int64) (*model.ZmanpayOrderWebResponse, error)
	CancelZmanpayDeposit(req model.ZmanpayDepositCancelRequest) error
	// SysLog
	CreateSystemLog(name string, req interface{}) error
}

type paygateZmanpayService struct {
	sharedDb                  *gorm.DB
	repo                      repository.ZmanpayRepository
	activityLuckyWheelService ActivityLuckyWheelService
	promotionWebService       PromotionWebService
	notiService               NotificationService
}

func NewZmanpayService(
	sharedDb *gorm.DB,
	repo repository.ZmanpayRepository,
	activityLuckyWheelService ActivityLuckyWheelService,
	promotionWebService PromotionWebService,
	notiService NotificationService,
) ZmanpayService {
	return &paygateZmanpayService{sharedDb, repo, activityLuckyWheelService, promotionWebService, notiService}
}

func (s paygateZmanpayService) CheckZmanpayCustomerByUserId(user model.UserBankDetailBody) (*model.ZmanpayCustomerResponse, error) {

	return CheckZmanpayCustomerByUserId(s.repo, user)
}

func CheckZmanpayCustomerByUserId(repo repository.ZmanpayRepository, user model.UserBankDetailBody) (*model.ZmanpayCustomerResponse, error) {

	pgAccount, err := GetZmanpayAccount(repo)
	if err != nil || pgAccount == nil {
		log.Println("CheckZmanpayCustomerByUserId.GetZmanpayAccount", err)
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}
	// [********] ไม่ต้องเช็ค เพราะต้องเช็ค บช ทั้งฝากและถอน if !pgAccount.IsDepositEnabled {
	// 	return nil, badRequest("PAYGATE_DEPOSIT_NOT_ENABLED")
	// }

	remoteBankCode, err := GetZmanpayCustomerBankUuid(user.BankCode)
	if err != nil || remoteBankCode == "" {
		return nil, badRequest("USER_BANK_NOT_SUPPORTED")
	}

	reqAccountNumber := helper.StripAllButNumbers(user.BankAccount)
	if reqAccountNumber == "" {
		return nil, badRequest("INVALID_ACCOUNT_NUMBER")
	}

	customer, err := repo.GetZmanpayCustomerByUserId(user.Id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// Create
			// การใส่ชื่อบัญชีหรือเลขที่บัญชีของลูกค้าไม่ถูกต้อง อาจมีผลทำให้การ ฝาก/ถอน ไม่สำเร็จ มีสถานะเป็น error
			// การฝากเงิน มีการตรวจสอบชื่อผู้โอนเงินเข้ามา กับชื่อของลูกค้า
			// การถอนเงิน มีการตรวจสอบเลขที่บัญชี ว่ามีอยู่จริงหรือไม่
			var createBody model.ZmanpayCustomerCreateRemoteRequest
			createBody.BankUuid = remoteBankCode
			createBody.BankAccountNumber = user.BankAccount
			createBody.BankAccountName = user.Fullname
			// createBody.RefCode = "ไม่มีเอกสาร"
			createBody.Status = "active"
			createResp, err := repo.ZmanpayCreateCustomer(*pgAccount, createBody)
			if err != nil {
				return nil, err
			}
			var createBody2 model.ZmanpayCustomerCreateBody
			createBody2.UserId = user.Id
			createBody2.CustomerUuid = createResp.Data.Uuid
			createBody2.FullName = user.Fullname
			createBody2.BankCode = remoteBankCode
			createBody2.AccountNo = user.BankAccount
			createBody2.AccountName = user.Fullname
			if _, err := repo.CreateZmanpayCustomer(createBody2); err != nil {
				return nil, err
			}
			// REGET
			customer2, err := repo.GetZmanpayCustomerByUserId(user.Id)
			if err != nil {
				return nil, err
			}
			return customer2, nil
		}
		return nil, err
	}

	// checkDiff
	if customer.FullName != user.Fullname || customer.BankCode != remoteBankCode || customer.AccountNo != user.BankAccount || customer.AccountName != user.Fullname {
		// Update
		var updateBody model.ZmanpayCustomerUpdateRemoteRequest
		updateBody.CustomerUuid = customer.CustomerUuid
		updateBody.BankUuid = remoteBankCode
		updateBody.BankAccountNumber = user.BankAccount
		updateBody.BankAccountName = user.Fullname
		// updateBody.RefCode = "ไม่มีเอกสาร" ZTA ??
		updateBody.Status = "active"
		if _, err := repo.ZmanpayUpdateCustomer(*pgAccount, updateBody); err != nil {
			return nil, err
		}
		var updateBody2 model.ZmanpayCustomerUpdateBody
		updateBody2.FullName = &user.Fullname
		updateBody2.BankCode = &remoteBankCode
		updateBody2.AccountNo = &user.BankAccount
		updateBody2.AccountName = &user.Fullname
		if err := repo.UpdateZmanpayCustomer(customer.Id, updateBody2); err != nil {
			return nil, err
		}
		// REGET
		customerUpdated, err := repo.GetZmanpayCustomerByUserId(user.Id)
		if err != nil {
			return nil, err
		}
		return customerUpdated, nil
	}

	return customer, nil
}

func (s paygateZmanpayService) ConfirmDepositTransaction(id int64, req model.BankConfirmDepositRequest) error {

	record, err := s.repo.GetBankTransactionById(id)
	if err != nil {
		return internalServerError(err)
	}
	if record.TransactionStatusId != model.TRANS_STATUS_PENDING {
		return badRequest("Transaction is not pending")
	}
	if record.TransactionTypeId != model.TRANSACTION_TYPE_DEPOSIT && record.TransactionTypeId != model.TRANSACTION_TYPE_BONUS {
		return badRequest("Transaction is not deposit")
	}
	jsonBefore, _ := json.Marshal(record)

	var updateData model.BankDepositTransactionConfirmBody
	updateData.TransactionStatusId = model.TRANS_STATUS_DEPOSIT_PENDING_CREDIT
	updateData.ConfirmedAt = req.ConfirmedAt
	updateData.ConfirmedByAdminId = req.ConfirmedByAdminId

	// if record.IsAutoCredit && record.TransferAt != nil {
	// 	seconds := time.Now().UTC().Sub(record.TransferAt.UTC()).Seconds()
	// 	autoProcessTimer := fmt.Sprintf("%.2f", seconds)
	// 	updateData.AutoProcessTimer = &autoProcessTimer
	// }

	var createBody model.CreateBankTransactionActionBody
	createBody.ActionKey = fmt.Sprintf("DCF_STATE#%d", record.Id)
	createBody.TransactionId = record.Id
	createBody.UserId = record.UserId
	createBody.TransactionTypeId = record.TransactionTypeId
	createBody.FromAccountId = record.FromAccountId
	createBody.ToAccountId = record.ToAccountId
	createBody.JsonBefore = string(jsonBefore)
	if req.TransferAt == nil {
		createBody.TransferAt = record.TransferAt
	} else {
		TransferAt := req.TransferAt
		createBody.TransferAt = TransferAt
		updateData.TransferAt = *TransferAt
	}
	if req.SlipUrl != nil {
		createBody.SlipUrl = *req.SlipUrl
	}
	createBody.CreditAmount = record.CreditAmount
	if req.BonusAmount != nil {
		createBody.BonusAmount = *req.BonusAmount
	}
	createBody.ConfirmedAt = req.ConfirmedAt
	createBody.ConfirmedByAdminId = req.ConfirmedByAdminId
	if actionId, err := s.repo.CreateTransactionAction(createBody); err == nil {
		// do nothing ?
		if err := s.repo.ConfirmPendingDepositTransaction(id, updateData); err != nil {
			if err := s.repo.RollbackTransactionAction(*actionId); err == nil {
				return internalServerError(err)
			}
			return internalServerError(err)
		}
	} else {
		return internalServerError(err)
	}
	return nil
}

func (s paygateZmanpayService) GetZmanpayWebDepositAccount() (*model.ZmanpayCustomerDepositInfo, error) {

	var result model.ZmanpayCustomerDepositInfo

	pgAccount, err := s.GetZmanpayAccount()
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}
	if !pgAccount.IsDepositEnabled {
		return nil, badRequest("PAYGATE_DEPOSIT_NOT_ENABLED")
	}

	result.Name = pgAccount.Name
	if pgAccount.DisplayName == "" {
		result.Name = "เติมเงินผ่าน QR Code"
	} else {
		result.DisplayName = pgAccount.DisplayName
	}
	result.ShopName = pgAccount.ShopName
	result.MinAmount = model.ZMANPAY_DEFMIN_DEPOSIT_AMOUNT
	result.MaxAmount = model.ZMANPAY_DEFMAX_DEPOSIT_AMOUNT

	return &result, nil
}

func (s paygateZmanpayService) GetZmanpayAccount() (*model.PaygateAccountResponse, error) {

	return GetZmanpayAccount(s.repo)
}

func GetZmanpayAccount(repo repository.ZmanpayRepository) (*model.PaygateAccountResponse, error) {

	pgAccount, err := repo.GetPaygateAccountByProviderId(model.PAYGATE_MERCHANT_ID_ZMANPAY)
	if err != nil {
		return nil, internalServerError(err)
	}
	return pgAccount, nil
}

func GetZmanpayCustomerBankUuid(userBankCode string) (string, error) {

	// 	curl -X 'GET' \
	//   'https://api.zapman.net/api/v1/client/bank' \
	//   -H 'accept: application/json'
	supportedBankCodeList := make(map[string]string)
	supportedBankCodeList["SCB"] = "b25d5216-c245-41bd-91f6-98c2f4f49cd4"
	supportedBankCodeList["KBANK"] = "59210d13-196e-4669-9e00-cee06772db0a"
	supportedBankCodeList["TTB"] = "177e51fa-aaf2-4c3c-9032-95739a9e682a"
	supportedBankCodeList["BBL"] = "b4937489-c370-43e3-a271-ce047d94e026"
	supportedBankCodeList["KTB"] = "b66de91b-ef3b-4a44-9280-f5a915e31124"
	supportedBankCodeList["BAY"] = "4a2b0f2d-5a41-403b-9348-cb1faaa6f524"
	supportedBankCodeList["GSB"] = "66c10559-3632-4a72-a049-09b827dfdd15"
	supportedBankCodeList["GHB"] = "a8554172-cf9c-4105-8536-07948ae047db"
	supportedBankCodeList["BAAC"] = "4bd57c64-b71a-4cb6-b06c-250fb0530a66"
	supportedBankCodeList["ICBC"] = "d4b9d5bc-c9b8-40b0-977d-547dddeb8350"
	supportedBankCodeList["UOB"] = "048bfe5c-20de-4c73-b561-f08c3cbe3703"
	supportedBankCodeList["HSBC"] = "86fa43da-883f-437e-8517-0f9103e51cee"
	supportedBankCodeList["IBANK"] = "3194087a-3054-4e3f-8412-23d9f33ff15e"
	supportedBankCodeList["TISCO"] = "4c91cbb7-7bd9-4848-af8f-cc5eb5ea28dc"
	supportedBankCodeList["KKP"] = "5cc15c51-a165-4a72-a321-b5402874a619"
	supportedBankCodeList["TCRB"] = "c1b750f7-1143-4fde-823e-0d618e1faa9f"
	supportedBankCodeList["LHB"] = "d0b0ab7f-77a8-45dc-8b02-ab1ec353dc16"
	supportedBankCodeList["CIMB"] = "a576f5b3-7119-4241-8150-6c17774e3a28"
	supportedBankCodeList["UOBT"] = "8cd94634-e87e-4163-a534-6bedddf6cf2c"

	// LOCAL_BANK_CODE
	// "id"	"name"	"code"	"icon_url"	"type_flag"	"country_code"	"use_currency"	"created_at"
	// "1"	"กสิกรไทย"	"kbank"	"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/kbank.png/public"	"********"	"TH"	"THB"	"2023-09-18 12:00:11"
	// "2"	"ไทยพาณิชย์"	"scb"	"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/scb.png/public"	"********"	"TH"	"THB"	"2023-09-18 12:00:11"
	// "3"	"กรุงเทพ"	"bbl"	"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/bbl.png/public"	"********"	"TH"	"THB"	"2023-09-18 12:00:11"
	// "4"	"กรุงศรี"	"bay"	"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/bay.png/public"	"********"	"TH"	"THB"	"2023-09-18 12:00:11"
	// "5"	"กรุงไทย"	"ktb"	"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/ktb.png/public"	"********"	"TH"	"THB"	"2023-09-18 12:00:11"
	// "6"	"ทีเอ็มบีธนชาต"	"ttb"	"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/ttb.png/public"	"********"	"TH"	"THB"	"2023-09-18 12:00:11"
	// "7"	"ออมสิน"	"gsb"	"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/gsb.png/public"	"********"	"TH"	"THB"	"2023-09-18 12:00:11"
	// "8"	"ธกส"	"baac"	"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/baac.png/public"	"********"	"TH"	"THB"	"2023-09-18 12:00:11"
	// "9"	"เกียรตินาคิน"	"kkp"	"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/kk.png/public"	"********"	"TH"	"THB"	"2023-09-18 12:00:11"
	// "10"	"อาคารสงเคราะห์"	"ghb"	"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/ghb.png/public"	"********"	"TH"	"THB"	"2023-09-18 12:00:11"
	// "11"	"ยูโอบี"	"uob"	"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/uob.png/public"	"********"	"TH"	"THB"	"2023-09-18 12:00:11"
	// "12"	"แลนด์ แอนด์ เฮ้าส์"	"lh"	"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/lh.png/public"	"********"	"TH"	"THB"	"2023-09-18 12:00:11"
	// "13"	"ซีไอเอ็มบี"	"cimb"	"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/cimb.png/public"	"********"	"TH"	"THB"	"2023-09-18 12:00:11"
	// "14"	"เอชเอสบีซี"	"hsbc"	"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/hsbc.png/public"	"********"	"TH"	"THB"	"2023-09-18 12:00:11"
	// "15"	"ไอซีบีซี"	"icbc"	"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/icbc.png/public"	"********"	"ALL"	"THB"	"2023-09-18 12:00:11"
	// "16"	"ธนาคารอิสลาม"	"isbt"	"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/isbt.png/public"	"********"	"TH"	"THB"	"2023-10-30 07:25:21"
	// "17"	"ทิสโก้"	"tisco"	"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/tisco.png/public"	"********"	"TH"	"THB"	"2023-10-30 07:25:21"
	// "18"	"ซิตี้แบงก์"	"citi"	"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/citi.png/public"	"********"	"TH"	"THB"	"2023-10-30 07:25:21"
	// "19"	"สแตนดาร์ดชาร์เตอร์ด"	"scbt"	"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/scbt.png/public"	"********"	"TH"	"THB"	"2023-10-30 07:25:21"
	// "20"	"TrueMoney Wallet"	"true"	"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/true.png/public"	"********"	"TH"	"THB"	"2023-10-30 07:25:21"
	// "21"	"ธนาคารภายนอก"	"external"	"https://storage.googleapis.com/cbgame/banks/none.png"	"********"	"TH"	"THB"	"2023-12-21 07:49:30"
	// "50"	"ธนาคารการค้าต่างประเทศลาว (LAK)"	"BCEL"	"https://storage.googleapis.com/cbgame/banks/none.png"	"********"	"LAOS"	"LAK"	"2024-05-10 09:49:43"
	// "51"	"ธนาคารการค้าต่างประเทศลาว (THB)"	"BCELTH"	"https://storage.googleapis.com/cbgame/banks/none.png"	"********"	"LAOS"	"THB"	"2024-05-10 09:49:43"
	// "52"	"ธนาคารพัฒนาลาว"	"LDB"	"https://storage.googleapis.com/cbgame/banks/none.png"	"********"	"LAOS"	"LAK"	"2024-05-10 09:49:43"
	// "53"	"ธนาคารส่งเสริมการเกษตร"	"APB"	"https://storage.googleapis.com/cbgame/banks/none.png"	"********"	"LAOS"	"LAK"	"2024-05-10 09:49:43"
	// "54"	"ธนาคารร่วมธุรกิจลาว"	"BOL"	"https://storage.googleapis.com/cbgame/banks/none.png"	"********"	"LAOS"	"LAK"	"2024-05-10 09:49:43"
	// "55"	"ธนาคารร่วมพัฒนา"	"LDBBLALA XXX"	"https://storage.googleapis.com/cbgame/banks/none.png"	"********"	"LAOS"	"LAK"	"2024-05-10 09:49:43"
	// "56"	"ธนาคาร ST"	"ST"	"https://storage.googleapis.com/cbgame/banks/none.png"	"********"	"LAOS"	"LAK"	"2024-05-10 09:49:43"
	// "57"	"ธนาคาร BIC"	"BIC"	"https://storage.googleapis.com/cbgame/banks/none.png"	"********"	"LAOS"	"LAK"	"2024-05-10 09:49:43"
	// "58"	"ธนาคาร Maruhan Japan"	"Maruhan Japan"	"https://storage.googleapis.com/cbgame/banks/none.png"	"********"	"LAOS"	"LAK"	"2024-05-10 09:49:43"
	// "59"	"ธนาคาร Sacombank"	"Sacombank"	"https://storage.googleapis.com/cbgame/banks/none.png"	"********"	"LAOS"	"LAK"	"2024-05-10 09:49:43"
	// "60"	"ธนาคารแห่งประเทศจีน"	"BKCHTHBK"	"https://storage.googleapis.com/cbgame/banks/none.png"	"********"	"LAOS"	"LAK"	"2024-05-10 09:49:43"
	// "61"	"ธนาคาร Vietin"	"Vietin"	"https://storage.googleapis.com/cbgame/banks/none.png"	"********"	"LAOS"	"LAK"	"2024-05-10 09:49:43"
	// "62"	"ธนาคาร ACLEDA"	"ACLEDA"	"https://storage.googleapis.com/cbgame/banks/none.png"	"********"	"LAOS"	"LAK"	"2024-05-10 09:49:43"
	// "100"	"ไม่พบข้อมูลธนาคาร"	"unknown"	"https://storage.googleapis.com/cbgame/banks/none.png"	"********"	"TH"	"THB"	"2024-08-17 15:46:00"

	userBankCode = strings.ToUpper(userBankCode)
	if userBankCode == "LH" {
		userBankCode = "LHB"
	}
	if _, ok := supportedBankCodeList[userBankCode]; !ok {
		return "", badRequest("BANK_CODE_NOT_SUPPORT")
	}

	return supportedBankCodeList[userBankCode], nil
}

func (s paygateZmanpayService) CreateZmanpayDeposit(req model.ZmanpayDepositCreateRequest) (*model.ZmanpayOrderWebResponse, error) {

	var result model.ZmanpayOrderWebResponse

	// Ruled by Provider
	if req.Amount < model.ZMANPAY_DEFMIN_DEPOSIT_AMOUNT || req.Amount > model.ZMANPAY_DEFMAX_DEPOSIT_AMOUNT {
		log.Println("req.Amount", req.Amount)
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}

	pgAccount, err := s.GetZmanpayAccount()
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}
	if !pgAccount.IsDepositEnabled {
		return nil, badRequest("PAYGATE_DEPOSIT_NOT_ENABLED")
	}
	// PrerequisitesZmanpay
	if pgAccount.ApiEndPoint == "" || pgAccount.AccessKey == "" || pgAccount.SecretKey == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}

	// [********] get previse deposit order in last 5 minutes
	if pOrder, err := s.repo.CheckZmanpayDepositOrderInLast5Minutes(req.UserId); err != nil {
		if err != gorm.ErrRecordNotFound {
			return nil, internalServerError(err)
		}
	} else if pOrder != nil {
		actionAtUtc := time.Now().UTC()
		if pOrder.Amount == req.Amount {
			if pOrder.CreatedAt.Add(time.Minute * 10).After(actionAtUtc) {
				result.OrderId = pOrder.Id
				result.UserId = pOrder.UserId
				result.OrderNo = pOrder.OrderNo
				result.Amount = pOrder.Amount
				result.TransferAmount = pOrder.TransferAmount
				result.TransactionStatus = pOrder.TransactionStatus
				// result.QrCode = pOrder.QrPromptpay
				// result.QrUrl = pOrder.QrPromptpay
				result.QrBase64 = pOrder.QrBase64
				result.ExtraPromptpayId = pOrder.ExtraPromptpayId
				result.CreatedAt = pOrder.CreatedAt
				return &result, nil
			}
		}
		// ภายใน 10 นาที ถ้ามีการทำรายการฝากเงิน จะไม่สามารถทำรายการได้
		if pOrder.CreatedAt.Add(time.Minute * 10).After(actionAtUtc) {
			// ยอดไม่ตรงต้องไปยกเลิก order เก่าก่อน
			return nil, badRequest("คุณทำรายการฝากเงินค้างอยู่ กรุณายกเลิกการทำรายการเดิมก่อน")
		}
	}

	webDomain := os.Getenv("WEB_DOMAIN")
	if webDomain == "" {
		return nil, errors.New("EMPTY_ENV_WEB_DOMAIN")
	}
	if !strings.HasPrefix(webDomain, "https://") {
		webDomain = "https://" + webDomain
	}
	webhookDomain := os.Getenv("CYBERGAME_WEBHOOK_ENDPOINT")
	if webhookDomain == "" {
		return nil, errors.New("EMPTY_ENV_CYBERGAME_WEBHOOK_ENDPOINT")
	}

	user, err := s.repo.GetUserBankDetailById(req.UserId)
	if err != nil {
		return nil, badRequest("INVALID_USER")
	}
	if user == nil || user.BankAccount == "" || user.BankCode == "" {
		return nil, badRequest("INVALID_USER_ACCOUNT")
	}
	remoteBankCode, err := GetZmanpayCustomerBankUuid(user.BankCode)
	if err != nil || remoteBankCode == "" {
		return nil, badRequest("USER_BANK_NOT_SUPPORTED")
	}

	accountingRepo := repository.NewAccountingRepository(s.repo.GetDb())
	configWeb, err := s.repo.GetWebConfiguration()
	if err != nil {
		log.Println("CreateZmanpayDeposit.GetWebConfiguration", err)
		return nil, badRequest("WEB_CONFIG_NOT_FOUND")
	}
	if user.Id != 0 && configWeb != nil {
		// [********] ฝากครั้งแรกขั้นต่ำ ปรับ flow เช็ค user ให้เช็คจากรายการฝากครั้งแรกจริงๆ ไม่เช็คจากรหัสที่ได้รับ
		if err := CheckFirstMinimunDeposit(accountingRepo, user.Id, req.Amount, *configWeb); err != nil {
			return nil, badRequest(fmt.Sprintf("ฝากครั้งแรกขั้นต่ำ %d บาท", configWeb.MinFirstMemberDeposit))
		}
	} else {
		log.Println("CreateZmanpayDeposit.CheckFirstMinimunDeposit", "CANT_CHECK_FIRST_MINIMUM_DEPOSIT_ON_EMPTY_CONFIG")
	}

	// Payment Extra Data
	// 1.CheckZmanpayCustomerByUserId
	payCustomer, err := s.CheckZmanpayCustomerByUserId(*user)
	if err != nil {
		log.Println("CreateZmanpayDeposit.CheckZmanpayCustomerByUserId", err)
		return nil, badRequest("INVALID_PAYGATE_USER")
	}
	if payCustomer.CustomerUuid == "" {
		log.Println("CreateZmanpayDeposit.CheckZmanpayCustomerByUserId", "EMPTY_PAYGATE_USER")
		return nil, badRequest("EMPTY_PAYGATE_USER")
	}

	// ===========================================================================================
	var createBody model.ZmanpayOrderCreateBody
	createBody.UserId = req.UserId
	createBody.OrderTypeId = model.ZMANPAY_ORDER_TYPE_DEPOSIT
	createBody.OrderNo = "" // Autogen
	createBody.Amount = req.Amount
	createBody.TransactionStatus = "PENDING"
	insertId, err := s.repo.CreateDbZmanpayOrder(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	// reget Order
	pendingOrder, err := s.repo.GetDbZmanpayOrderById(*insertId)
	if err != nil {
		// SET AS ERROR
		remark := "Error GetDbZmanpayOrderById, " + err.Error()
		if err := s.repo.UpdateDbZmanpayOrderError(*insertId, remark); err != nil {
			log.Println("CreateZmanpayDeposit.UpdateDbZmanpayOrderError", err)
		}
		return nil, internalServerError(err)
	}
	// Create ZMANPAY Order
	var remoteRequest model.ZmanpayDepositCreateRemoteRequest
	remoteRequest.CustomerAccountUuid = payCustomer.CustomerUuid
	remoteRequest.Amount = req.Amount
	remoteRequest.Currency = "thb"
	remoteRequest.PaymentMethod = "qr"
	remoteRequest.RedirectUrl = fmt.Sprintf("%s/deposit", webDomain)
	remoteRequest.CallbackUrl = fmt.Sprintf("%s/zmanpay/dep-callback", webhookDomain)
	remoteRequest.MerchantOrderId = pendingOrder.OrderNo
	remoteResp, err := s.repo.ZmanpayDeposit(*pgAccount, remoteRequest)
	if err != nil {
		// SET AS ERROR
		remark := "Error ZmanpayDeposit, " + err.Error()
		if err := s.repo.UpdateDbZmanpayOrderError(*insertId, remark); err != nil {
			log.Println("CreateZmanpayDeposit.UpdateDbZmanpayOrderError", err)
		}
		// SysLog At REPO
		return nil, internalServerError(err)
	}

	if remoteResp.Code != "ZAP20000" && remoteResp.Code != "20000" {
		log.Println("CreateZmanpayDeposit.ZmanpayDeposit.remoteResp", helper.StructJson(remoteResp))
		// SET AS ERROR
		remark := remoteResp.Message
		if len(remark) == 0 {
			remark = "UNKNOW_ERROR from depositWithZmanpay"
		}
		if err := s.repo.UpdateDbZmanpayOrderError(*insertId, remark); err != nil {
			log.Println("CreateZmanpayDeposit.UpdateDbZmanpayOrderError", err)
		}
		// SysLog
		if _, err := s.repo.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "CreateZmanpayDeposit.ZmanpayDeposit",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(remoteRequest),
			JsonResponse: helper.StructJson(remoteResp),
		}); err != nil {
			log.Println("CreateZmanpayDeposit.CreateSysLog", err)
		}
		return nil, internalServerError(fmt.Errorf(remark))
	}

	// Parse Float Amount
	// transferAmount, err := strconv.ParseFloat(remoteResp.Data.CustomerTransferAmount, 64)
	// if err != nil {
	// 	// SET AS ERROR
	// 	remark := "Error ParseFloat Amount, " + err.Error()
	// 	if err := s.repo.UpdateDbZmanpayOrderError(*insertId, remark); err != nil {
	// 		log.Println("CreateZmanpayDeposit.UpdateDbZmanpayOrderError", err)
	// 	}
	// 	return nil, internalServerError(err)
	// }
	transferAmount := remoteResp.Data.CustomerTransferAmount

	// onCreate Success
	var updateBody model.ZmanpayOrderUpdateBody
	updateBody.TransactionNo = remoteResp.Data.Uuid
	updateBody.TransactionDate = time.Now().UTC()
	updateBody.TransactionStatus = "WAIT_PAYMENT"
	updateBody.TransferAmount = transferAmount
	updateBody.QrBase64 = remoteResp.Data.PaymentImageBase64 // Qr-data-Base64
	updateBody.ExtraPromptpayId = remoteResp.Data.BankAccountPromptpayId
	if err := s.repo.UpdateDbZmanpayOrder(*insertId, updateBody); err != nil {
		// SET AS ERROR
		remark := "Error UpdateDbZmanpayOrder, " + err.Error()
		if err := s.repo.UpdateDbZmanpayOrderError(*insertId, remark); err != nil {
			log.Println("CreateZmanpayDeposit.UpdateDbZmanpayOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	waitPayOrder, err := s.repo.GetDbZmanpayOrderById(*insertId)
	if err != nil {
		return nil, internalServerError(err)
	}

	result.OrderId = waitPayOrder.Id
	result.UserId = waitPayOrder.UserId
	result.OrderNo = waitPayOrder.OrderNo
	result.Amount = waitPayOrder.Amount
	result.TransferAmount = transferAmount
	result.TransactionStatus = waitPayOrder.TransactionStatus
	// result.QrCode = waitPayOrder.QrPromptpay
	// result.QrUrl = waitPayOrder.QrPromptpay
	result.QrBase64 = waitPayOrder.QrBase64
	result.ExtraPromptpayId = waitPayOrder.ExtraPromptpayId
	result.CreatedAt = waitPayOrder.CreatedAt

	return &result, nil
}

func (s paygateZmanpayService) CreateZmanpayWithdraw(req model.ZmanpayWithdrawCreateRequest) (*int64, error) {

	// Ruled by Provider
	if req.Amount < model.ZMANPAY_DEFMIN_WITHDRAW_AMOUNT || req.Amount > model.ZMANPAY_DEFMAX_WITHDRAW_AMOUNT {
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}

	webhookDomain := os.Getenv("CYBERGAME_WEBHOOK_ENDPOINT")
	if webhookDomain == "" {
		return nil, errors.New("EMPTY_ENV_CYBERGAME_WEBHOOK_ENDPOINT")
	}

	pgAccount, err := s.GetZmanpayAccount()
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}
	if !pgAccount.IsWithdrawEnabled {
		return nil, badRequest("PAYGATE_WITHDRAW_NOT_ENABLED")
	}
	// PrerequisitesZmanpay
	if pgAccount.ApiEndPoint == "" || pgAccount.AccessKey == "" || pgAccount.SecretKey == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}

	user, err := s.repo.GetUserBankDetailById(req.UserId)
	if err != nil {
		return nil, badRequest("INVALID_USER")
	}
	if user == nil || user.BankAccount == "" || user.BankCode == "" {
		return nil, badRequest("INVALID_USER_ACCOUNT")
	}
	remoteBankCode, err := GetZmanpayCustomerBankUuid(user.BankCode)
	if err != nil || remoteBankCode == "" {
		return nil, badRequest("USER_BANK_NOT_SUPPORTED")
	}

	// Payment Extra Data
	// 1.CheckZmanpayCustomerByUserId
	payCustomer, err := s.CheckZmanpayCustomerByUserId(*user)
	if err != nil {
		log.Println("CreateZmanpayWithdraw.CheckZmanpayCustomerByUserId", err)
		return nil, badRequest("INVALID_PAYGATE_USER")
	}
	if payCustomer.CustomerUuid == "" {
		log.Println("CreateZmanpayWithdraw.CheckZmanpayCustomerByUserId", "EMPTY_PAYGATE_USER_UUID")
		return nil, badRequest("EMPTY_PAYGATE_USER")
	}

	// ===========================================================================================
	// CREATE Order
	var createBody model.ZmanpayOrderCreateBody
	createBody.RefId = &req.RefId
	createBody.UserId = req.UserId
	createBody.OrderTypeId = model.ZMANPAY_ORDER_TYPE_WITHDRAW
	createBody.OrderNo = "" // Autogen
	createBody.Amount = req.Amount
	createBody.TransactionStatus = "PENDING"
	insertId, err := s.repo.CreateDbZmanpayOrder(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	// reget Order
	pendingOrder, err := s.repo.GetDbZmanpayOrderById(*insertId)
	if err != nil {
		// SET AS ERROR
		remark := "Error GetDbZmanpayOrderById, " + err.Error()
		if err := s.repo.UpdateDbZmanpayOrderError(*insertId, remark); err != nil {
			log.Println("CreateZmanpayWithdraw.UpdateDbZmanpayOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// Create ZMANPAY Order
	var remoteRequest model.ZmanpayWithdrawCreateRemoteRequest
	remoteRequest.CustomerAccountUuid = payCustomer.CustomerUuid
	remoteRequest.Amount = pendingOrder.Amount
	remoteRequest.Currency = "thb"
	remoteRequest.CallbackUrl = fmt.Sprintf("%s/zmanpay/wid-callback", webhookDomain)
	remoteRequest.MerchantOrderId = pendingOrder.OrderNo
	remoteResp, err := s.repo.ZmanpayWithdraw(*pgAccount, remoteRequest)
	if err != nil {
		// SET AS ERROR
		remark := "Error ZmanpayWithdraw, " + err.Error()
		if err := s.repo.UpdateDbZmanpayOrderError(*insertId, remark); err != nil {
			log.Println("ZmanpayWithdraw.UpdateDbZmanpayOrderError", err)
		}
		// SysLog At REPO
		return nil, internalServerError(err)
	}

	// fmt.Println("CreateZmanpayWithdraw.remoteResp", helper.StructJson(remoteResp))

	if remoteResp.Code != "ZAP20000" && remoteResp.Code != "20000" {
		log.Println("ZmanpayWithdraw.CreateZmanpayWithdraw.remoteResp", helper.StructJson(remoteResp))
		// SET AS ERROR
		remark := remoteResp.Message
		if len(remark) == 0 {
			remark = "UNKNOW_ERROR from withdrawWithZmanpay"
		}
		if err := s.repo.UpdateDbZmanpayOrderError(*insertId, remark); err != nil {
			log.Println("ZmanpayWithdraw.UpdateDbZmanpayOrderError", err)
		}
		// SysLog
		if _, err := s.repo.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "CreateZmanpayWithdraw.ZmanpayWithdraw",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(remoteRequest),
			JsonResponse: helper.StructJson(remoteResp),
		}); err != nil {
			log.Println("CreateZmanpayWithdraw.CreateSysLog", err)
		}
		return nil, internalServerError(fmt.Errorf(remark))
	}

	// onCreate Success
	var updateBody model.ZmanpayOrderUpdateBody
	updateBody.TransactionNo = remoteResp.Data.Uuid
	updateBody.TransactionDate = time.Now().UTC()
	updateBody.TransactionStatus = "WAIT_PAYMENT"
	updateBody.QrPromptpay = remoteResp.Message
	if err := s.repo.UpdateDbZmanpayOrder(*insertId, updateBody); err != nil {
		// SET AS ERROR
		remark := "Error UpdateDbZmanpayOrder, " + err.Error()
		if err := s.repo.UpdateDbZmanpayOrderError(*insertId, remark); err != nil {
			log.Println("CreateZmanpayWithdraw.UpdateDbZmanpayOrderError", err)
		}
		return nil, internalServerError(err)
	}
	return insertId, nil
}

func CreateCustomerDepositFromZmanpayOrder(repo repository.ZmanpayRepository, orderId int64, adminId int64) (*int64, error) {

	// ** ทุกรายการจะต้องเช็คสถานะใหม่ทั้งหมด เพราะ admin แย่งกดอนุมัติได้
	item, err := repo.GetRawZmanpayPendingDepositOrderById(orderId)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, badRequest("ORDER_NOT_FOUND")
		}
		return nil, internalServerError(err)
	}

	// Check BankTransactionStatus = 'PENDING'
	if item.BankTransactionStatus != "PENDING" {
		return nil, badRequest("ORDER_NOT_PENDING")
	}

	return createCustomerDepositFromZmanpay(repo, *item, adminId)
}

func createCustomerDepositFromZmanpay(repo repository.ZmanpayRepository, item model.ZmanpayOrderResponse, adminId int64) (*int64, error) {

	var externalNoti model.NotifyExternalNotificationRequest
	actionAt := time.Now().UTC()
	// confirmByUserId := int64(0)

	depositAmount := item.Amount

	user, err := repo.GetUserBankDetailById(item.UserId)
	if err != nil {
		log.Println("createCustomerDepositFromZmanpay.GetUserBankDetailByMemberCode", err)
		return nil, internalServerError(err)
	}

	var promotionWebUserId int64
	if getPromotionWebUser, err := repo.GetDepositCurrentProcessingUserPromotion(item.UserId); err != nil {
		log.Println("createCustomerDepositFromZmanpay.GetDepositCurrentProcessingUserPromotion", err)
	} else if getPromotionWebUser != nil {
		promotionWebUserId = getPromotionWebUser.Id
	}

	// MemberCode
	if user.MemberCode == "" {
		memberCode, err := GenUniqueUserMemberCode(repository.NewUserRepository(repo.GetDb()), user.Id)
		if err != nil {
			log.Println("createCustomerDepositFromZmanpay.GenUniqueUserMemberCode", err)
			return nil, internalServerError(err)
		}
		user.MemberCode = *memberCode
	}

	// layer — 2024-04-02 at 10:06 AM
	// ถ้าแก้ตามนี้แล้วน่าจะไม่เจอ ถ้าเคสกันซ้ำเราครอบคลุม
	// @Dunk @TULA
	// กันซ้ำคือ heng ไปซ้ำกับแอดมินเติมมือ
	// ----
	// เงื่อนไขกันซ้ำ
	// 1 หารายการฝากสำเร็จล่าสุดของยูสนั้น
	// 2 ถ้ามีรายการล่าสุดที่เป็นเติมมือ
	// -เช็คเวลาเติม ต้องมากกว่าเวลาโอนในสลิป
	// -ยอดเงินเท่ากัน
	// **ถ้าเข้าเงื่อนไขทั้งหมด คือแสดงว่า webhook เข้าทีหลังแอดมินเติมมือ
	// ต้อง bypass ไม่ให้เติมอีก และอัพเดท status ว่าจับคู่ได่แล้ว
	// ---
	// วิธีนี้น่าจะใช้ได้กับทุก webhook (heng, fastbank) ที่มีเวลาโอนสลิปมาให้
	duplicateRemark := "INIT"
	hasDuplicateDeposit := false
	manualDeposit, err := repo.GetLastestBankAdminManualDepositTransaction(user.Id)
	if err != nil {
		if err.Error() != recordNotFound {
			log.Println("createCustomerDepositFromZmanpay.GetLastestBankAdminManualDepositTransaction", err)
			return nil, internalServerError(err)
		}
		// NOT_FOUND = ถ้าไม่เจอ ให้ทำต่อ
		duplicateRemark = "OK_NO_MANUAL_DEPOSIT"
	}
	if manualDeposit != nil && item.TransactionDate != nil {
		if manualDeposit.TransferAt.After(*item.TransactionDate) {
			// ถ้าเวลาเติมมือมากกว่าเวลาโอนในสลิป
			if manualDeposit.CreditAmount == depositAmount {
				// ถ้ายอดเงินเท่ากัน
				duplicateRemark = "STOP_MANUAL_DEPOSIT_AFTER_AND_SAME_AMOUNT"
				hasDuplicateDeposit = true
			} else {
				duplicateRemark = "OK_MANUAL_DEPOSIT_AMOUNT_NOT_MATCH"
			}
		} else {
			duplicateRemark = "OK_MANUAL_DEPOSIT_BEFORE_WITHDRAW"
		}
	}
	if duplicateRemark != "INIT" {
		// WebhookLog
		var createBody2 model.ZmanpayWebhookCreateBody
		createBody2.Name = "ZMANPAY_DEPOSIT_DUPLICATE_CHECKER"
		createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
			"item":                item,
			"method":              "createCustomerDeposit",
			"hasDuplicateDeposit": hasDuplicateDeposit,
			"error":               duplicateRemark,
			"manualDeposit":       manualDeposit,
		})
		if _, err = repo.CreateZmanpayWebhook(createBody2); err != nil {
			log.Println("Error CreateZmanpayWebhook.CreateZmanpayWebhook", err)
		}
		if hasDuplicateDeposit {
			return nil, internalServerError(fmt.Errorf("DUPLICATE_DEPOSIT"))
		}
	}

	pgAccount, err := GetZmanpayAccount(repo)
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}

	// ===================================================
	paygateRepo := repository.NewPaymentGatewayRepository(repo.GetDb())
	accountingRepo := repository.NewAccountingRepository(repo.GetDb())
	luckyWheelRepo := repository.NewActivityLuckyWheelRepository(repo.GetDb())
	promotionWebRepo := repository.NewPromotionWebRepository(repo.GetDb())
	notiRepo := repository.NewNotificationRepository(repo.GetDb())

	// Affiliate + Alliance Income
	member, err := repo.GetMemberById(user.Id)
	if err != nil {
		return nil, internalServerError(err)
	}
	if member.UserTypeName == "NONE" {
		// ถ้าฝากครั้งแรก จะได้เป็น AFFILIATE + REFER COMMISION
		if err := UserFirstDepositCommission(accountingRepo, *member, depositAmount); err != nil {
			log.Println("CreateFristTimeDepositRecord.UserFirstDepositCommission.ERROR", err)
			return nil, internalServerError(err)
		}
	}

	isAutoDeposit := true // Auto ฝาก นับตั้งแต่ได้รับยอดฝาก จนได้ยืนยันเครดิต
	if adminId != 0 {
		isAutoDeposit = false
	}

	// CREATE BANK TRANSACTION
	var createBankTransaction model.BankTransactionCreateBody
	createBankTransaction.MemberCode = user.MemberCode
	createBankTransaction.UserId = user.Id
	// createBankTransaction.StatementId = 0
	createBankTransaction.TransactionTypeId = model.TRANSACTION_TYPE_DEPOSIT
	// createBankTransaction.FromAccountName = "" // empty?
	createBankTransaction.ToAccountName = &pgAccount.TypeName
	createBankTransaction.ToAccountNumber = &pgAccount.ShopName
	createBankTransaction.PromotionId = &promotionWebUserId
	createBankTransaction.CreditAmount = depositAmount
	// createBankTransaction.BonusAmount = 0
	// createBankTransaction.BonusReason = nil
	createBankTransaction.DepositChannel = "ZMANPAY PAYMENT GATEWAY"
	createBankTransaction.TransactionStatusId = model.TRANS_STATUS_PENDING
	createBankTransaction.CreatedByAdminId = 0
	createBankTransaction.IsAutoCredit = isAutoDeposit
	createBankTransaction.TransferAt = &actionAt
	// create transaction
	transId, err := repo.InsertBankTransaction(createBankTransaction)
	if err != nil {
		log.Println("createCustomerDepositFromZmanpay.InsertBankTransaction", err)
		return nil, internalServerError(err)
	}

	// var actionStatement model.BankStatementMatchRequest
	// actionStatement.StatementId = *bankstatementId
	// actionStatement.UserId = user.Id
	// actionStatement.ConfirmedAt = time
	// actionStatement.ConfirmedByAdminId = &req.CreateByUserId
	// if err := s.serviceAccounting.SetStatementOwnerMatched(*bankstatementId, actionStatement, model.USE_ENDING_NOTI); err != nil {
	// 	log.Println("createCustomerDepositFromZmanpay.SetStatementOwnerMatched", err)
	// 	return nil, internalServerError(err)
	// }

	// DECREASE FASTBANK CREDIT 2.FROM create auto DEPOSIT
	// if err := repoAccounting.DecreaseFastbankCredit(1); err != nil {
	// 	log.Println("createCustomerDepositFromZmanpay.DECREASE_FASTBANK_CREDIT_ERROR", err)
	// }

	var confirmDeposit model.BankConfirmDepositRequest
	confirmDeposit.TransferAt = &actionAt
	confirmDeposit.ConfirmedAt = actionAt
	confirmDeposit.ConfirmedByAdminId = &adminId
	if err := confirmDepositTransaction(paygateRepo, *transId, confirmDeposit); err != nil {
		log.Println("createCustomerDepositFromZmanpay.confirmDepositTransaction", err)
		return nil, internalServerError(err)
	}

	// UPDATE HENG ORDER - BANK SUCCESS
	if err := repo.UpdateZmanpayOrderBankSucess(item.Id, *transId, adminId); err != nil {
		log.Println("createCustomerDepositFromZmanpay.UpdatePaygateOrderBankSucess", err)
	}

	isFirstDeposit := repo.IsFirstDeposit(user.Id)
	if isFirstDeposit {
		var bonusReq model.UserFirstDepositCreateRequest
		bonusReq.UserId = user.Id
		bonusReq.TransactionId = transId
		bonusReq.TransferAt = actionAt
		bonusReq.Amount = depositAmount
		bonusReq.Remark = "createCustomerDepositFromZmanpay"
		if err := SetFirstDepositBonus(accountingRepo, isFirstDeposit, bonusReq); err != nil {
			webhookLogMessage := fmt.Sprintf("createCustomerDepositFromZmanpay.userFirstDepositBonus, ERROR: %s", err)
			log.Println("SetFirstDepositBonus", webhookLogMessage)
			// return nil, errors.New(webhookLogMessage)
		}
	}

	// [USER_CREDIT]
	var userCreditReq model.UserTransactionCreateRequest
	userCreditReq.UserId = user.Id
	userCreditReq.RefId = transId
	userCreditReq.TransferAt = &actionAt
	userCreditReq.PromotionId = &promotionWebUserId
	userCreditReq.TypeId = model.CREDIT_TYPE_DEPOSIT
	// userCreditReq.AccountId = &bankAccount.Id
	userCreditReq.Detail = "ZMANPAY PAYMENT GATEWAY"
	userCreditReq.Amount = depositAmount
	userCreditReq.CreateBy = &adminId
	userCreditReq.ConfirmBy = &adminId
	userCreditReq.IsAdjustAuto = isAutoDeposit
	userCreditReq.PaymentMerchatId = &pgAccount.ProviderId
	if agentResp, err := repo.IncreaseUserCredit(userCreditReq); err != nil {
		log.Println("createCustomerDepositFromZmanpay.IncreaseUserCredit", err)
		return nil, internalServerError(err)
	} else {
		// AGENT_SUCCESS
		if err := repo.UpdateDepositTransactionStatusFromAgent(*transId, *agentResp); err != nil {
			log.Println("createCustomerDepositFromZmanpay.UpdateDeporsitTransactionStatusFromAgent", err)
		}
	}

	// [Lucky Wheel] ตอนฝากละได้โบนัส
	var luckyWheelBody model.ActivityLuckyWheelRoundUserRequest
	luckyWheelBody.UserId = user.Id
	luckyWheelBody.ConditionId = model.ACTIVITY_LUCKY_WHEEL_SETTING_CONDITION_DEPOSIT
	luckyWheelBody.ConditionAmount = depositAmount
	if err := CreateRoundActivityLuckyWheel(luckyWheelRepo, luckyWheelBody); err != nil {
		log.Println("createCustomerDepositFromZmanpay.CreateRoundActivityLuckyWheel", err)
	}

	// [TIER]
	if err := repo.IncreaseUserTierDepositAmount(user.Id, depositAmount); err != nil {
		log.Println("MatchStatementOwner.IncreaseUserTierDepositAmount", err)
	}

	var checkUserPromotionBody model.CheckUserPromotionBody
	checkUserPromotionBody.UserId = user.Id
	_, err = CheckUserPromotion(promotionWebRepo, checkUserPromotionBody)
	if err != nil {
		log.Println("createCustomerDepositFromZmanpay.CheckUserPromotion", err)
	}
	// [turnOver] ถ้ามีการฝากเงิน ให้สร้าง TurnOver
	turnoverShare := repository.NewTurnoverRepository(repo.GetDb())
	if err := CreateTurnOverFromSuccessDeposit(turnoverShare, user.Id, depositAmount, *transId); err != nil {
		log.Println("createCustomerDeposit.CreateTurnOverFromSuccessDeposit", err)
	}

	// ===================================================

	// [ notify]
	notiAtUtc := time.Now().UTC()
	externalNoti.TypeNotify = model.IsDepositAfterCredit
	externalNoti.TransId = transId
	externalNoti.Amount = depositAmount
	externalNoti.MemberCode = user.MemberCode
	externalNoti.UserCredit = user.Credit + depositAmount
	externalNoti.ConfirmedByAdminId = 0
	externalNoti.TransferDateTime = notiAtUtc.Add(7 * time.Hour).Format("2006-01-02 15:04:05")
	externalNoti.ActionTime = notiAtUtc.Add(7 * time.Hour).Format("2006-01-02 15:04:05")
	endTime := notiAtUtc
	elapsed := endTime.Sub(actionAt)
	elapsedSeconds := elapsed.Seconds()
	timeElapsed := fmt.Sprintf("%.2f", elapsedSeconds)
	externalNoti.TimerCounter = timeElapsed

	externalNoti.WebScoket.UserID = user.Id
	externalNoti.WebScoket.Amount = depositAmount
	externalNoti.WebScoket.MemberCode = user.MemberCode
	externalNoti.WebScoket.AlertType = "DEPOSIT"
	if err := repo.UpdateAutoProcessTimer(timeElapsed, *transId); err != nil {
		return nil, nil
	}
	if err := ExternalNotification(notiRepo, externalNoti); err != nil {
		log.Println("FailedNotify", err)
	}
	return nil, nil
}

func approveCustomerWithdrawFromZmanpay(repo repository.ZmanpayRepository, transId int64) (*int64, error) {

	withdrawTrans, err := repo.GetBankTransactionById(transId)
	if err != nil {
		log.Println("approveCustomerWithdrawFromZmanpay.GetUserBankDetailById", err)
		return nil, internalServerError(err)
	}

	// ============================= ON_SUCCESS =================================
	confirmAtUtc := time.Now().UTC()
	if withdrawTrans.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		// [create transaction action success]
		var createConfirm model.CreateSuccessTransferWithdrawRequest
		createConfirm.TransactionId = withdrawTrans.Id
		createConfirm.ConfirmedAt = confirmAtUtc
		var setIdAuto int64 = 0
		createConfirm.ConfirmedByAdminId = &setIdAuto
		if _, err := CreateSuccessTransferWithdraw(repository.NewBankingRepository(repo.GetDb()), createConfirm); err != nil {
			log.Println("approveCustomerWithdrawFromZmanpay.CreateSuccessTransferWithdraw", err)
			return nil, nil
		}
		// [Show Withdraw USER_TRANSACTION]
		var showUserTrans model.UserTransactionShowUpdate
		showUserTrans.TransactionId = withdrawTrans.Id
		showUserTrans.ConfirmedAt = &createConfirm.ConfirmedAt
		showUserTrans.ConfirmAdminId = createConfirm.ConfirmedByAdminId
		if err := repo.ShowUserTransaction(showUserTrans); err != nil {
			log.Println("approveCustomerWithdrawFromZmanpay.ShowUserTransaction", err)
			return nil, nil
		}
		// [update transaction status]
		secondUsed := "0.0" // timing
		if withdrawTrans.IsAutoCredit && withdrawTrans.TransferAt != nil {
			seconds := confirmAtUtc.Sub(withdrawTrans.TransferAt.UTC()).Seconds()
			secondUsed = fmt.Sprintf("%.2f", seconds)
		}
		if err := repo.UpdateTransactionStatusTransferingToSuccess(withdrawTrans.Id, secondUsed); err != nil {
			log.Println("approveCustomerWithdrawFromZmanpay.UpdateTransactionStatusTransferingToSuccess", err)
			return nil, internalServerError(err)
		}

		// [notify SUCCESS]
		var externalNoti model.NotifyExternalNotificationRequest
		notiRepo := repository.NewNotificationRepository(repo.GetDb())
		externalNoti.TypeNotify = model.IsWithdrawalCreditSuccess
		externalNoti.TransId = &transId
		externalNoti.Amount = withdrawTrans.CreditAmount
		externalNoti.MemberCode = withdrawTrans.MemberCode
		externalNoti.UserCredit = withdrawTrans.CreditAmount
		externalNoti.ConfirmedByAdminId = 0
		externalNoti.TimerCounter = secondUsed
		externalNoti.TransferDateTime = withdrawTrans.TransferAt.Add(7 * time.Hour).Format("2006-01-02 15:04:05")
		externalNoti.ActionTime = confirmAtUtc.Add(7 * time.Hour).Format("2006-01-02 15:04:05")
		if err := ExternalNotification(notiRepo, externalNoti); err != nil {
			log.Println("FailedNotify", err)
		}

	}
	return nil, nil
}

// func rollbackCustomerWithdrawFromZmanpay(repo repository.ZmanpayRepository, transId int64) (*int64, error) {

// 	withdrawTrans, err := repo.GetBankTransactionById(transId)
// 	if err != nil {
// 		log.Println("rollbackCustomerWithdrawFromZmanpay.GetBankTransactionById", err)
// 		return nil, internalServerError(err)
// 	}

// 	// ============================= ON_SUCCESS =================================
// 	if withdrawTrans.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_TRASNFERING {
// 		// [update transaction status]
// 		if err := repo.RollbackTransactionStatusTransferingToConfirmed(withdrawTrans.Id); err != nil {
// 			log.Println("approveCustomerWithdrawFromZmanpay.RollbackTransactionStatusTransferingToConfirmed", err)
// 			return nil, internalServerError(err)
// 		}
// 	}
// 	return nil, nil
// }

func (s paygateZmanpayService) CreateSystemLog(name string, req interface{}) error {

	// [SYSLOG] HACKED
	_, err := s.repo.CreateSystemLog(model.SystemLogCreateBody{
		Name:   name,
		Status: "HACKING",
		JsonRequest: helper.StructJson(map[string]interface{}{
			"req": req,
		}),
	})
	if err != nil {
		log.Println(name, "CreateSystemLog.ERROR", err.Error())
	}
	return nil
}

func (s paygateZmanpayService) CancelWithdrawFromZmanpay(transId int64, adminId int64) error {

	withdrawTrans, err := s.repo.GetBankTransactionById(transId)
	if err != nil {
		log.Println("rollbackWithdrawFromZmanpay.GetBankTransactionById", err)
		return internalServerError(err)
	}
	if withdrawTrans.TransactionStatusId != model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		return internalServerError(errors.New("ไม่อยู๋ในสถานะที่สามารถยกเลิกได้"))
	}

	user, err := s.repo.GetUserBankDetailById(withdrawTrans.UserId)
	if err != nil {
		return internalServerError(err)
	}

	paygateOrder, err := s.repo.GetDbZmanpayWithdrawOrderByRefId(transId)
	if err != nil {
		log.Println("CancelWithdrawFromZmanpay.GetDbZmanpayWithdrawOrderByRefId", err)
		return internalServerError(err)
	}
	// Same as Webhook
	// if successStatus == "REJECTED" || successStatus == "WITHDRAW_CANCELED" || successStatus == "ERROR" {
	if paygateOrder.TransactionStatus != "REJECTED" && paygateOrder.TransactionStatus != "WITHDRAW_CANCELED" && paygateOrder.TransactionStatus != "ERROR" {
		log.Println("CancelWithdrawFromZmanpay.paygateOrder.TransactionStatus=", paygateOrder.TransactionStatus)
		return internalServerError(errors.New("ไม่อยู๋ในสถานะที่สามารถยกเลิกได้"))
	}
	returnAmount := withdrawTrans.CreditAmount

	// race condition deposit
	actionAt := time.Now().UTC()
	var createBody model.RaceActionCreateBody
	createBody.Name = "ADMIN_CANCEL_WITHDRAW"
	createBody.JsonRequest = helper.StructJson(map[string]interface{}{
		"transId":      transId,
		"returnAmount": returnAmount,
	})
	createBody.Status = "PENDING"
	createBody.ActionKey = fmt.Sprintf("WITHDRAW_CANCEL_T%sU%dID%d", actionAt.Format("0601021504"), user.Id, transId)
	createBody.UnlockAt = actionAt.Add(time.Second * 1)
	if _, err := s.repo.GetRaceActionIdByActionKey(createBody.ActionKey); err != nil {
		log.Println("CancelWithdrawFromZmanpay.GetRaceActionIdByActionKey", err)
		if err != gorm.ErrRecordNotFound {
			return internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
		}
	} else {
		return internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
	}

	// create race condition
	actionId, err := s.repo.CreateRaceCondition(createBody)
	if err != nil {
		log.Println("CancelWithdrawFromZmanpay.ERROR.CreateRaceCondition", err)
		return internalServerError(errors.New("CANNOT_CREATE_ACTION_LUCKY_WHEEL_ROUND"))
	}
	if actionId == 0 {
		return internalServerError(errors.New("CANNOT_CREATE_ACTION_LUCKY_WHEEL_ROUND"))
	}

	// SET ORDER AS ERROR
	remark := "Admin Cancel Withdraw"
	if err := s.repo.UpdateDbZmanpayOrderError(paygateOrder.Id, remark); err != nil {
		log.Println("CreateZmanpayWithdraw.UpdateDbZmanpayOrderError", err)
	}

	if withdrawTrans.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		// [********] check user credit transaction ว่าตรงไหมกับเงื่อนไข (isShow = false)
		userTransaction, err := s.repo.GetUserWithdrawCreditTransactionByRefId(withdrawTrans.Id, returnAmount)
		if err != nil {
			if err.Error() == recordNotFound {
				log.Println("CancelWithdrawCredit.GetUserCreditTransactionById", err)
				return badRequest("ไม่ตรงกับเงือนไข")
			}
			return internalServerError(err)
		}
		if userTransaction != nil {
			var userCreditReq model.UserTransactionCreateRequest
			userCreditReq.UserId = user.Id
			userCreditReq.TransferAt = withdrawTrans.TransferAt
			userCreditReq.RefId = &withdrawTrans.Id
			userCreditReq.TypeId = model.CREDIT_TYPE_DEPOSIT
			userCreditReq.Amount = returnAmount
			IsShow := false
			userCreditReq.IsShow = &IsShow
			userCreditReq.ConfirmBy = &adminId
			if agentResp, err := s.repo.IncreaseUserCredit(userCreditReq); err != nil {
				log.Println("CancelWithdrawCredit.IncreaseUserCredit", err)
				return internalServerError(err)
			} else if !agentResp.AgentSuccess {
				// [********] error from agent will be in failed
				// if err := s.repoAccounting.UpdateDeporsitTransactionStatusFromAgent(*transId, *agentResp); err != nil {
				//     log.Println("CancelWithdrawCredit.UpdateDeporsitTransactionStatusFromAgent", err)
				// }
				// Set FastBank-READ ** ไม่มี เพราะเป็นการสร้างรายการฝากเอง
				agentFail := errors.New("ทำรายการเครดิตไม่สำเร็จ")
				log.Println("CancelWithdrawCredit.IncreaseUserCredit", agentFail)
				return internalServerError(agentFail)
			}
		}
	}

	// remark := "ADMIN_CANCEL_WITHDRAW"
	var updateApprovedBy model.UpdateUserTransactionStatusRequest
	updateApprovedBy.Id = withdrawTrans.Id
	updateApprovedBy.CanceledAt = actionAt
	updateApprovedBy.CanceledByAdminId = adminId
	updateApprovedBy.TransactionStatusId = model.TRANS_STATUS_WITHDRAW_CANCELED
	updateApprovedBy.CancelRemark = &remark
	err = s.repo.UpdateUserTransactionStatus(updateApprovedBy)
	if err != nil {
		return internalServerError(err)
	}

	var createConfirm model.CreateSuccessTransferWithdrawRequest
	createConfirm.TransactionId = withdrawTrans.Id
	createConfirm.ConfirmedAt = actionAt
	createConfirm.ConfirmedByAdminId = &adminId
	if _, err := CreateCanceledTransferWithdraw(repository.NewBankingRepository(s.sharedDb), createConfirm); err != nil {
		return err
	}

	return nil
}

func (s paygateZmanpayService) CreateZmanpayDepositWebhook(req model.ZmanpayWebhookRequest) (*int64, error) {

	var createBody model.ZmanpayWebhookCreateBody
	createBody.Name = "ZMANPAY_DEPOSIT_CALLBACK"
	createBody.JsonPayload = req.JsonPayload
	insertId, err := s.repo.CreateZmanpayWebhook(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// DEPOSIT

	var remoteResp model.ZmanpayDepositWebhookResponse
	errJson := json.Unmarshal([]byte(req.JsonPayload), &remoteResp)
	if errJson != nil {
		log.Println("Error unmarshal json", errJson)
		return nil, internalServerError(errJson)
	}

	if remoteResp.Uuid == "" || remoteResp.MerchantOrderId == "" {
		log.Println("CreateZmanpayDepositWebhook.NoRef", helper.StructJson(remoteResp))
		return nil, badRequest("ZMANPAY_DEPOSIT_HOOK_NOREF")
	}

	// Check Response Status.
	// Transaction status
	// สถานะต่างๆของธุรกรรมฝากเงิน (Deposit transaction) มีดังนี้
	// initial: สร้าง QR สำหรับโอนเงินสำเร็จ รอการฝากเงินจากลูกค้า
	// success: ธุรกรรมสำเร็จ ลูกค้าทำการฝากเงินเข้ามา
	// expired: ลูกค้าไม่ทำการโอนเงินเข้ามาภายในระยะเวลาที่กำหนด 15 นาที
	// rejected: รายการถูกปฎิเสธโดย Admin ของระบบ
	// deposit_canceled: ธุรกรรมถูกยกเลิก

	successStatus := strings.ToUpper(strings.TrimSpace(remoteResp.TransactionStatus))
	if successStatus == "SUCCESS" {
		successStatus = "SUCCESS" // Always use SUCCESS
	} else {
		// เก็บตามจริง successStatus
		log.Println("CreateZmanpayDepositWebhook.remoteResp.desc.ELSE", successStatus)
	}

	// Service Race Condition by Ref1(MchOrderNo) + perStatus
	acAtUtc := time.Now().UTC()
	var rcCreateBody model.RaceActionCreateBody
	rcCreateBody.Name = "CreateZmanpayDepositWebhook"
	rcCreateBody.JsonRequest = helper.StructJson(req)
	rcCreateBody.Status = "PENDING"
	// ActionKey = Ref2-MINUTE
	rcCreateBody.ActionKey = fmt.Sprintf("PG13D%s-%s-%s", acAtUtc.Format("0601021504"), remoteResp.Uuid, successStatus)
	rcCreateBody.UnlockAt = acAtUtc.Add(time.Second * 1)
	if _, err := s.repo.CreateRaceCondition(rcCreateBody); err != nil {
		return insertId, internalServerError(err)
	}

	// Get Posible Order
	var query model.ZmanpayOrderListRequest
	query.OrderNo = remoteResp.MerchantOrderId
	query.TransactionNo = remoteResp.Uuid
	// query.Amount = fmt.Sprintf("%f", remoteResp.Amount)
	query.Status = "WAIT_PAYMENT"
	query.Limit = 1
	list, _, err := s.repo.GetDbZmanpayOrderList(query)
	if err != nil {
		return nil, internalServerError(err)
	}

	// fmt.Println("ZmanpayDecryptRepayDespositPayload.list", helper.StructJson(list))

	// ** ไม่ได้เช็ค amount เพราะจะมีระบบที่ใช้ ทศนิยม และไม่เท่ากัน แต่ก็ควรเช็คแบบ +- 10%
	if len(list) > 0 {
		for _, item := range list {
			// Payment Success, Create Deposit if UserId is not null
			if item.UserId > 0 && item.OrderTypeId == model.ZMANPAY_ORDER_TYPE_DEPOSIT {
				if successStatus == "SUCCESS" {
					// Update Order
					if err := s.repo.ApproveDbZmanpayOrder(item.Id, successStatus); err != nil {
						return nil, internalServerError(err)
					}
					if _, err := CreateCustomerDepositFromZmanpayOrder(s.repo, item.Id, 0); err != nil {
						// WebhookLog
						var createBody2 model.ZmanpayWebhookCreateBody
						createBody2.Name = "ZMANPAY_DEPOSIT_ERROR"
						createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
							"item":   item,
							"method": "createCustomerDepositFromZmanpay",
							"error":  err.Error(),
						})
						if _, err = s.repo.CreateZmanpayWebhook(createBody2); err != nil {
							log.Println("Error CreateZmanpayWebhook.createCustomerDepositFromZmanpay", err)
						}
					}
				} else if successStatus == "EXPIRED" || successStatus == "REJECTED" || successStatus == "DEPOSIT_CANCELED" || successStatus == "ERROR" {
					// Update Order to EXPIRED
					if err := s.repo.UpdateDbZmanpayOrderError(item.Id, "Expired Deposit from webhook"); err != nil {
						log.Println("UpdateDbZmanpayOrderError", err)
						return nil, internalServerError(err)
					}
				}
			} else if item.UserId > 0 && item.OrderTypeId == model.ZMANPAY_ORDER_TYPE_WITHDRAW {
				if successStatus == "SUCCESS" {
					// Update Order
					if err := s.repo.ApproveDbZmanpayOrder(item.Id, successStatus); err != nil {
						return nil, internalServerError(err)
					}
					if _, err := approveCustomerWithdrawFromZmanpay(s.repo, *item.RefId); err != nil {
						// WebhookLog
						var createBody2 model.ZmanpayWebhookCreateBody
						createBody2.Name = "ZMANPAY_WITHDRAW_ERROR"
						createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
							"item":   item,
							"method": "approveCustomerWithdrawFromZmanpay",
							"error":  err.Error(),
						})
						if _, err = s.repo.CreateZmanpayWebhook(createBody2); err != nil {
							log.Println("Error approveCustomerWithdrawFromZmanpay.CreateZmanpayWebhook", err)
						}
					}
				} else if successStatus == "REJECTED" || successStatus == "WITHDRAW_CANCELED" || successStatus == "ERROR" {
					// Update Order
					if err := s.repo.ApproveDbZmanpayOrder(item.Id, successStatus); err != nil {
						return nil, internalServerError(err)
					}
					if err := s.cancelWithdrawFromZmanpayWebhookError(item); err != nil {
						log.Println("Error UpdateDbZmanpayOrderError", err)
					}
				}
			} else {
				log.Println("ApproveDbZmanpayOrder.WithoutItemUpdated", helper.StructJson(item))
			}
		}
	}
	return insertId, nil
}

func (s paygateZmanpayService) CreateZmanpayWithdrawWebhook(req model.ZmanpayWebhookRequest) (*int64, error) {

	var createBody model.ZmanpayWebhookCreateBody
	createBody.Name = "ZMANPAY_WITHDRAW_CALLBACK"
	createBody.JsonPayload = req.JsonPayload
	insertId, err := s.repo.CreateZmanpayWebhook(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// WITHDRAW

	var remoteResp model.ZmanpayWithdrawWebhookResponse
	errJson := json.Unmarshal([]byte(req.JsonPayload), &remoteResp)
	if errJson != nil {
		log.Println("Error unmarshal json", errJson)
		return nil, internalServerError(errJson)
	}

	// Check Response Status.
	// Transaction status
	// สถานะต่างๆของธุรกรรมถอนเงิน (Withdraw transaction) มีดังนี้
	// initial: สร้างธุรกรรมถอนเงินสำเร็จ
	// success: ธุรกรรมสำเร็จ ระบบทำการถอนเงินให้ลูกค้าเสร็จสิ้น
	// rejected: รายการถูกปฎิเสธโดย Admin ของระบบ
	// withdraw_start: รายการถอน เริ่มต้น
	// withdraw_queued: รายการถอน กำลังต่อคิวเพื่อถอน
	// withdraw_pending: รายการถอน อยู่ระหว่างการพิจารณา รอแอดมินอนุมัติ ส่วนมากจะเกิดกับรายการถอนจำนวนมากๆ
	// withdraw_canceled: ธุรกรรมถูกยกเลิก

	successStatus := strings.ToUpper(strings.TrimSpace(remoteResp.TransactionStatus))
	if successStatus == "SUCCESS" {
		successStatus = "SUCCESS" // Always use SUCCESS
	} else {
		log.Println("CreateZmanpayWithdrawWebhook.remoteResp.desc.ELSE", successStatus, remoteResp.TransactionStatus)
		// เก็บตามจริง successStatus = "ERROR"
	}

	// Service Race Condition by Ref1(MchOrderNo)
	acAtUtc := time.Now().UTC()
	var rcCreateBody model.RaceActionCreateBody
	rcCreateBody.Name = "CreateZmanpayWithdrawWebhook"
	rcCreateBody.JsonRequest = helper.StructJson(req)
	rcCreateBody.Status = "PENDING"
	// ActionKey = Ref2-MINUTE
	rcCreateBody.ActionKey = fmt.Sprintf("PG13W%s-%s-%s", acAtUtc.Format("0601021504"), remoteResp.Uuid, successStatus)
	rcCreateBody.UnlockAt = acAtUtc.Add(time.Second * 1)
	if _, err := s.repo.CreateRaceCondition(rcCreateBody); err != nil {
		return insertId, internalServerError(err)
	}

	// Get Posible Order
	var query model.ZmanpayOrderListRequest
	query.OrderNo = remoteResp.MerchantOrderId
	query.TransactionNo = remoteResp.Uuid
	// query.Amount = fmt.Sprintf("%f", remoteResp.Amount)
	query.Status = "WAIT_PAYMENT"
	query.Limit = 1
	list, _, err := s.repo.GetDbZmanpayOrderList(query)
	if err != nil {
		return nil, internalServerError(err)
	}

	// ** ไม่ได้เช็ค amount เพราะจะมีระบบที่ใช้ ทศนิยม และไม่เท่ากัน แต่ก็ควรเช็คแบบ +- 10%

	if len(list) > 0 {
		for _, item := range list {

			// Payment Success, Create Deposit if UserId is not null
			if item.UserId > 0 && item.OrderTypeId == model.ZMANPAY_ORDER_TYPE_DEPOSIT {
				if successStatus == "SUCCESS" {
					// Update Order
					if err := s.repo.ApproveDbZmanpayOrder(item.Id, successStatus); err != nil {
						return nil, internalServerError(err)
					}
					if _, err := CreateCustomerDepositFromZmanpayOrder(s.repo, item.Id, 0); err != nil {
						// WebhookLog
						var createBody2 model.ZmanpayWebhookCreateBody
						createBody2.Name = "ZMANPAY_DEPOSIT_ERROR"
						createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
							"item":   item,
							"method": "createCustomerDepositFromZmanpay",
							"error":  err.Error(),
						})
						if _, err = s.repo.CreateZmanpayWebhook(createBody2); err != nil {
							log.Println("Error CreateZmanpayWebhook.createCustomerDepositFromZmanpay", err)
						}
					}
				}
			} else if item.UserId > 0 && item.OrderTypeId == model.ZMANPAY_ORDER_TYPE_WITHDRAW {
				if successStatus == "SUCCESS" {
					// Update Order
					if err := s.repo.ApproveDbZmanpayOrder(item.Id, successStatus); err != nil {
						return nil, internalServerError(err)
					}
					if _, err := approveCustomerWithdrawFromZmanpay(s.repo, *item.RefId); err != nil {
						// WebhookLog
						var createBody2 model.ZmanpayWebhookCreateBody
						createBody2.Name = "ZMANPAY_WITHDRAW_ERROR"
						createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
							"item":   item,
							"method": "approveCustomerWithdrawFromZmanpay",
							"error":  err.Error(),
						})
						if _, err = s.repo.CreateZmanpayWebhook(createBody2); err != nil {
							log.Println("Error approveCustomerWithdrawFromZmanpay.CreateZmanpayWebhook", err)
						}
					}
				} else if successStatus == "REJECTED" || successStatus == "WITHDRAW_CANCELED" || successStatus == "ERROR" {
					// Update Order
					if err := s.repo.ApproveDbZmanpayOrder(item.Id, successStatus); err != nil {
						return nil, internalServerError(err)
					}
					if err := s.cancelWithdrawFromZmanpayWebhookError(item); err != nil {
						log.Println("Error UpdateDbZmanpayOrderError", err)
					}
				}
			} else {
				log.Println("ApproveDbZmanpayOrder.WithoutItemUpdated", helper.StructJson(item))
			}
		}
	}
	return insertId, nil
}

func (s paygateZmanpayService) GetPendingZmanpayDepositOrder(userId int64) (*model.ZmanpayOrderWebResponse, error) {

	var result model.ZmanpayOrderWebResponse

	waitPayOrder, err := s.repo.GetLastestZmanpayDepositOrderByUserId(userId)
	if err != nil {
		if err.Error() == recordNotFound {
			// return Empty id=0 record.
			return &result, nil
		}
		log.Println("GetPendingZmanpayDepositOrder.GetPendingZmanpayDepositOrderByUserId", err)
		return nil, internalServerError(err)
	}
	if waitPayOrder.Id != 0 && waitPayOrder.TransactionStatus != "WAIT_PAYMENT" {
		return &result, nil
	}

	pgAccount, err := s.GetZmanpayAccount()
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}

	remoteRecord, err := s.repo.ZmanpayGetOrder(*pgAccount, waitPayOrder.TransactionNo)
	if err != nil {
		log.Println("GetPendingZmanpayDepositOrder.ZmanpayGetOrder.ERROR", err)
		return nil, internalServerError(err)
	}
	// fmt.Println("ZmanpayGetOrder", helper.StructJson(remoteRecord))

	// สถานะต่างๆของธุรกรรมฝากเงิน (Deposit transaction) มีดังนี้
	// initial: สร้าง QR สำหรับโอนเงินสำเร็จ รอการฝากเงินจากลูกค้า
	// success: ธุรกรรมสำเร็จ ลูกค้าทำการฝากเงินเข้ามา
	// expired: ลูกค้าไม่ทำการโอนเงินเข้ามาภายในระยะเวลาที่กำหนด 15 นาที
	// rejected: รายการถูกปฎิเสธโดย Admin ของระบบ
	// deposit_canceled: ธุรกรรมถูกยกเลิก
	if remoteRecord.Data.Status != "initial" {
		return &result, nil
	}

	// ===========================================================================================
	// waitPayOrder, err := s.repo.GetDbZmanpayOrderById(*insertId)
	// if err != nil {
	// 	return nil, internalServerError(err)
	// }
	result.OrderId = waitPayOrder.Id
	result.UserId = waitPayOrder.UserId
	result.OrderNo = waitPayOrder.OrderNo
	result.Amount = waitPayOrder.Amount
	result.TransferAmount = waitPayOrder.TransferAmount
	result.TransactionStatus = waitPayOrder.TransactionStatus
	// result.QrCode = waitPayOrder.QrPromptpay
	// result.QrUrl = waitPayOrder.QrPromptpay
	result.QrBase64 = waitPayOrder.QrBase64
	result.ExtraPromptpayId = waitPayOrder.ExtraPromptpayId
	result.CreatedAt = waitPayOrder.CreatedAt

	return &result, nil
}

func (s paygateZmanpayService) CancelZmanpayDeposit(req model.ZmanpayDepositCancelRequest) error {

	waitPayOrder, err := s.repo.GetPendingZmanpayDepositOrder(req.OrderId)
	if err != nil {
		log.Println("GetPendingZmanpayDepositOrder.GetPendingZmanpayDepositOrder", err)
		return badRequest("ไม่พบรายการที่ต้องการยกเลิก")
	}

	if err := s.repo.UpdateDbZmanpayOrderError(waitPayOrder.Id, "User Cancel Deposit"); err != nil {
		log.Println("UpdateDbZmanpayOrderError", err)
		return internalServerError(err)
	}

	pgAccount, err := s.GetZmanpayAccount()
	if err != nil || pgAccount == nil {
		return badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}

	if _, err := s.repo.ZmanpayCancelDeposit(*pgAccount, waitPayOrder.TransactionNo); err != nil {
		log.Println("ZmanpayCancelDeposit", err)
		return internalServerError(err)
	}

	return nil
}

func (s paygateZmanpayService) cancelWithdrawFromZmanpayWebhookError(pgOrder model.ZmanpayOrderResponse) error {

	adminId := int64(1)

	if pgOrder.RefId == nil {
		log.Println("cancelWithdrawFromZmanpayWebhookError.pgOrder.RefId is nil")
		return badRequest("ไม่พบรายการที่ต้องการยกเลิก")
	}

	withdrawTrans, err := s.repo.GetBankTransactionById(*pgOrder.RefId)
	if err != nil {
		log.Println("rollbackWithdrawFromZmanpay.GetBankTransactionById", err)
		return internalServerError(err)
	}
	if withdrawTrans.TransactionStatusId != model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		return internalServerError(errors.New("ไม่อยู๋ในสถานะที่สามารถยกเลิกได้"))
	}

	user, err := s.repo.GetUserBankDetailById(withdrawTrans.UserId)
	if err != nil {
		return internalServerError(err)
	}

	paygateOrder, err := s.repo.GetDbZmanpayWithdrawOrderByRefId(withdrawTrans.Id)
	if err != nil {
		log.Println("CancelWithdrawFromZmanpay.GetDbZmanpayWithdrawOrderByRefId", err)
		return internalServerError(err)
	}
	// Same as Webhook
	// if successStatus == "REJECTED" || successStatus == "WITHDRAW_CANCELED" || successStatus == "ERROR" {
	if paygateOrder.TransactionStatus != "REJECTED" && paygateOrder.TransactionStatus != "WITHDRAW_CANCELED" && paygateOrder.TransactionStatus != "ERROR" {
		log.Println("CancelWithdrawFromZmanpay.paygateOrder.TransactionStatus=", paygateOrder.TransactionStatus)
		return internalServerError(errors.New("ไม่อยู๋ในสถานะที่สามารถยกเลิกได้"))
	}
	returnAmount := withdrawTrans.CreditAmount

	// race condition WITHDRAW_CANCEL
	actionAt := time.Now().UTC()
	var createBody model.RaceActionCreateBody
	createBody.Name = "ADMIN_CANCEL_WITHDRAW"
	createBody.JsonRequest = helper.StructJson(map[string]interface{}{
		"transId":      withdrawTrans.Id,
		"returnAmount": returnAmount,
	})
	createBody.Status = "PENDING"
	createBody.ActionKey = fmt.Sprintf("WITHDRAW_CANCEL_T%sU%dID%d", actionAt.Format("0601021504"), user.Id, withdrawTrans.Id)
	createBody.UnlockAt = actionAt.Add(time.Second * 1)
	if _, err := s.repo.GetRaceActionIdByActionKey(createBody.ActionKey); err != nil {
		log.Println("CancelWithdrawFromZmanpay.GetRaceActionIdByActionKey", err)
		if err != gorm.ErrRecordNotFound {
			return internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
		}
	} else {
		return internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
	}

	// create race condition
	actionId, err := s.repo.CreateRaceCondition(createBody)
	if err != nil {
		log.Println("CancelWithdrawFromZmanpay.ERROR.CreateRaceCondition", err)
		return internalServerError(errors.New("CANNOT_CREATE_ACTION_LUCKY_WHEEL_ROUND"))
	}
	if actionId == 0 {
		return internalServerError(errors.New("CANNOT_CREATE_ACTION_LUCKY_WHEEL_ROUND"))
	}

	// SET ORDER AS ERROR
	remark := "Payment Cancel Withdraw"
	if err := s.repo.UpdateDbZmanpayOrderError(paygateOrder.Id, remark); err != nil {
		log.Println("CreateZmanpayWithdraw.UpdateDbZmanpayOrderError", err)
	}

	if withdrawTrans.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		// [********] check user credit transaction ว่าตรงไหมกับเงื่อนไข (isShow = false)
		userTransaction, err := s.repo.GetUserWithdrawCreditTransactionByRefId(withdrawTrans.Id, returnAmount)
		if err != nil {
			if err.Error() == recordNotFound {
				log.Println("CancelWithdrawCredit.GetUserCreditTransactionById", err)
				return badRequest("ไม่ตรงกับเงือนไข")
			}
			return internalServerError(err)
		}
		if userTransaction != nil {
			var userCreditReq model.UserTransactionCreateRequest
			userCreditReq.UserId = user.Id
			userCreditReq.TransferAt = withdrawTrans.TransferAt
			userCreditReq.RefId = &withdrawTrans.Id
			userCreditReq.TypeId = model.CREDIT_TYPE_DEPOSIT
			userCreditReq.Amount = returnAmount
			IsShow := false
			userCreditReq.IsShow = &IsShow
			userCreditReq.ConfirmBy = &adminId
			if agentResp, err := s.repo.IncreaseUserCredit(userCreditReq); err != nil {
				log.Println("CancelWithdrawCredit.IncreaseUserCredit", err)
				return internalServerError(err)
			} else if !agentResp.AgentSuccess {
				// [********] error from agent will be in failed
				// if err := s.repoAccounting.UpdateDeporsitTransactionStatusFromAgent(*transId, *agentResp); err != nil {
				//     log.Println("CancelWithdrawCredit.UpdateDeporsitTransactionStatusFromAgent", err)
				// }
				// Set FastBank-READ ** ไม่มี เพราะเป็นการสร้างรายการฝากเอง
				agentFail := errors.New("ทำรายการเครดิตไม่สำเร็จ")
				log.Println("CancelWithdrawCredit.IncreaseUserCredit", agentFail)
				return internalServerError(agentFail)
			}
		}
	}

	// remark := "ADMIN_CANCEL_WITHDRAW"
	var updateApprovedBy model.UpdateUserTransactionStatusRequest
	updateApprovedBy.Id = withdrawTrans.Id
	updateApprovedBy.CanceledAt = actionAt
	updateApprovedBy.CanceledByAdminId = adminId
	updateApprovedBy.TransactionStatusId = model.TRANS_STATUS_WITHDRAW_CANCELED
	updateApprovedBy.CancelRemark = &remark
	err = s.repo.UpdateUserTransactionStatus(updateApprovedBy)
	if err != nil {
		return internalServerError(err)
	}

	var createConfirm model.CreateSuccessTransferWithdrawRequest
	createConfirm.TransactionId = withdrawTrans.Id
	createConfirm.ConfirmedAt = actionAt
	createConfirm.ConfirmedByAdminId = &adminId
	if _, err := CreateCanceledTransferWithdraw(repository.NewBankingRepository(s.sharedDb), createConfirm); err != nil {
		return err
	}

	return nil
}
