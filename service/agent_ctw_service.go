package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"fmt"
	"log"
	"math"
	"os"

	"gorm.io/gorm"
)

type AgentCtwService interface {
	// admin
	GetAgentCtwSetting() (*model.GetInternalAgentCtwSettingResponse, error)
	UpdateAgentCtwSetting(body model.UpdateAgentCtwSetting) error
	GetAgentCtwGameList() (*model.GetCtwGameListResponse, error)

	// web
	WebGetAgentCtwGameList(req model.WebGetAgentCtwGameList) ([]model.WebOurJsonGetAgentCtwGameListResponse, error)
	CallApiAgentCtwLaunch(req model.CallApiAgentCtwLaunch) (*model.PlayAgentCtwLaunchResponse, error)

	// call back
	CallBackAgentCtwCheckBalance(reqBody model.CallBackAgentCtwCheckBalanceRequest) (*model.CallBackAgentCtwCheckBalanceResponse, error)
	CallBackCtwTranferInOut(reqBody model.CallBackCtwTranferInOutRequest) (*model.CallBackCtwTranferInOutResponse, error)
	GetCtwBetDetail(reqBody model.CallBackCtwTranferInOutRequest) (*model.CallBackCtwGameBetDetail, error)
	CallBackCtwTranferInOutV2(reqBody model.CallBackCtwTranferInOutRequest) (*model.CallBackCtwTranferInOutResponse, error)
}

type agentCtwService struct {
	repo        repository.AgentCtwRepository
	sharedDb    *gorm.DB
	serviceGame GameService
}

func NewAgentCtwService(
	repo repository.AgentCtwRepository,
	sharedDb *gorm.DB,
	serviceGame GameService,
) AgentCtwService {
	return &agentCtwService{repo, sharedDb, serviceGame}
}

func (s agentCtwService) GetAgentCtwSetting() (*model.GetInternalAgentCtwSettingResponse, error) {

	s.serviceGame.ClearGameCache()
	s.repo.GetInternalAgentCtwSetting()

	getInternalAgentCtwSetting, err := s.repo.GetInternalAgentCtwSetting()
	if err != nil {
		return nil, err
	}

	if getInternalAgentCtwSetting.ProgramAllowUse == "NOT_ALLOW_USE" {
		// badRequest("กรุณาติดต่อเจ้าหน้าที่ ให้ set ระบบ")
		return nil, badRequest("NOT_ALLOW_USE_AGENT_CTW_PLEASE_CONTACT_ADMIN")
	}

	var response model.GetInternalAgentCtwSettingResponse
	response.Id = getInternalAgentCtwSetting.Id
	response.IsActive = getInternalAgentCtwSetting.IsActive
	response.CtwAppId = getInternalAgentCtwSetting.CtwAppId
	response.CtwAppPrivate = getInternalAgentCtwSetting.CtwAppPrivate
	return &response, nil
}

func (s agentCtwService) UpdateAgentCtwSetting(body model.UpdateAgentCtwSetting) error {

	s.serviceGame.ClearGameCache()
	s.repo.ClearCacheAgentCtwSetting()

	// p.lay confirm But dev-web for testing will not work
	body.CtwHrefBackUrl = "https://" + os.Getenv("DOMAIN_NAME")
	return s.repo.UpdateAgentCtwSetting(body)
}

func (s agentCtwService) GetAgentCtwGameList() (*model.GetCtwGameListResponse, error) {

	s.repo.ClearCacheAgentCtwSetting()
	setting, err := s.repo.GetInternalAgentCtwSetting()
	if err != nil {
		return nil, err
	}

	urlDetail := model.CallApiAgentCtwDetail{
		CtwAppId:      setting.CtwAppId,
		CtwAppPrivate: setting.CtwAppPrivate,
		CtwUrl:        setting.CtwUrl,
	}

	return s.repo.GetAgentCtwGameList(urlDetail)
}

func (s agentCtwService) WebGetAgentCtwGameList(req model.WebGetAgentCtwGameList) ([]model.WebOurJsonGetAgentCtwGameListResponse, error) {

	setting, err := s.repo.GetInternalAgentCtwSetting()
	if err != nil {
		return nil, err
	}

	urlDetail := model.CallApiAgentCtwDetail{
		CtwAppId:      setting.CtwAppId,
		CtwAppPrivate: setting.CtwAppPrivate,
		CtwUrl:        setting.CtwUrl,
	}

	if setting.ProgramAllowUse != "ALLOW_USE" {
		return nil, badRequest("NOT_ALLOW_USE_AGENT_CTW_PLEASE_CONTACT_ADMIN")
	}

	if setting.CtwAppId == "" || setting.CtwAppPrivate == "" {
		return nil, badRequest("AGENT_CTW_SETTING_NOT_FOUND")
	}

	response, err := s.repo.GetAgentCtwGameList(urlDetail)
	if err != nil {
		return nil, err
	}

	var result []model.WebOurJsonGetAgentCtwGameListResponse
	// ของ ค่ายเกมส่ง json มาตัวเล็ก ตัวใหญ่
	// type WebGetAgentCtwGameListResponse struct {
	// 	GameCode   string `json:"gameCode"`
	// 	Name       string `json:"Name"`
	// 	Type       int    `json:"type"`
	// 	IconUrl    string `json:"IconUrl"`
	// 	WebIconUrl string `json:"WebIconUrl"`
	// 	Platform   string `json:"Platform"`
	// }

	// ของเราเป็น ตัวเล็กทั้งหมด
	// type WebOurJsonGetAgentCtwGameListResponse struct {
	// 	GameCode   string `json:"gameCode"`
	// 	Name       string `json:"Name"`
	// 	Type       int    `json:"type"`
	// 	IconUrl    string `json:"iconUrl"`
	// 	WebIconUrl string `json:"webIconUrl"`
	// 	Platform   string `json:"platform"`
	// }

	for _, v := range response.Data.List {
		// req.VendorCode == "" || กัน front ไม่ส่ง vendorCode มา ไม่งั้นจะได้ รวม จะผิด
		include :=
			(req.VendorCode == "PGSOFT" && v.Platform == "pg") ||
				(req.VendorCode == "MX" && v.Platform == "pragmatic") ||
				(req.VendorCode == "JiliGames" && v.Platform == "jili")

		if include {
			result = append(result, model.WebOurJsonGetAgentCtwGameListResponse{
				GameCode:   v.ID,
				Name:       v.Name,
				Type:       v.Type,
				IconUrl:    v.IconUrl,
				WebIconUrl: v.WebIconUrl,
				Platform:   v.Platform,
			})
		}
	}

	return result, nil
}

func (s agentCtwService) CallApiAgentCtwLaunch(req model.CallApiAgentCtwLaunch) (*model.PlayAgentCtwLaunchResponse, error) {

	getMemberCode, err := s.repo.GetMemberCode(req.UserId)
	if err != nil {
		return nil, err
	}

	setting, err := s.repo.GetInternalAgentCtwSetting()
	if err != nil {
		return nil, err
	}

	if setting.ProgramAllowUse != "ALLOW_USE" {
		return nil, badRequest("NOT_ALLOW_USE_AGENT_CTW_PLEASE_CONTACT_ADMIN")
	}

	if setting.CtwAppId == "" || setting.CtwAppPrivate == "" {
		return nil, badRequest("AGENT_CTW_SETTING_NOT_FOUND")
	}

	reqBodyApiGame := model.CallApiAgentCtwLaunchBody{
		UserID: *getMemberCode,
		GameID: req.GameCode,
	}

	urlDetail := model.CallApiAgentCtwDetail{
		CtwAppId:      setting.CtwAppId,
		CtwAppPrivate: setting.CtwAppPrivate,
		CtwUrl:        setting.CtwUrl,
	}

	getLaunch, err := s.repo.CallApiAgentCtwLaunch(urlDetail, reqBodyApiGame)
	if err != nil {
		return nil, err
	}

	// แปลง ไม่ได้ ยังไม่เห็น error
	if getLaunch.Code != 0 {
		fmt.Println("CallApiAgentCtwLaunch.ERROR", helper.StructJson(getLaunch))
		return nil, badRequest("AGENT_CTW_LAUNCH_ERROR")
	}

	var response model.PlayAgentCtwLaunchResponse
	response.GameUrl = getLaunch.Data.Url

	return &response, nil
}

func (s agentCtwService) CallBackAgentCtwCheckBalance(reqBody model.CallBackAgentCtwCheckBalanceRequest) (*model.CallBackAgentCtwCheckBalanceResponse, error) {
	fmt.Println("CallBackAgentCtwCheckBalance", helper.StructJson(reqBody))

	getInternalAgentCtwSetting, err := s.repo.GetInternalAgentCtwSetting()
	if err != nil {
		return nil, err
	}

	if getInternalAgentCtwSetting.ProgramAllowUse != "ALLOW_USE" {
		return nil, badRequest("NOT_ALLOW_USE_AGENT_CTW_PLEASE_CONTACT_ADMIN")
	}

	if getInternalAgentCtwSetting.CtwAppId == "" || getInternalAgentCtwSetting.CtwAppPrivate == "" {
		return nil, badRequest("AGENT_CTW_SETTING_NOT_FOUND")
	}

	if reqBody.AppID != getInternalAgentCtwSetting.CtwAppId || reqBody.AppSecret != getInternalAgentCtwSetting.CtwAppPrivate {
		return nil, badRequest("AGENT_CTW_SETTING_NOT_MATCH")
	}

	// membercode = userid
	getUserMemberCode, err := s.repo.GetUserByMemberCode(reqBody.UserID)
	if err != nil {
		return nil, err
	}
	getUserDetail, err := GetUser(repository.NewUserRepository(s.sharedDb), getUserMemberCode.Id)
	if err != nil {
		return nil, err
	}

	var response model.CallBackAgentCtwCheckBalanceResponse
	response.Code = 0
	response.Error = ""
	response.Data.Balance = getUserDetail.Credit

	return &response, nil
}

func (s agentCtwService) CallBackCtwTranferInOut(reqBody model.CallBackCtwTranferInOutRequest) (*model.CallBackCtwTranferInOutResponse, error) {
	// {
	// 	"AppID": "ABCDE",
	// 	"AppSecret": "2de5c9c3-76a2-428a-aba0-XXXXXXXXXXXX",
	// 	"UserID": "1",
	// 	"TransactionID": "667f70ecc2a54a93fc4c2695667f70ec68f376bf8c282a55",
	// 	"Amount": -100,
	// 	"RoundID": "667f70ecc2a54a93fc4c2695667f70ec68f376bf8c282a55",
	// 	"GameID": "XingYunXiang",
	// 	"ReqTime": "2024-06-29T09:26:52.035906101+07:00",
	// 	"Reason": "bet"
	//   }

	getInternalAgentCtwSetting, err := s.repo.GetInternalAgentCtwSetting()
	if err != nil {
		return nil, err
	}

	if getInternalAgentCtwSetting.ProgramAllowUse != "ALLOW_USE" {
		return nil, badRequest("NOT_ALLOW_USE_AGENT_CTW_PLEASE_CONTACT_ADMIN")
	}

	if getInternalAgentCtwSetting.CtwAppId == "" || getInternalAgentCtwSetting.CtwAppPrivate == "" {
		return nil, badRequest("AGENT_CTW_SETTING_NOT_FOUND")
	}

	if reqBody.AppID != getInternalAgentCtwSetting.CtwAppId || reqBody.AppSecret != getInternalAgentCtwSetting.CtwAppPrivate {
		return nil, badRequest("AGENT_CTW_SETTING_NOT_MATCH")
	}

	getUserMemberCode, err := s.repo.GetUserByMemberCode(reqBody.UserID)
	if err != nil {
		// 404 Error: ในกรณีที่ไม่มีข้อมูล User (User Not Found)
		return nil, badRequest("USER_NOT_FOUND")
	}

	getUserDetail, err := GetUser(repository.NewUserRepository(s.sharedDb), getUserMemberCode.Id)
	if err != nil {
		return nil, err
	}

	// already try get bet but can't "code":20001 need to response this func first
	// var getBetDetail model.CallBackCtwGameBetDetailRequest
	// getBetDetail.RoundId = reqBody.RoundID
	// urlDetail := model.CallApiAgentCtwDetail{
	// 	CtwAppId:      getInternalAgentCtwSetting.CtwAppId,
	// 	CtwAppPrivate: getInternalAgentCtwSetting.CtwAppPrivate,
	// 	CtwUrl:        getInternalAgentCtwSetting.CtwUrl,
	// }
	// getBetDetailResponse, err := s.repo.CallApiAgentCtwBetDetail(urlDetail, getBetDetail)
	// if err != nil {
	// 	return nil, err
	// }
	// fmt.Println("getBetDetailResponse ROUND ID", getBetDetailResponse.Data.RoundId, helper.StructJson(getBetDetailResponse))

	// create transacion
	var createAgentCtwCallbackBody model.CreateAgentCtwCallback
	createAgentCtwCallbackBody.UserId = getUserMemberCode.Id
	createAgentCtwCallbackBody.MemberCode = reqBody.UserID
	// createAgentCtwCallbackBody.Payoff = 0
	// createAgentCtwCallbackBody.BetAmount = 0
	// createAgentCtwCallbackBody.WinloseAmount = 0
	createAgentCtwCallbackBody.Balance = getUserDetail.Credit
	createAgentCtwCallbackBody.BeforeBalance = getUserDetail.Credit
	createAgentCtwCallbackBody.AfterBalance = getUserDetail.Credit
	createAgentCtwCallbackBody.TransactionId = reqBody.TransactionID
	createAgentCtwCallbackBody.RoundId = reqBody.RoundID
	createAgentCtwCallbackBody.GameId = reqBody.GameID
	createAgentCtwCallbackBody.CallbackReason = reqBody.Reason
	createAgentCtwCallbackBody.Remark = "CREATE"
	createAgentCtwCallbackBody.IsSuccess = false
	if err := s.repo.CreateOrUpdateAgentCtwCallback(createAgentCtwCallbackBody); err != nil {
		return nil, err
	}

	var updateAgentCtwCallbackBody model.UpdateAgentCtwCallback
	updateAgentCtwCallbackBody.MemberCode = reqBody.UserID
	updateAgentCtwCallbackBody.RoundId = reqBody.RoundID

	playedAmount := reqBody.Amount
	playedAmount = math.Round(playedAmount*100) / 100
	var currnecntCredit *model.UserTransactionCreateResponse
	// beforeAmount := getUserDetail.Credit
	afterAmount := getUserDetail.Credit
	if playedAmount > 0 {
		var increaseUserCredit model.IncreaseUserCreditFromOtherAgentRequest
		increaseUserCredit.UserId = getUserMemberCode.Id
		increaseUserCredit.Amount = playedAmount
		currnecntCredit, err = s.repo.IncreaseUserCreditFromOtherAgent(increaseUserCredit)
		if err != nil {
			logerror := fmt.Sprintf("IncreaseUserCreditFromOtherAgent Error: %v", err)
			updateAgentCtwCallbackBody.Remark = &logerror
			if err := s.repo.UpdateAgentCtwCallback(updateAgentCtwCallbackBody); err != nil {
				log.Println(err)
			}
			return nil, err
		}
		// beforeAmount = currnecntCredit.AgentBeforeAmount
		afterAmount = currnecntCredit.AgentAfterAmount
	} else if playedAmount < 0 {
		var decreaseUserCredit model.DecreaseUserCreditFromOtherAgentRequest
		decreaseUserCredit.UserId = getUserMemberCode.Id
		decreaseUserCredit.Amount = -playedAmount // ต้องเป็นบวก
		currnecntCredit, err = s.repo.DecreaseUserCreditFromOtherAgent(decreaseUserCredit)
		if err != nil {
			logerror := fmt.Sprintf("DecreaseUserCreditFromOtherAgent Error: %v", err)
			updateAgentCtwCallbackBody.Remark = &logerror
			if err := s.repo.UpdateAgentCtwCallback(updateAgentCtwCallbackBody); err != nil {
				log.Println(err)
			}
			return nil, err
		}
		// beforeAmount = currnecntCredit.AgentBeforeAmount
		afterAmount = currnecntCredit.AgentAfterAmount
	}

	// update callback
	endOfAmountCallBack := "WAITING_WIN_LOSE"
	updateAgentCtwCallbackBody.Remark = &endOfAmountCallBack
	// !!!!!!!! ใช้่ before ไม่ได้ เพราะ stack เป็น round !!!!!!!
	// updateAgentCtwCallbackBody.BeforeBalance = &beforeAmount
	updateAgentCtwCallbackBody.AfterBalance = &afterAmount
	if err := s.repo.UpdateAgentCtwCallback(updateAgentCtwCallbackBody); err != nil {
		log.Println(err)
	}

	var response model.CallBackCtwTranferInOutResponse
	response.Code = 0
	response.Error = ""
	response.Data.Balance = afterAmount

	return &response, nil
}

func (s agentCtwService) CallBackCtwTranferInOutV2(reqBody model.CallBackCtwTranferInOutRequest) (*model.CallBackCtwTranferInOutResponse, error) {
	// {
	// 	"AppID": "ABCDE",
	// 	"AppSecret": "2de5c9c3-76a2-428a-aba0-XXXXXXXXXXXX",
	// 	"UserID": "1",
	// 	"TransactionID": "667f70ecc2a54a93fc4c2695667f70ec68f376bf8c282a55",
	// 	"Amount": -100,
	// 	"RoundID": "667f70ecc2a54a93fc4c2695667f70ec68f376bf8c282a55",
	// 	"GameID": "XingYunXiang",
	// 	"ReqTime": "2024-06-29T09:26:52.035906101+07:00",
	// 	"Reason": "bet"
	//   }

	fmt.Printf("CallBackCtwTranferInOutV2: %v\n", helper.StructJson(reqBody))
	getInternalAgentCtwSetting, err := s.repo.GetInternalAgentCtwSetting()
	if err != nil {
		return nil, err
	}

	if getInternalAgentCtwSetting.ProgramAllowUse != "ALLOW_USE" {
		return nil, badRequest("NOT_ALLOW_USE_AGENT_CTW_PLEASE_CONTACT_ADMIN")
	}

	if getInternalAgentCtwSetting.CtwAppId == "" || getInternalAgentCtwSetting.CtwAppPrivate == "" {
		return nil, badRequest("AGENT_CTW_SETTING_NOT_FOUND")
	}

	if reqBody.AppID != getInternalAgentCtwSetting.CtwAppId || reqBody.AppSecret != getInternalAgentCtwSetting.CtwAppPrivate {
		return nil, badRequest("AGENT_CTW_SETTING_NOT_MATCH")
	}

	getUserMemberCode, err := s.repo.GetUserByMemberCode(reqBody.UserID)
	if err != nil {
		// 404 Error: ในกรณีที่ไม่มีข้อมูล User (User Not Found)
		return nil, badRequest("USER_NOT_FOUND")
	}

	getUserDetail, err := GetUser(repository.NewUserRepository(s.sharedDb), getUserMemberCode.Id)
	if err != nil {
		return nil, err
	}

	// already try get bet but can't "code":20001 need to response this func first
	// var getBetDetail model.CallBackCtwGameBetDetailRequest
	// getBetDetail.RoundId = reqBody.RoundID
	// urlDetail := model.CallApiAgentCtwDetail{
	// 	CtwAppId:      getInternalAgentCtwSetting.CtwAppId,
	// 	CtwAppPrivate: getInternalAgentCtwSetting.CtwAppPrivate,
	// 	CtwUrl:        getInternalAgentCtwSetting.CtwUrl,
	// }
	// getBetDetailResponse, err := s.repo.CallApiAgentCtwBetDetail(urlDetail, getBetDetail)
	// if err != nil {
	// 	return nil, err
	// }
	// fmt.Println("getBetDetailResponse ROUND ID", getBetDetailResponse.Data.RoundId, helper.StructJson(getBetDetailResponse))

	// create transacion
	var createAgentCtwCallbackBody model.CreateAgentCtwCallback
	createAgentCtwCallbackBody.UserId = getUserMemberCode.Id
	createAgentCtwCallbackBody.MemberCode = reqBody.UserID
	if reqBody.Reason == "bet" {
		if reqBody.Amount < 0 {
			createAgentCtwCallbackBody.BetAmount = -reqBody.Amount
		} else {
			createAgentCtwCallbackBody.BetAmount = reqBody.Amount
		}
	} else if reqBody.Reason == "win" {
		createAgentCtwCallbackBody.Payoff = reqBody.Amount
	}
	// createAgentCtwCallbackBody.WinloseAmount =
	createAgentCtwCallbackBody.Balance = getUserDetail.Credit
	createAgentCtwCallbackBody.BeforeBalance = getUserDetail.Credit
	createAgentCtwCallbackBody.AfterBalance = getUserDetail.Credit
	createAgentCtwCallbackBody.TransactionId = reqBody.TransactionID
	createAgentCtwCallbackBody.RoundId = reqBody.RoundID
	createAgentCtwCallbackBody.GameId = reqBody.GameID
	createAgentCtwCallbackBody.CallbackReason = reqBody.Reason
	createAgentCtwCallbackBody.Remark = "CREATE"
	createAgentCtwCallbackBody.IsSuccess = false
	if err := s.repo.CreateOrUpdateAgentCtwCallback(createAgentCtwCallbackBody); err != nil {
		return nil, err
	}

	var updateAgentCtwCallbackBody model.UpdateAgentCtwCallback
	updateAgentCtwCallbackBody.MemberCode = reqBody.UserID
	updateAgentCtwCallbackBody.RoundId = reqBody.RoundID

	playedAmount := reqBody.Amount
	playedAmount = math.Round(playedAmount*100) / 100
	var currnecntCredit *model.UserTransactionCreateResponse
	// beforeAmount := getUserDetail.Credit
	afterAmount := getUserDetail.Credit
	if playedAmount > 0 {
		var increaseUserCredit model.IncreaseUserCreditFromOtherAgentRequest
		increaseUserCredit.UserId = getUserMemberCode.Id
		increaseUserCredit.Amount = playedAmount
		currnecntCredit, err = s.repo.IncreaseUserCreditFromOtherAgent(increaseUserCredit)
		if err != nil {
			logerror := fmt.Sprintf("IncreaseUserCreditFromOtherAgent Error: %v", err)
			updateAgentCtwCallbackBody.Remark = &logerror
			if err := s.repo.UpdateAgentCtwCallback(updateAgentCtwCallbackBody); err != nil {
				log.Println(err)
			}
			return nil, err
		}
		// beforeAmount = currnecntCredit.AgentBeforeAmount
		afterAmount = currnecntCredit.AgentAfterAmount
	} else if playedAmount < 0 {
		var decreaseUserCredit model.DecreaseUserCreditFromOtherAgentRequest
		decreaseUserCredit.UserId = getUserMemberCode.Id
		decreaseUserCredit.Amount = -playedAmount // ต้องเป็นบวก
		currnecntCredit, err = s.repo.DecreaseUserCreditFromOtherAgent(decreaseUserCredit)
		if err != nil {
			logerror := fmt.Sprintf("DecreaseUserCreditFromOtherAgent Error: %v", err)
			updateAgentCtwCallbackBody.Remark = &logerror
			if err := s.repo.UpdateAgentCtwCallback(updateAgentCtwCallbackBody); err != nil {
				log.Println(err)
			}
			return nil, err
		}
		// beforeAmount = currnecntCredit.AgentBeforeAmount
		afterAmount = currnecntCredit.AgentAfterAmount
	}

	// update callback
	endOfAmountCallBack := "SUCCESS_V2"
	updateAgentCtwCallbackBody.Remark = &endOfAmountCallBack
	// !!!!!!!! ใช้่ before ไม่ได้ เพราะ stack เป็น round !!!!!!!
	// updateAgentCtwCallbackBody.BeforeBalance = &beforeAmount
	updateAgentCtwCallbackBody.AfterBalance = &afterAmount
	setSuccess := true
	updateAgentCtwCallbackBody.IsSuccess = &setSuccess
	if err := s.repo.UpdateAgentCtwCallback(updateAgentCtwCallbackBody); err != nil {
		log.Println(err)
	}

	var response model.CallBackCtwTranferInOutResponse
	response.Code = 0
	response.Error = ""
	response.Data.Balance = afterAmount

	return &response, nil
}

func (s agentCtwService) GetCtwBetDetail(reqBody model.CallBackCtwTranferInOutRequest) (*model.CallBackCtwGameBetDetail, error) {
	getInternalAgentCtwSetting, err := s.repo.GetInternalAgentCtwSetting()
	if err != nil {
		return nil, err
	}

	if getInternalAgentCtwSetting.ProgramAllowUse != "ALLOW_USE" {
		return nil, badRequest("NOT_ALLOW_USE_AGENT_CTW_PLEASE_CONTACT_ADMIN")
	}

	if getInternalAgentCtwSetting.CtwAppId == "" || getInternalAgentCtwSetting.CtwAppPrivate == "" {
		return nil, badRequest("AGENT_CTW_SETTING_NOT_FOUND")
	}

	var getBetDetail model.CallBackCtwGameBetDetailRequest
	getBetDetail.RoundId = reqBody.RoundID

	urlDetail := model.CallApiAgentCtwDetail{
		CtwAppId:      getInternalAgentCtwSetting.CtwAppId,
		CtwAppPrivate: getInternalAgentCtwSetting.CtwAppPrivate,
		CtwUrl:        getInternalAgentCtwSetting.CtwUrl,
	}

	getBetDetailResponse, err := s.repo.CallApiAgentCtwBetDetail(urlDetail, getBetDetail)
	if err != nil {
		return nil, err
	}

	// update
	var updateAgentCtwCallbackBody model.UpdateAgentCtwCallback
	updateAgentCtwCallbackBody.MemberCode = getBetDetailResponse.Data.UserId
	updateAgentCtwCallbackBody.RoundId = getBetDetailResponse.Data.RoundId
	updateAgentCtwCallbackBody.Payoff = &getBetDetailResponse.Data.Win
	updateAgentCtwCallbackBody.BetAmount = &getBetDetailResponse.Data.Bet
	updateAgentCtwCallbackBody.WinloseAmount = &getBetDetailResponse.Data.Winloss
	endOfAmountCallBack := "SUCCESS"
	updateAgentCtwCallbackBody.Remark = &endOfAmountCallBack
	endOfFunc := true
	updateAgentCtwCallbackBody.IsSuccess = &endOfFunc

	if err := s.repo.UpdateAgentCtwCallback(updateAgentCtwCallbackBody); err != nil {
		log.Println(err)
		return nil, err
	}

	return getBetDetailResponse, nil
}
