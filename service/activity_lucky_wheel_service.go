package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"errors"
	"fmt"
	"log"
	"math/rand"
	"net/http"
	"os"
	"strconv"
	"time"

	"gorm.io/gorm"
)

type ActivityLuckyWheelService interface {
	//backoffice
	GetActivityLuckyWheelSetting() (*model.ActivityLuckyWheelSettingResponse, error)
	UpdateActivityLuckyWheelSetting(req model.ActivityLuckyWheelSettingUpdateRequest) error
	GetActivityLuckyWheelList() ([]model.ActivityLuckyWheelResponse, error)
	UpdateActivityLuckyWheel(list model.ActivityLuckyWheelRequest) error
	GetReportLuckyWheelRoundList(req model.LuckyweelSummaryListRequest) (*model.SuccessWithPagination, error)
	GetReportLuckyWheelRoundByUserId(req model.LuckyweelSummaryByUserIdRequest) (*model.SuccessMemberCodeWithPagination, error)
	GetActivityLuckyWheelSettingAmountSpin() ([]model.SelectOptions, error)
	GetActivityLuckyWheelSettingImageSpin(spinId int64) (*model.GetActivityLuckyWheelSettingImageSpinResponse, error)
	UploadImage(imageFileBody *http.Request) (*model.FileUploadResponse, error)

	//web
	CreateRoundActivityLuckyWheel(req model.ActivityLuckyWheelRoundUserRequest) error
	GetConditionType() ([]model.ConditionTypeResponse, error)
	GetActivityLuckyWheelUserList() ([]model.ActivityLuckyWheelUserResponse, error)
	GetActivityLuckyWheelRoundUser(userId int64) (*model.RoundLuckyWheelUserResponse, error)
	RoundActivityLuckyWheelUser(userId int64) (*model.RewardResponse, error)
}

type activityLuckyWheelService struct {
	repo        repository.ActivityLuckyWheelRepository
	shareDb     *gorm.DB
	serviceNoti NotificationService
}

func NewActivityLuckyWheelService(
	repo repository.ActivityLuckyWheelRepository,
	shareDb *gorm.DB,
	serviceNoti NotificationService,
) ActivityLuckyWheelService {
	return &activityLuckyWheelService{repo, shareDb, serviceNoti}
}

func (s *activityLuckyWheelService) GetActivityLuckyWheelSetting() (*model.ActivityLuckyWheelSettingResponse, error) {

	record, err := s.repo.GetActivityLuckyWheelSetting()
	if err != nil {
		if err.Error() == recordNotFound {
			// create
			var createBody model.ActivityLuckyWheelSettingCreateBody
			createBody.ConditionId = model.ACTIVITY_LUCKY_WHEEL_SETTING_CONDITION_DEPOSIT
			createBody.LosePerRoll = 1
			createBody.MaxRollPerDay = 0
			createBody.ActivityLuckyWheelSettingAmountSpinId = 2 // default 8 ช่อง
			createBody.CumulativeExpiredDays = 0

			if _, err := s.repo.CreateActivityLuckyWheelSetting(createBody); err != nil {
				return nil, internalServerError(err)
			}
			// ReGet
			record2, err := s.repo.GetActivityLuckyWheelSetting()
			if err != nil {
				return nil, internalServerError(err)
			}
			getActivityLuckyWheelSettingImageSpin, err := s.repo.GetActivityLuckyWheelSettingImageSpin(createBody.ActivityLuckyWheelSettingAmountSpinId)
			if err != nil {
				return nil, internalServerError(err)
			}
			record2.ImageSpin = *getActivityLuckyWheelSettingImageSpin

			return record2, nil
		}

		return nil, internalServerError(err)
	}

	getActivityLuckyWheelSettingImageSpin, err := s.repo.GetActivityLuckyWheelSettingImageSpin(record.ActivityLuckyWheelSettingAmountSpinId)
	if err != nil {
		return nil, internalServerError(err)
	}
	record.ImageSpin = *getActivityLuckyWheelSettingImageSpin

	return record, nil
}

func (s *activityLuckyWheelService) UpdateActivityLuckyWheelSetting(req model.ActivityLuckyWheelSettingUpdateRequest) error {

	record, err := s.repo.GetActivityLuckyWheelSetting()
	if err != nil {
		return internalServerError(err)
	}

	var body model.ActivityLuckyWheelSettingUpdateBody
	body.ConditionId = req.ConditionId
	body.LosePerRoll = req.LosePerRoll
	body.MaxRollPerDay = req.MaxRollPerDay
	body.ActivityLuckyWheelSettingAmountSpinId = req.ActivityLuckyWheelSettingAmountSpinId
	body.CumulativeExpiredDays = req.CumulativeExpiredDays
	body.IsEnabled = req.IsEnabled
	if err := s.repo.UpdateActivityLuckyWheelSetting(record.Id, body); err != nil {
		return internalServerError(err)
	}

	req.ImageSpin.ActivityLuckyWheelSettingAmountSpinId = req.ActivityLuckyWheelSettingAmountSpinId
	if err := s.repo.UpdateActivityLuckyWheelSettingImageSpin(req.ImageSpin); err != nil {
		return internalServerError(err)
	}

	return nil
}

func (s *activityLuckyWheelService) GetActivityLuckyWheelList() ([]model.ActivityLuckyWheelResponse, error) {
	result, err := s.repo.GetActivityLuckyWheelList()
	if err != nil {
		return nil, internalServerError(err)
	}
	return result, nil
}

func (s *activityLuckyWheelService) UpdateActivityLuckyWheel(req model.ActivityLuckyWheelRequest) error {

	// GetActivityLuckyWheelList() ([]model.ActivityLuckyWheelResponse, error)
	// getActivityLuckyWheelList, err := s.repo.GetActivityLuckyWheelList()
	// if err != nil {
	// 	return internalServerError(err)
	// }

	if len(req.List) == 0 {
		return badRequest("Invalid List Length")
	}

	// 15/05/2025 สามารถทำให้ minimumReward ซ้ำกันได้
	// for i := 0; i < len(req.List); i++ {
	// 	for j := i + 1; j < len(req.List); j++ {
	// 		if req.List[i].MinimumReward == req.List[j].MinimumReward {
	// 			return badRequest("ตั้งค่ารางวัลซ้ำกัน")
	// 		}
	// 	}
	// }

	// // MinimumReward == 0 return error
	// for _, item := range req.List {
	// 	if item.MinimumReward <= 0 {
	// 		return badRequest("ตั้งค่าเป็น 0 ไม่ได้")
	// 	}
	// }

	var body []model.ActivityLuckyWheelUpdateBody
	// req.PercentWin <= 0 will use getActivityLuckyWheelList.PercentWin and will all put in body
	for _, item := range req.List {
		// for _, item2 := range getActivityLuckyWheelList {
		// 	if item.Id == item2.Id {
		if item.PercentWin != nil && *item.PercentWin < 0 {
			return badRequest("เปอร์เซ็นต์ออกรางวัล ต้องไม่เป็น 0")
		} else {
			var bodyItem model.ActivityLuckyWheelUpdateBody
			// bodyItem.Id = item2.Id
			bodyItem.Id = item.Id
			bodyItem.Message = item.Message
			bodyItem.MinimumReward = item.MinimumReward
			bodyItem.HexBackgroundColor = item.HexBackgroundColor
			bodyItem.FontColor = item.FontColor
			bodyItem.PercentWin = item.PercentWin
			body = append(body, bodyItem)
		}
		// }
		// }
	}
	// update body as loop
	for _, item := range body {
		if err := s.repo.UpdateActivityLuckyWheel(item); err != nil {
			return internalServerError(err)
		}
	}

	return nil
}

func (s *activityLuckyWheelService) GetReportLuckyWheelRoundList(req model.LuckyweelSummaryListRequest) (*model.SuccessWithPagination, error) {

	// ClearExpiredRoundByOneMonth() error
	if err := s.repo.ClearExpiredRoundByOneMonth(); err != nil {
		return nil, nil
	}

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	summaryLucky, total, err := s.repo.GetReportLuckyWheelRoundList(req)
	if err != nil {
		return nil, internalServerError(err)
	}

	var res model.SuccessWithPagination
	res.Message = "Success"
	res.List = summaryLucky
	res.Total = total

	return &res, nil
}

func (s *activityLuckyWheelService) GetReportLuckyWheelRoundByUserId(req model.LuckyweelSummaryByUserIdRequest) (*model.SuccessMemberCodeWithPagination, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	list, total, err := s.repo.GetReportLuckyWheelRoundByUserId(req)
	if err != nil {
		return nil, internalServerError(err)
	}

	memberCode, err := s.repo.GetMemberCodeUser(req.UserId)
	if err != nil {
		return nil, internalServerError(err)
	}

	var res model.SuccessMemberCodeWithPagination
	res.Message = "Success"
	res.MemberCode = *memberCode
	res.List = list
	res.Total = total

	return &res, nil
}

func (s *activityLuckyWheelService) CreateRoundActivityLuckyWheel(req model.ActivityLuckyWheelRoundUserRequest) error {

	return CreateRoundActivityLuckyWheel(s.repo, req)
}

func CreateRoundActivityLuckyWheel(repo repository.ActivityLuckyWheelRepository, req model.ActivityLuckyWheelRoundUserRequest) error {

	setting, err := repo.GetActivityLuckyWheelSetting()
	if err != nil {
		return internalServerError(err)
	}
	if setting.IsEnabled {

		// เช็คเงื่อนไขที่สามารถหมุนกงล้อได้ ฝาก หรือ ถอน
		if req.ConditionId != setting.ConditionId {
			// badRequest("Condition Don't match")
			return nil

		}

		// เช็คว่าถึงยอดที่กำหนดมามั้ย
		if int(req.ConditionAmount) < setting.LosePerRoll {
			return nil
		}

		// รอบที่หมุดได้
		var amount int = int(req.ConditionAmount) / setting.LosePerRoll
		if setting.MaxRollPerDay != 0 {
			if amount >= setting.MaxRollPerDay {
				amount = setting.MaxRollPerDay
			}

			// เช็ครอบหมุนที่มีของวันนี้
			totalRound, err := repo.GetAmountRoundReceiveUser(req.UserId)
			if err != nil {
				return internalServerError(err)
			}

			// คำนวณรอบหมุนของวันนี้ว่ามีสิทธ์ได้อีกเท่าไหร่ (สิทธ์ที่ได้รับต่อวัน - รอบที่ได้รับไปแล้วของวันนี้)
			totalAll := setting.MaxRollPerDay - int(totalRound)
			if totalAll <= 0 {
				return nil
			}
			if amount >= totalAll {
				amount = totalAll
			}
		}
		if amount == 0 {
			return nil
		}

		// วันปัจจุบัน
		dateTime := time.Now()

		// insert table activity_lucky_wheel_round
		var createRoundBody model.ActivityLuckyWheelRoundBody
		createRoundBody.UserId = req.UserId
		createRoundBody.Received = amount
		createRoundBody.ConditionId = req.ConditionId
		createRoundBody.ConditionAmount = req.ConditionAmount
		createRoundBody.ReceivedDate = dateTime
		createRoundBody.ExpiredDate = nil
		// เช็ควันหมดอายุ
		if setting.CumulativeExpiredDays != 0 {
			expiredDate := dateTime.Add(time.Duration(setting.CumulativeExpiredDays) * 24 * time.Hour)
			createRoundBody.ExpiredDate = &expiredDate
		}
		insertId, err := repo.CreateActivityLuckyWheelRound(createRoundBody)
		if err != nil {
			return internalServerError(err)
		}

		// Clear Expired Round
		if err := repo.ClearExpiredRoundByUserId(req.UserId); err != nil {
			return nil
		}
		// later : bulk insert
		// เพิ่มรอบในการหมุนให้ user
		var createList = make([]model.ActivityLuckyWheelRoundUserBody, 0)
		var createBody model.ActivityLuckyWheelRoundUserBody
		createBody.LuckyWheelRoundId = *insertId
		createBody.UserId = req.UserId
		createBody.ConditionId = req.ConditionId
		createBody.ConditionAmount = req.ConditionAmount
		createBody.StatusId = model.ACTIVITY_LUCKY_WHEEL_ROUND_STATUS_ACTIVE
		createBody.ReceivedDate = dateTime
		createBody.ExpiredDate = nil
		createBody.ConditionDescription = fmt.Sprintf("ทุกยอดเสีย %v รับ 1 สิทธิ์", setting.LosePerRoll)
		if setting.ConditionId == 1 {
			createBody.ConditionDescription = fmt.Sprintf("ทุกยอดฝาก %v รับ 1 สิทธิ์", setting.LosePerRoll)
		}
		// เช็ควันหมดอายุ
		if setting.CumulativeExpiredDays != 0 {
			expiredDate := dateTime.Add(time.Duration(setting.CumulativeExpiredDays) * 24 * time.Hour)
			createBody.ExpiredDate = &expiredDate
		}

		for i := 0; i < amount; i++ {
			createList = append(createList, createBody)
		}

		if err := repo.CreateActivityLuckyWheelRoundUser(createList); err != nil {
			return internalServerError(err)
		}

		return nil
	} else {
		// badRequest("IsEnabled Status Not Open")
		return nil
	}

}

func (s *activityLuckyWheelService) GetConditionType() ([]model.ConditionTypeResponse, error) {
	result, err := s.repo.GetConditionType()
	if err != nil {
		return nil, internalServerError(err)
	}
	return result, nil
}

func (s *activityLuckyWheelService) GetActivityLuckyWheelUserList() ([]model.ActivityLuckyWheelUserResponse, error) {

	result, err := s.repo.GetActivityLuckyWheelUserList()
	if err != nil {
		return nil, internalServerError(err)
	}

	getActivityLuckyWheelSetting, err := s.repo.GetActivityLuckyWheelSetting()
	if err != nil {
		return nil, badRequest("NO_SETTING")
	}
	setTotalSpin := 0
	switch getActivityLuckyWheelSetting.ActivityLuckyWheelSettingAmountSpinId {
	case model.ACTIVITY_LUCKY_WHEEL_SETTING_AMOUNT_SPIN_6:
		setTotalSpin = 6
	case model.ACTIVITY_LUCKY_WHEEL_SETTING_AMOUNT_SPIN_8:
		setTotalSpin = 8
	case model.ACTIVITY_LUCKY_WHEEL_SETTING_AMOUNT_SPIN_10:
		setTotalSpin = 10
	case model.ACTIVITY_LUCKY_WHEEL_SETTING_AMOUNT_SPIN_12:
		setTotalSpin = 12
	}
	// Limit result to setTotalSpin
	if len(result) > setTotalSpin {
		result = result[:setTotalSpin]
	}

	return result, nil
}

func (s *activityLuckyWheelService) GetActivityLuckyWheelRoundUser(userId int64) (*model.RoundLuckyWheelUserResponse, error) {

	// Clear Expired Round
	if err := s.repo.ClearExpiredRoundByUserId(userId); err != nil {
		return nil, nil
	}

	luckySetting, err := s.repo.GetActivityLuckyWheelSetting()
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, badRequest("NOT_SETTING")
		}
		return nil, internalServerError(err)
	}
	if !luckySetting.IsEnabled {
		return nil, badRequest("UNAVAILABLE")
	}

	// รอบหมุนทั้งหมดของ User
	var totalRoundUser int64
	totalRoundUser, err = s.repo.GetAmountRoundUser(userId)
	if err != nil {
		return nil, internalServerError(err)
	}

	// รอบหมุนวันนี้ของ User
	var totalRoundUsedUser int64
	totalRoundUsedUser, err = s.repo.GetAmountRoundUsedUser(userId)
	if err != nil {
		return nil, internalServerError(err)
	}

	roundLeft := int(totalRoundUser)

	if luckySetting.MaxRollPerDay != 0 {
		if roundLeft >= luckySetting.MaxRollPerDay {
			roundLeft = luckySetting.MaxRollPerDay
			roundLeft = roundLeft - int(totalRoundUsedUser)
			if roundLeft <= 0 {
				roundLeft = 0
			}
		} else {
			if int(totalRoundUsedUser) >= luckySetting.MaxRollPerDay {
				roundLeft = 0
			}
		}
	}

	var res model.RoundLuckyWheelUserResponse
	getActivityLuckyWheelSettingImageSpin, err := s.repo.GetActivityLuckyWheelSettingImageSpin(luckySetting.ActivityLuckyWheelSettingAmountSpinId)
	if err != nil {
		return nil, internalServerError(err)
	}
	res.ImageSpin = *getActivityLuckyWheelSettingImageSpin

	res.ConditionId = luckySetting.ConditionId
	res.LosePerRoll = luckySetting.LosePerRoll
	res.RoundLeft = roundLeft

	setTotalSpin := 0
	switch luckySetting.ActivityLuckyWheelSettingAmountSpinId {
	case model.ACTIVITY_LUCKY_WHEEL_SETTING_AMOUNT_SPIN_6:
		setTotalSpin = 6
	case model.ACTIVITY_LUCKY_WHEEL_SETTING_AMOUNT_SPIN_8:
		setTotalSpin = 8
	case model.ACTIVITY_LUCKY_WHEEL_SETTING_AMOUNT_SPIN_10:
		setTotalSpin = 10
	case model.ACTIVITY_LUCKY_WHEEL_SETTING_AMOUNT_SPIN_12:
		setTotalSpin = 12
	}

	res.TotalSpin = setTotalSpin

	return &res, nil

}

func (s *activityLuckyWheelService) RoundActivityLuckyWheelUser(userId int64) (*model.RewardResponse, error) {
	// รอบหมุนทั้งหมดของ ไม่ต้องแล้วไม่ต้องมาคิดใหม่ มันอยู่ใน GetActivityLuckyWheelRoundUser หมด แล้ว @ฺP.Boss
	// luckySetting, err := s.repo.GetActivityLuckyWheelSetting()
	// if err != nil {
	// 	return nil, internalServerError(err)
	// }
	// // Check ว่าเปิดหรือปิด
	// if !luckySetting.IsEnabled {
	// 	return nil, badRequest("UNAVAILABLE")
	// }

	// // รอบหมุนทั้งหมดของ User
	// var totalRoundUser int64
	// totalRoundUser, err = s.repo.GetAmountRoundUser(userId)
	// if err != nil {
	// 	return nil, internalServerError(err)
	// }

	// // รอบหมุนวันนี้ของ User
	// var totalRoundUsedUser int64
	// totalRoundUsedUser, err = s.repo.GetAmountRoundUsedUser(userId)
	// if err != nil {
	// 	return nil, internalServerError(err)
	// }

	// roundLeft := int(totalRoundUser)

	// if luckySetting.MaxRollPerDay != 0 {
	// 	if roundLeft >= luckySetting.MaxRollPerDay {
	// 		roundLeft = luckySetting.MaxRollPerDay
	// 		roundLeft = roundLeft - int(totalRoundUsedUser)
	// 		if roundLeft <= 0 {
	// 			roundLeft = 0
	// 		}
	// 	} else {
	// 		if int(totalRoundUsedUser) >= luckySetting.MaxRollPerDay {
	// 			roundLeft = 0
	// 		}
	// 	}
	// }

	// // Check รอบหมุนว่ามีมั้ย
	// if roundLeft <= 0 {
	// 	return nil, badRequest("ไม่มีรอบหมุนกงล้อนำโชค")
	// }

	// Clear Expired Round
	if err := s.repo.ClearExpiredRoundByUserId(userId); err != nil {
		return nil, nil
	}

	// Get Round ปัจจุบัน
	currentUserRoundActive, err := s.GetActivityLuckyWheelRoundUser(userId)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, badRequest("ไม่มีรอบหมุนกงล้อนำโชค")
		}
		return nil, internalServerError(err)
	}

	if currentUserRoundActive.RoundLeft <= 0 {
		return nil, badRequest("ไม่มีรอบหมุนกงล้อนำโชค")
	}

	//race condition
	//add seconds to action key
	actionAt := time.Now().UTC().Format("20060102150405")
	var createBody model.RaceActionCreateBody
	createBody.Name = "LUCKY_WHEEL_ROUND"
	createBody.JsonRequest = helper.StructJson(currentUserRoundActive)
	createBody.Status = "PENDING"
	createBody.ActionKey = fmt.Sprintf("LUCKY_WHEEL_ROUND_%v_%v", userId, actionAt)
	createBody.UnlockAt = time.Now().UTC().Add(time.Second * 1)
	if _, err := s.repo.GetRaceActionIdByActionKey(createBody.ActionKey); err != nil {
		log.Println("RacingActivityLuckyWheel.ERROR.GetRaceActionIdByActionKey", err)
		if err != gorm.ErrRecordNotFound {
			return nil, internalServerError(errors.New("CANNOT_CHECK_ACTION"))
		}
	} else {
		return nil, internalServerError(errors.New("LUCY_WHEEL_ROUND_IN_PROGRESS_TRY_AGAIN_LATER"))
	}

	// create race condition
	actionId, err := s.repo.CreateRaceCondition(createBody)
	if err != nil {
		log.Println("RacingActivityLuckyWheel.ERROR.CreateRaceCondition", err)
		return nil, internalServerError(errors.New("CANNOT_CREATE_ACTION_LUCKY_WHEEL_ROUND"))
	}
	var res model.RewardResponse
	if actionId != 0 {
		currentUserRound, err := s.repo.GetActivityLuckyWheelRoundUser(userId)
		if err != nil {
			if err.Error() == recordNotFound {
				return nil, badRequest("ไม่มีรอบหมุนกงล้อนำโชค")
			}
			return nil, internalServerError(err)
		}

		// Get ActivityLuckyWheelList calculate
		activityLuckyWheelList, err := s.RandomLuckyWheel()
		if err != nil {
			return nil, internalServerError(err)
		}

		// Update the amount that get to  activity_lucky_wheel_round_user
		// UpdateActivityLuckyWheelRoundUser
		var updateBody model.UpdateActivityLuckyWheelRoundUserRequest
		updateBody.Id = currentUserRound.Id
		updateBody.UserId = &currentUserRound.UserId
		updateBody.LuckyWheelId = &activityLuckyWheelList.Id
		creditAmount := float64(activityLuckyWheelList.MinimumReward)
		updateBody.Reward = &creditAmount
		status := model.ACTIVITY_LUCKY_WHEEL_ROUND_STATUS_PRIZE_CLAIMED
		updateBody.StatusId = &status
		dateTime := time.Now().UTC()
		updateBody.RotatedDate = &dateTime

		if err := s.repo.UpdateActivityLuckyWheelRoundUser(updateBody); err != nil {
			return nil, internalServerError(err)
		}

		// CheckLuckyWheelUserIncome
		if err := s.CheckLuckyWheelUserIncome(currentUserRound.Id); err != nil {
			return nil, internalServerError(err)
		}

		// }

		res.LuckyWheelId = &activityLuckyWheelList.Id
		res.Reward = &creditAmount
	}

	return &res, nil
}

func (s *activityLuckyWheelService) RandomLuckyWheel() (*model.ActivityLuckyWheelResponse, error) {

	// Get ActivityLuckyWheelList
	activityLuckyWheelList, err := s.repo.GetActivityLuckyWheelList()
	if err != nil {
		return nil, internalServerError(err)
	}
	getActivityLuckyWheelSetting, err := s.repo.GetActivityLuckyWheelSetting()
	if err != nil {
		return nil, badRequest("NO_SETTING")
	}
	setTotalSpin := 0
	switch getActivityLuckyWheelSetting.ActivityLuckyWheelSettingAmountSpinId {
	case model.ACTIVITY_LUCKY_WHEEL_SETTING_AMOUNT_SPIN_6:
		setTotalSpin = 6
	case model.ACTIVITY_LUCKY_WHEEL_SETTING_AMOUNT_SPIN_8:
		setTotalSpin = 8
	case model.ACTIVITY_LUCKY_WHEEL_SETTING_AMOUNT_SPIN_10:
		setTotalSpin = 10
	case model.ACTIVITY_LUCKY_WHEEL_SETTING_AMOUNT_SPIN_12:
		setTotalSpin = 12
	}
	// Limit result to setTotalSpin
	if len(activityLuckyWheelList) > setTotalSpin {
		activityLuckyWheelList = activityLuckyWheelList[:setTotalSpin]
	}

	// Normalize the percentages if needed (only if the total is not 100)
	totalPercent := 0.0
	for _, item := range activityLuckyWheelList {
		totalPercent += item.PercentWin
	}

	src := rand.NewSource(time.Now().UnixNano())
	r := rand.New(src)
	randomNumber := r.Float64() * totalPercent

	// Select item based on the random number
	cumulativePercent := 0.0
	var selected *model.ActivityLuckyWheelResponse
	for _, item := range activityLuckyWheelList {
		cumulativePercent += item.PercentWin
		if randomNumber <= cumulativePercent {
			selected = &item
			break
		}
	}

	// If no item is selected, handle the error
	if selected == nil {
		return nil, errors.New("no lucky wheel item selected")
	}

	// Prepare the response
	resultRandom := model.ActivityLuckyWheelResponse{
		Id:                 selected.Id,
		Position:           selected.Position,
		Message:            selected.Message,
		MinimumReward:      selected.MinimumReward,
		HexBackgroundColor: selected.HexBackgroundColor,
		FontColor:          selected.FontColor,
		PercentWin:         selected.PercentWin,
		CreatedAt:          selected.CreatedAt,
		UpdatedAt:          selected.UpdatedAt,
	}

	return &resultRandom, nil
}

func (s *activityLuckyWheelService) GetUserIncomeMaxAmountConfigSetting() (model.MarketingConfigResponse, error) {

	var record model.MarketingConfigResponse
	record.UserIncomeMaxAmount = 500

	// TRY to get config, Default on error
	config, err := s.repo.GetMarketingConfigByKey("AUTO_USER_INCOME_MAX_AMOUNT", "500")
	if err == nil {
		// record.UserIncomeMaxAmount = config.ConfigVal convert to float64
		tempFloat, err := strconv.ParseFloat(config.ConfigVal, 64)
		if err == nil {
			record.UserIncomeMaxAmount = tempFloat
		}
	}
	return record, nil
}

func (s *activityLuckyWheelService) CheckLuckyWheelUserIncome(userWheelId int64) error {

	actionTime := time.Now()

	// get wheel
	trans, err := s.repo.GetActivityLuckyWheelRoundUserById(userWheelId)
	if err != nil {
		return internalServerError(err)
	}

	// status check
	if trans.StatusId == model.ACTIVITY_LUCKY_WHEEL_ROUND_STATUS_PRIZE_CLAIMED {

		user, err := s.repo.GetUserMemberInfoById(trans.UserId)
		if err != nil {
			return internalServerError(err)
		}

		config, err := s.GetUserIncomeMaxAmountConfigSetting()
		if err != nil {
			return internalServerError(err)
		}

		// [UserIncome]
		var incomeId *int64
		returnAmount := trans.Reward
		if trans.Reward != nil && *trans.Reward > 0 {
			var incomeLogCreateBody model.UserIncomeLogCreateBody
			incomeLogCreateBody.UserId = user.Id
			incomeLogCreateBody.TypeId = model.USER_INCOME_TYPE_LUCKY_WHEEL
			incomeLogCreateBody.Detail = "โบนัสหมุนกงล้อนำโชค"
			incomeLogCreateBody.CreditAmount = *returnAmount
			incomeLogCreateBody.StatusId = model.USER_INCOME_STATUS_PENDING
			incomeLogCreateBody.CreateBy = user.Id
			incomeLogCreateBody.CreateByName = user.Fullname
			incomeId, err = s.repo.CreateUserIncomeLog(incomeLogCreateBody)
			if err != nil {
				return err
			}
		}
		var updateBody model.UpdateActivityLuckyWheelRoundUserRequest
		updateBody.Id = trans.Id
		updateBody.UserIncomeId = incomeId
		updateBody.UserId = &trans.UserId
		// updateBody.LuckyWheelId = &trans.Id
		updateBody.Reward = trans.Reward
		var status int64
		if *trans.Reward > 0 {
			status = model.ACTIVITY_LUCKY_WHEEL_ROUND_STATUS_PRIZE_CLAIMED
		} else {
			status = model.ACTIVITY_LUCKY_WHEEL_ROUND_STATUS_PRIZE_RECEIVED
		}
		updateBody.StatusId = &status

		if err := s.repo.UpdateActivityLuckyWheelRoundUser(updateBody); err != nil {
			return internalServerError(err)
		}

		// OverAmount will stay PENDING in user income
		if *trans.Reward <= config.UserIncomeMaxAmount && *trans.Reward > 0 {
			return s.ConfirmUserIncomeLuckySpiner(*incomeId, user.Id, actionTime, trans.Id)
		} else {

			// AddTo BankPendingRecord -> รายได้จากกิจกรรมกงล้อนำโชค
			dashboardRepo := repository.NewDashboardRepository(s.shareDb)
			if err := AddBankPendingRecordFromReturnLuckyWheel(dashboardRepo, *incomeId, user.Id, actionTime, *returnAmount, "อัตโนมัติ"); err != nil {
				log.Println("CheckLuckyWheelUserIncome.AddBankPendingRecordFromReturnLuckyWheel", err)
			}

			//  EXTERNAL NOTI
			var externalNoti model.NotifyExternalNotificationRequest
			externalNoti.TypeNotify = model.ActitvityBeforeBonus
			externalNoti.MemberCode = *user.MemberCode
			externalNoti.BonusCredit = returnAmount
			externalNoti.UserCredit = user.Credit + *returnAmount
			suggestAuto := int64(0)
			externalNoti.ConfirmedByAdminId = suggestAuto
			if err := s.serviceNoti.ExternalNotification(externalNoti); err != nil {
				log.Println("FailedNotify", err)
			}
		}
	} else {
		return badRequest("INVALID_TRANSACTION_STATUS")
	}
	return nil
}

func (s *activityLuckyWheelService) ConfirmUserIncomeLuckySpiner(incomeId int64, confirmBy int64, actionTime time.Time, tranWheelId int64) error {

	// 1. CONFIRMING is to set User Credit and Turnover
	// SAME LOGIG as
	income, err := s.repo.GetUserIncomeLogById(incomeId)
	if err != nil {
		return internalServerError(err)
	}
	if income.StatusId == model.USER_INCOME_STATUS_PENDING && income.ConfirmBy == nil {

		returnAmount := income.CreditAmount

		user, err := s.repo.GetUserMemberInfoById(income.UserId)
		if err != nil {
			return internalServerError(err)
		}

		luckyWheelUser, err := s.repo.GetActivityLuckyWheelRoundUserById(tranWheelId)
		if err != nil {
			return internalServerError(err)
		}

		// [Confirm User Income]
		var confirmActivityBody model.ActivityLuckyWheelRoundConfirmCreateRequest
		confirmActivityBody.UserId = luckyWheelUser.UserId
		confirmActivityBody.ActionKey = fmt.Sprintf("ALWRC_%v", income.Id)
		confirmActivityBody.LuckyWheelUserId = luckyWheelUser.Id
		confirmActivityBody.ConditionAmount = *luckyWheelUser.Reward
		checkConfirm, _ := s.repo.GetActivityLuckyWheelRoundConfirmByKey(confirmActivityBody.ActionKey)

		createConfirmId, err := s.repo.CreateActivityLuckyWheelRoundConfirm(confirmActivityBody)
		if err != nil {
			return internalServerError(err)
		}
		var cleanUpData = make(map[string]interface{})
		cleanUpData["createConfirmId"] = createConfirmId

		if createConfirmId > 0 && checkConfirm == nil {
			// [UPDATE ACTIVITY_LUCKY_WHEEL_ROUND_USER]
			bonusAmount := returnAmount
			var updateBody model.UpdateActivityLuckyWheelRoundUserRequest
			updateBody.Id = luckyWheelUser.Id
			updateBody.Reward = &bonusAmount
			status := model.ACTIVITY_LUCKY_WHEEL_ROUND_STATUS_PRIZE_RECEIVED
			updateBody.StatusId = &status
			if err := s.repo.UpdateActivityLuckyWheelRoundUser(updateBody); err != nil {
				// roll back
				if err := s.CleanUpActivityLuckyWheel(cleanUpData); err != nil {
					return internalServerError(err)
				}
				return internalServerError(err)
			}
			cleanUpData["luckyWheelUser"] = luckyWheelUser.Id

			// [INCREASE CREDIT]
			var creditTransferResp *model.UserTransactionCreateResponse
			if bonusAmount > 0 {
				var userCreditReq model.UserTransactionCreateRequest
				userCreditReq.RefId = &income.Id
				userCreditReq.UserId = income.UserId
				userCreditReq.Detail = "โบนัสหมุนกงล้อนำโชค"
				userCreditReq.TypeId = model.CREDIT_TPYE_LUCKY_WHEEL // เพิ่ม type
				userCreditReq.BonusAmount = income.CreditAmount
				creditTransferResp, err = s.repo.IncreaseUserCredit(userCreditReq)
				if err != nil {
					// roll back
					if err := s.CleanUpActivityLuckyWheel(cleanUpData); err != nil {
						return internalServerError(err)
					}
					return internalServerError(err)
				}
			} else {
				creditTransferResp = &model.UserTransactionCreateResponse{
					AgentAfterAmount: user.Credit,
					TransferAt:       actionTime,
				}
			}
			// [CONFIRM USER INCOME]
			var confirmBody model.UserIncomeLogConfirmBody
			confirmBody.Id = income.Id
			confirmBody.CreditAfter = creditTransferResp.AgentAfterAmount
			confirmBody.TransferAt = creditTransferResp.TransferAt
			confirmBody.ConfirmBy = confirmBy
			confirmBody.ConfirmByName = *user.Username
			if err := s.repo.ConfirmUserIncomeLog(confirmBody); err != nil {
				// roll back
				if err := s.CleanUpActivityLuckyWheel(cleanUpData); err != nil {
					return internalServerError(err)
				}
				return internalServerError(err)
			}
			// [LINE]
			var externalNoti model.NotifyExternalNotificationRequest
			externalNoti.TypeNotify = model.ActitvityAfterBonus
			externalNoti.MemberCode = *user.MemberCode
			externalNoti.BonusCredit = &bonusAmount
			externalNoti.UserCredit = user.Credit + bonusAmount
			suggestAuto := int64(0)
			externalNoti.ConfirmedByAdminId = suggestAuto
			if err := s.serviceNoti.ExternalNotification(externalNoti); err != nil {
				log.Println("FailedNotify", err)
			}
		}

	} else {
		return badRequest("INVALID_TRANSACTION_STATUS")
	}
	return nil
}

func (s *activityLuckyWheelService) CleanUpActivityLuckyWheel(cleanUpData map[string]interface{}) error {

	for k, v := range cleanUpData {
		switch k {
		case "createConfirmId":
			if err := s.repo.RollBackConfirmActionConfirmActivityLuckyWheel(v.(int64)); err != nil {
				return err
			}
		case "luckyWheelUser":
			if err := s.repo.RollBackActivityLuckyWheelUser(v.(int64)); err != nil {
				return err
			}

		}
	}
	return nil
}

func (s *activityLuckyWheelService) GetActivityLuckyWheelSettingAmountSpin() ([]model.SelectOptions, error) {

	result, err := s.repo.GetActivityLuckyWheelSettingAmountSpin()
	if err != nil {
		return nil, internalServerError(err)
	}
	return result, nil
}

func (s *activityLuckyWheelService) GetActivityLuckyWheelSettingImageSpin(spinId int64) (*model.GetActivityLuckyWheelSettingImageSpinResponse, error) {

	result, err := s.repo.GetActivityLuckyWheelSettingImageSpin(spinId)
	if err != nil {
		return nil, internalServerError(err)
	}

	return result, nil
}

func (s *activityLuckyWheelService) UploadImage(imageFileBody *http.Request) (*model.FileUploadResponse, error) {

	fileReader, newFileName, err := imageFileBody.FormFile("file")
	if err != nil {
		log.Println("UploadFileS3.ERROR", err)
		return nil, err
	}

	filename := &newFileName.Filename

	dbName := os.Getenv("DB_NAME")

	pathName := fmt.Sprintf("cbgame/%v/luckywheel/", dbName)
	var newImageId *model.FileUploadResponse
	fileData, err := s.repo.UploadImageToS3(pathName, *filename, fileReader)
	if err != nil {
		log.Println("UploadFileS3.ERROR", err)
		return nil, badRequest("UNABLE_TO_UPLOAD_FILE")
	}

	newImageId = &model.FileUploadResponse{
		ImageUrl: fileData.ImageUrl,
	}

	return newImageId, nil
}
