package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net/http"
	"os"
	"time"
)

type TransactionService interface {
	GetTransactionList(query model.TransactionQuery) (*model.TrasactionListResponse, error)
	GetBankList() (*model.TransactionBankResponse, error)
	Deposit(body model.TransactionDepositBody) error
	DepositConfirmedFromUser(body model.CreateDepositConfirmedFromUserRequest) error
	Withdraw(body model.TransactionWithdrawBody) error

	ReadQrFromFileUpload(req *http.Request) (*string, error)

	TestIncreaseUserCredit(body model.TestIncreaseUserCreditRequest) (*model.UserTransactionCreateResponse, error)
	TestDecreaseUserCredit(body model.TestDecreaseUserCreditRequest) (*model.UserTransactionCreateResponse, error)
	CreateSystemLog(name string, body interface{}) error
}

type transactionService struct {
	repo        repository.TransactionRepository
	bankService BankingService
	userService UserService
}

func NewTransactionService(
	repo repository.TransactionRepository,
	bankService BankingService,
	userService UserService,
) TransactionService {
	return &transactionService{repo, bankService, userService}
}

func (s *transactionService) GetTransactionList(query model.TransactionQuery) (*model.TrasactionListResponse, error) {

	if err := helper.Pagination(&query.Page, &query.Limit); err != nil {
		return nil, internalServerError(err)
	}

	list, total, err := s.repo.GetTransactionList(query)
	if err != nil {
		return nil, err
	}

	for i, v := range list {
		if v.Type == "DEPOSIT" {
			list[i].TransactionDate = v.DepositedAt
		} else {
			list[i].TransactionDate = v.CreatedAt
		}
	}

	response := model.TrasactionListResponse{}
	response.List = list
	response.Total = total

	return &response, nil
}

func (s *transactionService) GetBankList() (*model.TransactionBankResponse, error) {

	list, err := s.repo.GetBankActiveList()
	if err != nil {
		return nil, err
	}

	response := model.TransactionBankResponse{}
	for _, v := range list {
		if v.ID != model.UNKNOWN_BANK {
			response.Result = append(response.Result, v)
		}
	}

	return &response, nil
}

func (s *transactionService) Deposit(body model.TransactionDepositBody) error {

	bankAccountId, err := s.repo.GetAccountBankIdByBankId(body.BankID)
	if err != nil {

		if err.Error() == "record not found" {
			return errors.New(transactionBankNotFound)
		}

		return internalServerError(err)
	}

	body.Type = "DEPOSIT"
	body.StatusId = 5
	body.BankAccountID = bankAccountId

	if err := s.repo.Deposit(body); err != nil {
		return err
	}

	qrData, err := verifyQrData(body.QrData)
	if err != nil {
		return internalServerError(err)
	}
	// get the FastBank

	if qrData != nil && qrData.VerifySlipInfo.TransAmount == body.Amount {
		statement, err := s.repo.GetStatementIdAndStatus(body.Amount, qrData.VerifySlipInfo.FromBankName, qrData.VerifySlipInfo.FromAccountNo, qrData.VerifySlipInfo.ToAccountNo)
		if err != nil && err.Error() != "record not found" {
			return internalServerError(err)
		}

		depositAt, err := time.Parse("2006-01-02 15:04", body.DepositedAt)
		if err != nil {
			return internalServerError(err)
		}

		if statement != nil {

			obj := model.BankStatementMatchRequest{}
			obj.StatementId = statement.ID
			obj.UserId = body.UserID
			obj.ConfirmedAt = depositAt
			var setIdAuto int64 = 0
			obj.ConfirmedByAdminId = &setIdAuto

			if err := s.bankService.MatchStatementOwner(obj); err != nil {
				return internalServerError(err)
			}
		}
	}

	return nil
}
func (s *transactionService) DepositConfirmedFromUser(body model.CreateDepositConfirmedFromUserRequest) error {

	// // GetBankAccountById(id int64) (*model.BankAccount, error)
	// bankAccount, err := s.repo.GetBankAccountById(body.BankId)
	// if err != nil {
	// 	return internalServerError(err)
	// }

	// // Get the bank account info from the QR code
	// qrData, err := verifyQrData(body.RawQrCode)
	// if err != nil {

	// 	return internalServerError(err)
	// }

	// // Basic validate
	// if bankAccount.AccountNumber != qrData.VerifySlipInfo.ToAccountNo {
	// 	log.Println("DepositConfirmedFromUser.AccountNumber", bankAccount.AccountNumber, qrData.VerifySlipInfo.ToAccountNo, helper.StructJson(qrData))
	// 	return badRequest("Account number not match")
	// }
	// if bankAccount.BankName != qrData.VerifySlipInfo.ToBankName {
	// 	return badRequest("Bank name not match")
	// }
	// if body.Amount != qrData.VerifySlipInfo.TransAmount {
	// 	return badRequest("Amount not match")
	// }

	// var CreateBody model.CreateDepositConfirmedFromUserBody
	// CreateBody.Amount = body.Amount
	// CreateBody.RawQrCode = body.RawQrCode
	// CreateBody.BankAccountId = bankAccount.Id
	// CreateBody.BankId = bankAccount.BankId
	// CreateBody.DepositedAt = body.DepositedAt
	// CreateBody.SlipUrl = body.SlipUrl
	// CreateBody.StatusId = model.TRANS_STATUS_PENDING
	// CreateBody.UserId = body.UserId

	// // create user transaction
	// userDepositId, err := s.repo.DepositConfirmedFromUser(CreateBody)
	// if err != nil {
	// 	return err
	// }

	// var validateBody model.ValidateQrDataWithBankTransactionBody
	// bankname := qrData.VerifySlipInfo.FromBankName
	// // FindBankByName
	// bank, err := s.repo.FindBankByName(bankname)
	// if err != nil {
	// 	return internalServerError(err)
	// }
	// validateBody.BankId = &bank.Id
	// validateBody.ToBankAccountId = bankAccount.Id
	// validateBody.TransferAmount = qrData.VerifySlipInfo.TransAmount

	// regex := regexp.MustCompile("[^0-9]")
	// numericString := regex.ReplaceAllString(qrData.VerifySlipInfo.FromAccountNo, "")
	// if len(numericString) >= 5 {
	// 	lastFiveDigits := numericString[len(numericString)-5:]
	// 	validateBody.FromBankAccount = lastFiveDigits
	// } else {
	// 	// Handle cases where the string has less than 5 digits
	// 	validateBody.FromBankAccount = numericString
	// }
	// // BKK
	// // Remove " น." from TransTime
	// transTimeWithoutSuffix := strings.Replace(qrData.VerifySlipInfo.TransTime, " น.", "", -1)
	// validateTranferAtFormat := time.Unix(qrData.VerifySlipInfo.TransDate, 0).Format("2006-01-02") + " " + transTimeWithoutSuffix

	// transferDateTime, err := time.Parse("2006-01-02 15:04", validateTranferAtFormat)
	// if err != nil {
	// 	// Handle the parsing error
	// 	return err
	// }

	// // Apply the timezone offset of -7 hours (you can adjust this offset as needed)
	// location := time.FixedZone("YourTimezone", -7*60*60) // -7 hours * 60 minutes * 60 seconds
	// transferDateTime = transferDateTime.In(location)

	// // Convert the DateTime back to the original format as a string
	// validateTranferAtFormat = transferDateTime.Format("2006-01-02 15:04")

	// // Now validateTranferAtFormat has the DateTime in the original format with the -7 timezone offset
	// validateBody.TransferDate = validateTranferAtFormat

	// possibleStatement, total, err := s.repo.FindUserDepositInBankTransaction(validateBody)

	// if err == nil && total == 1 {
	// 	for _, v := range possibleStatement {
	// 		// update the Bank Transaction by v id
	// 		if v.TransactionStatusId == model.TRANS_STATUS_PENDING {

	// 			var updateBankTransaction model.BankStatementMatchRequest
	// 			updateBankTransaction.StatementId = v.StatementId
	// 			updateBankTransaction.UserId = body.UserId
	// 			updateBankTransaction.ConfirmedAt = time.Now()
	// 			var setIdAuto int64 = 0
	// 			updateBankTransaction.ConfirmedByAdminId = &setIdAuto

	// 			if err := s.bankService.MatchStatementOwner(updateBankTransaction); err != nil {
	// 				return internalServerError(err)
	// 			}
	// 			if err := s.repo.UpdateUserTransactionStatementId(*userDepositId, v.StatementId); err != nil {
	// 				return internalServerError(err)
	// 			}
	// 			if err := s.repo.UpdateFromBankAccount(v.StatementId, qrData.VerifySlipInfo.FromAccountNo); err != nil {
	// 				return nil
	// 			}

	// 		}
	// 	}

	// } else if total == 0 {

	// 	userBankId, err := s.repo.GetUserBankIdByName(qrData.VerifySlipInfo.FromBankName)
	// 	if err != nil {
	// 		return internalServerError(err)
	// 	}

	// 	member, err := s.repo.GetMemberById(body.UserId)
	// 	if err != nil {
	// 		return internalServerError(err)
	// 	}

	// 	// อาจจะต้องใช้
	// 	if member.MemberCode == "" {
	// 		memberCode, err := s.userService.GenMemberCode(member.Id)
	// 		if err != nil {
	// 			return internalServerError(err)
	// 		}
	// 		member.MemberCode = *memberCode
	// 	}

	// 	var createStatement model.BankStatementCreateBody
	// 	createStatement.Amount = float64(body.Amount)
	// 	createStatement.AccountId = bankAccount.Id
	// 	createStatement.Detail = "ฝากเงินผ่าน QR Code"
	// 	createStatement.FromBankId = *userBankId
	// 	createStatement.FromAccountNumber = qrData.VerifySlipInfo.FromAccountNo
	// 	createStatement.StatementTypeId = model.STATEMENT_TYPE_TRANSFER_IN
	// 	//convert transferAt to time

	// 	transferAt, err := time.Parse("2006-01-02 15:04", validateTranferAtFormat)
	// 	if err != nil {
	// 		return internalServerError(err)
	// 	}
	// 	createStatement.TransferAt = transferAt
	// 	createStatement.StatementStatusId = model.STATEMENT_STATUS_PENDING

	// 	// create Bank statement
	// 	BankstatementId, err := s.repo.CreateBankStatement(createStatement)
	// 	if err != nil {
	// 		return internalServerError(err)
	// 	}

	// 	// create Bank Transaction
	// 	var createBankTransaction model.BankTransactionCreateBody
	// 	createBankTransaction.MemberCode = member.MemberCode
	// 	createBankTransaction.UserId = body.UserId
	// 	createBankTransaction.StatementId = BankstatementId
	// 	createBankTransaction.TransactionTypeId = model.TRANSACTION_TYPE_DEPOSIT
	// 	createBankTransaction.FromBankId = userBankId
	// 	createBankTransaction.FromAccountNumber = &qrData.VerifySlipInfo.FromAccountNo
	// 	createBankTransaction.ToAccountId = &bankAccount.Id
	// 	createBankTransaction.ToBankId = &bankAccount.BankId
	// 	createBankTransaction.ToAccountNumber = &bankAccount.AccountNumber
	// 	createBankTransaction.CreatedByAdminId = 0
	// 	createBankTransaction.TransactionStatusId = model.TRANS_STATUS_DEPOSIT_PENDING_SLIP
	// 	// ยัวไม่ได้ nil
	// 	createBankTransaction.CreditAmount = float64(body.Amount)
	// 	createBankTransaction.CreditBack = 0
	// 	createBankTransaction.DepositChannel = "QR_CODE"
	// 	createBankTransaction.OverAmount = 0
	// 	createBankTransaction.BonusAmount = 0
	// 	createBankTransaction.BeforeAmount = 0
	// 	createBankTransaction.AfterAmount = 0
	// 	createBankTransaction.TransferAt = &transferAt
	// 	createBankTransaction.CreatedByAdminId = 0

	// 	_, err = s.repo.CreateBankTransaction(createBankTransaction)
	// 	if err != nil {
	// 		return internalServerError(err)
	// 	}

	// 	if err := s.repo.UpdateUserTransactionStatementId(*userDepositId, *BankstatementId); err != nil {
	// 		return internalServerError(err)
	// 	}
	// } else {

	// 	statusLog := "SUCCESS"
	// 	jsonPayLoad := fmt.Sprintf("CHECK SLIP POSSIBLE %v ", helper.StructJson(possibleStatement))
	// 	jsonRequest := helper.StructJson(body)
	// 	logType := "CHECK_SLIP_POSSIBLE"
	// 	var createLog model.BankTransactionLogCreate
	// 	createLog.Status = statusLog
	// 	createLog.JsonPayload = jsonPayLoad
	// 	createLog.JsonRequest = jsonRequest
	// 	createLog.LogType = logType

	// 	if _, err := s.repo.CreateBankTransactionLog(createLog); err != nil {
	// 		return internalServerError(err)
	// 	}
	// 	log.Println("DepositConfirmedFromUser", helper.StructJson(possibleStatement))
	// 	return badRequest("มีการฝากเงินซ้ำ")
	// }

	return nil
}

func (s *transactionService) Withdraw(body model.TransactionWithdrawBody) error {

	body.Type = "WITHDRAW"
	body.StatusId = 6

	if err := s.repo.Withdraw(body); err != nil {
		return err
	}

	return nil
}

func verifyQrData(data string) (*model.TransactionVerifyQrData, error) {
	url := os.Getenv("ACCOUNTING_API_ENDPOINT")
	url += "/api/v2/statement/verifyQrCode?"

	client := &http.Client{}
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, err
	}

	q := req.URL.Query()
	q.Add("rawQrCode", data)
	req.URL.RawQuery = q.Encode()

	req.Header.Add("accept", "application/json")
	req.Header.Add("apiKey", os.Getenv("ACCOUNTING_API_KEY"))

	res, err := client.Do(req)
	if err != nil {
		log.Println("verifyQrData.err", err)
		return nil, err
	}

	defer res.Body.Close()

	if res.StatusCode != 200 {
		// An error occurred, parse the error response from the body
		errorResponse := &model.ExternalAccountError{}
		if err := json.NewDecoder(res.Body).Decode(errorResponse); err != nil {
			log.Println("verifyQrData.err", err)
			return nil, err
		}
		statusLog := "SUCCESS"
		jsonPayLoad := fmt.Sprintf("RAW QR CODE: %s ", data)
		jsonRequest := helper.StructJson(req)
		logType := "ERROR_VERIFY_QR_CODE_FROM_FASTBANK"
		var createLog model.BankTransactionLogCreateRequest
		createLog.Status = &statusLog
		createLog.JsonPayload = &jsonPayLoad
		createLog.JsonRequest = &jsonRequest
		createLog.LogType = &logType

		log.Println("verifyQrData.err", helper.StructJson(errorResponse))
		return nil, badRequest("เกิดข้อผิดพลาดจาก การตรวจสอบ QR Code กรุณาแจ้งผู้ดูแลระบบ")
	}

	var result *model.TransactionVerifyQrData
	if err := json.NewDecoder(res.Body).Decode(&result); err != nil {
		log.Println("verifyQrData.err", err)
		return nil, err
	}

	return result, nil
}

func VerifyQrDataKTB(data string) (*model.TransactionVerifyQrDataKTB, error) {
	url := os.Getenv("ACCOUNTING_API_ENDPOINT")
	url += "/api/v2/statement/verifyQrCodeKTB?"

	client := &http.Client{}
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, err
	}

	q := req.URL.Query()
	q.Add("rawQrCode", data)
	req.URL.RawQuery = q.Encode()

	req.Header.Add("accept", "application/json")
	req.Header.Add("apiKey", os.Getenv("ACCOUNTING_API_KEY"))

	res, err := client.Do(req)
	if err != nil {
		log.Println("verifyQrData.err", err)
		return nil, err
	}

	defer res.Body.Close()

	if res.StatusCode == 500 {
		// An error occurred, parse the error response from the body
		errorResponse := &model.ExternalAccountError{}
		if err := json.NewDecoder(res.Body).Decode(errorResponse); err != nil {
			log.Println("verifyQrData.err", err)
			return nil, err
		}
		statusLog := "SUCCESS"
		jsonPayLoad := fmt.Sprintf("RAW QR CODE: %s ", data)
		jsonRequest := helper.StructJson(req)
		logType := "ERROR_VERIFY_QR_CODE_FROM_FASTBANK"
		var createLog model.BankTransactionLogCreateRequest
		createLog.Status = &statusLog
		createLog.JsonPayload = &jsonPayLoad
		createLog.JsonRequest = &jsonRequest
		createLog.LogType = &logType

		log.Println("verifyQrData.err", helper.StructJson(errorResponse))
		return nil, badRequest("เกิดข้อผิดพลาดจาก การตรวจสอบ QR Code กรุณาแจ้งผู้ดูแลระบบ")
	}

	var result *model.TransactionVerifyQrDataKTB
	if err := json.NewDecoder(res.Body).Decode(&result); err != nil {
		log.Println("verifyQrData.err", err)
		return nil, err
	}

	return result, nil
}

func (s *transactionService) ReadQrFromFileUpload(req *http.Request) (*string, error) {

	rawQrCode, err := s.repo.ReadQrFromFileUpload(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	return rawQrCode, nil
}

func (s *transactionService) TestIncreaseUserCredit(body model.TestIncreaseUserCreditRequest) (*model.UserTransactionCreateResponse, error) {

	actionAt := time.Now()
	refId := int64(999)

	var userCreditReq model.UserTransactionCreateRequest
	userCreditReq.UserId = body.UserId
	userCreditReq.RefId = &refId
	userCreditReq.TransferAt = &actionAt
	userCreditReq.TypeId = model.CREDIT_TYPE_ALLIANCE_INCOME
	// userCreditReq.AccountId = &trans.ToAccountId
	userCreditReq.Detail = "TestIncreaseUserCredit"
	userCreditReq.BonusAmount = float64(body.Amount)
	// userCreditReq.BonusAmount = trans.BonusAmount
	// userCreditReq.ConfirmBy = 0
	// userCreditReq.CreateBy = &trans.CreatedByAdminId

	agentResp, err := s.repo.IncreaseUserCredit(userCreditReq)
	if err != nil {
		log.Println("TestIncreaseUserCredit.ERROR", err)
		return nil, internalServerError(err)
	}

	return agentResp, nil
}

func (s *transactionService) TestDecreaseUserCredit(body model.TestDecreaseUserCreditRequest) (*model.UserTransactionCreateResponse, error) {

	actionAt := time.Now()
	refId := int64(888)

	var userCreditReq model.UserTransactionCreateRequest
	userCreditReq.UserId = body.UserId
	userCreditReq.RefId = &refId
	userCreditReq.TransferAt = &actionAt
	userCreditReq.TypeId = model.CREDIT_TYPE_WITHDRAW
	// userCreditReq.AccountId = &trans.ToAccountId
	userCreditReq.Detail = "TestDecreaseUserCredit"
	userCreditReq.Amount = float64(body.Amount)
	// userCreditReq.BonusAmount = trans.BonusAmount
	// userCreditReq.ConfirmBy = 0
	// userCreditReq.CreateBy = &trans.CreatedByAdminId

	agentResp, err := s.repo.DecreaseUserCredit(userCreditReq)
	if err != nil {
		log.Println("TestDecreaseUserCredit.ERROR", err)
		return nil, internalServerError(err)
	}

	return agentResp, nil
}

func (s *transactionService) CreateSystemLog(name string, req interface{}) error {

	// [SYSLOG] INIT
	_, err := s.repo.CreateSystemLog(model.SystemLogCreateBody{
		Name:   name,
		Status: "HACKING",
		JsonRequest: helper.StructJson(map[string]interface{}{
			"req": req,
		}),
	})
	if err != nil {
		log.Println(name, "CreateSystemLog.ERROR", err.Error())
	}
	return nil
}
