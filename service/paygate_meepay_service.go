package service

import (
	"bytes"
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"image/png"
	"log"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/skip2/go-qrcode"
	"gorm.io/gorm"
)

type MeepayService interface {
	// Meepay
	// CreateMeepayWebhook(req model.MeepayWebhookRequest) (*int64, error)
	GetMeepayWebDepositAccount() (*model.MeepayCustomerDepositInfo, error)
	CreateMeepayDeposit(req model.MeepayDepositCreateRequest) (*model.MeepayOrderWebResponse, error)
	CreateMeepayWithdraw(req model.MeepayWithdrawCreateRequest) (*int64, error)
	// MeepayCheckBalance() (*model.MeepayCheckBalanceRemoteResponse, error)
	CancelWithdrawFromMeepay(transId int64, adminId int64) error
	CreateMeepayDepositWebhook(req model.MeepayWebhookRequest) (*int64, error)
	CreateMeepayWithdrawWebhook(req model.MeepayWebhookRequest) (*int64, error)
	// ORDER
	// GetPendingMeepayDepositOrder(userId int64) (*model.MeepayOrderWebResponse, error)
	// CancelMeepayDeposit(req model.MeepayDepositCancelRequest) error
	// SysLog
	CreateSystemLog(name string, req interface{}) error
}

type paygateMeepayService struct {
	sharedDb                  *gorm.DB
	repo                      repository.MeepayRepository
	activityLuckyWheelService ActivityLuckyWheelService
	promotionWebService       PromotionWebService
	notiService               NotificationService
}

func NewMeepayService(
	sharedDb *gorm.DB,
	repo repository.MeepayRepository,
	activityLuckyWheelService ActivityLuckyWheelService,
	promotionWebService PromotionWebService,
	notiService NotificationService,
) MeepayService {
	return &paygateMeepayService{sharedDb, repo, activityLuckyWheelService, promotionWebService, notiService}
}

func (s paygateMeepayService) ConfirmDepositTransaction(id int64, req model.BankConfirmDepositRequest) error {

	record, err := s.repo.GetBankTransactionById(id)
	if err != nil {
		return internalServerError(err)
	}
	if record.TransactionStatusId != model.TRANS_STATUS_PENDING {
		return badRequest("Transaction is not pending")
	}
	if record.TransactionTypeId != model.TRANSACTION_TYPE_DEPOSIT && record.TransactionTypeId != model.TRANSACTION_TYPE_BONUS {
		return badRequest("Transaction is not deposit")
	}
	jsonBefore, _ := json.Marshal(record)

	var updateData model.BankDepositTransactionConfirmBody
	updateData.TransactionStatusId = model.TRANS_STATUS_DEPOSIT_PENDING_CREDIT
	updateData.ConfirmedAt = req.ConfirmedAt
	updateData.ConfirmedByAdminId = req.ConfirmedByAdminId

	// if record.IsAutoCredit && record.TransferAt != nil {
	// 	seconds := time.Now().UTC().Sub(record.TransferAt.UTC()).Seconds()
	// 	autoProcessTimer := fmt.Sprintf("%.2f", seconds)
	// 	updateData.AutoProcessTimer = &autoProcessTimer
	// }

	var createBody model.CreateBankTransactionActionBody
	createBody.ActionKey = fmt.Sprintf("DCF_STATE#%d", record.Id)
	createBody.TransactionId = record.Id
	createBody.UserId = record.UserId
	createBody.TransactionTypeId = record.TransactionTypeId
	createBody.FromAccountId = record.FromAccountId
	createBody.ToAccountId = record.ToAccountId
	createBody.JsonBefore = string(jsonBefore)
	if req.TransferAt == nil {
		createBody.TransferAt = record.TransferAt
	} else {
		TransferAt := req.TransferAt
		createBody.TransferAt = TransferAt
		updateData.TransferAt = *TransferAt
	}
	if req.SlipUrl != nil {
		createBody.SlipUrl = *req.SlipUrl
	}
	createBody.CreditAmount = record.CreditAmount
	if req.BonusAmount != nil {
		createBody.BonusAmount = *req.BonusAmount
	}
	createBody.ConfirmedAt = req.ConfirmedAt
	createBody.ConfirmedByAdminId = req.ConfirmedByAdminId
	if actionId, err := s.repo.CreateTransactionAction(createBody); err == nil {
		// do nothing ?
		if err := s.repo.ConfirmPendingDepositTransaction(id, updateData); err != nil {
			if err := s.repo.RollbackTransactionAction(*actionId); err == nil {
				return internalServerError(err)
			}
			return internalServerError(err)
		}
	} else {
		return internalServerError(err)
	}
	return nil
}

func (s paygateMeepayService) GetMeepayWebDepositAccount() (*model.MeepayCustomerDepositInfo, error) {

	var result model.MeepayCustomerDepositInfo

	pgAccount, err := s.GetMeepayAccount()
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}
	if !pgAccount.IsDepositEnabled {
		return nil, badRequest("PAYGATE_DEPOSIT_NOT_ENABLED")
	}

	result.Name = pgAccount.Name
	if pgAccount.DisplayName == "" {
		result.Name = "เติมเงินผ่าน QR Code"
	} else {
		result.DisplayName = pgAccount.DisplayName
	}
	result.ShopName = pgAccount.ShopName
	result.MinAmount = model.MEEPAY_DEFMIN_DEPOSIT_AMOUNT
	result.MaxAmount = model.MEEPAY_DEFMAX_DEPOSIT_AMOUNT

	return &result, nil
}

func (s paygateMeepayService) GetMeepayAccount() (*model.PaygateAccountResponse, error) {

	return GetMeepayAccount(s.repo)
}

func GetMeepayAccount(repo repository.MeepayRepository) (*model.PaygateAccountResponse, error) {

	pgAccount, err := repo.GetPaygateAccountByProviderId(model.PAYGATE_MERCHANT_ID_MEEPAY)
	if err != nil {
		return nil, internalServerError(err)
	}
	return pgAccount, nil
}

func GetMeepayCustomerBankUuid(userBankCode string) (string, error) {

	// รหัสธนาคาร ธนาคาร	รหัส
	// BBL ธนาคารกรุงเทพ 002
	// KBANK ธนาคารกสิกรไทย 004
	// KTB ธนาคารกรุงไทย 006
	// JPMCB ธนาคารเจพีมอร์แกน 008
	// OCBC ธนาคารโอเวอร์ซี-ไชนีส 009
	// TTB ธนาคารทหารไทยธนชาต 011
	// SCB ธนาคารไทยพาณิชย์ 014
	// CITI ธนาคารซิตี้แบงก์ 017
	// SMBC ธนาคารซูมิโตโม มิตซุย แบงกิ้ง 018
	// SCBT ธนาคารสแตนดาร์ดชาร์เตอร์ด (ไทย) 020
	// CIMBT ธนาคารซีไอเอ็มบีไทย 022
	// RHB ธนาคารอาร์ เอช บี 023
	// UOBT ธนาคารยูโอบี 024
	// BAY ธนาคารกรุงศรีอยุธยา 025
	// MEGA ธนาคารเมกะ 026
	// BOFA ธนาคารแห่งอเมริกา 027
	// IOB ธนาคารอินเดียน โอเวอร์ซีส์ 029
	// GSB ธนาคารออมสิน 030
	// HSBC ธนาคารเอชเอสบีซี 031
	// DBBK ธนาคารดอยซ์แบงก์ 032
	// GHB ธนาคารอาคารสงเคราะห์ 033
	// BAAC ธนาคารเพื่อการเกษตร 034
	// EXIM ธนาคารเอ็กซิมแบงก์ 035
	// MHCB ธนาคารมิซูโฮ 039
	// BNPP ธนาคารบีเอ็นพี พารีบาส์ 045
	// BOC ธนาคารแห่งประเทศจีน 052
	// IBANK ธนาคารอิสลาม 066
	// TISCO ธนาคารทิสโก้ 067
	// KKP ธนาคารเกียรตินาคินภัทร 069
	// ICBCT ธนาคารไอซีบีซี (ไทย) 070
	// TCRB ธนาคารไทยเครดิต 071
	// LHBANK ธนาคารแลนด์ แอนด์ เฮ้าส์ 073
	// SMTB ธนาคารซูมิโตโม มิตซุย ทรัสต์ 080
	// SME D BANK ธนาคารเอสเอ็มอีแบงก์ 098

	// MAP RemoteBankCode to LocalBankCode
	// supportedBankCodeList[localBankCode] = remoteBankCode
	supportedBankCodeList := make(map[string]string)
	supportedBankCodeList["BBL"] = "002"   // ธนาคารกรุงเทพ == "3" "กรุงเทพ" "bbl"
	supportedBankCodeList["KBANK"] = "004" // ธนาคารกสิกรไทย == "1" "กสิกรไทย" "kbank"
	supportedBankCodeList["KTB"] = "006"   // ธนาคารกรุงไทย == "5" "กรุงไทย" "ktb"
	// supportedBankCodeList["JPMCB"] = "008" // ธนาคารเจพีมอร์แกน == "100" "ไม่พบข้อมูลธนาคาร" "unknown"
	// supportedBankCodeList["OCBC"] = "009"  // ธนาคารโอเวอร์ซี-ไชนีส == "100" "ไม่พบข้อมูลธนาคาร" "unknown"
	supportedBankCodeList["TTB"] = "011"  // ธนาคารทหารไทยธนชาต == "6" "ทีเอ็มบีธนชาต" "ttb"
	supportedBankCodeList["SCB"] = "014"  // ธนาคารไทยพาณิชย์ == "2" "ไทยพาณิชย์" "scb"
	supportedBankCodeList["CITI"] = "017" // ธนาคารซิตี้แบงก์ == "18" "ซิตี้แบงก์" "citi"
	// supportedBankCodeList["SMBC"] = "018"  // ธนาคารซูมิโตโม มิตซุย แบงกิ้ง == "100" "ไม่พบข้อมูลธนาคาร" "unknown"
	supportedBankCodeList["SCBT"] = "020" // ธนาคารสแตนดาร์ดชาร์เตอร์ด (ไทย) == "19" "สแตนดาร์ดชาร์เตอร์ด" "scbt"
	supportedBankCodeList["CIMB"] = "022" // ธนาคารซีไอเอ็มบีไทย == "13" "ซีไอเอ็มบี" "cimb"
	// supportedBankCodeList["RHB"] = "023"   // ธนาคารอาร์ เอช บี == "100" "ไม่พบข้อมูลธนาคาร" "unknown"
	supportedBankCodeList["UOB"] = "024" // ธนาคารยูโอบี == "11" "ยูโอบี" "uob"
	supportedBankCodeList["BAY"] = "025" // ธนาคารกรุงศรีอยุธยา == "4" "กรุงศรี" "bay"
	// supportedBankCodeList["MEGA"] = "026"       // ธนาคารเมกะ == "100" "ไม่พบข้อมูลธนาคาร" "unknown"
	// supportedBankCodeList["BOFA"] = "027"       // ธนาคารแห่งอเมริกา == "100" "ไม่พบข้อมูลธนาคาร" "unknown"
	// supportedBankCodeList["IOB"] = "029"        // ธนาคารอินเดียน โอเวอร์ซีส์ == "100" "ไม่พบข้อมูลธนาคาร" "unknown"
	supportedBankCodeList["GSB"] = "030"  // ธนาคารออมสิน == "7" "ออมสิน" "gsb"
	supportedBankCodeList["HSBC"] = "031" // ธนาคารเอชเอสบีซี == "14" "เอชเอสบีซี" "hsbc"
	// supportedBankCodeList["DBBK"] = "032"       // ธนาคารดอยซ์แบงก์ == "100" "ไม่พบข้อมูลธนาคาร" "unknown"
	supportedBankCodeList["GHB"] = "033"  // ธนาคารอาคารสงเคราะห์ == "10" "อาคารสงเคราะห์" "ghb"
	supportedBankCodeList["BAAC"] = "034" // ธนาคารเพื่อการเกษตร == "8" "ธกส" "baac"
	// supportedBankCodeList["EXIM"] = "035"       // ธนาคารเอ็กซิมแบงก์ == "100" "ไม่พบข้อมูลธนาคาร" "unknown"
	// supportedBankCodeList["MHCB"] = "039"       // ธนาคารมิซูโฮ == "100" "ไม่พบข้อมูลธนาคาร" "unknown"
	// supportedBankCodeList["BNPP"] = "045"       // ธนาคารบีเอ็นพี พารีบาส์ == "100" "ไม่พบข้อมูลธนาคาร" "unknown"
	supportedBankCodeList["BKCHTHBK"] = "052" // ธนาคารแห่งประเทศจีน == "60" "ธนาคารแห่งประเทศจีน" "BKCHTHBK"
	supportedBankCodeList["ISBT"] = "066"     // ธนาคารอิสลาม == "16" "ธนาคารอิสลาม" "isbt"
	supportedBankCodeList["TISCO"] = "067"    // ธนาคารทิสโก้ == "17" "ทิสโก้" "tisco"
	supportedBankCodeList["KKP"] = "069"      // ธนาคารเกียรตินาคินภัทร == "9" "เกียรตินาคิน" "kkp"
	supportedBankCodeList["ICBC"] = "070"     // ธนาคารไอซีบีซี (ไทย) == "15" "ไอซีบีซี" "icbc"
	// supportedBankCodeList["TCRB"] = "071"       // ธนาคารไทยเครดิต == "100" "ไม่พบข้อมูลธนาคาร" "unknown"
	supportedBankCodeList["LH"] = "073" // ธนาคารแลนด์ แอนด์ เฮ้าส์ == "12" "แลนด์ แอนด์ เฮ้าส์" "lh"
	// supportedBankCodeList["SMTB"] = "080"       // ธนาคารซูมิโตโม มิตซุย ทรัสต์ == "100" "ไม่พบข้อมูลธนาคาร" "unknown"
	// supportedBankCodeList["SME D BANK"] = "098" // ธนาคารเอสเอ็มอีแบงก์ == "100" "ไม่พบข้อมูลธนาคาร" "unknown"

	// LOCAL_BANK_CODE
	// "id"	"name"	"code"	"icon_url"	"type_flag"	"country_code"	"use_currency"	"created_at"
	// "1"	"กสิกรไทย"	"kbank"	"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/kbank.png/public"	"********"	"TH"	"THB"	"2023-09-18 12:00:11"
	// "2"	"ไทยพาณิชย์"	"scb"	"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/scb.png/public"	"********"	"TH"	"THB"	"2023-09-18 12:00:11"
	// "3"	"กรุงเทพ"	"bbl"	"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/bbl.png/public"	"********"	"TH"	"THB"	"2023-09-18 12:00:11"
	// "4"	"กรุงศรี"	"bay"	"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/bay.png/public"	"********"	"TH"	"THB"	"2023-09-18 12:00:11"
	// "5"	"กรุงไทย"	"ktb"	"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/ktb.png/public"	"********"	"TH"	"THB"	"2023-09-18 12:00:11"
	// "6"	"ทีเอ็มบีธนชาต"	"ttb"	"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/ttb.png/public"	"********"	"TH"	"THB"	"2023-09-18 12:00:11"
	// "7"	"ออมสิน"	"gsb"	"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/gsb.png/public"	"********"	"TH"	"THB"	"2023-09-18 12:00:11"
	// "8"	"ธกส"	"baac"	"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/baac.png/public"	"********"	"TH"	"THB"	"2023-09-18 12:00:11"
	// "9"	"เกียรตินาคิน"	"kkp"	"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/kk.png/public"	"********"	"TH"	"THB"	"2023-09-18 12:00:11"
	// "10"	"อาคารสงเคราะห์"	"ghb"	"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/ghb.png/public"	"********"	"TH"	"THB"	"2023-09-18 12:00:11"
	// "11"	"ยูโอบี"	"uob"	"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/uob.png/public"	"********"	"TH"	"THB"	"2023-09-18 12:00:11"
	// "12"	"แลนด์ แอนด์ เฮ้าส์"	"lh"	"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/lh.png/public"	"********"	"TH"	"THB"	"2023-09-18 12:00:11"
	// "13"	"ซีไอเอ็มบี"	"cimb"	"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/cimb.png/public"	"********"	"TH"	"THB"	"2023-09-18 12:00:11"
	// "14"	"เอชเอสบีซี"	"hsbc"	"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/hsbc.png/public"	"********"	"TH"	"THB"	"2023-09-18 12:00:11"
	// "15"	"ไอซีบีซี"	"icbc"	"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/icbc.png/public"	"********"	"ALL"	"THB"	"2023-09-18 12:00:11"
	// "16"	"ธนาคารอิสลาม"	"isbt"	"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/isbt.png/public"	"********"	"TH"	"THB"	"2023-10-30 07:25:21"
	// "17"	"ทิสโก้"	"tisco"	"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/tisco.png/public"	"********"	"TH"	"THB"	"2023-10-30 07:25:21"
	// "18"	"ซิตี้แบงก์"	"citi"	"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/citi.png/public"	"********"	"TH"	"THB"	"2023-10-30 07:25:21"
	// "19"	"สแตนดาร์ดชาร์เตอร์ด"	"scbt"	"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/scbt.png/public"	"********"	"TH"	"THB"	"2023-10-30 07:25:21"
	// "20"	"TrueMoney Wallet"	"true"	"https://imagedelivery.net/xVhPJrN2Mvh3likGpmWIgg/cbgame/banks/true.png/public"	"********"	"TH"	"THB"	"2023-10-30 07:25:21"
	// "21"	"ธนาคารภายนอก"	"external"	"https://storage.googleapis.com/cbgame/banks/none.png"	"********"	"TH"	"THB"	"2023-12-21 07:49:30"
	// "50"	"ธนาคารการค้าต่างประเทศลาว (LAK)"	"BCEL"	"https://storage.googleapis.com/cbgame/banks/none.png"	"********"	"LAOS"	"LAK"	"2024-05-10 09:49:43"
	// "51"	"ธนาคารการค้าต่างประเทศลาว (THB)"	"BCELTH"	"https://storage.googleapis.com/cbgame/banks/none.png"	"********"	"LAOS"	"THB"	"2024-05-10 09:49:43"
	// "52"	"ธนาคารพัฒนาลาว"	"LDB"	"https://storage.googleapis.com/cbgame/banks/none.png"	"********"	"LAOS"	"LAK"	"2024-05-10 09:49:43"
	// "53"	"ธนาคารส่งเสริมการเกษตร"	"APB"	"https://storage.googleapis.com/cbgame/banks/none.png"	"********"	"LAOS"	"LAK"	"2024-05-10 09:49:43"
	// "54"	"ธนาคารร่วมธุรกิจลาว"	"BOL"	"https://storage.googleapis.com/cbgame/banks/none.png"	"********"	"LAOS"	"LAK"	"2024-05-10 09:49:43"
	// "55"	"ธนาคารร่วมพัฒนา"	"LDBBLALA XXX"	"https://storage.googleapis.com/cbgame/banks/none.png"	"********"	"LAOS"	"LAK"	"2024-05-10 09:49:43"
	// "56"	"ธนาคาร ST"	"ST"	"https://storage.googleapis.com/cbgame/banks/none.png"	"********"	"LAOS"	"LAK"	"2024-05-10 09:49:43"
	// "57"	"ธนาคาร BIC"	"BIC"	"https://storage.googleapis.com/cbgame/banks/none.png"	"********"	"LAOS"	"LAK"	"2024-05-10 09:49:43"
	// "58"	"ธนาคาร Maruhan Japan"	"Maruhan Japan"	"https://storage.googleapis.com/cbgame/banks/none.png"	"********"	"LAOS"	"LAK"	"2024-05-10 09:49:43"
	// "59"	"ธนาคาร Sacombank"	"Sacombank"	"https://storage.googleapis.com/cbgame/banks/none.png"	"********"	"LAOS"	"LAK"	"2024-05-10 09:49:43"
	// "60"	"ธนาคารแห่งประเทศจีน"	"BKCHTHBK"	"https://storage.googleapis.com/cbgame/banks/none.png"	"********"	"LAOS"	"LAK"	"2024-05-10 09:49:43"
	// "61"	"ธนาคาร Vietin"	"Vietin"	"https://storage.googleapis.com/cbgame/banks/none.png"	"********"	"LAOS"	"LAK"	"2024-05-10 09:49:43"
	// "62"	"ธนาคาร ACLEDA"	"ACLEDA"	"https://storage.googleapis.com/cbgame/banks/none.png"	"********"	"LAOS"	"LAK"	"2024-05-10 09:49:43"
	// "100"	"ไม่พบข้อมูลธนาคาร"	"unknown"	"https://storage.googleapis.com/cbgame/banks/none.png"	"********"	"TH"	"THB"	"2024-08-17 15:46:00"

	userBankCode = strings.ToUpper(userBankCode)
	if _, ok := supportedBankCodeList[userBankCode]; !ok {
		return "", badRequest("BANK_CODE_NOT_SUPPORT")
	}
	return supportedBankCodeList[userBankCode], nil
}

func (s paygateMeepayService) CreateMeepayDeposit(req model.MeepayDepositCreateRequest) (*model.MeepayOrderWebResponse, error) {

	var result model.MeepayOrderWebResponse

	// Ruled by Provider
	if req.Amount < model.MEEPAY_DEFMIN_DEPOSIT_AMOUNT || req.Amount > model.MEEPAY_DEFMAX_DEPOSIT_AMOUNT {
		log.Println("req.Amount", req.Amount)
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}

	pgAccount, err := s.GetMeepayAccount()
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}
	if !pgAccount.IsDepositEnabled {
		return nil, badRequest("PAYGATE_DEPOSIT_NOT_ENABLED")
	}
	// PrerequisitesMeepay
	if pgAccount.ApiEndPoint == "" || pgAccount.MerchantId == "" || pgAccount.SecretKey == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}

	// [********] get previse deposit order in last 5 minutes
	if pOrder, err := s.repo.CheckMeepayDepositOrderInLast5Minutes(req.UserId); err != nil {
		if err != gorm.ErrRecordNotFound {
			return nil, internalServerError(err)
		}
	} else if pOrder != nil {
		actionAtUtc := time.Now().UTC()
		if pOrder.Amount == req.Amount {
			if pOrder.CreatedAt.Add(time.Minute * 10).After(actionAtUtc) {
				result.OrderId = pOrder.Id
				result.UserId = pOrder.UserId
				result.OrderNo = pOrder.OrderNo
				result.Amount = pOrder.Amount
				result.TransferAmount = pOrder.TransferAmount
				result.TransactionStatus = pOrder.TransactionStatus
				// result.QrCode = pOrder.QrPromptpay
				// result.QrUrl = pOrder.QrPromptpay
				result.QrBase64 = pOrder.QrBase64
				result.ExtraPromptpayId = pOrder.ExtraPromptpayId
				result.CreatedAt = pOrder.CreatedAt
				return &result, nil
			}
		}
		// ภายใน 10 นาที ถ้ามีการทำรายการฝากเงิน จะไม่สามารถทำรายการได้
		if pOrder.CreatedAt.Add(time.Minute * 10).After(actionAtUtc) {
			// ยอดไม่ตรงต้องไปยกเลิก order เก่าก่อน
			return nil, badRequest("คุณทำรายการฝากเงินค้างอยู่ กรุณายกเลิกการทำรายการเดิมก่อน")
		}
	}

	webDomain := os.Getenv("WEB_DOMAIN")
	if webDomain == "" {
		return nil, errors.New("EMPTY_ENV_WEB_DOMAIN")
	}
	if !strings.HasPrefix(webDomain, "https://") {
		webDomain = "https://" + webDomain
	}
	webhookDomain := os.Getenv("CYBERGAME_WEBHOOK_ENDPOINT")
	if webhookDomain == "" {
		return nil, errors.New("EMPTY_ENV_CYBERGAME_WEBHOOK_ENDPOINT")
	}

	user, err := s.repo.GetUserBankDetailById(req.UserId)
	if err != nil {
		return nil, badRequest("INVALID_USER")
	}
	if user == nil || user.BankAccount == "" || user.BankCode == "" {
		return nil, badRequest("INVALID_USER_ACCOUNT")
	}
	remoteBankCode, err := GetMeepayCustomerBankUuid(user.BankCode)
	if err != nil || remoteBankCode == "" {
		return nil, badRequest("USER_BANK_NOT_SUPPORTED")
	}

	accountingRepo := repository.NewAccountingRepository(s.repo.GetDb())
	configWeb, err := s.repo.GetWebConfiguration()
	if err != nil {
		log.Println("CreateMeepayDeposit.GetWebConfiguration", err)
		return nil, badRequest("WEB_CONFIG_NOT_FOUND")
	}
	if user.Id != 0 && configWeb != nil {
		// [********] ฝากครั้งแรกขั้นต่ำ ปรับ flow เช็ค user ให้เช็คจากรายการฝากครั้งแรกจริงๆ ไม่เช็คจากรหัสที่ได้รับ
		if err := CheckFirstMinimunDeposit(accountingRepo, user.Id, req.Amount, *configWeb); err != nil {
			return nil, badRequest(fmt.Sprintf("ฝากครั้งแรกขั้นต่ำ %d บาท", configWeb.MinFirstMemberDeposit))
		}
	} else {
		log.Println("CreateMeepayDeposit.CheckFirstMinimunDeposit", "CANT_CHECK_FIRST_MINIMUM_DEPOSIT_ON_EMPTY_CONFIG")
	}

	// Payment Extra Data
	// NOPE

	// ===========================================================================================
	// Create DB Order
	var createBody model.MeepayOrderCreateBody
	createBody.UserId = req.UserId
	createBody.OrderTypeId = model.MEEPAY_ORDER_TYPE_DEPOSIT
	createBody.OrderNo = "" // Autogen
	createBody.Amount = req.Amount
	createBody.TransactionStatus = model.MEEPAY_ORDER_STATUS_PENDING
	insertId, err := s.repo.CreateDbMeepayOrder(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	// reget Order
	pendingOrder, err := s.repo.GetDbMeepayOrderById(*insertId)
	if err != nil {
		// SET AS ERROR
		remark := "Error GetDbMeepayOrderById, " + err.Error()
		if err := s.repo.UpdateDbMeepayOrderError(*insertId, remark); err != nil {
			log.Println("CreateMeepayDeposit.UpdateDbMeepayOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// Create REMOTE Order 6/9
	var remoteRequest model.MeepayDepositCreateRemoteRequest
	remoteRequest.Amount = req.Amount
	remoteRequest.Reference = pendingOrder.OrderNo
	remoteRequest.CallbackUrl = fmt.Sprintf("%s/meepay/dep-callback", webhookDomain)
	remoteRequest.BankId = user.BankAccount
	remoteRequest.BankName = user.Fullname
	remoteRequest.BankCode = remoteBankCode
	remoteResp, err := s.repo.MeepayDeposit(*pgAccount, remoteRequest)
	if err != nil {
		// SET AS ERROR
		remark := "Error MeepayDeposit, " + err.Error()
		if err := s.repo.UpdateDbMeepayOrderError(*insertId, remark); err != nil {
			log.Println("CreateMeepayDeposit.UpdateDbMeepayOrderError", err)
		}
		// SysLog At REPO
		return nil, internalServerError(err)
	}

	if strings.ToLower(remoteResp.Status) != "succeeded" {
		log.Println("CreateMeepayDeposit.MeepayDeposit.remoteResp", helper.StructJson(remoteResp))
		// SET AS ERROR
		remark := remoteResp.Message
		if len(remark) == 0 {
			remark = "UNKNOW_ERROR from depositWithMeepay"
		}
		if err := s.repo.UpdateDbMeepayOrderError(*insertId, remark); err != nil {
			log.Println("CreateMeepayDeposit.UpdateDbMeepayOrderError", err)
		}
		// SysLog
		if _, err := s.repo.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "CreateMeepayDeposit.MeepayDeposit",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(remoteRequest),
			JsonResponse: helper.StructJson(remoteResp),
		}); err != nil {
			log.Println("CreateMeepayDeposit.CreateSysLog", err)
		}
		return nil, internalServerError(fmt.Errorf(remark))
	}

	//	{
	//	  "transactionId": "94f9cb93-d862-455b-b43c-c9d73b566b32",
	//	  "merchant": "Payment",
	//	  "amount": "50.00",
	//	  "qrCode": "<qrCode>",
	//	  "reference": "ORDER12345",
	//	  "status": "succeeded",
	//	  "message": "Succeeded"
	//	}

	// Parse Float Amount
	transferAmount, err := strconv.ParseFloat(remoteResp.Amount, 64)
	if err != nil {
		// SET AS ERROR
		remark := "Error ParseFloat Amount, " + err.Error()
		if err := s.repo.UpdateDbMeepayOrderError(*insertId, remark); err != nil {
			log.Println("CreateMeepayDeposit.ParseFloat.UpdateDbMeepayOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// onCreate Success
	var updateBody model.MeepayOrderUpdateBody
	updateBody.TransactionNo = remoteResp.TransactionId
	updateBody.TransactionDate = time.Now().UTC()
	updateBody.TransactionStatus = model.MEEPAY_ORDER_STATUS_WAIT_PAYMENT
	updateBody.TransferAmount = transferAmount
	updateBody.QrPromptpay = remoteResp.QrCode
	if err := s.repo.UpdateDbMeepayOrder(*insertId, updateBody); err != nil {
		// SET AS ERROR
		remark := "Error UpdateDbMeepayOrder, " + err.Error()
		if err := s.repo.UpdateDbMeepayOrderError(*insertId, remark); err != nil {
			log.Println("CreateMeepayDeposit.UpdateDbMeepayOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	// reget Order
	waitPayOrder, err := s.repo.GetDbMeepayOrderById(*insertId)
	if err != nil {
		return nil, internalServerError(err)
	}

	result.OrderId = waitPayOrder.Id
	result.UserId = waitPayOrder.UserId
	result.OrderNo = waitPayOrder.OrderNo
	result.Amount = waitPayOrder.Amount
	result.TransferAmount = transferAmount
	result.TransactionStatus = waitPayOrder.TransactionStatus
	result.QrCode = waitPayOrder.QrPromptpay
	result.ExtraPromptpayId = waitPayOrder.ExtraPromptpayId
	result.CreatedAt = waitPayOrder.CreatedAt

	// Serve QR Code as Base64
	imgData, err := qrcode.Encode(waitPayOrder.QrPromptpay, qrcode.Medium, 256)
	if err != nil {
		log.Println("CreateMeepayDeposit.qrcode.Encode.ERROR=unable to encode png: %w", err)
		return &result, nil
	}
	// encode to base64
	img, err := png.Decode(bytes.NewReader(imgData))
	if err != nil {
		log.Println("CreateMeepayDeposit.qrcode.Decode.ERROR=unable to decode jpeg: %w", err)
		return &result, nil
	}
	var buf bytes.Buffer
	if err := png.Encode(&buf, img); err != nil {
		log.Println("CreateMeepayDeposit.qrcode.Encode.ERROR=unable to encode png: %w", err)
		return &result, nil
	}
	result.QrBase64 = base64.StdEncoding.EncodeToString(buf.Bytes())

	return &result, nil
}

func (s paygateMeepayService) CreateMeepayWithdraw(req model.MeepayWithdrawCreateRequest) (*int64, error) {

	// Ruled by Provider
	if req.Amount < model.MEEPAY_DEFMIN_WITHDRAW_AMOUNT || req.Amount > model.MEEPAY_DEFMAX_WITHDRAW_AMOUNT {
		return nil, errors.New("INVALID_AMOUNT_RANGE")
	}

	webhookDomain := os.Getenv("CYBERGAME_WEBHOOK_ENDPOINT")
	if webhookDomain == "" {
		return nil, errors.New("EMPTY_ENV_CYBERGAME_WEBHOOK_ENDPOINT")
	}

	pgAccount, err := s.GetMeepayAccount()
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}
	if !pgAccount.IsWithdrawEnabled {
		return nil, badRequest("PAYGATE_WITHDRAW_NOT_ENABLED")
	}
	// PrerequisitesMeepay
	if pgAccount.ApiEndPoint == "" || pgAccount.MerchantId == "" || pgAccount.SecretKey == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}

	user, err := s.repo.GetUserBankDetailById(req.UserId)
	if err != nil {
		return nil, badRequest("INVALID_USER")
	}
	if user == nil || user.BankAccount == "" || user.BankCode == "" {
		return nil, badRequest("INVALID_USER_ACCOUNT")
	}
	withdrawBankCode, err := GetMeepayCustomerBankUuid(user.BankCode)
	if err != nil || withdrawBankCode == "" {
		return nil, badRequest("USER_BANK_NOT_SUPPORTED")
	}

	// Payment Extra Data
	// NOPE

	// ===========================================================================================
	// CREATE Order
	var createBody model.MeepayOrderCreateBody
	createBody.RefId = &req.RefId
	createBody.UserId = req.UserId
	createBody.OrderTypeId = model.MEEPAY_ORDER_TYPE_WITHDRAW
	createBody.OrderNo = "" // Autogen
	createBody.Amount = req.Amount
	createBody.TransactionStatus = "" // Autogen
	insertId, err := s.repo.CreateDbMeepayOrder(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	// reget Order
	pendingOrder, err := s.repo.GetDbMeepayOrderById(*insertId)
	if err != nil {
		// SET AS ERROR
		remark := "Error GetDbMeepayOrderById, " + err.Error()
		if err := s.repo.UpdateDbMeepayOrderError(*insertId, remark); err != nil {
			log.Println("CreateMeepayWithdraw.UpdateDbMeepayOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// Create MEEPAY Order
	var remoteRequest model.MeepayWithdrawCreateRemoteRequest
	remoteRequest.Amount = req.Amount
	remoteRequest.Reference = pendingOrder.OrderNo
	remoteRequest.CallbackUrl = fmt.Sprintf("%s/meepay/wid-callback", webhookDomain)
	remoteRequest.BankId = user.BankAccount
	remoteRequest.BankName = user.Fullname
	remoteRequest.BankCode = withdrawBankCode
	remoteResp, err := s.repo.MeepayWithdraw(*pgAccount, remoteRequest)
	if err != nil {
		// SET AS ERROR
		remark := "Error MeepayWithdraw, " + err.Error()
		if err := s.repo.UpdateDbMeepayOrderError(*insertId, remark); err != nil {
			log.Println("MeepayWithdraw.UpdateDbMeepayOrderError", err)
		}
		// SysLog At REPO
		return nil, internalServerError(err)
	}

	// fmt.Println("CreateMeepayWithdraw.remoteResp=", helper.StructJson(remoteResp))

	if strings.ToLower(remoteResp.Status) != "succeeded" {
		log.Println("MeepayWithdraw.CreateMeepayWithdraw.remoteResp=", helper.StructJson(remoteResp))
		// SET AS ERROR
		remark := remoteResp.Message
		if len(remark) == 0 {
			remark = "UNKNOW_ERROR from withdrawWithMeepay"
		}
		if err := s.repo.UpdateDbMeepayOrderError(*insertId, remark); err != nil {
			log.Println("MeepayWithdraw.UpdateDbMeepayOrderError", err)
		}
		// SysLog
		if _, err := s.repo.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "CreateMeepayWithdraw.MeepayWithdraw",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(remoteRequest),
			JsonResponse: helper.StructJson(remoteResp),
		}); err != nil {
			log.Println("CreateMeepayWithdraw.CreateSysLog", err)
		}
		return nil, internalServerError(fmt.Errorf(remark))
	}

	// onCreate Success PENDING -> WAIT_PAYMENT
	var updateBody model.MeepayOrderUpdateBody
	updateBody.TransactionNo = remoteResp.TransactionId
	updateBody.TransactionDate = time.Now().UTC()
	updateBody.TransactionStatus = model.MEEPAY_ORDER_STATUS_WAIT_PAYMENT
	updateBody.QrPromptpay = remoteResp.Message
	if err := s.repo.UpdateDbMeepayOrder(*insertId, updateBody); err != nil {
		// SET AS ERROR
		remark := "Error UpdateDbMeepayOrder, " + err.Error()
		if err := s.repo.UpdateDbMeepayOrderError(*insertId, remark); err != nil {
			log.Println("CreateMeepayWithdraw.UpdateDbMeepayOrderError", err)
		}
		return nil, internalServerError(err)
	}
	return insertId, nil
}

func CreateCustomerDepositFromMeepayOrder(repo repository.MeepayRepository, orderId int64, adminId int64) (*int64, error) {

	// ** ทุกรายการจะต้องเช็คสถานะใหม่ทั้งหมด เพราะ admin แย่งกดอนุมัติได้
	item, err := repo.GetRawMeepayPendingDepositOrderById(orderId)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, badRequest("ORDER_NOT_FOUND")
		}
		return nil, internalServerError(err)
	}

	// Check BankTransactionStatus = 'PENDING'
	if item.BankTransactionStatus != "PENDING" {
		return nil, badRequest("ORDER_NOT_PENDING")
	}

	return createCustomerDepositFromMeepay(repo, *item, adminId)
}

func createCustomerDepositFromMeepay(repo repository.MeepayRepository, item model.MeepayOrderResponse, adminId int64) (*int64, error) {

	var externalNoti model.NotifyExternalNotificationRequest
	actionAt := time.Now().UTC()
	// confirmByUserId := int64(0)

	depositAmount := item.Amount

	user, err := repo.GetUserBankDetailById(item.UserId)
	if err != nil {
		log.Println("createCustomerDepositFromMeepay.GetUserBankDetailByMemberCode", err)
		return nil, internalServerError(err)
	}

	var promotionWebUserId int64
	if getPromotionWebUser, err := repo.GetDepositCurrentProcessingUserPromotion(item.UserId); err != nil {
		log.Println("createCustomerDepositFromMeepay.GetDepositCurrentProcessingUserPromotion", err)
	} else if getPromotionWebUser != nil {
		promotionWebUserId = getPromotionWebUser.Id
	}

	// MemberCode
	if user.MemberCode == "" {
		memberCode, err := GenUniqueUserMemberCode(repository.NewUserRepository(repo.GetDb()), user.Id)
		if err != nil {
			log.Println("createCustomerDepositFromMeepay.GenUniqueUserMemberCode", err)
			return nil, internalServerError(err)
		}
		user.MemberCode = *memberCode
	}

	// layer — 2024-04-02 at 10:06 AM
	// ถ้าแก้ตามนี้แล้วน่าจะไม่เจอ ถ้าเคสกันซ้ำเราครอบคลุม
	// @Dunk @TULA
	// กันซ้ำคือ heng ไปซ้ำกับแอดมินเติมมือ
	// ----
	// เงื่อนไขกันซ้ำ
	// 1 หารายการฝากสำเร็จล่าสุดของยูสนั้น
	// 2 ถ้ามีรายการล่าสุดที่เป็นเติมมือ
	// -เช็คเวลาเติม ต้องมากกว่าเวลาโอนในสลิป
	// -ยอดเงินเท่ากัน
	// **ถ้าเข้าเงื่อนไขทั้งหมด คือแสดงว่า webhook เข้าทีหลังแอดมินเติมมือ
	// ต้อง bypass ไม่ให้เติมอีก และอัพเดท status ว่าจับคู่ได่แล้ว
	// ---
	// วิธีนี้น่าจะใช้ได้กับทุก webhook (heng, fastbank) ที่มีเวลาโอนสลิปมาให้
	duplicateRemark := "INIT"
	hasDuplicateDeposit := false
	manualDeposit, err := repo.GetLastestBankAdminManualDepositTransaction(user.Id)
	if err != nil {
		if err.Error() != recordNotFound {
			log.Println("createCustomerDepositFromMeepay.GetLastestBankAdminManualDepositTransaction", err)
			return nil, internalServerError(err)
		}
		// NOT_FOUND = ถ้าไม่เจอ ให้ทำต่อ
		duplicateRemark = "OK_NO_MANUAL_DEPOSIT"
	}
	if manualDeposit != nil && item.TransactionDate != nil {
		if manualDeposit.TransferAt.After(*item.TransactionDate) {
			// ถ้าเวลาเติมมือมากกว่าเวลาโอนในสลิป
			if manualDeposit.CreditAmount == depositAmount {
				// ถ้ายอดเงินเท่ากัน
				duplicateRemark = "STOP_MANUAL_DEPOSIT_AFTER_AND_SAME_AMOUNT"
				hasDuplicateDeposit = true
			} else {
				duplicateRemark = "OK_MANUAL_DEPOSIT_AMOUNT_NOT_MATCH"
			}
		} else {
			duplicateRemark = "OK_MANUAL_DEPOSIT_BEFORE_WITHDRAW"
		}
	}
	if duplicateRemark != "INIT" {
		// WebhookLog
		var createBody2 model.MeepayWebhookCreateBody
		createBody2.Name = "MEEPAY_DEPOSIT_DUPLICATE_CHECKER"
		createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
			"item":                item,
			"method":              "createCustomerDeposit",
			"hasDuplicateDeposit": hasDuplicateDeposit,
			"error":               duplicateRemark,
			"manualDeposit":       manualDeposit,
		})
		if _, err = repo.CreateMeepayWebhook(createBody2); err != nil {
			log.Println("Error CreateMeepayWebhook.CreateMeepayWebhook", err)
		}
		if hasDuplicateDeposit {
			return nil, internalServerError(fmt.Errorf("DUPLICATE_DEPOSIT"))
		}
	}

	pgAccount, err := GetMeepayAccount(repo)
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}

	// ===================================================
	paygateRepo := repository.NewPaymentGatewayRepository(repo.GetDb())
	accountingRepo := repository.NewAccountingRepository(repo.GetDb())
	luckyWheelRepo := repository.NewActivityLuckyWheelRepository(repo.GetDb())
	promotionWebRepo := repository.NewPromotionWebRepository(repo.GetDb())
	notiRepo := repository.NewNotificationRepository(repo.GetDb())

	// Affiliate + Alliance Income
	member, err := repo.GetMemberById(user.Id)
	if err != nil {
		return nil, internalServerError(err)
	}
	if member.UserTypeName == "NONE" {
		// ถ้าฝากครั้งแรก จะได้เป็น AFFILIATE + REFER COMMISION
		if err := UserFirstDepositCommission(accountingRepo, *member, depositAmount); err != nil {
			log.Println("CreateFristTimeDepositRecord.UserFirstDepositCommission.ERROR", err)
			return nil, internalServerError(err)
		}
	}

	isAutoDeposit := true // Auto ฝาก นับตั้งแต่ได้รับยอดฝาก จนได้ยืนยันเครดิต
	if adminId != 0 {
		isAutoDeposit = false
	}

	// CREATE BANK TRANSACTION
	var createBankTransaction model.BankTransactionCreateBody
	createBankTransaction.MemberCode = user.MemberCode
	createBankTransaction.UserId = user.Id
	// createBankTransaction.StatementId = 0
	createBankTransaction.TransactionTypeId = model.TRANSACTION_TYPE_DEPOSIT
	// createBankTransaction.FromAccountName = "" // empty?
	createBankTransaction.ToAccountName = &pgAccount.TypeName
	createBankTransaction.ToAccountNumber = &pgAccount.ShopName
	createBankTransaction.PromotionId = &promotionWebUserId
	createBankTransaction.CreditAmount = depositAmount
	// createBankTransaction.BonusAmount = 0
	// createBankTransaction.BonusReason = nil
	createBankTransaction.DepositChannel = "MEEPAY PAYMENT GATEWAY"
	createBankTransaction.TransactionStatusId = model.TRANS_STATUS_PENDING
	createBankTransaction.CreatedByAdminId = 0
	createBankTransaction.IsAutoCredit = isAutoDeposit
	createBankTransaction.TransferAt = &actionAt
	// create transaction
	transId, err := repo.InsertBankTransaction(createBankTransaction)
	if err != nil {
		log.Println("createCustomerDepositFromMeepay.InsertBankTransaction", err)
		return nil, internalServerError(err)
	}

	// var actionStatement model.BankStatementMatchRequest
	// actionStatement.StatementId = *bankstatementId
	// actionStatement.UserId = user.Id
	// actionStatement.ConfirmedAt = time
	// actionStatement.ConfirmedByAdminId = &req.CreateByUserId
	// if err := s.serviceAccounting.SetStatementOwnerMatched(*bankstatementId, actionStatement, model.USE_ENDING_NOTI); err != nil {
	// 	log.Println("createCustomerDepositFromMeepay.SetStatementOwnerMatched", err)
	// 	return nil, internalServerError(err)
	// }

	// DECREASE FASTBANK CREDIT 2.FROM create auto DEPOSIT
	// if err := repoAccounting.DecreaseFastbankCredit(1); err != nil {
	// 	log.Println("createCustomerDepositFromMeepay.DECREASE_FASTBANK_CREDIT_ERROR", err)
	// }

	var confirmDeposit model.BankConfirmDepositRequest
	confirmDeposit.TransferAt = &actionAt
	confirmDeposit.ConfirmedAt = actionAt
	confirmDeposit.ConfirmedByAdminId = &adminId
	if err := confirmDepositTransaction(paygateRepo, *transId, confirmDeposit); err != nil {
		log.Println("createCustomerDepositFromMeepay.confirmDepositTransaction", err)
		return nil, internalServerError(err)
	}

	// UPDATE HENG ORDER - BANK SUCCESS
	if err := repo.UpdateMeepayOrderBankSucess(item.Id, *transId, adminId); err != nil {
		log.Println("createCustomerDepositFromMeepay.UpdatePaygateOrderBankSucess", err)
	}

	isFirstDeposit := repo.IsFirstDeposit(user.Id)
	if isFirstDeposit {
		var bonusReq model.UserFirstDepositCreateRequest
		bonusReq.UserId = user.Id
		bonusReq.TransactionId = transId
		bonusReq.TransferAt = actionAt
		bonusReq.Amount = depositAmount
		bonusReq.Remark = "createCustomerDepositFromMeepay"
		if err := SetFirstDepositBonus(accountingRepo, isFirstDeposit, bonusReq); err != nil {
			webhookLogMessage := fmt.Sprintf("createCustomerDepositFromMeepay.userFirstDepositBonus, ERROR: %s", err)
			log.Println("SetFirstDepositBonus", webhookLogMessage)
			// return nil, errors.New(webhookLogMessage)
		}
	}

	// [USER_CREDIT]
	var userCreditReq model.UserTransactionCreateRequest
	userCreditReq.UserId = user.Id
	userCreditReq.RefId = transId
	userCreditReq.TransferAt = &actionAt
	userCreditReq.PromotionId = &promotionWebUserId
	userCreditReq.TypeId = model.CREDIT_TYPE_DEPOSIT
	// userCreditReq.AccountId = &bankAccount.Id
	userCreditReq.Detail = "MEEPAY PAYMENT GATEWAY"
	userCreditReq.Amount = depositAmount
	userCreditReq.CreateBy = &adminId
	userCreditReq.ConfirmBy = &adminId
	userCreditReq.IsAdjustAuto = isAutoDeposit
	userCreditReq.PaymentMerchatId = &pgAccount.ProviderId
	if agentResp, err := repo.IncreaseUserCredit(userCreditReq); err != nil {
		log.Println("createCustomerDepositFromMeepay.IncreaseUserCredit", err)
		return nil, internalServerError(err)
	} else {
		// AGENT_SUCCESS
		if err := repo.UpdateDepositTransactionStatusFromAgent(*transId, *agentResp); err != nil {
			log.Println("createCustomerDepositFromMeepay.UpdateDeporsitTransactionStatusFromAgent", err)
		}
	}

	// [Lucky Wheel] ตอนฝากละได้โบนัส
	var luckyWheelBody model.ActivityLuckyWheelRoundUserRequest
	luckyWheelBody.UserId = user.Id
	luckyWheelBody.ConditionId = model.ACTIVITY_LUCKY_WHEEL_SETTING_CONDITION_DEPOSIT
	luckyWheelBody.ConditionAmount = depositAmount
	if err := CreateRoundActivityLuckyWheel(luckyWheelRepo, luckyWheelBody); err != nil {
		log.Println("createCustomerDepositFromMeepay.CreateRoundActivityLuckyWheel", err)
	}

	// [TIER]
	if err := repo.IncreaseUserTierDepositAmount(user.Id, depositAmount); err != nil {
		log.Println("MatchStatementOwner.IncreaseUserTierDepositAmount", err)
	}

	var checkUserPromotionBody model.CheckUserPromotionBody
	checkUserPromotionBody.UserId = user.Id
	_, err = CheckUserPromotion(promotionWebRepo, checkUserPromotionBody)
	if err != nil {
		log.Println("createCustomerDepositFromMeepay.CheckUserPromotion", err)
	}
	// [turnOver] ถ้ามีการฝากเงิน ให้สร้าง TurnOver
	turnoverShare := repository.NewTurnoverRepository(repo.GetDb())
	if err := CreateTurnOverFromSuccessDeposit(turnoverShare, user.Id, depositAmount, *transId); err != nil {
		log.Println("createCustomerDeposit.CreateTurnOverFromSuccessDeposit", err)
	}

	// ===================================================

	// [ notify]
	notiAtUtc := time.Now().UTC()
	externalNoti.TypeNotify = model.IsDepositAfterCredit
	externalNoti.TransId = transId
	externalNoti.Amount = depositAmount
	externalNoti.MemberCode = user.MemberCode
	externalNoti.UserCredit = user.Credit + depositAmount
	externalNoti.ConfirmedByAdminId = 0
	externalNoti.TransferDateTime = notiAtUtc.Add(7 * time.Hour).Format("2006-01-02 15:04:05")
	externalNoti.ActionTime = notiAtUtc.Add(7 * time.Hour).Format("2006-01-02 15:04:05")
	endTime := notiAtUtc
	elapsed := endTime.Sub(actionAt)
	elapsedSeconds := elapsed.Seconds()
	timeElapsed := fmt.Sprintf("%.2f", elapsedSeconds)
	externalNoti.TimerCounter = timeElapsed

	externalNoti.WebScoket.UserID = user.Id
	externalNoti.WebScoket.Amount = depositAmount
	externalNoti.WebScoket.MemberCode = user.MemberCode
	externalNoti.WebScoket.AlertType = "DEPOSIT"
	if err := repo.UpdateAutoProcessTimer(timeElapsed, *transId); err != nil {
		return nil, nil
	}
	if err := ExternalNotification(notiRepo, externalNoti); err != nil {
		log.Println("FailedNotify", err)
	}
	return nil, nil
}

func approveCustomerWithdrawFromMeepay(repo repository.MeepayRepository, transId int64) (*int64, error) {

	withdrawTrans, err := repo.GetBankTransactionById(transId)
	if err != nil {
		log.Println("approveCustomerWithdrawFromMeepay.GetUserBankDetailById", err)
		return nil, internalServerError(err)
	}

	// ============================= ON_SUCCESS =================================
	confirmAtUtc := time.Now().UTC()
	if withdrawTrans.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		// [create transaction action success]
		var createConfirm model.CreateSuccessTransferWithdrawRequest
		createConfirm.TransactionId = withdrawTrans.Id
		createConfirm.ConfirmedAt = confirmAtUtc
		var setIdAuto int64 = 0
		createConfirm.ConfirmedByAdminId = &setIdAuto
		if _, err := CreateSuccessTransferWithdraw(repository.NewBankingRepository(repo.GetDb()), createConfirm); err != nil {
			log.Println("approveCustomerWithdrawFromMeepay.CreateSuccessTransferWithdraw", err)
			return nil, nil
		}
		// [Show Withdraw USER_TRANSACTION]
		var showUserTrans model.UserTransactionShowUpdate
		showUserTrans.TransactionId = withdrawTrans.Id
		showUserTrans.ConfirmedAt = &createConfirm.ConfirmedAt
		showUserTrans.ConfirmAdminId = createConfirm.ConfirmedByAdminId
		if err := repo.ShowUserTransaction(showUserTrans); err != nil {
			log.Println("approveCustomerWithdrawFromMeepay.ShowUserTransaction", err)
			return nil, nil
		}
		// [update transaction status]
		secondUsed := "0.0" // timing
		if withdrawTrans.IsAutoCredit && withdrawTrans.TransferAt != nil {
			seconds := confirmAtUtc.Sub(withdrawTrans.TransferAt.UTC()).Seconds()
			secondUsed = fmt.Sprintf("%.2f", seconds)
		}
		if err := repo.UpdateTransactionStatusTransferingToSuccess(withdrawTrans.Id, secondUsed); err != nil {
			log.Println("approveCustomerWithdrawFromMeepay.UpdateTransactionStatusTransferingToSuccess", err)
			return nil, internalServerError(err)
		}

		// [notify SUCCESS]
		var externalNoti model.NotifyExternalNotificationRequest
		notiRepo := repository.NewNotificationRepository(repo.GetDb())
		externalNoti.TypeNotify = model.IsWithdrawalCreditSuccess
		externalNoti.TransId = &transId
		externalNoti.Amount = withdrawTrans.CreditAmount
		externalNoti.MemberCode = withdrawTrans.MemberCode
		externalNoti.UserCredit = withdrawTrans.CreditAmount
		externalNoti.ConfirmedByAdminId = 0
		externalNoti.TimerCounter = secondUsed
		externalNoti.TransferDateTime = withdrawTrans.TransferAt.Add(7 * time.Hour).Format("2006-01-02 15:04:05")
		externalNoti.ActionTime = confirmAtUtc.Add(7 * time.Hour).Format("2006-01-02 15:04:05")
		if err := ExternalNotification(notiRepo, externalNoti); err != nil {
			log.Println("FailedNotify", err)
		}

	}
	return nil, nil
}

// func rollbackCustomerWithdrawFromMeepay(repo repository.MeepayRepository, transId int64) (*int64, error) {

// 	withdrawTrans, err := repo.GetBankTransactionById(transId)
// 	if err != nil {
// 		log.Println("rollbackCustomerWithdrawFromMeepay.GetBankTransactionById", err)
// 		return nil, internalServerError(err)
// 	}

// 	// ============================= ON_SUCCESS =================================
// 	if withdrawTrans.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_TRASNFERING {
// 		// [update transaction status]
// 		if err := repo.RollbackTransactionStatusTransferingToConfirmed(withdrawTrans.Id); err != nil {
// 			log.Println("approveCustomerWithdrawFromMeepay.RollbackTransactionStatusTransferingToConfirmed", err)
// 			return nil, internalServerError(err)
// 		}
// 	}
// 	return nil, nil
// }

func (s paygateMeepayService) CreateSystemLog(name string, req interface{}) error {

	// [SYSLOG] HACKED
	_, err := s.repo.CreateSystemLog(model.SystemLogCreateBody{
		Name:   name,
		Status: "HACKING",
		JsonRequest: helper.StructJson(map[string]interface{}{
			"req": req,
		}),
	})
	if err != nil {
		log.Println(name, "CreateSystemLog.ERROR", err.Error())
	}
	return nil
}

func (s paygateMeepayService) CancelWithdrawFromMeepay(transId int64, adminId int64) error {

	withdrawTrans, err := s.repo.GetBankTransactionById(transId)
	if err != nil {
		log.Println("rollbackWithdrawFromMeepay.GetBankTransactionById", err)
		return internalServerError(err)
	}
	if withdrawTrans.TransactionStatusId != model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		return internalServerError(errors.New("ไม่อยู๋ในสถานะที่สามารถยกเลิกได้"))
	}

	user, err := s.repo.GetUserBankDetailById(withdrawTrans.UserId)
	if err != nil {
		return internalServerError(err)
	}

	paygateOrder, err := s.repo.GetDbMeepayWithdrawOrderByRefId(transId)
	if err != nil {
		log.Println("CancelWithdrawFromMeepay.GetDbMeepayWithdrawOrderByRefId", err)
		return internalServerError(err)
	}
	waitStatus := "ERROR"
	if paygateOrder.TransactionStatus != waitStatus {
		return internalServerError(errors.New("ไม่อยู๋ในสถานะที่สามารถยกเลิกได้"))
	}

	returnAmount := withdrawTrans.CreditAmount

	// race condition deposit
	actionAt := time.Now().UTC()
	var createBody model.RaceActionCreateBody
	createBody.Name = "ADMIN_CANCEL_WITHDRAW"
	createBody.JsonRequest = helper.StructJson(map[string]interface{}{
		"transId":      transId,
		"returnAmount": returnAmount,
	})
	createBody.Status = "PENDING"
	createBody.ActionKey = fmt.Sprintf("WITHDRAW_CANCEL_T%sU%dID%d", actionAt.Format("**********"), user.Id, transId)
	createBody.UnlockAt = actionAt.Add(time.Second * 1)
	if _, err := s.repo.GetRaceActionIdByActionKey(createBody.ActionKey); err != nil {
		log.Println("CancelWithdrawFromMeepay.GetRaceActionIdByActionKey", err)
		if err != gorm.ErrRecordNotFound {
			return internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
		}
	} else {
		return internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
	}

	// create race condition
	actionId, err := s.repo.CreateRaceCondition(createBody)
	if err != nil {
		log.Println("CancelWithdrawFromMeepay.ERROR.CreateRaceCondition", err)
		return internalServerError(errors.New("CANNOT_CREATE_ACTION_LUCKY_WHEEL_ROUND"))
	}
	if actionId == 0 {
		return internalServerError(errors.New("CANNOT_CREATE_ACTION_LUCKY_WHEEL_ROUND"))
	}

	// SET ORDER AS ERROR
	remark := "Admin Cancel Withdraw"
	if err := s.repo.UpdateDbMeepayOrderError(paygateOrder.Id, remark); err != nil {
		log.Println("CreateMeepayWithdraw.UpdateDbMeepayOrderError", err)
	}

	if withdrawTrans.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		// [********] check user credit transaction ว่าตรงไหมกับเงื่อนไข (isShow = false)
		userTransaction, err := s.repo.GetUserWithdrawCreditTransactionByRefId(withdrawTrans.Id, returnAmount)
		if err != nil {
			if err.Error() == recordNotFound {
				log.Println("CancelWithdrawCredit.GetUserCreditTransactionById", err)
				return badRequest("ไม่ตรงกับเงือนไข")
			}
			return internalServerError(err)
		}
		if userTransaction != nil {
			var userCreditReq model.UserTransactionCreateRequest
			userCreditReq.UserId = user.Id
			userCreditReq.TransferAt = withdrawTrans.TransferAt
			userCreditReq.RefId = &withdrawTrans.Id
			userCreditReq.TypeId = model.CREDIT_TYPE_DEPOSIT
			userCreditReq.Amount = returnAmount
			IsShow := false
			userCreditReq.IsShow = &IsShow
			userCreditReq.ConfirmBy = &adminId
			if agentResp, err := s.repo.IncreaseUserCredit(userCreditReq); err != nil {
				log.Println("CancelWithdrawCredit.IncreaseUserCredit", err)
				return internalServerError(err)
			} else if !agentResp.AgentSuccess {
				// [********] error from agent will be in failed
				// if err := s.repoAccounting.UpdateDeporsitTransactionStatusFromAgent(*transId, *agentResp); err != nil {
				//     log.Println("CancelWithdrawCredit.UpdateDeporsitTransactionStatusFromAgent", err)
				// }
				// Set FastBank-READ ** ไม่มี เพราะเป็นการสร้างรายการฝากเอง
				agentFail := errors.New("ทำรายการเครดิตไม่สำเร็จ")
				log.Println("CancelWithdrawCredit.IncreaseUserCredit", agentFail)
				return internalServerError(agentFail)
			}
		}
	}

	// remark := "ADMIN_CANCEL_WITHDRAW"
	var updateApprovedBy model.UpdateUserTransactionStatusRequest
	updateApprovedBy.Id = withdrawTrans.Id
	updateApprovedBy.CanceledAt = actionAt
	updateApprovedBy.CanceledByAdminId = adminId
	updateApprovedBy.TransactionStatusId = model.TRANS_STATUS_WITHDRAW_CANCELED
	updateApprovedBy.CancelRemark = &remark
	err = s.repo.UpdateUserTransactionStatus(updateApprovedBy)
	if err != nil {
		return internalServerError(err)
	}

	var createConfirm model.CreateSuccessTransferWithdrawRequest
	createConfirm.TransactionId = withdrawTrans.Id
	createConfirm.ConfirmedAt = actionAt
	createConfirm.ConfirmedByAdminId = &adminId
	if _, err := CreateCanceledTransferWithdraw(repository.NewBankingRepository(s.sharedDb), createConfirm); err != nil {
		return err
	}

	return nil
}

func (s paygateMeepayService) CreateMeepayDepositWebhook(req model.MeepayWebhookRequest) (*int64, error) {

	var createBody model.MeepayWebhookCreateBody
	createBody.Name = "MEEPAY_DEPOSIT_CALLBACK"
	createBody.JsonPayload = req.JsonPayload
	insertId, err := s.repo.CreateMeepayWebhook(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// DEPOSIT
	var remoteResp model.MeepayDepositWebhookResponse
	errJson := json.Unmarshal([]byte(req.JsonPayload), &remoteResp)
	if errJson != nil {
		log.Println("CreateMeepayDepositWebhook.Error unmarshal json", errJson)
		// Try2
		var remoteResp2 model.MeepayDepositWebhook2Response
		errJson2 := json.Unmarshal([]byte(req.JsonPayload), &remoteResp2)
		if errJson2 != nil {
			log.Println("CreateMeepayDepositWebhook.Error unmarshal json2", errJson2)
			return nil, internalServerError(errJson2)
		}
		// Check Amount
		transferAmount, err := strconv.ParseFloat(remoteResp2.Amount, 64)
		if err != nil {
			log.Println("CreateMeepayDepositWebhook.Error ParseFloat", err)
			return nil, internalServerError(err)
		}
		if transferAmount <= 0 {
			log.Println("CreateMeepayDepositWebhook.Error Amount <= 0", remoteResp2.Amount)
			return nil, badRequest("MEEPAY_DEPOSIT_HOOK_AMOUNT_ZERO")
		}

		remoteResp.Amount = transferAmount
		remoteResp.BankCode = remoteResp2.BankCode
		remoteResp.BankId = remoteResp2.BankId
		remoteResp.BankName = remoteResp2.BankName
		remoteResp.Hash = remoteResp2.Hash
		remoteResp.Merchant = remoteResp2.Merchant
		remoteResp.Message = remoteResp2.Message
		remoteResp.Reference = remoteResp2.Reference
		remoteResp.Status = remoteResp2.Status
		remoteResp.TransactionId = remoteResp2.TransactionId
		remoteResp.TransferDate = remoteResp2.TransferDate

	}
	if remoteResp.TransactionId == "" || remoteResp.Reference == "" {
		log.Println("CreateMeepayDepositWebhook.NoRef", helper.StructJson(remoteResp))
		return nil, badRequest("MEEPAY_DEPOSIT_HOOK_NOREF")
	}

	// Check Response Status. [succeeded]
	successStatus := strings.ToUpper(strings.TrimSpace(remoteResp.Status))
	if successStatus == "SUCCEEDED" || successStatus == "SUCCESS" {
		successStatus = model.MEEPAY_ORDER_STATUS_SUCCESS // Always use SUCCESS
	} else {
		log.Println("CreateMeepayDepositWebhook.remoteResp.desc.ELSE", successStatus)
		// เก็บตามจริง successStatus = "ERROR"
		// CreateMeepayDepositWebhook.remoteResp.desc.ELSE EXPIRED
	}

	// Service Race Condition by Ref1(MchOrderNo) + perStatus
	acAtUtc := time.Now().UTC()
	var rcCreateBody model.RaceActionCreateBody
	rcCreateBody.Name = "CreateMeepayDepositWebhook"
	rcCreateBody.JsonRequest = helper.StructJson(req)
	rcCreateBody.Status = "PENDING"
	// ActionKey = Ref2-MINUTE
	rcCreateBody.ActionKey = fmt.Sprintf("PG16D%s-%s", acAtUtc.Format("**********"), remoteResp.TransactionId)
	rcCreateBody.UnlockAt = acAtUtc.Add(time.Second * 1)
	if _, err := s.repo.CreateRaceCondition(rcCreateBody); err != nil {
		return insertId, internalServerError(err)
	}

	// Get Posible Order
	var query model.MeepayOrderListRequest
	query.TransactionNo = remoteResp.TransactionId
	query.OrderNo = remoteResp.Reference
	// query.Amount = fmt.Sprintf("%f", remoteResp.Amount)
	query.Status = model.MEEPAY_ORDER_STATUS_WAIT_PAYMENT
	query.Limit = 1
	list, _, err := s.repo.GetDbMeepayOrderList(query)
	if err != nil {
		return nil, internalServerError(err)
	}

	// fmt.Println("MeepayDecryptRepayDespositPayload.list", helper.StructJson(list))

	// ** ไม่ได้เช็ค amount เพราะจะมีระบบที่ใช้ ทศนิยม และไม่เท่ากัน แต่ก็ควรเช็คแบบ +- 10%
	if len(list) > 0 {
		for _, item := range list {
			// Payment Success, Create Deposit if UserId is not null
			if item.UserId > 0 && item.OrderTypeId == model.MEEPAY_ORDER_TYPE_DEPOSIT {
				if successStatus == "SUCCESS" {
					// Update Order
					if err := s.repo.ApproveDbMeepayOrder(item.Id, successStatus); err != nil {
						return nil, internalServerError(err)
					}
					if _, err := CreateCustomerDepositFromMeepayOrder(s.repo, item.Id, 0); err != nil {
						// WebhookLog
						var createBody2 model.MeepayWebhookCreateBody
						createBody2.Name = "MEEPAY_DEPOSIT_ERROR"
						createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
							"item":   item,
							"method": "createCustomerDepositFromMeepay",
							"error":  err.Error(),
						})
						if _, err = s.repo.CreateMeepayWebhook(createBody2); err != nil {
							log.Println("Error CreateMeepayWebhook.createCustomerDepositFromMeepay", err)
						}
					}
				} else if successStatus == "EXPIRED" {
					// Update Order to EXPIRED
					if err := s.repo.UpdateDbMeepayOrderError(item.Id, "Expired Deposit from webhook"); err != nil {
						log.Println("UpdateDbMeepayOrderError", err)
						return nil, internalServerError(err)
					}
				}
			} else if item.UserId > 0 && item.OrderTypeId == model.MEEPAY_ORDER_TYPE_WITHDRAW {
				if successStatus == "SUCCESS" {
					// Update Order
					if err := s.repo.ApproveDbMeepayOrder(item.Id, successStatus); err != nil {
						return nil, internalServerError(err)
					}
					if _, err := approveCustomerWithdrawFromMeepay(s.repo, *item.RefId); err != nil {
						// WebhookLog
						var createBody2 model.MeepayWebhookCreateBody
						createBody2.Name = "MEEPAY_WITHDRAW_ERROR"
						createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
							"item":   item,
							"method": "approveCustomerWithdrawFromMeepay",
							"error":  err.Error(),
						})
						if _, err = s.repo.CreateMeepayWebhook(createBody2); err != nil {
							log.Println("Error approveCustomerWithdrawFromMeepay.CreateMeepayWebhook", err)
						}
					}
				} else if successStatus == "FAILED" || successStatus == "REJECTED" || successStatus == "EXPIRED" {
					// Update Order
					if err := s.repo.ApproveDbMeepayOrder(item.Id, successStatus); err != nil {
						return nil, internalServerError(err)
					}
					if err := s.cancelWithdrawFromMeepayWebhookError(item); err != nil {
						log.Println("Error UpdateDbMeepayOrderError", err)
					}
				}
			} else {
				log.Println("ApproveDbMeepayOrder.WithoutItemUpdated", helper.StructJson(item))
			}
		}
	}
	return insertId, nil
}

func (s paygateMeepayService) CreateMeepayWithdrawWebhook(req model.MeepayWebhookRequest) (*int64, error) {

	var createBody model.MeepayWebhookCreateBody
	createBody.Name = "MEEPAY_WITHDRAW_CALLBACK"
	createBody.JsonPayload = req.JsonPayload
	insertId, err := s.repo.CreateMeepayWebhook(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// WITHDRAW
	var remoteResp model.MeepayWithdrawWebhookResponse
	errJson := json.Unmarshal([]byte(req.JsonPayload), &remoteResp)
	if errJson != nil {
		log.Println("Error unmarshal json", errJson)
		return nil, internalServerError(errJson)
	}

	// Check Response Status. [succeeded]
	successStatus := strings.ToUpper(strings.TrimSpace(remoteResp.Status))
	if successStatus == "SUCCEEDED" || successStatus == "SUCCESS" {
		successStatus = model.MEEPAY_ORDER_STATUS_SUCCESS // Always use SUCCESS
	} else {
		log.Println("CreateMeepayWithdrawWebhook.remoteResp.desc.ELSE", successStatus, remoteResp.Status)
		// เก็บตามจริง successStatus = "ERROR"
	}

	// Service Race Condition by Ref1(MchOrderNo)
	acAtUtc := time.Now().UTC()
	var rcCreateBody model.RaceActionCreateBody
	rcCreateBody.Name = "CreateMeepayWithdrawWebhook"
	rcCreateBody.JsonRequest = helper.StructJson(req)
	rcCreateBody.Status = "PENDING"
	// ActionKey = Ref2-MINUTE
	rcCreateBody.ActionKey = fmt.Sprintf("PG16W%s-%s", acAtUtc.Format("**********"), remoteResp.TransactionId)
	rcCreateBody.UnlockAt = acAtUtc.Add(time.Second * 1)
	if _, err := s.repo.CreateRaceCondition(rcCreateBody); err != nil {
		return insertId, internalServerError(err)
	}

	// Get Posible Order
	var query model.MeepayOrderListRequest
	query.TransactionNo = remoteResp.TransactionId
	query.OrderNo = remoteResp.Reference
	// query.Amount = fmt.Sprintf("%f", remoteResp.Amount)
	query.Status = model.MEEPAY_ORDER_STATUS_WAIT_PAYMENT
	query.Limit = 1
	list, _, err := s.repo.GetDbMeepayOrderList(query)
	if err != nil {
		return nil, internalServerError(err)
	}

	// ** ไม่ได้เช็ค amount เพราะจะมีระบบที่ใช้ ทศนิยม และไม่เท่ากัน แต่ก็ควรเช็คแบบ +- 10%

	if len(list) > 0 {
		for _, item := range list {

			// Payment Success, Create Deposit if UserId is not null
			if item.UserId > 0 && item.OrderTypeId == model.MEEPAY_ORDER_TYPE_DEPOSIT {
				if successStatus == "SUCCESS" {
					// Update Order
					if err := s.repo.ApproveDbMeepayOrder(item.Id, successStatus); err != nil {
						return nil, internalServerError(err)
					}
					if _, err := CreateCustomerDepositFromMeepayOrder(s.repo, item.Id, 0); err != nil {
						// WebhookLog
						var createBody2 model.MeepayWebhookCreateBody
						createBody2.Name = "MEEPAY_DEPOSIT_ERROR"
						createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
							"item":   item,
							"method": "createCustomerDepositFromMeepay",
							"error":  err.Error(),
						})
						if _, err = s.repo.CreateMeepayWebhook(createBody2); err != nil {
							log.Println("Error CreateMeepayWebhook.createCustomerDepositFromMeepay", err)
						}
					}
				}
			} else if item.UserId > 0 && item.OrderTypeId == model.MEEPAY_ORDER_TYPE_WITHDRAW {
				if successStatus == "SUCCESS" {
					// Update Order
					if err := s.repo.ApproveDbMeepayOrder(item.Id, successStatus); err != nil {
						return nil, internalServerError(err)
					}
					if _, err := approveCustomerWithdrawFromMeepay(s.repo, *item.RefId); err != nil {
						// WebhookLog
						var createBody2 model.MeepayWebhookCreateBody
						createBody2.Name = "MEEPAY_WITHDRAW_ERROR"
						createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
							"item":   item,
							"method": "approveCustomerWithdrawFromMeepay",
							"error":  err.Error(),
						})
						if _, err = s.repo.CreateMeepayWebhook(createBody2); err != nil {
							log.Println("Error approveCustomerWithdrawFromMeepay.CreateMeepayWebhook", err)
						}
					}
				} else if successStatus == "FAILED" || successStatus == "REJECTED" || successStatus == "EXPIRED" {
					// Update Order
					if err := s.repo.ApproveDbMeepayOrder(item.Id, successStatus); err != nil {
						return nil, internalServerError(err)
					}
					if err := s.cancelWithdrawFromMeepayWebhookError(item); err != nil {
						log.Println("Error UpdateDbMeepayOrderError", err)
					}
				}
			} else {
				log.Println("ApproveDbMeepayOrder.WithoutItemUpdated", helper.StructJson(item))
			}
		}
	}
	return insertId, nil
}

func (s paygateMeepayService) cancelWithdrawFromMeepayWebhookError(pgOrder model.MeepayOrderResponse) error {

	adminId := int64(1)

	withdrawTrans, err := s.repo.GetBankTransactionById(*pgOrder.RefId)
	if err != nil {
		log.Println("rollbackWithdrawFromMeepay.GetBankTransactionById", err)
		return internalServerError(err)
	}
	if withdrawTrans.TransactionStatusId != model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		return internalServerError(errors.New("ไม่อยู๋ในสถานะที่สามารถยกเลิกได้"))
	}

	user, err := s.repo.GetUserBankDetailById(withdrawTrans.UserId)
	if err != nil {
		return internalServerError(err)
	}

	paygateOrder, err := s.repo.GetDbMeepayWithdrawOrderByRefId(withdrawTrans.Id)
	if err != nil {
		log.Println("CancelWithdrawFromMeepay.GetDbMeepayWithdrawOrderByRefId", err)
		return internalServerError(err)
	}
	waitStatus := "ERROR"
	if paygateOrder.TransactionStatus != waitStatus {
		return internalServerError(errors.New("ไม่อยู๋ในสถานะที่สามารถยกเลิกได้"))
	}

	returnAmount := withdrawTrans.CreditAmount

	// race condition deposit
	actionAt := time.Now().UTC()
	var createBody model.RaceActionCreateBody
	createBody.Name = "ADMIN_CANCEL_WITHDRAW"
	createBody.JsonRequest = helper.StructJson(map[string]interface{}{
		"transId":      withdrawTrans.Id,
		"returnAmount": returnAmount,
	})
	createBody.Status = "PENDING"
	createBody.ActionKey = fmt.Sprintf("WITHDRAW_CANCEL_T%sU%dID%d", actionAt.Format("**********"), user.Id, withdrawTrans.Id)
	createBody.UnlockAt = actionAt.Add(time.Second * 1)
	if _, err := s.repo.GetRaceActionIdByActionKey(createBody.ActionKey); err != nil {
		log.Println("CancelWithdrawFromMeepay.GetRaceActionIdByActionKey", err)
		if err != gorm.ErrRecordNotFound {
			return internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
		}
	} else {
		return internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
	}

	// create race condition
	actionId, err := s.repo.CreateRaceCondition(createBody)
	if err != nil {
		log.Println("CancelWithdrawFromMeepay.ERROR.CreateRaceCondition", err)
		return internalServerError(errors.New("CANNOT_CREATE_ACTION_LUCKY_WHEEL_ROUND"))
	}
	if actionId == 0 {
		return internalServerError(errors.New("CANNOT_CREATE_ACTION_LUCKY_WHEEL_ROUND"))
	}

	// SET ORDER AS ERROR
	remark := "Payment Cancel Withdraw"
	if err := s.repo.UpdateDbMeepayOrderError(paygateOrder.Id, remark); err != nil {
		log.Println("CreateMeepayWithdraw.UpdateDbMeepayOrderError", err)
	}

	if withdrawTrans.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		// [********] check user credit transaction ว่าตรงไหมกับเงื่อนไข (isShow = false)
		userTransaction, err := s.repo.GetUserWithdrawCreditTransactionByRefId(withdrawTrans.Id, returnAmount)
		if err != nil {
			if err.Error() == recordNotFound {
				log.Println("CancelWithdrawCredit.GetUserCreditTransactionById", err)
				return badRequest("ไม่ตรงกับเงือนไข")
			}
			return internalServerError(err)
		}
		if userTransaction != nil {
			var userCreditReq model.UserTransactionCreateRequest
			userCreditReq.UserId = user.Id
			userCreditReq.TransferAt = withdrawTrans.TransferAt
			userCreditReq.RefId = &withdrawTrans.Id
			userCreditReq.TypeId = model.CREDIT_TYPE_DEPOSIT
			userCreditReq.Amount = returnAmount
			IsShow := false
			userCreditReq.IsShow = &IsShow
			userCreditReq.ConfirmBy = &adminId
			if agentResp, err := s.repo.IncreaseUserCredit(userCreditReq); err != nil {
				log.Println("CancelWithdrawCredit.IncreaseUserCredit", err)
				return internalServerError(err)
			} else if !agentResp.AgentSuccess {
				// [********] error from agent will be in failed
				// if err := s.repoAccounting.UpdateDeporsitTransactionStatusFromAgent(*transId, *agentResp); err != nil {
				//     log.Println("CancelWithdrawCredit.UpdateDeporsitTransactionStatusFromAgent", err)
				// }
				// Set FastBank-READ ** ไม่มี เพราะเป็นการสร้างรายการฝากเอง
				agentFail := errors.New("ทำรายการเครดิตไม่สำเร็จ")
				log.Println("CancelWithdrawCredit.IncreaseUserCredit", agentFail)
				return internalServerError(agentFail)
			}
		}
	}

	// remark := "ADMIN_CANCEL_WITHDRAW"
	var updateApprovedBy model.UpdateUserTransactionStatusRequest
	updateApprovedBy.Id = withdrawTrans.Id
	updateApprovedBy.CanceledAt = actionAt
	updateApprovedBy.CanceledByAdminId = adminId
	updateApprovedBy.TransactionStatusId = model.TRANS_STATUS_WITHDRAW_CANCELED
	updateApprovedBy.CancelRemark = &remark
	err = s.repo.UpdateUserTransactionStatus(updateApprovedBy)
	if err != nil {
		return internalServerError(err)
	}

	var createConfirm model.CreateSuccessTransferWithdrawRequest
	createConfirm.TransactionId = withdrawTrans.Id
	createConfirm.ConfirmedAt = actionAt
	createConfirm.ConfirmedByAdminId = &adminId
	if _, err := CreateCanceledTransferWithdraw(repository.NewBankingRepository(s.sharedDb), createConfirm); err != nil {
		return err
	}

	return nil
}
