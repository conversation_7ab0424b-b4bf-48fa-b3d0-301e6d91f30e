package service

import (
	"log"
	"net/http"
)

type ErrorResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

func (e ErrorResponse) Error() string {
	return e.Message
}

func badRequest(msg string) error {
	return ErrorResponse{
		Code:    http.StatusBadRequest,
		Message: msg,
	}
}

func errorWithData(msg string, data interface{}) error {
	return ErrorResponse{
		Code:    http.StatusBadRequest,
		Message: msg,
		Data:    data,
	}
}

func notFound(msg string) error {
	return ErrorResponse{
		Code:    http.StatusNotFound,
		Message: msg,
	}
}

func unauthorized(msg string) error {
	return ErrorResponse{
		Code:    http.StatusUnauthorized,
		Message: msg,
	}
}

func internalServerError(msg error) error {

	log.Println(msg)

	// if os.Getenv("GIN_MODE") != "release" {
	return ErrorResponse{
		Code:    http.StatusInternalServerError,
		Message: msg.Error(),
	}
	// }

	// return ErrorResponse{
	// 	Code:    http.StatusInternalServerError,
	// 	Message: "เกิดข้อผิดพลาดบางอย่าง กรุณาลองใหม่อีกครั้ง",
	// }
}

// // MAP ERROR MESSAGE
// var errorMessages = map[string]string{
// 	"INTERNAL_SERVER_ERROR": "เกิดข้อผิดพลาดบางอย่าง กรุณาลองใหม่อีกครั้ง",
// 	"RECORD_NOT_FOUND":      "ไม่พบข้อมูล",
// 	"INCORRECT_IDENTITY":    "ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง",
// 	// Handler-Controller
// 	"CREATE_SUCCESS": "เพิ่มข้อมูลสำเร็จ",
// 	"UPDATE_SUCCESS": "อัพเดทข้อมูลสำเร็จ",
// 	"DELETE_SUCCESS": "ลบข้อมูลสำเร็จ",
// 	// PAYMENT_GATEWAY
// 	"INVALID_PAYGATE_USER": "โปรดตรวจสอบข้อมูลบัญชีธนาคารผู้ใช้",
// 	"PAYGATE_EMPTY_SETTING":        "การตั้งค่าระบบไม่ถูกต้อง",
// 	"INVALID_AMOUNT_RANGE": "จำนวนเงินไม่ถูกต้อง",
// }

// func TranslateMsg(msg string) string {
// 	// Translate error message
// 	if translatedMsg, ok := errorMessages[msg]; ok {
// 		msg = translatedMsg
// 	}
// 	return msg
// }
