package service

import (
	"bytes"
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/tealeg/xlsx"
)

type UserCreditService interface {
	GetuserTransferBankAccountList() ([]model.WebBankAccountResponse, error)
	GetTypeOptions() ([]model.SelectOptions, error)
	GetSortOptions() ([]model.SelectOptions, error)
	GetUserCreditTransactionSummary(query model.UserTransactionListRequest) (*model.UserTransactionSummaryResponse, error)
	GetUserTransactionList(req model.UserTransactionListRequest) (*model.SuccessWithPagination, error)
	RemoveUserTransaction(id int64, adminId int64) error
	GetUserTransactionRemovedList(req model.UserTransactionListRequest) (*model.SuccessWithPagination, error)

	ExportUserTransactionList(c *gin.Context, query model.UserTransactionForExcelListRequest) error
	// PlaylogReport
	GetReportPlayLogStatusResponse(req model.ReportPlayLogStatusRequest) (*model.SuccessWithPagination, error)
	GetReportPlayLogResponse(req model.ReportPlayLogResponseRequest) (*model.SuccessWithPagination, error)
	GetReportPlayLogSummary(req model.ReportPlayLogResponseRequest) (*model.ReportPlayLogSummaryResponse, error)
	RerunFailPlaylog(req model.ReportPlayLogRerunRequest, adminId int64) error
}

type userCreditService struct {
	repo repository.UserCreditRepository
}

func NewUserCreditService(
	repo repository.UserCreditRepository,
) UserCreditService {
	return &userCreditService{repo}
}

func (s userCreditService) GetReportPlayLogStatusResponse(req model.ReportPlayLogStatusRequest) (*model.SuccessWithPagination, error) {

	if err := helper.Pagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	list, total, err := s.repo.GetReportPlayLogStatusResponse(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	return &model.SuccessWithPagination{
		List:  list,
		Total: total,
	}, nil
}

func (s userCreditService) GetReportPlayLogResponse(req model.ReportPlayLogResponseRequest) (*model.SuccessWithPagination, error) {

	if err := helper.Pagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	list, total, err := s.repo.GetReportPlayLogResponse(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	return &model.SuccessWithPagination{
		List:  list,
		Total: total,
	}, nil
}

func (s userCreditService) GetReportPlayLogSummary(req model.ReportPlayLogResponseRequest) (*model.ReportPlayLogSummaryResponse, error) {

	list, err := s.repo.GetReportPlayLogSummary(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	return list, nil
}

func (s userCreditService) RerunFailPlaylog(req model.ReportPlayLogRerunRequest, adminId int64) error {

	// validate statementDate
	statementDate, err := time.Parse("2006-01-02", req.StatementDate)
	if err != nil {
		return badRequest("invalid statementDate")
	}

	return CronRerunFailWinLoseByDate(repository.NewAgentInfoRepository(s.repo.GetDb()), statementDate.Format("2006-01-02"), adminId)
}

func (s *userCreditService) GetuserTransferBankAccountList() ([]model.WebBankAccountResponse, error) {

	list, err := s.repo.GetuserTransferBankAccountList()
	if err != nil {
		return nil, internalServerError(err)
	}
	return list, nil
}

func (s *userCreditService) GetTypeOptions() ([]model.SelectOptions, error) {

	// หน้าบ้านเอาไปกรองหน้า ฝากถอนเสร็จสิ้น
	ids := []int64{
		model.CREDIT_TYPE_DEPOSIT,
		model.CREDIT_TYPE_WITHDRAW,
		model.CREDIT_TYPE_BONUS,
		model.CREDIT_TYPE_PROMOTION_RETURN_LOSS,
		model.CREDIT_TYPE_AFFILIATE_INCOME,
		// ไม่แสดง เพราะไม่มีอยู่แล้ว model.CREDIT_TYPE_ALLIANCE_INCOME,
		model.CREDIT_TYPE_TAKE_CREDIT_BACK,
		model.CREDIT_TYPE_DAILY_ACTIVITY_BONUS,
		model.CREDIT_TPYE_LUCKY_WHEEL,
		model.CREDIT_TYPE_CANCEL_CREDIT,
	}

	list, err := s.repo.GetTypeOptions(ids)
	if err != nil {
		return nil, internalServerError(err)
	}
	return list, nil
}

func (s *userCreditService) GetSortOptions() ([]model.SelectOptions, error) {

	list, err := s.repo.GetSortOptions()
	if err != nil {
		return nil, internalServerError(err)
	}
	return list, nil
}

func (s *userCreditService) GetUserCreditTransactionSummary(query model.UserTransactionListRequest) (*model.UserTransactionSummaryResponse, error) {

	summary, err := s.repo.GetUserCreditTransactionSummary(query)
	if err != nil {
		return nil, err
	}

	// [20250422] ยกเลิกเติมเครดิต = หักลบออกจากยอดฝาก
	if summary != nil {
		summary.TotalDepositAmount = summary.TotalDepositAmount - summary.TotalCancelCreditBack
	}

	return summary, nil
}

func (s *userCreditService) GetUserTransactionList(req model.UserTransactionListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	list, total, err := s.repo.GetCreditTransactionList(req)
	if err != nil {
		return nil, err
	}

	return &model.SuccessWithPagination{
		Total: total,
		List:  list,
	}, nil
}

func (s *userCreditService) ExportUserTransactionList(c *gin.Context, query model.UserTransactionForExcelListRequest) error {

	list, _, err := s.repo.GetCreditTransactionForExcelList(query)
	if err != nil {
		return internalServerError(err)
	}

	file := xlsx.NewFile()
	sheet, err := file.AddSheet("sheet")
	if err != nil {
		return err
	}

	// ORDER BY Field name
	row1 := sheet.AddRow()
	row1.AddCell().Value = "Id"
	row1.AddCell().Value = "รหัสสมาชิก"
	row1.AddCell().Value = "ประเภท"
	row1.AddCell().Value = "รายละเอียด"
	row1.AddCell().Value = "วัน-เวลาบันทึก"
	row1.AddCell().Value = "วัน-เวลาโอน"
	row1.AddCell().Value = "เครดิตดึงกลับ"
	row1.AddCell().Value = "เครดิต"
	row1.AddCell().Value = "โบนัส"
	row1.AddCell().Value = "เครดิตพนัน คงเหลือ"
	row1.AddCell().Value = "ผู้บันทึก/ผู้อนุมัติ"
	row1.AddCell().Value = "-"
	row1.AddCell().Value = "ระยะเวลาทำงาน"

	for _, v := range list {

		var isAdjustAutoName string
		if v.IsAdjustAuto {
			isAdjustAutoName = "ปรับมือ ออโต้"
		} else {
			isAdjustAutoName = "ปรับมือ"
		}

		var directionNameTH string
		if v.DirectionId == model.CREDIT_DIRECTION_DEPOSIT {
			directionNameTH = "ฝาก"
		} else if v.DirectionId == model.CREDIT_DIRECTION_WITHDRAW {
			directionNameTH = "ถอน"
		}

		var adminUsername string
		if v.CreateAdminUsername == "" {
			adminUsername = "bot-auto/bot-auto"
		} else {
			adminUsername = fmt.Sprintf("%s/%s", v.CreateAdminUsername, v.ConfirmAdminUsername)
		}

		row2 := sheet.AddRow()
		row2.AddCell().Value = strconv.FormatInt(v.Id, 10)
		row2.AddCell().Value = v.UserMemberCode
		row2.AddCell().Value = directionNameTH
		row2.AddCell().Value = v.TypeName
		row2.AddCell().Value = v.TransferAt.Format("2006-01-02 15:04:05")
		row2.AddCell().Value = v.CreatedAt.Format("2006-01-02 15:04:05")
		row2.AddCell().Value = strconv.FormatFloat(v.CreditBack, 'f', 2, 64)
		row2.AddCell().Value = strconv.FormatFloat(v.CreditAmount, 'f', 2, 64)
		row2.AddCell().Value = strconv.FormatFloat(v.BonusAmount, 'f', 2, 64)
		row2.AddCell().Value = strconv.FormatFloat(v.CreditAfter, 'f', 2, 64)
		row2.AddCell().Value = adminUsername
		row2.AddCell().Value = isAdjustAutoName
		row2.AddCell().Value = strconv.Itoa(v.WorkSeconds)
	}

	var b bytes.Buffer
	if err := file.Write(&b); err != nil {
		return err
	}

	var downloadName string
	if query.DateType == "today" {
		downloadName = ("UserTransactionListToday.xlsx")
	} else if query.DateType == "yesterday" {
		downloadName = ("UserTransactionListYesterday.xlsx")
	} else if query.DateType == "this_month" {
		downloadName = ("UserTransactionListThisMonth.xlsx")
	} else {
		downloadName = ("UserTransactionList.xlsx")
	}
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Disposition", "attachment; filename="+downloadName)
	c.Data(http.StatusOK, "application/octet-stream", b.Bytes())

	return nil
}

func (s *userCreditService) RemoveUserTransaction(id int64, adminId int64) error {

	_, err := s.repo.GetCreditTransactionById(id)
	if err != nil {
		return internalServerError(err)
	}

	if err := s.repo.RemoveCreditTransaction(id, adminId); err != nil {
		return internalServerError(err)
	}
	return nil
}

func (s *userCreditService) GetUserTransactionRemovedList(req model.UserTransactionListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	list, total, err := s.repo.GetCreditTransactionRemovedList(req)
	if err != nil {
		return nil, err
	}

	return &model.SuccessWithPagination{
		Total: total,
		List:  list,
	}, nil
}
