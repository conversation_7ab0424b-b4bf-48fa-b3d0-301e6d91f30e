package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"fmt"
	"log"
)

type EditSectionService interface {
	UpdateEditSection(body model.UpdateEditSectionBody) error
	GetEditSection() (*model.GetEditSectionResponse, error)
	WebGetEditSection() (*model.GetEditSectionResponse, error)
}

type editSectionService struct {
	repo repository.EditSectionRepository
}

func NewEditSectionService(
	repo repository.EditSectionRepository,
) EditSectionService {
	return &editSectionService{repo}
}

func (s editSectionService) UpdateEditSection(body model.UpdateEditSectionBody) error {

	data, err := s.GetEditSection()
	if err != nil {
		log.Println(err)
	}

	if err := s.repo.UpdateEditSection(body); err != nil {
		return err
	}

	// [ADMIN_ACTION] SUCCESS
	var adminActionCreateBody model.AdminActionCreateBody
	adminActionCreateBody.AdminId = body.UpdatedById
	adminActionCreateBody.TypeId = model.ADMIN_ACTION_EDIT_SECTION
	adminActionCreateBody.Detail = fmt.Sprintf("แก้ไขข้อมูล Section ฝากเงิน และ ถอนเงิน")
	adminActionCreateBody.IsSuccess = true
	adminActionCreateBody.IsShow = true
	adminActionCreateBody.JsonInput = helper.StructJson(data)
	adminActionCreateBody.JsonOutput = helper.StructJson(body)
	if _, err := s.repo.CreateAdminAction(adminActionCreateBody); err != nil {
		return err
	}

	return nil
}

func (s editSectionService) GetEditSection() (*model.GetEditSectionResponse, error) {

	record, err := s.repo.GetEditSection()
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(recordNotFound)
		}
		return nil, internalServerError(err)
	}
	return record, nil
}

func (s editSectionService) WebGetEditSection() (*model.GetEditSectionResponse, error) {

	record, err := s.GetEditSection()
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(recordNotFound)
		}
		return nil, internalServerError(err)
	}

	return record, nil
}
