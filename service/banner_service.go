package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"fmt"
	"log"
	"os"
	"strings"
)

type BannerService interface {
	BannerGetIsActive(query model.BannerWebQuery) (*model.BannerListResponse, error)
	BannerGetList(query model.BannerQuery) (*model.BannerListResponse, error)
	BannerGetShopList() (*model.BannerShopListResponse, error)
	BannerGetTicketList() (*model.BannerListResponse, error)
	BannerGetTypeList() ([]model.BannerTypeListResponse, error)
	BannerDetail(bannerId int64) (*model.BannerDetail, error)
	BannerCreate(banner model.BannerCreateBody) error
	BannerUpdate(body model.BannerUpdateBody) error
	BannerUpdateSortOrder(list model.BannerSortBody) error
	BannerUpdateShop(bannerId string) error
	BannerUpdateTicket(bannerId string) error
	BannerDelete(bannerId int64) error
}

type BannerServiceRepos struct {
	repo      repository.BannerRepository
	FileRepo  repository.FileRepository
	CacheRepo repository.CacheRepository
}

func NewBannerService(
	repo repository.BannerRepository,
	FileRepo repository.FileRepository,
	CacheRepo repository.CacheRepository,
) BannerService {
	return &BannerServiceRepos{repo, FileRepo, CacheRepo}
}

func (s BannerServiceRepos) BannerGetIsActive(query model.BannerWebQuery) (*model.BannerListResponse, error) {

	response := &model.BannerListResponse{}

	// if s.CacheRepo.Get("banner_active_list", &response) {
	// 	return response, nil
	// }

	bannerList, err := s.repo.GetBannerIsActive(query)
	if err != nil {

		if err.Error() == recordNotFound {
			return nil, notFound(bannerNotFound)
		}

		return nil, internalServerError(err)
	}

	response.List = bannerList

	// s.CacheRepo.Set("banner_active_list", response, 0)

	return response, nil
}

func (s BannerServiceRepos) BannerGetList(query model.BannerQuery) (*model.BannerListResponse, error) {

	bannerList, err := s.repo.GetBannerList(query)
	if err != nil {

		if err.Error() == recordNotFound {
			return nil, notFound(bannerNotFound)
		}

		return nil, internalServerError(err)
	}

	result := &model.BannerListResponse{
		List: bannerList,
	}

	return result, nil
}

func (s BannerServiceRepos) BannerGetShopList() (*model.BannerShopListResponse, error) {

	shopTop, shopBottom, err := s.repo.GetBannerShopList()
	if err != nil {

		if err.Error() == recordNotFound {
			return nil, notFound(bannerNotFound)
		}

		return nil, internalServerError(err)
	}

	result := &model.BannerShopListResponse{
		TopList:    shopTop,
		BottomList: shopBottom,
	}

	return result, nil
}

func (s BannerServiceRepos) BannerGetTicketList() (*model.BannerListResponse, error) {

	ticketList, err := s.repo.GetBannerTicketList()
	if err != nil {

		if err.Error() == recordNotFound {
			return nil, notFound(bannerNotFound)
		}

		return nil, internalServerError(err)
	}

	result := &model.BannerListResponse{
		List: ticketList,
	}

	return result, nil
}

func (s BannerServiceRepos) BannerGetTypeList() ([]model.BannerTypeListResponse, error) {

	bannerList, err := s.repo.GetBannerTypeList()
	if err != nil {
		return nil, internalServerError(err)
	}

	return bannerList, nil
}

func (s BannerServiceRepos) BannerDetail(bannerId int64) (*model.BannerDetail, error) {

	banner, err := s.repo.GetBannerById(bannerId)
	if err != nil {

		if err.Error() == recordNotFound {
			return nil, notFound(bannerNotFound)
		}

		return nil, internalServerError(err)
	}

	return banner, nil
}

func (s BannerServiceRepos) BannerCreate(body model.BannerCreateBody) error {

	count, err := s.repo.CountBannerByType(body.Type)
	if err != nil {
		return internalServerError(err)
	}

	body.SortOrder = int(count + 1)

	if err := s.repo.CreateBanner(body); err != nil {
		return internalServerError(err)
	}

	s.CacheRepo.Delete("banner_active_list")

	return nil
}

func (s BannerServiceRepos) BannerUpdate(body model.BannerUpdateBody) error {

	isActiveTotal := 4

	banner, err := s.repo.GetBannerById(body.Id)
	if err != nil {

		if err.Error() == recordNotFound {
			return notFound(bannerNotFound)
		}

		return internalServerError(err)
	}

	count, err := s.repo.CountBannerIsActiveByType(banner.Type)
	if err != nil {
		return internalServerError(err)
	}

	if body.IsActive && count == int64(isActiveTotal) {
		return badRequest(bannerIsActivedNotOverFour)
	}

	if err := s.repo.UpdateBanner(body); err != nil {
		return internalServerError(err)
	}

	s.CacheRepo.Delete("banner_active_list")

	return nil
}

func (s BannerServiceRepos) BannerDelete(bannerId int64) error {

	getCoverUrl, err := s.repo.GetCoverUrlByBannerId(bannerId)
	if err != nil {
		return err
	}

	s.deleteCoverUrl(getCoverUrl, bannerId)

	if err := s.repo.DeleteBanner(bannerId); err != nil {
		return internalServerError(err)
	}

	s.CacheRepo.Delete("banner_active_list")

	return nil
}

func (s BannerServiceRepos) BannerUpdateSortOrder(list model.BannerSortBody) error {

	if len(list.List) == 0 {
		return badRequest(bannerListIsRequire)
	}

	for _, item := range list.List {
		if item.SortOrder == 0 {
			return badRequest(bannerSortOrderNotZero)
		}
	}

	if err := s.repo.UpdateBannerSortOrder(list); err != nil {
		return internalServerError(err)
	}

	s.CacheRepo.Delete("banner_active_list")

	return nil
}

func (s BannerServiceRepos) BannerUpdateShop(bannerId string) error {

	if err := s.repo.UpdateBannerShop(bannerId); err != nil {
		return internalServerError(err)
	}

	s.CacheRepo.Delete("banner_active_list")

	return nil
}

func (s BannerServiceRepos) BannerUpdateTicket(bannerId string) error {

	if err := s.repo.UpdateBannerTicket(bannerId); err != nil {
		return internalServerError(err)
	}

	s.CacheRepo.Delete("banner_active_list")

	return nil
}

func (s BannerServiceRepos) deleteCoverUrl(oldCoverUrl string, bannerId int64) {

	bucketName := os.Getenv("BUCKET_NAME")

	if oldCoverUrl != "" && helper.FindString("https://storage.googleapis.com", oldCoverUrl) {

		url := fmt.Sprintf("https://storage.googleapis.com/%s/", bucketName)

		slicePath := strings.Split(oldCoverUrl, url)
		slicePath[1] = strings.Replace(slicePath[1], "%2F", "/", -1)
		slicePath[1] = strings.Replace(slicePath[1], "%20", " ", -1)
		if err := s.FileRepo.DeleteFile(slicePath[1]); err != nil {
			log.Println("delete file error : ", oldCoverUrl)
			return
		}
	}
}
