package service

import (
	"bytes"
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"image/png"
	"log"
	"strconv"
	"strings"
	"time"

	"github.com/skip2/go-qrcode"
	"gorm.io/gorm"
)

type PaymentcoService interface {
	// Paymentco
	CreatePaymentcoWebhook(req model.PaymentcoWebhookRequest) (*int64, error)
	GetPaymentcoWebDepositAccount() (*model.PaymentcoCustomerDepositInfo, error)
	CreatePaymentcoDeposit(req model.PaymentcoDepositCreateRequest) (*model.PaymentcoOrderWebResponse, error)
	CreatePaymentcoWithdraw(req model.PaymentcoWithdrawCreateRequest) (*int64, error)
	// PaymentcoCheckBalance() (*model.PaymentcoCheckBalanceRemoteResponse, error)
	CancelWithdrawFromPaymentco(transId int64, adminId int64) error
	// SysLog
	CreateSystemLog(name string, req interface{}) error
}

type paygatePaymentcoService struct {
	sharedDb                  *gorm.DB
	repo                      repository.PaymentcoRepository
	activityLuckyWheelService ActivityLuckyWheelService
	promotionWebService       PromotionWebService
	notiService               NotificationService
}

func NewPaymentcoService(
	sharedDb *gorm.DB,
	repo repository.PaymentcoRepository,
	activityLuckyWheelService ActivityLuckyWheelService,
	promotionWebService PromotionWebService,
	notiService NotificationService,
) PaymentcoService {
	return &paygatePaymentcoService{sharedDb, repo, activityLuckyWheelService, promotionWebService, notiService}
}

func (s paygatePaymentcoService) ConfirmDepositTransaction(id int64, req model.BankConfirmDepositRequest) error {

	record, err := s.repo.GetBankTransactionById(id)
	if err != nil {
		return internalServerError(err)
	}
	if record.TransactionStatusId != model.TRANS_STATUS_PENDING {
		return badRequest("Transaction is not pending")
	}
	if record.TransactionTypeId != model.TRANSACTION_TYPE_DEPOSIT && record.TransactionTypeId != model.TRANSACTION_TYPE_BONUS {
		return badRequest("Transaction is not deposit")
	}
	jsonBefore, _ := json.Marshal(record)

	var updateData model.BankDepositTransactionConfirmBody
	updateData.TransactionStatusId = model.TRANS_STATUS_DEPOSIT_PENDING_CREDIT
	updateData.ConfirmedAt = req.ConfirmedAt
	updateData.ConfirmedByAdminId = req.ConfirmedByAdminId

	// if record.IsAutoCredit && record.TransferAt != nil {
	// 	seconds := time.Now().UTC().Sub(record.TransferAt.UTC()).Seconds()
	// 	autoProcessTimer := fmt.Sprintf("%.2f", seconds)
	// 	updateData.AutoProcessTimer = &autoProcessTimer
	// }

	var createBody model.CreateBankTransactionActionBody
	createBody.ActionKey = fmt.Sprintf("DCF_STATE#%d", record.Id)
	createBody.TransactionId = record.Id
	createBody.UserId = record.UserId
	createBody.TransactionTypeId = record.TransactionTypeId
	createBody.FromAccountId = record.FromAccountId
	createBody.ToAccountId = record.ToAccountId
	createBody.JsonBefore = string(jsonBefore)
	if req.TransferAt == nil {
		createBody.TransferAt = record.TransferAt
	} else {
		TransferAt := req.TransferAt
		createBody.TransferAt = TransferAt
		updateData.TransferAt = *TransferAt
	}
	if req.SlipUrl != nil {
		createBody.SlipUrl = *req.SlipUrl
	}
	createBody.CreditAmount = record.CreditAmount
	if req.BonusAmount != nil {
		createBody.BonusAmount = *req.BonusAmount
	}
	createBody.ConfirmedAt = req.ConfirmedAt
	createBody.ConfirmedByAdminId = req.ConfirmedByAdminId
	if actionId, err := s.repo.CreateTransactionAction(createBody); err == nil {
		// do nothing ?
		if err := s.repo.ConfirmPendingDepositTransaction(id, updateData); err != nil {
			if err := s.repo.RollbackTransactionAction(*actionId); err == nil {
				return internalServerError(err)
			}
			return internalServerError(err)
		}
	} else {
		return internalServerError(err)
	}
	return nil
}

func (s paygatePaymentcoService) CreatePaymentcoWebhook(req model.PaymentcoWebhookRequest) (*int64, error) {

	var createBody model.PaymentcoWebhookCreateBody
	createBody.Name = "PAYMENTCO_CALLBACK"
	createBody.JsonPayload = req.JsonPayload
	insertId, err := s.repo.CreatePaymentcoWebhook(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// DEPOSIT todo
	// amount=54.00&appId=River&orderId=DPM241039&pay=true&version=1.0&sign=91CB6A4CDEC321086CF98BF474C36520
	// amount=54.00&appId=River&orderId=DPM241039&pay=true&version=1.0&sign=91CB6A4CDEC321086CF98BF474C36520&transferDateTime=2020-01-02 03:04:05
	if strings.Contains(req.JsonPayload, "amount=") {
		// Decode urlstring into map
		// decode in to struct
		var remoteResp model.PaymentcoDepositWebhookResponse
		remoteResp.TransferDateTime = time.Now().Format("2006-01-02 15:04:05")
		params := strings.Split(req.JsonPayload, "&")
		for _, param := range params {
			pair := strings.Split(param, "=")
			if len(pair) == 2 {
				switch pair[0] {
				case "amount":
					remoteResp.Amount = pair[1]
				case "appId":
					remoteResp.AppId = pair[1]
				case "orderId":
					remoteResp.OrderId = pair[1]
				case "pay":
					remoteResp.Pay = pair[1]
				case "version":
					remoteResp.Version = pair[1]
				case "sign":
					remoteResp.Sign = pair[1]
				case "transferDateTime":
					remoteResp.TransferDateTime = pair[1]
				}
			}
		}
		// fmt.Println("HasPrefix.remoteResp", helper.StructJson(remoteResp))
		if err := s.createPaymentcoDepositFromWebhook(remoteResp); err != nil {
			log.Println("createPaymentcoDepositFromWebhook", err)
		}
		return insertId, nil
	}

	// WITHDRAW todo ระบบ payment แจ้งว่ายังไม่รองรับ
	// {
	// }
	// var remoteResp model.PaymentcoWithDrawWebhookResponse
	// errJson := json.Unmarshal([]byte(req.JsonPayload), &remoteResp)
	// if errJson != nil {
	// 	log.Println("Error unmarshal json", errJson)
	// 	return nil, internalServerError(errJson)
	// }
	// if remoteResp.ReferenceId != "" {
	// 	if err := s.createPaymentcoWithdrawFromWebhook(remoteResp); err != nil {
	// 		log.Println("createPaymentcoDepositFromWebhook", err)
	// 	}
	// }

	return insertId, nil
}

func (s paygatePaymentcoService) createPaymentcoDepositFromWebhook(remoteResp model.PaymentcoDepositWebhookResponse) error {

	// Service Race Condition by Ref1(MchOrderNo)
	var rcCreateBody model.RaceActionCreateBody
	rcCreateBody.Name = "CreatePaymentcoWebhook"
	rcCreateBody.JsonRequest = helper.StructJson(remoteResp)
	rcCreateBody.Status = "PENDING"
	// ActionKey = Ref2-MINUTE
	rcCreateBody.ActionKey = fmt.Sprintf("%s-%s", remoteResp.OrderId, time.Now().Format("0601021504"))
	rcCreateBody.UnlockAt = time.Now().UTC().Add(time.Second * 1)
	if _, err := s.repo.CreateRaceCondition(rcCreateBody); err != nil {
		return internalServerError(err)
	}

	// Get Posible Order
	var query model.PaymentcoOrderListRequest
	query.OrderNo = remoteResp.OrderId
	query.TransactionNo = remoteResp.OrderId
	query.Status = "WAIT_PAYMENT"
	query.Limit = 1
	list, _, err := s.repo.GetDbPaymentcoOrderList(query)
	if err != nil {
		return internalServerError(err)
	}

	// fmt.Println("PaymentcoDecryptRepayDespositPayload.list", helper.StructJson(list))

	if len(list) > 0 {
		for _, item := range list {
			// Update Order
			successStatus := strings.ToUpper(remoteResp.Pay)
			if successStatus == "TRUE" {
				successStatus = "SUCCESS"
			}
			if err := s.repo.ApproveDbPaymentcoOrder(item.Id, successStatus); err != nil {
				return internalServerError(err)
			}
			// Payment Success, Create Deposit if UserId is not null
			if item.UserId > 0 && item.OrderTypeId == model.PAYMENTCO_ORDER_TYPE_DEPOSIT {
				if successStatus == "SUCCESS" {
					if _, err := CreateCustomerDepositFromPaymentcoOrder(s.repo, item.Id, 0); err != nil {
						// WebhookLog
						var createBody2 model.PaymentcoWebhookCreateBody
						createBody2.Name = "PAYMENTCO_DEPOSIT_ERROR"
						createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
							"item":   item,
							"method": "createPaymentcoDepositFromWebhook",
							"error":  err.Error(),
						})
						if _, err = s.repo.CreatePaymentcoWebhook(createBody2); err != nil {
							log.Println("Error CreatePaymentcoWebhook.createPaymentcoDepositFromWebhook", err)
						}
					}
				}
			} else if item.UserId > 0 && item.OrderTypeId == model.PAYMENTCO_ORDER_TYPE_WITHDRAW {
				if successStatus == "SUCCESS" {
					if _, err := ApproveCustomerWithdrawFromPaymentco(s.repo, *item.RefId); err != nil {
						// WebhookLog
						var createBody2 model.PaymentcoWebhookCreateBody
						createBody2.Name = "PAYMENTCO_WITHDRAW_ERROR"
						createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
							"item":   item,
							"method": "approveCustomerWithdrawFromPaymentco",
							"error":  err.Error(),
						})
						if _, err = s.repo.CreatePaymentcoWebhook(createBody2); err != nil {
							log.Println("Error approveCustomerWithdrawFromPaymentco.CreatePaymentcoWebhook", err)
						}
					}
				} else if successStatus == "ERROR" || successStatus == "REJECTED" {
					if err := s.CancelWithdrawFromPaymentcoWebhookError(item); err != nil {
						log.Println("Error UpdateDbPaymentcoOrderError", err)
					}
				}
			} else {
				log.Println("ApproveDbPaymentcoOrder.WithoutItemUpdated", helper.StructJson(item))
			}
		}
	}
	return nil
}

func (s paygatePaymentcoService) CreatePaymentcoWithdrawFromWebhook(remoteResp model.PaymentcoWithDrawWebhookResponse) error {

	// Service Race Condition by Ref1(MchOrderNo)
	var rcCreateBody model.RaceActionCreateBody
	rcCreateBody.Name = "CreatePaymentcoWebhook"
	rcCreateBody.JsonRequest = helper.StructJson(remoteResp)
	rcCreateBody.Status = "PENDING"
	// ActionKey = Ref2-MINUTE
	rcCreateBody.ActionKey = fmt.Sprintf("%s-%s", remoteResp.TransactionId, time.Now().Format("0601021504"))
	rcCreateBody.UnlockAt = time.Now().UTC().Add(time.Second * 1)
	if _, err := s.repo.CreateRaceCondition(rcCreateBody); err != nil {
		return internalServerError(err)
	}

	// Get Posible Order
	var query model.PaymentcoOrderListRequest
	query.OrderNo = remoteResp.TransactionId
	query.TransactionNo = remoteResp.ReferenceId
	// query.Amount = fmt.Sprintf("%f", remoteResp.Amount)
	query.Status = "WAIT_PAYMENT"
	query.Limit = 1
	list, _, err := s.repo.GetDbPaymentcoOrderList(query)
	if err != nil {
		return internalServerError(err)
	}

	// fmt.Println("PaymentcoDecryptRepayDespositPayload.list", helper.StructJson(list))

	if len(list) > 0 {
		for _, item := range list {
			// Update Order
			successStatus := strings.ToUpper(remoteResp.Status)
			if successStatus == "SUCCESS" {
				successStatus = "SUCCESS"
			} else if successStatus == "REJECTED" {
				successStatus = "REJECTED"
			} else if successStatus == "ERROR" {
				successStatus = "ERROR"
			}
			if err := s.repo.ApproveDbPaymentcoOrder(item.Id, successStatus); err != nil {
				return internalServerError(err)
			}
			// Payment Success, Create Deposit if UserId is not null
			if item.UserId > 0 && item.OrderTypeId == model.PAYMENTCO_ORDER_TYPE_DEPOSIT {
				if successStatus == "SUCCESS" {
					if _, err := CreateCustomerDepositFromPaymentcoOrder(s.repo, item.Id, 0); err != nil {
						// WebhookLog
						var createBody2 model.PaymentcoWebhookCreateBody
						createBody2.Name = "PAYMENTCO_DEPOSIT_ERROR"
						createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
							"item":   item,
							"method": "createCustomerDepositFromPaymentco",
							"error":  err.Error(),
						})
						if _, err = s.repo.CreatePaymentcoWebhook(createBody2); err != nil {
							log.Println("Error CreatePaymentcoWebhook.createCustomerDepositFromPaymentco", err)
						}
					}
				}
			} else if item.UserId > 0 && item.OrderTypeId == model.PAYMENTCO_ORDER_TYPE_WITHDRAW {
				if successStatus == "SUCCESS" {
					if _, err := ApproveCustomerWithdrawFromPaymentco(s.repo, *item.RefId); err != nil {
						// WebhookLog
						var createBody2 model.PaymentcoWebhookCreateBody
						createBody2.Name = "PAYMENTCO_WITHDRAW_ERROR"
						createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
							"item":   item,
							"method": "approveCustomerWithdrawFromPaymentco",
							"error":  err.Error(),
						})
						if _, err = s.repo.CreatePaymentcoWebhook(createBody2); err != nil {
							log.Println("Error approveCustomerWithdrawFromPaymentco.CreatePaymentcoWebhook", err)
						}
					}
				} else if successStatus == "ERROR" || successStatus == "REJECTED" {
					if err := s.CancelWithdrawFromPaymentcoWebhookError(item); err != nil {
						log.Println("Error UpdateDbPaymentcoOrderError", err)
					}
				}
			} else {
				log.Println("ApproveDbPaymentcoOrder.WithoutItemUpdated", helper.StructJson(item))
			}
		}
	}
	return nil
}

func (s paygatePaymentcoService) GetPaymentcoWebDepositAccount() (*model.PaymentcoCustomerDepositInfo, error) {

	var result model.PaymentcoCustomerDepositInfo

	pgAccount, err := s.GetPaymentcoAccount()
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}
	if !pgAccount.IsDepositEnabled {
		return nil, badRequest("PAYGATE_DEPOSIT_NOT_ENABLED")
	}

	result.Name = pgAccount.Name
	if pgAccount.DisplayName == "" {
		result.Name = "เติมเงินผ่าน QR Code"
	} else {
		result.DisplayName = pgAccount.DisplayName
	}
	result.ShopName = pgAccount.ShopName
	result.MinAmount = 100
	result.MaxAmount = 200000

	return &result, nil
}

func (s paygatePaymentcoService) GetPaymentcoAccount() (*model.PaygateAccountResponse, error) {

	return GetPaymentcoAccount(s.repo)
}

func GetPaymentcoAccount(repo repository.PaymentcoRepository) (*model.PaygateAccountResponse, error) {

	pgAccount, err := repo.GetPaygateAccountByProviderId(model.PAYGATE_MERCHANT_ID_PAYMENTCO)
	if err != nil {
		return nil, internalServerError(err)
	}
	return pgAccount, nil
}

func (s paygatePaymentcoService) CreatePaymentcoDeposit(req model.PaymentcoDepositCreateRequest) (*model.PaymentcoOrderWebResponse, error) {

	var result model.PaymentcoOrderWebResponse

	// Ruled by Provider
	// if req.Amount < 50 || req.Amount > 200000 {
	// 	log.Println("req.Amount", req.Amount)
	// 	return nil, errors.New("INVALID_AMOUNT_RANGE")
	// }

	pgAccount, err := s.GetPaymentcoAccount()
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}
	if !pgAccount.IsDepositEnabled {
		return nil, badRequest("PAYGATE_DEPOSIT_NOT_ENABLED")
	}
	if pgAccount.ApiEndPoint == "" || pgAccount.SecretKey == "" || pgAccount.MerchantId == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}

	// ผมอยากเก็บ default เอาไว้กัน Adminตั้งค่าต่ำกว่า payment
	// if req.Amount < 100 || req.Amount > 50000 {
	// 	return nil, errors.New("INVALID_AMOUNT_RANGE")
	// }
	// if req.Amount < pgAccount.PaymentDepositMinimum || req.Amount > pgAccount.PaymentDepositMaximum {
	// 	return nil, errors.New("INVALID_AMOUNT_RANGE")
	// }

	user, err := s.repo.GetUserBankDetailById(req.UserId)
	if err != nil {
		return nil, badRequest("INVALID_USER")
	}
	if user == nil || user.BankAccount == "" || user.BankCode == "" {
		return nil, badRequest("INVALID_USER_ACCOUNT")
	}

	accountingRepo := repository.NewAccountingRepository(s.repo.GetDb())
	configWeb, err := s.repo.GetWebConfiguration()
	if err != nil {
		log.Println("CreatePaymentcoDeposit.GetWebConfiguration", err)
		return nil, badRequest("WEB_CONFIG_NOT_FOUND")
	}
	if user.Id != 0 && configWeb != nil {
		// [********] ฝากครั้งแรกขั้นต่ำ ปรับ flow เช็ค user ให้เช็คจากรายการฝากครั้งแรกจริงๆ ไม่เช็คจากรหัสที่ได้รับ
		if err := CheckFirstMinimunDeposit(accountingRepo, user.Id, req.Amount, *configWeb); err != nil {
			return nil, badRequest(fmt.Sprintf("ฝากครั้งแรกขั้นต่ำ %d บาท", configWeb.MinFirstMemberDeposit))
		}
	} else {
		log.Println("CreatePaymentcoDeposit.CheckFirstMinimunDeposit", "CANT_CHECK_FIRST_MINIMUM_DEPOSIT_ON_EMPTY_CONFIG")
	}

	// ===========================================================================================
	var createBody model.PaymentcoOrderCreateBody
	createBody.UserId = req.UserId
	createBody.OrderTypeId = model.PAYMENTCO_ORDER_TYPE_DEPOSIT
	createBody.OrderNo = "" // Autogen
	createBody.Amount = req.Amount
	createBody.TransactionStatus = "PENDING"
	insertId, err := s.repo.CreateDbPaymentcoOrder(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	// reget Order
	pendingOrder, err := s.repo.GetDbPaymentcoOrderById(*insertId)
	if err != nil {
		// SET AS ERROR
		remark := "Error GetDbPaymentcoOrderById, " + err.Error()
		if err := s.repo.UpdateDbPaymentcoOrderError(*insertId, remark); err != nil {
			log.Println("CreatePaymentcoDeposit.UpdateDbPaymentcoOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// Create PAYMENTCO Order
	var remoteRequest model.PaymentcoDepositCreateRemoteRequest
	remoteRequest.OrderId = pendingOrder.OrderNo
	// Payout Amount notcontain any decimalpoints Example1000.00 will be100000
	remoteRequest.Amount = fmt.Sprintf("%.0f", req.Amount*100)
	// remoteRequest.CustName = user.Fullname
	// remoteRequest.CustSecondaryName = user.MemberCode
	// remoteRequest.CustMobile = user.Phone
	// remoteRequest.BankAcc = user.BankAccount
	// remoteRequest.CustBank = user.BankCode
	remoteResp, err := s.repo.PaymentcoDeposit(*pgAccount, remoteRequest)
	if err != nil {
		// SET AS ERROR
		remark := "Error PaymentcoDeposit, " + err.Error()
		if err := s.repo.UpdateDbPaymentcoOrderError(*insertId, remark); err != nil {
			log.Println("PaymentcoDeposit.UpdateDbPaymentcoOrderError", err)
		}
		// SysLog
		if _, err := s.repo.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "CreatePaymentcoDeposit.PaymentcoDeposit",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(remoteRequest),
			JsonResponse: helper.StructJson(map[string]interface{}{"error": err.Error()}),
		}); err != nil {
			log.Println("CreatePaymentcoDeposit.CreateSysLog", err)
		}
		return nil, internalServerError(err)
	}

	fmt.Println("PaymentcoDeposit.remoteResp", helper.StructJson(remoteResp))

	// Parse Float Amount
	transferAmount, err := strconv.ParseFloat(remoteResp.TransferAmount, 64)
	if err != nil {
		// SET AS ERROR
		remark := "Error ParseFloat Amount, " + err.Error()
		if err := s.repo.UpdateDbPaymentcoOrderError(*insertId, remark); err != nil {
			log.Println("CreatePaymentcoDeposit.UpdateDbPaymentcoOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// onCreate Success
	var updateBody model.PaymentcoOrderUpdateBody
	updateBody.TransactionNo = remoteResp.ReferenceId
	updateBody.TransactionDate = time.Now()
	updateBody.TransactionStatus = "WAIT_PAYMENT"
	updateBody.TransferAmount = transferAmount
	updateBody.QrPromptpay = remoteResp.Qrcodestring
	if err := s.repo.UpdateDbPaymentcoOrder(*insertId, updateBody); err != nil {
		// SET AS ERROR
		remark := "Error UpdateDbPaymentcoOrder, " + err.Error()
		if err := s.repo.UpdateDbPaymentcoOrderError(*insertId, remark); err != nil {
			log.Println("CreatePaymentcoDeposit.UpdateDbPaymentcoOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	waitPayOrder, err := s.repo.GetDbPaymentcoOrderById(*insertId)
	if err != nil {
		return nil, internalServerError(err)
	}

	result.UserId = waitPayOrder.UserId
	result.OrderNo = waitPayOrder.OrderNo
	result.Amount = waitPayOrder.Amount
	result.TransferAmount = transferAmount
	result.TransactionStatus = *waitPayOrder.TransactionStatus
	result.QrCode = waitPayOrder.QrPromptpay
	result.CreatedAt = waitPayOrder.CreatedAt

	imgData, err := qrcode.Encode(waitPayOrder.QrPromptpay, qrcode.Medium, 256)
	if err != nil {
		// return nil, fmt.Errorf("unable to encode png: %w", err)
		return &result, nil
	}
	// encode to base64
	img, err := png.Decode(bytes.NewReader(imgData))
	if err != nil {
		// return nil, fmt.Errorf("unable to decode jpeg: %w", err)
		return &result, nil
	}
	var buf bytes.Buffer
	if err := png.Encode(&buf, img); err != nil {
		// return nil, fmt.Errorf("unable to encode png: %w", err)
		return &result, nil
	}
	result.QrBase64 = base64.StdEncoding.EncodeToString(buf.Bytes())

	return &result, nil
}

func (s paygatePaymentcoService) CreatePaymentcoWithdraw(req model.PaymentcoWithdrawCreateRequest) (*int64, error) {

	pgAccount, err := s.GetPaymentcoAccount()
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}
	if !pgAccount.IsWithdrawEnabled {
		return nil, badRequest("PAYGATE_WITHDRAW_NOT_ENABLED")
	}
	if pgAccount.ApiEndPoint == "" || pgAccount.SecretKey == "" || pgAccount.MerchantId == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}
	if req.Amount < pgAccount.PaymentWithdrawMinimum {
		return nil, badRequest("WITHDRAW_AMOUNT_MINIMUM")
	}
	if req.Amount > pgAccount.PaymentWithdrawMaximum {
		return nil, badRequest("WITHDRAW_AMOUNT_MAXIMUM")
	}

	user, err := s.repo.GetUserBankDetailById(req.UserId)
	if err != nil {
		return nil, badRequest("INVALID_USER")
	}
	if user == nil || user.BankAccount == "" || user.BankCode == "" {
		return nil, badRequest("INVALID_USER_ACCOUNT")
	}

	// remoteRequest.UserAccountBank = user.BankCode // todo validate ?
	withdrawBankCode := user.BankCode
	// user mobile must start with prefix 66 or 856
	// withdrawPhone := user.Phone
	// if len(withdrawPhone) > 0 && strings.HasPrefix(withdrawPhone, "0") {
	// 	withdrawPhone = "66" + withdrawPhone[1:]
	// }

	// ===========================================================================================
	// CREATE Order
	var createBody model.PaymentcoOrderCreateBody
	createBody.RefId = &req.RefId
	createBody.UserId = req.UserId
	createBody.OrderTypeId = model.PAYMENTCO_ORDER_TYPE_WITHDRAW
	createBody.OrderNo = "" // Autogen
	createBody.Amount = req.Amount
	createBody.TransactionStatus = "PENDING"
	insertId, err := s.repo.CreateDbPaymentcoOrder(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	// reget Order
	pendingOrder, err := s.repo.GetDbPaymentcoOrderById(*insertId)
	if err != nil {
		// SET AS ERROR
		remark := "Error GetDbPaymentcoOrderById, " + err.Error()
		if err := s.repo.UpdateDbPaymentcoOrderError(*insertId, remark); err != nil {
			log.Println("CreatePaymentcoWithdraw.UpdateDbPaymentcoOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// Create PAYMENTCO Order
	var remoteRequest model.PaymentcoWithdrawCreateRemoteRequest
	remoteRequest.OrderId = pendingOrder.OrderNo
	remoteRequest.Amount = fmt.Sprintf("%.2f", req.Amount)
	remoteRequest.AccountName = user.Fullname
	remoteRequest.BankName = withdrawBankCode
	remoteRequest.AccountNo = user.BankAccount
	remoteResp, err := s.repo.PaymentcoWithdraw(*pgAccount, remoteRequest)
	if err != nil {
		// SET AS ERROR
		remark := "Error PaymentcoWithdraw, " + err.Error()
		if err := s.repo.UpdateDbPaymentcoOrderError(*insertId, remark); err != nil {
			log.Println("PaymentcoWithdraw.UpdateDbPaymentcoOrderError", err)
		}
		// SysLog
		if _, err := s.repo.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "CreatePaymentcoWithdraw.PaymentcoWithdraw",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(remoteRequest),
			JsonResponse: helper.StructJson(map[string]interface{}{"error": err.Error()}),
		}); err != nil {
			log.Println("CreatePaymentcoWithdraw.CreateSysLog", err)
		}
		return nil, internalServerError(err)
	}

	// fmt.Println("CreatePaymentcoWithdraw.remoteResp", helper.StructJson(remoteResp))

	if remoteResp.Data.ReferenceId == "" || remoteResp.Data.TransactionId == "" {
		// SET AS ERROR
		remark := remoteResp.Message
		if len(remark) == 0 {
			remark = "UNKNOW_ERROR"
		}
		if err := s.repo.UpdateDbPaymentcoOrderError(*insertId, remark); err != nil {
			log.Println("PaymentcoWithdraw.UpdateDbPaymentcoOrderError", err)
		}
		// SysLog
		if _, err := s.repo.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "CreatePaymentcoWithdraw.PaymentcoWithdraw",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(remoteRequest),
			JsonResponse: helper.StructJson(remoteResp),
		}); err != nil {
			log.Println("CreatePaymentcoWithdraw.CreateSysLog", err)
		}
		return nil, internalServerError(fmt.Errorf("UNKNOW_ERROR"))
	}

	// onCreate Success
	var updateBody model.PaymentcoOrderUpdateBody
	updateBody.TransactionNo = remoteResp.Data.ReferenceId
	updateBody.TransactionDate = time.Now()
	updateBody.TransactionStatus = "WAIT_PAYMENT"
	updateBody.QrPromptpay = remoteResp.Message
	if err := s.repo.UpdateDbPaymentcoOrder(*insertId, updateBody); err != nil {
		// SET AS ERROR
		remark := "Error UpdateDbPaymentcoOrder, " + err.Error()
		if err := s.repo.UpdateDbPaymentcoOrderError(*insertId, remark); err != nil {
			log.Println("CreatePaymentcoWithdraw.UpdateDbPaymentcoOrderError", err)
		}
		return nil, internalServerError(err)
	}
	return insertId, nil
}

func CreateCustomerDepositFromPaymentcoOrder(repo repository.PaymentcoRepository, orderId int64, adminId int64) (*int64, error) {

	// ** ทุกรายการจะต้องเช็คสถานะใหม่ทั้งหมด เพราะ admin แย่งกดอนุมัติได้
	item, err := repo.GetRawPaymentcoPendingDepositOrderById(orderId)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, badRequest("ORDER_NOT_FOUND")
		}
		return nil, internalServerError(err)
	}

	// Check BankTransactionStatus = 'PENDING'
	if item.BankTransactionStatus != "PENDING" {
		return nil, badRequest("ORDER_NOT_PENDING")
	}

	return createCustomerDepositFromPaymentco(repo, *item, adminId)
}

func createCustomerDepositFromPaymentco(repo repository.PaymentcoRepository, item model.PaymentcoOrderResponse, adminId int64) (*int64, error) {

	var externalNoti model.NotifyExternalNotificationRequest
	actionAt := time.Now()
	// confirmByUserId := int64(0)

	depositAmount := item.Amount

	user, err := repo.GetUserBankDetailById(item.UserId)
	if err != nil {
		log.Println("createCustomerDepositFromPaymentco.GetUserBankDetailByMemberCode", err)
		return nil, internalServerError(err)
	}

	var promotionWebUserId int64
	if getPromotionWebUser, err := repo.GetDepositCurrentProcessingUserPromotion(item.UserId); err != nil {
		log.Println("createCustomerDepositFromPaymentco.GetDepositCurrentProcessingUserPromotion", err)
	} else if getPromotionWebUser != nil {
		promotionWebUserId = getPromotionWebUser.Id
	}

	// MemberCode
	if user.MemberCode == "" {
		memberCode, err := GenUniqueUserMemberCode(repository.NewUserRepository(repo.GetDb()), user.Id)
		if err != nil {
			log.Println("createCustomerDepositFromPaymentco.GenUniqueUserMemberCode", err)
			return nil, internalServerError(err)
		}
		user.MemberCode = *memberCode
	}

	// layer — 2024-04-02 at 10:06 AM
	// ถ้าแก้ตามนี้แล้วน่าจะไม่เจอ ถ้าเคสกันซ้ำเราครอบคลุม
	// @Dunk @TULA
	// กันซ้ำคือ heng ไปซ้ำกับแอดมินเติมมือ
	// ----
	// เงื่อนไขกันซ้ำ
	// 1 หารายการฝากสำเร็จล่าสุดของยูสนั้น
	// 2 ถ้ามีรายการล่าสุดที่เป็นเติมมือ
	// -เช็คเวลาเติม ต้องมากกว่าเวลาโอนในสลิป
	// -ยอดเงินเท่ากัน
	// **ถ้าเข้าเงื่อนไขทั้งหมด คือแสดงว่า webhook เข้าทีหลังแอดมินเติมมือ
	// ต้อง bypass ไม่ให้เติมอีก และอัพเดท status ว่าจับคู่ได่แล้ว
	// ---
	// วิธีนี้น่าจะใช้ได้กับทุก webhook (heng, fastbank) ที่มีเวลาโอนสลิปมาให้
	duplicateRemark := "INIT"
	hasDuplicateDeposit := false
	manualDeposit, err := repo.GetLastestBankAdminManualDepositTransaction(user.Id)
	if err != nil {
		if err.Error() != recordNotFound {
			log.Println("createCustomerDepositFromPaymentco.GetLastestBankAdminManualDepositTransaction", err)
			return nil, internalServerError(err)
		}
		// NOT_FOUND = ถ้าไม่เจอ ให้ทำต่อ
		duplicateRemark = "OK_NO_MANUAL_DEPOSIT"
	}
	if manualDeposit != nil && item.TransactionDate != nil {
		if manualDeposit.TransferAt.After(*item.TransactionDate) {
			// ถ้าเวลาเติมมือมากกว่าเวลาโอนในสลิป
			if manualDeposit.CreditAmount == depositAmount {
				// ถ้ายอดเงินเท่ากัน
				duplicateRemark = "STOP_MANUAL_DEPOSIT_AFTER_AND_SAME_AMOUNT"
				hasDuplicateDeposit = true
			} else {
				duplicateRemark = "OK_MANUAL_DEPOSIT_AMOUNT_NOT_MATCH"
			}
		} else {
			duplicateRemark = "OK_MANUAL_DEPOSIT_BEFORE_WITHDRAW"
		}
	}
	if duplicateRemark != "INIT" {
		// WebhookLog
		var createBody2 model.PaymentcoWebhookCreateBody
		createBody2.Name = "PAYMENTCO_DEPOSIT_DUPLICATE_CHECKER"
		createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
			"item":                item,
			"method":              "createCustomerDeposit",
			"hasDuplicateDeposit": hasDuplicateDeposit,
			"error":               duplicateRemark,
			"manualDeposit":       manualDeposit,
		})
		if _, err = repo.CreatePaymentcoWebhook(createBody2); err != nil {
			log.Println("Error CreatePaymentcoWebhook.CreatePaymentcoWebhook", err)
		}
		if hasDuplicateDeposit {
			return nil, internalServerError(fmt.Errorf("DUPLICATE_DEPOSIT"))
		}
	}

	pgAccount, err := GetPaymentcoAccount(repo)
	if err != nil || pgAccount == nil {
		return nil, internalServerError(err)
	}

	// ===================================================
	paygateRepo := repository.NewPaymentGatewayRepository(repo.GetDb())
	accountingRepo := repository.NewAccountingRepository(repo.GetDb())
	luckyWheelRepo := repository.NewActivityLuckyWheelRepository(repo.GetDb())
	promotionWebRepo := repository.NewPromotionWebRepository(repo.GetDb())
	notiRepo := repository.NewNotificationRepository(repo.GetDb())

	// Affiliate + Alliance Income
	member, err := repo.GetMemberById(user.Id)
	if err != nil {
		return nil, internalServerError(err)
	}
	if member.UserTypeName == "NONE" {
		// ถ้าฝากครั้งแรก จะได้เป็น AFFILIATE + REFER COMMISION
		if err := UserFirstDepositCommission(accountingRepo, *member, depositAmount); err != nil {
			log.Println("CreateFristTimeDepositRecord.UserFirstDepositCommission.ERROR", err)
			return nil, internalServerError(err)
		}
	}

	isAutoDeposit := true // Auto ฝาก นับตั้งแต่ได้รับยอดฝาก จนได้ยืนยันเครดิต
	if adminId != 0 {
		isAutoDeposit = false
	}

	// CREATE BANK TRANSACTION
	var createBankTransaction model.BankTransactionCreateBody
	createBankTransaction.MemberCode = user.MemberCode
	createBankTransaction.UserId = user.Id
	// createBankTransaction.StatementId = 0
	createBankTransaction.TransactionTypeId = model.TRANSACTION_TYPE_DEPOSIT
	// createBankTransaction.FromAccountName = "" // empty?
	createBankTransaction.ToAccountName = &pgAccount.TypeName
	createBankTransaction.ToAccountNumber = &pgAccount.ShopName
	createBankTransaction.PromotionId = &promotionWebUserId
	createBankTransaction.CreditAmount = depositAmount
	// createBankTransaction.BonusAmount = 0
	// createBankTransaction.BonusReason = nil
	createBankTransaction.DepositChannel = "PAYMENTCO PAYMENT GATEWAY"
	createBankTransaction.TransactionStatusId = model.TRANS_STATUS_PENDING
	createBankTransaction.CreatedByAdminId = 0
	createBankTransaction.IsAutoCredit = isAutoDeposit
	createBankTransaction.TransferAt = &actionAt
	// create transaction
	transId, err := repo.InsertBankTransaction(createBankTransaction)
	if err != nil {
		log.Println("createCustomerDepositFromPaymentco.InsertBankTransaction", err)
		return nil, internalServerError(err)
	}

	// var actionStatement model.BankStatementMatchRequest
	// actionStatement.StatementId = *bankstatementId
	// actionStatement.UserId = user.Id
	// actionStatement.ConfirmedAt = time
	// actionStatement.ConfirmedByAdminId = &req.CreateByUserId
	// if err := s.serviceAccounting.SetStatementOwnerMatched(*bankstatementId, actionStatement, model.USE_ENDING_NOTI); err != nil {
	// 	log.Println("createCustomerDepositFromPaymentco.SetStatementOwnerMatched", err)
	// 	return nil, internalServerError(err)
	// }

	// DECREASE FASTBANK CREDIT 2.FROM create auto DEPOSIT
	// if err := repoAccounting.DecreaseFastbankCredit(1); err != nil {
	// 	log.Println("createCustomerDepositFromPaymentco.DECREASE_FASTBANK_CREDIT_ERROR", err)
	// }

	var confirmDeposit model.BankConfirmDepositRequest
	confirmDeposit.TransferAt = &actionAt
	confirmDeposit.ConfirmedAt = actionAt
	confirmDeposit.ConfirmedByAdminId = &adminId
	if err := confirmDepositTransaction(paygateRepo, *transId, confirmDeposit); err != nil {
		log.Println("createCustomerDepositFromPaymentco.confirmDepositTransaction", err)
		return nil, internalServerError(err)
	}

	// UPDATE HENG ORDER - BANK SUCCESS
	if err := repo.UpdatePaymentcoOrderBankSucess(item.Id, *transId, adminId); err != nil {
		log.Println("createCustomerDepositFromPaymentco.UpdatePaygateOrderBankSucess", err)
	}

	isFirstDeposit := repo.IsFirstDeposit(user.Id)
	if isFirstDeposit {
		var bonusReq model.UserFirstDepositCreateRequest
		bonusReq.UserId = user.Id
		bonusReq.TransactionId = transId
		bonusReq.TransferAt = actionAt
		bonusReq.Amount = depositAmount
		bonusReq.Remark = "createCustomerDepositFromPaymentco"
		if err := SetFirstDepositBonus(accountingRepo, isFirstDeposit, bonusReq); err != nil {
			webhookLogMessage := fmt.Sprintf("createCustomerDepositFromPaymentco.userFirstDepositBonus, ERROR: %s", err)
			log.Println("SetFirstDepositBonus", webhookLogMessage)
			// return nil, errors.New(webhookLogMessage)
		}
	}

	// [USER_CREDIT]
	var userCreditReq model.UserTransactionCreateRequest
	userCreditReq.UserId = user.Id
	userCreditReq.RefId = transId
	userCreditReq.TransferAt = &actionAt
	userCreditReq.PromotionId = &promotionWebUserId
	userCreditReq.TypeId = model.CREDIT_TYPE_DEPOSIT
	// userCreditReq.AccountId = &bankAccount.Id
	userCreditReq.Detail = "PAYMENTCO PAYMENT GATEWAY"
	userCreditReq.Amount = depositAmount
	userCreditReq.CreateBy = &adminId
	userCreditReq.ConfirmBy = &adminId
	userCreditReq.IsAdjustAuto = isAutoDeposit
	userCreditReq.PaymentMerchatId = &pgAccount.ProviderId
	if agentResp, err := repo.IncreaseUserCredit(userCreditReq); err != nil {
		log.Println("createCustomerDepositFromPaymentco.IncreaseUserCredit", err)
		return nil, internalServerError(err)
	} else {
		// AGENT_SUCCESS
		if err := repo.UpdateDepositTransactionStatusFromAgent(*transId, *agentResp); err != nil {
			log.Println("createCustomerDepositFromPaymentco.UpdateDeporsitTransactionStatusFromAgent", err)
		}
	}

	// [Lucky Wheel] ตอนฝากละได้โบนัส
	var luckyWheelBody model.ActivityLuckyWheelRoundUserRequest
	luckyWheelBody.UserId = user.Id
	luckyWheelBody.ConditionId = model.ACTIVITY_LUCKY_WHEEL_SETTING_CONDITION_DEPOSIT
	luckyWheelBody.ConditionAmount = depositAmount
	if err := CreateRoundActivityLuckyWheel(luckyWheelRepo, luckyWheelBody); err != nil {
		log.Println("createCustomerDepositFromPaymentco.CreateRoundActivityLuckyWheel", err)
	}

	// [TIER]
	if err := repo.IncreaseUserTierDepositAmount(user.Id, depositAmount); err != nil {
		log.Println("MatchStatementOwner.IncreaseUserTierDepositAmount", err)
	}

	var checkUserPromotionBody model.CheckUserPromotionBody
	checkUserPromotionBody.UserId = user.Id
	_, err = CheckUserPromotion(promotionWebRepo, checkUserPromotionBody)
	if err != nil {
		log.Println("createCustomerDepositFromPaymentco.CheckUserPromotion", err)
	}
	// [turnOver] ถ้ามีการฝากเงิน ให้สร้าง TurnOver
	turnoverShare := repository.NewTurnoverRepository(repo.GetDb())
	if err := CreateTurnOverFromSuccessDeposit(turnoverShare, user.Id, depositAmount, *transId); err != nil {
		log.Println("createCustomerDeposit.CreateTurnOverFromSuccessDeposit", err)
	}

	// ===================================================

	// [ notify]
	externalNoti.TypeNotify = model.IsDepositAfterCredit
	externalNoti.TransId = transId
	externalNoti.Amount = depositAmount
	externalNoti.MemberCode = user.MemberCode
	externalNoti.UserCredit = user.Credit + depositAmount
	externalNoti.ConfirmedByAdminId = 0
	externalNoti.TransferDateTime = time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02 15:04:05")
	externalNoti.ActionTime = time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02 15:04:05")
	endTime := time.Now()
	elapsed := endTime.Sub(actionAt)
	elapsedSeconds := elapsed.Seconds()
	timeElapsed := fmt.Sprintf("%.2f", elapsedSeconds)
	externalNoti.TimerCounter = timeElapsed

	externalNoti.WebScoket.UserID = user.Id
	externalNoti.WebScoket.Amount = depositAmount
	externalNoti.WebScoket.MemberCode = user.MemberCode
	externalNoti.WebScoket.AlertType = "DEPOSIT"
	if err := repo.UpdateAutoProcessTimer(timeElapsed, *transId); err != nil {
		return nil, nil
	}
	if err := ExternalNotification(notiRepo, externalNoti); err != nil {
		log.Println("FailedNotify", err)
	}
	return nil, nil
}

func ApproveCustomerWithdrawFromPaymentco(repo repository.PaymentcoRepository, transId int64) (*int64, error) {

	withdrawTrans, err := repo.GetBankTransactionById(transId)
	if err != nil {
		log.Println("approveCustomerWithdrawFromPaymentco.GetUserBankDetailById", err)
		return nil, internalServerError(err)
	}

	// ============================= ON_SUCCESS =================================
	if withdrawTrans.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		// [create transaction action success]
		var createConfirm model.CreateSuccessTransferWithdrawRequest
		createConfirm.TransactionId = withdrawTrans.Id
		createConfirm.ConfirmedAt = time.Now()
		var setIdAuto int64 = 0
		createConfirm.ConfirmedByAdminId = &setIdAuto
		if _, err := CreateSuccessTransferWithdraw(repository.NewBankingRepository(repo.GetDb()), createConfirm); err != nil {
			log.Println("approveCustomerWithdrawFromPaymentco.CreateSuccessTransferWithdraw", err)
			return nil, nil
		}
		// [Show Withdraw USER_TRANSACTION]
		var showUserTrans model.UserTransactionShowUpdate
		showUserTrans.TransactionId = withdrawTrans.Id
		showUserTrans.ConfirmedAt = &createConfirm.ConfirmedAt
		showUserTrans.ConfirmAdminId = createConfirm.ConfirmedByAdminId
		if err := repo.ShowUserTransaction(showUserTrans); err != nil {
			log.Println("approveCustomerWithdrawFromPaymentco.ShowUserTransaction", err)
			return nil, nil
		}
		// [update transaction status]
		secondUsed := "0.0" // timing
		if withdrawTrans.IsAutoCredit && withdrawTrans.TransferAt != nil {
			seconds := time.Now().UTC().Sub(withdrawTrans.TransferAt.UTC()).Seconds()
			secondUsed = fmt.Sprintf("%.2f", seconds)
		}
		if err := repo.UpdateTransactionStatusTransferingToSuccess(withdrawTrans.Id, secondUsed); err != nil {
			log.Println("approveCustomerWithdrawFromPaymentco.UpdateTransactionStatusTransferingToSuccess", err)
			return nil, internalServerError(err)
		}
	}
	return nil, nil
}

// func rollbackCustomerWithdrawFromPaymentco(repo repository.PaymentcoRepository, transId int64) (*int64, error) {

// 	withdrawTrans, err := repo.GetBankTransactionById(transId)
// 	if err != nil {
// 		log.Println("rollbackCustomerWithdrawFromPaymentco.GetBankTransactionById", err)
// 		return nil, internalServerError(err)
// 	}

// 	// ============================= ON_SUCCESS =================================
// 	if withdrawTrans.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_TRASNFERING {
// 		// [update transaction status]
// 		if err := repo.RollbackTransactionStatusTransferingToConfirmed(withdrawTrans.Id); err != nil {
// 			log.Println("approveCustomerWithdrawFromPaymentco.RollbackTransactionStatusTransferingToConfirmed", err)
// 			return nil, internalServerError(err)
// 		}
// 	}
// 	return nil, nil
// }

func (s paygatePaymentcoService) CreateSystemLog(name string, req interface{}) error {

	// [SYSLOG] HACKED
	_, err := s.repo.CreateSystemLog(model.SystemLogCreateBody{
		Name:   name,
		Status: "HACKING",
		JsonRequest: helper.StructJson(map[string]interface{}{
			"req": req,
		}),
	})
	if err != nil {
		log.Println(name, "CreateSystemLog.ERROR", err.Error())
	}
	return nil
}

func (s paygatePaymentcoService) CancelWithdrawFromPaymentco(transId int64, adminId int64) error {

	withdrawTrans, err := s.repo.GetBankTransactionById(transId)
	if err != nil {
		log.Println("rollbackWithdrawFromPaymentco.GetBankTransactionById", err)
		return internalServerError(err)
	}
	if withdrawTrans.TransactionStatusId != model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		return internalServerError(errors.New("ไม่อยู๋ในสถานะที่สามารถยกเลิกได้"))
	}

	user, err := s.repo.GetUserBankDetailById(withdrawTrans.UserId)
	if err != nil {
		return internalServerError(err)
	}

	paygateOrder, err := s.repo.GetDbPaymentcoOrderByRefId(transId)
	if err != nil {
		log.Println("CancelWithdrawFromPaymentco.GetDbPaymentcoOrderByRefId", err)
		return internalServerError(err)
	}
	waitStatus := "ERROR"
	if paygateOrder.TransactionStatus == nil || *paygateOrder.TransactionStatus != waitStatus {
		return internalServerError(errors.New("ไม่อยู๋ในสถานะที่สามารถยกเลิกได้"))
	}

	returnAmount := withdrawTrans.CreditAmount

	// race condition deposit
	actionAt := time.Now().UTC().Format("************")
	var createBody model.RaceActionCreateBody
	createBody.Name = "ADMIN_CANCEL_WITHDRAW"
	createBody.JsonRequest = helper.StructJson(map[string]interface{}{
		"transId":      transId,
		"returnAmount": returnAmount,
	})
	createBody.Status = "PENDING"
	createBody.ActionKey = fmt.Sprintf("WITHDRAW_CANCEL_T%sU%dID%d", actionAt, user.Id, transId)
	createBody.UnlockAt = time.Now().UTC().Add(time.Second * 1)
	if _, err := s.repo.GetRaceActionIdByActionKey(createBody.ActionKey); err != nil {
		log.Println("CancelWithdrawFromPaymentco.GetRaceActionIdByActionKey", err)
		if err != gorm.ErrRecordNotFound {
			return internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
		}
	} else {
		return internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
	}

	// create race condition
	actionId, err := s.repo.CreateRaceCondition(createBody)
	if err != nil {
		log.Println("CancelWithdrawFromPaymentco.ERROR.CreateRaceCondition", err)
		return internalServerError(errors.New("CANNOT_CREATE_ACTION_LUCKY_WHEEL_ROUND"))
	}
	if actionId == 0 {
		return internalServerError(errors.New("CANNOT_CREATE_ACTION_LUCKY_WHEEL_ROUND"))
	}

	// SET ORDER AS ERROR
	remark := "Admin Cancel Withdraw"
	if err := s.repo.UpdateDbPaymentcoOrderError(paygateOrder.Id, remark); err != nil {
		log.Println("CreatePaymentcoWithdraw.UpdateDbPaymentcoOrderError", err)
	}

	if withdrawTrans.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		// [********] check user credit transaction ว่าตรงไหมกับเงื่อนไข (isShow = false)
		userTransaction, err := s.repo.GetUserWithdrawCreditTransactionByRefId(withdrawTrans.Id, returnAmount)
		if err != nil {
			if err.Error() == recordNotFound {
				log.Println("CancelWithdrawCredit.GetUserCreditTransactionById", err)
				return badRequest("ไม่ตรงกับเงือนไข")
			}
			return internalServerError(err)
		}
		if userTransaction != nil {
			var userCreditReq model.UserTransactionCreateRequest
			userCreditReq.UserId = user.Id
			userCreditReq.TransferAt = withdrawTrans.TransferAt
			userCreditReq.RefId = &withdrawTrans.Id
			userCreditReq.TypeId = model.CREDIT_TYPE_DEPOSIT
			userCreditReq.Amount = returnAmount
			IsShow := false
			userCreditReq.IsShow = &IsShow
			userCreditReq.ConfirmBy = &adminId
			if agentResp, err := s.repo.IncreaseUserCredit(userCreditReq); err != nil {
				log.Println("CancelWithdrawCredit.IncreaseUserCredit", err)
				return internalServerError(err)
			} else if !agentResp.AgentSuccess {
				// [********] error from agent will be in failed
				// if err := s.repoAccounting.UpdateDeporsitTransactionStatusFromAgent(*transId, *agentResp); err != nil {
				//     log.Println("CancelWithdrawCredit.UpdateDeporsitTransactionStatusFromAgent", err)
				// }
				// Set FastBank-READ ** ไม่มี เพราะเป็นการสร้างรายการฝากเอง
				agentFail := errors.New("ทำรายการเครดิตไม่สำเร็จ")
				log.Println("CancelWithdrawCredit.IncreaseUserCredit", agentFail)
				return internalServerError(agentFail)
			}
		}
	}

	// remark := "ADMIN_CANCEL_WITHDRAW"
	var updateApprovedBy model.UpdateUserTransactionStatusRequest
	updateApprovedBy.Id = withdrawTrans.Id
	updateApprovedBy.CanceledAt = time.Now()
	updateApprovedBy.CanceledByAdminId = adminId
	updateApprovedBy.TransactionStatusId = model.TRANS_STATUS_WITHDRAW_CANCELED
	updateApprovedBy.CancelRemark = &remark
	err = s.repo.UpdateUserTransactionStatus(updateApprovedBy)
	if err != nil {
		return internalServerError(err)
	}

	var createConfirm model.CreateSuccessTransferWithdrawRequest
	createConfirm.TransactionId = withdrawTrans.Id
	createConfirm.ConfirmedAt = time.Now()
	createConfirm.ConfirmedByAdminId = &adminId
	if _, err := CreateCanceledTransferWithdraw(repository.NewBankingRepository(s.sharedDb), createConfirm); err != nil {
		return err
	}

	return nil
}

func (s paygatePaymentcoService) CreatePaymentcoDepositWebhook(req model.PaymentcoWebhookRequest) (*int64, error) {

	var createBody model.PaymentcoWebhookCreateBody
	createBody.Name = "PAYMENTCO_DEPOSIT_CALLBACK"
	createBody.JsonPayload = req.JsonPayload
	insertId, err := s.repo.CreatePaymentcoWebhook(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// DEPOSIT-TODO

	return insertId, nil
}

func (s paygatePaymentcoService) CreatePaymentcoWithdrawWebhook(req model.PaymentcoWebhookRequest) (*int64, error) {

	var createBody model.PaymentcoWebhookCreateBody
	createBody.Name = "PAYMENTCO_WITHDRAW_CALLBACK"
	createBody.JsonPayload = req.JsonPayload
	insertId, err := s.repo.CreatePaymentcoWebhook(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// WITHDRAW-TODO

	return insertId, nil
}

func (s paygatePaymentcoService) CancelWithdrawFromPaymentcoWebhookError(payonexOrder model.PaymentcoOrderResponse) error {

	adminId := int64(1)

	withdrawTrans, err := s.repo.GetBankTransactionById(*payonexOrder.RefId)
	if err != nil {
		log.Println("rollbackWithdrawFromPaymentco.GetBankTransactionById", err)
		return internalServerError(err)
	}
	if withdrawTrans.TransactionStatusId != model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		return internalServerError(errors.New("ไม่อยู๋ในสถานะที่สามารถยกเลิกได้"))
	}

	user, err := s.repo.GetUserBankDetailById(withdrawTrans.UserId)
	if err != nil {
		return internalServerError(err)
	}

	paygateOrder, err := s.repo.GetDbPaymentcoOrderByRefId(withdrawTrans.Id)
	if err != nil {
		log.Println("CancelWithdrawFromPaymentco.GetDbPaymentcoOrderByRefId", err)
		return internalServerError(err)
	}
	waitStatus := "ERROR"
	if paygateOrder.TransactionStatus == nil || *paygateOrder.TransactionStatus != waitStatus {
		return internalServerError(errors.New("ไม่อยู๋ในสถานะที่สามารถยกเลิกได้"))
	}

	returnAmount := withdrawTrans.CreditAmount

	// race condition deposit
	actionAt := time.Now().UTC().Format("************")
	var createBody model.RaceActionCreateBody
	createBody.Name = "ADMIN_CANCEL_WITHDRAW"
	createBody.JsonRequest = helper.StructJson(map[string]interface{}{
		"transId":      withdrawTrans.Id,
		"returnAmount": returnAmount,
	})
	createBody.Status = "PENDING"
	createBody.ActionKey = fmt.Sprintf("WITHDRAW_CANCEL_T%sU%dID%d", actionAt, user.Id, withdrawTrans.Id)
	createBody.UnlockAt = time.Now().UTC().Add(time.Second * 1)
	if _, err := s.repo.GetRaceActionIdByActionKey(createBody.ActionKey); err != nil {
		log.Println("CancelWithdrawFromPaymentco.GetRaceActionIdByActionKey", err)
		if err != gorm.ErrRecordNotFound {
			return internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
		}
	} else {
		return internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
	}

	// create race condition
	actionId, err := s.repo.CreateRaceCondition(createBody)
	if err != nil {
		log.Println("CancelWithdrawFromPaymentco.ERROR.CreateRaceCondition", err)
		return internalServerError(errors.New("CANNOT_CREATE_ACTION_LUCKY_WHEEL_ROUND"))
	}
	if actionId == 0 {
		return internalServerError(errors.New("CANNOT_CREATE_ACTION_LUCKY_WHEEL_ROUND"))
	}

	// SET ORDER AS ERROR
	remark := "Payment Cancel Withdraw"
	if err := s.repo.UpdateDbPaymentcoOrderError(paygateOrder.Id, remark); err != nil {
		log.Println("CreatePaymentcoWithdraw.UpdateDbPaymentcoOrderError", err)
	}

	if withdrawTrans.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		// [********] check user credit transaction ว่าตรงไหมกับเงื่อนไข (isShow = false)
		userTransaction, err := s.repo.GetUserWithdrawCreditTransactionByRefId(withdrawTrans.Id, returnAmount)
		if err != nil {
			if err.Error() == recordNotFound {
				log.Println("CancelWithdrawCredit.GetUserCreditTransactionById", err)
				return badRequest("ไม่ตรงกับเงือนไข")
			}
			return internalServerError(err)
		}
		if userTransaction != nil {
			var userCreditReq model.UserTransactionCreateRequest
			userCreditReq.UserId = user.Id
			userCreditReq.TransferAt = withdrawTrans.TransferAt
			userCreditReq.RefId = &withdrawTrans.Id
			userCreditReq.TypeId = model.CREDIT_TYPE_DEPOSIT
			userCreditReq.Amount = returnAmount
			IsShow := false
			userCreditReq.IsShow = &IsShow
			userCreditReq.ConfirmBy = &adminId
			if agentResp, err := s.repo.IncreaseUserCredit(userCreditReq); err != nil {
				log.Println("CancelWithdrawCredit.IncreaseUserCredit", err)
				return internalServerError(err)
			} else if !agentResp.AgentSuccess {
				// [********] error from agent will be in failed
				// if err := s.repoAccounting.UpdateDeporsitTransactionStatusFromAgent(*transId, *agentResp); err != nil {
				//     log.Println("CancelWithdrawCredit.UpdateDeporsitTransactionStatusFromAgent", err)
				// }
				// Set FastBank-READ ** ไม่มี เพราะเป็นการสร้างรายการฝากเอง
				agentFail := errors.New("ทำรายการเครดิตไม่สำเร็จ")
				log.Println("CancelWithdrawCredit.IncreaseUserCredit", agentFail)
				return internalServerError(agentFail)
			}
		}
	}

	// remark := "ADMIN_CANCEL_WITHDRAW"
	var updateApprovedBy model.UpdateUserTransactionStatusRequest
	updateApprovedBy.Id = withdrawTrans.Id
	updateApprovedBy.CanceledAt = time.Now()
	updateApprovedBy.CanceledByAdminId = adminId
	updateApprovedBy.TransactionStatusId = model.TRANS_STATUS_WITHDRAW_CANCELED
	updateApprovedBy.CancelRemark = &remark
	err = s.repo.UpdateUserTransactionStatus(updateApprovedBy)
	if err != nil {
		return internalServerError(err)
	}

	var createConfirm model.CreateSuccessTransferWithdrawRequest
	createConfirm.TransactionId = withdrawTrans.Id
	createConfirm.ConfirmedAt = time.Now()
	createConfirm.ConfirmedByAdminId = &adminId
	if _, err := CreateCanceledTransferWithdraw(repository.NewBankingRepository(s.sharedDb), createConfirm); err != nil {
		return err
	}

	return nil
}
