package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"fmt"
	"log"
	"math"
	"strings"
	"time"

	"gorm.io/gorm"
)

type ActivityDailyService interface {
	// option
	GetActivityDailyCondition() ([]model.ActivityDailyConditionResponse, error)
	GetActivityDailyStatus() ([]model.ActivityDailyStatusResponse, error)
	// main
	GetFirstActivityDaily() (*model.GetActivityDailyResponse, error)
	CreateAndUpdateFirstActivityDaily(req model.UpdateActivityDailyRequest) (int64, error)
	// web
	GetWebActivityDaily(userId int64) (*model.GetWebActivityDailyResponse, error)
	CreateUserColletedActivityDaily(req model.CreateActivityDailyUserRequest) (int64, error)
	GetSevenDayCollectedBouns(userId int64) (*model.GetSevenDayCollectedBounsResponse, error)
	CreateSevenDayCollectedBouns(req model.CreateActivityDailyUserRequest) error
	// turnover
	GetTurnoverUserActivityDaily(req model.GetTurnoverUserActivityDailyRequest) (*model.SuccessWithPagination, error)
}

type activityDailyService struct {
	shareDb *gorm.DB
	repo    repository.ActivityDailyRepository
}

func NewactivityDailyService(
	shareDb *gorm.DB,
	repo repository.ActivityDailyRepository,
) ActivityDailyService {
	return &activityDailyService{shareDb, repo}
}

func (s activityDailyService) GetActivityDailyCondition() ([]model.ActivityDailyConditionResponse, error) {

	option, err := s.repo.GetActivityDailyCondition()
	if err != nil {
		return nil, err
	}
	return option, nil
}

func (s activityDailyService) GetActivityDailyStatus() ([]model.ActivityDailyStatusResponse, error) {

	option, err := s.repo.GetActivityDailyStatus()
	if err != nil {
		return nil, err
	}
	return option, nil
}

func (s activityDailyService) GetFirstActivityDaily() (*model.GetActivityDailyResponse, error) {

	option, err := s.repo.GetFirstActivityDaily()
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(recordNotFound)
		}
		return nil, nil
	}

	return option, nil
}

func (s activityDailyService) CreateAndUpdateFirstActivityDaily(req model.UpdateActivityDailyRequest) (int64, error) {

	activityDaily, err := s.repo.GetFirstActivityDaily()
	if err != nil {
		var body model.CreateActivityDailyRequest
		body.MondayBonus = *req.MondayBonus
		body.TuesdayBonus = *req.TuesdayBonus
		body.WednesdayBonus = *req.WednesdayBonus
		body.ThursdayBonus = *req.ThursdayBonus
		body.FridayBonus = *req.FridayBonus
		body.SaturdayBonus = *req.SaturdayBonus
		body.SundayBonus = *req.SundayBonus
		body.CompletedBonus = *req.CompletedBonus
		body.ActivityDailyConditionId = req.ActivityDailyConditionId
		body.ActivityDailyStatusId = req.ActivityDailyStatusId
		body.DepositAmountCondition = *req.DepositAmountCondition
		body.UpdatedByAdminId = req.UpdatedByAdminId

		id, err := s.repo.CreateFirstActivityDaily(body)
		if err != nil {
			return 0, err
		}
		return id, nil
	} else {
		req.Id = activityDaily.Id
		err := s.repo.UpdateFirstActivityDaily(req)
		if err != nil {
			return 0, err
		}
		return activityDaily.Id, nil
	}

}

func (s activityDailyService) GetWebActivityDaily(userId int64) (*model.GetWebActivityDailyResponse, error) {

	dayName, currentModelDay, playDate, _, _ := s.getTodayDay()
	//เอา config มา
	activityDaily, err := s.repo.GetFirstActivityDaily()
	if err != nil {
		return nil, err
	}

	var webActiveResponse model.GetWebActivityDailyResponse
	webActiveResponse.ActivityDailyStatusId = activityDaily.ActivityDailyStatusId
	webActiveResponse.ActivityDailyStatusTh = activityDaily.ActivityDailyStatusTh
	webActiveResponse.ActivityDailyConditionId = activityDaily.ActivityDailyConditionId
	webActiveResponse.DepositAmountCondition = activityDaily.DepositAmountCondition
	webActiveResponse.ActivityDailyConditionTh = activityDaily.ActivityDailyConditionTh
	webActiveResponse.MondayBonus = activityDaily.MondayBonus
	webActiveResponse.TuesdayBonus = activityDaily.TuesdayBonus
	webActiveResponse.WednesdayBonus = activityDaily.WednesdayBonus
	webActiveResponse.ThursdayBonus = activityDaily.ThursdayBonus
	webActiveResponse.FridayBonus = activityDaily.FridayBonus
	webActiveResponse.SaturdayBonus = activityDaily.SaturdayBonus
	webActiveResponse.SundayBonus = activityDaily.SundayBonus
	webActiveResponse.TodayAvailableDay = currentModelDay
	if activityDaily.ActivityDailyStatusId == model.ACTIVITY_DAILY_STATUS_DEACTIVE {
		return &webActiveResponse, nil
	}
	// check เงือนไข ว่าผ่านไหม
	var checkPassConditionUser model.CheckConditionUserRequest
	checkPassConditionUser.UserId = userId
	checkPassConditionUser.ConditionId = activityDaily.ActivityDailyConditionId
	checkPassConditionUser.PlayDate = playDate
	checkPassConditionUser.DepositAmountCondition = activityDaily.DepositAmountCondition
	ConditionUser, err := s.CheckConditionUserColletedActivityDaily(checkPassConditionUser)
	if err != nil {
		return nil, err
	}

	currentWeekCollected, err := s.repo.GetActivityDailyUserOnCurrentWeek(userId)
	if err != nil {
		return nil, err
	}

	// วันที่เคยกดรับไปแล้ว
	for _, v := range currentWeekCollected {
		if v.ActivityDayId == model.ACTIVITY_DAY_MONDAY {
			webActiveResponse.UserCollectedMon = true
		} else if v.ActivityDayId == model.ACTIVITY_DAY_TUESDAY {
			webActiveResponse.UserCollectedTue = true
		} else if v.ActivityDayId == model.ACTIVITY_DAY_WEDNESDAY {
			webActiveResponse.UserCollectedWed = true
		} else if v.ActivityDayId == model.ACTIVITY_DAY_THURSDAY {
			webActiveResponse.UserCollectedThu = true
		} else if v.ActivityDayId == model.ACTIVITY_DAY_FRIDAY {
			webActiveResponse.UserCollectedFri = true
		} else if v.ActivityDayId == model.ACTIVITY_DAY_SATURDAY {
			webActiveResponse.UserCollectedSat = true
		} else if v.ActivityDayId == model.ACTIVITY_DAY_SUNDAY {
			webActiveResponse.UserCollectedSun = true
		}
	}

	// วันที่กดรับ และถ้ารับไปแล้วจะไม่ให้กด
	if !ConditionUser {
		webActiveResponse.IsMon = false
		webActiveResponse.IsTue = false
		webActiveResponse.IsWed = false
		webActiveResponse.IsThu = false
		webActiveResponse.IsFri = false
		webActiveResponse.IsSat = false
		webActiveResponse.IsSun = false
	} else {
		if dayName == "MONDAY" {
			webActiveResponse.IsMon = true
			if webActiveResponse.UserCollectedMon {
				webActiveResponse.IsMon = false
			}
		} else if dayName == "TUESDAY" {
			webActiveResponse.IsTue = true
			if webActiveResponse.UserCollectedTue {
				webActiveResponse.IsTue = false
			}
		} else if dayName == "WEDNESDAY" {
			webActiveResponse.IsWed = true
			if webActiveResponse.UserCollectedWed {
				webActiveResponse.IsWed = false
			}
		} else if dayName == "THURSDAY" {
			webActiveResponse.IsThu = true
			if webActiveResponse.UserCollectedThu {
				webActiveResponse.IsThu = false
			}
		} else if dayName == "FRIDAY" {
			webActiveResponse.IsFri = true
			if webActiveResponse.UserCollectedFri {
				webActiveResponse.IsFri = false
			}
		} else if dayName == "SATURDAY" {
			webActiveResponse.IsSat = true
			if webActiveResponse.UserCollectedSat {
				webActiveResponse.IsSat = false
			}
		} else if dayName == "SUNDAY" {
			webActiveResponse.IsSun = true
			if webActiveResponse.UserCollectedSun {
				webActiveResponse.IsSun = false
			}
		}
	}
	// [วันที่ไม่ได้รับ]
	if !webActiveResponse.UserCollectedSun && currentModelDay > model.ACTIVITY_DAY_SUNDAY && !webActiveResponse.IsSun {
		webActiveResponse.IsSkipSun = true
	}
	if !webActiveResponse.UserCollectedSat && currentModelDay > model.ACTIVITY_DAY_SATURDAY && !webActiveResponse.IsSat {
		webActiveResponse.IsSkipSat = true
	}
	if !webActiveResponse.UserCollectedFri && currentModelDay > model.ACTIVITY_DAY_FRIDAY && !webActiveResponse.IsFri {
		webActiveResponse.IsSkipFri = true
	}
	if !webActiveResponse.UserCollectedThu && currentModelDay > model.ACTIVITY_DAY_THURSDAY && !webActiveResponse.IsThu {
		webActiveResponse.IsSkipThu = true
	}
	if !webActiveResponse.UserCollectedWed && currentModelDay > model.ACTIVITY_DAY_WEDNESDAY && !webActiveResponse.IsWed {
		webActiveResponse.IsSkipWed = true
	}
	if !webActiveResponse.UserCollectedTue && currentModelDay > model.ACTIVITY_DAY_TUESDAY && !webActiveResponse.IsTue {
		webActiveResponse.IsSkipTue = true
	}
	if !webActiveResponse.UserCollectedMon && currentModelDay > model.ACTIVITY_DAY_MONDAY && !webActiveResponse.IsMon {
		webActiveResponse.IsSkipMon = true
	}

	return &webActiveResponse, nil
}

func (s activityDailyService) getTodayDay() (string, int64, string, time.Time, error) {

	var currentDayToModelDay int64
	// [ต้องบวกให้หน้าบ้าน]
	playAt := time.Now().UTC().Add(7 * time.Hour)
	playDate := playAt.Format("2006-01-02")
	playDayName := playAt.Weekday().String()
	dayUpper := strings.ToUpper(playDayName)
	if dayUpper == "MONDAY" {
		currentDayToModelDay = model.ACTIVITY_DAY_MONDAY
	} else if dayUpper == "TUESDAY" {
		currentDayToModelDay = model.ACTIVITY_DAY_TUESDAY
	} else if dayUpper == "WEDNESDAY" {
		currentDayToModelDay = model.ACTIVITY_DAY_WEDNESDAY
	} else if dayUpper == "THURSDAY" {
		currentDayToModelDay = model.ACTIVITY_DAY_THURSDAY
	} else if dayUpper == "FRIDAY" {
		currentDayToModelDay = model.ACTIVITY_DAY_FRIDAY
	} else if dayUpper == "SATURDAY" {
		currentDayToModelDay = model.ACTIVITY_DAY_SATURDAY
	} else if dayUpper == "SUNDAY" {
		currentDayToModelDay = model.ACTIVITY_DAY_SUNDAY
	}

	return dayUpper, currentDayToModelDay, playDate, playAt, nil
}

func (s activityDailyService) CreateUserColletedActivityDaily(req model.CreateActivityDailyUserRequest) (int64, error) {

	actionAt := time.Now()
	var createBody model.CreateActivityDailyUserBody
	createBody.UserId = req.UserId
	_, currentModelDay, playDate, playDateTime, _ := s.getTodayDay()
	createBody.ActivityDayId = currentModelDay

	activityDaily, err := s.repo.GetFirstActivityDaily()
	if err != nil {
		return 0, err
	}

	if activityDaily.ActivityDailyStatusId == model.ACTIVITY_DAILY_STATUS_DEACTIVE {
		return 0, badRequest("ยังไม่เปิดใช้งาน")
	}
	// Bug fix ถ้าไม่ใช่สมาชิก จะไม่สามารถรับรางวัลได้
	user, err := s.repo.GetUserById(req.UserId)
	if err != nil {
		return 0, err
	}
	if user.MemberCode == nil {
		return 0, badRequest("คุณไม่ใช่สมาชิก")
	}
	if user.UserStatusId == model.USER_STATUS_DEACTIVE {
		return 0, badRequest("คุณไม่ใช่สมาชิก")
	}

	// // ถ้าวันนี้กดรับ ไปแล้ว จะไม่สามารถกดรับได้
	var reqCheck model.CheckDuplicateActivityDailyRequest
	reqCheck.UserId = req.UserId
	reqCheck.CollectedDateCheck = playDate
	reqCheck.DayId = currentModelDay
	checkUserAlreadyCollected, _ := s.repo.CheckDuplicateActivityDaily(reqCheck)
	if checkUserAlreadyCollected != nil {
		return 0, badRequest("วันนี้คุณได้รับรางวัลไปแล้ว")
	}

	// check เงือนไข ว่าผ่านไหม
	var checkPassConditionUser model.CheckConditionUserRequest
	checkPassConditionUser.UserId = req.UserId
	checkPassConditionUser.ConditionId = activityDaily.ActivityDailyConditionId
	checkPassConditionUser.PlayDate = playDate
	checkPassConditionUser.DepositAmountCondition = activityDaily.DepositAmountCondition
	ConditionUser, err := s.CheckConditionUserColletedActivityDaily(checkPassConditionUser)
	if err != nil {
		return 0, err
	}
	if !ConditionUser {
		return 0, badRequest("คุณไม่ผ่านเงื่อนไข")
	}

	currentWeekCollected, err := s.repo.GetActivityDailyUserOnCurrentWeek(req.UserId)
	if err != nil {
		return 0, err
	}
	if len(currentWeekCollected) >= 7 || currentModelDay == model.ACTIVITY_DAY_COMPLETE_DAILY {
		return 0, badRequest("คุณได้รับรางวัลครบแล้ว")
	} else {
		// req.CollectedBonus get from that day
		if currentModelDay == model.ACTIVITY_DAY_MONDAY {
			createBody.CollectedBonus = activityDaily.MondayBonus
		} else if currentModelDay == model.ACTIVITY_DAY_TUESDAY {
			createBody.CollectedBonus = activityDaily.TuesdayBonus
		} else if currentModelDay == model.ACTIVITY_DAY_WEDNESDAY {
			createBody.CollectedBonus = activityDaily.WednesdayBonus
		} else if currentModelDay == model.ACTIVITY_DAY_THURSDAY {
			createBody.CollectedBonus = activityDaily.ThursdayBonus
		} else if currentModelDay == model.ACTIVITY_DAY_FRIDAY {
			createBody.CollectedBonus = activityDaily.FridayBonus
		} else if currentModelDay == model.ACTIVITY_DAY_SATURDAY {
			createBody.CollectedBonus = activityDaily.SaturdayBonus
		} else if currentModelDay == model.ACTIVITY_DAY_SUNDAY {
			createBody.CollectedBonus = activityDaily.SundayBonus
		}
	}
	createBody.CollectedDate = playDateTime
	// Unique key per user and day
	Uniquekey := fmt.Sprintf("%v%v", user.Id, playDateTime.Format("20060102"))
	createBody.DailyKey = Uniquekey
	//find the dailyKey double check
	checkConfirm, _ := s.repo.CheckConfirmActivityDailyByDailyKey(Uniquekey)
	if checkConfirm != nil {
		return 0, badRequest("คุณได้รับรางวัลครบแล้ว")
	}

	// create activity daily user
	createBody.ActivityDailyConditionId = activityDaily.ActivityDailyConditionId
	checkTurn, err := GetTurnoverSetting(repository.NewTurnoverRepository(s.shareDb))
	if err != nil {
		return 0, err
	}
	createBody.TidturnPercent = checkTurn.TidturnActivityDailyPercent
	if activityDaily.ActivityDailyConditionId == model.NONE {
		createBody.AmountCondition = 0
	} else {
		createBody.AmountCondition = activityDaily.DepositAmountCondition
	}
	insertId, err := s.repo.CreateActivityDailyUser(createBody)
	if err != nil {
		return 0, err
	}

	// [20231218] เปิดใช้งาน ต้อง Double check ว่า วันนี้กดรับไปแล้วหรือยัง
	if insertId != 0 && checkConfirm == nil {
		// [USER_CREDIT] Just only go to user credit
		var userCreditReq model.UserTransactionCreateRequest
		userCreditReq.RefId = &insertId
		userCreditReq.UserId = createBody.UserId
		userCreditReq.TypeId = model.CREDIT_TYPE_DAILY_ACTIVITY_BONUS // เพิ่ม type
		userCreditReq.BonusAmount = createBody.CollectedBonus
		userCreditReq.Detail = "โบนัสกิจกรรมรายวัน"
		userCreditReq.StartWorkAt = actionAt // เริ่มนับตอนกดยินยัน
		_, err = s.repo.IncreaseUserCredit(userCreditReq)
		if err != nil {
			return 0, err
		} else {
			if err := s.CreateTurnOverFromActivityDaily(req.UserId, createBody.CollectedBonus, insertId, currentModelDay); err != nil {
				return 0, err
			}
		}
	}

	return insertId, nil
}

func (s activityDailyService) CheckConditionUserColletedActivityDaily(req model.CheckConditionUserRequest) (bool, error) {

	// true mean pass the condition
	var result bool

	switch req.ConditionId {
	case model.MINIMUM_DEPOSIT:
		checkMinDeposit, err := s.CheckConditionMinimumCreditAmount(req)
		if err != nil {
			return false, err
		}
		result = checkMinDeposit
		return result, nil

	case model.DEPOSIT_ACCUMULATED_AMOUNT:
		checkMinDeposit, err := s.CheckConditionAccumulatedCreditAmount(req)
		if err != nil {
			return false, err
		}
		result = checkMinDeposit
		return result, nil

	default:
		//NONE
		result = true
		return result, nil

	}
}

func (s activityDailyService) CheckConditionAccumulatedCreditAmount(req model.CheckConditionUserRequest) (bool, error) {

	var reqTransaction model.CheckDailyActivityUserTransactionListRequest
	reqTransaction.UserId = &req.UserId
	reqTransaction.FromDate = req.PlayDate
	reqTransaction.ToDate = req.PlayDate

	transaction, err := s.repo.CheckDailyActivityUserTransactionList(reqTransaction)
	if err != nil {
		return false, err
	}

	var totalCredit float64
	for _, v := range transaction {
		if v.TypeId == model.CREDIT_TYPE_DEPOSIT {
			totalCredit += v.CreditAmount
		}
	}

	if totalCredit >= req.DepositAmountCondition {
		return true, nil
	}

	return false, nil
}

func (s activityDailyService) CheckConditionMinimumCreditAmount(req model.CheckConditionUserRequest) (bool, error) {

	var reqTransaction model.CheckDailyActivityUserTransactionListRequest
	reqTransaction.UserId = &req.UserId
	reqTransaction.FromDate = req.PlayDate
	reqTransaction.ToDate = req.PlayDate
	transaction, err := s.repo.CheckDailyActivityUserTransactionList(reqTransaction)
	if err != nil {
		return false, err
	}

	for _, v := range transaction {
		if v.TypeId == model.CREDIT_TYPE_DEPOSIT {
			if req.DepositAmountCondition <= v.CreditAmount {
				return true, nil
			}
		}
	}
	return false, nil
}

func (s activityDailyService) GetSevenDayCollectedBouns(userId int64) (*model.GetSevenDayCollectedBounsResponse, error) {

	var res model.GetSevenDayCollectedBounsResponse

	activityDaily, err := s.repo.GetFirstActivityDaily()
	if err != nil {
		return nil, err
	}

	if activityDaily.ActivityDailyStatusId == model.ACTIVITY_DAILY_STATUS_DEACTIVE {
		return nil, badRequest("ยังไม่เปิดใช้งาน")
	}

	currentWeekCollected, err := s.repo.GetActivityDailyUserOnCurrentWeek(userId)
	if err != nil {
		return nil, err
	}

	if len(currentWeekCollected) == 7 {
		res.CompletedBonusAvilable = true
	} else {
		res.CompletedBonusAvilable = false
	}
	res.CompletedBonus = activityDaily.CompletedBonus

	completeDaily := false
	for _, v := range currentWeekCollected {
		if v.ActivityDayId == model.ACTIVITY_DAY_COMPLETE_DAILY {
			completeDaily = true
			break
		}
	}

	if model.NONE == activityDaily.ActivityDailyConditionId {
		res.DepositAmountCondition = 0
	} else {
		res.DepositAmountCondition = activityDaily.DepositAmountCondition
	}
	res.ActivityDailyConditionId = activityDaily.ActivityDailyConditionId
	res.ActivityDailyConditionTh = activityDaily.ActivityDailyConditionTh
	res.CompletedCollected = completeDaily

	return &res, nil

}

func (s activityDailyService) CreateSevenDayCollectedBouns(req model.CreateActivityDailyUserRequest) error {

	actionAt := time.Now()
	var createBody model.CreateActivityDailyUserBody
	createBody.UserId = req.UserId
	_, _, playDate, playDateTime, _ := s.getTodayDay()

	activityDaily, err := s.repo.GetFirstActivityDaily()
	if err != nil {
		return err
	}

	if activityDaily.ActivityDailyStatusId == model.ACTIVITY_DAILY_STATUS_DEACTIVE {
		return badRequest("ยังไม่เปิดใช้งาน")
	}
	// Bug fix ถ้าไม่ใช่สมาชิก จะไม่สามารถรับรางวัลได้
	user, err := s.repo.GetUserById(req.UserId)
	if err != nil {
		return err
	}
	if user.MemberCode == nil {
		return badRequest("คุณไม่ใช่สมาชิก")
	}
	if user.UserStatusId == model.USER_STATUS_DEACTIVE {
		return badRequest("คุณไม่ใช่สมาชิก")
	}
	// [20231231] ถ้าครบ 7 วันไม่ค้องเช็คผ่านเงื่อนไข (P.Mink)
	// var checkPassConditionUser model.CheckConditionUserRequest
	// checkPassConditionUser.UserId = req.UserId
	// checkPassConditionUser.ConditionId = activityDaily.ActivityDailyConditionId
	// checkPassConditionUser.PlayDate = playDate
	// checkPassConditionUser.DepositAmountCondition = activityDaily.DepositAmountCondition
	// ConditionUser, err := s.CheckConditionUserColletedActivityDaily(checkPassConditionUser)
	// if err != nil {
	// 	return err
	// }
	// if !ConditionUser {
	// 	return badRequest("คุณไม่ผ่านเงื่อนไข")
	// }
	// check ว่าต้องกดรับมาก่อน ถึงจะกดรับได้
	sevenDayCollectedBouns, err := s.GetSevenDayCollectedBouns(req.UserId)
	if err != nil {
		return err
	}
	if !sevenDayCollectedBouns.CompletedBonusAvilable {
		return badRequest("คุณยังไม่สามารถรับรางวัลได้")
	}
	if sevenDayCollectedBouns.CompletedCollected {
		return badRequest("คุณได้รับรางวัลครบแล้ว")
	}

	// check ว่ารับครบเกิน 7 วันแล้วหรือยัง
	currentWeekCollected, err := s.repo.GetActivityDailyUserOnCurrentWeek(req.UserId)
	if err != nil {
		return err
	}
	for _, v := range currentWeekCollected {
		if v.ActivityDayId == model.ACTIVITY_DAY_COMPLETE_DAILY {
			return badRequest("คุณได้รับรางวัลครบแล้ว")
		}
	}
	if len(currentWeekCollected) > 7 {
		return badRequest("คุณได้รับรางวัลครบแล้ว")
	} else {
		createBody.CollectedBonus = activityDaily.CompletedBonus
		createBody.CollectedDate = playDateTime
		createBody.ActivityDayId = model.ACTIVITY_DAY_COMPLETE_DAILY
	}

	dailyDate := playDate
	dailyKey := fmt.Sprintf("ALL%v%v", user.Id, strings.Replace(dailyDate, "-", "", -1))
	createBody.DailyKey = dailyKey
	checkConfirm, _ := s.repo.CheckConfirmActivityDailyByDailyKey(dailyKey)
	if checkConfirm != nil {
		return badRequest("คุณได้รับรางวัลครบแล้ว")
	}

	// activity_daily_condition_id
	createBody.ActivityDailyConditionId = activityDaily.ActivityDailyConditionId
	checkTurn, err := GetTurnoverSetting(repository.NewTurnoverRepository(s.shareDb))
	if err != nil {
		return err
	}
	createBody.TidturnPercent = checkTurn.TidturnActivityDailyPercent
	if activityDaily.ActivityDailyConditionId == model.NONE {
		createBody.AmountCondition = 0
	} else {
		createBody.AmountCondition = activityDaily.DepositAmountCondition
	}
	insertId, err := s.repo.CreateActivityDailyUser(createBody)
	if err != nil {
		return err
	}

	// [20231218] เปิดใช้งาน ต้อง Double check ว่า วันนี้กดรับไปแล้วหรือยัง
	// [USER_CREDIT] Just only go to user credit
	if insertId != 0 && checkConfirm == nil {
		var userCreditReq model.UserTransactionCreateRequest
		userCreditReq.RefId = &insertId
		userCreditReq.UserId = createBody.UserId
		userCreditReq.TypeId = model.CREDIT_TYPE_DAILY_ACTIVITY_BONUS // เพิ่ม type
		userCreditReq.BonusAmount = createBody.CollectedBonus
		userCreditReq.StartWorkAt = actionAt // เริ่มนับตอนกดยินยัน
		userCreditReq.Detail = "โบนัสกิจกรรมรายวัน"
		_, err := s.repo.IncreaseUserCredit(userCreditReq)
		if err != nil {
			return err
		} else {
			if err := s.CreateTurnOverFromActivityDaily(req.UserId, createBody.CollectedBonus, insertId, 8); err != nil {
				return err
			}
		}
	}

	return nil
}

func (s *activityDailyService) CreateTurnOverFromActivityDaily(userId int64, bonusAmount float64, refId int64, day int64) error {

	// serviceTurnover
	checkTurn, err := GetTurnoverSetting(repository.NewTurnoverRepository(s.shareDb))
	if err != nil {
		return nil
	}
	if checkTurn.TidturnActivityDailyPercent > 0 {

		tidTurn := (bonusAmount * float64(checkTurn.TidturnActivityDailyPercent) / 100)

		tidTurn = math.Ceil(tidTurn)

		createdTime := time.Now().UTC()
		var createBody model.TurnoverUserStatementCreateBody
		createBody.UserId = userId
		createBody.RefTypeId = refId
		createBody.TypeId = model.TURN_BONUS_ACTIVITY_DAILY
		createBody.Name = model.TURNOVER_CATE_ACTIVITY_DAILY
		if day == 8 {
			createBody.PromotionName = "รายได้กิจกรรมรายวัน โบนัสครบ 7 วัน"
		} else {
			createBody.PromotionName = fmt.Sprintf("รายได้กิจกรรมรายวัน รับวันที่: %d", day)
		}
		createBody.BonusAmount = bonusAmount
		createBody.StatusId = model.TURNOVER_STATEMENT_STATUS_PENDING
		createBody.StartTurnAmount = tidTurn
		createBody.StartTurnAt = &createdTime
		createBody.TotalTurnAmount = tidTurn
		if _, err := s.repo.CreateTurnoverUserStatement(createBody); err != nil {
			return err
		}
	} else {
		createdTime := time.Now().UTC()
		var createBody model.TurnoverUserStatementCreateBody
		createBody.UserId = userId
		createBody.RefTypeId = refId
		createBody.TypeId = model.TURN_BONUS_ACTIVITY_DAILY
		createBody.Name = model.TURNOVER_CATE_ACTIVITY_DAILY
		createBody.PromotionName = fmt.Sprintf("รายได้กิจกรรมรายวัน รับวันที่: %d", day)
		createBody.BonusAmount = 0
		createBody.StatusId = model.TURNOVER_STATEMENT_STATUS_PENDING
		createBody.StartTurnAmount = 0
		createBody.StartTurnAt = &createdTime
		createBody.TotalTurnAmount = 0
		craeteTurnId, err := s.repo.CreateTurnoverUserStatement(createBody)
		if err != nil {
			return err
		}

		var updateTurnoverUserStatement model.TurnoverUserStatementUpdateBody
		setTotalTurnAmount := 0.0
		setTimeTurnAt := time.Now().UTC()
		updateTurnoverUserStatement.StatusId = model.TURNOVER_STATEMENT_STATUS_COMPLETED
		updateTurnoverUserStatement.TotalTurnAmount = &setTotalTurnAmount
		updateTurnoverUserStatement.EndTurnAt = &setTimeTurnAt
		if err := s.repo.UpdateTurnoverUserStatement(*craeteTurnId, updateTurnoverUserStatement); err != nil {
			log.Println("UpdateTurnoverUserStatement", err)
		}

		// create turnover withdraw log
		var createTurnoverWithDrawLog model.CreateTurnoverUserWithdrawLog
		createTurnoverWithDrawLog.UserId = userId
		createTurnoverWithDrawLog.LogKey = fmt.Sprintf("BONUS_ACTIVITY_DAILY_U%d_D%s", userId, time.Now().UTC().Format("20060102150405"))
		createTurnoverWithDrawLog.TotalWithdrawPrice = 0
		createTurnoverWithDrawLog.CurrentTurn = 0
		createTurnoverWithDrawLog.PlayTotal = 0
		createTurnoverWithDrawLog.LastPlayY = 0
		createTurnoverWithDrawLog.LastTotalX = 0
		if _, err = s.repo.CreateTurnoverUserWithdrawLog(createTurnoverWithDrawLog); err != nil {
			log.Println("CreateTurnOverFromActivityDaily.CreateTurnoverUserWithdrawLog", err)
		}

	}

	return nil
}

func (s *activityDailyService) GetTurnoverUserActivityDaily(req model.GetTurnoverUserActivityDailyRequest) (*model.SuccessWithPagination, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	turnoverUserActivityDaily, total, err := s.repo.GetTurnoverUserActivityDaily(req)
	if err != nil {
		return nil, err
	}

	var successWithPagination model.SuccessWithPagination
	successWithPagination.List = turnoverUserActivityDaily
	successWithPagination.Total = total
	successWithPagination.Message = "success"

	return &successWithPagination, nil
}
