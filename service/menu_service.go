package service

import (
	"cybergame-api/model"
	"cybergame-api/repository"
	"strings"
)

type MenuService interface {
	GetMenu(adminId int64) ([]model.Menu, error)
}

type menuService struct {
	PermRepo repository.PermissionRepository
}

func NewMenuService(
	PermRepo repository.PermissionRepository,
) MenuService {
	return &menuService{PermRepo}
}

func (s *menuService) GetMenu(adminId int64) ([]model.Menu, error) {

	perms, adminPers, err := s.PermRepo.GetPermissions(adminId)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(recordNotFound)
		}
		return nil, internalServerError(err)
	}

	var menu []model.Menu

	// ENV show Tier Setting
	useSpecielSetting := false
	spSetting, err := s.PermRepo.GetTierSetting("use_speciel_setting")
	if err == nil && spSetting.FromValue == 1 {
		useSpecielSetting = true
		menu = append(menu, model.Menu{
			Id:      -1,
			Title:   "use_speciel_setting",
			Name:    "use_speciel_setting",
			List:    nil,
			Managed: false,
		})
	}

	for _, per := range perms {
		if per.PermissionKey == "speciel_setting" && !useSpecielSetting {
			continue
		}
		subMenu := []model.SubMenu{}
		for _, subPer := range perms {
			if !subPer.Main {
				count := len(strings.Split(subPer.PermissionKey, "_"))
				if per.PermissionKey == strings.Join(strings.Split(subPer.PermissionKey, "_")[:count-1], "_") {
					subMenu = append(subMenu, model.SubMenu{
						Id:    subPer.Id,
						Title: subPer.Name,
						Name:  subPer.PermissionKey,
					})
				}
			}
		}
		if per.Main {
			menu = append(menu, model.Menu{
				Id:      per.Id,
				Title:   per.Name,
				Name:    per.PermissionKey,
				List:    &subMenu,
				Managed: checkRead(*per, adminPers),
			})
		}
	}

	return menu, nil
}

func checkRead(per model.Permission, adminPers *[]model.AdminPermission) bool {

	read := false

	for _, adminPer := range *adminPers {
		if adminPer.PermissionId == per.Id {
			read = true
		}
	}

	return read
}
