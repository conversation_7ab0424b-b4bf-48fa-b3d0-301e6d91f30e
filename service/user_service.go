package service

import (
	"bytes"
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"errors"
	"fmt"
	"log"
	"math"
	"net/http"
	"os"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/tealeg/xlsx"
	"gorm.io/gorm"
)

type UserService interface {
	// backoffice
	GetUserLoginLogs(req model.GetUserLogListRequest) (*model.SuccessWithPagination, error)
	GetUser(id int64) (*model.UserDetail, error)
	GetUserDetail(id int64) (*model.UserDetailResponse, error)
	GetUserList(query model.UserListQuery) (*model.SuccessWithPagination, error)
	GetUpdateLogs(query model.UserUpdateQuery) (*model.SuccessWithPagination, error)
	CreateUser(body model.UserCreateRequest) error
	UpdateUser(userId int64, body model.UpdateUserRequest, adminName string, adminId int64) error
	DeleteUser(id int64, adminId int64) error
	GetMemberRefreshCredit(id int64) (*model.UserRefreshCreditResponse, error)
	UserListQueryForExcel(c *gin.Context, query model.UserListQueryForExcel) error
	GetUserDespositRankOptions() ([]model.SelectOptions, error)
	GetUserTurnOverRankOptions() ([]model.SelectOptions, error)

	// frontend
	GetMe(id int64, ip string) (*model.UserMe, error)
	SendOtpRegister(body model.UserSendOtpBody) (*model.UserOtpResponse, error)
	VerifyOtpRegister(body model.UserVerifyOtpBody) error
	RacingSendOtpForget() (*int64, error)
	SendOtpForget(body model.UserSendOtpBody) (*model.UserOtpResponse, error)
	ResetPassword(userId int64, body model.UserUpdatePassword, adminId int64) error
	UserRegister(user model.UserRegister) (*model.UserVerifyOtpResponse, error)
	Login(body model.UserLogin) (*model.UserLoginResponse, error)
	GetGameToken(userId int64, ip string) (*model.UserLoginResponse, error)
	FrontResetPassword(userId int64, body model.UserUpdatePasswordForFront) error
	GetBankList() (*model.UserBankListResponse, error)
	GenUniqueUserMemberCode(userId int64) (*string, error)
	GetRegisterSetting() (*model.ConfigurationUserRegisterResponse, error)
	CheckNewUserPhone(phone string) (*model.UserRegisterCheckResponse, error)
	// UserLine
	UserLineLogin(body model.UserLineLoginRequest) (*model.UserLoginResponse, error)
	UserLineRegister(body model.UserLineRegisterRequest) (*model.UserVerifyOtpResponse, error)
	// REF-WebConfing
	GetWebConfiguration() (*model.ConfigurationResponse, error)

	// InactiveUser
	GetInactiveUserList(query model.InactiveUserListRequest) ([]model.InactiveUserResponse, int64, error)
	RemoveInactiveUser(query model.InactiveUserRemoveListRequest) error
	ExportInactiveUserXlsx(c *gin.Context, query model.InactiveUserListForExcelRequest) error

	// AGENT
	// TestDecreaseFastBankCredit() error

	GenMemberCodeAndAffilate(userId int64) (*string, error)
	GenMissingUserAffilate(userId int64) (*model.CheckMissingAffiliateReponse, error)

	// USER WITHDRAW SETTING
	UserWithdrawSettingCreate(req model.UserWithdrawSettingCreateRequest, adminId int64) (int64, error)
	UserWithdrawSettingUpdateByUserId(req model.UserWithdrawSettingUpdateByUserIdRequest, adminId int64) error
	UserWithdrawSettingGetById(req model.UserWithdrawSettingGetByRequest) (*model.UserWithdrawSettingGetByUserIdResponse, error)
	UserWithdrawSettingGetByUserId(req model.UserWithdrawSettingGetByUserIdRequest) (*model.UserWithdrawSettingGetByUserIdResponse, error)
	// REPORT - graph
	GetUserChannelSummaryGraph(req model.UserChannelSummaryGraphRequest) (*model.UserChannelSummaryGraphResponse, error)
	// [ADMIN_LOG]
	LogAdmin(name string, adminId int64, req interface{}) error
	EncodeData(refby int64) string
	DecodeData(refby string) int64

	// RAW PLAYLOG
	GetAgentCallbackPlayLogList(req model.AgentCallbackPlayLogListRequest) (interface{}, error)
}

type userService struct {
	repo         repository.UserRepository
	shareDb      *gorm.DB
	perRepo      repository.PermissionRepository
	groupRepo    repository.GroupRepository
	otpRepo      repository.OtpRepository
	agentInfo    repository.AgentInfoRepository
	recomendRepo repository.RecommendRepository
	afRepo       repository.AffiliateRepository
	notiLine     NotificationService
	adminAction  AdminActionService
	// promotionWebService PromotionWebService
}

func NewUserService(
	repo repository.UserRepository,
	shareDb *gorm.DB,
	perRepo repository.PermissionRepository,
	groupRepo repository.GroupRepository,
	otpRepo repository.OtpRepository,
	agentInfo repository.AgentInfoRepository,
	recomendRepo repository.RecommendRepository,
	afRepo repository.AffiliateRepository,
	notiLine NotificationService,
	adminAction AdminActionService,
	// promotionWebService PromotionWebService,
) UserService {
	return &userService{repo, shareDb, perRepo, groupRepo, otpRepo, agentInfo, recomendRepo, afRepo, notiLine, adminAction}
}

func (s *userService) EncodeData(refby int64) string {

	return helper.EncodeData(refby)
}

func (s *userService) DecodeData(refby string) int64 {

	return helper.DecodeData(refby)
}

func (s *userService) GetUserDespositRankOptions() ([]model.SelectOptions, error) {

	var query model.UserTierSettingListRequest
	query.Type = model.USERTIER_TYPE_DEPOSIT

	list, _, err := s.repo.GetUserTierSettingList(query)
	if err != nil {
		return nil, internalServerError(err)
	}

	var options []model.SelectOptions
	for _, v := range list {
		options = append(options, model.SelectOptions{
			Id:    v.Id,
			Value: fmt.Sprintf("%d", v.Id),
			Label: v.Name,
		})
	}
	return options, nil
}

func (s *userService) GetUserTurnOverRankOptions() ([]model.SelectOptions, error) {

	var query model.UserTierSettingListRequest
	query.Type = model.USERTIER_TYPE_TURNOVER

	list, _, err := s.repo.GetUserTierSettingList(query)
	if err != nil {
		return nil, internalServerError(err)
	}

	var options []model.SelectOptions
	for _, v := range list {
		options = append(options, model.SelectOptions{
			Id:    v.Id,
			Value: fmt.Sprintf("%d", v.Id),
			Label: v.Name,
		})
	}
	return options, nil
}

func (s *userService) GetUserLoginLogs(req model.GetUserLogListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.Pagination(&req.Page, &req.Limit); err != nil {
		return nil, err
	}

	logs, total, err := s.repo.GetUserLoginLogs(req)
	if err != nil {
		return nil, err
	}

	var list model.SuccessWithPagination
	list.Message = "Success"
	list.List = logs
	list.Total = total

	return &list, nil
}

func (s *userService) GetPlaylogSummaryByUserId(userId int64) (*model.UserPlaylogSummaryResponse, error) {

	result, err := s.repo.GetUserPlaylogSummaryByUserId(userId)
	if err != nil {
		if err.Error() == gorm.ErrRecordNotFound.Error() {
			// CRETE NEW
			totalPlaylog, err := s.repo.GetTotalPlaylogSummaryByUserId(userId)
			if err != nil {
				log.Println("GetTotalPlaylogSummaryByUserId.ERROR", err)
				return nil, err
			}
			createBody := model.UserPlaylogSummaryCreateBody{
				UserId:           userId,
				PlaylogStartDate: totalPlaylog.PlaylogStartDate,
				TotalTurn:        totalPlaylog.TotalTurn,
				TotalWinLoss:     totalPlaylog.TotalWinLoss,
				UpdatedAt:        time.Now(),
			}
			if _, err := s.repo.CreateUserPlaylogSummary(createBody); err != nil {
				return nil, err
			}
			// REGET
			result2, err := s.repo.GetUserPlaylogSummaryByUserId(userId)
			if err != nil {
				return nil, err
			}
			return result2, nil
		}
		return nil, err
	}
	return result, nil
}

func (s *userService) GetUserDetail(id int64) (*model.UserDetailResponse, error) {

	var result model.UserDetailResponse

	user, err := GetUser(s.repo, id)
	if err != nil {
		return nil, err
	}

	// Clone for External Use
	result.Id = user.Id
	result.MemberCode = user.MemberCode
	result.Username = user.Username
	result.Phone = user.Phone
	result.Fullname = user.Fullname
	result.BankName = user.BankName
	result.BankAccount = user.BankAccount
	result.BankId = user.BankId
	result.GameToken = user.GameToken
	result.ChannelId = user.ChannelId
	result.Channel = user.Channel
	result.TrueWallet = user.TrueWallet
	result.Contact = user.Contact
	result.Note = user.Note
	result.Course = user.Course
	result.Type = user.Type
	result.Credit = user.Credit
	result.Ref = user.Ref
	result.RefCode = user.RefCode
	result.Encrypt = user.Encrypt
	result.IpRegistered = user.IpRegistered

	// Append WinLose from Playlog
	playlogSummary, err := s.GetPlaylogSummaryByUserId(user.Id)
	if err != nil {
		return nil, err
	}
	// Update if UpdatedAt is over 1 hour
	if playlogSummary.UpdatedAt == nil || playlogSummary.UpdatedAt.Add(time.Hour).Unix() < time.Now().Unix() {
		totalPlaylog, err := s.repo.GetTotalPlaylogSummaryByUserId(user.Id)
		if err != nil {
			log.Println("GetTotalPlaylogSummaryByUserId.ERROR", err)
			return &result, nil
		}
		var updateBody model.UserPlaylogSummaryUpdateBody
		updateBody.TotalTurn = &totalPlaylog.TotalTurn
		updateBody.TotalWinLoss = &totalPlaylog.TotalWinLoss
		if err := s.repo.UpdateUserPlaylogSummary(playlogSummary.Id, updateBody); err != nil {
			log.Println("UpdateUserPlaylogSummaryTotal.ERROR", err)
		}
		playlogSummary.TotalTurn = totalPlaylog.TotalTurn
		playlogSummary.TotalWinLoss = totalPlaylog.TotalWinLoss
	} else {
		result.TotalTurn = playlogSummary.TotalTurn
		result.TotalWinLoss = playlogSummary.TotalWinLoss
	}
	return &result, nil
}

func (s *userService) GetUser(id int64) (*model.UserDetail, error) {

	user, err := GetUser(s.repo, id)
	if err != nil {
		return nil, err
	}
	return user, nil
}

func GetUser(repo repository.UserRepository, id int64) (*model.UserDetail, error) {

	user, err := repo.GetUser(id)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(userNotFound)
		}
		return nil, err
	}
	user.Ref = helper.EncodeData(user.Id)

	// Custom RefCode
	if userInfo, err := repo.GetUserAllianceInfoByUserId(user.Id); err == nil {
		user.RefCode = userInfo.RefCode
	}

	if user.MemberCode != "" {
		agentName := os.Getenv("AGENT_NAME")
		agentProvider := os.Getenv("AGENT_PROVIDER")
		if agentProvider == "AMB" {
			agentData := model.AmbBalance{}
			agentData.Username = user.MemberCode
			agent, err := repo.AmbGetCredit(agentData)
			if err != nil {
				return nil, err
			}
			if agent != nil {
				user.Credit = agent.Data.Balance
				if err := repo.UserUpdateCredit(id, agent.Data.Balance); err != nil {
					return nil, nil
				}
			}
		} else {
			agentData := model.AgcBalance{}
			agentData.Agentname = agentName
			agentData.PlayerName = user.MemberCode
			agentData.Timestamp = int64(time.Now().Unix())
			agentData.Sign = helper.CreateSign(os.Getenv("AGENT_KEY"), agentData.Agentname+user.MemberCode, time.Now())
			agent, err := repo.AgcGetCredit(agentData)
			if err != nil {
				return nil, err
			}
			if agent != nil {
				user.Credit = agent.Balance
				if err := repo.UserUpdateCredit(id, agent.Balance); err != nil {
					return nil, nil
				}
			}
		}
	}
	return user, nil
}

func (s *userService) GetMemberRefreshCredit(id int64) (*model.UserRefreshCreditResponse, error) {

	var response model.UserRefreshCreditResponse

	user, err := s.repo.GetUser(id)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(userNotFound)
		}

		return nil, err
	} else {
		if user.MemberCode != "" {
			agentName := os.Getenv("AGENT_NAME")
			agentProvider := os.Getenv("AGENT_PROVIDER")
			if agentProvider == "AMB" {
				agentData := model.AmbBalance{}
				agentData.Username = user.MemberCode
				agent, err := s.repo.AmbGetCredit(agentData)
				if err != nil {
					return nil, err
				}
				if agent != nil {
					user.Credit = agent.Data.Balance
					if err := s.repo.UserUpdateCredit(id, agent.Data.Balance); err != nil {
						return nil, nil
					}
					response.Id = user.Id
					response.Credit = agent.Data.Balance
					response.MemberCode = user.MemberCode
				} else {
					response.Id = user.Id
					response.Credit = user.Credit
					response.MemberCode = user.MemberCode
				}
			} else {
				agentData := model.AgcBalance{}
				agentData.Agentname = agentName
				agentData.PlayerName = user.MemberCode
				agentData.Timestamp = int64(time.Now().Unix())
				agentData.Sign = helper.CreateSign(os.Getenv("AGENT_KEY"), agentData.Agentname+user.MemberCode, time.Now())
				agent, err := s.repo.AgcGetCredit(agentData)
				if err != nil {
					return nil, err
				}
				if agent != nil {

					user.Credit = agent.Balance
					if err := s.repo.UserUpdateCredit(id, agent.Balance); err != nil {
						return nil, nil
					}
					response.Id = user.Id
					response.Credit = agent.Balance
					response.MemberCode = user.MemberCode
				} else {
					response.Id = user.Id
					response.Credit = user.Credit
					response.MemberCode = user.MemberCode
				}
			}
		} else {
			response.Id = user.Id
			response.Credit = user.Credit
			response.MemberCode = user.MemberCode
		}

	}

	return &response, nil
}

func (s *userService) GetUserList(query model.UserListQuery) (*model.SuccessWithPagination, error) {

	if err := helper.Pagination(&query.Page, &query.Limit); err != nil {
		return nil, err
	}

	list, total, err := s.repo.GetUserList(query)
	if err != nil {
		return nil, err
	}

	domain := os.Getenv("DOMAIN_NAME")
	frontUrl := os.Getenv("WEB_DOMAIN")
	if frontUrl != "" {
		domain = frontUrl
	}

	// REF_CODE
	allianceUserIds := []int64{}
	for i, v := range list {
		// FOR Alliance ONLY
		if v.UserTypeId == model.USER_TYPE_ALLIANCE {
			allianceUserIds = append(allianceUserIds, v.Id)
		}
		list[i].RefUrl = fmt.Sprintf("https://%s/register?ref=%s", domain, helper.EncodeData(v.Id))
	}

	if len(allianceUserIds) > 0 {
		var query model.UserAllianceListRequest
		query.UserIds = allianceUserIds
		allSettingList, _, err := s.repo.GetUserAllianceInfoList(query)
		if err != nil {
			log.Println("GetUserAllianceInfoList", err)
		}
		for i, v := range list {
			// FOR Alliance ONLY
			if v.UserTypeId == model.USER_TYPE_ALLIANCE {
				for _, v2 := range allSettingList {
					if v.Id == v2.UserId && v2.RefCode != "" {
						list[i].RefUrl = fmt.Sprintf("https://%s/register?refCode=%s", domain, v2.RefCode)
					}
				}
			}
		}
	}

	// UserWithdrawSettingGetListByUserId(userId []int64) ([]model.UserWithdrawSettingGetByUserIdResponse, error)
	// // AcountAmount float64 `json:"acountAmount"`
	// // MaximumTimePerDay float64 `json:"maximumTimePerDay"`

	var UserId []int64
	for _, v := range list {
		UserId = append(UserId, v.Id)
	}

	withdrawSetting, err := s.repo.UserWithdrawSettingGetListByUserId(UserId)
	if err != nil {
		return nil, err
	}

	pertime := "withdraw_maximum_time_per_day"
	getConfigpertime, err := s.repo.UserWithdrawConfigGetByKey(pertime)
	if err != nil {
		return nil, err
	}

	accumlated := "withdraw_accumulated_amount"
	getConfigaccumlated, err := s.repo.UserWithdrawConfigGetByKey(accumlated)
	if err != nil {
		return nil, err
	}

	for i, v := range list {
		found := false

		for _, v2 := range withdrawSetting {
			if v.Id == v2.UserId {
				list[i].AccumulatedAmount = v2.AccumulatedAmount
				list[i].MaximumTimePerDay = v2.MaximumTimePerDay
				found = true
				break
			}
		}

		if !found {
			list[i].AccumulatedAmount = getConfigaccumlated.ConfigValue
			list[i].MaximumTimePerDay = getConfigpertime.ConfigValue
		}
	}

	// UserTier Data
	if total != nil && *total > 0 {
		userTierData1, err1 := s.GetUserTierSettingByDepositList()
		userTierData2, err2 := s.GetUserTierSettingByTurnOverList()
		for i, v := range list {
			if err1 == nil && userTierData1.IsEnabled {
				for _, v2 := range userTierData1.List {
					if int64(v.TotalDepositAmount) >= v2.FromValue && int64(v.TotalDepositAmount) <= v2.ToValue {
						list[i].DepositRank = v2.Name
						break
					}
				}
			}
			if err2 == nil && userTierData2.IsEnabled {
				for _, v2 := range userTierData2.List {
					if int64(v.TotalTurnOverAmount) >= v2.FromValue && int64(v.TotalTurnOverAmount) <= v2.ToValue {
						list[i].TurnOverRank = v2.Name
						break
					}
				}
			}
		}
	}

	result := &model.SuccessWithPagination{
		Message: "Success",
		List:    list,
		Total:   *total,
	}
	return result, nil
}

func (s userService) GetUserTierSettingByDepositList() (*model.UserTierSettingResponse, error) {

	var result model.UserTierSettingResponse
	result.Type = model.USERTIER_TYPE_DEPOSIT

	// ENV enable Tier Setting
	tierSetting, err := s.repo.GetTierSetting("is_deposit_tier_setting")
	if err == nil && tierSetting.Id != 0 {
		if tierSetting.FromValue == 1 {
			result.IsEnabled = true
		} else {
			result.IsEnabled = false
		}
	} else {
		result.IsEnabled = false
	}

	var req model.UserTierSettingListRequest
	req.Type = model.USERTIER_TYPE_DEPOSIT
	if err := helper.Pagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	list, _, err := s.repo.GetUserTierSettingList(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	result.List = list
	return &result, nil
}

func (s userService) GetUserTierSettingByTurnOverList() (*model.UserTierSettingResponse, error) {

	var result model.UserTierSettingResponse
	result.Type = model.USERTIER_TYPE_TURNOVER

	// ENV enable Tier Setting
	tierSetting, err := s.repo.GetTierSetting("is_turnover_tier_setting")
	if err == nil && tierSetting.Id != 0 {
		if tierSetting.FromValue == 1 {
			result.IsEnabled = true
		} else {
			result.IsEnabled = false
		}
	} else {
		result.IsEnabled = false
	}

	var req model.UserTierSettingListRequest
	req.Type = model.USERTIER_TYPE_TURNOVER
	if err := helper.Pagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	list, _, err := s.repo.GetUserTierSettingList(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	result.List = list
	return &result, nil
}

func (s *userService) UserListQueryForExcel(c *gin.Context, query model.UserListQueryForExcel) error {

	list, _, err := s.repo.GetUserListForExcel(query)
	if err != nil {
		return err
	}
	var UserId []int64
	for _, v := range list {
		UserId = append(UserId, v.Id)
	}

	withdrawSetting, err := s.repo.UserWithdrawSettingGetListByUserId(UserId)
	if err != nil {
		return err
	}

	pertime := "withdraw_maximum_time_per_day"
	getConfigpertime, err := s.repo.UserWithdrawConfigGetByKey(pertime)
	if err != nil {
		return err
	}

	accumlated := "withdraw_accumulated_amount"
	getConfigaccumlated, err := s.repo.UserWithdrawConfigGetByKey(accumlated)
	if err != nil {
		return err
	}

	for i, v := range list {
		found := false

		for _, v2 := range withdrawSetting {
			if v.Id == v2.UserId {
				list[i].AccumulatedAmount = v2.AccumulatedAmount
				list[i].MaximumTimePerDay = v2.MaximumTimePerDay
				found = true
				break
			}
		}

		if !found {
			list[i].AccumulatedAmount = getConfigaccumlated.ConfigValue
			list[i].MaximumTimePerDay = getConfigpertime.ConfigValue
		}
	}

	file := xlsx.NewFile()
	sheet, err := file.AddSheet("sheet")
	if err != nil {
		return err
	}

	// ORDER BY Field name
	//ชุดข้อมูลที่ต้องการส่งออก (ทุกคอลัมน์)
	row1 := sheet.AddRow()
	row1.AddCell().Value = "Id"
	row1.AddCell().Value = "รหัสสมาชิก"
	row1.AddCell().Value = "ชื่อผู้ใช้/ชื่อ"
	row1.AddCell().Value = "ยอดคงเหลือ"
	row1.AddCell().Value = "รับโปรโมชั่น"
	row1.AddCell().Value = "ธนาคาร"
	row1.AddCell().Value = "ช่องทางที่รู้จัก"
	row1.AddCell().Value = "เวลาสมัคร/เวลาอัพเดท"
	row1.AddCell().Value = "IPสมัคร/IPล็อกอินล่าสุด"
	row1.AddCell().Value = "วัน-เวลาล็อกอินล่าสุด"
	row1.AddCell().Value = "ถอนออโต้สูงสุดต่อวัน"
	row1.AddCell().Value = "ถอนสูงสุดจำนวนครั้งต่อวัน"

	for _, v := range list {
		row2 := sheet.AddRow()
		row2.AddCell().Value = strconv.FormatInt(v.Id, 10)
		row2.AddCell().Value = v.MemberCode
		row2.AddCell().Value = fmt.Sprintf("%s %s", v.Phone, v.Fullname)
		row2.AddCell().Value = strconv.FormatFloat(v.Credit, 'f', 2, 64)
		row2.AddCell().Value = v.Note
		row2.AddCell().Value = fmt.Sprintf("%s %s", v.BankName, v.BankAccount)
		row2.AddCell().Value = fmt.Sprintf("%s %s", v.Channel, v.RefBy)
		row2.AddCell().Value = fmt.Sprintf("%s %s", v.CreatedAt.Format("2006-01-02 15:04:05"), v.UpdatedAt.Format("2006-01-02 15:04:05"))
		row2.AddCell().Value = fmt.Sprintf("%s %s", v.IpRegistered, v.Ip)
		row2.AddCell().Value = v.LogedinAt.Format("2006-01-02 15:04:05")
		row2.AddCell().Value = strconv.FormatFloat(v.AccumulatedAmount, 'f', 2, 64)
		row2.AddCell().Value = strconv.FormatFloat(v.MaximumTimePerDay, 'f', 2, 64)
	}

	var b bytes.Buffer
	if err := file.Write(&b); err != nil {
		return err
	}

	var downloadName string
	if query.UserCategory == "member" {
		downloadName = ("MemberList.xlsx")
	} else if query.UserCategory == "non-member" {
		downloadName = ("NonMemberList.xlsx")
	} else if query.UserCategory == "scammer" {
		downloadName = ("scammer.xlsx")
	}
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Disposition", "attachment; filename="+downloadName)
	c.Data(http.StatusOK, "application/octet-stream", b.Bytes())

	return nil
}

func (s *userService) GetMe(id int64, ip string) (*model.UserMe, error) {

	user, err := s.repo.FrontGetUser(id)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(userNotFound)
		}
		return nil, err
	}
	user.Ref = helper.EncodeData(user.Id)

	// Custom RefCode
	if userInfo, err := s.repo.GetUserAllianceInfoByUserId(user.Id); err == nil {
		user.RefCode = userInfo.RefCode
	}

	if user.MemberCode != "" {
		agentName := os.Getenv("AGENT_NAME")
		agentProvider := os.Getenv("AGENT_PROVIDER")
		if agentProvider == "AMB" {
			agentData := model.AmbBalance{}
			agentData.Username = user.MemberCode
			agent, err := s.repo.AmbGetCredit(agentData)
			if err != nil {
				return nil, err
			}
			if agent != nil {
				user.Credit = agent.Data.Balance
				if err := s.repo.UserUpdateCredit(id, agent.Data.Balance); err != nil {
					return nil, nil
				}
			}
		} else {
			agentData := model.AgcBalance{}
			agentData.Agentname = agentName
			agentData.PlayerName = user.MemberCode
			agentData.Timestamp = int64(time.Now().Unix())
			agentData.Sign = helper.CreateSign(os.Getenv("AGENT_KEY"), agentData.Agentname+user.MemberCode, time.Now())
			agent, err := s.repo.AgcGetCredit(agentData)
			if err != nil {
				return nil, err
			}
			if agent != nil {
				user.Credit = agent.Balance
				if err := s.repo.UserUpdateCredit(id, agent.Balance); err != nil {
					return nil, nil
				}
			}
		}
	}

	return user, nil
}

func (s *userService) GetUpdateLogs(query model.UserUpdateQuery) (*model.SuccessWithPagination, error) {

	if err := helper.Pagination(&query.Page, &query.Limit); err != nil {
		return nil, err
	}

	list, total, err := s.repo.GetUpdateLogs(query)
	if err != nil {
		return nil, err
	}

	result := &model.SuccessWithPagination{
		Message: "Success",
		List:    list,
		Total:   *total,
	}

	return result, nil
}

func (s *userService) CreateUser(body model.UserCreateRequest) error {

	timeNow := time.Now()

	// [SYSLOG] INIT
	sysLogJsonInput := make(map[string]interface{})
	sysLogId, err := s.repo.CreateSystemLog(model.SystemLogCreateBody{
		Name:   "CreateUser",
		Status: "PENDING",
		JsonRequest: helper.StructJson(map[string]interface{}{
			"phone":         body.Phone,
			"fullname":      body.Fullname,
			"bankAccount":   body.BankAccount,
			"bankId":        body.BankId,
			"lineId":        body.LineId,
			"channelId":     body.ChannelId,
			"refMemberCode": body.RefMemberCode,
			"adminId":       body.AdminId,
		}),
	})
	if err != nil {
		log.Println("AgcDepositAgent.CreateAgentLog error ------> ", err.Error())
	}

	configWeb, err := s.repo.GetConfiguration()
	if err != nil {
		log.Println("CreateUser.GetConfiguration", err)
		return internalServerError(err)
	}
	// ถ้าใช้มากกว่านี้ให้ log เพิ่ม
	sysLogJsonInput["configWeb.AutoUserApproveTypeId"] = configWeb.AutoUserApproveTypeId

	checkUser, err := s.repo.CheckUserByPhone(body.Phone)
	if err != nil {
		return err
	}
	if checkUser != nil {
		return badRequest(userExist)
	}
	if accountUser, err := s.repo.CheckUserByAccountNo(body.BankAccount); err != nil {
		return err
	} else if accountUser != nil {
		return badRequest(userAccountNoExist)
	}

	admin, err := s.repo.GetAdminById(body.AdminId)
	if err != nil {
		return err
	}

	var userlogInfo model.UserUpdateLogs
	userlogInfo.Description = "Create User"
	userlogInfo.CreatedByUsername = admin.Username

	if body.ChannelId != nil {
		haveChannel, err := s.recomendRepo.CheckRecommendExist(*body.ChannelId)
		if err != nil {
			return badRequest(err.Error())
		}
		if !haveChannel {
			return notFound(channelInvalid)
		}
	}

	hashedPassword, err := helper.GenUserPassword(body.Password)
	if err != nil {
		return nil
	}

	var newUser model.UserFormCreate
	newUser.Username = body.Phone
	newUser.Phone = body.Phone
	newUser.Password = hashedPassword
	newUser.Fullname = body.Fullname
	newUser.UserStatusId = model.USER_STATUS_ACTIVE
	newUser.BankAccount = body.BankAccount
	newUser.BankId = body.BankId
	if body.ChannelId != nil {
		newUser.ChannelId = *body.ChannelId
	}
	newUser.LineId = body.LineId
	// newUser.Encrypt = helper.Encode(body.Password)
	// P.layer ******** แก้ให้ gen เองจาก API

	// P.TULA 2024-10-22 ตอนนี้ genmembercode จะ update encrypt ให้เลย อาจจะต้องลบ
	// passwordAgentGame := helper.Encode(os.Getenv("AGENT_NAME") + "Aa01")
	// newUser.Encrypt = passwordAgentGame

	newUser.VerifiedAt = &timeNow
	newUser.CreatedBy = body.AdminId
	newUser.UpdatedAt = &timeNow

	splitFullname := strings.Split(body.Fullname, " ")
	var firstname, lastname *string
	if len(splitFullname) == 2 {
		firstname = &splitFullname[0]
		lastname = &splitFullname[1]
		newUser.Firstname = *firstname
		newUser.Lastname = *lastname
	}
	if len(splitFullname) == 3 {
		firstname = &splitFullname[1]
		lastname = &splitFullname[2]
		newUser.Firstname = *firstname
		newUser.Lastname = *lastname
	}
	sysLogJsonInput["createUserBody"] = newUser

	insertId, err := s.repo.CreateUser(newUser)
	if err != nil {
		return nil
	}

	// Is RefBy Valid
	if body.RefMemberCode != nil && *body.RefMemberCode != "" {
		var changeRefByRequest model.MoveUserRefbyRequest
		changeRefByRequest.UserId = *insertId
		changeRefByRequest.NewRefByMemberCode = *body.RefMemberCode
		if err := s.moveUserRefby(changeRefByRequest, userlogInfo); err != nil {
			log.Println("CreateUser.MoveUserRefby", err)
			sysLogJsonInput["RefMemberCode"] = "FAIL_REF_MEMBER_CODE"
		} else {
			sysLogJsonInput["RefMemberCode"] = "HAS_REF_MEMBER_CODE"
		}
	}

	// auto_user_approve_type_id
	// 1 รับ user ทันทีหลังสมัครสมาชิก
	// 2 รับ user หลังจากฝากครั้งแรก
	if configWeb.AutoUserApproveTypeId == 1 {
		sysLogJsonInput["AutoUserApproveTypeId"] = "1.รับ userทันทีหลังสมัครสมาชิก"
		_, err := s.GenUniqueUserMemberCode(*insertId)
		if err != nil {
			return internalServerError(err)
		}
	}

	// [SYSLOG]
	if sysLogErr := s.repo.SetSystemLogSuccess(model.SystemLogUpdateBody{
		Id:        *sysLogId,
		JsonInput: helper.StructJson(sysLogJsonInput),
		JsonReponse: helper.StructJson(map[string]interface{}{
			"insertId": insertId,
		}),
	}); sysLogErr != nil {
		log.Println("AmbRegister.SetAgentLogSuccess error ------> ", sysLogErr.Error())
	}

	// [ADMIN_ACTION] SUCCESS เพิ่ม ข้อมูลผู้ใช้งาน {name}
	var adminActionCreateBody model.AdminActionCreateRequest
	adminActionCreateBody.AdminId = body.AdminId
	adminActionCreateBody.TypeId = model.ADMIN_ACTION_MANAGE_USER
	adminActionCreateBody.Detail = fmt.Sprintf("เพิ่ม ข้อมูลผู้ใช้งาน %s", body.Phone)
	adminActionCreateBody.JsonInput = helper.StructJson(body)
	if _, err := s.adminAction.CreateSuccessAdminAction(adminActionCreateBody); err != nil {
		return nil
	}

	var externalNoti model.NotifyExternalNotificationRequest
	externalNoti.TypeNotify = model.IsMemberRegistration
	externalNoti.Phone = body.Phone
	externalNoti.WebScoket.PhoneNumber = body.Phone
	externalNoti.WebScoket.FullName = body.Fullname
	if bank, err := s.repo.GetBankById(body.BankId); err == nil && bank != nil {
		externalNoti.WebScoket.BankAccount = fmt.Sprintf("%s %s", bank.Name, body.BankAccount)
	} else {
		externalNoti.WebScoket.BankAccount = body.BankAccount
	}

	getHeadRef, err := s.repo.GetHeadUsersRefUserId(*insertId)
	if err != nil {
		log.Println("CreateUser.GetUserAllianceกกกกกไInfoByUserId", err)
	}

	if getHeadRef != nil {
		if getHeadRef.UserTypeId == model.USER_TYPE_ALLIANCE {
			externalNoti.RefType = "พันธมิตร"
			// AlGetCommissionSettingUser(userId int64) (*model.AllianceCommissionSettingUser, error)
			allSetting, err := s.repo.AlGetCommissionSettingUser(getHeadRef.Id)
			if err != nil {
				log.Println("CreateUser.AlGetCommissionSettingUser", err)
			}
			if allSetting != nil {
				externalNoti.RefAlias = allSetting.Alias
			}
		} else if getHeadRef.UserTypeId == model.USER_TYPE_AFFILIATE {
			externalNoti.RefType = "แนะนำเพื่อน "
		} else {
			externalNoti.RefType = "สมัครเอง"
		}
		if getHeadRef.MemberCode != nil {
			externalNoti.RefMemberCode = *getHeadRef.MemberCode
		}
		externalNoti.RefUsername = getHeadRef.Fullname
		externalNoti.RefPhone = getHeadRef.Phone
	} else {
		externalNoti.RefType = "สมัครเอง"
	}

	externalNoti.WebScoket.AlertType = "NEW_MEMBER"
	if err := s.notiLine.ExternalNotification(externalNoti); err != nil {
		log.Println("FailedNotify", err)
	}

	return nil
}

func (s *userService) GetRegisterSetting() (*model.ConfigurationUserRegisterResponse, error) {

	// check OTP web setting
	// useOtpRegister = true == check
	// AllowOnlineRegisterForm  bool  ปิดการสมัครผ่านเบอร์โทร
	// CheckAccountNameFastbank bool  ปิดการเช็คชื่อบัญชีธนาคาร เช็คต้องมีชื่อถึงจะสามารถสมัครได้
	configuration, err := s.repo.GetUserRegisterConfiguration()
	if err != nil {
		return nil, err
	}
	return configuration, nil
}

func (s *userService) CheckNewUserPhone(phone string) (*model.UserRegisterCheckResponse, error) {

	user, err := s.repo.CheckUserByPhone(phone)
	if err != nil {
		return nil, err
	}
	if user != nil {
		return nil, badRequest(userExist)
	}

	// IF NO OTP ENABLED, CREATE LOCAL OTP, later
	// if _, err := s.repo.GetLocalRegisterOtpByPhone(phone); err != nil {
	// 	if err.Error() == recordNotFound {
	// 		conv, _ := strconv.Atoi(os.Getenv("OTP_EXPIRE_MINUTE"))
	// 		otpExpireMinute := time.Duration(conv)
	// 		otpBody := model.UserOtpBody{}
	// 		otpBody.Phone = phone
	// 		otpBody.Type = "REGISTER"
	// 		otpBody.ExpiredAt = time.Now().Add(time.Minute * otpExpireMinute)
	// 		otpBody.Ref = "LOCAL"
	// 		otpBody.AntsOtpId = ""
	// 		if err := s.repo.CreateLocalRegisterOtp(otpBody); err != nil {
	// 			return nil, internalServerError(err)
	// 		}
	// 	}
	// }

	return &model.UserRegisterCheckResponse{
		Phone:         phone,
		IsCanRegister: true,
	}, nil
}

func (s *userService) CheckAndUpdateOtpSend(phone string) (int64, error) {

	// มีไว้ เช็คถ้า ขอแล้วไม่กดไรเลย
	s.ClearExpiredCacheOtp()

	now := time.Now().UTC()
	entry, exists := cacheTryOtp.data[phone]

	// If the phone is not in cache or the entry has expired
	if !exists || now.After(entry.ExpiredAt) {
		// Create a new cache entry
		cacheTryOtp.data[phone] = model.VerifyOtpRetry{
			RetryCount:   0,
			Phone:        phone,
			ExpiredAt:    now.Add(30 * time.Minute),
			ClearCacheAt: now.Add(60 * time.Minute),
		}
		return 1, nil
	}

	// If RetryCount exceeds 3, reset it to 1
	if entry.RetryCount > 3 {
		entry.RetryCount = 0
		entry.ExpiredAt = now.Add(30 * time.Minute) // Reset expiration
	} else {
		// Otherwise, just increment RetryCount
		entry.RetryCount++
	}

	// Update the cache entry
	cacheTryOtp.data[phone] = entry

	return entry.RetryCount, nil
}

func (s userService) SendOtpRegister(body model.UserSendOtpBody) (*model.UserOtpResponse, error) {

	retryCount, err := s.CheckAndUpdateOtpSend(body.Phone)
	if err != nil {
		return nil, err
	}

	// If retryCount exceeds the limit, block further attempts
	if retryCount > 3 {
		return nil, badRequest("OTP_RETRY_LIMIT_3_EXCEEDED")
	}

	// Check Exists User
	user, err := s.repo.CheckUserByPhone(body.Phone)
	if err != nil {
		return nil, internalServerError(err)
	}
	if user != nil {
		return nil, badRequest(userExist)
	}

	// check config
	configuration, err := s.repo.GetUserRegisterConfiguration()
	if err != nil {
		return nil, err
	}
	if !configuration.UseOtpRegister {
		return nil, badRequest("WEB_OTP_DISABLED")
	}

	// Check Exists Otp by Phone
	otp, err := s.repo.GetRegisterOtpByPhone(body)
	if err != nil {
		return nil, internalServerError(err)
	}
	if otp != nil && otp.ExpiredAt.Unix() > time.Now().UTC().Unix() {
		return nil, errorWithData("คุณสามารถกดขอรหัส OTP ได้หลังเวลา", otp.ExpiredAt.Local().String())
	}

	// Otp By Provider
	otpResult := model.OtpSendResponse{}
	cyberOtpKey := s.otpRepo.GetCyberOtpKey()
	if cyberOtpKey != "" {
		// OTP from our service
		var cyberbody model.SendCyberOtpRequest
		cyberbody.OtpKey = cyberOtpKey
		cyberbody.Phone = body.Phone
		result, err := s.otpRepo.SendCyberOtp(cyberbody)
		if err != nil {
			log.Println("SendCyberOtp", err)
			return nil, internalServerError(err)
		}
		// result
		otpResult.OtpId = result.Data.OtpId
		otpResult.ReferenceCode = result.Data.OtpRef
	} else {
		if os.Getenv("APP_ENV") != "local" {
			result, err := s.otpRepo.RequestOtp(body.Phone)
			if err != nil {
				return nil, internalServerError(err)
			}
			if result != nil && result.Success.(map[string]interface{})["message"] != "Success" {
				return nil, internalServerError(err)
			}
			if err := s.otpRepo.DecreaseSmsCredit(1); err != nil {
				log.Println("DECREASE_SMS_CREDIT_ERROR", err)
			}
			otpResult.ReferenceCode = result.ReferenceCode
			otpResult.OtpId = result.OtpId
		} else if os.Getenv("APP_ENV") == "local" {
			otpResult.ReferenceCode = helper.GenNumber(6)
			otpResult.OtpId = helper.AlphaNumerics(20)
		}

	}

	// Set OtpLog
	conv, _ := strconv.Atoi(os.Getenv("OTP_EXPIRE_MINUTE"))
	otpExpireMinute := time.Duration(conv)

	otpBody := model.UserOtpBody{}
	otpBody.Phone = body.Phone
	otpBody.Type = "REGISTER"
	otpBody.ExpiredAt = time.Now().Add(time.Minute * otpExpireMinute)
	otpBody.Ref = otpResult.ReferenceCode
	otpBody.AntsOtpId = otpResult.OtpId
	if err := s.repo.CreateUserRegisterOtp(otpBody); err != nil {
		return nil, internalServerError(err)
	}
	// result
	response := model.UserOtpResponse{}
	response.OtpId = otpBody.AntsOtpId
	response.OtpRef = otpBody.Ref

	return &response, nil
}

var cacheTryOtp = struct {
	data map[string]model.VerifyOtpRetry
}{
	data: make(map[string]model.VerifyOtpRetry),
}

func (s *userService) CheckAndUpdateOtpRetry(phone string) (int64, error) {

	// มีไว้เช็ค ห้ามเกิน 3 ครั้ง + 1 ในการขอ ใน 5 นาทีจะ แบน 30 min

	now := time.Now().UTC()
	entry, exists := cacheTryOtp.data[phone]

	// If the phone is not in cache or the entry has expired
	if !exists || now.After(entry.ExpiredAt) {
		// Create a new cache entry
		cacheTryOtp.data[phone] = model.VerifyOtpRetry{
			RetryCount:   1,
			Phone:        phone,
			ExpiredAt:    now.Add(30 * time.Minute),
			ClearCacheAt: now.Add(60 * time.Minute),
		}
		return 1, nil
	}

	// If the cache entry exists and is valid, increment the retry count
	entry.RetryCount++
	cacheTryOtp.data[phone] = entry

	return entry.RetryCount, nil
}

func (s *userService) ClearExpiredCacheOtp() {

	now := time.Now().UTC()

	for phone, entry := range cacheTryOtp.data {
		// Remove the entry if ClearCacheAt has passed
		if now.After(entry.ClearCacheAt) {
			delete(cacheTryOtp.data, phone)
		}
	}
}

func (s userService) VerifyOtpRegister(body model.UserVerifyOtpBody) error {

	// fmt.Println("VerifyOtpRegister", helper.StructJson(body))

	retryCount, err := s.CheckAndUpdateOtpRetry(body.Phone)
	if err != nil {
		return nil
	}
	// fmt.Println(helper.StructJson(cacheTryOtp.data))

	// If retryCount exceeds the limit, block further attempts 1 มาจากการกดขอ
	if retryCount > 3 {
		return badRequest("OTP_RETRY_LIMIT_3_EXCEEDED")
	}

	var query model.UserResendOtpBody
	query.OtpId = body.OtpId
	query.Phone = body.Phone

	otp, err := s.repo.GetUserOtpLogByOtpId(query)
	if err != nil {
		return internalServerError(err)
	}
	if otp == nil {
		return notFound(userOtpNotMatch)
	}
	if otp.VerifiedAt != nil {
		return badRequest(userOtpUsed)
	}

	// check config
	configuration, err := s.repo.GetUserRegisterConfiguration()
	if err != nil {
		return err
	}
	if !configuration.UseOtpRegister {
		return badRequest("WEB_OTP_DISABLED")
	}

	// REMOTE CHECK
	cyberOtpKey := s.otpRepo.GetCyberOtpKey()
	if cyberOtpKey != "" {
		// OTP from our service
		var cyberbody model.VerifyCyberOtpRequest
		cyberbody.OtpId = body.OtpId
		cyberbody.OtpCode = body.Code
		result, err := s.otpRepo.VerifyCyberOtp(cyberbody)
		if err != nil {
			log.Println(err)
			return internalServerError(err)
		}
		if strings.ToLower(result.Message) != "success" {
			log.Println(result.Message)
			return badRequest(result.Message)
		}
	} else {
		result, err := s.otpRepo.VerifyOtp(body)
		if err != nil {
			log.Println(err)
			return internalServerError(err)
		}
		if result != nil {
			if result.IsExprCode {
				log.Println("EXPIRED")
				return badRequest(userOtpExpired)
			}
			if !result.Result || result.IsErrorCount {
				log.Println("NOT_MATCH")
				return badRequest(userOtpNotMatch)
			}
		}
	}

	// onSuccess :: update Local Pin for later check
	if err := s.repo.UpdateUserOtpLocalPin(otp.Id, body.Code); err != nil {
		return internalServerError(err)
	}

	return nil
}

func (s userService) VerifyLocalOtpRegister(body model.UserVerifyOtpBody) error {

	otp, err := s.repo.GetLocalRegisterOtpByPhone(body.Phone)
	if err != nil {
		return internalServerError(err)
	}
	if otp == nil {
		return notFound(userOtpNotMatch)
	}
	if otp.VerifiedAt != nil {
		return badRequest(userOtpUsed)
	}
	if otp.Ref == "LOCAL" {
		log.Panicln("PASSED_LOCAL")
	} else {
		if otp.LocalPin != body.Code {
			log.Println("NOT_MATCH", otp.LocalPin, body.Code)
			return badRequest(userOtpNotMatch)
		}
	}
	return nil
}

func (s userService) RacingSendOtpForget() (*int64, error) {

	actionAt := time.Now()

	var createBody model.RaceActionCreateBody
	createBody.Name = "RacingSendOtpForget"
	// createBody.JsonRequest = helper.StructJson(map[string]interface{}{"id": id})
	createBody.Status = "PENDING"

	// KEY = OTP_FORGET_yymmddhhmm
	timing := actionAt.Format("0601021504")
	// only one action per five second
	createBody.ActionKey = fmt.Sprintf("OTP_FORGET_%s", timing)
	createBody.UnlockAt = actionAt.Add(time.Second * 5)
	if _, err := s.repo.GetRaceActionIdByActionKey(createBody.ActionKey); err != nil {
		// log.Println("RacingSendOtpForget.ERROR.GetRaceActionByActionKey", err)
		if err != gorm.ErrRecordNotFound {
			return nil, internalServerError(errors.New("CANNOT_CHECK_ACTION"))
		}
	} else {
		return nil, internalServerError(errors.New("WORK_IN_ACTION"))
	}

	// only not found will be created
	actionId, err := s.repo.CreateRaceCondition(createBody)
	if err != nil {
		log.Println("RacingSendOtpForget.ERROR.CreateRaceCondition", err)
		return nil, internalServerError(errors.New("CANNOT_CREATE_ACTION"))
	}
	return &actionId, nil
}

func (s userService) SendOtpForget(body model.UserSendOtpBody) (*model.UserOtpResponse, error) {

	user, err := s.repo.CheckUserByPhone(body.Phone)
	if err != nil {
		return nil, internalServerError(err)
	}

	if user == nil {
		return nil, badRequest(userNotFoundData)
	}

	otp, err := s.repo.GetForgetOtpByPhone(body)
	if err != nil {
		return nil, internalServerError(err)
	}

	if otp != nil && otp.ExpiredAt.Unix() > time.Now().UTC().Unix() {
		return nil, errorWithData("คุณสามารถกดขอรหัส OTP ได้หลังเวลา", otp.ExpiredAt.Local().String())
	}

	otpResult := model.OtpSendResponse{}
	cyberOtpKey := s.otpRepo.GetCyberOtpKey()
	if cyberOtpKey != "" {
		// OTP from our service
		var cyberbody model.SendCyberOtpRequest
		cyberbody.OtpKey = cyberOtpKey
		cyberbody.Phone = body.Phone
		result, err := s.otpRepo.SendCyberOtp(cyberbody)
		if err != nil {
			log.Println("SendCyberOtp", err)
			return nil, internalServerError(err)
		}
		// result
		otpResult.OtpId = result.Data.OtpId
		otpResult.ReferenceCode = result.Data.OtpRef
	} else {
		if os.Getenv("APP_ENV") != "local" {
			result, err := s.otpRepo.RequestOtp(body.Phone)
			if err != nil {
				return nil, internalServerError(err)
			}
			if result != nil && result.Success.(map[string]interface{})["message"] != "Success" {
				return nil, internalServerError(err)
			}
			if err := s.otpRepo.DecreaseSmsCredit(1); err != nil {
				log.Println("DECREASE_SMS_CREDIT_ERROR", err)
			}
			otpResult.ReferenceCode = result.ReferenceCode
			otpResult.OtpId = result.OtpId
		}
		if os.Getenv("APP_ENV") == "local" {
			otpResult.ReferenceCode = helper.GenNumber(6)
			otpResult.OtpId = helper.AlphaNumerics(20)
		}
	}

	// Set OTP LOG
	conv, _ := strconv.Atoi(os.Getenv("OTP_EXPIRE_MINUTE"))
	otpExpireMinute := time.Duration(conv)

	otpBody := model.UserOtpBody{}
	otpBody.Phone = body.Phone
	otpBody.Type = "FORGET"
	otpBody.Ref = otpResult.ReferenceCode
	otpBody.AntsOtpId = otpResult.OtpId
	otpBody.ExpiredAt = time.Now().Add(time.Minute * otpExpireMinute)
	if user != nil {
		otpBody.UserId = user.Id
		if err := s.repo.CreateUserOtpOfForget(otpBody); err != nil {
			return nil, internalServerError(err)
		}
	}

	response := model.UserOtpResponse{}
	response.UserId = user.Id
	response.OtpId = otpBody.AntsOtpId
	response.OtpRef = otpBody.Ref

	return &response, nil
}

func (s *userService) UserRegister(body model.UserRegister) (*model.UserVerifyOtpResponse, error) {

	registerPhone := strings.TrimSpace(body.Phone)

	configWeb, err := s.repo.GetConfiguration()
	if err != nil {
		log.Println("UserCreateWithdrawTransaction.GetConfiguration", err)
		return nil, internalServerError(err)
	}

	// 2025/01/04 P.LAY confirm block api
	if configWeb.AllowOnlineRegistration == false {
		return nil, badRequest("WEB_REGISTER_DISABLED")
	}

	// 2025/05/01 confirm P.MARKOD ให้ทำผ่าน API
	// 2025/05/01 ช่วงเย็น P.LAY ให้ front ดัก
	// getBank, err := s.repo.GetBankById(body.BankId)
	// if err != nil {
	// 	return nil, internalServerError(err)
	// }
	// var bankAccountRequest model.AccountInfoFastbankRequest
	// bankAccountRequest.BankCode = getBank.Code
	// bankAccountRequest.BankNo = body.BankAccount
	// getUserAccountFromExternal, err := s.repo.GetAccountInfoFastbank(bankAccountRequest)
	// if err != nil {
	// 	return nil, badRequest("USER_BANK_ACCOUNT_DATA_NOT_CORRECT")
	// }
	// if getUserAccountFromExternal.BankAccountName != "" {
	// 	body.Fullname = getUserAccountFromExternal.BankAccountName
	// } else {
	// 	body.Fullname = getUserAccountFromExternal.BankAccountNameEN
	// }

	// OTP Setting
	if configWeb.UseOtpRegister {

		if err := s.VerifyLocalOtpRegister(model.UserVerifyOtpBody{
			Phone: registerPhone,
			OtpId: body.OtpId,
			Code:  body.Code,
		}); err != nil {
			return nil, err
		}
	}

	if user, err := s.repo.CheckUserByPhone(registerPhone); err != nil {
		return nil, internalServerError(err)
	} else if user != nil {
		return nil, badRequest(userExist)
	}

	if accountUser, err := s.repo.CheckUserByAccountNo(body.BankAccount); err != nil {
		return nil, err
	} else if accountUser != nil {
		return nil, badRequest(userAccountNoExist)
	}

	if accountUser, err := s.repo.CheckUserByAccountNo(body.BankAccount); err != nil {
		return nil, err
	} else if accountUser != nil {
		return nil, badRequest(userAccountNoExist)
	}

	// if otp.AntsOtpId != body.OtpId {
	// 	return nil, notFound(userOtpNotMatch)
	// }

	// if otp.VerifiedAt != nil {
	// 	return nil, badRequest(userOtpUsed)
	// }

	if body.ChannelId != nil {
		haveChannel, err := s.recomendRepo.CheckRecommendExist(*body.ChannelId)
		if err != nil {
			return nil, badRequest(err.Error())
		}
		if !haveChannel {
			return nil, notFound(channelInvalid)
		}
	}

	// if os.Getenv("APP_ENV") != "local" {

	// verifyOtpData := model.UserVerifyOtpBody{
	// 	OtpId: body.OtpId,
	// 	Code:  body.Code,
	// }

	// result, err := s.otpRepo.VerifyOtp(verifyOtpData)
	// if err != nil {
	// 	return nil, internalServerError(err)
	// }

	// if result != nil {

	// 	if !result.Result || result.IsErrorCount {
	// 		return nil, badRequest(userOtpNotMatch)
	// 	}

	// 	if result.IsExprCode {
	// 		return nil, badRequest(userOtpExpired)
	// 	}
	// }
	// }

	hashedPassword, err := helper.GenUserPassword(body.Password)
	if err != nil {
		return nil, internalServerError(err)
	}

	var newUser model.UserRegisterForm
	newUser.Username = registerPhone
	newUser.Phone = registerPhone
	newUser.Password = hashedPassword
	newUser.Fullname = body.Fullname
	newUser.BankAccount = body.BankAccount
	newUser.BankId = body.BankId
	if body.ChannelId != nil {
		newUser.ChannelId = *body.ChannelId
	}
	newUser.LineId = body.LineId
	// newUser.Encrypt = helper.Encode(body.Password)
	// P.layer ******** แก้ให้ gen เองจาก API
	// P.TULA 2024-10-22 ตอนนี้ genmembercode จะ update encrypt ให้เลย อาจจะต้องลบ
	// passwordAgentGame := helper.Encode(os.Getenv("AGENT_NAME") + "Aa01")
	// newUser.Encrypt = passwordAgentGame
	newUser.IpRegistered = body.IpRegistered

	splitFullname := strings.Split(body.Fullname, " ")
	var firstname, lastname *string
	if len(splitFullname) == 2 {
		firstname = &splitFullname[0]
		lastname = &splitFullname[1]
		newUser.Firstname = *firstname
		newUser.Lastname = *lastname
	}

	if len(splitFullname) == 3 {
		firstname = &splitFullname[1]
		lastname = &splitFullname[2]
		newUser.Firstname = *firstname
		newUser.Lastname = *lastname
	}

	var credit float64
	var afObj model.Affiliate
	var refTypeId int64
	var refId int64

	if body.RefCode != "" {
		ref, err := s.repo.GetUserIdByRefCode(body.RefCode)
		if err != nil {
			return nil, errors.New("INVALID_REF_CODE")
		}
		if ref != nil {
			refTypeId = ref.UserTypeId
			afObj.RefId = ref.UserId
			newUser.RefBy = &ref.UserId
			credit, err = s.calCommissionRegister()
			if err != nil {
				return nil, internalServerError(err)
			}
		}
		refId = ref.UserId
	} else if body.RefBy != "" {
		refUserId := helper.DecodeData(body.RefBy)
		ref, err := s.repo.GetUserIdByRef(refUserId)
		if err != nil {
			return nil, errors.New("INVALID_REF_CODE")
		}
		if ref != nil {
			refTypeId = ref.UserTypeId
			afObj.RefId = ref.UserId
			newUser.RefBy = &ref.UserId
			credit, err = s.calCommissionRegister()
			if err != nil {
				return nil, internalServerError(err)
			}
		}
		refId = ref.UserId
	} else if body.SaleCode != "" {
		ref, err := s.repo.GetUserIdBySaleCode(body.SaleCode)
		if err != nil {
			return nil, errors.New("INVALID_REF_CODE")
		}
		if ref != nil {
			refTypeId = ref.UserTypeId
			afObj.RefId = ref.UserId
			newUser.RefBy = &ref.UserId
			credit, err = s.calCommissionRegister()
			if err != nil {
				return nil, internalServerError(err)
			}
		}
		refId = ref.UserId
	}

	insertId, err := s.repo.UserRegister(newUser, afObj, credit, refTypeId)
	if err != nil {
		return nil, internalServerError(err)
	}

	if (credit > 0 && body.RefCode != "") || (credit > 0 && body.RefBy != "") || (credit > 0 && body.SaleCode != "") {
		if err := s.CreateTurnOverFromAffNewRegister(refId, credit, *insertId); err != nil {
			return nil, internalServerError(err)
		}
	}

	userObj := model.UserResponse{
		Id:         *insertId,
		Phone:      registerPhone,
		Fullname:   newUser.Fullname,
		MemberCode: nil, // ไม่มี
	}

	token, err := helper.CreateJWTUser(userObj)
	if err != nil {
		return nil, internalServerError(err)
	}

	// Single Session
	if err := s.repo.SetUserSingleSession(*insertId, token); err != nil {
		return nil, internalServerError(err)
	}

	response := model.UserVerifyOtpResponse{
		Token: token,
	}

	// auto_user_approve_type_id
	// 1 รับ user ทันทีหลังสมัครสมาชิก
	// 2 รับ user หลังจากฝากครั้งแรก
	if configWeb.AutoUserApproveTypeId == 1 {
		_, err := s.GenUniqueUserMemberCode(*insertId)
		if err != nil {
			return nil, internalServerError(err)
		}
	}

	// [20240205] Promotion
	// var checkNewMember model.CheckUserPromotionBody
	// checkNewMember.UserId = *insertId
	// if _, err := CheckUserPromotionOnlyNewMemberFree(repository.NewPromotionWebRepository(s.shareDb), checkNewMember); err != nil {
	// 	log.Println("CheckUserPromotionOnlyNewMemberFree.error", err)
	// }

	// noti
	var externalNoti model.NotifyExternalNotificationRequest
	externalNoti.TypeNotify = model.IsMemberRegistration
	externalNoti.Phone = body.Phone
	externalNoti.WebScoket.PhoneNumber = body.Phone
	externalNoti.WebScoket.FullName = body.Fullname
	if bank, err := s.repo.GetBankById(body.BankId); err == nil && bank != nil {
		externalNoti.WebScoket.BankAccount = fmt.Sprintf("%s %s", bank.Name, body.BankAccount)
	} else {
		externalNoti.WebScoket.BankAccount = body.BankAccount
	}

	getHeadRef, err := s.repo.GetHeadUsersRefUserId(*insertId)
	if err != nil {
		log.Println("CreateUser.GetUserAllianceกกกกกไInfoByUserId", err)
	}

	if getHeadRef != nil {
		if getHeadRef.UserTypeId == model.USER_TYPE_ALLIANCE {
			externalNoti.RefType = "พันธมิตร"
			// AlGetCommissionSettingUser(userId int64) (*model.AllianceCommissionSettingUser, error)
			allSetting, err := s.repo.AlGetCommissionSettingUser(getHeadRef.Id)
			if err != nil {
				log.Println("CreateUser.AlGetCommissionSettingUser", err)
			}
			if allSetting != nil {
				externalNoti.RefAlias = allSetting.Alias
			}
		} else if getHeadRef.UserTypeId == model.USER_TYPE_AFFILIATE {
			externalNoti.RefType = "แนะนำเพื่อน "
		} else {
			externalNoti.RefType = "สมัครเอง"
		}
		if getHeadRef.MemberCode != nil {
			externalNoti.RefMemberCode = *getHeadRef.MemberCode
		}
		externalNoti.RefUsername = getHeadRef.Fullname
		externalNoti.RefPhone = getHeadRef.Phone
	} else {
		externalNoti.RefType = "สมัครเอง"
	}

	// var externalNoti model.NotifyExternalNotificationRequest
	// externalNoti.TypeNotify = model.IsMemberRegistration
	// externalNoti.Phone = registerPhone
	// externalNoti.WebScoket.PhoneNumber = registerPhone
	// externalNoti.WebScoket.FullName = body.Fullname
	// bank, _ := s.repo.GetBankById(body.BankId)
	// if bank != nil {
	// 	externalNoti.WebScoket.BankAccount = body.BankAccount
	// } else {
	// 	externalNoti.WebScoket.BankAccount = fmt.Sprintf("%s %s", bank.Name, body.BankAccount)
	// }

	// P.mink confirm ยากให้ auto login
	if err = s.repo.UpdateLogin(*insertId, body.IpRegistered); err != nil {
		log.Println("LogUserLogin.UpdateLogin", err)
	}

	externalNoti.WebScoket.AlertType = "NEW_MEMBER"
	if err := s.notiLine.ExternalNotification(externalNoti); err != nil {
		log.Println("FailedNotify", err)
	}

	return &response, nil
}

func (s *userService) Login(body model.UserLogin) (*model.UserLoginResponse, error) {

	user, err := s.repo.GetFrontUserByPhone(body.Phone)
	if err != nil {
		return nil, internalServerError(err)
	}
	if user == nil || user.Id == 0 {
		return nil, notFound("USER_NOT_FOUND")
	}
	if user.UserStatusId != model.USER_STATUS_ACTIVE {
		return nil, badRequest("USER_NOT_ACTIVE")
	}
	if err := helper.CompareUserPassword(body.Password, user.Password); err != nil {
		// return nil, badRequest("USER_NOT_FOUND")
		// 2024/02/23 ลูกค้าค้องการที่จะรู้ risk โดนยิ่งเช็คเบอร์
		return nil, badRequest("USER_PASSWORD_NOT_CORRECT")
	}

	token, err := helper.CreateJWTUser(*user)
	if err != nil {
		return nil, internalServerError(err)
	}

	// Single Session
	if err := s.repo.SetUserSingleSession(user.Id, token); err != nil {
		return nil, internalServerError(err)
	}

	if err = s.repo.UpdateLogin(user.Id, body.Ip); err != nil {
		return nil, internalServerError(err)
	}

	var response model.UserLoginResponse
	response.Token = token

	if user.MemberCode != nil {

		domainName := os.Getenv("DOMAIN_NAME")
		agentName := os.Getenv("AGENT_NAME")

		password, err := helper.Decode(user.Encrypt)
		if err != nil {
			return nil, internalServerError(err)
		}

		agentProvider := os.Getenv("AGENT_PROVIDER")
		if agentProvider == "AMB" {
			agentData := model.AmbLogin{}
			agentData.Username = *user.MemberCode
			result, err := s.repo.AmbLogin(agentData)
			if err != nil {
				return nil, internalServerError(err)
			}
			response.GameProvider = agentProvider
			response.GameToken = result.Url
		} else {
			sign := agentName + *user.MemberCode + *password
			timeNow := time.Now()
			agentData := model.AgcLogin{}
			agentData.Username = *user.MemberCode
			agentData.Partner = agentName
			agentData.Timestamp = timeNow.Unix()
			agentData.Sign = helper.CreateSign(os.Getenv("AGENT_KEY"), sign, timeNow)
			agentData.Domain = domainName
			agentData.Lang = "th-th"
			agentData.IsMobile = false
			agentData.Ip = body.Ip
			result, err := s.repo.AgcLogin(agentData)
			if err != nil {
				return nil, internalServerError(err)
			}
			response.GameProvider = agentProvider
			response.GameToken = result.Token
		}
	}

	return &response, nil
}

func (s *userService) UserLineLogin(body model.UserLineLoginRequest) (*model.UserLoginResponse, error) {

	// Insert Log
	var createBody model.LineLoginLogCreateBody
	createBody.AccessToken = body.AccessToken
	createBody.RefreshToken = body.RefreshToken
	createBody.Uuid = body.Uuid
	createBody.ImageUrl = body.Image
	createBody.DisplayName = body.Name
	if _, err := s.repo.CreateLineLoginLog(createBody); err != nil {
		log.Println("UserLineLogin.CreateLineLoginLog", err)
		return nil, internalServerError(err)
	}

	// Validate Token Same ClientId
	if _, err := s.repo.ValidateLineToken(body.AccessToken); err != nil {
		log.Println("UserLineLogin.ValidateLineToken", err)
		return nil, badRequest("USER_NOT_FOUND")
	}

	user, err := s.repo.GetFrontUserByLineUuid(body.Uuid)
	if err != nil {
		if err.Error() == recordNotFound {
			// * Need Register, API return 400 for Frontend to do that.
			return nil, badRequest("LINE_UUID_NOT_FOUND")
		}
		return nil, internalServerError(err)
	}
	if user == nil || user.Id == 0 {
		return nil, notFound("LINE_UUID_NOT_FOUND")
	}
	if user.UserStatusId != model.USER_STATUS_ACTIVE {
		return nil, badRequest("USER_NOT_ACTIVE")
	}

	token, err := helper.CreateJWTUser(*user)
	if err != nil {
		return nil, internalServerError(err)
	}

	// Single Session
	if err := s.repo.SetUserSingleSession(user.Id, token); err != nil {
		return nil, internalServerError(err)
	}

	if err = s.repo.UpdateLogin(user.Id, body.Ip); err != nil {
		return nil, internalServerError(err)
	}

	var response model.UserLoginResponse
	response.Token = token

	// Get Game Token
	if user.MemberCode != nil {

		domainName := os.Getenv("DOMAIN_NAME")
		agentName := os.Getenv("AGENT_NAME")

		password, err := helper.Decode(user.Encrypt)
		if err != nil {
			return nil, internalServerError(err)
		}

		agentProvider := os.Getenv("AGENT_PROVIDER")
		if agentProvider == "AMB" {
			agentData := model.AmbLogin{}
			agentData.Username = *user.MemberCode
			result, err := s.repo.AmbLogin(agentData)
			if err != nil {
				return nil, internalServerError(err)
			}
			response.GameProvider = agentProvider
			response.GameToken = result.Url
		} else {
			sign := agentName + *user.MemberCode + *password
			timeNow := time.Now()
			agentData := model.AgcLogin{}
			agentData.Username = *user.MemberCode
			agentData.Partner = agentName
			agentData.Timestamp = timeNow.Unix()
			agentData.Sign = helper.CreateSign(os.Getenv("AGENT_KEY"), sign, timeNow)
			agentData.Domain = domainName
			agentData.Lang = "th-th"
			agentData.IsMobile = false
			agentData.Ip = body.Ip
			result, err := s.repo.AgcLogin(agentData)
			if err != nil {
				return nil, internalServerError(err)
			}
			response.GameProvider = agentProvider
			response.GameToken = result.Token
		}
	}
	return &response, nil
}

func (s *userService) GetWebConfiguration() (*model.ConfigurationResponse, error) {

	configuration, err := s.repo.GetConfiguration()
	if err != nil {
		return nil, err
	}
	return configuration, nil
}

func (s *userService) UserLineRegister(body model.UserLineRegisterRequest) (*model.UserVerifyOtpResponse, error) {

	// *ต้องใช้เบอร์ในการติดต่อ Agent
	registerPhone := strings.TrimSpace(body.Phone)

	configWeb, err := s.repo.GetConfiguration()
	if err != nil {
		log.Println("UserCreateWithdrawTransaction.GetConfiguration", err)
		return nil, internalServerError(err)
	}

	// 2025/05/01 confirm P.MARKOD ให้ทำผ่าน API
	// 2025/05/01 ช่วงเย็น P.LAY ให้ front ดัก
	// getBank, err := s.repo.GetBankById(body.BankId)
	// if err != nil {
	// 	return nil, internalServerError(err)
	// }
	// var bankAccountRequest model.AccountInfoFastbankRequest
	// bankAccountRequest.BankCode = getBank.Code
	// bankAccountRequest.BankNo = body.BankAccount
	// getUserAccountFromExternal, err := s.repo.GetAccountInfoFastbank(bankAccountRequest)
	// if err != nil {
	// 	return nil, badRequest("USER_BANK_ACCOUNT_DATA_NOT_CORRECT")
	// }
	// if getUserAccountFromExternal.BankAccountName != "" {
	// 	body.Fullname = getUserAccountFromExternal.BankAccountName
	// } else {
	// 	body.Fullname = getUserAccountFromExternal.BankAccountNameEN
	// }

	// Validate Token Same ClientId
	if _, err := s.repo.ValidateLineToken(body.AccessToken); err != nil {
		return nil, badRequest(err.Error())
	}

	// No-OTP

	if userphone, err := s.repo.CheckUserByPhone(registerPhone); err != nil {
		return nil, internalServerError(err)
	} else if userphone != nil {
		return nil, badRequest(userExist)
	}
	if userUuid, err := s.repo.CheckUserByLineUuid(body.Uuid); err != nil {
		return nil, internalServerError(err)
	} else if userUuid != nil {
		return nil, badRequest(userExist)
	}
	if accountUser, err := s.repo.CheckUserByAccountNo(body.BankAccount); err != nil {
		return nil, err
	} else if accountUser != nil {
		return nil, badRequest(userAccountNoExist)
	}

	if body.ChannelId != nil {
		haveChannel, err := s.recomendRepo.CheckRecommendExist(*body.ChannelId)
		if err != nil {
			return nil, badRequest(err.Error())
		}
		if !haveChannel {
			return nil, notFound(channelInvalid)
		}
	}

	// Fixed Password
	goodPassword := "iI3Im1l1ackNvZ"
	hashedPassword, err := helper.GenUserPassword(goodPassword)
	if err != nil {
		return nil, internalServerError(err)
	}

	var newUser model.UserRegisterForm
	newUser.Username = body.Uuid
	newUser.LoginType = "line"
	newUser.Phone = registerPhone
	newUser.Password = hashedPassword
	newUser.Fullname = body.Fullname
	newUser.BankAccount = body.BankAccount
	newUser.BankId = body.BankId
	if body.ChannelId != nil {
		newUser.ChannelId = *body.ChannelId
	}
	// newUser.Encrypt = helper.Encode(goodPassword)
	// P.layer ******** แก้ให้ gen เองจาก API

	// P.TULA 2024-10-22 ตอนนี้ genmembercode จะ update encrypt ให้เลย อาจจะต้องลบ
	// passwordAgentGame := helper.Encode(os.Getenv("AGENT_NAME") + "Aa01")
	// newUser.Encrypt = passwordAgentGame

	newUser.IpRegistered = body.Ip

	splitFullname := strings.Split(body.Fullname, " ")
	var firstname, lastname *string
	if len(splitFullname) == 2 {
		firstname = &splitFullname[0]
		lastname = &splitFullname[1]
		newUser.Firstname = *firstname
		newUser.Lastname = *lastname
	}
	if len(splitFullname) == 3 {
		firstname = &splitFullname[1]
		lastname = &splitFullname[2]
		newUser.Firstname = *firstname
		newUser.Lastname = *lastname
	}

	var credit float64
	var afObj model.Affiliate
	var refTypeId int64
	var refId int64

	if body.RefCode != "" {
		ref, err := s.repo.GetUserIdByRefCode(body.RefCode)
		if err != nil {
			return nil, errors.New("INVALID_REF_CODE")
		}
		if ref != nil {
			refTypeId = ref.UserTypeId
			afObj.RefId = ref.UserId
			newUser.RefBy = &ref.UserId
			credit, err = s.calCommissionRegister()
			if err != nil {
				return nil, internalServerError(err)
			}
		}
		refId = afObj.RefId
	} else if body.RefBy != "" {
		refUserId := helper.DecodeData(body.RefBy)
		ref, err := s.repo.GetUserIdByRef(refUserId)
		if err != nil {
			return nil, errors.New("INVALID_REF_CODE")
		}
		if ref != nil {
			refTypeId = ref.UserTypeId
			afObj.RefId = ref.UserId
			newUser.RefBy = &ref.UserId
			credit, err = s.calCommissionRegister()
			if err != nil {
				return nil, internalServerError(err)
			}
		}
		refId = ref.UserId
	}

	insertId, err := s.repo.UserRegister(newUser, afObj, credit, refTypeId)
	if err != nil {
		return nil, internalServerError(err)
	}

	if credit > 0 && body.RefCode != "" || credit > 0 && body.RefBy != "" {
		if err := s.CreateTurnOverFromAffNewRegister(refId, credit, *insertId); err != nil {
			return nil, internalServerError(err)
		}
	}

	// auto_user_approve_type_id
	// 1 รับ user ทันทีหลังสมัครสมาชิก
	// 2 รับ user หลังจากฝากครั้งแรก
	if configWeb.AutoUserApproveTypeId == 1 {
		_, err := s.GenUniqueUserMemberCode(*insertId)
		if err != nil {
			return nil, internalServerError(err)
		}
	}

	// ==============================================================================

	insertUser, err := s.repo.GetUserById(*insertId)
	if err != nil {
		return nil, internalServerError(err)
	}

	userObj := model.UserResponse{
		Id:         insertUser.Id,
		Phone:      insertUser.Phone,
		Fullname:   insertUser.Fullname,
		MemberCode: insertUser.MemberCode,
	}
	token, err := helper.CreateJWTUser(userObj)
	if err != nil {
		return nil, internalServerError(err)
	}
	response := model.UserVerifyOtpResponse{
		Token: token,
	}

	// Single Session
	if err := s.repo.SetUserSingleSession(insertUser.Id, token); err != nil {
		return nil, internalServerError(err)
	}

	// P.mink confirm ยากให้ auto login
	if err = s.repo.UpdateLogin(*insertId, body.Ip); err != nil {
		log.Println("LogUserLogin.UpdateLogin", err)
	}

	var externalNoti model.NotifyExternalNotificationRequest
	externalNoti.TypeNotify = model.IsMemberRegistration
	externalNoti.Phone = insertUser.Phone
	externalNoti.WebScoket.BankAccount = insertUser.BankAccount
	externalNoti.WebScoket.PhoneNumber = insertUser.Phone
	externalNoti.WebScoket.FullName = insertUser.Fullname
	externalNoti.WebScoket.AlertType = "NEW_MEMBER"
	if err := s.notiLine.ExternalNotification(externalNoti); err != nil {
		log.Println("FailedNotify", err)
	}

	return &response, nil
}

func (s *userService) GetGameToken(userId int64, ip string) (*model.UserLoginResponse, error) {

	user, err := s.repo.GetUserById(userId)
	if err != nil {
		return nil, internalServerError(err)
	}
	if user == nil || user.Id == 0 {
		return nil, notFound("USER_NOT_FOUND")
	}
	if user.UserStatusId != model.USER_STATUS_ACTIVE {
		return nil, badRequest("USER_NOT_ACTIVE")
	}

	var response model.UserLoginResponse
	if user.MemberCode != nil {

		domainName := os.Getenv("DOMAIN_NAME")
		agentName := os.Getenv("AGENT_NAME")

		password, err := helper.Decode(user.Encrypt)
		if err != nil {
			return nil, internalServerError(err)
		}

		agentProvider := os.Getenv("AGENT_PROVIDER")
		if agentProvider == "AMB" {
			agentData := model.AmbLogin{}
			agentData.Username = *user.MemberCode
			result, err := s.repo.AmbLogin(agentData)
			if err != nil {
				return nil, internalServerError(err)
			}
			response.GameProvider = agentProvider
			response.GameToken = result.Url
		} else {
			sign := agentName + *user.MemberCode + *password
			timeNow := time.Now()
			agentData := model.AgcLogin{}
			agentData.Username = *user.MemberCode
			agentData.Partner = agentName
			agentData.Timestamp = timeNow.Unix()
			agentData.Sign = helper.CreateSign(os.Getenv("AGENT_KEY"), sign, timeNow)
			agentData.Domain = domainName
			agentData.Lang = "th-th"
			agentData.IsMobile = false
			agentData.Ip = ip
			result, err := s.repo.AgcLogin(agentData)
			if err != nil {
				return nil, internalServerError(err)
			}
			response.GameProvider = agentProvider
			response.GameToken = result.Token
		}
	}
	return &response, nil
}

func (s userService) userUpdateLog(reqUpdateUser model.UpdateUserRequest, userDetailStruct model.UserDetail, adminName string) error {

	var changeList []model.UserUpdateLogs

	reqValue := reflect.ValueOf(reqUpdateUser)
	userValue := reflect.ValueOf(userDetailStruct)
	// Skip fields with nil or empty string values in reqUpdateUser
	for i := 0; i < reqValue.NumField(); i++ {
		fieldName := reqValue.Type().Field(i).Name
		reqFieldValue := reqValue.Field(i).Interface()
		// Check if the field exists in userValue before accessing its value
		userField := userValue.FieldByName(fieldName)
		if !userField.IsValid() {
			// The field doesn't exist in userDetailStruct, handle it as needed
			continue
		}
		if reqFieldValue == nil || (reflect.ValueOf(reqFieldValue).Kind() == reflect.String && reqFieldValue == "") {
			continue
		}

		userFieldValue := userValue.FieldByName(fieldName).Interface()

		if !reflect.DeepEqual(reqFieldValue, userFieldValue) {
			if fieldName == "Encrypt" {
				continue
			}
			if fieldName == "Password" {
				description := "Changed Password"
				// Log the difference or do anything you need
				changeList = append(changeList, model.UserUpdateLogs{
					UserId:            userDetailStruct.Id,
					Description:       description,
					CreatedByUsername: adminName,
					Ip:                reqUpdateUser.Ip, // Assuming reqUpdateUser has 'Ip' field
				})
			} else {
				userFieldValue := userValue.FieldByName(fieldName).Interface()

				if !reflect.DeepEqual(reqFieldValue, userFieldValue) {
					var description string
					if fieldName == "ChannelId" {
						// Get Channel Name
						// GetChannelOption() ([]model.RecommendList, error)
						channel, _ := s.repo.GetChannelOption()
						if channel != nil {
							oldChannel := ""
							newChannel := ""
							for _, v := range channel {
								if v.Id == reqFieldValue.(int64) {
									newChannel = *v.Title
								}
								if v.Id == userFieldValue.(int64) {
									oldChannel = *v.Title
								}
							}
							description = fmt.Sprintf("Channel changed from %s => %s", oldChannel, newChannel)
						} else {
							description = fmt.Sprintf("Channel changed from %v => %v", userFieldValue, reqFieldValue)
						}

					} else if fieldName == "BankId" {
						// GetBanksOption() ([]model.BankResponse, error)
						bank, _ := s.repo.GetBanksOption()
						if bank != nil {
							oldBank := ""
							newBank := ""
							for _, v := range bank {
								if v.Id == reqFieldValue.(int64) {
									newBank = v.Code
								}
								if v.Id == userFieldValue.(int64) {
									oldBank = v.Code
								}
							}
							description = fmt.Sprintf("Bank changed from %s => %s", oldBank, newBank)
						} else {
							description = fmt.Sprintf("Bank changed from %v => %v", userFieldValue, reqFieldValue)
						}
					} else {
						description = fmt.Sprintf("%s changed from %v => %v", fieldName, userFieldValue, reqFieldValue)
					}

					// Log the difference or do anything you need
					changeList = append(changeList, model.UserUpdateLogs{
						UserId:            userDetailStruct.Id,
						Description:       description,
						CreatedByUsername: adminName,
						Ip:                reqUpdateUser.Ip, // Assuming reqUpdateUser has 'Ip' field
					})
				}
			}
		}
	}
	if len(changeList) > 0 {
		if err := s.repo.UserUpdateLog(changeList); err != nil {
			return err
		}
	}

	return nil
}

func (s *userService) UpdateUser(userId int64, req model.UpdateUserRequest, adminName string, adminId int64) error {

	sysLogJsonInput := make(map[string]interface{})
	sysLogId, _ := s.repo.CreateSystemLog(model.SystemLogCreateBody{
		Name:   "UPDATE_USER",
		Status: "PENDING",
		JsonRequest: helper.StructJson(map[string]interface{}{
			"fullname":      req.Fullname,
			"firstname":     req.Firstname,
			"lastname":      req.Lastname,
			"memberCode":    req.MemberCode,
			"phone":         req.Phone,
			"username":      req.Username,
			"refMemberCode": req.RefMemberCode,
			"bankAccount":   req.BankAccount,
			"bankId":        req.BankId,
			"channelId":     req.ChannelId,
			"trueWallet":    req.TrueWallet,
			"contact":       req.Contact,
			"note":          req.Note,
			"course":        req.Course,
			"lineId":        req.LineId,
			// "encrypt":       req.Encrypt,
			"ip": req.Ip,
		}),
	})

	user, err := s.repo.GetUser(userId)
	if err != nil {
		if err.Error() == recordNotFound {
			return notFound(userNotFound)
		}
		return internalServerError(err)
	}
	if user == nil {
		return notFound(userNotFound)
	}

	var body model.UpdateUserBody
	body.Fullname = req.Fullname
	body.Firstname = req.Firstname
	body.Lastname = req.Lastname
	body.MemberCode = req.MemberCode
	if user.LoginType == "line" {
		body.Password = ""
	} else {
		body.Password = req.Password
	}
	body.Phone = req.Phone
	body.Username = req.Username
	body.BankAccount = req.BankAccount
	body.BankId = req.BankId
	body.ChannelId = req.ChannelId
	body.TrueWallet = req.TrueWallet
	body.Contact = req.Contact
	body.Note = req.Note
	body.Course = req.Course
	body.LineId = req.LineId
	// body.Encrypt = req.Encrypt
	body.Ip = req.Ip

	if body.Phone != "" && body.Phone != user.Phone {
		otherUser, err := s.repo.CheckUserByPhone(body.Phone)
		if err != nil {
			return nil
		}
		if otherUser != nil {
			return badRequest(userExist)
		}
		body.Username = body.Phone
	}
	if body.BankAccount != "" && body.BankAccount != user.BankAccount {
		otherUser, err := s.repo.CheckUserByAccountNo(body.BankAccount)
		if err != nil {
			return nil
		}
		if otherUser != nil {
			return badRequest(userAccountNoExist)
		}
	}
	if body.BankAccount != "" && body.BankAccount != user.BankAccount {
		otherUser, err := s.repo.CheckUserByAccountNo(body.BankAccount)
		if err != nil {
			return nil
		}
		if otherUser != nil {
			return badRequest(userAccountNoExist)
		}
	}
	if body.Password != "" {

		// [ADMIN_ACTION] SUCCESS อัพเดท รหัสผ่านผู้ใช้งาน {name}
		displayName := ""
		if user.MemberCode != "" {
			displayName = user.MemberCode
		}
		if displayName == "" {
			displayName = user.Fullname
		}
		var adminActionCreateBody model.AdminActionCreateRequest
		adminActionCreateBody.AdminId = adminId
		adminActionCreateBody.TypeId = model.ADMIN_ACTION_MANAGE_USER
		adminActionCreateBody.Detail = fmt.Sprintf("อัพเดท รหัสผ่านผู้ใช้งาน %s", displayName)
		adminActionCreateBody.JsonInput = helper.StructJson(body)
		if _, err := s.adminAction.CreateSuccessAdminAction(adminActionCreateBody); err != nil {
			return nil
		}

		user, err := s.repo.GetUserById(userId)
		if err != nil {
			return internalServerError(err)
		}
		// check agent password
		// always update agent password ********
		// agentPassword := ""
		// if user.Encrypt != "" {
		// 	pass, err := helper.Decode(user.Encrypt)
		// 	if err != nil {
		// 		return internalServerError(err)
		// 	}
		// 	agentPassword = *pass
		// }
		// prevent same password for agent
		// if agentPassword != staticPasswordAgentGame {

		if user.MemberCode != nil {
			staticPasswordAgentGame := os.Getenv("AGENT_NAME") + "Aa01"
			// ResetAgentGamePassword(user model.UserResponse, staticPasswordAgentGame string) error
			if err := s.ResetAgentGamePassword(*user, req.Ip, staticPasswordAgentGame); err != nil {
				return internalServerError(err)
			}
			newEncrypt := helper.Encode(staticPasswordAgentGame)
			body.Encrypt = &newEncrypt
		}

		// Always update encrypt
		// body.Encrypt = helper.Encode(body.Password)
		// P.layer ******** แก้ให้ gen เองจาก API
		// }

		// Finally, update password with hashed
		newHashPasword, err := helper.GenUserPassword(body.Password)
		if err != nil {
			return internalServerError(err)
		}
		body.Password = newHashPasword
	}

	if err := s.repo.UpdateUser(userId, body); err != nil {
		return err
	} else {
		err := s.userUpdateLog(req, *user, adminName)
		if err != nil {
			log.Println("UpdateUser.userUpdateLog", err)
		}
	}

	var userlogInfo model.UserUpdateLogs
	userlogInfo.CreatedByUsername = adminName
	userlogInfo.Ip = req.Ip

	// [20240418] Check RefBy onChange
	if req.RefMemberCode != nil {
		userinfo, err := s.repo.GetUserInfo(userId)
		if err == nil && *req.RefMemberCode != userinfo.RefByMemberCode {
			if *req.RefMemberCode == "" {
				// Remove on Empty
				if err := s.removeUserRefby(userId, userlogInfo); err != nil {
					log.Println("UpdateUser.removeUserRefby", err)
					return err
				}
			} else {
				var changeRefByRequest model.MoveUserRefbyRequest
				changeRefByRequest.UserId = userId
				changeRefByRequest.NewRefByMemberCode = *req.RefMemberCode
				if err := s.moveUserRefby(changeRefByRequest, userlogInfo); err != nil {
					log.Println("UpdateUser.moveUserRefby", err)
					return err
				}
			}
		}
	}

	if adminName != "" {
		sysLogJsonInput["updateByAdminUserName"] = adminName
	}
	// [syslog]
	if sysLogId != nil {
		if sysLogErr := s.repo.SetSystemLogSuccess(model.SystemLogUpdateBody{
			Id:        *sysLogId,
			JsonInput: helper.StructJson(sysLogJsonInput),
			JsonReponse: helper.StructJson(map[string]interface{}{
				"insertId": user.Id,
			}),
		}); sysLogErr != nil {
			log.Println("AmbRegister.SetAgentLogSuccess error ------> ", sysLogErr.Error())
		}
	}

	// [ADMIN_ACTION] SUCCESS อัพเดท ข้อมูลผู้ใช้งาน {name}
	displayName := ""
	if user.MemberCode != "" {
		displayName = user.MemberCode
	}
	if displayName == "" {
		displayName = user.Fullname
	}
	var adminActionCreateBody model.AdminActionCreateRequest
	adminActionCreateBody.AdminId = adminId
	adminActionCreateBody.TypeId = model.ADMIN_ACTION_MANAGE_USER
	adminActionCreateBody.Detail = fmt.Sprintf("อัพเดท ข้อมูลผู้ใช้งาน %s", displayName)
	adminActionCreateBody.JsonInput = helper.StructJson(body)
	if _, err := s.adminAction.CreateSuccessAdminAction(adminActionCreateBody); err != nil {
		return nil
	}

	return nil
}

func (s userService) ResetAgentGamePassword(user model.UserResponse, ip string, staticPasswordAgentGame string) error {

	domainName := os.Getenv("DOMAIN_NAME")
	agentName := os.Getenv("AGENT_NAME")
	agentProvider := os.Getenv("AGENT_PROVIDER")

	if user.MemberCode != nil {
		actionAt := time.Now()
		if agentProvider == "AMB" {
			agentData := model.AmbChangePassword{
				Username: *user.MemberCode,
				Password: staticPasswordAgentGame,
			}
			if _, err := s.repo.AmbChangePassword(agentData); err != nil {
				return err
			}
		} else {
			sign := agentName + *user.MemberCode + staticPasswordAgentGame
			agentData := model.AgcChangePassword{
				PlayerName:  *user.MemberCode,
				Partner:     agentName,
				NewPassword: staticPasswordAgentGame,
				Timestamp:   actionAt.Unix(),
				Sign:        helper.CreateSign(os.Getenv("AGENT_KEY"), sign, actionAt),
			}
			if _, err := s.repo.AgcChangePassword(agentData); err != nil {
				return err
			}
		}
	}

	var response model.UserLoginResponse
	if agentProvider == "AMB" {
		agentData := model.AmbLogin{}
		agentData.Username = *user.MemberCode
		result, err := s.repo.AmbLogin(agentData)
		if err != nil {
			return internalServerError(err)
		}
		response.GameProvider = agentProvider
		response.GameToken = result.Url
	} else {
		sign := agentName + *user.MemberCode + staticPasswordAgentGame
		timeNow := time.Now()
		agentData := model.AgcLogin{}
		agentData.Username = *user.MemberCode
		agentData.Partner = agentName
		agentData.Timestamp = timeNow.Unix()
		agentData.Sign = helper.CreateSign(os.Getenv("AGENT_KEY"), sign, timeNow)
		agentData.Domain = domainName
		agentData.Lang = "th-th"
		agentData.IsMobile = false
		agentData.Ip = ip
		result, err := s.repo.AgcLogin(agentData)
		if err != nil {
			return internalServerError(err)
		}
		response.GameProvider = agentProvider
		response.GameToken = result.Token
	}

	return nil
}

func (s userService) moveUserRefby(req model.MoveUserRefbyRequest, userlogInfo model.UserUpdateLogs) error {

	var body model.MoveAffiliateUserMemberBody

	member, err := s.repo.GetUserForGenmemberByUserId(req.UserId)
	if err != nil {
		log.Println("moveUserRefby.GetUserForGenmemberByUserId", err)
		return err
	}
	body.MemberId = member.Id
	body.MemberCode = member.MemberCode

	if member.RefBy != nil && *member.RefBy != 0 {
		oldRefBy, err := s.repo.GetUserForGenmemberByUserId(*member.RefBy)
		if err != nil {
			log.Println("moveUserRefby.GetUserForGenmemberByUserId", err)
			if err != gorm.ErrRecordNotFound {
				return err
			}
		}
		if oldRefBy != nil {
			body.OldRefBy = oldRefBy.Id
			body.OldRefByMemberCode = oldRefBy.MemberCode
		}
	}
	newRefBy, err := s.repo.GetUserForGenmemberByMemberCode(req.NewRefByMemberCode)
	if err != nil {
		log.Println("moveUserRefby.GetUserForGenmemberByMemberCode", err)
		return err
	}
	if newRefBy.UserTypeId != model.USER_TYPE_AFFILIATE && newRefBy.UserTypeId != model.USER_TYPE_ALLIANCE {
		return badRequest("NEW_REF_BY_NOT_AFFILIATE")
	}
	body.NewRefBy = newRefBy.Id
	body.NewRefByMemberCode = newRefBy.MemberCode

	if body.MemberId == body.NewRefBy || body.MemberCode == body.NewRefByMemberCode {
		log.Println("moveUserRefby", "NOT_YOUR_SELF")
		return nil
	}
	if body.OldRefBy == body.NewRefBy {
		log.Println("moveUserRefby", "SAME_REF_BY")
		return nil
	}

	// ** Only Affiliate
	if err := s.repo.MoveAffiliateUserMember(body, userlogInfo); err != nil {
		log.Println("moveUserRefby.MoveAffiliateUserMember", err)
		return err
	}

	return nil
}

func (s userService) removeUserRefby(userId int64, userlogInfo model.UserUpdateLogs) error {

	var body model.MoveAffiliateUserMemberBody

	member, err := s.repo.GetUserForGenmemberByUserId(userId)
	if err != nil {
		log.Println("removeUserRefby.GetUserForGenmemberByUserId", err)
		return err
	}
	body.MemberId = member.Id
	body.MemberCode = member.MemberCode

	if member.RefBy != nil && *member.RefBy != 0 {
		oldRefBy, err := s.repo.GetUserForGenmemberByUserId(*member.RefBy)
		if err != nil {
			log.Println("removeUserRefby.GetUserForGenmemberByUserId", err)
			if err != gorm.ErrRecordNotFound {
				return err
			}
		}
		if oldRefBy != nil {
			body.OldRefBy = oldRefBy.Id
			body.OldRefByMemberCode = oldRefBy.MemberCode
		}
	}

	// ** Only Affiliate
	if err := s.repo.RemoveAffiliateUserMember(body, userlogInfo); err != nil {
		log.Println("removeUserRefby.MoveAffiliateUserMember", err)
		return err
	}
	return nil
}

func (s *userService) ResetPassword(userId int64, body model.UserUpdatePassword, adminId int64) error {

	user, err := s.repo.GetUserById(userId)
	if err != nil {
		return internalServerError(err)
	}

	if user.LoginType == "line" {
		return badRequest("ไม่สามรถเปลี่ยนรหัสผ่าน Line ได้")
	}

	if err := helper.CompareUserPassword(body.OldPassword, user.Password); err != nil {
		return badRequest(userPasswordNotMatch)
	}

	// [AGENT]
	// staticPasswordAgentGame := os.Getenv("AGENT_NAME") + "Aa01"
	// if user.MemberCode != nil {
	// 	// ResetAgentGamePassword(user model.UserResponse, staticPasswordAgentGame string) error
	// 	if err := s.ResetAgentGamePassword(*user, body.Ip, staticPasswordAgentGame); err != nil {
	// 		return internalServerError(err)
	// 	}
	// }

	// Always update encrypt
	// body.Encrypt = helper.Encode(body.NewPassword)
	// P.layer ******** แก้ให้ gen เองจาก API
	// passwordAgentGame := helper.Encode(staticPasswordAgentGame)
	// body.Encrypt = passwordAgentGame

	newHashPasword, err := helper.GenUserPassword(body.NewPassword)
	if err != nil {
		return internalServerError(err)
	}
	body.NewPassword = newHashPasword
	if err := s.repo.UpdateUserPassword(userId, body); err != nil {
		return err
	}

	if adminId > 0 {
		// [ADMIN_ACTION] SUCCESS เปลี่ยนรหัสผ่าน ข้อมูลผู้ใช้งาน {name}
		displayName := ""
		if user.MemberCode != nil {
			displayName = *user.MemberCode
		}
		if displayName == "" {
			displayName = user.Fullname
		}
		var adminActionCreateBody model.AdminActionCreateRequest
		adminActionCreateBody.AdminId = adminId
		adminActionCreateBody.TypeId = model.ADMIN_ACTION_MANAGE_USER
		adminActionCreateBody.Detail = fmt.Sprintf("เปลี่ยนรหัสผ่าน ข้อมูลผู้ใช้งาน %s", displayName)
		adminActionCreateBody.JsonInput = helper.StructJson(body)
		if _, err := s.adminAction.CreateSuccessAdminAction(adminActionCreateBody); err != nil {
			return nil
		}
	}

	return nil
}

func (s userService) FrontResetPassword(userId int64, body model.UserUpdatePasswordForFront) error {

	if os.Getenv("APP_ENV") != "local" {

		otp, err := s.repo.GetForgetOtpLogByOtpId(body.OtpId)
		if err != nil {
			return internalServerError(err)
		}
		if otp == nil {
			return notFound(userOtpNotMatch)
		}
		if otp.VerifiedAt != nil {
			return badRequest(userOtpUsed)
		}

		// Verify Otp by Provider
		cyberOtpKey := s.otpRepo.GetCyberOtpKey()
		if cyberOtpKey != "" {
			// OTP from our service
			var cyberbody model.VerifyCyberOtpRequest
			cyberbody.OtpId = body.OtpId
			cyberbody.OtpCode = body.Code
			result, err := s.otpRepo.VerifyCyberOtp(cyberbody)
			if err != nil {
				log.Println(err)
				return internalServerError(err)
			}
			if strings.ToLower(result.Message) != "success" {
				log.Println(result.Message)
				return badRequest(result.Message)
			}
		} else {
			verifyOtpData := model.UserVerifyOtpBody{
				OtpId: body.OtpId,
				Code:  body.Code,
			}
			result, err := s.otpRepo.VerifyOtp(verifyOtpData)
			if err != nil {
				return internalServerError(err)
			}
			if result != nil {
				if !result.Result || result.IsErrorCount {
					return badRequest(userOtpNotMatch)
				}
				if result.IsExprCode {
					return badRequest(userOtpExpired)
				}
			}
		}
	}

	// Check User
	user, err := s.repo.GetUserById(userId)
	if err != nil {
		return internalServerError(err)
	}
	if user == nil {
		return notFound("USER_NOT_FOUND")
	}
	if !user.IsResetPassword {
		return badRequest(frontUserPasswordIsReset)
	}

	// [AGENT]
	// encrypt := ""
	// staticPasswordAgentGame := os.Getenv("AGENT_NAME") + "Aa01"
	// encrypt := helper.Encode(staticPasswordAgentGame)

	// if user.MemberCode != nil {
	// 	agentName := os.Getenv("AGENT_NAME")
	// 	timeNow := time.Now()
	// 	// encrypt = helper.Encode(body.Password)
	// 	// P.layer ******** แก้ให้ gen เองจาก API

	// 	agentProvider := os.Getenv("AGENT_PROVIDER")
	// 	if agentProvider == "AMB" {
	// 		agentData := model.AmbChangePassword{
	// 			Username: *user.MemberCode,
	// 			Password: staticPasswordAgentGame,
	// 		}
	// 		if _, err = s.repo.AmbChangePassword(agentData); err != nil {
	// 			return err
	// 		}
	// 	} else {
	// 		sign := agentName + *user.MemberCode + staticPasswordAgentGame
	// 		agentData := model.AgcChangePassword{
	// 			PlayerName:  *user.MemberCode,
	// 			Partner:     agentName,
	// 			NewPassword: staticPasswordAgentGame,
	// 			Timestamp:   timeNow.Unix(),
	// 			Sign:        helper.CreateSign(os.Getenv("AGENT_KEY"), sign, timeNow),
	// 		}
	// 		if _, err = s.repo.AgcChangePassword(agentData); err != nil {
	// 			return err
	// 		}
	// 	}
	// }

	newPasword, err := helper.GenUserPassword(body.Password)
	if err != nil {
		return internalServerError(err)
	}

	if err := s.repo.FrontUpdatePassword(userId, newPasword); err != nil {
		return err
	}

	return nil
}

func (s *userService) GetBankList() (*model.UserBankListResponse, error) {

	banks, err := s.repo.GetBankList()
	if err != nil {
		return nil, internalServerError(err)
	}

	var result model.UserBankListResponse
	for _, v := range banks {
		if v.Id != model.UNKNOWN_BANK {
			result.List = append(result.List, v)
		}
	}

	return &result, nil
}

func (s *userService) DeleteUser(id int64, adminId int64) error {

	user, err := s.repo.GetUserById(id)
	if err != nil {
		return internalServerError(err)
	}
	if user == nil {
		return notFound(userNotFound)
	}

	if err := s.repo.DeleteUser(*user); err != nil {
		return internalServerError(err)
	}

	name := ""
	if user.MemberCode != nil {
		name = *user.MemberCode
	}
	if name == "" {
		name = user.Fullname
	}

	// [ADMIN_ACTION] SUCCESS ลบ ข้อมูลผู้ใช้งาน {name}
	var adminActionCreateBody model.AdminActionCreateRequest
	adminActionCreateBody.AdminId = adminId
	adminActionCreateBody.TypeId = model.ADMIN_ACTION_MANAGE_USER
	adminActionCreateBody.Detail = fmt.Sprintf("ลบ ข้อมูลผู้ใช้งาน %s", name)
	adminActionCreateBody.JsonInput = helper.StructJson(id)
	if _, err := s.adminAction.CreateSuccessAdminAction(adminActionCreateBody); err != nil {
		return nil
	}

	return nil
}

func (s *userService) GenUniqueUserMemberCode(userId int64) (*string, error) {

	genMemberCode, err := GenUniqueUserMemberCode(s.repo, userId)
	if err != nil {
		return nil, err
	}

	return genMemberCode, nil
}

func GenUniqueUserMemberCode(repo repository.UserRepository, userId int64) (*string, error) {

	actionAtUtc := time.Now().UTC()

	user, err := repo.GetUserForGenMember(userId)
	if err != nil {
		return nil, err
	}
	if user.Encrypt == "" {
		// ต้องมีรหัสผ่านก่อน
		passwordAgentGame := helper.Encode(os.Getenv("AGENT_NAME") + "Aa01")
		user.Encrypt = passwordAgentGame
	}
	passDecode, err := helper.Decode(user.Encrypt)
	if err != nil {
		return nil, err
	}

	// RaceCondition ป้องกันการสร้างสมาชิกซ้ำ แล้วฝากไม่เข้าเพราะ ไปเข้า membercode เดิมแต่ user ปัจจุบันใช้ membercode ใหม่
	// RACE_ACTION per user per hour if re-gen
	debugResult := make(map[string]interface{}, 0)
	var createBody model.CronActionCreateBody
	createBody.Status = "PENDING"
	// KEY = GEN_UMEM_{userId}_{YYMMDDHH}
	createBody.ActionKey = fmt.Sprintf("GEN_UMEM_%d_%s", userId, actionAtUtc.Format("0601021504"))
	createBody.UnlockAt = actionAtUtc.Add(time.Minute * 60)
	if _, err := repo.GetCronActionByActionKey(createBody.ActionKey); err != nil {
		if err != gorm.ErrRecordNotFound {
			return nil, badRequest("CANT_CHECK_GEN_MEMBER_CODE")
		}
	} else {
		return nil, badRequest("USER_ALREADY_GEN_MEMBER_CODE")
	}
	actionId, err := repo.CreateCronAction(createBody)
	if err != nil {
		log.Println("CronCalcReturnTurn.ERROR.CreateCronAction", err)
		return nil, badRequest("CANT_CHECK_GEN_MEMBER_CODE")
	}

	// =============================
	agentInfo, err := repo.GetAgentInfo()
	if err != nil {
		return nil, err
	}
	// Set Request
	conv := strconv.FormatInt(agentInfo.Total, 10)
	agentName := os.Getenv("AGENT_NAME")
	memberNumber := helper.IncrementNumber(conv) // zta68pk + 000001
	memberCode := fmt.Sprintf("%s%s", agentName, memberNumber)
	// Inceased Total First, later : use queue.
	if err := repo.IncrementTotal(); err != nil {
		return nil, err
	}

	agentProvider := os.Getenv("AGENT_PROVIDER")
	if agentProvider == "AMB" {
		agentData := model.AmbRegister{
			Username: memberCode,
			Password: *passDecode,
			Name:     user.Fullname,
		}
		if err := repo.AmbRegister(agentData); err != nil {
			return nil, err
		}
	} else {
		// DEFAULT : Agc/Cb
		sign := agentName + memberCode
		agentData := model.AgcRegister{
			Username:  memberCode,
			Agentname: agentName,
			Fullname:  user.Fullname,
			Password:  *passDecode,
			Currency:  "THB",
			Dob:       "1990-01-01",
			Mobile:    user.Phone,
			Ip:        user.IpRegistered,
			Timestamp: actionAtUtc.Unix(),
			Sign:      helper.CreateSign(os.Getenv("AGENT_KEY"), sign, actionAtUtc),
		}
		if err := repo.AgcRegister(agentData); err != nil {
			return nil, err
		}
	}

	var update model.UserUpdateMemberAndRef
	update.MemberCode = memberCode
	update.Encrypt = user.Encrypt
	if err := repo.UpdateMemberCode(userId, update); err != nil {
		return nil, err
	}

	debugResult["update_data"] = update

	// RACE_ACTION = SUCCESS
	if err := repo.SetSuccessCronAction(actionId, helper.StructJson(debugResult)); err != nil {
		log.Println("CronCalcReturnTurn.ERROR.SetSuccessCronAction", err)
	}

	return &memberCode, nil
}

func (s *userService) GenMemberCodeAndAffilate(userId int64) (*string, error) {

	// set ให้ genmembercode กให้เป็น defualt ค่า affiliate เลยหลังจาก genmembercode แล้ว
	user, err := s.repo.GetUserForGenmemberByUserId(userId)
	if err != nil {
		return nil, err
	}

	var newMemberCode *string
	if user.MemberCode == "" {
		memberCode, err := s.GenUniqueUserMemberCode(userId)
		if err != nil {
			return nil, err
		}
		newMemberCode = memberCode
	} else {
		return nil, badRequest("USER_ALREADY_HAVE_MEMBER_CODE")
	}

	// SET BOTH MEMBER CODE AND AFFILIATE แหก !
	if user.UserTypeId == model.USER_TYPE_NONE {
		if err := s.repo.UpdateGenMemberCodeToAffilate(userId); err != nil {
			return nil, err
		}
		if user.RefBy != nil && *user.RefBy != 0 {
			if err := s.repo.CreateAffiliateMember(*user.RefBy, user.Id); err != nil {
				return nil, internalServerError(err)
			}
		}
	}

	return newMemberCode, nil
}

func (s *userService) GenMissingUserAffilate(userId int64) (*model.CheckMissingAffiliateReponse, error) {

	// recheck User if have deposit but not have affiliate (user_type = NONE)
	var result model.CheckMissingAffiliateReponse

	user, err := s.repo.GetUserForGenmemberByUserId(userId)
	if err != nil {
		return nil, err
	}
	result.UserId = userId
	result.MemberCode = user.MemberCode
	result.UserTypeId = user.UserTypeId

	depositCount, err := s.repo.CountUserTransactionDeposit(userId)
	if err != nil {
		return nil, err
	}
	result.DepositCount = depositCount

	if user.UserTypeId == model.USER_TYPE_NONE {
		// Get User Deposit
		if depositCount > 0 {
			if err := s.repo.UpdateGenMemberCodeToAffilate(userId); err != nil {
				return nil, err
			}
			if user.RefBy != nil && *user.RefBy != 0 {
				if err := s.repo.CreateAffiliateMember(*user.RefBy, user.Id); err != nil {
					return nil, internalServerError(err)
				}
			}
		}
	} else {
		// recheck
		if err := s.repo.CreateMissingAffiliate(userId); err != nil {
			return nil, err
		}
	}
	return &result, nil
}

func (s userService) calCommissionRegister() (float64, error) {

	data, err := s.afRepo.GetCommissionSetting()
	if err != nil {
		return 0, internalServerError(err)
	}

	var commission float64 = data.ReferralBonus

	return commission, nil
}

func (s *userService) GetInactiveUserList(query model.InactiveUserListRequest) ([]model.InactiveUserResponse, int64, error) {

	if err := helper.Pagination(&query.Page, &query.Limit); err != nil {
		return nil, 0, badRequest(err.Error())
	}

	list, total, err := s.repo.GetInactiveUserList(query)
	if err != nil {
		return nil, 0, internalServerError(err)
	}
	return list, total, nil
}

func (s *userService) RemoveInactiveUser(query model.InactiveUserRemoveListRequest) error {

	if len(query.SelectedIds) > 0 {
		successCount := 0
		fmt.Println(query.SelectedIds)
		for _, v := range query.SelectedIds {
			fmt.Println("v", v)
			if err := s.repo.RemoveInactiveUserById(v, query.DateType); err != nil {
				log.Println("RemoveInactiveUserById", err)
			} else {
				successCount++
			}
		}

		// [ADMIN_ACTION] SUCCESS {name} ลบข้อมูลสมาชิกที่ไม่มีการเคลื่อนไหว
		var adminActionCreateBody model.AdminActionCreateRequest
		adminActionCreateBody.AdminId = query.AdminId
		adminActionCreateBody.TypeId = model.ADMIN_ACTION_MANAGE
		adminActionCreateBody.Detail = fmt.Sprintf("ลบข้อมูลสมาชิกที่ไม่มีการเคลื่อนไหว %d รายการ", successCount)
		adminActionCreateBody.JsonInput = helper.StructJson(query)
		if _, err := s.adminAction.CreateSuccessAdminAction(adminActionCreateBody); err != nil {
			return nil
		}
	}
	return nil
}

func (s *userService) ExportInactiveUserXlsx(c *gin.Context, query model.InactiveUserListForExcelRequest) error {

	list, _, err := s.repo.GetInactiveUserListForExcel(query)
	if err != nil {
		return err
	}

	file := xlsx.NewFile()
	sheet, err := file.AddSheet("sheet")
	if err != nil {
		return err
	}

	// ORDER BY Field name
	row1 := sheet.AddRow()
	row1.AddCell().Value = "Id"
	row1.AddCell().Value = "ชื่อผู้ใช้"
	row1.AddCell().Value = "เครดิต"
	row1.AddCell().Value = "เวลาที่เคลื่อนไหวล่าสุด"
	row1.AddCell().Value = "จำนวนวันที่ไม่เคลื่อนไหว"

	for _, v := range list {
		row2 := sheet.AddRow()
		row2.AddCell().Value = strconv.FormatInt(v.Id, 10)
		row2.AddCell().Value = v.Username
		row2.AddCell().Value = strconv.FormatFloat(v.Credit, 'f', 2, 64)
		row2.AddCell().Value = v.LastActionAt.Format("2006-01-02 15:04:05")
		row2.AddCell().Value = strconv.FormatInt(v.LastActionDays, 10)
	}

	var b bytes.Buffer
	if err := file.Write(&b); err != nil {
		return err
	}

	downloadName := ("InactiveUserList.xlsx")
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Disposition", "attachment; filename="+downloadName)
	c.Data(http.StatusOK, "application/octet-stream", b.Bytes())

	return nil
}

func (s *userService) UserWithdrawSettingCreate(req model.UserWithdrawSettingCreateRequest, adminId int64) (int64, error) {

	user, err := s.repo.GetUserById(req.UserId)
	if err != nil {
		return 0, internalServerError(err)
	}
	if user == nil {
		return 0, notFound(userNotFound)
	}

	createdId, err := s.repo.UserWithdrawSettingCreate(req)
	if err != nil {
		return 0, err
	}

	name := ""
	if user.MemberCode != nil {
		name = *user.MemberCode
	}
	if name == "" {
		name = user.Fullname
	}

	// [ADMIN_ACTION] SUCCESS ตั้งค่าการถอนเงิน ผู้ใช้งาน {name}
	var adminActionCreateBody model.AdminActionCreateRequest
	adminActionCreateBody.AdminId = adminId
	adminActionCreateBody.TypeId = model.ADMIN_ACTION_MANAGE_USER
	adminActionCreateBody.Detail = fmt.Sprintf("ตั้งค่าการถอนเงิน ผู้ใช้งาน %s", name)
	adminActionCreateBody.JsonInput = helper.StructJson(req)
	if _, err := s.adminAction.CreateSuccessAdminAction(adminActionCreateBody); err != nil {
		log.Println("UserWithdrawSettingCreate.CreateSuccessAdminAction", err)
	}

	return createdId, nil
}

func (s *userService) UserWithdrawSettingUpdateByUserId(req model.UserWithdrawSettingUpdateByUserIdRequest, adminId int64) error {

	user, err := s.repo.GetUserById(req.UserId)
	if err != nil {
		return internalServerError(err)
	}
	if user == nil {
		return notFound(userNotFound)
	}

	result, err := s.repo.UserWithdrawSettingGetByUserId(req.UserId)
	if err != nil {

		pertime := "withdraw_maximum_time_per_day"
		getConfigpertime, err := s.repo.UserWithdrawConfigGetByKey(pertime)
		if err != nil {
			return err
		}

		accumlated := "withdraw_accumulated_amount"
		getConfigaccumlated, err := s.repo.UserWithdrawConfigGetByKey(accumlated)
		if err != nil {
			return err
		}

		var createReq model.UserWithdrawSettingCreateRequest
		if req.AccumulatedAmount != nil {
			createReq.AccumulatedAmount = *req.AccumulatedAmount
		} else {
			createReq.AccumulatedAmount = getConfigaccumlated.ConfigValue
		}
		if req.MaximumTimePerDay != nil {
			createReq.MaximumTimePerDay = *req.MaximumTimePerDay
		} else {
			createReq.MaximumTimePerDay = getConfigpertime.ConfigValue
		}
		createReq.UserId = req.UserId
		if _, err := s.repo.UserWithdrawSettingCreate(createReq); err != nil {
			return err
		}
	}

	if result != nil {
		if err := s.repo.UserWithdrawSettingUpdateByUserId(req); err != nil {
			return err
		}
		return nil
	}

	name := ""
	if user.MemberCode != nil {
		name = *user.MemberCode
	}
	if name == "" {
		name = user.Fullname
	}

	// [ADMIN_ACTION] SUCCESS อัพเดท ตั้งค่าการถอนเงิน ผู้ใช้งาน {name}
	var adminActionCreateBody model.AdminActionCreateRequest
	adminActionCreateBody.AdminId = adminId
	adminActionCreateBody.TypeId = model.ADMIN_ACTION_MANAGE_USER
	adminActionCreateBody.Detail = fmt.Sprintf("อัพเดท ตั้งค่าการถอนเงิน ผู้ใช้งาน %s", name)
	adminActionCreateBody.JsonInput = helper.StructJson(req)
	if _, err := s.adminAction.CreateSuccessAdminAction(adminActionCreateBody); err != nil {
		log.Println("UserWithdrawSettingCreate.CreateSuccessAdminAction", err)
	}

	return nil
}

func (s *userService) UserWithdrawSettingGetById(req model.UserWithdrawSettingGetByRequest) (*model.UserWithdrawSettingGetByUserIdResponse, error) {

	result, err := s.repo.UserWithdrawSettingGetById(req.Id)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (s *userService) UserWithdrawSettingGetByUserId(req model.UserWithdrawSettingGetByUserIdRequest) (*model.UserWithdrawSettingGetByUserIdResponse, error) {

	result, err := s.repo.UserWithdrawSettingGetByUserId(req.UserId)
	if err != nil {
		// UserWithdrawConfigGetByKey(key string) (*model.UserWithdrawConfig, error)
		// withdraw_accumulated_amount
		//withdraw_maximum_time_per_day
		pertime := "withdraw_maximum_time_per_day"
		getConfigpertime, err := s.repo.UserWithdrawConfigGetByKey(pertime)
		if err != nil {
			return nil, err
		}

		accumlated := "withdraw_accumulated_amount"
		getConfigaccumlated, err := s.repo.UserWithdrawConfigGetByKey(accumlated)
		if err != nil {
			return nil, err
		}

		// UserWithdrawSettingCreate(req model.UserWithdrawSettingCreateRequest) (int64, error)
		var createReq model.UserWithdrawSettingCreateRequest
		createReq.UserId = req.UserId
		createReq.AccumulatedAmount = getConfigaccumlated.ConfigValue
		createReq.MaximumTimePerDay = getConfigpertime.ConfigValue
		createInsertedId, err := s.repo.UserWithdrawSettingCreate(createReq)
		if err != nil {
			return nil, err
		}

		if createInsertedId > 0 {
			result, err := s.repo.UserWithdrawSettingGetById(createInsertedId)
			if err != nil {
				return nil, err
			}
			return result, nil
		}

	} else {
		return result, nil
	}

	return result, nil
}

func (s userService) GetUserChannelSummaryGraph(req model.UserChannelSummaryGraphRequest) (*model.UserChannelSummaryGraphResponse, error) {

	var result model.UserChannelSummaryGraphResponse

	userData, err := s.repo.GetUserChannelListGraph(req)
	if err != nil {
		return nil, err
	}
	// fmt.Println("userData", helper.StructJson(userData))

	refChannel := make(map[int64]model.Recommend)
	title1 := "พันธมิตร"
	refChannel[3] = model.Recommend{
		Id:         3,
		Title:      &title1,
		GraphColor: "#2D3642",
	}
	title2 := "ลิงก์รับทรัพย์"
	refChannel[2] = model.Recommend{
		Id:         2,
		Title:      &title2,
		GraphColor: "#67C6E3",
	}

	userRefData, err := s.repo.GetUserRefChannelListGraph(req)
	if err != nil {
		return nil, err
	}
	// fmt.Println("userRefData", helper.StructJson(userRefData))

	userChannel := make(map[int64]model.Recommend)
	userChannelList, _, err := s.repo.GetRecommendList(model.RecommendQuery{})
	if err != nil {
		return nil, err
	}
	for _, v := range userChannelList {
		userChannel[v.Id] = model.Recommend{
			Id:         v.Id,
			Title:      v.Title,
			GraphColor: v.GraphColor,
		}
	}

	// const labels: string[] = ['2024-04-04', '2024-04-05', '2024-04-06']

	// const datasets: Dataset[] = [
	//
	//	{
	//	  label: 'พันธมิตร',
	//	  data: [10, 20, 55],
	//	  backgroundColor: '#2D3642',
	//	},
	//	{
	//	  label: 'ลิงก์รับทรัพย์',
	//	  data: [12, 30, 22],
	//	  backgroundColor: '#67C6E3',
	//	},
	//	{
	//	  label: 'Line',
	//	  data: [5, 25, 15],
	//	  backgroundColor: '#06C755',
	//	},
	//	{
	//	  label: 'Youtube',
	//	  data: [5, 15, 20],
	//	  backgroundColor: '#CE1A19',
	//	},
	//
	// ]

	dateType, err := repository.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.FromDate,
		DateTo:   req.ToDate,
	})
	if err != nil {
		return nil, err
	}

	fromDate, err := time.Parse("2006-01-02", dateType.DateFrom)
	if err != nil {
		return nil, err
	}
	toDate, err := time.Parse("2006-01-02", dateType.DateTo)
	if err != nil {
		return nil, err
	}

	// SELECT  * FROM user AS tb_user WHERE   tb_user.created_at >= '2024-04-07 17:00:00'  AND tb_user.created_at <=  '2024-04-08 16:59:59'
	fromDateInChan := fromDate
	for {
		if fromDateInChan.After(toDate) {
			break
		}
		result.DateList = append(result.DateList, fromDateInChan.Format("2006-01-02"))
		fromDateInChan = fromDateInChan.AddDate(0, 0, 1)
	}
	// fmt.Println("fromDate", helper.StructJson(fromDate))
	// fmt.Println("toDate", helper.StructJson(toDate))

	// if len(userData) > 0 {
	// map[channelId]map[date] = userCount
	tempRefData := make(map[int64]map[string]int64)
	for _, v := range userRefData {
		if _, ok := tempRefData[v.ChannelId]; !ok {
			tempRefData[v.ChannelId] = make(map[string]int64)
		}
		tempRefData[v.ChannelId][v.OfDate] = v.UserCount
	}
	// fmt.Println("tempRefData", helper.StructJson(tempRefData))
	for chanId, dateList := range tempRefData {
		if chanInfo, ok := refChannel[chanId]; ok {
			userCountList := make([]int64, 0)
			fromDateInChan := fromDate
			for {
				if fromDateInChan.After(toDate) {
					break
				}
				// ZERO Filled
				if _, ok := dateList[fromDateInChan.Format("2006-01-02")]; ok {
					userCountList = append(userCountList, dateList[fromDateInChan.Format("2006-01-02")])
				} else {
					userCountList = append(userCountList, 0)
				}
				fromDateInChan = fromDateInChan.AddDate(0, 0, 1)
			}
			// set resp
			result.ChannelList = append(result.ChannelList, model.UserChannelGraphChannel{
				ChannelId:     chanId,
				ChannelName:   *chanInfo.Title,
				UserCountList: userCountList,
				ChannelColor:  chanInfo.GraphColor,
			})
		}
	}

	tempData := make(map[int64]map[string]int64)
	for _, v := range userData {
		if _, ok := tempData[v.ChannelId]; !ok {
			tempData[v.ChannelId] = make(map[string]int64)
		}
		tempData[v.ChannelId][v.OfDate] = v.UserCount
	}
	// fmt.Println("tempData", helper.StructJson(tempData))

	for chanId, dateList := range tempData {
		if chanInfo, ok := userChannel[chanId]; ok {
			userCountList := make([]int64, 0)
			fromDateInChan := fromDate
			for {
				if fromDateInChan.After(toDate) {
					break
				}
				// ZERO Filled
				if _, ok := dateList[fromDateInChan.Format("2006-01-02")]; ok {
					userCountList = append(userCountList, dateList[fromDateInChan.Format("2006-01-02")])
				} else {
					userCountList = append(userCountList, 0)
				}
				fromDateInChan = fromDateInChan.AddDate(0, 0, 1)
			}
			// set resp
			result.ChannelList = append(result.ChannelList, model.UserChannelGraphChannel{
				ChannelId:     chanId,
				ChannelName:   *chanInfo.Title,
				UserCountList: userCountList,
				ChannelColor:  chanInfo.GraphColor,
			})
		} else if chanId == 0 {
			userCountList := make([]int64, 0)
			fromDateInChan := fromDate
			for {
				if fromDateInChan.After(toDate) {
					break
				}
				// ZERO Filled
				if _, ok := dateList[fromDateInChan.Format("2006-01-02")]; ok {
					userCountList = append(userCountList, dateList[fromDateInChan.Format("2006-01-02")])
				} else {
					userCountList = append(userCountList, 0)
				}
				fromDateInChan = fromDateInChan.AddDate(0, 0, 1)
			}
			// set resp
			result.ChannelList = append(result.ChannelList, model.UserChannelGraphChannel{
				ChannelId:     chanId,
				ChannelName:   "สมัครปกติ",
				UserCountList: userCountList,
				ChannelColor:  "#f59e0b",
			})
		}
	}

	return &result, nil
}

func (s userService) LogAdmin(name string, adminId int64, req interface{}) error {

	var createBody model.AdminLogCreateBody
	createBody.Name = name
	createBody.AdminId = adminId
	createBody.JsonReq = helper.StructJson(req)
	if _, err := s.repo.CreateAdminLog(createBody); err != nil {
		return internalServerError(err)
	}
	return nil
}

func (s *userService) CreateTurnOverFromAffNewRegister(userId int64, bonusAmount float64, turnoverRefId int64) error {

	// GetTurnoverSetting() (*model.TurnoverSettingResponse, error)
	// serviceTurnover
	// GetTurnoverSetting(repo repository.TurnoverRepository) (*model.TurnoverSettingResponse, error)

	checkTurn, err := GetTurnoverSetting(repository.NewTurnoverRepository(s.shareDb))
	if err != nil {
		return nil
	}
	if checkTurn.TidturnLinkRegisterPercent > 0 {

		tidTurn := (bonusAmount * float64(checkTurn.TidturnManualBonusPercent) / 100)

		tidTurn = math.Ceil(tidTurn)

		createdTime := time.Now().UTC()
		var createBody model.TurnoverUserStatementCreateBody
		createBody.UserId = userId
		createBody.RefTypeId = turnoverRefId
		createBody.TypeId = model.TURN_BONUS_AFF_TYPE_NEW_REGISTER
		createBody.Name = model.TURNOVER_CATE_AFF_TYPE_NEW_REGISTER
		createBody.PromotionName = model.TURNOVER_CATE_AFF_TYPE_NEW_REGISTER
		createBody.BonusAmount = bonusAmount
		createBody.StatusId = model.TURNOVER_STATEMENT_STATUS_PENDING
		createBody.StartTurnAmount = tidTurn
		createBody.StartTurnAt = &createdTime
		createBody.TotalTurnAmount = tidTurn
		if _, err := s.repo.CreateTurnoverUserStatement(createBody); err != nil {
			return err
		}
	} else {
		createdTime := time.Now().UTC()
		var createBody model.TurnoverUserStatementCreateBody
		createBody.UserId = userId
		createBody.RefTypeId = turnoverRefId
		createBody.TypeId = model.TURN_BONUS_AFF_TYPE_NEW_REGISTER
		createBody.Name = model.TURNOVER_CATE_AFF_TYPE_NEW_REGISTER
		createBody.PromotionName = model.TURNOVER_CATE_AFF_TYPE_NEW_REGISTER
		createBody.BonusAmount = 0
		createBody.StatusId = model.TURNOVER_STATEMENT_STATUS_PENDING
		createBody.StartTurnAmount = 0
		createBody.StartTurnAt = &createdTime
		createBody.TotalTurnAmount = 0
		craeteTurnId, err := s.repo.CreateTurnoverUserStatement(createBody)
		if err != nil {
			return err
		}

		var updateTurnoverUserStatement model.TurnoverUserStatementUpdateBody
		setTotalTurnAmount := 0.0
		setTimeTurnAt := time.Now().UTC()
		updateTurnoverUserStatement.StatusId = model.TURNOVER_STATEMENT_STATUS_COMPLETED
		updateTurnoverUserStatement.TotalTurnAmount = &setTotalTurnAmount
		updateTurnoverUserStatement.EndTurnAt = &setTimeTurnAt
		if err := s.repo.UpdateTurnoverUserStatement(*craeteTurnId, updateTurnoverUserStatement); err != nil {
			log.Println("UpdateTurnoverUserStatement", err)
		}

		// create turnover withdraw log
		var createTurnoverWithDrawLog model.CreateTurnoverUserWithdrawLog
		createTurnoverWithDrawLog.UserId = userId
		createTurnoverWithDrawLog.LogKey = fmt.Sprintf("BONUS_AFF_REGISTER_U%d_D%s", userId, time.Now().UTC().Format("20060102150405"))
		createTurnoverWithDrawLog.TotalWithdrawPrice = 0
		createTurnoverWithDrawLog.CurrentTurn = 0
		createTurnoverWithDrawLog.PlayTotal = 0
		createTurnoverWithDrawLog.LastPlayY = 0
		createTurnoverWithDrawLog.LastTotalX = 0
		if _, err = s.repo.CreateTurnoverUserWithdrawLog(createTurnoverWithDrawLog); err != nil {
			log.Println("CheckCouponTurnOverWithdraw.CreateTurnoverUserWithdrawLog", err)
		}

	}

	return nil
}

func (s userService) GetAgentCallbackPlayLogList(req model.AgentCallbackPlayLogListRequest) (interface{}, error) {

	if req.AgentGameProviderId == 3 {
		// CTW
		if err := helper.Pagination(&req.Page, &req.Limit); err != nil {
			return nil, err
		}

		list, total, err := s.repo.GetAgentCtwCallbackPlayLogList(req)
		if err != nil {
			return nil, err
		}

		return &model.SuccessWithPagination{
			Total: total,
			List:  list,
		}, nil
	}

	if req.AgentGameProviderId == 2 {
		// PGHARD
		if err := helper.Pagination(&req.Page, &req.Limit); err != nil {
			return nil, err
		}

		list, total, err := s.repo.GetAgentPgHardCallbackPlayLogList(req)
		if err != nil {
			return nil, err
		}

		return &model.SuccessWithPagination{
			Total: total,
			List:  list,
		}, nil
	}

	// if req.AgentGameProviderId == 1 {
	// 	// PGSOFT
	// 	if req.UserId == nil {
	// 		return nil, badRequest("USER_ID_REQUIRED")
	// 	}
	// 	user, err := s.repo.FrontGetUser(*req.UserId)
	// 	if err != nil {
	// 		if err.Error() == recordNotFound {
	// 			return nil, notFound(userNotFound)
	// 		}
	// 		return nil, err
	// 	}
	// 	agentName := os.Getenv("AGENT_NAME")
	// 	// password, err := helper.Decode(user.Encrypt)
	// 	// if err != nil {
	// 	// 	return nil, internalServerError(err)
	// 	// }
	// 	timeNow := time.Now()
	// 	sign := agentName
	// 	// 89586
	// 	var agentDataWinLose model.AgcWinloseReportRequest
	// 	agentDataWinLose.StartDate = req.FromDate
	// 	agentDataWinLose.EndDate = req.ToDate
	// 	agentDataWinLose.PageSize = req.Limit
	// 	// agentDataWinLose.PageIndex = req.Page
	// 	agentDataWinLose.MemberName = os.Getenv("AGENT_NAME")
	// 	agentDataWinLose.AgentName = os.Getenv("AGENT_NAME")
	// 	agentDataWinLose.PlayerName = user.MemberCode
	// 	agentDataWinLose.Products = []int{1, 2, 3, 4, 6, 7}
	// 	agentDataWinLose.Currency = ""
	// 	agentDataWinLose.AgentCurrency = true
	// 	agentDataWinLose.TimeStamp = timeNow.Unix()
	// 	agentDataWinLose.Sign = helper.CreateSign(os.Getenv("AGENT_KEY"), sign, timeNow)
	// 	fmt.Println("AGC.agentDataWinLose", helper.StructJson(agentDataWinLose))
	// list, errgame := s.repo.AgcWinloseReport(agentDataWinLose)
	// if errgame != nil {
	// 	return nil, err
	// }

	// 	var allDetails []model.AgcWinloseReportDetail
	// 	for _, record := range list.Result.Records {
	// 		allDetails = append(allDetails, record.Details...)
	// 	}
	// 	return &model.SuccessWithPagination{
	// 		Total: list.Result.Total,
	// 		List:  allDetails,
	// 	}, nil
	// }

	if req.AgentGameProviderId == 1 {
		// PGSOFT
		if req.UserId == nil {
			return nil, badRequest("USER_ID_REQUIRED")
		}
		_, err := s.repo.FrontGetUser(*req.UserId)
		if err != nil {
			if err.Error() == recordNotFound {
				return nil, notFound(userNotFound)
			}
			return nil, err
		}
		agentName := os.Getenv("AGENT_NAME")
		// password, err := helper.Decode(user.Encrypt)
		// if err != nil {
		// 	return nil, internalServerError(err)
		// }
		timeNow := time.Now()
		sign := agentName
		// type AgcTicketsFetchPagingRequest struct {
		// 	FromDate  string `json:"FromDate"`
		// 	ToDate    string `json:"ToDate"`
		// 	Metadata  string `json:"Metadata"`
		// 	PageSize  int    `json:"PageSize"`
		// 	Partner   string `json:"Partner"`
		// 	TimeStamp int64  `json:"TimeStamp"`
		// 	Sign      string `json:"sign"`
		// }

		var agentDataWinLose model.AgcTicketsFetchPagingRequest
		agentDataWinLose.FromDate = req.FromDate
		agentDataWinLose.ToDate = req.ToDate
		agentDataWinLose.PageSize = req.Limit
		agentDataWinLose.Partner = agentName
		agentDataWinLose.TimeStamp = timeNow.Unix()
		agentDataWinLose.Sign = helper.CreateSign(os.Getenv("AGENT_KEY"), sign, timeNow)
		fmt.Println("AGC.agentDataWinLose", helper.StructJson(agentDataWinLose))
		list, errgame := s.repo.AgcTicketReport(agentDataWinLose)
		if errgame != nil {
			return nil, err
		}
		return &model.SuccessWithPagination{
			Total: 0,
			List:  list,
		}, nil
	}

	return nil, nil
}
