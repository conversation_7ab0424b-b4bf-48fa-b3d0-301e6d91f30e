package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"encoding/json"
	"fmt"
	"log"
	"strings"
	"time"

	"gorm.io/gorm"
)

type PapayaPayService interface {
	// PapayaPay
	CreatePapayaPayFundInWebhook(req model.PapayaPayWebhookRequest) (*int64, error)
	CreatePapayaPayFundOutWebhook(req model.PapayaPayWebhookRequest) (*int64, error)
	GetPapayaPayWebDepositAccount() (*model.PapayaPayCustomerDepositInfo, error)
	CreatePapayaPayDeposit(req model.PapayaPayDepositCreateRequest) (*model.PapayaPayOrderWebResponse, error)
	CreatePapayaPayWithdraw(req model.PapayaPayWithdrawCreateRequest) (*int64, error)
	// PapayaPayCheckBalance() (*model.PapayaPayCheckBalanceRemoteResponse, error)
	// SysLog
	CreateSystemLog(name string, req interface{}) error
}

type paygatePapayaPayService struct {
	sharedDb                  *gorm.DB
	repo                      repository.PapayaPayRepository
	activityLuckyWheelService ActivityLuckyWheelService
	promotionWebService       PromotionWebService
	notiService               NotificationService
}

func NewPapayaPayService(
	sharedDb *gorm.DB,
	repo repository.PapayaPayRepository,
	activityLuckyWheelService ActivityLuckyWheelService,
	promotionWebService PromotionWebService,
	notiService NotificationService,
) PapayaPayService {
	return &paygatePapayaPayService{sharedDb, repo, activityLuckyWheelService, promotionWebService, notiService}
}

func (s paygatePapayaPayService) confirmDepositTransaction(id int64, req model.BankConfirmDepositRequest) error {

	record, err := s.repo.GetBankTransactionById(id)
	if err != nil {
		return internalServerError(err)
	}
	if record.TransactionStatusId != model.TRANS_STATUS_PENDING {
		return badRequest("Transaction is not pending")
	}
	if record.TransactionTypeId != model.TRANSACTION_TYPE_DEPOSIT && record.TransactionTypeId != model.TRANSACTION_TYPE_BONUS {
		return badRequest("Transaction is not deposit")
	}
	jsonBefore, _ := json.Marshal(record)

	var updateData model.BankDepositTransactionConfirmBody
	updateData.TransactionStatusId = model.TRANS_STATUS_DEPOSIT_PENDING_CREDIT
	updateData.ConfirmedAt = req.ConfirmedAt
	updateData.ConfirmedByAdminId = req.ConfirmedByAdminId

	// if record.IsAutoCredit && record.TransferAt != nil {
	// 	seconds := time.Now().UTC().Sub(record.TransferAt.UTC()).Seconds()
	// 	autoProcessTimer := fmt.Sprintf("%.2f", seconds)
	// 	updateData.AutoProcessTimer = &autoProcessTimer
	// }

	var createBody model.CreateBankTransactionActionBody
	createBody.ActionKey = fmt.Sprintf("DCF_STATE#%d", record.Id)
	createBody.TransactionId = record.Id
	createBody.UserId = record.UserId
	createBody.TransactionTypeId = record.TransactionTypeId
	createBody.FromAccountId = record.FromAccountId
	createBody.ToAccountId = record.ToAccountId
	createBody.JsonBefore = string(jsonBefore)
	if req.TransferAt == nil {
		createBody.TransferAt = record.TransferAt
	} else {
		TransferAt := req.TransferAt
		createBody.TransferAt = TransferAt
		updateData.TransferAt = *TransferAt
	}
	if req.SlipUrl != nil {
		createBody.SlipUrl = *req.SlipUrl
	}
	createBody.CreditAmount = record.CreditAmount
	if req.BonusAmount != nil {
		createBody.BonusAmount = *req.BonusAmount
	}
	createBody.ConfirmedAt = req.ConfirmedAt
	createBody.ConfirmedByAdminId = req.ConfirmedByAdminId
	if actionId, err := s.repo.CreateTransactionAction(createBody); err == nil {
		// do nothing ?
		if err := s.repo.ConfirmPendingDepositTransaction(id, updateData); err != nil {
			if err := s.repo.RollbackTransactionAction(*actionId); err == nil {
				return internalServerError(err)
			}
			return internalServerError(err)
		}
	} else {
		return internalServerError(err)
	}
	return nil
}

func (s paygatePapayaPayService) CreatePapayaPayFundInWebhook(req model.PapayaPayWebhookRequest) (*int64, error) {

	var createBody model.PapayaPayWebhookCreateBody
	createBody.Name = "PAPAYA_FUNDIN_CALLBACK"
	createBody.JsonPayload = req.JsonPayload
	insertId, err := s.repo.CreatePapayaPayWebhook(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// DEPOSIT
	// {
	// 	"status": "02_Paid",
	// 	"type": "auto",
	// 	"callbackStatus": "02_Completed",
	// 	"currencyCode": "THB",
	// 	"amount": 114,
	// 	"fees": 1.71,
	// 	"qrCodeTransactionId": "PGWAY24074",
	// 	"transactionRef1": "NUJG46P1XL8HEPZXMZHK",
	// 	"transactionRef2": "OXI73A0DUL6UW9PUTSSY",
	// 	"transactionRef3": "EFG8FY81FBJSJF2MRLCI",
	// 	"merchant": "668fa10fe969a667dd0559ea",
	// 	"createdAt": "2024-07-11T21:52:45.000Z",
	// 	"updatedAt": "2024-07-11T14:53:38.812Z",
	// 	"qrPayBankRef": "875446cfb78c464ebe5831b4a51e2008",
	// 	"id": "668ff1bda51febf91bd0db92"
	// }
	var remoteResp model.PapayaPayDepositWebhookResponse
	errJson := json.Unmarshal([]byte(req.JsonPayload), &remoteResp)
	if errJson != nil {
		log.Println("Error unmarshal json", errJson)
		return nil, internalServerError(errJson)
	}

	if remoteResp.QrCodeTransactionId == "" || remoteResp.TransactionRef1 == "" {
		return nil, internalServerError(fmt.Errorf("INVALID_REFERENCE"))
	}

	// Service Race Condition by Ref1
	var rcCreateBody model.RaceActionCreateBody
	rcCreateBody.Name = "CreatePapayaPayWebhook"
	rcCreateBody.JsonRequest = helper.StructJson(req)
	rcCreateBody.Status = "PENDING"
	// ActionKey = Ref2-MINUTE
	rcCreateBody.ActionKey = fmt.Sprintf("%s-%s", remoteResp.QrCodeTransactionId, time.Now().Format("**********"))
	rcCreateBody.UnlockAt = time.Now().UTC().Add(time.Second * 1)
	if _, err := s.repo.CreateRaceCondition(rcCreateBody); err != nil {
		return insertId, internalServerError(err)
	}

	// Get Posible Order
	var query model.PapayaPayOrderListRequest
	query.OrderNo = remoteResp.QrCodeTransactionId
	query.TransactionNo = remoteResp.QrCodeTransactionId // "TODO NO CALLBACK REF"
	query.Amount = fmt.Sprintf("%f", remoteResp.Amount)
	query.Status = "WAIT_PAYMENT"
	query.Limit = 1
	list, _, err := s.repo.GetDbPapayaPayOrderList(query)
	if err != nil {
		return nil, internalServerError(err)
	}

	if len(list) > 0 {
		for _, item := range list {
			// Update Order
			successStatus := remoteResp.Status
			if successStatus == "02_Paid" {
				successStatus = "PAID"
			}
			if err := s.repo.ApproveDbPapayaPayOrder(item.Id, successStatus); err != nil {
				return nil, internalServerError(err)
			}
			// Payment Success, Create Deposit if UserId is not null
			if item.UserId > 0 && item.OrderTypeId == model.PAPAYAPAY_ORDER_TYPE_DEPOSIT {
				if remoteResp.Status == "02_Paid" && remoteResp.Amount > 0 {
					if _, err := s.createCustomerDepositFromPapayaPay(item); err != nil {
						// WebhookLog
						var createBody2 model.PapayaPayWebhookCreateBody
						createBody2.Name = "PAPAYAPAY_DEPOSIT_ERROR"
						createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
							"item":   item,
							"method": "createCustomerDepositFromPapayaPay",
							"error":  err.Error(),
						})
						if _, err = s.repo.CreatePapayaPayWebhook(createBody2); err != nil {
							log.Println("Error CreatePapayaPayWebhook.createCustomerDepositFromPapayaPay", err)
						}
					}
				}
			} else {
				log.Println("ApproveDbPapayaPayOrder.WithoutItemUpdated", helper.StructJson(item))
			}
		}
	}
	return insertId, nil
}

func (s paygatePapayaPayService) CreatePapayaPayFundOutWebhook(req model.PapayaPayWebhookRequest) (*int64, error) {

	var createBody model.PapayaPayWebhookCreateBody
	createBody.Name = "PAPAYA_FUNDOUT_CALLBACK"
	createBody.JsonPayload = req.JsonPayload
	insertId, err := s.repo.CreatePapayaPayWebhook(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// Withdraw
	// {
	// 	"type": "FundOut",
	// 	"currencyCode": "THB",
	// 	"fundOutStatus": "REJECTED",
	// 	"amount": 30,
	// 	"serviceFee": 0,
	// 	"isPaid": false,
	// 	"transactionRemark": "below minimum",
	// 	"fundOutDescription": "Withdrawal",
	// 	"transactionRef1": "KHHNH1Y7Q5IVXZR1OOVF",
	// 	"fundOutPaymentReference": "TRRSL2OM2R6P4457",
	// 	"bankName": "",
	// 	"bankCode": "004",
	// 	"accountNumber": "**********",
	// 	"createdAt": "2024-07-11T21:59:56.000Z",
	// 	"updatedAt": "2024-07-11T22:00:18.000Z",
	// 	"bankTransactionRef": "below minimum"
	// }

	// {
	// 	"type": "FundOut",
	// 	"currencyCode": "THB",
	// 	"fundOutStatus": "PAID",
	// 	"amount": 104,
	// 	"serviceFee": 0.312,
	// 	"isPaid": true,
	// 	"transactionRemark": "Success 6964 - P",
	// 	"fundOutDescription": "Withdrawal",
	// 	"transactionRef1": "BOHWRDDZO436YVPBTVPG",
	// 	"fundOutPaymentReference": "PGWAY24076",
	// 	"bankName": "",
	// 	"bankCode": "004",
	// 	"accountNumber": "**********",
	// 	"createdAt": "2024-07-11T22:08:12.000Z",
	// 	"updatedAt": "2024-07-11T22:08:40.000Z",
	// 	"bankTransactionRef": "Success 6964 - P"
	// }
	var remoteResp model.PapayaPayWithDrawWebhookResponse
	errJson := json.Unmarshal([]byte(req.JsonPayload), &remoteResp)
	if errJson != nil {
		log.Println("Error unmarshal json", errJson)
		return nil, internalServerError(errJson)
	}

	if remoteResp.FundOutPaymentReference == "" || remoteResp.TransactionRef1 == "" {
		return nil, internalServerError(fmt.Errorf("INVALID_REFERENCE"))
	}

	// Service Race Condition by Ref1
	var rcCreateBody model.RaceActionCreateBody
	rcCreateBody.Name = "CreatePapayaPayWebhook"
	rcCreateBody.JsonRequest = helper.StructJson(req)
	rcCreateBody.Status = "PENDING"
	// ActionKey = Ref2-MINUTE
	rcCreateBody.ActionKey = fmt.Sprintf("%s-%s", remoteResp.FundOutPaymentReference, time.Now().Format("**********"))
	rcCreateBody.UnlockAt = time.Now().UTC().Add(time.Second * 1)
	if _, err := s.repo.CreateRaceCondition(rcCreateBody); err != nil {
		return insertId, internalServerError(err)
	}

	// Get Posible Order
	var query model.PapayaPayOrderListRequest
	query.OrderNo = remoteResp.FundOutPaymentReference
	query.TransactionNo = remoteResp.TransactionRef1 // "TODO NO CALLBACK REF"
	query.Amount = fmt.Sprintf("%f", remoteResp.Amount)
	query.Status = "WAIT_PAYMENT"
	query.Limit = 1
	list, _, err := s.repo.GetDbPapayaPayOrderList(query)
	if err != nil {
		return nil, internalServerError(err)
	}

	if len(list) > 0 {
		for _, item := range list {
			// Update Order
			successStatus := remoteResp.FundOutStatus
			// if successStatus == "WAIT_PAYMENT" {
			// TRANSFORM STATUS TO ENUM
			// 	successStatus = "NOT_WAIT_PAYMENT"
			// }
			if err := s.repo.ApproveDbPapayaPayOrder(item.Id, successStatus); err != nil {
				return nil, internalServerError(err)
			}
			// Payment Success, Create Deposit if UserId is not null
			if item.RefId != nil && item.OrderTypeId == model.PAPAYAPAY_ORDER_TYPE_WITHDRAW {
				if remoteResp.FundOutStatus == "PAID" && remoteResp.Amount > 0 {
					if _, err := approveCustomerWithdrawFromPapayaPay(s.repo, *item.RefId); err != nil {
						// WebhookLog
						var createBody2 model.PapayaPayWebhookCreateBody
						createBody2.Name = "PAPAYAPAY_WITHDRAW_ERROR"
						createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
							"item":   item,
							"method": "approveCustomerWithdrawFromPapayaPay",
							"error":  err.Error(),
						})
						if _, err = s.repo.CreatePapayaPayWebhook(createBody2); err != nil {
							log.Println("Error approveCustomerWithdrawFromPapayaPay.CreatePapayaPayWebhook", err)
						}
					}
				} else if remoteResp.FundOutStatus == "REJECTED" && remoteResp.Amount > 0 {
					if _, err := rollbackCustomerWithdrawFromPapayaPay(s.repo, *item.RefId); err != nil {
						// WebhookLog
						var createBody2 model.PapayaPayWebhookCreateBody
						createBody2.Name = "PAPAYAPAY_WITHDRAW_ERROR"
						createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
							"item":   item,
							"method": "rollbackCustomerWithdrawFromPapayaPay",
							"error":  err.Error(),
						})
						if _, err = s.repo.CreatePapayaPayWebhook(createBody2); err != nil {
							log.Println("Error rollbackCustomerWithdrawFromPapayaPay.CreatePapayaPayWebhook", err)
						}
					}
					// Create detail for BankTransaction
					var createBankTransactionExternalDetail model.BankTransactionExternalDetailCreateRequest
					createBankTransactionExternalDetail.BankTransactionId = *item.RefId
					createBankTransactionExternalDetail.Detail = "ถูกปฏิเสธจากระบบ PapayaPay, " + remoteResp.TransactionRemark
					createBankTransactionExternalDetail.ErrorCode = 500
					if _, err := s.repo.CreateBankTransactionExternalDetail(createBankTransactionExternalDetail); err != nil {
						log.Println("Error rollbackCustomerWithdrawFromPapayaPay.CreateBankTransactionExternalDetail", err)
					}
				}
			} else {
				log.Println("ApproveDbPapayaPayOrder.WithoutItemUpdated", helper.StructJson(item))
			}
		}
	}

	return insertId, nil
}

func (s paygatePapayaPayService) GetPapayaPayWebDepositAccount() (*model.PapayaPayCustomerDepositInfo, error) {

	var result model.PapayaPayCustomerDepositInfo

	pgAccount, err := s.GetPapayaPayAccount()
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}
	if !pgAccount.IsDepositEnabled {
		return nil, badRequest("PAYGATE_DEPOSIT_NOT_ENABLED")
	}

	result.Name = pgAccount.Name
	if pgAccount.DisplayName == "" {
		result.Name = "เติมเงินผ่าน QR Code"
	} else {
		result.DisplayName = pgAccount.DisplayName
	}
	result.ShopName = pgAccount.ShopName
	result.MinAmount = 100
	result.MaxAmount = 200000

	return &result, nil
}

func (s paygatePapayaPayService) GetPapayaPayAccount() (*model.PaygateAccountResponse, error) {

	pgAccount, err := s.repo.GetPaygateAccountByProviderId(model.PAYGATE_MERCHANT_ID_PAPAYAPAY)
	if err != nil {
		return nil, internalServerError(err)
	}
	return pgAccount, nil
}

func (s paygatePapayaPayService) CreatePapayaPayDeposit(req model.PapayaPayDepositCreateRequest) (*model.PapayaPayOrderWebResponse, error) {

	var result model.PapayaPayOrderWebResponse

	pgAccount, err := s.GetPapayaPayAccount()
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}
	if !pgAccount.IsDepositEnabled {
		return nil, badRequest("PAYGATE_DEPOSIT_NOT_ENABLED")
	}

	user, err := s.repo.GetUserBankDetailById(req.UserId)
	if err != nil {
		return nil, badRequest("INVALID_USER")
	}
	if user == nil || user.BankAccount == "" || user.BankCode == "" {
		return nil, badRequest("INVALID_USER_ACCOUNT")
	}

	// Bank Abbreviations = user.BankCode Support ?
	// Bank Code	Bank Name (Thai)	Bank Name (English)	Short Name
	// 001	ธนาคารแห่งประเทศไทย	BANK OF THAILAND	BOT
	// 002	ธนาคารกรุงเทพ จำกัด (มหาชน)	BANGKOK BANK PUBLIC COMPANY LTD.	BBL
	// 004	ธนาคารกสิกรไทย จำกัด (มหาชน)	KASIKORNBANK PUBLIC COMPANY LIMITED	KBANK
	// 006	ธนาคารกรุงไทย จำกัด (มหาชน)	KRUNG THAI BANK PUBLIC COMPANY LTD.	KTB
	// 011	ธนาคารทหารไทยธนชาต จำกัด (มหาชน)	TMB THANACHART BANK PUBLIC COMPANY LIMITED	TTB TMB
	// 014	ธนาคารไทยพาณิชย์ จำกัด (มหาชน)	SIAM COMMERCIAL BANK PUBLIC COMPANY LTD.	SCB
	// 017	ธนาคารซิตี้แบงก์ เอ็น.เอ.	CITIBANK, N.A.	CITI
	// 020	ธนาคารสแตนดาร์ดชาร์เตอร์ด (ไทย) จำกัด (มหาชน)	STANDARD CHARTERED BANK (THAI) PUBLIC COMPANY LIMITED	StandardCB
	// 022	ธนาคารซีไอเอ็มบี ไทย จำกัด (มหาชน)	CIMB THAI BANK PUBLIC COMPANY LIMITED	CIMB
	// 024	ธนาคารยูโอบี จำกัด (มหาชน)	UNITED OVERSEAS BANK (THAI) PUBLIC COMPANY LIMITED	UOB
	// 025	ธนาคารกรุงศรีอยุธยา จำกัด (มหาชน)	BANK OF AYUDHYA PUBLIC COMPANY LTD.	BAY
	// 030	ธนาคารออมสิน	GOVERNMENT SAVINGS BANK	GSB
	// 033	ธนาคารอาคารสงเคราะห์	THE GOVERNMENT HOUSING BANK	GHB
	// 034	ธนาคารเพื่อการเกษตรและสหกรณ์การเกษตร	BANK FOR AGRICULTURE AND AGRICULTURAL COOPERATIVES	BAAC
	// 052	ธนาคารแห่งประเทศจีน (ไทย) จำกัด (มหาชน)	BANK OF CHINA (THAI) PUBLIC COMPANY LIMITED	BOC
	// 066	ธนาคารอิสลามแห่งประเทศไทย	ISLAMIC BANK OF THAILAND	IBT
	// 067	ธนาคารทิสโก้ จำกัด (มหาชน)	TISCO BANK PUBLIC COMPANY LIMITED	TISCO
	// 069	ธนาคารเกียรตินาคินภัทร จำกัด (มหาชน)	KIATNAKIN PHATRA BANK PUBLIC COMPANY LIMITED	KKP
	// 070	ธนาคารไอซีบีซี (ไทย) จำกัด (มหาชน)	INDUSTRIAL AND COMMERCIAL BANK OF CHINA (THAI) PUBLIC COMPANY LIMITED	ICBC
	// 073	ธนาคารแลนด์ แอนด์ เฮ้าส์ จำกัด (มหาชน)	LAND AND HOUSES BANK PUBLIC COMPANY LIMITED	LHB
	acceptBankCodeList := []string{"BOT", "BBL", "KBANK", "KTB", "TTB", "SCB", "CITI", "StandardCB", "CIMB", "UOB", "BAY", "GSB", "GHB", "BAAC", "BOC", "IBT", "TISCO", "KKP", "ICBC", "LHBANK"}
	bankCodeMap := map[string]string{
		"BOT":        "001",
		"BBL":        "002",
		"KBANK":      "004",
		"KTB":        "006",
		"TTB":        "011",
		"SCB":        "014",
		"CITI":       "017",
		"StandardCB": "020",
		"CIMB":       "022",
		"UOB":        "024",
		"BAY":        "025",
		"GSB":        "030",
		"GHB":        "033",
		"BAAC":       "034",
		"BOC":        "052",
		"IBT":        "066",
		"TISCO":      "067",
		"KKP":        "069",
		"ICBC":       "070",
		"LHBANK":     "073",
	}

	reqBankCode := user.BankCode
	// rename bank code tb_bank=>PAPAYAPAY
	if reqBankCode == "LH" {
		reqBankCode = "LHBANK"
	}
	if !helper.StringInArray(strings.ToUpper(reqBankCode), acceptBankCodeList) {
		return nil, badRequest("BANK_CODE_NOT_SUPPORT")
	}
	reqAccountNumber := helper.StripAllButNumbers(user.BankAccount)
	if reqAccountNumber == "" {
		return nil, badRequest("INVALID_ACCOUNT_NUMBER")
	}
	ppyBankCode, ok := bankCodeMap[strings.ToUpper(reqBankCode)]
	if !ok {
		return nil, badRequest("BANK_CODE_NOT_SUPPORT")
	}

	accountingRepo := repository.NewAccountingRepository(s.repo.GetDb())
	configWeb, err := s.repo.GetWebConfiguration()
	if err != nil {
		log.Println("CreatePapayaPayDeposit.GetWebConfiguration", err)
		return nil, badRequest("WEB_CONFIG_NOT_FOUND")
	}
	if user.Id != 0 && configWeb != nil {
		// [********] ฝากครั้งแรกขั้นต่ำ ปรับ flow เช็ค user ให้เช็คจากรายการฝากครั้งแรกจริงๆ ไม่เช็คจากรหัสที่ได้รับ
		if err := CheckFirstMinimunDeposit(accountingRepo, user.Id, req.Amount, *configWeb); err != nil {
			return nil, badRequest(fmt.Sprintf("ฝากครั้งแรกขั้นต่ำ %d บาท", configWeb.MinFirstMemberDeposit))
		}
	} else {
		log.Println("CreatePapayaPayDeposit.CheckFirstMinimunDeposit", "CANT_CHECK_FIRST_MINIMUM_DEPOSIT_ON_EMPTY_CONFIG")
	}

	// ===========================================================================================
	var createBody model.PapayaPayOrderCreateBody
	createBody.UserId = req.UserId
	createBody.OrderTypeId = model.PAPAYAPAY_ORDER_TYPE_DEPOSIT
	createBody.OrderNo = "" // Autogen
	createBody.Amount = req.Amount
	createBody.TransactionStatus = "PENDING"
	insertId, err := s.repo.CreateDbPapayaPayOrder(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	// reget Order
	pendingOrder, err := s.repo.GetDbPapayaPayOrderById(*insertId)
	if err != nil {
		// SET AS ERROR
		remark := "Error GetDbPapayaPayOrderById, " + err.Error()
		if err := s.repo.UpdateDbPapayaPayOrderError(*insertId, remark); err != nil {
			log.Println("CreatePapayaPayDeposit.UpdateDbPapayaPayOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// Create PAPAYAPAY Order
	var remoteRequest model.PapayaPayDepositCreateRemoteRequest
	remoteRequest.QrCodeTransactionId = pendingOrder.OrderNo
	remoteRequest.Currency = "THB"
	remoteRequest.Amount = pendingOrder.Amount
	remoteRequest.PayMethod = "thaiqr"
	remoteRequest.BankCode = ppyBankCode
	remoteRequest.AccountNumber = reqAccountNumber
	remoteRequest.AccountName = user.Fullname
	remoteRequest.Description = "Payment"
	remoteResp, err := s.repo.PapayaPayDeposit(*pgAccount, remoteRequest)
	if err != nil {
		// SET AS ERROR
		remark := "Error PapayaPayDeposit, " + err.Error()
		if err := s.repo.UpdateDbPapayaPayOrderError(*insertId, remark); err != nil {
			log.Println("PapayaPayDeposit.UpdateDbPapayaPayOrderError", err)
		}
		// SysLog
		if _, err := s.repo.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "CreatePapayaPayDeposit.PapayaPayDeposit",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(remoteRequest),
			JsonResponse: helper.StructJson(map[string]interface{}{"error": err.Error()}),
		}); err != nil {
			log.Println("CreatePapayaPayDeposit.CreateSysLog", err)
		}
		return nil, internalServerError(err)
	}

	fmt.Println("PapayaPayDeposit.remoteResp", helper.StructJson(remoteResp))

	// onCreate Success
	var updateBody model.PapayaPayOrderUpdateBody
	updateBody.TransactionNo = pendingOrder.OrderNo // "TODO NO CALLBACK REF"
	updateBody.TransactionDate = time.Now()
	updateBody.TransactionStatus = "WAIT_PAYMENT"
	updateBody.QrPromptpay = remoteResp.PayUrl
	if err := s.repo.UpdateDbPapayaPayOrder(*insertId, updateBody); err != nil {
		// SET AS ERROR
		remark := "Error UpdateDbPapayaPayOrder, " + err.Error()
		if err := s.repo.UpdateDbPapayaPayOrderError(*insertId, remark); err != nil {
			log.Println("CreatePapayaPayDeposit.UpdateDbPapayaPayOrderError", err)
		}
		return nil, internalServerError(err)
	}

	paymentUrl := remoteResp.PayUrl

	// ===========================================================================================
	waitPayOrder, err := s.repo.GetDbPapayaPayOrderById(*insertId)
	if err != nil {
		return nil, internalServerError(err)
	}

	result.UserId = waitPayOrder.UserId
	result.OrderNo = waitPayOrder.OrderNo
	result.Amount = waitPayOrder.Amount
	result.TransactionStatus = *waitPayOrder.TransactionStatus
	result.PaymentUrl = paymentUrl
	result.CreatedAt = waitPayOrder.CreatedAt

	return &result, nil
}

func (s paygatePapayaPayService) CreatePapayaPayWithdraw(req model.PapayaPayWithdrawCreateRequest) (*int64, error) {

	pgAccount, err := s.GetPapayaPayAccount()
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}
	if !pgAccount.IsWithdrawEnabled {
		return nil, badRequest("PAYGATE_WITHDRAW_NOT_ENABLED")
	}

	user, err := s.repo.GetUserBankDetailById(req.UserId)
	if err != nil {
		return nil, badRequest("INVALID_USER")
	}
	if user == nil || user.BankAccount == "" || user.BankCode == "" {
		return nil, badRequest("INVALID_USER_ACCOUNT")
	}

	// Bank Abbreviations = user.BankCode Support ?
	// Bank Code	Bank Name (Thai)	Bank Name (English)	Short Name
	// 001	ธนาคารแห่งประเทศไทย	BANK OF THAILAND	BOT
	// 002	ธนาคารกรุงเทพ จำกัด (มหาชน)	BANGKOK BANK PUBLIC COMPANY LTD.	BBL
	// 004	ธนาคารกสิกรไทย จำกัด (มหาชน)	KASIKORNBANK PUBLIC COMPANY LIMITED	KBANK
	// 006	ธนาคารกรุงไทย จำกัด (มหาชน)	KRUNG THAI BANK PUBLIC COMPANY LTD.	KTB
	// 011	ธนาคารทหารไทยธนชาต จำกัด (มหาชน)	TMB THANACHART BANK PUBLIC COMPANY LIMITED	TTB TMB
	// 014	ธนาคารไทยพาณิชย์ จำกัด (มหาชน)	SIAM COMMERCIAL BANK PUBLIC COMPANY LTD.	SCB
	// 017	ธนาคารซิตี้แบงก์ เอ็น.เอ.	CITIBANK, N.A.	CITI
	// 020	ธนาคารสแตนดาร์ดชาร์เตอร์ด (ไทย) จำกัด (มหาชน)	STANDARD CHARTERED BANK (THAI) PUBLIC COMPANY LIMITED	StandardCB
	// 022	ธนาคารซีไอเอ็มบี ไทย จำกัด (มหาชน)	CIMB THAI BANK PUBLIC COMPANY LIMITED	CIMB
	// 024	ธนาคารยูโอบี จำกัด (มหาชน)	UNITED OVERSEAS BANK (THAI) PUBLIC COMPANY LIMITED	UOB
	// 025	ธนาคารกรุงศรีอยุธยา จำกัด (มหาชน)	BANK OF AYUDHYA PUBLIC COMPANY LTD.	BAY
	// 030	ธนาคารออมสิน	GOVERNMENT SAVINGS BANK	GSB
	// 033	ธนาคารอาคารสงเคราะห์	THE GOVERNMENT HOUSING BANK	GHB
	// 034	ธนาคารเพื่อการเกษตรและสหกรณ์การเกษตร	BANK FOR AGRICULTURE AND AGRICULTURAL COOPERATIVES	BAAC
	// 052	ธนาคารแห่งประเทศจีน (ไทย) จำกัด (มหาชน)	BANK OF CHINA (THAI) PUBLIC COMPANY LIMITED	BOC
	// 066	ธนาคารอิสลามแห่งประเทศไทย	ISLAMIC BANK OF THAILAND	IBT
	// 067	ธนาคารทิสโก้ จำกัด (มหาชน)	TISCO BANK PUBLIC COMPANY LIMITED	TISCO
	// 069	ธนาคารเกียรตินาคินภัทร จำกัด (มหาชน)	KIATNAKIN PHATRA BANK PUBLIC COMPANY LIMITED	KKP
	// 070	ธนาคารไอซีบีซี (ไทย) จำกัด (มหาชน)	INDUSTRIAL AND COMMERCIAL BANK OF CHINA (THAI) PUBLIC COMPANY LIMITED	ICBC
	// 073	ธนาคารแลนด์ แอนด์ เฮ้าส์ จำกัด (มหาชน)	LAND AND HOUSES BANK PUBLIC COMPANY LIMITED	LHB
	acceptBankCodeList := []string{"BOT", "BBL", "KBANK", "KTB", "TTB", "SCB", "CITI", "StandardCB", "CIMB", "UOB", "BAY", "GSB", "GHB", "BAAC", "BOC", "IBT", "TISCO", "KKP", "ICBC", "LHBANK"}
	bankCodeMap := map[string]string{
		"BOT":        "001",
		"BBL":        "002",
		"KBANK":      "004",
		"KTB":        "006",
		"TTB":        "011",
		"SCB":        "014",
		"CITI":       "017",
		"StandardCB": "020",
		"CIMB":       "022",
		"UOB":        "024",
		"BAY":        "025",
		"GSB":        "030",
		"GHB":        "033",
		"BAAC":       "034",
		"BOC":        "052",
		"IBT":        "066",
		"TISCO":      "067",
		"KKP":        "069",
		"ICBC":       "070",
		"LHBANK":     "073",
	}

	reqBankCode := user.BankCode
	// rename bank code tb_bank=>PAPAYAPAY
	if reqBankCode == "LH" {
		reqBankCode = "LHBANK"
	}
	if !helper.StringInArray(strings.ToUpper(reqBankCode), acceptBankCodeList) {
		return nil, badRequest("BANK_CODE_NOT_SUPPORT")
	}
	reqAccountNumber := helper.StripAllButNumbers(user.BankAccount)
	if reqAccountNumber == "" {
		return nil, badRequest("INVALID_ACCOUNT_NUMBER")
	}
	ppyBankCode, ok := bankCodeMap[strings.ToUpper(reqBankCode)]
	if !ok {
		return nil, badRequest("BANK_CODE_NOT_SUPPORT")
	}

	// ===========================================================================================
	// CREATE Order
	var createBody model.PapayaPayOrderCreateBody
	createBody.RefId = &req.RefId
	createBody.UserId = req.UserId
	createBody.OrderTypeId = model.PAPAYAPAY_ORDER_TYPE_WITHDRAW
	createBody.OrderNo = "" // Autogen
	createBody.Amount = req.Amount
	createBody.TransactionStatus = "PENDING"
	insertId, err := s.repo.CreateDbPapayaPayOrder(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	// reget Order
	pendingOrder, err := s.repo.GetDbPapayaPayOrderById(*insertId)
	if err != nil {
		// SET AS ERROR
		remark := "Error GetDbPapayaPayOrderById, " + err.Error()
		if err := s.repo.UpdateDbPapayaPayOrderError(*insertId, remark); err != nil {
			log.Println("CreatePapayaPayWithdraw.UpdateDbPapayaPayOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// Create PAPAYAPAY Order
	var remoteRequest model.PapayaPayWithdrawCreateRemoteRequest
	remoteRequest.CurrencyCode = "THB"
	remoteRequest.FundOutPaymentReference = pendingOrder.OrderNo
	remoteRequest.FundOutDescription = "Withdrawal"
	remoteRequest.AccountName = user.Fullname
	remoteRequest.AccountNumber = reqAccountNumber
	remoteRequest.BankCode = ppyBankCode
	remoteRequest.Amount = pendingOrder.Amount
	remoteResp, err := s.repo.PapayaPayWithdraw(*pgAccount, remoteRequest)
	if err != nil {
		// SET AS ERROR
		remark := "Error PapayaPayWithdraw, " + err.Error()
		if err := s.repo.UpdateDbPapayaPayOrderError(*insertId, remark); err != nil {
			log.Println("PapayaPayWithdraw.UpdateDbPapayaPayOrderError", err)
		}
		// SysLog
		if _, err := s.repo.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "CreatePapayaPayWithdraw.PapayaPayWithdraw",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(remoteRequest),
			JsonResponse: helper.StructJson(map[string]interface{}{"error": err.Error()}),
		}); err != nil {
			log.Println("CreatePapayaPayWithdraw.CreateSysLog", err)
		}
		return nil, internalServerError(err)
	}

	fmt.Println("CreatePapayaPayWithdraw.remoteResp", helper.StructJson(remoteResp))

	if remoteResp.StatusCode != 200 || remoteResp.Data.FundOutStatus != "PROCESSING" {
		// SET AS ERROR
		remark := remoteResp.Data.Message
		if len(remark) == 0 {
			remark = "UNKNOW_ERROR from withdrawWithPapayaPay"
		}
		if err := s.repo.UpdateDbPapayaPayOrderError(*insertId, remark); err != nil {
			log.Println("PapayaPayWithdraw.UpdateDbPapayaPayOrderError", err)
		}
		// SysLog
		if _, err := s.repo.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "CreatePapayaPayWithdraw.PapayaPayWithdraw",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(remoteRequest),
			JsonResponse: helper.StructJson(remoteResp),
		}); err != nil {
			log.Println("CreatePapayaPayWithdraw.CreateSysLog", err)
		}
		return nil, internalServerError(fmt.Errorf("UNKNOW_ERROR"))
	}

	// onCreate Success
	var updateBody model.PapayaPayOrderUpdateBody
	updateBody.TransactionNo = remoteResp.Data.Data.TransactionRef1
	updateBody.TransactionDate = time.Now()
	updateBody.TransactionStatus = "WAIT_PAYMENT"
	updateBody.QrPromptpay = remoteResp.Data.Data.FundOutCallbackStatus
	if err := s.repo.UpdateDbPapayaPayOrder(*insertId, updateBody); err != nil {
		// SET AS ERROR
		remark := "Error UpdateDbPapayaPayOrder, " + err.Error()
		if err := s.repo.UpdateDbPapayaPayOrderError(*insertId, remark); err != nil {
			log.Println("CreatePapayaPayWithdraw.UpdateDbPapayaPayOrderError", err)
		}
		return nil, internalServerError(err)
	}
	return insertId, nil
}

func (s paygatePapayaPayService) createCustomerDepositFromPapayaPay(item model.PapayaPayOrderResponse) (*int64, error) {

	var externalNoti model.NotifyExternalNotificationRequest
	actionAt := time.Now()
	confirmByUserId := int64(0)

	depositAmount := item.Amount

	user, err := s.repo.GetUserBankDetailById(item.UserId)
	if err != nil {
		log.Println("createCustomerDepositFromPapayaPay.GetUserBankDetailByMemberCode", err)
		return nil, internalServerError(err)
	}

	var promotionWebUserId int64
	if getPromotionWebUser, err := s.repo.GetDepositCurrentProcessingUserPromotion(item.UserId); err != nil {
		log.Println("createCustomerDepositFromPapayaPay.GetDepositCurrentProcessingUserPromotion", err)
	} else if getPromotionWebUser != nil {
		promotionWebUserId = getPromotionWebUser.Id
	}

	// MemberCode
	if user.MemberCode == "" {
		memberCode, err := GenUniqueUserMemberCode(repository.NewUserRepository(s.sharedDb), user.Id)
		if err != nil {
			log.Println("createCustomerDepositFromPapayaPay.GenUniqueUserMemberCode", err)
			return nil, internalServerError(err)
		}
		user.MemberCode = *memberCode
	}

	// layer — 2024-04-02 at 10:06 AM
	// ถ้าแก้ตามนี้แล้วน่าจะไม่เจอ ถ้าเคสกันซ้ำเราครอบคลุม
	// @Dunk @TULA
	// กันซ้ำคือ heng ไปซ้ำกับแอดมินเติมมือ
	// ----
	// เงื่อนไขกันซ้ำ
	// 1 หารายการฝากสำเร็จล่าสุดของยูสนั้น
	// 2 ถ้ามีรายการล่าสุดที่เป็นเติมมือ
	// -เช็คเวลาเติม ต้องมากกว่าเวลาโอนในสลิป
	// -ยอดเงินเท่ากัน
	// **ถ้าเข้าเงื่อนไขทั้งหมด คือแสดงว่า webhook เข้าทีหลังแอดมินเติมมือ
	// ต้อง bypass ไม่ให้เติมอีก และอัพเดท status ว่าจับคู่ได่แล้ว
	// ---
	// วิธีนี้น่าจะใช้ได้กับทุก webhook (heng, fastbank) ที่มีเวลาโอนสลิปมาให้
	duplicateRemark := "INIT"
	hasDuplicateDeposit := false
	manualDeposit, err := s.repo.GetLastestBankAdminManualDepositTransaction(user.Id)
	if err != nil {
		if err.Error() != recordNotFound {
			log.Println("createCustomerDepositFromPapayaPay.GetLastestBankAdminManualDepositTransaction", err)
			return nil, internalServerError(err)
		}
		// NOT_FOUND = ถ้าไม่เจอ ให้ทำต่อ
		duplicateRemark = "OK_NO_MANUAL_DEPOSIT"
	}
	if manualDeposit != nil && item.TransactionDate != nil {
		if manualDeposit.TransferAt.After(*item.TransactionDate) {
			// ถ้าเวลาเติมมือมากกว่าเวลาโอนในสลิป
			if manualDeposit.CreditAmount == depositAmount {
				// ถ้ายอดเงินเท่ากัน
				duplicateRemark = "STOP_MANUAL_DEPOSIT_AFTER_AND_SAME_AMOUNT"
				hasDuplicateDeposit = true
			} else {
				duplicateRemark = "OK_MANUAL_DEPOSIT_AMOUNT_NOT_MATCH"
			}
		} else {
			duplicateRemark = "OK_MANUAL_DEPOSIT_BEFORE_WITHDRAW"
		}
	}
	if duplicateRemark != "INIT" {
		// WebhookLog
		var createBody2 model.PapayaPayWebhookCreateBody
		createBody2.Name = "PAPAYAPAY_DEPOSIT_DUPLICATE_CHECKER"
		createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
			"item":                item,
			"method":              "createCustomerDeposit",
			"hasDuplicateDeposit": hasDuplicateDeposit,
			"error":               duplicateRemark,
			"manualDeposit":       manualDeposit,
		})
		if _, err = s.repo.CreatePapayaPayWebhook(createBody2); err != nil {
			log.Println("Error CreatePapayaPayWebhook.CreatePapayaPayWebhook", err)
		}
		if hasDuplicateDeposit {
			return nil, internalServerError(fmt.Errorf("DUPLICATE_DEPOSIT"))
		}
	}

	pgAccount, err := s.GetPapayaPayAccount()
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}

	// ===================================================

	// Affiliate + Alliance Income
	member, err := s.repo.GetMemberById(user.Id)
	if err != nil {
		return nil, internalServerError(err)
	}
	if member.UserTypeName == "NONE" {
		// ถ้าฝากครั้งแรก จะได้เป็น AFFILIATE + REFER COMMISION
		if err := UserFirstDepositCommission(repository.NewAccountingRepository(s.sharedDb), *member, depositAmount); err != nil {
			log.Println("CreateFristTimeDepositRecord.UserFirstDepositCommission.ERROR", err)
			return nil, internalServerError(err)
		}
	}

	isAutoDeposit := true // Auto ฝาก นับตั้งแต่ได้รับยอดฝาก จนได้ยืนยันเครดิต

	// CREATE BANK TRANSACTION
	var createBankTransaction model.BankTransactionCreateBody
	createBankTransaction.MemberCode = user.MemberCode
	createBankTransaction.UserId = user.Id
	// createBankTransaction.StatementId = 0
	createBankTransaction.TransactionTypeId = model.TRANSACTION_TYPE_DEPOSIT
	// createBankTransaction.FromAccountName = "" // empty?
	createBankTransaction.ToAccountName = &pgAccount.TypeName
	createBankTransaction.ToAccountNumber = &pgAccount.ShopName
	createBankTransaction.PromotionId = &promotionWebUserId
	createBankTransaction.CreditAmount = depositAmount
	// createBankTransaction.BonusAmount = 0
	// createBankTransaction.BonusReason = nil
	createBankTransaction.DepositChannel = "PAPAYAPAY PAYMENT GATEWAY"
	createBankTransaction.TransactionStatusId = model.TRANS_STATUS_PENDING
	createBankTransaction.CreatedByAdminId = 0
	createBankTransaction.IsAutoCredit = isAutoDeposit
	createBankTransaction.TransferAt = &actionAt
	// create transaction
	transId, err := s.repo.InsertBankTransaction(createBankTransaction)
	if err != nil {
		log.Println("createCustomerDepositFromPapayaPay.InsertBankTransaction", err)
		return nil, internalServerError(err)
	}

	// var actionStatement model.BankStatementMatchRequest
	// actionStatement.StatementId = *bankstatementId
	// actionStatement.UserId = user.Id
	// actionStatement.ConfirmedAt = time
	// actionStatement.ConfirmedByAdminId = &req.CreateByUserId
	// if err := s.serviceAccounting.SetStatementOwnerMatched(*bankstatementId, actionStatement, model.USE_ENDING_NOTI); err != nil {
	// 	log.Println("createCustomerDepositFromPapayaPay.SetStatementOwnerMatched", err)
	// 	return nil, internalServerError(err)
	// }

	// DECREASE FASTBANK CREDIT 2.FROM create auto DEPOSIT
	// if err := s.repoAccounting.DecreaseFastbankCredit(1); err != nil {
	// 	log.Println("createCustomerDepositFromPapayaPay.DECREASE_FASTBANK_CREDIT_ERROR", err)
	// }

	var confirmDeposit model.BankConfirmDepositRequest
	confirmDeposit.TransferAt = &actionAt
	confirmDeposit.ConfirmedAt = actionAt
	confirmDeposit.ConfirmedByAdminId = &confirmByUserId
	if err := s.confirmDepositTransaction(*transId, confirmDeposit); err != nil {
		log.Println("createCustomerDepositFromPapayaPay.confirmDepositTransaction", err)
		return nil, internalServerError(err)
	}

	isFirstDeposit := s.repo.IsFirstDeposit(user.Id)
	if isFirstDeposit {
		var bonusReq model.UserFirstDepositCreateRequest
		bonusReq.UserId = user.Id
		bonusReq.TransactionId = transId
		bonusReq.TransferAt = actionAt
		bonusReq.Amount = depositAmount
		bonusReq.Remark = "createCustomerDepositFromPapayaPay"
		if err := SetFirstDepositBonus(repository.NewAccountingRepository(s.sharedDb), isFirstDeposit, bonusReq); err != nil {
			webhookLogMessage := fmt.Sprintf("createCustomerDepositFromPapayaPay.userFirstDepositBonus, ERROR: %s", err)
			log.Println("SetFirstDepositBonus", webhookLogMessage)
			// return nil, errors.New(webhookLogMessage)
		}
	}

	// [USER_CREDIT]
	var userCreditReq model.UserTransactionCreateRequest
	userCreditReq.UserId = user.Id
	userCreditReq.RefId = transId
	userCreditReq.TransferAt = &actionAt
	userCreditReq.PromotionId = &promotionWebUserId
	userCreditReq.TypeId = model.CREDIT_TYPE_DEPOSIT
	// userCreditReq.AccountId = &bankAccount.Id
	userCreditReq.Detail = "PAPAYAPAY PAYMENT GATEWAY"
	userCreditReq.Amount = depositAmount
	userCreditReq.CreateBy = &confirmByUserId
	userCreditReq.ConfirmBy = &confirmByUserId
	userCreditReq.IsAdjustAuto = isAutoDeposit
	userCreditReq.PaymentMerchatId = &pgAccount.ProviderId
	if agentResp, err := s.repo.IncreaseUserCredit(userCreditReq); err != nil {
		log.Println("createCustomerDepositFromPapayaPay.IncreaseUserCredit", err)
		return nil, internalServerError(err)
	} else {
		// AGENT_SUCCESS
		if err := s.repo.UpdateDepositTransactionStatusFromAgent(*transId, *agentResp); err != nil {
			log.Println("createCustomerDepositFromPapayaPay.UpdateDeporsitTransactionStatusFromAgent", err)
		}
	}

	// [Lucky Wheel] ตอนฝากละได้โบนัส
	var luckyWheelBody model.ActivityLuckyWheelRoundUserRequest
	luckyWheelBody.UserId = user.Id
	luckyWheelBody.ConditionId = model.ACTIVITY_LUCKY_WHEEL_SETTING_CONDITION_DEPOSIT
	luckyWheelBody.ConditionAmount = depositAmount
	if err := s.activityLuckyWheelService.CreateRoundActivityLuckyWheel(luckyWheelBody); err != nil {
		log.Println("createCustomerDepositFromPapayaPay.CreateRoundActivityLuckyWheel", err)
	}

	// [TIER]
	if err := s.repo.IncreaseUserTierDepositAmount(member.Id, depositAmount); err != nil {
		log.Println("MatchStatementOwner.IncreaseUserTierDepositAmount", err)
	}

	var checkUserPromotionBody model.CheckUserPromotionBody
	checkUserPromotionBody.UserId = user.Id
	_, err = s.promotionWebService.CheckUserPromotion(checkUserPromotionBody)
	if err != nil {
		log.Println("createCustomerDepositFromPapayaPay.CheckUserPromotion", err)
	}
	// [turnOver] ถ้ามีการฝากเงิน ให้สร้าง TurnOver
	if err := CreateTurnOverFromSuccessDeposit(repository.NewTurnoverRepository(s.sharedDb), user.Id, depositAmount, *transId); err != nil {
		log.Println("createCustomerDeposit.CreateTurnOverFromSuccessDeposit", err)
	}

	// ===================================================

	// [ notify]
	externalNoti.TypeNotify = model.IsDepositAfterCredit
	externalNoti.TransId = transId
	externalNoti.Amount = depositAmount
	externalNoti.MemberCode = user.MemberCode
	externalNoti.UserCredit = user.Credit + depositAmount
	externalNoti.ConfirmedByAdminId = 0
	externalNoti.TransferDateTime = time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02 15:04:05")
	externalNoti.ActionTime = time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02 15:04:05")
	endTime := time.Now()
	elapsed := endTime.Sub(actionAt)
	elapsedSeconds := elapsed.Seconds()
	timeElapsed := fmt.Sprintf("%.2f", elapsedSeconds)
	externalNoti.TimerCounter = timeElapsed

	externalNoti.WebScoket.UserID = user.Id
	externalNoti.WebScoket.Amount = depositAmount
	externalNoti.WebScoket.MemberCode = user.MemberCode
	externalNoti.WebScoket.AlertType = "DEPOSIT"
	if err := s.repo.UpdateAutoProcessTimer(timeElapsed, *transId); err != nil {
		return nil, nil
	}
	if err := s.notiService.ExternalNotification(externalNoti); err != nil {
		log.Println("FailedNotify", err)
	}
	return nil, nil
}

func approveCustomerWithdrawFromPapayaPay(repo repository.PapayaPayRepository, transId int64) (*int64, error) {

	withdrawTrans, err := repo.GetBankTransactionById(transId)
	if err != nil {
		log.Println("approveCustomerWithdrawFromPapayaPay.GetUserBankDetailById", err)
		return nil, internalServerError(err)
	}

	// ============================= ON_SUCCESS =================================
	if withdrawTrans.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		// [create transaction action success]
		var createConfirm model.CreateSuccessTransferWithdrawRequest
		createConfirm.TransactionId = withdrawTrans.Id
		createConfirm.ConfirmedAt = time.Now()
		var setIdAuto int64 = 0
		createConfirm.ConfirmedByAdminId = &setIdAuto
		if _, err := CreateSuccessTransferWithdraw(repository.NewBankingRepository(repo.GetDb()), createConfirm); err != nil {
			log.Println("approveCustomerWithdrawFromPapayaPay.CreateSuccessTransferWithdraw", err)
			return nil, nil
		}
		// [Show Withdraw USER_TRANSACTION]
		var showUserTrans model.UserTransactionShowUpdate
		showUserTrans.TransactionId = withdrawTrans.Id
		showUserTrans.ConfirmedAt = &createConfirm.ConfirmedAt
		showUserTrans.ConfirmAdminId = createConfirm.ConfirmedByAdminId
		if err := repo.ShowUserTransaction(showUserTrans); err != nil {
			log.Println("approveCustomerWithdrawFromPapayaPay.ShowUserTransaction", err)
			return nil, nil
		}
		// [update transaction status]
		secondUsed := "0.0" // timing
		if withdrawTrans.IsAutoCredit && withdrawTrans.TransferAt != nil {
			seconds := time.Now().UTC().Sub(withdrawTrans.TransferAt.UTC()).Seconds()
			secondUsed = fmt.Sprintf("%.2f", seconds)
		}
		if err := repo.UpdateTransactionStatusTransferingToSuccess(withdrawTrans.Id, secondUsed); err != nil {
			log.Println("approveCustomerWithdrawFromPapayaPay.UpdateTransactionStatusTransferingToSuccess", err)
			return nil, internalServerError(err)
		}

		// [notify SUCCESS]
		var externalNoti model.NotifyExternalNotificationRequest
		notiRepo := repository.NewNotificationRepository(repo.GetDb())
		externalNoti.TypeNotify = model.IsWithdrawalCreditSuccess
		externalNoti.TransId = &transId
		externalNoti.Amount = withdrawTrans.CreditAmount
		externalNoti.MemberCode = withdrawTrans.MemberCode
		externalNoti.UserCredit = withdrawTrans.CreditAmount
		externalNoti.ConfirmedByAdminId = 0
		externalNoti.TimerCounter = secondUsed
		externalNoti.TransferDateTime = withdrawTrans.TransferAt.Add(7 * time.Hour).Format("2006-01-02 15:04:05")
		externalNoti.ActionTime = time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02 15:04:05")
		if err := ExternalNotification(notiRepo, externalNoti); err != nil {
			log.Println("FailedNotify", err)
		}

	}
	return nil, nil
}

func rollbackCustomerWithdrawFromPapayaPay(repo repository.PapayaPayRepository, transId int64) (*int64, error) {

	withdrawTrans, err := repo.GetBankTransactionById(transId)
	if err != nil {
		log.Println("rollbackCustomerWithdrawFromPapayaPay.GetBankTransactionById", err)
		return nil, internalServerError(err)
	}

	// ============================= ON_SUCCESS =================================
	if withdrawTrans.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		// [update transaction status]
		if err := repo.RollbackTransactionStatusTransferingToConfirmed(withdrawTrans.Id); err != nil {
			log.Println("approveCustomerWithdrawFromPapayaPay.RollbackTransactionStatusTransferingToConfirmed", err)
			return nil, internalServerError(err)
		}
	}
	return nil, nil
}

func (s paygatePapayaPayService) CreateSystemLog(name string, req interface{}) error {

	// [SYSLOG] HACKED
	_, err := s.repo.CreateSystemLog(model.SystemLogCreateBody{
		Name:   name,
		Status: "HACKING",
		JsonRequest: helper.StructJson(map[string]interface{}{
			"req": req,
		}),
	})
	if err != nil {
		log.Println(name, "CreateSystemLog.ERROR", err.Error())
	}
	return nil
}
