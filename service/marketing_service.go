package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"strconv"
	"time"

	"gorm.io/gorm"
)

var cacheMenuCount *model.AlertMenuCountResponse

type MarketingService interface {
	// CONFIG
	GetUserIncomeMaxAmountConfig() (model.MarketingConfigResponse, error)
	SetUserIncomeMaxAmountConfig(body model.MarketingConfigUpdateRequest) error
	// MARKETING USER_INCOME
	GetUserIncomeLogList(query model.UserIncomeLogListRequest) ([]model.UserIncomeLogResponse, int64, error)
	GetUserIncomeLogListSummary(req model.UserIncomeLogTotalSummaryRequest) (*model.UserIncomeLogTotalSummaryResponse, error)
	GetUserIncomePendingCount() (*model.AlertMenuCountResponse, error)
	GetUserIncomePendingList(query model.UserIncomeLogListRequest) ([]model.UserIncomeLogResponse, int64, error)
	RacingConfirmUserIncome(id int64) (*int64, error)
	// TestRcConfirmUserIncome(id int64) error
	ConfirmUserIncome(id int64, adminId int64) error
	CancelUserIncome(id int64, adminId int64) error
	GetUserWinLoseSummary(query model.UserWinLoseSummaryReportRequest) (model.UserWinLoseSummaryReportResponse, error)
	GetUserWinLoseSummaryList(query model.UserWinLoseSummaryListRequest) ([]model.UserWinLoseSummaryResponse, int64, error)
	GetUserWinLoseDailySummary(query model.UserWinLoseDailySummaryRequest) (model.UserWinLoseSummaryReportResponse, error)
	GetUserWinLoseDailyList(query model.UserWinLoseDailyListRequest) ([]model.UserWinLoseDailyResponse, int64, error)
	GetUserTodayWinLoseSummary(query model.UserTodayWinLoseSummaryReportRequest) (model.UserWinLoseSummaryReportResponse, error)
	GetUserTodayWinLoseSummaryList(query model.UserTodayWinLoseSummaryListRequest) ([]model.UserWinLoseSummaryResponse, int64, error)
	// UserDailyTransactionReport
	GetTransactionReportDaily(query model.TransactionReportDailyQuery) (model.TransactionReportDailyResponse, error)
}

type marketingService struct {
	repo                      repository.MarketingRepository
	shareDb                   *gorm.DB
	activityLuckyWheelService ActivityLuckyWheelService
	serviceNoti               NotificationService
}

func NewMarketingService(
	repo repository.MarketingRepository,
	shareDb *gorm.DB,
	activityLuckyWheelService ActivityLuckyWheelService,
	serviceNoti NotificationService,
) MarketingService {
	return &marketingService{repo, shareDb, activityLuckyWheelService, serviceNoti}
}

func (s *marketingService) GetUserIncomeMaxAmountConfig() (model.MarketingConfigResponse, error) {

	var record model.MarketingConfigResponse
	record.UserIncomeMaxAmount = 500

	// TRY to get config, Default on error
	config, err := s.repo.GetMarketingConfigByKey("AUTO_USER_INCOME_MAX_AMOUNT", "500")
	if err == nil {
		// record.UserIncomeMaxAmount = config.ConfigVal convert to float64
		tempFloat, err := strconv.ParseFloat(config.ConfigVal, 64)
		if err == nil {
			record.UserIncomeMaxAmount = tempFloat
		}
	}
	return record, nil
}

func (s *marketingService) SetUserIncomeMaxAmountConfig(body model.MarketingConfigUpdateRequest) error {

	// Set Default
	oldSetting, err := s.GetUserIncomeMaxAmountConfig()
	if err != nil {
		return internalServerError(err)
	}

	if body.UserIncomeMaxAmount != nil {
		if err := s.repo.UpdateMarketingConfig("AUTO_USER_INCOME_MAX_AMOUNT", fmt.Sprintf("%f", *body.UserIncomeMaxAmount)); err != nil {
			return internalServerError(err)
		}
	}

	var adminActionCreateBody model.AdminActionCreateBody
	adminActionCreateBody.AdminId = body.UpdatedById
	adminActionCreateBody.TypeId = model.ADMIN_ACTION_MARKETING_CONFIG
	adminActionCreateBody.Detail = fmt.Sprintf("เปลี่ยน การตั้งค่าอนุมัติออโต้รายได้จาก %f เป็น %f", oldSetting.UserIncomeMaxAmount, *body.UserIncomeMaxAmount)
	adminActionCreateBody.IsSuccess = true
	adminActionCreateBody.IsShow = true
	adminActionCreateBody.JsonInput = helper.StructJson(body)
	adminActionCreateBody.JsonOutput = "update success"
	if _, err := s.repo.CreateAdminAction(adminActionCreateBody); err != nil {
		return err
	}

	return nil
}

func (s marketingService) GetUserIncomeLogList(query model.UserIncomeLogListRequest) ([]model.UserIncomeLogResponse, int64, error) {

	if err := helper.Pagination(&query.Page, &query.Limit); err != nil {
		return nil, 0, err
	}

	list, total, err := s.repo.GetUserIncomeLogList(query)
	if err != nil && err.Error() != "record not found" {
		return nil, 0, err
	}
	return list, total, nil
}

func (s marketingService) GetUserIncomeLogListSummary(req model.UserIncomeLogTotalSummaryRequest) (*model.UserIncomeLogTotalSummaryResponse, error) {

	list, err := s.repo.GetUserIncomeLogListSummary(req)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s marketingService) GetUserIncomePendingCount() (*model.AlertMenuCountResponse, error) {

	// Cache for 1 minute, Expire every 30 second
	if cacheMenuCount != nil && cacheMenuCount.CacheExpireAt.After(time.Now()) {
		return cacheMenuCount, nil
	}

	var result model.AlertMenuCountResponse

	count, err := s.repo.GetUserIncomePendingCount()
	if err != nil {
		return nil, err
	}
	result.PendingUserIncome = count

	count2, err := s.repo.GetHengOrderSuccessPendingCount()
	if err != nil {
		return nil, err
	}
	result.PendingHengOrder = count2

	depositPendingCount, err := s.repo.CountDepositPendingStatus()
	if err != nil {
		return nil, err
	}
	result.PendingDepositCount = depositPendingCount

	// CountWithdrawPendingStatus() (int64, error)
	withdrawPendingCount, err := s.repo.CountWithdrawPendingStatus()
	if err != nil {
		return nil, err
	}
	result.PendingWithdrawCount = withdrawPendingCount

	// Cache for 1 minute, Expire every 30 second
	result.CacheExpireAt = time.Now().Add(time.Second * 30)
	cacheMenuCount = &result

	return &result, nil
}

func (s marketingService) GetUserIncomePendingList(query model.UserIncomeLogListRequest) ([]model.UserIncomeLogResponse, int64, error) {

	if err := helper.Pagination(&query.Page, &query.Limit); err != nil {
		return nil, 0, err
	}

	list, total, err := s.repo.GetUserIncomePendingList(query)
	if err != nil && err.Error() != "record not found" {
		return nil, 0, err
	}
	return list, total, nil
}

func (s marketingService) RacingConfirmUserIncome(id int64) (*int64, error) {

	actionAt := time.Now()

	var createBody model.RaceActionCreateBody
	createBody.Name = "อนุมัติรายได้"
	createBody.JsonRequest = helper.StructJson(map[string]interface{}{"id": id})
	createBody.Status = "PENDING"

	// KEY = APPROVE_USER_INCOME_{total_second}_{ref_user_id}
	timing := actionAt.Format("200601021504")
	// only one action per five second
	timing += fmt.Sprintf("%02d", int64(float64(actionAt.Second()/5.0)*5))
	createBody.ActionKey = fmt.Sprintf("APPROVE_USER_INCOME_%s_%d", timing, id)
	createBody.UnlockAt = actionAt.Add(time.Second * 5)
	if _, err := s.repo.GetRaceActionIdByActionKey(createBody.ActionKey); err != nil {
		// log.Println("RacingConfirmUserIncome.ERROR.GetRaceActionByActionKey", err)
		if err != gorm.ErrRecordNotFound {
			return nil, internalServerError(errors.New("CANNOT_CHECK_ACTION"))
		}
	} else {
		return nil, internalServerError(errors.New("WORK_IN_ACTION"))
	}

	// only not found will be created
	actionId, err := s.repo.CreateRaceCondition(createBody)
	if err != nil {
		log.Println("RacingConfirmUserIncome.ERROR.CreateRaceCondition", err)
		return nil, internalServerError(errors.New("CANNOT_CREATE_ACTION"))
	}
	return &actionId, nil
}

func (s *marketingService) ConfirmUserIncome(id int64, adminId int64) error {

	actionAt := time.Now()

	income, err := s.repo.GetUserIncomeLogById(id)
	if err != nil {
		return internalServerError(err)
	}
	if income.StatusId == model.USER_INCOME_STATUS_PENDING && income.ConfirmBy == nil {

		returnAmount := income.CreditAmount

		// Income's Owner
		user, err := s.repo.GetUserMemberInfoById(income.UserId)
		if err != nil {
			return internalServerError(err)
		}
		// Confirm By
		admin, err := s.repo.GetAdminById(adminId)
		if err != nil {
			return err
		}

		if income.TypeId == model.USER_INCOME_TYPE_PROMOTION_RETURN_LOSS {
			// ** SAME AS (s *promotionReturnService) autoConfirmUserTakeReturnAmount()
			// 1.[USER_CREDIT] ** Do First **
			var userCreditReq model.UserTransactionCreateRequest
			userCreditReq.RefId = &income.Id
			userCreditReq.UserId = user.Id
			userCreditReq.Detail = "แจกโบนัสฟรี คืนยอดเสีย"
			userCreditReq.TypeId = model.CREDIT_TYPE_PROMOTION_RETURN_LOSS
			userCreditReq.BonusAmount = returnAmount
			creditTransferResp, err := s.repo.IncreaseUserCredit(userCreditReq)
			if err != nil {
				return internalServerError(err)
			}
			// [UserIncome]
			var confirmBody model.UserIncomeLogConfirmBody
			confirmBody.Id = income.Id
			confirmBody.CreditAfter = creditTransferResp.AgentAfterAmount
			confirmBody.TransferAt = creditTransferResp.TransferAt
			confirmBody.ConfirmBy = adminId
			confirmBody.ConfirmByName = admin.Username
			if err := s.repo.ConfirmUserIncomeLog(confirmBody); err != nil {
				// later : handle error ?
				return internalServerError(err)
			}
			// [turnOver] ตอนรับเงินคืน
			if err := MakeUserTidTurnPromotionReturnLoss(repository.NewTurnoverRepository(s.shareDb), user.Id, returnAmount); err != nil {
				// No error return
				return internalServerError(err)
			}
			// AddTo BankPendingRecord -> แจกโบนัสคืนยอดเสีย
			dashboardRepo := repository.NewDashboardRepository(s.shareDb)
			if err := ConfirmBankPendingRecordFromAny(dashboardRepo, model.BANK_PENDING_RECORD_TYPE_RETURN_LOSS, income.Id, creditTransferResp.TransferAt, admin.Username); err != nil {
				log.Println("ConfirmUserIncome.ConfirmBankPendingRecordFromAny", err)
			}
			// ** EOF SAME AS (s *promotionReturnService) autoConfirmUserTakeReturnAmount()
		} else if income.TypeId == model.USER_INCOME_TYPE_PROMOTION_RETURN_TURN {
			// ** SAME AS (s *promotionReturnService) autoConfirmUserTakeReturnAmount()
			// 1.[USER_CREDIT] ** Do First **
			var userCreditReq model.UserTransactionCreateRequest
			userCreditReq.RefId = &income.Id
			userCreditReq.UserId = user.Id
			// userCreditReq.Detail = "แจกโบนัสฟรี คืนยอดคอมมิชชั่น"
			// พี่อิทบอกให้เปลี่ยนเป็น คืนยอด commission แทน
			userCreditReq.Detail = "แจกโบนัสฟรี คืนยอดcommission"
			userCreditReq.TypeId = model.CREDIT_TYPE_PROMOTION_RETURN_TURN
			userCreditReq.BonusAmount = returnAmount
			creditTransferResp, err := s.repo.IncreaseUserCredit(userCreditReq)
			if err != nil {
				return internalServerError(err)
			}
			// [UserIncome]
			var confirmBody model.UserIncomeLogConfirmBody
			confirmBody.Id = income.Id
			confirmBody.CreditAfter = creditTransferResp.AgentAfterAmount
			confirmBody.TransferAt = creditTransferResp.TransferAt
			confirmBody.ConfirmBy = adminId
			confirmBody.ConfirmByName = admin.Username
			if err := s.repo.ConfirmUserIncomeLog(confirmBody); err != nil {
				// later : handle error ?
				return internalServerError(err)
			}
			// [turnOver] ตอนรับเงินคืน
			if err := MakeUserTidTurnPromotionReturnTurn(repository.NewTurnoverRepository(s.shareDb), user.Id, returnAmount); err != nil {
				// No error return
				return internalServerError(err)
			}
			// AddTo BankPendingRecord -> แจกโบนัสฟรี คืนยอดcommission
			dashboardRepo := repository.NewDashboardRepository(s.shareDb)
			if err := ConfirmBankPendingRecordFromAny(dashboardRepo, model.BANK_PENDING_RECORD_TYPE_RETURN_TURN, income.Id, creditTransferResp.TransferAt, admin.Username); err != nil {
				log.Println("ConfirmUserIncome.ConfirmBankPendingRecordFromAny", err)
			}
			// ** EOF SAME AS (s *promotionReturnService) autoConfirmUserTakeReturnAmount()
		} else if income.TypeId == model.USER_INCOME_TYPE_AFFILIATE {
			// [USER_CREDIT] Just only go to user credit
			var userCreditReq model.UserTransactionCreateRequest
			userCreditReq.RefId = &income.Id
			userCreditReq.UserId = income.UserId
			userCreditReq.TypeId = model.CREDIT_TYPE_AFFILIATE_INCOME
			userCreditReq.BonusAmount = returnAmount
			userCreditReq.StartWorkAt = actionAt // เริ่มนับตอนกดยินยัน
			creditTransferResp, err := s.repo.IncreaseUserCredit(userCreditReq)
			if err != nil {
				return internalServerError(err)
			}
			// [UserIncome]
			var confirmBody model.UserIncomeLogConfirmBody
			confirmBody.Id = income.Id
			confirmBody.CreditAfter = creditTransferResp.AgentAfterAmount
			confirmBody.TransferAt = creditTransferResp.TransferAt
			confirmBody.ConfirmBy = adminId
			confirmBody.ConfirmByName = admin.Username
			if err := s.repo.ConfirmUserIncomeLog(confirmBody); err != nil {
				return internalServerError(err)
			}
			if rcWithdraw, err := s.repo.GetAffTransactionWithdrawById(*income.RefId); err == nil {
				// [AffTransaction] set waitConfirm to TRANSFERRED
				// unmarchel rcWithdraw.JsonRelatedIds to []int64
				waitIds := make([]int64, 0)
				if errJson := json.Unmarshal([]byte(rcWithdraw.JsonRelatedIds), &waitIds); errJson == nil {
					if err := s.repo.SetConfirmAffTransactionList(waitIds); err != nil {
						return internalServerError(err)
					}
				}
			}
			// AddTo BankPendingRecord -> แจกโบนัสแนะนำเพื่อน
			dashboardRepo := repository.NewDashboardRepository(s.shareDb)
			if err := ConfirmBankPendingRecordFromAny(dashboardRepo, model.BANK_PENDING_RECORD_TYPE_BONUS_AFF, income.Id, creditTransferResp.TransferAt, admin.Username); err != nil {
				log.Println("ConfirmUserIncome.ConfirmBankPendingRecordFromAny", err)
			}
		} else if income.TypeId == model.USER_INCOME_TYPE_ALLIANCE {
			log.Println("ALLIANCE_HAS_TOBE_CONFIRMED")
			// [UserIncome]
			var confirmIncomeBody model.UserIncomeLogConfirmBody
			confirmIncomeBody.Id = income.Id
			confirmIncomeBody.CreditAfter = 0
			// Auto Confirm By Admin - Admin is creater
			confirmIncomeBody.TransferAt = actionAt
			confirmIncomeBody.ConfirmBy = admin.Id
			confirmIncomeBody.ConfirmByName = admin.Fullname

			// [********] โยกเคลียร์ยอดพันธมิตร แบบโอนเครดิตได้ด้วย
			var userCreditReq model.UserTransactionCreateRequest
			userCreditReq.RefId = &income.Id
			userCreditReq.UserId = income.UserId
			userCreditReq.TransferAt = &actionAt
			userCreditReq.TypeId = model.CREDIT_TYPE_ALLIANCE_INCOME
			userCreditReq.BonusAmount = returnAmount
			IsShow := true
			userCreditReq.IsShow = &IsShow
			userCreditReq.ConfirmBy = &admin.Id
			if agentResp, err := s.repo.IncreaseUserCredit(userCreditReq); err != nil {
				log.Println("WithdrawAllianceIncome.IncreaseUserCredit", err)
				return internalServerError(err)
			} else if agentResp.AgentSuccess {
				confirmIncomeBody.CreditAfter = agentResp.AgentAfterAmount
			} else {
				agentFail := errors.New("ทำรายการเครดิตไม่สำเร็จ")
				log.Println("WithdrawAllianceIncome.IncreaseUserCredit", agentFail)
				return internalServerError(agentFail)
			}
			if err := s.repo.ConfirmUserIncomeLog(confirmIncomeBody); err != nil {
				// later : handle error ?
				return internalServerError(err)
			}
			// AddTo BankPendingRecord -> รายได้พันธมิตร
			dashboardRepo := repository.NewDashboardRepository(s.shareDb)
			if err := ConfirmBankPendingRecordFromAny(dashboardRepo, model.BANK_PENDING_RECORD_TYPE_ALLIANCE_INCOME, income.Id, actionAt, admin.Username); err != nil {
				log.Println("ConfirmUserIncome.ConfirmBankPendingRecordFromAny", err)
			}
			log.Println("ALLIANCE_INCOME_CONFIRMED")
		} else if income.TypeId == model.USER_INCOME_TYPE_LUCKY_WHEEL {
			var cleanUpDataLuckyWheel = make(map[string]interface{})
			returnAmount := income.CreditAmount
			user, err := s.repo.GetUserMemberInfoById(income.UserId)
			if err != nil {
				return internalServerError(err)
			}

			luckyWheelUser, err := s.repo.GetActivityLuckyWheelRoundUserByInComeId(income.Id)
			if err != nil {
				return internalServerError(err)
			}
			if returnAmount > 0 {

				// [Confirm User Income]
				var confirmActivityBody model.ActivityLuckyWheelRoundConfirmCreateRequest
				confirmActivityBody.UserId = luckyWheelUser.UserId
				confirmActivityBody.ActionKey = fmt.Sprintf("ALWRC_%v", income.Id)
				confirmActivityBody.LuckyWheelUserId = luckyWheelUser.Id
				confirmActivityBody.ConditionAmount = *luckyWheelUser.Reward
				checkConfirm, _ := s.repo.GetActivityLuckyWheelRoundConfirmByKey(confirmActivityBody.ActionKey)

				createConfirmId, err := s.repo.CreateActivityLuckyWheelRoundConfirm(confirmActivityBody)
				if err != nil {
					return internalServerError(err)
				}
				cleanUpDataLuckyWheel["createConfirmId"] = createConfirmId
				if createConfirmId > 0 && checkConfirm == nil {
					// [UPDATE ACTIVITY_LUCKY_WHEEL_ROUND_USER] receive prize
					bonusAmount := returnAmount
					var updateBody model.UpdateActivityLuckyWheelRoundUserRequest
					updateBody.Id = luckyWheelUser.Id
					updateBody.Reward = &bonusAmount
					status := model.ACTIVITY_LUCKY_WHEEL_ROUND_STATUS_PRIZE_RECEIVED
					updateBody.StatusId = &status

					if err := s.repo.UpdateActivityLuckyWheelRoundUser(updateBody); err != nil {
						// rollback
						if err := s.CleanUpActivityLuckyWheel(cleanUpDataLuckyWheel); err != nil {
							return internalServerError(err)
						}
						return internalServerError(err)
					}
					cleanUpDataLuckyWheel["luckyWheelUser"] = luckyWheelUser.Id

					// [INCREASE CREDIT]
					var userCreditReq model.UserTransactionCreateRequest
					userCreditReq.RefId = &income.Id
					userCreditReq.UserId = income.UserId
					userCreditReq.Detail = "โบนัสหมุนกงล้อนำโชค"
					userCreditReq.TypeId = model.CREDIT_TPYE_LUCKY_WHEEL // เพิ่ม type
					userCreditReq.BonusAmount = income.CreditAmount
					creditTransferResp, err := s.repo.IncreaseUserCredit(userCreditReq)
					if err != nil {
						if err := s.CleanUpActivityLuckyWheel(cleanUpDataLuckyWheel); err != nil {
							return internalServerError(err)
						}
						return internalServerError(err)
					}
					// [CONFIRM USER INCOME]
					var confirmBody model.UserIncomeLogConfirmBody
					confirmBody.Id = income.Id
					confirmBody.CreditAfter = creditTransferResp.AgentAfterAmount
					confirmBody.TransferAt = creditTransferResp.TransferAt
					confirmBody.ConfirmBy = adminId
					confirmBody.ConfirmByName = admin.Fullname
					if err := s.repo.ConfirmUserIncomeLog(confirmBody); err != nil {
						if err := s.CleanUpActivityLuckyWheel(cleanUpDataLuckyWheel); err != nil {
							return internalServerError(err)
						}
						return internalServerError(err)
					}
				}
				// AddTo BankPendingRecord -> รายได้จากกิจกรรมกงล้อนำโชค
				dashboardRepo := repository.NewDashboardRepository(s.shareDb)
				if err := ConfirmBankPendingRecordFromAny(dashboardRepo, model.BANK_PENDING_RECORD_TYPE_LUCKY_WHEEL, income.Id, actionAt, admin.Username); err != nil {
					log.Println("ConfirmUserIncome.ConfirmBankPendingRecordFromAny", err)
				}
			} else {
				// แก้บัคที่เคยเข้ามาเป็น 0
				bonusAmount := float64(0)
				var updateBody model.UpdateActivityLuckyWheelRoundUserRequest
				updateBody.Id = luckyWheelUser.Id
				updateBody.Reward = &bonusAmount
				status := model.ACTIVITY_LUCKY_WHEEL_ROUND_STATUS_PRIZE_RECEIVED
				updateBody.StatusId = &status

				if err := s.repo.UpdateActivityLuckyWheelRoundUser(updateBody); err != nil {
					return internalServerError(err)
				}

				// [CONFIRM USER INCOME]
				var confirmBody model.UserIncomeLogConfirmBody
				confirmBody.Id = income.Id
				confirmBody.CreditAfter = user.Credit
				confirmBody.TransferAt = time.Now().UTC()
				confirmBody.ConfirmBy = adminId
				confirmBody.ConfirmByName = admin.Fullname
				if err := s.repo.ConfirmUserIncomeLog(confirmBody); err != nil {
					return internalServerError(err)
				}
				// AddTo BankPendingRecord -> รายได้จากกิจกรรมกงล้อนำโชค
				dashboardRepo := repository.NewDashboardRepository(s.shareDb)
				if err := ConfirmBankPendingRecordFromAny(dashboardRepo, model.BANK_PENDING_RECORD_TYPE_LUCKY_WHEEL, income.Id, actionAt, admin.Username); err != nil {
					log.Println("ConfirmUserIncome.ConfirmBankPendingRecordFromAny", err)
				}
				// return notFound("NO_RETURN_DATA")
			}
		}
		// [LINE]
		var externalNoti model.NotifyExternalNotificationRequest
		externalNoti.TypeNotify = model.ActitvityAfterBonus
		externalNoti.MemberCode = *user.MemberCode
		externalNoti.BonusCredit = &returnAmount
		externalNoti.UserCredit = user.Credit + returnAmount
		externalNoti.ConfirmedByAdminId = adminId
		if err := s.serviceNoti.ExternalNotification(externalNoti); err != nil {
			log.Println("FailedNotify", err)
		}
	} else {
		return errors.New("INVALID_TRANSACTION_STATUS")
	}
	return nil
}

func (s *marketingService) CancelUserIncome(id int64, adminId int64) error {

	actionAt := time.Now()

	income, err := s.repo.GetUserIncomeLogById(id)
	if err != nil {
		return internalServerError(err)
	}
	if income.StatusId == model.USER_INCOME_STATUS_PENDING && income.ConfirmBy == nil {

		// Confirm By
		admin, err := s.repo.GetAdminById(adminId)
		if err != nil {
			return err
		}

		// [UserIncome]
		var confirmBody model.UserIncomeLogConfirmBody
		confirmBody.Id = income.Id
		confirmBody.CreditAfter = 0
		confirmBody.TransferAt = actionAt
		confirmBody.ConfirmBy = adminId
		confirmBody.ConfirmByName = admin.Username
		if err := s.repo.CancelUserIncomeLog(confirmBody); err != nil {
			// later : handle error ?
			return internalServerError(err)
		}

		// AddTo BankPendingRecord -> Any
		pendingRecordType := int64(0)
		switch income.TypeId {
		case model.USER_INCOME_TYPE_PROMOTION_RETURN_LOSS:
			pendingRecordType = model.BANK_PENDING_RECORD_TYPE_RETURN_LOSS
		case model.USER_INCOME_TYPE_PROMOTION_RETURN_TURN:
			pendingRecordType = model.BANK_PENDING_RECORD_TYPE_RETURN_TURN
		case model.USER_INCOME_TYPE_AFFILIATE:
			pendingRecordType = model.BANK_PENDING_RECORD_TYPE_BONUS_AFF
		case model.USER_INCOME_TYPE_ALLIANCE:
			pendingRecordType = model.BANK_PENDING_RECORD_TYPE_ALLIANCE_INCOME
		case model.USER_INCOME_TYPE_LUCKY_WHEEL:
			pendingRecordType = model.BANK_PENDING_RECORD_TYPE_LUCKY_WHEEL
		}
		dashboardRepo := repository.NewDashboardRepository(s.shareDb)
		if err := RejectBankPendingRecordFromAny(dashboardRepo, pendingRecordType, income.Id, actionAt, admin.Username); err != nil {
			log.Println("CancelUserIncome.RejectBankPendingRecordFromAny", err)
		}

	} else {
		return errors.New("INVALID_TRANSACTION_STATUS")
	}
	return nil
}

func (s marketingService) GetUserWinLoseSummary(query model.UserWinLoseSummaryReportRequest) (model.UserWinLoseSummaryReportResponse, error) {

	var result model.UserWinLoseSummaryReportResponse

	data, err := s.repo.GetUserWinLoseSummary(query)
	if err != nil {
		return result, err
	}

	result.TotalTurnOver = data.TotalTurnOver
	result.TotalDiffAmount = data.TotalDiffAmount
	result.TotalWinLose = data.TotalWinLose

	return result, nil
}

func (s marketingService) GetUserWinLoseSummaryList(query model.UserWinLoseSummaryListRequest) ([]model.UserWinLoseSummaryResponse, int64, error) {

	if err := helper.Pagination(&query.Page, &query.Limit); err != nil {
		return nil, 0, err
	}

	list, total, err := s.repo.GetUserWinLoseSummaryList(query)
	if err != nil && err.Error() != "record not found" {
		return nil, 0, err
	}
	return list, total, nil
}

func (s marketingService) GetUserWinLoseDailySummary(query model.UserWinLoseDailySummaryRequest) (model.UserWinLoseSummaryReportResponse, error) {

	var result model.UserWinLoseSummaryReportResponse

	data, err := s.repo.GetUserWinLoseDailySummary(query)
	if err != nil {
		return result, err
	}

	result.TotalTurnOver = data.TotalTurnOver
	result.TotalDiffAmount = data.TotalDiffAmount
	result.TotalWinLose = data.TotalWinLose

	return result, nil
}

func (s marketingService) GetUserWinLoseDailyList(query model.UserWinLoseDailyListRequest) ([]model.UserWinLoseDailyResponse, int64, error) {

	if err := helper.Pagination(&query.Page, &query.Limit); err != nil {
		return nil, 0, err
	}

	list, total, err := s.repo.GetUserWinLoseDailyList(query)
	if err != nil && err.Error() != "record not found" {
		return nil, 0, err
	}
	return list, total, nil
}

func (s marketingService) GetUserTodayWinLoseSummary(query model.UserTodayWinLoseSummaryReportRequest) (model.UserWinLoseSummaryReportResponse, error) {

	var result model.UserWinLoseSummaryReportResponse

	data, err := s.repo.GetUserTodayWinLoseSummary(query)
	if err != nil {
		return result, err
	}

	result.TotalTurnOver = data.TotalTurnOver
	result.TotalDiffAmount = data.TotalDiffAmount
	result.TotalWinLose = data.TotalWinLose

	return result, nil
}

func (s marketingService) GetUserTodayWinLoseSummaryList(query model.UserTodayWinLoseSummaryListRequest) ([]model.UserWinLoseSummaryResponse, int64, error) {

	if err := helper.Pagination(&query.Page, &query.Limit); err != nil {
		return nil, 0, err
	}

	list, total, err := s.repo.GetUserTodayWinLoseSummaryList(query)
	if err != nil && err.Error() != "record not found" {
		return nil, 0, err
	}
	return list, total, nil
}

func (s *marketingService) CleanUpActivityLuckyWheel(cleanUpData map[string]interface{}) error {

	for k, v := range cleanUpData {
		switch k {
		case "createConfirmId":
			if err := s.repo.RollBackConfirmActionConfirmActivityLuckyWheel(v.(int64)); err != nil {
				return err
			}
		case "luckyWheelUser":
			if err := s.repo.RollBackActivityLuckyWheelUser(v.(int64)); err != nil {
				return err
			}

		}
	}
	return nil
}

func (s marketingService) GetTransactionReportDaily(query model.TransactionReportDailyQuery) (model.TransactionReportDailyResponse, error) {

	var empty model.TransactionReportDailyResponse

	if err := helper.Pagination(&query.Page, &query.Limit); err != nil {
		return empty, err
	}

	result, err := s.repo.GetTransactionReportDaily(query)
	if err != nil {
		return empty, err
	}
	return result, nil
}
