package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"fmt"
	"log"
	"os"
	"strings"
)

type StaffService interface {
	StaffGetList() (*model.StaffListResponse, error)
	StaffDetail(staffId int64) (*model.StaffDetail, error)
	StaffCreate(staff model.StaffBody) error
	StaffUpdate(staffId int64, body model.StaffUpdateBody) error
	StaffUpdateSortOrder(list model.StaffSortBody) error
	StaffDelete(staffId int64) error
}

type StaffServiceRepos struct {
	repo     repository.StaffRepository
	FileRepo repository.FileRepository
}

func NewStaffService(
	repo repository.StaffRepository,
	FileRepo repository.FileRepository,
) StaffService {
	return &StaffServiceRepos{repo, FileRepo}
}

func (s StaffServiceRepos) StaffGetList() (*model.StaffListResponse, error) {

	staffList, err := s.repo.GetStaffList()
	if err != nil {

		if err.Error() == recordNotFound {
			return nil, notFound(staffNotFound)
		}

		return nil, internalServerError(err)
	}

	result := &model.StaffListResponse{}

	for _, staff := range staffList {
		if staff.Type == "MANAGEMENT" {
			result.ManagementList = append(result.ManagementList, staff)
		} else {
			result.StaffList = append(result.StaffList, staff)
		}
	}

	return result, nil
}

func (s StaffServiceRepos) StaffDetail(staffId int64) (*model.StaffDetail, error) {

	staff, err := s.repo.GetStaffById(staffId)
	if err != nil {

		if err.Error() == recordNotFound {
			return nil, notFound(staffNotFound)
		}

		return nil, internalServerError(err)
	}

	return staff, nil
}

func (s StaffServiceRepos) StaffCreate(body model.StaffBody) error {

	if err := s.repo.CreateStaff(body); err != nil {
		return internalServerError(err)
	}

	return nil
}

func (s StaffServiceRepos) StaffUpdate(staffId int64, body model.StaffUpdateBody) error {

	getCoverUrl, err := s.repo.GetCoverUrlByStaffId(staffId)
	if err != nil {
		return err
	}

	if getCoverUrl != "" && getCoverUrl != body.StaffUrl {
		s.deleteCoverUrl(getCoverUrl, staffId)
	}

	if err := s.repo.UpdateStaff(staffId, body); err != nil {
		return internalServerError(err)
	}

	return nil
}

func (s StaffServiceRepos) StaffDelete(staffId int64) error {

	getCoverUrl, err := s.repo.GetCoverUrlByStaffId(staffId)
	if err != nil {
		return err
	}

	s.deleteCoverUrl(getCoverUrl, staffId)

	if err := s.repo.DeleteStaff(staffId); err != nil {
		return internalServerError(err)
	}

	return nil
}

func (s StaffServiceRepos) StaffUpdateSortOrder(list model.StaffSortBody) error {

	if len(list.List) == 0 {
		return badRequest(staffListIsRequire)
	}

	for _, item := range list.List {
		if item.SortOrder == 0 {
			return badRequest(staffSortOrderNotZero)
		}
	}

	if err := s.repo.UpdateStaffSortOrder(list); err != nil {
		return internalServerError(err)
	}

	return nil
}

func (s StaffServiceRepos) deleteCoverUrl(oldCoverUrl string, staffId int64) {

	bucketName := os.Getenv("BUCKET_NAME")

	if oldCoverUrl != "" && helper.FindString("https://storage.googleapis.com", oldCoverUrl) {

		url := fmt.Sprintf("https://storage.googleapis.com/%s/", bucketName)

		slicePath := strings.Split(oldCoverUrl, url)
		slicePath[1] = strings.Replace(slicePath[1], "%2F", "/", -1)
		slicePath[1] = strings.Replace(slicePath[1], "%20", " ", -1)
		if err := s.FileRepo.DeleteFile(slicePath[1]); err != nil {
			log.Println("delete file error : ", oldCoverUrl)
			return
		}
	}
}
