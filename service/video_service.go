package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
)

type VideoService interface {
	VideoGetList(query model.VideoQuery) (*model.VideoListResponse, error)
	VideoDetail(videoId int64) (*model.VideoDetail, error)
	VideoCreate(video model.VideoBody) error
	VideoUpdate(videoId int64, body model.VideoUpdateBody) error
	VideoDelete(videoId int64) error
}

type VideoServiceRepos struct {
	repo      repository.VideoRepository
	FileRepo  repository.FileRepository
	CacheRepo repository.CacheRepository
}

func NewVideoService(
	repo repository.VideoRepository,
	FileRepo repository.FileRepository,
	CacheRepo repository.CacheRepository,
) VideoService {
	return &VideoServiceRepos{repo, FileRepo, CacheRepo}
}

func (s VideoServiceRepos) VideoGetList(query model.VideoQuery) (*model.VideoListResponse, error) {

	if err := helper.Pagination(&query.Page, &query.Limit); err != nil {
		return nil, err
	}

	response := &model.VideoListResponse{}

	// if query.Page == 0 && query.Limit == 10 {
	// 	if s.CacheRepo.Get("video_list-10", &response) {
	// 		return response, nil
	// 	}
	// }

	// if query.Page == 0 && query.Limit == 6 {
	// 	if s.CacheRepo.Get("video_list-6", &response) {
	// 		return response, nil
	// 	}
	// }

	videoList, total, err := s.repo.GetVideoList(query)
	if err != nil {

		if err.Error() == recordNotFound {
			return nil, notFound(videoNotFound)
		}

		return nil, internalServerError(err)
	}

	response.List = videoList
	response.Total = total

	// if query.Page == 0 && query.Limit == 10 {
	// 	s.CacheRepo.Set("video_list-10", response, 0)
	// }

	// if query.Page == 0 && query.Limit == 6 {
	// 	s.CacheRepo.Set("video_list-6", response, 0)
	// }

	return response, nil
}

func (s VideoServiceRepos) VideoDetail(videoId int64) (*model.VideoDetail, error) {

	video, err := s.repo.GetVideoById(videoId)
	if err != nil {

		if err.Error() == recordNotFound {
			return nil, notFound(videoNotFound)
		}

		return nil, internalServerError(err)
	}

	return video, nil
}

func (s VideoServiceRepos) VideoCreate(body model.VideoBody) error {

	youtubeId, err := helper.GetYoutubeId(body.YoutubeUrl)
	if err != nil {
		return badRequest(err.Error())
	}

	url := "https://www.youtube.com/watch?v=" + youtubeId
	body.CoverUrl = "https://img.youtube.com/vi/" + youtubeId + "/0.jpg"

	youtube, err := helper.GetYoutubeContent(url)
	if err != nil {
		return internalServerError(err)
	}

	body.Title = youtube.Title

	if err := s.repo.CreateVideo(body); err != nil {
		return internalServerError(err)
	}

	s.CacheRepo.Delete("video_list")

	return nil
}

func (s VideoServiceRepos) VideoUpdate(videoId int64, body model.VideoUpdateBody) error {

	youtube, err := s.repo.GetVideoById(videoId)
	if err != nil {
		return internalServerError(err)
	}

	if youtube == nil {
		return notFound(videoNotFound)
	}

	if body.YoutubeUrl != "" && body.YoutubeUrl != youtube.YoutubeUrl {
		youtubeId, err := helper.GetYoutubeId(body.YoutubeUrl)
		if err != nil {
			return badRequest(err.Error())
		}

		url := "https://www.youtube.com/watch?v=" + youtubeId
		body.CoverUrl = "https://img.youtube.com/vi/" + youtubeId + "/0.jpg"

		youtube, err := helper.GetYoutubeContent(url)
		if err != nil {
			return internalServerError(err)
		}

		body.Title = youtube.Title
	}

	if err := s.repo.UpdateVideo(videoId, body); err != nil {
		return internalServerError(err)
	}

	s.CacheRepo.Delete("video_list")

	return nil
}

func (s VideoServiceRepos) VideoDelete(videoId int64) error {

	if err := s.repo.DeleteVideo(videoId); err != nil {
		return internalServerError(err)
	}

	s.CacheRepo.Delete("video_list")

	return nil
}
