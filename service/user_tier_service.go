package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"fmt"
)

type UserTierService interface {
	GetUserListTierSetting() (*model.UserListTierSettingResponse, error)
	// Tier Setting
	GetUserTierSettingByDepositList() (*model.UserTierSettingResponse, error)
	UpdateUserTierSettingByDeposit(req model.UserTierByDepositSettingUpdateRequest) error
	GetUserTierSettingByTurnOverList() (*model.UserTierSettingResponse, error)
	UpdateUserTierSettingByTurnOver(req model.UserTierByTurnOverSettingUpdateRequest) error
	// UserData
	IncreaseUserTierDepositAmount(userId int64, amount float64) error
	IncreaseUserTierTurnoverAmount(userId int64, amount float64) error
}

type userTierService struct {
	repo repository.UserTierRepository
}

func NewUserTierService(
	repo repository.UserTierRepository,
) UserTierService {
	return &userTierService{repo}
}

func (s userTierService) GetUserListTierSetting() (*model.UserListTierSettingResponse, error) {

	var result model.UserListTierSettingResponse

	tierSetting, err := s.repo.GetUserListTierSetting()
	if err != nil {
		return &result, nil
	}

	// ENV enable Tier Setting
	// tierSetting, err := s.repo.GetTierSetting("is_deposit_tier_setting")
	// if err == nil && tierSetting.Id != 0 {
	// 	if tierSetting.FromValue == 1 {
	// 		result.IsEnvEnabled = true
	// 	}
	// }

	// if result.IsEnvEnabled {
	// 	tierSetting2, err := s.repo.GetTierSetting("is_deposit_tier_setting")
	// 	if err == nil && tierSetting2.FromValue == 1 {
	// 		result.IsEnvEnabled = true
	// 	}
	// 	tierSetting3, err := s.repo.GetTierSetting("is_turnover_tier_setting")
	// 	if err == nil && tierSetting3.FromValue == 1 {
	// 		result.IsEnvEnabled = true
	// 	}
	// }

	return tierSetting, nil
}

func (s userTierService) GetUserTierSettingByDepositList() (*model.UserTierSettingResponse, error) {

	var result model.UserTierSettingResponse
	result.Type = model.USERTIER_TYPE_DEPOSIT

	// ENV enable Tier Setting
	tierSetting, err := s.repo.GetTierSetting("is_deposit_tier_setting")
	if err == nil && tierSetting.Id != 0 {
		if tierSetting.FromValue == 1 {
			result.IsEnabled = true
		} else {
			result.IsEnabled = false
		}
	} else {
		result.IsEnabled = false
	}

	var req model.UserTierSettingListRequest
	req.Type = model.USERTIER_TYPE_DEPOSIT
	if err := helper.Pagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	list, _, err := s.repo.GetUserTierSettingList(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	result.List = list
	return &result, nil
}

func (s userTierService) GetUserTierSettingByTurnOverList() (*model.UserTierSettingResponse, error) {

	var result model.UserTierSettingResponse
	result.Type = model.USERTIER_TYPE_TURNOVER

	// ENV enable Tier Setting
	tierSetting, err := s.repo.GetTierSetting("is_turnover_tier_setting")
	if err == nil && tierSetting.Id != 0 {
		if tierSetting.FromValue == 1 {
			result.IsEnabled = true
		} else {
			result.IsEnabled = false
		}
	} else {
		result.IsEnabled = false
	}

	var req model.UserTierSettingListRequest
	req.Type = model.USERTIER_TYPE_TURNOVER
	if err := helper.Pagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	list, _, err := s.repo.GetUserTierSettingList(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	result.List = list
	return &result, nil
}

func (s userTierService) UpdateUserTierSettingByDeposit(req model.UserTierByDepositSettingUpdateRequest) error {

	workingType := model.USERTIER_TYPE_DEPOSIT

	// ENV enable Tier Setting
	tierSetting, err := s.repo.GetUserTierSetting("is_deposit_tier_setting")
	if err != nil {
		return internalServerError(err)
	}
	if req.IsEnabled != (tierSetting.FromValue == 1) {
		// Update Enable
		var updateBody1 model.UserTierSettingUpdateBody
		isEnabled := int64(0)
		if req.IsEnabled {
			isEnabled = int64(1)
		}
		updateBody1.FromValue = &isEnabled
		if err := s.repo.UpdateUserTierSetting(tierSetting.Id, updateBody1); err != nil {
			return internalServerError(err)
		}
	}

	var query model.UserTierSettingListRequest
	query.Type = workingType
	if err := helper.Pagination(&query.Page, &query.Limit); err != nil {
		return badRequest(err.Error())
	}
	list, _, err := s.repo.GetUserTierSettingList(query)
	if err != nil {
		return internalServerError(err)
	}

	var mapSortOrder = make(map[int]model.UserTierSettingItem)
	for _, item := range list {
		mapSortOrder[item.SortOrder] = item
	}

	var createBody []model.UserTierSettingCreateBody
	updateBody := make(map[int]model.UserTierSettingUpdateBody)
	var deleteBody []int64

	for i, item := range req.List {
		if _, ok := mapSortOrder[item.SortOrder]; ok {
			// Update
			var tempRow model.UserTierSettingUpdateBody
			// tempRow.Type = &workingType
			// tempRow.SortOrder = &item.SortOrder
			tempRow.Name = &req.List[i].Name
			tempRow.FromValue = &req.List[i].FromValue
			tempRow.ToValue = &req.List[i].ToValue
			updateBody[item.SortOrder] = tempRow
			delete(mapSortOrder, item.SortOrder)
		} else {
			// Create
			var tempRow model.UserTierSettingCreateBody
			tempRow.Type = workingType
			tempRow.SortOrder = item.SortOrder
			tempRow.Name = item.Name
			tempRow.FromValue = item.FromValue
			tempRow.ToValue = item.ToValue
			createBody = append(createBody, tempRow)
			delete(mapSortOrder, item.SortOrder)
		}
	}

	if len(createBody) > 0 {
		if err := s.repo.CreateUserTierSettingBulk(createBody); err != nil {
			return internalServerError(err)
		}
	}
	if len(updateBody) > 0 {
		for sortOrder, item := range updateBody {
			if err := s.repo.UpdateUserTierDepositSettingBySortId(int64(sortOrder), item); err != nil {
				return internalServerError(err)
			}
		}
	}
	if len(mapSortOrder) > 0 {
		for _, item := range mapSortOrder {
			deleteBody = append(deleteBody, item.Id)
		}
		if err := s.repo.DeleteUserTierSettingByIds(deleteBody); err != nil {
			return internalServerError(err)
		}
	}

	return nil
}

func (s userTierService) UpdateUserTierSettingByTurnOver(req model.UserTierByTurnOverSettingUpdateRequest) error {

	workingType := model.USERTIER_TYPE_TURNOVER

	// ENV enable Tier Setting
	tierSetting, err := s.repo.GetUserTierSetting("is_turnover_tier_setting")
	if err != nil {
		return internalServerError(err)
	}
	fmt.Println("req", helper.StructJson(req))
	fmt.Println("tierSetting", helper.StructJson(tierSetting))

	if req.IsEnabled != (tierSetting.FromValue == 1) {
		// Update Enable
		var updateBody1 model.UserTierSettingUpdateBody
		isEnabled := int64(0)
		if req.IsEnabled {
			isEnabled = int64(1)
		}
		updateBody1.FromValue = &isEnabled
		if err := s.repo.UpdateUserTierSetting(tierSetting.Id, updateBody1); err != nil {
			return internalServerError(err)
		}
	}

	var query model.UserTierSettingListRequest
	query.Type = workingType
	if err := helper.Pagination(&query.Page, &query.Limit); err != nil {
		return badRequest(err.Error())
	}
	list, _, err := s.repo.GetUserTierSettingList(query)
	if err != nil {
		return internalServerError(err)
	}

	var mapSortOrder = make(map[int]model.UserTierSettingItem)
	for _, item := range list {
		mapSortOrder[item.SortOrder] = item
	}

	var createBody []model.UserTierSettingCreateBody
	updateBody := make(map[int]model.UserTierSettingUpdateBody)
	var deleteBody []int64

	for i, item := range req.List {
		if _, ok := mapSortOrder[item.SortOrder]; ok {
			// Update
			var tempRow model.UserTierSettingUpdateBody
			// tempRow.Type = &workingType
			// tempRow.SortOrder = &item.SortOrder
			tempRow.Name = &req.List[i].Name
			tempRow.FromValue = &req.List[i].FromValue
			tempRow.ToValue = &req.List[i].ToValue
			updateBody[item.SortOrder] = tempRow
			delete(mapSortOrder, item.SortOrder)
		} else {
			// Create
			var tempRow model.UserTierSettingCreateBody
			tempRow.Type = workingType
			tempRow.SortOrder = item.SortOrder
			tempRow.Name = item.Name
			tempRow.FromValue = item.FromValue
			tempRow.ToValue = item.ToValue
			createBody = append(createBody, tempRow)
			delete(mapSortOrder, item.SortOrder)
		}
	}

	if len(createBody) > 0 {
		if err := s.repo.CreateUserTierSettingBulk(createBody); err != nil {
			return internalServerError(err)
		}
	}
	if len(updateBody) > 0 {
		for sortOrder, item := range updateBody {
			if err := s.repo.UpdateUserTierTurnOverSettingBySortId(int64(sortOrder), item); err != nil {
				return internalServerError(err)
			}
		}
	}
	if len(mapSortOrder) > 0 {
		for _, item := range mapSortOrder {
			deleteBody = append(deleteBody, item.Id)
		}
		if err := s.repo.DeleteUserTierSettingByIds(deleteBody); err != nil {
			return internalServerError(err)
		}
	}
	return nil
}

func (s userTierService) IncreaseUserTierDepositAmount(userId int64, amount float64) error {

	if err := s.repo.IncreaseUserTierDepositAmount(userId, amount); err != nil {
		return internalServerError(err)
	}
	return nil
}

func (s userTierService) IncreaseUserTierTurnoverAmount(userId int64, amount float64) error {

	if err := s.repo.IncreaseUserTierTurnoverAmount(userId, amount); err != nil {
		return internalServerError(err)
	}
	return nil
}
