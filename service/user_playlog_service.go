package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"errors"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"gorm.io/gorm"
)

type UserPlaylogService interface {
	// MANUAL by DATE
	RunGetUserPlayLogByDate(req model.CronCreatePlayLogRequest) error
	// CRON=TODAY = REALTIME = TURNOVER
	RunGetUserTodayPlayLog() error
	// SUM REPORT
	RunTodayAffiliateUserList() error
	// TEST=RD
	RunGetUserPlayLog() error
}

type userPlaylogService struct {
	repo repository.UserPlaylogRepository
}

func NewUserPlaylogService(
	repo repository.UserPlaylogRepository,
) UserPlaylogService {
	return &userPlaylogService{repo}
}

func (s userPlaylogService) RunGetUserPlayLog() error {

	return nil
}

func (s userPlaylogService) RacingRunTodayPlayLog(agentName string, fnName string) (*int64, error) {

	actionAt := time.Now().UTC()

	var createBody model.RaceActionCreateBody
	createBody.Name = fnName
	createBody.JsonRequest = helper.StructJson(map[string]interface{}{"agentName": agentName})
	createBody.Status = "PENDING"

	// KEY = TODAY_PLAYLOG_{AGENT_NAME} = RUN ONCE PER 10 MINUTES or OLDWORK is completed
	createBody.ActionKey = fmt.Sprintf("TODAY_PLAYLOG_%s", agentName)
	createBody.UnlockAt = actionAt.Add(time.Minute * 10) // Max run is around 5-8 minutes, So 10 minutes is enough
	if oldWork, err := s.repo.GetRaceActionByActionKey(createBody.ActionKey); err != nil {
		// log.Println("RacingConfirmUserIncome.ERROR.GetRaceActionByActionKey", err)
		if err != gorm.ErrRecordNotFound {
			return nil, internalServerError(errors.New("CANNOT_CHECK_ACTION"))
		}
	} else {
		if oldWork.Id > 0 && actionAt.After(oldWork.UnlockAt) {
			// UPDATE
			canceledKey := fmt.Sprintf("TODAY_PLAYLOG_DONE_%d", oldWork.Id)
			canceledStatus := "TIMEOUT"
			var updateBody model.RaceActionUpdateBody
			updateBody.ActionKey = &canceledKey
			updateBody.Status = &canceledStatus
			if err := s.repo.UpdateRaceCondition(oldWork.Id, updateBody); err != nil {
				log.Println("RacingConfirmUserIncome.ERROR.UpdateRaceCondition", err)
				return nil, internalServerError(errors.New("WORK_IN_ACTION"))
			}
		} else {
			return nil, internalServerError(errors.New("WORK_IN_ACTION"))
		}
	}

	// only not found will be created
	actionId, err := s.repo.CreateRaceCondition(createBody)
	if err != nil {
		log.Println("RacingConfirmUserIncome.ERROR.CreateRaceCondition", err)
		return nil, internalServerError(errors.New("CANNOT_CREATE_ACTION"))
	}
	return &actionId, nil
}

func (s userPlaylogService) RunGetUserPlayLogByDate(req model.CronCreatePlayLogRequest) error {

	statementDate := req.StatementDate

	// RACE_CONDITION
	actionId, err := s.RacingRunTodayPlayLog(os.Getenv("AGENT_NAME"), "RunGetUserPlayLogByDate")
	if err != nil {
		return err
	}

	// todo AGC + AMB
	// -- GAME sport(1) casino(2) game(4) Then Add more column by lottery(3), p2p(6), Financial(7)
	// -- ** (5) ไม่มีห้ามดึง = พัง
	productIds := []int{model.AGENT_PRODUCT_SPORT, model.AGENT_PRODUCT_CASINO, model.AGENT_PRODUCT_GAME, model.AGENT_PRODUCT_LOTTERY, model.AGENT_PRODUCT_P2P, model.AGENT_PRODUCT_FINANCIAL}
	hasExternalLotteryApi := os.Getenv("LOTTERY_ENDPOINT") != "" && os.Getenv("AGENT_NAME") != ""

	go func() {
		for _, productId := range productIds {
			if productId == model.AGENT_PRODUCT_LOTTERY && hasExternalLotteryApi {
				// s.runAgcTodayWinLoseLottery(statementDate, false)
				log.Println("SKIP EXTERNAL LOTTERY, Use CALLBACK")
			} else {
				s.runAgcTodayWinLose(productId, statementDate, false)
			}
		}
		// RUN ได้เลย ตลอด ไม่ต้องรอ
		s.runPgHardToAgcTodayWinLose(model.AGENT_PRODUCT_GAME, statementDate, false)
		s.runAgentCtwToAgcTodayWinLose(model.AGENT_PRODUCT_GAME, statementDate, false)
		var pgSummaryReportReq model.PgHardCallbackSummaryRequest
		pgSummaryReportReq.DateFrom = statementDate
		pgSummaryReportReq.DateTo = statementDate
		s.RunPgHardCallbackSummary(pgSummaryReportReq)
		// UPDATE
		canceledKey := fmt.Sprintf("TODAY_PLAYLOG_DONE_%d", *actionId)
		canceledStatus := "SUCCESS"
		var updateBody model.RaceActionUpdateBody
		updateBody.ActionKey = &canceledKey
		updateBody.Status = &canceledStatus
		if err := s.repo.UpdateRaceCondition(*actionId, updateBody); err != nil {
			log.Println("RacingConfirmUserIncome.ERROR.UpdateRaceCondition", err)
			log.Println("Finish RunGetUserTodayPlayLog But ERROR UpdateRaceCondition")
		}
		log.Println("Finish RunGetUserTodayPlayLog statementDate", statementDate)
	}()
	return nil
}

func (s userPlaylogService) RunTodayAffiliateUserList() error {

	// [2024-10-15] onDemand Sync
	// go s.goTodayAffiliateUserList()

	go func() {
		// Today in BKK Only
		loc := time.FixedZone("Asia/Bangkok", 7*60*60)
		statementDate := time.Now().In(loc).Format("2006-01-02")
		affRepo := repository.NewAffiliateRepository(s.repo.GetDb())
		// RACE_CONDITION
		actionId, err := RacingRunAffiliateUserList(affRepo, statementDate)
		if err != nil || actionId == nil {
			log.Println("realtimeAffiliateUserList.ERROR.racingRunAffiliateUserList", err)
			return
		}
		// Do Work
		if err := s.repo.MakeReportAffiliateUserList(statementDate); err != nil {
			log.Println("realtimeAffiliateUserList.MakeReportAffiliateUserList", err)
		} else {
			// RACE_SUCCESS
			if err := RacingRunAffiliateUserListSuccess(affRepo, *actionId); err != nil {
				log.Println("realtimeAffiliateUserList.ERROR.racingRunAffiliateUserListSuccess", err)
			}
		}
	}()
	return nil
}

func (s userPlaylogService) RunGetUserTodayPlayLog() error {

	actionAt := time.Now().UTC()

	// RACE_CONDITION
	actionId, err := s.RacingRunTodayPlayLog(os.Getenv("AGENT_NAME"), "RunGetUserTodayPlayLog")
	if err != nil {
		return err
	}

	// -- GAME sport(1) casino(2) game(4) Then Add more column by lottery(3), p2p(6), Financial(7)
	// -- ** (5) ไม่มีห้ามดึง = พัง
	productIds := []int{model.AGENT_PRODUCT_SPORT, model.AGENT_PRODUCT_CASINO, model.AGENT_PRODUCT_GAME, model.AGENT_PRODUCT_LOTTERY, model.AGENT_PRODUCT_P2P, model.AGENT_PRODUCT_FINANCIAL}
	hasExternalLotteryApi := os.Getenv("LOTTERY_ENDPOINT") != "" && os.Getenv("AGENT_NAME") != ""

	go func() {
		statementAt := actionAt
		statementDate := fmt.Sprintf("%v-%02d-%02d", statementAt.Year(), int(statementAt.Month()), statementAt.Day())
		for _, productId := range productIds {
			if productId == model.AGENT_PRODUCT_LOTTERY && hasExternalLotteryApi {
				// s.runAgcTodayWinLoseLottery(statementDate, false)
				log.Println("SKIP EXTERNAL LOTTERY, Use CALLBACK")
			} else {
				s.runAgcTodayWinLose(productId, statementDate, false)
			}
		}
		// RUN ได้เลย ตลอด ไม่ต้องรอ
		s.runPgHardToAgcTodayWinLose(model.AGENT_PRODUCT_GAME, statementDate, false)
		s.runAgentCtwToAgcTodayWinLose(model.AGENT_PRODUCT_GAME, statementDate, false)
		var pgSummaryReportReq model.PgHardCallbackSummaryRequest
		pgSummaryReportDate := time.Now().UTC().Add(7 * time.Hour)
		pgSummaryReportStart := pgSummaryReportDate.AddDate(0, 0, -1).Format("2006-01-02")
		pgSummaryReportReq.DateFrom = pgSummaryReportStart
		pgSummaryReportReq.DateTo = pgSummaryReportDate.Format("2006-01-02")
		s.RunPgHardCallbackSummary(pgSummaryReportReq)
		// RUN YESTERDAY if before 12.20
		actionInBkk := actionAt.In(time.FixedZone("Asia/Bangkok", 7*60*60))
		if actionInBkk.Hour() < 12 || (actionInBkk.Hour() == 12 && actionInBkk.Minute() < 20) {
			statementAt = actionAt.AddDate(0, 0, -1)
			statementDate = statementAt.Format("2006-01-02")
			for _, productId := range productIds {
				if productId == model.AGENT_PRODUCT_LOTTERY && hasExternalLotteryApi {
					// s.runAgcTodayWinLoseLottery(statementDate, false)
					log.Println("SKIP YESTERDAY EXTERNAL LOTTERY IN THE MORNING")
				} else {
					s.runAgcTodayWinLose(productId, statementDate, false)
				}
			}
		}
		// RACE_CONDITION UPDATE
		successKey := fmt.Sprintf("TODAY_PLAYLOG_DONE_%d", *actionId)
		successStatus := "SUCCESS"
		var updateBody model.RaceActionUpdateBody
		updateBody.ActionKey = &successKey
		updateBody.Status = &successStatus
		if err := s.repo.UpdateRaceCondition(*actionId, updateBody); err != nil {
			log.Println("RacingConfirmUserIncome.ERROR.UpdateRaceCondition", err)
			log.Println("Finish RunGetUserTodayPlayLog But ERROR UpdateRaceCondition")
		}
		log.Println("Finish RunGetUserTodayPlayLog statementAt", statementAt)
	}()
	return nil
}

func (s userPlaylogService) runAgcTodayWinLose(productId int, statementDate string, force bool) error {

	actionAt := time.Now().UTC()
	path := fmt.Sprintf("todaywinlose%v force=%v", productId, force)
	// completedPath := fmt.Sprintf("completed_%v", productId)
	var cronError error
	log.Printf("Running runTodayWinLose %v date %v", path, statementDate)

	// DONOT RUN DURING 12.20 - 12.40
	// [2024-09-09] 12.40 => Extend to 12.01 - 12.59 when large rows(>7000) was 8min on LOCAL-DEV
	bbkLoc := time.FixedZone("Asia/Bangkok", 7*60*60)
	runTime := actionAt.In(bbkLoc)
	if runTime.Hour() == 12 && runTime.Minute() >= 01 && runTime.Minute() <= 59 {
		log.Printf("Skip runTodayWinLose %v date %v", path, statementDate)
		return nil
	}

	// Later : check api_status
	// get completed run
	// completedStatus, err := s.repo.GetUserPlaylogCompletedStatus(completedPath, statementDate)
	// if err != nil {
	// 	if err.Error() != "record not found" {
	// 		log.Println(err)
	// 		return
	// 	}
	// }

	agentName := os.Getenv("AGENT_NAME")
	body := model.AgcSimpleWinlose{}
	body.StartDate = statementDate
	body.EndDate = statementDate
	body.AgentName = agentName
	body.MemberName = agentName
	body.Products = []int{productId}
	body.PageSize = 250
	// body.PageIndex = 1
	body.TimeStamp = int(actionAt.Unix())
	body.Sign = helper.CreateSign(os.Getenv("AGENT_KEY"), agentName, actionAt)

	// isHasError := false
	var createList = make(map[string]model.UserTodayPlaylogCreateBody, 0)
	var uniqueList []string
	var memberCodeList []string
	var totalCreated int64
	for i := 0; ; i++ {

		body.PageIndex = i + 1

		// log.Println("GetExternalPlaylogByDate.WAIT", body)
		time.Sleep(5 * time.Second)
		list, err := s.repo.AgcSimpleWinLose(body)
		if err != nil {
			cronError = err
			log.Println("GetExternalPlaylogByDate.ERROR", err)
			break
		}
		if list.Error != nil && list.Error.Code != 0 {
			cronError = errors.New(list.Error.Message)
			if list.Error.Code == -15 {
				// request frequency limit is 3 seconds (-15)
				log.Println("List.ERROR", list.Error, ", RETRY PAGE=", body.PageIndex)
				i--
				time.Sleep(3 * time.Second)
				continue
			} else {
				log.Println("List.ERROR", list.Error)
				break
			}
		}
		// fmt.Println("runAgcTodayWinLose.list.Result.Records.LEN", len(list.Result.Records))
		if len(list.Result.Records) <= 0 {
			log.Println("ZERO==GOTALL")
			break
		}

		for _, j := range list.Result.Records {

			var tempRow model.UserTodayPlaylogCreateBody
			// {memberCode}_{statementDate}_{productId}
			uniqueKey := fmt.Sprintf("%v_%v_%v", j.UserName, strings.Replace(statementDate, "-", "", -1), productId)
			tempRow.StatementDate = body.StartDate
			tempRow.DailyKey = uniqueKey

			memberCodeList = append(memberCodeList, j.UserName)
			tempRow.MemberCode = j.UserName

			if productId == model.AGENT_PRODUCT_SPORT {
				tempRow.TurnSport = j.TurnOver
				tempRow.WinLoseSport = j.Payout
				tempRow.ValidAmountSport = j.ValidAmount
			} else if productId == model.AGENT_PRODUCT_CASINO {
				tempRow.TurnCasino = j.TurnOver
				tempRow.WinLoseCasino = j.Payout
				tempRow.ValidAmountCasino = j.ValidAmount
			} else if productId == model.AGENT_PRODUCT_GAME {
				tempRow.TurnGame = j.TurnOver
				tempRow.WinLoseGame = j.Payout
				tempRow.ValidAmountGame = j.ValidAmount
			} else if productId == model.AGENT_PRODUCT_LOTTERY {
				tempRow.TurnLottery = j.TurnOver
				tempRow.WinLoseLottery = j.Payout
				tempRow.ValidAmountLottery = j.ValidAmount
			} else if productId == model.AGENT_PRODUCT_P2P {
				tempRow.TurnP2p = j.TurnOver
				tempRow.WinLoseP2p = j.Payout
				tempRow.ValidAmountP2p = j.ValidAmount
			} else if productId == model.AGENT_PRODUCT_FINANCIAL {
				tempRow.TurnFinancial = j.TurnOver
				tempRow.WinLoseFinancial = j.Payout
				tempRow.ValidAmountFinancial = j.ValidAmount
			}

			tempRow.TurnTotal = tempRow.TurnSport + tempRow.TurnCasino + tempRow.TurnGame + tempRow.TurnLottery + tempRow.TurnP2p + tempRow.TurnFinancial
			tempRow.WinLoseTotal = tempRow.WinLoseSport + tempRow.WinLoseCasino + tempRow.WinLoseGame + tempRow.WinLoseLottery + tempRow.WinLoseP2p + tempRow.WinLoseFinancial
			tempRow.ValidAmountTotal = tempRow.ValidAmountSport + tempRow.ValidAmountCasino + tempRow.ValidAmountGame + tempRow.ValidAmountLottery + tempRow.ValidAmountP2p + tempRow.ValidAmountFinancial
			createList[uniqueKey] = tempRow

			// Bulk insert
			if len(createList) >= 100 {
				// check exists
				for k := range createList {
					uniqueList = append(uniqueList, k)
				}
				if dbList, _, err := s.repo.GetTodayPlayLogKeyList(uniqueList); err != nil {
					cronError = err // cant check
				} else {
					// if exists, remove from createList
					for index, dbReord := range dbList {
						dbKey := dbReord.DailyKey
						err := s.UpdateUserTodayPlaylog(createList[dbKey], dbList[index])
						if err != nil {
							log.Println(err)
							cronError = err
						}
						delete(createList, dbKey)
					}
					if len(createList) > 0 {
						err := s.repo.CreateUserTodayPlaylogBulk(createList, memberCodeList)
						if err != nil {
							log.Println(err)
							cronError = err
							continue
						}
						totalCreated += int64(len(createList))
					}
				}
				createList = make(map[string]model.UserTodayPlaylogCreateBody, 0)
				uniqueList = make([]string, 0)
				memberCodeList = make([]string, 0)
			}
		}
	}

	// fmt.Println("createList", helper.StructJson(createList))

	// LEFTOVER
	if len(createList) > 0 {
		// check exists
		for k := range createList {
			uniqueList = append(uniqueList, k)
		}
		if dbList, _, err := s.repo.GetTodayPlayLogKeyList(uniqueList); err != nil {
			cronError = err // cant check
		} else {
			// if exists, remove from createList
			for index, dbReord := range dbList {
				dbKey := dbReord.DailyKey
				err := s.UpdateUserTodayPlaylog(createList[dbKey], dbList[index])
				if err != nil {
					log.Println(err)
					cronError = err
				}
				delete(createList, dbKey)
			}
			if len(createList) > 0 {
				// [20240408] DISABLE LOG
				// for _, row := range createList {
				// 	// LOG_ON_DIFF
				// 	var body model.WebhookLogCreateBody
				// 	body.JsonRequest = helper.StructJson(row)
				// 	body.JsonPayload = "{}"
				// 	body.LogType = "CreateUserTodayPlaylogBulk"
				// 	body.Status = row.DailyKey
				// 	if _, err := s.repo.CreateWebhookLog(body); err != nil {
				// 		log.Println("CreateWebhookLog", err)
				// 	}
				// }
				err := s.repo.CreateUserTodayPlaylogBulk(createList, memberCodeList)
				if err != nil {
					log.Println(err)
					cronError = err
				}
				totalCreated += int64(len(createList))
			}
		}
		totalCreated += int64(len(createList))
	}

	if cronError != nil {
		log.Println("runTodayWinLose : ", path, "ERROR", cronError)
	}
	log.Println("Finish runTodayWinLose : ", path)
	return cronError
}

func (s userPlaylogService) runPgHardToAgcTodayWinLose(productId int, statementDate string, force bool) error {

	actionAt := time.Now().UTC()
	path := fmt.Sprintf("runPgHardToAgcTodayWinLose%v force=%v", productId, force)
	// completedPath := fmt.Sprintf("completed_%v", productId)
	var cronError error
	log.Printf("Running runPgHardToAgcTodayWinLose %v date %v", path, statementDate)

	// DONOT RUN DURING 12.20 - 12.40
	// [2024-09-09] 12.40 => Extend to 12.01 - 12.59 when large rows(>7000) was 8min on LOCAL-DEV
	bbkLoc := time.FixedZone("Asia/Bangkok", 7*60*60)
	runTime := actionAt.In(bbkLoc)
	if runTime.Hour() == 12 && runTime.Minute() >= 01 && runTime.Minute() <= 59 {
		log.Printf("Skip runPgHardToAgcTodayWinLose %v date %v", path, statementDate)
		return nil
	}

	// Later : check api_status
	// get completed run
	// completedStatus, err := s.repo.GetUserPlaylogCompletedStatus(completedPath, statementDate)
	// if err != nil {
	// 	if err.Error() != "record not found" {
	// 		log.Println(err)
	// 		return
	// 	}
	// }

	var body model.AgentPgHardCallbackSummaryRequest
	body.StatementDate = statementDate
	body.PageSize = 250

	// isHasError := false
	var createList = make(map[string]model.UserTodayPlaylogCreateBody, 0)
	var uniqueList []string
	var memberCodeList []string
	var totalCreated int64
	for i := 0; ; i++ {

		body.PageIndex = i + 1

		// log.Println("GetExternalPlaylogByDate.WAIT", body)
		time.Sleep(5 * time.Second)
		list, err := s.repo.GetAgentPgHardCallback(body)
		if err != nil {
			cronError = err
			log.Println("GetExternalPlaylogByDate.ERROR", err)
			break
		}

		// fmt.Println("runPgHardToAgcTodayWinLose.list.Result.Records.LEN", len(list))
		if len(list) <= 0 {
			log.Println("runPgHardToAgcTodayWinLose ZERO==GOTALL")
			break
		}

		for _, j := range list {

			var tempRow model.UserTodayPlaylogCreateBody
			// {memberCode}_{statementDate}_{productId}
			uniqueKey := fmt.Sprintf("%v_%v_PGH%v", j.MemberCode, strings.Replace(statementDate, "-", "", -1), productId)

			tempRow.StatementDate = statementDate
			tempRow.DailyKey = uniqueKey

			memberCodeList = append(memberCodeList, j.MemberCode)
			tempRow.MemberCode = j.MemberCode

			tempRow.TurnGame = j.TotalBet
			tempRow.WinLoseGame = j.TotalWinlose
			tempRow.ValidAmountGame = j.TotalBet

			tempRow.TurnTotal = tempRow.TurnSport + tempRow.TurnCasino + tempRow.TurnGame + tempRow.TurnLottery + tempRow.TurnP2p + tempRow.TurnFinancial
			tempRow.WinLoseTotal = tempRow.WinLoseSport + tempRow.WinLoseCasino + tempRow.WinLoseGame + tempRow.WinLoseLottery + tempRow.WinLoseP2p + tempRow.WinLoseFinancial
			tempRow.ValidAmountTotal = tempRow.ValidAmountSport + tempRow.ValidAmountCasino + tempRow.ValidAmountGame + tempRow.ValidAmountLottery + tempRow.ValidAmountP2p + tempRow.ValidAmountFinancial
			createList[uniqueKey] = tempRow

			// Bulk insert
			if len(createList) >= 100 {
				// check exists
				for k := range createList {
					uniqueList = append(uniqueList, k)
				}
				if dbList, _, err := s.repo.GetTodayPlayLogKeyList(uniqueList); err != nil {
					cronError = err // cant check
				} else {
					// if exists, remove from createList
					for index, dbReord := range dbList {
						dbKey := dbReord.DailyKey
						err := s.UpdateUserTodayPlaylog(createList[dbKey], dbList[index])
						if err != nil {
							log.Println(err)
							cronError = err
						}
						delete(createList, dbKey)
					}
					if len(createList) > 0 {
						err := s.repo.CreateUserTodayPlaylogBulk(createList, memberCodeList)
						if err != nil {
							log.Println(err)
							cronError = err
							continue
						}
						totalCreated += int64(len(createList))
					}
				}
				createList = make(map[string]model.UserTodayPlaylogCreateBody, 0)
				uniqueList = make([]string, 0)
				memberCodeList = make([]string, 0)
			}
		}
	}

	// fmt.Println("createList", helper.StructJson(createList))

	// LEFTOVER
	if len(createList) > 0 {
		// check exists
		for k := range createList {
			uniqueList = append(uniqueList, k)
		}
		if dbList, _, err := s.repo.GetTodayPlayLogKeyList(uniqueList); err != nil {
			cronError = err // cant check
		} else {
			// if exists, remove from createList
			for index, dbReord := range dbList {
				dbKey := dbReord.DailyKey
				err := s.UpdateUserTodayPlaylog(createList[dbKey], dbList[index])
				if err != nil {
					log.Println(err)
					cronError = err
				}
				delete(createList, dbKey)
			}
			if len(createList) > 0 {
				// [20240408] DISABLE LOG
				// for _, row := range createList {
				// 	// LOG_ON_DIFF
				// 	var body model.WebhookLogCreateBody
				// 	body.JsonRequest = helper.StructJson(row)
				// 	body.JsonPayload = "{}"
				// 	body.LogType = "CreateUserTodayPlaylogBulk"
				// 	body.Status = row.DailyKey
				// 	if _, err := s.repo.CreateWebhookLog(body); err != nil {
				// 		log.Println("CreateWebhookLog", err)
				// 	}
				// }
				err := s.repo.CreateUserTodayPlaylogBulk(createList, memberCodeList)
				if err != nil {
					log.Println(err)
					cronError = err
				}
				totalCreated += int64(len(createList))
			}
		}
		totalCreated += int64(len(createList))
	}

	if cronError != nil {
		log.Println("runPgHardToAgcTodayWinLose : ", path, "ERROR", cronError)
	}
	log.Println("Finish runPgHardToAgcTodayWinLose : ", path)
	return cronError
}

func (s userPlaylogService) UseCallbackrunAgcTodayWinLoseLottery(statementDate string, force bool) error {

	actionAt := time.Now().UTC()
	productId := model.AGENT_PRODUCT_LOTTERY
	path := fmt.Sprintf("todaywinlose%v force=%v", productId, force)
	// completedPath := fmt.Sprintf("completed_%v", productId)
	var cronError error
	log.Printf("Running runAgcTodayWinLoseLottery %v date %v", path, statementDate)

	// DONOT RUN DURING 12.20 - 12.40
	// [2024-09-09] 12.40 => Extend to 12.01 - 12.59 when large rows(>7000) was 8min on LOCAL-DEV
	bbkLoc := time.FixedZone("Asia/Bangkok", 7*60*60)
	runTime := actionAt.In(bbkLoc)
	if runTime.Hour() == 12 && runTime.Minute() >= 01 && runTime.Minute() <= 59 {
		log.Printf("Skip runAgcTodayWinLoseLottery %v date %v", path, statementDate)
		return nil
	}

	// agentName := os.Getenv("AGENT_NAME")
	body := model.LotteryPlaylogRequest{}
	body.Date = statementDate
	body.Size = 250
	// body.PageIndex = 1

	// isHasError := false
	var createList = make(map[string]model.UserTodayPlaylogCreateBody, 0)
	var uniqueList []string
	var totalCreated int64
	for i := 0; ; i++ {

		// AGENT start at 1, LOTTERY start at 0
		body.Page = i + 1

		// log.Println("GetExternalPlaylogByDate.WAIT", body)
		time.Sleep(5 * time.Second)
		list, err := s.repo.LotterySimpleWinLose(body)
		if err != nil {
			cronError = err
			log.Println("GetExternalPlaylogByDate.ERROR", err)
			break
		}

		// fmt.Println("runAgcTodayWinLose.list.Result.Records.LEN", len(list.Content))
		if len(list.Content) <= 0 {
			log.Println("runAgcTodayWinLose ZERO==GOTALL")
			break
		}

		for _, j := range list.Content {

			var tempRow model.UserTodayPlaylogCreateBody
			// {memberCode}_{statementDate}_{productId}
			uniqueKey := fmt.Sprintf("%v_%v_%v", j.UserId, strings.Replace(statementDate, "-", "", -1), productId)
			tempRow.StatementDate = body.Date
			tempRow.DailyKey = uniqueKey
			tempRow.UserId = j.UserId

			if productId == model.AGENT_PRODUCT_SPORT {
				tempRow.TurnSport = j.TurnOver
				tempRow.WinLoseSport = j.WinLoss
				tempRow.ValidAmountSport = j.ValidAmount
			} else if productId == model.AGENT_PRODUCT_CASINO {
				tempRow.TurnCasino = j.TurnOver
				tempRow.WinLoseCasino = j.WinLoss
				tempRow.ValidAmountCasino = j.ValidAmount
			} else if productId == model.AGENT_PRODUCT_GAME {
				tempRow.TurnGame = j.TurnOver
				tempRow.WinLoseGame = j.WinLoss
				tempRow.ValidAmountGame = j.ValidAmount
			} else if productId == model.AGENT_PRODUCT_LOTTERY {
				tempRow.TurnLottery = j.TurnOver
				tempRow.WinLoseLottery = j.WinLoss
				tempRow.ValidAmountLottery = j.ValidAmount
			} else if productId == model.AGENT_PRODUCT_P2P {
				tempRow.TurnP2p = j.TurnOver
				tempRow.WinLoseP2p = j.WinLoss
				tempRow.ValidAmountP2p = j.ValidAmount
			} else if productId == model.AGENT_PRODUCT_FINANCIAL {
				tempRow.TurnFinancial = j.TurnOver
				tempRow.WinLoseFinancial = j.WinLoss
				tempRow.ValidAmountFinancial = j.ValidAmount
			}

			tempRow.TurnTotal = tempRow.TurnSport + tempRow.TurnCasino + tempRow.TurnGame + tempRow.TurnLottery + tempRow.TurnP2p + tempRow.TurnFinancial
			tempRow.WinLoseTotal = tempRow.WinLoseSport + tempRow.WinLoseCasino + tempRow.WinLoseGame + tempRow.WinLoseLottery + tempRow.WinLoseP2p + tempRow.WinLoseFinancial
			tempRow.ValidAmountTotal = tempRow.ValidAmountSport + tempRow.ValidAmountCasino + tempRow.ValidAmountGame + tempRow.ValidAmountLottery + tempRow.ValidAmountP2p + tempRow.ValidAmountFinancial
			createList[uniqueKey] = tempRow

			// Bulk insert
			if len(createList) >= 100 {
				// check exists
				for k := range createList {
					uniqueList = append(uniqueList, k)
				}
				if dbList, _, err := s.repo.GetTodayPlayLogKeyList(uniqueList); err != nil {
					cronError = err // cant check
				} else {
					// if exists, remove from createList
					for index, dbReord := range dbList {
						dbKey := dbReord.DailyKey
						err := s.UpdateUserTodayPlaylog(createList[dbKey], dbList[index])
						if err != nil {
							log.Println(err)
							cronError = err
						}
						delete(createList, dbKey)
					}
					if len(createList) > 0 {
						err := s.repo.CreateUserTodayPlaylogBulkDirect(createList)
						if err != nil {
							log.Println(err)
							cronError = err
							continue
						}
						totalCreated += int64(len(createList))
					}
				}
				createList = make(map[string]model.UserTodayPlaylogCreateBody, 0)
				uniqueList = make([]string, 0)
			}
		}
	}

	// fmt.Println("createList", helper.StructJson(createList))

	// LEFTOVER
	if len(createList) > 0 {
		// check exists
		for k := range createList {
			uniqueList = append(uniqueList, k)
		}
		if dbList, _, err := s.repo.GetTodayPlayLogKeyList(uniqueList); err != nil {
			cronError = err // cant check
		} else {
			// if exists, remove from createList
			for index, dbReord := range dbList {
				dbKey := dbReord.DailyKey
				err := s.UpdateUserTodayPlaylog(createList[dbKey], dbList[index])
				if err != nil {
					log.Println(err)
					cronError = err
				}
				delete(createList, dbKey)
			}
			if len(createList) > 0 {
				// [20240408] DISABLE LOG
				// for _, row := range createList {
				// 	// LOG_ON_DIFF
				// 	var body model.WebhookLogCreateBody
				// 	body.JsonRequest = helper.StructJson(row)
				// 	body.JsonPayload = "{}"
				// 	body.LogType = "CreateUserTodayPlaylogBulk"
				// 	body.Status = row.DailyKey
				// 	if _, err := s.repo.CreateWebhookLog(body); err != nil {
				// 		log.Println("CreateWebhookLog", err)
				// 	}
				// }
				err := s.repo.CreateUserTodayPlaylogBulkDirect(createList)
				if err != nil {
					log.Println(err)
					cronError = err
				}
				totalCreated += int64(len(createList))
			}
		}
		totalCreated += int64(len(createList))
	}

	if cronError != nil {
		log.Println("runAgcTodayWinLoseLottery : ", path, "ERROR", cronError)
	}
	log.Println("Finish runAgcTodayWinLoseLottery : ", path)
	return cronError
}

func (s userPlaylogService) UpdateUserTodayPlaylog(createBody model.UserTodayPlaylogCreateBody, dbRecord model.UserTodayPlaylogResponse) error {

	createRow := model.UserTodayPlaylogResponse{
		Id:                   dbRecord.Id,
		TurnSport:            createBody.TurnSport,
		ValidAmountSport:     createBody.ValidAmountSport,
		WinLoseSport:         createBody.WinLoseSport,
		TurnCasino:           createBody.TurnCasino,
		WinLoseCasino:        createBody.WinLoseCasino,
		ValidAmountCasino:    createBody.ValidAmountCasino,
		TurnGame:             createBody.TurnGame,
		WinLoseGame:          createBody.WinLoseGame,
		ValidAmountGame:      createBody.ValidAmountGame,
		TurnLottery:          createBody.TurnLottery,
		WinLoseLottery:       createBody.WinLoseLottery,
		ValidAmountLottery:   createBody.ValidAmountLottery,
		TurnP2p:              createBody.TurnP2p,
		WinLoseP2p:           createBody.WinLoseP2p,
		ValidAmountP2p:       createBody.ValidAmountP2p,
		TurnFinancial:        createBody.TurnFinancial,
		WinLoseFinancial:     createBody.WinLoseFinancial,
		ValidAmountFinancial: createBody.ValidAmountFinancial,
		TurnTotal:            createBody.TurnTotal,
		WinLoseTotal:         createBody.WinLoseTotal,
		ValidAmountTotal:     createBody.ValidAmountTotal,
	}

	dbRow := model.UserTodayPlaylogResponse{
		Id:                   dbRecord.Id,
		TurnSport:            dbRecord.TurnSport,
		ValidAmountSport:     dbRecord.ValidAmountSport,
		WinLoseSport:         dbRecord.WinLoseSport,
		TurnCasino:           dbRecord.TurnCasino,
		WinLoseCasino:        dbRecord.WinLoseCasino,
		ValidAmountCasino:    dbRecord.ValidAmountCasino,
		TurnGame:             dbRecord.TurnGame,
		WinLoseGame:          dbRecord.WinLoseGame,
		ValidAmountGame:      dbRecord.ValidAmountGame,
		TurnLottery:          dbRecord.TurnLottery,
		WinLoseLottery:       dbRecord.WinLoseLottery,
		ValidAmountLottery:   dbRecord.ValidAmountLottery,
		TurnP2p:              dbRecord.TurnP2p,
		WinLoseP2p:           dbRecord.WinLoseP2p,
		ValidAmountP2p:       dbRecord.ValidAmountP2p,
		TurnFinancial:        dbRecord.TurnFinancial,
		WinLoseFinancial:     dbRecord.WinLoseFinancial,
		ValidAmountFinancial: dbRecord.ValidAmountFinancial,
		TurnTotal:            dbRecord.TurnTotal,
		WinLoseTotal:         dbRecord.WinLoseTotal,
		ValidAmountTotal:     dbRecord.ValidAmountTotal,
	}

	localJson := helper.StructJson(createRow)
	remoteJson := helper.StructJson(dbRow)
	// log.Println("localJson", localJson)
	// log.Println("remoteJson", remoteJson)
	// later : golang fastest way to compare json

	if localJson != remoteJson {
		// LOG_ON_DIFF
		updateBody := model.UserTodayPlaylogUpdateBody{
			Id:                   dbRecord.Id,
			TurnSport:            &createBody.TurnSport,
			ValidAmountSport:     &createBody.ValidAmountSport,
			WinLoseSport:         &createBody.WinLoseSport,
			TurnCasino:           &createBody.TurnCasino,
			WinLoseCasino:        &createBody.WinLoseCasino,
			ValidAmountCasino:    &createBody.ValidAmountCasino,
			TurnGame:             &createBody.TurnGame,
			WinLoseGame:          &createBody.WinLoseGame,
			ValidAmountGame:      &createBody.ValidAmountGame,
			TurnLottery:          &createBody.TurnLottery,
			WinLoseLottery:       &createBody.WinLoseLottery,
			ValidAmountLottery:   &createBody.ValidAmountLottery,
			TurnP2p:              &createBody.TurnP2p,
			WinLoseP2p:           &createBody.WinLoseP2p,
			ValidAmountP2p:       &createBody.ValidAmountP2p,
			TurnFinancial:        &createBody.TurnFinancial,
			WinLoseFinancial:     &createBody.WinLoseFinancial,
			ValidAmountFinancial: &createBody.ValidAmountFinancial,
			TurnTotal:            &createBody.TurnTotal,
			WinLoseTotal:         &createBody.WinLoseTotal,
			ValidAmountTotal:     &createBody.ValidAmountTotal,
		}
		if err := s.repo.UpdateUserTodayPlaylog(updateBody); err != nil {
			log.Println(err)
		}
	}
	// log.Println("UpdateUserTodayPlaylog : No need update")
	return nil
}

func (s userPlaylogService) runAgentCtwToAgcTodayWinLose(productId int, statementDate string, force bool) error {

	actionAt := time.Now().UTC()
	path := fmt.Sprintf("runAgentCtwToAgcTodayWinLose%v force=%v", productId, force)
	// completedPath := fmt.Sprintf("completed_%v", productId)
	var cronError error
	log.Printf("Running runAgentCtwToAgcTodayWinLose %v date %v", path, statementDate)

	// DONOT RUN DURING 12.20 - 12.40
	// [2024-09-09] 12.40 => Extend to 12.01 - 12.59 when large rows(>7000) was 8min on LOCAL-DEV
	bbkLoc := time.FixedZone("Asia/Bangkok", 7*60*60)
	runTime := actionAt.In(bbkLoc)
	if runTime.Hour() == 12 && runTime.Minute() >= 01 && runTime.Minute() <= 59 {
		log.Printf("Skip runAgentCtwToAgcTodayWinLose %v date %v", path, statementDate)
		return nil
	}

	// Later : check api_status
	// get completed run
	// completedStatus, err := s.repo.GetUserPlaylogCompletedStatus(completedPath, statementDate)
	// if err != nil {
	// 	if err.Error() != "record not found" {
	// 		log.Println(err)
	// 		return
	// 	}
	// }

	var body model.AgentCtwCallbackSummaryRequest
	body.StatementDate = statementDate
	body.PageSize = 250

	// isHasError := false
	var createList = make(map[string]model.UserTodayPlaylogCreateBody, 0)
	var uniqueList []string
	var memberCodeList []string
	var totalCreated int64
	for i := 0; ; i++ {

		body.PageIndex = i + 1

		// log.Println("GetExternalPlaylogByDate.WAIT", body)
		time.Sleep(5 * time.Second)
		list, err := s.repo.GetAgentCtwCallback(body)
		if err != nil {
			cronError = err
			log.Println("GetExternalPlaylogByDate.ERROR", err)
			break
		}

		// fmt.Println("runAgentCtwToAgcTodayWinLose.list.Result.Records.LEN", len(list))
		if len(list) <= 0 {
			log.Println("runAgentCtwToAgcTodayWinLose ZERO==GOTALL")
			break
		}

		for _, j := range list {

			var tempRow model.UserTodayPlaylogCreateBody
			// {memberCode}_{statementDate}_{productId}
			uniqueKey := fmt.Sprintf("%v_%v_CTW%v", j.MemberCode, strings.Replace(statementDate, "-", "", -1), productId)

			tempRow.StatementDate = statementDate
			tempRow.DailyKey = uniqueKey

			memberCodeList = append(memberCodeList, j.MemberCode)
			tempRow.MemberCode = j.MemberCode

			tempRow.TurnGame = j.TotalBet
			tempRow.WinLoseGame = j.TotalWinlose
			tempRow.ValidAmountGame = j.TotalBet

			tempRow.TurnTotal = tempRow.TurnSport + tempRow.TurnCasino + tempRow.TurnGame + tempRow.TurnLottery + tempRow.TurnP2p + tempRow.TurnFinancial
			tempRow.WinLoseTotal = tempRow.WinLoseSport + tempRow.WinLoseCasino + tempRow.WinLoseGame + tempRow.WinLoseLottery + tempRow.WinLoseP2p + tempRow.WinLoseFinancial
			tempRow.ValidAmountTotal = tempRow.ValidAmountSport + tempRow.ValidAmountCasino + tempRow.ValidAmountGame + tempRow.ValidAmountLottery + tempRow.ValidAmountP2p + tempRow.ValidAmountFinancial
			createList[uniqueKey] = tempRow

			// Bulk insert
			if len(createList) >= 100 {
				// check exists
				for k := range createList {
					uniqueList = append(uniqueList, k)
				}
				if dbList, _, err := s.repo.GetTodayPlayLogKeyList(uniqueList); err != nil {
					cronError = err // cant check
				} else {
					// if exists, remove from createList
					for index, dbReord := range dbList {
						dbKey := dbReord.DailyKey
						err := s.UpdateUserTodayPlaylog(createList[dbKey], dbList[index])
						if err != nil {
							log.Println(err)
							cronError = err
						}
						delete(createList, dbKey)
					}
					if len(createList) > 0 {
						err := s.repo.CreateUserTodayPlaylogBulk(createList, memberCodeList)
						if err != nil {
							log.Println(err)
							cronError = err
							continue
						}
						totalCreated += int64(len(createList))
					}
				}
				createList = make(map[string]model.UserTodayPlaylogCreateBody, 0)
				uniqueList = make([]string, 0)
				memberCodeList = make([]string, 0)
			}
		}
	}

	// fmt.Println("createList", helper.StructJson(createList))

	// LEFTOVER
	if len(createList) > 0 {
		// check exists
		for k := range createList {
			uniqueList = append(uniqueList, k)
		}
		if dbList, _, err := s.repo.GetTodayPlayLogKeyList(uniqueList); err != nil {
			cronError = err // cant check
		} else {
			// if exists, remove from createList
			for index, dbReord := range dbList {
				dbKey := dbReord.DailyKey
				err := s.UpdateUserTodayPlaylog(createList[dbKey], dbList[index])
				if err != nil {
					log.Println(err)
					cronError = err
				}
				delete(createList, dbKey)
			}
			if len(createList) > 0 {
				// [20240408] DISABLE LOG
				// for _, row := range createList {
				// 	// LOG_ON_DIFF
				// 	var body model.WebhookLogCreateBody
				// 	body.JsonRequest = helper.StructJson(row)
				// 	body.JsonPayload = "{}"
				// 	body.LogType = "CreateUserTodayPlaylogBulk"
				// 	body.Status = row.DailyKey
				// 	if _, err := s.repo.CreateWebhookLog(body); err != nil {
				// 		log.Println("CreateWebhookLog", err)
				// 	}
				// }
				err := s.repo.CreateUserTodayPlaylogBulk(createList, memberCodeList)
				if err != nil {
					log.Println(err)
					cronError = err
				}
				totalCreated += int64(len(createList))
			}
		}
		totalCreated += int64(len(createList))
	}

	if cronError != nil {
		log.Println("runCTWToAgcTodayWinLose : ", path, "ERROR", cronError)
	}
	log.Println("Finish runCTWToAgcTodayWinLose : ", path)
	return cronError
}

func (s userPlaylogService) RunPgHardCallbackSummary(req model.PgHardCallbackSummaryRequest) ([]model.PgHardCallbackSummaryResponse, error) {

	getList, err := s.repo.GetPgHardCallbackSummary(req)
	if err != nil {
		return nil, err
	}

	var reqOldList model.AgentPgHardCallbackSummaryReportRequest
	reqOldList.DateFrom = req.DateFrom
	reqOldList.DateTo = req.DateTo
	oldList, err := s.repo.AgentPgHardCallbackSummaryReportList(reqOldList)
	if err != nil {
		return nil, err
	}

	// Step 1: Create a map of existing data using key "userId_statementDate"
	oldDataMap := make(map[string]model.AgentPgHardCallbackSummaryReportResponse)
	for _, old := range oldList {
		key := fmt.Sprintf("%d_%s", old.UserID, old.StatementDate)
		oldDataMap[key] = old
	}

	var createList []model.CreateAgentPgHardCallbackSummaryReport
	var updateList []model.UpdateAgentPgHardCallbackSummaryReport

	// Step 2: Loop through new data and decide create or update
	for _, newItem := range getList {
		statementDate := newItem.CreatedAt.Format("2006-01-02") // Ensure date is in YYYY-MM-DD format
		key := fmt.Sprintf("%d_%s", newItem.UserId, statementDate)
		// check old and new diff
		if newItem.BetAmount == oldDataMap[key].BetAmount &&
			newItem.Payoff == oldDataMap[key].Payoff &&
			newItem.WinloseAmount == oldDataMap[key].WinloseAmount &&
			newItem.BeforeBalance == oldDataMap[key].BeforeBalance &&
			newItem.AfterBalance == oldDataMap[key].AfterBalance {
			// Skip if no changes
			continue
		}

		if oldItem, exists := oldDataMap[key]; exists {
			// Prepare for update
			updateList = append(updateList, model.UpdateAgentPgHardCallbackSummaryReport{
				Id:            oldItem.Id,
				UserId:        newItem.UserId,
				StatementDate: statementDate,
				MemberCode:    newItem.MemberCode,
				BetAmount:     newItem.BetAmount,
				Payoff:        newItem.Payoff,
				WinloseAmount: newItem.WinloseAmount,
				BeforeBalance: newItem.BeforeBalance,
				AfterBalance:  newItem.AfterBalance,
			})
		} else {
			// Prepare for create
			createList = append(createList, model.CreateAgentPgHardCallbackSummaryReport{
				UserId:        newItem.UserId,
				StatementDate: statementDate,
				MemberCode:    newItem.MemberCode,
				BetAmount:     newItem.BetAmount,
				Payoff:        newItem.Payoff,
				WinloseAmount: newItem.WinloseAmount,
				BeforeBalance: newItem.BeforeBalance,
				AfterBalance:  newItem.AfterBalance,
			})
		}
	}

	// Step 3: Execute create and update operations
	if len(createList) > 0 {
		if err := s.repo.CreateAgentPgHardCallbackSummaryReport(createList); err != nil {
			return nil, err
		}
	}

	for _, updateItem := range updateList {
		if err := s.repo.UpdateAgentPgHardCallbackSummaryReport(updateItem); err != nil {
			return nil, err
		}
	}

	return getList, nil
}
