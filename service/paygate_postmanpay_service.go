package service

import (
	"bytes"
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"image/png"
	"log"
	"os"
	"strings"
	"time"

	"github.com/skip2/go-qrcode"
	"gorm.io/gorm"
)

type PostmanPayService interface {
	// PostmanPay
	CreatePostmanPayWebhook(req model.PostmanPayWebhookRequest) (*int64, error)
	GetPostmanPayWebDepositAccount() (*model.PostmanPayCustomerDepositInfo, error)
	CreatePostmanPayDeposit(req model.PostmanPayDepositCreateRequest) (*model.PostmanPayOrderWebResponse, error)
	CreatePostmanPayWithdraw(req model.PostmanPayWithdrawCreateRequest) (*int64, error)
	// PostmanPayCheckBalance() (*model.PostmanPayCheckBalanceRemoteResponse, error)
	// ไม่มี API CancelWithdrawFromPostmanPay(transId int64, adminId int64) error
	CreatePostmanPayDepositWebhook(req model.PostmanPayWebhookRequest) (*int64, error)
	CreatePostmanPayWithdrawWebhook(req model.PostmanPayWebhookRequest) (*int64, error)
	// ORDER
	// GetPendingPostmanPayDepositOrder(userId int64) (*model.PostmanPayOrderWebResponse, error)
	// ไม่มี API CancelPostmanPayDeposit(req model.PostmanPayDepositCancelRequest) error
	// SysLog
	CreateSystemLog(name string, req interface{}) error
}

type paygatePostmanPayService struct {
	sharedDb                  *gorm.DB
	repo                      repository.PostmanPayRepository
	activityLuckyWheelService ActivityLuckyWheelService
	promotionWebService       PromotionWebService
	notiService               NotificationService
}

func NewPostmanPayService(
	sharedDb *gorm.DB,
	repo repository.PostmanPayRepository,
	activityLuckyWheelService ActivityLuckyWheelService,
	promotionWebService PromotionWebService,
	notiService NotificationService,
) PostmanPayService {
	return &paygatePostmanPayService{sharedDb, repo, activityLuckyWheelService, promotionWebService, notiService}
}

func (s paygatePostmanPayService) ConfirmDepositTransaction(id int64, req model.BankConfirmDepositRequest) error {

	record, err := s.repo.GetBankTransactionById(id)
	if err != nil {
		return internalServerError(err)
	}
	if record.TransactionStatusId != model.TRANS_STATUS_PENDING {
		return badRequest("Transaction is not pending")
	}
	if record.TransactionTypeId != model.TRANSACTION_TYPE_DEPOSIT && record.TransactionTypeId != model.TRANSACTION_TYPE_BONUS {
		return badRequest("Transaction is not deposit")
	}
	jsonBefore, _ := json.Marshal(record)

	var updateData model.BankDepositTransactionConfirmBody
	updateData.TransactionStatusId = model.TRANS_STATUS_DEPOSIT_PENDING_CREDIT
	updateData.ConfirmedAt = req.ConfirmedAt
	updateData.ConfirmedByAdminId = req.ConfirmedByAdminId

	// if record.IsAutoCredit && record.TransferAt != nil {
	// 	seconds := time.Now().UTC().Sub(record.TransferAt.UTC()).Seconds()
	// 	autoProcessTimer := fmt.Sprintf("%.2f", seconds)
	// 	updateData.AutoProcessTimer = &autoProcessTimer
	// }

	var createBody model.CreateBankTransactionActionBody
	createBody.ActionKey = fmt.Sprintf("DCF_STATE#%d", record.Id)
	createBody.TransactionId = record.Id
	createBody.UserId = record.UserId
	createBody.TransactionTypeId = record.TransactionTypeId
	createBody.FromAccountId = record.FromAccountId
	createBody.ToAccountId = record.ToAccountId
	createBody.JsonBefore = string(jsonBefore)
	if req.TransferAt == nil {
		createBody.TransferAt = record.TransferAt
	} else {
		TransferAt := req.TransferAt
		createBody.TransferAt = TransferAt
		updateData.TransferAt = *TransferAt
	}
	if req.SlipUrl != nil {
		createBody.SlipUrl = *req.SlipUrl
	}
	createBody.CreditAmount = record.CreditAmount
	if req.BonusAmount != nil {
		createBody.BonusAmount = *req.BonusAmount
	}
	createBody.ConfirmedAt = req.ConfirmedAt
	createBody.ConfirmedByAdminId = req.ConfirmedByAdminId
	if actionId, err := s.repo.CreateTransactionAction(createBody); err == nil {
		// do nothing ?
		if err := s.repo.ConfirmPendingDepositTransaction(id, updateData); err != nil {
			if err := s.repo.RollbackTransactionAction(*actionId); err == nil {
				return internalServerError(err)
			}
			return internalServerError(err)
		}
	} else {
		return internalServerError(err)
	}
	return nil
}

func (s paygatePostmanPayService) CreatePostmanPayWebhook(req model.PostmanPayWebhookRequest) (*int64, error) {

	var createBody model.PostmanPayWebhookCreateBody
	createBody.Name = "POSTMANPAY_CALLBACK"
	createBody.JsonPayload = req.JsonPayload
	insertId, err := s.repo.CreatePostmanPayWebhook(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// just for log
	// MainFlow use Deposit and Withdraw webhook.
	var remoteResp model.PostmanPayWebhookResponse
	errJson := json.Unmarshal([]byte(req.JsonPayload), &remoteResp)
	if errJson != nil {
		log.Println("Error unmarshal json", errJson)
		return nil, internalServerError(errJson)
	}
	log.Println("CreatePostmanPayWebhook.PostmanPayWebhookResponse=", helper.StructJson(remoteResp))

	return insertId, nil
}

func (s paygatePostmanPayService) GetPostmanPayWebDepositAccount() (*model.PostmanPayCustomerDepositInfo, error) {

	var result model.PostmanPayCustomerDepositInfo

	pgAccount, err := s.GetPostmanPayAccount()
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}
	if !pgAccount.IsDepositEnabled {
		return nil, badRequest("PAYGATE_DEPOSIT_NOT_ENABLED")
	}

	result.Name = pgAccount.Name
	if pgAccount.DisplayName == "" {
		result.Name = "เติมเงินผ่าน QR Code"
	} else {
		result.DisplayName = pgAccount.DisplayName
	}
	result.ShopName = pgAccount.ShopName
	result.MinAmount = model.POSTMANPAY_DEFMIN_DEPOSIT_AMOUNT
	result.MaxAmount = model.POSTMANPAY_DEFMAX_DEPOSIT_AMOUNT

	return &result, nil
}

func (s paygatePostmanPayService) GetPostmanPayAccount() (*model.PaygateAccountResponse, error) {

	return GetPostmanPayAccount(s.repo)
}

func GetPostmanPayAccount(repo repository.PostmanPayRepository) (*model.PaygateAccountResponse, error) {

	pgAccount, err := repo.GetPaygateAccountByProviderId(model.PAYGATE_MERCHANT_ID_POSTMANPAY)
	if err != nil {
		return nil, internalServerError(err)
	}
	return pgAccount, nil
}

func GetPostmanPayCustomerBank(bankCode string) (string, error) {

	remoteBankCode := ""
	uBankCode := strings.ToUpper(strings.TrimSpace(bankCode))

	switch uBankCode {
	case "000", "PROMPTPAY":
		remoteBankCode = "000"
	case "002", "BBL":
		remoteBankCode = "BBL" // "3" "กรุงเทพ" "bbl"
	case "004", "KBANK":
		remoteBankCode = "KBANK" // "1" "กสิกรไทย" "kbank"
	case "006", "KTB":
		remoteBankCode = "KTB" // "5"	"กรุงไทย"	"ktb"
	case "011", "TMB":
		remoteBankCode = "TMB"
	case "014", "SCB":
		remoteBankCode = "SCB" // "2" "ไทยพาณิชย์" "scb"
	case "022", "CIMB":
		remoteBankCode = "CIMB" // "13"	"ซีไอเอ็มบี"	"cimb"
	case "024", "UOB":
		remoteBankCode = "UOB" // "11"	"ยูโอบี"	"uob"
	case "025", "BAY":
		remoteBankCode = "BAY" // "4"	"กรุงศรี"	"bay"
	case "030", "GSB":
		remoteBankCode = "GSB" // "7"	"ออมสิน"	"gsb"
	case "033", "GHB":
		remoteBankCode = "GHB" // "10"	"อาคารสงเคราะห์"	"ghb"
	case "034", "BAAC":
		remoteBankCode = "BAAC" // "8"	"ธกส"	"baac"
	case "067", "TISCO":
		remoteBankCode = "TISCO" // // "17"	"ทิสโก้"	"tisco"
	case "069", "KKP":
		remoteBankCode = "KKP" // "9"	"เกียรตินาคิน"	"kkp"
	case "071", "TCRB":
		remoteBankCode = "TCRB" // ไทยเครดิต
	case "073", "LHBANK", "LH":
		remoteBankCode = "LHBANK" //"12"	"แลนด์ แอนด์ เฮ้าส์"	"lh"
	case "074", "TTB":
		remoteBankCode = "TTB" // "6"	"ทีเอ็มบีธนชาต"	"ttb"
	case "USDT":
		remoteBankCode = "USDT"
	default:
		// // "14"	"เอชเอสบีซี"	"hsbc"
		// "15"	"ไอซีบีซี"	"icbc"
		// "16"	"ธนาคารอิสลาม"	"isbt"
		// "18"	"ซิตี้แบงก์"	"citi"
		// "19"	"สแตนดาร์ดชาร์เตอร์ด"	"scbt"
		// และอื่นๆ หลังจากนี้ทั้งหมด
		return "", errors.New("USER_BANK_NOT_SUPPORTED")
	}

	return remoteBankCode, nil
}

func (s paygatePostmanPayService) CreatePostmanPayDeposit(req model.PostmanPayDepositCreateRequest) (*model.PostmanPayOrderWebResponse, error) {

	var result model.PostmanPayOrderWebResponse

	// Ruled by Provider
	if req.Amount < model.POSTMANPAY_DEFMIN_DEPOSIT_AMOUNT || req.Amount > model.POSTMANPAY_DEFMAX_DEPOSIT_AMOUNT {
		log.Println("req.Amount", req.Amount)
		return nil, badRequest("INVALID_AMOUNT_RANGE")
	}
	// ผมอยากเก็บ default เอาไว้กัน Adminตั้งค่าต่ำกว่า payment
	if req.Amount < model.POSTMANPAY_DEFMIN_DEPOSIT_AMOUNT || req.Amount > model.POSTMANPAY_DEFMAX_DEPOSIT_AMOUNT {
		log.Println("PostmanPayDeposit.INVALID_AMOUNT_RANGE req.Amount=", req.Amount, "POSTMANPAY_DEFMIN_DEPOSIT_AMOUNT=", model.POSTMANPAY_DEFMIN_DEPOSIT_AMOUNT, "POSTMANPAY_DEFMAX_DEPOSIT_AMOUNT=", model.POSTMANPAY_DEFMAX_DEPOSIT_AMOUNT)
		return nil, badRequest("INVALID_AMOUNT_RANGE")
	}

	pgAccount, err := s.GetPostmanPayAccount()
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}
	if !pgAccount.IsDepositEnabled {
		return nil, badRequest("PAYGATE_DEPOSIT_NOT_ENABLED")
	}
	// [Prerequisites] EP + CLIENTID + APIKEY + SECRET + MERCHANTID
	if pgAccount.ApiEndPoint == "" || pgAccount.PartnerKey == "" || pgAccount.AccessKey == "" || pgAccount.SecretKey == "" || pgAccount.MerchantId == "" {
		return nil, badRequest("PAYGATE_EMPTY_SETTING")
	}
	if pgAccount.PaymentDepositMinimum > 0 && req.Amount < pgAccount.PaymentDepositMinimum {
		log.Println("PostmanPayDeposit.INVALID_AMOUNT_RANGE req.Amount=", req.Amount, "pgAccount.PaymentDepositMinimum=", pgAccount.PaymentDepositMinimum)
		return nil, badRequest("INVALID_AMOUNT_RANGE")
	}
	if pgAccount.PaymentDepositMaximum > 0 && req.Amount > pgAccount.PaymentDepositMaximum {
		log.Println("PostmanPayDeposit.INVALID_AMOUNT_RANGE req.Amount=", req.Amount, "pgAccount.PaymentDepositMaximum=", pgAccount.PaymentDepositMaximum)
		return nil, badRequest("INVALID_AMOUNT_RANGE")
	}

	// [********] get EXACTLY same amount from previse deposit order in last 5 minutes (any order)
	confSameDepositMin := time.Minute * 5 // minutes
	if pOrder, err := s.repo.CheckPostmanPayDepositOrderInLast5Minutes(req.UserId, req.Amount); err != nil {
		if err != gorm.ErrRecordNotFound {
			return nil, internalServerError(err)
		}
	} else if pOrder != nil {
		actionAtUtc := time.Now().UTC()
		if pOrder.CreatedAt.Add(confSameDepositMin).After(actionAtUtc) {
			result.UserId = pOrder.UserId
			result.OrderNo = pOrder.OrderNo
			result.Amount = pOrder.Amount
			result.TransferAmount = pOrder.TransferAmount
			result.TransactionStatus = pOrder.TransactionStatus
			result.QrCode = pOrder.QrPromptpay
			// result.PaymentPageUrl = pOrder.PaymentPageUrl
			result.CreatedAt = pOrder.CreatedAt

			imgData, err := qrcode.Encode(pOrder.QrPromptpay, qrcode.Medium, 256)
			if err != nil {
				// return nil, fmt.Errorf("unable to encode png: %w", err)
				return &result, nil
			}
			// encode to base64
			img, err := png.Decode(bytes.NewReader(imgData))
			if err != nil {
				// return nil, fmt.Errorf("unable to decode jpeg: %w", err)
				return &result, nil
			}
			var buf bytes.Buffer
			if err := png.Encode(&buf, img); err != nil {
				// return nil, fmt.Errorf("unable to encode png: %w", err)
				return &result, nil
			}
			result.QrBase64 = base64.StdEncoding.EncodeToString(buf.Bytes())
			return &result, nil
		}
	}

	webhookDomain := os.Getenv("CYBERGAME_WEBHOOK_ENDPOINT")
	if webhookDomain == "" {
		return nil, errors.New("EMPTY_ENV_CYBERGAME_WEBHOOK_ENDPOINT")
	}

	user, err := s.repo.GetUserBankDetailById(req.UserId)
	if err != nil {
		return nil, badRequest("INVALID_USER")
	}
	if user == nil || user.BankAccount == "" || user.BankCode == "" {
		return nil, badRequest("INVALID_USER_ACCOUNT")
	}
	remoteBankCode, err := GetPostmanPayCustomerBank(user.BankCode)
	if err != nil || remoteBankCode == "" {
		return nil, badRequest("USER_BANK_NOT_SUPPORTED")
	}

	accountingRepo := repository.NewAccountingRepository(s.repo.GetDb())
	configWeb, err := s.repo.GetWebConfiguration()
	if err != nil {
		log.Println("CreatePostmanPayDeposit.GetWebConfiguration", err)
		return nil, badRequest("WEB_CONFIG_NOT_FOUND")
	}
	if user.Id != 0 && configWeb != nil {
		// [********] ฝากครั้งแรกขั้นต่ำ ปรับ flow เช็ค user ให้เช็คจากรายการฝากครั้งแรกจริงๆ ไม่เช็คจากรหัสที่ได้รับ
		if err := CheckFirstMinimunDeposit(accountingRepo, user.Id, req.Amount, *configWeb); err != nil {
			return nil, badRequest(fmt.Sprintf("ฝากครั้งแรกขั้นต่ำ %d บาท", configWeb.MinFirstMemberDeposit))
		}
	} else {
		log.Println("CreatePostmanPayDeposit.CheckFirstMinimunDeposit", "CANT_CHECK_FIRST_MINIMUM_DEPOSIT_ON_EMPTY_CONFIG")
	}

	// ===========================================================================================
	var createBody model.PostmanPayOrderCreateBody
	createBody.UserId = req.UserId
	createBody.OrderTypeId = model.POSTMANPAY_ORDER_TYPE_DEPOSIT
	createBody.OrderNo = "" // Autogen
	createBody.Amount = req.Amount
	createBody.TransactionStatus = "PENDING"
	insertId, err := s.repo.CreateDbPostmanPayOrder(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	// reget Order
	pendingOrder, err := s.repo.GetDbPostmanPayOrderById(*insertId)
	if err != nil {
		// SET AS ERROR
		remark := "Error GetDbPostmanPayOrderById, " + err.Error()
		if err := s.repo.UpdateDbPostmanPayOrderError(*insertId, remark); err != nil {
			log.Println("CreatePostmanPayDeposit.UpdateDbPostmanPayOrderError", err)
		}
		return nil, internalServerError(err)
	}
	// Create POSTMANPAY Order
	var remoteRequest model.PostmanPayDepositCreateRemoteRequest
	remoteRequest.TransactionId = pendingOrder.OrderNo
	remoteRequest.BankAccountNumber = user.BankAccount
	remoteRequest.BankName = remoteBankCode
	remoteRequest.Name = user.Fullname
	remoteRequest.Amount = req.Amount
	remoteRequest.CallbackUrl = fmt.Sprintf("%s/postmanpay/dep-callback", webhookDomain)
	remoteRequest.Type = "QR"
	remoteResp, err := s.repo.PostmanPayDeposit(*pgAccount, remoteRequest)
	if err != nil {
		// SET AS ERROR
		remark := "Error PostmanPayDeposit, " + err.Error()
		if err := s.repo.UpdateDbPostmanPayOrderError(*insertId, remark); err != nil {
			log.Println("PostmanPayDeposit.UpdateDbPostmanPayOrderError", err)
		}
		// SysLog At REPO
		return nil, internalServerError(err)
	}

	// fmt.Println("PostmanPayDeposit.remoteResp", helper.StructJson(remoteResp))

	// Parse Float Amount
	// transferAmount, err := strconv.ParseFloat(remoteResp.Data.Amount, 64)
	// if err != nil {
	// 	// SET AS ERROR
	// 	remark := "Error ParseFloat Amount, " + err.Error()
	// 	if err := s.repo.UpdateDbPostmanPayOrderError(*insertId, remark); err != nil {
	// 		log.Println("CreatePostmanPayDeposit.UpdateDbPostmanPayOrderError", err)
	// 	}
	// 	return nil, internalServerError(err)
	// }
	transferAmount := remoteResp.Data.DepositAmount

	// onCreate Success
	var updateBody model.PostmanPayOrderUpdateBody
	updateBody.TransactionNo = remoteResp.Data.ReferenceId
	updateBody.TransactionDate = time.Now().UTC()
	updateBody.TransactionStatus = "WAIT_PAYMENT"
	updateBody.TransferAmount = transferAmount
	updateBody.QrPromptpay = remoteResp.Data.Qrcode
	// updateBody.PaymentPageUrl = remoteResp.Data.Redirect
	if err := s.repo.UpdateDbPostmanPayOrder(*insertId, updateBody); err != nil {
		// SET AS ERROR
		remark := "Error UpdateDbPostmanPayOrder, " + err.Error()
		if err := s.repo.UpdateDbPostmanPayOrderError(*insertId, remark); err != nil {
			log.Println("CreatePostmanPayDeposit.UpdateDbPostmanPayOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	waitPayOrder, err := s.repo.GetDbPostmanPayOrderById(*insertId)
	if err != nil {
		return nil, internalServerError(err)
	}

	result.UserId = waitPayOrder.UserId
	result.OrderNo = waitPayOrder.OrderNo
	result.Amount = waitPayOrder.Amount
	result.TransferAmount = transferAmount
	result.TransactionStatus = waitPayOrder.TransactionStatus
	result.QrCode = waitPayOrder.QrPromptpay
	// result.PaymentPageUrl = waitPayOrder.PaymentPageUrl
	result.CreatedAt = waitPayOrder.CreatedAt

	imgData, err := qrcode.Encode(waitPayOrder.QrPromptpay, qrcode.Medium, 256)
	if err != nil {
		// return nil, fmt.Errorf("unable to encode png: %w", err)
		return &result, nil
	}
	// encode to base64
	img, err := png.Decode(bytes.NewReader(imgData))
	if err != nil {
		// return nil, fmt.Errorf("unable to decode jpeg: %w", err)
		return &result, nil
	}
	var buf bytes.Buffer
	if err := png.Encode(&buf, img); err != nil {
		// return nil, fmt.Errorf("unable to encode png: %w", err)
		return &result, nil
	}
	result.QrBase64 = base64.StdEncoding.EncodeToString(buf.Bytes())

	return &result, nil
}

func (s paygatePostmanPayService) CreatePostmanPayWithdraw(req model.PostmanPayWithdrawCreateRequest) (*int64, error) {

	// Ruled by Provider
	// - loan trade amount must greater than 100
	if req.Amount < model.POSTMANPAY_DEFMIN_WITHDRAW_AMOUNT || req.Amount > model.POSTMANPAY_DEFMAX_WITHDRAW_AMOUNT {
		return nil, badRequest("INVALID_AMOUNT_RANGE")
	}

	pgAccount, err := s.GetPostmanPayAccount()
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}
	if !pgAccount.IsWithdrawEnabled {
		return nil, badRequest("PAYGATE_WITHDRAW_NOT_ENABLED")
	}
	// [Prerequisites] EP + CLIENTID + APIKEY + SECRET + MERCHANTID
	if pgAccount.ApiEndPoint == "" || pgAccount.PartnerKey == "" || pgAccount.AccessKey == "" || pgAccount.SecretKey == "" || pgAccount.MerchantId == "" {
		return nil, errors.New("PAYGATE_EMPTY_SETTING")
	}

	user, err := s.repo.GetUserBankDetailById(req.UserId)
	if err != nil {
		return nil, badRequest("INVALID_USER")
	}
	if user == nil || user.BankAccount == "" || user.BankCode == "" {
		return nil, badRequest("INVALID_USER_ACCOUNT")
	}

	webhookDomain := os.Getenv("CYBERGAME_WEBHOOK_ENDPOINT")
	if webhookDomain == "" {
		return nil, errors.New("EMPTY_ENV_CYBERGAME_WEBHOOK_ENDPOINT")
	}

	withdrawBankCode, err := GetPostmanPayCustomerBank(user.BankCode)
	if err != nil || withdrawBankCode == "" {
		return nil, badRequest("USER_BANK_NOT_SUPPORTED")
	}

	// token, err := s.GetPostmanPayAccessToken(pgAccount)
	// if err != nil {
	// 	// return nil, internalServerError(errors.New("INVALID_ACCESS_TOKEN"))
	// 	return nil, err
	// }

	// ===========================================================================================
	// CREATE Order
	var createBody model.PostmanPayOrderCreateBody
	createBody.RefId = &req.RefId
	createBody.UserId = req.UserId
	createBody.OrderTypeId = model.POSTMANPAY_ORDER_TYPE_WITHDRAW
	createBody.OrderNo = "" // Autogen
	createBody.Amount = req.Amount
	createBody.TransactionStatus = "PENDING"
	insertId, err := s.repo.CreateDbPostmanPayOrder(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	// reget Order
	pendingOrder, err := s.repo.GetDbPostmanPayOrderById(*insertId)
	if err != nil {
		// SET AS ERROR
		remark := "Error GetDbPostmanPayOrderById, " + err.Error()
		if err := s.repo.UpdateDbPostmanPayOrderError(*insertId, remark); err != nil {
			log.Println("CreatePostmanPayWithdraw.UpdateDbPostmanPayOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// Create POSTMANPAY Order
	var remoteRequest model.PostmanPayWithdrawCreateRemoteRequest
	remoteRequest.TransactionId = pendingOrder.OrderNo
	remoteRequest.Amount = pendingOrder.Amount
	remoteRequest.BankAccountNumber = user.BankAccount
	remoteRequest.BankName = withdrawBankCode
	remoteRequest.Name = user.Fullname
	remoteRequest.Phone = user.Phone
	remoteRequest.CallbackUrl = fmt.Sprintf("%s/postmanpay/wid-callback", webhookDomain)
	remoteResp, err := s.repo.PostmanPayWithdraw(*pgAccount, remoteRequest)
	if err != nil {
		// SET AS ERROR
		remark := "Error PostmanPayWithdraw, " + err.Error()
		if err := s.repo.UpdateDbPostmanPayOrderError(*insertId, remark); err != nil {
			log.Println("PostmanPayWithdraw.UpdateDbPostmanPayOrderError", err)
		}
		// SysLog At REPO
		return nil, internalServerError(err)
	}

	// fmt.Println("CreatePostmanPayWithdraw.remoteResp", helper.StructJson(remoteResp))

	if remoteResp.Data.ReferenceId == "" {
		// SET AS ERROR
		remark := remoteResp.Message
		if len(remark) == 0 {
			remark = "UNKNOW_ERROR from withdrawWithPostmanPay"
		}
		if err := s.repo.UpdateDbPostmanPayOrderError(*insertId, remark); err != nil {
			log.Println("PostmanPayWithdraw.UpdateDbPostmanPayOrderError", err)
		}
		// SysLog
		if _, err := s.repo.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "CreatePostmanPayWithdraw.PostmanPayWithdraw",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(remoteRequest),
			JsonResponse: helper.StructJson(remoteResp),
		}); err != nil {
			log.Println("CreatePostmanPayWithdraw.CreateSysLog", err)
		}
		return nil, internalServerError(fmt.Errorf("UNKNOW_ERROR"))
	}

	// onCreate Success
	var updateBody model.PostmanPayOrderUpdateBody
	updateBody.TransactionNo = remoteResp.Data.ReferenceId
	updateBody.TransactionDate = time.Now().UTC()
	updateBody.TransactionStatus = "WAIT_PAYMENT"
	updateBody.QrPromptpay = remoteResp.Message
	if err := s.repo.UpdateDbPostmanPayOrder(*insertId, updateBody); err != nil {
		// SET AS ERROR
		remark := "Error UpdateDbPostmanPayOrder, " + err.Error()
		if err := s.repo.UpdateDbPostmanPayOrderError(*insertId, remark); err != nil {
			log.Println("CreatePostmanPayWithdraw.UpdateDbPostmanPayOrderError", err)
		}
		return nil, internalServerError(err)
	}
	return insertId, nil
}

func CreateCustomerDepositFromPostmanPayOrder(repo repository.PostmanPayRepository, orderId int64, adminId int64) (*int64, error) {

	// ** ทุกรายการจะต้องเช็คสถานะใหม่ทั้งหมด เพราะ admin แย่งกดอนุมัติได้
	item, err := repo.GetRawPostmanPayPendingDepositOrderById(orderId)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, badRequest("ORDER_NOT_FOUND")
		}
		return nil, internalServerError(err)
	}

	// Check BankTransactionStatus = 'PENDING'
	if item.BankTransactionStatus != "PENDING" {
		return nil, badRequest("ORDER_NOT_PENDING")
	}

	return createCustomerDepositFromPostmanPay(repo, *item, adminId)
}

func createCustomerDepositFromPostmanPay(repo repository.PostmanPayRepository, item model.PostmanPayOrderResponse, adminId int64) (*int64, error) {

	var externalNoti model.NotifyExternalNotificationRequest
	actionAt := time.Now().UTC()
	// confirmByUserId := int64(0)

	depositAmount := item.Amount

	user, err := repo.GetUserBankDetailById(item.UserId)
	if err != nil {
		log.Println("createCustomerDepositFromPostmanPay.GetUserBankDetailByMemberCode", err)
		return nil, internalServerError(err)
	}

	var promotionWebUserId int64
	if getPromotionWebUser, err := repo.GetDepositCurrentProcessingUserPromotion(item.UserId); err != nil {
		log.Println("createCustomerDepositFromPostmanPay.GetDepositCurrentProcessingUserPromotion", err)
	} else if getPromotionWebUser != nil {
		promotionWebUserId = getPromotionWebUser.Id
	}

	// MemberCode
	if user.MemberCode == "" {
		memberCode, err := GenUniqueUserMemberCode(repository.NewUserRepository(repo.GetDb()), user.Id)
		if err != nil {
			log.Println("createCustomerDepositFromPostmanPay.GenUniqueUserMemberCode", err)
			return nil, internalServerError(err)
		}
		user.MemberCode = *memberCode
	}

	// layer — 2024-04-02 at 10:06 AM
	// ถ้าแก้ตามนี้แล้วน่าจะไม่เจอ ถ้าเคสกันซ้ำเราครอบคลุม
	// @Dunk @TULA
	// กันซ้ำคือ heng ไปซ้ำกับแอดมินเติมมือ
	// ----
	// เงื่อนไขกันซ้ำ
	// 1 หารายการฝากสำเร็จล่าสุดของยูสนั้น
	// 2 ถ้ามีรายการล่าสุดที่เป็นเติมมือ
	// -เช็คเวลาเติม ต้องมากกว่าเวลาโอนในสลิป
	// -ยอดเงินเท่ากัน
	// **ถ้าเข้าเงื่อนไขทั้งหมด คือแสดงว่า webhook เข้าทีหลังแอดมินเติมมือ
	// ต้อง bypass ไม่ให้เติมอีก และอัพเดท status ว่าจับคู่ได่แล้ว
	// ---
	// วิธีนี้น่าจะใช้ได้กับทุก webhook (heng, fastbank) ที่มีเวลาโอนสลิปมาให้
	duplicateRemark := "INIT"
	hasDuplicateDeposit := false
	manualDeposit, err := repo.GetLastestBankAdminManualDepositTransaction(user.Id)
	if err != nil {
		if err.Error() != recordNotFound {
			log.Println("createCustomerDepositFromPostmanPay.GetLastestBankAdminManualDepositTransaction", err)
			return nil, internalServerError(err)
		}
		// NOT_FOUND = ถ้าไม่เจอ ให้ทำต่อ
		duplicateRemark = "OK_NO_MANUAL_DEPOSIT"
	}
	if manualDeposit != nil && item.TransactionDate != nil {
		if manualDeposit.TransferAt.After(*item.TransactionDate) {
			// ถ้าเวลาเติมมือมากกว่าเวลาโอนในสลิป
			if manualDeposit.CreditAmount == depositAmount {
				// ถ้ายอดเงินเท่ากัน
				duplicateRemark = "STOP_MANUAL_DEPOSIT_AFTER_AND_SAME_AMOUNT"
				hasDuplicateDeposit = true
			} else {
				duplicateRemark = "OK_MANUAL_DEPOSIT_AMOUNT_NOT_MATCH"
			}
		} else {
			duplicateRemark = "OK_MANUAL_DEPOSIT_BEFORE_WITHDRAW"
		}
	}
	if duplicateRemark != "INIT" {
		// WebhookLog
		var createBody2 model.PostmanPayWebhookCreateBody
		createBody2.Name = "POSTMANPAY_DEPOSIT_DUPLICATE_CHECKER"
		createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
			"item":                item,
			"method":              "createCustomerDeposit",
			"hasDuplicateDeposit": hasDuplicateDeposit,
			"error":               duplicateRemark,
			"manualDeposit":       manualDeposit,
		})
		if _, err = repo.CreatePostmanPayWebhook(createBody2); err != nil {
			log.Println("Error CreatePostmanPayWebhook.CreatePostmanPayWebhook", err)
		}
		if hasDuplicateDeposit {
			return nil, internalServerError(fmt.Errorf("DUPLICATE_DEPOSIT"))
		}
	}

	pgAccount, err := GetPostmanPayAccount(repo)
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}

	// ===================================================
	paygateRepo := repository.NewPaymentGatewayRepository(repo.GetDb())
	accountingRepo := repository.NewAccountingRepository(repo.GetDb())
	luckyWheelRepo := repository.NewActivityLuckyWheelRepository(repo.GetDb())
	promotionWebRepo := repository.NewPromotionWebRepository(repo.GetDb())
	notiRepo := repository.NewNotificationRepository(repo.GetDb())

	// Affiliate + Alliance Income
	member, err := repo.GetMemberById(user.Id)
	if err != nil {
		return nil, internalServerError(err)
	}
	if member.UserTypeName == "NONE" {
		// ถ้าฝากครั้งแรก จะได้เป็น AFFILIATE + REFER COMMISION
		if err := UserFirstDepositCommission(accountingRepo, *member, depositAmount); err != nil {
			log.Println("CreateFristTimeDepositRecord.UserFirstDepositCommission.ERROR", err)
			return nil, internalServerError(err)
		}
	}

	isAutoDeposit := true // Auto ฝาก นับตั้งแต่ได้รับยอดฝาก จนได้ยืนยันเครดิต
	if adminId != 0 {
		isAutoDeposit = false
	}

	// CREATE BANK TRANSACTION
	var createBankTransaction model.BankTransactionCreateBody
	createBankTransaction.MemberCode = user.MemberCode
	createBankTransaction.UserId = user.Id
	// createBankTransaction.StatementId = 0
	createBankTransaction.TransactionTypeId = model.TRANSACTION_TYPE_DEPOSIT
	// createBankTransaction.FromAccountName = "" // empty?
	createBankTransaction.ToAccountName = &pgAccount.TypeName
	createBankTransaction.ToAccountNumber = &pgAccount.ShopName
	createBankTransaction.PromotionId = &promotionWebUserId
	createBankTransaction.CreditAmount = depositAmount
	// createBankTransaction.BonusAmount = 0
	// createBankTransaction.BonusReason = nil
	createBankTransaction.DepositChannel = "POSTMANPAY PAYMENT GATEWAY"
	createBankTransaction.TransactionStatusId = model.TRANS_STATUS_PENDING
	createBankTransaction.CreatedByAdminId = 0
	createBankTransaction.IsAutoCredit = isAutoDeposit
	createBankTransaction.TransferAt = &actionAt
	// create transaction
	transId, err := repo.InsertBankTransaction(createBankTransaction)
	if err != nil {
		log.Println("createCustomerDepositFromPostmanPay.InsertBankTransaction", err)
		return nil, internalServerError(err)
	}

	// var actionStatement model.BankStatementMatchRequest
	// actionStatement.StatementId = *bankstatementId
	// actionStatement.UserId = user.Id
	// actionStatement.ConfirmedAt = time
	// actionStatement.ConfirmedByAdminId = &req.CreateByUserId
	// if err := s.serviceAccounting.SetStatementOwnerMatched(*bankstatementId, actionStatement, model.USE_ENDING_NOTI); err != nil {
	// 	log.Println("createCustomerDepositFromPostmanPay.SetStatementOwnerMatched", err)
	// 	return nil, internalServerError(err)
	// }

	// DECREASE FASTBANK CREDIT 2.FROM create auto DEPOSIT
	// if err := repoAccounting.DecreaseFastbankCredit(1); err != nil {
	// 	log.Println("createCustomerDepositFromPostmanPay.DECREASE_FASTBANK_CREDIT_ERROR", err)
	// }

	var confirmDeposit model.BankConfirmDepositRequest
	confirmDeposit.TransferAt = &actionAt
	confirmDeposit.ConfirmedAt = actionAt
	confirmDeposit.ConfirmedByAdminId = &adminId
	if err := confirmDepositTransaction(paygateRepo, *transId, confirmDeposit); err != nil {
		log.Println("createCustomerDepositFromPostmanPay.confirmDepositTransaction", err)
		return nil, internalServerError(err)
	}

	// UPDATE HENG ORDER - BANK SUCCESS
	if err := repo.UpdatePostmanPayOrderBankSucess(item.Id, *transId, adminId); err != nil {
		log.Println("createCustomerDepositFromPostmanPay.UpdatePaygateOrderBankSucess", err)
	}

	isFirstDeposit := repo.IsFirstDeposit(user.Id)
	if isFirstDeposit {
		var bonusReq model.UserFirstDepositCreateRequest
		bonusReq.UserId = user.Id
		bonusReq.TransactionId = transId
		bonusReq.TransferAt = actionAt
		bonusReq.Amount = depositAmount
		bonusReq.Remark = "createCustomerDepositFromPostmanPay"
		if err := SetFirstDepositBonus(accountingRepo, isFirstDeposit, bonusReq); err != nil {
			webhookLogMessage := fmt.Sprintf("createCustomerDepositFromPostmanPay.userFirstDepositBonus, ERROR: %s", err)
			log.Println("SetFirstDepositBonus", webhookLogMessage)
			// return nil, errors.New(webhookLogMessage)
		}
	}

	// [USER_CREDIT]
	var userCreditReq model.UserTransactionCreateRequest
	userCreditReq.UserId = user.Id
	userCreditReq.RefId = transId
	userCreditReq.TransferAt = &actionAt
	userCreditReq.PromotionId = &promotionWebUserId
	userCreditReq.TypeId = model.CREDIT_TYPE_DEPOSIT
	// userCreditReq.AccountId = &bankAccount.Id
	userCreditReq.Detail = "POSTMANPAY PAYMENT GATEWAY"
	userCreditReq.Amount = depositAmount
	userCreditReq.CreateBy = &adminId
	userCreditReq.ConfirmBy = &adminId
	userCreditReq.IsAdjustAuto = isAutoDeposit
	userCreditReq.PaymentMerchatId = &pgAccount.ProviderId
	if agentResp, err := repo.IncreaseUserCredit(userCreditReq); err != nil {
		log.Println("createCustomerDepositFromPostmanPay.IncreaseUserCredit", err)
		return nil, internalServerError(err)
	} else {
		// AGENT_SUCCESS
		if err := repo.UpdateDepositTransactionStatusFromAgent(*transId, *agentResp); err != nil {
			log.Println("createCustomerDepositFromPostmanPay.UpdateDeporsitTransactionStatusFromAgent", err)
		}
	}

	// [Lucky Wheel] ตอนฝากละได้โบนัส
	var luckyWheelBody model.ActivityLuckyWheelRoundUserRequest
	luckyWheelBody.UserId = user.Id
	luckyWheelBody.ConditionId = model.ACTIVITY_LUCKY_WHEEL_SETTING_CONDITION_DEPOSIT
	luckyWheelBody.ConditionAmount = depositAmount
	if err := CreateRoundActivityLuckyWheel(luckyWheelRepo, luckyWheelBody); err != nil {
		log.Println("createCustomerDepositFromPostmanPay.CreateRoundActivityLuckyWheel", err)
	}

	// [TIER]
	if err := repo.IncreaseUserTierDepositAmount(user.Id, depositAmount); err != nil {
		log.Println("MatchStatementOwner.IncreaseUserTierDepositAmount", err)
	}

	var checkUserPromotionBody model.CheckUserPromotionBody
	checkUserPromotionBody.UserId = user.Id
	_, err = CheckUserPromotion(promotionWebRepo, checkUserPromotionBody)
	if err != nil {
		log.Println("createCustomerDepositFromPostmanPay.CheckUserPromotion", err)
	}
	// [turnOver] ถ้ามีการฝากเงิน ให้สร้าง TurnOver
	turnoverShare := repository.NewTurnoverRepository(repo.GetDb())
	if err := CreateTurnOverFromSuccessDeposit(turnoverShare, user.Id, depositAmount, *transId); err != nil {
		log.Println("createCustomerDeposit.CreateTurnOverFromSuccessDeposit", err)
	}

	// ===================================================

	// [ notify]
	notiAtUtc := time.Now().UTC()
	externalNoti.TypeNotify = model.IsDepositAfterCredit
	externalNoti.TransId = transId
	externalNoti.Amount = depositAmount
	externalNoti.MemberCode = user.MemberCode
	externalNoti.UserCredit = user.Credit + depositAmount
	externalNoti.ConfirmedByAdminId = 0
	externalNoti.TransferDateTime = notiAtUtc.Add(7 * time.Hour).Format("2006-01-02 15:04:05")
	externalNoti.ActionTime = notiAtUtc.Add(7 * time.Hour).Format("2006-01-02 15:04:05")
	endTime := notiAtUtc
	elapsed := endTime.Sub(actionAt)
	elapsedSeconds := elapsed.Seconds()
	timeElapsed := fmt.Sprintf("%.2f", elapsedSeconds)
	externalNoti.TimerCounter = timeElapsed

	externalNoti.WebScoket.UserID = user.Id
	externalNoti.WebScoket.Amount = depositAmount
	externalNoti.WebScoket.MemberCode = user.MemberCode
	externalNoti.WebScoket.AlertType = "DEPOSIT"
	if err := repo.UpdateAutoProcessTimer(timeElapsed, *transId); err != nil {
		return nil, nil
	}
	if err := ExternalNotification(notiRepo, externalNoti); err != nil {
		log.Println("FailedNotify", err)
	}
	return nil, nil
}

func approveCustomerWithdrawFromPostmanPay(repo repository.PostmanPayRepository, transId int64) (*int64, error) {

	withdrawTrans, err := repo.GetBankTransactionById(transId)
	if err != nil {
		log.Println("approveCustomerWithdrawFromPostmanPay.GetUserBankDetailById", err)
		return nil, internalServerError(err)
	}

	// ============================= ON_SUCCESS =================================
	confirmAtUtc := time.Now().UTC()
	if withdrawTrans.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		// [create transaction action success]
		var createConfirm model.CreateSuccessTransferWithdrawRequest
		createConfirm.TransactionId = withdrawTrans.Id
		createConfirm.ConfirmedAt = confirmAtUtc
		var setIdAuto int64 = 0
		createConfirm.ConfirmedByAdminId = &setIdAuto
		if _, err := CreateSuccessTransferWithdraw(repository.NewBankingRepository(repo.GetDb()), createConfirm); err != nil {
			log.Println("approveCustomerWithdrawFromPostmanPay.CreateSuccessTransferWithdraw", err)
			return nil, nil
		}
		// [Show Withdraw USER_TRANSACTION]
		var showUserTrans model.UserTransactionShowUpdate
		showUserTrans.TransactionId = withdrawTrans.Id
		showUserTrans.ConfirmedAt = &createConfirm.ConfirmedAt
		showUserTrans.ConfirmAdminId = createConfirm.ConfirmedByAdminId
		if err := repo.ShowUserTransaction(showUserTrans); err != nil {
			log.Println("approveCustomerWithdrawFromPostmanPay.ShowUserTransaction", err)
			return nil, nil
		}
		// [update transaction status]
		secondUsed := "0.0" // timing
		if withdrawTrans.IsAutoCredit && withdrawTrans.TransferAt != nil {
			seconds := confirmAtUtc.Sub(withdrawTrans.TransferAt.UTC()).Seconds()
			secondUsed = fmt.Sprintf("%.2f", seconds)
		}
		if err := repo.UpdateTransactionStatusTransferingToSuccess(withdrawTrans.Id, secondUsed); err != nil {
			log.Println("approveCustomerWithdrawFromPostmanPay.UpdateTransactionStatusTransferingToSuccess", err)
			return nil, internalServerError(err)
		}

		// [notify SUCCESS]
		var externalNoti model.NotifyExternalNotificationRequest
		notiRepo := repository.NewNotificationRepository(repo.GetDb())
		externalNoti.TypeNotify = model.IsWithdrawalCreditSuccess
		externalNoti.TransId = &transId
		externalNoti.Amount = withdrawTrans.CreditAmount
		externalNoti.MemberCode = withdrawTrans.MemberCode
		externalNoti.UserCredit = withdrawTrans.CreditAmount
		externalNoti.ConfirmedByAdminId = 0
		externalNoti.TimerCounter = secondUsed
		externalNoti.TransferDateTime = withdrawTrans.TransferAt.Add(7 * time.Hour).Format("2006-01-02 15:04:05")
		externalNoti.ActionTime = confirmAtUtc.Add(7 * time.Hour).Format("2006-01-02 15:04:05")
		if err := ExternalNotification(notiRepo, externalNoti); err != nil {
			log.Println("FailedNotify", err)
		}

	}
	return nil, nil
}

// func rollbackCustomerWithdrawFromPostmanPay(repo repository.PostmanPayRepository, transId int64) (*int64, error) {

// 	withdrawTrans, err := repo.GetBankTransactionById(transId)
// 	if err != nil {
// 		log.Println("rollbackCustomerWithdrawFromPostmanPay.GetBankTransactionById", err)
// 		return nil, internalServerError(err)
// 	}

// 	// ============================= ON_SUCCESS =================================
// 	if withdrawTrans.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_TRASNFERING {
// 		// [update transaction status]
// 		if err := repo.RollbackTransactionStatusTransferingToConfirmed(withdrawTrans.Id); err != nil {
// 			log.Println("approveCustomerWithdrawFromPostmanPay.RollbackTransactionStatusTransferingToConfirmed", err)
// 			return nil, internalServerError(err)
// 		}
// 	}
// 	return nil, nil
// }

func (s paygatePostmanPayService) CreateSystemLog(name string, req interface{}) error {

	// [SYSLOG] HACKED
	_, err := s.repo.CreateSystemLog(model.SystemLogCreateBody{
		Name:   name,
		Status: "HACKING",
		JsonRequest: helper.StructJson(map[string]interface{}{
			"req": req,
		}),
	})
	if err != nil {
		log.Println(name, "CreateSystemLog.ERROR", err.Error())
	}
	return nil
}

func (s paygatePostmanPayService) CreatePostmanPayDepositWebhook(req model.PostmanPayWebhookRequest) (*int64, error) {

	var createBody model.PostmanPayWebhookCreateBody
	createBody.Name = "POSTMANPAY_DEPOSIT_CALLBACK"
	createBody.JsonPayload = req.JsonPayload
	insertId, err := s.repo.CreatePostmanPayWebhook(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// DEPOSIT
	// {
	// 	"referenceId": "{{referenceId}}", // The unique reference ID for the transaction
	// 	"transactionId": "{{transactionId}}", // The unique transaction ID
	// 	"clientId": "{{CLIENT_ID}}", // The client's unique identifier
	// 	"merchantId": "{{MERCHANT_ID}}", // The merchant's unique identifier
	// 	"walletId": "{{WALLET_ID}}", // The wallet ID associated with the transaction
	// 	"bankCode": "074", // The bank code (e.g., TTB)
	// 	"bankName": "TTB", // The bank name (e.g., TTB)
	// 	"bankAccountNumber": "62xxxxxx40", // The bank account number involved in the transaction
	// 	"bankAccountName": "", // The bank account holder's name (if available)
	// 	"amount": 400.64, // The transaction amount
	// 	"status": "completed", // The status of the transaction (e.g., "completed","expired")
	// 	"timestamp": **********, // Unix timestamp of when the transaction was initiated
	// 	"matchTimestamp": *************, // Unix timestamp of when the transaction was matched
	// 	"type": "QR", // The type of transaction (e.g., QR)
	// 	"hash": "U2FsdGVkX1/Z6EHf3XX6hmzlnK7TvYoLKsUlyR0F47LIERkRq2fKKhKwpwq3j9wu" // AES-encrypted hash of the transactionId
	// }
	var remoteResp model.PostmanPayDepositWebhookResponse
	errJson := json.Unmarshal([]byte(req.JsonPayload), &remoteResp)
	if errJson != nil {
		log.Println("Error unmarshal json", errJson)
		return nil, internalServerError(errJson)
	}

	if remoteResp.ReferenceId == "" || remoteResp.TransactionId == "" {
		log.Println("CreatePostmanPayDepositWebhook.NoRef", helper.StructJson(remoteResp))
		return nil, badRequest("POSTMANPAY_DEPOSIT_HOOK_NOREF")
	}

	// Check Response Status.
	successStatus := strings.ToUpper(strings.TrimSpace(remoteResp.Status))
	respStatusKey := "U"
	if successStatus == "COMPLETED" {
		successStatus = "PAID" // Always use PAID
		respStatusKey = "P"    // completed ได้ครั้งเดียว
	} else if successStatus == "EXPIRED" {
		respStatusKey = "D"
	} else {
		log.Println("CreatePostmanPayDepositWebhook.remoteResp.desc.ELSE", successStatus)
		// เก็บตามจริง successStatus = "ERROR"
		respStatusKey = "E"
	}

	// Service Race Condition by Ref1(MchOrderNo) + perStatus
	acAtUtc := time.Now().UTC()
	var rcCreateBody model.RaceActionCreateBody
	rcCreateBody.Name = "CreatePostmanPayDepositWebhook"
	rcCreateBody.JsonRequest = helper.StructJson(req)
	rcCreateBody.Status = "PENDING"
	// ActionKey = Ref2-MINUTE
	rcCreateBody.ActionKey = fmt.Sprintf("PG14D%s-%s%s", acAtUtc.Format("**********"), remoteResp.TransactionId, respStatusKey)
	rcCreateBody.UnlockAt = acAtUtc.Add(time.Second * 1)
	if _, err := s.repo.CreateRaceCondition(rcCreateBody); err != nil {
		return insertId, internalServerError(err)
	}

	// Get Posible Order
	var query model.PostmanPayOrderListRequest
	query.OrderNo = remoteResp.TransactionId
	query.TransactionNo = remoteResp.ReferenceId
	// query.Amount = fmt.Sprintf("%f", remoteResp.Amount)
	query.Status = "WAIT_PAYMENT"
	query.Limit = 1
	list, _, err := s.repo.GetDbPostmanPayOrderList(query)
	if err != nil {
		return nil, internalServerError(err)
	}

	// fmt.Println("PostmanPayDecryptRepayDespositPayload.list", helper.StructJson(list))

	// ** ไม่ได้เช็ค amount เพราะจะมีระบบที่ใช้ ทศนิยม และไม่เท่ากัน แต่ก็ควรเช็คแบบ +- 10%

	if len(list) > 0 {
		for _, item := range list {

			// Payment Success, Create Deposit if UserId is not null
			if item.UserId > 0 && item.OrderTypeId == model.POSTMANPAY_ORDER_TYPE_DEPOSIT {
				if successStatus == "PAID" {
					// Update Order
					if err := s.repo.ApproveDbPostmanPayOrder(item.Id, successStatus); err != nil {
						return nil, internalServerError(err)
					}
					if _, err := CreateCustomerDepositFromPostmanPayOrder(s.repo, item.Id, 0); err != nil {
						// WebhookLog
						var createBody2 model.PostmanPayWebhookCreateBody
						createBody2.Name = "POSTMANPAY_DEPOSIT_ERROR"
						createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
							"item":   item,
							"method": "createCustomerDepositFromPostmanPay",
							"error":  err.Error(),
						})
						if _, err = s.repo.CreatePostmanPayWebhook(createBody2); err != nil {
							log.Println("Error CreatePostmanPayWebhook.createCustomerDepositFromPostmanPay", err)
						}
					}
				}
			} else if item.UserId > 0 && item.OrderTypeId == model.POSTMANPAY_ORDER_TYPE_WITHDRAW {
				if successStatus == "PAID" {
					// Update Order
					if err := s.repo.ApproveDbPostmanPayOrder(item.Id, successStatus); err != nil {
						return nil, internalServerError(err)
					}
					if _, err := approveCustomerWithdrawFromPostmanPay(s.repo, *item.RefId); err != nil {
						// WebhookLog
						var createBody2 model.PostmanPayWebhookCreateBody
						createBody2.Name = "POSTMANPAY_WITHDRAW_ERROR"
						createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
							"item":   item,
							"method": "approveCustomerWithdrawFromPostmanPay",
							"error":  err.Error(),
						})
						if _, err = s.repo.CreatePostmanPayWebhook(createBody2); err != nil {
							log.Println("Error approveCustomerWithdrawFromPostmanPay.CreatePostmanPayWebhook", err)
						}
					}
				} else if successStatus == "FAILED" || successStatus == "REJECTED" || successStatus == "EXPIRED" {
					// Update Order
					if err := s.repo.ApproveDbPostmanPayOrder(item.Id, successStatus); err != nil {
						return nil, internalServerError(err)
					}
					if err := s.cancelWithdrawFromPostmanPayWebhookError(item); err != nil {
						log.Println("Error UpdateDbPostmanPayOrderError", err)
					}
				}
			} else {
				log.Println("ApproveDbPostmanPayOrder.WithoutItemUpdated", helper.StructJson(item))
			}
		}
	}
	return insertId, nil
}

func (s paygatePostmanPayService) CreatePostmanPayWithdrawWebhook(req model.PostmanPayWebhookRequest) (*int64, error) {

	var createBody model.PostmanPayWebhookCreateBody
	createBody.Name = "POSTMANPAY_WITHDRAW_CALLBACK"
	createBody.JsonPayload = req.JsonPayload
	insertId, err := s.repo.CreatePostmanPayWebhook(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// WITHDRAW
	//	{
	//		"referenceId":"{{referrencId}}",
	//	    "transactionId":"{{transactionId}}",
	//	    "status":"completed",  // pending, processing, failed, rejected, completed
	//	    "message":"Some Message",
	//	    "qrcode":"0046000600000101030140225202312086ZwVuYndMYCSnMTv65102TH9104B07F", // This qrcode will send if payout is completed, Qrcode created by Bank.
	//	    "hash": "xxxxxxx"  // AES hashing transactionId by key that create by api_key + secret_key
	//	}

	var remoteResp model.PostmanPayWithdrawWebhookResponse
	errJson := json.Unmarshal([]byte(req.JsonPayload), &remoteResp)
	if errJson != nil {
		log.Println("Error unmarshal json", errJson)
		return nil, internalServerError(errJson)
	}

	// fmt.Println("PostmanPayDecryptRepayDespositPayload.remoteResp", helper.StructJson(remoteResp))

	// Check Response Status.
	successStatus := strings.ToUpper(strings.TrimSpace(remoteResp.Status))
	respStatusKey := "U"
	if successStatus == "COMPLETED" {
		successStatus = "PAID" // Always use PAID
		respStatusKey = "P"    // completed ได้ครั้งเดียว
	} else if successStatus == "PENDING" {
		respStatusKey = "W"
	} else if successStatus == "PROCESSING" {
		respStatusKey = "B"
	} else if successStatus == "FAILED" {
		respStatusKey = "F"
	} else if successStatus == "REJECTED" {
		respStatusKey = "R"
	} else if successStatus == "EXPIRED" {
		respStatusKey = "D"
	} else {
		log.Println("CreatePostmanPayWithdrawWebhook.remoteResp.desc.ELSE", successStatus)
		// เก็บตามจริง successStatus = "ERROR"
		respStatusKey = "E"
	}

	// Service Race Condition by Ref1(MchOrderNo)
	acAtUtc := time.Now().UTC()
	var rcCreateBody model.RaceActionCreateBody
	rcCreateBody.Name = "CreatePostmanPayWithdrawWebhook"
	rcCreateBody.JsonRequest = helper.StructJson(req)
	rcCreateBody.Status = "PENDING"
	// ActionKey = Ref2-MINUTE
	rcCreateBody.ActionKey = fmt.Sprintf("PG14W%s-%s%s", acAtUtc.Format("**********"), remoteResp.TransactionId, respStatusKey)
	rcCreateBody.UnlockAt = acAtUtc.Add(time.Second * 1)
	if _, err := s.repo.CreateRaceCondition(rcCreateBody); err != nil {
		return insertId, internalServerError(err)
	}

	// Get Posible Order
	var query model.PostmanPayOrderListRequest
	query.OrderNo = remoteResp.TransactionId
	query.TransactionNo = remoteResp.ReferenceId
	// query.Amount = fmt.Sprintf("%f", remoteResp.Amount)
	query.Status = "WAIT_PAYMENT"
	query.Limit = 1
	list, _, err := s.repo.GetDbPostmanPayOrderList(query)
	if err != nil {
		return nil, internalServerError(err)
	}

	// ** ไม่ได้เช็ค amount เพราะจะมีระบบที่ใช้ ทศนิยม และไม่เท่ากัน แต่ก็ควรเช็คแบบ +- 10%

	if len(list) > 0 {
		for _, item := range list {

			// Payment Success, Create Deposit if UserId is not null
			if item.UserId > 0 && item.OrderTypeId == model.POSTMANPAY_ORDER_TYPE_DEPOSIT {
				if successStatus == "PAID" {
					// Update Order
					if err := s.repo.ApproveDbPostmanPayOrder(item.Id, successStatus); err != nil {
						return nil, internalServerError(err)
					}
					if _, err := CreateCustomerDepositFromPostmanPayOrder(s.repo, item.Id, 0); err != nil {
						// WebhookLog
						var createBody2 model.PostmanPayWebhookCreateBody
						createBody2.Name = "POSTMANPAY_DEPOSIT_ERROR"
						createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
							"item":   item,
							"method": "createCustomerDepositFromPostmanPay",
							"error":  err.Error(),
						})
						if _, err = s.repo.CreatePostmanPayWebhook(createBody2); err != nil {
							log.Println("Error CreatePostmanPayWebhook.createCustomerDepositFromPostmanPay", err)
						}
					}
				}
			} else if item.UserId > 0 && item.OrderTypeId == model.POSTMANPAY_ORDER_TYPE_WITHDRAW {
				if successStatus == "PAID" {
					// Update Order
					if err := s.repo.ApproveDbPostmanPayOrder(item.Id, successStatus); err != nil {
						return nil, internalServerError(err)
					}
					if _, err := approveCustomerWithdrawFromPostmanPay(s.repo, *item.RefId); err != nil {
						// WebhookLog
						var createBody2 model.PostmanPayWebhookCreateBody
						createBody2.Name = "POSTMANPAY_WITHDRAW_ERROR"
						createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
							"item":   item,
							"method": "approveCustomerWithdrawFromPostmanPay",
							"error":  err.Error(),
						})
						if _, err = s.repo.CreatePostmanPayWebhook(createBody2); err != nil {
							log.Println("Error approveCustomerWithdrawFromPostmanPay.CreatePostmanPayWebhook", err)
						}
					}
				} else if successStatus == "FAILED" || successStatus == "REJECTED" || successStatus == "EXPIRED" {
					// Update Order
					if err := s.repo.ApproveDbPostmanPayOrder(item.Id, successStatus); err != nil {
						return nil, internalServerError(err)
					}
					if err := s.cancelWithdrawFromPostmanPayWebhookError(item); err != nil {
						log.Println("Error UpdateDbPostmanPayOrderError", err)
					}
				}
			} else {
				log.Println("ApproveDbPostmanPayOrder.WithoutItemUpdated", helper.StructJson(item))
			}
		}
	}
	return insertId, nil
}

func (s paygatePostmanPayService) cancelWithdrawFromPostmanPayWebhookError(pgOrder model.PostmanPayOrderResponse) error {

	adminId := int64(1)

	withdrawTrans, err := s.repo.GetBankTransactionById(*pgOrder.RefId)
	if err != nil {
		log.Println("rollbackWithdrawFromPostmanPay.GetBankTransactionById", err)
		return internalServerError(err)
	}
	if withdrawTrans.TransactionStatusId != model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		return internalServerError(errors.New("ไม่อยู๋ในสถานะที่สามารถยกเลิกได้"))
	}

	user, err := s.repo.GetUserBankDetailById(withdrawTrans.UserId)
	if err != nil {
		return internalServerError(err)
	}

	paygateOrder, err := s.repo.GetDbPostmanPayOrderByRefId(withdrawTrans.Id)
	if err != nil {
		log.Println("CancelWithdrawFromPostmanPay.GetDbPostmanPayOrderByRefId", err)
		return internalServerError(err)
	}
	waitStatus := "ERROR"
	if paygateOrder.TransactionStatus != waitStatus {
		return internalServerError(errors.New("ไม่อยู๋ในสถานะที่สามารถยกเลิกได้"))
	}

	returnAmount := withdrawTrans.CreditAmount

	// race condition deposit
	actionAt := time.Now().UTC()
	var createBody model.RaceActionCreateBody
	createBody.Name = "ADMIN_CANCEL_WITHDRAW"
	createBody.JsonRequest = helper.StructJson(map[string]interface{}{
		"transId":      withdrawTrans.Id,
		"returnAmount": returnAmount,
	})
	createBody.Status = "PENDING"
	createBody.ActionKey = fmt.Sprintf("WITHDRAW_CANCEL_T%sU%dID%d", actionAt.Format("**********"), user.Id, withdrawTrans.Id)
	createBody.UnlockAt = actionAt.Add(time.Second * 1)
	if _, err := s.repo.GetRaceActionIdByActionKey(createBody.ActionKey); err != nil {
		log.Println("CancelWithdrawFromPostmanPay.GetRaceActionIdByActionKey", err)
		if err != gorm.ErrRecordNotFound {
			return internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
		}
	} else {
		return internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
	}

	// create race condition
	actionId, err := s.repo.CreateRaceCondition(createBody)
	if err != nil {
		log.Println("CancelWithdrawFromPostmanPay.ERROR.CreateRaceCondition", err)
		return internalServerError(errors.New("CANNOT_CREATE_ACTION_LUCKY_WHEEL_ROUND"))
	}
	if actionId == 0 {
		return internalServerError(errors.New("CANNOT_CREATE_ACTION_LUCKY_WHEEL_ROUND"))
	}

	// SET ORDER AS ERROR
	remark := "Payment Cancel Withdraw"
	if err := s.repo.UpdateDbPostmanPayOrderError(paygateOrder.Id, remark); err != nil {
		log.Println("CreatePostmanPayWithdraw.UpdateDbPostmanPayOrderError", err)
	}

	if withdrawTrans.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		// [********] check user credit transaction ว่าตรงไหมกับเงื่อนไข (isShow = false)
		userTransaction, err := s.repo.GetUserWithdrawCreditTransactionByRefId(withdrawTrans.Id, returnAmount)
		if err != nil {
			if err.Error() == recordNotFound {
				log.Println("CancelWithdrawCredit.GetUserCreditTransactionById", err)
				return badRequest("ไม่ตรงกับเงือนไข")
			}
			return internalServerError(err)
		}
		if userTransaction != nil {
			var userCreditReq model.UserTransactionCreateRequest
			userCreditReq.UserId = user.Id
			userCreditReq.TransferAt = withdrawTrans.TransferAt
			userCreditReq.RefId = &withdrawTrans.Id
			userCreditReq.TypeId = model.CREDIT_TYPE_DEPOSIT
			userCreditReq.Amount = returnAmount
			IsShow := false
			userCreditReq.IsShow = &IsShow
			userCreditReq.ConfirmBy = &adminId
			if agentResp, err := s.repo.IncreaseUserCredit(userCreditReq); err != nil {
				log.Println("CancelWithdrawCredit.IncreaseUserCredit", err)
				return internalServerError(err)
			} else if !agentResp.AgentSuccess {
				// [********] error from agent will be in failed
				// if err := s.repoAccounting.UpdateDeporsitTransactionStatusFromAgent(*transId, *agentResp); err != nil {
				//     log.Println("CancelWithdrawCredit.UpdateDeporsitTransactionStatusFromAgent", err)
				// }
				// Set FastBank-READ ** ไม่มี เพราะเป็นการสร้างรายการฝากเอง
				agentFail := errors.New("ทำรายการเครดิตไม่สำเร็จ")
				log.Println("CancelWithdrawCredit.IncreaseUserCredit", agentFail)
				return internalServerError(agentFail)
			}
		}
	}

	// remark := "ADMIN_CANCEL_WITHDRAW"
	var updateApprovedBy model.UpdateUserTransactionStatusRequest
	updateApprovedBy.Id = withdrawTrans.Id
	updateApprovedBy.CanceledAt = actionAt
	updateApprovedBy.CanceledByAdminId = adminId
	updateApprovedBy.TransactionStatusId = model.TRANS_STATUS_WITHDRAW_CANCELED
	updateApprovedBy.CancelRemark = &remark
	err = s.repo.UpdateUserTransactionStatus(updateApprovedBy)
	if err != nil {
		return internalServerError(err)
	}

	var createConfirm model.CreateSuccessTransferWithdrawRequest
	createConfirm.TransactionId = withdrawTrans.Id
	createConfirm.ConfirmedAt = actionAt
	createConfirm.ConfirmedByAdminId = &adminId
	if _, err := CreateCanceledTransferWithdraw(repository.NewBankingRepository(s.sharedDb), createConfirm); err != nil {
		return err
	}

	return nil
}
