package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"errors"
	"fmt"
	"log"
	"math"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm"
)

type PromotionReturnTurnService interface {
	GetReturnTurnSetting() (*model.PromotionReturnTurnSettingResponse, error)
	UpdateReturnTurnSetting(req model.PromotionReturnTurnSettingUpdateRequest) error
	GetReturnTurnHistoryUserList(req model.PromotionReturnTurnHistoryUserListRequest) (*model.SuccessWithPagination, error)
	GetReturnTurnHistoryUserSummary(req model.PromotionReturnTurnHistoryUserListRequest) (*model.PromotionReturnTurnHistoryUserSummaryResponse, error)
	GetReturnTurnHistoryLogList(req model.PromotionReturnTurnHistoryListRequest) (*model.SuccessWithPagination, error)

	GetUserCurrentReturnDetail(userId int64) (*model.PromotionReturnTurnUserDetail, error)
	TakeUserReturnAmount(userId int64) error
	GetUserReturnTurnHistoryList(req model.PromotionReturnTurnTransactionListRequest) (*model.SuccessWithPagination, error)

	CronCutReturnTurnDaily() error
	CronCutReturnTurnByDate(ofDate string) error
}

type promotionReturnTurnService struct {
	repo                      repository.PromotionReturnTurnRepository
	shareDb                   *gorm.DB
	activityLuckyWheelService ActivityLuckyWheelService
	serviceNoti               NotificationService
}

func NewPromotionReturnTurnService(
	repo repository.PromotionReturnTurnRepository,
	shareDb *gorm.DB,
	activityLuckyWheelService ActivityLuckyWheelService,
	serviceNoti NotificationService,
) PromotionReturnTurnService {
	return &promotionReturnTurnService{repo, shareDb, activityLuckyWheelService, serviceNoti}
}

func (s *promotionReturnTurnService) GetReturnTurnSetting() (*model.PromotionReturnTurnSettingResponse, error) {

	record, err := s.repo.GetReturnTurnSetting()
	if err != nil {
		if err.Error() == recordNotFound {
			// create
			var createBody model.PromotionReturnTurnSettingCreateBody
			// createBody.ReturnPercent = 0
			createBody.ReturnTypeId = model.PROMOTION_RETURN_TYPE_LOSER
			createBody.CutTypeId = model.PROMOTION_RETURN_CUT_TYPE_DAILY
			createBody.MinTurnPrice = 0
			createBody.MaxReturnPrice = 0
			createBody.CreditExpireDays = 0
			createBody.CalcOnSport = true
			createBody.ReturnSportPercent = 0
			createBody.CalcOnCasino = true
			createBody.ReturnCasinoPercent = 0
			createBody.CalcOnGame = true
			createBody.ReturnGamePercent = 0
			createBody.CalcOnLottery = true
			createBody.ReturnLotteryPercent = 0
			createBody.CalcOnP2p = true
			createBody.ReturnP2pPercent = 0
			createBody.CalcOnFinancial = true
			createBody.ReturnFinancialPercent = 0
			createBody.Detail = ""
			if _, err := s.repo.CreateReturnTurnSetting(createBody); err != nil {
				return nil, internalServerError(err)
			}
			// ReGet
			record2, err := s.repo.GetReturnTurnSetting()
			if err != nil {
				return nil, internalServerError(err)
			}
			return record2, nil
		}
		return nil, internalServerError(err)
	}
	return record, nil
}

func (s *promotionReturnTurnService) UpdateReturnTurnSetting(req model.PromotionReturnTurnSettingUpdateRequest) error {

	record, err := s.GetReturnTurnSetting()
	if err != nil {
		return internalServerError(err)
	}

	// Check CutType
	if req.CutTypeId != nil {
		if _, err := s.repo.GetReturnTurnCutTypeById(*req.CutTypeId); err != nil {
			if err.Error() == recordNotFound {
				return notFound("CUT_TYPE_NOT_FOUND")
			}
			return internalServerError(err)
		}
	}

	var body model.PromotionReturnTurnSettingUpdateBody
	// body.ReturnPercent = req.ReturnPercent
	body.ReturnTypeId = req.ReturnTypeId
	body.CutTypeId = req.CutTypeId
	body.MinTurnPrice = req.MinTurnPrice
	body.MaxReturnPrice = req.MaxReturnPrice
	body.CreditExpireDays = req.CreditExpireDays
	body.CalcOnSport = req.CalcOnSport
	body.ReturnSportPercent = req.ReturnSportPercent
	body.CalcOnCasino = req.CalcOnCasino
	body.ReturnCasinoPercent = req.ReturnCasinoPercent
	body.CalcOnGame = req.CalcOnGame
	body.ReturnGamePercent = req.ReturnGamePercent
	body.CalcOnLottery = req.CalcOnLottery
	body.ReturnLotteryPercent = req.ReturnLotteryPercent
	body.CalcOnP2p = req.CalcOnP2p
	body.ReturnP2pPercent = req.ReturnP2pPercent
	body.CalcOnFinancial = req.CalcOnFinancial
	body.ReturnFinancialPercent = req.ReturnFinancialPercent
	if req.Detail != nil {
		body.Detail = *req.Detail
	}
	body.IsEnabled = req.IsEnabled
	if err := s.repo.UpdateReturnTurnSetting(record.Id, body); err != nil {
		return internalServerError(err)
	}
	return nil
}

func (s *promotionReturnTurnService) GetReturnTurnHistoryAllUserList(req model.PromotionReturnTurnHistoryUserListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	userList, total, err := s.repo.GetReturnTurnHistoryAllUserList(req)
	if err != nil {
		return nil, internalServerError(err)
	}

	var userIds = make(map[int64]int64, 0)
	for _, item := range userList {
		userIds[item.Id] = item.Id
	}

	if len(userIds) > 0 {
		req.UserIds = helper.MapIdsToInt64Array(userIds)
		totalData, err := s.repo.GetTotalUserReturnTurnList(req)
		if err != nil {
			return nil, internalServerError(err)
		}

		// Merge summary data
		for i, item := range userList {
			// check has key exists
			if _, ok := totalData[item.Id]; ok {
				userList[i].TotalTurnAmount = -totalData[item.Id].TotalTurnAmount
				userList[i].TotalTakenPrice = totalData[item.Id].TotalTakenPrice
			}
		}
	}
	return &model.SuccessWithPagination{
		List:  userList,
		Total: total,
	}, nil
}

func (s *promotionReturnTurnService) GetReturnTurnHistoryUserList(req model.PromotionReturnTurnHistoryUserListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	list, total, err := s.repo.GetReturnTurnHistoryUserList(req)
	if err != nil {
		return nil, internalServerError(err)
	}

	return &model.SuccessWithPagination{
		List:  list,
		Total: total,
	}, nil
}

func (s *promotionReturnTurnService) GetReturnTurnHistoryUserSummary(req model.PromotionReturnTurnHistoryUserListRequest) (*model.PromotionReturnTurnHistoryUserSummaryResponse, error) {

	result, err := s.repo.GetReturnTurnHistoryUserSummary(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	return result, nil
}

func (s *promotionReturnTurnService) GetReturnTurnHistoryLogList(req model.PromotionReturnTurnHistoryListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	logList, total, err := s.repo.GetReturnTurnHistoryLogList(req)
	if err != nil {
		return nil, internalServerError(err)
	}

	// convert OfdateT00:00:00 to string, And lost amount to negative
	for i, item := range logList {
		ofdate, err := time.Parse("2006-01-02", item.OfDate)
		if err == nil {
			logList[i].OfDate = ofdate.Format("2006-01-02")
		} else if len(item.OfDate) >= 10 {
			logList[i].OfDate = item.OfDate[0:10]
		}
		if item.TotalTurnAmount > 0 {
			logList[i].TotalTurnAmount = -item.TotalTurnAmount
		}
		if item.TotalTurnSport > 0 {
			logList[i].TotalTurnSport = -item.TotalTurnSport
		}
		if item.TotalTurnCasino > 0 {
			logList[i].TotalTurnCasino = -item.TotalTurnCasino
		}
		if item.TotalTurnGame > 0 {
			logList[i].TotalTurnGame = -item.TotalTurnGame
		}
		if item.TotalTurnLottery > 0 {
			logList[i].TotalTurnLottery = -item.TotalTurnLottery
		}
		if item.TotalTurnP2p > 0 {
			logList[i].TotalTurnP2p = -item.TotalTurnP2p
		}
		if item.TotalTurnFinancial > 0 {
			logList[i].TotalTurnFinancial = -item.TotalTurnFinancial
		}
	}
	return &model.SuccessWithPagination{
		List:  logList,
		Total: total,
	}, nil
}

func (s *promotionReturnTurnService) GetUserCurrentReturnDetail(userId int64) (*model.PromotionReturnTurnUserDetail, error) {

	var result model.PromotionReturnTurnUserDetail

	// CRON check before user GET
	if err := s.doCheckUserReturnTurnList(userId); err != nil {
		log.Println("GetUserCurrentReturnDetail.doCheckUserReturnTurnList.ERROR=", err)
	}

	actionTime := time.Now().UTC()
	promotion, err := s.repo.GetReturnTurnSetting()
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound("PROMOTION_NOT_FOUND")
		}
		return nil, internalServerError(err)
	}
	if !promotion.IsEnabled {
		return nil, notFound("PROMOTION_NOT_AVAILABLE")
	}
	// fmt.Println("GetReturnTurnSetting.promotion", helper.StructJson(promotion))

	returnPrice := 0.0
	statusId := model.PROMOTION_RETURN_STATUS_PENDING
	statusName := "PENDING"

	list, err := s.repo.GetCurrentReturnTurnTransactionList(userId)
	if err != nil {
		return nil, internalServerError(err)
	}

	// SUM TOTAL return if not expired or taken
	relatedItemList := make([]model.PromotionReturnTurnTransaction, 0)
	for _, item := range list {
		if item.StatusId == model.PROMOTION_RETURN_STATUS_READY {
			// extract date from D20231009U11
			itemOfDate, err := time.Parse("2006-01-02T15:04:05Z", item.OfDate)
			// To 2023-10-09 07:30:00 UTC (14.30 +7)
			// itemOfDate = itemOfDate.UTC().Add(time.Hour * 7).Add(time.Minute * 30)
			if err == nil {
				allowTakeAt := itemOfDate.Add(time.Hour * 7).Add(time.Minute * 30) // at 14.30
				if allowTakeAt.Before(actionTime) {
					// Not Expired
					var smallItem model.PromotionReturnTurnTransaction
					smallItem.Id = item.Id
					smallItem.ReturnPrice = item.ReturnPrice
					relatedItemList = append(relatedItemList, smallItem)
					returnPrice += item.ReturnPrice
					statusId = item.StatusId
					statusName = item.StatusName
				}
			} else {
				log.Println("GetUserCurrentReturnDetail.ParseItemOfDate.ERROR=", err, item.OfDate)
			}
		}
	}

	// append user data
	result.UserId = userId
	result.StatusId = statusId
	result.StatusName = statusName
	result.Detail = promotion.Detail
	// result.ReturnPercent = promotion.ReturnPercent
	result.ReturnPrice = returnPrice
	result.RelatedItemList = relatedItemList

	return &result, nil
}

func (s *promotionReturnTurnService) TakeUserReturnAmount(userId int64) error {

	actionAt := time.Now()

	trans, err := s.GetUserCurrentReturnDetail(userId)
	if err != nil {
		log.Println("TakeUserReturnAmount.GetUserCurrentReturnDetail.ERROR=", err)
		return internalServerError(err)
	}

	user, err := s.repo.GetUserMemberInfoById(userId)
	if err != nil {
		log.Println("TakeUserReturnAmount.GetUserMemberInfoById.ERROR=", err)
		return internalServerError(err)
	}

	if trans.ReturnPrice > 0 && len(trans.RelatedItemList) > 0 {

		config, err := s.GetUserIncomeMaxAmountConfig()
		if err != nil {
			return internalServerError(err)
		}

		returnAmount := trans.ReturnPrice
		// [UserIncome]
		var incomeLogCreateBody model.UserIncomeLogCreateBody
		incomeLogCreateBody.UserId = user.Id
		incomeLogCreateBody.TypeId = model.USER_INCOME_TYPE_PROMOTION_RETURN_TURN
		// incomeLogCreateBody.Detail = "แจกโบนัสฟรี คืนยอดคอมมิชชั่น"
		// พี่อิทบอกให้เปลี่ยนเป็น คืนยอด commission แทน
		incomeLogCreateBody.Detail = "แจกโบนัสฟรี คืนยอดcommission"
		incomeLogCreateBody.CreditAmount = returnAmount
		incomeLogCreateBody.StatusId = model.USER_INCOME_STATUS_PENDING
		incomeLogCreateBody.CreateBy = user.Id
		incomeLogCreateBody.CreateByName = user.Fullname
		incomeId, err := s.repo.CreateUserIncomeLog(incomeLogCreateBody)
		if err != nil {
			return err
		}

		// Then update statement ** SET AS TAKEN AS IT CREATE INCOME **
		// So Remain value will be ZERO
		for _, item := range trans.RelatedItemList {
			var updateBody model.PromotionReturnTurnTransactionUpdateBody
			updateBody.StatusId = model.PROMOTION_RETURN_STATUS_TAKEN
			updateBody.TakeAt = &actionAt
			updateBody.TakenPrice = item.ReturnPrice
			if err := s.repo.UpdateTakeReturnTurnTransaction(item.Id, updateBody); err != nil {
				return internalServerError(err)
			}
		}

		// OverAmount will stay PENDING in user income
		if returnAmount <= config.UserIncomeMaxAmount {
			return s.autoConfirmUserTakeReturnAmount(*incomeId, user.Id)
		} else {

			// AddTo BankPendingRecord -> แจกโบนัสฟรี คืนยอดcommission
			dashboardRepo := repository.NewDashboardRepository(s.shareDb)
			if err := AddBankPendingRecordFromReturnTurn(dashboardRepo, *incomeId, user.Id, actionAt, returnAmount, "อัตโนมัติ"); err != nil {
				log.Println("TakeUserReturnAmount.AddBankPendingRecordFromReturnTurn", err)
			}

			//  EXTERNAL NOTI
			var externalNoti model.NotifyExternalNotificationRequest
			externalNoti.TypeNotify = model.ActitvityBeforeBonus
			externalNoti.MemberCode = *user.MemberCode
			externalNoti.BonusCredit = &returnAmount
			externalNoti.UserCredit = user.Credit + returnAmount
			suggestAuto := int64(0)
			externalNoti.ConfirmedByAdminId = suggestAuto

			externalNoti.WebScoket.UserID = user.Id
			externalNoti.WebScoket.FullName = user.Fullname
			externalNoti.WebScoket.PhoneNumber = user.Phone
			externalNoti.WebScoket.AlertType = "PENDING_BONUS_ACTIVITY"
			if err := s.serviceNoti.ExternalNotification(externalNoti); err != nil {
				log.Println("TakeUserReturnAmount.ExternalNotification.ERROR=", err)
			}
		}
	} else {
		return notFound("NO_RETURN_DATA")
	}
	return nil
}

func (s *promotionReturnTurnService) GetUserIncomeMaxAmountConfig() (model.MarketingConfigResponse, error) {

	var record model.MarketingConfigResponse
	record.UserIncomeMaxAmount = 500

	// TRY to get config, Default on error
	config, err := s.repo.GetMarketingConfigByKey("AUTO_USER_INCOME_MAX_AMOUNT", "500")
	if err == nil {
		// record.UserIncomeMaxAmount = config.ConfigVal convert to float64
		tempFloat, err := strconv.ParseFloat(config.ConfigVal, 64)
		if err == nil {
			record.UserIncomeMaxAmount = tempFloat
		}
	}
	return record, nil
}

func (s *promotionReturnTurnService) autoConfirmUserTakeReturnAmount(incomeId int64, confirmBy int64) error {

	// 1. CONFIRMING is to set User Credit and Turnover
	// SAME LOGIG as

	income, err := s.repo.GetUserIncomeLogById(incomeId)
	if err != nil {
		return internalServerError(err)
	}
	if income.StatusId == model.USER_INCOME_STATUS_PENDING && income.ConfirmBy == nil {

		returnAmount := income.CreditAmount
		if returnAmount > 0 {
			user, err := s.repo.GetUserMemberInfoById(income.UserId)
			if err != nil {
				return internalServerError(err)
			}
			// 1.[USER_CREDIT] ** Do First **
			var userCreditReq model.UserTransactionCreateRequest
			userCreditReq.RefId = &income.Id
			userCreditReq.UserId = user.Id
			// userCreditReq.Detail = "แจกโบนัสฟรี คืนยอดคอมมิชชั่น"
			// พี่อิทบอกให้เปลี่ยนเป็น คืนยอด commission แทน
			userCreditReq.Detail = "แจกโบนัสฟรี คืนยอดcommission"
			userCreditReq.TypeId = model.CREDIT_TYPE_PROMOTION_RETURN_TURN
			userCreditReq.BonusAmount = returnAmount
			creditTransferResp, err := s.repo.IncreaseUserCredit(userCreditReq)
			if err != nil {
				// later : handle error ?
				return internalServerError(err)
			}
			// [UserIncome]
			var confirmBody model.UserIncomeLogConfirmBody
			confirmBody.Id = income.Id
			confirmBody.CreditAfter = creditTransferResp.AgentAfterAmount
			confirmBody.TransferAt = creditTransferResp.TransferAt
			confirmBody.ConfirmBy = confirmBy
			confirmBody.ConfirmByName = *user.Username
			if err := s.repo.ConfirmUserIncomeLog(confirmBody); err != nil {
				return internalServerError(err)
			}
			// [turnOver] ตอนรับเงินคืน
			if err := MakeUserTidTurnPromotionReturnTurn(repository.NewTurnoverRepository(s.shareDb), user.Id, returnAmount); err != nil {
				// No error return
				return internalServerError(err)
			}
			// [LINE]
			var externalNoti model.NotifyExternalNotificationRequest
			externalNoti.TypeNotify = model.ActitvityAfterBonus
			externalNoti.MemberCode = *user.MemberCode
			externalNoti.BonusCredit = &returnAmount
			externalNoti.UserCredit = user.Credit + returnAmount
			suggestAuto := int64(0)
			externalNoti.ConfirmedByAdminId = suggestAuto
			if err := s.serviceNoti.ExternalNotification(externalNoti); err != nil {
				log.Println("FailedNotify", err)
			}
		} else {
			return notFound("NO_RETURN_DATA")
		}
	} else {
		return badRequest("INVALID_TRANSACTION_STATUS")
	}
	return nil
}

func (s *promotionReturnTurnService) GetUserReturnTurnHistoryList(req model.PromotionReturnTurnTransactionListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	// CRON check before user GET
	if err := s.doCheckUserReturnTurnList(req.UserId); err != nil {
		log.Println("GetUserReturnTurnHistoryList.ERROR.doCheckUserReturnTurnList", err)
	}

	list, total, err := s.repo.GetReturnTurnTransactionList(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	return &model.SuccessWithPagination{
		List:  list,
		Total: total,
	}, nil
}

func (s *promotionReturnTurnService) CronCutReturnTurnDaily() error {

	actionTime := time.Now()
	// test
	// SUN "2024-08-04T11:30:00Z"
	// actionTime = time.Date(2024, 8, 4, 11, 30, 0, 0, time.UTC)
	// NEXT_SAT "2024-08-10T11:30:00Z"
	// actionTime = time.Date(2024, 8, 10, 11, 30, 0, 0, time.UTC)
	// NEXT-SUN = "2024-08-11T11:30:00Z"
	// actionTime = time.Date(2024, 8, 11, 11, 30, 0, 0, time.UTC)

	// Day by day at 11.00 to 23.59 Daily (+7)
	bbkLoc := time.FixedZone("Asia/Bangkok", 7*60*60)
	runTimeInBkk := actionTime.In(bbkLoc)
	if runTimeInBkk.Hour() < 11 {
		return errors.New("NOT_IN_TIME:" + runTimeInBkk.String())
	}

	// Check Daily playlog to START CUT and CALC
	// Use OLD_PLAYLONG = api_status as play_logs
	statementDate := actionTime.AddDate(0, 0, -1)
	checkData, err := s.repo.CheckDailyPlayLog(statementDate.Format("2006-01-02"))
	if err != nil {
		return internalServerError(err)
	}
	if !checkData.IsReady {
		return badRequest("PLAY_LOG_IS_NOT_READY")
	}

	// =========== Playlog is Ready ! ===========

	// useAction
	var createBody model.CronActionCreateBody
	createBody.Status = "PENDING"
	// KEY = SYNC_PROCUTRETURNTURN_20060102_11:00:00 = Once a day
	createBody.ActionKey = fmt.Sprintf("SYNC_PROCUTRETURNTURN_%s%s", actionTime.Format("20060102"), "11:00:00")
	createBody.UnlockAt = actionTime.Add(time.Minute * 1)
	if _, err := s.repo.GetCronActionByActionKey(createBody.ActionKey); err != nil {
		if err != gorm.ErrRecordNotFound {
			return nil
		}
	} else {
		// already create then CRON_STATUS
		if err := s.repo.UpdateAgcCronCalcStatus("PROMOTION_RETURN_LOSS", statementDate.Format("2006-01-02"), "DONE"); err != nil {
			log.Println(err)
		}
		// already create then calc
		return s.CalcReturnTurnPendingTransaction(actionTime)
	}
	actionId, err := s.repo.CreateCronAction(createBody)
	if err != nil {
		log.Println("CronCutReturnTurnDaily.ERROR.CreateCronAction", err)
		return internalServerError(errors.New("WORK_IN_ACTION"))
	}

	debugResult := make(map[string]interface{}, 0)

	// Check Promotion
	promotionSetting, err := s.repo.GetReturnTurnSetting()
	if err != nil {
		debugResult["skip"] = "PROMOTION_NOT_FOUND"
		if err := s.repo.SetSuccessCronAction(actionId, helper.StructJson(debugResult)); err != nil {
			log.Println("CronCutReturnTurnDaily.ERROR.SetSuccessCronAction", err)
		}
		if err.Error() == recordNotFound {
			return notFound("PROMOTION_NOT_FOUND")
		}
		return internalServerError(err)
	}
	// Remove Detail ** DO NOT EDIT DETAIL **
	debugResult["promotion"] = map[string]interface{}{
		// "ReturnPercent":    promotionSetting.ReturnPercent,
		"ReturnTypeId":           promotionSetting.ReturnTypeId,
		"CutTypeId":              promotionSetting.CutTypeId,
		"MinTurnPrice":           promotionSetting.MinTurnPrice,
		"MaxReturnPrice":         promotionSetting.MaxReturnPrice,
		"CreditExpireDays":       promotionSetting.CreditExpireDays,
		"CalcOnSport":            promotionSetting.CalcOnSport,
		"ReturnSportPercent":     promotionSetting.ReturnSportPercent,
		"CalcOnCasino":           promotionSetting.CalcOnCasino,
		"ReturnCasinoPercent":    promotionSetting.ReturnCasinoPercent,
		"CalcOnGame":             promotionSetting.CalcOnGame,
		"ReturnGamePercent":      promotionSetting.ReturnGamePercent,
		"CalcOnLottery":          promotionSetting.CalcOnLottery,
		"ReturnLotteryPercent":   promotionSetting.ReturnLotteryPercent,
		"CalcOnP2p":              promotionSetting.CalcOnP2p,
		"ReturnP2pPercent":       promotionSetting.ReturnP2pPercent,
		"CalcOnFinancial":        promotionSetting.CalcOnFinancial,
		"ReturnFinancialPercent": promotionSetting.ReturnFinancialPercent,
		"IsEnabled":              promotionSetting.IsEnabled,
		"CreatedAt":              promotionSetting.CreatedAt,
		"UpdatedAt":              promotionSetting.UpdatedAt,
		"CacheExpiredAt":         promotionSetting.CacheExpiredAt,
	}
	if !promotionSetting.IsEnabled {
		debugResult["skip"] = "PROMOTION_NOT_ENABLED"
		if err := s.repo.SetSuccessCronAction(actionId, helper.StructJson(debugResult)); err != nil {
			log.Println("CronCutReturnTurnDaily.ERROR.SetSuccessCronAction", err)
		}
		return notFound("PROMOTION_NOT_AVAILABLE")
	}

	// =========== DO_WORK ===========

	if promotionSetting.CutTypeId == model.PROMOTION_RETURN_CUT_TYPE_DAILY {
		// Just Only Create
		if createResult, err := doCreateReturnTurnDailyTransaction(s.repo, actionTime, *promotionSetting); err != nil {
			debugResult["doCreateReturnTurnDailyTransaction.error"] = err.Error()
			log.Println("CronCutReturnTurnDaily.ERROR.doCreateReturnTurnDailyTransaction", err)
			if err := s.repo.SetFailCronAction(actionId, helper.StructJson(debugResult)); err != nil {
				log.Println("CronCutReturnTurnDaily.ERROR.SetFailCronAction", err)
			}
			return internalServerError(err)
		} else {
			debugResult["doCreateReturnTurnDailyTransaction.createResult"] = createResult
		}
	} else if promotionSetting.CutTypeId == model.PROMOTION_RETURN_CUT_TYPE_WEEKLY {
		// Check Weekly - Non of days in this week should not have any daily type.
		actionAtUtc := actionTime.UTC()
		actionAtBkk := actionAtUtc.In(time.FixedZone("Asia/Bangkok", 7*60*60))
		if actionAtBkk.Weekday() == time.Sunday {
			// Its Sunday
			if createResult, err := s.doCreateReturnTurnWeeklyTransaction(actionTime, *promotionSetting); err != nil {
				debugResult["doCreateReturnTurnWeeklyTransaction.error"] = err.Error()
				log.Println("CronCutReturnTurnDaily.ERROR.doCreateReturnTurnWeeklyTransaction", err)
				if err := s.repo.SetFailCronAction(actionId, helper.StructJson(debugResult)); err != nil {
					log.Println("CronCutReturnTurnDaily.ERROR.SetFailCronAction", err)
				}
				return internalServerError(err)
			} else {
				debugResult["doCreateReturnTurnWeeklyTransaction.createResult"] = createResult
			}
		} else {
			debugResult["skip"] = "NOT_SUN_DAY"
		}
	}

	// SUCCESS
	if err := s.repo.SetSuccessCronAction(actionId, helper.StructJson(debugResult)); err != nil {
		log.Println("CronCutReturnTurnDaily.ERROR.SetSuccessCronAction", err)
	}
	return nil
}

func doCreateReturnTurnDailyTransaction(repo repository.PromotionReturnTurnRepository, actionTime time.Time, setting model.PromotionReturnTurnSettingResponse) (*model.CutReturnTurnDailyReponse, error) {

	var result model.CutReturnTurnDailyReponse

	if setting.CutTypeId != model.PROMOTION_RETURN_CUT_TYPE_DAILY {
		log.Println("doCreateReturnTurnDailyTransaction.ERROR.INVALID_CUT_TYPE")
		return nil, errors.New("INVALID_CUT_TYPE")
	}

	// play_log of Yesterday
	statementDate := actionTime.AddDate(0, 0, -1).Format("2006-01-02")
	userData, err := repo.GetDailyTotalUserPaylogList(statementDate)
	if err != nil {
		return nil, internalServerError(err)
	}
	result.StatementDate = statementDate
	result.TotalUserDataCount = int64(len(userData))

	// แสดงตามที่ตั้งไว้ เช่น กีฬา, คาสิโน, สล็อต ถ้าเลือกทั้งหมดแสดงทั้งหมด
	gameDetail := "none" // ไม่มีเกมที่เลือก
	calcOnList := make([]string, 0)
	if setting.CalcOnSport {
		calcOnList = append(calcOnList, "sport") // กีฬา
	}
	if setting.CalcOnCasino {
		calcOnList = append(calcOnList, "casino") // คาสิโน
	}
	if setting.CalcOnGame {
		calcOnList = append(calcOnList, "game") // สล็อต
	}
	if setting.CalcOnLottery {
		calcOnList = append(calcOnList, "lottery") // สลาก
	}
	if setting.CalcOnP2p {
		calcOnList = append(calcOnList, "p2p") // พนันกับผู้เล่น
	}
	if setting.CalcOnFinancial {
		calcOnList = append(calcOnList, "financial") // การเงิน
	}

	if len(calcOnList) > 0 {
		gameDetail = strings.Join(calcOnList, ",")
	}

	// Bulk Insert
	var createList = make(map[string]model.PromotionReturnTurnTransactionCreateBody, 0)

	// Start First Empty item
	firstItem := model.PromotionReturnTurnTransactionCreateBody{}
	firstItem.UserId = 0
	firstItem.DailyKey = "D" + actionTime.Format("20060102") + "U0"
	firstItem.TotalTurnAmount = 0
	firstItem.TotalTurnSport = 0
	firstItem.ReturnSportPercent = setting.ReturnSportPercent
	firstItem.TotalTurnCasino = 0
	firstItem.ReturnCasinoPercent = setting.ReturnCasinoPercent
	firstItem.TotalTurnGame = 0
	firstItem.ReturnGamePercent = setting.ReturnGamePercent
	firstItem.TotalTurnLottery = 0
	firstItem.ReturnLotteryPercent = setting.ReturnLotteryPercent
	firstItem.TotalTurnP2p = 0
	firstItem.ReturnP2pPercent = setting.ReturnP2pPercent
	firstItem.TotalTurnFinancial = 0
	firstItem.ReturnFinancialPercent = setting.ReturnFinancialPercent
	firstItem.OfDate = actionTime.Format("2006-01-02")
	// firstItem.ReturnPercent = setting.ReturnPercent
	firstItem.ReturnTypeId = setting.ReturnTypeId
	firstItem.CutTypeId = setting.CutTypeId // ** วิธีการตัดคิดตอนตัด หรือตอนสร้าง ??? **
	firstItem.MinTurnPrice = setting.MinTurnPrice
	firstItem.MaxReturnPrice = setting.MaxReturnPrice
	firstItem.CreditExpireDays = setting.CreditExpireDays
	firstItem.GameDetail = gameDetail
	createList[firstItem.DailyKey] = firstItem

	// Loop and Create by 1 User 1 record
	for _, user := range userData {
		// ติดลบ แปลว่าเสีย -50 = ยอดเสีย 50
		totalTurnAmount := user.TotalTurnAmount
		if totalTurnAmount > 0 {
			uniqueDailyKey := fmt.Sprintf("D%sU%d", actionTime.Format("20060102"), user.UserId)
			var createBody model.PromotionReturnTurnTransactionCreateBody
			createBody.UserId = user.UserId
			createBody.DailyKey = uniqueDailyKey

			// แสดงยอดเทิรนรวม เท่าที่เลือกเท่านั้น
			// [241202] 3. คืนยอดเทิน เปลี่ยนเป็น คืนยอด ValidAmount
			createBody.TotalTurnAmount = 0
			if setting.CalcOnSport {
				// createBody.TotalTurnAmount += (user.TotalTurnSport)
				createBody.TotalTurnAmount += (user.TotalValidAmountSport)
			}
			if setting.CalcOnCasino {
				// createBody.TotalTurnAmount += (user.TotalTurnCasino)
				createBody.TotalTurnAmount += (user.TotalValidAmountCasino)
			}
			if setting.CalcOnGame {
				// createBody.TotalTurnAmount += (user.TotalTurnGame)
				createBody.TotalTurnAmount += (user.TotalValidAmountGame)
			}
			if setting.CalcOnLottery {
				// createBody.TotalTurnAmount += (user.TotalTurnLottery)
				createBody.TotalTurnAmount += (user.TotalValidAmountLottery)
			}
			if setting.CalcOnP2p {
				// createBody.TotalTurnAmount += (user.TotalTurnP2p)
				createBody.TotalTurnAmount += (user.TotalValidAmountP2p)
			}
			if setting.CalcOnFinancial {
				// createBody.TotalTurnAmount += (user.TotalTurnFinancial)
				createBody.TotalTurnAmount += (user.TotalValidAmountFinancial)
			}
			// ยอดเสียแต่ละเกมแสดงปกติ (เก็บเป็น บวก แสดงเป็น ติดลบ)
			// createBody.TotalTurnSport = (user.TotalTurnSport)
			createBody.TotalTurnSport = (user.TotalValidAmountSport)
			createBody.ReturnSportPercent = setting.ReturnSportPercent
			// createBody.TotalTurnCasino = (user.TotalTurnCasino)
			createBody.TotalTurnCasino = (user.TotalValidAmountCasino)
			createBody.ReturnCasinoPercent = setting.ReturnCasinoPercent
			// createBody.TotalTurnGame = (user.TotalTurnGame)
			createBody.TotalTurnGame = (user.TotalValidAmountGame)
			createBody.ReturnGamePercent = setting.ReturnGamePercent
			// createBody.TotalTurnLottery = (user.TotalTurnLottery)
			createBody.TotalTurnLottery = (user.TotalValidAmountLottery)
			createBody.ReturnLotteryPercent = setting.ReturnLotteryPercent
			// createBody.TotalTurnP2p = (user.TotalTurnP2p)
			createBody.TotalTurnP2p = (user.TotalValidAmountP2p)
			createBody.ReturnP2pPercent = setting.ReturnP2pPercent
			// createBody.TotalTurnFinancial = (user.TotalTurnFinancial)
			createBody.TotalTurnFinancial = (user.TotalValidAmountFinancial)
			createBody.ReturnFinancialPercent = setting.ReturnFinancialPercent
			createBody.OfDate = actionTime.Format("2006-01-02")
			// createBody.ReturnPercent = setting.ReturnPercent
			createBody.ReturnTypeId = setting.ReturnTypeId
			createBody.CutTypeId = setting.CutTypeId // ** วิธีการตัดคิดตอนตัด หรือตอนสร้าง ???
			createBody.MinTurnPrice = setting.MinTurnPrice
			createBody.MaxReturnPrice = setting.MaxReturnPrice
			createBody.CreditExpireDays = setting.CreditExpireDays
			createBody.GameDetail = gameDetail
			createList[uniqueDailyKey] = createBody
			result.TotalUserTurnCount++
			if len(createList) >= 300 {
				// check exists
				dbList, _, err := repo.GetReturnTurnTransactionListByDailyKeyList(createList)
				if err != nil {
					// cant check
					return nil, internalServerError(err)
				}
				// if exists, remove from createList
				for _, dbItem := range dbList {
					delete(createList, dbItem.DailyKey)
				}
				if len(createList) > 0 {
					// Add Promotion Income each User
					if err := repo.CreateReturnTurnTransactionBulk(createList); err != nil {
						return nil, internalServerError(err)
					}
					// [Lucky Wheel] ตอนคืนยอดเสีย = Wheel Credit add each User
					if err := ReturnTurnAddUserWheelCreditBulk(repo, createList); err != nil {
						return nil, internalServerError(err)
					}
					createList = make(map[string]model.PromotionReturnTurnTransactionCreateBody, 0)
				}
			}
		}
	}
	// LEFTOVER
	if len(createList) > 0 {
		// check exists
		dbList, _, err := repo.GetReturnTurnTransactionListByDailyKeyList(createList)
		if err != nil {
			// cant check
			return nil, internalServerError(err)
		}
		// if exists, remove from createList
		for _, dbItem := range dbList {
			delete(createList, dbItem.DailyKey)
		}
		if len(createList) > 0 {
			// Add Promotion Income each User
			if err := repo.CreateReturnTurnTransactionBulk(createList); err != nil {
				return nil, internalServerError(err)
			}
			// [Lucky Wheel] ตอนคืนยอดเสีย = Wheel Credit add each User
			if err := ReturnTurnAddUserWheelCreditBulk(repo, createList); err != nil {
				return nil, internalServerError(err)
			}
		}
	}
	return &result, nil
}

func (s *promotionReturnTurnService) doCreateReturnTurnWeeklyTransaction(actionTime time.Time, setting model.PromotionReturnTurnSettingResponse) (*model.CutReturnTurnDailyReponse, error) {

	var result model.CutReturnTurnDailyReponse

	// [20250508] สรุป สูตรคือ เหมือนเดิม แยกออกมาแค่ตอน คูณเปอร์เซ็น
	// คืนยอดเสีย
	// รวมยอดเสียทุกเกมที่เลือก สุทธิ ถ้ารวมแล้วเสียมากกว่าได้ และมากกว่าขั้นต่ำ ก็จะคำนวนให้ ตามเปอรเซ็นเดียวเหมือนเดิม แล้วก็ ได้สูงสุดตามยอดสูงสุดที่กำหนดไว้
	// คืนค่าคอม
	// รวมยอดถูกต้อง ของเกมที่เลือกทั้งหมด สุทธิแล้วเช็คขั้นต่ำ จากนั้นเอาแต่ละเกมไปคูณ % ของตัวเอง แล้วรวมรายได้สุทธิมาเช็คกับ max จะได้สูงสุดติด max
	// min max ขั้นต่ำ และสูงสุด ไม่ว่าจะรายวัน หรือรายสัปดาห์ ก็จะ ตัดยอดรายวันเสมอ

	// ยอดเสียรายสัปดาห์ คือ ยอดได้+ยอดเสีย จาก วันจันทร์ 12.30 ถึง วันตัดยอก วันอาทิตย์ 12.30
	if setting.CutTypeId != model.PROMOTION_RETURN_CUT_TYPE_WEEKLY {
		log.Println("doCreateReturnTurnWeeklyTransaction.ERROR.INVALID_CUT_TYPE")
		return nil, errors.New("INVALID_CUT_TYPE")
	}

	// Mink.J — [20240805] at 1:51 PM
	// สรุปการตัดยอดรายสัปดาห์ค่ะ
	// ภายใน 1 สัปดาห์ หากมีอย่างน้อย 1 วัน ที่ ตั้งค่าเป็นตัดยอดคืนยอดเสียรายวัน จะไม่ได้รับคืนยอดเสียรายสัปดาห์ค่ะ (รายวันยังรับอยู่ค่ะ) หากภายในหนึ่งสัปดาห์สลับไปมาหลายครั้ง ยูสจะได้รับแค่วันที่ตั้งค่าคืนยอดเสียรายวัน
	// หากจะใช้ค่ายอดเสียรายสัปดาห์ จะต้องตั้งค่าตั้งแต่วันต้นสัปดาห์หลังตัดยอดค่ะ
	// TULA — [20240805] at 1:56 PM
	// จ. รายวัน
	// อ. รายสัป
	// พ. วัน
	// พฤ. สัป
	// ศ. วัน
	// ส.อ. สัปดาห์
	// จะได้ยอดวันไหนบ้าง
	// Mink.J — [20240805] at 1:57 PM
	// จะได้ จ. , พ., ศ
	// เท่านั้นค่ะ
	// TULA — [20240805] at 1:57 PM
	// แล้วก็ วัน อ. ก็จะเช็ค รายสัปดาห์ ถูกมะ
	// จะเจอว่า จ. มีรายวันอยู่ ก็จะไม่คำนวน
	// เข้าใจละ

	// Check Has Daily in this week
	todayDate := actionTime.Format("2006-01-02")
	if total, err := s.repo.CheckReturnTurnSystemHasDailyCuttypeInWeek(todayDate); err != nil {
		return nil, notFound("MAY_CREATED_THIS_WEEK")
	} else if total != 0 {
		return nil, notFound("ALREADY_CREATED_THIS_WEEK")
	}
	// =================== NO DAILY IN THIS WEEK ===================

	// play_log of Yesterday
	statementDate := actionTime.AddDate(0, 0, -1).Format("2006-01-02")
	userData, err := s.repo.GetWeeklyTotalUserPaylogList(statementDate)
	if err != nil {
		return nil, internalServerError(err)
	}
	result.StatementDate = statementDate
	result.TotalUserDataCount = int64(len(userData))

	// แสดงตามที่ตั้งไว้ เช่น กีฬา, คาสิโน, สล็อต ถ้าเลือกทั้งหมดแสดงทั้งหมด
	gameDetail := "none" // ไม่มีเกมที่เลือก
	calcOnList := make([]string, 0)
	if setting.CalcOnSport {
		calcOnList = append(calcOnList, "sport") // กีฬา
	}
	if setting.CalcOnCasino {
		calcOnList = append(calcOnList, "casino") // คาสิโน
	}
	if setting.CalcOnGame {
		calcOnList = append(calcOnList, "game") // สล็อต
	}
	if setting.CalcOnLottery {
		calcOnList = append(calcOnList, "lottery") // สลาก
	}
	if setting.CalcOnP2p {
		calcOnList = append(calcOnList, "p2p") // พนันกับผู้เล่น
	}
	if setting.CalcOnFinancial {
		calcOnList = append(calcOnList, "financial") // การเงิน
	}
	if len(calcOnList) > 0 {
		gameDetail = strings.Join(calcOnList, ",")
	}

	// Bulk Insert
	var createList = make(map[string]model.PromotionReturnTurnTransactionCreateBody, 0)

	// Start First Empty item
	firstItem := model.PromotionReturnTurnTransactionCreateBody{}
	firstItem.UserId = 0
	firstItem.DailyKey = "D" + actionTime.Format("20060102") + "U0"
	firstItem.TotalTurnAmount = 0
	firstItem.TotalTurnSport = 0
	firstItem.ReturnSportPercent = setting.ReturnSportPercent
	firstItem.TotalTurnCasino = 0
	firstItem.ReturnCasinoPercent = setting.ReturnCasinoPercent
	firstItem.TotalTurnGame = 0
	firstItem.ReturnGamePercent = setting.ReturnGamePercent
	firstItem.TotalTurnLottery = 0
	firstItem.ReturnLotteryPercent = setting.ReturnLotteryPercent
	firstItem.TotalTurnP2p = 0
	firstItem.ReturnP2pPercent = setting.ReturnP2pPercent
	firstItem.TotalTurnFinancial = 0
	firstItem.ReturnFinancialPercent = setting.ReturnFinancialPercent
	firstItem.OfDate = actionTime.Format("2006-01-02")
	// firstItem.ReturnPercent = setting.ReturnPercent
	firstItem.ReturnTypeId = setting.ReturnTypeId
	firstItem.CutTypeId = setting.CutTypeId // ** วิธีการตัดคิดตอนตัด หรือตอนสร้าง ??? **
	firstItem.MinTurnPrice = setting.MinTurnPrice
	firstItem.MaxReturnPrice = setting.MaxReturnPrice
	firstItem.CreditExpireDays = setting.CreditExpireDays
	firstItem.GameDetail = gameDetail
	createList[firstItem.DailyKey] = firstItem

	// Loop and Create by 1 User 1 record
	for _, user := range userData {
		// ติดลบ แปลว่าเสีย -50 = ยอดเสีย 50
		totalTurnAmount := user.TotalTurnAmount
		if totalTurnAmount > 0 {
			uniqueDailyKey := fmt.Sprintf("D%sU%d", actionTime.Format("20060102"), user.UserId)
			var createBody model.PromotionReturnTurnTransactionCreateBody
			createBody.UserId = user.UserId
			createBody.DailyKey = uniqueDailyKey

			// แสดงยอดเทิรนรวม เท่าที่เลือกเท่านั้น
			// [241202] 3. คืนยอดเทิน เปลี่ยนเป็น คืนยอด ValidAmount
			createBody.TotalTurnAmount = 0
			if setting.CalcOnSport {
				// createBody.TotalTurnAmount += (user.TotalTurnSport)
				createBody.TotalTurnAmount += (user.TotalValidAmountSport)
			}
			if setting.CalcOnCasino {
				// createBody.TotalTurnAmount += (user.TotalTurnCasino)
				createBody.TotalTurnAmount += (user.TotalValidAmountCasino)
			}
			if setting.CalcOnGame {
				// createBody.TotalTurnAmount += (user.TotalTurnGame)
				createBody.TotalTurnAmount += (user.TotalValidAmountGame)
			}
			if setting.CalcOnLottery {
				// createBody.TotalTurnAmount += (user.TotalTurnLottery)
				createBody.TotalTurnAmount += (user.TotalValidAmountLottery)
			}
			if setting.CalcOnP2p {
				// createBody.TotalTurnAmount += (user.TotalTurnP2p)
				createBody.TotalTurnAmount += (user.TotalValidAmountP2p)
			}
			if setting.CalcOnFinancial {
				// createBody.TotalTurnAmount += (user.TotalTurnFinancial)
				createBody.TotalTurnAmount += (user.TotalValidAmountFinancial)
			}
			// ยอดเสียแต่ละเกมแสดงปกติ (เก็บเป็น บวก แสดงเป็น ติดลบ)
			// createBody.TotalTurnSport = (user.TotalTurnSport)
			createBody.TotalTurnSport = (user.TotalValidAmountSport)
			createBody.ReturnSportPercent = setting.ReturnSportPercent
			// createBody.TotalTurnCasino = (user.TotalTurnCasino)
			createBody.TotalTurnCasino = (user.TotalValidAmountCasino)
			createBody.ReturnCasinoPercent = setting.ReturnCasinoPercent
			// createBody.TotalTurnGame = (user.TotalTurnGame)
			createBody.TotalTurnGame = (user.TotalValidAmountGame)
			createBody.ReturnGamePercent = setting.ReturnGamePercent
			// createBody.TotalTurnLottery = (user.TotalTurnLottery)
			createBody.TotalTurnLottery = (user.TotalValidAmountLottery)
			createBody.ReturnLotteryPercent = setting.ReturnLotteryPercent
			// createBody.TotalTurnP2p = (user.TotalTurnP2p)
			createBody.TotalTurnP2p = (user.TotalValidAmountP2p)
			createBody.ReturnP2pPercent = setting.ReturnP2pPercent
			// createBody.TotalTurnFinancial = (user.TotalTurnFinancial)
			createBody.TotalTurnFinancial = (user.TotalValidAmountFinancial)
			createBody.ReturnFinancialPercent = setting.ReturnFinancialPercent
			createBody.OfDate = actionTime.Format("2006-01-02")
			// createBody.ReturnPercent = setting.ReturnPercent
			createBody.ReturnTypeId = setting.ReturnTypeId
			createBody.CutTypeId = setting.CutTypeId // ** วิธีการตัดคิดตอนตัด หรือตอนสร้าง ???
			createBody.MinTurnPrice = setting.MinTurnPrice
			createBody.MaxReturnPrice = setting.MaxReturnPrice
			createBody.CreditExpireDays = setting.CreditExpireDays
			createBody.GameDetail = gameDetail
			createList[uniqueDailyKey] = createBody
			result.TotalUserTurnCount++
			if len(createList) >= 300 {
				// check exists
				dbList, _, err := s.repo.GetReturnTurnTransactionListByDailyKeyList(createList)
				if err != nil {
					// cant check
					return nil, internalServerError(err)
				}
				// if exists, remove from createList
				for _, dbItem := range dbList {
					delete(createList, dbItem.DailyKey)
				}
				if len(createList) > 0 {
					// Add Promotion Income each User
					if err := s.repo.CreateReturnTurnTransactionBulk(createList); err != nil {
						return nil, internalServerError(err)
					}
					// [Lucky Wheel] ตอนคืนยอดเสีย = Wheel Credit add each User
					if err := ReturnTurnAddUserWheelCreditBulk(s.repo, createList); err != nil {
						return nil, internalServerError(err)
					}
					createList = make(map[string]model.PromotionReturnTurnTransactionCreateBody, 0)
				}
			}
		}
	}
	// LEFTOVER
	if len(createList) > 0 {
		// check exists
		dbList, _, err := s.repo.GetReturnTurnTransactionListByDailyKeyList(createList)
		if err != nil {
			// cant check
			return nil, internalServerError(err)
		}
		// if exists, remove from createList
		for _, dbItem := range dbList {
			delete(createList, dbItem.DailyKey)
		}
		if len(createList) > 0 {
			// Add Promotion Income each User
			if err := s.repo.CreateReturnTurnTransactionBulk(createList); err != nil {
				return nil, internalServerError(err)
			}
			// [Lucky Wheel] ตอนคืนยอดเสีย = Wheel Credit add each User
			if err := ReturnTurnAddUserWheelCreditBulk(s.repo, createList); err != nil {
				return nil, internalServerError(err)
			}
		}
	}
	return &result, nil
}

func ReturnTurnAddUserWheelCreditBulk(repo repository.PromotionReturnTurnRepository, bulkBody map[string]model.PromotionReturnTurnTransactionCreateBody) error {

	for _, item := range bulkBody {
		// เอาที่เพิ่ม เครดิทเก่าออก
		var luckyWheelBody model.ActivityLuckyWheelRoundUserRequest
		luckyWheelBody.UserId = item.UserId
		luckyWheelBody.ConditionId = model.ACTIVITY_LUCKY_WHEEL_SETTING_CONDITION_LOSE
		luckyWheelBody.ConditionAmount = item.TotalTurnAmount
		if err := CreateRoundActivityLuckyWheel(repository.NewActivityLuckyWheelRepository(repo.GetDb()), luckyWheelBody); err != nil {
			log.Println("CreateBankStatementFromWebhook.CreateRoundActivityLuckyWheel", err)
		}
	}

	return nil
}

func (s *promotionReturnTurnService) CalcReturnTurnPendingTransaction(actionTime time.Time) error {

	return CalcReturnTurnPendingTransaction(s.repo, actionTime)
}

func CalcReturnTurnPendingTransaction(repo repository.PromotionReturnTurnRepository, actionTime time.Time) error {

	// RACE_ACTION per minute
	var createBody model.CronActionCreateBody
	createBody.Status = "PENDING"
	// KEY = SYNC_CALCPRORETURNTURN_20060102_{hour}{minute}
	createBody.ActionKey = fmt.Sprintf("SYNC_CALCPRORETURNTURN_%s", actionTime.Format("20060102_1504"))
	createBody.UnlockAt = actionTime.Add(time.Minute * 1)
	if _, err := repo.GetCronActionByActionKey(createBody.ActionKey); err != nil {
		if err != gorm.ErrRecordNotFound {
			return nil
		}
	} else {
		return nil
	}
	actionId, err := repo.CreateCronAction(createBody)
	if err != nil {
		log.Println("CronCalcReturnTurn.ERROR.CreateCronAction", err)
		return internalServerError(errors.New("WORK_IN_ACTION"))
	}

	debugResult := make(map[string]interface{}, 0)

	// Check Promotion
	promotionSetting, err := repo.GetReturnTurnSetting()
	if err != nil {
		debugResult["skip"] = "PROMOTION_NOT_FOUND"
		if err := repo.SetSuccessCronAction(actionId, helper.StructJson(debugResult)); err != nil {
			log.Println("CronCalcReturnTurn.ERROR.SetSuccessCronAction", err)
		}
		if err.Error() == recordNotFound {
			return notFound("PROMOTION_NOT_FOUND")
		}
		return internalServerError(err)
	}
	// Remove Detail ** DO NOT EDIT DETAIL **
	debugResult["promotion"] = map[string]interface{}{
		// "ReturnPercent":    promotionSetting.ReturnPercent,
		"ReturnTypeId":           promotionSetting.ReturnTypeId,
		"CutTypeId":              promotionSetting.CutTypeId,
		"MinTurnPrice":           promotionSetting.MinTurnPrice,
		"MaxReturnPrice":         promotionSetting.MaxReturnPrice,
		"CreditExpireDays":       promotionSetting.CreditExpireDays,
		"CalcOnSport":            promotionSetting.CalcOnSport,
		"ReturnSportPercent":     promotionSetting.ReturnSportPercent,
		"CalcOnCasino":           promotionSetting.CalcOnCasino,
		"ReturnCasinoPercent":    promotionSetting.ReturnCasinoPercent,
		"CalcOnGame":             promotionSetting.CalcOnGame,
		"ReturnGamePercent":      promotionSetting.ReturnGamePercent,
		"CalcOnLottery":          promotionSetting.CalcOnLottery,
		"ReturnLotteryPercent":   promotionSetting.ReturnLotteryPercent,
		"CalcOnP2p":              promotionSetting.CalcOnP2p,
		"ReturnP2pPercent":       promotionSetting.ReturnP2pPercent,
		"CalcOnFinancial":        promotionSetting.CalcOnFinancial,
		"ReturnFinancialPercent": promotionSetting.ReturnFinancialPercent,
		"IsEnabled":              promotionSetting.IsEnabled,
		"CreatedAt":              promotionSetting.CreatedAt,
		"UpdatedAt":              promotionSetting.UpdatedAt,
		"CacheExpiredAt":         promotionSetting.CacheExpiredAt,
	}
	if !promotionSetting.IsEnabled {
		debugResult["skip"] = "PROMOTION_NOT_ENABLED"
		if err := repo.SetSuccessCronAction(actionId, helper.StructJson(debugResult)); err != nil {
			log.Println("CronCalcReturnTurn.ERROR.SetSuccessCronAction", err)
		}
		return notFound("PROMOTION_NOT_AVAILABLE")
	}

	// =========== DO_WORK ===========
	// 1. Check today is going to Calc or not.
	doCalc := false
	if promotionSetting.CutTypeId == model.PROMOTION_RETURN_CUT_TYPE_WEEKLY {
		// เงื่อนไขการคำนวณ คิดยอดเสียรายสัปดาห์ เป็นดังนี้
		// สรุปว่า คืนยอดเสียตัดยอดให้ยูส วันอาทิตย์ ตัดอยดคำนวณ 12:30 แลกเครดิต 14:30
		// หมายความว่า เป็นยอดจากวันอาทิตย์ ถึง วันเสาร์ ที่แล้ว
		// (ยอดตัดวันจันทร์ เราคิดจากยอดวันอาทิตย์ค่ะ  ถ้านับก็คือยอดตัดวันจันทร์ - วันอาทิตย์ (ก็คือยอดเล่นวันจันทร์ - อาทิตย์)
		debugResult["CronCalcReturnTurn.CutType"] = "PROMOTION_RETURN_CUT_TYPE_WEEKLY"
		// ตัดยอด SUN(14)12.30 || => SUN(7)+(8+9+10+11+12)+SAT(13)
		// จะรับได้ในวัน SUN(14)14.30 จะเป็นยอดทั้งหมดที่เล้นมา จนถึงวันเสาร์นี้
		actionAtUtc := actionTime.UTC()
		actionAtBkk := actionAtUtc.In(time.FixedZone("Asia/Bangkok", 7*60*60))
		if actionAtBkk.Weekday() == time.Sunday {
			// Its Sunday and after 12.30
			if (actionAtBkk.Hour() == 12 && actionAtBkk.Minute() > 30) || actionAtBkk.Hour() > 12 {
				doCalc = true
			}
		}
	} else if promotionSetting.CutTypeId == model.PROMOTION_RETURN_CUT_TYPE_DAILY {
		// Daily == Calc Immediately
		debugResult["CronCalcReturnTurn.CutType"] = "PROMOTION_RETURN_CUT_TYPE_DAILY"
		doCalc = true
	}

	if doCalc {
		var query model.PromotionReturnTurnTransactionUncalcListRequest
		query.Page = 0
		query.Limit = 500
		unCalcList, _, err := repo.GetReturnTurnUncalcTransactionList(query)
		if err != nil {
			debugResult["CronCalcReturnTurn.GetReturnUncalcTransactionList.error"] = err.Error()
			log.Println("CronCalcReturnTurn.ERROR.GetReturnTurnTransactionListByCalcAtIsNull", err)
			if err := repo.SetFailCronAction(actionId, helper.StructJson(debugResult)); err != nil {
				log.Println("CronCalcReturnTurn.ERROR.SetFailCronAction", err)
			}
			return internalServerError(err)
		}
		if len(unCalcList) == 0 {
			debugResult["skip"] = "NO_UNCALC_LIST"
			if err := repo.SetSuccessCronAction(actionId, helper.StructJson(debugResult)); err != nil {
				log.Println("CronCalcReturnTurn.ERROR.SetSuccessCronAction", err)
			}
			return nil
		}
		debugResult["unCalcList.Count"] = len(unCalcList)

		// Calc
		calcAt := time.Now()
		updatedCount := 0
		for _, item := range unCalcList {
			if item.GameDetail != "none" {
				// Mink.J — 02/15/2024 11:44 AM
				// ทั้งขั้นต่ำ กับยอดสูงสุด เอารวมทั้งสามเกมส์ค่ะ
				// TULA — 02/15/2024 12:50 PM
				// ถ้าเลือก ให้คำนวน เฉพาะ คาสิโน ตั้งขั้นต่ำ 100 แล้วเสีย คาสิโน 50 เกมสล๊อต 50 กีฬา 50 จะได้ คืนยอดเสียไหม
				// Mink.J — 02/15/2024 12:51 PM
				// ไม่ได้ค่ะ

				checkTotalTurnAmount := 0.0
				returnPriceSatang := 0.0

				calcGameList := strings.Split(item.GameDetail, ",")
				if len(calcGameList) > 0 {
					for _, game := range calcGameList {
						if game == "sport" {
							checkTotalTurnAmount += item.TotalTurnSport
						} else if game == "casino" {
							checkTotalTurnAmount += item.TotalTurnCasino
						} else if game == "game" {
							checkTotalTurnAmount += item.TotalTurnGame
						} else if game == "lottery" {
							checkTotalTurnAmount += item.TotalTurnLottery
						} else if game == "p2p" {
							checkTotalTurnAmount += item.TotalTurnP2p
						} else if game == "financial" {
							checkTotalTurnAmount += item.TotalTurnFinancial
						}
					}
				}

				// fmt.Println("checkTotalTurnAmount", checkTotalTurnAmount, item.MinTurnPrice, item.ReturnPercent, item.MaxReturnPrice)
				if checkTotalTurnAmount >= item.MinTurnPrice {
					for _, game := range calcGameList {
						returnPriceSatangRow := 0.0
						if game == "sport" {
							// Floor = 4 * 100 * 15 = 60 as 0.60
							returnPriceSatangRow = item.TotalTurnSport * item.ReturnSportPercent
							returnPriceSatang += math.Floor(returnPriceSatangRow)
						} else if game == "casino" {
							// Floor = 4 * 100 * 15 = 60 as 0.60
							returnPriceSatangRow = item.TotalTurnCasino * item.ReturnCasinoPercent
							returnPriceSatang += math.Floor(returnPriceSatangRow)
						} else if game == "game" {
							// Floor = 4 * 100 * 15 = 60 as 0.60
							returnPriceSatangRow = item.TotalTurnGame * item.ReturnGamePercent
							returnPriceSatang += math.Floor(returnPriceSatangRow)
						} else if game == "lottery" {
							// Floor = 4 * 100 * 15 = 60 as 0.60
							returnPriceSatangRow = item.TotalTurnLottery * item.ReturnLotteryPercent
							returnPriceSatang += math.Floor(returnPriceSatangRow)
						} else if game == "p2p" {
							// Floor = 4 * 100 * 15 = 60 as 0.60
							returnPriceSatangRow = item.TotalTurnP2p * item.ReturnP2pPercent
							returnPriceSatang += math.Floor(returnPriceSatangRow)
						} else if game == "financial" {
							// Floor = 4 * 100 * 15 = 60 as 0.60
							returnPriceSatangRow = item.TotalTurnFinancial * item.ReturnFinancialPercent
							returnPriceSatang += math.Floor(returnPriceSatangRow)
						}
					}
				}

				// Calc MAX Return Price onSetting
				if item.MaxReturnPrice > 0 {
					maxReturnPriceSatang := item.MaxReturnPrice * 100
					// MIN-MAX
					if returnPriceSatang > maxReturnPriceSatang {
						returnPriceSatang = maxReturnPriceSatang
					}
				}

				var updateBody model.PromotionReturnTurnTransactionCalcBody
				updateBody.CutTypeId = promotionSetting.CutTypeId // ** วิธีการตัดคิดตอนตัด หรือตอนสร้าง ???
				updateBody.StatusId = model.PROMOTION_RETURN_STATUS_READY
				updateBody.CalcAt = &calcAt
				updateBody.ReturnPrice = returnPriceSatang / 100
				if err := repo.UpdateCalcReturnTurnTransaction(item.Id, updateBody); err != nil {
					debugResult["UpdateCalcReturnTurnTransaction.error"] = err.Error()
					log.Println("CronCalcReturnTurn.ERROR.UpdateReturnTurnTransaction", err)
					if err := repo.SetFailCronAction(actionId, helper.StructJson(debugResult)); err != nil {
						log.Println("CronCalcReturnTurn.ERROR.SetFailCronAction", err)
					}
					return internalServerError(err)
				}
				updatedCount++
			} else {
				var updateBody model.PromotionReturnTurnTransactionCalcBody
				// NO_STATUS JUST LEFT IT PENDING
				// updateBody.StatusId = model.PROMOTION_RETURN_STATUS_READY
				updateBody.CalcAt = &calcAt
				if err := repo.UpdateCalcReturnTurnTransaction(item.Id, updateBody); err != nil {
					debugResult["UpdateCalcReturnTurnTransaction.error"] = err.Error()
					log.Println("CronCalcReturnTurn.ERROR.UpdateReturnTurnTransaction", err)
					if err := repo.SetFailCronAction(actionId, helper.StructJson(debugResult)); err != nil {
						log.Println("CronCalcReturnTurn.ERROR.SetFailCronAction", err)
					}
					return internalServerError(err)
				}
				updatedCount++
			}
		}
		debugResult["updatedCount"] = updatedCount
	} else {
		debugResult["skip"] = "NO_CALC_TODAY"
	}

	// RACE_ACTION = SUCCESS
	if err := repo.SetSuccessCronAction(actionId, helper.StructJson(debugResult)); err != nil {
		log.Println("CronCalcReturnTurn.ERROR.SetSuccessCronAction", err)
	}
	return nil
}

func (s *promotionReturnTurnService) doCheckUserReturnTurnList(userId int64) error {

	var query model.PromotionReturnTurnTransactionListRequest
	query.Page = 0
	query.Limit = 0
	query.UserId = userId
	statusId := model.PROMOTION_RETURN_STATUS_READY
	query.StatusId = &statusId
	list, _, err := s.repo.GetReturnTurnTransactionList(query)
	if err != nil {
		return internalServerError(err)
	}

	// วันหมดอายุการรับเครดิตคืน 0 คือ เคริตที่ได้ จะทบไปเรื่อยๆ จนกว่าจะกดรับ
	// วันหมดอายุการรับเครดิตคืน 1 คือ เครดิตมีอายุรับได้ 1 วัน (ถึง 11.00 ของอีกวัน ถ้า มีอายุ 1 วัน)
	// รับเงินได้ตั้งแต่เวลา 14.30
	// วันหมดอายุการรับเครดิตคืน 0 คือ เคริตที่ได้ จะทบไปเรื่อยๆ จนกว่าจะกดรับ
	// วันหมดอายุการรับเครดิตคืน 1 คือ เครดิตมีอายุรับได้ 1 วัน
	// ตัดรอบ 14.30 ของทุกวัน ถ้าอายุ 1 วัน จะตัด 11.00 ของวันถัดไป
	// ConvertFrom 2023-10-09 14:30:00 +0700 +07 To 2023-10-09 07:30:00 UTC
	// Expire Weekly
	// if item.CalcAt != nil {
	// expireDate := item.CalcAt.AddDate(0, 0, item.CreditExpireDays)

	// [20240815]
	// ปรับ Requirement เฉพาะ คืนยอดเสียรายสัปดาห์ ในส่วนของวันหมดอายุสะสมการรับ
	// สมมุติตั้งสะสมอายุการรับ 1 วัน เริ่มแจก 01/09/24  14:30 น.
	// หมดเวลาอายุการรับ 1 วัน เป็น 01/09/24 23:59 น.
	// หมายเหตุ: จากเดิม วันหมดอายุการรับเครดิตคืน 1 คือ เครดิตมีอายุรับได้ 1 วัน (ถึง 11.00 ของอีกวัน ถ้า มีอายุ 1 วัน)     รับเงินได้ตั้งแต่เวลา 14.30

	actionTime := time.Now().UTC()
	// actionTime 2023-10-09 07:17:50.6427708 +0000 UTC
	// itemOfDate 2023-10-09 07:30:00 +0000 UTC
	// expireDate 2023-10-20 07:30:00 +0000 UTC
	for _, item := range list {
		// check expire
		if item.CreditExpireDays > 0 {
			if item.CutTypeId == model.PROMOTION_RETURN_CUT_TYPE_WEEKLY {
				// Expire Weekly
				// Ex. 2024-08-07 = 07T00:00:00Z UTC + 1 ExpireDay = 2024-08-08 00:00:00 UTC = BKK-7
				if ofDate, err := time.Parse("2006-01-02T15:04:05Z", item.OfDate); err == nil {
					// expireDate := item.CalcAt.AddDate(0, 0, item.CreditExpireDays)
					expireDate := ofDate.Add(time.Hour*-7).AddDate(0, 0, item.CreditExpireDays)
					fmt.Println("CreditExpireDays", item.CreditExpireDays, "actionTime", actionTime, "itemOfDate", ofDate, "expireDate", expireDate)
					if expireDate.Before(actionTime) {
						// Expired
						var updateBody model.PromotionReturnTurnTransactionUpdateBody
						updateBody.StatusId = model.PROMOTION_RETURN_STATUS_EXPIRED
						if err := s.repo.UpdateExpriedReturnTurnTransaction(item.Id, updateBody); err != nil {
							return internalServerError(err)
						}
					}
				}
			} else {
				// Expire Daily
				// ofDate, err := time.Parse("20060102", item.DailyKey[1:9])
				if ofDate, err := time.Parse("2006-01-02T15:04:05Z", item.OfDate); err == nil {
					// From 2023-10-09 07:30:00 UTC (14.30 +7)
					// itemOfDate := ofDate.UTC().Add(time.Hour * 7).Add(time.Minute * 30)
					// To 2023-10-10 04:00:00 UTC (11.00 +7)
					// expireDate := ofDate.AddDate(0, 0, item.CreditExpireDays)
					// expireDate = expireDate.Add(time.Hour * 4)
					expireDate := ofDate.Add(time.Hour*-7).AddDate(0, 0, item.CreditExpireDays)
					fmt.Println("CreditExpireDays", item.CreditExpireDays, "actionTime", actionTime, "itemOfDate", ofDate, "expireDate", expireDate)
					if expireDate.Before(actionTime) {
						// Expired
						var updateBody model.PromotionReturnTurnTransactionUpdateBody
						updateBody.StatusId = model.PROMOTION_RETURN_STATUS_EXPIRED
						if err := s.repo.UpdateExpriedReturnTurnTransaction(item.Id, updateBody); err != nil {
							return internalServerError(err)
						}
					}
				} else {
					log.Println("doCheckUserReturnTurnList.ERROR.ParseItemOfDate", err, item.OfDate)
				}
			}
		}
	}
	return nil
}

func (s *promotionReturnTurnService) CronCutReturnTurnByDate(ofDate string) error {

	return CronCutReturnTurnByDate(s.repo, ofDate)
}

func CronCutReturnTurnByStatementDate(repo repository.PromotionReturnTurnRepository, statement string) error {

	// Validate INPUT
	statementAt, err := time.Parse("2006-01-02", statement)
	if err != nil {
		return badRequest("INVALID_DATE")
	}
	// ofDate is Tomorrow
	ofDate := statementAt.AddDate(0, 0, 1)

	return CronCutReturnTurnByDate(repo, ofDate.Format("2006-01-02"))
}

func CronCutReturnTurnByDate(repo repository.PromotionReturnTurnRepository, ofDate string) error {

	actionTime := time.Now()

	// Validate INPUT
	ofTime, err := time.Parse("2006-01-02", ofDate)
	if err != nil {
		return badRequest("INVALID_DATE")
	}

	log.Println("CronCutReturnTurnByDate ofDate=", ofDate, " ofTime=", ofTime)

	// If today then check time
	if ofTime.Format("2006-01-02") == actionTime.Format("2006-01-02") {
		// Day by day at 11.00 to 23.59 Daily (+7)
		bbkLoc := time.FixedZone("Asia/Bangkok", 7*60*60)
		runTime := actionTime.In(bbkLoc)
		if runTime.Hour() < 11 {
			return errors.New("NOT_IN_TIME:" + runTime.String())
		}
	}

	// Check Daily playlog to START CUT and CALC
	// Use OLD_PLAYLONG = api_status as play_logs
	statementDate := ofTime.AddDate(0, 0, -1)
	checkData, err := repo.CheckDailyPlayLog(statementDate.Format("2006-01-02"))
	if err != nil {
		return internalServerError(err)
	}
	if !checkData.IsReady {
		return badRequest("PLAY_LOG_IS_NOT_READY")
	}
	// =========== Playlog is Ready ! ===========

	// useAction
	var createBody model.CronActionCreateBody
	createBody.Status = "PENDING"
	// KEY = SYNC_PROCUTRETURNTURNBYDATE_20060102_11:00:00
	createBody.ActionKey = fmt.Sprintf("SYNC_PROCUTRETURNTURNBYDATE_%s%s", ofTime.Format("20060102"), "11:00:00")
	createBody.UnlockAt = actionTime.Add(time.Minute * 1)
	if _, err := repo.GetCronActionByActionKey(createBody.ActionKey); err != nil {
		if err != gorm.ErrRecordNotFound {
			return nil
		}
	} else {
		// already create then CRON_STATUS
		if err := repo.UpdateAgcCronCalcStatus("PROMOTION_RETURN_LOSS", statementDate.Format("2006-01-02"), "DONE"); err != nil {
			log.Println(err)
		}
		// already create then calc
		// add Current hours and minutes
		now := time.Now()
		actionTime2 := ofTime.Add(time.Hour * time.Duration(now.Hour()))
		actionTime2 = actionTime2.Add(time.Minute * time.Duration(now.Minute()))
		return CalcReturnTurnPendingTransaction(repo, actionTime2)
	}
	actionId, err := repo.CreateCronAction(createBody)
	if err != nil {
		log.Println("CronCutReturnTurnByDate.ERROR.CreateCronAction", err)
		return internalServerError(errors.New("WORK_IN_ACTION"))
	}

	debugResult := make(map[string]interface{}, 0)

	// Check Promotion
	promotionSetting, err := repo.GetReturnTurnSetting()
	if err != nil {
		debugResult["skip"] = "PROMOTION_NOT_FOUND"
		if err := repo.SetSuccessCronAction(actionId, helper.StructJson(debugResult)); err != nil {
			log.Println("CronCutReturnTurnByDate.ERROR.SetSuccessCronAction", err)
		}
		if err.Error() == recordNotFound {
			return notFound("PROMOTION_NOT_FOUND")
		}
		return internalServerError(err)
	}
	// Remove Detail ** DO NOT EDIT DETAIL **
	debugResult["promotion"] = map[string]interface{}{
		// "ReturnPercent":    promotionSetting.ReturnPercent,
		"ReturnTypeId":           promotionSetting.ReturnTypeId,
		"CutTypeId":              promotionSetting.CutTypeId,
		"MinTurnPrice":           promotionSetting.MinTurnPrice,
		"MaxReturnPrice":         promotionSetting.MaxReturnPrice,
		"CreditExpireDays":       promotionSetting.CreditExpireDays,
		"CalcOnSport":            promotionSetting.CalcOnSport,
		"ReturnSportPercent":     promotionSetting.ReturnSportPercent,
		"CalcOnCasino":           promotionSetting.CalcOnCasino,
		"ReturnCasinoPercent":    promotionSetting.ReturnCasinoPercent,
		"CalcOnGame":             promotionSetting.CalcOnGame,
		"ReturnGamePercent":      promotionSetting.ReturnGamePercent,
		"CalcOnLottery":          promotionSetting.CalcOnLottery,
		"ReturnLotteryPercent":   promotionSetting.ReturnLotteryPercent,
		"CalcOnP2p":              promotionSetting.CalcOnP2p,
		"ReturnP2pPercent":       promotionSetting.ReturnP2pPercent,
		"CalcOnFinancial":        promotionSetting.CalcOnFinancial,
		"ReturnFinancialPercent": promotionSetting.ReturnFinancialPercent,
		"IsEnabled":              promotionSetting.IsEnabled,
		"CreatedAt":              promotionSetting.CreatedAt,
		"UpdatedAt":              promotionSetting.UpdatedAt,
		"CacheExpiredAt":         promotionSetting.CacheExpiredAt,
	}
	if !promotionSetting.IsEnabled {
		debugResult["skip"] = "PROMOTION_NOT_ENABLED"
		if err := repo.SetSuccessCronAction(actionId, helper.StructJson(debugResult)); err != nil {
			log.Println("CronCutReturnTurnByDate.ERROR.SetSuccessCronAction", err)
		}
		return notFound("PROMOTION_NOT_AVAILABLE")
	}

	// =========== DO_WORK ===========

	// Just Only Create
	if createResult, err := doCreateReturnTurnDailyTransaction(repo, ofTime, *promotionSetting); err != nil {
		debugResult["doCreateReturnTurnDailyTransaction.error"] = err.Error()
		log.Println("CronCutReturnTurnByDate.ERROR.doCreateReturnTurnDailyTransaction", err)
		if err := repo.SetFailCronAction(actionId, helper.StructJson(debugResult)); err != nil {
			log.Println("CronCutReturnTurnByDate.ERROR.SetFailCronAction", err)
		}
		return internalServerError(err)
	} else {
		debugResult["doCreateReturnTurnDailyTransaction.createResult"] = createResult
	}

	// SUCCESS
	if err := repo.SetSuccessCronAction(actionId, helper.StructJson(debugResult)); err != nil {
		log.Println("CronCutReturnTurnByDate.ERROR.SetSuccessCronAction", err)
	}
	return nil
}
