package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"os"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm"
)

type PaymentGatewayService interface {
	// PaymentGateway Account
	// GetWebPaygateDepositAccount() (*model.PaygateCustomerDepositAccount, error)
	GetWebPaygateDepositAccountList() ([]model.PaygateCustomerDepositAccount, error)
	// MultipleProvider
	GetPaygateMerchantLimitList() ([]model.PaygateMerchantLimitResponse, error)
	GetNonActivePaygateMerchantOption() ([]model.SelectOptions, error)
	GetPaygateAccountById(req model.GetByIdRequest) (*model.PaygateAccountResponse, error)
	GetPaygateAccountList(req model.PaygateAccountListRequest) (*model.SuccessWithPagination, error)
	CreatePaygateAccount(req model.PaygateAccountCreateRequest) (*int64, error)
	UpdatePaygateAccount(id int64, req model.PaygateAccountUpdateRequest) error
	DeletePaygateAccount(id int64) error

	// HENG
	// GetPaygateHengBalance(req model.PaygateHengBalanceRequest) (*model.PaygateHengBalanceResponse, error)
	// GetPaygateHengOrderList(req model.PaygateHengOrderListRequest) (*model.SuccessWithPagination, error)
	// GetPaygateHengOrderById(id int64) (*model.PaygateHengOrderResponse, error)
	// GetPaygateHengQrById(id int64) (*model.PaygateHengOrderQrResponse, error)
	// CreatePaygateHengOrder(req model.PaygateHengOrderCreateRequest) (*int64, error)
	// CreatePaygateHengWebhook(req model.PaygateHengWebhookRequest) (*int64, error)
	// // HENG-REPORT
	// GetHengOrderReportList(req model.HengOrderReportListRequest) (*model.SuccessWithPagination, error)
	// CreateHengDepositFromOrder(orderId int64) error
	// IgnoreHengDepositFromOrder(orderId int64) error
	// GetHengCallbackReportResponse(req model.HengCallbackReportListRequest) (*model.SuccessWithPagination, error)
	// // Test-HENG
	// CheckCustomerDepositName(userid int64, statementName string) error
	// // WEB
	// GetHengWebDepositAccount() (*model.PaygateHengCustomerDepositInfo, error)
	// CreatePaygateHengDeposit(req model.PaygateHengDepositCreateRequest) (*int64, error)
	// SETTING
	// GetPaygateHengSetting() (*model.PaygateHengSettingResponse, error)
	// UpdatePaygateHengSetting(req model.PaygateHengSettingUpdateRequest) error
	// LUCKYTHAI
	CreateLuckyThaiWebhook(req model.LuckyThaiWebhookRequest) (*int64, error)
	GetLuckyThaiWebDepositAccount() (*model.LuckyThaiCustomerDepositInfo, error)
	CreateLuckyThaiDeposit(req model.LuckyThaiDepositCreateRequest) (*model.LuckyThaiOrderWebResponse, error)
	CreateLuckyThaiWithdraw(req model.LuckyThaiWithdrawCreateRequest) (*int64, error)
	LuckyThaiCheckBalance() (*model.LuckyThaiCheckBalanceRemoteResponse, error)
	// SysLog
	CreateSystemLog(name string, req interface{}) error
	// Report
	GetReportPaymentListOptions() ([]model.SelectOptions, error)
	GetPaygateOrderReportList(req model.PaygateOrderReportListRequest) (*model.SuccessWithPagination, error)
	CreatePaygateDepositFromOrder(body model.PaygateOrderCreateDepositRequest) error
	IgnorePaygateDepositFromOrder(body model.PaygateOrderIgnoreOrderRequest) error

	GetPaygateMerchantOption() ([]model.SelectOptions, error)
	// RaceCondition
	RacingDepositCreate(paymentId int64, userId int64, amount float64) (*int64, error)
}

type paymentGatewayService struct {
	repo                      repository.PaymentGatewayRepository
	sharedDb                  *gorm.DB
	activityLuckyWheelService ActivityLuckyWheelService
	promotionWebService       PromotionWebService
	notiService               NotificationService
}

func NewPaymentGatewayService(
	repo repository.PaymentGatewayRepository,
	sharedDb *gorm.DB,
	activityLuckyWheelService ActivityLuckyWheelService,
	promotionWebService PromotionWebService,
	notiService NotificationService,
) PaymentGatewayService {
	return &paymentGatewayService{repo, sharedDb, activityLuckyWheelService, promotionWebService, notiService}
}

func (s paymentGatewayService) GetWebPaygateDepositAccountList() ([]model.PaygateCustomerDepositAccount, error) {

	var result []model.PaygateCustomerDepositAccount

	merchantList, err := s.repo.GetDepositActivePaygateMerchantList()
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, badRequest("MERCHANT_NOT_FOUND")
		}
		return nil, internalServerError(err)
	}

	for _, merchant := range merchantList {
		// [********] P.lay And P.mink confirm in discord pull from merchant แต่ผมจะเก็บ default ไว้ก่อน
		// tempRow.Name = pgAccount.MerchantType
		// tempRow.ShopName = pgAccount.MerchantName
		// // Display Name - ข้อความ แสดงหน้าแจ้งฝาก หน้าเว็บ (ลูกค้าแก้เอง)
		// // Name - ชื่อpayment แสดงในส่วนของการตั้งค่า หลังบ้าน (ห้ามแก้)
		// // Shop Name - แสดงที่รายการฝากถอน หลังบ้าน (ลูกค้าแก้เอง ให้ตรงกับ PaymentGateway)
		// tempRow.DisplayName = merchant.DisplayName
		// // later : Merge MIN/MAX with WebConfig WithdrawLimit
		// tempRow.MinAmount = 0
		// tempRow.MaxAmount = *********
		result = append(result, model.PaygateCustomerDepositAccount{
			MerchantId:  merchant.ProviderId,
			Name:        merchant.Name, // หน้าบ้านใช้ดักตามเดิมไว้ค่อยเปลี่ยนเป็น ProviderId/MerchantId ทีหลัง
			DisplayName: merchant.DisplayName,
			MinAmount:   int64(merchant.PaymentDepositMinimum),
			MaxAmount:   int64(merchant.PaymentDepositMaximum),
		})
	}

	return result, nil
}

func (s paymentGatewayService) TestMerchantConnect(id int64) (string, error) {

	// var result string
	result := "ERROR"

	// Just Check by ID
	pgAccount, err := s.repo.GetPaygateAccountById(id)
	if err != nil {
		if err.Error() == recordNotFound {
			return "ERROR", notFound("MERCHANT_NOT_FOUND")
		}
		return "ERROR", internalServerError(err)
	}

	fmt.Println("TestMerchantConnect", pgAccount)

	// if pgAccount.Id != 0 {
	// 	actionAt := time.Now()
	// 	if pgAccount.TypeName == model.PAYGATE_MERCHANT_TYPE_HENG {
	// 		// Use Login API to test
	// 		var newSetting model.PaygateHengSettingResponse
	// 		newSetting.ApiEndPoint = pgAccount.ApiEndPoint
	// 		newSetting.Username = pgAccount.Username
	// 		newSetting.Password = pgAccount.Password
	// 		if remoteResp, err := s.repo.LoginPaygateHeng(newSetting); err == nil {
	// 			var body model.PaygateMerchantUpdateBody
	// 			body.MerchantUpdateAt = &actionAt
	// 			body.CallbackUrl = &remoteResp.CallbackUrl
	// 			if err := s.repo.UpdatePaygateMerchant(id, body); err != nil {
	// 				return "ERROR", internalServerError(err)
	// 			}
	// 			result = "ACTIVE"
	// 		} else {
	// 			return "ERROR", badRequest(err.Error())
	// 		}
	// 	} else if pgAccount.TypeName == model.PAYGATE_MERCHANT_TYPE_LUCKYTHAI {
	// 		// Use Check Balance API to test
	// 		if _, err := s.repo.LuckyThaiCheckBalance(*pgAccount); err != nil {
	// 			result = "ERROR"
	// 		} else {
	// 			var body model.PaygateMerchantUpdateBody
	// 			body.MerchantUpdateAt = &actionAt
	// 			// body.CallbackUrl = &remoteResp.CallbackUrl
	// 			if err := s.repo.UpdatePaygateMerchant(id, body); err != nil {
	// 				return "ERROR", internalServerError(err)
	// 			}
	// 			result = "ACTIVE"
	// 		}
	// 	} else if pgAccount.TypeName == model.PAYGATE_MERCHANT_TYPE_PAPAYAPAY {
	// 		// Use Check Balance API to test
	// 		if len(pgAccount.PrivateKey) > 10 {
	// 			result = "ACTIVE"
	// 		}
	// 	} else if pgAccount.TypeName == model.PAYGATE_MERCHANT_TYPE_PAYONEX {
	// 		// Use Check Balance API to test
	// 		if len(pgAccount.AccessKey) > 5 && len(pgAccount.SecretKey) > 5 {
	// 			result = "ACTIVE"
	// 		}
	// 	} else if pgAccount.TypeName == model.PAYGATE_MERCHANT_TYPE_JBPAY {
	// 		// Use Check Balance API to test
	// 		if len(pgAccount.PartnerKey) > 5 && (len(pgAccount.RepayAppId) > 5 || len(pgAccount.LoanAppId) > 5) && len(pgAccount.MerchantId) > 5 && len(pgAccount.Token) > 5 && len(pgAccount.AesKey) > 5 && len(pgAccount.ApiEndPoint) > 5 {
	// 			result = "ACTIVE"
	// 		}
	// 	} else if pgAccount.TypeName == model.PAYGATE_MERCHANT_TYPE_POMPAY {
	// 		// Use Check Balance API to test
	// 		if len(pgAccount.MerchantId) > 3 && len(pgAccount.SecretKey) > 20 {
	// 			result = "ACTIVE"
	// 		}
	// 	} else if pgAccount.TypeName == model.PAYGATE_MERCHANT_TYPE_PAYMENTCO {
	// 		// Use MID and SecretKey to test
	// 		if len(pgAccount.MerchantId) > 3 && len(pgAccount.SecretKey) > 20 {
	// 			result = "ACTIVE"
	// 		}
	// 	} else if pgAccount.TypeName == model.PAYGATE_MERCHANT_TYPE_ZAPPAY {
	// 		// Use Check Balance API to test
	// 		if len(pgAccount.PartnerKey) > 5 && (len(pgAccount.RepayAppId) > 5 || len(pgAccount.LoanAppId) > 5) && len(pgAccount.MerchantId) > 5 && len(pgAccount.Token) > 5 && len(pgAccount.AesKey) > 5 && len(pgAccount.ApiEndPoint) > 5 {
	// 			result = "ACTIVE"
	// 		}
	// 	} else if pgAccount.TypeName == model.PAYGATE_MERCHANT_TYPE_ONEPAY {
	// 		// Use Check Balance API to test
	// 		if len(pgAccount.ApiEndPoint) > 5 && len(pgAccount.PartnerKey) > 5 {
	// 			result = "ACTIVE"
	// 		}
	// 	} else if pgAccount.TypeName == model.PAYGATE_MERCHANT_TYPE_FLASHPAY {
	// 		// Prerequisites
	// 		// To begin working with the FLASH-PAY , ensure you have access to the following credentials:
	// 		// API Key: A unique identifier used to authenticate your API requests.
	// 		// Secret Key: A private key used to generate signatures for secure communication.
	// 		// Merchant ID: A unique identifier assigned to your business for API transactions.
	// 		// Client ID: A unique identifier for the client initiating transactions.
	// 		if len(pgAccount.ApiEndPoint) > 5 && len(pgAccount.AccessKey) > 5 && len(pgAccount.SecretKey) > 5 && len(pgAccount.MerchantId) > 3 {
	// 			result = "ACTIVE"
	// 		}
	// 	} else if pgAccount.TypeName == model.PAYGATE_MERCHANT_TYPE_BIZPAY {
	// 		// Same as FLASHPAY
	// 		if len(pgAccount.ApiEndPoint) > 5 && len(pgAccount.AccessKey) > 5 && len(pgAccount.SecretKey) > 5 && len(pgAccount.MerchantId) > 3 {
	// 			result = "ACTIVE"
	// 		}
	// 	} else if pgAccount.TypeName == model.PAYGATE_MERCHANT_TYPE_SUGARPAY {
	// 		// Same as FLASHPAY
	// 		if len(pgAccount.ApiEndPoint) > 5 && len(pgAccount.AccessKey) > 5 && len(pgAccount.SecretKey) > 5 && len(pgAccount.LoanAppId) > 3 {
	// 			result = "ACTIVE"
	// 		}
	// 	}
	// }

	// // Update if Current
	// curSetting, err := s.GetPaygateSetting()
	// if err != nil {
	// 	return result, internalServerError(err)
	// }
	// if curSetting.MerchantId != id && curSetting.MerchantId == pgAccount.Id {
	// 	var body model.PaygateSettingUpdateBody
	// 	body.Status = &result
	// 	if err := s.repo.UpdatePaygateSetting(curSetting.Id, body); err != nil {
	// 		return "ERROR", internalServerError(err)
	// 	}
	// }
	return result, nil
}

func confirmDepositTransaction(repo repository.PaymentGatewayRepository, id int64, req model.BankConfirmDepositRequest) error {

	record, err := repo.GetBankTransactionById(id)
	if err != nil {
		return internalServerError(err)
	}
	if record.TransactionStatusId != model.TRANS_STATUS_PENDING {
		return badRequest("Transaction is not pending")
	}
	if record.TransactionTypeId != model.TRANSACTION_TYPE_DEPOSIT && record.TransactionTypeId != model.TRANSACTION_TYPE_BONUS {
		return badRequest("Transaction is not deposit")
	}
	jsonBefore, _ := json.Marshal(record)

	var updateData model.BankDepositTransactionConfirmBody
	updateData.TransactionStatusId = model.TRANS_STATUS_DEPOSIT_PENDING_CREDIT
	updateData.ConfirmedAt = req.ConfirmedAt
	updateData.ConfirmedByAdminId = req.ConfirmedByAdminId

	// if record.IsAutoCredit && record.TransferAt != nil {
	// 	seconds := time.Now().UTC().Sub(record.TransferAt.UTC()).Seconds()
	// 	autoProcessTimer := fmt.Sprintf("%.2f", seconds)
	// 	updateData.AutoProcessTimer = &autoProcessTimer
	// }

	var createBody model.CreateBankTransactionActionBody
	createBody.ActionKey = fmt.Sprintf("DCF_STATE#%d", record.Id)
	createBody.TransactionId = record.Id
	createBody.UserId = record.UserId
	createBody.TransactionTypeId = record.TransactionTypeId
	createBody.FromAccountId = record.FromAccountId
	createBody.ToAccountId = record.ToAccountId
	createBody.JsonBefore = string(jsonBefore)
	if req.TransferAt == nil {
		createBody.TransferAt = record.TransferAt
	} else {
		TransferAt := req.TransferAt
		createBody.TransferAt = TransferAt
		updateData.TransferAt = *TransferAt
	}
	if req.SlipUrl != nil {
		createBody.SlipUrl = *req.SlipUrl
	}
	createBody.CreditAmount = record.CreditAmount
	if req.BonusAmount != nil {
		createBody.BonusAmount = *req.BonusAmount
	}
	createBody.ConfirmedAt = req.ConfirmedAt
	createBody.ConfirmedByAdminId = req.ConfirmedByAdminId
	if actionId, err := repo.CreateTransactionAction(createBody); err == nil {
		// do nothing ?
		if err := repo.ConfirmPendingDepositTransaction(id, updateData); err != nil {
			if err := repo.RollbackTransactionAction(*actionId); err == nil {
				return internalServerError(err)
			}
			return internalServerError(err)
		}
	} else {
		return internalServerError(err)
	}
	return nil
}

func (s paymentGatewayService) CreateLuckyThaiWebhook(req model.LuckyThaiWebhookRequest) (*int64, error) {

	var createBody model.LuckyThaiWebhookCreateBody
	createBody.Name = "LUCKYTH_CALLBACK"
	createBody.JsonPayload = req.JsonPayload
	insertId, err := s.repo.CreateLuckyThaiWebhook(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// DEPOSIT
	// chainName=BANK&orderNo=PBANKTHB000749T1713368688340RyjVi6&payAmount=0&clientCode=S001228iQ9C7&sign=f0caee936d4d460a4323a038b482900a&clientNo=kONOWIvCQp&txid&payer=&coinUnit=THB&status=CREATE
	// chainName=BANK&orderNo=PBANKTHB000749T1713368050627RDqvG3&payAmount=0&clientCode=S001228iQ9C7&sign=8740646cf30d953d3df533fd82d94038&clientNo=xaFXyelUvD&txid&payer=&coinUnit=THB&status=CREATE
	// chainName=BANK&orderNo=PBANKTHB000749T1713369879532R1KLFD&payAmount=100.00&clientCode=S001228iQ9C7&sign=7dd8e353bed6db8f8d4ffc043b469647&clientNo=NEKISErdkR&txid=7285139000677448401&payer=&coinUnit=THB&status=PAID
	// chainName=BANK&orderNo=PBANKTHB000749T1713427035214R7p6aO&payAmount=113.00&clientCode=S001228iQ9C7&sign=a0cbc37f3f4f9e168e6684394dfb7e45&clientNo=ST9M24045&txid=7256115000763095594&payer=&coinUnit=THB&status=PAID

	// Withdraw
	// chainName=BANK&orderNo=DFBANKTHB000749T1713432369885RdYbUt&payAmount=103.00&clientCode=S001228iQ9C7&sign=b40a80c275e2e24d69a4016bc321fc76&clientNo=ST9M24048&txid&payer=&coinUnit=THB&status=CANCEL
	// chainName=BANK&orderNo=DFBANKTHB000749T1713432982603RHlAxY&payAmount=103.00&clientCode=S001228iQ9C7&sign=fbb787f248ef442b19f2b892a310f613&clientNo=ST9M24049&txid=setryy&payer=&coinUnit=THB&status=PAID

	// decode in to struct
	var remoteResp model.LuckyThaiWebhookResponse
	params := strings.Split(req.JsonPayload, "&")
	for _, param := range params {
		pair := strings.Split(param, "=")
		if len(pair) == 2 {
			switch pair[0] {
			case "chainName":
				remoteResp.ChainName = pair[1]
			case "orderNo":
				remoteResp.OrderNo = pair[1]
			case "payAmount":
				remoteResp.PayAmount, _ = strconv.ParseFloat(pair[1], 64)
			case "clientCode":
				remoteResp.ClientCode = pair[1]
			case "sign":
				remoteResp.Sign = pair[1]
			case "clientNo":
				remoteResp.ClientNo = pair[1]
			case "txid":
				remoteResp.Txid = pair[1]
			case "payer":
				remoteResp.Payer = pair[1]
			case "coinUnit":
				remoteResp.CoinUnit = pair[1]
			case "status":
				remoteResp.Status = pair[1]
			}
		}
	}

	if remoteResp.ClientNo == "" || remoteResp.OrderNo == "" {
		return nil, internalServerError(fmt.Errorf("INVALID_REFERENCE"))
	}

	// Service Race Condition by Reference2
	var rcCreateBody model.RaceActionCreateBody
	rcCreateBody.Name = "CreateLuckyThaiWebhook"
	rcCreateBody.JsonRequest = helper.StructJson(req)
	rcCreateBody.Status = "PENDING"
	// ActionKey = Ref2-MINUTE
	rcCreateBody.ActionKey = fmt.Sprintf("%s-%s", remoteResp.ClientNo, time.Now().Format("0601021504"))
	rcCreateBody.UnlockAt = time.Now().UTC().Add(time.Second * 1)
	if _, err := s.repo.CreateRaceCondition(rcCreateBody); err != nil {
		return insertId, internalServerError(err)
	}

	// Get Posible Order
	var query model.LuckyThaiOrderListRequest
	query.OrderNo = remoteResp.ClientNo
	query.TransactionNo = remoteResp.OrderNo
	query.Amount = fmt.Sprintf("%.2f", remoteResp.PayAmount)
	query.Status = "WAIT_PAYMENT"
	query.Limit = 1
	list, _, err := s.repo.GetDbLuckyThaiOrderList(query)
	if err != nil {
		return nil, internalServerError(err)
	}

	if len(list) > 0 {
		for _, item := range list {

			if item.TransactionStatus == nil {
				continue
			}

			// Update Order
			successStatus := remoteResp.Status
			if successStatus == "WAIT_PAYMENT" {
				successStatus = "NOT_WAIT_PAYMENT"
			}
			if err := s.repo.ApproveDbLuckyThaiOrder(item.Id, successStatus); err != nil {
				return nil, internalServerError(err)
			}

			// Payment Success, Create Deposit if UserId is not null
			if item.UserId > 0 && item.OrderTypeId == model.PAYGATE_ORDER_TYPE_DEPOSIT {
				if remoteResp.Status == "PAID" && remoteResp.PayAmount > 0 {
					if _, err := s.createCustomerDepositFromLuckyThai(item, remoteResp); err != nil {
						// WebhookLog
						var createBody2 model.LuckyThaiWebhookCreateBody
						createBody2.Name = "LUCKYTH_DEPOSIT_ERROR"
						createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
							"item":   item,
							"method": "createCustomerDeposit",
							"error":  err.Error(),
						})
						if _, err = s.repo.CreateLuckyThaiWebhook(createBody2); err != nil {
							log.Println("Error CreateLuckyThaiWebhook.CreateLuckyThaiWebhook", err)
						}
					}
				}
			} else if item.RefId != nil && item.OrderTypeId == model.PAYGATE_ORDER_TYPE_WITHDRAW {
				if remoteResp.Status == "PAID" && remoteResp.PayAmount > 0 {
					if _, err := approveCustomerWithdrawFromLuckyThai(s.repo, *item.RefId); err != nil {
						// WebhookLog
						var createBody2 model.LuckyThaiWebhookCreateBody
						createBody2.Name = "LUCKYTH_WITHDRAW_ERROR"
						createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
							"item":   item,
							"method": "approveCustomerWithdrawFromLuckyThai",
							"error":  err.Error(),
						})
						if _, err = s.repo.CreateLuckyThaiWebhook(createBody2); err != nil {
							log.Println("Error approveCustomerWithdrawFromLuckyThai.CreateLuckyThaiWebhook", err)
						}
					}
				} else if remoteResp.Status == "CANCEL" && remoteResp.PayAmount > 0 {
					if _, err := rollbackCustomerWithdrawFromLuckyThai(s.repo, *item.RefId); err != nil {
						// WebhookLog
						var createBody2 model.LuckyThaiWebhookCreateBody
						createBody2.Name = "LUCKYTH_WITHDRAW_ERROR"
						createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
							"item":   item,
							"method": "rollbackCustomerWithdrawFromLuckyThai",
							"error":  err.Error(),
						})
						if _, err = s.repo.CreateLuckyThaiWebhook(createBody2); err != nil {
							log.Println("Error rollbackCustomerWithdrawFromLuckyThai.CreateLuckyThaiWebhook", err)
						}
					}
				}
			} else {
				log.Println("ApproveDbLuckyThaiOrder.WithoutItemUpdated", helper.StructJson(item))
			}
		}
	}
	return insertId, nil
}

func (s paymentGatewayService) GetLuckyThaiWebDepositAccount() (*model.LuckyThaiCustomerDepositInfo, error) {

	var result model.LuckyThaiCustomerDepositInfo

	isPaymenGatewayEnabled := os.Getenv("PAYGATE_LUCKY_ENABLED")
	if isPaymenGatewayEnabled != "1" {
		return nil, nil
	}

	result.Name = "Thai Payment"
	result.ShopName = os.Getenv("PAYGATE_LUCKY_DISPLAY_NAME")
	result.MinAmount = 100
	result.MaxAmount = 50000

	return &result, nil
}

func (s paymentGatewayService) GetLuckyThaiAccount() (*model.PaygateAccountResponse, error) {

	pgAccount, err := s.repo.GetPaygateAccountByProviderId(model.PAYGATE_MERCHANT_ID_LUCKYTHAI)
	if err != nil {
		return nil, internalServerError(err)
	}
	return pgAccount, nil
}

func (s paymentGatewayService) CreateLuckyThaiDeposit(req model.LuckyThaiDepositCreateRequest) (*model.LuckyThaiOrderWebResponse, error) {

	var result model.LuckyThaiOrderWebResponse

	pgAccount, err := s.GetLuckyThaiAccount()
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}

	var createBody model.LuckyThaiOrderCreateBody
	createBody.UserId = req.UserId
	createBody.OrderTypeId = model.PAYGATE_ORDER_TYPE_DEPOSIT
	createBody.OrderNo = "" // Autogen
	createBody.Amount = req.Amount
	createBody.TransactionStatus = "PENDING"
	insertId, err := s.repo.CreateDbLuckyThaiOrder(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	// reget Order
	pendingOrder, err := s.repo.GetDbLuckyThaiOrderById(*insertId)
	if err != nil {
		// SET AS ERROR
		remark := "Error GetDbLuckyThaiOrderById, " + err.Error()
		if err := s.repo.UpdateDbLuckyThaiOrderError(*insertId, remark); err != nil {
			log.Println("CreateLuckyThaiDeposit.UpdateDbLuckyThaiOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// Create LUCKYTH Order
	var remoteRequest model.LuckyThaiDepositCreateRemoteRequest
	remoteRequest.ReferenceNo = pendingOrder.OrderNo
	remoteRequest.Amount = pendingOrder.Amount
	remoteResp, err := s.repo.LuckyThaiDeposit(*pgAccount, remoteRequest)
	if err != nil {
		// SET AS ERROR
		remark := "Error LuckyThaiDeposit, " + err.Error()
		if err := s.repo.UpdateDbLuckyThaiOrderError(*insertId, remark); err != nil {
			log.Println("LuckyThaiDeposit.UpdateDbLuckyThaiOrderError", err)
		}
		// SysLog
		if _, err := s.repo.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "CreateLuckyThaiDeposit.LuckyThaiDeposit",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(remoteRequest),
			JsonResponse: helper.StructJson(map[string]interface{}{"error": err.Error()}),
		}); err != nil {
			log.Println("CreateLuckyThaiDeposit.CreateSysLog", err)
		}
		return nil, internalServerError(err)
	}

	fmt.Println("LuckyThaiDeposit.remoteResp", helper.StructJson(remoteResp))

	// if remoteResp.Status.Code != "000" {
	// 	// SET AS ERROR
	// 	remark := "Error remoteResp.Status.Code != 000, remoteResp:" + helper.StructJson(remoteResp)
	// 	if err := s.repo.UpdateDbLuckyThaiOrderError(*insertId, remark); err != nil {
	// 		log.Println("CreateLuckyThaiDeposit.UpdateDbLuckyThaiOrderError", err)
	// 	}
	// 	return nil, internalServerError(fmt.Errorf(remoteResp.Status.Message))
	// }

	// OrderNo       string  `json:"orderNo"`
	// ReceiveAddr   string  `json:"receiveAddr"`
	// ChainName     string  `json:"chainName"`
	// CoinUnit      string  `json:"coinUnit"`
	// RequestAmount float64 `json:"requestAmount"`
	// Status        string  `json:"status"`
	// PayUrl        string  `json:"payUrl"`
	// Hrefbackurl   string  `json:"hrefbackurl"`
	// Sign          string  `json:"sign"`
	// resp {
	// 	"success": true,
	// 	"code": 200,
	// 	"data": {
	// 		"orderNo": "PBANKTHB000749T1713368688340RyjVi6",
	// 		"receiveAddr": "https://ppypayment.xyzonline.app/oYDYJMKfg4GaYHLNS1Gt?redirectionUrl=http://127.0.0.1:8080",
	// 		"chainName": "BANK",
	// 		"coinUnit": "THB",
	// 		"requestAmount": 121,
	// 		"status": "CREATE",
	// 		"payUrl": "https://ppypayment.xyzonline.app/oYDYJMKfg4GaYHLNS1Gt?redirectionUrl=http://127.0.0.1:8080",
	// 		"hrefbackurl": "https://api-master.cbgame88.com/api/webhook/luckyth/callback",
	// 		"sign": "870d8de4d17e47adb5460f7180174226"
	// 	}
	// }

	// onCreate Success
	var updateBody model.LuckyThaiOrderUpdateBody
	updateBody.TransactionNo = remoteResp.Data.OrderNo
	updateBody.TransactionDate = time.Now()
	updateBody.TransactionStatus = "WAIT_PAYMENT"
	updateBody.QrPromptpay = remoteResp.Data.PayUrl // Just remark
	if err := s.repo.UpdateDbLuckyThaiOrder(*insertId, updateBody); err != nil {
		// SET AS ERROR
		remark := "Error UpdateDbLuckyThaiOrder, " + err.Error()
		if err := s.repo.UpdateDbLuckyThaiOrderError(*insertId, remark); err != nil {
			log.Println("CreateLuckyThaiDeposit.UpdateDbLuckyThaiOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// remove all parameters after ?
	// "payUrl":"https://pay.luckyth.org/pay?orderNo=PI8B582024041818EDKZYZRPD","hrefbackurl":"https://dev-web.cbgame88.com","sign":"7c157ec9b4109ccd839e11b1d7ad16ac"}}
	// "payUrl": "https://ppypayment.xyzonline.app/oYDYJMKfg4GaYHLNS1Gt?redirectionUrl=http://127.0.0.1:8080",
	// u, err := url.Parse(remoteResp.Data.PayUrl)
	// if err != nil {
	// 	return nil, internalServerError(err)
	// }
	// u.RawQuery = ""
	// paymentUrl := u.String()

	paymentUrl := remoteResp.Data.PayUrl
	// CASE1 : replace redirectionUrl=http://127.0.0.1:8080 with redirectionUrl=WEB_DOMAIN
	// CASE2 : "paymentUrl": "https://ppypayment.xyzonline.app/aZV5nL4Zi4c2OnTxWeFu?redirectionUrl=https://pxcb.luckythb.xyz/payment/proxy/redirect?orderNoPBANKTHB000798T1714116537498RtUrPB&xyz=abc",

	frontUrl := "https://" + os.Getenv("DOMAIN_NAME")
	domain := os.Getenv("WEB_DOMAIN")
	if frontUrl != "" {
		frontUrl = "https://" + domain
	}

	// Replace value of redirectionUrl={any} to redirectionUrl=WEB_DOMAIN
	// paymentUrl = strings.Replace(paymentUrl, "redirectionUrl=http://127.0.0.1:8080", "redirectionUrl="+domain, -1)
	// if strings.Contains(paymentUrl, "redirectionUrl=") {
	paymentUrl = strings.Replace(paymentUrl, "redirectionUrl=", "redirectionUrl="+frontUrl+"?old=", -1)

	// ===========================================================================================
	waitPayOrder, err := s.repo.GetDbLuckyThaiOrderById(*insertId)
	if err != nil {
		return nil, internalServerError(err)
	}

	result.UserId = waitPayOrder.UserId
	result.OrderNo = waitPayOrder.OrderNo
	result.Amount = waitPayOrder.Amount
	result.TransactionStatus = *waitPayOrder.TransactionStatus
	result.PaymentUrl = paymentUrl
	result.CreatedAt = waitPayOrder.CreatedAt

	return &result, nil
}

func (s paymentGatewayService) CreateLuckyThaiWithdraw(req model.LuckyThaiWithdrawCreateRequest) (*int64, error) {

	pgAccount, err := s.GetLuckyThaiAccount()
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}
	if !pgAccount.IsWithdrawEnabled {
		return nil, badRequest("PAYGATE_WITHDRAW_NOT_ENABLED")
	}
	sandBoxMode := false
	ginMode := os.Getenv("GIN_MODE")
	if ginMode == "debug" && pgAccount.ShopName == "DEV_DEMO" {
		sandBoxMode = true
	}

	user, err := s.repo.GetUserBankDetailById(req.UserId)
	if err != nil {
		return nil, badRequest("INVALID_USER")
	}
	if user == nil || user.BankAccount == "" || user.BankCode == "" {
		return nil, badRequest("INVALID_USER_ACCOUNT")
	}

	// Bank Abbreviations = user.BankCode Support ?
	// LAST UPDATE [2024-05-07]
	// 1.KBANK			Kasikorn Bank
	// 2.SCB			SCB
	// 3.BBL			Bangkok Bank
	// 4.BAY			BANK OF AYUDHYA
	// 5.KTB			KTB
	// 6.TTB			TMB THANACHART BANK
	// 7.GSB=>GOV		GOVERNMENT SAVINGS BANK
	// 8.BAAC			BANK FOR AGRICULTURE AND AGRICULTURAL COOPERATIVES
	// 9.KK				KIATNAKIN PHATRA BANK
	// 10.GHB			THE GOVERNMENT HOUSING BANK
	// 11.UOB=>UOBT		UNITED OVERSEAS BANK (THAI)
	// 12.LH=>LHBANK	LAND AND HOUSES BANK
	// 13.CIMB			CIMB THAI BANK
	// 14.
	// 15.ICBC			INDUSTRIAL AND COMMERCIAL BANK OF CHINA (THAI)
	// 16.ISBT			ISLAMIC BANK OF THAILAND
	// 17.
	// 18.CITI			CITIBANK
	// 19.SCBT			STANDARD CHARTERED BANK (THAI)
	// -.BOC			BANK OF CHINA (THAI)
	// LAST UPDATE [2024-05-07]

	reqBankCode := user.BankCode
	// rename bank code SYS=>LUCKYTH
	if reqBankCode == "BSB" {
		reqBankCode = "GOV"
	} else if reqBankCode == "UOB" {
		reqBankCode = "UOBT"
	} else if reqBankCode == "LH" {
		reqBankCode = "LHBANK"
	}
	acceptBankCodeList := []string{"KBANK", "SCB", "BBL", "BAY", "KTB", "TTB", "GOV", "BAAC", "KK", "GHB", "UOBT", "LHBANK", "CIMB", "ICBC", "ISBT", "CITI", "SCBT", "BOC"}
	if !helper.StringInArray(strings.ToUpper(reqBankCode), acceptBankCodeList) {
		return nil, badRequest("BANK_CODE_NOT_SUPPORT")
	}
	reqAccountNumber := helper.StripAllButNumbers(user.BankAccount)
	if reqAccountNumber == "" {
		return nil, badRequest("INVALID_ACCOUNT_NUMBER")
	}

	// ===========================================================================================
	// CREATE Order
	var createBody model.LuckyThaiOrderCreateBody
	createBody.RefId = &req.RefId
	createBody.UserId = req.UserId
	createBody.OrderTypeId = model.PAYGATE_ORDER_TYPE_WITHDRAW
	createBody.OrderNo = "" // Autogen
	createBody.Amount = req.Amount
	createBody.TransactionStatus = "PENDING"
	insertId, err := s.repo.CreateDbLuckyThaiOrder(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	// reget Order
	pendingOrder, err := s.repo.GetDbLuckyThaiOrderById(*insertId)
	if err != nil {
		// SET AS ERROR
		remark := "Error GetDbLuckyThaiOrderById, " + err.Error()
		if err := s.repo.UpdateDbLuckyThaiOrderError(*insertId, remark); err != nil {
			log.Println("CreateLuckyThaiWithdraw.UpdateDbLuckyThaiOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// Create LUCKYTH Order
	var remoteRequest model.LuckyThaiWithdrawCreateRemoteRequest
	remoteRequest.ReferenceNo = pendingOrder.OrderNo
	remoteRequest.BankCode = reqBankCode
	remoteRequest.AccountNo = reqAccountNumber
	remoteRequest.Accountname = user.Fullname
	remoteRequest.Amount = pendingOrder.Amount
	if sandBoxMode {
		// onCreate Success
		var updateBody model.LuckyThaiOrderUpdateBody
		updateBody.TransactionNo = helper.AlphaNumerics(7)
		updateBody.TransactionDate = time.Now()
		updateBody.TransactionStatus = "WAIT_PAYMENT"
		updateBody.QrPromptpay = "https://dev-admin.cbgame88.com"
		if err := s.repo.UpdateDbLuckyThaiOrder(*insertId, updateBody); err != nil {
			// SET AS ERROR
			remark := "Error UpdateDbLuckyThaiOrder, " + err.Error()
			if err := s.repo.UpdateDbLuckyThaiOrderError(*insertId, remark); err != nil {
				log.Println("CreateLuckyThaiWithdraw.UpdateDbLuckyThaiOrderError", err)
			}
			return nil, internalServerError(err)
		}
		return insertId, nil
	}
	remoteResp, err := s.repo.LuckyThaiWithdraw(*pgAccount, remoteRequest)
	if err != nil {
		// SET AS ERROR
		remark := "Error LuckyThaiWithdraw, " + err.Error()
		if err := s.repo.UpdateDbLuckyThaiOrderError(*insertId, remark); err != nil {
			log.Println("LuckyThaiWithdraw.UpdateDbLuckyThaiOrderError", err)
		}
		// SysLog
		if _, err := s.repo.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "CreateLuckyThaiWithdraw.LuckyThaiWithdraw",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(remoteRequest),
			JsonResponse: helper.StructJson(map[string]interface{}{"error": err.Error()}),
		}); err != nil {
			log.Println("CreateLuckyThaiWithdraw.CreateSysLog", err)
		}
		return nil, internalServerError(err)
	}

	fmt.Println("CreateLuckyThaiWithdraw.remoteResp", helper.StructJson(remoteResp))

	// if remoteResp.Status.Code != "000" {
	// 	// SET AS ERROR
	// 	remark := "Error remoteResp.Status.Code != 000, remoteResp:" + helper.StructJson(remoteResp)
	// 	if err := s.repo.UpdateDbLuckyThaiOrderError(*insertId, remark); err != nil {
	// 		log.Println("CreateLuckyThaiWithdraw.UpdateDbLuckyThaiOrderError", err)
	// 	}
	// 	return nil, internalServerError(fmt.Errorf(remoteResp.Status.Message))
	// }

	// {
	// 	"success": true,
	// 	"code": 200,
	// 	"data": {
	// 		"orderNo": "DFBANKTHB000749T1713432982603RHlAxY",
	// 		"receiveAddr": "kbank|**********|dev 5002|IFSC",
	// 		"chainName": "BANK",
	// 		"coinUnit": "THB",
	// 		"requestAmount": 103,
	// 		"status": "PAYING",
	// 		"payUrl": "",
	// 		"hrefbackurl": "",
	// 		"sign": "fafc2e502ee92954dc3e6ddd59a5213f"
	// 	}
	// }

	// onCreate Success
	var updateBody model.LuckyThaiOrderUpdateBody
	updateBody.TransactionNo = remoteResp.Data.OrderNo
	updateBody.TransactionDate = time.Now()
	updateBody.TransactionStatus = "WAIT_PAYMENT"
	updateBody.QrPromptpay = remoteResp.Data.ReceiveAddr
	if err := s.repo.UpdateDbLuckyThaiOrder(*insertId, updateBody); err != nil {
		// SET AS ERROR
		remark := "Error UpdateDbLuckyThaiOrder, " + err.Error()
		if err := s.repo.UpdateDbLuckyThaiOrderError(*insertId, remark); err != nil {
			log.Println("CreateLuckyThaiWithdraw.UpdateDbLuckyThaiOrderError", err)
		}
		return nil, internalServerError(err)
	}
	return insertId, nil
}

func (s paymentGatewayService) createCustomerDepositFromLuckyThai(item model.LuckyThaiOrderResponse, remoteResp model.LuckyThaiWebhookResponse) (*int64, error) {

	var externalNoti model.NotifyExternalNotificationRequest
	actionAt := time.Now()
	confirmByUserId := int64(0)

	depositAmount := item.Amount

	user, err := s.repo.GetUserBankDetailById(item.UserId)
	if err != nil {
		log.Println("createCustomerDeposit.GetUserBankDetailByMemberCode", err)
		return nil, internalServerError(err)
	}

	var promotionWebUserId int64
	if getPromotionWebUser, err := s.repo.GetDepositCurrentProcessingUserPromotion(item.UserId); err != nil {
		log.Println("createCustomerDeposit.GetDepositCurrentProcessingUserPromotion", err)
	} else if getPromotionWebUser != nil {
		promotionWebUserId = getPromotionWebUser.Id
	}

	// MemberCode
	if user.MemberCode == "" {
		memberCode, err := GenUniqueUserMemberCode(repository.NewUserRepository(s.sharedDb), user.Id)
		if err != nil {
			log.Println("createCustomerDeposit.GenUniqueUserMemberCode", err)
			return nil, internalServerError(err)
		}
		user.MemberCode = *memberCode
	}

	// layer — 2024-04-02 at 10:06 AM
	// ถ้าแก้ตามนี้แล้วน่าจะไม่เจอ ถ้าเคสกันซ้ำเราครอบคลุม
	// @Dunk @TULA
	// กันซ้ำคือ heng ไปซ้ำกับแอดมินเติมมือ
	// ----
	// เงื่อนไขกันซ้ำ
	// 1 หารายการฝากสำเร็จล่าสุดของยูสนั้น
	// 2 ถ้ามีรายการล่าสุดที่เป็นเติมมือ
	// -เช็คเวลาเติม ต้องมากกว่าเวลาโอนในสลิป
	// -ยอดเงินเท่ากัน
	// **ถ้าเข้าเงื่อนไขทั้งหมด คือแสดงว่า webhook เข้าทีหลังแอดมินเติมมือ
	// ต้อง bypass ไม่ให้เติมอีก และอัพเดท status ว่าจับคู่ได่แล้ว
	// ---
	// วิธีนี้น่าจะใช้ได้กับทุก webhook (heng, fastbank) ที่มีเวลาโอนสลิปมาให้
	duplicateRemark := "INIT"
	hasDuplicateDeposit := false
	manualDeposit, err := s.repo.GetLastestBankAdminManualDepositTransaction(user.Id)
	if err != nil {
		if err.Error() != recordNotFound {
			log.Println("createCustomerDeposit.GetLastestBankAdminManualDepositTransaction", err)
			return nil, internalServerError(err)
		}
		// NOT_FOUND = ถ้าไม่เจอ ให้ทำต่อ
		duplicateRemark = "OK_NO_MANUAL_DEPOSIT"
	}
	if manualDeposit != nil && item.TransactionDate != nil {
		if manualDeposit.TransferAt.After(*item.TransactionDate) {
			// ถ้าเวลาเติมมือมากกว่าเวลาโอนในสลิป
			if manualDeposit.CreditAmount == depositAmount {
				// ถ้ายอดเงินเท่ากัน
				duplicateRemark = "STOP_MANUAL_DEPOSIT_AFTER_AND_SAME_AMOUNT"
				hasDuplicateDeposit = true
			} else {
				duplicateRemark = "OK_MANUAL_DEPOSIT_AMOUNT_NOT_MATCH"
			}
		} else {
			duplicateRemark = "OK_MANUAL_DEPOSIT_BEFORE_WITHDRAW"
		}
	}
	if duplicateRemark != "INIT" {
		// WebhookLog
		var createBody2 model.LuckyThaiWebhookCreateBody
		createBody2.Name = "LUCKYTH_DEPOSIT_DUPLICATE_CHECKER"
		createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
			"item":                item,
			"method":              "createCustomerDeposit",
			"hasDuplicateDeposit": hasDuplicateDeposit,
			"error":               duplicateRemark,
			"manualDeposit":       manualDeposit,
		})
		if _, err = s.repo.CreateLuckyThaiWebhook(createBody2); err != nil {
			log.Println("Error CreateLuckyThaiWebhook.CreateLuckyThaiWebhook", err)
		}
		if hasDuplicateDeposit {
			return nil, internalServerError(fmt.Errorf("DUPLICATE_DEPOSIT"))
		}
	}

	pgAccount, err := s.repo.GetPaygateAccountByProviderId(model.PAYGATE_MERCHANT_ID_LUCKYTHAI)
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}

	// ===================================================

	// Affiliate + Alliance Income
	member, err := s.repo.GetMemberById(user.Id)
	if err != nil {
		return nil, internalServerError(err)
	}
	if member.UserTypeName == "NONE" {
		// ถ้าฝากครั้งแรก จะได้เป็น AFFILIATE + REFER COMMISION
		if err := UserFirstDepositCommission(repository.NewAccountingRepository(s.sharedDb), *member, depositAmount); err != nil {
			log.Println("CreateFristTimeDepositRecord.UserFirstDepositCommission.ERROR", err)
			return nil, internalServerError(err)
		}
	}

	isAutoDeposit := true // Auto ฝาก นับตั้งแต่ได้รับยอดฝาก จนได้ยืนยันเครดิต

	// CREATE BANK TRANSACTION
	var createBankTransaction model.BankTransactionCreateBody
	createBankTransaction.MemberCode = user.MemberCode
	createBankTransaction.UserId = user.Id
	// createBankTransaction.StatementId = 0
	createBankTransaction.TransactionTypeId = model.TRANSACTION_TYPE_DEPOSIT
	createBankTransaction.FromAccountName = &remoteResp.Payer // empty?
	createBankTransaction.ToAccountName = &pgAccount.TypeName
	createBankTransaction.ToAccountNumber = &pgAccount.ShopName
	createBankTransaction.PromotionId = &promotionWebUserId
	createBankTransaction.CreditAmount = depositAmount
	// createBankTransaction.BonusAmount = 0
	// createBankTransaction.BonusReason = nil
	createBankTransaction.DepositChannel = "LUCKYTH PAYMENT GATEWAY"
	createBankTransaction.TransactionStatusId = model.TRANS_STATUS_PENDING
	createBankTransaction.CreatedByAdminId = 0
	createBankTransaction.IsAutoCredit = isAutoDeposit
	createBankTransaction.TransferAt = &actionAt
	// create transaction
	transId, err := s.repo.InsertBankTransaction(createBankTransaction)
	if err != nil {
		log.Println("createCustomerDeposit.InsertBankTransaction", err)
		return nil, internalServerError(err)
	}

	// var actionStatement model.BankStatementMatchRequest
	// actionStatement.StatementId = *bankstatementId
	// actionStatement.UserId = user.Id
	// actionStatement.ConfirmedAt = time
	// actionStatement.ConfirmedByAdminId = &req.CreateByUserId
	// if err := s.serviceAccounting.SetStatementOwnerMatched(*bankstatementId, actionStatement, model.USE_ENDING_NOTI); err != nil {
	// 	log.Println("createCustomerDeposit.SetStatementOwnerMatched", err)
	// 	return nil, internalServerError(err)
	// }

	// DECREASE FASTBANK CREDIT 2.FROM create auto DEPOSIT
	// if err := s.repoAccounting.DecreaseFastbankCredit(1); err != nil {
	// 	log.Println("createCustomerDeposit.DECREASE_FASTBANK_CREDIT_ERROR", err)
	// }

	var confirmDeposit model.BankConfirmDepositRequest
	confirmDeposit.TransferAt = &actionAt
	confirmDeposit.ConfirmedAt = actionAt
	confirmDeposit.ConfirmedByAdminId = &confirmByUserId
	if err := confirmDepositTransaction(s.repo, *transId, confirmDeposit); err != nil {
		log.Println("createCustomerDeposit.confirmDepositTransaction", err)
		return nil, internalServerError(err)
	}

	isFirstDeposit := s.repo.IsFirstDeposit(user.Id)
	if isFirstDeposit {
		var bonusReq model.UserFirstDepositCreateRequest
		bonusReq.UserId = user.Id
		bonusReq.TransactionId = transId
		bonusReq.TransferAt = actionAt
		bonusReq.Amount = depositAmount
		bonusReq.Remark = "CreateBankStatementFromWebhookAndAutoSmsMode"
		if err := SetFirstDepositBonus(repository.NewAccountingRepository(s.sharedDb), isFirstDeposit, bonusReq); err != nil {
			webhookLogMessage := fmt.Sprintf("CreateBankStatementFromWebhookAndAutoSmsMode.userFirstDepositBonus, ERROR: %s", err)
			log.Println("SetFirstDepositBonus", webhookLogMessage)
			// return nil, errors.New(webhookLogMessage)
		}
	}

	// [USER_CREDIT]
	var userCreditReq model.UserTransactionCreateRequest
	userCreditReq.UserId = user.Id
	userCreditReq.RefId = transId
	userCreditReq.TransferAt = &actionAt
	userCreditReq.PromotionId = &promotionWebUserId
	userCreditReq.TypeId = model.CREDIT_TYPE_DEPOSIT
	// userCreditReq.AccountId = &bankAccount.Id
	userCreditReq.Detail = "LUCKYTH PAYMENT GATEWAY"
	userCreditReq.Amount = depositAmount
	userCreditReq.CreateBy = &confirmByUserId
	userCreditReq.ConfirmBy = &confirmByUserId
	userCreditReq.IsAdjustAuto = isAutoDeposit
	userCreditReq.PaymentMerchatId = &pgAccount.ProviderId
	if agentResp, err := s.repo.IncreaseUserCredit(userCreditReq); err != nil {
		log.Println("createCustomerDeposit.IncreaseUserCredit", err)
		return nil, internalServerError(err)
	} else {
		// AGENT_SUCCESS
		if err := s.repo.UpdateDepositTransactionStatusFromAgent(*transId, *agentResp); err != nil {
			log.Println("createCustomerDeposit.UpdateDeporsitTransactionStatusFromAgent", err)
		}
	}

	// [Lucky Wheel] ตอนฝากละได้โบนัส
	var luckyWheelBody model.ActivityLuckyWheelRoundUserRequest
	luckyWheelBody.UserId = user.Id
	luckyWheelBody.ConditionId = model.ACTIVITY_LUCKY_WHEEL_SETTING_CONDITION_DEPOSIT
	luckyWheelBody.ConditionAmount = depositAmount
	if err := s.activityLuckyWheelService.CreateRoundActivityLuckyWheel(luckyWheelBody); err != nil {
		log.Println("createCustomerDeposit.CreateRoundActivityLuckyWheel", err)
	}

	// [TIER]
	if err := s.repo.IncreaseUserTierDepositAmount(user.Id, depositAmount); err != nil {
		log.Println("MatchStatementOwner.IncreaseUserTierDepositAmount", err)
	}

	var checkUserPromotionBody model.CheckUserPromotionBody
	checkUserPromotionBody.UserId = user.Id
	_, err = s.promotionWebService.CheckUserPromotion(checkUserPromotionBody)
	if err != nil {
		log.Println("createCustomerDeposit.CheckUserPromotion", err)
	}
	// [turnOver] ถ้ามีการฝากเงิน ให้สร้าง TurnOver
	if err := CreateTurnOverFromSuccessDeposit(repository.NewTurnoverRepository(s.sharedDb), user.Id, depositAmount, *transId); err != nil {
		log.Println("CreateBankStatementFromWebhookAndAuto.CreateTurnOverFromSuccessDeposit", err)
	}
	// ===================================================

	// [ notify]
	externalNoti.TypeNotify = model.IsDepositAfterCredit
	externalNoti.TransId = transId
	externalNoti.Amount = depositAmount
	externalNoti.MemberCode = user.MemberCode
	externalNoti.UserCredit = user.Credit + depositAmount
	externalNoti.ConfirmedByAdminId = 0
	externalNoti.TransferDateTime = time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02 15:04:05")
	externalNoti.ActionTime = time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02 15:04:05")
	endTime := time.Now()
	elapsed := endTime.Sub(actionAt)
	elapsedSeconds := elapsed.Seconds()
	timeElapsed := fmt.Sprintf("%.2f", elapsedSeconds)
	externalNoti.TimerCounter = timeElapsed

	externalNoti.WebScoket.UserID = user.Id
	externalNoti.WebScoket.Amount = depositAmount
	externalNoti.WebScoket.MemberCode = user.MemberCode
	externalNoti.WebScoket.AlertType = "DEPOSIT"
	if err := s.repo.UpdateAutoProcessTimer(timeElapsed, *transId); err != nil {
		return nil, nil
	}
	if err := s.notiService.ExternalNotification(externalNoti); err != nil {
		log.Println("FailedNotify", err)
	}
	return nil, nil
}

func approveCustomerWithdrawFromLuckyThai(repo repository.PaymentGatewayRepository, transId int64) (*int64, error) {

	withdrawTrans, err := repo.GetBankTransactionById(transId)
	if err != nil {
		log.Println("approveCustomerWithdrawFromLuckyThai.GetUserBankDetailById", err)
		return nil, internalServerError(err)
	}

	// ============================= ON_SUCCESS =================================
	if withdrawTrans.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		// [create transaction action success]
		var createConfirm model.CreateSuccessTransferWithdrawRequest
		createConfirm.TransactionId = withdrawTrans.Id
		createConfirm.ConfirmedAt = time.Now()
		var setIdAuto int64 = 0
		createConfirm.ConfirmedByAdminId = &setIdAuto
		if _, err := CreateSuccessTransferWithdraw(repository.NewBankingRepository(repo.GetDb()), createConfirm); err != nil {
			log.Println("approveCustomerWithdrawFromLuckyThai.CreateSuccessTransferWithdraw", err)
			return nil, nil
		}
		// [Show Withdraw USER_TRANSACTION]
		var showUserTrans model.UserTransactionShowUpdate
		showUserTrans.TransactionId = withdrawTrans.Id
		showUserTrans.ConfirmedAt = &createConfirm.ConfirmedAt
		showUserTrans.ConfirmAdminId = createConfirm.ConfirmedByAdminId
		if err := repo.ShowUserTransaction(showUserTrans); err != nil {
			log.Println("approveCustomerWithdrawFromLuckyThai.ShowUserTransaction", err)
			return nil, nil
		}
		// [update transaction status]
		secondUsed := "0.0" // timing
		if withdrawTrans.IsAutoCredit && withdrawTrans.TransferAt != nil {
			seconds := time.Now().UTC().Sub(withdrawTrans.TransferAt.UTC()).Seconds()
			secondUsed = fmt.Sprintf("%.2f", seconds)
		}
		if err := repo.UpdateTransactionStatusTransferingToSuccess(withdrawTrans.Id, secondUsed); err != nil {
			log.Println("approveCustomerWithdrawFromLuckyThai.UpdateTransactionStatusTransferingToSuccess", err)
			return nil, internalServerError(err)
		}

		// [notify SUCCESS]
		var externalNoti model.NotifyExternalNotificationRequest
		notiRepo := repository.NewNotificationRepository(repo.GetDb())
		externalNoti.TypeNotify = model.IsWithdrawalCreditSuccess
		externalNoti.TransId = &transId
		externalNoti.Amount = withdrawTrans.CreditAmount
		externalNoti.MemberCode = withdrawTrans.MemberCode
		externalNoti.UserCredit = withdrawTrans.CreditAmount
		externalNoti.ConfirmedByAdminId = 0
		externalNoti.TimerCounter = secondUsed
		externalNoti.TransferDateTime = withdrawTrans.TransferAt.Add(7 * time.Hour).Format("2006-01-02 15:04:05")
		externalNoti.ActionTime = time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02 15:04:05")
		if err := ExternalNotification(notiRepo, externalNoti); err != nil {
			log.Println("FailedNotify", err)
		}

	}
	return nil, nil
}

func rollbackCustomerWithdrawFromLuckyThai(repo repository.PaymentGatewayRepository, transId int64) (*int64, error) {

	withdrawTrans, err := repo.GetBankTransactionById(transId)
	if err != nil {
		log.Println("rollbackCustomerWithdrawFromLuckyThai.GetBankTransactionById", err)
		return nil, internalServerError(err)
	}

	// ============================= ON_SUCCESS =================================
	if withdrawTrans.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		// [update transaction status]
		if err := repo.RollbackTransactionStatusTransferingToConfirmed(withdrawTrans.Id); err != nil {
			log.Println("approveCustomerWithdrawFromLuckyThai.RollbackTransactionStatusTransferingToConfirmed", err)
			return nil, internalServerError(err)
		}
	}
	return nil, nil
}

func CheckTransferingWithdraw(repo repository.PaymentGatewayRepository, transId int64) (model.WithdrawCheckTransferingResponse, error) {

	// CallBack from Withdraw List Command
	var result model.WithdrawCheckTransferingResponse

	result.TransactionId = transId
	result.Status = "TRANSFERING"

	// pgAccount, err := GetPaygateAccountById
	// if err != nil {
	// 	return result, internalServerError(err)
	// }
	// if pgAccount.Status != "ACTIVE" {
	// 	return result, badRequest("PAYGATE_NOT_ACTIVE")
	// }

	// // Only LuckyThai
	// if pgAccount.MerchantType == model.PAYGATE_MERCHANT_TYPE_LUCKYTHAI {
	// 	pgAccount, err := repo.GetPaygateAccountById(model.PAYGATE_MERCHANT_ID_LUCKYTHAI)
	// 	if err != nil {
	// 		return result, internalServerError(err)
	// 	}
	// 	order, err := repo.GetDbLuckyThaiOrderByRefId(transId)
	// 	if err != nil {
	// 		return result, badRequest("INVALID_TRANSACTION")
	// 	}
	// 	orderStatus, err := repo.LuckyThaiGetOrder(*pgAccount, order.OrderNo)
	// 	if err != nil {
	// 		return result, badRequest("INVALID_TRANSACTION")
	// 	}
	// 	checkResult := orderStatus.Data.Status
	// 	if checkResult == "PAID" {
	// 		result.Status = "SUCCESS"
	// 		if _, err := approveCustomerWithdrawFromLuckyThai(repo, transId); err != nil {
	// 			return result, internalServerError(err)
	// 		}
	// 	} else if checkResult == "CANCEL" {
	// 		result.Status = "FAILED"
	// 		if _, err := rollbackCustomerWithdrawFromLuckyThai(repo, transId); err != nil {
	// 			return result, internalServerError(err)
	// 		}
	// 	} else {
	// 		result.Status = "TRANSFERING"
	// 	}
	// } else if pgAccount.MerchantType == model.PAYGATE_MERCHANT_TYPE_PAPAYAPAY {
	// 	// [********] ไม่มี Order ให้ GET จาก PapayaPay
	// 	result.Status = "TRANSFERING"
	// } else if pgAccount.MerchantType == model.PAYGATE_MERCHANT_TYPE_PAYONEX {
	// 	// [********] ไม่มี Order ให้ GET จาก Payonex
	// 	result.Status = "TRANSFERING"
	// } else if pgAccount.MerchantType == model.PAYGATE_MERCHANT_TYPE_JBPAY {
	// 	// [********] ไม่มี Order ให้ GET จาก Jbpay
	// 	result.Status = "TRANSFERING"
	// } else if pgAccount.MerchantType == model.PAYGATE_MERCHANT_TYPE_POMPAY {
	// 	// [********] ไม่มี Order ให้ GET จาก Pompay
	// 	result.Status = "TRANSFERING"
	// } else if pgAccount.MerchantType == model.PAYGATE_MERCHANT_TYPE_PAYMENTCO {
	// 	// [********] ไม่มี Order ให้ GET จาก Pompay
	// 	result.Status = "TRANSFERING"
	// } else if pgAccount.MerchantType == model.PAYGATE_MERCHANT_TYPE_ZAPPAY {
	// 	// [********] ไม่มี Order ให้ GET จาก Zappay
	// 	result.Status = "TRANSFERING"
	// } else if pgAccount.MerchantType == model.PAYGATE_MERCHANT_TYPE_ONEPAY {
	// 	// [********] ไม่มี Order ให้ GET จาก ONEPAY
	// 	result.Status = "TRANSFERING"
	// } else if pgAccount.MerchantType == model.PAYGATE_MERCHANT_TYPE_FLASHPAY {
	// 	// [********] ไม่มี Order ให้ GET จาก FLASHPAY
	// 	result.Status = "TRANSFERING"
	// } else if pgAccount.MerchantType == model.PAYGATE_MERCHANT_TYPE_BIZPAY {
	// 	// [********] ไม่มี Order ให้ GET จาก BIZPAY
	// 	result.Status = "TRANSFERING"
	// } else if pgAccount.MerchantType == model.PAYGATE_MERCHANT_TYPE_SUGARPAY {
	// 	// [********] ไม่มี Order ให้ GET จาก SUGARPAY
	// 	result.Status = "TRANSFERING"
	// } else if pgAccount.MerchantType == model.PAYGATE_MERCHANT_TYPE_POSTMANPAY {
	// 	// [********] ไม่มี Order ให้ GET จาก POSTMANPAY
	// 	result.Status = "TRANSFERING"

	return result, nil
}

func (s paymentGatewayService) LuckyThaiCheckBalance() (*model.LuckyThaiCheckBalanceRemoteResponse, error) {

	pgAccount, err := s.GetLuckyThaiAccount()
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}

	result, err := s.repo.LuckyThaiCheckBalance(*pgAccount)
	if err != nil {
		return nil, internalServerError(err)
	}
	return result, nil
}

func (s paymentGatewayService) CreateSystemLog(name string, req interface{}) error {

	// [SYSLOG] HACKED
	_, err := s.repo.CreateSystemLog(model.SystemLogCreateBody{
		Name:   name,
		Status: "HACKING",
		JsonRequest: helper.StructJson(map[string]interface{}{
			"req": req,
		}),
	})
	if err != nil {
		log.Println(name, "CreateSystemLog.ERROR", err.Error())
	}
	return nil
}

// HENG-REPORT

func (s paymentGatewayService) GetHengOrderReportList(req model.HengOrderReportListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.Pagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	var req2 model.PaygateOrderReportListRequest
	req2.MerchantId = model.PAYGATE_MERCHANT_ID_HENG
	req2.DateType = req.DateType
	req2.FromDate = req.FromDate
	req2.ToDate = req.ToDate
	req2.BankTransactionStatus = req.BankTransactionStatus
	req2.Search = req.Search
	req2.Page = req.Page
	req2.Limit = req.Limit
	req2.SortCol = req.SortCol
	req2.SortAsc = req.SortAsc

	list, total, err := s.repo.GetHengOrderReportList(req2)
	if err != nil {
		return nil, internalServerError(err)
	}
	return &model.SuccessWithPagination{
		List:  list,
		Total: total,
	}, nil
}

func (s paymentGatewayService) GetHengCallbackReportResponse(req model.HengCallbackReportListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.Pagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	list, total, err := s.repo.GetHengCallbackReportResponse(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	return &model.SuccessWithPagination{
		List:  list,
		Total: total,
	}, nil
}

func (s paymentGatewayService) GetReportPaymentListOptions() ([]model.SelectOptions, error) {

	// list, err := s.repo.GetReportPaymentListOptions()
	// if err != nil {
	// 	return nil, internalServerError(err)
	// }

	// Fixed List
	list := []model.SelectOptions{
		// {Value: fmt.Sprintf("%d", model.PAYGATE_MERCHANT_ID_HENG), Label: model.PAYGATE_MERCHANT_TYPE_HENG},
		// {Value: fmt.Sprintf("%d", model.PAYGATE_MERCHANT_ID_LUCKYTHAI), Label: model.PAYGATE_MERCHANT_TYPE_LUCKYTHAI},
		// {Value: fmt.Sprintf("%d", model.PAYGATE_MERCHANT_ID_PAPAYAPAY), Label: model.PAYGATE_MERCHANT_TYPE_PAPAYAPAY},
		{Value: fmt.Sprintf("%d", model.PAYGATE_MERCHANT_ID_PAYONEX), Label: model.PAYGATE_MERCHANT_TYPE_PAYONEX, Id: model.PAYGATE_MERCHANT_ID_PAYONEX},
		{Value: fmt.Sprintf("%d", model.PAYGATE_MERCHANT_ID_JBPAY), Label: model.PAYGATE_MERCHANT_TYPE_JBPAY, Id: model.PAYGATE_MERCHANT_ID_JBPAY},
		{Value: fmt.Sprintf("%d", model.PAYGATE_MERCHANT_ID_POMPAY), Label: model.PAYGATE_MERCHANT_TYPE_POMPAY, Id: model.PAYGATE_MERCHANT_ID_POMPAY},
		{Value: fmt.Sprintf("%d", model.PAYGATE_MERCHANT_ID_PAYMENTCO), Label: model.PAYGATE_MERCHANT_TYPE_PAYMENTCO, Id: model.PAYGATE_MERCHANT_ID_PAYMENTCO},
		{Value: fmt.Sprintf("%d", model.PAYGATE_MERCHANT_ID_ZAPPAY), Label: model.PAYGATE_MERCHANT_TYPE_ZAPPAY, Id: model.PAYGATE_MERCHANT_ID_ZAPPAY},
		{Value: fmt.Sprintf("%d", model.PAYGATE_MERCHANT_ID_ONEPAY), Label: model.PAYGATE_MERCHANT_TYPE_ONEPAY, Id: model.PAYGATE_MERCHANT_ID_ONEPAY},
		{Value: fmt.Sprintf("%d", model.PAYGATE_MERCHANT_ID_FLASHPAY), Label: model.PAYGATE_MERCHANT_TYPE_FLASHPAY, Id: model.PAYGATE_MERCHANT_ID_FLASHPAY},
		{Value: fmt.Sprintf("%d", model.PAYGATE_MERCHANT_ID_BIZPAY), Label: model.PAYGATE_MERCHANT_TYPE_BIZPAY, Id: model.PAYGATE_MERCHANT_ID_BIZPAY},
		{Value: fmt.Sprintf("%d", model.PAYGATE_MERCHANT_ID_SUGARPAY), Label: model.PAYGATE_MERCHANT_TYPE_SUGARPAY, Id: model.PAYGATE_MERCHANT_ID_SUGARPAY},
		{Value: fmt.Sprintf("%d", model.PAYGATE_MERCHANT_ID_ZMANPAY), Label: model.PAYGATE_MERCHANT_TYPE_ZMANPAY, Id: model.PAYGATE_MERCHANT_ID_ZMANPAY},
		{Value: fmt.Sprintf("%d", model.PAYGATE_MERCHANT_ID_POSTMANPAY), Label: model.PAYGATE_MERCHANT_TYPE_POSTMANPAY, Id: model.PAYGATE_MERCHANT_ID_POSTMANPAY},
		{Value: fmt.Sprintf("%d", model.PAYGATE_MERCHANT_ID_MAZEPAY), Label: model.PAYGATE_MERCHANT_TYPE_MAZEPAY, Id: model.PAYGATE_MERCHANT_ID_MAZEPAY},
		{Value: fmt.Sprintf("%d", model.PAYGATE_MERCHANT_ID_MEEPAY), Label: model.PAYGATE_MERCHANT_TYPE_MEEPAY, Id: model.PAYGATE_MERCHANT_ID_MEEPAY},
	}

	return list, nil
}

func (s paymentGatewayService) GetPaygateOrderReportList(req model.PaygateOrderReportListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.Pagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	if req.MerchantId == model.PAYGATE_MERCHANT_ID_HENG {
		list, total, err := s.repo.GetHengOrderReportList(req)
		if err != nil {
			return nil, internalServerError(err)
		}
		return &model.SuccessWithPagination{
			List:  list,
			Total: total,
		}, nil
	} else if req.MerchantId == model.PAYGATE_MERCHANT_ID_LUCKYTHAI {
		// list, total, err := s.repo.GetHengOrderReportList(req)
		// if err != nil {
		// 	return nil, internalServerError(err)
		// }
		// return &model.SuccessWithPagination{
		// 	List:  list,
		// 	Total: total,
		// }, nil
	} else if req.MerchantId == model.PAYGATE_MERCHANT_ID_PAPAYAPAY {
		// list, total, err := s.repo.GetHengOrderReportList(req)
		// if err != nil {
		// 	return nil, internalServerError(err)
		// }
		// return &model.SuccessWithPagination{
		// 	List:  list,
		// 	Total: total,
		// }, nil
	} else if req.MerchantId == model.PAYGATE_MERCHANT_ID_PAYONEX {
		list, total, err := s.repo.GetPayonexOrderReportList(req)
		if err != nil {
			return nil, internalServerError(err)
		}
		return &model.SuccessWithPagination{
			List:  list,
			Total: total,
		}, nil
	} else if req.MerchantId == model.PAYGATE_MERCHANT_ID_JBPAY {
		list, total, err := s.repo.GetJbpayOrderReportList(req)
		if err != nil {
			return nil, internalServerError(err)
		}
		return &model.SuccessWithPagination{
			List:  list,
			Total: total,
		}, nil
	} else if req.MerchantId == model.PAYGATE_MERCHANT_ID_POMPAY {
		list, total, err := s.repo.GetPompayOrderReportList(req)
		if err != nil {
			return nil, internalServerError(err)
		}
		return &model.SuccessWithPagination{
			List:  list,
			Total: total,
		}, nil
	} else if req.MerchantId == model.PAYGATE_MERCHANT_ID_PAYMENTCO {
		list, total, err := s.repo.GetPaymentcoOrderReportList(req)
		if err != nil {
			return nil, internalServerError(err)
		}
		return &model.SuccessWithPagination{
			List:  list,
			Total: total,
		}, nil
	} else if req.MerchantId == model.PAYGATE_MERCHANT_ID_ZAPPAY {
		list, total, err := s.repo.GetZappayOrderReportList(req)
		if err != nil {
			return nil, internalServerError(err)
		}
		return &model.SuccessWithPagination{
			List:  list,
			Total: total,
		}, nil
	} else if req.MerchantId == model.PAYGATE_MERCHANT_ID_ONEPAY {
		list, total, err := s.repo.GetOnepayOrderReportList(req)
		if err != nil {
			return nil, internalServerError(err)
		}
		return &model.SuccessWithPagination{
			List:  list,
			Total: total,
		}, nil
	} else if req.MerchantId == model.PAYGATE_MERCHANT_ID_FLASHPAY {
		list, total, err := s.repo.GetFlashpayOrderReportList(req)
		if err != nil {
			return nil, internalServerError(err)
		}
		return &model.SuccessWithPagination{
			List:  list,
			Total: total,
		}, nil
	} else if req.MerchantId == model.PAYGATE_MERCHANT_ID_BIZPAY {
		list, total, err := s.repo.GetBizpayOrderReportList(req)
		if err != nil {
			return nil, internalServerError(err)
		}
		return &model.SuccessWithPagination{
			List:  list,
			Total: total,
		}, nil
	} else if req.MerchantId == model.PAYGATE_MERCHANT_ID_SUGARPAY {
		list, total, err := s.repo.GetSugarpayOrderReportList(req)
		if err != nil {
			return nil, internalServerError(err)
		}
		return &model.SuccessWithPagination{
			List:  list,
			Total: total,
		}, nil
	} else if req.MerchantId == model.PAYGATE_MERCHANT_ID_ZMANPAY {
		list, total, err := s.repo.GetZmanpayOrderReportList(req)
		if err != nil {
			return nil, internalServerError(err)
		}
		return &model.SuccessWithPagination{
			List:  list,
			Total: total,
		}, nil
	} else if req.MerchantId == model.PAYGATE_MERCHANT_ID_POSTMANPAY {
		list, total, err := s.repo.GetPostmanPayOrderReportList(req)
		if err != nil {
			return nil, internalServerError(err)
		}
		return &model.SuccessWithPagination{
			List:  list,
			Total: total,
		}, nil
	} else if req.MerchantId == model.PAYGATE_MERCHANT_ID_MAZEPAY {
		list, total, err := s.repo.GetMazepayOrderReportList(req)
		if err != nil {
			return nil, internalServerError(err)
		}
		return &model.SuccessWithPagination{
			List:  list,
			Total: total,
		}, nil
	} else if req.MerchantId == model.PAYGATE_MERCHANT_ID_MEEPAY {
		list, total, err := s.repo.GetMeepayOrderReportList(req)
		if err != nil {
			return nil, internalServerError(err)
		}
		return &model.SuccessWithPagination{
			List:  list,
			Total: total,
		}, nil
	}

	return nil, badRequest("MERCHANT_NOT_FOUND")
}

func (s paymentGatewayService) CreatePaygateDepositFromOrder(body model.PaygateOrderCreateDepositRequest) error {

	// Service Race Condition by OrderId per minute, ActionKey = OrderId-MINUTE
	actionKey := fmt.Sprintf("CPGDFO%d-%s-%d", body.Id, time.Now().Format("0601021504"), body.Id)
	if _, err := s.repo.GetRaceActionIdByActionKey(actionKey); err != nil {
		if err != gorm.ErrRecordNotFound {
			return badRequest("PLEASE_WAIT_1_MINUTE")
		}
	} else {
		return badRequest("PLEASE_WAIT_1_MINUTE")
	}

	var rcCreateBody model.RaceActionCreateBody
	rcCreateBody.Name = "CreatePaygateDepositFromOrder"
	rcCreateBody.JsonRequest = helper.StructJson(map[string]interface{}{
		"orderId": body.Id,
	})
	rcCreateBody.Status = "PENDING"
	rcCreateBody.ActionKey = actionKey
	rcCreateBody.UnlockAt = time.Now().UTC().Add(time.Second * 1)
	if _, err := s.repo.CreateRaceCondition(rcCreateBody); err != nil {
		return internalServerError(err)
	}

	if body.MerchantId == model.PAYGATE_MERCHANT_ID_HENG {
		// if _, err := createCustomerDepositFromHengOrder(s.repo, body.Id); err != nil {
		// 	log.Println("Error CreatePaygateDepositFromOrder.createCustomerDepositFromHengOrder", err)
		// 	return err
		// }
	} else if body.MerchantId == model.PAYGATE_MERCHANT_ID_LUCKYTHAI {
		// if _, err := createCustomerDepositFromLuckyThaiOrder(s.repo, body.Id); err != nil {
		// 	log.Println("Error CreatePaygateDepositFromOrder.createCustomerDepositFromLuckyThaiOrder", err)
		// }
	} else if body.MerchantId == model.PAYGATE_MERCHANT_ID_PAPAYAPAY {
		// if _, err := createCustomerDepositFromPapayaPayOrder(s.repo, body.Id); err != nil {
		// 	log.Println("Error CreatePaygateDepositFromOrder.createCustomerDepositFromPapayaPayOrder", err)
		// }
	} else if body.MerchantId == model.PAYGATE_MERCHANT_ID_PAYONEX {
		if _, err := CreateCustomerDepositFromPayonexOrder(repository.NewPayonexRepository(s.sharedDb), body.Id, body.AdminId); err != nil {
			log.Println("Error CreatePaygateDepositFromOrder.CreateCustomerDepositFromPayonexOrder", err)
			return err
		}
	} else if body.MerchantId == model.PAYGATE_MERCHANT_ID_JBPAY {
		if _, err := CreateCustomerDepositFromJbpayOrder(repository.NewJbpayRepository(s.sharedDb), body.Id, body.AdminId); err != nil {
			log.Println("Error CreatePaygateDepositFromOrder.CreateCustomerDepositFromJbpayOrder", err)
			return err
		}
	} else if body.MerchantId == model.PAYGATE_MERCHANT_ID_POMPAY {
		if _, err := CreateCustomerDepositFromPompayOrder(repository.NewPompayRepository(s.sharedDb), body.Id, body.AdminId); err != nil {
			log.Println("Error CreatePaygateDepositFromOrder.CreateCustomerDepositFromPompayOrder", err)
			return err
		}
	} else if body.MerchantId == model.PAYGATE_MERCHANT_ID_PAYMENTCO {
		if _, err := CreateCustomerDepositFromPaymentcoOrder(repository.NewPaymentcoRepository(s.sharedDb), body.Id, body.AdminId); err != nil {
			log.Println("Error CreatePaygateDepositFromOrder.CreateCustomerDepositFromPaymentcoOrder", err)
			return err
		}
	} else if body.MerchantId == model.PAYGATE_MERCHANT_ID_ZAPPAY {
		if _, err := CreateCustomerDepositFromZappayOrder(repository.NewZappayRepository(s.sharedDb), body.Id, body.AdminId); err != nil {
			log.Println("Error CreatePaygateDepositFromOrder.CreateCustomerDepositFromZappayOrder", err)
			return err
		}
	} else if body.MerchantId == model.PAYGATE_MERCHANT_ID_ONEPAY {
		if _, err := CreateCustomerDepositFromOnepayOrder(repository.NewOnepayRepository(s.sharedDb), body.Id, body.AdminId); err != nil {
			log.Println("Error CreatePaygateDepositFromOrder.CreateCustomerDepositFromOnepayOrder", err)
			return err
		}
	} else if body.MerchantId == model.PAYGATE_MERCHANT_ID_FLASHPAY {
		if _, err := CreateCustomerDepositFromFlashpayOrder(repository.NewFlashpayRepository(s.sharedDb), body.Id, body.AdminId); err != nil {
			log.Println("Error CreatePaygateDepositFromOrder.CreateCustomerDepositFromFlashpayOrder", err)
			return err
		}
	} else if body.MerchantId == model.PAYGATE_MERCHANT_ID_BIZPAY {
		if _, err := CreateCustomerDepositFromBizpayOrder(repository.NewBizpayRepository(s.sharedDb), body.Id, body.AdminId); err != nil {
			log.Println("Error CreatePaygateDepositFromOrder.CreateCustomerDepositFromBizpayOrder", err)
			return err
		}
	} else if body.MerchantId == model.PAYGATE_MERCHANT_ID_SUGARPAY {
		if _, err := CreateCustomerDepositFromSugarpayOrder(repository.NewSugarpayRepository(s.sharedDb), body.Id, body.AdminId); err != nil {
			log.Println("Error CreatePaygateDepositFromOrder.CreateCustomerDepositFromSugarpayOrder", err)
			return err
		}
	} else if body.MerchantId == model.PAYGATE_MERCHANT_ID_ZMANPAY {
		if _, err := CreateCustomerDepositFromZmanpayOrder(repository.NewZmanpayRepository(s.sharedDb), body.Id, body.AdminId); err != nil {
			log.Println("Error CreatePaygateDepositFromOrder.CreateCustomerDepositFromZmanpayOrder", err)
			return err
		}
	} else if body.MerchantId == model.PAYGATE_MERCHANT_ID_POSTMANPAY {
		if _, err := CreateCustomerDepositFromPostmanPayOrder(repository.NewPostmanPayRepository(s.sharedDb), body.Id, body.AdminId); err != nil {
			log.Println("Error CreatePaygateDepositFromOrder.CreateCustomerDepositFromPostmanPayOrder", err)
			return err
		}
	} else if body.MerchantId == model.PAYGATE_MERCHANT_ID_MAZEPAY {
		if _, err := CreateCustomerDepositFromMazepayOrder(repository.NewMazepayRepository(s.sharedDb), body.Id, body.AdminId); err != nil {
			log.Println("Error CreatePaygateDepositFromOrder.CreateCustomerDepositFromMazepayOrder", err)
			return err
		}
	} else if body.MerchantId == model.PAYGATE_MERCHANT_ID_MEEPAY {
		if _, err := CreateCustomerDepositFromMeepayOrder(repository.NewMeepayRepository(s.sharedDb), body.Id, body.AdminId); err != nil {
			log.Println("Error CreatePaygateDepositFromOrder.CreateCustomerDepositFromMeepayOrder", err)
			return err
		}
	}

	return nil
}

func (s paymentGatewayService) IgnorePaygateDepositFromOrder(body model.PaygateOrderIgnoreOrderRequest) error {

	// Service Race Condition by OrderId per minute, ActionKey = OrderId-MINUTE
	actionKey := fmt.Sprintf("CPGDFO%d-%s-%d", body.Id, time.Now().Format("0601021504"), body.Id)
	if _, err := s.repo.GetRaceActionIdByActionKey(actionKey); err != nil {
		if err != gorm.ErrRecordNotFound {
			return badRequest("PLEASE_WAIT_1_MINUTE")
		}
	} else {
		return badRequest("PLEASE_WAIT_1_MINUTE")
	}

	var rcCreateBody model.RaceActionCreateBody
	rcCreateBody.Name = "CreatePaygateDepositFromOrder"
	rcCreateBody.JsonRequest = helper.StructJson(map[string]interface{}{
		"orderId": body.Id,
	})
	rcCreateBody.Status = "PENDING"
	rcCreateBody.ActionKey = actionKey
	rcCreateBody.UnlockAt = time.Now().UTC().Add(time.Second * 1)
	if _, err := s.repo.CreateRaceCondition(rcCreateBody); err != nil {
		return internalServerError(err)
	}

	if body.MerchantId == model.PAYGATE_MERCHANT_ID_HENG {
		// item, err := s.repo.GetRawPaygateHengPendingDepositOrderById(body.Id)
		// if err != nil {
		// 	if err.Error() == recordNotFound {
		// 		return badRequest("ORDER_NOT_FOUND")
		// 	}
		// 	return internalServerError(err)
		// }
		// if err := s.repo.IgnoreDbPaygateHengPendingDepositOrder(item.Id); err != nil {
		// 	return internalServerError(err)
		// }
	} else if body.MerchantId == model.PAYGATE_MERCHANT_ID_LUCKYTHAI {
		// item, err := s.repo.GetRawPaygatePaygatePendingDepositOrderById(body.Id)
		// if err != nil {
		// 	if err.Error() == recordNotFound {
		// 		return badRequest("ORDER_NOT_FOUND")
		// 	}
		// 	return internalServerError(err)
		// }
		// if err := s.repo.IgnoreDbPaygatePaygatePendingDepositOrder(item.Id); err != nil {
		// 	return internalServerError(err)
		// }
	} else if body.MerchantId == model.PAYGATE_MERCHANT_ID_PAPAYAPAY {
		// item, err := s.repo.GetRawPaygatePaygatePendingDepositOrderById(body.Id)
		// if err != nil {
		// 	if err.Error() == recordNotFound {
		// 		return badRequest("ORDER_NOT_FOUND")
		// 	}
		// 	return internalServerError(err)
		// }
		// if err := s.repo.IgnoreDbPaygatePaygatePendingDepositOrder(item.Id); err != nil {
		// 	return internalServerError(err)
		// }
	} else if body.MerchantId == model.PAYGATE_MERCHANT_ID_PAYONEX {
		item, err := s.repo.GetRawPayonexPendingDepositOrderById(body.Id)
		if err != nil {
			if err.Error() == recordNotFound {
				return badRequest("ORDER_NOT_FOUND")
			}
			return internalServerError(err)
		}
		if err := s.repo.IgnoreDbPayonexPendingDepositOrder(item.Id, body.AdminId); err != nil {
			return internalServerError(err)
		}
	} else if body.MerchantId == model.PAYGATE_MERCHANT_ID_JBPAY {
		item, err := s.repo.GetRawJbpayPendingDepositOrderById(body.Id)
		if err != nil {
			if err.Error() == recordNotFound {
				return badRequest("ORDER_NOT_FOUND")
			}
			return internalServerError(err)
		}
		if err := s.repo.IgnoreDbJbpayPendingDepositOrder(item.Id, body.AdminId); err != nil {
			return internalServerError(err)
		}
	} else if body.MerchantId == model.PAYGATE_MERCHANT_ID_POMPAY {
		item, err := s.repo.GetRawPompayPendingDepositOrderById(body.Id)
		if err != nil {
			if err.Error() == recordNotFound {
				return badRequest("ORDER_NOT_FOUND")
			}
			return internalServerError(err)
		}
		if err := s.repo.IgnoreDbPompayPendingDepositOrder(item.Id, body.AdminId); err != nil {
			return internalServerError(err)
		}
	} else if body.MerchantId == model.PAYGATE_MERCHANT_ID_PAYMENTCO {
		item, err := s.repo.GetRawPaymentcoPendingDepositOrderById(body.Id)
		if err != nil {
			if err.Error() == recordNotFound {
				return badRequest("ORDER_NOT_FOUND")
			}
			return internalServerError(err)
		}
		if err := s.repo.IgnoreDbPaymentcoPendingDepositOrder(item.Id, body.AdminId); err != nil {
			return internalServerError(err)
		}
	} else if body.MerchantId == model.PAYGATE_MERCHANT_ID_ZAPPAY {
		item, err := s.repo.GetRawZappayPendingDepositOrderById(body.Id)
		if err != nil {
			if err.Error() == recordNotFound {
				return badRequest("ORDER_NOT_FOUND")
			}
			return internalServerError(err)
		}
		if err := s.repo.IgnoreDbZappayPendingDepositOrder(item.Id, body.AdminId); err != nil {
			return internalServerError(err)
		}
	} else if body.MerchantId == model.PAYGATE_MERCHANT_ID_ONEPAY {
		item, err := s.repo.GetRawOnepayPendingDepositOrderById(body.Id)
		if err != nil {
			if err.Error() == recordNotFound {
				return badRequest("ORDER_NOT_FOUND")
			}
			return internalServerError(err)
		}
		if err := s.repo.IgnoreDbOnepayPendingDepositOrder(item.Id, body.AdminId); err != nil {
			return internalServerError(err)
		}
	} else if body.MerchantId == model.PAYGATE_MERCHANT_ID_FLASHPAY {
		item, err := s.repo.GetRawFlashpayPendingDepositOrderById(body.Id)
		if err != nil {
			if err.Error() == recordNotFound {
				return badRequest("ORDER_NOT_FOUND")
			}
			return internalServerError(err)
		}
		if err := s.repo.IgnoreDbFlashpayPendingDepositOrder(item.Id, body.AdminId); err != nil {
			return internalServerError(err)
		}
	} else if body.MerchantId == model.PAYGATE_MERCHANT_ID_BIZPAY {
		item, err := s.repo.GetRawBizpayPendingDepositOrderById(body.Id)
		if err != nil {
			if err.Error() == recordNotFound {
				return badRequest("ORDER_NOT_FOUND")
			}
			return internalServerError(err)
		}
		if err := s.repo.IgnoreDbBizpayPendingDepositOrder(item.Id, body.AdminId); err != nil {
			return internalServerError(err)
		}
	} else if body.MerchantId == model.PAYGATE_MERCHANT_ID_SUGARPAY {
		item, err := s.repo.GetRawSugarpayPendingDepositOrderById(body.Id)
		if err != nil {
			if err.Error() == recordNotFound {
				return badRequest("ORDER_NOT_FOUND")
			}
			return internalServerError(err)
		}
		if err := s.repo.IgnoreDbSugarpayPendingDepositOrder(item.Id, body.AdminId); err != nil {
			return internalServerError(err)
		}
	} else if body.MerchantId == model.PAYGATE_MERCHANT_ID_ZMANPAY {
		item, err := s.repo.GetRawZmanpayPendingDepositOrderById(body.Id)
		if err != nil {
			if err.Error() == recordNotFound {
				return badRequest("ORDER_NOT_FOUND")
			}
			return internalServerError(err)
		}
		if err := s.repo.IgnoreDbZmanpayPendingDepositOrder(item.Id, body.AdminId); err != nil {
			return internalServerError(err)
		}
	} else if body.MerchantId == model.PAYGATE_MERCHANT_ID_POSTMANPAY {
		item, err := s.repo.GetRawPostmanPayPendingDepositOrderById(body.Id)
		if err != nil {
			if err.Error() == recordNotFound {
				return badRequest("ORDER_NOT_FOUND")
			}
			return internalServerError(err)
		}
		if err := s.repo.IgnoreDbPostmanPayPendingDepositOrder(item.Id, body.AdminId); err != nil {
			return internalServerError(err)
		}
	} else if body.MerchantId == model.PAYGATE_MERCHANT_ID_MAZEPAY {
		item, err := s.repo.GetRawMazepayPendingDepositOrderById(body.Id)
		if err != nil {
			if err.Error() == recordNotFound {
				return badRequest("ORDER_NOT_FOUND")
			}
			return internalServerError(err)
		}
		if err := s.repo.IgnoreDbMazepayPendingDepositOrder(item.Id, body.AdminId); err != nil {
			return internalServerError(err)
		}
	} else if body.MerchantId == model.PAYGATE_MERCHANT_ID_MEEPAY {
		item, err := s.repo.GetRawMeepayPendingDepositOrderById(body.Id)
		if err != nil {
			if err.Error() == recordNotFound {
				return badRequest("ORDER_NOT_FOUND")
			}
			return internalServerError(err)
		}
		if err := s.repo.IgnoreDbMeepayPendingDepositOrder(item.Id, body.AdminId); err != nil {
			return internalServerError(err)
		}
	}

	return nil
}

func (s paymentGatewayService) GetPaygateMerchantOption() ([]model.SelectOptions, error) {

	option, err := s.repo.GetPaygateMerchantOption()
	if err != nil {
		return nil, internalServerError(err)
	}

	return option, nil
}

func (s *paymentGatewayService) RacingDepositCreate(paymentId int64, userId int64, amount float64) (*int64, error) {

	// [20250408] ทำ Race Condition ป้องกันการสร้างซ้ำ โดยเช็ต ตามนี้ 1 ผู้ใช้ ต่อ 1 นาที
	// ยอดเดิม 10 นาที
	// เปลี่ยนยอด 1 นาที

	actionAtUtc := time.Now().UTC()
	timing := actionAtUtc.Format("0601021504") // one per minute

	var createBody model.RaceActionCreateBody
	createBody.Name = "RacingGetwayPaymentCreate"
	// createBody.JsonRequest = helper.StructJson(map[string]interface{}{"id": id})
	createBody.Status = "PENDING"
	createBody.JsonRequest = helper.StructJson(map[string]interface{}{
		"userId":      userId,
		"amount":      amount,
		"actionAtUtc": actionAtUtc,
	})
	createBody.ActionKey = fmt.Sprintf("PG%dDEP%d-%s", paymentId, userId, timing)
	createBody.UnlockAt = actionAtUtc.Add(time.Minute * 1)

	if _, err := s.repo.GetRaceActionIdByActionKey(createBody.ActionKey); err != nil {
		// log.Println("RacingDepositCreate.ERROR.GetRaceActionByActionKey", err)
		if err != gorm.ErrRecordNotFound {
			return nil, internalServerError(errors.New("CANNOT_CHECK_ACTION"))
		}
	} else {
		return nil, internalServerError(errors.New("WORK_IN_ACTION"))
	}

	// ถ้ายอดซ้ำ จะเช็ครายการ createdAt ว่ามีการสร้างรายการใน 10 นาทีหรือไม่
	// ถ้ามีรายการใน 10 นาที จะไม่สร้างรายการใหม่
	depRecord, err := s.repo.GetLastestAnyPaygateDeposit(paymentId, userId)
	if err != nil {
		if err != gorm.ErrRecordNotFound {
			return nil, internalServerError(errors.New("CANNOT_CHECK_ACTION"))
		}
	}
	if depRecord != nil && depRecord.Amount == amount {
		// check createdAt must be more than 10 minutes or ERROR
		log.Println("RacingDepositCreate.WARNING", "LOG", depRecord.Id, depRecord.CreatedAt, actionAtUtc, "DIFF=", actionAtUtc.Sub(depRecord.CreatedAt).Minutes())
		if depRecord.CreatedAt.Add(time.Minute * 10).After(actionAtUtc) {
			return nil, internalServerError(errors.New("WAIT_10_MINUTES"))
		}
	}

	// only not found will be created
	actionId, err := s.repo.CreateRaceCondition(createBody)
	if err != nil {
		log.Println("RacingDepositCreate.ERROR.CreateRaceCondition", err)
		return nil, internalServerError(errors.New("CANNOT_CREATE_ACTION"))
	}
	return &actionId, nil
}

func (s paymentGatewayService) GetPaygateMerchantLimitList() ([]model.PaygateMerchantLimitResponse, error) {

	option, err := s.repo.GetPaygateMerchantLimitList()
	if err != nil {
		return nil, internalServerError(err)
	}
	return option, nil
}

func (s paymentGatewayService) GetNonActivePaygateMerchantOption() ([]model.SelectOptions, error) {

	option, err := s.repo.GetNonActivePaygateMerchantOption()
	if err != nil {
		return nil, internalServerError(err)
	}
	return option, nil
}

func (s paymentGatewayService) GetPaygateAccountById(req model.GetByIdRequest) (*model.PaygateAccountResponse, error) {

	record, err := s.repo.GetPaygateAccountById(req.Id)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound(recordNotFound)
		}
		return nil, internalServerError(err)
	}
	return record, nil
}

func (s paymentGatewayService) GetPaygateAccountList(req model.PaygateAccountListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		return nil, badRequest(err.Error())
	}

	list, total, err := s.repo.GetPaygateAccountList(req)
	if err != nil {
		return nil, internalServerError(err)
	}
	return &model.SuccessWithPagination{
		List:  list,
		Total: total,
	}, nil
}

func (s paymentGatewayService) CreatePaygateAccount(req model.PaygateAccountCreateRequest) (*int64, error) {

	// [ADMIN_LOG] Add Log
	var logData model.PaygateAdminLogCreateBody
	logData.AdminId = 0
	logData.Name = "CREATE_PAYGATE_ACCOUNT"
	logData.Status = "PENDING"
	logData.JsonReq = helper.StructJson(map[string]interface{}{
		"req": req,
	})
	logId, err := s.repo.CreatePaygateAdminLog(logData)
	if err != nil {
		log.Println("Error CreatePaygateAccount.CreatePaygateAdminLog", err)
	}

	// Check Provider
	merchant, err := s.repo.GetPaygateMerchantById(req.ProviderId)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, notFound("MERCHANT_NOT_FOUND")
		}
		return nil, internalServerError(err)
	}
	// Check Exist Account same Provider
	existAccount, err := s.repo.GetPaygateAccountByProviderId(merchant.Id)
	if err != nil {
		if err.Error() == recordNotFound {
			// ok
		} else {
			return nil, internalServerError(err)
		}
	}
	if existAccount != nil {
		return nil, badRequest("Provider มีการใช้งานอยู่แล้ว")
	}

	var createBody model.PaygateAccountCreateBody
	createBody.ProviderId = req.ProviderId
	createBody.Name = merchant.TypeName           // FIXED
	createBody.TypeName = merchant.TypeName       // FIXED
	createBody.HasDeposit = merchant.HasDeposit   // clone
	createBody.HasWithdraw = merchant.HasWithdraw // clone
	createBody.ApiEndPoint = merchant.ApiEndPoint // clone
	// User Account
	createBody.DisplayName = req.DisplayName
	if merchant.HasDeposit {
		createBody.IsDepositEnabled = req.IsDepositEnabled
	}
	if merchant.HasWithdraw {
		createBody.IsWithdrawEnabled = req.IsWithdrawEnabled
	}
	createBody.ShopName = req.ShopName
	createBody.Username = req.Username
	createBody.Password = req.Password
	createBody.PrivateKey = req.PrivateKey
	createBody.AccessKey = req.AccessKey
	createBody.SecretKey = req.SecretKey
	createBody.PartnerKey = req.PartnerKey
	createBody.RepayAppId = req.RepayAppId
	createBody.LoanAppId = req.LoanAppId
	createBody.MerchantId = req.MerchantId
	createBody.Token = req.Token
	createBody.AesKey = req.AesKey
	// ไม่เช็คละ
	createBody.PaymentWithdrawMinimum = req.PaymentWithdrawMinimum
	createBody.PaymentWithdrawMaximum = req.PaymentWithdrawMaximum
	createBody.PaymentDepositMinimum = req.PaymentDepositMinimum
	createBody.PaymentDepositMaximum = req.PaymentDepositMaximum

	insertId, err := s.repo.CreatePaygateAccount(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// Allow enable withdraw only one account.
	if merchant.HasWithdraw && req.IsWithdrawEnabled {
		if err := s.repo.SetSingleEnableWithdrawAccount(*insertId); err != nil {
			log.Println("Error CreatePaygateAccount.SetSingleEnableWithdrawAccount", err)
		}
	}

	// [ADMIN_LOG]
	if err := s.repo.UpdatePaygateAdminLog(*logId, model.PaygateAdminLogUpdateBody{
		Status: "SUCCESS",
		JsonResponse: helper.StructJson(map[string]interface{}{
			"createBody": createBody,
		}),
	}); err != nil {
		log.Println("Error CreatePaygateAccount.UpdateSuccessPaygateAdminLog", err)
	}

	return insertId, nil
}

func (s paymentGatewayService) UpdatePaygateAccount(id int64, req model.PaygateAccountUpdateRequest) error {

	oldMerchant, err := s.repo.GetPaygateAccountById(id)
	if err != nil {
		return internalServerError(err)
	}

	// [ADMIN_LOG] Add Log
	var logData model.PaygateAdminLogCreateBody
	logData.AdminId = 0
	logData.Name = "UPDATE_PAYGATE_ACCOUNT"
	logData.Status = "PENDING"
	logData.JsonReq = helper.StructJson(map[string]interface{}{
		"id":  id,
		"req": req,
	})
	logId, err := s.repo.CreatePaygateAdminLog(logData)
	if err != nil {
		log.Println("Error UpdatePaygateAccount.CreatePaygateAdminLog", err)
	}

	var body model.PaygateAccountUpdateBody
	body.DisplayName = req.DisplayName
	if oldMerchant.HasDeposit {
		body.IsDepositEnabled = req.IsDepositEnabled
	}
	if oldMerchant.HasWithdraw {
		body.IsWithdrawEnabled = req.IsWithdrawEnabled
	}
	body.ShopName = req.ShopName
	body.Username = req.Username
	body.Password = req.Password
	body.PrivateKey = req.PrivateKey
	body.AccessKey = req.AccessKey
	body.SecretKey = req.SecretKey
	body.PartnerKey = req.PartnerKey
	body.RepayAppId = req.RepayAppId
	body.LoanAppId = req.LoanAppId
	body.MerchantId = req.MerchantId
	body.Token = req.Token
	body.AesKey = req.AesKey
	// ไม่เช็คละ
	body.PaymentWithdrawMinimum = req.PaymentWithdrawMinimum
	body.PaymentWithdrawMaximum = req.PaymentWithdrawMaximum
	body.PaymentDepositMinimum = req.PaymentDepositMinimum
	body.PaymentDepositMaximum = req.PaymentDepositMaximum
	if err := s.repo.UpdatePaygateAccount(id, body); err != nil {
		return internalServerError(err)
	}

	// Allow enable withdraw only one account.
	if oldMerchant.HasWithdraw && req.IsWithdrawEnabled != nil && *req.IsWithdrawEnabled {
		if err := s.repo.SetSingleEnableWithdrawAccount(oldMerchant.Id); err != nil {
			log.Println("Error UpdatePaygateAccount.SetSingleEnableWithdrawAccount", err)
		}
	}

	// [ADMIN_LOG]
	if err := s.repo.UpdatePaygateAdminLog(*logId, model.PaygateAdminLogUpdateBody{
		Status: "SUCCESS",
		JsonResponse: helper.StructJson(map[string]interface{}{
			"oldMerchant":              oldMerchant,
			"PaygateAccountUpdateBody": body,
		}),
	}); err != nil {
		log.Println("Error UpdatePaygateAccount.UpdatePaygateAdminLog.ERROR=", err)
	}

	return nil
}

func (s paymentGatewayService) DeletePaygateAccount(id int64) error {

	// [ADMIN_LOG] Add Log
	var logData model.PaygateAdminLogCreateBody
	logData.AdminId = 0
	logData.Name = "DELETE_PAYGATE_ACCOUNT"
	logData.Status = "PENDING"
	logData.JsonReq = helper.StructJson(map[string]interface{}{
		"id": id,
	})
	logId, err := s.repo.CreatePaygateAdminLog(logData)
	if err != nil {
		log.Println("Error DeletePaygateAccount.CreatePaygateAdminLog", err)
	}

	oldMerchant, err := s.repo.GetPaygateAccountById(id)
	if err != nil {
		return internalServerError(err)
	}

	if err := s.repo.DeletePaygateAccount(id); err != nil {
		return internalServerError(err)
	}

	// [ADMIN_LOG]
	if err := s.repo.UpdatePaygateAdminLog(*logId, model.PaygateAdminLogUpdateBody{
		Status: "SUCCESS",
		JsonResponse: helper.StructJson(map[string]interface{}{
			"oldMerchant": oldMerchant,
		}),
	}); err != nil {
		log.Println("Error DeletePaygateAccount.UpdateSuccessPaygateAdminLog.ERROR=", err)
	}

	return nil
}
