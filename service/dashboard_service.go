package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"fmt"
	"log"
	"strconv"
	"strings"
	"time"
)

type DashboardService interface {
	// Bank Total Deposit-Withdraw Report
	GetTotalDepositWithdrawSummary(req model.ReportBankTotalDepositWithdrawRequest) (*model.ReportBankTotalDepositWithdrawResponse, error)
	// AdminCorp
	GetAdminCorpBankSummary() (*model.AdminCorpBankSummaryResponse, error)
	GetAdminCorpPendingTransactionList() ([]model.BankPendingRecordResponse, error)
	// TEST
	CreateBankPendingRecord(req model.BankPendingRecordCreateRequest) (*int64, error)
	ApproveBankPendingRecord(req model.BankPendingRecordApproveRequest) error
	RejectBankPendingRecord(req model.BankPendingRecordRejectRequest) error
}

type dashboardServiceRepos struct {
	repo repository.DashboardRepository
}

func NewDashboardService(
	repo repository.DashboardRepository,
) DashboardService {
	return &dashboardServiceRepos{repo}
}

func (s *dashboardServiceRepos) GetTotalDepositWithdrawSummary(req model.ReportBankTotalDepositWithdrawRequest) (*model.ReportBankTotalDepositWithdrawResponse, error) {

	// เอายอด มาจาก ยอดฝากรวมวันนี้ ยอดถอนรวมวันนี้
	// @Summary (getSummaryReportAccountList) สรุปภาพรวม ข้อมูลรายการบัญชีธนาคารฝากถอน Fastbank only
	// @Description (getSummaryReportAccountList) สรุปภาพรวม ข้อมูลรายการบัญชีธนาคารฝากถอน Fastbank only
	// @Tags Report - Summary Report
	// @Router /summary-report/account-list [get]

	var result model.ReportBankTotalDepositWithdrawResponse

	list, err := s.repo.GetTotalDepositWithdrawList(req)
	if err != nil {
		log.Println("GetTotalDepositWithdrawSummary.GetTotalDepositWithdrawList.ERROR=", err)
		return nil, err
	}

	for _, item := range list {
		result.TotalDeposit += item.TotalDeposit
		result.DepositCount += item.DepositCount
		result.TotalWithdraw += item.TotalWithdraw
		result.WithdrawCount += item.WithdrawCount
	}
	result.List = list

	return &result, nil
}

func (s *dashboardServiceRepos) GetSummaryReportDailyOld(req model.ReportSummaryRequest) (*model.ReportSummaryResponse, error) {

	var report model.ReportSummaryResponse
	report.DateType = req.DateType

	// if userReport, err := s.repo.GetUserReportDaily(req); err == nil {
	// 	report.TotalNewUserCount = userReport.TotalNewUserCount
	// 	report.TotalUserCount = userReport.TotalUserCount
	// 	// ยอดใช้งาน [********] นับเฉพาะ Users ที่มีรหัสสมาชิกแล้วเท่านั้น
	// 	report.TotalActiveUserCount = userReport.TotalActiveUserCount
	// 	report.DateFrom = userReport.DateFrom
	// 	report.DateTo = userReport.DateTo
	// }
	if depositReport, err := s.repo.GetUserAccountingReportDaily(req); err == nil {
		report.TotalFirstDepositPrice = depositReport.TotalFirstDepositPrice
		report.TotalFirstDepositUserCount = depositReport.TotalFirstDepositUserCount
		report.TotalDepositPrice = depositReport.TotalDepositPrice
		report.TotalDepositUserCount = depositReport.TotalDepositUserCount
		report.TotalWithdrawPrice = depositReport.TotalWithdrawPrice
		report.TotalWithdrawUserCount = depositReport.TotalWithdrawUserCount
		report.TotalBankProfit = depositReport.TotalBankProfit
	}
	if incomeReport, err := s.repo.GetUserIncomeReportDaily(req); err == nil {
		report.TotalAffiliatePrice = incomeReport.TotalAffiliatePrice
		report.TotalAlliancePrice = incomeReport.TotalAlliancePrice
		report.TotalReturnLossTakenPrice = incomeReport.TotalReturnLossTakenPrice
		report.TotalReturnTurnTakenPrice = incomeReport.TotalReturnTurnTakenPrice
	}
	// if promotionReport, err := s.repo.GetPromotionReportDaily(req); err == nil {
	// report.TotalBonusPrice += promotionReport.TotalReturnLossTakenPrice //= 30
	// report.TotalBonusPrice += promotionReport.TotalAccountingBonusPrice //= Bonus 10
	// report.TotalBonusPrice += promotionReport.TotalAffiliateBonusPrice
	// later : more promotion ? [********] ผมมาเพิ่มรายการกิจกรรมรับโบนัสรายวันกับกงล้อ
	// [********] โบนัสทุกอย่างนับจาก user_transaction ล้วนๆ ทุกประเภท
	// report.TotalBonusPrice += promotionReport.TotalActivityBonusPrice // 40
	// }
	if creditReport, err := s.repo.GetUserCreditReportDaily(req); err == nil {
		report.TotalCreditBackPrice = creditReport.TotalCreditBackPrice
		report.TotalCreditBackCount = creditReport.TotalCreditBackCount
	}
	if playlogReport, err := s.repo.GetUserTodayPlaylogReportDaily(req); err == nil {
		// fmt.Println("playlogReport", helper.StructJson(playlogReport))
		report.TotalTurn = playlogReport.TotalTurn
		// "totalWinlose": -20 == ลูกค้าเสีย 20 report จะเป็น 20
		// ถ้า ลูกค้าได้ 20 report จะเป็น -20
		report.TotalWinlose = helper.Float64ToDecimal(playlogReport.TotalWinlose)
		report.TotalProfitPrice = playlogReport.TotalWinlose * -1
	}
	// [20240521] เปลี่ยนวิธีการคำนวน เป็น lose - win - commission

	// P.Mink บอกให้ เอา ลบ ค่าคอมมิชชั่น ออกจาก เพราะ web solo งง
	// totalCommissionPrice := report.TotalAffiliatePrice + report.TotalAlliancePrice
	// report.TotalProfitPrice = helper.Float64ToDecimal(report.TotalProfitPrice - totalCommissionPrice)
	report.TotalProfitPrice = helper.Float64ToDecimal(report.TotalProfitPrice)

	if getAcivity, err := s.repo.GetActivitySummaryReportDaily(req); err == nil {
		report.TotalPromotionWebCredit = getAcivity.TotalPromotionWebCredit
		report.TotalPromotionReturnLoss = getAcivity.TotalPromotionReturnLoss
		report.TotalActivityLuckyWheel = getAcivity.TotalActivityLuckyWheel
		report.TotalCheckInBonus = getAcivity.TotalCheckInBonus
		report.TotalPromotionCashCoupon = getAcivity.TotalPromotionCashCoupon
		report.TotalAdminCreateBonus = getAcivity.TotalAdminCreateBonus
		report.TotalPromotionReturnTurn = getAcivity.TotalPromotionReturnTurn
		// SEVEN_USER_BONUS_INCOME ** no getAcivity.TotalBonusPrice +
		report.TotalBonusPrice += getAcivity.TotalPromotionWebCredit + getAcivity.TotalPromotionReturnLoss + getAcivity.TotalActivityLuckyWheel + getAcivity.TotalCheckInBonus + getAcivity.TotalPromotionCashCoupon + getAcivity.TotalAdminCreateBonus + getAcivity.TotalPromotionReturnTurn
		report.TotalBonusCount += getAcivity.TotalBonusCount
	}

	return &report, nil
}

func (s *dashboardServiceRepos) GetSummaryReportDaily(req model.ReportSummaryRequest) (*model.ReportSummaryResponse, error) {

	dateType, err := s.repo.GetDateFromDateType(model.DateTypeResponse{
		DateType: req.DateType,
		DateFrom: req.DateFrom,
		DateTo:   req.DateTo,
	})
	if err != nil {
		return nil, err
	}

	currentDate := time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02")
	startDate, _ := time.Parse("2006-01-02", dateType.DateFrom)
	endDate, _ := time.Parse("2006-01-02", dateType.DateTo)
	currentDateParsed, _ := time.Parse("2006-01-02", currentDate)

	reportNew := &model.ReportSummaryResponse{}

	if userReport, err := s.repo.GetUserReportDaily(req); err == nil {
		reportNew.TotalNewUserCount = userReport.TotalNewUserCount
		reportNew.TotalUserCount = userReport.TotalUserCount
		// ยอดใช้งาน [********] นับเฉพาะ Users ที่มีรหัสสมาชิกแล้วเท่านั้น
		reportNew.TotalActiveUserCount = userReport.TotalActiveUserCount
		reportNew.DateFrom = userReport.DateFrom
		reportNew.DateTo = userReport.DateTo
	}

	if endDate.Equal(currentDateParsed) || startDate.Equal(currentDateParsed) || endDate.After(currentDateParsed) {
		oldRequest := model.ReportSummaryRequest{
			DateFrom: currentDate,
			DateTo:   dateType.DateTo,
		}

		reportOld, err := s.GetSummaryReportDailyOld(oldRequest)
		if err != nil {
			return nil, err
		}

		if reportNew != nil {
			// reportNew.TotalUserCount = reportOld.TotalUserCount
			// reportNew.TotalNewUserCount += reportOld.TotalNewUserCount
			// reportNew.TotalActiveUserCount += reportOld.TotalActiveUserCount
			reportNew.TotalFirstDepositPrice += reportOld.TotalFirstDepositPrice
			reportNew.TotalFirstDepositUserCount += reportOld.TotalFirstDepositUserCount
			reportNew.TotalDepositPrice += reportOld.TotalDepositPrice
			reportNew.TotalDepositUserCount += reportOld.TotalDepositUserCount
			reportNew.TotalWithdrawPrice += reportOld.TotalWithdrawPrice
			reportNew.TotalWithdrawUserCount += reportOld.TotalWithdrawUserCount
			reportNew.TotalBankProfit += reportOld.TotalBankProfit
			reportNew.TotalAffiliatePrice += reportOld.TotalAffiliatePrice
			reportNew.TotalAlliancePrice += reportOld.TotalAlliancePrice
			reportNew.TotalReturnLossTakenPrice += reportOld.TotalReturnLossTakenPrice
			reportNew.TotalReturnTurnTakenPrice += reportOld.TotalReturnTurnTakenPrice
			reportNew.TotalCreditBackPrice += reportOld.TotalCreditBackPrice
			reportNew.TotalCreditBackCount += reportOld.TotalCreditBackCount
			reportNew.TotalTurn += reportOld.TotalTurn
			reportNew.TotalWinlose += reportOld.TotalWinlose
			reportNew.TotalProfitPrice += reportOld.TotalProfitPrice
			reportNew.TotalBonusPrice += reportOld.TotalBonusPrice
			reportNew.TotalBonusCount += reportOld.TotalBonusCount
			reportNew.TotalPromotionWebCredit += reportOld.TotalPromotionWebCredit
			reportNew.TotalPromotionReturnLoss += reportOld.TotalPromotionReturnLoss
			reportNew.TotalActivityLuckyWheel += reportOld.TotalActivityLuckyWheel
			reportNew.TotalCheckInBonus += reportOld.TotalCheckInBonus
			reportNew.TotalPromotionCashCoupon += reportOld.TotalPromotionCashCoupon
			reportNew.TotalAdminCreateBonus += reportOld.TotalAdminCreateBonus
			reportNew.TotalPromotionReturnTurn += reportOld.TotalPromotionReturnTurn

			// [********] ยอดฝาก และ ยอดฝากครั้งแรก จะถูกหักไปด้วยยอดดึงเครดิตกลับ.
			// ลบแต่ยอด สุทธิ จาก ยอดดึงเครดิตกลับ
			// reportNew.TotalBankProfit -= reportNew.TotalCreditBackPrice
			// ยืนยันวันที่ 18/04/25
			// ก่อนหน้านี้พี่เลย์แจ้งปรับ (การ์ดสีฟ้า) คือยอดฝาก - ยอดถอน - ยอดดึงเครดิตกลับ = ยอดสุทธิ (ฝาก-ถอน)
			// ปรับให้เป็นเหมือนเดิมคือ ยอดฝาก - ยอดถอน = ยอดสุทธิ (ฝาก-ถอน)
		}

		if reportNew != nil {
			reportNew.DateType = req.DateType
			reportNew.DateFrom = req.DateFrom
			reportNew.DateTo = req.DateTo
			return reportNew, nil
		}
		reportOld.DateType = req.DateType
		reportOld.DateFrom = req.DateFrom
		reportOld.DateTo = req.DateTo
		return reportOld, nil
	}

	reportNew.DateType = req.DateType
	reportNew.DateFrom = req.DateFrom
	reportNew.DateTo = req.DateTo
	return reportNew, nil
}

func (s *dashboardServiceRepos) GetAdminCorpBankSummary() (*model.AdminCorpBankSummaryResponse, error) {

	actionAtUtc := time.Now().UTC()

	var summary model.AdminCorpBankSummaryResponse
	summary.OfDate = actionAtUtc.Format("2006-01-02")
	summary.UpdateAt = actionAtUtc

	// ใข้ สรุปภาพรวม
	// https: //dev-api.cbgame88.com/api/summary-report/daily?dateType=yesterday
	// รอเทสแล้วจะเอาไปใช้ แทน
	var query model.ReportSummaryRequest
	query.DateType = "daily"
	getReportSummaryList, err := s.GetSummaryReportDaily(query)
	if err != nil {
		log.Println("GetAdminCorpBankSummary.GetReportSummaryListTotal.ERROR=", err)
		return nil, internalServerError(err)
	}

	fmt.Println("GetAdminCorpBankSummary", helper.StructJson(getReportSummaryList))

	// สุทธิ (ฝาก-ถอน)
	summary.TotalProfitAmount = getReportSummaryList.TotalBankProfit
	summary.TotalRecordCount = getReportSummaryList.TotalDepositUserCount + getReportSummaryList.TotalWithdrawUserCount
	// ยอดฝากรวม
	summary.DepositAmount = getReportSummaryList.TotalDepositPrice
	summary.DepositCount = getReportSummaryList.TotalDepositUserCount
	// ยอดถอนรวม
	summary.WithdrawAmount = getReportSummaryList.TotalWithdrawPrice
	summary.WithdrawCount = getReportSummaryList.TotalWithdrawUserCount
	// ยอดโบนัส
	summary.BonusAmount = getReportSummaryList.TotalBonusPrice
	summary.BonusCount = getReportSummaryList.TotalBonusCount
	// ยอดดึงเครดิตกลับ
	summary.CreditBackAmount = getReportSummaryList.TotalCreditBackPrice
	summary.CreditBackCount = getReportSummaryList.TotalCreditBackCount
	// ยอดฝากครั้งแรก
	summary.NewMemberDepositAmount = getReportSummaryList.TotalFirstDepositPrice
	summary.NewMemberDepositCount = getReportSummaryList.TotalFirstDepositUserCount
	// ยอดฝากสมาชิกเก่า
	summary.OldMemberDepositAmount = getReportSummaryList.TotalDepositPrice - getReportSummaryList.TotalFirstDepositPrice
	summary.OldMemberDepositCount = getReportSummaryList.TotalDepositUserCount - getReportSummaryList.TotalFirstDepositUserCount

	// [********] ยอดฝาก และ ยอดฝากครั้งแรก จะถูกหักไปด้วยยอดดึงเครดิตกลับ.
	// ลบแต่ยอด สุทธิ จาก ยอดดึงเครดิตกลับ
	// [********] report.TotalBankProfit -= report.TotalCreditBackPrice
	// ยืนยันวันที่ 18/04/25
	// ก่อนหน้านี้พี่เลย์แจ้งปรับ (การ์ดสีฟ้า) คือยอดฝาก - ยอดถอน - ยอดดึงเครดิตกลับ = ยอดสุทธิ (ฝาก-ถอน)
	// ปรับให้เป็นเหมือนเดิมคือ ยอดฝาก - ยอดถอน = ยอดสุทธิ (ฝาก-ถอน)

	return &summary, nil
}

func (s *dashboardServiceRepos) GetAdminCorpPendingTransactionList() ([]model.BankPendingRecordResponse, error) {

	data, err := s.repo.GetAdminCorpPendingTransactionList()
	if err != nil {
		log.Println("GetAdminCorpPendingTransactionList.ERROR=", err)
		return nil, err
	}
	return data, nil
}

func (s *dashboardServiceRepos) CreateBankPendingRecord(req model.BankPendingRecordCreateRequest) (*int64, error) {

	// refId + RecordTypeId
	refId := time.Now().Unix()
	refKey := fmt.Sprintf("%d-%d", req.RecordTypeId, refId)

	var createBody model.BankPendingRecordCreateBody
	createBody.RefKey = refKey
	createBody.UserId = req.UserId
	createBody.Name = req.Name
	createBody.RecordTypeId = req.RecordTypeId
	createBody.MemberCode = req.MemberCode
	createBody.UserFullname = req.UserFullname
	createBody.UserPhone = req.UserPhone
	createBody.FromBankCode = req.FromBankCode
	createBody.FromAccount = req.FromAccount
	createBody.ToBankCode = req.ToBankCode
	createBody.ToAccount = req.ToAccount
	createBody.TransferAt = req.TransferAt
	createBody.CreditAmount = req.CreditAmount
	createBody.AdminFullname = req.AdminFullname
	insertId, err := s.repo.CreateBankPendingRecord(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// Later open or CRON
	// if err := s.repo.DeleteMonthOlderBankPendingRecord(); err != nil {
	// 	log.Println("CreateBankPendingRecord.DeleteMonthOlderBankPendingRecord.ERROR=", err)
	// }

	// Socket onCreated
	if err := s.repo.SendWebSocketUpdateAdminCorpDashboard(*insertId); err != nil {
		log.Println("CreateBankPendingRecord.SendWebSocketUpdateAdminCorpDashboard.ERROR=", err)
	}

	return insertId, nil
}

func AddBankPendingRecordFromDeposit(repo repository.DashboardRepository, refId int64) error {

	bankRecord, err := repo.GetRawBankRecordById(refId)
	if err != nil {
		log.Println("AddBankPendingRecordFromDeposit.GetRawBankRecord.ERROR=", err)
		return internalServerError(err)
	}
	if bankRecord == nil || bankRecord.UserId == nil {
		return badRequest("AddBankPendingRecordFromDeposit.SKIPED.bankRecord not found")
	}
	// Process only PENDING_ALL status
	if bankRecord.TransactionStatusId != model.TRANS_STATUS_PENDING && bankRecord.TransactionStatusId != model.TRANS_STATUS_DEPOSIT_PENDING_CREDIT && bankRecord.TransactionStatusId != model.TRANS_STATUS_DEPOSIT_PENDING_SLIP {
		log.Println("AddBankPendingRecordFromDeposit.SKIPED.bankRecord status not pending=", bankRecord.TransactionStatusId)
		// Maybe already processed
		return badRequest("bankRecord status not pending")
	}

	// BANK_PENDING_RECORD_TYPE + refId
	refKey := fmt.Sprintf("%d-%d", model.BANK_PENDING_RECORD_TYPE_DEPOSIT, refId)

	var userInfo model.UserRawResponse
	if bankRecord.UserId != nil {
		rawUserInfo, err := repo.GetRawUserById(*bankRecord.UserId)
		if err != nil {
			log.Println("AddBankPendingRecordFromWithdraw.GetUserById.ERROR=", err)
		}
		if rawUserInfo != nil {
			userInfo.Id = rawUserInfo.Id
			userInfo.MemberCode = rawUserInfo.MemberCode
			userInfo.Fullname = rawUserInfo.Fullname
			userInfo.Phone = rawUserInfo.Phone
		}
	}

	var createBody model.BankPendingRecordCreateBody
	createBody.RefKey = refKey
	createBody.UserId = userInfo.Id
	createBody.Name = "รายการฝาก"
	createBody.RecordTypeId = model.BANK_PENDING_RECORD_TYPE_DEPOSIT
	createBody.MemberCode = userInfo.MemberCode
	createBody.UserFullname = userInfo.Fullname
	createBody.UserPhone = userInfo.Phone
	createBody.FromBankCode = bankRecord.FromBankCode
	createBody.FromAccount = bankRecord.FromAccountNumber
	createBody.ToBankCode = bankRecord.ToBankCode
	createBody.ToAccount = bankRecord.ToAccountNumber
	createBody.TransferAt = bankRecord.TransferAt
	createBody.CreditAmount = bankRecord.CreditAmount
	createBody.AdminFullname = bankRecord.CreatedByAdminFullname
	insertId, err := repo.CreateBankPendingRecord(createBody)
	if err != nil {
		return internalServerError(err)
	}

	// Socket onCreated
	if err := repo.SendWebSocketUpdateAdminCorpDashboard(*insertId); err != nil {
		log.Println("AddBankPendingRecordFromDeposit.SendWebSocketUpdateAdminCorpDashboard.ERROR=", err)
	}
	return nil
}

func AddBankPendingRecordFromWithdraw(repo repository.DashboardRepository, refId int64) error {

	bankRecord, err := repo.GetRawBankRecordById(refId)
	if err != nil {
		log.Println("AddBankPendingRecordFromWithdraw.GetRawBankRecord.ERROR=", err)
		return internalServerError(err)
	}
	if bankRecord == nil || bankRecord.UserId == nil {
		return badRequest("bankRecord not found")
	}

	// BANK_PENDING_RECORD_TYPE + refId
	refKey := fmt.Sprintf("%d-%d", model.BANK_PENDING_RECORD_TYPE_WITHDRAW, refId)

	var userInfo model.UserRawResponse
	if bankRecord.UserId != nil {
		rawUserInfo, err := repo.GetRawUserById(*bankRecord.UserId)
		if err != nil {
			log.Println("AddBankPendingRecordFromWithdraw.GetUserById.ERROR=", err)
		}
		if rawUserInfo != nil {
			userInfo.Id = rawUserInfo.Id
			userInfo.MemberCode = rawUserInfo.MemberCode
			userInfo.Fullname = rawUserInfo.Fullname
			userInfo.Phone = rawUserInfo.Phone
		}
	}

	var createBody model.BankPendingRecordCreateBody
	createBody.RefKey = refKey
	createBody.UserId = userInfo.Id
	createBody.Name = "รายการถอน"
	createBody.RecordTypeId = model.BANK_PENDING_RECORD_TYPE_WITHDRAW
	createBody.MemberCode = userInfo.MemberCode
	createBody.UserFullname = userInfo.Fullname
	createBody.UserPhone = userInfo.Phone
	createBody.FromBankCode = bankRecord.FromBankCode
	createBody.FromAccount = bankRecord.FromAccountNumber
	createBody.ToBankCode = bankRecord.ToBankCode
	createBody.ToAccount = bankRecord.ToAccountNumber
	createBody.TransferAt = bankRecord.TransferAt
	createBody.CreditAmount = bankRecord.CreditAmount
	createBody.AdminFullname = bankRecord.CreatedByAdminFullname
	insertId, err := repo.CreateBankPendingRecord(createBody)
	if err != nil {
		return internalServerError(err)
	}

	// Socket onCreated
	if err := repo.SendWebSocketUpdateAdminCorpDashboard(*insertId); err != nil {
		log.Println("AddBankPendingRecordFromWithdraw.SendWebSocketUpdateAdminCorpDashboard.ERROR=", err)
	}
	return nil
}

func AddBankPendingRecordFromAffiliate(repo repository.DashboardRepository, refId int64, userId int64, transferAt time.Time, amount float64, adminFullname string) error {

	userInfo, err := repo.GetRawUserById(userId)
	if err != nil {
		log.Println("AddBankPendingRecordFromAffiliate.GetUserById.ERROR=", err)
		return internalServerError(err)
	}

	// BANK_PENDING_RECORD_TYPE + refId
	refKey := fmt.Sprintf("%d-%d", model.BANK_PENDING_RECORD_TYPE_BONUS_AFF, refId)

	var createBody model.BankPendingRecordCreateBody
	createBody.RefKey = refKey
	createBody.UserId = userInfo.Id
	createBody.Name = "โบนัสกิจกรรม"
	createBody.RecordTypeId = model.BANK_PENDING_RECORD_TYPE_BONUS_AFF
	createBody.MemberCode = userInfo.MemberCode
	createBody.UserFullname = userInfo.Fullname
	createBody.UserPhone = userInfo.Phone
	createBody.FromBankCode = ""
	createBody.FromAccount = "โบนัสกิจกรรม"
	createBody.ToBankCode = ""
	createBody.ToAccount = "แจกโบนัสแนะนำเพื่อน"
	createBody.TransferAt = &transferAt
	createBody.CreditAmount = amount
	createBody.AdminFullname = adminFullname
	insertId, err := repo.CreateBankPendingRecord(createBody)
	if err != nil {
		return internalServerError(err)
	}

	// Socket onCreated
	if err := repo.SendWebSocketUpdateAdminCorpDashboard(*insertId); err != nil {
		log.Println("AddBankPendingRecordFromAffiliate.SendWebSocketUpdateAdminCorpDashboard.ERROR=", err)
	}
	return nil
}

func AddBankPendingRecordFromReturnLoss(repo repository.DashboardRepository, refId int64, userId int64, transferAt time.Time, amount float64, adminFullname string) error {

	userInfo, err := repo.GetRawUserById(userId)
	if err != nil {
		log.Println("AddBankPendingRecordFromReturnLoss.GetUserById.ERROR=", err)
		return internalServerError(err)
	}

	// BANK_PENDING_RECORD_TYPE + refId
	refKey := fmt.Sprintf("%d-%d", model.BANK_PENDING_RECORD_TYPE_RETURN_LOSS, refId)

	var createBody model.BankPendingRecordCreateBody
	createBody.RefKey = refKey
	createBody.UserId = userInfo.Id
	createBody.Name = "โบนัสกิจกรรม"
	createBody.RecordTypeId = model.BANK_PENDING_RECORD_TYPE_RETURN_LOSS
	createBody.MemberCode = userInfo.MemberCode
	createBody.UserFullname = userInfo.Fullname
	createBody.UserPhone = userInfo.Phone
	createBody.FromBankCode = ""
	createBody.FromAccount = "โบนัสกิจกรรม"
	createBody.ToBankCode = ""
	createBody.ToAccount = "แจกโบนัสคืนยอดเสีย"
	createBody.TransferAt = &transferAt
	createBody.CreditAmount = amount
	createBody.AdminFullname = adminFullname
	insertId, err := repo.CreateBankPendingRecord(createBody)
	if err != nil {
		return internalServerError(err)
	}

	// Socket onCreated
	if err := repo.SendWebSocketUpdateAdminCorpDashboard(*insertId); err != nil {
		log.Println("AddBankPendingRecordFromReturnLoss.SendWebSocketUpdateAdminCorpDashboard.ERROR=", err)
	}
	return nil
}

func AddBankPendingRecordFromReturnLuckyWheel(repo repository.DashboardRepository, refId int64, userId int64, transferAt time.Time, amount float64, adminFullname string) error {

	userInfo, err := repo.GetRawUserById(userId)
	if err != nil {
		log.Println("AddBankPendingRecordFromReturnLuckyWheel.GetUserById.ERROR=", err)
		return internalServerError(err)
	}

	// BANK_PENDING_RECORD_TYPE + refId
	refKey := fmt.Sprintf("%d-%d", model.BANK_PENDING_RECORD_TYPE_LUCKY_WHEEL, refId)

	var createBody model.BankPendingRecordCreateBody
	createBody.RefKey = refKey
	createBody.UserId = userInfo.Id
	createBody.Name = "โบนัสกิจกรรม"
	createBody.RecordTypeId = model.BANK_PENDING_RECORD_TYPE_LUCKY_WHEEL
	createBody.MemberCode = userInfo.MemberCode
	createBody.UserFullname = userInfo.Fullname
	createBody.UserPhone = userInfo.Phone
	createBody.FromBankCode = ""
	createBody.FromAccount = "โบนัสกิจกรรม"
	createBody.ToBankCode = ""
	createBody.ToAccount = "รายได้จากกิจกรรมกงล้อนำโชค"
	createBody.TransferAt = &transferAt
	createBody.CreditAmount = amount
	createBody.AdminFullname = adminFullname
	insertId, err := repo.CreateBankPendingRecord(createBody)
	if err != nil {
		return internalServerError(err)
	}

	// Socket onCreated
	if err := repo.SendWebSocketUpdateAdminCorpDashboard(*insertId); err != nil {
		log.Println("AddBankPendingRecordFromReturnLuckyWheel.SendWebSocketUpdateAdminCorpDashboard.ERROR=", err)
	}
	return nil
}

func AddBankPendingRecordFromReturnTurn(repo repository.DashboardRepository, refId int64, userId int64, transferAt time.Time, amount float64, adminFullname string) error {

	userInfo, err := repo.GetRawUserById(userId)
	if err != nil {
		log.Println("AddBankPendingRecordFromReturnTurn.GetUserById.ERROR=", err)
		return internalServerError(err)
	}

	// BANK_PENDING_RECORD_TYPE + refId
	refKey := fmt.Sprintf("%d-%d", model.BANK_PENDING_RECORD_TYPE_RETURN_TURN, refId)

	var createBody model.BankPendingRecordCreateBody
	createBody.RefKey = refKey
	createBody.UserId = userInfo.Id
	createBody.Name = "โบนัสกิจกรรม"
	createBody.RecordTypeId = model.BANK_PENDING_RECORD_TYPE_RETURN_TURN
	createBody.MemberCode = userInfo.MemberCode
	createBody.UserFullname = userInfo.Fullname
	createBody.UserPhone = userInfo.Phone
	createBody.FromBankCode = ""
	createBody.FromAccount = "โบนัสกิจกรรม"
	createBody.ToBankCode = ""
	createBody.ToAccount = "แจกโบนัสฟรี คืนยอดcommission"
	createBody.TransferAt = &transferAt
	createBody.CreditAmount = amount
	createBody.AdminFullname = adminFullname
	insertId, err := repo.CreateBankPendingRecord(createBody)
	if err != nil {
		return internalServerError(err)
	}

	// Socket onCreated
	if err := repo.SendWebSocketUpdateAdminCorpDashboard(*insertId); err != nil {
		log.Println("AddBankPendingRecordFromReturnTurn.SendWebSocketUpdateAdminCorpDashboard.ERROR=", err)
	}
	return nil
}

func ConfirmBankPendingRecordFromAny(repo repository.DashboardRepository, recordTypeId int64, refId int64, transferAt time.Time, adminFullname string) error {

	actionAtUtc := time.Now().UTC()

	// BANK_PENDING_RECORD_TYPE + refId
	refKey := fmt.Sprintf("%d-%d", recordTypeId, refId)
	updateStatus := model.BANK_PENDING_RECORD_TYPE_APPROVED
	// updateAdminFullname := adminFullname

	pendingRecord, err := repo.GetBankPendingRecordByRefKey(refKey)
	if err != nil {
		log.Println("ConfirmBankPendingRecordFromAny.GetBankPendingRecordByRefKey.ERROR=", err)
		return internalServerError(err)
	}

	var body model.BankPendingRecordUpdateBody
	body.TransferAt = &transferAt
	body.UpdateStatus = &updateStatus
	body.UpdateAdminFullname = &adminFullname
	body.SocketAt = &actionAtUtc
	if err := repo.UpdateBankPendingRecord(pendingRecord.Id, body); err != nil {
		return internalServerError(err)
	}

	// Socket onCreated
	if err := repo.SendWebSocketUpdateAdminCorpDashboard(pendingRecord.Id); err != nil {
		log.Println("ConfirmBankPendingRecordFromAny.SendWebSocketUpdateAdminCorpDashboard.ERROR=", err)
	}

	return nil
}

func RejectBankPendingRecordFromAny(repo repository.DashboardRepository, recordTypeId int64, refId int64, transferAt time.Time, adminFullname string) error {

	actionAtUtc := time.Now().UTC()

	// BANK_PENDING_RECORD_TYPE + refId
	refKey := fmt.Sprintf("%d-%d", recordTypeId, refId)
	updateStatus := model.BANK_PENDING_RECORD_TYPE_REJECTED
	// updateAdminFullname := adminFullname

	pendingRecord, err := repo.GetBankPendingRecordByRefKey(refKey)
	if err != nil {
		log.Println("RejectBankPendingRecordFromAny.GetBankPendingRecordByRefKey.ERROR=", err)
		return badRequest("PENDING_RECORD_NOT_FOUND")
	}

	var body model.BankPendingRecordUpdateBody
	body.TransferAt = &transferAt
	body.UpdateStatus = &updateStatus
	body.UpdateAdminFullname = &adminFullname
	body.SocketAt = &actionAtUtc
	if err := repo.UpdateBankPendingRecord(pendingRecord.Id, body); err != nil {
		return internalServerError(err)
	}

	// Socket onCreated
	if err := repo.SendWebSocketUpdateAdminCorpDashboard(pendingRecord.Id); err != nil {
		log.Println("RejectBankPendingRecordFromAny.SendWebSocketUpdateAdminCorpDashboard.ERROR=", err)
	}

	return nil
}

func (s *dashboardServiceRepos) ApproveBankPendingRecord(req model.BankPendingRecordApproveRequest) error {

	actionAtUtc := time.Now().UTC()

	fmt.Println("ApproveBankPendingRecord", helper.StructJson(req))

	// admin
	admin, err := s.repo.GetAdminById(req.AdminId)
	if err != nil {
		log.Println("ApproveBankPendingRecord.GetAdminById.ERROR=", err)
		return internalServerError(err)
	}

	record, err := s.repo.GetBankPendingRecordById(req.Id)
	if err != nil {
		log.Println("ApproveBankPendingRecord.GetBankPendingRecordById.ERROR=", err)
		return internalServerError(err)
	}
	// spit refKey by "-" to get refId
	tempKey := strings.Split(record.RefKey, "-")
	if len(tempKey) != 2 {
		log.Println("ApproveBankPendingRecord.refKey ERROR=", record.RefKey)
		return internalServerError(fmt.Errorf("refKey not valid"))
	}
	refId, err := strconv.ParseInt(tempKey[1], 10, 64)
	if err != nil {
		log.Println("ApproveBankPendingRecord.refId ERROR=", err)
		return internalServerError(err)
	}

	// Socket onUpdate
	if err := ConfirmBankPendingRecordFromAny(s.repo, record.RecordTypeId, refId, actionAtUtc, admin.Username); err != nil {
		log.Println("ApproveBankPendingRecord.SendWebSocketUpdateAdminCorpDashboard.ERROR=", err)
	}
	return nil
}

func (s *dashboardServiceRepos) RejectBankPendingRecord(req model.BankPendingRecordRejectRequest) error {

	actionAtUtc := time.Now().UTC()

	fmt.Println("RejectBankPendingRecord", helper.StructJson(req))

	// admin
	admin, err := s.repo.GetAdminById(req.AdminId)
	if err != nil {
		log.Println("RejectBankPendingRecord.GetAdminById.ERROR=", err)
		return internalServerError(err)
	}

	record, err := s.repo.GetBankPendingRecordById(req.Id)
	if err != nil {
		log.Println("RejectBankPendingRecord.GetBankPendingRecordById.ERROR=", err)
		return internalServerError(err)
	}
	// spit refKey by "-" to get refId
	tempKey := strings.Split(record.RefKey, "-")
	if len(tempKey) != 2 {
		log.Println("RejectBankPendingRecord.refKey ERROR=", record.RefKey)
		return internalServerError(fmt.Errorf("refKey not valid"))
	}
	refId, err := strconv.ParseInt(tempKey[1], 10, 64)
	if err != nil {
		log.Println("RejectBankPendingRecord.refId ERROR=", err)
		return internalServerError(err)
	}

	// Socket onUpdate
	if err := RejectBankPendingRecordFromAny(s.repo, record.RecordTypeId, refId, actionAtUtc, admin.Username); err != nil {
		log.Println("UpdateBankPendingRecord.SendWebSocketUpdateAdminCorpDashboard.ERROR=", err)
	}
	return nil
}
