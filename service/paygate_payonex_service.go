package service

import (
	"bytes"
	"cybergame-api/helper"
	"cybergame-api/model"
	"cybergame-api/repository"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"image/png"
	"log"
	"strings"
	"time"

	"github.com/skip2/go-qrcode"
	"gorm.io/gorm"
)

type PayonexService interface {
	// Payonex
	CreatePayonexWebhook(req model.PayonexWebhookRequest) (*int64, error)
	GetPayonexWebDepositAccount() (*model.PayonexCustomerDepositInfo, error)
	CreatePayonexDeposit(req model.PayonexDepositCreateRequest) (*model.PayonexOrderWebResponse, error)
	CreatePayonexWithdraw(req model.PayonexWithdrawCreateRequest) (*int64, error)
	// PayonexCheckBalance() (*model.PayonexCheckBalanceRemoteResponse, error)
	CancelWithdrawFromPayonex(transId int64, adminId int64) error
	// SysLog
	CreateSystemLog(name string, req interface{}) error
}

type paygatePayonexService struct {
	sharedDb                  *gorm.DB
	repo                      repository.PayonexRepository
	activityLuckyWheelService ActivityLuckyWheelService
	promotionWebService       PromotionWebService
	notiService               NotificationService
}

func NewPayonexService(
	sharedDb *gorm.DB,
	repo repository.PayonexRepository,
	activityLuckyWheelService ActivityLuckyWheelService,
	promotionWebService PromotionWebService,
	notiService NotificationService,
) PayonexService {
	return &paygatePayonexService{sharedDb, repo, activityLuckyWheelService, promotionWebService, notiService}
}

func (s paygatePayonexService) GetPayonexAccessToken(pgAccount *model.PaygateAccountResponse) (string, error) {

	return GetPayonexAccessToken(s.repo, pgAccount)
}

func GetPayonexAccessToken(repo repository.PayonexRepository, pgAccount *model.PaygateAccountResponse) (string, error) {

	if pgAccount == nil {
		newSetting, err := GetPayonexAccount(repo)
		if err != nil {
			return "", internalServerError(err)
		}
		pgAccount = newSetting
	}
	if pgAccount == nil {
		return "", badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}
	if pgAccount.AccessKey == "" || pgAccount.SecretKey == "" {
		// if os.Getenv("GIN_MODE") == "debug" {
		// 	return "SANDBOX_TOKEN", nil
		// } else {
		// 	return "", badRequest("INVALID_USERNAME_OR_PASSWORD")
		// }
		return "", badRequest("INVALID_MERCHANT_ACCESS_KEY_OR_SECRET")
	}

	// use DB cache if not expired
	accessToken, err := repo.GetDbPayonexAccessToken()
	if err != nil {
		if err.Error() == recordNotFound {
			data, err := repo.PayonexGetToken(*pgAccount)
			if err != nil {
				return "", internalServerError(err)
			}
			accessToken := data.Data.Token
			// Save Cache
			var createBody model.PayonexTokenCreateBody
			createBody.AccessToken = accessToken
			createBody.ExpireAt = time.Now().Add(time.Minute * 30)
			createBody.CreateBy = 1
			if _, err = repo.CreateDbPayonexAccessToken(createBody); err != nil {
				return "", internalServerError(err)
			}
			// Save Response as Webhook
			var createBody2 model.PayonexWebhookCreateBody
			createBody2.Name = "PAYONEX_LOGEDIN"
			createBody2.JsonPayload = helper.StructJson(data)
			if _, err = repo.CreatePayonexWebhook(createBody2); err != nil {
				log.Println("Error GetPayonexAccessToken.CreatePayonexWebhook", err)
			}
			return accessToken, nil
		}
		return "", internalServerError(err)
	}
	return accessToken.AccessToken, nil
}

func (s paygatePayonexService) CheckPayonexCustomerByUserId(user model.UserBankDetailBody) (*model.PayonexCustomerResponse, error) {

	return CheckPayonexCustomerByUserId(s.repo, user)
}

func CheckPayonexCustomerByUserId(repo repository.PayonexRepository, user model.UserBankDetailBody) (*model.PayonexCustomerResponse, error) {

	pgAccount, err := GetPayonexAccount(repo)
	if err != nil || pgAccount == nil {
		log.Println("CheckPayonexCustomerByUserId.GetPayonexAccount", err)
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}
	// [********] ไม่ต้องเช็ค เพราะต้องเช็ค บช ทั้งฝากและถอน if !pgAccount.IsDepositEnabled {
	// 	return nil, badRequest("PAYGATE_DEPOSIT_NOT_ENABLED")
	// }

	// user, err := repo.GetUserBankDetailById(req.UserId)
	// if err != nil {
	// 	return nil, badRequest("INVALID_USER")
	// }
	// if user == nil || user.BankAccount == "" || user.BankCode == "" {
	// 	return nil, badRequest("INVALID_USER_ACCOUNT")
	// }
	// https://api.payonex.asia/customers/options/bank-codes
	// {
	// 	"success": true,
	// 	"message": "successfully",
	// 	"code": "20000",
	// 	"data": [
	// 		{
	// 			"bank_code": "BBL",
	// 			"bank_name_th": "ธนาคารกรุงเทพ",
	// 			"bank_name_en": "Bangkok Bank"
	// 		},
	// 		{
	// 			"bank_code": "KBANK",
	// 			"bank_name_th": "ธนาคารกสิกรไทย",
	// 			"bank_name_en": "Kasikorn Bank"
	// 		},
	// 		{
	// 			"bank_code": "KTB",
	// 			"bank_name_th": "ธนาคารกรุงไทย",
	// 			"bank_name_en": "Krung Thai Bank"
	// 		},
	// 		{
	// 			"bank_code": "TTB",
	// 			"bank_name_th": "ธนาคารทหารไทยธนชาต",
	// 			"bank_name_en": "TMBThanachart Bank"
	// 		},
	// 		{
	// 			"bank_code": "SCB",
	// 			"bank_name_th": "ธนาคารไทยพาณิชย์",
	// 			"bank_name_en": "Siam Commercial Bank"
	// 		},
	// 		{
	// 			"bank_code": "BAY",
	// 			"bank_name_th": "ธนาคารกรุงศรีอยุธยา",
	// 			"bank_name_en": "Bank of Ayudhya"
	// 		},
	// 		{
	// 			"bank_code": "GSB",
	// 			"bank_name_th": "ธนาคารออมสิน",
	// 			"bank_name_en": "Government Savings Bank"
	// 		},
	// 		{
	// 			"bank_code": "GHB",
	// 			"bank_name_th": "ธนาคารอาคารสงเคราะห์",
	// 			"bank_name_en": "Government Housing Bank"
	// 		},
	// 		{
	// 			"bank_code": "BAAC",
	// 			"bank_name_th": "ธ.ก.ส.",
	// 			"bank_name_en": "Bank for Agriculture and Agricultural Cooperatives"
	// 		},
	// 		{
	// 			"bank_code": "ICBC",
	// 			"bank_name_th": "ธนาคารพาณิชย์อุตสาหกรรมแห่งประเทศจีน",
	// 			"bank_name_en": "Industrial and Commercial Bank of China Limited"
	// 		},
	// 		{
	// 			"bank_code": "UOBT",
	// 			"bank_name_th": "ธนาคารยูโอบี (ไทย)",
	// 			"bank_name_en": "United Overseas Bank"
	// 		},
	// 		{
	// 			"bank_code": "HSBC",
	// 			"bank_name_th": "ฮ่องกงและเซี่ยงไฮ้แบงกิ้งคอร์ปอเรชั่น",
	// 			"bank_name_en": "Hong Kong & Shanghai Corporation"
	// 		},
	// 		{
	// 			"bank_code": "IBANK",
	// 			"bank_name_th": "ธนาคารอิสลามแห่งประเทศไทย",
	// 			"bank_name_en": "Islamic Bank of Thailand "
	// 		},
	// 		{
	// 			"bank_code": "TISCO",
	// 			"bank_name_th": "ธนาคารทิสโก้",
	// 			"bank_name_en": "Tisco Bank"
	// 		},
	// 		{
	// 			"bank_code": "KK",
	// 			"bank_name_th": "ธนาคารเกียรตินาคิน",
	// 			"bank_name_en": "Kiatnakin Bank"
	// 		},
	// 		{
	// 			"bank_code": "TCRB",
	// 			"bank_name_th": "ธนาคารไทยเครดิตเพื่อรายย่อย",
	// 			"bank_name_en": "The Thai Credit Retail Bank"
	// 		},
	// 		{
	// 			"bank_code": "LHB",
	// 			"bank_name_th": "ธนาคารแลนด์แอนด์เฮ้าส์",
	// 			"bank_name_en": "Land and Houses Bank"
	// 		},
	// 		{
	// 			"bank_code": "CIMB",
	// 			"bank_name_th": "ธนาคารซีไอเอ็มบีไทย",
	// 			"bank_name_en": "CIMB Thai"
	// 		}
	// 	]
	// }
	acceptBankCodeList := []string{"BBL", "KBANK", "KTB", "TTB", "SCB", "BAY", "GSB", "GHB", "BAAC", "ICBC", "UOBT", "HSBC", "IBANK", "TISCO", "KK", "TCRB", "LHB", "CIMB"}

	reqBankCode := strings.ToUpper(user.BankCode)
	// rename bank code tb_bank=>PAYONEX
	if reqBankCode == "LH" {
		reqBankCode = "LHB"
	}
	if !helper.StringInArray(strings.ToUpper(reqBankCode), acceptBankCodeList) {
		return nil, badRequest("BANK_CODE_NOT_SUPPORT")
	}
	user.BankCode = reqBankCode

	reqAccountNumber := helper.StripAllButNumbers(user.BankAccount)
	if reqAccountNumber == "" {
		return nil, badRequest("INVALID_ACCOUNT_NUMBER")
	}

	token, err := GetPayonexAccessToken(repo, pgAccount)
	if err != nil {
		// return nil, internalServerError(errors.New("INVALID_ACCESS_TOKEN"))
		return nil, err
	}

	customer, err := repo.GetPayonexCustomerByUserId(user.Id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// Create
			var createBody model.PayonexCustomerCreateRemoteRequest
			createBody.Name = user.Fullname
			createBody.BankCode = user.BankCode
			createBody.AccountNo = user.BankAccount
			createResp, err := repo.PayonexCreateCustomer(token, *pgAccount, createBody)
			if err != nil {
				return nil, err
			}
			var createBody2 model.PayonexCustomerCreateBody
			createBody2.UserId = user.Id
			createBody2.CustomerUuid = createResp.Data.CustomerUuid
			createBody2.FullName = user.Fullname
			createBody2.BankCode = user.BankCode
			createBody2.AccountNo = user.BankAccount
			createBody2.AccountName = user.Fullname
			if _, err := repo.CreatePayonexCustomer(createBody2); err != nil {
				return nil, err
			}
			// REGET
			customer2, err := repo.GetPayonexCustomerByUserId(user.Id)
			if err != nil {
				return nil, err
			}
			return customer2, nil
		}
		return nil, err
	}

	// checkDiff
	if customer.FullName != user.Fullname || customer.BankCode != user.BankCode || customer.AccountNo != user.BankAccount || customer.AccountName != user.Fullname {
		// Update
		var updateBody model.PayonexCustomerUpdateRemoteRequest
		updateBody.CustomerUuid = customer.CustomerUuid
		updateBody.Name = user.Fullname
		updateBody.BankCode = user.BankCode
		updateBody.AccountNo = user.BankAccount
		updateResp, err := repo.PayonexUpdateCustomer(token, *pgAccount, updateBody)
		if err != nil {
			return nil, err
		}
		if updateResp.Success {
			var updateBody2 model.PayonexCustomerUpdateBody
			updateBody2.FullName = &user.Fullname
			updateBody2.BankCode = &user.BankCode
			updateBody2.AccountNo = &user.BankAccount
			updateBody2.AccountName = &user.Fullname
			if err := repo.UpdatePayonexCustomer(customer.Id, updateBody2); err != nil {
				return nil, err
			}
		} else {
			log.Println("CheckPayonexCustomerByUserId.PayonexUpdateCustomer", updateResp)
			return nil, badRequest("UPDATE_CUSTOMER_FAIL")
		}
		// REGET
		customerUpdated, err := repo.GetPayonexCustomerByUserId(user.Id)
		if err != nil {
			return nil, err
		}
		return customerUpdated, nil
	}

	return customer, nil
}

func (s paygatePayonexService) ConfirmDepositTransaction(id int64, req model.BankConfirmDepositRequest) error {

	record, err := s.repo.GetBankTransactionById(id)
	if err != nil {
		return internalServerError(err)
	}
	if record.TransactionStatusId != model.TRANS_STATUS_PENDING {
		return badRequest("Transaction is not pending")
	}
	if record.TransactionTypeId != model.TRANSACTION_TYPE_DEPOSIT && record.TransactionTypeId != model.TRANSACTION_TYPE_BONUS {
		return badRequest("Transaction is not deposit")
	}
	jsonBefore, _ := json.Marshal(record)

	var updateData model.BankDepositTransactionConfirmBody
	updateData.TransactionStatusId = model.TRANS_STATUS_DEPOSIT_PENDING_CREDIT
	updateData.ConfirmedAt = req.ConfirmedAt
	updateData.ConfirmedByAdminId = req.ConfirmedByAdminId

	// if record.IsAutoCredit && record.TransferAt != nil {
	// 	seconds := time.Now().UTC().Sub(record.TransferAt.UTC()).Seconds()
	// 	autoProcessTimer := fmt.Sprintf("%.2f", seconds)
	// 	updateData.AutoProcessTimer = &autoProcessTimer
	// }

	var createBody model.CreateBankTransactionActionBody
	createBody.ActionKey = fmt.Sprintf("DCF_STATE#%d", record.Id)
	createBody.TransactionId = record.Id
	createBody.UserId = record.UserId
	createBody.TransactionTypeId = record.TransactionTypeId
	createBody.FromAccountId = record.FromAccountId
	createBody.ToAccountId = record.ToAccountId
	createBody.JsonBefore = string(jsonBefore)
	if req.TransferAt == nil {
		createBody.TransferAt = record.TransferAt
	} else {
		TransferAt := req.TransferAt
		createBody.TransferAt = TransferAt
		updateData.TransferAt = *TransferAt
	}
	if req.SlipUrl != nil {
		createBody.SlipUrl = *req.SlipUrl
	}
	createBody.CreditAmount = record.CreditAmount
	if req.BonusAmount != nil {
		createBody.BonusAmount = *req.BonusAmount
	}
	createBody.ConfirmedAt = req.ConfirmedAt
	createBody.ConfirmedByAdminId = req.ConfirmedByAdminId
	if actionId, err := s.repo.CreateTransactionAction(createBody); err == nil {
		// do nothing ?
		if err := s.repo.ConfirmPendingDepositTransaction(id, updateData); err != nil {
			if err := s.repo.RollbackTransactionAction(*actionId); err == nil {
				return internalServerError(err)
			}
			return internalServerError(err)
		}
	} else {
		return internalServerError(err)
	}
	return nil
}

func (s paygatePayonexService) CreatePayonexWebhook(req model.PayonexWebhookRequest) (*int64, error) {

	var createBody model.PayonexWebhookCreateBody
	createBody.Name = "PAYONEX_CALLBACK"
	createBody.JsonPayload = req.JsonPayload
	insertId, err := s.repo.CreatePayonexWebhook(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// DEPOSIT
	// {
	// 	"uuid": "165c19c0-3435-42fd-a500-50f18c5cc485",
	// 	"customerUuid": "1cddb474-010f-43c1-83dd-9abf34651a30",
	// 	"accountName": "เอาไว้เล่นวันที่ 16",
	// 	"bankCode": "KTB",
	// 	"accountNo": "**********",
	// 	"amount": 50,
	// 	"fee": 0.75,
	// 	"settleAmount": 49.25,
	// 	"type": "deposit",
	// 	"status": "SUCCESS",
	// 	"referenceId": "PGWAY240722"
	// }
	// {
	// 	"uuid": "3fd01438-0241-4a3c-9890-e2e6f7cfabdf",
	// 	"customerUuid": "aaeba791-0923-4582-863a-2c7e2e3f1627",
	// 	"accountName": "ชนิกานต์ ตังกบดี",
	// 	"bankCode": "KBANK",
	// 	"accountNo": "**********",
	// 	"amount": 20,
	// 	"fee": 0.3,
	// 	"settleAmount": 19.7,
	// 	"type": "deposit",
	// 	"status": "SUCCESS",
	// 	"referenceId": "PGWAY240741"
	// }

	// {
	// 	"uuid": "bdac40f6-9c92-40ed-ab98-3fa062370859",
	// 	"customerUuid": "aaeba791-0923-4582-863a-2c7e2e3f1627",
	// 	"accountName": "ชนิกานต์ ตังกบดี",
	// 	"bankCode": "KBANK",
	// 	"accountNo": "**********",
	// 	"amount": 501,
	// 	"fee": 1.5,
	// 	"settleAmount": "502.50",
	// 	"type": "withdraw",
	// 	"status": "SUCCESS",
	// 	"referenceId": "REF000001W"
	// }
	var remoteResp model.PayonexDepositWebhookResponse
	errJson := json.Unmarshal([]byte(req.JsonPayload), &remoteResp)
	if errJson != nil {
		log.Println("Error unmarshal json", errJson)
		return nil, internalServerError(errJson)
	}

	if remoteResp.CustomerUuid == "" || remoteResp.Status == "" {
		return nil, internalServerError(fmt.Errorf("INVALID_REFERENCE"))
	}

	// Service Race Condition by Ref1
	var rcCreateBody model.RaceActionCreateBody
	rcCreateBody.Name = "CreatePayonexWebhook"
	rcCreateBody.JsonRequest = helper.StructJson(req)
	rcCreateBody.Status = "PENDING"
	// ActionKey = Ref2-MINUTE
	rcCreateBody.ActionKey = fmt.Sprintf("%s-%s", remoteResp.Uuid, time.Now().Format("**********"))
	rcCreateBody.UnlockAt = time.Now().UTC().Add(time.Second * 1)
	if _, err := s.repo.CreateRaceCondition(rcCreateBody); err != nil {
		return insertId, internalServerError(err)
	}

	// Get Posible Order
	var query model.PayonexOrderListRequest
	query.OrderNo = remoteResp.ReferenceId
	query.TransactionNo = remoteResp.Uuid // "TODO NO CALLBACK REF"
	query.Amount = &remoteResp.Amount
	query.Status = "WAIT_PAYMENT"
	query.Limit = 1
	list, _, err := s.repo.GetDbPayonexOrderList(query)
	if err != nil {
		return nil, internalServerError(err)
	}

	if len(list) > 0 {
		for _, item := range list {
			// Update Order
			successStatus := strings.ToUpper(remoteResp.Status)
			if successStatus == "SUCCESS" {
				successStatus = "PAID"
			}
			if err := s.repo.ApproveDbPayonexOrder(item.Id, successStatus); err != nil {
				return nil, internalServerError(err)
			}
			// Payment Success, Create Deposit if UserId is not null
			if item.UserId > 0 && item.OrderTypeId == model.PAYONEX_ORDER_TYPE_DEPOSIT && strings.ToLower(remoteResp.Type) == "deposit" {
				if successStatus == "PAID" && remoteResp.Amount > 0 {
					if _, err := createCustomerDepositFromPayonex(s.repo, item, 0); err != nil {
						// WebhookLog
						var createBody2 model.PayonexWebhookCreateBody
						createBody2.Name = "PAYONEX_DEPOSIT_ERROR"
						createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
							"item":   item,
							"method": "createCustomerDepositFromPayonex",
							"error":  err.Error(),
						})
						if _, err = s.repo.CreatePayonexWebhook(createBody2); err != nil {
							log.Println("Error CreatePayonexWebhook.createCustomerDepositFromPayonex", err)
						}
					}
				}
			} else if item.UserId > 0 && item.OrderTypeId == model.PAYONEX_ORDER_TYPE_WITHDRAW && strings.ToLower(remoteResp.Type) == "withdraw" {
				if successStatus == "PAID" && remoteResp.Amount > 0 {
					if _, err := approveCustomerWithdrawFromPayonex(s.repo, *item.RefId); err != nil {
						// WebhookLog
						var createBody2 model.PayonexWebhookCreateBody
						createBody2.Name = "PAYONEX_WITHDRAW_ERROR"
						createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
							"item":   item,
							"method": "approveCustomerWithdrawFromPayonex",
							"error":  err.Error(),
						})
						if _, err = s.repo.CreatePayonexWebhook(createBody2); err != nil {
							log.Println("Error approveCustomerWithdrawFromPayonex.CreatePayonexWebhook", err)
						}
					}
				} else if successStatus == "ERROR" && remoteResp.Amount > 0 {
					if err := s.cancelWithdrawFromPayonexWebhookError(item); err != nil {
						log.Println("Error UpdateDbPayonexOrderError", err)
					}
				}
			} else {
				log.Println("ApproveDbPayonexOrder.WithoutItemUpdated", helper.StructJson(item))
			}
		}
	}
	return insertId, nil
}

func (s paygatePayonexService) GetPayonexWebDepositAccount() (*model.PayonexCustomerDepositInfo, error) {

	var result model.PayonexCustomerDepositInfo

	pgAccount, err := s.GetPayonexAccount()
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}
	if !pgAccount.IsDepositEnabled {
		return nil, badRequest("PAYGATE_DEPOSIT_NOT_ENABLED")
	}

	result.Name = pgAccount.Name
	if pgAccount.DisplayName == "" {
		result.Name = "เติมเงินผ่าน QR Code"
	} else {
		result.DisplayName = pgAccount.DisplayName
	}
	result.ShopName = pgAccount.ShopName
	result.MinAmount = 100
	result.MaxAmount = 200000

	return &result, nil
}

func (s paygatePayonexService) GetPayonexAccount() (*model.PaygateAccountResponse, error) {

	return GetPayonexAccount(s.repo)
}

func GetPayonexAccount(repo repository.PayonexRepository) (*model.PaygateAccountResponse, error) {

	pgAccount, err := repo.GetPaygateAccountByProviderId(model.PAYGATE_MERCHANT_ID_PAYONEX)
	if err != nil {
		return nil, internalServerError(err)
	}
	return pgAccount, nil
}

func (s paygatePayonexService) CreatePayonexDeposit(req model.PayonexDepositCreateRequest) (*model.PayonexOrderWebResponse, error) {

	var result model.PayonexOrderWebResponse

	pgAccount, err := s.GetPayonexAccount()
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}
	if !pgAccount.IsDepositEnabled {
		return nil, badRequest("PAYGATE_DEPOSIT_NOT_ENABLED")
	}

	// [********] get previse deposit order in last 5 minutes
	if pOrder, err := s.repo.CheckPayonexDepositOrderInLast5Minutes(req.UserId, req.Amount); err != nil {
		if err != gorm.ErrRecordNotFound {
			return nil, internalServerError(err)
		}
	} else if pOrder != nil {
		actionAtUtc := time.Now().UTC()
		if pOrder.CreatedAt.Add(time.Minute * 5).After(actionAtUtc) {
			result.UserId = pOrder.UserId
			result.OrderNo = pOrder.OrderNo
			result.Amount = pOrder.Amount
			result.TransferAmount = pOrder.TransferAmount
			result.TransactionStatus = *pOrder.TransactionStatus
			result.QrCode = pOrder.QrPromptpay
			result.CreatedAt = pOrder.CreatedAt

			imgData, err := qrcode.Encode(pOrder.QrPromptpay, qrcode.Medium, 256)
			if err != nil {
				// return nil, fmt.Errorf("unable to encode png: %w", err)
				return &result, nil
			}
			// encode to base64
			img, err := png.Decode(bytes.NewReader(imgData))
			if err != nil {
				// return nil, fmt.Errorf("unable to decode jpeg: %w", err)
				return &result, nil
			}
			var buf bytes.Buffer
			if err := png.Encode(&buf, img); err != nil {
				// return nil, fmt.Errorf("unable to encode png: %w", err)
				return &result, nil
			}
			result.QrBase64 = base64.StdEncoding.EncodeToString(buf.Bytes())
			return &result, nil
		}
	}

	user, err := s.repo.GetUserBankDetailById(req.UserId)
	if err != nil {
		return nil, badRequest("INVALID_USER")
	}
	if user == nil || user.BankAccount == "" || user.BankCode == "" {
		return nil, badRequest("INVALID_USER_ACCOUNT")
	}

	accountingRepo := repository.NewAccountingRepository(s.repo.GetDb())
	configWeb, err := s.repo.GetWebConfiguration()
	if err != nil {
		log.Println("CreatePayonexDeposit.GetWebConfiguration", err)
		return nil, badRequest("WEB_CONFIG_NOT_FOUND")
	}
	if user.Id != 0 && configWeb != nil {
		// [********] ฝากครั้งแรกขั้นต่ำ ปรับ flow เช็ค user ให้เช็คจากรายการฝากครั้งแรกจริงๆ ไม่เช็คจากรหัสที่ได้รับ
		if err := CheckFirstMinimunDeposit(accountingRepo, user.Id, req.Amount, *configWeb); err != nil {
			return nil, badRequest(fmt.Sprintf("ฝากครั้งแรกขั้นต่ำ %d บาท", configWeb.MinFirstMemberDeposit))
		}
	} else {
		log.Println("CreatePayonexDeposit.CheckFirstMinimunDeposit", "CANT_CHECK_FIRST_MINIMUM_DEPOSIT_ON_EMPTY_CONFIG")
	}

	token, err := s.GetPayonexAccessToken(pgAccount)
	if err != nil {
		log.Println("CreatePayonexDeposit.GetPayonexAccessToken.ERROR", err)
		return nil, err
	}

	// PayonexUser
	payonexCustomer, err := s.CheckPayonexCustomerByUserId(*user)
	if err != nil {
		log.Println("CreatePayonexDeposit.CheckPayonexCustomerByUserId", err)
		return nil, badRequest("INVALID_PAYGATE_USER")
	}
	if payonexCustomer.CustomerUuid == "" {
		log.Println("CreatePayonexDeposit.CheckPayonexCustomerByUserId", "EMPTY_PAYGATE_USER_UUID")
		return nil, badRequest("EMPTY_PAYGATE_USER")
	}

	// ===========================================================================================
	var createBody model.PayonexOrderCreateBody
	createBody.UserId = req.UserId
	createBody.OrderTypeId = model.PAYONEX_ORDER_TYPE_DEPOSIT
	createBody.OrderNo = "" // Autogen
	createBody.Amount = req.Amount
	createBody.TransactionStatus = "PENDING"
	insertId, err := s.repo.CreateDbPayonexOrder(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	// reget Order
	pendingOrder, err := s.repo.GetDbPayonexOrderById(*insertId)
	if err != nil {
		// SET AS ERROR
		remark := "Error GetDbPayonexOrderById, " + err.Error()
		if err := s.repo.UpdateDbPayonexOrderError(*insertId, remark); err != nil {
			log.Println("CreatePayonexDeposit.UpdateDbPayonexOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// Create PAYONEX Order
	var remoteRequest model.PayonexDepositCreateRemoteRequest
	remoteRequest.CustomerUuid = payonexCustomer.CustomerUuid
	remoteRequest.ReferenceId = pendingOrder.OrderNo
	remoteRequest.Amount = pendingOrder.Amount
	remoteRequest.Note = "ManualDeposit"
	remoteRequest.Remark = ""
	remoteResp, err := s.repo.PayonexDeposit(token, *pgAccount, remoteRequest)
	if err != nil {
		// SET AS ERROR
		remark := "Error PayonexDeposit, " + err.Error()
		if err := s.repo.UpdateDbPayonexOrderError(*insertId, remark); err != nil {
			log.Println("PayonexDeposit.UpdateDbPayonexOrderError", err)
		}
		// SysLog
		if _, err := s.repo.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "CreatePayonexDeposit.PayonexDeposit",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(remoteRequest),
			JsonResponse: helper.StructJson(map[string]interface{}{"error": err.Error()}),
		}); err != nil {
			log.Println("CreatePayonexDeposit.CreateSysLog", err)
		}
		return nil, internalServerError(err)
	}

	fmt.Println("PayonexDeposit.remoteResp", helper.StructJson(remoteResp))

	// onCreate Success
	var updateBody model.PayonexOrderUpdateBody
	updateBody.TransactionNo = remoteResp.Data.Uuid
	updateBody.TransactionDate = time.Now()
	updateBody.TransactionStatus = "WAIT_PAYMENT"
	updateBody.TransferAmount = remoteResp.Data.TransferAmount
	updateBody.QrPromptpay = remoteResp.Data.QrCode
	if err := s.repo.UpdateDbPayonexOrder(*insertId, updateBody); err != nil {
		// SET AS ERROR
		remark := "Error UpdateDbPayonexOrder, " + err.Error()
		if err := s.repo.UpdateDbPayonexOrderError(*insertId, remark); err != nil {
			log.Println("CreatePayonexDeposit.UpdateDbPayonexOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	waitPayOrder, err := s.repo.GetDbPayonexOrderById(*insertId)
	if err != nil {
		return nil, internalServerError(err)
	}

	result.UserId = waitPayOrder.UserId
	result.OrderNo = waitPayOrder.OrderNo
	result.Amount = waitPayOrder.Amount
	result.TransferAmount = remoteResp.Data.TransferAmount
	result.TransactionStatus = *waitPayOrder.TransactionStatus
	result.QrCode = waitPayOrder.QrPromptpay
	result.CreatedAt = waitPayOrder.CreatedAt

	imgData, err := qrcode.Encode(waitPayOrder.QrPromptpay, qrcode.Medium, 256)
	if err != nil {
		// return nil, fmt.Errorf("unable to encode png: %w", err)
		return &result, nil
	}
	// encode to base64
	img, err := png.Decode(bytes.NewReader(imgData))
	if err != nil {
		// return nil, fmt.Errorf("unable to decode jpeg: %w", err)
		return &result, nil
	}
	var buf bytes.Buffer
	if err := png.Encode(&buf, img); err != nil {
		// return nil, fmt.Errorf("unable to encode png: %w", err)
		return &result, nil
	}
	result.QrBase64 = base64.StdEncoding.EncodeToString(buf.Bytes())

	return &result, nil
}

func (s paygatePayonexService) CreatePayonexWithdraw(req model.PayonexWithdrawCreateRequest) (*int64, error) {

	pgWdAccount, err := s.GetPayonexAccount()
	if err != nil || pgWdAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}
	if !pgWdAccount.IsWithdrawEnabled {
		return nil, badRequest("PAYGATE_WITHDRAW_NOT_ENABLED")
	}

	user, err := s.repo.GetUserBankDetailById(req.UserId)
	if err != nil {
		return nil, badRequest("INVALID_USER")
	}
	if user == nil || user.BankAccount == "" || user.BankCode == "" {
		return nil, badRequest("INVALID_USER_ACCOUNT")
	}

	token, err := s.GetPayonexAccessToken(pgWdAccount)
	if err != nil {
		log.Println("CreatePayonexWithdraw.GetPayonexAccessToken.ERROR", err)
		return nil, err
	}

	// PayonexUser
	payonexCustomer, err := s.CheckPayonexCustomerByUserId(*user)
	if err != nil {
		log.Println("CreatePayonexWithdraw.CheckPayonexCustomerByUserId", err)
		return nil, badRequest("INVALID_PAYGATE_USER")
	}
	if payonexCustomer.CustomerUuid == "" {
		log.Println("CreatePayonexWithdraw.CheckPayonexCustomerByUserId", "EMPTY_PAYGATE_USER_UUID")
		return nil, badRequest("EMPTY_PAYGATE_USER")
	}

	// todo check User info VS CustomerInfo

	// ===========================================================================================
	// CREATE Order
	var createBody model.PayonexOrderCreateBody
	createBody.RefId = &req.RefId
	createBody.UserId = req.UserId
	createBody.OrderTypeId = model.PAYONEX_ORDER_TYPE_WITHDRAW
	createBody.OrderNo = "" // Autogen
	createBody.Amount = req.Amount
	createBody.TransactionStatus = "PENDING"
	insertId, err := s.repo.CreateDbPayonexOrder(createBody)
	if err != nil {
		return nil, internalServerError(err)
	}

	// ===========================================================================================
	// reget Order
	pendingOrder, err := s.repo.GetDbPayonexOrderById(*insertId)
	if err != nil {
		// SET AS ERROR
		remark := "Error GetDbPayonexOrderById, " + err.Error()
		if err := s.repo.UpdateDbPayonexOrderError(*insertId, remark); err != nil {
			log.Println("CreatePayonexWithdraw.UpdateDbPayonexOrderError", err)
		}
		return nil, internalServerError(err)
	}

	// Create PAYONEX Order
	var remoteRequest model.PayonexWithdrawCreateRemoteRequest
	remoteRequest.CustomerUuid = payonexCustomer.CustomerUuid
	remoteRequest.ReferenceId = pendingOrder.OrderNo
	remoteRequest.Amount = pendingOrder.Amount
	remoteRequest.Note = "ManualWithdrawal"
	remoteRequest.Remark = ""
	remoteResp, err := s.repo.PayonexWithdraw(token, *pgWdAccount, remoteRequest)
	if err != nil {
		// SET AS ERROR
		remark := "Error PayonexWithdraw, " + err.Error()
		if err := s.repo.UpdateDbPayonexOrderError(*insertId, remark); err != nil {
			log.Println("PayonexWithdraw.UpdateDbPayonexOrderError", err)
		}
		// SysLog
		if _, err := s.repo.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "CreatePayonexWithdraw.PayonexWithdraw",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(remoteRequest),
			JsonResponse: helper.StructJson(map[string]interface{}{"error": err.Error()}),
		}); err != nil {
			log.Println("CreatePayonexWithdraw.CreateSysLog", err)
		}
		return nil, internalServerError(err)
	}

	fmt.Println("CreatePayonexWithdraw.remoteResp", helper.StructJson(remoteResp))

	if !remoteResp.Success || remoteResp.Data.Uuid == "" {
		// SET AS ERROR
		remark := remoteResp.Message
		if len(remark) == 0 {
			remark = "UNKNOW_ERROR from withdrawWithPayonex"
		}
		if err := s.repo.UpdateDbPayonexOrderError(*insertId, remark); err != nil {
			log.Println("PayonexWithdraw.UpdateDbPayonexOrderError", err)
		}
		// SysLog
		if _, err := s.repo.CreatePaygateSystemLog(model.PaygateSystemLogCreateBody{
			Name:         "CreatePayonexWithdraw.PayonexWithdraw",
			Status:       "ERROR",
			JsonReq:      helper.StructJson(remoteRequest),
			JsonResponse: helper.StructJson(remoteResp),
		}); err != nil {
			log.Println("CreatePayonexWithdraw.CreateSysLog", err)
		}
		return nil, internalServerError(fmt.Errorf("UNKNOW_ERROR"))
	}

	// onCreate Success
	var updateBody model.PayonexOrderUpdateBody
	updateBody.TransactionNo = remoteResp.Data.Uuid
	updateBody.TransactionDate = time.Now()
	updateBody.TransactionStatus = "WAIT_PAYMENT"
	updateBody.QrPromptpay = remoteResp.Message
	if err := s.repo.UpdateDbPayonexOrder(*insertId, updateBody); err != nil {
		// SET AS ERROR
		remark := "Error UpdateDbPayonexOrder, " + err.Error()
		if err := s.repo.UpdateDbPayonexOrderError(*insertId, remark); err != nil {
			log.Println("CreatePayonexWithdraw.UpdateDbPayonexOrderError", err)
		}
		return nil, internalServerError(err)
	}
	return insertId, nil
}

func CreateCustomerDepositFromPayonexOrder(repo repository.PayonexRepository, orderId int64, adminId int64) (*int64, error) {

	// ** ทุกรายการจะต้องเช็คสถานะใหม่ทั้งหมด เพราะ admin แย่งกดอนุมัติได้
	item, err := repo.GetRawPayonexPendingDepositOrderById(orderId)
	if err != nil {
		if err.Error() == recordNotFound {
			return nil, badRequest("ORDER_NOT_FOUND")
		}
		return nil, internalServerError(err)
	}

	// Check BankTransactionStatus = 'PENDING'
	if item.BankTransactionStatus != "PENDING" {
		return nil, badRequest("ORDER_NOT_PENDING")
	}

	return createCustomerDepositFromPayonex(repo, *item, adminId)
}

func createCustomerDepositFromPayonex(repo repository.PayonexRepository, item model.PayonexOrderResponse, adminId int64) (*int64, error) {

	var externalNoti model.NotifyExternalNotificationRequest
	actionAt := time.Now()
	// confirmByUserId := int64(0)

	depositAmount := item.Amount

	user, err := repo.GetUserBankDetailById(item.UserId)
	if err != nil {
		log.Println("createCustomerDepositFromPayonex.GetUserBankDetailByMemberCode", err)
		return nil, internalServerError(err)
	}

	var promotionWebUserId int64
	if getPromotionWebUser, err := repo.GetDepositCurrentProcessingUserPromotion(item.UserId); err != nil {
		log.Println("createCustomerDepositFromPayonex.GetDepositCurrentProcessingUserPromotion", err)
	} else if getPromotionWebUser != nil {
		promotionWebUserId = getPromotionWebUser.Id
	}

	// MemberCode
	if user.MemberCode == "" {
		memberCode, err := GenUniqueUserMemberCode(repository.NewUserRepository(repo.GetDb()), user.Id)
		if err != nil {
			log.Println("createCustomerDepositFromPayonex.GenUniqueUserMemberCode", err)
			return nil, internalServerError(err)
		}
		user.MemberCode = *memberCode
	}

	// layer — 2024-04-02 at 10:06 AM
	// ถ้าแก้ตามนี้แล้วน่าจะไม่เจอ ถ้าเคสกันซ้ำเราครอบคลุม
	// @Dunk @TULA
	// กันซ้ำคือ heng ไปซ้ำกับแอดมินเติมมือ
	// ----
	// เงื่อนไขกันซ้ำ
	// 1 หารายการฝากสำเร็จล่าสุดของยูสนั้น
	// 2 ถ้ามีรายการล่าสุดที่เป็นเติมมือ
	// -เช็คเวลาเติม ต้องมากกว่าเวลาโอนในสลิป
	// -ยอดเงินเท่ากัน
	// **ถ้าเข้าเงื่อนไขทั้งหมด คือแสดงว่า webhook เข้าทีหลังแอดมินเติมมือ
	// ต้อง bypass ไม่ให้เติมอีก และอัพเดท status ว่าจับคู่ได่แล้ว
	// ---
	// วิธีนี้น่าจะใช้ได้กับทุก webhook (heng, fastbank) ที่มีเวลาโอนสลิปมาให้
	duplicateRemark := "INIT"
	hasDuplicateDeposit := false
	manualDeposit, err := repo.GetLastestBankAdminManualDepositTransaction(user.Id)
	if err != nil {
		if err.Error() != recordNotFound {
			log.Println("createCustomerDepositFromPayonex.GetLastestBankAdminManualDepositTransaction", err)
			return nil, internalServerError(err)
		}
		// NOT_FOUND = ถ้าไม่เจอ ให้ทำต่อ
		duplicateRemark = "OK_NO_MANUAL_DEPOSIT"
	}
	if manualDeposit != nil && item.TransactionDate != nil {
		if manualDeposit.TransferAt.After(*item.TransactionDate) {
			// ถ้าเวลาเติมมือมากกว่าเวลาโอนในสลิป
			if manualDeposit.CreditAmount == depositAmount {
				// ถ้ายอดเงินเท่ากัน
				duplicateRemark = "STOP_MANUAL_DEPOSIT_AFTER_AND_SAME_AMOUNT"
				hasDuplicateDeposit = true
			} else {
				duplicateRemark = "OK_MANUAL_DEPOSIT_AMOUNT_NOT_MATCH"
			}
		} else {
			duplicateRemark = "OK_MANUAL_DEPOSIT_BEFORE_WITHDRAW"
		}
	}
	if duplicateRemark != "INIT" {
		// WebhookLog
		var createBody2 model.PayonexWebhookCreateBody
		createBody2.Name = "PAYONEX_DEPOSIT_DUPLICATE_CHECKER"
		createBody2.JsonPayload = helper.StructJson(map[string]interface{}{
			"item":                item,
			"method":              "createCustomerDeposit",
			"hasDuplicateDeposit": hasDuplicateDeposit,
			"error":               duplicateRemark,
			"manualDeposit":       manualDeposit,
		})
		if _, err = repo.CreatePayonexWebhook(createBody2); err != nil {
			log.Println("Error CreatePayonexWebhook.CreatePayonexWebhook", err)
		}
		if hasDuplicateDeposit {
			return nil, internalServerError(fmt.Errorf("DUPLICATE_DEPOSIT"))
		}
	}

	pgAccount, err := GetPayonexAccount(repo)
	if err != nil || pgAccount == nil {
		return nil, badRequest("PAYGATE_PAYMENT_NOT_ENABLED")
	}

	// ===================================================
	paygateRepo := repository.NewPaymentGatewayRepository(repo.GetDb())
	accountingRepo := repository.NewAccountingRepository(repo.GetDb())
	luckyWheelRepo := repository.NewActivityLuckyWheelRepository(repo.GetDb())
	promotionWebRepo := repository.NewPromotionWebRepository(repo.GetDb())
	notiRepo := repository.NewNotificationRepository(repo.GetDb())

	// Affiliate + Alliance Income
	member, err := repo.GetMemberById(user.Id)
	if err != nil {
		return nil, internalServerError(err)
	}
	if member.UserTypeName == "NONE" {
		// ถ้าฝากครั้งแรก จะได้เป็น AFFILIATE + REFER COMMISION
		if err := UserFirstDepositCommission(accountingRepo, *member, depositAmount); err != nil {
			log.Println("CreateFristTimeDepositRecord.UserFirstDepositCommission.ERROR", err)
			return nil, internalServerError(err)
		}
	}

	isAutoDeposit := true // Auto ฝาก นับตั้งแต่ได้รับยอดฝาก จนได้ยืนยันเครดิต
	if adminId != 0 {
		isAutoDeposit = false
	}

	// CREATE BANK TRANSACTION
	var createBankTransaction model.BankTransactionCreateBody
	createBankTransaction.MemberCode = user.MemberCode
	createBankTransaction.UserId = user.Id
	// createBankTransaction.StatementId = 0
	createBankTransaction.TransactionTypeId = model.TRANSACTION_TYPE_DEPOSIT
	// createBankTransaction.FromAccountName = "" // empty?
	createBankTransaction.ToAccountName = &pgAccount.TypeName
	createBankTransaction.ToAccountNumber = &pgAccount.ShopName
	createBankTransaction.PromotionId = &promotionWebUserId
	createBankTransaction.CreditAmount = depositAmount
	// createBankTransaction.BonusAmount = 0
	// createBankTransaction.BonusReason = nil
	createBankTransaction.DepositChannel = "PAYONEX PAYMENT GATEWAY"
	createBankTransaction.TransactionStatusId = model.TRANS_STATUS_PENDING
	createBankTransaction.CreatedByAdminId = 0
	createBankTransaction.IsAutoCredit = isAutoDeposit
	createBankTransaction.TransferAt = &actionAt
	// create transaction
	transId, err := repo.InsertBankTransaction(createBankTransaction)
	if err != nil {
		log.Println("createCustomerDepositFromPayonex.InsertBankTransaction", err)
		return nil, internalServerError(err)
	}

	// var actionStatement model.BankStatementMatchRequest
	// actionStatement.StatementId = *bankstatementId
	// actionStatement.UserId = user.Id
	// actionStatement.ConfirmedAt = time
	// actionStatement.ConfirmedByAdminId = &req.CreateByUserId
	// if err := s.serviceAccounting.SetStatementOwnerMatched(*bankstatementId, actionStatement, model.USE_ENDING_NOTI); err != nil {
	// 	log.Println("createCustomerDepositFromPayonex.SetStatementOwnerMatched", err)
	// 	return nil, internalServerError(err)
	// }

	// DECREASE FASTBANK CREDIT 2.FROM create auto DEPOSIT
	// if err := repoAccounting.DecreaseFastbankCredit(1); err != nil {
	// 	log.Println("createCustomerDepositFromPayonex.DECREASE_FASTBANK_CREDIT_ERROR", err)
	// }

	var confirmDeposit model.BankConfirmDepositRequest
	confirmDeposit.TransferAt = &actionAt
	confirmDeposit.ConfirmedAt = actionAt
	confirmDeposit.ConfirmedByAdminId = &adminId
	if err := confirmDepositTransaction(paygateRepo, *transId, confirmDeposit); err != nil {
		log.Println("createCustomerDepositFromPayonex.confirmDepositTransaction", err)
		return nil, internalServerError(err)
	}

	// UPDATE HENG ORDER - BANK SUCCESS
	if err := repo.UpdatePayonexOrderBankSucess(item.Id, *transId, adminId); err != nil {
		log.Println("createCustomerDepositFromPayonex.UpdatePaygateOrderBankSucess", err)
	}

	isFirstDeposit := repo.IsFirstDeposit(user.Id)
	if isFirstDeposit {
		var bonusReq model.UserFirstDepositCreateRequest
		bonusReq.UserId = user.Id
		bonusReq.TransactionId = transId
		bonusReq.TransferAt = actionAt
		bonusReq.Amount = depositAmount
		bonusReq.Remark = "createCustomerDepositFromPayonex"
		if err := SetFirstDepositBonus(accountingRepo, isFirstDeposit, bonusReq); err != nil {
			webhookLogMessage := fmt.Sprintf("createCustomerDepositFromPayonex.userFirstDepositBonus, ERROR: %s", err)
			log.Println("SetFirstDepositBonus", webhookLogMessage)
			// return nil, errors.New(webhookLogMessage)
		}
	}

	// [USER_CREDIT]
	var userCreditReq model.UserTransactionCreateRequest
	userCreditReq.UserId = user.Id
	userCreditReq.RefId = transId
	userCreditReq.TransferAt = &actionAt
	userCreditReq.PromotionId = &promotionWebUserId
	userCreditReq.TypeId = model.CREDIT_TYPE_DEPOSIT
	// userCreditReq.AccountId = &bankAccount.Id
	userCreditReq.Detail = "PAYONEX PAYMENT GATEWAY"
	userCreditReq.Amount = depositAmount
	userCreditReq.CreateBy = &adminId
	userCreditReq.ConfirmBy = &adminId
	userCreditReq.IsAdjustAuto = isAutoDeposit
	userCreditReq.PaymentMerchatId = &pgAccount.ProviderId
	if agentResp, err := repo.IncreaseUserCredit(userCreditReq); err != nil {
		log.Println("createCustomerDepositFromPayonex.IncreaseUserCredit", err)
		return nil, internalServerError(err)
	} else {
		// AGENT_SUCCESS
		if err := repo.UpdateDepositTransactionStatusFromAgent(*transId, *agentResp); err != nil {
			log.Println("createCustomerDepositFromPayonex.UpdateDeporsitTransactionStatusFromAgent", err)
		}
	}

	// [Lucky Wheel] ตอนฝากละได้โบนัส
	var luckyWheelBody model.ActivityLuckyWheelRoundUserRequest
	luckyWheelBody.UserId = user.Id
	luckyWheelBody.ConditionId = model.ACTIVITY_LUCKY_WHEEL_SETTING_CONDITION_DEPOSIT
	luckyWheelBody.ConditionAmount = depositAmount
	if err := CreateRoundActivityLuckyWheel(luckyWheelRepo, luckyWheelBody); err != nil {
		log.Println("createCustomerDepositFromPayonex.CreateRoundActivityLuckyWheel", err)
	}

	// [TIER]
	if err := repo.IncreaseUserTierDepositAmount(user.Id, depositAmount); err != nil {
		log.Println("MatchStatementOwner.IncreaseUserTierDepositAmount", err)
	}

	var checkUserPromotionBody model.CheckUserPromotionBody
	checkUserPromotionBody.UserId = user.Id
	_, err = CheckUserPromotion(promotionWebRepo, checkUserPromotionBody)
	if err != nil {
		log.Println("createCustomerDepositFromPayonex.CheckUserPromotion", err)
	}
	// [turnOver] ถ้ามีการฝากเงิน ให้สร้าง TurnOver
	turnoverShare := repository.NewTurnoverRepository(repo.GetDb())
	if err := CreateTurnOverFromSuccessDeposit(turnoverShare, user.Id, depositAmount, *transId); err != nil {
		log.Println("createCustomerDeposit.CreateTurnOverFromSuccessDeposit", err)
	}

	// ===================================================

	// [ notify]
	externalNoti.TypeNotify = model.IsDepositAfterCredit
	externalNoti.TransId = transId
	externalNoti.Amount = depositAmount
	externalNoti.MemberCode = user.MemberCode
	externalNoti.UserCredit = user.Credit + depositAmount
	externalNoti.ConfirmedByAdminId = 0
	externalNoti.TransferDateTime = time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02 15:04:05")
	externalNoti.ActionTime = time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02 15:04:05")
	endTime := time.Now()
	elapsed := endTime.Sub(actionAt)
	elapsedSeconds := elapsed.Seconds()
	timeElapsed := fmt.Sprintf("%.2f", elapsedSeconds)
	externalNoti.TimerCounter = timeElapsed

	externalNoti.WebScoket.UserID = user.Id
	externalNoti.WebScoket.Amount = depositAmount
	externalNoti.WebScoket.MemberCode = user.MemberCode
	externalNoti.WebScoket.AlertType = "DEPOSIT"
	if err := repo.UpdateAutoProcessTimer(timeElapsed, *transId); err != nil {
		return nil, nil
	}
	if err := ExternalNotification(notiRepo, externalNoti); err != nil {
		log.Println("FailedNotify", err)
	}
	return nil, nil
}

func approveCustomerWithdrawFromPayonex(repo repository.PayonexRepository, transId int64) (*int64, error) {

	withdrawTrans, err := repo.GetBankTransactionById(transId)
	if err != nil {
		log.Println("approveCustomerWithdrawFromPayonex.GetUserBankDetailById", err)
		return nil, internalServerError(err)
	}

	// ============================= ON_SUCCESS =================================
	if withdrawTrans.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		// [create transaction action success]
		var createConfirm model.CreateSuccessTransferWithdrawRequest
		createConfirm.TransactionId = withdrawTrans.Id
		createConfirm.ConfirmedAt = time.Now()
		var setIdAuto int64 = 0
		createConfirm.ConfirmedByAdminId = &setIdAuto
		if _, err := CreateSuccessTransferWithdraw(repository.NewBankingRepository(repo.GetDb()), createConfirm); err != nil {
			log.Println("approveCustomerWithdrawFromPayonex.CreateSuccessTransferWithdraw", err)
			return nil, nil
		}
		// [Show Withdraw USER_TRANSACTION]
		var showUserTrans model.UserTransactionShowUpdate
		showUserTrans.TransactionId = withdrawTrans.Id
		showUserTrans.ConfirmedAt = &createConfirm.ConfirmedAt
		showUserTrans.ConfirmAdminId = createConfirm.ConfirmedByAdminId
		if err := repo.ShowUserTransaction(showUserTrans); err != nil {
			log.Println("approveCustomerWithdrawFromPayonex.ShowUserTransaction", err)
			return nil, nil
		}
		// [update transaction status]
		secondUsed := "0.0" // timing
		if withdrawTrans.IsAutoCredit && withdrawTrans.TransferAt != nil {
			seconds := time.Now().UTC().Sub(withdrawTrans.TransferAt.UTC()).Seconds()
			secondUsed = fmt.Sprintf("%.2f", seconds)
		}
		if err := repo.UpdateTransactionStatusTransferingToSuccess(withdrawTrans.Id, secondUsed); err != nil {
			log.Println("approveCustomerWithdrawFromPayonex.UpdateTransactionStatusTransferingToSuccess", err)
			return nil, internalServerError(err)
		}

		// [notify SUCCESS]
		var externalNoti model.NotifyExternalNotificationRequest
		notiRepo := repository.NewNotificationRepository(repo.GetDb())
		externalNoti.TypeNotify = model.IsWithdrawalCreditSuccess
		externalNoti.TransId = &transId
		externalNoti.Amount = withdrawTrans.CreditAmount
		externalNoti.MemberCode = withdrawTrans.MemberCode
		externalNoti.UserCredit = withdrawTrans.CreditAmount
		externalNoti.ConfirmedByAdminId = 0
		externalNoti.TimerCounter = secondUsed
		externalNoti.TransferDateTime = withdrawTrans.TransferAt.Add(7 * time.Hour).Format("2006-01-02 15:04:05")
		externalNoti.ActionTime = time.Now().UTC().Add(7 * time.Hour).Format("2006-01-02 15:04:05")
		if err := ExternalNotification(notiRepo, externalNoti); err != nil {
			log.Println("FailedNotify", err)
		}

	}
	return nil, nil
}

func RollbackCustomerWithdrawFromPayonex(repo repository.PayonexRepository, transId int64) (*int64, error) {

	withdrawTrans, err := repo.GetBankTransactionById(transId)
	if err != nil {
		log.Println("rollbackCustomerWithdrawFromPayonex.GetBankTransactionById", err)
		return nil, internalServerError(err)
	}

	// ============================= ON_SUCCESS =================================
	if withdrawTrans.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		// [update transaction status]
		if err := repo.RollbackTransactionStatusTransferingToConfirmed(withdrawTrans.Id); err != nil {
			log.Println("approveCustomerWithdrawFromPayonex.RollbackTransactionStatusTransferingToConfirmed", err)
			return nil, internalServerError(err)
		}
	}
	return nil, nil
}

func (s paygatePayonexService) CreateSystemLog(name string, req interface{}) error {

	// [SYSLOG] HACKED
	_, err := s.repo.CreateSystemLog(model.SystemLogCreateBody{
		Name:   name,
		Status: "HACKING",
		JsonRequest: helper.StructJson(map[string]interface{}{
			"req": req,
		}),
	})
	if err != nil {
		log.Println(name, "CreateSystemLog.ERROR", err.Error())
	}
	return nil
}

func (s paygatePayonexService) CancelWithdrawFromPayonex(transId int64, adminId int64) error {

	withdrawTrans, err := s.repo.GetBankTransactionById(transId)
	if err != nil {
		log.Println("rollbackWithdrawFromPayonex.GetBankTransactionById", err)
		return internalServerError(err)
	}
	if withdrawTrans.TransactionStatusId != model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		return internalServerError(errors.New("ไม่อยู๋ในสถานะที่สามารถยกเลิกได้"))
	}

	user, err := s.repo.GetUserBankDetailById(withdrawTrans.UserId)
	if err != nil {
		return internalServerError(err)
	}

	paygateOrder, err := s.repo.GetDbPayonexOrderByRefId(transId)
	if err != nil {
		log.Println("CancelWithdrawFromPayonex.GetDbPayonexOrderByRefId", err)
		return internalServerError(err)
	}
	waitStatus := "ERROR"
	if paygateOrder.TransactionStatus == nil || *paygateOrder.TransactionStatus != waitStatus {
		return internalServerError(errors.New("ไม่อยู๋ในสถานะที่สามารถยกเลิกได้"))
	}

	returnAmount := withdrawTrans.CreditAmount

	// race condition deposit
	actionAt := time.Now().UTC().Format("20**********")
	var createBody model.RaceActionCreateBody
	createBody.Name = "ADMIN_CANCEL_WITHDRAW"
	createBody.JsonRequest = helper.StructJson(map[string]interface{}{
		"transId":      transId,
		"returnAmount": returnAmount,
	})
	createBody.Status = "PENDING"
	createBody.ActionKey = fmt.Sprintf("WITHDRAW_CANCEL_T%sU%dID%d", actionAt, user.Id, transId)
	createBody.UnlockAt = time.Now().UTC().Add(time.Second * 1)
	if _, err := s.repo.GetRaceActionIdByActionKey(createBody.ActionKey); err != nil {
		log.Println("CancelWithdrawFromPayonex.GetRaceActionIdByActionKey", err)
		if err != gorm.ErrRecordNotFound {
			return internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
		}
	} else {
		return internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
	}

	// create race condition
	actionId, err := s.repo.CreateRaceCondition(createBody)
	if err != nil {
		log.Println("CancelWithdrawFromPayonex.ERROR.CreateRaceCondition", err)
		return internalServerError(errors.New("CANNOT_CREATE_ACTION_LUCKY_WHEEL_ROUND"))
	}
	if actionId == 0 {
		return internalServerError(errors.New("CANNOT_CREATE_ACTION_LUCKY_WHEEL_ROUND"))
	}

	// SET ORDER AS ERROR
	remark := "Admin Cancel Withdraw"
	if err := s.repo.UpdateDbPayonexOrderError(paygateOrder.Id, remark); err != nil {
		log.Println("CreatePayonexWithdraw.UpdateDbPayonexOrderError", err)
	}

	if withdrawTrans.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		// [********] check user credit transaction ว่าตรงไหมกับเงื่อนไข (isShow = false)
		userTransaction, err := s.repo.GetUserWithdrawCreditTransactionByRefId(withdrawTrans.Id, returnAmount)
		if err != nil {
			if err.Error() == recordNotFound {
				log.Println("CancelWithdrawCredit.GetUserCreditTransactionById", err)
				return badRequest("ไม่ตรงกับเงือนไข")
			}
			return internalServerError(err)
		}
		if userTransaction != nil {
			var userCreditReq model.UserTransactionCreateRequest
			userCreditReq.UserId = user.Id
			userCreditReq.TransferAt = withdrawTrans.TransferAt
			userCreditReq.RefId = &withdrawTrans.Id
			userCreditReq.TypeId = model.CREDIT_TYPE_DEPOSIT
			userCreditReq.Amount = returnAmount
			IsShow := false
			userCreditReq.IsShow = &IsShow
			userCreditReq.ConfirmBy = &adminId
			if agentResp, err := s.repo.IncreaseUserCredit(userCreditReq); err != nil {
				log.Println("CancelWithdrawCredit.IncreaseUserCredit", err)
				return internalServerError(err)
			} else if !agentResp.AgentSuccess {
				// [********] error from agent will be in failed
				// if err := s.repoAccounting.UpdateDeporsitTransactionStatusFromAgent(*transId, *agentResp); err != nil {
				//     log.Println("CancelWithdrawCredit.UpdateDeporsitTransactionStatusFromAgent", err)
				// }
				// Set FastBank-READ ** ไม่มี เพราะเป็นการสร้างรายการฝากเอง
				agentFail := errors.New("ทำรายการเครดิตไม่สำเร็จ")
				log.Println("CancelWithdrawCredit.IncreaseUserCredit", agentFail)
				return internalServerError(agentFail)
			}
		}
	}

	// remark := "ADMIN_CANCEL_WITHDRAW"
	var updateApprovedBy model.UpdateUserTransactionStatusRequest
	updateApprovedBy.Id = withdrawTrans.Id
	updateApprovedBy.CanceledAt = time.Now()
	updateApprovedBy.CanceledByAdminId = adminId
	updateApprovedBy.TransactionStatusId = model.TRANS_STATUS_WITHDRAW_CANCELED
	updateApprovedBy.CancelRemark = &remark
	err = s.repo.UpdateUserTransactionStatus(updateApprovedBy)
	if err != nil {
		return internalServerError(err)
	}

	var createConfirm model.CreateSuccessTransferWithdrawRequest
	createConfirm.TransactionId = withdrawTrans.Id
	createConfirm.ConfirmedAt = time.Now()
	createConfirm.ConfirmedByAdminId = &adminId
	if _, err := CreateCanceledTransferWithdraw(repository.NewBankingRepository(s.sharedDb), createConfirm); err != nil {
		return err
	}

	return nil
}

func (s paygatePayonexService) cancelWithdrawFromPayonexWebhookError(payonexOrder model.PayonexOrderResponse) error {

	adminId := int64(1)

	withdrawTrans, err := s.repo.GetBankTransactionById(*payonexOrder.RefId)
	if err != nil {
		log.Println("rollbackWithdrawFromPayonex.GetBankTransactionById", err)
		return internalServerError(err)
	}
	if withdrawTrans.TransactionStatusId != model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		return internalServerError(errors.New("ไม่อยู๋ในสถานะที่สามารถยกเลิกได้"))
	}

	user, err := s.repo.GetUserBankDetailById(withdrawTrans.UserId)
	if err != nil {
		return internalServerError(err)
	}

	paygateOrder, err := s.repo.GetDbPayonexOrderByRefId(withdrawTrans.Id)
	if err != nil {
		log.Println("CancelWithdrawFromPayonex.GetDbPayonexOrderByRefId", err)
		return internalServerError(err)
	}
	waitStatus := "ERROR"
	if paygateOrder.TransactionStatus == nil || *paygateOrder.TransactionStatus != waitStatus {
		return internalServerError(errors.New("ไม่อยู๋ในสถานะที่สามารถยกเลิกได้"))
	}

	returnAmount := withdrawTrans.CreditAmount

	// race condition deposit
	actionAt := time.Now().UTC().Format("20**********")
	var createBody model.RaceActionCreateBody
	createBody.Name = "ADMIN_CANCEL_WITHDRAW"
	createBody.JsonRequest = helper.StructJson(map[string]interface{}{
		"transId":      withdrawTrans.Id,
		"returnAmount": returnAmount,
	})
	createBody.Status = "PENDING"
	createBody.ActionKey = fmt.Sprintf("WITHDRAW_CANCEL_T%sU%dID%d", actionAt, user.Id, withdrawTrans.Id)
	createBody.UnlockAt = time.Now().UTC().Add(time.Second * 1)
	if _, err := s.repo.GetRaceActionIdByActionKey(createBody.ActionKey); err != nil {
		log.Println("CancelWithdrawFromPayonex.GetRaceActionIdByActionKey", err)
		if err != gorm.ErrRecordNotFound {
			return internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
		}
	} else {
		return internalServerError(errors.New("PLEASE_WAIT_ONE_MINUTE"))
	}

	// create race condition
	actionId, err := s.repo.CreateRaceCondition(createBody)
	if err != nil {
		log.Println("CancelWithdrawFromPayonex.ERROR.CreateRaceCondition", err)
		return internalServerError(errors.New("CANNOT_CREATE_ACTION_LUCKY_WHEEL_ROUND"))
	}
	if actionId == 0 {
		return internalServerError(errors.New("CANNOT_CREATE_ACTION_LUCKY_WHEEL_ROUND"))
	}

	// SET ORDER AS ERROR
	remark := "Payment Cancel Withdraw"
	if err := s.repo.UpdateDbPayonexOrderError(paygateOrder.Id, remark); err != nil {
		log.Println("CreatePayonexWithdraw.UpdateDbPayonexOrderError", err)
	}

	if withdrawTrans.TransactionStatusId == model.TRANS_STATUS_WITHDRAW_TRASNFERING {
		// [********] check user credit transaction ว่าตรงไหมกับเงื่อนไข (isShow = false)
		userTransaction, err := s.repo.GetUserWithdrawCreditTransactionByRefId(withdrawTrans.Id, returnAmount)
		if err != nil {
			if err.Error() == recordNotFound {
				log.Println("CancelWithdrawCredit.GetUserCreditTransactionById", err)
				return badRequest("ไม่ตรงกับเงือนไข")
			}
			return internalServerError(err)
		}
		if userTransaction != nil {
			var userCreditReq model.UserTransactionCreateRequest
			userCreditReq.UserId = user.Id
			userCreditReq.TransferAt = withdrawTrans.TransferAt
			userCreditReq.RefId = &withdrawTrans.Id
			userCreditReq.TypeId = model.CREDIT_TYPE_DEPOSIT
			userCreditReq.Amount = returnAmount
			IsShow := false
			userCreditReq.IsShow = &IsShow
			userCreditReq.ConfirmBy = &adminId
			if agentResp, err := s.repo.IncreaseUserCredit(userCreditReq); err != nil {
				log.Println("CancelWithdrawCredit.IncreaseUserCredit", err)
				return internalServerError(err)
			} else if !agentResp.AgentSuccess {
				// [********] error from agent will be in failed
				// if err := s.repoAccounting.UpdateDeporsitTransactionStatusFromAgent(*transId, *agentResp); err != nil {
				//     log.Println("CancelWithdrawCredit.UpdateDeporsitTransactionStatusFromAgent", err)
				// }
				// Set FastBank-READ ** ไม่มี เพราะเป็นการสร้างรายการฝากเอง
				agentFail := errors.New("ทำรายการเครดิตไม่สำเร็จ")
				log.Println("CancelWithdrawCredit.IncreaseUserCredit", agentFail)
				return internalServerError(agentFail)
			}
		}
	}

	// remark := "ADMIN_CANCEL_WITHDRAW"
	var updateApprovedBy model.UpdateUserTransactionStatusRequest
	updateApprovedBy.Id = withdrawTrans.Id
	updateApprovedBy.CanceledAt = time.Now()
	updateApprovedBy.CanceledByAdminId = adminId
	updateApprovedBy.TransactionStatusId = model.TRANS_STATUS_WITHDRAW_CANCELED
	updateApprovedBy.CancelRemark = &remark
	err = s.repo.UpdateUserTransactionStatus(updateApprovedBy)
	if err != nil {
		return internalServerError(err)
	}

	var createConfirm model.CreateSuccessTransferWithdrawRequest
	createConfirm.TransactionId = withdrawTrans.Id
	createConfirm.ConfirmedAt = time.Now()
	createConfirm.ConfirmedByAdminId = &adminId
	if _, err := CreateCanceledTransferWithdraw(repository.NewBankingRepository(s.sharedDb), createConfirm); err != nil {
		return err
	}

	return nil
}
