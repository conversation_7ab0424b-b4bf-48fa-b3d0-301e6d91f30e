package service

import (
	"cybergame-api/helper"
	"cybergame-api/model"
	"log"
	"time"

	"cybergame-api/repository"
)

type IssueReportService interface {
	CurrentAdmin(input any) (*string, error)
	// issue report
	CreateIssueReport(body model.IssueReportCreateRequest) (*int64, error)
	GetIssueReportById(id int64) (*model.IssueReportBody, error)
	UpdateIssueReport(body model.IssueReportUpdateRequest) error
	GetIssueReportList(req model.IssueReportListRequest) (*model.SuccessWithPagination, error)
	DeleteIssueReportAndWeb(id int64) error
	//Option
	GetIssueStatusOptions() ([]model.IssueStatus, error)
	//web url
	CreateWebUrl(body model.CreateWebUrl) (*int64, error)
	GetWebUrlList() ([]model.GetWebUrlBody, error)
	GetWebUrlById(req model.GetWebUrlById) (*model.GetWebUrlBody, error)
	DeleteWebUrlById(req model.GetWebUrlById) error
}

type issueReportService struct {
	repo repository.IssueReportRepository
}

func NewIssueReportService(
	repo repository.IssueReportRepository,
) IssueReportService {
	return &issueReportService{repo}
}

func (s *issueReportService) CurrentAdmin(input any) (*string, error) {

	if input == nil {
		return nil, badRequest(invalidCurrentAdminId)
	}
	var username = input.(string)

	return &username, nil
}

func (s *issueReportService) CreateIssueReport(body model.IssueReportCreateRequest) (*int64, error) {

	var cleanUpData = make(map[string]interface{})

	var createBody model.IssueReportCreateBody
	createBody.Description = body.Description
	createBody.IssueStatusId = body.IssueStatusId
	createBody.CreatedByName = *body.CreatedByName
	timeNow := time.Now()
	createBody.UpdatedAt = timeNow

	insertId, err := s.repo.CreateIssueReport(createBody)
	if err != nil {
		return nil, err
	}
	cleanUpData["issueId"] = *insertId
	var webIssue []model.IssueReportWebCreate
	for _, v := range body.WebUrl {
		webIssue = append(webIssue, model.IssueReportWebCreate{
			IssueReportId: *insertId,
			IssueWebUrlId: v.Id,
		})
	}

	err = s.repo.CreateIssueReportWeb(webIssue)
	if err != nil {
		if err := s.CleanUpIssueReport(cleanUpData); err != nil {
			log.Println("CreateIssueReport.ERROR.CleanUpCreated", err, cleanUpData)
			return nil, err
		}
	}

	return insertId, nil
}

func (s *issueReportService) GetIssueReportById(id int64) (*model.IssueReportBody, error) {

	issueReport, err := s.repo.GetIssueReportById(id)
	if err != nil {
		return nil, err
	}
	var ids []int64
	ids = append(ids, id)

	webUrl, err := s.repo.GetIssueReportWebById(ids)
	if err != nil {
		return nil, err
	}

	var issueReportBody model.IssueReportBody
	issueReportBody.Id = issueReport.Id
	issueReportBody.Description = issueReport.Description
	issueReportBody.IssueStatusId = issueReport.IssueStatusId
	issueReportBody.IssueStatusTh = issueReport.IssueStatusTh
	issueReportBody.IssueStatusEn = issueReport.IssueStatusEn
	issueReportBody.CreatedByName = issueReport.CreatedByName
	issueReportBody.ApprovedByName = issueReport.ApprovedByName
	issueReportBody.CreatedAt = issueReport.CreatedAt

	if issueReport.UpdatedAt == nil {
		issueReportBody.UpdatedAt = issueReport.CreatedAt
	} else {
		issueReportBody.UpdatedAt = *issueReport.UpdatedAt
	}

	var webUrlRes []model.WebUrlRes
	for _, web := range webUrl {
		webUrlRes = append(webUrlRes, model.WebUrlRes{
			Id:  web.IssueWebUrlId,
			Url: web.Url,
		})
		issueReportBody.WebUrl = webUrlRes
	}
	return &issueReportBody, nil
}

func (s *issueReportService) UpdateIssueReport(body model.IssueReportUpdateRequest) error {

	//get by id
	issueReport, err := s.repo.GetIssueReportById(body.Id)
	if err != nil {
		return err
	}

	var updateBody model.IssueReportUpdateBody
	updateBody.Id = body.Id

	if body.Description == nil {
		body.Description = &issueReport.Description
	} else {
		updateBody.Description = body.Description
	}

	if body.IssueStatusId == nil {
		body.IssueStatusId = &issueReport.IssueStatusId
	} else {
		updateBody.IssueStatusId = body.IssueStatusId
	}
	updateBody.ApprovedByName = body.ApprovedByName

	if err := s.repo.UpdateIssueReport(updateBody); err != nil {
		return err
	}
	return nil
}

func (s *issueReportService) CleanUpIssueReport(cleanUpData map[string]interface{}) error {
	for k, v := range cleanUpData {
		switch k {
		case "issueId":
			if err := s.repo.DeleteIssueReportAndWeb(v.(int64)); err != nil {
				return internalServerError(err)
			}
		}
	}
	return nil
}

func (s *issueReportService) GetIssueReportList(req model.IssueReportListRequest) (*model.SuccessWithPagination, error) {

	if err := helper.Pagination(&req.Page, &req.Limit); err != nil {
		return nil, err
	}

	// GetIssueReportList(req model.IssueReportListRequest) ([]model.IssueReport, int64, error)
	issueReport, total, err := s.repo.GetIssueReportList(req)
	if err != nil {
		return nil, err
	}

	var idsList []int64
	for _, v := range issueReport {
		idsList = append(idsList, v.Id)
	}

	webUrl, err := s.repo.GetIssueReportWebById(idsList)
	if err != nil {
		return nil, err
	}

	var issueReportResponse []model.IssueReportResponse

	for _, report := range issueReport {
		var webUrlRes []model.WebUrlRes

		for _, web := range webUrl {
			if report.Id == web.IssueReportId {
				webUrlRes = append(webUrlRes, model.WebUrlRes{
					Id:  web.IssueWebUrlId,
					Url: web.Url,
				})
			}
		}

		// Append the IssueReportResponse here (outside of the inner loop)
		if report.UpdatedAt == nil {
			report.UpdatedAt = &report.CreatedAt
		}
		issueReportResponse = append(issueReportResponse, model.IssueReportResponse{
			Id:             report.Id,
			Description:    report.Description,
			WebUrl:         webUrlRes,
			IssueStatusId:  report.IssueStatusId,
			IssueStatusTh:  report.IssueStatusTh,
			IssueStatusEn:  report.IssueStatusEn,
			CreatedByName:  &report.CreatedByName,
			ApprovedByName: &report.ApprovedByName,
			CreatedAt:      report.CreatedAt,
			UpdatedAt:      *report.UpdatedAt,
		})
	}

	// Now, issueReportResponse contains the correct data for each report with its associated webUrl.

	var successWithPagination model.SuccessWithPagination
	successWithPagination.List = issueReportResponse
	successWithPagination.Total = total

	return &successWithPagination, nil

}

func (s *issueReportService) DeleteIssueReportAndWeb(id int64) error {
	if err := s.repo.DeleteIssueReportAndWeb(id); err != nil {
		return err
	}
	return nil
}

func (s *issueReportService) GetIssueStatusOptions() ([]model.IssueStatus, error) {
	issueStatus, err := s.repo.GetIssueStatusOptions()
	if err != nil {
		return nil, err
	}
	return issueStatus, nil
}

func (s *issueReportService) CreateWebUrl(body model.CreateWebUrl) (*int64, error) {

	insertId, err := s.repo.CreateWebUrl(body)
	if err != nil {
		return nil, err
	}
	return insertId, nil
}

func (s *issueReportService) GetWebUrlList() ([]model.GetWebUrlBody, error) {
	webUrl, err := s.repo.GetWebUrlList()
	if err != nil {
		return nil, err
	}
	return webUrl, nil
}

func (s *issueReportService) GetWebUrlById(req model.GetWebUrlById) (*model.GetWebUrlBody, error) {

	webUrl, err := s.repo.GetWebUrlById(req)
	if err != nil {
		return nil, err
	}
	return webUrl, nil
}

func (s *issueReportService) DeleteWebUrlById(req model.GetWebUrlById) error {

	if err := s.repo.DeleteWebUrlById(req); err != nil {
		return err
	}
	return nil
}
