package service

import (
	"cybergame-api/model"
	"cybergame-api/repository"
	"fmt"
	"log"
	"os"

	"gorm.io/gorm"
)

type AgentCblottoService interface {
	// admin
	GetAgentCblottoSetting() (*model.GetInternalAgentCblottoSettingResponse, error)
	UpdateAgentCblottoSetting(body model.UpdateAgentCblottoSetting) error

	// web [ยังไม่มี เกม list]
	//รอ เกม list
	CallApiAgentCblottoLaunch(req model.CallApiAgentCblottoLaunch) (*model.AgentCblottoUserLoginGameResponse, error)

	// call back
	CallBackAgentCblottoCheckBalance(reqBody model.CallBackAgentCblottoCheckBalanceRequest, headerKey string) (*model.CallBackAgentCblottoCheckBalanceResponse, error)
	CallBackAgentCblottoTransaction(reqBody model.CallBackAgentCblottoTransaction, headerKey string) (*model.CallBackAgentCblottoTransactionResponse, error)
}

type agentCblottoService struct {
	repo        repository.AgentCblottoRepository
	sharedDb    *gorm.DB
	serviceGame GameService
}

func NewAgentCblottoService(
	repo repository.AgentCblottoRepository,
	sharedDb *gorm.DB,
	serviceGame GameService,
) AgentCblottoService {
	return &agentCblottoService{repo, sharedDb, serviceGame}
}

func (s agentCblottoService) GetAgentCblottoSetting() (*model.GetInternalAgentCblottoSettingResponse, error) {

	s.serviceGame.ClearGameCache()
	s.repo.GetInternalAgentCblottoSetting()

	getInternalAgentCblottoSetting, err := s.repo.GetInternalAgentCblottoSetting()
	if err != nil {
		return nil, err
	}

	if getInternalAgentCblottoSetting.ProgramAllowUse == "NOT_ALLOW_USE" {
		// badRequest("กรุณาติดต่อเจ้าหน้าที่ ให้ set ระบบ")
		return nil, badRequest("NOT_ALLOW_USE_AGENT_CTW_PLEASE_CONTACT_ADMIN")
	}

	var response model.GetInternalAgentCblottoSettingResponse
	response.Id = getInternalAgentCblottoSetting.Id
	response.IsActive = getInternalAgentCblottoSetting.IsActive
	response.CblottoAppId = getInternalAgentCblottoSetting.CblottoAppId
	response.CblottoAppPrivate = getInternalAgentCblottoSetting.CblottoAppPrivate
	return &response, nil
}

func (s agentCblottoService) UpdateAgentCblottoSetting(body model.UpdateAgentCblottoSetting) error {

	s.serviceGame.ClearGameCache()
	s.repo.ClearCacheAgentCblottoSetting()

	// p.lay confirm But dev-web for testing will not work
	body.CblottoHrefBackUrl = "https://" + os.Getenv("DOMAIN_NAME")
	return s.repo.UpdateAgentCblottoSetting(body)
}

func (s agentCblottoService) CallApiAgentCblottoLaunch(req model.CallApiAgentCblottoLaunch) (*model.AgentCblottoUserLoginGameResponse, error) {

	getMemberCode, err := s.repo.GetMemberCode(req.UserId)
	if err != nil {
		return nil, badRequest("Not enough credit")
	}

	setting, err := s.repo.GetInternalAgentCblottoSetting()
	if err != nil {
		return nil, err
	}

	if setting.ProgramAllowUse != "ALLOW_USE" {
		return nil, badRequest("NOT_ALLOW_USE_AGENT_CTW_PLEASE_CONTACT_ADMIN")
	}

	if setting.CblottoAppId == "" || setting.CblottoAppPrivate == "" {
		return nil, badRequest("AGENT_CTW_SETTING_NOT_FOUND")
	}

	// gameId, err := strconv.ParseInt(req.GameCode, 10, 64)
	// if err != nil {
	// 	return nil, badRequest("INVALID_GAME_CODE")
	// }

	reqBodyApiGame := model.AgentCblottoUserLoginGame{
		OperatorId: setting.CblottoAppId,
		Username:   *getMemberCode,
		// GameId:     gameId,
	}

	urlDetail := model.CallApiAgentCblottoDetail{
		CblottoAppId:      setting.CblottoAppId,
		CblottoAppPrivate: setting.CblottoAppPrivate,
		CblottoUrl:        setting.CblottoUrl,
	}

	getLaunch, err := s.repo.GetAgentCblottoGameLogin(reqBodyApiGame, urlDetail)
	if err != nil {
		return nil, err
	}

	var response model.AgentCblottoUserLoginGameResponse
	response.Url = getLaunch.Url

	return &response, nil
}

func (s agentCblottoService) CallBackAgentCblottoCheckBalance(reqBody model.CallBackAgentCblottoCheckBalanceRequest, headerKey string) (*model.CallBackAgentCblottoCheckBalanceResponse, error) {

	getInternalAgentAgentCblottoSetting, err := s.repo.GetInternalAgentCblottoSetting()
	if err != nil {
		return nil, err
	}

	if getInternalAgentAgentCblottoSetting.ProgramAllowUse == "NOT_ALLOW_USE" || !getInternalAgentAgentCblottoSetting.IsActive {
		return nil, badRequest("NOT_ALLOW_USE_CBLOTTO_SETTING_PLEASE_CONTACT_ADMIN")
	}

	if headerKey != getInternalAgentAgentCblottoSetting.CblottoAppPrivate {
		return nil, badRequest("INVALID_CBLOTTO_HARD_PRIVATE_KEY")
	}

	getUserMemberCode, err := s.repo.GetUserByMemberCode(reqBody.Username)
	if err != nil {
		return nil, err
	}
	getUserDetail, err := GetUser(repository.NewUserRepository(s.sharedDb), getUserMemberCode.Id)
	if err != nil {
		return nil, err
	}

	var response model.CallBackAgentCblottoCheckBalanceResponse
	response.Username = getUserDetail.MemberCode
	response.Balance = getUserDetail.Credit

	return &response, nil
}

func (s agentCblottoService) CallBackAgentCblottoTransaction(reqBody model.CallBackAgentCblottoTransaction, headerKey string) (*model.CallBackAgentCblottoTransactionResponse, error) {

	getInternalAgentAgentCblottoSetting, err := s.repo.GetInternalAgentCblottoSetting()
	if err != nil {
		return nil, err
	}

	if getInternalAgentAgentCblottoSetting.ProgramAllowUse == "NOT_ALLOW_USE" || !getInternalAgentAgentCblottoSetting.IsActive {
		return nil, badRequest("NOT_ALLOW_USE_CBLOTTO_SETTING_PLEASE_CONTACT_ADMIN")
	}

	if headerKey != getInternalAgentAgentCblottoSetting.CblottoAppPrivate {
		return nil, badRequest("INVALID_CBLOTTO_HARD_PRIVATE_KEY")
	}

	getUserMemberCode, err := s.repo.GetUserByMemberCode(reqBody.Username)
	if err != nil {
		return nil, err
	}
	getUserDetail, err := GetUser(repository.NewUserRepository(s.sharedDb), getUserMemberCode.Id)
	if err != nil {
		return nil, err
	}

	var beforeAmount, afterAmount float64

	if reqBody.TransactionType == "bet" {
		var createAgentCblottoCallbackBody model.CreateAgentCblottoCallback
		createAgentCblottoCallbackBody.UserId = getUserMemberCode.Id
		createAgentCblottoCallbackBody.MemberCode = reqBody.Username
		createAgentCblottoCallbackBody.Payoff = 0
		createAgentCblottoCallbackBody.BetAmount = reqBody.BetAmount
		// createAgentCblottoCallbackBody.WinloseAmount =
		createAgentCblottoCallbackBody.Balance = getUserDetail.Credit
		createAgentCblottoCallbackBody.BeforeBalance = getUserDetail.Credit
		createAgentCblottoCallbackBody.AfterBalance = getUserDetail.Credit
		createAgentCblottoCallbackBody.TransactionId = reqBody.TransactionId
		createAgentCblottoCallbackBody.RoundId = fmt.Sprintf("%d", reqBody.RoundId)
		createAgentCblottoCallbackBody.GameId = fmt.Sprintf("%d", reqBody.GameId)
		createAgentCblottoCallbackBody.CallbackReason = "BET"
		createAgentCblottoCallbackBody.Remark = "CREATE"
		createAgentCblottoCallbackBody.IsSuccess = false
		if err := s.repo.CreateOrUpdateAgentCblottoCallback(createAgentCblottoCallbackBody); err != nil {
			return nil, err
		}

		var updateAgentCblottoCallbackBody model.UpdateAgentCblottoCallback
		updateAgentCblottoCallbackBody.MemberCode = reqBody.Username
		updateAgentCblottoCallbackBody.RoundId = fmt.Sprintf("%d", reqBody.RoundId)
		updateAgentCblottoCallbackBody.TransactionId = reqBody.TransactionId

		var decreaseUserCredit model.DecreaseUserCreditFromOtherAgentRequest
		decreaseUserCredit.UserId = getUserMemberCode.Id
		decreaseUserCredit.Amount = reqBody.BetAmount
		currnecntCredit, err := s.repo.DecreaseUserCreditFromOtherAgent(decreaseUserCredit)
		if err != nil {
			logerror := fmt.Sprintf("DecreaseUserCreditFromOtherAgent Error: %v", err)
			updateAgentCblottoCallbackBody.Remark = &logerror
			if err := s.repo.UpdateAgentCblottoCallback(updateAgentCblottoCallbackBody); err != nil {
				log.Println(err)
			}
			return nil, err
		}
		beforeAmount = currnecntCredit.AgentBeforeAmount
		afterAmount = currnecntCredit.AgentAfterAmount
	}

	if reqBody.TransactionType == "reward" {
		var createAgentCblottoCallbackBody model.CreateAgentCblottoCallback
		createAgentCblottoCallbackBody.UserId = getUserMemberCode.Id
		createAgentCblottoCallbackBody.MemberCode = reqBody.Username
		createAgentCblottoCallbackBody.Payoff = reqBody.RewardAmount
		// createAgentCblottoCallbackBody.BetAmount = reqBody.BetAmount
		// createAgentCblottoCallbackBody.WinloseAmount =
		createAgentCblottoCallbackBody.Balance = getUserDetail.Credit
		createAgentCblottoCallbackBody.BeforeBalance = getUserDetail.Credit
		createAgentCblottoCallbackBody.AfterBalance = getUserDetail.Credit
		createAgentCblottoCallbackBody.TransactionId = reqBody.TransactionId
		createAgentCblottoCallbackBody.RoundId = fmt.Sprintf("%d", reqBody.RoundId)
		createAgentCblottoCallbackBody.GameId = fmt.Sprintf("%d", reqBody.GameId)
		createAgentCblottoCallbackBody.CallbackReason = "REWARD"
		createAgentCblottoCallbackBody.Remark = "UPDATE"
		createAgentCblottoCallbackBody.IsSuccess = false
		if err := s.repo.CreateOrUpdateAgentCblottoCallback(createAgentCblottoCallbackBody); err != nil {
			return nil, err
		}

		var updateAgentCblottoCallbackBody model.UpdateAgentCblottoCallback
		updateAgentCblottoCallbackBody.MemberCode = reqBody.Username
		updateAgentCblottoCallbackBody.RoundId = fmt.Sprintf("%d", reqBody.RoundId)
		updateAgentCblottoCallbackBody.TransactionId = reqBody.TransactionId

		var increaseUserCredit model.IncreaseUserCreditFromOtherAgentRequest
		increaseUserCredit.UserId = getUserMemberCode.Id
		increaseUserCredit.Amount = reqBody.RewardAmount
		currnecntCredit, err := s.repo.IncreaseUserCreditFromOtherAgent(increaseUserCredit)
		if err != nil {
			logerror := fmt.Sprintf("DecreaseUserCreditFromOtherAgent Error: %v", err)
			updateAgentCblottoCallbackBody.Remark = &logerror
			if err := s.repo.UpdateAgentCblottoCallback(updateAgentCblottoCallbackBody); err != nil {
				log.Println(err)
			}
			return nil, err
		}
		beforeAmount = currnecntCredit.AgentBeforeAmount
		afterAmount = currnecntCredit.AgentAfterAmount

		setSuccess := true
		updateAgentCblottoCallbackBody.IsSuccess = &setSuccess
		updateAgentCblottoCallbackBody.AfterBalance = &afterAmount
		if err := s.repo.UpdateAgentCblottoCallback(updateAgentCblottoCallbackBody); err != nil {
			return nil, err
		}
	}

	if reqBody.TransactionType == "cancel" {
		var createAgentCblottoCallbackBody model.CreateAgentCblottoCallback
		createAgentCblottoCallbackBody.UserId = getUserMemberCode.Id
		createAgentCblottoCallbackBody.MemberCode = reqBody.Username
		// createAgentCblottoCallbackBody.Payoff = reqBody.RewardAmount
		// createAgentCblottoCallbackBody.BetAmount = reqBody.BetAmount
		// createAgentCblottoCallbackBody.WinloseAmount =
		createAgentCblottoCallbackBody.CancelAmount = reqBody.CancelAmount
		createAgentCblottoCallbackBody.Balance = getUserDetail.Credit
		createAgentCblottoCallbackBody.BeforeBalance = getUserDetail.Credit
		createAgentCblottoCallbackBody.AfterBalance = getUserDetail.Credit
		createAgentCblottoCallbackBody.TransactionId = reqBody.TransactionId
		createAgentCblottoCallbackBody.RoundId = fmt.Sprintf("%d", reqBody.RoundId)
		createAgentCblottoCallbackBody.GameId = fmt.Sprintf("%d", reqBody.GameId)
		createAgentCblottoCallbackBody.CallbackReason = "CANCEL"
		createAgentCblottoCallbackBody.Remark = "CANCEL_TRANSACTION"
		createAgentCblottoCallbackBody.IsSuccess = false
		if err := s.repo.CreateOrUpdateAgentCblottoCallback(createAgentCblottoCallbackBody); err != nil {
			return nil, err
		}

		var updateAgentCblottoCallbackBody model.UpdateAgentCblottoCallback
		updateAgentCblottoCallbackBody.MemberCode = reqBody.Username
		updateAgentCblottoCallbackBody.RoundId = fmt.Sprintf("%d", reqBody.RoundId)
		updateAgentCblottoCallbackBody.TransactionId = reqBody.TransactionId

		var increaseUserCredit model.IncreaseUserCreditFromOtherAgentRequest
		increaseUserCredit.UserId = getUserMemberCode.Id
		increaseUserCredit.Amount = reqBody.CancelAmount
		currnecntCredit, err := s.repo.IncreaseUserCreditFromOtherAgent(increaseUserCredit)
		if err != nil {
			logerror := fmt.Sprintf("DecreaseUserCreditFromOtherAgent Error: %v", err)
			updateAgentCblottoCallbackBody.Remark = &logerror
			if err := s.repo.UpdateAgentCblottoCallback(updateAgentCblottoCallbackBody); err != nil {
				log.Println(err)
			}
			return nil, err
		}
		beforeAmount = currnecntCredit.AgentBeforeAmount
		afterAmount = currnecntCredit.AgentAfterAmount

		// setSuccess := false
		// updateAgentCblottoCallbackBody.IsSuccess = &setSuccess
		updateAgentCblottoCallbackBody.AfterBalance = &afterAmount
		if err := s.repo.UpdateAgentCblottoCallback(updateAgentCblottoCallbackBody); err != nil {
			return nil, err
		}
	}
	var response model.CallBackAgentCblottoTransactionResponse
	response.TransactionId = reqBody.TransactionId
	response.RoundId = reqBody.RoundId
	response.GameId = reqBody.GameId
	response.BetAmount = reqBody.BetAmount
	response.RewardAmount = reqBody.RewardAmount
	response.CancelAmount = reqBody.CancelAmount
	response.Username = getUserDetail.MemberCode
	response.BalanceBefore = beforeAmount
	response.BalanceAfter = afterAmount

	return &response, nil
}
