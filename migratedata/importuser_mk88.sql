create table ezrich_user
(
       id                bigint auto_increment
              primary key,
       member_code       varchar(255)                             null,
       ref_by            bigint                                   null,
       username          varchar(255)                             null,
       password          varchar(255)                             null,
       phone             varchar(255)                             not null,
       user_status_id    bigint         default 1                 not null,
       user_type_id      bigint         default 1                 not null,
       firstname         varchar(255)                             null,
       lastname          varchar(255)                             null,
       fullname          varchar(255)                             null,
       credit            decimal(14, 2) default 0.00              null,
       ip                varchar(20)                              null,
       bank_account      varchar(15)                              null,
       channel_id        int                                      null,
       true_wallet       varchar(20)                              null,
       contact           varchar(255)                             null,
       note              varchar(255)                             null,
       course            varchar(50)                              null,
       line_id           varchar(30)                              null,
       encrypt           varchar(255)                             null,
       ip_registered     varchar(20)                              null,
       verified_at       datetime                                 null,
       bank_id           int                                      null,
       created_by        int                                      null,
       is_reset_password tinyint        default 0                 null,
       logedin_at        datetime                                 null,
       created_at        datetime       default CURRENT_TIMESTAMP null,
       updated_at        datetime                                 null on update CURRENT_TIMESTAMP,
       deleted_at        datetime                                 null,
       constraint ezrich_user_username_uindex
              unique (username)
);

create index ezrich_user_contact_index
       on ezrich_user (contact);

create index ezrich_user_ref_by_index
       on ezrich_user (ref_by);

create index ezrich_user_member_code_index
       on ezrich_user (member_code);






INSERT INTO ezrich_user (member_code, username, password, phone, user_status_id, user_type_id, firstname,
                         lastname, fullname, credit, ip, bank_account, bank_id, channel_id, true_wallet, contact, note,
                         course,
                         line_id, encrypt, ip_registered, created_by, is_reset_password, logedin_at,
                         created_at, updated_at)
SELECT mem_code,
       mem_tel,
       mem_pass,
       mem_tel,
       1,
       0,
       '',
       '',
       mem_name,
       0,
       IF(length(mem_ip_lastlogin) > 20, '', mem_ip_lastlogin),
       mem_banknumber,
       mem_bank,
       case
              when mem_known = 'google' then 9
              when mem_known = 'feacbook' then 7
              when mem_known = 'line' then 3
              when mem_known = 'fblive' then 13
              when mem_known = 'tiktok' then 12
              when mem_known = 'youtube' then 1
              when mem_known = 'friend' then 10
              when mem_known = 'partner' then 11
              when mem_known = 'orher' then 8
              else 8 end ,
       IF(mem_bank = 15, mem_banknumber, null),
       mem_aff_code,
       '',
       '',
       IF(length(mem_other_contacts) > 30, '', mem_other_contacts),
       mem_pass,
       IF(length(mem_ip_rgs) > 20, '', mem_ip_rgs),
       0,
       0,
       mem_last_active,
       mem_create,
       mem_update
FROM tb_member
            # LEFT JOIN tb_memsaff_line tml on tb_member.site_id = tml.site_id and tb_member.user_id = tml.upline_mem_id
where tb_member.site_id = 390
  and mem_code <> 'wait'
  and mem_last_active > '2023-8-27 00:00:00'
  and mem_del = 0;# OR tml.upline_mem_code is not null);



update ezrich_user as mku
       INNer JOIN ezrich_user as mkaf on mkaf.member_code = mku.contact
       set mku.ref_by = mkaf.id
where mku.ref_by is null;


SELECT tmp.*
FROM tb_member
            inner join tb_mem_partner tmp on tb_member.site_id = tmp.site_id and tb_member.mem_id = tmp.mpart_mem_id
       # LEFT JOIN tb_memsaff_line tml on tb_member.site_id = tml.site_id and tb_member.user_id = tml.upline_mem_id
where tb_member.site_id = 390
  and mem_last_active > '2023-8-27 00:00:00'
  and mem_del = 0;# OR tml.upline_mem_code is not null);