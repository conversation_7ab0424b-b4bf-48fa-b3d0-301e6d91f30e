package handler

import (
	"cybergame-api/middleware"
	"cybergame-api/model"
	"cybergame-api/service"
	"os"

	"cybergame-api/repository"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type transactionController struct {
	transactionService service.TransactionService
}

func newTransactionController(
	transactionService service.TransactionService,
) transactionController {
	return transactionController{transactionService}
}

func TransactionController(r *gin.RouterGroup, db *gorm.DB) {

	singleSession := middleware.SingleSession(db)

	actionService := service.NewAdminActionService(repository.NewAdminActionRepository(db))
	repo := repository.NewTransactionRepository(db)
	userRepo := repository.NewUserRepository(db)
	agentRepo := repository.NewAgentConnectRepository(db)
	// bankRepo := repository.NewBankingRepository(db)
	repoBanking := repository.NewBankingRepository(db)
	repoAccounting := repository.NewAccountingRepository(db)
	repoAgentConnect := repository.NewAgentConnectRepository(db)
	perRepo := repository.NewPermissionRepository(db)
	groupRepo := repository.NewGroupRepository(db)
	otpRepo := repository.NewOtpRepository(db)
	agentInfoRepo := repository.NewAgentInfoRepository(db)
	recommendRepo := repository.NewRecommendRepository(db)
	afRepo := repository.NewAffiliateRepository(db)
	notiRepo := repository.NewNotificationRepository(db)
	notiService := service.NewNotificationService(notiRepo)
	afService := service.NewAffiliateService(db, afRepo, repoAgentConnect, notiService)
	userService := service.NewUserService(
		userRepo,
		db,
		perRepo,
		groupRepo,
		otpRepo,
		agentInfoRepo,
		recommendRepo,
		afRepo,
		notiService,
		actionService,
	)
	gameService := service.NewGameService(agentInfoRepo)
	couponCashRepo := repository.NewCouponCashRepository(db)
	couponCashService := service.NewCouponCashService(couponCashRepo, userService)
	turnoverRepo := repository.NewTurnoverRepository(db)
	turnoverService := service.NewTurnoverService(turnoverRepo)
	agRepo := repository.NewAgentConnectRepository(db)
	allianceRepo := repository.NewAllianceRepository(db)
	alService := service.NewAllianceService(allianceRepo, db, agRepo)
	activityLusckyWheelRepo := repository.NewActivityLuckyWheelRepository(db)
	activityLuckyWheelService := service.NewActivityLuckyWheelService(activityLusckyWheelRepo, db, notiService)
	promotionWebRepo := repository.NewPromotionWebRepository(db)
	promotionWebService := service.NewPromotionWebService(promotionWebRepo, db, notiService, userService)
	accountService := service.NewAccountingService(repoAccounting, userService, gameService, repoAgentConnect, notiService, afService, alService, actionService, activityLuckyWheelService, promotionWebService, db)
	bankingService := service.NewBankingService(repoBanking, repoAccounting, agentRepo, accountService, userService, notiService, afService, turnoverService, alService, activityLuckyWheelService, promotionWebService, couponCashService, db)
	// tranService := service.NewTransactionService(repo, agentRepo, bankingService)
	service := service.NewTransactionService(repo, bankingService, userService)
	handler := newTransactionController(service)

	b := r.Group("/transactions", middleware.AuthorizeUser, singleSession.SingleUserSession())
	b.GET("", handler.transaction)
	b.GET("bank/list", handler.bankList)
	b.POST("/deposit", handler.Deposit)
	b.POST("/withdraw", handler.Withdraw)

	web := r.Group("/web", middleware.AuthorizeUser, singleSession.SingleUserSession())
	web.POST("/deposit-from-users", handler.depositConfirmedFromUser)
	web.POST("/deposit-upload-qr", handler.getQrFromSlip)

	testRoute := r.Group("/test")
	testRoute.POST("/agent-deposit", handler.testAgentDeposit)
	testRoute.POST("/agent-withdraw", handler.testAgentWithdraw)
}

// @Summary แสดงรายการธุรกรรม
// @Description Get Transaction List
// @Tags Transaction
// @Accept json
// @Security BearerAuth
// @Produce json
// @Param page query model.TransactionQuery true "query"
// @Success 200 {object} model.TrasactionListResponse
// @Failure 400 {object} ErrorResponse
// @Router /transactions [get]
func (t *transactionController) transaction(c *gin.Context) {

	query := model.TransactionQuery{}
	if err := c.ShouldBindQuery(&query); err != nil {
		HandleError(c, err)
		return
	}

	query.UserId = int64(c.MustGet("userId").(float64))

	result, err := t.transactionService.GetTransactionList(query)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, result)
}

// @Summary แสดงรายการธนาคาร
// @Description Get Bank List
// @Tags Transaction
// @Accept json
// @Security BearerAuth
// @Produce json
// @Success 200 {object} []model.TransactionBankList
// @Failure 400 {object} ErrorResponse
// @Router /transactions/bank/list [get]
func (t *transactionController) bankList(c *gin.Context) {

	result, err := t.transactionService.GetBankList()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, result)
}

// @Summary ฝากเงิน
// @Description Deposit
// @Tags Transaction
// @Accept json
// @Security BearerAuth
// @Produce json
// @Param body body model.TransactionDepositBody true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} ErrorResponse
// @Router /transactions/deposit [post]
func (t *transactionController) Deposit(c *gin.Context) {

	var body model.TransactionDepositBody

	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}

	body.UserID = int64(c.MustGet("userId").(float64))

	if err := t.transactionService.Deposit(body); err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(201, model.Success{Message: "Deposit success"})
}

// @Summary ถอนเงิน
// @Description Withdraw
// @Tags Transaction
// @Accept json
// @Security BearerAuth
// @Produce json
// @Param body body model.TransactionWithdrawBody true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} ErrorResponse
// @Router /transactions/withdraw [post]
func (t *transactionController) Withdraw(c *gin.Context) {

	var body model.TransactionWithdrawBody

	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}

	body.UserID = int64(c.MustGet("userId").(float64))

	if err := t.transactionService.Withdraw(body); err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(201, model.Success{Message: "Withdraw success"})
}

// @Summary แจ้งฝากเงินโดย user
// @Description Deposit
// @Tags Web - Deposit
// @Accept json
// @Security BearerAuth
// @Produce json
// @Param body body model.CreateDepositConfirmedFromUserRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} ErrorResponse
// @Router /web/deposit-from-users [post]
func (t *transactionController) depositConfirmedFromUser(c *gin.Context) {

	var body model.CreateDepositConfirmedFromUserRequest

	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}

	body.UserId = int64(c.MustGet("userId").(float64))

	if err := t.transactionService.DepositConfirmedFromUser(body); err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(201, model.Success{Message: "Deposit success"})
}

// @Summary getQrFromSlip อัพโหลดไฟล์
// @Description getQrFromSlip อัพโหลดไฟล์
// @Tags Files
// @Security BearerAuth
// @Accept multipart/form-data
// @Produce json
// @Param file formData file true "upload file"
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/deposit-upload-qr [post]
func (h transactionController) getQrFromSlip(c *gin.Context) {

	qrString, err := h.transactionService.ReadQrFromFileUpload(c.Request)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithData{Message: "success", Data: qrString})
}

// @Summary (testAgentDeposit)
// @Description (testAgentDeposit)
// @Tags Test -
// @Accept json
// @Security BearerAuth
// @Produce json
// @Param body body model.TestIncreaseUserCreditRequest true "body"
// @Success 201 {object} model.UserTransactionCreateResponse
// @Failure 400 {object} ErrorResponse
// @Router /test/agent-deposit [post]
func (h *transactionController) testAgentDeposit(c *gin.Context) {

	var body model.TestIncreaseUserCreditRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}

	ginMode := os.Getenv("GIN_MODE")
	if ginMode == "debug" {
		result, err := h.transactionService.TestIncreaseUserCredit(body)
		if err != nil {
			HandleError(c, err)
			return
		}
		c.JSON(201, result)
		return
	} else {
		// สำหรับ production ถ้ามีการใช้งานแสดงว่าหลุด
		if err := h.transactionService.CreateSystemLog("testAgentDeposit", body); err != nil {
			HandleError(c, err)
			return
		}
	}
	c.JSON(201, nil)
}

// @Summary (testAgentWithdraw)
// @Description (testAgentWithdraw)
// @Tags Test -
// @Accept json
// @Security BearerAuth
// @Produce json
// @Param body body model.TestDecreaseUserCreditRequest true "body"
// @Success 201 {object} model.UserTransactionCreateResponse
// @Failure 400 {object} ErrorResponse
// @Router /test/agent-withdraw [post]
func (h *transactionController) testAgentWithdraw(c *gin.Context) {

	var body model.TestDecreaseUserCreditRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}

	ginMode := os.Getenv("GIN_MODE")
	if ginMode == "debug" {
		result, err := h.transactionService.TestDecreaseUserCredit(body)
		if err != nil {
			HandleError(c, err)
			return
		}
		c.JSON(201, result)
		return
	} else {
		// สำหรับ production ถ้ามีการใช้งานแสดงว่าหลุด
		if err := h.transactionService.CreateSystemLog("testAgentWithdraw", body); err != nil {
			HandleError(c, err)
			return
		}
	}
	c.JSON(201, nil)
}
