package handler

import (
	"cybergame-api/helper"
	"cybergame-api/middleware"
	"cybergame-api/model"
	"cybergame-api/repository"
	"cybergame-api/service"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"gorm.io/gorm"
)

type renewalWebController struct {
	renewalWebService service.RenewalWebService
}

func newRenewalWebController(
	renewalWebService service.RenewalWebService,
) renewalWebController {
	return renewalWebController{renewalWebService}
}

func RenewalWebController(r *gin.RouterGroup, db *gorm.DB) {

	singleSession := middleware.SingleSession(db)

	downlineRepo := repository.NewDownlineRepository(db)
	downlineService := service.NewDownlineService(downlineRepo)
	repo := repository.NewRenewalWebRepository(db)
	service := service.NewRenewalWebService(repo, downlineService)
	handler := newRenewalWebController(service)
	// Web Status
	webStatusRoute := r.Group("/renewal-status", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	webStatusRoute.GET("/detail", handler.getLocalWebInfo)
	// Web Renewal - to renew the web
	webRenewalRoute := r.Group("/renewal-webs", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	webRenewalRoute.GET("/package-list", handler.getWebRenewalPackageList)
	webRenewalRoute.GET("/package-detail/:id", handler.getWebRenewalPackageById)
	webRenewalRoute.POST("/buy", handler.buyWebRenewalPackage)
	// Sms Renewal - to topup the sms credit
	smsRenewalRoute := r.Group("/topup-sms", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	smsRenewalRoute.GET("/package-list", handler.getSmsRenewalPackageList)
	smsRenewalRoute.GET("/package-detail/:id", handler.getSmsRenewalPackageById)
	smsRenewalRoute.POST("/buy", handler.buySmsRenewalPackage)
	// Fastbank Renewal - to topup the credit
	fastbankRenewalRoute := r.Group("/topup-fastbank", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	fastbankRenewalRoute.GET("/package-list", handler.getFastbankRenewalPackageList)
	fastbankRenewalRoute.GET("/package-detail/:id", handler.getFastbankRenewalPackageById)
	fastbankRenewalRoute.POST("/buy", handler.buyFastbankRenewalPackage)
	// Invoice - from renewal web sms and fastbank credit
	invoiceRoute := r.Group("/invoice", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	invoiceRoute.GET("/list", handler.getInvoiceList)
	invoiceRoute.GET("/detail/:id", handler.getInvoiceById)
	invoiceRoute.GET("/view-bill/:id", handler.viewBillById)
	invoiceRoute.GET("/view-receipt/:id", handler.viewReceiptById)
	invoiceRoute.POST("create-heng-payment", handler.createInvoiceHengPayment)
	invoiceRoute.POST("create-payonex-payment", handler.createInvoicePayonexPayment)
	invoiceRoute.POST("confirm-payment", handler.confirmPayment)
	invoiceRoute.POST("upload/slip", handler.uploadInvoiceSlipToCloudFlare)
}

// @Summary (getLocalWebInfo)
// @Description (getLocalWebInfo)
// @Description หมดอายุเกิน 5 วันจะ login admin ไม่ได้
// @Tags RenewalWebs - WebMaster
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.WebStatusResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /renewal-status/detail [get]
func (h renewalWebController) getLocalWebInfo(c *gin.Context) {

	data, err := h.renewalWebService.GetLocalWebInfo()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (getWebRenewalPackageList)
// @Description (getWebRenewalPackageList)
// @Tags RenewalWebs - Web Renewal
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.WebRenewalPackageListRequest true "WebRenewalPackageListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /renewal-webs/package-list [get]
func (h renewalWebController) getWebRenewalPackageList(c *gin.Context) {

	var query model.WebRenewalPackageListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.renewalWebService.GetWebRenewalPackageList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithPagination{List: data.List, Total: data.Total})
}

// @Summary (getWebRenewalPackageById)
// @Description (getWebRenewalPackageById)
// @Tags RenewalWebs - Web Renewal
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 200 {object} model.WebRenewalPackageResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /renewal-webs/package-detail/{id} [get]
func (h renewalWebController) getWebRenewalPackageById(c *gin.Context) {

	var query model.GetByIdRequest
	if err := c.ShouldBindUri(&query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.renewalWebService.GetWebRenewalPackageById(query.Id)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (buyWebRenewalPackage)
// @Description (buyWebRenewalPackage)
// @Tags RenewalWebs - Web Renewal
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.WebRenewalBuyRequest true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /renewal-webs/buy [post]
func (h renewalWebController) buyWebRenewalPackage(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	var body model.WebRenewalBuyRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}
	body.AdminId = adminId

	insertId, err := h.renewalWebService.BuyWebRenewalPackage(body)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.SuccessWithId{Message: "Created success", Id: *insertId})
}

// @Summary (getSmsRenewalPackageList)
// @Description (getSmsRenewalPackageList)
// @Tags RenewalWebs - Web Renewal
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.SmsRenewalPackageListRequest true "SmsRenewalPackageListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /topup-sms/package-list [get]
func (h renewalWebController) getSmsRenewalPackageList(c *gin.Context) {

	var query model.SmsRenewalPackageListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.renewalWebService.GetSmsRenewalPackageList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithPagination{List: data.List, Total: data.Total})
}

// @Summary (getSmsRenewalPackageById)
// @Description (getSmsRenewalPackageById)
// @Tags RenewalWebs - Web Renewal
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 200 {object} model.SmsRenewalPackageResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /topup-sms/package-detail/{id} [get]
func (h renewalWebController) getSmsRenewalPackageById(c *gin.Context) {

	var query model.GetByIdRequest
	if err := c.ShouldBindUri(&query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.renewalWebService.GetSmsRenewalPackageById(query.Id)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (buySmsRenewalPackage)
// @Description (buySmsRenewalPackage)
// @Tags RenewalWebs - Web Renewal
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.SmsRenewalBuyRequest true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /topup-sms/buy [post]
func (h renewalWebController) buySmsRenewalPackage(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	var body model.SmsRenewalBuyRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}
	body.AdminId = adminId

	insertId, err := h.renewalWebService.BuySmsRenewalPackage(body)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.SuccessWithId{Message: "Created success", Id: *insertId})
}

// @Summary (getFastbankRenewalPackageList)
// @Description (getFastbankRenewalPackageList)
// @Tags RenewalWebs - Web Renewal
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.FastbankRenewalPackageListRequest true "FastbankRenewalPackageListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /topup-fastbank/package-list [get]
func (h renewalWebController) getFastbankRenewalPackageList(c *gin.Context) {

	var query model.FastbankRenewalPackageListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.renewalWebService.GetFastbankRenewalPackageList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithPagination{List: data.List, Total: data.Total})
}

// @Summary (getFastbankRenewalPackageById)
// @Description (getFastbankRenewalPackageById)
// @Tags RenewalWebs - Web Renewal
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 200 {object} model.FastbankRenewalPackageResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /topup-fastbank/package-detail/{id} [get]
func (h renewalWebController) getFastbankRenewalPackageById(c *gin.Context) {

	var query model.GetByIdRequest
	if err := c.ShouldBindUri(&query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.renewalWebService.GetFastbankRenewalPackageById(query.Id)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (buyFastbankRenewalPackage)
// @Description (buyFastbankRenewalPackage)
// @Tags RenewalWebs - Web Renewal
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.FastbankRenewalBuyRequest true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /topup-fastbank/buy [post]
func (h renewalWebController) buyFastbankRenewalPackage(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	var body model.FastbankRenewalBuyRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}
	body.AdminId = adminId

	insertId, err := h.renewalWebService.BuyFastbankRenewalPackage(body)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.SuccessWithId{Message: "Created success", Id: *insertId})
}

// @Summary (getInvoiceList)
// @Description (getInvoiceList)
// @Tags Invoice
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.InvoiceListRequest true "InvoiceListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /invoice/list [get]
func (h renewalWebController) getInvoiceList(c *gin.Context) {

	var query model.InvoiceListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.renewalWebService.GetInvoiceList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithPagination{List: data.List, Total: data.Total})
}

// @Summary (getInvoiceById)
// @Description (getInvoiceById)
// @Tags Invoice
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 200 {object} model.InvoiceResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /invoice/detail/{id} [get]
func (h renewalWebController) getInvoiceById(c *gin.Context) {

	var query model.GetByIdRequest
	if err := c.ShouldBindUri(&query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.renewalWebService.GetInvoiceById(query.Id)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (viewBillById)
// @Description (viewBillById)
// @Tags Invoice
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 200 {object} model.InvoiceBillResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /invoice/view-bill/{id} [get]
func (h renewalWebController) viewBillById(c *gin.Context) {

	var query model.GetByIdRequest
	if err := c.ShouldBindUri(&query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.renewalWebService.ViewBillById(query.Id)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (viewReceiptById)
// @Description (viewReceiptById)
// @Tags Invoice
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 200 {object} model.InvoiceReceiptResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /invoice/view-receipt/{id} [get]
func (h renewalWebController) viewReceiptById(c *gin.Context) {

	var query model.GetByIdRequest
	if err := c.ShouldBindUri(&query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.renewalWebService.ViewReceiptById(query.Id)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (createInvoiceHengPayment)
// @Description (createInvoiceHengPayment)
// @Tags Invoice
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.InvoiceHengPaymentRequest true "body"
// @Success 201 {object} model.DownlineInvoiceHengPaymentCreateResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /invoice/create-heng-payment [post]
func (h renewalWebController) createInvoiceHengPayment(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	var body model.InvoiceHengPaymentRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}
	body.AdminId = adminId

	qrData, err := h.renewalWebService.CreateInvoiceHengPayment(body)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, qrData)
}

// @Summary (createInvoicePayonexPayment)
// @Description (createInvoicePayonexPayment)
// @Tags Invoice
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.InvoicePayonexPaymentRequest true "body"
// @Success 201 {object} model.DownlineInvoicePayonexPaymentCreateResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /invoice/create-payonex-payment [post]
func (h renewalWebController) createInvoicePayonexPayment(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	var body model.InvoicePayonexPaymentRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}
	body.AdminId = adminId

	qrData, err := h.renewalWebService.CreateInvoicePayonexPayment(body)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, qrData)
}

// @Summary (confirmPayment)
// @Description (confirmPayment)
// @Tags Invoice
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.InvoiceConfirmPaymentRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /invoice/confirm-payment [post]
func (h renewalWebController) confirmPayment(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	var body model.InvoiceConfirmPaymentRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}
	body.AdminId = adminId

	if err := h.renewalWebService.ConfirmPayment(body); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Confirm success"})
}

// @Summary (uploadInvoiceSlipToCloudFlare) อัพโหลดไฟล์ CloudFlare แบบ feature
// @Description (uploadInvoiceSlipToCloudFlare) Upload File CloudFlare
// @Tags Invoice
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param file formData file true "file"
// @Success 200 {object} model.FileUploadResponse
// @Failure 400 {object} ErrorResponse
// @Router /invoice/upload/slip [post]
func (h renewalWebController) uploadInvoiceSlipToCloudFlare(c *gin.Context) {

	data, err := h.renewalWebService.UploadImageToS3InvoiceSlip(c.Request)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}
