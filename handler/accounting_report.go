package handler

import (
	"cybergame-api/helper"
	"cybergame-api/middleware"
	"cybergame-api/model"
	"cybergame-api/repository"
	"cybergame-api/service"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"gorm.io/gorm"
)

type accountingReportController struct {
	accountingReportService service.AccountingReportService
}

func newAccountingReportController(
	accountingReportService service.AccountingReportService,
) accountingReportController {
	return accountingReportController{accountingReportService}
}

func AccountingReportController(r *gin.RouterGroup, db *gorm.DB) {

	singleSession := middleware.SingleSession(db)
	// CORE
	repo := repository.NewAccountingReportRepository(db)
	service := service.NewAccountingReportService(repo)
	handler := newAccountingReportController(service)
	accountingReportOptionRoute := r.Group("/summary-report", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	accountingReportOptionRoute.GET("/daily", handler.getSummaryReportDaily)
	accountingReportOptionRoute.GET("/user-total-credit", handler.GetUserCreditSummaryTotal)
	accountingReportOptionRoute.POST("/manual-re-run", handler.ManualBackupSummaryReportDailyReRun)
	accountingReportOptionRoute.GET("/check-already-rerun", handler.CheckAlreadyExistReportSummaryDashboardRerun)
	// /summary-report/back-up
	backUp := r.Group("/summary-report")
	backUp.GET("/back-up", handler.ManualBackupSummaryReportDaily)

}

// @Summary (getSummaryReportDaily) สรุปภาพรวม ดึงข้อมูลสรุปภาพรวม
// @Description (getSummaryReportDaily) สรุปภาพรวม ดึงข้อมูลสรุปภาพรวม
// @Description [********] ยอดฝาก และ ยอดฝากครั้งแรก จะถูกหักไปด้วยยอดดึงเครดิตกลับ.
// @Description กรองข้อมูลตามประเภทวัน ส่ง dateType เป็น daily, yesterday, last_week, last_month
// @Description กรองข้อมูลตามช่วงวันที่ ส่ง startDate, endDate ในรูปแบบ YYYY-MM-DD (2021-12-31)
// @Description
// @Tags Report - Summary Report
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.ReportSummaryRequest true "ReportSummaryRequest"
// @Success 200 {object} model.ReportSummaryResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /summary-report/daily [get]
func (h accountingReportController) getSummaryReportDaily(c *gin.Context) {

	var query model.ReportSummaryRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.accountingReportService.GetSummaryReportDaily(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (ManualBackupSummaryReportDaily) สรุปภาพรวม ดึงข้อมูลสรุปภาพรวม
// @Description backup สรุปภาพรวม ดึงข้อมูลสรุปภาพรวม
// @Tags Report - Summary Report
// @Accept json
// @Produce json
// @Param _ query model.ReportSummaryRequest true "ReportSummaryRequest"
// @Success 200 {object} model.ReportSummaryResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /summary-report/back-up [get]
func (h accountingReportController) ManualBackupSummaryReportDaily(c *gin.Context) {

	var query model.ReportSummaryRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.accountingReportService.ManualBackupSummaryReportDaily(query)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary (GetUserCreditSummaryTotal) สรุปภาพรวม ดึงข้อมูลสรุปภาพรวม
// @Description สรุปภาพรวม ดึงข้อมูลสรุปภาพรวม
// @Tags Report - Summary Report
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.TotalSumUserActiveCredit
// @Failure 400 {object} handler.ErrorResponse
// @Router /summary-report/user-total-credit [get]
func (h accountingReportController) GetUserCreditSummaryTotal(c *gin.Context) {
	data, err := h.accountingReportService.GetUserCreditSummaryTotal()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (ManualBackupSummaryReportDailyReRun) สรุปภาพรวม ดึงข้อมูลสรุปภาพรวม
// @Description backup สรุปภาพรวม ดึงข้อมูลสรุปภาพรวม
// @Tags Report - Summary Report
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.CreateReportSummaryDashboardRerun true "CreateReportSummaryDashboardRerun"
// @Success 200 {object} model.ReportSummaryResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /summary-report/manual-re-run [post]
func (h accountingReportController) ManualBackupSummaryReportDailyReRun(c *gin.Context) {
	var body model.CreateReportSummaryDashboardRerun
	if err := c.ShouldBind(&body); err != nil {
		HandleError(c, err)
		return
	}

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))
	body.CreatedById = adminId

	err := h.accountingReportService.ManualBackupSummaryReportDailyReRun(body)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, "success")
}

// @Summary (CheckAlreadyExistReportSummaryDashboardRerun) สรุปภาพรวม ดึงข้อมูลสรุปภาพรวม
// @Description backup สรุปภาพรวม ดึงข้อมูลสรุปภาพรวม
// @Tags Report - Summary Report
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.ReportSummaryResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /summary-report/check-already-rerun [get]
func (h accountingReportController) CheckAlreadyExistReportSummaryDashboardRerun(c *gin.Context) {

	data, err := h.accountingReportService.CheckAlreadyExistReportSummaryDashboardRerun()
	if err != nil {
		return
	}

	c.JSON(200, data)
}
