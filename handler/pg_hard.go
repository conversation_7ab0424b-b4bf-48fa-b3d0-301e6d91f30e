package handler

import (
	"cybergame-api/helper"
	"cybergame-api/middleware"
	"cybergame-api/model"
	"cybergame-api/repository"
	"cybergame-api/service"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type pgHardController struct {
	pgHardService service.PgHardService
}

func newPgHardController(
	pgHardService service.PgHardService,
) pgHardController {
	return pgHardController{pgHardService}
}

func PgHardController(r *gin.RouterGroup, db *gorm.DB) {

	repoGame := repository.NewAgentInfoRepository(db)
	serviceGame := service.NewGameService(repoGame)

	repo := repository.NewPgHardRepository(db)
	service := service.NewPgHardService(repo, db, serviceGame)
	handler := newPgHardController(service)

	rootPgHard := r.Group("/program/pghard")
	rootPgHard.GET("/operator/preset", handler.GetPgHardOperatorPreset)
	rootPgHard.GET("/callback-list", handler.GetAgentPgHardCallback)

	user := r.Group("/user/pg-h", middleware.AuthorizeUser)
	user.GET("/games-list", handler.GetPgHardGameList)
	user.POST("/play", handler.GetPgHardGameSessionInit)
	// middleware.AuthorizePgHard
	callback := r.Group("/play/pg")
	callback.POST("/checkBalance", handler.CallBackPgHardCheckBalance)
	callback.POST("/settleBet", handler.CallBackPgHardGameSettleBet)

	admin := r.Group("/admin", middleware.AuthorizeAdmin)
	admin.GET("/pg-h-setting", handler.GetAgentPgHardSetting)
	admin.PUT("/pg-h-setting", handler.UpdateAgentPgHardSetting)
	admin.GET("/pg-h-setting/preset", handler.AdminGetPgHardOperatorPreset)

	cron := r.Group("/cron")
	cron.GET("/pghard/callback-summary", handler.GetPgHardCallbackSummary)

}

// @Summary Get PgHard Game List
// @Description Get PgHard Game List
// @Tags User Agent PG-H
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.GetPgHardGameListResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /user/pg-h/games-list [get]
func (h pgHardController) GetPgHardGameList(c *gin.Context) {

	data, err := h.pgHardService.GetPgHardGameList()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary Get Pg Operator Preset
// @Description Get Pg Operator Preset
// @Tags Program Agent PG
// @Accept json
// @Produce json
// @Success 200 {object} model.GetPgOperatorPresetResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /program/pghard/operator/preset [get]
func (h pgHardController) GetPgHardOperatorPreset(c *gin.Context) {

	data, err := h.pgHardService.GetPgHardOperatorPreset()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary Get PgHard Game Session Init
// @Description Get PgHard Game Session Init
// @Security BearerAuth
// @Tags User Agent PG-H
// @Accept json
// @Produce json
// @Param Body body model.GetPgHardGame true "Get PgHard Game Session Init"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} handler.ErrorResponse
// @Router /user/pg-h/play [post]
func (h pgHardController) GetPgHardGameSessionInit(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var reqBody model.GetPgHardGame
	if err := c.ShouldBindJSON(&reqBody); err != nil {
		HandleError(c, err)
		return
	}

	reqBody.UserId = userId
	data, err := h.pgHardService.GetPgHardGameSessionInit(reqBody)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

func (h pgHardController) CallBackPgHardCheckBalance(c *gin.Context) {
	// @Summary CallBack PgHard Check Balance
	// @Description CallBack PgHard Check Balance
	// @Tags Agent PG
	// @Accept json
	// @Produce json
	// @Security BearerAuth
	// @Param Body body model.CallBackPgHardCheckBalanceRequest true "CallBack PgHard Check Balance"
	// @Success 200 {object} model.CallBackPgHardCheckBalanceResponse
	// @Failure 400 {object} handler.ErrorResponse
	// @Router /play/pg/checkBalance [post]

	getHeader := c.Request.Header.Get("Authorization")

	var reqBody model.CallBackPgHardCheckBalanceRequest
	if err := c.ShouldBindJSON(&reqBody); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.pgHardService.CallBackPgHardCheckBalance(reqBody, getHeader)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

func (h pgHardController) CallBackPgHardGameSettleBet(c *gin.Context) {
	// @Summary CallBack PgHard Game Settle Bet
	// @Description CallBack PgHard Game Settle Bet
	// @Tags Agent PG
	// @Accept json
	// @Produce json
	// @Security BearerAuth
	// @Param Body body model.CallBackPgHardGameSettleBetRequest true "CallBack PgHard Game Settle Bet"
	// @Success 200 {object} model.CallBackPgHardGameSettleBetResponse
	// @Failure 400 {object} handler.ErrorResponse
	// @Router /play/pg/settleBet [post]

	getHeader := c.Request.Header.Get("Authorization")
	var reqBody model.CallBackPgHardGameSettleBetRequest
	if err := c.ShouldBindJSON(&reqBody); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.pgHardService.CallBackPgHardGameSettleBet(reqBody, getHeader)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

func (h pgHardController) GetAgentPgHardCallback(c *gin.Context) {
	// @Summary Get Agent PgHard Callback
	// @Description Get Agent PgHard Callback
	// @Tags Program Agent PG
	// @Accept json
	// @Produce json
	// @Security BearerAuth
	// @Param query query model.AgentPgHardCallbackSummaryRequest true "Get Agent PgHard Callback"
	// @Success 200 {object} []model.AgentPgHardCallbackSummary
	// @Failure 400 {object} handler.ErrorResponse
	// @Router /program/pghard/callback-list [get]

	var req model.AgentPgHardCallbackSummaryRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.pgHardService.GetAgentPgHardCallback(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary Get Agent PgHard Setting
// @Description Get Agent PgHard Setting
// @Tags Admin Agent PG
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} model.GetAgentPgHardSetting
// @Failure 400 {object} handler.ErrorResponse
// @Router /admin/pg-h-setting [get]
func (h pgHardController) GetAgentPgHardSetting(c *gin.Context) {

	data, err := h.pgHardService.GetAgentPgHardSetting()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary Update Agent PgHard Setting
// @Description Update Agent PgHard Setting
// @Tags Admin Agent PG
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param Body body model.UpdateAgentPgHardSetting true "Update Agent PgHard Setting"
// @Success 200
// @Failure 400 {object} handler.ErrorResponse
// @Router /admin/pg-h-setting [put]
func (h pgHardController) UpdateAgentPgHardSetting(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	var reqBody model.UpdateAgentPgHardSetting
	if err := c.ShouldBindJSON(&reqBody); err != nil {
		HandleError(c, err)
		return
	}

	reqBody.UpdatedById = adminId
	reqBody.UpdatedAt = time.Now().UTC()
	err := h.pgHardService.UpdateAgentPgHardSetting(reqBody)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(201, model.Success{Message: "UPDATE SUCCESS"})
}

// @Summary Admin Get Pg Operator Preset
// @Description Admin Get Pg Operator Preset
// @Tags Admin Agent PG
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} model.GetPgOperatorPresetResponseList
// @Failure 400 {object} handler.ErrorResponse
// @Router /admin/pg-h-setting/preset [get]
func (h pgHardController) AdminGetPgHardOperatorPreset(c *gin.Context) {

	data, err := h.pgHardService.AdminGetPgHardOperatorPreset()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary Get PgHard Callback Summary
// @Description Get PgHard Callback Summary
// @Tags Agent PG
// @Accept json
// @Produce json
// @Param query query model.PgHardCallbackSummaryRequest true "Get PgHard Callback Summary"
// @Success 200 {object} []model.PgHardCallbackSummaryResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /cron/pghard/callback-summary [get]
func (h pgHardController) GetPgHardCallbackSummary(c *gin.Context) {
	var req model.PgHardCallbackSummaryRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.pgHardService.GetPgHardCallbackSummary(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}
