package handler

import (
	"cybergame-api/helper"
	"cybergame-api/middleware"
	"cybergame-api/model"
	"cybergame-api/repository"
	"cybergame-api/service"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type allianceWhiteController struct {
	allianceWhiteService service.AllianceWhiteService
}

func newAllianceWhiteController(
	allianceWhiteService service.AllianceWhiteService,
) allianceWhiteController {
	return allianceWhiteController{allianceWhiteService}
}

func AllianceWhiteController(r *gin.RouterGroup, db *gorm.DB) {

	singleSession := middleware.SingleSession(db)

	repo := repository.NewAllianceWhiteRepository(db)
	service := service.NewAllianceWhiteService(repo)
	handler := newAllianceWhiteController(service)

	adminRoute := r.Group("/alwhite-member", middleware.AuthorizeUser, singleSession.SingleUserSession())
	adminRoute.POST("/register", handler.registerMember)
}

// @Summary (registerMember) Register new member
// @Description (registerMember) Register new member
// @Tags White Alliance - Member
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.AllianceMemberRegister true "Register User"
// @Success 201 {object} model.UserVerifyOtpResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /alwhite-member/register [post]
func (h allianceWhiteController) registerMember(c *gin.Context) {

	body := model.AllianceMemberRegister{}

	refUserId := int64(c.MustGet("userId").(float64))

	ip := c.ClientIP()
	body.IpRegistered = helper.GetIPv4(ip)
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	body.RefBy = &refUserId

	result, err := h.allianceWhiteService.UserRegister(body)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, result)
}
