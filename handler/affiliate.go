package handler

import (
	"cybergame-api/helper"
	"cybergame-api/middleware"
	"cybergame-api/model"
	"cybergame-api/service"

	"cybergame-api/repository"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"gorm.io/gorm"
)

type affiliateController struct {
	affiliateService service.AffiliateService
}

func newAffiliateController(
	affiliateService service.AffiliateService,
) affiliateController {
	return affiliateController{affiliateService}
}

func AffiliateController(r *gin.RouterGroup, db *gorm.DB) {

	repo := repository.NewAffiliateRepository(db)
	agRepo := repository.NewAgentConnectRepository(db)
	serviceRepo := repository.NewNotificationRepository(db)
	serviceNoti := service.NewNotificationService(serviceRepo)
	service := service.NewAffiliateService(db, repo, agRepo, serviceNoti)
	handler := newAffiliateController(service)

	role := middleware.Role(db)
	singleSession := middleware.SingleSession(db)

	// WEB
	p := r.Group("/affiliates", middleware.AuthorizeUser, singleSession.SingleUserSession())
	p.GET("/summary", handler.summary)
	p.GET("/member", handler.getMember)
	p.GET("/member/option-level", handler.AfMemberOptionLevel)
	p.GET("/income", handler.getIncome)
	p.GET("/income-log", handler.getIncomeWebLogList)
	p.GET("/income-history", handler.getUserIncomeCompletedLogList)
	p.POST("/withdraw-commission", handler.withdrawCommission)
	// BACKOFFICE
	a := r.Group("/affiliates/backoffice", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	a.GET("/register-bonus-type", role.CheckPermission([]string{"marketing_manage", "marketing_manage_edit"}), handler.getAfRegisterBonusType)
	a.GET("/register-bonus-option", role.CheckPermission([]string{"marketing_manage", "marketing_manage_edit"}), handler.getAfRegisterBonusOption)
	a.GET("/commission", role.CheckPermission([]string{"marketing_manage", "marketing_manage_edit"}), handler.getCommissionForAdmin)
	a.GET("/report", role.CheckPermission([]string{"marketing_manage", "marketing_manage_edit"}), handler.getAfReport)
	a.PUT("/update", role.CheckPermission([]string{"marketing_manage_edit"}), handler.updateCommission)
	// View from USER_ICON in USER_MODAL
	memberInfoRoute := r.Group("/user-related", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	// ประวัติการทำรายการ - เอาจากฝากถอนเสร็จสิ้น เชื่อมเสร็จแล้ว
	// memberInfoRoute.GET("/transactions/list", handler.getUserTransactionList)
	// แนะนำเพื่อน - เสร็จแล้ว
	memberInfoRoute.GET("/affiliate-member/list", handler.getUserAffiliateMemberList)
	// โปรโมชั่น - ยังไม่ได้ทำ ??? [20231108]
	// memberInfoRoute.GET("/promotions/list", handler.getUserPromotionList)

	// รายงานลิงค์รับทรัพย์ - รายชื่อผู้แนะนำเพื่อน + รายชื่อสมาชิกที่ถูกแนะนำพร้อมยอดฝากและยอดเล่นและยอดเครดิต
	reportRoute := r.Group("/affiliate-report", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	reportRoute.GET("/members/list", handler.getAffiliateUserList)
	reportRoute.GET("/summary", handler.getAffiliateUserSummary)
	reportRoute.GET("/summary-realtime", handler.getAffiliateUserSummaryRealtime)
	reportRoute.GET("/deposit-play/list", handler.getAffiliateDepositPlayList)
	reportRoute.GET("/deposit-play/count-level", handler.getAffiliateDepositPlayCountLevel)
	reportRoute.GET("/transaction/list", handler.getAffTransactionListSummary)
	// ADMIN-DEBUGMODE
	debugRoute := r.Group("/affiliates-admin", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	debugRoute.GET("/summary", handler.getUserAffiliateIncomeSummary)
	debugRoute.GET("/income-log", handler.getUserIncomeWebLogList)

	manualRoute := r.Group("/affiliate-manual")
	manualRoute.POST("/members/backup-list", handler.backupAffiliateUserList)

	// /web/affiliates/detail [get]

	webRoute := r.Group("/web", middleware.AuthorizeUser)
	webRoute.GET("/affiliates/detail", handler.getWebAffiliateCommissionDetail)

}

// @Summary (getUserAffiliateIncomeSummary) Get Affiliate Summary
// @Description (getUserAffiliateIncomeSummary) Get Affiliate Summary
// @Tags Affiliates - AdminMode
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.GetUserAliianceSummaryRequest true "GetUserAliianceSummaryRequest"
// @Success 200 {object} model.AfSummary
// @Failure 400 {object} handler.ErrorResponse
// @Router /affiliates-admin/summary [get]
func (h affiliateController) getUserAffiliateIncomeSummary(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))
	var req model.GetUserAliianceSummaryRequest
	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}
	req.AdminId = adminId
	if err := validator.New().Struct(req); err != nil {
		HandleError(c, err)
		return
	}

	result, err := h.affiliateService.Summary(req.UserId)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, result)
}

// @Summary (getUserIncomeWebLogList)
// @Description (getUserIncomeWebLogList)
// @Tags Affiliates - AdminMode
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.UserIncomeWebLogListAdminRequest true "UserIncomeWebLogListAdminRequest"
// @Success 200 {object} model.AfIncomeResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /affiliates-admin/income-log [get]
func (h affiliateController) getUserIncomeWebLogList(c *gin.Context) {

	var req model.UserIncomeWebLogListAdminRequest
	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}

	var req2 model.UserIncomeWebLogListRequest
	req2.UserId = req.UserId
	req2.FromDate = req.FromDate
	req2.ToDate = req.ToDate
	req2.TypeId = req.TypeId
	req2.StatusId = req.StatusId
	req2.Page = req.Page
	req2.Limit = req.Limit
	req2.SortCol = req.SortCol
	req2.SortAsc = req.SortAsc

	result, err := h.affiliateService.GetUserIncomeWebLogList(req2)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, result)
}

// @Summary (getUserAffiliateMemberList) ข้อมูลสมาชิก - แนะนำเพื่อน
// @Description (getUserAffiliateMemberList) Get User Affiliate Member List
// @Tags User - Related Detail
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.AffiliateMemberListRequest true "query"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /user-related/affiliate-member/list [get]
func (h affiliateController) getUserAffiliateMemberList(c *gin.Context) {

	var query model.AffiliateMemberListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	list, total, err := h.affiliateService.GetUserAffiliateMemberList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithPagination{Message: "Success", List: list, Total: total})
}

// @Summary Get Affiliate Summary
// @Description Get Affiliate Summary
// @Tags Affiliates
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.AfSummary
// @Failure 400 {object} handler.ErrorResponse
// @Router /affiliates/summary [get]
func (h affiliateController) summary(c *gin.Context) {

	id := int64(c.MustGet("userId").(float64))

	result, err := h.affiliateService.Summary(id)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, result)
}

// @Summary (getMember) Get Affiliate Member
// @Description (getMember) Get Affiliate Member
// @Tags Affiliates
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.AffMemberListRequest true "AffMemberListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /affiliates/member [get]
func (h affiliateController) getMember(c *gin.Context) {

	id := int64(c.MustGet("userId").(float64))

	var req model.AffMemberListRequest
	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}
	req.RefBy = &id

	result, err := h.affiliateService.GetActiveAffMemberByLevel(req)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, result)
}

// @Summary Get Affiliate Income
// @Description Get Affiliate Income
// @Tags Affiliates
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.AffMemberListRequest true "AffMemberListRequest"
// @Success 200 {object} model.AfIncomeResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /affiliates/income [get]
func (h affiliateController) getIncome(c *gin.Context) {

	id := int64(c.MustGet("userId").(float64))

	var req model.AffMemberListRequest
	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}

	result, err := h.affiliateService.GetIncome(id, req)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, result)
}

// @Summary (getIncomeWebLogList) แสดงหน้าเว็บ แนะนำเพื่อน - สรุปรายได้
// @description # typeName ตัวกรองประเภท
// @Description | typeName | ชื่อแสดง | หมายเหตุ |
// @Description | ---------------- | ------------| ------------|
// @Description | ไม่ส่ง |  ทั้งหมด | แสดงทั้งหมด |
// @Description | 1 | แนะนำเพื่อนสมัครรับรายได้ | พาเพื่อนมาสมัครสำเร็จ |
// @Description | 2 | แนะนำเพื่อนสมัครฝากครั้งแรก | เพื่อนฝากครั้งแรกสำเร็จ |
// @Description | 3 | แนะนำเพื่อนรายได้คอมมิชชั่น | เพื่อนมียอดเทิร์น |
// @Description | ---------------- | ------------|
// @description # statusId ตัวกรองสถานะ
// @Description | StatusId | ชื่อแสดง | หมายเหตุ |
// @Description | ---------------- | ------------| ------------|
// @Description | ไม่ส่ง | ทั้งหมด | แสดงทั้งหมด |
// @Description | 1 | รอกดรับ | ได้เงินแล้วรอกดรับ |
// @Description | 2 | หมดอายุ | เงินรอรับเกินวันที่กำหนดไว้ |
// @Description | 3 | รออนุมัติ | กดรับแล้วยอดเกินกำหนดให้รออนุมัติ |
// @Description | 4 | รับแล้ว | กดรับแล้วหรืออนุมัติเงินเข้าแล้ว |
// @Description | ---------------- | ------------|
// @Description (getIncomeWebLogList)
// @Tags Affiliates
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.UserIncomeWebLogListRequest true "UserIncomeWebLogListRequest"
// @Success 200 {object} model.UserIncomeWebLogListResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /affiliates/income-log [get]
func (h affiliateController) getIncomeWebLogList(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var req model.UserIncomeWebLogListRequest
	req.UserId = userId
	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}

	result, err := h.affiliateService.GetUserIncomeWebLogList(req)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, result)
}

// @summary (getUserIncomeCompletedLogList) ประวัติการโอนเงินเข้ากระเป๋าหลัก
// @description (getUserIncomeCompletedLogList) Get User's Income Completed Log List
// @description # typeName ตัวกรองประเภท
// @Description | typeName | ข้อมูลที่ได้ | หมายเหตุ |
// @Description | ---------------- | ------------| ------------|
// @Description | ALL |  ทั้งหมด | แสดงทั้งหมด |
// @Description | AFFILIATE |  แนะนำเพื่อน | *ไม่แสดงในหน้าพันธมิตร |
// @Description | PROMOTION_RETURN_LOSS |  โปรโมชั่นคืนเงิน | คืนยอดเสีย |
// @Description | PROMOTION_RETURN_TURN |  โปรโมชั่นคืนยอดเทิร์น | คืนยอดเทิร์น |
// @Description | ALLIANCE |  พันธมิตร | *ไม่แสดงในหน้าลิ้งครับทรัพย์ |
// @Description | ---------------- | ------------|
// @Description
// @tags Affiliates
// @security BearerAuth
// @accept json
// @produce json
// @param _ query model.UserIncomeCompletedLogListRequest true "query"
// @success 200 {object} model.SuccessWithPagination
// @failure 400 {object} handler.ErrorResponse
// @router /affiliates/income-history [get]
func (h affiliateController) getUserIncomeCompletedLogList(c *gin.Context) {

	userId := int64(c.MustGet("userId").(float64))
	query := model.UserIncomeCompletedLogListRequest{}
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	query.UserId = userId
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	result, total, err := h.affiliateService.GetUserIncomeCompletedLogList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithPagination{Message: "Success", List: result, Total: total})
}

// @Summary (withdrawCommission) Withdraw Affiliate Commission
// @Description (withdrawCommission) Withdraw Affiliate Commission
// @Tags Affiliates
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 201 {object} string
// @Failure 400 {object} handler.ErrorResponse
// @Router /affiliates/withdraw-commission [post]
func (h affiliateController) withdrawCommission(c *gin.Context) {

	userId := int64(c.MustGet("userId").(float64))

	if err := h.affiliateService.WithdrawCommission(userId); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Withdraw success"})
}

// @Summary Get Affiliate Register Bonus Type
// @Description Get Affiliate Register Bonus Type
// @Tags Affiliates
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.AfRegisterBonusType
// @Failure 400 {object} handler.ErrorResponse
// @Router /affiliates/backoffice/register-bonus-type [get]
func (h affiliateController) getAfRegisterBonusType(c *gin.Context) {

	result, err := h.affiliateService.GetAfRegisterBonusType()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, result)
}

// @Summary Get Affiliate Register Bonus Option
// @Description Get Affiliate Register Bonus Option
// @Tags Affiliates
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.AfRegisterBonusOption
// @Failure 400 {object} handler.ErrorResponse
// @Router /affiliates/backoffice/register-bonus-option [get]
func (h affiliateController) getAfRegisterBonusOption(c *gin.Context) {

	result, err := h.affiliateService.GetAfRegisterBonusOption()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, result)
}

// @Summary (getCommissionForAdmin) จัดการการตลาด จัดการแนะนำเพื่อน ดึงข้อมูลการตั้งค่าลิ้งค์รับทรัพย์
// @Description (getCommissionForAdmin) Get Affiliate Commission
// @Tags Affiliates
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.AfCommissionResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /affiliates/backoffice/commission [get]
func (h affiliateController) getCommissionForAdmin(c *gin.Context) {

	result, err := h.affiliateService.GetCommissionSetting()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, result)
}

// @Summary Get Affiliate Report
// @Description Get Affiliate Report
// @Tags Affiliates
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param _ query model.AfReportQuery true "Query Affiliate"
// @Success 200 {object} string
// @Failure 400 {object} handler.ErrorResponse
// @Router /affiliates/backoffice/report [get]
func (h affiliateController) getAfReport(c *gin.Context) {

	var query model.AfReportQuery
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}

	result, err := h.affiliateService.GetAfReport(query)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, result)
}

// @Summary (updateCommission) จัดการการตลาด จัดการแนะนำเพื่อน อัพเดทการตั้งค่าลิ้งค์รับทรัพย์
// @Description (updateCommission) Update Affiliate Commission
// @Tags Affiliates
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.AfCommissionUpdateRequest true "body"
// @Success 201 {object} string
// @Failure 400 {object} handler.ErrorResponse
// @Router /affiliates/backoffice/update [put]
func (h affiliateController) updateCommission(c *gin.Context) {

	var body model.AfCommissionUpdateRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))
	body.UpdateById = adminId

	if err := h.affiliateService.UpdateCommission(body); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Update success"})
}

// @Summary (getAffiliateUserList) รายงานลิงก์รับทรัพย์ - รายชื่อผู้แนะนำเพื่อน | รายงานการตลาด > รายงานแนะนำเพื่อน
// @Description (getAffiliateUserList)
// @Description # การกรองวันที่ กรองเพื่อเอาข้อมูลตัวเลขเฉพาะช่วงที่เลือก
// @Description กรองข้อมูลตามประเภทวัน ส่ง dateType เป็น today, yesterday, last_week, last_month
// @Description กรองข้อมูลตามช่วงวันที่ ส่ง fromDate, toDate ในรูปแบบ YYYY-MM-DD (2021-12-31)
// @Description *ถ้าเลือกทั้งหมดไม่ต้องส่งเลย
// @Tags Affiliates - Report
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.AffiliateUserListRequest true "query"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /affiliate-report/members/list [get]
func (h affiliateController) getAffiliateUserList(c *gin.Context) {

	var query model.AffiliateUserListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	list, total, err := h.affiliateService.GetAffiliateUserList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithPagination{Message: "Success", List: list, Total: total})
}

// @Summary (backupAffiliateUserList)
// @Description (backupAffiliateUserList)
// @Tags Affiliates - Manual
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.AffiliateUserListRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /affiliate-manual/members/backup-list [post]
func (h affiliateController) backupAffiliateUserList(c *gin.Context) {

	var body model.AffiliateUserListRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.affiliateService.MakeReportAffiliateUserList(body); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "Backup success"})
}

// @Summary (getAffiliateDepositPlayList) รายงานลิงก์รับทรัพย์ - รายชื่อสมาชิกที่ถูกแนะนำพร้อมยอดฝากและยอดเล่นและยอดเครดิต
// @Description (getAffiliateDepositPlayList)
// @Description #การกรองวันที่ กรองเพื่อเอาข้อมูลตัวเลขเฉพาะช่วงที่เลือก
// @Description กรองข้อมูลตามประเภทวัน ส่ง dateType เป็น today, yesterday, last_week, last_month
// @Description กรองข้อมูลตามช่วงวันที่ ส่ง fromDate, toDate ในรูปแบบ YYYY-MM-DD (2021-12-31)
// @Description *ถ้าเลือกทั้งหมดไม่ต้องส่งเลย
// @Tags Affiliates - Report
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.AffiliateDepositPlayListRequest true "query"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /affiliate-report/deposit-play/list [get]
func (h affiliateController) getAffiliateDepositPlayList(c *gin.Context) {

	var query model.AffiliateDepositPlayListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	list, total, err := h.affiliateService.GetAffiliateDepositPlayList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithPagination{Message: "Success", List: list, Total: total})
}

// @Summary (getAffiliateDepositPlayCountLevel) รายงานลิงก์รับทรัพย์ - จำนวนสมาชิกที่ถูกแนะนำ ตามเลเวล
// @Description (getAffiliateDepositPlayCountLevel)
// @Description #การกรองวันที่ กรองเพื่อเอาข้อมูลตัวเลขเฉพาะช่วงที่เลือก
// @Description กรองข้อมูลตามประเภทวัน ส่ง dateType เป็น today, yesterday, last_week, last_month
// @Description กรองข้อมูลตามช่วงวันที่ ส่ง fromDate, toDate ในรูปแบบ YYYY-MM-DD (2021-12-31)
// @Description *ถ้าเลือกทั้งหมดไม่ต้องส่งเลย
// @Tags Affiliates - Report
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.AffiliateDepositPlayListRequest true "query"
// @Success 200 {object} model.AffiliateUserDownlineCountResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /affiliate-report/deposit-play/count-level [get]
func (h affiliateController) getAffiliateDepositPlayCountLevel(c *gin.Context) {

	var query model.AffiliateDepositPlayListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.affiliateService.GetAffiliateDepositPlayCountLevel(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (getAffiliateUserSummary) Get Affiliate User Summary
// @Description (getAffiliateUserSummary) Get Affiliate User Summary
// @Tags Affiliates - Report
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.AffiliateUserSummaryRequest true "AffiliateUserSummaryRequest"
// @Success 200 {object} model.AffiliateUserSummary
// @Failure 400 {object} handler.ErrorResponse
// @Router /affiliate-report/summary [get]
func (h affiliateController) getAffiliateUserSummary(c *gin.Context) {

	var query model.AffiliateUserSummaryRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	result, err := h.affiliateService.GetAffiliateUserSummary(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, result)
}

// @Summary (getAffiliateUserSummaryRealtime) ทดสอบเทียบกับที่ออกรายงานไว้
// @Description (getAffiliateUserSummaryRealtime) ทดสอบเทียบกับที่ออกรายงานไว้
// @Tags Affiliates - Report
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.AffiliateUserSummaryRequest true "AffiliateUserSummaryRequest"
// @Success 200 {object} model.AffiliateUserSummary
// @Failure 400 {object} handler.ErrorResponse
// @Router /affiliate-report/summary-realtime [get]
func (h affiliateController) getAffiliateUserSummaryRealtime(c *gin.Context) {

	var query model.AffiliateUserSummaryRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	result, err := h.affiliateService.GetAffiliateUserSummaryRealTime(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, result)
}

// GetAffTransactionListSummary(req model.AffTransactionListRequest) (*model.AffTransactionSummaryResponse, error)
// @Summary (getAffTransactionListSummary) Get Affiliate Transaction List Summary
// @Description (getAffTransactionListSummary) Get Affiliate Transaction List Summary
// @Tags Affiliates - Report
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.AffTransactionListRequest true "AffTransactionListRequest"
// @Success 200 {object} model.AffTransactionSummaryResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /affiliate-report/transaction/list [get]
func (h affiliateController) getAffTransactionListSummary(c *gin.Context) {

	var query model.AffTransactionListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	result, err := h.affiliateService.GetAffTransactionListSummary(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, result)
}

// @Summary (getAfReport) Get User Affiliate Report Option Level
// @Description (getAfReport) Get Affiliate Report
// @Tags Affiliates
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} []model.SelectOptions
// @Failure 400 {object} handler.ErrorResponse
// @Router /affiliates/member/option-level [get]
func (h affiliateController) AfMemberOptionLevel(c *gin.Context) {

	data := []model.SelectOptions{
		{Id: 1, Value: "1", Label: "ระดับชั้น 1"},
		{Id: 2, Value: "2", Label: "ระดับชั้น 2"},
		{Id: 3, Value: "3", Label: "ระดับชั้น 3"},
	}
	c.JSON(200, data)
}

// @Summary (getWebAffiliateCommissionDetail) Get Web Affiliate Commission Detail
// @Description (getWebAffiliateCommissionDetail) Get Web Affiliate Commission Detail
// @Tags Affiliates - web
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.GetWebAffiliateCommissionDetailResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/affiliates/detail [get]
func (h affiliateController) getWebAffiliateCommissionDetail(c *gin.Context) {

	result, err := h.affiliateService.GetWebAffiliateCommissionDetail()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, result)
}
