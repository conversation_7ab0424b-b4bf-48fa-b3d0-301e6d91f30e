package handler

import (
	"cybergame-api/helper"
	"cybergame-api/middleware"
	"cybergame-api/model"
	"cybergame-api/repository"
	"cybergame-api/service"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type categoryGameSettingController struct {
	categoryGameSettingService service.CategoryGameSettingService
}

func newCategoryGameSettingController(
	categoryGameSettingService service.CategoryGameSettingService,
) categoryGameSettingController {
	return categoryGameSettingController{categoryGameSettingService}
}

func CategoryGameSettingController(r *gin.RouterGroup, db *gorm.DB) {

	singleSession := middleware.SingleSession(db)
	role := middleware.Role(db)

	repo := repository.NewCategoryGameSettingRepository(db)
	service := service.NewCategoryGameSettingService(repo)
	handler := newCategoryGameSettingController(service)

	categoryGameSettingRoute := r.Group("/category-game-setting", middleware.AuthorizeAdmin, singleSession.SingleAdminSession(), role.CheckPermission([]string{"setting"}))
	categoryGameSettingRoute.PUT("", handler.UpdateCategoryGameSetting)
	categoryGameSettingRoute.GET("", handler.GetCategoryGameSetting)

	webCategoryGameSettingRoute := r.Group("/web/category-game-setting", middleware.AuthorizeUser)
	webCategoryGameSettingRoute.GET("", handler.WebGetCategoryGameSetting)

	categoryGameSettingRoutev2 := r.Group("/category-game-setting/v2", middleware.AuthorizeAdmin, singleSession.SingleAdminSession(), role.CheckPermission([]string{"setting"}))
	categoryGameSettingRoutev2.GET("", handler.GetCategoryGameSettingV2)
	categoryGameSettingRoutev2.PUT("/sort", handler.SortGetCategoryGameSettingV2)
	categoryGameSettingRoutev2.PUT("/:id", handler.UpdateCategoryGameSettingV2)

	webCategoryGameSettingRoutev2 := r.Group("/web/category-game-setting/v2")
	webCategoryGameSettingRoutev2.GET("", handler.WebGetCategoryGameSettingV2)

}

// @Summary (UpdateCategoryGameSetting)
// @Description (UpdateCategoryGameSetting)
// @Tags ADMIN - Category Game Setting
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.UpdateCategoryGameSettingBody true "UpdateCategoryGameSettingBody"
// @Success 200
// @Failure 400 {object} handler.ErrorResponse
// @Router /category-game-setting [put]
func (h categoryGameSettingController) UpdateCategoryGameSetting(c *gin.Context) {

	var req model.UpdateCategoryGameSettingBody
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))
	req.UpdatedById = adminId
	if err := h.categoryGameSettingService.UpdateCategoryGameSetting(req); err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(201, model.Success{Message: "Updated success"})
}

// @Summary (GetCategoryGameSetting)
// @Description (GetCategoryGameSetting)
// @Tags ADMIN - Category Game Setting
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.GetCategoryGameSettingResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /category-game-setting [get]
func (h categoryGameSettingController) GetCategoryGameSetting(c *gin.Context) {

	record, err := h.categoryGameSettingService.GetCategoryGameSetting()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, record)
}

// @Summary (WebGetCategoryGameSetting)
// @Description (WebGetCategoryGameSetting)
// @Tags WEB - Category Game Setting
// @Accept json
// @Produce json
// @Success 200 {object} model.GetCategoryGameSettingResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/category-game-setting [get]
func (h categoryGameSettingController) WebGetCategoryGameSetting(c *gin.Context) {

	record, err := h.categoryGameSettingService.WebGetCategoryGameSetting()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, record)
}

// @Summary (GetCategoryGameSettingV2)
// @Description (GetCategoryGameSettingV2)
// @Tags ADMIN - Category Game Setting V2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} []model.GetCategoryGameSettingV2
// @Failure 400 {object} handler.ErrorResponse
// @Router /category-game-setting/v2 [get]
func (h categoryGameSettingController) GetCategoryGameSettingV2(c *gin.Context) {

	record, err := h.categoryGameSettingService.GetCategoryGameSettingV2()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, record)
}

// @Summary (SortGetCategoryGameSettingV2)
// @Description (SortGetCategoryGameSettingV2)
// @Tags ADMIN - Category Game Setting V2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.DragSortRequest true "DragSortRequest"
// @Success 200
// @Failure 400 {object} handler.ErrorResponse
// @Router /category-game-setting/v2/sort [put]
func (h categoryGameSettingController) SortGetCategoryGameSettingV2(c *gin.Context) {

	var req model.DragSortRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.categoryGameSettingService.SortGetCategoryGameSettingV2(req); err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, "success")
}

// @Summary (WebGetCategoryGameSettingV2)
// @Description (WebGetCategoryGameSettingV2)
// @Tags ADMIN - Category Game Setting V2
// @Accept json
// @Produce json
// @Success 200 {object} []model.GetCategoryGameSettingV2
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/category-game-setting/v2 [get]
func (h categoryGameSettingController) WebGetCategoryGameSettingV2(c *gin.Context) {

	record, err := h.categoryGameSettingService.WebGetCategoryGameSettingV2()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, record)
}

// @Summary (UpdateCategoryGameSettingV2)
// @Description (UpdateCategoryGameSettingV2)
// @Tags ADMIN - Category Game Setting V2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "ID of the category game setting to update"
// @Param body body model.UpdateCategoryGameSettingV2 true "UpdateCategoryGameSettingV2"
// @Success 200
// @Failure 400 {object} handler.ErrorResponse
// @Router /category-game-setting/v2/{id} [put]
func (h categoryGameSettingController) UpdateCategoryGameSettingV2(c *gin.Context) {

	var req model.UpdateCategoryGameSettingV2
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	id := c.Param("id")
	identifier, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleError(c, err)
		return
	}
	req.Id = identifier

	if err := h.categoryGameSettingService.UpdateCategoryGameSettingV2(req); err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, model.Success{Message: "Updated success"})
}
