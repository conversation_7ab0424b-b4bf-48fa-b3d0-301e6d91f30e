package handler

import (
	"cybergame-api/helper"
	"cybergame-api/middleware"
	"cybergame-api/model"
	"cybergame-api/repository"
	"cybergame-api/service"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type editSectionController struct {
	editSectionService service.EditSectionService
}

func newEditSectionController(
	editSectionService service.EditSectionService,
) editSectionController {
	return editSectionController{editSectionService}
}

func EditSectionController(r *gin.RouterGroup, db *gorm.DB) {

	singleSession := middleware.SingleSession(db)
	role := middleware.Role(db)

	repo := repository.NewEditSectionRepository(db)
	service := service.NewEditSectionService(repo)
	handler := newEditSectionController(service)

	editSectionRoute := r.Group("/edit-section", middleware.AuthorizeAdmin, singleSession.SingleAdminSession(), role.CheckPermission([]string{"setting"}))
	editSectionRoute.PUT("", handler.UpdateEditSection)
	editSectionRoute.GET("", handler.GetEditSection)

	webEditSectionRoute := r.Group("/web/edit-section", middleware.AuthorizeUser)
	webEditSectionRoute.GET("", handler.WebGetEditSection)

}

// @Summary (UpdateEditSection)
// @Description (UpdateEditSection)
// @Tags ADMIN - Edit Section
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.UpdateEditSectionBody true "UpdateEditSectionBody"
// @Success 200
// @Failure 400 {object} handler.ErrorResponse
// @Router /edit-section [put]
func (h editSectionController) UpdateEditSection(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	var req model.UpdateEditSectionBody
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	req.UpdatedById = adminId
	if err := h.editSectionService.UpdateEditSection(req); err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(201, model.Success{Message: "Updated success"})
}

// @Summary (GetEditSection)
// @Description (GetEditSection)
// @Tags ADMIN - Edit Section
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.GetEditSectionResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /edit-section [get]
func (h editSectionController) GetEditSection(c *gin.Context) {

	record, err := h.editSectionService.GetEditSection()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, record)
}

// @Summary (WebGetEditSection)
// @Description (WebGetEditSection)
// @Tags WEB - Edit Section
// @Accept json
// @Produce json
// @Success 200 {object} model.GetEditSectionResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/edit-section [get]
func (h editSectionController) WebGetEditSection(c *gin.Context) {

	record, err := h.editSectionService.WebGetEditSection()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, record)
}
