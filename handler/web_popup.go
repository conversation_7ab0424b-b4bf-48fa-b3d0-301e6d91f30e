package handler

import (
	"cybergame-api/middleware"
	"cybergame-api/model"
	"cybergame-api/repository"
	"cybergame-api/service"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"gorm.io/gorm"
)

type webPopupController struct {
	webPopupService service.WebPopupService
}

func newWebPopupController(
	webPopupService service.WebPopupService,
) webPopupController {
	return webPopupController{webPopupService}
}

func WebPopupController(r *gin.RouterGroup, db *gorm.DB) {

	singleSession := middleware.SingleSession(db)

	repo := repository.NewWebPopupRepository(db)
	service := service.NewWebPopupService(repo)
	handler := newWebPopupController(service)

	managePopupRoute := r.Group("/web-popup", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	managePopupRoute.GET("/list", handler.getWebPopupList)
	managePopupRoute.GET("/detail/:id", handler.getWebPopupById)
	managePopupRoute.POST("/create", handler.createWebPopup)
	managePopupRoute.PATCH("/:id", handler.updateWebPopup)
	managePopupRoute.POST("/sort", handler.sortOrderWebPopup)
	managePopupRoute.DELETE("/:id", handler.deleteWebPopup)
	managePopupRoute.POST("/upload/image", handler.webpopUploadImageToCloudFlare)

	webRoute := r.Group("/web/web-popup")
	webRoute.GET("/list", handler.getWebPopupShowList)

	// getAllUploads
	r.GET("/getallimage", handler.getAllUploads)
}

// @Summary (getWebPopupList)
// @Description (getWebPopupList)
// @Tags WebPopups - WebPopup
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.WebPopupListRequest true "WebPopupListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /web-popup/list [get]
func (h webPopupController) getWebPopupList(c *gin.Context) {

	var query model.WebPopupListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.webPopupService.GetWebPopupList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithPagination{List: data.List, Total: data.Total})
}

// @Summary (getWebPopupById)
// @Description (getWebPopupById)
// @Tags WebPopups - WebPopup
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /web-popup/detail/{id} [get]
func (h webPopupController) getWebPopupById(c *gin.Context) {

	var query model.GetByIdRequest
	if err := c.ShouldBindUri(&query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.webPopupService.GetWebPopupById(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithData{Message: "success", Data: data})
}

// @Summary (createWebPopup)
// @Description (createWebPopup)
// @Tags WebPopups - WebPopup
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.WebPopupCreateRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /web-popup/create [post]
func (h webPopupController) createWebPopup(c *gin.Context) {

	var body model.WebPopupCreateRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	if _, err := h.webPopupService.CreateWebPopup(body); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Created success"})
}

// @Summary (updateWebPopup)
// @Description (updateWebPopup)
// @Tags WebPopups - WebPopup
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Param body body model.WebPopupUpdateRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /web-popup/{id} [patch]
func (h webPopupController) updateWebPopup(c *gin.Context) {

	identifier, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		HandleError(c, err)
		return
	}

	body := model.WebPopupUpdateRequest{}
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.webPopupService.UpdateWebPopup(identifier, body); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Updated success"})
}

// @Summary (sortOrderWebPopup) เรียงลำดับ popup หน้าเว็บ ด้วย AntDrag
// @Description (sortOrderWebPopup)
// @Tags WebPopups - WebPopup
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.DragSortRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /web-popup/sort [post]
func (h webPopupController) sortOrderWebPopup(c *gin.Context) {

	body := model.DragSortRequest{}
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.webPopupService.SortOrderWebPopup(body); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Updated success"})
}

// @Summary (deleteWebPopup)
// @Description (deleteWebPopup)
// @Tags WebPopups - WebPopup
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /web-popup/{id} [delete]
func (h webPopupController) deleteWebPopup(c *gin.Context) {

	identifier, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		HandleError(c, err)
		return
	}

	if err := h.webPopupService.DeleteWebPopup(identifier); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Deleted success"})
}

// @Summary (getWebPopupShowList) แสดง popup หน้าเว็บ
// @Description (getWebPopupShowList)
// @Tags WEB - WebPopup
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.WebPopupListRequest true "WebPopupListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/web-popup/list [get]
func (h webPopupController) getWebPopupShowList(c *gin.Context) {

	var query model.WebPopupListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}
	show := true
	query.IsShow = &show

	data, err := h.webPopupService.GetWebPopupList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithPagination{List: data.List, Total: data.Total})
}

// @Summary (uploadImageToCloudFlare) อัพโหลดไฟล์ CloudFlare แบบ feature
// @Description (uploadImageToCloudFlare) Upload File CloudFlare
// @Tags WebPopups - WebPopup
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param file formData file true "file"
// @Success 200 {object} model.FileUploadResponse
// @Failure 400 {object} ErrorResponse
// @Router /web-popup/upload/image [post]
func (h webPopupController) webpopUploadImageToCloudFlare(c *gin.Context) {

	data, err := h.webPopupService.UploadImageToS3Webpop(c.Request)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

func (h webPopupController) getAllUploads(c *gin.Context) {
	// @Summary (getAllUploads) แสดงรายการไฟล์ที่อัพโหลดไป CloudFlare
	// @Description (getAllUploads) แสดงรายการไฟล์ที่อัพโหลดไป CloudFlare
	// @Tags WebPopups - WebPopup
	// @Security BearerAuth
	// @Accept json
	// @Produce json
	// @Success 200 {object} model.CloudFlareUploadResponse
	// @Failure 400 {object} ErrorResponse
	// @Router /getallimage [get]

	data, err := h.webPopupService.GetAllUploads()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}
