package handler

import (
	"cybergame-api/helper"
	"cybergame-api/middleware"
	"cybergame-api/model"
	"cybergame-api/repository"
	"cybergame-api/service"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type activityDailyV2Controller struct {
	activityDailyV2Service service.ActivityDailyV2Service
}

func newactivityDailyV2Controller(
	activityDailyV2Service service.ActivityDailyV2Service,
) activityDailyV2Controller {
	return activityDailyV2Controller{activityDailyV2Service}
}

func ActivityDailyV2Controller(r *gin.RouterGroup, db *gorm.DB) {

	activityDailyV2 := repository.NewActivityDailyV2Repository(db)
	activityDailyV2Service := service.NewActivityDailyV2Service(db, activityDailyV2)
	handler := newactivityDailyV2Controller(activityDailyV2Service)

	rootActivityDailyV2 := r.Group("/activity-daily-v2", middleware.AuthorizeAdmin)
	rootActivityDailyV2.POST("", handler.createActivityDailyV2Total)
	rootActivityDailyV2.GET("", handler.getActivityDailyV2)
	rootActivityDailyV2.GET("/turnover-user/list", handler.GetTurnoverUserActivityDailyV2)

	rootActivityDailyV2UserRevice := r.Group("/activity-daily-v2/user-revice", middleware.AuthorizeUser)
	rootActivityDailyV2UserRevice.POST("", handler.UserReviceActivityDailyV2)

	rootActivityDailyV2Web := r.Group("/activity-daily-v2/web", middleware.AuthorizeUser)
	rootActivityDailyV2Web.GET("", handler.WebGetActivityDailyV2)

	rootActivityDailyV2ConditionOption := r.Group("/activity-daily-v2/option", middleware.AuthorizeAdmin)
	rootActivityDailyV2ConditionOption.GET("/condition", handler.GetActivityDailyV2TotalConditionOptions)
}

// @Summary CreateActivityDailyV2Total
// @Description CreateActivityDailyV2Total
// @Tags Activity Daily V2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.CreateActivityDailyV2Request true "body"
// @Success 200 {object} int64
// @Failure 400 {object} handler.ErrorResponse
// @Router /activity-daily-v2 [post]
func (h *activityDailyV2Controller) createActivityDailyV2Total(c *gin.Context) {

	var req model.CreateActivityDailyV2Request
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))
	req.UpdatedById = adminId
	req.UpdatedAt = time.Now().UTC()

	if err := h.activityDailyV2Service.CreateActivityDailyV2Total(req); err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, "SUCCESS")
}

// @Summary GetTurnoverUserActivityDailyV2
// @Description GetTurnoverUserActivityDailyV2
// @Tags Activity Daily V2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.GetTurnoverUserActivityDailyV2Request true "query"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /activity-daily-v2/turnover-user/list [get]
func (h *activityDailyV2Controller) GetTurnoverUserActivityDailyV2(c *gin.Context) {

	var req model.GetTurnoverUserActivityDailyV2Request
	if err := c.ShouldBindQuery(&req); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.activityDailyV2Service.GetTurnoverUserActivityDailyV2(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary GetActivityDailyV2
// @Description GetActivityDailyV2
// @Tags Activity Daily V2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.GetActivityDailyV2TotalResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /activity-daily-v2 [get]
func (h *activityDailyV2Controller) getActivityDailyV2(c *gin.Context) {

	result, err := h.activityDailyV2Service.GetActivityDailyV2()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, result)
}

// @Summary UserReviceActivityDailyV2
// @Description UserReviceActivityDailyV2
// @Tags Activity Daily V2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} string
// @Failure 400 {object} handler.ErrorResponse
// @Router /activity-daily-v2/user-revice [post]
func (h *activityDailyV2Controller) UserReviceActivityDailyV2(c *gin.Context) {

	var req model.UserReviceActivityDailyV2Request
	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))
	req.UserId = userId

	if err := h.activityDailyV2Service.UserReviceActivityDailyV2(req); err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, "SUCCESS")
}

// WebGetActivityDailyV2(userId int64) ([]model.WebActivityDailyV2ReviceDetail, error)
// @Summary WebGetActivityDailyV2
// @Description WebGetActivityDailyV2
// @Tags Activity Daily V2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} []model.WebActivityDailyV2ReviceDetail
// @Failure 400 {object} handler.ErrorResponse
// @Router /activity-daily-v2/web [get]
func (h *activityDailyV2Controller) WebGetActivityDailyV2(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	result, err := h.activityDailyV2Service.WebGetActivityDailyV2(userId)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, result)
}

// @Summary GetActivityDailyV2TotalConditionOptions
// @Description GetActivityDailyV2TotalConditionOptions
// @Tags Activity Daily V2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} []model.SelectOptions
// @Failure 400 {object} handler.ErrorResponse
// @Router /activity-daily-v2/option/condition [get]
func (h *activityDailyV2Controller) GetActivityDailyV2TotalConditionOptions(c *gin.Context) {

	result, err := h.activityDailyV2Service.GetActivityDailyV2TotalConditionOptions()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, result)
}
