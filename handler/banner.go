package handler

import (
	"cybergame-api/model"
	"cybergame-api/service"
	"strconv"

	"cybergame-api/repository"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

type bannerController struct {
	BannerService service.BannerService
}

func newBannerController(
	BannerService service.BannerService,
) bannerController {
	return bannerController{BannerService}
}

func BannerController(r *gin.RouterGroup, db *gorm.DB, rdb *redis.Client) {

	repo := repository.NewBannerRepository(db)
	fileRepo := repository.NewFileRepository(db)
	cacheRepo := repository.NewCacheRepository(rdb)
	service := service.NewBannerService(repo, fileRepo, cacheRepo)
	handler := newBannerController(service)

	banner := r.Group("/banner")
	banner.GET("web", handler.getBannerIsActive)
	banner.GET("list", handler.getBannerList)
	banner.GET("shop", handler.getBannerShopList)
	banner.GET("ticket", handler.getBannerTicketList)
	banner.GET("type", handler.getBannerTypeList)
	banner.POST("", handler.createBanner)
	banner.POST("sort", handler.updateBannerSortOrder)
	banner.PUT(":bannerId", handler.updateBanner)
	banner.PUT("shop/:bannerId", handler.updateBannerShop)
	banner.PUT("ticket/:bannerId", handler.updateBannerTicket)
	banner.DELETE(":bannerId", handler.deleteBanner)
}

// @Summary แสดงแบนเนอร์ที่ Active
// @Description Get Banner Active
// @Tags Banner
// @Accept  json
// @Produce  json
// @Param bannerType query model.BannerWebQuery true "bannerType"
// @Success 200 {object} model.BannerListResponse
// @Failure 400 {object} ErrorResponse
// @Router /banner/web [get]
func (h bannerController) getBannerIsActive(c *gin.Context) {

	query := model.BannerWebQuery{}

	if err := c.ShouldBindQuery(&query); err != nil {
		HandleError(c, err)
		return
	}

	result, err := h.BannerService.BannerGetIsActive(query)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, result)
}

// @Summary แสดงแบนเนอร์ทั้งหมด
// @Description Get Banner List
// @Tags Banner
// @Accept  json
// @Security BearerAuth
// @Produce  json
// @Param bannerType query model.BannerQuery true "bannerType"
// @Success 200 {object} model.BannerListResponse
// @Failure 400 {object} ErrorResponse
// @Router /banner/list [get]
func (h bannerController) getBannerList(c *gin.Context) {

	query := model.BannerQuery{}

	if err := c.ShouldBindQuery(&query); err != nil {
		HandleError(c, err)
		return
	}

	result, err := h.BannerService.BannerGetList(query)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, result)
}

// @Summary แสดงแบนเนอร์ร้านค้าด้านบน
// @Description Get Banner Shop List
// @Tags Banner
// @Accept  json
// @Security BearerAuth
// @Produce  json
// @Success 200 {object} model.BannerShopListResponse
// @Failure 400 {object} ErrorResponse
// @Router /banner/shop [get]
func (h bannerController) getBannerShopList(c *gin.Context) {

	result, err := h.BannerService.BannerGetShopList()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, result)
}

// @Summary แสดงแบนเนอร์ขายตั๋ว
// @Description Get Banner Ticket List
// @Tags Banner
// @Accept  json
// @Security BearerAuth
// @Produce  json
// @Success 200 {object} model.BannerShopListResponse
// @Failure 400 {object} ErrorResponse
// @Router /banner/ticket [get]
func (h bannerController) getBannerTicketList(c *gin.Context) {

	result, err := h.BannerService.BannerGetTicketList()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, result)
}

// @Summary แสดงประเภทแบนเนอร์ทั้งหมด
// @Description Get Banner Type List
// @Tags Banner
// @Accept  json
// @Security BearerAuth
// @Produce  json
// @Success 200 {object} []string
// @Failure 400 {object} ErrorResponse
// @Router /banner/type [get]
func (h bannerController) getBannerTypeList(c *gin.Context) {

	result, err := h.BannerService.BannerGetTypeList()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, result)
}

// @Summary สร้างข้อมูลแบนเนอร์
// @Description Create Banner
// @Tags Banner
// @Accept  json
// @Security BearerAuth
// @Produce  json
// @Param body body model.BannerCreateBody true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} ErrorResponse
// @Router /banner [post]
func (h bannerController) createBanner(c *gin.Context) {

	body := model.BannerCreateBody{}

	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.BannerService.BannerCreate(body); err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(201, model.Success{Message: "Created success"})
}

// @Summary อัพเดทลำดับแบนเนอร์
// @Description Update Banner Sort Order
// @Tags Banner
// @Accept  json
// @Security BearerAuth
// @Produce  json
// @Param body body model.BannerSortBody true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} ErrorResponse
// @Router /banner/sort [post]
func (h bannerController) updateBannerSortOrder(c *gin.Context) {

	body := model.BannerSortBody{}

	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.BannerService.BannerUpdateSortOrder(body); err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(201, model.Success{Message: "Updated success"})
}

// @Summary อัพเดทแบนเนอร์ของ SHOP_BOTTOM, STAFF, PLAYER, YOUTH
// @Description Update Banner
// @Tags Banner
// @Accept  json
// @Security BearerAuth
// @Produce  json
// @Param bannerId path int true "bannerId"
// @Param body body model.BannerUpdateBody true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} ErrorResponse
// @Router /banner/{bannerId} [put]
func (h bannerController) updateBanner(c *gin.Context) {

	bannerId := c.Param("bannerId")
	body := model.BannerUpdateBody{}

	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}

	bannerIdInt, err := strconv.ParseInt(bannerId, 10, 64)
	if err != nil {
		HandleError(c, err)
		return
	}

	body.Id = bannerIdInt

	if err := h.BannerService.BannerUpdate(body); err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(201, model.Success{Message: "Updated success"})
}

// @Summary เลือกแสดงแบนเนอร์หน้าร้านค้าด้านบน
// @Description Update Banner Shop
// @Tags Banner
// @Accept  json
// @Security BearerAuth
// @Produce  json
// @Param bannerId path int true "bannerId"
// @Success 201 {object} model.Success
// @Failure 400 {object} ErrorResponse
// @Router /banner/shop/{bannerId} [put]
func (h bannerController) updateBannerShop(c *gin.Context) {

	bannerId := c.Param("bannerId")

	if err := h.BannerService.BannerUpdateShop(bannerId); err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(201, model.Success{Message: "Updated success"})
}

// @Summary เลือกแสดงแบนเนอร์หน้าขายตั๋ว
// @Description Update Banner Ticket
// @Tags Banner
// @Accept  json
// @Security BearerAuth
// @Produce  json
// @Param bannerId path int true "bannerId"
// @Success 201 {object} model.Success
// @Failure 400 {object} ErrorResponse
// @Router /banner/ticket/{bannerId} [put]
func (h bannerController) updateBannerTicket(c *gin.Context) {

	bannerId := c.Param("bannerId")

	if err := h.BannerService.BannerUpdateTicket(bannerId); err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(201, model.Success{Message: "Updated success"})
}

// @Summary ลบข้อมูลแบนเนอร์
// @Description Delete Banner
// @Tags Banner
// @Accept  json
// @Security BearerAuth
// @Produce  json
// @Param bannerId path int true "bannerId"
// @Success 201 {object} model.Success
// @Failure 400 {object} ErrorResponse
// @Router /banner/{bannerId} [delete]
func (h bannerController) deleteBanner(c *gin.Context) {

	bannerId := c.Param("bannerId")
	toInt64, err := strconv.ParseInt(bannerId, 10, 64)
	if err != nil {
		HandleError(c, err)
		return
	}

	if err := h.BannerService.BannerDelete(toInt64); err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(201, model.Success{Message: "Deleted success"})
}
