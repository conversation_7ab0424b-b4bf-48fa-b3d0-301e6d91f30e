package handler

import (
	"cybergame-api/helper"
	"cybergame-api/middleware"
	"cybergame-api/model"
	"cybergame-api/service"
	"errors"
	"log"
	"strconv"
	"strings"

	"cybergame-api/repository"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"gorm.io/gorm"
)

type userController struct {
	userService service.UserService
}

func newUserController(
	userService service.UserService,
) userController {
	return userController{userService}
}

func UserController(r *gin.RouterGroup, db *gorm.DB) {

	actionService := service.NewAdminActionService(repository.NewAdminActionRepository(db))
	repo := repository.NewUserRepository(db)
	perRepo := repository.NewPermissionRepository(db)
	groupRepo := repository.NewGroupRepository(db)
	otpRepo := repository.NewOtpRepository(db)
	agentInfoRepo := repository.NewAgentInfoRepository(db)
	recommendRepo := repository.NewRecommendRepository(db)
	afRepo := repository.NewAffiliateRepository(db)
	notiRepo := repository.NewNotificationRepository(db)
	notiService := service.NewNotificationService(notiRepo)
	service := service.NewUserService(
		repo,
		db,
		perRepo,
		groupRepo,
		otpRepo,
		agentInfoRepo,
		recommendRepo,
		afRepo,
		notiService,
		actionService,
	)
	handler := newUserController(service)

	role := middleware.Role(db)
	singleSession := middleware.SingleSession(db)

	testRoute := r.Group("/test", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	testRoute.POST("/encode-refby", handler.encodeRefBy)
	testRoute.POST("/decode-refby", handler.decodeRefBy)

	b := r.Group("/users", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	b.GET("/detail/:id", role.CheckPermission([]string{"member", "member_edit", "member_delete", "deposit_list", "withdraw_list"}), handler.GetUserDetail)
	b.GET("/options/deposit-rank", role.CheckPermission([]string{"member", "member_edit", "member_delete"}), handler.getUserDespositRankOptions)
	b.GET("/options/turnover-rank", role.CheckPermission([]string{"member", "member_edit", "member_delete"}), handler.getUserTurnOverRankOptions)
	b.GET("/list", role.CheckPermission([]string{"member", "member_edit", "member_delete"}), handler.getUserList)
	b.GET("/refresh-credit/:id", role.CheckPermission([]string{"member", "member_edit", "member_delete"}), handler.GetMemberRefreshCredit)
	b.GET("/updatelogs", role.CheckPermission([]string{"member", "member_edit", "member_delete"}), handler.getUpdateLogs)
	b.POST("/create", role.CheckPermission([]string{"member_edit", "member_delete"}), handler.createUser)
	b.PUT("/update/:id", role.CheckPermission([]string{"member_edit", "member_delete"}), handler.updateUser)
	b.PUT("/password/:id", role.CheckPermission([]string{"member_edit", "member_delete"}), handler.resetPassword)
	b.DELETE("/:id", role.CheckPermission([]string{"member_delete"}), handler.deleteUser)
	// b.GET("/export-xlsx", role.CheckPermission([]string{"member", "member_edit", "member_delete"}), handler.exportUserListXlsx)
	b.GET("/export-xlsx", role.CheckPermission([]string{"export_file_user"}), handler.exportUserListXlsx)

	rootLog := r.Group("/log", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	rootLog.GET("/user-login", handler.getLoginLogs)

	f := r.Group("/users")
	f.GET("/bank/list", handler.bankList)
	f.POST("/send-otp/register", handler.sendOtpRegister)
	f.POST("/verify-otp/register", handler.verifyOtpRegister)
	f.POST("/send-otp/forget", handler.sendOtpForget)
	f.POST("/register", handler.userRegister)
	f.POST("/login", handler.login)
	f.POST("/resetpassword/:userId", handler.frontResetPassword)
	f.POST("/line-login", handler.loginUserLine)
	f.POST("/line-register", handler.registerUserLine)

	f.GET("/register-setting", handler.getRegisterSetting)
	f.GET("/captcha", handler.generateUserCaptcha)
	f.POST("/check-phone", handler.checkNewUserPhone)

	fa := r.Group("/users", middleware.AuthorizeUser, singleSession.SingleUserSession(), middleware.Scam(db).CheckScam)
	fa.GET("/me", handler.GetMe)
	fa.GET("/game-token", handler.getGameToken)
	fa.PUT("/changepassword", handler.frontChangePassword)

	inactiveUserRoute := r.Group("/inactive-users", middleware.AuthorizeAdmin, singleSession.SingleAdminSession(), role.CheckPermission([]string{"send_sms"}))
	inactiveUserRoute.GET("/list", handler.getInactiveUserList)
	inactiveUserRoute.POST("/remove-selected", handler.removeInactiveUser)
	// inactiveUserRoute.GET("/export-xlsx", role.CheckPermission([]string{"member", "member_edit"}), handler.exportInactiveUserXlsx)
	inactiveUserRoute.GET("/export-xlsx", handler.exportInactiveUserXlsx)

	adminRoute := r.Group("/admin", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	adminRoute.GET("/gen-membercode/:id", handler.GenMemberCode)

	webRoute := r.Group("/web", middleware.AuthorizeUser, singleSession.SingleUserSession())
	webRoute.POST("/gen-missing-affiliate", handler.genMissingUserAffilate)

	adminWithdrawSettingRoute := adminRoute.Group("/users/withdraw-setting", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	adminWithdrawSettingRoute.POST("/create", role.CheckPermission([]string{"member_edit", "member_delete"}), handler.userWithdrawSettingCreate)
	adminWithdrawSettingRoute.PUT("/update/:userId", role.CheckPermission([]string{"member_edit", "member_delete"}), handler.userWithdrawSettingUpdateByUserId)
	adminWithdrawSettingRoute.GET("/config/:userId", role.CheckPermission([]string{"member_edit", "member_delete"}), handler.UserWithdrawSettingGetByUserId)
	adminWithdrawSettingRoute.GET("/get-by-id/:id", role.CheckPermission([]string{"member_edit", "member_delete"}), handler.UserWithdrawSettingGetById)

	reportRoute := r.Group("/summary-report", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	reportRoute.GET("/user-register-channel", handler.getUserChannelSummaryGraph)

	adminAgentCallbackRoute := r.Group("/admin/agent-callback", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	adminAgentCallbackRoute.GET("/play-log/list", handler.getAgentCallbackPlayLogList)
	adminAgentCallbackRoute.GET("/option/game-provider-id", handler.getAgentCallbackPlayLogOptionGameId)
}

// @Summary (encodeRefBy)
// @Description TEST
// @Tags Users - backoffice
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.GetUserLogListRequest true "GetUserLogListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /test/encode-refby [post]
func (h userController) encodeRefBy(c *gin.Context) {

	var req model.EncodeRefByRequest
	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}

	data := h.userService.EncodeData(req.RefBy)
	c.JSON(200, data)
}

// @Summary (decodeRefBy)
// @Description TEST
// @Tags Users - backoffice
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.GetUserLogListRequest true "GetUserLogListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /test/decode-refby [post]
func (h userController) decodeRefBy(c *gin.Context) {

	var req model.DecodeRefByRequest
	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}

	data := h.userService.DecodeData(req.RefBy)
	c.JSON(200, data)
}

// @Summary แสดงลิสประวัติการเข้าสู่ระบบของ User
// @Description Login Logs
// @Tags Users - backoffice
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.GetUserLogListRequest true "GetUserLogListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /log/user-login [get]
func (h userController) getLoginLogs(c *gin.Context) {

	var req model.GetUserLogListRequest
	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}

	list, err := h.userService.GetUserLoginLogs(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, list)
}

// @Summary (GetUserDetail) Get User
// @Description (GetUserDetail) Get User for userList Modal
// @Tags Users
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "User ID"
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /users/detail/{id} [get]
func (h userController) GetUserDetail(c *gin.Context) {

	id := c.Param("id")
	toInt, err := strconv.Atoi(id)
	if err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.userService.GetUserDetail(int64(toInt))
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithData{Message: "Success", Data: data})
}

// @Summary (getUserDespositRankOptions) ข้อมูลตัวเลือกระดับการฝากเงิน
// @Description (getUserDespositRankOptions) ข้อมูลตัวเลือกระดับการฝากเงิน
// @Tags Users
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} []model.SelectOptions
// @Failure 400 {object} handler.ErrorResponse
// @Router /users/options/deposit-rank [get]
func (h userController) getUserDespositRankOptions(c *gin.Context) {

	data, err := h.userService.GetUserDespositRankOptions()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (getUserTurnOverRankOptions) ข้อมูลตัวเลือกระดับการเล่นเทิร์นโอเวอร์
// @Description (getUserTurnOverRankOptions) ข้อมูลตัวเลือกระดับการเล่นเทิร์นโอเวอร์
// @Tags Users
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} []model.SelectOptions
// @Failure 400 {object} handler.ErrorResponse
// @Router /users/options/turnover-rank [get]
func (h userController) getUserTurnOverRankOptions(c *gin.Context) {

	data, err := h.userService.GetUserTurnOverRankOptions()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (getUserList) Get User List
// @Description (getUserList) Get User List
// @Description # UserCategory ประเภทผู้ใช้งาน Member Non-Member Scammer
// @Description | UserCategory | Description |
// @Description | ---------------- | ------------|
// @Description | member | สมาชิก |
// @Description | non-member | ผู้ใช้ทั่วไป |
// @Description | scammer | ผู้ใช้ที่ถูกแบน |
// @Description | ---------------- | ------------|
// @Description
// @Tags Users
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.UserListQuery false "Queries"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /users/list [get]
func (h userController) getUserList(c *gin.Context) {

	query := model.UserListQuery{}
	if err := c.ShouldBindQuery(&query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.userService.GetUserList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary Get User Update Logs
// @Description Get User Update Logs
// @Tags Users
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.UserUpdateQuery false "Queries"
// @Success 200 {object} model.SuccessWithList
// @Failure 400 {object} handler.ErrorResponse
// @Router /users/updatelogs [get]
func (h userController) getUpdateLogs(c *gin.Context) {

	query := model.UserUpdateQuery{}
	if err := c.ShouldBindQuery(&query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.userService.GetUpdateLogs(query)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary (createUser) Create User
// @Description (createUser) Create User
// @Tags Users
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.UserCreateRequest false "Create User"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /users/create [post]
func (h userController) createUser(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	data := model.UserCreateRequest{}
	if err := c.ShouldBindJSON(&data); err != nil {
		HandleError(c, err)
		return
	}
	data.AdminId = adminId

	// [ADMIN_LOG]
	if err := h.userService.LogAdmin("createUser", adminId, data); err != nil {
		log.Println("createUser.ERROR: ", err)
	}

	err := h.userService.CreateUser(data)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Register success"})
}

// @Summary (userRegister) Register User
// @Description (userRegister) Register User
// @Tags Front Users
// @Accept json
// @Produce json
// @Param body body model.UserRegister true "Register User"
// @Success 201 {object} model.UserVerifyOtpResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /users/register [post]
func (h userController) userRegister(c *gin.Context) {

	body := model.UserRegister{}

	var currentIp string
	if body.IpRegistered != "" {
		currentIp = body.IpRegistered
	} else {
		if ip := c.GetHeader("X-Real-IP"); ip != "" {
			currentIp = ip
		} else if ip = c.GetHeader("X-Forwarded-For"); ip != "" {
			currentIp = strings.Split(ip, ",")[0]
		} else {
			currentIp = c.ClientIP()
		}
	}

	body.IpRegistered = currentIp

	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}

	result, err := h.userService.UserRegister(body)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, result)
}

// @Summary (login) หน้าเว็บ ผู้เล่น ล็อกอิน
// @Description (login) หน้าเว็บ ผู้เล่น ล็อกอิน
// @Tags Front Users
// @Accept json
// @Produce json
// @Param body body model.UserLogin true "Login"
// @Success 201 {object} model.SuccessWithToken
// @Failure 400 {object} ErrorResponse
// @Router /users/login [post]
func (h userController) login(c *gin.Context) {

	var body model.UserLogin

	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}

	var currentIp string
	if body.Ip != "" {
		currentIp = body.Ip
	} else {
		if ip := c.GetHeader("X-Real-IP"); ip != "" {
			currentIp = ip
		} else if ip = c.GetHeader("X-Forwarded-For"); ip != "" {
			currentIp = strings.Split(ip, ",")[0]
		} else {
			currentIp = c.ClientIP()
		}
	}

	body.Ip = currentIp
	result, err := h.userService.Login(body)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, result)
}

// @Summary (loginUserLine) ล็อกอินด้วยไลน์
// @Description (loginUserLine) Login with Line
// @Description Reponse model.SuccessWithToken คือ การ Login สำเร็จ และมี Token สำหรับเข้าเกมส์
// @Description Reponse Error Badrequest400 LINE_UUID_NOT_FOUND คือ ไม่พบข้อมูลผู้ใช้งานในระบบ ให้ไปหน้าลงทะเบียน
// @Description Reponse Error อื่นๆ ให้ แจ้ง ERROR แล้วกลับไปหน้า login / หน้าแรก
// @Tags Front Users
// @Accept json
// @Produce json
// @Param body body model.UserLineLoginRequest true "UserLineLoginRequest"
// @Success 201 {object} model.SuccessWithToken
// @Failure 400 {object} ErrorResponse
// @Router /users/line-login [post]
func (h userController) loginUserLine(c *gin.Context) {

	var body model.UserLineLoginRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}

	result, err := h.userService.UserLineLogin(body)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, result)
}

// @Summary (RegisterUserByLine) ลงทะเบียนสมาชิกด้วยไลน์
// @Description (RegisterUserByLine) ลงทะเบียนสมาชิกด้วยไลน์
// @Tags Front Users
// @Accept json
// @Produce json
// @Param body body model.UserLineRegisterRequest true "UserLineRegisterRequest"
// @Success 201 {object} model.SuccessWithToken
// @Failure 400 {object} ErrorResponse
// @Router /users/line-register [post]
func (h userController) registerUserLine(c *gin.Context) {

	var body model.UserLineRegisterRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}

	result, err := h.userService.UserLineRegister(body)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, result)
}

// @Summary แสดงข้อมูลตนเอง
// @Description Get Me
// @Tags Front Users
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.UserMe
// @Failure 400 {object} ErrorResponse
// @Router /users/me [get]
func (h userController) GetMe(c *gin.Context) {

	id := c.MustGet("userId").(float64)
	// ip := helper.GetIPv4(c.ClientIP())
	var currentIp string
	if ip := c.GetHeader("X-Real-IP"); ip != "" {
		currentIp = ip
	} else if ip = c.GetHeader("X-Forwarded-For"); ip != "" {
		currentIp = strings.Split(ip, ",")[0]
	} else {
		currentIp = c.ClientIP()
	}

	data, err := h.userService.GetMe(int64(id), currentIp)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary (getGameToken) Login agent แล้วเข้าเกมส์
// @Description (getGameToken) Login agent แล้วเข้าเกมส์
// @Tags Front Users
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.UserLoginResponse
// @Failure 400 {object} ErrorResponse
// @Router /users/game-token [get]
func (h userController) getGameToken(c *gin.Context) {

	id := c.MustGet("userId").(float64)
	// ip := helper.GetIPv4(c.ClientIP())
	var currentIp string
	if ip := c.GetHeader("X-Real-IP"); ip != "" {
		currentIp = ip
	} else if ip = c.GetHeader("X-Forwarded-For"); ip != "" {
		currentIp = strings.Split(ip, ",")[0]
	} else {
		currentIp = c.ClientIP()
	}
	data, err := h.userService.GetGameToken(int64(id), currentIp)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (GetBankList) แสดงลิสธนาคาร
// @Description (GetBankList) Get Bank List
// @Tags Front Users
// @Accept json
// @Produce json
// @Success 200 {object} model.UserBankListResponse
// @Failure 400 {object} ErrorResponse
// @Router /users/bank/list [get]
func (h userController) bankList(c *gin.Context) {

	data, err := h.userService.GetBankList()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (sendOtpRegister) ส่ง OTP สำหรับลงทะเบียน ไปยังเบอร์โทรศัพท์
// @Description (sendOtpRegister) Send OTP
// @Tags Front Users
// @Accept json
// @Produce json
// @Param body body model.UserSendOtpBody true "Send OTP"
// @Success 201 {object} model.UserOtpResponse
// @Failure 400 {object} ErrorResponse
// @Router /users/send-otp/register [post]
func (h userController) sendOtpRegister(c *gin.Context) {

	var body model.UserSendOtpBody
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}

	result, err := h.userService.SendOtpRegister(body)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, result)
}

// @Summary (getRegisterSetting) ดึงข้อมูลการตั้งค่าการลงทะเบียนผู้ใช้งาน
// @Description (getRegisterSetting)
// @Tags Front Users
// @Accept json
// @Produce json
// @Success 200 {object} model.ConfigurationUserRegisterResponse
// @Failure 400 {object} ErrorResponse
// @Router /users/register-setting [get]
func (h userController) getRegisterSetting(c *gin.Context) {

	setting, err := h.userService.GetRegisterSetting()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, setting)
}

// @Summary (generateUserCaptcha) Generate captcha for user check phone and etc..
// @Description (generateUserCaptcha) Generate user captcha
// @Tags Front Users
// @Accept json
// @Produce json
// @Success 201 {object} model.CapchaResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /users/captcha [get]
func (h userController) generateUserCaptcha(c *gin.Context) {

	setting, err := h.userService.GetWebConfiguration()
	if err != nil {
		HandleError(c, err)
		return
	}

	resp := middleware.GenerateUserCaptcha(int(setting.CheckPhoneCaptchaLen))
	c.JSON(201, resp)
}

// @Summary (checkNewUserPhone) เช็คเบอร์ลงทะเบียนใหม่ ซ้ำหรือไม่
// @Description (checkNewUserPhone)
// @Tags Front Users
// @Accept json
// @Produce json
// @Param body body model.UserRegisterCheckRequest true "body"
// @Success 201 {object} model.UserRegisterCheckResponse
// @Failure 400 {object} ErrorResponse
// @Router /users/check-phone [post]
func (h userController) checkNewUserPhone(c *gin.Context) {

	var body model.UserRegisterCheckRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}

	setting, err := h.userService.GetWebConfiguration()
	if err != nil {
		HandleError(c, err)
		return
	}

	if setting.CheckPhoneCaptchaLen > 0 {
		var capchaReq model.CapchaVerifyRequest
		capchaReq.Id = body.CaptchaId
		capchaReq.VerifyValue = body.CaptchaValue
		resp, err := middleware.VerifyCaptcha(capchaReq)
		if err != nil || !resp.IsValid {
			HandleError(c, errors.New("INVALID_CAPTCHA"))
			return
		}
	}

	result, err := h.userService.CheckNewUserPhone(body.Phone)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, result)
}

// @Summary (verifyOtpRegister) เช็ค OTP สำหรับลงทะเบียน ผ่านแล้วจะไปกรอกข้อมูลต่อ
// @Description Verify OTP
// @Tags Front Users
// @Accept json
// @Produce json
// @Param body body model.UserVerifyOtpBody true "Verify OTP"
// @Success 201 {object} model.Success
// @Failure 400 {object} ErrorResponse
// @Router /users/verify-otp/register [post]
func (h userController) verifyOtpRegister(c *gin.Context) {

	var body model.UserVerifyOtpBody
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.userService.VerifyOtpRegister(body); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "Verify success"})
}

// @Summary (sendOtpForget) ส่ง OTP สำหรับลืมรหัสผ่าน ไปยังเบอร์โทรศัพท์
// @Description (sendOtpForget) Send OTP
// @Tags Front Users
// @Accept json
// @Produce json
// @Param body body model.UserSendOtpBody true "Send OTP"
// @Success 201 {object} model.UserOtpResponse
// @Failure 400 {object} ErrorResponse
// @Router /users/send-otp/forget [post]
func (h userController) sendOtpForget(c *gin.Context) {

	var body model.UserSendOtpBody
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}

	// RACE_CONDITION_BLOCKER
	if _, err := h.userService.RacingSendOtpForget(); err != nil {
		HandleError(c, errors.New("ระบบมีผู้ใช้งาน กรุณาทำรายการใหม่หายหลัง"))
		return
	}

	result, err := h.userService.SendOtpForget(body)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, result)
}

// @Summary reset password สำหรับลืมรหัสผ่าน
// @Description Reset Password
// @Tags Front Users
// @Accept json
// @Produce json
// @Param userId path string true "User ID"
// @Param body body model.UserUpdatePasswordForFront true "Reset Password"
// @Success 201 {object} model.Success
// @Failure 400 {object} ErrorResponse
// @Router /users/resetpassword/{userId} [post]
func (h userController) frontResetPassword(c *gin.Context) {

	id := c.Param("userId")
	toInt, err := strconv.Atoi(id)
	if err != nil {
		HandleError(c, err)
		return
	}

	ip := c.ClientIP()
	ip4 := helper.GetIPv4(ip)

	var body model.UserUpdatePasswordForFront
	body.Ip = ip4

	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.userService.FrontResetPassword(int64(toInt), body); err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(201, model.Success{Message: "Change password success"})
}

// @Summary (updateUser) Update User
// @Description (updateUser) Update User
// @Tags Users
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "User ID"
// @Param body body model.UpdateUserRequest true "Update User"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /users/update/{id} [put]
func (h userController) updateUser(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))
	adminName := c.MustGet("username").(string)

	// ip := helper.GetIPv4(c.ClientIP())

	data := model.UpdateUserRequest{}
	if err := c.ShouldBindJSON(&data); err != nil {
		HandleError(c, err)
		return
	}

	var currentIp string
	if data.IpAddress != "" {
		currentIp = data.IpAddress
	} else {
		if ip := c.GetHeader("X-Real-IP"); ip != "" {
			currentIp = ip
		} else if ip = c.GetHeader("X-Forwarded-For"); ip != "" {
			currentIp = strings.Split(ip, ",")[0]
		} else {
			currentIp = c.ClientIP()
		}
	}

	id := c.Param("id")
	toInt, err := strconv.Atoi(id)
	if err != nil {
		HandleError(c, err)
		return
	}
	data.Ip = currentIp

	// [ADMIN_LOG]
	if err := h.userService.LogAdmin("updateUser", adminId, data); err != nil {
		log.Println("updateUser.LogAdmin.ERROR: ", err)
	}

	err = h.userService.UpdateUser(int64(toInt), data, adminName, adminId)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Updated success"})
}

// @Summary (resetPassword) Update User Password
// @Description (resetPassword) Update User Password
// @Tags Users
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "User ID"
// @Param body body model.UserUpdatePassword true "Update User Password"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /users/password/{id} [put]
func (h userController) resetPassword(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	id := c.Param("id")
	toInt, err := strconv.Atoi(id)
	if err != nil {
		HandleError(c, err)
		return
	}

	ip := c.ClientIP()
	ip4 := helper.GetIPv4(ip)
	body := model.UserUpdatePassword{}
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	body.Ip = ip4

	// [ADMIN_LOG]
	if err := h.userService.LogAdmin("resetUserPassword", adminId, body); err != nil {
		log.Println("resetUserPassword.ERROR: ", err)
	}

	if err := h.userService.ResetPassword(int64(toInt), body, adminId); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Reset password success"})
}

// @Summary Change User Password
// @Description Change User Password
// @Tags Front Users
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.UserUpdatePassword true "Update User Password"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /users/changepassword [put]
func (h userController) frontChangePassword(c *gin.Context) {

	id := c.MustGet("userId").(float64)
	toInt := int64(id)

	ip := c.ClientIP()
	ip4 := helper.GetIPv4(ip)

	body := model.UserUpdatePassword{}

	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}

	body.UserId = toInt
	body.Ip = ip4

	if err := h.userService.ResetPassword(toInt, body, 0); err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(201, model.Success{Message: "Reset password success"})
}

// @Summary (deleteUser) Delete User
// @Description (deleteUser) Delete User
// @Tags Users
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "User ID"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /users/{id} [delete]
func (h userController) deleteUser(c *gin.Context) {

	id := c.Param("id")
	toInt, err := strconv.Atoi(id)
	if err != nil {
		HandleError(c, err)
		return
	}

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))
	// [ADMIN_LOG]
	if err := h.userService.LogAdmin("deleteUser", adminId, id); err != nil {
		log.Println("deleteUser.ERROR: ", err)
	}

	err = h.userService.DeleteUser(int64(toInt), adminId)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Deleted success"})
}

// @Summary (exportUserListXlsx) ส่งออกไฟล์ Excel รายชื่อสมาชิก Member Non-Member Scammer
// @Description (exportUserListXlsx) Export User List
// @Description # การกรองด้วยประเภทผู้ใช้งาน Member Non-Member Scammer
// @Description | UserCategory | Description |
// @Description | ---------------- | ------------|
// @Description | member | สมาชิก |
// @Description | non-member | ผู้ใช้ทั่วไป |
// @Description | scammer | ผู้ใช้ที่ถูกแบน |
// @Description | ---------------- | ------------|
// @Tags Users
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.UserListQueryForExcel true "Queries"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /users/export-xlsx [get]
func (h userController) exportUserListXlsx(c *gin.Context) {

	var query model.UserListQueryForExcel
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.userService.UserListQueryForExcel(c, query); err != nil {
		HandleError(c, err)
		return
	}
}

// GetMemberRefreshCredit(id int64) (*model.UserRefreshCreditResponse, error)
// @Summary แสดงข้อมูล User สำหรับ Refresh Credit
// @Description Get User Refresh Credit
// @Tags Users
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 200 {object} model.UserRefreshCreditResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /users/refresh-credit/{id} [get]
func (h userController) GetMemberRefreshCredit(c *gin.Context) {

	var reqId model.UserRefreshCreditRequest
	if err := c.ShouldBindUri(&reqId); err != nil {
		HandleError(c, err)
		return
	}
	data, err := h.userService.GetMemberRefreshCredit(reqId.Id)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary (getInactiveUserList) สมาชิกที่ไม่มีการเคลื่อนไหว - รายชื่อ
// @Description (getInactiveUserList) รายชื่อสมาชิกที่ไม่มีการเคลื่อนไหว
// @Description
// @Description # การกรองด้วยประเภทวัน 5 วัน 10 วัน 15 วัน 1 เดือน 2 เดือน 3 เดือน 4 เดือน 5 เดือน
// @Description | dateType | Description |
// @Description | ---------------- | ------------|
// @Description | 5_days | 5 วัน |
// @Description | 10_days | 10 วัน |
// @Description | 15_days | 15 วัน |
// @Description | 1_month | 1 เดือน |
// @Description | 2_month | 2 เดือน |
// @Description | 3_month | 3 เดือน |
// @Description | 4_month | 4 เดือน |
// @Description | 5_month | 5 เดือนหรือมากกว่า |
// @Description | ---------------- | ------------|
// @Description
// @Tags Inactive Users
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.InactiveUserListRequest true "InactiveUserListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /inactive-users/list [get]
func (h userController) getInactiveUserList(c *gin.Context) {

	var query model.InactiveUserListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	list, total, err := h.userService.GetInactiveUserList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithPagination{List: list, Total: total})
}

// @Summary (removeInactiveUser) สมาชิกที่ไม่มีการเคลื่อนไหว - ลบรายชื่อที่เลือก
// @Description (removeInactiveUser) ลบรายชื่อสมาชิกที่ไม่มีการเคลื่อนไหว
// @Description # ส่งไอดีที่เลือกมาเป็น Array of Int64 พร้อม dateType ประเภทวัน 5_days 10_days 15_days 1_month 2_month 3_month 4_month 5_month
// @Tags Inactive Users
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.InactiveUserRemoveListRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /inactive-users/remove-selected [post]
func (h userController) removeInactiveUser(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	body := model.InactiveUserRemoveListRequest{}
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}
	body.AdminId = adminId

	// [ADMIN_LOG]
	if err := h.userService.LogAdmin("removeInactiveUser", adminId, body); err != nil {
		log.Println("removeInactiveUser.ERROR: ", err)
	}

	if err := h.userService.RemoveInactiveUser(body); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Updated success"})
}

// @Summary (exportInactiveUserXlsx) สมาชิกที่ไม่มีการเคลื่อนไหว - ส่งออกไฟล์ Excel
// @Description (exportInactiveUserXlsx) ส่งออกไฟล์ Excel รายชื่อสมาชิกที่ไม่มีการเคลื่อนไหว
// @Tags Inactive Users
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.InactiveUserListForExcelRequest true "InactiveUserListForExcelRequest"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /inactive-users/export-xlsx [get]
func (h userController) exportInactiveUserXlsx(c *gin.Context) {

	var query model.InactiveUserListForExcelRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.userService.ExportInactiveUserXlsx(c, query); err != nil {
		HandleError(c, err)
		return
	}

}

// @Summary (GenMemberCode) สร้างรหัสสมาชิกใหม่ ถ้ายังไม่มี
// @Description (GenMemberCode) Gen Member Code
// @Tags Admin v2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 200 {object} string
// @Failure 400 {object} ErrorResponse
// @Router /admin/gen-membercode/{id} [get]
func (h userController) GenMemberCode(c *gin.Context) {

	var req model.CreateUserMemberCode
	if err := c.ShouldBindUri(&req); err != nil {
		HandleError(c, err)
		return
	}

	code, err := h.userService.GenMemberCodeAndAffilate(req.Id)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, code)
}

// @Summary (genMissingUserAffilate) ตรวจสอบและสร้าง Affiliate ให้กับ user ที่มีรายการฝาก แต่ยังไม่ได้ Affiliate
// @Description (genMissingUserAffilate)
// @Tags Web - Users
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.CheckMissingAffiliateReponse
// @Failure 400 {object} ErrorResponse
// @Router /web/gen-missing-affiliate [post]
func (h userController) genMissingUserAffilate(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	data, err := h.userService.GenMissingUserAffilate(userId)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (userWithdrawSettingCreate) สร้างการตั้งค่าการถอนเงินสำหรับผู้ใช้งาน
// @Description (userWithdrawSettingCreate) Create User Withdraw Setting
// @Tags Admin - Users Setting
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.UserWithdrawSettingCreateRequest true "UserWithdrawSettingCreateRequest"
// @Success 201 {object} model.Success
// @Failure 400 {object} ErrorResponse
// @Router /admin/users/withdraw-setting/create [post]
func (h userController) userWithdrawSettingCreate(c *gin.Context) {

	var req model.UserWithdrawSettingCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(req); err != nil {
		HandleError(c, err)
		return
	}

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))
	// [ADMIN_LOG]
	if err := h.userService.LogAdmin("userWithdrawSettingCreate", adminId, req); err != nil {
		log.Println("userWithdrawSettingCreate.ERROR: ", err)
	}

	createdId, err := h.userService.UserWithdrawSettingCreate(req, adminId)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, createdId)
}

// @Summary (userWithdrawSettingUpdateByUserId) แก้ไขการตั้งค่าการถอนเงินสำหรับผู้ใช้งาน
// @Description (userWithdrawSettingUpdateByUserId) Update User Withdraw Setting
// @Tags Admin - Users Setting
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param userId path int64 true "userId"
// @Param body body model.UserWithdrawSettingUpdateByUserIdRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} ErrorResponse
// @Router /admin/users/withdraw-setting/update/{userId} [put]
func (h userController) userWithdrawSettingUpdateByUserId(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	var req model.UserWithdrawSettingUpdateByUserIdRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}
	var param model.UserWithdrawSettingGetByUserIdRequest
	if err := c.ShouldBindUri(&param); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(req); err != nil {
		HandleError(c, err)
		return
	}
	req.UpdatedByAdminId = &adminId
	req.UserId = param.UserId

	// [ADMIN_LOG]
	if err := h.userService.LogAdmin("userWithdrawSettingUpdateByUserId", adminId, req); err != nil {
		log.Println("userWithdrawSettingUpdateByUserId.ERROR: ", err)
	}

	if err := h.userService.UserWithdrawSettingUpdateByUserId(req, adminId); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Updated success"})
}

// @Summary (UserWithdrawSettingGetByUserId) ดึงข้อมูลการตั้งค่าการถอนเงินสำหรับผู้ใช้งาน
// @Description (UserWithdrawSettingGetByUserId) Get User Withdraw Setting
// @Tags Admin - Users Setting
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param userId path int64 true "userId"
// @Success 200 {object} model.UserWithdrawSettingGetByUserIdResponse
// @Failure 400 {object} ErrorResponse
// @Router /admin/users/withdraw-setting/config/{userId} [get]
func (h userController) UserWithdrawSettingGetByUserId(c *gin.Context) {

	var req model.UserWithdrawSettingGetByUserIdRequest
	if err := c.ShouldBindUri(&req); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.userService.UserWithdrawSettingGetByUserId(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary (UserWithdrawSettingGetById) ดึงข้อมูลการตั้งค่าการถอนเงินสำหรับผู้ใช้งาน
// @Description (UserWithdrawSettingGetById) Get User Withdraw Setting
// @Tags Admin - Users Setting
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int64 true "id"
// @Success 200 {object} model.UserWithdrawSettingGetByUserIdResponse
// @Failure 400 {object} ErrorResponse
// @Router /admin/users/withdraw-setting/get-by-id/{id} [get]
func (h userController) UserWithdrawSettingGetById(c *gin.Context) {

	var req model.UserWithdrawSettingGetByRequest
	if err := c.ShouldBindUri(&req); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.userService.UserWithdrawSettingGetById(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary (getUserChannelSummaryGraph) สรุปภาพรวม - สรุปช่องทางสมัครสมาชิกใหม่
// @Description (getUserChannelSummaryGraph) สรุปภาพรวม - สรุปช่องทางสมัครสมาชิกใหม่
// @Description
// @Description # การกรองด้วยประเภทวัน dateType ทั้งหมด วันนี้ เมื่อวาน เดือนนี้ **ถ้าส่ง fromDate, toDate จะใช้ fromDate, toDate ก่อน
// @Description | dateType | Description |
// @Description | ---------------- | ------------|
// @Description | all *หรือไม่ส่ง* | แสดงทั้งหมด |
// @Description | today | แสดงวันนี้ |
// @Description | yesterday | แสดงเมื่อวาน |
// @Description | last_week | 7 วันย้อนหลัง |
// @Description | this_month | แสดงเดือนนี้ 1-31 |
// @Description | last_month | แสดง 30 วันก่อนหน้า |
// @Description | ---------------- | ------------|
// @Description
// @Description # กรองข้อมูลตามช่วงวันที่ ส่ง fromDate, toDate ในรูปแบบ YYYY-MM-DD (2021-12-31)
// @Description * ถ้าเลือกทั้งหมดไม่ต้องส่งเลย
// @Tags Report - Summary Report
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.UserChannelSummaryGraphRequest true "query"
// @Success 200 {object} model.UserChannelSummaryGraphResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /summary-report/user-register-channel [get]
func (h userController) getUserChannelSummaryGraph(c *gin.Context) {

	var query model.UserChannelSummaryGraphRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.userService.GetUserChannelSummaryGraph(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary รายการการเล่นเกมส์ของตัวแทน
// @Description รายการการเล่นเกมส์ของตัวแทน
// @Tags Agent - Play Log
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.AgentCallbackPlayLogListRequest true "agentCallbackPlayLogListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /admin/agent-callback/play-log/list [get]
func (h userController) getAgentCallbackPlayLogList(c *gin.Context) {

	var query model.AgentCallbackPlayLogListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}

	list, err := h.userService.GetAgentCallbackPlayLogList(query)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, list)
}

// @Summary Get Game Agent Play Log Game Id
// @Description Get Game Agent Play Log Game Id
// @Tags Configuration
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} []model.SelectOptions
// @Failure 400 {object} handler.ErrorResponse
// AgentGameProviderId
// @Router /admin/agent-callback/option/game-provider-id [get]
func (h *userController) getAgentCallbackPlayLogOptionGameId(c *gin.Context) {

	data := []model.SelectOptions{
		// {Id: 1, Value: "IMI", Label: "IMI"},
		{Id: 2, Value: "PGHARD", Label: "PGHARD"},
		{Id: 3, Value: "CTW", Label: "CTW"},
	}
	c.JSON(200, data)
}
