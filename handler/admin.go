package handler

import (
	"cybergame-api/helper"
	"cybergame-api/middleware"
	"cybergame-api/model"
	"cybergame-api/service"
	"log"
	"strconv"

	"cybergame-api/repository"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"gorm.io/gorm"
)

type adminController struct {
	adminService service.AdminService
}

func newAdminController(
	adminService service.AdminService,
) adminController {
	return adminController{adminService}
}

func AdminController(r *gin.RouterGroup, db *gorm.DB) {

	actionService := service.NewAdminActionService(repository.NewAdminActionRepository(db))

	repo := repository.NewAdminRepository(db)
	perRepo := repository.NewPermissionRepository(db)
	groupRepo := repository.NewGroupRepository(db)
	service := service.NewAdminService(repo, perRepo, groupRepo, actionService)
	handler := newAdminController(service)

	role := middleware.Role(db)
	singleSession := middleware.SingleSession(db)

	admin := r.Group("/admins", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	// r.GET("/detail/:id", middleware.Authorize, role.CheckAdmin("admin"), handler.GetAdmin)
	admin.GET("/me", handler.getMe)
	admin.GET("/option-list", handler.GetAdminOptionList)
	admin.GET("/detail/:id", role.CheckPermission([]string{"admin_manage"}), handler.getAdmin)
	admin.GET("/list", role.CheckPermission([]string{"admin_manage"}), handler.getAdminList)
	admin.POST("/create", role.CheckPermission([]string{"admin_manage_create"}), handler.createAdmin)
	admin.PUT("/update/:id", role.CheckPermission([]string{"admin_manage_edit"}), handler.updateAdmin)
	admin.PUT("/password/:id", role.CheckPermission([]string{"admin_manage_edit"}), handler.resetPassword)
	admin.DELETE("/:id", role.CheckPermission([]string{"admin_manage_delete"}), handler.deleteAdmin)

	admin.GET("/group", role.CheckPermission([]string{"admin_manage_group"}), handler.groupList)
	admin.GET("/group/:id", role.CheckPermission([]string{"admin_manage_group"}), handler.getGroup)
	// r.POST("/group,  handler.createGroup)
	admin.PUT("/group/:id", role.CheckPermission([]string{"admin_manage_group"}), handler.updateGroup)
	admin.DELETE("/group/:id", role.CheckPermission([]string{"admin_manage_group"}), handler.deleteGroup)
	admin.DELETE("/permission", role.CheckPermission([]string{"admin_manage_delete"}), handler.deletePermission)
	admin.GET("/login-list", role.CheckPermission([]string{"admin_manage_login_history"}), handler.getAdminLoginLogList)

	admin.POST("/totp-reset", role.CheckPermission([]string{"admin_manage_reset_2fa"}), handler.TotpReset)
	admin.POST("/refresh-token", handler.AdminRefreshToken)

	getOtpSetting := r.Group("/admins")
	getOtpSetting.POST("/totp-secret", handler.TotpGererateSecret)
	getOtpSetting.POST("/totp-verify", handler.TotpVerifyCheck)
	getOtpSetting.GET("/detail-totp", handler.GetAdminDetailTotp)

}

// @Summary Group List
// @Description Group List
// @Tags Admins
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.AdminGroupQuery false "Queries"
// @Success 200 {object} model.SuccessWithList
// @Failure 400 {object} handler.ErrorResponse
// @Router /admins/group [get]
func (h adminController) groupList(c *gin.Context) {

	query := model.AdminGroupQuery{}
	if err := c.ShouldBindQuery(&query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.adminService.GetGroupList(query)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary Get Group
// @Description Get Group
// @Tags Admins
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "Group ID"
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /admins/group/{id} [get]
func (h adminController) getGroup(c *gin.Context) {

	id := c.Param("id")
	toInt, err := strconv.Atoi(id)
	if err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.adminService.GetGroup(toInt)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, model.SuccessWithData{Message: "Success", Data: data})
}

// @Summary Get Admin
// @Description Get Admin
// @Tags Admins
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "Admin ID"
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /admins/detail/{id} [get]
func (h adminController) getAdmin(c *gin.Context) {

	id := c.Param("id")
	toInt, err := strconv.Atoi(id)
	if err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.adminService.GetAdmin(int64(toInt))
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, model.SuccessWithData{Message: "Success", Data: data})
}

// @Summary Get admin profile (me)
// @Tags Admins
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /admins/me [get]
func (h adminController) getMe(c *gin.Context) {

	id := c.MustGet("adminId").(float64)

	data, err := h.adminService.GetAdmin(int64(id))
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, model.SuccessWithData{Message: "Success", Data: data})
}

// @Summary Get Admin List
// @Description Get Admin List
// @Tags Admins
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.AdminListQuery false "Queries"
// @Success 200 {object} model.SuccessWithList
// @Failure 400 {object} handler.ErrorResponse
// @Router /admins/list [get]
func (h adminController) getAdminList(c *gin.Context) {

	query := model.AdminListQuery{}
	if err := c.ShouldBindQuery(&query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.adminService.GetAdminList(query)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary (createAdmin) Create Admin
// @Description (createAdmin) Create Admin
// @Tags Admins
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param Body body model.CreateAdmin true "Create Admin"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /admins/create [post]
func (h adminController) createAdmin(c *gin.Context) {

	adminId := int64(c.MustGet("adminId").(float64))

	data := model.CreateAdmin{}
	if err := c.ShouldBindJSON(&data); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(data); err != nil {
		HandleSingleError(c, err)
		return
	}
	data.CreateBy = adminId

	err := h.adminService.CreateAdmin(data)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Register success"})
}

// @Summary Update Group
// @Description Update Group
// @Tags Admins
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "Group ID"
// @Param Body body model.AdminUpdateGroup true "Update Group"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /admins/group/{id} [put]
func (h adminController) updateGroup(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	id := c.Param("id")
	toInt, err := strconv.Atoi(id)
	if err != nil {
		HandleError(c, err)
		return
	}

	var req model.AdminUpdateGroup
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(req); err != nil {
		HandleError(c, err)
		return
	}
	req.GroupId = int64(toInt)
	req.UpdateBy = adminId

	err = h.adminService.UpdateGroup(req)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Updated success"})
}

// @Summary (updateAdmin) Update Admin
// @Description (updateAdmin) Update Admin
// @Tags Admins
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "Admin ID"
// @Param Body body model.AdminUpdateRequest true "Update Admin"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /admins/update/{id} [put]
func (h adminController) updateAdmin(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	data := model.AdminUpdateRequest{}
	if err := c.ShouldBindJSON(&data); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(data); err != nil {
		HandleSingleError(c, err)
		return
	}
	data.UpdateBy = adminId

	id := c.Param("id")
	toInt, err := strconv.Atoi(id)
	if err != nil {
		HandleError(c, err)
		return
	}

	err, perErrs := h.adminService.UpdateAdmin(int64(toInt), data)
	if err != nil {
		HandleError(c, err)
		return
	}
	if perErrs != nil {
		HandleError(c, perErrs)
		return
	}
	c.JSON(201, model.Success{Message: "Updated success"})
}

// @Summary Update Admin Password
// @Description Update Admin Password
// @Tags Admins
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "Admin ID"
// @Param Body body model.AdminUpdatePassword true "Update Admin Password"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /admins/password/{id} [put]
func (h adminController) resetPassword(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	id := c.Param("id")
	toInt, err := strconv.Atoi(id)
	if err != nil {
		HandleError(c, err)
		return
	}

	data := model.AdminUpdatePassword{}
	if err := c.ShouldBindJSON(&data); err != nil {
		HandleError(c, err)
		return
	}
	data.UpdateBy = adminId

	if err := validator.New().Struct(data); err != nil {
		HandleError(c, err)
		return
	}
	if err := h.adminService.ResetPassword(int64(toInt), data); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Reset password success"})
}

// @Summary Delete Group
// @Description Delete Group
// @Tags Admins
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "Group ID"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /admins/group/{id} [delete]
func (h adminController) deleteGroup(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	id := c.Param("id")
	toInt, err := strconv.Atoi(id)
	if err != nil {
		HandleError(c, err)
		return
	}

	var param model.DeleteGroup
	param.Id = int64(toInt)
	param.DeleteBy = adminId
	if err := h.adminService.DeleteGroup(param); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Deleted success"})
}

// @Summary Delete Permission
// @Description Delete Permission
// @Tags Admins
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param delete body model.DeletePermission true "Delete Permission"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /admins/permission [delete]
func (h adminController) deletePermission(c *gin.Context) {

	var body model.DeletePermission
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.adminService.DeletePermission(body); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Deleted success"})
}

// @Summary Delete Admin
// @Description Delete Admin
// @Tags Admins
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "Admin ID"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /admins/{id} [delete]
func (h adminController) deleteAdmin(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	id := c.Param("id")
	toInt, err := strconv.Atoi(id)
	if err != nil {
		HandleError(c, err)
		return
	}
	req := model.AdminDeleteRequest{
		AdminId:  int64(toInt),
		UpdateBy: adminId,
	}

	if err := h.adminService.DeleteAdmin(req); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Deleted success"})
}

// @Summary (getAdminLoginLogList) ประวัติการเข้าสู่ระบบผู้ใช้งาน
// @Description (getAdminLoginLogList) Get Admin Login List
// @Tags Admins
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.AdminLoginLogListRequest false "query"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /admins/login-list [get]
func (h adminController) getAdminLoginLogList(c *gin.Context) {

	query := model.AdminLoginLogListRequest{}
	if err := c.ShouldBindQuery(&query); err != nil {
		HandleError(c, err)
		return
	}

	list, total, err := h.adminService.GetAdminLoginLogList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithPagination{Message: "Success", Total: total, List: list})
}

// @Summary (generateTotpSecret) Generate TOTP Secret
// @Description (generateTotpSecret) Generate TOTP Secret
// @Tags Admins
// @Accept json
// @Produce json
// @Param Body body model.TotpGererateSecret true "TotpGererateSecret"
// @Success 201 {object} model.TotpGererateSecretResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /admins/totp-secret [post]
func (h adminController) TotpGererateSecret(c *gin.Context) {

	var req model.TotpGererateSecret
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.adminService.TotpGererateSecret(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(201, data)
}

// @Summary (verifyTotp) Verify TOTP
// @Description (verifyTotp) Verify TOTP
// @Tags Admins
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param Body body model.TotpVerifyCheck true "Verify TOTP"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /admins/totp-verify [post]
func (h adminController) TotpVerifyCheck(c *gin.Context) {

	var req model.TotpVerifyCheck
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	response, err := h.adminService.TotpVerifyCheck(req)
	if err != nil {
		var adminActionCreateBody model.AdminActionCreateRequest
		adminActionCreateBody.AdminId = req.AdminId
		adminActionCreateBody.TypeId = model.ADMIN_ACTION_LOGIN_FAILED
		adminActionCreateBody.RefObjectId = 0
		adminActionCreateBody.Detail = "รหัส Totp ไม่ถูกต้อง"
		adminActionCreateBody.JsonInput = helper.StructJson(req)
		adminActionCreateBody.JsonOutput = helper.StructJson(struct {
			errors interface{}
		}{
			errors: err.Error(),
		})
		if _, err := h.adminService.CreateFailedAdminAction(adminActionCreateBody); err != nil {
			log.Println("loginAdmin.CreateFailedAdminAction", err)
		}
		HandleError(c, err)
		return
	}

	c.JSON(201, response)
}

// @Summary (resetTotp) Reset TOTP
// @Description (resetTotp) Reset TOTP
// @Tags Admins
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param Body body model.TotpResetRequest true "Reset TOTP"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /admins/totp-reset [post]
func (h adminController) TotpReset(c *gin.Context) {

	var req model.TotpResetRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}
	adminId, err := h.adminService.CheckCurrentAdminId(c.MustGet("adminId"))
	if err != nil {
		HandleError(c, err)
		return
	}
	req.UpdatedById = *adminId

	if err := h.adminService.TotpReset(req); err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(201, model.Success{Message: "Reset success"})
}

// @Summary (getAdminDetailTotp) Get Admin Detail TOTP
// @Description (getAdminDetailTotp) Get Admin Detail TOTP
// @Tags Admins
// @Accept json
// @Produce json
// @Success 200 {object} model.WebSettingTotpResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /admins/detail-totp [get]
func (h adminController) GetAdminDetailTotp(c *gin.Context) {

	data, err := h.adminService.GetAdminDetailTotp()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, model.WebSettingTotpResponse{WebSettingTotp: data.WebSettingTotp})
}

// @Summary (getAdminDetailTotp) Get Admin Detail TOTP
// @Description (getAdminDetailTotp) Get Admin Detail TOTP
// @Tags Admins
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.AdminRefreshTokenRequest true "Admin Refresh Token"
// @Success 200 {object} model.AdminRefreshToken
// @Failure 400 {object} handler.ErrorResponse
// @Router /admins/refresh-token [post]
func (h adminController) AdminRefreshToken(c *gin.Context) {

	var req model.AdminRefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))
	req.AdminId = adminId

	data, err := h.adminService.AdminRefreshToken(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary Get Admin Option List
// @Description Get Admin Option List
// @Tags Admins
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.SuccessWithList
// @Failure 400 {object} handler.ErrorResponse
// @Router /admins/option-list [get]
func (h adminController) GetAdminOptionList(c *gin.Context) {

	data, err := h.adminService.GetAdminOptionList()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithData{Message: "Success", Data: data})
}
