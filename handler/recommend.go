package handler

import (
	"cybergame-api/helper"
	"cybergame-api/middleware"
	"cybergame-api/model"
	"cybergame-api/service"
	"log"
	"strconv"

	"cybergame-api/repository"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"gorm.io/gorm"
)

type recommendController struct {
	recommendService service.RecommendService
}

func newRecommendController(
	recommendService service.RecommendService,
) recommendController {
	return recommendController{recommendService}
}

func RecommendController(r *gin.RouterGroup, db *gorm.DB) {

	repo := repository.NewRecommendRepository(db)
	service := service.NewRecommendService(repo)
	handler := newRecommendController(service)

	role := middleware.Role(db)
	singleSession := middleware.SingleSession(db)

	a := r.Group("/recommends", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	a.GET("/list", role.CheckPermission([]string{"member", "member_edit", "member_delete"}), handler.getRecommendList)
	a.POST("/create", role.CheckPermission([]string{"member_edit"}), handler.createRecommend)
	a.PUT("/update/:id", role.CheckPermission([]string{"member_edit"}), handler.updateRecommend)
	a.DELETE("/:id", role.CheckPermission([]string{"member_delete"}), handler.deleteRecommend)

	f := r.Group("/recommends")
	f.GET("/channel", handler.getListForFront)

}

// @Summary (getRecommendList) Get Recommend List
// @Description (getRecommendList) Get Recommend List
// @Tags Recommends
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.RecommendQuery true "Query Recommend"
// @Success 200 {object} model.SuccessWithList
// @Failure 400 {object} handler.ErrorResponse
// @Router /recommends/list [get]
func (h recommendController) getRecommendList(c *gin.Context) {

	var query model.RecommendQuery
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}

	list, total, err := h.recommendService.GetRecommendList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithPagination{Message: "Success", List: list, Total: total})
}

// @Summary (getListForFront) Get Recommend List For Front
// @Description (getListForFront) Get Recommend List For Front
// @Tags Recommends
// @Accept json
// @Produce json
// @Success 200 {object} model.RecommendListResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /recommends/channel [get]
func (h recommendController) getListForFront(c *gin.Context) {

	list, err := h.recommendService.GetListForFront()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, list)
}

// @Summary (createRecommend) Create Recommend
// @Description (createRecommend) Create Recommend
// @Tags Recommends
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param Body body model.CreateRecommend true "Create Recommend"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /recommends/create [post]
func (h recommendController) createRecommend(c *gin.Context) {

	var body model.CreateRecommend
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}

	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))
	// [ADMIN_LOG]
	if err := h.recommendService.LogAdmin("createRecommend", adminId, body); err != nil {
		log.Println("createRecommend.ERROR: ", err)
	}

	if err := h.recommendService.CreateRecommend(body); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Created success"})
}

// @Summary (updateRecommend) Update Recommend
// @Description (updateRecommend) Update Recommend
// @Tags Recommends
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "Recommend ID"
// @Param Body body model.RecommendUpdateRequest true "Update Recommend"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /recommends/update/{id} [put]
func (h recommendController) updateRecommend(c *gin.Context) {

	var body model.RecommendUpdateRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	id := c.Param("id")
	toInt, err := strconv.Atoi(id)
	if err != nil {
		HandleError(c, err)
		return
	}

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))
	// [ADMIN_LOG]
	logBody := map[string]interface{}{
		"id":   id,
		"body": body,
	}
	if err := h.recommendService.LogAdmin("updateRecommend", adminId, logBody); err != nil {
		log.Println("updateRecommend.ERROR: ", err)
	}

	if err := h.recommendService.UpdateRecommend(int64(toInt), body); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Updated success"})
}

// @Summary (deleteRecommend) Delete Recommend
// @Description (deleteRecommend) Delete Recommend
// @Tags Recommends
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "Recommend ID"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /recommends/{id} [delete]
func (h recommendController) deleteRecommend(c *gin.Context) {

	id := c.Param("id")
	toInt, err := strconv.Atoi(id)
	if err != nil {
		HandleError(c, err)
		return
	}

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))
	// [ADMIN_LOG]
	if err := h.recommendService.LogAdmin("deleteRecommend", adminId, id); err != nil {
		log.Println("deleteRecommend.ERROR: ", err)
	}

	if err := h.recommendService.DeleteRecommend(int64(toInt)); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Deleted success"})
}
