package handler

import (
	"cybergame-api/repository"
	"cybergame-api/service"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type telegramController struct {
	telegramService service.TelegramService
}

func newTelegramController(
	telegramService service.TelegramService,
) telegramController {
	return telegramController{telegramService}
}

func TelegramController(r *gin.RouterGroup, db *gorm.DB) {

	repo := repository.NewTelegramRepository(db)
	service := service.NewTelegramService(repo)
	handler := newTelegramController(service)

	// managePopupRoute := r.Group("/web-popup", middleware.AuthorizeAdmin)
	// managePopupRoute.GET("/list", handler.getTelegramList)
	// managePopupRoute.GET("/detail/:id", handler.getTelegramById)
	// managePopupRoute.POST("/create", handler.createTelegram)
	// managePopupRoute.PATCH("/:id", handler.updateTelegram)
	// managePopupRoute.DELETE("/:id", handler.deleteTelegram)

	webRoute := r.Group("/cronjob")
	webRoute.GET("/bot-trigger/message", handler.getTelegramUpdate)
}

// @Summary (getTelegramUpdate)
// @Description (getTelegramUpdate)
// @Tags Telegrams - Telegram
// @Accept json
// @Produce json
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /cronjob/bot-trigger/message [get]
func (h telegramController) getTelegramUpdate(c *gin.Context) {

	data, err := h.telegramService.GetTelegramUpdate()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}
