package handler

import (
	"cybergame-api/middleware"
	"cybergame-api/model"
	"cybergame-api/repository"
	"cybergame-api/service"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type issueReportController struct {
	issueReportService service.IssueReportService
}

func newIssueReportController(
	issueReportService service.IssueReportService,
) issueReportController {
	return issueReportController{issueReportService}
}

func IssueReportController(r *gin.RouterGroup, db *gorm.DB) {

	singleSession := middleware.SingleSession(db)

	repo := repository.NewIssueReportRepository(db)
	service := service.NewIssueReportService(repo)
	handler := newIssueReportController(service)

	rootIssue := r.Group("/issue-report", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	rootIssue.POST("", handler.createIssueReport)
	rootIssue.GET("/:id", handler.getIssueReportById)
	rootIssue.GET("/list", handler.getIssueReportList)
	rootIssue.PUT("/:id", handler.updateIssueReport)
	rootIssue.DELETE("/:id", handler.deleteIssueReportAndWeb)
	//option
	rootIssue.GET("/status/option", handler.getIssueStatusOptions)
	//web url
	rootWebUrl := rootIssue.Group("/web-url", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	rootWebUrl.POST("", handler.createWebUrl)
	rootWebUrl.GET("/list", handler.getWebUrlList)
	rootWebUrl.GET("/:id", handler.getWebUrlById)
	rootWebUrl.DELETE("/:id", handler.deleteWebUrlById)
}

// @Summary Create Issue Report
// @Description Create Issue Report
// @Tags Issue Report
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.IssueReportCreateRequest true "body"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /issue-report [post]
func (h issueReportController) createIssueReport(c *gin.Context) {

	username, err := h.issueReportService.CurrentAdmin(c.MustGet("username"))
	if err != nil {
		HandleError(c, err)
		return
	}
	var body model.IssueReportCreateRequest
	body.CreatedByName = username

	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}

	createdId, err := h.issueReportService.CreateIssueReport(body)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, createdId)
}

// GetIssueReportById(id int64) (*model.IssueReportBody, error
// @Summary Get Issue Report By Id
// @Description Get Issue Report By Id
// @Tags Issue Report
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /issue-report/{id} [get]
func (h issueReportController) getIssueReportById(c *gin.Context) {

	var getById model.IssueReportGetByIdRequest
	if err := c.ShouldBindUri(&getById); err != nil {
		HandleError(c, err)
		return
	}

	issueReport, err := h.issueReportService.GetIssueReportById(getById.Id)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, issueReport)
}

// @Summary Update Issue Report
// @Description Update Issue Report
// @Tags Issue Report
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Param body body model.IssueReportUpdateRequest true "body"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /issue-report/{id} [put]
func (h issueReportController) updateIssueReport(c *gin.Context) {

	var updatedId model.IssueReportGetByIdRequest
	if err := c.ShouldBindUri(&updatedId); err != nil {
		HandleError(c, err)
		return
	}

	var body model.IssueReportUpdateRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}

	username, err := h.issueReportService.CurrentAdmin(c.MustGet("username"))
	if err != nil {
		HandleError(c, err)
		return
	}
	body.Id = updatedId.Id
	body.ApprovedByName = username

	err = h.issueReportService.UpdateIssueReport(body)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, "success")
}

// @Summary Get Issue Report List
// @Description Get Issue Report List
// @Tags Issue Report
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.IssueReportListRequest true "IssueReportListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /issue-report/list [get]
func (h issueReportController) getIssueReportList(c *gin.Context) {

	var req model.IssueReportListRequest
	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}

	issueReport, err := h.issueReportService.GetIssueReportList(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, issueReport)
}

// @Summary Delete Issue Report And Web
// @Description Delete Issue Report And Web
// @Tags Issue Report
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /issue-report/{id} [delete]
func (h issueReportController) deleteIssueReportAndWeb(c *gin.Context) {

	var deleteById model.IssueReportGetByIdRequest
	if err := c.ShouldBindUri(&deleteById); err != nil {
		HandleError(c, err)
		return
	}

	err := h.issueReportService.DeleteIssueReportAndWeb(deleteById.Id)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, nil)
}

// @Summary Get Issue Status Options
// @Description Get Issue Status Options
// @Tags Issue Report
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /issue-report/status/option [get]
func (h issueReportController) getIssueStatusOptions(c *gin.Context) {

	issueStatus, err := h.issueReportService.GetIssueStatusOptions()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, issueStatus)
}

// @Summary Create Web Url
// @Description Create Web Url
// @Tags Issue Report Web Url
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.CreateWebUrl true "body"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /issue-report/web-url [post]
func (h issueReportController) createWebUrl(c *gin.Context) {

	var body model.CreateWebUrl
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}

	insertId, err := h.issueReportService.CreateWebUrl(body)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, insertId)
}

// @Summary Get Web Url List
// @Description Get Web Url List
// @Tags Issue Report Web Url
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /issue-report/web-url/list [get]
func (h issueReportController) getWebUrlList(c *gin.Context) {

	webUrl, err := h.issueReportService.GetWebUrlList()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, webUrl)
}

// @Summary Get Web Url By Id
// @Description Get Web Url By Id
// @Tags Issue Report Web Url
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /issue-report/web-url/{id} [get]
func (h issueReportController) getWebUrlById(c *gin.Context) {

	var getById model.GetWebUrlById
	if err := c.ShouldBindUri(&getById); err != nil {
		HandleError(c, err)
		return
	}

	webUrl, err := h.issueReportService.GetWebUrlById(getById)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, webUrl)
}

// @Summary Delete Web Url By Id
// @Description Delete Web Url By Id
// @Tags Issue Report Web Url
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /issue-report/web-url/{id} [delete]
func (h issueReportController) deleteWebUrlById(c *gin.Context) {

	var deleteById model.GetWebUrlById
	if err := c.ShouldBindUri(&deleteById); err != nil {
		HandleError(c, err)
		return
	}

	err := h.issueReportService.DeleteWebUrlById(deleteById)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, "success")
}
