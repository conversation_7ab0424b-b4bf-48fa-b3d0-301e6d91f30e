package handler

import (
	"cybergame-api/middleware"
	"cybergame-api/model"
	"cybergame-api/repository"
	"cybergame-api/service"
	"os"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"gorm.io/gorm"
)

type dashboardController struct {
	dashboardService service.DashboardService
}

func newDashboardController(
	dashboardService service.DashboardService,
) dashboardController {
	return dashboardController{dashboardService}
}

func DashboardController(r *gin.RouterGroup, db *gorm.DB) {

	singleSession := middleware.SingleSession(db)
	role := middleware.Role(db)

	repo := repository.NewDashboardRepository(db)
	service := service.NewDashboardService(repo)
	handler := newDashboardController(service)
	dashboardBankRoute := r.Group("/dashboards/bank", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	dashboardBankRoute.GET("/total-depwid-summary", handler.getTotalDepositWithdrawSummary)

	dashboardProductRoute := r.Group("/dashboards/admin-corp", middleware.AuthorizeAdmin, singleSession.SingleAdminSession(), role.CheckPermission([]string{"admin_dashboard"}))
	dashboardProductRoute.GET("/bank-summary", handler.getAdminCorpBankSummary)
	dashboardProductRoute.GET("/pending-transaction-list", handler.getAdminCorpPendingTransactionList)

	testRoute := r.Group("/test")
	testRoute.POST("/dashboards/bank-pending-record", handler.createBankPendingRecord)
	testRoute.POST("/dashboards/bank-pending-record/approve", handler.approveBankPendingRecord)
	testRoute.POST("/dashboards/bank-pending-record/reject", handler.rejectBankPendingRecord)

}

// @Summary (getTotalDepositWithdrawSummary) รายงานบัญชีฝาก-ถอน รวม สรุปยอดฝากถอน
// @Description (getTotalDepositWithdrawSummary) กรอง datetype
// @Description กรองข้อมูลตามประเภทวัน ส่ง dateType เป็น daily, yesterday, last_week, last_month
// @Tags Dashboards - รายงานบัญชีฝาก-ถอน
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.ReportBankTotalDepositWithdrawRequest true "กรองข้อมูลตามประเภทวัน ส่ง dateType เป็น daily, yesterday, last_week, last_month"
// @Success 200 {object} model.ReportBankTotalDepositWithdrawResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /dashboards/bank/total-depwid-summary [get]
func (h *dashboardController) getTotalDepositWithdrawSummary(c *gin.Context) {

	var req model.ReportBankTotalDepositWithdrawRequest
	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.dashboardService.GetTotalDepositWithdrawSummary(req)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (getAdminCorpBankSummary) สรุปยอดฝากถอน
// @Description (getAdminCorpBankSummary) ล้างข้อมูลทุกเที่ยงคืน นับวันต่อวัน
// @Tags Dashboards - Admin Corperation
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.AdminCorpBankSummaryResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /dashboards/admin-corp/bank-summary [get]
func (h *dashboardController) getAdminCorpBankSummary(c *gin.Context) {

	data, err := h.dashboardService.GetAdminCorpBankSummary()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (getAdminCorpPendingTransactionList) รายการฝากถอนที่ยังไม่เรียบร้อย 5 รายการ
// @Description (getAdminCorpPendingTransactionList) รายการจะค้างจนกว่าจะเปลี่ยนสถานะ
// @Tags Dashboards - Admin Corperation
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} []model.BankPendingRecordResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /dashboards/admin-corp/pending-transaction-list [get]
func (h *dashboardController) getAdminCorpPendingTransactionList(c *gin.Context) {

	data, err := h.dashboardService.GetAdminCorpPendingTransactionList()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (createBankPendingRecord) TEST just add Data for test
// @Description (createBankPendingRecord)
// @Tags Dashboards - Admin Corperation
// @Accept json
// @Produce json
// @Param body body model.BankPendingRecordCreateRequest true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /test/dashboards/bank-pending-record [post]
func (h *dashboardController) createBankPendingRecord(c *gin.Context) {

	var body model.BankPendingRecordCreateRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	ginMode := os.Getenv("GIN_MODE")
	if ginMode == "release" {
		c.JSON(201, model.SuccessWithId{Message: "Created success", Id: time.Now().Unix()})
		return
	}

	insertId, err := h.dashboardService.CreateBankPendingRecord(body)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.SuccessWithId{Message: "Created success", Id: *insertId})
}

// @Summary (approveBankPendingRecord)  TEST just add Data for test
// @Description (approveBankPendingRecord)
// @Tags Dashboards - Admin Corperation
// @Accept json
// @Produce json
// @Param body body model.BankPendingRecordApproveRequest true "body"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /test/dashboards/bank-pending-record/approve [post]
func (h *dashboardController) approveBankPendingRecord(c *gin.Context) {

	ginMode := os.Getenv("GIN_MODE")
	if ginMode == "release" {
		c.JSON(200, model.Success{Message: "Updated success"})
		return
	}

	body := model.BankPendingRecordApproveRequest{}
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.dashboardService.ApproveBankPendingRecord(body); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "Updated success"})
}

// @Summary (rejectBankPendingRecord)  TEST just add Data for test
// @Description (rejectBankPendingRecord)
// @Tags Dashboards - Admin Corperation
// @Accept json
// @Produce json
// @Param body body model.BankPendingRecordRejectRequest true "body"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /test/dashboards/bank-pending-record/reject [post]
func (h *dashboardController) rejectBankPendingRecord(c *gin.Context) {

	ginMode := os.Getenv("GIN_MODE")
	if ginMode == "release" {
		c.JSON(200, model.Success{Message: "Updated success"})
		return
	}

	body := model.BankPendingRecordRejectRequest{}
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.dashboardService.RejectBankPendingRecord(body); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "Updated success"})
}
