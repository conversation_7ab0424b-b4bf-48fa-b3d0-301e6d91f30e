package handler

import (
	"cybergame-api/helper"
	"cybergame-api/middleware"
	"cybergame-api/model"
	"cybergame-api/service"
	"log"
	"strconv"

	"cybergame-api/repository"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"gorm.io/gorm"
)

type allianceController struct {
	allianceService service.AllianceService
}

func newAllianceController(
	allianceService service.AllianceService,
) allianceController {
	return allianceController{allianceService}
}

func AllianceController(r *gin.RouterGroup, db *gorm.DB) {

	repo := repository.NewAllianceRepository(db)
	agRepo := repository.NewAgentConnectRepository(db)
	service := service.NewAllianceService(repo, db, agRepo)
	handler := newAllianceController(service)

	role := middleware.Role(db)
	singleSession := middleware.SingleSession(db)

	a := r.Group("/alliances/backoffice", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	// a.GET("/commission-setting", role.CheckPermission([]string{"marketing_manage", "marketing_manage_edit", "member", "member_edit", "member_delete"}), handler.commissionSetting)
	a.GET("/commission-setting/user/:id", role.CheckPermission([]string{"marketing_manage", "marketing_manage_edit"}), handler.commissionSettingUser)
	a.PUT("/commission-setting", role.CheckPermission([]string{"marketing_manage_edit", "member_edit"}), handler.updateCommissionSetting)
	a.PUT("/commission-setting/user/:id", role.CheckPermission([]string{"alliances_commission_edit"}), handler.updateCommissionSettingUser)
	a.PUT("/downgrade-setting/user/:id", role.CheckPermission([]string{"marketing_manage_edit"}), handler.downSettingUserToAffiliate)

	p := r.Group("/alliances", middleware.AuthorizeUser, singleSession.SingleUserSession())
	p.GET("/summary", handler.allianceSummary)
	p.GET("/daily", handler.getMemberDailyTransaction)
	p.GET("/transaction-summary", handler.getAllianceTransactionSummary)
	p.GET("/member", handler.getMemberMoneyTransaction)
	p.GET("/member-total-income", handler.getMemberTotalIncome)
	p.GET("/member-total-income-summary", handler.getMemberTotalIncomeSummary)
	p.GET("/first-deposit-history", handler.getFirstDepositAllianceList)
	p.GET("/first-deposit-history/summary", handler.getFirstDepositAllianceSummary)

	// กลุ่มให้เข้าดูข้อมูลมุมมองผู้ใช้ได้จาก ไอดีแอดมิน
	adminAlliance := r.Group("/admin-alliances", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	adminAlliance.GET("/summary", handler.getUserAllianceSummary)
	adminAlliance.GET("/daily", handler.getUserMemberDailyTransaction)
	adminAlliance.GET("/daily-summary", handler.getUserAllianceTransactionSummary)
	adminAlliance.GET("/member", handler.getUserMemberMoneyTransaction)
	adminAlliance.GET("/member-total-income", handler.getUserMemberTotalIncome)
	adminAlliance.GET("/member-total-income-summary", handler.getUserMemberTotalIncomeSummary)
	adminAlliance.GET("/first-deposit-history/summary", handler.getUserFirstDepositAllianceSummary)
	adminAlliance.GET("/first-deposit-history", handler.getUserFirstDepositAllianceList)

	// พันธมิตร เมนูใหม่ 2023-12-25
	newAllianceRoute := r.Group("/alliances", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	newAllianceRoute.GET("/summary/list", handler.getAllianceTotalList)
	newAllianceRoute.GET("/summary/total", handler.getSumAllianceWinLoseTotal)

	// รายงานพันธมิตร - รายชื่อผู้เป็นพันธมิตร + รายงานพันธมิตรฝากครั้งแรก + รายละเอียดแพ้ชนะ + โยกเคลียร์ยอดพันธมิตร + ประวัติการโยก 10 รายการล่าสุด + ยอดฝากถอน
	reportRoute := r.Group("/alliances-report", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	reportRoute.GET("/members/list", handler.getAllianceUserList)
	reportRoute.GET("/first-deposit/summary", handler.getAllianceFirstDepositSummary)
	reportRoute.GET("/first-deposit/list", handler.getAllianceFirstDepositList)
	reportRoute.GET("/winlose/history", handler.getAllianceWinLoseHistory)
	reportRoute.GET("/winlose/summary", handler.getAllianceWinLoseSummary)
	reportRoute.POST("/income-withdraw", handler.withdrawAllianceIncome)
	reportRoute.POST("/income-withdraw/confirm/:id", handler.confirmAllianceIncome)
	reportRoute.GET("/income-withdraw/history", handler.getAllianceIncomeWithdrawHistory)
	reportRoute.GET("/bank-transaction/list", handler.getAllianceBankTransaction)
	reportRoute.GET("/bank-transaction/summary", handler.getAllianceBankTransactionSummary)

	webRoute := r.Group("/web")
	webRoute.GET("/alliances/alias", handler.getAliasByRef)

}

// @Summary (allianceSummary) หน้าเว็บ พันธมิตร - สรุปภาพรวม
// @Description Get Alliance Summary หน้าเว็บ พันธมิตร - สรุปภาพรวม
// @Tags Alliances
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.AllianceSummaryQuery false "Query Alliance"
// @Success 200 {object} model.AlSummary
// @Failure 400 {object} handler.ErrorResponse
// @Router /alliances/summary [get]
func (h allianceController) allianceSummary(c *gin.Context) {

	id := int64(c.MustGet("userId").(float64))

	var query model.AllianceSummaryQuery
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	query.UserId = id

	result, err := h.allianceService.AlSummary(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, result)
}

// @Summary (getMemberDailyTransaction) Get Alliance Transaction Daily หน้าเว็บ พันธมิตร - รายการฝาก-ถอนรายวัน
// @Description (getMemberDailyTransaction) Get Alliance Transaction Daily ส่งข้อมูลทุกวันที่กรองมา
// @Tags Alliances
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.AllianceTransactionDailyQuery false "Query Alliance"
// @Success 200 {object} model.AllianceTransactionResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /alliances/daily [get]
func (h allianceController) getMemberDailyTransaction(c *gin.Context) {

	id := int64(c.MustGet("userId").(float64))

	query := model.AllianceTransactionDailyQuery{}
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}

	result, err := h.allianceService.AlGetTransactionDaily(id, query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, result)
}

// @Summary (getAllianceTransactionSummary) Get Alliance Transaction Daily Summary แถวล่างสุด
// @Description Get (getAllianceTransactionSummary) Alliance Transaction Daily
// @Tags Alliances
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.AllianceTransactionDailyQuery false "Query Alliance"
// @Success 200 {object} model.AllianceTransaction
// @Failure 400 {object} handler.ErrorResponse
// @Router /alliances/transaction-summary [get]
func (h allianceController) getAllianceTransactionSummary(c *gin.Context) {

	id := int64(c.MustGet("userId").(float64))
	query := model.AllianceTransactionDailyQuery{}

	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}

	result, err := h.allianceService.AlGetTransactionSummary(id, query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, result)
}

// @Summary (getMemberMoneyTransaction) Get Alliance Transaction Member ฝาก-ถอน สมาชิก
// @Description (getMemberMoneyTransaction) Get Alliance Transaction Member
// @Tags Alliances
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.AllianceTransactionMemberQuery false "Query Alliance"
// @Success 200 {object} model.AllianceTransactionResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /alliances/member [get]
func (h allianceController) getMemberMoneyTransaction(c *gin.Context) {

	id := int64(c.MustGet("userId").(float64))

	query := model.AllianceTransactionMemberQuery{}
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}

	result, err := h.allianceService.AlGetTransactionMember(id, query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, result)
}

// @Summary (getMemberTotalIncome) หน้าเว็บ พันธมิตร - ตาราง รายได้
// @Description (getMemberTotalIncome) Get Alliance member Income
// @Tags Alliances
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.AllianceMemberTotalIncomeListRequest false "Query Alliance"
// @Success 200 {object} model.AllianceMemberTotalIncomeResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /alliances/member-total-income [get]
func (h allianceController) getMemberTotalIncome(c *gin.Context) {

	userId := int64(c.MustGet("userId").(float64))

	query := model.AllianceMemberTotalIncomeListRequest{}
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	query.RefUserId = userId

	result, err := h.allianceService.GetMemberTotalIncome(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, result)
}

// @Summary (getMemberTotalIncomeSummary) หน้าเว็บ พันธมิตร - ตาราง รายได้ ผลรวม
// @Description (getMemberTotalIncomeSummary) Get Alliance member Income
// @Tags Alliances
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.AllianceMemberTotalIncomeListRequest false "Query Alliance"
// @Success 200 {object} model.AllianceMemberTotalIncomeResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /alliances/member-total-income-summary [get]
func (h allianceController) getMemberTotalIncomeSummary(c *gin.Context) {

	userId := int64(c.MustGet("userId").(float64))

	query := model.AllianceMemberTotalIncomeListRequest{}
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	query.RefUserId = userId

	result, err := h.allianceService.GetMemberTotalIncomeSummary(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, result)
}

// @Summary Get Alliance Commission Setting User
// @Description Get Alliance Commission Setting User
// @Tags Alliances
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "User ID"
// @Success 200 {object} string
// @Failure 400 {object} handler.ErrorResponse
// @Router /alliances/backoffice/commission-setting/user/{id} [get]
func (h allianceController) commissionSettingUser(c *gin.Context) {

	id := c.Param("id")
	toInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleError(c, err)
		return
	}

	result, err := h.allianceService.AlGetCommissionSettingUser(toInt)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, result)
}

// @Summary (updateCommissionSetting) Update Alliance Commission Setting
// @Description (updateCommissionSetting) Update Alliance Commission Setting
// @Tags Alliances
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.AllianceCommissionBody true "body"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /alliances/backoffice/commission-setting [put]
func (h allianceController) updateCommissionSetting(c *gin.Context) {

	var body model.AllianceCommissionBody
	if err := c.ShouldBind(&body); err != nil {
		HandleError(c, err)
		return
	}

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))
	// [ADMIN_LOG]
	if err := h.allianceService.LogAdmin("updateCommissionSetting", adminId, body); err != nil {
		log.Println("updateCommissionSetting.ERROR: ", err)
	}

	if err := h.allianceService.AlUpdateCommissionSetting(body); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "Success"})
}

// @Summary (updateCommissionSettingUser) Update Alliance Commission Setting
// @Description (updateCommissionSettingUser) Update Alliance Commission Setting
// @Tags Alliances
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "User ID"
// @Param body body model.AllianceCommissionSettingUpdateRequest true "body"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /alliances/backoffice/commission-setting/user/{id} [put]
func (h allianceController) updateCommissionSettingUser(c *gin.Context) {

	id := c.Param("id")
	toInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleError(c, err)
		return
	}

	var body model.AllianceCommissionSettingUpdateRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))
	// [ADMIN_LOG]
	if err := h.allianceService.LogAdmin("updateCommissionSettingUser", adminId, body); err != nil {
		log.Println("updateCommissionSettingUser.ERROR: ", err)
	}

	if err := h.allianceService.AlUpdateCommissionSettingUser(toInt, body); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "Success"})
}

// @Summary (downSettingUserToAffiliate) ลดขั้นสมาชิกเป็น Affiliate
// @Description (downSettingUserToAffiliate) ลดขั้นสมาชิกเป็น Affiliate
// @Tags Alliances
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "User ID"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /alliances/backoffice/downgrade-setting/user/{id} [put]
func (h allianceController) downSettingUserToAffiliate(c *gin.Context) {

	id := c.Param("id")
	toInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleError(c, err)
		return
	}

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))
	// [ADMIN_LOG]
	if err := h.allianceService.LogAdmin("updateCommissionSettingUser", adminId, toInt); err != nil {
		log.Println("updateCommissionSettingUser.ERROR: ", err)
	}

	if err := h.allianceService.AlDownSettingUserToAffiliate(toInt); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "Success"})
}

// @Summary (getFirstDepositAllianceList) หน้าเว็บ พันธมิตร - ประวัติ ตารางรายชื่อสมาชิกที่ฝากครั้งแรก
// @Description (getFirstDepositAllianceList) Get First Deposit Alliance List
// @Tags Alliances
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.GetFirstDepositAliianceListRequest true "GetFirstDepositAliianceListRequest"
// @Success 200 {object} model.GetFirstDepositAliianceListRequest
// @Failure 400 {object} handler.ErrorResponse
// @Router /alliances/first-deposit-history [get]
func (h allianceController) getFirstDepositAllianceList(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))
	var req model.GetFirstDepositAliianceListRequest

	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(req); err != nil {
		HandleError(c, err)
		return
	}

	list, err := h.allianceService.GetFirstDepositAllianceList(userId, req)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, list)
}

// @Summary (GetFirstDepositAllianceSummary) หน้าเว็บ พันธมิตร - ประวัติ ดึงข้อมูลสรุป
// @Description (GetFirstDepositAllianceSummary) Get First Deposit Alliance Summary หน้าเว็บ พันธมิตร - ประวัติ
// @Tags Alliances
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.GetFirstDepositAliianceSummaryRequest true "GetFirstDepositAliianceSummaryRequest"
// @Success 200 {object} model.GetFirstDepositAllianceSummary
// @Failure 400 {object} handler.ErrorResponse
// @Router /alliances/first-deposit-history/summary [get]
func (h allianceController) getFirstDepositAllianceSummary(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))
	var req model.GetFirstDepositAliianceSummaryRequest

	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(req); err != nil {
		HandleError(c, err)
		return
	}

	list, err := h.allianceService.GetFirstDepositAllianceSummary(userId, req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, list)
}

// @Summary (getAllianceUserList) รายงานพันธมิตร - รายชื่อผู้เป็นพันธมิตร
// @Description (getAllianceUserList)
// @Description
// @Description # การกรองด้วยประเภทวัน dateType ทั้งหมด วันนี้ เมื่อวาน เดือนนี้ **ถ้าส่ง fromDate, toDate จะใช้ fromDate, toDate ก่อน
// @Description | dateType | Description |
// @Description | ---------------- | ------------|
// @Description | all *หรือไม่ส่ง* | แสดงทั้งหมด |
// @Description | today | แสดงวันนี้ |
// @Description | yesterday | แสดงเมื่อวาน |
// @Description | this_month | แสดงเดือนนี้ 1-31 |
// @Description | last_month | แสดง 30 วันก่อนหน้า |
// @Description | ---------------- | ------------|
// @Description
// @Description # กรองข้อมูลตามช่วงวันที่ ส่ง fromDate, toDate ในรูปแบบ YYYY-MM-DD (2021-12-31)
// @Description * ถ้าเลือกทั้งหมดไม่ต้องส่งเลย
// @Description
// @Tags Alliances - Report
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.AllianceUserListRequest true "query"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /alliances-report/members/list [get]
func (h allianceController) getAllianceUserList(c *gin.Context) {

	var query model.AllianceUserListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	list, total, err := h.allianceService.GetAllianceUserList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithPagination{Message: "Success", List: list, Total: total})
}

// @Summary (getAllianceFirstDepositSummary) รายงานพันธมิตร - รายงานพันธมิตรฝากครั้งแรก - ข้อมูลสรุป
// @Description (getAllianceFirstDepositSummary)
// @Description
// @Description # การกรองด้วยประเภทวัน dateType ทั้งหมด วันนี้ เมื่อวาน เดือนนี้ **ถ้าส่ง fromDate, toDate จะใช้ fromDate, toDate ก่อน
// @Description | dateType | Description |
// @Description | ---------------- | ------------|
// @Description | all *หรือไม่ส่ง* | แสดงทั้งหมด |
// @Description | today | แสดงวันนี้ |
// @Description | yesterday | แสดงเมื่อวาน |
// @Description | this_month | แสดงเดือนนี้ 1-31 |
// @Description | last_month | แสดง 30 วันก่อนหน้า |
// @Description | ---------------- | ------------|
// @Description
// @Description # กรองข้อมูลตามช่วงวันที่ ส่ง fromDate, toDate ในรูปแบบ YYYY-MM-DD (2021-12-31)
// @Description * ถ้าเลือกทั้งหมดไม่ต้องส่งเลย
// @Description
// @Tags Alliances - Report
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.AllianceFirstDepositListRequest true "query"
// @Success 200 {object} model.AllianceFirstDepositSummaryResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /alliances-report/first-deposit/summary [get]
func (h allianceController) getAllianceFirstDepositSummary(c *gin.Context) {

	var query model.AllianceFirstDepositListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	summary, err := h.allianceService.GetAllianceFirstDepositSummary(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, summary)
}

// @Summary (getAllianceFirstDepositList) รายงานพันธมิตร - รายงานพันธมิตรฝากครั้งแรก - รายชื่อลูกข่ายที่ฝากครั้งแรก
// @Description (getAllianceFirstDepositList)
// @Description
// @Description # การกรองด้วยประเภทวัน dateType ทั้งหมด วันนี้ เมื่อวาน เดือนนี้ **ถ้าส่ง fromDate, toDate จะใช้ fromDate, toDate ก่อน
// @Description | dateType | Description |
// @Description | ---------------- | ------------|
// @Description | all *หรือไม่ส่ง* | แสดงทั้งหมด |
// @Description | today | แสดงวันนี้ |
// @Description | yesterday | แสดงเมื่อวาน |
// @Description | this_month | แสดงเดือนนี้ 1-31 |
// @Description | last_month | แสดง 30 วันก่อนหน้า |
// @Description | ---------------- | ------------|
// @Description [20250507] เพิ่มวันที่สมัครสมาชิก
// @Description
// @Description # กรองข้อมูลตามช่วงวันที่ ส่ง fromDate, toDate ในรูปแบบ YYYY-MM-DD (2021-12-31)
// @Description * ถ้าเลือกทั้งหมดไม่ต้องส่งเลย
// @Description
// @Tags Alliances - Report
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.AllianceFirstDepositListRequest true "query"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /alliances-report/first-deposit/list [get]
func (h allianceController) getAllianceFirstDepositList(c *gin.Context) {

	var query model.AllianceFirstDepositListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	list, total, err := h.allianceService.GetAllianceFirstDepositList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithPagination{Message: "Success", List: list, Total: total})
}

// @Summary (getAllianceWinLoseHistory) รายงานพันธมิตร - รายละเอียดแพ้ชนะ
// @Description (getAllianceWinLoseHistory)
// @Description
// @Description # การกรองด้วยประเภทวัน dateType ทั้งหมด วันนี้ เมื่อวาน เดือนนี้ **ถ้าส่ง fromDate, toDate จะใช้ fromDate, toDate ก่อน
// @Description | dateType | Description |
// @Description | ---------------- | ------------|
// @Description | all *หรือไม่ส่ง* | แสดงทั้งหมด |
// @Description | today | แสดงวันนี้ |
// @Description | yesterday | แสดงเมื่อวาน |
// @Description | this_month | แสดงเดือนนี้ 1-31 |
// @Description | last_month | แสดง 30 วันก่อนหน้า |
// @Description | ---------------- | ------------|
// @Description
// @Description # กรองข้อมูลตามช่วงวันที่ ส่ง fromDate, toDate ในรูปแบบ YYYY-MM-DD (2021-12-31)
// @Description * ถ้าเลือกทั้งหมดไม่ต้องส่งเลย
// @Description
// @Tags Alliances - Report
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.AllianceWinLoseHistoryListRequest true "query"
// @Success 200 {object} model.AllianceWinLoseHistoryResponseWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /alliances-report/winlose/history [get]
func (h allianceController) getAllianceWinLoseHistory(c *gin.Context) {

	var query model.AllianceWinLoseHistoryListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	response, err := h.allianceService.GetAllianceWinLoseHistory(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, response)
}

// @Summary (getAllianceWinLoseSummary) รายงานพันธมิตร - รายละเอียดแพ้ชนะ เฉพาะผลรวม
// @Description (getAllianceWinLoseSummary)
// @Description
// @Description # การกรองด้วยประเภทวัน dateType ทั้งหมด วันนี้ เมื่อวาน เดือนนี้ **ถ้าส่ง fromDate, toDate จะใช้ fromDate, toDate ก่อน
// @Description | dateType | Description |
// @Description | ---------------- | ------------|
// @Description | all *หรือไม่ส่ง* | แสดงทั้งหมด |
// @Description | today | แสดงวันนี้ |
// @Description | yesterday | แสดงเมื่อวาน |
// @Description | this_month | แสดงเดือนนี้ 1-31 |
// @Description | last_month | แสดง 30 วันก่อนหน้า |
// @Description | ---------------- | ------------|
// @Description
// @Description # กรองข้อมูลตามช่วงวันที่ ส่ง fromDate, toDate ในรูปแบบ YYYY-MM-DD (2021-12-31)
// @Description * ถ้าเลือกทั้งหมดไม่ต้องส่งเลย
// @Description
// @Tags Alliances - Report
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.AllianceWinLoseHistoryListRequest true "query"
// @Success 200 {object} model.AllianceTotalWinLoseHistoryResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /alliances-report/winlose/summary [get]
func (h allianceController) getAllianceWinLoseSummary(c *gin.Context) {

	var query model.AllianceWinLoseHistoryListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	record, err := h.allianceService.GetAllianceWinLoseSummary(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, record)
}

// @Summary (withdrawAllianceIncome) รายงานพันธมิตร - รายละเอียดแพ้ชนะ - โยกเคลียร์ยอดพันธมิตร
// @Description (withdrawAllianceIncome) รายงานพันธมิตร - รายละเอียดแพ้ชนะ - โยกเคลียร์ยอดพันธมิตร
// @Description
// @Description # [20240103] โยกเคลียร์ยอดพันธมิตร แบบโอนเครดิตได้ด้วย, ให้ส่ง TransferType = "credit" ถ้าต้องการให้โอนเครดิตเข้ากระเป๋า
// @Description
// @Tags Alliances - Report
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.AllianceIncomeWithdrawCreateRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /alliances-report/income-withdraw [post]
func (h allianceController) withdrawAllianceIncome(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	var body model.AllianceIncomeWithdrawCreateRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}
	body.CreatedBy = adminId

	// RACE_CONDITION_BLOCKER
	if _, err := h.allianceService.RacingWithdrawAllianceIncome(body); err != nil {
		HandleError(c, err)
		return
	}

	// [ADMIN_LOG]
	if err := h.allianceService.LogAdmin("withdrawAllianceIncome", adminId, body); err != nil {
		log.Println("withdrawAllianceIncome.ERROR: ", err)
	}

	if err := h.allianceService.WithdrawAllianceIncome(body); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Created success"})
}

// @Summary (confirmAllianceIncome) รายงานพันธมิตร - Manual confirm pending income
// @Description (confirmAllianceIncome) รายงานพันธมิตร
// @Tags Alliances - Report
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "incomeId"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /alliances-report/income-withdraw/confirm/{id} [post]
func (h allianceController) confirmAllianceIncome(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	id := c.Param("id")
	toInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleError(c, err)
		return
	}

	// [ADMIN_LOG]
	if err := h.allianceService.LogAdmin("confirmAllianceIncome", adminId, toInt); err != nil {
		log.Println("confirmAllianceIncome.ERROR: ", err)
	}

	if err := h.allianceService.ConfirmAllianceIncome(toInt, adminId); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "Update success"})
}

// @Summary (getAllianceIncomeWithdrawHistory) รายงานพันธมิตร - รายละเอียดแพ้ชนะ - โยกเคลียร์ยอดพันธมิตร ประวัติ 10 รายการล่าสุด
// @Description (getAllianceIncomeWithdrawHistory)
// @Description # การกรองวันที่ กรองเพื่อเอาข้อมูลตัวเลขเฉพาะช่วงที่เลือก
// @Description กรองข้อมูลตามช่วงวันที่บันทึก ส่ง fromDate, toDate ในรูปแบบ YYYY-MM-DD (2021-12-31)
// @Tags Alliances - Report
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.AllianceIncomeWithdrawHistoryListRequest true "query"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /alliances-report/income-withdraw/history [get]
func (h allianceController) getAllianceIncomeWithdrawHistory(c *gin.Context) {

	var query model.AllianceIncomeWithdrawHistoryListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	list, total, err := h.allianceService.GetAllianceIncomeWithdrawHistory(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithPagination{Message: "Success", List: list, Total: total})
}

// @Summary (getAllianceBankTransactionSummary) รายงานพันธมิตร - ยอดฝากถอน
// @Description (getAllianceBankTransactionSummary)
// @Description
// @Description # การกรองด้วยประเภทวัน dateType ทั้งหมด วันนี้ เมื่อวาน เดือนนี้ **ถ้าส่ง fromDate, toDate จะใช้ fromDate, toDate ก่อน
// @Description | dateType | Description |
// @Description | ---------------- | ------------|
// @Description | all *หรือไม่ส่ง* | แสดงทั้งหมด |
// @Description | today | แสดงวันนี้ |
// @Description | yesterday | แสดงเมื่อวาน |
// @Description | this_month | แสดงเดือนนี้ 1-31 |
// @Description | last_month | แสดง 30 วันก่อนหน้า |
// @Description | ---------------- | ------------|
// @Description
// @Description # กรองข้อมูลตามช่วงวันที่ ส่ง fromDate, toDate ในรูปแบบ YYYY-MM-DD (2021-12-31)
// @Description * ถ้าเลือกทั้งหมดไม่ต้องส่งเลย
// @Description
// @Tags Alliances - Report
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.AllianceBankTransactionListRequest true "query"
// @Success 200 {object} model.AllianceBankTransactionResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /alliances-report/bank-transaction/summary [get]
func (h allianceController) getAllianceBankTransactionSummary(c *gin.Context) {

	var query model.AllianceBankTransactionListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	reponse, err := h.allianceService.GetAllianceBankTransactionSummary(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, reponse)
}

// @Summary (getAllianceBankTransaction) รายงานพันธมิตร - ยอดฝากถอน
// @Description (getAllianceBankTransaction)
// @Description
// @Description # การกรองด้วยประเภทวัน dateType ทั้งหมด วันนี้ เมื่อวาน เดือนนี้ **ถ้าส่ง fromDate, toDate จะใช้ fromDate, toDate ก่อน
// @Description | dateType | Description |
// @Description | ---------------- | ------------|
// @Description | all *หรือไม่ส่ง* | แสดงทั้งหมด |
// @Description | today | แสดงวันนี้ |
// @Description | yesterday | แสดงเมื่อวาน |
// @Description | this_month | แสดงเดือนนี้ 1-31 |
// @Description | last_month | แสดง 30 วันก่อนหน้า |
// @Description | ---------------- | ------------|
// @Description
// @Description # กรองข้อมูลตามช่วงวันที่ ส่ง fromDate, toDate ในรูปแบบ YYYY-MM-DD (2021-12-31)
// @Description * ถ้าเลือกทั้งหมดไม่ต้องส่งเลย
// @Description
// @Tags Alliances - Report
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.AllianceBankTransactionListRequest true "query"
// @Success 200 {object} model.AllianceBankTransactionResponseWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /alliances-report/bank-transaction/list [get]
func (h allianceController) getAllianceBankTransaction(c *gin.Context) {

	var query model.AllianceBankTransactionListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	reponse, err := h.allianceService.GetAllianceBankTransaction(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, reponse)
}

// @Summary (getUserAllianceSummary) หน้าเว็บ พันธมิตร - สรุปภาพรวม
// @Description (getUserAllianceSummary) Get Alliance Summary พันธมิตร - สรุปภาพรวม
// @Tags Alliances - From Admin (DEBUG)
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.GetUserAliianceSummaryRequest true "GetUserAliianceSummaryRequest"
// @Success 200 {object} model.AlSummary
// @Failure 400 {object} handler.ErrorResponse
// @Router /admin-alliances/summary [get]
func (h allianceController) getUserAllianceSummary(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))
	var req model.GetUserFirstDepositAliianceSummaryRequest
	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}
	req.AdminId = adminId
	if err := validator.New().Struct(req); err != nil {
		HandleError(c, err)
		return
	}

	var query model.AllianceSummaryQuery
	query.UserId = req.UserId
	query.FromDate = req.From
	query.ToDate = req.To
	result, err := h.allianceService.AlSummary(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, result)
}

// @Summary (getUserMemberDailyTransaction) หน้าเว็บ พันธมิตร - รายการฝาก-ถอนรายวัน
// @Description (getUserMemberDailyTransaction) Get Alliance Transaction Daily ส่งข้อมูลทุกวันที่กรองมา
// @Tags Alliances - From Admin (DEBUG)
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.GetUserFirstDepositAliianceListRequest false "query"
// @Success 200 {object} model.AllianceTransactionResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /admin-alliances/daily [get]
func (h allianceController) getUserMemberDailyTransaction(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))
	query := model.GetUserFirstDepositAliianceListRequest{}
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	query.AdminId = adminId

	var req model.AllianceTransactionDailyQuery
	req.From = query.From
	req.To = query.To
	req.Page = query.Page
	req.Limit = query.Limit
	result, err := h.allianceService.AlGetTransactionDaily(query.UserId, req)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, result)
}

// @Summary (getUserAllianceTransactionSummary) หน้าเว็บ พันธมิตร - รายการฝาก-ถอนรายวัน แถวล่างสุด
// @Description Get (getUserAllianceTransactionSummary) Alliance Transaction Daily
// @Tags Alliances - From Admin (DEBUG)
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.GetUserFirstDepositAliianceListRequest false "Query Alliance"
// @Success 200 {object} model.AllianceTransaction
// @Failure 400 {object} handler.ErrorResponse
// @Router /admin-alliances/daily-summary [get]
func (h allianceController) getUserAllianceTransactionSummary(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))
	query := model.GetUserFirstDepositAliianceListRequest{}
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	query.AdminId = adminId

	var req model.AllianceTransactionDailyQuery
	req.From = query.From
	req.To = query.To
	result, err := h.allianceService.AlGetTransactionSummary(query.UserId, req)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, result)
}

// @Summary (getUserMemberMoneyTransaction) Get Alliance Transaction Member ฝาก-ถอน สมาชิก
// @Description (getUserMemberMoneyTransaction) Get Alliance Transaction Member
// @Tags Alliances - From Admin (DEBUG)
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.AllianceAdminTransactionMemberQuery false "Query Alliance"
// @Success 200 {object} model.AllianceTransactionResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /admin-alliances/member [get]
func (h allianceController) getUserMemberMoneyTransaction(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))
	query := model.AllianceAdminTransactionMemberQuery{}
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	query.AdminId = adminId

	var req model.AllianceTransactionMemberQuery
	req.From = query.From
	req.To = query.To
	req.MemberCode = query.MemberCode

	result, err := h.allianceService.AlGetTransactionMember(query.UserId, req)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, result)
}

// @Summary (getUserMemberTotalIncome) หน้าเว็บ พันธมิตร - ตาราง รายได้
// @Description (getUserMemberTotalIncome) Get Alliance member Income
// @Tags Alliances - From Admin (DEBUG)
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.AllianceAdminMemberTotalIncomeListRequest false "Query Alliance"
// @Success 200 {object} model.AllianceMemberTotalIncomeResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /admin-alliances/member-total-income [get]
func (h allianceController) getUserMemberTotalIncome(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	query := model.AllianceAdminMemberTotalIncomeListRequest{}
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	query.AdminId = adminId

	var req model.AllianceMemberTotalIncomeListRequest
	req.RefUserId = query.RefUserId
	req.Search = query.Search
	req.FromDate = query.FromDate
	req.ToDate = query.ToDate
	req.Page = query.Page
	req.Limit = query.Limit
	req.SortCol = query.SortCol
	req.SortAsc = query.SortAsc
	result, err := h.allianceService.GetMemberTotalIncome(req)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, result)
}

// @Summary (getUserMemberTotalIncomeSummary) หน้าเว็บ พันธมิตร - ตาราง รายได้ ผลรวม
// @Description (getUserMemberTotalIncomeSummary) Get Alliance member Income
// @Tags Alliances - From Admin (DEBUG)
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.AllianceAdminMemberTotalIncomeListRequest false "Query Alliance"
// @Success 200 {object} model.AllianceMemberTotalIncomeResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /admin-alliances/member-total-income-summary [get]
func (h allianceController) getUserMemberTotalIncomeSummary(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	query := model.AllianceAdminMemberTotalIncomeListRequest{}
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	query.AdminId = adminId

	var req model.AllianceMemberTotalIncomeListRequest
	req.RefUserId = query.RefUserId
	req.Search = query.Search
	req.FromDate = query.FromDate
	req.ToDate = query.ToDate
	req.Page = query.Page
	req.Limit = query.Limit
	req.SortCol = query.SortCol
	req.SortAsc = query.SortAsc
	result, err := h.allianceService.GetMemberTotalIncomeSummary(req)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, result)
}

// @Summary (getUserFirstDepositAllianceSummary) หน้าเว็บ พันธมิตร - ประวัติ ตารางรายชื่อสมาชิกที่ฝากครั้งแรก
// @Description (getUserFirstDepositAllianceSummary) Get First Deposit Alliance Summary
// @Tags Alliances - From Admin (DEBUG)
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.GetUserFirstDepositAliianceSummaryRequest true "GetUserFirstDepositAliianceSummaryRequest"
// @Success 200 {object} model.GetFirstDepositAllianceSummary
// @Failure 400 {object} handler.ErrorResponse
// @Router /admin-alliances/first-deposit-history/summary [get]
func (h allianceController) getUserFirstDepositAllianceSummary(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	var req model.GetUserFirstDepositAliianceSummaryRequest
	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}
	req.AdminId = adminId
	if err := validator.New().Struct(req); err != nil {
		HandleError(c, err)
		return
	}

	var query model.GetFirstDepositAliianceSummaryRequest
	query.From = req.From
	query.To = req.To
	list, err := h.allianceService.GetFirstDepositAllianceSummary(req.UserId, query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, list)
}

// @Summary (getUserFirstDepositAllianceList) หน้าเว็บ พันธมิตร - ประวัติ ตารางรายชื่อสมาชิกที่ฝากครั้งแรก
// @Description (getUserFirstDepositAllianceList) Get First Deposit Alliance List
// @Tags Alliances - From Admin (DEBUG)
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.GetUserFirstDepositAliianceListRequest true "GetUserFirstDepositAliianceListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /admin-alliances/first-deposit-history [get]
func (h allianceController) getUserFirstDepositAllianceList(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	var req model.GetUserFirstDepositAliianceListRequest
	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}
	req.AdminId = adminId
	if err := validator.New().Struct(req); err != nil {
		HandleError(c, err)
		return
	}

	var query model.GetFirstDepositAliianceListRequest
	query.From = req.From
	query.To = req.To
	query.Page = req.Page
	query.Limit = req.Limit
	list, err := h.allianceService.GetFirstDepositAllianceList(req.UserId, query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, list)
}

// @Summary (getAllianceTotalList) พันธมิตร - เมนูใหม่ 2023-12-25
// @Description (getAllianceTotalList)
// @Description
// @Description # การกรองด้วยประเภทวัน dateType ทั้งหมด วันนี้ เมื่อวาน เดือนนี้ **ถ้าส่ง fromDate, toDate จะใช้ fromDate, toDate ก่อน
// @Description | dateType | Description |
// @Description | ---------------- | ------------|
// @Description | all *หรือไม่ส่ง* | แสดงทั้งหมด |
// @Description | today | แสดงวันนี้ |
// @Description | yesterday | แสดงเมื่อวาน |
// @Description | this_month | แสดงเดือนนี้ 1-31 |
// @Description | last_month | แสดง 30 วันก่อนหน้า |
// @Description | ---------------- | ------------|
// @Description
// @Description # กรองข้อมูลตามช่วงวันที่ ส่ง fromDate, toDate ในรูปแบบ YYYY-MM-DD (2021-12-31)
// @Description * ถ้าเลือกทั้งหมดไม่ต้องส่งเลย
// @Description
// @Tags Alliances - พันธมิตร
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.AllianceWinLoseTotalListRequest true "query"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /alliances/summary/list [get]
func (h allianceController) getAllianceTotalList(c *gin.Context) {

	var query model.AllianceWinLoseTotalListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	list, total, err := h.allianceService.GetAllianceWinLoseList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithPagination{Message: "Success", List: list, Total: total})
}

// @Summary (getAliasByRef) หน้าเว็บ พันธมิตร - นามแฝง
// @Description (getAliasByRef) Get Alias By UserId
// @Tags Web - Alliances
// @Accept json
// @Produce json
// @Param _ query model.GetAliasByUserIdRequest true "Query GetAliasByUserIdRequest"
// @Success 200 {object} model.GetAliasByUserIdResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/alliances/alias [get]
func (h allianceController) getAliasByRef(c *gin.Context) {

	var query model.GetAliasByUserIdRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}

	alias, err := h.allianceService.GetAliasByRef(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, alias)
}

// @Summary (getSumAllianceWinLoseTotal) พันธมิตร - รายงานพันธมิตร - รายงานพันธมิตรฝากครั้งแรก - ข้อมูลสรุป
// @Description (getSumAllianceWinLoseTotal) พันธมิตร - รายงานพันธมิตร - รายงานพันธมิตรฝากครั้งแรก - ข้อมูลสรุป
// @Tags Alliances - พันธมิตร
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.AllianceWinLoseSumTotalRequest true "query"
// @Success 200 {object} model.GetSumAllianceWinLoseTotalResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /alliances/summary/total [get]
func (h allianceController) getSumAllianceWinLoseTotal(c *gin.Context) {

	var query model.AllianceWinLoseSumTotalRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	summary, err := h.allianceService.GetSumAllianceWinLoseTotal(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, summary)
}
