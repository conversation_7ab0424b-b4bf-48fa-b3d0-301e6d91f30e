package handler

import (
	"cybergame-api/helper"
	"cybergame-api/middleware"
	"cybergame-api/model"
	"cybergame-api/repository"
	"cybergame-api/service"
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type couponCashController struct {
	CouponCashService service.CouponCashService
}

func newCouponCashController(
	CouponCashService service.CouponCashService,
) couponCashController {
	return couponCashController{CouponCashService}
}

func CouponCashController(r *gin.RouterGroup, db *gorm.DB) {

	singleSession := middleware.SingleSession(db)

	repo := repository.NewCouponCashRepository(db)
	userRepo := repository.NewUserRepository(db)
	perRepo := repository.NewPermissionRepository(db)
	groupRepo := repository.NewGroupRepository(db)
	otpRepo := repository.NewOtpRepository(db)
	agentInfoRepo := repository.NewAgentInfoRepository(db)
	recommendRepo := repository.NewRecommendRepository(db)
	afRepo := repository.NewAffiliateRepository(db)
	notiRepo := repository.NewNotificationRepository(db)
	notiService := service.NewNotificationService(notiRepo)
	actionService := service.NewAdminActionService(repository.NewAdminActionRepository(db))
	userService := service.NewUserService(
		userRepo,
		db,
		perRepo,
		groupRepo,
		otpRepo,
		agentInfoRepo,
		recommendRepo,
		afRepo,
		notiService,
		actionService,
	)

	service := service.NewCouponCashService(repo, userService)
	handler := newCouponCashController(service)

	couponCash := r.Group("/coupon-cash", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	//option
	optionCouponCash := couponCash.Group("/option")
	optionCouponCash.GET("/status", handler.GetCouponCashStatus)
	optionCouponCash.GET("/user-status", handler.GetCouponCashUserStatus)

	//coupon cash
	couponCash.POST("", handler.CreateCouponCash)
	couponCash.GET("/list", handler.GetCouponCashList)
	couponCash.GET("/user-list", handler.GetCouponCashUserList)
	couponCash.DELETE("/delete/:id", handler.deleteMainCouponCashById)
	couponCash.DELETE("/user/delete/:id", handler.deleteCouponCashUserById)
	couponCash.GET("/check-turn-over-withdraw", handler.checkCouponTurnOverWithdraw)
	couponCash.GET("/summary", handler.CouponCashSummary)
	couponCash.GET("/user-summary", handler.CouponCashUserSummary)

	couponCashUser := r.Group("/coupon-cash", middleware.AuthorizeUser, singleSession.SingleUserSession())
	couponCashUser.POST("/user-use", handler.UserUseCouponCash)

}

// @Summary Get Coupon Cash Status (GetCouponCashStatus)
// @Description Get Coupon Cash Status
// @Tags Coupon Cash Option
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.CouponCashStatus
// @Failure 400 {object} handler.ErrorResponse
// @Router /coupon-cash/option/status [get]
func (h couponCashController) GetCouponCashStatus(c *gin.Context) {

	option, err := h.CouponCashService.GetCouponCashStatus()
	if err != nil {
		HandleError(c, err)
		return

	}
	c.JSON(200, option)
}

// @Summary Get Coupon Cash User Status (GetCouponCashUserStatus)
// @Description Get Coupon Cash User Status
// @Tags Coupon Cash Option
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.CouponCashUserStatus
// @Failure 400 {object} handler.ErrorResponse
// @Router /coupon-cash/option/user-status [get]
func (h couponCashController) GetCouponCashUserStatus(c *gin.Context) {

	option, err := h.CouponCashService.GetCouponCashUserStatus()
	if err != nil {
		HandleError(c, err)
		return

	}
	c.JSON(200, option)
}

// @Summary Create Coupon Cash (CreateCouponCash)
// @Description Create Coupon Cash
// @Tags Coupon Cash
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param req body model.CouponCashCreateRequest true "body"
// @Success 200 {object} model.CouponCashCreateRequest
// @Failure 400 {object} handler.ErrorResponse
// @Router /coupon-cash [post]
func (h couponCashController) CreateCouponCash(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	var req model.CouponCashCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	req.CreatedByAdminId = adminId
	id, err := h.CouponCashService.CreateCouponCash(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, id)
}

// @Summary Get Coupon Cash List (GetCouponCashList)
// @Description Get Coupon Cash List
// @Tags Coupon Cash
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.GetCouponCashListRequest true "GetCouponCashListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /coupon-cash/list [get]
func (h couponCashController) GetCouponCashList(c *gin.Context) {

	var req model.GetCouponCashListRequest
	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}

	list, err := h.CouponCashService.GetCouponCashList(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, list)
}

// @Summary Get Coupon Cash User List (GetCouponCashUserList) รายละเอียดการใช้คูปอง
// @Description Get Coupon Cash User List รายละเอียดการใช้คูปอง
// @Tags Coupon Cash
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.GetCouponCashUserListRequest true "GetCouponCashUserListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /coupon-cash/user-list [get]
func (h couponCashController) GetCouponCashUserList(c *gin.Context) {

	var req model.GetCouponCashUserListRequest
	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}

	list, err := h.CouponCashService.GetCouponCashUserList(req)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, list)
}

// UserUseCouponCash(req model.UserUseCouponCashRequest) (string, error)
// @Summary User Use Coupon Cash (UserUseCouponCash)
// @Description User Use Coupon Cash
// @Tags Coupon Cash
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param req body model.UserUseCouponCashRequest true "body"
// @Success 200 {string} string
// @Failure 400 {object} handler.ErrorResponse
// @Router /coupon-cash/user-use [post]
func (h couponCashController) UserUseCouponCash(c *gin.Context) {

	var req model.UserUseCouponCashRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	req.UserId = helper.ConvertIdAnyToInt64(c.MustGet("userId"))
	message, err := h.CouponCashService.UserUseCouponCash(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, message)
}

// @Summary Delete Coupon Cash By Id (DeleteCouponCashUserById)
// @Description Delete Coupon Cash By Id
// @Tags Coupon Cash
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 200 {string} string
// @Failure 400 {object} handler.ErrorResponse
// @Router /coupon-cash/delete/{id} [delete]
func (h couponCashController) deleteMainCouponCashById(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))
	var param model.SoftDeleteCouponCashUserRequest
	if err := c.ShouldBindUri(&param); err != nil {
		HandleError(c, err)
		return
	}

	var req model.SoftDeleteCouponCashUserByCouponCashId
	req.Id = param.Id
	req.DeletedByAdminId = adminId
	req.DeletedAt = time.Now()

	err := h.CouponCashService.DeleteMainCouponCashUserById(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, "success")
}

// @Summary Delete Coupon Cash User By Id (DeleteCouponCashUserById)
// @Description Delete Coupon Cash User By Id
// @Tags Coupon Cash
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 200 {string} string
// @Failure 400 {object} handler.ErrorResponse
// @Router /coupon-cash/user/delete/{id} [delete]
func (h couponCashController) deleteCouponCashUserById(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))
	var param model.SoftDeleteCouponCashRequest
	if err := c.ShouldBindUri(&param); err != nil {
		HandleError(c, err)
		return
	}

	var req model.SoftDeleteCouponCashUserById
	req.Id = param.Id
	req.DeletedByAdminId = adminId
	req.DeletedAt = time.Now()

	err := h.CouponCashService.DeleteCouponCashUserById(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, "success")
}

// @Summary Check Coupon Turn Over Withdraw (CheckCouponTurnOverWithdraw)
// @Description Check Coupon Turn Over Withdraw
// @Tags Coupon Cash
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.CheckCouponTurnOverWithdrawRequest true "CheckCouponTurnOverWithdrawRequest"
// @Success 200 {string} string
// @Failure 400 {object} handler.ErrorResponse
// @Router /coupon-cash/check-turn-over-withdraw [get]
func (h couponCashController) checkCouponTurnOverWithdraw(c *gin.Context) {

	var param model.CheckCouponTurnOverWithdrawRequest
	if err := c.ShouldBind(&param); err != nil {
		HandleError(c, err)
		return
	}
	message, data, err := h.CouponCashService.CheckCouponTurnOverWithdraw(param.UserId, param.WithdrawAmount)
	if err != nil {
		HandleError(c, err)
		return
	}

	message = fmt.Sprintf("Turn Over Withdraw message: %s, Turn Over Withdraw: %v", message, data)

	c.JSON(200, message)
}

// @Summary Coupon Cash Summary (CouponCashSummary)
// @Description Coupon Cash Summary
// @Tags Coupon Cash
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.CouponCashSummaryRequest true "CouponCashSummaryRequest"
// @Success 200 {object} model.CouponCashSummaryResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /coupon-cash/summary [get]
func (h couponCashController) CouponCashSummary(c *gin.Context) {

	var req model.CouponCashSummaryRequest
	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.CouponCashService.CouponCashSummary(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary Coupon Cash User Summary (CouponCashUserSummary)
// @Description Coupon Cash User Summary
// @Description กรองจากวันที่รับคูปองไม่ใช่วันที่สร้าง
// @Tags Coupon Cash
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.CouponCashUserSummaryRequest true "CouponCashUserSummaryRequest"
// @Success 200 {object} model.CouponCashUserSummaryResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /coupon-cash/user-summary [get]
func (h couponCashController) CouponCashUserSummary(c *gin.Context) {

	var req model.CouponCashUserSummaryRequest
	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.CouponCashService.CouponCashUserSummary(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}
