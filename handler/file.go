package handler

import (
	"cybergame-api/service"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type fileController struct {
	FileService service.FileService
}

func newFileController(
	FileService service.FileService,
) fileController {
	return fileController{FileService}
}

func FileController(r *gin.RouterGroup, db *gorm.DB) {

	// repo := repository.NewFileRepository(db)
	// service := service.NewFileService(repo)
	// handler := newFileController(service)

	// file := r.Group("/media")
	// file.POST("", handler.uploadMedia)

	// fileRoute := r.Group("/files")
	// fileRoute.POST("", handler.uploadFile)
}

// @Summary อัพโหลดไฟล์ เดิม uploadMedia
// @Description uploadMedia
// @Tags Media
// @Accept json
// @Security BearerAuth
// @Produce json
// @Param file formData file true "file"
// @Param directory formData string true "directory"
// @Success 201 {object} model.FileUploadResponse
// @Failure 400 {object} ErrorResponse
// @Router /media [post]
func (h fileController) uploadMedia(c *gin.Context) {

	result, err := h.FileService.UploadMedia(c.Request)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, result)
}

// @Summary อัพโหลดไฟล์แบบใหม่
// @Description Upload File
// @Tags Media
// @Accept json
// @Security BearerAuth
// @Produce json
// @Param file formData file true "file"
// @Success 201 {object} model.FileUploadResponse
// @Failure 400 {object} ErrorResponse
// @Router /files [post]
func (h fileController) uploadFile(c *gin.Context) {

	result, err := h.FileService.UploadFile(c.Request)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, result)
}
