package handler

import (
	"cybergame-api/helper"
	"cybergame-api/middleware"
	"cybergame-api/model"
	"cybergame-api/service"
	"errors"
	"fmt"
	"log"
	"strings"

	"cybergame-api/repository"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type authController struct {
	adminService service.AdminService
}

func newAuthController(
	adminService service.AdminService,
) authController {
	return authController{adminService}
}

func AuthController(r *gin.RouterGroup, db *gorm.DB) {

	actionService := service.NewAdminActionService(repository.NewAdminActionRepository(db))

	repo := repository.NewAdminRepository(db)
	perRepo := repository.NewPermissionRepository(db)
	groupRepo := repository.NewGroupRepository(db)
	service := service.NewAdminService(repo, perRepo, groupRepo, actionService)
	handler := newAuthController(service)

	r.POST("/login", handler.loginAdmin)
	r.GET("/captcha", handler.generateCaptcha)
	r.POST("/captcha", handler.verifyCaptcha)
}

// @Summary (loginAdmin) Admin login
// @Description (loginAdmin) Admin login
// @Tags Auth
// @Accept json
// @Produce json
// @Param login body model.LoginAdmin true "Login"
// @Success 201 {object} model.LoginResponse
// @Failure 400 {object} handler.ErrorResponse
// @Failure 401 {object} handler.ErrorResponse
// @Failure 404 {object} handler.ErrorResponse
// @Router /login [post]
func (h authController) loginAdmin(c *gin.Context) {

	var req model.LoginAdmin
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	// req.IpAddress = helper.GetIPv4(c.ClientIP())
	var currentIp string
	if req.IpAddress != "" {
		currentIp = req.IpAddress
	} else {
		if ip := c.GetHeader("X-Real-IP"); ip != "" {
			currentIp = ip
		} else if ip = c.GetHeader("X-Forwarded-For"); ip != "" {
			currentIp = strings.Split(ip, ",")[0]
		} else {
			currentIp = c.ClientIP()
		}
	}
	// test, err := ipify.GetIp()
	// if err != nil {
	// 	fmt.Println(err)
	// }
	// fmt.Println("ipify.GetIp() ", test)
	// fmt.Println("c.GetHeader(X-Real-IP) ", c.GetHeader("X-Real-IP"))
	// fmt.Println("c.GetHeader(X-Forwarded-For) ", c.GetHeader("X-Forwarded-For"))
	// fmt.Println("c.ClientIP() ", c.ClientIP())

	if req.Agent == "" {
		// ใช้ไม่ได้ทุกเคส ต้องเอาจาก client side หน้าบ้าน
		req.Agent = c.Request.UserAgent()
	}

	req.Browser = helper.GetAgentBrowser(req.Agent)
	req.Device = "desktop"
	if helper.IsAgentmobile(req.Agent) {
		req.Device = "mobile"
	}

	var capchaReq model.CapchaVerifyRequest
	capchaReq.Id = req.CaptchaId
	capchaReq.VerifyValue = req.CaptchaValue
	resp, err := middleware.VerifyCaptcha(capchaReq)
	if err != nil || !resp.IsValid {
		var adminActionCreateBody model.AdminActionCreateRequest
		adminActionCreateBody.AdminId = 0
		adminActionCreateBody.TypeId = model.ADMIN_ACTION_LOGIN_FAILED
		adminActionCreateBody.RefObjectId = 0
		adminActionCreateBody.Detail = fmt.Sprintf("Username %s Captcha Verify failed: %s", req.Username, err)
		adminActionCreateBody.JsonInput = helper.StructJson(req)
		adminActionCreateBody.JsonOutput = helper.StructJson(struct {
			errors interface{}
		}{
			errors: err.Error(),
		})
		if _, err := h.adminService.CreateFailedAdminAction(adminActionCreateBody); err != nil {
			log.Println("loginAdmin.CreateFailedAdminAction", err)
		}
		HandleError(c, errors.New("INVALID_CAPTCHA"))
		return
	}

	req.IpAddress = currentIp
	response, err := h.adminService.LoginAdmin(req)
	if err != nil {
		var adminActionCreateBody model.AdminActionCreateRequest
		adminActionCreateBody.AdminId = 0
		adminActionCreateBody.TypeId = model.ADMIN_ACTION_LOGIN_FAILED
		adminActionCreateBody.RefObjectId = 0
		adminActionCreateBody.Detail = fmt.Sprintf("Username %s Login failed: %s", req.Username, err.Error())
		adminActionCreateBody.JsonInput = helper.StructJson(req)
		adminActionCreateBody.JsonOutput = helper.StructJson(struct {
			errors interface{}
		}{
			errors: err.Error(),
		})
		if _, err := h.adminService.CreateFailedAdminAction(adminActionCreateBody); err != nil {
			log.Println("loginAdmin.CreateFailedAdminAction", err)
		}
		HandleError(c, err)
		return
	}

	// c.JSON(201, model.LoginResponse{Token: *token, IsVerifyTotp: Totp.IsVerifyTotp, WebSettingTotp: Totp.WebSettingTotp, Id: Totp.Id})
	c.JSON(201, response)
}

// @Summary (generateCaptcha) Generate captcha for admin login
// @Description (generateCaptcha) Generate captcha
// @Tags Auth
// @Accept json
// @Produce json
// @Success 201 {object} model.CapchaResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /captcha [get]
func (h authController) generateCaptcha(c *gin.Context) {

	resp := middleware.GenerateCaptcha()
	c.JSON(201, resp)
}

// @Summary (verifyCaptcha) Verify captcha for admin login (TEST/DEBUG ONLY)
// @Description (verifyCaptcha) Verify captcha (TEST/DEBUG ONLY) ถ้าสำเร็จแล้วจะลบ captcha ออกจาก memory ให้สร้างใหม่
// @Tags Auth
// @Accept json
// @Produce json
// @Param body body model.CapchaVerifyRequest true "body"
// @Success 201 {object} model.CapchaVerifyResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /captcha [post]
func (h authController) verifyCaptcha(c *gin.Context) {

	var body model.CapchaVerifyRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}

	resp, err := middleware.VerifyCaptcha(body)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, resp)
}
