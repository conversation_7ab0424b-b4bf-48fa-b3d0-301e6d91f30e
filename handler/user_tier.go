package handler

import (
	"cybergame-api/middleware"
	"cybergame-api/model"
	"cybergame-api/repository"
	"cybergame-api/service"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"gorm.io/gorm"
)

type userTierController struct {
	userTierService service.UserTierService
}

func newUserTierController(
	userTierService service.UserTierService,
) userTierController {
	return userTierController{userTierService}
}

func UserTierController(r *gin.RouterGroup, db *gorm.DB) {

	handler := newUserTierController(service.NewUserTierService(repository.NewUserTierRepository(db)))
	role := middleware.Role(db)
	// SETTING
	managePopupRoute := r.Group("/usertier-setting", middleware.AuthorizeAdmin)
	managePopupRoute.GET("/userlist-setting", handler.getUserListTierSetting)
	managePopupRoute.GET("/deposit-list", role.CheckPermission([]string{"speciel_setting"}), handler.getUserTierSettingByDepositList)
	managePopupRoute.GET("/turnover-list", role.CheckPermission([]string{"speciel_setting"}), handler.getUserTierSettingByTurnOverList)
	managePopupRoute.PATCH("/deposit-update", role.CheckPermission([]string{"speciel_setting"}), handler.updateUserTierSettingByDeposit)
	managePopupRoute.PATCH("/turnover-update", role.CheckPermission([]string{"speciel_setting"}), handler.updateUserTierSettingByTurnOver)
	// TEST USER DATA
	managePopupRoute.POST("/increase-amount", role.CheckPermission([]string{"speciel_setting"}), handler.increaseUserTierAmount)
}

// @Summary (getUserListTierSetting)
// @Description (getUserListTierSetting)
// @Tags UserTiers - Manage Setting
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.UserListTierSettingResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /usertier-setting/userlist-setting [get]
func (h userTierController) getUserListTierSetting(c *gin.Context) {

	// ENV show Tier Setting todo
	// spSetting, err := s.PermRepo.GetTierSetting("use_speciel_setting")
	// if err != nil || spSetting.Id == 0 {
	// 	HandleError(c, err)
	// 	return
	// }

	data, err := h.userTierService.GetUserListTierSetting()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (getUserTierSettingByDepositList)
// @Description (getUserTierSettingByDepositList)
// @Tags UserTiers - Manage Setting
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.UserTierSettingResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /usertier-setting/deposit-list [get]
func (h userTierController) getUserTierSettingByDepositList(c *gin.Context) {

	// ENV show Tier Setting todo
	// spSetting, err := s.PermRepo.GetTierSetting("use_speciel_setting")
	// if err != nil || spSetting.Id == 0 {
	// 	HandleError(c, err)
	// 	return
	// }

	data, err := h.userTierService.GetUserTierSettingByDepositList()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (updateUserTierSettingByDeposit)
// @Description (updateUserTierSettingByDeposit)
// @Tags UserTiers - Manage Setting
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.UserTierByDepositSettingUpdateRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /usertier-setting/deposit-update [patch]
func (h userTierController) updateUserTierSettingByDeposit(c *gin.Context) {

	// ENV show Tier Setting todo
	// spSetting, err := s.PermRepo.GetTierSetting("use_speciel_setting")
	// if err != nil || spSetting.Id == 0 {
	// 	HandleError(c, err)
	// 	return
	// }

	body := model.UserTierByDepositSettingUpdateRequest{}
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.userTierService.UpdateUserTierSettingByDeposit(body); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "Updated success"})
}

// @Summary (getUserTierSettingByTurnOverList)
// @Description (getUserTierSettingByTurnOverList)
// @Tags UserTiers - Manage Setting
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.UserTierSettingResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /usertier-setting/turnover-list [get]
func (h userTierController) getUserTierSettingByTurnOverList(c *gin.Context) {

	// ENV show Tier Setting todo
	// spSetting, err := s.PermRepo.GetTierSetting("use_speciel_setting")
	// if err != nil || spSetting.Id == 0 {
	// 	HandleError(c, err)
	// 	return
	// }

	data, err := h.userTierService.GetUserTierSettingByTurnOverList()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (updateUserTierSettingByTurnOver)
// @Description (updateUserTierSettingByTurnOver)
// @Tags UserTiers - Manage Setting
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.UserTierByTurnOverSettingUpdateRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /usertier-setting/turnover-update [patch]
func (h userTierController) updateUserTierSettingByTurnOver(c *gin.Context) {

	// ENV show Tier Setting todo
	// spSetting, err := s.PermRepo.GetTierSetting("use_speciel_setting")
	// if err != nil || spSetting.Id == 0 {
	// 	HandleError(c, err)
	// 	return
	// }

	body := model.UserTierByTurnOverSettingUpdateRequest{}
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.userTierService.UpdateUserTierSettingByTurnOver(body); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "Updated success"})
}

// @Summary (increaseUserTierAmount)
// @Description (increaseUserTierAmount)
// @Tags UserTiers - Manage Setting
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.UserTierDataUpdateRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /usertier-setting/increase-amount [post]
func (h userTierController) increaseUserTierAmount(c *gin.Context) {

	// ENV show Tier Setting todo
	// spSetting, err := s.PermRepo.GetTierSetting("use_speciel_setting")
	// if err != nil || spSetting.Id == 0 {
	// 	HandleError(c, err)
	// 	return
	// }

	body := model.UserTierDataUpdateRequest{}
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	if body.DepositAmount > 0 {
		if err := h.userTierService.IncreaseUserTierDepositAmount(body.UserId, body.DepositAmount); err != nil {
			HandleError(c, err)
			return
		}
	} else if body.TurnOverAmount > 0 {
		if err := h.userTierService.IncreaseUserTierTurnoverAmount(body.UserId, body.TurnOverAmount); err != nil {
			HandleError(c, err)
			return
		}
	}
	c.JSON(200, model.Success{Message: "Updated success"})
}
