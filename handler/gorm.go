package handler

import (
	"cybergame-api/middleware"
	"cybergame-api/service"
	"errors"

	"cybergame-api/model"
	"cybergame-api/repository"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type gormController struct {
	gormService service.GormService
}

func newGormController(
	gormService service.GormService,
) gormController {
	return gormController{gormService}
}

func GormController(r *gin.RouterGroup, db *gorm.DB) {

	repo := repository.NewGormRepository(db)
	service := service.NewGormService(repo)
	handler := newGormController(service)

	// WebFlow-RD
	gormGormRoute := r.Group("/gorm", middleware.AuthorizeAdmin)
	gormGormRoute.GET("/table-schema", handler.getTableSchema)
	gormGormRoute.GET("/table-size", handler.getTableSize)
	// AutomateSetup
	gormSettingRoute := r.Group("/gorm-config", middleware.AuthorizeAdmin)
	gormSettingRoute.GET("/setting", handler.getGormSetting)
	gormSettingRoute.POST("/setting", handler.setGormSetting)
	// AutomateCheck
	gormCompareRoute := r.Group("/gorm-comp")
	gormCompareRoute.GET("/table-schema/:gkey", handler.getTableCompareSchema)
	gormCompareRoute.POST("/table-compare", handler.compareTableSchema)
}

// @Summary (getTableSchema) Get table schema
// @Description (getTableSchema) Get table schema
// @Tags Gorm - Schema
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} []model.GormTable
// @Failure 400 {object} handler.ErrorResponse
// @Router /gorm/table-schema [get]
func (h gormController) getTableSchema(c *gin.Context) {

	tables, err := h.gormService.GetTableSchema()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, tables)
}

// @Summary (getTableSize) Get table size
// @Description (getTableSize) Get table size
// @Tags Gorm - Schema
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} []model.GormTableSize
// @Failure 400 {object} handler.ErrorResponse
// @Router /gorm/table-size [get]
func (h gormController) getTableSize(c *gin.Context) {

	tables, err := h.gormService.GetTableSize()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, tables)
}

// @Summary (getGormSetting) Get GORM setting
// @Description (getGormSetting) Get GORM setting
// @Tags Gorm - Schema
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.GormSettingResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /gorm-config/setting [get]
func (h gormController) getGormSetting(c *gin.Context) {

	setting, err := h.gormService.GetGormSetting()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, setting)
}

// @Summary (setGormSetting) Set GORM setting
// @Description (setGormSetting) Set GORM setting
// @Tags Gorm - Schema
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param request body model.GormSettingUpdateRequest true "GORM setting"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /gorm-config/setting [post]
func (h gormController) setGormSetting(c *gin.Context) {

	var setting model.GormSettingUpdateRequest
	// Bind JSON to struct
	if err := c.ShouldBindJSON(&setting); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.gormService.SetGormSetting(setting); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "Update success"})
}

// @Summary (getTableCompareSchema) Get table schema -- Called by Automate checker
// @Description (getTableCompareSchema) Get table schema
// @Tags Gorm - Schema
// @Accept json
// @Produce json
// @Param gkey path string true "GKEY"
// @Success 200 {object} []model.GormTable
// @Failure 400 {object} handler.ErrorResponse
// @Router /gorm-comp/table-schema/{gkey} [get]
func (h gormController) getTableCompareSchema(c *gin.Context) {

	gkey := c.Param("gkey")
	if gkey == "" {
		HandleError(c, errors.New("gkey is required"))
		return
	}

	tables, err := h.gormService.GetTableSchema()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, tables)
}

// @Summary (compareTableSchema) Manual Compare table schema
// @Description (compareTableSchema) Manual Compare table schema
// @Tags Gorm - Schema
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param request body model.GormTableCompareRequest true "GORM req"
// @Success 200 {object} model.GormTable
// @Failure 400 {object} handler.ErrorResponse
// @Router /gorm-comp/table-compare [post]
func (h gormController) compareTableSchema(c *gin.Context) {

	var req model.GormTableCompareRequest
	// Bind JSON to struct
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	result, err := h.gormService.CompareTableSchema(req)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, result)
}
