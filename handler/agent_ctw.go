package handler

import (
	"cybergame-api/helper"
	"cybergame-api/middleware"
	"cybergame-api/model"
	"cybergame-api/repository"
	"cybergame-api/service"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type agentCtwController struct {
	agentCtwService service.AgentCtwService
}

func newAgentCtwController(
	agentCtwService service.AgentCtwService,
) agentCtwController {
	return agentCtwController{agentCtwService}
}

func AgentCtwController(r *gin.RouterGroup, db *gorm.DB) {

	repoGame := repository.NewAgentInfoRepository(db)
	serviceGame := service.NewGameService(repoGame)

	repo := repository.NewAgentCtwRepository(db)
	service := service.NewAgentCtwService(repo, db, serviceGame)
	handler := newAgentCtwController(service)

	admin := r.Group("/admin", middleware.AuthorizeAdmin)
	admin.GET("/agent-ctw-setting", handler.GetAgentCtwSetting)
	admin.PUT("/agent-ctw-setting", handler.UpdateAgentCtwSetting)
	admin.GET("/agent-ctw-setting/game-list", handler.GetAgentCtwGameList)

	user := r.Group("/user", middleware.AuthorizeUser)
	user.GET("/agent-ctw/game-list", handler.WebGetAgentCtwGameList)
	user.POST("/agent-ctw/play", handler.CallApiAgentCtwLaunch)

	callback := r.Group("/play/ctw")
	callback.POST("/Cash/Get", handler.CallBackAgentCtwCheckBalance)
	callback.POST("/Cash/TransferInOut", handler.CallBackCtwTranferInOut)

	test := r.Group("/test")
	test.POST("/ctw/bet-detail", handler.CallApiAgentCtwBetDetail)

}

// @Summary Get Agent Ctw Setting
// @Description Get Agent Ctw Setting
// @Tags Admin Agent CTW
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} model.GetInternalAgentCtwSettingResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /admin/agent-ctw-setting [get]
func (h agentCtwController) GetAgentCtwSetting(c *gin.Context) {

	data, err := h.agentCtwService.GetAgentCtwSetting()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary Update Agent Ctw Setting
// @Description Update Agent Ctw Setting
// @Tags Admin Agent CTW
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param body body model.UpdateAgentCtwSetting true "body"
// @Success 200
// @Failure 400 {object} handler.ErrorResponse
// @Router /admin/agent-ctw-setting [put]
func (h agentCtwController) UpdateAgentCtwSetting(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	var reqBody model.UpdateAgentCtwSetting
	if err := c.ShouldBindJSON(&reqBody); err != nil {
		HandleError(c, err)
		return
	}

	reqBody.UpdatedById = adminId
	reqBody.UpdatedAt = time.Now().UTC()
	err := h.agentCtwService.UpdateAgentCtwSetting(reqBody)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, model.Success{Message: "UPDATE SUCCESS"})
}

// @Summary Get Agent Ctw Game List
// @Description Get Agent Ctw Game List
// @Tags Admin Agent CTW
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} model.GetCtwGameListResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /admin/agent-ctw-setting/game-list [get]
func (h agentCtwController) GetAgentCtwGameList(c *gin.Context) {

	data, err := h.agentCtwService.GetAgentCtwGameList()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary Get Agent Ctw Game List
// @Description Get Agent Ctw Game List
// @Tags Web Agent CTW
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param _ query model.WebGetAgentCtwGameList true "query"
// @Success 200 {object} model.GetCtwGameListResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /user/agent-ctw/game-list [get]
func (h agentCtwController) WebGetAgentCtwGameList(c *gin.Context) {

	var req model.WebGetAgentCtwGameList
	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.agentCtwService.WebGetAgentCtwGameList(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary Call Api Agent Ctw Launch
// @Description Call Api Agent Ctw Launch
// @Tags Web Agent CTW
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param body body model.CallApiAgentCtwLaunch true "body"
// @Success 200 {object} model.CallApiAgentCtwLaunchResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /user/agent-ctw/play [post]
func (h agentCtwController) CallApiAgentCtwLaunch(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var req model.CallApiAgentCtwLaunch
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	req.UserId = userId
	data, err := h.agentCtwService.CallApiAgentCtwLaunch(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)

}

func (h agentCtwController) CallBackAgentCtwCheckBalance(c *gin.Context) {
	// @Summary Call Back Agent Ctw Check Balance
	// @Description Call Back Agent Ctw Check Balance
	// @Tags Agent CTW
	// @Accept json
	// @Produce json
	// @Security BearerAuth
	// @Param Body body model.CallBackAgentCtwCheckBalanceRequest true "CallBack Agent Ctw Check Balance"
	// @Success 200 {object} model.CallBackAgentCtwCheckBalanceResponse
	// @Failure 400 {object} handler.ErrorResponse
	// @Router /play/ctw/Cash/Get [post]

	var reqBody model.CallBackAgentCtwCheckBalanceRequest
	if err := c.ShouldBindJSON(&reqBody); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.agentCtwService.CallBackAgentCtwCheckBalance(reqBody)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

func (h agentCtwController) CallBackCtwTranferInOut(c *gin.Context) {
	// CallBackCtwTranferInOut(reqBody model.CallBackCtwTranferInOutRequest) (*model.CallBackCtwTranferInOutResponse, error)
	// @Summary Call Back Ctw Tranfer In Out
	// @Description Call Back Ctw Tranfer In Out
	// @Tags Agent CTW
	// @Accept json
	// @Produce json
	// @Security BearerAuth
	// @Param Body body model.CallBackCtwTranferInOutRequest true "CallBack Ctw Tranfer In Out"
	// @Success 200 {object} model.CallBackCtwTranferInOutResponse
	// @Failure 400 {object} handler.ErrorResponse
	// @Router /play/ctw/Cash/TransferInOut [post]

	var reqBody model.CallBackCtwTranferInOutRequest
	if err := c.ShouldBindJSON(&reqBody); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.agentCtwService.CallBackCtwTranferInOutV2(reqBody)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)

	// RETURN IMMEDIATELY IF USE V1 will need to open this code
	// c.Request.Context().Done()

	// // CONTINUE PROCESSING
	// go func(req model.CallBackCtwTranferInOutRequest) {
	// 	getCtwBetDetail, err := h.agentCtwService.GetCtwBetDetail(req)
	// 	if err != nil {
	// 		fmt.Println("Error fetching CTW bet details:", err)
	// 		return
	// 	}
	// 	fmt.Println("RES getCtwBetDetail ROUND", getCtwBetDetail.Data.RoundId, helper.StructJson(getCtwBetDetail))
	// }(reqBody)
}

func (h agentCtwController) CallApiAgentCtwBetDetail(c *gin.Context) {
	// @Summary Call Api Agent Ctw Bet Detail
	// @Description Call Api Agent Ctw Bet Detail
	// @Tags Agent CTW
	// @Accept json
	// @Produce json
	// @Security BearerAuth
	// @Param Body body model.CallBackCtwTranferInOutRequest true "Call Api Agent Ctw Bet Detail"
	// @Success 200 {object} model.CallBackCtwGameBetDetail
	// @Failure 400 {object} handler.ErrorResponse
	// @Router /test/ctw/bet-detail [post]

	var reqBody model.CallBackCtwTranferInOutRequest
	if err := c.ShouldBindJSON(&reqBody); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.agentCtwService.GetCtwBetDetail(reqBody)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)

}
