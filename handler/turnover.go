package handler

import (
	"cybergame-api/helper"
	"cybergame-api/middleware"
	"cybergame-api/model"
	"cybergame-api/repository"
	"cybergame-api/service"
	"errors"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"gorm.io/gorm"
)

type turnoverController struct {
	turnoverService service.TurnoverService
}

func newTurnoverController(
	turnoverService service.TurnoverService,
) turnoverController {
	return turnoverController{turnoverService}
}

func TurnoverController(r *gin.RouterGroup, db *gorm.DB) {

	singleSession := middleware.SingleSession(db)

	repo := repository.NewTurnoverRepository(db)
	service := service.NewTurnoverService(repo)
	handler := newTurnoverController(service)

	turnoverOptionRoute := r.Group("/turnover", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	turnoverOptionRoute.GET("/list", handler.getUserTurnoverList)
	turnoverOptionRoute.POST("/create", handler.createUserTurnover)
	turnoverOptionRoute.POST("/decrease", handler.decreaseUserTurnover)
	turnoverOptionRoute.POST("/agent", handler.getExternalUserTurnover)
	turnoverOptionRoute.GET("/check/:id", handler.checkUserTurnover)
	turnoverOptionRoute.POST("/withdraw", handler.setWithdrawTurnover)
	turnoverOptionRoute.GET("/check-turn-success-on-this-day", handler.checkTurnSuccessOnThisDay)

	turnoverSettingRoute := r.Group("/turnover", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	turnoverSettingRoute.GET("/setting", handler.getTurnoverSetting)
	turnoverSettingRoute.POST("/setting", handler.setTurnoverSetting)

	turnoverUserRoute := r.Group("/turnover", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	turnoverUserRoute.GET("/user-pending-list", handler.UserTurnOverPendingList)
	turnoverUserRoute.GET("/pending-list", handler.TurnOverPendingList)

	// /user/turnover/check-available-game
	userTurnoverRoute := r.Group("/user/turnover", middleware.AuthorizeUser)
	userTurnoverRoute.POST("/check-available-game", handler.avaliableGamePlayCheckTurnOver)

}

// @Summary (getUserTurnoverList) ดึงข้อมูลรายการติดเทินของผู้ใช้
// @Description (getUserTurnoverList) ดึงข้อมูลรายการติดเทินของผู้ใช้
// @Tags Turnover - Debug SUPERADMIN_ONLY
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.TurnoverUserStatementListRequest true "TurnoverUserStatementListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /turnover/list [get]
func (h turnoverController) getUserTurnoverList(c *gin.Context) {

	var query model.TurnoverUserStatementListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.turnoverService.GetUserTurnoverList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (getExternalUserTurnover)
// @Description (getExternalUserTurnover)
// @Tags Turnover - Debug SUPERADMIN_ONLY
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.TurnoverUserCurrentAmountRequest true "body"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /turnover/agent [post]
func (h turnoverController) getExternalUserTurnover(c *gin.Context) {

	var query model.TurnoverUserCurrentAmountRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	err := h.turnoverService.GetExternalUserTurnover(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "Created success"})
}

// @Summary (createUserTurnover) เพิ่มรายการติดเทินของผู้ใช้
// @Description (createUserTurnover) เพิ่มรายการติดเทินของผู้ใช้
// @Tags Turnover - Debug SUPERADMIN_ONLY
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.TurnoverUserCreateRequest true "body"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /turnover/create [post]
func (h turnoverController) createUserTurnover(c *gin.Context) {

	var req model.TurnoverUserCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	if _, err := h.turnoverService.CreateUserTurnover(req); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "Created success"})
}

// @Summary (checkUserTurnover)
// @Description (checkUserTurnover)
// @Tags Turnover - Debug SUPERADMIN_ONLY
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 200 {object} model.TurnoverUserCurrentResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /turnover/check/{id} [get]
func (h turnoverController) checkUserTurnover(c *gin.Context) {

	var req model.GetByIdRequest
	if err := c.ShouldBindUri(&req); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.turnoverService.CheckUserTurnover(req.Id)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (decreaseUserTurnover) ลดรายการติดเทินของผู้ใช้ SUPERADMIN_ONLY
// @Description (decreaseUserTurnover) ลดรายการติดเทินของผู้ใช้
// @Tags Turnover - Debug SUPERADMIN_ONLY
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.TurnoverUserDecreaseRequest true "body"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /turnover/decrease [post]
func (h turnoverController) decreaseUserTurnover(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))
	if adminId != 1 {
		HandleError(c, errors.New("UNAUTHORIZED"))
		return
	}

	var req model.TurnoverUserDecreaseRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.turnoverService.DecreaseUserTurnover(req); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "Created success"})
}

// @Summary (setWithdrawTurnover) เมื่อถอนเงิน SUPERADMIN_ONLY
// @Description (setWithdrawTurnover) เมื่อถอนเงิน
// @Tags Turnover - Debug SUPERADMIN_ONLY
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.TurnoverUserWithdrawRequest true "body"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /turnover/withdraw [post]
func (h turnoverController) setWithdrawTurnover(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))
	if adminId != 1 {
		HandleError(c, errors.New("UNAUTHORIZED"))
		return
	}

	var req model.TurnoverUserWithdrawRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.turnoverService.SetWithdrawTurnover(req); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "Created success"})
}

// @Summary (CheckTurnSuccessOnThisDay) ตรวจสอบการเทินวันนี้ (สำเร็จ)
// @Description (CheckTurnSuccessOnThisDay) ตรวจสอบการเทินวันนี้ (สำเร็จ)
// @Tags Turnover - Debug SUPERADMIN_ONLY
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.CheckTurnSuccessOnThisDayRequest true "CheckTurnSuccessOnThisDayRequest"
// @Success 200 {object} model.CheckTurnSuccessOnThisDayResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /turnover/check-turn-success-on-this-day [get]
func (h turnoverController) checkTurnSuccessOnThisDay(c *gin.Context) {

	var req model.CheckTurnSuccessOnThisDayRequest
	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(req); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.turnoverService.CheckTurnSuccessOnThisDay(req)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (getTurnoverSetting) จัดการการตลาด จัดการแนะนำเพื่อน ดึงข้อมูลการตั้งค่าเทิน
// @Description (getTurnoverSetting) Get Turnover Percent Configuration
// @Tags Configuration - Turnover
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.TurnoverSettingResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /turnover/setting [get]
func (h turnoverController) getTurnoverSetting(c *gin.Context) {

	configuration, err := h.turnoverService.GetTurnoverSetting()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, configuration)
}

// @Summary (setTurnoverSetting) จัดการการตลาด จัดการแนะนำเพื่อน อัพเดทการตั้งค่าเทิน
// @Description (setTurnoverSetting) Update Turnover Percent Configuration
// @Tags Configuration - Turnover
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.TurnoverSettingUpdateRequest true "body"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /turnover/setting [post]
func (h turnoverController) setTurnoverSetting(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	var body model.TurnoverSettingUpdateRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	body.UpdateBy = adminId

	if err := h.turnoverService.SetTurnoverSetting(body); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "Update Success"})
}

// @Summary (UserTurnOverPendingList) ดึงข้อมูลรายการติดเทินของผู้ใช้
// @Description (UserTurnOverPendingList) ดึงข้อมูลรายการติดเทินของผู้ใช้
// @Tags Turnover
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.GetUserTurnOverStartmentListRequest true "GetUserTurnOverStartmentListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /turnover/user-pending-list [get]
func (h turnoverController) UserTurnOverPendingList(c *gin.Context) {

	var query model.GetUserTurnOverStartmentListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.turnoverService.UserTurnOverPendingList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (TurnOverPendingList) ดึงข้อมูลรายการติดเทินของผู้ใช้
// @Description (TurnOverPendingList) ดึงข้อมูลรายการติดเทินของผู้ใช้
// @Tags Turnover
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.GetTurnOverStartmentListRequest true "GetTurnOverStartmentListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /turnover/pending-list [get]
func (h turnoverController) TurnOverPendingList(c *gin.Context) {

	var query model.GetTurnOverStartmentListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.turnoverService.TurnOverPendingList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// AvaliableGamePlayCheckTurnOver(req model.AvaliableGamePlayCheckTurnOverRequest) error
// @Summary AvaliableGamePlayCheckTurnOver ตรวจสอบการเล่นเกม
// @Description AvaliableGamePlayCheckTurnOver ตรวจสอบการเล่นเกม
// @Tags User - Play Turn Game
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.AvaliableGamePlayCheckTurnOverRequest true "body"
// @Failure 400 {object} handler.ErrorResponse
// @Router /user/turnover/check-available-game [post]
func (h turnoverController) avaliableGamePlayCheckTurnOver(c *gin.Context) {

	var req model.AvaliableGamePlayCheckTurnOverRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))
	req.UserId = userId
	err := h.turnoverService.AvaliableGamePlayCheckTurnOver(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, nil)
}
