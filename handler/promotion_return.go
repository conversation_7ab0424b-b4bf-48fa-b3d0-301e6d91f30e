package handler

import (
	"cybergame-api/helper"
	"cybergame-api/middleware"
	"cybergame-api/model"
	"cybergame-api/repository"
	"cybergame-api/service"
	"log"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"gorm.io/gorm"
)

type promotionReturnController struct {
	promotionReturnService     service.PromotionReturnService
	promotionReturnTurnService service.PromotionReturnTurnService
}

func newPromotionReturnController(
	promotionReturnService service.PromotionReturnService,
	promotionReturnTurnService service.PromotionReturnTurnService,
) promotionReturnController {
	return promotionReturnController{promotionReturnService, promotionReturnTurnService}
}
func PromotionReturnController(r *gin.RouterGroup, db *gorm.DB) {

	activityLusckyWheelRepo := repository.NewActivityLuckyWheelRepository(db)
	serviceRepo := repository.NewNotificationRepository(db)
	serviceNoti := service.NewNotificationService(serviceRepo)
	activityLuckyWheelService := service.NewActivityLuckyWheelService(activityLusckyWheelRepo, db, serviceNoti)
	serviceReturnLoss := service.NewPromotionReturnService(repository.NewPromotionReturnRepository(db), db, activityLuckyWheelService, serviceNoti)
	serviceReturnTurn := service.NewPromotionReturnTurnService(repository.NewPromotionReturnTurnRepository(db), db, activityLuckyWheelService, serviceNoti)
	handler := newPromotionReturnController(serviceReturnLoss, serviceReturnTurn)

	role := middleware.Role(db)
	singleSession := middleware.SingleSession(db)

	customerRoute := r.Group("/customer-promotion", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	customerRoute.GET("/list", handler.getUserTurnoverStatementList)
	customerRoute.POST("/cancel", handler.cancelUserTurnoverStatement)

	settingRoute := r.Group("/promotion-return/setting", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	settingRoute.GET("", role.CheckPermission([]string{"activity_manage"}), handler.getReturnSetting)
	settingRoute.PATCH("", role.CheckPermission([]string{"activity_manage"}), handler.updateReturnSetting)

	historyRoute := r.Group("/promotion-return/history", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	historyRoute.GET("/user-list", handler.getReturnHistoryUserList)
	historyRoute.GET("/user-summary", handler.getReturnHistoryUserSummary)
	historyRoute.GET("/log-list", handler.getReturnHistoryLogList)

	webOpenRoute := r.Group("/web/promotions/open-list", middleware.AuthorizeUser, singleSession.SingleUserSession())
	webOpenRoute.GET("promotion-list", handler.getPromotionOpenList)

	webRoute := r.Group("/web/promotion-return-loser", middleware.AuthorizeUser, singleSession.SingleUserSession())
	webRoute.GET("/current", handler.getUserCurrentReturnDetail)
	webRoute.POST("/take", handler.takeUserReturnAmount)
	webRoute.GET("/list", handler.getUserReturnHistoryList)

	cronRoute := r.Group("/cron/promotion-return-loser")
	cronRoute.GET("/cut-daily", handler.cronCutReturnLossDaily)
	cronRoute.GET("/cut-date", middleware.AuthorizeAdmin, singleSession.SingleAdminSession(), handler.cronCutOfDate)

}

// @Summary (getUserTurnoverStatementList) โปรโมชั่นลูกค้า ดึงข้อมูลรายการติดเทิร์น ของผู้เล่นทั้งหมด
// @Description (getUserTurnoverStatementList) ดึงข้อมูลรายการติดเทิร์น ของผู้เล่นทั้งหมด
// @Description
// @Description startTurnAmount คือ จำนวนที่ติดเทินทั้งหมด totalTurnAmount คือ จำนวนคงเหลือ
// @Description
// @Description | StatusId | Description |
// @Description | ---------------- | ------------|
// @Description | 1 | PENDING รอผ่านเงื่อนไข |
// @Description | 2 | CANCELED ถูกยกเลิกแล้ว |
// @Description | 3 | COMPLETED ผ่านเทินโอเวอร์แล้ว |
// @Description | 4 | EXPIRED หมดอายุ |
// @Description | ---------------- | ------------|
// @Description
// @Tags Promotion - Customer Promotion / Turn over
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.UserTurnoverStatementListRequest true "UserTurnoverStatementListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /customer-promotion/list [get]
func (h promotionReturnController) getUserTurnoverStatementList(c *gin.Context) {

	var query model.UserTurnoverStatementListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	pagingList, err := h.promotionReturnService.GetUserTurnoverStatementList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, pagingList)
}

// @Summary (cancelUserTurnoverStatement) โปรโมชั่นลูกค้า ลบโปรโมชั่นลูกค้า ยกเลิกการติดเทินรายการนี้
// @Description (cancelUserTurnoverStatement) ลบโปรโมชั่นลูกค้า ยกเลิกการติดเทินรายการนี้
// @Tags Promotion - Customer Promotion / Turn over
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.AdminCancelTurnoverStatementRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /customer-promotion/cancel [post]
func (h promotionReturnController) cancelUserTurnoverStatement(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	var body model.AdminCancelTurnoverStatementRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}
	body.AdminId = adminId

	if err := h.promotionReturnService.CancelUserTurnoverStatement(body); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Updated success"})
}

// @Summary (getPromotionOpenList) ดึงข้อมูลโปรโมชั่นที่เปิดให้เล่น
// @Description (getPromotionOpenList) ดึงข้อมูลโปรโมชั่นที่เปิดให้เล่น
// @Tags Web - User Promotion
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.GetActivityMenuRequest true "GetActivityMenuRequest"
// @Success 200 {object} []model.PromotionOpenList
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/promotions/open-list/promotion-list [get]
func (h promotionReturnController) getPromotionOpenList(c *gin.Context) {

	var req model.GetActivityMenuRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.promotionReturnService.GetPromotionOpenList(req)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (getReturnSetting) ดึงข้อมูลการตั้งค่าโปรโมชั่นคืนยอดเสีย
// @Description (getReturnSetting) ดึงข้อมูลการตั้งค่าโปรโมชั่นคืนยอดเสีย
// @Tags Promotion - Return Setting
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.PromotionReturnSettingResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /promotion-return/setting [get]
func (h promotionReturnController) getReturnSetting(c *gin.Context) {

	data, err := h.promotionReturnService.GetReturnSetting()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (updateReturnSetting) อัพเดทการตั้งค่าโปรโมชั่นคืนยอดเสีย
// @Description (updateReturnSetting) อัพเดทการตั้งค่าโปรโมชั่นคืนยอดเสีย
// @Tags Promotion - Return Setting
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.PromotionReturnSettingUpdateRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /promotion-return/setting [patch]
func (h promotionReturnController) updateReturnSetting(c *gin.Context) {

	body := model.PromotionReturnSettingUpdateRequest{}
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	body.UpdatedById = adminId

	if err := h.promotionReturnService.UpdateReturnSetting(body); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Updated success"})
}

// @Summary (getReturnHistoryUserList) รายงานการตลาด > รายงานคืนยอดเสีย ดึงข้อมูลรายชื่อผู้เล่น ที่มีประวัติการคืนยอดเสีย
// @Description (getReturnHistoryUserList) ดึงข้อมูลรายชื่อผู้เล่น ที่มีประวัติการคืนยอดเสีย
// @Description # การกรองด้วยประเภทวัน dateType ทั้งหมด วันนี้ เมื่อวาน เดือนนี้ **ถ้าส่ง fromDate, toDate จะใช้ fromDate, toDate ก่อน
// @Description | dateType | Description |
// @Description | ---------------- | ------------|
// @Description | all *หรือไม่ส่ง* | แสดงทั้งหมด |
// @Description | today | แสดงวันนี้ |
// @Description | yesterday | แสดงเมื่อวาน |
// @Description | last_week | แสดง 7 วันย้อนหลัง |
// @Description | this_month | แสดงเดือนนี้ 1-31 |
// @Description | last_month | แสดง 30 วันย้อนหลัง |
// @Tags Promotion - Return Loss Amount Report
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.PromotionReturnHistoryUserListRequest true "PromotionReturnHistoryUserListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /promotion-return/history/user-list [get]
func (h promotionReturnController) getReturnHistoryUserList(c *gin.Context) {

	var query model.PromotionReturnHistoryUserListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	pagingList, err := h.promotionReturnService.GetReturnHistoryUserList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, pagingList)
}

// @Summary (getReturnHistoryUserSummary) รายงานการตลาด > รายงานคืนยอดเสีย ดึงข้อมูล การ์ดยอดรวม
// @Description (getReturnHistoryUserSummary) ดึงข้อมูล การ์ดยอดรวม ยอดรวมคืนยอดเสีย (ติด - หมายถึงลูกค้าแพ้, ไม่ติดลบ หมายถึงลูกค้าชนะ) และ ยอดรวมรายได้ที่กดรับ
// @Description # การกรองด้วยประเภทวัน dateType ทั้งหมด วันนี้ เมื่อวาน เดือนนี้ **ถ้าส่ง fromDate, toDate จะใช้ fromDate, toDate ก่อน
// @Description | dateType | Description |
// @Description | ---------------- | ------------|
// @Description | all *หรือไม่ส่ง* | แสดงทั้งหมด |
// @Description | today | แสดงวันนี้ |
// @Description | yesterday | แสดงเมื่อวาน |
// @Description | last_week | แสดง 7 วันย้อนหลัง |
// @Description | this_month | แสดงเดือนนี้ 1-31 |
// @Description | last_month | แสดง 30 วันย้อนหลัง |
// @Tags Promotion - Return Loss Amount Report
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.PromotionReturnHistoryUserListRequest true "PromotionReturnHistoryUserListRequest"
// @Success 200 {object} model.PromotionReturnHistoryUserSummaryResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /promotion-return/history/user-summary [get]
func (h promotionReturnController) getReturnHistoryUserSummary(c *gin.Context) {

	var query model.PromotionReturnHistoryUserListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.promotionReturnService.GetReturnHistoryUserSummary(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (getReturnHistoryLogList) รายงานการตลาด > รายงานคืนยอดเสีย ดึงข้อมูลประวัติการคืนยอดเสีย ของผู้เล่น 1 คน
// @Description (getReturnHistoryLogList) ดึงข้อมูลประวัติการคืนยอดเสีย ของผู้เล่น 1 คน
// @Description
// @Description | LogStatus | Description |
// @Description | ---------------- | ------------|
// @Description | READY | รอกดรับ |
// @Description | TAKEN | รับแล้ว |
// @Description | EXPIRED | หมดอายุ |
// @Description | ---------------- | ------------|
// @Description
// @Tags Promotion - Return Loss Amount Report
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.PromotionReturnHistoryListRequest true "PromotionReturnHistoryListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /promotion-return/history/log-list [get]
func (h promotionReturnController) getReturnHistoryLogList(c *gin.Context) {

	var query model.PromotionReturnHistoryListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	pagingList, err := h.promotionReturnService.GetReturnHistoryLogList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, pagingList)
}

// @Summary (getUserCurrentReturnDetail) ผู้ใช้ดูยอดคืนเงินปัจจุบันของตัวเอง
// @Description (getUserCurrentReturnDetail) ผู้ใช้ดูยอดคืนเงินปัจจุบันของตัวเอง
// @Tags Web - User Promotion Return Loss Amount
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.PromotionReturnUserDetail
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/promotion-return-loser/current [get]
func (h promotionReturnController) getUserCurrentReturnDetail(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	data, err := h.promotionReturnService.GetUserCurrentReturnDetail(userId)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (takeUserReturnAmount) ผู้ใช้โยกเงินเข้าบัญชีเครดิคตัวเอง
// @Description (takeUserReturnAmount) ผู้ใช้โยกเงินเข้าบัญชีเครดิคตัวเอง
// @Tags Web - User Promotion Return Loss Amount
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/promotion-return-loser/take [post]
func (h promotionReturnController) takeUserReturnAmount(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	if err := h.promotionReturnService.TakeUserReturnAmount(userId); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Updated success"})
}

// @Summary (getUserReturnHistoryList) ประวัติการโยกเงินเข้าบัญชีเครดิคตัวเอง
// @Description (getUserReturnHistoryList) ประวัติการโยกเงินเข้าบัญชีเครดิคตัวเอง
// @Tags Web - User Promotion Return Loss Amount
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.PromotionReturnTransactionListRequest true "PromotionReturnTransactionListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/promotion-return-loser/list [get]
func (h promotionReturnController) getUserReturnHistoryList(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var query model.PromotionReturnTransactionListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}
	query.UserId = userId

	data, err := h.promotionReturnService.GetUserReturnHistoryList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithPagination{List: data.List, Total: data.Total})
}

// @Summary (cronCutReturnLossDaily) ระบบ สั่งตัดยอดประจำวัน คืนยอดเสีย และ คืนยอดเทิร์น
// @Description (cronCutReturnLossDaily) ระบบ สั่งตัดยอดประจำวัน
// @Tags Promotion - Return Setting
// @Accept json
// @Produce json
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /cron/promotion-return-loser/cut-daily [get]
func (h promotionReturnController) cronCutReturnLossDaily(c *gin.Context) {

	// คืนยอดเสีย และ คืนยอดเทิร์น
	if err := h.promotionReturnService.CronCutReturnLossDaily(); err != nil {
		log.Println("CronCutReturnLossDaily", err)
	}
	if err := h.promotionReturnTurnService.CronCutReturnTurnDaily(); err != nil {
		log.Println("CronCutReturnTurnDaily", err)
	}
	c.JSON(201, model.Success{Message: "Updated success"})
}

// @Summary (cronCutOfDate) ระบบ สั่งตัดยอดของวันที่กำหนด ทำงานซ้ำได้ ถ้าคำนวนจบรอบ คืนยอดเสีย และ คืนยอดเทิร์น
// @Description (cronCutOfDate) ระบบ สั่งตัดยอดของวันที่กำหนด ทำงานซ้ำได้ ถ้าคำนวนจบรอบ
// @Tags Promotion - Return Setting
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.PromotionReturnCronRequest true "PromotionReturnCronRequest"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /cron/promotion-return-loser/cut-date [get]
func (h promotionReturnController) cronCutOfDate(c *gin.Context) {

	var query model.PromotionReturnCronRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	// คืนยอดเสีย และ คืนยอดเทิร์น
	if err := h.promotionReturnService.CronCutReturnLossByDate(query.OfDate); err != nil {
		log.Println("CronCutReturnLossDaily", err)
	}
	if err := h.promotionReturnTurnService.CronCutReturnTurnByDate(query.OfDate); err != nil {
		log.Println("CronCutReturnTurnDaily", err)
	}
	c.JSON(201, model.Success{Message: "Updated success"})
}
