package handler

import (
	"cybergame-api/helper"
	"cybergame-api/middleware"
	"cybergame-api/model"
	"cybergame-api/repository"
	"cybergame-api/service"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type agentCblottoController struct {
	agentCblottoService service.AgentCblottoService
}

func newAgentCblottoController(
	agentCblottoService service.AgentCblottoService,
) agentCblottoController {
	return agentCblottoController{agentCblottoService}
}

func AgentCblottoController(r *gin.RouterGroup, db *gorm.DB) {

	repoGame := repository.NewAgentInfoRepository(db)
	serviceGame := service.NewGameService(repoGame)

	repo := repository.NewAgentCblottoRepository(db)
	service := service.NewAgentCblottoService(repo, db, serviceGame)
	handler := newAgentCblottoController(service)

	admin := r.Group("/admin", middleware.AuthorizeAdmin)
	admin.GET("/agent-cblotto-setting", handler.GetAgentCblottoSetting)
	admin.PUT("/agent-cblotto-setting", handler.UpdateAgentCblottoSetting)

	user := r.Group("/user", middleware.AuthorizeUser)
	user.POST("/agent-cblotto/play", handler.CallApiAgentCblottoLaunch)

	callback := r.Group("/play/cblotto")
	callback.POST("/check-balance", handler.CallBackAgentCblottoCheckBalance)
	callback.POST("/transaction", handler.CallBackAgentCblottoTransaction)

}

// @Summary Get Agent Cblotto Setting
// @Description Get Agent Cblotto Setting
// @Tags Admin Agent Cblotto
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} model.GetInternalAgentCblottoSettingResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /admin/agent-cblotto-setting [get]
func (h agentCblottoController) GetAgentCblottoSetting(c *gin.Context) {

	data, err := h.agentCblottoService.GetAgentCblottoSetting()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary Update Agent Cblotto Setting
// @Description Update Agent Cblotto Setting
// @Tags Admin Agent Cblotto
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param body body model.UpdateAgentCblottoSetting true "body"
// @Success 200
// @Failure 400 {object} handler.ErrorResponse
// @Router /admin/agent-cblotto-setting [put]
func (h agentCblottoController) UpdateAgentCblottoSetting(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	var reqBody model.UpdateAgentCblottoSetting
	if err := c.ShouldBindJSON(&reqBody); err != nil {
		HandleError(c, err)
		return
	}

	reqBody.UpdatedById = adminId
	reqBody.UpdatedAt = time.Now().UTC()
	err := h.agentCblottoService.UpdateAgentCblottoSetting(reqBody)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, model.Success{Message: "UPDATE SUCCESS"})
}

// @Summary Call Api Agent Cblotto Launch
// @Description Call Api Agent Cblotto Launch
// @Tags User Agent Cblotto
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} model.AgentCblottoUserLoginGameResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /user/agent-cblotto/play [post]
func (h agentCblottoController) CallApiAgentCblottoLaunch(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var reqBody model.CallApiAgentCblottoLaunch
	reqBody.UserId = userId

	data, err := h.agentCblottoService.CallApiAgentCblottoLaunch(reqBody)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary Call Back Agent Cblotto Check Balance
// @Description Call Back Agent Cblotto Check Balance
// @Tags Callback Agent Cblotto
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param Body body model.CallBackAgentCblottoCheckBalanceRequest true "CallBack Agent Cblotto Check Balance"
// @Success 200 {object} model.CallBackAgentCblottoCheckBalanceResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /play/cblotto/check-balance [post]
func (h agentCblottoController) CallBackAgentCblottoCheckBalance(c *gin.Context) {

	headerKey := c.GetHeader("Authorization")
	var reqBody model.CallBackAgentCblottoCheckBalanceRequest
	if err := c.ShouldBindJSON(&reqBody); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.agentCblottoService.CallBackAgentCblottoCheckBalance(reqBody, headerKey)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary Call Back Agent Cblotto Transaction
// @Description Call Back Agent Cblotto Transaction
// @Tags Callback Agent Cblotto
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param Body body model.CallBackAgentCblottoTransaction true "CallBack Agent Cblotto Transaction"
// @Success 200 {object} model.CallBackAgentCblottoTransactionResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /play/cblotto/transaction [post]
func (h agentCblottoController) CallBackAgentCblottoTransaction(c *gin.Context) {

	headerKey := c.GetHeader("Authorization")
	var reqBody model.CallBackAgentCblottoTransaction
	if err := c.ShouldBindJSON(&reqBody); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.agentCblottoService.CallBackAgentCblottoTransaction(reqBody, headerKey)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}
