package handler

import (
	"cybergame-api/helper"
	"cybergame-api/middleware"
	"cybergame-api/model"
	"cybergame-api/service"
	"io"
	"log"
	"os"
	"strconv"

	"cybergame-api/repository"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"gorm.io/gorm"
)

type paymentGatewayController struct {
	paymentGatewayService    service.PaymentGatewayService
	paygatePapayaPayService  service.PapayaPayService
	paygatePayonexService    service.PayonexService
	paygateJbpayService      service.JbpayService
	paygatePompayService     service.PompayService
	paygatePaymentcoService  service.PaymentcoService
	paygateZappayService     service.ZappayService
	paygateOnepayService     service.OnepayService
	paygateFlashpayService   service.FlashpayService
	paygateBizpayService     service.BizpayService
	paygateSugarpayService   service.SugarpayService
	paygateZmanpayService    service.ZmanpayService
	paygatePostmanPayService service.PostmanPayService
	paygateMazepayService    service.MazepayService
	paygateMeepayService     service.MeepayService
}

func newPaymentGatewayController(
	paymentGatewayService service.PaymentGatewayService,
	paygatePapayaPayService service.PapayaPayService,
	paygatePayonexService service.PayonexService,
	paygateJbpayService service.JbpayService,
	paygatePompayService service.PompayService,
	paygatePaymentcoService service.PaymentcoService,
	paygateZappayService service.ZappayService,
	paygateOnepayService service.OnepayService,
	paygateFlashpayService service.FlashpayService,
	paygateBizpayService service.BizpayService,
	paygateSugarpayService service.SugarpayService,
	paygateZmanpayService service.ZmanpayService,
	paygatePostmanPayService service.PostmanPayService,
	paygateMazepayService service.MazepayService,
	paygateMeepayService service.MeepayService,
) paymentGatewayController {
	return paymentGatewayController{
		paymentGatewayService,
		paygatePapayaPayService,
		paygatePayonexService,
		paygateJbpayService,
		paygatePompayService,
		paygatePaymentcoService,
		paygateZappayService,
		paygateOnepayService,
		paygateFlashpayService,
		paygateBizpayService,
		paygateSugarpayService,
		paygateZmanpayService,
		paygatePostmanPayService,
		paygateMazepayService,
		paygateMeepayService,
	}
}

func PaymentGatewayController(r *gin.RouterGroup, db *gorm.DB) {

	singleSession := middleware.SingleSession(db)

	activityLusckyWheelRepo := repository.NewActivityLuckyWheelRepository(db)
	serviceRepo := repository.NewNotificationRepository(db)
	serviceNoti := service.NewNotificationService(serviceRepo)
	activityLuckyWheelService := service.NewActivityLuckyWheelService(activityLusckyWheelRepo, db, serviceNoti)
	promotionWebRepo := repository.NewPromotionWebRepository(db)
	userRepo := repository.NewUserRepository(db)
	perRepo := repository.NewPermissionRepository(db)
	groupRepo := repository.NewGroupRepository(db)
	otpRepo := repository.NewOtpRepository(db)
	agentInfoRepo := repository.NewAgentInfoRepository(db)
	recommendRepo := repository.NewRecommendRepository(db)
	afRepo := repository.NewAffiliateRepository(db)
	notiRepo := repository.NewNotificationRepository(db)
	notiService := service.NewNotificationService(notiRepo)
	actionService := service.NewAdminActionService(repository.NewAdminActionRepository(db))
	userService := service.NewUserService(
		userRepo,
		db,
		perRepo,
		groupRepo,
		otpRepo,
		agentInfoRepo,
		recommendRepo,
		afRepo,
		notiService,
		actionService,
	)
	promotionWebService := service.NewPromotionWebService(promotionWebRepo, db, serviceNoti, userService)

	repo1 := repository.NewPaymentGatewayRepository(db)
	service1 := service.NewPaymentGatewayService(repo1, db, activityLuckyWheelService, promotionWebService, notiService)
	// PapayaPay
	paygatePapayaPayRepo := repository.NewPapayaPayRepository(db)
	paygatePapayaPayService := service.NewPapayaPayService(db, paygatePapayaPayRepo, activityLuckyWheelService, promotionWebService, notiService)
	// Payonex
	paygatePayonexRepo := repository.NewPayonexRepository(db)
	paygatePayonexService := service.NewPayonexService(db, paygatePayonexRepo, activityLuckyWheelService, promotionWebService, notiService)
	// Jbpay
	paygateJbpayRepo := repository.NewJbpayRepository(db)
	paygateJbpayService := service.NewJbpayService(db, paygateJbpayRepo, activityLuckyWheelService, promotionWebService, notiService)
	// Pompay
	paygatePompayRepo := repository.NewPompayRepository(db)
	paygatePompayService := service.NewPompayService(db, paygatePompayRepo, activityLuckyWheelService, promotionWebService, notiService)
	// Paymentco
	paygatePaymentcoRepo := repository.NewPaymentcoRepository(db)
	paygatePaymentcoService := service.NewPaymentcoService(db, paygatePaymentcoRepo, activityLuckyWheelService, promotionWebService, notiService)
	// Zappay
	paygateZappayRepo := repository.NewZappayRepository(db)
	paygateZappayService := service.NewZappayService(db, paygateZappayRepo, activityLuckyWheelService, promotionWebService, notiService)
	// Onepay
	paygateOnepayRepo := repository.NewOnepayRepository(db)
	paygateOnepayService := service.NewOnepayService(db, paygateOnepayRepo, activityLuckyWheelService, promotionWebService, notiService)
	// Flashpay
	paygateFlashpayRepo := repository.NewFlashpayRepository(db)
	paygateFlashpayService := service.NewFlashpayService(db, paygateFlashpayRepo, activityLuckyWheelService, promotionWebService, notiService)
	// Bizpay
	paymentBizpayRepo := repository.NewBizpayRepository(db)
	paymentBizpayService := service.NewBizpayService(db, paymentBizpayRepo, activityLuckyWheelService, promotionWebService, notiService)
	// Sugarpay
	paymentSugarpayRepo := repository.NewSugarpayRepository(db)
	paymentSugarpayService := service.NewSugarpayService(db, paymentSugarpayRepo, activityLuckyWheelService, promotionWebService, notiService)
	// Zmanpay
	paygateZmanpayRepo := repository.NewZmanpayRepository(db)
	paygateZmanpayService := service.NewZmanpayService(db, paygateZmanpayRepo, activityLuckyWheelService, promotionWebService, notiService)
	// PostmanPay
	paygatePostmanPayRepo := repository.NewPostmanPayRepository(db)
	paygatePostmanPayService := service.NewPostmanPayService(db, paygatePostmanPayRepo, activityLuckyWheelService, promotionWebService, notiService)
	// Mazepay
	paygateMazepayRepo := repository.NewMazepayRepository(db)
	paygateMazepayService := service.NewMazepayService(db, paygateMazepayRepo, activityLuckyWheelService, promotionWebService, notiService)
	// Meepay
	paygateMeepayRepo := repository.NewMeepayRepository(db)
	paygateMeepayService := service.NewMeepayService(db, paygateMeepayRepo, activityLuckyWheelService, promotionWebService, notiService)
	handler := newPaymentGatewayController(service1,
		paygatePapayaPayService, paygatePayonexService, paygateJbpayService, paygatePompayService, paygatePaymentcoService,
		paygateZappayService, paygateOnepayService, paygateFlashpayService, paymentBizpayService, paymentSugarpayService,
		paygateZmanpayService, paygatePostmanPayService, paygateMazepayService, paygateMeepayService)

	role := middleware.Role(db)

	// [********] MultiSelected Payment Gateway
	manageMultiplePaygateRoute := r.Group("/payment-gateways", middleware.AuthorizeAdmin, singleSession.SingleAdminSession(), role.CheckPermission([]string{"payment_gateway", "payment_gateway_edit"}))
	manageMultiplePaygateRoute.GET("/provider-options", handler.getPaygateProviderOptions)
	manageMultiplePaygateRoute.GET("/provider-limit-list", handler.getPaygateProviderLimitList)
	manageMultiplePaygateRoute.GET("/account-list", handler.getPaygateAccountList)
	manageMultiplePaygateRoute.GET("/account-detail/:id", handler.getPaygateAccountById)
	manageMultiplePaygateRoute.POST("/account-create", handler.createPaygateAccount)
	manageMultiplePaygateRoute.PATCH("/account-update/:id", role.CheckPermission([]string{"payment_gateway_edit"}), handler.updatePaygateAccount)
	manageMultiplePaygateRoute.DELETE("/account-delete/:id", handler.deletePaygateAccount)

	webUserRoute := r.Group("/web", middleware.AuthorizeUser, singleSession.SingleUserSession())
	// webUserRoute.GET("/paygate/deposit-account", handler.getWebPaygateDepositAccount)
	webUserRoute.GET("/paygate/deposit-account-list", handler.getWebPaygateDepositAccountList)
	// PAYMENT GATEWAY CALLBACK
	webhookRoute := r.Group("/webhook")
	// webhookRoute.POST("/heng/callback", handler.createPaygateHengWebhook)
	webhookRoute.POST("/luckyth/callback", handler.createLuckyThaiWebhook)
	webhookRoute.POST("/papaya-in/callback", handler.createPapayaPayFundInWebhook)
	webhookRoute.POST("/papaya-out/callback", handler.createPapayaPayFundOutWebhook)
	webhookRoute.POST("/payonex/callback", handler.createPayonexWebhook)
	webhookRoute.POST("/jbpayment/callback", handler.createJbpayWebhook)
	webhookRoute.POST("/jbpayment/repay-callback", handler.createJbpayDepositWebhook)
	webhookRoute.POST("/jbpayment/loan-callback", handler.createJbpayWithdrawWebhook)
	webhookRoute.POST("/pompay/callback", handler.createPompayWebhook)
	webhookRoute.POST("/paymentco/callback", handler.createPaymentcoWebhook)
	webhookRoute.POST("/zappay/repay-callback", handler.createZappayDepositWebhook)
	webhookRoute.POST("/zappay/loan-callback", handler.createZappayWithdrawWebhook)
	webhookRoute.POST("/onepay/callback", handler.createOnepayWebhook)
	webhookRoute.POST("/onepay/dep-callback", handler.createOnepayDepositWebhook)
	webhookRoute.POST("/onepay/wid-callback", handler.createOnepayWithdrawWebhook)
	webhookRoute.POST("/flashpay/callback", handler.createFlashpayWebhook)
	webhookRoute.POST("/flashpay/dep-callback", handler.createFlashpayDepositWebhook)
	webhookRoute.POST("/flashpay/wid-callback", handler.createFlashpayWithdrawWebhook)
	webhookRoute.POST("/bizpay/callback", handler.createBizpayWebhook)
	webhookRoute.POST("/bizpay/dep-callback", handler.createBizpayDepositWebhook)
	webhookRoute.POST("/bizpay/wid-callback", handler.createBizpayWithdrawWebhook)
	webhookRoute.POST("/sugarpay/dep-callback", handler.createSugarpayDepositWebhook)
	webhookRoute.POST("/sugarpay/wid-callback", handler.createSugarpayWithdrawWebhook)
	webhookRoute.POST("/zmanpay/dep-callback", handler.createZmanpayDepositWebhook)
	webhookRoute.POST("/zmanpay/wid-callback", handler.createZmanpayWithdrawWebhook)
	webhookRoute.POST("/postmanpay/dep-callback", handler.createPostmanPayDepositWebhook)
	webhookRoute.POST("/postmanpay/wid-callback", handler.createPostmanPayWithdrawWebhook)
	webhookRoute.POST("/mazepay/dep-callback", handler.createMazepayDepositWebhook)
	webhookRoute.POST("/mazepay/wid-callback", handler.createMazepayWithdrawWebhook)
	webhookRoute.POST("/meepay/dep-callback", handler.createMeepayDepositWebhook)
	webhookRoute.POST("/meepay/wid-callback", handler.createMeepayWithdrawWebhook)
	// HENG
	// hengRoute := r.Group("/heng", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	// hengRoute.GET("/balance", handler.getPaygateHengBalance)
	// hengRoute.POST("/order", handler.createPaygateHengOrder)
	// hengRoute.GET("/orders", handler.getPaygateHengOrderList)
	// hengRoute.GET("/orders/:id", handler.getPaygateHengOrderById)
	// hengRoute.GET("/qr/:id", handler.getPaygateHengOrderQrById)
	// hengRoute.GET("/qr-base64/:id", handler.getPaygateHengOrderQrBase64ById)
	// HENG BOF
	// hengReportRoute := r.Group("/heng-report", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	// hengReportRoute.GET("/order-list", handler.getHengOrderReportList)
	// hengReportRoute.POST("/create-deposit/:id", handler.createHengDepositFromOrder)
	// hengReportRoute.POST("/ignore-deposit/:id", handler.ignoreHengDepositFromOrder)
	// hengReportRoute.GET("/callback-list", handler.getHengCallbackReportResponse)
	// [20240502] เอาออกไปใช้ Merchant รวม
	// adminRoute := r.Group("/payment-gateway/heng", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	// adminRoute.GET("/setting", handler.getPaygateHengSetting)
	// adminRoute.PUT("/setting", handler.updatePaygateHengSetting)
	// LUCKYTHAI
	luckythRoute := r.Group("/luckyth", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	luckythRoute.GET("/balance", handler.getLuckyThaiBalance)
	// PayoneX
	payonexRoute := r.Group("/payonex", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	payonexRoute.POST("/cancel-withdraw/:id", handler.cancelPayonexWithdraw)
	// USER PAYMENT GATEWAY
	webRoute := r.Group("/web", middleware.AuthorizeUser, singleSession.SingleUserSession())
	// HENG
	// webRoute.GET("/paygate-heng/deposit-account", handler.getHengWebDepositAccount)
	// webRoute.POST("/paygate-heng/deposit", handler.createPaygateHengDeposit)
	// webRoute.POST("/paygate-heng/test-checkname", handler.checkCustomerDepositName)
	// webRoute.GET("/paygate-heng/qr/:id", handler.getPaygateHengDepositQrById)
	// webRoute.GET("/paygate-heng/base64/:id", handler.getPaygateHengDepositBase64ById)
	// LUCKYTHAI
	webRoute.GET("/paygate-luckyth/deposit-account", handler.getLuckyThaiWebDepositAccount)
	webRoute.POST("/paygate-luckyth/deposit", handler.createLuckyThaiDeposit)
	webRoute.POST("/paygate-luckyth/withdraw", handler.createLuckyThaiWithdraw)
	webRoute.GET("/paygate-luckyth/balance", handler.getWebLuckyThaiBalance)
	// PapayaPay
	webRoute.POST("/paygate-papaya/deposit", handler.createPapayaPayDeposit)
	webRoute.POST("/paygate-papaya/withdraw", handler.createPapayaPayWithdraw)
	// Payonex
	webRoute.POST("/paygate-payonex/deposit", handler.createPayonexDeposit)
	webRoute.POST("/paygate-payonex/withdraw", handler.createPayonexWithdraw)
	// Jbpayment
	webRoute.POST("/paygate-jbpay/deposit", handler.createJbpayDeposit)
	webRoute.POST("/paygate-jbpay/withdraw", handler.createJbpayWithdraw)
	// Pompayment
	webRoute.POST("/paygate-pompay/deposit", handler.createPompayDeposit)
	webRoute.POST("/paygate-pompay/withdraw", handler.createPompayWithdraw)
	// Paymentco
	webRoute.POST("/paygate-paymentco/deposit", handler.createPaymentcoDeposit)
	webRoute.POST("/paygate-paymentco/withdraw", handler.createPaymentcoWithdraw)
	// Zappay
	webRoute.POST("/paygate-zappay/deposit", handler.createZappayDeposit)
	webRoute.POST("/paygate-zappay/withdraw", handler.createZappayWithdraw)
	// Onepay
	webRoute.POST("/paygate-onepay/deposit", handler.createOnepayDeposit)
	webRoute.POST("/paygate-onepay/withdraw", handler.createOnepayWithdraw)
	// Flashpay
	webRoute.POST("/paygate-flashpay/deposit", handler.createFlashpayDeposit)
	webRoute.POST("/paygate-flashpay/withdraw", handler.createFlashpayWithdraw)
	// Bizpay
	webRoute.POST("/paygate-bizpay/deposit", handler.createBizpayDeposit)
	webRoute.POST("/paygate-bizpay/withdraw", handler.createBizpayWithdraw)
	// Sugarpay
	webRoute.POST("/paygate-sugarpay/deposit", handler.createSugarpayDeposit)
	webRoute.POST("/paygate-sugarpay/withdraw", handler.createSugarpayWithdraw)
	// Zmanpay
	webRoute.GET("/paygate-zmanpay/pending-deposit", handler.getPendingZmanpayDepositOrder)
	webRoute.POST("/paygate-zmanpay/cancel-deposit", handler.cancelZmanpayDeposit)
	webRoute.POST("/paygate-zmanpay/deposit", handler.createZmanpayDeposit)
	webRoute.POST("/paygate-zmanpay/withdraw", handler.createZmanpayWithdraw)
	// PostmanPay
	webRoute.POST("/paygate-postmanpay/deposit", handler.createPostmanPayDeposit)
	webRoute.POST("/paygate-postmanpay/withdraw", handler.createPostmanPayWithdraw)
	// Mazepay
	webRoute.GET("/paygate-mazepay/pending-deposit", handler.getPendingMazepayDepositOrder)
	webRoute.POST("/paygate-mazepay/cancel-deposit", handler.cancelMazepayDeposit)
	webRoute.POST("/paygate-mazepay/deposit", handler.createMazepayDeposit)
	webRoute.POST("/paygate-mazepay/withdraw", handler.createMazepayWithdraw)
	// Meepay
	webRoute.POST("/paygate-meepay/deposit", handler.createMeepayDeposit)
	webRoute.POST("/paygate-meepay/withdraw", handler.createMeepayWithdraw)
	// All Payment BOF
	paygateReportRoute := r.Group("/paygate-report", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	paygateReportRoute.GET("/payment-options", handler.getReportPaymentListOptions)
	paygateReportRoute.GET("/order-list", handler.getPaygateOrderReportList)
	paygateReportRoute.POST("/create-deposit", handler.createPaygateDepositFromOrder)
	paygateReportRoute.POST("/ignore-deposit", handler.ignorePaygateDepositFromOrder)
	// User Log
	userTransaction := r.Group("/user-transaction", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	userTransaction.GET("/options/paygate-merchant", handler.getPaygateMerchantOption)
}

// @Summary (getWebPaygateDepositAccountList) ดึงข้อมูล Payment Gateway ที่ใช้ในเว็บ สำหรับฝากเงิน ทีละหลายบัญชี
// @Description (getWebPaygateDepositAccountList) บช เดียว ที่เลือก 1 เว็บเลือกได้ หลาย payment gateway
// @Tags WEB - Payment Gateway
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} []model.PaygateCustomerDepositAccount
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/paygate/deposit-account-list [get]
func (h paymentGatewayController) getWebPaygateDepositAccountList(c *gin.Context) {

	data, err := h.paymentGatewayService.GetWebPaygateDepositAccountList()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (getLuckyThaiBalance) ดึงข้อมูลยอดคงเหลือในระบบ LuckyThai
// @Description (getLuckyThaiBalance)
// @Tags Manage Payment Gateway - LuckyThai
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.LuckyThaiCheckBalanceRemoteResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /luckyth/balance [get]
func (h paymentGatewayController) getLuckyThaiBalance(c *gin.Context) {

	data, err := h.paymentGatewayService.LuckyThaiCheckBalance()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (getLuckyThaiWebDepositAccount) ดึงข้อมูล Payment Gateway ที่ใช้ในเว็บ สำหรับฝากเงิน ********** เลิกใช้ ไปใช้ บชรวม รอลบ**
// @Description (getLuckyThaiWebDepositAccount)
// @Tags WEB - Payment Gateway - LuckyThai
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.LuckyThaiCustomerDepositInfo
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/paygate-luckyth/deposit-account [get]
func (h paymentGatewayController) getLuckyThaiWebDepositAccount(c *gin.Context) {

	data, err := h.paymentGatewayService.GetLuckyThaiWebDepositAccount()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (createLuckyThaiDeposit) สร้างรายการฝาก ด้วย Payment Gateway LuckyThai
// @Description (createLuckyThaiDeposit)
// @Tags WEB - Payment Gateway - LuckyThai
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.LuckyThaiDepositCreateRequest true "body"
// @Success 201 {object} model.LuckyThaiOrderWebResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/paygate-luckyth/deposit [post]
func (h paymentGatewayController) createLuckyThaiDeposit(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var body model.LuckyThaiDepositCreateRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	body.UserId = userId
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	resp, err := h.paymentGatewayService.CreateLuckyThaiDeposit(body)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, resp)
}

// @Summary (createLuckyThaiWithdraw) สร้างรายการถอน ด้วย Payment Gateway LuckyThai
// @Description (createLuckyThaiWithdraw)
// @Tags WEB - Payment Gateway - LuckyThai
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.LuckyThaiWithdrawCreateRequest true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/paygate-luckyth/withdraw [post]
func (h paymentGatewayController) createLuckyThaiWithdraw(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var body model.LuckyThaiWithdrawCreateRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	body.UserId = userId
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	ginMode := os.Getenv("GIN_MODE")
	if ginMode == "debug" {
		insertId, err := h.paymentGatewayService.CreateLuckyThaiWithdraw(body)
		if err != nil {
			HandleError(c, err)
			return
		}
		c.JSON(201, model.SuccessWithId{Message: "Created success", Id: *insertId})
	} else {
		// สำหรับ production ถ้ามีการใช้งานแสดงว่าหลุด
		if err := h.paymentGatewayService.CreateSystemLog("createLuckyThaiWithdraw", nil); err != nil {
			HandleError(c, err)
			return
		}
		c.JSON(201, model.SuccessWithId{Message: "Created success", Id: 1})
	}
}

// @Summary (createLuckyThaiWebhook) สำหรับรับ Webhook จาก LuckyThai
// @Description (createLuckyThaiWebhook)
// @Tags Webhook Payment Gateway - All
// @Produce json
// @Param body body string true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /webhook/luckyth/callback [post]
func (h paymentGatewayController) createLuckyThaiWebhook(c *gin.Context) {

	var createReq model.LuckyThaiWebhookRequest
	createReq.JsonPayload = "{}"

	body := c.Request.Body
	responseData, err := io.ReadAll(body)
	if err != nil {
		log.Println(err)
	} else {
		createReq.JsonPayload = string(responseData)
	}

	if _, err := h.paymentGatewayService.CreateLuckyThaiWebhook(createReq); err != nil {
		log.Println("createLuckyThaiWebhook.ERROR", err.Error())
	}
	// c.JSON(201, model.Success{Message: "Created success"})
	// guys, can you return lowercase 'ok' to us when you receive our PAID callback for payin and payout.
	// currently you send back us

	// "message": "Created success"

	// please just send us:

	// ok

	// would be much appreciated!
	c.String(200, "ok")
}

// @Summary (getWebLuckyThaiBalance) ดึงข้อมูลยอดคงเหลือในระบบ LuckyThai
// @Description (getWebLuckyThaiBalance)
// @Tags WEB - Payment Gateway - LuckyThai
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.LuckyThaiCheckBalanceRemoteResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/paygate-luckyth/balance [get]
func (h paymentGatewayController) getWebLuckyThaiBalance(c *gin.Context) {

	data, err := h.paymentGatewayService.LuckyThaiCheckBalance()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (createPapayaPayDeposit) สร้างรายการฝาก ด้วย Payment Gateway PapayaPay
// @Description (createPapayaPayDeposit)
// @Tags WEB - Payment Gateway - PapayaPay
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.PapayaPayDepositCreateRequest true "body"
// @Success 201 {object} model.PapayaPayOrderWebResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/paygate-papaya/deposit [post]
func (h paymentGatewayController) createPapayaPayDeposit(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var body model.PapayaPayDepositCreateRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleWebError(c, err)
		return
	}
	body.UserId = userId
	if err := validator.New().Struct(body); err != nil {
		HandleWebError(c, err)
		return
	}

	resp, err := h.paygatePapayaPayService.CreatePapayaPayDeposit(body)
	if err != nil {
		HandleWebError(c, err)
		return
	}
	c.JSON(201, resp)
}

// @Summary (createPapayaPayWithdraw) สร้างรายการถอน ด้วย Payment Gateway PapayaPay
// @Description (createPapayaPayWithdraw)
// @Tags WEB - Payment Gateway - PapayaPay
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.PapayaPayWithdrawCreateRequest true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/paygate-papaya/withdraw [post]
func (h paymentGatewayController) createPapayaPayWithdraw(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var body model.PapayaPayWithdrawCreateRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleWebError(c, err)
		return
	}
	body.UserId = userId
	if err := validator.New().Struct(body); err != nil {
		HandleWebError(c, err)
		return
	}

	ginMode := os.Getenv("GIN_MODE")
	if ginMode == "debug" {
		insertId, err := h.paygatePapayaPayService.CreatePapayaPayWithdraw(body)
		if err != nil {
			HandleWebError(c, err)
			return
		}
		c.JSON(201, model.SuccessWithId{Message: "Created success", Id: *insertId})
	} else {
		// สำหรับ production ถ้ามีการใช้งานแสดงว่าหลุด
		if err := h.paygatePapayaPayService.CreateSystemLog("createPapayaPayWithdraw", nil); err != nil {
			HandleWebError(c, err)
			return
		}
		c.JSON(201, model.SuccessWithId{Message: "Created success", Id: 1})
	}
}

// @Summary (createPapayaPayFundInWebhook) สำหรับรับ Webhook จาก PapayaPay แบบ Fund-In
// @Description (createPapayaPayFundInWebhook)
// @Tags Webhook Payment Gateway - All
// @Produce json
// @Param body body string true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /webhook/papaya-in/callback [post]
func (h paymentGatewayController) createPapayaPayFundInWebhook(c *gin.Context) {

	var createReq model.PapayaPayWebhookRequest
	createReq.JsonPayload = "{}"

	body := c.Request.Body
	responseData, err := io.ReadAll(body)
	if err != nil {
		log.Println(err)
	} else {
		createReq.JsonPayload = string(responseData)
	}

	if _, err := h.paygatePapayaPayService.CreatePapayaPayFundInWebhook(createReq); err != nil {
		log.Println("createPapayaPayFundInWebhook.ERROR", err.Error())
	}
	// c.JSON(201, model.Success{Message: "Created success"})
	// later ต้อง return อะไรบ้าง
	c.String(200, "ok")
}

// @Summary (createPapayaPayFundOutWebhook) สำหรับรับ Webhook จาก PapayaPay แบบ Fund-Out
// @Description (createPapayaPayFundOutWebhook)
// @Tags Webhook Payment Gateway - All
// @Produce json
// @Param body body string true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /webhook/papaya-out/callback [post]
func (h paymentGatewayController) createPapayaPayFundOutWebhook(c *gin.Context) {

	var createReq model.PapayaPayWebhookRequest
	createReq.JsonPayload = "{}"

	body := c.Request.Body
	responseData, err := io.ReadAll(body)
	if err != nil {
		log.Println(err)
	} else {
		createReq.JsonPayload = string(responseData)
	}

	if _, err := h.paygatePapayaPayService.CreatePapayaPayFundOutWebhook(createReq); err != nil {
		log.Println("createPapayaPayFundOutWebhook.ERROR", err.Error())
	}
	// c.JSON(201, model.Success{Message: "Created success"})
	// later ต้อง return อะไรบ้าง
	c.String(200, "ok")
}

// @Summary (createPayonexWebhook) สำหรับรับ Webhook จาก Payonex
// @Description (createPayonexWebhook)
// @Tags Webhook Payment Gateway - All
// @Produce json
// @Param body body string true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /webhook/payonex/callback [post]
func (h paymentGatewayController) createPayonexWebhook(c *gin.Context) {

	var createReq model.PayonexWebhookRequest
	createReq.JsonPayload = "{}"

	body := c.Request.Body
	responseData, err := io.ReadAll(body)
	if err != nil {
		log.Println(err)
	} else {
		createReq.JsonPayload = string(responseData)
	}

	if _, err := h.paygatePayonexService.CreatePayonexWebhook(createReq); err != nil {
		log.Println("createPayonexWebhook.ERROR", err.Error())
	}
	// c.JSON(201, model.Success{Message: "Created success"})
	// later ต้อง return อะไรบ้าง
	c.String(200, "ok")
}

// @Summary (createJbpayWebhook) สำหรับรับ Webhook จาก Jbpay
// @Description (createJbpayWebhook)
// @Tags Webhook Payment Gateway - All
// @Produce json
// @Param body body string true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /webhook/jbpayment/callback [post]
func (h paymentGatewayController) createJbpayWebhook(c *gin.Context) {

	var createReq model.JbpayWebhookRequest
	createReq.JsonPayload = "{}"

	body := c.Request.Body
	responseData, err := io.ReadAll(body)
	if err != nil {
		log.Println(err)
	} else {
		createReq.JsonPayload = string(responseData)
	}

	if _, err := h.paygateJbpayService.CreateJbpayWebhook(createReq); err != nil {
		log.Println("createJbpayWebhook.ERROR", err.Error())
	}
	// c.JSON(201, model.Success{Message: "Created success"})
	// later ต้อง return อะไรบ้าง
	c.String(200, "ok")
}

// @Summary (createJbpayDepositWebhook) สำหรับรับ Webhook จาก Jbpay
// @Description (createJbpayDepositWebhook)
// @Tags Webhook Payment Gateway - All
// @Produce json
// @Param body body string true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /webhook/jbpayment/repay-callback [post]
func (h paymentGatewayController) createJbpayDepositWebhook(c *gin.Context) {

	var createReq model.JbpayWebhookRequest
	createReq.JsonPayload = "{}"

	body := c.Request.Body
	responseData, err := io.ReadAll(body)
	if err != nil {
		log.Println(err)
	} else {
		createReq.JsonPayload = string(responseData)
	}

	if _, err := h.paygateJbpayService.CreateJbpayDepositWebhook(createReq); err != nil {
		log.Println("createJbpayDepositWebhook.ERROR", err.Error())
	}
	// c.JSON(201, model.Success{Message: "Created success"})
	// later ต้อง return อะไรบ้าง
	c.String(200, "ok")
}

// @Summary (createJbpayWithdrawWebhook) สำหรับรับ Webhook จาก Jbpay
// @Description (createJbpayWithdrawWebhook)
// @Tags Webhook Payment Gateway - All
// @Produce json
// @Param body body string true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /webhook/jbpayment/loan-callback [post]
func (h paymentGatewayController) createJbpayWithdrawWebhook(c *gin.Context) {

	var createReq model.JbpayWebhookRequest
	createReq.JsonPayload = "{}"

	body := c.Request.Body
	responseData, err := io.ReadAll(body)
	if err != nil {
		log.Println(err)
	} else {
		createReq.JsonPayload = string(responseData)
	}

	if _, err := h.paygateJbpayService.CreateJbpayWithdrawWebhook(createReq); err != nil {
		log.Println("createJbpayWithdrawWebhook.ERROR", err.Error())
	}
	// c.JSON(201, model.Success{Message: "Created success"})
	// later ต้อง return อะไรบ้าง
	c.String(200, "ok")
}

// @Summary (createPompayWebhook) สำหรับรับ Webhook จาก Pompay
// @Description (createPompayWebhook)
// @Tags Webhook Payment Gateway - All
// @Produce json
// @Param body body string true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /webhook/pompay/callback [post]
func (h paymentGatewayController) createPompayWebhook(c *gin.Context) {

	var createReq model.PompayWebhookRequest
	createReq.JsonPayload = "{}"

	body := c.Request.Body
	responseData, err := io.ReadAll(body)
	if err != nil {
		log.Println(err)
	} else {
		createReq.JsonPayload = string(responseData)
	}

	if _, err := h.paygatePompayService.CreatePompayWebhook(createReq); err != nil {
		log.Println("createPompayWebhook.ERROR", err.Error())
	}
	// c.JSON(201, model.Success{Message: "Created success"})
	// later ต้อง return อะไรบ้าง
	c.String(200, "ok")
}

// @Summary (createPayonexDeposit) สร้างรายการฝาก ด้วย Payment Gateway Payonex
// @Description (createPayonexDeposit)
// @Tags WEB - Payment Gateway - Payonex
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.PayonexDepositCreateRequest true "body"
// @Success 201 {object} model.PayonexOrderWebResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/paygate-payonex/deposit [post]
func (h paymentGatewayController) createPayonexDeposit(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var body model.PayonexDepositCreateRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleWebError(c, err)
		return
	}
	body.UserId = userId
	if err := validator.New().Struct(body); err != nil {
		HandleWebError(c, err)
		return
	}

	resp, err := h.paygatePayonexService.CreatePayonexDeposit(body)
	if err != nil {
		HandleWebError(c, err)
		return
	}
	c.JSON(201, resp)
}

// @Summary (createPayonexWithdraw) สร้างรายการถอน ด้วย Payment Gateway Payonex
// @Description (createPayonexWithdraw)
// @Tags WEB - Payment Gateway - Payonex
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.PayonexWithdrawCreateRequest true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/paygate-payonex/withdraw [post]
func (h paymentGatewayController) createPayonexWithdraw(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var body model.PayonexWithdrawCreateRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleWebError(c, err)
		return
	}
	body.UserId = userId
	if err := validator.New().Struct(body); err != nil {
		HandleWebError(c, err)
		return
	}

	ginMode := os.Getenv("GIN_MODE")
	if ginMode == "debug" {
		insertId, err := h.paygatePayonexService.CreatePayonexWithdraw(body)
		if err != nil {
			HandleWebError(c, err)
			return
		}
		c.JSON(201, model.SuccessWithId{Message: "Created success", Id: *insertId})
	} else {
		// สำหรับ production ถ้ามีการใช้งานแสดงว่าหลุด
		if err := h.paygatePayonexService.CreateSystemLog("createPayonexWithdraw", nil); err != nil {
			HandleWebError(c, err)
			return
		}
		c.JSON(201, model.SuccessWithId{Message: "Created success", Id: 1})
	}
}

// @Summary (cancelPayonexWithdraw) ยกเลิก รายการถอน จาก Payonex
// @Description (cancelPayonexWithdraw) ยกเลิก รายการถอน จาก Payonex
// @Tags Manual - Payment Gateway
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /payonex/cancel-withdraw/{id} [post]
func (h paymentGatewayController) cancelPayonexWithdraw(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	identifier, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		HandleError(c, err)
		return
	}

	if err := h.paygatePayonexService.CancelWithdrawFromPayonex(identifier, adminId); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "Updated success"})
}

// @Summary (createJbpayDeposit) สร้างรายการฝาก ด้วย Payment Gateway Jbpay
// @Description (createJbpayDeposit)
// @Tags WEB - Payment Gateway - Jbpay
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.JbpayDepositCreateRequest true "body"
// @Success 201 {object} model.JbpayOrderWebResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/paygate-jbpay/deposit [post]
func (h paymentGatewayController) createJbpayDeposit(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var body model.JbpayDepositCreateRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	body.UserId = userId
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	resp, err := h.paygateJbpayService.CreateJbpayDeposit(body)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, resp)
}

// @Summary (createJbpayWithdraw) สร้างรายการถอน ด้วย Payment Gateway Jbpay
// @Description (createJbpayWithdraw)
// @Tags WEB - Payment Gateway - Jbpay
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.JbpayWithdrawCreateRequest true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/paygate-jbpay/withdraw [post]
func (h paymentGatewayController) createJbpayWithdraw(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var body model.JbpayWithdrawCreateRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	body.UserId = userId
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	ginMode := os.Getenv("GIN_MODE")
	if ginMode == "debug" {
		insertId, err := h.paygateJbpayService.CreateJbpayWithdraw(body)
		if err != nil {
			HandleError(c, err)
			return
		}
		c.JSON(201, model.SuccessWithId{Message: "Created success", Id: *insertId})
	} else {
		// สำหรับ production ถ้ามีการใช้งานแสดงว่าหลุด
		if err := h.paygateJbpayService.CreateSystemLog("createJbpayWithdraw", nil); err != nil {
			HandleError(c, err)
			return
		}
		c.JSON(201, model.SuccessWithId{Message: "Created success", Id: 1})
	}
}

// @Summary (createPompayDeposit) สร้างรายการฝาก ด้วย Payment Gateway Pompay
// @Description (createPompayDeposit)
// @Tags WEB - Payment Gateway - Pompay
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.PompayDepositCreateRequest true "body"
// @Success 201 {object} model.PompayOrderWebResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/paygate-pompay/deposit [post]
func (h paymentGatewayController) createPompayDeposit(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var body model.PompayDepositCreateRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	body.UserId = userId
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	resp, err := h.paygatePompayService.CreatePompayDeposit(body)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, resp)
}

// @Summary (createPompayWithdraw) สร้างรายการถอน ด้วย Payment Gateway Pompay
// @Description (createPompayWithdraw)
// @Tags WEB - Payment Gateway - Pompay
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.PompayWithdrawCreateRequest true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/paygate-pompay/withdraw [post]
func (h paymentGatewayController) createPompayWithdraw(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var body model.PompayWithdrawCreateRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	body.UserId = userId
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	ginMode := os.Getenv("GIN_MODE")
	if ginMode == "debug" {
		insertId, err := h.paygatePompayService.CreatePompayWithdraw(body)
		if err != nil {
			HandleError(c, err)
			return
		}
		c.JSON(201, model.SuccessWithId{Message: "Created success", Id: *insertId})
	} else {
		// สำหรับ production ถ้ามีการใช้งานแสดงว่าหลุด
		if err := h.paygatePompayService.CreateSystemLog("createPompayWithdraw", nil); err != nil {
			HandleError(c, err)
			return
		}
		c.JSON(201, model.SuccessWithId{Message: "Created success", Id: 1})
	}
}

// @Summary (getReportPaymentListOptions) ข้อมูลตัวเลือก Payment Gateway ที่มีรายงาน
// @Description (getReportPaymentListOptions) ข้อมูลตัวเลือก Payment Gateway ที่มีรายงาน
// @Tags Payment Gateway - Report
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} []model.SelectOptions
// @Failure 400 {object} handler.ErrorResponse
// @Router /paygate-report/payment-options [get]
func (h paymentGatewayController) getReportPaymentListOptions(c *gin.Context) {

	data, err := h.paymentGatewayService.GetReportPaymentListOptions()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (getPaygateOrderReportList) รายการแจ้งฝาก Paygate
// @Description (getPaygateOrderReportList) รายการแจ้งฝาก Paygate
// @Description #การกรองวันที่ กรองเพื่อเอาข้อมูลตัวเลขเฉพาะช่วงที่เลือก
// @Description กรองข้อมูลตามประเภทวัน ส่ง dateType เป็น today, yesterday, last_week, last_month
// @Description กรองข้อมูลตามช่วงวันที่ ส่ง fromDate, toDate ในรูปแบบ YYYY-MM-DD (2021-12-31)
// @Tags Payment Gateway - Report
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.PaygateOrderReportListRequest true "PaygateOrderReportListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /paygate-report/order-list [get]
func (h paymentGatewayController) getPaygateOrderReportList(c *gin.Context) {

	var query model.PaygateOrderReportListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	pagination, err := h.paymentGatewayService.GetPaygateOrderReportList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, pagination)
}

// @Summary (createPaygateDepositFromOrder) สร้างรายการฝาก ด้วย Paygate Order
// @Description (createPaygateDepositFromOrder) สร้างรายการฝาก ด้วย Paygate Order
// @Tags Payment Gateway - Report
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.PaygateOrderCreateDepositRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /paygate-report/create-deposit [post]
func (h paymentGatewayController) createPaygateDepositFromOrder(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	var body model.PaygateOrderCreateDepositRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	body.AdminId = adminId
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.paymentGatewayService.CreatePaygateDepositFromOrder(body); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "Updated success"})
}

// @Summary (ignorePaygateDepositFromOrder) ยกเลิก ไม่สนใจ ไม่ทำรายการฝาก จาก Paygate Order
// @Description (ignorePaygateDepositFromOrder) ยกเลิก ไม่สนใจ ไม่ทำรายการฝาก จาก Paygate Order
// @Tags Payment Gateway - Report
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.PaygateOrderIgnoreOrderRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /paygate-report/ignore-deposit [post]
func (h paymentGatewayController) ignorePaygateDepositFromOrder(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	var body model.PaygateOrderIgnoreOrderRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	body.AdminId = adminId
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.paymentGatewayService.IgnorePaygateDepositFromOrder(body); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "Updated success"})
}

// @Summary (getPaygateMerchantOption) ข้อมูลตัวเลือก Merchant ของ Paygate
// @Description (getPaygateMerchantOption) ข้อมูลตัวเลือก Merchant ของ Paygate
// @Tags Users Transaction
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} []model.SelectOptions
// @Failure 400 {object} handler.ErrorResponse
// @Router /user-transaction/options/paygate-merchant [get]
func (h paymentGatewayController) getPaygateMerchantOption(c *gin.Context) {

	data, err := h.paymentGatewayService.GetPaygateMerchantOption()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (createPaymentcoWebhook) สำหรับรับ Webhook จาก Paymentco
// @Description (createPaymentcoWebhook)
// @Tags Webhook Payment Gateway - All
// @Produce json
// @Param body body string true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /webhook/paymentco/callback [post]
func (h paymentGatewayController) createPaymentcoWebhook(c *gin.Context) {

	var createReq model.PaymentcoWebhookRequest
	createReq.JsonPayload = "{}"

	body := c.Request.Body
	responseData, err := io.ReadAll(body)
	if err != nil {
		log.Println(err)
	} else {
		createReq.JsonPayload = string(responseData)
	}

	if _, err := h.paygatePaymentcoService.CreatePaymentcoWebhook(createReq); err != nil {
		log.Println("createPaymentcoWebhook.ERROR", err.Error())
	}
	// c.JSON(201, model.Success{Message: "Created success"})
	// later ต้อง return อะไรบ้าง
	c.String(200, "ok")
}

// @Summary (createPaymentcoDeposit) สร้างรายการฝาก ด้วย Payment Gateway Paymentco
// @Description (createPaymentcoDeposit)
// @Tags WEB - Payment Gateway - Paymentco
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.PaymentcoDepositCreateRequest true "body"
// @Success 201 {object} model.PaymentcoOrderWebResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/paygate-paymentco/deposit [post]
func (h paymentGatewayController) createPaymentcoDeposit(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var body model.PaymentcoDepositCreateRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	body.UserId = userId
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	resp, err := h.paygatePaymentcoService.CreatePaymentcoDeposit(body)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, resp)
}

// @Summary (createPaymentcoWithdraw) สร้างรายการถอน ด้วย Payment Gateway Paymentco
// @Description (createPaymentcoWithdraw)
// @Tags WEB - Payment Gateway - Paymentco
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.PaymentcoWithdrawCreateRequest true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/paygate-paymentco/withdraw [post]
func (h paymentGatewayController) createPaymentcoWithdraw(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var body model.PaymentcoWithdrawCreateRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	body.UserId = userId
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	ginMode := os.Getenv("GIN_MODE")
	if ginMode == "debug" {
		insertId, err := h.paygatePaymentcoService.CreatePaymentcoWithdraw(body)
		if err != nil {
			HandleError(c, err)
			return
		}
		c.JSON(201, model.SuccessWithId{Message: "Created success", Id: *insertId})
	} else {
		// สำหรับ production ถ้ามีการใช้งานแสดงว่าหลุด
		if err := h.paygatePaymentcoService.CreateSystemLog("createPaymentcoWithdraw", nil); err != nil {
			HandleError(c, err)
			return
		}
		c.JSON(201, model.SuccessWithId{Message: "Created success", Id: 1})
	}
}

// @Summary (createZappayDepositWebhook) สำหรับรับ Webhook จาก Zappay
// @Description (createZappayDepositWebhook)
// @Tags Webhook Payment Gateway - All
// @Produce json
// @Param body body string true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /webhook/zappay/repay-callback [post]
func (h paymentGatewayController) createZappayDepositWebhook(c *gin.Context) {

	var createReq model.ZappayWebhookRequest
	createReq.JsonPayload = "{}"

	body := c.Request.Body
	responseData, err := io.ReadAll(body)
	if err != nil {
		log.Println(err)
	} else {
		createReq.JsonPayload = string(responseData)
	}

	if _, err := h.paygateZappayService.CreateZappayDepositWebhook(createReq); err != nil {
		log.Println("createZappayDepositWebhook.ERROR", err.Error())
	}
	// c.JSON(201, model.Success{Message: "Created success"})
	// later ต้อง return อะไรบ้าง
	c.String(200, "ok")
}

// @Summary (createZappayWithdrawWebhook) สำหรับรับ Webhook จาก Zappay
// @Description (createZappayWithdrawWebhook)
// @Tags Webhook Payment Gateway - All
// @Produce json
// @Param body body string true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /webhook/zappay/loan-callback [post]
func (h paymentGatewayController) createZappayWithdrawWebhook(c *gin.Context) {

	var createReq model.ZappayWebhookRequest
	createReq.JsonPayload = "{}"

	body := c.Request.Body
	responseData, err := io.ReadAll(body)
	if err != nil {
		log.Println(err)
	} else {
		createReq.JsonPayload = string(responseData)
	}

	if _, err := h.paygateZappayService.CreateZappayWithdrawWebhook(createReq); err != nil {
		log.Println("createZappayWithdrawWebhook.ERROR", err.Error())
	}
	// c.JSON(201, model.Success{Message: "Created success"})
	// later ต้อง return อะไรบ้าง
	c.String(200, "ok")
}

// @Summary (createZappayDeposit) สร้างรายการฝาก ด้วย Payment Gateway Zappay
// @Description (createZappayDeposit)
// @Tags WEB - Payment Gateway - Zappay
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.ZappayDepositCreateRequest true "body"
// @Success 201 {object} model.ZappayOrderWebResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/paygate-zappay/deposit [post]
func (h paymentGatewayController) createZappayDeposit(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var body model.ZappayDepositCreateRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	body.UserId = userId
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	resp, err := h.paygateZappayService.CreateZappayDeposit(body)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, resp)
}

// @Summary (createZappayWithdraw) สร้างรายการถอน ด้วย Payment Gateway Zappay
// @Description (createZappayWithdraw)
// @Tags WEB - Payment Gateway - Zappay
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.ZappayWithdrawCreateRequest true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/paygate-zappay/withdraw [post]
func (h paymentGatewayController) createZappayWithdraw(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var body model.ZappayWithdrawCreateRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	body.UserId = userId
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	ginMode := os.Getenv("GIN_MODE")
	if ginMode == "debug" {
		insertId, err := h.paygateZappayService.CreateZappayWithdraw(body)
		if err != nil {
			HandleError(c, err)
			return
		}
		c.JSON(201, model.SuccessWithId{Message: "Created success", Id: *insertId})
	} else {
		// สำหรับ production ถ้ามีการใช้งานแสดงว่าหลุด
		if err := h.paygateZappayService.CreateSystemLog("createZappayWithdraw", nil); err != nil {
			HandleError(c, err)
			return
		}
		c.JSON(201, model.SuccessWithId{Message: "Created success", Id: 1})
	}
}

// @Summary (createOnepayWebhook) สำหรับรับ Webhook จาก Onepay
// @Description (createOnepayWebhook)
// @Tags Webhook Payment Gateway - All
// @Produce json
// @Param body body string true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /webhook/onepay/callback [post]
func (h paymentGatewayController) createOnepayWebhook(c *gin.Context) {

	var createReq model.OnepayWebhookRequest
	createReq.JsonPayload = "{}"

	body := c.Request.Body
	responseData, err := io.ReadAll(body)
	if err != nil {
		log.Println(err)
	} else {
		createReq.JsonPayload = string(responseData)
	}

	if _, err := h.paygateOnepayService.CreateOnepayWebhook(createReq); err != nil {
		log.Println("createOnepayWebhook.ERROR", err.Error())
	}
	// c.JSON(201, model.Success{Message: "Created success"})
	// later ต้อง return อะไรบ้าง
	c.String(200, "ok")
}

// @Summary (createOnepayDepositWebhook) สำหรับรับ Webhook จาก Onepay
// @Description (createOnepayDepositWebhook)
// @Tags Webhook Payment Gateway - All
// @Produce json
// @Param body body string true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /webhook/onepay/dep-callback [post]
func (h paymentGatewayController) createOnepayDepositWebhook(c *gin.Context) {

	var createReq model.OnepayWebhookRequest
	createReq.JsonPayload = "{}"

	body := c.Request.Body
	responseData, err := io.ReadAll(body)
	if err != nil {
		log.Println(err)
	} else {
		createReq.JsonPayload = string(responseData)
	}

	if _, err := h.paygateOnepayService.CreateOnepayDepositWebhook(createReq); err != nil {
		log.Println("createOnepayDepositWebhook.ERROR", err.Error())
	}
	// c.JSON(201, model.Success{Message: "Created success"})
	// later ต้อง return อะไรบ้าง
	c.String(200, "ok")
}

// @Summary (createOnepayWithdrawWebhook) สำหรับรับ Webhook จาก Onepay
// @Description (createOnepayWithdrawWebhook)
// @Tags Webhook Payment Gateway - All
// @Produce json
// @Param body body string true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /webhook/onepay/wid-callback [post]
func (h paymentGatewayController) createOnepayWithdrawWebhook(c *gin.Context) {

	var createReq model.OnepayWebhookRequest
	createReq.JsonPayload = "{}"

	body := c.Request.Body
	responseData, err := io.ReadAll(body)
	if err != nil {
		log.Println(err)
	} else {
		createReq.JsonPayload = string(responseData)
	}

	if _, err := h.paygateOnepayService.CreateOnepayWithdrawWebhook(createReq); err != nil {
		log.Println("createOnepayWithdrawWebhook.ERROR", err.Error())
	}
	// c.JSON(201, model.Success{Message: "Created success"})
	// later ต้อง return อะไรบ้าง
	c.String(200, "ok")
}

// @Summary (createOnepayDeposit) สร้างรายการฝาก ด้วย Payment Gateway Onepay
// @Description (createOnepayDeposit)
// @Tags WEB - Payment Gateway - Onepay
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.OnepayDepositCreateRequest true "body"
// @Success 201 {object} model.OnepayOrderWebResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/paygate-onepay/deposit [post]
func (h paymentGatewayController) createOnepayDeposit(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var body model.OnepayDepositCreateRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	body.UserId = userId
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	resp, err := h.paygateOnepayService.CreateOnepayDeposit(body)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, resp)
}

// @Summary (createOnepayWithdraw) สร้างรายการถอน ด้วย Payment Gateway Onepay
// @Description (createOnepayWithdraw)
// @Tags WEB - Payment Gateway - Onepay
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.OnepayWithdrawCreateRequest true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/paygate-onepay/withdraw [post]
func (h paymentGatewayController) createOnepayWithdraw(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var body model.OnepayWithdrawCreateRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	body.UserId = userId
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	ginMode := os.Getenv("GIN_MODE")
	if ginMode == "debug" {
		insertId, err := h.paygateOnepayService.CreateOnepayWithdraw(body)
		if err != nil {
			HandleError(c, err)
			return
		}
		c.JSON(201, model.SuccessWithId{Message: "Created success", Id: *insertId})
	} else {
		// สำหรับ production ถ้ามีการใช้งานแสดงว่าหลุด
		if err := h.paygateOnepayService.CreateSystemLog("createOnepayWithdraw", nil); err != nil {
			HandleError(c, err)
			return
		}
		c.JSON(201, model.SuccessWithId{Message: "Created success", Id: 1})
	}
}

// @Summary (createFlashpayWebhook) สำหรับรับ Webhook จาก Flashpay
// @Description (createFlashpayWebhook)
// @Tags Webhook Payment Gateway - All
// @Produce json
// @Param body body string true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /webhook/flashpay/callback [post]
func (h paymentGatewayController) createFlashpayWebhook(c *gin.Context) {

	var createReq model.FlashpayWebhookRequest
	createReq.JsonPayload = "{}"

	body := c.Request.Body
	responseData, err := io.ReadAll(body)
	if err != nil {
		log.Println(err)
	} else {
		createReq.JsonPayload = string(responseData)
	}

	if _, err := h.paygateFlashpayService.CreateFlashpayWebhook(createReq); err != nil {
		log.Println("createFlashpayWebhook.ERROR", err.Error())
	}
	// c.JSON(201, model.Success{Message: "Created success"})
	// later ต้อง return อะไรบ้าง
	c.String(200, "ok")
}

// @Summary (createFlashpayDepositWebhook) สำหรับรับ Webhook จาก Flashpay
// @Description (createFlashpayDepositWebhook)
// @Tags Webhook Payment Gateway - All
// @Produce json
// @Param body body string true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /webhook/flashpay/dep-callback [post]
func (h paymentGatewayController) createFlashpayDepositWebhook(c *gin.Context) {

	var createReq model.FlashpayWebhookRequest
	createReq.JsonPayload = "{}"

	body := c.Request.Body
	responseData, err := io.ReadAll(body)
	if err != nil {
		log.Println(err)
	} else {
		createReq.JsonPayload = string(responseData)
	}

	if _, err := h.paygateFlashpayService.CreateFlashpayDepositWebhook(createReq); err != nil {
		log.Println("createFlashpayDepositWebhook.ERROR", err.Error())
	}
	// c.JSON(201, model.Success{Message: "Created success"})
	// later ต้อง return อะไรบ้าง
	c.String(200, "ok")
}

// @Summary (createFlashpayWithdrawWebhook) สำหรับรับ Webhook จาก Flashpay
// @Description (createFlashpayWithdrawWebhook)
// @Tags Webhook Payment Gateway - All
// @Produce json
// @Param body body string true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /webhook/flashpay/wid-callback [post]
func (h paymentGatewayController) createFlashpayWithdrawWebhook(c *gin.Context) {

	var createReq model.FlashpayWebhookRequest
	createReq.JsonPayload = "{}"

	body := c.Request.Body
	responseData, err := io.ReadAll(body)
	if err != nil {
		log.Println(err)
	} else {
		createReq.JsonPayload = string(responseData)
	}

	if _, err := h.paygateFlashpayService.CreateFlashpayWithdrawWebhook(createReq); err != nil {
		log.Println("createFlashpayWithdrawWebhook.ERROR", err.Error())
	}
	// c.JSON(201, model.Success{Message: "Created success"})
	// later ต้อง return อะไรบ้าง
	c.String(200, "ok")
}

// @Summary (createFlashpayDeposit) สร้างรายการฝาก ด้วย Payment Gateway Flashpay
// @Description (createFlashpayDeposit)
// @Tags WEB - Payment Gateway - Flashpay
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.FlashpayDepositCreateRequest true "body"
// @Success 201 {object} model.FlashpayOrderWebResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/paygate-flashpay/deposit [post]
func (h paymentGatewayController) createFlashpayDeposit(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var body model.FlashpayDepositCreateRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	body.UserId = userId
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	resp, err := h.paygateFlashpayService.CreateFlashpayDeposit(body)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, resp)
}

// @Summary (createFlashpayWithdraw) สร้างรายการถอน ด้วย Payment Gateway Flashpay
// @Description (createFlashpayWithdraw)
// @Tags WEB - Payment Gateway - Flashpay
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.FlashpayWithdrawCreateRequest true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/paygate-flashpay/withdraw [post]
func (h paymentGatewayController) createFlashpayWithdraw(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var body model.FlashpayWithdrawCreateRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	body.UserId = userId
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	ginMode := os.Getenv("GIN_MODE")
	if ginMode == "debug" {
		insertId, err := h.paygateFlashpayService.CreateFlashpayWithdraw(body)
		if err != nil {
			HandleError(c, err)
			return
		}
		c.JSON(201, model.SuccessWithId{Message: "Created success", Id: *insertId})
	} else {
		// สำหรับ production ถ้ามีการใช้งานแสดงว่าหลุด
		if err := h.paygateFlashpayService.CreateSystemLog("createFlashpayWithdraw", nil); err != nil {
			HandleError(c, err)
			return
		}
		c.JSON(201, model.SuccessWithId{Message: "Created success", Id: 1})
	}
}

// @Summary (createBizpayWebhook) สำหรับรับ Webhook จาก Bizpay
// @Description (createBizpayWebhook)
// @Tags Webhook Payment Gateway - All
// @Produce json
// @Param body body string true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /webhook/bizpay/callback [post]
func (h paymentGatewayController) createBizpayWebhook(c *gin.Context) {

	var createReq model.BizpayWebhookRequest
	createReq.JsonPayload = "{}"

	body := c.Request.Body
	responseData, err := io.ReadAll(body)
	if err != nil {
		log.Println(err)
	} else {
		createReq.JsonPayload = string(responseData)
	}

	if _, err := h.paygateBizpayService.CreateBizpayWebhook(createReq); err != nil {
		log.Println("createBizpayWebhook.ERROR", err.Error())
	}
	// c.JSON(201, model.Success{Message: "Created success"})
	// later ต้อง return อะไรบ้าง
	c.String(200, "ok")
}

// @Summary (createBizpayDepositWebhook) สำหรับรับ Webhook จาก Bizpay
// @Description (createBizpayDepositWebhook)
// @Tags Webhook Payment Gateway - All
// @Produce json
// @Param body body string true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /webhook/bizpay/dep-callback [post]
func (h paymentGatewayController) createBizpayDepositWebhook(c *gin.Context) {

	var createReq model.BizpayWebhookRequest
	createReq.JsonPayload = "{}"

	body := c.Request.Body
	responseData, err := io.ReadAll(body)
	if err != nil {
		log.Println(err)
	} else {
		createReq.JsonPayload = string(responseData)
	}

	if _, err := h.paygateBizpayService.CreateBizpayDepositWebhook(createReq); err != nil {
		log.Println("createBizpayDepositWebhook.ERROR", err.Error())
	}
	// c.JSON(201, model.Success{Message: "Created success"})
	// later ต้อง return อะไรบ้าง
	c.String(200, "ok")
}

// @Summary (createBizpayWithdrawWebhook) สำหรับรับ Webhook จาก Bizpay
// @Description (createBizpayWithdrawWebhook)
// @Tags Webhook Payment Gateway - All
// @Produce json
// @Param body body string true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /webhook/bizpay/wid-callback [post]
func (h paymentGatewayController) createBizpayWithdrawWebhook(c *gin.Context) {

	var createReq model.BizpayWebhookRequest
	createReq.JsonPayload = "{}"

	body := c.Request.Body
	responseData, err := io.ReadAll(body)
	if err != nil {
		log.Println(err)
	} else {
		createReq.JsonPayload = string(responseData)
	}

	if _, err := h.paygateBizpayService.CreateBizpayWithdrawWebhook(createReq); err != nil {
		log.Println("createBizpayWithdrawWebhook.ERROR", err.Error())
	}
	// c.JSON(201, model.Success{Message: "Created success"})
	// later ต้อง return อะไรบ้าง
	c.String(200, "ok")
}

// @Summary (createBizpayDeposit) สร้างรายการฝาก ด้วย Payment Gateway Bizpay
// @Description (createBizpayDeposit)
// @Tags WEB - Payment Gateway - Bizpay
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.BizpayDepositCreateRequest true "body"
// @Success 201 {object} model.BizpayOrderWebResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/paygate-bizpay/deposit [post]
func (h paymentGatewayController) createBizpayDeposit(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var body model.BizpayDepositCreateRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	body.UserId = userId
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	resp, err := h.paygateBizpayService.CreateBizpayDeposit(body)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, resp)
}

// @Summary (createBizpayWithdraw) สร้างรายการถอน ด้วย Payment Gateway Bizpay
// @Description (createBizpayWithdraw)
// @Tags WEB - Payment Gateway - Bizpay
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.BizpayWithdrawCreateRequest true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/paygate-bizpay/withdraw [post]
func (h paymentGatewayController) createBizpayWithdraw(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var body model.BizpayWithdrawCreateRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	body.UserId = userId
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	ginMode := os.Getenv("GIN_MODE")
	if ginMode == "debug" {
		insertId, err := h.paygateBizpayService.CreateBizpayWithdraw(body)
		if err != nil {
			HandleError(c, err)
			return
		}
		c.JSON(201, model.SuccessWithId{Message: "Created success", Id: *insertId})
	} else {
		// สำหรับ production ถ้ามีการใช้งานแสดงว่าหลุด
		if err := h.paygateBizpayService.CreateSystemLog("createBizpayWithdraw", nil); err != nil {
			HandleError(c, err)
			return
		}
		c.JSON(201, model.SuccessWithId{Message: "Created success", Id: 1})
	}
}

// @Summary (createSugarpayDepositWebhook) สำหรับรับ Webhook จาก Sugarpay
// @Description (createSugarpayDepositWebhook)
// @Tags Webhook Payment Gateway - All
// @Produce json
// @Param body body string true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /webhook/sugarpay/dep-callback [post]
func (h paymentGatewayController) createSugarpayDepositWebhook(c *gin.Context) {

	var createReq model.SugarpayWebhookRequest
	createReq.JsonPayload = "{}"

	body := c.Request.Body
	responseData, err := io.ReadAll(body)
	if err != nil {
		log.Println(err)
	} else {
		createReq.JsonPayload = string(responseData)
	}

	if _, err := h.paygateSugarpayService.CreateSugarpayDepositWebhook(createReq); err != nil {
		log.Println("createSugarpayDepositWebhook.ERROR", err.Error())
	}
	// Merchant Please return response JSON like this
	// JSON
	// {
	//    "status": "OK",
	// }
	c.String(200, helper.StructJson(model.SugarpayWebhookReturnResponse{Status: "OK"}))
}

// @Summary (createSugarpayWithdrawWebhook) สำหรับรับ Webhook จาก Sugarpay
// @Description (createSugarpayWithdrawWebhook)
// @Tags Webhook Payment Gateway - All
// @Produce json
// @Param body body string true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /webhook/sugarpay/wid-callback [post]
func (h paymentGatewayController) createSugarpayWithdrawWebhook(c *gin.Context) {

	var createReq model.SugarpayWebhookRequest
	createReq.JsonPayload = "{}"

	body := c.Request.Body
	responseData, err := io.ReadAll(body)
	if err != nil {
		log.Println(err)
	} else {
		createReq.JsonPayload = string(responseData)
	}

	if _, err := h.paygateSugarpayService.CreateSugarpayWithdrawWebhook(createReq); err != nil {
		log.Println("createSugarpayWithdrawWebhook.ERROR", err.Error())
	}
	// Merchant Please return response JSON like this
	// JSON
	// {
	//    "status": "OK",
	// }
	c.String(200, helper.StructJson(model.SugarpayWebhookReturnResponse{Status: "OK"}))
}

// @Summary (createSugarpayDeposit) สร้างรายการฝาก ด้วย Payment Gateway Sugarpay
// @Description (createSugarpayDeposit)
// @Tags WEB - Payment Gateway - Sugarpay
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.SugarpayDepositCreateRequest true "body"
// @Success 201 {object} model.SugarpayOrderWebResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/paygate-sugarpay/deposit [post]
func (h paymentGatewayController) createSugarpayDeposit(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var body model.SugarpayDepositCreateRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	body.UserId = userId
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	resp, err := h.paygateSugarpayService.CreateSugarpayDeposit(body)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, resp)
}

// @Summary (createSugarpayWithdraw) สร้างรายการถอน ด้วย Payment Gateway Sugarpay
// @Description (createSugarpayWithdraw)
// @Tags WEB - Payment Gateway - Sugarpay
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.SugarpayWithdrawCreateRequest true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/paygate-sugarpay/withdraw [post]
func (h paymentGatewayController) createSugarpayWithdraw(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var body model.SugarpayWithdrawCreateRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	body.UserId = userId
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	ginMode := os.Getenv("GIN_MODE")
	if ginMode == "debug" {
		insertId, err := h.paygateSugarpayService.CreateSugarpayWithdraw(body)
		if err != nil {
			HandleError(c, err)
			return
		}
		c.JSON(201, model.SuccessWithId{Message: "Created success", Id: *insertId})
	} else {
		// สำหรับ production ถ้ามีการใช้งานแสดงว่าหลุด
		if err := h.paygateSugarpayService.CreateSystemLog("createSugarpayWithdraw", nil); err != nil {
			HandleError(c, err)
			return
		}
		c.JSON(201, model.SuccessWithId{Message: "Created success", Id: 1})
	}
}

// @Summary (createZmanpayDepositWebhook) สำหรับรับ Webhook จาก Zmanpay
// @Description (createZmanpayDepositWebhook)
// @Tags Webhook Payment Gateway - All
// @Produce json
// @Param body body string true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /webhook/zmanpay/dep-callback [post]
func (h paymentGatewayController) createZmanpayDepositWebhook(c *gin.Context) {

	var createReq model.ZmanpayWebhookRequest
	createReq.JsonPayload = "{}"

	body := c.Request.Body
	responseData, err := io.ReadAll(body)
	if err != nil {
		log.Println(err)
	} else {
		createReq.JsonPayload = string(responseData)
	}

	if _, err := h.paygateZmanpayService.CreateZmanpayDepositWebhook(createReq); err != nil {
		log.Println("createZmanpayDepositWebhook.ERROR", err.Error())
	}
	// Merchant Please return response JSON like this
	// JSON
	// {
	//    "status": "OK",
	// }
	c.String(200, helper.StructJson(model.ZmanpayWebhookReturnResponse{Status: "OK"}))
}

// @Summary (createZmanpayWithdrawWebhook) สำหรับรับ Webhook จาก Zmanpay
// @Description (createZmanpayWithdrawWebhook)
// @Tags Webhook Payment Gateway - All
// @Produce json
// @Param body body string true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /webhook/zmanpay/wid-callback [post]
func (h paymentGatewayController) createZmanpayWithdrawWebhook(c *gin.Context) {

	var createReq model.ZmanpayWebhookRequest
	createReq.JsonPayload = "{}"

	body := c.Request.Body
	responseData, err := io.ReadAll(body)
	if err != nil {
		log.Println(err)
	} else {
		createReq.JsonPayload = string(responseData)
	}

	if _, err := h.paygateZmanpayService.CreateZmanpayWithdrawWebhook(createReq); err != nil {
		log.Println("createZmanpayWithdrawWebhook.ERROR", err.Error())
	}
	c.String(200, helper.StructJson(model.ZmanpayWebhookReturnResponse{Status: "OK"}))
}

// @Summary (getPendingZmanpayDepositOrder) ดึงข้อมูลไปเช็ตว่ามีรายการฝากค้างอยู่หรือไม่
// @Description (getPendingZmanpayDepositOrder)
// @Tags WEB - Payment Gateway - Zmanpay
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 201 {object} model.ZmanpayOrderWebResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/paygate-zmanpay/pending-deposit [get]
func (h paymentGatewayController) getPendingZmanpayDepositOrder(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	resp, err := h.paygateZmanpayService.GetPendingZmanpayDepositOrder(userId)
	if err != nil {
		HandleWebError(c, err)
		return
	}
	c.JSON(200, resp)
}

// @Summary (cancelZmanpayDeposit) ถ้ามีรายการฝากค้างอยู่ สามารถยกเลิกได้
// @Description (cancelZmanpayDeposit)
// @Tags WEB - Payment Gateway - Zmanpay
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.ZmanpayDepositCancelRequest true "body"
// @Success 201 {object} model.ZmanpayOrderWebResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/paygate-zmanpay/cancel-deposit [post]
func (h paymentGatewayController) cancelZmanpayDeposit(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var body model.ZmanpayDepositCancelRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleWebError(c, err)
		return
	}
	body.UserId = userId
	if err := validator.New().Struct(body); err != nil {
		HandleWebError(c, err)
		return
	}

	if err := h.paygateZmanpayService.CancelZmanpayDeposit(body); err != nil {
		HandleWebError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "ยกเลิกรายการฝากสำเร็จ"})
}

// @Summary (createZmanpayDeposit) สร้างรายการฝาก ด้วย Payment Gateway Zmanpay
// @Description (createZmanpayDeposit)
// @Tags WEB - Payment Gateway - Zmanpay
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.ZmanpayDepositCreateRequest true "body"
// @Success 201 {object} model.ZmanpayOrderWebResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/paygate-zmanpay/deposit [post]
func (h paymentGatewayController) createZmanpayDeposit(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var body model.ZmanpayDepositCreateRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleWebError(c, err)
		return
	}
	body.UserId = userId
	if err := validator.New().Struct(body); err != nil {
		HandleWebError(c, err)
		return
	}

	resp, err := h.paygateZmanpayService.CreateZmanpayDeposit(body)
	if err != nil {
		HandleWebError(c, err)
		return
	}
	c.JSON(201, resp)
}

// @Summary (createZmanpayWithdraw) สร้างรายการถอน ด้วย Payment Gateway Zmanpay
// @Description (createZmanpayWithdraw)
// @Tags WEB - Payment Gateway - Zmanpay
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.ZmanpayWithdrawCreateRequest true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/paygate-zmanpay/withdraw [post]
func (h paymentGatewayController) createZmanpayWithdraw(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var body model.ZmanpayWithdrawCreateRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleWebError(c, err)
		return
	}
	body.UserId = userId
	if err := validator.New().Struct(body); err != nil {
		HandleWebError(c, err)
		return
	}

	ginMode := os.Getenv("GIN_MODE")
	if ginMode == "debug" {
		insertId, err := h.paygateZmanpayService.CreateZmanpayWithdraw(body)
		if err != nil {
			HandleWebError(c, err)
			return
		}
		c.JSON(201, model.SuccessWithId{Message: "Created success", Id: *insertId})
	} else {
		// สำหรับ production ถ้ามีการใช้งานแสดงว่าหลุด
		if err := h.paygateZmanpayService.CreateSystemLog("createZmanpayWithdraw", nil); err != nil {
			HandleWebError(c, err)
			return
		}
		c.JSON(201, model.SuccessWithId{Message: "Created success", Id: 1})
	}
}

// @Summary (createPostmanPayDepositWebhook) สำหรับรับ Webhook จาก PostmanPay
// @Description (createPostmanPayDepositWebhook)
// @Tags Webhook Payment Gateway - All
// @Produce json
// @Param body body string true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /webhook/postmanpay/dep-callback [post]
func (h paymentGatewayController) createPostmanPayDepositWebhook(c *gin.Context) {

	var createReq model.PostmanPayWebhookRequest
	createReq.JsonPayload = "{}"

	body := c.Request.Body
	responseData, err := io.ReadAll(body)
	if err != nil {
		log.Println(err)
	} else {
		createReq.JsonPayload = string(responseData)
	}

	if _, err := h.paygatePostmanPayService.CreatePostmanPayDepositWebhook(createReq); err != nil {
		log.Println("createPostmanPayDepositWebhook.ERROR", err.Error())
	}
	c.String(200, "ok")
}

// @Summary (createPostmanPayWithdrawWebhook) สำหรับรับ Webhook จาก PostmanPay
// @Description (createPostmanPayWithdrawWebhook)
// @Tags Webhook Payment Gateway - All
// @Produce json
// @Param body body string true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /webhook/postmanpay/wid-callback [post]
func (h paymentGatewayController) createPostmanPayWithdrawWebhook(c *gin.Context) {

	var createReq model.PostmanPayWebhookRequest
	createReq.JsonPayload = "{}"

	body := c.Request.Body
	responseData, err := io.ReadAll(body)
	if err != nil {
		log.Println(err)
	} else {
		createReq.JsonPayload = string(responseData)
	}

	if _, err := h.paygatePostmanPayService.CreatePostmanPayWithdrawWebhook(createReq); err != nil {
		log.Println("createPostmanPayWithdrawWebhook.ERROR", err.Error())
	}
	c.String(200, "ok")
}

// @Summary (createPostmanPayDeposit) สร้างรายการฝาก ด้วย Payment Gateway PostmanPay
// @Description (createPostmanPayDeposit)
// @Tags WEB - Payment Gateway - PostmanPay
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.PostmanPayDepositCreateRequest true "body"
// @Success 201 {object} model.PostmanPayOrderWebResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/paygate-postmanpay/deposit [post]
func (h paymentGatewayController) createPostmanPayDeposit(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var body model.PostmanPayDepositCreateRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleWebError(c, err)
		return
	}
	body.UserId = userId
	if err := validator.New().Struct(body); err != nil {
		HandleWebError(c, err)
		return
	}

	resp, err := h.paygatePostmanPayService.CreatePostmanPayDeposit(body)
	if err != nil {
		HandleWebError(c, err)
		return
	}
	c.JSON(201, resp)
}

// @Summary (createPostmanPayWithdraw) สร้างรายการถอน ด้วย Payment Gateway PostmanPay
// @Description (createPostmanPayWithdraw)
// @Tags WEB - Payment Gateway - PostmanPay
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.PostmanPayWithdrawCreateRequest true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/paygate-postmanpay/withdraw [post]
func (h paymentGatewayController) createPostmanPayWithdraw(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var body model.PostmanPayWithdrawCreateRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleWebError(c, err)
		return
	}
	body.UserId = userId
	if err := validator.New().Struct(body); err != nil {
		HandleWebError(c, err)
		return
	}

	ginMode := os.Getenv("GIN_MODE")
	if ginMode == "debug" {
		insertId, err := h.paygatePostmanPayService.CreatePostmanPayWithdraw(body)
		if err != nil {
			HandleWebError(c, err)
			return
		}
		c.JSON(201, model.SuccessWithId{Message: "Created success", Id: *insertId})
	} else {
		// สำหรับ production ถ้ามีการใช้งานแสดงว่าหลุด
		if err := h.paygatePostmanPayService.CreateSystemLog("createPostmanPayWithdraw", nil); err != nil {
			HandleWebError(c, err)
			return
		}
		c.JSON(201, model.SuccessWithId{Message: "Created success", Id: 1})
	}
}

// @Summary (getPaygateProviderLimitList) ข้อมูลตัวเลือก Payment Gateway สำหรับเพิ่มข้อมูลบัญชีใหม่
// @Description (getPaygateProviderLimitList) ข้อมูลตัวเลือก Payment Gateway สำหรับเพิ่มข้อมูลบัญชีใหม่
// @Tags Payment Gateway - Multiple Providers
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} []model.PaygateMerchantLimitResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /payment-gateways/provider-limit-list [get]
func (h paymentGatewayController) getPaygateProviderLimitList(c *gin.Context) {

	data, err := h.paymentGatewayService.GetPaygateMerchantLimitList()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (getPaygateProviderOptions) ข้อมูลตัวเลือก Payment Gateway สำหรับเพิ่มข้อมูลบัญชีใหม่
// @Description (getPaygateProviderOptions) ข้อมูลตัวเลือก Payment Gateway สำหรับเพิ่มข้อมูลบัญชีใหม่
// @Tags Payment Gateway - Multiple Providers
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} []model.SelectOptions
// @Failure 400 {object} handler.ErrorResponse
// @Router /payment-gateways/provider-options [get]
func (h paymentGatewayController) getPaygateProviderOptions(c *gin.Context) {

	data, err := h.paymentGatewayService.GetNonActivePaygateMerchantOption()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (getPaygateAccountList) ดึงข้อมูลการตั้งค่าการใช้งาน บัญชี Payment Gateway
// @Description (getPaygateAccountList)
// @Tags Payment Gateway - Multiple Providers
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.PaygateAccountListRequest true "PaygateAccountListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /payment-gateways/account-list [get]
func (h paymentGatewayController) getPaygateAccountList(c *gin.Context) {

	var query model.PaygateAccountListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	paging, err := h.paymentGatewayService.GetPaygateAccountList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, paging)
}

// @Summary (getPaygateAccountById) ดึงข้อมูลการตั้งค่าการใช้งาน บัญชี Payment Gateway ตาม ID
// @Description (getPaygateAccountById)
// @Tags Payment Gateway - Multiple Providers
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 200 {object} model.PaygateHengOrderResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /payment-gateways/account-detail/{id} [get]
func (h paymentGatewayController) getPaygateAccountById(c *gin.Context) {

	var query model.GetByIdRequest
	if err := c.ShouldBindUri(&query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.paymentGatewayService.GetPaygateAccountById(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (createPaygateAccount)
// @Description (createPaygateAccount)
// @Tags Payment Gateway - Multiple Providers
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.PaygateAccountCreateRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /payment-gateways/account-create [post]
func (h paymentGatewayController) createPaygateAccount(c *gin.Context) {

	var body model.PaygateAccountCreateRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	insertId, err := h.paymentGatewayService.CreatePaygateAccount(body)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.SuccessWithId{Message: "Created success", Id: *insertId})
}

// @Summary (updatePaygateAccount)
// @Description (updatePaygateAccount)
// @Tags Payment Gateway - Multiple Providers
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Param body body model.PaygateAccountUpdateRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /payment-gateways/account-update/{id} [patch]
func (h paymentGatewayController) updatePaygateAccount(c *gin.Context) {

	identifier, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		HandleError(c, err)
		return
	}

	body := model.PaygateAccountUpdateRequest{}
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.paymentGatewayService.UpdatePaygateAccount(identifier, body); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "Updated success"})
}

// @Summary (deletePaygateAccount)
// @Description (deletePaygateAccount)
// @Tags Payment Gateway - Multiple Providers
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /payment-gateways/account-delete/{id} [delete]
func (h paymentGatewayController) deletePaygateAccount(c *gin.Context) {

	identifier, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		HandleError(c, err)
		return
	}

	if err := h.paymentGatewayService.DeletePaygateAccount(identifier); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "Deleted success"})
}

// @Summary (createMazepayDepositWebhook) สำหรับรับ Webhook จาก Mazepay
// @Description (createMazepayDepositWebhook)
// @Tags Webhook Payment Gateway - All
// @Produce json
// @Param body body string true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /webhook/mazepay/dep-callback [post]
func (h paymentGatewayController) createMazepayDepositWebhook(c *gin.Context) {

	var createReq model.MazepayWebhookRequest
	createReq.JsonPayload = "{}"

	body := c.Request.Body
	responseData, err := io.ReadAll(body)
	if err != nil {
		log.Println(err)
	} else {
		createReq.JsonPayload = string(responseData)
	}

	if _, err := h.paygateMazepayService.CreateMazepayDepositWebhook(createReq); err != nil {
		log.Println("createMazepayDepositWebhook.ERROR", err.Error())
	}
	c.String(200, helper.StructJson(model.MazepayWebhookReturnResponse{Status: "OK"}))
}

// @Summary (createMazepayWithdrawWebhook) สำหรับรับ Webhook จาก Mazepay
// @Description (createMazepayWithdrawWebhook)
// @Tags Webhook Payment Gateway - All
// @Produce json
// @Param body body string true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /webhook/mazepay/wid-callback [post]
func (h paymentGatewayController) createMazepayWithdrawWebhook(c *gin.Context) {

	var createReq model.MazepayWebhookRequest
	createReq.JsonPayload = "{}"

	body := c.Request.Body
	responseData, err := io.ReadAll(body)
	if err != nil {
		log.Println(err)
	} else {
		createReq.JsonPayload = string(responseData)
	}

	if _, err := h.paygateMazepayService.CreateMazepayWithdrawWebhook(createReq); err != nil {
		log.Println("createMazepayWithdrawWebhook.ERROR", err.Error())
	}
	c.String(200, helper.StructJson(model.MazepayWebhookReturnResponse{Status: "OK"}))
}

// @Summary (getPendingMazepayDepositOrder) ดึงข้อมูลไปเช็ตว่ามีรายการฝากค้างอยู่หรือไม่
// @Description (getPendingMazepayDepositOrder) ถ้าไม่มี จะเป็นข้อมูล เปล่าๆ ยอดเป็น 0 id เป็น 0
// @Tags WEB - Payment Gateway - Mazepay
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 201 {object} model.MazepayOrderWebResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/paygate-mazepay/pending-deposit [get]
func (h paymentGatewayController) getPendingMazepayDepositOrder(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	resp, err := h.paygateMazepayService.GetPendingMazepayDepositOrder(userId)
	if err != nil {
		HandleWebError(c, err)
		return
	}
	c.JSON(200, resp)
}

// @Summary (cancelMazepayDeposit) ถ้ามีรายการฝากค้างอยู่ สามารถยกเลิกได้
// @Description (cancelMazepayDeposit)
// @Tags WEB - Payment Gateway - Mazepay
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.MazepayDepositCancelRequest true "body"
// @Success 201 {object} model.MazepayOrderWebResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/paygate-mazepay/cancel-deposit [post]
func (h paymentGatewayController) cancelMazepayDeposit(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var body model.MazepayDepositCancelRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleWebError(c, err)
		return
	}
	body.UserId = userId
	if err := validator.New().Struct(body); err != nil {
		HandleWebError(c, err)
		return
	}

	if err := h.paygateMazepayService.CancelMazepayDeposit(body); err != nil {
		HandleWebError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "ยกเลิกรายการฝากสำเร็จ"})
}

// @Summary (createMazepayDeposit) สร้างรายการฝาก ด้วย Payment Gateway Mazepay
// @Description (createMazepayDeposit)
// @Tags WEB - Payment Gateway - Mazepay
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.MazepayDepositCreateRequest true "body"
// @Success 201 {object} model.MazepayOrderWebResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/paygate-mazepay/deposit [post]
func (h paymentGatewayController) createMazepayDeposit(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var body model.MazepayDepositCreateRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleWebError(c, err)
		return
	}
	body.UserId = userId
	if err := validator.New().Struct(body); err != nil {
		HandleWebError(c, err)
		return
	}

	resp, err := h.paygateMazepayService.CreateMazepayDeposit(body)
	if err != nil {
		HandleWebError(c, err)
		return
	}
	c.JSON(201, resp)
}

// @Summary (createMazepayWithdraw) สร้างรายการถอน ด้วย Payment Gateway Mazepay
// @Description (createMazepayWithdraw)
// @Tags WEB - Payment Gateway - Mazepay
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.MazepayWithdrawCreateRequest true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/paygate-mazepay/withdraw [post]
func (h paymentGatewayController) createMazepayWithdraw(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var body model.MazepayWithdrawCreateRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleWebError(c, err)
		return
	}
	body.UserId = userId
	if err := validator.New().Struct(body); err != nil {
		HandleWebError(c, err)
		return
	}

	ginMode := os.Getenv("GIN_MODE")
	if ginMode == "debug" {
		insertId, err := h.paygateMazepayService.CreateMazepayWithdraw(body)
		if err != nil {
			HandleWebError(c, err)
			return
		}
		c.JSON(201, model.SuccessWithId{Message: "Created success", Id: *insertId})
	} else {
		// สำหรับ production ถ้ามีการใช้งานแสดงว่าหลุด
		if err := h.paygateMazepayService.CreateSystemLog("createMazepayWithdraw", nil); err != nil {
			HandleWebError(c, err)
			return
		}
		c.JSON(201, model.SuccessWithId{Message: "Created success", Id: 1})
	}
}

// @Summary (createMeepayDepositWebhook) สำหรับรับ Webhook จาก Meepay
// @Description (createMeepayDepositWebhook)
// @Tags Webhook Payment Gateway - All
// @Produce json
// @Param body body string true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /webhook/meepay/dep-callback [post]
func (h paymentGatewayController) createMeepayDepositWebhook(c *gin.Context) {

	var createReq model.MeepayWebhookRequest
	createReq.JsonPayload = "{}"

	body := c.Request.Body
	responseData, err := io.ReadAll(body)
	if err != nil {
		log.Println(err)
	} else {
		createReq.JsonPayload = string(responseData)
	}

	if _, err := h.paygateMeepayService.CreateMeepayDepositWebhook(createReq); err != nil {
		log.Println("createMeepayDepositWebhook.ERROR", err.Error())
	}
	c.String(200, helper.StructJson(model.MeepayWebhookReturnResponse{Status: "OK"}))
}

// @Summary (createMeepayWithdrawWebhook) สำหรับรับ Webhook จาก Meepay
// @Description (createMeepayWithdrawWebhook)
// @Tags Webhook Payment Gateway - All
// @Produce json
// @Param body body string true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /webhook/meepay/wid-callback [post]
func (h paymentGatewayController) createMeepayWithdrawWebhook(c *gin.Context) {

	var createReq model.MeepayWebhookRequest
	createReq.JsonPayload = "{}"

	body := c.Request.Body
	responseData, err := io.ReadAll(body)
	if err != nil {
		log.Println(err)
	} else {
		createReq.JsonPayload = string(responseData)
	}

	if _, err := h.paygateMeepayService.CreateMeepayWithdrawWebhook(createReq); err != nil {
		log.Println("createMeepayWithdrawWebhook.ERROR", err.Error())
	}
	c.String(200, helper.StructJson(model.MeepayWebhookReturnResponse{Status: "OK"}))
}

// @Summary (createMeepayDeposit) สร้างรายการฝาก ด้วย Payment Gateway Meepay
// @Description (createMeepayDeposit)
// @Tags WEB - Payment Gateway - Meepay
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.MeepayDepositCreateRequest true "body"
// @Success 201 {object} model.MeepayOrderWebResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/paygate-meepay/deposit [post]
func (h paymentGatewayController) createMeepayDeposit(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var body model.MeepayDepositCreateRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleWebError(c, err)
		return
	}
	body.UserId = userId
	if err := validator.New().Struct(body); err != nil {
		HandleWebError(c, err)
		return
	}

	resp, err := h.paygateMeepayService.CreateMeepayDeposit(body)
	if err != nil {
		HandleWebError(c, err)
		return
	}
	c.JSON(201, resp)
}

// @Summary (createMeepayWithdraw) สร้างรายการถอน ด้วย Payment Gateway Meepay
// @Description (createMeepayWithdraw)
// @Tags WEB - Payment Gateway - Meepay
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.MeepayWithdrawCreateRequest true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/paygate-meepay/withdraw [post]
func (h paymentGatewayController) createMeepayWithdraw(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var body model.MeepayWithdrawCreateRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleWebError(c, err)
		return
	}
	body.UserId = userId
	if err := validator.New().Struct(body); err != nil {
		HandleWebError(c, err)
		return
	}

	ginMode := os.Getenv("GIN_MODE")
	if ginMode == "debug" {
		insertId, err := h.paygateMeepayService.CreateMeepayWithdraw(body)
		if err != nil {
			HandleWebError(c, err)
			return
		}
		c.JSON(201, model.SuccessWithId{Message: "Created success", Id: *insertId})
	} else {
		// สำหรับ production ถ้ามีการใช้งานแสดงว่าหลุด
		if err := h.paygateMeepayService.CreateSystemLog("createMeepayWithdraw", nil); err != nil {
			HandleWebError(c, err)
			return
		}
		c.JSON(201, model.SuccessWithId{Message: "Created success", Id: 1})
	}
}
