package handler

import (
	"cybergame-api/helper"
	"cybergame-api/middleware"
	"cybergame-api/model"
	"cybergame-api/service"

	"cybergame-api/repository"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"gorm.io/gorm"
)

type marketingController struct {
	marketingService service.MarketingService
}

func newMarketingController(
	marketingService service.MarketingService,
) marketingController {
	return marketingController{marketingService}
}

func MarketingController(r *gin.RouterGroup, db *gorm.DB) {

	singleSession := middleware.SingleSession(db)

	repo := repository.NewMarketingRepository(db)
	activityLusckyWheelRepo := repository.NewActivityLuckyWheelRepository(db)
	serviceRepo := repository.NewNotificationRepository(db)
	serviceNoti := service.NewNotificationService(serviceRepo)
	activityLuckyWheelService := service.NewActivityLuckyWheelService(activityLusckyWheelRepo, db, serviceNoti)
	service := service.NewMarketingService(repo, db, activityLuckyWheelService, serviceNoti)
	handler := newMarketingController(service)

	alertRoute := r.Group("/alert", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	alertRoute.GET("/menu-count", handler.getUserIncomePendingCount)

	// Marketing รายงานการตลาด - ประวัติการโยกเงินเข้ากระเป๋าหลัก + รายงานข้อมูลแพ้-ชนะ
	// เปลี่ยนชื่อ ประวัติโยกเงินเข้ากระเป๋าหลัก เป็น รายงานโบนัสกิจกรรม
	marketingRoute := r.Group("/marketing", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	marketingRoute.GET("/user-income/list", handler.getUserIncomeLogList)
	marketingRoute.GET("/user-income/list/summary", handler.getUserIncomeLogListSummary)
	marketingRoute.GET("/configs/auto-user-income-max-amount", handler.getUserIncomeMaxAmountConfig)
	marketingRoute.POST("/configs/auto-user-income-max-amount", handler.setUserIncomeMaxAmountConfig)
	marketingRoute.GET("/user-income/pending-list", handler.getUserIncomePendingList)
	marketingRoute.POST("/user-income/confirm/:id", handler.confirmUserIncome)
	marketingRoute.POST("/user-income/cancel/:id", handler.cancelUserIncome)
	marketingRoute.GET("/summary-win-lose/summary", handler.getUserWinLoseSummary)
	marketingRoute.GET("/summary-win-lose/list", handler.getUserWinLoseSummaryList)
	marketingRoute.GET("/summary-win-lose/daily-summary", handler.getUserWinLoseDailySummary)
	marketingRoute.GET("/summary-win-lose/daily-list", handler.getUserWinLoseDailyList)
	marketingRoute.GET("/summary-win-lose/today-summary", handler.getUserTodayWinLoseSummary)
	marketingRoute.GET("/summary-win-lose/today-list", handler.getUserTodayWinLoseSummaryList)
	marketingRoute.GET("/summary-transaction/daily-list", handler.getTransactionReportDaily)

}

// @summary (getUserIncomePendingCount) เมนู - นับจำนวนรายการ แจ้งเตือน
// @description (getUserIncomePendingCount) นับจำนวนราย แจ้งเตือน
// @Description
// @tags Alert Menu
// @security BearerAuth
// @accept json
// @produce json
// @success 200 {object} model.AlertMenuCountResponse
// @failure 400 {object} handler.ErrorResponse
// @router /alert/menu-count [get]
func (h marketingController) getUserIncomePendingCount(c *gin.Context) {

	result, err := h.marketingService.GetUserIncomePendingCount()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, result)
}

// @summary (getUserIncomeLogList) ประวัติการโยกเงินเข้ากระเป๋าหลัก รายงานโบนัสกิจกรรม
// @description (getUserIncomeLogList) Get All User Income Log List
// @description # typeName ตัวกรองประเภท
// @Description | typeName | ข้อมูลที่ได้ | หมายเหตุ |
// @Description | ---------------- | ------------| ------------|
// @Description | ALL *หรือ ไม่ส่ง* | ทั้งหมด | แสดงทั้งหมด |
// @Description | AFFILIATE | แจกโบนัสแนะนำเพื่อน | *ไม่แสดงในหน้าพันธมิตร |
// @Description | PROMOTION_RETURN_LOSS | โปรโมชั่นคืนเงิน | แจกโบนัสคืนยอดเสีย+ยอดเทิร์น |
// @Description | PROMOTION_RETURN_TURN |  โปรโมชั่นคืนยอดเทิร์น | คืนยอดเทิร์น |
// @Description | ---------------- | ------------| ------------|
// @Description
// @description # statusId ตัวกรองสถานะ
// @Description | statusId | ข้อมูลที่ได้ | หมายเหตุ |
// @Description | ---------------- | ------------| ------------|
// @Description | *ไม่ส่ง* | ทั้งหมด | แสดงทั้งหมด |
// @Description | 1 | รอกดรับ | แสดงรายการที่รอกดรับ |
// @Description | 2 | สำเร็จ | แสดงรายการที่รับเครดิตแล้ว |
// @Description | 3 | ไม่อนุมัติ | แสดงรายการที่ ไม่อนุมัติ |
// @Description | ---------------- | ------------| ------------|
// @Description
// @description # statusName ตัวกรองสถานะ
// @Description | statusName | ข้อมูลที่ได้ |
// @Description | ---------------- | ------------|
// @Description | *ไม่ส่ง* หรือ ALL | แสดงรายการทั้งหมด |
// @Description | PENDING | แสดงรายการที่รออนุมัติ |
// @Description | COMPLETED | แสดงรายการที่เสร็จสิ้นแล้ว |
// @Description | ---------------- | ------------|
// @Description
// @tags Marketing
// @security BearerAuth
// @accept json
// @produce json
// @param _ query model.UserIncomeLogListRequest true "query"
// @success 200 {object} model.SuccessWithPagination
// @failure 400 {object} handler.ErrorResponse
// @router /marketing/user-income/list [get]
func (h marketingController) getUserIncomeLogList(c *gin.Context) {

	query := model.UserIncomeLogListRequest{}
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	result, total, err := h.marketingService.GetUserIncomeLogList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithPagination{Message: "Success", List: result, Total: total})
}

// @summary (getUserIncomeLogListSummary) รายงานการโยกเงินเข้ากระเป๋าหลัก - สรุปยอดรวม
// @description (getUserIncomeLogListSummary) Get All User Income Log List Summary
// @tags Marketing
// @security BearerAuth
// @accept json
// @produce json
// @Param _ query model.UserIncomeLogTotalSummaryRequest true "query"
// @success 200 {object} model.UserIncomeLogTotalSummaryResponse
// @failure 400 {object} handler.ErrorResponse
// @router /marketing/user-income/list/summary [get]
func (h marketingController) getUserIncomeLogListSummary(c *gin.Context) {

	var query model.UserIncomeLogTotalSummaryRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	result, err := h.marketingService.GetUserIncomeLogListSummary(query)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, result)
}

// @summary (getUserIncomeMaxAmountConfig) ดึงข้อมูลการตั้งค่า การโยกเงินเข้ากระเป๋าหลัก - จำนวนเงินสูงสุดที่จะออโต้โยกเงินเข้ากระเป๋าหลัก
// @Description (getUserIncomeMaxAmountConfig)
// @Tags Marketing
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.MarketingConfigResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /marketing/configs/auto-user-income-max-amount [get]
func (h marketingController) getUserIncomeMaxAmountConfig(c *gin.Context) {

	data, err := h.marketingService.GetUserIncomeMaxAmountConfig()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (setUserIncomeMaxAmountConfig) ตั้งค่า การโยกเงินเข้ากระเป๋าหลัก - จำนวนเงินสูงสุดที่จะออโต้โยกเงินเข้ากระเป๋าหลัก
// @Description (setUserIncomeMaxAmountConfig)
// @Tags Marketing
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.MarketingConfigUpdateRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /marketing/configs/auto-user-income-max-amount [post]
func (h marketingController) setUserIncomeMaxAmountConfig(c *gin.Context) {

	var body model.MarketingConfigUpdateRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	body.UpdatedById = adminId

	if err := h.marketingService.SetUserIncomeMaxAmountConfig(body); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Update success"})
}

// @summary (getUserIncomePendingList) ประวัติการโยกเงินเข้ากระเป๋าหลัก - โยกเงินรออนุมัติ
// @description (getUserIncomePendingList) Get All User Income Log List
// @description # typeName ตัวกรองประเภท
// @Description | typeName | ข้อมูลที่ได้ | หมายเหตุ |
// @Description | ---------------- | ------------| ------------|
// @Description | ALL *หรือ ไม่ส่ง* | ทั้งหมด | แสดงทั้งหมด |
// @Description | AFFILIATE | แนะนำเพื่อน | *ไม่แสดงในหน้าพันธมิตร |
// @Description | PROMOTION_RETURN_LOSS | โปรโมชั่นคืนเงิน | คืนยอดเสีย |
// @Description | PROMOTION_RETURN_TURN |  โปรโมชั่นคืนยอดเทิร์น | คืนยอดเทิร์น |
// @Description | ---------------- | ------------|
// @Description
// @description # statusId ตัวกรองสถานะ
// @Description | statusId | ข้อมูลที่ได้ | หมายเหตุ |
// @Description | ---------------- | ------------| ------------|
// @Description | *ไม่ส่ง* | ทั้งหมด | แสดงทั้งหมด |
// @Description | 1 | รอกดรับ | แสดงรายการที่รอกดรับ |
// @Description | 2 | สำเร็จ | แสดงรายการที่รับเครดิตแล้ว |
// @Description | ---------------- | ------------|
// @Description
// @tags Marketing
// @security BearerAuth
// @accept json
// @produce json
// @param _ query model.UserIncomeLogListRequest true "query"
// @success 200 {object} model.SuccessWithPagination
// @failure 400 {object} handler.ErrorResponse
// @router /marketing/user-income/pending-list [get]
func (h marketingController) getUserIncomePendingList(c *gin.Context) {

	query := model.UserIncomeLogListRequest{}
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	result, total, err := h.marketingService.GetUserIncomePendingList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithPagination{Message: "Success", List: result, Total: total})
}

// @Summary (confirmUserIncome) ยืนยันการโยกเงินเข้ากระเป๋าหลัก
// @Description (confirmUserIncome)
// @Tags Marketing
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /marketing/user-income/confirm/{id} [post]
func (h marketingController) confirmUserIncome(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	var query model.GetByIdRequest
	if err := c.ShouldBindUri(&query); err != nil {
		HandleError(c, err)
		return
	}

	// RACE_CONDITION_BLOCKER
	if _, err := h.marketingService.RacingConfirmUserIncome(query.Id); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.marketingService.ConfirmUserIncome(query.Id, adminId); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Update success"})
}

// @Summary (cancelUserIncome) ไม่อนุมัติ การโยกเงินเข้ากระเป๋าหลัก
// @Description (cancelUserIncome)
// @Tags Marketing
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /marketing/user-income/cancel/{id} [post]
func (h marketingController) cancelUserIncome(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	var query model.GetByIdRequest
	if err := c.ShouldBindUri(&query); err != nil {
		HandleError(c, err)
		return
	}

	// RACE_CONDITION_BLOCKER
	if _, err := h.marketingService.RacingConfirmUserIncome(query.Id); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.marketingService.CancelUserIncome(query.Id, adminId); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Update success"})
}

// @summary (getUserWinLoseSummary) รายงานข้อมูลแพ้-ชนะ รายงานข้อมูลแพ้ ชนะ
// @description (getUserWinLoseSummary) Get All User Total Win Lose
// @Description # การกรองด้วยประเภทวัน dateType ทั้งหมด วันนี้ เมื่อวาน เดือนนี้ **ถ้าส่ง fromDate, toDate จะใช้ fromDate, toDate ก่อน
// @Description | dateType | Description |
// @Description | ---------------- | ------------|
// @Description | all *หรือไม่ส่ง* | แสดงทั้งหมด |
// @Description | today | แสดงวันนี้ |
// @Description | yesterday | แสดงเมื่อวาน |
// @Description | this_month | แสดงเดือนนี้ 1-31 |
// @Description | last_month | แสดง 30 วันก่อนหน้า |
// @Description | ---------------- | ------------|
// @description # gameType ตัวกรองประเภทการเล่น
// @Description | gameType | ข้อมูลที่ได้ |
// @Description | ---------------- | ------------|
// @Description | sport | กีฬา |
// @Description | casino | คาสิโน |
// @Description | game | เกมส์ |
// @Description | ---------------- | ------------|
// @Description
// @tags Marketing
// @security BearerAuth
// @accept json
// @produce json
// @param _ query model.UserWinLoseSummaryReportRequest true "query"
// @success 200 {object} model.UserWinLoseSummaryReportResponse
// @failure 400 {object} handler.ErrorResponse
// @router /marketing/summary-win-lose/summary [get]
func (h marketingController) getUserWinLoseSummary(c *gin.Context) {

	query := model.UserWinLoseSummaryReportRequest{}
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.marketingService.GetUserWinLoseSummary(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @summary (getUserWinLoseSummaryList) รายงานข้อมูลแพ้-ชนะ รายงานข้อมูลแพ้ ชนะ
// @description (getUserWinLoseSummaryList) Get All User Total Win Lose
// @Description # การกรองด้วยประเภทวัน dateType ทั้งหมด วันนี้ เมื่อวาน เดือนนี้ **ถ้าส่ง fromDate, toDate จะใช้ fromDate, toDate ก่อน
// @Description | dateType | Description |
// @Description | ---------------- | ------------|
// @Description | all *หรือไม่ส่ง* | แสดงทั้งหมด |
// @Description | today | แสดงวันนี้ |
// @Description | yesterday | แสดงเมื่อวาน |
// @Description | this_month | แสดงเดือนนี้ 1-31 |
// @Description | last_month | แสดง 30 วันก่อนหน้า |
// @Description | ---------------- | ------------|
// @description # gameType ตัวกรองประเภทการเล่น
// @Description | gameType | ข้อมูลที่ได้ |
// @Description | ---------------- | ------------|
// @Description | sport | กีฬา |
// @Description | casino | คาสิโน |
// @Description | game | เกมส์ |
// @Description | ---------------- | ------------|
// @Description # การเรียงลำดับ
// @Description | sortCol | ข้อมูลที่ได้ |
// @Description | ---------------- | ------------|
// @Description | totalTurnOver | ยอดเทิร์นโอเวอร์ |
// @Description | totalValidAmount | ยอดเงินที่เล่น |
// @Description | diffAmount | ยอดเงินที่เล่น |
// @Description | ---------------- | ------------|
// @Description
// @tags Marketing
// @security BearerAuth
// @accept json
// @produce json
// @param _ query model.UserWinLoseSummaryListRequest true "query"
// @success 200 {object} model.SuccessWithPagination
// @failure 400 {object} handler.ErrorResponse
// @router /marketing/summary-win-lose/list [get]
func (h marketingController) getUserWinLoseSummaryList(c *gin.Context) {

	query := model.UserWinLoseSummaryListRequest{}
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	list, total, err := h.marketingService.GetUserWinLoseSummaryList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithPagination{Message: "Success", List: list, Total: total})
}

// @summary (getUserWinLoseDailyList) รายงานข้อมูลแพ้-ชนะ รายงานข้อมูลแพ้ ชนะ ประจำวัน รายงานข้อมูลแพ้-ชนะ รายวัน
// @description (getUserWinLoseDailyList) Get All User Total Win Lose daily
// @Description # การกรองด้วย รายเดือน ให้ส่ง วันที่ 1 ของเดือนนั้น ปีนั้น เช่น 2021-01-01 2021-02-01 หรือ 2024-12-01
// @Description | ofDate | 2024-12-01 |
// @Description | ---------------- | ------------|
// @description # TypeName ตัวกรองประเภทการเล่น
// @Description | TypeName | ข้อมูลที่ได้ |
// @Description | ---------------- | ------------|
// @Description | sport | กีฬา |
// @Description | casino | คาสิโน |
// @Description | game | เกมส์ |
// @Description | ---------------- | ------------|
// @Description # การเรียงลำดับ
// @Description | sortCol | ข้อมูลที่ได้ |
// @Description | ---------------- | ------------|
// @Description # MemberCode | รหัสสมาชิก |
// @Description | ---------------- | ------------|
// @Description
// @tags Marketing
// @security BearerAuth
// @accept json
// @produce json
// @param _ query model.UserWinLoseDailyListRequest true "query"
// @success 200 {object} model.SuccessWithPagination
// @failure 400 {object} handler.ErrorResponse
// @router /marketing/summary-win-lose/daily-list [get]
func (h marketingController) getUserWinLoseDailyList(c *gin.Context) {

	query := model.UserWinLoseDailyListRequest{}
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	list, total, err := h.marketingService.GetUserWinLoseDailyList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithPagination{Message: "Success", List: list, Total: total})
}

// @summary (getUserWinLoseDailySummary) ตัวเลขสรุป รายงานข้อมูลแพ้-ชนะ รายงานข้อมูลแพ้ ชนะ
// @description (getUserWinLoseDailySummary) Get All User Total Win Lose
// @Description # การกรองด้วยประเภทวัน dateType ทั้งหมด วันนี้ เมื่อวาน เดือนนี้ **ถ้าส่ง fromDate, toDate จะใช้ fromDate, toDate ก่อน
// @Description | ---------------- | ------------|
// @description # gameType ตัวกรองประเภทการเล่น
// @Description | gameType | ข้อมูลที่ได้ |
// @Description | ---------------- | ------------|
// @Description | sport | กีฬา |
// @Description | casino | คาสิโน |
// @Description | game | เกมส์ |
// @Description | ---------------- | ------------|
// @Description
// @tags Marketing
// @security BearerAuth
// @accept json
// @produce json
// @param _ query model.UserWinLoseDailySummaryRequest true "query"
// @success 200 {object} model.UserWinLoseSummaryReportResponse
// @failure 400 {object} handler.ErrorResponse
// @router /marketing/summary-win-lose/daily-summary [get]
func (h marketingController) getUserWinLoseDailySummary(c *gin.Context) {

	query := model.UserWinLoseDailySummaryRequest{}
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.marketingService.GetUserWinLoseDailySummary(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @summary (getUserTodayWinLoseSummary) รายงานข้อมูลแพ้-ชนะ รายงานข้อมูลแพ้ ชนะ
// @description (getUserTodayWinLoseSummary) Get All User Total Win Lose
// @Description # การกรองด้วยประเภทวัน dateType ทั้งหมด วันนี้ เมื่อวาน เดือนนี้ **ถ้าส่ง fromDate, toDate จะใช้ fromDate, toDate ก่อน
// @Description | ---------------- | ------------|
// @description # gameType ตัวกรองประเภทการเล่น
// @Description | gameType | ข้อมูลที่ได้ |
// @Description | ---------------- | ------------|
// @Description | sport | กีฬา |
// @Description | casino | คาสิโน |
// @Description | game | เกมส์ |
// @Description | ---------------- | ------------|
// @Description
// @tags Marketing
// @security BearerAuth
// @accept json
// @produce json
// @param _ query model.UserTodayWinLoseSummaryReportRequest true "query"
// @success 200 {object} model.UserWinLoseSummaryReportResponse
// @failure 400 {object} handler.ErrorResponse
// @router /marketing/summary-win-lose/today-summary [get]
func (h marketingController) getUserTodayWinLoseSummary(c *gin.Context) {

	query := model.UserTodayWinLoseSummaryReportRequest{}
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.marketingService.GetUserTodayWinLoseSummary(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @summary (getUserTodayWinLoseSummaryList) รายงานข้อมูลแพ้-ชนะ รายงานข้อมูลแพ้ ชนะ
// @description (getUserTodayWinLoseSummaryList) Get All User Total Win Lose
// @Description # การกรองด้วยประเภทวัน dateType ทั้งหมด วันนี้ เมื่อวาน เดือนนี้ **ถ้าส่ง fromDate, toDate จะใช้ fromDate, toDate ก่อน
// @Description | ---------------- | ------------|
// @description # gameType ตัวกรองประเภทการเล่น
// @Description | gameType | ข้อมูลที่ได้ |
// @Description | ---------------- | ------------|
// @Description | sport | กีฬา |
// @Description | casino | คาสิโน |
// @Description | game | เกมส์ |
// @Description | ---------------- | ------------|
// @Description # การเรียงลำดับ
// @Description | sortCol | ข้อมูลที่ได้ |
// @Description | ---------------- | ------------|
// @Description | totalTurnOver | ยอดเทิร์นโอเวอร์ |
// @Description | totalValidAmount | ยอดเงินที่เล่น |
// @Description | diffAmount | ยอดเงินที่เล่น |
// @Description | ---------------- | ------------|
// @Description
// @tags Marketing
// @security BearerAuth
// @accept json
// @produce json
// @param _ query model.UserTodayWinLoseSummaryListRequest true "query"
// @success 200 {object} model.SuccessWithPagination
// @failure 400 {object} handler.ErrorResponse
// @router /marketing/summary-win-lose/today-list [get]
func (h marketingController) getUserTodayWinLoseSummaryList(c *gin.Context) {

	query := model.UserTodayWinLoseSummaryListRequest{}
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	list, total, err := h.marketingService.GetUserTodayWinLoseSummaryList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithPagination{Message: "Success", List: list, Total: total})
}

// @summary (getTransactionReportDaily) รายงานสมาชิกฝากถอนรายวัน
// @description (getTransactionReportDaily) Get All User Total Win Lose daily
// @Description # การกรองด้วย รายเดือน ให้ส่ง วันที่ 1 ของเดือนนั้น ปีนั้น เช่น 2021-01-01 2021-02-01 หรือ 2024-12-01
// @Description | ofDate | 2024-12-01 |
// @Description | ---------------- | ------------|
// @description # fromDate-toDate ส่ง string 2024-12-31 มา ถห้ามส่ง ofDate เพราะจะเอา OfDate(ทั้งเดือน) มาคำนวณก่อน
// @Description | fromDate | วันที่เริ่ม | 2024-01-01
// @Description | toDate | ถึงวันที่ | 2024-12-31
// @Description | ---------------- | ------------|
// @Description
// @tags Marketing
// @security BearerAuth
// @accept json
// @produce json
// @param _ query model.TransactionReportDailyQuery true "query"
// @success 200 {object} model.TransactionReportDailyResponse
// @failure 400 {object} handler.ErrorResponse
// @router /marketing/summary-transaction/daily-list [get]
func (h marketingController) getTransactionReportDaily(c *gin.Context) {

	query := model.TransactionReportDailyQuery{}
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.marketingService.GetTransactionReportDaily(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}
