package handler

import (
	"cybergame-api/helper"
	"cybergame-api/middleware"
	"cybergame-api/model"
	"cybergame-api/repository"
	"cybergame-api/service"
	"fmt"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"gorm.io/gorm"
)

type bankingController struct {
	bankingService    service.BankingService
	accountingService service.AccountingService
}

func newBankingController(
	bankingService service.BankingService,
	accountingService service.AccountingService,
) bankingController {
	return bankingController{bankingService, accountingService}
}

func BankingController(r *gin.RouterGroup, db *gorm.DB) {

	actionService := service.NewAdminActionService(repository.NewAdminActionRepository(db))
	repoBanking := repository.NewBankingRepository(db)
	repoAccounting := repository.NewAccountingRepository(db)
	repoAgentConnect := repository.NewAgentConnectRepository(db)
	userRepo := repository.NewUserRepository(db)
	perRepo := repository.NewPermissionRepository(db)
	groupRepo := repository.NewGroupRepository(db)
	otpRepo := repository.NewOtpRepository(db)
	agentInfoRepo := repository.NewAgentInfoRepository(db)
	recommendRepo := repository.NewRecommendRepository(db)
	afRepo := repository.NewAffiliateRepository(db)
	serviceRepo := repository.NewNotificationRepository(db)
	serviceNoti := service.NewNotificationService(serviceRepo)
	afService := service.NewAffiliateService(db, afRepo, repoAgentConnect, serviceNoti)
	notiRepo := repository.NewNotificationRepository(db)
	notiService := service.NewNotificationService(notiRepo)
	userService := service.NewUserService(
		userRepo,
		db,
		perRepo,
		groupRepo,
		otpRepo,
		agentInfoRepo,
		recommendRepo,
		afRepo,
		notiService,
		actionService,
	)
	gameService := service.NewGameService(agentInfoRepo)
	turnoverRepo := repository.NewTurnoverRepository(db)
	turnoverService := service.NewTurnoverService(turnoverRepo)
	agRepo := repository.NewAgentConnectRepository(db)
	allianceRepo := repository.NewAllianceRepository(db)
	couponCashRepo := repository.NewCouponCashRepository(db)
	couponCashService := service.NewCouponCashService(couponCashRepo, userService)
	alService := service.NewAllianceService(allianceRepo, db, agRepo)
	activityLusckyWheelRepo := repository.NewActivityLuckyWheelRepository(db)
	activityLuckyWheelService := service.NewActivityLuckyWheelService(activityLusckyWheelRepo, db, serviceNoti)
	promotionWebRepo := repository.NewPromotionWebRepository(db)
	promotionWebService := service.NewPromotionWebService(promotionWebRepo, db, serviceNoti, userService)
	accountService := service.NewAccountingService(repoAccounting, userService, gameService, repoAgentConnect, notiService, afService, alService, actionService, activityLuckyWheelService, promotionWebService, db)
	bankingService := service.NewBankingService(repoBanking, repoAccounting, repoAgentConnect, accountService, userService, notiService, afService, turnoverService, alService, activityLuckyWheelService, promotionWebService, couponCashService, db)
	handler := newBankingController(bankingService, accountService)

	role := middleware.Role(db)
	singleSession := middleware.SingleSession(db)

	root := r.Group("/banking", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	root.GET("/transactiontypes/list", role.CheckPermission([]string{"deposit_list", "withdraw_list"}), handler.getTransactionTypes)
	root.GET("/transactionstatuses/list", handler.getTransactionStatuses)
	root.GET("/statementtypes/list", handler.getStatementTypes)
	root.GET("/transaction-deposit-status/option", handler.getTransactionDepositStatus)
	root.GET("/transaction-deposit-types/option", role.CheckPermission([]string{"deposit_list"}), handler.getTransactionDepositType)
	root.GET("/transaction-deposit-sortby/option", handler.sortByUserTransaction)
	root.GET("/transaction-withdraw-status/option", handler.getTransactionWithdrawStatus)
	root.GET("/transaction-withdraw-types/option", role.CheckPermission([]string{"withdraw_list"}), handler.getTransactionWithdrawType)

	statementRoute := root.Group("/statements", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	statementRoute.GET("/list", handler.getBankStatements)
	statementRoute.GET("/summary", handler.getBankStatementSummary)
	statementRoute.GET("/detail/:id", handler.getBankStatementById)
	statementRoute.POST("", handler.createBankStatement)
	statementRoute.POST("/matchowner", role.CheckPermission([]string{"deposit_list"}), handler.matchStatementOwner)
	statementRoute.POST("/ignoreowner/:id", handler.ignoreStatementOwner)
	statementRoute.DELETE("/:id", handler.deleteBankStatement)

	transactionRoute := root.Group("/transactions", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	transactionRoute.GET("/list", role.CheckPermission([]string{"bank"}), handler.getBankTransactions)
	transactionRoute.GET("/count_deposit_statuses", role.CheckPermission([]string{"deposit_list"}), handler.getBankDepositTransStatusCounts)
	transactionRoute.GET("/count_withdraw_statuses", role.CheckPermission([]string{"withdraw_list"}), handler.getBankWithdrawTransStatusCounts)
	transactionRoute.GET("/detail/:id", handler.getBankTransactionById)
	transactionRoute.POST("/unused-cancel/:id", handler.cancelPendingTransaction)
	transactionRoute.POST("/retry-deposit-agent/:refId", role.CheckPermission([]string{"deposit_list"}), handler.retryDepositAgent)

	transactionRoute.POST("/withdraw-record", role.CheckPermission([]string{"create_withdraw_tranasction"}), handler.createWithdrawRecord)
	transactionRoute.POST("/withdraw-credit-back", role.CheckPermission([]string{"withdraw_list"}), handler.createWithdrawPullCreditBack)
	transactionRoute.POST("/user-credit-cancel", role.CheckPermission([]string{"cancel_tranasction_credit"}), handler.createUserCancelCredit)
	transactionRoute.POST("/upload/deposit-slip", role.CheckPermission([]string{"deposit_list"}), handler.uploadImageToCloudflareDepositSlip)
	transactionRoute.POST("/upload/bonus-slip", role.CheckPermission([]string{"deposit_list"}), handler.uploadImageToCloudflareBonusSlip)
	transactionRoute.POST("/deposit-record", role.CheckPermission([]string{"deposit_list"}), handler.createDepositRecord)
	transactionRoute.POST("/free-bonus", role.CheckPermission([]string{"deposit_list"}), handler.createFreeBonus)
	transactionRoute.GET("/possibleowners/:id", role.CheckPermission([]string{"deposit_list"}), handler.getPossibleOwnersByStatementId)
	transactionRoute.GET("/deposit-list", role.CheckPermission([]string{"deposit_list"}), handler.getBankTransactionDepositList)
	transactionRoute.GET("/withdraw-list", role.CheckPermission([]string{"withdraw_list"}), handler.getBankTransactionWithdrawList)
	transactionRoute.POST("/first-time-deposit-record", role.CheckPermission([]string{"deposit_list"}), handler.createFristTimeDepositRecord)
	// transactionRoute.GET("/success-list", handler.getBankTransactionSuccess)
	transactionRoute.GET("/deposit/:id", handler.getDeposiTransactionById)
	transactionRoute.GET("/withdraw/:id", handler.getTransactionWithdrawById)
	transactionRoute.POST("/ignore-deposit", role.CheckPermission([]string{"deposit_list"}), handler.updateIgnoreDeposit)
	// transactionRoute.DELETE("/delete/:id", handler.removedTransaction)
	// transactionRoute.GET("/removed-success-list", handler.removedSuccessTransactionList)
	transactionRoute.GET("/check/admin-duplicate-withdraw", role.CheckPermission([]string{"withdraw_list"}), handler.checkAdminDuplicateWithdrawList)

	transactionRoute.POST("/withdraw-auto", role.CheckPermission([]string{"withdraw_list"}), handler.createAutoWithdraw)
	transactionRoute.POST("/withdraw-selected-account", role.CheckPermission([]string{"withdraw_list"}), handler.createTransWithdrawWithSelectedAccount)
	transactionRoute.POST("/withdraw-external-account", role.CheckPermission([]string{"withdraw_list"}), handler.createTransWithdrawWithExternalAccount)
	transactionRoute.POST("/withdraw-manual-account", role.CheckPermission([]string{"withdraw_list"}), handler.createTransWithdrawWithManualAccount)
	transactionRoute.POST("/cancel-withdraw", handler.cancelWithdrawCredit)
	transactionRoute.POST("/check-transfering-withdraw", role.CheckPermission([]string{"withdraw_list"}), handler.checkTransferingWithdraw)

	transactionRoute.POST("/confirmdeposit/:id", handler.confirmDepositTransaction)
	transactionRoute.POST("/confirmdepositcredit/:id", handler.confirmDepositCreditTransaction)
	transactionRoute.POST("/confirmcreditwithdraw/:id", handler.confirmCreditWithdrawTransaction)
	transactionRoute.POST("/confirmtransferwithdraw/:id", handler.confirmTransferWithdrawTransaction)

	transactionRoute.GET("/first-time-deposit-list", role.CheckPermission([]string{"deposit_list"}), handler.getBankTransactionFirstTimeDeposit)

	transactionRoute.GET("/external-detail/:id", handler.getBankTransactionExternalDetailByBankTransactionId)
	transactionRoute.POST("/confirm-withdraw-transaction", role.CheckPermission([]string{"withdraw_list"}), handler.confirmWithdrawTransactionAnyStatus)

	transactionRoute.POST("/confirm-payment-withdraw", role.CheckPermission([]string{"withdraw_list"}), handler.autoConfirmPayment)
	transactionRoute.GET("/smsmode-deposit-list", role.CheckPermission([]string{"deposit_list"}), handler.getSmsModeDepositList)
	transactionRoute.GET("/smsmode-status-option", role.CheckPermission([]string{"deposit_list"}), handler.getSmsModeDepositStatusOption)
	transactionRoute.POST("/smsmode-confirm-deposit", role.CheckPermission([]string{"deposit_list"}), handler.adminConfirmDepositSmsMode)
	transactionRoute.POST("/smsmode-cancel-deposit", role.CheckPermission([]string{"deposit_list"}), handler.adminCancelDepositSmsMode)

	memberRoute := root.Group("/member", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	memberRoute.GET("/info/:code", role.CheckPermission([]string{"deposit_list"}), handler.getMemberByCode)
	memberRoute.GET("/list", role.CheckPermission([]string{"deposit_list"}), handler.getMembers)
	memberRoute.GET("/transactionsummary", role.CheckPermission([]string{"member", "member_edit", "member_delete", "deposit_list", "withdraw_list"}), handler.getMemberTransactionSummary)
	memberRoute.GET("/transactions", role.CheckPermission([]string{"member", "member_edit", "member_delete", "deposit_list", "withdraw_list"}), handler.getMemberTransactions)
	memberRoute.GET("/statements", handler.getMemberStatements)
	memberRoute.GET("/statements/detail/:id", handler.getMemberStatementById)
	// TEST
	memberRoute.POST("/statements1", handler.processMemberDepositCredit)
	memberRoute.POST("/statements2", handler.processMemberWithdrawCredit)
	memberRoute.POST("/statements3", handler.processMemberBonusCredit)
	memberRoute.POST("/statements4", handler.processMemberGetbackCredit)

	web := r.Group("/web", middleware.AuthorizeUser, singleSession.SingleUserSession())
	web.GET("user-transaction-list", handler.getWebUserTransactionList)
	web.POST("/withdrawal", handler.userCreateWithDrawTransaction)
	web.GET("/check-user-duplicate-withdraw", handler.webCheckUserDuplicateWithdrawProeccing)

	testSocket := r.Group("/test")
	testSocket.GET("/is-first-deposit/:id", handler.IsFirstDeposit)

	user := r.Group("/web", middleware.AuthorizeUser)
	user.POST("/banking/transactions/deposit-by-slip", handler.userDepositBySlip)
	user.GET("/banking/transactions/check-scammer-slip", handler.checkScammerSlip)
	user.POST("/banking/transactions/deposit-laos", handler.createDepositLaosBankRecord)
	user.POST("/banking/transactions/upload/deposit-slip", handler.userUploadImageToCloudflareDepositSlip)
	user.POST("/banking/transactions/deposit-by-upload", handler.userDepositByUploadFile)

	registUser := r.Group("/web")
	registUser.POST("/banking/transactions/fastbank-account-info", handler.getAccountInfoFastbank)

	// /test/check-other-exist-turnover/{id}
	test := r.Group("/test")
	test.GET("/check-other-exist-turnover/:id", handler.checkOtherExistTurnOver)
	test.POST("/turn-over-withdraw-checker", handler.turnOverWithdrawChecker)

	turnoverUserRoute := r.Group("/turnover", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	turnoverUserRoute.POST("/cancel-user", handler.canceledAllTurn)
	turnoverUserRoute.POST("/empty-user-list", handler.emptyUserTurnList)

	botRoute := r.Group("/bot", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	botRoute.POST("/transactions/withdraw-credit-back", handler.botCreateWithdrawPullCreditBack)

}

// @Summary get Transaction Type List
// @Description ดึงข้อมูลตัวเลือก ประเภทการทำรายการ
// @Tags Banking - Options
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.SuccessWithPagination
// @Router /banking/transactiontypes/list [get]
func (h bankingController) getTransactionTypes(c *gin.Context) {
	// มีใน code mที่เอาไปใช้
	// var data = []model.SimpleOption{
	// 	{Key: "deposit", Name: "ฝาก"},
	// 	{Key: "withdraw", Name: "ถอน"},
	// 	{Key: "bonus", Name: "โบนัส"},
	// 	{Key: "getcreditback", Name: "ดึงเครดิตกลับ"},
	// }
	// GetTransactionType() ([]model.TransactionTypeResponse, error)
	list, err := h.bankingService.GetTransactionType()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, list)
}

// @Summary get Transaction Status List
// @Description ดึงข้อมูลตัวเลือก สถานะรายการฝากถอน
// @Tags Banking - Options
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.SuccessWithPagination
// @Router /banking/transactionstatuses/list [get]
func (h bankingController) getTransactionStatuses(c *gin.Context) {
	// ยังไม่มีใน code ที่เอาไปใช้
	var data = []model.SimpleOption{
		{Key: "pending", Name: "รอดำเนินการ"},
		{Key: "canceled", Name: "ยกเลิกแล้ว"},
		{Key: "finished", Name: "อนุมัติแล้ว"},
	}
	c.JSON(200, model.SuccessWithPagination{List: data, Total: 2})
}

// @Summary get Transaction Deposit Status List
// @Description ดึงข้อมูลตัวเลือก สถานะรายการฝาก
// @Description ที่เป็นรอดำเนินการทั้งหมดใช้ API แยก
// @Tags Banking - Bank Transaction v2 Options
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.SuccessWithPagination
// @Router /banking/transaction-deposit-status/option [get]
func (h *bankingController) getTransactionDepositStatus(c *gin.Context) {

	tranStatusPending := strconv.FormatInt(model.TRANS_STATUS_PENDING, 10)                        // defualt และ คนฝากเงินเลขไม่ตรง
	tranStatusPendingCredit := strconv.FormatInt(model.TRANS_STATUS_DEPOSIT_PENDING_CREDIT, 10)   // รอเครดิตจากเกม
	tranStatusCreditApproved := strconv.FormatInt(model.TRANS_STATUS_DEPOSIT_CREDIT_APPROVED, 10) // อนุมัติแล้ว (เกมเครดิต)
	tranStatusCreditRejected := strconv.FormatInt(model.TRANS_STATUS_DEPOSIT_CREDIT_REJECTED, 10) // ไม่อนุมัติ (เกมครดิต)
	tranStatusPendingSlip := strconv.FormatInt(model.TRANS_STATUS_DEPOSIT_PENDING_SLIP, 10)       // แจ้งฝาก slip เข้ามาก่อน webhook
	tranStatusIgnore := strconv.FormatInt(model.TRANS_STATUS_DEPOSIT_IGNORE, 10)                  // แจ้งฝาก slip เข้ามาก่อน webhook

	data := []model.OptionValueString{

		{Value: fmt.Sprintf("%v,%v,%v", tranStatusPending, tranStatusPendingCredit, tranStatusPendingSlip), Label: "รอดำเนินการ"},
		{Value: tranStatusPending, Label: "รอตรวจสอบ"},
		{Value: tranStatusPendingCredit, Label: "รอดำเนินการ (เกมเครดิต)"},
		{Value: tranStatusCreditApproved, Label: "อนุมัติแล้ว (เกมเครดิต)"},
		{Value: tranStatusCreditRejected, Label: "ไม่อนุมัติ (เกมครดิต)"},
		{Value: fmt.Sprintf("%v,%v", tranStatusCreditRejected, tranStatusIgnore), Label: "ไม่อนุมัติ (เกมครดิต)"},
	}
	c.JSON(200, model.SuccessWithPagination{List: data, Total: 4})

}

// @Summary get Transaction withdraw Status List
// @Description ดึงข้อมูลตัวเลือก สถานะรายการถอน
// @Description ที่เป็นรอดำเนินการทั้งหมดใช้ API แยก
// @Tags Banking - Bank Transaction v2 Options
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.SuccessWithPagination
// @Router /banking/transaction-withdraw-status/option [get]
func (h *bankingController) getTransactionWithdrawStatus(c *gin.Context) {

	tranStatusPending := strconv.FormatInt(model.TRANS_STATUS_WITHDRAW_PENDING, 10)
	tranStatusPendingOverBudget := strconv.FormatInt(model.TRANS_STATUS_WITHDRAW_OVER_BUDGET, 10)
	tranStatusApprove := strconv.FormatInt(model.TRANS_STATUS_WITHDRAW_APPROVED, 10)
	tranStatusRejected := strconv.FormatInt(model.TRANS_STATUS_WITHDRAW_REJECTED, 10)
	tranStatusfailed := strconv.FormatInt(model.TRANS_STATUS_WITHDRAW_FAILED, 10)
	tranStatusSuccess := strconv.FormatInt(model.TRANS_STATUS_WITHDRAW_SUCCESS, 10)
	tranStatusOverMax := strconv.FormatInt(model.TRANS_STATUS_WITHDRAW_OVER_MAX, 10)
	tranStatusUnSure := strconv.FormatInt(model.TRANS_STATUS_WITHDRAW_UNSURE, 10)
	transStatusTransfering := strconv.FormatInt(model.TRANS_STATUS_WITHDRAW_TRASNFERING, 10)

	data := []model.OptionValueString{
		{Value: fmt.Sprintf("%v,%v,%v,%v,%v,%v", tranStatusPending, tranStatusPendingOverBudget, tranStatusOverMax, tranStatusRejected, tranStatusApprove, tranStatusfailed), Label: "ยังไม่เรียบร้อย"},
		{Value: fmt.Sprintf("%v", tranStatusPending), Label: "รอปรับเครดิต"},
		{Value: fmt.Sprintf("%v,%v,%v,%v,%v,%v", tranStatusPending, tranStatusPendingOverBudget, tranStatusOverMax, tranStatusApprove, tranStatusfailed, tranStatusUnSure), Label: "รอโอน"},
		{Value: fmt.Sprintf("%v", transStatusTransfering), Label: "กำลังโอน"},
		{Value: tranStatusSuccess, Label: "สำเร็จ "},
		{Value: fmt.Sprintf("%v,%v", tranStatusfailed, tranStatusRejected), Label: "ไม่สำเร็จ"},
	}
	c.JSON(200, model.SuccessWithPagination{List: data, Total: 6})
}

// @Summary get Transaction Deposit Type List
// @Description ดึงข้อมูลตัวเลือก สถานะรายการฝากถอน
// @Description ที่เป็นรอดำเนินการทั้งหมดใช้ API แยก
// @Tags Banking - Bank Transaction v2 Options
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.SuccessWithPagination
// @Router /banking/transaction-deposit-types/option [get]
func (h *bankingController) getTransactionDepositType(c *gin.Context) {

	// transTypeDeposit := strconv.FormatInt(model.TRANSACTION_TYPE_DEPOSIT, 10)
	// transTypeBonus := strconv.FormatInt(model.TRANSACTION_TYPE_BONUS, 10)

	data := []model.OptionValueInt{
		{Value: model.TRANSACTION_TYPE_DEPOSIT, Label: "ฝาก"},
		{Value: model.TRANSACTION_TYPE_BONUS, Label: "โบนัส"},
	}
	c.JSON(200, model.SuccessWithPagination{List: data, Total: 4})

}

// @Summary get Transaction Deposit sort by option
// @Description ดึงข้อมูลตัวเลือก sort by
// @Tags Banking - Bank Transaction v2 Options
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.Success
// @Router /banking/transaction-deposit-sortby/option [get]
func (h *bankingController) sortByUserTransaction(c *gin.Context) {

	var data = []model.SimpleOption{
		{Key: "created_at", Name: "โอนเข้า"},
		{Key: "transfer_at", Name: "โอนออก"},
	}
	c.JSON(200, data)

}

// @Summary get Transaction Deposit Type List
// @Description ดึงข้อมูลตัวเลือก สถานะรายการฝากถอน
// @Description ที่เป็นรอดำเนินการทั้งหมดใช้ API แยก
// @Tags Banking - Bank Transaction v2 Options
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.SuccessWithPagination
// @Router /banking/transaction-withdraw-types/option [get]
func (h *bankingController) getTransactionWithdrawType(c *gin.Context) {

	data := []model.OptionValueInt{
		{Value: model.TRANSACTION_TYPE_WITHDRAW, Label: "ถอน"},
		{Value: model.TRANSACTION_TYPE_CREDITBACK, Label: "ดึงเครดิตกลับ"},
		{Value: model.TRANSACTION_TYPE_CREDITCANCEL, Label: "ยกเลิกเครดิต"},
	}
	c.JSON(200, model.SuccessWithPagination{List: data, Total: 4})
}

// @Summary get Statement type List
// @Description ดึงข้อมูลตัวเลือก ประเภทรายการเดินบัญชี
// @Tags Banking - Options
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.SuccessWithPagination
// @Router /banking/statementtypes/list [get]
func (h bankingController) getStatementTypes(c *gin.Context) {
	// มีใน code mที่เอาไปใช้

	// var data = []model.SimpleOption{
	// 	{Key: " transfer_in", Name: "โอนเข้า"},
	// 	{Key: " transfer_out", Name: "โอนออก"},
	// }
	list, err := h.bankingService.GetStatementType()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, list)
}

// @Summary GetStatementList รายการเดินบัญชี รายการโอนรอดำเนินการ
// @Description ดึงข้อมูลลิสการโอนเงิน ใช้แสดงในหน้า จัดการธนาคาร - ธุรกรรม
// @Tags Banking - Bank Account Statements
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.BankStatementListRequest true "BankStatementListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/statements/list [get]
func (h bankingController) getBankStatements(c *gin.Context) {

	var query model.BankStatementListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.bankingService.GetBankStatements(query)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, model.SuccessWithPagination{List: data.List, Total: data.Total})
}

// @Summary GetBankStatementSummary
// @Description ดึงข้อมูลจำนวนรายการสรุป เช่น รายการโอนรอดำเนินการ รายการฝากถอนรอดำเนินการ
// @Tags Banking - Bank Account Statements
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/statements/summary [get]
func (h bankingController) getBankStatementSummary(c *gin.Context) {

	var query model.BankStatementListRequest

	data, err := h.bankingService.GetBankStatementSummary(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithData{Message: "success", Data: data})
}

// @Summary GetStatementByID
// @Description ดึงข้อมูลการโอนด้วย id *ยังไม่ได้ใช้งาน*
// @Tags Banking - Bank Account Statements
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/statements/detail/{id} [get]
func (h bankingController) getBankStatementById(c *gin.Context) {

	var req model.GetByIdRequest

	if err := c.ShouldBindUri(&req); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.bankingService.GetBankStatementById(req)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithData{Message: "success", Data: data})
}

// @Summary (createBankStatement)
// @Description (createBankStatement)
// @Tags Banking - Bank Account Statements
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.BankStatementCreateBody true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/statements [post]
func (h bankingController) createBankStatement(c *gin.Context) {

	var banking model.BankStatementCreateBody
	if err := c.ShouldBindJSON(&banking); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(banking); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.bankingService.CreateBankStatement(banking); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Created success"})
}

// @Summary (MatchStatementOwner) ดำเนินการข้อมูลการเดินบัญชี
// @Description (matchStatementOwner) ดำเนินการข้อมูลการเดินบัญชี
// @Tags Banking - Bank Account Statements
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.BankStatementMatchRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/statements/matchowner [post]
func (h bankingController) matchStatementOwner(c *gin.Context) {

	adminId, err := h.accountingService.CheckCurrentAdminId(c.MustGet("adminId"))
	if err != nil {
		HandleError(c, err)
		return
	}

	var req model.BankStatementMatchRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(req); err != nil {
		HandleError(c, err)
		return
	}
	req.ConfirmedAt = time.Now()
	req.ConfirmedByAdminId = adminId

	if actionErr := h.bankingService.MatchStatementOwner(req); actionErr != nil {
		HandleError(c, actionErr)
		return
	}
	c.JSON(201, model.Success{Message: "Confirm success"})
}

// @Summary IgnoreStatementOwner เพิกเฉยข้อมูลการเดินบัญชี
// @Description เพิกเฉยข้อมูลการเดินบัญชี
// @Tags Banking - Bank Account Statements
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/statements/ignoreowner/{id} [post]
func (h bankingController) ignoreStatementOwner(c *gin.Context) {

	id := c.Param("id")
	identifier, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleError(c, err)
		return
	}

	adminId, err := h.accountingService.CheckCurrentAdminId(c.MustGet("adminId"))
	if err != nil {
		HandleError(c, err)
		return
	}

	var req model.BankStatementMatchRequest
	req.ConfirmedAt = time.Now()
	req.ConfirmedByAdminId = adminId
	actionErr := h.bankingService.IgnoreStatementOwner(identifier, req)
	if actionErr != nil {
		HandleError(c, actionErr)
		return
	}
	c.JSON(201, model.Success{Message: "Ignore success"})
}

// @Summary DeleteBankStatement
// @Description ลบข้อมูลการโอนด้วย id ใช้ในหน้า จัดการธนาคาร - ธุรกรรม ส่งรหัสผ่านมาเพื่อยืนยันด้วย
// @Tags Banking - Bank Account Statements
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/statements/{id} [delete]
func (h bankingController) deleteBankStatement(c *gin.Context) {

	id := c.Param("id")
	identifier, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleError(c, err)
		return
	}

	actionErr := h.bankingService.DeleteBankStatement(identifier)
	if actionErr != nil {
		HandleError(c, actionErr)
		return
	}
	c.JSON(201, model.Success{Message: "Deleted success"})
}

// @Summary GetBankTransactions
// @Description ดึงข้อมูลลิสการฝากถอน
// @Tags Banking - Bank Transaction
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.BankTransactionListRequest true "BankTransactionListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/transactions/list [get]
func (h bankingController) getBankTransactions(c *gin.Context) {

	var query model.BankTransactionListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.bankingService.GetBankTransactions(query)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, model.SuccessWithPagination{List: data.List, Total: data.Total})
}

// @Summary GetBankDepositTransStatusCounts ดึงข้อมูลจำนวนรายการฝากทั้งหมด ตามสถานะ
// @Description ดึงข้อมูลจำนวนรายการฝากทั้งหมด ตามสถานะ
// @Tags Banking - Bank Transaction
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.GetBankTransactionDepositCountRequest true "GetBankTransactionDepositCountRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/transactions/count_deposit_statuses [get]
func (h bankingController) getBankDepositTransStatusCounts(c *gin.Context) {

	var req model.GetBankTransactionDepositCountRequest
	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.bankingService.GetBankDepositTransStatusCounts(req)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithData{Message: "success", Data: data})
}

// @Summary GetBankWithdrawTransStatusCounts ดึงข้อมูลจำนวนรายการถอนทั้งหมด ตามสถานะ
// @Description ดึงข้อมูลจำนวนรายการถอนทั้งหมด ตามสถานะ
// @Tags Banking - Bank Transaction
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.GetBankTransactionWithdrawCountRequest true "GetBankTransactionWithdrawCountRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/transactions/count_withdraw_statuses [get]
func (h bankingController) getBankWithdrawTransStatusCounts(c *gin.Context) {

	var req model.GetBankTransactionWithdrawCountRequest
	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.bankingService.GetBankWithdrawTransStatusCounts(req)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithData{Message: "success", Data: data})
}

// @Summary GetBankTransactionById
// @Description ดึงข้อมูลการฝากถอน ด้วย id
// @Tags Banking - Bank Transaction
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/transactions/detail/{id} [get]
func (h bankingController) getBankTransactionById(c *gin.Context) {

	var req model.BankTransactionGetRequest

	if err := c.ShouldBindUri(&req); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.bankingService.GetBankTransactionById(req)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithData{Message: "success", Data: data})
}

// @Summary CancelPendingTransaction ยกเลิก ข้อมูลการฝากและถอน ที่รอยืนยัน
// @Description ยกเลิก ไม่ยืนยัน ข้อมูลการฝากและถอน ที่รอยืนยัน
// @Tags Banking - Bank Transaction
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Param body body model.BankTransactionCancelBody true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/transactions/cancel/{id} [post]
func (h bankingController) cancelPendingTransaction(c *gin.Context) {

	adminId, err := h.accountingService.CheckCurrentAdminId(c.MustGet("adminId"))
	if err != nil {
		HandleError(c, err)
		return
	}

	id := c.Param("id")
	identifier, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleError(c, err)
		return
	}

	var data model.BankTransactionCancelBody
	if err := c.ShouldBind(&data); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(data); err != nil {
		HandleError(c, err)
		return
	}

	data.TransactionStatusId = model.TRANS_STATUS_WITHDRAW_REJECTED
	// data.CancelRemark = data.CancelRemark
	data.CanceledAt = time.Now()
	data.CanceledByAdminId = *adminId

	actionErr := h.bankingService.CancelPendingTransaction(identifier, data)
	if actionErr != nil {
		HandleError(c, actionErr)
		return
	}
	c.JSON(201, model.Success{Message: "Cancel success"})
}

// @Summary ConfirmDepositTransaction ยืนยันข้อมูลการฝาก เฉยๆ
// @Description ยืนยันข้อมูลการฝาก เฉยๆ
// @Tags Banking - Bank Transaction
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Param body body model.BankConfirmDepositRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/transactions/confirmdeposit/{id} [post]
func (h bankingController) confirmDepositTransaction(c *gin.Context) {

	adminId, err := h.accountingService.CheckCurrentAdminId(c.MustGet("adminId"))
	if err != nil {
		HandleError(c, err)
		return
	}

	id := c.Param("id")
	identifier, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleError(c, err)
		return
	}

	var req model.BankConfirmDepositRequest
	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(req); err != nil {
		HandleError(c, err)
		return
	}
	req.ConfirmedAt = time.Now()
	req.ConfirmedByAdminId = adminId
	actionErr := h.bankingService.ConfirmDepositTransaction(identifier, req)
	if actionErr != nil {
		HandleError(c, actionErr)
		return
	}
	c.JSON(201, model.Success{Message: "Confirm success"})
}

// @Summary ConfirmDepositCreditTransaction ยืนยันข้อมูลการฝาก เพื่ออนุมัติเครดิต
// @Description ยืนยันข้อมูลการฝาก เพื่ออนุมัติเครดิต
// @Tags Banking - Bank Transaction
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Param body body model.BankConfirmDepositRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/transactions/confirmdepositcredit/{id} [post]
func (h bankingController) confirmDepositCreditTransaction(c *gin.Context) {

	adminId, err := h.accountingService.CheckCurrentAdminId(c.MustGet("adminId"))
	if err != nil {
		HandleError(c, err)
		return
	}

	id := c.Param("id")
	identifier, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleError(c, err)
		return
	}

	var req model.BankConfirmDepositRequest
	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(req); err != nil {
		HandleError(c, err)
		return
	}
	req.ConfirmedAt = time.Now()
	req.ConfirmedByAdminId = adminId
	actionCreditErr := h.bankingService.ConfirmDepositCredit(identifier, req)
	if actionCreditErr != nil {
		HandleError(c, actionCreditErr)
		return
	}
	c.JSON(201, model.Success{Message: "Confirm success"})
}

// @Summary confirmCreditWithdrawTransaction ยืนยัน/อนุมัติ รายการถอน ในสถานะ รอปรับเครดิต
// @Description ยืนยัน/อนุมัติ รายการถอน ในสถานะ รอปรับเครดิต
// @Tags Banking - Bank Transaction
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Param body body model.BankConfirmCreditWithdrawRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/transactions/confirmcreditwithdraw/{id} [post]
func (h bankingController) confirmCreditWithdrawTransaction(c *gin.Context) {

	id := c.Param("id")
	identifier, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleError(c, err)
		return
	}

	adminId, err := h.accountingService.CheckCurrentAdminId(c.MustGet("adminId"))
	if err != nil {
		HandleError(c, err)
		return
	}

	var req model.BankConfirmCreditWithdrawRequest
	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(req); err != nil {
		HandleError(c, err)
		return
	}
	req.ConfirmedAt = time.Now()
	req.ConfirmedByAdminId = adminId

	actionErr := h.bankingService.ConfirmWithdrawTransaction(identifier, req)
	if actionErr != nil {
		HandleError(c, actionErr)
		return
	}
	c.JSON(201, model.Success{Message: "Confirm success"})
}

// @Summary confirmTransferWithdrawTransaction ยืนยัน/อนุมัติ รายการถอน ในสถานะ รอโอน
// @Description ยืนยัน/อนุมัติ รายการถอน ในสถานะ รอโอน
// @Tags Banking - Bank Transaction
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Param body body model.BankConfirmTransferWithdrawRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/transactions/confirmtransferwithdraw/{id} [post]
func (h bankingController) confirmTransferWithdrawTransaction(c *gin.Context) {

	id := c.Param("id")
	identifier, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleError(c, err)
		return
	}

	adminId, err := h.accountingService.CheckCurrentAdminId(c.MustGet("adminId"))
	if err != nil {
		HandleError(c, err)
		return
	}

	var req model.BankConfirmTransferWithdrawRequest
	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(req); err != nil {
		HandleError(c, err)
		return
	}
	req.ConfirmedAt = time.Now()
	req.ConfirmedByAdminId = adminId

	actionErr := h.bankingService.ConfirmWithdrawTransfer(identifier, req)
	if actionErr != nil {
		HandleError(c, actionErr)
		return
	}
	c.JSON(201, model.Success{Message: "Confirm success"})
}

// @Summary (getMemberByCode) showLimit := 30
// @Description (getMemberByCode) ดึงข้อมูลสมาชิกด้วยโค้ด
// @Tags Banking - Member Transaction
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param code path string true "memberCode"
// @Success 200 {object} model.MemberForDropdownResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/member/info/{code} [get]
func (h bankingController) getMemberByCode(c *gin.Context) {

	memberCode := c.Param("code")

	result, err := h.bankingService.GetMemberByCode(memberCode)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, result)
}

// @Summary (getMembers) ดึงข้อมูลลิสมาชิก
// @Description (getMembers) ดึงข้อมูลลิสมาชิก
// @Description [********] ดึงข้อมูลสมาชิกที่ Active เท่านั้น
// @Tags Banking - Bank Account Statements
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.MemberListRequest true "MemberListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/member/list [get]
func (h bankingController) getMembers(c *gin.Context) {

	var query model.MemberListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.bankingService.GetMembers(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithPagination{List: data.List, Total: data.Total})
}

// @Summary GetMemberTransactionSummary
// @Description ดึงข้อมูลสรุปการฝากถอนของสมาชิก
// @Tags Banking - Member Transaction
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.MemberTransactionListRequest true "query"
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/member/transactionsummary [get]
func (h bankingController) getMemberTransactionSummary(c *gin.Context) {

	var query model.MemberTransactionListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.bankingService.GetMemberTransactionSummary(query)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, model.SuccessWithData{Message: "success", Data: data})
}

// @Summary GetMemberTransactions
// @Description ดึงข้อมูลลิสการฝากถอนของสมาชิก
// @Tags Banking - Member Transaction
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.MemberTransactionListRequest true "query"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/member/transactions [get]
func (h bankingController) getMemberTransactions(c *gin.Context) {

	var query model.MemberTransactionListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.bankingService.GetMemberTransactions(query)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, model.SuccessWithPagination{List: data.List, Total: data.Total})
}

// @Summary GetMemberStatements ดึงข้อมูลรายการทางการเงินทั้งหมดของสมาชิก
// @Description ดึงข้อมูลรายการทางการเงินทั้งหมดของสมาชิก
// @Tags Banking - Member Statement
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.MemberTransactionListRequest true "query"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/member/statements [get]
func (h bankingController) getMemberStatements(c *gin.Context) {

	var query model.MemberStatementListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.bankingService.GetMemberStatements(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithPagination{List: data.List, Total: data.Total})
}

// @Summary GetMemberStatementById
// @Description ดึงข้อมูลรายการทางการเงินทั้งหมดของสมาชิกด้วย id
// @Tags Banking - Member Statement
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/member/statements/detail/{id} [get]
func (h bankingController) getMemberStatementById(c *gin.Context) {

	var req model.GetByIdRequest
	if err := c.ShouldBindUri(&req); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.bankingService.GetMemberStatementById(req)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithData{Message: "success", Data: data})
}

// @Summary ProcessMemberDepositCredit
// @Description สร้างข้อมูลรายการทางการเงิน
// @Tags Banking - Member Statement
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.MemberStatementCreateRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/member/statements1 [post]
func (h bankingController) processMemberDepositCredit(c *gin.Context) {

	var req model.MemberStatementCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(req); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.bankingService.ProcessMemberDepositCredit(req.UserId, req.Amount); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Created success"})
}

// @Summary ProcessMemberWithdrawCredit
// @Description สร้างข้อมูลรายการทางการเงิน
// @Tags Banking - Member Statement
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.MemberStatementCreateRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/member/statements2 [post]
func (h bankingController) processMemberWithdrawCredit(c *gin.Context) {

	var req model.MemberStatementCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(req); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.bankingService.ProcessMemberWithdrawCredit(req.UserId, req.Amount); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Created success"})
}

// @Summary ProcessMemberBonusCredit
// @Description สร้างข้อมูลรายการทางการเงิน
// @Tags Banking - Member Statement
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.MemberStatementCreateRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/member/statements3 [post]
func (h bankingController) processMemberBonusCredit(c *gin.Context) {

	var req model.MemberStatementCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(req); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.bankingService.ProcessMemberBonusCredit(req.UserId, req.Amount); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Created success"})
}

// @Summary ProcessMemberGetbackCredit
// @Description สร้างข้อมูลรายการทางการเงิน
// @Tags Banking - Member Statement
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.MemberStatementCreateRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/member/statements4 [post]
func (h bankingController) processMemberGetbackCredit(c *gin.Context) {

	var req model.MemberStatementCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(req); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.bankingService.ProcessMemberGetbackCredit(req.UserId, req.Amount); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Created success"})
}

// @Summary GetPossibleOwnersByStatementId หน้า รายการฝาก
// @Description (getPossibleOwnersByStatementId) ดึงข้อมูลลิสสมาชิก ที่มีข้อมูลใกล้เคียงกับรายการสเตทเม้นที่รอดำเนินการ
// @Tags Banking - Bank Transaction v2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router 	/banking/transactions/possibleowners/{id} [get]
func (h bankingController) getPossibleOwnersByStatementId(c *gin.Context) {

	var req model.GetByIdRequest
	if err := c.ShouldBindUri(&req); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.bankingService.GetPossibleOwnersByStatementId(req.Id)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary GetBankTransactionDepositList ดึงข้อมูลรายการฝาก
// @Description ดึงข้อมูลรายการฝาก
// @Description | status | Description |
// @Description | ---------------- | ------------|
// @Description | PENDING_ALL |  ยังไม่เรียบร้อย |
// @Description | PENDING | รอดำเนินการ |
// @Description | PENDING_CREDIT | รอตรวจสอบ |
// @Description | CREDIT_APPROVED | สำเร็จ|
// @Description | CREDIT_REJECTED | ไม่สำเร็จ |
// @Description | ---------------- | ------------|
// @Tags Banking - Bank Transaction v2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.GetBankTransactionDepositListRequest true "GetBankTransactionDepositListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/transactions/deposit-list [get]
func (h bankingController) getBankTransactionDepositList(c *gin.Context) {

	var req model.GetBankTransactionDepositListRequest
	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.bankingService.GetBankTransactionDepositList(req)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (getWebUserTransactionList) ดึงข้อมูลรายการฝาก-ถอนของ user
// @Description ดึงข้อมูลรายการฝาก
// @Tags Banking - Web Bank Transaction v2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.GetUserTransactionListRequest true "GetUserTransactionListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/user-transaction-list [get]
func (h bankingController) getWebUserTransactionList(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var req model.GetUserTransactionListRequest
	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(req); err != nil {
		HandleError(c, err)
		return
	}

	req.UserId = userId
	data, err := h.bankingService.GetWebUserTransactionList(req)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (userCreateWithDrawTransaction) สร้างข้อมูลการถอน
// @Description (userCreateWithDrawTransaction)
// @Tags Banking - Web Bank Transaction v2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.UserCreateWithdrawTransactionRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/withdrawal [post]
func (h bankingController) userCreateWithDrawTransaction(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))
	var withdraw model.UserCreateWithdrawTransactionRequest
	if err := c.ShouldBindJSON(&withdraw); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(withdraw); err != nil {
		HandleError(c, err)
		return
	}
	withdraw.UserId = userId

	if _, err := h.bankingService.UserCreateWithdrawTransaction(withdraw); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Created success"})
}

// @Summary createAutoWithdraw กด ถอน ยืนยันและโอนอัตโนมัติ
// @Description กด withdraw auto ใช้ได้แค่ กรณี amount มากกว่า config แต่ไม่เกินวงเงิน
// @Tags Banking - Bank Transaction v2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.CreateAutoWithdrawRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/transactions/withdraw-auto [post]
func (h bankingController) createAutoWithdraw(c *gin.Context) {

	var req model.CreateAutoWithdrawRequest
	adminId, err := h.accountingService.CheckCurrentAdminId(c.MustGet("adminId"))
	if err != nil {
		HandleError(c, err)
		return
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}
	req.ConfirmedAt = time.Now()
	req.ConfirmedByAdminId = adminId

	// UserCreateWithdrawTransaction(req model.UserCreateWithdrawTransactionRequest) (*int64, error)
	res, err := h.bankingService.CreateAutoWithdraw(req)
	if err != nil {
		HandleError(c, err)
		return
	}
	if res.Status == 200 {
		c.JSON(201, res)
	} else {
		c.JSON(400, res)
	}
}

// @Summary CreateTransWithdrawWithSelectedAccount เลือกบัญชี และ สร้างรายการถอน
// @Description CreateTransWithdrawWithSelectedAccount เลือกบัญชี และ สร้างรายการถอน
// @Tags Banking - Bank Transaction v2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.CreateTransWithdrawWithSelectedAccountRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/transactions/withdraw-selected-account [post]
func (h bankingController) createTransWithdrawWithSelectedAccount(c *gin.Context) {

	var req model.CreateTransWithdrawWithSelectedAccountRequest
	adminId, err := h.accountingService.CheckCurrentAdminId(c.MustGet("adminId"))
	if err != nil {
		HandleError(c, err)
		return
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}
	req.ConfirmedAt = time.Now()
	req.ConfirmedByAdminId = adminId

	// UserCreateWithdrawTransaction(req model.UserCreateWithdrawTransactionRequest) (*int64, error)
	res, err := h.bankingService.CreateTransWithdrawWithSelectedAccount(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	if res.Status == 200 {
		c.JSON(201, res)
	} else {
		c.JSON(400, res)
	}
}

// @Summary CreateTransWithdrawWithManualAccount ยืนยันโดย โอนมือ
// @Description CreateTransWithdrawWithManualAccount ยืนยันโดย โอนมือ
// @Tags Banking - Bank Transaction v2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.CreateTransWithdrawWithManualAccountRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/transactions/withdraw-manual-account [post]
func (h bankingController) createTransWithdrawWithManualAccount(c *gin.Context) {

	adminId, err := h.accountingService.CheckCurrentAdminId(c.MustGet("adminId"))
	if err != nil {
		HandleError(c, err)
		return
	}

	var req model.CreateTransWithdrawWithManualAccountRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}
	req.ConfirmedAt = time.Now()
	req.ConfirmedByAdminId = adminId

	// UserCreateWithdrawTransaction(req model.UserCreateWithdrawTransactionRequest) (*int64, error)
	if _, err = h.bankingService.CreateTransWithdrawWithManualAccount(req); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Created success"})
}

// @Summary CreateTransWithdrawWithExternalAccount ยืนยันโดย โอนผ่านบัญชีธนาคารนอกระบบ
// @Description CreateTransWithdrawWithExternalAccount ยืนยันโดย โอนผ่านบัญชีธนาคารนอกระบบ
// @Tags Banking - Bank Transaction v2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.CreateTransWithdrawWithExternalAccountRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/transactions/withdraw-external-account [post]
func (h bankingController) createTransWithdrawWithExternalAccount(c *gin.Context) {

	var req model.CreateTransWithdrawWithExternalAccountRequest
	adminId, err := h.accountingService.CheckCurrentAdminId(c.MustGet("adminId"))
	if err != nil {
		HandleError(c, err)
		return
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}
	req.ConfirmedAt = time.Now()
	req.ConfirmedByAdminId = adminId

	// UserCreateWithdrawTransaction(req model.UserCreateWithdrawTransactionRequest) (*int64, error)
	_, err = h.bankingService.CreateTransWithdrawWithExternalAccount(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(201, model.Success{Message: "Created success"})
}

// @Summary GetBankTransactionWithdrawList ดึงข้อมูลรายการถอน
// @Description GetBankTransactionWithdrawList ดึงข้อมูลรายการถอน
// @Description | Status           | Description          |
// @Description | ---------------- | ------------|
// @Description | PENDING_ALL |  ยังไม่เรียบร้อย |
// @Description | PENDING_CREDIT | รอปรับเครดิต |
// @Description | PENDING_TRANSFER | รอโอน |
// @Description | TRANSFERING | อยู่ในระหว่างการโอน |
// @Description | SUCCESS | สำเร็จ|
// @Description | FAILED | ไม่สำเร็จ |
// @Description | CANCELED | ถูกยกเลิกจากแอดมิน |
// @Description | ---------------- | ------------|
// @Tags Banking - Bank Transaction v2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.GetBankTransactionWithdrawListRequest true "GetBankTransactionWithdrawListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/transactions/withdraw-list [get]
func (h bankingController) getBankTransactionWithdrawList(c *gin.Context) {

	var req model.GetBankTransactionWithdrawListRequest
	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.bankingService.GetBankTransactionWithdrawList(req)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary GetDepositTransactionById ดึงข้อมูลรายการฝาก
// @Description GetDepositTransactionById ดึงข้อมูลรายการฝาก
// @Tags Banking - Bank Transaction v2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/transactions/deposit/{id} [get]
func (h bankingController) getDeposiTransactionById(c *gin.Context) {

	var req model.GetByIdRequest
	if err := c.ShouldBindUri(&req); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.bankingService.GetDepositTransactionById(req.Id)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary GetTransactionWithdrawById ดึงข้อมูลรายการถอน
// @Description GetTransactionWithdrawById ดึงข้อมูลรายการถอน
// @Tags Banking - Bank Transaction v2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/transactions/withdraw/{id} [get]
func (h bankingController) getTransactionWithdrawById(c *gin.Context) {

	var req model.GetByIdRequest
	if err := c.ShouldBindUri(&req); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.bankingService.GetTransactionWithdrawById(req.Id)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary CancelWithdrawCredit ยกเลิกการถอน
// @Description CancelWithdrawCredit ยกเลิกการถอน
// @Tags Banking - Bank Transaction v2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.CancelWithdrawCreditRequest true "body"
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/transactions/cancel-withdraw [post]
func (h bankingController) cancelWithdrawCredit(c *gin.Context) {

	var req model.CancelWithdrawCreditRequest
	adminId, err := h.accountingService.CheckCurrentAdminId(c.MustGet("adminId"))
	if err != nil {
		HandleError(c, err)
		return
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}
	req.ConfirmedAt = time.Now()
	req.ConfirmedByAdminId = adminId

	data, err := h.bankingService.CancelWithdrawCredit(req)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (checkTransferingWithdraw) ตรวจสอบสถานะการโอนเงิน
// @Description (checkTransferingWithdraw) ตรวจสอบสถานะการโอนเงิน จะส่งกลับมาเป็นสถานะ "TRANSFERING" หรือ "SUCCESS" หรือ "FAILED"
// @Tags Banking - Bank Transaction v2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.WithdrawCheckTransferingRequest true "body"
// @Success 200 {object} model.WithdrawCheckTransferingResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/transactions/check-transfering-withdraw [post]
func (h bankingController) checkTransferingWithdraw(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	var req model.WithdrawCheckTransferingRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}
	req.ConfirmedBy = adminId

	data, err := h.bankingService.CheckTransferingWithdraw(req)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary   อัพเดทการ ignore รายการฝาก
// @Description UpdateIgnoreDeposit อัพเดทการ ignore รายการฝาก
// @Tags Banking - Bank Transaction v2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.CreateIgnoredTransacionRequest true "body"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/transactions/ignore-deposit [post]
func (h bankingController) updateIgnoreDeposit(c *gin.Context) {

	var req model.CreateIgnoredTransacionRequest
	adminId, err := h.accountingService.CheckCurrentAdminId(c.MustGet("adminId"))
	if err != nil {
		HandleError(c, err)
		return
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}
	req.ConfirmedAt = time.Now()
	req.ConfirmedByAdminId = adminId

	if _, err = h.bankingService.UpdateIgnoreDeposit(req); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "Updated success"})
}

// @Summary (uploadImageToCloudflareDepositSlip) Upload Image To Cloudflare Deposit Slip
// @Description (uploadImageToCloudflareDepositSlip) Upload Image To Cloudflare Deposit Slip
// @Tags Banking - Bank Transaction v2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param file formData file true "file"
// @Success 200 {object} model.FileUploadResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/transactions/upload/deposit-slip [post]
func (h *bankingController) uploadImageToCloudflareDepositSlip(c *gin.Context) {

	data, err := h.bankingService.UploadImageToS3DepositSlip(c.Request)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (CreateDepositRecord) สร้างรายการฝาก
// @Description (CreateDepositRecord) สร้างรายการฝาก
// @Tags Banking - Bank Transaction v2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.CreateDepositRecordRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/transactions/deposit-record [post]
func (h bankingController) createDepositRecord(c *gin.Context) {

	var req model.CreateDepositRecordRequest
	adminId, err := h.accountingService.CheckCurrentAdminId(c.MustGet("adminId"))
	if err != nil {
		HandleError(c, err)
		return
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}
	req.CreateAt = time.Now()
	req.CreateByAdminId = *adminId

	if _, err = h.bankingService.CreateDepositRecord(req); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Created success"})
}

// CreateFreeBonus(req model.CreateFreeBonusRequest) (*int64, error)
// @Summary CreateFreeBonus สร้างรายการโบนัสฟรีฝาก
// @Description CreateFreeBonus สร้างรายการฝาก
// @Tags Banking - Bank Transaction v2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.CreateFreeBonusRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/transactions/free-bonus [post]
func (h bankingController) createFreeBonus(c *gin.Context) {

	var req model.CreateFreeBonusRequest
	adminId, err := h.accountingService.CheckCurrentAdminId(c.MustGet("adminId"))
	if err != nil {
		HandleError(c, err)
		return
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}
	req.ConfirmedAt = time.Now()
	req.ConfirmedByAdminId = adminId

	_, err = h.bankingService.CreateFreeBonus(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(201, model.Success{Message: "Created success"})
}

// @Summary CreateWithdrawRecord สร้างรายการถอน
// @Description CreateWithdrawRecord สร้างรายการถอน
// @Tags Banking - Bank Transaction v2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.CreateWithdrawRecordRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/transactions/withdraw-record [post]
func (h bankingController) createWithdrawRecord(c *gin.Context) {

	var req model.CreateWithdrawRecordRequest
	adminId, err := h.accountingService.CheckCurrentAdminId(c.MustGet("adminId"))
	if err != nil {
		HandleError(c, err)
		return
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}
	req.ConfirmedAt = time.Now()
	req.ConfirmedByAdminId = adminId

	res, err := h.bankingService.CreateWithdrawRecord(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	if res.Status == 200 {
		c.JSON(201, res)
	} else {
		c.JSON(400, res)
	}
}

// @Summary (CreateWithdrawPullCreditBack) ถอนเครดิตกลับ
// @Description (CreateWithdrawPullCreditBack)  ถอนเครดิตกลับ
// @Description [********] ย้ายไปอยู่ที่ User และไม่แสดงที่ รายการถอน
// @Tags Banking - Bank Transaction v2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.CreateWithdrawPullCreditBackRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/transactions/withdraw-credit-back [post]
func (h bankingController) createWithdrawPullCreditBack(c *gin.Context) {

	var req model.CreateWithdrawPullCreditBackRequest
	adminId, err := h.accountingService.CheckCurrentAdminId(c.MustGet("adminId"))
	if err != nil {
		HandleError(c, err)
		return
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}
	req.ConfirmedAt = time.Now().UTC()
	req.ConfirmedByAdminId = adminId

	if _, err = h.bankingService.CreateWithdrawPullCreditBack(req); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Created success"})
}

// @Summary (createUserCancelCredit) ยกเลิกเติมเครดิต
// @Description (createUserCancelCredit) ปุ่ม ดึงเครดิตกลับ ที่หน้าถอน จะเปลี่ยนเป็น ยกเลิกเติมเครดิต Modal กรอกเหมือนเดิม เพิ่ม remark ( หน้าบ้านเปลี่ยน API )
// @Description รายการ จะขึ้นที่ รายการถอน เพราะ กระทบกับระบบธนาคาร
// @Description ยอด จะถูกหัก ออกจาก ยอดฝาก ทุกที่ ( สรุปภาพรวม ฝากถอนเสร็จสิ้น )
// @Description ไม่ใช่ ยอด ดึงเครดิตกลับ
// @Tags Banking - Bank Transaction v2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.CreateUserCancelCreditRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/transactions/user-credit-cancel [post]
func (h bankingController) createUserCancelCredit(c *gin.Context) {

	adminId, err := h.accountingService.CheckCurrentAdminId(c.MustGet("adminId"))
	if err != nil {
		HandleError(c, err)
		return
	}

	var req model.CreateUserCancelCreditRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}
	req.ConfirmedAt = time.Now().UTC()
	req.ConfirmedByAdminId = adminId

	if _, err = h.bankingService.CreateUserCancelCredit(req); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Created success"})
}

// @Summary IsFirstDeposit ทดสอบเช็คฝากครั้งแรก id = user id
// @Description IsFirstDeposit ทดสอบเช็คฝากครั้งแรก id = user id
// @Tags Test
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /test/is-first-deposit/{id} [get]
func (h bankingController) IsFirstDeposit(c *gin.Context) {

	var req model.GetByIdRequest
	if err := c.ShouldBindUri(&req); err != nil {
		HandleError(c, err)
		return
	}

	result, err := h.bankingService.IsFirstDeposit(req.Id)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithData{Message: "success", Data: result})
}

// @Summary CreateFristTimeDepositRecord สร้างรายการฝากครั้งแรก
// @Description CreateFristTimeDepositRecord สร้างรายการฝากครั้งแรก
// @Tags Banking - Bank Transaction v2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.CreateFristTimeDepositRecordRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/transactions/first-time-deposit-record [post]
func (h bankingController) createFristTimeDepositRecord(c *gin.Context) {

	var req model.CreateFristTimeDepositRecordRequest
	adminId, err := h.accountingService.CheckCurrentAdminId(c.MustGet("adminId"))
	if err != nil {
		HandleError(c, err)
		return
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}
	req.CreateAt = time.Now()
	req.CreateByUserId = *adminId

	_, err = h.bankingService.CreateFristTimeDepositRecord(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(201, model.Success{Message: "Created success"})
}

// @Summary WebCheckUserDuplicateWithdrawProeccing ตรวจสอบการถอนซ้ำ
// @Description WebCheckUserDuplicateWithdrawProeccing ตรวจสอบการถอนซ้ำ
// @Tags Banking - Web Bank Transaction v2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 201 {object} model.CheckUserDuplicateWithdrawResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/check-user-duplicate-withdraw [get]
func (h bankingController) webCheckUserDuplicateWithdrawProeccing(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))
	data, err := h.bankingService.WebCheckUserDuplicateWithdrawProeccing(userId)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary GetBankTransactionFirstTimeDeposit ดึงข้อมูลรายการฝากครั้งแรก
// @Description GetBankTransactionFirstTimeDeposit ดึงข้อมูลรายการฝากครั้งแรก
// @Description กรองข้อมูลตามประเภทวัน ส่ง dateType เป็น daily, yesterday, last_week, last_month
// @Description กรองข้อมูลตามช่วงวันที่ ส่ง startDate, endDate ในรูปแบบ YYYY-MM-DD (2021-12-31)
// @Description [********] เพิ่มวันที่สมัครสมาชิก
// @Description
// @Tags Banking - Bank Transaction v2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.GetBankTransactionFirstTimeDepositRequest true "GetBankTransactionFirstTimeDepositRequest"
// @Success 200 {object} model.CheckUserDuplicateWithdrawResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/transactions/first-time-deposit-list [get]
func (h bankingController) getBankTransactionFirstTimeDeposit(c *gin.Context) {

	var req model.GetBankTransactionFirstTimeDepositRequest
	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.bankingService.GetBankTransactionFirstTimeDeposit(req)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary RetryDepositAgent สร้างรายการฝากใหม่
// @Description RetryDepositAgent สร้างรายการฝากใหม่
// @Tags Banking - Bank Transaction v2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param refId path int64 true "refId"
// @Success 201 {object} string
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/transactions/retry-deposit-agent/{refId} [post]
func (h bankingController) retryDepositAgent(c *gin.Context) {

	var req model.RetryDepositAgentRequest
	adminId, err := h.accountingService.CheckCurrentAdminId(c.MustGet("adminId"))
	if err != nil {
		HandleError(c, err)
		return
	}

	id := c.Param("refId")
	identifier, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleError(c, err)
		return
	}

	req.ConfirmedAt = time.Now()
	req.ConfirmedByAdminId = adminId
	req.RefId = identifier
	res, err := h.bankingService.RetryDepositAgent(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(201, res)

}

// @Summary CheckAdminDuplicateWithdrawList ตรวจสอบการถอนซ้ำ
// @Description CheckAdminDuplicateWithdrawList ตรวจสอบการถอนซ้ำ
// @Tags Banking - Bank Transaction v2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.CheckAdminDuplicateWithdrawListRequest true "CheckAdminDuplicateWithdrawListRequest"
// @Success 200 {object} model.CheckAdminDuplicateWithdrawList
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/transactions/check/admin-duplicate-withdraw [get]
func (h bankingController) checkAdminDuplicateWithdrawList(c *gin.Context) {

	var req model.CheckAdminDuplicateWithdrawListRequest
	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.bankingService.CheckAdminDuplicateWithdrawList(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)

}

// @Summary GetBankTransactionExternalDetailByBankTransactionId ดึงข้อมูล detail รายการฝาก
// @Description GetBankTransactionExternalDetailByBankTransactionId ดึงข้อมูลรายการฝาก
// @Tags Banking - Bank Transaction v2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 200 {object} model.BankTransactionExternalDetailGetByBankTransactionIdResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/transactions/external-detail/{id} [get]
func (h bankingController) getBankTransactionExternalDetailByBankTransactionId(c *gin.Context) {

	var req model.BankTransactionExternalDetailGetByBankTransactionIdRequest
	if err := c.ShouldBindUri(&req); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.bankingService.GetBankTransactionExternalDetailByBankTransactionId(req.Id)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary ConfirmWithdrawTransaction ยืนยันรายการโอน
// @Description ConfirmWithdrawTransaction ยืนยันรายการโอน
// @Tags Banking - Bank Transaction v2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.CreateAutoWithdrawRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/transactions/confirm-withdraw-transaction [post]
func (h bankingController) confirmWithdrawTransactionAnyStatus(c *gin.Context) {

	var req model.CreateAutoWithdrawRequest
	adminId, err := h.accountingService.CheckCurrentAdminId(c.MustGet("adminId"))
	if err != nil {
		HandleError(c, err)
		return
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}
	req.ConfirmedAt = time.Now()
	req.ConfirmedByAdminId = adminId

	err = h.bankingService.ConfirmWithdrawTransactionAnyStatus(req)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Created success"})
}

// @Summary (userUploadImageToCloudflareDepositSlip) อัพโหลดสลิป เมื่อ ผู้ใช้แจ้งฝาก แบบอัพโหลดภาพเฉยๆ
// @Description (userUploadImageToCloudflareDepositSlip) อัพโหลดสลิป เมื่อ ผู้ใช้แจ้งฝาก แบบอัพโหลดภาพเฉยๆ
// @Tags Banking - Bank Transaction v2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param file formData file true "file"
// @Success 200 {object} model.FileUploadResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/banking/transactions/upload/deposit-slip [post]
func (h *bankingController) userUploadImageToCloudflareDepositSlip(c *gin.Context) {

	// todo checkfile name overwriting?
	data, err := h.bankingService.UploadImageToS3DepositSlip(c.Request)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (uploadImageToCloudflareBonusSlip) อัพโหลดสลิป bonus แก้เขินเว็บ solo ไม่ใช้เป็นฝากปกติ ระวังเคสไม่ตรวจ รายการซ้ำ
// @Description (uploadImageToCloudflareBonusSlip) อัพโหลดสลิป เมื่อ ผู้ใช้แจ้งโบนัส แบบอัพโหลดภาพเฉยๆ
// @Tags Banking - Bank Transaction v2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param file formData file true "file"
// @Success 200 {object} model.FileUploadResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/transactions/upload/bonus-slip [post]
func (h *bankingController) uploadImageToCloudflareBonusSlip(c *gin.Context) {

	data, err := h.bankingService.UploadImageToS3BonusSlip(c.Request)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary (userDepositByUploadFile) ผู้ใช้แจ้งฝาก แบบอัพโหลดภาพเฉยๆ
// @Description (userDepositByUploadFile) ผู้ใช้แจ้งฝาก แบบอัพโหลดภาพเฉยๆ
// @Tags Banking - Bank Transaction v2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.CreateDepositFromUserUploadRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/banking/transactions/deposit-by-upload [post]
func (h bankingController) userDepositByUploadFile(c *gin.Context) {

	var req model.CreateDepositFromUserUploadRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}
	req.UserId = int64(c.MustGet("userId").(float64))

	if _, err := h.bankingService.UserDepositByUploadFile(req); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Created success"})
}

// @Summary (UserDepositBySlip) ยืนยันการฝากจากผู้ใช้
// @Description (UserDepositBySlip) ยืนยันการฝากจากผู้ใช้
// @Tags Banking - Bank Transaction v2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.CreateDepositConfirmedFromUserRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/banking/transactions/deposit-by-slip [post]
func (h bankingController) userDepositBySlip(c *gin.Context) {

	var req model.CreateDepositConfirmedFromUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}
	req.UserId = int64(c.MustGet("userId").(float64))

	if _, err := h.bankingService.UserDepositBySlip(req); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Created success"})
}

// @Summary CheckScammerSlip ตรวจสอบหลักฐานการโอน
// @Description CheckScammerSlip ตรวจสอบหลักฐานการโอน
// @Tags Banking - Bank Transaction v2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.CheckScammerSlipResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/banking/transactions/check-scammer-slip [get]
func (h bankingController) checkScammerSlip(c *gin.Context) {
	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var req model.CheckScammerSlipRequest
	req.UserId = userId

	data := h.bankingService.CheckScammerSlipRequest(req)
	c.JSON(200, data)
}

// CheckOtherExistTurnOver(userId int64) error
// @Summary CheckOtherExistTurnOver ตรวจสอบการมีรายการเทินโอเวอร์อื่น
// @Description CheckOtherExistTurnOver ตรวจสอบการมีรายการเทินโอเวอร์อื่น
// @Tags Banking - Bank Transaction v2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 200 {object} model.CheckScammerSlipResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /test/check-other-exist-turnover/{id} [get]
func (h bankingController) checkOtherExistTurnOver(c *gin.Context) {
	var req model.GetByIdRequest

	if err := c.ShouldBindUri(&req); err != nil {
		HandleError(c, err)
		return
	}
	data, turn, err := h.bankingService.CheckOtherExistTurnOver(req.Id)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, gin.H{"message": data, "turn": turn})
}

// TurnOverWithdrawChecker(userId int64, amount float64) error
// @Summary TurnOverWithdrawChecker ตรวจสอบการเทินโอเวอร์
// @Description TurnOverWithdrawChecker ตรวจสอบการเทินโอเวอร์
// @Tags Banking - Bank Transaction v2
// @Accept json
// @Produce json
// @Param body body model.TurnOverWithdrawChecker true "body"
// @Success 200 {object} model.CheckScammerSlipResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /test/turn-over-withdraw-checker [post]
func (h bankingController) turnOverWithdrawChecker(c *gin.Context) {

	var req model.TurnOverWithdrawChecker
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	data := h.bankingService.TurnOverWithdrawChecker(req.UserId, req.Amount)

	c.JSON(200, gin.H{"message": data})
}

// @Summary CanceledAllTurn ยกเลิกการเทินโอเวอร์
// @Description CanceledAllTurn ยกเลิกการเทินโอเวอร์
// @Tags Turnover
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.ClearAllTurnOver true "body"
// @Success 200 {object} string
// @Failure 400 {object} handler.ErrorResponse
// @Router /turnover/cancel-user [post]
func (h bankingController) canceledAllTurn(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	var req model.ClearAllTurnOver
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}
	req.AdminId = adminId

	if err := h.bankingService.CanceledAllTurn(req); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "ล้างรายการเทินโอเวอร์สำเร็จ"})
}

// @Summary (emptyUserTurnList) [********] ยกเลิกการเทินโอเวอร์ทีละหลายๆคน
// @Description (emptyUserTurnList) [********] ยกเลิกการเทินโอเวอร์ทีละหลายๆคน
// @Tags Turnover
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.EmptyUserTurnListRequest true "body"
// @Success 200 {object} string
// @Failure 400 {object} handler.ErrorResponse
// @Router /turnover/empty-user-list [post]
func (h bankingController) emptyUserTurnList(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	var req model.EmptyUserTurnListRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}
	req.AdminId = adminId

	if err := h.bankingService.EmptyUserTurnList(req); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "ล้างรายการเทินโอเวอร์สำเร็จ"})
}

// @Summary CreateDepositLaosBankRecord สร้างรายการฝากจากธนาคารลาว
// @Description CreateDepositLaosBankRecord สร้างรายการฝากจากธนาคารลาว
// @Tags Banking - Bank Transaction v2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.CreateDepositLaosBankRecordRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/banking/transactions/deposit-laos [post]
func (h bankingController) createDepositLaosBankRecord(c *gin.Context) {

	var req model.CreateDepositLaosBankRecordRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}
	req.UserId = int64(c.MustGet("userId").(float64))

	_, err := h.bankingService.CreateDepositLaosBankRecord(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(201, model.Success{Message: "Created success"})
}

// @Summary GetAccountInfoFastbank ดึงข้อมูลบัญชีจาก Fastbank
// @Description GetAccountInfoFastbank ดึงข้อมูลบัญชีจาก Fastbank
// @Tags Banking - Bank Transaction v2
// @Accept json
// @Produce json
// @Param body body model.AccountInfoFastbankRequest true "body"
// @Success 200 {object} model.AccountInfoFastbankResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/banking/transactions/fastbank-account-info [post]
func (h bankingController) getAccountInfoFastbank(c *gin.Context) {

	var req model.AccountInfoFastbankRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.bankingService.GetAccountInfoFastbank(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary ContinueConfirmWithdrawPaymentOnly ยืนยันการโอนอัตโนมัติ
// @Description ContinueConfirmWithdrawPaymentOnly ยืนยันการโอนอัตโนมัติ
// @Tags Banking - Bank Transaction v2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.CreateAutoWithdrawRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/transactions/confirm-payment-withdraw [post]
func (h bankingController) autoConfirmPayment(c *gin.Context) {

	var req model.CreateAutoWithdrawRequest
	adminId, err := h.accountingService.CheckCurrentAdminId(c.MustGet("adminId"))
	if err != nil {
		HandleError(c, err)
		return
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}
	req.ConfirmedAt = time.Now()
	req.ConfirmedByAdminId = adminId

	_, err = h.bankingService.ContinueConfirmWithdrawPaymentOnly(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(201, model.Success{Message: "Created success"})
}

// @Summary GetSmsModeDepositList ดึงข้อมูลรายการฝากจาก SMS Mode
// @Description GetSmsModeDepositList ดึงข้อมูลรายการฝากจาก SMS Mode
// @Tags Banking - Bank Transaction v2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.GetSmsModeDepositListRequest true "GetSmsModeDepositListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/transactions/smsmode-deposit-list [get]
func (h bankingController) getSmsModeDepositList(c *gin.Context) {

	var req model.GetSmsModeDepositListRequest
	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}
	data, err := h.bankingService.GetSmsModeDepositList(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary getSmsModeDepositStatusOption ดึงข้อมูล select options
// @Description getSmsModeDepositStatusOption ดึงข้อมูล select options
// @Tags Banking - Bank Transaction v2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} []model.SelectOptions
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/transactions/smsmode-status-option [get]
func (h bankingController) getSmsModeDepositStatusOption(c *gin.Context) {

	options := []model.SelectOptions{
		{
			Id:    1,
			Label: "ทั้งหมด",
			Value: "ALL",
		},
		{
			Id:    2,
			Label: "รอยืนยัน",
			Value: "PENDING",
		},
		{
			Id:    3,
			Label: "ยืนยันแล้ว",
			Value: "PAID",
		},
		{
			Id:    4,
			Label: "ไม่สำเร็จ",
			Value: "FAILED",
		},
	}

	c.JSON(200, options)
}

// AdminConfirmDepositSmsMode(req model.AdminConfirmDepositSmsModeRequest) (*int64, error)
// @Summary AdminConfirmDepositSmsMode ยืนยันการฝากจาก SMS Mode
// @Description AdminConfirmDepositSmsMode ยืนยันการฝากจาก SMS Mode
// @Tags Banking - Bank Transaction v2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.AdminConfirmDepositSmsModeRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/transactions/smsmode-confirm-deposit [post]
func (h bankingController) adminConfirmDepositSmsMode(c *gin.Context) {

	var req model.AdminConfirmDepositSmsModeRequest
	adminId, err := h.accountingService.CheckCurrentAdminId(c.MustGet("adminId"))
	if err != nil {
		HandleError(c, err)
		return
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}
	req.CreateByAdminId = adminId

	_, err = h.bankingService.AdminConfirmDepositSmsMode(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(201, model.Success{Message: "Created success"})
}

// AdminCancelDepositSmsMode(req model.AdminConfirmDepositSmsModeRequest) (*int64, error)
// @Summary AdminCancelDepositSmsMode ยกเลิกการฝากจาก SMS Mode
// @Description AdminCancelDepositSmsMode ยกเลิกการฝากจาก SMS Mode
// @Tags Banking - Bank Transaction v2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.AdminConfirmCanceledSmsModeRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /banking/transactions/smsmode-cancel-deposit [post]
func (h bankingController) adminCancelDepositSmsMode(c *gin.Context) {

	var req model.AdminConfirmCanceledSmsModeRequest
	adminId, err := h.accountingService.CheckCurrentAdminId(c.MustGet("adminId"))
	if err != nil {
		HandleError(c, err)
		return
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}
	req.CreateByAdminId = adminId

	_, err = h.bankingService.AdminCancelDepositSmsMode(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(201, model.Success{Message: "Canceled success"})
}

// @Summary BotCreateWithdrawPullCreditBack สร้างรายการถอนเครดิตกลับ
// @Description BotCreateWithdrawPullCreditBack สร้างรายการถอนเครดิตกลับ
// @Tags Banking - Bank Transaction v2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.BotCreateWithdrawPullCreditBackRequest true "body"
// @Success 201 {object} model.BotCreateWithdrawPullCreditBackResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /bot/transactions/withdraw-credit-back [post]
func (h bankingController) botCreateWithdrawPullCreditBack(c *gin.Context) {

	var req model.BotCreateWithdrawPullCreditBackRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	adminId, err := h.accountingService.CheckCurrentAdminId(c.MustGet("adminId"))
	if err != nil {
		HandleError(c, err)
		return
	}

	req.CreateByAdminId = *adminId

	data, err := h.bankingService.BotCreateWithdrawPullCreditBack(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(201, data)
}
