package handler

import (
	"cybergame-api/helper"
	"cybergame-api/middleware"
	"cybergame-api/model"
	"cybergame-api/repository"
	"cybergame-api/service"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type activityDailyController struct {
	activityDailyService service.ActivityDailyService
}

func newactivityDailyController(
	activityDailyService service.ActivityDailyService,
) activityDailyController {
	return activityDailyController{activityDailyService}
}

func ActivityDailyController(r *gin.RouterGroup, db *gorm.DB) {

	singleSession := middleware.SingleSession(db)

	activity := repository.NewActivityDailyRepository(db)
	activityDailyService := service.NewactivityDailyService(db, activity)
	handler := newactivityDailyController(activityDailyService)
	// main
	rootActivityDaily := r.Group("/activity-daily", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	rootActivityDaily.GET("/first", handler.getFirstActivityDaily)
	rootActivityDaily.PUT("/update", handler.createAndUpdateFirstActivityDaily)
	rootActivityDaily.GET("/turnover", handler.getTurnoverUserActivityDaily)
	// option
	rootOption := rootActivityDaily.Group("/option")
	rootOption.GET("/condition", handler.getActivityDailyCondition)
	rootOption.GET("/status", handler.getActivityDailyStatus)

	// web
	web := r.Group("/web", middleware.AuthorizeUser, singleSession.SingleUserSession())
	web.GET("/activity-daily", handler.getWebActivityDaily)
	web.POST("/activity-daily/collect", handler.createUserColletedActivityDaily)
	web.GET("/activity-daily/seven-day", handler.getSevenDayCollectedBouns)
	web.POST("/activity-daily/seven-day", handler.createSevenDayCollectedBouns)

}

// @Summary GetActivityDailyCondition
// @Description GetActivityDailyCondition
// @Tags Activity Daily - Option
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Success 200 {object} model.ActivityDailyConditionResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /activity-daily/option/condition [get]
func (h *activityDailyController) getActivityDailyCondition(c *gin.Context) {

	options, err := h.activityDailyService.GetActivityDailyCondition()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, options)
}

// @Summary GetActivityDailyStatus
// @Description GetActivityDailyStatus
// @Tags Activity Daily - Option
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Success 200 {object} model.ActivityDailyConditionResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /activity-daily/option/status [get]
func (h *activityDailyController) getActivityDailyStatus(c *gin.Context) {

	options, err := h.activityDailyService.GetActivityDailyStatus()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, options)
}

// @Summary GetFirstActivityDaily
// @Description GetFirstActivityDaily
// @Tags Activity Daily - Main
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Success 200 {object} model.GetActivityDailyResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /activity-daily/first [get]
func (h *activityDailyController) getFirstActivityDaily(c *gin.Context) {

	activityDaily, err := h.activityDailyService.GetFirstActivityDaily()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, activityDaily)
}

// @Summary CreateAndUpdateFirstActivityDaily
// @Description CreateAndUpdateFirstActivityDaily
// @Tags Activity Daily - Main
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param req body model.UpdateActivityDailyRequest true "body"
// @Success 200 {object} int64
// @Failure 400 {object} handler.ErrorResponse
// @Router /activity-daily/update [put]
func (h *activityDailyController) createAndUpdateFirstActivityDaily(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	var req model.UpdateActivityDailyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}
	req.UpdatedByAdminId = adminId
	id, err := h.activityDailyService.CreateAndUpdateFirstActivityDaily(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, id)
}

// GetWebActivityDaily(userId int64) (*model.GetWebActivityDailyResponse, error)
// @Summary GetWebActivityDaily
// @Description GetWebActivityDaily
// @Tags Activity Daily - Web
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Success 200 {object} model.GetWebActivityDailyResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/activity-daily [get]
func (h *activityDailyController) getWebActivityDaily(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	activityDaily, err := h.activityDailyService.GetWebActivityDaily(userId)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, activityDaily)
}

// @Summary CreateUserColletedActivityDaily
// @Description CreateUserColletedActivityDaily
// @Tags Activity Daily - Web
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/activity-daily/collect [post]
func (h *activityDailyController) createUserColletedActivityDaily(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var req model.CreateActivityDailyUserRequest

	req.UserId = userId
	_, err := h.activityDailyService.CreateUserColletedActivityDaily(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, "success")
}

// GetSevenDayCollectedBouns(userId int64) (*model.GetSevenDayCollectedBounsResponse, error)
// @Summary GetSevenDayCollectedBouns
// @Description GetSevenDayCollectedBouns
// @Tags Activity Daily - Web
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Success 200 {object} model.GetSevenDayCollectedBounsResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/activity-daily/seven-day [get]
func (h *activityDailyController) getSevenDayCollectedBouns(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	activityDaily, err := h.activityDailyService.GetSevenDayCollectedBouns(userId)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, activityDaily)
}

// CheckConditionUserColletedActivityDailyForAdmin(req model.CreateActivityDailyUserRequest) error
// @Summary CheckConditionUserColletedActivityDailyForAdmin
// @Description CheckConditionUserColletedActivityDailyForAdmin
// @Tags Activity Daily - Web
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/activity-daily/seven-day [post]
func (h *activityDailyController) createSevenDayCollectedBouns(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var req model.CreateActivityDailyUserRequest

	req.UserId = userId
	err := h.activityDailyService.CreateSevenDayCollectedBouns(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, "success")
}

// @Summary GetTurnoverUserActivityDaily
// @Description GetTurnoverUserActivityDaily
// @Tags Activity Daily
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param _ query model.GetTurnoverUserActivityDailyRequest true "query"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /activity-daily/turnover [get]
func (h *activityDailyController) getTurnoverUserActivityDaily(c *gin.Context) {

	var req model.GetTurnoverUserActivityDailyRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		HandleError(c, err)
		return
	}
	data, err := h.activityDailyService.GetTurnoverUserActivityDaily(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}
