package handler

import (
	"cybergame-api/service"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
)

type ErrorResponse struct {
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

type errorFieldAndMessage struct {
	Field   string `json:"field"`
	Message string `json:"message"`
}

type errorMessageResponse struct {
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

type validateResponse struct {
	Errors []errorFieldAndMessage `json:"errors"`
}

func getErrorMsg(fe validator.FieldError) string {
	switch fe.Tag() {
	case "required":
		return "กรุณากรอกข้อมูล" + fe.Field()
	case "lte":
		return "ความยาว " + fe.Field() + "ไม่เกิน " + fe.Param() + " ตัวอักษร"
	case "gte":
		return "ความยาว " + fe.Field() + "ไม่น้อยกว่า " + fe.Param() + " ตัวอักษร"
	case "min":
		return "ความยาว " + fe.Field() + " ต้องไม่น้อยกว่า " + fe.Param() + " ตัวอักษร"
	case "max":
		return "ความยาว " + fe.Field() + "ห้ามเกิน " + fe.Param() + " ตัวอักษร"
	case "email":
		return "รูปแบบอีเมล์ไม่ถูกต้อง"
	}
	return "ข้อมูลไม่ถูกต้อง"
}

func HandleError(c *gin.Context, err interface{}) {
	switch e := err.(type) {
	case service.ErrorResponse:
		c.AbortWithStatusJSON(e.Code, ErrorResponse{Message: e.Message, Data: e.Data})
	case validator.ValidationErrors:
		list := make([]errorFieldAndMessage, len(e))
		for i, fe := range e {
			list[i] = errorFieldAndMessage{fe.Field(), getErrorMsg(fe)}
		}
		c.AbortWithStatusJSON(http.StatusBadRequest, validateResponse{Errors: list})
	case error:
		if e.Error() == "EOF" {
			c.AbortWithStatusJSON(http.StatusBadRequest, errorMessageResponse{Message: "ข้อมูลไม่ถูกต้อง"})
		} else {
			c.AbortWithStatusJSON(http.StatusBadRequest, errorMessageResponse{Message: e.Error()})
		}
	case interface{}:
		c.AbortWithStatusJSON(http.StatusBadRequest, errorMessageResponse{Message: err.(string)})
	}
}

// MAP ERROR MESSAGE
var errorMessages = map[string]string{
	"INTERNAL_SERVER_ERROR": "เกิดข้อผิดพลาดบางอย่าง กรุณาลองใหม่อีกครั้ง",
	"RECORD_NOT_FOUND":      "ไม่พบข้อมูล",
	"INCORRECT_IDENTITY":    "ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง",
	// Handler-Controller
	"CREATE_SUCCESS": "เพิ่มข้อมูลสำเร็จ",
	"UPDATE_SUCCESS": "อัพเดทข้อมูลสำเร็จ",
	"DELETE_SUCCESS": "ลบข้อมูลสำเร็จ",
	// GENERAL REMOTE
	"INVALID_UNMARSHAL_JSON":                 "ไม่สามารถแปลงข้อมูล JSON ได้",
	"CANNOT_CREATE_ACTION":                   "กรุณาทำรายการใหม่ภายหลัง",
	"CANNOT_CREATE_ACTION_LUCKY_WHEEL_ROUND": "กรุณาทำรายการใหม่ภายหลัง",
	"USER_BANK_NOT_SUPPORTED":                "ไม่รองรับการทำรายการกับธนาคารของผู้ใช้",
	"ORDER_NOT_FOUND":                        "ไม่พบรายการ",
	// PAYMENT_GATEWAY
	"INVALID_PAYGATE_USER":                 "โปรดตรวจสอบข้อมูลบัญชีธนาคารผู้ใช้",
	"PAYGATE_EMPTY_SETTING":                "การตั้งค่าระบบเพยเม้นไม่ถูกต้อง",
	"PAYGATE_PAYMENT_NOT_ENABLED":          "ระบบชำระเงินยังไม่เปิดใช้งาน",
	"PAYGATE_DEPOSIT_NOT_ENABLED":          "ระบบฝากเงินยังไม่เปิดใช้งาน",
	"PAYGATE_WITHDRAW_NOT_ENABLED":         "ระบบถอนเงินยังไม่เปิดใช้งาน",
	"INVALID_AMOUNT_RANGE":                 "จำนวนเงินไม่ถูกต้อง",
	"PAYGATE_INVALID_EXTERNAL_CALL":        "ไม่สามารถเรียกใช้งานระบบชำระเงินภายนอกได้",
	"PAYGATE_INVALID_REMOTE_RESPONSE_DATA": "ไม่สามารถตรวจสอบข้อมูลการตอบกลับจากระบบชำระเงิน",
	"PAYGATE_INVALID_REMOTE_RESPONSE_CODE": "ไม่สามารถดำเนินการได้ รหัสตอบกลับไม่สำเร็จ",
	"PAYGATE_CANT_CHECK_BALANCE":           "ไม่สามารถตรวจสอบยอดคงเหลือได้",
	"PAYGATE_INSUFFICIENT_BALANCE":         "ยอดเงินไม่เพียงพอ",
}

func TranslateWebErrorMessage(msg string) string {
	// Translate error message
	if translatedMsg, ok := errorMessages[msg]; ok {
		msg = translatedMsg
	}
	return msg
}

func HandleWebError(c *gin.Context, err interface{}) {
	// for user/customer language.
	switch e := err.(type) {
	case service.ErrorResponse:
		c.AbortWithStatusJSON(e.Code, ErrorResponse{Message: TranslateWebErrorMessage(e.Message), Data: e.Data})
	case validator.ValidationErrors:
		list := make([]errorFieldAndMessage, len(e))
		for i, fe := range e {
			list[i] = errorFieldAndMessage{fe.Field(), getErrorMsg(fe)}
		}
		c.AbortWithStatusJSON(http.StatusBadRequest, validateResponse{Errors: list})
	case error:
		if e.Error() == "EOF" {
			c.AbortWithStatusJSON(http.StatusBadRequest, errorMessageResponse{Message: "ข้อมูลไม่ถูกต้อง"})
		} else {
			c.AbortWithStatusJSON(http.StatusBadRequest, errorMessageResponse{Message: TranslateWebErrorMessage(e.Error())})
		}
	case interface{}:
		c.AbortWithStatusJSON(http.StatusBadRequest, errorMessageResponse{Message: err.(string)})
	}
}

func HandleSingleError(c *gin.Context, err interface{}) {
	switch e := err.(type) {
	case service.ErrorResponse:
		c.AbortWithStatusJSON(e.Code, ErrorResponse{Message: e.Message, Data: e.Data})
	case validator.ValidationErrors:
		message := "Unknown error"
		for _, fe := range e {
			message = getErrorMsg(fe)
		}
		c.AbortWithStatusJSON(http.StatusBadRequest, errorMessageResponse{Message: message})
	case error:
		if e.Error() == "EOF" {
			c.AbortWithStatusJSON(http.StatusBadRequest, errorMessageResponse{Message: "ข้อมูลไม่ถูกต้อง"})
		} else {
			c.AbortWithStatusJSON(http.StatusBadRequest, errorMessageResponse{Message: e.Error()})
		}
	case interface{}:
		c.AbortWithStatusJSON(http.StatusBadRequest, errorMessageResponse{Message: err.(string)})
	}
}
