package v2

import (
	errors "cybergame-api/handler"
	"cybergame-api/helper"
	"cybergame-api/middleware"
	response "cybergame-api/model"
	model "cybergame-api/model/v2"
	repositoryV1 "cybergame-api/repository"
	repositoryV2 "cybergame-api/repository/v2"
	service "cybergame-api/service/v2"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"strconv"
)

type activityDailyAutoCheckinV2Controller struct {
	activityDailyAutoCheckinV2Service service.ActivityDailyAutoCheckinV2Service
}

func NewActivityDailyAutoCheckinV2Service(
	activityDailyAutoCheckinV2Service service.ActivityDailyAutoCheckinV2Service,
) activityDailyAutoCheckinV2Controller {
	return activityDailyAutoCheckinV2Controller{activityDailyAutoCheckinV2Service}
}

func ActivityDailyAutoCheckinV2Controller(r *gin.RouterGroup, db *gorm.DB) {
	repo := repositoryV2.NewActivityDailyAutoCheckinV2Repository(db)
	userRepository := repositoryV1.NewUserRepository(db)
	activityDailyV2Repository := repositoryV1.NewActivityDailyV2Repository(db)
	cronRepository := repositoryV1.NewCronRepository(db)
	userCreditRepository := repositoryV1.NewUserCreditRepository(db)
	turnoverRepository := repositoryV1.NewTurnoverRepository(db)
	service := service.NewActivityDailyAutoCheckinV2Service(
		db,
		repo,
		userRepository,
		activityDailyV2Repository,
		cronRepository,
		userCreditRepository,
		turnoverRepository,
	)
	handler := NewActivityDailyAutoCheckinV2Service(service)
	role := middleware.Role(db)
	singleSession := middleware.SingleSession(db)

	activityDailyCheckinV2Route := r.Group("/activity-daily-auto-checkin-v2", middleware.AuthorizeUser)
	activityDailyCheckinV2Route.POST("", handler.createUserDailyAutoCheckin)
	activityDailyCheckinV2Route.GET("/ready-claim", handler.getUserDailyAutoCheckinReadyClaimed)
	activityDailyCheckinV2Route.PUT("/claim-reward/:id", handler.getUserDailyAutoCheckinClaimReward)

	activityDailyCheckinV2ReportRoute := r.Group("/activity-daily-auto-checkin-v2/report", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	activityDailyCheckinV2ReportRoute.GET("", role.CheckPermission([]string{"activity_manage"}), handler.getReportDailyAutoCheckinV2)
}

// @Summary CreateUserDailyAutoCheckin
// @Description CreateUserDailyAutoCheckin
// @Tags Activity Daily Auto Checkin V2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} response.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /activity-daily-auto-checkin-v2 [post]
func (h *activityDailyAutoCheckinV2Controller) createUserDailyAutoCheckin(c *gin.Context) {
	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))
	if err := h.activityDailyAutoCheckinV2Service.CreateUserDailyAutoCheckinV2(userId); err != nil {
		errors.HandleError(c, err)
		return
	}

	c.JSON(200, response.Success{Message: "User daily auto check-in V2 success"})
}

// @Summary GetUserDailyAutoCheckinV2ReadyClaimed
// @Description GetUserDailyAutoCheckinV2ReadyClaimed
// @Tags Activity Daily Auto Checkin V2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} response.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /activity-daily-auto-checkin-v2/ready-claim [get]
func (h *activityDailyAutoCheckinV2Controller) getUserDailyAutoCheckinReadyClaimed(c *gin.Context) {
	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))
	result, err := h.activityDailyAutoCheckinV2Service.GetUserDailyAutoCheckinReadyClaimV2(userId)
	if err != nil {
		errors.HandleError(c, err)
		return
	}

	c.JSON(200, result)
}

// @Summary GetUserDailyAutoCheckinV2ClaimReward
// @Description GetUserDailyAutoCheckinV2ClaimReward
// @Tags Activity Daily Auto Checkin V2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "Daily Checkin ID"
// @Success 200 {object} response.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /activity-daily-auto-checkin-v2/claim-reward/{id} [put]
func (h *activityDailyAutoCheckinV2Controller) getUserDailyAutoCheckinClaimReward(c *gin.Context) {
	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))
	id := c.Param("id")
	dailyId, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		errors.HandleError(c, err)
		return
	}
	if err := h.activityDailyAutoCheckinV2Service.CreateUserDailyAutoCheckinClaimRewardV2(userId, dailyId); err != nil {
		errors.HandleError(c, err)
		return
	}

	c.JSON(200, response.Success{Message: "User daily auto checkin claim credit success"})
}

// @Summary GetReportDailyAutoCheckinV2
// @Description GetReportDailyAutoCheckinV2
// @Tags Activity Daily Auto Checkin V2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param dateType query string false "Date type" Enums(daily, monthly) Default(daily)
// @Param fromDate query string false "From date" Format(date)
// @Param toDate query string false "To date" Format(date)
// @Param memberCode query string false "Member code"
// @Param page query int false "Page" Default(1) Minimum(1)
// @Param limit query int false "Limit" Default(10) Minimum(1) Maximum(100)
// @Success 200 {object} model.GetReportDailyAutoCheckinV2Response
// @Failure 400 {object} handler.ErrorResponse
// @Router /activity-daily-auto-checkin-v2/report [get]
func (h *activityDailyAutoCheckinV2Controller) getReportDailyAutoCheckinV2(c *gin.Context) {

	var req model.GetReportDailyAutoCheckinV2Request
	if err := c.ShouldBindQuery(&req); err != nil {
		errors.HandleError(c, err)
		return
	}

	data, err := h.activityDailyAutoCheckinV2Service.GetReportDailyAutoCheckinV2(req)
	if err != nil {
		errors.HandleError(c, err)
		return
	}

	c.JSON(200, data)
}
