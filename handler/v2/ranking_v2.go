package v2

import (
	errors "cybergame-api/handler"
	"cybergame-api/helper"
	"cybergame-api/middleware"
	response "cybergame-api/model"
	model "cybergame-api/model/v2"
	repositoryV2 "cybergame-api/repository/v2"
	serviceV2 "cybergame-api/service/v2"
	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"gorm.io/gorm"
	"strconv"
)

type rankingV2Controller struct {
	rankingV2Service serviceV2.RankingV2Service
}

func NewRankingV2Controller(rankingV2Service serviceV2.RankingV2Service) *rankingV2Controller {
	return &rankingV2Controller{rankingV2Service}
}

func RankingV2Controller(r *gin.RouterGroup, db *gorm.DB) {
	s3V2Repo := repositoryV2.NewS3V2Repository(db)
	rankingV2Repo := repositoryV2.NewRankingV2Repository(db)
	rankingV2Service := serviceV2.NewRankingV2Service(rankingV2Repo, s3V2Repo)
	handler := NewRankingV2Controller(rankingV2Service)
	role := middleware.Role(db)
	singleSession := middleware.SingleSession(db)

	rankingV2Setting := r.Group("/ranking/setting", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	rankingV2Setting.POST("/upload-image", role.CheckPermission([]string{"activity_manage"}), handler.uploadImageToS3Ranking)
	rankingV2Setting.POST("", role.CheckPermission([]string{"activity_manage"}), handler.CreateRankingV2Setting)
	rankingV2Setting.GET("/list", role.CheckPermission([]string{"activity_manage"}), handler.getRankingV2Setting)
	rankingV2Setting.GET("/report/:rankingId", role.CheckPermission([]string{"activity_manage"}), handler.getRankingReportByRankingId)
	rankingV2Setting.PUT("", role.CheckPermission([]string{"activity_manage"}), handler.updateRankingV2Setting)
	rankingV2Setting.DELETE("/:id", role.CheckPermission([]string{"activity_manage"}), handler.deleteRankingV2Setting)

	rankingV2Web := r.Group("/ranking/web", middleware.AuthorizeUser)
	rankingV2Web.GET("/total", handler.getTotalRankingV2)
	rankingV2Web.GET("/user", handler.getUserRankingV2)
	rankingV2Web.GET("/turn-ranking", handler.getTurnRankingV2)
}

// uploadImageToS3Ranking godoc
// @Summary Upload Image to S3 for Ranking V2
// @Description Upload Image to S3 for Ranking V2
// @Tags Ranking V2
// @Security ApiKeyAuth
// @Accept multipart/form-data
// @Produce json
// @Param image formData file true "Image File"
// @Success 200 {object} model.ImageUploadResponse
// @Failure 400 {object} handler.ErrorResponse
// @Failure 500 {object} handler.ErrorResponse
// @Router /ranking/setting/upload-image [post]
func (h *rankingV2Controller) uploadImageToS3Ranking(c *gin.Context) {

	data, err := h.rankingV2Service.UploadImageToS3Ranking(c.Request)
	if err != nil {
		errors.HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// CreateRankingV2Setting godoc
// @Summary Create Ranking V2 Setting
// @Description Create Ranking V2 Setting
// @Tags Ranking V2
// @Security ApiKeyAuth
// @Accept json
// @Produce json
// @Param request body model.CreateRankingV2SettingRequest true "Create Ranking V2 Setting Request"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Failure 500 {object} handler.ErrorResponse
// @Router /ranking/setting [post]
func (h *rankingV2Controller) CreateRankingV2Setting(c *gin.Context) {
	var body model.CreateRankingV2SettingRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		errors.HandleError(c, err)
		return
	}

	if err := validator.New().Struct(body); err != nil {
		errors.HandleError(c, err)
		return
	}

	if err := h.rankingV2Service.CreateRankingV2Setting(body); err != nil {
		errors.HandleError(c, err)
		return
	}

	c.JSON(200, response.SuccessWithData{Message: "Create ranking setting success", Data: nil})
}

// getRankingV2Setting godoc
// @Summary Get Ranking V2 Setting
// @Description Get Ranking V2 Setting
// @Tags Ranking V2
// @Security ApiKeyAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Failure 500 {object} handler.ErrorResponse
// @Router /ranking/setting/list [get]
func (h *rankingV2Controller) getRankingV2Setting(c *gin.Context) {
	list, err := h.rankingV2Service.GetRankingV2Setting()
	if err != nil {
		errors.HandleError(c, err)
		return
	}

	c.JSON(200, response.SuccessWithData{Message: "success", Data: list})
}

// getRankingReportByRankingId godoc
// @Summary Get Ranking Report by Ranking ID
// @Description Get Ranking Report by Ranking ID
// @Tags Ranking V2
// @Security ApiKeyAuth
// @Accept json
// @Produce json
// @Param rankingId path int true "Ranking ID"
// @Param request query model.RankingListRequest true "Ranking List Request"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Failure 500 {object} handler.ErrorResponse
// @Router /ranking/setting/report/{rankingId} [get]
func (h *rankingV2Controller) getRankingReportByRankingId(c *gin.Context) {
	rankingId, err := strconv.ParseInt(c.Param("rankingId"), 10, 64)
	if err != nil {
		errors.HandleError(c, err)
		return
	}

	var req model.RankingListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		errors.HandleError(c, err)
		return
	}

	if err := validator.New().Struct(req); err != nil {
		errors.HandleError(c, err)
		return
	}

	data, err := h.rankingV2Service.GetRankingReportByRankingId(rankingId, req)
	if err != nil {
		errors.HandleError(c, err)
		return
	}

	c.JSON(200, response.SuccessWithData{Message: "Success", Data: data})
}

// updateRankingV2Setting godoc
// @Summary Update Ranking V2 Setting
// @Description Update Ranking V2 Setting
// @Tags Ranking V2
// @Security ApiKeyAuth
// @Accept json
// @Produce json
// @Param request body model.UpdateRankingV2SettingRequest true "Update Ranking V2 Setting Request"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Failure 500 {object} handler.ErrorResponse
// @Router /ranking/setting [put]
func (h *rankingV2Controller) updateRankingV2Setting(c *gin.Context) {
	var body model.UpdateRankingV2SettingRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		errors.HandleError(c, err)
		return
	}

	if err := validator.New().Struct(body); err != nil {
		errors.HandleError(c, err)
		return
	}

	if err := h.rankingV2Service.UpdateRankingV2Setting(body); err != nil {
		errors.HandleError(c, err)
		return
	}

	c.JSON(200, response.SuccessWithData{Message: "Update ranking setting success", Data: nil})
}

// deleteRankingV2Setting godoc
// @Summary Delete Ranking V2 Setting
// @Description Delete Ranking V2 Setting
// @Tags Ranking V2
// @Security ApiKeyAuth
// @Accept json
// @Produce json
// @Param id path int true "Ranking V2 Setting ID"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Failure 500 {object} handler.ErrorResponse
// @Router /ranking/setting/{id} [delete]
func (h *rankingV2Controller) deleteRankingV2Setting(c *gin.Context) {
	id := c.Param("id")
	toInt64, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		errors.HandleError(c, err)
		return
	}

	if err := h.rankingV2Service.DeleteRankingV2Setting(toInt64); err != nil {
		errors.HandleError(c, err)
		return
	}

	c.JSON(200, response.SuccessWithData{Message: "Delete ranking setting success", Data: nil})
}

// getTotalRankingV2 godoc
// @Summary Get Total Ranking V2
// @Description Get Total Ranking V2
// @Tags Ranking V2
// @Security ApiKeyAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Failure 500 {object} handler.ErrorResponse
// @Router /ranking/web/total [get]
func (h *rankingV2Controller) getTotalRankingV2(c *gin.Context) {
	list, err := h.rankingV2Service.GetTotalRankingV2()
	if err != nil {
		errors.HandleError(c, err)
		return
	}

	c.JSON(200, response.SuccessWithData{Message: "success", Data: list})
}

// getUserRankingV2 godoc
// @Summary Get User Ranking V2
// @Description Get User Ranking V2
// @Tags Ranking V2
// @Security ApiKeyAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Failure 500 {object} handler.ErrorResponse
// @Router /ranking/web/user [get]
func (h *rankingV2Controller) getUserRankingV2(c *gin.Context) {
	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))
	userRanking, err := h.rankingV2Service.GetUserRankingV2(userId)
	if err != nil {
		errors.HandleError(c, err)
		return
	}

	c.JSON(200, response.SuccessWithData{Message: "success", Data: userRanking})
}

// getTurnRankingV2 godoc
// @Summary Get Turn Ranking V2
// @Description Get Turn Ranking V2
// @Tags Ranking V2
// @Security ApiKeyAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Failure 500 {object} handler.ErrorResponse
// @Router /ranking/web/turn-ranking [get]
func (h *rankingV2Controller) getTurnRankingV2(c *gin.Context) {
	list, err := h.rankingV2Service.GetTurnRankingV2()
	if err != nil {
		errors.HandleError(c, err)
		return
	}

	c.JSON(200, response.SuccessWithData{Message: "success", Data: list})
}
