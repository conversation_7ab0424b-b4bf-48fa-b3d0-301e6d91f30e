package v2

import (
	errors "cybergame-api/handler"
	"cybergame-api/middleware"
	response "cybergame-api/model"
	model "cybergame-api/model/v2"
	repository "cybergame-api/repository/v2"
	service "cybergame-api/service/v2"
	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"gorm.io/gorm"
	"strconv"
)

type questController struct {
	questService service.QuestService
}

func NewQuestController(questService service.QuestService) questController {
	return questController{questService}
}

func QuestController(r *gin.RouterGroup, db *gorm.DB) {
	s3Repo := repository.NewS3V2Repository(db)
	repo := repository.NewQuestRepository(db)
	service := service.NewQuestService(repo, s3Repo)
	handler := NewQuestController(service)
	role := middleware.Role(db)
	singleSession := middleware.SingleSession(db)

	questRoute := r.Group("/quest", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())

	questRoute.GET("/quest-type", role.CheckPermission([]string{"activity_manage"}), handler.getQuestType)
	questRoute.POST("/upload-image", role.CheckPermission([]string{"activity_manage"}), handler.uploadImageToS3Quest)

	questRoute.POST("", role.CheckPermission([]string{"activity_manage"}), handler.createQuest)
	questRoute.GET("/list", role.CheckPermission([]string{"activity_manage"}), handler.getQuestList)
	questRoute.PUT("", role.CheckPermission([]string{"activity_manage"}), handler.updateQuest)
	questRoute.DELETE("/:id", role.CheckPermission([]string{"activity_manage"}), handler.deleteQuest)
}

// @Summary (createQuest) สร้างเควส
// @Description (createQuest) สร้างเควส
// @Tags Quest - Quest V2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.CreateQuest true "Create Quest"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /quest [post]
func (h *questController) createQuest(c *gin.Context) {
	var body model.CreateQuest
	if err := c.ShouldBindJSON(&body); err != nil {
		errors.HandleError(c, err)
		return
	}

	if err := validator.New().Struct(body); err != nil {
		errors.HandleError(c, err)
		return
	}

	if err := h.questService.CreateQuest(body); err != nil {
		errors.HandleError(c, err)
		return
	}
	c.JSON(200, response.Success{Message: "Create quest success"})
}

// @Summary (uploadImageToS3Quest) อัพโหลดรูปภาพเควสไปยัง S3
// @Description (uploadImageToS3Quest) อัพโหลดรูปภาพเควสไปยัง S3
// @Tags Quest - Quest V2
// @Security BearerAuth
// @Accept multipart/form-data
// @Produce json
// @Param file formData file true "Image File"
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /quest/upload-image [post]
func (h *questController) uploadImageToS3Quest(c *gin.Context) {

	data, err := h.questService.UploadImageToS3Quest(c.Request)
	if err != nil {
		errors.HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (getQuestList) ดึงข้อมูลเควส
// @Description (getQuestList) ดึงข้อมูลเควส
// @Tags Quest - Quest V2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param page query int false "Page"
// @Param limit query int false "Limit"
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /quest/list [get]
func (h *questController) getQuestList(c *gin.Context) {
	var query model.QuestListRequest
	if err := c.ShouldBind(&query); err != nil {
		errors.HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		errors.HandleError(c, err)
		return
	}
	quests, err := h.questService.GetQuest(query)
	if err != nil {
		errors.HandleError(c, err)
		return
	}
	c.JSON(200, response.SuccessWithData{Message: "Success", Data: quests})
}

// @Summary (updateQuest) อัพเดทเควส
// @Description (updateQuest) อัพเดทเควส
// @Tags Quest - Quest V2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.UpdateQuest true "Update Quest"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /quest [PUT]
func (h *questController) updateQuest(c *gin.Context) {
	var body model.UpdateQuest
	if err := c.ShouldBindJSON(&body); err != nil {
		errors.HandleError(c, err)
		return
	}

	if err := validator.New().Struct(body); err != nil {
		errors.HandleError(c, err)
		return
	}

	if err := h.questService.UpdateQuest(body); err != nil {
		errors.HandleError(c, err)
		return
	}
	c.JSON(200, response.Success{Message: "Update quest success"})
}

// @Summary (deleteQuest) ลบเควส
// @Description (deleteQuest) ลบเควส
// @Tags Quest - Quest V2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "Quest ID"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /quest/{id} [delete]
func (h *questController) deleteQuest(c *gin.Context) {
	id := c.Param("id")
	toInt64, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		errors.HandleError(c, err)
		return
	}

	if err := h.questService.DeleteQuest(toInt64); err != nil {
		errors.HandleError(c, err)
		return
	}

	c.JSON(200, response.Success{Message: "Delete quest success"})
}

// @Summary (getQuestType) ดึงข้อมูลประเภทเควส
// @Description (getQuestType) ดึงข้อมูลประเภทเควส
// @Tags Quest - QuestType V2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /quest/quest-type [get]
func (h *questController) getQuestType(c *gin.Context) {
	questTypes, err := h.questService.GetQuestType()
	if err != nil {
		errors.HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithData{Message: "Success", Data: questTypes})
}
