package v2

import (
	"cybergame-api/middleware"
	response "cybergame-api/model"
	repositoryV1 "cybergame-api/repository"
	repository "cybergame-api/repository/v2"
	service "cybergame-api/service/v2"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type userQuestPlayLogController struct {
	userQuestPlayLogV2Service service.UserQuestPlayLogV2Service
}

func NewUserQuestPlayLogController(userQuestPlayLogV2Service service.UserQuestPlayLogV2Service) userQuestPlayLogController {
	return userQuestPlayLogController{userQuestPlayLogV2Service}
}

func UserQuestPlayLogController(r *gin.RouterGroup, db *gorm.DB) {
	userQuestPlayLogRepo := repository.NewUserQuestPlayLogV2Repository(db)
	agentConnectV2Repo := repository.NewAgentConnectV2Repository(db)
	dailyQuestRepo := repository.NewDailyQuestRepository(db)
	agentInfoRepository := repositoryV1.NewAgentInfoRepository(db)
	userQuestPlayLogService := service.NewUserQuestPlayLogV2Service(agentInfoRepository, userQuestPlayLogRepo, agentConnectV2Repo, dailyQuestRepo)
	handler := NewUserQuestPlayLogController(userQuestPlayLogService)

	userQuestPlayLogGroup := r.Group("/quest-play-log", middleware.AuthorizeUser)
	userQuestPlayLogGroup.GET("/list", handler.getUserQuestPlayLogByDate)
}

// getUserQuestPlayLogByDate godoc
// @Summary Get User Quest Play Log By Date
// @Description Get User Quest Play Log By Date
// @Tags User Quest Play Log
// @Security ApiKeyAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Failure 500 {object} handler.ErrorResponse
// @Router /quest-play-log/list [get]
func (h *userQuestPlayLogController) getUserQuestPlayLogByDate(c *gin.Context) {
	//userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))
	//err := h.userQuestPlayLogV2Service.GetPlayLogByDate(userId)
	//if err != nil {
	//	errors.HandleError(c, err)
	//	return
	//}

	c.JSON(200, response.Success{Message: "success"})
}
