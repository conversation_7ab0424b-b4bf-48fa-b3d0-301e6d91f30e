package v2

import (
	errors "cybergame-api/handler"
	"cybergame-api/helper"
	"cybergame-api/middleware"
	response "cybergame-api/model"
	model "cybergame-api/model/v2"
	repositoryV1 "cybergame-api/repository"
	repository "cybergame-api/repository/v2"
	service "cybergame-api/service/v2"
	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"gorm.io/gorm"
	"strconv"
)

type dailyQuestController struct {
	dailyQuestService service.DailyQuestService
}

func NewDailyQuestController(dailyQuestService service.DailyQuestService) dailyQuestController {
	return dailyQuestController{dailyQuestService}
}

func DailyQuestController(r *gin.RouterGroup, db *gorm.DB) {
	repo := repository.NewDailyQuestRepository(db)
	cronRepository := repositoryV1.NewCronRepository(db)
	userQuestPlayLogRepo := repository.NewUserQuestPlayLogV2Repository(db)
	userCreditRepository := repositoryV1.NewUserCreditRepository(db)
	turnoverRepository := repositoryV1.NewTurnoverRepository(db)
	agentInfoRepository := repositoryV1.NewAgentInfoRepository(db)
	service := service.NewDailyQuestService(
		db,
		repo,
		cronRepository,
		userQuestPlayLogRepo,
		userCreditRepository,
		turnoverRepository,
		agentInfoRepository,
	)
	handler := NewDailyQuestController(service)
	role := middleware.Role(db)
	singleSession := middleware.SingleSession(db)

	// Admin
	dailyQuestRoute := r.Group("/quest-daily-quest", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	dailyQuestRoute.GET("/condition", role.CheckPermission([]string{"activity_manage"}), handler.getDailyQuestCondition)
	dailyQuestRoute.POST("", role.CheckPermission([]string{"activity_manage"}), handler.createDailyQuest)
	dailyQuestRoute.GET("/list", role.CheckPermission([]string{"activity_manage"}), handler.getDailyQuests)
	dailyQuestRoute.GET("/:id", role.CheckPermission([]string{"activity_manage"}), handler.getDailyQuestById)
	dailyQuestRoute.PUT("", role.CheckPermission([]string{"activity_manage"}), handler.updateDailyQuest)
	dailyQuestRoute.PUT("/active", role.CheckPermission([]string{"activity_manage"}), handler.updateDailyQuestActive)
	dailyQuestRoute.DELETE("/:id", role.CheckPermission([]string{"activity_manage"}), handler.deleteDailyQuest)

	// Web
	dailyQuestWebRoute := r.Group("/quest-daily-quest/web", middleware.AuthorizeUser)
	dailyQuestWebRoute.GET("/daily", handler.getWebDailyQuests)
	dailyQuestWebRoute.GET("/list", handler.getWebUserDailyQuestList)
	dailyQuestWebRoute.POST("/create-user-quest", handler.createUserDailyQuest)
	dailyQuestWebRoute.PUT("/claim-reward", handler.claimUserDailyQuestReward)
}

// @Summary (getDailyQuestCondition) Daily Quest Condition
// @Description (getDailyQuestCondition) Daily Quest Condition
// @Tags Quest - DailyQuest V2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /quest-daily-quest/condition [get]
func (h *dailyQuestController) getDailyQuestCondition(c *gin.Context) {
	dailyQuestCondition, err := h.dailyQuestService.GetDailyQuestCondition()
	if err != nil {
		errors.HandleError(c, err)
		return
	}
	c.JSON(200, response.SuccessWithData{Message: "Success", Data: dailyQuestCondition})
}

// @Summary (createDailyQuest) สร้าง Daily Quest
// @Description (createDailyQuest) สร้าง Daily Quest
// @Tags Quest - DailyQuest V2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.CreateDailyQuestRequest true "Create Daily Quest"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /quest-daily-quest [post]
func (h *dailyQuestController) createDailyQuest(c *gin.Context) {
	var body model.CreateDailyQuestRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		errors.HandleError(c, err)
		return
	}

	if err := validator.New().Struct(body); err != nil {
		errors.HandleError(c, err)
		return
	}

	if err := h.dailyQuestService.CreateDailyQuest(body); err != nil {
		errors.HandleError(c, err)
		return
	}
	c.JSON(200, response.Success{Message: "Create Daily Quest success"})
}

// @Summary (getDailyQuests) Daily Quest List
// @Description (getDailyQuests) Daily Quest List
// @Tags Quest - DailyQuest V2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param page query int false "Page"
// @Param limit query int false "Limit"
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /quest-daily-quest/daily [get]
func (h *dailyQuestController) getDailyQuests(c *gin.Context) {
	var query model.DailyQuestListRequest
	if err := c.ShouldBindQuery(&query); err != nil {
		errors.HandleError(c, err)
		return
	}

	if err := validator.New().Struct(query); err != nil {
		errors.HandleError(c, err)
		return
	}

	dailyQuest, err := h.dailyQuestService.GetDailyQuests(query)
	if err != nil {
		errors.HandleError(c, err)
		return
	}
	c.JSON(200, response.SuccessWithData{Message: "Success", Data: dailyQuest})
}

// @Summary (getDailyQuestById) Daily Quest By Id
// @Description (getDailyQuestById) Daily Quest By Id
// @Tags Quest - DailyQuest V2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "Daily Quest ID"
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /quest-daily-quest/{id} [get]
func (h *dailyQuestController) getDailyQuestById(c *gin.Context) {
	id := c.Param("id")
	toInt64, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		errors.HandleError(c, err)
		return
	}

	dailyQuest, err := h.dailyQuestService.GetDailyQuestById(toInt64)
	if err != nil {
		errors.HandleError(c, err)
		return
	}

	c.JSON(200, response.SuccessWithData{Message: "Success", Data: dailyQuest})
}

// @Summary (updateDailyQuest) อัพเดท Daily Quest
// @Description (updateDailyQuest) อัพเดท Daily Quest
// @Tags Quest - DailyQuest V2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.UpdateDailyQuestRequest true "Update Daily Quest"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /quest-daily-quest [put]
func (h *dailyQuestController) updateDailyQuest(c *gin.Context) {
	var body model.UpdateDailyQuestRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		errors.HandleError(c, err)
		return
	}

	if err := validator.New().Struct(body); err != nil {
		errors.HandleError(c, err)
		return
	}

	if err := h.dailyQuestService.UpdateDailyQuest(body); err != nil {
		errors.HandleError(c, err)
		return
	}
	c.JSON(200, response.Success{Message: "Update Daily Quest success"})

}

// @Summary (updateDailyQuestActive) อัพเดทสถานะ Active ของ Daily Quest
// @Description (updateDailyQuestActive) อัพเดทสถานะ Active ของ Daily Quest
// @Tags Quest - DailyQuest V2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.UpdateDailyQuestActiveRequest true "Update Daily Quest Active Status"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /quest-daily-quest/active [put]
func (h *dailyQuestController) updateDailyQuestActive(c *gin.Context) {
	var body model.UpdateDailyQuestActiveRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		errors.HandleError(c, err)
		return
	}

	if err := validator.New().Struct(body); err != nil {
		errors.HandleError(c, err)
		return
	}

	if err := h.dailyQuestService.UpdateDailyQuestActive(body); err != nil {
		errors.HandleError(c, err)
		return
	}

	c.JSON(200, response.Success{Message: "Update Daily Quest active status success"})
}

// @Summary (deleteDailyQuest) ลบ Daily Quest
// @Description (deleteDailyQuest) ลบ Daily Quest
// @Tags Quest - DailyQuest V2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "Daily Quest ID"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /quest-daily-quest/{id} [delete]
func (h *dailyQuestController) deleteDailyQuest(c *gin.Context) {
	id := c.Param("id")
	toInt64, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		errors.HandleError(c, err)
		return
	}

	if err := h.dailyQuestService.DeleteDailyQuest(toInt64); err != nil {
		errors.HandleError(c, err)
		return
	}

	c.JSON(200, response.Success{Message: "Delete Daily Quest success"})
}

// @Summary (getWebDailyQuests) Daily Quest List
// @Description (getWebDailyQuests) Daily Quest List
// @Tags Quest - Web DailyQuest V2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /quest-daily-quest/web/list [get]
func (h *dailyQuestController) getWebDailyQuests(c *gin.Context) {
	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))
	dailyQuest, err := h.dailyQuestService.WebGetDailyQuests(userId)
	if err != nil {
		errors.HandleError(c, err)
		return
	}

	c.JSON(200, response.SuccessWithData{Message: "Success", Data: dailyQuest})
}

// @Summary (getWebUserDailyQuestList) Daily Quest Ready Claim
// @Description (getWebUserDailyQuestList) Daily Quest Ready Claim
// @Tags Quest - Web DailyQuest V2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param page query int false "Page"
// @Param limit query int false "Limit"
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /quest-daily-quest/web/list [get]
func (h *dailyQuestController) getWebUserDailyQuestList(c *gin.Context) {
	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))
	var query model.DailyQuestListRequest
	if err := c.ShouldBindQuery(&query); err != nil {
		errors.HandleError(c, err)
		return
	}

	if err := validator.New().Struct(query); err != nil {
		errors.HandleError(c, err)
		return
	}

	list, err := h.dailyQuestService.WebGetUserDailyQuestList(userId, query)
	if err != nil {
		errors.HandleError(c, err)
		return
	}

	c.JSON(200, response.SuccessWithData{Message: "Success", Data: list})
}

// @Summary (createUserDailyQuest) Create User Daily Quest
// @Description (createUserDailyQuest) Create User Daily Quest
// @Tags Quest - Web DailyQuest V2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param dailyQuestId query int true "Daily Quest ID"
// @Param questId query int true "Quest ID"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /quest-daily-quest/web/create-user-quest [post]
func (h *dailyQuestController) createUserDailyQuest(c *gin.Context) {
	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var req model.CreateUserDailyQuestRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		errors.HandleError(c, err)
		return
	}

	if err := validator.New().Struct(req); err != nil {
		errors.HandleError(c, err)
		return
	}

	if err := h.dailyQuestService.CreateUserDailyQuest(userId, &req); err != nil {
		errors.HandleError(c, err)
		return
	}

	c.JSON(200, response.Success{Message: "Create User Daily Quest success"})

}

// @Summary (claimUserDailyQuestReward) Claim User Daily Quest Reward
// @Description (claimUserDailyQuestReward) Claim User Daily Quest Reward
// @Tags Quest - Web DailyQuest V2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.ClaimUserDailyQuestReward true "Claim User Daily Quest Reward"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /quest-daily-quest/web/claim-reward [put]
func (h *dailyQuestController) claimUserDailyQuestReward(c *gin.Context) {
	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))
	var body model.ClaimUserDailyQuestReward
	if err := c.ShouldBindJSON(&body); err != nil {
		errors.HandleError(c, err)
		return
	}

	if err := validator.New().Struct(body); err != nil {
		errors.HandleError(c, err)
		return
	}

	if err := h.dailyQuestService.ClaimUserDailyQuestReward(userId, body); err != nil {
		errors.HandleError(c, err)
		return
	}

	c.JSON(200, response.Success{Message: "Claim User Daily Quest Reward success"})
}
