package handler

import (
	"cybergame-api/middleware"
	"cybergame-api/model"
	"cybergame-api/repository"
	"cybergame-api/service"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type notificationController struct {
	notificationService service.NotificationService
}

func newNotificationController(notificationService service.NotificationService,
) notificationController {
	return notificationController{notificationService}
}

func NotificationController(r *gin.RouterGroup, db *gorm.DB) {
	notificationRepo := repository.NewNotificationRepository(db)
	notificationService := service.NewNotificationService(notificationRepo)
	handler := newNotificationController(notificationService)
	// notification options
	notificationRoute := r.Group("/notification/options")
	notificationRoute.GET("", handler.getNotificationType)

	// notification configuration
	notificationConfigRoute := r.Group("/configuration/notification")
	notificationConfigRoute.GET("", handler.getConfigurationNotification)
	notificationConfigRoute.PUT("", handler.updateConfigurationNotification)

	// line notification
	lineNotificationRoute := r.Group("/line/notification")
	lineNotificationRoute.POST("", handler.lineNotification)

	// backoffice notification
	backofficeNotificationRoute := r.Group("/configuration/backoffice", middleware.AuthorizeAdmin)
	backofficeNotificationRoute.POST("/notification", handler.createConfigurationBackofficeNotification)
	backofficeNotificationRoute.GET("/notification", handler.getConfigurationBackofficeNotification)

	// telegram notification
	teleNotificationRoute := r.Group("/configuration/telegram", middleware.AuthorizeAdmin)
	teleNotificationRoute.GET("/access-token", handler.getTelegramAccessToken)
	teleNotificationRoute.POST("/access-token", handler.setTelegramAccessToken)

	// notification token
	notificationTokenRoute := r.Group("/configuration/notification/token", middleware.AuthorizeAdmin)
	notificationTokenRoute.POST("", handler.createConfigurationNotificationToken)
	notificationTokenRoute.PUT("", handler.updateConfigurationNotificationToken)
	notificationTokenRoute.DELETE("/:id", handler.deleteConfigurationNotificationToken)
	notificationTokenRoute.GET("/:id", handler.getConfigurationNotificationTokenById)
	notificationTokenRoute.GET("", handler.getConfigurationNotificationTokenList)

	// external notification
	externalNotificationRoute := r.Group("/configuration/external/notification", middleware.AuthorizeAdmin)
	externalNotificationRoute.POST("", handler.createConfigurationExternalNotification)
	externalNotificationRoute.DELETE("/:id", handler.deleteConfigurationExternalNotification)
	externalNotificationRoute.GET("/list", handler.getConfigurationExternalNotificationList)
	externalNotificationRoute.GET("/:id", handler.getConfigurationExternalNotificationById)
	externalNotificationRoute.PUT("/:id", handler.updateConfigurationExternalNotification)

	backup := r.Group("/configuration/backup")
	backup.GET("/notification-line", handler.autoCreateBackUpTokenInUse)
	backup.GET("/change-oldtoken-type", handler.updateOldTokenToLine)

	// // test telegram message
	newtestpath := r.Group("/configuration/external/notification")
	newtestpath.GET("/test", handler.TestTelegramMessage)

	clearCache := r.Group("/configuration")
	clearCache.GET("/clear-notification-cache", handler.clearNotificationCache)

	notiSummary := r.Group("/noti-summary")
	notiSummary.GET("/user-new-member", handler.getUserNewMemberForNoti)

	// /noti-summary/test/bank-transaction-summary-report
	// /cronjob/noti-summary/bank-transaction-summary-report [get]
	cronjobNoti := r.Group("/cronjob")

	cronjobNoti.GET("/noti-summary/daily-transaction-summary-report", handler.GetTransactionDailySummaryReportNotification)
	cronjobNoti.GET("/noti-summary/hour-transaction-summary-report", handler.GetTransactionHourSummaryReportNotification)
	cronjobNoti.GET("/noti-summary/affiliate-summary-report", handler.getAffiliateSummaryReportNotification)

}

// @Summary Get NotificationType
// @Description Get NotificationType
// @Tags Notification - Options
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Success 200 {object} model.ConfigurationNotificationType
// @Failure 400 {object} handler.ErrorResponse
// @Router /notification/options [get]
func (h *notificationController) getNotificationType(c *gin.Context) {

	options, err := h.notificationService.GetNotificationType()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, options)
}

// @Summary Get ConfigurationNotification
// @Description Get ConfigurationNotification
// @Tags Notification - configuration
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Success 200 {object} model.ConfigurationNotification
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/notification [get]
func (h *notificationController) getConfigurationNotification(c *gin.Context) {

	configurationNotification, err := h.notificationService.GetConfigurationNotification()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, configurationNotification)
}

// @Summary Update ConfigurationNotification
// @Description Update ConfigurationNotification
// @Tags Notification - configuration
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param body body model.UpdateConfigurationNotificationRequest true "body"
// @Success 200 {object} model.UpdateConfigurationNotificationRequest
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/notification [put]
func (h *notificationController) updateConfigurationNotification(c *gin.Context) {

	var req model.UpdateConfigurationNotificationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	err := h.notificationService.UpdateConfigurationNotification(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, "success")
}

// @Summary Send Line Notification
// @Description Send Line Notification
// @Tags Notification - line
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /line/notification [post]
func (h *notificationController) lineNotification(c *gin.Context) {
	// เวลาส่่งให้ระบุ type ด้วย เดียว ข้อความจะดึงข้อมูลมาแจ้งเองครับ
	// IsMemberRegistration         = "IS_MEMBER_REGISTRATION"
	// IsDepositBeforeCredit        = "IS_DEPOSIT_BEFORE_CREDIT"
	// IsDepositAfterCredit         = "IS_DEPOSIT_AFTER_CREDIT"
	// IsWithdrawalCreditSuccess     = "IS_WITHDRAWAL_CREDIT_SUCCESS"
	// IsWithdrawalAwaitingTransfer = "IS_WITHDRAWAL_AWAITING_TRANSFER"
	// IsWithdrawalCreditFailed      = "IS_WITHDRAWAL_CREDIT_FAILED"

	//จะแจ้งเป็น
	// IsMemberRegistrationMessage         = "แจ้ง สมัครสมาชิก"
	// IsDepositBeforeCreditMessage        = "แจ้งฝาก ก่อนปรับเครดิต"
	// IsDepositAfterCreditMessage         = "แจ้งฝาก หลังปรับเครดิต"
	// IsWithdrawalCreditSuccessMessage     = "แจ้งถอน ก่อนปรับเครดิต"
	// IsWithdrawalAwaitingTransferMessage = "แจ้งถอน รอโอนเงิน"
	// IsWithdrawalCreditFailedMessage      = "แจ้งถอน หลังปรับเครดิต"

	// ตัวอย่าง
	var req model.NotifyExternalNotificationRequest
	req.TypeNotify = model.IsDepositAfterCredit
	// req.UserId = 1132456

	err := h.notificationService.ExternalNotification(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, "success")
}

// @Summary Create ConfigurationBackofficeNotification
// @Description Create ConfigurationBackofficeNotification
// @Tags V2 Notification - configuration
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param body body model.CreateConfigurationBackofficeNotificationBody true "body"
// @Success 200 {object} model.CreateConfigurationBackofficeNotificationBody
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/backoffice/notification [post]
func (h *notificationController) createConfigurationBackofficeNotification(c *gin.Context) {

	var req model.CreateConfigurationBackofficeNotificationBody
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	_, err := h.notificationService.CreateConfigurationBackofficeNotification(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, "success")
}

// @Summary Get ConfigurationBackofficeNotification
// @Description Get ConfigurationBackofficeNotification
// @Tags V2 Notification - configuration
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Success 200 {object} model.GetConfigurationBackofficeNotificationResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/backoffice/notification [get]
func (h *notificationController) getConfigurationBackofficeNotification(c *gin.Context) {

	configurationNotification, err := h.notificationService.GetConfigurationBackofficeNotification()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, configurationNotification)
}

// @Summary getTelegramAccessToken
// @Description getTelegramAccessToken
// @Tags V2 Notification - Telegram
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.TelegramAccessTokenResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/telegram/access-token [get]
func (h *notificationController) getTelegramAccessToken(c *gin.Context) {

	token, err := h.notificationService.GetTelegramAccessToken()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, token)
}

// @Summary setTelegramAccessToken
// @Description setTelegramAccessToken
// @Tags V2 Notification - Telegram
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.TelegramAccessTokenUpdateRequest true "body"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/telegram/access-token [post]
func (h *notificationController) setTelegramAccessToken(c *gin.Context) {

	var req model.TelegramAccessTokenUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	err := h.notificationService.SetTelegramAccessToken(req)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, "success")
}

// @Summary Create ConfigurationNotificationToken
// @Description Create ConfigurationNotificationToken
// @Tags V2 Notification - configuration
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param body body []model.CreateConfigurationNotificationTokenBody true "body"
// @Success 200 {object} model.CreateConfigurationNotificationTokenBody
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/notification/token [post]
func (h *notificationController) createConfigurationNotificationToken(c *gin.Context) {

	var req []model.CreateConfigurationNotificationTokenBody
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	err := h.notificationService.CreateConfigurationNotificationToken(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, "success")
}

// @Summary Update ConfigurationNotificationToken
// @Description Update ConfigurationNotificationToken
// @Tags V2 Notification - configuration
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param body body model.UpdateConfigurationNotificationTokenBody true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/notification/token [put]
func (h *notificationController) updateConfigurationNotificationToken(c *gin.Context) {

	var req model.UpdateConfigurationNotificationTokenBody
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	err := h.notificationService.UpdateConfigurationNotificationToken(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, "success")
}

// @Summary Delete ConfigurationNotificationToken
// @Description Delete ConfigurationNotificationToken
// @Tags V2 Notification - configuration
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param id path int64 true "id"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/notification/token/{id} [delete]
func (h *notificationController) deleteConfigurationNotificationToken(c *gin.Context) {

	var req model.ConfigurationNotificationTokenGetById
	if err := c.ShouldBindUri(&req); err != nil {
		HandleError(c, err)
		return
	}
	err := h.notificationService.DeleteConfigurationNotificationToken(req.Id)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, "success")
}

// @Summary Get ConfigurationNotificationTokenById
// @Description Get ConfigurationNotificationTokenById
// @Tags V2 Notification - configuration
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param id path int true "id"
// @Success 200 {object} model.GetConfigurationNotificationTokenResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/notification/token/{id} [get]
func (h *notificationController) getConfigurationNotificationTokenById(c *gin.Context) {

	var req model.ConfigurationNotificationTokenGetById
	if err := c.ShouldBindUri(&req); err != nil {
		HandleError(c, err)
		return
	}
	configurationNotification, err := h.notificationService.GetConfigurationNotificationTokenById(req.Id)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, configurationNotification)
}

// @Summary Get ConfigurationNotificationTokenList
// @Description Get ConfigurationNotificationTokenList
// @Tags V2 Notification - configuration
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param _ query model.GetConfigurationNotificationTokenList true "GetConfigurationNotificationTokenList"
// @Success 200 {object} []model.GetConfigurationNotificationTokenResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/notification/token [get]
func (h *notificationController) getConfigurationNotificationTokenList(c *gin.Context) {

	var req model.GetConfigurationNotificationTokenList
	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}

	configurationNotification, err := h.notificationService.GetConfigurationNotificationTokenList(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, configurationNotification)
}

// @Summary Create ConfigurationExternalNotification
// @Description Create ConfigurationExternalNotification
// @Tags V2 Notification - configuration
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param body body model.CreateConfigurationExternalNotificationRequest true "body"
// @Success 200 {object} model.CreateConfigurationExternalNotificationRequest
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/external/notification [post]
func (h *notificationController) createConfigurationExternalNotification(c *gin.Context) {

	var req model.CreateConfigurationExternalNotificationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	_, err := h.notificationService.CreateConfigurationExternalNotificationWithToken(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, "success")
}

// DeleteConfigurationExternalNotification
// @Summary Delete ConfigurationExternalNotification
// @Description Delete ConfigurationExternalNotification
// @Tags V2 Notification - configuration
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param id path int true "id"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/external/notification/{id} [delete]
func (h *notificationController) deleteConfigurationExternalNotification(c *gin.Context) {

	var req model.ConfigurationNotificationTokenGetById
	if err := c.ShouldBindUri(&req); err != nil {
		HandleError(c, err)
		return
	}
	err := h.notificationService.DeleteConfigurationExternalNotification(req.Id)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, "success")
}

// GetConfigurationExternalNotificationList(req model.GetConfigurationExternalNotificationListRequest) (*model.SuccessWithPagination, error)
// @Summary Get ConfigurationExternalNotificationList
// @Description Get ConfigurationExternalNotificationList
// @Tags V2 Notification - configuration
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param _ query model.GetConfigurationExternalNotificationListRequest true "GetConfigurationExternalNotificationListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/external/notification/list [get]
func (h *notificationController) getConfigurationExternalNotificationList(c *gin.Context) {

	var req model.GetConfigurationExternalNotificationListRequest
	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}

	configurationNotification, err := h.notificationService.GetConfigurationExternalNotificationList(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, configurationNotification)
}

// @Summary Get ConfigurationExternalNotificationById
// @Description Get ConfigurationExternalNotificationById
// @Tags V2 Notification - configuration
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param id path int true "id"
// @Success 200 {object} model.GetConfigurationExternalNotificationByIdResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/external/notification/{id} [get]
func (h *notificationController) getConfigurationExternalNotificationById(c *gin.Context) {

	var req model.ConfigurationNotificationTokenGetById
	if err := c.ShouldBindUri(&req); err != nil {
		HandleError(c, err)
		return
	}
	configurationNotification, err := h.notificationService.GetConfigurationExternalNotificationById(req.Id)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, configurationNotification)
}

// @Summary Update ConfigurationExternalNotification
// @Description Update ConfigurationExternalNotification
// @Tags V2 Notification - configuration
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param id path int true "id"
// @Param body body model.UpdateConfigurationExternalNotificationRequest true "body"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/external/notification/{id} [put]
func (h *notificationController) updateConfigurationExternalNotification(c *gin.Context) {

	var req model.UpdateConfigurationExternalNotificationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	var param model.UpdateConfigurationExternalNotificationByIdRequest
	if err := c.ShouldBindUri(&param); err != nil {
		HandleError(c, err)
		return
	}

	req.Id = param.Id
	err := h.notificationService.UpdateConfigurationExternalNotification(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, "success")
}

// @Summary (TestTelegramMessage) Test Telegram Message
// @Description (TestTelegramMessage) Test Telegram Message
// @Description type is LINE, TELEGRAM
// @Tags V2 Notification - configuration
// @Accept  json
// @Produce  json
// @Param token query string true "token"
// @Param typenoti query string true "typenoti"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/external/notification/test [get]
func (h *notificationController) TestTelegramMessage(c *gin.Context) {

	token := c.Query("token")
	typeNoti := c.Query("typenoti")
	// TestNotiMessage(token string, typeNoti string) error
	err := h.notificationService.TestNotiMessage(token, typeNoti)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, "success")
}

// AutoCreateBackUpTokenInUse() error
// @Summary Auto Create BackUp Token In Use
// @Description Auto Create BackUp Token In Use
// @Tags V2 Notification - configuration
// @Accept  json
// @Produce  json
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/backup/notification-line [get]
func (h *notificationController) autoCreateBackUpTokenInUse(c *gin.Context) {

	err := h.notificationService.AutoCreateBackUpTokenInUse()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, "success")
}

// @Summary Auto Create BackUp Token change token type
// @Description Auto Create BackUp Token change token type
// @Tags V2 Notification - configuration
// @Accept  json
// @Produce  json
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/backup/change-oldtoken-type [get]
func (h *notificationController) updateOldTokenToLine(c *gin.Context) {

	err := h.notificationService.UpdateOldTokenToLine()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, "success")
}

// ClearNotificationCache() error
// @Summary Clear Notification Cache
// @Description Clear Notification Cache
// @Tags V2 Notification - configuration
// @Accept  json
// @Produce  json
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/clear-notification-cache [get]
func (h *notificationController) clearNotificationCache(c *gin.Context) {

	err := h.notificationService.ClearNotificationCache()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, "success")
}

// GetUserNewMemberForNoti() ([]model.UserNewMemberCountResponse, error)
// @Summary Get User New Member For Noti
// @Description Get User New Member For Noti
// @Tags V2 Notification - configuration
// @Accept  json
// @Produce  json
// @Success 200 {object} []model.UserNewMemberCountResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /noti-summary/user-new-member [get]
func (h *notificationController) getUserNewMemberForNoti(c *gin.Context) {

	configurationNotification, err := h.notificationService.GetUserNewMemberForNoti()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, configurationNotification)
}

// @Summary Get Transaction daily Summary Report Notification
// @Description Get Transaction daily Summary Report Notification
// @Tags V2 Notification - Cronjob
// @Accept  json
// @Produce  json
// @Success 200 {object} model.GetTransactionSummaryReportNotificationResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /cronjob/noti-summary/daily-transaction-summary-report [get]
func (h *notificationController) GetTransactionDailySummaryReportNotification(c *gin.Context) {

	_, err := h.notificationService.GetTransactionDailySummaryReportNotification()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, nil)
}

// @Summary Get Transaction hour Summary Report Notification
// @Description Get Transaction hour Summary Report Notification
// @Tags V2 Notification - Cronjob
// @Accept  json
// @Produce  json
// @Success 200 {object} model.GetTransactionSummaryReportNotificationResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /cronjob/noti-summary/hour-transaction-summary-report [get]
func (h *notificationController) GetTransactionHourSummaryReportNotification(c *gin.Context) {

	_, err := h.notificationService.GetTransactionHourSummaryReportNotification()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, nil)
}

// @Summary Get Affiliate Summary Report Notification
// @Description Get Affiliate Summary Report Notification
// @Tags V2 Notification - Cronjob
// @Accept  json
// @Produce  json
// @Success 200 {object} model.GetTransactionSummaryReportNotificationResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /cronjob/noti-summary/affiliate-summary-report [get]
func (h *notificationController) getAffiliateSummaryReportNotification(c *gin.Context) {

	_, err := h.notificationService.GetAffiliateSummaryReportNotification()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, nil)
}
