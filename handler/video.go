package handler

import (
	"cybergame-api/model"
	"cybergame-api/service"
	"strconv"

	"cybergame-api/repository"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

type videoController struct {
	VideoService service.VideoService
}

func newVideoController(
	VideoService service.VideoService,
) videoController {
	return videoController{VideoService}
}

func VideoController(r *gin.RouterGroup, db *gorm.DB, rdb *redis.Client) {

	repo := repository.NewVideoRepository(db)
	fileRepo := repository.NewFileRepository(db)
	cacheRepo := repository.NewCacheRepository(rdb)
	service := service.NewVideoService(repo, fileRepo, cacheRepo)
	handler := newVideoController(service)

	video := r.Group("/video")
	video.GET("list", handler.getVideoList)
	video.GET(":videoId", handler.getVideoDetail)
	video.POST("", handler.createVideo)
	video.PUT(":videoId", handler.updateVideo)
	video.DELETE(":videoId", handler.deleteVideo)
}

// @Summary ดึงข้อมูลวีดีโอ
// @Description Get Video List
// @Tags Video
// @Accept  json
// @Security BearerAuth
// @Produce  json
// @Param query query model.VideoQuery true "query"
// @Success 200 {object} model.VideoListResponse
// @Failure 400 {object} ErrorResponse
// @Router /video/list [get]
func (h videoController) getVideoList(c *gin.Context) {

	query := model.VideoQuery{}

	if err := c.ShouldBindQuery(&query); err != nil {
		HandleError(c, err)
		return
	}

	result, err := h.VideoService.VideoGetList(query)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, result)
}

// @Summary ดึงข้อมูลวีดีโอ
// @Description Get Video Detail
// @Tags Video
// @Accept  json
// @Security BearerAuth
// @Produce  json
// @Param videoId path int true "videoId"
// @Success 200 {object} model.VideoDetail
// @Failure 400 {object} ErrorResponse
// @Router /video/{videoId} [get]
func (h videoController) getVideoDetail(c *gin.Context) {

	videoId := c.Param("videoId")
	toInt64, err := strconv.ParseInt(videoId, 10, 64)
	if err != nil {
		HandleError(c, err)
		return
	}

	result, err := h.VideoService.VideoDetail(toInt64)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, result)
}

// @Summary สร้างข้อมูลวีดีโอ
// @Description Create Video
// @Tags Video
// @Accept  json
// @Security BearerAuth
// @Produce  json
// @Param body body model.VideoBody true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} ErrorResponse
// @Router /video [post]
func (h videoController) createVideo(c *gin.Context) {

	body := model.VideoBody{}

	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.VideoService.VideoCreate(body); err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(201, model.Success{Message: "Created success"})
}

// @Summary แก้ไขข้อมูลวีดีโอ
// @Description Update Video
// @Tags Video
// @Accept  json
// @Security BearerAuth
// @Produce  json
// @Param videoId path int true "videoId"
// @Param body body model.VideoUpdateBody true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} ErrorResponse
// @Router /video/{videoId} [put]
func (h videoController) updateVideo(c *gin.Context) {

	videoId := c.Param("videoId")
	toInt64, err := strconv.ParseInt(videoId, 10, 64)
	if err != nil {
		HandleError(c, err)
		return
	}

	body := model.VideoUpdateBody{}

	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.VideoService.VideoUpdate(toInt64, body); err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(201, model.Success{Message: "Updated success"})
}

// @Summary ลบข้อมูลวีดีโอ
// @Description Delete Video
// @Tags Video
// @Accept  json
// @Security BearerAuth
// @Produce  json
// @Param videoId path int true "videoId"
// @Success 201 {object} model.Success
// @Failure 400 {object} ErrorResponse
// @Router /video/{videoId} [delete]
func (h videoController) deleteVideo(c *gin.Context) {

	videoId := c.Param("videoId")
	toInt64, err := strconv.ParseInt(videoId, 10, 64)
	if err != nil {
		HandleError(c, err)
		return
	}

	if err := h.VideoService.VideoDelete(toInt64); err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(201, model.Success{Message: "Deleted success"})
}
