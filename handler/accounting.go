package handler

import (
	"cybergame-api/helper"
	"cybergame-api/middleware"
	"cybergame-api/model"
	"cybergame-api/repository"
	"cybergame-api/service"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"gorm.io/gorm"
)

type accountingController struct {
	accountingService service.AccountingService
}

func newAccountingController(
	accountingService service.AccountingService,
) accountingController {
	return accountingController{accountingService}
}

func AccountingController(r *gin.RouterGroup, db *gorm.DB) {

	actionService := service.NewAdminActionService(repository.NewAdminActionRepository(db))
	repo := repository.NewAccountingRepository(db)
	userRepo := repository.NewUserRepository(db)
	perRepo := repository.NewPermissionRepository(db)            // Create a PermissionRepository instance
	groupRepo := repository.NewGroupRepository(db)               // Create a GroupRepository instance
	agentConnectRepo := repository.NewAgentConnectRepository(db) // Create an AgentConnectRepository instance
	otpRepo := repository.NewOtpRepository(db)                   // Create an OtpRepository instance
	agentInfoRepo := repository.NewAgentInfoRepository(db)       // Create an AgentInfoRepository instance
	recommendRepo := repository.NewRecommendRepository(db)
	afRepo := repository.NewAffiliateRepository(db)
	serviceRepo := repository.NewNotificationRepository(db)
	serviceNoti := service.NewNotificationService(serviceRepo)
	afService := service.NewAffiliateService(db, afRepo, agentConnectRepo, serviceNoti)
	notiRepo := repository.NewNotificationRepository(db)
	notiService := service.NewNotificationService(notiRepo)
	userService := service.NewUserService(
		userRepo,
		db,
		perRepo,
		groupRepo,
		otpRepo,
		agentInfoRepo,
		recommendRepo,
		afRepo,
		notiService,
		actionService,
	)
	gameService := service.NewGameService(agentInfoRepo)
	agRepo := repository.NewAgentConnectRepository(db)
	allianceRepo := repository.NewAllianceRepository(db)
	alService := service.NewAllianceService(allianceRepo, db, agRepo)
	activityLusckyWheelRepo := repository.NewActivityLuckyWheelRepository(db)
	activityLuckyWheelService := service.NewActivityLuckyWheelService(activityLusckyWheelRepo, db, serviceNoti)
	promotionWebRepo := repository.NewPromotionWebRepository(db)
	promotionWebService := service.NewPromotionWebService(promotionWebRepo, db, serviceNoti, userService)
	accountingService := service.NewAccountingService(repo, userService, gameService, agentConnectRepo, notiService, afService, alService, actionService, activityLuckyWheelService, promotionWebService, db)
	handler := newAccountingController(accountingService)

	role := middleware.Role(db)
	singleSession := middleware.SingleSession(db)

	root := r.Group("/accounting", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	root.GET("/autocreditflags/list", handler.getAutoCreditFlags)
	root.GET("/autowithdrawflags/list", handler.getAutoWithdrawFlags)
	root.GET("/qrwalletstatuses/list", handler.getQrWalletStatuses)
	root.GET("/accountpriorities/list", handler.getAccountPriorities)
	root.GET("/accountstatuses/list", handler.getAccountStatuses)
	root.GET("/accountbotstatuses/list", handler.getAccountBotStatuses)
	root.GET("/transfertypes/list", handler.getTransferTypes)
	root.GET("/bankaccounts/allow-sms-mode", handler.getBankAllowSmsMode)

	bankRoute := root.Group("/banks", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	bankRoute.GET("/list", role.CheckPermission([]string{"member", "member_edit", "member_delete", "admin_dashboard"}), handler.getBanks)

	accountTypeRoute := root.Group("/accounttypes", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	accountTypeRoute.GET("/list", handler.getAccountTypes)

	accountRoute := root.Group("/bankaccounts", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	accountRoute.PUT("/sort", role.CheckPermission([]string{"bank"}), handler.sortBankAccountList)
	accountRoute.GET("/list", role.CheckPermission([]string{"bank"}), handler.getBankAccounts)
	accountRoute.GET("/base-list", handler.getBankAccountList)
	accountRoute.GET("/detail/:id", role.CheckPermission([]string{"bank"}), handler.getBankAccountById)
	accountRoute.POST("", role.CheckPermission([]string{"bank"}), handler.createBankAccount)
	accountRoute.PATCH("/:id", role.CheckPermission([]string{"bank"}), handler.updateBankAccount)
	accountRoute.PUT("/allow-show/:id", role.CheckPermission([]string{"bank"}), handler.UpdateBankAccountIsShowBank)
	accountRoute.DELETE("/:id", role.CheckPermission([]string{"bank"}), handler.deleteBankAccount)
	accountRoute.PUT("/priority-withdraw", handler.updateBankAccountPriority)
	accountRoute.GET("/priority-withdraw/validation", handler.bankAccountWithDrawPriorityValidation)
	accountRoute.GET("/option", role.CheckPermission([]string{"bank", "deposit_withdrawal"}), handler.webBankAccountAdminList)
	accountRoute.POST("/upload-image", role.CheckPermission([]string{"bank"}), handler.uploadImageToCloudflareBankAccountQr)
	accountRoute.GET("/total-statement", handler.totalBankStatementSummary)

	account2Route := root.Group("/bankaccounts2", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	account2Route.GET("/settings", handler.getExternalSettings)
	account2Route.GET("/customeraccount", handler.getCustomerAccountsInfo)
	account2Route.GET("/list", handler.getExternalAccounts)
	account2Route.GET("/status/:account", role.CheckPermission([]string{"bank"}), handler.getExternalAccountStatus)
	account2Route.GET("/balance/:account", handler.getExternalAccountBalance)
	account2Route.POST("", handler.createExternalAccount)
	account2Route.PUT("", role.CheckPermission([]string{"bank"}), handler.updateExternalAccount)
	account2Route.PUT("/status", role.CheckPermission([]string{"bank"}), handler.enableExternalAccount)
	account2Route.DELETE("/:account", role.CheckPermission([]string{"bank"}), handler.deleteExternalAccount)
	account2Route.POST("/transfer", role.CheckPermission([]string{"bank"}), handler.transferExternalAccount)
	account2Route.GET("/logs", handler.getExternalAccountLogs)
	account2Route.GET("/statements", handler.getExternalAccountStatements)
	account2Route.POST("/config", role.CheckPermission([]string{"SUPER_ADMIN"}), handler.createBotaccountConfig)

	webhookRoute := r.Group("/accounting/webhooks")
	webhookRoute.POST("/action", handler.webhookAction)
	webhookRoute.POST("/noti", handler.webhookNoti)

	transactionRoute := root.Group("/transactions", middleware.AuthorizeAdmin, singleSession.SingleAdminSession(), role.CheckPermission([]string{"bank"}))
	transactionRoute.GET("/list", role.CheckPermission([]string{"bank"}), handler.getTransactions)
	transactionRoute.GET("/detail/:id", handler.getTransactionById)
	transactionRoute.POST("", role.CheckPermission([]string{"bank"}), handler.createTransaction)
	transactionRoute.DELETE("/:id", handler.deleteTransaction)

	transferRoute := root.Group("/transfers", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	transferRoute.GET("/list", handler.getTransfers)
	transferRoute.GET("/detail/:id", handler.getTransferById)
	transferRoute.POST("", handler.createTransfer)
	transferRoute.POST("/confirm/:id", handler.confirmTransfer)
	transferRoute.DELETE("/:id", handler.deleteTransfer)

	statementRoute := root.Group("/statements", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	statementRoute.GET("/list", handler.getAccountStatements)
	statementRoute.GET("/fastbank-accounts/list", handler.getFastBankAccountList)
	statementRoute.GET("/list2", handler.getExternalStatementWithTransactionList)
	statementRoute.GET("/unmatch-list", handler.getExternalUnreadStatementWithTransactionList)
	statementRoute.GET("/detail/:id", handler.getAccountStatementById)

	webAccounting := r.Group("/web-accounting", middleware.AuthorizeUser, singleSession.SingleUserSession())
	webAccounting.GET("/list", handler.getWebBankDepositAccount)
	webAccounting.GET("/bank", handler.getWebBanks)

	configRoute := root.Group("/config", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	configRoute.GET("/bank-withdraw-max", handler.getBankWithdrawMaxConfig)

	accountingReportOptionRoute := r.Group("/summary-report", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	accountingReportOptionRoute.GET("/account-list", role.CheckPermission([]string{"deposit_list", "withdraw_list"}), handler.getSummaryReportAccountList)
	accountingReportOptionRoute.GET("/bank-transaction-graph2", role.CheckPermission([]string{"deposit_list", "withdraw_list"}), handler.getBankTransactionSummaryGraph2)

	// /test/bankaccounts/external-match-withdraw-transaction
	testRoute := r.Group("/test")
	testRoute.POST("/bankaccounts/external-match-withdraw-transaction", handler.externalMatchWithdrawTransaction)
	testRoute.POST("/unmarshal", handler.testUnmarshal)

	// exchange
	exchangeRoute := r.Group("/accounting", middleware.AuthorizeAdmin)
	exchangeRoute.GET("/exchange-currency/list", handler.getExchangeCurrencyList)
	exchangeRoute.POST("/exchange-rate", handler.createExchangeRate)
	exchangeRoute.GET("/exchange-rate/list", handler.getExchangeRateList)
	exchangeRoute.GET("/exchange-rate/update-log/list", handler.getExchangeUpdateLogList)

	exchangeUserRoute := r.Group("/accounting/user", middleware.AuthorizeUser)
	exchangeUserRoute.GET("/exchange-rate/list", handler.getUserExchangeRateList)
	// accounting/exchange-currency/laos
	exchangeCurrencyRoute := r.Group("/accounting/exchange-currency")
	exchangeCurrencyRoute.GET("/laos", handler.getLaosExchangeCurrency)

	// sms
	webDepositRoute := r.Group("/web/deposit", middleware.AuthorizeUser)
	webDepositRoute.POST("/sms-mode", handler.createSmsModeFastbankDeposit)

	// /accounting/bankaccounts/auto-is-show
	backup := r.Group("/accounting/bankaccounts/auto-is-show")
	backup.GET("", handler.migrateOldBankAccount)
}

// @Summary (getSummaryReportAccountList) สรุปภาพรวม ข้อมูลรายการบัญชีธนาคารฝากถอน Fastbank only
// @Description (getSummaryReportAccountList) สรุปภาพรวม ข้อมูลรายการบัญชีธนาคารฝากถอน Fastbank only
// @Tags Report - Summary Report
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} []model.FastBankAccountResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /summary-report/account-list [get]
func (h accountingController) getSummaryReportAccountList(c *gin.Context) {

	data, err := h.accountingService.GetSummaryReportAccountList()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary getBanks get Bank List
// @Description ดึงข้อมูลตัวเลือก รายชื่อธนาคารทั้งหมด
// @Tags Accounting - Options
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param page query int false "page"
// @Param limit query int false "limit"
// @Param search query string false "search"
// @Param sortCol query string false "sortCol"
// @Param sortAsc query string false "sortAsc"
// @Success 200 {object} model.SuccessWithPagination
// @Router /accounting/banks/list [get]
func (h accountingController) getBanks(c *gin.Context) {

	var query model.BankListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.accountingService.GetBanks(query)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, model.SuccessWithPagination{List: data.List, Total: data.Total})
}

// @Summary getBanks get Bank List (web)
// @Description ดึงข้อมูลตัวเลือก รายชื่อธนาคารทั้งหมด
// @Tags Accounting - Options
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.SuccessWithPagination
// @Router /web-accounting/bank [get]
func (h accountingController) getWebBanks(c *gin.Context) {

	var query model.BankListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.accountingService.GetBanks(query)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, model.SuccessWithPagination{List: data.List, Total: data.Total})
}

// @Summary getAccountTypes
// @Description ดึงข้อมูลตัวเลือก ประเภทบัญชีธนาคารทั้งหมด
// @Tags Accounting - Options
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.SuccessWithPagination
// @Router /accounting/accounttypes/list [get]
func (h accountingController) getAccountTypes(c *gin.Context) {

	var query model.AccountTypeListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.accountingService.GetAccountTypes(query)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, model.SuccessWithPagination{List: data.List, Total: data.Total})
}

// @Summary getAutoCreditFlags ยังไม่ใช้
// @Description ดึงข้อมูลตัวเลือก การตั้งค่าปรับเครดิตอัตโนมัติ
// @Tags Accounting - Options
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.SuccessWithPagination
// @Router /accounting/autocreditflags/list [get]
func (h accountingController) getAutoCreditFlags(c *gin.Context) {
	// ยังไม่มีใน code ที่เอาไปใช้ ผมยังไม่ได้สร้าง table
	var data = []model.SimpleOption{
		{Key: "manual", Name: "สร้างใบงานและปรับเครดิตเอง"},
		{Key: "auto", Name: "ปรับเครดิตออโต้ (Bot)"},
	}
	c.JSON(200, model.SuccessWithPagination{List: data, Total: 2})
}

// @Summary getAutoWithdrawFlags ยังไม่ใช้
// @Description ดึงข้อมูลตัวเลือก การตั้งค่าถอนโอนเงินอัตโนมัติ
// @Tags Accounting - Options
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.SuccessWithPagination
// @Router /accounting/autowithdrawflags/list [get]
func (h accountingController) getAutoWithdrawFlags(c *gin.Context) {
	// ยังไม่มีใน code ที่เอาไปใช้ ผมยังไม่ได้สร้าง table

	var data = []model.SimpleOption{
		{Key: "manual", Name: "สร้างใบงานและปรับเครดิตเอง"},
		{Key: "auto_backoffice", Name: "บัญชีถอนหลัก ปรับเครดิตออโต้ คลิกผ่านระบบหลังบ้าน"},
		{Key: "auto_bot", Name: "บัญชีถอนหลัก ปรับเครดิตออโต้ โอนเงินออโต้ (Bot)"},
	}
	c.JSON(200, model.SuccessWithPagination{List: data, Total: 3})
}

// @Summary getQrWalletStatuses ยังไม่ใช้
// @Description ดึงข้อมูลตัวเลือก การเปิดใช้งาน QR Wallet
// @Tags Accounting - Options
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.SuccessWithPagination
// @Router /accounting/qrwalletstatuses/list [get]
func (h accountingController) getQrWalletStatuses(c *gin.Context) {
	// ยังไม่มีใน code ที่เอาไปใช้ ผมยังไม่ได้สร้าง table
	var data = []model.SimpleOption{
		{Key: "use_qr", Name: "เปิด"},
		{Key: "disabled", Name: "ปิด"},
	}
	c.JSON(200, model.SuccessWithPagination{List: data, Total: 2})
}

// @Summary (getAccountStatuses) ดึงข้อมูลตัวเลือก สถานะบัญชีธนาคาร
// @Description (getAccountStatuses)
// @Tags Accounting - Options
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.SuccessWithPagination
// @Router /accounting/accountstatuses/list [get]
func (h accountingController) getAccountStatuses(c *gin.Context) {

	list, err := h.accountingService.GetAccountStatus()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, list)
}

// @Summary getAccountPriorities
// @Description ดึงข้อมูลตัวเลือก ลำดับความสำคัญบัญชีธนาคาร
// @Tags Accounting - Options
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.SuccessWithPagination
// @Router /accounting/accountpriorities/list [get]
func (h accountingController) getAccountPriorities(c *gin.Context) {

	// var data = []model.SimpleOption{
	// 	{Key: "new", Name: "ระดับ NEW ทั่วไป"},
	// 	{Key: "gold", Name: "ระดับ Gold ฝากมากกว่า 10 ครั้ง"},
	// 	{Key: "platinum", Name: "ระดับ Platinum ฝากมากกว่า 20 ครั้ง"},
	// 	{Key: "vip", Name: "ระดับ VIP ฝากมากกว่า 20 ครั้ง"},
	// 	{Key: "classic", Name: "ระดับ CLASSIC ฝากสะสมมากกว่า 1,000 บาท"},
	// 	{Key: "superior", Name: "ระดับ SUPERIOR ฝากสะสมมากกว่า 10,000 บาท"},
	// 	{Key: "deluxe", Name: "ระดับ DELUXE ฝากสะสมมากกว่า 100,000 บาท"},
	// 	{Key: "wisdom", Name: "ระดับ WISDOM ฝากสะสมมากกว่า 500,000 บาท"},
	// }
	// c.JSON(200, model.SuccessWithPagination{List: data, Total: 8})

	data, err := h.accountingService.GetBankAccountPriorities()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithPagination{List: data.List, Total: data.Total})
}

// @Summary getAccountBotStatuses
// @Description ดึงข้อมูลตัวเลือก สถานะบอท
// @Tags Accounting - Options
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.SuccessWithPagination
// @Router /accounting/accountbotstatuses/list [get]
func (h accountingController) getAccountBotStatuses(c *gin.Context) {
	// มีใน code mที่เอาไปใช้
	// var data = []model.SimpleOption{
	// 	{Key: "active", Name: "เชื่อมต่อ"},
	// 	{Key: "disconnected", Name: "ไม่ได้เชื่อมต่อ"},
	// }
	list, err := h.accountingService.GetConnectionStatus()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, list)
}

// @Summary get Transfer Types
// @Description ดึงข้อมูลตัวเลือก ประเภทการทำธุรกรรม (ฝาก/ถอน)
// @Tags Accounting - Options
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.SuccessWithPagination
// @Router /accounting/transfertypes/list [get]
func (h accountingController) getTransferTypes(c *gin.Context) {
	// มีใน code ที่เอาไปใช้ แค่เอาไว้ search list ยังไม่ต้องแยก database มา
	var data = []model.SimpleOption{
		{Key: "all_deposit", Name: "ฝากเงิน"},
		{Key: "all_withdraw", Name: "ถอนเงิน"},
	}
	c.JSON(200, model.SuccessWithPagination{List: data, Total: 2})
}

// @Summary GetBankAccountList ดึงจาก fastbank
// @Description | Status           | Description          |
// @Description | ---------------- | ------------|
// @Description | DEPOSIT |  บช ฝาก |
// @Description | WITHDRAW | บช ถอน |
// @Description | HOLD | ฟัก |
// @Description | ---------------- | ------------|
// @Description ดึงข้อมูลลิสบัญชีธนาคาร ใช้แสดงในหน้า จัดการธนาคาร
// @Tags Accounting - Bank Accounts
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.BankAccountListRequest true "BankAccountListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/bankaccounts/list [get]
func (h accountingController) getBankAccounts(c *gin.Context) {

	var query model.BankAccountListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.accountingService.GetBankAccounts(query)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, model.SuccessWithPagination{List: data.List, Total: data.Total})
}

// @Summary GetBankAccountList ไม่ดึงจาก fastbank
// @Description ดึงข้อมูลลิสบัญชีธนาคาร ใช้แสดงในหน้า จัดการธนาคาร
// @Description | Status           | Description          |
// @Description | ---------------- | ------------|
// @Description | DEPOSIT |  บช ฝาก |
// @Description | WITHDRAW | บช ถอน |
// @Description | HOLD | ฟัก |
// @Description | ---------------- | ------------|
// @Tags Accounting - Bank Accounts
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.BankAccountListRequest true "BankAccountListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/bankaccounts/base-list [get]
func (h accountingController) getBankAccountList(c *gin.Context) {

	var query model.BankAccountListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.accountingService.GetBankAccountList(query)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, model.SuccessWithPagination{List: data.List, Total: data.Total})
}

// @Summary GetBankAccountById
// @Description ดึงข้อมูลบัญชีธนาคาร ด้วย id
// @Tags Accounting - Bank Accounts
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/bankaccounts/detail/{id} [get]
func (h accountingController) getBankAccountById(c *gin.Context) {

	var accounting model.BankGetByIdRequest
	if err := c.ShouldBindUri(&accounting); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.accountingService.GetBankAccountById(accounting)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, model.SuccessWithData{Message: "success", Data: data})
}

// @Summary (createBankAccount) สร้าง บัญชีธนาคาร ใหม่ ในหน้า จัดการธนาคาร
// @Description (createBankAccount) สร้าง บัญชีธนาคาร ใหม่ ในหน้า จัดการธนาคาร
// @Tags Accounting - Bank Accounts
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.BankAccountCreateRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/bankaccounts [post]
func (h accountingController) createBankAccount(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	var req model.BankAccountCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(req); err != nil {
		HandleError(c, err)
		return
	}
	req.CreateBy = adminId

	// [ADMIN_LOG]
	if err := h.accountingService.LogAdmin("createBankAccount", adminId, req); err != nil {
		log.Println("createBankAccount.ERROR: ", err)
	}

	err := h.accountingService.CreateBankAccount(req)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Created success"})
}

// @Summary (updateBankAccount) แก้ไข บัญชีธนาคาร ในหน้า จัดการธนาคาร
// @Description (updateBankAccount) แก้ไข บัญชีธนาคาร ในหน้า จัดการธนาคาร
// @Tags Accounting - Bank Accounts
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Param body body model.BankAccountUpdateRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/bankaccounts/{id} [patch]
func (h accountingController) updateBankAccount(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	id := c.Param("id")
	identifier, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleError(c, err)
		return
	}

	body := model.BankAccountUpdateRequest{}
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}
	body.UpdateBy = adminId

	// [ADMIN_LOG]
	if err := h.accountingService.LogAdmin("updateBankAccount", adminId, body); err != nil {
		log.Println("updateBankAccount.ERROR: ", err)
	}

	if err := h.accountingService.UpdateBankAccount(identifier, body); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Updated success"})
}

// @Summary (UpdateBankAccountIsShowBank) แก้ไข is show front
// @Description (UpdateBankAccountIsShowBank) แก้ไข  is show front
// @Tags Accounting - Bank Accounts
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Param body body model.UpdateBankAccountIsShowBankRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/bankaccounts/allow-show/{id} [put]
func (h accountingController) UpdateBankAccountIsShowBank(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	id := c.Param("id")
	identifier, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleError(c, err)
		return
	}

	body := model.UpdateBankAccountIsShowBankRequest{}
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	body.AdminId = adminId
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}
	body.Id = identifier

	// [ADMIN_LOG]
	if err := h.accountingService.LogAdmin("updateBankAccount", adminId, body); err != nil {
		log.Println("updateBankAccount.ERROR: ", err)
	}

	if err := h.accountingService.UpdateBankAccountIsShowBank(body); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Updated success"})
}

// @Summary (deleteBankAccount) ลบข้อมูลบัญชีธนาคาร ด้วย id
// @Description (deleteBankAccount) ลบข้อมูลบัญชีธนาคาร ด้วย id
// @Tags Accounting - Bank Accounts
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/bankaccounts/{id} [delete]
func (h accountingController) deleteBankAccount(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	id := c.Param("id")
	identifier, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleError(c, err)
		return
	}

	var req model.BankAccountDeleteRequest
	req.AccountId = identifier
	req.UpdateBy = adminId

	// [ADMIN_LOG]
	if err := h.accountingService.LogAdmin("deleteBankAccount", adminId, req); err != nil {
		log.Println("deleteBankAccount.ERROR: ", err)
	}

	if err := h.accountingService.DeleteBankAccount(req); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Deleted success"})
}

// @Summary GetTransactionList
// @Description ดึงข้อมูลลิสธุรกรรม ใช้แสดงในหน้า จัดการธนาคาร - ธุรกรรม และ รายการฝากถอนเงินสด
// @Tags Accounting - Bank Account Transactions
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.BankAccountTransactionListRequest true "BankAccountTransactionListRequest"
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/transactions/list [get]
func (h accountingController) getTransactions(c *gin.Context) {

	var query model.BankAccountTransactionListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.accountingService.GetTransactions(query)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, model.SuccessWithPagination{List: data.List, Total: data.Total})
}

// @Summary GetTransactionById
// @Description ดึงข้อมูลธุรกรรมด้วย id *ยังไม่ได้ใช้งาน*
// @Tags Accounting - Bank Account Transactions
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/transactions/detail/{id} [get]
func (h accountingController) getTransactionById(c *gin.Context) {

	var accounting model.BankGetByIdRequest

	if err := c.ShouldBindUri(&accounting); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.accountingService.GetTransactionById(accounting)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, model.SuccessWithData{Message: "success", Data: data})
}

// @Summary (createTransaction) สร้าง ธุรกรรม ในหน้า จัดการธนาคาร
// @Description (createTransaction) สร้าง ธุรกรรม ในหน้า จัดการธนาคาร - ธุรกรรม ส่ง AccountId มาด้วย
// @Tags Accounting - Bank Account Transactions
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.BankAccountTransactionBody true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/transactions [post]
func (h accountingController) createTransaction(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	username, err := h.accountingService.CheckCurrentUsername(c.MustGet("username"))
	if err != nil {
		HandleError(c, err)
		return
	}

	var accounting model.BankAccountTransactionBody
	accounting.CreatedByUsername = *username
	if err := c.ShouldBindJSON(&accounting); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(accounting); err != nil {
		HandleError(c, err)
		return
	}

	// [ADMIN_LOG]
	if err := h.accountingService.LogAdmin("createTransaction", adminId, accounting); err != nil {
		log.Println("createTransaction.ERROR: ", err)
	}

	if err := h.accountingService.CreateTransaction(accounting, adminId); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Created success"})
}

// @Summary (deleteTransaction) ลบข้อมูลธุรกรรมด้วย id ใช้ในหน้า จัดการธนาคาร
// @Description (deleteTransaction) ลบข้อมูลธุรกรรมด้วย id ใช้ในหน้า จัดการธนาคาร - ธุรกรรม ส่งรหัสผ่านมาเพื่อยืนยันด้วย
// @Tags Accounting - Bank Account Transactions
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Param body body model.ConfirmRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/transactions/{id} [delete]
func (h accountingController) deleteTransaction(c *gin.Context) {

	adminId, err := h.accountingService.CheckCurrentAdminId(c.MustGet("adminId"))
	if err != nil {
		HandleError(c, err)
		return
	}

	id := c.Param("id")
	identifier, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleError(c, err)
		return
	}

	var confirmation model.ConfirmRequest
	confirmation.UserId = *adminId
	if err := c.ShouldBindJSON(&confirmation); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(confirmation); err != nil {
		HandleError(c, err)
		return
	}
	if _, err := h.accountingService.CheckConfirmationPassword(confirmation); err != nil {
		HandleError(c, err)
		return
	}

	// [ADMIN_LOG]
	if err := h.accountingService.LogAdmin("deleteTransaction", *adminId, identifier); err != nil {
		log.Println("deleteTransaction.ERROR: ", err)
	}

	delErr := h.accountingService.DeleteTransaction(identifier)
	if delErr != nil {
		HandleError(c, delErr)
		return
	}
	c.JSON(201, model.Success{Message: "Deleted success"})
}

// @Summary GetTransferList
// @Description ดึงข้อมูลลิสการโอนเงิน ใช้แสดงในหน้า จัดการธนาคาร - ธุรกรรม
// @Tags Accounting - Bank Account Transfers
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.BankAccountTransferListRequest true "BankAccountTransferListRequest"
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/transfers/list [get]
func (h accountingController) getTransfers(c *gin.Context) {

	var query model.BankAccountTransferListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.accountingService.GetTransfers(query)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, model.SuccessWithPagination{List: data.List, Total: data.Total})
}

// @Summary GetTransferByID
// @Description ดึงข้อมูลการโอนด้วย id *ยังไม่ได้ใช้งาน*
// @Tags Accounting - Bank Account Transfers
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/transfers/detail/{id} [get]
func (h accountingController) getTransferById(c *gin.Context) {

	var accounting model.BankGetByIdRequest
	if err := c.ShouldBindUri(&accounting); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.accountingService.GetTransferById(accounting)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, model.SuccessWithData{Message: "success", Data: data})
}

// @Summary (createTransfer) สร้างข้อมูลการโอน
// @Description (createTransfer) สร้างข้อมูลการโอน
// @Tags Accounting - Bank Account Transfers
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.BankAccountTransferBody true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/transfers [post]
func (h accountingController) createTransfer(c *gin.Context) {

	username, err := h.accountingService.CheckCurrentUsername(c.MustGet("username"))
	if err != nil {
		HandleError(c, err)
		return
	}

	var accounting model.BankAccountTransferBody
	accounting.CreatedByUsername = *username
	if err := c.ShouldBindJSON(&accounting); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(accounting); err != nil {
		HandleError(c, err)
		return
	}

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))
	// [ADMIN_LOG]
	if err := h.accountingService.LogAdmin("createTransfer", adminId, accounting); err != nil {
		log.Println("createTransfer.ERROR: ", err)
	}

	if err := h.accountingService.CreateTransfer(accounting); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Created success"})
}

// @Summary (confirmTransfer) ยืนยันการโอน
// @Description (confirmTransfer) ยืนยันการโอน
// @Tags Accounting - Bank Account Transfers
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/transfers/confirm/{id} [post]
func (h accountingController) confirmTransfer(c *gin.Context) {

	adminId, err := h.accountingService.CheckCurrentAdminId(c.MustGet("adminId"))
	if err != nil {
		HandleError(c, err)
		return
	}

	id := c.Param("id")
	identifier, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleError(c, err)
		return
	}

	// [ADMIN_LOG]
	if err := h.accountingService.LogAdmin("confirmTransfer", *adminId, identifier); err != nil {
		log.Println("confirmTransfer.ERROR: ", err)
	}

	if err := h.accountingService.ConfirmTransfer(identifier, *adminId); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Updated success"})
}

// @Summary (deleteTransfer) ลบข้อมูลการโอนด้วย id ใช้ในหน้า จัดการธนาคาร
// @Description (deleteTransfer) ลบข้อมูลการโอนด้วย id ใช้ในหน้า จัดการธนาคาร - ธุรกรรม ส่งรหัสผ่านมาเพื่อยืนยันด้วย
// @Tags Accounting - Bank Account Transfers
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Param body body model.ConfirmRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/transfers/{id} [delete]
func (h accountingController) deleteTransfer(c *gin.Context) {

	adminId, err := h.accountingService.CheckCurrentAdminId(c.MustGet("adminId"))
	if err != nil {
		HandleError(c, err)
		return
	}

	id := c.Param("id")
	identifier, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleError(c, err)
		return
	}

	var confirmation model.ConfirmRequest
	confirmation.UserId = *adminId
	if err := c.ShouldBindJSON(&confirmation); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(confirmation); err != nil {
		HandleError(c, err)
		return
	}
	if _, err := h.accountingService.CheckConfirmationPassword(confirmation); err != nil {
		HandleError(c, err)
		return
	}

	// [ADMIN_LOG]
	if err := h.accountingService.LogAdmin("deleteTransfer", *adminId, identifier); err != nil {
		log.Println("deleteTransfer.ERROR: ", err)
	}

	if delErr := h.accountingService.DeleteTransfer(identifier); delErr != nil {
		HandleError(c, delErr)
		return
	}
	c.JSON(201, model.Success{Message: "Deleted success"})
}

// @Summary (getFastBankAccountList) ดึงข้อมูลลิสบัญชีธนาคาร ใช้แสดงในหน้า รายการเดินบัญชี
// @Description (getFastBankAccountList) ดึงข้อมูลลิสบัญชีธนาคาร ใช้แสดงในหน้า รายการเดินบัญชี
// @Description | Status           | Description          |
// @Description | ---------------- | ------------|
// @Description | DEPOSIT |  บช ฝาก |
// @Description | WITHDRAW | บช ถอน |
// @Description | ---------------- | ------------|
// @Description
// @Tags Accounting - Bank Account Statements
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.BankAccountListRequest true "BankAccountListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/statements/fastbank-accounts/list [get]
func (h accountingController) getFastBankAccountList(c *gin.Context) {

	var query model.BankAccountListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.accountingService.GetFastBankAccountList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithPagination{List: data.List, Total: data.Total})
}

// @Summary (getAccountStatements) รายการเดินบัญชีธนาคาร
// @Description (getAccountStatements) ดึงข้อมูล Statement รายการเดินบัญชีธนาคาร จาก FASTBANK ตรงๆ
// @Tags Accounting - Bank Account Statements
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.BankAccountStatementListRequest true "BankAccountStatementListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/statements/list [get]
func (h accountingController) getAccountStatements(c *gin.Context) {

	var query model.BankAccountStatementListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.accountingService.GetAccountStatements(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithPagination{List: data.List, Total: data.Total})
}

// @Summary (getExternalStatementWithTransactionList) รายการเดินบัญชีธนาคาร แบบใหม่ 2023-11-21
// @Description (getExternalStatementWithTransactionList) ดึงข้อมูล Statement รายการเดินบัญชีธนาคาร จาก FASTBANK ตรงๆ พร้อมสถานะ
// @Description # AccountId คือไอดีบัญชีธนาคารในระบบ
// @Description # OfDate วันที่ Statement (dateTime เวลาธนาคาร)
// @Description # DirectionId ทิศทางทางการเงิน 1 ฝาก 2 ถอน
// @Description # StatusId สถานะรายการ ทั้งหมด(ไม่่ต้องส่ง) 1 รอเช็คสถานะ 2 ตรวจยอดแล้ว
// @Tags Accounting - Bank Account Statements
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.ExternalStatementWithTransactionListRequest true "ExternalStatementWithTransactionListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/statements/list2 [get]
func (h accountingController) getExternalStatementWithTransactionList(c *gin.Context) {

	var query model.ExternalStatementWithTransactionListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	list, total, err := h.accountingService.GetExternalStatementWithTransactionList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithPagination{List: list, Total: total})
}

// @Summary (getExternalUnreadStatementWithTransactionList) รายการเดินบัญชีธนาคาร ที่ยังไม่ได้เติม
// @Description (getExternalUnreadStatementWithTransactionList) ดึงข้อมูล Statement รายการเดินบัญชีธนาคาร จาก FASTBANK ที่ยังไม่ได้อ่าน
// @Description # AccountId คือไอดีบัญชีธนาคารในระบบ
// @Description # OfDate วันที่ Statement (dateTime เวลาธนาคาร)
// @Description # DirectionId ทิศทางทางการเงิน 1 ฝาก 2 ถอน
// @Description # StatusId สถานะรายการ ทั้งหมด(ไม่่ต้องส่ง) 1 รอเช็คสถานะ 2 ตรวจยอดแล้ว
// @Tags Accounting - Bank Account Statements
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.ExternalStatementWithTransactionListRequest true "ExternalStatementWithTransactionListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/statements/unmatch-list [get]
func (h accountingController) getExternalUnreadStatementWithTransactionList(c *gin.Context) {

	var query model.ExternalStatementWithTransactionListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	list, total, err := h.accountingService.GetExternalUnreadStatementWithTransactionList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithPagination{List: list, Total: total})
}

// @Summary GetAccountStatementById
// @Description ดึงข้อมูลการโอนด้วย id *ยังไม่ได้ใช้งาน*
// @Tags Accounting - Bank Account Statements
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/statements/detail/{id} [get]
func (h accountingController) getAccountStatementById(c *gin.Context) {

	var req model.BankGetByIdRequest
	if err := c.ShouldBindUri(&req); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.accountingService.GetAccountStatementById(req)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithData{Message: "success", Data: data})
}

// @Summary GetCustomerAccountsInfo เช็คชื่อบัญชีธนาคารลูกค้า
// @Description ดึงข้อมูลบัญชีธนาคารของลูกค้า เพื่อเช็คชื่อบัญชีธนาคาร
// @Tags Accounting - FASTBANK
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.CustomerAccountInfoRequest true "CustomerAccountInfoRequest"
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/bankaccounts2/customeraccount [post]
func (h accountingController) getCustomerAccountsInfo(c *gin.Context) {

	var query model.CustomerAccountInfoRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.accountingService.GetCustomerAccountsInfo(query)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, model.SuccessWithData{Message: "success", Data: data})
}

// @Summary GetExternalSettings
// @Description อัพเดทข้อมูล บัญชีธนาคารบอท ด้วยเลขบัญชี
// @Tags Accounting - FASTBANK
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/bankaccounts2/settings [get]
func (h accountingController) getExternalSettings(c *gin.Context) {

	data, err := h.accountingService.GetExternalSettings()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, model.SuccessWithData{Message: "success", Data: data})
}

// @Summary GetExternalAccounts
// @Description ดึงข้อมูลลิสบัญชีธนาคาร บอท
// @Tags Accounting - FASTBANK
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.BankAccountListRequest true "BankAccountListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/bankaccounts2/list [get]
func (h accountingController) getExternalAccounts(c *gin.Context) {

	// var query model.BankAccountListRequest
	// if err := c.ShouldBind(&query); err != nil {
	// 	HandleError(c, err)
	// 	return
	// }
	// if err := validator.New().Struct(query); err != nil {
	// 	HandleError(c, err)
	// 	return
	// }

	data, err := h.accountingService.GetExternalAccounts()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, model.SuccessWithPagination{List: data.List, Total: data.Total})
}

// @Summary GetExternalAccountStatus
// @Description ดึงข้อมูล บัญชีธนาคารบอท ด้วยเลขบัญชี
// @Tags Accounting - FASTBANK
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param account path string true "accountNumber"
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/bankaccounts2/status/{account} [get]
func (h accountingController) getExternalAccountStatus(c *gin.Context) {

	var query model.ExternalAccountStatusRequest
	query.AccountNumber = c.Param("account")

	data, err := h.accountingService.GetExternalAccountStatus(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithData{Message: "success", Data: data})
}

// @Summary GetExternalAccountBalance
// @Description ดึงข้อมูล บัญชีธนาคารบอท ด้วยเลขบัญชี
// @Tags Accounting - FASTBANK
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param account path string true "accountNumber"
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/bankaccounts2/balance/{account} [get]
func (h accountingController) getExternalAccountBalance(c *gin.Context) {

	var query model.ExternalAccountStatusRequest
	query.AccountNumber = c.Param("account")

	data, err := h.accountingService.GetExternalAccountBalance(query)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, model.SuccessWithData{Message: "success", Data: data})
}

// @Summary (createExternalAccount) สร้าง บัญชีธนาคารภายนอก ใหม่
// @Description (createExternalAccount) สร้าง บัญชีธนาคารภายนอก ใหม่
// @Tags Accounting - FASTBANK
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.ExternalAccountCreateBody true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/bankaccounts2 [post]
func (h accountingController) createExternalAccount(c *gin.Context) {

	var accounting model.ExternalAccountCreateBody
	if err := c.ShouldBindJSON(&accounting); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(accounting); err != nil {
		HandleError(c, err)
		return
	}

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))
	// [ADMIN_LOG]
	if err := h.accountingService.LogAdmin("createExternalAccount", adminId, accounting); err != nil {
		log.Println("createExternalAccount.ERROR: ", err)
	}

	if _, err := h.accountingService.CreateExternalAccount(accounting); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Created success"})
}

// @Summary UpdateExternalAccount
// @Description อัพเดทข้อมูล บัญชีธนาคารบอท ด้วยเลขบัญชี
// @Tags Accounting - FASTBANK
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.ExternalAccountCreateBody true "body"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/bankaccounts2/ [put]
func (h accountingController) updateExternalAccount(c *gin.Context) {

	var query model.ExternalAccountUpdateBody
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))
	// [ADMIN_LOG]
	if err := h.accountingService.LogAdmin("updateExternalAccount", adminId, query); err != nil {
		log.Println("updateExternalAccount.ERROR: ", err)
	}

	if _, err := h.accountingService.UpdateExternalAccount(query); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "Update success"})
}

// @Summary (enableExternalAccount) เปิด ปิด สถานะบัญชีธนาคารบอท ด้วยเลขบัญชี
// @Description (enableExternalAccount) เปิด ปิด สถานะบัญชีธนาคารบอท ด้วยเลขบัญชี
// @Tags Accounting - FASTBANK
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.BankAccountToggleFastbankRequest true "body"
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/bankaccounts2/status [put]
func (h accountingController) enableExternalAccount(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	var query model.BankAccountToggleFastbankRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}
	query.UpdateBy = adminId

	// [ADMIN_LOG]
	if err := h.accountingService.LogAdmin("EnableExternalAccount", adminId, query); err != nil {
		log.Println("EnableExternalAccount.ERROR: ", err)
	}

	// [ADMIN_ACTION] 2025-05-06 เพิ่ม log admin action
	var adminActionCreateBody model.AdminActionCreateRequest
	adminActionCreateBody.AdminId = adminId
	adminActionCreateBody.TypeId = model.ADMIN_ACTION_ACCOUNT_MANAGE
	adminActionCreateBody.Detail = fmt.Sprintf("ปรับการเปิดปิดบอท เลขบัญชี %s", query.AccountNo)
	adminActionCreateBody.JsonInput = helper.StructJson(query)
	if _, err := h.accountingService.CreateSuccessAdminAction(adminActionCreateBody); err != nil {
		log.Println("GetExternalAccountStatus.CreateSuccessAdminAction.ERROR", err)
	}

	result, err := h.accountingService.EnableExternalAccount(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithData{Message: "Update success", Data: result})
}

// @Summary (deleteExternalAccount) ลบข้อมูล บัญชีธนาคารบอท ด้วยเลขบัญชี
// @Description (deleteExternalAccount) ลบข้อมูล บัญชีธนาคารบอท ด้วยเลขบัญชี
// @Tags Accounting - FASTBANK
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param account path string true "accountNumber"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/bankaccounts2/{account} [delete]
func (h accountingController) deleteExternalAccount(c *gin.Context) {

	var query model.ExternalAccountStatusRequest
	query.AccountNumber = c.Param("account")

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))
	// [ADMIN_LOG]
	if err := h.accountingService.LogAdmin("deleteExternalAccount", adminId, query); err != nil {
		log.Println("deleteExternalAccount.ERROR: ", err)
	}

	if err := h.accountingService.DeleteExternalAccount(query); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "Delete success"})
}

// @Summary (transferExternalAccount) โอนเงิน บัญชีธนาคารบอท
// @Description (transferExternalAccount) โอนเงิน บัญชีธนาคารบอท
// @Tags Accounting - FASTBANK
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.ExternalAccountTransferRequest true "body"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/bankaccounts2/transfer [post]
func (h accountingController) transferExternalAccount(c *gin.Context) {

	var query model.ExternalAccountTransferRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))
	// [ADMIN_LOG]
	if err := h.accountingService.LogAdmin("transferExternalAccount", adminId, query); err != nil {
		log.Println("transferExternalAccount.ERROR: ", err)
	}

	if err := h.accountingService.TransferExternalAccount(query); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "Transfer success"})
}

// @Summary GetExternalAccountLogs
// @Description ดึงข้อมูล Logs บัญชีธนาคารบอท
// @Tags Accounting - FASTBANK
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.ExternalStatementListRequest true "ExternalStatementListRequest"
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/bankaccounts2/logs [get]
func (h accountingController) getExternalAccountLogs(c *gin.Context) {

	var query model.ExternalStatementListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.accountingService.GetExternalAccountLogs(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithPagination{List: data.List, Total: data.Total})
}

// @Summary GetExternalAccountStatements
// @Description ดึงข้อมูล Statement บัญชีธนาคารบอท
// @Tags Accounting - FASTBANK
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.ExternalStatementListRequest true "ExternalStatementListRequest"
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/bankaccounts2/statements [get]
func (h accountingController) getExternalAccountStatements(c *gin.Context) {

	var query model.ExternalStatementListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.accountingService.GetExternalAccountStatements(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithPagination{List: data.List, Total: data.Total})
}

// @Summary WebhookAction
// @Description เว็บฮุคแบบ GET
// @Tags Accounting - FASTBANK
// @Accept json
// @Produce json
// @Param body body model.ExternalAccountEnableRequest true "body"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/webhooks/action [post]
func (h accountingController) webhookAction(c *gin.Context) {

	jsonData, errValidate := io.ReadAll(c.Request.Body)
	if errValidate != nil {
		HandleError(c, errValidate)
		return
	}

	// IsNewStateMentList
	var resp model.WebhookStatementResponse
	errJson := json.Unmarshal(jsonData, &resp)
	if errJson != nil {
		var createLogBankTransaction model.BankTransactionLogCreateRequest
		reqJosnRequest := string(jsonData)
		reqAction := "ERROR-JSON-REQUEST-FASTBANK"
		reqType := "pending"
		payload := fmt.Sprintf(`{"error": "%s"}`, errJson)
		createLogBankTransaction.JsonRequest = &reqJosnRequest
		createLogBankTransaction.LogType = &reqAction
		createLogBankTransaction.Status = &reqType
		createLogBankTransaction.JsonPayload = &payload
		_, err := h.accountingService.CreateBankTransactionLog(createLogBankTransaction)
		if err != nil {
			HandleError(c, err)
			return
		}
		HandleError(c, errJson)
		return
	}
	jsonString := string(jsonData)
	insertId, err := h.accountingService.CreateWebhookLog("ACTION", jsonString)
	if err != nil {
		HandleError(c, err)
		return
	}
	var updateReq model.WebhookLogUpdateBody
	updateReq.Status = "success"
	updateReq.JsonPayload = "{}"

	// Do Work After
	if resp.NewStatementList != nil {
		for _, v := range resp.NewStatementList {
			_, err := h.accountingService.CreateBankStatementFromWebhookAndAuto(v, nil)
			if err != nil {
				updateReq.Status = err.Error()
			}
		}
	}
	updateReq.JsonPayload = "{}"

	// Update WebhookLog
	if updateReq.Status == "success" {
		if err = h.accountingService.SetSuccessWebhookLog(*insertId, "{}"); err != nil {
			HandleError(c, err)
			return
		}
	} else {
		if err = h.accountingService.SetFailedWebhookLog(*insertId, updateReq.Status); err != nil {
			HandleError(c, err)
			return
		}
	}
	c.JSON(200, model.Success{Message: "success"})
}

// @Summary WebhookNoti
// @Description เว็บฮุคแบบ POST
// @Tags Accounting - FASTBANK
// @Accept json
// @Produce json
// @Param body body model.ExternalAccountEnableRequest true "body"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/webhooks/noti [post]
func (h accountingController) webhookNoti(c *gin.Context) {

	jsonData, errValidate := io.ReadAll(c.Request.Body)
	if errValidate != nil {
		HandleError(c, errValidate)
		return
	}

	jsonString := string(jsonData)
	_, err := h.accountingService.CreateWebhookLog("NOTI", jsonString)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "success"})
}

// @Summary (createBotaccountConfig) เพิ่ม การตั้งค่าบัญชีธนาคาร ใหม่
// @Description (createBotaccountConfig) เพิ่ม การตั้งค่าบัญชีธนาคาร ใหม่
// @Tags Accounting - FASTBANK
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.BotAccountConfigCreateBody true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/bankaccounts2/config [post]
func (h accountingController) createBotaccountConfig(c *gin.Context) {

	var reqCreate model.BotAccountConfigCreateBody
	if err := c.ShouldBindJSON(&reqCreate); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(reqCreate); err != nil {
		HandleError(c, err)
		return
	}

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))
	// [ADMIN_LOG]
	if err := h.accountingService.LogAdmin("createBotaccountConfig", adminId, reqCreate); err != nil {
		log.Println("createBotaccountConfig.ERROR: ", err)
	}

	if err := h.accountingService.CreateBotaccountConfig(reqCreate); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Created success"})
}

// @Summary (updateBankAccountPriority) อัพเดทลำดับความสำคัญของบัญชีธนาคาร
// @Description (updateBankAccountPriority) อัพเดทลำดับความสำคัญของบัญชีธนาคาร
// @Tags Accounting - Bank Accounts
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.PriorityWithdrawstructRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/bankaccounts/priority-withdraw [put]
func (h accountingController) updateBankAccountPriority(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	body := model.PriorityWithdrawstructRequest{}
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}

	// [ADMIN_LOG]
	if err := h.accountingService.LogAdmin("updateBankAccountPriority", adminId, body); err != nil {
		log.Println("updateBankAccountPriority.ERROR: ", err)
	}

	err := h.accountingService.UpdateBankAccountPriority(body)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "Update success"})
}

// @Summary (getWebBankDepositAccount) ดึงข้อมูลบัญชีธนาคารที่ใช้ในเว็บ
// @Description (getWebBankDepositAccount) ดึงข้อมูลบัญชีธนาคารที่ใช้ในเว็บ
// @Tags Accounting - Bank Accounts
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.WebBankAccountResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /web-accounting/list [get]
func (h accountingController) getWebBankDepositAccount(c *gin.Context) {

	// get userId
	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	data, err := h.accountingService.GetWebBankDepositAccount(userId)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (webBankAccountAdminList) webBankAccountAdminList
// @Description (webBankAccountAdminList) ดึงข้อมูลบัญชีธนาคารที่ใช้ เป็๋น option
// @Tags Accounting - Bank Accounts
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.WebBankAccountResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/bankaccounts/option [get]
func (h accountingController) webBankAccountAdminList(c *gin.Context) {

	data, err := h.accountingService.GetActiveDepositBankAccountList()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary BankAccountWithDrawPriorityValidation
// @Description ตรวจสอบว่าจะถอนเงินได้หรือไม่ และ คำนวณค่าธรรมเนียม
// @Tags Accounting - Bank Accounts
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param amount query float64 true "amount"
// @Success 200 {object} model.PriorityValidation
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/bankaccounts/priority-withdraw/validation [get]
func (h accountingController) bankAccountWithDrawPriorityValidation(c *gin.Context) {

	var amount float64
	if err := c.ShouldBind(&amount); err != nil {
		HandleError(c, err)
		return
	}
	data, err := h.accountingService.BankAccountWithDrawPriorityValidation(200)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// SortBankAccountList(req model.DragSortRequest) error
// @Summary SortBankAccountList
// @Description เรียงลำดับบัญชีธนาคาร
// @Tags Accounting - Bank Accounts
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.DragSortRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/bankaccounts/sort [put]
func (h accountingController) sortBankAccountList(c *gin.Context) {

	body := model.DragSortRequest{}
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.accountingService.SortBankAccountList(body); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Updated success"})
}

// GetBankWithdrawMaxConfig() (*float64, error)
// @Summary GetBankWithdrawMaxConfig
// @Description ดึงข้อมูล จำนวนเงินสูงสุดที่สามารถถอนได้ต่อครั้ง
// @Tags Accounting - Bank Accounts
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} float64
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/config/bank-withdraw-max [get]
func (h accountingController) getBankWithdrawMaxConfig(c *gin.Context) {

	data, err := h.accountingService.GetBankWithdrawMaxConfig()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary (externalMatchWithdrawTransaction) ตรวจสอบการถอนเงิน จาก Statement ธนาคาร
// @Description (externalMatchWithdrawTransaction) ตรวจสอบการถอนเงิน จาก Statement ธนาคาร
// @Tags Accounting - Bank Accounts
// @Accept json
// @Produce json
// @Param body body model.BankStatementCreateBody true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /test/bankaccounts/external-match-withdraw-transaction [post]
func (h accountingController) externalMatchWithdrawTransaction(c *gin.Context) {

	body := model.BankStatementCreateBody{}
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))
	// [ADMIN_LOG]
	if err := h.accountingService.LogAdmin("externalMatchWithdrawTransaction", adminId, body); err != nil {
		log.Println("externalMatchWithdrawTransaction.ERROR: ", err)
	}

	if err := h.accountingService.ExternalMatchWithdrawTransaction(body); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Updated success"})
}

// @Summary (getBankTransactionSummaryGraph2) สรุปภาพรวม - สรุปยอดฝาก-ถอน ทั้งกราฟเส้น และ กราฟแท่ง
// @Description (getBankTransactionSummaryGraph2)
// @Description
// @Description # การกรองด้วยประเภทวัน dateType ทั้งหมด วันนี้ เมื่อวาน เดือนนี้ **ถ้าส่ง fromDate, toDate จะใช้ fromDate, toDate ก่อน
// @Description | dateType | Description |
// @Description | ---------------- | ------------|
// @Description | all *หรือไม่ส่ง* | แสดงทั้งหมด |
// @Description | today | แสดงวันนี้ |
// @Description | yesterday | แสดงเมื่อวาน |
// @Description | this_month | แสดงเดือนนี้ 1-31 |
// @Description | last_month | แสดง 30 วันก่อนหน้า |
// @Description | ---------------- | ------------|
// @Description
// @Description # กรองข้อมูลตามช่วงวันที่ ส่ง fromDate, toDate ในรูปแบบ YYYY-MM-DD (2021-12-31)
// @Description * ถ้าเลือกทั้งหมดไม่ต้องส่งเลย
// @Tags Report - Summary Report
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.BankTransactionGraph2Request true "query"
// @Success 200 {object} model.BankTransactionGraph2Response
// @Failure 400 {object} handler.ErrorResponse
// @Router /summary-report/bank-transaction-graph2 [get]
func (h accountingController) getBankTransactionSummaryGraph2(c *gin.Context) {

	var query model.BankTransactionGraph2Request
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.accountingService.GetBankTransactionSummaryGraph2(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary GetExchangeCurrencyList
// @Description ดึงข้อมูล อัตราแลกเปลี่ยน
// @Tags Accounting - Exchange Currency
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.GetExchangeCurrencyListRequest true "GetExchangeCurrencyListRequest"
// @Success 200 {object} model.GetExchangeCurrencyListResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/exchange-currency/list [get]
func (h accountingController) getExchangeCurrencyList(c *gin.Context) {

	var query model.GetExchangeCurrencyListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.accountingService.GetExchangeCurrencyList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary CreateExchangeRate
// @Description เพิ่ม อัตราแลกเปลี่ยน
// @Tags Accounting - Exchange Currency
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body []model.CreateExchangeRateRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/exchange-rate [post]
func (h accountingController) createExchangeRate(c *gin.Context) {

	var req []model.CreateExchangeRateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))
	// [ADMIN_LOG]
	if err := h.accountingService.LogAdmin("createExchangeRate", adminId, req); err != nil {
		log.Println("createExchangeRate.ERROR: ", err)
	}

	if err := h.accountingService.CreateExchangeRate(req, adminId); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Created success"})
}

// @Summary GetExchangeRateList
// @Description ดึงข้อมูล อัตราแลกเปลี่ยน
// @Tags Accounting - Exchange Currency
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.GetExchangeRateList
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/exchange-rate/list [get]
func (h accountingController) getExchangeRateList(c *gin.Context) {

	data, err := h.accountingService.GetExchangeRateList()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary GetUserExchangeRateList
// @Description ดึงข้อมูล อัตราแลกเปลี่ยน
// @Tags Accounting User - Exchange Currency
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.GetExchangeRateList
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/user/exchange-rate/list [get]
func (h accountingController) getUserExchangeRateList(c *gin.Context) {

	data, err := h.accountingService.GetUserExchangeRateList()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// GetLaosExchangeCurrency() (float64, error)
// @Summary GetLaosExchangeCurrency
// @Description ดึงข้อมูล อัตราแลกเปลี่ยน ลาว
// @Tags Accounting - Exchange Currency
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} float64
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/exchange-currency/laos [get]
func (h accountingController) getLaosExchangeCurrency(c *gin.Context) {

	data, err := h.accountingService.GetLaosExchangeCurrency()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary GetExchangeUpdateLogList
// @Description ดึงข้อมูล ประวัติการอัพเดท อัตราแลกเปลี่ยน
// @Tags Accounting - Exchange Currency
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.GetExchangeUpdateLogListRequest true "GetExchangeUpdateLogListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/exchange-rate/update-log/list [get]
func (h accountingController) getExchangeUpdateLogList(c *gin.Context) {

	var query model.GetExchangeUpdateLogListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.accountingService.GetExchangeUpdateLogList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (testUnmarshal) Test Unmarshal - Set Logic in Service..
// @Description (testUnmarshal)
// @Tags Webhook - Test Unmarshal
// @Accept json
// @Produce json
// @Param body body string true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /test/unmarshal [post]
func (h accountingController) testUnmarshal(c *gin.Context) {

	body := c.Request.Body
	responseData, err := io.ReadAll(body)
	if err != nil {
		log.Println(err)
		HandleError(c, err)
		return
	}

	result, err := h.accountingService.TestUnmarshal(responseData)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, result)
}

// CreateSmsModeFastbankDeposit(body model.CreateSmsModeDepositFastbankRequest) (*model.CreateSmsModeDepositFastbankResponse, error)
// @Summary CreateSmsModeFastbankDeposit
// @Description สร้างข้อมูลการฝากเงิน ด้วย SMS จาก user
// @Tags Banking - Web Bank Transaction v2
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.CreateSmsModeDepositFastbankRequest true "body"
// @Success 201 {object} model.CreateSmsModeDepositFastbankResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/deposit/sms-mode [post]
func (h accountingController) createSmsModeFastbankDeposit(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))
	var body model.CreateSmsModeDepositFastbankRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}

	body.UserId = userId
	data, err := h.accountingService.CreateSmsModeFastbankDeposit(body)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, data)
}

// @Summary (getBankAllowSmsMode)
// @Description (getBankAllowSmsMode) ดึงข้อมูล ธนาคารที่อนุญาตให้ใช้ SMS Mode
// @Tags Accounting - Bank Accounts
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.OptionValueInt
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/bankaccounts/allow-sms-mode [get]
func (h accountingController) getBankAllowSmsMode(c *gin.Context) {
	//  model.BANK_ID_KTB ||  model.BANK_ID_BBL ||  model.BANK_ID_BAY ||  model.BANK_ID_TTB ||  model.BANK_ID_KK ||  model.BANK_ID_KBANK
	var data = []model.OptionValueInt{
		{Value: 1, Label: "KBANK"},
		{Value: 3, Label: "BBL"},
		{Value: 6, Label: "TTB"},
		{Value: 5, Label: "KTB"},
		{Value: 4, Label: "BAY "},
		{Value: 9, Label: "KKP"},
		{Value: 2, Label: "SCB"},
	}
	c.JSON(200, data)
}

// MigrateOldBankAccount() error
// @Summary MigrateOldBankAccount
// @Description ย้ายข้อมูลเก่า จาก ฐานข้อมูลเก่า ไป ใหม่
// @Tags Accounting - Bank Accounts
// @Accept json
// @Produce json
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/bankaccounts/auto-is-show [get]
func (h accountingController) migrateOldBankAccount(c *gin.Context) {

	if err := h.accountingService.MigrateOldBankAccount(); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "Migrate success"})
}

// @Summary (uploadImageToCloudflareBankAccountQr) อัพโหลดไฟล์รูปภาพ
// @Description อัพโหลดไฟล์รูปภาพ ไปยัง Cloudflare
// @Tags Accounting - Bank Accounts
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param file formData file true "file"
// @Success 200 {object} model.FileUploadResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/bankaccounts/upload-image [post]
func (h accountingController) uploadImageToCloudflareBankAccountQr(c *gin.Context) {

	data, err := h.accountingService.UploadImageToS3BankAccountQr(c.Request)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary TotalBankStatementSummary
// @Description สรุปยอดฝาก-ถอน ของบัญชีธนาคาร
// @Tags Accounting - Bank Accounts
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.TotalBankStatementRequest true "TotalBankStatementRequest"
// @Success 200 {object} model.TotalBankTransactionSummaryResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /accounting/bankaccounts/total-statement [get]
func (h accountingController) totalBankStatementSummary(c *gin.Context) {

	var query model.TotalBankStatementRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.accountingService.TotalBankStatementSummary(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}
