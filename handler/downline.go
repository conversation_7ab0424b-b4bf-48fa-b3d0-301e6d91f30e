package handler

import (
	"cybergame-api/middleware"
	"cybergame-api/model"
	"cybergame-api/repository"
	"cybergame-api/service"
	"net/http"
	"os"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"gorm.io/gorm"
)

type downlineController struct {
	downlineService service.DownlineService
}

func newDownlineController(
	downlineService service.DownlineService,
) downlineController {
	return downlineController{downlineService}
}

func DownlineController(r *gin.RouterGroup, db *gorm.DB) {

	singleSession := middleware.SingleSession(db)

	repo := repository.NewDownlineRepository(db)
	service := service.NewDownlineService(repo)
	handler := newDownlineController(service)

	downlineProductRoute := r.Group("/downlines", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	downlineProductRoute.GET("/web-info", handler.getWebInfo)
	downlineProductRoute.GET("/upload-info", handler.UpdateDownlineBalance)
	downlineProductRoute.GET("/payment-info", handler.getPaymentInfo)
	downlineProductRoute.POST("/submit-invoice", handler.submitInvoice)
	downlineProductRoute.POST("/submit-scammer", handler.submitScammer)
	downlineProductRoute.POST("/update-invoice", handler.UpdatePendingInvoiceList)

	fromRemoteRoute := r.Group("/from-remote")
	fromRemoteRoute.POST("/create-invoice", handler.remoteCreateInvoice)

	downlineWebRoute := r.Group("/web")
	downlineWebRoute.GET("/web-info", handler.getPublicWebInfo)

	scammerRoute := r.Group("/scammers")
	scammerRoute.GET("/scammer-detail", handler.getMasterScammerList)

	lineContactRoute := r.Group("/line-contact")
	lineContactRoute.GET("", handler.getMasterLineContact)
}

// @Summary (getWebInfo) TEST FOR ADMIN CHECK
// @Description (getWebInfo) TEST FOR ADMIN CHECK
// @Tags Downlines - Request
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.WebStatusResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /downlines/web-info [get]
func (h downlineController) getWebInfo(c *gin.Context) {

	data, err := h.downlineService.GetWebInfo()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (UpdateDownlineBalance) TEST FOR ADMIN CHECK
// @Description (UpdateDownlineBalance) TEST FOR ADMIN CHECK
// @Tags Downlines - Request
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.WebStatusResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /downlines/upload-info [get]
func (h downlineController) UpdateDownlineBalance(c *gin.Context) {

	data, err := h.downlineService.UpdateDownlineBalance()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (getPaymentInfo) TEST FOR ADMIN CHECK
// @Description (getPaymentInfo) TEST FOR ADMIN CHECK
// @Tags Downlines - Request
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.DownlinePaymentInfo
// @Failure 400 {object} handler.ErrorResponse
// @Router /downlines/payment-info [get]
func (h downlineController) getPaymentInfo(c *gin.Context) {

	data, err := h.downlineService.GetPaymentInfo()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (submitInvoice) TEST FOR ADMIN CHECK
// @Description (submitInvoice) TEST FOR ADMIN CHECK
// @Tags Downlines - Request
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.GetByIdRequest true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /downlines/submit-invoice [post]
func (h downlineController) submitInvoice(c *gin.Context) {

	var body model.GetByIdRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	insertId, err := h.downlineService.SubmitInvoice(body.Id)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.SuccessWithId{Message: "success", Id: *insertId})
}

// @Summary (UpdatePendingInvoiceList) TEST FOR ADMIN CHECK
// @Description (UpdatePendingInvoiceList) TEST FOR ADMIN CHECK
// @Tags Downlines - Request
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /downlines/update-invoice [post]
func (h downlineController) UpdatePendingInvoiceList(c *gin.Context) {

	err := h.downlineService.UpdatePendingInvoiceList()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "success"})
}

// @Summary (SubmitScammer) TEST FOR ADMIN CHECK
// @Description (SubmitScammer) TEST FOR ADMIN CHECK
// @Tags Downlines - Request
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.GetByIdRequest true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /downlines/submit-scammer [post]
func (h downlineController) submitScammer(c *gin.Context) {

	var body model.GetByIdRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	insertId, err := h.downlineService.SubmitScammer(body.Id)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.SuccessWithId{Message: "success", Id: *insertId})
}

// @Summary (remoteCreateInvoice) MASTER use this to create invoice
// @Description (remoteCreateInvoice)
// @Tags Downlines - Request
// @Accept json
// @Produce json
// @Param _ query model.DownlineWebInfoRequest true "query"
// @Param body body model.DownlineInvoiceSubmitRequest true "body"
// @Success 201 {object} model.DownlineInvoiceSubmitResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /from-remote/create-invoice [post]
func (h downlineController) remoteCreateInvoice(c *gin.Context) {

	var query model.DownlineWebInfoRequest
	if err := c.ShouldBindQuery(&query); err != nil {
		c.JSON(http.StatusBadRequest, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	agentApiKey := os.Getenv("MASTER_WEB_KEY")
	if agentApiKey != query.ApiKey {
		c.JSON(http.StatusBadRequest, gin.H{"error": "INVALID_API_KEY"})
		return
	}

	var body model.DownlineInvoiceSubmitRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	resp, err := h.downlineService.CreateInvoiceFromRemote(body)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, resp)
}

// @Summary (getPublicWebInfo)
// @Description (getPublicWebInfo)
// @Tags Downlines - Public
// @Accept json
// @Produce json
// @Success 200 {object} model.PublicWebInfo
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/web-info [get]
func (h downlineController) getPublicWebInfo(c *gin.Context) {

	data, err := h.downlineService.GetPublicWebInfo()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (getMasterScammerList)
// @Description (getMasterScammerList)
// @Tags Scammers
// @Accept json
// @Produce json
// @Param _ query model.DownlineScammerequest true "query"
// @Success 200 {object} model.DownlineScammerPaginationResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /scammers/scammer-detail [get]
func (h downlineController) getMasterScammerList(c *gin.Context) {

	var query model.DownlineScammerequest
	if err := c.ShouldBindQuery(&query); err != nil {
		c.JSON(http.StatusBadRequest, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.downlineService.GetMasterScammerList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithPagination{List: data.List, Total: data.Total})
}

// @Summary (getMasterLineContact) ดึงข้อมูล line contact ของ master
// @Description (getMasterLineContact)
// @Tags Invoice
// @Accept json
// @Produce json
// @Success 200 {object} model.DownlineLineContactResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /line-contact [get]
func (h downlineController) getMasterLineContact(c *gin.Context) {

	data, err := h.downlineService.GetMasterLineContact()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}
