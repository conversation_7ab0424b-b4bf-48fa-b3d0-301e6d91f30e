package handler

import (
	"cybergame-api/helper"
	"cybergame-api/middleware"
	"cybergame-api/model"
	"cybergame-api/repository"
	"cybergame-api/service"
	"log"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"gorm.io/gorm"
)

type accountMoveController struct {
	accountMoveService service.AccountMoveService
}

func newAccountMoveController(
	accountMoveService service.AccountMoveService,
) accountMoveController {
	return accountMoveController{accountMoveService}
}

func AccountMoveController(r *gin.RouterGroup, db *gorm.DB) {

	// REF
	actionService := service.NewAdminActionService(repository.NewAdminActionRepository(db))
	userRepo := repository.NewUserRepository(db)
	repoAccounting := repository.NewAccountingRepository(db)
	repoAgentConnect := repository.NewAgentConnectRepository(db)
	perRepo := repository.NewPermissionRepository(db)
	groupRepo := repository.NewGroupRepository(db)
	otpRepo := repository.NewOtpRepository(db)
	agentInfoRepo := repository.NewAgentInfoRepository(db)
	promotionWebRepo := repository.NewPromotionWebRepository(db)
	recommendRepo := repository.NewRecommendRepository(db)
	afRepo := repository.NewAffiliateRepository(db)
	serviceRepo := repository.NewNotificationRepository(db)
	serviceNoti := service.NewNotificationService(serviceRepo)
	afService := service.NewAffiliateService(db, afRepo, repoAgentConnect, serviceNoti)
	notiRepo := repository.NewNotificationRepository(db)
	notiService := service.NewNotificationService(notiRepo)
	userService := service.NewUserService(
		userRepo,
		db,
		perRepo,
		groupRepo,
		otpRepo,
		agentInfoRepo,
		recommendRepo,
		afRepo,
		notiService,
		actionService,
	)
	gameService := service.NewGameService(agentInfoRepo)
	agRepo := repository.NewAgentConnectRepository(db)
	allianceRepo := repository.NewAllianceRepository(db)
	alService := service.NewAllianceService(allianceRepo, db, agRepo)
	activityLusckyWheelRepo := repository.NewActivityLuckyWheelRepository(db)
	activityLuckyWheelService := service.NewActivityLuckyWheelService(activityLusckyWheelRepo, db, serviceNoti)
	promotionWebService := service.NewPromotionWebService(promotionWebRepo, db, serviceNoti, userService)
	accountService := service.NewAccountingService(repoAccounting, userService, gameService, repoAgentConnect, notiService, afService, alService, actionService, activityLuckyWheelService, promotionWebService, db)
	adminActionRepo := repository.NewAdminActionRepository(db)
	adminActionService := service.NewAdminActionService(adminActionRepo)
	// CORE
	repo := repository.NewAccountMoveRepository(db)
	service := service.NewAccountMoveService(db, repo, accountService, adminActionService)
	handler := newAccountMoveController(service)

	role := middleware.Role(db)
	singleSession := middleware.SingleSession(db)

	accountMoveOptionRoute := r.Group("/account-move/options", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	accountMoveOptionRoute.GET("/from-account", role.CheckPermission([]string{"bank"}), handler.getFromAccountOptions)
	accountMoveOptionRoute.GET("/to-account", role.CheckPermission([]string{"bank"}), handler.getToAccountOptions)
	accountMoveOptionRoute.GET("/trans-status", role.CheckPermission([]string{"bank"}), handler.getTransactionStatusOptions)

	accountMoveTransactionRoute := r.Group("/account-move", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	accountMoveTransactionRoute.GET("/accounts/list", role.CheckPermission([]string{"bank"}), handler.getAccountMoveAccountList)
	accountMoveTransactionRoute.GET("/trans/list", role.CheckPermission([]string{"bank"}), handler.getAccountMoveTransactionList)
	accountMoveTransactionRoute.GET("/trans/count-status", role.CheckPermission([]string{"bank"}), handler.getAccountMoveTransactionStatusCount)
	accountMoveTransactionRoute.GET("/trans/detail/:id", role.CheckPermission([]string{"bank"}), handler.getAccountMoveTransactionById)
	accountMoveTransactionRoute.POST("/trans/create", role.CheckPermission([]string{"bank"}), handler.createAccountMoveTransaction)
	accountMoveTransactionRoute.GET("/statements/list", role.CheckPermission([]string{"bank", "deposit_list", "withdraw_list"}), handler.getAccountMoveStatementList)
	accountMoveTransactionRoute.POST("/statements/create-webhook", role.CheckPermission([]string{"bank", "deposit_list", "withdraw_list"}), handler.hookFastBankStatement)
	accountMoveTransactionRoute.GET("/logs/list", role.CheckPermission([]string{"bank", "deposit_list", "withdraw_list"}), handler.getAccountMoveLogList)

}

// @Summary (getFromAccountOptions) ข้อมูลตัวเลือกบัญชีธนาคารต้นทาง
// @Description (getFromAccountOptions) ข้อมูลตัวเลือกบัญชีธนาคารต้นทาง
// @Tags Move Money - AccountMove Transaction
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} []model.SelectOptions
// @Failure 400 {object} handler.ErrorResponse
// @Router /account-move/options/from-account [get]
func (h accountMoveController) getFromAccountOptions(c *gin.Context) {

	data, err := h.accountMoveService.GetFromAccountOptions()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (getToAccountOptions) ข้อมูลตัวเลือกบัญชีธนาคารปลายทาง
// @Description (getToAccountOptions) ข้อมูลตัวเลือกบัญชีธนาคารปลายทาง
// @Tags Move Money - AccountMove Transaction
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} []model.SelectOptions
// @Failure 400 {object} handler.ErrorResponse
// @Router /account-move/options/to-account [get]
func (h accountMoveController) getToAccountOptions(c *gin.Context) {

	data, err := h.accountMoveService.GetToAccountOptions()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (getTransactionStatusOptions) ข้อมูลตัวเลือกสถานะการโยกย้ายเงิน
// @Description (getTransactionStatusOptions) ข้อมูลตัวเลือกสถานะการโยกย้ายเงิน
// @Tags Move Money - AccountMove Transaction
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} []model.SelectOptions
// @Failure 400 {object} handler.ErrorResponse
// @Router /account-move/options/trans-status [get]
func (h accountMoveController) getTransactionStatusOptions(c *gin.Context) {

	data, err := h.accountMoveService.GetTransactionStatusOptions()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (getAccountMoveAccountList) ข้อมูลรายการบัญชีธนาคารที่ใช้โยกย้ายเงินได้
// @Description (getAccountMoveAccountList) ข้อมูลรายการบัญชีธนาคารที่ใช้โยกย้ายเงินได้
// @Tags Move Money - AccountMove Transaction
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.AccountMoveAccountListRequest true "AccountMoveAccountListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /account-move/accounts/list [get]
func (h accountMoveController) getAccountMoveAccountList(c *gin.Context) {

	var query model.AccountMoveAccountListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.accountMoveService.GetAccountMoveAccountList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithPagination{List: data.List, Total: data.Total})
}

// @Summary (getAccountMoveTransactionStatusCount) นับจำนวน รายการโยกย้ายเงิน ตามสถานะ
// @Description (getAccountMoveTransactionStatusCount) นับจำนวน รายการโยกย้ายเงิน ตามสถานะ
// @Tags Move Money - AccountMove Transaction
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.AccountMoveTransactionListRequest true "AccountMoveTransactionListRequest"
// @Success 200 {object} model.AccountMoveTransactionStatusCount
// @Failure 400 {object} handler.ErrorResponse
// @Router /account-move/trans/count-status [get]
func (h accountMoveController) getAccountMoveTransactionStatusCount(c *gin.Context) {

	var query model.AccountMoveTransactionListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.accountMoveService.GetAccountMoveTransactionStatusCount(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (getAccountMoveTransactionList) ข้อมูลรายการโยกย้ายเงิน
// @Description (getAccountMoveTransactionList) ข้อมูลรายการโยกย้ายเงิน
// @Tags Move Money - AccountMove Transaction
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.AccountMoveTransactionListRequest true "AccountMoveTransactionListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /account-move/trans/list [get]
func (h accountMoveController) getAccountMoveTransactionList(c *gin.Context) {

	var query model.AccountMoveTransactionListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.accountMoveService.GetAccountMoveTransactionList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithPagination{List: data.List, Total: data.Total})
}

// @Summary (getAccountMoveTransactionById) ข้อมูลรายการโยกย้ายเงินด้วยไอดี
// @Description (getAccountMoveTransactionById) ข้อมูลรายการโยกย้ายเงินด้วยไอดี
// @Tags Move Money - AccountMove Transaction
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /account-move/trans/detail/{id} [get]
func (h accountMoveController) getAccountMoveTransactionById(c *gin.Context) {

	var query model.GetByIdRequest
	if err := c.ShouldBindUri(&query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.accountMoveService.GetAccountMoveTransactionById(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithData{Message: "success", Data: data})
}

// @Summary (createAccountMoveTransaction) สร้างรายการโยกย้ายเงิน
// @Description (createAccountMoveTransaction) สร้างรายการโยกย้ายเงิน
// @Tags Move Money - AccountMove Transaction
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.AccountMoveTransactionCreateRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /account-move/trans/create [post]
func (h accountMoveController) createAccountMoveTransaction(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	var body model.AccountMoveTransactionCreateRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}
	body.AdminId = adminId

	// [ADMIN_LOG]
	if err := h.accountMoveService.LogAdmin("createAccountMoveTransaction", adminId, body); err != nil {
		log.Println("getFromAccountOptions.ERROR: ", err)
	}

	if _, err := h.accountMoveService.CreateAccountMoveTransaction(body); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Created success"})
}

// @Summary (getAccountMoveStatementList) ข้อมูลรายการ Transaction ของ FastBank
// @Description (getAccountMoveStatementList) ข้อมูลรายการ Transaction ของ FastBank
// @Tags Move Money - AccountMove Statement
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.AccountMoveStatementListRequest true "AccountMoveStatementListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /account-move/statements/list [get]
func (h accountMoveController) getAccountMoveStatementList(c *gin.Context) {

	var query model.AccountMoveStatementListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.accountMoveService.GetAccountMoveStatementList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithPagination{List: data.List, Total: data.Total})
}

// @Summary (hookFastBankStatement) สั่งยิง Webhook จาก Statement
// @Description (hookFastBankStatement) สั่งยิง Webhook จาก Statement
// @Tags Move Money - AccountMove Statement
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.AccountMoveWebhookCreateRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /account-move/statements/create-webhook [post]
func (h accountMoveController) hookFastBankStatement(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	var body model.AccountMoveWebhookCreateRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}
	body.AdminId = adminId

	// [ADMIN_LOG]
	if err := h.accountMoveService.LogAdmin("hookFastBankStatement", adminId, body); err != nil {
		log.Println("getFromAccountOptions.ERROR: ", err)
	}

	if _, err := h.accountMoveService.HookFastBankStatement(body); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Created success"})
}

// @Summary (getAccountMoveLogList) ข้อมูลรายการ Log ของ FastBank
// @Description (getAccountMoveLogList) ข้อมูลรายการ Log ของ FastBank
// @Tags Move Money - AccountMove Log
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.AccountMoveLogListRequest true "AccountMoveLogListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /account-move/logs/list [get]
func (h accountMoveController) getAccountMoveLogList(c *gin.Context) {

	var query model.AccountMoveLogListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.accountMoveService.GetAccountMoveLogList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithPagination{List: data.List, Total: data.Total})
}
