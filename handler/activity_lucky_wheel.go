package handler

import (
	"cybergame-api/helper"
	"cybergame-api/middleware"
	"cybergame-api/model"
	"cybergame-api/repository"
	"cybergame-api/service"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"gorm.io/gorm"
)

type activityLuckyWheelController struct {
	activityLuckyWheelService service.ActivityLuckyWheelService
}

func newActivityLuckyWheelController(
	activityLuckyWheelService service.ActivityLuckyWheelService,
) activityLuckyWheelController {
	return activityLuckyWheelController{activityLuckyWheelService}
}
func ActivityLuckyWheelController(r *gin.RouterGroup, db *gorm.DB) {

	repo := repository.NewActivityLuckyWheelRepository(db)
	serviceRepo := repository.NewNotificationRepository(db)
	serviceNoti := service.NewNotificationService(serviceRepo)

	service := service.NewActivityLuckyWheelService(repo, db, serviceNoti)
	handler := newActivityLuckyWheelController(service)
	role := middleware.Role(db)
	singleSession := middleware.SingleSession(db)

	settingRoute := r.Group("/activity-lucky-wheel/setting", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	settingRoute.GET("", role.CheckPermission([]string{"activity_manage"}), handler.getActivityLuckyWheelSetting)
	settingRoute.PATCH("", role.CheckPermission([]string{"activity_manage"}), handler.updateActivityLuckyWheelSetting)
	settingRoute.GET("/option/amount-spin", handler.getActivityLuckyWheelSettingAmountSpin)
	settingRoute.GET("/image-spin/:id", handler.getActivityLuckyWheelSettingImageSpin)

	luckyRoute := r.Group("/activity-lucky-wheel", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	luckyRoute.GET("/list", role.CheckPermission([]string{"activity_manage"}), handler.getActivityLuckyWheelList)
	luckyRoute.PATCH("", role.CheckPermission([]string{"activity_manage"}), handler.updateActivityLuckyWheel)
	luckyRoute.POST("/test/round-user", handler.createRoundActivityLuckyWheel)
	luckyRoute.GET("/report/list", handler.getReportLuckyWheelRoundList)
	luckyRoute.GET("/report/detail", handler.getReportLuckyWheelRoundByUserId)
	luckyRoute.POST("/upload/image", handler.LuckyWheelUploadImageToCloudFlare)

	optionsRoute := r.Group("/activity-lucky-wheel/options", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	optionsRoute.GET("/condition", role.CheckPermission([]string{"activity_manage"}), handler.getConditionType)

	webRoute := r.Group("/web/activity-lucky-wheel", middleware.AuthorizeUser, singleSession.SingleUserSession())
	webRoute.GET("/list", handler.getActivityLuckyWheelUserList)
	webRoute.GET("/round-user", handler.getActivityLuckyWheelRoundUser)
	webRoute.POST("/play", handler.roundActivityLuckyWheelUser)

}

// @Summary (getActivityLuckyWheelSetting) ดึงข้อมูลการตั้งค่าจัดการกิจกรรมกงล้อนำโชค
// @Description (getActivityLuckyWheelSetting) ดึงข้อมูลการตั้งค่าจัดการกิจกรรมกงล้อนำโชค
// @Tags Activity - Lucky Wheel Setting
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /activity-lucky-wheel/setting [get]
func (h activityLuckyWheelController) getActivityLuckyWheelSetting(c *gin.Context) {

	data, err := h.activityLuckyWheelService.GetActivityLuckyWheelSetting()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithData{Message: "Success", Data: data})
}

// @Summary (updateActivityLuckyWheelSetting) อัพเดทการตั้งค่าจัดการกิจกรรมกงล้อนำโชค
// @Description (updateActivityLuckyWheelSetting) อัพเดทการตั้งค่าจัดการกิจกรรมกงล้อนำโชค
// @Tags Activity - Lucky Wheel Setting
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.ActivityLuckyWheelSettingUpdateRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /activity-lucky-wheel/setting [patch]
func (h activityLuckyWheelController) updateActivityLuckyWheelSetting(c *gin.Context) {

	body := model.ActivityLuckyWheelSettingUpdateRequest{}
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.activityLuckyWheelService.UpdateActivityLuckyWheelSetting(body); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Updated success"})
}

// @Summary (getActivityLuckyWheelList) ดึงข้อมูลกงล้อนำโชคทั้งหมด
// @Description (getActivityLuckyWheelList) ดึงข้อมูลกงล้อนำโชคทั้งหมด
// @Tags Activity - Lucky Wheel
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /activity-lucky-wheel/list [get]
func (h activityLuckyWheelController) getActivityLuckyWheelList(c *gin.Context) {

	list, err := h.activityLuckyWheelService.GetActivityLuckyWheelList()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithList{Message: "Success", List: list})
}

// @Summary (updateActivityLuckyWheel) อัพเดทกงล้อนำโชคทั้งหมด
// @Description (updateActivityLuckyWheel) อัพเดทกงล้อนำโชคทั้งหมด
// @Tags Activity - Lucky Wheel
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.ActivityLuckyWheelRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /activity-lucky-wheel [patch]
func (h activityLuckyWheelController) updateActivityLuckyWheel(c *gin.Context) {
	body := model.ActivityLuckyWheelRequest{}

	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}

	err := h.activityLuckyWheelService.UpdateActivityLuckyWheel(body)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Update success"})
}

// @Summary (createRoundActivityLuckyWheel) สร้างรอบหมุนกงล้อนำโชค user
// @Description (createRoundActivityLuckyWheel) สร้างรอบหมุนกงล้อนำโชค user
// @Tags Activity - Lucky Test Create Round
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.ActivityLuckyWheelRoundUserRequest true "body"
// @Success 201 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /activity-lucky-wheel/test/round-user [post]
func (h activityLuckyWheelController) createRoundActivityLuckyWheel(c *gin.Context) {
	var body model.ActivityLuckyWheelRoundUserRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.activityLuckyWheelService.CreateRoundActivityLuckyWheel(body); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Created success"})
}

// @Summary (getReportLuckyWheelRoundList) รายงานกงล้อนำโชค
// @Description (getReportLuckyWheelRoundList) รายงานกงล้อนำโชค
// @Tags Activity - Lucky Wheel
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.LuckyweelSummaryListRequest true "LuckyweelSummaryListRequest"
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /activity-lucky-wheel/report/list [get]
func (h activityLuckyWheelController) getReportLuckyWheelRoundList(c *gin.Context) {

	var query model.LuckyweelSummaryListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.activityLuckyWheelService.GetReportLuckyWheelRoundList(query)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary (getReportLuckyWheelRoundByUserId) รายละเอียดรายงานกงล้อนำโชค
// @Description (getReportLuckyWheelRoundByUserId) รายละเอียดรายงานกงล้อนำโชค
// @Tags Activity - Lucky Wheel
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.LuckyweelSummaryByUserIdRequest true "LuckyweelSummaryByUserIdRequest"
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /activity-lucky-wheel/report/detail [get]
func (h activityLuckyWheelController) getReportLuckyWheelRoundByUserId(c *gin.Context) {

	var query model.LuckyweelSummaryByUserIdRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.activityLuckyWheelService.GetReportLuckyWheelRoundByUserId(query)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary (getConditionList) ดึงข้อมูลเงื่อนไขที่สามารถหมุนกงล้อได้
// @Description (getConditionList) ดึงข้อมูลเงื่อนไขที่สามารถหมุนกงล้อได้
// @Tags Activity - Options
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.ConditionTypeResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /activity-lucky-wheel/options/condition [get]
func (h activityLuckyWheelController) getConditionType(c *gin.Context) {
	list, err := h.activityLuckyWheelService.GetConditionType()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, list)
}

// @Summary (getActivityLuckyWheelUserList) ดึงข้อมูลกงล้อนำโชคทั้งหมด
// @Description (getActivityLuckyWheelUserList) ดึงข้อมูลกงล้อนำโชคทั้งหมด
// @Tags Web Activity - Lucky
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/activity-lucky-wheel/list [get]
func (h activityLuckyWheelController) getActivityLuckyWheelUserList(c *gin.Context) {

	list, err := h.activityLuckyWheelService.GetActivityLuckyWheelUserList()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithList{Message: "Success", List: list})
}

// @Summary (getActivityLuckyWheelRoundUser) ดึงข้อมูลรอบหมุนกงล้อนำโชค user
// @Description (getActivityLuckyWheelRoundUser) ดึงข้อมูลรอบหมุนกงล้อนำโชค user
// @Tags Web Activity - Lucky
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/activity-lucky-wheel/round-user [get]
func (h activityLuckyWheelController) getActivityLuckyWheelRoundUser(c *gin.Context) {
	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))
	list, err := h.activityLuckyWheelService.GetActivityLuckyWheelRoundUser(userId)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, model.SuccessWithData{Message: "Success", Data: list})
}

// RoundActivityLuckyWheelUser(userId int64) (*int64, error)
// @Summary (roundActivityLuckyWheelUser) หมุนกงล้อนำโชค user
// @Description (roundActivityLuckyWheelUser) หมุนกงล้อนำโชค user
// @Tags Web Activity - Lucky
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/activity-lucky-wheel/play [post]
func (h activityLuckyWheelController) roundActivityLuckyWheelUser(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	list, err := h.activityLuckyWheelService.RoundActivityLuckyWheelUser(userId)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, model.SuccessWithData{Message: "Success", Data: list})
}

//	GetActivityLuckyWheelSettingAmountSpin() ([]model.SelectOptions, error)
//
// @Summary (getActivityLuckyWheelSettingAmountSpin) ดึงข้อมูลจำนวนการหมุนกงล้อ
// @Description (getActivityLuckyWheelSettingAmountSpin) ดึงข้อมูลจำนวนการหมุนกงล้อ
// @Tags Web Activity - Lucky
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /activity-lucky-wheel/setting/option/amount-spin [get]
func (h activityLuckyWheelController) getActivityLuckyWheelSettingAmountSpin(c *gin.Context) {

	list, err := h.activityLuckyWheelService.GetActivityLuckyWheelSettingAmountSpin()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, list)
}

// @Summary (getActivityLuckyWheelSettingImageSpin) ดึงข้อมูลภาพการหมุนกงล้อ
// @Description (getActivityLuckyWheelSettingImageSpin) ดึงข้อมูลภาพการหมุนกงล้อ
// @Tags Web Activity - Lucky
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /activity-lucky-wheel/setting/image-spin/{id} [get]
func (h activityLuckyWheelController) getActivityLuckyWheelSettingImageSpin(c *gin.Context) {

	var req model.GetByIdRequest
	if err := c.ShouldBindUri(&req); err != nil {
		HandleError(c, err)
		return
	}

	list, err := h.activityLuckyWheelService.GetActivityLuckyWheelSettingImageSpin(req.Id)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, list)
}

// @Summary (uploadImageToCloudFlare) อัพโหลดไฟล์ CloudFlare แบบ feature
// @Description (uploadImageToCloudFlare) Upload File CloudFlare
// @Tags WebPopups - WebPopup
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param file formData file true "file"
// @Success 200 {object} model.FileUploadResponse
// @Failure 400 {object} ErrorResponse
// @Router /activity-lucky-wheel/upload/image [post]
func (h activityLuckyWheelController) LuckyWheelUploadImageToCloudFlare(c *gin.Context) {

	data, err := h.activityLuckyWheelService.UploadImage(c.Request)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}
