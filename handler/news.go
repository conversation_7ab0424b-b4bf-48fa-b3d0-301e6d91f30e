package handler

import (
	"cybergame-api/model"
	"cybergame-api/service"
	"strconv"

	"cybergame-api/repository"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

type newsController struct {
	NewsService service.NewsService
}

func newNewsController(
	NewsService service.NewsService,
) newsController {
	return newsController{NewsService}
}

func NewsController(r *gin.RouterGroup, db *gorm.DB, rdb *redis.Client) {

	repo := repository.NewNewsRepository(db)
	fileRepo := repository.NewFileRepository(db)
	cacheRepo := repository.NewCacheRepository(rdb)
	service := service.NewNewsService(repo, fileRepo, cacheRepo)
	handler := newNewsController(service)

	news := r.Group("/news")
	news.GET("list", handler.getNewsList)
	news.GET("highlight", handler.getNewsHighlightList)
	news.GET(":newsId", handler.getNewsSeasonList)
	news.POST("", handler.createNews)
	news.POST("sort", handler.updateNewsSortOrder)
	news.PUT(":newsId", handler.updateNews)
	news.DELETE(":newsId", handler.deleteNews)
}

// @Summary ดึงข้อมูลข่าวสาร
// @Description Get News List
// @Tags News
// @Accept  json
// @Security BearerAuth
// @Produce  json
// @Param query query model.NewsQuery true "query"
// @Success 200 {object} model.NewsListResponse
// @Failure 400 {object} ErrorResponse
// @Router /news/list [get]
func (h newsController) getNewsList(c *gin.Context) {

	query := model.NewsQuery{}

	if err := c.ShouldBindQuery(&query); err != nil {
		HandleError(c, err)
		return
	}

	result, err := h.NewsService.NewsGetList(query)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, result)
}

// @Summary ดึงข้อมูลข่าวไฮไลท์
// @Description Get News Highlight List
// @Tags News
// @Accept  json
// @Security BearerAuth
// @Produce  json
// @Success 200 {object} model.NewsListResponse
// @Failure 400 {object} ErrorResponse
// @Router /news/highlight [get]
func (h newsController) getNewsHighlightList(c *gin.Context) {

	result, err := h.NewsService.NewsGetHighlightList()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, result)
}

// @Summary ดึงข้อมูลข่าวสาร
// @Description Get News Detail
// @Tags News
// @Accept  json
// @Security BearerAuth
// @Produce  json
// @Param newsId path int true "newsId"
// @Success 200 {object} model.NewsDetail
// @Failure 400 {object} ErrorResponse
// @Router /news/{newsId} [get]
func (h newsController) getNewsSeasonList(c *gin.Context) {

	newsId := c.Param("newsId")
	toInt64, err := strconv.ParseInt(newsId, 10, 64)
	if err != nil {
		HandleError(c, err)
		return
	}

	result, err := h.NewsService.NewsDetail(toInt64)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, result)
}

// @Summary สร้างข้อมูลข่าวสาร
// @Description Create News
// @Tags News
// @Accept  json
// @Security BearerAuth
// @Produce  json
// @Param body body model.NewsBody true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} ErrorResponse
// @Router /news [post]
func (h newsController) createNews(c *gin.Context) {

	body := model.NewsBody{}

	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.NewsService.NewsCreate(body); err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(201, model.Success{Message: "Created success"})
}

// @Summary แก้ไขข้อมูลข่าวสาร
// @Description Update News
// @Tags News
// @Accept  json
// @Security BearerAuth
// @Produce  json
// @Param newsId path int true "newsId"
// @Param body body model.NewsUpdateBody true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} ErrorResponse
// @Router /news/{newsId} [put]
func (h newsController) updateNews(c *gin.Context) {

	newsId := c.Param("newsId")
	toInt64, err := strconv.ParseInt(newsId, 10, 64)
	if err != nil {
		HandleError(c, err)
		return
	}

	body := model.NewsUpdateBody{}

	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.NewsService.NewsUpdate(toInt64, body); err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(201, model.Success{Message: "Updated success"})
}

// @Summary ลบข้อมูลข่าวสาร
// @Description Delete News
// @Tags News
// @Accept  json
// @Security BearerAuth
// @Produce  json
// @Param newsId path int true "newsId"
// @Success 201 {object} model.Success
// @Failure 400 {object} ErrorResponse
// @Router /news/{newsId} [delete]
func (h newsController) deleteNews(c *gin.Context) {

	newsId := c.Param("newsId")
	toInt64, err := strconv.ParseInt(newsId, 10, 64)
	if err != nil {
		HandleError(c, err)
		return
	}

	if err := h.NewsService.NewsDelete(toInt64); err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(201, model.Success{Message: "Deleted success"})
}

// @Summary แก้ไขลำดับข่าว
// @Description Update News Sort Order
// @Tags News
// @Accept  json
// @Security BearerAuth
// @Produce  json
// @Param body body model.NewsSortBody true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} ErrorResponse
// @Router /news/sort [post]
func (h newsController) updateNewsSortOrder(c *gin.Context) {

	body := model.NewsSortBody{}

	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.NewsService.NewsUpdateSortOrder(body); err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(201, model.Success{Message: "Updated success"})
}
