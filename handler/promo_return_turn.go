package handler

import (
	"cybergame-api/helper"
	"cybergame-api/middleware"
	"cybergame-api/model"
	"cybergame-api/repository"
	"cybergame-api/service"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"gorm.io/gorm"
)

type promotionReturnTurnController struct {
	promotionReturnTurnService service.PromotionReturnTurnService
}

func newPromotionReturnTurnController(
	promotionReturnTurnService service.PromotionReturnTurnService,
) promotionReturnTurnController {
	return promotionReturnTurnController{promotionReturnTurnService}
}
func PromotionReturnTurnController(r *gin.RouterGroup, db *gorm.DB) {

	activityLusckyWheelRepo := repository.NewActivityLuckyWheelRepository(db)
	serviceRepo := repository.NewNotificationRepository(db)
	serviceNoti := service.NewNotificationService(serviceRepo)
	activityLuckyWheelService := service.NewActivityLuckyWheelService(activityLusckyWheelRepo, db, serviceNoti)
	serviceReturnTurn := service.NewPromotionReturnTurnService(repository.NewPromotionReturnTurnRepository(db), db, activityLuckyWheelService, serviceNoti)
	handler := newPromotionReturnTurnController(serviceReturnTurn)

	role := middleware.Role(db)
	singleSession := middleware.SingleSession(db)

	// 1.SETTING
	settingRoute := r.Group("/promo-return-turn/setting", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	settingRoute.GET("", role.CheckPermission([]string{"activity_manage"}), handler.getReturnTurnSetting)
	settingRoute.PATCH("", role.CheckPermission([]string{"activity_manage"}), handler.updateReturnTurnSetting)
	// 2.WEB
	webRoute := r.Group("/web/promo-return-turn", middleware.AuthorizeUser, singleSession.SingleUserSession())
	webRoute.GET("/current", handler.getUserCurrentReturnDetail)
	webRoute.POST("/take", handler.takeUserReturnAmount)
	webRoute.GET("/list", handler.getUserReturnTurnHistoryList)

}

// @Summary (getReturnTurnSetting) ดึงข้อมูลการตั้งค่าโปรโมชั่นคืนยอดเสีย
// @Description (getReturnTurnSetting) ดึงข้อมูลการตั้งค่าโปรโมชั่นคืนยอดเสีย
// @Tags Promotion - Return Turn Setting
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.PromotionReturnTurnSettingResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /promo-return-turn/setting [get]
func (h promotionReturnTurnController) getReturnTurnSetting(c *gin.Context) {

	data, err := h.promotionReturnTurnService.GetReturnTurnSetting()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (updateReturnTurnSetting) อัพเดทการตั้งค่าโปรโมชั่นคืนยอดเสีย
// @Description (updateReturnTurnSetting) อัพเดทการตั้งค่าโปรโมชั่นคืนยอดเสีย
// @Tags Promotion - Return Turn Setting
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.PromotionReturnTurnSettingUpdateRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /promo-return-turn/setting [patch]
func (h promotionReturnTurnController) updateReturnTurnSetting(c *gin.Context) {

	body := model.PromotionReturnTurnSettingUpdateRequest{}
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.promotionReturnTurnService.UpdateReturnTurnSetting(body); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Updated success"})
}

// @Summary (getUserCurrentReturnDetail) ผู้ใช้ดูยอดคืนเงินปัจจุบันของตัวเอง ตัดยอดวันนี้ 12.30 รับเงินได้ตอน 14.30
// @Description (getUserCurrentReturnDetail) ผู้ใช้ดูยอดคืนเงินปัจจุบันของตัวเอง ตัดยอดวันนี้ 12.30 รับเงินได้ตอน 14.30
// @Tags Web - User Promotion Return Turn Amount
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.PromotionReturnUserDetail
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/promo-return-turn/current [get]
func (h promotionReturnTurnController) getUserCurrentReturnDetail(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	data, err := h.promotionReturnTurnService.GetUserCurrentReturnDetail(userId)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (takeUserReturnAmount) ผู้ใช้โยกเงินเข้าบัญชีเครดิคตัวเอง
// @Description (takeUserReturnAmount) ผู้ใช้โยกเงินเข้าบัญชีเครดิคตัวเอง
// @Tags Web - User Promotion Return Turn Amount
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/promo-return-turn/take [post]
func (h promotionReturnTurnController) takeUserReturnAmount(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	if err := h.promotionReturnTurnService.TakeUserReturnAmount(userId); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Updated success"})
}

// @Summary (getUserReturnTurnHistoryList) ประวัติการโยกเงินเข้าบัญชีเครดิคตัวเอง
// @Description (getUserReturnTurnHistoryList) ประวัติการโยกเงินเข้าบัญชีเครดิคตัวเอง
// @Tags Web - User Promotion Return Turn Amount
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.PromotionReturnTurnTransactionListRequest true "PromotionReturnTurnTransactionListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/promo-return-turn/list [get]
func (h promotionReturnTurnController) getUserReturnTurnHistoryList(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var query model.PromotionReturnTurnTransactionListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	query.UserId = userId
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.promotionReturnTurnService.GetUserReturnTurnHistoryList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithPagination{List: data.List, Total: data.Total})
}
