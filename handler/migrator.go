package handler

import (
	"cybergame-api/middleware"
	"cybergame-api/model"
	"cybergame-api/repository"
	"cybergame-api/service"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"gorm.io/gorm"
)

type migratorController struct {
	migratorService service.MigratorService
}

func newMigratorController(
	migratorService service.MigratorService,
) migratorController {
	return migratorController{migratorService}
}

func MigratorController(r *gin.RouterGroup, db *gorm.DB) {

	singleSession := middleware.SingleSession(db)

	// CORE
	repo := repository.NewMigratorRepository(db)
	service := service.NewMigratorService(repo)
	handler := newMigratorController(service)

	migratorRoute := r.Group("/migrator", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	migratorRoute.POST("/users", handler.migratorUsers)
	migratorRoute.POST("/partners", handler.migratorPartners)
	migratorRoute.POST("/affiliate", handler.migratorUserAffiliate)

	migratorRoute.POST("/missing-affiliate", handler.migratorUserMissingAffiliate)
	migratorRoute.POST("/missing-first-transaction", handler.migratorUserFirstDepositTransaction)
	migratorRoute.POST("/set-old-user-first-deposit", handler.migratorSetOldUserFirstDeposit)

	bcelRoute := r.Group("/migrator-bcel", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	bcelRoute.POST("/users", handler.migratorBcelUsers)
}

// @Summary (migratorUsers) 1. Migrator Users
// @Description (migratorUsers)
// @Tags Migrator
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /migrator/users [post]
func (h migratorController) migratorUsers(c *gin.Context) {

	err := h.migratorService.MigratorUsers()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "success"})
}

// @Summary (migratorPartners) 2. Migrator Partners
// @Description (migratorPartners)
// @Tags Migrator
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router  /migrator/partners [post]
func (h migratorController) migratorPartners(c *gin.Context) {

	err := h.migratorService.MigratorPartners()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "success"})
}

// @Summary (migratorUserAffiliate) 3. Migrator User Affiliate
// @Description (migratorUserAffiliate)
// @Tags Migrator
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.MigratorResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router  /migrator/affiliate [post]
func (h migratorController) migratorUserAffiliate(c *gin.Context) {

	resp, err := h.migratorService.MigratorUserAffiliate()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, resp)
}

// @Summary (migratorUserMissingAffiliate) 4. Migrator Users
// @Description (migratorUserMissingAffiliate)
// @Tags Migrator
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.MigratorResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /migrator/missing-affiliate [post]
func (h migratorController) migratorUserMissingAffiliate(c *gin.Context) {

	resp, err := h.migratorService.MigratorUserMissingAffiliate()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, resp)
}

// @Summary (migratorUserFirstDepositTransaction) 4. Migrator Users First Deposit Transaction
// @Description (migratorUserFirstDepositTransaction)
// @Tags Migrator
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.MigratorResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /migrator/missing-first-transaction [post]
func (h migratorController) migratorUserFirstDepositTransaction(c *gin.Context) {

	resp, err := h.migratorService.MigratorUserFirstDepositTransaction()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, resp)
}

// @Summary (migratorSetOldUserFirstDeposit) 4. Migrator Set Old User First Deposit
// @Description (migratorSetOldUserFirstDeposit)
// @Description ทำ Migrate คนเก่า (คนที่ไอดีน้อยกว่า n)
// @Description จะใส่ข้อมูลฝาก 0 บาททำรายการวันที่ 31 ตุลา เป็นฝากครั้งแรก
// @Description แล้วถ้า มีฝากครั้งแรก ก่อนหน้านี้ จะเอารายการแแรก ออก
// @Tags Migrator
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.MigratorOldUserListRequest true "body"
// @Success 200 {object} model.MigratorOldUserFirstDepResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /migrator/set-old-user-first-deposit [post]
func (h migratorController) migratorSetOldUserFirstDeposit(c *gin.Context) {

	var body model.MigratorOldUserListRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	resp, err := h.migratorService.MigratorSetOldUserFirstDeposit(body)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, resp)
}

// @Summary (migratorBcelUsers) 1. Migrator Users From Bcel *emptyPasswordSalt
// @Description (migratorBcelUsers)
// @Tags Migrator Bcel
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /migrator-bcel/users [post]
func (h migratorController) migratorBcelUsers(c *gin.Context) {

	err := h.migratorService.MigratorBcelUsers()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "success"})
}
