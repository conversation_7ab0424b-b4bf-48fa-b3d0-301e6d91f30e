package handler

import (
	"cybergame-api/helper"
	"cybergame-api/middleware"
	"cybergame-api/model"
	"cybergame-api/repository"
	"cybergame-api/service"
	"fmt"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type gameOrderController struct {
	LotteryService service.GameOrderService
}

func newGameOrderController(
	GameOrderService service.GameOrderService,
) gameOrderController {
	return gameOrderController{GameOrderService}
}

func GameOrderController(r *gin.RouterGroup, db *gorm.DB) {

	gameOrder := repository.NewGameOrderRepository(db)
	gameOrderService := service.NewGameOrderService(gameOrder, db)
	handler := newGameOrderController(gameOrderService)

	rootLottery := r.Group("/lottery", middleware.AuthorizeUser)
	rootLottery.POST("/bet", handler.CreateBetOrderLottery)
	rootLotteryCallback := r.Group("/lottery-webhook", middleware.HMACMiddleware)
	rootLotteryCallback.POST("/win", handler.CreateWinOrderLottery)
	rootLotteryCallback.POST("/refund", handler.CreateRefundOrderLottery)
	rootLotteryCallback.POST("/commission", handler.CreateCommissionOrderLottery)

	rootTest := r.Group("/test", middleware.AuthorizeAdmin)
	rootTest.POST("/encode", handler.gameOrderEncodeExample)
	rootTest.POST("/decode", handler.gameOrderDecodeExample)

	rootOrder := r.Group("/order")
	rootOrder.POST("/game", handler.createGameOrder)

}

// @Summary Create bet order lottery (CreateBetOrderLottery)
// @Description JWT token user
// @Description ref1No := the external reference no
// @Description amount := the amount of the bet
// @Tags Lottery
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.CreateBetOrderLotteryRequest true "CreateBetOrderLottery"
// @Success 200 {object} model.CreateOrderLotteryResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /lottery/bet [post]
func (h *gameOrderController) CreateBetOrderLottery(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var req model.CreateBetOrderLotteryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	req.UserId = userId
	res, err := h.LotteryService.CreateBetOrderLottery(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, res)
}

// @Summary Create win order lottery (CreateWinOrderLottery)
// @Description JWT token user
// @Description ref1No := the external reference no
// @Description amount := the amount of the win
// @Tags Lottery
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.CreateWinOrderLotteryRequest true "CreateWinOrderLottery"
// @Success 200 {object} model.CreateOrderLotteryResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /lottery-webhook/win [post]
func (h *gameOrderController) CreateWinOrderLottery(c *gin.Context) {

	fmt.Println("CreateWinOrderLottery")
	//userId := helper.ConvertIdAnyToInt64(c.MustGet("userId")) // TODO: remove this line

	var req model.CreateWinOrderLotteryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	res, err := h.LotteryService.CreateWinOrderLottery(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, res)
}

// @Summary Create refund order lottery (CreateRefundOrderLottery)
// @Description JWT token user
// @Description ref1No := the external reference no
// @Description amount := the amount of the refund
// @Tags Lottery
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.CreateRefundOrderLotteryRequest true "CreateRefundOrderLottery"
// @Success 200 {object} model.CreateOrderLotteryResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /lottery/refund [post]
func (h *gameOrderController) CreateRefundOrderLottery(c *gin.Context) {

	// userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var req model.CreateRefundOrderLotteryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	res, err := h.LotteryService.CreateRefundOrderLottery(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, res)
}

// @Summary Create commission order lottery (CreateCommissionOrderLottery)
// @Description JWT token user
// @Description ref1No := the external reference no
// @Description amount := the amount of the commission
// @Tags Lottery
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.CreateCommissionOrderLotteryRequest true "CreateCommissionOrderLottery"
// @Success 200 {object} model.CreateOrderLotteryResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /lottery/commission [post]
func (h *gameOrderController) CreateCommissionOrderLottery(c *gin.Context) {

	// userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var req model.CreateCommissionOrderLotteryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	res, err := h.LotteryService.CreateCommissionOrderLottery(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, res)
}

// @Summary Set sent order lottery (SetSentOrderLottery)
// @Description encode
// @Tags Lottery
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param body body model.SetSentOrderLotteryEncodeRequest true "SetSentOrderLottery"
// @Success 200 {string} string
// @Failure 400 {object} handler.ErrorResponse
// @Router /test/encode [post]
func (h *gameOrderController) gameOrderEncodeExample(c *gin.Context) {

	var req model.SetSentOrderLotteryEncodeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	res, err := h.LotteryService.GameOrderEncodeExample(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, res)
}

// @Summary Set sent order lottery (gameOrderDecodeExample)
// @Description decode
// @Tags Lottery
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param body body string true "gameOrderDecodeExample"
// @Success 200 {object} model.SetSentOrderLotteryEncodeRequest
// @Failure 400 {object} handler.ErrorResponse
// @Router /test/decode [post]
func (h *gameOrderController) gameOrderDecodeExample(c *gin.Context) {

	var req string
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	res, err := h.LotteryService.GameOrderDecodeExample(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, res)
}

// @Summary Create order lottery (CreateOrderLottery)
// @Tags Lottery
// @Accept json
// @Produce json
// @Param body body model.CreateOrderRequest true "CreateOrderLottery"
// @Success 200 {object} model.CreateOrderLotteryResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /order/game [post]
func (h *gameOrderController) createGameOrder(c *gin.Context) {

	var req model.CreateOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	res, err := h.LotteryService.CreateGameOrder(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, res)
}
