package handler

import (
	"cybergame-api/helper"
	"cybergame-api/middleware"
	"cybergame-api/model"
	"cybergame-api/service"
	"io"
	"log"

	"cybergame-api/repository"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"gorm.io/gorm"
)

type agentController struct {
	agentService service.AgentService
}

func newAgentController(
	agentService service.AgentService,
) agentController {
	return agentController{agentService}
}

func AgentController(r *gin.RouterGroup, db *gorm.DB) {

	// singleSession := middleware.SingleSession(db)

	service1 := service.NewAgentService(repository.NewAgentF888Repository(db))
	handler := newAgentController(service1)

	// AgentFlow
	publicRoute := r.Group("/agf888")
	publicRoute.POST("/register", handler.agentRegister)
	publicRoute.POST("/login", handler.agentLogin)
	publicRoute.GET("/playlog", handler.getAgentPlayLog)

	serviceRoute := r.Group("/agf888", middleware.AuthorizeUser)
	serviceRoute.POST("/change-password", handler.agentChangePassword)
	serviceRoute.GET("/game-list", handler.getAgentGameList)
	serviceRoute.GET("/game-url", handler.getAgentGameUrl)
	serviceRoute.POST("/deposit", handler.agentDeposit)
	serviceRoute.POST("/withdraw", handler.agentWithdraw)

	// PAYMENT GATEWAY CALLBACK
	webhookRoute := r.Group("/hook", middleware.AuthorizeBasicUser)
	webhookRoute.POST("/f888-cbgame", handler.hookF888CbGame)

	// Endpoints
	endpointRoute := r.Group("/ep", middleware.AuthorizeUser)
	endpointRoute.POST("/auth001", handler.endpointAuth001)
	endpointRoute.POST("/game001", handler.endpointGame001)
	endpointRoute.POST("/game002", handler.endpointGame002)
}

// @Summary (agentRegister)
// @Description (agentRegister)
// @Tags Agent F888 - Services
// @Produce json
// @Param body body model.AgentF888RegisterRequest true "body"
// @Success 200 {object} model.AgentF888RegisterResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /agf888/register [post]
func (h agentController) agentRegister(c *gin.Context) {

	var body model.AgentF888RegisterRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	resp, err := h.agentService.AgentRegister(body)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, resp)
}

// @Summary (agentLogin)
// @Description (agentLogin)
// @Tags Agent F888 - Services
// @Produce json
// @Param body body model.AgentF888LoginRequest true "body"
// @Success 200 {object} model.AgentF888LoginResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /agf888/login [post]
func (h agentController) agentLogin(c *gin.Context) {

	var body model.AgentF888LoginRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	resp, err := h.agentService.AgentLogin(body)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, resp)
}

// @Summary (getAgentPlayLog)
// @Description (getAgentPlayLog)
// @Tags Agent F888 - Endpoints
// @Produce json
// @Param body body model.AgentF888PlayLogRequest true "body"
// @Success 200 {object} model.AgentF888PlayLogResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /agf888/playlog [get]
func (h agentController) getAgentPlayLog(c *gin.Context) {

	var body model.AgentF888PlayLogRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	resp, err := h.agentService.GetAgentPlayLog(body)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, resp)
}

// @Summary (agentChangePassword)
// @Description (agentChangePassword)
// @Tags Agent F888 - Services
// @Security BearerAuth
// @Produce json
// @Param body body model.AgentF888ChangePasswordRequest true "body"
// @Success 200 {object} model.AgentF888ChangePasswordResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /agf888/change-password [post]
func (h agentController) agentChangePassword(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var body model.AgentF888ChangePasswordRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	body.UserId = userId
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	resp, err := h.agentService.AgentChangePassword(body)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, resp)
}

// @Summary (getAgentGameList)
// @Description (getAgentGameList)
// @Tags Agent F888 - Services
// @Security BearerAuth
// @Produce json
// @Param body body model.AgentF888GameListRequest true "body"
// @Success 200 {object} model.AgentF888GameListResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /agf888/game-list [get]
func (h agentController) getAgentGameList(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var body model.AgentF888GameListRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	body.UserId = userId
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	resp, err := h.agentService.GetAgentGameList(body)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, resp)
}

// @Summary (getAgentGameUrl)
// @Description (getAgentGameUrl)
// @Tags Agent F888 - Services
// @Security BearerAuth
// @Produce json
// @Param body body model.AgentF888GameUrlRequest true "body"
// @Success 200 {object} model.AgentF888GameUrlResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /agf888/game-url [get]
func (h agentController) getAgentGameUrl(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var body model.AgentF888GameUrlRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	body.UserId = userId
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	resp, err := h.agentService.GetAgentGameUrl(body)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, resp)
}

// @Summary (agentDeposit)
// @Description (agentDeposit)
// @Tags Agent F888 - Services
// @Security BearerAuth
// @Produce json
// @Param body body model.AgentF888DepositRequest true "body"
// @Success 200 {object} model.AgentF888DepositResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /agf888/deposit [post]
func (h agentController) agentDeposit(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var body model.AgentF888DepositRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	body.UserId = userId
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	resp, err := h.agentService.AgentDeposit(body)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, resp)
}

// @Summary (agentWithdraw)
// @Description (agentWithdraw)
// @Tags Agent F888 - Services
// @Security BearerAuth
// @Produce json
// @Param body body model.AgentF888WithdrawRequest true "body"
// @Success 200 {object} model.AgentF888WithdrawResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /agf888/withdraw [post]
func (h agentController) agentWithdraw(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var body model.AgentF888WithdrawRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	body.UserId = userId
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	resp, err := h.agentService.AgentWithdraw(body)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, resp)
}

// @Summary (hookF888CbGame) Your company staging game endpoint where we can call hook to
// @Description (hookF888CbGame) Your company staging game endpoint where we can call hook to
// @Tags Agent F888 - Hooks
// @Security BasicAuth
// @Produce json
// @Param body body string true "body"
// @Success 200 {object} model.HookF888CbgameResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /hook/f888-cbgame [post]
func (h agentController) hookF888CbGame(c *gin.Context) {

	var createReq model.HookF888CbgameRequest
	createReq.JsonPayload = "{}"

	body := c.Request.Body
	payloadByte, err := io.ReadAll(body)
	if err != nil {
		log.Println(err)
	} else {
		createReq.JsonPayload = string(payloadByte)
	}

	resp, err := h.agentService.HookF888CbGame(createReq)
	if err != nil {
		log.Println("HookF888CbGame.ERROR", err.Error())
		var result model.HookF888CbgameResponse
		// result.Error = err
		c.JSON(200, result)
		return
	}
	c.JSON(200, resp)
}

// @Summary (endpointAuth001) AUTH001 - Authentication
// @Description (endpointAuth001) AUTH001 - Authentication
// @Tags Agent F888 - Endpoints
// @Security BearerAuth
// @Produce json
// @Param body body model.F888Auth001Request true "body"
// @Success 200 {object} model.F888Auth001Response
// @Failure 400 {object} handler.ErrorResponse
// @Router /ep/auth001 [post]
func (h agentController) endpointAuth001(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var body model.F888Auth001Request
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	resp, err := h.agentService.EndpointAuth001(userId, body)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, resp)
}

// @Summary (endpointGame001) GAME001 - Get Game List
// @Description (endpointGame001) GAME001 - Get Game List
// @Tags Agent F888 - Endpoints
// @Security BearerAuth
// @Produce json
// @Param body body model.F888Game001Request true "body"
// @Success 200 {object} model.F888Game001Response
// @Failure 400 {object} handler.ErrorResponse
// @Router /ep/game001 [post]
func (h agentController) endpointGame001(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var body model.F888Game001Request
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	resp, err := h.agentService.EndpointGame001(userId, body)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, resp)
}

// @Summary (endpointGame002) GAME002 - Get Game Url
// @Description (endpointGame002) GAME002 - Get Game Url
// @Tags Agent F888 - Endpoints
// @Security BearerAuth
// @Produce json
// @Param body body model.F888Game002Request true "body"
// @Success 200 {object} model.F888Game002Response
// @Failure 400 {object} handler.ErrorResponse
// @Router /ep/game002 [post]
func (h agentController) endpointGame002(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var body model.F888Game002Request
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	resp, err := h.agentService.EndpointGame002(userId, body)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, resp)
}
