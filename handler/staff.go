package handler

import (
	"cybergame-api/model"
	"cybergame-api/service"
	"strconv"

	"cybergame-api/repository"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type staffController struct {
	StaffService service.StaffService
}

func newStaffController(
	StaffService service.StaffService,
) staffController {
	return staffController{StaffService}
}

func StaffController(r *gin.RouterGroup, db *gorm.DB) {

	repo := repository.NewStaffRepository(db)
	fileRepo := repository.NewFileRepository(db)
	service := service.NewStaffService(repo, fileRepo)
	handler := newStaffController(service)

	staff := r.Group("/staff")
	staff.GET("list", handler.getStaffList)
	staff.GET(":staffId", handler.getStaffSeasonList)
	staff.POST("", handler.createStaff)
	staff.POST("sort", handler.updateStaffSortOrder)
	staff.PUT(":staffId", handler.updateStaff)
	staff.DELETE(":staffId", handler.deleteStaff)
}

// @Summary แสดงรายการทีมงาน
// @Description Get Staff List
// @Tags Staff
// @Accept  json
// @Security BearerAuth
// @Produce  json
// @Success 200 {object} model.StaffListResponse
// @Failure 400 {object} ErrorResponse
// @Router /staff/list [get]
func (h staffController) getStaffList(c *gin.Context) {

	result, err := h.StaffService.StaffGetList()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, result)
}

// @Summary ดึงข้อมูลทีมงานด้วย id
// @Description Get Staff Detail
// @Tags Staff
// @Accept  json
// @Security BearerAuth
// @Produce  json
// @Param staffId path int true "staffId"
// @Success 200 {object} model.StaffDetail
// @Failure 400 {object} ErrorResponse
// @Router /staff/{staffId} [get]
func (h staffController) getStaffSeasonList(c *gin.Context) {

	staffId := c.Param("staffId")
	toInt64, err := strconv.ParseInt(staffId, 10, 64)
	if err != nil {
		HandleError(c, err)
		return
	}

	result, err := h.StaffService.StaffDetail(toInt64)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, result)
}

// @Summary สร้างข้อมูลทีมงาน
// @Description Create Staff
// @Tags Staff
// @Accept  json
// @Security BearerAuth
// @Produce  json
// @Param body body model.StaffBody true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} ErrorResponse
// @Router /staff [post]
func (h staffController) createStaff(c *gin.Context) {

	body := model.StaffBody{}

	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.StaffService.StaffCreate(body); err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(201, model.Success{Message: "Created success"})
}

// @Summary แก้ไขข้อมูลทีมงาน
// @Description Update Staff
// @Tags Staff
// @Accept  json
// @Security BearerAuth
// @Produce  json
// @Param staffId path int true "staffId"
// @Param body body model.StaffUpdateBody true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} ErrorResponse
// @Router /staff/{staffId} [put]
func (h staffController) updateStaff(c *gin.Context) {

	staffId := c.Param("staffId")
	toInt64, err := strconv.ParseInt(staffId, 10, 64)
	if err != nil {
		HandleError(c, err)
		return
	}

	body := model.StaffUpdateBody{}

	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.StaffService.StaffUpdate(toInt64, body); err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(201, model.Success{Message: "Updated success"})
}

// @Summary ลบข้อมูลทีมงาน
// @Description Delete Staff
// @Tags Staff
// @Accept  json
// @Security BearerAuth
// @Produce  json
// @Param staffId path int true "staffId"
// @Success 201 {object} model.Success
// @Failure 400 {object} ErrorResponse
// @Router /staff/{staffId} [delete]
func (h staffController) deleteStaff(c *gin.Context) {

	staffId := c.Param("staffId")
	toInt64, err := strconv.ParseInt(staffId, 10, 64)
	if err != nil {
		HandleError(c, err)
		return
	}

	if err := h.StaffService.StaffDelete(toInt64); err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(201, model.Success{Message: "Deleted success"})
}

// @Summary อัพเดทลำดับทีมงาน
// @Description Update Staff Sort Order
// @Tags Staff
// @Accept  json
// @Security BearerAuth
// @Produce  json
// @Param body body model.StaffSortBody true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} ErrorResponse
// @Router /staff/sort [post]
func (h staffController) updateStaffSortOrder(c *gin.Context) {

	body := model.StaffSortBody{}

	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.StaffService.StaffUpdateSortOrder(body); err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(201, model.Success{Message: "Updated success"})
}
