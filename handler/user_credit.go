package handler

import (
	"cybergame-api/helper"
	"cybergame-api/middleware"
	"cybergame-api/model"
	"cybergame-api/service"
	"strconv"

	"cybergame-api/repository"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"gorm.io/gorm"
)

type userCreditController struct {
	userCreditService service.UserCreditService
}

func newUserCreditController(
	userCreditService service.UserCreditService,
) userCreditController {
	return userCreditController{userCreditService}
}

func UserCreditController(r *gin.RouterGroup, db *gorm.DB) {

	repo := repository.NewUserCreditRepository(db)
	service := service.NewUserCreditService(repo)
	handler := newUserCreditController(service)

	role := middleware.Role(db)
	singleSession := middleware.SingleSession(db)

	adminLogRoute := r.Group("/user-transactions", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	adminLogRoute.GET("/list", role.CheckPermission([]string{"deposit_withdrawal"}), handler.getUserTransactionList)
	adminLogRoute.GET("/summary", role.CheckPermission([]string{"deposit_withdrawal"}), handler.getUserCreditTransactionSummary)
	adminLogRoute.POST("/remove/:id", role.CheckPermission([]string{"deposit_withdrawal"}), handler.removeUserTransaction)
	adminLogRoute.GET("/removed-list", role.CheckPermission([]string{"deposit_withdrawal"}), handler.getUserTransactionRemovedList)

	// adminLogRoute.GET("/export-xlsx", role.CheckPermission([]string{"deposit_withdrawal"}), handler.exportUserTransactionList)
	adminLogRoute.GET("/export-xlsx", role.CheckPermission([]string{"export_file_transaction"}), handler.exportUserTransactionList)

	optionsRoute := r.Group("/user-transactions/options", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	optionsRoute.GET("/account-list", role.CheckPermission([]string{"deposit_withdrawal"}), handler.getuserTransferBankAccountList)
	optionsRoute.GET("/type-list", role.CheckPermission([]string{"deposit_withdrawal"}), handler.getTypeOptions)
	optionsRoute.GET("/sort-list", role.CheckPermission([]string{"deposit_withdrawal"}), handler.getSortOptions)

	playlogReportRoute := r.Group("/playlog-report", middleware.AuthorizeAdmin, singleSession.SingleUserSession())
	playlogReportRoute.GET("/daily-status-list", handler.getReportPlayLogStatusResponse)
	playlogReportRoute.GET("/playlog-list", handler.getReportPlayLogResponse)
	playlogReportRoute.GET("/playlog-summary", handler.getReportPlayLogSummary)
	playlogReportRoute.POST("/rerun-fail-playlog", handler.rerunFailPlaylog)
}

// @Summary (getReportPlayLogStatusResponse)
// @Description (getReportPlayLogStatusResponse)
// @Tags Playlog Report - Status Report
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.ReportPlayLogStatusRequest true "ReportPlayLogStatusRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /playlog-report/daily-status-list [get]
func (h userCreditController) getReportPlayLogStatusResponse(c *gin.Context) {

	var query model.ReportPlayLogStatusRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	pagination, err := h.userCreditService.GetReportPlayLogStatusResponse(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, pagination)
}

// @Summary (getReportPlayLogResponse)
// @Description (getReportPlayLogResponse)
// @Tags Playlog Report - Status Report
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.ReportPlayLogResponseRequest true "ReportPlayLogResponseRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /playlog-report/playlog-list [get]
func (h userCreditController) getReportPlayLogResponse(c *gin.Context) {

	var query model.ReportPlayLogResponseRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	pagination, err := h.userCreditService.GetReportPlayLogResponse(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, pagination)
}

// @Summary (getReportPlayLogSummary)
// @Description (getReportPlayLogSummary)
// @Tags Playlog Report - Status Report
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.ReportPlayLogResponseRequest true "ReportPlayLogResponseRequest"
// @Success 200 {object} model.ReportPlayLogSummaryResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /playlog-report/playlog-summary [get]
func (h userCreditController) getReportPlayLogSummary(c *gin.Context) {

	var query model.ReportPlayLogResponseRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	pagination, err := h.userCreditService.GetReportPlayLogSummary(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, pagination)
}

// @Summary (rerunFailPlaylog)
// @Description (rerunFailPlaylog)
// @Tags Playlog Report - Status Report
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.ReportPlayLogRerunRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /playlog-report/rerun-fail-playlog [post]
func (h userCreditController) rerunFailPlaylog(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	var body model.ReportPlayLogRerunRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.userCreditService.RerunFailPlaylog(body, adminId); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Created success"})
}

// @Summary (getuserTransferBankAccountList) ข้อมูลตัวเลือกธนาคาร
// @Description (getuserTransferBankAccountList) ข้อมูลตัวเลือกธนาคาร
// @Description - หน้าบ้านเอาไปกรองหน้า ฝากถอนเสร็จสิ้น
// @Tags Users Transaction
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} []model.SelectOptions
// @Failure 400 {object} handler.ErrorResponse
// @Router /user-transactions/options/account-list [get]
func (h userCreditController) getuserTransferBankAccountList(c *gin.Context) {

	data, err := h.userCreditService.GetuserTransferBankAccountList()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (getTypeOptions) ข้อมูลตัวเลือกประเภทการกระทำแอดมิน
// @Description (getTypeOptions) ข้อมูลตัวเลือกประเภทการกระทำแอดมิน
// @Description - หน้าบ้านเอาไปกรองหน้า ฝากถอนเสร็จสิ้น
// @Tags Users Transaction
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} []model.SelectOptions
// @Failure 400 {object} handler.ErrorResponse
// @Router /user-transactions/options/type-list [get]
func (h userCreditController) getTypeOptions(c *gin.Context) {

	data, err := h.userCreditService.GetTypeOptions()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (getSortOptions) ข้อมูลตัวเลือกประเภทการกระทำแอดมิน
// @Description (getSortOptions) ข้อมูลตัวเลือกประเภทการกระทำแอดมิน
// @Tags Users Transaction
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} []model.SelectOptions
// @Failure 400 {object} handler.ErrorResponse
// @Router /user-transactions/options/sort-list [get]
func (h userCreditController) getSortOptions(c *gin.Context) {

	data, err := h.userCreditService.GetSortOptions()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (getUserCreditTransactionSummary) ข้อมูลสรุปผู้ใช้ รายการฝาก-ถอน เสร็จสิ้น ตัวกรองเดียวกับ List ตาราง
// @Description (getUserCreditTransactionSummary) ข้อมูลสรุปผู้ใช้ รายการฝาก-ถอน เสร็จสิ้น
// @Description
// @Description # การกรองด้วยประเภทวัน ทั้งหมด วันนี้ เมื่อวาน เดือนนี้ **ถ้าส่ง ofDate จะฟัง ofdate ก่อน ส่วน fromDate toDate จะทำงานหลังสุดถ้ามี
// @Description | dateType | Description |
// @Description | ---------------- | ------------|
// @Description | all *หรือไม่ส่ง* | แสดงทั้งหมด |
// @Description | today | แสดงวันนี้ |
// @Description | yesterday | แสดงเมื่อวาน |
// @Description | this_month | แสดงเดือนนี้ 1-31 |
// @Description | ---------------- | ------------|
// @Description
// @Tags Users Transaction
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.UserTransactionListRequest true "query"
// @Success 200 {object} model.UserTransactionSummaryResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /user-transactions/summary [get]
func (h userCreditController) getUserCreditTransactionSummary(c *gin.Context) {

	var query model.UserTransactionListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.userCreditService.GetUserCreditTransactionSummary(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (getUserTransactionList) รายการฝาก-ถอน เสร็จสิ้น
// @Description (getUserTransactionList) รายการฝาก-ถอน เสร็จสิ้น
// @Description
// @Description # การกรองด้วยประเภทวัน ทั้งหมด วันนี้ เมื่อวาน เดือนนี้ **ถ้าส่ง ofDate จะฟัง ofdate ก่อน ส่วน fromDate toDate จะทำงานหลังสุดถ้ามี
// @Description | dateType | Description |
// @Description | ---------------- | ------------|
// @Description | all *หรือไม่ส่ง* | แสดงทั้งหมด |
// @Description | today | แสดงวันนี้ |
// @Description | yesterday | แสดงเมื่อวาน |
// @Description | this_month | แสดงเดือนนี้ 1-31 |
// @Description | ---------------- | ------------|
// @Description
// @Tags Users Transaction
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.UserTransactionListRequest true "query"
// @Success 200 {object} model.SuccessWithList
// @Failure 400 {object} handler.ErrorResponse
// @Router /user-transactions/list [get]
func (h userCreditController) getUserTransactionList(c *gin.Context) {

	query := model.UserTransactionListRequest{}
	if err := c.ShouldBindQuery(&query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.userCreditService.GetUserTransactionList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (exportUserTransactionList) ส่งออก excel รายการฝาก-ถอน เสร็จสิ้น
// @Description (exportUserTransactionList) ส่งออก excel รายการฝาก-ถอน เสร็จสิ้น
// @Tags Users Transaction
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.UserTransactionForExcelListRequest true "query"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /user-transactions/export-xlsx [get]
func (h userCreditController) exportUserTransactionList(c *gin.Context) {

	query := model.UserTransactionForExcelListRequest{}
	if err := c.ShouldBindQuery(&query); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.userCreditService.ExportUserTransactionList(c, query); err != nil {
		HandleError(c, err)
		return
	}
}

// @Summary (removeUserTransaction) ลบ รายการฝาก-ถอน เสร็จสิ้น
// @Description (removeUserTransaction) ลบ รายการฝาก-ถอน เสร็จสิ้น
// @Tags Users Transaction
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /user-transactions/remove/{id} [post]
func (h userCreditController) removeUserTransaction(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	id := c.Param("id")
	identifier, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleError(c, err)
		return
	}

	if err := h.userCreditService.RemoveUserTransaction(identifier, adminId); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Updated success"})
}

// @Summary (getUserTransactionRemovedList) รายการฝาก-ถอน เสร็จสิ้น ที่ถูกลบ
// @Description (getUserTransactionRemovedList) รายการฝาก-ถอน เสร็จสิ้น ที่ถูกลบ
// @Description
// @Description # การกรองด้วยประเภทวัน ทั้งหมด วันนี้ เมื่อวาน เดือนนี้ **ถ้าส่ง ofDate จะฟัง ofdate ก่อน ส่วน fromDate toDate จะทำงานหลังสุดถ้ามี
// @Description | dateType | Description |
// @Description | ---------------- | ------------|
// @Description | all *หรือไม่ส่ง* | แสดงทั้งหมด |
// @Description | today | แสดงวันนี้ |
// @Description | yesterday | แสดงเมื่อวาน |
// @Description | this_month | แสดงเดือนนี้ 1-31 |
// @Description | ---------------- | ------------|
// @Description
// @Tags Users Transaction
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.UserTransactionListRequest true "query"
// @Success 200 {object} model.SuccessWithList
// @Failure 400 {object} handler.ErrorResponse
// @Router /user-transactions/removed-list [get]
func (h userCreditController) getUserTransactionRemovedList(c *gin.Context) {

	query := model.UserTransactionListRequest{}
	if err := c.ShouldBindQuery(&query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.userCreditService.GetUserTransactionRemovedList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}
