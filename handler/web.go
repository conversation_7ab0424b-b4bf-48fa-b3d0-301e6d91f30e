package handler

import (
	"cybergame-api/model"
	"cybergame-api/service"

	"cybergame-api/repository"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"gorm.io/gorm"
)

type webController struct {
	webService service.WebService
}

func newWebController(
	webService service.WebService,
) webController {
	return webController{webService}
}

func WebController(r *gin.RouterGroup, db *gorm.DB) {

	repo := repository.NewAffiliateRepository(db)
	alRepo := repository.NewAllianceRepository(db)
	userRepo := repository.NewUserRepository(db)
	service := service.NewWebService(repo, alRepo, userRepo)
	handler := newWebController(service)

	r = r.Group("/web")
	r.POST("increase-ref-count", handler.increaseRefCount)
}

// @Summary (increaseRefCount)
// @Description (increaseRefCount)
// @Tags Web - Register
// @Accept json
// @Produce json
// @Param body body model.GetAliasByUserIdRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/increase-ref-count [post]
func (h webController) increaseRefCount(c *gin.Context) {

	var body model.GetAliasByUserIdRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.webService.IncreaseRefCount(body); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "Update success"})
}
