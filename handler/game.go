package handler

import (
	"cybergame-api/middleware"
	"cybergame-api/model"
	"cybergame-api/service"
	"errors"
	"net/http"
	"os"
	"strings"

	"cybergame-api/repository"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt"
	"gorm.io/gorm"
)

type gameController struct {
	gameService service.GameService
}

func newGameController(
	gameService service.GameService,
) gameController {
	return gameController{gameService}
}

func GameController(r *gin.RouterGroup, db *gorm.DB) {

	singleSession := middleware.SingleSession(db)

	agentInfoRepo := repository.NewAgentInfoRepository(db)
	service := service.NewGameService(agentInfoRepo)
	handler := newGameController(service)

	gameRoute := r.Group("/games")
	gameRoute.GET("/providers", handler.getGameProviderList)
	gameRoute.GET("/list/:name", handler.getGameList)
	gameRoute.GET("/list2", handler.getGameList2)
	gameRoute.GET("/category/:name", handler.getGameCategory)
	gameRoute.POST("/play", middleware.AuthorizeUser, singleSession.SingleUserSession(), handler.playGame)
	gameRoute.GET("/play-html", handler.playHtmlGame)
	gameRoute.GET("/test-login", handler.testLoginGame)
	gameRoute.GET("/vendor-maintenance-list", handler.AgcVendorMaintenanceList)

	configRoute := r.Group("/config", middleware.AuthorizeUser, singleSession.SingleUserSession())
	configRoute.GET("/game", handler.getGameConfiguration)

	cacheRoute := r.Group("/games")
	cacheRoute.GET("/clear-game-cache", handler.ClearGameCache)

}

// @Summary (getGameConfiguration) ดึงข้อมูลการตั้งค่าเกมส์
// @Description (getGameConfiguration)
// @Tags Game
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 201 {object} model.GameConfiguration
// @Failure 400 {object} ErrorResponse
// @Router /v1/config/game [get]
func (h gameController) getGameConfiguration(c *gin.Context) {

	setting, err := h.gameService.GetGameConfiguration()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, setting)
}

// @Summary (getGameProviderList) Get Game provider
// @Description (getGameProviderList) Get Game provider
// @Tags Game
// @Accept json
// @Produce json
// @Success 200 {object} model.SuccessWithData
// @Router /v1/games/providers [get]
func (h gameController) getGameProviderList(c *gin.Context) {

	data := h.gameService.GameGetProviders()

	c.JSON(200, model.SuccessWithData{Message: "Success", Data: data})
}

// @Summary (getGameList) Get Game list
// @Description (getGameList) Get Game list
// @Tags Game
// @Accept json
// @Produce json
// @Param name path string true "name"
// @Success 200 {object} model.SuccessWithData
// @Router /v1/games/list/{name} [get]
func (h gameController) getGameList(c *gin.Context) {

	name := c.Param("name")

	// ** Compatible with old API **
	var req model.AgentGameListRequest
	req.Vendor = name
	result, err := h.gameService.GameGetList(req)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithData{Message: "Success", Data: result})
}

// @Summary (getGameList2) Get Game list
// @Description (getGameList2) Get Game list
// @Tags Game
// @Accept json
// @Produce json
// @Param _ query model.AgentGameListRequest true "query"
// @Success 200 {object} model.SuccessWithData
// @Router /v1/games/list2 [get]
func (h gameController) getGameList2(c *gin.Context) {

	var query model.AgentGameListRequest
	if err := c.ShouldBindQuery(&query); err != nil {
		HandleError(c, err)
		return
	}

	result, err := h.gameService.GameGetList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithData{Message: "Success", Data: result})
}

// @Summary (getGameCategory) Get Game category
// @Description (getGameCategory) Get Game category
// @Tags Game
// @Accept json
// @Produce json
// @Param name path string true "POPULAR, SPORT, CASINO, SLOT, LOTTO, P2P"
// @Success 200 {object} model.GameCategoryResponse
// @Router /v1/games/category/{name} [get]
func (h gameController) getGameCategory(c *gin.Context) {

	name := c.Param("name")

	result, err := h.gameService.GameGetCategory(name)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, result)
}

// @Summary (playGame) Play Game
// @Description (playGame) Play Game
// @Tags Game
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.AgcPlayBody true "Play Game"
// @Success 201 {object} model.AgcPlayResponse
// @Failure 400 {object} ErrorResponse
// @Router /v1/games/play [post]
func (h gameController) playGame(c *gin.Context) {

	userId := c.MustGet("userId").(float64)

	// ip := helper.GetIPv4(c.ClientIP())
	var currentIp string
	if ip := c.GetHeader("X-Real-IP"); ip != "" {
		currentIp = ip
	} else if ip = c.GetHeader("X-Forwarded-For"); ip != "" {
		currentIp = strings.Split(ip, ",")[0]
	} else {
		currentIp = c.ClientIP()
	}

	var body model.AgcPlayBody
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}

	body.UserId = int64(userId)
	body.Ip = currentIp

	result, err := h.gameService.GamePlay(body)
	if err != nil {
		// create new error response
		// HandleError(c, "GAME_NOT_AVAILABLE")
		HandleError(c, err)
		return
	}
	c.JSON(201, result)
}

// @Summary (playHtmlGame) Play Game
// @Description (playHtmlGame) Play Game
// @Tags Game
// @Accept json
// @Produce json
// @Param _ query model.AgcPlayHtmlRequest false "Play Game"
// @Success 201 {object} string
// @Failure 400 {object} ErrorResponse
// @Router /v1/games/play-html [get]
func (h gameController) playHtmlGame(c *gin.Context) {

	// ip := helper.GetIPv4(c.ClientIP())
	var currentIp string
	if ip := c.GetHeader("X-Real-IP"); ip != "" {
		currentIp = ip
	} else if ip = c.GetHeader("X-Forwarded-For"); ip != "" {
		currentIp = strings.Split(ip, ",")[0]
	} else {
		currentIp = c.ClientIP()
	}

	var body model.AgcPlayHtmlRequest
	if err := c.ShouldBindQuery(&body); err != nil {
		HandleError(c, err)
		return
	}

	// decode JWT access token
	claims, _ := jwt.ParseWithClaims(body.AccessToken, jwt.MapClaims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(os.Getenv("JWT_SECRET_USER")), nil
	})
	if claims.Claims.(jwt.MapClaims)["userId"] == nil &&
		claims.Claims.(jwt.MapClaims)["phone"] == nil &&
		claims.Claims.(jwt.MapClaims)["username"] == nil {
		c.AbortWithStatusJSON(401, errors.New("Unauthorized"))
		return
	}

	userId := claims.Claims.(jwt.MapClaims)["userId"].(float64)
	body.UserId = int64(userId)
	body.Ip = currentIp

	html, err := h.gameService.GamePlayHtml(body)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.Data(http.StatusOK, "text/html; charset=utf-8", []byte(html))
}

// @Summary (testLoginGame) Play Game
// @Description (testLoginGame) Play Game
// @Tags Game
// @Accept json
// @Produce json
// @Param _ query model.AgcTestLoginRequest false "Play Game"
// @Success 201 {object} string
// @Failure 400 {object} ErrorResponse
// @Router /v1/games/test-login [get]
func (h gameController) testLoginGame(c *gin.Context) {

	// ip := helper.GetIPv4(c.ClientIP())
	var currentIp string
	if ip := c.GetHeader("X-Real-IP"); ip != "" {
		currentIp = ip
	} else if ip = c.GetHeader("X-Forwarded-For"); ip != "" {
		currentIp = strings.Split(ip, ",")[0]
	} else {
		currentIp = c.ClientIP()
	}

	var body model.AgcTestLoginRequest
	if err := c.ShouldBindQuery(&body); err != nil {
		HandleError(c, err)
		return
	}

	// decode JWT access token
	claims, _ := jwt.ParseWithClaims(body.AccessToken, jwt.MapClaims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(os.Getenv("JWT_SECRET_USER")), nil
	})
	if claims.Claims.(jwt.MapClaims)["userId"] == nil &&
		claims.Claims.(jwt.MapClaims)["phone"] == nil &&
		claims.Claims.(jwt.MapClaims)["username"] == nil {
		c.AbortWithStatusJSON(401, errors.New("Unauthorized"))
		return
	}

	userId := claims.Claims.(jwt.MapClaims)["userId"].(float64)
	body.UserId = int64(userId)
	body.Ip = currentIp

	html, err := h.gameService.TestLoginGame(body)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.Data(http.StatusOK, "text/html; charset=utf-8", []byte(html))
}

// ClearGameCache() error
// @Summary (ClearGameCache) Clear Game Cache
// @Description (ClearGameCache) Clear Game Cache
// @Tags Game
// @Accept json
// @Produce json
// @Success 200 {object} model.Success
// @Router /v1/games/clear-game-cache [get]
func (h gameController) ClearGameCache(c *gin.Context) {

	err := h.gameService.ClearGameCache()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "Success"})
}

// AgcVendorMaintenanceList() (*model.AgcVendorMaintenanceListResponse, error)
// @Summary (AgcVendorMaintenanceList) Get Agc Vendor Maintenance List
// @Description (AgcVendorMaintenanceList) Get Agc Vendor Maintenance List
// @Tags Game
// @Accept json
// @Produce json
// @Success 200 {object} model.Success
// @Router /v1/games/vendor-maintenance-list [get]
func (h gameController) AgcVendorMaintenanceList(c *gin.Context) {

	result, err := h.gameService.AgcVendorMaintenanceList()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, result)
}
