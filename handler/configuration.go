package handler

import (
	"cybergame-api/helper"
	"cybergame-api/middleware"
	"cybergame-api/model"
	"cybergame-api/repository"
	"cybergame-api/service"
	"errors"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type configurationController struct {
	configurationService service.ConfigurationService
}

func newConfigurationController(configurationService service.ConfigurationService,
) configurationController {
	return configurationController{configurationService}
}

func ConfigurationController(r *gin.RouterGroup, db *gorm.DB) {

	adminActionService := service.NewAdminActionService(repository.NewAdminActionRepository(db))

	configurationRepo := repository.NewConfigurationRepository(db)
	configurationService := service.NewConfigurationService(configurationRepo, adminActionService)
	handler := newConfigurationController(configurationService)
	role := middleware.Role(db)
	singleSession := middleware.SingleSession(db)
	// configuration web options
	configurationOptionRoute := r.Group("/configuration/options", middleware.AuthorizeAdmin, singleSession.SingleAdminSession(), role.CheckPermission([]string{"setting"}))
	configurationOptionRoute.GET("/auto-user-approve-type", handler.getAutoUserApproveType)
	configurationOptionRoute.GET("/auto-withdraw-type", handler.getAutoWithdrawType)
	configurationOptionRoute.GET("/turn-withdraw-type", handler.getTurnWithdrawType)
	configurationOptionRoute.GET("/register-format", handler.GetConfigurationRegisterFormat)
	// configuration web
	configurationRoute := r.Group("/configuration", middleware.AuthorizeAdmin, singleSession.SingleAdminSession(), role.CheckPermission([]string{"setting"}))
	configurationRoute.GET("", handler.getConfiguration)
	configurationRoute.PUT("", handler.updateConfiguration)
	configurationRoute.POST("clear-cache", handler.clearAllConfigurationCache)
	configurationRoute.POST("/upload/logo/image", handler.uploadImageToCloudflareConfigLogo)
	configurationRoute.GET("bank-limit", handler.getBankLimitConfiguration)
	configurationRoute.PUT("bank-limit", handler.updateBankLimitConfiguration)
	configurationRoute.GET("bank-limit/:id", handler.getUserBankLimit)
	configurationRoute.GET("/game-priority-setting-list", handler.getAgentGamePrioritySettingList)
	configurationRoute.PUT("/game-priority-setting-sort", handler.SortGamegentGamePriorityOrder)
	configurationRoute.GET("/game-priority-category", handler.getGameCategory)
	configurationRoute.PUT("/game-priority-setting/:id", handler.UpdateAgentGamePrioritySetting)
	configurationRoute.GET("/running-message", handler.GetRunningMessage)

	configurationWebRoute := r.Group("/configuration", middleware.AuthorizeUser, singleSession.SingleUserSession())
	configurationWebRoute.GET("/web-amount", handler.getWebConfiguration)

	webLineRoute := r.Group("/web")
	webLineRoute.GET("/contact", handler.getContactConfig)
	webLineRoute.GET("/configuration/check-show-transaction", handler.GetConfigurationCheckShowTransaction)

	// /play/increment-played/{vendorCode} [put]
	playGameControllerRoute := r.Group("/play", middleware.AuthorizeUser)
	playGameControllerRoute.PUT("/increment-played/:vendorCode", handler.IncrementAgentGameTotalPlayed)

	// banner setting
	bannerSettingRoute := r.Group("/configuration", middleware.AuthorizeAdmin, singleSession.SingleAdminSession(), role.CheckPermission([]string{"setting"}))
	bannerSettingRoute.POST("/banner-setting", handler.CreateBannerSetting)
	bannerSettingRoute.PATCH("/banner-setting/:id", handler.updateBannerSetting)
	bannerSettingRoute.GET("/banner-setting-list", handler.GetBannerSettingList)
	bannerSettingRoute.PUT("/banner-setting", handler.DeleteBannerSetting)
	bannerSettingRoute.POST("/upload/banner/image", handler.uploadImageToS3ConfigBanner)

	// /web/configuration/banner
	webBannerSettingRoute := r.Group("/web")
	webBannerSettingRoute.GET("/configuration/banner-public", handler.WebGetPublicBannerSettingList)
	webBannerSettingRoute.GET("/configuration/banner", handler.WebGetBannerSettingList)
	webBannerSettingRoute.GET("/configuration/register-format", handler.WebGetConfigurationRegisterFormat)

	activityMenu := r.Group("/configuration", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	activityMenu.PUT("/activity-menu/:id", handler.UpdateActivityMenu)
	activityMenu.PUT("/activity-menu/sort", handler.SortActivityMenu)
	activityMenu.POST("/activity-menu/upload/image", handler.UploadImageActivityMenu)
	activityMenu.GET("/activity-menu", handler.GetActivityMenu)
	activityMenu.GET("/activity-menu/item", handler.GetActivityMenuItem)

}

// @Summary Get AutoUserApproveType
// @Description Get AutoUserApproveType
// @Tags Configuration - Options
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.AutoUserApproveType
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/options/auto-user-approve-type [get]
func (h *configurationController) getAutoUserApproveType(c *gin.Context) {

	options, err := h.configurationService.GetAutoUserApproveType()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, options)
}

// @Summary Get AutoWithdrawType
// @Description Get AutoWithdrawType
// @Tags Configuration - Options
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.AutoWithdrawType
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/options/auto-withdraw-type [get]
func (h *configurationController) getAutoWithdrawType(c *gin.Context) {

	options, err := h.configurationService.GetAutoWithdrawType()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, options)
}

// @Summary Get TurnWithdrawType
// @Description Get TurnWithdrawType
// @Tags Configuration - Options
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.TurnWithdrawType
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/options/turn-withdraw-type [get]
func (h *configurationController) getTurnWithdrawType(c *gin.Context) {

	options, err := h.configurationService.GetTurnWithdrawType()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, options)
}

// @Summary (GetConfiguration) Get Configuration
// @Description (GetConfiguration) Get Configuration
// @Tags Configuration
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.ConfigurationResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration [get]
func (h *configurationController) getConfiguration(c *gin.Context) {

	configuration, err := h.configurationService.GetConfiguration()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, configuration)
}

// @Summary (updateConfiguration) Update Configuration
// @Description (updateConfiguration) Update Configuration
// @Description - ปิดการใช้งานแจ้งฝาก => DISABLED (ปิดการใช้งาน)
// @Description - เปิดการใช้งานอัพสลิป => FILE (อัพโหลดไฟล์เฉยๆ+สร้างรายการฝาก)
// @Description - เปิดการใช้งานเช็คสลิปออโต้ => QR_AUTO (ไม่เก็บไฟล์ ใช้ QR เช็คกับ Fastbank +สร้างรายการฝาก)
// @Tags Configuration
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.UpdateConfigurationRequest true "body"
// @Success 200 {object} model.UpdateConfigurationRequest
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration [put]
func (h *configurationController) updateConfiguration(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	var body model.UpdateConfigurationRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	body.UpdateBy = adminId

	if err := h.configurationService.UpdateConfiguration(body); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, "success")
}

// @Summary (clearAllConfigurationCache) Clear Configuration Cache
// @Description (clearAllConfigurationCache) Clear Configuration Cache
// @Tags Configuration
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/clear-cache [post]
func (h *configurationController) clearAllConfigurationCache(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))
	if adminId == 0 {
		HandleError(c, errors.New("INVALID_ADMIN_ID"))
		return
	}

	if err := h.configurationService.ClearAllConfigurationCache(); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "Success"})
}

// @Summary (getWebConfiguration) Get Web Configuration
// @Description (getWebConfiguration) Get Web Configuration
// @Tags Configuration - Web
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.GetWebConfigurationResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/web-amount [get]
func (h *configurationController) getWebConfiguration(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	configuration, err := h.configurationService.GetWebConfiguration(userId)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, configuration)
}

// @Summary Get Line Config Web
// @Description Get Line Config Web
// @Tags Configuration - Web
// @Accept json
// @Produce json
// @Success 200 {object} model.GetContactConfigWebResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/contact [get]
func (h *configurationController) getContactConfig(c *gin.Context) {

	line, err := h.configurationService.GetContactConfig()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, line)
}

// UploadImageToCloudflareConfigLogo(imageFileBody *http.Request) (*model.FileUploadResponse, error)
// @Summary Upload Image To Cloudflare Config Logo
// @Description Upload Image To Cloudflare Config Logo
// @Tags Configuration - Web
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param file formData file true "file"
// @Success 200 {object} model.FileUploadResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/upload/logo/image [post]
func (h *configurationController) uploadImageToCloudflareConfigLogo(c *gin.Context) {

	data, err := h.configurationService.UploadImageToS3ConfigLogo(c.Request)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (GetBankLimitConfiguration) Get การตั้งค่าจำกัดการถอนสูงสุด
// @Description (GetBankLimitConfiguration) [********] เพิ่ม Config จำกัดการถอนสูงสุดต่อเครดิต/ จำนวนครั้ง(ต่อวัน)
// @Tags Configuration
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.BankLimitConfigurationResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/bank-limit [get]
func (h *configurationController) getBankLimitConfiguration(c *gin.Context) {

	configuration, err := h.configurationService.GetBankLimitConfiguration()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, configuration)
}

// @Summary (updateBankLimitConfiguration) Update การตั้งค่าจำกัดการถอนสูงสุด
// @Description (updateBankLimitConfiguration) [********] เพิ่ม Config จำกัดการถอนสูงสุดต่อเครดิต/ จำนวนครั้ง(ต่อวัน)
// @Tags Configuration
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.UpdateBankLimitConfigurationRequest true "body"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/bank-limit [put]
func (h *configurationController) updateBankLimitConfiguration(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	var body model.UpdateBankLimitConfigurationRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	body.UpdateBy = adminId

	if err := h.configurationService.UpdateBankLimitConfiguration(body); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "success"})
}

// @Summary (getUserBankLimit) Check user bank limit
// @Description (getUserBankLimit)
// @Tags Configuration
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "User ID"
// @Success 200 {object} model.UserBankLimitResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/bank-limit/{id} [get]
func (h *configurationController) getUserBankLimit(c *gin.Context) {

	id := c.Param("id")
	toInt, err := strconv.Atoi(id)
	if err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.configurationService.GetUserBankLimit(int64(toInt))
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary Get Agent Game Priority Setting List
// @Description Get Agent Game Priority Setting List
// @Tags Configuration
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.GetAgentGamePrioritySettingListRequest true "GetAgentGamePrioritySettingListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/game-priority-setting-list [get]
func (h *configurationController) getAgentGamePrioritySettingList(c *gin.Context) {

	var req model.GetAgentGamePrioritySettingListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.configurationService.GetAgentGamePrioritySettingList(req)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary Sort Game Agent Game Priority Order
// @Description Sort Game Agent Game Priority Order
// @Tags Configuration
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param req body model.DragSortRequest true "body"
// @Success 200 {object} string
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/game-priority-setting-sort [put]
func (h *configurationController) SortGamegentGamePriorityOrder(c *gin.Context) {

	var req model.DragSortRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))
	if err := h.configurationService.SortGamegentGamePriorityOrder(req, adminId); err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, "success")
}

// @Summary Increment Agent Game Total Played
// @Description Increment Agent Game Total Played
// @Tags Configuration
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param vendorCode path string true "Vendor Code"
// @Success 200 {object} string
// @Failure 400 {object} handler.ErrorResponse
// @Router /play/increment-played/{vendorCode} [put]
func (h *configurationController) IncrementAgentGameTotalPlayed(c *gin.Context) {

	vendorCode := c.Param("vendorCode")

	if err := h.configurationService.IncrementAgentGameTotalPlayed(vendorCode); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, "success")
}

// @Summary Get Game Category
// @Description Get Game Category
// @Tags Configuration
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} []model.SelectOptions
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/game-priority-category [get]
func (h *configurationController) getGameCategory(c *gin.Context) {

	data := []model.SelectOptions{
		{Id: 1, Value: "SLOT", Label: "slot"},
		{Id: 2, Value: "CASINO", Label: "casino"},
		{Id: 3, Value: "SPORT", Label: "sport"},
		{Id: 4, Value: "P2P", Label: "P2P"},
		{Id: 5, Value: "LOTTO", Label: "lotto"},
		{Id: 6, Value: "POPULAR", Label: "popular"},
	}
	c.JSON(200, data)
}

// @Summary Update Agent Game Priority Setting
// @Description Update Agent Game Priority Setting
// @Tags Configuration
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "ID"
// @Param body body model.UpdateAgentGamePrioritySettingBody true "body"
// @Success 200 {object} string
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/game-priority-setting/{id} [put]
func (h *configurationController) UpdateAgentGamePrioritySetting(c *gin.Context) {

	id := c.Param("id")
	identifier, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleError(c, err)
		return
	}

	var body model.UpdateAgentGamePrioritySettingBody
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	body.Id = identifier

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))
	body.UpdatedByID = adminId
	if err := h.configurationService.UpdateAgentGamePrioritySetting(body); err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, "success")
}

// @Summary Create Banner Setting
// @Description Create Banner Setting
// @Tags Configuration Banner Setting
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.CreateBannerSettingRequest true "body"
// @Success 200 {object} string
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/banner-setting [post]
func (h *configurationController) CreateBannerSetting(c *gin.Context) {

	var req model.CreateBannerSettingRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}
	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))
	req.CreatedByID = adminId
	if err := h.configurationService.CreateBannerSetting(req); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, "success")
}

// @Summary (updateBannerSetting) Update Banner Setting
// @Description (updateBannerSetting) Update Banner Setting
// @Tags Configuration Banner Setting
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "ID"
// @Param body body model.BannerSettingUpdateRequest true "body"
// @Success 200 {object} string
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/banner-setting/{id} [patch]
func (h *configurationController) updateBannerSetting(c *gin.Context) {

	id := c.Param("id")
	identifier, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleError(c, err)
		return
	}

	var req model.BannerSettingUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	// adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))
	// req.UpdatedByID = adminId
	if err := h.configurationService.UpdateBannerSetting(identifier, req); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, "success")
}

// @Summary (GetBannerSettingList) Get Banner Setting List
// @Description (GetBannerSettingList) Get Banner Setting List
// @Tags Configuration Banner Setting
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.GetBannerSettingListRequest true "GetBannerSettingListRequest"
// @Success 200 {object} []model.GetBannerSettingListResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/banner-setting-list [get]
func (h *configurationController) GetBannerSettingList(c *gin.Context) {

	var req model.GetBannerSettingListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.configurationService.GetBannerSettingList(req)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary Delete Banner Setting
// @Description Delete Banner Setting
// @Tags Configuration Banner Setting
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.DeleteBannerSettingRequest true "body"
// @Success 200 {object} string
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/banner-setting [put]
func (h *configurationController) DeleteBannerSetting(c *gin.Context) {

	var req model.DeleteBannerSettingRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}
	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))
	req.DeletedAt = time.Now().UTC()
	req.DeletedByID = adminId
	if err := h.configurationService.DeleteBannerSetting(req); err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, "success")
}

// @Summary Upload Image To Cloudflare Config Banner
// @Description Upload Image To Cloudflare Config Banner
// @Tags Configuration - Web
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param file formData file true "file"
// @Success 200 {object} model.FileUploadResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/upload/banner/unused [post]
func (h *configurationController) UploadImageToCloudflareConfigBanner(c *gin.Context) {

	data, err := h.configurationService.UploadImageToS3ConfigBanner(c.Request)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (WebGetPublicBannerSettingList)
// @Description (WebGetPublicBannerSettingList)
// @Tags Configuration Banner Setting
// @Accept json
// @Produce json
// @Param _ query model.GetBannerSettingListRequest true "GetBannerSettingListRequest"
// @Success 200 {object} []model.GetBannerSettingListResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/configuration/banner-public [get]
func (h *configurationController) WebGetPublicBannerSettingList(c *gin.Context) {

	var req model.GetBannerSettingListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		HandleError(c, err)
		return
	}
	req.ShowMode = "PUBLIC"

	data, err := h.configurationService.GetPublicBannerSettingList(req)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (WebGetBannerSettingList) Get Banner Setting Web List
// @Description (WebGetBannerSettingList) Get Banner Setting Web List
// @Tags Configuration Banner Setting
// @Accept json
// @Produce json
// @Param _ query model.GetBannerSettingListRequest true "GetBannerSettingListRequest"
// @Success 200 {object} []model.GetBannerSettingListResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/configuration/banner [get]
func (h *configurationController) WebGetBannerSettingList(c *gin.Context) {

	var req model.GetBannerSettingListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		HandleError(c, err)
		return
	}
	req.ShowMode = "LOGEDIN"

	data, err := h.configurationService.GetPublicBannerSettingList(req)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary Upload Image To S3 Config Banner
// @Description Upload Image To S3 Config Banner
// @Tags Configuration - Web
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param file formData file true "file"
// @Success 200 {object} model.FileUploadResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/upload/banner/image [post]
func (h *configurationController) uploadImageToS3ConfigBanner(c *gin.Context) {

	data, err := h.configurationService.UploadImageToS3ConfigBanner(c.Request)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary Get Configuration Check Show Transaction
// @Description Get Configuration Check Show Transaction
// @Tags Configuration
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.ConfigurationCheckShowTransactionResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/configuration/check-show-transaction [get]
func (h *configurationController) GetConfigurationCheckShowTransaction(c *gin.Context) {

	data, err := h.configurationService.GetConfigurationCheckShowTransactionResponse()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary Get Configuration Register Format
// @Description Get Configuration Register Format
// @Tags Configuration - Web
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} []model.SelectOptions
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/options/register-format [get]
func (h *configurationController) GetConfigurationRegisterFormat(c *gin.Context) {

	data, err := h.configurationService.GetConfigurationRegisterFormat()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary Web Get Configuration Register Format
// @Description Web Get Configuration Register Format
// @Tags Configuration - Web
// @Accept json
// @Produce json
// @Success 200 {object} model.WebGetConfigurationRegisterFormatResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/configuration/register-format [get]
func (h *configurationController) WebGetConfigurationRegisterFormat(c *gin.Context) {

	data, err := h.configurationService.WebGetConfigurationRegisterFormat()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary Update Activity Menu
// @Description Update Activity Menu
// @Tags Configuration - activity
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Param body body model.UpdateActivityMenuRequest true "body"
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/activity-menu/{id} [put]
func (h *configurationController) UpdateActivityMenu(c *gin.Context) {

	var body model.UpdateActivityMenuRequest
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}

	var param model.GetByIdRequest
	if err := c.ShouldBindUri(&param); err != nil {
		HandleError(c, err)
		return
	}

	timeNow := time.Now().UTC()
	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))
	body.UpdatedByID = &adminId
	body.UpdatedAt = &timeNow
	body.Id = param.Id

	if err := h.configurationService.UpdateActivityMenu(body); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, "success")
}

// @Summary Sort Activity Menu
// @Description Sort Activity Menu
// @Tags Configuration - activity
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param req body model.DragSortRequest true "body"
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/activity-menu/sort [put]
func (h *configurationController) SortActivityMenu(c *gin.Context) {

	var req model.DragSortRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.configurationService.SortActivityMenu(req); err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, "success")
}

// @Summary Upload Image activity menu
// @Description Upload Image activity menu
// @Tags Configuration - activity
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param file formData file true "file"
// @Success 200 {object} model.FileUploadResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/activity-menu/upload/image [post]
func (h *configurationController) UploadImageActivityMenu(c *gin.Context) {

	data, err := h.configurationService.UploadImageActivityMenu(c.Request)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary Get Activity Menu
// @Description Get Activity Menu
// @Tags Configuration - activity
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.GetActivityMenuRequest true "GetActivityMenuRequest"
// @Success 200 {object} []model.GetActivityMenuResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/activity-menu [get]
func (h *configurationController) GetActivityMenu(c *gin.Context) {

	var req model.GetActivityMenuRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		HandleError(c, err)
		return
	}

	// set default to front end
	req.Lang = "th"

	data, err := h.configurationService.GetActivityMenu(req)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary Get Activity Menu By Id
// @Description Get Activity Menu By Id en, th, mm, la, cn
// @Tags Configuration - activity
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.GetActivityMenuItemRequest true "GetActivityMenuItemRequest"
// @Success 200 {object} model.GetActivityMenuResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/activity-menu/item [get]
func (h *configurationController) GetActivityMenuItem(c *gin.Context) {

	var req model.GetActivityMenuItemRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.configurationService.GetActivityMenuItem(req)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary Get Running Message
// @Description Get Running Message
// @Tags Configuration - Web
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.RunningMessageResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /configuration/running-message [get]
func (h *configurationController) GetRunningMessage(c *gin.Context) {

	data, err := h.configurationService.GetRunningMessage()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}
