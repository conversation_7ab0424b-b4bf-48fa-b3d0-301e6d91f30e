package handler

import (
	"cybergame-api/middleware"
	"cybergame-api/model"
	repositoryV2 "cybergame-api/repository/v2"
	"cybergame-api/service"
	serviceV2 "cybergame-api/service/v2"
	"io"
	"log"
	"os"
	"strconv"

	"cybergame-api/repository"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"gorm.io/gorm"
)

type cronController struct {
	cronService       service.CronService
	userPlaylog       service.UserPlaylogService
	userQuestPlaylogs serviceV2.UserQuestPlayLogV2Service
	userDailyQuestV2  serviceV2.DailyQuestService
}

func newCronController(
	cronService service.CronService,
	userPlaylog service.UserPlaylogService,
	userQuestPlaylogs serviceV2.UserQuestPlayLogV2Service,
	userDailyQuestV2 serviceV2.DailyQuestService,
) cronController {
	return cronController{cronService, userPlaylog, userQuestPlaylogs, userDailyQuestV2}
}

func CronController(r *gin.RouterGroup, db *gorm.DB) {

	singleSession := middleware.SingleSession(db)

	repo := repository.NewAgentInfoRepository(db)
	afRepo := repository.NewAffiliateRepository(db)
	alRepo := repository.NewAllianceRepository(db)
	cronService := service.NewCronService(repo, afRepo, alRepo)
	userPlaylogRepo := repository.NewUserPlaylogRepository(db)
	userPlaylogService := service.NewUserPlaylogService(userPlaylogRepo)

	userQuestPlaylogRepo := repositoryV2.NewUserQuestPlayLogV2Repository(db)
	agentConnectV2Repo := repositoryV2.NewAgentConnectV2Repository(db)
	dailyQuestRepo := repositoryV2.NewDailyQuestRepository(db)
	userQuestPlaylogService := serviceV2.NewUserQuestPlayLogV2Service(repo, userQuestPlaylogRepo, agentConnectV2Repo, dailyQuestRepo)

	//dailyQuestRepo := repositoryV2.NewDailyQuestRepository(db)
	cronRepository := repository.NewCronRepository(db)
	//userQuestPlayLogRepo := repository.NewUserQuestPlayLogV2Repository(db)
	userCreditRepository := repository.NewUserCreditRepository(db)
	turnoverRepository := repository.NewTurnoverRepository(db)
	agentInfoRepository := repository.NewAgentInfoRepository(db)
	userDailyQuestV2Service := serviceV2.NewDailyQuestService(
		db,
		dailyQuestRepo,
		cronRepository,
		userQuestPlaylogRepo,
		userCreditRepository,
		turnoverRepository,
		agentInfoRepository,
	)

	handler := newCronController(cronService, userPlaylogService, userQuestPlaylogService, userDailyQuestV2Service)

	cronRoute := r.Group("/cronjobs")
	cronRoute.GET("/simplewinlose", handler.simplewinlose)
	cronRoute.GET("/get-user-playlog", handler.runGetUserTodayPlayLog)
	cronRoute.GET("/delete-expired-log", handler.CronDeleteLog)
	cronRoute.GET("/get-user-quest-playlog", handler.getUserQuestPlaylogs)
	cronRoute.GET("/get-check-user-quest", handler.checkUserQuest)

	manualRoute := r.Group("/run-manuals", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	manualRoute.GET("/simplewinlose", handler.simplewinloseByDate)
	manualRoute.GET("/get-user-playlog", handler.runGetUserPlayLogByDate)
	manualRoute.GET("/calc-alliance-income", handler.cronCreateAllianceWinloseIncome)
	manualRoute.GET("/recalc-alliance-income", handler.cronRecalcAllianceWinloseIncome)
	manualRoute.GET("/test-calc-only", handler.cronCalculateAfAlOnly)
	manualRoute.GET("/migate-aff", handler.CronMigrateOldAff)
	manualRoute.GET("/show-agc-simplewinlose-g", handler.viewAgcSimpleWinLoseGreen)
	manualRoute.GET("/show-agc-simplewinlose-tt", handler.viewAgcSimpleWinLoseTidtech)
	manualRoute.POST("/migrate-play-log-status", handler.migrateOldAgcPlaylogStatus)
	manualRoute.POST("/migrate-aff-level/:id", handler.migrateAffiliateUserAllLevel)
	manualRoute.POST("/update-aff-total/:id", handler.migrateAffiliateTotalMember)
	manualRoute.GET("/get-user-quest-playlog", handler.getUserQuestPlaylogsByDate)
	manualRoute.GET("/get-check-user-quest", handler.checkUserQuestByDate)

	debugRoute := r.Group("/show-debug", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	debugRoute.GET("/show-calc-income/:id", handler.showAllianceWinloseIncome)

	mockerRoute := r.Group("/mocker")
	mockerRoute.POST("/alliance-income", handler.testAddAllianceWinloseIncome)
	mockerRoute.POST("/check-aff-exp", handler.checkExpireAffiliateTransaction)

	webhookRoute := r.Group("/webhook")
	webhookRoute.POST("/lottery/play-log/callback", handler.createLotteryPlayLogWebhook)

}

// @Summary (showAllianceWinloseIncome) พันธมิตร - แสดงการคำนวนรายได้
// @Description (showAllianceWinloseIncome) พันธมิตร - แสดงการคำนวนรายได้
// @Tags Alliances - From Admin (DEBUG)
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "Income ID"
// @Success 200 {object} model.AllianceMemberTotalIncomeResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /show-debug/show-calc-income/{id} [get]
func (h cronController) showAllianceWinloseIncome(c *gin.Context) {

	id := c.Param("id")
	toInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleError(c, err)
		return
	}

	r1, r2, err := h.cronService.ShowAllianceWinloseIncome(toInt)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, gin.H{"r1": r1, "r2": r2})
}

// @Summary Run Cronjob Player Summary
// @Description Run Cronjob Player Summary
// @Tags Crons
// @Accept json
// @Produce json
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /cronjobs/simplewinlose [get]
func (h cronController) simplewinlose(c *gin.Context) {

	err := h.cronService.CronSimpleWinLose()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "success"})
}

// @Summary (simplewinloseByDate) Run Cronjob Player Summary
// @Description (simplewinloseByDate) Run Cronjob Player Summary
// @description # statementDate คือ วันที่เล่นเกม เป็นยอดวันที่เล่น ไม่ใช่วันที่ปิดยอด
// @Tags Crons
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.CronCreatePlayLogRequest true "CronCreatePlayLogRequest"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /run-manuals/simplewinlose [get]
func (h cronController) simplewinloseByDate(c *gin.Context) {

	var query model.CronCreatePlayLogRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	err := h.cronService.CronSimpleWinLoseByDate(query.StatementDate)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "success"})
}

// @summary (cronCreateAllianceWinloseIncome) คำนวนรายได้จาก Play_log
// @description (cronCreateAllianceWinloseIncome) คำนวนรายได้จาก Play_log <span style="color:blue">
// @description # statementDate คือ วันที่เล่นเกม เป็นยอดวันที่เล่น ไม่ใช่วันที่ปิดยอด
// @tags Crons
// @Security BearerAuth
// @accept json
// @produce json
// @param _ query model.CronAllianceIncomeCalcRequest true "query"
// @success 200 {object} model.Success
// @failure 400 {object} handler.ErrorResponse
// @router /run-manuals/calc-alliance-income [get]
func (h cronController) cronCreateAllianceWinloseIncome(c *gin.Context) {

	query := model.CronAllianceIncomeCalcRequest{}
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}

	err := h.cronService.CronCreateAllianceWinloseIncome(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "Success"})
}

// @summary (cronRecalcAllianceWinloseIncome) คำนวนรายได้จาก Play_log ใหม่ ใช้รายการเดิม
// @description (cronRecalcAllianceWinloseIncome) คำนวนรายได้จาก Play_log <span style="color:blue">
// @description # statementDate คือ วันที่เล่นเกม เป็นยอดวันที่เล่น ไม่ใช่วันที่ปิดยอด
// @tags Crons
// @Security BearerAuth
// @accept json
// @produce json
// @param _ query model.CronAllianceIncomeRecalcRequest true "query"
// @success 200 {object} model.Success
// @failure 400 {object} handler.ErrorResponse
// @router /run-manuals/recalc-alliance-income [get]
func (h cronController) cronRecalcAllianceWinloseIncome(c *gin.Context) {

	query := model.CronAllianceIncomeRecalcRequest{}
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}

	err := h.cronService.CronRecalcAllianceWinloseIncome(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "Success"})
}

// @Summary (runGetUserTodayPlayLog) ดึงยอดได้เสียของวันนี้ ใช้ทำเทิร์นโอเวอร์
// @Description (runGetUserTodayPlayLog) ดึงยอดได้เสียของวันนี้
// @Tags Crons
// @Accept json
// @Produce json
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /cronjobs/get-user-playlog [get]
func (h cronController) runGetUserTodayPlayLog(c *gin.Context) {

	if err := h.userPlaylog.RunGetUserTodayPlayLog(); err != nil {
		HandleError(c, err)
		return
	}
	if err := h.userPlaylog.RunTodayAffiliateUserList(); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "success"})
}

// @Summary (RunGetUserPlayLogByDate)
// @Description (RunGetUserPlayLogByDate)
// @description # statementDate คือ วันที่เล่นเกม เป็นยอดวันที่เล่น ไม่ใช่วันที่ปิดยอด
// @Tags Crons
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.CronCreatePlayLogRequest true "CronCreatePlayLogRequest"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /run-manuals/get-user-playlog [get]
func (h cronController) runGetUserPlayLogByDate(c *gin.Context) {

	var query model.CronCreatePlayLogRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	err := h.userPlaylog.RunGetUserPlayLogByDate(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "success"})
}

// @Summary (testAddAllianceWinloseIncome)
// @Description (testAddAllianceWinloseIncome)
// @Tags Tester - Mocker
// @Accept json
// @Produce json
// @Param body body model.TestAddAllianceIncomeRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /mocker/alliance-income [post]
func (h cronController) testAddAllianceWinloseIncome(c *gin.Context) {

	body := model.TestAddAllianceIncomeRequest{}
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	ginMode := os.Getenv("GIN_MODE")
	if ginMode == "debug" {
		if err := h.cronService.TestAddAllianceWinloseIncome(body); err != nil {
			HandleError(c, err)
			return
		}
	} else {
		// สำหรับ production ถ้ามีการใช้งานแสดงว่าหลุด
		if err := h.cronService.CreateSystemLog("TestAddAllianceWinloseIncome", body); err != nil {
			HandleError(c, err)
			return
		}
	}
	c.JSON(201, model.Success{Message: "Updated success"})
}

// @Summary (checkExpireAffiliateTransaction)
// @Description (checkExpireAffiliateTransaction)
// @Tags Tester - Mocker
// @Accept json
// @Produce json
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /mocker/check-aff-exp [post]
func (h cronController) checkExpireAffiliateTransaction(c *gin.Context) {

	ginMode := os.Getenv("GIN_MODE")
	if ginMode == "debug" {
		if err := h.cronService.CronExpireAffiliateTransaction(); err != nil {
			HandleError(c, err)
			return
		}
	} else {
		// สำหรับ production ถ้ามีการใช้งานแสดงว่าหลุด
		if err := h.cronService.CreateSystemLog("CronExpireAffiliateTransaction", nil); err != nil {
			HandleError(c, err)
			return
		}
	}
	c.JSON(201, model.Success{Message: "Updated success"})
}

// CronDeleteLog() (string, error)
// @Summary (CronDeleteLog) ลบข้อมูล Log หลัง 30 วัน
// @Description (CronDeleteLog) ลบข้อมูล Log หลัง 30 วัน
// @Description DELETE AGENT LOG
// @Description DELETE BANK TRANSACTION LOG
// @Description DELETE PAYGATE HENG WEBHOOK
// @Description DELETE PROMOTION WEB USER LOG
// @Description DELETE USER LOGIN LOG
// @Description DELETE WEBHOOK LOG
// @Tags Crons
// @Accept json
// @Produce json
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /cronjobs/delete-expired-log [get]
func (h cronController) CronDeleteLog(c *gin.Context) {

	message, err := h.cronService.CronDeleteLog()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, message)
}

// @Summary (getUserQuestPlaylogs) ดึงข้อมูล User Quest Playlog
// @Description (getUserQuestPlaylogs) ดึงข้อมูล User Quest Playlog
// @Tags Crons
// @Accept json
// @Produce json
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /cronjobs/get-user-quest-playlog [get]
func (h cronController) getUserQuestPlaylogs(c *gin.Context) {
	err := h.userQuestPlaylogs.GetUserQuestPlayLogs()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "success"})
}

// @Summary (checkUserQuest) ตรวจสอบ User Quest Playlog ตามวันที่
// @Description (checkUserQuest) ตรวจสอบ User Quest Playlog ตามวันที่
// @Tags Crons
// @Accept json
// @Produce json
// @Param _ query model.CronCreatePlayLogRequest true "CronCreatePlayLogRequest"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /cronjobs/get-check-user-quest [get]
func (h cronController) checkUserQuest(c *gin.Context) {
	err := h.userDailyQuestV2.CheckUserQuest()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "success"})
}

// @Summary (cronCalculateAfAlOnly) ลองคำนวนรายได้จาก Play_log ใหม่ ใช้รายการเดิม
// @Description (cronCalculateAfAlOnly)
// @Security BearerAuth
// @Tags Crons
// @Accept json
// @Produce json
// @Param _ query model.CronCreatePlayLogRequest true "CronCreatePlayLogRequest"
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /run-manuals/test-calc-only [get]
func (h cronController) cronCalculateAfAlOnly(c *gin.Context) {

	var query model.CronCreatePlayLogRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.cronService.CronCalculateAfAlOnly(query.StatementDate)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithData{Message: "Success", Data: data})

}

// @Summary (CronMigrateOldAff) ลองคำนวนรายได้จาก Play_log ใหม่ ใช้รายการเดิม
// @Description (CronMigrateOldAff)
// @Security BearerAuth
// @Tags Crons
// @Accept json
// @Produce json
// @Success 200 {object} model.SuccessWithData
// @Failure 400 {object} handler.ErrorResponse
// @Router /run-manuals/migate-aff [get]
func (h cronController) CronMigrateOldAff(c *gin.Context) {

	data1, data2, err := h.cronService.CronMigrateOldAff()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithData{Message: "Success", Data: struct {
		UserAffIncomeReponse      interface{} `json:"userAffIncomeReponse"`
		CronMigrateOldAffResponse interface{} `json:"cronMigrateOldAffResponse"`
	}{UserAffIncomeReponse: data1, CronMigrateOldAffResponse: data2}})

}

// @Summary (viewAgcSimpleWinLoseGreen)
// @Description (viewAgcSimpleWinLoseGreen)
// @Security BearerAuth
// @Tags Crons
// @Accept json
// @Produce json
// @Param _ query model.ViewAgcSimpleWinLoseListRequest true "ViewAgcSimpleWinLoseListRequest"
// @Success 200 {object} model.AgcSimpleWinloseResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /run-manuals/show-agc-simplewinlose-g [get]
func (h cronController) viewAgcSimpleWinLoseGreen(c *gin.Context) {

	var query model.ViewAgcSimpleWinLoseListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	resp, err := h.cronService.ViewAgcSimpleWinLoseGreen(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, resp)
}

// @Summary (viewAgcSimpleWinLoseTidtech)
// @Description (viewAgcSimpleWinLoseTidtech)
// @Security BearerAuth
// @Tags Crons
// @Accept json
// @Produce json
// @Param _ query model.ViewAgcSimpleWinLoseListRequest true "ViewAgcSimpleWinLoseListRequest"
// @Success 200 {object} model.AgcSimpleWinloseResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /run-manuals/show-agc-simplewinlose-tt [get]
func (h cronController) viewAgcSimpleWinLoseTidtech(c *gin.Context) {

	var query model.ViewAgcSimpleWinLoseListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	resp, err := h.cronService.ViewAgcSimpleWinLoseTidtech(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, resp)
}

// @Summary (migrateOldAgcPlaylogStatus)
// @Description (migrateOldAgcPlaylogStatus)
// @Security BearerAuth
// @Tags Crons
// @Accept json
// @Produce json
// @Success 200 {object} model.AgcSimpleWinloseResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /run-manuals/migrate-play-log-status [post]
func (h cronController) migrateOldAgcPlaylogStatus(c *gin.Context) {

	if err := h.cronService.MigrateOldAgcPlaylogStatus(); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, "DONE")
}

// @Summary (migrateAffiliateUserAllLevel)
// @Description (migrateAffiliateUserAllLevel)
// @Security BearerAuth
// @Tags Crons
// @Accept json
// @Produce json
// @Param id path int true "user id"
// @Success 200 {object} model.AgcSimpleWinloseResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /run-manuals/migrate-aff-level/{id} [post]
func (h cronController) migrateAffiliateUserAllLevel(c *gin.Context) {

	id := c.Param("id")
	identifier, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleError(c, err)
		return
	}

	if err := h.cronService.MigrateAffiliateUserAllLevel(identifier); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, "DONE")
}

// @Summary (migrateAffiliateTotalMember)
// @Description (migrateAffiliateTotalMember)
// @Security BearerAuth
// @Tags Crons
// @Accept json
// @Produce json
// @Param id path int true "user id"
// @Success 200 {object} model.AgcSimpleWinloseResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /run-manuals/update-aff-total/{id} [post]
func (h cronController) migrateAffiliateTotalMember(c *gin.Context) {

	id := c.Param("id")
	identifier, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleError(c, err)
		return
	}

	if err := h.cronService.MigrateAffiliateTotalMember(identifier); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, "DONE")
}

// @Summary (getUserQuestPlaylogsByDate) ดึงข้อมูล User Quest Playlog ตามวันที่
// @Description (getUserQuestPlaylogsByDate) ดึงข้อมูล User Quest Playlog ตามวันที่
// @Tags Crons
// @Accept json
// @Produce json
// @Param _ query model.CronCreatePlayLogRequest true "CronCreatePlayLogRequest"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /run-manuals/get-user-quest-playlog [get]
func (h cronController) getUserQuestPlaylogsByDate(c *gin.Context) {
	var query model.CronCreatePlayLogRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	err := h.userQuestPlaylogs.GetUserQuestPlayLogsByDate(query.StatementDate)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "success"})
}

// @Summary (checkUserQuestByDate) ตรวจสอบ User Quest ตามวันที่
// @Description (checkUserQuestByDate) ตรวจสอบ User Quest ตามวันที่
// @Tags Crons
// @Accept json
// @Produce json
// @Param _ query model.CronCreatePlayLogRequest true "CronCreatePlayLogRequest"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /run-manuals/get-check-user-quest [get]
func (h cronController) checkUserQuestByDate(c *gin.Context) {
	var query model.CronCreatePlayLogRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	err := h.userDailyQuestV2.CheckUserQuestByDate(query.StatementDate)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "success"})
}

// @Summary (createLotteryPlayLogWebhook) สำหรับรับ Webhook จาก Payonex
// @Description (createLotteryPlayLogWebhook)
// @Tags Webhook - Lottery Playlog
// @Produce json
// @Param body body string true "body"
// @Success 201 {object} model.SuccessWithId
// @Failure 400 {object} handler.ErrorResponse
// @Router /webhook/lottery/play-log/callback [post]
func (h cronController) createLotteryPlayLogWebhook(c *gin.Context) {

	var createReq model.LotteryWebhookRequest
	createReq.JsonPayload = "{}"

	body := c.Request.Body
	responseData, err := io.ReadAll(body)
	if err != nil {
		log.Println(err)
	} else {
		createReq.JsonPayload = string(responseData)
	}

	if err := h.cronService.CreateLotteryPlayLogWebhook(createReq); err != nil {
		log.Println("CreateLotteryPlayLogWebhook.ERROR", err.Error())
	}
	// c.JSON(201, model.Success{Message: "Created success"})
	// later ต้อง return อะไรบ้าง
	c.String(200, "ok")
}
