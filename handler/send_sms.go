package handler

import (
	"cybergame-api/helper"
	"cybergame-api/middleware"
	"cybergame-api/model"
	"cybergame-api/repository"
	"cybergame-api/service"
	"strings"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type sendSmsController struct {
	sendSmsService service.SendSmsService
}

func newSendSmsController(
	sendSmsService service.SendSmsService,
) sendSmsController {
	return sendSmsController{sendSmsService}
}

func SendSmsController(r *gin.RouterGroup, db *gorm.DB) {

	singleSession := middleware.SingleSession(db)
	role := middleware.Role(db)

	repo := repository.NewSendSmsRepository(db)
	service := service.NewSendSmsService(repo)
	handler := newSendSmsController(service)

	sendSmsRoute := r.Group("/send-sms", middleware.AuthorizeAdmin, singleSession.SingleAdminSession(), role.CheckPermission([]string{"member"}))
	sendSmsRoute.POST("/user-inactive", handler.sendSmsUserInactive)
	sendSmsRoute.GET("/list", handler.getSendSmsList)
	sendSmsRoute.GET("/status-options", handler.getSendSmsStatusOptions)
	sendSmsRoute.GET("/sender-name-options", handler.getSendSmsSenderNameOptions)
	sendSmsRoute.GET("/spam-word-list", handler.getSendSmsSpamWordList)
	sendSmsRoute.POST("/spam-word-check", handler.checkSendSmsSpamWord)
}

// เวลา แก้จะได้แก้ที่เดียว ไม่ migrate ทุกเว็บ แยกกัน กันเวลา outsource ไป deploy api db แยก ที่ อื่น
var SpamWords = []string{
	"ถอนไว", "การันตี", "ถ$นไว", "Black Jack", "เว็บตsง", "black jack", "คุณมีเงินคืน", "ฝากไม่มีขั้นต่ำ", "หาคู่", "พนัน", "betting", "จ่ๅยชัวร์", "sbo", "รูเล็ตต์", "ฟรีสปิu", "ธนาคารประชาชน", "sbotop", "เว็บใหญ่", "รวยไว", "ถ0u", "ได้เงินชัวร์", "@sbotop", "แจกสูตร", "Casino", "@sboltd", "poker", "คาเฟือน", "กาสิโน", "ควย", "จ่ายจริง", "รางวัลใหญ่", "lIตกจริง", "ได้ถ0นชัวร์", "แตกจริง", "ไม่ต้องแชรื", "แจกเงิน", "ได้ถอนชัวร์", "Å", "แตก", "ไม่ต้องลุ้น", "æ", "เล่นง่าย", "sวe", "Ξ", "เว็บตรง", "เงินกู้", "Θ", "โชคดี", "คาชิโน่", "bet", "Σ", "คาสิโu", "Ψ", "คาสิโน", "ค่าft", "Π", "แจกทุนฟรี", "Ω", "คืนยอด", "สปิu", "Λ", "ไม่มีเทิร์น", "จ่ายเร็ว", "สปิน", "Γ", "แตกง่าย", "ไม่ต้องแชร์", "คืนทุน", "φ", "ไม่มีขั้นต่ำ", "รับเละ", "แตกกระจาย", "å", "ได้เงินจริง", "€", "รับปั่น", "ø", "ปรับค่าแตก", "เวu", "ฟรีเครดิต", "Gambler", "llตก", "ถoน", "เครดิตฟรี", "เว็Uตรง", "โอนไว", "แจ็กพอตแตก", "รับปิ้น", "Slot", "▶️", "โบนัสฟรี", "บาคาร่า", "แตnง่าย", "baccarat", "Shit", "จ่ายเต็ม", "สมาชิกใหม่รับ", "gambler", "iiชร์", "Poker", "ฟรีสปิน", "Fuck", "♤", "gambling", "ถอนออโต้", "ไม่มีขั้นต่ําา", "Gambling", "casino", "iiจก", "Baccarat", "รvE", "roulette", "ทำเทิน", "shit", "กลุ่มลับ", "ยอดเสีย", "fuck", "iiตก", "slot", "ถอนกี่ครั้งก็ได้", "สล็อต", "Lกมใxม่", "แจกไม่อั้น", "ยูสใหม่", "ได้รับฟรี", "ค่าน้ําดี", "llจกเงิu", "แตกหนัก", "ไม่โกง", "แบล็คแจ็ค", "ไม่อั้u", "𝔹𝔼𝕋", "sะบบooโต้", "เหี้ย", "ยังไงก็แตก", "ฝากถอนออโต้", "เสี่ยงดวง", "llจก", "เวบตรง", "ฝากง่าย", "สัส", "IIตก", "เช็กซี่", "IIจก", "ถou", "แลกทุกวัน", "IIชร์", "Betting", "เวปตรง", "lกมใxม่", "sexy", "เทิร์นต่ำ", "เว็u", "💵", "sวย", "ไม่ต้องฝาก", "bacara", "เทิร์น", "ถอนได้ไม่อั้น", "รve", "ได้จริง", "สินเชื่อฉุกเฉิน", "ถอนได้ชัวร์", "รวe", "ไม่ต้องแช", "ได้ชัวร์", "สุ่มแจก", "รวย", "ถอนได้เลย", "โป๊กเกอร์", "สลอต", "ใหม่ฟรี", "โป๊", "สล็0ต", "เก่าฟรี", "ถอนไม่อั้น", "เงินด่วน", "วงล้อ", "รวE", "เดิมพัน", "เว็uตรง", "ยื่นกู้", "เสี่ยงโชค", "ฝากไม่มีขั้นต่ําา", "SวE", "HOU", "asshole", "ย้ายมาเล่นเว็บเรา", "เสียคืน", "Roulette", "dickhead", "ไพ่", "รว€", "ถ0น",
}

// @Summary Send SMS to user inactive
// @Description Send SMS to user inactive
// @Tags Send SMS
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.CreateSendSmsRequest true "body"
// @Success 200 {object} model.Success
// @Failure 400 {object} ErrorResponse
// @Router /send-sms/user-inactive [post]
func (s *sendSmsController) sendSmsUserInactive(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	var req model.CreateSendSmsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	for _, spamWord := range SpamWords {
		if strings.Contains(req.Message, spamWord) {
			c.JSON(400, ErrorResponse{Message: "SPAM_MESSAGE"})
			return
		}
	}

	req.CreatedById = adminId

	if err := s.sendSmsService.SendSmsUserInactive(req); err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(201, model.Success{Message: "create success"})
}

// @Summary Get Send SMS spam word list
// @Description Get Send SMS spam word list
// @Tags Send SMS
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.SimpleOption
// @Failure 400 {object} ErrorResponse
// @Router /send-sms/spam-word-list [get]
func (s *sendSmsController) getSendSmsSpamWordList(c *gin.Context) {

	c.JSON(200, SpamWords)

}

// @Summary Get Send SMS spam word check
// @Description Get Send SMS spam word check
// @Tags Send SMS
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.CheckSendSmsMessageSpamRequest true "body"
// @Success 200 {object} model.SimpleOption
// @Failure 400 {object} ErrorResponse
// @Router /send-sms/spam-word-check [post]
func (s *sendSmsController) checkSendSmsSpamWord(c *gin.Context) {

	var req model.CheckSendSmsMessageSpamRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	for _, spamWord := range SpamWords {
		if strings.Contains(req.Message, spamWord) {
			c.JSON(400, ErrorResponse{Message: "SPAM_MESSAGE"})
			return
		}
	}

	c.JSON(200, model.Success{Message: "PASS"})
}

// @Summary Get Send SMS List
// @Description Get Send SMS List
// @Tags Send SMS
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.GetSendSmsListRequest true "GetSendSmsListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} ErrorResponse
// @Router /send-sms/list [get]
func (s *sendSmsController) getSendSmsList(c *gin.Context) {

	var query model.GetSendSmsListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := s.sendSmsService.GetSendSmsList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithPagination{List: data.List, Total: data.Total, Message: "Success"})
}

// @Summary Get Send SMS Status Options
// @Description Get Send SMS Status Options
// @Tags Send SMS
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.SimpleOption
// @Failure 400 {object} ErrorResponse
// @Router /send-sms/status-options [get]
func (s *sendSmsController) getSendSmsStatusOptions(c *gin.Context) {

	data := []model.SimpleOption{
		{Key: model.SEND_SMS_PENDING, Name: "Pending"},
		{Key: model.SEND_SMS_ACCEPTED, Name: "Accepted"},
		{Key: model.SEND_SMS_UNDELIVERED, Name: "Undelivered"},
		{Key: model.SEND_SMS_BLACKLIST, Name: "Blacklist"},
	}
	c.JSON(200, data)
}

// @Summary Get Send SMS Sender Name Options
// @Description Get Send SMS Sender Name Options
// @Tags Send SMS
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.SelectOptions
// @Failure 400 {object} ErrorResponse
// @Router /send-sms/sender-name-options [get]
func (s *sendSmsController) getSendSmsSenderNameOptions(c *gin.Context) {

	data, err := s.sendSmsService.GetSendSmsSenderName()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}
