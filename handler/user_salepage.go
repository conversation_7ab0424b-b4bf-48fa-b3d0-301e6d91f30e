package handler

import (
	"cybergame-api/helper"
	"cybergame-api/middleware"
	"cybergame-api/model"
	"cybergame-api/service"
	"strconv"

	"cybergame-api/repository"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"gorm.io/gorm"
)

type userSalepageController struct {
	userSalepageService service.UserSalepageService
}

func newUserSalepageController(
	userSalepageService service.UserSalepageService,
) userSalepageController {
	return userSalepageController{userSalepageService}
}

func UserSalepageController(r *gin.RouterGroup, db *gorm.DB) {

	singleSession := middleware.SingleSession(db)
	handler := newUserSalepageController(service.NewUserSalepageService(repository.NewUserSalepageRepository(db)))

	whiteRoute := r.Group("/alwhite-member", middleware.AuthorizeUser, singleSession.SingleUserSession())
	whiteRoute.GET("/salepage-summary", handler.getUserSalepageSummaryStat)
	whiteRoute.GET("/salepage-info", handler.getUserSalepageInfo)
	whiteRoute.POST("/salepage-info", handler.updateUserSalepageInfo)
	// custom sale page...
	customSalepageRoute := r.Group("/alwhite-custom", middleware.AuthorizeUser, singleSession.SingleUserSession())
	customSalepageRoute.GET("/salepage-block/list", handler.getCustomSalepageBlockList)
	customSalepageRoute.GET("/salepage-block/:id", handler.getCustomSalepageBlockById)
	customSalepageRoute.POST("/salepage-block/:id", handler.updateCustomSalepageBlock)
	customSalepageRoute.POST("/salepage-block/sort", handler.sortCustomSalepageBlock)
	customSalepageRoute.POST("/salepage-image-upload", handler.uploadImageToS3CustomSalePage)

	// Public route allow all kind of call.
	webRoute := r.Group("/web/user-salepage")
	webRoute.GET("/blocks/:code", handler.getWebCustomSalepageBlockList)
	webRoute.POST("/add-click", handler.increaseTodayLinkClick)
	webRoute.POST("/add-register-click", handler.increaseTodayMemberRegisterClick)
	webRoute.POST("/add-admin-click", handler.increaseTodayAdminClick)
}

// @Summary (getUserSalepageSummaryStat)
// @Description (getUserSalepageSummaryStat)
// @Tags UserSalepages - Web
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.UserSalepageStatSummaryResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /alwhite-member/salepage-summary [get]
func (h *userSalepageController) getUserSalepageSummaryStat(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	data, err := h.userSalepageService.GetUserSalepageStatSummary(userId)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (getUserSalepageInfo) ดึงข้อมูลหน้า Sale page ของ User สร้างลิงค์
// @Description (getUserSalepageInfo)
// @Tags UserSalepages - Web
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.UserSalepageResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /alwhite-member/salepage-info [get]
func (h *userSalepageController) getUserSalepageInfo(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	data, err := h.userSalepageService.GetUserSalepageInfo(userId)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (updateUserSalepageInfo) อัพเดทข้อมูลหน้า Sale page ของ User สร้างลิงค์
// @Description (updateUserSalepageInfo)
// @Tags UserSalepages - Web
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.UserSalepageUpdateRequest true "body"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /alwhite-member/salepage-info [post]
func (h *userSalepageController) updateUserSalepageInfo(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	body := model.UserSalepageUpdateRequest{}
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}
	body.UserId = userId
	if err := validator.New().Struct(body); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.userSalepageService.UpdateUserSalepageInfo(body); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "Updated success"})
}

// @Summary (getCustomSalepageBlockList) ดึงลิสข้อมูล Block ของ Sale page ของ User
// @Description (getCustomSalepageBlockList) No Pagination
// @Tags SalePage - Custom
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} []model.CustomSalepageBlockResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /alwhite-custom/salepage-block/list [get]
func (h *userSalepageController) getCustomSalepageBlockList(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var query model.CustomSalepageBlockListRequest
	// if err := c.ShouldBind(&query); err != nil {
	// 	HandleError(c, err)
	// 	return
	// }
	query.UserId = userId
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	list, err := h.userSalepageService.GetCustomSalepageBlockList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, list)
}

// @Summary (getCustomSalepageBlockById) ดึงข้อมูล Block ของ Sale page ของ User เอามาอัพเดทได้
// @Description (getCustomSalepageBlockById)
// @Tags SalePage - Custom
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 200 {object} model.CustomSalepageBlockResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /alwhite-custom/salepage-block/{id} [get]
func (h *userSalepageController) getCustomSalepageBlockById(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var query model.GetByIdUserRequest
	if err := c.ShouldBindUri(&query); err != nil {
		HandleError(c, err)
		return
	}
	query.UserId = userId
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.userSalepageService.GetCustomSalepageBlockById(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (updateCustomSalepageBlock) อัพเดทข้อมูล Block ของ Sale page ของ User
// @Description (updateCustomSalepageBlock)
// @Tags SalePage - Custom
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Param body body model.CustomSalepageBlockUpdateRequest true "body"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /alwhite-custom/salepage-block/{id} [post]
func (h *userSalepageController) updateCustomSalepageBlock(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	identifier, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		HandleError(c, err)
		return
	}

	req := model.CustomSalepageBlockUpdateRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}
	req.Id = identifier
	req.UserId = userId
	if err := validator.New().Struct(req); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.userSalepageService.UpdateCustomSalepageBlock(req); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "Updated success"})
}

// @Summary (sortCustomSalepageBlock) อัพเดทข้อมูล Block ของ Sale page ของ User
// @Description (sortCustomSalepageBlock)
// @Tags SalePage - Custom
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.DragSortRequest true "body"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /alwhite-custom/salepage-block/sort [post]
func (h *userSalepageController) sortCustomSalepageBlock(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	req := model.DragSortRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(req); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.userSalepageService.SortCustomSalepageBlock(req, userId); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "Updated success"})
}

// @Summary (uploadImageToS3CustomSalePage)
// @Description (uploadImageToS3CustomSalePage)
// @Tags SalePage - Custom
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param file formData file true "file"
// @Success 200 {object} model.FileUploadResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /alwhite-custom/salepage-image-upload [post]
func (h *userSalepageController) uploadImageToS3CustomSalePage(c *gin.Context) {

	data, err := h.userSalepageService.UploadImageToS3CustomSalePage(c.Request)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (getWebCustomSalepageBlockList) หน้าเว็บ ดึงลิสข้อมูล Block ของ Sale page ของ User
// @Description (getWebCustomSalepageBlockList) No Pagination
// @Tags UserSalepages - Web
// @Accept json
// @Produce json
// @Param code path string true "code"
// @Success 200 {object} []model.CustomSalepageBlockResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/user-salepage/blocks/{code} [get]
func (h *userSalepageController) getWebCustomSalepageBlockList(c *gin.Context) {

	identifier := c.Param("code")

	// onError return empty list
	list, err := h.userSalepageService.GetWebCustomSalepageBlockList(identifier)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, list)
}

// @Summary (increaseTodayLinkClick) เพิ่มจำนวนคลิกในวันนี้ จากการเปิดหน้าจอ
// @Description (increaseTodayLinkClick)
// @Tags UserSalepages - Web
// @Accept json
// @Produce json
// @Param body body model.PostUserSalepageRequest true "body"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/user-salepage/add-click [post]
func (h *userSalepageController) increaseTodayLinkClick(c *gin.Context) {

	var req model.PostUserSalepageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(req); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.userSalepageService.IncreaseTodayLinkClick(req); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "Success"})
}

// @Summary (increaseTodayMemberRegisterClick) เพิ่มจำนวนคลิกในวันนี้ จากากรกดปุ่มสมัครสมาชิก
// @Description (increaseTodayMemberRegisterClick)
// @Tags UserSalepages - Web
// @Accept json
// @Produce json
// @Param body body model.PostUserSalepageRequest true "body"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/user-salepage/add-register-click [post]
func (h *userSalepageController) increaseTodayMemberRegisterClick(c *gin.Context) {

	var req model.PostUserSalepageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(req); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.userSalepageService.IncreaseTodayMemberRegisterClick(req); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "Success"})
}

// @Summary (increaseTodayAdminClick) เพิ่มจำนวนคลิกในวันนี้ จากการกดปุ่มแจ้งแอดมิน
// @Description (increaseTodayAdminClick)
// @Tags UserSalepages - Web
// @Accept json
// @Produce json
// @Param body body model.PostUserSalepageRequest true "body"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /web/user-salepage/add-admin-click [post]
func (h *userSalepageController) increaseTodayAdminClick(c *gin.Context) {

	var req model.PostUserSalepageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(req); err != nil {
		HandleError(c, err)
		return
	}

	if err := h.userSalepageService.IncreaseTodayAdminClick(req); err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.Success{Message: "Success"})
}
