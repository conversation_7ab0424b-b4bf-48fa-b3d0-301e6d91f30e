package handler

import (
	"cybergame-api/helper"
	"cybergame-api/middleware"
	"cybergame-api/model"
	"cybergame-api/repository"
	"cybergame-api/service"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type promotionWebController struct {
	promotionWebService service.PromotionWebService
}

func newPromotionWebController(
	promotionWebService service.PromotionWebService,
) promotionWebController {
	return promotionWebController{promotionWebService}
}

func PromotionWebController(r *gin.RouterGroup, db *gorm.DB) {

	singleSession := middleware.SingleSession(db)

	repo := repository.NewPromotionWebRepository(db)
	serviceRepo := repository.NewNotificationRepository(db)
	serviceNoti := service.NewNotificationService(serviceRepo)
	userRepo := repository.NewUserRepository(db)
	perRepo := repository.NewPermissionRepository(db)
	groupRepo := repository.NewGroupRepository(db)
	otpRepo := repository.NewOtpRepository(db)
	agentInfoRepo := repository.NewAgentInfoRepository(db)
	recommendRepo := repository.NewRecommendRepository(db)
	afRepo := repository.NewAffiliateRepository(db)
	notiRepo := repository.NewNotificationRepository(db)
	notiService := service.NewNotificationService(notiRepo)
	actionService := service.NewAdminActionService(repository.NewAdminActionRepository(db))
	userService := service.NewUserService(
		userRepo,
		db,
		perRepo,
		groupRepo,
		otpRepo,
		agentInfoRepo,
		recommendRepo,
		afRepo,
		notiService,
		actionService,
	)
	service := service.NewPromotionWebService(repo, db, serviceNoti, userService)
	handler := newPromotionWebController(service)

	role := middleware.Role(db)

	// backoffice
	rootPromotionWeb := r.Group("/promotion-web", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	rootPromotionWeb.POST("", role.CheckPermission([]string{"promotion"}), handler.createPromotionWeb)
	rootPromotionWeb.GET("/:id", role.CheckPermission([]string{"promotion_view"}), handler.getPromotionWebById)
	rootPromotionWeb.GET("/internal/list", handler.getPromotionWebInternalList)
	rootPromotionWeb.GET("/list", role.CheckPermission([]string{"promotion_view"}), handler.getPromotionWebList)
	rootPromotionWeb.PUT("/:id", role.CheckPermission([]string{"promotion_edit"}), handler.updatePromotionWeb)
	rootPromotionWeb.PUT("/cancel/:id", role.CheckPermission([]string{"promotion_canceled"}), handler.cancelPromotionWeb)
	rootPromotionWeb.PUT("/user/cancel/:id", handler.cancelPromotionWebUserById)
	rootPromotionWeb.GET("/user/list-collected", handler.promotionWebUserGetListByUserId)
	rootPromotionWeb.GET("/user/turnover-win-lose-summary/:id", handler.getPromotionWebUserWinLoseSummaryCurrentUser)
	rootPromotionWeb.GET("/slide-list", handler.getPromotionWebSlideListOnlyActive)
	rootPromotionWeb.POST("/upload/cover", handler.uploadPromotionCoverToCloudFlare)
	rootPromotionWeb.DELETE("/:id", role.CheckPermission([]string{"promotion_delete"}), handler.deletePromotionWeb)
	rootPromotionWeb.PUT("/sort-priority-order", handler.sortPromotionWebPriorityOrder)
	rootPromotionWeb.PUT("/unlock-user-credit", role.CheckPermission([]string{"promotion_approved"}), handler.lockCreditPromotionUpdate)
	// Report
	rootPromotionWeb.GET("/history-report", handler.GetUserPromotionReportList)

	option := rootPromotionWeb.Group("/option")
	option.GET("/type", handler.getPromotionWebType)
	option.GET("/status", handler.getPromotionWebStatus)
	option.GET("/bonus-condition", handler.getPromotionWebBonusCondition)
	option.GET("/bonus-type", handler.getPromotionWebBonusType)
	option.GET("/turnover-type", handler.getPromotionWebTurnoverType)
	option.GET("/date-type", handler.getpromotionWebDateType)
	option.GET("/user-status", handler.getPromotionWebUserStatus)
	option.GET("/promotion-user-status", handler.getPromotionWebUserStatusOptions)

	rootPromotionWeb.GET("/user/:userId", handler.getUserPromotionWebByUserId)
	rootPromotionWeb.GET("/user/list", handler.getUserPromotionWebList)
	rootPromotionWeb.GET("/summary", handler.promotionWebSummary)
	rootPromotionWeb.GET("/user/summary", handler.promotionWebUserSummary)

	// web
	rootUserPromotionWeb := r.Group("/promotion-web", middleware.AuthorizeUser)
	user := rootUserPromotionWeb.Group("/user")
	user.POST("", handler.createUserCollectPromotionWeb)
	user.GET("/show", handler.showPromotionWebForUser)
	user.GET("/show/:id", handler.showPromotionWebForUserById)
	user.GET("/locked-credit", handler.checkIsLockedCreditPromotion)
	user.GET("/link/:hiddenUrlLink", handler.showPromotionWebForHiddenUrl)

	rootPublicPromotionWeb := r.Group("/promotion-web")
	public := rootPublicPromotionWeb.Group("/public")
	public.GET("/show", handler.showPromotionWebForPublic)
	public.GET("/show/:id", handler.ShowPromotionWebById)
	public.GET("/show/winlose", middleware.AuthorizeAdmin, singleSession.SingleAdminSession(), handler.singleWinLoseByMember)
	public.POST("/check-user-promotion", middleware.AuthorizeAdmin, singleSession.SingleAdminSession(), handler.checkUserPromotion)
	public.GET("/auto-generate-user-for-test", middleware.AuthorizeAdmin, singleSession.SingleAdminSession(), handler.autoGenerateUserForTest)
	public.GET("/check-promotion-withdraw", middleware.AuthorizeAdmin, singleSession.SingleAdminSession(), handler.checkPromotionWithdraw)
	public.POST("/new-member-free", middleware.AuthorizeAdmin, singleSession.SingleAdminSession(), handler.checkUserPromotionOnlyNewMemberFree)

	test := r.Group("/promotion-web/check-locked-user-credit")
	test.GET("/:userId", handler.checkIsLockedCreditPromotionByUserId)

	// bof lock credit
	rootLockCredit := r.Group("/lock-credit", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	rootLockCredit.GET("/withdraw-list", handler.GetLockCreditWithdrawList)
	rootLockCredit.PUT("/withdraw-unlock/:id", handler.UnLockCreditWithdraw)

	// web lock credit
	rootLockCreditUser := r.Group("/user/lock-credit", middleware.AuthorizeUser)
	rootLockCreditUser.GET("/withdraw-check", handler.CheckLockCreditWithdrawByUserId)

	// /backup/lock-credit/withdraw-migrate [post]
	rootLockCreditMigrate := r.Group("/backup/lock-credit")
	rootLockCreditMigrate.POST("/withdraw-migrate", handler.MigrateBackUpLockCreditBack)

}

// @Summary Get PromotionWebType
// @Description Get PromotionWebType
// @Tags Promotion Web - Options
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.PromotionWebTypeResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /promotion-web/option/type [get]
func (h *promotionWebController) getPromotionWebType(c *gin.Context) {

	options, err := h.promotionWebService.GetPromotionWebType()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, options)
}

// @Summary Get PromotionWebStatus
// @Description Get PromotionWebStatus
// @Description | Query Parameter | Description       |
// @Description |------------------ | ----------------------------------------------|
// @Description | ALL    | ทั้งหมด |
// @Description | ---------------- | ---------------------------------------------- |
// @Tags Promotion Web - Options
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param selected query string false "selected"
// @Success 200 {object} model.PromotionWebStatusResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /promotion-web/option/status [get]
func (h *promotionWebController) getPromotionWebStatus(c *gin.Context) {

	// [2023/12/13] เปลี่ยนแบบเอา ครบทุกอัน
	// [2023/12/13] กลับมาแบ่งตาม create update ด้วย ALL
	var req model.PromotionWebOptionRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		HandleError(c, err)
		return
	}

	options, err := h.promotionWebService.GetPromotionWebStatus(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, options)
}

// @Summary Get PromotionWebBonusCondition
// @Description Get PromotionWebBonusCondition
// @Tags Promotion Web - Options
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.PromotionWebBonusConditionResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /promotion-web/option/bonus-condition [get]
func (h *promotionWebController) getPromotionWebBonusCondition(c *gin.Context) {

	options, err := h.promotionWebService.GetPromotionWebBonusCondition()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, options)
}

// @Summary Get PromotionWebBonusType
// @Description Get PromotionWebBonusType
// @Tags Promotion Web - Options
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.PromotionWebBonusTypeResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /promotion-web/option/bonus-type [get]
func (h *promotionWebController) getPromotionWebBonusType(c *gin.Context) {

	options, err := h.promotionWebService.GetPromotionWebBonusType()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, options)
}

// @Summary Get PromotionWebTurnoverType
// @Description Get PromotionWebTurnoverType
// @Tags Promotion Web - Options
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.PromotionWebTurnoverTypeResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /promotion-web/option/turnover-type [get]
func (h *promotionWebController) getPromotionWebTurnoverType(c *gin.Context) {

	options, err := h.promotionWebService.GetPromotionWebTurnoverType()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, options)
}

// @Summary Get GetpromotionWebDateType
// @Description Get GetpromotionWebDateType
// @Tags Promotion Web - Options
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.GetpromotionWebDateTypeResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /promotion-web/option/date-type [get]
func (h *promotionWebController) getpromotionWebDateType(c *gin.Context) {

	options, err := h.promotionWebService.GetpromotionWebDateType()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, options)
}

// @Summary (createPromotionWeb) Create PromotionWeb เพิ่มโปรโมชั่นเว็บ
// @Description (createPromotionWeb) Create PromotionWeb
// @Tags Promotion Web - backoffice
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param req body model.PromotionWebCreateRequest true "body"
// @Success 200 {object} int64
// @Failure 400 {object} handler.ErrorResponse
// @Router /promotion-web [post]
func (h *promotionWebController) createPromotionWeb(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	var req model.PromotionWebCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	req.CreatedByAdminId = adminId
	id, err := h.promotionWebService.CreatePromotionWeb(req)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, id)
}

// @Summary (getPromotionWebById) Get PromotionWeb By Id
// @Description (getPromotionWebById) Get PromotionWeb By Idupdate_by_admin_id
// @Tags Promotion Web - backoffice
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /promotion-web/{id} [get]
func (h *promotionWebController) getPromotionWebById(c *gin.Context) {

	var req model.PromotionWebGetByIdRequest
	if err := c.ShouldBindUri(&req); err != nil {
		HandleError(c, err)
		return
	}

	promotionWeb, err := h.promotionWebService.GetPromotionWebById(req.Id)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, promotionWeb)
}

// @Summary Get PromotionWeb List (ไม่ต้องใช้)
// @Description Get PromotionWeb List
// @Tags Promotion Web - backoffice
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.PromotionWebGetInternalListRequest true "PromotionWebGetInternalListRequest"
// @Success 200 {object} model.SuccessWithList
// @Failure 400 {object} handler.ErrorResponse
// @Router /promotion-web/internal/list [get]
func (h *promotionWebController) getPromotionWebInternalList(c *gin.Context) {

	var req model.PromotionWebGetInternalListRequest
	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.promotionWebService.GetPromotionWebInternalList(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary Get PromotionWeb List
// @Description Get PromotionWeb List
// @Tags Promotion Web - backoffice
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.PromotionWebGetListRequest true "PromotionWebGetListRequest"
// @Success 200 {object} model.SuccessWithList
// @Failure 400 {object} handler.ErrorResponse
// @Router /promotion-web/list [get]
func (h *promotionWebController) getPromotionWebList(c *gin.Context) {

	var req model.PromotionWebGetListRequest
	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}
	data, err := h.promotionWebService.GetPromotionWebList(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary Update PromotionWeb
// @Description Update PromotionWeb
// @Tags Promotion Web - backoffice
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Param body body model.PromotionWebUpdateRequest true "body"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /promotion-web/{id} [put]
func (h *promotionWebController) updatePromotionWeb(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	var req model.PromotionWebUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	var param model.PromotionWebGetByIdRequest
	if err := c.ShouldBindUri(&param); err != nil {
		HandleError(c, err)
		return
	}

	req.Id = param.Id
	req.UpdatedByAdminId = adminId
	if err := h.promotionWebService.UpdatePromotionWeb(req); err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(201, model.Success{Message: "update successfully"})
}

// CancelPromotionWeb(id int64) error
// @Summary Delete PromotionWeb
// @Description Delete PromotionWeb
// @Tags Promotion Web - backoffice
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /promotion-web/cancel/{id} [put]
func (h *promotionWebController) cancelPromotionWeb(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))
	var param model.PromotionWebGetByIdRequest
	if err := c.ShouldBindUri(&param); err != nil {
		HandleError(c, err)
		return
	}

	var req model.CancelPromotionWebRequest
	req.Id = param.Id
	req.CanceledByAdminId = adminId
	req.CanceledAt = time.Now().UTC()
	if err := h.promotionWebService.CancelPromotionWeb(req, "CANCEL"); err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(201, model.Success{Message: "delete successfully"})
}

// @Summary Delete PromotionWeb (DeletePromotionWeb)
// @Description Delete PromotionWeb (DeletePromotionWeb)
// @Tags Promotion Web - backoffice
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /promotion-web/{id} [delete]
func (h *promotionWebController) deletePromotionWeb(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	var param model.PromotionWebGetByIdRequest
	if err := c.ShouldBindUri(&param); err != nil {
		HandleError(c, err)
		return
	}

	var req model.DeletePromotionWebRequest
	req.Id = param.Id
	req.DeletedByAdminId = adminId
	req.DeletedAt = time.Now().UTC()
	if err := h.promotionWebService.DeletePromotionWeb(req); err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(201, model.Success{Message: "delete successfully"})
}

// @Summary Get User PromotionWeb By UserId
// @Description Get User PromotionWeb By UserId
// @Tags Promotion Web User - backoffice
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param userId path int true "userId"
// @Success 200 {object} model.PromotionWebUserByUserIdResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /promotion-web/user/{userId} [get]
func (h *promotionWebController) getUserPromotionWebByUserId(c *gin.Context) {

	var param model.PromotionWebUserByUserIdRequest
	if err := c.ShouldBindUri(&param); err != nil {
		HandleError(c, err)
		return
	}

	promotionWebUser, err := h.promotionWebService.GetUserPromotionWebByUserId(param.UserId)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, promotionWebUser)
}

// @Summary Create User Collect PromotionWeb
// @Description Create User Collect PromotionWeb
// @Tags Promotion Web - user
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param req body model.PromotionWebUserCreateRequest true "body"
// @Success 200 {object} int64
// @Failure 400 {object} handler.ErrorResponse
// @Router /promotion-web/user [post]
func (h *promotionWebController) createUserCollectPromotionWeb(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var req model.PromotionWebUserCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	req.UserId = userId
	id, err := h.promotionWebService.CreateUserCollectPromotionWeb(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, id)
}

// @Summary Get User PromotionWeb List
// @Description Get User PromotionWeb List
// @Description | Query Parameter | Description       |
// @Description |------------------|----------------------------------------------|
// @Description | CANCELED    | สมาชิกที่รับโปรโมชันที่ยกเลิกแล้ว |
// @Description | OTHER    | สมาชิกที่รับโปรโมชันที่ยังไม่ได้ยกเลิก |
// @Tags Promotion Web User - backoffice
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.PromotionWebUserGetListRequest true "PromotionWebUserGetListRequest"
// @Success 200 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /promotion-web/user/list [get]
func (h *promotionWebController) getUserPromotionWebList(c *gin.Context) {

	var req model.PromotionWebUserGetListRequest
	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}
	data, err := h.promotionWebService.GetUserPromotionWebList(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary ShowPromotionWebForUser รับ หน้าเว็บ
// @Description ShowPromotionWebForUser
// @Description | Status Return | Description       |
// @Description |------------------|----------------------------------------------|
// @Description | NOT_AVAILABLE    | ไม่สามารถรับโปรโมชันได้ |
// @Description | AVAILABLE    | สามารถรับโปรโมชันได้ |
// @Description | ON_PROCESS    | กำลังดำเนินการรับโปรโมชัน |
// @Tags Promotion Web - user
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.ShowPromotionWebForUserResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /promotion-web/user/show [get]
func (h *promotionWebController) showPromotionWebForUser(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	data, err := h.promotionWebService.ShowPromotionWebForUser(userId)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary Cancel PromotionWeb User By Id
// @Description Cancel PromotionWeb User By Id
// @Tags Promotion Web User - backoffice
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /promotion-web/user/cancel/{id} [put]
func (h *promotionWebController) cancelPromotionWebUserById(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))
	var param model.PromotionWebUserGetByIdRequest
	if err := c.ShouldBindUri(&param); err != nil {
		HandleError(c, err)
		return
	}

	var req model.CancelPromotionWebUserById
	req.Id = param.Id
	req.CanceledByAdminId = adminId

	if err := h.promotionWebService.CancelPromotionWebUserById(req); err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(201, model.Success{Message: "cancel successfully"})
}

// @Summary ShowPromotionWebForUserById รับ หน้าเว็บ
// @Description ShowPromotionWebForUserById
// @Description | Status Return | Description       |
// @Description |------------------|----------------------------------------------|
// @Description | NOT_AVAILABLE    | ไม่สามารถรับโปรโมชันได้ |
// @Description | AVAILABLE    | สามารถรับโปรโมชันได้ |
// @Description | ON_PROCESS    | กำลังดำเนินการรับโปรโมชัน |
// @Tags Promotion Web - user
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 200 {object} model.ShowPromotionWebForUserResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /promotion-web/user/show/{id} [get]
func (h *promotionWebController) showPromotionWebForUserById(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var param model.ShowPromotionWebForUserById
	if err := c.ShouldBindUri(&param); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.promotionWebService.ShowPromotionWebForUserById(userId, param.Id)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary ShowPromotionWebForPublic
// @Description ShowPromotionWebForPublic
// @Tags Promotion Web - public
// @Accept json
// @Produce json
// @Success 200 {object} []model.ShowPromotionWebForUserResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /promotion-web/public/show [get]
func (h *promotionWebController) showPromotionWebForPublic(c *gin.Context) {

	data, err := h.promotionWebService.ShowPromotionWebForPublic()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary ShowPromotionWebForPublic
// @Description ShowPromotionWebForPublic
// @Tags Promotion Web - public
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 200 {object} []model.PromotionWebGetByIdResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /promotion-web/public/show/{id} [get]
func (h *promotionWebController) ShowPromotionWebById(c *gin.Context) {

	var req model.PromotionWebGetByIdRequest
	if err := c.ShouldBindUri(&req); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.promotionWebService.ShowPromotionWebById(req.Id)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary SingleWinLoseByMember
// @Description SingleWinLoseByMember
// @Tags Promotion Web - public
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.AgcSimpleWinloseRequest true "AgcSimpleWinloseRequest"
// @Success 200 {object} model.AgcSimpleWinloseResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /promotion-web/public/show/winlose [get]
func (h *promotionWebController) singleWinLoseByMember(c *gin.Context) {

	var req model.AgcSimpleWinloseRequest
	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.promotionWebService.SingleWinLoseByMember(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// CheckUserPromotion(body model.CheckUserPromotionBody) (string, error)
// @Summary CheckUserPromotion
// @Description CheckUserPromotion
// @Tags Promotion Web - public
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.CheckUserPromotionBody true "body"
// @Success 200 {object} string
// @Failure 400 {object} handler.ErrorResponse
// @Router /promotion-web/public/check-user-promotion [post]
func (h *promotionWebController) checkUserPromotion(c *gin.Context) {

	var req model.CheckUserPromotionBody
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.promotionWebService.CheckUserPromotion(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary AutoGenerateUserForTest
// @Description AutoGenerateUserForTest
// @Tags Promotion Web - public
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.UserFormCreate
// @Failure 400 {object} handler.ErrorResponse
// @Router /promotion-web/public/auto-generate-user-for-test [get]
func (h *promotionWebController) autoGenerateUserForTest(c *gin.Context) {

	data, err := h.promotionWebService.AutoGenerateUserForTest()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary CheckPromotionWithdraw
// @Description CheckPromotionWithdraw
// @Tags Promotion Web - public
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.CheckPromotionWithdrawRequest true "CheckPromotionWithdrawRequest"
// @Success 200 {object} string
// @Failure 400 {object} handler.ErrorResponse
// @Router /promotion-web/public/check-promotion-withdraw [get]
func (h *promotionWebController) checkPromotionWithdraw(c *gin.Context) {

	var req model.CheckPromotionWithdrawRequest
	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.promotionWebService.CheckPromotionWithdraw(req.UserId, req.CreditWithdraw)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary PromotionWebUserGetListByUserId
// @Description PromotionWebUserGetListByUserId
// @Tags Promotion Web - backoffice
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.PromotionWebUserGetListByUserIdRequest true "PromotionWebUserGetListByUserIdRequest"
// @Success 200 {object} model.PromotionWebUserGetListByUserIdResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /promotion-web/user/list-collected [get]
func (h *promotionWebController) promotionWebUserGetListByUserId(c *gin.Context) {

	var req model.PromotionWebUserGetListByUserIdRequest
	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}
	data, err := h.promotionWebService.PromotionWebUserGetListByUserId(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary GetPromotionWebUserStatus
// @Description GetPromotionWebUserStatus
// @Tags Promotion Web - backoffice
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.GetpromotionWebDateTypeResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /promotion-web/option/user-status [get]
func (h *promotionWebController) getPromotionWebUserStatus(c *gin.Context) {

	data, err := h.promotionWebService.GetPromotionWebUserStatus()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// GetPromotionWebUserWinLoseSummaryCurrentUser(promotionWebUserId int64) (*model.PromotionWebUserCurrentWinLostResponse, error)
// @Summary GetPromotionWebUserWinLoseSummaryCurrentUser
// @Description GetPromotionWebUserWinLoseSummaryCurrentUser
// @Tags Promotion Web - backoffice
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 200 {object} model.PromotionWebUserCurrentWinLostResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /promotion-web/user/turnover-win-lose-summary/{id} [get]
func (h *promotionWebController) getPromotionWebUserWinLoseSummaryCurrentUser(c *gin.Context) {

	var param model.PromotionWebUserGetByIdRequest
	if err := c.ShouldBindUri(&param); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.promotionWebService.GetPromotionWebUserWinLoseSummaryCurrentUser(param.Id)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary PromotionWebGetSildeListOnlyActive
// @Description PromotionWebGetSildeListOnlyActive
// @Tags Promotion Web - backoffice
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} []model.PromotionWebGetSildeListOnlyActive
// @Failure 400 {object} handler.ErrorResponse
// @Router /promotion-web/slide-list [get]
func (h *promotionWebController) getPromotionWebSlideListOnlyActive(c *gin.Context) {

	data, err := h.promotionWebService.PromotionWebGetSildeListOnlyActive()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary (uploadPromotionCoverToCloudFlare) อัพโหลดไฟล์ Cover ของ Promotion ขึ้น CloudFlare
// @Description (uploadPromotionCoverToCloudFlare) Upload File CloudFlare
// @Tags Promotion Web - backoffice
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param file formData file true "file"
// @Success 200 {object} model.FileUploadResponse
// @Failure 400 {object} ErrorResponse
// @Router /promotion-web/upload/cover [post]
func (h promotionWebController) uploadPromotionCoverToCloudFlare(c *gin.Context) {

	data, err := h.promotionWebService.UploadImageToS3PromotionCover(c.Request)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// CheckUserPromotionOnlyNewMemberFree(body model.CheckUserPromotionBody) (string, error)
// @Summary CheckUserPromotionOnlyNewMemberFree
// @Description CheckUserPromotionOnlyNewMemberFree
// @Tags Promotion Web - public
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body model.CheckUserPromotionBody true "body"
// @Success 200 {object} string
// @Failure 400 {object} handler.ErrorResponse
// @Router /promotion-web/public/new-member-free [post]
func (h *promotionWebController) checkUserPromotionOnlyNewMemberFree(c *gin.Context) {

	var req model.CheckUserPromotionBody
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.promotionWebService.CheckUserPromotionOnlyNewMemberFree(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary (GetUserPromotionReportList) ประวัติรายงานการรับโปรโมชั่นของยูส (รายบุคคล)
// @Description (GetUserPromotionReportList) ประวัติรายงานการรับโปรโมชั่นของยูส (รายบุคคล) เหมือนหน้า สมาชิกที่รับโปรโมชัน
// @Tags Promotion Web - ประวัติการรับโปรโมชั่น
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.GetUserPromotionReportListRequest true "GetUserPromotionReportListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /promotion-web/history-report [get]
func (h *promotionWebController) GetUserPromotionReportList(c *gin.Context) {

	var req model.GetUserPromotionReportListRequest
	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}

	pagination, err := h.promotionWebService.GetUserPromotionReportList(req)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, pagination)
}

// SortPromotionWebPriorityOrder(req model.DragSortRequest) error
// @Summary SortPromotionWebPriorityOrder
// @Description SortPromotionWebPriorityOrder
// @Tags Promotion Web - backoffice
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param req body model.DragSortRequest true "body"
// @Success 200 {object} string
// @Failure 400 {object} handler.ErrorResponse
// @Router /promotion-web/sort-priority-order [put]
func (h *promotionWebController) sortPromotionWebPriorityOrder(c *gin.Context) {

	var req model.DragSortRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	err := h.promotionWebService.SortPromotionWebPriorityOrder(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, "success")
}

// @Summary PromotionWebSummary
// @Description PromotionWebSummary
// @Tags Promotion Web - backoffice
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.PromotionWebGetListSummaryRequest true "PromotionWebGetListSummaryRequest"
// @Success 200 {object} model.PromotionWebGetListSummaryResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /promotion-web/summary [get]
func (h *promotionWebController) promotionWebSummary(c *gin.Context) {

	var req model.PromotionWebGetListSummaryRequest
	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.promotionWebService.PromotionWebSummary(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary PromotionWebUserSummary
// @Description PromotionWebUserSummary
// @Tags Promotion Web - backoffice
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.PromotionWebUserGetListSummaryRequest true "PromotionWebUserGetListSummaryRequest"
// @Success 200 {object} model.PromotionWebUserGetListSummaryResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /promotion-web/user/summary [get]
func (h *promotionWebController) promotionWebUserSummary(c *gin.Context) {

	var req model.PromotionWebUserGetListSummaryRequest
	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.promotionWebService.PromotionWebUserSummary(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary LockCreditPromotionUpdate
// @Description LockCreditPromotionUpdate
// @Tags Promotion Web - backoffice
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param req body model.LockCreditPromotionUpdateRequest true "body"
// @Success 200 {object} string
// @Failure 400 {object} handler.ErrorResponse
// @Router /promotion-web/unlock-user-credit [put]
func (h *promotionWebController) lockCreditPromotionUpdate(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))

	var req model.LockCreditPromotionUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, err)
		return
	}

	err := h.promotionWebService.LockCreditPromotionUpdate(adminId, req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, "success")
}

// @Summary CheckIsLockedCreditPromotionByUserId
// @Description CheckIsLockedCreditPromotionByUserId
// @Tags Promotion Web - backoffice
// @Accept json
// @Produce json
// @Param userId path int true "userId"
// @Success 200 {object} string
// @Failure 400 {object} handler.ErrorResponse
// @Router /promotion-web/check-locked-user-credit/{userId} [get]
func (h *promotionWebController) checkIsLockedCreditPromotionByUserId(c *gin.Context) {

	var param model.PromotionWebUserByUserIdRequest
	if err := c.ShouldBindUri(&param); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.promotionWebService.CheckIsLockedCreditPromotionByUserId(param.UserId)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary CheckIsLockedCreditPromotionByUserId
// @Description CheckIsLockedCreditPromotionByUserId
// @Tags Promotion Web - Lock credit
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.LockCreditPromotionUpdateResposnse
// @Failure 400 {object} handler.ErrorResponse
// @Router /promotion-web/user/locked-credit [get]
func (h *promotionWebController) checkIsLockedCreditPromotion(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	data, err := h.promotionWebService.CheckIsLockedCreditPromotion(userId)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary Get PromotionWebUserStatusOptions
// @Description Get PromotionWebUserStatusOptions
// @Tags Promotion Web - backoffice
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} []model.SelectOptions
// @Failure 400 {object} handler.ErrorResponse
// @Router /promotion-web/option/promotion-user-status [get]
func (h *promotionWebController) getPromotionWebUserStatusOptions(c *gin.Context) {

	data, err := h.promotionWebService.GetPromotionWebUserStatusOptions()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary GetLockCreditWithdrawList
// @Description GetLockCreditWithdrawList
// @Tags Lock Credit Withdraw - backoffice
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.GetLockCreditWithdrawListRequest true "GetLockCreditWithdrawListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /lock-credit/withdraw-list [get]
func (h *promotionWebController) GetLockCreditWithdrawList(c *gin.Context) {

	var req model.GetLockCreditWithdrawListRequest
	if err := c.ShouldBind(&req); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.promotionWebService.GetLockCreditWithdrawList(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary UnLockCreditWithdraw
// @Description UnLockCreditWithdraw
// @Tags Lock Credit Withdraw - backoffice
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "id"
// @Success 200 {object} string
// @Failure 400 {object} handler.ErrorResponse
// @Router /lock-credit/withdraw-unlock/{id} [put]
func (h *promotionWebController) UnLockCreditWithdraw(c *gin.Context) {

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))
	var param model.PromotionWebUserGetByIdRequest
	if err := c.ShouldBindUri(&param); err != nil {
		HandleError(c, err)
		return
	}

	var req model.UpdateLockCreditWithdrawRequest
	req.Id = param.Id
	req.ApprovedById = adminId

	err := h.promotionWebService.UnLockCreditWithdraw(req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, "success")
}

// @Summary CheckLockCreditWithdrawByUserId
// @Description CheckLockCreditWithdrawByUserId
// @Tags Lock Credit Withdraw - web
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} []model.CheckLockCreditWithdrawByUserId
// @Failure 400 {object} handler.ErrorResponse
// @Router /user/lock-credit/withdraw-check [get]
func (h *promotionWebController) CheckLockCreditWithdrawByUserId(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	data, err := h.promotionWebService.CheckLockCreditWithdrawByUserId(userId)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary MigrateBackUpLockCreditBack
// @Description MigrateBackUpLockCreditBack
// @Tags Lock Credit Withdraw - backoffice
// @Accept json
// @Produce json
// @Success 200 {object} model.MigrateBackUpLockCreditBackResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /backup/lock-credit/withdraw-migrate [post]
func (h *promotionWebController) MigrateBackUpLockCreditBack(c *gin.Context) {

	data, err := h.promotionWebService.MigrateBackUpLockCreditBack()
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// ShowPromotionWebForHiddenUrl(userId int64, hiddenUrlLink string) (*model.ShowPromotionWebForUserResponse, error)
// @Summary ShowPromotionWebForHiddenUrl
// @Description ShowPromotionWebForHiddenUrl
// @Tags Promotion Web - public
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param hiddenUrlLink path string true "hiddenUrlLink"
// @Success 200 {object} model.ShowPromotionWebForUserResponse
// @Failure 400 {object} handler.ErrorResponse
// @Router /promotion-web/user/link/{hiddenUrlLink} [get]
func (h *promotionWebController) showPromotionWebForHiddenUrl(c *gin.Context) {

	userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))

	var param model.ShowPromotionWebForHiddenUrlRequest
	if err := c.ShouldBindUri(&param); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.promotionWebService.ShowPromotionWebForHiddenUrl(userId, param.HiddenUrlLink)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}
