package handler

import (
	"cybergame-api/helper"
	"cybergame-api/middleware"
	"cybergame-api/model"
	"cybergame-api/service"
	"log"
	"strconv"

	"cybergame-api/repository"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type scammerController struct {
	scammerService service.ScammerService
}

func newScammerController(
	scammerService service.ScammerService,
) scammerController {
	return scammerController{scammerService}
}

func ScammerController(r *gin.RouterGroup, db *gorm.DB) {

	repo := repository.NewScammerRepository(db)
	service := service.NewScammerService(repo)
	handler := newScammerController(service)

	role := middleware.Role(db)
	singleSession := middleware.SingleSession(db)

	r = r.Group("/scammers", middleware.AuthorizeAdmin, singleSession.SingleAdminSession())
	r.GET("/list", handler.getScammerList, role.CheckPermission([]string{"member", "member_edit", "member_delete"}))
	r.GET("/summary", handler.getScammerSummary)
	r.POST("/create", handler.createScammer, role.CheckPermission([]string{"member_edit"}))
	r.DELETE("/delete/:id", handler.deleteScammer, role.CheckPermission([]string{"scammer_cancel"}))
}

// @Summary (getScammerList) Get Scammer List
// @Description (getScammerList) Get Scammer List
// @Tags Scammers
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.ScammerQuery true "Query Scammer"
// @Success 200 {object} model.SuccessWithList
// @Failure 400 {object} handler.ErrorResponse
// @Router /scammers/list [get]
func (h scammerController) getScammerList(c *gin.Context) {

	var query model.ScammerQuery
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.scammerService.GetScammerList(query)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(200, data)
}

// @Summary (getScammerSummary) Get Scammer List
// @Description (getScammerSummary) Get Scammer List
// @Tags Scammers
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} model.ScammerSummary
// @Failure 400 {object} handler.ErrorResponse
// @Router /scammers/summary [get]
func (h scammerController) getScammerSummary(c *gin.Context) {

	data, err := h.scammerService.GetScammerSummary()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (createScammer) Create Scammer
// @Description (createScammer) Create Scammer
// @Tags Scammers
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param Body body model.CreateScammer true "Create Scammer"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /scammers/create [post]
func (h scammerController) createScammer(c *gin.Context) {

	var body model.CreateScammer
	if err := c.ShouldBindJSON(&body); err != nil {
		HandleError(c, err)
		return
	}

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))
	// [ADMIN_LOG]
	if err := h.scammerService.LogAdmin("createScammer", adminId, body); err != nil {
		log.Println("createScammer.ERROR: ", err)
	}

	createdId, err := h.scammerService.CreateScammer(body, adminId)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, createdId)
}

// @Summary (deleteScammer) Delete Scammer
// @Description (deleteScammer) Delete Scammer
// @Tags Scammers
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "Scammer ID"
// @Success 201 {object} model.Success
// @Failure 400 {object} handler.ErrorResponse
// @Router /scammers/delete/{id} [delete]
func (h scammerController) deleteScammer(c *gin.Context) {

	id := c.Param("id")
	toInt64, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleError(c, err)
		return
	}

	adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))
	// [ADMIN_LOG]
	if err := h.scammerService.LogAdmin("deleteScammer", adminId, id); err != nil {
		log.Println("deleteScammer.ERROR: ", err)
	}

	if h.scammerService.DeleteScammer(toInt64, adminId) != nil {
		HandleError(c, err)
		return
	}
	c.JSON(201, model.Success{Message: "Deleted success"})
}
