package handler

import (
	"cybergame-api/middleware"
	"cybergame-api/model"
	"cybergame-api/service"

	"cybergame-api/repository"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"gorm.io/gorm"
)

type adminActionController struct {
	adminActionService service.AdminActionService
}

func newAdminActionController(
	adminActionService service.AdminActionService,
) adminActionController {
	return adminActionController{adminActionService}
}

func AdminActionController(r *gin.RouterGroup, db *gorm.DB) {

	repo := repository.NewAdminActionRepository(db)
	service := service.NewAdminActionService(repo)
	handler := newAdminActionController(service)

	role := middleware.Role(db)

	adminActionRoute := r.Group("/admin-action/logs", middleware.AuthorizeAdmin)
	adminActionRoute.GET("/list", role.CheckPermission([]string{"admin_manage_transaction_history"}), handler.getAdminActionLogList)

	optionRoute := r.Group("/admin-action/options")
	optionRoute.GET("/type", handler.getAdminActionTypeOptions)
}

// @Summary (getAdminActionTypeOptions) ข้อมูลตัวเลือกประเภทการกระทำแอดมิน
// @Description (getAdminActionTypeOptions) ข้อมูลตัวเลือกประเภทการกระทำแอดมิน
// @Tags Admins - Action Log
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} []model.SelectOptions
// @Failure 400 {object} handler.ErrorResponse
// @Router /admin-action/options/type [get]
func (h adminActionController) getAdminActionTypeOptions(c *gin.Context) {

	data, err := h.adminActionService.GetAdminActionTypeOptions()
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, data)
}

// @Summary (getAdminActionLogList) รายการประวัติการกระทำของแอดมิน
// @Description (getAdminActionLogList) รายการประวัติการกระทำของแอดมิน
// @Tags Admins - Action Log
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param _ query model.AdminActionLogListRequest true "AdminActionLogListRequest"
// @Success 200 {object} model.SuccessWithPagination
// @Failure 400 {object} handler.ErrorResponse
// @Router /admin-action/logs/list [get]
func (h adminActionController) getAdminActionLogList(c *gin.Context) {

	var query model.AdminActionLogListRequest
	if err := c.ShouldBind(&query); err != nil {
		HandleError(c, err)
		return
	}
	if err := validator.New().Struct(query); err != nil {
		HandleError(c, err)
		return
	}

	data, err := h.adminActionService.GetAdminActionLogList(query)
	if err != nil {
		HandleError(c, err)
		return
	}
	c.JSON(200, model.SuccessWithPagination{List: data.List, Total: data.Total})
}
