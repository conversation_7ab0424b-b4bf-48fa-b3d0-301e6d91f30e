# If you prefer the allow list template instead of the deny list, see community template:
# https://github.com/github/gitignore/blob/main/community/Golang/Go.AllowList.gitignore
#
# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/
build/
docs/
.vscode/

# Go workspace file
go.work

# Environment file
*.env
google_service_account.json

# Goolge file
google_service_account.json
domovie-cb4c3a0a3264.json

migrate.log
.idea