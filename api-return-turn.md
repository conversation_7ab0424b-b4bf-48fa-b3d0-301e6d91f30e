# API Promotion Return Documentation (คืนยอดเสีย)

## Stack Technology
- **Language**: Go
- **Framework**: Gin (Web Framework)
- **ORM**: GORM & SQLX
- **Database**: PostgreSQL
- **Validation**: go-playground/validator

## โครงสร้างโปรเจค

```
├── handler/
│   └── promotion_return.go            # API endpoints handlers
├── service/
│   └── promotion_return_service.go    # Business logic layer
├── repository/
│   └── promotion_return_repository.go  # Database access layer
├── model/
│   └── promotion_return_model.go      # Data models & DTOs
└── migration/
    └── *.sql                           # Database migrations
```

## Database Tables

### 1. promotion_return_setting
```sql
CREATE TABLE promotion_return_setting (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    return_percent DECIMAL(10,2) DEFAULT 0.00,
    return_type_id BIGINT DEFAULT 1,
    cut_type_id BIGINT DEFAULT 1,
    min_loss_price DECIMAL(10,2) DEFAULT 0.00,
    max_return_price DECIMAL(10,2) DEFAULT 0.00,
    detail TEXT,
    is_enabled BOOLEAN DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME ON UPDATE CURRENT_TIMESTAMP
);
```

### 2. promotion_return_loser (Transaction Log)
```sql
CREATE TABLE promotion_return_loser (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    status_id BIGINT DEFAULT 1,
    daily_key VARCHAR(255) UNIQUE,
    of_date DATE NOT NULL,
    total_loss_amount DECIMAL(10,2) DEFAULT 0.00,
    total_loss_live_casino DECIMAL(10,2) DEFAULT 0.00,
    total_loss_slot DECIMAL(10,2) DEFAULT 0.00,
    total_loss_sport DECIMAL(10,2) DEFAULT 0.00,
    return_percent DECIMAL(10,2) DEFAULT 0.00,
    game_detail VARCHAR(50),
    return_type_id BIGINT DEFAULT 1,
    cut_type_id BIGINT DEFAULT 1,
    min_loss_price DECIMAL(10,2) DEFAULT 0.00,
    max_return_price DECIMAL(10,2) DEFAULT 0.00,
    return_price DECIMAL(10,2) DEFAULT 0.00,
    calc_at DATETIME,
    take_at DATETIME,
    taken_price DECIMAL(10,2) DEFAULT 0.00,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME ON UPDATE CURRENT_TIMESTAMP,
    KEY idx_status_id (status_id),
    KEY idx_user_id (user_id),
    UNIQUE KEY idx_daily_key (daily_key)
);
```

### 3. calculate_play_type
```sql
CREATE TABLE calculate_play_type (
    id BIGINT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Initial data
INSERT INTO calculate_play_type (id, name) VALUES
    (1, 'Live-Casino'),
    (2, 'Slot'),
    (3, 'Sport');
```

### 4. promotion_return_calculate_type (Junction Table)
```sql
CREATE TABLE promotion_return_calculate_type (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    promotion_setting_id BIGINT NOT NULL,
    calculate_type_id BIGINT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (promotion_setting_id) REFERENCES promotion_return_setting(id) ON DELETE CASCADE,
    FOREIGN KEY (calculate_type_id) REFERENCES calculate_play_type(id),
    UNIQUE KEY unique_promotion_calculate (promotion_setting_id, calculate_type_id)
);
```

### 5. Reference Tables
```sql
-- Status Types
CREATE TABLE promotion_return_loser_status (
    id BIGINT PRIMARY KEY,
    name VARCHAR(255)
);
-- Values: 1=PENDING, 2=READY, 3=TAKEN, 4=EXPIRED

-- Cut Types
CREATE TABLE promotion_return_cut_type (
    id BIGINT PRIMARY KEY,
    name VARCHAR(255)
);
-- Values: 1=รายวัน (Daily), 2=รายสัปดาห์ (Weekly)

-- Return Types
CREATE TABLE promotion_return_loser_type (
    id BIGINT PRIMARY KEY,
    name VARCHAR(255)
);
-- Values: 1=คืนยอดเสียเมื่อยอดเสียเกิน
```

## API Endpoints

### Admin APIs

#### 1. Customer Promotion Management
```go
// GET /customer-promotion/list
// ดึงรายการผู้เล่นที่ติดเทิร์น
Request: {
    fromDate?: string    // วันที่เริ่ม
    toDate?: string      // วันที่สิ้นสุด
    promotionName?: string // ชื่อโปรโมชั่น
    statusId?: int64     // สถานะ (1-4)
    search?: string      // ค้นหา username/fullname
    page: int           // หน้า (default: 1)
    limit: int          // จำนวนต่อหน้า (default: 10, max: 100)
    sortCol?: string    // column สำหรับเรียงลำดับ
    sortAsc?: string    // ASC/DESC
}

Response: {
    list: [{
        id: int64
        userId: int64
        username: string
        fullname: string
        typeId: int64
        typeName: string
        statusId: int64
        statusName: string
        startTurnAmount: float64
        totalTurnAmount: float64
        promotionName: string
        bonusAmount: float64
        startTurnAt: time
        endTurnAt?: time
        createdAt: time
        updatedAt?: time
    }],
    total: int64
}

// POST /customer-promotion/cancel
// ยกเลิกการติดเทิร์น
Request: {
    statementId: int64   // ID รายการที่จะยกเลิก
}
Response: {
    message: "Updated success"
}
```

#### 2. Promotion Return Setting
```go
// GET /promotion-return/setting
// ดึงการตั้งค่าโปรโมชั่นคืนยอดเสีย
Response: {
    id: int64
    returnPercent: float64      // เปอร์เซ็นต์คืน
    returnTypeId: int64          // ประเภทการคืน
    cutTypeId: int64             // รอบการตัด (1=รายวัน, 2=รายสัปดาห์)
    minLossPrice: float64        // ยอดเสียขั้นต่ำ
    maxReturnPrice: float64      // ยอดคืนสูงสุด
    detail: string              // รายละเอียด HTML
    isEnable: bool              // เปิด/ปิด
    calculateTypes: []int64     // ประเภทเกมที่คำนวณ [1,2,3]
    createdAt: time
    updatedAt?: time
    cacheExpiredAt: time
}

// PATCH /promotion-return/setting
// อัพเดทการตั้งค่า
Request: {
    returnPercent?: float64
    returnTypeId?: int64
    cutTypeId?: int64
    minLossPrice?: float64
    maxReturnPrice?: float64
    detail?: string
    isEnable?: bool
    calculateTypes?: []int64     // ประเภทเกมที่จะคำนวณ [1,2,3]
}
```

#### 3. Return History Report
```go
// GET /promotion-return/history/user-list
// รายชื่อผู้เล่นที่มีประวัติคืนยอดเสีย
Request: {
    dateType?: string    // all, today, yesterday, last_week, this_month, last_month
    fromDate?: string
    toDate?: string
    search?: string      // ค้นหา member_code/username/fullname
    page: int
    limit: int
    sortCol?: string
    sortAsc?: string
}

Response: {
    list: [{
        id: int64
        memberCode: string
        username: string
        fullname: string
        totalLossAmount: float64    // ยอดเสียรวม
        totalTakenPrice: float64    // ยอดที่รับไปแล้ว
    }],
    total: int64
}

// GET /promotion-return/history/user-summary
// สรุปยอดรวมการคืนยอดเสีย
Request: (same as user-list)
Response: {
    dateType: string
    fromDate: string
    toDate: string
    totalLossAmount: float64
    totalTakenPrice: float64
}

// GET /promotion-return/history/log-list
// ประวัติการคืนยอดเสียรายคน
Request: {
    userId?: int64
    fromDate?: string
    toDate?: string
    statusId?: int64
    page: int
    limit: int
}

Response: {
    list: [{
        id: int64
        userId: int64
        ofDate: string
        totalLossAmount: float64
        totalLossLiveCasino: float64
        totalLossSlot: float64
        totalLossSport: float64
        cutTypeName: string
        returnPercent: float64
        gameDetail: string
        returnPrice: float64
        creditExpireAt: string
        logStatus: string    // READY, TAKEN, EXPIRED
    }],
    total: int64
}
```

### Web/User APIs

#### 1. Open Promotion List
```go
// GET /web/promotions/open-list/promotion-list
// รายการโปรโมชั่นที่เปิดให้เล่น
Response: [{
    key: string         // RETURN_LOSS, COUPON_CASH, CHECK_IN, etc.
    name: string        // ชื่อโปรโมชั่น
    description: string
    imageUrl: string
}]
```

#### 2. User Return Loss
```go
// GET /web/promotion-return-loser/current
// ยอดคืนเงินปัจจุบันของผู้ใช้
Response: {
    userId: int64
    statusId: int64
    statusName: string
    returnPercent: float64
    returnPrice: float64
    detail: string
    relatedItemList: [{
        id: int64
        userId: int64
        dailyKey: string
        ofDate: string
        totalLossAmount: float64
        totalLossLiveCasino: float64
        totalLossSlot: float64
        totalLossSport: float64
        statusId: int64
        statusName: string
        returnPercent: float64
        gameDetail: string
        returnTypeId: int64
        cutTypeId: int64
        minLossPrice: float64
        maxReturnPrice: float64
        returnPrice: float64
        calcAt?: time
        takeAt?: time
        takenPrice: float64
        createdAt: time
        updatedAt?: time
    }]
}

// POST /web/promotion-return-loser/take
// รับเงินคืนเข้าบัญชี
Response: {
    message: "Updated success"
}

// GET /web/promotion-return-loser/list
// ประวัติการรับเงินคืน
Request: {
    statusId?: int64
    page: int
    limit: int
}

Response: {
    list: [PromotionReturnTransaction],
    total: int64
}
```

### Cron Jobs

```go
// GET /cron/promotion-return-loser/cut-daily
// ตัดยอดประจำวัน (ทำงานอัตโนมัติ)

// GET /cron/promotion-return-loser/cut-date (Admin Only)
// ตัดยอดของวันที่กำหนด
Request: {
    ofDate: string  // วันที่ format: YYYY-MM-DD
}
```

## Models & Data Structures

### Constants
```go
const (
    // Return Type
    PROMOTION_RETURN_TYPE_LOSER = 1
    
    // Cut Type
    PROMOTION_RETURN_CUT_TYPE_DAILY  = 1
    PROMOTION_RETURN_CUT_TYPE_WEEKLY = 2
    
    // Status
    PROMOTION_RETURN_STATUS_PENDING = 1  // รอคำนวณ
    PROMOTION_RETURN_STATUS_READY   = 2  // พร้อมรับ
    PROMOTION_RETURN_STATUS_TAKEN   = 3  // รับแล้ว
    PROMOTION_RETURN_STATUS_EXPIRED = 4  // หมดอายุ
)
```

### Main Models
```go
type PromotionReturnSetting struct {
    Id               int64
    ReturnPercent    float64
    ReturnTypeId     int64
    CutTypeId        int64
    MinLossPrice     float64
    MaxReturnPrice   float64
    Detail           string
    IsEnabled        bool
    CreatedAt        time.Time
    UpdatedAt        *time.Time
    CalculateTypes   []CalculatePlayType  // ประเภทเกมที่คำนวณ
}

type CalculatePlayType struct {
    Id        int64
    Name      string
    CreatedAt time.Time
}

type PromotionReturnTransaction struct {
    Id                 int64
    UserId             int64
    DailyKey           string    // Unique key: userId_date
    OfDate             string    // วันที่คำนวณ
    TotalLossAmount    float64   // ยอดเสียรวม
    TotalLossSport     float64
    TotalLossCasino    float64
    TotalLossGame      float64
    TotalLossLottery   float64
    TotalLossP2p       float64
    TotalLossFinancial float64
    StatusId           int64
    StatusName         string
    ReturnPercent      float64
    GameDetail         string
    ReturnTypeId       int64
    CutTypeId          int64
    MinLossPrice       float64
    MaxReturnPrice     float64
    CreditExpireDays   int
    ReturnPrice        float64   // ยอดที่จะคืน
    CalcAt             *time.Time // เวลาคำนวณ
    TakeAt             *time.Time // เวลารับเงิน
    TakenPrice         float64    // ยอดที่รับไปแล้ว
    CreatedAt          time.Time
    UpdatedAt          *time.Time
}

```

## Service Layer Pattern

### Interface Definition
```go
type PromotionReturnService interface {
    // Settings
    GetReturnSetting() (*PromotionReturnSettingResponse, error)
    UpdateReturnSetting(req PromotionReturnSettingUpdateRequest) error
    
    // User Operations
    GetUserCurrentReturnDetail(userId int64) (*PromotionReturnUserDetail, error)
    TakeUserReturnAmount(userId int64) error
    GetUserReturnHistoryList(req PromotionReturnTransactionListRequest) (*SuccessWithPagination, error)
    
    // Reports
    GetReturnHistoryUserList(req PromotionReturnHistoryUserListRequest) (*SuccessWithPagination, error)
    GetReturnHistoryUserSummary(req PromotionReturnHistoryUserListRequest) (*PromotionReturnHistoryUserSummaryResponse, error)
    GetReturnHistoryLogList(req PromotionReturnHistoryListRequest) (*SuccessWithPagination, error)
    
    // Cron Jobs
    CronCutReturnLossDaily() error
    CronCutReturnLossByDate(ofDate string) error
}
```

### Implementation Pattern
```go
type promotionReturnService struct {
    repo                      repository.PromotionReturnRepository
    shareDb                   *gorm.DB
    activityLuckyWheelService ActivityLuckyWheelService
    serviceNoti               NotificationService
}

// Example: Update setting with calculate types
func (s *promotionReturnService) UpdateReturnSetting(req UpdateRequest) error {
    // Update main settings
    if err := s.repo.UpdateReturnSetting(id, req); err != nil {
        return err
    }
    
    // Update calculate types (many-to-many)
    if req.CalculateTypes != nil {
        if err := s.repo.UpdatePromotionCalculateTypes(id, req.CalculateTypes); err != nil {
            return err
        }
    }
    return nil
}

func NewPromotionReturnService(
    repo repository.PromotionReturnRepository,
    shareDb *gorm.DB,
    activityLuckyWheelService ActivityLuckyWheelService,
    serviceNoti NotificationService,
) PromotionReturnService {
    return &promotionReturnService{repo, shareDb, activityLuckyWheelService, serviceNoti}
}
```

## Repository Layer Pattern

### Interface Definition
```go
type PromotionReturnRepository interface {
    GetDb() *gorm.DB
    
    // Settings
    GetReturnSetting() (*PromotionReturnSettingResponse, error)
    CreateReturnSetting(body PromotionReturnSettingCreateBody) (*int64, error)
    UpdateReturnSetting(id int64, body PromotionReturnSettingUpdateBody) error
    
    // Calculate Types
    GetCalculatePlayTypes() ([]CalculatePlayType, error)
    GetPromotionCalculateTypes(promotionId int64) ([]int64, error)
    UpdatePromotionCalculateTypes(promotionId int64, typeIds []int64) error
    
    // Transactions
    GetCurrentReturnTransactionList(userId int64) ([]PromotionReturnTransaction, error)
    GetReturnTransactionList(req PromotionReturnTransactionListRequest) ([]PromotionReturnTransaction, int64, error)
    CreateReturnTransaction(body PromotionReturnTransactionCreateBody) (*int64, error)
    UpdateCalcReturnTransaction(id int64, body PromotionReturnTransactionCalcBody) error
    UpdateTakeReturnTransaction(id int64, body PromotionReturnTransactionUpdateBody) error
    UpdateExpriedReturnTransaction(id int64, body PromotionReturnTransactionUpdateBody) error
    
    // User Credit Operations
    IncreaseUserCredit(body UserTransactionCreateRequest) (*UserTransactionCreateResponse, error)
    DecreaseUserCredit(body UserTransactionCreateRequest) (*UserTransactionCreateResponse, error)
    
    // Play Log
    GetDailyTotalUserPaylogList(statementDate string) ([]PlaylogTotalAmount, error)
    GetWeeklyTotalUserPaylogList(statementDate string) ([]PlaylogTotalAmount, error)
    CheckDailyPlayLog(statementDate string) (*CronPlayLogCheckReponse, error)
}
```

## Controller/Handler Pattern

### Gin Router Setup
```go
func PromotionReturnController(r *gin.RouterGroup, db *gorm.DB) {
    // Initialize repositories
    promotionReturnRepo := repository.NewPromotionReturnRepository(db)
    notificationRepo := repository.NewNotificationRepository(db)
    activityLuckyWheelRepo := repository.NewActivityLuckyWheelRepository(db)
    
    // Initialize services
    notificationService := service.NewNotificationService(notificationRepo)
    activityLuckyWheelService := service.NewActivityLuckyWheelService(activityLuckyWheelRepo, db, notificationService)
    promotionReturnService := service.NewPromotionReturnService(promotionReturnRepo, db, activityLuckyWheelService, notificationService)
    
    // Initialize controller
    handler := newPromotionReturnController(promotionReturnService)
    
    // Middleware
    role := middleware.Role(db)
    singleSession := middleware.SingleSession(db)
    
    // Setup routes
    setupAdminRoutes(r, handler, role, singleSession)
    setupWebRoutes(r, handler, singleSession)
    setupCronRoutes(r, handler, singleSession)
}
```

### Request Handler Pattern
```go
func (h promotionReturnController) getUserCurrentReturnDetail(c *gin.Context) {
    // Get user ID from context (set by auth middleware)
    userId := helper.ConvertIdAnyToInt64(c.MustGet("userId"))
    
    // Call service
    data, err := h.promotionReturnService.GetUserCurrentReturnDetail(userId)
    if err != nil {
        HandleError(c, err)
        return
    }
    
    // Return response
    c.JSON(200, data)
}

func (h promotionReturnController) updateReturnSetting(c *gin.Context) {
    // Parse request body
    var body model.PromotionReturnSettingUpdateRequest
    if err := c.ShouldBindJSON(&body); err != nil {
        HandleError(c, err)
        return
    }
    
    // Validate
    if err := validator.New().Struct(body); err != nil {
        HandleError(c, err)
        return
    }
    
    // Get admin ID from context
    adminId := helper.ConvertIdAnyToInt64(c.MustGet("adminId"))
    body.UpdatedById = adminId
    
    // Call service
    if err := h.promotionReturnService.UpdateReturnSetting(body); err != nil {
        HandleError(c, err)
        return
    }
    
    // Return success
    c.JSON(201, model.Success{Message: "Updated success"})
}
```

## Middleware

### Authentication
```go
// Admin authentication
middleware.AuthorizeAdmin

// User authentication  
middleware.AuthorizeUser

// Single session check
singleSession.SingleAdminSession()
singleSession.SingleUserSession()

// Role-based permissions
role.CheckPermission([]string{"activity_manage"})
```

## Business Logic Flow

### 1. Daily Return Calculation
```
1. Cron job triggers at scheduled time
2. Check play_log table for daily transactions
3. Calculate total loss amount per user for selected play types only
4. Check against minimum threshold (minLossPrice)
5. Calculate return amount (percentage * loss, max: maxReturnPrice)
6. Create transaction record with status READY
7. Send notification to user
```

### 2. User Claims Return
```
1. User requests current return detail
2. System checks for READY status transactions
3. User clicks "Take" to claim
4. System updates user credit balance
5. Update transaction status to TAKEN
6. Record take_at timestamp
7. Create turnover statement if required
```

### 3. Auto Expiration
```
1. Check transactions past creditExpireDays
2. Update status from READY to EXPIRED
3. User can no longer claim expired returns
```

## Error Handling

```go
// Standard error responses
HandleError(c *gin.Context, err error)

// Error types
- 400 Bad Request: Invalid input/validation failed
- 401 Unauthorized: Authentication required
- 403 Forbidden: Insufficient permissions
- 404 Not Found: Resource not found
- 500 Internal Server Error: System error

// Custom error messages
- STATEMENT_NOT_FOUND
- STATEMENT_NOT_PENDING
- CUT_TYPE_NOT_FOUND
- INSUFFICIENT_BALANCE
```

## Cache Management

```go
// Settings cache with 60-minute expiration
var promotionReturnSetting *model.PromotionReturnSettingResponse

// Cache check and refresh
if promotionReturnSetting != nil && time.Now().Before(promotionReturnSetting.CacheExpiredAt) {
    return promotionReturnSetting, nil
}

// Clear cache on update
promotionReturnSetting = nil
```

## Transaction Management

```go
// Use database transactions for atomic operations
tx := repo.GetDb().Begin()

// Perform operations
if err := tx.Table("promotion_return_setting").Updates(body).Error; err != nil {
    tx.Rollback()
    return err
}

// Commit on success
tx.Commit()
```

## Pagination Pattern

```go
type PaginationRequest struct {
    Page    int    `form:"page" default:"1" min:"1"`
    Limit   int    `form:"limit" default:"10" min:"1" max:"100"`
    SortCol string `form:"sortCol"`
    SortAsc string `form:"sortAsc"` // ASC or DESC
}

type SuccessWithPagination struct {
    List  interface{} `json:"list"`
    Total int64       `json:"total"`
}

// Query implementation
query = query.Limit(req.Limit).Offset(req.Page * req.Limit)
```

## Security Considerations

1. **Authentication**: All endpoints require JWT token authentication
2. **Authorization**: Role-based access control for admin operations
3. **Session Management**: Single session enforcement
4. **Input Validation**: Using go-playground/validator for all inputs
5. **SQL Injection Prevention**: Using parameterized queries via GORM
6. **Audit Trail**: Admin actions are logged with admin_id
7. **Rate Limiting**: Consider implementing rate limiting for API endpoints

## Testing Strategy

```go
// Unit Tests
- Test service layer business logic
- Test repository database operations
- Mock external dependencies

// Integration Tests  
- Test full API endpoints
- Test with real database (test environment)
- Test transaction rollback scenarios

// End-to-End Tests
- Test complete user flows
- Test cron job execution
- Test concurrent operations
```

## Deployment Considerations

1. **Environment Variables**
   - Database connection string
   - JWT secret key
   - Cron job schedules
   - Cache TTL settings

2. **Database Migrations**
   - Run migrations in order
   - Backup before major changes
   - Test rollback procedures

3. **Monitoring**
   - API response times
   - Database query performance
   - Error rates and types
   - Cron job execution status

4. **Scaling**
   - Database connection pooling
   - Cache layer (Redis)
   - Load balancing for API servers
   - Background job queue for heavy operations

## Implementation Notes for New Project

1. **Dependencies**
```bash
go get github.com/gin-gonic/gin
go get gorm.io/gorm
go get gorm.io/driver/postgres
go get github.com/jmoiron/sqlx
go get github.com/lib/pq
go get github.com/go-playground/validator/v10
```

2. **Project Structure**
   - Follow clean architecture principles
   - Separate concerns (handler/service/repository)
   - Use interfaces for dependency injection
   - Implement proper error handling

3. **Database Design**
   - Use proper indexes for performance
   - Consider partitioning for large tables
   - Implement soft deletes where appropriate
   - Use transactions for data consistency

4. **API Design**
   - Follow RESTful conventions
   - Use proper HTTP status codes
   - Implement request/response logging
   - Add API versioning support

5. **Performance Optimization**
   - Implement caching strategy
   - Use database connection pooling
   - Optimize queries with proper indexes
   - Consider pagination for large datasets